<template>

    <vx-card title="Cierre Siniestralidad">

        <div class="container">
            <div class="a-mqN00-0 ">

                <div class="w-full md:w-full lg:w-10/12 xl:w-6/12  flex m-4">
                    <h2>                        
                        Empresa                    
                    </h2>
                </div>
                <div class="w-full md:w-full lg:w-10/12 xl:w-6/12  flex items-center justify-center m-4">  
                    <h1>
                        <strong>
                            <font size="6" color="#008fbe" face="Arial">{{this.sesion.sesion_empresa }}</font>
                            
                        </strong>
                    </h1>
                    
                </div>

            </div>
            <div class="a-Vvn2X">

                <div class="w-full md:w-full lg:w-10/12 xl:w-6/12  flex items-center justify-center m-4">
                    <label>Periodo Ejecución:</label>

                    <datepicker
                    :language="languages['es']"
                    placeholder="Fecha Inicial"
                    :minimumView="'month'"
                    :maximumView="'year'"
                    :initialView="'year'"
                    :full-month-name="false"
                    :format="dateFormat"                    
                    :bootstrapStyling="true"
                    v-model="Periodo"></datepicker>
                </div>

                <div class="w-full md:w-full lg:w-10/12 xl:w-6/12  flex items-center justify-center m-4">
                    <label>Fecha Inicial:</label>          
                    <div class=" flex m-4">                              
                        <vs-input v-model="FechaInicial"  class="w-full" readonly ></vs-input>
                    </div>
                    <label>Fecha Final:</label>       
                    <div class=" flex  m-4">                                 
                        <vs-input v-model="FechaFinal"   class="w-full" readonly ></vs-input>
                    </div>
                </div>

            </div>
            <div class="a-yw200 p-4">

                <vs-row>
                    <vs-col vs-w="2">
                        <SM-Buscar v-model="CodigoAseguradora"
                                    label="Aseguradora"
                                    api="app/Ajenos/ConsultaCodigosAseguradoras"
                                    api_campo_respuesta_mostrar="Codigo"
                                    :api_campos="['Codigo', 'Aseguradora' ]"
                                    api_campo_respuesta="Codigo"
                                    :api_titulos="['Codigo', 'Aseguradora']"
                                    :api_preload="true"
                                    :api_campo_respuesta_estricto="false"
                                    :disabled_texto="true"
                                    :callback_buscar="CosultarAseguradoras" 
                            />
                    </vs-col>
                    <vs-col vs-w="6" class="pl-4">

                        <vs-input label="Nombre Aseguradora" class="w-full" v-model="NombreAseguradora" readonly />

                    </vs-col>
                </vs-row>


            </div>
            <div class="a-r2VJJ">

                <vs-button color="success" class="w-full md:w-full lg:w-4/12 xl:w-1/12 m-2" 
                        @click="EjecutarCierre">
                                Ejecutar
                            </vs-button>


            </div>
        </div>

    </vx-card>
</template>



<script>

import Datepicker from 'vuejs-datepicker';
import * as lang from 'vuejs-datepicker/src/locale';




export default {
    props:{
        clientWidth:Number
    },
    components: {
        Datepicker
    },
    data() {
        return {
            languages: lang,
            Periodo: '',
            FechaInicial:'',
            FechaFinal: '',
            monthMenu: false,
            txtMonth: '',
            month: '',
            CodigoAseguradora:'',
            NombreAseguradora:'',           
            dateFormat : 'MMMM - yyyy'  
            
        }

    },
    computed: {
        sesion() {
            return this.$store.state.sesion
        }
    },
    mounted(){
        
        this.Periodo = (new Date(Date.now()))

        const firstDayCurrentMonth = this.getFirstDayOfMonth(
            this.Periodo.getFullYear(),
            this.Periodo.getMonth(),
        );

        // const date2 = new Date();
        const lastDayCurrentMonth = this.getLastDayOfMonth(
            this.Periodo.getFullYear(),
            this.Periodo.getMonth(),
        );

        this.FechaInicial = this.formatDate(firstDayCurrentMonth)
        this.FechaFinal   = this.formatDate(lastDayCurrentMonth)
    },
    watch: {
        'Periodo': function (value) {

            
            const firstDayCurrentMonth = this.getFirstDayOfMonth(
                value.getFullYear(),
                value.getMonth(),
            );

            const lastDayCurrentMonth = this.getLastDayOfMonth(
                value.getFullYear(),
                value.getMonth(),
            );

            this.FechaInicial = this.formatDate(firstDayCurrentMonth)
            this.FechaFinal   = this.formatDate(lastDayCurrentMonth)
           

            
        }
    },
    methods: {    
        CosultarAseguradoras(datos) {
            this.NombreAseguradora = datos.Aseguradora

        },
        async EjecutarCierre(){

            if (this.CodigoAseguradora === null || this.CodigoAseguradora === undefined) {

                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Alerta',
                    text: 'Codigo Aseguradora es requerido',
                })
                return
            }

            if (this.FechaInicial === null || this.FechaInicial  === undefined) {

                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Alerta',
                    text: 'Fecha inicial es requerida',
                })
                return
            }  

            if (this.FechaFinal === null || this.FechaFinal  === undefined) {

                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Alerta',
                    text: 'Fecha Final es requerida',
                })
                return
            }
      
              let postData = {
                FechaInicio:this.FechaInicial,
                FechaFin:this.FechaFinal,
                CodigoAseguradora:this.CodigoAseguradora
            
             }
                    
            
            await this.axios.post('/app/v1_Siniestralidad/EjecutaCierreSiniestralidad', {
                Opciones: {
                    ...postData
                    },
            })
            .then((resp) => {
                    let Codigo 
                    let mensajeCodigo


                    if(resp.data.json && resp.data.json.length != 0){
                        Codigo = resp.data.json[0].Codigo
                        mensajeCodigo = resp.data.json[0].Descripcion
                    }else{
                        Codigo = resp.data.Codigo
                        mensajeCodigo = resp.data.Descripcion
                    }
                    if(Codigo && Codigo != 0){                       
                        this.$vs.notify({
                            time: 4000,
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: mensajeCodigo
                        })  
                    }
                    else{          
                        this.$vs.dialog({
                            type: 'confirm',
                            color: '#ED8C72',
                            acceptText: 'Continuar',
                            title: 'Finalizado Exitosamente',
                            text:  mensajeCodigo
                            
                        })                        
                    }
                })  


        },
        getFirstDayOfMonth: function(year, month)  {
          return new Date(year, month, 1);
        },
        getLastDayOfMonth: function (year, month) {
            return new Date(year, month + 1, 0);
        },
        formatDate: function(date) {
            const year = date.toLocaleString('default', {year: 'numeric'});
            const month = date.toLocaleString('default', {month: '2-digit',});
            const day = date.toLocaleString('default', {day: '2-digit'});

            return [year, month, day].join('-');
        }
        

    }

}


</script>


<style scoped>
.container {
    display: grid;
    grid-gap: 5px;
    width: 100%;
    height: 100%;
    grid-template-areas: "a-mqN00-0 a-Vvn2X a-Vvn2X a-Vvn2X"
        "a-yw200 a-yw200 a-yw200 a-yw200"
        "a-r2VJJ a-r2VJJ a-r2VJJ a-r2VJJ";
    grid-template-columns: 0.5fr 0.5fr 0.5fr 0.5fr;
    grid-template-rows: 0.34fr 0.33fr 0.33fr;
}

.container>div {
    border: 1px solid #888;
}

.a-mqN00-0 {
    grid-area: a-mqN00-0;
}

.a-Vvn2X {
    grid-area: a-Vvn2X;
}

.a-yw200 {
    grid-area: a-yw200;
}

.a-r2VJJ {
    grid-area: a-r2VJJ;
}

.container {
    max-width: 100%;
}

.clearfix {
    overflow: auto;
    display: flow-root;
}
</style>