<template>
    <div>
        <buscador ref="BuscarAjenoDel" buscador_titulo="Buscador / Ajenos" :api="'app/v1_contabilidad_hospital/BusquedaAjeno'" 
            :campos="['Apellido', 'Nombre', 'Especialidad', 'Codigo']"
            :titulos="['#Apellido', '#Nombre', '#Especialidad', 'Codigo']"
            :api_filtro="{Codigo:DelAjeno}"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" :api_preload="true" />
        
            <buscador ref="BuscarAjenoAl" buscador_titulo="Buscador / Ajenos" :api="'app/v1_contabilidad_hospital/BusquedaAjeno'" 
            :campos="['Apellido', 'Nombre', 'Especialidad', 'Codigo']"
            :titulos="['#Apellido', '#Nombre', '#Especialidad', 'Codigo']"
            :api_filtro="{Codigo:AlAjeno}"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" :api_preload="true" />


        <vx-card title="Facturación de Manejo de Cuenta Ajena">
            <vs-row class="w-full mb-4" vs-justify="center">
                <ConfiguracionCaja
                    @CargarCaja="CargarCaja"
                    :FacturaCambiaria=1
                    TipoDocumento = "F"
                    TipoCajaSeleccionada = "TipoManejoCuenta"
                    CajaSeleccionada = "ManejoCuenta"
                    AgrupacionCaja="CajaManejoCuenta"
                    class="w-full">
                </ConfiguracionCaja>
            </vs-row>

            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="1">
                    <label class="typo__label">Fecha Planilla:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="3">
                    <vs-input type="date" v-model="FechaPlanilla"></vs-input>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="1">
                    <label class="typo__label">Fecha Factura:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                    <vs-input type="date" v-model="FechaFactura"></vs-input>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="1">
                    <label class="typo__label">Del Ajeno:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-input v-model="DelAjeno" @keyup.enter="BusquedaAjeno(0)" @keydown.tab="BusquedaAjeno(0)" @change="BusquedaAjeno(0)"></vs-input>
                    <vs-button color="primary" icon-pack="fa" icon="fa-search" @click="BusquedaAjeno(0)"></vs-button>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="5">
                    &nbsp;{{ AjenoSeleccionadoDel.Titulo }} {{ AjenoSeleccionadoDel.Nombre }} {{ AjenoSeleccionadoDel.Apellido }}
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="1">
                    <label class="typo__label">Al:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-input v-model="AlAjeno" @keyup.enter="BusquedaAjeno()" @keydown.tab="BusquedaAjeno()" @change="BusquedaAjeno()"></vs-input>
                    <vs-button color="primary" icon-pack="fa" icon="fa-search" @click="BusquedaAjeno()"></vs-button>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="5">
                    &nbsp;{{ AjenoSeleccionadoAl.Titulo }} {{ AjenoSeleccionadoAl.Nombre }} {{ AjenoSeleccionadoAl.Apellido }}
                </vs-col>
            </vs-row>
            <br>
            <vs-button color="primary" icon-pack="feather" icon="icon-printer" @click="BusquedaAjenoPorPagar()">Generar</vs-button>
        </vx-card>
    </div>
</template>
<script>
    import ConfiguracionCaja from '/src/components/sermesa/modules/caja/ConfiguracionCaja.vue'
    export default {
        components: {
            ConfiguracionCaja
        },
        data(){
            return{
                FechaPlanilla: '',
                FechaFactura: '',
                DelAjeno: '',
                AlAjeno: '',
                AjenoSeleccionadoDel: {
                    Empresa: '',
                    Codigo: '',
                    Nombre: '',
                    Apellido: '',
                    Titulo: '',
                    Especialidad: '',
                    Tipo: '',
                    PagaHonorariosXInformes: '',
                    Direccion: '',
                    Telefono: '',
                    ClinicaCalleAvenida: '',
                    ClinicaTel: '',
                    HonorariosPacienteCOEX: '',
                    CorreoElectronico: ''
                },
                AjenoSeleccionadoAl: {
                    Empresa: '',
                    Codigo: '',
                    Nombre: '',
                    Apellido: '',
                    Titulo: '',
                    Especialidad: '',
                    Tipo: '',
                    PagaHonorariosXInformes: '',
					Direccion: '',
                    Telefono: '',
                    ClinicaCalleAvenida: '',
                    ClinicaTel: '',
                    HonorariosPacienteCOEX: '',
                    CorreoElectronico: ''
                },
                configuracion:{
                    popUpConfiguracion: false,

                    tiposCajas: [],
                    tipoCajaSeleccionada: null,
                    indexTipoCajaSeleccionada: null,

                    cajaSeleccionada: null,
                    cajasFiltradasPorTipo: [],
                    codigosPermisosCajas: []                        
                },
                caja: {
                    serieFactura:[],
                    serieFacturaFiltrada:[],
                                        
                    tiposFacturacion: [],
                    tipoFacturacionSeleccionada:{}
                }

            }
        },
        methods:{
            BusquedaAjeno(indicador){
                if(indicador == 0){
                    if(this.DelAjeno != ''){
                        this.axios.post('/app/v1_contabilidad_hospital/BusquedaAjeno', {
                            Codigo: this.DelAjeno
                        })
                        .then(resp =>{
                            if(resp.data.json.length == 1){
                                this.DelAjeno = resp.data.json[0].Codigo
                                this.AjenoSeleccionadoDel = resp.data.json[0]

                            }else{
                                this.$refs.BuscarAjenoDel.iniciar((data) => {
                                    if(data != null){
                                        this.DelAjeno = data.Codigo
                                        this.AjenoSeleccionadoDel = data

                                    }
                                })
                            }
                        })
                    }else{
                        this.$refs.BuscarAjenoDel.iniciar((data) => {
                            if(data != null){
                                this.DelAjeno = data.Codigo
                                this.AjenoSeleccionadoDel = data

                            }
                        })
                    }
                }else{
                    if(this.AlAjeno != ''){
                        this.axios.post('/app/v1_contabilidad_hospital/BusquedaAjeno', {
                            Codigo: this.AlAjeno
                        })
                        .then(resp =>{
                            if(resp.data.json.length == 1){
                                this.AlAjeno = resp.data.json[0].Codigo
                                this.AjenoSeleccionadoAl = resp.data.json[0]

                            }else{
                                this.$refs.BuscarAjenoAl.iniciar((data) => {
                                    if(data != null){
                                        this.AlAjeno = data.Codigo
                                        this.AjenoSeleccionadoAl = data

                                    }
                                })
                            }
                        })
                    }else{
                        this.$refs.BuscarAjenoAl.iniciar((data) => {
                            if(data != null){
                                this.AlAjeno = data.Codigo
                                this.AjenoSeleccionadoAl = data

                            }
                        })
                    }
                }
            },
            BusquedaAjenoPorPagar(){
                if(this.configuracion.cajaSeleccionada == null){
                    this.$vs.notify({
                        time: 4000,
                        title: 'Contabilidad',
                        color: 'danger',
                        text: 'Debe de seleccionar una serie de factura...',
                        position: 'top-center'
                    })

                }else{
                    this.axios.post('/app/v1_contabilidad_hospital/BusquedaAjenoPorPagar', {
                        CodigoAjenoDel: this.DelAjeno,
                        CodigoAjenoAl: this.AlAjeno,
                        FechaPlanilla: this.FechaPlanilla
                    })
                    .then(resp => {
                        if(resp.data.codigo == 0){
                            this.$vs.dialog({
                                type: 'confirm',
                                color: 'primary',
                                title: 'Confirmacion',
                                text: resp.data.resultado == 1 ? `Será ${resp.data.resultado} factura.` : `Serán ${resp.data.resultado} facturas.`,
                                acceptText: 'Aceptar',
                                cancelText: 'Cancelar',
                                accept: () => {
                                    this.axios.post('/app/v1_contabilidad_hospital/GrabarAjenoPorPagar', {
                                        CodigoAjenoDel: this.DelAjeno,
                                        CodigoAjenoAl: this.AlAjeno,
                                        FechaPlanilla: this.FechaPlanilla,
                                        SerieFactura: this.configuracion.cajaSeleccionada.SerieFac,
                                        FechaFactura: this.FechaFactura,
                                        CajaFactura: this.configuracion.cajaSeleccionada.CajaLocal
                                    })
                                    .then(resp => {
                                        if(resp.data.codigo == 0){
                                            let resultado = resp.data.resultado.split(',')
                                            for(let NumFactura  of resultado){
                                                this.axios.post('/app/v1_FacturaElectronica/GeneraFel', {
                                                    serieFactura: this.configuracion.cajaSeleccionada.SerieFac,
                                                    numeroFactura: NumFactura
                                                })
                                                .then(resp => {
                                                    if(resp.data.codigo == 0){
                                                        this.GenerarReporteFactura(this.configuracion.cajaSeleccionada.SerieFac, NumFactura)
                                                    }
                                                })
                                            }
                                            this.DelAjeno = ''
                                            this.AlAjeno = ''
                                        }
                                    })
                                }
                            })
                        }
                    })
                }
            },
            GenerarReporteFactura(SerieFactura, Factura){
                this.$reporte_modal({
                    Nombre: "Impresion FEL",
                    Opciones: {
                        SerieFactura: SerieFactura,
                        NumFactura: Factura
                    },
                    Formato: "PDF",
                    Imprimir: true,
                    Descargar: true,
                    NombreArchivoDescargar: SerieFactura + '-' + Factura
                })
            },
            CargarCaja(cajaSeleccionada){                
                this.configuracion.cajaSeleccionada   = cajaSeleccionada.Cajero
                this.configuracion.activarFacturacion = cajaSeleccionada.Factura
            }
        },
        mounted(){
            this.FechaFactura = new Date().toISOString().slice(0, 10);
            this.FechaPlanilla= new Date().toISOString().slice(0, 10);
        }
    }
</script>
<style scoped>
    .panel-cajero-elegir{
        background-color: rgba(var(--vs-danger), 1) !important; 
    }
    .panel-cajero {
        display:inline-block;
        border-radius: 25px;
        background-color: rgba(var(--vs-primary), 1);
        color: white;
        padding: 5px 65px 5px 10px;
        position: relative;
        width: auto;
        /* top: 12px; */
        overflow: hidden;
        /* margin-right: -14px; */
        font-size: 13px;
        /* right: 50px; */
        /* border-radius: 30px 0 0 0; */
        /* box-shadow: inset 0 2px 0px 1px rgba(0, 0, 0, 0.2); */
        cursor: pointer;
    }

</style>