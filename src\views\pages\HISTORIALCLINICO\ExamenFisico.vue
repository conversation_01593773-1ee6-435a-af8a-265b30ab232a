<template>
<div class="p-2">
    <vx-card>
        <vs-row>
            <vs-col>
                <DxForm :form-data.sync="examenFisico" labelMode="floating" :read-only="Modulo === 2 ? true : false">
                    <!-- CABEZA -->
                    <DxFormGroupItem caption="Cabeza" :col-count-by-screen="colCountByScreen">
                        <DxFormItem data-field="Cabeza" editor-type="dxTextArea" :editor-options="{ height: 100 }" />
                        <DxFormItem data-field="Cuello" editor-type="dxTextArea" :editor-options="{ height: 100 }" />
                        <DxFormItem data-field="Ojos" editor-type="dxTextArea" :editor-options="{ height: 100 }" />
                        <DxFormItem data-field="Boca" editor-type="dxTextArea" :editor-options="{ height: 100 }" />
                        <DxFormItem data-field="Oidos" editor-type="dxTextArea" :editor-options="{ height: 100 }">
                            <DxFormLabel :text="'Oídos'" />
                        </DxFormItem>
                        <DxFormItem data-field="Nariz" editor-type="dxTextArea" :editor-options="{ height: 100 }" />
                    </DxFormGroupItem>

                    <!-- CUERPO -->
                    <DxFormGroupItem caption="Cuerpo" :col-count-by-screen="colCountByScreen">
                        <DxFormItem data-field="Abdomen" editor-type="dxTextArea" :editor-options="{ height: 100 }" />
                        <DxFormItem data-field="Corazon" editor-type="dxTextArea" :editor-options="{ height: 100 }">
                            <DxFormLabel :text="'Corazón'" />
                        </DxFormItem>
                        <DxFormItem data-field="Piel" editor-type="dxTextArea" :editor-options="{ height: 100 }" />
                        <DxFormItem data-field="Pulmones" editor-type="dxTextArea" :editor-options="{ height: 100 }" />
                        <DxFormItem data-field="Extremidades" editor-type="dxTextArea" :editor-options="{ height: 100 }" />
                        <DxFormItem data-field="Torax" editor-type="dxTextArea" :editor-options="{ height: 100 }">
                            <DxFormLabel :text="'Tórax'" />
                        </DxFormItem>
                        <DxFormItem data-field="RegionInguinal" editor-type="dxTextArea" :editor-options="{ height: 100 }">
                            <DxFormLabel :text="'Región inguinal'" />
                        </DxFormItem>
                    </DxFormGroupItem>

                    <!-- GENITAL -->
                    <DxFormItem item-type="group" :col-span="1" :col-count="2">
                        <DxFormItem item-type="group" caption="Genital" :col-span="1" :col-count="1">
                            <DxFormItem data-field="Genital" editor-type="dxTextArea" :editor-options="{ height: 100 }">
                                <DxFormLabel :text="''" />
                            </DxFormItem>
                        </DxFormItem>
                        <DxFormItem item-type="group" caption="Ginecológico / Rectal" :col-span="1" :col-count="1">
                            <DxFormItem data-field="Ginecologico" editor-type="dxTextArea" :editor-options="{ height: 100 }">
                                <DxFormLabel :text="'Ginecológico / Rectal'" />
                            </DxFormItem>
                        </DxFormItem>
                    </DxFormItem>

                    <!-- NERVIOSO -->
                    <DxFormItem item-type="group">
                        <DxFormItem item-type="group" caption="Nervioso">
                            <DxFormItem data-field="Nervioso" editor-type="dxTextArea" :editor-options="{ height: 100 }">
                                <DxFormLabel :text="''" />
                            </DxFormItem>
                        </DxFormItem>
                    </DxFormItem>
                </DxForm>
            </vs-col>
        </vs-row>
    </vx-card>
</div>
</template>

<script>
import 'devextreme-vue/lookup'

export default {
    name: 'ExamenFisico',
    components: {},
    data() {
        return {
            indiceTab: 0,
            buttons: [{
                name: 'Evoluciones',
                icon: 'arrow-up-right-dots'
            }, {
                name: 'Laboratorios',
                icon: 'vial-circle-check'
            }, {
                name: 'Exámenes',
                icon: 'radiation'
            }, {
                name: 'Encamamiento',
                icon: 'bed'
            }, ],
            tabs: [{
                name: 'Antecedentes personales',
                value: 1,
                icon: "address-card",
            }, {
                name: 'Exámen físico',
                value: 2,
                icon: "person-falling-burst",
            }, {
                name: 'Signos vitales',
                value: 3,
                icon: "heart-pulse",
            }, {
                name: 'Impresión clínica / Diagnóstico',
                value: 4,
                icon: "laptop-medical",
            }, {
                name: 'Referencia',
                value: 5,
                icon: "person-arrow-up-from-line",
            }, {
                name: 'Tratamiento',
                value: 6,
                icon: "pills",
            }, {
                name: 'Lab. ext.',
                value: 7,
                icon: "vial",
            }, {
                name: 'Receta',
                value: 8,
                icon: "file-prescription",
            }, ],

            numericOptionGineco: {
                max: 100,
                min: 0,
                format: '#',
                step: 0
            },
            examenFisico: {
                Cabeza: null,
                Ojos: null,
                Oidos: null,
                Nariz: null,
                Boca: null,
                Cuello: null,
                Torax: null,
                Corazon: null,
                Pulmones: null,
                Abdomen: null,
                Extremidades: null,
                Piel: null,
                RegionInguinal: null,
                Ginecologico: null,
                Genital: null,
                Nervioso: null
            },
            examenNervioso: 'HABILIDADES COGNITIVAS: \nNERVIOS CRANEALES: \nFUERZA: \nREM: \nFUNCION CEREBELOSA: \nSENSIBLIDIDAD: \nTONO: \nATROFISMO: \nATAVICOS: \nMOVIMIENTOS ANORMALES: \nMENINGEOS: \nMARCHA: \nSOPLOS:'
        }
    },
    props: {
        Sexo: null,
        IdCita: null,
        IdCliente: null,
        IdPaciente: null,
        IdMedico: null,
        IdConsulta: null,

        Modulo: null, //Variable para saber desde donde está siendo invocado el componente 1 = Historial | 2 = Evoluciones
    },
    methods: {
        BusquedaExamenFisico() {
            this.axios.post('/app/v1_Historial_Clinico/BusquedaExamenFisico', {
                    IdCliente: this.IdCliente,
                    IdCita: this.IdCita
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.examenFisico = resp.data.json[0]

                        // En esta sección se sustituyen los saltos de línea con los que se almacena el texto en base de datos, para que se puedan visualizar correctamente en Sighos
                        this.examenFisico.Cabeza = this.examenFisico.Cabeza !== null ? this.examenFisico.Cabeza.replace(/\r\n/g, '\n') : null
                        this.examenFisico.Ojos = this.examenFisico.Ojos !== null ? this.examenFisico.Ojos.replace(/\r\n/g, '\n') : null
                        this.examenFisico.Oidos = this.examenFisico.Oidos !== null ? this.examenFisico.Oidos.replace(/\r\n/g, '\n') : null
                        this.examenFisico.Nariz = this.examenFisico.Nariz !== null ? this.examenFisico.Nariz.replace(/\r\n/g, '\n') : null
                        this.examenFisico.Boca = this.examenFisico.Boca !== null ? this.examenFisico.Boca.replace(/\r\n/g, '\n') : null
                        this.examenFisico.Cuello = this.examenFisico.Cuello !== null ? this.examenFisico.Cuello.replace(/\r\n/g, '\n') : null
                        this.examenFisico.Torax = this.examenFisico.Torax !== null ? this.examenFisico.Torax.replace(/\r\n/g, '\n') : null
                        this.examenFisico.Corazon = this.examenFisico.Corazon !== null ? this.examenFisico.Corazon.replace(/\r\n/g, '\n') : null
                        this.examenFisico.Pulmones = this.examenFisico.Pulmones !== null ? this.examenFisico.Pulmones.replace(/\r\n/g, '\n') : null
                        this.examenFisico.Abdomen = this.examenFisico.Abdomen !== null ? this.examenFisico.Abdomen.replace(/\r\n/g, '\n') : null
                        this.examenFisico.Extremidades = this.examenFisico.Extremidades !== null ? this.examenFisico.Extremidades.replace(/\r\n/g, '\n') : null
                        this.examenFisico.Piel = this.examenFisico.Piel !== null ? this.examenFisico.Piel.replace(/\r\n/g, '\n') : null
                        this.examenFisico.RegionInguinal = this.examenFisico.RegionInguinal !== null ? this.examenFisico.RegionInguinal.replace(/\r\n/g, '\n') : null
                        this.examenFisico.Ginecologico = this.examenFisico.Ginecologico !== null ? this.examenFisico.Ginecologico.replace(/\r\n/g, '\n') : null
                        this.examenFisico.Genital = this.examenFisico.Genital !== null ? this.examenFisico.Genital.replace(/\r\n/g, '\n') : null
                        this.examenFisico.Nervioso = this.examenFisico.Nervioso !== null ? this.examenFisico.Nervioso.replace(/\r\n/g, '\n') : null
                    }

                    if (this.examenFisico === undefined || this.examenFisico === null || this.examenFisico.IdFisicoNervioso === undefined) {
                        this.LimpiarVariables()
                        this.examenFisico.Nervioso = this.examenNervioso
                    }
                })

        },
        LimpiarVariables() {
            this.examenFisico = {
                Cabeza: '',
                Ojos: '',
                Oidos: '',
                Nariz: '',
                Boca: '',
                Cuello: '',
                Torax: '',
                Corazon: '',
                Pulmones: '',
                Abdomen: '',
                Extremidades: '',
                Piel: '',
                RegionInguinal: '',
                Ginecologico: '',
                Genital: '',
                Nervioso: ''
            }
        },
        GuadarExamenFisico() {
            if (this.IdConsulta !== null) {

                //Se sustituyen los saltos de línea que se presentan en Sighos para sustituirlos por lo que se almacenan en Delphi
                this.axios.post('/app/v1_Historial_Clinico/ActualizarExamenFisico', {
                        IdFisicoCabeza: this.examenFisico.IdFisicoCabeza,
                        Cabeza: this.examenFisico.Cabeza !== null ? this.examenFisico.Cabeza.replace(/\n/g, '\r\n') : null,
                        Ojos: this.examenFisico.Ojos !== null ? this.examenFisico.Ojos.replace(/\n/g, '\r\n') : null,
                        Oidos: this.examenFisico.Oidos !== null ? this.examenFisico.Oidos.replace(/\n/g, '\r\n') : null,
                        Nariz: this.examenFisico.Nariz !== null ? this.examenFisico.Nariz.replace(/\n/g, '\r\n') : null,
                        Boca: this.examenFisico.Boca !== null ? this.examenFisico.Boca.replace(/\n/g, '\r\n') : null,
                        Cuello: this.examenFisico.Cuello !== null ? this.examenFisico.Cuello.replace(/\n/g, '\r\n') : null,
                        IdFisicoCuerpo: this.examenFisico.IdFisicoCuerpo,
                        Torax: this.examenFisico.Torax !== null ? this.examenFisico.Torax.replace(/\n/g, '\r\n') : null,
                        Corazon: this.examenFisico.Corazon !== null ? this.examenFisico.Corazon.replace(/\n/g, '\r\n') : null,
                        Pulmones: this.examenFisico.Pulmones !== null ? this.examenFisico.Pulmones.replace(/\n/g, '\r\n') : null,
                        Abdomen: this.examenFisico.Abdomen !== null ? this.examenFisico.Abdomen.replace(/\n/g, '\r\n') : null,
                        Extremidades: this.examenFisico.Extremidades !== null ? this.examenFisico.Extremidades.replace(/\n/g, '\r\n') : null,
                        Piel: this.examenFisico.Piel !== null ? this.examenFisico.Piel.replace(/\n/g, '\r\n') : null,
                        RegionInguinal: this.examenFisico.RegionInguinal !== null ? this.examenFisico.RegionInguinal.replace(/\n/g, '\r\n') : null,
                        Ginecologico: this.examenFisico.Ginecologico !== null ? this.examenFisico.Ginecologico.replace(/\n/g, '\r\n') : null,
                        Genital: this.examenFisico.Genital !== null ? this.examenFisico.Genital.replace(/\n/g, '\r\n') : null,
                        IdFisicoNervioso: this.examenFisico.IdFisicoNervioso,
                        Nervioso: this.examenFisico.Nervioso !== null ? this.examenFisico.Nervioso.replace(/\n/g, '\r\n') : null,
                        IdHistorialClinico: this.IdConsulta
                    })
                    .then(() => {
                        this.BusquedaExamenFisico()
                    })
            }
        }
    },
    mounted() {
        if (this.Modulo !== 2) {
            this.BusquedaExamenFisico()
        }
    },
    watch: {
        // 'IdCliente'(newval) {
        //     if (newval !== '' && newval !== null && newval !== undefined) {
        //         this.BusquedaExamenFisico()
        //     }
        // },
        'IdCita'(newval) {
            if (newval !== '' && newval !== null && newval !== undefined) {
                this.BusquedaExamenFisico()
            }
        },
    },
    computed: {
        colCountByScreen() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 2,
                    lg: 3
                };
        },

        colCountByScreen1() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 2,
                    lg: 2
                };
        },
    },
}
</script>
