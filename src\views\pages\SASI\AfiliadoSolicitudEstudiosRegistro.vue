<template>
<div class="afiliados-registro-solicitudes-container">
    <div v-show="IdStatus != 'A'" class="alert">
        <i class="fas fa-exclamation-triangle"></i>
        El afiliado no esta activo, solo se puede consultar la información.
    </div>
    <form @submit="handleSubmit">
        <DxForm css-class="solicitudes-form" :ref="REFFORM" :form-data.sync="FormData" label-location="left" label-mode="floating" validation-group="validacionSolicitud" :col-count="2" required-message="{0} es obligatorio" :read-only="!PermiteGrabar" :show-validation-summary="true" :screen-by-width="getSizeQualifier">
            <DxFormGroupItem :col-count="2" caption="Datos de Solicitud">

                <DxFormSimpleItem data-field="NumeroSolicitud" :label="{text:'Número de Solicitud'}" editor-type="dxTextBox" :editor-options="{readOnly:true}" :col-span="1" />
                <DxFormSimpleItem data-field="FechaSolicitud" :label="{text:'Fecha de Solicitud'}" editor-type="dxDateBox" :editorOptions="{ dataType: 'date', min: new Date(1990,0,1), displayFormat: 'dd/MM/yyyy', readOnly: true }" :col-span="1" />
                <DxFormSimpleItem data-field="NombreMedico" template="template-medico" :col-span="2" :validation-rules="[{type:'required', message:'Ingrese datos del médico'}]" />
                <DxFormSimpleItem data-field="DiasVigencia" caption="Días de Viegencia" editor-type="dxSelectBox" :editor-options="{readOnly: !PermiteGrabar, showClearButton:true, items:[30,60,90]}" :col-span="2" />
                <DxFormSimpleItem data-field="Observaciones" editor-type="dxTextArea" :editor-options="{maxLength:255, height: 90,}" :col-span="PermiteGrabar?2:1" />
                <!-- Notas solo es visible cuando se muestra la información -->
                <DxFormSimpleItem :visible="Modo=='LECTURA'" data-field="Notas" :label="{text:'Notas de autorizaciones'}" editor-type="dxTextArea" :col-span="1" :editor-options="{maxLength:250, height: 90,}" />
            </DxFormGroupItem>
            <DxFormGroupItem :col-count="1" :col-span="2" caption="Diagnósticos">
                <DxFormSimpleItem template="diagnosticos" :col-span="6" :validation-rules="[ValidationRules.Diagnosticos]" />
            </DxFormGroupItem>

            <DxFormGroupItem :col-count="6" :col-span="2" caption="Detalle de la autorización" :visible="Modo=='LECTURA'">

                <DxFormSimpleItem data-field="NumeroAutorizacion" editor-type="dxTextBox" />
                <DxFormSimpleItem data-field="StatusSolicitud" editor-type="dxTextBox" />
                <DxFormSimpleItem data-field="UsuarioIngreso" editor-type="dxTextBox" />
                <DxFormSimpleItem data-field="FechaIngreso" editor-type="dxDateBox" :editorOptions="{ dataType: 'date', displayFormat: 'dd/MM/yyyy HH:mm' }" />

                <DxFormSimpleItem data-field="Admision" editor-type="dxTextBox" />
                <DxFormSimpleItem data-field="FechaAdmision" editor-type="dxDateBox" :editorOptions="{ dataType: 'date', displayFormat: 'dd/MM/yyyy HH:mm' }" />

                <DxFormSimpleItem data-field="UsuarioAutoriza" editor-type="dxTextBox" />
                <DxFormSimpleItem data-field="FechaAutoriza" editor-type="dxDateBox" :editorOptions="{ dataType: 'date', displayFormat: 'dd/MM/yyyy HH:mm' }" />

                <DxFormSimpleItem data-field="UsuarioOperado" editor-type="dxTextBox" />
                <DxFormSimpleItem data-field="FechaOperado" editor-type="dxDateBox" :editorOptions="{ dataType: 'date', displayFormat: 'dd/MM/yyyy HH:mm' }" />

                <DxFormSimpleItem data-field="UsuarioAnulacion" editor-type="dxTextBox" />
                <DxFormSimpleItem data-field="FechaAnulacion" editor-type="dxDateBox" :editorOptions="{ dataType: 'date', displayFormat: 'dd/MM/yyyy HH:mm' }" />

                <DxFormSimpleItem data-field="UsuarioReactiva" editor-type="dxTextBox" />
                <DxFormSimpleItem data-field="FechaReactivacion" editor-type="dxDateBox" :editorOptions="{ dataType: 'date', displayFormat: 'dd/MM/yyyy HH:mm' }" />
            </DxFormGroupItem>

            <DxFormGroupItem :col-count="1" :col-span="2" caption="Procedimientos">
                <DxFormSimpleItem data-field="Procedimientos" :template="PermiteGrabar?'productos':'consulta-productos'" :col-span="2" />
            </DxFormGroupItem>

            <DxFormButtonItem :visible="PermiteGrabar" :button-options="submitButtonOptions" horizontal-alignment="center" verical-alignment="buttom" css-class="--mysticky" :col-span="2" />

            <template #template-medico>

                <div class="flex flex-row">
                    <DxNumberBox class="w-1/6" v-model="FormData.IdMedico" :read-only="!PermiteGrabar" label-mode="floating" label="Código" @focus-in="busquedaTextBox = true" @value-changed="ConsultaAjenos" />
                    <DxTextBox class="w-5/6" v-model="FormData.NombreMedico" :read-only="true" label-mode="floating" label="Médico">
                        <DxTextBoxButton v-if="PermiteGrabar" :options="{
                            icon: 'find',
                            hint: 'Seleccionar Médico',
                            type: 'default',
                            stylingMode: 'text',
                            visible: true,
                            onClick: buscarMedico,
                            disabled: false, //para evitar que el readonly del textbox lo inhabilite
                        }" name="password" location="after" />
                    </DxTextBox>
                </div>

            </template>

            <template #diagnosticos>
                <div>
                    <DxLookup v-if="PermiteGrabar" class="mt-2 mb-2" ref="refLookup" :dataSource="cie10DataSource" display-expr="Display" @value-changed="AgregarDiagnostico" :value="selectedCIE10" :use-popover="true" :drop-down-options="{showTitle:false,}" placeholder="Seleccione un diagnóstico para agregar a la lista" />
                    <DxDataGrid :data-source="FormData.Diagnosticos" v-bind="DiagnosticosGridOptions">
                        <DxDataGridColumn :visible="PermiteGrabar" :width="50" type="buttons" alignment="center" :buttons="[{name:'delete'}]" />
                        <DxDataGridColumn width="auto" data-field="Display" caption="Diagnóstico" alignment="center" :buttons="[{name:'delete'}]" />
                    </DxDataGrid>
                </div>
            </template>

            <template #productos>
                <div class="p-1" :style="{ border: borderStyle, borderRadius:'5px' }">
                    <DxValidator :adapter="adapterConfig" validation-group="validacionSolicitud">
                        <DxRequiredRule message="Se requiere por lo menos un procedimiento" />
                    </DxValidator>

                    <vs-row class="pt-2">
                        <vs-col vs-w="6" vs-justify="center" vs-align="center" class="pr-2">

                            <DxDataGrid :ref="gridCargaProductos" :data-source="productosOrden" v-bind="dataGridProductosOptions" :headerFilter="{visible:false, allowSearch:false}" :width="'100%'" :height="'400px'">
                                <DxDataGridColumn caption="#" :width="30" cell-template="grid-numeracion" :allow-filtering="false" :allow-resizing="false" />
                                <DxDataGridColumn :width="50" type="buttons" alignment="center" :buttons="[{name:'delete'}]" />
                                <DxDataGridColumn :width="'auto'" data-field="Codigo" caption="Código" alignment="center" :allow-editing="false" />
                                <DxDataGridColumn :width="'auto'" data-field="Nombre" caption="Producto" alignment="center" :allow-editing="false" />
                                <DxDataGridColumn :width="'auto'" data-field="Precio" caption="Precio" alignment="center" :data-type="monedaOptions.dataType" :format="monedaOptions.format" :allow-editing="false" />
                                <template #grid-numeracion="{ data }">
                                    {{ data.rowIndex + 1 }}
                                </template>
                            </DxDataGrid>

                        </vs-col>
                        <vs-col vs-justify="center" vs-align="center" vs-w="6" class="pr-2">

                            <DxDataGrid :ref="gridProductos" v-bind="busquedaGridOptions" :data-source="productos" @row-dbl-click="AgregarProducto">
                                <DxDataGridColumn :width="'auto'" data-field="Codigo" caption="Código" alignment="center" />
                                <DxDataGridColumn :width="'80%'" data-field="Nombre" caption="Producto" alignment="center" />
                                <DxDataGridColumn :width="'auto'" data-field="Precio" caption="Precio" data-type="number" alignment="center" :format="{ type: 'fixedPoint' , precision: 2 }" :allow-editing="false" />
                                <DxDataGridColumn :width="'50'" data-field="Autorizado" caption="Autorizado" alignment="center" :format="{ type: 'fixedPoint' , precision: 2 }" :allow-filering="false" />
                            </DxDataGrid>

                        </vs-col>
                    </vs-row>
                </div>
            </template>

            <template #consulta-productos>
                <div>
                    <DxDataGrid :data-source="FormData.Procedimientos" v-bind="consultaProdGridOptions">
                        <DxDataGridColumn caption="#" :width="30" cell-template="grid-numeracion" :allow-filtering="false" :allow-resizing="false" />
                        <DxDataGridColumn data-field="StatusAnalista" caption="Aut." :width="50" cell-template="smile-cell-template" :allow-filtering="false" :allow-resizing="false" />
                        <DxDataGridColumn data-field="CodigoProducto" caption="Código" :width="100" />
                        <DxDataGridColumn data-field="NombreProducto" caption="Producto" width="auto" />
                        <DxDataGridColumn data-field="TipoOrden" alignment="center" :width="90" />
                        <DxDataGridColumn data-field="NoOrden" caption="Orden" :width="100" />
                        <DxDataGridColumn data-field="Warning" caption="Alerta (W)" :width="55" />
                        <DxDataGridColumn caption="Precio" :width="100" :data-type="monedaOptions.dataType" :format="monedaOptions.format" :calculate-cell-value="calculoPrecio" />
                        <DxDataGridColumn data-field="Declinacion" caption="Motivo Declinación" width="auto" />
                        <template #smile-cell-template="{ data: templateOptions }">
                            <div>
                                <DxButton v-if="templateOptions.data.StatusAnalista=='A'" icon="far fa-smile" type="success" stylingMode="outlined" class="smile-button" hint="Autorizado" />
                                <DxButton v-if="templateOptions.data.StatusAnalista=='D'" icon="far fa-frown" type="danger" stylingMode="outlined" class="smile-button" hint="Declinado" />
                            </div>
                        </template>
                        <template #grid-numeracion="{ data }">
                            {{ data.rowIndex + 1 }}
                        </template>
                    </DxDataGrid>
                </div>
            </template>
        </DxForm>
    </form>

    <buscador class="solicitud-estudios-buscador-medico" ref="buscadorMedico" buscador_titulo="Buscador / Médico" api="app/Ajenos/Busqueda_Ajenos" :campos="['Codigo','Nombre','Apellido','Corporativo','#Tipo']" :titulos="['Codigo','Nombre','Apellido','Corporativo','Tipo']" :multiselect="false" :api_validar_campo="true" :api_preload="false" :api_cache="false" :api_disable_seleccion="null" @close="CancelaBusqueda" />
</div>
</template>

<script>
import 'devextreme-vue/text-area'

import {
    DefaulGridOptions,
    _confMoneda
} from './data.js'

const REFFORM = 'refForm'
const gridCargaProductos = 'gridCargaProductos'
const gridProductos = 'gridProductos'
const gridDiagnosticos = 'gridDiagnosticos'
const CATALOGOCIE = []
export default {
    name: 'AfiliadosSolictudEstudioRegistro',
    components: {
        
    },
    data() {
        //Adapter del validador para editores personalizados -->procedimientos
        const callbacks = [];
        const adapterConfig = {
            getValue: () => {
                return (this.productosOrden && this.productosOrden.length > 0) ? 'OKIS' : null;
            },
            applyValidationResults: (e) => {
                this.borderStyle = e.isValid ? "none" : "2px solid rgba(217, 83, 79, .4)";
            },
            validationRequestsCallbacks: callbacks,
        };
        const revalidate = () => {
            callbacks.forEach((func) => {
                func();
            });
        };
        return {
            FormData: {
                NumeroSolicitud: null,
                FechaSolicitud: null,
                IdMedico: null, // este es el codigo del medico cuando se cargó correctamente
                NombreMedico: null,
                DiasVigencia: null,
                Diagnosticos: [],
                Procedimientos: [],
                Observaciones: null,
                Notas: null,
                //Datos de consulta
                NumeroAutorizacion: null,
                Admision: null,
                FechaAdmision: null,
                StatusSolicitud: null,

                UsuarioIngreso: null,
                FechaIngreso: null,
                UsuarioAutoriza: null,
                FechaAutoriza: null,
                UsuarioOperado: null,
                FechaOperado: null,
                UsuarioAnulacion: null,
                FechaAnulacion: null,
                UsuarioReactiva: null,
                FechaReactivacion: null,
            },
            REFFORM,
            DiagnosticosGridOptions: {
                ...DefaulGridOptions,
                height: '250',
                groupPanel: null,
                searchPanel: null,
                selection: {
                    mode: 'single'
                },
                paging: {
                    enabled: true,
                    pageSize: 10
                },
                editing: {
                    useIcons: true,
                    allowDeleting: true,
                    confirmDelete: false,
                },
            },
            dataGridProductosOptions: {
                ...DefaulGridOptions,
                toolbar: {
                    items: [{
                        name: 'searchPanel'
                    }]
                },
                selection: {
                    mode: 'single'
                },
                editing: {
                    allowUpdating: true,
                    allowAdding: false,
                    allowDeleting: true,
                    mode: 'cell',
                    useIcons: true,
                    confirmDelete: false,
                },
                summary: {
                    totalItems: [{
                        column: 'Precio',
                        summaryType: 'sum',
                        displayFormat: "Total: {0}",
                        valueFormat: _confMoneda.format,
                        showInColumn: 'Nombre',
                    }, ]
                }
            },
            submitButtonOptions: {
                text: 'Guardar',
                type: 'success',
                icon: 'save',
                useSubmitBehavior: true,
                disabled: false,
            },
            busquedaTextBox: false, //para inidcar que la busqueda del médico es por textobx y no por el buscador :)

            ValidationRules: {
                Diagnosticos: {
                    type: 'custom',
                    reevaluate: true,
                    validationCallback: (e) => {
                        if (!this.FormData.Diagnosticos || this.FormData.Diagnosticos.length == 0) {
                            e.rule.message = 'Ingrese por lo menos un diagnóstico'
                            return false
                        }
                        e.rule.message = ''
                        return true
                    }
                },
                Procedimientos: {
                    type: 'custom',
                    reevaluate: true,
                    validationCallback: (e) => {
                        if (!this.FormData.Procedimientos || this.FormData.Procedimientos.length == 0) {
                            e.rule.message = 'Ingrese por lo menos un procedimiento'
                            return false
                        }
                        e.rule.message = ''
                        return true
                    }
                },
            },
            adapterConfig,
            revalidate,
            borderStyle: "none",

            // Catálogo de diagnóstico CIE10
            selectedCIE10: null,
            catalogoCIE10: CATALOGOCIE,
            cie10DataSource: {
                paginate: true,
                pageSize: 10,
                sort: 'Codigo',
                // loadMode: 'raw',
                byKey: (e) => {
                    return this.cie10.Codigo === e ? this.cie10.Display : ''
                },
                load: (e) => {
                    if (e.take) {
                        if (e.skip === e.take) {
                            e.take = e.take + 10
                        }
                    }
                    return this.$filtrado_avanzado_palabras(e.searchValue, this.catalogoCIE10, 'Display', e.skip, e.skip + e.take)
                }
            },
            // Catálogo de producto o estudios 
            productos: [], //Variable que guarda el catálogo de productos
            productosOrden: [], //Variable que guarda los productos agregados a la orden

            gridCargaProductos,
            gridProductos,
            DefaultDxGridConfiguration: DefaulGridOptions,
            busquedaGridOptions: {
                ...DefaulGridOptions,
                paging: {
                    enabled: true,
                    pageSize: 10
                },
                headerFilter: {
                    visible: true,
                    allowSearch: true
                },
                width: '100%',
                height: '400px',
                selection: {
                    mode: 'single'
                },
            },

            //este es para mostrar los servicios en modo consulta
            consultaProdGridOptions: {
                ...DefaulGridOptions,
                searchPanel: null,
                groupPanel: null,
                height: 'auto',
                summary: {
                    calculateCustomSummary: (e) => {
                        switch (e.summaryProcess) {
                            case 'start':
                                e.totalValue = 0
                                break;
                            case 'calculate':
                                if (e.name == 'autorizado') {
                                    e.totalValue += e.value.StatusAnalista == 'A' ? e.value.Precio : 0
                                } else {
                                    e.totalValue += e.value.StatusAnalista != 'A' ? e.value.Precio : 0
                                }
                                break;
                            default:
                                break;
                        }
                    },
                    totalItems: [{
                            summaryType: 'custom',
                            name: 'autorizado',
                            displayFormat: 'Autorizado: {0}',
                            showInColumn: 'CodigoProducto',
                            valueFormat: _confMoneda.format,
                        },
                        {
                            summaryType: 'custom',
                            name: 'declinado',
                            displayFormat: 'Declinado: {0}',
                            showInColumn: 'Producto',
                            valueFormat: _confMoneda.format,
                        },
                        {
                            summaryType: 'sum',
                            column: 'Precio',
                            displayFormat: 'Total: {0}',
                            valueFormat: _confMoneda.format,
                        },
                    ]
                }
            },
            monedaOptions: _confMoneda,
        }
    },
    props: {
        IdCliente: String,
        IdAfiliado: String,
        IdStatus: String,
        Modo: {
            type: String,
            required: true,
            default: 'NUEVO',
            validator(value) {
                return ['NUEVO', 'LECTURA', 'EDITAR'].includes(value)
            }
        },
        IdSolicitud: {
            type: Number,
            default: null
        },
        IdAseguradora: String,
        NivelPrecios: Number,
        TelefonoCelular: String,

    },
    methods: {
        handleSubmit(e) {
            e.preventDefault()
            this.GuardarOrden()
        },

        CancelaBusqueda() {
            this.$emit('busqueda-medico', false)
        },
        GuardarOrden() {

            let arrayProductos = []

            for (const i of this.productosOrden) {
                arrayProductos.push(i.Codigo.trim())
            }

            this.axios.post('/app/v1_afiliados/RegistroSolucitudProcedimiento', {
                    IdAfiliado: this.IdAfiliado,
                    IdCita: this.IdCita,
                    IdConsulta: this.IdConsulta,
                    IdCliente: this.IdCliente,
                    NivelPrecios: this.NivelPrecios,

                    CodigoMedico: this.FormData.IdMedico,
                    Observacion: this.FormData.Observaciones,
                    Productos: this.productosOrden.map(x => x.Codigo),
                    Diagnosticos: this.FormData.Diagnosticos.map(x => x.Codigo),
                    DiasVigencia: this.FormData.DiasVigencia,
                    ExamenControl: Boolean(this.FormData.DiasVigencia),
                    SMSNotificacion: parseInt(this.TelefonoCelular) ? [parseInt(this.TelefonoCelular)] : null
                })
                .then(resp => {
                    if (resp.data.codigo === 0) {
                        this.$emit('saved-data', resp.data)
                        this.Limpiar()
                    }
                })
        },

        onCellPrepared(e) {
            if (e.rowType === 'data' && e.row.data.StatusAnalista === 'Declinado') {
                e.cellElement.classList.add("declinado");
            }
        },
        CargarSolicitud() {
            if (this.IdSolicitud) {
                Promise.all(
                    [
                        this.axios.post('/app/v1_afiliados/SolucitudEstudiosProductos', {
                            IdSolicitud: this.IdSolicitud
                        }),
                        this.axios.post('/app/v1_autorizaciones/ConsultaDiagnosticos', {
                            Solicitud: this.IdSolicitud,
                            Operacion: 'CONSULTADIAGNOSTICO'
                        }),
                    ]
                ).then((values) => {
                    const enc = values[0]
                    const dx = values[1]
                    this.FormData.IdMedico = enc.data[0].CodigoMedico
                    this.FormData.NombreMedico = enc.data[0].NombreMedico

                    this.FormData.NumeroSolicitud = enc.data[0].IdSolicitud
                    this.FormData.FechaSolicitud = enc.data[0].FechaIngreso

                    this.FormData.DiasVigencia = enc.data[0].DiasVigencia
                    this.FormData.Observaciones = enc.data[0].Observacion
                    this.FormData.Notas = enc.data[0].Notas

                    //campos visibles en modo consulta, estas no se registran ni se manipulan de momento
                    this.FormData.NumeroAutorizacion = enc.data[0].NoAutorizacion
                    this.FormData.Admision = ''.concat(enc.data[0].SerieAdmision ?? '', enc.data[0].Admision ?? '')
                    this.FormData.FechaAdmision = enc.data[0].FechaAdmision
                    this.FormData.StatusSolicitud = enc.data[0].StatusSolicitud

                    this.FormData.UsuarioIngreso = enc.data[0].UsuarioIngresoEnc
                    this.FormData.FechaIngreso = enc.data[0].FechaIngreso
                    this.FormData.UsuarioAutoriza = enc.data[0].UsuarioAutorizaEnc
                    this.FormData.FechaAutoriza = enc.data[0].FechaAutorizaEnc
                    this.FormData.UsuarioOperado = enc.data[0].UsuarioOperadoEnc
                    this.FormData.FechaOperado = enc.data[0].FechaOperadoEnc
                    this.FormData.UsuarioAnulacion = enc.data[0].UsuarioAnulacionEnc
                    this.FormData.FechaAnulacion = enc.data[0].FechaAnulacionEnc
                    this.FormData.UsuarioReactiva = enc.data[0].UsuarioReactivaEnc
                    this.FormData.FechaReactivacion = enc.data[0].FechaReactivacionEnc

                    this.FormData.Diagnosticos = dx.data.json.map(x => {
                        return {
                            Codigo: x.CodigoDiagnostico,
                            Descripcion: x.descripcion,
                            Display: x ? x.CodigoDiagnostico + ' - ' + x.descripcion : 'Diagnóstico para Estadística'
                        }
                    })
                    this.FormData.Procedimientos = enc.data
                    this.productosOrden = enc.data.map(p => {
                        return {
                            Autorizado: null,
                            Cantidad: 1,
                            Codigo: p.CodigoProducto,
                            Nombre: p.NombreProducto,
                            Precio: p.ProductoPrecio,
                        }
                    })
                })
            } else {
                this.Limpiar()
            }

        },

        buscarMedico() {
            this.busquedaTextBox = false
            this.$emit('busqueda-medico', true)
            this.$refs.buscadorMedico.iniciar((data) => {
                if (data != null) {
                    this.FormData.IdMedico = data.Codigo
                    this.FormData.NombreMedico = data.NombreCompleto
                }
            })
        },

        AgregarDiagnostico(e) {
            if (e.value && !this.FormData.Diagnosticos.find(x => x.Codigo == e.value.Codigo)) {
                this.FormData.Diagnosticos.push(e.value)
                this.$nextTick(() => {
                    this.formSolicitud.repaint()
                    setTimeout(() => {
                        this.$refs.refLookup.instance.focus()
                    }, 100)

                })
            } else {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'Agregar Diagnóstico',
                    acceptText: 'Aceptar',
                    text: 'El diagnóstico ya ha sido agregado.',
                    buttonCancel: 'border',
                })
            }
        },

        AgregarProducto(e) {
            /** NOTA pareciera que debe de validarse ingresar solo un producto para la solicitud, sin embargo requirieron agregar varios productos repetidos por solicitud */
            e.data.Cantidad = 1
            e.data.Precio = parseFloat(e.data.Precio).toFixed(2)
            this.productosOrden.push(e.data)

            this.dataGridProductos.clearSelection()
            this.infoProducto = {}
        },

        CargarProductos() {
            this.axios.post('/app/v1_afiliados/BusquedaServiciosEstudios', {
                    CodigoAseguradora: this.IdAseguradora,
                    NivelPrecio: this.NivelPrecios,
                })
                .then(resp => {
                    this.productos = resp.data.json
                })
        },

        ConsultaAjenos(e) {
            if (e.value && this.busquedaTextBox) {
                this.busquedaTextBox = false
                this.axios.post('/app/Ajenos/Busqueda_Ajenos', {
                        Codigo: e.value
                    })
                    .then(resp => {
                        if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                            this.FormData.NombreMedico = resp.data.json[0].NombreCompleto
                        } else {
                            this.FormData.NombreMedico = null
                            this.FormData.IdMedico = null
                            this.$vs.notify({
                                time: 4000,
                                title: 'Busqueda de Médico',
                                text: 'No se encontró médico con el código proporcionado',
                                iconPack: "feather",
                                icon: "icon-alert-circle",
                                color: "warning",
                                position: "top-center",
                            })
                        }
                    })
            }
        },

        CargarCIE10() {
            if (CATALOGOCIE.length > 0)
                return

            this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaDiagnosticos', {})
                .then(resp => {
                    resp.data.json.map(x => {
                        CATALOGOCIE.push({
                            Codigo: x.codigoDiagnostico,
                            Descripcion: x.Diagnostico,
                            Display: x ? x.codigoDiagnostico + ' - ' + x.Diagnostico : 'Diagnóstico para Estadística'
                        })
                    })
                })
        },

        getSizeQualifier(width) {
            if (width < 768) return "xs";
            if (width < 992) return "sm";
            if (width < 1200) return "md";
            return "lg";
        },

        async Limpiar() {
            this.FormData.NumeroSolicitud = null
            this.FormData.FechaSolicitud = await this.$dbdate()
            this.FormData.IdMedico = null
            this.FormData.NombreMedico = null
            this.FormData.DiasVigencia = null
            this.FormData.Diagnosticos = []
            this.FormData.Procedimientos = []
            this.FormData.Observaciones = null
            this.FormData.Notas = null
            this.productosOrden = []
        },

        calculoPrecio(data) {
            return data.Precio ?? data.ProductoPrecio
        },
    },
    mounted() {
        this.CargarSolicitud()
    },
    beforeMount() {
        this.CargarProductos()
    },
    created() {
        this.CargarCIE10()
    },
    watch: {
        'IdSolicitud'() {
            this.CargarSolicitud()
        },
        'Modo'(val) {
            if (val == 'NUEVO')
                this.Limpiar()
            if (val == 'LECTURA')
                this.CargarSolicitud()

        },
        'NivelPrecios'() {
            if (this.Modo == 'NUEVO')
                this.CargarProductos()
        },
        'IdCliente'() {
            this.CargarSolicitud()
        },
    },
    computed: {
        dataGridDiagnosticos() {
            return this.$refs[gridDiagnosticos].instance
        },
        formSolicitud: function () {
            return this.$refs[REFFORM].instance
        },
        dataGridCargaProductos: function () {
            return this.$refs[gridCargaProductos].instance;
        },

        dataGridProductos: function () {
            return this.$refs[gridProductos].instance;
        },

        colCountByScreen() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 2,
                    lg: 2
                };
        },
        colCountByScreenDatos() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 3,
                    lg: 3
                };
        },
        PermiteGrabar() {
            return (this.Modo == 'NUEVO' || this.Modo == 'EDITAR' && this.FormData.StatusSolicitud == 'I') && this.IdStatus == 'A'
        }
    },

}
</script>

<style>
.afiliados-registro-solicitudes-container td:has(.smile-button) {
    padding-top: 0;
    padding-bottom: 0;
}

.smile-button {
    border: none;
}

.smile-button {
    background-color: transparent !important;
}

.smile-button i {
    font-size: x-large !important;
}

.afiliados-registro-solicitudes-container .alert {
    color: #F7B334;
    padding: 10px;
    overflow: hidden;
    position: relative;
    font-weight: 600;
    font-size: medium;
}
</style>
