<template>
<vx-card title="Autorización Solicitud de compra">

    <div class="content content-pagex">
        <form>
            <div class="flex flex-wrap">
                <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">
                    <div class="flex flex-wrap">
                        <div style="margin-left:10px;margin-right:20px" class="xs:w-full md:w-5/6 lg:w-5/6 xl:w-5/6">
                            <ValidationProvider name="Agencia">
                                <label class="typo__label">Departamentos</label>
                                <multiselect v-model="cbDepartamento" :options="ListaDepartamentos" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Departamento_seleccionada" placeholder="Seleccionar" @input="onChangeDepartamento">
                                    <span slot="noOptions">Lista no disponible.</span>
                                </multiselect>
                            </ValidationProvider>
                        </div>
                    </div>
                </div>
                <div style="margin-right: 20px" class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <label class="typo__label">Estado</label>
                    <multiselect v-model="cb_lista_operacion" :options="lista_estado" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Operacion_seleccionada" placeholder="Seleccionar" @input="onChangeEstado">
                        <span slot="noOptions">Lista no disponible.</span>
                    </multiselect>
                </div>

                <div style="margin-left:10px;margin-right:20px" class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7">
                    <label class="typo__label">Fecha Inicio:</label>
                    <vs-input type="date" v-model="fecha_inicio" name="date1" />
                </div>

                <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7">
                    <label class="typo__label">Fecha Final:</label>
                    <vs-input type="date" v-model="fecha_final" name="date1" />
                </div>

                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <vs-button style="float:left;margin: 20px" color="primary" type="filled" icon-pack="fas" icon="fa-search" @click="Consulta_autorizaciones()"> Búsqueda</vs-button>
                </div>
            </div>
        </form>

        <!---------------------- DETALLE SOLICITUDES PENDIENTES --------------->
        <vs-divider></vs-divider>
        <vs-table2 tooltip max-items="10" pagination :data="Lista_autorizaciones" noDataText="Sin datos disponibles" search id="tb_departamentos">

            <template slot="thead">
                <th width="60">Nº. Solicitud </th>
                <th width="250">Departamento</th>
                <th width="20">Tipo</th>
                <th width="450">Observación de Solicitud</th>
                <th width="100">Fecha Solicitud</th>
                <th width="200">Usuario</th>
                <th width="120">Estado</th>
                <th width="20">Autorizar</th>
                <th width="20">Detalle</th>

            </template>

            <template  slot-scope="{data}">
                <tr :key="indextr" v-for="(tr, indextr) in data">
                    <vs-td2 no tooltip :data="data[indextr].ID_SOLICITUD_ENC">
                        {{data[indextr].ID_SOLICITUD_ENC}}
                    </vs-td2>

                    <vs-td2 :data="data[indextr].DEPARTAMENTO_DES">
                        {{data[indextr].DEPARTAMENTO}}
                        - {{data[indextr].DEPARTAMENTO_DES}}
                    </vs-td2>
                    <vs-td2  :data="data[indextr].TIPO">
                        {{data[indextr].TIPO}}
                    </vs-td2>

                    <vs-td2  :data="data[indextr].OBSERVACIONES">
                        {{data[indextr].OBSERVACIONES}}
                    </vs-td2>
                    <vs-td2 no tooltip :data="data[indextr].FECHA_CREACION_ORDEN">
                        {{data[indextr].FECHA_CREACION_ORDEN}}
                    </vs-td2>
                    <vs-td2  :data="data[indextr].USUARIO">
                        {{data[indextr].USUARIO}}
                    </vs-td2>
                    <vs-td2 style="text-align: center;" :data="data[indextr].ESTADO">
                        <label v-if="data[indextr].ESTADO == 'P'" style="height: 30px;background-color:#B2BABB;width:150px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                        <label v-if="data[indextr].ESTADO == 'A'" style=" height: 30px;background-color:green;width:150px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                        <label v-if="data[indextr].ESTADO == 'R'" style="height: 30px;background-color:#C82B0A;width:150px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                    </vs-td2>
                    <vs-td2 align="center">
                        <vs-button v-if="data[indextr].ESTADO == 'A'" disabled="false" color="primary" icon-pack="fas" icon="fa-check-double" style="display:inline-block;margin-right:2px" @click="Finalizar_Autorizacion(data[indextr])"></vs-button>
                        <vs-button v-else color="primary" icon-pack="fas" icon="fa-check-double" style="display:inline-block;margin-right:2px" @click="Finalizar_Autorizacion(data[indextr])"></vs-button>
                    </vs-td2>
                    <vs-td2 align="center">
                        <vs-button color="success" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-eye" @click="Consultar_solicitud(data[indextr].ID_SOLICITUD_ENC)"></vs-button>
                    </vs-td2>
                </tr>
            </template>
        </vs-table2>

        <!--------------- VISOR DE DETALLE -------------->
        <vs-popup classContent="popup-example" title="Verificación de Detalle Solicitud" :active.sync="Ventana_Detalle" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">

                <form>
                    <div class="flex flex-wrap">
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                            <label class="label-sizem">Solicitud de Compra No. </label>
                            <label class="label-sizem">{{this.IdSolicitudSeleccionado}}</label>
                        </div>
                        <!---  DATOS SOLICITUD -->
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        </div>
                    </div>

                    <vs-divider></vs-divider>
                    <vs-table2 max-items="10" pagination :data="Lista_detalle_solicitud" noDataText="Sin datos disponibles" search id="tb_departamentos">

                        <template slot="thead">
                            <th>Código </th>
                            <th>Producto</th>
                            <th>Cantidad</th>
                        </template>

                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 :data="data[indextr].id_producto">
                                    {{data[indextr].id_producto}}
                                </vs-td2>

                                <vs-td2 :data="data[indextr].nombre">
                                    {{data[indextr].nombre}}
                                </vs-td2>

                                <vs-td2 :data="data[indextr].cantidad">
                                    {{data[indextr].cantidad}}
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
                </form>
            </div>
        </vs-popup>

        <!--------------- EMERGENTE AUTORIZACION -------------->
        <vs-popup classContent="popup-example" title="Finalizar autorización" :active.sync="Estado_VentanaEmergente_Auto" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">

                <form>
                    <div class="flex flex-wrap">
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">

                        </div>
                        <!---  AUTORIZAR / RECHAZAR SOLICITUD-->
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                            <label class="typo__label">Estado a definir: </label>
                            <multiselect v-model="cb_lista_estado_solicitud" :options="Lista_solicitud_estado" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="estado_solicitud_seleccionada" placeholder="Seleccionar" @input="onChangeEstadoSolicitud">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </div>
                    </div>

                    <!---------- OBSERVACIONES ------------>
                    <div class="w-full md:w-full lg:w-full xl:w-full">
                        <label class="typo__label">Observaciones</label>
                        <div class="vs-con-input">
                            <input type="text" class="vs-inputx vs-input--input large" cols="40" rows="5" v-model="Observaciones">
                        </div>
                    </div>

                    <!---- agregar boton para guardar-->
                    <vs-divider></vs-divider>
                    <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" id="btn_confirmacion" @click="Confirmacion_Transaccion()"> Grabar</vs-button>
                    <vs-divider></vs-divider>
                </form>
            </div>
        </vs-popup>
    </div>
</vx-card>
</template>

<script>
/**
 * @General
 * Modulo permite autorizar o rechazar una solicitud de compra generada para el correlativo que inicio sesión.
 */
import Multiselect from 'vue-multiselect'
import "vue-multiselect/dist/vue-multiselect.min.css"
import moment from "moment";
export default {

    components: {
        Multiselect,

    },
    data() {
        return {
            Ventana_Detalle: false,
            Estado_VentanaEmergente_Auto: false,
            autorizar_solicitud: false,
            cb_lista_operacion: '',
            fecha_inicio: this.getDateValue(new Date()),
            fecha_final: this.getDateValue(new Date()),
            Id_estado_seleccionado: '',
            lista_estado: [{
                    ID: 'T',
                    DESCRIPCION: 'Todos'
                },
                {
                    ID: 'P',
                    DESCRIPCION: 'Proceso'
                },
                {
                    ID: 'A',
                    DESCRIPCION: 'Autorizado'
                },
                {
                    ID: 'R',
                    DESCRIPCION: 'Rechazado'
                }
            ],

            cb_lista_estado_solicitud: '',
            Id_estado_solicitu_seleccionado: '',
            Lista_solicitud_estado: [{
                    ID: 'A',
                    DESCRIPCION: 'Autorizar'
                },
                {
                    ID: 'R',
                    DESCRIPCION: 'Rechazar'
                }
            ],
            Observaciones: '',
            Lista_autorizaciones: [],
            Lista_solicitud_enc: [],
            Lista_detalle_solicitud: [],
            Datos_autorizacion_seleccionado: [],
            Id_responsable_1: '',
            Nombre_resposable_1: '',
            Id_responsable_2: '',
            Nombre_resposable_2: '',
            user: '',
            message: 'prueba mensaje',
            messages: [],
            notificaciones: [],

            IdSolicitudSeleccionado: '',
            ListaDepartamentos: [],
            cbDepartamento: '',
            IdDepartamento: '',
            Departamento: '',

        };
    },
    mounted() {
        this.Id_estado_seleccionado = 'P';
        this.cb_lista_operacion = {
            ID: 'P',
            DESCRIPCION: 'Proceso'
        }
        this.ConsultarDepartamentos();
        this.Consulta_autorizaciones();
    },
    methods: {
        getDateValue(value) {
            if (value === undefined) {
                return '';
            } else if (value === null) {
                return '';
            } else if (value == '') {
                return '';
            }
            return moment(value).format('YYYY-MM-DD');
        },
        Operacion_seleccionada({
            DESCRIPCION
        }) {
            return `${DESCRIPCION} `
        },
        onChangeEstado(value) {
            if (value !== null && value.length !== 0) {
                this.Id_estado_seleccionado = value.ID;
            } else {
                this.Id_estado_seleccionado = '';
            }
        },
        estado_solicitud_seleccionada({
            DESCRIPCION
        }) {
            return `${DESCRIPCION} `
        },
        onChangeEstadoSolicitud(value) {
            if (value !== null && value.length !== 0) {
                this.Id_estado_solicitu_seleccionado = value.ID;
            } else {
                this.Id_estado_solicitu_seleccionado = '';
            }
        },
        ConsultarDepartamentos() {

            this.axios.post('/app/v1_OrdenCompra/consulta_departamento', {})
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.ListaDepartamentos = "";
                    } else {
                        this.ListaDepartamentos = resp.data.json;
                    }
                })
                .catch(() => {})

        },
        onChangeDepartamento(value) {

            if (value !== null && value.length !== 0) {
                this.IdDepartamento = value.CODIGO;
                this.Departamento = value.NOMBRE;

            } else {
                this.IdDepartamento = '';
                this.Departamento = '';
            }
        },
        Departamento_seleccionada({
            CODIGO,
            NOMBRE
        }) {
            return `${CODIGO} - ${NOMBRE} `
        },
        Consulta_autorizaciones() {
            const sesion = this.$store.state.sesion;
            const url = this.$store.state.global.url;
            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/lista_autorizaciones_asignadas', {
                    empresa: sesion.sesion_empresa,
                    estado: this.Id_estado_seleccionado,
                    fecha_inicio: this.fecha_inicio,
                    fecha_fin: this.fecha_final,
                    corporativo: sesion.corporativo,
                    id_departamento: this.IdDepartamento
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Autorización Solicitud',
                            text: resp.data.mensaje,
                        })
                        this.$vs.loading.close();
                        this.Lista_autorizaciones = [];
                    } else {
                        this.Lista_autorizaciones = [];
                        this.Lista_autorizaciones = resp.data.json;
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },
        Consultar_solicitud(id_solicitud) {

            const url = this.$store.state.global.url
            const sesion = this.$store.state.sesion
            sesion.depto_seleccionado

            this.axios.post(url + 'app/v1_OrdenCompra/consultar_solicitud_compra', {
                    empresa: sesion.sesion_empresa,
                    id_departamento: '0',
                    id_bodega: '0',
                    tipo_consulta: 'E',
                    estado: '',
                    codigo: id_solicitud
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Autorización Solicitud',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.Lista_solicitud_enc = [];
                    } else {
                        this.Lista_solicitud_enc = resp.data.json;
                        this.Generar_detalle(this.Lista_solicitud_enc);
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();

        },
        async Generar_detalle(datos) {

            const url = this.$store.state.global.url
            this.Lista_detalle_solicitud = [];
            this.IdSolicitudSeleccionado = datos.find(obj => obj.CODIGO != '').CODIGO;
           
            this.axios.post(url + 'app/v1_OrdenCompra/consultar_solicitud_compra_det', {
                    id_empresa: datos.find(obj => obj.EMPRESA != '').EMPRESA,
                    id_solicitud_enc: datos.find(obj => obj.CODIGO != '').CODIGO,
                    tipo_transaccion: 'T'
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Autorización Solicitud',
                            text: resp.data.mensaje,
                        })

                    } else {
                        this.Lista_detalle_solicitud = resp.data.json;
                        this.Ventana_Detalle = true
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();

        },
        async Consultar_Responsbles(num_documento) {

            var id_depto = 0; 

            const url = this.$store.state.global.url
            const sesion = this.$store.state.sesion
            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/consulta_listado_autorizaciones', {
                    empresa: sesion.sesion_empresa,
                    id_documento: '1',
                    num_documento: num_documento,
                    tipo_consulta: 'E',
                    id_departamento: id_depto
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Autorización Solicitud',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                    } else {
                        if (num_documento === '1') {
                            this.Id_responsable_1 = '';
                            this.Nombre_resposable_1 = '';
                            this.Id_responsable_1 = resp.data.json.find(obj => obj.ID_DOCUMENTO === '1').ID_RESPONSABLE;
                            this.Nombre_resposable_1 = resp.data.json.find(obj => obj.ID_DOCUMENTO === '1').NOMBRE;
                        } else if (num_documento === '2') {
                            this.Id_responsable_2 = '';
                            this.Nombre_resposable_2 = '';
                            this.Id_responsable_2 = resp.data.json.find(obj => obj.ID_DOCUMENTO === '1').ID_RESPONSABLE;
                            this.Nombre_resposable_2 = resp.data.json.find(obj => obj.ID_DOCUMENTO === '1').NOMBRE;
                        }

                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },
        Finalizar_Autorizacion(datos) {
            this.Id_estado_solicitu_seleccionado = '';
            this.cb_lista_estado_solicitud = '';

            this.Observaciones = '';

            this.Datos_autorizacion_seleccionado = [];

            if (datos.ESTADO == 'P') {
                this.Datos_autorizacion_seleccionado = datos;
                this.Estado_VentanaEmergente_Auto = true;
            } else {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Autorización Solicitud',
                    text: 'Autorizacion solicitud finalizada.',
                })

            }
        },
        Confirmacion_Transaccion() {
            /**
             * @General
             * Función Permite  actualizar un registro;
             */

            if (this.Id_estado_solicitu_seleccionado == '' || this.Id_estado_solicitu_seleccionado == null) {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Autorización Solicitud',
                    text: 'Seleccionar Autorización / Rechazo',
                })
                return;
            }

            const sesion = this.$store.state.sesion
            this.Corporativo_Sesion = sesion.corporativo
            const url = this.$store.state.global.url
            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/finalizar_autorizacion', {
                    empresa: sesion.sesion_empresa,
                    corporativo: sesion.corporativo,
                    id_historial_autorizacion: this.Datos_autorizacion_seleccionado.CODIGO,
                    id_solicitud_enc: this.Datos_autorizacion_seleccionado.ID_SOLICITUD_ENC,
                    estado: this.Id_estado_solicitu_seleccionado,
                    id_documento: this.Datos_autorizacion_seleccionado.ID_DOCUMENTO,
                    num_documento: this.Datos_autorizacion_seleccionado.NUM_DOCUMENTO,
                    observaciones: this.Observaciones,
                    id_cotizacion_enc: 0,
                    id_orden_compra_enc: 0
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Autorización Solicitud',
                            text: resp.data.mensaje,
                        })
                        this.$vs.loading.close();
                    } else {

                        this.Estado_VentanaEmergente_Auto = false;
                        this.Consulta_autorizaciones();
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();

        },
        alertDisplay() {
            this.$swal('Hello Vue world!!!');
        }

    },
    computed: {

        sesion() {
            return this.$store.state.sesion
        },
    }
}
</script>

<style scoped>
.footer {
    height: 50px;
    margin-top: -65px;
    margin-left: 10px;
}

.label-sizem {
    font-size: 16px;
    font-weight: bold;
}
</style>
