<template>
    <div class="container">
        <!-- <vx-card title="Direcciones"> -->


        <ValidationObserver ref="adressesData" v-slot="{ invalid, handleSubmit }" mode="lazy">
            <form @submit.prevent="handleSubmit(submitForm(submit))">


                <div class="tab-text">

                    <div class="container_div">

                        <div class="container">

                            <vs-row>
                                <vs-col vs-type="flex" vs-w="10">
                                    <div class="w-full ml-2">
                                        <h6>Dirección de residencia</h6>
                                        <hr
                                            style="height: 8px; border: 0; box-shadow: inset 0 12px 12px -12px rgba(0, 0, 0, 0.5)">

                                    </div>
                                </vs-col>
                            </vs-row>

                            <vs-row>
                                <vs-col vs-type="flex" vs-w="4">
                                    <div class="w-2/3">
                                        <validationProvider name="CalleResidencia" class="required"
                                            rules="required|numero_min:0" v-slot="{ errors }" >
                                            <vs-input label="CalleResidencia" class="w-full p-2" type="number"
                                                v-model.number="PacienteData.CalleResidencia" 
                                                :maxlength="2"
                                                :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null" />
                                        </validationProvider>
                                    </div>
                                </vs-col>

                                <vs-col vs-type="flex" vs-w="4">
                                    <div class="w-2/3">
                                        <validationProvider name="Avenida/Manzana Residencia" class="required"
                                            rules="required|numero_min:0" v-slot="{ errors }">
                                            <vs-input label="Avenida/Manzana Residencia" class="w-full p-2" type="alpha_spaces"
                                                v-model="PacienteData.AvenidaResidencia" :minlength="1" :maxlength="20"
                                                :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null" />
                                        </validationProvider>
                                    </div>
                                </vs-col>

                                <vs-col vs-type="flex" vs-w="4">
                                    <div class="w-2/3">
                                        <validationProvider name="No. Casa/Apto. Residencia" class="required"
                                            rules="required|min:4" v-slot="{ errors }">
                                            <vs-input label="No. Casa/Apto. Residencia" class="w-full p-2"
                                                v-model.trim="PacienteData.NoCasaResidencia" :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null" />
                                        </validationProvider>
                                    </div>
                                </vs-col>
                            </vs-row>


                            <vs-row>
                                <vs-col vs-type="flex" vs-w="4">
                                    <div class="w-2/3">
                                        <validationProvider name="Zona Residencia" class="required" rules="required|min:1"
                                            v-slot="{ errors }">
                                            <vs-input label="Zona Residencia" class="w-full p-2" type="number"
                                                v-model.trim="PacienteData.ZonaResidencia" 
                                                :maxlength="2"
                                                :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null" />
                                        </validationProvider>
                                    </div>
                                </vs-col>

                                <vs-col vs-type="flex" vs-w="4">
                                    <div class="w-2/3">
                                        <validationProvider name="colonia Residencia">
                                            <vs-input label="Colonia Residencia" class="w-full p-2"
                                                v-model="PacienteData.ColoniaResidencia" />
                                        </validationProvider>
                                    </div>
                                </vs-col>

                                <vs-col vs-type="flex" vs-w="4">
                                    <div class="w-2/3">
                                        <validationProvider name="Edificio Residencia">
                                            <vs-input label="Edificio" class="w-full p-2"
                                                v-model="PacienteData.EdificioResidencia" />
                                        </validationProvider>
                                    </div>
                                </vs-col>
                            </vs-row>

                            <vs-row class="pt-4">

                                <vs-col vs-type="flex" vs-w="4" class="ml-2">
                                    <div class="w-5/6">
                                        <!-- <vs-input v-model="PacienteData.DeptoResidencia" class="w-full p-2" /> -->
                                        <multiselect
                                          v-model="selDepartamento"
                                          :options="ListaDepartamentos"
                                          @input="onChangeDepartamento"
                                          :searchable="true"
                                          :close-on-select="true"
                                          :show-labels="false"
                                          :custom-label="DepartamentoSeleccionado"
                                          label="Departamento Dirección"
                                          placeholder="Departamento">
                                          <span
                                          slot="noOptions">Lista no disponible.</span>
                                          </multiselect>

                                    </div>
                                </vs-col>

                                <vs-col vs-type="flex" vs-w="4">
                                    <div class="w-2/3">
                                        <!-- <vs-input v-model="PacienteData.MunicipioResidencia" class="w-full p-2" /> -->
                                        <multiselect
                                          v-model="selMunicipio"
                                          :options="ListaMunicipios"
                                          :searchable="true"
                                          :close-on-select="true"
                                          :show-labels="false"
                                          :custom-label="MunicipioSeleccionado"
                                          label="Municipio Dirección"
                                          placeholder="Municipio">
                                          <span
                                          slot="noOptions">Lista
                                          no
                                          disponible.</span>
                                          </multiselect>


                                    </div>
                                </vs-col>
                            </vs-row>

                            <vs-row>
                                <vs-col vs-type="flex" vs-w="10">
                                    <div class="w-full  p-2 ml-2">
                                        <h6>Dirección de Trabajo</h6>
                                        <hr
                                            style="height: 8px; border: 0; box-shadow: inset 0 12px 12px -12px rgba(0, 0, 0, 0.5)">
                                    </div>
                                </vs-col>
                            </vs-row>

                            <vs-row>
                                <vs-col vs-type="flex" vs-w="4">
                                    <div class="w-2/3">
                                        <validationProvider name="CalleLaboral" 
                                        rules="greaterThanZero" v-slot="{ errors }" >
                                            <vs-input label="CalleDirLaboral Dir. Laboral" 
                                                class="w-full p-2" type="number"
                                                v-model.number="PacienteData.CalleTrabajo" 
                                                :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null"/>
                                        </validationProvider>
                                    </div>
                                </vs-col>

                                <vs-col vs-type="flex" vs-w="4">
                                    <div class="w-2/3">
                                        <validationProvider name="Avenida/ManzanaLaboral"
                                        rules="numero_min:1" v-slot="{ errors }" >
                                            <vs-input label="Avenida/Manzana Dir. Laboral" class="w-full p-2" type="alpha_spaces"
                                                v-model.trim="PacienteData.AvenidaTrabajo" :minlength="1" :maxlength="20"
                                                :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null"/>
                                        </validationProvider>
                                    </div>
                                </vs-col>

                                <vs-col vs-type="flex" vs-w="4">
                                    <div class="w-2/3">
                                        <validationProvider name="No. Casa/Apto"
                                        rules="min:4" v-slot="{ errors }" >
                                            <vs-input label="No. Casa/Apto. Dir. Laboral" class="w-full p-2"
                                                v-model.trim="PacienteData.NoCasaTrabajo" 
                                                :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null"/>
                                        </validationProvider>
                                    </div>
                                </vs-col>
                            </vs-row>


                            <vs-row>
                                <vs-col vs-type="flex" vs-w="4">
                                    <div class="w-2/3">
                                        <validationProvider name="ZonaLaboral"
                                        rules="greaterThanZero" v-slot="{ errors }" >
                                            <vs-input label="Zona Dir. Laboral" class="w-full p-2" type="number"
                                                v-model.trim="PacienteData.ZonaTrabajo" 
                                                :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null"/>
                                        </validationProvider>
                                    </div>
                                </vs-col>

                                <vs-col vs-type="flex" vs-w="4">
                                    <div class="w-2/3">
                                        <validationProvider name="coloniaLaboral"
                                        rules="min:4" v-slot="{ errors }" >
                                            <vs-input label="Colonia Dir. Laboral" class="w-full p-2"
                                                v-model="PacienteData.ColoniaTrabajo" 
                                                :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null"/>
                                        </validationProvider>
                                    </div>
                                </vs-col>

                                <vs-col vs-type="flex" vs-w="4">
                                    <div class="w-2/3">
                                        <validationProvider name="EdificioLaboral"
                                          rules="min:4" v-slot="{ errors }" >
                                            <vs-input label="Edificio Dir. Laboral" class="w-full p-2"
                                                v-model="PacienteData.EdificioTrabajo" 
                                                :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null"/>
                                        </validationProvider>
                                    </div>
                                </vs-col>
                            </vs-row>

                            <vs-row>
                                <vs-col vs-type="flex" vs-w="4">
                                    <div class="w-2/3">
                                        <validationProvider name="OficinaLaboral"
                                        rules="alpha_num" v-slot="{ errors }" >
                                            <vs-input label="Oficina Dir. Laboral" class="w-full p-2"
                                                v-model="PacienteData.OficinaTrabajo" 
                                                :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null"/>
                                        </validationProvider>
                                    </div>
                                </vs-col>

                                <vs-col vs-type="flex" vs-w="4">
                                    <div class="w-2/3">
                                        <!-- <validationProvider name="DepartamentoLaboral">
                                                <vs-input label="Departamento Dir. Laboral" class="w-full p-2"
                            v-model.trim="DeptoTrabajo" /> 

                                                <vs-input v-model="PacienteData.DeptoTrabajo" class="w-full p-2"
                                                    type="hidden" />

                                                <SM-Buscar label="Departamento" v-model="PacienteData.DeptoTrabajo"
                                                    api="app/Ajenos/Busqueda_Departamentos" :api_campos="['Departamento']"
                                                    :api_titulos="['Departamento']" api_campo_respuesta="IdDepartamento"
                                                    api_campo_respuesta_mostrar="Departamento"
                                                    :callback_buscar="(e) => { IdDepartamentoLab = e.IdDepartamento }"
                                                    :callback_cancelar="(e) => { IdDepartamentoLab = null }"
                                                    :disabled_texto="true" :disabled_busqueda="bloquear"
                                                    :api_preload="true" />


                                            </validationProvider> -->
                                    </div>
                                </vs-col>

                                <vs-col vs-type="flex" vs-w="4">
                                    <div v-if="IdDepartamentoLab > 0" class="w-2/3  p-2">
                                        <!-- <validationProvider name="MunicipioLaboral" class="required"
                                                rules="required|min:1" v-slot="{ errors }">
                                                <<vs-input label="Municipio Dir. Laboral" class="w-full p-2"
                            v-model.trim="MunicipioTrabajo" /> 


                                                <vs-input v-model="PacienteData.MunicipioTrabajo" type="hidden"
                                                    class="w-full p-2" />

                                                <SM-Buscar label="Municipio" v-model="PacienteData.MunicipioTrabajo"
                                                    api="app/Ajenos/Busqueda_Municipios" :api_campos="['Municipio']"
                                                    :api_titulos="['Municipio']" :api_filtro="{
                                                        Codigo_Departamento: IdDepartamentoLab
                                                    }" api_campo_respuesta="Codigo_Municipio"
                                                    api_campo_respuesta_mostrar="Municipio"
                                                    :callback_buscar="(e) => { IdMunicipioLab = e.IdDepartamento }"
                                                    :callback_cancelar="(e) => { IdMunicipioLab = null }"
                                                    :disabled_texto="true" :disabled_busqueda="bloquear" :api_preload="true"
                                                    :danger="errors.length > 0"
                                                    :dangertext="(errors.length > 0) ? errors[0] : null" />
                                            </validationProvider> -->
                                    </div>
                                </vs-col>
                            </vs-row>

                        </div>

                    </div>

                    <div v-show="showButtons">
                        <vs-button style="margin-right:1px" v-on:click="handleSubmit(submit())" :disabled="invalid">
                            Guardar
                        </vs-button>
                    </div>

                </div>
            </form>
        </ValidationObserver>
        <!-- </vx-card> -->
    </div>
</template>



<script>

import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";

import { mapFields } from "vuex-map-fields";


export default {
    name: "DireccionesPaciente",
    props: ['renderComp'],
    data: () => ({
        //SaludSiempre: "Salud Siempre",
        //PacienteData:{},
        NumeroDocumento: null,
        showButtons: false,
        IdDepartamentoRes: '',
        //TIdDepartamentoRes:'1617',
        IdMunicipioRes: null,
        IdDepartamentoLab: null,
        IdMunicipioLab: null,
        bloquear: false,
        ListaDepartamentos: [],
        ListaMunicipios: [],
        Departamentos: [],
        IdDeptoSeleccionado: '',
        selDepartamento: '',
        selMunicipio: ''
        //cb_departamentos: '',

    }),
    components: {
        Multiselect
    },
    created() {


    },
    computed: {
        ...mapFields('paciente', ["PacienteData"])

    },
    beforeCreate() {
        //this.IdDepartamentoRes = this.PacienteData.DeptoResidencia


    },
    beforeMount() {
        //this.ConsultarDepartamentos()
    },
    mounted() {

        this.ConsultarDepartamentos()
        this.ConsultarMunicipios()        
    },
    watch: {


    },
    updated() {

    },
    activated() {

        if (this.isEmpty(this.PacienteData.CalleTrabajo) && this.isEmpty(this.PacienteData.AvenidaTrabajo)) {
            this.PacienteData.DeptoTrabajo = ''
            this.PacienteData.MunicipioTrabajo = ''
        }
    },
    methods: {

        getionarDatos() {
            return new Promise((resolve) => {
                // Lógica de guardado del cliente aquí
                // Resuelve la promesa si el guardado es exitoso
                return resolve(true);
                // Rechaza la promesa si hay un error en el guardado
                // reject('Mensaje de error aquí');
            });
        },
        validacionFormulario() {
            return new Promise((resolve, reject) => {
                this.$refs.adressesData.validate()
                    .then((isValid) => {
                        if (isValid) {
                            resolve(true);
                        } else {
                            reject('Revisar campos requeridos');
                        }
                    })
                    .catch((error) => {
                        reject(error);
                    });
            });
        },
        gestionarPaciente() {
            return new Promise((resolve, reject) => {
                this.$store.dispatch('paciente/crearPaciente')
                    .then((response) => {
                        resolve(response);
                    })
                    .catch((error) => {
                        reject(error);
                    });
            });
        },

        async submit() {
        
            this.FormateoDirecciones()
            
            this.getionarDatos()
                .then(() => {
                    return this.validacionFormulario();
                })
                .then(() => {                                        
                    return this.gestionarPaciente();
                })
                .then(() => {

                    this.$emit("ready", true);
                })
                .catch((error) => {
                    this.$vs.dialog({
                        type: 'confirm',
                        color: '#ED8C72',
                        acceptText: 'Continuar',
                        title: 'Error en gestión de Paciente',
                        text: error,
                        clientWidth: 100,
                        accept: () => {
                            this.$emit("ready", false);
                            
                        }
                    });
                })

        },
        FormateoDirecciones() {
            if (!this.isEmpty(this.PacienteData.CalleTrabajo) && !this.isEmpty(this.PacienteData.AvenidaTrabajo)) {
                this.PacienteData.DireccionTrabajo = this.DarFormatoDireccion(this.PacienteData.CalleTrabajo
                    , this.PacienteData.AvenidaTrabajo
                    , this.PacienteData.NoCasaTrabajo
                    , this.PacienteData.ZonaTrabajo
                    , this.PacienteData.ColoniaTrabajo
                    , this.PacienteData.EdificioTrabajo)
            }

            if (!this.isEmpty(this.PacienteData.CalleResidencia) && !this.isEmpty(this.PacienteData.AvenidaResidencia)) {
                this.PacienteData.Direccion = this.DarFormatoDireccion(this.PacienteData.CalleResidencia
                    , this.PacienteData.AvenidaResidencia
                    , this.PacienteData.NoCasaResidencia
                    , this.PacienteData.ZonaResidencia
                    , this.PacienteData.ColoniaResidencia
                    , this.PacienteData.EdificioResidencia)
            }

        },
        DarFormatoDireccion(calle, avenida, noCasa, zona, colonia, edificio) {
            let direccion;
            direccion = ""

            if (!this.isEmpty(calle))
                direccion = calle + ' CALLE,'

            if (!this.isEmpty(avenida)){                
                const defAvenida = isNaN(parseInt(avenida)) ? 'AVENIDA' + ' ' +avenida : avenida + ' ' + 'AVENIDA'
                direccion = direccion  + ' '+ defAvenida
            }

            if (!this.isEmpty(noCasa))
                direccion = direccion + ',' + noCasa

            if (!this.isEmpty(edificio))
                direccion = direccion + ', EDIFICIO ' + edificio

            if (!this.isEmpty(colonia))
                direccion = direccion + ',  COLONIA ' + colonia


            if (!this.isEmpty(zona))
                direccion = direccion + ',  ZONA ' + zona

            return direccion;

        },
        isEmpty(value) {
            if (typeof value === "undefined" || value === null || value === "" )
            {                  
                return true;
            }
        },
        isNumber(value) {
            return typeof value === 'number';
        },
        async ConsultarDepartamentos() {
            const resp = await this.axios.post('/app/Ajenos/Busqueda_Departamentos', {
                IdDepartamento: '',
            })
            this.ListaDepartamentos = resp.data.json;

            const tDeptos = resp.data.json.map(m => {
                return {
                    ...m,
                }
            })

            var parsedData = JSON.stringify(tDeptos);
            const jsonData = JSON.parse(parsedData);

            const v_depto = ( this.isEmpty(this.PacienteData.DeptoResidencia) ||  this.PacienteData.DeptoResidencia ===0 )  ? this.PacienteData.DeptoResidencia : '1617';
        
            const val = jsonData.find(x => x.IdDepartamento === v_depto)?.Departamento||''

            this.selDepartamento = {
                Departamento: val
            }

        },
        async obtenerNombreDepto( idDepto){

            const tDeptos = this.ListaDepartamentos.map(m => {
                return {
                    ...m,
                }
            })
            var parsedData = JSON.stringify(tDeptos);
            const jsonData = JSON.parse(parsedData);
            const v_depto = '' + idDepto||'1617'
            const val = jsonData.find(x => x.IdDepartamento === v_depto)?.Departamento

            this.selDepartamento = {
                Departamento: val
            }

        },
        async ConsultarMunicipios() {
            const resp = await this.axios.post('/app/Ajenos/Busqueda_Municipios', {
                Codigo_Departamento:this.selDepartamento||'1617',
                IdMunicipio:''
            })
            this.ListaMunicipios = resp.data.json;

            const tMunc = resp.data.json.map(m => {
                return {
                    ...m,
                }
            })

             var parsedData = JSON.stringify(tMunc);
             const jsonData  = JSON.parse(parsedData);
             const v_munic = ( this.isEmpty(this.PacienteData.MunicipioResidencia) ||  this.PacienteData.MunicipioResidencia ===0 )  ? this.PacienteData.MunicipioResidencia : '74';

            const val = jsonData.find(x => x.Codigo_Municipio === v_munic)?.Municipio

            this.selMunicipio = {
                Municipio: val//'GUATEMALA'
            }

        },
        DepartamentoSeleccionado({
            Departamento
        }) {
            return `${Departamento} `
        },
        MunicipioSeleccionado({
            Municipio
        }) {
            return `${Municipio} `
        },
        onChangeDepartamento(value) {
            if (value !== null && value.length !== 0) {
                this.IdDeptoSeleccionado = value.IdDepartamento;
                
            }

            this.selDepartamento = value.IdDepartamento            
            this.ConsultarMunicipios()
            this.obtenerNombreDepto( this.selDepartamento)

        }


    }

}





</script>

<style scoped>
* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

.container_div {
    display: grid;
    grid-gap: 5px;
    width: 100%;
    height: 100%;

}

.container_div>div {
    border: 1px solid #888;
    padding-top: 10px;
    padding-bottom: 10px;
}

/* hr {
    height: 12px;
    border: 0;
    box-shadow: inset 0 12px 12px -12px rgba(0, 0, 0, 0.5);
} */

.container_b {
    display: grid;
    grid-gap: 10px;
    width: 100%;
    height: 10%;
}

.container {
    max-width: 100%;
}

.radio-group.radio-group--inline div[data-radio-button] {
    width: auto;
}

.radio-group.radio-group--inline div[data-radio-button]:not(:first-of-type) {
    padding-left: var(--space-base);
}

.vx-card .vx-card__collapsible-content .vx-card__body {
    padding: 0.5rem;
}
</style>
