<template>
<vx-card title="Comparativo / Autorización Cotización">
    <div class="content content-pagex">
        <!----------------------- Encabezado cotización ---------->
        <div class="flex flex-wrap">
            <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">
                <div class="flex flex-wrap">
                    <div style="margin-left:10px;margin-right:20px" class="xs:w-full md:w-5/6 lg:w-5/6 xl:w-5/6">
                        <ValidationProvider name="Agencia">
                            <label class="typo__label">Departamentos</label>
                            <multiselect v-model="cbDepartamento" :options="ListaDepartamentos" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Departamento_seleccionada" placeholder="Seleccionar" @input="onChangeDepartamento">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>

                </div>

            </div>
            <div style="margin-right: 20px" class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <label class="typo__label">Estado</label>
                <multiselect v-model="cb_lista_operacion" :options="lista_estado" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Operacion_seleccionada" placeholder="Seleccionar" @input="onChangeEstado">
                    <span slot="noOptions">Lista no disponible.</span>
                </multiselect>
            </div>

            <div style="margin-left:10px;margin-right:20px" class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7">
                <label class="typo__label">Fecha Inicio:</label>
                <vs-input type="date" v-model="fecha_inicio" name="date1" />
            </div>

            <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7">
                <label class="typo__label">Fecha Final:</label>
                <vs-input type="date" v-model="fecha_final" name="date1" />
            </div>

            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <vs-button style="float:left;margin: 20px" color="primary" type="filled" icon-pack="fas" icon="fa-search" @click="Consultar_Autorizacion_cotizacion()"> Búsqueda</vs-button>
            </div>
        </div>

        <!----------------------- ********* INICIO Cotizaciones Internas generadas****---------->
        <vs-table2 max-items="10" pagination :data="Lista_cotizaciones_internas" tooltip>

            <template slot="thead">
                <th width = "100">Nº. Solicitud</th>
                <th width = "300">Departamento</th>
                <th width = "30">Tipo</th>
                <th width = "500">Observación de Solicitud</th>
                <th width = "100">Fecha solicitud</th>
                <th width = "200">Usuario</th>
                <th width = "110">Estado</th>
                <th width = "20">Comparativo</th>
            </template>

            <template slot-scope="{data}">

                <tr :key="indextr" v-for="(tr, indextr) in data" @click="SeleccionarCotizacionInterna(tr)">

                    <vs-td2 :data="data[indextr].IDSOLICITUDENCFK">
                        {{data[indextr].IDSOLICITUDENCFK}}
                    </vs-td2>

                    <vs-td2 :data="data[indextr].DESCRIPCION_DEPTO">
                        {{data[indextr].IDDEPARTAMENTOFK}}
                        -
                        {{data[indextr].DESCRIPCION_DEPTO}}
                    </vs-td2>

                    <vs-td2 :data="data[indextr].TIPO">
                        {{data[indextr].TIPO}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].OBSERVACIONES_S">
                        {{data[indextr].OBSERVACIONES_S}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].FECHA_CREACION_ORDEN">
                        {{data[indextr].FECHA_CREACION_ORDEN}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].USUARIO">
                        {{data[indextr].USUARIO}}-{{data[indextr].NOMBRE }}
                    </vs-td2>
                   
                    <!---<vs-td2 :data="data[indextr].DESCRIPCION_ESTADO">
                            {{data[indextr].DESCRIPCION_ESTADO}}
                        </vs-td2>-->
                    <vs-td2 style="text-align: center;" :data="data[indextr].ESTADO">
                        <label v-if="data[indextr].ESTADO == 'P'" style="height: 30px;background-color:#B2BABB;width:150px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                        <label v-if="data[indextr].ESTADO == 'A'" style=" height: 30px;background-color:green;width:150px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                        <label v-if="data[indextr].ESTADO == 'R'" style="height: 30px;background-color:#C82B0A;width:150px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                    </vs-td2>
                    <vs-td2 align="center">
                        <vs-button v-if="data[indextr].ESTADO == 'P'" color="success" icon-pack="fa" icon="fa-users" @click="Abrir_ventana_comparativo_det(data[indextr])" class="mr-1" style="display:inline-block">
                        </vs-button>
                        <vs-button v-else color="#B2BABB" icon-pack="fa" icon="fa-users" class="mr-1" style="display:inline-block">
                        </vs-button>
                        <!-- <vs-button color="success" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-download" @click="GenerarExcel(data[indextr])"></vs-button> -->
                    </vs-td2>
                </tr>
            </template>
        </vs-table2>
        <!----------------------- ********* FINAL Cotizaciones Internas generadas****---------->

        <div class="flex flex-wrap">
            <!-- <div class="w-full md:w-full lg:wful xl:w-fulll"> -->
            <div style="margin-right: 20px" class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <div style="font-family: arial;font-size: 18px;    padding: 27px 0;color: black;  margin-right: 30px;">Cotizaciones Asociadas</div>
            </div>  
        </div>
        <div class="flex flex-wrap">
            <div style="margin: 15px" class="flex flex-wrap">
                <div class="w-full md:w-1/9 lg:w-1/9 xl:w-1/9, label-sizem">
                    <div style="border-radius:5px;padding:5px;font-size:15px;background-color:white;color:black">
                        Solicitud No.: {{CotExt_Id_Solicitud}}
                        <br>
                        Departamento Solicitante: {{CotExt_Id_Departamento}}
                    </div>
                </div>
            </div>

        </div>
        <!----------------------- Inicio Cotizaciones Externas---------->
        <div>
            <vs-divider></vs-divider>
            <vs-table2 max-items="10" pagination :data="Lista_cotizaciones_externas" tooltip>

                <template slot="thead">
                    <th width = "100">Nº. Cotización</th>
                    <th width = "100">NIT</th>
                    <th width = "300">Proveedor</th>
                    <th width = "100">Monto Total</th>
                    <th width = "500">Observaciones</th>
                    <th width="200">Usuario</th>
                    <!-- <th></th> -->
                </template>

                <template slot-scope="{data}">

                    <tr :key="indextr" v-for="(tr, indextr) in data">

                        <vs-td2 :data="data[indextr].IDCOTIZACIONENC">
                            {{data[indextr].IDCOTIZACIONENC}}
                        </vs-td2>

                        <vs-td2 :data="data[indextr].PROV_NIT">
                            {{data[indextr].PROV_NIT}}
                        </vs-td2>

                        <vs-td2 :data="data[indextr].DESCRIPCION_PROVEEDOR">
                            {{data[indextr].DESCRIPCION_PROVEEDOR}}
                        </vs-td2>

                        <vs-td2 :data="data[indextr].TOTAL_REP">
                            {{data[indextr].TOTAL_REP}}
                        </vs-td2>
                        <vs-td2 :data="data[indextr].OBSERVACIONES">
                            {{data[indextr].OBSERVACIONES}}
                        </vs-td2>
                        <vs-td2 :data="data[indextr].USUARIO">
                            {{data[indextr].USUARIO}} - {{data[indextr].NOMBRES}}
                        </vs-td2>
                        
                        <!---
                            <vs-td style="text-align: center;" :data="data[indextr].ESTADO">
                                <label v-if="data[indextr].ESTADO == 'P'" style="height: 30px;background-color:#B2BABB;width:150px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                                <label v-if="data[indextr].ESTADO == 'A'" style=" height: 30px;background-color:green;width:150px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                                <label v-if="data[indextr].ESTADO == 'R'" style="height: 30px;background-color:#C82B0A;width:150px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                            </vs-td>--->
                        <!-- <vs-td2 align="right"> -->
                        <!---<vs-button :color="data[indextr].COLOR_BOTON_EDITAR" icon-pack="feather" icon="icon-edit-1"  @click="Abrir_Ventana_Emergente_Editar(data[indextr]) " class="mr-1" style="display:inline-block"></vs-button>--->

                        <!---                            <vs-button :color="data[indextr].COLOR_BOTON_EDITAR" icon-pack="fas" icon="fa-check-double" @click="Finalizar_Autorizacion(data[indextr])" class="mr-1" style="display:inline-block"></vs-button>-->
                        <!---<vs-button v-if="data[indextr].ESTADO == 'P'" color="success" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-download" @click="GenerarCotizacion(data[indextr])"></vs-button>---->
                        <!-- <vs-button color="success" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-download" @click="GenerarCotizacion(data[indextr])"></vs-button>

                            </vs-td2> -->

                    </tr>
                </template>
            </vs-table2>

        </div>
        <!----------------------- Finalizadas Cotizaciones Externas---------->
        <!-------------------------EXPORTAR FILTROS Cotizacion_interna_selec ---------------->

        <vs-divider></vs-divider>
        <!-- <download-excel style="visibility:hidden" :data="ListaComparativoDetalle" :fields="json_fields" :header="DatosEncabezado" :footer="DatosFinalizacion" name="Comparativo.xls" worksheet="Historial">
                <vs-button id="btn_descarga" color="primary" style="float:right;margin: 5px" type="filled" icon-pack="far" icon="fa-file-excel"> Descargar</vs-button>
            </download-excel> -->
        <vs-divider></vs-divider>

        <!----------- EMERGENTE AUTORIZAR O RECHAZAR COTIZACION------->
        <vs-popup classContent="popup-example" title="Autorizar cotización." :active.sync="Estado_VentanaEmergente_Auto" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
            <div style="padding:5px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">

                <form>
                    <div class="flex flex-wrap">
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">

                        </div>
                        <!---  AUTORIZAR / RECHAZAR SOLICITUD-->
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                            <label class="typo__label">Estado a definir: </label>
                            <multiselect v-model="cb_lista_estado_solicitud" :options="Lista_solicitud_estado" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="estado_solicitud_seleccionada" placeholder="Seleccionar" @input="onChangeEstadoSolicitud">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </div>
                    </div>

                    <!---------- OBSERVACIONES ------------>
                    <div class="w-full md:w-full lg:w-full xl:w-full">
                        <label class="typo__label">Observaciones</label>
                        <div class="vs-con-input">
                            <input type="text" class="vs-inputx vs-input--input large" cols="40" rows="5" v-model="Observaciones">
                        </div>
                    </div>

                    <!---- agregar boton para guardar-->
                    <vs-divider></vs-divider>
                    <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" id="btn_confirmacion" @click="Confirmacion_Transaccion()"> Grabar</vs-button>
                    <vs-divider></vs-divider>
                </form>
            </div>
        </vs-popup>

        <!----------- EMERGENTE EDITAR COMPARATIVO------->
        <vs-popup classContent="popup-example" title="Editar comparativo." :active.sync="Estado_VentanaEmergente_Comparativo" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:5px">

                <form>
                    <div class="titulo-disenio">
                        <label>COMPARATIVO COTIZACIONES</label>
                    </div>
                    <br><br>
                    <div class="espacio">
                    </div>
                    <div v-for="(field, index) in Campos_Encabezado" :key="field.Nombre">
                        <label style="font-family: 'Barmeno';font-weight: 200;font-size: 15px;" :for="field.Nombre">PROV. {{(index+1)}} {{field.Nombre}}</label>
                    </div>

                    <vs-divider></vs-divider>
                    <div v-if=" Campos_Encabezado.length ===1">

                        <vs-table2 max-items="10" pagination :data="ListaComparativoDetalle" tooltip id="tb_lista_comparativo_1">

                            <template slot="thead">

                                <th width="850px">Producto</th>
                                <th>Cant. Solicitada</th>
                                <th> Prov. 1 Precio/Unitario</th>
                                <th> Seleccionar</th>

                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr1" v-for="(tr, indextr1) in data">
                                    <vs-td2 width="850px" :data="data[indextr1].PRODUCTO">
                                        {{data[indextr1].PRODUCTO}}
                                    </vs-td2>

                                    <vs-td2 :data="data[indextr1].SOLICITADO">
                                        {{parseInt(data[indextr1].SOLICITADO)}}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr1].PRECIO_UNITARIO_P1">
                                        {{data[indextr1].PRECIO_UNITARIO_P1}}
                                    </vs-td2>
                                    <vs-switch style="margin-top:10px;margin-left:20px" :disabled="data[indextr1].EDITABLE_P1" :key="data[indextr1].ID_DET_COMPARATIVO_P1" v-model="data[indextr1].SELECCIONADO_P1" @click="Comparativo_DetalleCambio( data[indextr1].ID_DET_COMPARATIVO_P1, data[indextr1].SELECCIONADO_P1 )" />

                                </tr>
                            </template>
                        </vs-table2>
                        <!---<table id="miTabla">
                            </table>--->
                    </div>

                    <div v-else-if=" Campos_Encabezado.length ===2">

                        <vs-table2 max-items="10" pagination :data="ListaComparativoDetalle" tooltip id="tb_lista_comparativo_2">

                            <template slot="thead">
                                <th width="850px">Producto</th>
                                <th>Cant. Solicitada</th>
                                <th> Prov. 1 Precio/Unitario</th>
                                <th> Seleccionar</th>
                                <th> Prov. 2 Precio/Unitario</th>
                                <th> Seleccionar</th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr3" v-for="(tr, indextr3) in data">
    
                                    <vs-td2 width="850px" :data="data[indextr3].PRODUCTO">
                                        {{data[indextr3].PRODUCTO}}
                                    </vs-td2>

                                    <vs-td2 :data="data[indextr3].SOLICITADO">
                                        {{data[indextr3].SOLICITADO}}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr3].PRECIO_UNITARIO_P1">
                                        {{data[indextr3].PRECIO_UNITARIO_P1}}
                                    </vs-td2>
                                    <vs-switch style="margin-top:10px;margin-left:20px" :disabled="data[indextr3].EDITABLE_P1" :key="data[indextr3].ID_DET_COMPARATIVO_P1" v-model="data[indextr3].SELECCIONADO_P1" @click="Comparativo_DetalleCambio( data[indextr3].ID_DET_COMPARATIVO_P1, data[indextr3].SELECCIONADO_P1 )" />
                                    <vs-td2 :data="data[indextr3].PRECIO_UNITARIO_P2">
                                        {{data[indextr3].PRECIO_UNITARIO_P2}}
                                    </vs-td2>
                                    <vs-switch style="margin-top:10px;margin-left:20px" :disabled="data[indextr3].EDITABLE_P2" :key="data[indextr3].ID_DET_COMPARATIVO_P2" v-model="data[indextr3].SELECCIONADO_P2" @click="Comparativo_DetalleCambio(   data[indextr3].ID_DET_COMPARATIVO_P2, data[indextr3].SELECCIONADO_P2 )" />

                                </tr>
                            </template>
                        </vs-table2>
                    </div>

                    <div v-else-if=" Campos_Encabezado.length ===3">

                        <vs-table2 max-items="10" pagination :data="ListaComparativoDetalle" tooltip id="tb_lista_comparativo_3">

                            <template slot="thead">
                                <th width="850px">Producto -</th>
                                <th>Cant. Solicitada</th>
                                <th> Prov. 1 Precio/Unitario</th>
                                <th> Seleccionar</th>
                                <th> Prov. 2 Precio/Unitario</th>
                                <th> Seleccionar</th>
                                <th> Prov. 3 Precio/Unitario</th>
                                <th> Seleccionar</th>

                            </template>
                            <template slot-scope="{data}">

                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2 width="850px" :data="data[indextr].PRODUCTO">
                                        {{data[indextr].PRODUCTO}}
                                    </vs-td2>

                                    <vs-td2 :data="data[indextr].SOLICITADO">
                                        {{data[indextr].SOLICITADO}}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].PRECIO_UNITARIO_P1">
                                        {{data[indextr].PRECIO_UNITARIO_P1}}
                                    </vs-td2>
                                    <vs-switch style="margin-top:10px;margin-left:20px" :disabled="data[indextr].EDITABLE_P1" :key="data[indextr].ID_DET_COMPARATIVO_P1" v-model="data[indextr].SELECCIONADO_P1" @click="Comparativo_DetalleCambio( data[indextr].ID_DET_COMPARATIVO_P1, data[indextr].SELECCIONADO_P1 )" />
                                    <vs-td2 :data="data[indextr].PRECIO_UNITARIO_P2">
                                        {{data[indextr].PRECIO_UNITARIO_P2}}
                                    </vs-td2>
                                    <vs-switch style="margin-top:10px;margin-left:20px" :disabled="data[indextr].EDITABLE_P2" :key="data[indextr].ID_DET_COMPARATIVO_P2" v-model="data[indextr].SELECCIONADO_P2" @click="Comparativo_DetalleCambio(   data[indextr].ID_DET_COMPARATIVO_P2, data[indextr].SELECCIONADO_P2 )" />
                                    <vs-td2 :data="data[indextr].PRECIO_UNITARIO_P3">
                                        {{data[indextr].PRECIO_UNITARIO_P3}}
                                    </vs-td2>
                                    <vs-switch style="margin-top:10px;margin-left:20px" :disabled="data[indextr].EDITABLE_P3" :key="data[indextr].ID_DET_COMPARATIVO_P3" v-model="data[indextr].SELECCIONADO_P3" @click="Comparativo_DetalleCambio(   data[indextr].ID_DET_COMPARATIVO_P3, data[indextr].SELECCIONADO_P3 )" />
                                </tr>
                            </template>
                        </vs-table2>
                    </div>

                    <!---------- OBSERVACIONES ------------>
                    <div class="w-full md:w-full lg:w-full xl:w-full">
                        <label class="typo__label">Observaciones</label>
                        <div class="vs-con-input">
                            <input type="text" class="vs-inputx vs-input--input large" cols="40" rows="5" v-model="Observaciones">
                        </div>
                    </div>

                    <!---- agregar boton para guardar-->
                    <vs-divider></vs-divider>
                    <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" id="btn_confirmacion" @click="Confirmacion_Transaccion()"> Finalizar</vs-button>
                    <vs-divider></vs-divider>

                </form>
            </div>
        </vs-popup>

    </div>

    <componenteComparativoDet v-if="Estado_VentanaComparativoDetalle" ref="componentecomparativodetalle" :callback="Abrir_ventana_comparativo_det" :cerrar="()=>Estado_VentanaComparativoDetalle=false" />
    <!--- <componenteHistorialCostoProducto v-if="Estado_VentanaHistorialCosto" ref="componenteHistorialCosto" :callback="Abrir_Ventana_Historial_Costo_Producto" :cerrar="()=>Estado_VentanaHistorialCosto=false" />  --->
</vx-card>
</template>

<script>

import Multiselect from 'vue-multiselect'

import "vue-multiselect/dist/vue-multiselect.min.css"
import moment from 'moment';

const axios = require('axios');


export default {
    name: 'ComponenteVistaComparativo',
    created() {
        this.$root.$refs.AX = this;
    },
    components: {
        'componenteComparativoDet': () => import('./INV017_Comparativo'),
        Multiselect,
    },
    data() {
        return {
            Estado_VentanaComparativoDetalle: false,
            cb_lista_operacion: '',
            Id_estado_seleccionado: 0,
            lista_estado: [{
                    ID: 'P',
                    DESCRIPCION: 'Proceso'
                },
                {
                    ID: 'A',
                    DESCRIPCION: 'Autorizado'
                },
                {
                    ID: 'R',
                    DESCRIPCION: 'Rechazado'
                }
            ],

            fecha_inicio: this.getDateValue(new Date()),
            fecha_final: this.getDateValue(new Date()),
            Cotizacion_interna_selec: [],
            Lista_cotizaciones_internas: [],
            Lista_cotizaciones_externas: [],
            CotExt_Id_Cotizacion: 0,
            ListaComparativoDetalle: [],
            Campos_Encabezado: [],
            DatosEncabezado: [],
            json_fields_aux: [],
            json_fields: {
                'No.': 'ID',
                'CODIGO': 'CODIGO',
                'PRODUCTO': 'PRODUCTO',
                'CANT. SOLICITADA': 'SOLICITADO',
            },
            DatosFinalizacion: [],

            Observaciones: '',
            Id_estado_solicitu_seleccionado: '',
            cb_lista_estado_solicitud: '',
            Datos_autorizacion_seleccionado: [],
            Lista_solicitud_estado: [{
                    ID: 'A',
                    DESCRIPCION: 'Autorizar'
                },
                {
                    ID: 'R',
                    DESCRIPCION: 'Rechazar'
                }
            ],
            Estado_VentanaEmergente_Auto: false,
            user: '',
            message: 'prueba mensaje',
            messages: [],
            notificaciones: [],
            Estado_VentanaEmergente_Comparativo: false,
            CantProveedores: 1,
            IdComparativoEnc: 0,
            ListaDepartamentos: [],
            cbDepartamento: '',
            IdDepartamento: '',
            Departamento: '',
            CotExt_Id_Solicitud: 0,
            CotExt_Id_Departamento: '',

        }
    },
    mounted() {
        this.Id_estado_seleccionado = 'P';
        this.cb_lista_operacion = {
            ID: 'P',
            DESCRIPCION: 'Proceso'
        }
        this.Consultar_Autorizacion_cotizacion();
        this.ConsultarDepartamentos();
        this.CotExt_Id_Departamento = ''
        this.CotExt_Id_Solicitud = 0

    },
    methods: {
        getDateValue(value) {
            if (value === undefined) {
                return '';
            } else if (value === null) {
                return '';
            } else if (value == '') {
                return '';
            }
            return moment(value).format('YYYY-MM-DD');
        },
        ConsultarDepartamentos() {

            this.axios.post('/app/v1_OrdenCompra/consulta_departamento', {})
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.ListaDepartamentos = "";
                    } else {
                        this.ListaDepartamentos = resp.data.json;
                    }
                })
                .catch(() => {})

        },
        onChangeDepartamento(value) {

            if (value !== null && value.length !== 0) {
                this.IdDepartamento = value.CODIGO;
                this.Departamento = value.NOMBRE;
            } else {
                this.IdDepartamento = '';
                this.Departamento = '';
            }
        },
        Departamento_seleccionada({
            CODIGO,
            NOMBRE
        }) {
            return `${CODIGO} - ${NOMBRE} `
        },
        Consultar_DetalleComparativo: async function (datos) {
            var detalle = [];
            const sesion = this.$store.state.sesion
            await axios.post('/app/v1_OrdenCompra/comparativo_detalle', {
                    IdSolicitud: datos.IDSOLICITUDENCFK,
                    Corporativo: sesion.corporativo,
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Comparativo',
                            text: resp.data.mensaje,
                        })
                        detalle = []
                    } else {
                        detalle = resp.data.json;
                    }
                })
                .catch(() => {
                    detalle = []
                })
            return detalle;
        },
        Consultar_EncabezadoComparativo: async function (datos) {

            await axios.post('/app/v1_OrdenCompra/comparativo_encabezado', {
                    IdSolicitud: datos.IDSOLICITUDENCFK
                })
                .then(resp => {
                    datos = [];
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Comparativo',
                            text: resp.data.mensaje,
                        })
                    } else {
                        datos = resp.data.json;
                    }
                })
                .catch(() => {})
            return datos;
        },

        async Abrir_ventana_comparativo_det(datos) {
            this.Campos_Encabezado = [];
            this.Campos_Encabezado = await this.Consultar_EncabezadoComparativo(datos);
            this.ListaComparativoDetalle = await this.Consultar_DetalleComparativo(datos);
            this.Estado_VentanaComparativoDetalle = true;
            setTimeout(() => {
                this.$refs.componentecomparativodetalle.Consultar_autorizaciones(this.Campos_Encabezado, this.ListaComparativoDetalle, datos);
            }, 100)

        },

        Comparativo_DetalleCambio(id, estado) {

            if (estado) {
                estado = 0;
            } else {
                estado = 1;
            }

            const sesion = this.$store.state.sesion
            const url = this.$store.state.global.url

            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/Seleccionar_Comparativo_detalle', {
                    id_detalle_comparativo: id,
                    estado: estado,
                    Corporativo: sesion.corporativo
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Comparativo',
                            text: resp.data.mensaje,
                        })
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();

        },
        estado_solicitud_seleccionada({
            DESCRIPCION
        }) {
            return `${DESCRIPCION} `
        },
        onChangeEstadoSolicitud(value) {
            if (value !== null && value.length !== 0) {
                this.Id_estado_solicitu_seleccionado = value.ID;
            } else {
                this.Id_estado_solicitu_seleccionado = '';
            }
        },

        Consultar_autorizaciones(datos) {

            window.onload = function what() {
                document.getElementById('tb_lista_comparativo_1').innerHTML = '';
            };
            window.onload = function what() {
                document.getElementById('tb_lista_comparativo_2').innerHTML = '';
            };
            window.onload = function what() {
                document.getElementById('tb_lista_comparativo_3').innerHTML = '';
            };
            const url = this.$store.state.global.url;
            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/comparativo_encabezado', {
                    IdSolicitud: datos.IDSOLICITUDENCFK
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Comparativo',
                            text: resp.data.mensaje,
                        })

                        this.Campos_Encabezado = [];
                    } else {
                        this.Campos_Encabezado = [];
                        this.$vs.loading.close();
                        this.Campos_Encabezado = resp.data.json;
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();

            setTimeout(() => {
                this.Consultar_GenerarComparativo(datos)
            }, 2000)
        },
        async GenerarExcel(datos) {

            const url = this.$store.state.global.url;
            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/comparativo_encabezado', {
                    IdSolicitud: datos.IDSOLICITUDENCFK
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Comparativo',
                            text: resp.data.mensaje,
                        })

                        this.Campos_Encabezado = [];
                    } else {
                        this.Campos_Encabezado = resp.data.json;
                        var result = this.Campos_Encabezado.find(obj => obj.CantidadProveedores != '').CantidadProveedores;

                        this.AgregarEncabezado(result, datos, this.Campos_Encabezado);

                        /*********** FIN ENCABEZADO ********** */
                        this.$vs.loading();
                        this.axios.post(url + 'app/v1_OrdenCompra/comparativo_detalleExcel', {
                                IdSolicitud: datos.IDSOLICITUDENCFK
                            })
                            .then(resp => {
                                this.$vs.loading.close();
                                if (resp.data.codigo != 0) {
                                    this.$vs.notify({
                                        color: '#B71C1C',
                                        title: 'Comparativo',
                                        text: 'No existe comparativo generado.',
                                    })

                                    this.ListaComparativoDetalle = [];
                                } else {
                                    this.ListaComparativoDetalle = resp.data.json;
                                                                
                                    this.$vs.loading()

                                    setTimeout(() => {
                                        this.$vs.loading.close();
                                        document.getElementById("btn_descarga").click(); // Click on the checkbox
                                        this.$vs.loading.close();
                                    }, 4000)
                                    this.$vs.loading.close();
                                }
                            })
                            .catch(() => {
                                this.$vs.loading.close();
                            })
                        this.$vs.loading.close();
                        /************FIN DETALLE ********* */
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();

        },
        AgregarEncabezado(numero_prov, datos_encabezado, ListaProveedores) {

            this.DatosEncabezado = [];
            this.DatosEncabezado.push("COMPARATIVO COTIZACIONES.");
            this.DatosEncabezado.push("DEPARTAMENTO SOLICITANTE: " + datos_encabezado.DESCRIPCION_DEPTO);
            if (datos_encabezado.DESCRIPCION_BODEGA != '')
                this.DatosEncabezado.push("BODEGA SOLICITANTE: " + datos_encabezado.DESCRIPCION_BODEGA);
            this.DatosEncabezado.push("Nº. SOLICITUD DE COMPRA: " + datos_encabezado.IDSOLICITUDENCFK);
            this.DatosEncabezado.push("FECHA GENERACION REPORTE: " + moment(new Date()).format('DD/MM/YYYY'));
            this.DatosEncabezado.push(String.fromCharCode(32));
            this.DatosEncabezado.push(String.fromCharCode(32));

            /****ENCABEZADO DE LA TABLA DETALLE ** */
            this.json_fields_aux = [];
            this.json_fields_aux = Object.assign(this.json_fields_aux, {
                'No.': 'ID'
            }, {
                'CODIGO': 'CODIGO'
            }, {
                'PRODUCTO': 'PRODUCTO'
            }, {
                'CANT. SOLICITADA': 'SOLICITADO'
            })

            //Agregar detalle cotizaciones proveedores
            for (var i = 1; i <= numero_prov; i++) {
                var Precio_descripcion = 'PROVEEDOR NO. ' + i + ' (Precio/Unitario) ';
                var Precio_id = 'PRECIO_UNITARIO_P' + i;

                var Seleccionado_descripcion = 'Seleccionado P. ' + i;
                var Seleccionado_id = 'SELECCIONADO_P' + i;
                this.json_fields_aux = Object.assign(this.json_fields_aux, {
                    [Precio_descripcion]: Precio_id
                });
                this.json_fields_aux = Object.assign(this.json_fields_aux, {
                    [Seleccionado_descripcion]: Seleccionado_id

                });
            }
            this.json_fields = this.json_fields_aux;
            this.DatosFinalizacion = [];
            this.DatosFinalizacion.push(String.fromCharCode(32));
            this.DatosFinalizacion.push(String.fromCharCode(32));
            for (var contador = 0; contador < ListaProveedores.length; contador++) {
                this.DatosFinalizacion.push('PROVEEDOR NO. ' + (contador + 1) + ': ' + ListaProveedores[contador].Nombre);
            }

        },
        Operacion_seleccionada({
            DESCRIPCION
        }) {
            return `${DESCRIPCION} `
        },
        onChangeEstado(value) {
            if (value !== null && value.length !== 0) {
                this.Id_estado_seleccionado = value.ID;
            } else {
                this.Id_estado_seleccionado = '';
            }
        },
        Consultar_Autorizacion_cotizacion: function () {
            this.Lista_cotizaciones_externas = [];
            this.Lista_cotizaciones_internas = [];
            const url = this.$store.state.global.url
            const sesion = this.$store.state.sesion
            this.$vs.loading();

            this.axios.post(url + 'app/v1_OrdenCompra/obtener_autorizacion_cotizacion', {
                    Empresa: sesion.sesion_empresa,
                    corporativo: sesion.corporativo,
                    estado: this.Id_estado_seleccionado,
                    fecha_inicio: this.fecha_inicio,
                    fecha_fin: this.fecha_final,
                    Departamento: this.IdDepartamento
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Comparativo',
                            text: resp.data.mensaje,
                        })
                        this.Lista_cotizaciones_internas = [];
                    } else {
                        this.Lista_cotizaciones_internas = resp.data.json;
                        this.Estado_VentanaEmergente_Comparativo = false;

                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();

        },
        Consultar_cotizacion(Tipo, Estado, IdSolicitud) {

            const url = this.$store.state.global.url
            const sesion = this.$store.state.sesion
            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/consultar_cotizacion_enc', {
                    Empresa: sesion.sesion_empresa,
                    tipo: Tipo,
                    estado: Estado,
                    fecha_inicio: this.fecha_inicio,
                    fecha_fin: this.fecha_final,
                    Solicitud_enc: IdSolicitud
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {

                        if (Tipo === 'I') {
                            this.Lista_cotizaciones_internas = [];
                        } else {
                            this.Lista_cotizaciones_externas = [];
                        }

                    } else {
                        if (Tipo === 'I') {
                            this.Lista_cotizaciones_internas = resp.data.json;
                        } else {
                            this.Lista_cotizaciones_externas = resp.data.json;
                        }
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();

        },
        GenerarSolicitudCompra(datos) {

            const url = this.$store.state.global.url
            this.$vs.loading();

            /**************CONSULTA SOLICITUD DETALLE*********** */

            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/consultar_solicitud_compra_det', {
                    id_empresa: datos.IDEMPRESASFK,
                    id_solicitud_enc: datos.IDSOLICITUDENCFK,
                    tipo_transaccion: 'T'
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Comparativo',
                            text: resp.data.mensaje,
                        })

                        this.Lista_detalle_solicitud = [];
                    } else {

                        this.GenerarPDFSolicitud(resp.data.json, datos, resp.data.json.length)
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })

            this.$vs.loading.close();

        },
        SeleccionarCotizacionInterna(tr) {
    
            this.Cotizacion_interna_selec = tr;
            this.CotExt_Id_Solicitud = tr.IDSOLICITUDENCFK;
            this.CotExt_Id_Departamento = tr.DESCRIPCION_DEPTO;
            this.Consultar_cotizacion('E', 'P', this.CotExt_Id_Solicitud);

        },
        Finalizar_Autorizacion(datos) {

            this.Observaciones = '';
            this.Id_estado_solicitud_eleccionado = '';
            this.cb_lista_estado_solicitud = '';
            this.Datos_autorizacion_seleccionado = [];

            if (datos.ESTADO == 'P') {
                this.Datos_autorizacion_seleccionado = datos;
                this.Estado_VentanaEmergente_Auto = true;
            } else {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Comparativo',
                    text: 'Autorización cotización finalizada.',
                })

            }
        },
        Confirmacion_Transaccion() {
            /**
             * @General
             * Función Permite  actualizar un registro;
             */

            const sesion = this.$store.state.sesion
            this.Corporativo_Sesion = sesion.corporativo
            const url = this.$store.state.global.url

            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/comparativo_finalizar', {
                    Id_comparativo_enc: this.IdComparativoEnc,
                    Corporativo: sesion.corporativo
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Comparativo',
                            text: resp.data.mensaje,
                        })

                    } else {

                        this.$vs.loading();
                        this.axios.post(url + 'app/v1_OrdenCompra/finalizar_autorizacion', {
                                empresa: sesion.sesion_empresa,
                                corporativo: sesion.corporativo,
                                id_historial_autorizacion: this.Cotizacion_interna_selec.IDAUTORIZACIONHISTORIAL,
                                id_solicitud_enc: this.Cotizacion_interna_selec.IDSOLICITUDENCFK,
                                estado: 'A',
                                id_documento: 2,
                                num_documento: 1,
                                observaciones: this.Observaciones,
                                id_cotizacion_enc: '0',
                                id_orden_compra_enc: 0
                            })
                            .then(resp => {
                                this.$vs.loading.close();
                            
                                if (resp.data.codigo != 0) {
                                    this.$vs.notify({
                                        color: '#B71C1C',
                                        title: 'Comparativo',
                                        text: resp.data.mensaje,
                                    })

                                } else {

                                    this.Estado_VentanaEmergente_Comparativo = false;
                                    this.Estado_VentanaEmergente_Auto = false;
                                    this.Lista_cotizaciones_internas = [];
                                    this.Lista_cotizaciones_externas = [];
                                    this.Consultar_Autorizacion_cotizacion();
                                }
                            })
                            .catch(() => {
                                this.$vs.loading.close();
                               
                            })
                        this.$vs.loading.close();
                       
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                    
                })
            this.$vs.loading.close();
            
        },

        GenerarCotizacion(datos) {

            /**CONSULTAR DETALLE COTIZACION SELECCIONADA */

            const url = this.$store.state.global.url
            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/consultar_cotizacion_det', {
                    IdCotizacionEnc: datos.IDCOTIZACIONENC
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Comparativo',
                            text: resp.data.mensaje,
                        })

                    } else {

                        this.GenerarPDFCotizacion(resp.data.json, datos)

                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();

        },
    }
}
</script>

<style scoped>
.titulo-disenio {
    font-family: 'Barmeno';
    font-weight: 300;
    font-size: 25px;
    color: #ffffff;
    border-radius: 15px;
    margin: 0 auto;

    width: 400px;
    text-align: center;
    background: #fbd72f;
    margin-bottom: -40px;
}

.espacio {
    width: 0px;
    height: 0px;
    margin: 0 auto;
    border-style: solid;
    border-width: 50px;
    border-bottom-width: 0px;
    border-color: #ffffff transparent transparent transparent;
}

.label-sizem {
    font-size: 16px;
    font-weight: bold;
}
</style>
