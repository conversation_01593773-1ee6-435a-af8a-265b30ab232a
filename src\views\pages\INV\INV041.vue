<template>
    <div>
        <buscador ref="BuscarProveedorConsignacion" buscador_titulo="Buscador / Proveedores" :api="'app/v1_OrdenCompra/ConsultaProveedorConsignacion'" 
            :campos="['Proveedor', 'Nombre', 'Nit', 'Direccion', 'Telefonos']"
            :titulos="['Proveedor', '#Nombre', '#Nit', '#Direccion', '#Telefonos']"
            :api_filtro="{'Proveedor':info.Proveedor}"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" :api_preload="true" />

        <buscador ref="BuscarBodegaConsignacion" buscador_titulo="Buscador / Bodegas" :api="'app/v1_OrdenCompra/ConsultaBodegaConsignacion'" 
            :campos="['CodigoBodega', 'NombreBodega']"
            :titulos="['CodigoBodega', '#NombreBodega']"
            :api_filtro="{CodigoAgrupacion:'8',CodigoBodega:CodigoBodega}"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" :api_preload="true" />

        <buscador ref="BuscarProductoProveedorConsignacion" buscador_titulo="Buscador / Productos" :api="'app/v1_OrdenCompra/ConsultaProductoProveedorConsignacion'" 
            :campos="['Producto', 'Descripcion']"
            :titulos="['Producto', '#Descripcion']"
            :api_filtro="{Proveedor:info.Proveedor, Producto:ProductoBuscar}"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" :api_preload="true" />

        <vx-card title="Pedido Consignación">
            <vs-row vs-type="flex" vs-w="12">
                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                    <label class="vs-input--label">Fecha&nbsp;</label>
                    <vs-input type="date" v-model="Fecha"></vs-input>
                </vs-col>
                <vs-col vs-align="center" vs-w="3">
                    <vs-button color="primary" icon-pack="fa" icon="fa-save" @click="LimpiarCampos();LimpiarProveedor();LimpiarBodega();LimpiarProducto()">Limpiar</vs-button>
                </vs-col>
                <vs-col vs-align="center" vs-w="3">
                    <vs-button color="success" icon-pack="fa" icon="fa-save" @click="GrabarPedido()">Grabar Pedido</vs-button>
                </vs-col>
            </vs-row>
            <br>
            <vx-card title="Datos de la Orden">
                <vs-row vs-type="flex" vs-w="12">
                    <vs-col vs-type="flex" vs-align="center" vs-w="2">
                        <vs-input ref="ProveedorConsignacion" v-model="info.Proveedor" @keyup.enter="Proveedores()" @keydown.tab="Proveedores()" ></vs-input>
                        <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="Proveedores()" icon="fa-search"></vs-button>
                    </vs-col>
                    <vs-col vs-type="flex" vs-align="center" vs-w="6">
                        Proveedor:&nbsp;&nbsp;<h3>{{ info.Nombre }}</h3>
                    </vs-col>
                </vs-row>
                <br>
                <vs-row vs-w="12">
                    <vs-col vs-type="flex" vs-align="center" vs-w="2"></vs-col>
                    <vs-col vs-type="flex" vs-align="center" vs-w="6">
                        Dirección:&nbsp;&nbsp;<h3>{{ info.Direccion }}</h3>
                    </vs-col>
                </vs-row>
                <br>
                <vs-row vs-type="flex" vs-w="12">
                    <vs-col vs-type="flex" vs-align="center" vs-w="2"></vs-col>
                    <vs-col vs-type="flex" vs-align="center" vs-w="2">
                        Telefonos:&nbsp;&nbsp;<h3>{{ info.Telefonos }}</h3>
                    </vs-col>
                    <vs-col vs-type="flex" vs-align="center" vs-justify="flex-end" vs-w="6">
                        Nit:&nbsp;&nbsp;<h3>{{ info.Nit }}</h3>
                    </vs-col>
                </vs-row>
            </vx-card>
            <br>
            <vx-card title="Datos de Entrega">
                <vs-row vs-type="flex" vs-w="12">
                    <vs-col vs-type="flex" vs-align="center" vs-w="1">
                        <label class="vs-input--label">Bodega Destino:&nbsp;</label>
                    </vs-col>
                    <vs-col vs-type="flex" vs-align="center" vs-w="2">
                        <vs-input ref="BodegaDestino" v-model="CodigoBodega" @keyup.enter="Bodega()" @keydown.tab="Bodega()" ></vs-input>
                        <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="Bodega()" icon="fa-search"></vs-button>
                    </vs-col>
                    <vs-col vs-type="flex" vs-align="center" vs-w="5">
                        <h3>{{ this.NombreBodega }}</h3>
                    </vs-col>
                    <vs-col vs-type="flex" vs-align="center" vs-w="1">
                        <label class="vs-input--label">Entrega a más tardar:&nbsp;</label>
                    </vs-col>
                    <vs-col vs-type="flex" vs-align="center" vs-w="3">
                        <vs-input type="date" v-model="FechaMasTardar"></vs-input>
                    </vs-col>
                </vs-row>
                <br>
                <vs-row vs-type="flex" vs-w="12">
                    <vs-col vs-type="flex" vs-align="center" vs-w="1">
                        <label class="vs-input--label">Solicitante&nbsp;</label>
                    </vs-col>
                    <vs-col vs-type="flex" vs-align="center" vs-w="2">
                        <vs-input v-model="Solicitante" disabled></vs-input>
                    </vs-col>
                    <vs-col vs-type="flex" vs-align="center" vs-w="5">
                        <h3>&nbsp;&nbsp;{{ this.NombreSolicitante }}</h3>
                    </vs-col>
                    <vs-col vs-type="flex" vs-align="center" vs-w="1">
                        <label class="vs-input--label">Contacto:&nbsp;</label>
                    </vs-col>
                    <vs-col vs-type="flex" vs-align="center" vs-w="3">
                        <vs-input v-model="Contacto" style="width: 100%;"></vs-input>
                    </vs-col>
                </vs-row>
                <br>
                <vs-row vs-type="flex" vs-justify="flex-end" vs-w="12">
                    <vs-col vs-type="flex" vs-w="1" vs-align="center">
                        <label class="vs-input--label">Tel. Contacto:&nbsp;</label>
                    </vs-col>
                    <vs-col vs-type="flex" vs-w="3" vs-align="center">
                        <vs-input v-model="TelContacto"></vs-input>
                    </vs-col>
                </vs-row>
                <br>
                <vs-row vs-type="flex" vs-w="12">
                    <vs-col vs-type="flex" vs-w="1" vs-align="center">
                        <label class="vs-input--label">Motivo de Pedido:&nbsp;</label>
                    </vs-col>
                    <vs-col vs-type="flex" vs-w="11" vs-align="center">
                        <vs-textarea rows="5" counter="240" v-model="Motivo"/>
                    </vs-col>
                </vs-row>
            </vx-card>
            <br>
            <vs-divider></vs-divider>
            <br>
            <div>
                <vs-row vs-type="flex" vs-w="12">
                    <vs-col vs-type="flex" vs-w="12" vs-align="center">
                        <label class="vs-input--label">Producto &nbsp;</label>
                        <vs-input ref="ProductoDeseado" v-model="ProductoBuscar" @keyup.enter="CargarProducto()" @keydown.tab="CargarProducto()" />
                        <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="CargarProducto()" icon="fa-search"></vs-button>
                    </vs-col>
                </vs-row>
                <br>
                <vs-row vs-type="flex" vs-w="12" >
                    <vs-col vs-type="flex" vs-w="12" vs-align="center">
                        <!----- Detalle producto seleccionado--->
                        <div v-if="Producto_seleccionado.Producto>0" class="w-full md:w-4/5 lg:w-4/5 xl:w-4/5">
                            <div style="border-radius:5px;padding:5px;font-size:14px;background-color:#A5D6A7">
                                Código: {{Producto_seleccionado.Producto}}
                                <br>
                                Producto: {{Producto_seleccionado.Descripcion}}
                                <br>
                                Presentación: {{Producto_seleccionado.NombreUnidad}}
                                <br>
                                Precio Convenio: {{$formato_moneda(Producto_seleccionado.PrecioConvenio)}}
                            </div>
                        </div>
                    </vs-col>
                </vs-row>
                <br>
                <vs-row vs-type="flex" vs-w="12" >
                    <vs-col vs-type="flex" vs-align="center" vs-w="3">
                        <label class="vs-input--label">Cantidad &nbsp;</label>
                        <vs-input ref="CantidadProducto" @change="onChangeCantidad" v-model="Cantidad" />
                    </vs-col>
                    <vs-col vs-type="flex" vs-align="center" vs-w="5">
                        Valor Total:&nbsp;&nbsp;<h3>{{ $formato_moneda(ValorSubTotal) }}</h3>
                    </vs-col>
                </vs-row>
                <br>
                <vs-row vs-type="flex" vs-w="12" >
                    <vs-col vs-type="flex" vs-align="center" vs-w="3">
                        <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="AgregarProducto()" icon="fa-plus-circle">Agregar</vs-button>
                    </vs-col>
                </vs-row>
            </div>
            <br>
            <div class="card">
                <vs-table2 max-items="10" tooltip pagination :data="detalleProductosManual" >
                    <template slot="thead">
                        <th order="Producto" width="20px">Producto</th>
                        <th order="Cantidad" width="100px">Cantidad</th>
                        <th order="U Medida" width="20px">UnidadMedida</th>
                        <th order="Descripcion" width="20px">Descripcion</th>
                        <th order="Precio Q." width="20px">PrecioUnitario</th>
                        <th order="Valor Total" width="20px">ValorTotal</th>
                        <th order="Convenio" width="20px">Convenio</th>
                        <th width="20px">Eliminar</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2 noTooltip>
                                {{ tr.Producto }}
                            </vs-td2>
                            <vs-td2 noTooltip>
                                <vs-input type="number" v-model.number="tr.Cantidad" @change="OnChangeCantidadPedido(tr)"></vs-input>
                            </vs-td2>
                            <vs-td2 noTooltip>
                                {{ tr.NombreUnidad }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.Descripcion }}
                            </vs-td2>
                            <vs-td2 noTooltip>
                                {{ $formato_moneda(tr.PrecioConvenio) }}
                            </vs-td2>
                            <vs-td2 noTooltip>
                                {{ $formato_moneda(tr.ValorTotal) }}
                            </vs-td2>
                            <vs-td2 noTooltip>
                                {{ tr.Convenio}}
                            </vs-td2>
                            <vs-td2 noTooltip>
                                <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="EliminarProducto(indextr, data[indextr])"></vs-button>
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
            <br>
            <div>
                <vs-row vs-type="flex" vs-justify="flex-end" vs-w="12" >
                    <vs-col vs-type="flex" vs-align="center" vs-w="4" >
                        Total:&nbsp;&nbsp;<h3>{{ $formato_moneda(GranTotal) }}</h3>
                    </vs-col>
                </vs-row>
            </div>
        </vx-card>
    </div>
</template>
<script>
    import moment from 'moment'
    import Multiselect from "vue-multiselect";
    import "vue-multiselect/dist/vue-multiselect.min.css"
    export default {
        data() {
            return{
                Fecha: '',
                Proveedor: '',
                info: {
                    Proveedor: '',
                    Nombre: '',
                    Nit: '',
                    Direccion: '',
                    Telefonos: ''
                },
                CodigoBodega: '',
                NombreBodega: '',
                FechaMasTardar: '',
                Solicitante: '',
                NombreSolicitante: '',
                Contacto: '',
                TelContacto: '',
                Motivo: '',
                detalleProductosManual: [{
                    Producto: '',
                    Cantidad: 0,
                    UnidadMedida: '',
                    NombreUnidad: '',
                    Descripcion: '',
                    PrecioUnitario: 0,
                    ValorTotal: 0,
                    Convenio: '',
                    PrecioConvenio: 0,
                    CostoConvenio: 0
                }],
                Producto_seleccionado: {
                    Producto: '',
                    Cantidad: 0,
                    UnidadMedida: '',
                    NombreUnidad: '',
                    Descripcion: '',
                    PrecioUnitario: 0,
                    ValorTotal: 0,
                    Convenio: '',
                    PrecioConvenio: 0,
                    CostoConvenio: 0
                },
                ProductoBuscar: '',
                Cantidad: 0,
                ValorSubTotal: 0,
                GranTotal: 0

            }
        },
        comments: {
            Multiselect
        },
        methods:{
            Proveedores(){
                if(this.info.Proveedor != ''){
                    this.axios.post('/app/v1_OrdenCompra/ConsultaProveedorConsignacion', {
                        Proveedor: this.info.Proveedor
                    })
                    .then(resp => {
                        if(resp.data.codigo == 0){
                            if(resp.data.json.length == 1){
                                this.info = resp.data.json[0]
                            }else{
                                this.$refs.BuscarProveedorConsignacion.iniciar((data) =>{
                                    if(data != null){
                                        this.info = data
                                    }
                                })
                            }
                        }
                    })
                }else{
                    this.$refs.BuscarProveedorConsignacion.iniciar((data) =>{
                        if(data != null){
                            this.info = data
                        }
                    })
                }
                
            },
            Bodega(){
                if(this.CodigoBodega != ''){
                    this.axios.post('/app/v1_OrdenCompra/ConsultaBodegaConsignacion', {
                        CodigoBodega: this.CodigoBodega,
                        CodigoAgrupacion: '8'
                    })
                    .then(resp => {
                        if(resp.data.codigo == 0){
                            if(resp.data.json.length == 1){
                                this.NombreBodega = resp.data.json[0].NombreBodega
                            }else{
                                this.$refs.BuscarBodegaConsignacion.iniciar((data) => {
                                    if(data != null){
                                        this.CodigoBodega = data.CodigoBodega
                                        this.NombreBodega = data.NombreBodega
                                    }
                                })
                            }
                        }
                    })
                }else{
                    this.$refs.BuscarBodegaConsignacion.iniciar((data) => {
                        if(data != null){
                            this.CodigoBodega = data.CodigoBodega
                            this.NombreBodega = data.NombreBodega
                        }
                    })
                }
            },
            CargarProducto(){
                if(this.info.Proveedor != ''){
                    if(this.ProductoBuscar != ''){
                        this.axios.post('/app/v1_OrdenCompra/ConsultaProductoProveedorConsignacion', {
                            Proveedor: this.info.Proveedor,
                            Producto: this.ProductoBuscar
                        })
                        .then(resp =>{
                            if(resp.data.json.length == 1){
                                this.ProductoBuscar = resp.data.json[0].Producto
                                this.Producto_seleccionado.Producto = resp.data.json[0].Producto
                                this.Producto_seleccionado.UnidadMedida = resp.data.json[0].UnidadMedida
                                this.Producto_seleccionado.NombreUnidad = resp.data.json[0].NombreUnidad
                                this.Producto_seleccionado.Descripcion = resp.data.json[0].Descripcion
                                this.Producto_seleccionado.PrecioConvenio = resp.data.json[0].PrecioConvenio
                                this.Producto_seleccionado.CostoConvenio = resp.data.json[0].CostoConvenio
                                this.Producto_seleccionado.Convenio = resp.data.json[0].Convenio

                                this.$refs.CantidadProducto.focusInput()
                            }else{
                                this.$refs.BuscarProductoProveedorConsignacion.iniciar((data) => {
                                    if(data != null){
                                        this.ProductoBuscar = data.Producto
                                        this.Producto_seleccionado.Producto = data.Producto
                                        this.Producto_seleccionado.UnidadMedida = data.UnidadMedida
                                        this.Producto_seleccionado.NombreUnidad = data.NombreUnidad
                                        this.Producto_seleccionado.Descripcion = data.Descripcion
                                        this.Producto_seleccionado.PrecioConvenio = data.PrecioConvenio
                                        this.Producto_seleccionado.CostoConvenio = data.CostoConvenio
                                        this.Producto_seleccionado.Convenio = data.Convenio

                                        this.$refs.CantidadProducto.focusInput()
                                    }
                                })
                            }
                        })
                    }else{
                        this.$refs.BuscarProductoProveedorConsignacion.iniciar((data) => {
                            if(data != null){
                                this.ProductoBuscar = data.Producto
                                this.Producto_seleccionado.Producto = data.Producto
                                this.Producto_seleccionado.UnidadMedida = data.UnidadMedida
                                this.Producto_seleccionado.NombreUnidad = data.NombreUnidad
                                this.Producto_seleccionado.Descripcion = data.Descripcion
                                this.Producto_seleccionado.PrecioConvenio = data.PrecioConvenio
                                this.Producto_seleccionado.CostoConvenio = data.CostoConvenio
                                this.Producto_seleccionado.Convenio = data.Convenio

                                this.$refs.CantidadProducto.focusInput()
                            }
                        })
                    }
                }else{
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Pedido Consignación',
                        text: 'Debe de Seleccionar un Proveddor',
                    })
                }
               
            },
            onChangeCantidad(){
                if(this.Producto_seleccionado.Producto != ''){
                    this.ValorSubTotal = parseFloat(this.Producto_seleccionado.PrecioConvenio) * parseFloat(this.Cantidad)
                }else{
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Pedido Consignación',
                        text: 'Debe de seleccionar un producto',
                    })
                    this.$refs.ProductoDeseado.focusInput()
                }
                
            },
            OnChangeCantidadPedido(data){
                if(data.Cantidad == '' || data.Cantidad == null || data.Cantidad == 0){
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Pedido Consignación',
                        text: 'Debe de ingresar la cantidad',
                    })
                    data.Cantidad = 1
                    
                    return
                }
                data.ValorTotal = data.Cantidad * data.PrecioConvenio
                let total = 0;
                this.detalleProductosManual.forEach(element => {
                    total += (Number(element.ValorTotal))
                });

                this.GranTotal = parseFloat(total)
            },
            AgregarProducto(){
                if(this.Producto_seleccionado.Producto == '' || this.Producto_seleccionado.Producto == null || this.Producto_seleccionado.Producto == 0){
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Pedido Consignación',
                        text: 'Debe de seleccionar un producto',
                    })
                    return
                }
                if(this.Cantidad == '' || this.Cantidad == null || this.Cantidad == 0){
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Pedido Consignación',
                        text: 'Debe de ingresar la cantidad',
                    })
                    return
                }

                const found = this.detalleProductosManual.find(element => element.Producto == this.Producto_seleccionado.Producto)
                if(found){
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Pedido Consignación',
                        text: 'Este código ya fue ingresado, no se puede volver a ingresar',
                    })

                    this.Cantidad = 0

                    return
                }

                this.detalleProductosManual.push({
                    Producto: this.Producto_seleccionado.Producto,
                    Cantidad: this.Cantidad,
                    UnidadMedida: this.Producto_seleccionado.UnidadMedida,
                    NombreUnidad: this.Producto_seleccionado.NombreUnidad,
                    Descripcion: this.Producto_seleccionado.Descripcion,
                    PrecioUnitario: this.Producto_seleccionado.PrecioUnitario,
                    ValorTotal: this.ValorSubTotal,
                    Convenio: this.Producto_seleccionado.Convenio,
                    PrecioConvenio: this.Producto_seleccionado.PrecioConvenio,
                    CostoConvenio: this.Producto_seleccionado.CostoConvenio
                })

                let total = 0;
                this.detalleProductosManual.forEach(element => {
                    total += (Number(element.ValorTotal))
                });

                this.GranTotal = parseFloat(total)
                this.LimpiarProducto()
                this.$refs.ProductoDeseado.focusInput()
            },
            EliminarProducto(index, datos){
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',
                    text: '¿Eliminar detalle? ',
                    acceptText: 'Aceptar',
                    cancelText: 'Cancelar',
                    accept: () => {
                        this.GranTotal = parseFloat(this.GranTotal - datos.ValorTotal)
                        this.$delete(this.detalleProductosManual, index)
                    }
                })

            },
            GrabarPedido(){
                if(this.CodigoBodega == '' || this.CodigoBodega == null || this.CodigoBodega == 0){
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Pedido Consignación',
                        text: 'Debe de ingresar una bodega',
                    })
                    return
                }
                if(this.info.Proveedor == '' || this.info.Proveedor == null || this.info.Proveedor == 0){
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Pedido Consignación',
                        text: 'Debe de Seleccionar un Proveddor',
                    })
                    return
                }
                if(this.Motivo == '' || this.Motivo == null){
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Pedido Consignación',
                        text: 'Debe ingresar un motivo',
                    })
                    return
                }

                this.axios.post('/app/v1_OrdenCompra/IngresoPedidoConsignacion', {
                    Proveedor: this.info.Proveedor,
                    Motivo: this.Motivo,
                    Contacto: this.Contacto,
                    TelContacto: this.TelContacto,
                    LugarEntrega: this.NombreBodega,
                    FechaEntrega: this.FechaMasTardar,
                    CodigoBodega: this.CodigoBodega,
                    Datos: JSON.stringify(this.detalleProductosManual)
                })
                .then(resp => {
                    if(resp.data.codigo == '0'){
                        this.$reporte_modal({
                            Nombre: "Pedido Consignacion",
                            Opciones: {
                                Pedido: resp.data.resultado
                            },
                            Formato: "PDF"
                        })
                        this.LimpiarProducto()
                        this.LimpiarBodega()
                        this.LimpiarProveedor()
                        this.LimpiarCampos()
                    }
                })
            },
            ObtenerFechas(){
                var date = new Date()
                this.Fecha = moment(new Date(Date.now())).format('YYYY-MM-DD')
                this.FechaMasTardar = moment(new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1)).format('YYYY-MM-DD')
            },
            LimpiarProveedor(){
                this.info.Proveedor = ''
                this.info.Nombre = ''
                this.info.Nit = ''
                this.info.Direccion = ''
                this.info.Telefonos = ''
            },
            LimpiarBodega(){
                this.CodigoBodega = '',
                this.NombreBodega = ''
            },
            LimpiarProducto(){
                    this.Producto_seleccionado.Producto = '',
                    this.Producto_seleccionado.Cantidad = 0,
                    this.Producto_seleccionado.UnidadMedida = '',
                    this.Producto_seleccionado.NombreUnidad = '',
                    this.Producto_seleccionado.Descripcion = '',
                    this.Producto_seleccionado.PrecioUnitario = 0,
                    this.Producto_seleccionado.ValorTotal = 0,
                    this.Producto_seleccionado.Convenio = '',
                    this.Producto_seleccionado.PrecioConvenio = 0
                    this.ValorSubTotal = 0
                    this.Cantidad = 0
                    this.ProductoBuscar = ''
            },
            LimpiarCampos(){
                this.ObtenerFechas()
                this.Contacto = ''
                this.TelContacto = ''
                this.Motivo = ''
                this.detalleProductosManual = []
                this.GranTotal = 0
            }
        },
        mounted() {
            let sesion = this.$store.state.sesion
            this.Solicitante = sesion.corporativo
            this.NombreSolicitante = sesion.usuario
            this.detalleProductosManual = []
            this.$refs.ProveedorConsignacion.focusInput()
            this.ObtenerFechas()
            
        }

    }
</script>