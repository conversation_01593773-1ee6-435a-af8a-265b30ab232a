<template>
    <div id="app">

        <div class="datos_habitacion pl-4">

            <!-- <div class="container"> -->

            <div class="row">
                <!-- <div class="row justify-content-md-center"> -->
                    <div class="container-fluid" style="vertical-align: middle; margin-top: -15px;">

                        <vs-row>
                            <vs-col vs-w="2" class="w-4/12 pr-8">     
                                    <SM-Buscar v-model="CodigoHabitacion" 
                                        label="Habitación" 
                                        api="app/v1_admision/ConsultarHabitaciones"
                                        :api_campo_respuesta_mostrar="['Codigo', 'Nombre']"
                                        :api_campos="['Codigo', 'Nombre', 'CodigoArea', 'Area', 'Producto','NivelPrecios','Status']"
                                        :api_titulos="['Codigo', 'Nombre#', 'CodigoArea#', 'Area#', 'Producto#','NivelPrecios#','Status#']"
                                        api_campo_respuesta="Codigo" 
                                        :callback_buscar="DatosHabitacion().Consultar"
                                        :callback_cancelar="DatosHabitacion().Limpiar"
                                        :api_preload="true"
                                        :disabled_texto="!HabilitarCampos" 
                                        />
                            </vs-col>

                            <vs-col vs-w="6" class="w-7/12 pl-4">                                
                                    <vs-input v-model="NombreHabitacion" label="Descripción" class="w-full" disabled/>
                            </vs-col>
                        </vs-row>

                    </div>
            </div>
            <!-- </div> -->
        </div>
    </div>
</template>


<script>
//import { mapFields } from "vuex-map-fields";

export default {
    name: "ConsultaHabitaciones",
    data() {
        return {
            CodigoHabitacion: '',
            NombreHabitacion: '',
            HabilitarCampos: true
        };
    },
    computed: {

    },
    watch: {
        'CodigoHabitacion'(newValue) {
            this.$emit('codigo-habitacion', newValue);
        }
    },
    methods: {
        DatosHabitacion() {
            return {

                Consultar: (datos) => {
                    
                    this.NombreHabitacion = datos.Nombre
                    this.HabilitarCampos = false
                },
                Limpiar: () => {
                    this.CodigoHabitacion = null
                    this.NombreHabitacion = null
                    this.HabilitarCampos = true
                }
            }
        }
    }

}


</script>



<style scoped>
.datos_habitacion {
    padding-top: 2rem;
    padding-bottom: 1rem;
    padding-left: 1rem;
    padding-right: 1rem;
    border: 1px solid #888;
    border-radius: 5px;
    margin: 5px;
}
</style>

