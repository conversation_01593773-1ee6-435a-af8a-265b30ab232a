<template>
    <div id="app">
        <div class="tabla-polizas contenedor-tabla">

            <vs-table2 max-items="4" tooltip :data="ListaPolizas">
                <template slot="thead">
                    <th width="50px" filtro="CodigoAfiliado">Codigo Afiliado</th>
                    <th width="50px" filtro="IdPoliza">Id Poliza</th>
                    <th width="300px" filtro="NombrePlan">Nombre Plan</th>
                    <th width="150px" filtro="CodigoPlan">Codigo Plan</th>
                    <th width="150px" filtro="Estado">Estado</th>
                </template>

                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data" :ref="`list_${indextr}`" v-on:click=submit(tr,indextr)
                        :class="indextr === filaSeleccionada ? 'resaltar-columna': ''">
                        <vs-td2>
                            {{ tr.Codigo<PERSON>filiado }}
                        </vs-td2>
                        <vs-td2>
                            {{ tr.Codigo }}
                        </vs-td2>

                        <vs-td2>
                            {{ tr.NombrePlan }}
                        </vs-td2>

                        <vs-td2>
                            {{ tr.CodigoPlan }}
                        </vs-td2>
                        <vs-td2>
                            {{ tr.Estado == 'A' ? 'Activo' : 
                                tr.Estado == 'C' ? 'Cancelado' : 
                                 tr.Estado == 'D' ? 'Desactivado' : tr.Estado }}
                        </vs-td2>
                    </tr>
                </template>

            </vs-table2>
        </div>

    </div>
</template>


  
<script>


export default {
    name: "PolizaSaludSiempre",
    props: {
        IdPaciente: {
            required: true,
            type: Number
        },
        SubOpcion: {
            required: false,
            type: String
        }
    },
    components: {
    },
    data() {
        return {
            selPolizaSaludSiempre: '',
            ListaPolizas: [],
            filaSeleccionada: null
        };
    },
    watch: {
        IdPaciente: async function(value){
            const resp = await this.axios.post('/app/v1_JefeCaja/consulta_polizas_x_paciente', {
                IdPaciente: value,
                SubOpcion: this.SubOpcion
            })
            this.ListaPolizas = resp.data.json            
        },immediate: true
    },
    activated() {
        
    },
    mounted() {
        this.ConsultaPolizasPaciente()
    },
    methods: {
        async ConsultaPolizasPaciente() {
            const resp = await this.axios.post('/app/v1_JefeCaja/consulta_polizas_x_paciente', {
                SubOpcion: this.SubOpcion,
                IdPaciente: this.IdPaciente
            })
            this.ListaPolizas = resp.data.json
            this.$emit("lista-vacia", this.ListaPolizas.length);
            
        },
        submit(item,indextr) {
            this.filaSeleccionada = indextr
            this.$emit("poliza-seleccionada", item);
        }


    }
};
</script>


<style scoped>


</style>

<style >
.tabla-polizas .contenedor-tabla {
    min-height: auto;
}

.resaltar-columna {
    background-color:  rgba(0, 143, 190, 0.2) !important
}
</style>
