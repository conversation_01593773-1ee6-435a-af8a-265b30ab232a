<template>
<div>
    <vx-card :title="`Órdenes Farmacia ${sesion.sesion_sucursal_nombre}`">

        <buscador ref="buscar_orden" buscador_titulo="Buscador / Admisión" :api="'app/v1_JefeCaja/consulta_admision_recalculo'" :campos="['Serie','Codigo','Nombres','Apellidos','Habitacion']" :titulos="['Serie','Admisión','Nombres','Apellidos','Habitación']" :api_filtro="{Opcion:'CONSULTA',SubOpcion:'ADMISION',activo: 1,pagina: 1}" :multiselect="false" :api_validar_campo="true" :api_preload="false" />

        <ValidarPermisoHuella ref="componenteValidarPermiso" :permisoHuella="PermisoEntrega" @getPermisosHuellas="PeticionRecepcion"></ValidarPermisoHuella>
        <div class="flex flex-wrap">
            <div class="sm:w-full md:w-full lg:w-1/2 xl:w-1/2 p-0">
                <p style="font-size: 20px"><b>Paciente:</b> {{ info.paciente }}</p>
            </div>
            <div class="sm:w-full md:w-full lg:w-1/2 xl:w-1/2"></div>
            <div style="font-size: 20px" class="sm:w-full md:w-1/6 lg:w-1/6 xl:w-1/6"><b>Nivel Precios:</b> {{ info.nivelprecio }}</div>
            <div style="font-size: 20px" class="sm:w-full md:w-1/6 lg:w-1/6 xl:w-1/6"><b>Descuento:</b> {{ info.TipoDescuento }}</div>

            <div v-if="info.TipoPaciente== 'Privado'" class="PacientePrivado" style="font-size: 20px"><b>{{ info.TipoPaciente }}</b> </div>
            <div v-else-if="info.TipoPaciente== 'Seguro'" class="Seguro" style="font-size: 20px"><b>Asegurado</b> </div>
            <div v-else-if="info.TipoPaciente=='Salud Siempre'" class="PacienteSaludSiempre" style="font-size: 20px"><b>{{ info.TipoPaciente }}</b> </div>

        </div>

        <div class="md:w-full lg:w-1/12 xl:w-1/12 m-1">
            <ValidationProvider>
                <vs-input label="Habitación:" class="w-full" :disabled="bloqueoBusqueda" v-model="info.habitacion" v-on:change="BusquedaAdmisionHabitacion" />
            </ValidationProvider>

        </div>
        <div class="flex flex-wrap">
            <div class="w-full md:w-full lg:w-1/12 xl:w-1/12 m-1">

                <ValidationProvider>
                    <vs-input label="Serie:" :disabled="bloqueoBusqueda" class="w-full" v-model="info.serie" v-on:change="info.serie=info.serie.toUpperCase();BusquedaAdmisionHabitacion" />
                </ValidationProvider>
            </div>
            <!---AGRUPACIÓN PARA BUSQUEDA DE ADMISIÓN-->
            <div class="w-full md:w-full lg:w-1/12 xl:w-1/12 m-1">
                <label class="vs-input--label">Admisión</label>

                <vx-input-group class="">
                    <vs-input v-model="info.numeroAdmision" :disabled="bloqueoBusqueda" @change="BusquedaAdmisionHabitacion()" />
                    <template slot="append">
                        <div class="append-text btn-addon">
                            <button type="submit" v-show="false" name="button"></button>
                            <vs-button id="button-with-loading-1" color="primary" icon-pack="feather" @click="ValidacionCampoBuscar()" icon="icon-search" v-if="bloqueoBusqueda==false"></vs-button>
                            <vs-button id="button-with-loading-2" color="danger" icon-pack="feather" @click="LimpiarDatosAdmision();" icon="icon-x" v-else></vs-button>
                        </div>
                    </template>
                </vx-input-group>
            </div>

            <div class="w-full md:w-full lg:w-1/3 xl:w-1/3 m-1">

                <ValidationProvider>
                    <vs-input disabled label="Nombre del paciente:" class="w-full" v-model="info.paciente" />
                </ValidationProvider>
            </div>
        </div>

        <!---- BUSQUEDA TIPO ORDEN FA-->

        <div class="flex flex-wrap">
            <div class="w-full md:w-full lg:w-2/12 xl:w-2/12 m-1">
                <label class="typo__label">Tipo Orden</label>
                <multiselect v-model="cb_lista_tipos_ordenes" :options="PermisosTiposOrdenesUsuario" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Operacion_seleccionada" placeholder="Seleccionar" @input="onChangeEstado">
                    <span slot="noOptions">Lista no disponible.</span>
                </multiselect>
            </div>
            <div class="w-full md:w-full lg:w-1/6 xl:w-1/6 m-1">
                <vs-input label="Nombre" class="w-full" :value="info.Hospital" disabled />
            </div>
            <div class="w-full md:w-full lg:w-1/6 xl:w-1/6 m-1">
                <vs-input label="Bodega" class="w-full" :value="info.bodega" disabled />
            </div>
        </div>
        <!---- BUSQUEDA TIPO ORDEN FC-->
        <div class="flex flex-wrap">
            <div class="w-full md:w-full lg:w-2/12 xl:w-2/12 m-1">
                <vs-input label="Tipo Orden" class="w-full" :value="info.ConsTipoOrden" disabled />
            </div>
            <div class="w-full md:w-full lg:w-1/6 xl:w-1/6 m-1">
                <vs-input label="Nombre" class="w-full" :value="info.ConsHospital" disabled />
            </div>
            <div class="w-full md:w-full lg:w-1/6 xl:w-1/6 m-1">
                <vs-input label="Bodega" class="w-full" :value="info.Consbodega" disabled />
            </div>
        </div>

        <!---- BUSQUEDA MEDICO-->
        <div class="flex flex-wrap">
            <div class="w-full md:w-full lg:w-1/3 xl:w-1/3 m-1">
                <ValidationProvider rules="required" v-slot="{ errors }">
                    <vs-input label="Nombre del Médico/Terapista:" class="w-full" :value="info.nombreMedico" disabled :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                </ValidationProvider>
            </div>
        </div>
        <!-- NUEVA FORMA DE PRESENTAR DATOS -->
        <vs-divider>Detalle Orden</vs-divider>
        <div>
            <vs-tabs :color="colorx">

                <vs-tab label="Cargos Vale Electrónico" icon="account_balance" @click="colorx='#FFA500';CargarMedicamentos('AUTOMATICO')">
                    <div class="flex flex-wrap">
                        <div class="md:w-full lg:w-1/4 xl:w-1/4 m-1">
                                
                            <div class="w-full md:w-full lg:w-full xl:w-full m-2">
                                <vs-checkbox v-model="CargarTodo" @change="SeleccionarTodos()">Seleccionar Todos.</vs-checkbox>
                            </div>
    
                        </div>
                        <div class="md:w-full lg:w-1/4 xl:w-1/4 m-5">
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 m-5">
                            <vx-input-group class="">
                                <template slot="append">
                                    <vs-button color="danger" icon-pack="feather" icon="icon-x" class="mr-5" @click="LimpiarDatosAdmision">Limpiar</vs-button>
                                    <vs-button color="green" icon-pack="feather" icon="icon-refresh-cw" class="mr-5" @click="CargarMedicamentos()">Actualizar </vs-button>
                                    <vs-button icon-pack="fas" icon="fa-save" @click="ValidacionInsertarCargos(true)">Cargar Orden</vs-button>
                                </template>
                            </vx-input-group>

                        </div>
                        <div class="w-full md:w-full lg:w-full xl:w-full">
                            <div style="margin-left:10px" class="flex flex-wrap mt-4">
                                <div class="sm:w-full md:w-full lg:w-full xl:w-full  p-0">
                                    <div class="w-full p-0">
                                        <h3 style="color: #3498db;"><b>Productos a Cargar</b></h3>
                                    </div>
                                    <vs-table2 max-items="10" pagination :data="ListaMedicamentosValeElec" noDataText="Sin datos disponibles" search id="tb_proveedores">

                                        <template slot="thead">
                                            <th width="100px">¿Despachar?</th>
                                            <th width="100px">Código</th>
                                            <th width="600px">Médicamento</th>

                                            <th>Fecha Asignada</th>
                                            <th>Tipo</th>

                                        </template>

                                        <template slot-scope="{data}">

                                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                                <td v-if="tr.DiaDespacho =='Hoy'" class="colorFechaDelDia">
                                                    <vs-checkbox v-model="data[indextr].seleccion" @change="CargarExistencia(tr, indextr)" />
                                                </td>
                                                <td v-else class="colorFechaDeOtroDia">
                                                    <vs-checkbox v-model="data[indextr].seleccion" @change="CargarExistencia(tr, indextr)" />
                                                </td>

                                                <vs-td2 v-if="tr.DiaDespacho =='Hoy'" class="colorFechaDelDia" :data="data[indextr].Codigo">
                                                    {{data[indextr].Codigo}}
                                                </vs-td2>
                                                <vs-td2 v-else class="colorFechaDeOtroDia" :data="data[indextr].Codigo">
                                                    {{data[indextr].Codigo}}
                                                </vs-td2>

                                                <vs-td2 v-if="tr.DiaDespacho =='Hoy'" class="colorFechaDelDia" :data="data[indextr].Nombre">
                                                    {{data[indextr].Nombre}}
                                                </vs-td2>
                                                <vs-td2 v-else class="colorFechaDeOtroDia" :data="data[indextr].Nombre">
                                                    {{data[indextr].Nombre}}
                                                </vs-td2>

                                                <vs-td2 v-if="tr.DiaDespacho =='Hoy'" class="colorFechaDelDia" :data="data[indextr].FECHAASIGNADA">
                                                    {{data[indextr].FECHAASIGNADA}}
                                                </vs-td2>
                                                <vs-td2 v-else class="colorFechaDeOtroDia" :data="data[indextr].FECHAASIGNADA">
                                                    {{data[indextr].FECHAASIGNADA}}
                                                </vs-td2>

                                                <vs-td2 v-if="tr.DiaDespacho =='Hoy'" class="colorFechaDelDia" width="120px" :data="data[indextr].Tipo">
                                                    {{data[indextr].Tipo}}
                                                </vs-td2>
                                                <vs-td2 v-else class="colorFechaDeOtroDia" width="120px" :data="data[indextr].Tipo">
                                                    {{data[indextr].Tipo}}
                                                </vs-td2>
                                            </tr>
                                        </template>
                                    </vs-table2>
                                </div>

                                <vs-spacer></vs-spacer>
                            </div>
                        </div>
                    </div>
                </vs-tab>

                <vs-tab label="Cargos Manuales" icon="edit" @click="colorx = '#00C853'">
                    <div class="flex flex-wrap">
                        <div class="md:w-full lg:w-1/4 xl:w-1/4 m-1">
                            <label class="vs-input--label">Búsqueda Producto</label>
                            <vx-input-group class="">
                                <vs-input v-model="BusquedaProducto" ref="LectorProductos" @change="BusquedaMedicamentos('Cambio')" />
                                <template slot="append">
                                    <div class="append-text btn-addon">
                                        <vs-button id="button-with-loading-3" color="primary" icon-pack="feather" @click="BusquedaMedicamentos('ClickBuscar')" icon="icon-search"></vs-button>
                                    </div>
                                </template>
                            </vx-input-group>
                        </div>
                        <div class="md:w-full lg:w-1/4 xl:w-1/4 m-5">
                            <div>
                                <tr>
                                    <td class="p-3">
                                        <h4>TOTAL</h4>
                                    </td>
                                    <td class="p-3" width="200px" style="text-align:right">
                                        <h4>{{totalCargos}}</h4>
                                    </td>
                                </tr>
                            </div>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 m-5">
                            <vx-input-group class="">
                                <template slot="append">
                                    <vs-button color="danger" icon-pack="feather" icon="icon-x" class="mr-5" @click="LimpiarDatosAdmision">Limpiar</vs-button>
                                    <vs-button icon-pack="fas" icon="fa-save" @click=" VentanaFinalizarCargos = true;">Cargar Orden</vs-button>
                                </template>
                            </vx-input-group>

                        </div>
                        <br><br>

                        <div class="w-full md:w-full lg:w-full xl:w-full">
                            <vs-checkbox v-model="InventarioMayorCero" @change="FiltroInventario()">Inventario mayor Cero</vs-checkbox>
                        </div>

                        <!-- BUSQUEDA DE PRODUCTOS -->

                        <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                            <vs-table2 ref="tablacargos" max-items="10" pagination :data="ListadoProductos" height="470px">
                                <template slot="thead">
                                    <th order="Código" width="10%">Código</th>
                                    <th order="Nombre" width="60%">Nombre</th>
                                    <th order="Precio" width="20%">Precio</th>
                                    <th order="Existencia" width="10%">Existencia</th>
                                    <!-- <th order="Exist" width="50px">Existencia</th> -->
                                </template>
                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data" @click="ValidacionProductoSelecionado(tr)">
                                        <vs-td2>
                                            {{ tr.Codigo }}
                                        </vs-td2>
                                        <vs-td2>
                                            <div style="word-wrap: break-word;white-space: wrap;">
                                                {{ tr.Nombre }}
                                            </div>
                                        </vs-td2>
                                        <vs-td2 style="min-width: 120px;">
                                            {{parseFloat(tr.Precio).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                        </vs-td2>

                                        <vs-td2 v-if="tr.Existencia > 0" class="Disponible">
                                            {{ tr.Existencia }}
                                        </vs-td2>
                                        <vs-td2 v-else class="NoDisponible">
                                            {{ tr.Existencia }}
                                        </vs-td2>

                                    </tr>
                                </template>
                            </vs-table2>
                        </div>

                        <!-- CARGOS MANUALES -->
                        <!-- <div class="sm:w-full md:w-full lg:w-full xl:w-7/12  p-0"> -->
                        <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
                            <vs-table2 ref="tablacargos" max-items="10" pagination :data="cargos" height="470px">
                                <template slot="thead">
                                    <th order="Codigo" width="10%">Código</th>
                                    <th order="Nombre">Descripción</th>
                                    <th order="UnidadMedida" width="10%">U. Medida</th>
                                    <!----<th order="Ajeno" width="16%">Ajeno</th>--->
                                    <th width="15%">Cant.</th>
                                    <th order="Precio" width="10%">Precio</th>
                                    <th width="10%">Valor</th>
                                    <th width="10%">Consignación</th>
                                    <th width="50px">Eliminar</th>
                                </template>
                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td2 width="10%">
                                            {{ tr.Codigo }}
                                        </vs-td2>
                                        <vs-td2>
                                            <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">
                                                <small>
                                                    {{ tr.Nombre }}
                                                </small>
                                            </div>
                                        </vs-td2>
                                        <vs-td2 width="10%">
                                            {{ tr.UnidadMedida }}
                                        </vs-td2>

                                        <vs-td2 width="15%">
                                            <vs-input style="width: 100%; text-align: center; vertical-align: middle;" v-model="tr.Cantidad" v-on:keyup="ModificacionCantidad(indextr)" />
                                        </vs-td2>

                                        <vs-td2 v-if="tr.Precio==0.00" width="10%">
                                            <vs-input type="number" class="w-full" v-model="tr.PrecioLibre" />
                                        </vs-td2>
                                        <vs-td2 v-if="tr.Precio!=0.00" width="10%">
                                            {{parseFloat(tr.Precio).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                        </vs-td2>

                                        <vs-td2 width="10%">
                                            {{parseFloat(tr.Precio * tr.Cantidad ).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                        </vs-td2>
                                        <vs-td2 width="10%" style="text-align: center;" v-if="tr.Consignacion">
                                            SI
                                        </vs-td2>
                                        <vs-td2 width="10%" style="text-align: center;" v-else>
                                            NO
                                        </vs-td2>
                                        <vs-td width="50px" style="text-align: center;">
                                            <vs-button color="danger" type="filled" icon-pack="fas" icon="fa-trash" @click="cargos.splice(indextr,1)"></vs-button>
                                        </vs-td>

                                    </tr>
                                </template>

                            </vs-table2>
                        </div>

                    </div>
                </vs-tab>
            </vs-tabs>
        </div>

    </vx-card>
    <div>
        {{ HuellaLectura }}
    </div>

    <vs-popup class="popNuevo" title="Información Cargos" :active.sync="VentanaMensajesGenerales" style="z-index:99999" id="3">

        <div>
            <div class="sm:w-full md:w-full lg:w-full xl:w-full"><b>Mensaje:</b> {{ MensajeGeneralDato }}
            </div>
            <br>
            <div>
                <vs-button class="mr-5" color="primary" style="float: right" type="filled" icon-pack="feather" icon="icon-save" @click="VentanaMensajesGenerales=false">
                    Aceptar</vs-button>
                <vs-button class="mr-5" color="danger" style="float: right" type="border" icon-pack="feather" icon="icon-x" @click="VentanaMensajesGenerales = false">
                    Cancelar</vs-button>
            </div>
        </div>

    </vs-popup>

    <vs-popup class="popNuevo" title="Mensaje Cargos" :active.sync="VentanaInsertarCantidad" style="z-index:99999" id="2">

        <div>
            <ValidationProvider>
                <vs-input label="Cantidad a cargar:" type="number" class="w-full" v-model="CantidadProducto" />
            </ValidationProvider>
            <br>
            <div>
                <vs-button color="primary" style="float: right" type="filled" icon-pack="feather" icon="icon-save" @click="VentanaInsertarCantidad=false;BusquedaProducto='';agregaProducto(ProductoPreSeleccionado);">
                    Grabar</vs-button>
                <vs-button color="danger" style="float: right" type="border" icon-pack="feather" icon="icon-x" @click="FocoBusquedaProducto();VentanaInsertarCantidad=false;">
                    Cancelar</vs-button>
            </div>
        </div>

    </vs-popup>

    <vs-popup class="popNuevo" title="Finalización de Cargos" :active.sync="VentanaFinalizarCargos" style="z-index:99999" id="1">

        <div>
            <vs-textarea class="w-full" label="Observaciones" counter="200" v-model="info.observaciones" />
            <br>
            <div>
                <vs-button color="primary" style="float: right" type="filled" icon-pack="feather" icon="icon-save" @click="VentanaFinalizarCargos=false;InsertCargos();">
                    Grabar</vs-button>
                <vs-button color="danger" style="float: right" type="border" icon-pack="feather" icon="icon-x" @click="VentanaFinalizarCargos=false;">
                    Cancelar</vs-button>
            </div>
        </div>

    </vs-popup>

    <!------------ mensaje finalización de cargos -->

    <vs-popup class="popFinalizacion" title="Finalización de  Cargos" :active.sync="VentanaReporteCargos" style="z-index:99999" id="4">
        <div class="flex flex-wrap">

            <div class="sm:w-full md:w-full lg:w-full xl:w-full"><b>Mensaje:</b> {{ MensajeGeneralDato }}
            </div>
            <br>
            
            <div class="flex w-full"  v-if="Respuesta.Orden1 > 0 || Respuesta.Orden2 >0">
                <vs-checkbox v-model="Reimpresion">Reimpresión</vs-checkbox>
            </div>
            
            <br>
            <br>
            <div class="flex w-full ">
                <vs-button v-if="Respuesta.Orden1 >0" class="mr-4" color="green" style="float: right" type="filled" @click="ReportePDF_IMPRESION(Reimpresion, Respuesta.Tipo1, Respuesta.Orden1, '', 0, false )">
                    <i class="fas fa-file-pdf "></i>
                    {{Respuesta.Tipo1 }} {{Respuesta.Orden1 }}
                </vs-button>

                <vs-button class="mr-4" v-if="Respuesta.Orden2 >0" color="green" style="float: right" type="filled" @click="ReportePDF_IMPRESION(Reimpresion, Respuesta.Tipo2, Respuesta.Orden2, '', 0 , false  )">
                    <i class="fas fa-file-pdf "></i>
                    {{Respuesta.Tipo2 }} {{Respuesta.Orden2 }}
                </vs-button>

                <vs-button color="primary" style="float: right" type="filled" icon-pack="feather" icon="icon-save" @click="VentanaReporteCargos=false">
                    Aceptar
                </vs-button>
            </div>
            <br><br>
        </div>
    </vs-popup>
</div>
</template>

<script>
import Multiselect from 'vue-multiselect'
import "vue-multiselect/dist/vue-multiselect.min.css"
import ValidarPermisoHuella from "/src/components/validador-huella/ValidarPermisoHuella.vue"
import 'flatpickr/dist/flatpickr.css';
import {
    Spanish as SpanishLocale
} from 'flatpickr/dist/l10n/es.js';
import moment from 'moment';

export default {
    components: {

        ValidarPermisoHuella,
        Multiselect
    },
    data() {
        return {
            CargarTodo: false,
            Reimpresion: false,
            VentanaReporteCargos: false,
            IpLocal: '',
            VentanaFinalizarCargos: false,
            VentanaMensajesGenerales: false,
            MensajeGeneralDato: '',
            VentanaInsertarCantidad: false,
            colorx: '#00C853',
            cb_lista_tipos_ordenes: '',
            bloqueoBusqueda: false,
            PermisoEntrega: 'PERMISO_ENTREGA',
            Estado_VentanaEmergente: true,
            CargosManuales: false,
            AutoFocus: false,
            InventarioMayorCero: false,
            info: {
                serie: null,
                numeroAdmision: null,
                nivelprecio: null,
                bodega: null,
                tipoOrden: null,
                filtroTipoOrden: [],
                filtroTipoOrdenGeneral: [],
                medico: null,
                nombreMedico: null,
                observaciones: null,
                fechaEstudio: null,
                habitacion: null,
                paciente: null,
                Asegura: null,
                TipoDescuento: null,
                Hospital: null,
                ConsTipoOrden: null,
                ConsHospital: null,
                Consbodega: null,
                TipoPaciente: null,
            },
            cargos: [],
            productos: [],
            mostrarOrden: false,
            configFromdateTimePicker: {
                minDate: new Date(),
                maxDate: this.Otros().sumarDias(new Date(), 360),
                locale: SpanishLocale,
                dateFormat: "d/m/Y H:i",
                enableTime: true,
                time_24hr: true
            },
            BusquedaProducto: null,
            CantidadProducto: null,
            ListadoProductosOriginal: [],
            ListadoProductos: [],
            ListaMedicamentosValeElec: [],
            TipoHuella: '',
            CorporativoRecibe: '',
            CorporativoEntrega: '',

            PermisosTiposOrdenesUsuario: [],
            ProductoPreSeleccionado: [],

            Respuesta: {
                Tipo1: '',
                Orden1: '',
                Tipo2: '',
                Orden2: ''
            },
        }

    },
    computed: {

        HuellaLectura() {

            this.PermisoEntrega = 'PERMISO_RECIBE';
            if ((this.CorporativoEntrega != '') && (this.TipoHuella == 'Entrega')) {
                this.TipoHuella = 'Recibe';
                this.$refs.componenteValidarPermiso.popUpPermisosHuella = true;
                this.$refs.componenteValidarPermiso.ParametroCorporativo  = '';
                this.$refs.componenteValidarPermiso.info.Nombres  = '';
                this.$refs.componenteValidarPermiso.info.Apellidos  = '';
                this.$refs.componenteValidarPermiso.TituloPopup = 'Validación de huella Enfermería.';
            }

        },

        VerificacionTipoOrden() { // Carlos de vale electronico
            var TPOrden = '';
            if (this.info.tipoOrden != null) {
                TPOrden = this.info.tipoOrden.substr(0, 2);

            }

            if (TPOrden == 'FA' || TPOrden == 'FC')

                this.$vs.dialog({
                    type: 'confirm',
                    color: 'dark',
                    title: 'Verificar producto',
                    text: ' ¿Realizar cargos vale electrónico?',
                    acceptText: 'Continuar',
                    cancelText: 'Cancelar',
                    clientWidth: 100,
                    accept: () => {
                        this.CargosManuales = true;
                        this.CargarMedicamentos();
                    },
                    cancel: () => {
                        setTimeout(() => {
                            this.CargosManuales = false;
                        }, 100)

                    }
                })
            return '';
        },
        totalCargos() {
            return this.cargos.reduce((acumulador, cargo) => acumulador + (cargo.PrecioLibre ? parseFloat(cargo.PrecioLibre) : parseFloat(cargo.Precio)) * cargo.Cantidad, 0).toLocaleString("es-GT", {
                style: "currency",
                currency: "GTQ"
            })

        },
        sesion() {
            return this.$store.state.sesion
        },
        montoTotal() {
            return this.cargos.reduce((acumulador, cargo) => acumulador + (cargo.PrecioLibre ? parseFloat(cargo.PrecioLibre) : parseFloat(cargo.Precio)) * cargo.Cantidad, 0)
        },
    },
    mounted() {

        this.IpLocal = localStorage.getItem('IpLocal');
        this.Consulta().init()
        this.CargarCategoriasAjenos()
        for (let privilegio of this.$store.state.privilegios) {
            if (privilegio.Privilegio.includes("TIPO_HOSPITAL")) {
                let tipo_orden = privilegio.Privilegio.substring(privilegio.Privilegio.length - 2, privilegio.Privilegio.length)
                this.info.filtroTipoOrden.push(tipo_orden)

            } else if (privilegio.Privilegio.includes("TIPO_GENERAL")) {
                let tipo_orden_general = privilegio.Privilegio.substring(privilegio.Privilegio.length - 3, privilegio.Privilegio.length)
                this.info.filtroTipoOrdenGeneral.push(tipo_orden_general)
            }
        }
    },
    methods: {
        SeleccionarTodos() {
                var i = 0;
                if (this.CargarTodo) {
    
                    for (i = 0; i < this.ListaMedicamentosValeElec.length; i++) {
                        this.ListaMedicamentosValeElec[i].seleccion = true;
                    }
                } else {
                    for (i = 0; i < this.ListaMedicamentosValeElec.length; i++) {
                        this.ListaMedicamentosValeElec[i].seleccion = false;
                    }
    
                }
        },    
        ModificacionCantidad(index) {
            var CantidadIngresadaAnt = 0;
            const Ingresado = this.cargos.filter(listado => listado.Codigo == this.cargos[index].Codigo)

            var parsedDataIngresada = JSON.stringify(Ingresado);
            const jsonDataIngresada = JSON.parse(parsedDataIngresada);
            CantidadIngresadaAnt = parseFloat(jsonDataIngresada.reduce((total, alias) => {
                return parseFloat(total) + parseFloat(alias.Cantidad);
            }, 0));

            if (parseFloat(CantidadIngresadaAnt) > parseFloat(this.cargos[index].Existencia) && (this.cargos[index].Consignacion == false)) {

                this.$vs.dialog({
                    type: 'confirm',
                    color: 'dark',
                    title: 'Verificar producto',
                    cancelText: 'Cancelar',
                    acceptText: 'Aceptar',
                    text: 'No hay  existencias para el producto, ¿Desea continuar?',
                    accept: () => {
                        //Actualiza la nueva cantidad
                        this.cargos[index].CantidadOriginal = this.cargos[index].Cantidad;
                    },
                    cancel: () => {
                        //ARegresa a la cantidad anterior
                        this.cargos[index].Cantidad = this.cargos[index].CantidadOriginal;
                    }
                })

            } else if (parseFloat(CantidadIngresadaAnt) > parseFloat(this.cargos[index].Existencia) && (this.cargos[index].Consignacion == true)) {

                this.$vs.dialog({
                    colorAlert: 'red',
                    titleAlert: 'danger',
                    activeAlert: false,
                    title: 'Verificar producto',
                    acceptText: 'Aceptar',
                    text: 'No existe suficiente inventario para el producto de Consignación, no es posible cargar.',
                    accept: () => {
                        //ARegresa a la cantidad anterior
                        this.cargos[index].Cantidad = this.cargos[index].CantidadOriginal;
                    }
                })

            }
            //}

        },
        ValidacionProductoSelecionado(Producto) {
            if (Producto.costopromedioValidado > 0) {
                this.VentanaInsertarCantidad = true
                this.ProductoPreSeleccionado = Producto;
            } else {

                this.MensajeGeneralDato = Producto.Mensajecostopromedio;
                this.VentanaMensajesGenerales = true;
                this.VentanaInsertarCantidad = false
            }
        },
        ReportePDF_IMPRESION(ImpresionAuto, ImpTipo, ImpOrden, ImpTipo2, ImpOrden2, DescargarAuto) {
            return new Promise(resolve => {
                if (ImpTipo != '' && ImpOrden != '' && ImpTipo != "undefined" && ImpOrden != "undefined") {

                    this.$reporte_modal({
                        Nombre: "Cargos por Orden Farmacia",
                        Opciones: {
                            AjusteImpresion: ImpresionAuto ? 1 : 0,
                            tiporeporte: "application/pdf",
                            nombrereporte: "cargos_por_orden_farmacia",
                            TipoOrden: ImpTipo,
                            CodigoOrden: ImpOrden,
                            TipoOrden2: ImpTipo2,
                            CodigoOrden2: ImpOrden2,
                            json: "[{\"Etiqueta\":\"nombrereporte\",\"Parametro\":\"nombrereporte\",\"value\":\"cargos_por_orden_farmacia\"},{\"Etiqueta\":\"Tipo:\",\"Parametro\":\"TipoOrden\",\"value\":\"" + ImpTipo + "\"},{\"Etiqueta\":\"Número:\",\"Parametro\":\"CodigoOrden\",\"value\":\"" + ImpOrden + "\"}, {\"Etiqueta\":\"TipoOrden2\",\"Parametro\":\"TipoOrden2\",\"value\":\"" + ImpTipo2 + "\"},{\"Etiqueta\":\"CodigoOrden2\",\"Parametro\":\"CodigoOrden2\",\"value\":\"" + ImpOrden2 + "\"}, {\"Etiqueta\":\"AjusteImpresion\",\"Parametro\":\"TipoOrden2\",\"value\":\"" + ImpresionAuto ? 1 : 0 + "\"}]",
                            ConfigCorreo: null
                        },
                        Imprimir: ImpresionAuto,
                        Descargar: DescargarAuto,
                        NombreArchivoDescargar: ImpTipo + '-' + ImpOrden

                    }).then(
                        resolve(resp)
                    )
                }
            })
        },
        Operacion_seleccionada({
            Codigo
        }) {
            return `${Codigo}`
        },
        onChangeEstado(value) {
            this.cargos = [];
            this.ListadoProductos = [];
            this.ListadoProductosOriginal = [];
            this.ProductoPreSeleccionado = [];
            this.ListaMedicamentosValeElec = [];
            this.BusquedaProducto = '';

            if (value != null) {
                this.info.tipoOrden = value.Codigo;
                this.info.bodega = value.BodegaDefault;
                this.info.Hospital = value.Nombre;
            } else {
                this.info.tipoOrden = '';
                this.info.bodega = '';
                this.info.Hospital = '';
            }

        },
        ValidacionTipoDescuento(Valor) {
            if (Valor == 'S') {
                this.VentanaMensajesGenerales = true;
                this.MensajeGeneralDato = 'Paciente con seguro.';
            } else if (Valor == 'Q') {
                this.VentanaMensajesGenerales = true;
                this.MensajeGeneralDato = 'Paciente de paquete';
            }

        },

        ValidacionCampoBuscar() {
            if ((this.info.habitacion != '' && this.info.habitacion != null) || (this.info.serie != '' && this.info.serie != null) || (this.info.numeroAdmision > 0)) {
                this.BusquedaAdmisionHabitacion();
            } else {
                this.HabilitarComponenteBusquedaAdmision();
            }
            return;
        },
        LimpiarDatosAdmision() {
            this.CargarTodo = false;
            this.BusquedaProducto = null;
            this.cb_lista_tipos_ordenes = null;
            this.bloqueoBusqueda = false;
            this.info.serie = null;
            this.info.numeroAdmision = null;
            this.info.nivelprecio = null;
            this.info.bodega = null;
            this.info.tipoOrden = null;
            this.info.medico = null;
            this.info.nombreMedico = null;
            this.info.observaciones = null;
            this.info.fechaEstudio = null;
            this.info.habitacion = null;
            this.info.paciente = null;
            this.info.Asegura = null;
            this.info.TipoDescuento = null;
            this.info.Hospital = null;
            this.info.ConsTipoOrden = null;
            this.info.Consbodega = null;
            this.info.ConsHospital = null;
            this.info.TipoPaciente = null;
            this.PermisosTiposOrdenesUsuario = [];
            this.ListadoProductos = [];
            this.ListadoProductosOriginal = [];
            this.ProductoPreSeleccionado = [];
            this.cargos = [];
            this.ListaMedicamentosValeElec = []
        },
        HabilitarComponenteBusquedaAdmision() {
            this.$refs.buscar_orden.iniciar((datos) => {
                if (datos != null) {
                    this.CargarTodo = false;
                    this.info.serie = datos.Serie
                    this.info.numeroAdmision = datos.Codigo
                    this.info.paciente = datos.Paciente
                    this.info.habitacion = datos.Habitacion
                    this.info.nivelprecio = datos.NivelPrecios
                    this.info.medico = datos.CodigoMedico
                    this.info.fechaEstudio = new Date()
                    this.info.Asegura = datos.Asegura
                    this.bloqueoBusqueda = true;
                    this.info.TipoDescuento = datos.TipoDescuento;
                    this.info.nombreMedico = datos.Medico;
                    this.info.TipoPaciente = datos.TipoPaciente;
                    this.ValidacionTipoDescuento(datos.TipoDescuento);
                    this.PermisosTipoOrdenes();

                    setTimeout(() => {
                        this.CargarMedicamentos();
                    }, 1500)

                }
            })

        },
        async PermisosTipoOrdenes() { // Consulta de tipos de ordenes por usuario.
            /***** EXTRAER LOS TIPO DE ORDENES POR MODULO */
            var TipoOrdenMod = '';
            for (var i = 0; i < this.info.filtroTipoOrden.length; i++) {
                if (TipoOrdenMod == '') {
                    TipoOrdenMod = this.info.filtroTipoOrden[i];
                } else {
                    TipoOrdenMod = TipoOrdenMod + ',' + this.info.filtroTipoOrden[i];
                }

            }

            /***** EXTRAER LOS TIPO DE ORDENES POR USUARIO */
            if (TipoOrdenMod != '') {
                this.PermisosTiposOrdenesUsuario = [];
                this.axios.post('/app/v1_JefeCaja/ObtenerTiposOrdenesPorRol', {
                        Activa: 0,
                        filtroTipoOrdenGeneral: '',
                        filtroTipoOrdenHospital: TipoOrdenMod,
                        opcion: 'C',
                        pagina: 1,
                        subOpcion: '3'
                    })
                    .then(resp => {
                        if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                            this.PermisosTiposOrdenesUsuario = resp.data.json;

                            //FILTRA EL TIPO FA Y LO COLOCA POR DEFECTO
                            var listaFa = '';
                            listaFa = this.PermisosTiposOrdenesUsuario.filter(f => f.Codigo.substring(0, 2) == 'FA');
                            if (listaFa != '') {
                                this.cb_lista_tipos_ordenes = {
                                    Codigo: listaFa[0].Codigo
                                }
                                this.info.tipoOrden = listaFa[0].Codigo;
                                this.info.bodega = listaFa[0].BodegaDefault;
                                this.info.Hospital = listaFa[0].Nombre;
                            }

                            var listaCo = '';
                            listaCo = this.PermisosTiposOrdenesUsuario.filter(f => f.Codigo.substring(0, 2) == 'FC');
                            if (listaCo != '') {

                                this.info.ConsTipoOrden = listaCo[0].Codigo;
                                this.info.Consbodega = listaCo[0].BodegaDefault;
                                this.info.ConsHospital = listaCo[0].Nombre;
                            }
                        } else {
                            this.LimpiarDatosAdmision();
                            this.$vs.notify({
                                title: 'Alerta',
                                color: 'danger',
                                position: 'top-center',
                                time: 6000,
                                text: 'No existen permisos para tipos de ordenes para este usuario.'
                            })
                            return;
                        }
                    })
                    .catch()
            }

        },
        BusquedaAdmisionHabitacion() {
            this.axios.post('/app/v1_JefeCaja/consulta_admision_recalculo', {
                    Habitacion: this.info.habitacion,
                    Opcion: 'CONSULTA',
                    Serie: this.info.serie,
                    Codigo: this.info.numeroAdmision,
                    SubOpcion: 'ADMISION',
                    activo: 1,
                    pagina: 1,
                    tieneHabitacion: 0
                })
                .then(resp => {
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        resp.data.json.map(datos => {
                            this.CargarTodo = false;
                            this.info.serie = datos.Serie
                            this.info.numeroAdmision = datos.Codigo
                            this.info.paciente = datos.Paciente
                            this.info.habitacion = datos.Habitacion
                            this.info.nivelprecio = datos.NivelPrecios
                            this.info.medico = datos.CodigoMedico
                            this.info.nombreMedico = datos.Medico;
                            this.info.fechaEstudio = new Date()
                            this.info.Asegura = datos.Asegura
                            this.info.TipoDescuento = datos.TipoDescuento;
                            this.info.TipoPaciente = datos.TipoPaciente;
                            this.bloqueoBusqueda = true;
                            this.ValidacionTipoDescuento(datos.TipoDescuento);
                            this.PermisosTipoOrdenes();

                            setTimeout(() => {
                                this.CargarMedicamentos();
                            }, 1500)

                        })
                    }
                })
                .catch()
            //}

        },
        PeticionRecepcion(respuestaHuella) {
            //this.InsertCargos(true);

            if (respuestaHuella.statusValidacion == 'NA') {
                this.$vs.notify({
                    title: 'Alerta',
                    color: 'danger',
                    position: 'top-center',
                    time: 6000,
                    text: 'No se registro una huella valida'
                })
                return;
            }

            if (respuestaHuella.statusValidacion == 'N') {
                this.$vs.notify({
                    title: 'Alerta',
                    color: 'danger',
                    position: 'top-center',
                    time: 6000,
                    text: 'No cuenta con permiso para recibir pedidos de este departamento'
                })
                return;
            }

            if (this.TipoHuella == 'Entrega') {
                this.CorporativoEntrega = respuestaHuella.Corporativo;
            } else if (this.TipoHuella == 'Recibe') {
                this.CorporativoRecibe = respuestaHuella.Corporativo;
                if (this.CorporativoEntrega === this.CorporativoRecibe) {
                    //if (this.CorporativoEntrega == this.CorporativoRecibe) {
                    this.$vs.notify({
                        title: 'Alerta',
                        color: 'danger',
                        position: 'top-center',
                        time: 6000,
                        text: 'No es posible Entregar y Recibir con el mismos corporativo.'
                    })
                    this.TipoHuella = '';
                    this.CorporativoEntrega = "";
                    this.CorporativoRecibe = "";
                    return;
                } else {
                    this.TipoHuella = '';
                    this.InsertCargos(true)
                }
            }

        },
        CargarMedicamentos(TipoCarga) {

            if (!this.IpLocal || this.IpLocal.length <= 0) {
                this.VentanaMensajesGenerales = true;
                this.MensajeGeneralDato = 'Su máquina no esta configurada para utilizar el lector de huellas';
                //return;
            }

            if ((TipoCarga != 'AUTOMATICO') || (TipoCarga == 'AUTOMATICO' && this.ListaMedicamentosValeElec.length <= 0)) {

                if (this.info.serie != null && this.info.numeroAdmision != null) {
                    this.axios.post('/app/v1_farmacia/DetalleMedicamentos', {
                            SerieAdmision: this.info.serie,
                            NumeroAdmision: this.info.numeroAdmision,
                            Campo1: this.info.nivelprecio,
                            Campo5: this.info.tipoOrden
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.ListaMedicamentosValeElec = resp.data.json.map(m => {
                                    return {
                                        seleccion: false,
                                        ...m
                                    }
                                })

                            } else {
                                this.ListaMedicamentosValeElec = "";
                            }
                        })
                        .catch()
                }
            }
        },
        CargarExistencia(Datos, Posicion) {
            if (this.ListaMedicamentosValeElec[Posicion].seleccion) {
                this.axios.post('/app/v1_farmacia/BusquedaExistencia', {
                        productos: Datos.Codigo
                    })
                    .then(resp => {
                        if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                            var resultado = resp.data.json.map(m => {
                                return {
                                    ...m
                                }
                            })
                            if (resultado[0].codigo > 0) {

                                this.$vs.dialog({
                                    type: 'confirm',
                                    color: 'dark',
                                    title: 'Verificar producto',
                                    acceptText: 'Aceptar',
                                    cancelText: 'Cancelar',
                                    text: resultado[0].descripcion,
                                    buttonCancel: false,
                                    accept: () => {

                                    },
                                    cancel: () => {

                                        this.ListaMedicamentosValeElec[Posicion].seleccion = false;
                                    }
                                })
                            }

                        }
                    })
                    .catch()
            }
        },
        FocoBusquedaProducto() {
            this.BusquedaProducto = '';
            this.$refs.LectorProductos.focusInput();

        },

        DatosProductos() {

            if (!this.info.tipoOrden || !this.info.nivelprecio) {
                this.productos = []
                return
            }

        },
        DatosTipoOrden(datos) {
            this.info.tipoOrden = datos.Codigo
            this.info.bodega = datos.BodegaDefault
            this.cargos = []
            this.DatosProductos()
        },
        Otros() {
            return {
                sumarDias: (fecha, dias) => {
                    fecha.setDate(fecha.getDate() + dias);
                    return fecha;
                }
            }
        },
        Consulta() {
            return {
                habitacion: (datos) => {
                    this.info.habitacion = datos.Habitacion
                    this.info.nivelprecio = datos.NivelPrecios
                    this.info.paciente = datos.Paciente
                    this.CargarTodo = false;

                },
                init: () => {
                    this.LimpiarDatosAdmision()
                }
            }
        },
        CargarCategoriasAjenos() {
            this.axios.post('/app/emergencia/ConsultaCategoriasAjenos', {})
                .then(resp => {
                    const categoriasAjenos = resp.data.json[0].descripcion
                    this.categoriasAjenos = categoriasAjenos.split(',')
                })
        },
        BusquedaMedicamentos() {

            if (this.info.tipoOrden == null) {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Farmacia - Información',
                    text: 'Seleccionar Tipo de Orden.',
                })
                this.FocoBusquedaProducto();
                return;
            }
            this.ListadoProductos = [];
            this.axios.post('/app/v1_farmacia/BusquedaMedicamentos', {
                    Campo1: this.info.nivelprecio,
                    Campo2: this.BusquedaProducto,
                    Campo3: this.info.Asegura,
                    Campo5: this.info.tipoOrden

                })
                .then(resp => {

                    if (resp.data && resp.data.json.length > 0) {

                        this.ListadoProductosOriginal = resp.data.json;
                        if (this.ListadoProductosOriginal.length == 1) { //Si el resultado = 1, entonces lo agrega automaticamente. 
                            this.ProductoPreSeleccionado = resp.data.json[0];
                            //this.VentanaInsertarCantidad = true
                            this.ValidacionProductoSelecionado(resp.data.json[0])
                        }
                        if (this.InventarioMayorCero && this.ListadoProductosOriginal.length > 1) {
                            this.ListadoProductos = this.ListadoProductosOriginal.filter(f => f.Existencia > 0);
                        } else {
                            this.ListadoProductos = resp.data.json
                        }

                    }
                })
        },
        DatosMedico(datos) {
            this.info.medico = datos.Codigo
            // this.info.nombreMedico = datos.Nombre ? .trim() + ' ' + datos.Apellido ? .trim()
            this.info.nombreMedico = datos.Nombre.trim() + ' ' + datos.Apellido.TRIM()
        },
        agregaProducto(producto) {
            var CantidadIngresadaAnt = 0;
            var NuevaCantidadAcumulada = 0;
            const Ingresado = this.cargos.filter(listado => listado.Codigo == producto.Codigo)

            //Validación si ya existe el producto ingresado
            if (Ingresado.length > 0) {
                var parsedData = JSON.stringify(Ingresado);
                const jsonData = JSON.parse(parsedData);

                CantidadIngresadaAnt = parseFloat(jsonData.reduce((total, alias) => {
                    return parseFloat(total) + parseFloat(alias.Cantidad);
                }, 0));

                NuevaCantidadAcumulada = parseFloat(CantidadIngresadaAnt) + parseFloat(this.CantidadProducto);

            } else {
                NuevaCantidadAcumulada = this.CantidadProducto;
            }

            if (producto.CATEGORIA.length <= 0) {

                this.VentanaMensajesGenerales = true;
                this.MensajeGeneralDato = 'No puede registrar un producto no clasificado en una categoría.';
                return;
            }

            if (this.categoriasAjenos.includes(producto.CATEGORIA)) {

                this.VentanaMensajesGenerales = true;
                this.MensajeGeneralDato = 'Este producto es para un tipo de orden aje de honorarios.';
                return
            }

            if (producto.Precio <= 0) {

                this.VentanaMensajesGenerales = true;
                this.MensajeGeneralDato = 'Precio es inválido, debe ser mayor a cero.';
                return
            }

            if ((Number(producto.Existencia) < NuevaCantidadAcumulada) && (producto.Consignacion)) {
                this.VentanaMensajesGenerales = true;
                this.MensajeGeneralDato = 'No existe suficiente inventario para el producto de Consignación, no es posible cargar.';
                return
            }

            if (Number(producto.Existencia) < NuevaCantidadAcumulada) {
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'dark',
                    title: 'Verificar producto',
                    cancelText: 'Cancelar',
                    acceptText: 'Aceptar',
                    text: 'No hay  existencias para el producto, ¿Desea continuar?',

                    accept: () => {

                        this.cargos.unshift({
                            ...producto,
                            Cantidad: this.CantidadProducto,
                            CantidadOriginal: this.CantidadProducto
                        })
                        this.CantidadProducto = null;
                        this.FocoBusquedaProducto();

                    }
                })
            } else {
                this.cargos.unshift({
                    ...producto,
                    Cantidad: this.CantidadProducto,
                    CantidadOriginal: this.CantidadProducto
                })
                this.CantidadProducto = null;

            }

        },
        FiltroInventario() {
            if (this.InventarioMayorCero) {
                return this.ListadoProductos = this.ListadoProductosOriginal.filter(f => f.Existencia > 0);
            } else {
                return this.ListadoProductos = this.ListadoProductosOriginal;
            }

        },
        ValidacionInsertarCargos() {
            var ExistenSeleccionados = [];
            ExistenSeleccionados = this.ListaMedicamentosValeElec.filter(f => f.seleccion);

            if (ExistenSeleccionados.length <= 0) {
                this.VentanaMensajesGenerales = true;
                this.MensajeGeneralDato = 'No es posible cargar, debe de seleccionar al menos un medicamento!'
                return;
            }

            this.TipoHuella = 'Entrega'
            this.CorporativoEntrega = '';
            this.CorporativoRecibe = '';
            this.PermisoEntrega = 'PERMISO_ENTREGA';
            this.TipoHuella = 'Entrega';
            
            this.$refs.componenteValidarPermiso.popUpPermisosHuella = true;
            this.$refs.componenteValidarPermiso.ParametroCorporativo  = '';
            this.$refs.componenteValidarPermiso.info.Nombres  = '';
            this.$refs.componenteValidarPermiso.info.Apellidos  = '';
            this.$refs.componenteValidarPermiso.TituloPopup = 'Validación de huella Farmacia.';
        },
        InsertCargos(ValeElectronico) {

            if (this.info.medico == '') {
                this.info.medico == 'SIN REFERENCIA'
            }
            if (this.info.observaciones == '') {

                this.VentanaMensajesGenerales = true;
                this.MensajeGeneralDato = 'Seleccionar observaciones.';
                return;
            }

            if (ValeElectronico) {
                this.cargos = [];
                this.cargos = this.ListaMedicamentosValeElec.filter(f => f.seleccion == true);

            }
            //return;
            //Para otros tipos de cargos diferente a consiganción   
            const cargos0 = this.cargos.filter(f => f.TipoOrden.substr(0, 2) != 'FC')
            const cargosOtros = cargos0.map(m => {
                return {
                    Codigo: m.Codigo,
                    Cantidad: m.Cantidad,
                    Ajeno: '',
                    PrecioLibre: '0',
                    TipoOrden: m.TipoOrden,
                    ControlMedicamentos: ValeElectronico ? m.Id : 0
                }
            })

            //Tipos de cargos unicamente de consignación
            const cargos1 = this.cargos.filter(f => f.TipoOrden.substr(0, 2) == 'FC')
            const cargosFC = cargos1.map(m => {
                return {
                    Codigo: m.Codigo,
                    Cantidad: m.Cantidad,
                    Ajeno: '',
                    PrecioLibre: '0',
                    TipoOrden: m.TipoOrden,
                    ControlMedicamentos: ValeElectronico ? m.Id : 0
                }
            })

            var ListaProductos = '';
            var Categorias = '';
            var SubNiveles = '';
            var Cantidades = '';
            this.cargos.map(m => {
                if (ListaProductos == '') {
                    ListaProductos = m.Codigo.trim();
                } else {
                    ListaProductos = ListaProductos + ',' + m.Codigo.trim();
                }

                if (Categorias == '') {
                    Categorias = m.CATEGORIA.trim();
                } else {
                    Categorias = Categorias + ',' + m.CATEGORIA.trim();
                }

                if (SubNiveles == '') {
                    SubNiveles = m.SUBNIVEL.trim();
                } else {
                    SubNiveles = SubNiveles + ',' + m.SUBNIVEL.trim();
                }

                if (Cantidades == '') {
                    Cantidades = m.Cantidad;
                } else {
                    Cantidades = Cantidades + ',' + m.Cantidad;
                }
            })

            if (this.info.nivelprecio <= 0) {
                this.VentanaMensajesGenerales = true;
                this.MensajeGeneralDato = 'Nivel de precio incorrecto.';
                return
            }

            this.Pre_Validaciones(ListaProductos, Categorias, SubNiveles, Cantidades) /*********** PRE-VALIDACIONES DE CARGOS************ */
                .then((resp) => {
                    const Total = resp.data.TOTAL;
                    const NivelPrecioNuevo = resp.data.NIVEL_PRECIO;

                    this.Guardar_Cargos(cargosOtros, Total, NivelPrecioNuevo, cargosFC) /*********** REGISTRAR CARGOS************ */
                        .then((resp) => {
                            this.MensajeGeneralDato = 'Cargo(s) realizado(s): ' + resp;
                            this.Reimpresion = false;
                            //Validación para mandar a imprimir
                            if (this.Respuesta.Orden1 > 0) {
                                if (this.Respuesta.Orden2 > 0) {
                                    this.ReportePDF_IMPRESION(true, this.Respuesta.Tipo1, this.Respuesta.Orden1, this.Respuesta.Tipo2, this.Respuesta.Orden2, false).then(
                                        this.ReportePDF_IMPRESION(false, this.Respuesta.Tipo1, this.Respuesta.Orden1, '', 0, true).then(
                                            this.ReportePDF_IMPRESION(false, this.Respuesta.Tipo2, this.Respuesta.Orden2, '', 0, true)
                                        )
                                    )

                                } else if (this.Respuesta.Orden1 > 0) {
                                    this.ReportePDF_IMPRESION(true, this.Respuesta.Tipo1, this.Respuesta.Orden1, '', 0, true)
                                }
                            } else if (this.Respuesta.Orden2 > 0) {
                                this.ReportePDF_IMPRESION(true, this.Respuesta.Tipo2, this.Respuesta.Orden2, '', 0, true)
                            }

                            this.VentanaReporteCargos = true;
                            this.CargosManuales = true;
                            this.ListadoProductos = [];
                            this.ListaMedicamentosValeElec = [];
                            this.cargos = [];
                            this.LimpiarDatosAdmision();

                        });

                }).catch();

        },

        Pre_Validaciones(ListaProductos, Cantidades, SubNiveles, Categorias) {
            return new Promise((resolve, reject) => {
                this.axios.post('/app/v1_farmacia/PreGuardarCargo', {
                        SerieAdmision: this.info.serie,
                        Admision: this.info.numeroAdmision,
                        NivelPrecio: this.info.nivelprecio,
                        Productos: ListaProductos,
                        Cantidades: Cantidades,
                        Subniveles: SubNiveles,
                        Categorias: Categorias,
                        Total: this.montoTotal

                    })
                    .then(resp => {
                        if (resp.data.Codigo == 0) {
                            resolve(resp)
                        } else {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'No es posible validar los cargos.',
                            })
                            reject(new Error('Error'));
                        }
                    })
            })

        },
        Guardar_Cargos(cargosOtros, MontoTotal, NivelPrecio, cargosFC) {

            var TIPOC = '';
            if (cargosFC.length > 0) {
                TIPOC = cargosFC[0].TipoOrden;
            }
            return new Promise(resolve => {
                this.axios.post('/app/emergencia/CargarOrdenFarmacia', {
                        serie: this.info.serie,
                        codigoAdmision: this.info.numeroAdmision,
                        nivelPrecio: NivelPrecio,
                        tipoOrden: this.info.tipoOrden,
                        medico: this.info.medico && this.info.medico != '' ? this.info.medico : '1',
                        observaciones: this.info.observaciones,
                        fechaEstudio: this.info.fechaEstudio && this.info.fechaEstudio != '' ? moment(this.info.fechaEstudio, 'DD/MM/YYYY HH:mm').format('DD/MM/YYYY HH:mm') : null,
                        montoTotal: MontoTotal,
                        IdCargoTipo: 2,
                        TipoC: TIPOC,
                        CorporativoEntrega: this.CorporativoEntrega,
                        CorporativoRecibe: this.CorporativoRecibe,
                        cargos: cargosOtros,
                        cargosFC
                    }).then(resp => {
                        if (resp.data.codigo == 0) {
                            const Respuesta = resp.data ? resp.data.json[0].descripcion : resp.json.descripcion
                            this.Respuesta.Tipo1 = '';
                            this.Respuesta.Orden1 = '';
                            this.Respuesta.Tipo2 = '';
                            this.Respuesta.Orden2 = '';

                            this.Respuesta.Tipo1 = resp.data ? resp.data.json[0].RespTipo1 : '';
                            this.Respuesta.Orden1 = resp.data ? resp.data.json[0].RespOrden1 : '';

                            this.Respuesta.Tipo2 = resp.data ? resp.data.json[0].RespTipo2 : '';
                            this.Respuesta.Orden2 = resp.data ? resp.data.json[0].RespOrden2 : '';

                            resolve(Respuesta)

                        } else {
                            this.ListaMedicos = "";
                            this.MensajeGeneralDato = resp.data ? resp.data.json[0].descripcion : '';
                            this.VentanaMensajesGenerales = true;
                        }
                    })
                    .catch()
            })
        },
    }
}
</script>

<style scoped>
.cantidad button {
    height: 25px;
    width: 30px;
    border: 1px solid #ccc;
    border-radius: 5px;

}

.cantidad div {
    display: inline-block;
    height: 25px;
    width: 30px;
    text-align: center;
}

.quitar_espacio {
    padding: 0;
    margin: 0;
}

.quitar_espacio_buscar>.label {
    padding: 0 !important;
}

.Disponible {
    /* color: red;
  background-color: green; */
    font-size: 50%;
    text-align: center;
    color: white;
    border-radius: 5px;
    background-color: #2E7D32;

}

.NoDisponible {
    /* color: red;
  background-color: green; */
    font-size: 50%;
    text-align: center;
    color: white;
    border-radius: 5px;
    background-color: #BF360C;

}

.con-vs-popup {
    z-index: 53000;
}

.popNuevo {
    display: grid;
    place-items: center;
}

.popFinalizacion {
    display: grid;
    place-items: center;
}

.vs-popup--content {
    width: auto !important;
    padding: 1rem;
    font-size: 1rem;
}

.colorFechaDelDia {
    background-color: rgb(255, 255, 108);
}

.colorFechaDeOtroDia {
    background-color: rgb(202, 223, 247);
}

.PacientePrivado {

    background-color: #388E3C;
    color: white
}

.PacienteSaludSiempre {
    background-color: #F57C00;
    color: white
}

.Seguro {
    background-color: #FFEB3B;
}
</style>
