<template>
    <vx-card title="Consulta de Pacientes">
        <div class="container_div p-4">
            <div class="container">
                <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                    <form @submit.prevent="handleSubmit(submitForm())">
                        <div class="flex flex-wrap">
                            <vs-row>
                                <vs-col vs-type="flex" vs-w="6">
                                    <div class="w-4/5  p-2">
                                        <!-- <validationProvider name="Nombre Paciente"> -->
                                            <vs-input v-model="consultaPaciente.NombrePaciente" label="Nombre Paciente"
                                                class="w-full" @keyup.enter="handleSubmit(ConsultarPaciente().Consultar())"/>
                                        <!-- </validationProvider> -->
                                    </div>
                                    <div class="w-4/5  p-2">
                                        <!-- <validationProvider name="Nombre Paciente"> -->
                                        <vs-input v-model="consultaPaciente.ApellidoPaciente" label="Apellido Paciente"
                                            class="w-full" @keyup.enter="handleSubmit(ConsultarPaciente().Consultar())" />
                                        <!-- </validationProvider> -->
                                    </div>
                                </vs-col>
                                <!-- <vs-col vs-type="flex" vs-w="6">
                                    <div class="w-4/5  p-2">
                                        <validationProvider name="Documento">
                                            <vs-input v-model="consultaPaciente.NumeroDocumento" label="No. Documento"
                                                class="w-full" />
                                        </validationProvider>
                                    </div>
                                </vs-col> -->
                            </vs-row>
                            <vs-row>
                                <div class="flex w-full p-1">
                                    <div class="pl-2">
                                        <vs-button @click="handleSubmit(ConsultarPaciente().Consultar())"
                                            :disabled="invalid">
                                            Consultar
                                        </vs-button>
                                    </div>
                                    <div class="pl-2" v-if="showCreateButton">
                                        <vs-button @click="submit(null)">
                                            Crear
                                        </vs-button>
                                    </div>
                                </div>
                            </vs-row>
                        </div>
                    </form>
                </ValidationObserver>
                <vs-table2 max-items="10" search tooltip pagination :data="listaPacientes">
                    <template slot="thead">
                        <th width="50px" filtro="Paciente">Paciente</th>
                        <th width="330px" filtro="Cliente">Cliente</th>
                        <th width="330px" filtro="NombrePaciente">NombrePaciente</th>
                        <th width="20px" filtro="PorcentajeCoincidencia">PorcentajeCoincidencia</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data" :ref="`list_${indextr}`" v-on:click=submit(tr)
                            :class="{ 'activo': tr.activo }">
                            <vs-td2>
                                {{ tr.Codigo }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.IdCliente }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.NombrePaciente }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.PorcentajeCoincidencia }}
                            </vs-td2>
            
                        </tr>
                    </template>
                </vs-table2>
            </div>
        </div>
    </vx-card>
</template>
<script>


export default {    
    name: "ConsultaPacientes",
    data() {
        return {
            listaPacientes: [],
            consultaPaciente: {
                Empresa: '',
                NombrePaciente: '',
                ApellidoPaciente: '',
                NumeroDocumento: ''
            }
        }

    },
    props: {
        showCreateButton: {
            type: Boolean,
            default: true
        }
    },
    mounted() {
        this.NombrePaciente = ''
    },
    watch: {
        'consultaPaciente.NombrePaciente': function () {
            this.clearTable()
        }
    },
    methods: {
        ConsultarPaciente() {
            return {
                Consultar: async () => {
                    const resp = await this.axios.post('/app/v1_admision/ConsultaPacientePorNombre', this.consultaPaciente)


                    this.listaPacientes = resp.data.json.map(m => {
                        return {
                            ...m,
                        }
                    })
                },
            }
        },
        submit(item) {
           // console.log()
           // console.log(this.$store.getters["paciente/getPaciente"])
            this.$emit("ready", true)
            if (item) {
                this.$store.dispatch('paciente/initPaciente')
                this.$store.dispatch('paciente/setPaciente', item)
                this.$store.dispatch('workflow/guardarTipoPaso', 'EDIT')
            }
            else {
                this.$store.dispatch('workflow/guardarTipoPaso', 'CREATE')
                this.$store.dispatch('admision/initAdmision')
                this.$store.dispatch('paciente/initPaciente')
            }
        },
        clearTable() {
            this.listaPacientes = ''
        }
    }
};
</script>
<style scoped>
* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}
.container_div {
    display: grid;
    grid-gap: 10px;
    width: 100%;
    height: 100%;

}
.container_div>div {
    border: 1px solid #888;
    padding-top: 10px;
    padding-bottom: 10px;
}
.container_b {
    display: grid;
    grid-gap: 10px;
    width: 100%;
    height: 10%;
}
.container {
    max-width: 100%;
}
.radio-group.radio-group--inline div[data-radio-button] {
    width: auto;
}
.radio-group.radio-group--inline div[data-radio-button]:not(:first-of-type) {
    padding-left: var(--space-base);
}
</style>