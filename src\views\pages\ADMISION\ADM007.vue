<template>
<vx-card title="Aceptación de Admisión">

    <div v-if="!nuevo" style="border:1px solid #ccc; border-radius:10px">
        <div class="flex flex-wrap" style="padding:10px 20px">
            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <label class="vs-input--label">Busqueda</label>
                <vx-input-group class="">
                    <vs-input v-model="info.codigo" />
                    <template slot="append">
                        <div class="append-text btn-addon">
                            <button type="submit" v-show="false" name="button"></button>
                            <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_radiologias()" icon="icon-search"></vs-button>
                        </div>
                    </template>
                </vx-input-group>
            </div>
        </div>
        <div class="flex flex-wrap" style="padding:10px 20px">
            <div class="w-full ">
                <vs-table v-model="selected" @selected="handleSelected" pagination max-items="5" :data="admisiones">

                    <template slot="thead">
                        <vs-th>Serie</vs-th>
                        <vs-th>Código</vs-th>
                        <vs-th>Habitación</vs-th>
                        <vs-th>Nombres</vs-th>
                        <vs-th>Apellidos</vs-th>
                        <vs-th>Seguro</vs-th>
                        <vs-th>Status</vs-th>
                        <vs-th>Fecha Ingreso</vs-th>
                    </template>

                    <template slot-scope="{data}">
                        <vs-tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">

                            <vs-td :data="data[indextr].serie">
                                {{ data[indextr].serie }}
                            </vs-td>

                            <vs-td :data="data[indextr].codigo">
                                {{ data[indextr].codigo }}
                            </vs-td>

                            <vs-td :data="data[indextr].habitacion">
                                {{ data[indextr].habitacion }}
                            </vs-td>

                            <vs-td :data="data[indextr].nombres">
                                {{ data[indextr].nombres }}
                            </vs-td>

                            <vs-td :data="data[indextr].apellidos">
                                {{ data[indextr].apellidos }}
                            </vs-td>

                            <vs-td :data="data[indextr].seguro">
                                {{ data[indextr].seguro }}
                            </vs-td>

                            <vs-td :data="data[indextr].status">
                                {{ data[indextr].status }}
                            </vs-td>

                            <vs-td :data="data[indextr].ingreso">
                                {{ data[indextr].ingreso }}
                            </vs-td>
                        </vs-tr>
                    </template>

                </vs-table>
            </div>
        </div>
        <div class="flex flex-wrap" style="padding:10px 20px">
            <div class="w-full ">
                <vs-tabs>
                    <vs-tab label="Sumario de la Admisión">
                        <div class="tab-text">
                            <vs-button color="primary" style="float:right" type="filled" icon-pack="fas" icon="fa-print" @click="guardar_informe(true)">Imprimir</vs-button>
                            <div style="clear:both"></div>
                            <br>
                            <vue-editor class="editor" v-model="content" :editor-toolbar="customToolbar" />
                            <div style="clear:both;margin-top:45px"></div>
                        </div>
                    </vs-tab>
                    <vs-tab label="Compromiso de Pago">
                        <div class="tab-text">
                            <vs-button color="primary" style="float:right" type="filled" icon-pack="fas" icon="fa-print" @click="guardar_informe(true)">Imprimir</vs-button>
                            <div style="clear:both"></div>
                            <br>
                            <vue-editor class="editor" v-model="content" :editor-toolbar="customToolbar" />
                            <div style="clear:both;margin-top:45px"></div>
                        </div>
                    </vs-tab>
                    <vs-tab label="Consentimiento Informado">
                        <div class="tab-text">
                            <vs-button color="primary" style="float:right" type="filled" icon-pack="fas" icon="fa-print" @click="guardar_informe(true)">Imprimir</vs-button>
                            <div style="clear:both"></div>
                            <br>
                            <vue-editor class="editor" v-model="content" :editor-toolbar="customToolbar" />
                            <div style="clear:both;margin-top:45px"></div>
                        </div>
                    </vs-tab>

                </vs-tabs>

            </div>
        </div>
    </div>

    <vs-divider></vs-divider>

    <!-- <vs-button color="primary" style="float:right" type="border" @click="info.opcion.show=false" v-else> Salir</vs-button> -->
    <div style="clear:both"></div>

</vx-card>
</template>

<script>
// import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";

import {
    VueEditor
} from "vue2-editor";

export default {
    data() {
        return {
            examenes: [],
            selected: [],
            content: '',
            customToolbar: [
                []
            ],
            admisiones: [{
                serie: 'E',
                codigo: 123456,
                habitacion: 'N23',
                nombres: 'Pedro',
                apellidos: 'Rivera',
                seguro: null,
                status: 'A',
                ingreso: '01/01/2020 11:50:00'
            }],
            info: {
                invalido: false,
                nombre1: null,
                nombre2: null,
                apellido1: null,
                apellido2: null,
                casada: null,
                nacimiento: null,
                edad: null,
                dpi: null,
                telefono: null,
                sexo: null,
                estado_civil: null,
                religion: null,
                lugar_nacimiento: null,
                nacionalidad: null,
                telefonos: null,
                celular: null,
                email: null,

                profesion: null,

                direccion_factura: null,
                nombre_factura: null,
                nit: null,

                carnet: null,
                socio: null,
                pasaporte: null,
                segurosocial: null,

                direccion: null,
                ciudad: null,
                calle: null,
                avenida: null,
                casa: null,
                zona: null,
                colonia: null,
                edificio: null,
                departamento: null,
                municipio: null,

                direccion_trabajo: null,
                telefono_trabajo: null,
                ocupacion: null,
                CalleTrabajo: null,
                AvenidaTrabajo: null,
                NoCasaTrabajo: null,
                ZonaTrabajo: null,
                ColoniaTrabajo: null,
                EdificioTrabajo: null,
                OficinaTrabajo: null,
                DeptoTrabajo: null,
                MunicipioTrabajo: null,

                padre: null,
                madre: null,
                conyuge: null,
            },
            Tipos: [{
                    Id: 1,
                    Nombre: 'Interno'
                },
                {
                    Id: 2,
                    Nombre: 'Emergencia'
                },
                {
                    Id: 3,
                    Nombre: 'IGSS'
                },
                {
                    Id: 4,
                    Nombre: 'RN en Hospital'
                },
                {
                    Id: 5,
                    Nombre: 'Paquete'
                },
            ],
            EstadosCiviles: [{
                    Id: 'C',
                    Nombre: 'Casado(a)'
                },
                {
                    Id: 'D',
                    Nombre: 'Divorsiado(a)'
                },
                {
                    Id: 'S',
                    Nombre: 'Soltero(a)'
                },
                {
                    Id: 'U',
                    Nombre: 'Unido(a)'
                },
                {
                    Id: 'V',
                    Nombre: 'Viudo(a)'
                },
            ]
        }
    },
    props: {
        paciente: {
            default: null
        },
        nuevo: {
            default: false
        }
    },
    components: {
        VueEditor,
        // Multiselect
    },
    methods: {

        cargar_estudios() {
            this.axios.post('/app/admision/Medicamento', {
                    Corporativo: 0
                })
                .then(resp => {
                    
                    this.examenes = []
                    resp.data.json.map(data => {
                        this.examenes.push({
                            ControlMedicamento: data.ControlMedicamento,
                            Admision: data.Admision,
                            Codigo: data.Codigo,
                            FechaAsignada: data.FechaAsignada,
                            Frecuencia: data.Frecuencia,
                            DIAS: data.DIAS,
                            Serie: data.Serie,
                            Paciente: data.Paciente,
                            Tipo: data.tipo
                        })
                    })

                })
        },

        handleSelected(tr) {
            
            this.$vs.notify({
                title: `Selected ${tr.serie}`,
                text: `Email: ${tr.codigo}`
            })
        }
    },
    created() {

    }
}
</script>

<style scoped>
.center li {
    display: inline !important;
    padding: 2px 10px;
}

.button {
    margin: 0px !important;
}

.btnx {
    margin-left: 10px !important;
    background: red;
    border-radius: 5px 0px 0px 5px;
}

.btn-drop {
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: red;

}

.menu-funcionalidad {
    /* padding-top:0 !important; */
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    padding: 15px;
    display: inline;
    float: right;
    border-radius: 10px;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 10px;
}

.menu-funcionalidad td {
    text-align: center;
    padding-right: 10px;
}

.vs-switch {
    height: 30px;
    width: 60px !important;
}

.vs-circle-switch {
    height: 25px;
    width: 25px;
}

.vs-switch--icon {
    font-size: 15px
}

.vs-switch.vs-switch-active .vs-switch--circle {
    margin-left: calc(100% - 30px);
}

.editor {
    height: 350px;
}
</style>
