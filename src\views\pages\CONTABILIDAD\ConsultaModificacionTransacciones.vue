<template>
    <div>
        <form @submit="handleSubmit">
            <DxForm :ref="formTransaccion" :form-data.sync="formulario" label-mode="floating" style="margin: 5px">
                <DxFormGroupItem :col-count="2">
                    <DxFormGroupItem>
                        <DxFormItem data-field="Cuenta" editor-type="dxSelectBox" :editor-options="{
                            width: 'auto',
                            searchEnabled: true,
                            items: cuentas,
                            displayExpr: 'Valor',
                            valueExpr: 'Codigo',
                          
                            onValueChanged: AsignarSiguienteCheque,
                            }" :validationRules="[{ type: 'required' }]" />
                        <DxFormGroupItem >
                            <DxFormItem template="Alerta" />
                        </DxFormGroupItem>
                        <DxFormGroupItem :col-count="2">
                            <DxFormItem template="Buttons"  />
                            <DxFormButtonItem v-if="fechas && fechas.length > 0" horizontal-alignment="center" verical-alignment="center" :button-options="buttonGrabar" />
                        </DxFormGroupItem>
                    </DxFormGroupItem>

                    <DxFormGroupItem>
                        <DxFormItem v-if="fechas && fechas.length > 0" template="Parametros" />
                    </DxFormGroupItem>

                </DxFormGroupItem>
                <DxFormGroupItem captio=" ">
                </DxFormGroupItem>
                <template #Parametros="{}">
                    <div class="pb-2">
                        <DxDataGrid :data-source="filteredDatos" :show-borders="true">
                            <DxDataGridColumn data-field="SaldoInicial" data-type="number" caption="Saldo Inicial" :customize-text="customizeTextValores" />
                            <DxDataGridColumn data-field="Debitos" data-type="number" caption="Débitos" :customize-text="customizeTextValores" />
                            <DxDataGridColumn data-field="Creditos" data-type="number" caption="Créditos" :customize-text="customizeTextValores" />
                            <DxDataGridColumn data-field="SaldoFinal" data-type="number" caption="Saldo" :customize-text="customizeTextValores" />
                            <DxDataGridPaging :page-size="1" />
                        </DxDataGrid>
                    </div>
                </template>
                <template #Buttons="{}">
                    <div  v-if="fechas && fechas.length > 0" style="display: flex; width: 100%;">
                        <DxButton id="saveButtonConsuta" style="margin-right: 2px;" :disabled="selectedIndex === 0" icon="fas fa-step-backward" type="normal" @click="irPrimero" />
                        <DxButton id="saveButtonDosConsulta" style="margin-right: 2px;" :disabled="selectedIndex === 0" icon="fas fa-caret-left" type="normal" @click="irAnterior" />
                        <DxButton id="saveButtonDosConsulta" style="margin-right: 2px;"  icon="fas fa-caret-right" :disabled="selectedIndex === fechas.length - 1" @click="irSiguiente" />
                        <DxButton id="saveButtonConsuta" icon="fas fa-step-forward" :disabled="selectedIndex === fechas.length - 1" @click="irUltimo" />

                    </div>
                </template>
                <template  #Alerta="{}">
                    <div>
                        <vs-alert color="danger"  v-if="!fechas || fechas.length === 0" active="true" style="text-align: center;">
                            <p> Sin datos</p>
                        </vs-alert>
                    </div>
                </template>
            </DxForm>
        </form>
        <div style="display: flex;" v-if="fechas && fechas.length > 0">
            <div>
                <!-- Tabla -->
                <DxDataGrid
                    ref="dataGridRef"
                    class="custom-dx-grid"
                    :data-source="fechas"
                    width="200px"
                    height="135px"
                    key-expr="Fecha"
                    :focused-row-enabled="true"
                    :focused-row-index="selectedIndex"
                    :show-borders="true"
                    @selection-changed="onSelectionChanged"
                >
                    <DxDataGridColumn data-field="Fecha" data-type="date" caption="Periodo" />
                    <DxDataGridScrolling mode="virtual" />
                    
                </DxDataGrid>
            </div>
            &nbsp;
            <div v-if="fechas && fechas.length > 0">
                <DxDataGrid :data-source="datos" :show-borders="true"   :word-wrap-enabled="true"  :column-auto-width="true" :height="400">
                    <DxDataGridColumn data-field="Fecha" data-type="date" caption="Fecha" />
                    <DxDataGridColumn data-field="Tipo" data-type="string" caption="Tipo" />
                    <DxDataGridColumn data-field="Numero" data-type="number" caption="Número" />
                    <DxDataGridColumn data-field="Observaciones" data-type="string" caption="Detalle" />
                    <DxDataGridColumn data-field="Monto" data-type="number" caption="Valor"/>
                    <DxDataGridScrolling mode="infinite" />
                </DxDataGrid>

            </div>
        </div>
    </div>
</template>

<script>
const formTransaccion = "formTransaccion";

export default {
    data(){
        return{
            formulario:{
                Cuenta:null
            },

            Conceptos: {
                SaldoInicial: 0,
                Debitos: 0,
                Creditos: 0,
                SaldoFinal:0,
            },

            formTransaccion: null,
            isGridDisabled: true,

            cuentas: [],
            fechas:[],
            selectedFechas: [],
            datos:[],
            selectedIndex: 0,

            buttonGrabar: {
                width: "auto",
                icon: "fas fa-trash",
                hint: "Eliminar Conciliación",
                type: "danger",
                text: "Eliminar",
                onClick: () => {
                    this.EliminarConciliacion()
                },
                useSubmitBehavior: false,
            },
        }
    },
    computed: {
        formTransformAnticipoInstanceaccion: function () {
            return this.$refs[formTransaccion].instance;
        },

        filteredDatos() {
            return this.fechas.filter(dato =>
                this.selectedFechas.includes(dato.Fecha)
            );
        },
        selectedCuenta: {
            get() {
                return this.formulario.Cuenta;
            },
            set(value) {
                this.formulario.Cuenta = value;
            },
        },
    },
    methods:{
        handleSubmit(e) {
            e.preventDefault();
        },

        CargarCuentas(asignar) {
            // La variable de asignar indica cuando debe seleccionar la primera cuenta de la lista
            this.axios.post("/app/v1_bancos/Cuentas", {
             Opcion: 1,
            }, { skipLoading: true })
            .then((resp) => {
                this.cuentas = resp.data.json;
                if (asignar) {
                    
                    
                    this.AsignarBanco(this.cuentas[0]);
                }
            });
        },

        AsignarSiguienteCheque(e) {
            this.fechas = []
            this.datos = []
            
            let cuenta = this.cuentas.find((x) => x.Codigo == e.value);
            this.AsignarBanco(cuenta);
            
            this.CargarDatos(cuenta);
        },

        AsignarBanco(e) {
            //Asignar los valores del banco a los campos correspondientes del formulario
            this.formulario.Cuenta = e.Codigo;
        },


        CargarDatos(e) {
            this.axios.post("/app/v1_bancos/DatosConsultaTransacciones", {
                Opcion: 4,
                SubOpcion: 1,
                Cuenta: e.Codigo,
            }, { skipLoading: true })
            .then((resp) => {
                if (resp.data.codigo === 0) {
                    this.fechas = resp.data.json;

                    this.$nextTick(() => {
                        if (this.fechas.length > 0) {
                            this.seleccionarFila(0); // 👉 selecciona la primera fila automáticamente
                        }
                    });
                }
            });
        },

        onSelectionChanged(e) {
            this.selectedFechas = [];

            let selectedFecha = e.selectedRowsData[0]?.Fecha;
            if (selectedFecha) {
                if (!this.selectedFechas.includes(selectedFecha)) {
                    this.selectedFechas.push(selectedFecha);
                }
                this.selectedIndex = e.component.getRowIndexByKey(e.selectedRowKeys[0]);
                this.CargarDetalle(e);
            }
        },


        seleccionarFila(index) {
            if (index >= 0 && index < this.fechas.length) {
                const gridInstance = this.$refs.dataGridRef.instance;
                gridInstance.selectRowsByIndexes([index]);
                gridInstance.option('focusedRowIndex', index);

                const selectedRowData = this.fechas[index];
                if (selectedRowData) {
                    this.selectedFechas = [selectedRowData.Fecha];
                    this.selectedIndex = index;

                    this.$nextTick(() => {
                        gridInstance.navigateToRow(selectedRowData.Fecha);
                    });

                    this.CargarDetalle({
                        selectedRowsData: [selectedRowData],
                        selectedRowKeys: [selectedRowData.Fecha],
                        component: gridInstance
                    });
                }
            }
        },





        CargarDetalle(e) {
            let fechaSeleccionada = e.selectedRowsData[0]?.Fecha;
            if (!fechaSeleccionada) return; // Evita la llamada si no hay fecha

            this.axios.post("/app/v1_bancos/ConsultaDetalleConcicliaciones", {
                Opcion: 4,
                SubOpcion: 2,
                Cuenta: this.formulario.Cuenta,
                Fecha: fechaSeleccionada
            }, { skipLoading: true })
            .then((resp) => {
                if (resp.data.codigo === 0) {
                    this.datos = resp.data.json;
                }
            })
            .catch(() => {
            });
        },

        
        customizeTextValores(cellInfo) {
            if (cellInfo.value !== null) {
                let formattedValue = cellInfo.value.toLocaleString('en-US', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                });
                return "Q. " + formattedValue;
            }
            return null;
        },



        EliminarConciliacion() {
            // Mostrar la confirmación antes de proceder con la eliminación
            this.$vs.dialog({
                type: 'confirm',
                color: '#e74c3c',
                title: 'Confirmación de eliminación',
                acceptText: 'Eliminar',
                cancelText: 'Cancelar',
                text: `¿Estás seguro de que deseas eliminar la conciliación?`,
                buttonCancel: 'border',
                clientWidth: 100,
                accept: () => {
                    // Solo si el usuario acepta, realizamos el axios para eliminar
                    this.axios.post("/app/v1_bancos/EliminarConciliacion", {
                        Opcion: 4,
                        SubOpcion: 3,
                        Cuenta: this.formulario.Cuenta,
                        Fecha: this.selectedFechas[0]
                    }, { skipLoading: true })
                    .then((resp) => {
                        if (resp.data.codigo === 0) {
                            // Si la eliminación fue exitosa, muestra una notificación
                            this.$vs.notify({
                                text: 'Conciliación Eliminada Exitosamente.',
                                iconPack: 'feather',
                                icon: 'icon-alert-circle',
                                color: 'danger'
                            });

                            this.CargarCuentas(true);
                            this.selectedFechas = [];
                        } else {
                            // En caso de error, puedes mostrar un mensaje diferente
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#eb984e',
                                title: 'Error',
                                text: 'Hubo un error al intentar eliminar la conciliación.',
                                position: 'top-center',
                                duration: 3000
                            });
                        }
                    })
                    .catch(() => {
                        // Manejo de errores en caso de fallo en la solicitud
                        this.$vs.notification({
                            color: 'danger',
                            title: 'Error',
                            text: 'No se pudo realizar la solicitud.',
                            position: 'top-center',
                            duration: 3000
                        });
                    });
                },
                cancel: () => {
                    // Si el usuario cancela, no hacer nada
                }
            });
        },

        irPrimero() {
            this.seleccionarFila(0);
        },

        irAnterior() {
            if (this.selectedIndex > 0) {
                this.seleccionarFila(this.selectedIndex - 1);
            }
        },

        irSiguiente() {
            if (this.selectedIndex < this.fechas.length - 1) {
                this.seleccionarFila(this.selectedIndex + 1);
            }
        },


        irUltimo() {
            const ultimoIndex = this.fechas.length - 1;
            this.seleccionarFila(ultimoIndex);

            // Asegura el scroll visual al último elemento
            const ultimaFecha = this.fechas[ultimoIndex]?.Fecha;
            if (ultimaFecha) {
                this.$refs.dataGridRef.instance.navigateToRow(ultimaFecha);
            }
        },




    },
    beforeMount() {
        this.CargarCuentas(true);
    },
    
}
</script>


<style>


#saveButtonConsuta .dx-icon {
    font-size: 17px;
    color: black;

}

#saveButtonConsuta{
    width: 60px !important;
}

#saveButtonDosConsulta .dx-icon {
    font-size: 25px;
    color: black;
}

#saveButtonDosConsulta{
    width: 60px !important;
}

/* Puedes colocarlo en tu componente o en un archivo CSS global */
.custom-dx-grid .dx-row-focused {
  background-color: #007bff !important; /* Cambia esto al color que prefieras */
  color: white !important;
}

.custom-dx-grid .dx-selection {
  background-color: #007bff !important;
  color: white !important;
}




</style>