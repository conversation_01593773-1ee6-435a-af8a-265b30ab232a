<template>
<div>
    <!-- {{info}} -->
    <buscador ref="buscar_aplicativo" buscador_titulo="Buscador / Aplicativo" :api="list_aplicativo" campo_llave="IdAplicativo" :campos="['Nombre']" :titulos="['Aplicativo']" :multiselect="false" :api_validar_campo="true" />
    <buscador ref="buscar_funcionalidad" buscador_titulo="Buscador / Funcionalidad" :api="list_funcionalidad" campo_llave="IdFuncionalidad" :campos="['Nombre']" :titulos="['Funcionalidad']" :multiselect="false" :api_validar_campo="true" />

    <vs-popup :active.sync="exportar.mostrar" title="Exportar">
        <textarea name="" id="" cols="30" rows="10" style="width:100%;height:400px;resize: none;" readonly :value="exportar.json"></textarea>
    </vs-popup>

    <vs-popup :active.sync="reporte_nuevo" title="Nuevo Parametro">
        <ValidationObserver ref="formValidate" v-slot="{ invalid,handleSubmit }" mode="lazy">
            <ValidationProvider name="Etiqueta" rules="required" v-slot="{ errors }">
                <vs-input label="Etiqueta" class="w-full" v-model="parametros.Etiqueta" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
            </ValidationProvider>
            <vs-row>
                <ValidationProvider name="Parametros" rules="required" v-slot="{ errors }" class="w-full pr-2  sm:w-2/5">
                    <vs-input label="Parametro" class="w-full" v-model="parametros.Parametro" @change="parametros.Parametro=parametros.Parametro.replace(/ /g, '')" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                </ValidationProvider>
                <ValidationProvider name="Tipo" rules="required" v-slot="{ errors }" class="w-full pr-2  sm:w-1/5">
                    <vs-select v-model="parametros.Tipo" label="Tipo" class="w-full" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null">
                        <vs-select-item :key="index" :value="item" :text="item" v-for="(item,index) in listado_tipologia" />
                    </vs-select>
                </ValidationProvider>
                <vs-input label="Valor Defecto" class="w-full pr-2 sm:w-1/5" v-model="parametros.ValorDefecto" />

                <div class="sm:w-1/5">
                    <label for="" style="color:#777;font-size:12px">Obligatorio</label>
                    <vs-switch v-model="parametros.Obligatorio" style="margin:auto" />
                </div>
            </vs-row>
            <vs-textarea label="Parametros de Busqueda" class="w-full mb-4 mt-4" v-model="parametros.Eval" style="margin-bottom:0" />
            <vs-row>
                <vs-spacer></vs-spacer>
                <vs-button color="primary" icon-pack="feather" icon="icon-plus" style="margin-right:2pxd" @click.native="handleSubmit(()=>nuevoParametro())" :disabled="invalid">Agregar Parametro</vs-button>
            </vs-row>
        </ValidationObserver>
    </vs-popup>

    <vs-popup :title="(info.IdReporte)?'Editar Reporte':'Nuevo Reporte'" :active.sync="reporte_nuevoEditar">
        <form v-on:submit.prevent="cargar_orden()">
            <div class="flex flex-wrap">
                <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                    <vs-input label="Nombre" class="w-full" v-model="info.nombre" />
                </div>
                <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
                    <vs-input label="URL" class="w-full" v-model="info.url" />
                </div>
            </div>
            <div class="flex flex-wrap">
                <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                    <label class="vs-input--label">Aplicación</label>
                    <vx-input-group class>
                        <vs-button id="button-with-loading" class="w-full" color="primary" v-on:click="buscar_aplicativos()">{{info.Aplicativo}}</vs-button>
                    </vx-input-group>
                    </div>  
                </div>
                <!-- <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 ml-5"> -->
                <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                    <label class="vs-input--label">Visible</label>
                    <vs-switch v-model="info.VisibilidadReporte" />
                </div>            
            </div>  
            <vs-divider />
            <vs-table2 :data="info.listado_parametros">
                <template slot="header">
                    <vs-row class="mb-5">
                        <vs-spacer></vs-spacer>
                        <vs-button color="primary" icon-pack="feather" icon="icon-plus" style="margin-right:2pxd" @click="reporte_nuevo = true">Agregar Parametro</vs-button> <!-- <vs-button color="primary" icon-pack="feather" icon="icon-edit" style="margin-right:2pxd" @click="nuevoParametro()" v-else>Editar Parametro</vs-button> -->
                    </vs-row>
                </template>
                <template slot="thead">
                    <th>Etiqueta</th>
                    <th>Parametro</th>
                    <th>TipoParametro</th>
                    <th>Defecto</th>
                    <th width="350px">Eval</th>
                    <th>*</th>
                    <th width="150px">Acciones</th>
                </template>
                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">
                        <vs-td2>{{tr.Etiqueta}}</vs-td2>
                        <vs-td2>{{tr.Parametro}}</vs-td2>
                        <vs-td2>{{tr.Tipo}}</vs-td2>
                        <vs-td2>{{tr.ValorDefecto}}</vs-td2>
                        <vs-td2>{{tr.Eval}}
                            <div>
                                <small>{{tr.MensajeError}}</small>
                            </div>
                        </vs-td2>
                        <vs-td2>
                            <i style="font-size:20px" class="far fa-check-square" v-if="tr.Obligatorio==1"></i>
                            <i style="font-size:20px" class="far fa-square" v-else></i>

                        </vs-td2>
                        <vs-td2 width="110px">
                            <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" @click="editar_parametro(indextr)"></vs-button>
                            <vs-button color="danger" style="margin-left:1px;display:inline-block;" v-on:click="eliminar_parametro(indextr)" icon-pack="fas" icon="fa-trash"></vs-button>
                        </vs-td2>
                    </tr>
                </template>
            </vs-table2>
            <vs-divider />
            <vs-button @click="exportar_reporte()">Exportar</vs-button>
            <vs-button style="float:right" @click="guardar_reporte()" v-if="!info.IdReporte">Guardar</vs-button>
            <vs-button color="primary" style="float:right" type="filled" icon-pack="feather" icon="icon-save" @click="editar_reporte()" v-else>Editar</vs-button>
            <div style="clear:both"></div>
        </form>
    </vs-popup>
</div>
</template>

<script>
export default {
    data() {
        return {
            reporte_nuevoEditar: false,
            reporte_editar: false,
            reporte_nuevo: false,

            listado_aplicativo: [],
            listado_reportes: [],
            listado_tipologia: [
                'Texto',
                'Numero',
                'Fecha',
                'Fecha-ISO',
                'Fecha/Hora',
                'Fecha/Hora-ISO',
                'Checkbox',
                'Oculto'
            ],
            info: {
                IdReporte: null,
                nombre: null,
                url: null,
                idAplicativo: 0,
                Aplicativo: "--",
                idFuncionalidad: 0,
                Funcionalidad: "--",
                listado_parametros: [],
                VisibilidadReporte: true
            },
            exportar:{
                mostrar:false,
                json:null
            },
            parametros: {
                Index: null,
                Etiqueta: null,
                Parametro: null,
                Tipo: null,
                Eval: null,
                MensajeError: null,
                Obligatorio: false,
                ValorDefecto: null
            }
        }
    },
    props: {
        infoTemp: {
            default: []
        },
        parametrosTemp: {
            default: []
        },
        callback: {
            default: null
        }
    },
    computed: {
        list_aplicativo() {
            let arr = [];
            this.listado_aplicativo.map(data => {
                arr.push({
                    IdAplicativo: data.IdAplicativo,
                    Nombre: data.Nombre
                });
            });
            return arr;
        },
        list_funcionalidad() {
            let arr = [];
            this.listado_aplicativo
                .filter(data => data.IdAplicativo == this.info.idAplicativo)
                .map(data => {
                    data._level.map(data => {
                        arr.push({
                            IdFuncionalidad: data.IdFuncionalidad,
                            Nombre: data.Nombre_Funcionalidad
                        });
                    });
                });
            return arr;
        }
    },
    watch: {
        reporte_nuevoEditar() {
            this.info = this.infoTemp
            this.parametros = this.parametrosTemp
        }
    },
    methods: {
        lista_aplicativos() {
            this.axios
                .post("/app/administracion/obtener_aplicativos", {})
                .then(resp => {
                    this.listado_aplicativo = resp.data.json;
                });
        },
        buscar_aplicativos() {
            this.$refs.buscar_aplicativo.iniciar(data => {
                if (data != null) {
                    this.info.idAplicativo = data.IdAplicativo;
                    this.info.Aplicativo = data.Nombre;
                    this.info.idFuncionalidad = 0;
                    this.info.Funcionalidad = "--";
                }
            });
        },

        nuevoParametro() {
            if (this.parametros.Index == null) {
                this.info.listado_parametros.push({
                    ...this.parametros
                });
            } else {
                this.info.listado_parametros[this.parametros.Index].Etiqueta = this.parametros.Etiqueta
                this.info.listado_parametros[this.parametros.Index].Parametro = this.parametros.Parametro
                this.info.listado_parametros[this.parametros.Index].Tipo = this.parametros.Tipo
                this.info.listado_parametros[this.parametros.Index].Eval = this.parametros.Eval
                this.info.listado_parametros[this.parametros.Index].MensajeError = this.parametros.MensajeError
                this.info.listado_parametros[this.parametros.Index].Obligatorio = (this.parametros.Obligatorio) ? 1 : 0
                this.info.listado_parametros[this.parametros.Index].ValorDefecto = this.parametros.ValorDefecto
            }

            this.parametros.Index = null
            this.parametros.Etiqueta = null
            this.parametros.Parametro = null
            this.parametros.Tipo = null
            this.parametros.Eval = null
            this.parametros.MensajeError = null
            this.parametros.Obligatorio = false
            this.parametros.ValorDefecto = null
            this.reporte_nuevo = false
        },
        editar_parametro(index) {
            let a = this.info.listado_parametros[index]
            this.parametros.Index = index
            this.parametros.Etiqueta = a.Etiqueta
            this.parametros.Parametro = a.Parametro
            this.parametros.Tipo = a.Tipo
            this.parametros.Eval = a.Eval
            this.parametros.MensajeError = a.MensajeError
            this.parametros.Obligatorio = (a.Obligatorio == 1) ? true : false
            this.parametros.ValorDefecto = a.ValorDefecto
            this.reporte_nuevo = true
        },

        eliminar_parametro(index) {
            this.info.listado_parametros.splice(index, 1)
        },

        guardar_reporte() {
            this.axios
                .post("/app/reporte/ReporteNuevo", {
                    Nombre: this.info.nombre,
                    Url: this.info.url,
                    IdAplicativo: this.info.idAplicativo,
                    IdFuncionalidad: this.info.idFuncionalidad,
                    Visible: this.info.VisibilidadReporte,
                    Parametros: this.info.listado_parametros.map(data => {
                        return {
                            Etiqueta: data.Etiqueta,
                            Parametro: data.Parametro,
                            Tipo: data.Tipo,
                            Eval: data.Eval,
                            MensajeError: data.MensajeError,
                            Obligatorio: (data.Obligatorio == true || data.Obligatorio == 1) ? 1 : 0,
                            Defecto: data.ValorDefecto,
                        }

                    })
                })
                .then(() => {
                    this.callback()
                    this.reporte_nuevoEditar = false;
                });
        },

        exportar_reporte(){
            this.exportar.mostrar= true
            const e =  {
                    nombre:this.info['nombre'],
                    url:this.info['url'],
                    idAplicativo:this.info['idAplicativo'],
                    Aplicativo:this.info['Aplicativo'],
                    idFuncionalidad:this.info['idFuncionalidad'],
                    Funcionalidad:this.info['Funcionalidad'],
                    listado_parametros:this.info['listado_parametros'],
                }
            this.exportar.json = JSON.stringify(e)
        },

        editar_reporte() {
            this.axios
                .post("/app/reporte/ReporteEditar", {
                    IdReporte: this.info.IdReporte,
                    Nombre: this.info.nombre,
                    Url: this.info.url,
                    IdAplicativo: this.info.idAplicativo,
                    IdFuncionalidad: this.info.idFuncionalidad,
                    Visible: this.info.VisibilidadReporte,
                    Parametros: this.info.listado_parametros.map(data => {
                        return {
                            Etiqueta: data.Etiqueta,
                            Parametro: data.Parametro,
                            Tipo: data.Tipo,
                            Eval: data.Eval,
                            MensajeError: data.MensajeError,
                            Obligatorio: (data.Obligatorio == true || data.Obligatorio == 1) ? 1 : 0,
                            Defecto: data.ValorDefecto
                        }

                    })
                })
                .then(() => {
                    this.callback()
                    this.reporte_nuevoEditar = false;
                });
        },

    },
    created() {
        this.lista_aplicativos();

    }
}
</script>
