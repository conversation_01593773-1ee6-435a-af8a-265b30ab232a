<template>
<div class="afiliado-ficha-container">
    <buscador class="ficha-afiliado-cliente-buscador" ref="buscador" buscador_titulo="Buscador / Clientes" api="app/v1_afiliados/BusquedaCliente" :campos="['IdCliente', 'IdPaciente', 'NombreCompleto', 'PrimerNombre', 'SegundoNombre', 'PrimerApellido', 'SegundoApellido', 'Sexo','DocumentoDPI', 'Pasaporte', 'FechaNacimientoFormat']" :titulos="['Id Cliente', 'Id Paciente',  '#Nombre', 'Primer Nombre', 'Segundo Nombre', 'Primer Apellido', 'SegundoApellido', '#Sexo', 'DPI', 'Pasaporte', '#Fecha Nacimiento', ]" :multiselect="false" :api_validar_campo="true" :api_preload="false" :api_cache="false" :api_disable_seleccion="null" @close="$emit('busqueda-cliente', false)" />

    <form @submit="handleSubmit">
        <DxForm css-class="afiliado-form" :ref="REFFORM" :form-data.sync="FormData" label-location="left" label-mode="floating" validation-group="validacionAfiliado" :col-count="2" :col-count-by-screen="{sm:1, md:2}" required-message="{0} es obligatorio" :disabled="false" :read-only="Modo=='LECTURA'" :show-validation-summary="true" @content-ready="onContentReady">
            <DxFormGroupItem css-class="box" caption="Ficha del Afiliado" :col-count-by-screen="{xs: 2, sm:4, md:6, lg:6}" :col-count="6" :col-span="2">
                <DxFormSimpleItem data-field="NombreContrato" caption="Contrato" editor-type="dxTextBox" :editor-options="{readOnly:true}" :col-span="2" />
                <DxFormSimpleItem data-field="IdPlan" caption="Plan" :col-span="2" :is-required="Modo == 'NUEVO'">
                    <template #default>
                        <div>
                            <SeleccionPlan @value-change="onIdPlanChange" :IdPlan="FormData.IdPlan" :editor-options="{label:'Plan', labelMode:'floating', disabled: Modo != 'NUEVO', }" :PlanList="CatalogoPlanes" :validator="{validationGroup: 'validacionAfiliado', validationRules:[{type: 'required', message: 'Seleccione Plan'}]}"/>
                        </div>
                    </template>
                </DxFormSimpleItem>
                <DxFormSimpleItem data-field="IdPoliza" :label="{text:'Póliza'}" editor-type="dxTextBox" :editor-options="{readOnly:true}" />
                <DxFormSimpleItem data-field="NumeroAdhesion" :label="{text:'Número de Adhesión'}" :validation-rules="adhesionRules" editor-type="dxTextBox" :editor-options="{readOnly: Modo != 'NUEVO'}" />
                <DxFormSimpleItem data-field="IdAfiliado" editor-type="dxTextBox" :editor-options="{readOnly:true}" />
                <DxFormSimpleItem data-field="IdCliente" template="idcliente" />
                <DxFormSimpleItem data-field="IdPaciente" template="idpaciente"  />
                <DxFormSimpleItem data-field="Status" :editor-options="{readOnly:true}" :col-span="1" />
                <DxFormSimpleItem data-field="FechaInicioCobertura" :validation-rules="inicioCoberturaRules" editor-type="dxDateBox" :editorOptions="{ dataType: 'date', min: new Date(1990,0,1), displayFormat: 'dd/MM/yyyy', readOnly: !['NUEVO'].includes(Modo) }" />
                <DxFormSimpleItem v-if="Modo=='DEPENDIENTE'" :label="{text:'Parentesco'}" data-field="IdParentesco" :validation-rules="[{ type: 'stringLength', max: 2 }, ValidationRules.Parentesco]" editor-type="dxSelectBox" :editor-options="{dataSource: ParentescosPermitidos, displayExpr: 'Descripcion', valueExpr: 'Codigo', readOnly: false}" />
                <DxFormSimpleItem v-else data-field="Parentesco" editor-type="dxTextBox" :editor-options="{readOnly:true}" />
            </DxFormGroupItem>
            <DxFormTabbedItem ref="tabitem" :tab-panel-options="tabPanelOptions" :col-span="2" :col-count="2">
                <DxFormTab icon="fas fa-address-card" title="Datos Generales" :visible="false">
                    <DxFormGroupItem caption="Datos Generales" :col-count="2" :col-span="2">

                        <DxFormSimpleItem data-field="Nombre1" :label="{text:'Primer Nombre'}" :validation-rules="[{ dxTabIndex: 0, type: 'stringLength', max: 30, min: 2, message: RangeMessage('Primer Nombre', 2, 30), }]" editor-type="dxTextBox" :editorOptions="{ readOnly: DisabledItems?.Nombre1??false }" css-class="uppercase" />
                        <DxFormSimpleItem data-field="Nombre2" :label="{text:'Segundo Nombre'}" :validation-rules="[{ dxTabIndex: 0, type: 'stringLength', max: 30, min:2, ignoreEmptyValue:true, message: RangeMessage('Segundo Nombre',2 ,30)  }]" editor-type="dxTextBox" :editorOptions="{ readOnly: DisabledItems?.Nombre2??false }" css-class="uppercase" />
                        <DxFormSimpleItem data-field="Apellido1" :label="{text:'Primer Apellido'}" :validation-rules="[{ dxTabIndex: 0, type: 'stringLength', max: 30, min: 2, message: RangeMessage('Primer Apellido', 2, 30) }]" editor-type="dxTextBox" :editorOptions="{ readOnly: DisabledItems?.Apellido1??false }" css-class="uppercase" />
                        <DxFormSimpleItem data-field="Apellido2" :label="{text:'Segundo Apellido'}" :validation-rules="[{ dxTabIndex: 0, type: 'stringLength', max: 30, min: 2, ignoreEmptyValue:true, message: RangeMessage('Segundo Apellido', 2, 30)}]" editor-type="dxTextBox" :editorOptions="{ readOnly: DisabledItems?.Apellido2??false }" css-class="uppercase" />
                        <DxFormSimpleItem data-field="ApellidoCasada" :validation-rules="[{ dxTabIndex: 0, type: 'stringLength', max: 30, min:2, ignoreEmptyValue:true, message: RangeMessage('Apellido de Casada', 2, 30) }]" editor-type="dxTextBox" :editorOptions="{ readOnly: DisabledItems?.ApellidoCasada??false }" css-class="uppercase" />
                        <!-- descomentar si si requiere registrar
                        <DxFormSimpleItem data-field="DocumentoOrden" :validation-rules="[{ type: 'stringLength', max: 4 }]" />
                        <DxFormSimpleItem data-field="DocumentoRegistro" :validation-rules="[{ type: 'stringLength', max: 20 }]" /> 
                        -->
                        <DxFormSimpleItem data-field="DPI" :validation-rules="[{dxTabIndex: 0, ...ValidationRules.Dpi}, { dxTabIndex: 1, type: 'stringLength', max: 15, min: 0 }]" :editor-options="{mask:'0000 00000 0000'}" />
                        <DxFormSimpleItem data-field="FechaNacimiento" :validation-rules="[{dxTabIndex: 0, type: 'required', message: RangeMessage('Fecha de nacimiento obigatoria')}]" editor-type="dxDateBox" :editorOptions="{ dataType: 'date', max: new Date(), displayFormat: 'dd/MM/yyyy' }" />
                        <DxFormSimpleItem data-field="EstadoCivil" editor-type="dxSelectBox" :editor-options="{dataSource: CatalogoEstadoCivil, displayExpr: 'Nombre', valueExpr: 'Codigo',}" />
                        <DxFormSimpleItem data-field="Genero" :label="{text:'Sexo'}" editor-type="dxSelectBox" :editor-options="{dataSource: CatalogoSexos, displayExpr: 'Nombre', valueExpr: 'Codigo',}" :validation-rules="[{dxTabIndex: 0, type: 'required', message: 'Seleccione sexo'}]" />
                        <DxFormSimpleItem data-field="Nit" :label="{text:'NIT'}" :validation-rules="[ {dxTabIndex: 0,...ValidationRules.Nit}, ]" css-class="uppercase" />
                        <DxFormSimpleItem data-field="Pasaporte" :validation-rules="[ { dxTabIndex: 0, type: 'stringLength', max: 50, message: RangeMessage('Pasaporte', null, 50) }]" />
                        <DxFormSimpleItem data-field="LugarNacimiento" :label="{text:'País de Nacimiento'}" editor-type="dxSelectBox" :col-span="1" :editor-options="{ dataSource: CatalogoPaises, displayExpr: 'Nombre', valueExpr: 'Id',  searchEnabled: true, searchTimeout: 200,}" />
                        <DxFormSimpleItem data-field="Nacionalidad" editor-type="dxSelectBox" :col-span="1" :editor-options="{ dataSource: CatalogoPaises, displayExpr: 'Nombre', valueExpr: 'Id',  searchEnabled: true, searchTimeout: 200,}" />
                        <DxFormSimpleItem data-field="AfiliacionIGGS" :validation-rules="[{ dxTabIndex: 0, type: 'stringLength', max: 50 }]" />
                        <DxFormSimpleItem data-field="Profesion" :validation-rules="[{ dxTabIndex: 0, type: 'stringLength', max: 150 }]" />
                        <DxFormSimpleItem data-field="LugarTrabajo" :validation-rules="[{ dxTabIndex: 0, type: 'stringLength', max: 50 }]" />
                        <DxFormSimpleItem data-field="Edad" caption="Edad" editor-type="dxTextBox" :editor-options="{readOnly:true}" />
                        <DxFormSimpleItem data-field="TipoSangre" editor-type="dxSelectBox" :editor-options="{dataSource: CatalogoTipoSangre, displayExpr: 'Nombre', valueExpr: 'Codigo',}" :validation-rules="[{ type: 'pattern', pattern: /^(A\+|A-|B\+|B-|AB\+|AB-|O\+|O-)$/ }]" />

                    </DxFormGroupItem>
                </DxFormTab >
                <DxFormTab icon="fas fa-home" title="Datos de Contacto">
                    <DxFormGroupItem caption="Datos de Contacto" :col-count="2" :col-span="2">

                        <DxFormSimpleItem data-field="CalleAvenida" :label="{text:'Calle / Sección'}" :validation-rules="[{dxTabIndex: 1, type: 'stringLength', max: 50, message: RangeMessage('Calle de Domicilio', null, 50)}]" />
                        <DxFormSimpleItem data-field="AvenidaDomicilio" :validation-rules="[ { dxTabIndex: 1, type: 'stringLength', max: 15, message: RangeMessage('Avenida Domicilio', null, 15)}]" />
                        <DxFormSimpleItem data-field="Zona" :validation-rules="[ { dxTabIndex: 1, type: 'range', min: 1, max: 25, message: 'Zona debe tener un valor entre 1 y 25' }, ]" editor-type="dxNumberBox" />
                        <DxFormSimpleItem data-field="Colonia" :validation-rules="[ { dxTabIndex: 1, type: 'stringLength', max: 50, message: RangeMessage('Colonia') }]" />
                        <DxFormSimpleItem data-field="Pais" editor-type="dxSelectBox" :col-span="1" :editor-options="{ dataSource: CatalogoPaises, displayExpr: 'Nombre', valueExpr: 'Id',  searchEnabled: true, searchTimeout: 200,}" />
                        <DxFormSimpleItem data-field="Departamento" editor-type="dxSelectBox" :col-span="1" :editor-options="{ dataSource: DepartamentoContacto, displayExpr: 'Departamento', valueExpr: 'IdDepartamento',  searchEnabled: true, searchTimeout: 200,}" :validation-rules="[{ type: 'range', min: 1611, max: 1632 }, ]" />
                        <DxFormSimpleItem data-field="Municipio" editor-type="dxSelectBox" :col-span="1" :editor-options="{ dataSource: MunicipioContacto, displayExpr: 'Municipio', valueExpr: 'IdMunicipio',  searchEnabled: true, searchTimeout: 200,}" :validation-rules="[{ type: 'range', min: 1, max: 340 }, ]" />
                        <DxFormSimpleItem data-field="TelefonoDomicilio" :label="{text:'Teléfono Domicilio'}" :validation-rules="[{ type: 'stringLength', max: 8, message: RangeMessage('Teléfono de Domicilio',null, 8) }]" :editor-options="{mask:'0000 0000'}" />
                        <DxFormSimpleItem data-field="TelefonoCelular" :label="{text:'Teléfono Celular'}" :validation-rules="[{ type: 'stringLength', max: 8, message: RangeMessage('Teléfono de Celular', null, 8) }]" :editor-options="{mask:'0000 0000'}" />

                        <DxFormSimpleItem data-field="Email1" :validation-rules="[{ dxTabIndex: 1, type: 'stringLength', max: 50, message:RangeMessage('Email 1',null,50) }, { dxTabIndex: 1, type: 'email' }, {dxTabIndex: 1, ...ValidationRules.Email1}, ]" />
                        <DxFormSimpleItem data-field="Email2" :validation-rules="[{ dxTabIndex: 1, type: 'stringLength', max: 50, message:RangeMessage('Email 2',null,50)}, { dxTabIndex: 1, type: 'email' }]" />
                        <DxFormSimpleItem data-field="NoCasaOApto" :validation-rules="[{ dxTabIndex: 1, type: 'stringLength', max: 25 }]" />
                        <DxFormSimpleItem data-field="Edificio" :validation-rules="[{ dxTabIndex: 1, type: 'stringLength', max: 50 }]" />

                    </DxFormGroupItem>
                </DxFormTab >
                <DxFormTab icon="fas fa-suitcase" title="Datos Laborales">
                    <DxFormGroupItem caption="Datos Laborales" :col-count="2" :col-span="2">

                        <DxFormSimpleItem data-field="TrabajoCalleAvenida" :label="{text:'Calle'}" :validation-rules="[{ dxTabIndex: 2, type: 'stringLength', max: 50 }]" />
                        <DxFormSimpleItem data-field="TrabajoAvenida" :label="{text:'Avenida'}" :validation-rules="[{ dxTabIndex: 2, type: 'stringLength', max: 10 }]" />
                        <DxFormSimpleItem data-field="TrabajoZona" :label="{text:'Zona'}" :validation-rules="[{ dxTabIndex: 2, type: 'range', min: 0, max: 25 }]" />
                        <DxFormSimpleItem data-field="TrabajoColonia" :label="{text:'Colonia'}" :validation-rules="[{ dxTabIndex: 2, type: 'stringLength', max: 50 }]" />
                        <DxFormSimpleItem data-field="TrabajoPais" :label="{text:'País'}" :validation-rules="[{ dxTabIndex: 2, type: 'range', min: 1, max: 200 }]" editor-type="dxSelectBox" :editor-options="{ dataSource: CatalogoPaises, displayExpr: 'Nombre', valueExpr: 'Id',  searchEnabled: true, searchTimeout: 200,}" />
                        <DxFormSimpleItem data-field="TrabajoDepartamento" :label="{text:'Departamento'}" :validation-rules="[{ dxTabIndex: 2, type: 'range', min: 1611, max: 1632 }]" editor-type="dxSelectBox" :editor-options="{ dataSource: DepartamentoLaboral, displayExpr: 'Departamento', valueExpr: 'IdDepartamento',  searchEnabled: true, searchTimeout: 200,}" />
                        <DxFormSimpleItem data-field="TrabajoMunicipio" :label="{text:'Municipio'}" :validation-rules="[{ dxTabIndex: 2, type: 'range', min: 1, max: 340 }]" editor-type="dxSelectBox" :col-span="1" :editor-options="{ dataSource: MunicipioLaboral, displayExpr: 'Municipio', valueExpr: 'IdMunicipio',  searchEnabled: true, searchTimeout: 200,}" />
                        <DxFormSimpleItem data-field="TelefonoTrabajo" :label="{text:'Telefono'}" :validation-rules="[{ dxTabIndex: 2, type: 'stringLength', max: 12 }]" />
                        <DxFormSimpleItem data-field="NoCasaOAptoTrabajo" :label="{text:'No. Casa o Apartamento'}" :validation-rules="[{ dxTabIndex: 2, type: 'stringLength', max: 50 }]" />
                        <DxFormSimpleItem data-field="EdificioTrabajo" :label="{text:'Edicicio'}" :validation-rules="[{ dxTabIndex: 2, type: 'stringLength', max: 50 }]" />
                        <DxFormSimpleItem data-field="OficinaTrabajo" :label="{text:'Oficina'}" :validation-rules="[{ dxTabIndex: 2, type: 'stringLength', max: 15 }]" />

                    </DxFormGroupItem>
                </DxFormTab >
                <DxFormTab icon="fas fa-file-invoice" title="Datos Facturación">

                    <DxFormGroupItem caption="Datos Facturación" :col-count="2" :col-span="2">
                        <DxFormSimpleItem data-field="IdClientePrincipal" :label="{text:'Titular Cliente'}" editor-type="dxTextBox" :editor-options="{readOnly: true}" />
                        <DxFormSimpleItem data-field="Titular" editor-type="dxTextBox" :editor-options="{readOnly: true}" />
                        <DxFormSimpleItem data-field="NitFactura" :validation-rules="[{dxTabIndex: 3, ...ValidationRules.Nit}, {dxTabIndex: 3, type: 'stringLength', max: 15 }]" />
                        <DxFormSimpleItem data-field="NombreFactura" :validation-rules="[{ dxTabIndex: 3, type: 'stringLength', max: 150 }]" />
                        <DxFormSimpleItem data-field="CorreoFactura" :validation-rules="[{ dxTabIndex: 3, type: 'stringLength', max: 100 }, {dxTabIndex: 3, type: 'email' }]" />
                    </DxFormGroupItem>

                </DxFormTab >
                <DxFormTab icon="fas fa-info-circle" title="Datos Específicos">
                    <DxFormGroupItem caption="Datos Específicos" :col-count="4" :col-span="2">

                        <DxFormSimpleItem :col-span="2" data-field="MedicoCabecera" :label="{text:'Médico de Cabecera'}" :validation-rules="[{ dxTabIndex: 4, type: 'stringLength', max: 50 }]" />
                        <DxFormSimpleItem :col-span="2" data-field="TelefonoMedicoCabecera" :label="{text:'Teléfono'}" :validation-rules="[{ dxTabIndex: 4, type: 'stringLength', max: 50 }]" :editor-options="{mask:'0000 0000'}" />
                        <DxFormSimpleItem :col-span="2" data-field="OtrosMedicos" :label="{text:'Otros Médicos'}" :validation-rules="[{ dxTabIndex: 4, type: 'stringLength', max: 50 }]" />
                        <DxFormSimpleItem :col-span="2" data-field="TelefonoOtrosMedicos" :label="{text:'Teléfono'}" :validation-rules="[{ dxTabIndex: 4, type: 'stringLength', max: 50 }]" :editor-options="{mask:'0000 0000'}" />

                        <DxFormSimpleItem :col-span="1" data-field="PrimeraVisita" editor-type="dxDateBox" :editorOptions="{ dataType: 'date', max: new Date(), displayFormat: 'dd/MM/yyyy', readOnly:true }" />
                        <DxFormSimpleItem :col-span="1" data-field="UltimaVisita" :label="{text:'Última Visita'}" editor-type="dxDateBox" :editor-options="{dataType: 'date', max: new Date(), displayFormat: 'dd/MM/yyyy', readOnly:true}" />
                        <DxFormSimpleItem :col-span="2" data-field="SaludSiempre" editor-type="dxCheckBox" />

                    </DxFormGroupItem>

                    <DxFormGroupItem caption="Alergias" :col-count="2" :col-span="2">
                        <DxFormSimpleItem data-field="AlergiasMedicamento" :validation-rules="[{ dxTabIndex: 4, type: 'stringLength', max: 50 }]" />
                        <DxFormSimpleItem data-field="AlergiasComidas" :validation-rules="[{ dxTabIndex: 4, type: 'stringLength', max: 50 }]" />
                        <DxFormSimpleItem data-field="AlergiasSustanciasAmbiente" :validation-rules="[{ dxTabIndex: 4, type: 'stringLength', max: 50 }]" />
                        <DxFormSimpleItem data-field="AlergiasSustanciasPiel" :validation-rules="[{ dxTabIndex: 4, type: 'stringLength', max: 50 }]" />
                        <DxFormSimpleItem data-field="AlergiasOtros" :validation-rules="[{ dxTabIndex: 4, type: 'stringLength', max: 50 }]" />
                    </DxFormGroupItem>

                    <DxFormGroupItem caption="Alimentos que no le gustan" :col-count="2" :col-span="2">
                        <DxFormSimpleItem data-field="AlimentosNoCarne" :label="{text:'Carnes'}" :validation-rules="[{ dxTabIndex: 4, type: 'stringLength', max: 50 }]" />
                        <DxFormSimpleItem data-field="AlimentosNoFrutas" :label="{text:'Frutas'}" :validation-rules="[{ dxTabIndex: 4, type: 'stringLength', max: 50 }]" />
                        <DxFormSimpleItem data-field="AlimentosNoVegetales" :label="{text:'Vegetales'}" :validation-rules="[{ dxTabIndex: 4, type: 'stringLength', max: 50 }]" />
                        <DxFormSimpleItem data-field="AlimentosVegetariano" :label="{text:'Vegetariano'}" editor-type="dxCheckBox" />
                        <DxFormSimpleItem data-field="AlimentosNoOtros" :label="{text:'Otros'}" :validation-rules="[{ dxTabIndex: 4, type: 'stringLength', max: 50 }]" />
                    </DxFormGroupItem>

                </DxFormTab >

            </DxFormTabbedItem>
            <DxFormButtonItem :visible="Modo!='LECTURA'" :button-options="submitButtonOptions" horizontal-alignment="center" verical-alignment="buttom" css-class="--mysticky" :col-span="2" />

            <template #idcliente>
                <DxTextBox v-model="FormData.IdCliente" :read-only="true" label-mode="floating" label="Id Cliente">
                    <DxTextBoxButton :options="{
                        icon: 'find',
                        hint: 'Buscar cliente y carga los datos para registrar nuevo afiliado',
                        type: 'default',
                        stylingMode: 'text',
                        visible: ['DEPENDIENTE','NUEVO'].includes(Modo),
                        onClick: buscarCliente,
                        disabled: false, //para evitar que el readonly del textbox lo inhabilite
                    }" name="password" location="after" />
                </DxTextBox>
            </template>
            <template #idpaciente>
                <DxNumberBox v-model="FormData.IdPaciente" :read-only="true" label-mode="floating" label="Id Paciente">
                    <DxNumberBoxButton :options="{
                        icon: 'edit',
                        hint: 'Asociar cliente y paciente',
                        type: 'default',
                        stylingMode: 'text',
                        visible: ['EDITAR'].includes(Modo) && Permisos.ClientePaciente,
                        onClick: ()=> VerClientePaciente = true,
                        disabled: false, //para evitar que el readonly del textbox lo inhabilite
                    }" name="clientePaciente" location="after" />
                </DxNumberBox>
            </template>
        </DxForm>
    </form>
    <DxPopup :visible.sync="VerClientePaciente" :drag-enabled="false" :hide-on-outside-click="false" :show-close-button="true" :show-title="true" width="90%" height="90%" :title="`Relación Clientes-Pacientes Cliente ${FormData.IdCliente} ` " content-template="cliente-paciente-popup-content" :position="popupPosition">
        <template #cliente-paciente-popup-content>
            <DxScrollView :scroll-by-conent="true" width="100%" height="100%">
                <div>
                    <ClientePaciente v-if="VerClientePaciente" :datos-cliente="{ IdCliente: FormData.IdCliente, IdAfiliado: FormData.IdAfiliado }" @asociated="onClientePacienteChanged" @paciente-created="onClientePacienteChanged"/>
                </div>
            </DxScrollView>            
        </template>
    </DxPopup>
</div>
</template>

<script>
import validador_dpi_nit from '@/components/sermesa/funciones/SMVFormatsDocs'
import ValidationSummary from "devextreme/ui/validation_summary"
import TabPanel from "devextreme/ui/tab_panel"

import ClientePaciente from '../../../components/sermesa/modules/paciente/ClientePaciente.vue'

import {
    popupPosition
} from './data.js'
const REFFORM = 'refForm'
export default {
    /**
     * POS ALPARECER TAMBIEN SE PUEDEN ACTUALIZAR CLINETES SIN QUE ESTEN AFILIADOS :/
     * Permite multiples comprtamientos:
     * Registro de nuevo afiliado: debe de fijarse el parentesco como titular (no tiene idAfiliado ni numeroadhesion, ni plan)
     * Actualización de datos de afiliado
     * Agregar dependiente a plan de seguro :), habilitar unicamente los parentescos disponibles y paranada el de titular
     * Permite realizar la busqueda de cliente para completar los datos 
     */
    name: 'AfiliadoFicha',
    components: {
        
      
        SeleccionPlan: () => import('./SeleccionPlan.vue'),
        ClientePaciente,
    },
    props: {
        IdCliente: null,
        IdPlan: null,
        IdContrato: null,
        IdAfiliado: null,
        NumeroAdhesion: null,
        FechaInicioCobertura: null,
        Modo: {
            type: String,
            required: true,
            validator(value) {
                return ['NUEVO', 'EDITAR', 'DEPENDIENTE', 'LECTURA'].includes(value)
            }
        },
        ParentescosPermitidos: {
            type: Array,
            default: () => null,
        },
        DisabledItems: {
            type: Object,
            default: null
        }
    },
    
    data() {
        return {

            FormData: {
                IdEmpresa: null,
                IdCliente: null,
                IdPaciente: null,
                IdPlan: null,
                NombreContrato: null,
                NombrePlan: null,
                IdPoliza: null,
                NumeroAdhesion: null,
                IdAfiliado: null,
                IdStatus: null,
                Status: null,
                FechaInicioCobertura: null,
                Nombre1: null,
                Nombre2: null,
                Apellido1: null,
                Apellido2: null,
                ApellidoCasada: null,
                DPI: null,
                FechaNacimiento: null,
                EstadoCivil: null,
                Genero: null,
                Nit: null,
                Pasaporte: null,
                LugarNacimiento: null,
                Nacionalidad: null,
                AfiliacionIGGS: null,
                Profesion: null,
                LugarTrabajo: null,
                Edad: null,
                TipoSangre: null,
                CalleAvenida: null,
                AvenidaDomicilio: null,
                Zona: null,
                Colonia: null,
                Pais: null,
                Departamento: null,
                Municipio: null,
                TelefonoDomicilio: null,
                TelefonoCelular: null,
                Email1: null,
                Email2: null,
                NoCasaOApto: null,
                Edificio: null,
                TrabajoCalleAvenida: null,
                TrabajoAvenida: null,
                TrabajoZona: null,
                TrabajoColonia: null,
                TrabajoPais: null,
                TrabajoDepartamento: null,
                TrabajoMunicipio: null,
                TelefonoTrabajo: null,
                NoCasaOAptoTrabajo: null,
                EdificioTrabajo: null,
                OficinaTrabajo: null,
                NitFactura: null,
                NombreFactura: null,
                CorreoFactura: null,
                MedicoCabecera: null,
                TelefonoMedicoCabecera: null,
                OtrosMedicos: null,
                TelefonoOtrosMedicos: null,
                PrimeraVisita: null,
                UltimaVisita: null,
                SaludSiempre: false,
                AlergiasMedicamento: null,
                AlergiasComidas: null,
                AlergiasSustanciasAmbiente: null,
                AlergiasSustanciasPiel: null,
                AlergiasOtros: null,
                AlimentosNoCarne: null,
                AlimentosNoFrutas: null,
                AlimentosNoVegetales: null,
                AlimentosVegetariano: false,
                AlimentosNoOtros: null,
                IdTipoDocumento: 0,
                TipoDeDocumento: null,
                DocumentoOrden: null,
                DocumentoRegistro: null,
                IdParentesco: null,
                Parentesco: null,
                IdClientePrincipal: null,
                Titular: null,
            },
            submitButtonOptions: {
                text: 'Guardar',
                type: 'success',
                icon: 'save',
                useSubmitBehavior: true,
                disabled: false
            },
            IdClienteOptions: {
                buttons: [{
                    name: 'boncito',
                    location: 'after',
                    options: {
                        icon: 'find',
                        hint: 'Buscar cliente y carga los datos para registrar nuevo afiliado',
                        type: 'default',
                        stylingMode: 'text',
                        visible: true,
                        onClick: this.buscarCliente,
                        disabled: false, //para evitar que el readonly del textbox lo inhabilite
                    }
                }],
            },

            IdPaisGuatemala: 91,

            CatalogoPaises: null,
            CatalogoDepartamentos: null,
            CatalogoMunicipios: null,
            CatalogoSexos: null,
            CatalogoPlanes: null,
            CatalogoEstadoCivil: [{
                    Codigo: 'C',
                    Nombre: 'CASADO(A)'
                },
                {
                    Codigo: 'S',
                    Nombre: 'SOLTERO(A)'
                }
            ],
            CatalogoTipoSangre: null,
            TabSelectedItemIndex: 0,

            tabPanelOptions: {
                animationEnabled: false,
                swipeEnabled: true,
                stylingMode: 'primary', // ó  secondary
                iconPosition: "top",
                tabsPosition: "left",
                deferRendering: false,
                selectedIndex: this.TabSelectedItemIndex,
                onSelectionChanged: (e) => this.onTabItemSelectionChanged(e),
            },

            ValidationRules: {
                Email1: {
                    type: 'custom',
                    reevaluate: true,
                    validationCallback: (e) => {
                        if (this.FormData.Email2 && !this.FormData.Email1) {
                            e.rule.message = 'Ingrese correo electrónico principal'
                            return false
                        }
                        e.rule.message = ''
                        return true
                    }
                },
                Dpi: {
                    type: 'custom',
                    reevaluate: true,
                    validationCallback: (e) => {
                        /*
                        --->Descomentar para validar CUI requerido para guatemaltecos mayores de 18 años
                        if (this.FormData.Nacionalidad == this.IdPaisGuatemala && this.FormData.Edad >= 18 && !e.value) {
                            e.rule.message = 'Ingrese DPI para guatemalteco mayor de 18 años'
                            return false
                        }
                        */
                        if (e.value && !validador_dpi_nit.cuiValido(e.value)) {
                            e.rule.message = 'Ingrese DPI válido'
                            return false
                        }
                        e.rule.message = ''
                        return true
                    }
                },
                Pasaporte: {
                    type: 'custom',
                    reevaluate: true,
                    validationCallback: (e) => {
                        if (this.FormData.Nacionalidad && this.FormData.Nacionalidad != this.IdPaisGuatemala && !e.value) {
                            e.rule.message = 'Ingrese Pasaporte para nacionalidad extranjera'
                            return false
                        }
                        e.rule.message = ''
                        return true
                    }
                },
                Nit: {
                    type: 'custom',
                    reevaluate: true,
                    validationCallback: (e) => {
                        if (e.value && e.value.toUpperCase() != 'C/F' && !validador_dpi_nit.nitValido(e.value)) {
                            e.rule.message = 'Ingrese NIT válido'
                            return false
                        }
                        e.rule.message = ''
                        return true
                    }
                },
                DeptoDomicilio: {
                    type: 'custom',
                    reevaluate: true,
                    validationCallback: (e) => {
                        if (this.FormData.Pais == this.IdPaisGuatemala && !e.value) {
                            e.rule.message = 'Ingrese un Departamento de Domicilio'
                            return false
                        }
                        e.rule.message = ''
                        return true
                    }
                },
                MuniDomicilio: {
                    type: 'custom',
                    reevaluate: true,
                    validationCallback: (e) => {
                        if (this.FormData.Departamento && !e.value) {
                            e.rule.message = 'Ingrese un Municipio de Domicilio'
                            return false
                        }
                        e.rule.message = ''
                        return true
                    }
                },
                Parentesco: {
                    type: 'custom',
                    reevaluate: true,
                    validationCallback: (e) => {
                        if (this.Modo == 'DEPENDIENTE' && !e.value) {
                            e.rule.message = 'Ingrese un parentesco para el dependiente'
                            return false
                        }
                        e.rule.message = ''
                        return true
                    }
                }
            },
            
            REFFORM,
            VerClientePaciente: false,
            popupPosition,
            Permisos: {
                ClientePaciente: false,
            },
        }
    },
    computed: {
        DepartamentoContacto() {
            if (this.FormData.Pais == this.IdPaisGuatemala)
                return this.CatalogoDepartamentos
            return null
        },
        MunicipioContacto() {
            if (this.FormData.Departamento && this.CatalogoMunicipios) {
                return this.CatalogoMunicipios.filter(x => x.IdDepartamento == this.FormData.Departamento)
            }
            return null
        },

        DepartamentoLaboral() {
            if (this.FormData.TrabajoPais == this.IdPaisGuatemala)
                return this.CatalogoDepartamentos
            return null
        },
        MunicipioLaboral() {
            if (this.FormData.TrabajoDepartamento && this.CatalogoMunicipios) {
                return this.CatalogoMunicipios.filter(x => x.IdDepartamento == this.FormData.TrabajoDepartamento)
            }
            return null
        },

        FormInstance: function () {
            return this.$refs[REFFORM].instance
        },
        DisabledPlanEditors() {
            return null
        },
        adhesionRules() {
            return this.Modo == 'EDITAR' && !this.FormData.IdAfiliado? []:[ {type: 'stringLength', min:2, max: 50, message: this.RangeMessage('Número de Adhesión', 2, 50) }]
        },
        inicioCoberturaRules() {
            return this.Modo == 'EDITAR' && !this.FormData.IdAfiliado? []:[{ type: 'required', message: 'Ingrese una fecha de inicio cobertura'}]
        },
    },
    watch: {
        'FormData.FechaNacimiento'(value) {
            this.FormData.Edad = this.$calcular_edad(value, this.Ahora)
        },

        //para limpiar los datos cuando es diferente a guatemala
        'FormData.Pais'(value) {
            if (value != this.IdPaisGuatemala)
                this.FormData.Departamento = null
        },
        'FormData.Departamento'(value) {
            if (!value)
                this.FormData.Municipio = null
        },
        'IdCliente'() {
            if (this.IdCliente && ['LECTURA', 'EDITAR'].includes(this.Modo))
                this.Cargar()
        },
        'Modo'(value) {
            if (value == 'NUEVO') {
                this.LimpiarDatos()
            } else if (value == 'DEPENDIENTE') {
                this.LimpiarDatos()
                this.InitDependiente()
            } else {
                this.Cargar()
            }

            this.SetOptions()
        },
        //para limpiar los datos cuando es diferente a guatemala
        'FormData.TrabajoPais'(value) {
            if (value != this.IdPaisGuatemala)
                this.FormData.TrabajoDepartamento = null
        },
        'FormData.TrabajoDepartamento'(value) {
            if (!value)
                this.FormData.TrabajoMunicipio = null
        },
        //Chapulin para que se pueda trabajar con los datos registrados en cero
        'FormData.Zona'(value) {
            if(value == "0")
                this.FormData.Zona = null
        },

    },
    mounted() {
        
    },
    beforeMount() {
        this.$validar_funcionalidad('/SASI/SASI006', 'CLIENTE_PACIENTE', (d) => {
                this.Permisos.ClientePaciente = d.status;
            })
        this.CargaCatalogos()
        this.Ahora = new Date()
        this.CargaPermisos()
        if (['LECTURA', 'EDITAR'].includes(this.Modo))
            this.Cargar()
        if (this.Modo == 'DEPENDIENTE')
            this.InitDependiente()
    },
    methods: {
        /**Colocar aqui las actualizacionde de las propiedades de los editores que no funcionen con la reactividad convencinal */
        SetOptions() {
            this.IdClienteOptions.buttons[0].options.visible = this.Modo == 'NUEVO'
        },
        onTabItemSelectionChanged(e) {
            this.TabSelectedItemIndex = e.component.option('selectedIndex')
            this.tabPanelOptions.selectedIndex = e.component.option('selectedIndex')
        },
        onIdPlanChange(data) {
            if (data) {
                this.FormData.IdPlan = data.IdPlan
                this.FormData.IdContrato = data.IdContrato
                this.FormData.IdPoliza = data.IdPoliza
                this.FormData.NombreContrato = data.NombreContrato
            }
        },
        buscarCliente() {
            this.$emit('busqueda-cliente', true)
            this.$refs.buscador.iniciar((data) => {
                if (data != null) {
                    const formCopy = {...this.FormData}//si es nuevo el afiliado para asignarle los  valores seleccionados antes de cargarlo 
                    this.Cargar(data.IdCliente, null).then(() => {
                        if (this.Modo == 'DEPENDIENTE')
                            this.InitDependiente()

                        if (this.Modo == 'NUEVO') {
                            this.FormData.IdPlan				= formCopy.IdPlan
                            this.FormData.NombrePlan			= formCopy.NombrePlan
                            this.FormData.IdContrato			= formCopy.IdContrato
                            this.FormData.NombreContrato		= formCopy.NombreContrato
                            this.FormData.IdPoliza				= formCopy.IdPoliza
                            this.FormData.NumeroAdhesion		= formCopy.NumeroAdhesion
                            this.FormData.IdAfiliado			= formCopy.IdAfiliado
                            this.FormData.IdStatus				= formCopy.IdStatus
                            this.FormData.Status				= formCopy.Status
                            this.FormData.FechaInicioCobertura	= formCopy.FechaInicioCobertura
                            this.FormData.NitFactura			= formCopy.NitFactura
                            this.FormData.NombreFactura			= formCopy.NombreFactura
                            this.FormData.CorreoFactura			= formCopy.CorreoFactura
                            this.FormData.IdParentesco			= formCopy.IdParentesco
                            this.FormData.Parentesco			= formCopy.Parentesco
                            this.FormData.IdClientePrincipal	= formCopy.IdClientePrincipal
                            this.FormData.Titular				= formCopy.Titular
                        }
                    })
                }
            })
        },
        onContentReady() {
            //para que cambie de pestaña al dar click en al validation summary
            let summary = document.getElementsByClassName("dx-form-validation-summary")[0]
            let sInstance = ValidationSummary.getInstance(summary)
            sInstance.on("itemClick", this.onValidationSumItemClick)
        },
        onValidationSumItemClick(e) {

            let tInstance = TabPanel.getInstance(document.getElementsByClassName("dx-multiview dx-swipeable dx-tabpanel dx-widget dx-visibility-change-handler dx-collection")[0])
            const tabIndex = e.itemData.validator.option("validationRules")[0].dxTabIndex

            tInstance.option("selectedIndex", tabIndex)
            this.TabSelectedItemIndex = tabIndex

            this.$nextTick(() => {
                this.FormInstance.validate()
                e.itemData.validator.focus
            })
        },
        RangeMessage(fieldName = '', min = 0, max) {
            if (min && max)
                return `${fieldName} debe contener entre ${min} y ${max} caracteres`

            if (max)
                return `${fieldName} debe tener como máximo ${max} caracteres`

            if (min)
                return `${fieldName} debe tener por lo menos ${min} caracteres`

            return `${fieldName} es obligatorio`
        },
        RequiredMessage(e) {
            return ''.concat(e, ' es obligatorio')
        },

        LimpiarDatos() {
            this.FichaVacia();
        },
        /**Prepara el formulario para el registro de un dependiente */
        InitDependiente() {
            this.FormData.IdPlan = this.IdPlan
            this.FormData.NumeroAdhesion = this.NumeroAdhesion
            this.FormData.IdParentesco = null
            this.FormData.FechaInicioCobertura = this.FechaInicioCobertura
        },
        Cargar(pIdCliente = this.IdCliente, pIdAfiliado = this.IdAfiliado) {
            return new Promise((resolve, reject) => {
                this.LimpiarDatos()

                if (pIdCliente)
                    this.axios.post("/app/v1_afiliados/FichaAfiliado", {
                        IdCliente: pIdCliente,
                        IdAfiliado: pIdAfiliado, //filtro adicional por si existen multiples afiliaciones para el cliente
                    })
                    .then(resp => {
                        if (resp.data.codigo == 0) {
                            this.FormData = resp.data.json[0]
                        }
                        resolve()
                    })
                    .catch(
                        (error) => reject(error)
                    )
                else {
                    resolve()
                }

            })
        },
        CargaCatalogos() {
            this.axios.post('/app/v1_afiliados/BusquedaPaises', {}).then((resp) => {
                this.CatalogoPaises = resp.data.json
                this.IdPaisGuatemala = this.CatalogoPaises.find(x => 'guatemala'.localeCompare(x.Nombre, undefined, {
                    sensitivity: 'base'
                }) == 0).Id || 0
            })
            //el enpoint devuelve los codigo como string, se requieren int
            this.axios.post('/app/Ajenos/Busqueda_Departamentos', {}).then((resp) => this.CatalogoDepartamentos = resp.data.json.map((x) => {
                x.IdDepartamento = parseInt(x.IdDepartamento)
                return x
            }))
            this.axios.post('/app/Ajenos/Busqueda_Municipios', {}).then((resp) => this.CatalogoMunicipios = resp.data.json.map((x) => {
                x.IdDepartamento = parseInt(x.IdDepartamento)
                x.IdMunicipio = parseInt(x.Codigo_Municipio)
                return x
            }))
            this.axios.post('/app/v1_afiliados/BusquedaPlanes', {}).then((resp) => this.CatalogoPlanes = resp.data.json)
            this.CatalogoSexos = [{
                Codigo: 'F',
                Nombre: 'Femenino'
            }, {
                Codigo: 'M',
                Nombre: 'Masculino'
            }]
            this.CatalogoTipoSangre = [{
                    Codigo: 'A+',
                    Nombre: 'A+'
                },
                {
                    Codigo: 'A-',
                    Nombre: 'A-'
                },
                {
                    Codigo: 'B-',
                    Nombre: 'B-'
                },
                {
                    Codigo: 'O+',
                    Nombre: 'O+'
                },
                {
                    Codigo: 'O-',
                    Nombre: 'O-'
                },
                {
                    Codigo: 'AB+',
                    Nombre: 'AB+'
                },
                {
                    Codigo: 'AB-',
                    Nombre: 'AB-'
                },
                {
                    Codigo: 'B+',
                    Nombre: 'B+'
                }
            ]
        },
        CargaPermisos() {
           
        },
        Grabar() {
            let postData = {
                ...this.FormData,
                AlimentosVegetariano: this.FormData.AlimentosVegetariano ? 'S' : 'N',
                SaludSiempre: this.FormData.SaludSiempre ? 'S' : 'N',
                IdParentesco: this.Modo == 'NUEVO' ? '00' : this.FormData.IdParentesco,
                Nombre1: this.FormData.Nombre1?.toUpperCase(),
                Nombre2: this.FormData.Nombre2?.toUpperCase(),
                Apellido1: this.FormData.Apellido1?.toUpperCase(),
                Apellido2: this.FormData.Apellido2?.toUpperCase(),
                ApellidoCasada: this.FormData.ApellidoCasada?.toUpperCase(),
                Nit: this.FormData.Nit?.toUpperCase(),
                NitFactura: this.FormData.NitFactura?.toUpperCase(),
            }

            if (postData.Email1)
                postData.Email1 = postData.Email1.replace(/\s+/g, "")
            if (postData.Email2)
                postData.Email2 = postData.Email2.replace(/\s+/g, "")
            if (postData.CorreoFactura)
                postData.CorreoFactura = postData.CorreoFactura.replace(/\s+/g, "")
            
            this.axios.post(`/app/v1_afiliados/${this.Modo=='EDITAR'?'ActualizaCliente':'GrabarAfiliado'}`, {
                    Afiliado: postData
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        let x = this.Cargar()
                        x.then(
                            () => this.$emit('saved', this.FormData)
                        )
                    }
                })

        },
        handleSubmit(e) {
            e.preventDefault()
            this.Grabar()
        },
        FichaVacia() {

            this.FormData.IdEmpresa = null
            this.FormData.IdCliente = null
            this.FormData.IdPaciente = null
            this.FormData.IdPlan = null
            this.FormData.IdContrato = null
            this.FormData.NombrePlan = null
            this.FormData.IdPoliza = null
            this.FormData.NumeroAdhesion = null
            this.FormData.IdAfiliado = null
            this.FormData.IdStatus = null
            this.FormData.Status = null
            this.FormData.FechaInicioCobertura = null
            this.FormData.Nombre1 = null
            this.FormData.Nombre2 = null
            this.FormData.Apellido1 = null
            this.FormData.Apellido2 = null
            this.FormData.ApellidoCasada = null
            this.FormData.DPI = null
            this.FormData.FechaNacimiento = null
            this.FormData.EstadoCivil = null
            this.FormData.Genero = null
            this.FormData.Nit = null
            this.FormData.Pasaporte = null
            this.FormData.LugarNacimiento = null
            this.FormData.Nacionalidad = null
            this.FormData.AfiliacionIGGS = null
            this.FormData.Profesion = null
            this.FormData.LugarTrabajo = null
            this.FormData.Edad = null
            this.FormData.TipoSangre = null
            this.FormData.CalleAvenida = null
            this.FormData.AvenidaDomicilio = null
            this.FormData.Zona = null
            this.FormData.Colonia = null
            this.FormData.Pais = null
            this.FormData.Departamento = null
            this.FormData.Municipio = null
            this.FormData.TelefonoDomicilio = null
            this.FormData.TelefonoCelular = null
            this.FormData.Email1 = null
            this.FormData.Email2 = null
            this.FormData.NoCasaOApto = null
            this.FormData.Edificio = null
            this.FormData.TrabajoCalleAvenida = null
            this.FormData.TrabajoAvenida = null
            this.FormData.TrabajoZona = null
            this.FormData.TrabajoColonia = null
            this.FormData.TrabajoPais = null
            this.FormData.TrabajoDepartamento = null
            this.FormData.TrabajoMunicipio = null
            this.FormData.TelefonoTrabajo = null
            this.FormData.NoCasaOAptoTrabajo = null
            this.FormData.EdificioTrabajo = null
            this.FormData.OficinaTrabajo = null
            this.FormData.NitFactura = null
            this.FormData.NombreFactura = null
            this.FormData.CorreoFactura = null
            this.FormData.MedicoCabecera = null
            this.FormData.TelefonoMedicoCabecera = null
            this.FormData.OtrosMedicos = null
            this.FormData.TelefonoOtrosMedicos = null
            this.FormData.PrimeraVisita = null
            this.FormData.UltimaVisita = null
            this.FormData.SaludSiempre = false
            this.FormData.AlergiasMedicamento = null
            this.FormData.AlergiasComidas = null
            this.FormData.AlergiasSustanciasAmbiente = null
            this.FormData.AlergiasSustanciasPiel = null
            this.FormData.AlergiasOtros = null
            this.FormData.AlimentosNoCarne = null
            this.FormData.AlimentosNoFrutas = null
            this.FormData.AlimentosNoVegetales = null
            this.FormData.AlimentosVegetariano = false
            this.FormData.AlimentosNoOtros = null
            this.FormData.IdTipoDocumento = 0,
            this.FormData.TipoDeDocumento = 0
            this.FormData.DocumentoOrden = null
            this.FormData.DocumentoRegistro = null
            this.FormData.IdParentesco = null
            this.FormData.Parentesco = null
            this.FormData.IdClientePrincipal = null
            this.FormData.Titular = null
            this.FormData.NombreContrato = null
        },
        onClientePacienteChanged(e){
            this.Cargar()
            this.$emit('cliente-paciente-changed', e)
        },
    },
}
</script>

<style>
.uppercase input
{  
    text-transform:uppercase;  
}  

.dx-box-item:has(.mysticky) {
    position: sticky !important;
    bottom: 15px !important;
    background-color: whitesmoke !important;
    z-index: 1;
}

/* reglas css para ocultar columnas del buscador de clientes */
.ficha-afiliado-cliente-buscador .buscador_table th:nth-child(4),
.ficha-afiliado-cliente-buscador .buscador_table th:nth-child(5),
.ficha-afiliado-cliente-buscador .buscador_table th:nth-child(6),
.ficha-afiliado-cliente-buscador .buscador_table th:nth-child(7),
.ficha-afiliado-cliente-buscador .buscador_table td:nth-child(4),
.ficha-afiliado-cliente-buscador .buscador_table td:nth-child(5),
.ficha-afiliado-cliente-buscador .buscador_table td:nth-child(6),
.ficha-afiliado-cliente-buscador .buscador_table td:nth-child(7) {
    display: none;
}

.ficha-afiliado-cliente-buscador .con-vs-popup .vs-popup {
    width: 85% !important;
}
</style><style lang="scss">
.box,
.dx-radiobutton-icon .dx-radiobutton-icon-checked,
.dx-invalid::after {

    width: 100%;
    height: inherits;
    display: inherits;
    padding: 2px;

    --border-angle: 0turn;
    /*// For animation. */
    --main-bg: conic-gradient(from var(--border-angle),
            #f2ecf9,
            #eeeef6 5%,
            #eeeef6 60%,
            #f2ecf9 95%);

    border: solid 1px transparent;
    /*border-radius: 1em;*/
    --gradient-border: conic-gradient(from var(--border-angle), transparent 25%, #08f, #f03 99%, transparent);

    background:
        /* // padding-box clip this background in to the overall element except the border. */
        var(--main-bg) padding-box,
        /* // border-box extends this background to the border space */
        var(--gradient-border) border-box,
        /* // Duplicate main background to fill in behind the gradient border. You can remove this if you want the border to extend "outside" the box background. */
        var(--main-bg) border-box;

    background-position: center center;

    animation: bg-spin 3s linear infinite;

    @keyframes bg-spin {
        to {
            --border-angle: 1turn;
        }
    }

    &:hover {
        animation-play-state: paused;
    }
}

@property --border-angle {
    syntax: "<angle>";
    inherits: true;
    initial-value: 0turn;
}
/** ajustar el tamano de los datagrid componente clientes-pacientes para velos en el modal */
.clientes-pacientes-container .dx-datagrid {
    max-height: calc(80vh - 255px) !important;
}
</style>
