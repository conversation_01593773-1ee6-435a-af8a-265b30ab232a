<template>
<div id="Bancos">
    <DxDataGrid :ref="gridBancos" v-bind="DefaultDxGridConfiguration" :data-source="bancos" :headerFilter="{visible:false, allowSearch:false}" :width="'100%'" :height="'500px'" @row-updating="ModificarBanco" @row-inserting="AgregarBanco" @row-removing="EliminarBanco" @row-updated="CargarBancos" @row-inserted="CargarBancos" @row-removed="CargarBancos" @editor-preparing="editorDatagrid">
        <DxDataGridSelection mode="single" />

        <DxDataGridEditing :allow-updating="true" :allow-adding="true" :allow-deleting="true" mode="row" :use-icons="true" :confirmDelete="true" />

        <DxDataGridColumn :width="100" type="buttons" alignment="center">
            <DxDataGridButton name="edit" />
            <DxDataGridButton name="delete" />
        </DxDataGridColumn>
        <DxDataGridColumn :width="100" data-field="Codigo" caption="Código" data-type="string" alignment="center" :editor-options="{ maxLength: 3, minLength: 3 }" css-class="codigoUpper" :validationRules="[{ type: 'required' }]" />
        <DxDataGridColumn :width="400" data-field="Nombre" alignment="center" :editor-options="{ maxLength: 60 }" :validationRules="[{ type: 'required' }]" />
        <DxDataGridColumn :width="200" data-field="Telefonos" caption="Teléfonos" data-type="string" alignment="center" :editor-options="{ maxLength: 25 }" />
        <DxDataGridColumn :width="300" data-field="Direccion" caption="Dirección" data-type="string" alignment="center" :editor-options="{ maxLength: 60 }" />
    </DxDataGrid>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'

const gridBancos = 'gridBancos'

export default {
    name: 'Bancos',
    components: {},
    data() {
        return {
            DefaultDxGridConfiguration,
            gridBancos,
            bancos: [],
        }
    },
    methods: {
        CargarBancos() {
            this.axios.post('/app/v1_bancos/Bancos', {
                    Opcion: 1
                })
                .then(resp => {
                    this.bancos = resp.data.json
                })
        },
        onlyLeters(e) {
            /*
                8 = backspace
                9 = tab
                13 = enter
                60 - 90 = letras
                37 = flecha izquierda
                39 = flecha derecha
            */
            if ((e.event.keyCode < 65 || e.event.keyCode > 90) && e.event.keyCode !== 192 && e.event.keyCode !== 8 && e.event.keyCode !== 9 && e.event.keyCode !== 13 && e.event.keyCode !== 37 && e.event.keyCode !== 39) {
                e.event.preventDefault()
            }
        },
        AgregarBanco(e) {
            e.cancel = new Promise((resolve, reject) => {
                var existe = false

                for (const i of this.bancos) {
                    if (i.Codigo.toUpperCase() == e.data.Codigo.toUpperCase()) {
                        existe = true
                        break;
                    }
                }

                if (existe !== true) {
                    this.axios.post('/app/v1_bancos/Bancos', {
                            Codigo: e.data.Codigo.toUpperCase(),
                            Direccion: e.data.Direccion,
                            Nombre: e.data.Nombre,
                            Telefonos: e.data.Telefonos,
                            Opcion: 2
                        })
                        .then((resp) => {
                            resp.data.codigo == 0 ? resolve(false) : resolve(true)
                        }).catch((err) => {
                            reject(err.descripcion ? err.descripcion : err)
                        })
                } else {
                    resolve(false)
                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'Código incorrecto',
                        acceptText: 'Aceptar',
                        text: 'El código que se le asignó al banco ya existe. Asigne otro código al nuevo banco.',
                        buttonCancel: 'border',
                        accept: () => {},
                    })
                }
            })
        },
        ModificarBanco(e) {
            e.cancel = new Promise((resolve, reject) => {
                var existe = false

                if (e.newData.Codigo) {
                    for (const i of this.bancos) {
                        if (i.Codigo.toUpperCase() == e.newData.Codigo.toUpperCase()) {
                            existe = true
                            break;
                        }
                    }
                }

                if (existe !== true) {
                    this.axios.post('/app/v1_bancos/Bancos', {
                            Codigo: e.newData.Codigo ? e.newData.Codigo : e.oldData.Codigo,
                            Direccion: e.newData.Direccion ? e.newData.Direccion : e.oldData.Direccion,
                            Nombre: e.newData.Nombre ? e.newData.Nombre : e.oldData.Nombre,
                            Telefonos: e.newData.Telefonos ? e.newData.Telefonos : e.oldData.Telefonos,
                            CodigoActual: e.oldData.Codigo,
                            Opcion: 4
                        })
                        .then((resp) => {
                            resp.data.codigo == 0 ? resolve(false) : resolve(true)
                        }).catch((err) => {
                            reject(err.descripcion ? err.descripcion : err)
                        })
                } else {
                    resolve(false)
                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'Código incorrecto',
                        acceptText: 'Aceptar',
                        text: 'El código que se le asignó al banco ya existe. Asigne otro código al banco que desea modificar.',
                        buttonCancel: 'border',
                        accept: () => {},
                    })
                }
            })
        },
        EliminarBanco(e) {
            e.cancel = new Promise((resolve) => {
                this.axios.post('/app/v1_bancos/Bancos', {
                    Codigo: e.data.Codigo,
                    Opcion: 3
                }).then((resp) => {
                    resp.data.codigo == 0 ? resolve(false) : resolve(true)
                }).catch(() => {
                    resolve(true)
                })
            })
        },
        editorDatagrid(e) {
            if (e.parentType === 'dataRow' && e.dataField === 'Codigo') {
                e.editorOptions.onKeyDown = this.onlyLeters
                e.editorElement.classList.add('codigoUpper')
            }
        },

        refreshDataGrid() {
            this.dataGridBancos.refresh();
            this.dataGridBancos.clearSelection();
            this.dataGridBancos.clearSorting();
            this.dataGridBancos.clearFilter();
            this.dataGridBancos.pageIndex(0);
        }
    },
    created() {},
    mounted() {
        this.CargarBancos()
    },
    watch: {},
    computed: {
        dataGridBancos: function () {
            return this.$refs[gridBancos].instance;
        },
    }
}
</script>

<style>
.codigoUpper input {
    text-transform: uppercase;
}

#Bancos .dx-datagrid-rowsview .dx-data-row .dx-validator.dx-datagrid-invalid.dx-cell-modified::after,
[dir] .dx-datagrid-rowsview .dx-data-row .dx-validator.dx-datagrid-invalid.dx-datagrid-invalid::after {
    border: 3px solid #d70000
}
</style>
