import moment from 'moment'

/* Valida si viene en blanco un objeto y devuelve false o true */ 
export const isEmptyObject = (obj) => {
    return Object.keys(obj).length === 0;
}

/* Genera la fecha actual en timestamp */
const dt = new Date();
const padL = (nr, chr = `0`) => `${nr}`.padStart(2, chr);

export const fechaActualSistema  = async () => {
    const fecha = `${
        padL(dt.getDate())}/${
        padL(dt.getMonth()+1)}/${
        dt.getFullYear()}/ ${
        padL(dt.getHours())}:${
        padL(dt.getMinutes())}:${
        padL(dt.getSeconds())}`

    return moment(fecha, "DD/MM/YYYY hh:mm:ss").toDate().getTime()
}

export const getIpLocal = () => {
    return localStorage.getItem('IpLocal');
}
