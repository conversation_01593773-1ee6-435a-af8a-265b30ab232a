<template>
<div class="p-2 flex flex-wrap">
    <div class="flex  w-full lg:w-1/2 p-2">
        <vs-card>
            <div slot="header">
                <h3>
                    Cuerpo del correo electrónico
                </h3>
            </div>
            <DxHtmlEditor v-bind="htmlEditorOptions" :value.sync="CuerpoCorreo" height="500" />
        </vs-card>
    </div>
    <div class="flex w-full lg:w-1/2 p-2">
        <vs-card>
            <div slot="header">
                <h3>
                    Observaciones del estado de cuenta
                </h3>
            </div>
            <DxHtmlEditor v-bind="htmlEditorOptions" :value.sync="DescripcionEstadoCuenta" height="500" />
        </vs-card>
    </div>

    <vs-button icon="save" color="primary" class="m-2" @click="Grabar">Guradar cambios</vs-button>

</div>
</template>

<script>
import DxHtmlEditor from 'devextreme-vue/html-editor'

export default {
    components: {
        DxHtmlEditor,
    },
    data() {
        return {
            DescripcionEstadoCuenta: '',
            CuerpoCorreo: '',
            diasbled: false,
            htmlEditorOptions: {
                disabled: this.diasbled,
                toolbar: {
                    multiline: false,
                    items: [
                        "undo",
                        "redo",
                        'clear',
                        "separator",
                        {
                            name: "header",
                            acceptedValues: [false, 1, 2, 3, 4, 5],
                            options: {
                                inputAttr: {
                                    'aria-label': 'Encabezado'
                                }
                            }
                        },
                        "separator",
                        "bold",
                        "italic",
                        "strike",
                        "underline",
                        "separator",
                        "alignLeft",
                        "alignCenter",
                        "alignRight",
                        "alignJustify",
                        "separator",
                        'background',
                        'color', 'link', 'increaseIndent', 'decreaseIndent',
                        'orderedList',
                        'bulletList',

                    ]
                }
            }
        }
    },
    created() {

    },
    computed: {

    },
    beforeMount() {
        this.CargarValoresDefecto()
    },
    watch: {

    },
    methods: {
        async CargarValoresDefecto() {
            return this.axios.post('/app/Honorarios/ConfValoresDefecto', {}).then(resp => {
                if (resp.data.codigo == 0) {
                    this.CuerpoCorreo = resp.data.json.CuerpoCorreo
                    this.DescripcionEstadoCuenta = resp.data.json.DescripcionEstadoCuenta
                }
            }).catch(() => {
                this.disabled = true
            })
        },
        Grabar() {
            this.axios.post('/app/Honorarios/GuardarValoresDefecto', {
                Configuraciones: {
                    DescripcionEstadoCuenta: this.DescripcionEstadoCuenta,
                    CuerpoCorreo:this.CuerpoCorreo,
                }
            }).catch(() => this.CargarValoresDefecto())
        }
    },
}
</script>
