<template>
<vx-card class="p-2" title="Restaurar Contraseña WEB" collapse-action data-action="collapse">

    <div class="vx-row text-left">
        <div class="vx-col w-full ">
            <label class="vs-input--label">Corporativo</label>
            <vx-input-group class="">
                <vs-input type="number" v-model="corporativo" />
                <template slot="append">
                    <div class="append-text btn-addon">
                        <vs-button id="button-with-loading" @click="Guardar()"></vs-button>
                    </div>
                </template>
            </vx-input-group>

        </div>

    </div>

    <!-- Support Tracker Meta Data -->

</vx-card>
</template>

<script>
export default {
    data() {
        return {
            corporativo: null,

            timer_chart: null,

        }
    },
    components: {

    },
    methods: {
        Guardar() {
            this.axios.post('/app/usuario/generador_password', {
                corporativo: this.corporativo
            })

        }
    },

    mounted() {

    }
}
</script>
