
/********************Creacion de Funciones********************************/
-->>
USE [HOSPITAL]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

-- ==========================================================
-- Author:  <PERSON>
-- Create date: 2023.06.01
-- Description:	Funcion para traer los abonos en el estado de cuenta
-- ==========================================================

CREATE FUNCTION [dbo].[fnEstadoCuentaAbonos] (
    @i_EmpresaUnificada varchar(3),
		@i_Serie varchar(5),
		@i_CodigoAdmision integer		
)
RETURNS @CuentaAbonos table (Empresa varchar(3), Recibo varchar(20), SumMonto smallmoney,Codigo varchar(3),<PERSON><PERSON> varchar(10),
														 FormaPago varchar(50),BancoTarjeta varchar(50), MontoTotal varchar(20), NombreCorto varchar(60))
AS
BEGIN
Declare @MontoTotal money


if exists(
  select 
    A.BeneficioTerceros as SumMonto 
  from 
    Admisiones A Inner join 
		DescuentoBeneficio DB ON (A.Serie = DB.SerieAdmision and A.Codigo = DB.Admision) 
  where 
    A.Serie = @i_Serie 
    and A.Codigo = @i_CodigoAdmision 
    and A.Empresa = @i_EmpresaUnificada 
    and DB.Status = 'S' 
  Group By A.BeneficioTerceros, DB.Empresa, DB.SerieAdmision, DB.Admision, DB.Fecha, DB.Autorizacion
) Begin 
--Obtenemos MontoTotal--
Select 
  @montoTotal = IsNull(sum(SumMonto),0)
FROM 
  (
    SELECT SUM(RP.Monto) AS SumMonto 
    FROM 
      Hospital.dbo.Recibos AS R WITH (nolock) 
      INNER JOIN Hospital.dbo.RecibosPagos AS RP WITH (nolock) ON R.Empresa = RP.Empresa AND R.Serie = RP.SerieRecibo AND R.Codigo = RP.Recibo 
      INNER JOIN NOMINADB.dbo.BasesXTipoAdmision AS TA WITH (nolock) ON R.SerieAdmision = TA.CodAdm 
      INNER JOIN NOMINADB.dbo.EmpHospital AS nh WITH (nolock) ON nh.Empresa = TA.Empresa AND TA.Codigo = nh.Codigo 
    WHERE 
      (NOT EXISTS (
          SELECT 
            DISTINCT RecibosFacturas.SerieRecibo, 
            RecibosFacturas.Recibo 
          FROM 
            Hospital.dbo.Facturas WITH (nolock) INNER JOIN 
						Hospital.dbo.RecibosFacturas WITH (nolock) ON Facturas.Empresa = RecibosFacturas.Empresa 
																													AND Facturas.Serie = RecibosFacturas.SerieFactura 
																													AND Facturas.Codigo = RecibosFacturas.Factura 
          WHERE 
            (Facturas.Tipo = 3) AND (dbo.RecibosFacturas.SerieRecibo = R.Serie) 
            AND (dbo.RecibosFacturas.Recibo = R.Codigo)
        )
      ) 
      AND (R.Status = 'P') 
      AND (TA.Empresa = @i_EmpresaUnificada) 
    GROUP BY R.Empresa, R.Serie + + CAST(R.Codigo AS VARCHAR), R.SerieAdmision, R.Admision, TA.Codigo, R.Fecha, RP.BancoTarjeta, RP.Forma 
    HAVING (R.SerieAdmision = @i_Serie) AND (R.Admision = @i_CodigoAdmision) 
			
			UNION ALL 

    Select Distinct Valor *-1 AS SumMonto 
    from AdmisionesDevoluciones (nolock) 
    where SerieAdmision = @i_Serie  and Admision = @i_CodigoAdmision 
			
			UNION ALL 
    
		select A.BeneficioTerceros as SumMonto 
    from 
      Admisiones A Inner join 
			DescuentoBeneficio DB ON ( A.Serie = DB.SerieAdmision and A.Codigo = DB.Admision) 
    where A.Serie = @i_Serie 
      and A.Codigo = @i_CodigoAdmision 
      and A.Empresa = @i_EmpresaUnificada 
      and DB.Status = 'S' 
    Group By A.BeneficioTerceros, DB.Empresa, DB.SerieAdmision, DB.Admision, DB.Fecha, DB.Autorizacion
  ) A 

INSERT INTO @CuentaAbonos
SELECT 
  TOP (250) R.Empresa, 
  R.Serie + + CAST(R.Codigo AS VARCHAR) AS Recibo, 
  SUM(RP.Monto) AS SumMonto, 
  TA.Codigo, 
  CONVERT(VARCHAR(10), R.Fecha, 103) AS Fecha, 
  CASE WHEN RP.Forma = 'B' THEN 'CHEQUE' ELSE CASE WHEN RP.Forma = 'C' THEN 'CONTADO' ELSE CASE WHEN RP.Forma = 'E' THEN 'EFECTIVO' ELSE CASE WHEN RP.Forma = 'N' THEN 'NOMINA' ELSE CASE WHEN RP.Forma = 'R' THEN 'RETENCION' ELSE CASE WHEN RP.Forma = 'S' THEN 'SEGUROS' ELSE CASE WHEN RP.Forma = 'T' THEN 'TARJETA' ELSE CASE WHEN RP.Forma = 'X' THEN 'EXCENCION' ELSE '' END END END END END END END END AS FormaPago, 
  RP.BancoTarjeta, 
  @montoTotal AS MontoTotal,
	nh.NombreCorto
FROM 
  Hospital.dbo.Recibos AS R WITH (nolock) 
  INNER JOIN Hospital.dbo.RecibosPagos AS RP WITH (nolock) ON R.Empresa = RP.Empresa 
  AND R.Serie = RP.SerieRecibo 
  AND R.Codigo = RP.Recibo 
  INNER JOIN NOMINADB.dbo.BasesXTipoAdmision AS TA WITH (nolock) ON R.SerieAdmision = TA.CodAdm 
  INNER JOIN NOMINADB.dbo.EmpHospital AS nh WITH (nolock) ON nh.Empresa = TA.Empresa 
  AND TA.Codigo = nh.Codigo 	
WHERE 
  (
    NOT EXISTS (
      SELECT 
        DISTINCT RecibosFacturas.SerieRecibo, 
        RecibosFacturas.Recibo 
      FROM 
        Hospital.dbo.Facturas WITH (nolock) 
        INNER JOIN Hospital.dbo.RecibosFacturas WITH (nolock) ON Facturas.Empresa = RecibosFacturas.Empresa 
        AND Facturas.Serie = RecibosFacturas.SerieFactura 
        AND Facturas.Codigo = RecibosFacturas.Factura 
      WHERE (Facturas.Tipo = 3) 
        AND (dbo.RecibosFacturas.SerieRecibo = R.Serie) 
        AND (dbo.RecibosFacturas.Recibo = R.Codigo)
    )
  ) 
  AND (R.Status = 'P') 
  AND (TA.Empresa = @i_EmpresaUnificada) 
GROUP BY R.Empresa, R.Serie + + CAST(R.Codigo AS VARCHAR), R.SerieAdmision, R.Admision, TA.Codigo, R.Fecha, RP.BancoTarjeta, RP.Forma , nh.NombreCorto
HAVING (R.SerieAdmision = @i_Serie) AND (R.Admision = @i_CodigoAdmision)

	UNION ALL 

Select 
  Distinct Empresa, 
  'Dev-' + Cast(Codigo as varchar) AS Recibo, 
  Valor *-1 AS SumMonto, 
  @i_EmpresaUnificada as Codigo, 
  CONVERT(VARCHAR(10), Fecha, 103) AS Fecha, 
  'DEVOLUCION' AS FormaPago, 
  isnull(CuentaBanco, '') + '-' + Cast(Movimiento as Varchar) AS BancoTarjeta, 
  '' AS MontoTotal ,
	'' NombreCorto
from 
  AdmisionesDevoluciones (nolock) 
where 
  SerieAdmision = @i_Serie 
  and Admision = @i_CodigoAdmision 

	UNION ALL 

SELECT 
  '', 
  '' AS Recibo, 
  0 AS SumMonto, 
  '', 
  '', 
  '', 
  '', 
  @montoTotal AS MontoTotal,
	'' NombreCorto
where 
  not exists(
    SELECT 
      TOP (250) R.Empresa, 
      R.Serie + + CAST(R.Codigo AS VARCHAR) AS Recibo, 
      SUM(RP.Monto) AS SumMonto, 
      TA.Codigo, 
      CONVERT(VARCHAR(10), R.Fecha, 103) AS Fecha, 
      CASE WHEN RP.Forma = 'B' THEN 'CHEQUE' ELSE CASE WHEN RP.Forma = 'C' THEN 'CONTADO' ELSE CASE WHEN RP.Forma = 'E' THEN 'EFECTIVO' ELSE CASE WHEN RP.Forma = 'N' THEN 'NOMINA' ELSE CASE WHEN RP.Forma = 'R' THEN 'RETENCION' ELSE CASE WHEN RP.Forma = 'S' THEN 'SEGUROS' ELSE CASE WHEN RP.Forma = 'T' THEN 'TARJETA' ELSE CASE WHEN RP.Forma = 'X' THEN 'EXCENCION' ELSE '' END END END END END END END END AS FormaPago, 
      RP.BancoTarjeta, 
      '' ,
			nh.NombreCorto
    FROM 
      Hospital.dbo.Recibos AS R WITH (nolock) 
      INNER JOIN Hospital.dbo.RecibosPagos AS RP WITH (nolock) ON R.Empresa = RP.Empresa 
      AND R.Serie = RP.SerieRecibo 
      AND R.Codigo = RP.Recibo 
      INNER JOIN NOMINADB.dbo.BasesXTipoAdmision AS TA WITH (nolock) ON R.SerieAdmision = TA.CodAdm 
      INNER JOIN NOMINADB.dbo.EmpHospital AS nh WITH (nolock) ON nh.Empresa = TA.Empresa 
      AND TA.Codigo = nh.Codigo 
    WHERE 
      (
        NOT EXISTS (
          SELECT 
            DISTINCT RecibosFacturas.SerieRecibo, 
            RecibosFacturas.Recibo 
          FROM 
            Hospital.dbo.Facturas WITH (nolock) 
            INNER JOIN Hospital.dbo.RecibosFacturas WITH (nolock) ON Facturas.Empresa = RecibosFacturas.Empresa 
            AND Facturas.Serie = RecibosFacturas.SerieFactura 
            AND Facturas.Codigo = RecibosFacturas.Factura 
          WHERE 
            (Facturas.Tipo = 3) 
            AND (dbo.RecibosFacturas.SerieRecibo = R.Serie) 
            AND (dbo.RecibosFacturas.Recibo = R.Codigo)
        )
      ) 
      AND (R.Status = 'P') 
      AND (TA.Empresa = @i_EmpresaUnificada) 
    GROUP BY R.Empresa, R.Serie + + CAST(R.Codigo AS VARCHAR), R.SerieAdmision, 
						 R.Admision, TA.Codigo, R.Fecha, RP.BancoTarjeta, RP.Forma , nh.NombreCorto
    HAVING (R.SerieAdmision = @i_Serie) AND (R.Admision = @i_CodigoAdmision) 

			UNION ALL 

    Select 
      Distinct Empresa, 
      'Dev-' + Cast(Codigo as varchar) AS Recibo, 
      Valor *-1 AS SumMonto, 
      @i_EmpresaUnificada as Codigo, 
      CONVERT(VARCHAR(10), Fecha, 103) AS Fecha, 
      'DEVOLUCION' AS FormaPago, 
      isnull(CuentaBanco, '') + '-' + Cast(Movimiento as Varchar) AS BancoTarjeta, 
      '' ,
			'' NombreCorto
    from 
      AdmisionesDevoluciones (nolock) 
    where SerieAdmision = @i_Serie and Admision = @i_CodigoAdmision
  )
	
	Union ALL 

select 
  'MED' As Empresa, 
  Convert(varchar(20), DB.Autorizacion) as Recibo, 
  A.BeneficioTerceros as SumMonto, 
  CASE WHEN A.EmpresaReal = 'MED' THEN 'HLA' ELSE A.EmpresaReal END As Codigo, 
  CONVERT(varchar(10), DB.Fecha, 103) as Fecha, 
  'Beneficio Terceros' as FormaPago, 
  'Club Bi' as BancoTarjeta, 
  @montoTotal AS MontoTotal ,
	IsNull((Select nombrecorto From Nominadb..emphospital With (nolock) 
						Where Empresa= @i_empresaunificada and 
									nombrecorto is not null and 
									nivel_cafeteria is null and
									codigo = CASE WHEN A.EmpresaReal = 'MED' THEN 'HLA' ELSE A.EmpresaReal END),'') NombreCorto
from 
  Admisiones A WITH (nolock) 
  Inner join DescuentoBeneficio DB WITH (nolock) ON (
    A.Serie = DB.SerieAdmision 
    and A.Codigo = DB.Admision
  ) 
  AND A.Serie = @i_Serie 
  and A.Codigo = @i_CodigoAdmision 
  and DB.Status = 'S' 
  and A.BeneficioTerceros is not null 
  LEFT JOIN Hospital.dbo.Recibos AS R WITH (nolock) ON r.Serie = A.Serie 
  AND A.codigo = R.Admision 
Group By A.BeneficioTerceros, A.EmpresaReal, DB.SerieAdmision, DB.Admision, DB.Fecha, DB.Autorizacion 

	Union All 

select 
  'MED' As Empresa, 
  Convert(varchar(20), A.AutorizacionBI) as Recibo, 
  ISNULL(Round(sum(c.Valor * a.PorcentajeDescuento / 100), 2),0) as SumMonto, 
  CASE WHEN a.EmpresaReal = 'MED' THEN 'HLA' ELSE a.EmpresaReal END As codigo, 
  CONVERT(varchar(10), da.Fecha, 103) as Fecha, 
  'Beneficio Terceros' as FormaPago, 
  'Club Bi' as BancoTarjeta, 
  @montoTotal AS MontoTotal,
	IsNull((Select nombrecorto From Nominadb..emphospital With (nolock) 
					Where Empresa= @i_empresaunificada and 
								nombrecorto is not null and 
								nivel_cafeteria is null and
								codigo = CASE WHEN A.EmpresaReal = 'MED' THEN 'HLA' ELSE A.EmpresaReal END),'') NombreCorto
from 
  Admisiones A WITH (nolock) 
  INNER JOIN Cargos c on c.serieadmision = a.serie and c.admision = a.codigo 
  INNER JOIN categorias cat on cat.codigo = c.categoria and c.empresa = a.empresa and cat.empresa = a.empresa 
  INNER JOIN descuentosaplicados da on c.serieadmision = da.serieAdmision 
  and c.admision = da.admision 
  and da.serieadmision = a.serie 
  and da.admision = a.codigo 
where 
  (A.Serie = @i_Serie) 
  and (A.Codigo = @i_CodigoAdmision) 
  and (A.AutorizacionBI > 0) 
  AND Cat.DescuentoBI = 'S' 
  and a.IdTipoDescuento is not null 
Group By A.AutorizacionBI, A.Serie, a.EmpresaReal, A.codigo, A.entrada, da.Fecha end 

Else 
Begin 
--Calculo de monto--
Select 
  @montoTotal = IsNull(sum(SumMonto),0)
FROM 
  (
    SELECT 
      SUM(RP.Monto) AS SumMonto 
    FROM 
      Hospital.dbo.Recibos AS R WITH (nolock) 
      INNER JOIN Hospital.dbo.RecibosPagos AS RP WITH (nolock) ON R.Empresa = RP.Empresa 
      AND R.Serie = RP.SerieRecibo 
      AND R.Codigo = RP.Recibo 
      INNER JOIN NOMINADB.dbo.BasesXTipoAdmision AS TA WITH (nolock) ON R.SerieAdmision = TA.CodAdm 
      INNER JOIN NOMINADB.dbo.EmpHospital AS nh WITH (nolock) ON nh.Empresa = TA.Empresa 
      AND TA.Codigo = nh.Codigo 
    WHERE 
      (
        NOT EXISTS (
          SELECT 
            DISTINCT RecibosFacturas.SerieRecibo, 
            RecibosFacturas.Recibo 
          FROM 
            Hospital.dbo.Facturas WITH (nolock) 
            INNER JOIN Hospital.dbo.RecibosFacturas WITH (nolock) ON Facturas.Empresa = RecibosFacturas.Empresa 
            AND Facturas.Serie = RecibosFacturas.SerieFactura 
            AND Facturas.Codigo = RecibosFacturas.Factura 
          WHERE 
            (Facturas.Tipo = 3) 
            AND (dbo.RecibosFacturas.SerieRecibo = R.Serie) 
            AND (dbo.RecibosFacturas.Recibo = R.Codigo)
        )
      ) 
      AND (R.Status = 'P') 
      AND (TA.Empresa = @i_EmpresaUnificada) 
    GROUP BY R.Empresa, R.Serie + + CAST(R.Codigo AS VARCHAR), R.SerieAdmision, R.Admision, 
      TA.Codigo, R.Fecha, RP.BancoTarjeta, RP.Forma 
    HAVING 
      (R.SerieAdmision = @i_Serie) AND (R.Admision = @i_CodigoAdmision) 

			UNION ALL 

    Select 
      Distinct Valor *-1 AS SumMonto 
    from 
      AdmisionesDevoluciones (nolock) 
    where 
      SerieAdmision = @i_Serie 
      and Admision = @i_CodigoAdmision 

			UNION ALL 

    select 
      ISNULL(Round(sum(c.Valor * a.PorcentajeDescuento / 100), 2), 0) as SumMonto 
    from 
      Admisiones A WITH (nolock) 
      INNER JOIN Cargos c on c.serieadmision = a.serie 
      and c.admision = a.codigo 
      INNER JOIN categorias cat on cat.codigo = c.categoria 
      and c.empresa = a.empresa 
      and cat.empresa = a.empresa 
      INNER JOIN descuentosaplicados da on c.serieadmision = da.serieAdmision 
      and c.admision = da.admision 
      and da.serieadmision = a.serie 
      and da.admision = a.codigo 
    where 
      (A.Serie = @i_Serie) 
      and (A.Codigo = @i_CodigoAdmision) 
      and (A.AutorizacionBI > 0) 
      AND Cat.DescuentoBI = 'S' 
      and a.IdTipoDescuento is not null 
    Group By       A.AutorizacionBI, A.Serie, a.EmpresaReal, A.codigo, A.entrada, da.Fecha
  ) A 

INSERT INTO @CuentaAbonos
SELECT 
  TOP (250) R.Empresa, 
  R.Serie + + CAST(R.Codigo AS VARCHAR) AS Recibo, 
  SUM(RP.Monto) AS SumMonto, 
  TA.Codigo, 
  CONVERT(VARCHAR(10), R.Fecha, 103) AS Fecha, 
  CASE WHEN RP.Forma = 'B' THEN 'CHEQUE' ELSE CASE WHEN RP.Forma = 'C' THEN 'CONTADO' ELSE CASE WHEN RP.Forma = 'E' THEN 'EFECTIVO' ELSE CASE WHEN RP.Forma = 'N' THEN 'NOMINA' ELSE CASE WHEN RP.Forma = 'R' THEN 'RETENCION' ELSE CASE WHEN RP.Forma = 'S' THEN 'SEGUROS' ELSE CASE WHEN RP.Forma = 'T' THEN 'TARJETA' ELSE CASE WHEN RP.Forma = 'X' THEN 'EXCENCION' ELSE '' END END END END END END END END AS FormaPago, 
  RP.BancoTarjeta, 
  @montoTotal AS MontoTotal ,
	nh.NombreCorto
FROM 
  Hospital.dbo.Recibos AS R WITH (nolock) 
  INNER JOIN Hospital.dbo.RecibosPagos AS RP WITH (nolock) ON R.Empresa = RP.Empresa 
  AND R.Serie = RP.SerieRecibo 
  AND R.Codigo = RP.Recibo 
  INNER JOIN NOMINADB.dbo.BasesXTipoAdmision AS TA WITH (nolock) ON R.SerieAdmision = TA.CodAdm 
  INNER JOIN NOMINADB.dbo.EmpHospital AS nh WITH (nolock) ON nh.Empresa = TA.Empresa 
  AND TA.Codigo = nh.Codigo 
WHERE 
  (
    NOT EXISTS (
      SELECT 
        DISTINCT RecibosFacturas.SerieRecibo, 
        RecibosFacturas.Recibo 
      FROM 
        Hospital.dbo.Facturas WITH (nolock) 
        INNER JOIN Hospital.dbo.RecibosFacturas WITH (nolock) ON Facturas.Empresa = RecibosFacturas.Empresa 
        AND Facturas.Serie = RecibosFacturas.SerieFactura 
        AND Facturas.Codigo = RecibosFacturas.Factura 
      WHERE 
        (Facturas.Tipo = 3) 
        AND (
          dbo.RecibosFacturas.SerieRecibo = R.Serie
        ) 
        AND (
          dbo.RecibosFacturas.Recibo = R.Codigo
        )
    )
  ) 
  AND (R.Status = 'P') 
  AND (TA.Empresa = @i_EmpresaUnificada) 
GROUP BY R.Empresa, R.Serie + + CAST(R.Codigo AS VARCHAR), R.SerieAdmision, R.Admision, TA.Codigo, 
  R.Fecha, RP.BancoTarjeta, RP.Forma , nh.NombreCorto
HAVING 
  (R.SerieAdmision = @i_Serie) 
  AND (R.Admision = @i_CodigoAdmision) 

	UNION ALL 

Select 
  Distinct Empresa, 
  'Dev-' + Cast(Codigo as varchar) AS Recibo, 
  Valor *-1 AS SumMonto, 
  @i_EmpresaUnificada as Codigo, 
  CONVERT(VARCHAR(10), Fecha, 103) AS Fecha, 
  'DEVOLUCION' AS FormaPago, 
  isnull(CuentaBanco, '') + '-' + Cast(Movimiento as Varchar) AS BancoTarjeta, 
  '' AS MontoTotal ,
	'' NombreCorto
from 
  AdmisionesDevoluciones (nolock) 
where 
  SerieAdmision = @i_Serie 
  and Admision = @i_CodigoAdmision 

	UNION ALL 

SELECT 
  '', 
  '' AS Recibo, 
  0 AS SumMonto, 
  '', 
  '', 
  '', 
  '', 
  @montoTotal AS MontoTotal ,
	'' NombreCorto
where 
  not exists(
    SELECT 
      TOP (250) R.Empresa, 
      R.Serie + + CAST(R.Codigo AS VARCHAR) AS Recibo, 
      SUM(RP.Monto) AS SumMonto, 
      TA.Codigo, 
      CONVERT(VARCHAR(10), R.Fecha, 103) AS Fecha, 
      CASE WHEN RP.Forma = 'B' THEN 'CHEQUE' ELSE CASE WHEN RP.Forma = 'C' THEN 'CONTADO' ELSE CASE WHEN RP.Forma = 'E' THEN 'EFECTIVO' ELSE CASE WHEN RP.Forma = 'N' THEN 'NOMINA' ELSE CASE WHEN RP.Forma = 'R' THEN 'RETENCION' ELSE CASE WHEN RP.Forma = 'S' THEN 'SEGUROS' ELSE CASE WHEN RP.Forma = 'T' THEN 'TARJETA' ELSE CASE WHEN RP.Forma = 'X' THEN 'EXCENCION' ELSE '' END END END END END END END END AS FormaPago, 
      RP.BancoTarjeta, 
      '' ,
			nh.NombreCorto
    FROM 
      Hospital.dbo.Recibos AS R WITH (nolock) 
      INNER JOIN Hospital.dbo.RecibosPagos AS RP WITH (nolock) ON R.Empresa = RP.Empresa 
      AND R.Serie = RP.SerieRecibo 
      AND R.Codigo = RP.Recibo 
      INNER JOIN NOMINADB.dbo.BasesXTipoAdmision AS TA WITH (nolock) ON R.SerieAdmision = TA.CodAdm 
      INNER JOIN NOMINADB.dbo.EmpHospital AS nh WITH (nolock) ON nh.Empresa = TA.Empresa 
      AND TA.Codigo = nh.Codigo 
    WHERE 
      (
        NOT EXISTS (
          SELECT 
            DISTINCT RecibosFacturas.SerieRecibo, 
            RecibosFacturas.Recibo 
          FROM 
            Hospital.dbo.Facturas WITH (nolock) 
            INNER JOIN Hospital.dbo.RecibosFacturas WITH (nolock) ON Facturas.Empresa = RecibosFacturas.Empresa 
            AND Facturas.Serie = RecibosFacturas.SerieFactura 
            AND Facturas.Codigo = RecibosFacturas.Factura 
          WHERE 
            (Facturas.Tipo = 3) 
            AND (dbo.RecibosFacturas.SerieRecibo = R.Serie) 
            AND (dbo.RecibosFacturas.Recibo = R.Codigo)
        )
      ) 
      AND (R.Status = 'P') 
      AND (TA.Empresa = @i_EmpresaUnificada) 
    GROUP BY R.Empresa,R.Serie + + CAST(R.Codigo AS VARCHAR),R.SerieAdmision,R.Admision,
						 TA.Codigo,R.Fecha,RP.BancoTarjeta,RP.Forma, nh.NombreCorto
    HAVING 
      (R.SerieAdmision = @i_Serie) 
      AND (R.Admision = @i_CodigoAdmision) 
    UNION ALL 
    Select 
      Distinct Empresa, 
      'Dev-' + Cast(Codigo as varchar) AS Recibo, 
      Valor *-1 AS SumMonto, 
      @i_EmpresaUnificada as Codigo, 
			CONVERT(VARCHAR(10),Fecha,103) AS Fecha,
      'DEVOLUCION' AS FormaPago, 
      isnull(CuentaBanco, '') + '-' + Cast(Movimiento as Varchar) AS BancoTarjeta, 
      '' ,
			'' NombreCorto
    from 
      AdmisionesDevoluciones (nolock) 
    where 
      SerieAdmision = @i_Serie 
      and Admision = @i_CodigoAdmision
  ) 
Union ALL 
select 
  'MED' As Empresa, 
  Convert(varchar(20),DB.Autorizacion) as Recibo, 
  A.BeneficioTerceros as SumMonto, 
  CASE WHEN A.EmpresaReal = 'MED' THEN 'HLA' ELSE A.EmpresaReal END As Codigo, 
  CONVERT(varchar(10),DB.Fecha,103) as Fecha,
  'Beneficio Terceros' as FormaPago, 
  'Club Bi' as BancoTarjeta, 
  @montoTotal AS MontoTotal ,
	IsNull((Select nombrecorto From Nominadb..emphospital With (nolock) 
					Where Empresa= @i_empresaunificada and 
								nombrecorto is not null and 
								nivel_cafeteria is null and
								codigo = CASE WHEN A.EmpresaReal = 'MED' THEN 'HLA' ELSE A.EmpresaReal END),'') NombreCorto
from 
  Admisiones A WITH (nolock) 
  Inner join DescuentoBeneficio DB WITH (nolock) ON (
    A.Serie = DB.SerieAdmision 
    and A.Codigo = DB.Admision
  ) 
  AND A.Serie = @i_Serie 
  and A.Codigo = @i_CodigoAdmision 
  and DB.Status = 'S' 
  and A.BeneficioTerceros is not null 
  LEFT JOIN Hospital.dbo.Recibos AS R WITH (nolock) ON r.Serie = A.Serie 
  AND A.codigo = R.Admision 
Group By A.BeneficioTerceros,A.EmpresaReal,DB.SerieAdmision,DB.Admision,DB.Fecha,DB.Autorizacion

	Union All 

select 
  'MED' As Empresa, 
  Convert(varchar(20),A.AutorizacionBI) as Recibo, 
  ISNULL(Round(sum(c.Valor * a.PorcentajeDescuento / 100),2),0) as SumMonto, 
  CASE WHEN a.EmpresaReal = 'MED' THEN 'HLA' ELSE a.EmpresaReal END As codigo, 
  CONVERT(varchar(10),da.Fecha,103) as Fecha,
  'Beneficio Terceros' as FormaPago, 
  'Club Bi' as BancoTarjeta, 
  @montoTotal AS MontoTotal,
	IsNull((Select nombrecorto From Nominadb..emphospital With (nolock) 
					Where Empresa= @i_empresaunificada and 
								nombrecorto is not null and 
								nivel_cafeteria is null and
								codigo = CASE WHEN A.EmpresaReal = 'MED' THEN 'HLA' ELSE A.EmpresaReal END),'') NombreCorto
from 
  Admisiones A WITH (nolock) 
  INNER JOIN Cargos c on c.serieadmision = a.serie 
  and c.admision = a.codigo 
  INNER JOIN categorias cat on cat.codigo = c.categoria 
  and c.empresa = a.empresa 
  and cat.empresa = a.empresa 
  INNER JOIN descuentosaplicados da on c.serieadmision = da.serieAdmision 
  and c.admision = da.admision 
  and da.serieadmision = a.serie 
  and da.admision = a.codigo 
where 
  (A.Serie = @i_Serie) 
  and (A.Codigo = @i_CodigoAdmision) 
  and (A.AutorizacionBI > 0) 
  AND Cat.DescuentoBI = 'S' 
  and a.IdTipoDescuento = 1 
Group By A.AutorizacionBI,A.Serie,a.EmpresaReal,A.codigo,A.entrada,da.Fecha

End

RETURN
END
GO
-->>
USE [HOSPITAL]
GO

/****** Object:  UserDefinedFunction [dbo].[fnEstadoCuentaCargos]    Script Date: 05/07/2023 11:06:51 a.m. ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

-- ==========================================================
-- Author:  Javier Castillo
-- Create date: 2023.06.01
-- Description:	Funcion para traer los cargos en el estado de cuenta
-- ==========================================================

CREATE FUNCTION [dbo].[fnEstadoCuentaCargos] (
    @i_EmpresaUnificada varchar(3),
		@i_Sucursal varchar(3),
		@i_Serie varchar(5),
		@i_CodigoAdmision integer,
		@i_ConExtra char(1)
)
RETURNS TABLE
AS
RETURN(
SELECT   Area, SUM(Valor) AS Valor, SUM(ValorDesc) AS ValorDesc, AVG(Descuento) as Descuento, AdmisionCompleta as Admision, 
				 Seguro as Poliza, Pacientes as Paciente, Hospital, Habitacion, Entrada, Salida, Aseguradora,  SUM(DescuTotal) AS DescuTotal, 
				 AVG(DescArbitrario) As DescArb, Ordenado
FROM  (
SELECT        X.AdmisionPaciente, X.AdmisionCompleta, X.Pacientes, X.Entrada, X.Salida, X.Ordenmiento AS Ordenado, X.AREA AS Area, RTRIM(X.Ordenhabitacion) AS OrdenHabitacion, RTRIM(X.AdmisionArea) AS AdmisionArea,
                         RTRIM(X.OrdenTipoUbicacion) AS OrdenTipoUbicacion, X.Cantidad, X.CostoHospital AS Costo, X.Producto, X.Nombre, X.Valor, RTRIM(EH.Nombre) AS Hospital, RTRIM(EH.Direccion) AS Direccion, RTRIM(EH.Pbx) AS Telefono,
                         X.IdCliente, CASE WHEN Categorias.Descuento = 'S' THEN X.Valor * ISNULL(X.PorcentajeDescuento, 0) / 100 ELSE 0 END AS Descu,
                         CASE WHEN Categorias.Descuento = 'S' THEN X.Valor - X.Valor * ISNULL(X.PorcentajeDescuento, 0) / 100 ELSE X.Valor END AS ValorDesc, X.Habitacion, X.Seguro, Aseguradoras.Nombre AS Aseguradora,
                         X.PorcentajeDescuento AS Descuento, X.Fecha, X.TipoOrden, X.Orden, X.Categoria AS CodCat, CAST(CASE WHEN Categorias.Descuento = 'S' THEN X.Valor * ISNULL(X.PorcentajeDescuento, 0) / 100 ELSE 0 END AS FLOAT)
                         AS DescuTotal, RTRIM(Categorias.Nombre) AS Categoria, X.UnidadMedida, HOSPITAL.dbo.Categorias.Descuento AS TieneDesc, X.Descuento AS DescArbitrario, EH.Empresa
FROM            HOSPITAL.dbo.Aseguradoras WITH (nolock) INNER JOIN
                         HOSPITAL.dbo.Seguros WITH (nolock) ON HOSPITAL.dbo.Aseguradoras.Asegura = HOSPITAL.dbo.Seguros.Asegura AND HOSPITAL.dbo.Aseguradoras.Empresa = HOSPITAL.dbo.Seguros.Empresa RIGHT OUTER JOIN
                         NOMINADB.dbo.EmpHospital AS EH WITH (nolock) INNER JOIN
                             (SELECT        A.Empresa, A.Serie, A.Codigo AS Admision, A.Serie + '-' + CAST(A.Codigo AS varchar) AS AdmisionCompleta, CASE WHEN A.Interno = 'S' THEN 'INTERNO' ELSE CASE WHEN ISNULL
                             ((SELECT        [Empresa]
                                 FROM            [NOMINADB].[dbo].[BasesXTipoAdmision] WITH (nolock)
                                 WHERE        EMPRESA = A.Empresa AND [CodAdm] = A.Serie AND Descripcion = 'Emergencia'), '') = '' THEN 'AMBULATORIO' ELSE 'EMERGENCIA' END END AS TIPO, A.Entrada, A.Salida, O.Fecha, O.Tipo AS TipoOrden,
                         O.Codigo AS Orden, CAST(HOSPITAL.dbo.func_Area(2, C.Categoria, O.Tipo, OT.GrupoEstCta, p.Subdepartamento, AH.Nombre, O.OrdenTipoUbicacion, Areas_Habitaciones_1.Nombre) AS INTEGER) AS Ordenmiento,
                         HOSPITAL.dbo.func_Area(1, C.Categoria, O.Tipo, OT.GrupoEstCta, p.Subdepartamento, AH.Nombre, O.OrdenTipoUbicacion, Areas_Habitaciones_1.Nombre) AS AREA, Areas_Habitaciones_1.Nombre AS Ordenhabitacion,
                         Areas_Habitaciones_2.Nombre AS AdmisionArea, AH.Nombre AS OrdenTipoUbicacion, C.Cantidad, p.Nombre, C.Valor, CAST(C.Costo AS FLOAT) * CAST(C.Cantidad AS FLOAT) * 1.10000 AS CostoHospital, RTRIM(Pac.Nombre)
                         + ' ' + RTRIM(Pac.Apellido) + CASE WHEN isnull(Pac.ApellidoCasada, '') = '' THEN '' ELSE Rtrim(Pac.ApellidoCasada) END AS Pacientes, '[' + A.Serie + '-' + CAST(A.Codigo AS varchar) + '] ' + RTRIM(Pac.Nombre)
                         + ' ' + RTRIM(Pac.Apellido) + CASE WHEN isnull(Pac.ApellidoCasada, '') = '' THEN '' ELSE Rtrim(Pac.ApellidoCasada) END AS AdmisionPaciente, Pac.IdCliente, ISNULL(A.UltimaHab, ISNULL(O.Habitacion, '')) AS Habitacion,
                         A.Seguro, A.PorcentajeDescuento, C.Categoria, C.Producto, C.UnidadMedida, A.Descuento, C.Linea
FROM            HOSPITAL.dbo.Areas_Habitaciones AS AH WITH (nolock) RIGHT OUTER JOIN
(SELECT        CAN.Empresa, CAN.TipoOrden, CAN.Orden, CAN.Linea, CAN.SerieAdmision, CAN.Admision, CAN.Fecha, CAN.Producto, CASE WHEN CAN.Categoria = '06' THEN PP.Categoria ELSE CAN.Categoria END AS Categoria, 
                                                         CAN.Valor, CAN.Cantidad, CAN.Ajeno, CAN.PrecioUnitario, CAN.Factor, CAN.UnidadMedida, CAN.Costo, 
                                                         CAN.CostoHospital, CAN.CostoUltimo
                               FROM            INVENTARIO.dbo.Productos AS PP WITH (nolock) INNER JOIN
                                                             (SELECT        Empresa, TipoOrden, Orden, MIN(Linea) AS Linea, SerieAdmision, Admision, MIN(Fecha) AS Fecha, CASE WHEN GrupoComplementarios > 250 THEN GrupoComplementarios ELSE Producto END AS Producto, 
                         MAX(CASE WHEN PRODUCTO = GRUPOCOMPLEMENTARIOS THEN Categoria ELSE CASE WHEN grupocomplementarios > 250 THEN '0' ELSE categoria END END) AS Categoria, SUM(Valor) AS Valor, 
                         SUM(CASE WHEN PRODUCTO = GRUPOCOMPLEMENTARIOS THEN CANTIDAD ELSE CASE WHEN grupocomplementarios > 250 THEN 0 ELSE Cantidad END END) AS Cantidad, ISNULL(Ajeno, 0) AS Ajeno, SUM(PrecioUnitario) 
                         AS PrecioUnitario, MAX(Factor) AS Factor, MIN(UnidadMedida) AS UnidadMedida, SUM(Costo) AS Costo, SUM(CostoHospital) AS CostoHospital, SUM(CostoUltimo) AS CostoUltimo
FROM            Hospital.dbo.Cargos WITH (nolock)
GROUP BY Empresa, TipoOrden, Orden, SerieAdmision, Admision, ISNULL(Ajeno, 0), GrupoComplementarios, CASE WHEN GrupoComplementarios > 250 THEN GrupoComplementarios ELSE Producto END
HAVING        (Empresa = @i_EmpresaUnificada)) AS CAN ON CAN.Empresa = PP.Empresa AND PP.Codigo = CAN.Producto) AS C INNER JOIN
                         HOSPITAL.dbo.Admisiones AS A WITH (nolock) INNER JOIN
                         HOSPITAL.dbo.Ordenes AS O WITH (nolock) ON A.Empresa = O.Empresa AND A.Serie = O.SerieAdmision AND A.Codigo = O.Admision ON C.Orden = O.Codigo AND C.TipoOrden = O.Tipo AND
                         C.Empresa = O.Empresa LEFT OUTER JOIN
                         HOSPITAL.dbo.Pacientes AS Pac WITH (nolock) ON A.Empresa = Pac.Empresa AND A.Paciente = Pac.Codigo ON AH.Codigo = O.OrdenTipoUbicacion AND AH.Empresa = O.Empresa LEFT OUTER JOIN
                         INVENTARIO.dbo.Productos AS p WITH (nolock) ON C.Producto = p.Codigo AND C.Empresa = p.Empresa LEFT OUTER JOIN
                         HOSPITAL.dbo.OrdenesTipos AS OT WITH (nolock) ON O.Tipo = OT.Codigo AND O.Empresa = OT.Empresa LEFT OUTER JOIN
                         HOSPITAL.dbo.Habitaciones AS H WITH (nolock) LEFT OUTER JOIN
                         HOSPITAL.dbo.Areas_Habitaciones AS Areas_Habitaciones_2 WITH (nolock) ON H.Empresa = Areas_Habitaciones_2.Empresa AND H.Area = Areas_Habitaciones_2.Codigo ON A.UltimaHab = H.Codigo AND
                         A.Empresa = H.Empresa LEFT OUTER JOIN
                         HOSPITAL.dbo.Habitaciones AS Habitaciones_1 WITH (nolock) ON O.Empresa = Habitaciones_1.Empresa AND O.Habitacion = Habitaciones_1.Codigo LEFT OUTER JOIN
                         HOSPITAL.dbo.Areas_Habitaciones AS Areas_Habitaciones_1 WITH (nolock) ON Habitaciones_1.Empresa = Areas_Habitaciones_1.Empresa AND Habitaciones_1.Area = Areas_Habitaciones_1.Codigo
WHERE        (A.Empresa = @i_EmpresaUnificada) AND (C.Valor > 0) AND (A.Serie = @i_Serie) AND (A.Codigo = @i_CodigoAdmision)) AS X INNER JOIN
                         HOSPITAL.dbo.Categorias ON X.Empresa = HOSPITAL.dbo.Categorias.Empresa AND X.Categoria = HOSPITAL.dbo.Categorias.Codigo ON EH.Empresa = X.Empresa ON HOSPITAL.dbo.Seguros.Empresa = X.Empresa AND
                         HOSPITAL.dbo.Seguros.Codigo = X.Seguro
WHERE        (X.Categoria <> '30') AND (EH.Codigo = @i_Sucursal) OR
                         (EH.Codigo = @i_Sucursal) AND (CASE WHEN Categoria <> '30' THEN 'N' ELSE 'S' END = @i_ConExtra)
) AS alkjañ GROUP BY  Area, Ordenado, AdmisionCompleta, Seguro, Pacientes, Hospital, Habitacion, Entrada, Salida, Aseguradora, Descuento
)
GO
-->>
USE [HOSPITAL]
GO

/****** Object:  UserDefinedFunction [dbo].[fnEstadoCuentaClienteDetalle]    Script Date: 05/07/2023 11:09:14 a.m. ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

-- ==========================================================
-- Author:  Javier Castillo
-- Create date: 2023.06.01
-- Description:	Funcion para generar el detalle del estado de cuenta
-- ==========================================================

CREATE FUNCTION [dbo].[fnEstadoCuentaClienteDetalle] (
    @i_EmpresaUnificada varchar(3),
		@i_Sucursal varchar(3),
		@i_Serie varchar(5),
		@i_CodigoAdmision integer,
		@i_ConExtra char(1),
		@i_ConValor bit
)
RETURNS TABLE
AS
RETURN(
SELECT        X.AdmisionPaciente, X.AdmisionCompleta as Admision, X.Pacientes Paciente, X.Entrada, X.Salida, X.Ordenmiento AS Ordenado, X.AREA AS Area, RTRIM(X.Ordenhabitacion) AS OrdenHabitacion, RTRIM(X.AdmisionArea) AS AdmisionArea, 
                         RTRIM(X.OrdenTipoUbicacion) AS OrdenTipoUbicacion, X.Cantidad, X.CostoHospital AS Costo, X.Producto, X.Nombre, CASE WHEN @i_ConValor = 1 THEN X.Valor ELSE NULL END AS Valor, RTRIM(EH.Nombre) AS Hospital,
                         RTRIM(EH.Direccion) AS Direccion, RTRIM(EH.Pbx) AS Telefono, X.IdCliente, CASE WHEN Categorias.Descuento = 'S' THEN X.Valor * ISNULL(X.PorcentajeDescuento, 0) / 100 ELSE 0 END AS Descu, 
                         CASE WHEN Categorias.Descuento = 'S' THEN X.Valor - X.Valor * ISNULL(X.PorcentajeDescuento, 0) / 100 ELSE X.Valor END AS ValorDesc, X.Habitacion, X.Seguro Poliza, Aseguradoras.Nombre AS Aseguradora, 
                         X.PorcentajeDescuento AS Descuento, CONVERT(VARCHAR,x.FECHA,3) FECHA, X.TipoOrden, X.Orden, X.Categoria AS CodCat, CAST(CASE WHEN Categorias.Descuento = 'S' THEN X.Valor * ISNULL(X.PorcentajeDescuento, 0) 
                         / 100 ELSE 0 END + ISNULL(X.Descuento, 0) AS FLOAT) AS DescuTotal, RTRIM(Categorias.Nombre) AS Categoria, X.UnidadMedida, Categorias.Descuento AS TieneDesc, X.Descuento AS DescArbitrario
FROM            NOMINADB.dbo.EmpHospital AS EH WITH (nolock) RIGHT OUTER JOIN
                             (SELECT        A.Empresa, A.Serie, A.Codigo AS Admision, A.Serie + '-' + CAST(A.Codigo AS varchar) AS AdmisionCompleta, CASE WHEN A.Interno = 'S' THEN 'INTERNO' ELSE CASE WHEN ISNULL
                             ((SELECT        [Empresa]
                                 FROM            [NOMINADB].[dbo].[BasesXTipoAdmision] WITH (nolock)
                                 WHERE        EMPRESA = A.Empresa AND [CodAdm] = A.Serie AND Descripcion = 'Emergencia'), '') = '' THEN 'AMBULATORIO' ELSE 'EMERGENCIA' END END AS TIPO, A.Entrada, A.Salida, O.Fecha, O.Tipo AS TipoOrden, 
                         O.Codigo AS Orden, CAST(Hospital.dbo.func_Area(2, C.Categoria, O.Tipo, OT.GrupoEstCta, p.Subdepartamento, AH.Nombre, O.OrdenTipoUbicacion, Areas_Habitaciones_1.Nombre) AS INTEGER) AS Ordenmiento, Hospital.dbo.func_Area(1, C.Categoria,
                         O.Tipo, OT.GrupoEstCta, p.Subdepartamento, AH.Nombre, O.OrdenTipoUbicacion, Areas_Habitaciones_1.Nombre) AS AREA, Areas_Habitaciones_1.Nombre AS Ordenhabitacion, Areas_Habitaciones_2.Nombre AS AdmisionArea,
                         AH.Nombre AS OrdenTipoUbicacion, CASE WHEN CASE WHEN Isnull(OT.Hospital, '') = '' THEN CASE WHEN isnull(A.EmpresaReal, '') = '' THEN CASE WHEN isnull(O.Empresareal, '')
                         = '' THEN '' ELSE O.Empresareal END ELSE A.EmpresaReal END ELSE OT.Hospital END = @i_EmpresaUnificada THEN 'HLA' ELSE CASE WHEN Isnull(OT.Hospital, '') = '' THEN CASE WHEN isnull(A.EmpresaReal, '') 
                         = '' THEN CASE WHEN isnull(O.Empresareal, '') = '' THEN '' ELSE O.Empresareal END ELSE A.EmpresaReal END ELSE OT.Hospital END END AS Base, C.Cantidad,
                         RTRIM(p.Nombre) + CASE WHEN ISNULL(RTRIM(Aj.Nombre), '')='' THEN '' ELSE '/' + RTRIM( CASE WHEN Isnull(Aj.Titulo,'')='' THEN '' ELSE RTRIM(Aj.Titulo) + '. ' END  ) + RTRIM(Aj.Nombre) + ' ' + RTRIM(Aj.Apellido) END AS Nombre,
                         C.Valor, CAST(C.Costo AS FLOAT) * CAST(C.Cantidad AS FLOAT) * 1.10000 AS CostoHospital, RTRIM(Pac.Nombre)
                         + ' ' + RTRIM(Pac.Apellido) + CASE WHEN isnull(Pac.ApellidoCasada, '') = '' THEN '' ELSE Rtrim(Pac.ApellidoCasada) END AS Pacientes, '[' + A.Serie + '-' + CAST(A.Codigo AS varchar) + '] ' + RTRIM(Pac.Nombre) 
                         + ' ' + RTRIM(Pac.Apellido) + CASE WHEN isnull(Pac.ApellidoCasada, '') = '' THEN '' ELSE Rtrim(Pac.ApellidoCasada) END AS AdmisionPaciente, Pac.IdCliente, ISNULL(A.UltimaHab, ISNULL(O.Habitacion, '')) AS Habitacion, 
                         A.Seguro, A.PorcentajeDescuento, C.Categoria, C.Producto, C.UnidadMedida, A.Descuento, C.Linea
FROM            Hospital.dbo.Pacientes AS Pac WITH (nolock) RIGHT OUTER JOIN
(SELECT        CAN.Empresa, CAN.TipoOrden, CAN.Orden, CAN.Linea, CAN.SerieAdmision, CAN.Admision, CAN.Fecha, CAN.Producto, CASE WHEN CAN.Categoria = '06' THEN PP.Categoria ELSE CAN.Categoria END AS Categoria, 
                                                         CAN.Valor, CAN.Cantidad, CAN.Ajeno, CAN.PrecioUnitario, CAN.Factor, CAN.UnidadMedida, CAN.Costo, 
                                                         CAN.CostoHospital, CAN.CostoUltimo
                               FROM            INVENTARIO.dbo.Productos AS PP WITH (nolock) INNER JOIN
                                                             (SELECT        Empresa, TipoOrden, Orden, MIN(Linea) AS Linea, SerieAdmision, Admision, MIN(Fecha) AS Fecha, CASE WHEN GrupoComplementarios > 250 THEN GrupoComplementarios ELSE Producto END AS Producto, 
                         MAX(CASE WHEN PRODUCTO = GRUPOCOMPLEMENTARIOS THEN Categoria ELSE CASE WHEN grupocomplementarios > 250 THEN '0' ELSE categoria END END) AS Categoria, SUM(Valor) AS Valor, 
                         SUM(CASE WHEN PRODUCTO = GRUPOCOMPLEMENTARIOS THEN CANTIDAD ELSE CASE WHEN grupocomplementarios > 250 THEN 0 ELSE Cantidad END END) AS Cantidad, ISNULL(Ajeno, 0) AS Ajeno, SUM(PrecioUnitario) 
                         AS PrecioUnitario, MAX(Factor) AS Factor, MIN(UnidadMedida) AS UnidadMedida, SUM(Costo) AS Costo, SUM(CostoHospital) AS CostoHospital, SUM(CostoUltimo) AS CostoUltimo
FROM            Hospital.dbo.Cargos WITH (nolock)
GROUP BY Empresa, TipoOrden, Orden, SerieAdmision, Admision, ISNULL(Ajeno, 0), GrupoComplementarios, CASE WHEN GrupoComplementarios > 250 THEN GrupoComplementarios ELSE Producto END
HAVING        (Empresa = @i_EmpresaUnificada)) AS CAN ON CAN.Empresa = PP.Empresa AND PP.Codigo = CAN.Producto) AS C INNER JOIN
                         Hospital.dbo.Admisiones AS A WITH (nolock) INNER JOIN
                         Hospital.dbo.Ordenes AS O WITH (nolock) ON A.Empresa = O.Empresa AND A.Serie = O.SerieAdmision AND A.Codigo = O.Admision ON C.Orden = O.Codigo AND C.TipoOrden = O.Tipo AND C.Empresa = O.Empresa LEFT OUTER JOIN
                         Hospital.dbo.Ajenos AS AJ WITH (nolock) ON C.Ajeno = AJ.Codigo AND C.Empresa = AJ.Empresa ON Pac.Empresa = A.Empresa AND Pac.Codigo = A.Paciente LEFT OUTER JOIN
                         Hospital.dbo.Areas_Habitaciones AS AH WITH (nolock) ON O.OrdenTipoUbicacion = AH.Codigo AND O.Empresa = AH.Empresa LEFT OUTER JOIN
                         INVENTARIO.dbo.Productos AS p WITH (nolock) ON C.Producto = p.Codigo AND C.Empresa = p.Empresa LEFT OUTER JOIN
                         Hospital.dbo.OrdenesTipos AS OT WITH (nolock) ON O.Tipo = OT.Codigo AND O.Empresa = OT.Empresa LEFT OUTER JOIN
                         Hospital.dbo.Habitaciones AS H WITH (nolock) LEFT OUTER JOIN
                         Hospital.dbo.Areas_Habitaciones AS Areas_Habitaciones_2 WITH (nolock) ON H.Empresa = Areas_Habitaciones_2.Empresa AND H.Area = Areas_Habitaciones_2.Codigo ON A.UltimaHab = H.Codigo AND 
                         A.Empresa = H.Empresa LEFT OUTER JOIN
                         Hospital.dbo.Habitaciones AS Habitaciones_1 WITH (nolock) ON O.Empresa = Habitaciones_1.Empresa AND O.Habitacion = Habitaciones_1.Codigo LEFT OUTER JOIN
                         Hospital.dbo.Areas_Habitaciones AS Areas_Habitaciones_1 WITH (nolock) ON Habitaciones_1.Empresa = Areas_Habitaciones_1.Empresa AND Habitaciones_1.Area = Areas_Habitaciones_1.Codigo
WHERE        (A.Empresa = @i_EmpresaUnificada) AND (C.Valor > 0) AND (A.Serie = @i_Serie) AND (A.Codigo = @i_CodigoAdmision)) AS X INNER JOIN
                         Hospital.dbo.Categorias ON X.Empresa = Hospital.dbo.Categorias.Empresa AND X.Categoria = Hospital.dbo.Categorias.Codigo LEFT OUTER JOIN
                         Hospital.dbo.Aseguradoras WITH (nolock) INNER JOIN
                         Hospital.dbo.Seguros WITH (nolock) ON Hospital.dbo.Aseguradoras.Empresa = Hospital.dbo.Seguros.Empresa AND Hospital.dbo.Aseguradoras.Asegura = Hospital.dbo.Seguros.Asegura ON X.Empresa = Hospital.dbo.Seguros.Empresa AND X.Seguro = Hospital.dbo.Seguros.Codigo ON
                         EH.Empresa = X.Empresa
WHERE        (X.Categoria <> '30') AND (EH.Codigo = @i_Sucursal) OR
                         (CASE WHEN Categoria <> '30' THEN 'N' ELSE 'S' END = @i_ConExtra) AND (EH.Codigo = @i_Sucursal)
)
GO

-->>
USE [HOSPITAL]
GO
/****** Object:  UserDefinedFunction [dbo].[fnEstadoCuentaFacturas]    Script Date: 05/07/2023 11:13:49 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- ==========================================================
-- Author:  Javier Castillo
-- Create date: 2023.06.01
-- Description:	Querys para obtner las facturas en el  estados de cuenta 
-- ==========================================================

CREATE FUNCTION [dbo].[fnEstadoCuentaFacturas] (
    @i_EmpresaUnificada varchar(3),		
		@i_Serie varchar(5),
		@i_CodigoAdmision integer		
)
RETURNS TABLE
AS
RETURN(
			 SELECT F.SerieAdmision, CASE WHEN f.Empresa = 'MED' THEN 'HLA' ELSE f.Empresa END AS Hospital, ISNULL(E.SerieFace, F.Serie) AS Serie, 
							ISNULL(E.NumeroFace, F.Codigo) AS Codigo, CONVERT(varchar(10), F.Fecha, 105) AS Fecha, F.FechaRegistro AS TipoFecha, 
							CASE WHEN tipo = 3 THEN 'Seguro' ELSE CASE WHEN tipo = 2 THEN 'Copago/Coaseguro' ELSE CASE WHEN tipo = 1 THEN 'Extraordinarios' ELSE '' END END END AS TipoFactura,
              CASE WHEN tipo = 3 THEN Total - ISNULL(Descuento, 0) - isnull(Excedente, 0) ELSE Total - ISNULL(Descuento, 0) END AS Total, 
							F.Tipo, F.Serie AS Expr1, F.Codigo AS Expr2, CASE WHEN F.tipo = 3 THEN F.Saldo ELSE 0.00 END AS Saldo
				 FROM Facturas AS F WITH (nolock) LEFT OUTER JOIN
              FacturasFacE AS E WITH (nolock) ON F.Empresa = E.Empresa AND F.Serie = E.Serie AND F.Codigo = E.Codigo AND E.TipoDoc = 1
				WHERE (F.SerieAdmision = @i_Serie) AND (F.Admision = @i_CodigoAdmision) AND (F.Tipo IN (1, 2, 3)) AND (F.Status = 'P')

Union ALL

			 SELECT L.SerieAdmision, CASE WHEN EmpresaReal = 'MED' THEN 'HLA' ELSE EmpresaReal END AS Hospital, ISNULL(E.SerieFacE, L.SerieFactura) AS Serie,
						  ISNULL(E.NumeroFacE, L.Factura) AS Codigo, CONVERT(varchar(10),L.Registro, 105) AS Fecha, L.Registro AS TipoFecha, 
							'Seguro' AS TipoFactura, L.TotalAdmision - ISNULL(L.CopagoMonto, 0) AS Total, 
							3 AS Tipo, L.SerieFactura AS Expr1, L.Factura AS Expr2, L.SaldoAdmision AS Saldo
				 FROM FacturasLoteAdmisiones AS L WITH (nolock) LEFT OUTER JOIN
              FacturasFacE AS E WITH (nolock) ON L.EmpresaFactura = E.Empresa AND L.SerieFactura = E.Serie AND L.Factura = E.Codigo AND E.TipoDoc = 1
				WHERE (L.SerieAdmision = @i_Serie) AND (L.Admision = @i_CodigoAdmision)

Union ALL

			 SELECT F.SerieAdmision, CASE WHEN nc.Empresa = 'MED' THEN 'HLA' ELSE nc.Empresa END AS Hospital, ISNULL(NCE.SerieFacE, nc.SerieNota) AS Serie,
							ISNULL(NCE.NumeroFacE, nc.Codigo) AS Codigo, CONVERT(varchar(10), nc.Fecha, 105) AS Fecha, nc.Registro AS TipoFecha, 
							CASE WHEN F.Tipo = 3 THEN 'Nota Credito' ELSE CASE WHEN RebajarSaldoAdmCopago = 1 THEN 'Nota Credito' ELSE 'Nota Credito' END END AS TipoFactura,
              (nc.Servicios + nc.Ventas) * - 1 AS Total, 
							F.Tipo, F.Serie AS Expr1, F.Codigo AS Expr2, 0.00 AS Saldo
				 FROM NotasCredito AS nc WITH (nolock) INNER JOIN
              Facturas AS F WITH (nolock) ON nc.Empresa = F.Empresa AND nc.SerieFactura = F.Serie AND nc.Factura = F.Codigo LEFT OUTER JOIN
              FacturasFacE AS NCE WITH (nolock) ON nc.Codigo = NCE.Codigo AND nc.SerieNota = NCE.Serie AND nc.Empresa = NCE.Empresa AND NCE.TipoDoc = 2 LEFT OUTER JOIN
              FacturasFacE AS FE1 WITH (nolock) ON F.Empresa = FE1.Empresa AND F.Serie = FE1.Serie AND F.Codigo = FE1.Codigo AND FE1.TipoDoc = 1
				WHERE (F.SerieAdmision = @i_Serie) AND (F.Admision = @i_CodigoAdmision) AND (F.Tipo IN (1, 2, 3)) AND (F.Status = 'P') AND (nc.Status = 'V')



UNION ALL

    select DB.SerieAdmision,CASE WHEN EmpresaReal = 'MED' THEN 'HLA' ELSE EmpresaReal END As Empresa,'' As Serie,
					 DB.Autorizacion as Codigo,CONVERT(varchar(10), DB.Fecha, 105) as Fecha,DB.Fecha as TipoFecha,'Beneficio Terceros' as TipoFactura, 
					 A.BeneficioTerceros as Total,
           100 as Tipo,'' AS Expr1, DB.Autorizacion as Expr2, 0.00 as Saldo                  
      from Admisiones A Inner join DescuentoBeneficio DB 
        ON (A.Serie = DB.SerieAdmision and A.Codigo =DB.Admision)
     where (A.Serie=@i_Serie) and (A.Codigo=@i_CodigoAdmision) and (DB.Status='S') and (A.BeneficioTerceros  is not null)
     Group By A.BeneficioTerceros,DB.SerieAdmision,EmpresaReal ,DB.Admision,DB.Fecha,DB.Autorizacion


Union All  

	   Select A.Serie,CASE WHEN a.EmpresaReal = 'MED' THEN 'HLA' ELSE a.EmpresaReal END As Empresa,'' As Serie,
						A.AutorizacionBI as Codigo,CONVERT(varchar(10), da.Fecha, 105) as Fecha,a.entrada as TipoFecha,'Beneficio Terceros' as TipoFactura, 
						ISNULL(Round(sum(c.Valor*a.PorcentajeDescuento / 100 ),2),0) as Total,
            100 as Tipo,'' AS Expr1, A.AutorizacionBI as Expr2, 0.00 as Saldo                  
				from Admisiones A WITH (nolock)
	INNER JOIN Cargos c on c.serieadmision=a.serie and c.admision=a.codigo
	INNER JOIN categorias cat on cat.codigo=c.categoria and c.empresa=a.empresa and cat.empresa=a.empresa
	INNER JOIN descuentosaplicados da on c.serieadmision=da.serieAdmision and c.admision=da.admision and
						 da.serieadmision=a.serie and da.admision=a.codigo 
			 where (A.Serie=@i_Serie) and (A.Codigo=@i_CodigoAdmision) and  (A.AutorizacionBI>0)  AND 
						 Cat.DescuentoBI='S' and a.IdTipoDescuento is not null
     Group By A.AutorizacionBI,A.Serie,a.EmpresaReal ,A.codigo,A.entrada,da.Fecha
)
GO
-->>
USE [HOSPITAL]
GO

/****** Object:  UserDefinedFunction [dbo].[fnEstadoCuentaSaldos]    Script Date: 05/07/2023 11:15:03 a.m. ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


-- ==========================================================
-- Author:  Vinicio Cahuex
-- Create date: 2023.06.01
-- Description:	Querys para obtener los saldo en el estado de cuenta
-- ==========================================================

CREATE FUNCTION [dbo].[fnEstadoCuentaSaldos] (
    @i_EmpresaUnificada varchar(3),		
		@i_Serie varchar(5),
		@i_CodigoAdmision integer		
)
RETURNS TABLE
AS
RETURN(
	SELECT 	
		SerieAdmision,
		'Extraordinarios' AS Descrip, 
		SUM(Valor) AS COPA 
	FROM 
		Cargos WITH (nolock) 
	WHERE 
		(Empresa = @i_EmpresaUnificada) 
		AND (SerieAdmision = @i_Serie) 
		AND (Admision = @i_CodigoAdmision) 
		AND (Categoria = '30') 
	GROUP BY SerieAdmision, Admision 
	union all 
	SELECT 
		CA.SerieAdmision, 
		'Cuenta Ajena No Elegible' AS Descrip, 
		SUM(CA.Valor) AS COPA 
	FROM 
		Cargos AS CA WITH (nolock) 
		INNER JOIN Admisiones AS AD WITH (nolock) ON CA.Empresa = AD.Empresa 
		AND CA.SerieAdmision = AD.Serie 
		AND CA.Admision = AD.Codigo 
	WHERE 
		(CA.Empresa = @i_EmpresaUnificada) 
		AND (CA.SerieAdmision = @i_Serie) 
		AND (CA.Admision = @i_CodigoAdmision) 
		AND (CA.Categoria = '98') 
	GROUP BY CA.SerieAdmision, CA.Admision, AD.Seguro 
	union all 
	SELECT 
		CA.SerieAdmision, 
		'Cuenta Ajena' AS Descrip, 
		SUM(CA.Valor) AS COPA 
	FROM 
		Cargos AS CA WITH (nolock) 
		INNER JOIN Admisiones AS AD WITH (nolock) ON CA.Empresa = AD.Empresa 
		AND CA.SerieAdmision = AD.Serie 
		AND CA.Admision = AD.Codigo 
	WHERE 
		(CA.Empresa = @i_EmpresaUnificada) 
		AND (CA.SerieAdmision = @i_Serie) 
		AND (CA.Admision = @i_CodigoAdmision) 
		AND (CA.Categoria = '99') 
		AND (AD.Seguro IS NULL or Rtrim(AD.Seguro)= '') 
	GROUP BY CA.SerieAdmision, CA.Admision, AD.Seguro 
	union all 
	SELECT 
		Serie, 
		CASE WHEN DEDUCIBLE > 0 THEN 'Copago + Deducible' ELSE 'Copago' end AS Descrip, 
		CopagoACobrar - isnull(GastosEspNoCubiertos, 0) - isnull(CoaseguroACobrar, 0) + isnull(Deducible, 0) AS COPA 
	FROM 
		Admisiones WITH (nolock) 
	WHERE 
		(Empresa = @i_EmpresaUnificada) 
		AND (Serie = @i_Serie) 
		AND (Codigo = @i_CodigoAdmision) 
		AND (CopagoACobrar IS NOT NULL) 
		and (seguro is not null OR Rtrim(seguro)<> '') 
	union all 
	SELECT 
		Serie, 
		'Coaseguro' AS Descrip, 
		CoaseguroACobrar AS COPA 
	FROM 
		Admisiones WITH (nolock) 
	WHERE 
		(Empresa = @i_EmpresaUnificada) 
		AND (Serie = @i_Serie) 
		AND (Codigo = @i_CodigoAdmision) 
		AND (CoaseguroACobrar IS NOT NULL) 
		and (seguro is not null AND Rtrim(seguro)<> '' ) 
	union all 
	SELECT 
		Serie, 
		'Gastos Especiales no Cubiertos' AS Descrip, 
		GastosEspNoCubiertos AS COPA 
	FROM 
		Admisiones WITH (nolock) 
	WHERE 
		(Empresa = @i_EmpresaUnificada) 
		AND (Serie = @i_Serie) 
		AND (Codigo = @i_CodigoAdmision) 
		AND (GastosEspNoCubiertos IS NOT NULL) 
		and (seguro is not null  AND Rtrim(seguro)<> '' ) 
	union all 
	SELECT 
		SerieAdmision, 
		'Total Neto' AS Descrip, 
		SUM(ValorDesc) - ISNULL(Descuento, 0) AS COPA 
	FROM 
		(
			SELECT 
				CAR.SerieAdmision, 
				CASE WHEN (
					PorcentajeDescuento > 0 
					and idTipoDescuento > 0
				) THEN Valor ELSE CASE WHEN PorcentajeDescuento > 0 THEN CASE WHEN C.Descuento = 'S' THEN Valor - Valor * A.PorcentajeDescuento / 100 ELSE Car.Valor END ELSE Car.Valor END end AS ValorDesc, 
				A.Descuento, 
				A.PorcentajeDescuento 
			FROM 
				Cargos AS CAR WITH (nolock) 
				INNER JOIN Admisiones AS A WITH (nolock) ON CAR.Empresa = A.Empresa 
				AND CAR.SerieAdmision = A.Serie 
				AND CAR.Admision = A.Codigo 
				INNER JOIN Categorias AS C WITH (nolock) ON CAR.Empresa = C.Empresa 
				AND CAR.Categoria = C.Codigo 
			WHERE 
				(CAR.Empresa = @i_EmpresaUnificada) 
				AND (CAR.SerieAdmision = @i_Serie) 
				AND (CAR.Admision = @i_CodigoAdmision) 
				AND (CAR.Categoria NOT IN ('30', '98', '99') ) 
				AND (A.Seguro IS NULL OR RTrim(A.Seguro)= '' )
		) AS netoGroup 
	Group BY SerieAdmision, Descuento 
)
GO
/********************Fin Creacion de Funciones********************************/
/********************Creacion de Paquetes********************************/
-->>
USE [HOSPITAL]
GO

/****** Object:  StoredProcedure [dbo].[spEstadosDeCuentaOpciones]    Script Date: 06/07/2023 14:24:50 p.m. ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


-- ==========================================================
-- Author:  Javier Castillo
-- Create date: 2023.06.01
-- Description:	Querys para reportes de estados de cuenta
-- ==========================================================
CREATE PROCEDURE [dbo].[spEstadosDeCuentaOpciones]
  @i_Opcion				varchar(10),
	@i_SubOpcion    varchar(30),
	@i_EmpresaUnificada varchar(3),
	@i_EmpresaReal varchar(3),
	@i_Sucursal varchar(3)=null,
	@i_Serie char(1) = null,
	@i_CodigoAdmision int = null,
	@i_TipoOrden char(3) = null,
	@i_CodigoOrden int = null,
	@i_IncluirAjeno char(1) = null,
	@i_Categoria char(2) = null,
	@i_ConExtra char(1) = 'S'
AS BEGIN
BEGIN TRY
	DECLARE  @ErrRaise varchar(99) = '',
				   @ErrRaiseNum int = 0,					 
					 @w_ConValor bit = 1				

	IF @i_Opcion = 'CONSULTA'
	BEGIN
		IF @i_SubOpcion = 'CABECERA'
		BEGIN
				SELECT 		
					A.Serie + '-' + CAST(A.Codigo AS varchar) AS Admision, 			
					A.Seguro as Poliza, 
					RTRIM(Pac.Nombre) + ' ' + RTRIM(Pac.Apellido) + CASE WHEN isnull(Pac.ApellidoCasada, '') = '' THEN '' ELSE Rtrim(Pac.ApellidoCasada) END AS Paciente, 			
					RTRIM(EH.Nombre) AS Hospital, 
					A.UltimaHab Habitacion, 
					Entrada, 
					Salida, 
					isnull(aseg.Nombre,'') AS Aseguradora,
					ComentariosSemaforoE AS Observacion,
					--Revisar admision tipo Q con descuento
					CASE WHEN ( A.TipoDescuento IN ('S','M','D','E','P','F','A','B','R') and ISNULL(A.PorcentajeDescuento,0) > 0) 
							 THEN  'S'  ELSE  'N' END as AplicaDescuento,
					A.PorcentajeDescuento 
				FROM 
					HOSPITAL.dbo.Admisiones AS A WITH (nolock) LEFT JOIN 
					HOSPITAL.dbo.Seguros seg WITH (nolock)  ON (A.Empresa = seg.Empresa and A.Seguro = seg.Codigo) LEFT JOIN 
					HOSPITAL.dbo.Aseguradoras aseg WITH (nolock)  ON  (aseg.Asegura = seg.Asegura AND aseg.Empresa = seg.Empresa )	LEFT JOIN 			
					NOMINADB.dbo.EmpHospital AS EH WITH (nolock) ON (EH.Empresa = a.Empresa ) LEFT JOIN 
					HOSPITAL.dbo.Pacientes AS Pac WITH (nolock) ON (A.Empresa = Pac.Empresa AND A.Paciente = Pac.Codigo)					
				WHERE (A.Empresa = @i_EmpresaUnificada) 			
					AND (A.Serie = @i_Serie) 
					AND (A.Codigo = @i_CodigoAdmision)
					AND (EH.Codigo = @i_Sucursal) 						
		END
		ELSE IF @i_SubOpcion = 'CARGOS_ORDEN'
		BEGIN
			IF @i_TipoOrden IS NULL OR @i_CodigoOrden IS NULL
			BEGIN
				SELECT 0 AS codigo, 'El tipo o el código de la orden  esta vacio.' AS descripcion, 1 AS tipo_error
				RETURN
			END
			IF Substring(@i_TipoOrden,1,2) in ('FA', 'OP', 'OR', 'FC') 
			BEGIN
				SET @w_ConValor = 0;
			END

			SELECT c.SerieAdmision, 
						 c.Admision, 
						 a.Habitacion, 
						 a.Paciente, 
						 rtrim(e.nombre) as NombreSucursal,
						 concat(rtrim(h.nombre),' ',rtrim(h.apellido)) as NombrePaciente, 
						 isnull(h.Email,'') Email, 
						 c.TipoOrden, 
						 c.Orden,
						 c.Linea, 
						 c.Categoria, 
						 c.Producto, 
						 p.nombre as Descripcion, 
						 ISNULL(pp.UnidadMedida,'') UnidadMedida, 
						 c.Cantidad, 
						 c.PrecioUnitario, 
						 CASE WHEN @w_ConValor = 0 THEN 0 ELSE c.Valor END AS Valor, 
						 h.IdCliente, 
						 concat(rtrim(aj.Nombre),' ',rtrim(aj.Apellido)) as NombreMedico, 
						 ISNULL(aj.Email1,ISNULL(aj.Email2,'')) EmailMedico,
						 o.Usuario, 
						 CONVERT(VARCHAR,o.FECHA,103)+' '+LEFT(CONVERT(VARCHAR,o.FECHA,108),5) Fecha,
						 o.Inhabiles
			 FROM Cargos c (nolock)	Inner join Admisiones a (nolock) on (a.empresa = c.empresa and a.serie = c.serieadmision and a.codigo = c.admision)
						left join Inventario..Productos  p (nolock) on (p.empresa = c.empresa and p.codigo = c.producto)
						left join Inventario..ProductosPrecios pp (nolock) on (p.empresa = pp.empresa and p.codigo = pp.producto and pp.nivel = a.NivelPrecios)
						Left join Pacientes h (nolock) on (h.empresa = a.empresa and h.codigo = a.paciente)
						left join NominaDb..EmpHospital e (nolock) on (e.empresa = c.empresa and e.codigobase = right(concat('00',c.Sucursal),2))
						left join Ordenes o (nolock) on (o.empresa = c.empresa and o.Tipo = c.TipoOrden and o.Codigo = c.Orden )
						left join Ajenos  aj (nolock) on (aj.empresa = o.empresa and aj.Codigo = o.Medico)
			WHERE c.Empresa = @i_EmpresaUnificada
						and c.TipoOrden = @i_TipoOrden
						and c.Orden = @i_CodigoOrden
			ORDER BY Linea
		END
		ELSE IF @i_SubOpcion = 'CARGOS_ADMISION'
		BEGIN
			IF @i_Serie IS NULL OR @i_CodigoAdmision IS NULL
			BEGIN
				SELECT 0 AS codigo, 'La serie o el código de la admisión esta vacio.' AS descripcion, 1 AS tipo_error
				RETURN
			END
			SELECT --InicioCabecera--
						c.SerieAdmision, c.Admision, h.IdCliente,
						a.Paciente, concat(rtrim(h.nombre),' ',rtrim(h.apellido)) as NombrePaciente,
			      a.Habitacion,
						concat(rtrim(aj.Nombre),' ',rtrim(aj.Apellido)) as NombreMedico,
						ISNULL(CONVERT(VARCHAR,a.Entrada,103)+' '+LEFT(CONVERT(VARCHAR,a.Entrada,108),5),'') FechaEntrada, 
						ISNULL(CONVERT(VARCHAR,a.Salida,103)+' '+LEFT(CONVERT(VARCHAR,a.Salida,108),5),'') FechaSalida, 
						a.paquete idPaquete,
						ISNULL(pq.nombre,'') Paquete,
						h.Telefonos,						
						--InicioDetalle--
						c.Categoria, ca.Nombre NombreCategoria, Concat(c.TipoOrden,' ',convert(varchar,c.Orden)) CodigoOrden,
						c.Linea, LEFT(CONVERT(VARCHAR,c.FECHA,103),5) Fecha, c.Cantidad, c.Producto, 
						p.nombre as Descripcion, c.UnidadMedida,
						c.PrecioUnitario, c.Valor , 
						--Fin InicioDetalle--	
						CASE WHEN isnull(ca.Descuento,'N') = 'S' or isnull(ca.DescuentoBi,'N') = 'S' 
						THEN 'S' ELSE 'N' END AS DescuentoCategoria, 
						CASE WHEN ( A.TipoDescuento IN ('S','M','D','E','P','F','A','B','R') and ISNULL(A.PorcentajeDescuento,0) > 0) 
							 THEN  'S'  ELSE  'N' END as AplicaDescuento,
						A.NivelPrecios,
						A.PorcentajeDescuento,
						A.TipoDescuento,
						A.Status,
						IsNull(A.Preoperatorio,'N') Preoperatorio,
						IsNull(A.Descuento,0) Descuento,
						td.Descripcion descripcionTipoDescuento
			 FROM Admisiones a (nolock) Inner join  Cargos c (nolock)	on (a.empresa = c.empresa and a.serie = c.serieadmision and a.codigo = c.admision)
						Left join Inventario..Productos  p (nolock) on (p.empresa = c.empresa and p.codigo = c.producto)					
					  Left join Categorias ca (nolock) on (ca.empresa = c.empresa and ca.Codigo = c.Categoria)		
						Left join Pacientes h (nolock) on (h.empresa = a.empresa and h.codigo = a.paciente)
						Left join Ajenos  aj (nolock) on (aj.empresa = a.empresa and aj.Codigo = a.Medico)										
						Left join Paquetes  pq (nolock) on (a.empresa = pq.empresa and a.paquete = pq.codigo)		
						Left join TipoDescuento td (nolock) on (a.IdTipoDescuento = td.IdDescuento)
			WHERE c.Empresa = @i_EmpresaUnificada
						and c.serieadmision = @i_Serie
						and c.admision = @i_CodigoAdmision						  
						and c.Cantidad > 0
						and ((((a.TipoDescuento = 'S') or (ISNULL(a.Seguro,'') <> '')) and c.categoria <> '30') OR a.TipoDescuento <> 'S')
						and (ISNULL(@i_Categoria,'') = '' OR c.categoria = @i_Categoria)
						and ( (ISNULL(@i_IncluirAjeno,'0') = '1' AND ISNULL(a.paquete,0) = 0)  OR  c.Categoria < '98')												
		  ORDER BY CA.CODIGO, C.FECHA
		END
		ELSE IF @i_SubOpcion = 'ESTADO_CARGOS_CLIENTE_DETALLE'
		BEGIN
			IF @i_Serie IS NULL OR @i_CodigoAdmision IS NULL
			BEGIN
				SELECT 0 AS codigo, 'La serie o el código de la admisión esta vacio.' AS descripcion, 1 AS tipo_error
				RETURN
			END

			SELECT Area, Valor, ValorDesc, Descuento, Admision, Poliza, Paciente,
						 Hospital, Habitacion, Entrada, Salida, Aseguradora, Categoria,TipoOrden, 
						 Orden, Producto, Fecha, UnidadMedida, Cantidad, Nombre
			  FROM fnEstadoCuentaClienteDetalle(@i_EmpresaUnificada,@i_Sucursal,@i_Serie,@i_CodigoAdmision,@i_ConExtra,@w_ConValor)
			 ORDER BY Ordenado, Fecha

		END
		ELSE IF @i_SubOpcion = 'ESTADO_CARGOS_FACTURA'
		BEGIN
			IF @i_Serie IS NULL OR @i_CodigoAdmision IS NULL
			BEGIN
				SELECT 0 AS codigo, 'La serie o el código de la admisión esta vacio.' AS descripcion, 1 AS tipo_error
				RETURN
			END

			SELECT SerieAdmision, Hospital, Serie, Codigo, Fecha, TipoFecha, TipoFactura,
             Total, Tipo, Expr1, Expr2, Saldo
			  FROM fnEstadoCuentaFacturas(@i_EmpresaUnificada,@i_Serie,@i_CodigoAdmision)
			 ORDER BY Tipo, Expr1, Expr2, TipoFecha

		END
		ELSE IF @i_SubOpcion = 'ESTADO_CARGOS_ADMISION'
		BEGIN
			IF @i_Serie IS NULL OR @i_CodigoAdmision IS NULL
			BEGIN
				SELECT 0 AS codigo, 'La serie o el código de la admisión esta vacio.' AS descripcion, 1 AS tipo_error
				RETURN
			END

			SELECT Area, Valor, ValorDesc, Descuento, Admision, Poliza, Paciente,
						 Hospital, Habitacion, Entrada, Salida, Aseguradora, DescuTotal, DescArb
			  FROM fnEstadoCuentaCargos(@i_EmpresaUnificada,@i_Sucursal,@i_Serie,@i_CodigoAdmision,@i_ConExtra)
			 ORDER BY Ordenado

		END
		ELSE IF @i_SubOpcion = 'ESTADO_SALDOS_ADMISION'
		BEGIN
			IF @i_Serie IS NULL OR @i_CodigoAdmision IS NULL
			BEGIN
				SELECT 0 AS codigo, 'La serie o el código de la admisión esta vacio.' AS descripcion, 1 AS tipo_error
				RETURN
			END
			
			SELECT SerieAdmision, Descrip, COPA 
				FROM fnEstadoCuentaSaldos(@i_EmpresaUnificada,@i_Serie,@i_CodigoAdmision)

		END
		ELSE IF @i_SubOpcion = 'ESTADO_ABONOS_ADMISION'
		BEGIN
			IF @i_Serie IS NULL OR @i_CodigoAdmision IS NULL
			BEGIN
				SELECT 0 AS codigo, 'La serie o el código de la admisión esta vacio.' AS descripcion, 1 AS tipo_error
				RETURN
			END

			SELECT Empresa,Recibo,SumMonto,Codigo,Fecha,
						 FormaPago,BancoTarjeta, MontoTotal , NombreCorto
			 FROM fnEstadoCuentaAbonos(@i_EmpresaUnificada,@i_Serie,@i_CodigoAdmision)

		END
		ELSE
		BEGIN
			SELECT 0 AS codigo, CONCAT('No se ha encontrado la SubOpcion ',@i_SubOpcion) AS descripcion, 1 AS tipo_error;
		END
	END
	ELSE
	BEGIN 
		SELECT 0 AS codigo, CONCAT('No se ha encontrado la Opcion ',@i_Opcion) AS descripcion, 1 AS tipo_error;
	END
END TRY
  BEGIN CATCH
    ROLLBACK TRANSACTION
    SET @ErrRaise = ERROR_MESSAGE()
    SET @ErrRaiseNum = ERROR_NUMBER()
    --Select ERROR_NUMBER ( )  as NoError, ERROR_MESSAGE() as textoerror
    RAISERROR (@ErrRaise, 16, @ErrRaiseNum)
  END CATCH
END

GO
-->>
USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[sp_HisEstadoCta_General]    Script Date: 05/07/2023 11:00:25 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [dbo].[sp_HisEstadoCta_General](
	@i_EmpresaU		varchar(3)	= 'MED',	-- empresa unificada, mundo verde
	@i_EmpresaReal	varchar(3)	= NULL,		-- empresa real, mundo naranja
	@i_SerieAdm		varchar(1),				-- serie de la admision
	@i_Admision		Int,					-- numero de la admision
	@i_TipoFnd		smallint	= 1,		-- tipo de consulta
	@i_ShowNotas	varchar(1)  = '0'		-- mostrar notas en el encabezado: '1' = sí; cualquier otro valor = No
)

/*

Nomenclatura @TipoFnd:
1: Encabezado del reporte:
		Contiene la información de:	
		Admisión, Paciente, Médico, Afiliado, Notas, Observaciones,
		Información de la cuenta con seguro (que va en el pie del reporte).
2: Facturas y notas de la admision
3: Cargos no facturados
4: Recibos de anticipos y caqncelación
5: Devolución de anticipos
6: Liquidaciones SaSi P3 y Percapitados
7: Cuenta Ajena
*/

AS
BEGIN
/******************************************************
Fecha:	2023.06.02
Autor:	Vinicio Cahuex C.
Desc.:	devuelve información de acuerdo al tipo de busqueda:
		1 = encabezado del estado de cuenta: 
			admision, paquete, paciente, medico, seguro
		2 = recibos dl paciente, pagos y anticipos
Update:	2023.07.04
******************************************************/
	SET NOCOUNT ON

	BEGIN TRY

		DECLARE
		@FlagErr		int = 0,
		@w_ErrRaiseNum 	Int	= 0,
		@w_ErrRaise		varchar(max)= ''


		--valida que exista la admision
		Select @FlagErr = count(*)
		From Admisiones
		Where Empresa  = @i_EmpresaU 
			and Serie  = @i_SerieAdm 
			and Codigo = @i_Admision

		If @FlagErr <= 0 
		begin
			Select 0 AS codigo, 'No existe Admisión. Verifique.' AS descripcion, -1 AS tipo_error
			Return
		end--endIf

		--Encabezado del reporte (también información de la cuenta con seguro)
		if @i_TipoFnd = 1
		begin
			Select a.Serie, a.Codigo, a.Status, a.Habitacion, a.Interno, 
				concat(convert(varchar(10), a.Salida,  103), ' ', convert(varchar(5), a.Salida,  108)) as Alta,
				concat(convert(varchar(10), a.Entrada, 103), ' ', convert(varchar(5), a.Entrada, 108)) as Entrada,
				concat(rtrim(m.Nombre),' ',rtrim(m.Apellido)) as NombreMedico, a.PorcentajeDescuento, a.Descuento, 
				concat(rtrim(c.Nombre),' ',rtrim(c.Apellido)) as NomPaciente,  a.Socio, a.NivelPrecios, a.Preoperatorio, 
				s.Codigo as CodigoSeguro, s.Nombre as NombreSeguro, s.FacPacienteInt, s.FacPacienteExt, s.FacPacienteEme, 
				s.CoaseguroInt, s.CoaseguroExt, s.CoaseguroEme, s.CopagoEme, s.CopagoInt, s.CopagoExt, s.CuponValor, 
				a.Usuario, a.IdAfiliado, a.CopagoACobrar, a.CoaseguroACobrar, a.Clasificacion, a.TipoDescuento, 
				a.Paciente, p.Nombre as NombrePaquete, p.Valor as ValorPaquete,  p.ValorHospital as PaqueteHospital,
				Case TipoDescuento when 'Q' then isnull(a.paquete,0) else 0 End as Paquete, c.IdCliente, c.Telefonos,
				Case @i_ShowNotas when '1' 
				then 
					(Select Observacion from AdmisionesObservaciones o 
							Where o.Empresa  = @i_EmpresaU and o.Serie  = @i_SerieAdm and o.Codigo = @i_Admision)
				else '' end as Notas, ComentariosSemaforoE as Observacion, r.IdPlanXcontrato
			From Admisiones a		(nolock)
			Left Join Ajenos m		(nolock) on (m.empresa = a.Empresa and m.codigo = a.medico)
			Left Join Seguros s		(nolock) on (s.empresa = a.Empresa and s.codigo = a.seguro)
			Left Join Paquetes p	(nolock) on (p.Empresa = a.empresa and p.codigo = a.paquete)
			Left Join Pacientes c	(nolock) on (c.Empresa = a.empresa and c.codigo = a.paciente)
			left join PlanesMedicos..PlanesXcontrato r (nolock) on (r.idempresa = a.empresa and r.idpoliza = a.seguro)
			Where a.Empresa  = @i_EmpresaU 
				and a.Serie  = @i_SerieAdm 
				and a.Codigo = @i_Admision

			Return
		end; --tipo 1

		--Facturas
		if @i_TipoFnd = 2
		begin
		Select Distinct 
			IsNull(e.SerieFace,IsNull(c.pendiente, f.Serie)) as SerieE,
			IsNull(e.numeroface,IsNull(c.factura, f.codigo)) as NumeroE, 
			convert(varchar(12), f.fecha, 103) as Fecha,
			f.Saldo, (f.Total - f.Descuento) as Total
		From Facturas f (nolock)
		left join  FacturasFace e (nolock) On (e.empresa = f.empresa and e.serie = f.serie and e.codigo = f.codigo and e.tipoDoc = 1)
		left join (Select distinct 'Pendiente' as Pendiente, 0 as Factura, Empresa, SerieFactura as Serie, Factura as Codigo
			From FacEContingencias (nolock) 
			Where Generado = 0
			and Operacion = 1
			) as c On (c.empresa = f.empresa and c.serie = f.serie and c.codigo = f.codigo)
		Where f.empresa = @i_EmpresaReal
		and f.serieadmision = @i_SerieAdm
		and f.Admision = @i_Admision
		and f.Status = 'P'
		UNION
		Select 
			isnull(e.serieface, isnull(c.pendiente, l.seriefactura)) as SerieE,
			isnull(e.numeroface, isnull(c.factura,f.codigo)) as NumeroE,
			convert(varchar(12), Registro, 103) as Fecha,
			SaldoAdmision, (TotalAdmision - CopagoMonto) as Total

		From facturasloteadmisiones as l (nolock) 
		Inner join Facturas f (nolock) on (f.empresa = l.empresafactura and f.serie = l.seriefactura and f.codigo = l.factura and f.Status = 'P')
		left join  FacturasFace e (nolock) on (e.empresa = l.empresafactura and e.serie = l.seriefactura and e.codigo = l.factura and e.tipoDoc = 1)
		left join (Select distinct 'Pendiente' as Pendiente, 0 as Factura, Empresa, SerieFactura as Serie, Factura as Codigo
			From FacEContingencias (nolock) 
			Where Generado = 0
			and Operacion = 1
			) as c On (c.empresa = l.empresafactura and c.serie = l.seriefactura and c.codigo = l.factura)
		Where l.EmpresaFactura = @i_EmpresaReal
		and l.serieadmision = @i_SerieAdm
		and l.Admision = @i_Admision
		UNION
		Select 
			isnull(e.serieface, n.serienota) as SerieE, 
			isnull(e.Numeroface, n.codigo) as NumeroE, 
			convert(varchar(12), n.fecha, 103) as Fecha,
			NULL as Saldo, -(isnull(servicios,0) + isnull(ventas,0)) as Total
		From NotasCredito n
		left join FacturasFace e (nolock) on (e.empresa = e.empresa and e.serie = n.serienota and e.codigo = n.codigo and tipodoc = 2)
		Where concat(n.Empresa, SerieFactura, Factura) in (
			Select concat(Empresa, Serie, Codigo) as IdKey
			From Facturas
			Where SerieAdmision = @i_SerieAdm
			and Admision = @i_Admision
			)
		and n.Status = 'V'

			Return
		end; --tipo 2

		--Cargos no facturados, por categoria 
		if @i_TipoFnd = 3
		begin
			Select rtrim(k.nombre) as Nombre, 
				case k.Descuento when 'S'
				then
					concat(isnull(a.PorcentajeDescuento,0),' % Desc') 
				else
				  '0.00 % Desc'
				end
				as PDescuento,
				case k.Descuento when 'S'
				then
				  round(sum(c.Valor) * isnull(a.PorcentajeDescuento,0)/100,2)
				else
				  0
				end
				as MontoDescuento,
				round(sum(c.Valor),2) as Total

			From Cargos c (nolock)
			Inner Join Categorias k (nolock) On (k.empresa = c.empresa and k.codigo = c.categoria)
			Inner join Admisiones a (nolock) On (a.empresa = c.empresa and a.codigo = c.admision and a.serie = c.serieadmision)
			Where c.Empresa = @i_EmpresaU
			and c.SerieAdmision = @i_SerieAdm
			and c.Admision = @i_Admision
			and c.Cantidad >0
			and c.FacRec is NULL
			and c.SerieFacRec is NULL
			and c.Categoria NOT In (
				Select CategoriaAjenos AS CatDefault
				From Defaults (nolock)
				Where Empresa = @i_EmpresaU
				UNION
				Select CategoriaAjenosExtra
				From Defaults (nolock)
				Where Empresa = @i_EmpresaU
				)
			Group by k.nombre, k.Descuento, a.PorcentajeDescuento

			Return
		end --Tipo = 3

		--Recibos
		if @i_TipoFnd = 4
		begin
			Select Distinct  r.Tipo, case r.Tipo when 'A' then 'Ant.' else '' end as TipoR, 
				r.Serie, concat(r.Codigo, '') as Codigo,
				convert(varchar(12), r.fecha, 103) as Fecha,
			p.Status, p.Forma, p.BancoTarjeta, p.Monto
			From Recibos r  (nolock)
			Inner Join RecibosPagos p  (nolock) on (p.empresa = r.empresa and p.serierecibo = r.serie and p.recibo = r.Codigo)
			Where r.Empresa		= @i_EmpresaReal
			and r.SerieAdmision = @i_SerieAdm
			and r.Admision		= @i_Admision
			and r.Status <> 'A'
			UNION
			Select 'C' as Tipo, '' as TipoR, SerieRecibo, concat(Recibo, '') as Codigo,
				convert(varchar(12), Registro, 103) as Fecha, 'P' as Status, 
				'T' as Forma, NULL, MontoAplicado
			From RecibosLoteAdmisiones
			Where EmpresaRecibo = @i_EmpresaReal
			and SerieAdmision	= @i_SerieAdm
			and Admision		= @i_Admision
			UNION
			Select 'PA' as Tipo, 'PA' as TipoR, concat('No. ', pl.codigo) as Serie, 
			'Liquidación del' as Codigo,
			convert(varchar(12), FechaAplicaDocumento, 103) as Fecha, 
			'' as Status, '' AS Forma, pl.TipoPartida AS BancoTarjeta, pl.Haber AS Monto
			From   CONTADB..PartidasLineas AS pl (NoLock) 
			Inner Join CONTADB..Partidas AS p (nolock) ON (p.empresa = pl.empresa and p.periodo = pl.periodo and p.codigo = pl.codigo)
			Where pl.Empresa	= @i_EmpresaReal 
			and pl.SerieAdmision= @i_SerieAdm
			and pl.Admision		= @i_Admision 
			and pl.TipoDocumento= 'F'
			and pl.TipoPartida IS NOT NULL

			Return
		end --Tipo = 4

		--Devoluciones  (por anticipos)
		if @i_TipoFnd = 5
		begin
			Select concat('Devolución No.: ',codigo,' del ', Convert(varchar(12), fecha, 103), '.  Ch.: ', cuentabanco,' ', movimiento) as Devolucion, Valor
			From AdmisionesDevoluciones (nolock)
			Where Empresa  = @i_EmpresaReal 
			and SerieAdmision  = @i_SerieAdm 
			and Admision = @i_Admision
			UNION
			Select Case TipoPartida
				when 'LDA' then
					concat('PA No.: ',codigo,' Liquidación del ', Convert(varchar(12), fechaaplicadocumento, 103)) 
				when 'DNG' then
					concat('PA No.: ',codigo,' Dev. No gestionada del ', Convert(varchar(12), fechaaplicadocumento, 103)) 
				else
					concat('Devolución No.: ',codigo,' del ', Convert(varchar(12), fechaaplicadocumento, 103), '.  Ch.: ', TipoPartida) 
			End as Devolucion, Debe as Valor
			From CONTADB..PartidasLineas (NoLock)
			Where SerieAdmision = @i_SerieAdm 
			and Admision	= @i_Admision
			and TipoPartida is not null
			and TipoDocumento = 'R'
			and Debe > 0
			and Empresa = @i_EmpresaReal
			Return
		end --Tipo = 5

		--Liquiaciones SaSi P3 y Percapitados
		if @i_TipoFnd = 6
		begin
			Select concat(SerieFactura,' - ', Factura) as Documento, 
				(TotalAdmision - CopagoMonto) as Siniestro, 
				convert(varchar(10), ss.fecha, 103) as Fecha
			From SiniestroLoteAdmisiones fl (nolock) 
			Inner join SiniestroSaSi ss (nolock) on (ss.empresa = fl.empresafactura and ss.serie = fl.seriefactura and ss.codigo = fl.factura)
			where fl.EmpresaFactura = @i_EmpresaReal
			and fl.SerieAdmision = @i_SerieAdm
			and fl.Admision = @i_Admision

			Return
		end --Tipo = 6

		--Cuenta ajena
		if @i_TipoFnd = 7
		begin
			Select c.Ajeno, concat('Cuenta Ajena / ',rtrim(m.nombre),' ', rtrim(m.apellido)) as Medico, sum(Valor) as Total
			From Cargos c (nolock)
			left join Ajenos m (nolock) on (m.empresa = c.empresa and m.codigo = c.ajeno)
			Where c.Empresa = @i_EmpresaU
			and c.SerieAdmision = @i_SerieAdm
			and c.Admision = @i_Admision
			and c.Categoria In (
				Select CategoriaAjenos AS CatDefault
				From Defaults (nolock)
				Where Empresa = @i_EmpresaU
				UNION
				Select CategoriaAjenosExtra
				From Defaults (nolock)
				Where Empresa = @i_EmpresaU
				) 
			Group by c.Ajeno, m.nombre, m.apellido

			Return
		end --Tipo = 7

	END TRY
	BEGIN CATCH		
		--revierte cambios y envia alerta
		Set @w_ErrRaise = ERROR_MESSAGE()
		Set @w_ErrRaiseNum = ERROR_STATE()
		RAISERROR(@w_ErrRaise,16,@w_ErrRaiseNum)
	END CATCH
END
GO
-->>
/********************Fin Creacion de Paquetes********************************/
/********************Inicio Creacion de Permisos********************************/
USE HOSPITAL
GO
GRANT EXECUTE ON spEstadosDeCuentaOpciones TO USRREPORTE 
GO

USE HOSPITAL
GO
GRANT EXECUTE ON sp_HisEstadoCta_General TO USRREPORTE 
GO

USE CONTADB
GO
GRANT SELECT ON Partidas TO USRREPORTE
GO

USE CONTADB
GO
GRANT SELECT ON PartidasLineas TO USRREPORTE
GO
/********************Fin Creacion de Permisos********************************/
