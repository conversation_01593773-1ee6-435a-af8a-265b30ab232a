<template>
<div class="mantenimiento-seguros-container">
    <DxDataGrid ref="gridSeguros" v-bind="GridConfiguration" :columns="GridColumns" :editing="GridEditing" :toolbar="gridToolbar" :data-source="seguros" @row-inserting="onRowInserting" @row-updating="onRowUpdating" @init-new-row="onInitNewRow" @editor-preparing = "onEditorPreparing" @editing-start="onEditingStart">
        <template #opcionesTemplate>
            <ExpedienteGridToolBar :visible="true" :showItems="['refresh', 'add']" :pdfExportItems="[]" @refresh="Cargar" @add="onAdd"/>
        </template>
    </DxDataGrid>
</div>
</template>

<script>
import 'devextreme-vue/lookup'
import {
    srtLenRule,
    smallMoneyRule,
    gridConfiguration,
    gridToolbar,
    buildFilterExpresion,
    setUpperCaseConfig,
} from './data.js'
import ExpedienteGridToolBar from '../EXPEDIENTE/ExpedienteGridToolBar.vue'
import bitacora from '@/components/sermesa/funciones/SMBitacora'
export default {
    name: 'MantenimientoSeguros',
    components: {
        ExpedienteGridToolBar
    },
    props: {
        Aseguradora: {
            type: Object,
        },
    },
    data() {
        return {
            seguros: null,
            gridToolbar,
            GridConfiguration: gridConfiguration,
            GridColumns: [{
                    dataField: 'Empresa',
                    visible: false,
                },
                {
                    dataField: 'Asegura',
                    visible: false,
                },
                {
                    dataField: 'Codigo',
                    validationRules: [{
                            type: 'required'
                        },
                        srtLenRule('Codigo', 0, 3),
                        {
                            type: 'async',
                            reevaluate: false,
                            message: 'El código de seguro ya fue registrado anteriomente',
                            validationCallback: (e) => {
                                
                                return new Promise((resolve, reject) => {
                                    if(this.editingState)
                                        resolve()

                                    if (this.seguros.find(x=> x.Codigo == e.value))
                                        reject('El código de aseguradora ya fue registrado anteriormente')
                                    else {
                                        this.axios.post("/app/v1_cobros/CatalogoSeguros", {
                                            Codigo: e.value
                                        }).then(resp => {
                                            resp.data.json.length? reject('El código de aseguradora ya fue registrado anteriormente'): resolve()
                                        }).catch(err=>{
                                            reject(err)
                                        })
                                    }
                                })

                            }
                        },
                    ],
                    width: 80,
                },
                {
                    dataField: 'Nombre',
                    validationRules: [srtLenRule('Nombre', 2, 60)],
                    width: 'auto',
                    minWidth: 350,
                    calculateFilterExpression: buildFilterExpresion('Nombre')
                },
                {
                    caption: 'Internos',
                    dataField: 'FacPacienteInt',
                    datatype: 'number',
                    validationRules: [{
                        type: 'required'
                    }, smallMoneyRule, ],
                    visible: false,
                },
                {
                    caption: 'Emergencia',
                    dataField: 'FacPacienteEme',
                    dataType: 'number',
                    validationRules: [{
                        type: 'required'
                    }, smallMoneyRule, ],
                    visible: false,
                },
                {
                    caption: 'Externos',
                    dataField: 'FacPacienteExt',
                    dataType: 'number',
                    validationRules: [{
                        type: 'required'
                    }, smallMoneyRule, ],
                    visible: false,
                },
                {
                    caption: 'Internos',
                    dataField: 'CoaseguroInt',
                    dataType: 'number',
                    validationRules: [smallMoneyRule],
                    visible: false,
                },
                {
                    caption: 'Externos',
                    dataField: 'CoaseguroExt',
                    dataType: 'number',
                    validationRules: [{
                        type: 'required'
                    }, smallMoneyRule],
                    visible: false,
                },
                {
                    caption: 'Emergencia',
                    dataField: 'CoaseguroEme',
                    dataType: 'number',
                    validationRules: [smallMoneyRule],
                    visible: false,
                },
                {
                    caption: 'Internos',
                    dataField: 'CopagoInt',
                    dataType: 'number',
                    validationRules: [smallMoneyRule],
                    visible: false,
                },
                {
                    caption: 'Emergencia',
                    dataField: 'CopagoEme',
                    dataType: 'number',
                    validationRules: [smallMoneyRule],
                    visible: false,
                },
                {
                    caption: 'Externos',
                    dataField: 'CopagoExt',
                    dataType: 'number',
                    validationRules: [{
                        type: 'required'
                    }, smallMoneyRule],
                    visible: false,
                },
                {
                    caption: 'Facturar al Paciente y Seguro',
                    dataField: 'DobleFactura',
                    dataType: 'boolean',
                    visible: false,
                },
                {
                    dataField: 'CuponValor',
                    dataType: 'number',
                    validationRules: [smallMoneyRule],
                    visible: false,
                },
                {
                    dataField: 'Activa',
                    dataType: 'boolean',
                    width: 80,
                },
                {
                    dataField: 'UnidadPermisoRecalculo',
                    dataType: 'string',
                    validationRules: [srtLenRule('UnidadPermisoRecalculo', 0, 6)],
                    visible: false,
                },
                {
                    dataField: 'PermitirImprimirInformes',
                    caption: 'Imprimir Informes',
                    dataType: 'boolean',
                    width: 80,
                },
                {
                    caption: 'Plan Directo',
                    dataField: 'PolizaPlanDirecto',
                    dataType: 'boolean',
                    width: 80,
                },
                {
                    dataField: 'UnidadExtensionCredito',
                    dataType: 'string',
                    validationRules: [srtLenRule('UnidadExtensionCredito', 0, 6)],
                    visible: false,
                },
                {
                    dataField: 'NOPagaCopago',
                    visible: false,
                },
                { //agregado para mostrar cmapos sin negaciones el la descripción
                    dataField: 'PagaCopago',
                    dataType: 'boolean',
                    visible: false,
                },
                {
                    dataField: 'PolizaConPaquete',
                    dataType: 'boolean',
                    visible: false,
                },
                {
                    dataField: 'FacturacionPaquete',
                    dataType: 'boolean',
                    visible: false,
                },

            ],
            GridEditing: {
                ...gridConfiguration.editing,
                form: {
                    colCount: 3,
                    items: [{
                            itemType: 'group',
                            caption: 'Datos de Póliza',
                            colSpan: 3,
                            colCount: 2,
                            items: [{
                                    itemType: 'simple',
                                    name: 'Codigo',
                                    colSpan: 1,
                                },
                                {
                                    itemType: 'simple',
                                    name: 'Nombre',
                                    colSpan: 1,
                                },
                                {
                                    itemType: 'simple',
                                    name: 'Activa',
                                    colSpan: 1,
                                },
                                {
                                    itemType: 'simple',
                                    name: 'PolizaPlanDirecto',
                                    colSpan: 1,
                                },
                                {
                                    itemType: 'simple',
                                    name: 'DobleFactura',
                                    colSpan: 1,
                                },
                                {
                                    itemType: 'simple',
                                    name: 'PagaCopago',
                                    colSpan: 1,
                                },
                                {
                                    itemType: 'simple',
                                    name: 'PermitirImprimirInformes',
                                    colSpan: 1,
                                },
                            ],
                        },

                        {
                            itemType: 'group',
                            caption: 'Coaseguros',
                            items: [{
                                    itemType: 'simple',
                                    name: 'CoaseguroInt',
                                },
                                {
                                    itemType: 'simple',
                                    name: 'CoaseguroExt',
                                },
                                {
                                    itemType: 'simple',
                                    name: 'CoaseguroEme',
                                },
                            ],
                        },
                        {
                            itemType: 'group',
                            caption: 'Copagos',
                            items: [{
                                    itemType: 'simple',
                                    dataField: 'CopagoInt',
                                    caption: 'Internos',
                                },
                                {
                                    itemType: 'simple',
                                    dataField: 'CopagoEme',
                                    caption: 'Emergancia',
                                },
                                {
                                    itemType: 'simple',
                                    dataField: 'CopagoExt',
                                    caption: 'Externos',
                                },
                            ],
                        },
                        {
                            itemType: 'group',
                            caption: 'Porcentajes para la Factura del Paciente',
                            items: [{
                                    itemType: 'simple',
                                    name: 'FacPacienteInt',
                                },
                                {
                                    itemType: 'simple',
                                    name: 'FacPacienteEme',
                                },
                                {
                                    itemType: 'simple',
                                    name: 'FacPacienteExt',
                                },
                            ],
                        },
                    ]
                },
            },
            editingState: false,
            objBitacora: null,
        }
    },
    methods: {
        Cargar() {
            if (!this.Aseguradora || !this.Aseguradora.Asegura)
                return

            this.axios.post("/app/v1_cobros/CatalogoSeguros", {
                Asegura: this.Aseguradora.Asegura
            }).then(resp => {
                this.seguros = resp.data.json.map(x => {
                    x.DobleFactura = Boolean(x.DobleFactura)
                    x.PermitirImprimirInformes = Boolean(x.PermitirImprimirInformes)
                    x.PolizaPlanDirecto = Boolean(x.PolizaPlanDirecto)
                    x.PolizaConPaquete = Boolean(x.PolizaConPaquete)
                    x.FacturacionPaquete = Boolean(x.FacturacionPaquete)

                    return {
                        ...x,
                        //Para no mostrar campos con negación :/
                        PagaCopago: (!x.NOPagaCopago || x.NOPagaCopago == 'N'),
                    }
                })
            })
        },
        onAdd(){
            this.editingState = false
            this.GridSeguros.addRow()
        },
        onInitNewRow(e) {
            e.data.Empresa = this.$store.state.sesion.sesion_empresa_unificadora
            e.data.Asegura = this.Aseguradora.Asegura
            e.data.FacPacienteInt = 0
            e.data.FacPacienteEme = 0
            e.data.FacPacienteExt = 0
            e.data.CoaseguroInt = 0
            e.data.CoaseguroExt = 0
            e.data.CoaseguroEme = 0
            e.data.CopagoInt = 0
            e.data.CopagoEme = 0
            e.data.CopagoExt = 0

            e.data.Activa = true
            e.data.PolizaPlanDirecto = false
            e.data.DobleFactura = false
            e.data.PagaCopago = true
            e.data.PermitirImprimirInformes = false

        },
        onRowInserting(e) {
            e.data.NOPagaCopago = !e.data.PagaCopago

            this.InitBitacora({
                Empresa: e.data.Empresa,
                Asegura: e.data.Asegura,
                Codigo: e.data.Codigo,
            }) //se incicaliza aqui para settear la llave

            this.axios.post('/app/v1_cobros/InsertarSeguro', e.data)
                .then(()=>{
                    this.Bitacora(e.data, 'Inserción')
                    this.GridSeguros.cancelEditData()
                })
                .finally(() => {
                    this.Cargar()
                })
        },
        onRowUpdating(e) {
            e.newData.NOPagaCopago = !e.newData.PagaCopago
            this.axios.post('/app/v1_cobros/ActualizarSeguro', {
                    ...e.oldData,
                    ...e.newData
                })
                .then(() => {
                    this.Bitacora(e.newData, 'Modificación')
                    this.GridSeguros.cancelEditData()
                    this.Cargar()
                })
        },
        onEditorPreparing (e) {
            //read only de la llave
            if('Codigo' == e.dataField && this.editingState){
                e.editorOptions.readOnly = true
            }
            //uppercas de los editores
            if (['Codigo','Asegura'].includes(e.dataField)) {
                setUpperCaseConfig(e.editorOptions)
            }
        },
        onEditingStart(e) {
            this.editingState=true
            this.InitBitacora(e.data)
        },
        InitBitacora(data) {
            this.objBitacora = data
            this.objBitacora.tipobitacora = 'Tipo'
            bitacora.registrar(this.objBitacora, {
                Empresa: data.Empresa,
                Asegura: data.Asegura,
                Codigo: data.Codigo,
            })
        },
        Bitacora(newData, tipobitacora) {
            return new Promise((resolve, reject) => {
                Object.keys(newData).forEach(key => {
                    if(key != '__KEY__')
                        this.objBitacora[key] = newData[key]
                })
                this.objBitacora.tipobitacora = tipobitacora
                this.axios.post('/app/bitacora/registro_bitacora', {
                    llave: {
                        Empresa: this.objBitacora.Empresa,
                        Asegura: this.objBitacora.Asegura,
                        Codigo: this.objBitacora.Codigo,
                    },
                    tabla: 'Seguros',
                    info: bitacora.obtener()
                }).then((resp) => {
                    resolve(resp)
                }).catch((err) => reject(err))
            })
        },

    },
    mounted() {
        this.Cargar()
    },
    computed: {
        GridSeguros() {
            return this.$refs['gridSeguros'].instance
        }
    },
    watch: {
        'Aseguradora'() {
            this.Cargar()
        }
    },
}
</script>
