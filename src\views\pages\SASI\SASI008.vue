<template>
<div class="mantenimiento-planes-container">
    <vx-card class="p-2" title="Montos Máximos de Siniestralidad">
        <dx-data-grid :data-source="planes" v-bind="gridOptions" @row-updating="Grabar" :editing="editing">
            <dx-data-grid-column data-field="IdPoliza" width="75" :allow-editing="false" />
            <dx-data-grid-column data-field="NombreContrato" :allow-editing="false" />
            <dx-data-grid-column data-field="NombrePlan" :allow-editing="false" />
            <dx-data-grid-column data-field="MaximoVitalicio" v-bind="monedaOptions" />
            <dx-data-grid-column data-field="MaximoAnual" v-bind="monedaOptions" />
            <dx-data-grid-column data-field="MaximoEvento" v-bind="monedaOptions" />
            <dx-data-grid-column type="buttons" width="auto" />
        </dx-data-grid>
    </vx-card>
</div>
</template>

<script>
import {
    DefaulGridOptions,
    _confMoneda
} from './data.js'
export default {
    name: 'MaximosSiniestralidad',
    components: {

    },
    data() {
        return {
            gridOptions: {
                ...DefaulGridOptions,
                
                toolbar: {
                    items: [
                        'searchPanel',
                        'groupPanel',
                        {
                            widget: 'dxButton',
                            options: {
                                icon: 'refresh',
                                type: 'success',
                                stylingMode: 'outlined',
                                hint: 'Recargar la infromación',
                                onClick: this.Cargar
                            },
                        }
                    ],
                },

            },
            planes: null,
            monedaOptions: {
                dataType: 'number',
                width: 120,
                format: _confMoneda.format,
                validationRules: [{
                    ignoreEmptyValue: false,
                    type: 'range',
                    message: 'El valor debe estar entre 0 y 922337203685477.5807',
                    min: 0,
                    max: 922337203685477.5807,
                }]
            },
        }
    },
    props: {

    },
    methods: {

        Grabar(e) {
            e.cancel = new Promise((resolve, reject) => {
                this.axios.post("/app/v1_afiliados/PlanesActualizaMaximos", {
                        plan: {
                            ...e.oldData,
                            ...e.newData,
                            IdPlanXContrato: e.oldData.IdPlan
                        }
                    })
                    .then(() => {
                        resolve(true)
                        this.Cargar()
                    }).catch(err => {
                        reject(err)
                    })
            })
        },
        Cargar() {
            return new Promise((resolve, reject) => {

                this.axios.post("/app/v1_afiliados/BusquedaPlanes", {

                    })
                    .then(resp => {
                        this.planes = resp.data.json.filter(t => t.Status == 'A' && t.StatusContrato == 'A')
                        resolve(resp.data.json)
                    }).catch(err => {
                        reject(err)
                    })

            })

        },

    },
    mounted() {
        this.Cargar()
    },
    watch: {

    },
    computed: {
        editing() {
            return {
                    allowAdding: false,
                    allowDeleting: false,
                    allowUpdating: this.$validar_privilegio('EDITAR').status,
                    mode: 'row',
                    useIcons: true,
                }
        }
    },
}
</script>
