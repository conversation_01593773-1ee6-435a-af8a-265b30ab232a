<template>
<div class="contenedor" v-if="mostrar">
    <div class="modal p-4">
        <h4>{{in_titulo}}</h4>
        <vs-divider />
        <ValidationObserver ref="form" v-slot="{invalid, handleSubmit }">
            <form method="post" @submit.prevent="handleSubmit(validar)">
                <div class="flex flex-wrap">
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-2">
                        <img src="@/assets/images/pages/security/undraw_alert_mc7b.svg" height="100px" alt="Validación" class="mx-auto">
                    </div>
                    <div class="w-full md:w-2/3 lg:w-2/3 xl:w-2/3 p-2">
                        <ValidationProvider v-if="!in_corporativo" name="Corporativo" rules="required" v-slot="{ errors }">
                            <vs-input label="Corporativo" type="number" class="w-full mb-4 input-corporativo" v-model="info.corporativo" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" v-on:keydown.enter="handleSubmit(validar)" />
                        </ValidationProvider>
                        <vs-input v-else label="Código" class="w-full" :value="info.corporativo" disabled />
                        <ValidationProvider v-if="!in_corporativo" name="Corporativo" rules="required" v-slot="{ errors }">
                            <vs-input label="Contraseña" ref="password" class="w-full mb-4 input-password" v-model="info.password" type="password" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" v-on:keydown.enter="handleSubmit(validar)" />
                        </ValidationProvider>
                    </div>
                </div>
                <div class="flex flex-wrap p-2 justify-end">
                    <vs-button clientWidth="20" class="mr-2" color="danger" type="border" icon-pack="feather" icon="icon-x" @click.native="cancelar()"> Cancelar</vs-button>
                    <vs-button clientWidth="20" class="" color="success" icon-pack="feather" icon="icon-unlock" @click.native="handleSubmit(validar)" :disabled="invalid"> Validar</vs-button>
                </div>
            </form>
        </ValidationObserver>
    </div>

</div>
</template>

<script>
export default {
    name: 'SM-Validar-Pass',
    data() {
        return {
            mostrar: false,
            info: {
                corporativo: null,
                password: null,
            },

            //callback para devolver la respuesta
            callback: null
        }
    },
    props: {
        /*
        Titulo de la validación
        */
        in_titulo: {
            default: "Verificación de usuario"
        },

        /*
        Corporativo para valida
        Si posee datos se remplaza el corporativo y se deja fijo el enviado
        */
        in_corporativo: {
            default: null
        },

    },
    components: {

    },
    methods: {
        /*
        Permite inicializar el componente
        */
        iniciar(data) {
            //se mueve info.corporativo a esta sección para validar corporativo que realiza la acción
            this.info.corporativo = null
            this.mostrar = true
            this.callback = (data) ? data : null
            //colocar foco despues de iniciar
            this.$nextTick( ()=> {
                const inputxyz = document.querySelector('.input-corporativo input')
                inputxyz.focus()
            })  
        },

        /*
        Cuando se presiona el boton de validar
        */
        validar() {
            this.axios.post('/app/usuario/obtener_permisos_corporativo', {
                    corporativo: this.info.corporativo,
                    Password: btoa(this.info.password)
                })
                .then(resp => {
                    this.callback({
                        corporativo:this.info.corporativo,
                        permisos:resp.data.json.map(data=>{
                            return data.Privilegio
                        }).filter((v, i, a) => a.indexOf(v) === i)
                    })
                    this.limpiar()
                    this.mostrar = false
                })
                .catch(()=>{
                    this.info.password = null
                    this.$refs.form.reset();
                    this.$refs.password.$el.querySelector('input').focus()
                })

        },

        /*
        Al cancelar la validación
        */
        cancelar() {
            this.limpiar()
            this.mostrar = false
            this.callback(null)
        },

        /*
        Limpiando datos
        */
        limpiar() {
            this.info.password = null
            this.$refs.form.reset();
        }
    },
    mounted() {
        if (this.in_corporativo) this.info.corporativo = this.in_corporativo
    }
}
</script>

<style>
.contenedor {
    position: fixed;
    background-color: rgba(0, 0, 0, 0.6);
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 99999;
    display: flex;
    justify-content: center;
    align-items: center;

}

.contenedor .modal {
    background-color: white;
    border-radius: 10px;
    height: 290px;
    width: 550px;

}
</style>
