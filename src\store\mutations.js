/*=========================================================================================
  File Name: mutations.js
  Description: Vuex Store - mutations
  ----------------------------------------------------------------------------------------
  Item Name: Vuexy - Vuejs, HTML & Laravel Admin Dashboard Template
  Author: Pixinvent
  Author URL: http://www.themeforest.net/user/pixinvent
==========================================================================================*/


import { buildRoutes } from '../router'
const mutations = {


    // /////////////////////////////////////////////
    // COMPONENTS
    // /////////////////////////////////////////////

    // Vertical NavMenu

    TOGGLE_IS_VERTICAL_NAV_MENU_ACTIVE(state, value) {
        state.isVerticalNavMenuActive = value
    },
    TOGGLE_REDUCE_BUTTON(state, val) {
        state.reduceButton = val
    },
    UPDATE_MAIN_LAYOUT_TYPE(state, val) {
        state.mainLayoutType = val
    },
    UPDATE_VERTICAL_NAV_MENU_ITEMS_MIN(state, val) {
        state.verticalNavMenuItemsMin = val
    },
    UPDATE_VERTICAL_NAV_MENU_WIDTH(state, width) {
        state.verticalNavMenuWidth = width
    },


    // VxAutoSuggest

    UPDATE_STARRED_PAGE(state, payload) {

        // find item index in search list state
        const index = state.navbarSearchAndPinList["pages"].data.findIndex((item) => item.url == payload.url)

        // update the main list
        state.navbarSearchAndPinList["pages"].data[index].is_bookmarked = payload.val

        // if val is true add it to starred else remove
        if (payload.val) {
            state.starredPages.push(state.navbarSearchAndPinList["pages"].data[index])
        } else {
            // find item index from starred pages
            const index_2 = state.starredPages.findIndex((item) => item.url == payload.url)

            // remove item using index
            state.starredPages.splice(index_2, 1)
        }
    },

    // Navbar-Vertical

    ARRANGE_STARRED_PAGES_LIMITED(state, list) {
        const starredPagesMore = state.starredPages.slice(10)
        state.starredPages = list.concat(starredPagesMore)
    },
    ARRANGE_STARRED_PAGES_MORE(state, list) {
        let downToUp = false
        let lastItemInStarredLimited = state.starredPages[10]
        const starredPagesLimited = state.starredPages.slice(0, 10)
        state.starredPages = starredPagesLimited.concat(list)

        state.starredPages.slice(0, 10).map((i) => {
            if (list.indexOf(i) > -1) downToUp = true
        })

        if (!downToUp) {
            state.starredPages.splice(10, 0, lastItemInStarredLimited)
        }
    },


    // ////////////////////////////////////////////
    // UI
    // ////////////////////////////////////////////

    TOGGLE_CONTENT_OVERLAY(state, val) {
        state.bodyOverlay = val
    },
    UPDATE_PRIMARY_COLOR(state, val) {
        state.themePrimaryColor = val
    },
    UPDATE_THEME(state, val) {
        state.theme = val
    },
    UPDATE_WINDOW_WIDTH(state, width) {
        state.windowWidth = width
    },
    UPDATE_WINDOW_SCROLL_Y(state, val) {
        state.scrollY = val
    },


    // /////////////////////////////////////////////
    // User/Account
    // /////////////////////////////////////////////

    // Updates user info in state and localstorage
    UPDATE_USER_INFO(state, payload) {

        // Get Data localStorage
        let userInfo = JSON.parse(localStorage.getItem("userInfo")) || state.AppActiveUser

        for (const property of Object.keys(payload)) {

            if (payload[property] != null) {
                // If some of user property is null - user default property defined in state.AppActiveUser
                state.AppActiveUser[property] = payload[property]

                // Update key in localStorage
                userInfo[property] = payload[property]
            }


        }
        // Store data in localStorage
        localStorage.setItem("userInfo", JSON.stringify(userInfo))
    },

    // /////////////////////////////////////////////
    // Sesion
    // /////////////////////////////////////////////

    SESION_GUARDAR(state, sesion) {
        //   console.log({...state.sesion})
        let sesionInfo = (localStorage.getItem("sermesa_sesion")) ? JSON.parse(atob(localStorage.getItem("sermesa_sesion"))) : state.sesion
        for (const property of Object.keys(sesion)) {
            if (sesion[property] != null) {
                state.sesion[property] = sesion[property]
                sesionInfo[property] = sesion[property]
            }
        }
        sesionInfo.sesion_sucursal_actualizacion = false

        // Intervalo de tiempo para limpieza de sucursal de todos los modulos
        state.sesion['sesion_sucursal_nombre'] = null
        state.sesion['sesion_sucursal_actualizacion'] = true
        setTimeout(() => this.commit("SESSION_GUARDAR_SUCURSAL", sesionInfo), 1000)

        // Guardando en localstorage
        localStorage.setItem("sermesa_sesion", btoa(JSON.stringify(sesionInfo)))
    },

    SESSION_GUARDAR_SUCURSAL(state, sesion) {
        state.sesion.sesion_sucursal_nombre = sesion.sesion_sucursal_nombre
        state.sesion.sesion_sucursal_actualizacion = false
    },

    SESION_ELIMINAR(state) {
        // localStorage.clear()
        localStorage.removeItem('userInfo')
        localStorage.removeItem('sermesa_sesion')
        localStorage.removeItem('sermesa_sesion_funcionalidad')
        localStorage.removeItem('sermesa_sesion_tk')
        localStorage.removeItem('sermesa_sesion_ur')
        localStorage.removeItem('sermesa_sesion_inst')

        state.sesion.corporativo = 0 // From Auth
        state.sesion.usuario = "" // From Auth
        state.sesion.about = ""
        state.sesion.token = null
        state.sesion.ip = null
        state.sesion.status_socket = false // indica si el socket esta activo y en linea
        state.sesion.sesion_empresa = null
        state.sesion.sesion_sucursal = null
        state.sesion.sesion_sucursal_nombre = null
        //indica se ha seleccionado la empresa
        state.sesion.sesion_validar = false //indica si ya se valido la información
    },


    SESION_VALIDAR(state) {
        state.sesion["sesion_validar"] = true
    },

    SESION_BIENVENIDA(state, status) {
        state.sesion["bienvenida"] = status
    },

    SESION_SOCKET(state, status) {
        state.sesion["status_socket"] = status
    },

    SESION_TIEMPO(state, restart) {
        if (restart) {
            state.sesion["sesion_tiempo"] = state.sesion["sesion_tiempo_defecto"]
        } else {
            if (state.sesion["sesion_tiempo"] > 0) {
                state.sesion["sesion_tiempo"] = state.sesion["sesion_tiempo"] - 1
            } else {
                if (state.sesion.sesion_validar) {
                    // localStorage.clear()

                    localStorage.removeItem('userInfo')
                    localStorage.removeItem('sermesa_sesion')
                    localStorage.removeItem('sermesa_sesion_funcionalidad')
                    localStorage.removeItem('sermesa_sesion_tk')
                    localStorage.removeItem('sermesa_sesion_ur')
                    localStorage.removeItem('sermesa_sesion_inst')

                    state.sesion.usuario = "" // From Auth
                    state.sesion.sesion_sucursal_nombre = ""
                    state.sesion.about = ""
                    state.sesion.token = null
                    state.sesion.ip = null
                    state.sesion.status_socket = false // indica si el socket esta activo y en linea
                    state.sesion.sesion_validar = false //indica si ya se valido la información
                }
            }
        }
    },

    APPPC_SOCKET(state, status) {
        state.sesion["appPC_socket"] = status
    },

    // /////////////////////////////////////////////
    // Funcionalidades
    // /////////////////////////////////////////////

    FUNCIONALIDAD_LISTADO(state, funcionalidades) {        
        localStorage.setItem("sermesa_sesion_funcionalidad", btoa(JSON.stringify(funcionalidades)))
        state.funcionalidades = funcionalidades
        buildRoutes()
    },

    FUNCIONALIDAD_DOCUMENTACION(state, doc) {
        state.funcionalidad_doc = doc
    },

    // /////////////////////////////////////////////
    // Privilegios
    // /////////////////////////////////////////////
    PRIVILEGIOS_LISTADO(state, privilegios) {
        state.id_funcionalidad = (privilegios.length > 0) ? privilegios[0].IdFuncionalidad : 0;
        state.privilegios = privilegios
    },


    // /////////////////////////////////////////////
    // Privilegios
    // /////////////////////////////////////////////
    TABS_LISTADO(state, tabs) {
        state.tabPath = tabs.path
        if (state.tabs.filter(f => f.path == tabs.path).length == 0) state.tabs.push(tabs)
    },

    INSTANCIA_TOKEN(state, token) {
        if (!token) return false
        localStorage.setItem("sermesa_sesion_tk", btoa(JSON.stringify({
            fecha: new Date(),
            token: token
        })))

        state.global.tokenDefault = token
    },

    INSTANCIA_LISTADO(state, instancias) {
        if (!instancias) return false
        localStorage.setItem("sermesa_sesion_ur", btoa(JSON.stringify({
            fecha: new Date(),
            instancias: instancias
        })))

        state.global.instancia_url = instancias
    },

    INSTANCIA_CAMBIO(state, instancia) {
        if (!instancia) return false
        localStorage.setItem("sermesa_sesion_inst", btoa(JSON.stringify({
            fecha: new Date(),
            instancia: instancia
        })))

        state.global.instancia = instancia
        state.global.url = state.global.instancia_urls["VUE_APP_URL_" + instancia]
    },

    /**
    Extra
     */
    EXTRA_NOTIFY(state, notificacion) {
        state.extra.notificacion = notificacion
    },
    EXTRA_LOADING(state, estado) {
        state.extra.loading = estado
    },
    EXTRA_REPORTE(state, data) {
        state.reporte = {
            titulo: null,
            pdf: null, // Base 64
            abrirTab: false, // Abrir nueva pestaña
            imprimir: false, // Imprimir
        }
        state.reporte = {
            ...state.reporte,
            ...data
        }
    },
    EXTRA_CREDENCIAL(state, solicitud) {
        state.extra.solicitarCredencial = solicitud
    }
}

export default mutations
