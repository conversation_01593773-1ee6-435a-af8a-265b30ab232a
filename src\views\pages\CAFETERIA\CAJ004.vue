<template>
    <div>
        <vs-popup :title="tituloArea" :active.sync="isVisible" style="z-index:99998;">
            <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                <div class="flex flex-wrap">
                    <div class="w-full">
                        <div class="flex flex-wrap">
                            <div class="default-div">
                                <ValidationProvider name="codigo" rules="required|max:4" class="required" v-slot="{ errors }">
                                    <label class="typo_label">Código</label>
                                    <vs-input type="number" class="vs_codigo d-inline-block" v-model="codigo" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" disabled />
                                </ValidationProvider>
                            </div>
                            <div class="default-div">
                                <ValidationProvider name="nombre" rules="required|max:60" class="required" v-slot="{ errors }">
                                    <label class="typo_label">Nombre</label>
                                    <vs-input class="vs_nombre d-inline-block" v-model="nombre" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                </ValidationProvider>
                            </div>
                            <div class="default-div">
                                <ValidationProvider name="cargable" rules="required" class="required" v-slot="{ errors }">
                                    <label class="typo_label">Cargable</label>
                                    <vs-checkbox v-model="Cargable" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                </ValidationProvider>
                            </div>
                            <div class="default-div">
                                <ValidationProvider name="clasificacion" rules="required" class="required" >
                                    <label class="typo_label">Clasificación</label>
                                    <vs-select v-model="Clasificacion">
                                        <vs-select-item :key="index" :value="item.Codigo" :text="item.Nombre" v-for="(item,index) in CaralogoClasificacion" />
                                    </vs-select>
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap">
                            <div>
                                <vs-button @click="handleSubmit(GuardarArea)" :disabled="invalid" color="success" class="btn btn-primary mx-3 my-3" icon-pack="fas" icon="fa-save">
                                    Guardar
                                </vs-button>
                            </div>
                        </div>
                    </div>
                </div>
            </ValidationObserver>
        </vs-popup>
        <vx-card title="Área">
            <div>
                <vs-button @click="NuevaArea" color="success" icon-pack="fas" icon="fa-plus-circle" >Nueva Área</vs-button>
            </div>
            <vs-divider border-style="solid" color="dark"></vs-divider>
            <div class="con-tab-ejemplo">
                <vs-table2 max-items="10" search filter pagination :data="tablaAreas" noSelectText>
                    <template slot="thead">
                        <th>Codigo</th>
                        <th>Nombre</th>
                        <th>Clasificación</th>
                        <th>Cargable</th>
                        <th width="150px">Editar | Eliminar</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data" :data="tr">
                            <vs-td2>{{ tr.Codigo }}</vs-td2>
                            <vs-td2>{{ tr.Nombre }}</vs-td2>
                            <vs-td2>{{ tr.NombreClasificacion }}</vs-td2>
                            <vs-td2>{{ tr.Cargable?'SI': 'NO' }}</vs-td2>
                            <vs-td2 class="th_editar">
                                <vs-button @click="EditarArea(tr)" color="primary" size="small" icon-pack="fas" icon="fa-edit" class="btn_editar mr-1"></vs-button>
                                <vs-button @click="EliminarArea(tr)" color="danger" size="small" icon-pack="fas" icon="fa-trash-alt" class="btn_editar mr-1"></vs-button>
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
        </vx-card>
    </div>
</template>
<script>
    import bitacora from '@/components/sermesa/funciones/SMBitacora'
    export default {
        data() {
            return{
                tablaAreas: [],
                codigo: '',
                nombre: '',
                Clasificacion: null,
                Cargable: true,
                tituloArea: '',
                isVisible: false,
                isActualizarGuardar: false,
                CaralogoClasificacion: [],

                objBitacora: null,//para control de bitácora
            }
        },
        methods: {
            ObtenerAreas() {
                this.axios.post('/app/v1_JefeCaja/ObtenerAreas', {
                    "Opcion": "C",
                    "SubOpcion": "1",
                    "Hospital": "0"
                })
                .then( resp => {
                    this.tablaAreas = resp.data.json.map(m => {
                        return{
                            ...m
                        }
                    })
                })
            },
            Clasificaciones(){
                this.axios.post('/app/v1_JefeCaja/ClasificacionArea',{}).then((resp)=>{
                    this.CaralogoClasificacion = resp.data.json
                })
            },
            ObtenerAreaSiguiente() {
                return new Promise((resolve, reject)=>{
                    this.axios.post('/app/v1_JefeCaja/SiguienteCodigoArea', {
                        "Opcion": "C",
                        "SubOpcion": "2",
                        "Hospital": "0"
                    })
                    .then( resp => {
                        this.codigo = resp.data.json[0].SiguienteCodigo
                        resolve(this.codigo)
                    })
                    .catch( err => {
                        reject(err)
                    })
                })
                
            },
            async NuevaArea() {
                this.isVisible = true
                this.isActualizarGuardar = false
                this.tituloArea = 'Nueva Área'
                await this.ObtenerAreaSiguiente()

                this.objBitacora = {
                    tipobitacora: 'Tipo',
                }
                bitacora.registrar(this.objBitacora, {Codigo: this.codigo})
                this.objBitacora.tipobitacora = 'Inserción'
            },
            EditarArea(item) {
                this.isVisible = true
                this.isActualizarGuardar = true
                this.tituloArea = 'Modificar Área'
                this.codigo = item.Codigo
                this.nombre = item.Nombre
                this.Cargable = item.Cargable
                this.Clasificacion = item.Clasificacion
                
                this.objBitacora = {
                    ...item,
                    tipobitacora: 'Tipo',
                }
                bitacora.registrar(this.objBitacora, {Codigo: this.codigo})
                this.objBitacora.tipobitacora = 'Modificación'
            },
            EliminarArea(item){
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',
                    text: `Desea eliminar el área ${ item.Nombre }?`,
                    acceptText: 'Aceptar',
                    accept: () => {

                        this.objBitacora = {
                            ...item,
                            tipobitacora: 'Tipo',
                        }
                        bitacora.registrar(this.objBitacora, {Codigo: item.Codigo})
                        
                        this.objBitacora.tipobitacora = 'Eliminación'
                        this.objBitacora.Codigo = ''
                        this.objBitacora.Nombre = ''
                        this.objBitacora.Cargable = ''
                        this.objBitacora.Clasificacion = ''

                        this.axios.post('/app/v1_JefeCaja/EliminarArea', {
                            Opcion: "E",
                            SubOpcion: "1",
                            Hospital: "0",
                            Codigo: item.Codigo,
                            Bitacora2: bitacora.obtener(),
                        })
                        .then (resp => {
                            if(resp.data.json[0].tipo_error == '0'){
                                this.$vs.notify({
                                    color: 'success',
                                    title: 'Área',
                                    text: resp.data.json[0].descripcion
                                })
                                this.ObtenerAreas()
                            }else{
                                this.$vs.notify({
                                    color: 'danger',
                                    title: 'Área',
                                    text: resp.data.json[0].descripcion
                                })
                            }
                        })
                    }
                })
            },
            GuardarArea() {

                this.objBitacora.Codigo = this.codigo
                this.objBitacora.Nombre = this.nombre
                this.objBitacora.Cargable = this.Cargable
                this.objBitacora.Clasificacion = this.Clasificacion

                if(this.isActualizarGuardar) {
                    this.axios.post('/app/v1_JefeCaja/ActualizarArea', {
                        Opcion: "A",
                        SubOpcion: "1",
                        Hospital: "0",
                        Codigo: this.codigo,
                        Nombre: this.nombre,
                        Cargable: this.Cargable,
                        Clasificacion: this.Clasificacion,
                        Bitacora2: bitacora.obtener()
                    })
                    .then(resp => {
                        this.codigo = null
                        this.nombre = null
                        this.isVisible = false
                        this.ObtenerAreas()
                        this.$vs.notify({
                            color: 'success',
                            title: 'Áreas',
                            text: resp.data.json[0].descripcion
                        })
                    })
                }else{
                    this.axios.post('/app/v1_JefeCaja/AlmacenarArea', {
                        Opcion: "I",
                        SubOpcion: "1",
                        Hospital: "0",
                        Codigo: this.codigo,
                        Nombre: this.nombre,
                        Cargable: this.Cargable,
                        Clasificacion: this.Clasificacion,
                        Bitacora2: bitacora.obtener()
                    })
                    .then(resp => {
                        if(resp.data.json[0].tipo_error == '1'){
                            this.$vs.dialog({
                                color: 'danger',
                                title: 'Áreas',
                                text: resp.data.json[0].descripcion,
                                acceptText: 'Aceptar'
                            })
                            return
                        }else{
                            this.codigo = null
                            this.nombre = null
                            this.isVisible = false
                            this.ObtenerAreas()
                            this.$vs.notify({
                                color: 'success',
                                title: 'Áreas',
                                text: resp.data.json[0].descripcion
                            })
                        }
                    })
                }
            },
        },
        activated() {
            this.ObtenerAreas()
            this.Clasificaciones()
        },
        watch: {
            isVisible(valor){
                if(!valor){
                    this.codigo = null
                    this.nombre = null
                    this.Cargable = true
                    this.Clasificacion = null
                }
            }
        }
    }
</script>
<style lang="scss" scoped>
    .default-div {
        padding: 5px;
        .vs_codigo{
            width: 75px;
        }
        .vs_nombre{   
            width: 400px;
        }
    }
    .th_editar {
        text-align: center;
        .btn_editar{
            display: inline-block;
        }
    }
</style>