<template>
    <div class="input-group">
       <BaseCheckbox
         v-for="(option, index) in options"
         :option="option"
         :key="'admision' + index"
         :id=generateID()
         :groupName="groupName" 
         :checked="isSelected(option)"
         @change="updateSelectedValues" />
    </div>
</template>
  
<script>
import BaseCheckbox from "@/components/sermesa/global/checkbox/Basecheckbox";


export default {
    name: "BaseCheckboxGroup",
    components: {
        BaseCheckbox
    },
    props: {
        options: {
            required: true,
            type: Array
        },
        initialValues: {
            default: () => []
        },
        groupName: {
            required: true,
            type: String
        }
    },
    data() {
        return {
            Id: String,
            selectedValues: [...this.initialValues]
        };
    },
    methods: {
        isSelected(option) {
            return this.selectedValues.includes(option);
        },
        updateSelectedValues(option, isChecked) {
            if (isChecked) {
                this.selectedValues.push(option);
            } else {
                this.selectedValues = this.selectedValues.filter(
                    (value) => value !== option
                );
            }
            this.$emit("input", this.selectedValues);
        },
        generateID() {
            return ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, c => (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16));
        }
    }
};
</script>