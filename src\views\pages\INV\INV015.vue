<template>
<vx-card title="Solicitud de compra">

    <div class="content content-pagex">
        <!------UBICACION--------->
        <div style="margin: 15px" class="flex flex-wrap">

            <div style="margin-left:10px;margin-right:20px" class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4">
                <ValidationProvider name="Agencia" rules="required" v-slot="{ errors }" class="required">
                    <label class="typo__label">Departamentos</label>
                    <multiselect v-model="cb_departamentos" :options="ListaDepartamentos" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Departamento_seleccionada" placeholder="Seleccionar" @input="onChangeDepartamento" :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null">
                        <span slot="noOptions">Lista no disponible.</span>
                    </multiselect>
                </ValidationProvider>
            </div>

            <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7" v-if="emprasaProducto">
                <vs-button color="primary" style="float:left;margin-top:20px;margin-right:20px" type="filled" icon-pack="fas" icon="fa-plus" @click="Abrir_Ventana_Emergente_Nuevo()"> Nueva</vs-button>
            </div>

            <div class="xs:w-full md:w-8/12 lg:w-8/12 xl:w-8/12">
                <div class="flex flex-wrap">
                    <div class="xs:w-full md:w-5/6 lg:w-5/6 xl:w-5/12">
                        <label class="typo__label">Estado</label>
                        <multiselect v-model="cb_lista_operacion" :options="lista_estado" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Operacion_seleccionada" placeholder="Seleccionar" @input="onChangeEstado">
                            <span slot="noOptions">Lista no disponible.</span>
                        </multiselect>
                    </div>

                    <div style="margin-left:10px;margin-right:20px" class="xs:w-full md:w-2/6 lg:w-2/6 xl:w-2/6">
                        <label class="typo__label">Fecha Inicio:</label>
                        <vs-input type="date" v-model="fecha_inicio" name="date1" />
                    </div>
                    <div class="xs:w-full md:w-3/6 lg:w-3/6 xl:w3/6">
                        <label class="typo__label">Fecha Final:</label>
                        <vs-input type="date" v-model="fecha_final" name="date1" />
                    </div>
                    <div class=" md:w-1/6 lg:w-1/6 xl:w-1/6">
                        <vs-button style="float:left;margin: 20px" color="primary" type="filled" icon-pack="fas" icon="fa-search" @click="Consultar_solicitud('U')"> Búsqueda</vs-button>
                    </div>

                </div>
            </div>
        </div>
        <div>

            <vs-divider></vs-divider>

            <vs-table2 tooltip max-items="10" pagination :data="Lista_solicitud" search id="tb_lista_solicitud">

                <template slot="thead">
                    <th width="100">Nº. Solicitud</th>
                    <th width="300">Departamento</th>
                    <th width="30">Tipo</th>
                    <th width="500">Observación de Solicitud</th>
                    <th width="50">Fecha solicitud</th>
                    <th width="200">Usuario</th>
                    <th width="110">Estado</th>
                    <th width="20"> Línea Tiempo</th>
                </template>

                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">

                        <vs-td2 noTooltip :data="data[indextr].CODIGO">
                            {{data[indextr].CODIGO}}
                        </vs-td2>

                        <vs-td2 :data="data[indextr].DESCRIPCION_DEPTO">
                            {{data[indextr].ID_DEPARTAMENTO}}
                            -
                            {{data[indextr].DESCRIPCION_DEPTO}}
                        </vs-td2>

                        <vs-td2 noTooltip :data="data[indextr].TIPO_OC">
                            {{data[indextr].TIPO_OC}}
                        </vs-td2>
                        <vs-td2 :data="data[indextr].OBSERVACIONES">
                            {{data[indextr].OBSERVACIONES}}
                        </vs-td2>
                        <vs-td2 noTooltip :data="data[indextr].FECHA_CREACION_ORDEN">
                            {{data[indextr].FECHA_CREACION_ORDEN}}
                        </vs-td2>
                        <vs-td2 :data="data[indextr].ID_CORPORATIVO_SOLICITA">
                            {{data[indextr].ID_CORPORATIVO_SOLICITA}}
                            -{{data[indextr].USUARIO}}
                        </vs-td2>

                        <vs-td2 style="text-align: center;" :data="data[indextr].ESTADO">
                            <label v-if="data[indextr].ESTADO == 'P'" style="height: 30px;background-color:#B2BABB;width:150px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                            <label v-if="data[indextr].ESTADO == 'A1' || data[indextr].ESTADO === 'A2'" style=" height: 30px;background-color:green;width:150px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                            <label v-if="data[indextr].ESTADO == 'R1' || data[indextr].ESTADO === 'R2'" style="height: 30px;background-color: #D9614D ;width:150px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                        </vs-td2>

                        <vs-td2 noTooltip align="center">
                            <vs-button color="warning" icon-pack="fas" icon="fa-clock" style="display:inline-block;margin-right:2px" @click="Abrir_Ventana_Emergente_Tiempo(data[indextr])"></vs-button>
                        </vs-td2>
                    </tr>
                </template>
            </vs-table2>
        </div>
    </div>

    <!--------------- Nuevo / Editar / Información  Departamento (Ventana Emergente)-------------->
    <vs-popup classContent="popup-example" :title="Titulo_emergente" :active.sync="Estado_VentanaEmergente">
        <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">

            <form>

                <!------RESPONSABLE--------->

                <b class="label-sizem" style="margin: 15px">
                    Autorizar por:
                </b><br>
                <div class="label-sizem">
                    <b style="margin: 15px">
                        {{ Nombre_resposable_1 }}
                    </b><br>
                </div>

                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-radio v-model="Id_Tipo_Seleccionado" vs-name="Id_Tipo_Seleccionado" vs-value="P"> Bien</vs-radio>
                    </div>
                </div>

                <div style="margin: 15px" class="flex flex-wrap">
                    <br>
                    <br>
                    <div class="w-full">
                        <label class="typo__label"> Observaciones</label>
                        <vs-textarea :disabled="this.Nuevo === 'N'" class="w-full" v-model="observaciones" />
                        <br>
                    </div>
                </div>
                <vs-divider></vs-divider>

                <!-- FILTROS PARA INGRESO DETALLE----->
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5" style="margin-left:10px;margin-right:20px">
                        <label for="producto"> Producto </label>
                        <vs-input :disabled="this.Nuevo === 'N'" type="text" class="w-full" placeholder="Buscar" v-model="producto_buscar" @keyup.enter="cargar_producto" @blur="cargar_producto" />
                    </div>

                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                        <ValidationProvider name="Cantidad" rules="required|max:50" v-slot="{ errors }">
                            <label for="cantidad"> Cantidad </label>
                            <vs-input type="number" count="50" v-model="Cantidad_producto" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" id="txt_codigo" />
                        </ValidationProvider>
                    </div>

                    <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7">
                        <vs-button :disabled="this.Nuevo === 'N'" color="primary" style="float:left;margin: 20px" type="filled" icon-pack="fas" icon="fa-plus" @click="Agregar_Detalle()"> Agregar</vs-button>
                    </div>

                </div>

                <!--------------INICIO DETALLE PRODUCTO SELECCIONADO ------------->
                <div style="margin: 15px" class="flex flex-wrap">
                    <div v-if="Producto_seleccionado.Codigo > 0 " class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                        <!-- DESCUENTO-->
                        <div style="border-radius:5px;padding:5px;font-size:15px;background-color:#09af00;color:white">
                            {{Producto_seleccionado.Codigo}}
                            <br>
                            {{Producto_seleccionado.marca_comercial}}
                            <br>
                            {{Producto_seleccionado.Presentacion}}
                            <br>

                        </div>

                    </div>
                </div>

                <vs-divider></vs-divider>
                <vs-table2 max-items="10" pagination :data="Lista_detalle_solicitud" id="tb_departamentos">

                    <template slot="thead">
                        <th width="100px">Número</th>
                        <th width="100px">Código</th>
                        <th width="1000px">Producto</th>
                        <th width="100px">Cantidad</th>
                        <th width="100px"></th>
                    </template>

                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2 :data="data[indextr].correlativo">
                                {{indextr + 1 }}
                            </vs-td2>

                            <vs-td2 :data="data[indextr].id_producto">
                                {{data[indextr].id_producto}}
                            </vs-td2>

                            <vs-td2 :data="data[indextr].nombre">
                                {{data[indextr].nombre}}
                            </vs-td2>

                            <vs-td2 :data="data[indextr].cantidad">
                                {{data[indextr].cantidad}}
                            </vs-td2>

                            <vs-td2 align="right">
                                <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="Eliminar_Registro(indextr, data[indextr])"></vs-button>

                            </vs-td2>
                        </tr>

                    </template>
                </vs-table2>
                <vs-divider></vs-divider>
                <vs-button :disabled="this.Nuevo === 'N'" color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" id="btn_confirmacion" @click="Confirmacion_Transaccion()"> Grabar</vs-button>
                <vs-divider></vs-divider>
            </form>

        </div>
    </vs-popup>
    <!---------------Fin Ventana Emergente _---------->

    <!--------------- Resultado busqueda -------------->
    <vs-popup classContent="popup-example" title="Resultado Búsqueda" :active.sync="Estado_VentanaEmergente_Busqueda" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
        <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">

            <form>

                <vs-divider></vs-divider>
                <vs-table2 max-items="10" pagination :data="producto" id="tb_departamentos">

                    <template slot="thead">
                        <th width="100px">Codigo</th>
                        <th width="1000px">Descripcion</th>
                        <th width="100px">Marca</th>
                        <th width="100px">Presentación</th>
                        <th></th>
                    </template>

                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2 :data="data[indextr].Codigo">
                                {{data[indextr].Codigo}}
                            </vs-td2>
                            <vs-td2 :data="data[indextr].Nombre">
                                {{data[indextr].Nombre}}
                            </vs-td2>
                            <vs-td2 :data="data[indextr].Marca">
                                {{data[indextr].Marca}}
                            </vs-td2>
                            <vs-td2 :data="data[indextr].PresentacionNombre">
                                {{data[indextr].PresentacionNombre}}
                            </vs-td2>
                            <vs-td2 align="right">
                                <vs-button color="success" icon-pack="fas" icon="fa-check-circle" style="display:inline-block;margin-right:2px" @click="Seleccionar_Producto(data[indextr])"></vs-button>
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </form>

        </div>
    </vs-popup>
    <!---------------Fin Ventana Emergente _---------->
    <!--------------- LINEA DE TIEMPO prueba-------------->
    <vs-popup classContent="popup-example" title="Línea de Tiempo" :active.sync="Estado_VentanaEmergente_Tiempo">
        <div>

            <form>

                <div>
                    <div class="titulo-disenio">
                        Solicitud de Compra Nº. {{Temp_id_orden_seleccionada}}
                    </div>
                    <br><br>
                    <div class="espacio">

                    </div>

                    <div v-for="(item, index) in Lista_LineaTiempo" :key="item.FECHA">
                        <div class="espacio_cuadros"></div>
                        <div v-if="index >0" class="linea_gris"></div>
                        <div v-if="index >0" class="flecha_gris"></div>
                        <div class="espacio_cuadros"></div>

                        <div v-if="item.ESTADO_REAL == 'C' || item.ESTADO_REAL == 'A'" class="borde_verde">
                            <div class="borde_verde_hijo">
                                <p v-if="item.ORDEN_COMPRA > 0" class="Titulo-detalle"> {{item.DESCRIPCION_DOCUMENTO}} - {{item.ORDEN_COMPRA}}</p>
                                <p v-else class="Titulo-detalle"> {{item.DESCRIPCION_DOCUMENTO}}</p>
                                <p class="Titulo-detalle"> {{item.ESTADO}}</p>
                            </div>
                            <p class="Responsable"> {{item.RESPONSABLE}}</p>
                            <p class="Responsable"> {{item.FECHA}}</p>
                            <p class="Responsable"> {{item.OBSERVACIONES}}</p>
                        </div>

                        <div v-if="item.ESTADO_REAL == 'P'" class="borde_gris">
                            <div class="borde_gris_hijo">
                                <p v-if="item.ORDEN_COMPRA > 0" class="Titulo-detalle"> {{item.DESCRIPCION_DOCUMENTO}} - {{item.ORDEN_COMPRA}}</p>
                                <p v-else class="Titulo-detalle"> {{item.DESCRIPCION_DOCUMENTO}}</p>
                                <p class="Titulo-detalle"> {{item.ESTADO}}</p>
                            </div>
                            <p class="Responsable"> {{item.RESPONSABLE}}</p>
                            <p class="Responsable"> {{item.FECHA}}</p>
                            <p class="Responsable"> {{item.OBSERVACIONES}}</p>
                        </div>

                        <div v-if="item.ESTADO_REAL == 'R' " class="borde_rojo">
                            <div class="borde_rojo_hijo">
                                <p v-if="item.ORDEN_COMPRA > 0" class="Titulo-detalle"> {{item.DESCRIPCION_DOCUMENTO}} - {{item.ORDEN_COMPRA}}</p>
                                <p v-else class="Titulo-detalle"> {{item.DESCRIPCION_DOCUMENTO}}</p>
                                <p class="Titulo-detalle"> {{item.ESTADO}}</p>
                            </div>
                            <p class="Responsable"> {{item.RESPONSABLE}}</p>
                            <p class="Responsable"> {{item.FECHA}}</p>
                            <p class="Responsable"> {{item.OBSERVACIONES}}</p>
                        </div>
                    </div>
                </div>

            </form>

        </div>
    </vs-popup>

    <!----------------- COMPONENTES -------------->
    <compNuevoProducto v-if="Estado_VentanaProducto" ref="ComponenteNuevoProducto" :callback="Mostrar_ficha_producto" :cerrar="()=>Estado_VentanaProducto=false" />

</vx-card>
</template>

<script>
/**
 * @General
 * Modulo registra las ordenes de compra de Departamento/Bodega
 */
import Multiselect from 'vue-multiselect'
import "vue-multiselect/dist/vue-multiselect.min.css"
import moment from "moment";
export default {

    components: {
           Multiselect,
    },
    data() {
        return {
            Nuevo: '',
            Titulo_emergente: '',
            Estado_VentanaEmergente: false,
            Estado_VentanaEmergente_Busqueda: false,
            Estado_VentanaEmergente_Tiempo: false,
            Estado_botones_clase_des: false,

            Id_Solicitud_compra_enc: 0,
            Id_Tipo_Seleccionado: 'P',
            Id_clase_seleccionado: 1,
            Producto_seleccionado: {
                nombre: '',
                marca_comercial: '',
                Principio_activo: '',
                Presentacion: '',
                Codigo: '',
                codigo_interno: '',
                Activo: 'N'
            },

            producto: [],
            producto_buscar: '',
            Lista_solicitud: [],
            Lista_detalle_solicitud: [{
                id_interno: 0,
                id_solicitud_enc: 0,
                correlativo: '',
                id_producto: '',
                nombre: '',
                cantidad: '',
                id_empresa: ''
            }],
            Id_responsable_1: '',
            Nombre_resposable_1: '',
            Id_responsable_2: '',
            Nombre_resposable_2: '',

            Cantidad_producto: 0,

            //Campos para Habilitar o deshabilitar todo
            Deshabilitar_campo_editar: false,
            Deshabilitar_campo_multiempresa: false,
            Deshabilitar_campo_uniempresa: false,
            Operacion: '', // N = Nuevo Registro / A = Actualización registor
            Estado_VentanaProducto: false,
            messageWhenNoItems: 'There are not items',
            Lista_LineaTiempo: [],
            Temp_id_orden_seleccionada: '',
            user: '',
            message: 'prueba mensaje',
            messages: [],
            notificaciones: [],
            ListaDepartamentos: [],
            cb_departamentos: '',
            IdDepartamento: '',
            Departamento: '',
            emprasaProducto: false,
            observaciones: '',
            cb_lista_operacion: '',
            Id_estado_seleccionado: '',
            lista_estado: [{
                    ID: 'P',
                    DESCRIPCION: 'Proceso'
                },
                {
                    ID: 'A',
                    DESCRIPCION: 'Autorizado'
                },
                {
                    ID: 'R',
                    DESCRIPCION: 'Rechazado'
                }
            ],
            fecha_inicio: this.getDateValue(new Date()),
            fecha_final: this.getDateValue(new Date()),
        };
    },
    mounted() {
        this.ConsultarDepartamentos();
        this.Valida_EmpresaIngresoOC();
        this.getDateValue()
        this.Id_estado_seleccionado = 'P';
        this.cb_lista_operacion = {
            ID: 'P',
            DESCRIPCION: 'Proceso'
        }
    },

    methods: {
        getDateValue(value) {
            if (value === undefined) {
                return '';
            } else if (value === null) {
                return '';
            } else if (value == '') {
                return '';
            }
            return moment(value).format('YYYY-MM-DD');
        },

        ConsultarDepartamentos() {

            this.axios.post('/app/v1_OrdenCompra/consulta_departamento', {})
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.ListaDepartamentos = "";
                    } else {
                        this.ListaDepartamentos = resp.data.json;
                    }
                })
                .catch(() => {})

        },
        onChangeDepartamento(value) {

            if (value !== null && value.length !== 0) {
                this.IdDepartamento = value.CODIGO;
                this.Departamento = value.NOMBRE;
                this.Consultar_solicitud('U'); //Carga  la tabla inicial

            } else {
                this.IdDepartamento = '';
                this.Departamento = '';
            }
        },
        Departamento_seleccionada({
            CODIGO,
            NOMBRE
        }) {
            return `${CODIGO} - ${NOMBRE} `
        },
        Operacion_seleccionada({
            DESCRIPCION
        }) {
            return `${DESCRIPCION} `
        },
        onChangeEstado(value) {

            if (value !== null && value.length !== 0) {
                this.Id_estado_seleccionado = value.ID;
            } else {
                this.Id_estado_seleccionado = '';

            }
        },

        Consultar_solicitud(TipoBusqueda) {

            if (this.IdDepartamento == '' || this.IdDepartamento == null) {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Solicitud de Compra',
                    text: 'Seleccionar Departamento',
                })
                return;
            }

            const url = this.$store.state.global.url
            const sesion = this.$store.state.sesion
            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/consultar_solicitud_compra', {
                    empresa: sesion.sesion_empresa,
                    id_departamento: this.IdDepartamento,
                    id_bodega: sesion.bodega_seleccionada,
                    tipo_consulta: TipoBusqueda,
                    estado: this.Id_estado_seleccionado,
                    finicial: this.fecha_inicio,
                    ffinal: this.fecha_final,
                    codigo: 0
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Solicitud de Compra',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.Lista_solicitud = [];
                    } else {
                        this.Lista_solicitud = resp.data.json;

                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();

        },
        Consultar_solicitud_detalle(datos) {

            const url = this.$store.state.global.url

            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/consultar_solicitud_compra_det', {
                    id_empresa: datos.EMPRESA,
                    id_solicitud_enc: datos.CODIGO,
                    tipo_transaccion: 'T'
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Solicitud de Compra',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.Lista_detalle_solicitud = [];
                    } else {
                        this.Lista_detalle_solicitud = resp.data.json;

                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();

        },

        Abrir_Ventana_Emergente_Nuevo() {
            this.Consultar_Responsbles(1, 'P')
            this.Nuevo = 'S';
            this.Id_Solicitud_compra_enc = 0;
            this.observaciones = ''

            if (this.IdDepartamento == '' || this.IdDepartamento == '0' || this.IdDepartamento == null) {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Solicitud de Compra',
                    text: 'Seleccionar Ubicacion',
                })
                return;
            }
            this.Lista_detalle_solicitud = [],
                this.Titulo_emergente = 'Nueva Solicitud de compra';
            this.Estado_VentanaEmergente = true;

            //Estado de los campos de la ventana emergente
            this.Deshabilitar_campo_codigo = false;
            this.Deshabilitar_campo_multiempresa = false;
            this.Deshabilitar_campo_uniempresa = false;
            this.Operacion = 'N'; // Indica la variable que es un nuevo registro
            this.Limpiar_Campos();
            this.producto_buscar = '',
                this.Cantidad_producto = 0;

            this.Check_medicamento = false,
                this.Check_suministro = false

        },
        cargar_producto: function () {

            if (this.Id_clase_seleccionado > 0) {

                this.Limpiar_Campos();
                this.$vs.loading();
                this.axios.post('/app/inventario/invProductolist', {
                        Pagina: 1,
                        Busqueda: '',
                        Clase: this.Id_clase_seleccionado,
                        SubClase: 0,
                        Operacion: 'B',
                        Nombre: this.producto_buscar.trim(),
                        Departamento: this.IdDepartamento,
                    })
                    .then(resp => {

                        this.$vs.loading.close();
                        if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                            this.producto = []
                            resp.data.json.map(data => {
                                this.producto.push({
                                    ...data
                                })
                            })
                            this.Mostrar_resultado_producto(this.producto);
                        } else {
                            this.$vs.loading.close();
                            this.producto = []
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Producto',
                                text: (resp.data.codigo < 0) ? resp.data.descripcion : 'Sin Registros',
                            })
                        }
                    })
                    .catch(() => {
                        this.$vs.loading.close();
                    })
                this.$vs.loading.close();
            } else {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Producto',
                    text: 'Seleciconar Medicamento / Suministro.',
                })
            }
        },
        Valida_EmpresaIngresoOC() {
            this.emprasaProducto = false
            const url = this.$store.state.global.url;
            this.axios.post(url + 'app/v1_OrdenCompra/Valida_EmpresaOC', {
                    productoClase: 'P',
                    iddocumento: 3
                })
                .then(resp => {
                    if (resp.data.resultado == 0) {

                        this.emprasaProducto = true
                    } else {
                        this.emprasaProducto = false
                    }
                })
        },
        Mostrar_resultado_producto(value) {

            if (value.length == 1) {
                this.Producto_seleccionado.Codigo = value.find(obj => obj.Codigo != '').Codigo;
                this.Producto_seleccionado.marca_comercial = value.find(obj => obj.Nombre != '').Nombre;
                this.Producto_seleccionado.Principio_activo = value.find(obj => obj.Principio_activo != '').Principio_activo;
                this.Producto_seleccionado.codigo_interno = value.find(obj => obj.codigo_interno != '').codigo_interno;
                this.Producto_seleccionado.Presentacion = value.find(obj => obj.PresentacionNombre != '').PresentacionNombre;
            } else if (value.length > 1) {
                this.Estado_VentanaEmergente_Busqueda = true;
            }
        },
        Seleccionar_Producto(value) {
            this.Producto_seleccionado.Codigo = value.Codigo;
            this.Producto_seleccionado.marca_comercial = value.Nombre;
            this.Producto_seleccionado.Principio_activo = value.Principio_activo;
            this.Producto_seleccionado.codigo_interno = value.codigo_interno;
            this.Producto_seleccionado.Presentacion = value.PresentacionNombre;
            this.Estado_VentanaEmergente_Busqueda = false;
            this.producto = [];
        },
        Limpiar_Campos() {
            this.Producto_seleccionado.Codigo = '';
            this.Producto_seleccionado.marca_comercial = '';
            this.Producto_seleccionado.Principio_activo = '';
            this.Producto_seleccionado.codigo_interno = '';
            this.Producto_seleccionado.Presentacion = '';
        },
        Agregar_Detalle() {
            /**
             * @General
             * Función Permite  actualizar y  almacenar un nuevo registro detalle;
             */

            if (this.Cantidad_producto <= 0) {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Solicitud de Compra',
                    text: 'Cantidad Inválida',
                })
            } else {
                if ((!this.Lista_detalle_solicitud.some(data => data.id_producto === this.Producto_seleccionado.Codigo))) {
                    if (this.Producto_seleccionado.Codigo > 0) {
                        if (this.Operacion === 'A' && this.Id_Solicitud_compra_enc > 0) {
                            const url = this.$store.state.global.url
                            const sesion = this.$store.state.sesion
                            this.$vs.loading();
                            this.axios.post(url + 'app/v1_OrdenCompra/editar_solicitud_compra', {
                                    codigo: 0,
                                    id_empresa: sesion.sesion_empresa,
                                    id_solicitud_enc: this.Id_Solicitud_compra_enc,
                                    id_producto: this.Producto_seleccionado.Codigo,
                                    cantidad: this.Cantidad_producto,
                                    corporativo: sesion.corporativo,
                                    tipo_transaccion: 'N'
                                })
                                .then(resp => {
                                    this.$vs.loading.close();
                                    if (resp.data.codigo != 0) {
                                        this.$vs.notify({
                                            color: '#B71C1C',
                                            title: 'Solicitud de Compra',
                                            text: resp.data.mensaje,
                                        })
                                        //Limpia la tabla si no existen registros
                                        return;
                                    }
                                })
                                .catch(() => {
                                    this.$vs.loading.close();
                                    return;
                                })
                            this.$vs.loading.close();
                        }
                        this.Lista_detalle_solicitud.push({
                            id_interno: 0,
                            id_solicitud_enc: 0,
                            correlativo: (this.Lista_detalle_solicitud.length + 1),
                            id_producto: this.Producto_seleccionado.Codigo,
                            nombre: this.Producto_seleccionado.marca_comercial,
                            cantidad: this.Cantidad_producto
                        })
                        this.Limpiar_Campos();
                        this.producto_buscar = ''
                        this.Cantidad_producto = 0;
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Solicitud de Compra',
                            text: 'Seleccionar Producto.',
                        })
                    }

                } else {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Solicitud de Compra',
                        text: 'Producto ingresado.',
                    })
                }
            }
        },
        Eliminar_Registro(index, datos) {
            /**
             * @General
             * Función eliminar registro;
             */
            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Confirmación',
                text: '¿Eliminar detalle? ',
                accept: () => {

                    if (this.Operacion === 'A' && datos.id_interno > 0) {
                        const url = this.$store.state.global.url
                        const sesion = this.$store.state.sesion
                        this.$vs.loading();
                        this.axios.post(url + 'app/v1_OrdenCompra/editar_solicitud_compra', {
                                codigo: datos.id_interno,
                                id_empresa: datos.id_empresa,
                                id_solicitud_enc: datos.id_solicitud_enc,
                                id_producto: datos.id_producto,
                                cantidad: datos.cantidad,
                                corporativo: sesion.corporativo,
                                tipo_transaccion: 'E'
                            })
                            .then(resp => {
                                this.$vs.loading.close();
                                if (resp.data.codigo != 0) {
                                    this.$vs.notify({
                                        color: '#B71C1C',
                                        title: 'Solicitud de Compra',
                                        text: resp.data.mensaje,
                                    })
                                    //Limpia la tabla si no existen registros
                                    return;
                                }
                            })
                            .catch(() => {
                                this.$vs.loading.close();
                                return;
                            })
                        this.$vs.loading.close();
                    }
                    this.$delete(this.Lista_detalle_solicitud, index)
                }
            })
        },
        Confirmacion_Transaccion() {
            /**
             * @General
             * Función Permite   almacenar un nuevo registro;
             */
            if (this.Nuevo === 'S') {

                if (!this.Id_Tipo_Seleccionado > 0) {
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Solicitud de Compra',
                        text: 'Seleccionar Tipo',
                    })
                    return;
                }
                if (!this.Id_clase_seleccionado > 0) {
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Solicitud de Compra',
                        text: 'Seleccionar Clase',
                    })
                    return;
                }
                if (!this.Lista_detalle_solicitud.length > 0) {
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Solicitud de Compra',
                        text: 'Ingresar detalle',
                    })
                    return;
                }

                //Genera detalle del Json
                var JsonDetalle = JSON.stringify(this.Lista_detalle_solicitud);
                const sesion = this.$store.state.sesion
                this.Corporativo_Sesion = sesion.corporativo
                const url = this.$store.state.global.url
                this.$vs.loading();
                this.axios.post(url + 'app/v1_OrdenCompra/crear_solicitud_compra', {
                        codigo: '0',
                        empresa: sesion.sesion_empresa,
                        cant_lineas: this.Lista_detalle_solicitud.length,
                        observaciones: this.observaciones,
                        id_departamento: this.IdDepartamento,
                        id_bodega: 0,
                        id_producto_clase: this.Id_Tipo_Seleccionado,
                        id_producto_subclase: this.Id_clase_seleccionado,
                        id_corporativo_solicita: sesion.corporativo,
                        datos: JsonDetalle,

                    })
                    .then(resp => {
                        this.$vs.loading.close();
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Solicitud de Compra',
                                text: resp.data.mensaje,
                            })
                        } else {
                            this.Estado_VentanaEmergente = false;
                            this.Consultar_solicitud('U');
                            //Carga  la tabla inicial
                        }
                    })
                    .catch(() => {
                        this.$vs.loading.close();
                    })
                this.$vs.loading.close();
            }
            this.Estado_VentanaEmergente = false;
        },
        Abrir_Ventana_Emergente_Editar(datos_editar) {
            this.Nuevo = 'N';
            if (datos_editar.ESTADO === 'P') {
                this.Id_Solicitud_compra_enc = 0;
                this.Lista_detalle_solicitud = [];
                this.Producto_seleccionado = [];
                this.producto_buscar = '';
                this.Cantidad_producto = 0;
                this.Titulo_emergente = 'Editar Solicitud de compra';
                this.Estado = "A";
                this.Estado_VentanaEmergente = true;
                this.Id_Tipo_Seleccionado = 'P';
                this.Id_clase_seleccionado = datos_editar.ID_PRODUCTO_SUBCLASE;
                this.Id_Solicitud_compra_enc = datos_editar.CODIGO;

                this.Consultar_Responsbles(1, 'P')
                this.Consultar_solicitud_detalle(datos_editar);
                this.observaciones = datos_editar.OBSERVACIONES

                //Estado de los campos de la ventana emergente
                this.Deshabilitar_campo_codigo = true;
                this.Deshabilitar_boton_confirmacion = false;
                this.Operacion = 'A';
                // Indica la variable que es editar el registro
            } else {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Solicitud de Compra',
                    text: 'Solicitud en proceso de autorización, no es posible editar.',
                })
            }
        },
        Consultar_Responsbles(num_documento, clase) {
            this.Id_responsable_1 = '';
            this.Nombre_resposable_1 = '';

            const url = this.$store.state.global.url
            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/consulta_listado_autorizaciones', {
                    id_documento: '1',
                    num_documento: num_documento,
                    tipo_consulta: 'E',
                    id_departamento: this.IdDepartamento,
                    clase_sol: clase
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Autorizaciones',
                            text: 'No existe responsable para autorizar',
                        })
                        //Limpia la tabla si no existen registros
                        this.Lista_autorizaciones_responsable = "";
                    } else {
                        this.Lista_autorizaciones_responsable = resp.data.json;
                        if (num_documento === 1) {
                            this.Id_responsable_1 = '';
                            this.Nombre_resposable_1 = '';
                            this.Id_responsable_1 = this.Lista_autorizaciones_responsable.find(obj => obj.ID_DOCUMENTO === '1').ID_RESPONSABLE;
                            this.Nombre_resposable_1 = this.Lista_autorizaciones_responsable.find(obj => obj.ID_DOCUMENTO === '1').NOMBRE;
                        } else if (num_documento === 2) {

                            this.Id_responsable_2 = '';
                            this.Nombre_resposable_2 = '';
                            this.Id_responsable_2 = this.Lista_autorizaciones_responsable.find(obj => obj.ID_DOCUMENTO === '1').ID_RESPONSABLE;
                            this.Nombre_resposable_2 = this.Lista_autorizaciones_responsable.find(obj => obj.ID_DOCUMENTO === '1').NOMBRE;
                        }

                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },
        Mostrar_ficha_producto() {
            this.Estado_VentanaProducto = true;
            setTimeout(() => {
                this.$refs.ComponenteNuevoProducto.Ficha_producto();
            }, 100)

        },
        Abrir_Ventana_Emergente_Tiempo(datos) {

            this.Estado_VentanaEmergente_Tiempo = true;
            this.Temp_id_orden_seleccionada = datos.CODIGO;
            const url = this.$store.state.global.url

            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/consultar_LineaTiempo', {
                    id_solicitud_enc: datos.CODIGO
                })
                .then(resp => {

                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Solicitud de Compra',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.Lista_LineaTiempo = [];
                    } else {
                        this.Lista_LineaTiempo = resp.data.json;
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        }

    },
    computed: {

        sesion() {
            return this.$store.state.sesion
        },
        sesion_Depto() {
            return this.$store.state.this.IdDepartamento;

        },
        global() {
            return this.$store.state.global
        },
        Actualizacion_Solicitud: function () {
            return this.IdDepartamento;
        }
    },

}
</script>

<style scoped>
.titulo-disenio {
    font-family: 'Barmeno';
    font-weight: 300;
    font-size: 25px;
    color: #252222;
    border-radius: 15px;
    margin: 0 auto;

    width: 400px;
    text-align: center;
    background: #fbd72f;
    margin-bottom: -40px;
}

.centrar {

    margin-bottom: -40px;
    font-family: 'Barmeno';
    margin: 0 auto;
    text-align: center;
}

.centrar-flecha {

    width: 100px;
    height: 100px;
    font-family: 'Barmeno';
    margin: 0 auto;
    text-align: center;
}

.div {
    text-align: center;
    padding-top: 30px;
}

.borde_verde {

    font-family: 'Barmeno';
    font-weight: 300;
    font-size: 25px;
    color: #ffffff;
    border-radius: 15px;
    margin: 0 auto;

    width: 350px;
    height: 150px;
    background-color: #e8eeeb;
    border: 3px solid rgb(40, 180, 99);
}

.borde_verde_hijo {

    font-family: 'Barmeno';
    font-weight: 300;
    font-size: 25px;
    color: #0f0303;
    border-radius: 5px;
    margin: 0 auto;

    width: 345px;
    height: 50px;
    background-color: #13943c;

}

.borde_rojo {

    font-family: 'Barmeno';
    font-weight: 300;
    font-size: 25px;
    color: #ffffff;
    border-radius: 15px;
    margin: 0 auto;

    width: 350px;
    height: 150px;
    background-color: #e8eeeb;
    border: 3px solid #b42828;
}

.borde_rojo_hijo {

    font-family: 'Barmeno';
    font-weight: 300;
    font-size: 25px;
    color: #0f0303;
    border-radius: 5px;
    margin: 0 auto;

    width: 345px;
    height: 50px;
    background-color: #b42828;

}

.borde_gris {

    font-family: 'Barmeno';
    font-weight: 300;
    font-size: 25px;
    color: #ffffff;
    border-radius: 15px;
    margin: 0 auto;

    width: 350px;
    height: 150px;
    background-color: #e8eeeb;
    border: 3px solid #0a97c3;
}

.borde_gris_hijo {

    font-family: 'Barmeno';
    font-weight: 300;
    font-size: 25px;
    color: #0f0303;
    border-radius: 5px;
    margin: 0 auto;

    width: 345px;
    height: 50px;
    background-color: #0a97c3;

}

.linea_gris {

    font-family: 'Barmeno';
    font-weight: 300;
    font-size: 25px;
    color: #144494;
    border-radius: 1px;
    margin: 0 auto;
    width: 10px;
    height: 30px;
    background-color: #0a97c3;
    border: 3px solid #0a97c3;
}

.flecha_gris {
    width: 0px;
    height: 0px;
    margin: 0 auto;
    border-style: solid;
    border-width: 15px;
    border-bottom-width: 0px;
    border-color: #0a97c3 transparent transparent transparent;
}

.espacio {
    width: 0px;
    height: 0px;
    margin: 0 auto;
    border-style: solid;
    border-width: 50px;
    border-bottom-width: 0px;
    border-color: #ffffff transparent transparent transparent;
}

.espacio_cuadros {
    width: 0px;
    height: 0px;
    margin: 0 auto;
    border-style: solid;
    border-width: 5px;
    border-bottom-width: 0px;
    border-color: #ffffff transparent transparent transparent;
}

.caja_verde {
    border-radius: 20px;
    border: 5px dashed #13943c;
}

.caja_roja {
    border-radius: 20px;
    border: 5px dashed firebrick;
}

.caja_gris {
    border-radius: 20px;
    border: 5px dashed gray;
}

.Titulo-detalle {
    font-family: 'Arial';
    font-size: 15px;
    font-weight: bold;
    text-align: center;
    color: rgb(255, 255, 255);
    /*    font-family: arial;
        font-size: 15px;*/
    margin-left: 10px;
}

.Responsable {
    color: rgb(12, 12, 12);
    font-family: arial;
    font-size: 13px;
    margin-left: 10px;
}

.label-sizem {
    font-size: 16px;
    font-weight: bold;
}
</style>
