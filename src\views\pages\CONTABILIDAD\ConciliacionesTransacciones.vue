<template>
    <div>
        <form @submit="handleSubmit">
            <DxForm :ref="formTransaccion" :form-data.sync="formulario" label-mode="floating" style="margin: 5px">
                <DxFormGroupItem :col-count="2">
                    <DxFormGroupItem>
                        <DxFormItem data-field="Cuenta" editor-type="dxSelectBox" :editor-options="{
                          width: 'auto',
                          searchEnabled: true,
                          items: cuentas,
                          displayExpr: 'Valor',
                          valueExpr: 'Codigo',
                          disabled: true,
                          onValueChanged: AsignarSiguienteCheque,
                      }" :validationRules="[{ type: 'required' }]" />
                        <DxFormItem data-field="Fecha" editor-type="dxDateBox" :editor-options="{
                              openOnFieldClick: true, 
                              
                             
                          }" />

                        <DxFormItem template="Botones" />

                    </DxFormGroupItem>
                    <DxFormGroupItem>
                        <DxFormItem template="Conciliaciones" v-if="conciliaciones && Object.keys(conciliaciones).length > 0" />

                        <DxFormItem template="Valores" v-if="conciliaciones && Object.keys(conciliaciones).length > 0" />

                    </DxFormGroupItem>
                </DxFormGroupItem>

                <template #Botones="{}">
                    <div class="button-container"> 
                        <div class="responsive-button-group"  >
                            <vs-tooltip text="Inicio" position="left">
                                <DxButton class="responsive-button" id="saveButtonConciliacion" style="margin-right: 2px;" :disabled="selectedIndex === 0" icon="fas fa-step-backward" type="normal" @click="irPrimero" />
                            </vs-tooltip>
                            <vs-tooltip text="Anterior" position="top">
                                <DxButton  class="responsive-button" id="saveButtonDosConcilacion" style="margin-right: 2px;" :disabled="selectedIndex === 0" icon="fas fa-caret-left" type="normal" @click="irAnterior" />
                            </vs-tooltip>
                            <vs-tooltip text="Siguiente" position="top">
                                <DxButton  class="responsive-button" id="saveButtonDosConcilacion" style="margin-right: 2px;" :disabled="selectedIndex === cuentas.length - 1" icon="fas fa-caret-right" type="normal" @click="irSiguiente" />
                            </vs-tooltip>
                            <vs-tooltip text="Ultimo" position="top">
                                <DxButton class="responsive-button" id="saveButtonConciliacion" style="margin-right: 2px;" :disabled="selectedIndex === cuentas.length - 1" icon="fas fa-step-forward" type="normal" @click="irUltimo" />
                            </vs-tooltip>
                            <vs-tooltip text="Guardar Conciliación" position="top">
                                <DxButton class="responsive-button" style="margin-right: 2px;" icon="fas fa-save" type="success" styling-mode="outlined" @click="GrabarListaConciliacion" />
                            </vs-tooltip>
                            <vs-tooltip text="Iniciar Conciliación" position="top">
                                <DxButton  class="responsive-button" :disabled="conciliaciones !== null && Object.keys(conciliaciones).length > 0" style="margin-right: 2px;" icon="fas fa-bolt" type="warning" @click="Validad" />
                            </vs-tooltip>
                            <vs-tooltip text="Cancelar Conciliación" position="bottom">
                                <DxButton class="responsive-button" style="margin-right: 2px;" icon="fas fa-trash-alt" type="danger" @click="onRowRemoving" />
                            </vs-tooltip>
                            <!--    <vs-tooltip text="Impresión de Conciliación" position="top" >
                                  <vs-button   type="border"  icon="print"  class="custom-icon"   style="color: #8e44ad;"></vs-button>
                              </vs-tooltip> -->
                            <vs-tooltip text="Seleccionar" position="bottom">
                                <DxButton  class="responsive-button" style="margin-right: 2px;" icon="fas fa-check-circle" type="success" @click="selectAllRows" />
                            </vs-tooltip>
                            <vs-tooltip text="Des Seleccionar" position="top">
                                <DxButton  class="responsive-button" style="margin-right: 2px;" icon="fas fa-ban" type="danger" @click="deselectAllRows" />
                            </vs-tooltip>
                            <vs-tooltip text="Calcular" position="bottom">
                                <DxButton class="responsive-button" style="margin-right: 2px;" icon="formula" type="default" @click="SumatoriaCondicionada" />
                            </vs-tooltip>
                            <vs-tooltip text="Des-Seleccionar Solo Cheques" position="right">
                                <DxButton class="responsive-button" v-if="conciliaciones && Object.keys(conciliaciones).length > 0" style="margin-right: 2px; background-color: #8e44ad; color: white;" icon="selectall" type="default" @click="DesmarcharChequess" />
                            </vs-tooltip>
                        </div>
                    </div>
                </template>

                <template #Conciliaciones="{}">
                    <div>
                        <DxDataGrid :data-source="conciliaciones" :focused-row-enabled="true" :focused-row-key="focusedRowKey" key-expr="Codigo" :column-auto-width="true" :show-borders="true" :width="'100%'">
                            <DxDataGridSelection mode="single" />
                            <DxDataGridColumn data-field="Periodo" caption="Codigo" />
                            <DxDataGridColumn data-field="DescripcionPeriodo" caption="Periodo" />
                            <DxDataGridPaging :page-size="1" />
                        </DxDataGrid>
                    </div>
                </template>
                <template #Valores="{}">
                    <div class="table-container">
                        <div  class="table-responsive">
                            <table class="TablaConciliacionesMontos" style="border: 2px solid #e5e7e9;">
                                <thead style="background-color: antiquewhite; color: black;">
                                    <tr>
                                        <th>Cheques Y ND</th>
                                        <th>Depósitos Y NC</th>
                                        <th>Cheque</th>
                                        <th>Estado</th>
                                        <th>Saldos</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <DxTextBox v-model="formulario.Debitos" data-type="number" :read-only="true" :disabled="true" />
                                        </td>
                                        <td>
                                            <DxTextBox v-model="formulario.Creditos" data-type="number" :read-only="true" :disabled="true" />
                                        </td>
                                        <td>
                                            <DxTextBox v-model="formulario.NumCheques" data-type="number" :read-only="true" :disabled="true" />
                                        </td>
                                        <td>Inicial</td>
                                        <td>
                                            <DxTextBox v-model="formulario.SaldoInicial" data-type="number" :read-only="true" :disabled="true" />
                                        </td>

                                    </tr>
                                    <tr>
                                        <td>
                                            <DxTextBox v-model="formulario.DebitosBanco" width="100%" @input="calcularSumas" />
                                        </td>
                                        <td>
                                            <DxTextBox v-model="formulario.CreditosBanco" width="100%" @input="calcularSumas" />
                                        </td>
                                        <td>
                                            <DxTextBox v-model="formulario.NumChequesBanco" width="100%" @input="calcularSumas" />
                                        </td>
                                        <td>Final</td>
                                        <td>
                                            <DxTextBox v-model="formulario.SaldoBanco" width="100%" />
                                        </td>
                                    </tr>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td style="color: #c0392b;">{{ TotalDebito }}</td>
                                        <td style="color: #1f618d;">{{ TotalCreditos }}</td>
                                        <td style="color:#229954 ;">{{ TotalCheques }}</td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </template>


            </DxForm>
        </form>
        <DxDataGrid ref="dataGrid" :data-source="transacciones" :show-borders="true" key-expr="Numero" :width="'100%'" :column-auto-width="true" height="300px" @key-down="handleKeyDown" :focused-row-enabled="true" style="margin-top: 15px;" >
            <!-- Columnas -->
            <DxDataGridHeaderFilter   :visible="true" :allow-search="true" />
            <DxDataGridColumn :width="50" cell-template="checkButton"  alignment="center" />
            <DxDataGridColumn data-field="Tipo" data-type="string" />
            <DxDataGridColumn data-field="Numero" data-type="number" />
            <DxDataGridColumn data-field="Monto" data-type="number" />
            <DxDataGridColumn data-field="Fecha" data-type="date" />
            <DxDataGridColumn data-field="Observaciones" data-type="string" />

            <!-- Configuraciones adicionales -->
<!--             <DxDataGridDxSearchPanel :visible="true" :highlight-case-sensitive="true" />
 -->            <DxDataGridScrolling mode="infinite" />
            <DxDataGridDxSorting mode="none" />


            <template #checkButton="{data: info}">
                <div style="display: flex; flex-wrap: wrap; justify-content: center;">
                    <vs-tooltip v-if="info.data.Status !== 'A'" text="Cambiar estado" position="bottom" style="cursor: pointer;">
                        <font-awesome-icon v-if="info.data.Conciliado === 'N'" :icon="['far', 'square']" style="font-size: 20px; color: #0ed514;" @click="check(info)" />
                        <font-awesome-icon v-if="info.data.Conciliado === 'S'" :icon="['fas', 'square-check']" style="font-size: 20px; color: #0ed514;" @click="check(info)" />
                    </vs-tooltip>
                    <font-awesome-icon v-else :icon="['fas', 'square']" style="font-size: 20px; color: #20a10a;" />
                </div>
            </template>

        </DxDataGrid>
    </div>
</template>


<script>

const formTransaccion = "formTransaccion";


export default {
  data() {
      return {
          formTransaccion,
          formulario:{
              Cuenta: null,
              Fecha: null,
              Creditos: null,
              Debitos: null,
              NumCheques: null,
              SaldoInicial: null,
              DebitosBanco: null,
              CreditosBanco: null,
              NumChequesBanco: null,
              SaldoBanco: null,
              Periodo: null
          },

          cuentas:[],
          conciliaciones:[],
          transacciones:[],
          transaccionesLinea: [],

          focusedRowKey: 1,  // Clave de la fila enfocada
          filaSeleccionada:null,
          filaSeleccionadaTable:null,
          SumatoriaTable : false,
          switchValue: false,

          totalDebitos: 0,
          totalCreditos: 0,
          totalCheques: 0,
          totalSaldos: 0,

          selectedRowKeys: [], // Almacena las claves de las filas seleccionadas
          selectedIndex: 0,
          selectAll: 0, // Estado que determina si todos los checkboxes están seleccionados, 0 = ninguno, 1 = todos seleccionados, 2 = parcial.
          rows: [], // Aquí tienes la lista de las filas (o checkboxes) que deseas seleccionar.
          evitandoSeleccionCambiada: false, // Variable de control

          Valores:{
            Cuenta: null,
              Fecha: null,
              Creditos: null,
              Debitos: null,
              NumCheques: null,
              SaldoInicial: null,
              DebitosBanco: null,
              CreditosBanco: null,
              NumChequesBanco: null,
              SaldoBanco: null,
              Periodo: null
          }

      }
  },
  watch: {
      formulario: {
          handler: "calcularSumas",
          deep: true
      },
  },
  computed:{
    TotalDebito() {
        let Tipo = ["CH", "ND", "NA", "NH", "NP", "NN", "NM",'CV','CA']

        
        let formatter = new Intl.NumberFormat("es-GT", {
              style: "currency",
              currency: "GTQ",
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
          });

        let debitos = parseFloat((this.Valores.Debitos || '0').replace(/,/g, ''));
        let debitosBancos = parseFloat((this.formulario.DebitosBanco || '0').replace(/,/g, ''));
        let totalDebitos = debitos - debitosBancos;

        this.transacciones.forEach((row) => {
            let monto = parseFloat(row.Monto) || 0; // Convertir a número y evitar NaN
            if (row.Conciliado == "N" && Tipo.includes(row.Tipo)) {
                totalDebitos -= monto;
            }
            if (row.Conciliado == "N" && Tipo.includes(row.Tipo)) {
                debitosBancos -= monto;
            }
        });

        // Aplicar formato antes de retornar
        return formatter.format(totalDebitos * - 1);
    },

      TotalCreditos(){
          let Tipo = ["DP", "NC"]; 

          let formatter = new Intl.NumberFormat("es-GT", {
              style: "currency",
              currency: "GTQ",
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
          });

          let creditos = parseFloat((this.Valores.Creditos || '0').replace(/,/g, ''));
          let creditosBancos = parseFloat((this.formulario.CreditosBanco || '0').replace(/,/g, ''));
          let totalCreditos = creditos - creditosBancos;

          this.transacciones.forEach((row) => {
              let monto = parseFloat(row.Monto) || 0; // Convertir a número y evitar NaN
              if (row.Conciliado == "N" && Tipo.includes(row.Tipo)) {
                  totalCreditos -= monto;
              }
              if (row.Conciliado == "N" && Tipo.includes(row.Tipo)) {
                creditosBancos -= monto;
            }

          });
              // Aplicar formato antes de retornar
              return formatter.format(totalCreditos * - 1);
      },


        TotalCheques() {

            const numChequesFormulario = Number(this.formulario.NumChequesBanco) || 0;
 
            
            const totalChequesCH = this.transacciones.filter(t => t.Tipo === 'CH').length;
            const chequesNoSeleccionados = this.transacciones.filter(t => 
                t.Tipo === 'CH' && t.Conciliado === 'N'
            ).length;

            return totalChequesCH - chequesNoSeleccionados - numChequesFormulario;
        },


      selectedCuenta: {
          get() {
              return this.formulario.Cuenta;
          },
          set(value) {
              this.formulario.Cuenta = value;
          },
      },
      
        isDisabled() {
            let isDisabled = this.conciliaciones !== null && Object.keys(this.conciliaciones).length > 0;
            return isDisabled;
        },
  }, 

  methods:{
      handleSubmit(e) {
        e.preventDefault();
      },

      CargarCuentas(asignar) {
        this.axios.post("/app/v1_bancos/Cuentas", {
            Opcion: 1,
        }).then((resp) => {
            this.cuentas = resp.data.json;

            if (asignar) {
              this.AsignarBanco(this.cuentas[0]);
            }
        }, { skipLoading: true });
      },

      AsignarSiguienteCheque(e) {
          this.formulario.fecha = null
          this.conciliaciones = []
          this.transacciones = []
          let cuenta = this.cuentas.find((x) => x.Codigo == e.value);
          this.AsignarBanco(cuenta);
          this.ConciliacionesEnProceso(cuenta);
      },

      AsignarBanco(e) {
          this.formulario.Cuenta = e.Codigo;
      },

      onProcesoSeleccionado(e) {
          this.filaSeleccionada = e.data;
          const rowIndex = this.conciliaciones.findIndex((item) => item.Codigo === e.data.Codigo);
          
          if (rowIndex !== -1) {
              this.focusedRowKey = e.data.Codigo; // Aquí actualizas el `focusedRowKey` correctamente
          }

           /* Datos Visuales */
           this.formulario.Creditos = this.filaSeleccionada.Creditos 
           this.formulario.Debitos = this.filaSeleccionada.Debitos
           this.formulario.NumCheques = this.filaSeleccionada.NumCheques
           this.formulario.SaldoInicial = this.filaSeleccionada.SaldoInicial
           this.formulario.Periodo = this.filaSeleccionada.Periodo
           this.formulario.Fecha = this.filaSeleccionada.Fecha

           this.CargarConciliacionesSelect()
      },

      ConciliacionesEnProceso(cuenta) {
        this.axios.post("/app/v1_bancos/ConciliacionesEnProceso", {
            Cuenta: cuenta.Codigo 
        }).then((resp) => {
            if (resp.data.codigo === 0 && Array.isArray(resp.data.json) && resp.data.json.length > 0) {
                this.conciliaciones = resp.data.json;

                // Aquí actualizamos focusedRowKey para que apunte a la primera fila
                if (this.conciliaciones && this.conciliaciones.length > 0) {
                    this.focusedRowKey = this.conciliaciones[0].Codigo; // Establece el 'focusedRowKey' al primer 'Codigo'
                    this.onProcesoSeleccionado({ data: this.conciliaciones[0] });   // Llama a 'SeleccionarData' con la primera fila
                }
                  
                }
            })
            .catch((error) => {
                // En caso de error, mostrar una notificación de error
                this.$vs.notify({
                    text: error.message || "Error en la carga de datos",
                    iconPack: "feather",
                    icon: "icon-alert-circle",
                    color: "danger",
                });
            });
        },

      ConciliacionesEnProcesos(){
          this.axios.post("/app/v1_bancos/ConciliacionesEnProceso", {
             Cuenta: this.formulario.Cuenta
          })
          .then((resp) => {
              if (
                  resp.data.codigo === 0 &&
                  Array.isArray(resp.data.json) &&
                  resp.data.json.length > 0
              ) {
                  this.conciliaciones = resp.data.json;

                  // Aquí actualizamos focusedRowKey para que apunte a la primera fila
                  if (this.conciliaciones && this.conciliaciones.length > 0) {
                      this.focusedRowKey = this.conciliaciones[0].Codigo; // Establece el 'focusedRowKey' al primer 'Codigo'

                      // Llama a 'SeleccionarData' con la primera fila
                      this.onProcesoSeleccionado({ data: this.conciliaciones[0] });
                  }

              }
          })
          .catch(() => {
              this.$vs.notify({
                  text: "Error en la carga de datos",
                  iconPack: "feather",
                  icon: "icon-alert-circle",
                  color: "danger",
              });
          });
      },

   
      CargarConciliacionesSelect() {
        this.axios.post("/app/v1_bancos/TransaccionesBancosMovtos", {
            Cuenta: this.filaSeleccionada.Cuenta,
            Periodo: this.filaSeleccionada.Periodo,
        })
        .then((resp) => {
            if (resp.data.codigo === 0) {
                // Usar directamente la respuesta del API para obtener el array
                let transaccionesAPI = resp.data.json;

                // Añadir el número de línea a cada fila del array, comenzando desde 0
                this.transacciones = transaccionesAPI.map((item, index) => {
                    item.linea = index;  // Asignamos el número de línea, comenzando desde 0
                    return item;
                });

                if (this.transacciones.length > 0) {
                    this.evitandoSeleccionCambiada = true; // Desactiva el evento

                    setTimeout(() => {
                        this.evitandoSeleccionCambiada = false;
                    }, 500);

                    this.calcularSumas();
                    this.cargarDatosConciliacionConsulta();
                }
            }
        })
    },


      cargarDatosConciliacionConsulta() {
          const fechaFormateada = new Date(this.formulario.Fecha)
              .toISOString()
              .split("T")[0]; // Formato YYYY-MM-DD
          this.axios
              .post("/app/v1_bancos/IngresarConciliacionTransaccionesConsultas", {
              Opcion: 2,
              SubOpcion: 5,
              Cuenta: this.formulario.Cuenta,
              Fecha: fechaFormateada,
              })
              .then((resp) => {
                  if (resp.data.json) {
                    
                    this.$map({
                      objeto: this.Valores,
                      respuestaAxios: resp,
                      });
                  }
              });
      },





        selectAllRows() {
            this.SumatoriaTable = false
            this.axios.post("/app/v1_bancos/Seleccionar", {
                Opcion: 2,
                SubOpcion: 10,
                CuentaMontos: this.filaSeleccionada.Cuenta,
                Periodo: this.filaSeleccionada.Periodo,
            }).then((resp) => {
                if(resp.data.codigo === 0){
                    this.SumatoriaTable = false
                    this.ConciliacionesEnProcesos()
                    this.cargarDatosConciliacionConsulta()
                    
                }
            });
        },


      deselectAllRows() {
        this.SumatoriaTable = false

          this.axios
              .post("/app/v1_bancos/Seleccionar", {
              Opcion: 2,
              SubOpcion: 11,
              CuentaMontos: this.filaSeleccionada.Cuenta,
              Periodo: this.filaSeleccionada.Periodo,
          
              })
              .then((resp) => {
              if (resp.data.codigo === 0) {
                this.SumatoriaTable = false

                this.ConciliacionesEnProcesos()

              }
              });
      },

      calcularSumas() {
          let formatter = new Intl.NumberFormat("es-GT", {
              style: "currency",
              currency: "GTQ",
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
          });

          // Asegurarse de que las entradas se procesen correctamente como números
          let debitos = parseFloat((this.formulario.Debitos || '0').replace(/,/g, ''));
          let debitosBancos = parseFloat((this.formulario.DebitosBanco || '0').replace(/,/g, ''));
          let creditos = parseFloat((this.formulario.Creditos || '0').replace(/,/g, ''));
          let creditosBancos = parseFloat((this.formulario.CreditosBanco || '0').replace(/,/g, ''));
          let numCheques = parseFloat((this.formulario.NumCheques || '0').replace(/,/g, ''));
          let numChequesBanco = parseFloat((this.formulario.NumChequesBanco || '0').replace(/,/g, ''));

          // Calcula las sumas
          let totalDebitos = debitos - debitosBancos;
          let totalCreditos = creditos - creditosBancos;
          let totalCheques = numCheques - numChequesBanco;


          // Formateamos los valores monetarios
          this.totalDebitos = formatter.format(totalDebitos);
          this.totalCreditos = formatter.format(totalCreditos);
          this.totalCheques = totalCheques;

      },

      seleccionCambiada(e) {
            if (this.evitandoSeleccionCambiada) {
                return;
            }
         
            if (e.selectedRowKeys.length > 0) {
                let selectedKey = e.selectedRowKeys[0]; // Clave de la primera fila seleccionada
                let filaSeleccionada = e.component.getDataSource().items().find(item => item.Numero === selectedKey);
                if (filaSeleccionada) {
                    this.realizarAccionConSeleccion(filaSeleccionada);
                }
            }
        },
        


      irPrimero() {
          this.transacciones = []
          this.formulario.Fecha = null
          this.selectedIndex = 0;
          this.selectedCuenta = this.cuentas[this.selectedIndex].Codigo;
      },
      irAnterior() {
          if (this.selectedIndex > 0) {
             this.transacciones = []

              this.formulario.Fecha = null
              this.selectedIndex--;
              this.selectedCuenta = this.cuentas[this.selectedIndex].Codigo;
          }
      },
      irSiguiente() {
          if (this.selectedIndex < this.cuentas.length - 1) {
              this.formulario.Fecha = null
              this.transacciones = []
              this.selectedIndex++;
              this.selectedCuenta = this.cuentas[this.selectedIndex].Codigo;
          }
      },
      irUltimo() {
          this.selectedIndex = this.cuentas.length - 1;
          this.formulario.Fecha = null
          this.transacciones = []
          this.selectedCuenta = this.cuentas[this.selectedIndex].Codigo;
      },
      

      onRowRemoving() {
          this.axios
              .post("/app/v1_bancos/DeleteIngreso", {
              Opcion: 2,
              SubOpcion: 8,
              Cuenta: this.formulario.Cuenta, // Usa el campo de la fila
              Fecha: this.filaSeleccionada.Fecha,
              Periodo: this.filaSeleccionada.Periodo
          }, { skipLoading: true })
              .then((resp) => {
              if (resp.data.codigo === 0) {
                  // Notificación de éxito
                  this.$vs.notify({
                    title: "Reinicio",
                    text: "Reinicio de Conciliación Exitosa",
                    iconPack: "feather",
                    icon: "icon-check-circle",
                    color: "success",
                    });
                  this.limpiar()

                  this.CargarCuentas(true);
                    
              } else {
                  // Notificación de error
                  this.$vs.notify({
                  title: "Error al eliminar",
                  iconPack: "feather",
                  icon: "icon-alert-circle",
                  color: "danger",
                  });
              }
              })
              .catch(() => {

                  this.$vs.notify({
                      title: "Error inesperado",
                      iconPack: "feather",
                      icon: "icon-alert-circle",
                      color: "danger",
                  });
              });
      },


      SumatoriaCondicionada() {
        // Validar que NumChequesBanco no sea mayor que NumCheques
        if (this.formulario.NumChequesBanco > this.formulario.NumCheques) {
            // Mostrar diálogo de error con Vuesax
            this.$vs.dialog({
                type: 'alert',
                color: 'danger',
                title: 'Error de validación',
                text: 'El número de cheques en banco no puede ser mayor que el número total de cheques.',
                acceptText: 'Aceptar'
            });
            return; // Salir de la función sin ejecutar ninguna sumatoria
        }
        
        // Si la validación pasa, continuar con la lógica original
        if (this.SumatoriaTable === true) {
            this.Sumatoria();
        } else {
            this.SumatoriaDos();
        }
    },


      Sumatoria(){
        this.axios.post("/app/v1_bancos/Sumatoria",{
            Opcion: 13,
            Cuenta:this.filaSeleccionadaTable.Cuenta,
            Numero:this.filaSeleccionadaTable.Numero,
            Tipo: this.filaSeleccionadaTable.Tipo,
            Periodo: this.filaSeleccionada.Periodo,
            Conciliado: this.filaSeleccionadaTable.Conciliado
        }).then((resp)=>{
            if(resp.data.codigo === 0){
              this.SumatoriaPasoDos()
            }
        })
      },

      SumatoriaPasoDos() {
        // Accedemos a los valores computados
        let totalDebitos = parseFloat(this.TotalDebito.replace(/[^0-9.]+/g, ''));  // Eliminar moneda y comas
        let totalCreditos = parseFloat(this.TotalCreditos.replace(/[^0-9.]+/g, ''));  // Eliminar moneda y comas
        let totalCheques = this.TotalCheques;  // Total de cheques calculado (si es necesario limpiar también, se puede hacer lo mismo)

        // Aseguramos que los valores tengan 4 decimales
        totalDebitos = totalDebitos.toFixed(4);
        totalCreditos = totalCreditos.toFixed(4);

        // Realizamos la llamada POST pasando esos valores
        this.axios.post("/app/v1_bancos/SumatoriaPasoDos", {
            Opcion: 14,
            Cuenta: this.filaSeleccionadaTable.Cuenta,
            Numero: this.filaSeleccionadaTable.Numero,
            Tipo: this.filaSeleccionadaTable.Tipo,
            Periodo: this.filaSeleccionada.Periodo,
            SaldoInicial: this.Valores.SaldoInicial,
            Debitos: totalDebitos,  // Usamos el valor de totalDebitos
            Credito: totalCreditos,  // Usamos el valor de totalCreditos
            NoCheques: totalCheques,  // Usamos el valor de totalCheques
            Codigo: this.filaSeleccionada.Codigo,
            Fecha: this.formulario.Fecha
         })
        .then(() => {
            // Manejo de la respuesta si es necesario
            this.SumatoriaTable === false
            this.ConciliacionesEnProcesos()
        })
        .catch(() => {
         
        });
    },


    SumatoriaDos(){
          // Accedemos a los valores computados
        let totalDebitos = parseFloat(this.TotalDebito.replace(/[^0-9.]+/g, ''));  // Eliminar moneda y comas
        let totalCreditos = parseFloat(this.TotalCreditos.replace(/[^0-9.]+/g, ''));  // Eliminar moneda y comas
        let totalCheques = this.TotalCheques;  // Total de cheques calculado (si es necesario limpiar también, se puede hacer lo mismo)

        // Aseguramos que los valores tengan 4 decimales
        totalDebitos = totalDebitos.toFixed(4);
        totalCreditos = totalCreditos.toFixed(4);

        // Realizamos la llamada POST pasando esos valores
        this.axios.post("/app/v1_bancos/SumatoriaDos", {
            Opcion: 15,
            Cuenta: this.filaSeleccionada.Cuenta,
            Periodo: this.filaSeleccionada.Periodo,
            SaldoInicial: this.formulario.SaldoInicial,
            Debitos: totalDebitos,  // Usamos el valor de totalDebitos
            Credito: totalCreditos,  // Usamos el valor de totalCreditos
            NoCheques: totalCheques,  // Usamos el valor de totalCheques
            Codigo: this.filaSeleccionada.Codigo,
        })
        .then(() => {
            // Manejo de la respuesta si es necesario
            this.ConciliacionesEnProcesos()
        })

    },

      Validad() {
          let fecha = new Date(this.formulario.Fecha);
          let fechaFormateada = fecha.toISOString().split("T")[0]; // Formato YYYY-MM-DD
          this.axios
              .post("/app/v1_bancos/Validacion", {
              Opcion: 2,
              SubOpcion: 9,
              Cuenta: this.formulario.Cuenta,
              Fecha: fechaFormateada,
              })
              .then((resp) => {
                  if (resp.data.codigo === 0) {
                      this.cargarDatosConciliacion()
                    
                  }
              });
      },

      cargarDatosConciliacion() {
          const fechaFormateada = new Date(this.formulario.Fecha)
              .toISOString()
              .split("T")[0]; // Formato YYYY-MM-DD
          this.axios
              .post("/app/v1_bancos/IngresarConciliacionTransaccionesConsultas", {
              Opcion: 2,
              SubOpcion: 5,
              Cuenta: this.formulario.Cuenta,
              Fecha: fechaFormateada,
              })
              .then((resp) => {
                  if (resp.data.json) {
                      this.$map({
                      objeto: this.formulario,
                      respuestaAxios: resp,
                      });
                  }
                  this.InsertarRegistroLista()
              });
      },

      InsertarRegistroLista() {
          // Validar que Creditos y Debitos no sean nulos o indefinidos
          if (this.formulario.Creditos == null || this.formulario.Debitos == null) {
              this.$vs.notify({
              title: "Campos obligatorios (O_o)",
              text: "Los campos 'Créditos' y 'Débitos' no pueden estar vacíos.",
              iconPack: "feather",
              icon: "icon-alert-circle",
              color: "warning",
              });
              return; // Detener la ejecución si la validación falla
          }

          // Obtener fecha en formato YYYY-MM-DD
          const fechaFormateada = new Date(this.formulario.Fecha)
              .toISOString()
              .split("T")[0];

          // Ejecutar la solicitud a la API
          this.axios.post("/app/v1_bancos/IngresarConciliacionTransaccionesIngreso", {
              Opcion: 2,
              SubOpcion: 6,
              Cuenta: this.formulario.Cuenta,
              Fecha: fechaFormateada,
              SaldoInicial: this.formulario.SaldoInicial,
              Credito: this.formulario.Creditos,
              Debitos: this.formulario.Debitos,
              NoCheques: this.formulario.NumCheques
          }, { skipLoading: true })
              .then((resp) => {
              if (resp.data.codigo === 0) {
                  this.$vs.notify({
                  title: "Inicio exitoso",
                  text: "Conciliación Iniciada Exitosamente",
                  iconPack: "feather",
                  icon: "icon-check-circle",
                  color: "success",
                  });
              } else {
                  // Si la respuesta no es exitosa, puedes agregar manejo de errores aquí.
                  this.$vs.notify({
                  title: "Error",
                  text: resp.data.mensaje || "Hubo un problema al iniciar la conciliación.",
                  iconPack: "feather",
                  icon: "icon-alert-circle",
                  color: "danger",
                  });
              }
          })
          .catch(() => {
          // Manejo de errores en la solicitud
              this.$vs.notify({
                  title: "Error",
                  text: "No se puede ingresar a la Conciliación",
                  iconPack: "feather",
                  icon: "icon-alert-circle",
                  color: "danger",
              });
              
          }).finally(() => {
             this.ConciliacionesEnProcesos()
          });
      },

    GuardarConciliacion() {
          if (this.formulario.SaldoBanco === null || this.formulario.SaldoBanco === 0) {
              this.$vs.dialog({
              type: 'confirm',
              color: '#e74c3c',
              title: 'Error',
              acceptText: 'Aceptar',
              text: `El Saldo Final es nulo o cero. No se puede guardar la conciliación`,
              buttonCancel: 'border',
              clientWidth: 100,
                accept: () => {

                },
              });

              return; // No ejecutar la función si la condición es verdadera
          }
          this.axios
              .post("/app/v1_bancos/SaveConciliaciones ", {
              Opcion: 2,
              SubOpcion: 12,
              Cuenta: this.formulario.Cuenta, // Usa el campo de la fila
              Periodo: this.formulario.Periodo,
              Fecha: this.filaSeleccionada.Fecha,
              })
              .then((resp) => {
                if(resp.data.codigo === 0){
                    this.$vs.dialog({
                        type: 'confirm',
                        color: '#27ae60',
                        title: 'Confirmación de Grabado',
                        acceptText: 'Aceptar',
                        cancelText: 'Cancelar',
                        text: `Conciliación Grabada Exitosamente!`,
                        buttonCancel: 'border',
                        clientWidth: 100,
                        accept: () => {
                            this.limpiar();
                        },
                        cancel: () => {}
                    })
                }
            });
    },

    GrabarListaConciliacion() {
        this.axios.post("/app/v1_bancos/UpdateConciliaciones",{
            Opcion: 2,
            SubOpcion: 13,
            Cuenta: this.filaSeleccionada.Cuenta, 
            Periodo: this.filaSeleccionada.Periodo,
            Codigo: this.filaSeleccionada.Codigo,
            Fecha: this.filaSeleccionada.Fecha,

            //Datos para buscar
           //Datos para buscar
            Debitos: this.filaSeleccionada.Debitos.toString().replace(/,/g, ''),
            Creditos: this.filaSeleccionada.Creditos.toString().replace(/,/g, ''),
            NumCheques: this.filaSeleccionada.NumCheques.toString().replace(/,/g, ''),
            SaldoInicial: this.filaSeleccionada.SaldoInicial.toString().replace(/,/g, ''),

            // datos agregar 
            SaldoFinal: this.formulario.SaldoBanco,
            Tipo: "U",
            Debitobanco: this.formulario.DebitosBanco,
            CreditoBanco: this.formulario.CreditosBanco,
            NumChequesBanco: this.formulario.NumChequesBanco
        }).then((resp) => {
            if(resp.data.codigo === 0){
                   this.GuardarConciliacion()
            }
        })
    },

    handleKeyDown(e) {
        const dataGrid = this.$refs.dataGrid?.instance;
        if (!dataGrid) return;

        if (e.event.key === "Home") {
            // Ctrl + Inicio -> Ir a la primera fila y desplazar la vista
            e.event.preventDefault();
        
            dataGrid.option("focusedRowIndex", 0);
            dataGrid.navigateToRow(dataGrid.getKeyByRowIndex(0));

            this.$nextTick(() => {
                dataGrid.getScrollable().scrollTo({ top: 0 });
            });
        } 
        else if (e.event.key === "End") {
            // Ctrl + Fin -> Ir a la última fila del dataSource y desplazar la vista
            e.event.preventDefault();
            dataGrid.focus(); // Asegurar el foco antes de continuar

            const totalRows = this.transacciones.length;
            if (totalRows === 0) return;

            const lastRowIndex = totalRows - 1;
            
            dataGrid.option("focusedRowIndex", lastRowIndex);
            dataGrid.navigateToRow(dataGrid.getKeyByRowIndex(lastRowIndex));

            this.$nextTick(() => {
                dataGrid.getScrollable().scrollTo({ top: Number.MAX_SAFE_INTEGER });
            });
        } 
        else if (e.event.key === " ") {
            e.event.preventDefault(); // Evita el scroll
            dataGrid.focus(); // Asegurar el foco antes de continuar

            this.$nextTick(() => {
                const focusedRowIndex = dataGrid.option("focusedRowIndex");
                
                // Obtener el dataSource de la tabla (que es un arreglo de objetos)
                const dataSource = dataGrid.option("dataSource");

                // Verificar si el índice está dentro de los límites del dataSource
                if (focusedRowIndex >= 0 && focusedRowIndex < dataSource.length) {
                    const focusedRowData = dataSource[focusedRowIndex];

                    if (focusedRowData) {
                        // Si tienes una clave única (por ejemplo, 'linea' o 'Numero'), usa esa para obtener la fila por clave
                        const rowIndexByKey = dataGrid.getRowIndexByKey(focusedRowData.linea); // O 'Numero' si es el identificador único
                        dataGrid.option("focusedRowIndex", rowIndexByKey); // Asegurarte de que se seleccione la fila correcta
                        // Llamar a obtenerInformacionFila con la fila correcta
                        this.obtenerInformacionFila(focusedRowData);
                    }
                }
            });

        } 
    },  

    check(info) {

        // Luego llamas a las otras funciones que necesites
        this.obtenerInformacionFila(info.data);
        this.SumatoriaTable = true;
        
    },

    obtenerInformacionFila(fila) {
        if (fila.Conciliado === 'N') {
            fila.Conciliado = 'S';
        } else if (fila.Conciliado === 'S') {
            fila.Conciliado = 'N';
        }

        this.filaSeleccionadaTable = fila;
        this.realizarAccionConSeleccion(fila);
    },

    realizarAccionConSeleccion(seleccionado) {
        this.axios.post("/app/v1_bancos/SeleccionarUno", {
            Opcion: 10,
            Cuenta: this.formulario.Cuenta,
            Periodo: seleccionado.Periodo,
            Tipo: seleccionado.Tipo,
            Numero: seleccionado.Numero,
            Conciliado: seleccionado.Conciliado
        }, { skipLoading: true }) // Agregar aquí el parámetro
        .then(() => this.ConsultaUno(seleccionado))
        .catch(() => {
        });
    },

    ConsultaUno(seleccionado) {
        this.axios.post("/app/v1_bancos/BuscarUno", {
            Opcion: 16,
            Periodo: seleccionado.Periodo,
            Tipo: seleccionado.Tipo,
            Numero: seleccionado.Numero
        }, { skipLoading: true }) // Agregar aquí el parámetro de configuración
        .then((resp) => {
            if(resp.data.codigo === 0){
                this.transaccionesLinea = resp.data.json;

                this.transaccionesLinea.forEach(nuevoRegistro => {
                    let index = this.transacciones.findIndex(t =>
                        t.Numero === nuevoRegistro.Numero && t.Tipo === nuevoRegistro.Tipo
                    );

                    if (index !== -1) {
                        // Si en transacciones Conciliado es 'S' pero en el array nuevo es 'N', cambiarlo y viceversa
                        if (this.transacciones[index].Conciliado !== nuevoRegistro.Conciliado) {
                            this.transacciones[index].Conciliado = nuevoRegistro.Conciliado;
                        }
                    }
                });
            }
          
        }).catch(() => {
        });
    },

    DesmarcharChequess(){
        this.axios.post("/app/v1_bancos/DesmarcarCheques",{
            Cuenta: this.filaSeleccionada.Cuenta,
            Periodo: this.filaSeleccionada.Periodo
        }).then(()=>{
            this.calcularSumas();
            this.ConciliacionesEnProcesos();
            this.cargarDatosConciliacionConsulta();
        })
    },

    limpiar(){
        this.transacciones = []
        this.conciliaciones = []
        this.formulario.Fecha = null
        this.formulario.Debitos = null
        this.formulario.Creditos = null
        this.formulario.NumCheques =  null
        this.formulario.SaldoInicial = null
        this.formulario.DebitosBanco = null
        this.formulario.CreditosBanco = null
        this.formulario.NumChequesBanco = null
        this.formulario.SaldoBanco = null
        this.formulario.Periodo = null     
        this.selectedRowKeys = []// Almacena las claves de las filas seleccionadas
        this.selectedIndex = 0
        this.evitandoSeleccionCambiada = false // Variable de control
        this.filaSeleccionada = null
        this.CargarCuentas(true);
    }
    
  },
 
  beforeMount() {
      this.CargarCuentas(true);
  }
}
</script>

<style>

.custom-icon {
    background-color: #f4f6f7 !important;
}

.custom-icon .vs-icon {
  font-size: 27px !important; /* Aumenta el tamaño del ícono */
}

#saveButtonConciliacion .dx-icon {
    font-size: 17px;
    color: black;

}

#saveButtonConciliacion{
    width: 50px !important;

}

#saveButtonDosConcilacion .dx-icon {
    font-size: 25px;
    color: black;
}

#saveButtonDosConcilacion{
    width: 50px !important;
    
}

.OverFlow .contenedor-tabla
{
  overflow-y: auto !important;
  max-height: 400px !important; 
}


.table-container {
  width: 100%;
  display: flex;
  margin-top: 10px;
  justify-content: flex-end;
}

.table-responsive {
  max-width: 100%;
  overflow-x: auto;
}

.TablaConciliacionesMontos {
  border: 2px solid #e5e7e9;
  border-collapse: collapse;
  min-width: 600px; /* Ancho mínimo antes de que aparezca scroll */
}

.TablaConciliacionesMontos th,
.TablaConciliacionesMontos td {
  padding: 8px 12px;
  text-align: left;
  border: 1px solid #e5e7e9;
}

/* Estilos para móviles */
@media (max-width: 768px) {
  .table-container {
    justify-content: flex-start;
  }
  
  .TablaConciliacionesMontos {
    font-size: 14px;
  }
}

/* Estilos para pantallas muy pequeñas */
@media (max-width: 480px) {
  .TablaConciliacionesMontos {
    min-width: 100%;
  }
  
  .TablaConciliacionesMontos th,
  .TablaConciliacionesMontos td {
    padding: 6px 8px;
    font-size: 12px;
  }
}

 .button-container {
    width: 100%;
    overflow-x: auto;
    padding: 8px 0;
  }
  
  .responsive-button-group {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 6px;
    min-width: fit-content;
  }
  
  .responsive-button {
    min-width: 40px;
    height: 40px;
  }
  
  @media (max-width: 768px) {
    .responsive-button {
      padding: 6px;
      font-size: 12px;
    }
  }
  
  @media (max-width: 480px) {
    .responsive-button-group {
      flex-direction: row;
      flex-wrap: wrap;
    }
    
    .responsive-button {
      flex: 1 1 calc(25% - 6px);
      max-width: calc(25% - 6px);
    }
  }
</style>