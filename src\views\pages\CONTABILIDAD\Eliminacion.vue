<template>
<div>
    <form @submit="handleSubmit">
        <DxForm :ref="formEliminacion" :form-data.sync="formulario" label-mode="floating">
            <DxFormGroupItem :col-count="1">
                <DxFormGroupItem>
                    <DxFormItem data-field="Cuenta" editor-type="dxSelectBox" :editor-options="{ width: 'auto', searchEnabled: true, items: cuentas,  displayExpr: 'Valor', valueExpr: 'Codigo' }" :validationRules="[{ type: 'required' }]" />
                    <DxFormGroupItem :col-count="2">
                        <DxFormGroupItem>
                            <DxFormItem data-field="ChequeInicial" editor-type="dxNumberBox" :editor-options="{ min: 0, format: '#0', step: 1 }">
                                <DxFormLabel text="Del número" />
                            </DxFormItem>
                            <DxFormItem data-field="Inicial" :disabled="false" editor-type="dxTextBox" :editor-options="{ readOnly: true, tabIndex: -1 }">
                                <DxFormLabel text=" " />
                            </DxFormItem>
                        </DxFormGroupItem>
                        <DxFormGroupItem>
                            <DxFormItem data-field="ChequeFinal" editor-type="dxNumberBox" :editor-options="{ min: 0, format: '#0', step: 1, onFocusOut: AsignarFinal }">
                                <DxFormLabel text="Al número" />
                            </DxFormItem>
                            <DxFormItem data-field="Final" :disabled="false" editor-type="dxTextBox" :editor-options="{ readOnly: true, tabIndex: -1 }">
                                <DxFormLabel text=" " />
                            </DxFormItem>
                        </DxFormGroupItem>
                    </DxFormGroupItem>
                    <DxFormButtonItem :button-options="buttonEliminar" :visible="formulario.Inicial !== null && formulario.Final !== null" name="Eliminar" horizontal-alignment="center" verical-alignment="center" />
                </DxFormGroupItem>
            </DxFormGroupItem>
        </DxForm>
    </form>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'
import 'devextreme-vue/lookup'

// import DxButton from 'devextreme-vue/button'

const formEliminacion = 'formEliminacion'

export default {
    name: 'Eliminacion',
    components: {},
    data() {
        return {
            DefaultDxGridConfiguration,
            formEliminacion,

            formulario: {
                Cuenta: null, // Codigo de la cuenta de banco de 2 dígitos
                BancoContabilidad: null, // Cuenta contable de la cuenta bancaria seleccionada
                ChequeInicial: null, // Primer cheque del rango a eliminar
                ChequeFinal: null, // Último cheque del rango a eliminar
                NombreCuentaBanco: null, // Nombre de la cuenta contable de la cuenta bancaria
                Inicial: null,
                Final: null
            },
            cuentas: [],

            buttonEliminar: {
                width: 'auto',
                icon: 'fas fa-trash',
                text: 'Eliminar',
                type: 'danger',
                onClick: () => {
                    this.Eliminar()
                },
                useSubmitBehavior: false,
            },

            infoChequeInicial: null,
            infoChequeFinal: null,

            colAnticipo: null
        }
    },
    props: {
        ModalidadPago: null,
        TipoCheque: null,
        Corporativo: null
    },
    methods: {
        handleSubmit(e) {
            e.preventDefault()
        },

        CargarCuentas() {
            this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 1
                })
                .then(resp => {
                    this.cuentas = resp.data.json

                    this.formulario.Cuenta = this.cuentas[0].Codigo
                })
        },

        HabilitarBoton() {
            if (this.formulario.Inicial !== null && this.formulario.Final !== null) {
                this.formEliminacionInstance.itemOption('Eliminar', 'visible', true)
            } else {
                this.formEliminacionInstance.itemOption('Eliminar', 'visible', false)
            }
        },

        ValidarCheque(cheque) {
            if (cheque !== null && parseInt(cheque) > 0) {
                return true
            }
            return false
        },

        NumeroComas(x) {
            x = parseFloat(x).toFixed(2)
            return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        },

        async BuscarCheque(cheque) {
            return await this.axios.post('/app/v1_bancos/Eliminacion', {
                    Opcion: 1,
                    Cuenta: parseInt(this.formulario.Cuenta),
                    TipoCheque: this.TipoCheque,
                    ChequeInicial: cheque
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        return resp.data.json[0]
                    }
                    return null
                })
        },

        AsignarInicial() {
            let temp = this.formulario.ChequeInicial

            this.formulario.ChequeInicial = null
            this.formulario.Inicial = null
            this.formEliminacionInstance.repaint()

            setTimeout(() => {
                this.formulario.ChequeInicial = temp
            }, 500);
        },

        async AsignarFinal() {
            if (this.formulario.ChequeFinal !== null && this.formulario.ChequeFinal > 0) {
                this.formulario.Final = null
                this.infoChequeFinal = null
                this.infoChequeFinal = await this.BuscarCheque(this.formulario.ChequeFinal)
                this.formEliminacionInstance.repaint()

                if (this.infoChequeFinal !== null) {
                    if (this.infoChequeFinal.Impresion == null) {
                        this.formulario.Final = this.infoChequeFinal.NombreBeneficiario + ' - Q. ' + this.NumeroComas(this.infoChequeFinal.Monto)
                        this.formEliminacionInstance.repaint()
                    } else {
                        let texto = 'El '
                        if (this.ModalidadPago == 'C') {
                            texto = texto + 'cheque'
                        } else {
                            texto = texto + 'recibo'
                        }

                        texto = texto + ' ya se imprimió, no puede eliminarlo.'

                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Ya se imprimió',
                            acceptText: 'Aceptar',
                            text: texto,
                            buttonCancel: 'border',
                            accept: () => {
                                setTimeout(() => {
                                    this.formEliminacionInstance.getEditor('ChequeFinal').focus()
                                }, 500);
                            },
                        })
                        return
                    }
                } else {
                    let texto = 'El '
                    if (this.ModalidadPago == 'C') {
                        texto = texto + 'cheque'
                    } else {
                        texto = texto + 'recibo'
                    }

                    texto = texto + ' que ha ingresado no existe'

                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'No existe',
                        acceptText: 'Aceptar',
                        text: texto,
                        buttonCancel: 'border',
                        accept: () => {
                            setTimeout(() => {
                                this.formEliminacionInstance.getEditor('ChequeFinal').focus()
                            }, 500);
                        },
                    })
                    return
                }

            }
        },

        async Eliminar() {
            let eliminar = {
                Cheques: {},
                Retenciones: {},
                Devoluciones: {},
                Referencias: [],
                Cupones: [],
                Multiniveles: [],
                SaldoFactura: {},
                Saldos: {},
                ProntoPago: {},
                EliminarPagos: {},
                EliminarCompraPagos: {},
                ActualizarPagos: {},
                Bonificaciones: {},
                Radiologos: {},
                EliminarBonificaciones: {},
                EliminarAnticipos: {},
                Pago: {},
                Ajenos: {}
            }

            let comprasPagos = await this.BuscarComprasPagos()

            eliminar.Cheques = {
                Opcion: 3,
                TipoCheque: this.TipoCheque,
                Cuenta: this.formulario.Cuenta,
                ChequeInicial: this.formulario.ChequeInicial,
                ChequeFinal: this.formulario.ChequeFinal,
            }

            if (this.ModalidadPago == 'C') {
                eliminar.Retenciones = {
                    Opcion: 9,
                    TipoCheque: this.TipoCheque,
                    Cuenta: this.formulario.Cuenta,
                    ChequeInicial: this.formulario.ChequeInicial,
                    ChequeFinal: this.formulario.ChequeFinal,
                }

                eliminar.Devoluciones = {
                    Opcion: 10,
                    TipoCheque: this.TipoCheque,
                    Cuenta: this.formulario.Cuenta,
                    ChequeInicial: this.formulario.ChequeInicial,
                    ChequeFinal: this.formulario.ChequeFinal
                }
            }

            if (comprasPagos !== null) {
                for (let i = 0; i < comprasPagos.length; i++) {
                    let nuevo = {
                        Cuenta: null,
                        Cheque: null,
                        ProveedorInicio: comprasPagos[i].Proveedor,
                        Documento: comprasPagos[i].Documento
                    }
                    switch (comprasPagos[i].Tipo) {
                        case 'C':
                            nuevo.Opcion = 9
                            eliminar.Referencias.push(nuevo)
                            break;

                        case 'U':
                            nuevo.Opcion = 10
                            eliminar.Cupones.push(nuevo)
                            break;

                        case 'G':
                            nuevo.Opcion = 11
                            eliminar.Multiniveles.push(nuevo)
                            break;
                    }
                }
            }

            eliminar.SaldoFactura = {
                Opcion: 4,
                Cuenta: this.formulario.Cuenta,
                ChequeInicial: this.formulario.ChequeInicial,
                ChequeFinal: this.formulario.ChequeFinal
            }

            eliminar.Saldos = {
                Opcion: 15,
                Cuenta: this.formulario.Cuenta,
                TipoCheque: this.TipoCheque,
                ChequeInicial: this.formulario.ChequeInicial,
                ChequeFinal: this.formulario.ChequeFinal
            }

            eliminar.ProntoPago = {
                Opcion: 7,
                TipoCheque: this.TipoCheque,
                Cuenta: this.formulario.Cuenta,
                ChequeInicial: this.formulario.ChequeInicial,
                ChequeFinal: this.formulario.ChequeFinal
            }

            eliminar.EliminarPagos = {
                Opcion: 5,
                TipoCheque: this.TipoCheque,
                Cuenta: this.formulario.Cuenta,
                ChequeInicial: this.formulario.ChequeInicial,
                ChequeFinal: this.formulario.ChequeFinal
            }

            eliminar.EliminarCompraPagos = {
                Opcion: 6,
                TipoCheque: this.TipoCheque,
                Cuenta: this.formulario.Cuenta,
                ChequeInicial: this.formulario.ChequeInicial,
                ChequeFinal: this.formulario.ChequeFinal
            }

            eliminar.ActualizarPagos = {
                Opcion: 7,
                TipoCheque: this.TipoCheque,
                Cuenta: this.formulario.Cuenta,
                ChequeInicial: this.formulario.ChequeInicial,
                ChequeFinal: this.formulario.ChequeFinal
            }

            eliminar.Bonificaciones = {
                Opcion: 19,
                TipoCheque: this.TipoCheque,
                Cuenta: this.formulario.Cuenta,
                ChequeInicial: this.formulario.ChequeInicial,
                ChequeFinal: this.formulario.ChequeFinal
            }

            eliminar.Radiologos = {
                Opcion: 17,
                Cuenta: this.formulario.Cuenta,
                ChequeInicial: this.formulario.ChequeInicial,
                ChequeFinal: this.formulario.ChequeFinal
            }

            eliminar.EliminarBonificaciones = {
                Opcion: 20,
                TipoCheque: this.TipoCheque,
                Cuenta: this.formulario.Cuenta,
                ChequeInicial: this.formulario.ChequeInicial,
                ChequeFinal: this.formulario.ChequeFinal
            }

            eliminar.EliminarAnticipos = {
                Opcion: 8,
                Cuenta: this.formulario.Cuenta,
                ChequeInicial: this.formulario.ChequeInicial,
                ChequeFinal: this.formulario.ChequeFinal,
                Columna: this.colAnticipo
            }

            if (this.ModalidadPago == 'C') {
                eliminar.Pago = {
                    Opcion: 9,
                    Cuenta: this.formulario.Cuenta,
                    ChequeInicial: this.formulario.ChequeInicial,
                    ChequeFinal: this.formulario.ChequeFinal
                }
            } else {
                eliminar.Pago = {
                    Opcion: 10,
                    Cuenta: this.formulario.Cuenta,
                    ChequeInicial: this.formulario.ChequeInicial,
                    ChequeFinal: this.formulario.ChequeFinal
                }
            }

            eliminar.Ajenos = {
                Opcion: 11,
                Cuenta: this.formulario.Cuenta,
                ChequeInicial: this.formulario.ChequeInicial,
                ChequeFinal: this.formulario.ChequeFinal
            }

            await this.axios.post('/app/v1_bancos/EliminacionAgrupado', {
                    ...eliminar
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.LimpiarVariables()
                    }
                })

        },

        async BuscarComprasPagos() {
            return await this.axios.post('/app/v1_bancos/Eliminacion', {
                    Opcion: 2,
                    TipoCheque: this.TipoCheque,
                    Cuenta: this.formulario.Cuenta,
                    ChequeInicial: this.formulario.ChequeInicial,
                    ChequeFinal: this.formulario.ChequeFinal
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        return resp.data.json
                    }
                    return null
                })
        },

        CargarOpciones() {
            this.axios.post('/app/v1_bancos/Opciones', {
                    Opcion: 1
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.colAnticipo = parseInt(resp.data.json[0].EmpColAnticipos)
                    }
                })
        },

        LimpiarVariables() {
            this.formulario.ChequeInicial = null
            this.formulario.ChequeFinal = null
            this.formulario.Inicial = null
            this.formulario.Final = null
        },

    },
    created() {},
    mounted() {},
    beforeMount() {
        this.CargarCuentas()
        this.CargarOpciones()
    },
    watch: {
        async 'formulario.ChequeInicial'(newval) {
            if (newval && newval !== null) {
                this.infoChequeInicial = null
                this.formulario.Inicial = null
                this.infoChequeInicial = await this.BuscarCheque(newval)

                if (this.infoChequeInicial !== null) {
                    if (this.infoChequeInicial.Impresion == null) {
                        this.formulario.Inicial = this.infoChequeInicial.NombreBeneficiario + ' - Q. ' + this.NumeroComas(this.infoChequeInicial.Monto)

                        this.formEliminacionInstance.repaint()
                        setTimeout(() => {
                            this.formEliminacionInstance.getEditor('ChequeFinal').focus()
                        }, 500);
                    } else {
                        let texto = 'El '
                        if (this.ModalidadPago == 'C') {
                            texto = texto + 'cheque'
                        } else {
                            texto = texto + 'recibo'
                        }

                        texto = texto + ' ya se imprimió, no puede eliminarlo.'

                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Ya se imprimió',
                            acceptText: 'Aceptar',
                            text: texto,
                            buttonCancel: 'border',
                            accept: () => {
                                setTimeout(() => {
                                    this.formEliminacionInstance.getEditor('ChequeInicial').focus()
                                }, 500);
                            },
                        })
                        return
                    }

                } else {

                    let texto = 'El '
                    if (this.ModalidadPago == 'C') {
                        texto = texto + 'cheque'
                    } else {
                        texto = texto + 'recibo'
                    }

                    texto = texto + ' que ha ingresado no existe'

                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'No existe',
                        acceptText: 'Aceptar',
                        text: texto,
                        buttonCancel: 'border',
                        accept: () => {
                            setTimeout(() => {
                                this.formEliminacionInstance.getEditor('ChequeInicial').focus()
                            }, 500);
                        },
                    })
                    return
                }
            }
            if (this.formulario.ChequeFinal == 0 || this.formulario.ChequeFinal == null || parseInt(this.formulario.ChequeFinal) < parseInt(this.formulario.ChequeInicial)) {
                this.formulario.ChequeFinal = newval
                this.infoChequeFinal = null
                this.formulario.Final = null
            }
        },

    },
    computed: {
        formEliminacionInstance: function () {
            return this.$refs[formEliminacion].instance;
        },
    }
}
</script>

<style>
    </style>
