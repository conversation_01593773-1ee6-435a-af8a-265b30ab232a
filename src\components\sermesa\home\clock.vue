<template>
<div class="body-clock">
    
    <!-- <vx-card slot="no-body"  style="overflow:hidden;background:linear-gradient(135deg, #13f1fc 0%,#0470dc 100%);height:160px;border:2px solid white;box-shadow:none"> -->
    <div id="app">
        <div id="main">
            <div id="time">
                <span id="hours">{{hours}}</span><span id="min">{{minutes}}</span><span id="sec">{{sec}}</span> <span id="ampm">{{ampm}}</span>
            </div>
            <div id='days'>
                <div class="days" style="margin-right:10px" v-for="(day,key) in days" v-bind:class="{active:day.active}" :key="key">{{day.text}}</div>
            </div>
            <div id="fullDate">
                <span style="margin-right:10px" id="month">{{month}}</span><span style="margin-right:10px" id="date">{{date}}</span><span style="margin-right:10px" id="year">{{year}}</span>
            </div>
        </div>
    </div>
</div>
</template>

<script>
export default {
    data() {
        return {
            days: [
                {
                    text: "dom",
                    active: false
            },
                {
                    text: "lun",
                    active: false
            },
                {
                    text: "mar",
                    active: false
            },
                {
                    text: "mie",
                    active: false
            },
                {
                    text: "jue",
                    active: false
            },
                {
                    text: "vie",
                    active: false
            },
                {
                    text: "sab",
                    active: false
            }],

            months: ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'],
            ampm: '',
            hours: '',
            minutes: '',
            sec: '',
            month: '',
            date: '',
            year: ''
        }
    },

    methods: {
        startClock: function() {
            var THIS = this;

            setInterval(function() {
                let b = new Date(),
                    c = b.getHours(),
                    d = b.getMinutes(),
                    e = b.getSeconds(),
                    h = b.getDay(),
                    i = 12 <= c ? 'PM' : 'AM',
                    j = b.getMonth(),
                    k = b.getDate(),
                    l = b.getFullYear();
                THIS.$data.ampm = i;
                12 < c && (c %= 12),
                    0 == c && (c = 12),
                    9 >= e && (e = '0' + e),
                    9 >= d && (d = '0' + d),
                    THIS.$data.hours = c,
                    THIS.$data.minutes = `:${d}:`,
                    THIS.$data.sec = e,
                    THIS.$data.month = THIS.$data.months[j],
                    THIS.$data.date = k,
                    THIS.$data.year = l;
                THIS.$data.days[h].active = true;
            }, 100);
        }
    },

    created: function() {
        this.startClock();
    }
}
// Just noticed accessing localStorage is banned from codepen, so disabling saving theme to localStorage
</script>

<style scoped>
.body-clock {
    width: 100%;
    height: 100%;
    background-color:rgba(255, 255, 255, 0.6);
}

.body-clock {
    margin: 0;
    padding: 0;
    font-family: "Orbitron", sans-serif;
    font-weight: 700;
    /* background: #222222; */
    color: rgb(52, 73, 94);
    display: flex;
    align-items: center;
    justify-content: center;
}

#app {
    width: 100%;
    height: auto;
}

#app #time {
    width: 100%;
    margin: 0 auto;
    text-align: center;
    font-size: 2.5em;
    /* text-shadow: 0px 2px 25px rgba(63, 63, 63, 0.6) */
}

#app #time #ampm {
    font-size: 0.5em;
}

#app #days,
#app #fullDate {
    width: 25%;
    margin: 0 auto;
    display: flex;
    text-align: center;
    align-items: center;
    justify-content: center;
}

#app .days {
    flex: 1;
    color: rgb(127, 140, 141);
    text-align: center;
}

#app .active {
    color: rgb(0,143,190);
    /* text-shadow: 2px 2px 5px rgba(0, 0, 253, 0.5); */
}

#app #fullDate {
    margin-top: 0.25em;
    text-shadow: 0px 2px 25px rgba(144, 244, 253, 0.6);
}

#app #sec {
    display: inline-block;
    width: 70px;
}
</style>
