<template>
<vx-card title="Informe Diagnóstico">


        <vs-divider>Estudio</vs-divider>
        <vs-table :data="examenes" v-if="examenes.length>0">
            <template slot="thead">
                <vs-th>Examen</vs-th>
                <vs-th>Especialista</vs-th>
                <vs-th>Estado</vs-th>
                <vs-th>Acciones</vs-th>
            </template>
            <template slot-scope="{data}">
                <vs-tr :key="indextr" v-for="(tr, indextr) in data">
                    <vs-td :data="tr.examen_nombre">
                        [{{ tr.examen_codigo }}] {{ tr.examen_nombre }}
                    </vs-td>
                    <vs-td :data="tr.especialista_nombre">
                        <span v-if="tr.especialista_codigo">[{{ tr.especialista_codigo }}] {{ tr.especialista_nombre }}</span>
                    </vs-td>
                    <vs-td>
                        <vx-tooltip :text="(tr.congelado==false)?'Descongelado':'Congelado'" style="display:inline-block;margin-right:2px" position="bottom">
                            <vs-button :color="(tr.congelado==false)?'warning':'success'" size="small" radius type="filled" icon-pack="feather" icon="icon-lock"></vs-button>
                        </vx-tooltip>
                        <vx-tooltip :text="(tr.sms==false)?'Entrega Personalmente':'Envío SMS'" style="display:inline-block;margin-right:2px" position="bottom">
                            <vs-button :color="(tr.sms==false)?'danger':'success'" size="small" radius type="filled" icon-pack="feather" icon="icon-message-square" style="display:inline-block;margin-right:2px"></vs-button>
                        </vx-tooltip>
                    </vs-td>
                    <vs-td>
                        <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" @click="opciones(tr)"></vs-button>
                        <vs-button color="primary" icon-pack="feather" icon="icon-printer" style="display:inline-block" @click="opciones(tr)"></vs-button>
                    </vs-td>
                </vs-tr>
            </template>
        </vs-table>

        

    <vs-divider />

</vx-card>
</template>

<script>
// require styles

// import {
//     quillEditor
// } from 'vue-quill-editor'

export default {
    data() {
        return {
            popupActive2: false,
            activeItem: 0,
            bloqueoBuscar: false, //utlizado para evitar llamar multiples llamadas a los registros
            bloqueoBusqueda: false,
            bloqueoCampos: true,
            active: false,
            switch2: false,
            examenes: [],
            info: {
                opcion: {
                    show: false, //mostrar las opciones del examen_nombre
                    examen_codigo: '',
                    examen_nombre: '',
                    examen_anterior: '',
                    especialista_codigo: '',
                    especialista_nombre: '',
                    especialista_anterior: '',
                    congelado: false,
                    sms: false,
                    observaciones: '',
                    informe: '',
                    informe_cambio: false
                },
                orden_tipo: null,
                orden_numero: null,
                paciente_codigo: null,
                paciente_nombre: '',
                paciente_edad: '',
                refiere: '',
            },

            validar_descripcion: false,
            validar_problema: false,
            category_choices: [{
                text: 1,
                value: 1
            }],
            editorOption: {
                modules: {
                    toolbar: '#toolbar'
                },
                placeholder: 'Informe'
            },
        }
    },
    components: {
        // quillEditor,
    },
    methods: {

        cargar_orden() {
            if (this.bloqueoBuscar == true) return false;
            this.bloqueoBuscar = true;

            this.$vs.loading({
                background: this.backgroundLoading,
                color: this.colorLoading,
                container: "#button-with-loading",
                scale: 0.45
            })
            this.axios.post('/app/radiologia/radordenes', {
                    tipo: this.info.orden_tipo,
                    orden: this.info.orden_numero
                })
                .then(resp => {
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        this.info.paciente_codigo = resp.data.json[0].Paciente
                        this.info.paciente_nombre = resp.data.json[0].NombrePaciente
                        this.info.paciente_edad = resp.data.json[0].Edad + ' ' + resp.data.json[0].EdadMedida
                        this.info.refiere = resp.data.json[0].NombreMedico
                        this.bloqueoBusqueda = true
                        this.bloqueoCampos = false

                        this.cargar_estudios()

                    } else {
                        this.$vs.dialog({
                            color: 'danger',
                            title: 'Radiología - Error',
                            text: (resp.data.codigo < 0) ? resp.data.descripcion : 'Sin Registros',
                        })
                        return false
                    }
                })
                .catch(() => {
                    // console.log(err)
                    this.$vs.loading.close("#button-with-loading > .con-vs-loading")
                    this.bloqueoBuscar = false;
                    this.$vs.dialog({
                        color: 'danger',
                        title: 'Radiología - Error',
                        text: 'Error al obtener datos de la orden',
                    })
                })
        },

        cargar_estudios() {
            this.axios.post('/app/radiologia/radestudios', {
                    tipo: this.info.orden_tipo,
                    orden: this.info.orden_numero
                })
                .then(resp => {
                    this.examenes = []
                    resp.data.json.map(data => {
                        this.examenes.push({
                            examen_linea: data.LineaOrden,
                            examen_codigo: data.Producto,
                            examen_nombre: data.Nombre,
                            especialista_codigo: data.Radiologo,
                            especialista_nombre: data.NombreMedico,
                            observaciones: '',
                            congelado: (data.InformeLock.toLowerCase() == "s") ? true : false,
                            sms: data.EntregarPersonalmente,
                            informe: data.Detalle
                        })
                    })
                    this.$vs.loading.close("#button-with-loading > .con-vs-loading")
                    this.bloqueoBuscar = false;
                })
        },

        radiologia_congelar_descongelar() {
            if (this.info.opcion.congelado == false && !this.$validar_privilegio('CONGELAR').status) {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Privilegios',
                    text: 'No cuenta con los privilegios para CONGELAR',
                })
                this.info.opcion.congelado = !this.info.opcion.congelado
                return false
            }
            if (this.info.opcion.congelado == true && !this.$validar_privilegio('DESCONGELAR').status) {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Privilegios',
                    text: 'No cuenta con los privilegios para DESCONGELAR',
                })
                this.info.opcion.congelado = !this.info.opcion.congelado
                return false
            }
            
            this.axios.post('/app/radiologia/' + ((this.info.opcion.congelado == false) ? 'RadCongelaEst' : 'RadDescongelaEst'), {
                    tipo: this.info.orden_tipo,
                    orden: this.info.orden_numero,
                    LineaOrden: this.info.opcion.linea_orden,
                    NombreEstudio: '.',
                    Radiologo: 0
                })
                .then(() => {
                    
                    this.$vs.notify({
                        color: 'success',
                        title: 'Radiología',
                        text: 'Se ha ' + ((this.info.opcion.congelado) ? 'Congelado' : 'Descongelado') + ' exitosamente',
                    })
                    this.bloqueoCampos = this.info.opcion.congelado
                    this.cargar_estudios()
                })
                .then(err => {
                    console.warn(err)
                })
                .catch(() => {
                    this.info.opcion.congelado = !this.info.opcion.congelado
                    return false
                })
        },

        radiologia_mensaje() {
            this.axios.post('/app/radiologia/'+((this.info.opcion.sms)?'RadEnviaMsg':'RadNoEnviaMsg'), {
                    Tipo: this.info.orden_tipo,
                    Orden: this.info.orden_numero,
                    LineaOrden:this.info.opcion.linea_orden
                })
                .then(() => {
                    this.cargar_estudios()
                     this.$vs.notify({
                        color: 'success',
                        title: 'Radiología',
                        text: 'Envío de SMS ' + ((this.info.opcion.sms) ? 'Activo' : 'Desactivado'),
                    })
                })
        },

        buscar_especialista() {
            this.$refs.buscar_especialista.iniciar((data) => {
                if (data != null) {
                    this.info.opcion.especialista_codigo = data.Codigo
                    this.info.opcion.especialista_nombre = data.NombreRad
                }
            })
        },

        limpiar_orden() {
            this.examenes = []
            this.info.opcion.congelar = false
            this.info.opcion.sms = false
            this.info.orden_tipo = null
            this.info.orden_numero = null
            this.info.paciente_codigo = null
            this.info.paciente_nombre = ''
            this.info.paciente_edad = ''
            this.info.examen_codigo = ''
            this.info.examen_nombre = ''
            this.info.examen_linea = ''
            this.info.especialista_codigo = ''
            this.info.especialista_nombre = ''
            this.info.refiere = ''
            this.bloqueoBusqueda = false
            this.bloqueoCampos = true
            this.info.informe = ''
        },

        opciones(op) {
            this.info.opcion.show = true
            this.info.opcion.congelado = op.congelado
            this.info.opcion.sms = op.sms
            this.info.opcion.examen_codigo = op.examen_codigo
            this.info.opcion.examen_nombre = op.examen_nombre.trim()
            this.info.opcion.especialista_codigo = op.especialista_codigo
            this.info.opcion.especialista_nombre = op.especialista_nombre
            this.info.opcion.observaciones = op.observaciones
            this.info.opcion.informe = op.informe
            this.info.opcion.linea_orden = op.examen_linea
            this.bloqueoCampos = op.congelado

        },

        guardar_informe() {
            this.axios.post('/app/radiologia/radactualizaestudio', {
                    tipo: this.info.orden_tipo,
                    orden: this.info.orden_numero,
                    LineaOrden: this.info.opcion.linea_orden,
                    NombreEstudio: this.info.opcion.examen_nombre,
                    CodRadiologo: this.info.opcion.especialista_codigo,
                    Informe: this.info.opcion.informe,
                    Bitacora: [{
                        Empresa:'MED',
                        Serie:this.info.orden_tipo,
                        Documento: this.info.orden_numero,
                        Linea: this.info.opcion.linea_orden,
                        Tipo: "Radiologia",
                        Descripciones: [
                            'Modificación de Nombre Informe de Electrocardiograma > Electrocardiograma 123',
                            'Modificación de Radiologo',
                            'Modificación de Informe de Radiología'
                        ]
                    }]
                })
                .then(() => {
                    this.cargar_estudios()
                    this.$vs.notify({
                        color: 'success',
                        title: 'Radiología',
                        text: 'Información guardada exitosamente',
                    })
                    this.info.opcion.show = false;
                })
                .catch(() => {
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Radiología',
                        text: 'Error al guardar la información',
                    })
                })
        }

    },
    created() {

    }
}
</script>

<style>
.button {
    margin: 0px !important;
}

.btnx {
    margin-left: 10px !important;
    background: red;
    border-radius: 5px 0px 0px 5px;
}

.btn-drop {
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: red;

}

.menu-funcionalidad {
    /* padding-top:0 !important; */
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    padding: 15px;
    display: inline;
    float: right;
    border-radius: 10px;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 10px;
}

.menu-funcionalidad td {
    text-align: center;
    padding-right: 10px;
}

.vs-switch {
    height: 30px;
    width: 60px !important;
}

.vs-circle-switch {
    height: 25px;
    width: 25px;
}

.vs-switch--icon {
    font-size: 15px
}

.vs-switch.vs-switch-active .vs-switch--circle {
    margin-left: calc(100% - 30px);
}
</style>
