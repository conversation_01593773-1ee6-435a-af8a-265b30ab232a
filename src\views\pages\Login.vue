<template>
    <div class="h-screen flex w-full bg-img vx-row no-gutter items-center justify-center" id="page-login" :class="[(reanudar)?'reanudar':null]">
        <div class="vx-col sm:w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2 sm:m-0 m-4">
            <vx-card>
                <div slot="no-body" class="full-page-bg-color">
                    <div v-show="false">{{extra}}</div>
                    <div style="background-color:rgb(255, 255, 255)" class="vx-row no-gutter justify-center items-center">
    
                        <div class="vx-col hidden lg:block lg:w-1/2">
                            <img src="@/assets/images/pages/login.svg" height="200px" alt="login" class="mx-auto">
                        </div>
    
                        <div v-if="Id_Ventana==1" class="vx-col sm:w-full md:w-full lg:w-1/2 d-theme-dark-bg">
                            <div class="p-8 login-tabs-container">
    
                                <div class="vx-card__title mb-8" style="text-align:center">
    
                                    <span style="">
                                        <b>
                                            Iniciar Sesión
                                        </b>
                                    </span>
                                </div>
                                <div>
                                    <form class="" v-on:submit.prevent="verify()">
                                        <div>
                                            <vs-input v-if="!reanudar" name="email" icon-no-border icon="icon icon-user" icon-pack="feather" label-placeholder="Usuario (Corporativo)" v-model="usuario" class="w-full" />
                                            <vs-input v-else icon-no-border icon="icon icon-user" icon-pack="feather" label-placeholder="Corporativo" :value="sesion.corporativo" class="w-full" disabled />
                                        </div>
                                        <div>
                                            <vs-input type="password" name="password" icon-no-border icon="icon icon-lock" icon-pack="feather" label-placeholder="Contraseña" v-model="password" class="w-full mt-6" />
                                        </div>
    
                                        <br>
                                        <div class="w-full md:w-full lg:w-full xl:w-full">
                                            <span>
                                                <button type="submit" name="button" style="display:none"></button>
                                                <vs-button class="w-full" v-on:click="verify()">Iniciar Sesión</vs-button>
                                            </span>
                                        </div>
                                        <div class="w-full md:w-full lg:w-full xl:w-full">
    
                                            <span>
                                                <vs-button v-if="reanudar" class="w-full" color="danger" type="line" v-on:click="cerrar_sesion()">Cerrar Sesión</vs-button>
                                            </span>
                                            <br>
                                            <vs-button v-if="!reanudar" class="w-full" color="success" type="line" v-on:click="Recuperar_contraseña()">
                                                Recuperar contraseña
                                            </vs-button>
                                        </div>
    
                                    </form>
                                </div>
                            </div>
                        </div>
                        <!--------------- ENVIAR CORREO para recuperar contraseña--------->
                        <div v-if="Id_Ventana==2" class="vx-col sm:w-full md:w-full lg:w-1/2 d-theme-dark-bg" >
                            <div class="p-8 login-tabs-container">
                                <div class="vx-card__title mb-8" style="text-align:center">
                                    <br>
                                    <span style="color:#373C74">
                                        <b>Recuperar Contraseña</b>
                                    </span>
                                </div>
    
                                <div>
                                    <form v-on:submit.prevent="RecuperarClave()">
                                        <div>
                                            <vs-input icon-no-border icon="icon icon-user" icon-pack="feather" label-placeholder="Corporativo" v-model="corporativo_recuperacion" class="w-full" />
                                        </div>
                                        <br>
                                        <div class="flex flex-wrap">
                                            <div class="w-full">
                                                <vs-button class="w-full" color="success" @click="RecuperarClave()">
                                                    <transition name="custom-classes-transition" enter-active-class="animated tada" leave-active-class="animated bounceOutRight">
                                                        <font-awesome-icon v-if="animation==0" :icon="['fas', 'envelope']" class="mr-2 text-lg" />
                                                    </transition>
                                                    Verificar Corporativo...
                                                </vs-button>
                                            </div>
                                        </div>
                                        <br>
                                        <div class="flex flex-wrap">
                                            <div class="w-full">
                                                <vs-button class="w-full" color="danger" type="line" icon-pack="feather" icon="icon-x" @click=" Id_Ventana = 1"> Cancelar</vs-button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
    
                        <!--------------- cambiar clave --------->
                        <!-- v-show por el ref -->
                        <div v-show="Id_Ventana==3" class="vx-col sm:w-full md:w-full lg:w-1/2 d-theme-dark-bg">
                            <div class="p-8 login-tabs-container">
                                <ChangePassword ref="refChangePassword" 
                                    :corporativo="corporativo_recuperacion" 
                                    @password-changed="onPasswordChanged" 
                                    @cancel="onPasswordChangedCancel" 
                                    @show-token-button="(e)=> showTokenButton=e" 
                                    :skipCallback="skipCallback" 
                                    :showTokenButton="showTokenButton" 
                                    :mode="modoCambioClave" 
                                />
                            </div>
                        </div>
    
                    </div>
                </div>
    
            </vx-card>
        </div>
    
    </div>
</template>
    
    <script>
    import ChangePassword from './AUTH/ChangePassword.vue'

    export default {
        name: 'login',
        components: {
            ChangePassword
        },
        data() {
            return {
                corporativo_recuperacion: '',
                correo_recuperacion: '',

                Id_Ventana: 1, /// 1 = Mostrar inicio sesion / 2 = Enviar correo / 3 = Nueva contraseña
                Estado_VentanaEmergente: false,
                usuario: "",
                password: "",
                checkbox_remember_me: false,
                bloqueoInstancia: true,
                bloqueoPass: false,
                bloqueoText: null,
    
                instancia: null,
                Estado_VentanaAsignacion: false,
                token: '',
                contrasenia: '',
                contrasenia_conf: '',
                mensajeActualizacionClave: '',
                animation: 0,
                skipCallback: null,
                showTokenButton: true,
                modoCambioClave: 'token'
            }
        },
    
        computed: {
            global() {
                return this.$store.state.global
            },
            sesion() {
                return this.$store.state.sesion
            },
            extra() {
                const e = this.$store.state.extra
                if (e.loading) {
                    this.$vs.loading()
                } else {
                    this.$vs.loading.close()
                }
                if (e.notificacion) {
                    this.$vs.notify(e.notificacion)
                    this.$store.dispatch('notify', null)
                }
                return e
            },
        },
        props: {
            reanudar: {
                default: false
            }
        },
        methods: {
            onPasswordChanged(e) {
                this.usuario = e.Corporativo
                this.password = e.NewPassword
                this.verify()
            },
            onPasswordChangedCancel() {
                this.Id_Ventana = 1   
            },
            Recuperar_contraseña() {
                this.corporativo_recuperacion = "";
                this.Id_Ventana = 2;
            },
            /**
             * Cambio de instancia desde el middleware
             */
            obtenerInstancia() {
                this.axios.get('/Instancia')
                    .then(resp => {
                        if (resp.data.Token) this.instancia_token(resp.data.Token)
                        if (resp.data.Instancias) this.instancia_listado(resp.data.Instancias)
                        if (resp.data.Instancia) this.instancia_cambio(resp.data.Instancia)
                    })
            },
            verify() {
    
                //Edvin 2021 Abril 05 - Validacion si es diferente a vacio el campo de usuario y clave
                if ((this.usuario == "" && this.sesion.corporativo == "") || this.password == "") {
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Sesión',
                        text: ' Ingresar credenciales.',
                    })
                    return;
                }
    
                const url = this.$store.state.global.url
                const token = this.$store.state.global.tokenDefault
                const config = {
                    headers: {
                        Authorization: 'Bearer ' + token
                    }
                };
                this.axios.post(url + 'app/usuario/verificar', { //DEBE SER GET EN LUGAR DE POST
                        Corporativo: (this.reanudar) ? this.sesion.corporativo : this.usuario,
                        Password: this.password,
                        num_operacion: this.Id_Ventana
                    }, config)
                    .then(resp => {
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Sesión - Error',
                                text: 'Usuario o contraseña incorrecta',
                            })
                            this.password = null
                        } else {
                            if (resp.data.alertaclave) {
                                this.Id_Ventana = 3
                                this.corporativo_recuperacion = this.usuario
                                this.mensajeActualizacionClave = resp.data.alertaclave
                                this.cambiarclave = resp.data.cambiarclave //<--importante esta variable hace que se aobligatior cambiar la clave
                                
                                this.showTokenButton = true
                                this.modoCambioClave = resp.data.diasexpira == null ? 'token':'tokenFirstPassword'
                                this.skipCallback = resp.data.cambiarclave ? null: () => {
                                    this.grabarSesion(resp.data)
                                }

                                return
                            }
                            this.grabarSesion(resp.data)
                        }
                    })
                    .catch(() => {
                        this.password = null
                    })
            },

            //coloca los datos de sesion en el store y pide la sucursal al usuario, si aplica
            grabarSesion(dat){
                this.$store.dispatch('sesionGuardar', dat)           
                // reanudar
                if (!this.reanudar) {
                    window.location.href = "/";
                } else {
                    if (this.sesion.sesion_sucursal) {
                        this.axios.post('/app/usuario/seleccion_sucursal', {
                                Sucursal: this.sesion.sesion_sucursal,
                                Tipo: 2
                            })
                            .then(resp => {
                                //guardando la informacion en el localstorage del cliente
                                this.$store.dispatch('sesionGuardar', resp.data)
                                this.validar_privilegios() // verificar nuevamente los privilegios
                            })
                    } else {
                        this.validar_privilegios()
                    }
                }
            },
            cerrar_sesion() {
                this.$store.dispatch('sesionEliminar')
                location.replace("/pages/login");
            },
    
            /**
             * Instancias
             */
            instancia_token(token) {
                this.$store.dispatch('instanciaToken', token)
            },
    
            instancia_listado(instancias) {
                this.$store.dispatch('instanciaListado', instancias)
            },
    
            instancia_cambio(instancia) {
                this.$store.dispatch('instanciaCambio', instancia)
            },
    
            desbloqueoInstancia() {
                if (btoa(this.bloqueoText) == 'V2x0MTI1MC4=') {
                    this.bloqueoInstancia = false
                    this.bloqueoPass = false
                }
                this.bloqueoText = ''
            },
    
            // validando privilegios
            validar_privilegios() {
                this.axios.post('/app/usuario/obtener_privilegios', {})
                    .then(resp => {
                        this.$store.dispatch('sesionValidar') //valida que se ha cargado la informacion
                        if (resp.data.tipo == "Empresa") {
                            let temp_emp = ''
                            this.seleccionEmpresa.mostrar = true;
                            resp.data.json.map(data => {
                                if (temp_emp != data.Codigo) this.seleccionEmpresa.empresas.push({
                                    Empresa: data.Codigo,
                                    Nombre: data.Nombre,
                                    Sub: []
                                })
    
                                this.seleccionEmpresa.empresas[this.seleccionEmpresa.empresas.length - 1].Sub.push(data)
                                temp_emp = data.Codigo
                            })
    
                        } else {
                            this.$store.dispatch('funcionalidadListado', resp.data.json)
                            this.seleccionEmpresa.mostrar = false;
                        }
                    })
            },

            RecuperarClave() {
                this.animation = -1
                setTimeout(() => {
                    this.animation = 1 //animacion del icono :)
                    this.$refs.refChangePassword.EnviarToken().then(()=>{
                        this.Id_Ventana = 3
                    }).catch(()=> this.animation = 0)
                    
                }, 500)
            }
        },
        mounted() {
    
            this.obtenerInstancia()
        },
    
    }
    </script>
    
    <style lang="scss">
    #page-login {
        position: fixed;
        z-index: 99999;
        top: 0;
        left: 0;
    
        .social-login-buttons {
            .bg-facebook {
                background-color: #1551b1;
            }
    
            .bg-twitter {
                background-color: #00aaff;
            }
    
            .bg-google {
                background-color: #4285F4;
            }
    
            .bg-github {
                background-color: #333;
            }
        }
    }
    
    #page-login.reanudar {
        background-color: rgba(0, 0, 0, 0.6);
    }
    </style>
    <style>
    @import '../../assets/css/<EMAIL>';
    .bounce-enter-active {
        animation: bounce-in .5s;
    }

    .bounce-leave-active {
        animation: bounce-in .5s reverse;
    }

    @keyframes bounce-in {
        0% {
            transform: scale(0);
        }

        50% {
            transform: scale(1.5);
        }

        100% {
            transform: scale(1);
        }
    }
    </style>
    