<template>
<vx-card title="Mercadeo">

    <div class="content content-pagex">
        <form>

            <vx-card title="Cam biar fondo de pantalla sighos">

                <div   class="w-full md:w-1/2 lg:w-1/2 sm:w-1/2">
                    <FondoPantalla :v-show="false" api='/app/externo/ArchivosVariables_DB' @metodo_emit="ConsultaHorarios" :api_parametros="{ idControl: 'WEB_FONDO_SIGHOS' }"></FondoPantalla>
                </div>
                <!-- <div v-else>
                    <div class="tamaño">
                    <p>
                        Usuario no tiene asignado el permiso.
                    </p>
                </div>                    
                </div> -->
                <div class="tamaño">
                    <p>
                        Tamaño de la imagen 2560 * 1400 píxeles.
                    </p>
                </div>
            </vx-card>

        </form>
    </div>

</vx-card>
</template>

<script>
import FondoPantalla from '/src/components/sermesa/modules/archivos/SMHorario.vue'


export default {
    components: {
        // ValidarHuella: defineAsyncComponent(() => import('@/components/validador-huella/ValidarHuella.vue')),
        FondoPantalla
    },
    data() {
        return {

            Permisos: {
                    CAMBIAR_FONDO: false
            }
        };
    },
    mounted() {
        this.$validar_funcionalidad('/MERCADEO/MER001', 'CAMBIAR_FONDO', (d) => {
                this.Permisos.CAMBIAR_FONDO = d.status;
    
            })
    },
    
    methods: {
    },

};
</script>

<style scoped>
.tamaño {
    width: 18%;
    height: 40px;
    border: 2px solid #F4D03F;
    border-radius: 5px;
    padding: 10px;
    font-size: 12px;
    color: black;
    text-align: justify;
    background-color: #F9E79F;

}
</style>
