<template>
<vx-card style="display: none">
    <!--------------Inicio Vista Previa información _---------->

    <!---------AUTORIZACIOON COTIZACION------------------------->
    <vs-popup align="center" classContent="popup-example" title="Autorizaciones Orden Compra" :active.sync="Estado_VentanaNueva_Orden">
        <form>
            <div class="flex flex-wrap">

                <!----- Responsables 1 ---->

                <div style="margin-top: 30px" class="center_2">
                    <ValidationProvider name="Responsable" rules="required" v-slot="{ errors }" class="required">
                        <label style="font-size: 12px">Responsable No. 1</label>
                        <multiselect v-model="cb_busqueda_corporativo" :options="Lista_responsable" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Corporativo_seleccionado" placeholder="Búsqueda responsable" track-by="Corporativo" name="id_responsable" @input="onChangeResponsable" :disabled="Deshabilitar_campos1" :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null">
                            <span slot="noOptions">Lista no disponible.</span>
                        </multiselect>
                    </ValidationProvider>
                </div>
                <div class="center_2">
                    <td class="font-semibold">{{ responsable.corporativo }}</td>
                </div>
                <div class="center_2">
                    <td class="font-semibold">{{ responsable.nombre }}</td>
                </div>
                <div class="center_2">
                    <td class="font-semibold">{{ responsable.correo }}</td>
                </div>

                <div style="margin-bottom: 100px" class="w-full">
                    <vs-radio v-model="idTipoSeleccionadoS" vs-name="idTipoSeleccionadoS" vs-value="S"> Servicio</vs-radio>
                </div>

                <!--- Responsables 2 ---->

                <div style="margin-top: 1px" class="center_2" v-show="emprasaProducto">
                    <ValidationProvider name="Responsable" rules="required" v-slot="{ errors }" class="required">
                        <label style="font-size: 12px">Responsable No. 2</label>
                        <multiselect v-model="cb_busqueda_corporativo_2" :options="Lista_responsable" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Corporativo_seleccionado_2" placeholder="Búsqueda responsable" track-by="Corporativo" name="id_responsable" @input="onChangeResponsable_2" :disabled="Deshabilitar_campos2" :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null">
                            <span slot="noOptions">Lista no disponible.</span>
                        </multiselect>
                    </ValidationProvider>
                </div>
                <div class="center_2" v-show="emprasaProducto">
                    <td class="font-semibold">{{ responsable_2.corporativo }}</td>
                </div>
                <div class="center_2" v-show="emprasaProducto">
                    <td class="font-semibold">{{ responsable_2.nombre }}</td>
                </div>
                <div class="center_2" v-show="emprasaProducto">
                    <td class="font-semibold">{{ responsable_2.correo }}</td>
                </div>

                <div class="w-full" style="margin-bottom: 100px" v-show="emprasaProducto">
                    <vs-radio v-model="idTipoSeleccionadoP" vs-name="idTipoSeleccionadoP" vs-value="P"> Bien</vs-radio>
                </div>
            </div>

            <vs-divider></vs-divider>
            <vs-button :disabled="Deshabilitar_campos" color="primary" style="float: right" type="filled" icon-pack="feather" icon="icon-save" @click="Guardar_Asociacion"> Grabar</vs-button>
            <vs-button color="danger" style="float: right" type="border" icon-pack="feather" icon="icon-x" @click="Estado_VentanaNueva_Orden = false">
                Cancelar</vs-button>
        </form>
    </vs-popup>

    <!--------------Fin Vista Previa información _---------->

</vx-card>
</template>

<script>
/**
 * @General
 * Modulo registra los responsables de las ordenes de compra
 */

import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";

export default {
    name: "ComponenteNuevaOrdenCompra",
    data() {
        return {

            Deshabilitar_campos: false,
            Deshabilitar_campos1: false,
            Deshabilitar_campos2: false,
            Estado_VentanaNueva_Orden: true,
            Nombre_depto: "",
            Codigo_depto: "",

            id_documento_selec: "3",
            id_numero_doc: "1",

            cb_departamentos: "",
            Lista_departamentos: [],
            id_departamento_selec: "0",

            cb_busqueda_corporativo: "",
            Lista_responsable: [],
            id_responsable_seleccionado: "",

            cb_busqueda_corporativo_2: "",
            id_responsable_seleccionado_2: "",

            Lista_autorizaciones: [],
            responsable: {
                corporativo: "",
                nombre: "",
                correo: "",
                enviar: false,
                deshabilitado: false
            },
            responsable_2: {
                corporativo: "",
                nombre: "",
                correo: "",
                enviar: false,
                deshabilitado: false
            },
            res_variables: false,
            idTipoSeleccionadoS: 'S',
            idTipoSeleccionadoP: 'P',
            emprasaProducto: false,
            bandera1: 0,
            bandera2: 0

        };
    },
    components: {
        Multiselect,
    },
    props: {
        callback: {
            default: null,
        },
        cerrar: {
            default: null,
        },
    },
    watch: {
        Estado_VentanaNueva_Orden() {
            this.cerrar();
        },

    },
    mounted() {
        this.Valida_EmpresaIngresoOC();

    },
    methods: {
        Valida_EmpresaIngresoOC() {
            this.emprasaProducto = false
            const url = this.$store.state.global.url;
            this.axios.post(url + 'app/v1_OrdenCompra/Valida_EmpresaOC', {
                    productoClase: 'P',
                    iddocumento: 3
                })
                .then((resp) => {
                    if (resp.data.resultado != 0) {
                        this.emprasaProducto = false
                        this.Consultar_Responsable();
                        this.Consultar_Autorizaciones();
                    } else {
                        this.emprasaProducto = true
                        this.Consultar_Responsable();
                        this.Consultar_Autorizaciones();
                    }
                })

        },
        Departamento_seleccionado({
            NOMBRE
        }) {
            return ` ${NOMBRE} `;
        },
        Corporativo_seleccionado_2({
            Corporativo,
            nombre
        }) {
            return `${Corporativo} - ${nombre} `;
        },
        onChangeResponsable_2(value) {

            if (value !== null && value.length !== 0) {
                this.id_responsable_seleccionado_2 = value.Corporativo;
                this.responsable_2.corporativo = value.Corporativo;
                this.responsable_2.nombre = value.nombre;
                this.responsable_2.correo = value.correo;
                this.responsable_2.enviar = true;
                this.Deshabilitar_campos = false
            } else {
                this.id_responsable_seleccionado_2 = "";
                this.responsable_2.corporativo = "";
                this.responsable_2.nombre = "";
                this.responsable_2.correo = "";
                this.responsable_2.enviar = false;
                this.Deshabilitar_campos = true
            }
        },
        Corporativo_seleccionado({
            Corporativo,
            nombre
        }) {
            return `${Corporativo} - ${nombre} `;
        },
        onChangeResponsable(value) {

            if (value !== null && value.length !== 0) {
                this.id_responsable_seleccionado = value.Corporativo;
                this.responsable.corporativo = value.Corporativo;
                this.responsable.nombre = value.nombre;
                this.responsable.correo = value.correo;
                this.responsable.enviar = true;
                this.Deshabilitar_campos = false 
            } else {
                this.id_responsable_seleccionado = "";
                this.responsable.corporativo = "";
                this.responsable.nombre = "";
                this.responsable.enviar = false;
                this.Deshabilitar_campos = true
            }
        },
        Consultar_Responsable(Tipo_Busqueda = "", Busqueda = "") {

            const url = this.$store.state.global.url;

            this.axios
                .post(url + "app/v1_OrdenCompra/consulta_corporativo", {
                    TIPO_BUSQUEDA: Tipo_Busqueda,
                    BUSQUEDA: Busqueda,
                })
                .then((resp) => {

                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: "#B71C1C",
                            title: "Orden de compra",
                            text: resp.data.mensaje,
                        });
                        //Limpia la tabla si no existen registros
                        this.Lista_responsable = "";
                    } else {
                        this.Lista_responsable = resp.data.json;
                    }
                })
                .catch(() => {

                });

        },

        Guardar_Asociacion() {
            const sesion = this.$store.state.sesion;
            this.Corporativo_Sesion = sesion.corporativo
            this.res_variables = false;
            if (this.responsable.enviar === true) {
                this.res_variables = this.Validacion_Campos('ID', 'Responsable', this.id_responsable_seleccionado, true, 0);
                if (this.res_variables) {

                    const url = this.$store.state.global.url;
                    this.axios.post(url + 'app/v1_OrdenCompra/autorizacion_doc', {
                            empresa: sesion.sesion_empresa,
                            codigo: 0,
                            id_documento: '3',
                            id_responsable: this.id_responsable_seleccionado,
                            id_departamento: 0,
                            num_documento: '1',
                            operacion: 'N',
                            corporativo: this.Corporativo_Sesion,
                            activo: 'S',
                            tipo: this.idTipoSeleccionadoS

                        })
                        .then(resp => {

                            if (resp.data.codigo !== 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Autorización Documentos',
                                    text: resp.data.mensaje,
                                })

                            } else {
                                this.$root.$refs.A.Consultar_Autorizaciones();
                                if (this.responsable_2.enviar === false) {
                                    this.Estado_VentanaNueva_Orden = false;
                                    
                                }

                            }
                        })
                        .catch(() => {

                        })   
                }
            }

            if (this.emprasaProducto === true) {
                if (this.responsable_2.enviar === true) {
                    this.res_variables = this.Validacion_Campos('ID', 'Responsable', this.id_responsable_seleccionado_2, true, 0);
                    if (this.res_variables) {

                        const url = this.$store.state.global.url;
                        this.axios.post(url + 'app/v1_OrdenCompra/autorizacion_doc', {
                                empresa: sesion.sesion_empresa,
                                codigo: 0,
                                id_documento: '3',
                                id_responsable: this.id_responsable_seleccionado_2,
                                id_departamento: 0,
                                num_documento: '1',
                                operacion: 'N',
                                corporativo: this.Corporativo_Sesion,
                                activo: 'P',
                                tipo: this.idTipoSeleccionadoP
                            })
                            .then(resp => {

                                if (resp.data.codigo !== 0) {
                                    this.$vs.notify({
                                        color: '#B71C1C',
                                        title: 'Autorización Documentos',
                                        text: resp.data.mensaje,
                                    })

                                } else {
                                    this.$root.$refs.A.Consultar_Autorizaciones();
                                    this.Estado_VentanaNueva_Orden = false;
                                }
                            })
                            .catch(() => {
                                
                            })
                           
                    }
                }
                
            }
           
        },
        Validacion_Campos(Tipo, Nombre, valor, obligatorio, cant_maxima) {
            /**
             * @General
             * Tipo : N = Númerio, Nombre = Descripción del campo, Valor = A evaluar, Obligatorio= Verdadero o falso,cant_maxima  = Aplica unicamente para un string y define la cantidad de caracteres 
             * Devuelve un mensaje al usuario si las validaciones no se cumple y aborta, de lo contrario devuelve verdadero
             */

            if (Tipo == 'ID') {
                if (valor <= 0) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Autorización Documentos',
                        text: 'Campo \'' + Nombre + '\' es obligatorio.',
                    });
                    return false;
                } else {
                    return true;
                }
            } else if (Tipo == 'N') {

                if (valor < 0 || valor === "") {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Autorización Documentos',
                        text: 'Campo \'' + Nombre + '\' es obligatorio.',
                    });
                    return false;
                } else {
                    return true;
                }
            } else {
                if (valor == '' && obligatorio == true) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Autorización Documentos',
                        text: 'Campo \'' + Nombre + '\' es obligatorio.',
                    });
                    return false;
                } else {

                    if (cant_maxima < valor.length) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Autorización Documentos',
                            text: 'Campo ' + Nombre + ' no debe superar los ' + cant_maxima + ' carácteres.',
                        });
                        return false;
                    } else {
                        return true;
                    }

                }
            }
        },
        Consultar_Autorizaciones() {
            const url = this.$store.state.global.url
            const sesion = this.$store.state.sesion;

            this.axios.post(url + 'app/v1_OrdenCompra/consulta_listado_autorizaciones', {
                    empresa: sesion.sesion_empresa,
                    id_documento: '3',
                    num_documento: '1',
                    tipo_consulta: 'E',
                    id_departamento: this.id_departamento_selec,
                    tipo: 'S'

                })
                .then(resp => {

                    if (resp.data.codigo != 0) {
                        if (resp.data.json.length == 0) {
                            this.Deshabilitar_campos1 = false;
                            this.bandera1 = (this.bandera1 + 1);
                            
                        }
                        //Limpia la tabla si no existen registros
                        this.Lista_autorizaciones = "";
                        this.responsable.corporativo = '';
                        this.responsable.nombre = '';
                        this.responsable.correo = '';
                        this.responsable.enviar = true;
                        this.cb_busqueda_corporativo = '';
                        this.responsable.deshabilitado = false;
                    } else {
                        this.Lista_autorizaciones = resp.data.json;
                        if (resp.data.json.length == 1) {
                            this.Deshabilitar_campos1 = true;
                            this.bandera1 = 0
                            
                        }
                        this.Lista_autorizaciones = []
                        resp.data.json.map(data => {
                            this.Lista_autorizaciones.push({
                                ...data
                            })
                        })

                        this.responsable.corporativo = this.Lista_autorizaciones.find(obj => obj.ID_DOCUMENTO === this.id_documento_selec).ID_RESPONSABLE;
                        this.responsable.nombre = this.Lista_autorizaciones.find(obj => obj.ID_DOCUMENTO === this.id_documento_selec).NOMBRE;
                        this.responsable.correo = this.Lista_autorizaciones.find(obj => obj.ID_DOCUMENTO === this.id_documento_selec).CORREO;
                        this.responsable.enviar = false;
                        this.responsable.deshabilitado = true;
                        this.cb_busqueda_corporativo = {
                            Corporativo: this.responsable.corporativo,
                            nombre: this.responsable.nombre
                        };
                        this.idTipoSeleccionadoS = 'S'

                    }
                    if (this.bandera2 == "1" || this.bandera1 == "1") {
                            this.Deshabilitar_campos = false
                        } else {
                            this.Deshabilitar_campos = true
                        }
                })
                .catch(() => {

                })

            if (this.emprasaProducto === true) {
                this.axios.post(url + 'app/v1_OrdenCompra/consulta_listado_autorizaciones', {
                        empresa: sesion.sesion_empresa,
                        id_documento: '3',
                        num_documento: '1',
                        tipo_consulta: 'E',
                        id_departamento: this.id_departamento_selec,
                        tipo: 'P'
                    })
                    .then(resp => {

                        if (resp.data.codigo != 0) {

                            if (resp.data.json.length == 0) {
                                this.Deshabilitar_campos2 = false;
                                this.bandera2 = (this.bandera2 + 1);
                                
                            }
                            //Limpia la tabla si no existen registros
                            this.Lista_autorizaciones = "";
                            this.responsable_2.corporativo = '';
                            this.responsable_2.nombre = '';
                            this.responsable_2.correo = '';
                            this.responsable_2.enviar = true;
                            this.responsable_2.deshabilitado = false;
                            this.cb_busqueda_corporativo_2 = '';
                        } else 
                        {
                            this.Lista_autorizaciones = resp.data.json;

                            if (resp.data.json.length == 1) {
                                this.Deshabilitar_campos2 = true;
                                this.bandera2 = 0
                               
                            }

                            this.Lista_autorizaciones = []
                            resp.data.json.map(data => {
                                this.Lista_autorizaciones.push({
                                    ...data
                                })
                            })
                            
                            this.responsable_2.corporativo = this.Lista_autorizaciones.find(obj => obj.ID_DOCUMENTO === this.id_documento_selec).ID_RESPONSABLE;
                            this.responsable_2.nombre = this.Lista_autorizaciones.find(obj => obj.ID_DOCUMENTO === this.id_documento_selec).NOMBRE;
                            this.responsable_2.correo = this.Lista_autorizaciones.find(obj => obj.ID_DOCUMENTO === this.id_documento_selec).CORREO;
                            this.responsable_2.enviar = false;
                            this.responsable_2.deshabilitado = true;
                            this.cb_busqueda_corporativo_2 = {
                                Corporativo: this.responsable_2.corporativo,
                                nombre: this.responsable_2.nombre
                            };
                            this.idTipoSeleccionadoP = 'P'

                        }
                        if (this.bandera2 == "1" || this.bandera1 == "1") {
                            this.Deshabilitar_campos = false
                        } else {
                            this.Deshabilitar_campos = true
                        }
                    })

            }

        },

    },
};
</script>

<style scoped>
.center {
    margin: auto;
    width: 60%;
    padding: 10px;
}

.center_2 {
    margin: auto;
    width: 60%;
    padding: 3px;
}
</style>
