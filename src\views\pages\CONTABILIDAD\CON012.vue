<template>
<vx-card title="Contabilidad General">
    <div class="content content-pagex">
        <vs-divider class="label-size"> INICIALIZAR SALDOS </vs-divider>

        <div class="flex flex-wrap">
            <div class="padre xs:w-full md:w-1/3 lg:w-2/6 xl:w-2/6"></div>
            <div class="padre xs:w-full md:w-1/3 lg:w-2/6 xl:w-2/6" style="padding:10px 10px">
                <label class="label-size">Períodos</label>
                <ValidationProvider name="Periodos">
                    <multiselect v-model="cbPeriodos" :options="ListaPeriodos" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="PeriodoSeleccionado" placeholder="Seleccionar Período" @input="onChangePeriodos">
                        <span slot="noOptions">Lista no disponible.</span>
                    </multiselect>
                </ValidationProvider>

                <div class="xs:w-full md:w-1/3 lg:w-1/5 xl:w-1/5" align="right">
                    <div class="flex flex-wrap" style="padding:20px 4px">
                        <div class="label-size">
                            <vs-button color="success" class="label-size" type="filled" @click="InicializaSaldos()">Inicializar </vs-button>
                        </div>
                    </div>

                </div>

                <div class="cuadro-i" style="padding:30px 30px" v-show="Ver_ayuda">
                    <label class="label-i">Al presionar Inicializar, los saldos del período indicado quedarán ajustados al saldo final del período anterior, si no hay movimiento en el período anterior, usa el saldo inicial de la cuenta. Las partidas mayorizadas quedarán pendientes.</label>
                </div>

            </div>
        </div>

        <vs-divider></vs-divider>
        <br>
    </div>
</vx-card>
</template>

<script>
import Multiselect from 'vue-multiselect'

import "vue-multiselect/dist/vue-multiselect.min.css"

export default {
    components: {
        Multiselect,

    },
    data() {
        return {
            cbPeriodos: '',
            ListaPeriodos: [],
            CodigoPeriodo: '',
            DescPeriodo: '',
            Ver_ayuda: true
        }
    },
    mounted() {
        this.ConsultaPeriodo()
        this.ConsultaPeridoActual()

    },

    methods: {
        //PERIODOS
        ConsultaPeriodo() {
            this.axios.post('/app/v1_contabilidad_general/ConsultaPeriodo', {
                    Opcion: "C",
                    SubOpcion: "6",
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaPeriodos = resp.data.json;
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido cargar los periodos, favor validar.',
                        })
                    }
                })
                .catch(() => {})
        },
        ConsultaPeridoActual() {
            this.axios.post('/app/v1_contabilidad_general/ConsultaPeriodoActual', {
                    Opcion: "C",
                    SubOpcion: "2",
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.Fecha = resp.data.json[0].FechaFinal
                        this.cbPeriodos = {
                            CodigoPeriodo: resp.data.json[0].CodigoPeriodo,
                            DescPeriodo: resp.data.json[0].Descripcion
                        }

                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido cargar los periodos, favor validar.',
                        })
                    }
                })
        },
        onChangePeriodos(value) {
            if (value !== null && value.length !== 0) {
                this.CodigoPeriodo = value.CodigoPeriodo
                this.DescPeriodo = value.DescPeriodo
            } else {
                this.CodigoPeriodo = ''
                this.DescPeriodo = ''
            }
        },
        PeriodoSeleccionado({
            CodigoPeriodo,
            DescPeriodo
        }) {
            return `${CodigoPeriodo} - ${DescPeriodo} `
        },

        InicializaSaldos() {
            this.axios.post('/app/v1_contabilidad_general/ActActividad', {
                    Opcion: "U",
                    SubOpcion: "1",
                    Periodo: this.cbPeriodos.CodigoPeriodo
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ActPartidas()
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido Inicializar Saldos, favor validar.',
                        })
                    }
                })
                .catch(() => {})
        }
    }
}
</script>   

<style scoped>
.footer {
    height: 70px;
    margin-top: -65px;
    margin-left: 10px;
}

.label-size {
    font-size: 14px;
    font-weight: bold;
}

.label-i {
    font-size: 18px;
    font-weight: bold;
    color: rgb(240, 158, 6);
}

.cuadro-i {
    background: #faf2f1;
}

.flex-container {
    display: flex;
    flex-direction: row;
}

.flex-item-right {
    padding: 10px;
    flex: 50%;
    margin-left: 50px;
    margin-right: 50px;
}

/* Responsive layout - makes a one column layout instead of a two-column layout */
@media (max-width: 800px) {
    .flex-container {
        flex-direction: column;
    }
}

.padre {
    position: relative;
}

.hijo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
</style>
