<template>
    <div>
        <buscador ref="BuscarOrden" buscador_titulo="Buscador / Listado de Ordenes de Laboratorio" :api="'app/v1_JefeCaja/ListaOrdenesDiagnostico'" 
            :campos="['Apellido', 'ApellidoCasada', 'Nombre', 'TipoOrden', 'Orden', 'Fecha', 'ApellidoMedico', 'NombreMedico']"
            :titulos="['Apellido', 'Apellido Casada', 'Nombre', 'Tipo Orden', 'Orden', 'Fecha', 'Apellido Medico', 'Nombre Medico']"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" />
        <vx-card title="Hoja de Cargos">
            <vs-divider border-style="solid" color="dark"></vs-divider>
            <div class="box-custom">
                <vs-row vs-type="flex" vs-w="12">
                    <vs-col vs-w="1">
                        <div class="div-input flex-wrap-nowrap">
                            <label class="label-static">Tipo</label>
                            <vs-input class="w-full input-serie" v-model="tipoOrden" ></vs-input>
                        </div>
                    </vs-col>
                </vs-row>
                <vs-row vs-type="flex" vs-w="12">
                    <vs-col vs-w="1">
                        <div class="div-input flex-wrap-nowrap">
                            <label class="label-static">Número</label>
                            <vs-input class="w-full input-numero" v-model="numeroOrden" v-on:blur="OrdenDiagnostico()" ></vs-input>
                        </div>
                    </vs-col>
                    <vs-col vs-w="1">
                        <div class="button_search flex-wrap-nowrap">
                            <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="BuscarOrden()" icon="icon-search"></vs-button>
                        </div>
                    </vs-col>
                </vs-row>
                <vs-row vs-justify="left" vs-w="12">
                    <vs-col type="flex" vs-justify="left" vs-align="left" vs-w="1">
                        <div class="div-input flex-wrap-nowrap">
                            <label class="label-static">Paciente</label>
                            <input class="w-full label-info" v-model="codPaciente" disabled/>
                        </div>
                    </vs-col>
                    <vs-col type="flex" vs-justify="left" vs-align="left" vs-w="4">
                        <div class="div-input flex-wrap-nowrap">
                            <input class="w-full label-info2" v-model="nombrePaciente" disabled/>
                        </div>
                    </vs-col>
                </vs-row>
                <vs-row vs-justify="left" vs-w="12">
                    <vs-col type="flex" vs-justify="left" vs-align="left" vs-w="4">
                        <div class="div-input flex-wrap-nowrap">
                            <label class="label-static">Refiere</label>
                            <input class="w-full label-info" v-model="nombreMedico" disabled/>
                        </div>
                    </vs-col>
                </vs-row>
                <br>
                <vx-card>
                    <vs-row vs-justify="center" vs-w="12">
                        <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="12">
                            <div class="div-input flex-wrap-nowrap">
                                <label class="label-static1">Observaciones Examenes</label>
                            </div>
                        </vs-col>
                    </vs-row>
                    <vs-row  vs-type="flex" vs-w="12">
                        <vs-col vs-type="flex-wrap" vs-justify="center" vs-align="center" vs-w="12">
                            <div class="div-input">
                                <vs-textarea rows="10" v-model="observaciones"  class="w-full textEstado" disabled/>
                            </div>
                        </vs-col>
                    </vs-row>
                </vx-card>
            </div>
        </vx-card>
    </div>
</template>
<script>
    export default {
        data(){
            return{
                tipoOrden: '',
                numeroOrden: '',
                codPaciente: '',
                nombrePaciente: '',
                nombreMedico: '',
                observaciones: '',
                ListOrdenes: []
            }
        },
        methods: {
            isEmptyObject(obj){
                return Object.keys(obj).length === 0;
            },
            BuscarOrden(){
                this.$refs.BuscarOrden.iniciar((data) => {
                    if(data != null){
                        this.tipoOrden = data.TipoOrden
                        this.numeroOrden = data.Orden
                    }
                })
            },
            OrdenDiagnostico(){
                this.axios.post('/app/v1_JefeCaja/ConsultaOrdenDiagnostico', {
                    "TipoOrden": this.tipoOrden,
                    "NumeroOrden": this.numeroOrden
                })
                .then(resp => {
                    if(!this.isEmptyObject(resp.data.json)){
                        const { SerieAdmision, Admision, Nombre, Apellido, ApellidoCasada, Titulo, NombreMed, ApellidoMed, Observaciones } = resp.data.json[0]
                        this.codPaciente = SerieAdmision + Admision
                        this.nombrePaciente = ApellidoCasada == '' ? Nombre + ' ' + Apellido : Nombre + ' ' + Apellido + ' ' + ApellidoCasada
                        this.nombreMedico = Titulo + ' ' + NombreMed + ' ' + ApellidoMed
                        this.observaciones = Observaciones

                    }else{
                        this.codPaciente = ''
                        this.nombrePaciente = ''
                        this.nombreMedico = ''
                        this.observaciones = ''
                    }
                })
            },
            ListaOrdenesDiagnostico(){
                this.axios.post('/app/v1_JefeCaja/ListaOrdenesDiagnostico', {
                })
                .then(resp => {
                    console.log('Ingreso por aca')
                    if(!this.isEmptyObject(resp.data.json)){
                        this.ListOrdenes = resp.data.json.map(m => {
                            return{
                                ...m
                            }
                        })
                    }
                })
            }
        },
        watch: {
            numeroOrden(){
                this.OrdenDiagnostico()
            }
        }
        // deactivated(){
        //     this.$destroy()
        //     this.$el.parentNode.removeChild(this.$el)
        // }
    }
</script>
<style lang="scss" scoped>
    .box-custom{
        box-shadow: 0px 15px 40px -10px;
        padding: 15px;

        .button_search{
            padding-top: 35px;
        }
    }
    .div-input{
        padding: 10px;
        .input-serie{
            width: 75px;
        }
        .input-numero{
            width: 200px;
        }
        .textEstado{
            color: #fc0909;
            font-weight: bold;
            font-size: 25px;
        }

    }
    .label-info{
        margin-left: 10px;
        color:#E74C3C;
        opacity: 0.7;
        border: 0;
        font-size: 16px;
        background-color: white;
    }
    .label-info2{
        margin-left: 10px;
        padding-top: 25px;
        color:#E74C3C;
        opacity: 0.7;
        border: 0;
        font-size: 16px;
        background-color: white;
    }
    .label-static{
        font-weight: bold;
        font-size: 16px;
    }
    .label-static1{
        font-weight: bold;
        font-size: 24px;
        text-align: center;
    }
</style>