<template>
<div>
    <vs-popup id="contentreport" classContent="popup-generar" :title="reporte.titulo" :active.sync="reporte.generar" fullscreen style="z-index:99999">
        <div style=" width: 100%; height: 100%;">
            <object id="pdfDocument" v-if="reporte.pdf!=''" type="application/pdf" :data="reporte.pdf" ref="pdfDocument" width="100%" height="99%" margin-top="-50px">
                <p>Su navegador es incompatible con el plugin pdf.</p>
            </object>
        </div>
        <div v-if="reporte.mail && reporte.mail.EnvioCorreo" style="position: absolute;">
            <vs-button size="small" color="primary" type="filled" @click="Correo()" icon="mail">
                Enviar por Correo
            </vs-button>
        </div>
    </vs-popup>
    <vs-popup classContent="popup-example" title="Enviar Correo Electrónico" :active.sync="showCorreo" style="z-index:199999">
        <sm-email v-bind="reporte.mail" @success="showCorreo=false" />
    </vs-popup>
</div>
</template>

<script>
import print from 'print-js'

export default {
    data() {
        return {
            reporte: {
                generar: false,
                buscar: '',
                titulo: '',
                pdf: '',
                mail: null
            },
            showCorreo: false,
        }
    },
    computed: {
        reportePDF() {
            const r = this.$store.state.reporte
            return r
        }
    },
    watch: {
        reportePDF(value) {
            if (value.imprimir) {
                let byteCharacters = atob(value.pdf.replace('data:application/pdf;base64,', ''))
                let byteNumbers = new Array(byteCharacters.length);
                for (let i = 0; i < byteCharacters.length; i++) {
                    byteNumbers[i] = byteCharacters.charCodeAt(i);
                }
                let byteArray = new Uint8Array(byteNumbers);
                let file = new Blob([byteArray], {
                    type: 'application/pdf;base64',
                    name: this.reporte.titulo || 'Reporte'
                });
                let fileURL = URL.createObjectURL(file);
                // printJS(fileURL)
                // const printWindow = window.open(fileURL, "", "width=500  ,height=500");
                // printWindow.document.write("<body onafterprint='window.close()'>");

                // printWindow.document.writeln("<body onafterprint='window.close()'>");
                // printWindow.document.writeln("<script type=\"text/javascript\" >window.print()</scr"+"ipt>");

                // printWindow.document.write("<object  type='application/pdf' data='+' ref='pdfDocument' width='100%' height='98%'></object>")
                // printWindow.print();
                print(fileURL)

                // printWindow.onfocus = function() {
                //     console.log(1)
                //     printWindow.close()
                //     this.onbeforeunload = function() {
                //         console.log(2)
                //     }
                // }
                // printWindow.close();

                return false
            }
            if (value.abrirTab) {
                let byteCharacters = atob(value.pdf.replace('data:application/pdf;base64,', ''))
                let byteNumbers = new Array(byteCharacters.length);
                for (let i = 0; i < byteCharacters.length; i++) {
                    byteNumbers[i] = byteCharacters.charCodeAt(i);
                }
                let byteArray = new Uint8Array(byteNumbers);
                let file = new Blob([byteArray], {
                    type: 'application/pdf;base64'
                });
                let fileURL = URL.createObjectURL(file);
                window.open(fileURL);
                return false
            }
            this.reporte.generar = true
            this.reporte.pdf = value.pdf
            this.reporte.titulo = value.titulo
            this.reporte.mail = value.mail
        },
        'reporte.generar'(newvalue) {
            if (!newvalue) {
                this.reporte.pdf = ''
            }
        }
    },
    methods: {
        Correo() {
            let byteCharacters = atob(this.reporte.pdf.replace('data:application/pdf;base64,', ''))
            let byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            let byteArray = new Uint8Array(byteNumbers);
            let blob = new Blob([byteArray], {
                type: 'application/pdf;base64'
            });
            const myfile = new File([blob], this.reporte.titulo + '.pdf', {
                type: "data:application/pdf"
            })
            this.reporte.mail.attach = [myfile]
            this.showCorreo = true
        }
    }
}
</script>

<style>

</style>
