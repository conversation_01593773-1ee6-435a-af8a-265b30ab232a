<template>
<vx-card title="Asignación Departamento - Categoría">

    <div class="content content-pagex">

        <div class="terms">
            <div class="flex flex-wrap">
                <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                    <ValidationProvider name="Departamento">
                        <label class="label_sizem">Departamentos</label>
                        <multiselect v-model="cb_departamentos" :options="Lista_departamentos" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Departamento_seleccionado" placeholder="Seleccionar departamento" @input="onChangeDepartamento">
                            <span slot="noOptions">Lista no disponible.</span>
                        </multiselect>
                    </ValidationProvider>
                </div>
                
                <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                    <div style="margin-left:30px;margin-right:20px" class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
                        <ValidationProvider name="Operacion">
                            <label class="label_sizem">Operación</label>
                            <multiselect v-model="cb_lista_operacion" :options="Lista_operacion" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Operacion_seleccionada" placeholder="Seleccionar departamento" @input="onChangeTipo">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>
                </div>
            </div>
        </div>
        <br>
        <br>
        <div class="flex flex-wrap">
            <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                <ValidationProvider name="Agencia">
                    <label class="label_sizem">Categorías disponibles</label>
                    <multiselect class="label_sizem" v-model="id_categoria_selec" :options="Lista_categorias_disponibles" :multiple="true" :close-on-select="false" :clear-on-select="false" :preserve-search="true" placeholder="Seleccionar categoría" label="NOMBRE" track-by="NOMBRE" :preselect-first="true" @input="onChangeCategoria" :disabled="Deshabilitado_categoria">
                        <span slot="noOptions">Lista no disponible.</span>
                    </multiselect>
                </ValidationProvider>

            </div>

            <div class="flex flex-wrap">
                <div class="xs:w-full" align="left">
                    <vs-button color="primary" style="float:left;margin: 25px" type="filled" icon-pack="fas" icon="fa-plus" @click.native="Guardar_Asociacion()"> Asignar Categorías</vs-button>
                </div>

            </div>
        </div>

        <!---CHECK SELECCIONAR TODOS -->
        <div class="flex flex-wrap">
            <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">

                <input labelt="TODOS" type="checkbox" id="checkbox" v-model="Enviar_Todos" :disabled="Estado_Todos" name="lst_check" @change="onChangeCheck()" />
                <label class="typo__label"> Seleccionar todos</label>

            </div>

        </div>
        <!---CHECK SELECCIONAR TODOS -->
        <div>

            <vs-divider position="left">Detalle</vs-divider>
            <vs-table2 max-items="10" pagination :data="Lista_Asignaciones_categoria_depto" search id="tb_asignacion_categoria_depto">
                <template slot="thead">
                    <th>Departamento</th>
                    <th width="10">Código Categoria</th>
                    <th>Categoria</th>
                    <th>Operación</th>
                    <th></th>
                </template>

                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">

                        <vs-td2 :data="data[indextr].DEPARTAMENTO">
                            {{data[indextr].DEPARTAMENTO}}
                        </vs-td2>
                        <vs-td2 :data="data[indextr].CODIGO_CATEGORIA">
                            {{data[indextr].CODIGO_CATEGORIA}}
                        </vs-td2>
                        <vs-td2 :data="data[indextr].CATEGORIA">
                            {{data[indextr].CATEGORIA}}
                        </vs-td2>

                        <vs-td2 :data="data[indextr].DESCRIPCION_TIPO">
                            {{data[indextr].DESCRIPCION_TIPO}}
                        </vs-td2>

                        <vs-td2 width="50px">
                            <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="Eliminar_Registro(data[indextr])"></vs-button>
                        </vs-td2>
                    </tr>
                </template>

            </vs-table2>

        </div>
    </div>

    <!-----------------LLamanda de componenetes ----------------------->
    <!-- <componenteasignacion v-if="Estado_VentanaAsignacion" ref="productos" :callback="Iniciar_asignacion" :cerrar="()=>Estado_VentanaAsignacion=false" />  -->

</vx-card>
</template>

<script>
/**
 * @General
 * Modulo almacena la relación entre departamento y categorias, permitidas a comprar o requerir.
 */

import Multiselect from 'vue-multiselect'
import "vue-multiselect/dist/vue-multiselect.min.css"
export default {

    components: {
        //'componenteasignacion': () => import('./REG002_NuevaAsignacion'),
        Multiselect,

    },
    data() {
        return {
            Estado_Todos: true,
            Enviar_Todos: false,
            Deshabilitado_categoria: false,
            Estado_VentanaAsignacion: false,
            cb_departamentos: '',
            Lista_departamentos: [],
            id_departamento_selec: '',
            cb_lista_operacion: '',
            Lista_operacion: [{
                'CODIGO': 'R',
                'DESCRIPCION': 'Requerimientos'
            }, {
                'CODIGO': 'C',
                'DESCRIPCION': 'Compras'
            }],
            id_operacion_selec: '',

            Lista_Asignaciones_categoria_depto: [],
            Corporativo_Sesion: '',

            cb_categoria_disponibles: '',
            id_categoria_selec: '',
            Lista_categorias_disponibles: [],
            lista_categorias_select: [],
        };
    },
    mounted() {
        this.Consultar_Departamentos();

    },
    methods: {

        //Devolver los obejtos seleccionados de los  combobox
        Departamento_seleccionado({
            CODIGO,
            NOMBRE
        }) {
            return `${CODIGO} - ${NOMBRE} `
        },
        Operacion_seleccionada({
            DESCRIPCION
        }) {
            return `${DESCRIPCION} `
        },
        Categoria_seleccionada({
            CODIGO,
            NOMBRE
        }) {
            return `${CODIGO} - ${NOMBRE} `
        },
        async Consultar_Departamentos() {

            this.cb_categoria_disponibles = null;
            this.id_categoria_selec = '';
            this.cb_departamentos = null;
            this.id_departamento_selec = '';
            this.cb_lista_operacion = null;

            //$("#cb_departamentos").multiselect("clearSelection");
            //const sesion = this.$store.state.sesion;
            const url = this.$store.state.global.url;
            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/consulta_departamento', {
                    // Empresa: sesion.sesion_empresa,
                    // TipoConsulta: ''
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Mantenimiento Departamento',
                            text: resp.data.mensaje,
                        })
                        this.Lista_departamentos = [];
                    } else {
                        this.Lista_departamentos = resp.data.json;
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();

        },
        async Consultar_Asignaciones() {
            const url = this.$store.state.global.url;
            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/consulta_departamento_categoria', {
                    codigo_departamento: this.id_departamento_selec,
                    tipo: this.id_operacion_selec
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Mantenimiento Departamento',
                            text: resp.data.mensaje,
                        })
                        this.Lista_Asignaciones_categoria_depto = [];
                    } else {
                        this.Lista_Asignaciones_categoria_depto = resp.data.json;
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },
        async Consultar_CategoriasDisponibles() {

            const url = this.$store.state.global.url;
            //const sesion = this.$store.state.sesion;
            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/consulta_categorias_disponibles_departamentos', {
                    // Empresa: sesion.sesion_empresa,
                    Departamento: this.id_departamento_selec,
                    tipo: this.id_operacion_selec,
                    operacion: this.operacion
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.Lista_categorias_disponibles = [];
                    } else {
                        this.Lista_categorias_disponibles = [];
                        this.Lista_categorias_disponibles = resp.data.json;
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();

        },
        Guardar_Asociacion() {
            /**
             * @General
             * Función Permite  actualizar y  almacenar un nuevo registro;
             */
            const sesion = this.$store.state.sesion;
            this.Corporativo_Sesion = sesion.corporativo

            this.res_variables = this.Validacion_Campos('A', 'Departamento', this.id_departamento_selec, true);
            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('A', 'Operación', this.id_operacion_selec, true);

            var cantidad = Object.keys(this.lista_categorias_select).length;
            if (cantidad == '0' || cantidad == '2') { //Validación que el array tenga datos, que tenga mas de dos caracteres, 
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Mantenimiento Departamento',
                    text: 'Seleccionar categoría',
                })
                return;
            }

            if (this.res_variables) {

                this.$vs.loading();
                const url = this.$store.state.global.url;
                this.axios.post(url + 'app/v1_OrdenCompra/departamento_categoria', {
                        codigo: 0,
                        codigo_departamento: this.id_departamento_selec,
                        json_datos: this.lista_categorias_select,
                        estado: 'S',
                        tipo: this.id_operacion_selec,
                        operacion: 'N',
                        corporativo: this.Corporativo_Sesion,
                    })
                    .then(resp => {
                        this.$vs.loading.close();
                        if (resp.data.codigo !== 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Mantenimiento Departamento',
                                text: resp.data.mensaje,
                            })

                            this.lista_categorias_select = [];
                            this.id_categoria_selec = '';

                            this.cb_categoria_disponibles = '';
                            this.Consultar_Asignaciones();
                            this.Consultar_CategoriasDisponibles();

                        } else {
                            this.Lista_categorias_disponibles = [];
                            this.lista_categorias_select = [];
                            this.id_categoria_selec = '';
                            this.cb_categoria_disponibles = '';
                            this.Deshabilitado_categoria = false;
                            this.Enviar_Todos = false;
                            this.Consultar_Asignaciones();
                            this.Consultar_CategoriasDisponibles();

                        }
                    })
                    .catch(() => {
                        this.$vs.loading.close();
                    })
                this.$vs.loading.close();
            }

        },
        onChangeDepartamento(value) {
            //Si existe seleccionada un elemento
            this.Deshabilitado_categoria = false;
            this.Enviar_Todos = false;
            if (value !== null && value.length !== 0) {
                this.id_departamento_selec = value.CODIGO;
                if (this.id_operacion_selec !== null && this.id_operacion_selec !== 0 && this.id_operacion_selec !== "") {
                    this.Consultar_Asignaciones();
                    this.Consultar_CategoriasDisponibles();
                    this.Estado_Todos = false;
                }
            } else {
                this.id_departamento_selec = '';
                this.Lista_categorias_disponibles = [];
                this.Lista_Asignaciones_categoria_depto = [];
                this.Estado_Todos = true;
                this.Check_todos = false;
            }

        },
        onChangeTipo(value) {
            //Si existe seleccionada un element
            this.Deshabilitado_categoria = false;
            this.Enviar_Todos = false;
            if (value !== null && value.length !== 0) {
                this.id_operacion_selec = value.CODIGO;
                this.cb_categoria_disponibles = null;
                this.id_categoria_selec = '';
                this.Consultar_CategoriasDisponibles()
                this.Consultar_Asignaciones();
                this.Estado_Todos = false;
            } else {
                this.id_operacion_selec = '';
                this.Lista_Asignaciones_categoria_depto = [];
                this.Lista_categorias_disponibles = [];
                this.Estado_Todos = true;
                this.Check_todos = false;
            }

        },
        onChangeCategoria(value) {

            this.lista_categorias_select = JSON.stringify(value);

        },
        onChangeCheck() {
            if (this.Enviar_Todos) {
                this.Deshabilitado_categoria = true;
                this.id_categoria_selec = '',
                    this.lista_categorias_select = [];
                this.lista_categorias_select = JSON.stringify(this.Lista_categorias_disponibles);

            } else {

                this.Deshabilitado_categoria = false;
            }

        },
        Eliminar_Registro(value) {
            /**
             * @General
             * Función eliminar registro;
             */
            const sesion = this.$store.state.sesion;
            this.Corporativo_Sesion = sesion.corporativo
            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Confirmación',
                button: {
                    yes: 'Aceptar',
                    no: 'Cancelar'

                },
                text: '¿Dar de baja Asignación  \'' + value.DEPARTAMENTO + '\' -  \'' + value.CATEGORIA + '\' ? ',
                accept: () => {
                    const url = this.$store.state.global.url
                    this.$vs.loading();
                    this.axios.post(url + 'app/v1_OrdenCompra/departamento_categoria', {
                            codigo: value.CODIGO,
                            codigo_departamento: '0',
                            codigo_categoria: '0',
                            estado: 'N',
                            tipo: value.TIPO,
                            operacion: 'B',

                        })
                        .then(resp => {
                            this.$vs.loading.close();
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Mantenimiento Departamento',
                                    text: resp.data.mensaje,
                                })

                            } else {
                                this.Lista_Asignaciones_categoria_depto = [];
                                this.Consultar_Asignaciones();
                                this.Consultar_CategoriasDisponibles()
                                this.$vs.notify({
                                    color: '#danger',
                                    title: 'Mantenimiento Departamento',
                                    text: resp.data.mensaje,
                                });

                            }
                        })
                        .catch(() => {
                            this.$vs.loading.close();
                        })
                    this.$vs.loading.close();

                }
            })

        },
        Abrir_Ventana_Emergente_Asignacion() {
            this.Estado_VentanaAsignacion = true;
        },
        Iniciar_asignacion() {
            this.Estado_VentanaAsignacion = true;
        },
        Validacion_Campos(Tipo, Nombre, valor, obligatorio) {
            /**
             * @General
             * Tipo : N = Númerio, Nombre = Descripción del campo, Valor = A evaluar, Obligatorio= Verdadero o falso.
             * Devuelve un mensaje al usuario si las validaciones no se cumple y aborta, de lo contrario devuelve verdadero
             */
            if (Tipo == 'N') {
                if (valor <= 0) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Mantenimiento Departamento',
                        text: 'Campo \'' + Nombre + '\' es obligatorio.',
                    });
                    return false;
                } else {
                    return true;
                }
            } else {
                if (valor == '' && obligatorio == true) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Mantenimiento Departamento',
                        text: 'Campo \'' + Nombre + '\' es obligatorio.',
                    });
                    return false;
                } else {
                    return true;
                }
            }
        },

    },

}
</script>

<style scoped>
.modal-card {
    overflow: auto !important;
}

.modal-card {
    overflow: auto !important;
    z-index: 1 !important;
}

/*.modal-card-body {
        overflow: auto !important;
      }*/
.modal-card-foot {
    overflow: auto !important;
}

.custom {
    width: 80% !important;
    overflow: visible !important;
}

.modal-card-body {
    overflow: visible !important;
}

.label-size {
    font-size: 18px;
    font-weight: bold;
}

.label-sizem {
    font-size: 16px;
    font-weight: bold;
}
</style>
