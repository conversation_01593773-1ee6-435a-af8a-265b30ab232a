<template>
    <ValidationObserver ref="admisionInterna" v-slot="{ invalid, handleSubmit }" mode="lazy">
        <form @submit.prevent="handleSubmit(submitForm(submit))">


            <div class="admision-interna">

                <DatosPaciente ref="DatosPacienteComponent" v-show="false">
                </DatosPaciente>

                <SerieCodigoAdmision
                  ref="serieCodigoAdmisionComponent"
                  @selected-option-changed="onSelectedOptionChanged"
                  v-model="AdmisionData.TipoAdmision"
                  :location-data="'header'">
                </SerieCodigoAdmision>



                <DiagnosticoCovid
                  ref="diagnosticoCovidComponent"
                  @checked-options-changed="onCheckedOptionChanged"
                  @vacunas-seleccionadas="obtenerListaVacunas"
                  :id-paciente="parseInt(PacienteData.Codigo)">
                </DiagnosticoCovid>

                <div v-show="AdmisionData.TipoAdmision > 0">

                    <div v-show="AdmisionData.TipoAdmision == 1">

                        <ConsultaHabitaciones ref="consultaHabitacionesComponent"
                                              v-on:codigo-habitacion="obtenerCodigoHabitacion">
                        </ConsultaHabitaciones>
 
                    </div>

                    <TiposDescuento
                      ref="tipoDescuentoComponent"
                      @selected-tipodes-changed="onSelectedTipoChanged"
                      v-model="AdmisionData.TipoDescuento"
                      :tipo-admision="1"
                      :tipo-descuento="'Salud Siempre/Percapitados'">>
                    </TiposDescuento>

                    <div class="datosnotificarA">

                        <div class="w-full md:w-full lg:w-3/5 xl:w-3/5 m-1">
                            <validationProvider name="Primer Nombre Paciente" class="required" rules="required|min:3"
                                v-slot="{ errors }">
                                <vs-input class="w-full" label="Notificar a" v-model="AdmisionData.NotificarA"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null">
                                </vs-input>
                            </validationProvider>
                        </div>
                        <div class="w-full md:w-full lg:w-3/5 xl:w-3/5 m-1">
                            <validationProvider name="Primer Nombre Paciente" class="required" rules="required|min:3"
                                v-slot="{ errors }">
                                <vs-input class="w-full" label="Direccion" v-model="AdmisionData.DireccionNotificarA"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null">
                                </vs-input>
                            </validationProvider>
                        </div>

                        <div class="w-full md:w-full lg:w-3/5 xl:w-3/5 m-1">
                            <validationProvider name="Primer Nombre Paciente" class="required" rules="required|min:3"
                                v-slot="{ errors }">
                                <vs-input class="w-full" label="Telefono" v-model="AdmisionData.TelefonoNotificarA"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null">
                                </vs-input>
                            </validationProvider>
                        </div>

                    </div>

                   <ConsultarMedicos
                     ref="ConsultarMedicosComponent"
                     v-on:codigo-medico="obtenerCodigoMedico"
                     v-on:no-mail-checked="validaPoseeCorreo"
                     v-on:mail-changed="obtieneCorreoMedico">
                    </ConsultarMedicos>

                </div>


                <div v-show="showButtons">
                    <vs-button style="margin-right:1px" v-on:click="handleSubmit(submit())" :disabled="invalid"
                        :clientWidth="20"> Crear Admisión Interna
                    </vs-button>
                </div>
            </div>

        </form>
    </ValidationObserver>
</template>
  
<script>

import { mapFields } from "vuex-map-fields";

//Importación de componentes.
import DatosPaciente from "/src/components/sermesa/modules/paciente/DatosPaciente.vue"
import DiagnosticoCovid from "/src/components/sermesa/modules/admisiones/DiagnosticoCovid.vue";
import ConsultaHabitaciones from "/src/components/sermesa/modules/admisiones/ConsultaHabitaciones.vue"
import TiposDescuento from "/src/components/sermesa/modules/admisiones/TiposDescuento.vue";
import SerieCodigoAdmision from "/src/components/sermesa/modules/admisiones/SerieCodigoAdmision.vue";
import ConsultarMedicos from "/src/components/sermesa/modules/admisiones/ConsultarMedicos.vue"


export default {
    name: "AdmisionesInternas",
    components: {
        DatosPaciente,
        DiagnosticoCovid,
        ConsultaHabitaciones,
        TiposDescuento,
        SerieCodigoAdmision,
        ConsultarMedicos,
    },
    data() {
        return {
            IdPaciente: 0,
            PaqueteQx: '',
            initialSerie: '',
            selectedTipo: '',
            NombreMedico: '',
            showButtons: false,
            TipoAdmisionBusqueda: 13

        }
    },
    computed: {
        ...mapFields('admision', ["AdmisionData"]),
        ...mapFields('paciente', ["PacienteData"])

    },
    beforeCreated() {
    },
    created() {
        this.AdmisionData.TipoAdmision = 13
        this.IdPaciente = this.PacienteData.Codigo

    },
    beforeMount() {
    },
    mounted() {
        this.AdmisionData.TipoAdmision = 0
    },
    methods: {
        getionarDatos() {
            return new Promise((resolve) => {
                // Lógica de guardado del cliente aquí
                // Resuelve la promesa si el guardado es exitoso
                return resolve(true);
                // Rechaza la promesa si hay un error en el guardado
                // reject('Mensaje de error aquí');
            });
        },
        validacionFormulario() {
            return new Promise((resolve, reject) => {
                this.$refs.admisionInterna.validate()
                    .then((isValid) => {
                        if (isValid) {
                            resolve(true);
                        } else {

                            reject('Revisar campos requeridos');
                        }
                    })
                    .catch((error) => {
                        reject(error);
                    });
            });
        },
        gestionarAdmision() {
            return new Promise((resolve, reject) => {
                this.$store.dispatch('admision/crearAdmision')
                    .then((response) => {
                        this.$emit("mensaje-respuesta", response);
                        resolve(response);
                    })
                    .catch((error) => {
                        reject(error.descripcion[0].DescError);
                    });
            });
        },
        async submit() {

            if (this.AdmisionData.Codigo !== undefined && this.AdmisionData.Codigo > 0) {
                     // El código existe y es mayor que cero
                return;
            }
                

            if (this.AdmisionData.TipoAdmision === 0) {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Valor Requerido',
                    text: 'Debe elegir un Tipo de Tipo de Admisión',
                })
                return
            }

            this.AdmisionData.Paciente = this.PacienteData.Codigo
            this.AdmisionData.NombreFactura = this.PacienteData.NombreFactura
            this.AdmisionData.DireccionFactura = this.PacienteData.DireccionFactura
            this.AdmisionData.IdCliente = this.PacienteData.IdCliente
            this.AdmisionData.DPI = this.PacienteData.DPI
            this.AdmisionData.Nit = this.PacienteData.Nit
            this.AdmisionData.FechaNacimiento = this.PacienteData.Nacimiento

            this.getionarDatos()
                .then(() => {
                    return this.validacionFormulario();
                })
                .then(() => {
                    return this.gestionarAdmision();
                })
                .then(() => {
                    var obj = { Serie: this.AdmisionData.Serie, Admision: this.AdmisionData.Codigo }
                    this.$emit("admision", obj);

                    this.$vs.dialog({
                        type: 'confirm',
                        color: '#ED8C72',
                        acceptText: 'Continuar',
                        title: 'Se creo la Admisión No.',
                        text: this.AdmisionData.Serie + ' ' + this.AdmisionData.Codigo,
                        clientWidth: 100,
                        accept: () => {
                            this.$emit("ready", true);
                        }
                    })
                })
                .catch((error) => {
                    var obj = { Serie: null, Admision: null }
                    this.$emit("admision", obj);
                    this.$vs.dialog({
                        type: 'confirm',
                        color: '#ED8C72',
                        acceptText: 'Continuar',
                        title: 'Error en gestión de Paciente',
                        text: error,
                        clientWidth: 100,
                        accept: () => {

                            this.$emit("ready", false);

                        }
                    });
                })


        },
        onCheckedOptionChanged(diagnostico) {

            this.DisgnosticoCovid = diagnostico
            this.AdmisionData.DiagnosticoCOVID = (diagnostico.length === 0) ? 0 : 1
        },
        obtenerListaVacunas(vacunas) {

            this.AdmisionData.PrimeraVacunaCOVID = vacunas[0]?.Codigo || 0
            this.AdmisionData.SegundaVacunaCOVID = vacunas[1]?.Codigo || 0
            this.AdmisionData.TerceraVacunaCOVID = vacunas[2]?.Codigo || 0

        },
        onSelectedOptionChanged(admision) {
            this.AdmisionData.TipoAdmision = admision.TipoAdmision
            this.AdmisionData.Serie = admision.Serie
            this.AdmisionData.NivelPrecios = admision.NivelDePrecios
            this.AdmisionData.Habitacion = ''


        },
        onSelectedTipoChanged() {

        },
        obtenerCodigoMedico(datosMedico) {
            this.AdmisionData.Medico = datosMedico.CodigoMedico
            this.AdmisionData.CorreoElectronicoMedico = datosMedico.CorreoMedico
        },
        validaPoseeCorreo(poseeCorreo) {
            this.AdmisionData.Medico = poseeCorreo.CodigoMedico
            this.AdmisionData.CorreoElectronicoMedico = poseeCorreo.CorreoMedico
            this.AdmisionData.MedicoNoPoseeCorreo = poseeCorreo.NoCorreo
        },
        obtieneCorreoMedico(correoMedico) {
            this.AdmisionData.Medico = correoMedico.CodigoMedico
            this.AdmisionData.CorreoElectronicoMedico = correoMedico.CorreoMedico
            this.AdmisionData.MedicoNoPoseeCorreo = correoMedico.NoCorreo
        },
        obtenerCodigoHabitacion(codigo) {
            this.AdmisionData.Habitacion = codigo
        },
        onObtenerListaDiagnosticos(diagnosticos) {
            var codigos = []
            diagnosticos.forEach(function (item) {
                codigos.push(item.CodigoDiagnostico.trim());
            });
            var codigosSeparados = codigos.join(" | ");
            this.AdmisionData.Otros = codigosSeparados
        }
    },
};
</script>


<style scoped>
* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

.admision-interna {
    margin: 14px;
}

.medico-habitacion {
    display: grid;
    width: 100%;
    height: 100%;
    grid-template-areas: "codigo-medico codigo-habitacion";
    grid-template-columns: 50% 50%;
    grid-template-rows: 50% 50%;
}

.medico-habitacion>div {
    grid-gap: 1px;
    border-radius: 5px;
    margin: 1px;
}

.codigo-medico {
    grid-area: codigo-medico;
    border-radius: 5px;
}

.codigo-habitacion {
    grid-area: codigo-habitacion;
    border-radius: 5px;
}

.datosnotificarA {
    display: flex;
    grid-gap: 10px;
    align-items: center;
    padding-top: 0.5rem;
    padding-bottom: 1rem;
    padding-left: 1rem;
    padding-right: 1rem;
    border: 1px solid #888;
    border-radius: 5px;
    margin: 5px;
}
</style>

