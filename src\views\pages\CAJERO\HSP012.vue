<template>
    <vx-card :title="`Reportes de Cortes - ${sesion.sesion_sucursal_nombre}`"> 
        <vs-popup title="Búsqueda No. Corte" :active.sync="VentanEmergenteNoCorte"  @keyup.esc="VentanEmergenteNoCorte = false;" tabindex="0" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px" >

                <form>
                    <vs-row class="w-full">
                        <vs-col class="sm:w-full md:w-full lg:w-8/12 xl:w-6/12 p-1 flex">
                                <vs-input label="Buscar cortes desde la fecha:" type="date" style="width:200px; color: black;" v-model="corte.fechaCorte"  
                                          id="fechaCorteId" class="vs-con-loading__container"
                                          @keyup.esc="VentanEmergenteNoCorte = false;" v-on:keydown.enter="CargarNoCortes()" />    
                                <div class="append-text btn-addon pt-6">
                                    <vs-button id="button-with-loading" color="primary" icon-pack="fa" 
                                               @keyup.esc="VentanEmergenteNoCorte = false;" @click="CargarNoCortes()" icon="fa-search"  class="vs-con-loading__container"></vs-button>
                                </div>
                        </vs-col>
                    </vs-row>
                    <vs-divider></vs-divider>
                    <vs-table2 max-items="10" pagination :data="corte.NoCortes">

                        <template slot="thead">
                            <th>Fecha Inicial</th>
                            <th>Fecha Final</th>
                            <th>Codigo</th>
                            <th>Cajero</th>
                        </template>

                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr,indextr) in data" v-on:click=GuardarNoCorte(tr)>
                                <vs-td2 width='20%'>
                                    {{ tr.Inicio }}
                                </vs-td2>
                                <vs-td2 width='20%'>
                                    {{ tr.Final }}
                                </vs-td2>
                                <vs-td2 width='20%'>
                                    {{tr.Codigo}}
                                </vs-td2>
                                <vs-td2 width='20%'>
                                    {{!ValidarVariable(tr.Corporativo)?tr.Usuario:tr.Corporativo}}
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
                </form>

            </div>
        </vs-popup>
        <vs-row class="w-full">
            <ConfiguracionCaja ref="ConfiguracionCajaRef" 
                            @CargarCaja="CargarCaja" :MostrarMonitor=false
                            TipoCajaSeleccionada="TipoCajaFacturacion" CajaSeleccionada="CajaFacturacion" 
                            class="w-full">
            </ConfiguracionCaja>
        </vs-row>
        <vs-row class="w-full">
            <vs-col class="sm:w-full md:w-5/12 lg:w-4/12 xl:w-2/12 p-1">
                <vx-card :title="`Seleccione el reporte`" style="font-size:12px;" class="estiloParametros"> 
                    <div>
                        <DxRadioGroup   :items="listas.reportesCortes"
                                        display-expr="Descripcion"
                                        value-expr="Id"
                                        v-model="corte.tipoReporteSeleccionado"
                                        layout="vertical"                                        
                        /> 
                    </div>
                </vx-card>
            </vs-col>
            <vs-col class="sm:w-full md:w-7/12 lg:w-8/12 xl:w-10/12 p-1">
                <vx-card class="estiloParametros">                     
                    <vs-row class="w-full estiloEntradas" >
                        <label style="padding-right:5px; width:150px; text-align: right; vertical-align: middle; color:gray"
                                v-if="parametros.reporte1 || parametros.reporte2 || parametros.reporte3 || parametros.reporte4 || parametros.reporte6 ||  parametros.reporte8 || parametros.reporte9 || parametros.reporte11">Caja: </label>
                        <vs-input  v-model="corte.caja" v-if="parametros.reporte1 || parametros.reporte2 || parametros.reporte3 || parametros.reporte4 || parametros.reporte6 ||  parametros.reporte8 || parametros.reporte9 || parametros.reporte11"
                                   style="width:100px;" />     
                    </vs-row>
                    <vs-row class="w-full estiloEntradas" >
                        <label style="padding-right:5px; width:150px; text-align: right; vertical-align: middle; color:gray"
                               v-if="parametros.reporte1 || parametros.reporte3 || parametros.reporte4 || parametros.reporte9">Corte: </label>
                        <vs-input  v-model="corte.corte" v-if="parametros.reporte1 || parametros.reporte3 || parametros.reporte4 || parametros.reporte9"
                                   style="width:100px;" />    
                        <div class="append-text btn-addon" v-if="parametros.reporte1 || parametros.reporte3 || parametros.reporte4 || parametros.reporte9">
                            <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="CargarNoCortes()" icon="fa-search" :disabled="!ValidarVariable(corte.caja)"></vs-button>
                        </div>
                    </vs-row>
                    <vs-row class="w-full estiloEntradas" >                    
                        <label style="padding-right:5px; width: 150px; text-align: right; vertical-align: middle; color:gray"
                               v-if="parametros.reporte3 || parametros.reporte4 || parametros.reporte5 || parametros.reporte6 ||  parametros.reporte8 || parametros.reporte11" >De: </label>
                        <vs-input type="date" style="width:200px; color: black;" v-model="corte.fechaInicial" 
                               v-if="parametros.reporte3 || parametros.reporte4 || parametros.reporte5 || parametros.reporte6 ||  parametros.reporte8 || parametros.reporte11"/>                          
                    </vs-row>
                    <vs-row class="w-full estiloEntradas" >
                        <label style="padding-right:5px; width: 150px; text-align: right; vertical-align: middle; color:gray" v-if="parametros.reporte3 || parametros.reporte4 || parametros.reporte5 || parametros.reporte6 ||  parametros.reporte8 || parametros.reporte11">A: </label>
                        <vs-input type="date" style="width:200px; color: black;" v-model="corte.fechaFinal" 
                               v-if="parametros.reporte3 || parametros.reporte4 || parametros.reporte5 || parametros.reporte6 ||  parametros.reporte8 || parametros.reporte11"/>                          
                    </vs-row>
                    
                    <vs-row>
                        <label style="padding-right:5px; width: 150px;"></label>
                        <vs-button  @click="GenerarReportesCortes('application/pdf','PDF')" color="danger" type="border" style="margin-right:5px" ><i class="fas fa-file-pdf "></i> Generar</vs-button>
                        <vs-button  @click="GenerarReportesCortes('application/vnd.ms-excel','EXCEL')" color="success" type="border" ><i class="far fa-file-excel"></i> Generar</vs-button>                                          
                    </vs-row>
                    <vs-divider></vs-divider>
                </vx-card>
            </vs-col>
        </vs-row>
    </vx-card>
</template>
<script>
import moment from "moment";
import ConfiguracionCaja from "/src/components/sermesa/modules/caja/ConfiguracionCaja.vue";       

export default{
    name:'REPORTES_CORTES',
    components:{
        ConfiguracionCaja    
    },
    data(){
        return{  
            FormatoReporte:'application/pdf',
            VentanEmergenteNoCorte:false,
            configuracion:{
                cajaSeleccionada:null,
                activarFacturacion: false,
                activarRecibos: false  
            },
            permisos:{
                reporte0:false,
                reporte1:false,
                reporte2:false,
                reporte3:false,
                reporte4:false,
                reporte5:false,
                reporte6:false,
                reporte8:false,
                reporte9:false,
                reporte11:false
            },
            parametros:{
                reporte0:false,
                reporte1:false,
                reporte2:false,
                reporte3:false,
                reporte4:false,
                reporte5:false,
                reporte6:false,
                reporte8:false,
                reporte9:false,
                reporte11:false
            },          
            corte:{
                tipoReporteSeleccionado:null,
                caja:null,
                corte:null,
                fechaCorte:null,
                fechaInicial:null,
                fechaFinal:null,
                chequesPrevisados:false,
                NoCortes:[]
            },
            listas:{
                reportesCortes:[{Id:0,Descripcion:'0. Corte de Caja',disabled:true},
                                {Id:1,Descripcion:'1. Resumen de Corte de Caja',disabled:true},
                                {Id:2,Descripcion:'2. Corte Parcial de Caja',disabled:true},
                                {Id:3,Descripcion:'3. Facturas Emitidas',disabled:true},
                                {Id:4,Descripcion:'4. Recibos Emitidos',disabled:true},
                                {Id:5,Descripcion:'5. Arqueos de Cheques',disabled:true},
                                {Id:6,Descripcion:'6. Arqueo de Tarjetas',disabled:true},
                                {Id:8,Descripcion:'8. Arqueo de Exenciones',disabled:true},
                                {Id:9,Descripcion:'9. Recalcular Corte',disabled:true},
                                {Id:11,Descripcion:'11. Arqueo de Retenciones',disabled:true}]
            }
        }
    },
    methods:{
        actualizarCajero(){  
            this.$refs.ConfiguracionCajaRef.CargarCajaInicial();
        },
        async GenerarReporteCorte(formatoInterno,formato) {            
            let reporte = "Corte de Caja"
            let nombreReporte;
            let opcionInterna = this.corte.tipoReporteSeleccionado;

            if(this.corte.tipoReporteSeleccionado < 1){
                this.Consulta().MostrarError('Seleccione una opcion para generar el reporte')
                return
            }else if(this.corte.tipoReporteSeleccionado == 1){
                nombreReporte = 'ResumenCorteCaja'
            }else if(this.corte.tipoReporteSeleccionado == 2){
                nombreReporte = 'CorteParcialCaja'
            }else if(this.corte.tipoReporteSeleccionado == 3){
                nombreReporte = 'FacturasEmitidas'
            }else if(this.corte.tipoReporteSeleccionado == 4){
                nombreReporte = 'RecibosEmitidos'
            }else if(this.corte.tipoReporteSeleccionado == 5){
                nombreReporte = 'ArqueoDeCheque'
            }else if(this.corte.tipoReporteSeleccionado == 6){
                nombreReporte = 'ArqueoDeTarjetas'
            }else if(this.corte.tipoReporteSeleccionado == 8){
                nombreReporte = 'ArqueoDeExenciones'
                opcionInterna = 7
            }else if(this.corte.tipoReporteSeleccionado == 9){
                nombreReporte = 'RecalcularCorte'
                opcionInterna = 8
            }else if(this.corte.tipoReporteSeleccionado == 11){
                nombreReporte = 'ArqueoDeRetenciones'
                opcionInterna = 9
            }

            let postData = {
                caja: this.corte.caja ,
                corte: this.corte.corte,
                fechaCorte: this.corte.fechaCorte,
                fechaInicial: this.corte.fechaInicial,
                fechaFinal: this.corte.fechaFinal,
                subOpcion: opcionInterna,
                nombrereporte:nombreReporte,
                tiporeporte: formatoInterno
            }


            this.$reporte_modal({
                Nombre: reporte,
                Opciones: {
                    ...postData
                },
                Formato:formato
            })                

        },    
        CargarPermisos(){
            this.permisos.reporte0 = this.$validar_privilegio('CORTEDECAJA').status
            this.permisos.reporte1 = this.$validar_privilegio('RESUMENCORTEDECAJA').status
            this.permisos.reporte2 = this.$validar_privilegio('CORTEPARCIALDECAJA').status
            this.permisos.reporte3 = this.$validar_privilegio('FACTURASEMITIDAS').status
            this.permisos.reporte4 = this.$validar_privilegio('RECIBOSEMITIDOS').status
            this.permisos.reporte5 = this.$validar_privilegio('ARQUEOSCHEQUES').status
            this.permisos.reporte6 = this.$validar_privilegio('ARQUEOTARJETAS').status
            this.permisos.reporte8 = this.$validar_privilegio('ARQUEOEXCENCIONES').status
            this.permisos.reporte9 = this.$validar_privilegio('RECALCULARCORTES').status
            this.permisos.reporte11 = this.$validar_privilegio('ARQUEORETENCIONES').status
            this.listas.reportesCortes =
                this.listas.reportesCortes.map(reporte=>{reporte.disabled = reporte.Id == 0 && this.permisos.reporte0 ? false
                                                                           :reporte.Id == 1 && this.permisos.reporte1 ? false
                                                                           :reporte.Id == 2 && this.permisos.reporte2 ? false
                                                                           :reporte.Id == 3 && this.permisos.reporte3 ? false
                                                                           :reporte.Id == 4 && this.permisos.reporte4 ? false
                                                                           :reporte.Id == 5 && this.permisos.reporte5 ? false
                                                                           :reporte.Id == 6 && this.permisos.reporte6 ? false
                                                                           :reporte.Id == 8 && this.permisos.reporte8 ? false
                                                                           :reporte.Id == 9 && this.permisos.reporte9 ? false
                                                                           :reporte.Id == 11 && this.permisos.reporte11 ? false
                                                                           :true
                                                         return reporte
                                                        }) 
        },
        CargarCaja(cajaSeleccionada){                
            this.configuracion.cajaSeleccionada   = cajaSeleccionada.Cajero
            this.configuracion.activarFacturacion = cajaSeleccionada.Factura
            this.configuracion.activarRecibos     = cajaSeleccionada.Recibo
        },
        GenerarReportesCortes(formatoInterno,formato){
            if(this.corte.tipoReporteSeleccionado == 0){
                this.GenerarCorte0();
            }else{                
                this.GenerarReporteCorte(formatoInterno,formato);
            }
        },
        async Fel(serieFactura,numeroFactura){
            let respuesta = false;

            await this.axios.post('/app/v1_FacturaElectronica/GeneraFel',
                            {   serieFactura: serieFactura,
                                numeroFactura: numeroFactura
                            })                                  
                        .then(resp=>{
                                if(resp.data.codigo == 0 && resp.data.json[0].codigo == 0){
                                    respuesta = true;                                                                            
                                }else{                                   
                                    this.$vs.dialog({
                                                    type: 'confirm',
                                                    color: 'danger',
                                                    title: 'Error en '+serieFactura+'-'+numeroFactura,
                                                    acceptText: 'Aceptar',
                                                    cancelText: 'Cancelar',
                                                    text: resp.data.json[0].descripcion,                                               
                                                })
                                }
                            })                       

            return respuesta          
        },        
        async GenerarCorte0(){
            if(!this.configuracion.cajaSeleccionada || !this.configuracion.cajaSeleccionada.CajaLocal){
                this.Consulta().MostrarError('Configurar una caja para relizar el corte')
                return
            }
            let facturas = await
            this.axios.post('/app/v1_caja/ReportesCortes',{
                                caja:this.configuracion.cajaSeleccionada.CajaLocal,
                                opcion:'I',
                                subOpcion:this.corte.tipoReporteSeleccionado
                            })
                      .then(resp=>{
                            //SELECT 0 as codigo, 'Corte ' + Cast(@SiguienteCorte as Varchar(12)) + ' realizado' AS descripcion, 0 AS tipo_error
                            if(resp.data.json[0].tipo_error && resp.data.json[0].tipo_error == 0){
                                this.Consulta().MostrarSatisfactorio(resp.data.json[0].descripcion)
                                return null
                            }else if(resp.data.json[0].tipo_error && resp.data.json[0].tipo_error < 0){
                                this.Consulta().MostrarError(resp.data.json[0].descripcion)
                                return null
                            }else{
                                this.$vs.dialog({
                                                    type: 'confirm',
                                                    color: 'success',
                                                    title: 'Sincronización',
                                                    acceptText: 'Aceptar',
                                                    cancelText: 'Cancelar',
                                                    text: 'Sincronizando Facturas, si no hay ningun problema al finalizar, volver a realizar el corte.',                                               
                                                })   
                                return resp.data.json                            
                            }
                      })
            
            if(facturas){ // sincronizacion de facturas
                for(const factura of facturas){
                    let respuesta = await this.Fel(factura.Serie,factura.Codigo)
                    if(!respuesta) break; // finaliza la sincronizacion
                }
            }
        },
        Consulta(){
            return{
                MostrarError:(mensaje,tiempo=2000)=>{
                    this.$vs.notify({
                        time:tiempo,
                        title: 'Cortes',
                        color: 'danger',
                        text:mensaje,
                        position: 'top-center'
                    })
                },
                MostrarSatisfactorio:(mensaje)=>{
                    this.$vs.notify({
                        time:2000,
                        title: 'Cajero',
                        color: 'success',
                        text:mensaje,
                        position: 'top-center'
                    })
                }
            }
        },
        ValidarVariable(entrada){   
            if(typeof(entrada) == 'number'){
                return entrada ? true : false;
            }else if(typeof(entrada) == 'string'){
                return entrada && entrada.length > 0 ? true : false;
            }else if(typeof(entrada) == 'object'){
                return entrada ? true : false;
            }            
            return false;
        },
        GuardarNoCorte(Corte){
            this.corte.corte = Corte.Codigo
            this.corte.fechaCorte = null
            this.VentanEmergenteNoCorte = false
        },
        CargarNoCortes(){
            this.axios.post('/app/v1_caja/ReportesCortes',{
                                opcion:'C',
                                caja:this.corte.caja,
                                fechaCorte:this.corte.fechaCorte,
                                subOpcion:0
                            })
                      .then(resp=>{
                            this.corte.NoCortes = resp.data.json 
                            this.VentanEmergenteNoCorte = true
                            setTimeout(()=>{
                                document.getElementById("fechaCorteId").focus();
                            },100)                           
                      })
        }
    },
    async mounted(){
        this.CargarPermisos();

        this.corte.fechaInicial = moment(await this.$dbdate()).format('YYYY-MM-DD');
        this.corte.fechaFinal = moment(await this.$dbdate()).format('YYYY-MM-DD');        
    },
    activated() {
        window.addEventListener('focus', this.actualizarCajero);   
    },
    deactivated() {
        window.removeEventListener('focus',this.actualizarCajero);
    },
    computed:{
        sesion() {
            return this.$store.state.sesion
        }
    },
    watch:{
        VentanEmergenteNoCorte(valor){
            if(!valor){
                this.corte.fechaCorte = null
            }
        },
        'corte.tipoReporteSeleccionado'(reporte){
            this.parametros.reporte0 = false
            this.parametros.reporte1 = false
            this.parametros.reporte2 = false
            this.parametros.reporte3 = false
            this.parametros.reporte4 = false
            this.parametros.reporte5 = false
            this.parametros.reporte6 = false
            this.parametros.reporte8 = false
            this.parametros.reporte9 = false
            this.parametros.reporte11 = false

            if(reporte==0){ this.parametros.reporte0 = true }
            if(reporte==1){ this.parametros.reporte1 = true }
            if(reporte==2){ this.parametros.reporte2 = true }
            if(reporte==3){ this.parametros.reporte3 = true }
            if(reporte==4){ this.parametros.reporte4 = true }
            if(reporte==5){ this.parametros.reporte5 = true }
            if(reporte==6){ this.parametros.reporte6 = true }
            if(reporte==8){ this.parametros.reporte8 = true }
            if(reporte==9){ this.parametros.reporte9 = true }
            if(reporte==11){ this.parametros.reporte11 = true }
        }
    }
}
</script>
<style>
    .estiloEntradas{
        padding-bottom:15px;
        height:60px;
    }

    .dx-radiobutton{
        padding-bottom:10px;
    }

    @media (min-width: 1500px) {
        .estiloParametros{
            height:500px;
        }
    }
</style>