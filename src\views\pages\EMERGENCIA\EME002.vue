<template>
    <div>
        <div class="containerem">   
            <div class="tipo-reporte">
                <vs-row>
                    <vs-col>
                        <div class="example ex1 pl-10">
                            <!-- <h3>Selección impresión Estado de exámenes</h3> -->
                            <br>
                            <br>
                            <div class="radio-container">
                                <label class="radio blue">
                                    <input v-model="optionChecked" type="radio" name="group1" value="internos" />
                                    <span>Pacientes Internos </span>
                                </label>
                                <label class="radio blue">
                                    <input v-model="optionChecked" type="radio" name="group1" value="internosFormulario" />
                                    <span>Pacientes Internos Formulario</span>
                                </label>
                                <label class="radio blue">
                                    <input v-model="optionChecked" type="radio" name="group1" value="altas" />
                                    <span>Altas </span>
                                </label>
                                <label class="radio blue">
                                    <input v-model="optionChecked" type="radio" name="group1" value="admisionesDiarias" />
                                    <span>Admisiones Diarias</span>
                                </label>
                                <label class="radio blue">
                                    <input v-model="optionChecked" type="radio" name="group1" value="nitsFacturados" />
                                    <span>Nits Facturados</span>
                                </label>
                                <label class="radio blue">
                                    <input v-model="optionChecked" type="radio" name="group1" value="admsionesCorrelativas" />
                                    <span>Admisiones Correlativas</span>
                                </label>
                            </div>
                            <p :style="{ color: $options.COLOR }">
                            </p>
                        </div>
                    </vs-col>

                </vs-row>


            </div>
            <div class="filtro-sucursal p-4"  
            :class="{ 'disabled-div': 
                    optionChecked != 'internos' 
                &&  optionChecked != 'internosFormulario'
                &&  optionChecked != 'admsionesCorrelativas' }">

              
                    <vs-row>
                        <vs-col vs-w="2">
                            <SM-Buscar
                              v-model ="sucursalSeleccionada"
                              label="Sucursal"
                              api="app/emergencia/ConsultarSucursalesActivas"                              
                              :api_campos="['Codigo', 'CodigoBase', 'Nombre']"
                              :api_titulos="['Codigo', 'CodigoBase', 'Nombre']"
                              api_campo_respuesta="Codigo"
                              :api_preload="true"
                              :disabled_texto="false"
                              :api_filtro="{ Opcion: 5 }"
                              :callback_buscar="ConsultarDatosSucursal"
                              />
                        </vs-col>
                        <vs-col vs-w="6" class="pl-4">

                            <vs-input label="Nombre" class="w-full" v-model="this.NombreSucursal" readonly />

                        </vs-col>
                    </vs-row>

                        <div v-if=" optionChecked == 'admsionesCorrelativas' "
                            class="w-full md:w-full lg:w-10/12 xl:w-4/12 m-2 pt-2  mt-10">
                            <label >Tipo de Admisión...</label>
                            <multiselect v-model="selTipoAdmision" 
                                :options="TipoDeAdmision"  
                                :allow-empty="true"
                                :taggable="true" 
                                placeholder="Selección tipo de Admisión" 
                                track-by="Nombre" 
                                label="Nombre"
                                class="pt-2">
                            </multiselect>
                        </div>
            </div>

            <div class="filtro-fechas">
                <div v-if="optionChecked ==='internos'"
                class="w-full md:w-full lg:w-10/12 xl:w-6/12 m-2 pt-8">
                    <label class="vs-input--label">Tipo Ordenamiento </label>
                    <multiselect v-model="selectorOrden" :options="tiposOrdenamiento"  :allow-empty="false"
                        :taggable="true" placeholder="Seleccione tipo de ordenamiento" track-by="Nombre" label="Nombre">
                    </multiselect>
                </div>

                <div v-if="optionChecked !=='internos'  &&  optionChecked != 'internosFormulario'"
                    class="w-full md:w-full lg:w-10/12 xl:w-4/12 m-2 pt-2  mt-10">
                    <div class="datepicker-container">
                        <label class="pr-4">Fecha Inicial:</label>
                        <datepicker v-model="FechaInicial" placeholder="Fecha Inicial" :format="customFormatter"  ></datepicker>
                    </div>

                    <div class="datepicker-container"
                    v-if=" optionChecked != 'altas' && optionChecked !='admisionesDiarias'  &&  optionChecked != 'internosFormulario'"                   
                    >
                        <label class="pr-4">Fecha Final:</label>
                        <datepicker v-model="FechaFinal" placeholder="Fecha Final" :format="customFormatter"></datepicker>
                    </div>
                </div>

                <div v-if="optionChecked ==='admsionesCorrelativas'" class="flex  mb-4">
                  
                        <div class="w-full md:w-full lg:w-10/12 xl:w-4/12 m-2 pt-2  mt-10">
                            <label >Filtrar Admisiones...</label>
                            <multiselect v-model="selEstadoAdmisiones" 
                                :options="EstadosAdmisiones"  
                                :allow-empty="false"
                                :taggable="true" 
                                placeholder="Selección Status Admisiones" 
                                track-by="Nombre" 
                                label="Nombre"
                                class="pt-2">
                            </multiselect>

                        </div>

                         <div  class="w-full md:w-full lg:w-10/12 xl:w-4/12 m-2 pt-2  mt-10">
                    
                            <label class="pl-6">Descartar Admisiones...</label>
                            <multiselect v-model="selAdmisionesExcluidas" 
                                :options="AdmisionesExcluidas"  
                                :allow-empty="false"
                                :taggable="true"
                                placeholder="Selección Admisiones a Excluir" 
                                track-by="Nombre" 
                                label="Nombre"
                                class="pl-4 pt-2">
                            </multiselect>
                        </div>
                 
                </div>
            </div>
                        
            <div class="otros-filtros">
                <div class="sm:w-full md:w-full lg:w-full xl:w-full"
                        style="padding:9px;border:1px solid #ccc;border-radius:5px">
                        <label class="vs-input--label">Destino del reporte</label><br />
                        <vs-radio class="pr-6" v-model="FormatoReporte" vs-value="PDF">Generar en PDF</vs-radio>
                        <vs-radio class="pr-6" v-model="FormatoReporte" vs-value="EXCEL">Exportar a Excel</vs-radio>
                    </div>

                <vs-row class="pt-4">

                    <h3 class="pl-4">Generar Impresión</h3>
                    <br>
                    <br>

                    <br>
                    <vs-col vs-type="flex" vs-w="12">
                        <vs-col vs-type="flex" class="ml-6">
                            <vs-button class="block" @click.native="GenerarReporte()">
                                <i class="fas fa-file-pdf height:25px"></i>
                                Generar
                            </vs-button>
                        </vs-col>
                    </vs-col>
                </vs-row>

            </div>
        </div>

    </div>
</template>

<script>
//  sermesa\global\radioButtons\BaseRadioButtonGroup.vue
//import BaseCheckboxGroup from "@/components/sermesa/global/radioButtons/BaseRadioButtonGroup.vue"
import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";
import Datepicker from 'vuejs-datepicker';
import * as lang from 'vuejs-datepicker/src/locale';
import moment from "moment";


const REPORTE_PACIENTES_INTERNOS = 'GeneraReportePacientesInternos'
const REPORTE_ALIAS = 'GeneraReporteAlias'
const REPORTE_ADMISIONES_DIARIAS = 'GeneraReporteAdmisionesDiarias'




export default {
    name: "ReportesAdmisiones",
    components: {
        Multiselect,
        Datepicker
    },
    data() {
        return {
            checkboxOptions: [],
            optionChecked: '',
            sucursalSeleccionada: 'HLA',
            selectorOrden: '',
            languages: lang,
            FormatoReporte:'EXCEL',
            NombreSucursal: ' ',
            tiposOrdenamiento: [
                {
                    Codigo: '1',
                    Nombre: 'Apellido'
                },
                {
                    Codigo: '2',
                    Nombre: 'Habitacion'
                },
                {
                    Codigo: '3',
                    Nombre: 'Medico'
                },
                {
                    Codigo: '4',
                    Nombre: 'Apellido de Casada'
                }
            ],
            TipoDeAdmision:[
                {
                    Codigo:null,
                    Nombre:'Todas'
                },
                {
                    Codigo:1,
                    Nombre:'Internas'
                },
                {
                    Codigo:2,
                    Nombre:'Externas'
                },
                {
                    Codigo:3,
                    Nombre:'Emergencia'
                }
            ],
            EstadosAdmisiones:[
                {
                    Codigo:null,
                    Nombre:'Todas'
                },
                {
                    Codigo:1,
                    Nombre:'Admisiones activas'
                },
                {
                    Codigo:2,
                    Nombre:'Admisiones cerradas'
                },
                {
                    Codigo:3,
                    Nombre:'Admisiones facturadas'
                },
                {
                    Codigo:4,
                    Nombre:'Admisiones no facturadas'
                },
                {
                    Codigo:5,
                    Nombre:'Admisiones auditadas'
                }

            ],
            AdmisionesExcluidas:[
                {
                    Codigo:null,
                    Nombre:'Todas'
                },
                {
                    Codigo:1,
                    Nombre:'Admisiones sin cargos de hospital'
                },
                {
                    Codigo:2,
                    Nombre:'Admisiones auditadas'
                },
                {
                    Codigo:3,
                    Nombre:'Admisiones de Planes directos'
                }
            ],
            selTipoAdmision         :'',
            selEstadoAdmisiones     :'',
            selAdmisionesExcluidas  :'',
            ReportePacienteInternos : REPORTE_PACIENTES_INTERNOS,
            ReporteAdmisionesDiarias: REPORTE_ADMISIONES_DIARIAS,
            ReporteAlias: REPORTE_ALIAS,
            FechaInicial: '',//new Date('2018-06-22'),
            FechaFinal:''

            

        }
    },
    watch: {
        'optionChecked': function () {

            if( this.optionChecked ==='internos' ){
                this.FechaFinal =''
            }
            if( this.optionChecked !=='internos' ){
                this.sucursalSeleccionada =''
                this.selectorOrden = ''

            }

        }
    },
    mounted: function () {
        this.setDate()
    },
    methods: {
        ConsultarDatosSucursal(datos) {

            this.NombreSucursal = datos.Nombre
            this.sucursalSeleccionada = datos.Codigo
        },

        
        setDate(){
            this.FechaInicial = new Date();
            return moment(this.FechaInicial).format('DD/MM/YYYY');
        },
        customFormatter(date) {
            return moment(date).format('DD/MM/YYYY');
        },
        async ConsultaSucursalesActivas() {

            const resp = await this.axios.post('/app/emergencia/ConsultarSucursalesActivas',
                {
                    "Opcion": '0',
                    "EmpresaUnificadora": '',
                    "EmpresaReal": '',
                    "Sucursal":
                     ''
                })

            this.lista_resultados = resp.data.json.map(m => {

                return {
                    ...m,
                }
            })
        },        
        async GenerarReporte() {

            let reporte 
            switch (this.optionChecked) {
                case 'internos':
                    reporte  = "ReportePacientesInternos"
                    break;
                    case 'internosFormulario':
                    reporte  = "ReportePacientesInternosFormulario"
                    break;
                case 'altas':
                    reporte  = "ReporteAltas"
                    break;
                case 'admisionesDiarias':
                    reporte  = "ReporteAdmisionesDiarias"
                    break;
                case 'nitsFacturados':
                    reporte  = "ReporteNitsFacturados"
                    break;
                case 'admsionesCorrelativas':
                    reporte  = "ReporteAdmisionesCorrelativas"
                    break;

                default:
                // code block
            }
           
            if(reporte === 'ReporteAltas' || reporte === "ReporteAdmisionesDiarias" ){
                this.FechaFinal = this.FechaInicial
            }

            if(reporte  === "ReportePacientesInternos"){
                this.FechaFinal =''
            }

            const fechaInicial = moment(this.FechaInicial)
            const fechaFinal   = moment(this.FechaFinal)

            let postData = {
                Sucursal:   this.sucursalSeleccionada ,
                OrdenarPor: this.selectorOrden !== '' ?this.selectorOrden.Codigo:0,
                fechainicio:fechaInicial.isValid()? moment(this.FechaInicial).format('YYYY-MM-DD HH:mm:ss'):'',
                fechafin:   fechaFinal.isValid()? moment(this.FechaFinal).format('YYYY-MM-DD HH:mm:ss'):'',
                TiposAdmisiones:this.selTipoAdmision !== null ? this.selTipoAdmision.Codigo : null,
                EstatusAdmisiones:  this.selEstadoAdmisiones !== null ? this.selEstadoAdmisiones.Codigo : null,
                AdmisionesExcluidas:this.selAdmisionesExcluidas !== null ? this.selAdmisionesExcluidas.Codigo :null
            }


            if( reporte  !== "ReportePacientesInternos" && this.FechaInicial === 'undefined' ) {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Alerta',
                    text: 'Fecha Final es requerida',
                })
                return
            }
            this.$reporte_modal({
                Nombre: reporte,
                Opciones: {
                    ...postData
                },
                Formato:this.FormatoReporte
            })
        }
    }
}



</script>

<style scoped>
* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

.containerem {
    display: grid;
    grid-gap: 5px;
    width: 100%;
    height: 100%;
    grid-template-areas: "tipo-reporte filtro-sucursal"
        "tipo-reporte filtro-fechas"
        "tipo-reporte otros-filtros";
    grid-template-columns: 0.3fr 1fr;
    grid-template-rows: 5fr 5fr 3fr;
}

.containerem>div {
    border: 1px solid #888;
}

.tipo-reporte {
    grid-area: tipo-reporte;
}

.filtro-sucursal {
    grid-area: filtro-sucursal;
}

.filtro-fechas {
    grid-area: filtro-fechas;
}

.otros-filtros {
    grid-area: otros-filtros;
}

.containerem {
    max-width: 100%;
}

.example {
    margin: 20px;
}

.example input {
    display: none;
}

.example label {
    margin-right: 20px;
    display: inline-block;
    cursor: pointer;
    padding: 5px;
}

.ex1 span {
    display: block;
    padding: 15px 80px 15px 45px;
    border: 2px solid #ddd;
    border-radius: 5px;
    position: relative;
    transition: all 0.25s linear;
}

.ex1 span:before {
    content: '';
    position: absolute;
    left: 5px;
    top: 50%;
    -webkit-transform: translatey(-50%);
    transform: translatey(-50%);
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #ddd;
    transition: all 0.25s linear;
}

.ex1 input:checked+span {
    background-color: #fff;
    box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.1);
}

.ex1 .blue input:checked+span {
    color: rgb(96, 162, 203);
    border-color: rgb(143, 143, 160);
}

.ex1 .blue input:checked+span:before {
    background-color: rgb(98, 155, 209);
}

.example.ex1 .radio-container {
    display: flex;
    flex-direction: column;
}


.example.ex1 {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}


.datepicker-container {
    display: flex;
    flex-wrap: wrap;
    /* Permite que los datepickers se envuelvan en pantallas pequeñas */
    justify-content: flex-start;


}

.datepicker-container datepicker {
    margin-right: 5px;
    flex: 1;
    /* Hace que los datepickers se expandan para ocupar el espacio disponible */

    /* Ajusta el espacio entre los datepickers si es necesario */
}

.disabled-div {
  opacity: 0.5; /* Cambia la opacidad para dar la apariencia de estar desactivado */
  pointer-events: none; /* Evita interacciones con el div */
}


</style>