<template>
    <vx-card title="Consumos por Departamento">
 
        <div class="container">    
            <div class="a-167EA">
                <div class="w-full md:w-full lg:w-10/12 xl:w-6/12  flex items-center justify-center m-4">
                    <label>Fecha Inicial:</label>
                    <datepicker v-model="FechaInicial" placeholder="Fecha Inicial" :format="customFormatter"></datepicker>
                </div>
            </div>
            <div class="a-gpX84">
                <div class="w-full md:w-full lg:w-10/12 xl:w-6/12  flex items-center justify-center  m-4">
                    <label>Fecha Final:</label>
                    <datepicker v-model="FechaFinal" placeholder="Fecha Final" :format="customFormatter"></datepicker>
                </div>
            </div>
            <div class="a-79JYK">

                <vs-row>
                    <vs-col vs-type="flex" vs-w="6" class="m-4">

                            <div class="ws-full">                                
                                <vs-input label="Producto" class="w-full" v-model="this.CodigoProducto" />                                
                            </div>
                        
                          <SM-Buscar
                            class="w-full"
                            label="Decripción Producto"
                            v-model="CodigoProducto"
                            :disabled_busqueda="activarBusquedaProd"
                            api="app/inventario/ListaProductosInventario"
                            api_campo_respuesta_mostrar="Nombre" 
                            :api_campos="['Codigo', 'Nombre']"
                            :api_titulos="['Codigo', 'Nombre']" 
                            api_campo_respuesta="Codigo"
                            :api_campo_respuesta_estricto="false" 
                            :api_preload="false"
                            :disabled_texto="true"
                            :api_filtro="{                                            
                                Nombre: this.TextoBusqueda,
                                Tipo: 'P', 
                                Activo:'A'                                
                            }"                            
                            :mostrar_busqueda="true"                            
                            :callback_buscar="cargarProducto"                                                        
                            />                                                                                       
                    </vs-col>                     
                    <vs-textarea resize:none  id="listaProductos" v-model="listaDeProductos"   rows=2 class="m-4"/>
                </vs-row>


            </div>
            <div class="a-83YWL">

                <vs-row>
                    <div class="w-full md:w-full lg:w-10/12 xl:w-6/12  flex items-center justify-center" >                            
            
                    <vs-col vs-type="flex" vs-w="12" class="m-4">
                        <div class="w-3/5">
                            <label style="font-size:10px">Departamentos</label>
                            
                                <multiselect v-model="selectDepartamento" 
                                    :options="listaDepartamentos" 
                                    :searchable="true" 
                                    :close-on-select="true" 
                                    :show-labels="false" 
                                    :custom-label="departamento_seleccionada" 
                                    placeholder="Seleccionar" 
                                    @input="onChangedepartamento"
                                    deselect-label="Seleccionado"
                                    :clear-on-select="true"
                                    @select="onSelectDepto($event)"                                     
                                >
                                    <span slot="noOptions">Lista no disponible.</span>
                                </multiselect>
                            <!-- <multiselect
                                v-model="selectDepartamento"
                                :options="ListaDepartamentos"
                                :allow-empty="false"
                                placeholder="Seleccion departamento"
                                open-direction="bottom"
                                track-by="NOMBRE"
                                label="CODIGO"
                                deselect-label="Seleccionado"
                                :clear-on-select="true"
                                @select="onSelectDepto($event)"
                            >
                            </multiselect> -->

                        </div>
                    </vs-col>
                </div>                       
                    <vs-textarea resize:none  id="listaDeptosBodega" v-model="listaDeptos"  rows=2  class="m-4"/>
                </vs-row>         
            </div>

            <div class="a-BrEO0">
                <vs-row class="m-2">
                    <vs-col vs-type="flex" vs-w="12">
                        <vs-col vs-type="flex" class="m-2">

                            <vs-button class="block" @click.native="GenerarReporte()">
                                <i class="fas fa-file-pdf height:25px"></i>
                                Generar
                            </vs-button>
                        </vs-col>
                    </vs-col>
                </vs-row>
            </div>
        </div>

    </vx-card>
</template>



<script>

import Datepicker from 'vuejs-datepicker';
import * as lang from 'vuejs-datepicker/src/locale';
import moment from "moment";
import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";


export default {


    components: {
        Datepicker,        
        Multiselect
    },
    data() {
        return {
            languages: lang,
            FechaInicial: '',
            FechaFinal: '',
            Bodega: 0,
            NombreBodega: '',
            CodigoProducto: '',
            TextoBusqueda:'', 
            activarBusquedaProd:false,            
            listaDeProductos:'',
            listaDeptos:'',
            selectDepartamento:'',
            listaDepartamentos: [],
        }

    },
    mounted: function () {
        this.setinitialDate()
        this.setendDate();     
        this.listarDepartamentos()   
    },
    methods: {

        setinitialDate() {
            this.FechaInicial = new Date();
            return moment(this.FechaInicial).format('DD/MM/YYYY');
        },
        setendDate() {
            this.FechaFinal = new Date();
            return moment(this.FechaFinal).format('DD/MM/YYYY');
        },
        customFormatter(date) {
            return moment(date).format('DD/MM/YYYY');
        },
        cargarProducto(prods) {
            this.fillProducts(prods.Codigo)      
        },
        fillProducts(producto) {
            this.CodigoProducto =''
            
            const concatProds = this.listaDeProductos.length > 0 ?  '|' +producto : producto

            this.listaDeProductos += concatProds
        },
        onChangedepartamento(value) {
            if (value !== null && value.length !== 0) {
                this.idDepartamento = value.CODIGO;
                this.departamento = value.NOMBRE;
                this.habilitarNueva = true
            } else {
                this.idDepartamento = '';
                this.departamento = '';
            }
        },
        departamento_seleccionada({
            CODIGO,
            NOMBRE
        }) {
            return `${CODIGO} - ${NOMBRE} `
        },
        async listarDepartamentos() {
            this.axios.post('/app/v1_OrdenCompra/consulta_departamento', {})
                .then(resp => {                    
                    if (resp.data.codigo != 0) {
                        this.listaDepartamentos = "";
                    } else {                        
                        this.listaDepartamentos = resp.data.json;                        
                    }
                })
                .catch(() => {})                   
        },
        clearAll() {
            this.selectedProducts = []
        },
        onSelectDepto(selectedOption) {        
            this.selectDepartamento =''
            const concatDeptos = this.listaDeptos.length > 0 ?  '|' +selectedOption.CODIGO : selectedOption.CODIGO

            this.listaDeptos += concatDeptos
        },
        async GenerarReporte(){
            
            const FechaInicio = moment(this.FechaInicial, 'ddd MMM DD YYYY HH:mm:ss [GMT]ZZ').format('YYYY-MM-DD');
            const FechaFin = moment(this.FechaFinal, 'ddd MMM DD YYYY HH:mm:ss [GMT]ZZ').format('YYYY-MM-DD');


            let postData = {
                ListaDepartamentos:this.listaDeptos ,
                ListaProductos: this.listaDeProductos,
                FechaInicio:FechaInicio,
                FechaFin : FechaFin
            }
                    
            this.$reporte_modal({
                    Nombre: 'ReporteConsumosPorDepartamento',
                    Opciones: {
                    ...postData
                    },
                    Formato:'EXCEL'
            })

        }

    }

}


</script>



<style scoped>

* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

.container {
  grid-gap: 5px;
  display: grid;
  width: 100%;
  height: 100%;
  grid-template-areas: "a-167EA a-gpX84"
  "a-79JYK a-79JYK"
  "a-83YWL a-83YWL"
  "a-BrEO0 a-BrEO0";
  grid-template-columns: 0.5fr 0.5fr;
  grid-template-rows: 0.5fr 0.5fr 0.5fr 0.25fr;
}
.container > div {
  border: 1px solid #888;
}

.a-167EA {
  grid-area: a-167EA;
}
.a-gpX84 {
  grid-area: a-gpX84;
}
.a-79JYK {
  grid-area: a-79JYK;
}
.a-83YWL {
  grid-area: a-83YWL;
}
.a-BrEO0 {
  grid-area: a-BrEO0;
}

.container {
        max-width: 100%;
}


</style>