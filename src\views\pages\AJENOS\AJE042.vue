<template>
    <vx-card type="2" :title="`Devolución Cargos Farmacia`">
        <ValidationObserver ref="formValidate" v-slot="{  handleSubmit }" mode="lazy">
            <form ref="formAnulacion" method="post" @submit.prevent="handleSubmit(anular_orden())">
                <div>
                    <vs-tabs :color="colorx">
                        <!-- INICIO VALE ELECTRONICO DEVOLUCIONES-->
                        <vs-tab label="Cargos Vale Electrónico" :disabled="!permisos.vale_elec_devolucion" icon="account_balance" @click="colorx = '#FFA500';Limpiar()">
                            <div class="flex flex-wrap">
    
                                <div class="sm:w-full md:w-full lg:w-1/2 xl:w-1/2 p-0">
                                    <p><b>Habitación:</b> {{ info.habitacion }}</p>
                                </div>
                            </div>
    
                            <!--- BUSQUEDA ADMISION--->
                            <BusquedaAdmision ref="componenteAdmisiones" @datos_admision="DatosAdmision" @limpiar_datos_admision="LimpiarPantalla" :api_campos="['Serie','Codigo','Nombres','Apellidos','Habitacion']" :api_titulos="['Serie#','Admision#','Nombres','Apellidos','Habitación']">
                            </BusquedaAdmision>
    
                            <div class="w-full md:w-full lg:w-full xl:w-full m-2">
                                <vs-checkbox v-model="DevolverTodoCargoValeE" @change="SeleccionarTodaOrdenValeE()">Devolver todas las ordenes.</vs-checkbox>
                            </div>
    
                            <!--- DETALLE  ADMISION A DEVOLVER--->
                            <div class="flex flex-wrap mt-4">
                                <div class="w-full md:w-full lg:w-full xl:w-full">
                                    <vs-table2 ref="tablacargos" max-items="10" search pagination :data="ListaCargosValeElec">
                                        <template slot="thead">
                                            <th width="100px">¿Devolver?</th>
                                            <th order="Codigo" width="10%">Producto</th>
                                            <th order="Linea" width="5%">Línea</th>
                                            <th order="Nombre">Nombre del Producto</th>
                                            <th order="UnidadMedida" width="10%">Orden</th>
                                            <th order="Nombre">Fecha asignada</th>
                                            >
                                        </template>
                                        <template slot-scope="{data}">
                                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                                <td>
                                                    <vs-checkbox v-model="data[indextr].seleccion" />
                                                </td>
                                                <vs-td2 width="10%">
                                                    {{ tr.Producto }}
                                                </vs-td2>
                                                <vs-td2 width="5%">
                                                    {{ tr.Linea }}
                                                </vs-td2>
                                                <vs-td2>
                                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">
                                                        <small>
                                                            {{ tr.Nombre }}
                                                        </small>
                                                    </div>
                                                </vs-td2>
                                                <vs-td2 width="10%">
                                                    {{ tr.InfoOrden }}
                                                </vs-td2>
                                                <vs-td2>
                                                    {{ tr.FechaAsignada }}
                                                </vs-td2>
    
                                            </tr>
    
                                        </template>
    
                                    </vs-table2>
                                </div>
    
                            </div>
                            <div class="w-full m-2">
                                <vs-button color="success" class="md:w-full lg:w-4/12 xl:w-1/12 m-2" @click="AbrirCorporativoHuella()">
                                    Devolver Cargo Vale E
                                </vs-button>

                                <vs-button class="md:w-full lg:w-4/12 xl:w-1/12 m-2" color="warning" @click="Limpiar()">
                                    Limpiar Campos
                                </vs-button>
                            </div>
                        </vs-tab>
                                            
                        <vs-tab label="Cargos Manuales" icon="edit"  @click="colorx = '#00C853';Limpiar()">
                            <div class="flex flex-wrap">
                                <div class="md:w-full lg:w-4/12 xl:w-2/12 m-2">
                                    <label>Bodega:</label>
                                    &nbsp;&nbsp;
                                    <label style="color:black">{{info.bodega}}</label>
                                </div>
                            </div>
                            <div class="flex flex-wrap">
                                <div class="md:w-full lg:w-4/12 xl:w-2/12 m-2">
                                    <label>Admision:</label>
                                    &nbsp;&nbsp;
                                    <label style="color:black">{{info.admision}}</label>
                                </div>
                                <div class="md:w-full lg:w-6/12 xl:w-4/12 m-2 lg:pr-6">
                                    <label>Paciente:</label>
                                    &nbsp;&nbsp;
                                    <label style="color:black">{{info.paciente}}</label>
                                </div>
                                <div class="md:w-full lg:w-1/12 xl:w-5/12"></div>
    
                                <div class="md:w-full lg:w-4/12 xl:w-2/12 m-2">
                                    <ValidationProvider rules="required" v-slot="{ errors }">
                                        <SM-Buscar class="w-full" label="*Tipo de Orden" v-model="info.tipoOrden" api="app/v1_JefeCaja/ObtenerTiposOrdenesPorRol" :api_campos="['Codigo','Nombre']" :api_titulos="['Tipo de Orden','Descripción']" :api_filtro="{'Activa':0,'opcion':'C','subOpcion':'3','filtroTipoOrdenHospital':info.filtroTipoOrden.toString(),'filtroTipoOrdenGeneral':info.filtroTipoOrdenGeneral.toString()}" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Codigo" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="true" :callback_buscar="cargar().datos_tipo_orden" :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                                    </ValidationProvider>
                                </div>
    
                                <div class="md:w-full lg:w-6/12 xl:w-4/12 m-2 lg:pr-6">
                                    <vs-input label="Descripción:" class="w-full" :value="info.descripcionOrden" disabled />
                                </div>
                                <div class="md:w-full lg:w-10/12 xl:w-6/12 m-2">
                                    <ValidationProvider rules="required" v-slot="{ errors }">
                                        <vs-input label="*No. Orden" type="number" class="w-full" v-model="info.codigoOrden" :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" v-on:change="CargarOrden" />
                                    </ValidationProvider>
                                </div>
                            </div>
    
                            <div class="w-full md:w-full lg:w-full xl:w-full m-2">
                                <vs-checkbox v-model="DevolverTodoCargoManual" @change="SeleccionarTodaOrdenManual()">Devolver toda la orden.</vs-checkbox>
                            </div>
    
                            <div>
                                <div class="flex flex-wrap mt-4">
                                    <div class="w-full md:w-full lg:w-full xl:w-full">
                                        <vs-table2 ref="tablacargos" max-items="10" pagination :data="ListaCargosManuales">
                                            <template slot="thead">
                                                <th width="100px">¿Devolver?</th>
                                                <th order="Codigo" width="10%">Producto</th>
                                                <th order="Nombre">Nombre del Producto</th>
                                                <th order="UnidadMedida" width="10%">U. Medida</th>
    
                                                <th width="8%">Cant</th>
                                                <th order="Precio" width="12%">CostoEnvio</th>
                                                <th width="15%">Envio</th>
                                                <th width="10%">Bodega</th>
                                            </template>
                                            <template slot-scope="{data}">
                                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                                    <td>
                                                        <vs-checkbox v-model="data[indextr].seleccion" @change="tr.Invalido = false;tr.Cantidad = tr.Cant_Original;  " />
    
                                                    </td>
                                                    <vs-td2 width="10%">
                                                        {{ tr.Producto }}
                                                    </vs-td2>
                                                    <vs-td2>
                                                        <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">
                                                            <small>
                                                                {{ tr.Nombre }}
                                                            </small>
                                                        </div>
                                                    </vs-td2>
                                                    <vs-td2 width="10%">
                                                        {{ tr.UnidadMedida }}
                                                    </vs-td2>
    
                                                    <vs-td2 width="10%">
                                                        <div style="text-align: center;" class="flex flex-wrap w-fit p-1">
                                                            <div v-if="tr.seleccion">
                                                                <vs-input :danger="tr.Invalido" danger-text="Cantidad Inválida" placeholder="Password Danger" min="1" :max="tr.Cant_Original" type="number" class="w-full" v-model="tr.Cantidad" @keyup="tr.Invalido = ValidacionCantidad(tr.Cantidad,tr.Cant_Original)" @blur="tr.Invalido = ValidacionCantidad(tr.Cantidad,tr.Cant_Original)" />
                                                            </div>
    
                                                            <div v-else>
                                                                <vs-input disabled min="1" :max="tr.Cant_Original" type="number" class="w-full" v-model="tr.Cantidad" />
                                                            </div>
                                                        </div>
                                                    </vs-td2>
    
                                                    <vs-td2 width="15%">
                                                        {{parseFloat(tr.CostoConvenio).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                                    </vs-td2>
    
                                                    <vs-td2 width="8%">
                                                        <div style="text-align: center;" class="cantidad">
                                                            <div>{{ tr.IdEnvio }}</div>
                                                        </div>
                                                    </vs-td2>
    
                                                    <vs-td2 width="8%">
                                                        <div style="text-align: center;" class="cantidad">
                                                            <div>{{ tr.BodegaCargo }}</div>
                                                        </div>
                                                    </vs-td2>
    
                                                </tr>
    
                                            </template>
    
                                        </vs-table2>
                                    </div>
    
                                </div>
                            </div>
    
                            <div class="w-full m-2">
    
                                <vs-button color="success" class="md:w-full lg:w-4/12 xl:w-1/12 m-2" @click="GuardarDevolucionCargo()">
                                    Devolver Cargo
                                </vs-button>
    
                                <vs-button class="md:w-full lg:w-4/12 xl:w-1/12 m-2" color="warning" @click="Limpiar()">
                                    Limpiar Campos
                                </vs-button>
                            </div>
                        </vs-tab>
    
                    </vs-tabs>
                </div>
    
                <vs-divider position="left"></vs-divider>
    
            </form>
        </ValidationObserver>

        <ValidarCorporativoHuella ref="componenteValidarCorporativoHuella" :tituloHuella="tituloHuella"  @getCorporativoHuellas="getCorporativoHuellas"></ValidarCorporativoHuella>
        <vs-popup class="popNuevo" title="Información Cargos" :active.sync="VentanaMensajesGenerales" style="z-index:99999" id="3">
    
            <div>
                <div class="sm:w-full md:w-full lg:w-full xl:w-full"><b>Mensaje:</b> {{ MensajeGeneralDato }}
                </div>
                <br>
                <div>
                    <vs-button class="mr-5" color="primary" style="float: right" type="filled" icon-pack="feather" icon="icon-save" @click="VentanaMensajesGenerales=false">
                        Aceptar</vs-button>
                    <vs-button class="mr-5" color="danger" style="float: right" type="border" icon-pack="feather" icon="icon-x" @click="VentanaMensajesGenerales = false">
                        Cancelar</vs-button>
                </div>
            </div>
    
        </vs-popup>
    </vx-card>
    </template>
    
    <script>
    import BusquedaAdmision from "/src/components/sermesa/modules/admisiones/BusquedaAdmision.vue"
    import ValidarCorporativoHuella from "/src/components/validador-huella/ValidarCorporativoHuella.vue" 
    export default {
        components: {
            BusquedaAdmision,
            ValidarCorporativoHuella    
        },
        data() {
            return {
                tituloHuella:'Corporativo Huella Enfermeria',
                huella:{
                    enfermero:null,
                    usuarioEnfermero:null,
                    enfermeroValidado:false,
                    farmacia:null,
                    usuarioFarmacia:null,
                    farmaciaValidado:false
                },
                VentanaMensajesGenerales: false,
                MensajeGeneralDato: false,
                DevolverTodoCargoManual: false,
                DevolverTodoCargoValeE: false,
                colorx: '#00C853',
                info: {
                    serie: null,
                    numeroAdmision: null,
                    habitacion: null,
                    bodega: null,
                    tipoOrden: null,
                    descripcionOrden: null,
                    codigoOrden: null,
                    NumeroOrdenOrig: null,
                    admision: null,
                    paciente: null,
                    razon: null,
                    desactivarBotonAnulacion: true,
                    filtroTipoOrden: [],
                    filtroTipoOrdenGeneral: []
                },
                permisos: {
                    fa_devolucion: false,
                    fc_devolucion: false,
                    fe_devolucion: false,
                    op_devolucion: false,
                    hc_devolucion: false,
                    or_devolucion: false,
                    manuales_devolucion: false,
                    vale_elec_devolucion: false,
                },
    
                observaciones: '',
                TipoCargo: '',
                NumeroCargo: '',
    
                ListaCargosManuales: [],
                ListaCargosValeElec: [],
    
            }
    
        },
        watch: {
            'permisos.fa_devolucion'() {
                if (this.permisos.fa_devolucion) {
                    this.info.filtroTipoOrden.push('FA')
                }
            },
            'permisos.fc_devolucion'() {
                if (this.permisos.fc_devolucion) {
                    this.info.filtroTipoOrden.push('FC')
                }
            },
            'permisos.fe_devolucion'() {
                if (this.permisos.fe_devolucion) {
                    this.info.filtroTipoOrden.push('FE')
                }
            },
            'permisos.op_devolucion'() {
                if (this.permisos.op_devolucion) {
                    this.info.filtroTipoOrden.push('OP')
                }
            },
            'permisos.hc_devolucion'() {
                if (this.permisos.hc_devolucion) {
                    this.info.filtroTipoOrden.push('HC')
                }
            },
            'permisos.or_devolucion'() {
                if (this.permisos.or_devolucion) {
                    this.info.filtroTipoOrden.push('OR')
                }
            }                
        },
        mounted() {
            this.info.filtroTipoOrden = []
            this.info.filtroTipoOrdenGeneral = []
            this.permisos.fa_devolucion = this.$validar_privilegio('FA_DEVOLUCION').status
            this.permisos.fc_devolucion = this.$validar_privilegio('FC_DEVOLUCION').status
    
            this.permisos.fe_devolucion = this.$validar_privilegio('FE_DEVOLUCION').status
            this.permisos.op_devolucion = this.$validar_privilegio('OP_DEVOLUCION').status
    
            this.permisos.hc_devolucion = this.$validar_privilegio('HC_DEVOLUCION').status
            this.permisos.or_devolucion = this.$validar_privilegio('OR_DEVOLUCION').status
    
    
            this.permisos.manuales_devolucion = this.$validar_privilegio('CARGOS_MANUALES').status
            this.permisos.vale_elec_devolucion = this.$validar_privilegio('CARGOS_VALE_ELECTRONICO').status
        },
        methods: {   
            AbrirCorporativoHuella(){

               var ListaSeleccionada = [];
                ListaSeleccionada = this.ListaCargosValeElec.filter(f => f.seleccion == true)
                if (ListaSeleccionada.length == 0) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Devolución de cargos',
                        text: 'Seleccionar cargos a Devolver.',
                    })
                    return;
                }
                     
                
                this.limpiarDatosHuella();
                this.tituloHuella = 'Corporativo Huella Enfermeria'
                this.$refs.componenteValidarCorporativoHuella.CargarHuellas()                
            },
            getCorporativoHuellas(data){

            
                if(!this.huella.enfermeroValidado){
                    this.huella.usuarioEnfermero = data.usuario
                    this.huella.enfermero = data.corporativo
                    this.huella.enfermeroValidado = true;

                    this.tituloHuella = 'Corporativo Huella Farmacia'
                    this.$refs.componenteValidarCorporativoHuella.CargarHuellas()  
                }else{
                    this.huella.usuarioFarmacia = data.usuario
                    this.huella.farmacia = data.corporativo
                    this.huella.farmaciaValidado = true;

                    if (this.huella.enfermero != this.huella.farmacia ){

                        this.GuardarDevolucionCargoValeElectronico();
                    }else{
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Devolución de cargos',
                        text: 'No es permitido validar con el mismo corporativo.',
                    })
                    }
                }
            },
            limpiarDatosHuella(){
                this.tituloHuella='Corporativo Huella Enfermeria';
                this.huella.enfermero=null;
                this.huella.usuarioEnfermero=null;
                this.huella.enfermeroValidado=false;
                this.huella.farmacia=null;
                this.huella.usuarioFarmacia=null;
                this.huella.farmaciaValidado=false;
            },
            SeleccionarTodaOrdenManual() {
                var i = 0;
                if (this.DevolverTodoCargoManual) {
                    i = 0;
                    for (i = 0; i < this.ListaCargosManuales.length; i++) {
                        this.ListaCargosManuales[i].seleccion = true;
                    }
    
                } else {
                    i = 0;
                    for (i = 0; i < this.ListaCargosManuales.length; i++) {
                        this.ListaCargosManuales[i].seleccion = false;
                        this.ListaCargosManuales[i].Cantidad = this.ListaCargosManuales[i].Cant_Original;
                        this.ListaCargosManuales[i].Invalido = false;
                    }
    
                }
            },
            SeleccionarTodaOrdenValeE() {
                var i = 0;
                if (this.DevolverTodoCargoValeE) {
    
                    for (i = 0; i < this.ListaCargosValeElec.length; i++) {
                        this.ListaCargosValeElec[i].seleccion = true;
                    }
                } else {
                    for (i = 0; i < this.ListaCargosValeElec.length; i++) {
                        this.ListaCargosValeElec[i].seleccion = false;
                        //this.ListaCargosValeElec[i].Cantidad = this.ListaCargosValeElec[i].Cant_Original;
                        //this.ListaCargosValeElec[i].Invalido = false;
                    }
    
                }
            },
            ValidacionCantidad(CantNueva, CantidadOriginal) {
                if ((Number(CantNueva) >= 1) && (Number(CantNueva) <= Number(CantidadOriginal))) {
                    return false
                }
                return true
            },
            cargar() {
                return {
                    datos_tipo_orden: (datos) => {
                        this.info.tipoOrden = datos.Codigo
                        this.info.descripcionOrden = datos.Nombre
                    },
                    datos_orden: () => {
    
                    }
                }
    
            },
    
            CargarOrden() {
                if (this.info.tipoOrden != '' && this.info.codigoOrden != '') {
    
                    this.axios.post('/app/v1_farmacia/ConsultarDevolucion', {
                            TipoOrden: this.info.tipoOrden,
                            Id: this.info.codigoOrden
                        })
                        .then(resp => {
                            this.ListaCargosManuales = [];
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.info.paciente = resp.data.json[0].Paciente;
                                this.info.bodega = resp.data.json[0].BodegaCargo;
                                this.info.admision = resp.data.json[0].SerieAdmision + '-' + resp.data.json[0].Admision;
                                this.info.NumeroOrdenOrig = this.info.codigoOrden;
                                this.ListaCargosManuales = resp.data.json.map(m => {
                                    return {
                                        ...m,
                                        Invalido: false,
                                        seleccion: false
                                    }
                                })
                            } else {
                                this.Paciente = '';
                                this.Admision = '';
    
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Devolución de cargos',
                                    text: 'No existe Cargo seleccionado.',
                                })
                            }
                        })
                        .catch()
                }
            },
    
            GuardarDevolucionCargo() {
                var ListaErrores = [];
                var ListaSeleccionada = [];
                ListaSeleccionada = this.ListaCargosManuales.filter(f => f.seleccion == true)
                ListaErrores = this.ListaCargosManuales.filter(f => f.seleccion == true && f.Invalido == true)
                if (ListaSeleccionada.length == 0) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Devolución de cargos',
                        text: 'Seleccionar cargos a Devolver.',
                    })
                    return;
                }
                if (ListaErrores.length > 0) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Devolución de cargos',
                        text: 'Existen cantidades invalidas.',
                    })
                    return;
                }
    
                const DevCargos = ListaSeleccionada.map(m => {
                    return {
                        Linea: m.Linea,
                        Cantidad: m.Cantidad
                    }
                })
    
                if (this.info.tipoOrden != '' && this.info.NumeroOrdenOrig != '') {
                    this.axios.post('/app/v1_farmacia/DevolverCargo', {
                            TipoOrden: this.info.tipoOrden,
                            Id: this.info.NumeroOrdenOrig,
                            DevCargos
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.Limpiar()
                            }
                        })
                        .catch()
                }
            },
            GuardarDevolucionCargoValeElectronico() {
          
    
                var ListaSeleccionada = [];
                ListaSeleccionada = this.ListaCargosValeElec.filter(f => f.seleccion == true)
                if (ListaSeleccionada.length == 0) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Devolución de cargos',
                        text: 'Seleccionar cargos a Devolver.',
                    })
                    return;
                }
    
                const DevCargos = ListaSeleccionada.map(m => {
                    return {
                        Linea: m.Linea,
                        Cantidad: m.Cantidad,
                        IdControlMed: m.IdControlMedicamento,
                        ListaTipoOrden: m.Tipoorden,
                        ListaNumOrden: m.ORDEN
                    }
                })
                if (this.info.tipoOrden != '' && this.info.NumeroOrdenOrig != '') {
                    this.axios.post('/app/v1_farmacia/DevolverCargoValeEle', {
                            SerieAdmision: this.info.serie,
                            NumeroAdmision: this.info.numeroAdmision,
                            HuellaFarmacia: this.huella.farmacia,
                            HuellaEnfermeria:  this.huella.enfermero,
                            DevCargos
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.ListaDevolucionVale();
                            }
                        })
                        .catch()
                }
            },
            Limpiar() {
               
                this.info.codigoOrden = null;
                this.info.bodega = null;
                this.info.tipoOrden = null;
                this.info.descripcionOrden = null;
                this.info.NumeroOrdenOrig = null;
                this.info.admision = null;
                this.info.paciente = null;
                this.info.razon = null;
                this.info.numeroAdmision = null;
                this.info.serie = null;
                this.info.habitacion = null;
                this.ListaCargosManuales = [];
                this.ListaCargosValeElec = [];
                this.DevolverTodoCargoManual = false;
                this.DevolverTodoCargoValeE = false;
                this.$refs.componenteAdmisiones.limpiar_campos()
    
            },
    
            LimpiarPantalla() {
             
                this.info.codigoOrden = null;
                this.info.bodega = null;
                this.info.tipoOrden = null;
                this.info.descripcionOrden = null;
                this.info.NumeroOrdenOrig = null;
                this.info.admision = null;
                this.info.paciente = null;
                this.info.razon = null;
                this.info.numeroAdmision = null;
                this.info.serie = null;
                this.info.habitacion = null;
                this.ListaCargosManuales = [];
                this.ListaCargosValeElec = [];
                this.DevolverTodoCargoManual = false;
                this.DevolverTodoCargoValeE = false;
                //this.$refs.componenteAdmisiones.limpiar_campos()
    
            },
    
            ListaDevolucionVale() {
                this.axios.post('/app/v1_farmacia/ConsultarDevolucionValeElec', {
                        SerieAdmision: this.info.serie,
                        NumeroAdmision: this.info.numeroAdmision
                    })
                    .then(resp => {
                        if (resp.data.codigo == 0) {
    
                            this.ListaCargosValeElec = resp.data.json.map(m => {
                                return {
                                    ...m,
                                    seleccion: false
                                }
                            })
    
                        }
                    })
                    .catch()
                return;
            },
    
            DatosAdmision(datos) {
                this.info.serie = datos.Serie;
                this.info.numeroAdmision = datos.Codigo;
                this.info.paciente = datos.Paciente;
                this.info.habitacion = datos.Habitacion;
                this.ListaDevolucionVale();
            },
    
        }
    }
    </script>
    <style scoped>
    .popNuevo {
        display: grid;
        place-items: center;
    }
    </style>