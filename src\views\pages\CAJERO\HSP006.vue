<template>
    <vx-card :title="`Caja Remisiones Honorarios - ${sesion.sesion_sucursal_nombre}`">
        <vs-popup classContent="popup-example" title="Resultado Búsqueda" :active.sync="buscarChequesRemision"
            v-on:keydown.esc="$emit('close')" tabindex="0" style="z-index:99999" id="div-with-loading"
            class="vs-con-loading__container">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">

                <form>

                    <vs-divider></vs-divider>
                    <vs-table2 max-items="10" pagination :data="listas.listasCheques" id="tb_departamentos">

                        <template slot="thead">
                            <th>Remesa</th>
                            <th>Banco</th>
                            <th>Nombre</th>
                            <th>Monto</th>
                            <th>Saldo</th>
                            <th>Cheque</th>
                            <th>Cuenta</th>
                            <th></th>
                        </template>

                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 :data="tr.Remesa">
                                    {{ tr.Remesa }}
                                </vs-td2>
                                <vs-td2 :data="tr.BancoEmisor">
                                    {{ tr.BancoEmisor }}
                                </vs-td2>
                                <vs-td2 :data="tr.Nombre">
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">
                                        {{ tr.Nombre }}
                                    </div>
                                </vs-td2>
                                <vs-td2 :data="tr.Monto">
                                    {{ tr.Monto }}
                                </vs-td2>
                                <vs-td2 :data="tr.Saldo">
                                    {{ tr.Saldo }}
                                </vs-td2>
                                <vs-td2 :data="tr.ChequeNumero">
                                    {{ tr.ChequeNumero }}
                                </vs-td2>
                                <vs-td2 :data="tr.CuentaCheque">
                                    {{ tr.CuentaCheque }}
                                </vs-td2>

                                <vs-td2 align="right">
                                    <vs-button color="primary" icon-pack="fas" icon="fa-check-circle"
                                        style="display:inline-block;margin-right:2px" @click="resolver(tr)"></vs-button>
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
                </form>

            </div>
        </vs-popup>

        <buscador ref="BuscarEmpresaSeguro" buscador_titulo="Buscador / Empresa Seguro"
            :api="'app/v1_caja/ConsultaRecibosHospitales'" :campos="['Codigo', 'Nombre']"
            :titulos="['Codigo', 'Nombre']" :api_filtro="{ 'opcion': 'C', 'subOpcion': '2' }" :api_filtro_buscar="false"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" :api_preload="true" :api_cache="true"
            :all_datos="true" />

        <buscador ref="BuscarRemisionPorEmpresa" buscador_titulo="Buscador / Remisiones"
            :api="'app/v1_caja/ConsultaRecibosHospitales'"
            :campos="['CASA', 'Remision', 'Saldo', 'Total', 'SerieAdmision', 'Admision', 'Asegurado']"
            :titulos="['#CASA', 'Remisión', '#Saldo', '#Total', 'Serie', 'Admisión', 'Asegurado']"
            :api_filtro="{ 'opcion': 'C', 'subOpcion': '3', 'remision': noRemision }" :multiselect="false"
            :api_validar_campo="true" :api_reload="true" :api_preload="true" :api_cache="true" :all_datos="true" />

        <vs-row>
            <vs-row class="w-full">
                <ConfiguracionCaja ref="ConfiguracionCajaRef" @CargarCaja="CargarCaja"
                    :FiltrarAgrupacionCaja=FiltrarAgrupacionCaja TipoCajaSeleccionada="TipoCajaFacturacion"
                    CajaSeleccionada="CajaFacturacion" class="w-full">
                </ConfiguracionCaja>
                <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 p-1">
                    <label class="typo__label" style="color: rgba(0,0,0,.7); font-size: 10pt;">Remisión:&nbsp;</label>
                    <vs-col vs-type="flex" vs-align="center" class="p-0 m-0">
                        <vs-input ref="refRemision" v-model=noRemision
                            @keyup.enter="CargarRemisionesPorEmpresasSeguros()"
                            @keydown.tab="CargarRemisionesPorEmpresasSeguros()" class="w-full"></vs-input>
                        <vs-button id="button-with-loading" color="primary" icon-pack="fa" icon="fa-search"
                            @click="AbrirBuscadorRemisonPorEmpresa()"></vs-button>
                    </vs-col>
                </vs-col>
                <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 p-1">
                    <vs-input v-model=nombreEmpresa label="Nombre" class="w-full"></vs-input>
                </vs-col>
            </vs-row>
            <vs-row>
                <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 p-1">
                    <label class="typo__label" style="color: rgba(0,0,0,.7); font-size: 10pt;">Seguro:&nbsp;</label>
                    <vs-col vs-type="flex" vs-align="center">
                        <vs-input ref="refSeguro" v-model=empresa :disabled="true" class="w-full"></vs-input>
                    </vs-col>
                    <div v-if="nombreEmpresaOriginal && nombreEmpresaOriginal != ''" style="background-color:#ecf0f1;">
                        {{ nombreEmpresaOriginal }}
                    </div>
                </vs-col>
                <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-1/12 p-1">
                    <label class="typo__label" style="color: rgba(0,0,0,.7); font-size: 10pt;">Monto&nbsp;</label>
                    <DxNumberBox ref="numberMontoRemision" :min="0" :max="montoSaldo" v-model="montoRemision"
                        format="Q #,##0.00" @focus-in="() => montoRemision = null"
                        @value-changed="ValidarMontoRemision($event)" :disabled="!activarIngresoMontoRemision" />
                </vs-col>
                <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-1/12 p-1">
                    <label class="typo__label" style="color: rgba(0,0,0,.7); font-size: 10pt;">Saldo&nbsp;</label>
                    <DxNumberBox v-model=montoSaldo format="Q #,##0.00" class="w-full" :disabled="true" />
                </vs-col>
            </vs-row>
            <vs-row class="w-full p-1">
                <DxDataGrid :ref="PagosRef" :data-source="pagos" :visible="true" v-bind="DefaultDxGridConfiguration"
                    :hoverStateEnabled="false" no-data-text="Sin Datos" @init-new-row="nuevoRecibo"
                    :showColumnLines="true" :showBorders="true" :searchPanel="{ visible: false }"
                    :rowAlternationEnabled="true" width="100%" height="200px" @editor-preparing="onCellPrepared"
                    @saved="agregarLinea" :word-wrap-enabled="false">

                    <DxDataGridToolbar>
                        <DxDataGridItem location="before" name="addRowButton" showText="always" :options="{
                            type: 'success',
                            text: 'Nuevo',
                            disabled: !activarIngresoMontoRemision
                        }" />
                    </DxDataGridToolbar>

                    <DxDataGridEditing :allow-updating="true" :allow-adding="true" :allow-deleting="true" mode="row" />
                    <DxDataGridColumn :width="125" data-field="formaDePago" :set-cell-value="actualizarFormaDePago"
                        :visible="true" caption="Forma Pago" :allow-editing="true" :allow-filtering="false">
                        <DxDataGridLookup :dataSource="listas.formasPago" value-expr="Valor"
                            display-expr="Descripcion" />
                        <DxDataGridRequiredRule message="La forma de pago es requerida" />
                    </DxDataGridColumn>
                    <DxDataGridColumn :width="175" data-field="SaldoIngresado" caption="Monto" :allow-editing="true"
                        :editor-options="{ format: 'Q #,##0.00' }" format="Q #,##0.00" data-type="number"
                        alignment="center">
                        <DxCustomRule
                            message="El monto del recibo debe ser menor o igual al monto de la remisión y el cheque"
                            type="custom" :validation-callback="ValidarMonto" :reevaluate="true" />
                        <DxDataGridRangeRule message="El monto del recibo debe ser mayor a cero" :min="0" />
                    </DxDataGridColumn>
                    <DxDataGridColumn :width="300" data-field="banco" :visible="true" caption="Banco"
                        :allow-editing="true" :allow-filtering="false">
                        <DxCustomRule message="El banco es requerido para Cheque" type="custom"
                            :validation-callback="validarBanco" :reevaluate="true" />
                        <DxDataGridLookup :dataSource="listas.listaBancos" value-expr="Codigo"
                            :display-expr="(data) => data.Codigo.trim() + ' - ' + data.Nombre.trim()" />
                    </DxDataGridColumn>
                    <DxDataGridColumn :width="150" data-field="ChequeNumero" caption="Cheque" data-type="number"
                        :set-cell-value="actualizarCheque" :allow-editing="true" alignment="center">
                        <DxCustomRule message="Obligatorio para pago con remesa" type="custom"
                            :validation-callback="validarCheque" :reevaluate="true" />
                    </DxDataGridColumn>
                    <DxDataGridColumn :width="150" data-field="CuentaCheque" caption="Cuenta Cheque"
                        :allow-editing="false" alignment="center" />
                    <DxDataGridColumn :width="150" data-field="Autorizacion" caption="Autorización" alignment="center">
                    </DxDataGridColumn>
                    <DxDataGridColumn type="buttons" caption="Acción">
                        <DxDataGridButton name="edit" icon="edit" text="Editar" />
                        <DxDataGridButton name="delete" type="danger" icon="fas fa-trash" text="Borrar" />
                        <DxDataGridButton name="save" icon="save" text="Guardar" />
                        <DxDataGridButton name="cancel" type="danger" icon="feather icon-x" text="Cancelar" />
                    </DxDataGridColumn>
                </DxDataGrid>
            </vs-row>
            <vs-row class="w-full p-1">
                <vs-col vs-type="flex" vs-align="center">
                    <label class="typo__label pb-2 pt-2" style="color: rgba(0,0,0,.7); font-size: 12pt;">Honorarios /
                        Cta.
                        Ajena</label>
                </vs-col>
                <DxDataGrid ref="honorariosRef" :data-source="honorarios" :visible="true"
                    @cell-prepared="PintarColumnasAjenos" v-bind="DefaultDxGridConfiguration" :hoverStateEnabled="false"
                    :showColumnLines="true" :showBorders="true" :searchPanel="{ visible: false }"
                    :rowAlternationEnabled="true" width="100%" height="225px" :word-wrap-enabled="false">

                    <DxDataGridEditing :allow-updating="true" :allow-adding="false" :allow-deleting="true" mode="row" />

                    <DxDataGridColumn :width="70" data-field="TipoOrden" :visible="true" caption="Tipo"
                        :allow-editing="false" />
                    <DxDataGridColumn :width="70" data-field="Orden" :visible="true" caption="Orden"
                        :allow-editing="false" />
                    <DxDataGridColumn :width="100" data-field="Linea" caption="L" :allow-editing="false"
                        alignment="center" />
                    <DxDataGridColumn :width="100" data-field="Producto" :visible="true" caption="Código"
                        :allow-editing="false" />
                    <DxDataGridColumn :width="200" data-field="NombreCompleto" caption="Nombre" :allow-editing="false"
                        alignment="center" />
                    <DxDataGridColumn :width="150" data-field="Valor" caption="Pendiente" :allow-editing="false"
                        :editor-options="{ format: 'Q #,##0.00' }" format="Q #,##0.00" data-type="number"
                        alignment="rigth" />
                    <DxDataGridColumn :width="150" data-field="xPagar" caption="Por Pagar" :allow-editing="true"
                        :editor-options="{ format: 'Q #,##0.00' }" format="Q #,##0.00" data-type="number"
                        alignment="rigth">
                        <DxCustomRule message="El monto por pagar debe ser menor o igual al monto pendiente."
                            type="custom" :validation-callback="ValidarMontoPorPagar" />
                        <DxDataGridRangeRule message="El monto por pagar debe ser mayor a cero" :min="0" />
                    </DxDataGridColumn>
                    <DxDataGridColumn :width="70" data-field="Status" caption="C" :allow-editing="false"
                        alignment="center" />
                    <DxDataGridColumn type="buttons" caption="Acción">
                        <DxDataGridButton name="edit" icon="edit" text="Editar" :visible="activarEdicionHospital" />
                        <DxDataGridButton name="save" icon="save" text="Guardar" />
                        <DxDataGridButton name="cancel" type="danger" icon="feather icon-x" text="Cancelar" />
                    </DxDataGridColumn>
                    <DxDataGridSummary>
                        <DxDataGridTotalItem column="Valor" summary-type="sum" value-format="Q #,##0.00"
                            data-type="number" display-format="{0}" />
                        <DxDataGridTotalItem column="xPagar" summary-type="sum" value-format="Q #,##0.00"
                            data-type="number" display-format="{0}" />
                    </DxDataGridSummary>
                </DxDataGrid>
            </vs-row>
            <vs-row class='w-full pt-4'>
                <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 m-1">
                    <vs-button color="success" class="w-full" ref="botonRecibo"
                               :type="reciboSeleccionado?'border':'filled'" 
                               @keyup.tab="reciboSeleccionado=true"
                               v-on:blur="reciboSeleccionado=false"
                        :disabled="!configuracion.activarRecibos || pagos.length == 0" @click="GrabarReciboHonorario()">
                        Recibo
                    </vs-button>
                </vs-col>
                <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 m-1">
                    <vs-button color="warning" class="w-full" @click="LimpiarDatosRecibo()">
                        Limpiar
                    </vs-button>
                </vs-col>
            </vs-row>
            <vs-divider></vs-divider>
        </vs-row>
    </vx-card>
</template>
<script>

import 'flatpickr/dist/flatpickr.css';
import moment from "moment"
import { ref } from 'vue'
import ConfiguracionCaja from "/src/components/sermesa/modules/caja/ConfiguracionCaja.vue";
import {
    DefaultDxGridConfiguration,
    CalcularExencion
} from './data';

const autoExpandAll = ref(false);
const PagosRef = "PagosRef"
export default {
    name: 'Recibo_Honorarios',
    components: {
        ConfiguracionCaja
    },
    data() {
        return {
            reciboSeleccionado:false,
            FiltrarAgrupacionCaja: ['5'],
            resolver: function SeleccionarCheque(pago) {
                let cheque = 0
                if (pago) {
                    cheque = pago
                }
                return cheque
            },
            filaCheque: {},
            filaChequeNueva: {},
            buscarChequesRemision: false,
            IVA: 0.00,
            DefaultDxGridConfiguration: { ...DefaultDxGridConfiguration },
            expandir: autoExpandAll,
            PagosRef,
            activarBusquedaRemision: true,
            activarIngresoMontoRemision: false,
            activarBusquedaPagosRemision: false,
            empresa: null,
            nombreEmpresaOriginal: null,
            nombreEmpresa: null,
            noCheque: null,
            noRemision: null,
            montoRemision: null,
            montoSaldo: null,
            serie: null,
            admision: null,
            empresasSeguros: [],
            remision: [],
            pagos: [],
            listas: {
                formasPago: [],
                listaBancos: [],
                listasCheques: []
            },
            honorarios: [],
            configuracion: {
                cajaSeleccionada: null,
                activarFacturacion: false,
                activarRecibos: false
            },
            cajero: {
                serieFel: 'C05'
            }
        }
    },
    mounted() {
        this.Init()
    },
    activated() {
        window.addEventListener('focus', this.actualizarCajero);
    },
    deactivated() {
        window.removeEventListener('focus', this.actualizarCajero);
    },
    watch: {
        popUpRecibo(value) {
            if (!value) {
                this.recibo.montoRecibo = null
            }
        }
    },
    methods: {
        async CargarPagosPorNoCheque(noCheque, filaActual) {
            let montoBase = this.montoRemision - this.pagos.reduce((total, recibo) => recibo.__KEY__ != filaActual.__KEY__ ? total + this.ConvertirFloat(recibo.SaldoIngresado) : total, 0.00)
            if (!this.empresa || this.empresa.trim().length <= 0) {
                this.Consulta().MostrarError('Debe ingresar una empresa para buscar pagos', 3000)
                return 0
            }

            if (!this.noRemision || this.noRemision.trim().length <= 0) {
                this.Consulta().MostrarError('Debe ingresar una remisión para buscar pagos', 3000)
                return 0
            }

            if (!noCheque || noCheque.length <= 0) {
                this.Consulta().MostrarError('Debe ingresar un no. de cheque valido', 3000)
                return 0
            }

            if (filaActual.SaldoIngresado > montoBase) {
                this.Consulta().MostrarError('El monto ingresado es mayor al monto restante por liquidar de la remisión', 3000)
                return 0
            }

            let cheque = await
                this.axios.post('/app/v1_caja/ConsultaRecibosHospitales', {
                    opcion: 'C',
                    subOpcion: '4',
                    empresaSeguro: this.empresa,
                    noCheque: noCheque
                }).then(resp => {
                    if (resp.data.json && resp.data.json.length == 1) {
                        if (filaActual.SaldoIngresado > this.parseNumber(resp.data.json[0].Saldo)) {
                            this.Consulta().MostrarError('El saldo de la remesa es insuficiente para el monto  restante de la remisión', 3000)
                            return 0
                        } else {
                            resp.data.json[0].SaldoIngresado = filaActual.SaldoIngresado
                            return resp.data.json[0]
                        }
                    } else if (resp.data.json && resp.data.json.length == 0) {
                        this.Consulta().MostrarError('No se encontro un cheque con el numero ingresado', 3000)
                        return 0
                    } else {
                        this.listas.listasCheques = resp.data.json
                        return -1
                    }
                })

            if (cheque == -1) {
                cheque = await this.prometerSeleccionCheque()
                cheque.seleccion = 1
                this.buscarChequesRemision = false
            }
            return cheque
        },
        onCellPrepared(e) {
            if (e.dataField == 'banco' && (e.row.data.formaDePago == 'B' || e.row.data.formaDePago == 'N')) {
                e.editorOptions.dataSource = this.listas.listaBancos.filter(banco => banco.Tipo == 'B')
            } else if (e.dataField == 'banco' && (e.row.data.formaDePago == 'T' || e.row.data.formaDePago == 'R' || e.row.data.formaDePago == 'X')) {
                e.editorOptions.dataSource = this.listas.listaBancos.filter(banco => banco.Tipo == e.row.data.formaDePago)
            } else if (e.dataField == 'banco') {
                e.editorOptions.dataSource = []
            }
        },
        prometerSeleccionCheque() {
            return new Promise(resolve => {
                this.buscarChequesRemision = true
                this.resolver = resolve//aqui es donde se asigna la fución de resolve...
            })
        },
        SeleccionarCheque(pago) {
            let cheque = 0
            if (pago) {
                cheque = pago
            }
            return cheque
        },
        agregarLinea(e) {
            if (e.changes && e.changes.length == 1 && e.changes[0].type == "insert") {
                this.$nextTick(() => {
                    setTimeout(() => {
                        this.dataGridPagos.addRow()
                    }, 100)
                })
            }
        },
        async CargarMontoIva() {

            await this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision',
                {
                    opcion: 'I',
                    subOpcion: 51,
                    serieAdmision: this.serie,
                    numeroAdmision: this.admision,
                    ipoc: 2
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.IVA = resp.data.json.length > 0 ? resp.data.json[0].IVA : 0.00
                    }
                })
        },
        nuevoRecibo(e) {
            e.data.previsado = 'N'
            e.data.status = 'H'
            e.data.formaDePago = 'B'
            e.data.SaldoIngresado = this.montoRemision - this.pagos.reduce((total, recibo) => total + recibo.SaldoIngresado, 0.00)
        },
        actualizarFormaDePago(fila, value) {
            fila.formaDePago = value
            fila.banco = null
            fila.ChequeNumero = null
            fila.CuentaCheque = null
            this.actualizarMonto(fila, value)
        },
        actualizarMonto(fila, formaDePago) {
            let montoBase = 0.00
            let valorIva = this.ConvertirFloat(this.IVA) / 100


            montoBase = this.montoRemision
            fila.SaldoIngresado =
                formaDePago && formaDePago == 'X' ? CalcularExencion(montoBase, valorIva)
                    : montoBase - this.pagos.reduce((total, recibo) => total + this.ConvertirFloat(recibo.SaldoIngresado), 0.00)

        },
        validarBanco(linea) {
            let bancoValido = true;
            if ((linea.data.formaDePago == 'B')) {
                if (!linea.data.banco || linea.data.banco.trim() == '') {
                    bancoValido = false
                }
            }
            return bancoValido
        },
        validarCheque(linea) {
            let respuesta = true;
            if (linea.data.formaDePago == 'B' && (!linea.data.ChequeNumero || linea.data.ChequeNumero == '')) {
                respuesta = false
            }
            return respuesta;
        },
        async actualizarCheque(filaNueva, value, filaActual) {
            if (filaActual.formaDePago != 'B') {
                filaNueva.ChequeNumero = value
                return
            }
            let datosRemesa = await this.CargarPagosPorNoCheque(value, filaActual)
            this.actualizaFilaCheque(filaNueva, datosRemesa, filaActual)
        },
        actualizaFilaCheque(filaNueva, datosRemesa, filaActual) {
            if (datosRemesa == -1) return

            if (datosRemesa == 0) {
                filaNueva.ChequeNumero = ''
                filaNueva.Saldo = 0
                filaNueva.CuentaCheque = ''
            } else if (datosRemesa.seleccion == 1) {
                if (filaActual.SaldoIngresado > this.parseNumber(datosRemesa.Saldo)) {
                    this.Consulta().MostrarError('El saldo de la remesa es insuficiente para el monto  restante de la remisión', 3000)
                    filaNueva.ChequeNumero = ''
                    filaNueva.Saldo = 0
                    filaNueva.CuentaCheque = ''
                } else {
                    datosRemesa.SaldoIngresado = filaActual.SaldoIngresado
                    let fila = this.dataGridPagos.getVisibleRows().find(fila => fila.isEditing)
                    fila.data.ChequeNumero = datosRemesa.ChequeNumero
                    fila.data.Saldo = datosRemesa.Saldo
                    fila.data.SaldoIngresado = datosRemesa.SaldoIngresado
                    fila.data.CuentaCheque = datosRemesa.CuentaCheque
                }
            } else {
                filaNueva.ChequeNumero = datosRemesa.ChequeNumero
                filaNueva.Saldo = datosRemesa.Saldo
                filaNueva.SaldoIngresado = datosRemesa.SaldoIngresado
                filaNueva.CuentaCheque = datosRemesa.CuentaCheque
            }
        },
        CargarCaja(cajaSeleccionada) {
            this.configuracion.cajaSeleccionada = cajaSeleccionada.Cajero
            this.configuracion.activarFacturacion = cajaSeleccionada.Factura
            this.configuracion.activarRecibos = cajaSeleccionada.Recibo
        },
        async CargarAgrupaciones(agrupacion, subOpcion = 2, tipoPago = 'B') {
            await this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision', {
                opcion: 'C',
                subOpcion: subOpcion,
                agrupacion,
                tipoPago
            })
                .then(resp => {
                    if (resp.data && resp.data.json.length > 0) {
                        if (agrupacion == 'FormasDePago') {
                            this.listas.formasPago = resp.data.json.filter(pago => pago.Valor != 'E' && pago.Valor != 'N' && pago.Valor != 'T');
                        }
                        if (agrupacion == 'Bancos') {
                            this.listas.listaBancos = resp.data.json;
                        }
                    }
                }
                )
        },
        async GenerarRecibo(Serie, NumeroRecibo) {

            let reporte = "Recibo"

            let postData = {
                SerieRecibo: Serie,
                NumeroRecibo: NumeroRecibo
            }


            this.$reporte_modal({
                Nombre: reporte,
                Opciones: {
                    ...postData
                },
                Formato: this.FormatoReporte,
                NombreArchivoDescargar: Serie + '_' + NumeroRecibo,
                Descargar: true
            })

        },
        GrabarReciboHonorario() {
            const botonRecibo = document.getElementById("botonRecibo");
            if(botonRecibo) botonRecibo.blur();
            let listaRecibos =
                this.pagos.map(pago => ({
                    'status': 'H',
                    'formaDePago': pago.formaDePago,
                    'monto': this.ConvertirFloat(pago.SaldoIngresado),
                    'banco': pago.banco ? pago.banco : '',
                    'nombreBanco': pago.Nombre ? pago.Nombre : '',
                    'previsado': 'N',
                    'cheque': pago.ChequeNumero ? this.ConvertirEntero(pago.ChequeNumero) : '',
                    'cuenta': pago.CuentaCheque,
                    'autorizacion': pago.Autorizacion,
                    'tasa': 0.00
                })
                )

            let totalPorPagar =
                this.honorarios.reduce((totalPorPagar, honorario) => honorario.Status == 'H'
                    ? this.ConvertirFloat(honorario.xPagar) + totalPorPagar
                    : totalPorPagar, 0.00)

            listaRecibos.push({
                ...listaRecibos[0],
                'monto': totalPorPagar,
                'status': 'M'
            })

            let honorariosPorPagar =
                this.honorarios.filter(honorario => honorario.Status == 'H')
                    .map(honorario => ({
                        'tipoOrden': honorario.TipoOrden,
                        'orden': honorario.Orden,
                        'linea': this.ConvertirEntero(honorario.Linea),
                        'producto': honorario.Producto,
                        'valor': this.ConvertirFloat(honorario.Valor),
                        'porPagar': this.ConvertirFloat(honorario.xPagar),
                        'ajeno': honorario.Ajeno,
                        'status': honorario.Status,
                        'nombre': honorario.NombreCompleto
                    })
                    )

            /*
if(totalPorPagar<=0){
this.Consulta().MostrarError('Ingreso un valor por pagar para los honorarios.',2000)
return
}
                        */

            this.axios.post('/app/v1_caja/GeneracionRecibosHospitales', {
                opcion: 'I',
                subOpcion: 2,
                empresaSeguro: this.empresa,
                remision: this.noRemision,
                totalRemision: this.montoRemision,
                tipoRecibo: 'S',
                serieAdmision: this.serie,
                numeroAdmision: this.admision,
                serieFactura: this.configuracion.cajaSeleccionada.SerieFac,
                serieRecibo: this.configuracion.cajaSeleccionada.SerieRecibo,
                nombreFactura: this.nombreEmpresa,
                nombreRecibo: this.nombreEmpresa,
                listaRecibos: listaRecibos,
                listaPagosHonorarios: honorariosPorPagar,
                ipoc: 0
            }).then(resp => {
                if (resp.data.codigo == 0) {
                    this.GenerarRecibo(resp.data.Serie, resp.data.Recibo)
                    this.$vs.dialog({
                        type: 'confirm',
                        color: 'success',
                        title: 'Recibo Honorario',
                        acceptText: 'Aceptar',
                        cancelText: 'Cancelar',
                        text: resp.data.descripcion + ' ' + resp.data.Serie + '-' + resp.data.Recibo,
                    })
                    this.$refs.refRemision.focusInput()
                    this.LimpiarRemision();
                }
            })
        },
        LimpiarDatosRecibo() {
            this.activarBusquedaRemision = true
            this.activarIngresoMontoRemision = false
            this.activarBusquedaPagosRemision = false
            this.empresa = null
            this.nombreEmpresaOriginal = null
            this.nombreEmpresa = null
            this.noRemision = null
            this.montoRemision = null
            this.montoSaldo = null
            this.serie = null
            this.admision = null
            this.empresasSeguros = []
            this.remision = []
            this.pagos = []
            this.honorarios = []
            this.noCheque = null
            this.$refs.refSeguro.focusInput()
        },
        activarEdicionHospital(e) {
            return e.row.data.Status == "H"
        },
        PintarColumnasAjenos(e) {
            if (e.rowType != "data") return

            if (e.data.Status == "A") {
                e.cellElement.classList.add("columnaAmarrilla")
            }
        },
        ValidarMonto(linea) {
            let montoMaximo = 0.00
            let montoValido = true

            montoMaximo = this.montoRemision
                - this.pagos
                    .reduce((total, recibo) => recibo.__KEY__ != linea.data.__KEY__ ? total + this.ConvertirFloat(recibo.SaldoIngresado) : total, 0.00)

            if (linea.data.SaldoIngresado > montoMaximo) montoValido = false
            if (linea.data.formaDePago == 'B' && linea.data.SaldoIngresado > linea.data.Saldo) montoValido = false

            return montoValido
        },
        ValidarMontoPorPagar(e) {
            return this.ConvertirFloat(e.data.Valor) >= this.ConvertirFloat(e.value)
        },
        ValidarMontoRemision(e) {
            if (e.value > this.montoSaldo) {
                this.Consulta().MostrarError('El monto ingrseado no debe ser mayor al saldo', 2000)
                if (e.previousValue <= this.montoSaldo) {
                    this.montoRemision = e.previousValue
                    this.$refs.numberMontoRemision.value = e.previousValue
                } else {
                    this.montoRemision = this.montoSaldo
                    this.$refs.numberMontoRemision.value = this.montoSaldo
                }
            }
        },
        CargarEmpresasSeguros() {
            this.axios.post('/app/v1_caja/ConsultaRecibosHospitales', {
                opcion: 'C',
                subOpcion: '2',
                empresaSeguroBusqueda: this.empresa
            }).then(resp => {
                if (resp.data.json && resp.data.json.length == 1) {
                    this.empresa = resp.data.json[0].Codigo
                    this.nombreEmpresaOriginal = resp.data.json[0].Nombre
                    this.nombreEmpresa = resp.data.json[0].Nombre 
                }
            })
        },
        AbrirBuscadorEmpresaSeguro() {
            this.$refs.BuscarEmpresaSeguro.iniciar((empresa) => {
                if (empresa) {
                    this.empresa = empresa.Codigo
                    this.nombreEmpresaOriginal = empresa.Nombre
                    this.nombreEmpresa = empresa.Nombre
                    this.activarBusquedaRemision = true;
                    this.$refs.refRemision.focusInput()
                    this.LimpiarRemision();
                } else {
                    this.empresa = null
                    this.nombreEmpresaOriginal = null
                    this.nombreEmpresa = null
                    this.activarBusquedaRemision = false;
                    this.LimpiarRemision();
                }
            })

        },
        CargarRemisionesPorEmpresasSeguros() {
            /* if (!this.empresa || this.empresa.trim().length <= 0) {
                this.Consulta().MostrarError('Debe ingresar una empresa para buscar remisiones', 3000)
                return
            } */

            this.axios.post('/app/v1_caja/ConsultaRecibosHospitales', {
                opcion: 'C',
                subOpcion: '3',
                remision: this.noRemision
            }).then(resp => {
                if (resp.data.json && resp.data.json.length == 1) {
                    this.noRemision = resp.data.json[0].Remision
                    this.montoRemision = this.parseNumber(resp.data.json[0].Total)
                    this.montoSaldo = this.parseNumber(resp.data.json[0].Saldo)
                    this.serie = resp.data.json[0].SerieAdmision
                    this.admision = resp.data.json[0].Admision
                    this.empresa = resp.data.json[0].CASA
                    this.activarIngresoMontoRemision = true;
                    this.CargarEmpresasSeguros();
                    this.CargarHonorariosCuenta();
                    this.CargarMontoIva(resp.data.json[0].SerieAdmision, resp.data.json[0].Admision)
                } else {
                    this.LimpiarRemision();
                    this.Consulta().MostrarError('No se encontro la remisión')
                    return
                }
            })
        },
        AbrirBuscadorRemisonPorEmpresa() {

            this.$refs.BuscarRemisionPorEmpresa.iniciar((remision) => {
                if (remision) {
                    this.noRemision = remision.Remision
                    this.montoRemision = this.parseNumber(remision.Total)
                    this.montoSaldo = this.parseNumber(remision.Saldo)
                    this.serie = remision.SerieAdmision
                    this.admision = remision.Admision      
                    this.empresa = remision.CASA              
                    this.activarIngresoMontoRemision = true;
                    this.CargarEmpresasSeguros();
                    this.CargarHonorariosCuenta();
                    this.CargarMontoIva()
                } else {
                    this.LimpiarRemision();
                }
            })

        },
        LimpiarRemision() {
            this.noRemision = null
            this.montoRemision = null
            this.montoSaldo = null
            this.serie = null
            this.admision = null
            this.noCheque = null
            this.nombreEmpresa = null
            this.nombreEmpresaOriginal = null
            this.empresa = null
            this.honorarios = []
            this.pagos = []
            this.activarIngresoMontoRemision = false;
        },
        CargarHonorariosCuenta() {
            //exec SpHisCajeroRecibo @iOpcion='C', @iSubOpcion=5, @iEmpresaU= 'MED', @iEmpresaReal = 'MED', @iSerieAdm = 'E', @iAdmision = 419646            
            if (!this.serie || this.serie.length <= 0 ||
                !this.admision || this.admision.length <= 0) {
                return;
            }

            this.axios.post('/app/v1_caja/ConsultaRecibosHospitales', {
                opcion: 'C',
                subOpcion: '5',
                serieAdmision: this.serie,
                numeroAdmision: this.admision
            }).then(resp => {
                this.honorarios = resp.data.json
            })
        },
        parseDate(datestr, format) {
            if (!datestr || datestr == '')
                return null

            return moment(datestr, format, true).toDate();
        },
        parseNumber(entrada) {
            let numero = 0
            if (typeof entrada == "string") {
                if (Number.isNaN(Number(entrada))) numero = 0
                else numero = Number(entrada)
            } else if (typeof entrada == "number") {
                numero = entrada
            }
            return numero
        },
        GetDateValue(value) {
            if (value === undefined) {
                return null
            } else if (value === null) {
                return null
            } else if (value == '') {
                return null
            }
            return moment(value).format('YYYYMMDD');
        },
        GetDateTimeValue(value) {
            if (value === undefined) {
                return null
            } else if (value === null) {
                return null
            } else if (value == '') {
                return null
            }
            return moment(value).format('YYYYMMDD_HHmm');
        },
        actualizarCajero() {
            this.$refs.ConfiguracionCajaRef.CargarCajaInicial();
        },
        async Init() {
            await this.CargarAgrupaciones('FormasDePago');
            await this.CargarAgrupaciones('Bancos', 4, 'B');
        },
        ConvertirEntero(Valor) {
            let conversion = parseInt(Valor)
            if (isNaN(conversion)) {
                return 0
            }
            return conversion;
        },
        ConvertirFloat(Valor, Decimales = 2) {
            let conversion = parseFloat(Valor ? Valor : 0.00)
            if (isNaN(conversion)) {
                return 0.00
            }
            return parseFloat(conversion.toFixed(Decimales))
        },
        Consulta() {
            return {
                MostrarError: (mensaje, tiempo = 2000) => {
                    this.$vs.notify({
                        time: tiempo,
                        title: 'Cajero',
                        color: 'danger',
                        text: mensaje,
                        position: 'top-center'
                    })
                }
            }
        },
        ObtenerError(mensaje) {
            let error = '';
            if (mensaje.json && mensaje.json.length > 0) {
                error = mensaje.json[0].Message ??
                    mensaje.json[0].descripcion.slice(0, mensaje.json[0].descripcion.length > 400 ? 400 : mensaje.json[0].descripcion.length)
            } else {
                error = mensaje.descripcion.Message ??
                    mensaje.descripcion.slice(0, mensaje.descripcion.length > 400 ? 400 : mensaje.descripcion.length)
            }
            return error;
        }
    },
    computed: {
        sesion() {
            return this.$store.state.sesion
        },
        dataGridPagos() {
            return this.$refs['PagosRef'].instance
        }
    }
}
</script>
<style scoped>
#detail .vs-col {
    padding: 5px;
    border-color: black;
    border-style: solid;
    border-width: 1px;
}

[dir=ltr] .dx-datagrid-group-closed::before,
[dir=ltr] .dx-datagrid-group-opened::before {
    font-size: 40px !important;
    color: #337ab7;
}

.ajustar-texto-botton {
    margin: 5px 5px 5px 0px;
    font-size: 12px;
    width: auto;
    height: 45px;
    text-align: center;
}

.ajustar-texto-label {
    font-size: 12px;
    height: 10px;
    width: auto;
    color: black;
    text-align: center;
}

.label-total {
    font-size: 25px;
    color: black;
    text-align: center;
}

.centered-element {
    margin: 0;
    top: 50%;
    transform: translateY(-50%);
}

.multiselect,
.multiselect__input,
.multiselect__single {
    height: 10px !important;
    z-index: 500;
}

.main-expediente {
    display: grid;
    height: calc(100vh - 49px - 67px);
    grid-template-areas: "Encabezado Encabezado" "Opciones Contenido";
    grid-template-columns: 0fr 1fr;
    grid-template-rows: 85px 1fr;
    margin: 0;
    padding: 0;
    right: 0;
    background-color: rgb(255, 255, 255);
    position: fixed;
    left: 80px;
    width: calc(100%-80px);
    min-width: 400px;
}

.Encabezado {
    grid-area: Encabezado;
    overflow-y: hidden;
    font-size: small;
    /* background-color: #d0e1f9; */
    background: rgb(208, 225, 249);
    background: linear-gradient(90deg, rgba(208, 225, 249, 1) 67%, rgba(119, 175, 255, 1) 94%, rgba(2, 0, 36, 1) 100%);
    color: #2f496e;
}

.Contenido {
    grid-area: Contenido;
    overflow-y: auto;
}

.Opciones {
    grid-area: Opciones;
    overflow-y: auto;
    overflow-x: hidden;
    width: max-content;
}

.Opciones>#toolbar {
    background-color: #f4eade;
    background-color: #2988bc;
}

#view {
    margin-left: 10px;
    margin-top: 10px;
}
</style>
<style>
.DxNumberBox {
    border-color: black !important;
    border-style: solid !important;
    border-width: 1px !important;
}

.columnaAmarrilla {
    background-color: #fce803 !important;
    background: #fce803 !important;
}

.dx-popup-title {
    background-color: #f4eade !important;
    color: #2f496e !important;
}

.dx-list-item>#itemsMenuDrawer {
    color: #2988bc !important;
    background-color: #f4eade !important;
}

.dx-list-item-selected>#itemsMenuDrawer {
    color: #f4eade !important;
    background-color: #ed8c72 !important;
}

.dx-scrollable-container {
    touch-action: auto !important;
}

/*Ancho mínmo de los grid*/
#Contenido .dx-datagrid {
    min-width: 302px;
}

/**Modal actualizacion de peso y talla */
#popupTallaPeso .vs-popup {
    width: 400px !important;
}

.dx-datagrid-headers td {
    vertical-align: middle !important;
}

.dx-resizable {
    display: inline-grid;
}

.dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed)>td {
    background-color: #00ccff !important;
    color: black !important;
}

.dx-datagrid-rowsview .dx-row.dx-data-row.dx-column-lines.dx-state-hover>td {
    background-color: #79e4ff !important;
    color: black !important;
    font-size: 16px;
}

.dx-datagrid-headers {
    background-color: linen !important;
    color: black !important;
    font-weight: bold;
}

td {
    vertical-align: middle !important;
}

.dx-viewport,
.dx-device-phone,
.dx-device-mobile,
.dx-device-android,
.dx-device-android-6,
.dx-theme-generic,
.dx-theme-generic-typography,
.dx-color-scheme-light,
.dx-overlay-wrapper {
    color: #2980B9 !important;
}

[dir] .dx-popup-title {
    height: 3em !important;
}

.dx-button.dx-button-warning {
    background-color: #e0d100;
}

.buttonTab {
    height: 50px;
    width: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px !important;
}

.buttonTab:hover {
    background-color: blue !important;
}

.buttonTabSeleccionada {
    height: 50px;
    width: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px !important;
}

.buttonTabSeleccionada:hover {
    background-color: transparent !important;
}

.sticky {
    position: -webkit-sticky !important;
    /* for browser compatibility */
    position: sticky !important;
    top: 0px;
    z-index: 500;
}

.dx-overlay-wrapper.dx-popup-wrapper.dx-dropdowneditor-overlay.dx-popover-wrapper.dx-popover-without-title.dx-lookup-popup-wrapper.dx-lookup-popup-search.dx-position-bottom {
    z-index: 999999 !important;
}

.buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

.button {
    height: 40px;
    width: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px !important;
}

.button:hover {
    background-color: blue !important;
}

.div-button {
    display: flex;
    justify-content: center;
}

.stickyIntegracion {
    position: -webkit-sticky !important;
    /* for browser compatibility */
    position: sticky !important;
    top: 0px;
    background-color: rgb(117, 177, 255);
    z-index: 500;
}
</style>