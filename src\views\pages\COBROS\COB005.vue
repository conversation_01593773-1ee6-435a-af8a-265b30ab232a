<template>
<div class="fechas-reclamos-container">
    <vs-card>
        <div slot="header">
            <h3>
                <PERSON>chas de Recibido Aseguradoras
            </h3>
        </div>

        <SM-Tabs class="vs-tabs-component">
            <div class="tab p-4" label="Individual">
                <FechasRemision />
            </div>
            <div class="tab p-4 pb-0" label="Archivo">
                <FechasRemisionArchivo />
            </div>
        </SM-Tabs>

    </vs-card>
</div>
</template>

<script>
import FechasRemisionArchivo from './FechasRemisionArchivo.vue'
import FechasRemision from './FechasRemision.vue'
export default {
    name: 'carga-fechas-reclamos',
    components: {
        FechasRemisionArchivo,
        FechasRemision,
    },
    data() {
        return {

        }
    },
    methods: {

    },
    created() {

    },

}
</script>

<style>
.fechas-reclamos-container .vs-tabs-component>div>div:first-child {
    width: 150px !important;
}

.fechas-reclamos-container .vs-tabs-component>div>div:last-child {
    width: calc(100% - 150px) !important;
}

.fechas-reclamos-container .vs-tabs-component .contenedor-tab {
    max-height: calc(100vh - 150px) !important;
}

.fechas-reclamos-container .vs-tabs-component .dx-datagrid {
    height: calc(100vh - 300px) !important;
}
</style>
