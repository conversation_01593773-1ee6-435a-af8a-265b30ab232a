<template>
<div class="h-screen flex w-full bg-img vx-row no-gutter items-center justify-center" id="page-login" :class="[(reanudar)?'reanudar':null]">
    <div class="vx-col sm:w-1/2 md:w-1/2 lg:w-3/4 xl:w-3/5 sm:m-0 m-4">
        <vx-card>
            <div slot="no-body" class="full-page-bg-color">
                <div v-show="false">{{extra}}</div>
                <div class="vx-row no-gutter justify-center items-center">

                    <div class="vx-col hidden lg:block lg:w-1/2">
                        <img src="@/assets/images/pages/login.svg" height="200px" alt="login" class="mx-auto">
                    </div>

                    <div class="vx-col sm:w-full md:w-full lg:w-1/2 d-theme-dark-bg">
                        <div class="p-8 login-tabs-container">

                            <div class="vx-card__title mb-4" style="text-align:center">
                                <!-- <h4 class="mb-4">Sesión</h4> -->
                                <img src="@/assets/images/logo/logo.svg" style="display:inline" height="70px" alt="">
                            </div>

                            <div>
                                <form class="" v-on:submit.prevent="verify()">
                                    <vs-input v-if="!reanudar" name="email" icon-no-border icon="icon icon-user" icon-pack="feather" label-placeholder="Usuario (Corporativo)" v-model="usuario" class="w-full" />
                                    <vs-input v-else icon-no-border icon="icon icon-user" icon-pack="feather" label-placeholder="Usuario (Corporativo)" :value="sesion.corporativo" class="w-full" disabled />

                                    <vs-input type="password" name="password" icon-no-border icon="icon icon-lock" icon-pack="feather" label-placeholder="Contraseña" v-model="password" class="w-full mt-6" />
                                    <br>
                                    <!-- <div class="flex flex-wrap justify-between my-5">
                                        <router-link to="">Olvide la contraseña</router-link>
                                    </div> -->
                                    <button type="submit" name="button" style="display:none"></button>
                                    <vs-button class="float-right" v-on:click="verify()">Iniciar Sesión</vs-button>
                                    <vs-button v-if="reanudar" color="danger" type="line" v-on:click="cerrar_sesion()">Cerrar Sesión</vs-button>
                                </form>

                                <br><br>

                                <!-- <vs-divider></vs-divider> -->

                                <!-- <div style="width:100%;text-align:center">
                                    <a href="https://192.168.221.128/ReconocimientoFacial1/Validacion.aspx?corporativo=53761">
                                        <img src="@/assets/images/pages/login/face.png" alt="login" class="mx-auto" width="70px">
                                    </a>
                                    Reconocimiento Facial
                                </div> -->
                                <!-- <iframe src="https://192.168.221.128/ReconocimientoFacial1/Validacion.aspx?corporativo=53761" frameborder="0" width="100%" height="400px"></iframe> -->
                            </div>
                            <!-- {{bloqueoPass}} -->
                            <div v-if="bloqueoPass" style="position:absolute;bottom:0;right:0;background-color:#2196F3;color:white;padding:2px 10px;font-size:10px;border-radius:5px 0 0 0">
                                <input type="password" v-model="bloqueoText" v-on:keyup.enter="desbloqueoInstancia()">
                            </div>
                            <div style="position:absolute;bottom:0;right:0;background-color:#bdc3c7;color:white;padding:2px 10px;font-size:9px;border-radius:5px 0 0 0" @dblclick="bloqueoPass=true">
                                {{global.instancia}}
                                <select v-if="!bloqueoInstancia" v-on:change="instancia_cambio(instancia)" v-model="instancia">
                                    <option value="LOCAL">
                                        LOCAL
                                    </option>

                                    <option value="DESA">
                                        DESARROLLO
                                    </option>

                                    <option value="QA">
                                        TEST (QA)
                                    </option>

                                    <option value="PROD">
                                        PRODUCCIÓN
                                    </option>
                                </select>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </vx-card>
    </div>
</div>
</template>

<script>
export default {
    name: 'login',
    data() {
        return {
            usuario: "",
            password: "",
            checkbox_remember_me: false,
            bloqueoInstancia: true,
            bloqueoPass: false,
            bloqueoText: null,

            instancia: null
        }
    },
    computed: {
        global() {
            return this.$store.state.global
        },
        sesion() {
            return this.$store.state.sesion
        },
        extra() {
            const e = this.$store.state.extra
            if (e.loading) {
                this.$vs.loading()
            } else {
                this.$vs.loading.close()
            }
            if (e.notificacion) {
                this.$vs.notify(e.notificacion)
                this.$store.dispatch('notify', null)
            }
            return e
        },
    },
    props: {
        reanudar: {
            default: false
        }
    },
    methods: {
        /**
         * Cambio de instancia desde el middleware
         */
        obtenerInstancia() {
            this.axios.get('/Instancia')
                .then(resp => {
                    if (resp.data.Token) this.instancia_token(resp.data.Token)
                    if (resp.data.Instancias) this.instancia_listado(resp.data.Instancias)
                    if (resp.data.Instancia) this.instancia_cambio(resp.data.Instancia)
                })
        },
        verify() {
            const url = this.$store.state.global.url
            const token = this.$store.state.global.tokenDefault
            const config = {
                headers: {
                    Authorization: 'Bearer ' + token
                }
            };
            this.axios.post(url + 'app/usuario/verificar', { //DEBE SER GET EN LUGAR DE POST
                    Corporativo: (this.reanudar) ? this.sesion.corporativo : this.usuario,
                    Password: this.password
                }, config)
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Sesión - Error',
                            text: 'Usuario o contraseña incorrecta',
                        })
                        this.password = null
                    } else {
                        this.$store.dispatch('sesionGuardar', resp.data)
                        // reanudar
                        if (!this.reanudar) {
                            window.location.href = "/";
                        } else {
                            if (this.sesion.sesion_sucursal) {
                                this.axios.post('/app/usuario/seleccion_sucursal', {
                                        Sucursal: this.sesion.sesion_sucursal,
                                        Tipo: 2
                                    })
                                    .then(resp => {
                                        //guardando la informacion en el localstorage del cliente
                                        this.$store.dispatch('sesionGuardar', resp.data)
                                        this.validar_privilegios() // verificar nuevamente los privilegios
                                    })
                            } else {
                                this.validar_privilegios()
                            }
                        }
                    }
                })
                .catch(() => {})
        },
        cerrar_sesion() {
            this.$store.dispatch('sesionEliminar')
            location.replace("/pages/login");
        },

        /**
         * Instancias
         */
        instancia_token(token) {
            this.$store.dispatch('instanciaToken', token)
        },

        instancia_listado(instancias) {
            this.$store.dispatch('instanciaListado', instancias)
        },

        instancia_cambio(instancia) {
            this.$store.dispatch('instanciaCambio', instancia)
        },

        desbloqueoInstancia() {
            if (btoa(this.bloqueoText) == 'V2x0MTI1MC4=') {
                this.bloqueoInstancia = false
                this.bloqueoPass = false
            }
            this.bloqueoText = ''
        },

        // validando privilegios
        validar_privilegios() {
            this.axios.post('/app/usuario/obtener_privilegios', {})
                .then(resp => {
                    this.$store.dispatch('sesionValidar') //valida que se ha cargado la informacion
                    if (resp.data.tipo == "Empresa") {
                        let temp_emp = ''
                        this.seleccionEmpresa.mostrar = true;
                        resp.data.json.map(data => {
                            if (temp_emp != data.Codigo) this.seleccionEmpresa.empresas.push({
                                Empresa: data.Codigo,
                                Nombre: data.Nombre,
                                Sub: []
                            })

                            this.seleccionEmpresa.empresas[this.seleccionEmpresa.empresas.length - 1].Sub.push(data)
                            temp_emp = data.Codigo
                        })

                    } else {
                        this.$store.dispatch('funcionalidadListado', resp.data.json)
                        this.seleccionEmpresa.mostrar = false;
                    }
                })
        },

    },
    mounted() {
        this.obtenerInstancia()
    }
}
</script>

<style lang="scss">
#page-login {
    position: fixed;
    z-index: 99999;
    top: 0;
    left: 0;

    .social-login-buttons {
        .bg-facebook {
            background-color: #1551b1;
        }

        .bg-twitter {
            background-color: #00aaff;
        }

        .bg-google {
            background-color: #4285F4;
        }

        .bg-github {
            background-color: #333;
        }
    }
}

#page-login.reanudar {
    background-color: rgba(0, 0, 0, 0.6);
}
</style>
