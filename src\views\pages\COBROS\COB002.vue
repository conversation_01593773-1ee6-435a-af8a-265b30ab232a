<template>
<vx-card title="Bloqueo Honorarios">

    <!-- buscadores -->
    <buscador ref="buscar_admision" buscador_titulo="Buscador / Admisión" :api="'app/honorarios/BuscarAdmision'" :campos="['Admision','Paciente']" :titulos="['Admisión','Paciente']" :multiselect="false" :api_validar_campo="true" />

    <form>
        <vs-divider>Información de Admisión</vs-divider>
        <form v-on:submit.prevent="cargar_orden()">
            <div class="flex flex-wrap">

                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <!-- <vs-input label="Número" class="w-full" type="number" v-model="info.orden_numero" /> -->
                    <label class="vs-input--label">Admisión</label>
                    <vx-input-group class="">
                        <vs-input v-model="info.admision" v-mask="'A#########'" :disabled="bloqueoBusqueda" @change="info.admision=info.admision.toUpperCase(); cargar_admision()" />
                        <template slot="append">
                            <div class="append-text btn-addon">
                                <button type="submit" v-show="false" name="button"></button>
                                <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_admision()" icon="icon-search" v-if="bloqueoBusqueda==false"></vs-button>
                                <vs-button id="button-with-loading" color="danger" icon-pack="feather" @click="limpiar_admision()" icon="icon-x" v-else></vs-button>
                            </div>
                        </template>
                    </vx-input-group>
                </div>

            </div>
        </form>
        <div class="flex flex-wrap">
            <div class="w-full md:w-4/5 lg:w-4/5 xl:w-4/5">
                <vs-input label="Paciente" class="w-full" v-model="info.paciente_nombre" disabled />
            </div>
            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <vs-input label="Tipo Paciente" class="w-full" v-model="info.paciente_tipo" disabled />
            </div>
        </div>
        <div class="flex flex-wrap">
            <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                <vs-input label="Hospital de Egreso" class="w-full" v-model="info.hospital_egreso" disabled />
            </div>
            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <vs-input label="Fecha de Ingreso" class="w-full" v-model="info.fecha_ingreso" disabled />
            </div>
            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <vs-input label="Fecha de Egreso" class="w-full" v-model="info.fecha_egreso" disabled />
            </div>
        </div>

        <vs-divider>Listado de Cargos</vs-divider>
        <!-- {{listado_cargos_select}} -->
        <vs-table v-model="listado_cargos_select" :data="listado_cargos" v-if="listado_cargos.length>0">
            <template slot="thead">
                <vs-th>Selección</vs-th>
                <vs-th>Categoría Cargo</vs-th>
                <vs-th>Código Cargo </vs-th>
                <vs-th>Descripción Cargo </vs-th>
                <vs-th>Fecha Cargo </vs-th>
                <vs-th>Motivo Bloqueo </vs-th>
                <vs-th>Honorarios a Pagar </vs-th>
                <vs-th>Médico </vs-th>
                <vs-th>Colegiado </vs-th>
                <vs-th>Lote/Pagado </vs-th>
            </template>
            <template slot-scope="{data}">
                <vs-tr :data="tr" :key="indextr" v-for="(tr, indextr) in data" disabled>
                    <vs-td :data="tr.seleccion">
                        <vs-checkbox v-model="tr.seleccion"></vs-checkbox>
                    </vs-td>
                    <vs-td :data="tr.categoria">
                        {{ tr.categoria }}

                    </vs-td>
                    <vs-td :data="tr.codigo">
                        {{ tr.codigo}}
                    </vs-td>
                    <vs-td :data="tr.descripcion">
                        {{ tr.descripcion}}
                    </vs-td>
                    <vs-td :data="tr.fecha">
                        [{{ tr.fecha }}]
                    </vs-td>
                    <vs-td :data="tr.motivo">
                        <select name="" v-model="tr.motivo" id="" :disabled="!tr.seleccion || tr.bloqueo">
                            <option :value="item.IdMotivo" v-for="(item,index) in listado_tipologia" :key="index">{{item.Nombre}}</option>
                        </select>
                    </vs-td>
                    <vs-td :data="tr.pagar">
                        {{ tr.pagar}}
                    </vs-td>
                    <vs-td :data="tr.medico">
                        {{ tr.medico}}
                    </vs-td>
                    <vs-td :data="tr.colegiado">
                        {{ tr.colegiado}}
                    </vs-td>
                    <vs-td :data="tr.HonorariosPagadosLotes">
                        <vs-button id="button-with-loading" color="success" icon-pack="feather" icon="icon-check" v-if="(tr.HonorariosPagadosLotes!=null && tr.HonorariosPagadosLotes!='') || (tr.AjenosPagosMonto!=null && tr.AjenosPagosMonto!='')" disabled></vs-button>
                        <vs-button id="button-with-loading" color="danger" icon-pack="feather" icon="icon-x" v-else disabled></vs-button>
                    </vs-td>
                </vs-tr>
            </template>
        </vs-table>

        <div style="padding:10px 0;text-align:right;" v-if="bloqueoBusqueda && listado_cargos.length>0">
            <!-- {{listado_cargos_activos}} -->
            <vs-button style="float:right" v-on:click="guardar_bloqueo()" color="danger" icon-pack="fas" icon="fa-lock" :disabled="listado_cargos_activos ==0">Bloquear Cargos de Admisión</vs-button>
            <div style="clear:both"></div>
        </div>

    </form>
    <vs-divider />
</vx-card>
</template>

<script>
// require styles
// import {
//     VueEditor
// } from "vue2-editor";

export default {
    data() {
        return {
            popupActive2: false,
            bloqueoBuscar: false, //utlizado para evitar llamar multiples llamadas a los registros
            bloqueoBusqueda: false,
            listado_cargos_select: [],
            listado_cargos: [],
            listado_tipologia: [],
            info: {
                admision: null,
                paciente_nombre: null,
                paciente_tipo: null,
                hospital_egreso: null,
                fecha_ingreso: null,
                fecha_egreso: null
            }
        }
    },
    components: {
        // VueEditor
    },
    computed: {
        listado_cargos_activos() {
            return this.listado_cargos.filter(data => data.seleccion && !data.bloqueo).length
        }
    },
    watch: {
        'info.opcion.informe'() {
            this.info.opcion.informe_cambio = true
        },
        listado_cargos: {
            deep: true,

            // We have to move our method to a handler field
            handler(values) {

                // bloqueado
                let ar = values.filter(data => data.bloqueo && !data.seleccion)

                if (ar.length > 0) {
                    setTimeout(() => {
                        ar[0].seleccion = true
                        this.$vs.notify({
                            time: 4000,
                            title: 'Honorarios',
                            text: "El honorario se desbloquea desde el modúlo  <b>Liberar Honorarios</b>",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-right'
                        })
                    }, 100)
                } else {
                    setTimeout(() => {
                        values.filter(data => !data.seleccion && data.motivo != null).map(ar => {
                            ar.motivo = null
                        })

                    }, 100)
                }

                //pagado
                let ar2 = values.filter(data => ((data.HonorariosPagadosLotes != null && data.HonorariosPagadosLotes != "") || (data.AjenosPagosMonto != null && data.AjenosPagosMonto != "")) && data.seleccion)

                if (ar2.length > 0) {
                    setTimeout(() => {
                        ar2[0].seleccion = false
                        this.$vs.notify({
                            time: 4000,
                            title: 'Honorarios',
                            text: "El cargo ya fue procesado.",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-right'
                        })
                    }, 100)

                }

            }

        }
    },
    methods: {
        /*
            - Buscadores
            - Cargar / Listados
            - Guardar
            - Editar
            - Limpiar
            - Otros
        */

        // -------------------------------------------------------------------------------------------
        // Buscador
        // -------------------------------------------------------------------------------------------
        buscar_admision() {
            this.$refs.buscar_admision.iniciar((data) => {
                // console.log(data)
                if (data != null) {
                    this.info.admision = data.Admision
                    this.cargar_admision()
                }
            })
        },

        // -------------------------------------------------------------------------------------------
        // Cargar / Listado
        // -------------------------------------------------------------------------------------------
        cargar_tipologias() {
            this.axios.post('/app/ajenos/cargarTipologia', {})
                .then(resp => {
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        this.listado_tipologia = resp.data.json
                    }
                })
        },
        cargar_admision() {
            this.bloqueoBuscar = true;
            this.bloqueoBusqueda = true

            

            this.axios.post('/app/ajenos/cargarAdmision', {
                    admision: this.info.admision
                })
                .then(resp => {
                    // console.log(resp.data.json.length)
                    return new Promise((resolve, reject) => {
                        if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                            let res = resp.data.json[0]
                            // console.log(res)
                            // this.info.admision = 
                            this.info.paciente_nombre = res.Paciente
                            this.info.paciente_tipo = res.Seguro
                            this.info.hospital_egreso = res.Nombre
                            this.info.fecha_ingreso = this.$formato_fecha(res.Entrada)
                            this.info.fecha_egreso = this.$formato_fecha(res.Salida)

                            resolve(this.axios.post('/app/ajenos/cargarCargos', {
                                admision: this.info.admision
                            }))

                        } else {
                            // this.$vs.dialog({
                            //     color: 'danger',
                            //     title: 'Radiología - Error',
                            //     text: (resp.data.codigo < 0) ? resp.data.descripcion : 'Sin Registros',
                            // })
                            this.bloqueoBuscar = false;
                            this.bloqueoBusqueda = false
                            reject(false)
                        }
                        
                    })
                })
                .then((resp) => {
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        let res = resp.data.json

                        this.listado_cargos = res.map(data => {
                            return {
                                seleccion: (data.BloqueoHonorariosMotivo != "") ? true : false,
                                bloqueo: (data.BloqueoHonorariosMotivo != "") ? true : false,
                                tipoOrden: data.TipoOrden,
                                orden: data.Orden,
                                linea: data.Linea,
                                categoria: data.Categoria,
                                codigo: data.Producto,
                                descripcion: data.NombreProducto,
                                fecha: this.$formato_fecha(data.Fecha.split(' ')[0],'dMMy'),
                                motivo: (data.BloqueoHonorariosMotivo != "") ? data.BloqueoHonorariosMotivo : null,
                                pagar: data.Valor,
                                medico: data.NombreMedico.trim() + ' ' + data.ApellidoMedico,
                                colegiado: (data.Codigo) ? data.Codigo : data.Colegiado,
                                HonorariosPagadosLotes: data.HonorariosPagadosLotes,
                                AjenosPagosMonto: data.AjenosPagosMonto
                            }
                        })
                    }
                })
                .catch(err => {
                    console.log(err)
                    
                    this.bloqueoBuscar = false;
                    this.bloqueoBusqueda = false
                    this.$vs.notify({
                        time: 4000,
                        title: 'Honorarios',
                        text: "No se ha encontrado la admisión.",
                        iconPack: 'feather',
                        icon: 'icon-alert-circle',
                        color: 'danger',
                        position: 'bottom-right'
                    })
                    this.limpiar_admision()
                })
        },

        // -------------------------------------------------------------------------------------------
        // Guardar
        // -------------------------------------------------------------------------------------------
        guardar_bloqueo() {
            this.bloqueoBuscar = true;
            this.bloqueoBusqueda = true

            // this.$vs.loading({
            //     background: this.backgroundLoading,
            //     color: this.colorLoading,
            //     container: "#button-with-loading",
            //     scale: 0.45
            // })
            
            this.axios.post('/app/ajenos/GuardarHonorariosBloqueados', {
                    admision: this.info.admision,
                    cargos: this.listado_cargos.filter(data => data.seleccion == true && data.motivo != null).map(data => {
                        return {
                            TipoOrden: data.tipoOrden,
                            Orden: data.orden,
                            Linea: data.linea,
                            Motivo: data.motivo
                        }
                    })
                })
                .then(resp => {
                    this.cargar_admision()
                    this.$mensaje_ok('Honorarios', resp)
                    
                    // this.$vs.loading.close("#button-with-loading > .con-vs-loading")
                })
                .catch(() => {
                    
                    // this.$vs.loading.close("#button-with-loading > .con-vs-loading")
                })
        },

        // -------------------------------------------------------------------------------------------
        // Limpiar
        // -------------------------------------------------------------------------------------------
        limpiar_admision() {
            this.bloqueoBuscar = false //utlizado para evitar llamar multiples llamadas a los registros
            this.bloqueoBusqueda = false

            this.listado_cargos_select = []
            this.listado_cargos = []

            this.info.admision = null
            this.info.paciente_nombre = null
            this.info.paciente_tipo = null
            this.info.hospital_egreso = null
            this.info.fecha_ingreso = null
            this.info.fecha_egreso = null

        }

    },
    mounted() {
        this.cargar_tipologias()
    },
    created() {

    }
}
</script>

<style>
.button {
    margin: 0px !important;
}

.btnx {
    margin-left: 10px !important;
    background: red;
    border-radius: 5px 0px 0px 5px;
}

.btn-drop {
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: red;

}

.menu-funcionalidad {
    /* padding-top:0 !important; */
    /* box-shadow: 0 0 5px rgba(0, 0, 0, 0.3); */
    /* padding: 15px; */
    display: inline;
    float: right;
    border-radius: 10px;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 10px;
}

.menu-funcionalidad a {
    text-align: center;
    padding-right: 10px;
}

.menu-funcionalidad i {
    font-size: 20px;
}

.vs-switch {
    height: 30px;
    width: 60px !important;
}

.vs-circle-switch {
    height: 25px;
    width: 25px;
}

.vs-switch--icon {
    font-size: 15px
}

.vs-switch.vs-switch-active .vs-switch--circle {
    margin-left: calc(100% - 30px);
}

.popup-generar {
    height: 100%
}

select {
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #ccc;
}
</style>
