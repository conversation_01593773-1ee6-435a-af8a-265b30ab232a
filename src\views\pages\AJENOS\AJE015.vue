<template>
<vx-card title="Agenda Telefónica">
    <div class="content content-pagex">

        <vs-divider></vs-divider>

        <div class="flex flex-wrap">

            <div class="w-min md:w-1/4 lg:w-1/4 xl:w-1/4">
                <label>Tipo Contacto</label>
                <multiselect v-model="cb_tipo_telefono" :searchable="true" :close-on-select="true" :show-labels="true" :options="Lista_tipo_telefono" :custom-label="Tipo_Telefono_Seleccionado" placeholder="Seleccionar Opción" name="1" @input="onChangeTipoTelefono">
                    <span slot="noOptions">Lista no disponible.</span>
                </multiselect>
            </div>

            <div class="w-min md:w-1/4 lg:w-1/4 xl:w-1/4">
                <label>Estado</label>
                <multiselect v-model="cb_estado" :searchable="true" :close-on-select="true" :show-labels="true" :options="Lista_estados" :custom-label="Estado_Seleccionado" placeholder="Seleccionar Opción" name="1" @input="onChangeEstado">
                    <span slot="noOptions">Lista no disponible.</span>
                </multiselect>
            </div>

            <div>
                <vs-button style="float:left;margin: 25px" color="primary" type="filled" icon-pack="fas" icon="fa-search" @click="Consultar_Numeros_Telefonicos()"> Búsqueda</vs-button>
            </div>

            <div>
                <vs-button v-if="Permisos.PermisoIngreso" color="primary" style="float:left;margin:25px" type="filled" icon-pack="fas" icon="fa-plus" @click="Abrir_Ventana_Emergente_Nuevo()"> Nuevo</vs-button>
            </div>

        </div>

        <vs-divider></vs-divider>

        <vs-table2 max-items="12" pagination :data="Lista_Numeros_Telefonicos" tooltip search noDataText="Sin datos disponibles">
            <template slot="thead">

                <th width="280px" filtro="Nombre,Apellido">Nombre</th>
                <th width="280px" filtro="Departamento">Departamento / Hospital</th>
                <th width="150px" filtro="Correo">Correo</th>
                <th width="100px" filtro="Telefono">Celular</th>
                <th width="100px" filtro="TelefonoClinica">Teléfono Clínica</th>
                <th width="150px" filtro="Especialidad">Especialidad</th>
                <th width="80px"></th>
            </template>

            <template slot-scope="{data}">
                <tr :key="indextr" v-for="(tr, indextr) in data">

                    <!---    <vs-td2 width="20px" :data="data[indextr].IdAgenda">
                        {{data[indextr].IdAgenda}}
                    </vs-td2>

                    <vs-td2 width="40px" :data="data[indextr].NombreEstado">
                        {{data[indextr].NombreEstado}}
                    </vs-td2>-->

                    <vs-td2 :data="data[indextr].Nombre">
                        {{data[indextr].Nombre.trim()}} {{data[indextr].Apellido}}
                    </vs-td2>

                    <!--<vs-td2 width="150px" :data="data[indextr].Apellido">
                        {{data[indextr].Apellido}}
                    </vs-td2>--->

                    <vs-td2 :data="data[indextr].Departamento">
                        {{data[indextr].Departamento}}
                    </vs-td2>

                    <vs-td2 :data="data[indextr].Correo">
                        {{data[indextr].Correo}}
                    </vs-td2>

                    <vs-td2 :data="data[indextr].Telefono">
                        {{data[indextr].Telefono}}
                    </vs-td2>

                    <vs-td2 :data="data[indextr].TelefonoClinica">
                        {{data[indextr].TelefonoClinica}}
                    </vs-td2>

                    <vs-td2 :data="data[indextr].Especialidad">
                        {{data[indextr].Especialidad}}
                    </vs-td2>

                    <vs-td2 noTooltip align="right">
                        <vs-button v-if="Permisos.PermisoEditar" color="primary" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" @click="Abrir_Ventana_Emergente_Actualizar(data[indextr])"></vs-button>
                        <vs-button v-if="Permisos.PermisoBaja" color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="Abrir_Venta_Emergente_Baja(data[indextr])"></vs-button>
                        <vs-button v-if="Permisos.PermisoConsulta" color="warning" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-info" @click="Abrir_Ventana_Emergente_Info(data[indextr])"></vs-button>
                    </vs-td2>
                </tr>
            </template>
        </vs-table2>
    </div>
    <!---------------INICIO DE VENTANA EMERGENTE POPUP INGRESAR, MODIFICAR Y VISUALIZAR INFORMACIÓN---------->
    <vs-popup classContent="popup-example" :title="Descripcion_Emergente" :active.sync="Estado_Emergente">
        <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">

            <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
                <ValidationProvider name="tipo" rules="required" v-slot="{ errors }" class="required">
                    <label class="typo__label">Tipo:</label>
                    <multiselect :disabled="Estado_deshabilitado_botones" v-model="cb_busqueda_tipo" :searchable="true" :close-on-select="true" :show-labels="true" :options="lista_tipo" :custom-label="tipo_seleccionado" placeholder="Selecciones Opción" name="1" @input="onChangeTipo" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null">
                        <span slot="noOptions">Lista no disponible</span>
                    </multiselect>
                </ValidationProvider>
            </div>

            <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
                <label class="typo__label">Código:</label>
                <vs-input :disabled="true" id="idagenda" class="w-full" count="100" v-model="Id_Agenda" />
            </div>
            <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
                <ValidationProvider name="nombre_contacto" rules="required" v-slot="{ errors }" class="required">
                    <label class="typo__label">Nombre:</label>
                    <vs-input :disabled="Estado_deshabilitado_botones" id="nombre" class="w-full" count="100" v-model="nombre_contacto" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                </ValidationProvider>
            </div>

            <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
                <ValidationProvider name="apellido_contacto" rules="required" v-slot="{ errors }" class="required">
                    <label class="typo__label">Apellido:</label>
                    <vs-input :disabled="Estado_deshabilitado_botones" id="apellido" class="w-full" count="100" v-model="apellido_contacto" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                </ValidationProvider>
            </div>

            <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
                <ValidationProvider name="departamento_contacto" rules="required" v-slot="{ errors }" class="required">
                    <label class="typo__label">Departamento:</label>
                    <vs-input :disabled="Estado_deshabilitado_botones" id="departamento" class="w-full" count="100" v-model="departamento_contacto" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                </ValidationProvider>
            </div>
            <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
                <label class="typo__label">Correo:</label>
                <vs-input :disabled="Estado_deshabilitado_botones" id="correo" class="w-full" count="100" v-model="correo_contacto" />
            </div>
            <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
                <ValidationProvider name="telefono_contacto" rules="required" v-slot="{ errors }" class="required">
                    <label class="typo__label">Celular:</label>
                    <vs-input :disabled="Estado_deshabilitado_botones" id="telefono" class="w-full" count="100" v-model="telefono_contacto" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                </ValidationProvider>
            </div>

            <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
                <label class="typo__label">Teléfono Clinica:</label>
                <vs-input :disabled="Estado_deshabilitado_botones" id="telefono_clinica" class="w-full" count="100" v-model="telefono_clinica" />
            </div>

            <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
                <ValidationProvider name="especialidades" rules="required" v-slot="{ errors }" class="required">
                    <label class="typo__label">Especialidades:</label>
                    <multiselect :disabled="Estado_deshabilitado_botones" v-model="cb_busqueda_especialidades" :searchable="true" :close-on-select="true" :show-labels="true" :options="lista_especialidades" :custom-label="tipo_especialidades_seleccionado" placeholder="Selecciones Opción" name="1" @input="onChangeEspecialidades" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null">
                        <span slot="noOptions">Lista no disponible</span>
                    </multiselect>
                </ValidationProvider>
            </div>

            <div v-if="mostraFiltro" style="margin: 15px" class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">

                <label class="typo__label mr-12">Activo:</label>

                <label class="typo__label mr-3">Si</label>
                <input class="mr-8" type="radio" id="estado" value="1" v-model="Id_estado_seleccionado" :checked="checkedValue" :disabled="Estado_botones_activo">

                <label class="typo__label mr-3">No</label>
                <input type="radio" id="estado" value="0" v-model="Id_estado_seleccionado" :checked="checkedValue" :disabled="Estado_botones_activo">

            </div>

            <vs-divider></vs-divider>
            <vs-button v-if="Operacion =='I' || Operacion =='A'" color="primary" style="float:right;margin: 5px" :disabled="Estado_deshabilitado_botones" type="filled" icon-pack="feather" icon="icon-save" @click="Guardar_Numeros_Telefonicos()" id="btn_guardar"> Grabar </vs-button>
            <vs-button color="danger" style="float:right;margin: 5px" type="border" icon-pack="feather" icon="icon-x" @click="Estado_Emergente=false"> Cancelar</vs-button>
            <vs-divider></vs-divider>
        </div>
    </vs-popup>
    <!---------------Fin Ventana Emergente Popup---------->

    <vs-popup classContent="popup-example" :title="Descripcion_Emergente_Baja" :active.sync="Estado_Emergente_Baja">
        <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">

            <div v-if="mostraFiltro" class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
                <ValidationProvider name="tipo_contacto" rules="required" v-slot="{ errors }" class="required">
                    <label class="typo__label">Tipo:</label>
                    <multiselect :disabled="Estado_deshabilitado_botones" v-model="cb_busqueda_tipo_contacto" :searchable="true" :close-on-select="true" :show-labels="true" :options="lista_tipo_contacto" :custom-label="tipo_contacto_seleccionado" placeholder="Selecciones Opción" name="1" @input="onChangeTipoContacto" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null">
                        <span slot="noOptions">Lista no disponible</span>
                    </multiselect>
                </ValidationProvider>
            </div>

            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                <label class="typo__label">Código:</label>
                <vs-input :disabled="true" id="idagenda" class="w-full" count="100" v-model="Id_Agenda" />
            </div>

            <div class="w-full  md:w-1/2 lg:w-1/2 xl:w-1/2">
                <label class="typo__label">Nombre:</label>
                <vs-input :disabled="Estado_deshabilitado_botones" id="nombre" class="w-full" count="100" v-model="nombre_contacto" />
            </div>

            <div class="w-full  md:w-1/2 lg:w-1/2 xl:w-1/2">
                <label class="typo__label">Apellido:</label>
                <vs-input :disabled="Estado_deshabilitado_botones" id="apellido" class="w-full" count="100" v-model="apellido_contacto" />
            </div>

            <div v-if="mostraFiltro" class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                <label class="typo__label">Departamento:</label>
                <vs-input :disabled="Estado_deshabilitado_botones" id="departamento" class="w-full" count="100" v-model="departamento_contacto" />
            </div>

            <div v-if="mostraFiltro" class="w-full  md:w-1/2 lg:w-1/2 xl:w-1/2">
                <label class="typo__label">Correo:</label>
                <vs-input :disabled="Estado_deshabilitado_botones" id="correo" class="w-full" count="100" v-model="correo_contacto" />
            </div>

            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                <label class="typo__label">Número:</label>
                <vs-input :disabled="Estado_deshabilitado_botones" id="telefono" class="w-full" count="100" v-model="telefono_contacto" />
            </div>

            <div style="margin: 15px" class="w-full  md:w-1/2 lg:w-1/2 xl:w-1/2">

                <label class="typo__label mr-12">Activo:</label>

                <label class="typo__label mr-3">Si</label>
                <input class="mr-8" type="radio" id="estado" value="1" v-model="Id_estado_seleccionado" :checked="checkedValue" :disabled="Estado_botones_activo">

                <label class="typo__label mr-3">No</label>
                <input type="radio" id="estado" value="0" v-model="Id_estado_seleccionado" :checked="checkedValue" :disabled="Estado_botones_activo">

            </div>

            <vs-divider></vs-divider>
            <vs-button v-if="Operacion =='E' " color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" @click="Guardar_Numeros_Telefonicos()" id="btn_guardar"> Grabar </vs-button>
            <vs-button color="danger" style="float:right;margin: 5px" type="border" icon-pack="feather" icon="icon-x" @click="Estado_Emergente_Baja=false"> Cancelar</vs-button>
            <vs-divider></vs-divider>
        </div>
    </vs-popup>
</vx-card>
</template>

<script>
import Multiselect from 'vue-multiselect'
import "vue-multiselect/dist/vue-multiselect.min.css"

export default {
    components: {
        Multiselect
    },
    data() {
        return {

            Lista_tipo_telefono: [],
            cb_tipo_telefono: '',
            id_tipo_telefono: '',
            lista_tipo_contacto: [],
            cb_busqueda_tipo_contacto: '',
            id_tipo_contacto: '',
            lista_tipo: [],
            cb_busqueda_tipo: '',
            id_tipo: '',
            cb_estado: '',
            Lista_estados: [],
            id_estado: '',
            Lista_Numeros_Telefonicos: [],
            Estado_Emergente: false,
            telefono_clinica: '',
            cb_busqueda_especialidades: '',
            lista_especialidades: [],
            id_tipo_especialidad: '',

            Estado_botones_activo: false,
            Id_estado_seleccionado: '',

            Operacion: '', // I = Nuevo Registro / A = Actualización registor
            Estado_deshabilitado_botones: false,
            Descripcion_Emergente: '',

            Id_Agenda: 0,
            nombre_contacto: '',
            apellido_contacto: '',
            departamento_contacto: '',
            correo_contacto: '',
            telefono_contacto: '',
            checkedValue: '',
            mostraFiltro: true,
            Descripcion_Emergente_Baja: '',
            Estado_Emergente_Baja: false,

            Nombre_telefono: '',
            Apellido_telefono: '',
            Telefono: '',

            Permisos: {

                PermisoEditar: false,
                PermisoAdmin: false,
                PermisoCabina: false,
                PermisoBaja: false,
                PermisoConsulta: false,
                PermisoIngreso: false,
                PermisoAmbulancia: false

            },
            visualizacionTipoContacto: '',
            Permiso1: ''
            //cb_tipo_telefono: '',
            //cb_busqueda_tipo_contacto: ''

        };

    },
    mounted() {

        //VALIDACION DE PERMISO PARA EDITAR INFORMACION
        setTimeout(() => {
            this.$validar_funcionalidad('/Ajenos/AJE015', 'EDITAR', (d) => {
                this.Permisos.PermisoEditar = d.status;
            })
        }, 3000);

        //VALIDACION DE PERMISO PARA INACTIVAR INFORMACION
        setTimeout(() => {
            this.$validar_funcionalidad('/Ajenos/AJE015', 'INACTIVAR', (d) => {
                this.Permisos.PermisoBaja = d.status;

            })
        }, 3000);

        //VALIDACION DE PERMISO PARA VISUALIZAR INFORMACION
        setTimeout(() => {
            this.$validar_funcionalidad('/Ajenos/AJE015', 'VISUALIZAR', (d) => {
                this.Permisos.PermisoConsulta = d.status;
            })

        }, 3000);

        //VALIDACIÓN DE PERMISO PARA INGRESO DE NUEVOS NUMEROS DE CONTACTO

        setTimeout(() => {
            this.$validar_funcionalidad('/Ajenos/AJE015', 'NUEVO', (d) => {
                this.Permisos.PermisoIngreso = d.status;
            })

        }, 2000);
        this.Consultar_TipoTelefono();
        this.Consultar_Estado();
        /*

        this.Consultar_Tipo_Contacto();
        this.Consultar_Especialidades_Medicos();
        this.Consultar_Tipo();*/
        // this.Consultar_Numeros_Telefonicos();

        this.cb_tipo_telefono = {
            Descripcion: "Administrativo"
        };
        this.id_tipo_telefono = 1;
        this.cb_estado = {
            Descripcion: "Activo"
        };
        this.id_estado = 1;

        this.Consultar_Numeros_Telefonicos();
    },

    methods: {

        // VISUALIZACIÓN DE INFORMACIÓN 
        Abrir_Ventana_Emergente_Info(datos) {

            this.Descripcion_Emergente = "Información del Contacto Telefónico"
            this.Estado_Emergente = true;

            this.cb_busqueda_tipo = {
                Descripcion: datos.Descripcion

            }

            this.cb_busqueda_especialidades = {
                Descripcion: datos.Especialidad
            }
            this.Id_Agenda = datos.IdAgenda;
            this.nombre_contacto = datos.Nombre;
            this.apellido_contacto = datos.Apellido;
            this.departamento_contacto = datos.Departamento;
            this.correo_contacto = datos.Correo;
            this.telefono_contacto = datos.Telefono;

            this.id_tipo = datos.IdTipo;
            this.Id_estado_seleccionado = datos.Estado;

            this.id_tipo_especialidad = datos.CodigoEspecialidad;

            this.telefono_clinica = datos.TelefonoClinica;

            this.Estado_deshabilitado_botones = true;
            this.Estado_botones_activo = true;
            this.mostraFiltro = true;

        },
        //ACTUALIZAR INFORMACIÓN 
        Abrir_Ventana_Emergente_Actualizar(datos_editar) {
            this.Consultar_Tipo_Contacto();
            this.Consultar_Especialidades_Medicos();
            this.Consultar_Tipo();

            this.Descripcion_Emergente = 'Actualizar Información'
            this.Estado_Emergente = true;

            this.Id_Agenda = datos_editar.IdAgenda;
            this.nombre_contacto = datos_editar.Nombre;
            this.apellido_contacto = datos_editar.Apellido;
            this.departamento_contacto = datos_editar.Departamento;
            this.correo_contacto = datos_editar.Correo;
            this.telefono_contacto = datos_editar.Telefono;

            this.cb_busqueda_tipo = {
                Descripcion: datos_editar.Descripcion
            }

            this.cb_busqueda_especialidades = {
                Descripcion: datos_editar.Especialidad
            }

            this.id_tipo = datos_editar.IdTipo;
            this.Id_estado_seleccionado = datos_editar.Estado;
            this.Operacion = 'A';

            this.Estado_deshabilitado_botones = false;
            this.Estado_botones_activo = false;
            this.mostraFiltro = false;

            this.id_tipo_especialidad = datos_editar.CodigoEspecialidad;

            this.telefono_clinica = datos_editar.TelefonoClinica;

        },
        //INGRESO DE NUEVA INFORMACION
        Abrir_Ventana_Emergente_Nuevo() {

            this.Consultar_Tipo_Contacto();
            this.Consultar_Especialidades_Medicos();
            this.Consultar_Tipo();
            this.Estado_deshabilitado_botones = false;
            this.Estado_botones_activo = false;
            this.Estado_Emergente = true;
            this.mostraFiltro = true;
            this.Descripcion_Emergente = "Nuevo Número Telefónico";
            this.Operacion = 'I';
            this.Limpiar_Campos();
        },

        Abrir_Venta_Emergente_Baja(datos_baja) {

            this.Descripcion_Emergente_Baja = "INACTIVAR";
            this.Estado_Emergente_Baja = true;

            this.Id_Agenda = datos_baja.IdAgenda;
            this.nombre_contacto = datos_baja.Nombre;
            this.apellido_contacto = datos_baja.Apellido;
            this.departamento_contacto = datos_baja.Departamento;
            this.correo_contacto = datos_baja.Correo;
            this.telefono_contacto = datos_baja.Telefono;

            this.id_tipo_contacto = datos_baja.IdTipo;
            this.Id_estado_seleccionado = datos_baja.Estado;
            this.Operacion = 'E';

            this.Estado_botones_activo = false;
            this.Estado_deshabilitado_botones = true;
            this.mostraFiltro = false;

        },

        Limpiar_Campos() {
            this.Id_Agenda = 0;
            this.cb_busqueda_tipo = '';
            this.id_tipo = 0;
            this.nombre_contacto = '';
            this.apellido_contacto = '';
            this.departamento_contacto = '';
            this.correo_contacto = '';
            this.telefono_contacto = '';
            this.Id_estado_seleccionado = '';
            this.cb_busqueda_especialidades = '';
            this.id_tipo_especialidad = 0;
            this.telefono_clinica = '';
        },

        //METODO PARA LA CONSULTA DE TIPOS DE CONTACTOS ADMINISTRATIVOS, CABINA O TODOS.
        Consultar_TipoTelefono() {

            const url = this.$store.state.global.url
            this.axios.post(url + 'app/administracion/tipo_telefono', {
                    estado: 1,
                    opcion: 1

                })
                .then(resp => {

                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.Lista_tipo_telefono = [];

                    } else {
                        this.Lista_tipo_telefono = resp.data.json;

                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();

        },

        Tipo_Telefono_Seleccionado({
            Descripcion
        }) {
            return `${Descripcion}`
        },

        onChangeTipoTelefono(value) {
            //Si existe seleccionada un elemento

            if (value !== null && value.length !== 0) {

                this.id_tipo_telefono = value.CodigoTipo;

            } else {

                this.id_tipo_telefono = '';

            }
        },
        //METODO PARA CONSULTAR ESTADO ACTIVO, HE INACTIVO.
        Consultar_Estado() {

            const url = this.$store.state.global.url
            this.$vs.loading();
            this.axios.post(url + 'app/administracion/estado', {})
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.cb_estado = null; //Limpiar los valores seleccionados multiselec
                        this.Lista_estados = [];

                    } else {
                        this.Lista_estados = resp.data.json;
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();

        },
        Estado_Seleccionado({
            Descripcion
        }) {
            return `${Descripcion}`
        },

        onChangeEstado(value) {
            //Si existe seleccionada un elemento

            if (value !== null && value.length !== 0) {

                this.id_estado = value.Codigo;

            } else {

                this.id_estado = 'xx';

            }

        },

        //METODO PARA LA CONSULTA DE NUMEROS TELEFONICOS FILTRADO POR ESTADO Y TIPO DE TELEFONO.
        Consultar_Numeros_Telefonicos() {

            if (this.id_tipo_telefono == '' || this.id_tipo_telefono == null) {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Alerta',
                    text: ' Seleccione un Tipo de Contacto.',
                })
                return;
            }

            if (this.id_estado == '' || this.id_estado == null) {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Alerta',
                    text: ' Seleccione un Estatus.',
                })
                return;
            }

            const url = this.$store.state.global.url
            this.$vs.loading();
            this.axios.post(url + 'app/administracion/agenda_telefonica', {
                    IdAgenda: 0,
                    Nombre: '',
                    Apellido: '',
                    Departamento: '',
                    Correo: '',
                    Telefono: '',
                    Estado: this.id_estado,
                    Tipo: this.id_tipo_telefono,
                    Opcion: 'C',
                    Corporativo: 0
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros

                        this.Lista_Numeros_Telefonicos = "";

                    } else {
                        this.Lista_Numeros_Telefonicos = resp.data.json;

                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },

        Consultar_Especialidades_Medicos() {

            this.$vs.loading();
            this.axios.post('/app/administracion/especialidades', {
                    Opcion: 'C',
                    Empresa: 'MED'
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: resp.data.tipo_error,
                        })
                        //Limpia la tabla si no existen registros
                        this.cb_busqueda_especialidades = null;
                        this.lista_especialidades = [];

                    } else {
                        this.lista_especialidades = resp.data.json;

                    }
                })
                .catch(() => {

                })
        },

        tipo_especialidades_seleccionado({
            Descripcion
        }) {
            return `${Descripcion}`
        },

        onChangeEspecialidades(value) {

            if (value !== null && value.length !== 0) {

                this.id_tipo_especialidad = value.Codigo;

            } else {

                this.id_tipo_especialidad = '';
            }
        },

        //METODO PARA LA CONSULTA DE TIPOS DE CONTACTOS ADMINISTRATIVOS O CABINA
        Consultar_Tipo_Contacto() {

            const url = this.$store.state.global.url
            this.$vs.loading();
            this.axios.post(url + 'app/administracion/tipo_telefono', {
                    estado: 1,
                    opcion: 1
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.cb_busqueda_tipo_contacto = null; //Limpiar los valores seleccionados multiselec
                        this.lista_tipo_contacto = [];

                    } else {
                        this.lista_tipo_contacto = resp.data.json;

                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();

        },

        tipo_contacto_seleccionado({
            Descripcion
        }) {
            return `${Descripcion}`
        },

        onChangeTipoContacto(value) {
            //Si existe seleccionada un elemento

            if (value !== null && value.length !== 0) {

                this.id_tipo_contacto = value.CodigoTipo;

            } else {

                this.id_tipo_contacto = '';

            }

        },

        //METODO PARA LA CONSULTA DE TIPOS DE CONTACTOS ADMINISTRATIVOS O CABINA
        Consultar_Tipo() {

            const url = this.$store.state.global.url
            this.$vs.loading();
            this.axios.post(url + 'app/administracion/tipo_telefono', {
                    estado: 1,
                    opcion: 2
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.cb_busqueda_tipo = null; //Limpiar los valores seleccionados multiselec
                        this.lista_tipo = [];

                    } else {
                        this.lista_tipo = resp.data.json;
                        

                    }
                })
                .catch(() => {

                })

        },

        tipo_seleccionado({
            Descripcion
        }) {
            return `${Descripcion}`
        },

        onChangeTipo(value) {
            //Si existe seleccionada un elemento

            if (value !== null && value.length !== 0) {

                this.id_tipo = value.CodigoTipo;

                

            } else {

                this.id_tipo = '';

            }

        },

        //METODO PARA GUARDAR, MODIFICAR E INACTIVAR NUMEROS TELEFONICOS
        Guardar_Numeros_Telefonicos() {

            if (this.id_tipo == '' || this.id_tipo == null) {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Alerta',
                    text: ' Seleccione un Tipo de Contacto.',
                })
                return;
            }

            if (this.Id_estado_seleccionado == '' || this.Id_estado_seleccionado == null) {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Alerta',
                    text: ' Seleccione una Opcion de Activo.',
                })
                return;
            }

            if (this.id_tipo_especialidad == '' || this.id_tipo_especialidad == null) {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Alerta',
                    text: ' Seleccione una Especialidad.',
                })
                return;
            }

            this.$vs.loading();
            this.axios.post('/app/administracion/agenda_telefonica', {
                    IdAgenda: this.Id_Agenda,
                    Nombre: this.nombre_contacto,
                    Apellido: this.apellido_contacto,
                    Departamento: this.departamento_contacto,
                    Correo: this.correo_contacto,
                    Telefono: this.telefono_contacto,
                    Estado: this.Id_estado_seleccionado,
                    Tipo: this.id_tipo,
                    Opcion: this.Operacion,
                    TelefonoClinica: this.telefono_clinica,
                    CodigoEspecialidad: this.id_tipo_especialidad
                })
                .then(resp => {

                    this.$vs.loading.close();

                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: resp.data.descripcion,
                        })

                    } else {

                        setTimeout(() => {

                            this.$vs.notify({
                                color: '#0B7A0B',
                                title: 'Alerta',
                                text: resp.data.descripcion,
                            })

                        }, 2000);

                        this.Estado_Emergente_Baja = false;
                        this.Estado_Emergente = false;
                        this.nombre_contacto = '';
                        this.apellido_contacto = '';
                        this.departamento_contacto = '';
                        this.correo_contacto = '';
                        this.telefono_contacto = '';
                        this.Id_estado_seleccionado = '';
                        this.cb_busqueda_tipo_contacto = '';
                        this.cb_busqueda_tipo = '';
                        this.telefono_clinica = '';
                        this.cb_busqueda_especialidades = '';
                        this.Consultar_Numeros_Telefonicos();
                        //this.Consultar_MovimientoInventario();
                    }

                })
                .catch(() => {

                })
        },

    }
}
</script>
