<template>
<vs-row class="p-2 w-full historial-container">
    <vs-col vs-w="3" vs-justify="center" vs-align="center" class="pr-2 stickyDate">
        <vx-card>
            <!-- Se dejó toda la configuración, ya que si se usa la configuración global, se reinicia el pageIndex cada vez que se selecciona una fecha -->
            <DxDataGrid :ref="dataGridFechaKey" v-bind="DefaultDxGridConfiguration" :headerFilter="{visible:false, allowSearch:false}" :data-source="fechas" :width="'100%'" :height="'100%'" @selection-changed="onSelectionChanged">
                <DxDataGridScrolling mode="single" />
                <DxDataGridColumn :width="100" data-field="FechaRegistro" caption="Fecha" format="dd/MM/yyyy" data-type="date" alignment="center" />
                <DxDataGridColumn :width="200" data-field="NombreMedico" caption="Nombre médico" alignment="center" />
                <DxDataGridColumn :width="200" data-field="Especialidad" alignment="center">
                    <DxFormLabel :text="'Nombre médico'" />
                </DxDataGridColumn>
            </DxDataGrid>
        </vx-card>
    </vs-col>
    <vs-col vs-w="3" class="pr-2">

    </vs-col>
    <vs-col vs-justify="center" vs-align="center" vs-w="9">
        <div class="pb-0 pr-2 pl-3">
            <vx-card>
                <DxForm :form-data.sync="formulario" labelMode="floating" :read-only="true">
                    <DxFormItem item-type="group" :col-span="2" :col-count="2">
                        <DxFormItem data-field="Medico" editor-type="dxTextBox">
                            <DxFormLabel :text="'Nombre médico'" />
                        </DxFormItem>
                        <DxFormItem data-field="Especialidad" editor-type="dxTextBox" />
                    </DxFormItem>
                    <DxFormItem item-type="group" :col-span="2" :col-count="2">
                        <DxFormItem data-field="MotivoConsulta" editor-type="dxTextArea" :editor-options="{ height: 100 }">
                            <DxFormLabel :text="'Motivo de la consulta'" />
                        </DxFormItem>
                        <DxFormItem data-field="HistoriaEnfermedad" editor-type="dxTextArea" :editor-options="{ height: 100 }">
                            <DxFormLabel :text="'Historia de la enfermedad'" />
                        </DxFormItem>
                    </DxFormItem>
                </DxForm>
            </vx-card>
        </div>
        <div class="p-2 pl-3">
            <vx-card>
                <DxTabPanel id="tabPanel" ref="tabEvolucion" :repaint-changes-only="true" :height="'100%'" :data-source="tabs" :showNavButtons="true">
                    <template #title="{ data: tab }">
                        <span>
                            <font-awesome-icon class="pr-1" :icon="['fas', tab.icon]" style="font-size: 16px" />
                            {{ tab.name }}
                        </span>
                    </template>
                    <template #item="{ data: tab }">
                        <div>
                            <div v-if="tab.value === 1">
                                <Diagnostico ref="Diagnostico" v-bind="infoCita" :Modulo="2" />
                            </div>
                            <div v-if="tab.value === 2">
                                <Examen ref="Examen" v-bind="infoCita" :Modulo="2" />
                            </div>
                            <div v-if="tab.value === 3">
                                <Signos ref="Signos" v-bind="infoCita" :Modulo="2" />
                            </div>
                            <div v-if="tab.value === 4">
                                <Otros ref="Otros" v-bind="infoCita" :Modulo="2" />
                            </div>
                            <div v-if="tab.value === 5">
                                <Graficas ref="Graficas" v-bind="infoCita" :FechaNacimiento="FechaNacimiento" />
                            </div>
                        </div>
                    </template>
                </DxTabPanel>
            </vx-card>
        </div>
    </vs-col>
</vs-row>
</template>

<script>
import CustomStore from 'devextreme/data/custom_store'
import {
    DefaultDxGridConfiguration
} from './data'

import Examen from './ExamenFisico.vue'
import Signos from './Signos.vue'
import Diagnostico from './Diagnostico.vue'
import Otros from './Otros.vue'
import Graficas from './Graficas.vue'

const dataGridRefKey = 'gridExamen'
const dataGridFechaKey = 'gridFiltro'

const tabPanelEvolucion = 'tabEvolucion'

export default {
    name: 'Evolucion',
    components: {
        Examen,
        Signos,
        Diagnostico,
        Otros,
        Graficas
    },
    data() {
        return {
            examenes: [],
            examenesDataSource: new CustomStore({
                key: "id",
                load: () => {
                    return this.laboratorios;
                },
            }),
            dataGridRefKey,
            dataGridFechaKey,
            DefaultDxGridConfiguration,

            indiceTab: 0,
            tabs: [{
                    name: 'Impresión clínica / Diagnóstico',
                    value: 1,
                    disabled: true,
                    icon: "laptop-medical",
                }, {
                    name: 'Exámen físico',
                    value: 2,
                    disabled: true,
                    icon: "person-falling-burst",
                }, {
                    name: 'Signos vitales',
                    value: 3,
                    disabled: true,
                    icon: "heart-pulse",
                }, {
                    name: 'Otros',
                    value: 4,
                    disabled: true,
                    icon: "layer-group"
                },
                {
                    name: 'Gráficas',
                    value: 5,
                    disabled: true,
                    icon: "chart-line"
                },
            ],
            infoCita: {},
            signosCita: {},
            fechas: {},
            formulario: {
                Medico: null,
                Especialidad: null,
                MotivoConsulta: null,
                HistoriaEnfermedad: null
            },
        }
    },
    props: {
        IdCita: null,
        IdCliente: null,
        IdPaciente: null,
        IdConsulta: null,

        FechaNacimiento: null,
        Sexo: null,
        Edad: null
    },
    methods: {
        BusquedaFechasCita() {
            this.axios.post('/app/v1_Historial_Clinico/BusquedaFechasCita', {
                    IdCliente: this.IdCliente
                })
                .then(resp => {
                    this.fechas = resp.data.json

                    // this.refreshFechaDataGrid()
                })
        },
        BusquedaHistorialClinico(e) {
            this.axios.post('/app/v1_Historial_Clinico/BusquedaHistorialClinico', {
                    IdCliente: e.IdCliente,
                    IdCita: e.IdCita
                })
                .then(resp => {
                    this.formulario.MotivoConsulta = resp.data.json[0].Motivo
                    this.formulario.HistoriaEnfermedad = resp.data.json[0].Historia

                    this.infoCita = {
                        Sexo: this.Sexo,
                        ...e,
                        ...resp.data.json[0]
                    }
                })
        },
        refreshDataGrid() {
            this.dataGrid.refresh()
                .then(function () {

                });
            this.dataGrid.clearSelection();
            this.dataGrid.clearSorting();
            this.dataGrid.pageIndex(0);
        },
        refreshFechaDataGrid() {
            this.dataGridFecha.pageIndex(0);
            this.dataGridFecha.clearSelection();
            this.dataGridFecha.clearSorting();
            this.dataGridFecha.columnOption('Fecha', 'sortOrder', 'desc')
            this.botones = [];
        },
        formatFecha(date) {
            const year = new Date(date).toLocaleString('default', {
                year: 'numeric'
            });
            const month = new Date(date).toLocaleString('default', {
                month: '2-digit'
            });
            const day = new Date(date).toLocaleString('default', {
                day: '2-digit'
            });

            return [year, month, day].join('');
        },
        //Método para detectar cuando se selecciona una fila de la tabla de filtrado de fecha
        onSelectionChanged(e) {
            if (e.selectedRowsData.length > 0) {
                this.BusquedaHistorialClinico({
                    IdCita: e.selectedRowsData[0].IdCita,
                    IdConsulta: e.selectedRowsData[0].IdConsulta,
                    IdCliente: this.IdCliente
                })

                this.formulario.Medico = e.selectedRowsData[0].NombreMedico
                this.formulario.Especialidad = e.selectedRowsData[0].Especialidad

                for (let i of this.panelEvolucion._dataSource._items) {
                    i.disabled = false
                    if (this.Edad > 13 && i.value == 5) {
                        i.disabled = true
                    }
                }
            }
        },
        onRowClick(e) {
            if (e.data) {
                this.BusquedaHistorialClinico({
                    IdCita: e.data.IdCita,
                    IdConsulta: e.data.IdConsulta,
                    IdCliente: this.IdCliente
                })

                for (let i of this.panelEvolucion._dataSource._items) {
                    i.disabled = false
                }
            }
        },
        refresh() {
            this.refreshFechaDataGrid();
            this.infoCita = {}
            this.formulario.HistoriaEnfermedad = null
            this.formulario.MotivoConsulta = null
            this.formulario.Medico = null
            this.formulario.Especialidad = null

            for (let i of this.panelEvolucion._dataSource._items) {
                i.disabled = true
            }
            this.panelEvolucion.resetOption('selectedIndex')

            if (this.$refs.Examen && this.$refs.Examen.LimpiarVariables) {
                this.$refs.Examen.LimpiarVariables()
            }
            if (this.$refs.Signos && this.$refs.Signos.LimpiarVariables) {
                this.$refs.Signos.LimpiarVariables()
            } 
            if (this.$refs.Diagnostico && this.$refs.Diagnostico.LimpiarVariables) {
                this.$refs.Diagnostico.LimpiarVariables()
            } 
            if (this.$refs.Otros && this.$refs.Otros.LimpiarVariables) {
                this.$refs.Otros.LimpiarVariables()
            } 
            if (this.$refs.Graficas && this.$refs.Graficas.LimpiarVariables) {
                this.$refs.Graficas.LimpiarVariables()
            } 
        },
    },
    mounted() {
        if (this.IdCliente !== undefined && this.IdCliente !== '' && this.IdCliente !== null) {
            this.BusquedaFechasCita()
        }
    },
    watch: {
        'IdCliente'(newval) {
            if (newval !== undefined && newval !== '' && newval !== null) {
                this.BusquedaFechasCita()
            }
        },
    },
    computed: {

        // dataGrid: function () {
        //     return this.$refs[dataGridRefKey].instance;
        // },

        dataGridFecha: function () {
            return this.$refs[dataGridFechaKey].instance;
        },

        panelEvolucion: function () {
            return this.$refs[tabPanelEvolucion].instance;
        },
    },
}
</script>

<style>
.stickyDate {
    /* position: -webkit-sticky !important; */
    /* for browser compatibility */
    position: fixed !important;
    top: 70px;
    /* left: 15px; */
    /* background-color: rgb(117, 177, 255); */
    z-index: 1200;
}
</style>
