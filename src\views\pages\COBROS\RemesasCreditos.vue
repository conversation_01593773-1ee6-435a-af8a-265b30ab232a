<template>
    <div class="remesas-creditos-container">
        <dx-data-grid class="remesas-creditos-datagrid my-cell-invalid" ref="gridCreditos" v-bind="gridOptions" :data-source="creditos" @row-prepared="onRowPrepared" @row-updating="onRowUpdating" @row-inserted="onRowInserted" :editing="editing" @editor-preparing="onEditorPreparing" @row-inserting="onRowInserting" @init-new-row="onInitNewRow" @onEditCanceled="CancelarEdicion">
            <dx-data-grid-column data-field="FormaPago" caption="Forma" width="55" :allow-editing="false" />
            <dx-data-grid-column data-field="ChequeNumero" data-type="number" caption="Cheque No." width="95" :validation-rules="[{type:'required', message: 'Número de Cheque es requerido'}, duplicateRule]" :editor-options="{format:'####################'}" />
            <dx-data-grid-column data-field="CuentaCheque" caption="Cuenta No." width="120" :validation-rules="[ srtLenRule('El número de cuenta', 1,20), {type:'required', message: 'Número de Cuenta del cheque es requerido'} ,duplicateRule]" />
            <dx-data-grid-column data-field="FechaEmision" caption="Fecha Emisión" width="120" v-bind="dateOptions" :validation-rules="[{type:'required', message: 'Ingrese la fecha de emisión del cheque', reevaluate:true}]" />
            <dx-data-grid-column data-field="BancoEmisor" caption="Banco" :min-width="250" :validation-rules="[{type:'required', message: 'Ingrese la banco de emisión del cheque'}]">
                <DxDataGridLookup :data-source="bancosDataSource" value-expr="Codigo" :display-expr="(x)=> {return ''.concat(x.Codigo, ' - ', x.Nombre)}" />
            </dx-data-grid-column>
            <dx-data-grid-column data-field="Aseguradora" caption="Seguro" :min-width="250" :validation-rules="[{type:'required', message: 'Seleccione un seguro'}]">
                <DxDataGridLookup :data-source="aseguradorasDataSource" value-expr="Asegura" :display-expr="(x)=> {return ''.concat(x.Asegura, ' - ', x.Nombre)}" />
            </dx-data-grid-column>
            <dx-data-grid-column data-field="Monto" width="120" :fixed="true" fixed-position="right" :validation-rules="[moneyRule, {type:'required', message: 'Ingrese el monto del cheque'}]" data-type="number" format="'Q.' ,##0.00" />
            <dx-data-grid-column data-field="Saldo" width="90" :allow-editing="false" :visible="showNonEditableColumns" data-type="number" format="'Q.' ,##0.00" />
            <dx-data-grid-column data-field="FechaCompensado" caption="Depositado" width="90" v-bind="dateOptions" :allow-editing="false" :visible="showNonEditableColumns" />
            <dx-data-grid-column data-field="FechaLiquidado" width="90" v-bind="dateOptions" :allow-editing="false" :visible="showNonEditableColumns" />
            <dx-data-grid-column data-field="UsuarioLiquidador" width="90" :allow-editing="false" :visible="showNonEditableColumns" />

            <dx-data-grid-column data-field="FechaBloqueado" width="90" v-bind="dateOptions" :allow-editing="false" :visible="showNonEditableColumns" />
            <dx-data-grid-column data-field="UsuarioBloqueo" width="90" :allow-editing="false" :visible="showNonEditableColumns" />

            <dx-data-grid-column v-if="modo=='NUEVO'" caption="Acción" type="buttons" width="65" :allow-resizing="false" :fixed="true" :buttons="botones" />
            <dx-data-grid-column v-else caption="Acción" type="buttons" width="65" :allow-resizing="false" :fixed="true" cell-template="dropdowntemplate" css-class="remesa-credito-actions" />
            <template #dropdowntemplate="{data}">
                <AfiliadoAcciones :data="data" :action-buttons="cellButtonConf" @action-item-click="onItemClick" />
            </template>

            <template #definicion-colores={data}>
                <div :class="['credito-texto dx-button dx-button-mode-contained m-1 p-1', data.css] ">
                    {{data.text}}
                </div>
            </template>
        </dx-data-grid>
        <vs-prompt
            @cancel="ChapulinCancelarPrompt"
            @accept="promptConf.accept"
            @close="CancelarEdicion"
            :is-valid="promptValid"
            :active.sync="promptConf.active"
            :title="promptConf.title"
            accept-text="Aceptar"
            cancel-text="Cancelar"
        >
            <div class="con-exemple-prompt">
                 <b>{{ promptConf.message}}</b>.
                <vs-input ref="inputRazon" class="w-full" placeholder="Razón" v-model="promptConf.value" @keydown.esc="CancelarEdicion" @keydown.enter="()=>{if(promptValid) promptConf.accept()}"/>

                <vs-alert :active="!(promptConf.value && promptConf.value.length >= 8 && promptConf.value.length <= 100)" color="danger" icon="new_releases" >
                    {{ promptConf.invalidMessage }}
                </vs-alert>
            </div>
        </vs-prompt>
    </div>
</template>

<script>
import {
    gridConfiguration,
    moneyRule,
    srtLenRule,
    confMoneda,
} from './data'
import {
    dateOptions,
    buildFilterExpresion
} from './data'
import AfiliadoAcciones from '../SASI/AfiliadoAcciones.vue'
import Swal from 'sweetalert2'

const pathPermiso = '/COBROS/COB009'

 // validate es la funcion aplica la validación para editar el campo en base a  datos locales del cheque recibodos en data, devuelve null si es valido de lo contrario el mensaje de error si 
const updateGrantEndPoint = {
    ChequeNumero: {
        name: 'ChequeNumero',
        permiso: 'CHEQUE_MODIFICA_NUMERO',
        endpoint: 'ChequeNumero',
        validate: (data)=> {
            return (data.Monto == data.Saldo? null: 'El cheque ya fue operado parcialmente, Verifique por favor...')
                ?? (data.Status == 'P' ? null: 'El documento debe estar habilitado')
        }
    },
    CuentaCheque: {
        name: 'CuentaCheque',
        permiso: 'CHEQUE_MODIFICA_CUENTA',
        endpoint: 'ChequeCuenta',
        validate: (data)=> {
            return (data.Monto == data.Saldo? null: 'El cheque ya fue operado parcialmente, Verifique por favor...')
                ?? (data.Status == 'P' ? null: 'El documento debe estar habilitado')
        }
    },
    BancoEmisor: {
        name: 'BancoEmisor',
        permiso: 'CHEQUE_MODIFICA_BANCO',
        endpoint: 'ChequeBanco',
        validate: (data)=> {
            return (data.Monto == data.Saldo? null: 'El cheque ya fue operado parcialmente, Verifique por favor...')
                ?? (data.Status == 'P' ? null: 'El documento debe estar habilitado')
        }
    },
    Aseguradora: {
        name: 'Aseguradora',
        permiso: 'CHEQUE_MODIFICA_SEGURO',
        endpoint: 'ChequeAseguradora',
        validate: (data)=> {
            return (data.Monto == data.Saldo? null: 'El cheque ya fue operado parcialmente, Verifique por favor...')
                ?? (data.Status == 'P' ? null: 'El documento debe estar habilitado')
        }
    },
    Monto: {
        name: 'Monto',
        permiso: 'CHEQUE_MODIFICA_MONTO',
        endpoint: 'ChequeMonto',
        validate: (data)=> {
            return data.Status == 'P'? null: 'El documento debe estar habilitado'
        }
    },
    Rechazar: {
        name: 'Status',
        permiso: 'CHEQUE_RECHAZAR',
        endpoint: 'RechazarCheque',
        validate: (data)=> {
            return data.Status != 'R'? null: 'El documento ya se encuentra en un estado de rechazado'
        }
    },
    Habilitar: {
        name: 'Status',
        permiso: 'CHEQUE_HABILITAR',
        endpoint: 'HabilitarCheque',
        validate: (data)=> {
            return ['B','R'].includes(data.Status)? null: 'El documento no se encuentra en un estado Bloqueado/Rechazado'
        }
    },
    Bloquear: {
        name: 'Status',
        permiso: 'CHEQUE_BLOQUEAR',
        endpoint: 'BloquearCheque',
        validate: (data)=> {
            return (data.Monto == data.Saldo? null: 'El cheque ya fue operado parcialmente, Verifique por favor...')
                ?? (data.Status != 'B'? null: 'El documento se encuentra en un estado de bloqueado')
        }
    },

}
Object.freeze(updateGrantEndPoint)

export default {
    name: 'RemesasCreditos',
    props: {
        modo: String,
        save: {
            type: Boolean,
            default: false,
        },
        tipo: {
            type: String,
            default: 'B',
        },
        remesa: {
            type: Object,
            default () {
                return {
                    Empresa: null,
                    Codigo: null,
                }
            }
        },
        catalogoBancos: Array,
        catalogoAseguradoras: Array,
    },
    components: {
        AfiliadoAcciones,
    },
    data() {
        return {
            creditos: [],
            bancos: [],
            aseguradoras: [],
            filterExp: buildFilterExpresion("Display"),

            aseguradorasDataSource: {
                paginate: true,
                pageSize: 10,
                sort: 'Asegura',
                key: 'Asegura',
                byKey: (e) => {
                    return this.aseguradoras.Asegura === e ? e.Nombre : ''
                },
                load: (e) => {
                    if (!e.searchValue && !e.take)
                        return this.aseguradoras

                    if (e.take) {
                        if (e.skip === e.take) {
                            e.take = e.take + 10
                        }
                    }
                    return this.$filtrado_avanzado_palabras(e.searchValue, this.aseguradoras, 'Display', e.skip, e.skip + e.take)
                }
            },
            bancosDataSource: {
                paginate: true,
                pageSize: 10,
                sort: 'Codigo',
                key: 'Codigo',
                byKey: (e) => {
                    return this.bancos.Codigo === e ? e.Nombre : ''
                },
                load: (e) => {
                    if (!e.searchValue && !e.take)
                        return this.bancos

                    if (e.take) {
                        if (e.skip === e.take) {
                            e.take = e.take + 10
                        }
                    }
                    return this.$filtrado_avanzado_palabras(e.searchValue, this.bancos, 'Display', e.skip, e.skip + e.take)
                }
            },

            gridOptions: {
                keyExpr:'key',
                ...gridConfiguration,
                groupPanel: null,
                headerFilter: null,
                loadPanel: null,
                searchPanel: {
                    visible: true
                },
                columnHidingEnabled: false,
                toolbar: {
                    visible: true,

                    items: [{
                            text: 'Default',
                            css: 'credito-defecto',
                            location: 'after',
                            locateInMenu: 'auto',
                            template: 'definicion-colores',
                        },
                        {
                            text: 'Cheque NO depositado',
                            css: 'credito-no-depositado',
                            location: 'after',
                            locateInMenu: 'auto',
                            template: 'definicion-colores',
                        },
                        {
                            text: 'Cheque Liquidado',
                            css: 'credito-liquidado',
                            location: 'after',
                            locateInMenu: 'auto',
                            template: 'definicion-colores',
                        },
                        {
                            text: 'Cheque Bloqueado',
                            css: 'credito-bloqueado',
                            location: 'after',
                            locateInMenu: 'auto',
                            template: 'definicion-colores',
                        },
                        {
                            text: 'Cheque Rechazado',
                            css: 'credito-rechazado',
                            location: 'after',
                            locateInMenu: 'auto',
                            template: 'definicion-colores',
                        },
                        'searchPanel',
                        {
                            name: 'addRowButton',
                            locateInMenu: 'never',
                        },
                        // 'cancelButton',
                    ]
                },
                paging: {
                    enabled: true,
                },
                scrolling: {
                    mode: 'infinite',
                },
                width: '100%',

                wordWrapEnabled: false,
                summary: {
                    recalculateWhileEditing: true,
                    calculateCustomSummary: (e) => {
                        switch (e.summaryProcess) {
                            case 'start':
                                e.totalValue = 0
                                break
                            case 'calculate':
                                e.totalValue += e.value.Status != 'B' ? e.value.Monto : 0
                                break
                            default:
                                break
                        }
                    },
                    totalItems: [{
                        summaryType: 'custom',
                        name: 'montoTotal',
                        showInColumn: 'Monto',
                        valueFormat: confMoneda.format,
                    }, ],
                },
            },
            moneyRule,
            dateOptions,
            cellButtonConf: [{
                    id: 'editarMonto',
                    icon: 'fas fa-money-bill',
                    visible: () => true,
                    text: 'Mod. monto documento',
                    onClick: (e) => { //ojo ver el template que se esta enviando los datos del datarow al data del item de acciones :)
                        this.EditarCampo(updateGrantEndPoint.Monto.name, e.rowData)
                    }
                },
                {
                    id: 'editarNumero',
                    icon: 'fas fa-money-check',
                    visible: () => true,
                    text: 'Mod. número documento',
                    onClick: (e) => {
                        this.EditarCampo(updateGrantEndPoint.ChequeNumero.name, e.rowData)
                    }
                },

                {
                    id: 'editarNumeroCuenta',
                    icon: 'fas fa-hashtag',
                    visible: () => true,
                    onClick: (e) => {
                        this.EditarCampo(updateGrantEndPoint.CuentaCheque.name, e.rowData)
                    },
                    text: 'Mod. No. cuenta documento'
                },
                {
                    id: 'editarBanco',
                    icon: 'fas fa-building',
                    visible: () => true,
                    onClick: (e) => {
                        this.EditarCampo(updateGrantEndPoint.BancoEmisor.name, e.rowData)
                    },
                    text: 'Modificar banco emisor'
                },
                {
                    id: 'editarAseguradora',
                    icon: 'fas fa-hand-holding-usd',
                    visible: () => true,
                    onClick: (e) => {
                        this.EditarCampo(updateGrantEndPoint.Aseguradora.name, e.rowData)
                    },
                    text: 'Modificar aseguradora'
                },
                {
                    id: 'bloquear',
                    icon: 'lock',
                    visible: () => true,
                    text: 'Bloquear Cheque',
                    onClick: (e) => {
                        this.promptConf.title = 'Bloqueo de cheque'
                        this.promptConf.invalidMessage = 'La razón debe tener entre 8 y 100 caracteres'
                        this.promptConf.message = 'Ingrese la razón del bloqueo'
                        this.IniciarCambioEstado(updateGrantEndPoint.Bloquear, e.rowData.data)
                    }
                },
                {
                    id: 'habilitar',
                    icon: 'unlock',
                    visible: () => true,
                    text: 'Habilitar Cheque',
                    onClick: (e) => {
                        this.promptConf.title = 'Habilitar cheque'
                        this.promptConf.invalidMessage = 'La resolución debe tener entre 8 y 100 caracteres'
                        this.promptConf.message = 'Ingrese el número de resolución'
                        this.IniciarCambioEstado(updateGrantEndPoint.Habilitar, e.rowData.data)
                    }
                },
                {
                    id: 'rechazar',
                    icon: 'fas fa-ban',
                    visible: () => true,
                    text: 'Cheque Rechazo',
                    onClick: (e) => {
                        this.promptConf.title = 'Rechazo de cheque'
                        this.promptConf.invalidMessage = 'La razón de rechazo debe tener entre 8 y 100 caracteres'
                        this.promptConf.message = 'Ingrese la razón de rechazo'
                        this.IniciarCambioEstado(updateGrantEndPoint.Rechazar, e.rowData.data)
                    }
                },
            ],
            //datos de actualización 
            corporativoAutoriza: null,
            observaciones: null,
            //valida duplicados por Cuenta del cheque y numero de cheque
            editingField: '', //nombre del campo que se esta editando para documento existente
            duplicateRule: {
                type: 'async',
                reevaluate: true,
                message: 'El documento fue registrado anteriomente',
                validationCallback: (e) => {
                    return new Promise((resolve, reject) => {
                        const numero = e.data.ChequeNumero
                        const cuenta = e.data.CuentaCheque
                        const key    = e.data.__KEY__
                        if (e.data.CuentaCheque)
                            this.tempUltimaCta = e.data.CuentaCheque
                        
                        if (!numero || !cuenta || ['Monto', 'Aseguradora', 'BancoEmisor', ].includes(this.editingField) || this.modo == "LECTURA") {
                            //NOTA: en el modo lectura hay botones para actualizar cada celda se delega la valiación de cheque repetido al backen al darle grabar para no hacer dos petiniones
                            e.rule.message = ''
                            resolve()
                        } else if (this.creditos.find(x => x.ChequeNumero == numero && x.CuentaCheque == cuenta && x.__KEY__ != key)) {
                            reject('Este documento ya fue agreagdo a esta remesa')
                        } else {
                            this.axios.post('/app/v1_cobros/RemesaCheques', {
                                ChequeNumero: numero,
                                CuentaCheque: cuenta,
                            }).then(resp => {
                                if (!resp.data.json.length) {
                                    e.rule.reevaluate = false
                                    resolve()
                                } else {
                                    reject('El documento fue registrado anteriomente en otra remesa')
                                }

                            }).catch(() => {
                                reject('No fue poible validar el documento, error de conexión')
                            })
                        }

                    })

                }
            },
            promptConf:{
                active: false,
                message: '',
                value: '',
                valid: false,
                invalidMessage: '',
                accept: ()=>{},
            },
            tempUltimaCta: null,//ultima cuenta de cheque ingresada para colocar por defecto cada vez que agregue una nueva fila
            // cuentaOptions: {
            //     editorType: 'dxTextBox',
            //     editorOptions: {
            //         onFocusIn: (e) => {
            //             e.component.option('value',this.tempUltimaCta?? this.remesa.CuentaNumero)
            //         }
            //     },
            // },
        }

    },
    methods: {
        srtLenRule,
        normalizeText: (x) => {
            /*eslint-disable */
            return x.normalize('NFD').replace(/([^n\u0300-\u036f]|n(?!\u0303(?![\u0300-\u036f])))[\u0300-\u036f]+/gi, "$1")
            /*eslint-enable */
        },
        searchExprA(e) {
            return this.normalizeText(e.Asegura + e.Nombre)
        },
        onItemClick(e) {
            // e => cell template data https://js.devexpress.com/Vue/Documentation/22_1/ApiReference/UI_Components/dxDataGrid/Configuration/columns/#cellTemplate
            e.itemData.onClick(e)
        },
        onRowUpdating(e) {
            if (this.modo == 'NUEVO'){
                e.newData.Saldo = e.newData.Monto?? e.oldData.Monto
                return
            }
            
            e.cancel = true

            this.axios.post(`/app/v1_cobros/${updateGrantEndPoint[this.editingField].endpoint}`, {
                Documento: e.oldData,
                Monto: e.newData[this.editingField], //monto del cheque
                Numero: e.newData[this.editingField], //nuemero de cheque y numero de cuenta
                Codigo: e.newData[this.editingField], //Aseguradora y banco
                Corporativo: this.corporativoAutoriza
            }).then(() => {
                this.gridInstance.cancelEditData()
                this.Cargar()
                this.corporativoAutoriza = null
                this.editingField = ''
            })
        },
        onRowInserting(e) {
            e.data.Saldo = e.data.Monto
        },
        onRowPrepared(e) {
            if (e.rowType == 'data' && e.data.Remesa) {
                // B, R, P
                if (e.data.Status == 'B')
                    e.rowElement.classList ?.add('credito-bloqueado')

                else if (e.data.Status == 'R')
                    e.rowElement.classList ?.add('credito-rechazado')

                else if (e.data.FechaLiquidado)
                    e.rowElement.classList ?.add('credito-liquidado')

                else if (!e.data.FechaCompensado)
                    e.rowElement.classList ?.add('credito-no-depositado')

                else if (e.data.Status == 'P')
                    e.rowElement.classList ?.add('credito-defecto')
            }
        },
        onInitNewRow(e) {
            e.data.FormaPago = this.tipo
            e.data.CuentaCheque = this.tempUltimaCta?? this.remesa.CuentaNumero?.replace(/[^\d-]/g,'')
        },
        onEditorPreparing(e) {
            if (e.editorName == 'dxDateBox')
                e.editorOptions = {
                    ...e.editorOptions,
                    ...this.dateOptions.editorOptions
                }
            if (e.editorName == 'dxSelectBox')
                e.editorOptions.dropDownOptions = {
                    width: 500
                }

            if (this.modo != 'NUEVO' && e.row && !e.row.isNewRow) {
                e.editorOptions.buttons = [{
                    name: 'confirmar',
                    options: {
                        icon: 'save',
                        stylingMode: 'text',
                        onClick: () => {
                            this.gridInstance.saveEditData()
                        }
                    }
                }, ]
            }
        },
        onRowInserted() {
            this.$emit('update', this.creditos)
            if (this.modo == 'NUEVO')
                this.$nextTick(()=>{
                    this.gridInstance.addRow()
                })
        },

        Cargar() {
            this.creditos = null
            this.axios.post('/app/v1_cobros/RemesaCheques', {
                Remesa: this.remesa.Codigo
            }).then((resp) => {
                this.creditos = resp.data.json.map(x=>{
                    x.key = ''.concat(x.Empresa, x.Remesa, x.ChequeNumero, x.CuentaCheque)
                    return x
                })
                this.$emit('loaded-data', resp.data.json)
                this.$emit('update', resp.data.json)
                this.$emit('input', resp.data.json)
            })
        },
        CargarBancos() {
            this.axios.post('/app/v1_cobros/CatalogoBancos', {}).then(resp => {
                this.bancos = resp.data.filter(x => x.Tipo == 'B').map(m => {
                    m.Display = this.normalizeText(String.prototype.concat(m.Codigo, m.Nombre))
                    return m
                })
                this.$emit('loaded-bancos', this.bancos)
            })
        },
        CargarAseguradoras() {
            this.axios.post("/app/v1_cobros/CatalogoAseguradoras", {}).then(resp => {
                this.aseguradoras = resp.data.json.map(m => {
                    m.Display = this.normalizeText(String.prototype.concat(m.Asegura, m.Nombre))
                    return m
                })
                this.$emit('loaded-asegura', this.aseguradoras)
            })
        },
        EditarCampo(name, rowData) {
            const v = updateGrantEndPoint[name].validate(rowData.data)
            if(v)
                Swal.fire({
                    icon: 'warning',
                    title: v,
                    allowOutsideClick: false,
                    confirmButtonText: 'Aceptar',
                })
            else
                this.$validar_func_usuario_permiso(pathPermiso, updateGrantEndPoint[name].permiso).then((x) => {
                    this.editingField = name
                    this.corporativoAutoriza = x.corporativo
                    const rowIndex = this.gridInstance.getRowIndexByKey(rowData.data.key)
                    this.gridInstance.editCell(rowIndex, name)
                })
        },
        CancelarEdicion() {
            this.promptConf.active = false
            this.corporativoAutoriza = null
        },

        /**
         * Iniciacila la validaion de permiso e ingreso de observación de cambio de estado
         * @param {*} conf referencia al campo updateGrantEndPoint que se esta actualizando
         * @param {*} doc data del cheque que se va a editar
         */
        IniciarCambioEstado(conf, doc) {
            const v = conf.validate(doc)
            if(v)
                Swal.fire({
                    icon: 'warning',
                    title: v,
                    allowOutsideClick: false,
                    confirmButtonText: 'Aceptar',
                })
            else
                this.$validar_func_usuario_permiso(pathPermiso, conf.permiso)
                .then((x) => {
                    this.corporativoAutoriza = x.corporativo
                    this.promptConf.value = ''
                    this.promptConf.active = true
                    this.promptConf.accept = ()=> {
                        this.promptConf.active = true//para que no se cierre y salga la consola de error de client with
                        this.CambiarEstado(conf.endpoint, doc)
                    }
                    setTimeout(() => {
                        this.$refs.inputRazon.focusInput()
                    }, 500)
                })
        },

        /**
         * Ejecuta la actualización de estado
         * @param {*} endpoint ruta de backen en base al estado que se colocará
         * @param {*} docu datos del documento o cheque a aactualizar (por la pk)
         */
        CambiarEstado(endpoint, docu) {
            this.axios.post(`/app/v1_cobros/${endpoint}`, {
                Documento: docu,
                Observaciones: this.promptConf.value,
                Corporativo: this.corporativoAutoriza,
            }).then(() => {
                this.Cargar()
                this.corporativoAutoriza = null
                this.promptConf.active = false
            })
        },
        //para evitar consola de "client-width"
        ChapulinCancelarPrompt() {
            this.promptConf.active = true
            this.$nextTick(()=>{
                this.CancelarEdicion()
            })
        },
    },
    watch: {
        'remesa.Codigo'(value) {
            if (value)
                this.Cargar()
            else {
                this.creditos = null
                this.corporativoAutoriza = null
                this.editingField = ''
            }
                
        }
    },

    created() {
        if (!this.catalogoBancos)
            this.CargarBancos()
        else
            this.bancos = this.catalogoBancos

        if (!this.catalogoBancos)
            this.CargarAseguradoras()
        else
            this.aseguradoras = this.catalogoAseguradoras

        if (this.remesa.Codigo)
            this.Cargar()
    },
    computed: {
        editing() {
            return {
                allowAdding: this.modo == 'NUEVO',
                allowUpdating: ['EDITAR', 'NUEVO'].includes(this.modo),
                allowDeleting: this.modo == 'NUEVO',
                useIcons: true,
                newRowPosition: 'last',
                mode: this.modo == 'NUEVO' ? 'row' : 'cell'
            }
        },
        gridInstance() {
            return this.$refs['gridCreditos'].instance
        },
        showNonEditableColumns() {
            return !['NUEVO', 'EDITAR'].includes(this.modo)
        },
        montoTotal() {
            return this.creditos.reduce((acc, curr) => {
                acc += curr.Status != 'B' ? curr.Monto : 0
                return acc
            }, 0)
        },
        promptValid() {
            return (Boolean(this.promptConf.value) && this.promptConf.value.length >= 8 && this.promptConf.value.length <= 100)
        },
        botones() {
            return this.modo=='NUEVO'? [{name:'save', icon:'check'},'cancel','edit', 'delete']: [{name:'save', icon:'check'},'cancel','edit',]
        }
    },
}
</script>

<style>
/*Colores de las filas de detalle remesas credito*/
.credito-texto {
    font-size: 14px !important;
    font-weight: 400 !important;
}

.credito-defecto td,
.credito-defecto.dx-item {
    background-color: white !important;
}

.credito-no-depositado td,
.credito-no-depositado.credito-texto {
    background-color: #FDFD96 !important;
    /* background-color: yellow !important; */
}

.credito-liquidado td,
.credito-liquidado.credito-texto {
    background-color: #77DD77 !important;
    /* background-color: #85BB65 !important; */
}

.credito-bloqueado td,
.credito-bloqueado.credito-texto {
    background-color: #FF6961 !important;
    /* background-color: red !important; */
    /* color: white !important; */
}

.credito-rechazado td,
.credito-rechazado.credito-texto {
    background-color: #C576F6 !important;
}

.remesa-def-color-box {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: black solid 1px;
}

.remesa-credito-actions .dx-button {
    border-radius: 5px !important;
    height: 25px !important;
    background-color: white !important;
}

td.remesa-credito-actions {
    background-color: rgba(255, 255, 255, 0.133) !important;
}

.remesas-creditos-datagrid {
    max-height: 530px;
}

.lulo-class {
    font-size: large;
}
</style>
