<template>
<div class="p-2">
    <vx-card>
        <div class="p-2">
            <vs-button v-if="!Consulta" class="button" color="success" type="filled" style="width: 200px; size: 20px" @click="AbrirReceta">
                <font-awesome-icon :icon="['fas', 'file-prescription']" class="pr-2" style="font-size: 16px" />
                <span>Ingresar receta</span>
            </vs-button>
        </div>
        <div>
            <DxDataGrid v-bind="DefaultDxGridConfiguration" :data-source="recetas" :filter-sync-enabled="true" :headerFilter="{ visible: true , allowSearch: true }"  :height="'100%'">
                <DxDataGridSelection mode="single" />

                <DxDataGridColumn :width="50" alignment="center" cell-template="reporte" />

                <DxDataGridColumn :width="100" data-field="NoReceta" :allowHeaderFiltering="false" caption="Receta" data-type="string" alignment="center" />
                <DxDataGridColumn :width="100" data-field="IdCita" :allowHeaderFiltering="false" caption="Cita" data-type="string" alignment="center" />
                <DxDataGridColumn :width="120" data-field="FechaReceta" caption="Fecha" format="dd/MM/yyyy" data-type="date" alignment="center" />
                <DxDataGridColumn :width="200" data-field="Medico" :allowHeaderFiltering="false" caption="Médico" data-type="string" alignment="center" />

                <template #reporte="{data: info}">
                    <DxButton width="30px" height="30px" type="danger" styling-mode="contained" class="botonReporte" @click="MostrarReporte(info.data.NoReceta, info.data.IdCita)" hint="Imprimir orden">
                        <font-awesome-icon :icon="['fas', 'file-pdf']" style="font-size: 20px;" />
                    </DxButton>
                </template>
            </DxDataGrid>
        </div>
    </vx-card>

    <!-- Popup de prescripción -->
    <DxPopup :visible.sync="mostrarPrescripcion" :width="'95%'" :height="'95%'" :show-title="true" :full-screen="false" :hide-on-outside-click="false" title="Prescripción médica" :showCloseButton="true">
        <DxScrollView :scroll-by-conent="true">
            <div>
                <Prescripcion ref="Prescripcion" v-bind="$props" @noReceta="ImprimirReceta" />
            </div>
        </DxScrollView>
    </DxPopup>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'

import Prescripcion from './Prescripcion.vue'

import {
    comandoReceta
} from './data'

export default {
    name: 'Receta',
    components: {
        Prescripcion
    },
    data() {
        return {
            DefaultDxGridConfiguration,
            recetas: [],
            comandoReceta,
            comando: '',
            mostrarPrescripcion: false,
            IdCitaReporte: null
        }
    },
    props: {
        IdCita: null,
        IdCliente: null,
        IdPaciente: null,
        IdMedico: null,
        IdFicha: null,
        IdConsulta: null,

        Consulta: null // Variable para saber si se está llamando desde la búsqueda, y determinar si mostrar el botón para ingresar nuevas órdenes 
    },
    methods: {
        BusquedaReceta() {
            this.axios.post('/app/v1_Historial_Clinico/BusquedaReporteReceta', {
                    IdCliente: this.IdCliente
                })
                .then(resp => {
                    this.recetas = resp.data.json
                })
        },
        LimpiarVariables() {
            this.recetas = []
            this.$refs.Prescripcion.LimpiarVariables()
        },
        AbrirReceta() {
            this.mostrarPrescripcion = true
            this.$refs.Prescripcion.CargarDatos()
            // this.$socke_up(comandoReceta + this.IdCita)
        },
        ImprimirReceta(e) { // Imprime la receta desde el modal que genera las recetas
            this.mostrarPrescripcion = false
            this.$refs.Prescripcion.LimpiarVariables()
            this.$reporte_modal({
                Nombre: 'Receta',
                Opciones: {
                    IdReceta: e,
                    IdCita: this.IdCita
                }
            })

            this.BusquedaReceta()
        },
        async MostrarReporte(receta, cita) {
            await this.$reporte_modal({
                Nombre: 'Receta',
                Opciones: {
                    IdReceta: receta,
                    IdCita: cita
                }
            })
        },
    },
    mounted() {
        this.BusquedaReceta()
    },
    watch: {
        'IdCliente'(newval) {
            if (newval !== '' && newval !== null && newval !== undefined) {
                this.BusquedaReceta()
            }
        },
    },
}
</script>

<style scoped>
.div-button {
    display: flex;
    justify-content: center;
}
</style>
