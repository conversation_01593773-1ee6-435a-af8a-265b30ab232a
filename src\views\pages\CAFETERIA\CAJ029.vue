<template>
    <vx-card title="Consulta Recibos">
        <!-- Ingreso de Datos de Recibo  -->
        <vs-divider></vs-divider>
            <div class="box-custom">
                <div class="w-full">
                    <vs-row vs-justify="center">
                        <div class="xs:w-full md:w-12/12 lg:w-12/12 xl:w-12/12">
                            <div class="flex flex-wrap">
                                <div class="div-input">
                                    <ValidationProvider name="serie" rules="required|max:3" class="required" v-slot="{errors}">
                                        <label class="label-static">Serie:</label>
                                        <vs-input class="w-full input-serie"  v-model="serie" v-on:keyup = "serie = serie.toUpperCase()" ref="inputSerie" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"></vs-input>
                                    </ValidationProvider>
                                </div>
                                
                                <div class="div-input">
                                    <ValidationProvider name="recibo" rules="required" class="required" v-slot="{errors}">
                                        <label class="label-static">Número:</label>
                                        <vs-input class="w-full input-numero" v-model="recibo" ref="inputRecibo"  :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" v-on:blur= "ConsultaR()" v-on:keyup.enter="ConsultaR()"></vs-input>
                                    </ValidationProvider>
                                </div>
                            </div>
                        </div>
                    </vs-row>
                    <!-- Indica el estado o el tipo (ANULADO - ANTICIPADO)  -->
                    <vs-row vs-justify="right">
                        <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">
                            <div class="div-input">
                                <input class="w-full label-info" :value="estadoA" disabled/>
                                <input class="w-full label-info" :value="tipoD" disabled/>
                            </div>
                            <vs-divider></vs-divider>
                        </div>
                    </vs-row>
                </div>
                <br>
                <!--Datos Recibos - Datos anulados-->
                <div class="w-full">
                    <vs-row vs-justify="left">
                        <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">
                            <div class="div-input flex-wrap">
                                <label class="label-static">Código:</label>
                                <input class="w-full label-info" :value="paciente" disabled/>
                            </div>
                        </div>    
                        <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">   
                            <div class="div-input flex-wrap">
                                <label class="label-static">Total:</label>
                                <input class="w-full label-info" :value="total" disabled/>
                            </div> 
                        </div> 
                        <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12" v-if="datosAnulados">   
                            <div class="div-input flex-wrap">
                                <label class="label-static">Por:</label>
                                <input class="w-full label-info" :value="anulaciones.usuario" disabled/>
                            </div> 
                        </div> 
                    </vs-row>  
                    <vs-row vs-justify="left">  
                        <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">    
                            <div class="div-input flex-wrap">
                                <label class="label-static">A Nombre de:</label>
                                <input class="w-full label-info" :value="nombre" disabled/>
                            </div>
                        </div>    
                        <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">   
                            <div class="div-input flex-wrap">
                                <label class="label-static">Admisión:</label>
                                <input class="w-full label-info" :value="numAdmision" disabled/>
                            </div>
                        </div>
                        <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12" v-if="datosAnulados">   
                            <div class="div-input flex-wrap">
                                <label class="label-static">Fecha:</label>
                                <input class="w-full label-info" :value="anulaciones.fecha" disabled/>
                            </div>
                        </div>
                    </vs-row> 
                    <vs-row vs-justify="left">
                        <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">       
                            <div class="div-input flex-wrap">
                                <label class="label-static">Del:</label>
                                <input class="w-full label-info" :value="registro" disabled/>
                            </div>
                        </div>    
                        <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">   
                            <div class="div-input flex-wrap">
                                <label class="label-static">Cajero:</label>
                                <input class="w-full label-info" :value="usuario" disabled/>
                            </div> 
                        </div> 
                        <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12" v-if="datosAnulados">   
                            <div class="div-input flex-wrap">
                                <label class="label-static">Razón:</label>
                                <input class="w-full label-info" :value="anulaciones.razon" disabled/>
                            </div> 
                        </div> 
                    </vs-row>  
                </div>
                <!--Datos Ajenos-->
                <div class="w-full">
                    <div class="flex flex-wrap">
                        <div class="xs:w-full md:w-6/12 lg:w-6/12 xl:w-6/12" v-if="gridAjenos">
                            <vs-divider></vs-divider>
                            <h5>Ajenos</h5>
                            <br>
                            <vs-table2 max-items="10" :data="ajenos" height="200px" class="mt-0 mb-0">
                                <template slot="thead">
                                    <th order="NombreCompleto" width="50px">Nombre del Médico (Ajeno)</th>
                                    <th order="Monto" width="50px">Monto</th>
                                    <th order="Saldo" width="50px">Saldo</th>
                                </template>
                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td2>{{tr.NombreCompleto}}</vs-td2>
                                        <vs-td2 class="align_rigth">{{tr.Monto}}</vs-td2>
                                        <vs-td2>{{tr.Saldo}}</vs-td2>
                                    </tr>
                                </template>
                            </vs-table2>
                        </div>
                    </div>
                </div>
                <!-- Datos Factura -->
                <div class="w-full">
                    <div class="flex flex-wrap">
                        <div class="xs:w-full md:w-6/12 lg:w-6/12 xl:w-6/12" v-if="gridFacturas">
                            <vs-divider></vs-divider>
                            <h5>Facturas</h5>
                            <br>
                            <vs-table2 max-items="10" :data="facturas" height="200px" class="mt-0 mb-0">
                                <template slot="thead">
                                    <th order="Numero" width="50px">Facturas</th>
                                    <th order="Monto" width="50px">Abono</th>
                                    <th order="Fecha" width="50px">Fecha</th>
                                </template>
                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td2>{{tr.Numero}}</vs-td2>
                                        <vs-td2 class="align_rigth">{{tr.Monto}}</vs-td2>
                                        <vs-td2>{{tr.Fecha}}</vs-td2>
                                    </tr>
                                </template>
                            </vs-table2>
                        </div> 
                    </div>
                </div>
                <!-- Datos Remisión -->
                <div class="w-full">
                    <div class="flex flex-wrap">
                        <div class="xs:w-full md:w-5/12 lg:w-5/12 xl:w-5/12" v-if="gridRemisiones">
                            <vs-divider></vs-divider>
                            <h5>Remisiones</h5>
                            <br>
                            <vs-table2 max-items="10" :data="remisiones" height="200px" class="mt-0 mb-0">
                                <template slot="thead">
                                    <th order="ID" width="50px">Remisiones</th>
                                    <th order="Monto" width="50px">Abono</th>
                                    <th order="Fecha" width="50px">Fecha</th>
                                    <th order="IdAdmision" width="50px">Admision</th>
                                </template>
                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td2>{{tr.ID}}</vs-td2>
                                        <vs-td2 class="align_rigth">{{tr.Monto}}</vs-td2>
                                        <vs-td2>{{tr.Fecha}}</vs-td2>
                                        <vs-td2>{{tr.IdAdmision}}</vs-td2>
                                    </tr>
                                </template>
                            </vs-table2>
                        </div>
                    </div> 
                </div>
               <!-- Datos Devolucion -->
               <div class="w-full" v-if="datosDevolucion">
                <vs-divider></vs-divider>
                <h5>Devolución</h5>
                <br>
                    <vs-row vs-justify="left">
                        <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12" >
                            <div class="div-input flex-wrap">
                                <label class="label-static">Por:</label>
                                <input class="w-full label-info" :value="devoluciones.usuario" disabled/>
                            </div>
                        </div>    
                        <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">   
                            <div class="div-input flex-wrap">
                                <label class="label-static">Cuenta Cheque:</label>
                                <input class="w-full label-info" :value="devoluciones.cuentaBanco" disabled/>
                            </div> 
                        </div> 
                        <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">   
                            <div class="div-input flex-wrap">
                                <label class="label-static">Valor:</label>
                                <input class="w-full label-info" :value="devoluciones.monto" disabled/>
                            </div> 
                        </div> 
                    </vs-row>  
                    <vs-row vs-justify="left">  
                        <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">    
                            <div class="div-input flex-wrap">
                                <label class="label-static">Fecha:</label>
                                <input class="w-full label-info" :value="devoluciones.fecha" disabled/>
                            </div>
                        </div>    
                        <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">   
                            <div class="div-input flex-wrap">
                                <label class="label-static">Cheque:</label>
                                <input class="w-full label-info" :value="devoluciones.movimiento" disabled/>
                            </div>
                        </div>
                    </vs-row> 
                    <vs-row vs-justify="left">
                        <div class="xs:w-full md:w-8/12 lg:w-8/12 xl:w-8/12">
                            <div class="div-input flex-wrap">
                                <label class="label-static">Razon:</label>
                                <input class="w-full label-info" :value="devoluciones.razon" disabled/>
                            </div>
                        </div>       
                    </vs-row>
                </div> 
               <!--Forma de pagos-->
                <div class="w-full"  v-if="gridFormaP">
                <vs-divider></vs-divider>
                        <div class="xs:w-full md:w-12/12 lg:w-12/12 xl:w-12/12">
                            <h5>Formas de Pago</h5>
                            <br>
                            <vs-table2 max-items="10" :data="fpago" class="mt-0 mb-0">
                                <template slot="thead">
                                    <th order="FechaEfectividad" width="150px">Fecha Efectividad</th>
                                    <th order="Status" width="80px">Status</th>
                                    <th order="Forma" width="80px">Forma</th>
                                    <th order="Monto" width="100px">Monto</th>
                                    <th order="BancoTarjeta" width="100px">Banco/Tarjeta</th>
                                    <th order="Nombre" width="250px">Nombre</th>
                                    <th order="Previsado" width="80px">Previsado</th>
                                    <th order="Documento" width="80px">Documento</th>
                                    <th order="Cuenta" width="100px">Cuenta</th>
                                </template>
                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td2>{{tr.FechaEfectividad}}</vs-td2>
                                        <vs-td2>{{tr.Status}}</vs-td2>
                                        <vs-td2>{{tr.Forma}}</vs-td2>
                                        <vs-td2 class="align_rigth">{{tr.Monto}}</vs-td2>
                                        <vs-td2>{{tr.BancoTarjeta}}</vs-td2>
                                        <vs-td2>{{tr.Nombre}}</vs-td2>
                                        <vs-td2>{{tr.Previsado}}</vs-td2>
                                        <vs-td2>{{tr.Documento}}</vs-td2>
                                        <vs-td2>{{tr.Cuenta}}</vs-td2>
                                    </tr>
                                </template>
                            </vs-table2>
                    </div>
                </div>
            </div> 
       </vx-card>
</template>
<script>
 export default{
    data() {
        return {
                tipo: '',
                serie: '',
                recibo: '',
                paciente: '',
                nombre:  '',
                estado:  '',
                registro:  '',
                total: '',
                usuario:  '',
                numAdmision:  '',
                estadoA: '',
                tipoD: '',
            anulaciones: {
                usuario: '',
                fecha: '',
                razon: ''
             },
            facturas: [],
            remisiones: [],
            ajenos: [],
            //devoluciones
            devoluciones: {
                codigoD: '',
                fecha: '',
                registro: '',
				cuentaBanco: '',
				movimiento: '',
				usuario: '',
				razon: '',
				monto: '',
            },
            //forma de pago
            fpago: [],
            datosAnulados: false,
            datosDevolucion: false,
            gridAjenos: false,
            gridFacturas: false,
            gridRemisiones: false,
            gridFormaP: false,
        };
    },
   
    methods: {
            //Recupera los datos de Recibo
            ConsultaR() {
                this.axios.post("/app/v1_JefeCaja/ConsultarRecibos", {
                    SerieRecibo: this.serie,
                    Recibo: this.recibo
                })
                .then(resp => {
                    //Mapeo de datos
                    if (resp.data.codigo == 0 ){
                        this.tipo = resp.data.Tipo
                        this.paciente = resp.data.Paciente
                        this.nombre = resp.data.Nombre
                        this.estado = resp.data.Status
                        this.registro = resp.data.Registro
                        this.total = parseFloat(resp.data.Total).toFixed(2)
                        this.usuario = resp.data.Usuario
                        this.numAdmision = resp.data.NumAdmision
                        this.estadoA = resp.data.EstadoA
                        this.tipoD = resp.data.TipoD
                        this.Cargar().ConsultaAFR()
                    }
                })                        
            },
                
        Cargar(){
            return {
                // Carga Anulaciones, Facturas, Remisiones segun el status y tipo del Recibo
                ConsultaAFR: ()=>{ 
                    this.axios.post("/app/v1_JefeCaja/CargarAFR", {
                        SerieRecibo: this.serie,
                        Recibo: this.recibo,
                        Status: this.estado,
                        Tipo: this.tipo
                    })
                    .then(resp => {
                        if(!this.isEmptyObject(resp.data.json)){
                            //Mapeo de datos
                            resp.data.json.map(data => {
                                if (data.Datos == "A"){
                                    this.anulaciones.razon = data.Razon
                                    this.anulaciones.usuario = data.Usuario
                                    this.anulaciones.fecha = data.Fecha
                                    this.datosAnulados = true
                                    this.gridRemisiones = false
                                    this.gridFacturas = false
                                    this.facturas = ''
                                    this.remisiones= ''
                                }
                                else if (data.Datos == "F"){
                                    this.estadoA = ''
                                    this.anulaciones.razon = ''
                                    this.anulaciones.usuario = ''
                                    this.anulaciones.fecha = ''
                                    this.datosAnulados = false
                                    this.gridRemisiones = false
                                    this.facturas = resp.data.json.map(m => {                                     
                                        this.gridFacturas = true
                                            return {
                                                ...m,
                                                Monto: parseFloat(m.Monto).toFixed(2)
                                            } 
                                        }) 
                                }
                                else if (data.Datos == "R"){
                                    this.gridRemisiones = true
                                    this.gridFacturas = false
                                    this.datosAnulados = false
                                    this.remisiones = resp.data.json.map(m => {
                                        return {
                                            ...m,
                                            Monto: parseFloat(m.Monto).toFixed(2)
                                        }              
                                    }) 
                                }
                            })   
                        }
                        else{
                            this.gridRemisiones = false
                            this.gridFacturas = false
                        }
                        this.Cargar().CargaA();
                    })  
                },
                 //Carga Ajenos para el llenado de Grid  
                CargaA:()=>{
                    this.axios.post("/app/v1_JefeCaja/CargarAjenos", {
                        SerieRecibo: this.serie,
                        Recibo: this.recibo
                    })
                    .then(resp => {
                        if(!this.isEmptyObject(resp.data.json)){
                            //Mapeo de datos
                            this.ajenos = resp.data.json.map(m => {
                            this.gridAjenos = true
                                return {
                                    ...m,
                                    Monto: parseFloat(m.Monto).toFixed(2)
                                }  
                            })
                        }
                        else{
                            this.gridAjenos = false
                        }
                        this.Cargar().CargaD()
                    })
                },
                //Carga Devoluciones
                CargaD:()=>{
                    this.axios.post("/app/v1_JefeCaja/CargarDevoluciones", {
                        SerieRecibo: this.serie,
                        Recibo: this.recibo
                    })
                    .then(resp => {
                        if(!this.isEmptyObject(resp.data.json)){
                            //Mapeo de datos
                            resp.data.json.map(data => {
                                this.devoluciones.codigoD = data.CodDevolucion
                                this.devoluciones.fecha = data.Fecha
                                this.devoluciones.registro = data.Registro
                                this.devoluciones.cuentaBanco = data.CuentaBanco
                                this.devoluciones.movimiento = data.Movimiento
                                this.devoluciones.usuario = data.Usuario
                                this.devoluciones.razon = data.Razon
                                this.devoluciones.monto = parseFloat(data.Monto).toFixed(2) 
                                this.datosDevolucion = true
                            }) 
                        }
                        else{
                            this.datosDevolucion= false
                        }
                        this.Cargar().CargaFP();  
                    })
                },
                //Carga Forma de Pago  
                CargaFP:()=>{
                    this.axios.post("/app/v1_JefeCaja/CargarFormaPago", {
                        SerieRecibo: this.serie,
                        Recibo: this.recibo
                    })
                    .then(resp => {
                        if(!this.isEmptyObject(resp.data.json)){
                            //Mapeo de datos
                            this.fpago = resp.data.json.map(m => {
                            this.gridFormaP = true
                                return {
                                    ...m,
                                    Monto: parseFloat(m.Monto).toFixed(2)
                                }  
                            })
                        }
                        else{
                            this.gridFormaP = false
                        }
                    })
                },
            }
    },
    //Método para validar si el objeto trae datos (JasonTable)
    isEmptyObject(obj){
        return Object.keys(obj).length === 0;
    } 
    }
}


</script>

<style lang="scss" scoped>
    .div-input{
        padding: 10px;
        .input-razon{
            width: 200px;
        }
    }
 
    .label-info{
        margin-left: 10px;
        color:#E74C3C;
        opacity: 0.7;
        border: 0;
        font-size: 16px;
        background-color: white;
        font-weight: bold;
    }
    .label-staticRef{
        font-weight: bold;
        padding-left: 15px;
    }
    .label-static{
        font-weight: bold;
        padding-left: 5px;
    }
    .align_rigth{
        text-align: right;
    }
</style>