<template>
<vx-card title="Contabilidad General">

    <vs-divider class="label-size">CUENTAS</vs-divider>
    <div class="content content-pagex">

        <div class="terms">
            <vs-button color="primary" style="float:left;margin: 5px" type="filled" icon-pack="fas" icon="fa-plus" @click="NuevaCuenta()"> Nueva</vs-button>
            <div class=" md:w-1/5 lg:w-1/5 xl:w-1/5, label-size" style="float:left;margin: 15px" align="right">
                <vs-radio v-model="ActivaF" vs-name="Activa" vs-value="S"> Activa</vs-radio>
            </div>
            <div class=" md:w-1/5 lg:w-1/5 xl:w-1/5, label-size" style="float:left;margin: 15px" align="center">
                <vs-radio v-model="ActivaF" vs-name="Activa" vs-value="N"> Inactiva</vs-radio>
            </div>
            <div class=" md:w-1/5 lg:w-1/5 xl:w-1/5, label-size" style="float:left;margin: 15px" align="left">
                <vs-radio v-model="ActivaF" vs-name="Activa" vs-value=""> Todas</vs-radio>
            </div>
            <vs-button color="primary" style="float:left;margin: 5px" type="filled" icon-pack="fas" icon="fa-search" @click="ConsultasCuentas()"> Buscar Cuentas</vs-button>
            <vs-button color="warning" style="float:left;margin: 5px" type="filled" icon-pack="feather" icon="icon-layers" @click="Inicializar()"> Inicializar</vs-button>
        </div>
        <br>
        <br>
        <vs-divider class="label-size"></vs-divider>
        <div>
            <br>
            <br>
            <div>

                <vs-table2 tooltip max-items="10" pagination :data="ListaCuentas" noDataText="Sin datos disponibles" search id="tb_departamentos">

                    <template slot="thead">
                        <th width="20px">Código</th>
                        <th width="200px">Nombre</th>
                        <th width="20px">Tipo</th>
                        <th width="20px">Folio Mayor</th>
                        <th width="20px">Instrucciones</th>
                        <th width="20px">Saldo Inicial</th>
                        <th width="10px">Sub Cuentas</th>
                        <th width="20px">Tipo Actividad</th>
                        <th width="20px">Editar</th>
                        <th width="50px" style="text-align: center;">Estado</th>
                        <th width="30px">Cambio de Estado</th>
                    </template>

                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">

                            <vs-td2  noTooltip :data="data[indextr].CodigoCuenta">
                                {{data[indextr].CodigoCuenta}}
                            </vs-td2>
                            <vs-td2 :data="data[indextr].Nombre">
                                {{data[indextr].Nombre}}
                            </vs-td2>
                            <vs-td2 noTooltip :data="data[indextr].NombreTipo">
                                {{data[indextr].Tipo}} - {{data[indextr].NombreTipo}}
                            </vs-td2>
                            <vs-td2  noTooltip :data="data[indextr].FolioMayor" align="center">
                                {{data[indextr].FolioMayor}}
                            </vs-td2>
                            <vs-td2  noTooltip :data="data[indextr].Instrucciones">
                                {{data[indextr].Instrucciones}}
                            </vs-td2>
                            <vs-td2  noTooltip :data="data[indextr].SaldoIniDebe" align="right">
                                {{parseFloat(data[indextr].SaldoIniDebe).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                            </vs-td2>
                            <vs-td2  noTooltip :data="data[indextr].TieneSubCuentas" align="center">
                                {{data[indextr].TieneSubCuentas}}
                            </vs-td2>
                            <vs-td2  noTooltip :data="data[indextr].TipoActividad" align="center">
                                {{data[indextr].TipoActividad}}
                            </vs-td2>
                            <vs-td2 noTooltip align="center">
                                <vs-button color="success" icon-pack="feather" icon="icon-edit-1" v-if="data[indextr].Activa === 'N'" disabled="true" style="display:inline-block;margin-right:2px" @click="VentanaEditar(data[indextr])"></vs-button>
                                <vs-button color="success" icon-pack="feather" icon="icon-edit-1" v-else style="display:inline-block;margin-right:2px" @click="VentanaEditar(data[indextr])"></vs-button>
                            </vs-td2>
                            <vs-td2 noTooltip style="text-align: center;" variant="success" width="70px" :data="data[indextr].Activa">
                                {{data[indextr].Activa==='S'? 'ACTIVA':'INACTIVA'}}
                            </vs-td2>
                            <vs-td2 noTooltip align="center" :data="data[indextr].Activa">
                                <vs-switch v-model="data[indextr].Estado" :v-value="data[indextr].Estado" @click="BajaCuenta(data[indextr])"> </vs-switch>
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
        </div>
        <!--------------- Nuevo / Editar (Ventana Emergente)-------------->
        <vs-popup classContent="popup-example" :title="TituloVentana" :active.sync="EstadoVentana">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">

                <form @submit.prevent="()=>{}">
                    <!--- Inicio listas desplegables -->
                    <vs-divider class="label-size">{{TituloVentana}}</vs-divider>
                    <div style="margin: 15px" class="flex flex-wrap">
                        <div class="w-full md:w-5/5 lg:w-5/5 xl:w-2/5" style="float:left;padding:10px 70px">
                            <ValidationProvider name="Tipo" class="required">
                                <label class="label-size">Tipo</label>
                                <multiselect v-model="cbTipoCuentas" :options="ListaTipo" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="TipoSeleccionado" placeholder="Seleccionar" @input="onChangeTipoCuentas">
                                    <span slot="noOptions">Lista no disponible.</span>
                                </multiselect>
                            </ValidationProvider>
                        </div>
                        <!-- <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5"></div> -->
                        <div class="w-full md:w-5/5 lg:w-5/5 xl:w-2/5" style="float:left;padding:10px 70px">
                            <!----- INICIO SWITCH --->
                            <div style="margin: 15px" class="flex flex-wrap">
                                <div class="w-full sm:w-1/2" style="margin-bottom:15px">
                                    <label class="label-size"> Activa</label>
                                    <vs-switch v-model="Activa"> </vs-switch>
                                </div>
                                <div class="w-full sm:w-1/2" style="margin-bottom:15px">
                                    <label class="label-size"> SubCuentas</label>
                                    <vs-switch v-model="TieneSubCuentas"> </vs-switch>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div style="margin: 15px" class="flex flex-wrap">
                        <div class="w-full md:w-4/5 lg:w-4/5 xl:w-2/5" style="float:left;padding:10px 70px">
                            <ValidationProvider name="Codigo" rules="required|max:16" v-slot="{ errors }" class="required">
                                <label class="label-size"> Código</label>
                                <vs-input class="w-full" type="number" v-model="CodigoCuenta" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="DeshabilitarControles" v-on:blur="ValidacionCodigo" />
                            </ValidationProvider>
                        </div>

                        <div class="w-full md:w-4/5 lg:w-4/5 xl:w-2/5" style="float:left;padding:10px 70px">
                            <ValidationProvider name="Folio" rules="|numero_entero|numero_min:0" v-slot="{ errors }">
                                <label class="label-size"> Folio</label>
                                <vs-input class="w-full" type="number" v-model="Folio" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" v-on:blur="ValidacionFolio" />
                            </ValidationProvider>
                        </div>
                    </div>
                    <div style="margin: 15px" class="flex flex-wrap">
                        <div class="w-full md:w-5/5 lg:w-5/5 xl:w-2/5" style="float:left;padding:10px 70px">
                            <ValidationProvider name="Nombre" rules="required|max:40" v-slot="{ errors }" class="required">
                                <label class="label-size"> Nombre</label>
                                <vs-input class="w-full" count="40" v-model="NombreCuenta" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-5/5 lg:w-5/5 xl:w-3/5" style="float:left;padding:10px 70px">
                            <ValidationProvider name="Instrucciones" rules="max:200" v-slot="{ errors }">
                                <label class="label-size"> Instrucciones</label>
                                <vs-textarea class="label-size" counter="200" name="Instrucciones" v-model="Instrucciones" />
                                <span style="position:relative;top:-18px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                            </ValidationProvider>
                        </div>
                    </div>
                    <div style="margin: 15px" class="flex flex-wrap">
                        <div class="w-full md:w-4/5 lg:w-4/5 xl:w-2/5" style="float:left;padding:10px 70px;">
                            <ValidationProvider name="SaldoInicialDebe" rules="max:12">
                                <label class="label-size"> Saldo Inicial</label>
                                <vs-input class="w-full" type="number" count="12" v-model="SaldoInicialDebe" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5" style="float:left;padding:10px 70px" v-show="ShowTipoAct">
                            <vs-divider class="label-size"> Tipo de Actividad</vs-divider>
                            <br>
                            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5, label-size" style="float:left;margin: 5px" align="center">
                                <vs-radio v-model="TipoActividad" vs-value="E"> Efectivo</vs-radio>
                            </div>
                            <div class="w-full  md:w-1/5 lg:w-1/5 xl:w-1/5, label-size" style="float:left;margin: 5px" align="center">
                                <vs-radio v-model="TipoActividad" vs-value="O"> Operación</vs-radio>
                            </div>
                            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5, label-size" style="float:left;margin: 5px" align="center">
                                <vs-radio v-model="TipoActividad" vs-value="I"> Inversión</vs-radio>
                            </div>
                            <div class="w-full  md:w-1/5 lg:w-1/5 xl:w-1/5, label-size" style="float:left;margin: 5px" align="center">
                                <vs-radio v-model="TipoActividad" vs-value="F"> Financiamiento</vs-radio>
                            </div>

                        </div>
                        <vs-divider></vs-divider>
                        <div class="w-full" style="float:right;padding:10px 70px" align="right">
                            <vs-button color="primary" v-if="NoGuardar == true" :disabled=true type="filled" icon-pack="feather" icon="icon-save" @click="GuardarRegistro()"> Grabar</vs-button>
                            <vs-button color="primary" v-else type="filled" icon-pack="feather" icon="icon-save" @click="GuardarRegistro()"> Grabar</vs-button>
                        </div>
                        <vs-divider></vs-divider>
                    </div>
                    <div>

                        <vs-table2 max-items="15" class="label-size" pagination :data="ListaActividad" noDataText="Sin datos disponibles" v-show="ShowActividad">
                            <template slot="thead">
                                <th class="label-size">Período</th>
                                <th class="label-size">Descripción</th>
                                <th class="label-size">Debe</th>
                                <th class="label-size">Haber</th>
                                <th class="label-size">Actividad</th>
                                <th class="label-size">Saldo</th>
                            </template>

                            <template slot-scope="{data}">
                                <tr :key="indextr" v-bind:class="{'StatusAgendaPendiente': tr.Status === 'P', 'StatusAgendaFinalizado': tr.Status === 'F', 'StatusAgendaError': tr.Status === 'E', 'StatusAgendaCancelada': tr.Status === 'C', 'StatusAgendaNingun': tr.Status === 'N'}" v-for="(tr, indextr) in data">
                                    <vs-td2 :data="tr.Periodo">{{tr.Periodo}}</vs-td2>
                                    <vs-td2 :data="tr.Descripcion">{{tr.Descripcion}}</vs-td2>
                                    <vs-td2 :data="tr.Debe">{{parseFloat(tr.Debe).toFixed(2)}}</vs-td2>
                                    <vs-td2 :data="tr.Haber">{{parseFloat(tr.Haber).toFixed(2)}}</vs-td2>
                                    <vs-td2 :data="tr.Activdidad">{{parseFloat(tr.Actividad).toFixed(2)}}</vs-td2>
                                    <vs-td2 :data="tr.Saldo" >{{parseFloat(tr.Saldo).toFixed(2)}}</vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                </form>
            </div>
        </vs-popup>
        <!---------------Fin Ventana Emergente _---------->
        <!--------------- Inicializar Saldos-------------->
        <vs-popup classContent="popup-example" :title="TituloVentana" :active.sync="EstadoVentanaI">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">

                <form @submit.prevent="()=>{}">
                    <!--- Inicio listas desplegables -->
                    <vs-divider class="label-size">INICIALIZACIÓN DE SALDOS</vs-divider>
                    <div align="center" style="margin: 15px" class="flex flex-wrap">
                        <vs-button color="warning" style="float:left;margin: 5px" type="filled" icon-pack="feather" icon="icon-plus" @click="Totalizar()"> Sumar Saldos</vs-button>
                        <vs-button color="primary" style="float:left;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" @click="GrabarSaldos()"> Grabar Saldos</vs-button>

                    </div>
                    <vs-divider></vs-divider>

                    <div style="margin: 30px" class="flex flex-wrap">
                        <div class="w-full md:w-6/12 lg:w-6/12 xl:w-6/12">
                            <ValidationProvider name="Periodos">
                                <label class="label-size">Período</label>
                                <ValidationProvider name=" Períodos">
                                    <multiselect v-model="cbPeriodos" :options="ListaPeriodos" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="PeriodoSeleccionado" placeholder="Seleccionar Período" @input="onChangePeriodos">
                                        <span slot="noOptions">Lista no disponible.</span>
                                    </multiselect>
                                </ValidationProvider>
                            </ValidationProvider>
                        </div>
                        <br>
                        <br>
                        <!-- Ordenar Por  -->
                        <div class="w-full md:w-6/12 lg:w-6/12 xl:w-6/12, label-size" style="padding:10px 50px">
                            <label class="label-size"> Ordenar Por</label>
                            <br>
                            <vs-radio v-model="FolioF" vs-name="Filtro" vs-value=1 style="margin: 10px"> Folio</vs-radio>
                            <vs-radio v-model="FolioF" vs-name="Filtro" vs-value=0 style="margin: 10px"> Código</vs-radio>
                        </div>
                    </div>
                    <div class="flex flex-wrap" style="margin: 30px">
                        <div class="w-full md:w-6/12 lg:w-6/12 xl:w-6/12" style="float:left">
                            <label class="label-sizer"> Total Debe: </label>
                            <label class="label-sizer" style="margin: 15px"> {{ $formato_moneda(Debe) }} </label>
                        </div>
                        <div class="w-full md:w-6/12 lg:w-6/12 xl:w-6/12" style="padding:1px 30px">
                            <label class="label-sizer" style="margin: 15px"> Total Haber: </label>
                            <label class="label-sizer" style="margin: 15px"> {{ $formato_moneda(Haber) }} </label>
                        </div>
                    </div>
                    <!-- Grid folio - codigo - Nombre -->
                    <vs-table2 tooltip max-items="10" class="label-size" pagination :data="ListaCuentaI" noDataText="Sin datos disponibles" search id="tb_CuentasI">
                        <template slot="thead">
                            <th width="10px" class="label-size">Folio</th>
                            <th width="10px" class="label-size">Código</th>
                            <th width="170px" class="label-size">Nombre</th>
                            <th width="10px" class="label-size">Debe</th>
                            <th width="10px" class="label-size">Haber</th>
                        </template>

                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 noTooltip :data="tr.FolioMayor">{{tr.FolioMayor}}</vs-td2>
                                <vs-td2 noTooltip :data="tr.CodigoCuenta">{{tr.CodigoCuenta}}</vs-td2>
                                <vs-td2 :data="tr.Nombre">{{tr.Nombre}}</vs-td2>
                                <vs-td2 noTooltip :data="tr.SaldoIniDebe">
                                    <vs-input @blur="ActualizarC(tr)" v-model="tr.SaldoIniDebe" align="right" />
                                </vs-td2>
                                <vs-td2 noTooltip :data="tr.SaldoIniHaber">
                                    <vs-input @blur="ActualizarC(tr)" v-model="tr.SaldoIniHaber" align="right" />
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>

                </form>
            </div>
        </vs-popup>
        <!---------------Fin Ventana Emergente _---------->

    </div>
</vx-card>
</template>

<script>
import Multiselect from 'vue-multiselect'
import "vue-multiselect/dist/vue-multiselect.min.css"

export default {
    components: {
        Multiselect,

    },
    data() {
        return {
            ListaCuentas: [],
            ListaTipo: [],
            CodigoCuenta: '',
            NombreCuenta: '',
            Folio: '',
            CuentaNueva: '',
            CuentaAnterior: '',
            TieneSubCuentas: false,
            Periodo: 0,
            ActivaF: 'S',
            TituloVentana: '',
            Activa: true,
            Instrucciones: '',
            SaldoInicialDebe: '',
            DeshabilitarControles: false,
            EstadoVentana: false,
            //
            ListaDepartamentos: [],
            cbTipoCuentas: '',
            CodigoTipo: '',
            TipoActividad: '',
            ShowTipoAct: false,
            ShowActividad: false,
            ListaActividad: [],
            DigitosBalance: '',
            NuevoRegistro: false,
            EstadoVentanaI: false,
            cbPeriodos: '',
            ListaPeriodos: [],
            CodigoPeriodo: '',
            DescPeriodo: '',
            FolioF: '',
            CodigoF: '',
            ListaCuentaI: [],
            Debe: '',
            Haber: '',
            Filtro: '',
            NoGuardar: true,
            Estado: ''

        }
    },
    mounted() {
        this.ConsultasCuentas()
    },
    watch: {
        TieneSubCuentas(value) {
            if (value == true) {
                this.ConsultaDigBalance()
            }
        },
        FolioF(value) {
            this.Filtro = value
            this.ConsultaCuentaI()
        }
    },
    methods: {

        NuevaCuenta() {
            this.CodigoCuenta = ''
            this.ListaActividad = []
            this.TituloVentana = "Nueva Cuenta"
            this.TipoNombre = '',
                this.CodigoTipo = '',
                this.cbTipoCuentas = '',
                this.CodigoCuenta = '',
                this.Folio = '',
                this.NombreCuenta = '',
                this.Instrucciones = '',
                this.SaldoInicialDebe = '',
                //Estado de los campos 
                this.Activa = true
            this.SubCuentas = false
            this.EstadoVentana = true
            this.ShowActividad = true
            this.NuevoRegistro = true
            this.DeshabilitarControles = false,
                //Ejecución Metodos
                this.ConsultaTipo()
        },
        VentanaEditar(datos) {
            this.TituloVentana = "Edición Cuenta",
                this.EstadoVentana = true,
                this.CodigoTipo = datos.Tipo,
                this.DeshabilitarControles = true,
                this.CodigoCuenta = datos.CodigoCuenta,
                this.Folio = datos.FolioMayor,
                this.NombreCuenta = datos.Nombre,
                this.Instrucciones = datos.Instrucciones,
                this.SaldoInicialDebe = parseFloat(datos.SaldoIniDebe).toFixed(2)
            this.NoGuardar = false
            this.Activa = datos.Estado
            this.cbTipoCuentas = {
                CodigoTipo: datos.Tipo,
                NombreTipo: datos.NombreTipo
            }
            this.TieneSubCuentas = (datos.TieneSubCuentas == 'S' ? true : false)
            this.TipoActividad = datos.TipoActividad
            this.NuevoRegistro = false
            //Ejecución Metodos
            this.ConsultaTipo()
            this.ConsultaDigBalance(this.TieneSubCuentas)
            if (this.TieneSubCuentas == false) {
                this.ConsultaActividad()
            }
        },
        CerrarVentanaEmergente() {
            this.EstadoVentana = false;
            this.EstadoVentanaI = false
        },
        ConsultaTipo() {
            this.axios.post('/app/v1_contabilidad_general/ConsultasCuentas', {
                    Opcion: 'C',
                    SubOpcion: '2',
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaTipo = resp.data.json;
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido cargar las cuentas, favor validar.',
                        })
                    }
                })
                .catch(() => {})
        },
        onChangeTipoCuentas(value) {
            if (value !== null && value.length !== 0) {
                this.CodigoTipo = value.CodigoTipo;
                this.NombreTipo = value.NombreTipo;
            } else {
                this.CodigoTipo = '';
                this.NombreTipo = '';
            }
        },
        TipoSeleccionado({
            NombreTipo
        }) {
            return `${NombreTipo} `
        },
        ConsultaActividad() {
            this.ListaActividad = []
            this.axios.post('/app/v1_contabilidad_general/ConsultasCuentas', {
                    Opcion: 'C',
                    SubOpcion: '3',
                    CodigoCuenta: this.CodigoCuenta
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaActividad = resp.data.json;
                        this.ShowActividad = true
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido cargar las actividades, favor validar.',
                        })
                    }
                })

                .catch(() => {})
        },
        ValidacionCodigo() {
            if (this.CodigoCuenta != '') {
                this.axios.post('/app/v1_contabilidad_general/Validaciones', {
                        Opcion: 'C',
                        SubOpcion: '8',
                        CodigoCuenta: this.CodigoCuenta
                    })
                    .then(resp => {
                        if (resp.data.codigo !== 0) {
                            this.CodigoCuenta = ''
                            this.NoGuardar = true
                        } else(
                            this.NoGuardar = false
                        )
                    })
                    .catch(() => {
                        this.NoGuardar = true
                    })
            }

        },
        ValidacionFolio() {
            if (this.Folio != '') {
                this.axios.post('/app/v1_contabilidad_general/Validaciones', {
                        Opcion: 'C',
                        SubOpcion: '4',
                        Folio: this.Folio
                    })
                    .then(resp => {
                        if (resp.data.codigo !== 0) {
                            this.CodigoCuenta = ''
                        }
                    })
                    .catch(() => {})
            }
        },
        ConsultasCuentas() {
            this.axios.post('/app/v1_contabilidad_general/ConsultasCuentas', {
                    Opcion: 'C',
                    SubOpcion: '1',
                    Activa: this.ActivaF
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaCuentas = resp.data.json;
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido cargar las cuentas, favor validar.',
                        })

                    }
                })
                .catch(() => {})
        },
        ConsultaDigBalance(valor) {
            this.axios.post('/app/v1_contabilidad_general/ConsultasCuentas', {
                    Opcion: 'C',
                    SubOpcion: '5'
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.DigitosBalance = resp.data.json;
                        if (valor == true) {
                            let digitos = this.CodigoCuenta.length
                            this.DigitosBalance = resp.data.json[0].DigitosBalance
                            if (digitos == resp.data.json[0].DigitosBalance) {
                                this.ShowTipoAct = true

                            } else {
                                this.ShowTipoAct = false
                            }
                        }
                    }

                })
                .catch(() => {})
        },
        BajaCuenta(valor) {
            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Confirmación',
                text: '¿Cambiar estado a Cuenta \'' + valor.CodigoCuenta + '\'? ',
                acceptText: 'Aceptar',
                cancelText: 'Cancelar',
                accept: () => {
                    this.$vs.loading();
                    this.axios.post('/app/v1_contabilidad_general/ActualizacionCuentas', {
                            Opcion: 'U',
                            SubOpcion: '3',
                            CodigoCuenta: valor.CodigoCuenta,
                            Activa: (!valor.Estado ? 'N' : 'S')
                        })
                        .then(resp => {
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Cuentas',
                                    text: resp.data.mensaje,
                                })
                            } else {
                                this.ConsultasCuentas()
                            }
                        })
                        .catch(() => {
                            this.$vs.loading.close();
                        })
                    this.$vs.loading.close();
                },
                cancel: () => {
                    valor.Estado = !valor.Estado
                }
            })
        },
        GuardarRegistro() {
            if (this.CodigoCuenta == '' || this.CodigoCuenta == ' ') {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Cuentas',
                    text: 'Ingresar Código de cuenta',
                })
            } else
            if (this.NombreCuenta == '' || this.NombreCuenta == ' ') {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Cuentas',
                    text: 'Ingresar Nombre de cuenta',
                })
            } else
            if (this.CodigoTipo == '' || this.CodigoTipo == ' ') {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Cuentas',
                    text: 'Ingresar Tipo de Cuenta',
                })
            } else {
                if (this.NuevoRegistro == true) {
                    this.axios.post('/app/v1_contabilidad_general/IngresoCuenta', {
                            Opcion: 'I',
                            SubOpcion: '1',
                            CodigoCuenta: this.CodigoCuenta,
                            Tipo: this.CodigoTipo,
                            TieneSubCuentas: this.TieneSubCuentas == true ? 'S' : 'N',
                            NombreCuenta: this.NombreCuenta,
                            Folio: this.Folio,
                            SaldoInicialDebe: this.SaldoInicialDebe,
                            Activa: this.Activa == true ? 'S' : 'N',
                            TipoActividad: this.TipoActividad,
                            Instrucciones: this.Instrucciones

                        })
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                //this.ListaCuentas = resp.data.json;
                                this.CerrarVentanaEmergente();
                                this.ConsultasCuentas()
                            } else {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Cuentas',
                                    text: 'No se pudo ingresar el registro, favor validar.',
                                })
                            }
                        })
                        .catch(() => {})
                } else {
                    this.axios.post('/app/v1_contabilidad_general/ActualizacionCuentas', {
                            Opcion: 'U',
                            SubOpcion: '1',
                            CodigoCuenta: this.CodigoCuenta,
                            Tipo: this.CodigoTipo,
                            TieneSubCuentas: this.TieneSubCuentas == true ? 'S' : 'N',
                            NombreCuenta: this.NombreCuenta,
                            Folio: this.Folio,
                            SaldoInicialDebe: this.SaldoInicialDebe == 0.00 ? '' : this.SaldoInicialDebe,
                            Activa: this.Activa == true ? 'S' : 'N',
                            TipoActividad: this.TipoActividad,
                            Instrucciones: this.Instrucciones

                        })
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                //this.ListaCuentas = resp.data.json;
                                this.CerrarVentanaEmergente();
                                this.ConsultasCuentas()
                            } else {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Cuentas',
                                    text: 'No se pudo ingresar el registro, favor validar.',
                                })
                            }
                        })
                        .catch(() => {})
                }
            }
        },
        Inicializar() {
            this.EstadoVentanaI = true
            this.ConsultaPeriodo()
            this.ConsultaPeridoActual()
            this.ConsultaCuentaI()
            this.Totalizar()
        },
        ConsultaPeriodo() {
            this.axios.post('/app/v1_contabilidad_general/ConsultaPeriodo', {
                    Opcion: 'C',
                    SubOpcion: '6',
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaPeriodos = resp.data.json;
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido cargar los periodos, favor validar.',
                        })
                    }
                })
                .catch(() => {})
        },
        ConsultaPeridoActual() {
            this.axios.post('/app/v1_contabilidad_general/ConsultaPeriodoActual', {
                    Opcion: 'C',
                    SubOpcion: '2',
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.cbPeriodos = {
                            CodigoPeriodo: resp.data.json[0].CodigoPeriodo,
                            DescPeriodo: resp.data.json[0].Descripcion
                        }

                    } else {
                        this.cbPeriodos = []
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Auxiliar Bancos',
                            text: 'No se ha podido cargar los periodos, favor validar.',
                        })
                    }
                })
        },
        onChangePeriodos(value) {
            if (value !== null && value.length !== 0) {
                this.CodigoPeriodo = value.CodigoPeriodo;
                this.DescPeriodo = value.DescPeriodo;
            } else {
                this.CodigoPeriodo = '';
                this.DescPeriodo = '';
            }
        },
        PeriodoSeleccionado({
            CodigoPeriodo,
            DescPeriodo
        }) {
            return `${CodigoPeriodo} - ${DescPeriodo} `
        },
        ConsultaCuentaI() {
            this.axios.post('/app/v1_contabilidad_general/ConsultaCuentaI', {
                    Opcion: 'C',
                    SubOpcion: '9',
                    Filtro: this.Filtro
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaCuentaI = resp.data.json.map(m => {
                            return {
                                ...m,
                                SaldoIniDebe: parseFloat(m.SaldoIniDebe).toFixed(2),
                                SaldoIniHaber: parseFloat(m.SaldoIniHaber).toFixed(2)
                            }
                        });

                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido cargar las cuentas, favor validar.',
                        })
                    }
                })

        },
        Totalizar() {
            this.axios.post('/app/v1_contabilidad_general/ConsultaCuentaI', {
                    Opcion: 'C',
                    SubOpcion: '10',

                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.Debe = resp.data.json[0].Debe,
                            this.Haber = resp.data.json[0].Haber
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido cargar las cuentas, favor validar.',
                        })
                    }
                })

        },
        ActualizarC(datos) {
            this.axios.post('/app/v1_contabilidad_general/ActualizacionCuentas', {
                    Opcion: 'U',
                    SubOpcion: '2',
                    CodigoCuenta: datos.CodigoCuenta,
                    SaldoInicialDebe: datos.SaldoIniDebe == 0.00 ? '' : datos.SaldoIniDebe,
                    SaldoInicialHaber: datos.SaldoIniHaber == 0.00 ? '' : datos.SaldoIniHaber,

                })
                .catch(() => {})
            this.Totalizar()
        },
        GrabarSaldos() {
            if (this.PeriodoSeleccionado == '' || this.PeriodoSeleccionado == ' ') {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Cuentas',
                    text: 'Selecciones el Período',
                })
            } else {
                this.axios.post('/app/v1_contabilidad_general/GrabarSaldos', {
                        Opcion: 'I',
                        SubOpcion: '2',
                        Periodo: this.cbPeriodos.CodigoPeriodo,
                    })
                    .catch(() => {})
            }

        },

    }
}
</script>   

<style scoped>
.footer {
    height: 70px;
    margin-top: -65px;
    margin-left: 10px;
}

.label-size {
    font-size: 14px;
    font-weight: bold;
}

.label-sizer {
    font-size: 14px;
    font-weight: bold;
    color: red
}

.padre {
    position: relative;
}

.hijo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
</style>
