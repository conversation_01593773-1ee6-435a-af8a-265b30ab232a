<template>
    <vx-card title="Despacho Requerimiento Departamentos">
        <div class="content content-pagex">
            <ValidarPermisoHuella ref="componenteValidarPermiso" :permisoHuella="PermisoRecepcionDepartamento" @getPermisosHuellas="PeticionRecepcion"></ValidarPermisoHuella>
            <vs-divider></vs-divider>
            <br>
            <div class="flex flex-wrap">    
                <div class="sm:w-full md:w-7/12 lg:w-6/12 xl:w-5/12 pr-4">                       
                    <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                        <label class="typo__label">Bodega Fuente:</label>      
                        <strong>{{ cod_bodegaFuente.CODIGO }}</strong>
                        <multiselect
                            v-model="cod_bodegaFuente"
                            :options="Lista_bodegas_fuente"
                            :searchable="true"
                            :close-on-select="true"
                            :show-labels="false"
                            :allow-empty="false"
                            :custom-label="Bodegas_seleccionado"
                            placeholder="Seleccionar bodega"
                            @input="onChangeBodega"
                            :danger="errors.length > 0"
                            :danger-text="errors.length > 0 ? errors[0] : null">
                            <span
                            slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                    </ValidationProvider>                                                
                </div>
                <div class="sm:w-full md:w-4/12 lg:w-3/12 xl:w-2/12 pr-4">
                    <label class="typo__label">Estado</label>
                    <multiselect v-model="cb_lista_operacion" :options="lista_estado" :allow-empty="false" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Operacion_seleccionada" placeholder="Seleccionar" @input="onChangeEstado">
                        <span slot="noOptions">Lista no disponible.</span>
                    </multiselect>
                </div>
            </div>
            <vs-divider></vs-divider>
            <div class="flex flex-wrap">                     
                <vs-row class="w-full">
                    <div class="sm:w-full md:w-7/12 lg:w-6/12 xl:w-5/12 pr-4">                    
                        <label class="typo__label">Departamento Destino:</label>      
                        <multiselect
                            v-model="cod_departamentoDestino_filtro"
                            track-by="CODIGO"
                            :options="Lista_departamento_destino_filtro"
                            :searchable="true"
                            :close-on-select="true"
                            :show-labels="true"
                            :allow-empty="true"
                            :custom-label="Bodegas_seleccionado"
                            placeholder="Seleccionar departamento"
                            selected-label="Seleccionado"                            
                            deselect-label="Quitar selección"
                            select-label="Seleccionar"
                            @input="Consultar_OrdenEnc()">
                            <span slot="noOptions">Lista no disponible.</span>
                        </multiselect>
                    </div>
                    <div class="pr-4">
                        <label class="typo__label">Fecha Inicio:</label>
                        <vs-input type="date" v-model="fecha_inicio" name="date1" />
                    </div>
        
                    <div class="pr-4">
                        <label class="typo__label">Fecha Final:</label>
                        <vs-input type="date" v-model="fecha_final" name="date1" />
                    </div>
        
                    <vs-button style="float:left;margin: 18px 0px" color="primary" type="filled" icon-pack="fas" icon="fa-search" @click="Consultar_OrdenEnc()"> Búsqueda</vs-button>                    
                </vs-row>
    
            </div>
            <vs-divider></vs-divider>
    
            <vs-table2 max-items="10" pagination :data="Lista_vista" search id="tb_lista_solicitud">
    
                <template slot="thead">
                    <th width="130px">Nº. Requerimiento</th>
                    <th>Destino</th>
                    <th>Fecha(s)</th>
                    <th>Observaciones</th>
                    <th></th>
                    <!--<th style="text-align: center;">Recepcionada</th>-->
    
                </template>
    
                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">
    
                        <vs-td2 width='5%'>
                            {{tr.IDCODIGOREQUERIMIENTO}}
                        </vs-td2>
    
                        <vs-td2 width='20%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                {{tr.IDDEPARTAMENTO+' - '+tr.NOMBREDEPARTAMENTO}}
                            </div>
                        </vs-td2>
    
                        <vs-td2 width='20%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                <label>Solicitado: {{tr.FECHASOLICITUD}}</label>
                                <br v-if="tr.FECHASOLICITUD">
                                <label v-if="tr.FECHAENTREGADA">Despachada: {{tr.FECHAENTREGADA}}</label>                                                        
                            </div>
                        </vs-td2>

                        <vs-td2  width='30%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                {{ tr.OBSERVACIONES }}
                            </div>
                        </vs-td2>    
    
                        <vs-td2 v-if="Id_estado_seleccionado == 'P'" width='10%' align="right" style="white-space:nowrap;min-width: 200px;">
                            <vx-tooltip text="Consultar" style="display:inline-block;margin-right:10px">
                                <vs-button color="primary" icon-pack="fas" icon="fa-arrow-right" style="display:inline-block;margin-right:2px" @click="Emergente_Detalle(data[indextr])"></vs-button>
                            </vx-tooltip>                               
                            <vx-tooltip text="Excel" style="display:inline-block;margin-right:10px">
                                <vs-button icon-pack="far" icon="fa-file-excel" @click="Consultar_Movimiento(data[indextr],'EXCEL')" color="success" style="display:inline-block;margin-right:2px"></vs-button>
                            </vx-tooltip>    
                            <vx-tooltip text="Anular" style="display:inline-block;margin-right:2px">
                                <vs-button color="red" icon-pack="fas" icon="fa-times"   @click="Anular_Movimiento(data[indextr])"></vs-button>
                            </vx-tooltip>
                        </vs-td2>
                        <vs-td2 v-else width='10%' align="right" style="white-space:nowrap;min-width: 200px;">
                            <vx-tooltip text="Consultar" style="display:inline-block;margin-right:10px">
                                <vs-button color="#B2BABB" icon-pack="fas" icon="fa-arrow-right" style="display:inline-block;margin-right:2px" @click="Emergente_Consulta(data[indextr])"></vs-button>
                            </vx-tooltip>                            
                            <vx-tooltip text="Excel" style="display:inline-block;margin-right:10px">
                                <vs-button icon-pack="far" icon="fa-file-excel" @click="Consultar_Movimiento(data[indextr],'EXCEL')" color="success" style="display:inline-block;margin-right:2px"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip text="Imprimir" style="display:inline-block;margin-right:10px">
                                <vs-button color="primary" icon-pack="feather" icon="icon-printer" style="display:inline-block;margin-right:2px" @click="Consultar_Movimiento(data[indextr])"></vs-button>
                            </vx-tooltip>
                        </vs-td2>
    
    
                    </tr>
                </template>
            </vs-table2>
    
            <!----------------------- DESPACHO REQUERIMIENTO ---------->
            <vs-popup classContent="popup-example" :title="Descripcion_Emergente" :active.sync="Estado_Emergente">
                <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px"> 
                    
                    <div style="border:2px solid #1B5E20;border-radius:5px;padding:5px;font-size:12px;background-color: #C8E6C9">
                        Sucursal Origen: <b style="font-size:1vw">{{sesion.sesion_sucursal_nombre}}</b>
                    </div>
                    <br>
                    <!--- Ubicación Destino -->
                    <div class="w-1/2 md:w-1/2 lg:w-4/12 xl:w-4/12">
                        <b>No. Requerimiento: </b>                        
                        <b style="font-size:2vw">{{Requerimiento_seleccionado.IDCODIGOREQUERIMIENTO}}</b>                        
                    </div>
                    <div class="w-1/2 md:w-1/2 lg:w-6/12 xl:w-6/12">
                         <b> <small>Fecha Solictud: </small></b>
                        <b>{{Requerimiento_seleccionado.FECHASOLICITUD}}</b>
                    </div>
                    <br>
                    <div>
                        <label class="typo__label">Bodega Fuente:</label>      
                        <strong>{{ cod_bodegaFuente.CODIGO }}</strong>
                        <multiselect v-model="cod_bodegaFuente" :options="Lista_bodegas_fuente" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Bodegas_seleccionado"  placeholder="Seleccionar bodega" :disabled="Deshabilitar_campos">
                            <span slot="noOptions">Lista no disponible.</span>
                        </multiselect> 
                    </div>
                    <div>
                        <br>                                          
                        <label class="typo__label">Destino:</label>
                        <strong>{{ cb_bodegas.CODIGO }}</strong>
                        <multiselect v-model="cb_bodegas" :options="Lista_departamentos_destino" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Bodegas_seleccionado" placeholder="Seleccionar bodega" :disabled="Deshabilitar_campos">
                            <span slot="noOptions">Lista no disponible.</span>
                        </multiselect>
                    </div>
                    <br>
                    <div v-if="Consulta" class="w-full md:w-full lg:w-full xl:w-full">
                        Usuario Recepción:
                        <b>{{Requerimiento_seleccionado.CorporativoDespacho+"   "+Requerimiento_seleccionado.NombreDespacho}}</b>                      
                    </div>
                    <br>
                    <!--- Producto -->
                    <vs-divider>Detalle</vs-divider>
    
                    <!--- Mostrar detalle de la orden--->
                    <vs-table2 max-items="10" pagination :data="Lista_detalle" id="tb_lista_solicitud">
    
                        <template slot="thead">
                            <th>Linea</th>
                            <th>Codigo</th>
                            <th>Producto</th>
                            <th>Unidad Medida</th>
                            <th>Cant. Solicitada</th>
                            <th>Cant. Despachada</th>
                            <th></th>
                        </template>
    
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 width='5%'>
                                    {{ indextr+1 }}
                                </vs-td2>
                                <vs-td2 width='5%'>                                    
                                    {{tr.IDPRODUCTOFK}}                                                    
                                </vs-td2>
                                <vs-td2 width='45%'>
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                         
                                        {{tr.DESCRIPCION_PRODUCTO}}                
                                    </div>
                                </vs-td2>
                                <vs-td2 width='10%'>
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                                          
                                        {{tr.DESCRIPCION_UNIDAD}}
                                    </div>
                                </vs-td2>
                                <vs-td2 width='5%'>
                                    {{tr.CANTIDAD_PEDIDA}}
                                </vs-td2>
                                <vs-td2 width='15%' >
                                    <ValidationProvider name="Existencias" :rules="'required|min:0|max:'+tr.CANTIDAD_PEDIDA" v-slot="{ errors }" class="required">
                                        <vs-input  type="number"  v-model="tr.CANTIDAD_ENTREGA" class="w-full" :disabled="!tr.ESTADO_SWITCH" @input="VerificarExistencias(tr)" 
                                                   :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"/>                                    
                                    </ValidationProvider>
                                    <!--- Semaforo Existencias, máximos y mínimos--->
                                    <div v-if="!Consulta">
                                        <label v-if="parseFloat(tr.INV_DISPONIBLE-tr.CANTIDAD_ENTREGA) > parseFloat(tr.MINIMO_DESPACHANTE)" style="word-wrap: break-word;white-space: wrap; width:100px; border-radius: 3px;  background-color:#69F0AE;text-align: center;color:black;font-size:15px; "> Disponible: {{tr.INV_DISPONIBLE-tr.CANTIDAD_ENTREGA}} </label>
                                        <label v-else-if="(parseFloat(tr.INV_DISPONIBLE-tr.CANTIDAD_ENTREGA) >0) && (parseFloat(tr.INV_DISPONIBLE-tr.CANTIDAD_ENTREGA) <= parseFloat(tr.MINIMO_DESPACHANTE))" style="word-wrap: break-word;white-space: wrap; width:100px; border-radius: 3px;  background-color:#FFFF00;text-align: center;color:black;font-size:15px; "> Disponible: {{tr.INV_DISPONIBLE-tr.CANTIDAD_ENTREGA}} </label>
                                        <label v-else-if="parseFloat(tr.INV_DISPONIBLE-tr.CANTIDAD_ENTREGA)<= 0" style="word-wrap: break-word;white-space: wrap; width:100px; border-radius: 3px;  background-color:#FF5252;text-align: center;color:white;font-size:15px; "> Disponible: {{tr.INV_DISPONIBLE-tr.CANTIDAD_ENTREGA}} </label>
                                    </div>
                                   
                                </vs-td2>
                                <vs-td2 width='15%' v-if="!Consulta" >
                                    <div style="display:inline-block;margin-top:10px;margin-left:1px;margin-right:20px;">
                                        <vs-switch v-model="tr.ESTADO_SWITCH" />
                                    </div>                                    
                                    <vx-tooltip text="Eliminar" style="display:inline-block;">
                                        <vs-button color="danger"  style="margin-right: 10px;" icon-pack="fas" icon="fa-trash" @click="Eliminar_Registro(tr)"></vs-button>
                                    </vx-tooltip>
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
                    <vs-divider></vs-divider>
                    <div class="flex flex-wrap">
                        <div class="w-full md:w-full lg:w-full xl:w-full">
                            <vs-textarea label="Observaciones" counter="100" type="text" class="w-full" v-model="Observaciones" :disabled="Consulta" />
                        </div>
    
                    </div>
                    <vs-divider></vs-divider>
                    <vs-button v-if="!Consulta" color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" id="btn_confirmacion" @click="Finalizar_Despacho()"> Finalizar Despacho</vs-button>
                    <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-printer" id="btn_movimiento" @click="Consultar_Movimiento(Requerimiento_seleccionado)"> Imprimir </vs-button>                
                    <vs-button icon-pack="far" style="float:right;margin: 5px" icon="fa-file-excel" @click="Consultar_Movimiento(Requerimiento_seleccionado,'EXCEL')" color="success">Excel</vs-button>
                    <vs-divider></vs-divider>
                </div>
            </vs-popup>
    
        </div>
    </vx-card>
    </template>
    
    <script>
    import Multiselect from 'vue-multiselect'
    import moment from "moment"
    import "vue-multiselect/dist/vue-multiselect.min.css"
    import ValidarPermisoHuella from "/src/components/validador-huella/ValidarPermisoHuella.vue"    
    
    export default {
        components: {
            Multiselect,
            ValidarPermisoHuella
        },
        data() {
            return {
                Estado_VentanaEmergente_Busqueda: false,
                cb_lista_operacion: '',
                Descripcion_Emergente: '',
                PermisoRecepcionDepartamento:'',
                producto_buscar: '',
                lista_estado: [{
                        ID: 'P',
                        DESCRIPCION: 'No Despachado'
                    },
                    {
                        ID: 'T,N',
                        DESCRIPCION: 'Despachado,Anulado'
                    }
                ],
                Id_estado_seleccionado: 0,
                fecha_inicio: '',
                fecha_final: '',
                Lista_vista: [],
                Estado_Emergente: false,
                Consulta: false,
                permisos_tipos_bodegas: [],
                permiso_bodega_vencido:'',
                cod_departamentoDestino_filtro: '',
                Lista_bodegas_fuente: [],
                Lista_departamentos_destino: [],
                Lista_departamento_destino_filtro: [],
                id_bodega_seleccionada: '',
                cb_bodegas: '',
    
                Producto_seleccionado: {
                    nombre: '',
                    marca_comercial: '',
                    Principio_activo: '',
                    Concentracion: '',
                    Presentacion: '',
                    Codigo: '',
                    codigo_interno: '',
                    Activo: 'N'
                },
                IdCodigoRequerimiento: 0,
                Cantida_solicitar: '',
    
                Lista_detalle: [],
                Deshabilitar_campos: false,
                producto: [],
    
                Requerimiento_seleccionado: [],
                Observaciones: '',
    
                user: '',
                message: 'prueba mensaje',
                messages: [],
                notificaciones: [],
                cod_bodegaFuente:'',
                listado_reportes: [],
                listado_source: {
                    CODIGO_SOLICITUD: '',
                    CODIGO_MOVIMIENTO: '',
                    CODIGO_REQUERIMIENTO: '',
                }             
            }
        },
        mounted() {
            this.cb_lista_operacion = {
                ID: 'P',
                DESCRIPCION: 'No Despachado'
            }            
            this.Id_estado_seleccionado = 'P';

            for(let privilegio of this.$store.state.privilegios){
               if(privilegio.Privilegio.includes("TIPO_REQUERIMIENTO")){                      
                   let tipobodega = privilegio.Privilegio.split('_')[2];
                   if(tipobodega == 'T'){
                     this.permiso_bodega_vencido = 'S'
                   }else{
                     this.permisos_tipos_bodegas.push(privilegio.Privilegio.split('_')[2])                    
                   }                                                       
                }
            }      
            this.Consultar_Bodega('L',this.permisos_tipos_bodegas.join(","),this.permiso_bodega_vencido);
            this.Consultar_Departamentos(this.permisos_tipos_bodegas.join(","))
        },
        computed: {
            sesion() {
                return this.$store.state.sesion
            }
        },
        async beforeCreate(){
            this.listado_reportes = await this.$recupera_parametros_reporte('Movimiento Requerimiento')                        
        },
        methods: {
            async Consultar_Movimiento(datos,formato='PDF') {                
                this.listado_source.CODIGO_REQUERIMIENTO = datos.IDCODIGOREQUERIMIENTO
                this.$reporte_modal({
                    Nombre: "Movimiento Requerimiento",
                    Opciones: this.listado_source,
                    Formato: formato
                }).catch(() => {
                })
            },
            Consultar_Departamentos(ListaTipoBodegas) {
                const url = this.$store.state.global.url
                this.axios.post(url+'app/v1_OrdenCompra/ConsultasRequerimientos', {
                                                                        Opcion: 'D',
                                                                        SubOpcion: 'T',
                                                                        ListaTipoBodegas: ListaTipoBodegas
                                                                     })
                           .then(resp => {
            
                                if (resp.data.codigo != 0) {
                                    this.$vs.notify({
                                        color: '#B71C1C',
                                        title: 'Departamentos',
                                        text: resp.data.mensaje,
                                        position: 'top-center'
                                    })
                                } else {
                                    this.Lista_departamento_destino_filtro = resp.data.json;                         
                                }
                            })
                           .catch(() => { })
        
            },
            GetDateValue(value) {
                if (value === undefined) {
                    return '';
                } else if (value === null) {
                    return '';
                } else if (value == '') {
                    return '';
                }
                return moment(value).format('DD/MM/YYYY');          
            },
            Eliminar_Registro(value) {
                /**
                 * @General
                 * Función eliminar registro;
                 */
                
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',                    
                    acceptText: 'Aceptar',
                    cancelText: 'Cancelar',
                    text: '¿Dar de baja Pedido  \'' + value.DESCRIPCION_PRODUCTO + '\'   \'' + value.DESCRIPCION_UNIDAD + '\' ? ',
                    accept: () => {
                        this.axios.post('/app/v1_OrdenCompra/ManejoRequerimientos', {
                                CodProducto: value.IDPRODUCTOFK,
                                NoRequerimiento: this.IdCodigoRequerimiento,
                                TipoUpdate: 19
                            })
                            .then(resp => {
    
                                if (resp.data.tipo_error >= 0) {
                                    let indice = this.Lista_detalle.findIndex(d=>d.IDPRODUCTOFK == value.IDPRODUCTOFK)
                                    this.Lista_detalle.splice(indice,1);
                                }
                            })
                        
                    }
                })
    
            },
            VerificarExistencias(producto){
                if(Number(producto.CANTIDAD_ENTREGA) > Number(producto.INV_DISPONIBLE)){
                    this.$vs.notify({
                            position:'top-center',
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'No hay suficientes existencias',
                        });
                    producto.CANTIDAD_ENTREGA = 0
                }
                
                if(Number(producto.CANTIDAD_ENTREGA) > Number(producto.CANTIDAD_PEDIDA)){
                    this.$vs.notify({
                            position:'top-center',
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'No puede despachar mas que la cantidad solicitada',
                        });
                    producto.CANTIDAD_ENTREGA = 0
                }
            },
            Operacion_seleccionada({
                DESCRIPCION
            }) {
                return `${DESCRIPCION} `
            },
            onChangeEstado(value) {
                if (value !== null && value.length !== 0) {
                    this.Id_estado_seleccionado = value.ID;
                    this.Consultar_OrdenEnc();
                } else {
                    this.Id_estado_seleccionado = '';
                }
            },
            Bodegas_seleccionado({
                CODIGO,NOMBRE
            }) {
                return `${CODIGO} - ${NOMBRE} `;
            },
            onChangeBodega(value) {
                if(!value){
                    this.cod_bodegaFuente=''
                    this.id_bodega_seleccionada=''
                    return
                }
                if (value !== null && value.length !== 0) {
                    this.id_bodega_seleccionada = value.CODIGO;
                    this.Consultar_OrdenEnc();  
                } else {
                    this.id_bodega_seleccionada = '';
                    this.cod_bodegaFuente = '';
                }
            },
            Anular_Movimiento(Datos){
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación Anulación',
                    acceptText: 'Anular',
                    cancelText: 'Cancelar',
                    text: `¿Desea anular el requerimiento No. ${Datos.IDCODIGOREQUERIMIENTO}?`,
                    accept: () => {
                        return this.axios.post('/app/v1_OrdenCompra/ManejoRequerimientos', {
                            NoRequerimiento: Datos.IDCODIGOREQUERIMIENTO,
                            TipoUpdate: 14
                        }).then(resp =>{
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    position:'top-center',
                                    color: '#B71C1C',
                                    title: 'Alerta',
                                    text: resp.data.mensaje,
                                })
                            } else {
                                this.$vs.notify({
                                    position:'top-center',
                                    color: 'sucess',
                                    title: 'Anulación',
                                    text: resp.data.mensaje
                                })    
                                this.Consultar_OrdenEnc();
                            }
                        })
                    }
                })
            },
            Emergente_Consulta(Datos) {
                this.Estado_Emergente = true;
                this.Consulta = true;
                this.cb_bodegas = '';    
                this.producto_buscar = '';
                this.Cantida_solicitar = '';

                this.Requerimiento_seleccionado = Datos;

                this.IdCodigoRequerimiento = Datos.IDCODIGOREQUERIMIENTO;
                this.id_bodega_seleccionada = Datos.IdBodegaFuentefk;
                setTimeout(() => {
                    this.cb_bodegas = {
                        NOMBRE: Datos.NOMBREDEPARTAMENTO,
                        CODIGO: Datos.IDDEPARTAMENTO
                    }
                }, 500);
                this.Observaciones = Datos.OBSERVACIONES;
                this.Deshabilitar_campos = true;
                this.Descripcion_Emergente = 'Consulta Despacho Requerimiento';
                this.Consultar_OrdenDetalle()
            },
            Emergente_Detalle(Datos) {
                this.Estado_Emergente = true;
                this.Consulta = false;
                this.cb_bodegas = '';
    
                this.producto_buscar = '';
                this.Cantida_solicitar = '';
 
                this.Requerimiento_seleccionado = Datos;

                this.IdCodigoRequerimiento = Datos.IDCODIGOREQUERIMIENTO;
                this.id_bodega_seleccionada = Datos.IdBodegaFuentefk;
                setTimeout(() => {
                    this.cb_bodegas = {
                        NOMBRE: Datos.NOMBREDEPARTAMENTO,
                        CODIGO: Datos.IDDEPARTAMENTO
                    }
                }, 500);
                this.Observaciones = Datos.OBSERVACIONES;
                this.Deshabilitar_campos = true;
                this.Descripcion_Emergente = 'Despacho Requerimiento';
                this.Lista_detalle = [];
                this.PermisoRecepcionDepartamento = 'RECEPCION_DEPARTAMENTO_'+Datos.IDDEPARTAMENTO
                this.Consultar_OrdenDet()
                
            },
    
            Validacion_Campos(Tipo, Nombre, valor, obligatorio, cant_maxima) {
                /**
                 * @General
                 * Tipo : N = Númerio, Nombre = Descripción del campo, Valor = A evaluar, Obligatorio= Verdadero o falso,cant_maxima  = Aplica unicamente para un string y define la cantidad de caracteres 
                 * Devuelve un mensaje al usuario si las validaciones no se cumple y aborta, de lo contrario devuelve verdadero
                 */
                if (Tipo == 'ID') {
                    if (valor <= 0) {
                        this.$vs.notify({
                            position:'top-center',
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else if (Tipo == 'N') {
                    
                    if ( valor <0 || valor == "") {
                        this.$vs.notify({
                            position:'top-center',
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else {
                    if (valor == '' && obligatorio == true) {
                        this.$vs.notify({
                            position:'top-center',
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
    
                        if (cant_maxima < valor.length) {
                            this.$vs.notify({
                            position:'top-center',
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'Campo ' + Nombre + ' no debe superar los ' + cant_maxima + ' carácteres.',
                            });
                            return false;
                        } else {
                            return true;
                        }
    
                    }
                }
            },
            /*****************************CONSULTAR INFORMACION*************/
            Consultar_OrdenEnc() {
                if(this.id_bodega_seleccionada == ''){
                    this.$vs.notify({
                            position:'top-center',
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: 'Debe seleccionar la bodega fuente',
                                time: 4000
                            })
                    this.Lista_vista = [];
                    return;
                }
                if(!this.Id_estado_seleccionado || this.Id_estado_seleccionado == ''){
                    this.$vs.notify({
                            position:'top-center',
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: 'Debe seleccionar un Estado',
                                time: 4000
                            })
                    this.Lista_vista = [];
                    return;
                }
                
                this.axios.post('/app/v1_OrdenCompra/ConsultasRequerimientos', {                        
                        Estado: this.Id_estado_seleccionado,                        
                        FechaInicio: this.GetDateValue(this.fecha_inicio),
                        FechaFin: this.GetDateValue(this.fecha_final),
                        Opcion: 'C',
                        SubOpcion: 'A',
                        BodegaFuente: this.id_bodega_seleccionada,
                        DepartamentoDestino: this.cod_departamentoDestino_filtro?.CODIGO??null
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                            position:'top-center',
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_vista = [];
                        } else {
                            this.$vs.notify({
                                                position:'top-center',
                                                color: 'success',
                                                title: 'Inventario',
                                                text: 'Se actualizaron los requerimientos exitosamente',
                                            })
                            this.Lista_vista = resp.data.json;
    
                        }
                    })
                    .catch(() => {})

            },
            Consultar_OrdenDetalle() {
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/ConsultasRequerimientos', {
                        Opcion: 'C',
                        SubOpcion: 'B',
                        IdRequerimiento: this.IdCodigoRequerimiento
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {                            
                            this.Lista_detalle = [];
                        } else {
                            this.Lista_detalle = resp.data.json;
                        }
                    })
                    .catch(() => {})
            },
            Consultar_OrdenPrueba(){
                let arrayPrueba = this.Lista_detalle.map(function(producto){
                    producto.CANTIDAD_ENTREGA=10})
                this.Lista_detalle = arrayPrueba;
            },
            Consultar_OrdenDet() {
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/ConsultasRequerimientos', {
                        Opcion: 'C',
                        SubOpcion: 'B',
                        IdRequerimiento: this.IdCodigoRequerimiento
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {                   
                            this.Lista_detalle = [];
                        } else {
                            if(this.Lista_detalle.length > 0){
                                this.Lista_detalle = resp.data.json.map((producto) => {  let productoAnterior = this.Lista_detalle.find((prdAntiguo)=>Number(prdAntiguo.IDPRODUCTOFK)==Number(producto.IDPRODUCTOFK))
                                    
                                                                                             if(productoAnterior){
                                                                                                producto.CANTIDAD_ENTREGA = productoAnterior.CANTIDAD_ENTREGA
                                                                                                producto.ESTADO_SWITCH = productoAnterior.ESTADO_SWITCH
                                                                                             }
                                                                                             else if(Number(producto.CANTIDAD_PEDIDA)<=Number(producto.INV_DISPONIBLE)){
                                                                                                producto.CANTIDAD_ENTREGA = producto.CANTIDAD_PEDIDA
                                                                                             }
                                                                                                return producto;
                                                                                            });
                            }else{
                                this.Lista_detalle = resp.data.json.map(function(producto){ 
                                                                                        if(Number(producto.CANTIDAD_PEDIDA)<=Number(producto.INV_DISPONIBLE)){
                                                                                            producto.CANTIDAD_ENTREGA = producto.CANTIDAD_PEDIDA
                                                                                        }else{
                                                                                            producto.CANTIDAD_ENTREGA = 0
                                                                                        }
                                                                                        
                                                                                        return producto;
                                                                                     });
                            }
                            
    
                        }
                    })
                    .catch(() => {})
            },
            Consultar_Bodega(Operacion, ListaTipoBodegas) {
                const url = this.$store.state.global.url
                this.axios.post(url+'app/v1_OrdenCompra/consulta_bodega', {
                                                                        operacion: Operacion,
                                                                        lista_tipo_bodegas: ListaTipoBodegas
                                                                     })
                           .then(resp => {
            
                                if (resp.data.codigo != 0) {
                                    this.$vs.notify({
                            position:'top-center',
                                        color: '#B71C1C',
                                        title: 'Bodegas',
                                        text: resp.data.mensaje,
                                    })
                                } else {
                                        this.Lista_bodegas_fuente = resp.data.json;   
                                      //  this.Lista_departamentos_destino= resp.data.json;                         
                                }
                            })
                           .catch(() => { })
        
            },            
    
            /*************************** ACTUALIZAR, ELIMINAR NUEVO REGISTRO */
            
            Registrar_DespachoDet(datos, Operacion) {
    
                //Operacion = 'E' -> Se va despachar producto.
                //Operacion = 'A' -> Se va despachar producto.
                var res_variables = false;
    
                
    
                res_variables = this.Validacion_Campos('N', 'Cantidad Despachar', datos.CANTIDAD_ENTREGA, true, 100000000);
    
                if (res_variables) {
                    this.axios.post('/app/v1_OrdenCompra/Requisicion_Det', {
                            Requisicion_det: datos.IDREQUISICIONDET,
                            Requisicion_enc: datos.IDREQUISICIONENCFK,
                            Empresa: this.sesion.sesion_empresa,
                            Producto: datos.IDPRODUCTOFK,
                            cant_pedida: 0,
                            cant_entregada: datos.CANTIDAD_ENTREGA,
                            cant_recibida: 0,
                            corporativo: this.sesion.corporativo,
                            Operacion: Operacion,
                        })
                        .then(resp => {
    
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    position:'top-center',
                                    color: '#B71C1C',
                                    title: 'Alerta',
                                    text: resp.data.mensaje,
                                })
                            }
                            this.Consultar_OrdenDet();
                        })
                }
            },
            PeticionRecepcion(respuestaHuella){
                
                if(respuestaHuella.statusValidacion == 'NA'){
                    this.$vs.notify({
                        title:'Alerta',
                        color: 'danger',
                        position:'top-center',
                        time:6000,
                        text:'No se registro una huella valida'
                    })
                    return;
                }

                if(respuestaHuella.statusValidacion == 'N'){
                    this.$vs.notify({
                        title:'Alerta',
                        color: 'danger',
                        position:'top-center',
                        time:6000,
                        text:'No cuenta con permiso para recibir pedidos de este departamento'
                    })
                    return;
                }


                this.axios.post('/app/v1_OrdenCompra/ManejoRequerimientos', {
                            NoRequerimiento: this.Requerimiento_seleccionado.IDCODIGOREQUERIMIENTO,
                            ListaProductos: this.Lista_detalle.map((producto)=>producto.IDPRODUCTOFK).toString(),
                            CantidadesDespacho: this.Lista_detalle.map((producto)=>producto.CANTIDAD_ENTREGA).toString(),
                            TipoUpdate: 12,
                            Descripcion: this.Observaciones,
                            CorporativoRecepcion: respuestaHuella.Corporativo
                        })
                        .then(resp => {
                            
                            if (resp.data.json[0].codigo == 0) {
                                this.Estado_Emergente = false;
                                this.$vs.notify({
                                    position:'top-center',
                                    color: 'success',
                                    time: 6000,
                                    title: 'Despacho Requerimiento',                                    
                                    text: resp.data.json[0].descripcion.split(';')[0]
                                })
                                var mensajeMontoLimite = resp.data.json[0].descripcion.split(';')[1]
                                if(mensajeMontoLimite.slice(0,1) == "1"){
                                    this.$vs.notify({
                                        position:'top-center',
                                        color: 'warning',
                                        time: 6000,
                                        title: 'Monto Limite Bodega',                                    
                                        text: mensajeMontoLimite.slice(1)
                                    })
                                }else{
                                    this.$vs.dialog({
                                                type: 'confirm',
                                                color: 'success',
                                                title: 'Monto Limite Bodega',
                                                acceptText: 'Aceptar',
                                                cancelText: 'Cancelar',
                                                text: resp.data.json[0].descripcion.split(';')[1],                                               
                                                })
                                }
                                this.Consultar_OrdenEnc();    
                            }else{
                                this.$vs.notify({
                                    position:'top-center',
                                    color: 'danger',
                                    time: 6000,
                                    title: 'Despacho Requerimiento',                                    
                                    text: resp.data.json[0].descripcion
                                })
                            }                          
                        }).catch(() => { })                                                  
            },
            Finalizar_Despacho() {
               // 
               // 
                
               //this.Consultar_OrdenPrueba();
                
               /* VALIDACION DE ARRAY */
                
               //
                let res_variables = this.Validacion_Campos('S', 'Observaciones', this.Observaciones, true, 100);
                if(!res_variables)return;

                for (var i = 0; i < this.Lista_detalle.length; i++) {
                    if(this.Lista_detalle[i].CANTIDAD_ENTREGA < this.Lista_detalle[i].CANTIDAD_PEDIDA  ){

                        
                        this.Lista_detalle[i].CANTIDAD_ENTREGA < this.Lista_detalle[i].CANTIDAD_PEDIDA
                        var slug = this.Lista_detalle[i].ESTADO_SWITCH;
                        
                        if (slug == false) {
                            this.$vs.notify({
                            position:'top-center',
                                color: '#B71C1C',
                                title: 'Requerimientos',
                                text: 'Debe realizar el despacho manual del producto en la linea: ' + (i+1),
                                time: 6000
                            });
                            return false;
                        }
                    }
                        
                }
    
                this.$refs.componenteValidarPermiso.popUpPermisosHuella = true                                
            }
        }
    
    }
    </script>    