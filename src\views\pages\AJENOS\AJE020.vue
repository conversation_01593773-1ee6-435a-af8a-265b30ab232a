<template>
    <vx-card title="Consulta Admisiones Estado">

        <div class="admmisionesEstado">

            <div class="seleccionAdmision">

                <div class="radio-button-group">
                    <vs-col>
                        <label class="radio blue" style="width: 100%; text-align: center; vertical-align: middle;">
                            <input type="radio" v-model="TipoAdmision" name="group1" value="Interna"
                                @change="ConsultarAdmisionesEstado().Consultar()" />
                            <span>Interna</span>
                        </label>
                        <label class="radio blue" style="width: 100%; text-align: center; vertical-align: middle;">
                            <input type="radio" v-model="TipoAdmision" name="group1" value="Urgencias"
                                @change="ConsultarAdmisionesEstado().Consultar()" />
                            <span>Emergencia</span>
                        </label>

                        <label class="radio blue" style="width: 100%; text-align: center; vertical-align: middle;">
                            <input type="radio" v-model="TipoAdmision" name="group1" value="Externa"
                                @change="ConsultarAdmisionesEstado().Consultar()" />
                            <span>Externa</span>
                        </label>
                    </vs-col>

                </div>

            </div>

            <div class="listaAdmisiones">
                <div style="overflow:scroll;height:100%;width: 100%;overflow:auto">


                    <vs-table2 max-items="10" search tooltip pagination :data="lista_admisiones">
                        <template slot="thead">
                            <th width="150px" filtro="TipoAdmision">Tipo Admisión</th>
                            <th width="200px" filtro="CodigoAdmision">Admisión</th>
                            <th width="400px" filtro="NombrePaciente">Nombre Paciente</th>
                            <th width="200px" filtro="IngresoPaciente">Fecha Ingreso</th>
                            <th width="100px" filtro="EstadoProceso">Estado</th>
                            <th width="100px" filtro="Actividad">Actividad</th>
                        </template>

                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data" :ref="`list_${indextr}`">

                                <vs-td2>
                                    {{ tr.TipoAdmision }}
                                </vs-td2>

                                <vs-td2>
                                    {{ tr.CodigoAdmision }}
                                </vs-td2>

                                <vs-td2>
                                    {{ tr.NombrePaciente }}
                                </vs-td2>

                                <vs-td2>
                                    {{  tr.IngresoPaciente }}
                                </vs-td2>

                                <vs-td2>
                                    {{ tr.EstadoProceso }}
                                </vs-td2>

                                <vs-td2>
                                    {{ tr.Actividad }}
                                </vs-td2>

                            </tr>
                        </template>
                    </vs-table2>
                </div>

            </div>

        </div>


    </vx-card>
</template>



<script>


export default {
    data: () => ({
        lista_admisiones: '',
        TipoAdmision: ''

    }),
    methods: {
        ConsultarAdmisionesEstado() {
            return {

                Consultar: async () => {
                    
                    const resp = await this.axios.post('/app/v1_admision/ConsultarAdmisionesEstado', {
                        Empresa: '',
                        EmpresaReal: '',
                        Hospital: '',
                        TipoAdmision: this.TipoAdmision
                    })

                    
                    this.lista_admisiones = resp.data.json.map(m => {
                        return {
                            ...m,

                        }
                    })
                    
                },

            }
        },
        submit() {
            // this.$router.push('/ADMISION/ADM018')
        },
        obtenerTipoAdmision() {

        }

    }
}

</script>


<style scoped>
* {
    box-sizing: border-box;
    padding: 2px;
    margin: 0;
}

.admmisionesEstado {
    display: grid;
    grid-gap: 10px;
    width: 100%;
    height: 100%;
    grid-template-areas: "seleccionAdmision"
        "listaAdmisiones";
    grid-template-columns: 100%;
    grid-template-rows: 15% 85%;
}

.admmisionesEstado>div {
    border: 1px solid #888;

}

.seleccionAdmision {
    grid-area: seleccionAdmision;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    padding-left: 1rem;
    padding-right: 1rem;
    border-radius: 5px;
    margin: 5px;
}

.listaAdmisiones {
    grid-area: listaAdmisiones;
    border-radius: 5px;
}

label {
    margin-right: 40px;
    margin-left: 40px;
    vertical-align: middle;

}

input[type="radio"] {
    margin-right: 10px;
    margin-left: 5px;
}

.radio-button-group {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}


</style>


