<template>
<vs-row id="cateteres">
    <vs-col vs-w="2" vs-justify="center" vs-align="center" class="pl-2 pt-2">
        <vx-card title="Formularios ingresados">
            <DxDataGrid :ref="dataGridFechaKey" v-bind="DefaultDxGridConfiguration" :headerFilter="{visible:true}" :searchPanel="{visible: false}" :data-source="formulariosCateteres" :width="'100%'" :height="'100%'" @selection-changed="onSelectionChangedFecha">
                <DxSelection mode="single" />

                <DxColumn :width="100" data-field="FechaRegistro" caption="Fecha" format="dd/MM/yyyy HH:mm" data-type="date" alignment="center" />
            </DxDataGrid>
        </vx-card>
    </vs-col>
    <vs-col vs-w="10" vs-justify="center" vs-align="center" class="pl-2 pr-2 pt-2">
        <vx-card>
            <form @submit="handleSubmit">
                <DxForm :form-data.sync="formulario" labelMode="floating" :ref="dataEncabezadoRefKey">
                    <DxGroupItem item-type="group">
                        <DxButtonItem :col-span="3" :button-options="limpiarButtonOptions" horizontal-alignment="center" verical-alignment="center" />
                        <DxButtonItem :button-options="pdfButtonOptions" horizontal-alignment="center" verical-alignment="center" :visible="formulario.IdCateter !== null" />
                    </DxGroupItem>
                    <!-- INSERCIÓN DE CATÉTERES -->
                    <DxGroupItem :col-count-by-screen="colCountByScreenSelect" item-type="group" caption="Inserción catéter">
                        <DxItem data-field="NombreCompletoOperador" editor-type="dxTextBox" :editor-options="{ readOnly: true }">
                            <DxLabel :text="'Nombres y apellidos del operador'" />
                        </DxItem>
                        <DxItem data-field="Especialidad" editor-type="dxTextBox" :editor-options="{ readOnly: true }">
                            <DxLabel :text="'Especialidad'" />
                        </DxItem>
                        <DxItem data-field="Colegiado" editor-type="dxTextBox" :editor-options="{ readOnly: true }">
                            <DxLabel :text="'Colegiado No'" />
                        </DxItem>
                        <DxGroupItem caption="Fecha y lugar" item-type="group">
                            <DxItem data-field="FechaInsercion" editor-type="dxDateBox" :use-mask-behavior="true" :col-span="1" :is-required="true" :editor-options="{...dateBoxOptions, readOnly: formularioReadOnly.FechaInsercion }">
                                <DxLabel :text="'Fecha de inserción'" />
                            </DxItem>
                            <DxItem data-field="HospitalColoco" editor-type="dxSelectBox" :col-span="1" :editor-options="{ dataSource: hospitales, displayExpr: 'Nombre', valueExpr: 'CodigoBase', readOnly: formularioReadOnly.HospitalInsercion }">
                                <DxLabel :text="'Hospital donde se colocó'" />
                            </DxItem>
                        </DxGroupItem>
                        <DxGroupItem caption="Servicio donde se colocó">
                            <DxItem data-field="Servicio" :is-required="true" editor-type="dxSelectBox" :editor-options="{ dataSource: servicios, displayExpr:'text', valueExpr: 'id', readOnly: formularioReadOnly.Servicio }" />
                            <DxItem data-field="ServicioOtro" editor-type="dxTextBox" :is-required="servicioSeleccionado === -1" :editor-options="{ maxLength: 100, readOnly: (formularioReadOnly.Servicio || servicioSeleccionado !== -1) }">
                                <DxLabel :text="'Otro'" />
                            </DxItem>
                        </DxGroupItem>
                        <DxGroupItem item-type="group" caption="Sitio de inserción">
                            <DxItem data-field="Sitio" editor-type="dxSelectBox" :editor-options="{ dataSource: sitios, displayExpr:'text', valueExpr: 'id', readOnly: formularioReadOnly.Sitio }" />
                            <DxItem data-field="DeIz" editor-type="dxRadioGroup" :is-required="sitioSeleccionado >= 5 && sitioSeleccionado <= 17" :visible="sitioSeleccionado >= 5 && sitioSeleccionado <= 17" :editor-options="{
                                                    items:[{id:1, text:'Derecho'},{id:2,text:'Izquierdo'}],                                                
                                                    displayExpr:'text',
                                                    valueExpr: 'id',
                                                    layout:'horizontal',
                                                    readOnly: (formularioReadOnly.Sitio || sitioSeleccionado >= 20 || sitioSeleccionado === -1 || sitioSeleccionado === '')
                                                }">
                                <DxLabel :visible="false" />
                            </DxItem>
                            <DxItem data-field="SitioOtro" editor-type="dxTextBox" :is-required="sitioSeleccionado === -1" :editor-options="{ maxLength: 100, readOnly: (formularioReadOnly.Sitio || sitioSeleccionado !== -1) }">
                                <DxLabel :text="'Otro'" />
                            </DxItem>
                            <DxItem data-field="IntentosInsercion" editor-type="dxNumberBox" :editor-options="{ maxLength: 10, min: 1, readOnly: formularioReadOnly.Intentos, step: 0  }">
                                <DxLabel :text="'Número de intentos en la inserción:'" />
                            </DxItem>
                        </DxGroupItem>
                        <DxGroupItem :col-span="3" item-type="group" caption="Indicación(es) para su uso">
                            <DxGroupItem :col-count-by-screen="colCountByScreenSelect">
                                <DxItem v-for="(item, index) in indicacionesUso" v-bind:key="index" :data-field="item.name" editor-type="dxCheckBox" css-class="check-box" :editor-options="{ readOnly: formularioReadOnly.IndicacionesUso }">
                                    <DxLabel :text="item.text" />
                                </DxItem>
                            </DxGroupItem>
                            <DxGroupItem :col-count-by-screen="colCountByScreenSelect">
                                <DxItem data-field="IndicacionesOtros" editor-type="dxTextBox" :editor-options="{ maxLength: 100, readOnly: formularioReadOnly.IndicacionesUso }">
                                    <DxLabel :text="'Otro'" />
                                </DxItem>
                            </DxGroupItem>
                        </DxGroupItem>
                        <DxGroupItem :col-count-by-screen="colCountByScreenSelect" :col-span="3" item-type="group" caption="Características del catéter">
                            <DxGroupItem item-type="group">
                                <DxItem data-field="Marca" editor-type="dxTextBox" :is-required="true" :editor-options="{ maxLength: 100, readOnly: formularioReadOnly.Marca }" />
                                <DxItem data-field="NumeroSerie" editor-type="dxTextBox" :is-required="true" :editor-options="{ maxLength: 100, readOnly: formularioReadOnly.Serie }">
                                    <DxLabel :text="'Número de serie'" />
                                </DxItem>
                                <DxItem data-field="Antibiotico" editor-type="dxSelectBox" :editor-options="{ dataSource: [{id:1, text:'Si'},{id:0,text:'No'}], displayExpr:'text', valueExpr: 'id', readOnly: formularioReadOnly.Antibiotico }">
                                    <DxLabel :text="'Medicado con antibiótico'" />
                                </DxItem>
                            </DxGroupItem>
                            <DxGroupItem>
                                <DxItem data-field="Lumenes" editor-type="dxSelectBox" :editor-options="{ dataSource: [{id:1},{id:2},{id:3}], displayExpr:'id', valueExpr: 'id', readOnly: formularioReadOnly.Lumenes }">
                                    <DxLabel :text="'Número de lúmenes'" />
                                </DxItem>
                                <DxItem data-field="Tunelizado" editor-type="dxSelectBox" :editor-options="{ dataSource: [{id:1, text:'Si'},{id:0,text:'No'}], displayExpr:'text', valueExpr: 'id', readOnly: formularioReadOnly.Tunelizado }" />
                            </DxGroupItem>
                            <DxGroupItem>
                                <DxItem data-field="ObservacionInsercion" editor-type="dxTextArea" :editor-options="{ maxLength: 100, height: 100, readOnly: formularioReadOnly.Observaciones }">
                                    <DxLabel :text="'Observaciones de la inserción'" />
                                </DxItem>
                            </DxGroupItem>
                        </DxGroupItem>
                    </DxGroupItem>

                    <!-- RETIRO DE CATÉTERES -->
                    <DxGroupItem :col-count-by-screen="colCountByScreenSelect" item-type="group" caption="Retiro catéter">
                        <DxGroupItem item-type="group" caption="Fecha y lugar">
                            <DxItem data-field="FechaRetiro" editor-type="dxDateBox" :use-mask-behavior="true" :col-span="1" :editor-options="{...dateBoxOptions, readOnly: formularioReadOnly.FechaRetiro }">
                                <DxLabel :text="'Fecha de retiro'" />
                            </DxItem>
                            <DxItem data-field="HospitalRetiro" editor-type="dxSelectBox" :col-span="1" :editor-options="{ dataSource: hospitales, displayExpr: 'Nombre', valueExpr: 'CodigoBase', readOnly: formularioReadOnly.HospitalRetiro }">
                                <DxLabel :text="'Hospital donde se retiró'" />
                            </DxItem>
                        </DxGroupItem>
                        <DxGroupItem caption="Indicación(es) para retirar el catéter" :col-count-by-screen="colCountByScreen" :col-span="2">
                            <DxItem v-for="(item) in indicacionesRetiro" v-bind:key="item.id" :data-field="item.name" editor-type="dxCheckBox" css-class="check-box" :editor-options="{ readOnly: formularioReadOnly.IndicacionesRetiro }">
                                <DxLabel :text="item.text" />
                            </DxItem>
                            <DxItem data-field="OtroIndicacionesRetiro" editor-type="dxTextBox" :editor-options="{ maxLength: 100, readOnly: formularioReadOnly.IndicacionesRetiro }">
                                <DxLabel :text="'Otro'" />
                            </DxItem>
                        </DxGroupItem>
                        <DxGroupItem caption="Ante la sospecha de infección">
                            <!-- <DxItem v-for="(item) in sospechaInfeccion" v-bind:key="item.id" :data-field="item.name" editor-type="dxCheckBox" css-class="check-box" :editor-options="{ readOnly: consulta }">
                                            <DxLabel :text="item.text" />
                                        </DxItem> -->
                            <DxItem data-field="Sospecha" editor-type="dxRadioGroup" :editor-options="{ dataSource: sospechaInfeccion, displayExpr: 'text', valueExpr: 'id', layout: 'vertical', readOnly: formularioReadOnly.Sospecha, onValueChanged: valueChangedSospecha }">
                                <DxLabel :visible="false" />
                            </DxItem>
                            <DxLabel :text="'(Si cambio acceso vascular llene un nuevo formulario)'" />
                        </DxGroupItem>
                        <DxGroupItem caption="Envíos a cultivo">
                            <DxItem data-field="EnvioPunta" editor-type="dxSelectBox" :editor-options="{ dataSource: [{id:1, text:'Si'},{id:0,text:'No'}], displayExpr:'text', valueExpr: 'id', readOnly: formularioReadOnly.PuntaCultivo }">
                                <DxLabel :text="'Punta de catéter retirado'" />
                            </DxItem>
                            <DxItem data-field="EnvioOtro" editor-type="dxSelectBox" :editor-options="{ dataSource: [{id:1, text:'Si'},{id:0,text:'No'}], displayExpr:'text', valueExpr: 'id', readOnly: formularioReadOnly.OtraMuestra }">
                                <DxLabel :text="'Otra muestra'" />
                            </DxItem>
                            <DxItem v-for="(item) in muestraCultivo" :visible="muestraCultivoSeleccionada === 1" v-bind:key="item.id" :data-field="item.name" editor-type="dxCheckBox" css-class="check-box" :editor-options="{ readOnly: (formularioReadOnly.MuestraCultivo || muestraCultivoSeleccionada !== 1) }">
                                <DxLabel :text="item.text" />
                            </DxItem>
                        </DxGroupItem>
                        <DxGroupItem item-type="group" caption="Gérmen(es) aislado(s)">
                            <DxItem data-field="Hemocultivo" editor-type="dxTextBox" :editor-options="{ maxLength: 100, readOnly: formularioReadOnly.Hemocultivo }">
                                <DxLabel :text="'Hemocultivo'" />
                            </DxItem>
                            <DxItem data-field="PuntaCateter" editor-type="dxTextBox" :editor-options="{ maxLength: 100, readOnly: formularioReadOnly.PuntaCateter }">
                                <DxLabel :text="'Punta de catéter'" />
                            </DxItem>
                            <DxItem data-field="MaterialPurulento" editor-type="dxTextBox" :editor-options="{ maxLength: 100, readOnly: formularioReadOnly.MaterialPurulento }">
                                <DxLabel :text="'Material purulento del acceso vascular'" />
                            </DxItem>

                        </DxGroupItem>
                    </DxGroupItem>

                    <DxGroupItem item-type="group" caption=" ">
                        <DxButtonItem :col-span="3" :button-options="submitButtonOptions" horizontal-alignment="center" verical-alignment="center" :visible="showGuardar" />
                    </DxGroupItem>
                </DxForm>
            </form>
        </vx-card>
    </vs-col>

</vs-row>
</template>

<script>
import 'devextreme-vue/lookup'
import {
    DxDataGrid,
    DxColumn,
    DxSelection,
} from 'devextreme-vue/data-grid'
import {
    DxForm,
    DxItem,
    DxLabel,
    DxGroupItem,
    DxButtonItem,
} from 'devextreme-vue/form'
import 'devextreme-vue/radio-group'
import {
    DefaultDxGridConfiguration
} from './data'
import 'devextreme-vue/text-area'

const dataEncabezadoRefKey = 'encabezado-form'
const dataGridFechaKey = 'gridFechaKey'

export default {
    name: 'Cateteres',
    components: {
        DxDataGrid,
        DxColumn,
        DxSelection,
        DxForm,
        DxItem,
        DxLabel,
        DxGroupItem,
        DxButtonItem,
    },
    data() {
        return {
            DefaultDxGridConfiguration,
            dataGridFechaKey,
            dataEncabezadoRefKey,
            hospitales: [],
            formulariosCateteres: [],
            tabs: [{
                name: 'Ingreso',
                value: 1
            }, {
                name: 'Consulta',
                value: 2
            }],
            submitButtonOptions: {
                text: 'Guardar formulario',
                type: 'success',
                icon: 'save',
                useSubmitBehavior: true,
            },
            limpiarButtonOptions: {
                text: 'Nuevo formulario',
                type: 'default',
                icon: 'file',
                useSubmitBehavior: false,
                onClick: this.nuevoFormulario
            },
            pdfButtonOptions: {
                text: 'Generar PDF',
                type: 'danger',
                icon: 'fas fa-file-pdf',
                useSubmitBehavior: false,
                onClick: this.generarPDF
            },
            servicios: [{
                    id: 1,
                    text: 'Emergencia'
                }, {
                    id: 2,
                    text: 'UTI'
                },
                {
                    id: 3,
                    text: 'Pisos'
                }, {
                    id: 4,
                    text: 'Sala Op.'
                },
                {
                    id: -1,
                    text: 'Otro'
                }
            ],
            servicioSeleccionado: '',
            sitios: [{
                    text: 'Yugular externo',
                    name: 'Op5',
                    id: 5
                },
                {
                    text: 'Yugular interno',
                    name: 'Op8',
                    id: 8
                },
                {
                    text: 'Subclavio',
                    name: 'Op11',
                    id: 11
                },
                {
                    text: 'Femoral',
                    name: 'Op14',
                    id: 14
                },
                {
                    text: 'PICC en miembro superior',
                    name: 'Op17',
                    id: 17
                },
                {
                    text: 'Umbilical venoso',
                    name: 'Op20',
                    id: 20
                },
                {
                    text: 'Umbilical arterial',
                    name: 'Op21',
                    id: 21
                },
                {
                    text: 'Implantofix',
                    name: 'Op22',
                    id: 22
                },
                {
                    text: 'Otro',
                    id: -1
                }
            ],
            sitioSeleccionado: '',
            indicacionesUso: [{
                    text: 'Medicamentos IV',
                    name: 'Op23',
                    id: 23
                },
                {
                    text: 'Monitoreo hemodinámico',
                    name: 'Op24',
                    id: 24
                },
                {
                    text: 'Hemodiálisis',
                    name: 'Op25',
                    id: 25
                },
                {
                    text: 'Quimioterapia',
                    name: 'Op26',
                    id: 26
                },
                {
                    text: 'Soluciones IV',
                    name: 'Op27',
                    id: 27
                },
                {
                    text: 'Alimentación parenteral',
                    name: 'Op28',
                    id: 28
                },
                {
                    text: 'Cambio de catéter',
                    name: 'Op29',
                    id: 29
                },
                {
                    text: 'Sangre y derivados',
                    name: 'Op30',
                    id: 30
                },
            ],
            indicacionesRetiro: [{
                    text: 'Innecesario',
                    name: 'Op34',
                    id: 34
                },
                {
                    text: 'Mal funcionamiento',
                    name: 'Op35',
                    id: 35
                },
                {
                    text: 'Sospecha de infección local',
                    name: 'Op36',
                    id: 36
                },
                {
                    text: 'Sospecha de bacteriemia',
                    name: 'Op37',
                    id: 37
                }
            ],
            sospechaInfeccion: [{
                    text: 'Se retiró el catéter',
                    name: 'Op31',
                    id: 31
                },
                {
                    text: 'Se cambió el catéter sobre guía',
                    name: 'Op32',
                    id: 32
                },
                {
                    text: 'Se colocó catéter en otro acceso vascular',
                    name: 'Op33',
                    id: 33
                }
            ],
            sospechaSeleccionada: '',
            muestraCultivo: [{
                    text: 'Sangre',
                    name: 'Op38',
                    id: 38
                },
                {
                    text: 'Material purulento del acceso vascular',
                    name: 'Op39',
                    id: 39
                }
            ],
            muestraCultivoSeleccionada: '',

            formulario: {
                NombreCompletoOperador: null,
                NombreOperador: null,
                ApellidoOperador: null,
                IdCateter: null,
                FechaInsercion: null,
                HospitalColoco: null,
                Servicio: null, //
                ServicioOtro: null, //
                Sitio: null, //
                SitioOtro: null, //
                IntentosInsercion: null, //
                Sospecha: null, //
                Especialidad: null,
                Colegiado: null,
                IndicacionesOtros: null,
                Marca: null,
                NumeroSerie: null,
                Antibiotico: null, //
                Lumenes: null, //
                Tunelizado: null,
                ObservacionInsercion: null,
                FechaRetiro: null,
                HospitalRetiro: null,
                OtroIndicacionesRetiro: null,
                EnvioPunta: null, //
                EnvioOtro: null, //
                Hemocultivo: null,
                PuntaCateter: null,
                MaterialPurulento: null,
                //Inicio Indicaciones Uso 
                Op23: false,
                Op24: false,
                Op25: false,
                Op26: false,
                Op27: false,
                Op28: false,
                Op29: false,
                Op30: false,
                //Fin Indicaciones Uso
                //Inicio sospechaInfeccion
                Op31: false,
                Op32: false,
                Op33: false,
                //Fin sospechaInfeccion
                //Inicio indicacionesRetiro
                Op34: false,
                Op35: false,
                Op36: false,
                Op37: false,
                //Fin indicacionesRetiro
                Op38: false,
                Op39: false,
            },
            formularioReadOnly: {
                FechaInsercion: false,
                HospitalInsercion: false,
                Servicio: false,
                Sitio: false,
                Intentos: false,
                IndicacionesUso: false,
                Marca: false,
                Serie: false,
                Antibiotico: false,
                Lumenes: false,
                Tunelizado: false,
                Observaciones: false,
                FechaRetiro: false,
                HospitalRetiro: false,
                IndicacionesRetiro: false,
                Sospecha: false,
                PuntaCultivo: false,
                OtraMuestra: false,
                Muestras: false,
                Hemocultivo: false,
                PuntaCateter: false,
                MaterialPurulento: false,
            },

            dateBoxOptions: {
                max: new Date(),
                type: "date",
            },
            showGuardar: true,
            idCateterSeleccionado: '',
        }
    },
    props: {
        SerieAdmision: null,
        CodigoAdmision: null,
        NumeroExpediente: null,
        StatusAdmision: null,
        TipoOperacion: {
            /*
                I = Inserccion
                A = Actualizacion
                C = Consulta
            */
            default: 'I'
        },
        consulta: null, //Variable para indicar si el componente se usará para consulta -> 'true' o para ingreso -> 'false'
    },
    methods: {
        handleSubmit(e) {
            e.preventDefault()
            if (this.SerieAdmision && this.CodigoAdmision) {
                let sitio = ''
                let otroSitio = ''

                if (this.formulario.Sitio >= 5 && this.formulario.Sitio <= 17) {
                    let idTotal = Number(this.formulario.Sitio) + Number(this.formulario.DeIz)
                    sitio = this.formulario.Sitio + ',' + idTotal
                } else if (this.formulario.Sitio >= 20) {
                    sitio = this.formulario.Sitio
                } else if (this.formulario.Sitio === -1) {
                    sitio = null
                    otroSitio = this.formulario.SitioOtro
                }

                let postData = {
                    IdFormularioCateter: this.formulario.IdCateter,
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    FechaInsercion: this.formulario.FechaInsercion,
                    HospitalColoco: this.formulario.HospitalColoco,
                    ServicioColoco: this.formulario.Servicio === -1 ? null : this.formulario.Servicio,
                    ServicioColocoOtro: this.formulario.ServicioOtro,
                    Sospecha: this.sospechaSeleccionada,
                    NombreOperador: this.formulario.NombreOperador,
                    ApellidoOperador: this.formulario.ApellidoOperador,
                    Especialidad: this.formulario.Especialidad,
                    Colegiado: this.formulario.Colegiado,
                    SitioInsercion: sitio,
                    SitioInsercionOtro: otroSitio,
                    NumeroIntentos: this.formulario.IntentosInsercion,
                    IndicacionesUso: this.ObtenerValoresCheck('IndicacionesUso'),
                    IndicacionesUsoOtro: this.formulario.IndicacionesOtros,
                    Marca: this.formulario.Marca,
                    NumeroSerie: this.formulario.NumeroSerie,
                    MedicadoAntibiotico: this.formulario.Antibiotico,
                    NumeroLumenes: this.formulario.Lumenes,
                    Tunelizado: this.formulario.Tunelizado,
                    ObservacionInsercion: this.$reemplazar_tabulares(this.formulario.ObservacionInsercion),
                    FechaRetiro: this.formulario.FechaRetiro ? this.formulario.FechaRetiro : null,
                    HospitalRetiro: this.formulario.HospitalRetiro,
                    IndicacionesRetiro: this.ObtenerValoresCheck('IndicacionesRetiro'),
                    OtroIndicacionesRetiro: this.formulario.OtroIndicacionesRetiro,
                    SospechaInfeccion: this.sospechaSeleccionada,
                    PuntaCateterCultivo: this.formulario.EnvioPunta,
                    OtraMuestraCultivo: this.formulario.EnvioOtro,
                    MuestraCultivo: this.ObtenerValoresCheck('MuestraCultivo'),
                    Hemocultivo: this.formulario.Hemocultivo,
                    PuntaCateter: this.formulario.PuntaCateter,
                    MaterialPurulento: this.formulario.MaterialPurulento
                }
                this.axios.post("/app/v1_ExpedienteEvolucion/InsertarCateter", {
                    Opcion: this.formulario.IdCateter === null ? 'I' : 'A',
                    ...postData
                }).then(() => {
                    this.cargarFechasCateteres()
                    this.limpiarFormulario()
                    this.cargarDatosAjeno()

                })
            }
        },
        cargarFormulario(indice) {
            this.axios.post('/app/v1_ExpedienteEvolucion/BuscarCateter', {
                    Id: this.idCateterSeleccionado
                })
                .then(resp => {
                    this.formulario = {
                        NombreCompletoOperador: resp.data.json.NombreDoctorIngreso + ' ' + resp.data.json.ApellidoDoctorIngreso,
                        NombreOperador: resp.data.json.NombreDoctorIngreso,
                        ApellidoOperador: resp.data.json.ApellidoDoctorIngreso,
                        IdCateter: resp.data.json.IdCateter,
                        FechaInsercion: resp.data.json.FechaInsercion,
                        HospitalColoco: resp.data.json.CodigoBaseHospitalColoco,
                        Servicio: null,
                        ServicioOtro: resp.data.json.ServicioColocoOtro,
                        Sitio: null,
                        SitioOtro: resp.data.json.SitioInsercionOtro,
                        IntentosInsercion: resp.data.json.IntentosInsercion,
                        Sospecha: null,
                        Especialidad: resp.data.json.EspecialidadDoctorIngreso,
                        Colegiado: resp.data.json.ColegiadoDoctorIngreso,
                        IndicacionesOtros: resp.data.json.IndicacionesUsoOtro,
                        Marca: resp.data.json.MarcaCateter,
                        NumeroSerie: resp.data.json.NoSerieCateter,
                        Antibiotico: resp.data.json.MedicadoAntibiotico ? 1 : 0,
                        Lumenes: resp.data.json.NoLumenes !== null ? Number(resp.data.json.NoLumenes) : null,
                        Tunelizado: resp.data.json.Tunelizado ? 1 : 0,
                        ObservacionInsercion: resp.data.json.ObservacionInsercion,
                        FechaRetiro: resp.data.json.FechaRetiro,
                        HospitalRetiro: resp.data.json.CodigoBaseHospitalRetiro,
                        OtroIndicacionesRetiro: resp.data.json.IndicacionCateterOtro,
                        EnvioPunta: resp.data.json.PuntaCateterCultivo ? 1 : 0,
                        EnvioOtro: resp.data.json.OtraMuestraCultivo ? 1 : 0,
                        Hemocultivo: resp.data.json.HemocultivoGermen,
                        PuntaCateter: resp.data.json.PuntaCateterGermen,
                        MaterialPurulento: resp.data.json.MaterialPurulentoGermen,
                        //Indicaciones Uso 
                        Op23: false,
                        Op24: false,
                        Op25: false,
                        Op26: false,
                        Op27: false,
                        Op28: false,
                        Op29: false,
                        Op30: false,
                        //Sospecha Infeccion
                        Op31: false,
                        Op32: false,
                        Op33: false,
                        //IndicacionesRetiro
                        Op34: false,
                        Op35: false,
                        Op36: false,
                        Op37: false,
                        //Envios a cultivo
                        Op38: false,
                        Op39: false,
                    }

                    this.formularioReadOnly = {
                        FechaInsercion: resp.data.json.FechaInsercion !== null ? true : false,
                        HospitalInsercion: resp.data.json.CodigoBaseHospitalColoco !== null ? true : false,
                        Intentos: resp.data.json.IntentosInsercion !== null ? true : false,
                        Marca: resp.data.json.MarcaCateter !== null ? true : false,
                        Serie: resp.data.json.NoSerieCateter !== null ? true : false,
                        Antibiotico: resp.data.json.MedicadoAntibiotico !== null ? true : false,
                        Lumenes: resp.data.json.NoLumenes !== null ? true : false,
                        Tunelizado: resp.data.json.Tunelizado !== null ? true : false,
                        Observaciones: resp.data.json.ObservacionInsercion !== null ? true : false,
                        FechaRetiro: resp.data.json.FechaRetiro !== null ? true : false,
                        HospitalRetiro: resp.data.json.CodigoBaseHospitalRetiro !== null ? true : false,
                        PuntaCultivo: resp.data.json.PuntaCateterCultivo !== null ? true : false,
                        OtraMuestra: resp.data.json.OtraMuestraCultivo !== null ? true : false,
                        Hemocultivo: resp.data.json.HemocultivoGermen !== null ? true : false,
                        PuntaCateter: resp.data.json.PuntaCateterGermen !== null ? true : false,
                        MaterialPurulento: resp.data.json.MaterialPurulentoGermen !== null ? true : false,

                        //Variables que se validaran en el mapeo de seleccionados
                        Servicio: false,
                        Sitio: false,
                        IndicacionesUso: false,
                        IndicacionesRetiro: false,
                        Sospecha: false,
                        MuestraCultivo: false
                    }

                    this.mapearSeleccionados(resp.data.json.Seleccionados)

                    let valores = Object.values(this.formularioReadOnly)

                    if (valores.includes(false)) {
                        this.showGuardar = true
                    } else {
                        this.showGuardar = false
                    }

                })

            //Se agregó esta función ya que al seleccionar una fecha del datagrid, el datagrid vuelve a la primera página
            setTimeout(() => {
                this.dataGridFecha.pageIndex(indice)
            }, 100);

        },
        mapearSeleccionados(seleccionados) {
            let sitios = [5, 8, 11, 14, 17]
            let opciones = [4, 6, 7]

            seleccionados.map((x) => {
                if (x.IdEncabezado === 1) {
                    this.formulario.Servicio = x.IdDetalle
                    this.formularioReadOnly.Servicio = true
                } else if (x.IdEncabezado === 2) { //Opciones que pueden ser Derecho o Izquierdo
                    if (sitios.includes(x.IdDetalle)) {
                        this.formulario.Sitio = x.IdDetalle
                    }

                    if (x.Descripcion === "Derecho") {
                        this.formulario.DeIz = 1
                    } else if (x.Descripcion === "Izquierdo") {
                        this.formulario.DeIz = 2
                    }

                    this.formularioReadOnly.Sitio = true
                } else if (x.IdEncabezado === 3) { //Opciones que son un único valor
                    this.formulario.Sitio = x.IdDetalle
                    this.formularioReadOnly.Sitio = true
                } else if (opciones.includes(x.IdEncabezado)) {
                    this.formulario["Op" + x.IdDetalle] = true
                    if (x.IdEncabezado === 4) {
                        this.formularioReadOnly.IndicacionesUso = true
                    } else if (x.IdEncabezado === 6) {
                        this.formularioReadOnly.IndicacionesRetiro = true
                    } else if (x.IdEncabezado === 7) {
                        this.formularioReadOnly.MuestraCultivo = true
                    }
                } else if (x.IdEncabezado === 5) {
                    this.formulario.Sospecha = x.IdDetalle
                    this.formularioReadOnly.Sospecha = true
                }

                //Validación para cuando ingresaron otro servicio diferente al del catálogo
                if (!this.formularioReadOnly.Servicio && this.formulario.ServicioOtro !== null) {
                    this.formulario.Servicio = -1
                    this.formularioReadOnly.Servicio = true
                }

                //Validación para cuando ingresaron otro sitio diferente al del catálogo
                if (!this.formularioReadOnly.Sitio && this.formulario.SitioOtro !== null) {
                    this.formulario.Sitio = -1
                    this.formularioReadOnly.Sitio = true
                }
            })
        },
        refreshFechaDataGrid() {
            this.dataGridFecha.pageIndex(0);
            this.dataGridFecha.clearSelection();
            this.dataGridFecha.clearSorting();
            this.dataGridFecha.columnOption('FechaRegistro', 'sortOrder', 'desc');
        },
        onSelectionChangedFecha(e) {
            if (e.selectedRowsData.length > 0) {
                this.idCateterSeleccionado = e.selectedRowsData[0].IdCateter
                this.cargarFormulario(this.dataGridFecha.pageIndex())
            }
        },
        ObtenerValoresCheck(seccion) {
            let valoresCheck = ''
            if (seccion == 'IndicacionesUso') {
                valoresCheck = (this.formulario.Op23 ? ',23' : '') +
                    (this.formulario.Op24 ? ',24' : '') +
                    (this.formulario.Op25 ? ',25' : '') +
                    (this.formulario.Op26 ? ',26' : '') +
                    (this.formulario.Op27 ? ',27' : '') +
                    (this.formulario.Op28 ? ',28' : '') +
                    (this.formulario.Op29 ? ',29' : '') +
                    (this.formulario.Op30 ? ',30' : '')
            } else if (seccion == 'IndicacionesRetiro') {
                valoresCheck = (this.formulario.Op34 ? ',34' : '') +
                    (this.formulario.Op35 ? ',35' : '') +
                    (this.formulario.Op36 ? ',36' : '') +
                    (this.formulario.Op37 ? ',37' : '')
            } else if (seccion == 'MuestraCultivo') {
                valoresCheck = (this.formulario.Op38 ? ',38' : '') +
                    (this.formulario.Op39 ? ',39' : '')
            }
            return valoresCheck.length > 0 ? valoresCheck.slice(1) : ''
        },
        limpiarFormulario() {
            this.formulario = {
                NombreCompletoOperador: null,
                NombreOperador: null,
                ApellidoOperador: null,
                IdCateter: null,
                FechaInsercion: null,
                HospitalColoco: null,
                Servicio: null,
                ServicioOtro: null,
                Sitio: null,
                SitioOtro: null,
                IntentosInsercion: null,
                Sospecha: null,
                Especialidad: null,
                Colegiado: null,
                IndicacionesOtros: null,
                Marca: null,
                NumeroSerie: null,
                Antibiotico: null,
                Lumenes: null,
                Tunelizado: null,
                ObservacionInsercion: null,
                FechaRetiro: null,
                HospitalRetiro: null,
                OtroIndicacionesRetiro: null,
                EnvioPunta: null,
                EnvioOtro: null,
                Hemocultivo: null,
                PuntaCateter: null,
                MaterialPurulento: null,
                //Inicio Indicaciones Uso 
                Op23: false,
                Op24: false,
                Op25: false,
                Op26: false,
                Op27: false,
                Op28: false,
                Op29: false,
                Op30: false,
                //Fin Indicaciones Uso
                //Inicio sospechaInfeccion
                Op31: false,
                Op32: false,
                Op33: false,
                //Fin sospechaInfeccion
                //Inicio indicacionesRetiro
                Op34: false,
                Op35: false,
                Op36: false,
                Op37: false,
                //Fin indicacionesRetiro
                Op38: false,
                Op39: false,
            }
        },
        limpiarFormularioReadOnly() {
            this.formularioReadOnly = {
                FechaInsercion: false,
                HospitalInsercion: false,
                Servicio: false,
                Sitio: false,
                Intentos: false,
                IndicacionesUso: false,
                Marca: false,
                Serie: false,
                Antibiotico: false,
                Lumenes: false,
                Tunelizado: false,
                Observaciones: false,
                FechaRetiro: false,
                HospitalRetiro: false,
                IndicacionesRetiro: false,
                Sospecha: false,
                PuntaCultivo: false,
                OtraMuestra: false,
                Muestras: false,
                Hemocultivo: false,
                PuntaCateter: false,
                MaterialPurulento: false,
            }
        },
        cargarDatosAjeno() {
            this.axios.post("/app/v1_ExpedienteEvolucion/BusquedaDatosAjeno", {})
                .then((resp) => {
                    this.formulario.NombreOperador = resp.data[0].Nombres
                    this.formulario.ApellidoOperador = resp.data[0].Apellidos
                    this.formulario.Especialidad = resp.data[0].Especialidad
                    this.formulario.Colegiado = resp.data[0].Colegiado
                    this.formulario.NombreCompletoOperador = resp.data[0].Nombres + ' ' + resp.data[0].Apellidos

                    this.cateteresForm.repaint()
                })
        },
        cargarHospitales() {
            this.axios.post("/app/v1_ExpedienteEvolucion/BusquedaHospitales", {})
                .then((resp) => {
                    this.hospitales = resp.data
                })
        },
        cargarFechasCateteres() {
            this.axios.post("/app/v1_ExpedienteEvolucion/BusquedaFechasCateteres", {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision
                })
                .then((resp) => {
                    this.formulariosCateteres = resp.data.map((x, index) => {
                        return {
                            id: index,
                            FechaRegistro: x.FechaRegistro,
                            IdCateter: x.IdCateter
                        }
                    })
                })
            this.dataGridFecha.columnOption('FechaRegistro', 'sortOrder', 'desc');
        },
        valueChangedSospecha(e) {
            if (e.value !== null) {
                this.sospechaSeleccionada = e.value.id
            }
        },
        nuevoFormulario() {
            this.limpiarFormularioReadOnly()
            this.limpiarFormulario()
            this.cargarDatosAjeno()
            this.refreshFechaDataGrid()
        },
        generarPDF() {
            let postData = {
                IdCateter: this.formulario.IdCateter
            }

            this.$reporte_modal({
                Nombre: 'Catéteres',
                Opciones: {
                    ...postData
                }
            })
        }
    },
    mounted() {
        this.cargarFechasCateteres()
        if (this.TipoOperacion == 'I' || this.TipoOperacion == 'A') {
            this.cargarHospitales()
        }

        if (this.TipoOperacion == 'I') {
            this.cargarDatosAjeno()
        }
    },
    watch: {
        'formulario.Servicio'(newval) {
            if (newval !== -1) {
                this.formulario.ServicioOtro = ''
            }
            this.servicioSeleccionado = newval
        },
        'formulario.Sitio'(newval) {
            if (newval !== -1) {
                this.formulario.SitioOtro = ''
            }
            this.sitioSeleccionado = newval
        },
        'formulario.EnvioOtro'(newval) {
            this.muestraCultivoSeleccionada = newval
        },
    },
    computed: {
        colCountByScreen() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 2,
                    lg: 2
                };
        },
        colCountByScreenSelect() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 3,
                    lg: 3,
                    xl: 3
                };
        },

        cateteresForm: function () {
            return this.$refs[dataEncabezadoRefKey].instance;
        },
        dataGridFecha: function () {
            return this.$refs[dataGridFechaKey].instance;
        }
    },

}
</script>

<style>
#cateteres .dx-item-content .dx-box-item-content {
    place-content: baseline !important;
}

#cateteres .dx-invalid .dx-radiobutton-icon::before {
    border-color: red !important
}

#cateteres .dx-item-content .dx-box-item-content {
    place-content: center;
    padding: 5px !important;
    padding-right: 10px !important;
}

#cateteres .dx-layout-manager .dx-label-h-align .dx-field-item-label {
    white-space: pre-wrap;
    vertical-align: middle !important;
    word-break: break-word;
}

#cateteres .dx-layout-manager .dx-label-h-align.dx-flex-layout:not(.dx-field-item-label-align) {
    align-items: center !important;
}

#cateteres .dx-layout-manager .dx-label-h-align.dx-flex-layout .dx-field-item-label {
    min-width: 90%;
    max-width: 90%;
}

#cateteres .dx-layout-manager .dx-label-h-align.dx-flex-layout .dx-field-item-content,
.dx-layout-manager .dx-label-h-align.dx-flex-layout .dx-field-item-content-wrapper {
    min-width: 10%;
    max-width: 10%;
}

#cateteres .dx-layout-manager .dx-label-h-align.dx-flex-layout {
    padding: 0px !important;
    padding-left: 5px !important;
    padding-right: 5px !important;
}

#cateteres .dx-field-item-label-content {
    width: 90%;
}

#cateteres .dx-last-col .dx-last-row .dx-field-item .dx-col-2 .dx-field-item-optional .dx-flex-layout {
    place-content: center;
    padding: 5px !important;
    padding-right: 10px !important;
}

#cateteres .check-box {
    border-color: #e3e3e3;
    border-style: dotted;
    border-width: 1px;
}
</style>
