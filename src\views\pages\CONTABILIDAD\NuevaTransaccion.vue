<template>
  <div>
      <div>
          <form @submit="handleSubmit">
              <DxForm :ref="formTransaccion" :form-data.sync="formulario" label-mode="floating" style="margin: 5px">

                  <DxFormGroupItem :col-count="2">
                      <DxFormGroupItem>
                          <DxFormItem data-field="Cuenta" editor-type="dxSelectBox" :editor-options="{
                          width: 'auto',
                          searchEnabled: true,
                          items: cuentas,
                          displayExpr: 'Valor',
                          valueExpr: 'Codigo',
                          onValueChanged: AsignarSiguienteCheque,
                        }" :validationRules="[{ type: 'required' }]" />
                      </DxFormGroupItem>

                      <DxFormGroupItem :col-count="3">
                          <DxFormItem
                                data-field="Fecha"
                                editor-type="dxDateBox"
                                :validation-rules="[{ type: 'required' }]"
                                :editor-options="{
                                    openOnFieldClick: true,
                                    calendarOptions: {
                                        cellTemplate: 'cell' // Opcional: personalizar celdas si es necesario
                                    }
                                }"
                          />
                          <DxFormItem data-field="SiguienteCheque" editor-type="dxNumberBox"  :validationRules="[{ type: 'required' }]"  >
                              <DxFormLabel text="Numero" />
                          </DxFormItem>
                          <DxFormItem data-field="Monto" editor-type="dxNumberBox" :editor-options="{ min: 0, format: '#0.00', step: 0 }"  :validationRules="[{ type: 'required' }]" >
                              <DxFormLabel text="Monto" />
                          </DxFormItem>
                      </DxFormGroupItem>
                  </DxFormGroupItem>

                  <DxFormGroupItem :col-count-by-screen="colCountByScreen2">
                      <DxFormItem data-field="Funcion" editor-type="dxRadioGroup" :editor-options="{
                            items: funciones,
                            displayExpr: 'text',
                            valueExpr: 'id',
                            layout: 'horizontal',
                          }">
                          <DxFormLabel text="Función" />
                      </DxFormItem>
                      <!--    <DxFormButtonItem :button-options="buttonMayorizar" name="Periodo" horizontal-alignment="left" verical-alignment="left" />   -->
                  </DxFormGroupItem>

                  <DxFormGroupItem :col-count-by-screen="colCountByScreen3">
                      <DxFormItem data-field="Observaciones" editor-type="dxTextArea" :editor-options="{ height: '100%' }" />
                  </DxFormGroupItem>
                  <DxFormGroupItem :col-count="2" captio=" ">
                      <DxFormButtonItem :button-options="buttonGrabar" name="Grabar" horizontal-alignment="center" verical-alignment="center" />
                      <DxFormButtonItem :button-options="buttonLimpiarDatos" name="Periodo" horizontal-alignment="center" verical-alignment="center" />
                  </DxFormGroupItem>

              </DxForm>
          </form>

          <DxDataGrid :ref="gridDetalle" v-bind="DefaultDxGridConfiguration" :data-source="valoresTabla" :headerFilter="{ visible: false, allowSearch: false }" :width="'100%'" height="auto" @editor-preparing="overrideOnEditorPreparing" @init-new-row="onInitNewRow">
              <!-- Habilitar la edición de celdas -->
              <DxDataGridEditing mode="cell" :allow-updating="true" :allow-adding="true" :allow-deleting="true" :use-icons="true" />
              <!-- Selección de fila única -->
              <DxDataGridSelection mode="single" />
              <!-- Definir columnas editables -->
              <DxDataGridColumn width="10%" data-field="Documento" alignment="center" data-type="number" />
              <DxDataGridColumn width="30%" data-field="Detalle" alignment="center" data-type="string" />
              <DxDataGridColumn width="10%" data-field="Cuenta" alignment="center" data-type="string" />
              <DxDataGridColumn width="30%" data-field="Nombre" caption="Nombre de la cuenta" alignment="center" data-type="string" :allow-editing="false" />
              <DxDataGridColumn width="10%" data-field="Debe" alignment="center" data-type="number" :customize-text="customizeTextValores" :editor-options="{ min: 0 }">
                  <DxDataGridFormat name="holis" type="decimal" :precision="2" />
              </DxDataGridColumn>
              <DxDataGridColumn width="10%" data-field="Haber" alignment="center" data-type="number" :customize-text="customizeTextValores" :editor-options="{ min: 0 }"/>
              <!-- Sumarios -->
              <DxDataGridSummary>
                  <DxDataGridTotalItem column="Debe" summary-type="sum" :customize-text="customizeTextValores" :value-format="{ type: 'fixedPoint', precision: 2 }" />
                  <DxDataGridTotalItem column="Haber" summary-type="sum" :customize-text="customizeTextValores" />
              </DxDataGridSummary>
          </DxDataGrid>
      </div>
  </div>
</template>

<script>

import { DefaultDxGridConfiguration } from "./data";

const formTransaccion = "formTransaccion";
const gridDetalle = "gridDetalle";

export default {
  name: "transaccion",
 
  data() {
    return {
      SelectedOption: 1,
      formTransaccion,
      gridDetalle,
      DefaultDxGridConfiguration,
      cuentas: [],
      valoresTabla: [],

      formulario: {
        Cuenta: null,
        fecha: null,
        SiguienteCheque: null,
        Monto: null,
        Documento: null,
        Detalle: null,
        Fecha: new Date(),
        Observaciones: null,
        BancoContabilidad: null,
        NombreCuentaBanco: null,
      },

      currentDate: new Date().toJSON().substr(0, 10),

      funciones: [
        {
          id: "DP",
          text: "Deposito",
        },
        {
          id: "ND",
          text: "Nota de débito",
        },
        {
          id: "NC",
          text: "Nota de Credito",
        },
        {
          id: "CV",
          text: "Credito por Vencimiento",
        },
        {
          id: "CA",
          text: "Credito por Anulación",
        },
      ],

      buttonLimpiarDatos: {
        width: "auto",
        icon: "fas fa-trash",
        text: "limpiar",
        type: "danger",
        onClick: () => {
          this.limpiar()
        },
        useSubmitBehavior: true,
      },

      buttonGrabar: {
        width: "auto",
        icon: "fas fa-save",
        text: "Grabar",
        type: "success",
        onClick: () => {
          this.Grabar();
        },
        useSubmitBehavior: false,
      },
    };
  },
  methods: {
    handleSubmit(e) {
      e.preventDefault();
    },

    validateForm(e) {
      e.component.validate();
    },

    CargarCuentas(asignar) {
      // La variable de asignar indica cuando debe seleccionar la primera cuenta de la lista
      this.axios
        .post("/app/v1_bancos/Cuentas", {
          Opcion: 1,
        })
        .then((resp) => {
          this.cuentas = resp.data.json;

          if (asignar) {
            this.AsignarBanco(this.cuentas[0]);
          }
        });
    },

    AsignarSiguienteCheque(e) {
      let cuenta = this.cuentas.find((x) => x.Codigo == e.value);
      this.AsignarBanco(cuenta);
    },

    AsignarBanco(e) {
      //Asignar los valores del banco a los campos correspondientes del formulario
      this.formulario.Cuenta = e.Codigo;
      this.formulario.BancoContabilidad = e.Contabilidad;
      this.formulario.NumeroCuenta = e.Cuenta;
      this.formulario.NombreCuentaBanco = e.NombreCuenta;
      this.formulario.SiguienteCheque = null;
    },
    
    customizeTextValores(cellInfo) {
        if (cellInfo.value !== null && cellInfo.value !== "" && Number(cellInfo.value) !== 0) {
            const valorRedondeado = Number(cellInfo.value).toFixed(2);
            return "Q. " + valorRedondeado;
        }
        return null;
    },

    LlenarTabla() {
      // Limpiar los valores actuales de la tabla para evitar duplicados
      this.valoresTabla = [];
      // Validar que la cuenta bancaria y el siguiente cheque sean válidos
      if (
        this.formulario.SiguienteCheque !== null &&
        this.formulario.SiguienteCheque !== "" &&
        this.formulario.SiguienteCheque !== 0
      ) {
        if (["DP", "NC"].includes(this.formulario.Funcion)) {
          this.valoresTabla.push({
            Documento: this.formulario.SiguienteCheque,
            Detalle: this.formulario.Detalle || " ", // Se usa 'Sin detalle' si no se proporciona uno
            Cuenta: this.formulario.BancoContabilidad,
            Nombre: this.formulario.NombreCuentaBanco,
            Haber: null,
            Debe: this.formulario.Monto,
          });
        } else if (["ND"].includes(this.formulario.Funcion)) {
          this.valoresTabla.push({
            Documento: this.formulario.SiguienteCheque,
            Detalle: this.formulario.Detalle || " ", // Se usa 'Sin detalle' si no se proporciona uno
            Cuenta: this.formulario.BancoContabilidad,
            Nombre: this.formulario.NombreCuentaBanco,
            Haber: this.formulario.Monto,
            Debe: null,
          });
        } else {
          this.valoresTabla.push({
            Documento: this.formulario.SiguienteCheque,
            Detalle: this.formulario.Detalle || "", // Se usa 'Sin detalle' si no se proporciona uno
            Cuenta: this.formulario.BancoContabilidad,
            Nombre: this.formulario.NombreCuentaBanco,
            Haber: null || " ",
            Debe: null || " ",
          });
        }
      } else {
        // Mostrar una alerta si la cuenta de banco no está definida
        this.$vs.dialog({
          type: "alert",
          color: "#ED8C72",
          title: "Cuenta no definida",
          acceptText: "Aceptar",
          text: "La cuenta de banco seleccionada no está definida.",
          buttonCancel: "border",
          accept: () => {},
        });
      }
    },

    async Grabar() {
      const grid = this.$refs.gridDetalle.instance;
      const totalDebe = grid.getTotalSummaryValue("Debe");
      const totalHaber = grid.getTotalSummaryValue("Haber");

      

      if (totalDebe !== totalHaber) {
        this.$vs.dialog({
          type: "alert",
          color: "#e74c3c",
          title: "Error en el voucher",
          acceptText: "Aceptar",
          text: "Columnas del voucher no cuadran. Verifique.",
          buttonCancel: "border",
          customClass: "custom-dialog",
          accept: () => {
            
          },
        });

        return;
      }

      try {
        // Primer intento de grabado
        await this.axios.post("/app/v1_bancos/InsertarBancosMovtostrans", {
          Opcion: 1,
          SubOpcion: 1,
          Cuenta: this.formulario.Cuenta,
          Numero: this.formulario.SiguienteCheque,
          Fecha: new Date(this.formulario.Fecha).toISOString().split("T")[0] + ' 00:00:00',
          Monto: this.formulario.Monto,
          Tipo: this.formulario.Funcion,
          Observaciones: this.formulario.Observaciones,
          
        });

        // Si `Funcion` es uno de los valores específicos, intenta grabar con Grabar1
        if (["DP", "ND", "NC"].includes(this.formulario.Funcion)) {
          await this.Grabar1();
        }
        
        this.limpiar();
      } catch (error) {
        // Acceder al mensaje de error si está presente
        const errorMessage = error.response
          ? error.response.data.Message
          : "Ocurrió un error desconocido.";

        // Mostrar el mensaje del error en el diálogo
        this.$vs.dialog({
          type: "alert",
          color: "#e74c3c",
          title: "Ocurrio un Error",
          acceptText: "Aceptar",
          text: errorMessage, // Mostrar el mensaje del error aquí
          buttonCancel: "border",
          accept: () => {},
        });
      }
    },

    async Grabar1() {
      const Valor = this.valoresTabla;

      // Filtra las líneas que SÍ tienen cuenta antes de mapear
      const Valores = Valor
        .filter(linea => linea.Cuenta && linea.Cuenta.trim() !== "") // Valida que la cuenta exista y no esté vacía
        .map((linea, index) => {
          return {
            Indice: index + 1,
            Documento: linea.Documento,
            Detalle: linea.Detalle,
            Cuenta: linea.Cuenta,
            Nombre: linea.Nombre,
            Debe: linea.Debe,
            Haber: linea.Haber,
          };
        });

      // Si no hay valores válidos, no hagas el POST
      if (Valores.length === 0) {
          this.$vs.dialog({
            type: "alert",
            color: "#e74c3c",
            title: "Ocurrio un Error",
            text:"No hay líneas válidas con cuenta para registrar.",
            acceptText: "Aceptar",
            buttonCancel: "border",
            accept: () => {},
          });
        return;
      }

      // Enviar los datos válidos
      await this.axios.post("/app/v1_bancos/InsertaAuxiliarBancos", {
        Opcion: 1,
        SubOpcion: 2,
        CuentaBanco: this.formulario.Cuenta,
        Referencia: this.formulario.SiguienteCheque,
        Fecha: new Date(this.formulario.Fecha).toISOString().split("T")[0] + ' 00:00:00',
        Tipo: this.formulario.Funcion,
        Detalle: Valores,
      });
    },


    onInitNewRow(e) {
      e.data.Debe = '';
      e.data.Haber = '';
    },

    overrideOnEditorPreparing(e) {

      // Verificamos si la columna es "Cuenta" y si es una fila de datos
      if (e.dataField === "Cuenta" && e.parentType === "dataRow") {
        const defaultValueChangeHandler = e.editorOptions.onValueChanged;

        e.editorOptions.onValueChanged = (args) => {
          const cuenta = args.value; // Captura el valor ingresado de la cuenta
        
          // Realizamos la solicitud para obtener el nombre
          this.axios
            .post("/app/v1_bancos/Transacciones", {
              Opcion: 1,
              SubOpcion: 3,
              Contabilidad: cuenta,
            })
            .then((resp) => {
              if (resp.data.codigo == 0) {
                const updatedNombre = resp.data.json[0].Nombre;
                const rowIndex = e.row.rowIndex;
                
                // Actualizamos la celda de "Nombre"
                this.$refs.gridDetalle.instance.cellValue(
                  rowIndex,
                  "Nombre",
                  updatedNombre
                );

                // Actualizamos la fila para reflejar los cambios
                this.$refs.gridDetalle.instance.refresh();
              }
            })
            .catch(() => {
            });
          
          // Llamada al manejador original de cambio de valor
          defaultValueChangeHandler(args);
        };
      }
    },

    limpiarDatos() {
        this.formulario.Monto = null,
        this.formulario.Documento = null,
        this.formulario.Detalle = null,
        this.formulario.Observaciones = null,
        // this.formulario.BancoContabilidad = null,
        // this.formulario.NombreCuentaBanco = null;
        this.formulario.SiguienteCheque = null

    },

    limpiar () {

            this.formulario.Monto = null,
            this.formulario.Documento = null,
            this.formulario.Detalle = null,
            this.formulario.Observaciones = null,
            // this.formulario.BancoContabilidad = null,
            // this.formulario.NombreCuentaBanco = null,
            this.valoresTabla = []
            this.formulario.SiguienteCheque = null
            // this.CargarCuentas(true);

    },

  },


  created() {},
  mounted() {},
  watch: {
    "formulario.Observaciones"(newval) {
      if (newval !== null && newval !== "" && newval !== undefined) {
        this.LlenarTabla();
      }
    }
  },
  computed: {
    formTransformAnticipoInstanceaccion: function () {
      return this.$refs[formTransaccion].instance;
    },

    colCountByScreen() {
      return this.calculateColCountAutomatically
        ? null
        : {
            sm: 3,
            md: 4,
            lg: 4,
          };
    },

    colCountByScreen2() {
      return this.calculateColCountAutomatically
        ? null
        : {
            sm: 1,
            md: 1,
            lg: 1,
          };
    },

    colCountByScreen3() {
      return this.calculateColCountAutomatically
        ? null
        : {
            sm: 1,
            md: 1,
            lg: 1,
          };
    },

    colCountByScreenButtons() {
      return this.calculateColCountAutomatically
        ? null
        : {
            sm: 1,
            md: 2,
            lg: 4,
          };
    },

    colCountByScreentransaccion() {
      return this.calculateColCountAutomatically
        ? null
        : {
            sm: 3,
            md: 2,
            lg: 3,
          };
    },

    dataGridDetalle: function () {
      return this.$refs[gridDetalle].instance.refresh();
    }
  },
  beforeMount() {
    this.CargarCuentas(true);
  },
};
</script>
<style>

/* Para DevExtreme (si usas dxDateBox o similar) */
.dx-calendar .dx-week-number-cell.dx-week-day-mar,
.dx-calendar .dx-week-number-cell.dx-week-day-mié {
    font-weight: normal !important;
}

/* O una solución más general */
.dx-calendar .dx-week-number-cell {
    font-weight: normal !important;
}

</style>
