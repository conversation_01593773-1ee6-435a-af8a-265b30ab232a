<template>
    <div>
        <vx-card title="Anulación de Recibos">
            <vs-divider border-style="solid" color="dark"></vs-divider>
            <div class="box-custom">
                <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                    <vs-row vs-justify="center">
                        <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="8">
                            <div class="flex flex-wrap-nowrap">
                                <div class="div-input">
                                    <ValidationProvider name="serie" rules="required|max:3" class="required" v-slot="{ errors }">
                                        <label class="typo_label label-static">Serie</label>
                                        <vs-input class="w-full input-serie" v-model="serie" autofocus :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"></vs-input>
                                    </ValidationProvider>
                                </div>
                                <div class="div-input">
                                    <ValidationProvider name="numero" rules="required|numero_entero" class="required" v-slot="{ errors }">
                                        <label class="typo_label label-static">Número</label>
                                        <vs-input type="number" class="w-full input-numero" v-model="numero" v-on:blur="NumeroRecibo()" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"></vs-input>
                                    </ValidationProvider>
                                </div>
                            </div>
                        </vs-col>
                    </vs-row>
                    <br>
                    <vs-row vs-justify="center">
                        <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="8">
                            <div class="div-input flex-wrap-nowrap">
                                <label class="label-static">A Nombre de:</label>
                                <input class="w-full label-info" v-model="nombreDe" disabled/>
                            </div>
                        </vs-col>
                    </vs-row>
                    <br>
                    <vs-row vs-justify="center">
                        <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="4">
                            <div class="div-input flex-wrap-nowrap">
                                <label class="label-static">Del: </label>
                                <input class="w-full label-info" v-model="del" disabled/>
                            </div>
                        </vs-col>
                        <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="4">
                            <div class="div-input flex-wrap-nowrap">
                                <label class="label-static">Por: </label>
                                <input class="w-full label-info" v-model="por" disabled/>
                            </div>
                        </vs-col>
                    </vs-row>
                    <br>
                    <vs-row vs-justify="center">
                        <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="8">
                            <div class="div-input">
                                <ValidationProvider name="razon" rules="required|max:40" class="required" v-slot="{ errors }">
                                    <label class="typo_label label-static">Razón</label>
                                    <vs-input class="w-full input-razon" v-model="razon" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="isRazon"></vs-input>
                                </ValidationProvider>
                            </div>
                        </vs-col>
                    </vs-row>
                    <br>
                    <vs-row vs-justify="center" class="w-full">
                        <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="8">
                            <div class="w-full flex flex-wrap-nowrap">
                                <div class="div-input">
                                    <vs-button @click="handleSubmit(GuardarAnulacion)" :disabled="invalid" color="success" class="w-full" icon-pack="fas" icon="fa-check-circle">OK</vs-button>
                                </div>
                            </div>        
                        </vs-col>
                    </vs-row>
                </ValidationObserver>
            </div>     
        </vx-card>  
    </div>
</template>
<script>   
    export default {
        data() {
            return{
                serie: '',
                numero: '',
                razon: '',
                nombreDe: '',
                del: '',
                por: '',
                codigoPeriodo: '',
                total: '',
                serieAdmision: '',
                admision: '',
                isRazon: true
            }
        },
        methods: {
            isEmptyObject(obj){
                return Object.keys(obj).length === 0;
            },
            NumeroRecibo(){
                this.axios.post("/app/v1_JefeCaja/ObtenerRecibo", {
                    "Opcion": "C",
                    "SubOpcion": "2",
                    "Hospital": "0",
                    "Empresa": this.$store.state.sesion.sesion_empresa,
                    "Periodo": this.codigoPeriodo,
                    "Serie": this.serie,
                    "Numero": this.numero
                })
                .then(resp => {
                    if(!this.isEmptyObject(resp.data.json)){
                        const { Nombre, Fecha, Total, Status, Tipo, SerieAdmision, Admision } = resp.data.json[0]
                        if(Tipo == 'R'){
                            this.$vs.dialog({
                                color: 'danger',
                                title: 'Anulación Recibo',
                                text: 'Recibo no se puede anular. Recibo corresponde a una devolución.',
                                acceptText: 'Aceptar'
                            })
                            return
                        }
                        if(Status == 'A'){
                            this.$vs.dialog({
                                color: 'danger',
                                title: 'Anulación Recibo',
                                text: 'El recibo ya fue anulado',
                                acceptText: 'Aceptar'
                            })
                            this.nombreDe = ''
                            this.del = ''
                            this.por = ''
                            this.Nombre = ''
                            this.total = ''
                            this.serieAdmision = ''
                            this.admision = ''
                            this.isRazon = true
                            this.razon = ''
                        }else{
                            this.axios.post("/app/v1_JefeCaja/ObtenerReciboDevolucion", {
                                "Opcion": "C",
                                "SubOpcion": "3",
                                "Hospital": "0",
                                "Empresa": this.$store.state.sesion.sesion_empresa,
                                "Serie": this.serie,
                                "Numero": this.numero
                            })
                            .then(resp => {
                                if(!this.isEmptyObject(resp.data.json)){
                                    const { SerieReciboDev, ReciboDev } = resp.data.json[0]
                                    this.$vs.dialog({
                                        color: 'danger',
                                        title: 'Anulación Recibo',
                                        text: `Recibo no se puede anular. Esta vinculado a la devolución ${SerieReciboDev} - ${ReciboDev}`,
                                        acceptText: 'Aceptar'
                                    })
                                    this.nombreDe = ''
                                    this.del = ''
                                    this.por = ''
                                    this.Nombre = ''
                                    this.total = ''
                                    this.serieAdmision = ''
                                    this.admision = ''
                                    this.isRazon = true
                                    this.razon = ''
                                }else{
                                    this.axios.post("/app/v1_JefeCaja/ObtenerAjenosPago", {
                                        "Opcion": "C",
                                        "SubOpcion": "4",
                                        "Hospital": "0",
                                        "Empresa": this.$store.state.sesion.sesion_empresa,
                                        "Serie": this.serie,
                                        "Numero": this.numero
                                    })
                                    .then(resp => {
                                        const { Cantidad } = resp.data.json[0]
                                        if(Cantidad > 0){
                                            this.$vs.dialog({
                                                color: 'danger',
                                                title: 'Anulación Recibo',
                                                text: 'Los ajenos cubiertos con este recibo ya fueron liberados, no puede anularlo',
                                                acceptText: 'Aceptar'
                                            })
                                            this.nombreDe = ''
                                            this.del = ''
                                            this.por = ''
                                            this.Nombre = ''
                                            this.total = ''
                                            this.serieAdmision = ''
                                            this.admision = ''
                                            this.isRazon = true
                                            this.razon = ''
                                        }else{
                                            this.nombreDe = Nombre
                                            this.del = Fecha
                                            this.por = `Q. ${Total}`
                                            this.Nombre = Nombre
                                            this.total = Total
                                            this.serieAdmision = SerieAdmision
                                            this.admision = Admision
                                            this.isRazon = false
                                        }
                                    })
                                }
                            })  
                        }
                    }else{
                        this.$vs.dialog({
                            color: 'danger',
                            title: 'Anulación Recibo',
                            text: 'El Recibo pertenece a un período ilegal para la anulación',
                            acceptText: 'Aceptar'
                        })
                        this.nombreDe = ''
                        this.del = ''
                        this.por = ''
                        this.Nombre = ''
                        this.total = ''
                        this.serieAdmision = ''
                        this.admision = ''
                        this.isRazon = true
                        this.razon = ''
                    }    
                })
            },
            CodigoPeriodo(){
                this.axios.post("/app/v1_JefeCaja/ObtenerCodigoPeriodo", {
                    "Opcion": "C",
                    "SubOpcion": "1",
                    "Hospital": "0",
                    "Empresa": this.$store.state.sesion.sesion_empresa
                })
                .then(resp => {
                    if(!this.isEmptyObject(resp.data.json)){
                        const {Codigo} = resp.data.json[0]
                        this.codigoPeriodo = Codigo
                    }else{
                        this.codigoPeriodo = ''
                    }
                })
            },
            GuardarAnulacion(){

                this.axios.post("/app/v1_JefeCaja/GuardarAnulacion", {
                    "Opcion": "I",
                    "SubOpcion": "1",
                    "Hospital": "0",
                    "Empresa": this.$store.state.sesion.sesion_empresa,
                    "Serie": this.serie,
                    "Numero": this.numero,
                    "Total": this.total,
                    "SerieAdmision": this.serieAdmision,
                    "Admision": this.admision,
                    "Usuario": this.$store.state.sesion.corporativo,
                    "Razon": this.razon

                })
                .then(resp => {
                    if(resp.data.json[0].tipo_error == '0'){
                        this.$vs.dialog({
                            color: "success",
                            title: "Anulación Recibo",
                            text: resp.data.json[0].descripcion,
                            acceptText: 'Aceptar'
                        })
                        this.nombreDe = ''
                        this.del = ''
                        this.por = ''
                        this.Nombre = ''
                        this.total = ''
                        this.serieAdmision = ''
                        this.admision = ''
                        this.isRazon = true
                        this.razon = ''
                    }else{
                        this.$vs.dialog({
                            color: "danger",
                            title: "Anulación Recibo",
                            text: resp.data.json[0].descripcion,
                            acceptText: 'Aceptar'
                        })
                    }
                })
            }
        },
        activated(){
            this.CodigoPeriodo()
        },
        deactivated(){
            this.codigoPeriodo = ''
        }
    }

</script>
<style lang="scss" scoped>
    .box-custom{
        box-shadow: 0px 15px 40px -10px;
        padding: 15px;
    }
    .div-input{
        padding: 10px;
        .input-serie{
            width: 75px;
        }
        .input-numero{
            width: 200px;
        }
        .input-razon{
            width: 500px;
        }
    }
    .label-info{
        margin-left: 10px;
        color:#E74C3C;
        opacity: 0.7;
        border: 0;
        font-size: 14px;
        background-color: white;
    }
    .label-static{
        font-weight: bold;
    }
</style>