/*******************************************************************************************************************/
/****************************** CREACION DE PAQUETE spHisCargosHabitacion ******************************************/
/*******************************************************************************************************************/
USE [HOSPITAL]
GO

/****** Object:  StoredProcedure [dbo].[spHisCargosHabitacion]    Script Date: 17/04/2023 15:10:13 p.m. ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [dbo].[spHisCargosHabitacion]
(					@IOpcion varchar(10) = 'CONSULTA',
					--ADMISION/CARGOS/PRODUCTOS
					@ISubOpcion varchar(10) = 'ADMISION',
					@ISerieAdmision char(1) = null,
					@INumeroAdmision int = null,
					@IEmpresaUnificadora varchar(3) = 'MED',
					@IEmpresaReal char(3) = 'MED',
					@ISucursal char(3) = 'HLA',
					@IActiva int = 1,
					@IConHabitacion int = 1,				
					@IValidarFechaCierreSiniestralidad int = 1,
					@ICategoriaProducto char(2) = '24',
					--PARAMETROS PARA BUSQUEDA DE PRODUCTOS
					@INivelPrecioPaciente tinyint = null,
					@ICategoria int = null,
					@ICategoriaNombre varchar(50) = null,
					@ICodigo varchar(50)= NULL,
					@INombre varchar(50) = NULL,					
					@IActivo char(1) = 'S',
					--PARAMETROS PARA MODIFICAR CARGOS
					@ICargosModificados varchar(max) = null,
					@ICorporativo int = null,
					@IRazon varchar(100) = null
)
AS
BEGIN
	DECLARE @ErrRaise VARCHAR(99) = '',
					@ErrRaiseNum Int = 0,
					@Existe INT = 0,
					@WCargosFacturados INT = 0,
					@WOrdenConCierreSiniestralidad INT = 0,
					@WIndiceProducto TINYINT = 0,
					@WPaciente INT = 0,
					@WProducto varchar(max);
	
	BEGIN TRAN
		BEGIN TRY
			IF @IOpcion = 'CONSULTA'
			BEGIN
				IF @ISubOpcion = 'ADMISION'
				BEGIN
				 	 select @WCargosFacturados = count(*) 
					   from Cargos 
						where Empresa = @IEmpresaUnificadora 
							and TipoOrden = 'AD'+@ISerieAdmision
							and SerieAdmision = @ISerieAdmision
							and Admision = @INumeroAdmision
							and FacRec is not null
						
					 IF @WCargosFacturados >0 
					 BEGIN
						SELECT 0 AS codigo, 'La admisión ya tiene cargos a la habitación facturados, no se puede modificar' AS descripcion, 1 AS tipo_error;
					 END

					 SELECT @WOrdenConCierreSiniestralidad = CierreSiniestralidad
						 FROM Ordenes
						WHERE Empresa = @IEmpresaUnificadora and Tipo = 'AD'+@ISerieAdmision and Codigo = @INumeroAdmision
							and ISNULL(CierreSiniestralidad, 0) > 0

					IF @WOrdenConCierreSiniestralidad >0 
					 BEGIN
						SELECT 0 AS codigo, 'Encamamiento con Siniestralidad, NO puede modificarla' AS descripcion, 1 AS tipo_error;
					 END

					SELECT top 100  a.Serie,
													a.Codigo,
													a.Entrada,
													a.Salida,
													a.NivelPrecios,
													a.Habitacion,
													rtrim(concat(rtrim(p.Nombre), ' ', rtrim(p.Apellido), ' ', p.ApellidoCasada )) Paciente,
													concat(rtrim(a.Serie),'-',a.Codigo) as Admision,
													(select count(*) from Cargos 
													  where Empresa = @IEmpresaUnificadora 
														  and TipoOrden = 'AD'+@ISerieAdmision
															and SerieAdmision = @ISerieAdmision
															and Admision = @INumeroAdmision
															and Categoria = @ICategoriaProducto ) NumeroCargos
					  FROM admisiones a left join pacientes p on (a.paciente = p.codigo and a.empresa = p.empresa) 
					 WHERE a.empresa = @IEmpresaUnificadora 
					  and a.Serie = @ISerieAdmision
						and a.Codigo = @INumeroAdmision
					  and (@IActiva = 0 or (a.Salida is null and a.FechaPreEgreso is null))
						and (@IConHabitacion = 0 or (a.Habitacion is not null AND replace(a.Habitacion,' ','') != '' ))
						and (@IValidarFechaCierreSiniestralidad = 0 or a.FechaCierreSiniestralidad is null)

				END
				ELSE IF @ISubOpcion = 'CARGOS'
				BEGIN
				 select c.TipoOrden,c.Orden,c.Linea,c.Producto,p.Nombre,c.Categoria,p.Tipo,
								Convert(INT,c.Cantidad) Cantidad,
								Convert(DECIMAL(10,2),c.PrecioUnitario) PrecioUnitario,
								Convert(DECIMAL(10,2),c.Valor) Valor,
								Convert(DECIMAL(10,2),c.Costo) Costo,
								c.UnidadMedida, c.Factor, c.SubNivelPrecios SubNivel
				   from Cargos c left join inventario..productos p on (c.Empresa = p.Empresa and c.Producto=p.Codigo)
					where c.Empresa = @IEmpresaUnificadora 
						and c.TipoOrden = 'AD'+@ISerieAdmision
						and c.SerieAdmision = @ISerieAdmision
						and c.Admision = @INumeroAdmision
						and c.Categoria = @ICategoriaProducto										
					/*
					pp.UnidadMedida, Convert(DECIMAL(10,2),pp.Precio), pp.Factor, pp.SubNivel
					*/
				END		
				ELSE IF @ISubOpcion = 'PRODUCTOS'
				BEGIN
						SELECT TOP 100
							p.Categoria,
							c.Nombre CategoriaNombre,
							p.Codigo,
							p.Nombre,
							CASE WHEN p.Costeo = 'U' THEN Convert(DECIMAL(10,2),p.CostoUltimo) ELSE Convert(DECIMAL(10,2),p.CostoPromedio) END Costo,
							pp.UnidadMedida, 
							Convert(DECIMAL(10,2),pp.Precio) PrecioVenta, 
							pp.Factor, 
							pp.SubNivel
						FROM
							INVENTARIO.dbo.Productos p (NOLOCK) 
							INNER JOIN 	Categorias c (NOLOCK) on
									(p.Categoria = c.Codigo and p.Empresa  = c.Empresa )
							INNER JOIN INVENTARIO.dbo.ProductosPrecios pp (NOLOCK) on
									( p.Empresa = pp.Empresa and p.codigo = pp.Producto and pp.SubNivel <= p.UnidadVenta)
							INNER JOIN (SELECT  pp.Empresa, pp.Producto, min(pp.nivel) Nivel						
														FROM  INVENTARIO.dbo.Productos p (NOLOCK) 
																	INNER JOIN 	Categorias c (NOLOCK) on
																			(p.Categoria = c.Codigo and p.Empresa  = c.Empresa )
																	INNER JOIN INVENTARIO.dbo.ProductosPrecios pp (NOLOCK) on
																			( p.Empresa = pp.Empresa and p.codigo = pp.Producto and pp.SubNivel <= p.UnidadVenta)							
																WHERE 
																(@ICodigo is null or  concat(rtrim(p.Codigo),' ') like '%' + ltrim(rtrim(@ICodigo)) +'%')
																AND (@INombre is null or  concat(rtrim(p.Nombre),' ',rtrim(p.NombreGenerico),', ',rtrim(p.Descripcion)) like '%' + ltrim(rtrim(@INombre)) +'%')
																AND (@ICategoria is null or p.Categoria = @ICategoria)
																AND (@ICategoriaNombre is null or c.Nombre like '%' + @ICategoriaNombre + '%')						
																AND p.Empresa = @IEmpresaUnificadora
																AND p.Activo = @IActivo
																AND pp.Nivel = @INivelPrecioPaciente
															GROUP BY pp.Empresa,pp.Producto
														) FiltroNivel on (pp.Empresa = FiltroNivel.Empresa and pp.Producto = FiltroNivel.Producto and pp.Nivel = FiltroNivel.Nivel)
						WHERE 
						(@ICodigo is null or  concat(rtrim(p.Codigo),' ') like '%' + ltrim(rtrim(@ICodigo)) +'%')
						AND (@INombre is null or  concat(rtrim(p.Nombre),' ',rtrim(p.NombreGenerico),', ',rtrim(p.Descripcion)) like '%' + ltrim(rtrim(@INombre)) +'%')
						AND (@ICategoria is null or p.Categoria = @ICategoria)
						AND (@ICategoriaNombre is null or c.Nombre like '%' + @ICategoriaNombre + '%')						
						AND p.Empresa = @IEmpresaUnificadora
						AND p.Activo = @IActivo
						AND pp.Nivel = @INivelPrecioPaciente
						ORDER BY 
						p.Categoria,p.Codigo 
				END
				ELSE
				BEGIN
					SELECT 0 AS codigo, 'No se ha encontrado la Subopcion' AS descripcion, 1 AS tipo_error;
				END	
			END
			ELSE IF @IOpcion = 'INSERCCION'
			BEGIN
				IF @ISubOpcion = 'CARGOS'
				BEGIN
					DELETE FROM CARGOS 
					 WHERE  Empresa = @IEmpresaUnificadora 
						 and TipoOrden = 'AD'+@ISerieAdmision
						 and SerieAdmision = @ISerieAdmision
						 and Admision = @INumeroAdmision
						 and Categoria = @ICategoriaProducto	
					
					SELECT keyCount linea, item producto
						INTO #PRODUCTOS
						FROM Contadb..SplitStringIndex(@ICargosModificados,';')
					
					SELECT @WIndiceProducto = CONVERT(tinyint,min(linea))
						FROM #PRODUCTOS

					WHILE @WIndiceProducto <> 0
						 BEGIN

							 select @WProducto = producto
								 from #PRODUCTOS
								where linea = @WIndiceProducto
							
							IF OBJECT_ID('tempdb..#ListaProductos') IS NOT NULL 
								BEGIN
											INSERT INTO #ListaProductos
											select @WIndiceProducto Linea,
														[1] Codigo,[2] Cantidad,[3] Precio,[4] Valor,[5] UnidadMedida,
														[6] Factor,[7] SubNivel,[8] Costo							
												from
											(
											select  keyCount linea, item valor
												from Contadb..SplitStringIndex(@WProducto,',')
											)
											inicio pivot
											(
												max(valor)
												for linea in ([1],[2],[3],[4],[5],[6],[7],[8])
											) pivottable
								END
								ELSE
								BEGIN
											select @WIndiceProducto Linea,
														[1] Codigo,[2] Cantidad,[3] Precio,[4] Valor,[5] UnidadMedida,
														[6] Factor,[7] SubNivel,[8] Costo			
												INTO #ListaProductos
												from
											(
											select item linea, keyCount valor
												from Contadb..SplitStringIndex(@WProducto,',')
											)
											inicio pivot
											(
												max(linea)
												for valor in ([1],[2],[3],[4],[5],[6],[7],[8])
											) pivottable
								END


							 select @WIndiceProducto = CONVERT(tinyint,min(linea))
								 from #PRODUCTOS
								where linea > @WIndiceProducto
						 END

					
					INSERT INTO Modificaciones(Empresa, Tipo, Serie, Documento, Fecha, Usuario, Observaciones)
						VALUES (@IEmpresaUnificadora, 'O', CONCAT('AD',@ISerieAdmision), @INumeroAdmision, GetDate(), @ICorporativo, @IRazon)

					
					INSERT INTO CARGOS(Empresa,TipoOrden,Orden,Linea,SerieAdmision,Admision,Fecha,
														 Producto,Categoria,Valor,Cantidad,Status,Paciente,
														 PrecioUnitario,SubNivelPrecios,Factor,UnidadMedida,Costo, EmpresaReal, EmpresaUnif,CostoHospital)
					SELECT @IEmpresaUnificadora, CONCAT('AD',@ISerieAdmision), @INumeroAdmision, Linea, @ISerieAdmision, @INumeroAdmision,GetDate(), 
								 Codigo, @ICategoriaProducto, Valor,Cantidad,'H', @WPaciente,
								 Precio,SubNivel,Factor,UnidadMedida,Costo,@IEmpresaReal,@IEmpresaUnificadora,Costo
						FROM #ListaProductos
				
					IF OBJECT_ID('tempdb..#ListaProductos') IS NOT NULL 
							DROP TABLE #ListaProductos

					IF OBJECT_ID('tempdb..#PRODUCTOS') IS NOT NULL 
							DROP TABLE #PRODUCTOS
				END
				ELSE
				BEGIN
					SELECT 0 AS codigo, 'No se ha encontrado la Subopcion' AS descripcion, 1 AS tipo_error;
				END	
			END
			ELSE
			BEGIN
				SELECT 0 AS codigo, 'No se ha encontrado la Opcion' AS descripcion, 1 AS tipo_error;
			END	
			COMMIT TRAN
		END TRY
		BEGIN CATCH
			ROLLBACK TRAN
			SET @ErrRaise = ERROR_MESSAGE()
			SET @ErrRaiseNum = ERROR_NUMBER()
			RAISERROR(@ErrRaise, 16, @ErrRaiseNum)
		END CATCH 
END
GO
/*******************************************************************************************************************/
/****************************** PERMISOS DE PAQUETE spHisCargosHabitacion ******************************************/
/*******************************************************************************************************************/

USE HOSPITAL
GO

GRANT EXECUTE ON spHisCargosHabitacion TO USRHISJEFECAJA  
GO

USE INVENTARIO
GO

GRANT SELECT  ON  ProductosPrecios  TO USRHISJEFECAJA
GO

USE HOSPITAL
GO
GRANT SELECT ON HIS_Honorarios TO USRHISJEFECAJA
GO
