<template>
    <vx-card title="Listado de Huellas">
        <div>
            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <label class="vs-input--label">Coorporativo </label>
                <vx-input-group class="">
                    <vs-input class="d-inline-block" v-model="coorporativo" :disabled="desactivar"/> 
                    <template slot="append">
                        <div class="append-text btn-addon">
                            <vs-button  @click="consulta" class="btn btn-outline-info" icon-pack="fas" icon="fa-search" ></vs-button>
                        </div> 
                    </template>
                </vx-input-group>
                <div>
                    <vs-radio class="radioReference" v-model="referencia" vs-value="1">Corporativo</vs-radio>
                    <vs-radio class="radioReference" v-model="referencia" vs-value="2">Ajenos</vs-radio>
                </div>
            </div>
            <div class="card">
                <vs-table2 :data="tablaHuellas" v-model="selected">
                    <template slot="thead">
                        <th width="100px">Nombre Completo</th>
                        <th width="100px">Dedo</th>
                        <th width="100px">Huella</th>
                        <th width="25px">Acciones</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data" :data="tr" :is-selected="selected == tr" >
                            <vs-td2 width="100px">
                                {{ tr.NombreCompleto }}
                            </vs-td2>
                            <vs-td2 width="100px">
                                {{ tr.NombreDedo }}
                            </vs-td2>
                            <vs-td2 width="100px">
                                <img :src="tr.imgHuella" alt="">
                                <!-- {{ tr.imgHuella }} -->
                            </vs-td2>
                            <vs-td2 class="th_eliminar">
                                <vs-button @click="borrarHuella(tr)" color="danger" size="small" icon-pack="fas" icon="fa-minus-circle" class="btn_eliminar mr-1"></vs-button>
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
        </div>
    </vx-card>
</template>

<script>

    export default {
        data() {
            return {
                coorporativo: null,
                referencia: 1,
                selected: null,
                tablaHuellas: [],
                desactivar: true

            }
        }, 
        props: {
            tipoHuella: {
                type: String,
                default: "1"
            },
            corporativoBusqueda: {
                type: String
            },
            desctivarBusqueda: {
                type: Boolean,
                default: true
            }
        },
        methods: {
            async consulta(){
                if(this.referencia == '1'){
                    return this.axios.post('/app/huella/ConsultaUsuarioHuella', { 
                        Opcion: 'C', 
                        SubOpcion: '7', 
                        Hospital: '0', 
                        Corporativo: this.coorporativo,
                        Tipo: this.referencia
                    })
                    .then( resp => {
                        this.tablaHuellas = resp.data.json.map( m => {
                            return {
                                NombreCompleto: m.NombreCompleto,
                                NombreDedo: m.NombreDedo,
                                imgHuella: `data:image/png;base64,${m.ImagenHuella}`
                            }
                        })
                    })
                }else{
                    return this.axios.post('/app/huella/ConsultaUsuarioHuella', { 
                        Opcion: 'C', 
                        SubOpcion: '11', 
                        Hospital: '0', 
                        Corporativo: this.coorporativo,
                        Tipo: this.referencia
                    })
                    .then( resp => {
                        this.tablaHuellas = resp.data.json.map( m => {
                            return {
                                NombreCompleto: m.NombreCompleto,
                                NombreDedo: m.NombreDedo,
                                imgHuella: `data:image/png;base64,${m.ImagenHuella}`
                            }
                        })
                    })
                }
                
            },
            borrarHuella(item) {
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',
                    text: `¿Desea eliminar la huella: ${item.NombreDedo}?`,
                    acceptText: 'Aceptar',
                    accept: () => {
                        this.axios.post('/app/huella/BorrarHuella',{
                            Opcion: 'A',
                            SubOpcion: '5',
                            Hospital: '0',
                            Corporativo: this.coorporativo,
                            NombreDedo: item.NombreDedo
                        })
                        .then( () => {
                            this.consulta()
                        })
                    }
                })
            }
        },
        created() {
            this.referencia = this.tipoHuella
            this.coorporativo = this.corporativoBusqueda
            this.consulta()
        },
    }

</script>

<style lang="scss" scoped>
    .th_eliminar {
        text-align: center;
        .btn_eliminar{
            display: inline-block;
        }
    }
    .radioReference{
        padding: 5px 5px 5px 5px;
    }
    img{
        width: 50px;
        height: 50px;
    }
</style>