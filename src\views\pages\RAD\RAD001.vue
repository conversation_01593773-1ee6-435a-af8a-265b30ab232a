<template>
    <vx-card title="Informe Diagnóstico">
    
        <!-- buscadores -->
        <buscador ref="buscar_radiologias" buscador_titulo="Buscador / Radiología" :api="'app/radiologia/RadBusquedaOrdenes'" :campos="['Apellido','Nombre','Orden','Admision','Fecha','Nacimiento']" :titulos="['Apellidos','Nombres','Orden','Admisión','Fecha#','Nacimiento#']" :titulos_mascara="{'Orden':'AAX-#########','Admisión':'X##########'}" :titulos_grupos="[['Apellidos','Nombres'],['Orden','Admisión']]" :multiselect="false" :api_validar_campo="true" :api_preload="false" />
    
        <buscador ref="buscar_especialista" buscador_titulo="Buscador / Especialista" :api="'app/radiologia/RadBusquedaradiologos'" :campos="['Codigo','ApellidoRad','NombreRad']" :titulos="['Especialista','Apellidos','Nombres']" :multiselect="false" :api_validar_campo="true" />
    
        <buscador ref="buscar_plantillas" buscador_titulo="Buscador / Radiología" :api="'app/radiologia/RadBusquedaPlantillas'" :api_filtro="{producto
            :info.opcion.examen_codigo, ajeno:info.opcion.especialista_codigo}" :campos="['Tipo','TitutoInforme']" :titulos="['Tipo','Nombre']" :multiselect="false" :api_validar_campo="true" />
    
        <!-- reportes -->
        <vs-popup id="contentreport" classContent="popup-generar" title="Informe" :active.sync="info.reporte" fullscreen style="z-index:99998;height:100%">
            <embed v-if="info.reporte_src!=''" type="application/pdf" :src="info.reporte_src" ref="pdfDocument" width="100%" height="98%" />
        </vs-popup>
    
        <!-- Listado de imagenes -->
        <vs-popup id="contentreport" classContent="popup-generar" title="Imagenes" :active.sync="info.mostrar_imagenes" fullscreen style="z-index:99998;height:100%">
    
            <div v-if="!info.mostart_imagenes_carga" style="padding:10px;text-align:center">
                No se han encontrado imagenes
            </div>
            <div class="flex flex-wrap archivos" v-else>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5" v-for="(i,index) in listado_imagenes" :key="index">
    
                    <expandable-image class="image" :src="'data:image/png;base64, ' +i.src" v-if="i.archivo_nombre">
                    </expandable-image>
                    <!-- <vs-button color="danger" :id="'scanner_'+index" class="icon-eliminar" :disabled="i.archivo_nombre==null" v-on:click="remove_scanner(index)" icon-pack="fas" icon="fa-trash" v-if="i.archivo_nombre"> -->
                    <!-- </vs-button> -->
                </div>
            </div>
        </vs-popup>
    
        <vs-popup id="contentreport" title="Editar Médico" :active.sync="info.editarmedico" style="z-index:99998;">
            <div class="flex flex-wrap">
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <label class="vs-input--label">Médico</label>
                    <vx-input-group class="">
                        <vs-input v-model="info.opcion.medico_codigo" @blur="buscar_medico_auto()" />
                        <template slot="append">
                            <div class="append-text btn-addon">
                                <vs-button color="primary" icon-pack="feather" @click="buscar_medico()" icon="icon-search"></vs-button>
                            </div>
                        </template>
                    </vx-input-group>
                </div>
                <div class="w-full md:w-4/5 lg:w-4/5 xl:w-4/5">
                    <vs-input label="Nombre Especialista" class="w-full" v-model="info.opcion.medico_nombre" :disabled="true" />
                </div>
            </div>
    
            <vs-divider></vs-divider>
            <div style="text-align:right;margin:5px 0;padding:5px ">
                <vs-button right @click="guardar_medico()">Guardar</vs-button>
            </div>
        </vs-popup>
    
        <vs-popup id="contentreport" title="Editar Edad" :active.sync="info.editaredad" style="z-index:99998;">
            <div class="flex flex-wrap">
                <div class="w-full md:w-5/5 lg:w-5/5 xl:w-5/5">
                    <datepicker :inline="true" :language="languages['es']" v-model="info.edad" :format="info.edadFormato"></datepicker>
                </div>
            </div>
            <vs-divider></vs-divider>
            <div style="text-align:right;margin:5px 0;padding:5px ">
                <vs-button right @click="guardar_edad()">Guardar</vs-button>
            </div>
        </vs-popup>
    
        <form>
            <vs-divider>Orden</vs-divider>
            <form v-on:submit.prevent="cargar_orden()">
                <div class="flex flex-wrap">
                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                        <vs-input label="Tipo" class="w-full" v-on:change="info.orden_tipo=info.orden_tipo.toUpperCase()" v-model="info.orden_tipo" :disabled="bloqueoBusqueda" />
                    </div>
                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                        <!-- <vs-input label="Número" class="w-full" type="number" v-model="info.orden_numero" /> -->
                        <label class="vs-input--label">Número</label>
                        <vx-input-group class="">
                            <vs-input v-model="info.orden_numero" :disabled="bloqueoBusqueda" @change="cargar_orden()" />
                            <template slot="append">
                                <div class="append-text btn-addon">
    
                                    <button type="submit" v-show="false" name="button"></button>
                                    <vs-button id="button-with-loading-1" color="primary" icon-pack="feather" @click="buscar_radiologias()" icon="icon-search" v-if="bloqueoBusqueda==false"></vs-button>
                                    <vs-button id="button-with-loading-2" color="danger" icon-pack="feather" @click="limpiar_orden()" icon="icon-x" v-else></vs-button>
                                    <vs-button style="margin-left:1px" v-on:click="radiologia_reporte(0)" v-if="bloqueoBusqueda" icon-pack="fas" icon="fa-print"></vs-button>
    
                                    <vs-button v-if="info.orden_numero >  0 " id="button-with-loading-3" color="warning" icon-pack="fas" @click="HabilitarEnvio(0)" icon="fa-envelope"></vs-button>
                                    <!--
                                    <vs-button v-if="info.orden_numero >  0 " id="button-with-loading-4" color="#37474F" @click="radiologia_jpg()" icon-pack="fa" icon="fa-images"></vs-button>
                                    <a :href="'http://srvpacs032en/synapse'" target="_blank">
                                        <vs-button v-if="info.orden_numero >  0 " id="button-with-loading-5" color="#2E7D32" icon-pack="fas" icon="fa-home"></vs-button>
                                    </a> 
                                -->
                                </div>
                            </template>
                        </vx-input-group>
                    </div>
                    <div>
                        <label>Titulos en Inglés</label>
                        <br>
                        <vs-checkbox style="padding-left: 35px; padding-top: 7px;" v-model="info.idioma"></vs-checkbox>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
    
                        <td width="100px pt-5" style="padding:0px 10px">
                            <!-- <a :href="'http://srvpacs032en/Synapse/WebQuery/Index?path=/All%20Studies/&filter=accessionnumber='+{{  }}+''-156425'" target="_blank"></a> -->
                            <a :href="'http://srvpacs032en/Synapse/WebQuery/Index?path=/All%20Studies/&filter=accessionnumber='+ info.orden_tipo + '-'+  info.orden_numero" target="_blank">
                                <vs-button color="#37474F" class="mt-5" icon-pack="fas" v-if="info.orden_numero >  0" icon="fa-images">DICOM</vs-button>
                            </a>
                        </td>
                        <td v-if="Permisos.VER_IMAGEN" width="100px pt-5" style="padding:0px 10px">
                            <vs-button color="success" class="mt-5" icon-pack="fas" v-if="info.orden_numero >  0   " @click.native="radiologia_jpg()" icon="fa-download">Imágenes</vs-button>
                        </td>
                        <td v-if="Permisos.SUBIR_IMAGEN" width="100px pt-5" style="padding:0px 10px">
                            <vs-button color="success" class="mt-5" icon-pack="fas" v-if="info.orden_numero >  0  " @click.native="radiologia_jpgSubir()" icon="fa-upload">Subir</vs-button>
                        </td>
    
                    </div>
                </div>
            </form>
            <div class="flex flex-wrap">
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <vs-input label="Admisión" class="w-full" v-model="info.admision" disabled />
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <vs-input label="Cód. Paciente" class="w-full" v-model="info.paciente_codigo" disabled />
                </div>
                <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                    <vs-input label="Nombre del paciente" class="w-full" v-model="info.paciente_nombre" disabled />
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <label class="vs-input--label">Edad</label>
                    <vx-input-group class="">
    
                        <vs-input class="w-full" v-model="info.paciente_edad" disabled />
                        <!-- {{info}} -->
                        <template slot="append">
                            <div class="append-text btn-addon">
                                <vs-button v-if="$validar_privilegio('EDITAREDAD').status && bloqueoBusqueda" id="button-with-loading-3" color="primary" icon-pack="feather" @click="editar_edad()" icon="icon-edit"></vs-button>
                            </div>
                        </template>
                    </vx-input-group>
                </div>
            </div>
            <div class="flex flex-wrap">
                <div class="w-full">
                    <label class="vs-input--label">Refiere</label>
                    <vx-input-group class="">
    
                        <vs-input class="w-full" v-model="info.refiere" disabled />
                        <!-- {{info}} -->
                        <template slot="append">
                            <div class="append-text btn-addon">
                                <vs-button v-if="$validar_privilegio('EDITARMEDICO').status && bloqueoBusqueda" id="button-with-loading-4" color="primary" icon-pack="feather" @click="editar_medico_orden()" icon="icon-edit"></vs-button>
                            </div>
                        </template>
                    </vx-input-group>
                </div>
            </div>
    
            <vs-divider>Estudio</vs-divider>
            <vs-table :data="examenes" v-if="examenes.length>0">
                <template slot="thead">
                    <vs-th>Examen</vs-th>
                    <vs-th>Especialista</vs-th>
                    <vs-th>Estado</vs-th>
                    <vs-th width="150px">Acciones</vs-th>
                </template>
                <template slot-scope="{data}">
    
                    <vs-tr :key="indextr" v-for="(tr, indextr) in data">
                        <vs-td :data="tr.examen_nombre" edit style="padding:0">
                            <div @click="opciones(tr)" style="padding:10px;cursor:pointer;">
                                [{{ tr.examen_codigo }}] {{ tr.examen_nombre }}
                            </div>
                        </vs-td>
                        <vs-td :data="tr.especialista_nombre">
                            <span v-if="tr.especialista_codigo">[{{ tr.especialista_codigo }}] {{ tr.especialista_nombre }}</span>
                        </vs-td>
                        <vs-td>
                            <vx-tooltip :text="(tr.congelado==false)?'Descongelado':'Congelado'" style="display:inline-block;text-align:center;margin-right:2px;width:20px" position="bottom">
                                <i v-if="tr.congelado" style="color:#2ecc71" class="fas fa-lock"></i>
                                <i v-else style="color:#e74c3c" class="fas fa-lock-open"></i>
                            </vx-tooltip>
                            <vx-tooltip :text="(tr.sms==false)?'Entrega Personalmente':'Envío SMS'" style="display:inline-block;text-align:center;margin-right:2px;width:20px" position="bottom">
                                <i v-if="tr.sms" style="color:#2ecc71" class="fas fa-comment"></i>
                                <i v-else style="color:#e74c3c" class="fas fa-comment-slash"></i>
                            </vx-tooltip>
                        </vs-td>
    
                        <vs-td>
                            <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" @click="opciones(tr)"></vs-button>
                            <vs-button style="margin-left:1px;display:inline-block;" :disabled="!tr.congelado" color="dark" v-on:click="radiologia_reporte(tr.examen_linea)" v-if="bloqueoBusqueda" icon-pack="fas" icon="fa-print"></vs-button>
                            <vs-button style="margin-left:1px;display:inline-block;" color="warning" v-on:click="HabilitarEnvio(tr.examen_linea)" icon-pack="fas" icon="fa-envelope"></vs-button>
    
                        </vs-td>
                    </vs-tr>
                </template>
            </vs-table>
    
            <!-- ========================================================================================================================================================== -->
            <!-- POPUP -->
            <!-- ========================================================================================================================================================== -->
            <vs-popup classContent="popup-example" :title="'['+info.opcion.examen_codigo+'] ' + info.opcion.examen_nombre" :active.sync="info.opcion.show">
                <table>
                    <tr>
                        <td>
                            <vx-tooltip text="Congelar Informe" position="bottom">
                                <label>Congelar/Descongelar</label>
                                <vs-switch color="success" v-model="info.opcion.congelado" icon-pack="feather" vs-icon="icon-lock" v-on:click="confirmacion_congelar" />
                            </vx-tooltip>
                        </td>
                        <td>
                            <vx-tooltip text="Enviar por SMS" position="bottom">
                                <label>Envíar SMS</label>
                                <vs-switch color="success" v-model="info.opcion.sms" icon-pack="feather" vs-icon="icon-message-square" v-on:click="radiologia_mensaje" :disabled="bloqueoCampos" />
                            </vx-tooltip>
                        </td>
                    </tr>
                </table>
                <vs-divider></vs-divider>
                <div style="height:calc(100vh - 300px);overflow:auto">
    
                    <div style="padding:10px;border-radius:5px; border:1px solid #ccc;">
                        <div class="flex flex-wrap">
                            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                                <label class="vs-input--label">Especialista</label>
                                <vx-input-group class="">
                                    <vs-input v-model="info.opcion.especialista_codigo" :disabled="bloqueoCampos" @blur="buscar_especialista_auto()" />
                                    <template slot="append">
                                        <div class="append-text btn-addon">
                                            <vs-button color="primary" icon-pack="feather" :disabled="bloqueoCampos" @click="(!bloqueoCampos)?buscar_especialista():null" icon="icon-search"></vs-button>
                                        </div>
                                    </template>
                                </vx-input-group>
                            </div>
                            <div class="w-full md:w-4/5 lg:w-4/5 xl:w-4/5">
                                <vs-input label="Nombre Especialista" class="w-full" v-model="info.opcion.especialista_nombre" :disabled="true" />
                            </div>
                            <br>
                            <div class="w-full">
                                <vs-input label="Nombre Estudio" class="w-full" v-model="info.opcion.examen_nombre" :disabled="bloqueoCampos" />
                            </div>
                        </div>
    
                        <div class="flex flex-wrap">
                            <label class="vs-input--label">Observaciones</label>
                            <div style="background-color:#FAFAFA;width:100%;min-height:40px;color:#90A4AE;border:1px solid #bdc3c7;padding:5px;margin-bottom:5px;border-radius:5px" label="Observaciones" v-html="info.observaciones"></div>
                        </div>
                        <div class="flex flex-wrap">
                            <label class="vs-input--label">Región, Indicaciones u Observaciones</label>
                            <div style="background-color:#FAFAFA;width:100%;min-height:40px;color:#90A4AE;border:1px solid #bdc3c7;padding:5px;margin-bottom:5px;border-radius:5px" label="Observaciones" v-html="info.opcion.RegionObservaciones"></div>
                        </div>                        
                        <!-- <vs-divider>Informe</vs-divider> -->

                        
                        <vs-button color="primary" icon-pack="feather" @click="buscar_plantilla()" icon="icon-search" v-if="bloqueoCampos==false && !info.opcion.informeAdendum">Plantillas</vs-button>
                        <vue-editor v-model="info.opcion.informe" :editorToolbar="customToolbar" :disabled="bloqueoCampos || (!bloqueoCampos && info.opcion.informeAdendum)"></vue-editor>
                        <vs-row class="p-2" v-if="!info.opcion.informeAdendum == false"><label style="font-weight: bold; color: black; font-size: 14pt;">ADENDUM:</label></vs-row>
                        <vue-editor v-if="!info.opcion.informeAdendum == false"  v-model="info.opcion.adendum" :editorToolbar="customToolbar" :disabled="bloqueoCampos" ></vue-editor>
                    </div>
                    <!-- {{info}} -->
    
                </div>
                <vs-divider></vs-divider>
                <vs-button style="float:right" v-if="!bloqueoCampos" @click="guardar_informe(true)"> Guardar</vs-button>
                <vs-button color="primary" style="float:right" type="border" @click="info.opcion.show=false" v-else> Salir</vs-button>
                <div style="clear:both"></div>
                <!-- <br> -->
            </vs-popup>
    
            <!-------------------------------INICIO PARA ENVIO DE RESULTADOS-->
            <vs-popup classContent="popup-example" title="Enviar Resultado" :active.sync="HabilitarVentanaCorreo">
                <ValidationObserver ref="formValidate" v-slot="{ handleSubmit }" mode="lazy">
                    <form @submit.prevent="handleSubmit(submitForm(reporte.opciones))">
                        <div class="flex flex-wrap">
                            <vs-row>
    
                                <vs-col vs-type="flex" vs-w="6">
                                    <div class="w-4/5  p-2">
                                        <validationProvider name="Destinatario">
                                            <vs-input v-model="datosResultado.EnviarA" label="Destinatario" class="w-full" />
                                        </validationProvider>
                                    </div>
                                </vs-col>
    
                                <vs-col vs-type="flex" vs-w="6">
                                    <div class="w-4/5  p-2">
                                        <validationProvider name="Copiar a">
                                            <vs-input v-model="datosResultado.CopiarA" label="Copiar a" class="w-full" />
                                        </validationProvider>
                                    </div>
                                </vs-col>
    
                            </vs-row>
    
                            <vs-row>
    
                                <vs-col vs-type="flex" vs-w="12">
                                    <div class="w-full  p-2">
                                        <validationProvider name="Asunto">
                                            <vs-input v-model="datosResultado.Asunto" label="Asunto" class="w-full" />
                                        </validationProvider>
                                    </div>
                                </vs-col>
                            </vs-row>
    
                            <br>
                            <vs-row>
    
                                <vs-col vs-type="flex" vs-w="12">
                                    <div class="w-full  p-2">
                                        <vue-editor v-model="datosResultado.MensajeDescripcion" :editorToolbar="customToolbar"></vue-editor>
                                        <hr>
                                    </div>
                                </vs-col>
                            </vs-row>
    
                            <vs-row>
                                <vs-button color="success" type="border" style="float:right" submit @click="handleSubmit(EnviarReultado(reporte.opciones))">Enviar</vs-button>
                            </vs-row>
                        </div>
                    </form>
                </ValidationObserver>
    
            </vs-popup>
            <!-------------------------------FINALIZACION PARA ENVIO DE RESULTADOS-->
        </form>
        <vs-divider />
    </vx-card>
    </template>
    
    <script>
    // require styles
    import io from 'socket.io-client';
    
    import {
        VueEditor
    } from "vue2-editor";
    import Datepicker from 'vuejs-datepicker';
    import * as lang from 'vuejs-datepicker/src/locale';
    const ASUNTO = 'RESULTADOS DE RADIOLOGIA GRUPO SERMESA'
    const MENSAJE_TITULO = "<h2>Resultados Radiologia Hospitales la Paz</h2>"
    const MENSAJE_DESCRIPCION = "<p>Buen d&iacute;a estimad@, adjunto encontrar&aacute; sus resultados de radiologia.</p> <p>Departamento Radiologia.</p>"
    const DISCLAIMER = ''; //'<p><b>DISCLAIMER:</b>  La informaci&oacute;n contenida en este correo es de uso confidencial y exclusivo de las personas aquien se realizaron los ex&aacute;menes. Dicha informaci&oacute;n no se proporciona para ser utilizada de otra manera, circulada, referida, o divulgada a terceros. CENTRO HOSPITALARIO LA PAZ y las personas relacionadas con la preparaci&oacute;n, an&aacute;lisis, redacci&oacute;n o publicaci&oacute;n de estos resultados, no son responsables por el uso que se le d&eacute; a la informaci&oacute;n que por este medio se proporciona. Si usted no es el destinatario de este mensaje, por favor llame por tel&eacute;fono o escriba un correo electr&oacute;nico al remitente y borre este mensaje y cualquier adjunto de su sistema; no est&aacute; permitido divulgar de forma parcial o total el contenido de este correo.  Esta informaci&oacute;n no reemplaza una consulta m&eacute;dica o profesional de la salud, los resultados son preliminares o copia de los originales, y no debe ser interpretado como un tratamiento m&eacute;dico o una segunda opini&oacute;n de un s&iacute;ntoma o enfermedad, ya que requiere la interpretaci&oacute;n de un profesional de la salud. Si usted requiere de un resultado definitivo debe solicitarlo al &aacute;rea de laboratorio para que le proporcionen el informe, el cual debe estar sellado y firmado por Qu&iacute;mico Bi&oacute;logo responsable para que tenga validez.</p>'
    
    export default {
        data() {
            return {
                ruta: 'http://srvpacs032en/Synapse/WebQuery/Index?path=/All%20Studies/&filter=accessionnumber=',
                IpLocal: '',
                socket: io('https://sighos-linea.hospitaleslapaz.com/'),
                popupActive2: false,
                activeItem: 0,
                bloqueoBuscar: false, //utlizado para evitar llamar multiples llamadas a los registros
                bloqueoBusqueda: false,
                bloqueoCampos: true,
                active: false,
                switch2: false,
                examenes: [],
                languages: lang,
                listado_imagenes: [],
                info: {
                    opcion: {
                        show: false, //mostrar las opciones del examen_nombre
                        examen_codigo: '',
                        examen_nombre: '',
                        examen_anterior: '',
                        especialista_codigo: '',
                        especialista_nombre: '',
                        especialista_anterior: '',
                        medico_codigo: '',
                        medico_nombre: '',
                        medico_anterior: '',
                        congelado: false,
                        ingresaAdendum: false,
                        informeAdendum: false,
                        sms: false,
                        observaciones: '',
                        admision: '',
                        informe: '',
                        adendum:'',
                        informe_cambio: false,
                        adendum_cambio: false,
                        hospital: '',
                        nacimiento: null,
                        correo_paciente: '',
                        correo_medico: '',
                        RegionObservaciones: '',
                        linea_orden:null
                    },
                    orden_tipo: null,
                    orden_numero: null,
                    idioma: false,
                    paciente_codigo: null,
                    paciente_nombre: '',
                    paciente_edad: '',
                    refiere_codigo: null,
                    refiere: '',
                    reporte: false,
                    mostrar_imagenes: false,
                    mostart_imagenes_carga: false,
                    reporte_src: null,
                    editarmedico: false,
                    editaredad: false,
                    edad: null,
                    edadFormato: "yyyy-MM-dd"
                },
                validar_descripcion: false,
                validar_problema: false,
                category_choices: [{
                    text: 1,
                    value: 1
                }],
                customToolbar: [
                    [{
                        'header': [1, 2, 3, 4, 5, 6, false]
                    }],
                    [{
                        'font': []
                    }],
                    ['bold', 'italic', 'underline', 'strike'],
                    [{
                        list: "ordered"
                    }, {
                        list: "bullet"
                    }],
                    [{
                        'indent': '-1'
                    }, {
                        'indent': '+1'
                    }],
                    [{
                        'color': []
                    }, {
                        'background': []
                    }],
                ],
    
                scanner_img: [],
                scanner_path: '',
    
                actualizar_path: '',
                lista_resultados: [],
                HabilitarVentanaCorreo: false,
                datosResultado: {
                    EnviarA: '',
                    CopiarA: '',
                    CopiarEnOculto: '',
                    Asunto: ASUNTO, //"PRUEBAS DE LABORATORIO GRUPO SERMESA",
                    Mensajetitulo: MENSAJE_TITULO, //"<h2>Resultados laboratorios Hospitales la Paz</h2>",
                    MensajeDescripcion: MENSAJE_DESCRIPCION, //"<p>Buen d&iacute;a estimad@, adjunto encontrar&aacute; sus resultados de laboratorios.</p> <p>Departamento Laboratorio y Radiologia.</p>",
                    MensajeFirma: DISCLAIMER,
                    NombreDelArchivo: 'Radiologia.pdf',
                    idioma: false,
                    linea: '',
                    orden: '',
                    tipoorden: '',
                    tiporeporte: ''
    
                },
    
                reporte: {
                    generarPDF: true,
                    generarExcel: true,
                    generar: false,
                    popup: false,
                    buscar: '',
                    titulo: '',
                    url: '',
                    url_pdf: '',
                    opciones: [],
                    pdf: '',
                    buscador: null
                },
                Permisos: {
                    SUBIR_IMAGEN: false,
                    VER_IMAGEN: false,
                    DESCONGELARSUPERV : false,
                    DESCONGELARADENUM: false
                },
            }
        },
        components: {
            VueEditor,
            Datepicker
        },
        watch: {
            'info.opcion.informe'() {
                this.info.opcion.informe_cambio = true
            },
            'info.opcion.adendum'() {
                this.info.opcion.adendum_cambio = true
            }
        },
        methods: {
            HabilitarEnvio(ValueLinea) {
    
                this.datosResultado.EnviarA = this.info.correo_paciente;
                this.datosResultado.CopiarA = this.info.correo_medico;
                this.HabilitarVentanaCorreo = true;
                this.datosResultado.linea = ValueLinea;
                this.datosResultado.orden = this.info.orden_numero;
                this.datosResultado.tipoorden = this.info.orden_tipo.toUpperCase();
                this.datosResultado.tiporeporte = 'application/pdf';
    
            },
            EnviarReultado() {
    
                this.$genera_reporte_envio({
                    Nombre: "Informes por Orden",
                    Data_source: this.datosResultado,
                    Data_report: this.listado_reportes
                }).catch(() => {})
    
                this.HabilitarVentanaCorreo = false;
    
            },
            cargar_orden() {
                // if (this.bloqueoBuscar == true) return false;
    
                if (!(this.info.orden_numero > 0)) {
                    this.info.orden_numero = null
                    return false
                }
    
                this.bloqueoBuscar = true;
    
                this.axios.post('/app/radiologia/radordenes', {
                        tipo: this.info.orden_tipo,
                        orden: this.info.orden_numero
                    })
                    .then(resp => {
                        if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                            this.info.hospital = resp.data.json[0].Hospital
                            this.info.paciente_codigo = resp.data.json[0].Paciente
                            this.info.paciente_nombre = resp.data.json[0].NombrePaciente
                            this.info.paciente_edad = resp.data.json[0].Edad + ' ' + resp.data.json[0].EdadMedida
                            this.info.refiere = resp.data.json[0].NombreMedico
                            this.info.refiere_codigo = resp.data.json[0].Medico
                            this.info.nacimiento = resp.data.json[0].Nacimiento
                            this.info.observaciones = resp.data.json[0].Observaciones
                            this.info.admision = resp.data.json[0].SerieAdmision + '' + resp.data.json[0].Admision
                            this.info.correo_paciente = resp.data.json[0].CorreoCliente;
                            this.info.correo_medico = resp.data.json[0].CorreoDoctor;
    
                            this.cargar_estudios()
                            this.cargar_path()
    
                        } else {
                            this.$vs.dialog({
                                color: 'danger',
                                title: 'Radiología - Error',
                                text: (resp.data.codigo < 0) ? resp.data.descripcion : 'Sin Registros',
                            })
    
                            return false
                        }
                    })
                    .catch(() => {
                        
    
                        this.bloqueoBuscar = false;
                        this.$vs.dialog({
                            color: 'danger',
                            title: 'Radiología - Error',
                            text: 'Error al obtener datos de la orden',
                        })
                    })
            },
    
            cargar_estudios() {
                this.axios.post('/app/radiologia/radestudios', {
                        tipo: this.info.orden_tipo,
                        orden: this.info.orden_numero
                    })
                    .then(resp => {
    
                        this.examenes = []
                        resp.data.json.map(data => {
                            this.examenes.push({
                                examen_linea: data.LineaOrden,
                                examen_codigo: data.Producto,
                                examen_nombre: data.Nombre,
                                especialista_codigo: data.Radiologo,
                                especialista_anterior: data.Radiologo,
                                especialista_nombre: data.NombreMedico,
                                congelado: (data.InformeLock.toLowerCase() == "s") ? true : false,
                                informeAdendum: (data.InformeAdendum.toLowerCase() == "s") ? true : false,
                                ingresaAdendum: (data.IngresaAdendum.toLowerCase() == "s") ? true : false,
                                sms: data.EntregarPersonalmente,
                                informe: data.Detalle,
                                adendum: data.Adendum,
                                RegionObservaciones: data.RegionObservaciones,
                            })
                        })
                        
                        this.bloqueoBuscar = false;
                        this.bloqueoBusqueda = true
                        // this.bloqueoCampos = false
                    })
            },
    
            cargar_path() {
                this.axios.post('/app/radiologia/RadObtieneRuta', {
                        categoria: this.info.orden_tipo,
                        sucursal: this.info.hospital
                    })
                    .then(resp => {
                        if (resp.data.json.length > 0) {
                            this.scanner_path = resp.data.json[0].Path
                        }
    
                        return this.axios.post('/app/radiologia/RadObtieneRuta', {
                            categoria: 'ACTUALIZA',
                            sucursal: this.info.hospital,
                        })
                    })
                    .then(resp => {
                        if (resp.data.json.length > 0) {
                            this.actualizar_path = resp.data.json[0].Path
                        }
    
                    })
            },
    
            editar_edad() {
                let n = this.info.nacimiento.split(' ')[0].split('/')
                this.info.edad = new Date(n[2], n[1] - 1, n[0])
                this.info.editaredad = true
            },
    
            editar_medico_orden() {
                this.info.editarmedico = true
                this.info.opcion.medico_anterior = this.info.refiere_codigo
                this.info.opcion.medico_codigo = this.info.refiere_codigo
                this.info.opcion.medico_nombre = this.info.refiere
            },
    
            radiologia_reporte(linea) {
                this.$reporte_modal({
                    Nombre: "Informes por Orden",
                    Opciones: {
                        tipoorden: this.info.orden_tipo,
                        orden: this.info.orden_numero,
                        linea: linea,
                        idioma: this.info.idioma
                    }
                })
    
            },
            radiologia_jpgSubir() {
    
        
    
    
    
    
                this.IpLocal = localStorage.getItem('IpLocal');
                
            
                if (this.IpLocal == '') {
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Configuración',
                        text: 'No existe IP  configurada.',
                    })
                    return;
                }
                var url = "C:\\aplicaciones\\VISUALIZADORFM.exe " + this.scanner_path + " " + this.info.orden_tipo + " " + this.info.orden_numero + " SI SI " + this.actualizar_path + " " + this.info.paciente_nombre + "";
                
                this.socket.on("connect", () => {
                    //this.estado = true;
                });
    
                this.socket.emit('message', "WEB;" + this.IpLocal + ";" + url);
    
                this.socket.on("disconnect", () => {
                    //this.estado = false;
                });
    
    
                this.axios.post('/app/radiologia/RadActualizar_ImagenesUP', {
                    Tipo: this.info.orden_tipo,
                    Orden:  this.info.orden_numero,
                    })
                    .then( {})
                    .catch(() => {
    
                    })
            },
            radiologia_jpg() {
                this.IpLocal = localStorage.getItem('IpLocal');
                
                if (this.IpLocal == '') {
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Configuración',
                        text: 'No existe IP  configurada.',
                    })
                    return;
                }            
                var url = "C:\\aplicaciones\\VISUALIZADORFM.exe " + this.scanner_path + " " + this.info.orden_tipo + " " + this.info.orden_numero + " SI NO " + this.actualizar_path + " " + this.info.paciente_nombre + "";
                
                this.socket.on("connect", () => {
                    //this.estado = true;
                });
    
                this.socket.emit('message', "WEB;" + this.IpLocal + ";" + url);
    
                this.socket.on("disconnect", () => {
                    //this.estado = false;
                });
                //C:\aplicaciones\VISUALIZADORFM.exe \\**************\FotosRad\  RA1 348813 SI NO \\**************\Aplicaciones$\ ADALBERTO ORDOÑE
              
                /*this.info.paciente_nombre
                this.$socket.emit('radiologia_visor', {
                    tipo: this.info.orden_tipo,
                    orden: this.info.orden_numero,
                    url: this.scanner_path,
                    url_actualiza: this.actualizar_path
                })*/
            },
    
            cargar_scanner() {
                this.info.mostrar_imagenes = true
                this.info.mostart_imagenes_carga = false
    
                this.axios.post('/app/radiologia/RadObtenerImagen', {
                        categoria: this.info.orden_tipo,
                        subfolder: this.info.orden_tipo + '-' + this.info.orden_numero,
                    })
                    .then(resp => {
                        if (resp.data.codigo == 0) {
                            this.listado_imagenes = []
                            if (resp.data.listado.length > 0) this.info.mostart_imagenes_carga = true
                            resp.data.listado.map(data => {
                                this.listado_imagenes.push({
                                    src: data.src,
                                    archivo_nombre: data.nombreArchivo
                                })
                            })
                        }
    
                    })
                    .catch(() => {
    
                    })
    
            },
            confirmacion_congelar(){
                let estado  = this.info.opcion.congelado
                if(!this.info.opcion.congelado && (this.info.opcion.ingresaAdendum || this.info.opcion.informeAdendum)){
                    if(!this.info.opcion.informeAdendum){
                        this.$vs.dialog({
                            type: 'confirm',
                            color: 'warning',
                            title: 'Informe',
                            text: '¿Está seguro de CONGELAR? Una vez congelado, no se podrá realizar ninguna modificación, excepto mediante ADENDUM.',
                            acceptText: 'Aceptar',
                            cancelText: 'Cancelar',
                            accept: ()=>{
                                this.radiologia_congelar_descongelar(estado)
                            },
                            cancel:()=>{
                                this.info.opcion.congelado = !this.info.opcion.congelado;                                
                            }
                        })
                    }else{
                        this.$vs.dialog({
                            type: 'confirm',
                            color: 'warning',
                            title: 'Informe',
                            text: '¿Está seguro de CONGELAR ADENDUM? Una vez congelado, no se podrá realizar ninguna modificación.',
                            acceptText: 'Aceptar',
                            cancelText: 'Cancelar',
                            accept: ()=>{
                              this.radiologia_congelar_descongelar(estado)
                            },
                            cancel:()=>{
                                this.info.opcion.congelado = !this.info.opcion.congelado;
                            }
                        })
                    }
                }else{
                    this.radiologia_congelar_descongelar(estado)
                }
            },
            async radiologia_congelar_descongelar(estado) {
                if (this.info.opcion.congelado == false && !this.$validar_privilegio('CONGELAR').status) {
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Privilegios',
                        text: 'No cuenta con los privilegios para CONGELAR',
                    })
                    this.info.opcion.congelado = !this.info.opcion.congelado
                    return false
                }
                if (this.info.opcion.congelado == true && !this.$validar_privilegio('DESCONGELAR').status) {
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Privilegios',
                        text: 'No cuenta con los privilegios para DESCONGELAR',
                    })
                    this.info.opcion.congelado = !this.info.opcion.congelado
                    return false
                }                
                //Guardando previo a congelación
                new Promise((resolve, reject) => {
                        if (estado == false) {
                            this.guardar_informe(false, (status) => {
                                if (status == true) {
                                    resolve(true)
                                } else {
                                    reject(true)
                                }
                            })
                        } else {
                            resolve(true)
                        }
                    })
                    .then(() => {
                        var PermiSupDesbloqueo = 0;
                        var PermisoDescongelarAdendum = 0; 
                        if(this.Permisos.DESCONGELARSUPERV){ PermiSupDesbloqueo = 1; }
                        if(this.Permisos.DESCONGELARADENUM){ PermisoDescongelarAdendum = 1;}

                        
                        return this.axios.post('/app/radiologia/' + ((estado == false) ? 'RadCongelaEst' : 'RadDescongelaEst'), {
                            tipo: this.info.orden_tipo,
                            orden: this.info.orden_numero,
                            LineaOrden: this.info.opcion.linea_orden,
                            NombreEstudio: '.',
                            Radiologo: 0,
                            TipoUp: this.info.opcion.informeAdendum ? 7 : 2,
                            DesbloqueoSuperv: this.info.opcion.informeAdendum ? PermisoDescongelarAdendum : PermiSupDesbloqueo
                        })
                    })
                    .then(() => {
                        this.axios.post('/app/ajenos/HonorariosCrear', {
                            Tipo: 2,
                            TipoOrden: this.info.orden_tipo,
                            Orden: this.info.orden_numero,
                            Linea: this.info.opcion.linea_orden,
                        })
                        this.bloqueoCampos = !estado     
                        if(this.info.opcion.ingresaAdendum == true){
                            this.info.opcion.informeAdendum = true
                        }
                        this.cargar_estudios()
                    })
                    .catch(() => {
                      
                        this.info.opcion.congelado = !this.info.opcion.congelado
                        return false
                    })
            },
    
            radiologia_mensaje() {
                this.axios.post('/app/radiologia/' + ((this.info.opcion.sms) ? 'RadEnviaMsg' : 'RadNoEnviaMsg'), {
                        Tipo: this.info.orden_tipo,
                        Orden: this.info.orden_numero,
                        LineaOrden: this.info.opcion.linea_orden
                    })
                    .then(() => {
                        this.cargar_estudios()
                    })
            },
    
            buscar_especialista() {
                this.$refs.buscar_especialista.iniciar((data) => {
                    if (data != null) {
                        this.info.opcion.especialista_codigo = data.Codigo
                        this.info.opcion.especialista_nombre = data.NombreRad
                        this.info.opcion.ingresaAdendum = data.IngresaAdendum == 'S' ? true : false
                    }
                })
            },
    
            buscar_especialista_auto() {
                this.$refs.buscar_especialista.validar({
                    Codigo: this.info.opcion.especialista_codigo
                }, (data) => {
                    if (data != null && data.length > 0) {
                        this.info.opcion.especialista_nombre = data[0].NombreRad
                        this.info.opcion.ingresaAdendum = data[0].IngresaAdendum == 'S' ? true : false
                    } else {
                        this.info.opcion.especialista_codigo = this.info.opcion.especialista_anterior
                    }
                })
            },
    
            buscar_medico() {
                this.$refs.buscar_especialista.iniciar((data) => {
                    if (data != null) {
                        this.info.opcion.medico_codigo = data.Codigo
                        this.info.opcion.medico_nombre = data.NombreRad
                    }
                })
            },
    
            buscar_medico_auto() {
                this.$refs.buscar_especialista.validar({
                    Codigo: this.info.opcion.medico_codigo
                }, (data) => {
    
                    if (data != null && data.length > 0) {
                        // this.info.opcion.especialista_anterior = this.info.opcion.especialista_codigo
                        this.info.opcion.medico_anterior = this.info.opcion.medico_codigo
                        this.info.opcion.medico_nombre = data[0].NombreRad
                    } else {
                        this.info.opcion.medico_codigo = this.info.opcion.medico_anterior
                    }
                })
            },
    
            buscar_radiologias() {
                this.$refs.buscar_radiologias.iniciar((data) => {
                   
                    if (data != null) {
                        this.info.orden_tipo = data.Orden.split('-')[0]
                        this.info.orden_numero = data.Orden.split('-')[1]
                        this.cargar_orden()
                    }
                })
            },
    
            buscar_plantilla() {
                
                if (this.info.opcion.especialista_codigo== null || this.info.opcion.especialista_codigo == '') {
                    this.$vs.dialog({
                        color: 'danger',
                        title: 'Informe',
                        text: 'Seleccionar especialista!',
                        acceptText: 'Aceptar',
                        accept: this.acceptAlert
                    })
                    return false
                }

                this.$refs.buscar_plantillas.iniciar((data) => {
                    
                    if (data != null) {
                        this.info.opcion.informe = data.Detalle
                        // this.info.orden_tipo = data.Tipo.split('-')[0]
                        // this.info.orden_numero = data.Tipo.split('-')[1]
                        // this.cargar_orden()
                    }
                })
            },
    
            guardar_medico() {
                this.axios.post('/app/radiologia/RadMedicoALaOrden', {
                        Tipo: this.info.orden_tipo,
                        Orden: this.info.orden_numero,
                        LineaOrden: this.info.opcion.linea_orden,
                        CodRadiologo: this.info.opcion.medico_codigo
                    })
                    .then(() => {
                        this.info.editarmedico = false
                        this.info.refiere_codigo = this.info.opcion.medico_anterior
                        this.info.refiere_codigo = this.info.opcion.medico_codigo
                        this.info.refiere = this.info.opcion.medico_nombre
    
                    })
                // .catch(() => {
    
                // })
    
            },
    
            guardar_edad() {
                let d = new Date(this.info.edad)
    
                this.axios.post('/app/radiologia/RadActualizaEdad', {
                        Tipo: this.info.orden_tipo,
                        Orden: this.info.orden_numero,
                        FechaNacimiento: d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate() + ' 00:00:00'
                    })
                    .then(() => {
                        return this.axios.post('/app/radiologia/radordenes', {
                            tipo: this.info.orden_tipo,
                            orden: this.info.orden_numero
                        })
                    })
                    .then(resp => {
                        this.info.editaredad = false
                        if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                            this.info.paciente_edad = resp.data.json[0].Edad + ' ' + resp.data.json[0].EdadMedida
                            this.info.nacimiento = resp.data.json[0].Nacimiento
    
                        }
                    })
            },
    
            limpiar_orden() {
                this.examenes = []
                this.info.opcion.congelar = false
                this.info.opcion.sms = false
                this.info.opcion.medico_anterior = null
                this.info.opcion.medico_codigo = null
                this.info.opcion.medico_nombre = null
                this.info.opcion.linea_orden = null                
                this.info.orden_tipo = null
                this.info.idioma = false
                this.info.orden_numero = null
                this.info.paciente_codigo = null
                this.info.paciente_nombre = ''
                this.info.paciente_edad = ''
                this.info.examen_codigo = ''
                this.info.examen_nombre = ''
                this.info.examen_linea = ''
                this.info.admision = ''
                this.info.especialista_codigo = ''
                this.info.especialista_nombre = ''
                this.info.refiere = ''
                this.info.refiere_codigo = null
                this.bloqueoBusqueda = false
                this.bloqueoCampos = true
                this.info.informe = ''
                this.info.reporte = false
                this.info.reporte_src = null
            },
    
            opciones(op) {
               
                this.info.opcion.congelado = op.congelado
                this.info.opcion.sms = op.sms
                this.info.opcion.examen_codigo = op.examen_codigo
                this.info.opcion.examen_anterior = op.examen_nombre.trim()
                this.info.opcion.examen_nombre = op.examen_nombre.trim()
                this.info.opcion.especialista_codigo = op.especialista_codigo
                this.info.opcion.especialista_anterior = op.especialista_codigo
                this.info.opcion.especialista_nombre = op.especialista_nombre
                this.info.opcion.observaciones = op.observaciones
                this.info.opcion.informe = op.informe
                this.info.opcion.adendum = op.adendum
                this.info.opcion.linea_orden = op.examen_linea
                this.info.opcion.RegionObservaciones = op.RegionObservaciones
                this.info.opcion.ingresaAdendum = op.ingresaAdendum
                this.info.opcion.informeAdendum = op.informeAdendum
                this.bloqueoCampos = op.congelado                                
                setTimeout(() => {
                    this.info.opcion.informe_cambio = false
                    this.info.opcion.adendum_cambio = false
                }, 500)

                this.info.opcion.show = true
            },
    
            guardar_informe(recargar, callback) {
                // return new Promise((resolve) => {
                let descripciones = []
                if (this.info.opcion.examen_anterior != this.info.opcion.examen_nombre) descripciones.push({'Nombre de Examen': this.info.opcion.examen_anterior + ' > ' + this.info.opcion.examen_nombre})
                if (this.info.opcion.especialista_codigo != this.info.opcion.especialista_anterior) descripciones.push({'Especialista':  this.info.opcion.especialista_anterior + ' > ' + this.info.opcion.especialista_codigo})
                if (this.info.opcion.informe_cambio) descripciones.push({'Informe':this.info.opcion.informe})
                if (this.info.opcion.adendum_cambio) descripciones.push({'Adendum':this.info.opcion.adendum})
    
                let Bitacora_ = (descripciones.length > 0) ? {
                    Empresa: 'MED',
                    Serie: this.info.orden_tipo,
                    Documento: this.info.orden_numero,
                    Linea: this.info.opcion.linea_orden,
                    Tipo: "Radiologia",
                    Descripciones: descripciones
                } : null

                

                return this.axios.post('/app/radiologia/'+((this.info.opcion.informeAdendum) ? 'radactualizaadendum':'radactualizaestudio'), {
                        Tipo: this.info.orden_tipo,
                        Orden: this.info.orden_numero,
                        LineaOrden: this.info.opcion.linea_orden,
                        NombreEstudio: this.info.opcion.examen_nombre,
                        CodRadiologo: (this.info.opcion.especialista_codigo > 0) ? this.info.opcion.especialista_codigo : 0,
                        Informe: this.info.opcion.informe,
                        Adendum: this.info.opcion.adendum,
                        Bitacora: Bitacora_
                    })
                    .then(() => {     
                        this.guardar_bitacora(Bitacora_)                   
                        if (recargar) {
                            this.cargar_estudios()                            
                            this.info.opcion.show = false;
                        }
                        if (callback) callback(true)
                    })
                    .catch(() => {
                        if (callback) callback(false)
                    })
            },
            guardar_bitacora(info){
                if(info==null)return;
                this.info.opcion.informe_cambio = false;
                this.info.opcion.adendum_cambio = false;
                this.axios.post('/app/bitacora/registro_lista_bitacora',{
                    Bitacoras:[{
                        'info':JSON.stringify(info),
                        'llave':{'Id':this.info.orden_tipo +'-'+this.info.orden_numero+'-'+this.info.opcion.linea_orden},
                        'tabla':'RadiologiaInformes'
                    }]                    
                }).catch()
            },
        },
        mounted() {
    
            this.listado_reportes = this.$recupera_parametros_reporte('Informes por Orden')
            
            this.listado_reportes = this.$recupera_parametros_reporte('Informes por Orden')
            this.$validar_funcionalidad('/RAD/RAD001', 'SUBIR_IMAGEN', (d) => {
                this.Permisos.SUBIR_IMAGEN = d.status;
    
            })
            this.$validar_funcionalidad('/RAD/RAD001', 'VER_IMAGEN', (d) => {
                this.Permisos.VER_IMAGEN = d.status;
    
            })
            this.$validar_funcionalidad('/RAD/RAD001', 'DESCONGELARSUPERV', (d) => {
                this.Permisos.DESCONGELARSUPERV = d.status;
    
            })

            this.$validar_funcionalidad('/RAD/RAD001', 'DESCONGELARADENDUM', (d) => {
                this.Permisos.DESCONGELARADENUM = d.status;
    
            })            
            
    
        },
        
    
        incializaValores() {
            this.lista_resultados = []
            /*this.datosResultado = []
            this.datosResultado.EstadoOrden = ''
            this.datosResultado.DescripcionOrden = ''
            this.datosResultado.Tipo = null
            this.datosResultado.Orden = null
            this.datosResultado.EstadoExamen = ESTADO_EXAMEN*/
            this.datosResultado.Asunto = ASUNTO
            this.datosResultado.Mensajetitulo = MENSAJE_TITULO
            this.datosResultado.MensajeDescripcion = MENSAJE_DESCRIPCION
            this.datosResultado.MensajeFirma = DISCLAIMER
            /* this.EsteExamen = ''
             this.EstaSeccion = ''            
             */
        },
        async beforeCreate() {
            this.listado_reportes = await this.$recupera_parametros_reporte('Informes por Orden')
        },
        created() {
    
        }
    }
    </script>
    
    <style>

    .button {
        margin: 0px !important;
    }
    
    .btnx {
        margin-left: 10px !important;
        background: red;
        border-radius: 5px 0px 0px 5px;
    }
    
    .btn-drop {
        border: 1px solid rgba(0, 0, 0, 0.2);
        color: red;
    
    }
    
    .menu-funcionalidad {
        /* padding-top:0 !important; */
        /* box-shadow: 0 0 5px rgba(0, 0, 0, 0.3); */
        /* padding: 15px; */
        display: inline;
        float: right;
        border-radius: 10px;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        -webkit-box-pack: start;
        -ms-flex-pack: start;
        justify-content: space-between;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        margin-bottom: 10px;
    }
    
    .menu-funcionalidad a {
        text-align: center;
        padding-right: 10px;
    }
    
    .menu-funcionalidad i {
        font-size: 20px;
    }
    
    .vs-switch {
        height: 30px;
        width: 60px !important;
    }
    
    .vs-circle-switch {
        height: 25px;
        width: 25px;
    }
    
    .vs-switch--icon {
        font-size: 15px
    }
    
    .vs-switch.vs-switch-active .vs-switch--circle {
        margin-left: calc(100% - 30px);
    }
    
    .popup-generar {
        height: 100%
    }
    </style>
    