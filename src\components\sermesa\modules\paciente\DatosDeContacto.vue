<template>
    <div class="container">

        <ValidationObserver ref="contactData" v-slot="{ invalid, handleSubmit }" mode="lazy">
            <form @submit.prevent="handleSubmit(submitForm(submit))">

                <div class="tab-text">
                    <div class="container_div p-4">


                        <div class="container">

                            <vs-row>
                                <vs-col vs-type="flex" vs-w="10">
                                    <div class="w-full ml-2">
                                        <h6>Telefonos de contacto</h6>
                                        <hr
                                            style="height: 8px; border: 0; box-shadow: inset 0 12px 12px -12px rgba(0, 0, 0, 0.5)">

                                    </div>
                                </vs-col>
                            </vs-row>

                            <vs-row>
                                <!-- <vs-col vs-type="flex" vs-w="4">
                                    <div class="w-2/3  p-2">
                                        <validationProvider name="No. Telefono Movil" class="required"
                                            rules="required|min:8" v-slot="{ errors }">
                                            <vs-input label="No. Telefono Movil" class="w-full p-2"
                                                v-model.trim="PacienteData.Celular" 
                                                :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null" />
                                        </validationProvider>
                                    </div>
                                </vs-col> -->

                                <vs-col vs-type="flex" vs-w="4">
                                    <div class="w-2/3  p-2">
                                        <validationProvider name="No. Telefono Domiciliar">
                                            <vs-input label="No. Telefono Domiciliar" class="w-full p-2"
                                                v-model.trim="PacienteData.Telefonos" />
                                        </validationProvider>
                                    </div>
                                </vs-col>

                                <vs-col vs-type="flex" vs-w="4">
                                    <div class="w-2/3  p-2">
                                        <validationProvider name="No. Telefono Trabajo">
                                            <vs-input label="No. Telefono Trabajo" class="w-full p-2"
                                                v-model.trim="PacienteData.TelefonoTrabajo" />
                                        </validationProvider>
                                    </div>
                                </vs-col>
                            </vs-row>


                            <!-- <div class="datosCorreoMedico align-center">
                                <vs-row>
                                    <div class="checkboxStyle pt-4">
                                        <vs-col vs-type="flex" vs-w="4">
                                            <div class="w-2/3  p-2">
                                                <validationProvider name="Correo Electronico">
                                                    <vs-input        
                                                      v-bind:readonly="noCorreoElectronico"
                                                      label="Correo Electronico"
                                                      class="w-full p-2"
                                                      v-model.trim="PacienteData.Email" />
                                                </validationProvider>
                                            </div>
                                        </vs-col>

                                        <vs-col vs-w="2">
                                            <div class="w-full   pt-12">
                                               <BaseCheckboxGroup
                                                 :options="checkboxOptions"
                                                 v-model="optionChecked"
                                                 :groupName="'correoMedico'">
                                                 </BaseCheckboxGroup>
                                            </div>
                                        </vs-col>
                                    </div>
                                </vs-row>
                            </div> -->
                        </div>

                        <div v-show="showButtons">
                            <vs-button style="margin-right:1px" v-on:click="handleSubmit(submit())" :disabled="invalid">
                                Guardar
                            </vs-button>
                        </div>

                    </div>
                </div>
            </form>
        </ValidationObserver>
        <!-- </vx-card> -->
    </div>
</template>


<script>

import { mapFields } from 'vuex-map-fields';

//import BaseCheckboxGroup from "@/components/sermesa/global/checkbox/BaseCheckboxGroup";

export default {

    name: "DatosDeContacto",
    data: () => ({
        isReady: '',
        showButtons: false,
        checkboxOptions: [],
        optionChecked: [],
        noCorreoElectronico:Boolean

    }),
    components: {
        //BaseCheckboxGroup

    },
    computed: {
        ...mapFields('paciente', ["PacienteData"])
    },
    mounted() {
        this.CargarOpcionCorreo()
    },
    created() {

    },
    watch: {
        'optionChecked':function (val) {

            this.noCorreoElectronico = val.length > 0?true:false
            this.PacienteData.Email =''
    
        }

    },
    methods: {
        getionarDatos() {
            return new Promise((resolve) => {
                // Lógica de guardado del cliente aquí
                // Resuelve la promesa si el guardado es exitoso
                return resolve(true);
                // Rechaza la promesa si hay un error en el guardado
                // reject('Mensaje de error aquí');
            });
        },
        validacionFormulario() {
            return new Promise((resolve, reject) => {
                this.$refs.contactData.validate()
                    .then((isValid) => {
                        if (isValid) {

                            resolve(true);
                        } else {

                            reject('Revisar campos requeridos');
                        }
                    })
                    .catch((error) => {

                        reject(error);
                    });
            });
        },
        gestionarPaciente() {
            return new Promise((resolve, reject) => {
                this.$store.dispatch('paciente/crearPaciente')
                    .then((response) => {
                        
                        resolve(response);
                    })
                    .catch((error) => {
                        reject(error);
                    });
            });
        },

        async submit() {

            
            this.getionarDatos()
                .then(() => {
                    return this.validacionFormulario();
                })
                .then(() => {                                        
                    return this.gestionarPaciente();
                })
                .then(() => {
               
                    this.$emit("ready", true);
                })
                .catch((error) => {
                    this.$vs.dialog({
                        type: 'confirm',
                        color: '#ED8C72',
                        acceptText: 'Continuar',
                        title: 'Error en gestión de Paciente',
                        text: error,
                        clientWidth: 100,
                        accept: () => {
                        
                            this.$emit("ready", false);
                            
                        }
                    });
                })          
            
        },
        isValitInt(data) {
            if (this.isEmpty(data)) {
                data = data === null ? 0 : data;
            }
        },
        isEmpty(value) {
            if (typeof value === "undefined" || value === null || value === "")
                return true;
        },
        CargarOpcionCorreo() {
            this.checkboxOptions = [
                {
                    label: 'No posee correo',
                    value: '1',
                }
            ]
        }
    }

}

</script>


<style scoped>
* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

.container_div {
    display: grid;
    grid-gap: 10px;
    width: 100%;
    height: 100%;

}

.container_div>div {
    border: 1px solid #888;
    padding-top: 10px;
    padding-bottom: 10px;
}

/* hr {
    height: 12px;
    border: 0;
    box-shadow: inset 0 12px 12px -12px rgba(0, 0, 0, 0.5);
} */

.container_b {
    display: grid;
    grid-gap: 10px;
    width: 100%;
    height: 10%;
}

.container {
    max-width: 100%;
}

.datosCorreoMedico {
    align-items: center;
}

.align-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.checkboxStyle {
    display: flex;
    justify-content: left;
    align-items: left;
    width: 100%;
}

.radio-group.radio-group--inline div[data-radio-button] {
    width: auto;
}

.radio-group.radio-group--inline div[data-radio-button]:not(:first-of-type) {
    padding-left: var(--space-base);
}
</style>