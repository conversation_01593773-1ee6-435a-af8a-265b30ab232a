<template>
    <vx-card title="Devoluciones Requerimientos Departamentos">
    
        <div class="content content-pagex">
            <vs-divider></vs-divider>
            <br>
            <div class="flex flex-wrap">                
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">                    
                    <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                        <label class="typo__label">{{Titulo_pantalla.Titulo_bodega1}}</label>       
                        <strong>{{ cod_bodegaFuente.CODIGO }}</strong>                           
                        <multiselect
                        v-model="cod_bodegaFuente"
                        :options="Lista_bodegas_fuente"
                        :searchable="true"
                        :close-on-select="true"
                        :show-labels="false"
                        :allow-empty="false"
                        :custom-label="Bodegas_seleccionado"
                        placeholder="Seleccionar bodega"
                        @input="onChangeBodega"
                        :danger="errors.length > 0"
                        :danger-text="errors.length > 0 ? errors[0] : null"
                        >
                        <span
                        slot="noOptions">Lista no disponible.</span>
                        </multiselect>
                    </ValidationProvider>                                                                  
                </div>
            </div>

            <div class="flex flex-wrap">
    
                <div style="margin-right: 20px" class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <label class="typo__label">Estado</label>
                    <multiselect
                      v-model="cb_lista_operacion"
                      :options="lista_estado"
                      :searchable="true"
                      :close-on-select="true"
                      :show-labels="false"
                      :allow-empty="false"
                      :custom-label="Operacion_seleccionada"
                      placeholder="Seleccionar"
                      @input="onChangeEstado">
                      <span slot="noOptions">Lista no disponible</span>
                    </multiselect>
                </div>
    
                <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7 m-1">
                    <label class="typo__label">Fecha Inicio:</label>
                    <vs-input type="date" v-model="fecha_inicio" name="date1" />
                </div>
    
                <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7 m-1">
                    <label class="typo__label">Fecha Final:</label>
                    <vs-input type="date" v-model="fecha_final" name="date1" />
                </div>
    
                <vs-button style="float:left;margin: 20px" color="primary" type="filled" icon-pack="fas" icon="fa-search" @click="Consultar_OrdenEnc()"> Búsqueda</vs-button>                    
    
            </div>
            <vs-divider></vs-divider>            
            <vs-table2 v-if="Id_estado_seleccionado  == 'TP'" ref="tablaBusqueda" max-items="10" pagination :data="Lista_vista" search id="tb_lista_busqueda">    
                <template slot="thead">                    
                    <th width="130px"> {{Titulo_pantalla.Titulo_solicitud}} </th>
                    <th>Departamento</th>
                    <th>Fecha(s)</th>
                    <th>Observaciones</th>
                    <th></th>
                </template>
    
                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">
    
                        <vs-td2  width='5%'>
                            {{tr.IDCODIGOREQUERIMIENTO}}
                        </vs-td2>
    
                        <vs-td2  width='20%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                {{tr.IDDEPARTAMENTO+' - '+tr.NOMBREDEPARTAMENTO}}
                            </div>
                        </vs-td2>

                        <vs-td2 width='20%'> 
                            <div style="word-wrap: break-word;white-space: wrap;">
                                <label>Solicitado: {{tr.FECHASOLICITUD}}</label>
                            </div>
                        </vs-td2>

                        <vs-td2  width='30%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                {{ tr.OBSERVACIONES }}
                            </div>
                        </vs-td2>  
    
                        <vs-td2 width='10%' align="right" style="white-space:nowrap;min-width: 200px;">
                            <vx-tooltip text="Consultar" style="display:inline-block;margin-right:30px">
                                <vs-button color="primary" icon-pack="fas" icon="fa-arrow-left" style="display:inline-block;margin-right:2px" @click="Emergente_Detalle_Devolucion(data[indextr])"></vs-button>
                            </vx-tooltip>
                        </vs-td2>     
                    </tr>
                </template>
            </vs-table2>

            <vs-table2 v-if="Id_estado_seleccionado  != 'TP'" ref="tablaBusqueda" max-items="10" pagination :data="Lista_vista" search id="tb_lista_busqueda">    
                <template slot="thead">                    
                    <th width="130px"> {{Titulo_pantalla.Titulo_solicitud}} </th>
                    <th>Departamento</th>
                    <th>Fecha(s)</th>
                    <th>Observaciones</th>
                    <th></th>
                </template>
    
                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">

                        <vs-td2  width='5%'>
                            {{tr.IDCODIGOMOVIMIENTO}}
                        </vs-td2>
    
                        <vs-td2  width='20%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                {{tr.IDDEPARTAMENTO+' - '+tr.NOMBREDEPARTAMENTO}}
                            </div>
                        </vs-td2>

                        <vs-td2 width='20%'> 
                            <div style="word-wrap: break-word;white-space: wrap;">                                
                                <label v-if="Id_estado_seleccionado  != 'TP'">Solicitado Dev: {{tr.FECHASOLICITUD}}</label>
                                <br v-if="tr.FECHASOLICITUD">
                                <label v-if="Id_estado_seleccionado  == 'T'">Aceptado Dev: {{tr.FECHAENTREGADA}}</label>
                            </div>
                        </vs-td2>

                        <vs-td2  width='30%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                {{ tr.OBSERVACIONES }}
                            </div>
                        </vs-td2>  
                           
                        <vs-td2 width='10%' align="right" style="white-space:nowrap;min-width: 200px;">
                            <vx-tooltip text="Consultar" style="display:inline-block;margin-right:30px">
                                <vs-button color="primary" icon-pack="fas" icon="fa-eye" style="display:inline-block;margin-right:2px" @click="Emergente_Detalle_Consulta(data[indextr])"></vs-button>    
                            </vx-tooltip>                                                        
                            <vx-tooltip text="Imprimir" style="display:inline-block;margin-right:30px">
                                <vs-button color="primary" icon-pack="feather" icon="icon-printer" style="display:inline-block;margin-right:2px" @click="Consultar_Movimiento(data[indextr])"></vs-button>
                            </vx-tooltip>
                        </vs-td2>
    
                    </tr>
                </template>
            </vs-table2>
    
            <!----------------------- NUEVA ASOCIACIÓN ---------->
            <vs-popup classContent="popup-example" :title="Descripcion_Emergente" :active.sync="Estado_Emergente_Consulta">
                <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
                    <div style="border:2px solid #1B5E20;border-radius:5px;padding:5px;font-size:12px;background-color: #C8E6C9">
                        Sucursal Origen: <b style="font-size:1vw">{{sesion.sesion_sucursal_nombre}}</b>
                    </div>
                    <br>
                    <b> <small>No. Movimiento </small></b>
                    <b style="font-size:2vw">{{IdCodigoMovimiento}}</b> 

                    <div class="w-1/2 md:w-1/2 lg:w-6/12 xl:w-6/12">
                         <b> <small>Fecha Solictud Dev: </small></b>
                        <b>{{FechaSolicitud}}</b>
                    </div>
                    <div class="w-1/2 md:w-1/2 lg:w-6/12 xl:w-6/12" v-if="Id_estado_seleccionado  == 'T'">
                         <b> <small>Fecha Aceptación Dev: </small></b>
                        <b>{{FechaEntrega}}</b>
                    </div>

                    <!--- Departamento Fuente -->
                    <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <br>
                        <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                            <label class="typo__label">Departamento Fuente:</label>
                            <strong>{{id_departamento_destino}}</strong>
                            <vs-input  type="text" v-model="nombre_departamento_destino" :disabled="Deshabilitar_campos" class="w-full" 
                                       :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null"/>
                        </ValidationProvider>
                    </div>
                    <!--- Ubicación Destino -->
                    <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <br>
                        <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                            <label class="typo__label">Bodega Destino:</label>
                            <strong>{{ cod_bodegaFuente.CODIGO }}</strong>
                            <multiselect
                              v-model="cod_bodegaFuente"
                              :options="Lista_bodegas_fuente"
                              :searchable="true"
                              :close-on-select="true"
                              :show-labels="false"
                              :custom-label="Bodegas_seleccionado"
                              placeholder="Seleccionar bodega"
                              @input="onChangeBodegaFuente"
                              :danger="errors.length > 0"
                              :danger-text="errors.length > 0 ? errors[0] : null"
                              :disabled="IdCodigoMovimiento>0"
                              >
                              <span
                              slot="noOptions">Lista no disponible.</span>
                              </multiselect>
                        </ValidationProvider>
                    </div>
                    <!--- Mostrar detalle de la orden--->
                    <vs-table2 max-items="10" pagination :data="Lista_detalle" id="tb_lista_solicitud">
    
                        <template slot="thead">
                            <th>Linea</th>
                            <th>Código</th>
                            <th>Producto</th>
                            <th>Unidad Medida</th>
                            <th>Cantidad Devuelta</th>
                            <th>Cantidad Aceptada</th>
                        </template>
    
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 width='5%'>                                                                            
                                        {{tr.LINEA}}                                    
                                </vs-td2>     
                                <vs-td2 width='5%'>                                                                            
                                        {{tr.IDPRODUCTOFK}}                                    
                                </vs-td2>                                        
                                <vs-td2 width='65%'>
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                                        
                                        {{tr.DESCRIPCION_PRODUCTO}}                                        
                                    </div>
                                </vs-td2>
                                <vs-td2 width='15%'>
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">
                                        {{tr.DESCRIPCION_UNIDAD}}
                                    </div>                                    
                                </vs-td2>
                                <vs-td2 width='5%'>
                                    {{tr.CANTIDAD_PEDIDA}}
                                </vs-td2>
                                <vs-td2 v-if="Id_estado_seleccionado  == 'T'" width='5%'>
                                    {{tr.CANTIDAD}}
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
                    <vs-divider></vs-divider>
                    <div class="flex flex-wrap">
                        <div class="w-full md:w-full lg:w-full xl:w-full">
                            <vs-textarea label="Observaciones" type="text" class="w-full" v-model="Observaciones" :disabled="true"/>
                        </div>    
                    </div>
                    <vs-divider></vs-divider>
                    <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-printer" id="btn_movimiento" @click="Consultar_Movimiento(Requerimiento_Seleccionado)"> Imprimir </vs-button>                
                    <vs-divider></vs-divider>
                </div>
            </vs-popup>
    
            <!----------------------- RECEPCION DE PRODUCTO ---------->
            <vs-popup classContent="popup-example" :title="Descripcion_Emergente" :active.sync="Estado_Emergente">
                <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">                    
                    <b> <small>Requerimiento No. </small></b>
                    <b style="font-size:2vw">{{IdCodigoRequerimiento}}</b>
    
                    <!--- Ubicación Destino -->
                    <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <br>
                        <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                            <label class="typo__label">Bodega de Despacho:</label>
                            <strong>{{id_bodega_fuente}}</strong>
                            <vs-input  type="text" v-model="nombre_bodega_fuente" :disabled="Deshabilitar_campos" class="w-full" 
                                       :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null"/>
                        </ValidationProvider>
                    </div>  
                    <br>                      
                    <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <br>
                        <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                            <label class="typo__label">Departamento:</label>
                            <strong>{{id_departamento_destino}}</strong>
                            <vs-input  type="text" v-model="nombre_departamento_destino" :disabled="Deshabilitar_campos" class="w-full" 
                                       :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null"/>
                        </ValidationProvider>
                    </div>
                    <vs-divider>Detalle</vs-divider>
    
                    <!--- Mostrar detalle de la orden--->
                    <vs-table2 max-items="10" pagination :data="Lista_detalle" id="tb_lista_solicitud">
    
                        <template slot="thead">
                            <th>Linea</th>
                            <th>Código</th>
                            <th>Producto</th>
                            <th>Unidad Medida</th>
                            <th>Solicitada </th>
                            <th>Despachada</th>
                            <th>Aceptada</th>
                            <th>Devuelta</th>
                            <th>Devolver</th>
                        </template>
    
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 width='5%'>                                                                            
                                        {{tr.LINEA}}                                    
                                </vs-td2>  
                                <vs-td2 width='5%'>                                                                            
                                        {{tr.IDPRODUCTOFK}}                                    
                                </vs-td2>  
                                <vs-td2 width='65%'>
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                                        
                                        {{tr.DESCRIPCION_PRODUCTO}}                                        
                                    </div>
                                </vs-td2>
                                <vs-td2 width='15%'>
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                                        
                                        {{tr.DESCRIPCION_UNIDAD}}
                                    </div>
                                </vs-td2>
                                <vs-td2 width='5%'>
                                    {{tr.CANTIDAD_PEDIDA}}
                                </vs-td2>
                                <vs-td2 v-if="!Consulta_Pedido" width='5%'>
                                    {{tr.CANTIDAD_ENTREGA}}
                                </vs-td2>   
                                <vs-td2 v-if="!Consulta_Pedido" width='5%'>
                                    {{tr.CANTIDAD}}
                                </vs-td2>  
                                <vs-td2 v-if="!Consulta_Pedido" width='5%'>
                                    {{tr.CANTIDAD_DEVUELTA}}
                                </vs-td2>    
                                <vs-td2 v-if="!Consulta_Pedido" width='5%'>
                                    <ValidationProvider name="Existencias" :rules="'required|min:0|max:'+(tr.CANTIDAD-tr.CANTIDAD_DEVUELTA)" v-slot="{ errors }" class="required">
                                        <vs-input  v-model="tr.CANTIDAD_DEVOLVER" class="w-full"  @input="VerificarExistencias(tr)" 
                                                   :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"/>                                    
                                    </ValidationProvider>
                                </vs-td2>                           
                            </tr>
                        </template>
                    </vs-table2>
                    <vs-divider></vs-divider>
                    <div class="flex flex-wrap">
                        <div class="w-full md:w-full lg:w-full xl:w-full">
                            <vs-textarea disabled label="Observaciones" type="text" class="w-full" v-model="Observaciones" />
                        </div>
    
                    </div>
                    <vs-divider>---------------</vs-divider>
                    <div v-if="!Consulta_Pedido && !Consulta_Aceptados">
                        <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" id="btn_confirmacion" @click="Finalizar_DevolucionRequerimineto()">Ingresar Devolución</vs-button>
                    </div>
                    <vs-divider></vs-divider>
                </div>
            </vs-popup>
        </div>
    </vx-card>
    </template>
    
    <script>
    import Multiselect from 'vue-multiselect'
    import moment from "moment"
    import "vue-multiselect/dist/vue-multiselect.min.css"    
    export default {
        components: {
            Multiselect,
            //Timeline,
        },
        watch:{
            enviar_bodega_vencido(bodegaVencido){
                if(!this.cod_departamentoDestino && this.cod_departamentoDestino.length == 0)
                    return
                
                if(bodegaVencido && this.id_bodega_fuente==5)
                    return

                for(const bodega of this.Lista_bodegas_fuente){          
                    if(bodegaVencido){
                        if(bodega.CODIGO == 5){
                            this.id_bodega_fuente = bodega.CODIGO
                            this.cod_bodegaFuente = bodega
                            break
                        }
                        
                    }                                                                                 
                }                    

            }
        },
        data() {
            return {
                Estado_VentanaEmergente_Busqueda: false,
                cb_lista_operacion: '',
                Descripcion_Emergente: '',
                Consulta_Pedido:false,
                Consulta_Aceptados:false,
                producto_buscar: '',                
                lista_estado: [{
                        ID: 'TP',
                        DESCRIPCION: 'Requerimientos'
                    },
                    {
                        ID: 'P',
                        DESCRIPCION: 'Enviado Devolución'
                    },
                    {
                        ID: 'T',
                        DESCRIPCION: 'Aceptado Devolución'
                    }
                ],
                Id_estado_seleccionado: 0,
                fecha_inicio: '',
                fecha_final: '',
                Lista_vista: [],
                Estado_Emergente_Consulta: false,
                Estado_Emergente: false,
                FechaSolicitud:'',
                FechaEntrega:'',
                enviar_bodega_vencido: false,
                permisos_tipos_requerimientos: [],
                permiso_bodega_vencido:'',
                Lista_bodegas_fuente: [],
                Lista_Departamentos_destino_consulta: [],
                id_bodega_fuente: '',
                id_bodega_destino: '',
                nombre_bodega_fuente: '',
                nombre_bodega_destino: '',
                id_departamento_destino: '',
                nombre_departamento_destino: '',
                cod_bodegaFuente: '',
                cod_departamentoDestino: '',    
                Producto_seleccionado: {
                    nombre: '',
                    marca_comercial: '',
                    Principio_activo: '',
                    Concentracion: '',
                    Presentacion: '',
                    Codigo: '',
                    codigo_interno: '',
                    Activo: 'N'
                },
                IdCodigoRequerimiento: 0,
                IdCodigoMovimiento: 0,
                Cantida_solicitar: '',    
                Lista_detalle: [],
                Deshabilitar_campos: false,
                producto: [],    
                Observaciones: '',
                Requerimiento_Seleccionado: {},   
                permisos_tipos_bodegas: [],
                Titulo_pantalla: {
                    Titulo_bodega1: 'Bodega de Despacho:',
                    Titulo_solicitud: 'Nº. Requerimiento',   
                },
                listado_reportes: [],
                listado_source: {
                    CODIGO_SOLICITUD: '',
                    CODIGO_MOVIMIENTO: '',
                    CODIGO_REQUERIMIENTO: '',
                }                                
            }
        },
        mounted() {
    
            this.cb_lista_operacion = {
                ID: 'TP',
                DESCRIPCION: 'Requerimientos'
            }
            this.Id_estado_seleccionado = 'TP';                
            this.$refs.tablaBusqueda.actualizacionEncabezado()
            for(let privilegio of this.$store.state.privilegios){
               if(privilegio.Privilegio.includes("TIPO_REQUERIMIENTO")){                      
                   let tipobodega = privilegio.Privilegio.split('_')[2];
                   if(tipobodega == 'T'){
                     this.permiso_bodega_vencido = 'S'
                   }else{
                     this.permisos_tipos_bodegas.push(privilegio.Privilegio.split('_')[2])                    
                   }                                                       
                }
            }      
            this.Consultar_Bodega('L',this.permisos_tipos_bodegas.join(","),this.permiso_bodega_vencido);             
        },
        computed: {
            sesion() {
                
                return this.$store.state.sesion
            }
        },
        async beforeCreate(){
            this.listado_reportes = await this.$recupera_parametros_reporte('Movimiento Requerimiento')                        
        },
        methods: {
            async Consultar_Movimiento(datos) {       
                
                
                this.listado_source.CODIGO_MOVIMIENTO = datos.IDCODIGOMOVIMIENTO
                this.$genera_reporte({
                    Nombre: "Movimiento Requerimiento",
                    Data_source: this.listado_source,
                    Data_report: this.listado_reportes
                }).catch(() => {
                })
            },
            GetDateValue(value) {
                if (value === undefined) {
                    return '';
                } else if (value === null) {
                    return '';
                } else if (value == '') {
                    return '';
                }
                return moment(value).format('DD/MM/YYYY');          
            },
            VerificarExistencias(producto){                
                if(Number(producto.CANTIDAD) < (Number(producto.CANTIDAD_DEVOLVER)+Number(producto.CANTIDAD_DEVUELTA))){
                    this.$vs.notify({
                            position:'top-center',
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'No puede devolver mas a la cantidad aceptada',
                        });
                    producto.CANTIDAD_DEVOLVER = 0
                }
                if(Number(producto.CANTIDAD_DEVOLVER) < 0){
                    this.$vs.notify({
                            position:'top-center',
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'No puede ingresar cantidades negativas',
                        });
                    producto.CANTIDAD_DEVOLVER = 0
                }
            },
            Operacion_seleccionada({
                DESCRIPCION
            }) {
                return `${DESCRIPCION} `
            },
            titulosConsulta(estado){
                
                if (estado == 'TP'){                             
                    this.Titulo_pantalla.Titulo_bodega1  = 'Bodega de Despacho:'
                    this.Titulo_pantalla.Titulo_solicitud  = 'Nº. Requerimiento'
                }                             
                else{
                    this.Titulo_pantalla.Titulo_bodega1  = 'Bodega Destino:'
                    this.Titulo_pantalla.Titulo_solicitud  = 'Nº. Movimiento'
                }
                
            },
            onChangeEstado(value) {
                if (value !== null && value.length !== 0) {
                    if (value.ID == 'S')
                        this.Id_estado_seleccionado = 'P';
                    else
                        this.Id_estado_seleccionado = value.ID;
                    //let columna = this.$refs.tb_lista_busqueda.getColumnByField("id")
                    //columna.headerText = "ChangedText"
                    this.Consultar_OrdenEnc();  
                    this.titulosConsulta(this.Id_estado_seleccionado);  

                    setTimeout(()=>{
                        this.$refs.tablaBusqueda.actualizacionEncabezado()
                    },1000);                           

                } else {
                    this.Id_estado_seleccionado = '';
                }
            },
            Bodegas_seleccionado({
                CODIGO,NOMBRE
            }) {
                return `${CODIGO} - ${NOMBRE} `;
            },
            Departamentos_seleccionado({
                NOMBRE
            }) {
                return ` ${NOMBRE} `;
            },
            onChangeBodega(value) {
                if(!value){
                    this.cod_bodegaFuente = ''
                    this.id_bodega_fuente = ''
                    return
                }
                if (value !== null && value.length !== 0) {
                    this.id_bodega_fuente = value.CODIGO;                    
                    this.Consultar_OrdenEnc();  
                } else {
                    this.id_bodega_fuente = '';
                }
            },
            onChangeBodegaFuente(value) {
                if (value !== null && value.length !== 0) {
                    this.id_bodega_fuente = value.CODIGO;
                } else {
                    this.id_bodega_fuente = '';
                }                
            },
            onChangeDepartamentoDestino(value) {
                if (value !== null && value.length !== 0) {
                    this.id_departamento_destino = value.CODIGO;
                    this.Consultar_Bodega('L',this.id_departamento_destino);
                    if(this.enviar_bodega_vencido && this.id_bodega_fuente==5)
                        return

                    for(const bodega of this.Lista_bodegas_fuente){          
                        if(this.enviar_bodega_vencido){
                            if(bodega.CODIGO == 5){
                                this.id_bodega_fuente = bodega.CODIGO
                                this.cod_bodegaFuente = bodega
                                break
                            }
                            
                        }                                      
                        else if( bodega.TipoBodega == value.TipoBodega){                                                
                            this.id_bodega_fuente = bodega.CODIGO
                            this.cod_bodegaFuente = bodega
                            break
                        }                            
                    }                    
                } else {
                    this.cod_bodegaFuente = '';
                    this.id_bodega_fuente = '';
                }
            },
            Emergente_Detalle_Consulta(Datos) {
                this.Estado_Emergente_Consulta = true;
                this.Requerimiento_Seleccionado = Datos;
                       
                
                
                
                

                this.Observaciones = Datos.OBSERVACIONES;                
                this.IdCodigoMovimiento = Datos.IDCODIGOMOVIMIENTO;
                this.id_bodega_destino = Datos.IdBodegaDestinofk;
                this.id_departamento_destino = Datos.IDDEPARTAMENTO;
                this.nombre_bodega_destino = Datos.NOMBRE_BODEGA_DESTINO;
                this.nombre_departamento_destino = Datos.NOMBREDEPARTAMENTO;
                this.FechaSolicitud = Datos.FECHASOLICITUD;
                this.FechaEntrega = Datos.FECHAENTREGADA;
                this.Deshabilitar_campos = true;
                this.Descripcion_Emergente = 'Consulta Detalle Devolución Requerimiento';
                this.Consultar_OrdenDetMovimiento()
    
            },
            Emergente_Detalle_Devolucion(Datos) {
               // 
               // 
                this.Estado_Emergente = true;
                this.Consulta_Pedido = false,
                this.Consulta_Aceptados = false,
                this.producto_buscar = '';
                this.Cantida_solicitar = '';
                this.Requerimiento_Seleccionado = Datos;
                this.Observaciones = Datos.OBSERVACIONES;
                this.IdCodigoRequerimiento = Datos.IDCODIGOREQUERIMIENTO;
                this.id_bodega_fuente = Datos.IdBodegaFuentefk;
                this.id_departamento_destino = Datos.IDDEPARTAMENTO;
                this.nombre_bodega_fuente = Datos.NOMBRE_BODEGA_FUENTE;
                this.nombre_departamento_destino = Datos.NOMBREDEPARTAMENTO;

                this.Deshabilitar_campos = true;
                this.Descripcion_Emergente = 'Ingresar Solicitud Devolución';
                this.Consultar_OrdenDet()
    
            },
            Limpiar_Campos() {
                this.Producto_seleccionado.Codigo = '';
                this.Producto_seleccionado.marca_comercial = '';
                this.Producto_seleccionado.Principio_activo = '';
                this.Producto_seleccionado.Concentracion = '';
                this.Producto_seleccionado.codigo_interno = '';
                this.Producto_seleccionado.Presentacion = '';
            },
            Seleccionar_producto(obj) {
    
                this.Producto_seleccionado.Codigo = obj.Codigo;
                this.Producto_seleccionado.marca_comercial = obj.Descripcion;
                this.Producto_seleccionado.Concentracion = obj.Concentracion;
                this.Producto_seleccionado.codigo_interno = obj.codigo_interno;
                this.Producto_seleccionado.Presentacion = obj.PresentacionNombre;
                document.getElementById("cantidad_sol").focus();
                this.Estado_VentanaEmergente_Busqueda = false;
    
            },
            Mostrar_resultado_producto(value) {
                if (value.length === 1) {
                    value.map(obj => {
                        this.Producto_seleccionado.Codigo = obj.Codigo;
                        this.Producto_seleccionado.marca_comercial = obj.Descripcion;
                        this.Producto_seleccionado.Concentracion = obj.Concentracion;
                        this.Producto_seleccionado.codigo_interno = obj.codigo_interno;
                        this.Producto_seleccionado.Presentacion = obj.PresentacionNombre;
                        document.getElementById("cantidad_sol").focus();
    
                    })
    
                } else if (value.length > 1) {
                    this.Estado_VentanaEmergente_Busqueda = true;
                }
    
            },
            Validacion_Campos(Tipo, Nombre, valor, obligatorio, cant_maxima) {
                /**
                 * @General
                 * Tipo : N = Númerio, Nombre = Descripción del campo, Valor = A evaluar, Obligatorio= Verdadero o falso,cant_maxima  = Aplica unicamente para un string y define la cantidad de caracteres 
                 * Devuelve un mensaje al usuario si las validaciones no se cumple y aborta, de lo contrario devuelve verdadero
                 */
                if (Tipo == 'ID') {
                    if (valor <= 0) {
                        this.$vs.notify({
                            position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else if (Tipo == 'N') {
    
                    if (valor <= 0 || valor === "") {
                        this.$vs.notify({
                            position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else {
                    if (valor == '' && obligatorio == true) {
                        this.$vs.notify({
                            position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
    
                        if (cant_maxima < valor.length) {
                            this.$vs.notify({
                            position:'top-center',
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: 'Campo ' + Nombre + ' no debe superar los ' + cant_maxima + ' carácteres.',
                            });
                            return false;
                        } else {
                            return true;
                        }
    
                    }
                }
            },
            /*****************************CONSULTAR INFORMACION*************/
            Consultar_OrdenEnc() {
                if(!this.Id_estado_seleccionado || !this.cod_bodegaFuente ){
                    this.$vs.notify({
                            position:'top-center',
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: 'Seleccione una bodega de despacho y un estado para realizar la consulta',
                            })
                    this.Lista_vista = [];
                    return
                }

                if (this.Id_estado_seleccionado == 'TP'){
                    this.axios.post('/app/v1_OrdenCompra/ConsultasRequerimientos', {                        
                        Estado: 'T',
                        FechaInicio: this.GetDateValue(this.fecha_inicio),
                        FechaFin: this.GetDateValue(this.fecha_final),
                        Opcion: 'C',
                        SubOpcion: 'R',
                        BodegaFuente: this.id_bodega_fuente
                    })
                    .then(resp => {
                        this.titulo_bodega = 'Departamento Fuente'
                        
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                            position:'top-center',
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_vista = [];
                        } else {
                            this.$vs.notify({
                                                position:'top-center',
                                                color: 'success',
                                                title: 'Inventario',
                                                text: 'Se actualizaron los requerimientos exitosamente',
                                            })
                            this.Lista_vista = resp.data.json;
                        }
                    })
                    .catch(() => {})                    
                }else{
                    this.axios.post('/app/v1_OrdenCompra/ConsultasRequerimientos', {                        
                        Estado: this.Id_estado_seleccionado,
                        FechaInicio: this.GetDateValue(this.fecha_inicio),
                        FechaFin: this.GetDateValue(this.fecha_final),
                        Opcion: 'M',
                        SubOpcion: 'C',
                        BodegaDestino: this.id_bodega_fuente
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                            position:'top-center',
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_vista = [];
                        } else {
                            this.$vs.notify({
                                                position:'top-center',
                                                color: 'success',
                                                title: 'Inventario',
                                                text: 'Se actualizaron los requerimientos exitosamente',
                                            })
                            this.Lista_vista = resp.data.json;
                        }
                    })
                    .catch(() => {})
                }               
            },
            Consultar_OrdenDetMovimiento() {            
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/ConsultasRequerimientos', {
                        Opcion: 'M',
                        SubOpcion: 'D',
                        IdMovimiento: this.IdCodigoMovimiento
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                            position:'top-center',
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_detalle = [];
                        } else {
                            this.Lista_detalle = resp.data.json
                        }
                    })
                    .catch(() => {})
            },
            Consultar_OrdenDet() {
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/ConsultasRequerimientos', {
                        Opcion: 'C',
                        SubOpcion: 'D',
                        IdRequerimiento: this.IdCodigoRequerimiento
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                            position:'top-center',
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_detalle = [];
                        } else {
                            this.Lista_detalle = resp.data.json.map(producto => ({...producto, CANTIDAD_DEVOLVER: 0}));                            
                            
                        }
                    })
                    .catch(() => {})
            },
            Consultar_Departamentos(ListaTipoBodegas) {
                const url = this.$store.state.global.url
                this.axios.post(url+'app/v1_OrdenCompra/ConsultasRequerimientos', {
                                                                        Opcion: 'D',
                                                                        SubOpcion: 'P',
                                                                        ListaTipoBodegas: ListaTipoBodegas
                                                                     })
                           .then(resp => {
            
                                if (resp.data.codigo != 0) {
                                    this.$vs.notify({
                            position:'top-center',
                                        color: '#B71C1C',
                                        title: 'Departamentos',
                                        text: resp.data.mensaje,
                                    })
                                } else {
                                    this.Lista_Departamentos_destino_consulta = resp.data.json;                         
                                }
                            })
                           .catch(() => { })
        
            },
            Consultar_Bodega(Operacion, ListaTipoBodegas) {
                const url = this.$store.state.global.url
                this.axios.post(url+'app/v1_OrdenCompra/consulta_bodega', {
                                                                        operacion: Operacion,
                                                                        lista_tipo_bodegas: ListaTipoBodegas
                                                                     })
                           .then(resp => {
            
                                if (resp.data.codigo != 0) {
                                    this.$vs.notify({
                            position:'top-center',
                                        color: '#B71C1C',
                                        title: 'Bodegas',
                                        text: resp.data.mensaje,
                                    })
                                } else {
                                        this.Lista_bodegas_fuente = resp.data.json;   
                                      //  this.Lista_departamentos_destino= resp.data.json;                         
                                }
                            })
                           .catch(() => { })
        
            },    
            defecto(){},
            /*************************** ACTUALIZAR, ELIMINAR NUEVO REGISTRO */  
            Registrar_RecibidoDet(datos, operacion) {
                
                
               // 
    
                /* VALIDACION DE ARRAY */
    
                this.axios.post('/app/v1_OrdenCompra/Requisicion_Det', {
                        Requisicion_det: datos.IDREQUISICIONDET,
                        Requisicion_enc: datos.IDCODIGOREQUERIMIENTO,
                        Producto: datos.IDPRODUCTOFK,
                        cant_pedida: 0,
                        cant_entregada: 0,
                        cant_recibida: datos.CANTIDAD_RECIBIDA,
                        Operacion: operacion,
                    })
                    .then(resp => {
    
                        if (resp.data.codigo == 0) {
                            this.Consultar_OrdenDet();
                        }
                    })
    
            },
            Eliminar_Registro(value) {
                /**
                 * @General
                 * Función eliminar registro;
                 */
                
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',
                    acceptText: 'Aceptar',
                    cancelText: 'Cancelar',
                    text: '¿Dar de baja Pedido  \'' + value.DESCRIPCION_PRODUCTO + '\'   \'' + value.DESCRIPCION_UNIDAD + '\' ? ',
                    accept: () => {
                        this.axios.post('/app/v1_OrdenCompra/ManejoRequerimientos', {
                                CodProducto: value.IDPRODUCTOFK,
                                NoRequerimiento: this.IdCodigoRequerimiento,
                                TipoUpdate: 21
                            })
                            .then(resp => {
    
                                if (resp.data.tipo_error >= 0) {
                                    this.Consultar_OrdenDet();
                                    document.getElementById("busquedaProducto").focus();
                                }
                            })
                        
                    }
                })
    
            },
            Finalizar_DevolucionRequerimineto() {
                
                let Lista_devolucion = []
                for (var i = 0; i < this.Lista_detalle.length; i++) {
                    if(Number(this.Lista_detalle[i].CANTIDAD) < (Number(this.Lista_detalle[i].CANTIDAD_DEVOLVER) + Number(this.Lista_detalle[i].CANTIDAD_DEVUELTA))  ){                        
                        this.$vs.notify({
                            position:'top-center',
                            color: '#B71C1C',
                            title: 'Devolución Requerimientos',
                            text: 'La linea ' +(i+1)+ ' no puede devolver mas de las unidades aceptadas',
                            time: 6000
                        });
                        return false;
                    }
                    if(this.Lista_detalle[i].CANTIDAD_DEVOLVER >0){
                        Lista_devolucion.push({IDPRODUCTOFK:this.Lista_detalle[i].IDPRODUCTOFK,
                                               CANTIDAD_DEVOLVER:this.Lista_detalle[i].CANTIDAD_DEVOLVER})
                    }                   
                }

                if(Lista_devolucion.length == 0){
                    this.$vs.notify({
                            position:'top-center',
                            color: '#B71C1C',
                            title: 'Devolución Requerimientos',
                            text: 'Debe ingresar la cantidad a devolver al menos a 1 producto',
                            time: 6000
                        });
                    return false;
                }

                this.axios.post('/app/v1_OrdenCompra/ManejoRequerimientosDevoluciones', {
                            NoRequerimiento: this.IdCodigoRequerimiento,
                            ListaProductos: Lista_devolucion.map((producto)=>producto.IDPRODUCTOFK).toString(),
                            CantidadesDevolucion: Lista_devolucion.map((producto)=>producto.CANTIDAD_DEVOLVER).toString(),
                            TipoUpdate: 40
                        })
                        .then(resp => {
                            
                            if (resp.data.tipo_error >= 0) {
                                this.Estado_Emergente = false;
                                this.Consultar_OrdenDet();
    
                            }
                            
                        })                   
    
            }
        }
    
    }
    </script>
    