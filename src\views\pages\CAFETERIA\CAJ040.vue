<template>
<vx-card type = "2" :title="`Punto de Venta de Cafetería - ${sesion.sesion_sucursal_nombre}`" class="justify-center caj040" >
    <buscador ref="BuscarUsuarioCorporativo" 
            style="z-index:9999999"
            buscador_titulo="Buscador / Corporativo" :api="'app/Ajenos/Busqueda_Corporativo'" 
            :campos="['Corporativo', ['Nombres', 'Nombre'], ['Apellidos', 'Apellido']]"
            :titulos="['Corporattivo', 'Nombres', 'Apellidos']"
            :api_filtro="{ Corporativo:corporativoPedido}"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" :api_preload="true"  />
    <DxPopup :visible.sync="popUpSensorHuellas" :width="'60%'" :height="'60%'" 
             :show-title="true" :full-screen="false" :hide-on-outside-click="false"        
             title="Validación de Huella" :showCloseButton="true">
        <ValidarHuella ref="validarHuellas"  tipoHuella="1" :corporativoBuscar="corporativoHuella" @getValidacionHuella="getValidacionHuella"/>
    </DxPopup>   
    <vs-popup title="Desglose de Pago" :active.sync="popUpFormaPago" class="caj040">
        <vs-row>     
            <vs-row class="w-full" vs-justify="left">
                <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-1/12 p-1" style="text-align: right">
                    <label class="typo__label label-total centered-element">
                        <b>Total:</b>
                    </label>
                </vs-col>
                <vs-col class="sm:w-full md:w-full lg:w-5/12 xl:w-3/12 p-1" style="background-color: #b2c7a3; text-align: center;">
                    <label class="typo__label label-total centered-element">
                        {{ $formato_moneda(factura.totalFactura) }}
                    </label>
                </vs-col>                    
            </vs-row>           
            <vs-row class="w-full">
                <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-3/12 p-1" >
                    <label class="typo__label"> Monto Tarjeta 1</label>   
                    <DxNumberBox            
                            :min="0"
                            :show-spin-buttons="false"
                            @key-up="actualizarSaldoFactura"
                            id="tarjeta1"
                            v-model=factura.tarjeta1.monto format="Q #,##0.00" style="padding: 2px;"/>
                </vs-col>
                <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-2/12 p-1">                                                      
                    <div >Tipo Facturación</div>
                    <div>
                        <DxRadioGroup
                            :items="TiposPos"
                            value-expr="Id"                           
                            display-expr="Nombre"
                            v-model="factura.tarjeta1.posUtilizado"  
                            layout="horizontal"                                                      
                        />
                    </div>
                </vs-col>
                <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-3/12 p-1" v-if="false"> 
                    <vs-input label="No. Tarjeta"
                            class="w-full"
                            v-model="factura.tarjeta1.noTarjeta"/> 
                </vs-col>
                <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-3/12 p-1"> 
                    <vs-input label="No. Autorización"
                            class="w-full"
                            v-model="factura.tarjeta1.noAutorizacion"/> 
                </vs-col>
            </vs-row>
            <vs-row class="w-full">
                <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-3/12 p-1" >
                    <label class="typo__label"> Monto Tarjeta 2</label>   
                    <DxNumberBox            
                            :min="0"
                            :show-spin-buttons="false"
                            @key-up="actualizarSaldoFactura"
                            id="tarjeta2"
                            v-model=factura.tarjeta2.monto format="Q #,##0.00" style="padding: 2px;"/>
                </vs-col>
                <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-2/12 p-1">                                                      
                    <div >Tipo Facturación</div>
                    <div>
                        <DxRadioGroup
                            :items="TiposPos"
                            value-expr="Id"
                            display-expr="Nombre"
                            v-model="factura.tarjeta2.posUtilizado" 
                            layout="horizontal"                                                       
                        />
                    </div>
                </vs-col>
                <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-3/12 p-1" v-if="false"> 
                    <vs-input label="No. Tarjeta"
                            class="w-full"
                            v-model="factura.tarjeta2.noTarjeta"/> 
                </vs-col>
                <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-3/12 p-1"> 
                    <vs-input label="No. Autorización"
                            class="w-full"
                            v-model="factura.tarjeta2.noAutorizacion"/> 
                </vs-col>
            </vs-row>
            <vs-row class="w-full">
                <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-3/12 p-1" >
                    <label class="typo__label" style="word-wrap: break-word; white-space: nowrap;"> Efectivo (Cantidad que recibe)</label>   
                    <DxNumberBox            
                            :min="0"
                            :show-spin-buttons="false"
                            @key-up="actualizarSaldoFactura"
                            id="efectivo"
                            v-model=factura.efectivo.monto format="Q #,##0.00" style="padding: 2px;"/>
                </vs-col>
                <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1" >
                    <label class="typo__label"> Vuelto </label>   
                    <DxNumberBox            
                            :min="0"
                            :show-spin-buttons="false"
                            v-model=factura.efectivo.vuelto format="Q #,##0.00" style="padding: 2px; background-color:#a4dcfc;"/>
                </vs-col>
                <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1" >
                    <label class="typo__label"> Saldo </label>   
                    <DxNumberBox            
                            :min="0"
                            :show-spin-buttons="false"
                            v-model=factura.efectivo.saldo format="Q #,##0.00" style="padding: 2px; background-color: #f6ff00;"/>
                </vs-col>
            </vs-row>
            <vs-row class="w-full flex">
                <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1 pt-6"> 
                        <vs-button color="primary" icon-pack="feather" icon="icon-settings" class="w-full" id="botonFacturar" 
                                   :disabled="!configuracion.activarFacturacion" @click="consulta().generarFacturaInterna()">
                            Facturar
                        </vs-button>
                    </vs-col>
                    <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1 pt-6"> 
                        <vs-button @click="popUpFormaPago = false" color="warning" class="w-full pl-5 pr-5">
                            Cancelar
                        </vs-button>
                    </vs-col>
            </vs-row>
        </vs-row>        
    </vs-popup>
    <vs-popup classContent="popup-example" :title="'Huella Empleado Mesa '+pedido?.mesaSeleccionada?.Codigo" :active.sync="popUpHuellaMesa"  id="div-with-loading" class="vs-con-loading__container caj040" style="z-index: 50;">
        <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px;" >              
                <vs-row vs-align="center">
                        <vs-input label="Corporativo:" v-model="corporativoHuella" @keyup.enter="BuscarCorporativo(false)" @keydown.tab="BuscarCorporativo(false)" ></vs-input>
                        <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="BuscarCorporativo(false)" icon="fa-search" style="margin-top: 16px;"></vs-button>
                        <vs-button id="button-with-loading" color="danger" v-if="usuarioHuella" icon-pack="fa" @click="usuarioHuella=null; corporativoHuella=null;" icon="fa-times" style="margin-top: 16px;"></vs-button>
                        <div style="margin-left:20px;">
                            <h5>{{ usuarioHuella?.Nombres }}  {{ usuarioHuella?.Apellidos }}</h5>
                        </div>
                </vs-row>
                <vs-divider></vs-divider>

                <vs-row class="w-full" vs-align="left">
                    <vs-button color="primary" style="float:right" type="filled" icon-pack="feather" icon="icon-save" :disabled="usuarioHuella==null" @click="ValidaHuellaActiva()">Validar</vs-button>
                    <vs-button color="warning"  style="float:right; margin-left:10px;" @click="popUpHuellaMesa = false; corporativoHuella=null; usuarioHuella=null;">Cancelar</vs-button>                   
                </vs-row>
        </div>
    </vs-popup>
    <vs-popup classContent="popup-example" title="Datos Pedido" :wrapper-attr="{class:'caj040'}" :active.sync="popUpDatosPedido" class="vs-con-loading__container"  style="z-index: 100;">
        <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px;" >
                <vs-row>
                    <vs-col >
                        <label style="width: 200px; color:rgba(0,0,0,.7)">No. Pedido</label>
                        <DxSelectBox  style="width: 200px; font-size: .85rem;"                                     
                                    class="numeros-pedidos"                         
                                    id="custom-templates"
                                    :data-source="listas.numeroPedidos"
                                    display-expr="value"  
                                    value-expr="value"                              
                                    v-model="pedido.numeroPedidoSeleccionado"
                                ></DxSelectBox>
                    </vs-col>
                </vs-row>
                <vs-divider></vs-divider>
                &nbsp;
                <vs-row vs-align="center">
                    <vs-col vs-type="flex" vs-align="center" vs-w="6">
                        <vs-input v-model="nombrePedido" label="Nombre:"></vs-input>
                    </vs-col>
                </vs-row>
                <vs-row class="p-4" vs-align="center">
                    <vs-col vs-type="flex" vs-align="center" vs-w="6">
                        <h6>Ó</h6>                               
                    </vs-col>
                </vs-row>
                <vs-row vs-align="center">
                        <vs-input label="Corporativo:" v-model="corporativoPedido" @keyup.enter="BuscarCorporativo(true)" @keydown.tab="BuscarCorporativo(true)" ></vs-input>
                        <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="BuscarCorporativo(true)" icon="fa-search" style="margin-top: 16px;"></vs-button>
                        <vs-button id="button-with-loading" color="danger" v-if="usuarioPedido" icon-pack="fa" @click="usuarioPedido=null; corporativoPedido=null;" icon="fa-times" style="margin-top: 16px;"></vs-button>
                        <div style="margin-left:20px;">
                            <h5>{{ usuarioPedido?.Nombres }}  {{ usuarioPedido?.Apellidos }}</h5>
                        </div>
                </vs-row>
                <vs-divider></vs-divider>

                <vs-row class="w-full" vs-align="left">
                    <vs-button color="primary" style="float:right" type="filled" icon-pack="feather" icon="icon-save" @click="consulta().agregarPedido()"> Guardar</vs-button>
                    <vs-button color="warning"  style="float:right; margin-left:10px;" @click="popUpDatosPedido = false; pedido.paraRecoger=false; ">Cancelar</vs-button>                   
                </vs-row>
        </div>
    </vs-popup>
    <vs-popup classContent="popup-example" title="Ingreso Nota Comanda" :active.sync="popUpNotaComanda" id="div-with-loading" class="vs-con-loading__container caj040" style="z-index: 100;">
        <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5); margin-bottom:20px;" >
                <vs-row>
                    <vs-col>
                        <vs-textarea class="w-full" label="Nota" counter="200" v-model="textoNota" />
                    </vs-col>
                </vs-row>
                <vs-divider></vs-divider>
                &nbsp;                              
                <vs-row class="w-full" vs-align="left">
                    <vs-button color="primary" style="float:right" type="filled" icon-pack="feather" icon="icon-save" @click="consulta().grabarNota()">Guardar</vs-button>
                    <vs-button color="warning"  style="float:right; margin-left:10px;" @click="popUpNotaComanda=false;">Cancelar</vs-button>                   
                </vs-row>
        </div>
    </vs-popup>
    <SM-Validar-Pass
      ref="refValidarPass"
      in_titulo="Validación credenciales"
    />
    <vs-row class="w-full p-0">
        <ConfiguracionCaja ref="ConfiguracionCajaFactura" @CargarCaja="CargarCaja" TipoCajaSeleccionada="TipoCajaCafeteria" 
                           CajaSeleccionada="CajaCafeteria" AgrupacionCaja="CajaCafeteria" :MostrarMonitor=true 
                           :FacturaCambiaria=0 class="w-full p-0">
            <vs-row class="w-full flex justify-end">
                <div class="w-5/12 pl-8 pr-2">
                    <vs-button @click="consulta().validarCorte()" color="primary" :disabled="!configuracion.activarFacturacion" >Corte</vs-button>
                </div>
                <div class="w-4/12 pr-2">
                    <vs-button @click="realizarArqueo()" color="primary" :disabled="!configuracion.activarFacturacion">Arqueo</vs-button>
                </div>                        
                <div class="w-2/12 pr-2 " >            
                    <vx-tooltip text="Reimprimir Ultimo Corte" style="display:inline-block;margin-right:30px">
                        <vs-button  style="height: 35px;" color="green" 
                            icon-pack="feather" icon="icon-refresh-cw"  
                            :disabled="!configuracion.activarFacturacion"
                            @click="consulta().reimpresionCorte()">
                        </vs-button>     
                    </vx-tooltip>       
                </div>
                <vs-col class="w-1/12" >       
                    <vx-tooltip text="Reimprimir Ultima Factura" style="display:inline-block;margin-right:30px">
                        <vs-button  style="height: 35px;" color="success" 
                            icon-pack="feather" icon="icon-file-text"
                            :disabled="!configuracion.activarFacturacion" 
                            @click="consulta().reimpresionFactura()">
                        </vs-button>
                    </vx-tooltip>     
                </vs-col>
            </vs-row>       
        </ConfiguracionCaja>
    </vs-row>                  
    <vs-row class="w-full">
        <vs-col class="xs:w-full md:w-full lg:w-7/12 xl:w-7/12 pr-4">
            <vs-row class="w-full">
                <vs-col class="xs:w-5/12 md:w-5/12 lg:w-3/12 xl:w-3/12">
                    <label>Mesa:</label><br>
                    <DxSelectBox                                
                                id="selectMesa"
                                :style="{'background-color': pedido.mesaSeleccionada?'#008FBE':''}" 
                                :search-enabled="true"
                                :data-source="listas.mesas"
                                display-expr="Descripcion"                                
                                v-model="pedido.mesaSeleccionada"
                                field-template="mesasTemplate"
                                item-template="mesasTemplate"
                                @value-changed="consulta().cargarMesa()"
                            >
                            <template #mesasTemplate ="{ data }">
                                <vs-row style="padding: 0px;" class="filaSelection">
                                    <DxTextBox  :value="data?.Descripcion"                                                 
                                                style="border: 0px; max-width: 75px; padding: 0px; background-color: transparent; cursor: pointer;"
                                                class="filaSelection"
                                                />
                                    <span v-if="data?.Status == 'A'" class="material-icons" style="color:green; padding-top:4px;">done</span>
                                </vs-row>
                            </template>
                    </DxSelectBox>
                </vs-col>
                <vs-col class="xs:w-7/12 md:w-7/12 lg:w-4/12 xl:w-4/12 pl-4">
                    <label>Pedidos:</label><br>
                    <DxSelectBox    id="selectPedido"      
                                    :style="{'padding': '2px', 'background-color': pedido.pedidoSeleccionado?'#008FBE':''}"                                
                                    :items="listas.pedidos"
                                    :search-enabled="true"
                                    placeholder="Elegir Pedidos.."
                                    display-expr="Descripcion"                                    
                                    v-model="pedido.pedidoSeleccionado"
                                    @value-changed="consulta().cargarPedido()"
                                />    
                </vs-col>
                <vs-col class="xs:w-full md:w-full lg:w-5/12 xl:w-5/12 pl-4 pr-4">
                    <vs-row class="w-full">
                       <vs-col class="w-7/12 pt-5">
                         <vs-button @click="CargarNuevoPedido()" :class="{botonApagado: !pedido.paraRecoger, botonActivo: pedido.paraRecoger}" class="pl-3 pr-3" style="white-space: nowrap;">Para Llevar</vs-button>
                       </vs-col>
                       <vs-col class="w-3/12 pt-5">
                         <vs-button  @click="consulta().eliminarComanda()" class="pl-4 pr-4" color="danger" :disabled="productosPedidos.length <= 0" >Eliminar</vs-button>
                       </vs-col>
                    </vs-row>

                </vs-col>                      
            </vs-row>
            <vs-row class="w-full pb-2 pt-2">
                <vs-col class="xs:w-9/12 md:w-9/12 lg:w-7/12 xl:w-7/12">
                    <!-- Busqueda Producto. -->
                    <div class="vs-component vs-con-input-label vs-input w-full vs-input-shadow-drop vs-input-no-border d-theme-input-dark-bg vs-input-primary">
                        <div class="vs-con-input">
                            <input type="text" class="vs-inputx vs-input--input medium" placeholder="Buscar Producto:" style="padding: 4px !important;" v-model="busquedaProducto">
                        </div>
                    </div>
                </vs-col>   
                <vs-col class="xs:w-3/12 md:w-3/12 lg:w-5/12 xl:w-5/12 pl-3">
                    <div class="vs-con-input" v-if="this.nivelPrecios == 30">
                        *Pedido con descuento
                    </div>
                </vs-col>            
            </vs-row>  
            <vs-row class="w-full">
                <DxTabs :selected-index="listas.categorias.findIndex(c=>c?.CodigoCategoria==seleccionCategoria)" height="10%" width="calc(100% - 5px)" :rtl-enabled="false" orientation="horizontal" 
                        :show-nav-buttons="true" :scroll-by-content="true" 
                        @item-click="(e)=>filtrarSubDepartamento(listas.categorias[e.itemIndex])" class="flex "> 
                    <DxTabPanelItem v-for="(item, index) in listas.categorias" :key="index" template="tabButton" class="w-1/6"/>
                    <template #tabButton="{index}">
                            <div class="vx-card w-full p-1" :class="listas.categorias[index].CodigoCategoria === seleccionCategoria ? 'tabSeleccionado': 'tabLibre'">
                                <div @click="filtrarSubDepartamento(listas.categorias[index])" >
                                    <div style="height: 20%;">
                                        <font-awesome-icon :icon="['fas', listas.categorias[index].Icono?listas.categorias[index].Icono:'mug-hot']" style="font-size: 15px;" />
                                    </div>
                                    <div  
                                        style="word-wrap: break-word; white-space: nowrap; height: 80%; text-align: center; display: flex; justify-content: center; align-items: center;">                                        
                                        {{listas.categorias[index]?.Nombre}}
                                    </div>
                                </div>
                            </div>
                    </template>
                </DxTabs>
            </vs-row>
            <vs-row class="w-full">
                <DxTabs :selected-index="listas.subDepartamentosFiltrados.findIndex(d=>d?.CodigoSubDepartamento==seleccionSubDepartamento)" height="10%" width="calc(100% - 5px)" :rtl-enabled="false" orientation="horizontal" 
                        :show-nav-buttons="true" :scroll-by-content="true" 
                        @item-click="(e)=>consulta().cargaProductos(listas.subDepartamentosFiltrados[e.itemIndex])" class="flex p-0 m-0">
                    <DxTabPanelItem v-for="(item, index) in listas.subDepartamentosFiltrados" :key="index" template="tabButton2"/>
                    <template #tabButton2="{index}" >
                        <div class="vx-card grid-view-item overflow-hidden categoriasProductos p-0 m-0" 
                             :class="listas.subDepartamentosFiltrados[index]?.CodigoSubDepartamento === seleccionSubDepartamento ? 'tabSeleccionado': 'tabLibre'"
                             style="text-align: center; justify-content: center;  align-items: center; ">
                            <div  @click="consulta().cargaProductos(listas.subDepartamentosFiltrados[index])">
                                <div style="word-wrap: break-word; white-space: wrap; height: 100%; width: 100%; vertical-align: middle;">                                        
                                    {{listas.subDepartamentosFiltrados[index]?.Nombre}}
                                </div>
                            </div>
                        </div>
                    </template>
                </DxTabs>
            </vs-row>            
            <!-- Listado de Producto -->
            <div class="flex flex-wrap p-2" style="background-color: #008FBE; border-radius: 5px;">
                <!-- producto producto_filtro -->
                <div pagination class="lg:w-1/6 sm:w-1/3 w-full" style="padding:4px 4px;" v-for="(tr, indextr) in listas.productosFiltrados" :key="indextr">
                    <div class="vx-card grid-view-item overflow-hidden">

                        <div class="vs-con-loading__container cursor-pointer" @click="()=>anadirCargo(tr)">
                            
                            <!-- <div class="item-img-container bg-white h-64 flex items-center justify-center mb-4 cursor-pointer" style="max-height:100px"> -->
                                <!-- <img :src="'data:image/png;base64, ' +tr.Imagen" v-if="tr.Imagen"  alt="" class="grid-view-img px-4" style="max-height:250px"/> -->
                            <!--    <expandable-image class="image" :src="'data:image/png;base64, ' +tr.Imagen" v-if="tr.Imagen"> -->
                            <!--    </expandable-image> -->
                            <!--    <img v-else src="@/assets/images/others/notfound.png" alt="" class="grid-view-img px-4" style="max-height:100px" /> -->
                            <!-- </div> -->

                            <div class="item-details px-2" style="height: 82px;">
                                <div class="flex justify-between items-center" style="height: 20%;">
                                    <p style="font-size: 11px; margin-right: 1px; color:black;">{{tr.Codigo}} </p>
                                    <h6 class="font-bold" style="color:#008FBE;">{{$formato_moneda(tr.Precio)}}</h6>
                                </div>

                                <div style="word-wrap: break-word; white-space: wrap; height: 80%; text-align: center; display: flex; justify-content: center; align-items: center;">                                        
                                    <h6 class="font-semibold" :style="{'font-size': tr?.Nombre?.length > 36 ? '10px':'12px' }">
                                        {{tr?.Nombre?.trim()}}
                                    </h6>                                    
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>          
        </vs-col>
        <vs-col class="xs:w-full md:w-full lg:w-5/12 xl:w-5/12">
            <vs-row class="w-full flex pt-5">
                <vs-col class="w-4/12">
                    <vs-button @click="popUpFormaPago = true" color="success" :disabled="productosPedidos.length <= 0" >Facturar</vs-button>
                </vs-col>
                <vs-col class="w-4/12">
                    <vs-button @click="ImprimirPreFactura()" color="warning" :disabled="productosPedidos.length <= 0" >PreFactura</vs-button>
                </vs-col>
                <vs-col class="w-4/12">                  
                    <vs-row class="w-full">
                        <vs-col class="w-3/12 pr-3">
                            <vx-tooltip text="Huella Empleado" style="display:inline-block;margin-right:35px">
                                <vs-button  style="height: 35px;" color="primary"
                                    icon-pack="material-icons" icon="fingerprint"
                                    :disabled="productosPedidos.length <= 0" 
                                    @click="ValidaHuellaActiva()">
                                </vs-button>
                            </vx-tooltip>
                        </vs-col>
                        <vs-col class="w-9/12 pl-3">
                            <vs-button @click="consulta().generarFacturaNomina()"  color="primary" id="botonNomina" :disabled="productosPedidos.length <= 0">
                                Nomina
                            </vs-button>
                        </vs-col>                      
                    </vs-row>
                </vs-col>   
            </vs-row>
            <vs-row class="w-full">
                <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 p-1" style="height: 60px;">   
                    <label class="typo__label label-shrink" style="white-space: nowrap; color:#626262; font-size:12px;  padding-bottom:2px; padding-top:0px;"> Tipo Documento  </label>  
                    <DxSelectBox  
                        class="selectTipoDocumento"          
                        style="padding: 2px;"
                        :items="documento.tiposDocumentoLista"
                        display-expr="Descripcion"
                        placeholder="Seleccionar.."
                        v-model="documento.tipoDocumento"
                    />    
                </vs-col>              
                <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-3/12 p-1">
                    <vs-input label="No. documento" 
                            id="idValidacionDocumento"
                            v-model="documento.noDocumento" 
                            class="w-full"
                            @keydown.enter="ConfirmarActualizacionDoc()" @keydown.tab.prevent="ConfirmarActualizacionDoc()"
                            />      
                </vs-col>  
                <vs-col class="sm:w-full md:w-full lg:w-5/12 xl:w-7/12 p-1">
                    <vs-input label="Nombre"  
                            class="w-full"
                            v-model="documento.nombre" />      
                </vs-col>
            </vs-row>
            <vs-row class="w-full">
                <DxDataGrid ref="productosPedidoRef" class="w-full"
                        v-bind="DefaultDxGridConfiguration"
                        :data-source="productosPedidos"  
                        style="max-width: 1024px !important;"
                        @cell-prepared="estiloFila"    
                        @row-removing="(e)=>consulta().eliminarDetalleComanda(e)" 
                        @row-updating="(e)=>consulta().actualizarDetalleComanda(e)"   
                        :on-row-prepared="(e)=>consulta().configuracionFila(e)"                             
                        :hoverStateEnabled="false"
                        :column-hiding-enabled="true" 
                        :word-wrap-enabled="false"
                        :repaint-changes-only="true"
                        :visible="true" 
                        :showColumnLines="true" 
                        :showBorders="true"
                        :rowAlternationEnabled="true"
                        width="100%" height="560px" max-width="1024px"  
                        >

                        <DxDataGridSelection mode="none" />

                        <DxDataGridEditing :allow-updating="true" :allow-adding="false" :allow-deleting="true" mode="cell" />
                        
                        <DxDataGridColumn width="170px" :allow-editing=false data-field="Nombre" caption="Producto"  alignment="left" >                                                    
                            <DxDataGridRequiredRule/>
                        </DxDataGridColumn> 
                        <DxDataGridColumn width="75px" data-field="Precio" caption="Precio U." 
                                  :allow-updating=false :allow-editing=false :allow-filtering=false  
                                  :format="(e)=>monedaFormato.format(e)" data-type="number" alignment="right">                                                               
                            <DxDataGridRequiredRule/>
                        </DxDataGridColumn>
                        <DxDataGridColumn width="35px" data-field="Cantidad" caption="Cant."  
                                  :editor-options=numeroEnteroOptions  :allow-filtering=false                              
                                  :allow-updating=true :set-cell-value="actualizarSubTotalProducto" 
                                  format="#" data-type="number" alignment="right">  
                            <DxDataGridRangeRule  message="La cantidad minima valida es 1" :min="1"/>                                                             
                            <DxDataGridRequiredRule/>
                        </DxDataGridColumn> 
                        <DxDataGridColumn width="75px" :allow-editing=false :allow-filtering=false   
                                     data-field="SubTotal" caption="Valor" 
                                    :format="(e)=>monedaFormato.format(e)" data-type="number" alignment="right">                                                               
                            <DxDataGridRequiredRule/>
                        </DxDataGridColumn>   
                        <DxDataGridColumn width="130px" caption="Acción" type="buttons" name="accion" 
                            :buttons="[ {icon:'far fa-comment',onClick:AbrirIngresoNota, id: 'nota',text:'Nota', disabled:DesactivarComanda  },
                                        {icon:'fas fa-print',onClick:GenerarComanda, id: 'comanda',text:'Comanda',disabled:DesactivarComanda  },
                                        {icon:'fas fa-trash',name:'delete',type:'danger',text:'Borrar',id:'eliminar'}
                                      ]" css-class="button-icons">
                        </DxDataGridColumn>   
                        <DxDataGridSummary>
                            <DxDataGridTotalItem  column="SubTotal" summary-type="sum" show-in-column="Nombre" :value-format="(e)=>consulta().formatoMoneda(e)" data-type="number" display-format="Total: {0}"/>
                        </DxDataGridSummary>   
                </DxDataGrid>
            </vs-row>            
        </vs-col>
    </vs-row>   
</vx-card>
</template>
<script>
    import ValidarHuella from '/src/components/validador-huella/ValidarHuella.vue';
    import {DefaultDxGridConfiguration,
            _moneda } from './data';
    import ConfiguracionCaja from "/src/components/sermesa/modules/caja/ConfiguracionCaja.vue";     
      
    export default {
        components:{
            ValidarHuella,
            ConfiguracionCaja
        },
        data(){
            return {  
                noPagina: 0,
                productosPedidos: [],
                configuracionCafeteria:{
                    imprimir:true,
                    abrirTab:false
                },
                permisos:{
                    eliminar_producto:false,
                    arqueo:false,
                },
                usuarioPedido:null,
                nombrePedido:null,
                corporativoPedido:null,
                corporativoHuella:null,
                usuarioHuella:null,
                popUpHuellaMesa:false,
                popUpDatosPedido:false,
                popUpFormaPago:false,
                popUpNotaComanda:false,
                popUpSensorHuellas: false,
                nivelPrecios:1,
                TiposPos:[{Nombre:'Credomatic',Id:'CRE'},{Nombre:'Visa', Id:'VIS'}],
                numeroCorte:null,
                factura:{
                    totalFactura:null,
                    tarjeta1:{
                        monto:null,
                        posUtilizado:'VIS',
                        noTarjeta:null,
                        noAutorizacion:null
                    },
                    tarjeta2:{
                        monto:null,
                        posUtilizado:'VIS',
                        noTarjeta:null,
                        noAutorizacion:null
                    },
                    efectivo:{
                        monto:null,
                        vuelto:null,
                        saldo:null
                    }
                },
                configuracion:{
                    cajaSeleccionada: null,
                    activarFacturacion: false,
                    activarRecibos: false                     
                },                
                documento:{ 
                    tiposDocumentoLista:[],
                    tipoDocumento:null,
                    noDocumento:null,
                    nombre:null,
                    validacion:null			
                },	
                monedaFormato: _moneda,                 
                busquedaProducto:null,  
                seleccionCategoria:null,
                seleccionSubDepartamento:null,  
                noComanda:null,
                textoNota:null,
                lineaNota:null,     
                lineaEliminacion:null,           
                bodega:null,        
                listas:{
                    mesas:[],
                    pedidos:[],
                    numeroPedidosOcupados:[],
                    numeroPedidos:[],
                    productos:[],
                    productosFiltrados:[],
                    categorias:[],
                    subDepartamentos:[],
                    subDepartamentosFiltrados:[]
                },
                producto:{
                    CodigoProducto:null,
                    CantidadSolicitar:null
                },
                pedido:{
                    mesaSeleccionada:null,
                    pedidoSeleccionado:null,
                    numeroPedidoSeleccionado:null,
                    paraRecoger:false
                },
                DefaultDxGridConfiguration:
                {...DefaultDxGridConfiguration,
                 paging:{ enabled:true, pageSize:10 },
                 searchPanel:{visible:false}    
                },   
                numeroEnteroOptions: {  
                                dataField: "Miles",  
                                dataType: "number",  
                                format: 'fixedPoint',  
                                precision: 0,  
                                editorOptions: {  
                                    onKeyPress: function (e) {  
                                        var event = e.jQueryEvent,  
                                            str = String.fromCharCode(event.keyCode);  
                                        if (!/[0-9]/.test(str))  
                                            event.preventDefault();  
                                    }  
                                }  
                             }
            }
        },
        methods:{
            realizarArqueo(){
                if(!this.permisos.arqueo){
                    this.consulta().mostrarError('No cuenta con permiso para realizar el arqueo.');  
                    return
                }
                this.numeroCorte = 0; 
                this.ImprimirArqueo('ImprimeArqueoCafeteria');
            },
            obtenerPermisos(){
                this.permisos.eliminar_producto = this.$validar_privilegio('ELIMINAR_COMANDAS').status
                this.permisos.arqueo = this.$validar_privilegio('ARQUEO').status
            },
            recargarProductos(nuevoNivel){
                if(this.nivelPrecios != nuevoNivel){
                    this.nivelPrecios = nuevoNivel
                    this.consulta().cargaProductos({CodigoSubDepartamento:this.seleccionSubDepartamento})
                }
            },                       
            async init(){
                await this.consulta().cargaCategorias();
                if(this.listas.categorias.length>0){
                    this.seleccionCategoria = this.listas.categorias[0].CodigoCategoria
                }
                await this.consulta().cargaSubDepartamentos();
                if(this.listas.subDepartamentos.length>0){
                    this.seleccionSubDepartamento = this.listas.subDepartamentos[0]?.CodigoSubDepartamento
                }
                this.obtenerPermisos()
                this.cargarProductosIniciales()
                this.consulta().consultaPuntoVenta(4)  
                this.consulta().consultaPuntoVenta(6)  
                this.CargarTipoDocumento()           
                this.CargarConfiguracionImpresion()   
            },
            CargarDatosDocumento(TipoReceptor,NoDocumento,NombreReceptor){
                if(!this.consulta().validarCampos('','C','',NoDocumento,false,0)){
                    this.documento.tipoDocumento = this.documento.tiposDocumentoLista.find(d=>d.Valor == 4)
                    this.documento.noDocumento = 'C/F'
                    this.documento.nombre = 'CONSUMIDOR FINAL'
                    this.documento.validacion = 1
                    return;
                }

                if(!this.consulta().validarCampos('','N','',TipoReceptor,false,0)){
                    TipoReceptor = 4
                }

                this.documento.tipoDocumento = this.documento.tiposDocumentoLista.find(d=>d.Valor == TipoReceptor)
                this.documento.noDocumento = NoDocumento
                this.documento.nombre = NombreReceptor
                this.documento.validacion = 1
            },
            LimpiarPantalla(){
                this.documento.tipoDocumento = this.documento.tiposDocumentoLista.find(d=>d.Valor == 4)
                this.documento.noDocumento = 'C/F'
                this.documento.nombre = 'CONSUMIDOR FINAL'
                this.documento.validacion = 1

                this.pedido.mesaSeleccionada = null
                this.pedido.pedidoSeleccionado = null
                this.pedido.numeroPedidoSeleccionado = null
                this.pedido.paraRecoger = false
                this.productosPedidos = []

                this.factura.totalFactura = null
                this.factura.tarjeta1 = {monto:null,posUtilizado:'VIS',noTarjeta:null,noAutorizacion:null}
                this.factura.tarjeta2 = {monto:null,posUtilizado:'VIS',noTarjeta:null,noAutorizacion:null}
                this.factura.efectivo = {monto:null,vuelto:null,saldo:null}
                
               
            },
            async getValidacionHuella(data){
                if(data.StatusPlantilla == 'VERIFICADO'){ 
                    await this.consulta().descuentoNomina()
                    this.popUpSensorHuellas = false;
                    //this.popUpPermisosHuella = false  
                    //this.huellaValidada = true;                      
                }
            },
            ValidaHuellaActiva(){
                if(this.pedido.pedidoSeleccionado && 
                    this.consulta().validarCampos('Punto de Venta Cafetería','N','Corporativo',this.pedido.pedidoSeleccionado.Corporativo,false,0)
                  ){
                    this.corporativoHuella = this.pedido.pedidoSeleccionado.Corporativo
                }else if(this.pedido.mesaSeleccionada && 
                         !this.consulta().validarCampos('Punto de Venta Cafetería','N','Corporativo',this.corporativoHuella,false,0)){
                    this.popUpHuellaMesa = true;   
                    return;                                         
                }else{   
                    if(!this.pedido.mesaSeleccionada || !this.consulta().validarCampos('Punto de Venta Cafetería','N','Corporativo',this.corporativoHuella,false,0)){
                        this.consulta().mostrarError('La validación por huella solo es válida para Empleados')
                        return;
                    }
                }

                this.axios.post('/app/huella/ValidaHuellaActiva',{"Corporativo":this.corporativoHuella,"Opcion":"C","SubOpcion":13})
                    .then((resp)=>{
                            if(resp.data.estado==0){
                                if(resp.data.descripcion){
                                    this.ValidarHuella(this.corporativoHuella);
                                }else{
                                    this.$vs.notify({
                                            title:'Validacion Huella',
                                            text:'No cuenta con una huella registrada en el sistema',
                                            timer:6000,
                                            color:'danger',
                                            position: 'top-center'
                                        })
                                }
                            }
                        })  
                                
            },
            ValidarHuella(Corporativo){
                this.popUpSensorHuellas = true                
                this.$refs.validarHuellas.activarValidador(Corporativo)
            },
            DesactivarComanda(e){          
                return  !(e.row.data.Ensamblado == 'S' && e.row.data.Status == 'S')
            },
            ImprimirPreFactura(){
                let productoSinComanda = this.productosPedidos.findIndex(p=>p.Ensamblado == 'S' && p.Status == 'S')
                if(productoSinComanda>=0){
                    this.consulta().mostrarError('Tiene productos sin comandar.')
                    return
                }

                let reporte = "Impresion FEL"

                let postData = {
                    noComanda: this.noComanda,
                    nombrereporte: "PreFactura"
                }
                
                this.$reporte_modal({
                    Nombre: reporte,
                    Opciones: {
                        ...postData
                    },
                    AbrirTab:this.configuracionCafeteria.abrirTab,
                    Imprimir:this.configuracionCafeteria.imprimir
                })

            },
             ImprimirCorte(tipoReporte = "ImprimeArqueoCafeteria"){              

                let reporte = "Impresion Corte Cafeteria"

                let postData = {
                    caja: this.configuracion.cajaSeleccionada.CajaLocal,
                    SerieFactura: this.configuracion.cajaSeleccionada.SerieFac,
                    corte: this.numeroCorte,
                    nombrereporte: tipoReporte,
                    nombreImpresora: 'TMU',
                }
                
                this.$reporte_modal({
                    Nombre: reporte,
                    Opciones: {
                        ...postData
                    },
                    AbrirTab:true,
                    Imprimir:false
                })

            },   
            ImprimirArqueo(tipoReporte = "ImprimeArqueoCafeteria"){              

                let reporte = "Impresion Arqueo Cafeteria"

                let postData = {
                    caja: this.configuracion.cajaSeleccionada.CajaLocal,
                    SerieFactura: this.configuracion.cajaSeleccionada.SerieFac,
                    corte: this.numeroCorte,
                    nombrereporte: tipoReporte,
                    nombreImpresora: 'TMU',
                }
                
                this.$reporte_modal({
                    Nombre: reporte,
                    Opciones: {
                        ...postData
                    },
                    AbrirTab:true,
                    Imprimir:false
                })

            },            
            AbrirIngresoNota(e){                
                this.textoNota = e.row.data.Nota
                this.lineaNota = e.row.data.Linea
                this.popUpNotaComanda = true;
            },
            async GenerarComanda(e){
                let reporte = "Impresion FEL"

                let postData = {
                    noLinea:  e.row.data.Linea,
                    noComanda: this.noComanda,
                    nombrereporte: "Comanda"
                }
                let respuesta = 
                await this.$reporte_modal({
                    Nombre: reporte,
                    Opciones: {
                        ...postData
                    },
                    AbrirTab:this.configuracionCafeteria.abrirTab,
                    Imprimir:this.configuracionCafeteria.imprimir
                }).then(()=>{return 1;})
                  .catch(()=>{return 0;})
                
                if(respuesta == 1){
                    this.productosPedidos = 
                    this.productosPedidos.map(p=>{
                        if(p.Linea == e.row.data.Linea){
                            p.Status = 'P'
                        }
                        return p
                    })  
                }
            },
            validarSaldoFactura(){
                let valido = true;
                if(this.factura.tarjeta1.monto > 0){                    
                    valido = this.consulta().validarCampos('Punto de Venta Cafetería','C','Tipo Facturación Tarjeta 1',this.factura.tarjeta1.posUtilizado,true,0);
                    if(!valido) return valido
                    valido = this.consulta().validarCampos('Punto de Venta Cafetería','C','No. Autorización Tarjeta 1',this.factura.tarjeta1.noAutorizacion,true,0);
                    if(!valido) return valido
                }

                if(this.factura.tarjeta2.monto > 0){                    
                    valido = this.consulta().validarCampos('Punto de Venta Cafetería','C','Tipo Facturación Tarjeta 2',this.factura.tarjeta2.posUtilizado,true,0);
                    if(!valido) return valido
                    valido = this.consulta().validarCampos('Punto de Venta Cafetería','C','No. Autorización Tarjeta 2',this.factura.tarjeta2.noAutorizacion,true,0);
                    if(!valido) return valido
                }

                if(this.factura.efectivo.saldo > 0 ){
                    this.consulta().mostrarError('Tiene saldo pendiente.')
                    return false;
                }
                if(this.factura.efectivo.vuelto && this.factura.efectivo.vuelto > 0){
                    if(!this.factura.efectivo.monto || this.factura.efectivo.monto <= 0){
                        this.consulta().mostrarError('El vuelto no puede ser mayor al monto en efectivo.')
                         return false;
                    }
                    if(this.factura.efectivo.vuelto > this.factura.efectivo.monto){
                        this.consulta().mostrarError('El vuelto no puede ser mayor al monto en efectivo.')
                        return false;
                    }
                }

                return valido
            },
            actualizarSaldoFactura(componente){
                let efectivo = 0
                if(componente.element.id == "efectivo"){
                    efectivo = componente.component._parsedValue + this.factura.tarjeta1.monto + + this.factura.tarjeta2.monto
                }
                if(componente.element.id == "tarjeta1"){
                    efectivo = componente.component._parsedValue + this.factura.efectivo.monto + + this.factura.tarjeta2.monto
                }
                if(componente.element.id == "tarjeta2"){
                    efectivo = componente.component._parsedValue + this.factura.efectivo.monto + + this.factura.tarjeta1.monto
                }
                
                
                let montoValido = this.consulta().validarCampos('Punto de Venta Cafetería','N','Monto Total',this.factura.totalFactura,false,0);
                if(!montoValido || this.factura.totalFactura <= 0){
                    return;
                }
                let diferencia = efectivo - this.factura.totalFactura
                if(diferencia < 0){
                    this.factura.efectivo.vuelto = 0;
                    this.factura.efectivo.saldo = diferencia * -1
                }else if(diferencia >= 0){
                    this.factura.efectivo.vuelto = diferencia;
                    this.factura.efectivo.saldo = 0;
                }
            },
            BuscarCorporativo(corporativoPedido = true){
                if((this.corporativoPedido != '' && this.corporativoPedido != null && this.corporativoPedido != 0 && corporativoPedido) 
                     ||
                   (this.corporativoHuella != '' && this.corporativoHuella != null && this.corporativoHuella != 0 && !corporativoPedido) 
                  ){
                    this.axios.post('/app/administracion/CorporativoConsulta', {
                        IdCorporativo: corporativoPedido ? this.corporativoPedido : this.corporativoHuella
                    })
                    .then(resp => {
                        if(resp.data.codigo == 0){
                            if(resp.data.json.length == 1){
                                if(corporativoPedido){
                                    this.usuarioPedido = resp.data.json[0]
                                }else{
                                    this.usuarioHuella = resp.data.json[0]
                                }
                            }else{
                                this.$refs.BuscarUsuarioCorporativo.iniciar((data) => {
                                    if(data != null){
                                        this.axios.post('/app/administracion/CorporativoConsulta', {
                                            IdCorporativo: data.Corporativo
                                        })
                                        .then(resp => {
                                            if(corporativoPedido){
                                                this.usuarioPedido = resp.data.json[0]
                                                this.corporativoPedido = this.usuarioPedido.Corporativo
                                            }else{
                                                this.usuarioHuella = resp.data.json[0]
                                                this.corporativoHuella = this.usuarioHuella.Corporativo
                                            }
                                        })
                                    }
                                })
                            }
                        }
                    })
                }else{
                    this.$refs.BuscarUsuarioCorporativo.iniciar((data) => {
                        if(data != null){
                            this.axios.post('/app/administracion/CorporativoConsulta', {
                                IdCorporativo: data.Corporativo
                            })
                            .then(resp => {
                                if(corporativoPedido){
                                    this.usuarioPedido = resp.data.json[0]
                                    this.corporativoPedido = this.usuarioPedido.Corporativo                                
                                }else{
                                    this.usuarioHuella = resp.data.json[0]
                                    this.corporativoHuella = this.usuarioHuella.Corporativo
                                }
                            })
                        }
                    })
                }
            },
            CargarTipoDocumento(){
                this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision', {
                                    opcion:'C',
                                    subOpcion:2,
                                    agrupacion:'TipoReceptor'
                                })
                          .then(resp => {                            
                                    if (resp.data && resp.data.json.length > 0){
                                        this.documento.tiposDocumentoLista = resp.data.json;
                                        this.documento.tipoDocumento = this.documento.tiposDocumentoLista.find(d=>d.Valor==4);
                                        if(this.consulta().validarCampos('Punto de Venta Cafetería','O','Tipo Documento',this.documento.tipoDocumento,false,0))
                                        this.documento.noDocumento = 'C/F';
                                        this.documento.nombre = 'CONSUMIDOR FINAL';
                                        this.documento.validacion = 1;
                                    }
                                }
                               )
            },
            CargarConfiguracionImpresion(){
                this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision', {
                                    opcion:'C',
                                    subOpcion:2,
                                    agrupacion:'ImpresionCafeteria'
                                })
                          .then(resp => {                            
                                    if (resp.data && resp.data.json.length > 0){
                                        let abrirTab = resp.data.json.find(d=>d.Descripcion=="AbrirTab");
                                        let imprimir = resp.data.json.find(d=>d.Descripcion=="Imprimir");
                                        if(abrirTab){ this.configuracionCafeteria.abrirTab = abrirTab.Valor == 'N' ? false : true; }
                                        else{this.configuracionCafeteria.abrirTab = true}

                                        if(imprimir){ this.configuracionCafeteria.imprimir = imprimir.Valor == 'N' ? false : true; }
                                        else{this.configuracionCafeteria.imprimir = true}
                                    }else{
                                        this.configuracionCafeteria.abrirTab = true
                                        this.configuracionCafeteria.imprimir = true
                                    }
                                }
                            )
            },
            async CargarNuevoPedido(){
                this.consulta().cargarNumerosPedido();
                this.pedido.paraRecoger = true; 
                this.popUpDatosPedido = true;
            },
            ConfirmarActualizacionDoc(){
                if(!this.consulta().validarCampos('Punto de Venta Cafetería','N','Comanda',this.noComanda,true,0)){
                    return 
                }

                if(this.nivelPrecios != 30){
                    this.ValidacionDocumentoTributario();
                }else{
                    const validarDocumento = document.getElementById("idValidacionDocumento");
                    if(validarDocumento) validarDocumento.blur();
                    
                    this.$vs.dialog({
                            type: 'confirm',
                            color: 'warning',
                            title: 'Confirmar',
                            acceptText: 'Continuar',
                            cancelText: 'Cancelar',
                            text: 'Al actualizar el documento tributario se quitara el descuento',
                            accept: ()=>{
                                this.ValidacionDocumentoTributario();
                            },
                            cancel: ()=>{
                                if(this.pedido.mesaSeleccionada){
                                    this.documento.noDocumento = this.pedido.mesaSeleccionada.DocReceptor
                                    this.documento.tipoDocumento = this.documento.tiposDocumentoLista.find(d=>d.Valor==this.pedido.mesaSeleccionada.TipoReceptor);
                                    this.documento.nombre = this.pedido.mesaSeleccionada.NombreFactura
                                }else if(this.pedido.pedidoSeleccionado){
                                    this.documento.noDocumento = this.pedido.pedidoSeleccionado.DocReceptor
                                    this.documento.tipoDocumento = this.documento.tiposDocumentoLista.find(d=>d.Valor==this.pedido.pedidoSeleccionado.TipoReceptor);
                                    this.documento.nombre = this.pedido.pedidoSeleccionado.NombreFactura
                                }
                            }
                        })
                }

            },
            async ValidacionDocumentoTributario(){                                
                if(!this.consulta().validarCampos('Punto de Venta Cafetería','N','Comanda',this.noComanda,true,0)){
                    return 0
                }

                if(!this.documento.tipoDocumento){
                    this.MostrarError('Seleccione un tipo de documento para el cliente');
                    return 0
                }
                if(!this.documento.noDocumento){
                    this.MostrarError('Ingrese el NIT/DPI del cliente');
                    return 0
                }
                let respuesta =
                await
                this.axios.post('/app/v1_caja/ValidacionDocumentoTributario',{
                                    tipoReceptor: parseInt(this.documento.tipoDocumento.Valor),
                                    documento: this.documento.noDocumento
                                })
                          .then(resp=>{
                                    if ( resp.data.codigo == 0 && resp.data.json.length > 0 && resp.data.json[0].codigo == 0){
                                        this.documento.nombre = resp.data.json[0].NOMBRE
                                        this.documento.validacion = 1        
                                        return 1
                                    }else if ( resp.data.codigo == 0 && resp.data.json.length > 0 && resp.data.json[0].codigo != 0){
                                        this.MostrarError(resp.data.json[0].descripcion);
                                        this.documento.nombre = null
                                        this.documento.validacion = null                                        
                                    }
                                    else if(resp.data.json.length == 0){
                                        this.documento.nombre = null
                                        this.documento.validacion = null
                                        this.MostrarError('No se encontraron datos para el documento ingresado');
                                    }
                                    return 0
                                })
                            .catch(()=>{return 0})

                    if(respuesta == 1){
                        await this.ActualizarDocumentoTributario()
                    }

                return respuesta
            },  
            async ActualizarDocumentoTributario(){
                if(!this.consulta().validarCampos('Punto de Venta Cafetería','N','Comanda',this.noComanda,true,0)){
                    return
                }

                await this.axios.post('/app/v1_caja/PuntoDeVentaCafeteria', {
                                    Opcion:'I',
                                    SubOpcion:8,
                                    NoComanda:this.noComanda,
                                    NombreFactura:this.documento.nombre,
                                    Nit:parseInt(this.documento.tipoDocumento.Valor) == 4 ?this.documento.noDocumento:null,
                                    CuiPasaporte: parseInt(this.documento.tipoDocumento.Valor) != 4 ?this.documento.noDocumento:null,
                                    TipoReceptor:parseInt(this.documento.tipoDocumento.Valor)                                   

                                }).then(resp=>{
                                    if(resp.data.codigo == 0){                                                                                

                                        if(this.pedido.pedidoSeleccionado ){
                                            this.listas.pedidos = 
                                                this.listas.pedidos.map(pedido=>{
                                                    if(pedido.Codigo == this.pedido.pedidoSeleccionado.Codigo){
                                                        pedido.DocReceptor      = this.documento.noDocumento
                                                        pedido.NombreFactura    = this.documento.nombre
                                                        pedido.TipoReceptor     = this.documento.tipoDocumento.Valor                                                        
                                                        pedido.Nivel            = resp.data.Nivel
                                                    }
                                                    return pedido
                                                })
                                            this.pedido.pedidoSeleccionado.DocReceptor      = this.documento.noDocumento
                                            this.pedido.pedidoSeleccionado.NombreFactura    = this.documento.nombre
                                            this.pedido.pedidoSeleccionado.TipoReceptor     = this.documento.tipoDocumento.Valor
                                        }else if(this.pedido.mesaSeleccionada){
                                            this.listas.mesas = 
                                                this.listas.mesas.map(mesa=>{
                                                    if(mesa.Codigo == this.pedido.mesaSeleccionada.Codigo){
                                                        mesa.DocReceptor      = this.documento.noDocumento
                                                        mesa.NombreFactura    = this.documento.nombre
                                                        mesa.TipoReceptor     = this.documento.tipoDocumento.Valor
                                                        mesa.Nivel            = resp.data.Nivel
                                                    }
                                                    return mesa
                                                })
                                            this.pedido.mesaSeleccionada.DocReceptor      = this.documento.noDocumento
                                            this.pedido.mesaSeleccionada.NombreFactura    = this.documento.nombre
                                            this.pedido.mesaSeleccionada.TipoReceptor     = this.documento.tipoDocumento.Valor
                                        }

                                        if(resp.data.Nivel != this.nivelPrecios){
                                            if(this.pedido.pedidoSeleccionado ){
                                                this.consulta().cargarPedido()
                                            }else if(this.pedido.mesaSeleccionada){
                                                this.consulta().cargarMesa()
                                            }
                                        }
                                    }
                                })             
            },
            CargarCaja(cajaSeleccionada){   
                this.configuracion.cajaSeleccionada   = cajaSeleccionada.Cajero
                this.configuracion.activarFacturacion = cajaSeleccionada.Factura
                this.configuracion.activarRecibos     = cajaSeleccionada.Recibo
                if(cajaSeleccionada.Cajero){
                    this.bodega = cajaSeleccionada.Cajero.NumeroBodega               
                }
            },
            estiloFila(e) {
                if (e.rowType === "data") {
                    if (e.data.Ensamblado == 'S' && e.data.Status == 'S') {
                        e.cellElement.classList.add("columnaAmarrilla")
                    }
                }
            },
            filtrarProductos(){
                if(this.busquedaProducto && this.busquedaProducto.trim().length >= 2){
                    let filtro = this.busquedaProducto.trim().toLowerCase()
                    this.listas.productosFiltrados =
                        this.listas.productos.filter(producto => (`${producto.Codigo.trim().toLowerCase()}+${producto.Nombre.trim().toLowerCase()}`).match(filtro) == null ? false : true)
                }else{
                    this.listas.productosFiltrados = this.listas.productos
                }
            },
            anadirCargo(producto){
                this.producto = {...producto}
                this.consulta().agregarProducto()                
            },
            cargarProductosIniciales(){
                if(this.listas.subDepartamentos.length>0 && this.listas.categorias.length>0){
                    this.filtrarSubDepartamento(this.listas.categorias[0])
                }
            },
            actualizarSubTotalProducto(fila,value, filaActual){
                fila.SubTotal = value * filaActual.Precio
                fila.Cantidad = value
            },
            filtrarSubDepartamento(Categoria){
                this.listas.subDepartamentosFiltrados =
                    this.listas.subDepartamentos.filter(subDepartamento=>subDepartamento.CodigoCategoria == Categoria.CodigoCategoria)

                this.seleccionCategoria = Categoria.CodigoCategoria
                
                if(this.listas.subDepartamentosFiltrados.length > 0){                                        
                    this.consulta().cargaProductos(this.listas.subDepartamentosFiltrados[0])
                }else{
                    this.seleccionSubDepartamento = null
                    this.listas.productos = []
                }                
            },
            consulta(){
                return {
                    mostrarError:(mensaje,titulo = 'Punto de Venta Cafetería')=>{
                        this.$vs.notify({
                                        position:'top-center',
                                        color: '#B71C1C',
                                        title: titulo,
                                        text: mensaje,
                                    });
                    },
                    mostrarSatisfactorio:(mensaje,titulo = 'Punto de Venta Cafetería')=>{
                        this.$vs.notify({
                            time:2000,
                            position: 'top-center',
                            color: 'success',
                            title: titulo,
                            text:mensaje
                        })
                    },
                    validarCampos:(TituloNotificacion,Tipo,Nombre,Valor,Obligatorio,Longuitud)=>{
                        let campoValido = true;
                        let MensajeError = 'Campo \'' + Nombre + '\' es obligatorio.'
                        if(Tipo=='O'){
                            if(Valor == null || Valor == undefined || Object.keys(Valor).length <= 0){                            
                                campoValido = false;
                            }
                        }else if (Tipo == 'N') {            
                            if (Valor <= 0 || Valor === "") {
                                campoValido = false;
                            } 
                        } else {
                            if (Valor == '' || Valor == null || Valor == undefined || Valor.length === 0) {
                                campoValido = false;
                            } else if(Longuitud > 0) {    
                                if (Longuitud < Valor.length) {
                                    campoValido = false;
                                    MensajeError = 'La longuitud del campo \''+Nombre+'\' excede el maximo de caracteres ('+Longuitud+').';
                                }            
                            }
                        }

                        if(!campoValido && Obligatorio){
                            this.$vs.notify({
                                        position:'top-center',
                                        color: '#B71C1C',
                                        title: TituloNotificacion,
                                        text: MensajeError,
                                    });
                        }
                        return campoValido;
                    },
                    formatoMoneda:(e)=>{
                        this.factura.totalFactura = e; 
                        return this.$formato_moneda(e)
                    },
                    configuracionFila:(e)=>{
                        e.rowElement.style.height = '35px';
                    },
                    generarFacturaNomina:()=>{
                        if(this.nivelPrecios != 30){
                            this.consulta().mostrarError('Asociar la comanda a un empleado antes de generar la factura.')
                            return
                        }
                        this.consulta().generarFacturaInterna(true)
                    },
                    generarFacturaInterna:(descuentoNomina=false)=>{ 
                        let valido = true;
                        let productoSinComanda = this.productosPedidos.findIndex(p=>p.Ensamblado == 'S' && p.Status == 'S')                                               

                        if(productoSinComanda>=0){
                            this.consulta().mostrarError('Tiene productos sin comandar.')
                            return
                        }

                        if(this.nivelPrecios==30 && !descuentoNomina){
                            this.consulta().mostrarError('Esta comanda tiene descuento, utilice la opcion de Nomina.')
                            return
                        }

                        if(!this.validarSaldoFactura()) return

                        if(this.documento.validacion != 1){
                            this.consulta().mostrarError('Ingrese un Nit ó Doc. Tributario valido.')
                            return;
                        }    

                        if(this.documento.tipoDocumento.Valor != '4' && (this.documento?.noDocumento??'').toUpperCase() == 'C/F'){
                            this.consulta().mostrarError('Seleccione Tipo Documento NIT para utilizar C/F')
                            return;
                        }
                        if(!descuentoNomina){
                            valido = this.consulta().validarCampos('Punto de Venta Cafetería','N','Forma de pago',this.factura.efectivo.monto,false,0);
                            if(!valido){
                                valido = this.consulta().validarCampos('Punto de Venta Cafetería','N','Forma de pago',this.factura.tarjeta1.monto,false,0);
                                if(!valido){
                                    valido = this.consulta().validarCampos('Punto de Venta Cafetería','N','Forma de pago',this.factura.tarjeta2.monto,true,0);
                                    if(!valido){return;}
                                }
                            }
                        }

                        valido = this.consulta().validarCampos('Punto de Venta Cafetería','N','Mesa ó Pedido',this.noComanda,true,0);
                        if(!valido){return;}
                        

                        const botonFacturar = document.getElementById("botonFacturar");
                        const botonNomina = document.getElementById("botonNomina");
                        if(botonFacturar) botonFacturar.blur();
                        if(botonNomina) botonNomina.blur();

                        this.axios.post('/app/v1_caja/FacturacionPuntoDeVentaCafeteria',{
                                            serieFactura:this.configuracion.cajaSeleccionada.SerieFac,
                                            nomCliente:this.documento.nombre,
                                            tipoDoc:this.documento.tipoDocumento?.Valor, 
                                            nitCliente:this.documento.noDocumento,
                                            efectivo:this.factura.efectivo.monto,
                                            posTarjeta1:this.factura.tarjeta1.posUtilizado,
                                            tarjeta1:this.factura.tarjeta1.monto,
                                            autorizacion1:this.factura.tarjeta1.noAutorizacion,
                                            posTarjeta2:this.factura.tarjeta2.posUtilizado,
                                            tarjeta2:this.factura.tarjeta2.monto,
                                            autorizacion2:this.factura.tarjeta2.noAutorizacion,
                                            noComanda:this.noComanda,
                                            tipoProceso:3                                            
                                        })
                                  .then(resp=>{
                                        if(resp.data.codigo==0){
                                            this.popUpFormaPago = false                                            
                                            this.consulta().fel(resp.data.Serie,resp.data.Factura);
                                            this.consulta().consultaPuntoVenta(4)  
                                            this.consulta().consultaPuntoVenta(6) 
                                            this.LimpiarPantalla()     
                                            this.recargarProductos(1)
                                        }
                                    })
                        
                    },
                     validacionDocumentoDescuento: async()=>{
                        let docValidado = 0;
                        let noDocumento = await
                        this.axios.post('/app/v1_caja/PuntoDeVentaCafeteria', {
                                    Opcion:'C',
                                    SubOpcion:12,
                                    CorporativoPedido:this.corporativoHuella
                                }).then(resp=>{
                                    if(resp.data.codigo == 0){
                                        if(resp.data.json.length>0){   
                                            if(resp.data.json[0].codigo && resp.data.json[0].codigo < 0){
                                                this.consulta().mostrarError(resp.data.json[0].descripcion)
                                                return 0                                                                
                                            }else{
                                                return resp.data.json[0].Nit                                                                               
                                            }                                              
                                        }             
                                        return 0                          
                                    }
                                    return 0
                                }).catch(()=>{return 0})    

                        if(noDocumento && noDocumento.length > 1){
                            this.documento.tipoDocumento = this.documento.tiposDocumentoLista.find(d=>d.Valor == 4)                                                                           
                            this.documento.noDocumento = noDocumento
                            docValidado = await this.ValidacionDocumentoTributario()             
                        }                        
                        return docValidado;
//                        this.popUpHuellaMesa = false;                               
                    },
                    descuentoNomina:async()=>{
                        let docValidado  = await this.consulta().validacionDocumentoDescuento()
                        if(docValidado == 0){      
                            this.popUpHuellaMesa = false;             
                            return;
                        }

                        this.axios.post('/app/v1_caja/PuntoDeVentaCafeteria', {
                                    Opcion:'I',
                                    SubOpcion:7,
                                    Nivel: 30,
                                    CorporativoPedido:this.corporativoHuella,
                                    NoComanda:this.noComanda,
                                }).then(resp=>{
                                    if(resp.data.codigo == 0){
                                        this.nivelPrecios = resp.data.NivelPrecio

                                        if(this.pedido.pedidoSeleccionado ){
                                            this.consulta().cargarPedido()
                                        }else if(this.pedido.mesaSeleccionada){
                                            this.consulta().cargarMesa()
                                        }

                                        if(this.seleccionSubDepartamento){
                                            this.consulta().cargaProductos({CodigoSubDepartamento:this.seleccionSubDepartamento})
                                        }
                                        this.popUpHuellaMesa = false;
                                    }
                                })    
                                
                        this.popUpHuellaMesa = false;
                    },
                    fel: (serieFactura,numeroFactura)=>{
                        this.axios.post('/app/v1_FacturaElectronica/GeneraFel',
                                        {serieFactura: serieFactura,
                                         numeroFactura: numeroFactura
                                        })                                  
                                  .then(resp=>{
                                            if(resp.data.codigo == 0 && resp.data.json[0].codigo == 0){
                                                this.consulta().mostrarSatisfactorio(resp.data.json[0].descripcion);
                                                this.consulta().generarFactura(serieFactura,numeroFactura);                                                                               
                                            }else{
                                                this.consulta().mostrarError(resp.data.json[0].descripcion);
                                            }
                                        })                                 
                    },
                    generarFactura: (Serie,NumeroFactura) => {
                        let reporte = "Impresion FEL"

                        let postData = {
                            SerieFactura: Serie ,
                            NumFactura: NumeroFactura,
                            nombrereporte: this.configuracion.cajaSeleccionada.FormatoImpresion
                        }


                        this.$reporte_modal({
                            Nombre: reporte,
                            Opciones: {
                                ...postData
                            },
                            AbrirTab:this.configuracionCafeteria.abrirTab,
                            Imprimir:this.configuracionCafeteria.imprimir
                        })                

                    },   
                    validarCorte:()=>{
                        this.$vs.dialog({
                            type: 'confirm',
                            color: 'success',
                            title: 'Confirmar',
                            acceptText: 'SI',
                            cancelText: 'NO',
                            text: 'Desea realizar el corte de caja?',
                            accept: ()=>{
                                this.consulta().generarCorte();
                            }
                        })
                    },
                    reimpresionFactura:()=>{
                        this.axios.post('/app/v1_caja/ReprintCorteFel', {
                            tipoProceso : 9,
                            serieFactura: this.configuracion.cajaSeleccionada.SerieFac
                        }).then(resp => {
                            if (resp.data && resp.data.json.length > 0) {
                                let serieFactura = this.configuracion.cajaSeleccionada.SerieFac
                                let noFactura =  resp.data.json[0].Factura;
                                this.consulta().generarFactura(serieFactura,noFactura)                                
                            }
                        })
                    },
                    reimpresionCorte:()=>{
                        this.axios.post('/app/v1_caja/ReprintCorteFel', {
                            tipoProceso : 10,
                            numeroCaja  : this.configuracion.cajaSeleccionada.CajaLocal
                        }).then(resp => {
                            if (resp.data && resp.data.json.length > 0) {
                                this.numeroCorte = resp.data.json[0].NumCorte;                                        
                                this.ImprimirCorte('ImprimeArqueoCafeteriaRangoFacturas');                
                                this.ImprimirCorte("ProductosCorte"); 
                            }
                        })
                    },
                    generarCorte:()=>{

                        this.consulta().mostrarSatisfactorio('Corte caja: '+ 
                        this.configuracion.cajaSeleccionada.CajaLocal +
                        '.  Por favor espere...')

                        this.axios.post('/app/v1_caja/CorteCajaCafeteria',{
                            numeroCaja: this.configuracion.cajaSeleccionada.CajaLocal
                        }).then( resp => {
                            if (resp.data.json[0].codigo != 0) {
                                this.consulta().mostrarError(resp.data.json[0].descripcion)
                                return false
                            }else{
                                this.consulta().mostrarSatisfactorio('Imprimiendo corte: '+ resp.data.json[0].NumCorte)                                    
                                this.numeroCorte = resp.data.json[0].NumCorte;
                                this.ImprimirCorte('ImprimeArqueoCafeteriaRangoFacturas');                
                                this.ImprimirCorte("ProductosCorte"); 
                            }
                        })
                    },
                    agregarPedido:()=>{
                        let valido = this.consulta().validarCampos('Punto de Venta Cafetería','O','Corporativo',this.usuarioPedido,false,0);
                        let corporativoValido = valido;                        
                        if(!valido){
                            valido = this.consulta().validarCampos('Punto de Venta Cafetería','C','Nombre ó Corporativo',this.nombrePedido,true,0);
                            if(!valido){return;}
                        }
                        valido = this.consulta().validarCampos('Punto de Venta Cafetería','N','No. Pedido',this.pedido.numeroPedidoSeleccionado,true,0);
                        if(!valido){return;}
                        let usuario = corporativoValido ? this.usuarioPedido.Corporativo+' '+this.usuarioPedido.Nombres+' '+this.usuarioPedido.Apellidos
                                                        : this.nombrePedido;
                        let Descripcion = this.pedido.numeroPedidoSeleccionado + ' - '+ usuario;

                        let pedido = {Codigo:this.pedido.numeroPedidoSeleccionado,Descripcion:Descripcion,Status:'T',
                                      NoPedido:this.pedido.numeroPedidoSeleccionado,Corporativo:corporativoValido?this.usuarioPedido.Corporativo:null,
                                      Nivel:1,DocReceptor:'C/F',NombreFactura:'CONSUMIDOR FINAL', TipoReceptor:4,
                                      Nombre:corporativoValido?this.usuarioPedido.Nombres+' '+this.usuarioPedido.Apellidos:this.nombrePedido                                     
                                    };
                        this.listas.pedidos.push(pedido)
                        this.listas.pedidos = this.listas.pedidos.toSorted((a,b)=>a.NoPedido - b.NoPedido)
                        this.pedido.pedidoSeleccionado = pedido;
                        this.recargarProductos(1) // actualizando nivel de precios
                        this.consulta().limpiarMesa();
                        this.$vs.notify({
                            position:'top-center',
                            color: 'success',
                            title: 'Punto de Venta',
                            text: 'Se agregaron los datos del pedido',
                        });
                        this.usuarioPedido = null;
                        this.corporativoPedido = null;
                        this.nombrePedido = null;

                        this.pedido.paraRecoger = true; 
                        this.popUpDatosPedido = false;
                    },
                    cargarNumerosPedido: async ()=>{
                        await this.axios.post('/app/v1_caja/PuntoDeVentaCafeteria', {
                                    Opcion:'C',
                                    SubOpcion:8
                                })
                            .then(resp => {                            
                                    this.listas.numeroPedidosOcupados = resp.data.json
                                    if(resp.data?.json?.length > 0 ){
                                        this.listas.numeroPedidos = [];
                                        let totalPedidos = resp.data.json[0].NumeroMaximo
                                        for(let i=1;i<=totalPedidos;i++){
                                            this.listas.numeroPedidos.push({value:i,disabled:this.listas.numeroPedidosOcupados.findIndex(n=>n.NoPedido==i)>=0?true:false})                                           
                                        }
                                        let primerLibre = this.listas.numeroPedidos.find(n=>!n.disabled)
                                        if(primerLibre){
                                            this.pedido.numeroPedidoSeleccionado = primerLibre.value
                                        }                                         
                                    }
                                }
                            ).catch()
                    },
                    grabarNota: ()=>{                        
                        let valido = this.consulta().validarCampos('Punto de Venta Cafetería','C','Nota',this.textoNota,true,200);
                        if(!valido) return;

                        this.axios.post('/app/v1_caja/PuntoDeVentaCafeteria', {
                                    Opcion:'I',
                                    SubOpcion:6,
                                    NoComanda:this.noComanda,
                                    LineaComanda:this.lineaNota,
                                    Nota:this.textoNota,
                                }).then(resp=>{
                                    if(resp.data.codigo == 0){
                                        this.productosPedidos = 
                                            this.productosPedidos.map(p=>{
                                                if(p.Linea == this.lineaNota){
                                                    p.Nota = this.textoNota
                                                }
                                                return p;
                                            })

                                        this.popUpNotaComanda = false;
                                    }
                                })                            
                    },
                    consultaPuntoVenta: (SubOpcion)=>{
                        this.axios.post('/app/v1_caja/PuntoDeVentaCafeteria', {
                                    Opcion:'C',
                                    SubOpcion:SubOpcion,
                                    Bodega:this.bodega,
                                    Departamento:'060',
                                    SubDepartamento:1,
                                    Nivel:this.nivelPrecios                                    
                                })
                            .then(resp => {                            
                                    if (SubOpcion == 4){
                                       this.listas.mesas = resp.data.json
                                    }
                                    if (SubOpcion == 6){
                                       this.listas.pedidos = resp.data.json
                                    }
                                }
                            )
                    },
                    agregarProducto: async ()=>{
                        if(this.noComanda){
                            let status = await this.consulta().insertarDetalle()
                            if(status.codigo == 0){
                                this.productosPedidos.push({...this.producto,Cantidad:1,SubTotal:this.producto.Precio,Linea:status.Linea,Status:'S'});
                            }
                        }else{
                            let status = await this.consulta().insertarEncabezado();
                            if(status.codigo == 0){
                                this.noComanda = status.CorrelativoComanda                                
                                this.productosPedidos.push({...this.producto,Cantidad:1,SubTotal:this.producto.Precio,Linea:status.Linea,Status:'S'});
                            }
                        }
                        
                    },
                    insertarDetalle: async()=>{
                        if(this.pedido?.mesaSeleccionada??false){
                            return await this.axios.post('/app/v1_caja/PuntoDeVentaCafeteria', {
                                    Opcion:'I',
                                    SubOpcion:2,
                                    Bodega:this.bodega,
                                    Departamento:'060',
                                    SubDepartamento:1,
                                    Nivel:this.nivelPrecios,
                                    SucursalCafeteria:this.pedido.mesaSeleccionada.Sucursal,
                                    CodigoMesa:this.pedido.mesaSeleccionada.Codigo,
                                    CodigoProducto:this.producto.Codigo,
                                    TipoProducto:this.producto.Tipo,
                                    CantidadSolicitar:1,
                                    NoComanda: this.noComanda
                                })
                            .then(resp => {                            
                                    if (resp.data.codigo == 0){
                                       return resp.data
                                    }
                                    return resp.data
                                }
                            ).catch(()=>{return  {codigo:-1}})
                        }else if(this.pedido?.pedidoSeleccionado??false){
                            return await this.axios.post('/app/v1_caja/PuntoDeVentaCafeteria', {
                                    Opcion:'I',
                                    SubOpcion:5,
                                    Bodega:this.bodega,
                                    Departamento:'060',
                                    SubDepartamento:1,
                                    Nivel:this.nivelPrecios,
                                    SucursalCafeteria:this.pedido.pedidoSeleccionado.Sucursal,
                                    CodigoProducto:this.producto.Codigo,
                                    TipoProducto:this.producto.Tipo,
                                    CantidadSolicitar:1,
                                    NoComanda: this.noComanda
                                })
                            .then(resp => {                            
                                    if (resp.data.codigo == 0){
                                       return resp.data
                                    }
                                    return resp.data
                                }
                            ).catch(()=>{return  {codigo:-1}})
                        }else{
                            return  {codigo:-1}
                        }                        
                    },
                    insertarEncabezado: async ()=>{
                        if(this.pedido?.mesaSeleccionada??false){
                            return await this.axios.post('/app/v1_caja/PuntoDeVentaCafeteriaEncabezado', {
                                    Opcion:'I',
                                    SubOpcion:1,
                                    Bodega:this.bodega,
                                    Departamento:'060',
                                    SubDepartamento:1,
                                    Nivel:this.nivelPrecios,
                                    SucursalCafeteria:this.pedido.mesaSeleccionada.Sucursal,
                                    CodigoMesa:this.pedido.mesaSeleccionada.Codigo,
                                    CodigoProducto:this.producto.Codigo,
                                    TipoProducto:this.producto.Tipo,
                                    CantidadSolicitar:1
                                })
                            .then(resp => { // CorrelativoComanda                            
                                    if (resp.data.codigo == 0){
                                        this.pedido.mesaSeleccionada.Status = 'A'
                                        const mesaSeleccionada = this.listas.mesas.find(mesa=>mesa.Codigo==this.pedido.mesaSeleccionada.Codigo)
                                        if(mesaSeleccionada){
                                            mesaSeleccionada.Status = 'A'
                                        }
                                       return resp.data
                                    }
                                    return resp.data
                                }
                            ).catch(()=>{return  {codigo:-1}})
                        }else if(this.pedido?.pedidoSeleccionado??false){
                            return await this.axios.post('/app/v1_caja/PuntoDeVentaCafeteriaEncabezado', {
                                    Opcion:'I',
                                    SubOpcion:4,
                                    Bodega:this.bodega,
                                    Departamento:'060',
                                    SubDepartamento:1,
                                    Nivel:this.nivelPrecios,
                                    NoPedido:this.pedido.pedidoSeleccionado.NoPedido,   
                                    CorporativoPedido:this.pedido.pedidoSeleccionado.Corporativo,
                                    NombrePedido:this.pedido.pedidoSeleccionado.Nombre,                     
                                    CodigoProducto:this.producto.Codigo,
                                    TipoProducto:this.producto.Tipo,
                                    CantidadSolicitar:1
                                })
                            .then(resp => {                            
                                    if (resp.data.codigo == 0){
                                       const pedidoSeleccionado = this.listas.pedidos.find(pedido=>pedido.NoPedido == this.pedido.pedidoSeleccionado.NoPedido)

                                       if(pedidoSeleccionado){
                                        pedidoSeleccionado.Sucursal = resp.data.Sucursal
                                        pedidoSeleccionado.Codigo = resp.data.CorrelativoComanda
                                        pedidoSeleccionado.Status = 'A'
                                       }
                                       this.pedido.pedidoSeleccionado.Sucursal = resp.data.Sucursal;
                                       this.pedido.pedidoSeleccionado.Codigo = resp.data.CorrelativoComanda;
                                       this.pedido.pedidoSeleccionado.Status = 'A';

                                       return resp.data
                                    }
                                    return resp.data
                                }
                            ).catch(()=>{return  {codigo:-1}})
                        }else{
                            return  {codigo:-1}
                        }                      
                        
                    },
                    limpiarMesa:()=>{
                        this.pedido.mesaSeleccionada = null;
                        this.noComanda = null;
                    },
                    limpiarPedido:()=>{
                        this.pedido.paraRecoger = false; 
                        this.pedido.pedidoSeleccionado = null;
                        this.noComanda = null;
                    },
                    cargarMesa:()=>{     
                        let valido = this.consulta().validarCampos('Punto de Venta Cafetería','O','Pedido',this.pedido.mesaSeleccionada,false,0);
                        if(!valido){return;}

                        this.CargarDatosDocumento(this.pedido.mesaSeleccionada.TipoReceptor,
                                                  this.pedido.mesaSeleccionada.DocReceptor,
                                                  this.pedido.mesaSeleccionada.NombreFactura)

                       /*  if(this.pedido.pedidoSeleccionado && this.pedido.mesaSeleccionada){
                            this.consulta().limpiarPedido();
                        } */

                        if(!this.pedido.mesaSeleccionada || this.pedido.mesaSeleccionada.Status != 'A'){
                            if(this.nivelPrecios == 30){
                                this.recargarProductos(1)
                            }

                            this.consulta().limpiarPedido();
                            this.productosPedidos = []
                            this.noComanda = null                            
                            return;
                        }                                                          

                        this.axios.post('/app/v1_caja/PuntoDeVentaCafeteria', {
                                    Opcion:'C',
                                    SubOpcion:5,
                                    Departamento:'060',
                                    SucursalCafeteria:this.pedido.mesaSeleccionada.Sucursal,
                                    CodigoMesa:this.pedido.mesaSeleccionada.Codigo
                                })
                            .then(resp => {                            
                                    if (resp.data && resp.data.json.length > 0){
                                       this.productosPedidos = resp.data.json
                                       this.noComanda = resp.data.json[0].CorrelativoComanda  
                                       this.recargarProductos(this.productosPedidos[0].Nivel)
                                    }else{
                                        this.productosPedidos = []
                                        this.noComanda = null
                                    }                                                                        
                                })
                        this.consulta().limpiarPedido();
                    },
                    cargarPedido:()=>{  
                        let valido = this.consulta().validarCampos('Punto de Venta Cafetería','O','Pedido',this.pedido.pedidoSeleccionado,false,0);
                        if(!valido){return;}

                        this.CargarDatosDocumento(this.pedido.pedidoSeleccionado.TipoReceptor,
                                                  this.pedido.pedidoSeleccionado.DocReceptor,
                                                  this.pedido.pedidoSeleccionado.NombreFactura)

                        /* if(this.pedido.mesaSeleccionada && this.pedido.pedidoSeleccionado){
                            this.consulta().limpiarMesa();   
                        } */

                        if(!this.pedido.pedidoSeleccionado || this.pedido.pedidoSeleccionado.Status != 'A'){
                            this.recargarProductos(1)
                            this.productosPedidos = []
                            this.noComanda = null                            
                            return;
                        }                                                                        

                        this.axios.post('/app/v1_caja/PuntoDeVentaCafeteria', {
                                    Opcion:'C',
                                    SubOpcion:7,
                                    Departamento:'060',
                                    NoComanda:this.pedido.pedidoSeleccionado.Codigo,
                                    SucursalCafeteria:this.pedido.pedidoSeleccionado.Sucursal
                                })
                            .then(resp => {                            
                                    if (resp.data && resp.data.json.length > 0){
                                       this.productosPedidos = resp.data.json
                                       this.noComanda = this.pedido.pedidoSeleccionado.Codigo                                       
                                       this.recargarProductos(this.productosPedidos[0].Nivel)
                                    }else{
                                        this.productosPedidos = []
                                        this.noComanda = null
                                    }                                    
                                    this.pedido.paraRecoger = true 
                                })
                        this.consulta().limpiarMesa();
                    },
                    eliminarComanda: ()=>{
                        this.$vs.dialog({
                            type: 'confirm',
                            color: 'success',
                            title: 'Confirmar',
                            acceptText: 'SI',
                            cancelText: 'NO',
                            text: '¿Desea eliminar la comanda '+this.noComanda+'?',
                            accept: ()=>{

                                this.$refs.refValidarPass.iniciar((x) => {
                                    let permisoComanda = null;
                                    if (x != null) {
                                        permisoComanda = x.permisos.find((t) => t == "ELIMINAR_COMANDA");
                                    }

                                
                                    if (permisoComanda != undefined) {
                                        this.axios.post('/app/v1_caja/PuntoDeVentaCafeteria', {
                                            Opcion:'E',
                                            SubOpcion:2,
                                            NoComanda: this.noComanda
                                        }).then(resp=>{
                                            if(resp.data.codigo == 0){
                                                this.recargarProductos(1)
                                                this.consulta().limpiarMesa()
                                                this.consulta().limpiarPedido()
                                                this.CargarDatosDocumento(null,null,null)
                                                this.productosPedidos = []
                                                this.consulta().consultaPuntoVenta(4)  
                                                this.consulta().consultaPuntoVenta(6) 
                                            }
                                        })  
                                    } else {
                                        this.$vs.notify({
                                        time: 4000,
                                        title:
                                            "!No tiene permisos para eliminar comandas!",
                                        text: "Solicitar permiso para eliminación de comandas ",
                                        iconPack: "feather",
                                        icon: "icon-alert-circle",
                                        color: "warning",
                                        position: "top-center",
                                        });

                                        return;
                                    }
                                });   
                              
                            }
                        })   
                                                                   
                    },
                    peticionEliminarLineaComanda: async ()=>{
                        let respuesta = false;                        
                        await
                            this.axios.post('/app/v1_caja/PuntoDeVentaCafeteria', {
                                Opcion:'E',
                                SubOpcion:1,
                                LineaComanda:this.lineaEliminacion,
                                NoComanda: this.noComanda
                            })
                            .then(resp => {                            
                                    if (resp.data.codigo == 0){
                                        respuesta = true
                                    }
                                })

                        return respuesta;
                    },
                    eliminarDetalleComanda: (linea)=>{
                        this.lineaEliminacion = linea.data.Linea;
                        const isCanceled = new Promise((resolve, reject) => {
                            (async () => { 

                                if(this.productosPedidos.length == 1){
                                    reject('La comanda debe tener al menos 1 producto.')
                                    return
                                }
                                
                               
                                if(linea.data.Ensamblado != 'S' || linea.data.Status != 'P'){
                                    let respuesta = await this.consulta().peticionEliminarLineaComanda();
                                    if(respuesta) resolve();
                                    else reject()
                                    return;
                                }

                                this.$refs.refValidarPass.iniciar( async (x) =>{
                                        let permisoComanda = null;
                                        if (x != null) {
                                            permisoComanda = x.permisos.find((t) => t == "ELIMINAR_PRODUCTO");
                                        }

                                    
                                        if (permisoComanda != undefined) {
                                            let respuesta = await this.consulta().peticionEliminarLineaComanda();
                                            if(respuesta) resolve();
                                            else reject()
                                        } else {
                                                this.$vs.notify({
                                                time: 4000,
                                                title:
                                                    "!No tiene permisos para eliminar productos!",
                                                text: "Solicitar permiso para eliminación de productos ",
                                                iconPack: "feather",
                                                icon: "icon-alert-circle",
                                                color: "warning",
                                                position: "top-center",
                                                });

                                                reject('Sin permisos.')
                                        }
                                });                              
                            })()
                            
                           
                        })

                        linea.cancel = isCanceled      
                       
                    },
                    actualizarDetalleComanda: async (linea)=>{                     
                        const isCanceled = new Promise((resolve, reject) => {
                            if(linea.oldData.Ensamblado == 'S' && linea.oldData.Status == 'P'){
                                reject("Producto comandado no se puede modificar la cantidad")
                                return;
                            }   

                            this.axios.post('/app/v1_caja/PuntoDeVentaCafeteria', {
                                    Opcion:'I',
                                    SubOpcion:3,
                                    NoComanda: this.noComanda,
                                    Bodega: this.bodega,
                                    TipoProducto:linea.oldData.Tipo,
                                    LineaComanda:linea.oldData.Linea,                                    
                                    CodigoProducto:linea.oldData.Codigo,
                                    CantidadSolicitar:linea.newData.Cantidad
                                })
                            .then(resp => {                            
                                    if (resp.data.codigo == 0){
                                        resolve(false)
                                    }else{
                                        reject(resp.data.descripcion)
                                    }
                                })
                            .catch((e)=>{ if(e?.response?.data.Message)
                                            {reject(e.response.data.Message)} 
                                          else{reject(e.descripcion)} })
                        })

                        linea.cancel = isCanceled      

                    },
                    cargaCategorias:async ()=>{
                      await this.axios.post('/app/v1_caja/PuntoDeVentaCafeteria', {
                                    Opcion:'C',
                                    SubOpcion:2,
                                    Departamento:'060'
                                })
                            .then(resp => {                            
                                    if (resp.data && resp.data.json.length > 0){
                                       this.listas.categorias = resp.data.json
                                    }else{
                                       this.listas.categorias = []
                                    }
                                }
                            )
                    },
                    cargaSubDepartamentos:async ()=>{
                        await this.axios.post('/app/v1_caja/PuntoDeVentaCafeteria', {
                                    Opcion:'C',
                                    SubOpcion:3,
                                    Departamento:'060'
                                })
                            .then(resp => {                            
                                    if (resp.data && resp.data.json.length > 0){
                                       this.listas.subDepartamentos = resp.data.json                                      
                                    }else{
                                       this.listas.subDepartamentos = []
                                    }
                                }
                            )
                    },
                    cargaProductos:(Categoria)=>{
                            this.seleccionSubDepartamento = Categoria.CodigoSubDepartamento    

                            if(!this.bodega){
                                return
                            }

                            this.axios.post('/app/v1_caja/PuntoDeVentaCafeteria', {
                                    Opcion:'C',
                                    SubOpcion:1,
                                    Bodega:this.bodega,
                                    SubDepartamento:Categoria.CodigoSubDepartamento,
                                    Departamento:'060',
                                    Nivel:this.nivelPrecios
                                })
                            .then(resp => {                            
                                    if (resp.data && resp.data.json.length > 0){
                                        this.listas.productos = resp.data.json
                                        this.listas.productosFiltrados = resp.data.json
                                    }
                                }
                            )
                    }
                }
            },
            MostrarError(mensaje,tiempo=3000){
                this.$vs.notify({
                    time:tiempo,
                    title: 'Punto de Venta',
                    color: 'danger',
                    text:mensaje,
                    position: 'top-center'
                })
            },
        },
        mounted(){
            this.init();
        },
        computed:{
            sesion() {                
                return this.$store.state.sesion
            }
        },
        watch:{      
            popUpDatosPedido(valor){
                if(!valor){
                    this.usuarioPedido = null
                    this.nombrePedido  = null
                    this.corporativoPedido = null
                    this.pedido.numeroPedidoSeleccionado = null
                    this.pedido.paraRecoger=false;
                }
            },
            popUpHuellaMesa(valor){
                if(!valor){
                    this.popUpHuellaMesa = false; 
                    this.corporativoHuella=null; 
                    this.usuarioHuella=null;
                }
            },
            popUpSensorHuellas(valor){
                    if(!valor){                    
                        this.$refs.validarHuellas.desactivarValidador()
                        this.corporativoHuella = null;
                        this.usuarioHuella = null;
                    }
            }, 
            popUpFormaPago(popUpPago){
                if(!popUpPago){
                    this.factura.totalFactura = null
                    this.factura.tarjeta1 = {monto:null,posUtilizado:'VIS',noTarjeta:null,noAutorizacion:null}
                    this.factura.tarjeta2 = {monto:null,posUtilizado:'VIS',noTarjeta:null,noAutorizacion:null}
                    this.factura.efectivo = {monto:null,vuelto:null,saldo:null}
                }
            },     
            busquedaProducto(){
                this.filtrarProductos()
            },
            bodega(actual,anterior){
                if(anterior!=actual){
                    if(this.seleccionSubDepartamento){
                        this.consulta().cargaProductos({CodigoSubDepartamento:this.seleccionSubDepartamento})
                    }
                }
            }
        }
        
    }
</script>
<style>

.caj040 .button-icons .dx-link.dx-icon{
    font-size: 24px !important;
    width: 26px !important;
    height: 26px !important;    
}

.caj040 .dx-item.dx-tab{
    padding: 3px;
}

.caj040 .categoriasProductos { 
    flex: 1 0 auto;     
    min-width: 85px;
    min-height: 55px;
}

.caj040 .tabSeleccionado{
    background-color: #00953A;
    color: white;    
}

.caj040 .tabLibre{
    background-color: 'transparent';    
}

.caj040 .selectTipoDocumento .dx-dropdowneditor-button{
    min-width: 0px;
    width:0px;
}

.caj040 #selectMesa .dx-texteditor-input{
    color: white;
}

.caj040 #selectPedido .dx-texteditor-input{
    color: white;
}

.caj040 .numeros-pedidos{
    z-index: 9999999;
}

.caj040 .filaSelection:hover .filaSelection:focus .filaSelection{
    background-color: 'primary';
    cursor: 'pointer';
}

.caj040 .w-full.dx-widget.dx-visibility-change-handler{
 max-width: 1024px !important;
}

.caj040 .label-total
{
    font-size: 25px;
    color:black;
    text-align: center;
}

.caj040 .centered-element {
    margin: 0;
    top: 50%;
    transform: translateY(-50%);
}
.caj040 .columnaAmarrilla{
    background-color: #fce803 !important;
    background: #fce803 !important;
}
.caj040 .botonActivo{
    background:'primary' !important;
}

.caj040 .botonApagado{
    background: rgb(133, 133, 133) !important; 
}

/* Estilo tablas dev express */
.caj040 .dx-popup-title {
    background-color: #f4eade !important;
    color: #2f496e !important;
}

.caj040 .dx-list-item>#itemsMenuDrawer {
    color: #2988bc !important;
    background-color: #f4eade !important;
}

.caj040 .dx-list-item-selected>#itemsMenuDrawer {
    color: #f4eade !important;
    background-color: #ed8c72 !important;
}

.caj040 .dx-scrollable-container {
    touch-action: auto !important;
}

/*Ancho mínmo de los grid*/
.caj040 #Contenido .dx-datagrid {
    min-width: 302px;
}

.caj040 .dx-datagrid-headers td {
    vertical-align: middle !important;
}

.caj040 .dx-resizable {
    display: inline-grid;
}

.caj040 .dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed)>td {
    background-color: #00ccff !important;
    color: black !important;
}

.caj040 .dx-datagrid-rowsview .dx-row.dx-data-row.dx-column-lines.dx-state-hover>td {
    background-color: #79e4ff !important;
    color: black !important;
    font-size: 16px;
}


.caj040 .dx-datagrid-headers {
    background-color: linen !important;
    color: black !important;
    font-weight: bold;
}

.caj040 td {
    vertical-align: middle !important;
}
/* Fin Estilo tablas dev express */
.dx-state-disabled.dx-list-item{
 background-color: red;
}
</style>

<style scoped>
.numeros-pedidos{
    z-index: 9999999;
}

.filaSelection:hover .filaSelection:focus .filaSelection{
    background-color: 'primary';
    cursor: 'pointer';    
}

.Opciones {
    grid-area: Opciones;
    overflow-y: auto;
    overflow-x: hidden;
    width: max-content;
}

.Opciones>#toolbar {
    background-color: #f4eade;
    background-color: #2988bc;
}

#view {
    margin-left: 10px;
    margin-top: 10px;
}

.buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

.button {
    height: 125px;
    width: 150px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 20px !important;
}

.button:hover {
    background-color: blue !important;
}

.div-button {
    display: flex;
    justify-content: center;
}

.i-size {
    font-size: 40px;
    padding-bottom: 5px;
}
</style>