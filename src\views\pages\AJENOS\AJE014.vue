<template>
<div>
    <vx-card title="Rechazo de Cuenta Bancaria">
        <ValidationObserver ref="formValidate" v-slot="{  }" mode="lazy">
            <div class="flex mb-5 ">
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                    <SM-Buscar v-model="info.Lote" label="Lote" api="app/Ajenos/Busqueda_Honorarios_Lotes" :api_campos="['Tipo','Periodo','Lote','FechaCreado']" :api_titulos="['Tipo','Año','Lote','Creado']" :api_filtro="{Estado: 'P',Trasladado: 1,Tipo:'D'}" api_campo_respuesta="Id" api_campo_respuesta_mostrar="Lote" :callback_buscar="Consulta().Lotes" :callback_cancelar="Otros().Limpiar_Datos" :disabled_texto="true" :api_preload="true" />
                </div>
                <vs-spacer></vs-spacer>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                    <vs-input label="Hospital" class="w-full" :value="info.Empresa" disabled />
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                    <vs-input label="Fecha de Creación" class="w-full" :value="info.FechaCreado" disabled />
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                    <vs-input label="Fecha de Traslado" class="w-full" :value="info.FechaTraslado" disabled />
                </div>
            </div>
            <vs-divider></vs-divider>

            <div class="flex mb-5 ">
                <div class="w-full p-1">
                    <div class="contenido">
                        <vs-table2 max-items="10" search tooltip  :data="medicos" exportarExcel>
                            <template slot="header">
                                <h4>Médicos por Lote</h4>
                            </template>
                            <template slot="thead">
                                <th width="5px"></th>
                                <th width="80px">Cod. Méd</th>
                                <th>Médico</th>
                                <th>Tipo</th>
                                <th>Especialidad</th>
                            </template>

                            <template slot-scope="{data}">
                                <template v-for="(tr, indextr) in data">
                                    <tr :key="indextr" :class="[tr.EstadoValidacion==1?'autorizado':tr.EstadoValidacion==2?'denegado':tr.EstadoValidacion==3?'revisado':'']">
                                        <vs-td2>
                                            <vs-checkbox :val="tr" v-model="tr.Seleccion" />
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.Ajeno}}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.Medico}}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.Tipo}}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.Especialidad}}
                                        </vs-td2>
                                    </tr>
                                    <tr v-for="(f,index) in tr.Facturas" :key="index" class="detalle">
                                        <td></td>
                                        <td></td>
                                        <td colspan="2">
                                            <b>Factura:</b> {{f.FacturaSerie}}-{{f.FacturaNumero}}
                                        </td>
                                        <td colspan="2">
                                            {{f.FacturaFecha}}
                                        </td>
                                    </tr>
                                </template>
                            </template>
                        </vs-table2>
                        <!-- {{medicos}} -->
                    </div>
                </div>

            </div>
            <vs-divider></vs-divider>
            <div class="flex">
                <vs-spacer></vs-spacer>
                <!-- <vs-button type="filled" class="mr-3" disabled>Visualizar Partida por Lote</vs-button> -->
                <vs-button color="danger" type="filled" :disabled="!Validar" @click="Validar?Guardar().Rechazo():null">Rechazar Cuenta Bancaria</vs-button>
            </div>

            <div style="clear:both"></div>
        </ValidationObserver>
    </vx-card>
    <div class="facturas" v-if="info.MostrarFactura">
    </div>
</div>
</template>

<script>
export default {
    data() {
        return {
            /**
             * Documentación del módulo
             */
            doc_: [],

            /**
             * Editar el monto de la tabla
             */
            info: {
                Lote: null,
                Empresa: null,
                FechaCreado: null,
                FechaTraslado: null
            },

            /**
             * Listado de médicos por lote
             */
            medicos: [],

            /**
             * Listado de permisos
             */
            permisos: {
                editar: false
            }

        }
    },
    computed: {
        Validar() {
            return this.medicos.filter(f => f.Seleccion).length > 0
        }
    },
    components: {
        // Facturas: () => import('./AJE005_Factura.vue')
    },
    methods: {
        //=======================================================================================================
        // CONSULTA
        //=======================================================================================================
        Consulta: function() {
            return {
                Lotes: (item) => {
                    // console.log(item)
                    this.info.CuentaAjena = item.EsCuentaAjena == 1
                    this.info.FechaCreado = item.FechaCreado
                    this.info.Empresa = item.Empresa
                    this.info.FechaTraslado = item.TrasladoFecha
                    this.info.LoteNombre = item.Lote
                    this.Consulta().Consulta_Facturas(item.Id)
                },

                Consulta_Facturas: (Lote) => {
                    return this.axios.post('/app/ajenos/Honorarios_Facturas_Consulta', {
                            Lote,
                            Estado: 'P'
                        })
                        .then(resp => {
                            // console.log(resp)
                            this.medicos = []
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {

                                resp.data.json.forEach(m => {
                                    const existe = this.medicos.filter(f => f.Ajeno == m.Ajeno)
                                    if (existe.length == 0) {
                                        this.medicos.push({
                                            Tipo: m.AJE_Tipo,
                                            Ajeno: m.Ajeno,
                                            Especialidad: m.Especialidad,
                                            Medico: m.Medico,
                                            Seleccion: false,
                                            Facturas: [{
                                                FacturaFecha: m.FacturaFecha,
                                                FacturaNumero: m.FacturaNumero,
                                                FacturaSerie: m.FacturaSerie
                                            }]
                                        })
                                    } else {
                                        existe[0].Facturas.push({
                                            FacturaFecha: m.FacturaFecha,
                                            FacturaNumero: m.FacturaNumero,
                                            FacturaSerie: m.FacturaSerie
                                        })
                                    }
                                })
                            }
                        })
                }
            }
        },

        //=======================================================================================================
        // NUEVO
        //=======================================================================================================
        Nuevo: function() {
            return {}
        },

        //=======================================================================================================
        // GUARDAR
        //=======================================================================================================
        Guardar: function() {
            return {
                Rechazo: () => {
                    this.$vs.dialog({
                        type: 'confirm',
                        color: 'danger',
                        title: 'Confirmación',
                        acceptText: 'Rechazar Deposito',
                        cancelText: 'Cancelar',
                        text: `¿Desea rechazar la cuenta bancaria del médico?`,
                        accept: () => {
                            return this.axios.post('/app/ajenos/Pagos_Rechazo_Deposito', {
                                    Lote: this.info.Lote,
                                    Ajenos: this.medicos.filter(f => f.Seleccion).map(m => m.Ajeno)
                                })
                                .then(() => {
                                    this.Consulta().Consulta_Facturas(this.info.Lote)
                                })
                        }
                    })
                }
            }
        },

        //=======================================================================================================
        // EDITAR
        //=======================================================================================================
        Editar: function() {
            return {}
        },

        //=======================================================================================================
        // ELIMINAR
        //=======================================================================================================
        Eliminar: function() {
            return {}
        },

        //=======================================================================================================
        // OTROS
        //=======================================================================================================
        Otros: function() {
            return {
                Limpiar_Datos: () => {
                    Object.entries(this.info).forEach(([key]) => {
                        this.info[key] = null
                    });
                    // this.info.Total = '0.00'
                    this.medicos = []
                    // this.pagosVisibles = []
                },
                facturasCancelar: (actualizar = false) => {
                    this.info.MostrarFactura = false
                    if (actualizar) this.Consulta().Consulta_Pagos(this.info.Codigo)
                },
                agrupar: (items) => {
                    // console.log(items)
                    let respuesta = []

                    // Cargos
                    const cargos = items.filter(f => f.Tipo == "1")

                    const cargos30 = cargos.filter(f => f.Categoria == '30')
                    const cargos97 = cargos.filter(f => f.Categoria == '97')
                    const cargos98 = cargos.filter(f => f.Categoria == '98')
                    const cargos99 = cargos.filter(f => f.Categoria == '99')

                    // Funcion para crear el objecto
                    const crearObjeto = (info, tipo) => {
                        return {
                            seleccion: false,
                            Tipo: info.Tipo,
                            NombreSucursal: info.NombreSucursal,
                            TipoRegistro: info.TipoRegistro,
                            Documento: null,
                            Admision_FechaEgreso: null,
                            Cita_Cliente: null,
                            NombreCliente: null,
                            NombrePaciente: null,
                            NombreSeguro: null,
                            Categoria: info.Categoria,
                            NombreCategoria: info.NombreCategoria,
                            Valor: info.Valor,
                            Accion: tipo
                        }
                    }

                    // Agrupando cargos 30
                    if (cargos30.length > 0) {
                        const info = {
                            ...cargos30[0]
                        }
                        info.Valor = this.$formato_decimal(cargos30.reduce((acumulador, item) => parseFloat(acumulador) + parseFloat(item.Valor), 0), 2)
                        const resp = crearObjeto(info, 30)
                        this.pagosCargos["30"] = cargos30
                        respuesta.push(resp)
                    }

                    // Agrupando cargos 97
                    if (cargos97.length > 0) {
                        const info = {
                            ...cargos97[0]
                        }
                        info.Valor = this.$formato_decimal(cargos97.reduce((acumulador, item) => parseFloat(acumulador) + parseFloat(item.Valor), 0), 2)
                        const resp = crearObjeto(info, 97)
                        this.pagosCargos["97"] = cargos97
                        respuesta.push(resp)
                    }

                    if (cargos98.length > 0) {
                        const info = {
                            ...cargos98[0]
                        }
                        info.Valor = this.$formato_decimal(cargos98.reduce((acumulador, item) => parseFloat(acumulador) + parseFloat(item.Valor), 0), 2)
                        const resp = crearObjeto(info, 98)
                        this.pagosCargos["98"] = cargos98
                        respuesta.push(resp)
                    }

                    if (cargos99.length > 0) {
                        const info = {
                            ...cargos99[0]
                        }
                        info.Valor = this.$formato_decimal(cargos99.reduce((acumulador, item) => parseFloat(acumulador) + parseFloat(item.Valor), 0), 2)
                        const resp = crearObjeto(info, 99)
                        this.pagosCargos["99"] = cargos99
                        respuesta.push(resp)
                    }

                    // Otros grupos
                    respuesta.push(...items.filter(f => f.Tipo != "1"))

                    return respuesta
                },
                seleccionCargo: (item) => {
                    if (item.Accion) {
                        this.pagos.filter(f => f.Categoria == item.Accion).map(m => m.seleccion = item.seleccion)
                    }
                },
                verificarSeleccionParcial: (tipoCargo) => {
                    // Validando si es seleccion completa o parcial de una categoria de cargo (97 o 30)
                    const arr = this.pagosVisibles.filter(f => f.Tipo == 1 && f.Categoria == tipoCargo)
                    const cargos = this.pagos.filter(f => f.Tipo == 1 && f.Categoria == tipoCargo)
                    const seleccionados = cargos.filter(f => f.seleccion)
                    const nSeleccionados = cargos.filter(f => !f.seleccion).length
                    if (seleccionados.length > 0 && nSeleccionados > 0) {
                        if (arr.length > 0) {
                            const info = arr[0]
                            if (seleccionados.length > 0 && !info.seleccion) info.seleccion = true
                        }
                        return seleccionados.reduce((acumulador, item) => parseFloat(acumulador) + parseFloat(item.Valor), 0).toLocaleString("es-GT", {
                            style: "decimal",
                            maximumFractionDigits: 2,
                            minimumFractionDigits: 2
                        })
                    } else {

                        if (arr.length > 0) {
                            const info = arr[0]
                            if (seleccionados.length > 0 && !info.seleccion) info.seleccion = true
                            if (nSeleccionados > 0 && info.seleccion) info.seleccion = false
                        }
                        return null
                    }
                },
                reglaInfo: (item) => {
                    const info = {
                        Fitro: item.Fitro,
                        Regla: item.Regla,
                        Factura_0_Emitido: item.Factura_0_Emitido,
                        Factura_1_Emitido: item.Factura_1_Emitido,
                        Factura_2_Emitido: item.Factura_2_Emitido,
                        Factura_3_Emitido: item.Factura_3_Emitido,
                        Factura_0_Cancelado: item.Factura_0_Cancelado,
                        Factura_1_Cancelado: item.Factura_1_Cancelado,
                        Factura_2_Cancelado: item.Factura_2_Cancelado,
                        Factura_3_Cancelado: item.Factura_3_Cancelado,
                        Cuenta_Cerrada: item.Cuenta_Cerrada,
                        Candado_Seguro: item.Candado_Seguro,
                        Filtro_Fecha: item.Filtro_Fecha,
                        Regla_Fecha: item.Regla_Fecha
                    }
                    if (info.Filtro == 0) return ' - No coincide con el filtro'
                    if (info.Regla == 0) return ' - Sin regla de pago'
                    let regla = ''
                    if (info.Factura_0_Emitido == 0) regla += ' - Factura 0 no emitida'
                    if (info.Factura_1_Emitido == 0) regla += ' - Factura 1 no emitida'
                    if (info.Factura_2_Emitido == 0) regla += ' - Factura 2 no emitida'
                    if (info.Factura_3_Emitido == 0) regla += ' - Factura 3 no emitida'
                    if (info.Factura_0_Cancelado == 0) regla += ' - Factura 0 no cancelada'
                    if (info.Factura_1_Cancelado == 0) regla += ' - Factura 1 no cancelada'
                    if (info.Factura_2_Cancelado == 0) regla += ' - Factura 2 no cancelada'
                    if (info.Factura_3_Cancelado == 0) regla += ' - Factura 3 no cancelada'
                    if (info.Cuenta_Cerrada == 0) regla += ' - La cuenta no esta cerrada'
                    if (info.Filtro_Fecha == 0) regla += ' - Fecha de filtro superior al cargo'
                    if (info.Regla_Fecha == 0) regla += ' - Fecha de regla superior al cargo'
                    return regla
                }
            }
        }
    }
}
</script>

<style scoped>
.facturas {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100em;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 50px;
    z-index: 99999;
}
</style>
