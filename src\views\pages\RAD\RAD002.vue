<template>
<vx-card title="Escanear Estudios DX">
    <buscador ref="buscar_radiologias" buscador_titulo="Buscador / Radiología" :api="'app/radiologia/RadBusquedaOrdenes'" :campos="['Tipo','Admision','Nombre','Apellido']" :titulos="['Estudio','Admisión','Nombres','Apellidos']" :multiselect="false" :api_validar_campo="true" :api_reload="true" />

    <form v-on:submit.prevent="cargar_orden()">
        <div class="flex flex-wrap">
            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <vs-input label="Tipo" class="w-full" v-on:change="info.orden_tipo=info.orden_tipo.toUpperCase()" v-model="info.orden_tipo" :disabled="bloqueoBusqueda" />
            </div>
            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <label class="vs-input--label">Número</label>
                <vx-input-group class="">
                    <vs-input v-model="info.orden_numero" :disabled="bloqueoBusqueda" v-on:blur="cargar_orden()" />
                    <template slot="append">
                        <div class="append-text btn-addon">
                            <button type="submit" v-show="false" name="button"></button>
                            <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_radiologias()" icon="icon-search" v-if="bloqueoBusqueda==false"></vs-button>
                            <vs-button id="button-with-loading" color="danger" icon-pack="feather" @click="limpiar_orden()" icon="icon-x" v-else></vs-button>
                        </div>
                    </template>
                </vx-input-group>
            </div>
        </div>
    </form>
    <div class="flex flex-wrap">
        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
            <vs-input label="Cód. Paciente" class="w-full" v-model="info.paciente_codigo" disabled />
        </div>
        <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
            <vs-input label="Nombre del paciente" class="w-full" v-model="info.paciente_nombre" disabled />
        </div>
        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
            <vs-input label="Edad" class="w-full" v-model="info.paciente_edad" disabled />
        </div>
    </div>
    <div class="flex flex-wrap">
        <div class="w-full">
            <vs-input label="Refiere" class="w-full" v-model="info.refiere" disabled />
        </div>
    </div>
    <div v-if="bloqueoBusqueda">
        <vs-divider>Archivos</vs-divider>
        <div v-if="sesion.status_socket">
            <vs-button class="icon-camara" id="scanner_cargar" :disabled="bloqueoScanner" v-on:click="scanner(list.length-1)" icon-pack="fas" icon="fa-camera"> Agregar Imagen</vs-button>
        </div>
        <div v-else>
            <vs-button class="icon-camara" id="scanner_cargar" disabled="true" icon-pack="fas" icon="fa-camera" color="danger"> Socket Inactivo</vs-button>
        </div>
        
        <vs-divider />
        <div class="flex flex-wrap archivos">
            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5" v-for="(i,index) in list" :key="index">

                <expandable-image class="image" :src="'data:image/png;base64, ' +i.src" v-if="i.archivo_nombre">
                </expandable-image>

                <div v-else style="width:100%">
                    <img src="@/assets/images/others/notfound.png" alt="" style="max-width:100%;max-height:100%">
                </div>
                <!-- <vs-button color="danger" :id="'scanner_'+index" class="icon-eliminar" :disabled="i.archivo_nombre==null" v-on:click="remove_scanner(index)" icon-pack="fas" icon="fa-trash" v-if="i.archivo_nombre"> -->
                <!-- </vs-button> -->
            </div>
        </div>

    </div>
</vx-card>
</template>

<script>
// require styles

export default {
    data() {
        return {
            // litados
            list: [],
            //int
            scanner_index: 0,
            // booleanos
            bloqueoScanner: false,
            bloqueoBusqueda: false,
            // objetos
            info: {
                orden_tipo: '',
                orden_numero: null,
                paciente_codigo: null,
                paciente_nombre: '',
                paciente_edad: '',
                refiere: '',
            },
        }
    },
    computed:{
        sesion() {
            return this.$store.state.sesion
        }
    },
    components: {},
    methods: {
        cargar_orden() {
            if (this.info.orden_tipo.trim() == '' || this.info.orden_numero <= 0) return false
            // if (this.bloqueoBuscar == true) return false;
            this.bloqueoBuscar = true;

            this.$vs.loading({
                background: this.backgroundLoading,
                color: this.colorLoading,
                container: "#button-with-loading",
                scale: 0.45
            })
            this.axios.post('/app/radiologia/radordenes', {
                    tipo: this.info.orden_tipo,
                    orden: this.info.orden_numero
                })
                .then(resp => {
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        this.info.paciente_codigo = resp.data.json[0].Paciente
                        this.info.paciente_nombre = resp.data.json[0].NombrePaciente
                        this.info.paciente_edad = resp.data.json[0].Edad + ' ' + resp.data.json[0].EdadMedida
                        this.info.refiere = resp.data.json[0].NombreMedico
                        this.bloqueoBusqueda = true
                        this.bloqueoCampos = false

                        this.cargar_scanner()

                    } else {
                        this.$vs.dialog({
                            color: 'danger',
                            title: 'Radiología - Error',
                            text: (resp.data.codigo < 0) ? resp.data.descripcion : 'Sin Registros',
                        })
                    }

                    return false

                })
                .catch(() => {
                    // console.log(err)
                    this.$vs.loading.close("#button-with-loading > .con-vs-loading")
                    this.bloqueoBuscar = false;
                })
        },

        cargar_scanner() {
            
            this.axios.post('/app/radiologia/RadObtenerImagen', {
                    categoria: this.info.orden_tipo,
                    subfolder: this.info.orden_tipo + '-' + this.info.orden_numero,
                    // orden: this.info.orden_numero
                })
                .then(resp => {
                    // console.log(resp)
                    if (resp.data.codigo == 0) {
                        this.list = []
                        resp.data.listado.map(data => {
                            this.list.push({
                                src: data.src,
                                archivo_nombre: data.nombreArchivo
                            })
                        })
                    }
                    
                })
                .catch(() => {
                    
                })

        },

        buscar_radiologias() {
            this.$refs.buscar_radiologias.iniciar((data) => {
                // console.log(data)
                if (data != null) {
                    this.info.orden_tipo = data.Tipo.split('-')[0]
                    this.info.orden_numero = data.Tipo.split('-')[1]
                    this.cargar_orden()
                }
            })
        },

        limpiar_orden() {
            this.examenes = []
            this.info.orden_tipo = ""
            this.info.orden_numero = null
            this.info.paciente_codigo = null
            this.info.paciente_nombre = ''
            this.info.paciente_edad = ''
            this.info.refiere = ''
            this.bloqueoBusqueda = false
            this.list = []
        },

        scanner(index) {
            
            this.list.push({
                src: null,
                archivo_nombre: null
            })
            setTimeout(() => {
                index = this.list.length - 1
                // console.log(index)
                this.$vs.loading({
                    background: this.backgroundLoading,
                    color: this.colorLoading,
                    container: "#scanner_cargar",
                    scale: 0.45
                })
                this.scanner_index = index
                this.bloqueoScanner = true
                this.$socket.emit('scanner', {
                    quality: 70
                })
            }, 100)
        },

        // remove_scanner(index) {
        //     this.axios.post('/app/radiologia/RadEliminarImagen', {
        //             categoria: this.info.orden_tipo,
        //             subfolder: this.info.orden_tipo + '-' + this.info.orden_numero,
        //             nombreArchivo: this.list[index].archivo_nombre
        //             // orden: this.info.orden_numero
        //         })
        //         .then(resp => {
        //             // console.log(resp)
        //             if (resp.data.codigo == 0) {
        //                 this.list.splice(index, 1)
        //             }

        //         })
        // },

        guardarScanner(index) {
            this.axios.post('/app/radiologia/RadGuardarImagen', {
                    base64String: this.list[index].src,
                    categoria: this.info.orden_tipo,
                    subfolder: this.info.orden_tipo + '-' + this.info.orden_numero,
                    nombreArchivo: 'Escaner_WEB                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   '
                    // orden: this.info.orden_numero
                })
                .then(resp => {
                    // console.log(resp)
                    if (resp.data.codigo == 0) {
                        this.axios.post('/app/radiologia/RadSubeImagen', {
                                tipo: this.info.orden_tipo,
                                orden: this.info.orden_numero,
                            })
                            .then(() => {
                                this.list[index].archivo_nombre = resp.data.archivo_nombre
                            })

                    }

                })
        }

    },
    mounted() {
        this.sockets.subscribe('scanner_result', (data) => {
            this.$vs.loading.close("#scanner_cargar > .con-vs-loading")
            this.list[this.scanner_index].src = data
            this.guardarScanner(this.scanner_index)
            this.bloqueoScanner = false
        });
    },
    created() {
        // this.list.push({
        //     src: null,
        //     archivo_nombre: null
        // })
    }
}
</script>

<style>
.button {
    margin: 0px !important;
}

.btnx {
    margin-left: 10px !important;
    background: red;
    border-radius: 5px 0px 0px 5px;
}

.btn-drop {
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: red;

}

.menu-funcionalidad {
    /* padding-top:0 !important; */
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    padding: 15px;
    display: inline;
    float: right;
    border-radius: 10px;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 10px;
}

.menu-funcionalidad td {
    text-align: center;
    padding-right: 10px;
}

.vs-switch {
    height: 30px;
    width: 60px !important;
}

.vs-circle-switch {
    height: 25px;
    width: 25px;
}

.vs-switch--icon {
    font-size: 15px
}

.vs-switch.vs-switch-active .vs-switch--circle {
    margin-left: calc(100% - 30px);
}

.archivos .w-full {
    /* height: 150px !important; */
    /* display: flex; */
    position: relative;
    padding: 2px;
}

.archivos button {
    /* display: flex; */
    /* align-items: center; */
    /* justify-content: center; */
    margin: auto;
    position: absolute;
    top: 105px;
    left: calc(50% - 20px)
        /* margin-left: 2px; */
        /* align-items: center; */
}

.archivos img {
    height: 250px
}

.archivos .expandable-image {
    height: 250px
}
</style>
