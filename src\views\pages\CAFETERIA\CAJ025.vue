<template>

    
    <vx-card  title="Devolución en Admisiones" > 
        <SM-Tabs height="auto">
            <div class="tab p-4" label="Consultas" >
                <div class="flex">
                    <h5>Listado de Devoluciones</h5>
                    <vs-spacer></vs-spacer>
                </div>
                <br>
                <div class="flex flex-wrap" >
                    <div class="w-full  p-2">
                        <vs-table2 max-items="10" search pagination :data="ListaInfo" height="470px" class="mt-0 mb-0">
                            <template slot="thead">
                                <th order="Cod" width="100px">Código</th>
                                <th order="Fecha" filtro="Fecha" width="100px">Fecha</th>
                                <th order="SerieAdmision" width="100px">Serie Admision</th>
                                <th order="Admision" width="100px">Admision</th>
                                <th order="Valor" width="100px">Valor</th>                          
                                <th order="SerieRecibo" width="100px">Serie Recibo</th>
                                <th order="Recibo" width="100px">Recibo</th>
                                <th order="Accion" width="50px">Accion</th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2>{{tr.Cod}}</vs-td2>
                                    <vs-td2>{{tr.Fecha}}</vs-td2>
                                    <vs-td2>{{tr.SerieAdmision}}</vs-td2>
                                    <vs-td2>{{tr.Admision}}</vs-td2>
                                    <vs-td2>{{tr.Valor}}</vs-td2>
                                    <vs-td2>{{tr.SerieRecibo}}</vs-td2>
                                    <vs-td2>{{tr.Recibo}}</vs-td2>
                                    <vs-td2>
                                        <vs-button  @click="Editar = true;Info={...tr}; Cargar().ValidaAdmision(tr)" color="primary" size="small" style="display:inline" class="mr-2" icon-pack="feather" icon="icon-edit"></vs-button>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                </div>
            </div>
            <div class="tab p-4" Label="Edicion" v-if="Editar" >
                
                <div class="flex flex-wrap">
                    <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">
                        <label>Datos Devolución en Admisión</label>
                            <div class="flex flex-wrap">
                                <br>
                                <div class="xs:w-full md:w-6/12 lg:w-6/12 xl:w-6/12">
                                    <vs-input label="Código" class="w-full"  :value="Info.Cod" disabled/>
                                </div>
                                <div class="xs:w-full md:w-1/12 lg:w-1/12 xl:w-1/12"></div>
                                <div class="xs:w-full md:w-5/12 lg:w-5/12 xl:w-5/12">
                                    <vs-input  label="Fecha" class="w-full" :value="Info.Fecha"/>
                                </div>
                            </div>
                    </div>   
                    <div class="xs:w-full md:w-2/12 lg:w-2/12 xl:w-2/12"></div>
                    <div class="xs:w-full md:w-6/12 lg:w-6/12 xl:w-6/12">
                        <label>Datos de Admision</label>
                        <div class="flex flex-wrap">
                            <div class="xs:w-full md:w-2/12 lg:w-2/12 xl:w-2/12">
                                <vs-input label="Serie" class="w-full" :value ="Info.SerieAdmision" disabled/>
                            </div>
                            <div class="xs:w-full md:w-1/12 lg:w-1/12 xl:w-1/12"></div>
                            <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">
                                <div class="xs:w-full md:w-7/12 lg:w-7/12 xl:w-7/12">   
                                <SM-Buscar label="Admisión" v-model="Info.Admision"  api="app/v1_JefeCaja/CargarAdmisiones"
                                :api_campos="['SerieAdmision',	'Admision',	'Paciente',	'NombrePaciente',	'Entrada', 'Salida']" 
                                :api_titulos="['Serie Admisión',	'Admisión',	'#Paciente',	'Nombre Paciente',	'#Entrada', '#Salida']"
                                api_campo_respuesta_mostrar="Info.Admision"
                                :disabled_texto="true" 
                               :api_preload="true" :mostrar_busqueda="true" 
                               :callback_buscar="Cargar().ValidaAdmision" 
                               />
                            </div>
                            </div>    
                        </div>
                        <div class="flex flex-wrap">
                            <vs-input label="Nombre Paciente" class="w-full" v-model="Info.NombrePaciente" disabled/>
                        </div>
                    </div>
                <br>
                <vs-divider></vs-divider>
                <div class="flex flex-wrap">
                    <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">
                        <label>Devolución por Recibo</label>
                        <div class="flex flex-wrap">
                            <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">
                                <vs-input label="Serie" class="w-full" :value="Info.SerieRecibo" disabled/>
                            </div>
                            <div class="xs:w-full md:w-1/12 lg:w-1/12 xl:w-1/12"></div>
                            <div class="xs:w-full md:w-7/12 lg:w-7/12 xl:w-7/12">                             
                                <SM-Buscar label="Recibo" v-model="Info.Recibo"  api="app/v1_JefeCaja/CargarRecibo"
                                :api_campos="['SerieRecibo',	'Recibo',	'FechaRec',	'Total',	'Aplicado', 'Nombre']" 
                                :api_titulos="['Serie',	'Recibo',	'Fecha',	'#Total',	'#Aplicado', '#Nombre']" 
                                :api_filtro="{'Codigo': Info.Cod, 'SerieAdmision':Info.SerieAdmision,'Admision': Info.Admision}"
                                api_campo_respuesta_mostrar="Info.Recibo"
                                :disabled_texto="true" 
                               :api_preload="true" :mostrar_busqueda="true"  
                               :callback_buscar="Cargar().CargaRecibo" :disabled_busqueda="Bloquear"
                               />
                            <br>
                            <br>
                            </div>
                        </div>
                        <label>Datos Factura</label>
                        <div class="flex flex-wrap">
                            <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">
                                <vs-input label="Serie" class="w-full"  v-model="Info.SerieFact" disabled/>
                            </div>
                                <div class="xs:w-full md:w-1/12 lg:w-1/12 xl:w-1/12"></div>
                                <div class="xs:w-full md:w-7/12 lg:w-7/12 xl:w-7/12">
                                    <vs-input label="Factura" type="Factura" class="w-full"  v-model="Info.Factura" disabled/>
                                </div>
                            <div class="xs:w-full md:w-6/12 lg:w-6/12 xl:w-6/12">
                                <br>
                                    <vx-input-group  class="mb-base" >
                                        <template  slot="prepend">
                                            <div class="prepend-text bg-primary">
                                                <span>Q</span>
                                            </div>
                                        </template>
                                        <ValidationProvider rules="numero_minimo:0|numero_decimal:2|required" v-slot="{ errors }" >
                                            <vs-input  v-model="Info.Valor"
                                                :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"/>
                                        </ValidationProvider>
                                    </vx-input-group>   
                            </div>
                        </div>    
                    </div>   
                    <div class="xs:w-full md:w-1/12 lg:w-2/12 x2:w-2/12"></div>
                    <div class="xs:w-full md:w-6/12 lg:w-6/12 xl:w-6/12">
                        <label>Datos Facturas según Recibo</label>
                        <br>
                        <div class="xs:w-full sm:w-full md:w-12/12 lg:w-12/12 xl:w-12/12">
                            <vs-table2 max-items="10"  :data="ReciboFacturas" height="200px" class="mt-0 mb-0">
                                <template slot="thead">
                                    <th order="SerieRecibo" width="50px">Serie Recibo</th>
                                    <th order="Recibo" width="70px">Recibo</th>
                                    <th order="SerieFactura" width="50px">Serie Factura</th>
                                    <th order="Factura" width="70px">Factura</th>
                                    <th order="Monto" width="50px">Monto</th>
                                    <th order="Acción" width="50px">Acción</th>
                                </template>
                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td2>{{tr.SerieRecibo}}</vs-td2>
                                        <vs-td2>{{tr.Recibo}}</vs-td2>
                                        <vs-td2>{{tr.SerieFactura}}</vs-td2>
                                        <vs-td2>{{tr.Factura}}</vs-td2>
                                        <vs-td2>{{parseFloat(tr.Monto).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}</vs-td2>
                                        <vs-td2>
                                            <vs-button id="button-with-loading" color="success"   @click.native="SelecionarFactura(tr)"
                                            icon-pack="fas" icon="fa-check-circle"></vs-button>
                                        </vs-td2>
                                    </tr>
                                </template>
                            </vs-table2>
                        </div>  
                    </div>
                </div>
                    <br>
                    <div class="w-full">  
                        <ValidationProvider rules="max:250|required" v-slot="{ errors }">
                            <vs-input label="*Razón"   class="w-full"  v-model="Info.Razon" 
                                :danger="errors.length > 0"
                                :danger-text="(errors.length > 0) ? errors[0] : null" />
                        </ValidationProvider>
                        <br>
                        <br>
                    </div>
                    <div class="w-full">
                        <div class="flex flex-wrap">
                            <div class="xs:w-full md:w-2/12 lg:w-2/12 xl:w-2/12">
                                <label>Cheque</label>
                                <SM-Buscar label="Cuenta" v-model="Info.CuentaBanco"  api="app/v1_JefeCaja/CargarCuenta"
                                :api_campos="['Empresa',	'CuentaBanco',	'NombreCuenta']" 
                                :api_titulos="['#Empresa',	'Cuenta',	'Nombre Cuenta']" 
                                api_campo_respuesta="CuentaBanco"
                                api_campo_respuesta_mostrar="CuentaBanco"
                                :api_preload="true" :disabled_texto="true" 
                                :mostrar_busqueda="true" :callback_buscar="Cargar().CargaCuenta" /> 
                            </div> 
                            <div class="xs:w-full md:w-1/12 lg:w-1/12 xl:w-1/12"></div>
                            <div class="xs:w-full md:w-9/12 lg:w-9/12 xl:w-9/12">
                                <br>
                                <br>
                                <label>{{Info.BNombreBanco}}</label>  
                            </div>
                            <div class="xs:w-full md:w-2/12 lg:w-2/12 xl:w-2/12">
                                <vs-input type="number" label="Cheque" class="w-full" v-model="Info.Movimiento" v-on:blur="Cargar().CargaFechaCheque()"/>    
                            </div> 
                            <div class="xs:w-full md:w-1/12 lg:w-1/12 xl:w-1/12"></div>
                            <div class="xs:w-full md:w-9/12 lg:w-2/12 xl:w-2/12">
                                <vs-input label="Fecha Cheque" class="w-full" v-model="Info.FechaPago" disabled/>  
                            </div>
                        </div>
                        <br>
                    </div>                  
                    <vs-divider></vs-divider>
                    <div class="w-full">
                        <label>Aplicada a otra Admisión</label>
                        <div class="flex flex-wrap">
                            <div class="xs:w-full md:w-6/12 lg:w-6/12 xl:w-6/12">
                                <div class="flex flex-wrap">
                                <div class="xs:w-full md:w-2/12 lg:w-2/12 xl:w-2/12">
                                    <vs-input label="Serie" class="w-full" v-model="Info.SerieAdmAplicada"/>
                                </div>
                                <div class="xs:w-full md:w-1/12 lg:w-1/12 xl:w-1/12"></div>
                                <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">
                                    <vs-input label="Admisión" type="number" class="w-full" v-model="Info.AdmAplicada"/>
                                </div>
                                </div>
                                <vs-divider></vs-divider>
                            </div>
                            <div class="xs:w-full md:w-6/12 lg:w-5/12 xl:w-6/12">
                                <div class="flex flex-wrap">
                                    <div class="xs:w-full md:w-2/12 lg:w-2/12 xl:w-2/12">
                                        <vs-input label="Serie" class="w-full" v-model="Info.SerieRecAplicada"/>
                                    </div>
                                    <div class="xs:w-full md:w-1/12 lg:w-1/12 xl:w-1/12"></div>
                                    <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">
                                        <vs-input label="Recibo" type="number" class="w-full" v-model="Info.ReciboAplicada"/>
                                    </div>
                                    <br>
                                </div>
                                <vs-divider></vs-divider>
                            </div>
                            <div class="xs:w-full md:w-2/12 lg:w-2/12 xl:w-2/12">
                                <vs-button color="success" class="md:w-full"
                                    :disabled="ActivarBtn"
                                    @click="Guardar()">
                                    Guardar
                                </vs-button> 
                            </div>
                        </div>    
                    </div>
                </div>  
                </div>  
        </SM-Tabs> 
    </vx-card>
  </template>
  
  <script>
  export default {
    data(){
        return{
            Info: {
                Cod: 0, 
                SerieAdmision: null, 
                Admision: null,
				Fecha : null,
				CuentaBanco: 0,
				Movimiento: 0,
				Razon: null,
				Valor: 0,
				Usuario: null,
				Periodo: 0,
				RelacionSerieAdm: null,
				RelacionAdm: 0,
				SerieRecibo: null,
				Recibo: 0,
				RelacionSerieRec: null,
				RelacionRec: 0,
				SerieFact: null,
				Factura: 0,
				FechaPago: null,
				BNombreBanco: null,
                NombrePaciente: null
            },
            ListaInfo: [],
            Editar: false,
            ReciboFacturas: [],
            ActivarBtn: true,
            FechaRec: null,
            Bandera: 0,
            Bloquear: true,
        }
    },
    watch: {
        Editar(value) {
            if (!value) {
                this.Info.SerieAdmision = null, 
                this.Info.Admision = null,
				this.Info.Fecha = null,
				this.Info.SerieRecibo = null,
				this.Info.Recibo = 0
            }
        }
    },
    methods:{
        Cargar(){
            return {
                CargaAdmisiones: ()=>{
                        this.axios.post('/app/v1_JefeCaja/CargarDevAdmisiones', {

                        })
                        .then(resp =>{    
                                      
                            if (resp.data.codigo == 0) {
                                this.ListaInfo = resp.data.json.map(m => {
                                    return {
                                        ...m,
                                        Valor: parseFloat(m.Valor).toFixed(2)
                                    }
                                })                                            
                            }
                        })
                    },
                ValidaAdmision: (data)=>{
                        if (this.Info.SerieAdmision != data.SerieAdmision && this.Info.Admision != data.Admision)
                        {
                             this.Info.SerieRecibo = '';
                             this.Info.Recibo = '';
                             this.ReciboFacturas = [];
                        }
                    
                        this.Info.SerieAdmision = data.SerieAdmision;
                        this.Info.Admision = data.Admision;

                        this.axios.post('/app/v1_JefeCaja/ValidarAdmision', {
                            SerieAdmision: this.Info.SerieAdmision,
                            Admision: this.Info.Admision,
                        })
                        .then(resp => {
                                this.Info.SerieAdmision = resp.data.SerieAdmision;
                                this.Info.Admision = resp.data.Admision;
                                this.Info.NombrePaciente = resp.data.NombrePaciente;
                                this.Bloquear =false;
                        })
                        .catch(() => {
                           this.Bandera = 1
                           this.Info.NombrePaciente = ''
                        })
                },
                
                CargaRecibo: (data)=>{
                        this.Info.SerieRecibo = data.SerieRecibo;
                        this.Info.Recibo = data.Recibo;
                        this.Cargar().RecibosFacturas();
                },
                RecibosFacturas: ()=>{      
                    this.axios.post('/app/v1_JefeCaja/ValidarRecibosFacturas', {
                            SerieRecibo: this.Info.SerieRecibo,
                            Recibo: this.Info.Recibo,    
                        })
                        .then(resp =>{             
                                this.ReciboFacturas = resp.data.json.map(m => {
                                    return {
                                        ...m,
                                        Monto: parseFloat(m.Monto).toFixed(2)
                                    }  
                                })
                        }) 
                },
                CargaCuenta: (data)=>{
                        this.Info.CuentaBanco = data.CuentaBanco;
                        this.Info.BNombreBanco = data.NombreCuenta
                },
                CargaFechaCheque: ()=>{  
                        this.axios.post('/app/v1_JefeCaja/RecuperarFechaCheque', {
                            CuentaBanco: this.Info.CuentaBanco,
                            Movimiento: this.Info.Movimiento
                        })
                        .then(resp =>{                 
                            resp.data.json.map(m => {
                                this.Info.FechaPago = m.FechaPago
                            })
                    })
                    .catch(() => {
                            this.Bandera = 1
                        })
                },
                init: () => {
                    this.Cargar().CargaAdmisiones()
                }
            }
        },
        SelecionarFactura(data){ 
            let Valor = parseFloat(this.Info.Valor)
            let Monto = parseFloat(data.Monto)

            if(Valor <= Monto){
                this.Info.SerieFact = data.SerieFactura;
                this.Info.Factura = data.Factura;
                if(this.Bandera == 0){
                    this.ActivarBtn = false
                }
                
            }
            else{
                this.Info.SerieFact = ''
                this.Info.Factura = ''
                this.ActivarBtn = true

                this.$vs.notify({
                color: 'danger',
                    title: 'Alerta',
                    text: 'El Valor ingresado es mayor al monto de la factura seleccionada.',
                })
            } 
        },
        Guardar(){
            if (this.Info.RelacionAdm == '')
            {
                this.Info.RelacionAdm = 0
            }
            if (this.Info.RelacionRec == '')
            {
                this.Info.RelacionRec = 0
            }
            this.axios.post('/app/v1_JefeCaja/ModificarDevolucion', {
                Codigo: this.Info.Cod, 
                SerieAdmision: this.Info.SerieAdmision, 
                Admision: this.Info.Admision,
                SerieRecibo: this.Info.SerieRecibo,
                Recibo: this.Info.Recibo,
                Fecha: this.Info.Fecha,
                CuentaBanco: this.Info.CuentaBanco,
                Movimiento: this.Info.Movimiento,
                Razon: this.Info.Razon,
                Valor: this.Info.Valor,
                RelacionSerieAdmision:  this.Info.RelacionSerieAdm,
                RelacionAdmision: this.Info.RelacionAdm,
                RelacionSerieRecibo: this.Info.RelacionSerieRec,
                RelacionRecibo: this.Info.RelacionRec,
                SerieFactura: this.Info.SerieFact,
                Factura: this.Info.Factura,
                FechaPago: this.Info.FechaPago
            })
            .then(resp => {
                if (resp.data.codigo == 0){
                    this.Info.Cod = ''
                    this.Info.SerieAdmision = ''
                    this.Info.Admision = ''
                    this.Info.Fecha = ''
                    this.Info.CuentaBanco = ''
                    this.Info.Movimiento = ''
                    this.Info.Razon = ''
                    this.Info.Valor = ''
                    this.Info.RelacionSerieAdm = ''
                    this.Info.RelacionAdm = ''
                    this.Info.SerieRecibo = ''
                    this.Info.Recibo = ''
                    this.Info.RelacionSerieRec = ''
                    this.Info.RelacionRec = ''
                    this.Info.SerieFact = ''
                    this.Info.Factura = ''
                    this.Info.FechaPago = ''
                    this.Info.NombrePaciente = ''
                    this.Info.BNombreBanco= ''
                    this.ReciboFacturas = []
                    this.ActivarBtn = true
                    this.Editar = false
                    this.Cargar().CargaAdmisiones()
                }
                
            }
       ) }
    },
    async mounted() {
        this.Editar =false;
        this.Cargar().init();
    }
}
  </script>