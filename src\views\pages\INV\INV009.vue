<template>
  <vx-card
    title="Mantenimiento Estado de Productos"
  >
    <vs-row>
      <vs-col vs-justify="center" vs-align="center" vs-w="5">
        <label class="vs-input--label">Estado Producto:</label>
        <multiselect
          v-model="Estado"
          :options="ListadoEstados"
          :allow-empty="false"
          placeholder="Seleccione un estado"
          track-by="Id_Estado"
          label="Nombre_Estado"
          deselect-label="Seleccionado"
          z-index="105"
        ></multiselect>
      </vs-col>
      <vs-col vs-justify="center" vs-align="center" vs-w="5">
        <label class="vs-input--label">Bodega:</label>
        <multiselect
          v-model="Bodega"
          :options="ListadoBodegas"
          :allow-empty="false"
          placeholder="Seleccione una bodega"
          track-by="Codigo"
          label="Nombre_Bodega"
          deselect-label="Seleccionado"
          z-index="106"
          @select="cargar_productos"
        ></multiselect>
      </vs-col>

    </vs-row>
    
    <vs-row>
      <vs-col vs-justify="center" vs-align="center" vs-w="12" >
        <label class="vs-input--label">Productos:</label>
          <multiselect            
            v-model="ListadoProductosGuardar"
            id="ajax"
            label="Nombre"
            track-by="Codigo"
            placeholder="Escriba para Buscar"
            open-direction="bottom"
            :options="ListadoProductos"
            :multiple="true"
            :searchable="true"
            :loading="isLoading"
            :internal-search="true"
            :clear-on-select="false"
            :close-on-select="false"
            :options-limit="500"
            :limit="500"
            :limit-text="limitText"
            :show-no-results="false"
            :hide-selected="true"
            v-if="ListadoProductos!=null"
          >
          </multiselect>
      </vs-col>
    </vs-row>

    <vs-row>
      <vs-col vs-justify="center" vs-align="center" vs-w="12" id="columna2">
        <vs-divider></vs-divider>
        <template lang="html">
          <div>
          </div>
        </template>
      </vs-col>
    </vs-row>
    <vs-row>
      <vs-col vs-w="9"> </vs-col>
      <vs-col vs-w="3">     
        <vs-button :disabled="ListadoProductosGuardar.length == 0 || !Bodega || !Estado" @click="guardar()">Guardar</vs-button>
      </vs-col>
    </vs-row>
  </vx-card>
</template>

<script>
import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";
export default {
  data() {
    return {
      Estado: null,
      info: null,
      Bodega: null,
      ListadoEstados: [],
      ListadoBodegas: [],
      isLoading: false,
      ListadoProductos:null,
      ListadoProductosGuardar: [
        
      ],
      Producto: { Codigo: null, Nombre: null }
    };
  },
  components: {
    Multiselect
  },
  computed: {},
  methods: {
    guardar() {
      this.$vs.loading();

      this.ListadoProductosGuardar.forEach(Producto => {
        let Prod = {};
        Prod.Codigo_Producto = Producto.Codigo;
        Prod.Id_Estado = this.Estado.Id_Estado;
        Prod.Codigo_Bodega = this.Bodega.Codigo;
        this.axios
          .post("/app/inventario/guardar_estado", Prod)
          .then(() => {
            this.guardadoconexito = true;
            this.$vs.loading.close();
            this.Estado = null;
            this.Bodega = null;
            this.ListadoProductosGuardar = [];
            this.$vs.notify({
              title: "Guardado:",
              text: "Guardado",
              color: "success",
              time: 10000,
              icon: "check_box"
            });
          })
          .catch(() => {
            this.$vs.loading.close();
          });
      });
    },
    cargar_estados() {
      this.axios
        .post("/app/inventario/listado_estados_productos", {})
        .then(resp => {
          //
          this.ListadoEstados = resp.data.json;
        })
        .catch(err => {
          console.warn(err);
        });
    }, //Fin de metodo cargar_estados
    cargar_bodegas() {
      this.axios
        .post("/app/inventario/invBodegalistHosp", {})
        .then(resp => {
          //
          this.ListadoBodegas = resp.data.json;
        })
        .catch(err => {
          console.warn(err);
        });
    }, //Fin de metodo cargar_bodegas
    
    cargar_productos(Bodega) {
      this.ListadoProductos=[];
      this.ListadoProductosGuardar=[];
      this.isLoading = true;
      let Prod = {};
      Prod.Input = '';
      Prod.Codigo_Bodega =Bodega.Codigo;
      this.axios
        .post("/app/inventario/BusquedaProductoFarmacia", Prod)
        .then(resp => {
          this.ListadoProductos = resp.data.json;
          this.isLoading = false;
        })
        .catch(err => {
          console.warn(err);
          this.isLoading = false;
        });
    }, //Fin de cargar productos
    limitText(count) {
      return `y otros ${count} productos`;
    },
  },
  mounted() {
    this.cargar_estados(), this.cargar_bodegas();
  },
  created() {}
};
</script>

<style>
.vs-table--tbody {
  z-index: 1;
}

.vs-con-table .vs-con-tbody {
  overflow: initial;
}
</style>
