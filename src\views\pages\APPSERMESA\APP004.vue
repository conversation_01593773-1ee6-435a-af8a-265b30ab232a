<template>
<vx-card title="Artículos de Interes">
    <div class="flex flex-wrap">
        <div class="w-full md:w-1/5">
            <div style="padding:10px">
                <vx-card class="overlay-card overflow-hidden" >
                    
                    <template slot="no-body">
                        <img src="@/assets/images/others/notfound.png" alt="user-profile-cover" class="responsive">
                        <div class="card-overlay text-white flex flex-col justify-between">
                            <h4 class="text-white mb-4">Artículo #1</h4>
                            <p>Información</p>
                        </div>
                    </template>
                </vx-card>
            </div>
        </div>
    </div>
</vx-card>

<!-- <div class="">
        <vs-row vs-justify="center">
          <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="6">
            <vs-card>
              <div slot="header">
                <h3>
                  Hello world !
                </h3>
              </div>
              <div>
                <span>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</span>
              </div>
              <div slot="footer">
                <vs-row vs-justify="flex-end">
                  <vs-button type="gradient" color="danger" icon="favorite"></vs-button>
                  <vs-button color="primary" icon="turned_in_not"></vs-button>
                  <vs-button color="rgb(230,230,230)" color-text="rgb(50,50,50)" icon="settings"></vs-button>
                </vs-row>
              </div>
            </vs-card>
          </vs-col>
        </vs-row>
    </div> -->
</template>

<script>
export default {
    data() {
        return {
            emergencias: [],
            count_users: 0,
            count_emergency: 0
        }
    },
    components: {
        // flatPickr
    },
    // computed: {

    // },
    methods: {

    },
    created() {

    }
}
</script>

<style>
.button {
    margin: 0px !important;
}

.btnx {
    margin-left: 10px !important;
    background: red;
    border-radius: 5px 0px 0px 5px;
}

.btn-drop {
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: red;

}

.popup-generar {
    height: 100%
}
</style>
