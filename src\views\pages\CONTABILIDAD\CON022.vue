<template>
<vx-card class="Compras">
    <div class="content content-pagex">
        <vs-divider class="label-size"> AUXILIAR COMPRAS </vs-divider>

        <div class="flex-container">
            <div class="w-full flex flex-wrap">
                <vs-col class="md:w-full lg:w-full xl:w-4/12 p-2">
                    <vx-card class="w-full" style="padding:10px 20px">
                        <div class="w-full" align="right">
                            <div class="w-full" style="padding:5px 5px">
                                <DxButton :width="120" text="Partida" type="success" @click="GenerarPartida()" />&nbsp;
                                <DxButton :width="50" icon="pulldown" type="normal" @click="ConsultaAuxiliarG()" /> 

                            </div>
                        </div>  
                        <div class="w-full">
                        <label class="label-size">Período</label>
                            <DxSelectBox
                                :data-source="ListaPeriodos"
                                :display-expr="formatPeriodo"
                                value-expr="CodigoPeriodo"
                                v-model="cbPeriodos"
                                :search-enabled="true"
                                :accept-custom-value="true"
                                @value-changed="guardarPeriodoCompleto"
                            />
                        </div>
                        <div class="w-full" style="margin-top: 10px;">
                        <label class="label-size">Sucursal</label>
                              <DxSelectBox
                                    :items="ListaSucursal"
                                    :show-clear-button="true"
                                    v-model="cbSucursal" 
                                    :searchable="true" 
                                    :display-expr="formatSucursal"
                                    value-expr="Hospital"
                                    :search-enabled="true"
                                    :accept-custom-value="true"
                                />  
                        </div>
                        <div style="margin-top: 10px;">
                            <vs-alert v-if="DescMayorizada === 'M'" active="true" color="warning" style="text-align: center; height: 40px;">
                                <label>Hay una partida mayorizada en este período.</label>                    
                            </vs-alert>          
                        </div>
                    </vx-card>
                </vs-col>

                <!-- Reportes -->
                <vs-col class="w-full md:w-full lg:w-full xl:w-8/12 p-1">
                     <vx-card class="w-full">
                        <vs-row class="w-full">
                            <vs-row class="md:w-6/12 lg:w-6/12 xl:w-6/12">
                                <div class="w-full">
                                    <div class="w-full" style="padding:2px; margin-top: 10px" v-show="PendienteV">
                                        <vs-checkbox icon-pack="feather" icon="icon-check" color="success" v-model="Pendiente">Solo Pendientes de Revisión</vs-checkbox>
                                    </div>
                  <!--                   <div style="display: flex; margin-top: 10px;">
                                        <div style="padding:2px;width: 30%" v-show="FiltroV">
                                            <vs-checkbox icon-pack="feather" icon="icon-check" color="success" v-model="FiltroD">
                                                Filtrar Registro
                                            </vs-checkbox>
                                        </div>
                                        <div style="padding:2px; width: 70%;" v-show="FiltroV">

                                          <DxDateBox v-model="FechaRegistro" placeholder="Fecha" :disabled="!FiltroD"   display-format="dd/MM/yyyy"/>
                                        </div>
                                    </div> -->

                                    <div style="display: flex; margin-top: 10px;">
                                        <div style="padding:2px;width: 30%" v-show="FiltroVF">
                                            <vs-checkbox icon-pack="feather" icon="icon-check" color="success" v-model="Filtro">
                                                Filtrar Fecha
                                            </vs-checkbox>
                                        </div>
                                        <div style="padding:2px; width: 70%;" v-show="FiltroVF">
                                          <DxDateBox v-model="Fecha" placeholder="Fecha" :disabled="!Filtro"   display-format="dd/MM/yyyy"/>
                                        </div>
                                    </div>
                            
                                    <div class="w-full" v-show="CuentaV">
                                        <ValidationProvider name="Cuentas">
                                            <label class="label-size">Cuenta</label>
                                            <multiselect
                                                    v-model="cbCuentas"
                                                    :options="cuentasFormateadas"
                                                    :searchable="true"
                                                    :close-on-select="true"
                                                    :show-labels="false"
                                                    placeholder="Seleccionar Cuenta"
                                                    label="label"
                                                    track-by="CodigoCuenta"
                                                    
                                                >
                                                <span slot="noOptions">Lista no disponible.</span>
                                            </multiselect>
                                        </ValidationProvider>
                                    </div>
                                </div>
                            </vs-row>
                            
                             <vs-row class="w-full md:w-6/12 lg:w-6/12 xl:w-6/12">
                                <div>
                                    <div class="w-full bg-white rounded-xl shadow-sm p-4">
                                        <div class="space-y-3">
                                            <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors">
                                                <label class="flex items-center cursor-pointer w-full">
                                                    <span class="text-gray-700 font-medium mr-3 w-6">1.</span>
                                                    <vs-radio v-model="IdReporte" vs-name="Reporte" vs-value="1" class="radio-custom">
                                                        <span class="ml-2 text-gray-800">Detallado por Referencia</span>
                                                    </vs-radio>
                                                </label>
                                            </div>

                                            <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors">
                                                <label class="flex items-center cursor-pointer w-full">
                                                    <span class="text-gray-700 font-medium mr-3 w-6">2.</span>
                                                    <vs-radio v-model="IdReporte" vs-name="Reporte" vs-value="2" class="radio-custom">
                                                        <span class="ml-2 text-gray-800">Detallado Referencia Seleccionada</span>
                                                    </vs-radio>
                                                </label>
                                            </div>

                                            <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors">
                                                <label class="flex items-center cursor-pointer w-full">
                                                    <span class="text-gray-700 font-medium mr-3 w-6">3.</span>
                                                    <vs-radio v-model="IdReporte" vs-name="Reporte" vs-value="3" class="radio-custom">
                                                        <span class="ml-2 text-gray-800">Cuentas Integradas</span>
                                                    </vs-radio>
                                                </label>
                                            </div>

                                            <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors">
                                                <label class="flex items-center cursor-pointer w-full">
                                                    <span class="text-gray-700 font-medium mr-3 w-6">4.</span>
                                                    <vs-radio v-model="IdReporte" vs-name="Reporte" vs-value="4" class="radio-custom">
                                                        <span class="ml-2 text-gray-800">Detalle por Cuenta</span>
                                                    </vs-radio>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="flex flex-wrap">
                                        <div class="md:w-1/7 lg:w-1/7 xl:w-1/7">
                                            <div class="w-full" style="padding:10px 50px">
                                                <DxButton  v-show="ButtonPDF" type="danger" icon="pdffile" @click="GenerarReporte('application/pdf','PDF')" text="Generar Reporte" style="margin-right: 5px;"/>
                                                <DxButton  v-show="ButtonExcel" :width="180"  icon="tableproperties" text="Generar Reporte" type="success" @click="GenerarReporte('application/vnd.ms-excel','EXCEL')" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </vs-row>
                        </vs-row>
                    </vx-card>    
                </vs-col>

            </div>
        </div>

        <div class="w-full" align="center" v-if="DocSinCuentasV || DescPorDocumentoV">
            <div class="w-full" style="padding:10px 10px">
                <!-- <vs-button color="warning" class="label-size" type="filled" @click="OcultarInformacion()">  </vs-button> -->
                <DxButton :width="200" text="Ocultar Información" type="normal" styling-mode="outlined" @click="OcultarInformacion()" />
            </div>
        </div>

        <!--GRID DE AUXILIAR SIN CUENTA-->
        <div v-if="DocSinCuentasV">
            <DxDataGrid
                :data-source="DocSinCuentas"  
                :column-auto-width="true"
                :allow-column-reordering="true" 
                :show-borders="true"
            >
            <DxDataGridColumn data-field="Proveedor" caption="Proveedor" data-type="string"/>
            <DxDataGridColumn data-field="Referencia" caption="Referencia" data-type="string"/>
            <DxDataGridColumn data-field="Fecha" caption="Fecha" data-type="date"  :format="'dd/MM/yyyy'"  />
            <DxDataGridColumn data-field="Debe" caption="Debe" data-type="number"  :format="{ formatter: formatMoneda }" />
            <DxDataGridColumn data-field="Haber" caption="Haber" data-type="number"  :format="{ formatter: formatMoneda }" />
            <DxDataGridPaging :page-size="6" />   
            </DxDataGrid>
        </div>
        <!--GRID DE DESCUADRE POR DOCUMENTO-->
        <div v-if="DescPorDocumentoV">
            <DxDataGrid
                :data-source="DescPorDocumento" 
                :column-auto-width="true" 
                :allow-column-reordering="true"
                :show-borders="true"

            >
            <DxDataGridColumn data-field="Proveedor" caption="Proveedor" data-type="string"/>
            <DxDataGridColumn data-field="Referencia" caption="Referencia" data-type="string"/>
            <DxDataGridColumn data-field="Fecha" caption="Fecha" data-type="date"  :format="'dd/MM/yyyy'"  />
            <DxDataGridColumn data-field="SUMADEBE" caption="TotalDebe" data-type="number"  :format="{ formatter: formatMoneda }" />
            <DxDataGridColumn data-field="SUMAHABER" caption="TotalHaber" data-type="number"  :format="{ formatter: formatMoneda }" />
            <DxDataGridPaging :page-size="6" />        
            </DxDataGrid>
        </div>
        <div style="margin-top: 5px;">
        <DxDataGrid
           ref="myDataGridRef"  :data-source="AuxiliarCompras" :class="{'no-selection-style': IdReporte !== '2'}" :column-auto-width="true" :allow-column-reordering="true"  key-expr="Referencia"  :show-borders="true" @selection-changed="handleSelectionChanged"
        >
            <DxDataGridSelection mode="single" />
            <DxDataGridColumn data-field="Status" caption="Status" data-type="string"/>
            <DxDataGridColumn data-field="Fecha" caption="Fecha" data-type="date"  :format="'dd/MM/yyyy'"/>
            <DxDataGridColumn data-field="Proveedor" caption="Proveedor" data-type="string"/>
            <DxDataGridColumn data-field="Referencia" caption="Referencia" data-type="string"/>
            <DxDataGridColumn data-field="Cuenta" caption="Cuenta" data-type="string"/>
            <DxDataGridColumn data-field="NombreCuenta" caption="NombreCuenta" data-type="string"/>
            <DxDataGridColumn data-field="Debe" caption="Debe" data-type="number" :format="{ formatter: formatMoneda }" />
            <DxDataGridColumn data-field="Haber" caption="Haber" data-type="number" :format="{ formatter: formatMoneda }" />
            <DxDataGridHeaderFilter :visible="true" :allow-search="true" />
            <DxDataGridDxSearchPanel :visible="true" :highlight-case-sensitive="true" />
            <DxDataGridPaging :page-size="10" />        
        </DxDataGrid>
        </div>

    </div>

    <DxPopup :visible.sync="PartidaDiario" title="Generar Partida de Diario" height="460" width="500">
        <form>
            <vs-divider class="label-size"> OPCIONES </vs-divider>
            <vs-radio v-model="OpcionSel" vs-name="OpcionSel" vs-value="1"> Nueva Partida </vs-radio>
             <DxTextBox v-model="NuevaPartida" />
            <label> Fecha </label>
            <div>
                <flat-pickr
                    v-model="FechaPartida"
                    :config="configFromdateTimePicker"
                    class="devextreme-like-datepicker"
                />
            </div>
            <label>Referencia </label>
             <DxNumberBox :value="Referencia" :show-spin-buttons="true" v-model="Referencia" />
            <label for="">Descripción</label>
            <vs-textarea rows="3" class="w-full" v-model="Descripcion" />
            <div style="text-align: center;">
                <DxButton :width="120" text="Ok" type="success" @click=" ResumenQuery()" />
            </div>
        </form>
    </DxPopup>
</vx-card>
</template>

<script>

import Multiselect from 'vue-multiselect'
import "vue-multiselect/dist/vue-multiselect.min.css"
import flatPickr from 'vue-flatpickr-component';
import 'flatpickr/dist/flatpickr.css';
import {
    Spanish as SpanishLocale
} from 'flatpickr/dist/l10n/es.js';

import moment from "moment";
export default {
    components: {
        Multiselect,
        flatPickr
    },
    data() {
        return {

            cbPeriodos: '',
            ListaPeriodos: [],
            CodigoPeriodo: '',
            DescPeriodo: '',
            FechaPartida: null,
            FiltraF: '',
            Fecha: null,
            FechaV: true,
            FechaRegistro: null,
            Referencia: 0,
            Cuenta: '',
            CuentaV: true,
            Descripcion: '',
            OpcionSel: 1,
            NuevaPartida: null,
            Bloqueo1: false,
            Bloqueo2: true,
            Periodo: '',
            cbCuentas: {
                CodigoCuenta: null,
                Nombre: null
            },
            ListaCuentas: [],
            Filtro: false,
            FiltroD: false,
            CodCuenta: '',
            NombreCuenta: '',
            IdReporte: 0,
            AuxiliarCompras: [],
            ListadoReportes: [],
            CorrelativoPartida: '',
            FechaIPeriodoA: '',
            PartidaDiario: false,
            DocSinCuentas: [],
            DocSinCuentasV: false,
            DescPorDocumento: [],
            DescPorDocumentoV: false,
            Orden: '',
            ValidaMayorizacion: 'S',
            DescMayorizada: null,
            DescMayorizadaV: true,
            InsertaV: false,
            ModificarV: false,
            PendienteV: false,
            ListaSucursal: [],
            cbSucursal: '',
            ListaFace: [],
            ReferenciaSel: null,
            FiltroVF : true, 
            FiltroV: false,
            Pendiente : '',
            FechaActual: '',
            periodoSeleccionado: null,// Objeto completo seleccionad

            configFromdateTimePicker: {
                minDate: '',
                maxDate: '',
                locale: SpanishLocale,
                dateFormat: "d/m/Y",
            },
            ButtonExcel: false,
            ButtonPDF: false 

        }
    },
    mounted() {
        if (this.periodoSeleccionado && this.periodoSeleccionado.CodigoPeriodo) {
            this.ConsultaAuxiliarG();
            this.Mayorizar();
            this.limpiarFiltrosCompras();
        }
        this.IdReporte = '1'
        this.ConsultaPeriodo(),
        this.ConsultasCuentas(),
        this.ConsultaSucursal()
        this.ListadoReportes = this.$recupera
        this.DescPorDocumentoV = false
        this.DocSinCuentasV = false
    },
    watch: {
        'IdReporte'() {
            if (this.IdReporte == '1') {
                this.PendienteV = true
                this.FiltroV = true
                this.FechaV = true
                this.CuentaV = false
                this.ReferenciaSel = ''
               
                this.FiltroVF = true
                this.ButtonPDF = true
                this.ButtonExcel = true
                this.clearGridSelection()
                this.limpiarCuenta()
            }
            if (this.IdReporte == '2') {
                this.PendienteV = false
                this.FechaV = false
                this.CuentaV = false
                this.FiltroV = false 
                this.FiltroVF = false
                this.clearGridSelection()
                this.limpiarCuenta()
                 this.ButtonPDF = true
                this.ButtonExcel = false

            } else if (this.IdReporte == '3') {
                this.PendienteV = true
                this.FechaV = true
                this.CuentaV = true
                this.ReferenciaSel = ''
                this.FiltroV = true
                this.FiltroVF = true
                this.clearGridSelection()
                this.limpiarCuenta()
                this.ButtonPDF = true
                this.ButtonExcel = true

            } else if (this.IdReporte == '4') {
                this.PendienteV = true
                this.FiltroV = false
                this.FiltroVF = true
                this.FechaV = true
                this.CuentaV = true
                this.ReferenciaSel = ''
                this.clearGridSelection()
                this.limpiarCuenta()
                this.ButtonPDF = false
                this.ButtonExcel = true

            }
        },
        'OpcionSel'() {

            if (this.OpcionSel == '1') {
                this.Bloqueo1 = false,
                    this.Bloqueo2 = true

            } else {
                this.Bloqueo1 = true,
                    this.Bloqueo2 = false
            }
        },
        
        Filtro(nuevoValor) {
            if (nuevoValor) {
                // Si el checkbox está activado, se asigna la fecha actual
                this.Fecha = this.periodoSeleccionado.FechaInicial  // Formato 'YYYY-MM-DD'
                
            } else {
                // Si está desactivado, se borra la fecha
                this.Fecha = null;
            }
        },
        
        FiltroD(nuevoValor) {
            if (nuevoValor) {
                // Si el checkbox está activado, se asigna la fecha actual
                this.FechaRegistro = this.periodoSeleccionado.FechaInicial  // Formato 'YYYY-MM-DD'
                
            } else {
                // Si está desactivado, se borra la fecha
                this.FechaRegistro = null;
            }
        }
    },
    computed: {
        isSelectionAllowed() {
            return this.IdReporte === '2';
        },
        cuentasFormateadas() {
            return this.ListaCuentas.map(cuenta => ({
            ...cuenta,
            label: `${cuenta.CodigoCuenta} - ${cuenta.Nombre}`
            }));
        }
    },
    methods: {
        getDateValue(value) {
            if (value === undefined) {
                return '';
            } else if (value === null) {
                return '';
            } else if (value == '') {
                return '';
            }
            return moment(value).format('YYYY-MM-DD');
        },

        formatPeriodo(item) {
            if (!item) return '';
            return `${item.CodigoPeriodo} - ${item.DescPeriodo}`;
        },
        
        formatSucursal(item){
            if (!item) return '';
            return `${item.Hospital} - ${item.NombreHospital}`;
        },

        formatMoneda(value) {
            // Si el valor es 0 (o equivalente), retorna vacío
            if (value === 0 || value === 0.0000 || value === "0.0000") {
                return "";
            }
            
            // Si es null o undefined, retorna "Q 0.00" (como en tu lógica original)
            if (value === null || value === undefined) {
                return "Q 0.00";
            }

            // Formato normal para números distintos de cero
            const formatted = `Q ${Math.abs(value).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",")}`;
            return value < 0 ? `<span style="color: red;">-${formatted}</span>` : formatted;
        },

        handleSelectionChanged(e) {
            if (!this.isSelectionAllowed) {
            // Limpiamos selección si no está permitido seleccionar
            e.component.clearSelection();
            return;
            }

            // Si está permitido, ejecutamos el método original
            this.onSelectionChanged(e);
        },

        clearGridSelection() {
            // Accede a la instancia del DataGrid y limpia la selección
            this.$refs.myDataGridRef.instance.clearSelection();
            this.ReferenciaSel = null; // Limpia también tu variable local
        },

        onSelectionChanged({ selectedRowsData }) {
            this.ReferenciaSel = selectedRowsData[0]?.Referencia; 
        },

        SeleccionReferencia() {
            var ListaSeleccionada = [];
            var ReferenciaS = ''
            if (this.AuxiliarCompras.length > 0) {
                ListaSeleccionada = this.AuxiliarCompras.filter(ab => ab.Seleccion == true);
                for (let i = 0; i < ListaSeleccionada.length; i++) {
                    ReferenciaS = ListaSeleccionada[i].Referencia
                }
                this.ReferenciaSel = ReferenciaS;
            }

        },
        OcultarInformacion() {
            this.DocSinCuentasV = false
            this.DescPorDocumentoV = false
        },

        //PERIODOS
        ConsultaPeriodo() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/ConsultaPeriodo', {
                    Opcion: "C",
                    SubOpcion: "6",
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaPeriodos = resp.data.json;
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido cargar los periodos, favor validar.',
                        })
                    }
                    this.ConsultaPeriodoActual();
                })
                .catch(() => {})
        },

        ConsultaPeriodoActual() {
            const url = this.$store.state.global.url;
            const endpoint = url + 'app/v1_contabilidad_general/ConsultaPeriodoActual';
            
            
            this.axios.post(endpoint, {
                Opcion: "C",
                SubOpcion: "2",
            })
            .then(resp => {
                
                if (resp.data.codigo == 0) {
                    
                    this.DatosPeriodo = resp.data.json


                    const codigoPeriodoActual = resp.data.json[0].CodigoPeriodo;

                    
                    // Buscar el periodo en this.ListaPeriodos
                    const periodo = this.ListaPeriodos.find(p => p.CodigoPeriodo === codigoPeriodoActual);
                    
                    if (periodo) {
                        this.cbPeriodos = periodo.CodigoPeriodo;
                    }
                    
                } else {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Cuentas',
                        text: 'No se ha podido cargar los periodos, favor validar.',
                    });
                }
            })
            .catch(() => {
            });
        },

        onChangePeriodos(value) {
            if (value !== null && value.length !== 0) {
                this.CodigoPeriodo = value.CodigoPeriodo;
                this.DescPeriodo = value.DescPeriodo;

                // Establecer FechaMin y FechaMax
                this.FechaMin = new Date(value.FechaInicial);

                let fechaFinal = new Date(value.FechaFinal);
                fechaFinal.setDate(fechaFinal.getDate() + 1); // sumar 1 día para incluirla
                this.FechaMax = fechaFinal;

                // Configurar flatpickr
                this.configFromdateTimePicker.minDate = this.FechaMin;
                this.configFromdateTimePicker.maxDate = this.FechaMax;
                this.configFromdateTimePicker.enable = [
                    (date) => date >= this.FechaMin && date <= this.FechaMax
                ];

                // Asignar el último día del mes como FechaPartida
                let ultimoDiaDelMes = new Date(value.FechaFinal);
                ultimoDiaDelMes = new Date(ultimoDiaDelMes.getFullYear(), ultimoDiaDelMes.getMonth() + 1, 0);
                this.FechaPartida = ultimoDiaDelMes;

            } else {
                // Reset de los campos
                this.CodigoPeriodo = '';
                this.DescPeriodo = '';
                this.Fecha = new Date();
                this.FechaMin = '';
                this.FechaMax = '';
                this.configFromdateTimePicker.enable = [];
            }
        },


        guardarPeriodoCompleto(e) {
            let codigoSeleccionado = e.value;

            this.periodoSeleccionado = this.ListaPeriodos.find(
             (periodo) => periodo.CodigoPeriodo === codigoSeleccionado
            );

            this.DocSinCuentasV = false
            this.DescPorDocumentoV = false
            this.ConsultaAuxiliarG()
            this.Mayorizar()
            this.limpiarFiltrosCompras()
        },

        ConsultaSucursal() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/ConsultaCuentas', {
                    Opcion: 'C',
                    SubOpcion: '2'
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.ListaSucursal = [];
                    } else {
                        this.ListaSucursal = resp.data.json;

                        this.addTodas()
                    }
                })
        },

       addTodas() {
            this.ListaSucursal.unshift({
                Hospital: '',
                NombreHospital: 'Todas'
            });
            this.cbSucursal = {
                Hospital: '',
                NombreHospital: 'Todas'
            }
        },


        //Grid
        ConsultaAuxiliarG() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/ConsultaAuxiliarCM', {
                    Opcion: 'C',
                    SubOpcion: '1',
                    Periodo: this.periodoSeleccionado.CodigoPeriodo,
                    Sucursal: this.cbSucursal.Hospital

                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.AuxiliarCompras = resp.data.json
                    }
                })

        },
        //MAYORIZAR

        Mayorizar() {
            const url = this.$store.state.global.url;
            this.axios.post(url + 'app/v1_contabilidad_general/Mayorizacion_SM', {
                    Opcion: 'C',
                    SubOpcion: '1',
                    Periodo: this.periodoSeleccionado.CodigoPeriodo
                })
                .then(resp => {
                    this.DescMayorizada = null
                    if (resp.data.codigo === 0) {
                        this.DescMayorizada = resp.data.json[0].DescripcionM;
                    }
                })
                .catch(() => {
                });
        },
        //Cuentas
        ConsultasCuentas() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/ConsultasCuentas', {
                    Opcion: 'C',
                    SubOpcion: '1',
                    Activa: 'S'
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaCuentas = resp.data.json
                    } else {
                        this.ListaCuentas = []
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Auxiliar Compras',
                            text: 'No se ha podido cargar las cuentas, favor validar.',
                        })
                    }
                })

        },

        onChangeCuenta(value) {

            if (value !== null && Object.keys(value).length !== 0) {
            this.cbCuentas = {
                CodigoCuenta: value.CodigoCuenta,
                Nombre: value.Nombre
            };
            } else {
            this.cbCuentas = {
                CodigoCuenta: null,
                Nombre: null
            };
            }
        },

        //ReportesFiltros
        //Reporte 1 
        async GenerarReporte(formatoInterno, formato) {
            let nombreReporte = '';
            let OpcionInterna = this.IdReporte

            if (this.IdReporte == 1) {
                nombreReporte = 'ReporteDetalladoPorRefCM'
                this.ReferenciaSel = ''
            } else if (this.IdReporte == 2) {
                const necesitaReferencia = this.IdReporte == 2 && !this.Todos;
                if (necesitaReferencia && (!this.ReferenciaSel || Object.keys(this.ReferenciaSel).length === 0)) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Error al generar el reporte',
                        text: 'Seleccione una referencia antes de generar el Reporte.',
                    });
                    return;
                }
                nombreReporte = 'ReporteDetalladoPorRefSelCM'
                OpcionInterna = 1
            } else if (this.IdReporte == 3) {
                nombreReporte = 'ReporteCuentasIntegradasCM'
                OpcionInterna = 2
            } else if (this.IdReporte == 4) {
                nombreReporte = 'ReporteDetallePorCuentaCM'
                OpcionInterna = 3
            }

            let postData = {
                nombrereporte: nombreReporte,
                tiporeporte: formatoInterno,
                Opcion: 'C',
                SubOpcion: OpcionInterna,
                Periodo: this.periodoSeleccionado.CodigoPeriodo,
                Cuenta: this.cbCuentas.CodigoCuenta,
                Sucursal: this.cbSucursal.Hospital,
                ReferenciaCM: this.ReferenciaSel,
                FiltroF: this.FiltraF == true ? 1 : 0,
                Fecha: this.Fecha,
                Pendiente: this.Pendiente ? 'P' : null, // Aquí está el cambio
            }

            this.$reporte_modal({
                Nombre: 'ReportesAuxiliarCompras',
                Opciones: {
                    ...postData
                },
                Formato: formato
            })
        },


        GenerarPartida() {
            if (this.Fecha >= this.FechaIPeriodoA) {
                this.AuxiliarSinCuenta()
                this.Correlativo()
            } else {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#e74c3c',
                    title: 'Error al Generar Partida de Diario',
                    acceptText: 'Aceptar',
                    text: 'La Fecha seleccionada es menor a la fecha del periodo actual.',
                    buttonCancel: 'border',
                    accept: () => {},
                })
                return
            }
        
            const info = this.periodoSeleccionado

            if (info && info.CodigoPeriodo) {
                // Llamar a onChangePeriodos solo si la información es válida
                this.onChangePeriodos(info)
            } else {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#e74c3c',
                    title: 'Valores incorrectos',
                    acceptText: 'Aceptar',
                    text: 'No hay información válida en periodoSeleccionado',
                    buttonCancel: 'border',
                    accept: () => {},
                })
                return
            }
        },


        AuxiliarSinCuenta() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/ConsultaAuxiliarCM', {
                    Opcion: 'C',
                    SubOpcion: '3',
                    Periodo: this.periodoSeleccionado.CodigoPeriodo

                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.DocSinCuentas = resp.data.json

                        if (this.DocSinCuentas.length > 0) {
                            this.DocSinCuentasV = true
                            this.$vs.dialog({
                                    type: 'alert',
                                    color: '#e74c3c',
                                    title: 'Error al Generar Partida de Diario',
                                    acceptText: 'Aceptar',
                                    text: 'Favor revisar los documentos sin número de cuenta.',
                                    buttonCancel: 'border',
                                    accept: () => {},
                                })
                            return;
                        } else {

                            this.DocSinCuentasV = false
                            this.DescuadrePorDoc()
                        }
                    }
                })
        },
        

        DescuadrePorDoc() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/ConsultaAuxiliarCM', {
                    Opcion: 'C',
                    SubOpcion: '4',
                    Periodo: this.periodoSeleccionado.CodigoPeriodo

                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.DescPorDocumento = resp.data.json

                        if (this.DescPorDocumento.length > 0) {
                            this.DescPorDocumentoV = true

                             this.$vs.dialog({
                                    type: 'alert',
                                    color: '#e74c3c',
                                    title: 'Error al Generar Partida de Diario',
                                    acceptText: 'Aceptar',
                                    text: 'Partida total no cuadra por más de un centavo, a continuacion se detalla el total de los no cuadrados.',
                                    buttonCancel: 'border',
                                    accept: () => {},
                                })
                            return;
                        } else {
                            this.OpcionSel = 1
                            this.PartidaDiario = true
                        }
                    }
                })

        },

        
        Correlativo() {
            const url = this.$store.state.global.url;
            this.axios.post(url + 'app/v1_contabilidad_general/CorrelativoCorrecto', {
                Opcion: "C",
                SubOpcion: "8",
                Periodo: this.periodoSeleccionado.CodigoPeriodo,
            }).then((resp) => {
                if (resp.data && resp.data.json && resp.data.json.length > 0) {
                    const corr = resp.data.json[0].CorrelativoPartidas;
                    this.NuevaPartida = corr === '' ? '1' : corr; // ahora es string
                }
            }).catch(() => {
            });
        },

       /*  Inserta Una nueva Partida  */
        ResumenQuery() {
            if (this.Partida == 0 || this.Partida == '') {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Generar Partida de Diario',
                    text: 'Ingrese Número de partida.',
                })
                return;
            } else {
                const url = this.$store.state.global.url
                  // Convertir fecha de 'dd/MM/yyyy' a 'yyyy/MM/dd' (Forma segura)
                let fechaFormateada = null;
                if (this.FechaPartida) {
                    const [day, month, year] = this.FechaPartida.split('/');
                    const fechaObj = new Date(`${year}-${month}-${day}`);
                    
                    // Verificar si la fecha es válida
                    if (!isNaN(fechaObj.getTime())) {
                        fechaFormateada = fechaObj.toISOString().split('T')[0].replace(/-/g, '/');
                    }
                }
                this.axios.post(url + 'app/v1_contabilidad_general/TranAuxiliaresCM', {
                        Opcion: "I",
                        SubOpcion: "2",
                        Periodo: this.periodoSeleccionado.CodigoPeriodo,
                        CuentaBanco: this.cbCuentasB,
                        Partida: this.NuevaPartida,
                        Fecha: fechaFormateada,
                        Referencia: this.Referencia,
                        Descripcion: this.Descripcion
                    })
                    .then(resp => {
                        if (resp.data.codigo == 0) {
                            this.PartidaDiario = false   
                            this.Descripcion = null
                            this.Referencia = 0
                        }
                    })
                    .catch(()=>{
                          this.PartidaDiario = false   
                          this.Descripcion = null
                          this.Referencia = 0
                    })
            }

        },

        limpiarFiltrosCompras() {
            const gridInstance = this.$refs.myDataGridRef.instance;
            if (gridInstance) {
            gridInstance.clearFilter();                      // Limpia filtros por columna
            gridInstance.option('searchPanel.text', '');     // Limpia el texto del buscador
            gridInstance.option('paging.pageIndex', 0);      // Vuelve a la primera página
            }
        },

        limpiarCuenta() {
                this.cbCuentas = {
                    CodigoCuenta: null,
                    Nombre: null
                };
                this.onChangeCuenta(this.cbCuentas);
        }
    }
}
</script>   

<style scoped>
.footer {
    height: 70px;
    margin-top: -65px;
    margin-left: 10px;
}

.label-size {
    font-size: 14px;
    font-weight: bold;
}

.label-StatusM {
    font-size: 14px;
    font-weight: bold;
    color: darkred;
}

.label-i {
    font-size: 18px;
    font-weight: bold;
    color: rgb(240, 158, 6);
}

.cuadro-i {
    background: #faf2f1;
}

.flex-container {
    display: flex;
    flex-direction: row;
}

.padre {
    position: relative;
}

.hijo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

</style>

<style>


.Compras .radio-custom:hover {
  --vs-radio-border-color: #9ca3af;
}

.Compras .vs-radio--text {
  font-size: 0.9375rem;
}

.Compras .radio-custom {
  --vs-radio-border-color: #d1d5db;
  --vs-radio-background-color-checked: #3b82f6; /* Azul más moderno */
  --vs-radio-border-color-checked: #3b82f6;
  --vs-radio-size: 1.1rem;
}

.Compras .radio-custom:hover {
  --vs-radio-border-color: #93c5fd;
}

.Compras .vs-radio--text {
  font-size: 0.9375rem;
  color: #374151; /* Color de texto más oscuro */
}

/* Efecto de transición suave */
.Compras .vs-radio-con {
  transition: all 0.2s ease;
}


/* Cuando no se permite selección, quitar color de fila seleccionada */
.Compras .no-selection-style .dx-selection.dx-row > td {
  background-color: transparent !important;
}

.Compras .devextreme-like-datepicker {
  border: 1px solid #d3d3d3;
  border-radius: 4px;
  padding: 6px 10px;
  font-size: 14px;
  height: 36px;
  width: 100%;
  max-width: 100%;
  font-family: "Segoe UI", Roboto, sans-serif;
  box-sizing: border-box;
  transition: border 0.2s ease-in-out;
}


.Compras .dx-datagrid .dx-selection.dx-row > td {
  background-color: #f7dc6f !important;
   color: black !important;
}

.Compras .dx-header-filter:not(.dx-header-filter-empty) {
    color: yellow;
}

.Compras .dx-datagrid-headers .dx-datagrid-table .dx-header-row {
    background-color: #2980b9; /* Verde */
    color: white;
}


</style>
