<template>
    <vx-card type = "2" title="Eliminación de Resultados" class="justify-center">
        <buscador ref="buscarTipoOrden" buscador_titulo="Selecione un tipo de Orden" :api="'app/laboratorio/TipoOrden'" 
            :campos="['Codigo','Nombre']" :titulos="['Código','Nombre']" 
            :multiselect="false" :api_validar_campo="true" :api_preload="false" 
        />
        <vs-row>
            <div class="">
                <label class="vs-input--label">Tipo de Orden</label>
            
                <vx-input-group class="">
                    <vs-input class = "w-full md:w-32 sm:w-32 xs:w-32" placeholder="Tipo Orden" v-model="tipo" editable @change="CargarTipoOrden()"/>
                    <template slot="append">
                        <div class="append-text btn-addon">
                            <vs-button v-if="!empresa" id="button-with-loading" color="primary" icon-pack="feather" @click="BuscarTipoOrden()" icon="icon-search" class=""></vs-button>
                            <vs-button v-else id="button-with-loading" color="danger" icon-pack="feather" @click="LimpiarDatos()" icon="icon-x" class=""></vs-button>
                        </div>
                    </template>
                </vx-input-group>
            </div>
            <vs-input class="w-full md:w-64 sm:w-64 xs:w-64" label = " " placeholder="Tipo de Orden" v-model="nombreTipo" disabled />
            <vs-input class="w-full md:w-32 sm:w-32 xs:w-32" label = "No. Orden"  type="number" @blur="CargarOrden()" @keyup.enter="CargarOrden()" v-model="codigo"/>
        </vs-row>
        <p>{{datosAdm}}</p>
        <p>{{datosPaciente}}</p>
        <vs-divider></vs-divider>
        
        <ValidationObserver v-if="admision" ref="formValidate" v-slot="{ invalid,handleSubmit }" mode="lazy">
            <form method="post" @submit.prevent="handleSubmit(Eliminar())">
                
                <ValidationProvider rules="min:10|max:40|required" v-slot="{ errors }" class="required">
                    <vs-input class="w-full" label="Razón"  type="text" v-model="razon" placeholder="Ingrese una razón por la cual se eliminan los resultados"
                        :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" required />
                </ValidationProvider>
                <div style="text-align:center;margin:5px 0;padding:5px ">
                    <vs-button color="danger" icon-pack="feather" icon="icon-trash-2" style="display:inline-block;margin-right:2px" @click.native="EliminarResultado()" :disabled="invalid">Eliminar</vs-button>
                </div>
            </form>
        </ValidationObserver>
    </vx-card>
</template>
<script>
    export default{
        data(){
            return{
                empresa:null,
                tipo:null,
                nombreTipo:null,
                codigo:null,
                razon:null,
                serieAdmision:null,
                admision:null,
                datosPaciente:null,
                datosAdm:null
            }
        },
        methods:{
            LimpiarDatos(origin = null){
                this.empresa=null
                if(origin=='codigo'){
                    this.tipo=null
                    this.nombreTipo=null
                }
                this.codigo=null
                this.razon=null
                this.serieAdmision=null
                this.admision=null
                this.datosPaciente=null
                this.datosAdm=null
            },
            
            EliminarResultado(){
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',
                    acceptText: 'SI',
                    cancelText: 'NO',
                    text: '¿Desea eliminar los resultados del exámen? Esta acción no se puede deshacer',
                    accept: () => {
                        this.axios.post('/app/laboratorio/EliminarResultado', {
                            empresa: "MED",
                            tipoOrden: this.tipo,
                            orden: this.codigo,
                            razon: this.razon,
                            serieAdmision: this.serieAdmision,
                            admision: this.admision
                        })
                        .then( () => {
                            this.LimpiarDatos()
                        })
                        },
                        cancel: () => {}
                })
            },
            CargarOrden(origin){
                if(this.tipo && this.codigo)
                    this.axios.post('/app/laboratorio/ObtenerOrdenResultado', {
                            empresa: 'MED',
                            tipoOrden: this.tipo,
                            orden: this.codigo
                        })
                        .then(resp => {
                            if(resp.data.codigo === 0 && resp.data.json.length > 0){
                                this.datosAdm = "Admisión: "+ resp.data.json[0].SerieAdmision.trim() + " - " + resp.data.json[0].Admision 
                                this.datosPaciente = "Paciente: " + resp.data.json[0].Nombre.trim() +" "+resp.data.json[0].Apellido.trim() + (resp.data.json[0].ApellidoCasada && resp.data.json[0].ApellidoCasada.trim().length != '' ? " de "+ resp.data.json[0].ApellidoCasada: "" )
                                this.admision = resp.data.json[0].Admision
                                this.serieAdmision = resp.data.json[0].SerieAdmision.trim()
                            }
                            else {
                                this.$vs.notify({
                                    time: 8000,
                                    title: 'Eliminar Resultados dice . . .',
                                    text: 'No se encontraron resultados de exámenes con el código especificado',
                                    iconPack: 'feather',
                                    icon: 'icon-alert-circle',
                                    color: 'warning'
                                })
                                this.LimpiarDatos(origin)
                            }
                        })
                        .catch( () => {
                            this.LimpiarDatos()
                        })
                else
                    this.LimpiarDatos(origin)
            },
            BuscarTipoOrden(){
                this.$refs.buscarTipoOrden.iniciar((data) => {
                    this.SetDatosTipoOrden(data)
                })
            },
            SetDatosTipoOrden(data){
                if (data != null) {
                        this.tipo=data.Codigo.trim()
                        this.nombreTipo=data.Nombre.trim()
                        if(this.codigo)
                          this.CargarOrden();
                    }
            },
            CargarTipoOrden(){
                if(this.tipo)
                this.tipo = this.tipo.toUpperCase()
                this.axios.post('/app/laboratorio/TipoOrden', {
                            Codigo: this.tipo,
                            Nombre: this.codigo
                        })
                        .then(resp => {
                            if(resp.data.codigo === 0 && resp.data.json.length > 0){
                                this.SetDatosTipoOrden(resp.data.json[0])
                            }
                            else {
                                this.$vs.notify({
                                    time: 8000,
                                    title: 'Eliminar Resultados dice . . .',
                                    text: 'No se encontró el tipo de orden especificado',
                                    iconPack: 'feather',
                                    icon: 'icon-alert-circle',
                                    color: 'warning'
                                })
                                this.LimpiarDatos(origin)
                            }
                        })
                        .catch( () => {
                            this.LimpiarDatos()
                        })
            }
        }
    }
</script>