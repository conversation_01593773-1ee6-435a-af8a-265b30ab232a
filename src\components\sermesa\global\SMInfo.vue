<template>
<div>
    <div class="info" @click="mostrar=true" v-if="doc">
        <i class="fas fa-info-circle"></i>
        <i class="fas fa-exclamation-circle alerta" v-if="alerta"></i>
    </div>
    <div class="info-contenido" v-if="mostrar">
        <div class="titulo">
            {{titulo}}
            <i class="far fa-times-circle" style="cursor:pointer" @click="mostrar=false"></i>
        </div>

        <vs-tabs style="height:calc(100%- 150px)" :color ="tabColor">
            <vs-tab label="Información" v-if="mostrarDoc">
                <div class="contenido">
                    <div v-for="(item,index) in doc" :key="index" class="item" :class="[validar(item)==2?'nota':'',validar(item)==3?'desa':'']">
                        <i class="icono far fa-lightbulb"></i>
                        <div v-html="item" v-if="validar(item)!=0"></div>
                    </div>
                </div>
            </vs-tab>
            <vs-tab label="Bitacora" v-if="mostrarBitacora"  @click="consultar_bitacora()" style="height:calc(100%- 150px)">
                <div class="contenido">
                    <div v-for="(item,key) in  bitacora" :key="key" style="border-top:1px solid #ccc;padding:3px">
                        <div v-html="Otros().llave(item.Detalle)"></div>
                        <div v-html="Otros().detalle(item.Detalle)"></div>
                        <small><b>Usuario:</b> {{item.CorporativoCreado}}</small><br>
                        <small><b>Fecha:</b> {{item.FechaCreado}}</small>
                    </div>
                </div>
            </vs-tab>
            <div v-for="(items,index) in Datos" v-bind:key="index">
                <vs-tab v-bind:label="items.label">
                    <div v-if="items.forma == 'i'" class="contenido">
                        <div v-for="(item,index2) in items.contenido" :key="index2" class="item desa">
                            <div><i class="icono fas fa-check">{{ index2 }}</i></div>
                            <div class="alerta" v-html="item"></div>
                        </div>
                    </div>
                    <p v-else-if="items.forma == 'p'" v-html="items.contenido" />
                    <div v-else></div>
                </vs-tab>
            </div>

        </vs-tabs>

    </div>
</div>
</template>

<script>
export default {
    data() {
        return {
            mostrar: false,
            alerta: false,
            bitacora: []
        }
    },
    props: {
        /**
         * Título para la sección emergente por defecto 'Información'
         */
        titulo: {
            default: 'Información'
        },
        /**
         * Color para las pestañas 
         */
        tabColor: {
            default: '#008FBE'
        },
        Datos: Array,
        /**
         * Indica si se muestra la sección de la documentación del componete o pantalla
         */
        mostrarDoc: {
            default: true
        },
        /**
         * Indica si se muestra la sección de la documentación del componete o pantalla
         */
        mostrarBitacora: {
            default: true
        }
    },

    computed: {
        funcionalidad() {
            return this.$store.state.id_funcionalidad
        },
        doc() {
            const a = this.$store.state.funcionalidad_doc
            if (a)
                a.map(m => {
                    if (this.validar(m) == 2) this.alerta = true
                })
            return a
        },
        global() {
            return this.$store.state.global
        }
    },
    watch: {
        funcionalidad() {
            this.mostrar = false
            this.alerta = false
        }
    },
    methods: {
        validar(item) {
            const resp = (item.substr(0, 1) != '*') ? 1 : (item.substr(0, 2) == '**' && (this.global.instancia == 'LOCAL' || this.global.instancia == 'DESA')) ? 2 : (this.global.instancia == 'LOCAL' || this.global.instancia == 'DESA') ? 3 : 0
            return resp
        },
        consultar_bitacora() {
            this.bitacora = []
            return this.axios.post('/app/bitacora/consultar', {
                    id_funcionalidad: this.funcionalidad
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.bitacora = resp.data.json
                    }
                })
                .catch(() => {})
        },
        Otros() {
            return {
                llave: (item) => {
                    const arr = JSON.parse(item)
                    let resp = ''
                    Object.entries(arr).forEach(([key, value]) => {
                        if (key != 'info') resp += `<i class="fas fa-search-location"></i> <small>${key} | ${value}</small>`

                    })
                    return resp
                },
                detalle: (item) => {
                    const arr = JSON.parse(item)
                    let resp = ''
                    if (arr.info) {
                        Object.entries(arr.info).forEach(([key, value]) => {
                            if (resp != '') resp += '<br>'
                            resp += `<small><b>${key}</b></small><br>${value}`
                        })
                        return resp
                    }
                    return null
                }
            }
        }
    }
}
</script>

<style scoped>
.info {
    position: fixed;
    right: 0px;
    top: 90px;
    background: rgba(var(--vs-primary), 1);
    border: 2px solid rgba(0, 0, 0, 0.2);
    padding: 6px 3px 3px 6px;
    border-radius: 10px 0 0 10px;
    cursor: pointer;
    opacity: 0.8;
}

.info i {
    font-size: 21px;
    color: white;
}

.info-contenido {
    border-radius: 10px 0 0 10px;
    position: fixed;
    top: 65px;
    right: 0;
    width: 350px;
    height: calc(100% - 65px);
    background-color: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(7px);
    box-shadow: -10px 0 5px rgba(0, 0, 0, 0.2);
    z-index: 999;
    overflow: auto;
}

.info-contenido .titulo {
    font-size: 20px;
    padding: 10px 5px;
    background-color: rgba(0, 0, 0, 0.1);
    margin-bottom: 5px;
    border-radius: 10px 0 0 0;
}

.info-contenido .titulo i {
    float: right;
    margin-top: 5px;
}

.info-contenido .contenido {
    padding: 8px;
    max-height: calc(100% - 180px);
    overflow: auto;
    display: flex;
    flex-wrap: nowrap;
    flex-direction: column;
}

.info-contenido .contenido .item {
    margin-bottom: 7px;
    flex-grow: 4;
    display: flex;
    justify-content: flex-start;
}

.info-contenido .contenido .item>.icono {
    flex: 0 1 auto;
    width: 15px;
    font-size: 13px;
    margin-top: 3px;
}

.info-contenido .contenido .item>div {
    flex: 1 1;
    font-size: 14px;
    /* white-space: nowrap; */
}

.info-contenido .contenido .item.desa {
    color: #2980B9
}

.info-contenido .contenido .item.nota {
    color: #E74C3C
}

.info .alerta {
    color: red;
    font-size: 15px;
    position: absolute;
    left: -5px;
    bottom: -5px;
    background-color: white;
    border-radius: 50%;
}
</style>
