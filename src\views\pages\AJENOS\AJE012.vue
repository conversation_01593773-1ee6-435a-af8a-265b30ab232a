<template>
<div>
    <vs-popup ref="buscador" :title="`Información Cargos`" :active.sync="infoCargo.mostrar" style="z-index:52000" id="div-with-loading" class="vs-con-loading__container">
        <vs-table2 exportarExcel="Excluidos por Regla" v-if="infoCargo.info" search tooltip :data="infoCargo.info" height="500px">

            <template slot="header">
                <div class="pa-5" style="border:1px solid #ccc;padding:7px; border-radius:5px">
                    <i class="fas fa-money-bill-wave"></i> Sin regla o Restringida por regla /
                    <i class="fas fa-lock"></i> Bloqueados
                </div>
            </template>

            <template slot="thead">
                <th width="50px">Selección</th>
                <th width="100px">Motivo</th>
                <th order="NombreSucursal" width="120px">Hospital</th>
                <th order="TipoRegistro" width="150px">Tipo Doc.</th>
                <th order="NombreCategoria">Tipo Honorario</th>
                <th order="Admisión" width="120px">Admisión</th>
                <th order="Documento" width="120px">Orden</th>
                <th order="Admision_FechaEgreso" width="150px">Fecha Egreso</th>
                <th order="NombrePaciente">Paciente</th>
                <th order="NombreSeguro">Tipo Paciente</th>
                <th order="Valor" orderType="number">Monto</th>
            </template>

            <template slot-scope="{data}">
                <tr :key="indextr" v-for="(tr, indextr) in data">

                    <vs-td2>
                        <vs-checkbox v-model="tr.seleccion" danger @change="Otros().seleccionCargo(tr)" />
                    </vs-td2>

                    <vs-td2>
                        <i v-if="tr.BloqueoHonorarios == ''" class="fas fa-money-bill-wave" @click="Consulta().Consulta_Regla_Info(tr)"></i>
                        <i v-else class="fas fa-lock"></i>
                        <span v-if="tr.BloqueoHonorarios == ''"> {{Otros().reglaInfo(tr)}} </span>
                    </vs-td2>

                    <vs-td2>
                        {{ tr.NombreSucursal.toUpperCase() }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.TipoRegistro }}
                    </vs-td2>

                    <vs-td2>
                        <span v-if="tr.Categoria">({{ tr.Categoria }})</span> {{ tr.NombreCategoria.toUpperCase() }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.SerieAdmision }}{{ tr.Admision }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.Documento }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.Admision_FechaEgreso }}
                    </vs-td2>

                    <vs-td2>
                        {{tr.Cita_Cliente ? tr.NombreCliente : tr.NombrePaciente }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.NombreSeguro }}
                    </vs-td2>

                    <vs-td2>
                        <div style="text-align:right">
                            {{$formato_moneda(tr.Valor)}}
                        </div>
                    </vs-td2>

                </tr>
            </template>
        </vs-table2>
        <vs-divider></vs-divider>
        <div class="flex">
            <vs-spacer></vs-spacer>
            <vs-button @click="infoCargo.mostrar=false">Aceptar</vs-button>

        </div>
    </vs-popup>

    <vx-card title="Pago de Honorarios Excluidos por Regla">
        <ValidationObserver ref="formValidate" v-slot="{ invalid,handleSubmit }" mode="lazy">
            <div class="flex mb-5 " style="justify-content:space-between;border:1px solid rgba(0,0,0,0.1);padding:5px">
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <SM-Buscar v-model="info.Codigo" label="Código del Médico" api="app/Ajenos/Busqueda_Ajenos" :api_campos="['Codigo','Nombre','Apellido','Corporativo','Tipo']" :api_titulos="['Codigo','Nombre','Apellido','Corporativo','Tipo']" api_campo_respuesta="Codigo" :callback_buscar="Consulta().Consulta_Ajenos" :callback_cancelar="Otros().Limpiar_Datos" :disabled_texto="true" />
                </div>
            </div>
            <div class="flex mb-5 ">
                <div class="w-full md:w-2/3 lg:w-2/3 xl:w-2/3 p-1">
                    <vs-input label="Nombre Médico" class="w-full" v-model="info.Medico" :disabled="true" />
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <vs-input label="NIT" class="w-full" v-model="info.NIT" :disabled="true" />
                </div>
            </div>
            <div class="flex mb-5 ">
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <SM-Buscar label="Tipo de Médico" v-model="info.Tipo" :api="[{Codigo:'C',Nombre:'Cortesía'},{Codigo:'M',Nombre:'Casa'},{Codigo:'CC',Nombre:'Casa-Cortesía'},{Codigo:'R',Nombre:'Referencia'}]" :api_campos="['Nombre']" :api_titulos="['Nombre']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="false" />
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <SM-Buscar v-model="info.Especialidad" label="Especialidad" api="app/Ajenos/Busqueda_Especialidad" :api_cache="true" :api_campos="['Codigo','Descripcion']" :api_titulos="['Codigo','Descripción']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Descripcion" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="false " />
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <SM-Buscar v-model="info.SubEspecialidad" label="SubEspecialidad" api="app/Ajenos/Busqueda_SubEspecialidad" :api_campos="['Codigo','Nombre']" :api_titulos="['Codigo','Nombre']" :api_filtro="{Codigo_Especialidad:info.Especialidad}" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="false" />
                </div>
            </div>
            <vs-divider></vs-divider>
            <div class="flex flex-wrap mb-5">
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <ValidationProvider name="TelefonoClinica" v-slot="{ errors }">
                        <vs-input label="Fecha de Última Facturación" type="datetime" style="padding:0" disabled="false" class="w-full" v-model="info.FacturaFecha" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                    </ValidationProvider>
                </div>
            </div>
            <vs-divider></vs-divider>
            <vs-table2 max-items="10" search tooltip pagination :data="pagosVisibles">
                <template slot="header">
                    <vs-button color="primary" type="filled" @click.native="Consulta().Consulta_Datos()" icon-pack="feather" icon="icon-refresh-cw" :disabled="info.Codigo==null">
                        Actualizar
                    </vs-button>
                </template>
                <template slot="thead">
                    <th width="50px">Selección</th>
                    <th order="NombreSucursal" width="150px">Hospital</th>
                    <th order="TipoRegistro" width="300px">Tipo Doc.</th>
                    <th order="NombreCategoria">Tipo Honorario</th>
                    <!-- <th order="Documento" width="150px">Documento</th>
                    <th order="Admision_FechaEgreso" width="150px">Fecha Egreso</th>
                    <th order="NombrePaciente">Paciente</th>
                    <th order="NombreSeguro">Tipo Paciente</th> -->
                    <th order="Valor" orderType="number">Monto</th>
                    <th width="50px" orderType="number">Acción</th>
                </template>

                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">

                        <vs-td2>
                            <vs-checkbox v-model="tr.seleccion" indeterminate @change="Otros().seleccionCargo(tr)" />
                        </vs-td2>

                        <vs-td2>
                            {{ tr.NombreSucursal.toUpperCase() }}
                        </vs-td2>

                        <vs-td2>
                            {{ tr.TipoRegistro }}
                        </vs-td2>

                        <vs-td2>
                            <span v-if="tr.Categoria">({{ tr.Categoria }})</span> {{ tr.NombreCategoria.toUpperCase() }}
                        </vs-td2>

                        <!-- <vs-td2 v-if="tr.Tipo!=1">
                            {{ tr.Documento }}
                        </vs-td2>

                        <vs-td2 v-if="tr.Tipo!=1">
                            {{ tr.Admision_FechaEgreso }}
                        </vs-td2>

                        <vs-td2 v-if="tr.Tipo!=1">
                            {{tr.Cita_Cliente ? tr.NombreCliente : tr.NombrePaciente }}
                        </vs-td2>

                        <vs-td2 v-if="tr.Tipo!=1">
                            {{ tr.NombreSeguro }}
                        </vs-td2> -->

                        <vs-td2>
                            <div style="text-align:right">
                                {{ $formato_moneda(tr.Valor) }}
                                <div v-if="tr.Accion && Otros().verificarSeleccionParcial(tr.Accion)" style="color:red;font-size:12px">Parcial {{Otros().verificarSeleccionParcial(tr.Accion)}}</div>
                            </div>
                        </vs-td2>

                        <vs-td2 noTooltip>
                            <vs-button v-if="tr.Accion" color="success" size="small" icon-pack="fas" icon="fa-search-plus" class="mr-1" style="display:inline-block" v-on:click="infoCargo.mostrar=true;infoCargo.info = pagosCargos[tr.Accion]"></vs-button>
                        </vs-td2>
                    </tr>
                </template>
            </vs-table2>
            <table width="100%" style="border-spacing: 0;">
                <tr style="background-color:#bdc3c7; color:#34495e">
                    <td style="padding:10px">
                        Total a Facturar
                    </td>
                    <td width="250px" style="padding:10px; text-align:right;font-weight:bold">
                        {{calcular}}
                    </td>
                </tr>
            </table>
            <vs-divider></vs-divider>

            <div class="flex bottom">
                <vs-spacer></vs-spacer>
                <vs-button color="success" type="filled" @click.native="handleSubmit(()=>Guardar().Habilitar())" :disabled="invalid || pagos.filter(f=>f.seleccion).length==0">Habilitar Pago</vs-button>
            </div>
        </ValidationObserver>
    </vx-card>
    <div class="facturas" v-if="info.MostrarFactura">
        <Facturas v-model="info" :cancelar="Otros().facturasCancelar" :tabla="pagos.filter(f=>f.seleccion)" />
    </div>
</div>
</template>

<script>
export default {
    data() {
        return {

            /**
             * Documentación del módulo
             */
            doc_: [
                'Genera el listado de todos los cargos e interpretaciones que no se encuentren validados por reglas o bloquedos (Bloqueo de cargos).',
                'Los honorarios mostrados estan en estado <b>No Pagado</b> y no se han habilitado para pago.',
                'Al habilitar el pago se envían los honorarios a las planillas de cuenta ajena y planilla de honorarios respectivamente.',
                '* Se obtienen los datos del sp <i>sp_Ajenos_Honorarios_PagosHospital</i>.',
                '** Validar bitacora'
            ],

            /**
             * Editar el monto de la tabla
             */
            info: {
                Codigo: null,
                Medico: null,
                NIT: null,
                Tipo: null,
                Especialidad: null,
                SubEspecialidad: null,
                FacturaFecha: null,
                FacturaSerie: null,
                FacturaNumero: null,
                Total: '0.00',
                MostrarFactura: false,
            },

            /**
             * Listado de permisos
             */
            permisos: {
                editar: false
            },

            /**
             * Información de los pagos
             */
            pagos: [],
            pagosVisibles: [],
            pagosCargos: {
                30: null,
                97: null,
                98: null,
                99: null,
            },
            pagosSeleccion: [],

            /**
             * Información de los cargos
             */
            infoCargo: {
                mostrar: false,
                info: null
            }

        }
    },
    computed: {
        calcular() {
            const arr = this.pagos.filter(f => f.seleccion)
            return this.$formato_moneda(arr.reduce((acumulador, valor) => acumulador + parseFloat(valor.Valor ? valor.Valor : 0), 0))
        }
    },
    components: {
        Facturas: () => import('./AJE005_Factura.vue')
    },
    methods: {
        //=======================================================================================================
        // CONSULTA
        //=======================================================================================================
        Consulta: function () {
            return {
                Consulta_Ajenos: (item) => {
                    this.info.Medico = `${item.Nombre.trim()} ${item.Apellido.trim()}`
                    this.Consulta().Consulta_InfoAjenos(this.info.Codigo)
                        .then(() => {
                            this.Consulta().Consulta_Datos()
                        })
                },

                Consulta_Datos: () => {
                    this.Consulta().Consulta_Pagos(this.info.Codigo)
                    this.Consulta().Consulta_UltimaActualizacion(this.info.Codigo)
                },

                Consulta_InfoAjenos: (codigo) => {
                    return this.axios.post('/app/ajenos/Consulta_Ajenos', {
                            Codigo: codigo
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                let datos = resp.data.json[0]

                                // Remplazando el tipo  si se ha seleccionado tipocasacortesia
                                if (datos['TipoCortesiaCasa'] == true) datos['Tipo'] = 'CC'
                                this.info.Tipo = datos.Tipo
                                this.info.Especialidad = datos.EspecialidadPrincipal.trim()
                                this.info.SubEspecialidad = datos.Especialidad.trim()
                                return datos.Proveedor.trim()
                            }
                        })
                },

                Consulta_Pagos: (Ajeno) => {

                    this.pagos = []
                    this.pagosVisibles = []
                    return this.axios.post('/app/ajenos/Honorarios_Pagos_Consulta', {
                            Ajeno,
                            ValidacionReglas: 2,
                            TipoPlanilla: null // 'H' // Honorarios
                        })
                        .then(resp => {

                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.pagos = resp.data.json.map(m => {
                                    return {
                                        seleccion: false,
                                        Accion: null,
                                        ...m,
                                        // Valor: parseFloat(m.Es_SeguroSaludSiempre == 1 && m.Tipo == 1 ?  m.ValorTablaSASI : (m.Tipo == 2) ? m.Valor * 1.12 : m.Valor) 
                                    }
                                })
                                this.pagosVisibles = this.Otros().agrupar(this.pagos)
                            }
                        })
                        .catch(() => {

                        })
                },

                Consulta_UltimaActualizacion: (Ajeno) => {
                    return this.axios.post('/app/ajenos/HonorariosConsultaEstados', {
                            Opcion: 'A',
                            Ajeno
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.info.FacturaFecha = resp.data.json[0].Fecha
                            }
                        })
                },

                // Consulta_Regla_Info: (item) => {
                //     const resp = {
                //         Id: item.Id,
                //         Ajeno: item.Ajeno,
                //         AJE_Tipo: item.AJE_Tipo,
                //         Orden: item.Orden,
                //         Linea: item.Linea
                //     }
                // },

            }
        },

        //=======================================================================================================
        // NUEVO
        //=======================================================================================================
        Nuevo: function () {
            return {}
        },

        //=======================================================================================================
        // GUARDAR
        //=======================================================================================================
        Guardar: function () {
            return {
                Habilitar: () => {
                    this.$vs.dialog({
                        type: 'confirm',
                        color: 'success',
                        title: 'Confirmación',
                        acceptText: 'Habilitar Pago',
                        cancelText: 'Cancelar',
                        text: `¿Desea habilitar el pago al médico ${this.info.Medico} por un total a facturar de ${this.calcular}?`,
                        accept: () => {

                            const arr = this.pagos.filter(f => f.seleccion).map(m => {
                                return m.Id
                            })
                            return this.axios.post('/app/ajenos/HonorariosHabilitarPago', {
                                    HonorariosHabilitar: arr
                                })
                                .then(() => {

                                    this.Consulta().Consulta_Pagos(this.info.Codigo)
                                })
                                .catch(() => {

                                })
                        }
                    })
                }
            }
        },

        //=======================================================================================================
        // EDITAR
        //=======================================================================================================
        Editar: function () {
            return {}
        },

        //=======================================================================================================
        // ELIMINAR
        //=======================================================================================================
        Eliminar: function () {
            return {}
        },

        //=======================================================================================================
        // OTROS
        //=======================================================================================================
        Otros: function () {
            return {
                Limpiar_Datos: () => {
                    Object.entries(this.info).forEach(([key]) => {
                        this.info[key] = null
                    });
                    this.info.Total = '0.00'
                    this.pagos = []
                    this.pagosVisibles = []
                },
                facturasCancelar: (actualizar = false) => {
                    this.info.MostrarFactura = false
                    if (actualizar) this.Consulta().Consulta_Pagos(this.info.Codigo)
                },
                agrupar: (items) => {
                    let respuesta = []

                    // Cargos
                    const cargos = items.filter(f => f.Tipo == "1")
                    const interpretacion = items.filter(f => f.Tipo == "2")
                    const cupones = items.filter(f => f.Tipo == "3")
                    const compromiso = items.filter(f => f.Tipo == "4")

                    const cargos30 = cargos.filter(f => f.Categoria == '30')
                    const cargos97 = cargos.filter(f => f.Categoria == '97')
                    const cargos98 = cargos.filter(f => f.Categoria == '98')
                    const cargos99 = cargos.filter(f => f.Categoria == '99')

                    // Funcion para crear el objecto
                    const crearObjeto = (info, tipo) => {
                        return {
                            seleccion: false,
                            Tipo: info.Tipo,
                            NombreSucursal: info.NombreSucursal,
                            TipoRegistro: info.TipoRegistro,
                            Documento: null,
                            Admision_FechaEgreso: null,
                            Cita_Cliente: null,
                            NombreCliente: null,
                            NombrePaciente: null,
                            NombreSeguro: null,
                            Categoria: info.Categoria,
                            NombreCategoria: info.NombreCategoria,
                            Valor: info.Valor,
                            Accion: tipo
                        }
                    }

                    // Agrupando cargos 30
                    if (cargos30.length > 0) {
                        const info = {
                            ...cargos30[0]
                        }
                        info.Valor = this.$formato_decimal(cargos30.reduce((acumulador, item) => parseFloat(acumulador) + parseFloat(item.Valor), 0), 2)
                        const resp = crearObjeto(info, 30)
                        this.pagosCargos["30"] = cargos30
                        respuesta.push(resp)
                    }

                    // Agrupando cargos 97
                    if (cargos97.length > 0) {
                        const info = {
                            ...cargos97[0]
                        }
                        info.Valor = this.$formato_decimal(cargos97.reduce((acumulador, item) => parseFloat(acumulador) + parseFloat(item.Valor), 0), 2)
                        const resp = crearObjeto(info, 97)
                        this.pagosCargos["97"] = cargos97
                        respuesta.push(resp)
                    }

                    if (cargos98.length > 0) {
                        const info = {
                            ...cargos98[0]
                        }
                        info.Valor = this.$formato_decimal(cargos98.reduce((acumulador, item) => parseFloat(acumulador) + parseFloat(item.Valor), 0), 2)
                        const resp = crearObjeto(info, 98)
                        this.pagosCargos["98"] = cargos98
                        respuesta.push(resp)
                    }

                    if (cargos99.length > 0) {
                        const info = {
                            ...cargos99[0]
                        }
                        info.Valor = this.$formato_decimal(cargos99.reduce((acumulador, item) => parseFloat(acumulador) + parseFloat(item.Valor), 0), 2)
                        const resp = crearObjeto(info, 99)
                        this.pagosCargos["99"] = cargos99
                        respuesta.push(resp)
                    }

                    // Tipo Interpretación
                    if (interpretacion.length > 0) {
                        const info = {
                            ...interpretacion[0]
                        }
                        info.Valor = interpretacion.reduce((acumulador, item) => parseFloat(acumulador) + parseFloat(item.Valor ? item.Valor : 0), 0).toLocaleString("es-GT", {
                            style: "decimal",
                            maximumFractionDigits: 4,
                            minimumFractionDigits: 4
                        })
                        const resp = crearObjeto(info, 2)
                        this.pagosCargos["2"] = interpretacion
                        respuesta.push(resp)
                    }

                    // Tipo Cupones
                    if (cupones.length > 0) {
                        const info = {
                            ...cupones[0]
                        }
                        info.Valor = cupones.reduce((acumulador, item) => parseFloat(acumulador) + parseFloat(item.Valor ? item.Valor : 0), 0).toLocaleString("es-GT", {
                            style: "decimal",
                            maximumFractionDigits: 4,
                            minimumFractionDigits: 4
                        })
                        const resp = crearObjeto(info, 3)
                        this.pagosCargos["3"] = cupones
                        respuesta.push(resp)
                    }

                    // Tipo Compromiso
                    if (compromiso.length > 0) {
                        const info = {
                            ...compromiso[0]
                        }
                        info.Valor = compromiso.reduce((acumulador, item) => parseFloat(acumulador) + parseFloat(item.Valor), 0).toLocaleString("es-GT", {
                            style: "decimal",
                            maximumFractionDigits: 4,
                            minimumFractionDigits: 4
                        })
                        const resp = crearObjeto(info, 4)
                        this.pagosCargos["4"] = compromiso
                        respuesta.push(resp)
                    }

                    return respuesta
                },
                seleccionCargo: (item) => {
                    if (item.Accion) {
                        this.pagos.filter(f => f.Categoria == item.Accion || f.Tipo == item.Accion).map(m => m.seleccion = item.seleccion)
                    }
                },
                verificarSeleccionParcial: (tipoCargo) => {
                    // Validando si es seleccion completa o parcial de una categoria de cargo (97 o 30)
                    const arr = this.pagosVisibles.filter(f => f.Categoria == tipoCargo || f.Tipo == tipoCargo)
                    const cargos = this.pagos.filter(f => f.Categoria == tipoCargo || f.Tipo == tipoCargo)
                    const seleccionados = cargos.filter(f => f.seleccion)
                    const nSeleccionados = cargos.filter(f => !f.seleccion).length
                    if (seleccionados.length > 0 && nSeleccionados > 0) {
                        if (arr.length > 0) {
                            const info = arr[0]
                            if (seleccionados.length > 0 && !info.seleccion) info.seleccion = true
                        }
                        return seleccionados.reduce((acumulador, item) => parseFloat(acumulador) + parseFloat(item.Valor ? item.Valor : 0), 0).toLocaleString("es-GT", {
                            style: "decimal",
                            maximumFractionDigits: 2,
                            minimumFractionDigits: 2
                        })
                    } else {

                        if (arr.length > 0) {
                            const info = arr[0]
                            if (seleccionados.length > 0 && !info.seleccion) info.seleccion = true
                            if (nSeleccionados > 0 && info.seleccion) info.seleccion = false
                        }
                        return null
                    }
                },
                reglaInfo: (item) => {
                    const info = {
                        Fitro: item.Fitro,
                        Regla: item.Regla,
                        Factura_0_Emitido: item.Factura_0_Emitido,
                        Factura_1_Emitido: item.Factura_1_Emitido,
                        Factura_2_Emitido: item.Factura_2_Emitido,
                        Factura_3_Emitido: item.Factura_3_Emitido,
                        Factura_0_Cancelado: item.Factura_0_Cancelado,
                        Factura_1_Cancelado: item.Factura_1_Cancelado,
                        Factura_2_Cancelado: item.Factura_2_Cancelado,
                        Factura_3_Cancelado: item.Factura_3_Cancelado,
                        SaldoAjeno: item.SaldoAjeno,
                        Cuenta_Cerrada: item.Cuenta_Cerrada,
                        Candado_Seguro: item.Candado_Seguro,
                        Filtro_Fecha: item.Filtro_Fecha,
                        Regla_Fecha: item.Regla_Fecha
                    }
                    if (info.Filtro == 0) return ' - No coincide con el filtro'
                    if (info.Regla == 0) return ' - Sin regla de pago'
                    let regla = ''
                    if (info.Factura_0_Emitido == 0) regla += ' - Factura 0 no emitida'
                    if (info.Factura_1_Emitido == 0) regla += ' - Factura 1 no emitida'
                    if (info.Factura_2_Emitido == 0) regla += ' - Factura 2 no emitida'
                    if (info.Factura_3_Emitido == 0) regla += ' - Factura 3 no emitida'
                    if (info.Factura_0_Cancelado == 0) regla += ' - Factura 0 no cancelada'
                    if (info.Factura_1_Cancelado == 0) regla += ' - Factura 1 no cancelada'
                    if (info.Factura_2_Cancelado == 0) regla += ' - Factura 2 no cancelada'
                    if (info.Factura_3_Cancelado == 0) regla += ' - Factura 3 no cancelada'
                    if (info.SaldoAjeno > 1) regla += ' - Cuenta ajena con saldo'
                    if (info.Cuenta_Cerrada == 0) regla += ' - La cuenta no esta cerrada'
                    if (info.Filtro_Fecha == 0) regla += ' - Fecha de filtro superior al cargo'
                    if (info.Regla_Fecha == 0) regla += ' - Fecha de regla superior al cargo'
                    if (info.Candado_Seguro == 0) regla += ' - Candado Seguro'
                    if (regla == '') regla = 'Existen multiples reglas (No es posible determinar la regla a utilizar)'
                    return regla
                }
            }
        }
    }
}
</script>

<style scoped>
.facturas {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100em;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 50px;
    z-index: 99999;
}

</style>