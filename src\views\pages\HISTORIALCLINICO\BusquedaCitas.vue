<template>
<div id="busqueda-paciente">
    <DxDataGrid v-if="Citas !== null" :ref="dataGridRefKey" v-bind="DefaultDxGridConfiguration" :data-source="Citas" :headerFilter="{visible:true, allowSearch: true }" :searchPanel="{visible:true}" :sorting="{mode: 'none'}" :paging="{ enabled:true, pageSize:10 }" :height="'100%'" :width="'100%'" @row-dbl-click="SeleccionarCita">
        <DxDataGridSelection mode="single" />

        <!-- <DxGroupPanel :visible="true" />
        <DxGrouping :auto-expand-all="false" /> -->

        <DxDataGridColumn :width="150" data-field="Nombre" alignment="center" />
        <DxDataGridColumn :width="150" data-field="Apellido" alignment="center" />
        <DxDataGridColumn :width="120" data-field="FechaCita" caption="Hora cita" format="HH:mm" data-type="datetime" alignment="center" :visible="!TipoBusqueda" />
        <DxDataGridColumn :width="140" data-field="FechaConfirmacion" caption="Fecha confirmacion" format="dd/MM/yyyy HH:mm" data-type="datetime" alignment="center" :visible="!TipoBusqueda" />
        <DxDataGridColumn :width="100" data-field="Edad" alignment="center" />
        <DxDataGridColumn :width="200" data-field="NombreMedico" caption="Médico" alignment="center" :visible="!TipoBusqueda" />
        <DxDataGridColumn :width="150" data-field="Especialidad" alignment="center" :visible="!TipoBusqueda" />
        <DxDataGridColumn :width="150" data-field="TipoAfiliacion" caption="Tipo afiliación" alignment="center" :visible="!TipoBusqueda" />
        <DxDataGridColumn :width="150" data-field="IdCliente" caption="Código cliente" alignment="center" :visible="TipoBusqueda" />
        <DxDataGridColumn :width="150" data-field="IdPaciente" caption="Código paciente" alignment="center" :visible="TipoBusqueda" />
    </DxDataGrid>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'

const dataGridRefKey = 'grid'

export default {
    name: 'BusquedaCitas',
    components: {},
    data() {
        return {
            DefaultDxGridConfiguration,
            dataGridRefKey,
        }
    },
    props: {
        Citas: null,
        TipoBusqueda: null //Variable para determinar si se está buscando las citas de los médicos o los pacientes
    },
    methods: {
        SeleccionarCita(e) {
            e.data.EdadEspecifica = this.CalcularEdad(e.data)
            e.data.TipoBusqueda = this.TipoBusqueda
            this.$emit('cita', e.data);
            this.dataGrid.refresh();
            this.dataGrid.clearSelection();
        },
        CalcularEdad(e) {
            // var date = new Date(e)
            // var today = new Date()
            // var years = date.getYear() - today.getYear()
            // var months = 0
            // var days = 0

            // if (today.getMonth() >= date.getMonth()) {
            //     months = today.getMonth() - date.getMonth()
            // } else {
            //     years = years--
            //     months = 12 + today.getMonth() - date.getMonth()
            // }

            // if (today.getDate() >= date.getDate()) {
            //     days = today.getDate() - date.getDate()
            // }
            // else{

            // }

            var y = e.EdadYY,
                m = e.EdadMM,
                d = e.EdadDD,
                yText = '',
                mText = '',
                dText = ''

            if (y > 0) {
                yText = y + ' años '
            }
            if (m > 0) {
                mText = m + ' meses '
            }
            if (d > 0 && y <= 5) {
                dText = d + ' días'
            }
            return yText + mText + dText
        }
    },
    mounted() {},
    watch: {
        'Citas'(newval) {
            if (newval !== null) {
                this.dataGrid.columnOption('FechaCita', 'sortOrder', 'desc')
                this.dataGrid.clearFilter()
            }
        },
    },
    computed: {
        dataGrid: function () {
            return this.$refs[dataGridRefKey].instance;
        },
    },
}
</script>
