/**************************************************************************************************************************************/
/*******************************************INICIO CREACION DE PAQUETES****************************************************************/
/**************************************************************************************************************************************/

/**************************************************************************************************************************************/
/******************************************* spRecalcularCargos ***********************************************************************/
/**************************************************************************************************************************************/
USE [HOSPITAL]
GO

/****** Object:  StoredProcedure [dbo].[spRecalcularCargos]    Script Date: 07/09/2023 12:18:45 p.m. ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
-- ==========================================================
-- Author:  Javier Castillo
-- Create date: 2023.04.12
-- Description:	Recalcular cargos de admisiones
-- ==========================================================
CREATE PROCEDURE [dbo].[spRecalcularCargos]
  @i_Opcion				varchar(10) = 'CONSULTA',
	@i_SubOpcion    varchar(10) = 'ADMISION',
	@i_Admision			varchar(012) = null,
	@i_Paciente			varchar(200) = null,
	@i_Nombres		  varchar(50) = null,
	@i_Apellidos		  varchar(50) = null,
	@i_Activa			int = 1,		-- Si la admision se encuentra activa
	@i_ConHabitacion	int = 0,		-- Si la admisión posee habilitación 
	@i_Habitacion char(4) = NULL,
	@i_EmpresaUnifica	varchar(3) = 'MED',
	@i_EmpresaReal		varchar(3),
	@i_Hospital		varchar(3) = NULL,

  @i_Aseguradora varchar(60) = null,
  @i_CodigoSeguro varchar(3) = null,
  @i_Poliza varchar(60) = null,

	@i_Serie char(1) = NULL,
	@i_CodigoAdmision int = NULL,
	@i_TipoRecalculo char(2) = NULL,
	--@i_TipoAdmision char(1) = NULL,
	@i_CodigoSeguroRecalculo char(3) = NULL,
	@i_AseguradoraRecalculo char(3) = null,
	@i_Razon varchar(100) = NULL,
	@i_PlanRn as varchar(15) = 'PM2202-R',
	@i_Corporativo int = NULL,
	@i_IdAfiliado char(17) = NULL
AS
BEGIN
	DECLARE @w_ErrRaise		Varchar(MAX)	= '',
			@w_ErrRaiseNum 	INT				= 0,
			@w_TipoConsultaAdmision tinyint = 1
			/*
				1 = Serie y Admision
				2 = Nombres y Apellidos
				3 = Habitación								
			*/

	SET NOCOUNT ON;
	IF @i_Opcion = 'CONSULTA'
	BEGIN
		IF @i_SubOpcion = 'ADMISION'
		BEGIN
				IF ISNULL(@i_Serie,'') <> '' AND ISNULL(@i_CodigoAdmision,'') <> ''
				BEGIN
					set @w_TipoConsultaAdmision = 1
				END
				ELSE IF ISNULL(@i_Nombres,'') <> '' OR ISNULL(@i_Apellidos,'') <> ''
				BEGIN
					set @w_TipoConsultaAdmision = 2
				END
				ELSE IF ISNULL(@i_Habitacion,'') <> ''
				BEGIN
					set @w_TipoConsultaAdmision = 3
					set @i_Activa = 1
				END

				select
					top 100 a.Serie,
					a.Codigo,
					a.Entrada,
					a.Salida,
					a.NivelPrecios,
					a.Habitacion,
					a.TipoDescuento,
					a.Seguro,
					a.Paciente IdPaciente,
					rtrim(p.Nombre) Nombres,
					rtrim(p.Apellido) Apellidos,
					rtrim(concat(rtrim(p.Nombre), ' ', rtrim(p.Apellido), ' ', p.ApellidoCasada )) Paciente,
					a.Medico CodigoMedico,
					rtrim(concat(rtrim(a2.Nombre), ' ', rtrim(a2.Apellido))) Medico, 
					concat(rtrim(a.Serie),'-',a.Codigo) as Admision 
				from
					Admisiones a
				LEFT JOIN Pacientes p ON
					a.Paciente = p.Codigo
					AND a.Empresa = p.Empresa
				LEFT JOIN Ajenos a2 ON
					a.Medico = a2.Codigo
					and a.Empresa = a2.Empresa
				WHERE a.Empresa = @i_EmpresaUnifica					
					and (( @w_TipoConsultaAdmision = 1 and a.Serie = @i_Serie and a.Codigo = @i_CodigoAdmision )				
					or   ( @w_TipoConsultaAdmision = 2 
									and (ISNULL(@i_Nombres,'') = '' or p.nombre like '%'+@i_Nombres+'%')
									and (ISNULL(@i_Apellidos,'') = '' or p.apellido like '%'+@i_Apellidos+'%'))
					or   ( @w_TipoConsultaAdmision = 3 and a.Habitacion = @i_Habitacion)
					)						
					and (@i_ConHabitacion = 0 or (a.Habitacion is not null AND replace(a.Habitacion,' ','') != '' ))
					and (@i_Activa = 0 or ( (a.Salida is null and a.FechaPreEgreso is null) or a.status = 'A'))
				order by
					a.Entrada desc
		END
		ELSE IF @i_SubOpcion = 'EMERGENCIA'
		BEGIN
				IF ISNULL(@i_Serie,'') <> '' AND ISNULL(@i_CodigoAdmision,'') <> ''
				BEGIN
					set @w_TipoConsultaAdmision = 1
				END
				ELSE IF ISNULL(@i_Nombres,'') <> '' OR ISNULL(@i_Apellidos,'') <> ''
				BEGIN
					set @w_TipoConsultaAdmision = 2
				END
				ELSE IF ISNULL(@i_Habitacion,'') <> ''
				BEGIN
					set @w_TipoConsultaAdmision = 3
				END

				select
					top 100 a.Serie,
					a.Codigo,
					a.Entrada,
					a.Salida,
					a.NivelPrecios,
					a.Habitacion,
					a.TipoDescuento,
					a.Seguro,
					a.Paciente IdPaciente,
					rtrim(p.Nombre) Nombres,
					rtrim(p.Apellido) Apellidos,
					rtrim(concat(rtrim(p.Nombre), ' ', rtrim(p.Apellido), ' ', p.ApellidoCasada )) Paciente,
					a.Medico CodigoMedico,
					rtrim(concat(rtrim(a2.Nombre), ' ', rtrim(a2.Apellido))) Medico, 
					concat(rtrim(a.Serie),'-',a.Codigo) as Admision 
				from
					Admisiones a
				LEFT JOIN Pacientes p ON
					a.Paciente = p.Codigo
					AND a.Empresa = p.Empresa
				LEFT JOIN Ajenos a2 ON
					a.Medico = a2.Codigo
					and a.Empresa = a2.Empresa
				WHERE a.Empresa = @i_EmpresaUnifica					
					and (( @w_TipoConsultaAdmision = 1 and a.Serie = @i_Serie and a.Codigo = @i_CodigoAdmision )				
					or   ( @w_TipoConsultaAdmision = 2 
									and (ISNULL(@i_Nombres,'') = '' or p.nombre like '%'+@i_Nombres+'%')
									and (ISNULL(@i_Apellidos,'') = '' or p.apellido like '%'+@i_Apellidos+'%'))
					or   ( @w_TipoConsultaAdmision = 3 and a.Habitacion = @i_Habitacion)
					)						
					and (@i_ConHabitacion = 0 or (a.Habitacion is not null AND replace(a.Habitacion,' ','') != '' ))
					and (@i_Activa = 0 or (a.Status = 'A'))
				order by
					a.Entrada desc
		END
    ELSE 	IF @i_SubOpcion = 'SEGURO'
		BEGIN
	      select top 1
						      s.Asegura codigoAseguradora
                , a.Nombre as Aseguradora
					      , s.Codigo
					      , s.Nombre as Poliza
					      , s.Activa
					      , s.PolizaPlanDirecto
					      , a.NombreFactura as FacturarA					  
	      from Seguros s (nolock) 
	      Left Join Aseguradoras a (nolock)
	      ON (a.Empresa = s.Empresa and a.Asegura = s.Asegura)
	      Where s.Empresa = @i_EmpresaUnifica
          and s.Activa = 'S'          
          and s.Codigo = @i_CodigoSeguro
		END
    ELSE 	IF @i_SubOpcion = 'SEGUROS'
		BEGIN
	      select top 100
						      s.Asegura codigoAseguradora
                , a.Nombre as Aseguradora
					      , s.Codigo
					      , s.Nombre as Poliza
					      , s.Activa
					      , s.PolizaPlanDirecto
					      , a.NombreFactura as FacturarA	
								, IsNull(a.ValidarProcesoRecalculo,'N') PermisoCambioPoliza
								, IsNull(a.ValidarAfiliacionAlAsignar,'N') SolicitarIdAfiliado
	      from Seguros s (nolock) 
	      Left Join Aseguradoras a (nolock)
	      ON (a.Empresa = s.Empresa and a.Asegura = s.Asegura)
	      Where s.Empresa = @i_EmpresaUnifica
          and s.Activa = 'S'
          and (@i_Aseguradora is null or a.Nombre like '%'+@i_Aseguradora+'%')
          and (@i_CodigoSeguro is null or s.Codigo like '%'+@i_CodigoSeguro+'%')
          and (@i_Poliza is null or s.Nombre like '%'+@i_Poliza+'%')          
		END
		ELSE
		BEGIN
			SELECT 0 AS codigo, CONCAT('No se ha encontrado la SubOpcion ',@i_SubOpcion) AS descripcion, 1 AS tipo_error;
		END
	END
	ELSE IF @i_Opcion='INSERTAR'
	BEGIN
		BEGIN TRAN
		BEGIN TRY

			IF @i_SubOpcion = 'RECALCULO'
			BEGIN
					declare @CoberturasRn Table(
						NombreRN varchar(100), --varchar(100)
						Serieadmisionraiz char(1), --char(1)
						AdmisionRaiz int,			 --int
						NombrePlan varchar(50), -- varchar(50)
						NombreContrato varchar(50), --varchar(50)
						EdadInicial float, --float 8 53
						EdadFinal float, -- float 8 53
						TipoContrato char(1),-- char(1)
						NombrePaciente varchar(100), -- varchar(100)
						Cobertura bit, -- bit
						numeroadhesion varchar(25), --varchar(25)
						idafiliado char(17), -- char(17)
						idplan char(15), -- char(15)
						ExisteCobertura bit
					)

					declare @InsertarCobertura Table(
						CodRespuesta  int,
						Respuesta varchar(500)
					)

					declare @w_Serie_admision_raiz char(1),
									@w_Admision_raiz as int,
									@w_idafiliado_madre as varchar(17)='',
									@w_adhesion_madre as varchar(20),									
									@w_Actualizar_cobertura as int,
									@w_RazonRecalculo varchar(100),
									@w_CantidadCargosOrtopedia int = 0,
									@w_TipoAdmisionRecalculo char(1),
									@w_CantidadFacturasVigentes int = 0,
									@w_CantidadCargosSeguro int = 0,
									@w_PolizaPlanDirecto bit = 0,
									@w_CodigoHospitalAdmision char(3) = null,
									@w_TipoIngresoAdmision char(1) = null,
									@w_CodigoHabitacionAdmision char(1) = null,
									@w_NivelPrecios tinyint = null,
									@w_PrecioDescuento smallmoney = null,
									@w_CodigoSeguroActual char(3) = null,
									@w_TipoAdmision char(1) = null,
									@w_AdmisionInterna char(1) = null,
									@w_PlanMedax bit = 0,
									@w_MedaxABC bit = 0,
									@w_ValidarAfiliacionAlAsignar char(1) = null,
									@w_StatusAfiliado char(1) = null,
									@w_FechaInicioCobertura datetime,
									@w_ExclusionVitalicia char(1) = null
					
					SELECT @w_CantidadFacturasVigentes = COUNT(*)
						FROM Facturas
					 WHERE SerieAdmision = @i_Serie and
								 Admision = @i_CodigoAdmision and
								 Status = 'P' and
								 (@i_TipoRecalculo != 'OC' OR Tipo = 3)
					
					SELECT @w_CodigoSeguroActual = Seguro,
								 @w_TipoAdmision = TipoDescuento,
								 @w_AdmisionInterna = ISNULL(Interno,'N'),
								 @w_idafiliado_madre = IdAfiliado,
								 @w_PlanMedax = PlanMedax,
								 @w_MedaxABC = MedaxABC
						FROM Admisiones
				   WHERE Empresa = @i_EmpresaUnifica and
								 Serie = @i_Serie and
								 Codigo = @i_CodigoAdmision

					IF @i_TipoRecalculo = 'OC'
					BEGIN
						Select @w_CantidadCargosSeguro = COUNT(*)
							from Cargos C						  
						where c.Empresa = @i_EmpresaUnifica and c.SerieAdmision = @i_Serie and c.Admision = @i_CodigoAdmision
							and c.FacRec IS NULL and c.Categoria <> 30

						IF @w_CantidadCargosSeguro = 0 
						BEGIN
							SELECT 0 AS codigo, 'Admisión Sin Cargos Diferentes a Extraordinarios para Recalcular Anule Facturas' AS descripcion,
										 1 AS tipo_error
							ROLLBACK TRAN
							RETURN
						END

						Select @w_PolizaPlanDirecto = isnull(P.PolizaPlanDirecto,0)
							from Seguros P							
						 Where P.Empresa = @i_EmpresaUnifica and P.Codigo = @w_CodigoSeguroActual

						IF @w_PolizaPlanDirecto = 1
						BEGIN
							SELECT 0 AS codigo, 'Admisión de Plan Directo, llame al Call Center Ó Anule Facturas' AS descripcion,
										 1 AS tipo_error
							ROLLBACK TRAN
							RETURN
						END
					END

					IF @w_CantidadFacturasVigentes > 0
					BEGIN
						IF @i_TipoRecalculo = 'OC'
						BEGIN
							SELECT 0 AS codigo, 'Admision Facturada Paso 3 NO puede Recalcularla Debe anular la Factura del Seguro' AS descripcion,
										 1 AS tipo_error
							ROLLBACK TRAN
							RETURN
						END
						ELSE
						BEGIN
							SELECT 0 AS codigo, 'La admisión cuenta con facturas vigentes, proceda a anular antes de realizar el recalculo.' AS descripcion,
										 1 AS tipo_error
							ROLLBACK TRAN
							RETURN
						END
					END

					Select @w_CodigoHospitalAdmision = Codigo, 
								 @w_TipoIngresoAdmision = CASE WHEN Internos = @i_Serie THEN 'I' 
																							 WHEN Urgencias = @i_Serie THEN 'U' 
																							 WHEN Externos = @i_Serie THEN 'E' 
																							 WHEN Igss = @i_Serie THEN 'G' 
																							 WHEN Farmacia = @i_Serie THEN 'F' 
																					END,  
								 @w_CodigoHabitacionAdmision = CodigoHab
					from NominaDB..EmpHospital (nolock)
					Where Empresa = @i_EmpresaUnifica
					and (Internos is not NULL or Urgencias is not NULL or Externos is not NULL or Igss is not NULL or Farmacia is not NULL)
					and (Internos = @i_Serie or Urgencias = @i_Serie or Externos = @i_Serie or Igss = @i_Serie or Farmacia = @i_Serie)

					--Tipo de Calculo a privada borramos los campos de IdAfiliado, PlanMedax, MedaxABC
					IF @i_TipoRecalculo = 'S' 
					Begin
						set @w_idafiliado_madre = ''
						set	@w_PlanMedax = 0
						set @w_MedaxABC = 0
					End

					IF @i_TipoRecalculo = 'P' OR @i_TipoRecalculo = 'O' OR @i_TipoRecalculo = 'OC' 
					BEGIN
						Select 
									@w_NivelPrecios = CASE WHEN @w_TipoIngresoAdmision = 'E' THEN a.nivelpreciosext
																				 WHEN @w_TipoIngresoAdmision = 'I' or @w_AdmisionInterna = 'S' THEN a.nivelpreciosint 
																				 --Emergencia
																				 WHEN @w_TipoIngresoAdmision = 'U' THEN a.nivelprecioseme
																	  END, 
								 @w_PrecioDescuento = CASE WHEN @w_TipoIngresoAdmision = 'E' THEN a.descuentoext
																			  	 WHEN @w_TipoIngresoAdmision = 'I' or @w_AdmisionInterna = 'S' THEN a.descuentoint 
																				   --Emergencia
																				   WHEN @w_TipoIngresoAdmision = 'U' THEN a.descuentoeme
																	  END,
								 @w_ValidarAfiliacionAlAsignar = isnull(A.ValidarAfiliacionAlAsignar,'N'),
								 @w_PolizaPlanDirecto = isnull(S.PolizaPlanDirecto,0)
									 /*s.empresa, s.codigo, s.nombre, a.fechacambioniv,*/
					   From Seguros S	Inner Join 
									Aseguradoras A ON (S.Empresa = A.Empresa and S.Asegura = A.Asegura)
						Where A.empresa = @i_EmpresaUnifica
						  and A.Asegura = @i_AseguradoraRecalculo
						  and S.Codigo = @i_CodigoSeguroRecalculo
						
						IF @w_PolizaPlanDirecto = 1 and @i_TipoRecalculo = 'OC'
						BEGIN
							 SELECT 0 AS codigo, 'Poliza plan directo no es valida para recalculos con copago.' AS descripcion,
											1 AS tipo_error
							ROLLBACK TRAN
							RETURN
						END
						-- validacion que el cliente tenga una afiliacion valida 
						IF @w_ValidarAfiliacionAlAsignar = 'S'
						BEGIN
								 IF ISNULL(@i_IdAfiliado,'N') = 'N' 
								 BEGIN
										SELECT 0 AS codigo, 'Codigo de Afiliación invalido' AS descripcion,
											  	 1 AS tipo_error
										ROLLBACK TRAN
										RETURN
								 END

								 select  @w_StatusAfiliado = ac.Status
												,@w_FechaInicioCobertura = ac.FechaInicioCobertura
												,@w_ExclusionVitalicia = cl.ExclusionVitalicia
									from  PLANESMEDICOS.dbo.PlanesXContrato pc left outer join PLANESMEDICOS.dbo.Contratos c
										on (pc.IdEmpresa = c.IdEmpresa AND pc.IdContrato	 = c.IdContrato )	left outer join PLANESMEDICOS.dbo.AfiliadosXContrato ac
										on (ac.IdEmpresa = pc.IdEmpresa and ac.IdPlan = pc.IdPlanXContrato )	inner join Seguros s 
										on (pc.IdEmpresa = s.Empresa and pc.IdPoliza = s.Codigo)	inner join PLANESMEDICOS..Clientes cl 
										on (cl.IdEmpresa = pc.IdEmpresa and cl.IdCliente = ac.IdCliente)	left join Pacientes  pa
										on (pa.Empresa = cl.IdEmpresa	and pa.IdCliente = cl.IdCliente)	inner join Aseguradoras ase
										on (ase.Empresa = pc.IdEmpresa	and  ase.Asegura = s.Asegura)
								Where ac.IdEmpresa = @i_EmpresaUnifica	
								  and ac.IdAfiliado = @i_IdAfiliado
									and s.Codigo = @i_CodigoSeguroRecalculo
									and ase.Asegura = @i_AseguradoraRecalculo									
									and s.Asegura  in ('BSM','BSS', 'MBI')

								 IF @@RowCount <= 0
								 BEGIN
										SELECT 0 AS codigo, 'No se seleccionó una afiliación valida para el seguro' AS descripcion,
											  	 1 AS tipo_error
										ROLLBACK TRAN
										RETURN
								 END

								 IF ISNULL(@w_StatusAfiliado,'N') != 'A' 
								 BEGIN
										SELECT 0 AS codigo, 'El Afiliado no esta activo' AS descripcion,
											  	 1 AS tipo_error
										ROLLBACK TRAN
										RETURN
								 END

								 IF ISNULL(@w_ExclusionVitalicia,'N') = 'S' 
								 BEGIN
										SELECT 0 AS codigo, 'El Cliente validado tiene especificado "Exclusión Vitalicia", Verifique...' AS descripcion,
											  	 1 AS tipo_error
										ROLLBACK TRAN
										RETURN
								 END

								 IF @w_FechaInicioCobertura > GetDate()
								 BEGIN
										SELECT 0 AS codigo, 'La Vigencia de cobertura no ha iniciado' AS descripcion,
											  	 1 AS tipo_error
										ROLLBACK TRAN
										RETURN
								 END
						
								set @w_idafiliado_madre = @i_IdAfiliado 
								set @w_PlanMedax = 1
								set @w_MedaxABC = 0
						END

					END
					ELSE IF @i_TipoRecalculo = 'A'
					BEGIN
						Select 
									@w_NivelPrecios = a.nivelpreciosext
					   From Seguros S	Inner Join 
									Aseguradoras A ON (S.Empresa = A.Empresa and S.Asegura = A.Asegura)
						Where A.empresa = @i_EmpresaUnifica
						  and A.Asegura = @i_AseguradoraRecalculo
						  and S.Codigo = @i_CodigoSeguroRecalculo
					END
					ELSE IF @i_TipoRecalculo = 'S'
					BEGIN
						Select @w_NivelPrecios = CASE WHEN @w_TipoIngresoAdmision = 'E' THEN Nivel_Externos
																					WHEN @w_TipoIngresoAdmision = 'I' or @w_AdmisionInterna = 'S' THEN Nivel_Internos
																					WHEN @w_TipoIngresoAdmision = 'U' THEN Nivel_Emergencia 
																			END
							from NominaDB..EmpHospital
		  			 Where Empresa = @i_EmpresaUnifica and 
									 Codigo = @i_Hospital and
									 Activo = 'S'

					END

					IF @i_TipoRecalculo = 'P' 
					BEGIN
						IF @w_TipoAdmision = 'N' OR @w_TipoAdmision = 'A'
							BEGIN
								SET @w_TipoAdmisionRecalculo = 'S'
							END
						ELSE
							BEGIN
								SELECT 0 AS codigo, 'Conversion Invalida *** Esta admisión no es PRIVADA ***' AS descripcion,
											 1 AS tipo_error
								ROLLBACK TRAN
								RETURN
							END
					END
					ELSE IF @i_TipoRecalculo = 'S'  
					BEGIN
						IF @w_TipoAdmision = 'S'
							BEGIN
								SET @w_TipoAdmisionRecalculo = 'N'
							END
						ELSE
							BEGIN
								SELECT 0 AS codigo, 'Conversion Invalida *** Esta admisión no es de SEGUROS ***' AS descripcion,
											 1 AS tipo_error
								ROLLBACK TRAN
								RETURN
							END
					END
					ELSE IF @i_TipoRecalculo = 'O' 
					BEGIN
						IF @w_TipoAdmision = 'S'
							BEGIN
								SET @w_TipoAdmisionRecalculo = 'S'
							END
						ELSE
							BEGIN
								SELECT 0 AS codigo, 'Conversion Invalida *** Esta admisión no es de SEGUROS ***' AS descripcion,
											 1 AS tipo_error
								ROLLBACK TRAN
								RETURN
							END
					END
					ELSE IF @i_TipoRecalculo = 'A'
					BEGIN
						IF @w_TipoAdmision = 'Q'
							BEGIN
								SET @w_TipoAdmisionRecalculo = 'S'
							END
						ELSE
							BEGIN
								SELECT 0 AS codigo, 'Conversion Invalida *** Esta admisión no es de PAQUETE ***' AS descripcion,
											 1 AS tipo_error
								ROLLBACK TRAN
								RETURN
							END
					END
					ELSE IF @i_TipoRecalculo = 'OC' 
					BEGIN
						IF @w_TipoAdmision = 'S'
							BEGIN
								SET @w_TipoAdmisionRecalculo = 'S'
							END
						ELSE
							BEGIN
								SELECT 0 AS codigo, 'Conversion Invalida *** Esta admisión no es de SEGUROS ***' AS descripcion,
											 1 AS tipo_error
								ROLLBACK TRAN
								RETURN
							END
					END

					IF @i_CodigoSeguroRecalculo = '51R'
					Begin
					
						insert into @CoberturasRn execute sp_validar_cobertura_rn 
												@i_Serie_Admision = @i_Serie, @i_codigo_admision = @i_CodigoAdmision
						
						select top 1 @w_Serie_admision_raiz = Serieadmisionraiz, 
												 @w_Admision_raiz = AdmisionRaiz,
												 @w_idafiliado_madre = idafiliado,
												 @w_adhesion_madre = numeroadhesion,
												 @w_Actualizar_cobertura = ExisteCobertura

						 from @CoberturasRn 

						IF @@Rowcount > 0
						BEGIN
								IF (SELECT TOP 1 IdAfiliado FROM @CoberturasRn) = 0
								BEGIN
									SELECT 0 AS codigo, CONCAT('Este seguro es solo para recien nacido, Salud Siempre,',' verifique poliza asignada a la madre') AS descripcion, 1 AS tipo_error;
									ROLLBACK TRAN
									RETURN
								END

								IF (SELECT TOP 1 EdadFinal FROM @CoberturasRn) > 70
								BEGIN
									SELECT 0 AS codigo, CONCAT('El Seguro no cubre para adulto mayor','') AS descripcion, 1 AS tipo_error;
									ROLLBACK TRAN
									RETURN
								END

								IF (SELECT TOP 1 TipoContrato FROM @CoberturasRn) <> 'C'
								BEGIN
									SELECT 0 AS codigo, CONCAT('El beneficio de RN, Solo aplica para polizas de tipo completo','') AS descripcion, 1 AS tipo_error;
									ROLLBACK TRAN
									RETURN
								END
						END

						insert into @InsertarCobertura
						EXECUTE sp_insertar_cobertura_rn  
									@w_Serie_admision = @i_Serie,
									@w_Admision = @i_CodigoAdmision,
									@w_Usuario = @i_Corporativo,
									@w_Serie_admision_raiz = @w_Serie_admision_raiz,
									@w_Admision_raiz = @w_Admision_raiz,
									@w_idafiliado_madre = @w_idafiliado_madre,
									@w_adhesion_madre = @w_adhesion_madre ,
									@w_Plan_rn = @i_PlanRn,
									@w_Actualizar_cobertura = ExisteCobertura

						 set @w_PlanMedax = 1
						 set @w_MedaxABC = 0 
					END

			END

			IF  @w_CodigoSeguroActual = '51R' AND @i_CodigoSeguroRecalculo <> '51R'
			BEGIN
			 Update HOSPITAL.dbo.Caj_limites_cobertura
					Set status = 0
				Where serie_admision = @i_Serie
				  And admision = @i_CodigoAdmision
			END

			IF ISNULL(@w_CodigoSeguroActual,'') <> ''
			BEGIN
				set @w_RazonRecalculo = CONCAT('Niv.New: ',@w_NivelPrecios,' - Pól.Ant: ',@w_CodigoSeguroActual,' ')
			END
			ELSE
				set @w_RazonRecalculo = CONCAT('Nuevo Nivel: ',@w_NivelPrecios)

			IF @i_TipoRecalculo <> 'A'
			BEGIN

				Update Cargos  
				   Set PrecioUnitario = P.Precio
					From Cargos C	INNER JOIN 
							 INVENTARIO.DBO.PRODUCTOSPRECIOS P 
					ON (P.Empresa=C.Empresa AND 
							P.Producto=C.Producto AND 
							P.SubNivel = C.SubNivelPrecios AND 
							P.NIVEL = @w_NivelPrecios )
				WHERE C.EMPRESA = @i_EmpresaUnifica
					AND C.FACREC IS NULL
					AND P.Precio > 0
					AND C.CATEGORIA NOT IN (99,98,97,82)
					AND C.TIPOORDEN NOT IN ('AJE')
					AND C.SerieAdmision = @i_Serie 
					AND C.Admision= @i_CodigoAdmision

				Update Cargos  
					 SET Valor = ROUND(PrecioUnitario * Cantidad, 2)
					from Cargos C
				 Where C.Empresa = @i_EmpresaUnifica	AND 
							 C.FACREC IS NULL	AND 
							 C.PRECIOUNITARIO <> 0	AND 
							 C.Categoria NOT IN (99,98,97,82) AND 
							 C.TipoOrden NOT IN ('AJE') AND 
							 C.SerieAdmision = @i_Serie AND 
							 C.Admision = @i_CodigoAdmision					        

				select @w_CantidadCargosOrtopedia = count(*)
					From Cargos C	
					WHERE C.EMPRESA = @i_EmpresaUnifica
						AND C.FACREC IS NULL
						AND C.CATEGORIA IN (82)
						AND C.TIPOORDEN NOT IN ('AJE')
						AND C.SerieAdmision = @i_Serie 
						AND C.Admision= @i_CodigoAdmision

				IF @w_CantidadCargosOrtopedia > 0
				BEGIN
					Update Cargos  
					 Set PrecioUnitario = round((Case When p.Costeo = 'U' Then p.CostoUltimo  Else p.CostoPromedio END) * (1+(convert(float,N.PorcentajeIncremento)/100)),2)
					From Cargos C	INNER JOIN 
	  					 INVENTARIO.DBO.productos P 
						ON (P.Empresa=C.Empresa AND 
								P.Codigo=C.Producto )
							 INNER JOIN InvNivelPreciosOrtopedia N
						ON (P.Empresa = N.Empresa)
					WHERE N.NivelPrecio = @w_NivelPrecios
						AND C.EMPRESA = @i_EmpresaUnifica
						AND C.FACREC IS NULL
						AND C.CATEGORIA IN (82)
						AND C.TIPOORDEN NOT IN ('AJE')
						AND C.SerieAdmision = @i_Serie
						AND C.Admision= @i_CodigoAdmision

					IF @@RowCount = 0
					BEGIN
						SELECT  0 AS codigo,
										CONCAT('Configurar el nivel de precio de ortopedia (',@w_NivelPrecios,') para recalcular la orden.') AS descripcion, 
										1 AS tipo_error
						ROLLBACK TRAN
						RETURN
					END

					Update Cargos  
						 SET Valor = ROUND(PrecioUnitario * Cantidad, 2)
						from Cargos C
					 Where C.Empresa = @i_EmpresaUnifica	AND 
								 C.FACREC IS NULL	AND 
								 C.PRECIOUNITARIO <> 0	AND 
								 C.Categoria  IN (82) AND 
								 C.TipoOrden NOT IN ('AJE') AND 
								 C.SerieAdmision = @i_Serie AND 
								 C.Admision = @i_CodigoAdmision
					END
				END-- Fin CantidadCargosOrtopedia
				--@w_idafiliado_madre
				
				INSERT INTO Recalcular(Empresa,SerieAdmision,Admision,Calculo,Nivel,
									   Fecha,Usuario,Observaciones)
					 VALUES (@i_EmpresaUnifica,@i_Serie,@i_CodigoAdmision,@i_TipoRecalculo,@w_NivelPrecios,
							 GetDate(),@i_Corporativo,Concat(@i_Razon,' ',@w_RazonRecalculo,' - User. ',@i_Corporativo))

				UPDATE Admisiones
					 SET
							 NivelPrecios = @w_NivelPrecios,
							 Seguro = @i_CodigoSeguroRecalculo,
							 TipoDescuento = @w_TipoAdmisionRecalculo,
							 PorcentajeDescuento = @w_PrecioDescuento,
							 IdAfiliado = @w_idafiliado_madre,
							 PlanMedax = @w_PlanMedax,
							 MedaxABC = @w_MedaxABC
				 Where Empresa = @i_EmpresaUnifica and 
							 Serie = @i_Serie and 
							 Codigo = @i_CodigoAdmision

				COMMIT TRAN

				SELECT  0 AS codigo,
								'Se Recalculo la admisión exitosamente' AS descripcion, 
								0 AS tipo_error
			
			END TRY
			BEGIN CATCH
				ROLLBACK TRAN;
				Set @w_ErrRaise = ERROR_MESSAGE()
				Set @w_ErrRaiseNum = ERROR_STATE()
				RAISERROR(@w_ErrRaise,16,@w_ErrRaiseNum)
			END CATCH
	END
	ELSE
	BEGIN
		SELECT 0 AS codigo, CONCAT('No se ha encontrado la Opcion ',@i_Opcion) AS descripcion, 1 AS tipo_error;
	END
END

GO
/**************************************************************************************************************************************/
/******************************************* SpAdmObtienePolizasSaludSiemprexPaciente *************************************************/
/**************************************************************************************************************************************/
USE [HOSPITAL]
GO

/****** Object:  StoredProcedure [dbo].[SpAdmObtienePolizasSaludSiemprexPaciente]    Script Date: 07/09/2023 12:14:15 p.m. ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

-- ==================================================================================
-- Author:		Emerson Rios
-- Create date: 2023 06 28
-- Description: Consulta la lista de polizas salud siempre de un paciente 
-- Proyecto :   RT 289135 Migración de Admisiones
-- ==================================================================================
CREATE  PROCEDURE [dbo].[SpAdmObtienePolizasSaludSiemprexPaciente](
    @i_Opcion		varchar(10) = 'CONSULTA',
	@i_SubOpcion    varchar(10) = 'CLIENTE',
	@i_Empresa	    VARCHAR(3) = NULL,
	@i_IdPaciente   int = NULL		
)
AS
BEGIN

	SET NOCOUNT ON;

	IF @i_Opcion = 'CONSULTA'
	Begin
		IF @i_SubOpcion = 'CLIENTE'
		BEGIN
			select		  pa.Codigo IdPaciente
						, s.Codigo
						, ac.IdAfiliado AS CodigoAfiliado
						, ac.IdPlan AS CodigoPlan
						, ac.IdContrato AS CodigoContrato
						, pc.Nombre AS NombrePlan
						, ac.Status Estado
						, ase.NivelPreciosExt NivelPrecios
				from   PLANESMEDICOS.dbo.PlanesXContrato pc  
				left outer join PLANESMEDICOS.dbo.Contratos c
				on  pc.IdEmpresa = c.IdEmpresa 
					AND pc.IdContrato	 = c.IdContrato 
				left outer join PLANESMEDICOS.dbo.AfiliadosXContrato ac
				on ac.IdEmpresa = pc.IdEmpresa
					and ac.IdPlan	     = pc.IdPlanXContrato 
				inner join Seguros s 
				on pc.IdEmpresa = s.Empresa
				and pc.IdPoliza = s.Codigo
				inner join PLANESMEDICOS..Clientes cl 
				on  cl.IdEmpresa = pc.IdEmpresa 
				and cl.IdCliente = ac.IdCliente
				left join Pacientes  pa
				on pa.Empresa = cl.IdEmpresa
				and pa.IdCliente = cl.IdCliente
				inner join Aseguradoras ase
				on ase.Empresa = pc.IdEmpresa
				and  ase.Asegura = s.Asegura
				Where ac.IdEmpresa = @i_Empresa
				and pa.Codigo   = @i_IdPaciente    
				and ase.GrupoFacturacion = 1
			union all
				select         @i_IdPaciente IdPaciente
							, 'ABC' Codigo
							, 'ABC' AS CodigoAfiliado
							, 'ABC' AS CodigoPlan
							, 'ABC' AS CodigoContrato
							, 'Plan ABC (Pendiente Codificar)' AS NombrePlan
							, 'A' AS Status
							, ase.NivelPreciosExt NivelPrecios
				 from   PLANESMEDICOS.dbo.PlanesXContrato pc  
				left outer join PLANESMEDICOS.dbo.Contratos c
				on  pc.IdEmpresa = c.IdEmpresa 
					AND pc.IdContrato	 = c.IdContrato 
				left outer join PLANESMEDICOS.dbo.AfiliadosXContrato ac
				on ac.IdEmpresa = pc.IdEmpresa
					and ac.IdPlan	     = pc.IdPlanXContrato 
				inner join Seguros s 
				on pc.IdEmpresa = s.Empresa
				and pc.IdPoliza = s.Codigo
				inner join PLANESMEDICOS..Clientes cl 
				on  cl.IdEmpresa = pc.IdEmpresa 
				and cl.IdCliente = ac.IdCliente
				left join Pacientes  pa
				on pa.Empresa = cl.IdEmpresa
				and pa.IdCliente = cl.IdCliente
				inner join Aseguradoras ase
				on ase.Empresa = pc.IdEmpresa
				and  ase.Asegura = s.Asegura
				Where ac.IdEmpresa = @i_Empresa
				and pa.Codigo   = @i_IdPaciente    
				and ase.GrupoFacturacion = 1
		END
		IF @i_SubOpcion = 'CONTRATO'
		BEGIN
				select pa.Codigo IdPaciente
							,pc.IdPoliza  
						--, ac.IdPlan
						, s.Codigo
						, ac.IdAfiliado AS CodigoAfiliado
						--, ac.IdEmpresa AS Empresa
						, ac.IdPlan AS CodigoPlan
						, ac.IdContrato AS CodigoContrato
						--, ac.Parentesco
						, pc.Nombre AS NombrePlan
						, ac.Status Estado
						, ase.NivelPreciosExt NivelPrecios
						, cl.ExclusionVitalicia
						, ac.FechaInicioCobertura
				from  PLANESMEDICOS.dbo.PlanesXContrato pc left outer join PLANESMEDICOS.dbo.Contratos c
					on (pc.IdEmpresa = c.IdEmpresa AND pc.IdContrato	 = c.IdContrato )	left outer join PLANESMEDICOS.dbo.AfiliadosXContrato ac
					on (ac.IdEmpresa = pc.IdEmpresa and ac.IdPlan = pc.IdPlanXContrato )	inner join Seguros s 
					on (pc.IdEmpresa = s.Empresa and pc.IdPoliza = s.Codigo)	inner join PLANESMEDICOS..Clientes cl 
					on (cl.IdEmpresa = pc.IdEmpresa and cl.IdCliente = ac.IdCliente)	left join Pacientes  pa
					on (pa.Empresa = cl.IdEmpresa	and pa.IdCliente = cl.IdCliente)	inner join Aseguradoras ase
					on (ase.Empresa = pc.IdEmpresa	and  ase.Asegura = s.Asegura)
			Where ac.IdEmpresa = @i_Empresa	and pa.Codigo   = @i_IdPaciente  
				and s.Asegura  in ('BSM','BSS', 'MBI')
		END
		ELSE
		BEGIN
			SELECT 0 AS codigo, CONCAT('No se ha encontrado la SubOpcion ',@i_SubOpcion) AS descripcion, 1 AS tipo_error;
		END
	END
	ELSE
	BEGIN
		SELECT 0 AS codigo, CONCAT('No se ha encontrado la Opcion ',@i_Opcion) AS descripcion, 1 AS tipo_error;
	END
END
GO

/**************************************************************************************************************************************/
/*******************************************FIN CREACION DE PAQUETES*******************************************************************/
/**************************************************************************************************************************************/

/**************************************************************************************************************************************/
/******************************************* INICIO CREACION DE PERMISOS **************************************************************/
/**************************************************************************************************************************************/

USE HOSPITAL
GRANT EXECUTE ON spRecalcularCargos TO USRHISJEFECAJA
GO

USE HOSPITAL
GRANT EXECUTE ON SpAdmObtienePolizasSaludSiemprexPaciente TO USRHISJEFECAJA
GO

USE PLANESMEDICOS
GRANT SELECT ON clientes TO USRHISJEFECAJA
GO

USE PLANESMEDICOS
GRANT SELECT ON contratos TO USRHISJEFECAJA
GO
