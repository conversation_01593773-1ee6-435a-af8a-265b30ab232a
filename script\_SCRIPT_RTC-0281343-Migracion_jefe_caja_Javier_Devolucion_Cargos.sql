/**************** CREACION DE STORE PROCEDURES *********************************/

/**************** STORE PROCEDURE SpHisDevoluciones ****************************/
USE [HOSPITAL]
GO

/****** Object:  StoredProcedure [dbo].[SpHisDevoluciones]    Script Date: 24/05/2023 11:18:20 a.m. ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/****************************************************************************/
/*	CREADO POR: <PERSON>	Numero de Rational: RTC281343		*/
/*	FECHA CREACIÓN: 21/12/2022												*/
/*	PRODUCTO: <PERSON><PERSON>													*/
/**************************** DESCRIPCION ***********************************/
/*	Este Procedure tiene como objetivo consultar ordenes y realizar validaciones para devoluciones */
/****************************************************************************/

/****************************************************************************/
/*	MODIFICADO POR: Javier Castillo 	Numero de Rational: RTC281343		*/
/*	FECHA MODIFICACION: 11/04/2022												*/
/*	PRODUCTO: Jefe de Caja													*/
/**************************** DESCRIPCION ***********************************/
/*	Se agrego la logica para crear las devoluciones de ordenes */
/****************************************************************************/

-- EXEC SpHisDevoluciones 'C', '1', 0, 'MED', 'LA1', '495963', 'MED', '64707'
CREATE PROCEDURE [dbo].[SpHisDevoluciones](
	-- PARAMETROS DEFECTO 
	-- ========================================================== 
	@IOpcion VARCHAR(1) = '', 
	-- C = Consulta 
	-- I = Ingreso 
	-- A = Actualizar 
	-- E = Eliminación 
	@ISubOpcion VARCHAR(2) = '', 
	-- 1 = Orden
	-- 2 = Cargos
	@IHospital INT = 0,
	@IEmpresa VARCHAR(3) = '',
	@ITipo VARCHAR(3) = '',
	@IOrden VARCHAR(10) = '',
	@ISerieAdmision char(1) = null,
	@INumeroAdmision int = null,
	@IEmpresaUnificadora VARCHAR(3) = '',
	@IEmpresaBodega VARCHAR(3) = 'BOD',
	@IVerCargosCero INT = 0,
	@IUsuario VARCHAR(6) = '',
	@IProductos VARCHAR(MAX) = '',
	@IBodega INT = 0,
	@ITiposDevolucion VARCHAR(MAX) = 'OP,FC,FA',
	@IEsExtraordinario bit = 0
)
AS
BEGIN
	DECLARE
		@ErrRaise VARCHAR(99) = '',
		@ErrRaiseNum Int = 0,

		@CodigoUnidadDevolver VARCHAR(6) = '',
		@Permiso VARCHAR(1) = '',
		@CuantosFacturados INT = 0,
		@CargosSerieAdmision VARCHAR(1) = '',
		@CargosAdmision VARCHAR(20) = '',
		@OrdenesSerieAdmision VARCHAR(1) = '',
		@OrdenesAdmision VARCHAR(20) = '',
		@OrdenesStatus VARCHAR(1) = '',
		@OrdenesBodega VARCHAR(15) = '',
		@OrdenesStatusEstadisticas VARCHAR(1) = '',
		@OrdenesCierreSiniestralidad VARCHAR(14) = '',
		@OrdenesImagenesUP VARCHAR(1) = '',
		@AdmisionesPaciente VARCHAR(15) = '',
		@AdmisionesStatus VARCHAR(1) = '',
		@AdmisionesNombre VARCHAR(30) = '',
		@AdmisionesApellido VARCHAR(30) = '',
		@AdmisionesApellidoCasada VARCHAR(30) = '',
		@AdmisionesNivelPrecios INT = 0,
		@AdmisionesSerie VARCHAR(1) = '',
		@AdmisionesCodigo VARCHAR(15) = '',
		@AdmisionesIdAfiliado VARCHAR(17) = '',
		@AdmisionesFechaCierreSiniestralidad SMALLDATETIME = '',
		@RadiologiaInformesCuantos INT = 0,
		@DevolucionCargosSiguiente INT = 0,
		@WIndiceProducto INT = 0,
		@WCantidadCargos INT = 0,
		@WProductoActual VARCHAR(MAX) = '',
		@WProductoValor MONEY,
		@w_SalidaSp varchar(max),
		@WBodegaSucursal varchar(3),
		@WVenaPaciente varchar(12),
		@WCompraFutura varchar(12),
		@WPorcGanancia smallmoney,
		@SalidaCodigo int = 0 ,
		@SalidaDescripcion varchar(max) = '' ,
		@SalidaTipoError int = 0,
		@WComplementos char(1)='N',
		@WProductosComplementos VARCHAR(max)='',
		@WEmpresaReal VARCHAR(3) = '';

	SET NOCOUNT ON

	IF @IOpcion = 'C'
		BEGIN
			IF @ISubOpcion = '1'
				BEGIN
					SELECT 
						@CargosSerieAdmision = SerieAdmision,
						@CargosAdmision = Admision
					FROM Cargos 
					WHERE Empresa = @IEmpresaUnificadora
					AND TipoOrden = @ITipo
					AND Orden = @IOrden
					GROUP BY
						SerieAdmision
						,Admision

					IF EXISTS (
						SELECT 
							*
						FROM Cargos 
						WHERE Empresa = @IEmpresaUnificadora
						AND TipoOrden = @ITipo
						AND Orden = @IOrden
						AND FacRec IS NOT NULL
					)
						BEGIN
							SELECT 1 AS codigo, 'Esta orden ya fue facturada' AS descripcion, 1 AS tipo_error

							RETURN
						END
					ELSE
						BEGIN
							SELECT 
								@OrdenesSerieAdmision = SerieAdmision,
								@OrdenesAdmision = Admision,
								@OrdenesStatus = Status,
								@OrdenesBodega = Bodega,
								@OrdenesStatusEstadisticas = StatusEstadisticas,
								@OrdenesCierreSiniestralidad = CierreSiniestralidad,
								@OrdenesImagenesUP = ISNULL(ImagenesUP,0)
							FROM Ordenes 
							WHERE Empresa = @IEmpresaUnificadora 
							AND Tipo = @ITipo
							AND Codigo = @IOrden

							IF(@OrdenesImagenesUP != 0)
								BEGIN
									SELECT 1 AS codigo, 'Esta orden Ya tiene Imágenes de Radiología Grabadas' AS descripcion, 1 AS tipo_error

									RETURN
								END
							ELSE
								BEGIN
									Select
										@AdmisionesPaciente = A.Paciente, 
										@AdmisionesStatus = A.Status, 
										@AdmisionesNombre = LTRIM(RTRIM(P.Nombre)), 
										@AdmisionesApellido = LTRIM(RTRIM(P.Apellido)), 
										@AdmisionesApellidoCasada = LTRIM(RTRIM(P.ApellidoCasada)), 
										@AdmisionesNivelPrecios = A.NivelPrecios,
										@AdmisionesSerie = A.Serie, 
										@AdmisionesCodigo = A.Codigo, 
										@AdmisionesIdAfiliado = A.IdAfiliado, 
										@AdmisionesFechaCierreSiniestralidad = A.FechaCierreSiniestralidad
									FROM Admisiones A LEFT JOIN Pacientes P
									ON (A.Empresa = P.Empresa AND A.Paciente = P.Codigo)
									WHERE A.Empresa = @IEmpresaUnificadora
									AND A.Serie = @CargosSerieAdmision
									AND A.Codigo = @CargosAdmision

									IF((@AdmisionesStatus = 'D') OR (@AdmisionesIdAfiliado IS NOT NULL AND @AdmisionesFechaCierreSiniestralidad IS NOT NULL))
										BEGIN
											IF(@AdmisionesFechaCierreSiniestralidad IS NOT NULL)
												BEGIN
													SELECT 1 AS codigo, 'Esta admisión ya registró su Siniestralidad, no se puede modificar' AS descripcion, 1 AS tipo_error

													RETURN
												END
											ELSE
												BEGIN
													SELECT 1 AS codigo, 'La admisión de esta orden está desactivada, no se puede modificar' AS descripcion, 1 AS tipo_error

													RETURN
												END
										END
									ELSE
										BEGIN
											IF((@OrdenesStatusEstadisticas = 'A') OR (@AdmisionesIdAfiliado IS NOT NULL AND @OrdenesCierreSiniestralidad IS NOT NULL))
												BEGIN
													IF(@OrdenesCierreSiniestralidad IS NOT NULL)
														BEGIN
															SELECT 1 AS codigo, 'Esta orden ya registró su Siniestralidad, no se puede modificar' AS descripcion, 1 AS tipo_error

															RETURN
														END
													ELSE
														BEGIN
															SELECT 1 AS codigo, 'Esta orden ya fue incluida en las estadásticas, no se puede modificar' AS descripcion, 1 AS tipo_error

															RETURN
														END
												END
											ELSE
												BEGIN
													SELECT 
														@RadiologiaInformesCuantos = COUNT(*)
													FROM RadiologiaInformes
													WHERE Empresa= @IEmpresaUnificadora
													AND Impreso IS NOT NULL
													AND TipoOrden = @ITipo
													AND Orden = @IOrden

													IF(@RadiologiaInformesCuantos > 0)
														BEGIN
															SELECT 1 AS codigo, 'Esta orden ya posee informes de diagnóstico impresos, No se puede modificar dicha orden...' AS descripcion, 1 AS tipo_error

															RETURN
														END
													ELSE
														BEGIN
															IF EXISTS(
																SELECT
																	TipoOrden,
																	Orden,
																	Examen
																FROM LaboratorioResultados
																WHERE Empresa = @IEmpresaUnificadora
																AND TipoOrden = @ITipo 
																AND Orden = @IOrden
															)
																BEGIN
																	SELECT 1 AS codigo, 'Esta orden ya posee resultados de Laboratorio grabados, No se puede modificar dicha orden...' AS descripcion, 1 AS tipo_error

																	RETURN
																END
															ELSE
																BEGIN
																	IF EXISTS(
																		SELECT 
																			*
																		FROM Cargos c 
																		INNER JOIN LotesProntoPagoDetalle d
																			ON (d.Empresa = @IEmpresa AND d.SerieAdmision = c.SerieAdmision 
																			AND d.Admision = c.Admision AND d.Medico = c.Ajeno)
																		WHERE c.Empresa = @IEmpresaUnificadora
																		AND c.TipoOrden = @ITipo 
																		AND c.Orden = @IOrden
																	)
																		BEGIN
																			SELECT 1 AS codigo, 'Esta orden ya forma parte de un lote de facturas Pronto Pago, No puede anular dicha orden...' AS descripcion, 1 AS tipo_error

																			RETURN
																		END
																	ELSE
																		BEGIN
																			IF(@AdmisionesApellidoCasada != '')
																				BEGIN
																					SELECT 0 as codigo, 'Validaciones correctas...' AS descripcion, 0 AS tipo_error, @OrdenesBodega AS bodega, @AdmisionesNombre + ' ' + @AdmisionesApellidoCasada + ' ' + @AdmisionesApellido AS nombreCompleto, @AdmisionesSerie AS SerieAdmision, @AdmisionesCodigo AS CodigoAdmision

																					RETURN
																				END
																			ELSE
																				BEGIN
																					SELECT 0 as codigo, 'Validaciones correctas...' AS descripcion, 0 AS tipo_error, @OrdenesBodega AS bodega, @AdmisionesNombre + ' ' + @AdmisionesApellido AS nombreCompleto, @AdmisionesSerie AS SerieAdmision, @AdmisionesCodigo AS CodigoAdmision

																					RETURN
																				END
																		END
																END
														END
												END
										END
								END
						END
				END
			ELSE IF @ISubOpcion = '2'
				BEGIN
				 SELECT c.TipoOrden,
								c.Orden,
								c.Linea,
								c.Producto,
								p.Nombre,
								c.Categoria,
								p.Tipo,
								Convert(INT,c.Cantidad) Cantidad,
								Convert(DECIMAL(10,2),c.PrecioUnitario) PrecioUnitario,
								Convert(DECIMAL(10,2),c.Valor) Valor,
								Convert(DECIMAL(10,2),c.Costo) Costo,
								c.UnidadMedida, 
								c.Factor, 
								c.SubNivelPrecios SubNivel
				   FROM Cargos c left join inventario..productos p on (c.Empresa = p.Empresa and c.Producto=p.Codigo)
					WHERE c.Empresa = @IEmpresaUnificadora 
						and c.TipoOrden = @ITipo
						and c.Orden = @IOrden
						and c.SerieAdmision = @ISerieAdmision
						and c.Admision = @INumeroAdmision				
						and (@IVerCargosCero = 1 or c.Cantidad > 0)
				END
			ELSE
				BEGIN
					SELECT 1 AS codigo, 'No se ha encontrado la Subopcion' AS descripcion, 1 AS tipo_error

					RETURN
				END
		END
	ELSE IF @IOpcion = 'I'
		BEGIN
			IF @ISubOpcion = '1'
				BEGIN
					BEGIN TRAN
					BEGIN TRY
					
					SELECT keyCount linea, item TipoOrden
					  INTO #TiposFarmacia
					  FROM Contadb..SplitStringIndex(@ITiposDevolucion,',')

					IF ISNULL((Select count(*) from #TiposFarmacia where TipoOrden = Substring(@ITipo,1,2)),0) > 0
					BEGIN
						
							IF ISNULL((SELECT count(*) from Hospital..ControlMedicamentos
												  Where  SerieOrden = @ITipo and Orden = @IOrden),0) > 0
								BEGIN
									SELECT 1 AS codigo, 'ORDEN INGRESADA POR VALE ELECTRONICO NO ES POSIBLE LA DEVOLUCION MANUAL DEL PRODUCTO' AS descripcion, 1 AS tipo_error
									ROLLBACK TRAN
									RETURN
								END												
					END

					SELECT @WEmpresaReal = ISNULL(EmpresaReal,'')
						FROM ORDENES 
					 WHERE empresa = @IEmpresaUnificadora 
					   and Tipo = @ITipo 
						 and Codigo = @IOrden 

					IF @WEmpresaReal = ''
					BEGIN
						SET @WEmpresaReal = @IEmpresa
					END

					SELECT @DevolucionCargosSiguiente = Siguiente
					  FROM Correlativo_DevolucionCargos
					 WHERE Empresa = @IEmpresaUnificadora

				  UPDATE Correlativo_DevolucionCargos
						 SET  Siguiente = @DevolucionCargosSiguiente +1
					 where Empresa  = @IEmpresaUnificadora

				--Inicio modificacion de productos--
					SELECT keyCount linea, item producto
						INTO #PRODUCTOS
						FROM Contadb..SplitStringIndex(@IProductos,';')
					
					SELECT @WIndiceProducto = CONVERT(tinyint,min(linea))
						FROM #PRODUCTOS

					WHILE @WIndiceProducto <> 0
						 BEGIN

							 select @WProductoActual = producto
								 from #PRODUCTOS
								where linea = @WIndiceProducto
							
							IF OBJECT_ID('tempdb..#ListaProductos') IS NOT NULL 
								BEGIN
											INSERT INTO #ListaProductos
											select [1] Linea,	[2] Codigo,[3] Cantidad,
														 [4] Precio,[5] Valor,[6] UnidadMedida,
														 [7] Factor,[8] SubNivel,[9] Costo, [10] DevuelveTodo
												from
											(
											select  keyCount linea, item valor
												from Contadb..SplitStringIndex(@WProductoActual,',')
											)
											inicio pivot
											(
												max(valor)
												for linea in ([1],[2],[3],[4],[5],[6],[7],[8],[9],[10])
											) pivottable
								END
								ELSE
								BEGIN
											select [1] Linea,	[2] Codigo,[3] Cantidad,
														 [4] Precio,[5] Valor,[6] UnidadMedida,
														 [7] Factor,[8] SubNivel,[9] Costo, [10] DevuelveTodo		
												INTO #ListaProductos
												from
											(
											select item linea, keyCount valor
												from Contadb..SplitStringIndex(@WProductoActual,',')
											)
											inicio pivot
											(
												max(linea)
												for valor in ([1],[2],[3],[4],[5],[6],[7],[8],[9],[10])
											) pivottable
								END


							 select @WIndiceProducto = CONVERT(tinyint,min(linea))
								 from #PRODUCTOS
								where linea > @WIndiceProducto
						 END

						 SET @WIndiceProducto = 0

						 SELECT @WIndiceProducto = count(*)
							 FROM Hospital..His_honorarios
							WHERE TipoOrden=@ITipo and Orden=@IOrden
							  and Linea in (Select Linea from #ListaProductos)
								and estado_honorarios <> 'A'

							--VALIDACION HONORARIOS
						 IF @WIndiceProducto > 0 
						 BEGIN
							SELECT 1 AS codigo, 'Se encuentran cargos en uso por honorarios' AS descripcion, 1 AS tipo_error
							ROLLBACK TRAN
							RETURN
						 END

						 --VALIDACION PRODUCTOS COMPLEMENTOS
						 SELECT @WComplementos = ISNULL(ValidarDevolucionComplementos,'N') FROM Defaults
							WHERE Empresa = @IEmpresaUnificadora
						
						 IF @WComplementos = 'S'
						 BEGIN
							 SELECT DISTINCT ProductoPrimario as ProductoComplemento
								 INTO #Complementos
								 FROM Inventario..Complementarios C INNER JOIN #ListaProductos L 
									 ON (C.ProductoPrimario = L.Codigo)
								WHERE C.Empresa = @IEmpresaUnificadora and C.Activo = 'S'

								IF (SELECT COUNT(*) FROM #Complementos) > 0
								BEGIN

									SELECT @WProductosComplementos = coalesce(@WProductosComplementos + ',', '') +  convert(varchar(12),ProductoComplemento)
										FROM #Complementos
										
									SELECT 1 AS codigo, CONCAT('Los productos ',stuff(RTRIM(@WProductosComplementos),1,1,''),' son complementarios, no se pueden devolver') AS descripcion, 1 AS tipo_error
									ROLLBACK TRAN
									RETURN 
								END

								INSERT INTO #Complementos
								SELECT DISTINCT ProductoSecundario as ProductoComplemento								 
								 FROM Inventario..Complementarios C INNER JOIN #ListaProductos L 
									 ON (C.ProductoSecundario = L.Codigo)

								IF (SELECT COUNT(*) FROM #Complementos) > 0
								BEGIN
								  SELECT @WProductosComplementos = coalesce(@WProductosComplementos + ',', '') +  convert(varchar(12),ProductoComplemento)
										FROM #Complementos
										
									SELECT 1 AS codigo, CONCAT('Los productos ',stuff(RTRIM(@WProductosComplementos),1,1,''),' pertenecen a un producto complementario, no se pueden devolver') AS descripcion, 1 AS tipo_error
									
									ROLLBACK TRAN
									RETURN 
								END
						 END




						 Insert Into Devoluciones (Empresa, TipoOrden, Orden, Linea, Fecha, Producto,
																			 Cantidad, PrecioUnitario, UnidadMedida, Valor, 
																			 SerieAdmision, Admision, Usuario, Correlativo, Costo, EmpresaReal)
						 Select @IEmpresaUnificadora, @ITipo, @IOrden, Linea, GetDate(), Codigo,
										Cantidad,Precio,UnidadMedida,Valor,
										@ISerieAdmision, @INumeroAdmision, @IUsuario,@DevolucionCargosSiguiente,Costo, @WEmpresaReal
							 From #ListaProductos		

						 -- Inicio Cargos Modificados
						 UPDATE C 
						    SET C.Cantidad = C.Cantidad - L.Cantidad, 
								    C.Valor = C.Valor - L.Valor
							 FROM Cargos C inner join #ListaProductos	L ON (C.linea = L.linea and C.Producto = L.Codigo)					 
              WHERE C.Empresa = @IEmpresaUnificadora and C.TipoOrden = @ITipo 
							  and C.Orden = @IOrden and L.DevuelveTodo = 'N' 

						 SET @WIndiceProducto = 0

						 SELECT @WIndiceProducto = CONVERT(tinyint,min(Linea))
							 FROM #ListaProductos
						  WHERE DevuelveTodo = 'N'
						 
						 WHILE @WIndiceProducto <> 0
								BEGIN
									SET @WProductoValor = (SELECT Valor FROM #ListaProductos WHERE Linea = @WIndiceProducto)

									exec @w_SalidaSp = Hospital..sp_Ajenos_Honorarios_Editar_Interno  @I_Tipo=1 ,
																															@I_Empresa=@IEmpresaUnificadora,
																															@I_TipoOrden=@ITipo,
																															@I_Orden=@IOrden,
																															@I_Linea=@WIndiceProducto,
																															@I_Campo='Valor',
																															@I_Valor= @WProductoValor,
																															@o_Codigo = @SalidaCodigo  out,
																															@o_Descripcion = @SalidaDescripcion  out,
																															@o_TipoError = @SalidaTipoError out

									IF @SalidaTipoError = 1 
									BEGIN
										SELECT @SalidaCodigo AS codigo, @SalidaDescripcion AS descripcion, @SalidaTipoError AS tipo_error 
										ROLLBACK TRAN
										RETURN
									END

									select @WIndiceProducto = CONVERT(tinyint,min(Linea))
										from #ListaProductos
	  							 where linea > @WIndiceProducto AND DevuelveTodo = 'N'
								END
						 -- Fin Cargos Modificados						 

						UPDATE E
						   SET E.Existencia = E.Existencia + L.Cantidad
							FROM INVENTARIO..ExistenciasBodega E INNER JOIN #ListaProductos L ON (E.Producto = L.Codigo)
             WHERE E.Empresa = @IEmpresaBodega and E.Bodega = @IBodega 						
								
				--Fin Modificacion de productos--

					  SELECT @WBodegaSucursal =  EmpresaSucursal
						  FROM CONTADB..EmpresasDefaults
						 WHERE Empresa = @IEmpresaBodega

						SELECT @WVenaPaciente = VenaPaciente, @WCompraFutura = CompraFutura, @WPorcGanancia = PorcGanancia
							FROM Contabilidad
						 WHERE Empresa = @WEmpresaReal


						Insert Into Inventario..InvCargosDetalle
									(Empresa, TipoOperacion, SerieDocumento, Documento, Linea, 
										Producto, Cantidad, CostoUnitario, CostoUnitarioHospital, 
										Categoria, CostoTotal, 
										CostoTotalHospital, SerieDocumentoOrigen, DocumentoOrigen, 
										EmpresaFacReq, SerieFactura, Factura, FechaTransaccion, 
										Debe, Haber, BodegaFuente, 
										BodegaDestino, Departamento,EmpresaReal,LineaDocumento)
						Select  @WBodegaSucursal, 'OR' as Tipo , 'D' AS SerieDocumento,C.Correlativo AS Documento, Row_Number() Over (Order by C.Empresa, P.Categoria, C.TipoOrden, C.Orden) as Linea,
						C.Producto AS Producto, Cantidad = C.Cantidad, Cast(C.Costo As money) As CostoUnitario, Cast(C.Costo As Money) * (1+(@WPorcGanancia /100.000000)) As CostoUnitarioHospital,
						Cargos.Categoria, (Cast(C.Costo As money) * C.Cantidad)   As CostoTotal, 
						Cast(C.Costo As Money) *C.Cantidad* (1+(@WPorcGanancia /100.000000)) As CostoTotalHospital, C.Tipoorden AS SerieDocumentoOrigen, C.ORDEN AS DocumentoOrigen,
						null As empresaFacReq, null As SerieFactura, null As Factura, GetDate() As FechaTransaccion,
						B.CuentaLocal As Debe ,  RTRIM(ccc.CuentasCostoSuministro)  As Haber,NULL  As BodegaFuente,
						ISNULL(x.BodegaDefault, 99) As BodegaDestino, Departamento = NULL,@WEmpresaReal, C.linea
						From Hospital..Devoluciones C
						Inner Join Inventario..Productos P On (C.Empresa = P.Empresa and C.Producto=P.Codigo and P.Tipo = 'P')
						Inner Join Hospital..Cargos On (C.TipoOrden = Cargos.TipoOrden and C.Orden = Cargos.Orden 
																						and C.Empresa = Cargos.Empresa and C.Producto = Cargos.Producto)
						Left Join Hospital..OrdenesTipos x ON (c.Empresa = x.Empresa and c.TipoOrden = x.Codigo)
						Inner Join Inventario..Bodegas B ON (B.Empresa = @IEmpresaBodega  and  B.Codigo = ISNULL(x.BodegaDefault, 99))
						INNER JOIN Inventario..CategoriaCuentasCostos ccc ON (ccc.Empresa = @WBodegaSucursal  AND ccc.Categoria = Cargos.CATEGORIA 
																															AND ccc.Hospital = B.HOSPITAL)
						Where C.Empresa = @IEmpresaUnificadora AND C.Tipoorden = @ITipo   AND C.ORDEN = @IOrden  AND C.CORRELATIVO =  @DevolucionCargosSiguiente

						Insert Into Inventario..InvCargosDetalle
											(Empresa, TipoOperacion, SerieDocumento, Documento, Linea, 
											 Producto, Cantidad, CostoUnitario, CostoUnitarioHospital, 
											 Categoria, CostoTotal, 
                       CostoTotalHospital, SerieDocumentoOrigen, DocumentoOrigen, 
											 EmpresaFacReq, SerieFactura, Factura, FechaTransaccion,
											 Debe, Haber, BodegaFuente, 
                       BodegaDestino, Departamento,EmpresaReal,LineaDocumento)
						Select Empresa = @WEmpresaReal, TipoOperacion = 'OR' ,SerieDocumento =  'D' ,Documento = C.Correlativo , Row_Number() Over (Order by C.Empresa, P.Categoria, C.TipoOrden, C.Orden) As Linea,
						Producto = C.Producto , Cantidad = C.Cantidad, Cast(C.Costo As money) As CostoUnitario, Cast(C.Costo As Money) * (1+(@WPorcGanancia /100.000000)) As CostoUnitarioHospital,
						Cargos.Categoria, (Cast(C.Costo As money) * C.Cantidad)   As CostoTotal, 
						Cast(C.Costo As Money) *C.Cantidad* (1+(@WPorcGanancia /100.000000)) As CostoTotalHospital, SerieDocumentoOrigen = C.Tipoorden , DocumentoOrigen =  C.ORDEN,
						empresaFacReq = null, SerieFactura = null, Factura = null, FechaTransaccion =  GetDate() ,
						Debe = @WCompraFutura , Haber = @WVenaPaciente ,    BodegaFuente = NULL ,ISNULL(x.BodegaDefault, 99) As BodegaDestino, Departamento = NULL,@WEmpresaReal, C.linea
						From Hospital..Devoluciones C
						Inner Join Inventario..Productos P On (C.Empresa = P.Empresa and C.Producto=P.Codigo and P.Tipo = 'P')
						Inner Join Hospital..Cargos On (C.TipoOrden = Cargos.TipoOrden and C.Orden = Cargos.Orden 
																						and C.Empresa = Cargos.Empresa and C.Producto = Cargos.Producto)
						Left Join Hospital..OrdenesTipos x ON (c.Empresa = x.Empresa and c.TipoOrden = x.Codigo)
						Inner Join Inventario..Bodegas B ON (B.Empresa = @IEmpresaBodega  and  B.Codigo = ISNULL(x.BodegaDefault, 99))
						Where C.Empresa = @IEmpresaUnificadora AND C.Tipoorden = @ITipo AND C.ORDEN = @IOrden  AND C.CORRELATIVO = @DevolucionCargosSiguiente  


						-- Inicio Cargos Eliminados --
						 DELETE FROM Cargos
							WHERE Empresa = @IEmpresaUnificadora and TipoOrden = @ITipo 
							  and Orden = @IOrden and Linea IN (SELECT Linea from #ListaProductos WHERE DevuelveTodo = 'S')
						
								SET @WIndiceProducto = 0

						 SELECT @WIndiceProducto = CONVERT(tinyint,min(Linea))
							 FROM #ListaProductos
						  WHERE DevuelveTodo = 'S'
						 
						 WHILE @WIndiceProducto <> 0
							BEGIN
								exec @w_SalidaSp = Hospital..sp_Ajenos_Honorarios_Editar_Interno  @I_Tipo=1 ,@I_Empresa=@IEmpresaUnificadora,
																														@I_TipoOrden=@ITipo,
																														@I_Orden=@IOrden,@I_Linea=@WIndiceProducto,
																														@I_Campo='Estado_Cargo',@I_Valor='E',
																														@o_Codigo = @SalidaCodigo  out,
																														@o_Descripcion = @SalidaDescripcion  out,
																														@o_TipoError = @SalidaTipoError out
								
								IF @SalidaTipoError = 1 
								BEGIN
									SELECT @SalidaCodigo AS codigo, @SalidaDescripcion AS descripcion, @SalidaTipoError AS tipo_error 
									ROLLBACK TRAN
									RETURN
								END

								exec @w_SalidaSp = Hospital..sp_Ajenos_Honorarios_Editar_Interno  @I_Tipo=1 ,@I_Empresa=@IEmpresaUnificadora,
																																					@I_TipoOrden=@ITipo,
																																					@I_Orden=@IOrden,@I_Linea=@WIndiceProducto,
																																					@I_Campo='Estado_Interpretacion',@I_Valor='E',
																																					@o_Codigo = @SalidaCodigo  out,
																																					@o_Descripcion = @SalidaDescripcion  out,
																																					@o_TipoError = @SalidaTipoError out

								IF @SalidaTipoError = 1 
								BEGIN
									SELECT @SalidaCodigo AS codigo, @SalidaDescripcion AS descripcion, @SalidaTipoError AS tipo_error 
									ROLLBACK TRAN
									RETURN
								END

								select @WIndiceProducto = CONVERT(tinyint,min(Linea))
									from #ListaProductos
	  							where linea > @WIndiceProducto AND DevuelveTodo = 'S'
							END
						-- FIN cargos eliminados

					
						UPDATE  Hospital..Devoluciones
						SET     CostoUltimo =  Isnull( (Select P.CostoUltimo from Inventario..Productos P Where P.empresa = @IEmpresaBodega and P.codigo = Producto) , 0)
						WHERE   (Empresa = @IEmpresaUnificadora) AND (Correlativo = @DevolucionCargosSiguiente) AND TipoOrden = @ITipo AND Orden = @IOrden


					 SELECT @WCantidadCargos =  count(*)
						 FROM Cargos 
						WHERE Empresa = @IEmpresaUnificadora AND TipoOrden = @ITipo AND Orden = @IOrden	 

						IF @WCantidadCargos = 0
							BEGIN
								UPDATE Ordenes
									 SET Status = 'A', StatusEstadisticas = 'A'
								 WHERE Empresa = @IEmpresaUnificadora and Tipo = @ITipo and Codigo = @IOrden
							END

						IF OBJECT_ID('tempdb..#ListaProductos') IS NOT NULL 
							DROP TABLE #ListaProductos
						IF OBJECT_ID('tempdb..#PRODUCTOS') IS NOT NULL 
							DROP TABLE #PRODUCTOS
						IF OBJECT_ID('tempdb..#Complementos') IS NOT NULL 
							DROP TABLE #Complementos
						IF OBJECT_ID('tempdb..#TiposFarmacia') IS NOT NULL 
							DROP TABLE #TiposFarmacia											
						
						SELECT 0 AS codigo, 'Finalizo devolución de cargos' AS descripcion, 0 AS tipo_error
						COMMIT TRAN
					END TRY
					BEGIN CATCH
						ROLLBACK TRAN;
						Set @ErrRaise = ERROR_MESSAGE()
						Set @ErrRaiseNum = ERROR_STATE()
						RAISERROR(@ErrRaise,16,@ErrRaiseNum)
					END CATCH
				END
		END
	ELSE
		BEGIN
			SELECT 1 AS codigo, 'No se ha encontrado la opcion' AS descripcion, 1 AS tipo_error

			RETURN
		END
END
GO

/**************** FIN STORE PROCEDURE SpHisDevoluciones ***********************/
/***************** FIN CREACION DE STORE PROCEDURES ***********************************/


/***************** INICIO MODIFICACION DE STORE PROCEDURES ***********************************/
/***************** INICIO MODIFICACION DE sp_Ajenos_Honorarios_Editar_Interno ***********************************/
USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[sp_Ajenos_Honorarios_Editar_Interno]    Script Date: 11/04/2023 09:17:13 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- ==========================================================
-- Author:  Walter Ronquillo
-- Create date: 17.05.2021
-- Description:	Actualiza los campos de un cargo, cita, cupón definido
-- 21.04.2022 - Se agrega manejo de cuenta cuando el cambio es por médico
-- ==========================================================
-- ==========================================================
-- Author: Javier Castillo
-- Create date: 07.02.2023
-- Description:	Se agregan parametros de salida para usar internamente el store procedure
-- dentro del sp de anulacion de cargos 
-- ==========================================================
-- ==========================================================
-- Author: Javier Castillo
-- Create date: 11.04.2023
-- Description:	Se cambio el codigo de error cuando no se encuentre el cargo para que continue el proceso
-- ==========================================================
ALTER PROCEDURE [dbo].[sp_Ajenos_Honorarios_Editar_Interno]
@i_Tipo tinyint,
@i_Empresa char(3) 		= null,
@i_TipoOrden char(3) 	= null,
@i_Orden varchar(15)	= null,
@i_Linea varchar(3) 	= null,
@i_Campo varchar(40)	= null,
@i_Valor varchar(50)	= null,
@o_Codigo int = 0 out,
@o_Descripcion varchar(max) = '' out,
@o_TipoError int = 0 out
AS
BEGIN
	DECLARE
		@wTempAjenoManejoCuenta smallmoney = NULL,
		@wTempAjenoManejoCuentaAjena smallmoney = NULL,
		@w_ErrRaise		Varchar(MAX)	= '',
		@w_ErrRaiseNum 	INT			= 0,
		@SQL nvarchar(Max)		= null,
		@i_Existencia int		= null

	BEGIN TRAN
	BEGIN TRY
	SET NOCOUNT ON;
	

	-- ========================================================== 
	-- CARGOS
	-- ==========================================================
	IF @i_Tipo = 1 
	BEGIN
		IF @i_Empresa is not null AND @i_TipoOrden is not null AND @i_Orden is not null AND @i_Linea is not null 
		BEGIN 			
			-- CONVIRTIENDO LINEA Y SI NO SE ASIGNA SE OMITE
			DECLARE @w_Linea varchar(50) = '';
			SELECT @i_Linea = CONVERT(int, @i_Linea) ;
			IF (@i_Linea > 0) set @w_Linea = 'and Linea = '+@i_Linea
			-- BUSCANDO EXISTENCIA
	    	SELECT @i_Existencia = count(*) FROM His_Honorarios	WHERE EmpresaUnificadora = @i_Empresa and TipoOrden = @i_TipoOrden and Orden = @i_Orden and (@i_Linea = 0 OR Linea = @i_Linea);
			IF @i_Existencia >0
			BEGIN
				IF (charindex( UPPER(RTRIM(LTRIM(@i_Campo))),'TIPO,EMPRESA,TIPOORDEN,ORDEN,LINEA') = 0)
				BEGIN 
					-- INTENTANDO GUARDAR VALOR NUMERICO
					BEGIN TRY
						SET @SQL = 'Update His_Honorarios SET  '+@i_Campo+' = '+ @i_Valor + ' WHERE EmpresaUnificadora ='''+@i_Empresa+''' and TipoOrden = '''+@i_TipoOrden+''' and Orden = '+@i_Orden + @w_Linea+';'
						EXEC (@SQL);
					END TRY
					BEGIN CATCH
					--INTENTANDO GUARDAR VALOR STRING
						SET @SQL = 'Update His_Honorarios SET  '+@i_Campo+' = '''+ @i_Valor + ''' WHERE EmpresaUnificadora ='''+@i_Empresa+''' and TipoOrden = '''+@i_TipoOrden+''' and Orden = '+@i_Orden + @w_Linea+';'
						EXEC (@SQL);
					END CATCH
					
					
					-- Validando si es cambio es de médico
					IF @i_Campo = 'Ajeno'
					BEGIN
						SELECT @wTempAjenoManejoCuenta = ManejoCuenta, @wTempAjenoManejoCuentaAjena = ManejoCuentaAjena FROM Ajenos a where a.Codigo = @i_Valor;		
					
						Update
							His_Honorarios
						SET
							AJE_ManejoCuenta = @wTempAjenoManejoCuenta,
							AJE_ManejoCuentaAjena = @wTempAjenoManejoCuentaAjena
						WHERE
							EmpresaUnificadora = @i_Empresa
							and TipoOrden = @i_TipoOrden
							and Orden = @i_Orden
							and Linea = @i_Linea;
					END
					
					SELECT @o_Codigo = 0 , @o_Descripcion =  'Se actualizado '+ CONVERT(varchar, @i_Existencia) +' registro.' , @o_TipoError = 0
					COMMIT TRAN;
				END
				ELSE
				BEGIN
					SELECT @o_Codigo = 0, @o_Descripcion =  'No es posible editar este campo' , @o_TipoError = 1 ;
					COMMIT TRAN;
				END
			END
			ELSE 
			BEGIN 
				SELECT @o_Codigo = 0, @o_Descripcion =  'No se encontro el registro' , @o_TipoError = 2 ;
				COMMIT TRAN;
			END
		END
		ELSE
		BEGIN
			SELECT @o_Codigo = 0, @o_Descripcion =  'Campos requeridos para cargos.' , @o_TipoError = 1 ;
			COMMIT TRAN;
		END

	END
	-- ========================================================== 
	-- ! SI LA OPERACIÓN NO EXISTE
	-- ==========================================================
	ELSE 
	BEGIN
		SELECT @o_Codigo = 0, @o_Descripcion =  'Opción incorrecta' , @o_TipoError = 1 ;
		COMMIT TRAN;
	END
	
	END TRY
	BEGIN CATCH
		ROLLBACK TRAN;
		Set @w_ErrRaise = ERROR_MESSAGE()
		Set @w_ErrRaiseNum = ERROR_STATE()
		RAISERROR(@w_ErrRaise,16,@w_ErrRaiseNum)
	END CATCH
END
GO
/***************** FIN MODIFICACION DE sp_Ajenos_Honorarios_Editar_Interno ***********************************/

/***************** INICIO MODIFICACION DE SP_BITACORA ***********************************/

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[sp_Bitacora]    Script Date: 11/04/2023 09:40:11 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

ALTER PROCEDURE [dbo].[sp_Bitacora]
	@I_Operacion VarChar(20) = 'INGRESO',
	@i_IdFuncionalidad int = null,
	@i_EmpresaReal	char(3) = null,
	@i_Detalle text = null,
	@I_Usuario		INT = null,
	@I_Tabla Varchar(60) = null
AS
BEGIN	
	DECLARE
		@ErrRaise		Varchar(MAX)	= '',
		@ErrRaiseNum 	INT			= 0

	BEGIN TRAN
	BEGIN TRY
	SET NOCOUNT ON;

	IF @I_Operacion = 'CONSULTA'
	BEGIN	
		
		Select
			hs.*,
			saf.Nombre 
		FROM
			HIS_Bitacora hs
			INNER JOIN Contadb.dbo.SistemaAplicativoFuncionalidad saf ON
			hs.IdFuncionalidad = saf.IdFuncionalidad 
		where
			(@i_IdFuncionalidad is null or hs.IdFuncionalidad = @i_IdFuncionalidad)
			and (@I_Usuario is null or CorporativoCreado = @I_Usuario)
		order by 
		FechaCreado desc;
		
		COMMIT TRAN;
	
	END
	ELSE IF @I_Operacion = 'INGRESO'
	BEGIN
		
		Insert Into HIS_Bitacora (IdFuncionalidad,EmpresaReal,Detalle,CorporativoCreado,Tabla)
		values 
		(@i_IdFuncionalidad,@i_EmpresaReal,@i_Detalle,@I_Usuario,@I_Tabla);
	
		
		Select 	0 as codigo, 'Se agrego un nuevo filtro de cargos.' as descripcion, 0 as tipo_error;
		COMMIT TRAN;
	END
	ELSE IF @I_Operacion = 'WEB'
	BEGIN
		
		Insert Into HIS_Bitacora (IdFuncionalidad,EmpresaReal,Detalle,CorporativoCreado,Tabla)
		values 
		(@i_IdFuncionalidad,@i_EmpresaReal,@i_Detalle,@I_Usuario,@I_Tabla);
    Select 	0 as codigo, '.' as descripcion1, 0 as tipo_error;
		COMMIT TRAN;
	END
	ELSE IF @I_Operacion = 'INGRESO_AGENDA'
	BEGIN
		
		Insert Into HIS_Bitacora (IdFuncionalidad,EmpresaReal,Detalle,CorporativoCreado)
		values 
		(@i_IdFuncionalidad,@i_EmpresaReal,@i_Detalle,@I_Usuario);

		COMMIT TRAN;
	END
	-- ========================================================== 
	-- ! SI LA OPERACión NO EXISTE
	-- ==========================================================
	ELSE 
	BEGIN
		Select 	0 as codigo, 'Opción incorrecta' as descripcion, 1 as tipo_error;
		COMMIT TRAN;
	END

	END TRY
	BEGIN CATCH
		ROLLBACK TRAN;
		Set @ErrRaise = ERROR_MESSAGE()
		Set @ErrRaiseNum = ERROR_STATE()
		RAISERROR(@ErrRaise,16,@ErrRaiseNum)
	END CATCH
END;
GO
/***************** FIN MODIFICACION DE SP_BITACORA ***********************************/

/***************** FIN MODIFICACION DE STORE PROCEDURES ***********************************/

/***************** INICIO CREACION DE PERMISOS ***********************************/

USE HOSPITAL
GO

GRANT EXECUTE ON SpHisDevoluciones TO USRHISJEFECAJA
GO

USE HOSPITAL
GO
GRANT SELECT,UPDATE  ON HIS_Honorarios TO USRHISJEFECAJA
GO

/***************** FIN CREACION DE PERMISOS ***********************************/

