<template>
<vx-card type = "2" title="Devolución de Cargos" class="justify-center ">    
    <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
        <form ref="formModificarOrden" method="post" @submit.prevent="handleSubmit(modificar_cargos())">
            <div class="flex flex-wrap">
                <div class="flex flex-wrap w-full p-2 pb-4" style="border: 1px solid #888;">
                    <div class="md:w-full lg:w-4/12 xl:w-2/12 m-2">
                        <label>Bodega:</label> 
                        &nbsp;&nbsp;
                        <label style="color:black">{{info.bodega}}</label>                                 
                    </div>
                    <div class="md:w-full lg:w-7/12 xl:w-9/12"></div>
                    <div class="md:w-full lg:w-4/12 xl:w-2/12 m-2">
                        <label>Admision:</label> 
                        &nbsp;&nbsp;
                        <label style="color:black">{{info.admision}}</label>                                 
                    </div>
                    <div class="md:w-full lg:w-6/12 xl:w-4/12 m-2 lg:pr-6">
                        <label>Paciente:</label> 
                        &nbsp;&nbsp;
                        <label style="color:black">{{info.paciente}}</label>      
                    </div>
                    <div class="md:w-full lg:w-1/12 xl:w-5/12"></div>
                    <div class="md:w-full lg:w-4/12 xl:w-2/12 m-2">
                        <ValidationProvider rules="required" v-slot="{ errors }">
                            <SM-Buscar class="w-full" label="*Tipo de Orden" v-model="info.tipoOrden" api="app/v1_JefeCaja/ObtenerTiposOrdenesPorRol" :api_campos="['Codigo','Nombre']" :api_titulos="['Tipo de Orden','Descripción']" :api_filtro="{'Activa':0,'opcion':'C','subOpcion':'3','filtroTipoOrdenHospital':info.filtroTipoOrden.toString(),'filtroTipoOrdenGeneral':info.filtroTipoOrdenGeneral.toString()}"
                                    api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Codigo" :api_preload="true"  :mostrar_busqueda="true" :callback_buscar="cargar().datos_tipo_orden" 
                                    :danger="errors.length > 0"
                                    :danger-text="(errors.length > 0) ? errors[0] : null"/>                                
                        </ValidationProvider>            
                    </div>
                    <div class="md:w-full lg:w-6/12 xl:w-4/12 m-2 lg:pr-6">
                        <vs-input label="Descripción:" class="w-full" :value="info.descripcionOrden" disabled/>
                    </div>
                    <div class="md:w-full lg:w-10/12 xl:w-6/12 m-2">
                        <ValidationProvider rules="required" v-slot="{ errors }">
                            <vs-input label="*No. Orden" type="number"  class="w-full"  v-model="info.codigoOrden" 
                                    :danger="errors.length > 0"
                                    :danger-text="(errors.length > 0) ? errors[0] : null" v-on:change="consultar_orden"/>
                        </ValidationProvider>
                    </div>
                    <div class="md:w-full lg:w-1/12 xl:w-5/12 m-2"></div>
                </div>
                <vs-divider></vs-divider> 
                <div class="flex flex-wrap w-full  p-2 pb-4" style="border: 1px solid #888;">
                    <div class="lg:w-full xl:w-1/2 p-2">
                        <vs-table2 max-items="10" search pagination :data="cargosDevolucion" height="300px">
                            <template slot="thead">
                                <th order="Codigo" width="50px">Código</th>
                                <th order="Nombre">Nombre</th>
                                <th order="UnidadMedida" width="60px"> Unidad Medida</th>
                                <th order="Cantidad" width="140px">Cant.</th>
                                <!-- <th  width="50px">Acción</th> -->
                                <!-- <th order="Exist" width="50px">Existencia</th> -->
                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data" >
                                    <vs-td2>
                                        {{ tr.codigo }}
                                    </vs-td2>
                                    <vs-td2>
                                        <div class="col-md-8">
                                            <small>
                                                {{ tr.nombre }}
                                            </small>
                                        </div>
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.unidadMedida }}
                                    </vs-td2>
                                    <vs-td2>
                                        <div class="cantidad">
                                            <button type="button" @click="tr.cantidadDevolver++; tr.cantidad--; tr.devuelveTodo = tr.cantidad <= 0 ? 'S':'N';" :disabled="tr.cantidad<=0">+</button>
                                            <div>{{ tr.cantidadDevolver }}</div>
                                            <button v-if="tr.cantidadDevolver>1" type="button" @click="tr.cantidadDevolver--; tr.cantidad++; 
                                                                                                                 tr.devuelveTodo = 'N';">-</button>
                                            <button v-else type="button" @click="cargosDevolucion.splice(cargosDevolucion.indexOf(tr),1); tr.cantidadDevolver--; tr.cantidad++; tr.devuelveTodo = 'N'; cargos.splice(tr.indice,0,tr);">-</button>
                                        </div>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                        <!-- {{cargos}} -->
        
                    </div>
        
                    <!-- LISTADO DE CARGOS DE LA ORDEN -->
                    <div class=" lg:w-full xl:w-1/2 p-2">
                        <div>
                            <vs-table2 max-items="7" search pagination :data="cargos" height="300px">
                                <template slot="thead">
                                    <th order="Codigo" width="50px">Código</th>
                                    <th order="Nombre">Nombre</th>
                                    <th order="Cantidad" width="60px">Cantidad</th>
                                    <!-- <th order="Exist" width="50px">Existencia</th> -->
                                </template>
                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data"  @click="cargosDevolucion.unshift({...tr,
                                                                                                                                  indice: cargos.indexOf(tr),
                                                                                                                                  cantidadDevolver: 1,
                                                                                                                                  cantidad: tr.cantidad-1,
                                                                                                                                  devuelveTodo: tr.cantidad==1?'S':'N'}); 
                                                                                      cargos.splice(cargos.indexOf(tr),1);">
                                        <vs-td2>
                                            {{ tr.codigo }}
                                        </vs-td2>
                                        <vs-td2>
                                            {{ tr.nombre }}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.cantidad}}
                                        </vs-td2>
                                    </tr>
                                </template>
                            </vs-table2>
                        </div>
                    </div>

                    <div class="w-full m-2 mb-0">
                        <vs-button  
                            color="success" class="md:w-full lg:w-4/12 xl:w-1/12 m-2 mb-0"
                            :disabled="info.desactivarBotonModificacion || cargosDevolucion.length<=0"
                            @click="handleSubmit(modificar_cargos(invalid))">
                            Devolución de Cargos
                        </vs-button>

                        <vs-button class="md:w-full lg:w-4/12 xl:w-1/12 m-2 mb-0" color="warning" @click="confirmar_limpia_campos()">
                            Limpiar Campos
                        </vs-button>
                    </div>
                </div>


            </div>
        </form>
    </ValidationObserver>        
</vx-card>
</template>

<script>

export default {
    data(){
        return {
            info:{
                bodega: null,
                tipoOrden: null,
                descripcionOrden: null,
                codigoOrden: null,
                admision: null,
                paciente: null,
                desactivarBotonModificacion: true,
                filtroTipoOrden: [],
                filtroTipoOrdenGeneral: [] 
            },
            cargos: [],
            cargosDevolucion: [],
            permisos:{
				ba_devolucion: false,
                co_devolucion: false,
                cf_devolucion: false,
                cs_devolucion: false,
                cx_devolucion: false,
                em_devolucion: false,
                en_devolucion: false,
                fc_devolucion: false,
                ge_devolucion: false,
                hc_devolucion: false,
                ho_devolucion: false,
                in_devolucion: false,
                la_devolucion: false,
                me_devolucion: false,
                op_devolucion: false,
                ra_devolucion: false,
                sa_devolucion: false,
                so_devolucion: false,
                ut_devolucion: false,
                aje_devolucion: false,
                ban_devolucion: false
            }
        };
    },computed:{

    },methods:{
        consultar_orden(){            
            this.cargar_paciente()
        },
        cargar_paciente(){
            this.cargos = []
            this.cargosDevolucion = [] 

            if (this.cargar().validar_campos()) {                
                this.cargar().datos_orden()
            }else{                
                this.info.admision = null
                this.info.paciente = null  
                this.info.bodega = null
                this.info.desactivarBotonModificacion = true
            }


        },
        cargar(){
            return {
                datos_tipo_orden: (datos)=>{
                    this.info.tipoOrden = datos.Codigo
                    this.info.descripcionOrden = datos.Nombre
                    this.cargar_paciente()
                },
                datos_orden: () => {
                    // execSpHisDevoluciones 'C', '1', 0, 'MED', 'LA1', '495963', 'MED', '64707' --Empresa es EmpresaReal
                    this.axios.post('/app/v1_JefeCaja/obtener_orden_devolucion',{
                                            iOpcion: 'C',
                                            iSubOpcion: '1',                                            
                                            tipoOrden: this.info.tipoOrden,
                                            orden: this.info.codigoOrden                                            
                                        })
                              .then((resp) => {
                                                    let error 
                                                    let mensajeError
                                                    if(resp.data.json && resp.data.json.length != 0){
                                                        error = resp.data.json[0].tipo_error
                                                        mensajeError = resp.data.json[0].descripcion
                                                    }else{
                                                        error = resp.data.tipo_error
                                                        mensajeError = resp.data.descripcion
                                                    }

                                                    if(error && error != 0){
                                                        this.$vs.notify({
                                                            time: 4000,
                                                            color: '#B71C1C',
                                                            title: 'Alerta',
                                                            text: mensajeError
                                                        })

                                                        this.info.admision = null
                                                        this.info.paciente = null  
                                                        this.info.bodega = null
                                                        this.info.desactivarBotonModificacion = true  
                                                    }else if(resp.data.json.length==0){
                                                        this.$vs.notify({
                                                            time: 4000,
                                                            color: '#B71C1C',
                                                            title: 'Alerta',
                                                            text: 'No existe la orden'
                                                        })
                                                        this.info.admision = null
                                                        this.info.paciente = null  
                                                        this.info.bodega = null
                                                        this.info.desactivarBotonModificacion = true   
                                                    }
                                                    else{ //TipoOrden,CodigoOrden   
                                                        this.info.admision = resp.data.json[0].SerieAdmision + '-' + resp.data.json[0].CodigoAdmision
                                                        this.info.paciente = resp.data.json[0].nombreCompleto   
                                                        this.info.bodega = resp.data.json[0].bodega
                                                        this.info.desactivarBotonModificacion = false   
                                                        this.cargar().ObtenerCargos()                     
                                                    }     
                                                }
                                    )
                },
                ObtenerCargos:()=>{            
                    this.axios.post('/app/v1_JefeCaja/buscar_cargos_orden', {
                        iOpcion: "C",
                        iSubOpcion: "2",
                        tipoOrden: this.info.tipoOrden,
                        orden: this.info.codigoOrden,
                        serie: this.info.admision.split('-')[0],
                        noAdmision: this.info.admision.split('-')[1]                        
                    }).then((resp) => {
                            let error 
                            let mensajeError

                            if( resp.data.json.length == 0 ){
                                this.$vs.notify({
                                    time: 4000,
                                    color: '#B71C1C',
                                    title: 'Alerta',
                                    text: 'No se encontraron cargos para la admisión'
                                })
                                return
                            }

                            if(resp.data.json[0].tipo_error){
                                error = resp.data.json[0].tipo_error
                                mensajeError = resp.data.json[0].descripcion
                            }else if(resp.data.tipo_error){
                                error = resp.data.tipo_error
                                mensajeError = resp.data.descripcion
                            }

                            if(error && error != 0){
                                this.$vs.notify({
                                    time: 4000,
                                    color: '#B71C1C',
                                    title: 'Alerta',
                                    text: mensajeError
                                })
                                return
                            }

                            this.cargos = resp.data.json.map( cargo => ({
                                                                            linea: cargo.Linea,
                                                                            cantidad: cargo.Cantidad,
                                                                            codigo: cargo.Producto,
                                                                            nombre: cargo.Nombre,
                                                                            precio: cargo.PrecioUnitario,
                                                                            valor: cargo.Valor,
                                                                            unidadMedida: cargo.UnidadMedida,
                                                                            subNivel: cargo.SubNivel,
                                                                            factor: cargo.Factor,
                                                                            costo: cargo.Costo,
                                                                            indice: null
                                                                        }))
                        }
                    )
                },
                validar_campos:()=>{
                    return this.info.tipoOrden && this.info.descripcionOrden && this.info.codigoOrden                        
                }
            }
        },
        modificar_cargos(invalid){
            
            if(invalid){
                this.$vs.notify({
                                    time: 4000,
                                    title:'Alerta',
                                    text:'Validar los campos obligatorios',
                                    color: '#B71C1C',
                                })     
                return
            }

            if(!this.cargosDevolucion || this.cargosDevolucion.length == 0){
                this.$vs.notify({
                                    time: 4000,
                                    title:'Alerta',
                                    text:'Debe agregar al menos 1 cargo para devolver',
                                    color: '#B71C1C',
                                })     
                return
            }

            this.axios.post('/app/v1_JefeCaja/devolver_cargos', {
                        iOpcion: "I",
                        iSubOpcion: "1",
                        tipoOrden: this.info.tipoOrden,
                        orden: this.info.codigoOrden,
                        serie: this.info.admision.split('-')[0],
                        noAdmision: this.info.admision.split('-')[1],
                        bodega: !this.info.bodega ? 0 : this.info.bodega,
                        cargosDevolucion: this.cargosDevolucion.map( producto => producto.linea+','+producto.codigo+','+producto.cantidadDevolver+','+producto.precio+','+parseFloat(producto.precio*producto.cantidadDevolver).toFixed(2)+','+producto.unidadMedida+','+producto.factor+','+producto.subNivel+','+producto.costo+','+producto.devuelveTodo)
                    }).then((resp) => {
                            let error 
                            let mensajeError

                            if(resp.data.json && resp.data.json.length > 0 && resp.data.json[0].tipo_error){
                                error = resp.data.json[0].tipo_error
                                mensajeError = resp.data.json[0].descripcion
                            }else if(resp.data && resp.data.tipo_error){
                                error = resp.data.tipo_error
                                mensajeError = resp.data.descripcion
                            }

                            if(error && error != 0){
                                this.$vs.notify({
                                    time: 4000,
                                    color: '#B71C1C',
                                    title: 'Alerta',
                                    text: mensajeError
                                })
                                return
                            }

                            const sesion = this.$store.state.sesion;
                          
                            const bitacora = {
                                TipoOrden: this.info.tipoOrden,
                                Orden: this.info.codigoOrden,
                                SerieAdmision: this.info.admision.split('-')[0],
                                Admision: this.info.admision.split('-')[1],
                                Devoluciones: this.cargosDevolucion.map( cargo => ({Linea: cargo.linea,
                                                                                    Producto: cargo.codigo,
                                                                                    Valor: parseFloat(cargo.precio*cargo.cantidadDevolver).toFixed(2),
                                                                                    Cantidad: cargo.cantidadDevolver,
                                                                                    PrecioUnitario: cargo.precio,
                                                                                    UnidadMedida: cargo.unidadMedida,
                                                                                    Costo: cargo.costo
                                                                                   }))
                            }

                            this.axios.post('/app/bitacora/registro_bitacora',{
                                info:"{\"empresa\":"+sesion.sesion_empresa+",\"info\":{\"tipobitacora\":\"Tipo=>>Modificación\",\"Devolucion\":"+JSON.stringify(bitacora)+"}}",
                                Tabla:"Hospital..Devoluciones,Hospital..Cargos"
                            })

                            this.$vs.notify({        
                                            time: 4000,                                    
                                            title:'Exito',
                                            text: resp.data.json[0].descripcion,
                                            color:'success'
                                        })

                            this.limpiar_campos()
                            this.info.desactivarBotonModificacion = true
                        }
                    )
            
        },
        limpiar_campos(){            
            this.info.bodega = null
            this.info.tipoOrden = null
            this.info.descripcionOrden = null
            this.info.codigoOrden = null
            this.info.admision = null
            this.info.paciente = null   
            this.cargos = []
            this.cargosDevolucion = []   
        },
        limpiar_campos_exito(){            
            this.info.codigoOrden = null
            this.info.admision = null
            this.info.paciente = null      
            this.info.bodega = null   
        },
        confirmar_limpia_campos(){
            this.$vs.dialog({
                            type: 'confirm',
                            color: 'danger',
                            title: 'Confirmación',
                            acceptText: 'Limpiar',
                            cancelText: 'Cancelar',
                            text: `¿Desea limpiar los campos?`,
                            accept: () => {                                
                                this.limpiar_campos()
                                this.info.desactivarBotonModificacion = true
                            }
                        })
        }
    },watch:{
        'info.tipoOrden'(valor){
            if(!valor){
                this.info.descripcionOrden = null
            }
        }
    },
    mounted(){    
        this.info.filtroTipoOrden = []
        this.info.filtroTipoOrdenGeneral = []
        for(let privilegio of this.$store.state.privilegios){
            if(privilegio.Privilegio.includes("DEVOLUCION") && privilegio.Privilegio.split('_')[0].length == 2 ){
                let tipo_orden = privilegio.Privilegio.split('_')[0]                
                this.info.filtroTipoOrden.push(tipo_orden)                    
            }else if(privilegio.Privilegio.includes("DEVOLUCION") && privilegio.Privilegio.split('_')[0].length == 3 ){
                let tipo_orden_general = privilegio.Privilegio.split('_')[0]                 
                this.info.filtroTipoOrdenGeneral.push(tipo_orden_general)                    
            }
        }        
    }
}

</script>

<style scoped>
.cantidad button {
    height: 25px;
    width: 30px;
    border: 1px solid #ccc;
    border-radius: 5px;

}

.cantidad div {
    display: inline-block;
    height: 25px;
    width: 30px;
    text-align: center;
}
</style>