<template>
    <div>
        <vx-card title="Convenios de Consignación">
            <buscador ref="BuscarProveedorConsignacion" buscador_titulo="Buscador / Proveedores" :api="'app/v1_OrdenCompra/ConsultaProveedorConsignacion'" 
            :campos="['Proveedor', 'Nombre', 'Nit', 'Direccion', 'Telefonos']"
            :titulos="['Proveedor', '#Nombre', '#Nit', '#Direccion', '#Telefonos']"
            :api_filtro="{'Proveedor':Proveedor.Proveedor}"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" :api_preload="true" />

            <buscador ref="BuscarProductoProveedorConsignacion" buscador_titulo="Buscador / Productos" :api="'app/v1_OrdenCompra/ConsultaProductoProveedorConsignacion'" 
            :campos="['Producto', 'Descripcion']"
            :titulos="['Producto', '#Descripcion']"
            :api_filtro="{Proveedor:Proveedor.Proveedor, Producto:ProductoBuscar}"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" :api_preload="true" />

            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="12">
                    <label class="typo__label">Fecha:&nbsp;</label>
                    <vs-input type="date" v-model="FechaRegistro"></vs-input>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center" vs-justify="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="12">
                    <div class="almacenador w-full">
                        <div class="div-container">
                            <vs-row vs-align="center" vs-justify="center">
                                <label class="typo__label">&nbsp;Busqueda</label>
                                <vs-divider></vs-divider>
                                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                                    <vs-radio v-model="Busqueda" vs-name="radios1" vs-value="0">Proveedor</vs-radio>
                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                                    <vs-radio v-model="Busqueda" vs-name="radios1" vs-value="1">Producto</vs-radio>
                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                                    <vs-radio v-model="Busqueda" vs-name="radios1" vs-value="2">Convenio</vs-radio>
                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                                    <vs-radio v-model="Busqueda" vs-name="radios1" vs-value="3">Catálogo</vs-radio>
                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                    <vs-input v-model="Parametro"></vs-input>
                                </vs-col>
                            </vs-row>
                            <br>
                            <vs-row vs-align="center" vs-justify="flex-end">
                                <vs-col vs-type="flex" vs-align="center" vs-w="1">
                                    <vs-button color="primary" icon-pack="fas" icon="fa-search" @click="ConsultaConvenio()">Buscar</vs-button>
                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="3">
                                    <vs-button color="primary" icon-pack="fas" icon="fa-file-excel" @click="ConsultaConveniosExcel()">Excel</vs-button>
                                </vs-col>
                            </vs-row>
                        </div>
                    </div>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center" vs-justify="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="12">
                    <div ref="tablaProductos" class="w-full">
                        <vs-table2 max-items="10" tooltip pagination search :data="DetalleProductos">
                            <template slot="thead">
                                <th>Id Convenio</th>
                                <th>Linea</th>
                                <th>Producto</th>
                                <th>Nombre</th>
                                <th>Status</th>
                                <th>Costo Convenio</th>
                                <th>Precio Convenio</th>
                                <th>Proveedor</th>
                                <th>Nombre Proveedor</th>
                                <th>Convenio</th>
                                <th>Fecha</th>
                                <th>Fecha Convenio</th>
                                <th>Fecha Aplicar</th>
                                <th>Información</th>
                                <th>Modificar Convenio</th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :style="getStyle(`${tr.Status}`)" :data="tr" :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2 :data="data[indextr].IdConvenio">
                                        {{ data[indextr].IdConvenio }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Linea">
                                        {{ data[indextr].Linea }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Producto">
                                        {{ data[indextr].Producto }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Nombre">
                                        {{ data[indextr].Nombre }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Status">
                                        {{ data[indextr].Status }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].CostoConvenio">
                                        {{ $formato_moneda(data[indextr].CostoConvenio) }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].PrecioConvenio">
                                        {{ $formato_moneda(data[indextr].PrecioConvenio) }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Proveedor">
                                        {{ data[indextr].Proveedor }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].NbreProv">
                                        {{ data[indextr].NbreProv }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Convenio">
                                        {{ data[indextr].Convenio }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Fecha">
                                        {{ data[indextr].Fecha }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].FechaConvenio">
                                        {{ data[indextr].FechaConvenio }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].FechaAplicar">
                                        {{ data[indextr].FechaAplicar }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Informacion">
                                        {{ data[indextr].Informacion }}
                                    </vs-td2>
                                    <vs-td2 noTooltip>
                                        <vs-button color="primary" icon-pack="fas" icon="fa-edit" @click="IsModificacion=true;ModificarConvenio(tr)"  class="mr-1" style="display:inline-block"></vs-button>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                </vs-col>
            </vs-row>
            <vs-row vs-align="center" vs-justify="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="6">
                    <vs-button color="success" icon-pack="fas" icon="fa-check" @click="ActivarConvenio()"  class="mr-1" style="display:inline-block" :disabled="IsConvenio">Activar Convenio</vs-button>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-button color="danger" icon-pack="fas" icon="fa-check" @click="IsMotivo=true"  class="mr-1" style="display:inline-block" :disabled="IsConvenio">Inactivar Convenio</vs-button>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="12">
                    <div class="almacenador w-full">
                        <div class="div-container">
                            <vs-row vs-align="center">
                                <label class="typo__label">&nbsp;Agregar Nuevo Catálogo</label>
                                <vs-divider></vs-divider>
                                <vs-col vs-type="flex" vs-align="center" vs-w="3">
                                    <label class="typo__label">&nbsp;Proveedor:&nbsp;</label>
                                    <vs-input ref="ProveedorConsignacion" v-model="Proveedor.Proveedor" @keyup.enter="Proveedores()" @keydown.tab="Proveedores()" ></vs-input>
                                    <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="Proveedores()" icon="fa-search"></vs-button>
                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                    <label class="typo__label">Nombre:&nbsp;</label>
                                    <h5>{{Proveedor.Proveedor}} - {{ Proveedor.Nombre }}</h5>
                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="5" vs-justify="flex-end">
                                    <vs-button id="button-with-loading" color="primary" icon-pack="fa" icon="fa-save" v-if="styleHiddenNuevo" @click="styleHidden=true;styleHiddenNuevo=false;SiguienteConvenio()">Crear Convenio</vs-button>
                                    &nbsp;
                                    <vs-button id="button-with-loading" color="danger" icon-pack="fa" icon="fa-rectangle-xmark" @click="ConsultaConveniosInactivosExcel()">Convenios Eliminados</vs-button>
                                    <vs-button id="button-with-loading" color="primary" icon-pack="fa" icon="fa-bloggen" @click="IsInactivos=true;ConsultaConveniosInactivos()"> </vs-button>
                                </vs-col>
                            </vs-row>
                            <vs-row vs-align="center">
                                <vs-col vs-type="flex" vs-align="center" vs-w="3">

                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                    <label class="typo__label">Dirección:&nbsp;</label>
                                    <h5>{{ Proveedor.Direccion }}</h5>
                                </vs-col>
                            </vs-row>
                            <vs-row vs-align="center">
                                <vs-col vs-type="flex" vs-align="center" vs-w="3">

                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="3">
                                    <label class="typo__label">Nit:&nbsp;</label>
                                    <h5>{{ Proveedor.Nit }}</h5>
                                </vs-col>
                            </vs-row>
                            <br>
                            <vs-row vs-type="flex" vs-w="12" v-if="styleHidden">
                                <vs-col vs-type="flex" vs-w="12" vs-align="center">
                                    <!-- <div class="w-full" :style="styleHidden"> -->
                                        <label class="typo__label" >Producto &nbsp;</label>
                                        <vs-input ref="ProductoDeseado" v-model="ProductoBuscar" @keyup.enter="CargarProducto()" @keydown.tab="CargarProducto()" />
                                        <!-- <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="CargarProducto()" icon="fa-search" ></vs-button> -->
                                    <!-- </div> -->
                                </vs-col>
                            </vs-row>
                            <br>
                            <vs-row vs-type="flex" vs-w="12" >
                                <vs-col vs-type="flex" vs-w="12" vs-align="center">
                                    <!----- Detalle producto seleccionado--->
                                    <div v-if="MostrarProducto" class="w-full md:w-4/5 lg:w-4/5 xl:w-4/5">
                                        <div style="border-radius:5px;padding:5px;font-size:14px;background-color:#A5D6A7">
                                            Código: {{Producto_seleccionado.Producto}}
                                            <br>
                                            Producto: {{Producto_seleccionado.Descripcion}}
                                            <br>
                                            Unidad Medida: {{Producto_seleccionado.NombreUnidad}}
                                            <br>
                                            Precio Convenio: {{$formato_moneda(Producto_seleccionado.PrecioConvenio)}}
                                        </div>
                                    </div>
                                </vs-col>
                            </vs-row>
                            <br>
                            <vs-row vs-type="flex" vs-w="12" v-if="styleHidden" >
                                <vs-col vs-type="flex" vs-align="center" vs-w="3">
                                    <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="AgregarProducto()" icon="fa-plus-circle" >Agregar</vs-button>
                                </vs-col>
                            </vs-row>
                            <br>
                            <vs-row vs-align="center" vs-justify="center" v-if="styleHidden">
                                <vs-col vs-type="flex" vs-align="center" vs-w="7">
                                    <div ref="tablaProductos" class="w-full" >
                                        <vs-table2 max-items="10" tooltip pagination search :data="ProductosNuevos">
                                            <template slot="thead">
                                                <th>Producto</th>
                                                <th>U. Medida</th>
                                                <th>Nombre</th>
                                                <th>Precio</th>
                                            </template>
                                            <template slot-scope="{data}">
                                                <tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">
                                                    <vs-td2 :data="data[indextr].Producto">
                                                        {{ data[indextr].Producto }}
                                                    </vs-td2>
                                                    <vs-td2 :data="data[indextr].NombreUnidad">
                                                        {{ data[indextr].NombreUnidad }}
                                                    </vs-td2>
                                                    <vs-td2 :data="data[indextr].Descripcion">
                                                        {{ data[indextr].Descripcion }}
                                                    </vs-td2>
                                                    <vs-td2 :data="data[indextr].PrecioConvenio">
                                                        {{ $formato_moneda(data[indextr].PrecioConvenio) }}
                                                    </vs-td2>
                                                </tr>
                                            </template>
                                        </vs-table2>
                                    </div>
                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="5">
                                    <div class="almacenador w-full " v-if="styleHidden">
                                        <div class="div-container">
                                            <vs-row vs-align="center">
                                                <label class="typo__label">&nbsp;Datos de Convenio</label>
                                                <vs-divider></vs-divider>
                                                <vs-col vs-align="center" vs-w="12">
                                                    <vs-row vs-align="center">
                                                        <vs-col vs-type="flex" vs-align="center" vs-w="12">
                                                            <label class="typo__label">No. Convenio:&nbsp;</label>
                                                            <vs-input class="noconvenio-input" v-model="NoConvenio"></vs-input>
                                                        </vs-col>
                                                    </vs-row>
                                                    <br>
                                                    <vs-row vs-align="center">
                                                        <vs-col vs-type="flex" vs-align="center" vs-w="12">
                                                            <label class="typo__label">Convenio:&nbsp;</label>
                                                            <vs-input class="convenio-input" v-model="Convenio"></vs-input>
                                                        </vs-col>
                                                    </vs-row>
                                                    <br>
                                                    <vs-row vs-align="center">
                                                        <vs-col vs-type="flex" vs-align="center" vs-w="12">
                                                            <label class="typo__label">Fecha:&nbsp;</label>
                                                            <vs-input class="fechaconvenio-input" type="date" v-model="FechaConvenio"></vs-input>
                                                        </vs-col>
                                                    </vs-row>
                                                    <br>
                                                    <vs-row vs-align="center">
                                                        <vs-col vs-align="center" vs-w="12">
                                                            <label class="typo__label">Nota:&nbsp;</label>
                                                            <vs-textarea v-model="Nota" rows="5" counter="240"/>
                                                        </vs-col>
                                                    </vs-row>
                                                    <br>
                                                    <vs-row vs-align="center">
                                                        <vs-col vs-type="flex" vs-align="center" vs-justify="center" vs-w="12">
                                                            <vs-button color="success" icon-pack="fa" icon="fa-save" @click="GrabarConvenio()">Grabar</vs-button>
                                                            &nbsp;
                                                            <vs-button color="primary" icon-pack="fa" icon="fa-broom" @click="Limpiar()">Limpiar</vs-button>
                                                        </vs-col>
                                                    </vs-row>
                                                </vs-col>
                                            </vs-row>
                                        </div>
                                    </div>
                                </vs-col>
                            </vs-row>
                        </div>
                    </div>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="12">
                    <div class="almacenador w-full">
                        <div class="div-container">
                            <vs-row vs-align="center">
                                <label class="typo__label">&nbsp;Productos Sin Convenio Por Proveedor</label>
                                <vs-divider></vs-divider>
                                <vs-col vs-type="flex" vs-align="center" vs-w="8">
                                    <div ref="tablaProductos" class="w-full">
                                        <vs-table2 max-items="10" tooltip pagination search :data="ProductosSinConvenio">
                                            <template slot="thead">
                                                <th>Código</th>
                                                <th>Nombre Producto</th>
                                                <th>Precio Convenio</th>
                                                <th>Costo Ultimo</th>
                                            </template>
                                            <template slot-scope="{data}">
                                                <tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">
                                                    <vs-td2 :data="data[indextr].Codigo">
                                                        {{ data[indextr].Codigo }}
                                                    </vs-td2>
                                                    <vs-td2 :data="data[indextr].Nombre">
                                                        {{ data[indextr].Nombre }}
                                                    </vs-td2>
                                                    <vs-td2 :data="data[indextr].PrecioConvenio">
                                                        {{ $formato_moneda(data[indextr].PrecioConvenio) }}
                                                    </vs-td2>
                                                    <vs-td2 :data="data[indextr].CostoUltimo">
                                                        {{ $formato_moneda(data[indextr].CostoUltimo) }}
                                                    </vs-td2>
                                                </tr>
                                            </template>
                                        </vs-table2>
                                    </div>
                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="1">

                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="3">
                                    <vs-button color="primary" @click="ConsultaProductosSinConvenio()">Productos Sin Convenio</vs-button>
                                    &nbsp;
                                    <vs-button color="primary" @click="ConsultaProductosSinProveedorEscel()">Productos Sin Convenio Excel</vs-button>
                                </vs-col>
                            </vs-row>
                        </div>
                    </div>
                </vs-col>
            </vs-row>
            <vs-popup classContent="popup-example" title="Inactivar Convenio" :active.sync="IsMotivo">
                <vs-row vs-align="center">
                    <vs-col vs-type="flex" vs-align="center" vs-w="12">
                        <label class="typo__label">Motivo:&nbsp;</label>
                        <vs-textarea v-model="Motivo" rows="5" counter="240"/>
                    </vs-col>
                </vs-row>
                <br>
                <vs-row vs-align="center">
                    <vs-col vs-type="flex" vs-align="center" vs-justify="center" vs-w="12">
                        <vs-button color="success" icon-pack="fa" icon="fa-save" @click="InactivarConvenio()">Confirmar</vs-button>
                    </vs-col>
                </vs-row>
            </vs-popup>
            <vs-popup classContent="popup-example" title="Modificar Convenio" :active.sync="IsModificacion">
                <div style="padding:15px; border-radius:5px; box-shadow:0px 15px 60px -10px;margin-bottom:20px">
                    <vs-row vs-type="flex" vs-w="12">
                        <vs-col vs-type="flex" vs-align="center" vs-w="4">
                            Id Correlativo:&nbsp;&nbsp;<h3>{{ Info.IdCorrelativo }}</h3>
                        </vs-col>
                    </vs-row>
                    <br>
                    <vs-row vs-type="flex" vs-w="12">
                        <vs-col vs-type="flex" vs-align="center" vs-w="1">
                            <label class="typo__label">Correlativo:&nbsp;</label>
                        </vs-col>
                        <vs-col vs-type="flex" vs-align="center" vs-w="3">
                            <vs-input v-model="Info.IdConvenio"/>
                        </vs-col>
                        <vs-col vs-type="flex" vs-align="center" vs-w="5">
                            <vs-input v-model="Info.Convenio"/>
                        </vs-col>
                    </vs-row>
                    <br>
                    <vs-row vs-type="flex" vs-w="12">
                        <vs-col vs-type="flex" vs-align="center" vs-w="1">
                            <label class="typo__label">Proveedor:&nbsp;</label>
                        </vs-col>
                        <vs-col vs-type="flex" vs-align="center" vs-w="3">
                            <vs-input v-model="Info.Proveedor"/>
                        </vs-col>
                    </vs-row>
                    <br>
                    <vs-row vs-type="flex" vs-w="12">
                        <vs-col vs-type="flex" vs-align="center" vs-w="1">
                            <label class="typo__label">Producto:&nbsp;</label>
                            
                        </vs-col>
                        <vs-col vs-type="flex" vs-align="center" vs-w="3">
                            <vs-input v-model="Info.Producto"/>
                        </vs-col>
                    </vs-row>
                    <br>
                    <vs-row vs-type="flex" vs-w="12">
                        <vs-col vs-type="flex" vs-align="center" vs-w="1">
                            <label class="typo__label">Precio:&nbsp;</label>
                        </vs-col>
                        <vs-col vs-type="flex" vs-align="center" vs-w="3">
                            <vs-input v-model="Info.PrecioConvenio" @change="OnchangePrecioConvenio()"/>
                        </vs-col>
                    </vs-row>
                    <br>
                    <vs-row vs-type="flex" vs-w="12">
                        <vs-col vs-type="flex" vs-align="center" vs-w="1">
                            <label class="typo__label">Costo:&nbsp;</label>
                        </vs-col>
                        <vs-col vs-type="flex" vs-align="center" vs-w="3">
                            <vs-input v-model="Info.CostoConvenio"/>
                        </vs-col>
                    </vs-row>
                    <br>
                    <vs-row vs-type="flex" vs-justify="center" vs-w="12">
                        <vs-col vs-type="flex" vs-align="center" vs-w="3">
                            <vs-button color="success" icon-pack="fa" icon="fa-save" @click="ActualizarConvenio()">Actualizar</vs-button>
                        </vs-col>
                    </vs-row>
                </div>
            </vs-popup>
            <vs-popup classContent="popup-example" title="Modificar Convenio" :active.sync="IsInactivos">
                <vs-table2 max-items="10" tooltip pagination search :data="ConveniosInactivos">
                    <template slot="thead">
                        <th>IdConvenio</th>
                        <th>Producto</th>
                        <th>CostoC</th>
                        <th>PrecioC</th>
                        <th>Proveedor</th>
                        <th>Fecha</th>
                        <th>Motivo</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2 :data="data[indextr].IdConvenio">
                                {{ data[indextr].IdConvenio }}
                            </vs-td2>
                            <vs-td2 :data="data[indextr].Producto">
                                {{ data[indextr].Producto }}
                            </vs-td2>
                            <vs-td2 :data="data[indextr].CostoC">
                                {{ $formato_moneda(data[indextr].CostoC) }}
                            </vs-td2>
                            <vs-td2 :data="data[indextr].PrecioC">
                                {{ $formato_moneda(data[indextr].PrecioC) }}
                            </vs-td2>
                            <vs-td2 :data="data[indextr].Proveedor">
                                {{ data[indextr].Proveedor }}
                            </vs-td2>
                            <vs-td2 :data="data[indextr].Fecha">
                                {{ data[indextr].Fecha }}
                            </vs-td2>
                            <vs-td2 :data="data[indextr].Motivo">
                                {{ data[indextr].Motivo }}
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </vs-popup>
        </vx-card>
    </div>
</template>
<script>
    import moment from 'moment'
    export default {
        data(){
            return{
                FechaRegistro: '',
                Busqueda: 0,
                Parametro: '',
                DetalleProductos: [{
                    IdConvenio: '',
                    Linea: '',
                    Producto: '',
                    Nombre: '',
                    Status: '',
                    CostoConvenio: '',
                    PrecioConvenio: '',
                    Proveedor: '',
                    NbreProv: '',
                    Convenio: '',
                    Fecha: '',
                    FechaConvenio: '',
                    FechaAplicar: '',
                    Informacion: ''
                }],
                Proveedor: {
                    Proveedor: '',
                    Nombre: '',
                    Nit: '',
                    Telefonos: '',
                    Direccion: ''
                },
                Producto_seleccionado: {
                    Producto: '',
                    Cantidad: 0,
                    UnidadMedida: '',
                    NombreUnidad: '',
                    Descripcion: '',
                    PrecioUnitario: 0,
                    ValorTotal: 0,
                    Convenio: '',
                    PrecioConvenio: 0,
                    CostoConvenio: 0,
                    CostoPromedio: 0
                },
                MostrarProducto: false,
                ProductosNuevos: [{
                    Producto: '',
                    Cantidad: '',
                    UnidadMedida: '',
                    NombreUnidad: '',
                    Descripcion: '',
                    PrecioUnitario: '',
                    ValorTotal: '',
                    Convenio: '',
                    PrecioConvenio: '',
                    CostoConvenio: '',
                    CostoPromedio: '',
                    NumeroSerie: ''
                }],
                styleHidden: false,
                NoConvenio: '',
                Convenio: '',
                FechaConvenio: '',
                Nota: '',
                ProductosSinConvenio: [{
                    Codigo: '',
                    Nombre: '',
                    PrecioConvenio: '',
                    CostoUltimo: ''
                }],
                ProductoBuscar: '',
                styleHiddenNuevo: true,
                Motivo: '',
                IsMotivo: false,
                IsConvenio: true,
                IsModificacion: false,
                Info: {
                    Empresa: '',
                    IdCorrelativo: '',
                    IdConvenio: '',
                    Convenio: '',
                    Proveedor: '',
                    Producto: '',
                    PrecioConvenio: '',
                    CostoConvenio: '',
                    CostoConvenioAnterior: ''
                },
                ConveniosInactivos: [{
                    Empresa: '',
                    IdConvenioElimina: '',
                    Linea: '',
                    Proveedor: '',
                    Producto: '',
                    Fecha: '',
                    Usuario: '',
                    Convenio: '',
                    IdConvenio: '',
                    Informacion: '',
                    Motivo: '',
                    CostoC: '',
                    PrecioC: ''
                }],
                IsInactivos: false,
                ModificaInfo: {}
            }
        },
        methods: {
            ObtenerFechas(){
                this.FechaConvenio = moment(new Date(Date.now())).format('YYYY-MM-DD')
            },
            Proveedores(){
                if(this.Proveedor.Proveedor != ''){
                    this.axios.post('/app/v1_OrdenCompra/ConsultaProveedorConsignacion', {
                        Proveedor: this.Proveedor.Proveedor
                    })
                    .then(resp => {
                        if(resp.data.codigo == 0){
                            if(resp.data.json.length == 1){
                                this.Proveedor = resp.data.json[0]
                            }else{
                                this.$refs.BuscarProveedorConsignacion.iniciar((data) =>{
                                    if(data != null){
                                        this.Proveedor = data
                                    }
                                })
                            }
                        }
                    })
                }else{
                    this.$refs.BuscarProveedorConsignacion.iniciar((data) =>{
                        if(data != null){
                            this.Proveedor = data
                        }
                    })
                }
            },
            ConsultaConvenio(){
                this.axios.post('/app/v1_OrdenCompra/ConsultaConvenios', {
                    Tipo: this.Busqueda,
                    Parametro: this.Parametro
                })
                .then(resp => {
                    if(resp.data.codigo == '0'){
                        this.DetalleProductos = resp.data.json
                        this.IsConvenio = false
                    }else{
                        this.DetalleProductos = []
                        this.IsConvenio = true
                    }
                })
            },
            ConsultaConveniosExcel(){
                this.ConsultaConvenio()
                this.$reporte_modal({
                    Nombre: "Convenios Proveedor",
                    Opciones: {
                        Tipo: this.Busqueda,
                        Parametro: this.Parametro

                    },
                    Formato: "EXCEL"
                })
            },
            SiguienteConvenio(){
                this.axios.post('/app/v1_OrdenCompra/SiguienteConvenio', {
                })
                .then(resp => {
                    if(resp.data.codigo == '0'){
                        this.ObtenerFechas()
                        this.NoConvenio = resp.data.resultado
                    }
                })
            },
            async CargarProducto(){
                this.MostrarProducto = false
                if(this.Proveedor.Proveedor != ''){
                    if(this.ProductoBuscar != ''){
                        this.axios.post('/app/v1_OrdenCompra/ConsultaProductoProveedorConsignacion', {
                            Proveedor: this.Proveedor.Proveedor,
                            Producto: this.ProductoBuscar
                        })
                        .then(async resp =>{
                            if(resp.data.json.length == 1){
                                if(await this.ValidaProductoConvenio(resp.data.json[0].Producto)){
                                    this.ProductoBuscar = ''
                                    this.Producto_seleccionado = {}
                                    this.MostrarProducto = false

                                    return
                                    
                                }else{
                                    this.ProductoBuscar = resp.data.json[0].Producto.trim()
                                    this.Producto_seleccionado.Producto = resp.data.json[0].Producto
                                    this.Producto_seleccionado.UnidadMedida = resp.data.json[0].UnidadMedida
                                    this.Producto_seleccionado.NombreUnidad = resp.data.json[0].NombreUnidad
                                    this.Producto_seleccionado.Descripcion = resp.data.json[0].Descripcion
                                    this.Producto_seleccionado.PrecioConvenio = resp.data.json[0].PrecioConvenio
                                    this.Producto_seleccionado.CostoConvenio = resp.data.json[0].CostoConvenio
                                    this.Producto_seleccionado.Convenio = resp.data.json[0].Convenio
                                    this.Producto_seleccionado.CostoPromedio = resp.data.json[0].CostoPromedio
                                    this.MostrarProducto = true
                                }
                            }else{
                                this.$refs.BuscarProductoProveedorConsignacion.iniciar(async (data) => {
                                    if(data != null){
                                        if(await this.ValidaProductoConvenio(data.Producto)){
                                            this.ProductoBuscar = ''
                                            this.Producto_seleccionado = {}
                                            this.MostrarProducto = false

                                            return
                                            
                                        }else{
                                            this.ProductoBuscar = data.Producto.trim()
                                            this.Producto_seleccionado.Producto = data.Producto
                                            this.Producto_seleccionado.UnidadMedida = data.UnidadMedida
                                            this.Producto_seleccionado.NombreUnidad = data.NombreUnidad
                                            this.Producto_seleccionado.Descripcion = data.Descripcion
                                            this.Producto_seleccionado.PrecioConvenio = data.PrecioConvenio
                                            this.Producto_seleccionado.CostoConvenio = data.CostoConvenio
                                            this.Producto_seleccionado.Convenio = data.Convenio
                                            this.Producto_seleccionado.CostoPromedio = data.CostoPromedio
                                            this.MostrarProducto = true
                                        }
                                    }
                                })
                            }
                        })
                    }else{
                        this.$refs.BuscarProductoProveedorConsignacion.iniciar(async (data) => {
                            if(data != null){
                                if(await this.ValidaProductoConvenio(data.Producto)){
                                    this.ProductoBuscar = ''
                                    this.Producto_seleccionado = {}
                                    this.MostrarProducto = false

                                    return
                                    
                                }else{
                                    this.ProductoBuscar = data.Producto.trim()
                                    this.Producto_seleccionado.Producto = data.Producto
                                    this.Producto_seleccionado.UnidadMedida = data.UnidadMedida
                                    this.Producto_seleccionado.NombreUnidad = data.NombreUnidad
                                    this.Producto_seleccionado.Descripcion = data.Descripcion
                                    this.Producto_seleccionado.PrecioConvenio = data.PrecioConvenio
                                    this.Producto_seleccionado.CostoConvenio = data.CostoConvenio
                                    this.Producto_seleccionado.Convenio = data.Convenio
                                    this.Producto_seleccionado.CostoPromedio = data.CostoPromedio
                                    this.MostrarProducto = true
                                }
                            }
                        })
                    }
                }else{
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Convenios',
                        text: 'Debe de Seleccionar un Proveddor',
                    })
                }
            },
            async ValidaProductoConvenio(Producto){
                return this.axios.post('/app/v1_OrdenCompra/ValidaProductoConvenio', {
                    Proveedor: this.Proveedor.Proveedor,
                    Producto: Producto
                })
                .then(() => {
                    return false
                })
                .catch(() => {
                    return true
                })
            },
            AgregarProducto(){
                if(this.Producto_seleccionado.Producto == '' || this.Producto_seleccionado.Producto == null || this.Producto_seleccionado.Producto == 0){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Convenios',
                        text: 'Debe de seleccionar un producto..'
                    })
                    return
                }
                let ProductoBuscado = this.ProductosNuevos.filter(f => f.Producto == this.Producto_seleccionado.Producto)
                if(ProductoBuscado.length > 0){
                    if(ProductoBuscado[0].Producto == this.Producto_seleccionado.Producto){
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Convenios',
                            text: 'Este producto ya lo agrego al listado del convenio..'
                        })
                        return
                    }
                }
                
                this.ProductosNuevos.push({
                    Producto: this.Producto_seleccionado.Producto,
                    UnidadMedida: this.Producto_seleccionado.UnidadMedida,
                    NombreUnidad: this.Producto_seleccionado.NombreUnidad,
                    Descripcion: this.Producto_seleccionado.Descripcion,
                    PrecioUnitario: this.Producto_seleccionado.PrecioUnitario,
                    Convenio: this.Producto_seleccionado.Convenio,
                    PrecioConvenio: this.Producto_seleccionado.PrecioConvenio,
                    CostoConvenio: this.Producto_seleccionado.CostoConvenio,
                    CostoPromedio: this.Producto_seleccionado.CostoPromedio,
                    NumeroSerie: ''
                })

                this.Producto_seleccionado = {}
                this.ProductoBuscar = ''
                this.$refs.ProductoDeseado.focusInput()
                this.MostrarProducto = false
            },
            GrabarConvenio(){
                if(this.ProductosNuevos.length == 0){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Convenios',
                        text: 'Debe de seleccionar un producto..'
                    })
                    return
                }else{
                    this.axios.post('/app/v1_OrdenCompra/IngresoConvenio', {
                        Proveedor: this.Proveedor.Proveedor,
                        NoConvenio: this.NoConvenio,
                        Motivo: this.Nota,
                        FechaConvenio: this.FechaConvenio,
                        Convenio: this.Convenio,
                        Datos: JSON.stringify(this.ProductosNuevos)
                    })
                    .then(resp => {
                        if(resp.data.codigo == '0'){
                            this.Limpiar()
                            this.styleHiddenNuevo = true
                            this.styleHidden = false
                        }
                    })
                }
            },
            ConsultaProductosSinConvenio(){
                this.axios.post('/app/v1_OrdenCompra/ConsultaProductosSinConvenio', {
                    Proveedor: this.Proveedor.Proveedor
                })
                .then(resp => {
                    if(resp.data.codigo == '0'){
                        this.ProductosSinConvenio = resp.data.json.filter(f => f.PrecioConvenio != '')
                    }else{
                        this.ProductosSinConvenio = []
                    }
                })
            },
            ConsultaProductosSinProveedorEscel(){
                this.ConsultaProductosSinConvenio()
                this.$reporte_modal({
                    Nombre: "Productos Sin Convenio Proveedor",
                    Opciones: {
                        Proveedor: this.Proveedor.Proveedor,
                        NombreProveedor: this.Proveedor.Nombre

                    },
                    Formato: "EXCEL"
                })
            },
            Limpiar(){
                this.Producto_seleccionado = {}
                this.ProductoBuscar = ''
                this.ProductosNuevos = []
                this.MostrarProducto = false
                this.Convenio = ''
                this.Nota = ''
                this.SiguienteConvenio()
            },
            ActivarConvenio(){
                this.axios.post('/app/v1_OrdenCompra/ActivarConvenio', {
                    Terminal: 'SIGHOS',
                    Datos: JSON.stringify(this.DetalleProductos)
                })
                .then(resp => {
                    if(resp.data.codigo == '0'){
                        this.ConsultaConvenio()
                    }
                })
            },
            InactivarConvenio(){
                if(this.Motivo == ''){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Convenios',
                        text: 'Es necesrio indicar un motivo válido..'
                    })
                    return
                }
                this.axios.post('/app/v1_OrdenCompra/InactivarConvenio', {
                    Terminal: 'SIGHOS',
                    Motivo: this.Motivo,
                    Datos: JSON.stringify(this.DetalleProductos)
                })
                .then(resp => {
                    if(resp.data.codigo == '0'){
                        this.IsMotivo = false
                        this.ConsultaConvenio()
                    }
                })
            },
            ModificarConvenio(data){
                this.Info.Empresa = data.Empresa
                this.Info.IdCorrelativo = data.IdCorrelativo
                this.Info.IdConvenio = data.IdConvenio
                this.Info.Convenio = data.Convenio
                this.Info.Proveedor = data.Proveedor
                this.Info.Producto = data.Producto
                this.Info.PrecioConvenio = data.PrecioConvenio
                this.Info.CostoConvenio = data.CostoConvenio
                this.Info.CostoConvenioAnterior = data.CostoConvenio
                this.ModificaInfo = data
            },
            OnchangePrecioConvenio(){
                this.Info.CostoConvenio = ((100*(parseFloat(this.Info.PrecioConvenio) / 1.12))/100).toFixed(2)
                this.axios.post('/app/v1_OrdenCompra/NivelTolerancia', {
                    Producto: this.Info.Producto,
                    CostoIncremento: this.Info.CostoConvenio
                })
                .then(() => {
                })
                .catch(() => {
                    this.Info.PrecioConvenio = this.ModificaInfo.PrecioConvenio
                    this.Info.CostoConvenio = this.ModificaInfo.CostoConvenio
                });
            },
            ActualizarConvenio(){
                this.axios.post('/app/v1_OrdenCompra/ActualizarConvenio', {
                    Terminal: 'SIGHOS',
                    IdCorrelativo: this.Info.IdCorrelativo,
                    IdConvenio: this.Info.IdConvenio,
                    Convenio: this.Info.Convenio,
                    Producto: this.Info.Producto,
                    PrecioConvenio: this.Info.PrecioConvenio,
                    CostoConvenio: this.Info.CostoConvenio,
                    CostoConvenioAnterior: this.Info.CostoConvenioAnterior
                })
                .then(() => {
                    this.ConsultaConvenio()
                    this.IsModificacion = false
                })
            },
            ConsultaConveniosInactivos(){
                this.axios.post('/app/v1_OrdenCompra/ConveniosInactivos', {
                    Proveedor: this.Proveedor.Proveedor
                })
                .then(resp => {
                    if(resp.data.codigo == '0'){
                        this.ConveniosInactivos = resp.data.json
                    }else{
                        this.ConveniosInactivos = []
                    }
                })
            },
            ConsultaConveniosInactivosExcel(){
                this.$reporte_modal({
                    Nombre: "Convenios Inactivos",
                    Opciones: {
                        Proveedor: this.Proveedor.Proveedor,
                        NombreProveedor: this.Proveedor.Nombre

                    },
                    Formato: "EXCEL"
                })
            },
            getStyle(item1){
                return item1 == 'P'?'background-color: #B5B5B5; color: #E74C3C;':
                        item1 == 'A' ? 'background-color: #00953A; opacity: 0.7; color: black; font-weight: bold;' : 'background-color: white;'
            }
        },
        mounted(){
            this.DetalleProductos = []
            this.ProductosNuevos = []
            this.ProductosSinConvenio = []
            this.Producto_seleccionado = {}
            this.FechaRegistro = moment(new Date(Date.now())).format('YYYY-MM-DD')
            this.ObtenerFechas()
        }
    }
</script>
<style scoped>
    .almacenador{
        border: 1px solid #888;

    }
    .div-container{
        padding: 5px;
        margin: 10px;
    }
    .noconvenio-input{
        margin-left: 25px;
    }
    .convenio-input{
        margin-left: 50px;
    }
    .fechaconvenio-input{
        margin-left: 75px;
    }
</style>