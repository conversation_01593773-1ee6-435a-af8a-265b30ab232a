<template>
    <div>
        <vx-card :title="`${tituloModulo} - ${selectBase.Nombre}`">
            <div class="flex flex-wrap mb-1">
                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                    <label style="font-size:12px">Base</label>
                    <v-select v-model="selectBase" label="Nombre" :options="listaBases" :clearable="false"
                        @input="Otros().ChangeHospital('listarClinicas', 'selectClinica')" />
                </div>
                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                    <label style="font-size:12px">Clínica</label>
                    <v-select v-model="selectClinica" label="Nombre" :options="listarClinicas" :clearable="false"
                        @input="Otros().ChangeClinica()" />
                </div>
                <div v-if="this.vistaCalendario == 'day'" class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                    <label style="font-size:12px">Médico</label>
                    <v-select multiple v-model="selectMedico" label="text" :options="listaMedicos" :clearable="false"
                        @input="Otros().ChangeMedico()" />
                </div>
                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1"
                    v-if="vistaCalendario == 'agenda' && permisos.CrearRol">
                    <vs-button class="w-full mr-1 mt-5" title="Crear Procedimiento" color="success"
                        @click="Otros().AbrirVentana(1)">
                        Crear
                    </vs-button>
                </div>
                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1" v-if="vistaCalendario == 'day'">
                    <ValidationProvider name="IdCliente2" v-slot="{ errors }">
                        <SM-Buscar label="Cliente" api="app/citas/BusquedaClientes"
                            :api_filtro="{ Opcion: 'C', SubOpcion: '2', TipoCita: '1' }"
                            :api_campos="['IdCita', 'startDate', 'IdCliente', 'Nombre1', 'Nombre2', 'Apellido1', 'Apellido2', 'NombreBase', 'NombreClinica', 'NombreStatus']"
                            :api_titulos="['#IdCita', '#Fecha Cita', 'IdCliente', 'Nombre1', 'Nombre2', 'Apellido1', 'Apellido2', '#Base', '#Clinica', '#Estado']"
                            :callback_buscar="Consulta().BusquedaCliente" :disabled_texto="true"
                            :danger="errors.length > 0" :mostrar_texto="false" api_campo_respuesta_mostrar="Nombre1"
                            :dangertext="(errors.length > 0) ? errors[0] : null" :disabled_editar="true" />
                    </ValidationProvider>
                </div>
            </div>
        </vx-card>
        <vs-popup :title="ventana.titulo" v-show="ventana.mostrar" :active.sync="ventana.mostrar">
            <div v-if="ventana.opcion == 1">
                <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                    <form method="post" @submit.prevent="handleSubmit(Agregar().AgregarRol(1, invalid))">
                        <div class="flex flex-wrap mb-1">
                            <div class=" w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                                <label style="font-size:12px">Base</label>
                                <v-select v-model="selectBaseVentana" label="Nombre" :options="listaBases"
                                    :clearable="false"
                                    @input="Otros().ChangeHospital('listarClinicasVentana', 'selectClinicaVentana', true)" />
                            </div>
                            <div class=" w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                                <label style="font-size:12px">Clínica</label>
                                <v-select v-model="selectClinicaVentana" label="Nombre" :options="listarClinicasVentana"
                                    :clearable="false" />
                            </div>
                            <div class=" w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                                <ValidationProvider name="Medico" rules="required|numero_entero|numero_min:1"
                                    v-slot="{ errors }" class="required">
                                    <SM-Buscar v-model="info.Medico" label="Código del Médico"
                                        api="app/citas/Busqueda_Ajenos" :api_filtro="{ Opcion: 'C', SubOpcion: '1' }"
                                        :api_campos="['Codigo', 'Nombre', 'Apellido', 'Corporativo', 'Tipo']"
                                        :api_titulos="['Codigo', 'Nombre', 'Apellido', 'Corporativo', 'Tipo']"
                                        api_campo_respuesta="Codigo" :callback_buscar="Consulta().Consulta_Ajenos"
                                        :callback_nuevo="null" :callback_editar="null"
                                        :callback_cancelar="Otros().Limpiar_Datos" :disabled_texto="true"
                                        :danger="errors.length > 0" api_campo_respuesta_mostrar="Nombre"
                                        :dangertext="(errors.length > 0) ? errors[0] : null" :disabled_editar="true" />
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class=" w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                                <label style="font-size:12px">Generación de rol</label>
                                <v-select v-model="selectGeneraciónRol" label="Nombre" :options="listaGeneraciónRol"
                                    :clearable="false" @input="Otros().changeGeneracion()" />
                            </div>
                            <div class=" w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                                <ValidationProvider name="HoraFin" rules="required" class="required">
                                    <label style="font-size:12px">Hora Inicio (24 hrs)</label>
                                    <flat-pickr class="w-full" :config="configTimePicker" v-model="info.HoraInicio" />
                                </ValidationProvider>
                            </div>
                            <div class=" w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                                <ValidationProvider name="HoraFin" rules="required" class="required">
                                    <label style="font-size:12px">Hora Fin (24 hrs)</label>
                                    <flat-pickr class="w-full" :config="configTimePicker" v-model="info.HoraFin" />
                                </ValidationProvider>
                            </div>

                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class=" w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1" v-if="selectGeneraciónRol.Codigo == 1">
                                <ValidationProvider name="mesAnio" rules="required" class="required">
                                    <label style="font-size:12px">Año/mes</label>
                                    <flat-pickr class="w-full" v-model="info.mesAnio"
                                        :config="configFromdateTimePicker" />
                                </ValidationProvider>
                            </div>
                            <div class=" w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1" v-if="selectGeneraciónRol.Codigo == 1">
                                <ValidationProvider name="Días" rules="required" v-slot="{ errors }" class="required">
                                    <SM-Buscar v-model="info.Dias" label="Días"
                                        :api="[{ NumeroDia: '1', Dia: 'Domingo' }, { NumeroDia: '2', Dia: 'Lunes' }, { NumeroDia: '3', Dia: 'Martes' }, { NumeroDia: '4', Dia: 'Miercoles' }, { NumeroDia: '5', Dia: 'Jueves' }, { NumeroDia: '6', Dia: 'Viernes' }, { NumeroDia: '7', Dia: 'Sábado' }]"
                                        :api_cache="true" :api_campos="['#Codigo', 'Dia']"
                                        :api_titulos="['#NumeroDia', '#Día']" api_campo_respuesta="NumeroDia"
                                        api_campo_respuesta_mostrar="Dia" :api_preload="true" :disabled_texto="true"
                                        :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" :api_multiselect="true"
                                        :mostrar_busqueda="true" />
                                </ValidationProvider>
                            </div>
                            <div class=" w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1" v-if="selectGeneraciónRol.Codigo == 2">
                                <ValidationProvider name="FechaDesde" rules="required" class="required">
                                    <label style="font-size:12px">Fecha Desde</label>
                                    <flat-pickr class="w-full" v-model="info.FechaDesde"
                                        :config="configFromdateTimePicker" />
                                </ValidationProvider>
                            </div>
                            <div class=" w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1" v-if="selectGeneraciónRol.Codigo == 2">
                                <ValidationProvider name="FechaHasta" rules="required" class="required">
                                    <label style="font-size:12px">Fecha Hasta</label>
                                    <flat-pickr class="w-full" v-model="info.FechaHasta"
                                        :config="configFromdateTimePicker" />
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full  p-1">
                                <label style="font-size:12px">Observaciones</label>
                                <vs-textarea v-model="info.Observaciones" class="w-full" style="height: 175px;"
                                    rows="7" />
                            </div>
                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full h-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                                <vs-button height="100px" class="w-full mr-1 mt-5" color="success"
                                    @click="Agregar().AgregarRol(1, invalid)">
                                    Guardar
                                </vs-button>
                            </div>
                        </div>
                    </form>
                </ValidationObserver>
            </div>
            <div v-if="ventana.opcion == 2">
                <div class="flex flex-wrap mb-1">
                    <div class="w-full p-1">
                        <vs-button title="Editar Rol Clínica" color="primary" icon-pack="fas" icon="fa-edit"
                            class="mr-1" style="display:inline-block" @click="Otros().AbrirVentana(8)"
                            :disabled="!permisos.EditarRol" />
                        <vs-button title="Anular Rol Clínica" color="danger" icon-pack="fas" icon="fa-trash"
                            class="mr-1" style="display:inline-block" @click="Otros().AbrirVentana(7)"
                            :disabled="!permisos.AnularRol" />
                        <vs-button title="Bloquear/Desbloquear Horario" color="rgb(255, 223, 0)" icon-pack="fas"
                            icon="fa-unlock" class="mr-1" style="display:inline-block" @click="Otros().AbrirVentana(4)"
                            :disabled="!permisos.BloquearHorarios" />
                        <vs-button color="success" icon-pack="fas" icon="fa-calendar" class="mr-1"
                            style="display:inline-block" @click="Otros().AbrirVentana(3)"
                            :disabled="!permisos.CrearCita">Agregar Cita</vs-button>
                        <vs-button color="rgb(255, 130, 0)" icon-pack="fas" icon="fa-clock" class="mr-1"
                            style="display:inline-block" @click="Otros().AbrirVentana(12)"
                            :disabled="!permisos.IntercalarCita || infoRolClinica.IntercalarCita == '0'">Intercalar
                            Cita</vs-button>
                    </div>
                    <vs-divider position="left"></vs-divider>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Base: {{ this.infoRolClinica.Base }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Clínica: {{ this.infoRolClinica.Clinica }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Especialidad Ajeno: {{ this.infoRolClinica.EspecialidadAjeno
                            }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">ID Agenda Clínica: {{ this.infoRolClinica.IdAgendaClinica
                            }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Médico: {{ this.infoRolClinica.Medico }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Status Rol Clínica: {{ this.infoRolClinica.Status }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Fecha: {{ this.infoRolClinica.NombreDia +
            ',' + this.infoRolClinica.Fecha }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Hora Inicio (24 hrs): {{ this.infoRolClinica.HoraInicio }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Hora Fin (24 hrs): {{ this.infoRolClinica.HoraFin }}</label>
                    </div>
                    <vs-divider position="left"></vs-divider>
                    <div class="w-full md:w-1/1 lg:w-1/1 xl:w-1/1 p-1">
                        <label style="font-size:12px">Observaciones: {{ this.infoRolClinica.Observaciones }}</label>
                    </div>
                </div>
            </div>
            <div v-if="ventana.opcion == 3">
                <div class="flex flex-wrap mb-1">
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4 p-1">
                        <label style="font-size:12px">Base: {{ this.infoRolClinica.Base }}</label>
                    </div>
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4 p-1">
                        <label style="font-size:12px">Clínica: {{ this.infoRolClinica.Clinica }}</label>
                    </div>
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4 p-1">
                        <label style="font-size:12px">Médico: {{ this.infoRolClinica.Medico }}</label>
                    </div>
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4 p-1">
                        <label style="font-size:12px">Fecha: {{ this.infoRolClinica.NombreDia + ', '+this.infoRolClinica.Fecha }}</label>
                    </div>
                </div>
                <vs-divider position="left"></vs-divider>
                <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                    <form method="post" @submit.prevent="handleSubmit(Agregar().GuardarCita(invalid, false))">
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2  p-1">
                                <ValidationProvider name="QuienLlama" rules="alpha_spaces|max:100" v-slot="{ errors }">
                                    <label style="font-size:12px">Quién llama</label>
                                    <vs-input v-model="infoCitas.QuienLlama" type="text" class="w-full"
                                        :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" />
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2  p-1">
                                <ValidationProvider name="TelefonoLlama" rules="numero_entero|max:10"
                                    v-slot="{ errors }">
                                    <label style="font-size:12px">Telefono</label>
                                    <vs-input v-model="infoCitas.TelefonoLlama" type="text" class="w-full"
                                        :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" />
                                </ValidationProvider>
                            </div>
                        </div>
                        <vs-divider position="left"></vs-divider>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                                <ValidationProvider name="IdCliente" rules="required" v-slot="{ errors }"
                                    class="required">
                                    <SM-Buscar v-model="infoCitas.IdCliente" label="Cliente"
                                        api="app/citas/BusquedaClientes"
                                        :api_filtro="{ Opcion: 'C', SubOpcion: '1', TipoCita: '1' }"
                                        :api_campos="['IdCliente', 'Nombre1', 'Nombre2', 'Apellido1', 'Apellido2', 'FechaNacimiento', 'Planes', 'Historial', 'FechaUltimaCita', '#NombreCompleto']"
                                        :api_titulos="['IdCliente', 'Nombre1', 'Nombre2', 'Apellido1', 'Apellido2', 'FechaNacimiento', '#Planes', '#Historial', '#Ultima Cita Presentada', '#NombreCompleto']"
                                        api_campo_respuesta="IdCliente" :callback_buscar="Consulta().ContratoCliente"
                                        :danger="errors.length > 0" api_campo_respuesta_mostrar="NombreCompleto"
                                        :callback_nuevo="Agregar().AgregarCliente"
                                        :dangertext="(errors.length > 0) ? errors[0] : null" :disabled_editar="true"
                                        :disabled_nuevo="!permisos.CrearCliente" />
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                                <label style="font-size:12px">Horarios Disponibles (24 hrs)</label>
                                <v-select v-model="selectHorarioDisponible" label="Hora"
                                    :options="listaHorariosDisponibles" :clearable="false" />
                            </div>
                            <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                                <ValidationProvider name="MotivoCita" rules="required|numero_min:1" class="required">
                                    <label style="font-size:12px">Motivo Cita</label>
                                    <v-select v-model="selectMotivoCita" label="Nombre" :options="listaMotivoCita"
                                        :clearable="false" />
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                                <SM-Buscar v-model="infoCitas.IdContrato" label="Contrato" :api="listaContratosCliente"
                                    :api_campos="['NombreContrato', 'NombrePlan', 'NombreAfiliado']"
                                    :api_titulos="['Nombre Contrato', 'Nombre Plan', 'Nombre Afiliado']"
                                    :api_preload="true" api_campo_respuesta="IdContrato" :disabled_texto="verContratos"
                                    :disabled_busqueda="verContratos" api_campo_respuesta_mostrar="NombrePlan"
                                    :callback_cancelar="Otros().QuitarContrato"
                                    :callback_buscar="Consulta().ValidarContratoCliente" />
                            </div>
                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full  p-1">
                                <ValidationProvider name="Comentario" v-slot="{ errors }">
                                    <label>Comentario</label>
                                    <vs-textarea counter="150" :counter-danger.sync="counterDanger" class="w-full"
                                        v-model="infoCitas.Comentario" style="height: 275px;" rows="7"
                                        :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" required />
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full h-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                                <vs-button height="100px" class="w-full mr-1 mt-5" color="success"
                                    @click="Agregar().GuardarCita(invalid, false)">
                                    Guardar
                                </vs-button>
                            </div>
                            <div class="w-full h-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                                <vs-button class="w-full mr-1 mt-5" color="danger" @click="Otros().AbrirVentana(2)">
                                    Cancelar
                                </vs-button>
                            </div>
                        </div>
                    </form>
                </ValidationObserver>
            </div>
            <div v-if="ventana.opcion == 4">
                <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                    <form method="post" @submit.prevent="handleSubmit(Agregar().BloquearHorarios(invalid))">
                        <div class="flex flex-wrap mb-1">
                            <div class=" w-full h-full md:w-5/6 lg:w-5/6 xl:w-5/6 p-1 p-1"
                                v-if="infoHorarios.bloquearDesbloquear">
                                <ValidationProvider rules="required" v-slot="{ errors }" class="required">
                                    <SM-Buscar class="w-full" label="Horario a bloquear"
                                        api="app/citas/ListarHorariosDisponibles" :api_campos="['Hora']"
                                        :api_titulos="['Hora']" v-model="infoHorarios.Horas"
                                        :api_filtro="{ 'IdAgendaClinica': infoRolClinica.IdAgendaClinica }"
                                        api_campo_respuesta="Hora" api_campo_respuesta_mostrar="Hora"
                                        :api_preload="true" :disabled_texto="true" :mostrar_busqueda="true"
                                        :callback_buscar="null" :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" :api_multiselect="true" />
                                </ValidationProvider>
                            </div>
                            <div class=" w-full h-full md:w-5/6 lg:w-5/6 xl:w-5/6 p-1 p-1"
                                v-if="!infoHorarios.bloquearDesbloquear">
                                <ValidationProvider rules="required" v-slot="{ errors }" class="required">
                                    <SM-Buscar class="w-full" label="Horario a desbloquear"
                                        api="app/citas/HorariosNoDisponibles" :api_campos="['#IdHorario', 'Horario']"
                                        :api_titulos="['Horario']" v-model="infoHorarios.Horas"
                                        :api_filtro="{ 'IdAgendaClinica': infoRolClinica.IdAgendaClinica }"
                                        api_campo_respuesta="Horario" api_campo_respuesta_mostrar="Horario"
                                        :api_preload="true" :disabled_texto="true" :mostrar_busqueda="true"
                                        :callback_buscar="null" :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" :api_multiselect="true" />
                                </ValidationProvider>
                            </div>
                            <div class="w-full h-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-8">
                                <vs-switch color="success" v-model="infoHorarios.bloquearDesbloquear"
                                    @click="Otros().CambioSwitch()">
                                    <span slot="on">Bloquear</span>
                                    <span slot="off">Desbloquear</span>
                                </vs-switch>
                            </div>
                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full  p-1">
                                <ValidationProvider name="Comentario" rules="required" v-slot="{ errors }"
                                    class="required">
                                    <label style="font-size:12px">Comentario</label>
                                    <vs-textarea counter="150" :counter-danger.sync="counterDanger" class="w-full"
                                        v-model="infoHorarios.Comentario" style="height: 100px;" rows="3"
                                        :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" required />
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full h-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                                <vs-button height="100px" class="w-full mr-1 mt-5" color="success"
                                    @click="Agregar().BloquearHorarios(invalid)">
                                    Guardar
                                </vs-button>
                            </div>
                            <div class="w-full h-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                                <vs-button height="100px" class="w-full mr-1 mt-5" color="danger"
                                    @click="Otros().AbrirVentana(2)">
                                    Cancelar
                                </vs-button>
                            </div>
                        </div>
                    </form>
                </ValidationObserver>
            </div>
            <div v-if="ventana.opcion == 5">
                <div class="flex flex-wrap mb-1">
                    <div class="w-full p-1">
                        <vs-button title="Editar Cita" color="primary" icon-pack="fas" icon="fa-edit" class="mr-1"
                            style="display:inline-block"
                            :disabled="infoDetalleCita.ESTADO !== '1' || !permisos.EditarCita"
                            @click="Otros().AbrirVentana(9)" />
                        <vs-button title="Cancelar Cita" color="danger" icon-pack="fas" icon="fa-trash" class="mr-1"
                            style="display:inline-block"
                            :disabled="infoDetalleCita.ESTADO !== '1' || !permisos.CancelarCita"
                            @click="Otros().AbrirVentana(6)" />
                        <vs-button title="Confirmar Cita" color="success" icon-pack="fas" icon="fa-check" class="mr-1"
                            style="display:inline-block"
                            :disabled="infoDetalleCita.ConfirmarCita !== '1' || !permisos.ConfirmarCita"
                            @click="Otros().AbrirVentana(11)" />
                        <vs-button title="Cambiar Paciente" color="warning" icon-pack="fas" icon="fa-user" class="mr-1"
                            style="display:inline-block"
                            :disabled="infoDetalleCita.ESTADO !== '4' || !permisos.CambiarCliente"
                            @click="Otros().AbrirVentana(13)" />
                    </div>
                    <vs-divider position="left"></vs-divider>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Base: {{ this.infoDetalleCita.NombreBase }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Clínica: {{ this.infoDetalleCita.NombreClinica }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Médico: {{ this.infoDetalleCita.NombreMedico
                            }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">ID Agenda Clínica: {{ this.infoDetalleCita.IdAgendaClinica
                            }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Especialidad Ajeno: {{ this.infoDetalleCita.NombreSubEspecialidad
                            }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Status Cita: {{ this.infoDetalleCita.NombreStatus }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Paciente: {{ this.infoDetalleCita.NombrePaciente }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Fecha: {{ this.infoDetalleCita.NombreDia + ', ' +
            this.infoDetalleCita.Fecha }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Hora Cita (24 hrs): {{ this.infoDetalleCita.HoraCita }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Teléfono Celular/Domicilio: {{
            this.infoDetalleCita.TelefonoCelular
        }}/{{
                this.infoDetalleCita.TelefonoDomicilio }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Monto Copago: Q.{{ this.infoDetalleCita.Monto }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Tipo Consulta: {{ this.infoDetalleCita.NombreTipoCita }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Motivo Cita {{ this.infoDetalleCita.Motivo }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Cita Inicial: {{ this.infoDetalleCita.IdCitaInicial }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">ID Cita: {{ this.infoDetalleCita.IdCita }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Quién llama: {{ this.infoDetalleCita.PersonaLlama }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Teléfono Llama: {{ this.infoDetalleCita.TelefonoLlama }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Usuario Crea Cita: {{ this.infoDetalleCita.UsuarioCreaCita
                            }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Usuario Modifica Cita: {{ this.infoDetalleCita.UsuarioModificaCita
                            }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Usuario Confirma Cita: {{ this.infoDetalleCita.UsuarioConfirma
                            }}</label>
                    </div>
                    <vs-divider position="left"></vs-divider>
                    <div class="w-full md:w-1/1 lg:w-1/1 xl:w-1/1 p-1">
                        <label style="font-size:12px">Comentario: {{ this.infoDetalleCita.Comentario }}</label>
                    </div>
                </div>
            </div>
            <div v-if="ventana.opcion == 6">
                <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                    <form method="post" @submit.prevent="handleSubmit(Modificar().CancelarCita(invalid))">
                        <div class="flex flex-wrap mb-1">
                            <div class=" w-full">
                                <ValidationProvider name="MotivoCita" rules="required|numero_min:1" class="required">
                                    <label style="font-size:12px">Motivo Cancelación</label>
                                    <v-select v-model="selectMotivoCitaCancela" label="Nombre"
                                        :options="listaMotivoCitaCancela" :clearable="false" />
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full  p-1">
                                <ValidationProvider name="Comentario" rules="required" v-slot="{ errors }"
                                    class="required">
                                    <label style="font-size:12px">Comentario</label>
                                    <vs-textarea counter="150" :counter-danger.sync="counterDanger" class="w-full"
                                        v-model="infoCancelaCita.Comentario" style="height: 100px;" rows="3"
                                        :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" required />
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full h-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                                <vs-button height="100px" class="w-full mr-1 mt-5" color="success"
                                    @click="Modificar().CancelarCita(invalid)">
                                    Guardar
                                </vs-button>
                            </div>
                            <div class="w-full h-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                                <vs-button height="100px" class="w-full mr-1 mt-5" color="danger"
                                    @click="Otros().AbrirVentana(5)">
                                    Cancelar
                                </vs-button>
                            </div>
                        </div>
                    </form>
                </ValidationObserver>
            </div>
            <div v-if="ventana.opcion == 7">
                <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                    <form method="post" @submit.prevent="handleSubmit(Modificar().CancelarRolClinica(invalid))">
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full  p-1">
                                <ValidationProvider name="Comentario" rules="required" v-slot="{ errors }"
                                    class="required">
                                    <label style="font-size:12px">Comentario</label>
                                    <vs-textarea counter="150" :counter-danger.sync="counterDanger" class="w-full"
                                        v-model="infoCancelaRolClinica.Observaciones" style="height: 100px;" rows="3"
                                        :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" required />
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full h-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                                <vs-button height="100px" class="w-full mr-1 mt-5" color="success"
                                    @click="Modificar().CancelarRolClinica(invalid)">
                                    Guardar
                                </vs-button>
                            </div>
                            <div class="w-full h-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                                <vs-button height="100px" class="w-full mr-1 mt-5" color="danger"
                                    @click="Otros().AbrirVentana(2)">
                                    Cancelar
                                </vs-button>
                            </div>
                        </div>
                    </form>
                </ValidationObserver>
            </div>
            <div v-if="ventana.opcion == 8">
                <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                    <form method="post" @submit.prevent="handleSubmit(Agregar().AgregarRol(0, invalid))">
                        <div class="flex flex-wrap mb-1">
                            <div class=" w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                                <label style="font-size:12px">Base</label>
                                <v-select v-model="selectBaseVentana" label="Nombre" :options="listaBases"
                                    :clearable="false"
                                    @input="Otros().ChangeHospital('listarClinicasVentana', 'selectClinicaVentana', true)" />
                            </div>
                            <div class=" w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                                <label style="font-size:12px">Clínica</label>
                                <v-select v-model="selectClinicaVentana" label="Nombre" :options="listarClinicasVentana"
                                    :clearable="false" />
                            </div>
                            <div class=" w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                                <ValidationProvider name="Medico" rules="required|numero_entero|numero_min:1"
                                    v-slot="{ errors }" class="required">
                                    <SM-Buscar v-model="info.Medico" label="Código del Médico"
                                        api="app/citas/Busqueda_Ajenos"
                                        :api_filtro="{ Opcion: 'C', SubOpcion: '1', Codigo: infoRolClinica.IdMedico }"
                                        :api_campos="['Codigo', 'Nombre', 'Apellido', 'Corporativo', 'Tipo']"
                                        :api_titulos="['Codigo', 'Nombre', 'Apellido', 'Corporativo', 'Tipo']"
                                        api_campo_respuesta="Codigo" :callback_buscar="Consulta().Consulta_Ajenos"
                                        :callback_nuevo="null" :callback_editar="null"
                                        :callback_cancelar="Otros().Limpiar_Datos" :disabled_texto="true"
                                        :danger="errors.length > 0" api_campo_respuesta_mostrar="Nombre"
                                        :dangertext="(errors.length > 0) ? errors[0] : null" :disabled_editar="false" />
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class=" w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                                <ValidationProvider name="FechaHasta" rules="required" class="required">
                                    <label style="font-size:12px">Fecha</label>
                                    <flat-pickr class="w-full" v-model="info.FechaDesde"
                                        :config="configFromdateTimePicker" />
                                </ValidationProvider>
                            </div>
                            <div class=" w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                                <ValidationProvider name="HoraFin" rules="required" class="required">
                                    <label style="font-size:12px">Hora Inicio (24 hrs)</label>
                                    <flat-pickr class="w-full" :config="configTimePicker" v-model="info.HoraInicio" />
                                </ValidationProvider>
                            </div>
                            <div class=" w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                                <ValidationProvider name="HoraFin" rules="required" class="required">
                                    <label style="font-size:12px">Hora Fin (24 hrs)</label>
                                    <flat-pickr class="w-full" :config="configTimePicker" v-model="info.HoraFin" />
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full  p-1">
                                <ValidationProvider name="Comentario" rules="required" v-slot="{ errors }"
                                    class="required">
                                    <label style="font-size:12px">Observaciones</label>
                                    <vs-textarea counter="200" :counter-danger.sync="counterDanger" class="w-full"
                                        v-model="info.Observaciones" style="height: 175px;" rows="7"
                                        :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" required />
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full h-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                                <vs-button height="100px" class="w-full mr-1 mt-5" color="success"
                                    @click="Agregar().AgregarRol(0, invalid)">
                                    Guardar
                                </vs-button>
                            </div>
                            <div class="w-full h-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                                <vs-button height="100px" class="w-full mr-1 mt-5" color="danger"
                                    @click="Otros().AbrirVentana(2)">
                                    Cancelar
                                </vs-button>
                            </div>
                        </div>
                    </form>
                </ValidationObserver>
            </div>
            <div v-if="ventana.opcion == 9">
                <div class="flex flex-wrap mb-1">
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4 p-1">
                        <label style="font-size:12px">Base: {{ this.infoDetalleCita.NombreBase }}</label>
                    </div>
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4 p-1">
                        <label style="font-size:12px">Clínica: {{ this.infoDetalleCita.NombreClinica }}</label>
                    </div>
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4 p-1">
                        <label style="font-size:12px">Médico: {{ this.infoDetalleCita.NombreMedico }}</label>
                    </div>
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4 p-1">
                        <label style="font-size:12px">Fecha: {{ this.infoDetalleCita.NombreDia + ', ' +
            this.infoDetalleCita.Fecha }}</label>
                    </div>
                </div>
                <vs-divider position="left"></vs-divider>
                <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                    <form method="post" @submit.prevent="handleSubmit(Modificar().ModificarCita(invalid))">
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2  p-1">
                                <ValidationProvider name="QuienLlama" rules="alpha_spaces|max:100" v-slot="{ errors }">
                                    <label style="font-size:12px">Quién llama</label>
                                    <vs-input v-model="infoCitas.QuienLlama" type="text" class="w-full"
                                        :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" />
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2  p-1">
                                <ValidationProvider name="TelefonoLlama" rules="numero_entero|max:10"
                                    v-slot="{ errors }">
                                    <label style="font-size:12px">Telefono</label>
                                    <vs-input v-model="infoCitas.TelefonoLlama" type="text" class="w-full"
                                        :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" />
                                </ValidationProvider>
                            </div>
                        </div>
                        <vs-divider position="left"></vs-divider>
                        <div class="flex flex-wrap mb-1">
                            <div class=" w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                                <label style="font-size:12px">Base</label>
                                <v-select v-model="selectBaseVentana" label="Nombre" :options="listaBases"
                                    :clearable="false"
                                    @input="Otros().ChangeHospital('listarClinicasVentana', 'selectClinicaVentana', true, true)" />
                            </div>
                            <div class=" w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                                <ValidationProvider name="Clinica" rules="required" class="required">
                                    <label style="font-size:12px">Clínica</label>
                                    <v-select v-model="selectClinicaVentana" label="Nombre"
                                        :options="listarClinicasVentana" :clearable="false"
                                        @input="Otros().ChangeClinica(true)" />
                                </ValidationProvider>
                            </div>
                            <div class=" w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                                <ValidationProvider name="Médico" rules="required" v-slot="{ errors }" class="required">
                                    <SM-Buscar v-model="infoCitas.IdAgendaClinica" label="Médico"
                                        api="app/citas/MedicosAgenda" :api_cache="true"
                                        :api_filtro="{ Fecha: infoDetalleCita.Fecha, Base: infoCitas.Base, Clinica: infoCitas.Clinica }"
                                        :api_campos="['#IdAgendaClinica', 'Codigo', 'Nombre', 'Apellido', 'Corporativo', 'Tipo', 'NombreSubEspecialidad', 'Horarios', 'HorariosDisponibles']"
                                        :api_titulos="['#IdAgendaClinica', 'Codigo', 'Nombre', 'Apellido', 'Corporativo', 'Tipo', '#Especialidad', '#Horarios', '#HorariosDisponibles']"
                                        api_campo_respuesta="IdAgendaClinica" api_campo_respuesta_mostrar="Nombre"
                                        :callback_buscar="Consulta().HorariosMedico" :api_preload="true"
                                        :disabled_texto="true" :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null"
                                        :mostrar_busqueda="true" />
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class=" w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                                <ValidationProvider name="FechaHasta" rules="required" class="required">
                                    <label style="font-size:12px">Fecha</label>
                                    <flat-pickr class="w-full" v-model="infoCitas.Fecha"
                                        :config="configFromdateTimePicker" @input="Otros().ChangeDate()" />
                                </ValidationProvider>
                            </div>
                            <div class=" w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                                <ValidationProvider name="HorarioDisponible" rules="required" class="required">
                                    <label style="font-size:12px">Horarios Disponibles (24 hrs)</label>
                                    <v-select v-model="selectHorarioDisponible" label="Hora"
                                        :options="listaHorariosDisponibles" :clearable="false" />
                                </ValidationProvider>
                            </div>
                            <div class=" w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                                <ValidationProvider name="Paciente" rules="alpha_spaces|max:100" v-slot="{ errors }">
                                    <label style="font-size:12px">Paciente</label>
                                    <vs-input v-model="infoDetalleCita.NombrePaciente" type="text" class="w-full"
                                        :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" disabled />
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full  p-1">
                                <ValidationProvider name="Comentario" rules="required" v-slot="{ errors }"
                                    class="required">
                                    <label style="font-size:12px">Comentario</label>
                                    <vs-textarea counter="150" :counter-danger.sync="counterDanger" class="w-full"
                                        v-model="infoCitas.Comentario" style="height: 225px;" rows="7"
                                        :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" required />
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full h-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                                <vs-button height="100px" class="w-full mr-1 mt-5" color="success"
                                    @click="Modificar().ModificarCita(invalid)">
                                    Guardar
                                </vs-button>
                            </div>
                            <div class="w-full h-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                                <vs-button height="100px" class="w-full mr-1 mt-5" color="danger"
                                    @click="Otros().AbrirVentana(5)">
                                    Cancelar
                                </vs-button>
                            </div>
                        </div>
                    </form>
                </ValidationObserver>
            </div>
            <div v-if="ventana.opcion == 10">
                <div class="flex flex-wrap mb-1">
                    <PacienteNuevo ref="PacienteNuevo" @mensaje-respuesta="notificacion">
                    </PacienteNuevo>
                    <div class="w-full h-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                        <vs-button height="100px" class="w-full mr-1 mt-5" color="success"
                            @click="Agregar().GuardarCliente()">
                            Guardar
                        </vs-button>
                    </div>
                    <div class="w-full h-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                        <vs-button class="w-full mr-1 mt-5" color="danger" @click="Otros().AbrirVentana(3)">
                            Cancelar
                        </vs-button>
                    </div>
                </div>
            </div>
            <div v-if="ventana.opcion == 11">
                <div class="flex flex-wrap mb-1">
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Id Cita: {{ infoDetalleCita.IdCita }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Hora Cita: {{ infoDetalleCita.HoraCita }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Nombre Paciente: {{ infoDetalleCita.NombrePaciente
                            }}</label>
                    </div>
                </div>
                <div class="flex flex-wrap mb-1">
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Comentario: {{ infoDetalleCita.Comentario }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px;text-align: center;">Citas por año<br>Citas / Cupones: {{
            listaCitaContadores[0].CitasAnuales }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Citas acumuladas<br>Citas / Cupones: {{
            listaCitaContadores[0].CitasAcumuladas }}</label>
                    </div>
                </div>
                <div class="flex flex-wrap mb-1">
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Número Adhesión: {{ infoDetalleCita.NumeroAdhesion }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Médico: {{ this.infoDetalleCita.NombreMedico }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Especialidad: {{ this.infoDetalleCita.NombreClinica }}</label>
                    </div>
                </div>
                <div class="flex flex-wrap mb-1">
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Monto: Q.{{ this.infoDetalleCita.Monto }}</label>
                    </div>
                </div>
                <vs-divider position="left"></vs-divider>
                <ValidationObserver ref="formValidateConfirmar" v-slot="{ invalid, handleSubmit }" mode="lazy">
                    <form method="post" @submit.prevent="handleSubmit(Agregar().AgregarConfirmacion(invalid))">
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2  p-1">
                                <ValidationProvider name="TipoCita" rules="required" class="required">
                                    <label style="font-size:12px">Tipo Cita</label>
                                    <v-select v-model="selectTipoCita" label="Descripcion" :options="listaTipoCita"
                                        :clearable="false" @input="Otros().ChangeTipoCita()"
                                        :disabled="bloquearConfirmar" />
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2  p-1">
                                <ValidationProvider name="Póliza" rules="required" class="required">
                                    <label style="font-size:12px">Póliza</label>
                                    <v-select v-model="selectTipoAfiliado" label="Descripcion"
                                        :options="listaTipoAfiliado" :clearable="false"
                                        :disabled="listaTipoAfiliado.length == 0 || bloquearConfirmar" />
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2  p-1">
                                <ValidationProvider name="Propiedades" rules="required" class="required">
                                    <label style="font-size:12px">Propiedades</label>
                                    <v-select v-model="selectPropiedad" label="Descripcion" :options="listaPropiedades"
                                        :clearable="false" :disabled="bloquearConfirmar" />
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2  p-1">
                                <ValidationProvider name="NumeroAutorizacion" rules="alpha_num|max:15"
                                    v-slot="{ errors }">
                                    <label style="font-size:12px">Número Autorización</label>
                                    <vs-input v-model="infoConfirmar.NumeroAutorizacion" type="text" class="w-full"
                                        :danger="errors.length > 0" ref="iNumeroAutorizacion"
                                        :danger-text="(errors.length > 0) ? errors[0] : null"
                                        :disabled="bloquearConfirmar" />
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full  p-1">
                                <ValidationProvider name="Comentario" rules="required|max:150" v-slot="{ errors }"
                                    class="required">
                                    <label style="font-size:12px">Comentario</label>
                                    <vs-textarea counter="150" :counter-danger.sync="counterDanger" class="w-full"
                                        v-model="infoConfirmar.Comentario" style="height: 200px;" rows="6"
                                        :danger="errors.length > 0" ref="iComentario"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" required
                                        :disabled="bloquearConfirmar" />
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full h-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                                <vs-button class="w-full mr-1 mt-5" color="success"
                                    @click="Agregar().AgregarConfirmacion(invalid, 'N')" :disabled="bloquearConfirmar">
                                    Confirmar
                                </vs-button>
                            </div>
                            <div class="w-full h-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                                <vs-button class="w-full mr-1 mt-5" @click="Agregar().AgregarConfirmacion(invalid, 'S')"
                                    :disabled="bloquearConfirmar">
                                    Contesia
                                </vs-button>
                            </div>
                            <div class="w-full h-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                                <vs-button height="100px" class="w-full mr-1 mt-5" color="danger"
                                    @click="Otros().AbrirVentana(5)" :disabled="bloquearConfirmar">
                                    Cancelar
                                </vs-button>
                            </div>
                        </div>
                    </form>
                </ValidationObserver>
            </div>
            <div v-if="ventana.opcion == 12">
                <div class="flex flex-wrap mb-1">
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4 p-1">
                        <label style="font-size:12px">Base: {{ this.infoRolClinica.Base }}</label>
                    </div>
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4 p-1">
                        <label style="font-size:12px">Clínica: {{ this.infoRolClinica.Clinica }}</label>
                    </div>
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4 p-1">
                        <label style="font-size:12px">Médico: {{ this.infoRolClinica.Medico }}</label>
                    </div>
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4 p-1">
                        <label style="font-size:12px">Fecha: {{ this.infoRolClinica.NombreDia + ', '+this.infoRolClinica.Fecha }}</label>
                    </div>
                </div>
                <vs-divider position="left"></vs-divider>
                <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                    <form method="post" @submit.prevent="handleSubmit(Agregar().GuardarCita(invalid, true))">
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2  p-1">
                                <ValidationProvider name="IdCliente" rules="required" v-slot="{ errors }"
                                    class="required">
                                    <SM-Buscar v-model="infoCitas.IdCliente" label="Cliente"
                                        api="app/citas/BusquedaClientes"
                                        :api_filtro="{ Opcion: 'C', SubOpcion: '1', TipoCita: '1' }"
                                        :api_campos="['IdCliente', 'Nombre1', 'Nombre2', 'Apellido1', 'Apellido2', 'FechaNacimiento', 'Planes', 'Historial', 'FechaUltimaCita', '#NombreCompleto']"
                                        :api_titulos="['IdCliente', 'Nombre1', 'Nombre2', 'Apellido1', 'Apellido2', 'FechaNacimiento', '#Planes', '#Historial', '#Ultima Cita Presentada', '#NombreCompleto']"
                                        api_campo_respuesta="IdCliente" :callback_buscar="Consulta().ContratoCliente"
                                        :danger="errors.length > 0" api_campo_respuesta_mostrar="NombreCompleto"
                                        :callback_nuevo="Agregar().AgregarCliente"
                                        :dangertext="(errors.length > 0) ? errors[0] : null" :disabled_editar="true"
                                        :disabled_nuevo="!permisos.CrearCliente" />
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2  p-1">
                                <SM-Buscar v-model="infoCitas.IdContrato" label="Contrato" :api="listaContratosCliente"
                                    :api_campos="['NombreContrato', 'NombrePlan', 'NombreAfiliado']"
                                    :api_titulos="['Nombre Contrato', 'Nombre Plan', 'Nombre Afiliado']"
                                    :api_preload="true" api_campo_respuesta="IdContrato" :disabled_texto="verContratos"
                                    :disabled_busqueda="verContratos" api_campo_respuesta_mostrar="NombrePlan"
                                    :callback_cancelar="Otros().QuitarContrato"
                                    :callback_buscar="Consulta().ValidarContratoCliente" />
                            </div>
                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full  p-1">
                                <ValidationProvider name="Comentario" v-slot="{ errors }">
                                    <label>Comentario</label>
                                    <vs-textarea counter="150" :counter-danger.sync="counterDanger" class="w-full"
                                        v-model="infoCitas.Comentario" style="height: 275px;" rows="7"
                                        :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" required />
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full h-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                                <vs-button height="100px" class="w-full mr-1 mt-5" color="success"
                                    @click="Agregar().GuardarCita(invalid, true)">
                                    Guardar
                                </vs-button>
                            </div>
                            <div class="w-full h-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                                <vs-button class="w-full mr-1 mt-5" color="danger" @click="Otros().AbrirVentana(2)">
                                    Cancelar
                                </vs-button>
                            </div>
                        </div>
                    </form>
                </ValidationObserver>
            </div>
            <div v-if="ventana.opcion == 13">
                <vs-divider position="center">Datos Actuales</vs-divider>
                <div class="flex flex-wrap mb-1">
                    <div class="w-full md:w-3/12 lg:w-3/12 xl:w-3/12 p-1">
                        <label style="font-size:12px">IdCliente: {{ this.infoDetalleCita.IdCliente }}</label>
                    </div>
                    <div class="w-full md:w-6/12 lg:w-6/12 xl:w-6/12 p-1">
                        <label style="font-size:12px">Nombre Cliente: {{ this.infoDetalleCita.NombrePaciente }}</label>
                    </div>
                    <div class="w-full md:w-3/12 lg:w-3/12 xl:w-3/12 p-1">
                        <label style="font-size:12px">Fecha Nacimiento: {{ this.infoDetalleCita.FechaNacimiento
                            }}</label>
                    </div>
                </div>
                <vs-divider position="center">Datos Nuevos</vs-divider>
                <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                    <form method="post" @submit.prevent="handleSubmit(Agregar().GuardarCita(invalid, true))">
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                <ValidationProvider name="ClienteNuevo" rules="required" v-slot="{ errors }"
                                    class="required">
                                    <SM-Buscar v-model="ClienteNuevo.IdCliente" label="Cliente"
                                        api="app/citas/BusquedaClientes"
                                        :api_filtro="{ Opcion: 'C', SubOpcion: '1', TipoCita: '1' }"
                                        :api_campos="['IdCliente', 'Nombre1', 'Nombre2', 'Apellido1', 'Apellido2', 'FechaNacimiento', 'Planes', 'Historial', 'FechaUltimaCita', '#NombreCompleto']"
                                        :api_titulos="['IdCliente', 'Nombre1', 'Nombre2', 'Apellido1', 'Apellido2', 'FechaNacimiento', '#Planes', '#Historial', '#Ultima Cita Presentada', '#NombreCompleto']"
                                        api_campo_respuesta="IdCliente"
                                        :callback_buscar="Consulta().ContratoClienteCambio"
                                        api_campo_respuesta_mostrar="NombreCompleto" :danger="errors.length > 0"
                                        :dangertext="(errors.length > 0) ? errors[0] : null" :disabled_editar="true"
                                        :disabled_nuevo="!permisos.CrearCliente" />
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                <SM-Buscar v-model="infoCitas.IdContrato" label="Contrato" :api="listaContratosCliente"
                                    :api_campos="['NombreContrato', 'NombrePlan', 'NombreAfiliado']"
                                    :api_titulos="['Nombre Contrato', 'Nombre Plan', 'Nombre Afiliado']"
                                    :api_preload="true" api_campo_respuesta="IdContrato" :disabled_texto="verContratos"
                                    :disabled_busqueda="verContratos" api_campo_respuesta_mostrar="NombrePlan"
                                    :callback_cancelar="Otros().QuitarContrato"
                                    :callback_buscar="Consulta().ValidarContratoClienteCambio" />
                            </div>
                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full h-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                                <vs-button height="100px" class="w-full mr-1 mt-5" color="success"
                                    @click="Agregar().CambiarCliente(invalid)">
                                    Guardar
                                </vs-button>
                            </div>
                            <div class="w-full h-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                                <vs-button class="w-full mr-1 mt-5" color="danger" @click="Otros().AbrirVentana(5)">
                                    Cancelar
                                </vs-button>
                            </div>
                        </div>
                    </form>
                </ValidationObserver>
            </div>
        </vs-popup>
        <div class="flex flex-wrap mb-1" v-if="permisos.Visualizar">
            <div class="w-full h-full p-1">
                <DxScheduler time-zone="America/Guatemala" :data-source="data" :current-date="currentDate"
                    :start-day-hour="6" :end-day-hour="19" :show-all-day-panel="false" :views="views"
                    current-view="agenda" @option-changed="handlePropertyChange" :shade-until-current-time="true"
                    @appointmentClick="okClicked2" max-appointments-per-cell="unlimited"
                    @appointment-form-opening="onAppointmentFormOpening" :editing="editing" :group-by-date="groupByDate"
                    :groups="groups" ref="calendario">
                    <DxResource :data-source="selectMedico" field-expr="IdMedico" />
                </DxScheduler>
            </div>
        </div>
    </div>
</template>

<script>
import vSelect from 'vue-select'
import flatPickr from 'vue-flatpickr-component';
import 'flatpickr/dist/flatpickr.css';
import {
    Spanish as SpanishLocale
} from 'flatpickr/dist/l10n/es.js';
import esMessages from "devextreme/localization/messages/es.json";
import {
  locale,
  loadMessages
} from "devextreme/localization";
import PacienteNuevo from '@/components/sermesa/modules/paciente/DatosGeneralesPaciente.vue'

export default {
    data() {
        return {
            permisos: { Visualizar: null, CrearRol: null, EditarRol: null, AnularRol: null, BloquearHorarios: null, CrearCita: null, CrearCliente: null, EditarCita: null, CancelarCita: null, ConfirmarCita: null, IntercalarCita: null, CambiarCliente: null },
            tituloModulo: "Roles Clinicas",
            vistaCalendario: 'agenda',
            data: [],
            listaBases: [],
            listarClinicas: [],
            listarClinicasVentana: [],
            listaMedicos: [],
            selectMedico: [],
            views: ['agenda', 'day'],
            groups: ["IdMedico"],
            currentDate: new Date(),
            objetoConsultaRoles: {
                FechaVista: new Date(),
                Clinica: null
            },
            objetoConsultaCitas: {
                FechaVista: new Date(),
                Clinica: null
            },
            objetoConsultaClinicas: {
                Base: null
            },
            selectBase: {
                Codigo: null,
                Nombre: null
            },
            selectClinica: {
                Codigo: null,
                Nombre: null
            },
            selectBaseVentana: {
                Codigo: null,
                Nombre: null
            },
            selectClinicaVentana: {
                Codigo: null,
                Nombre: null
            },
            ventana: {
                titulo: '',
                mostrar: false,
                opcion: 0
            },
            info: {
                IdAgendaClinica: 0,
                Base: null,
                Clinica: null,
                Medico: null,
                Especialidad: null,
                TipoGeneracion: null,
                FechaDesde: null,
                FechaHasta: null,
                Dias: null,
                MesAnio: null,
                HoraInicio: null,
                HoraFin: null,
                Observaciones: null,
                EsInsert: null
            },
            configFromdateTimePicker: {
                minDate: new Date(),
                locale: SpanishLocale,
                dateFormat: "m-Y",
            },
            configTimePicker: {
                enableTime: true,
                enableSeconds: false,
                noCalendar: true,
                time_24hr: true
            },
            listaGeneraciónRol: [{ Codigo: 1, Nombre: 'Por dia de la semana' }, { Codigo: 2, Nombre: 'Por rango de fecha' }],
            selectGeneraciónRol: {
                Codigo: 1,
                Nombre: 'Por dia de la semana'
            },
            groupByDate: false,
            infoRolClinica: {},
            listaHorariosDisponibles: [],
            selectHorarioDisponible: null,
            listaMotivoCita: [],
            selectMotivoCita: null,
            infoHorarios: { Horas: [], Comentario: null, Horarios: null, bloquearDesbloquear: true },
            counterDanger: false,
            infoCitas: { QuienLlama: null, IdCliente: null, IdContrato: null, Comentario: null, TelefonoLlama: null, ValidarContrato: true, IdCopago: null, IdPlan: null, Numeroadhesion: null, Fecha: null, HoraCita: null, IdAgendaClinica: null, MotivoCita: null },
            infoDetalleCita: {},
            listaContratosCliente: [],
            infoCopago: {},
            listaMotivoCitaCancela: [],
            selectMotivoCitaCancela: null,
            infoCancelaCita: { Comentario: null, IdStatus: null, IdCita: null },
            infoCancelaRolClinica: { Observaciones: null, IdAgendaClinica: null },
            verContratos: true,
            //Confirmar Citas
            listaTipoCita: [],
            selectTipoCita: null,
            listaTipoAfiliado: [],
            selectTipoAfiliado: null,
            listaPropiedades: [],
            selectPropiedad: null,
            infoConfirmar: { NumeroAutorizacion: '', Comentario: '' },
            listaCitaContadores: [],
            ClienteNuevo: { IdCliente: null },
            CambioPaciente: {},
            bloquearConfirmar: true
        }
    },
    computed: {
        sesion() {
            return this.$store.state.sesion
        },
        editing() {
            return {
                allowDragging: false,
                allowResizing: false,
                allowDeleting: false
            };
        },
        calendario: function () {
            return this.$refs["calendario"].instance;
        }
    },
    components: {
        'v-select': vSelect,
        flatPickr,
        PacienteNuevo
    },
    methods: {
        //=======================================================================================================
        // CONSULTA
        //=======================================================================================================
        Consulta: function () {
            return {
                ListarRolesClinicas: () => {
                    this.axios.post('/app/citas/ListarRolesClinicas', this.objetoConsultaRoles)
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.data = resp.data.json
                            }
                        })
                        .catch(() => {

                        })
                },
                ListarBases: () => {
                    this.axios.post('/app/citas/ListarBases', {})
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.listaBases = resp.data.json
                            }
                        })
                        .catch(() => {

                        })
                },
                ListarClinicas: (array, objeto, cargarData = true) => {
                    this.axios.post('/app/citas/ListarClinicas', this.objetoConsultaClinicas)
                        .then(resp => {
                            if (resp.data.codigo == 0) {

                                this[array] = resp.data.json
                                if (cargarData) {
                                    this[objeto].Codigo = resp.data.json[0].Codigo
                                    this[objeto].Nombre = resp.data.json[0].Nombre
                                }

                                if (objeto == 'selectClinica') {
                                    this.objetoConsultaRoles.Clinica = this.selectClinica.Codigo
                                    this.objetoConsultaCitas.Clinica = this.selectClinica.Codigo

                                    if (this.vistaCalendario == 'day') {
                                        this.Consulta().ListarCitas()
                                        this.Consulta().ListarMedicos()
                                    } else {
                                        this.Consulta().ListarRolesClinicas()
                                    }
                                }
                            }
                        })
                        .catch(() => {
                        })
                },
                Consulta_Ajenos: (datos) => {
                    this.info.Medico = datos.Codigo
                    this.info.Especialidad = datos.Especialidad
                },
                HorariosMedico: (datos) => {
                    this.Consulta().ListarHorariosDisponibles({ "IdAgendaClinica": datos.IdAgendaClinica, "IdCita": this.infoDetalleCita.IdCita })
                },
                ListarCitas: () => {
                    this.objetoConsultaCitas.Base = this.selectBase.Codigo
                    this.objetoConsultaCitas.Clinica = this.selectClinica.Codigo

                    this.axios.post('/app/citas/ListarCitas', this.objetoConsultaCitas)
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.data = resp.data.json
                            }
                        })
                        .catch(() => {

                        })
                },
                ListarMedicos: () => {
                    this.objetoConsultaCitas.Base = this.selectBase.Codigo
                    this.listaMedicos = []
                    this.selectMedico = []
                    this.axios.post('/app/citas/ListarMedicos', this.objetoConsultaCitas)
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.listaMedicos = resp.data.json
                            }
                        })
                        .catch(() => {

                        })
                },
                ListarHorariosDisponibles: (obj) => {
                    this.axios.post('/app/citas/ListarHorariosDisponibles', obj)
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.listaHorariosDisponibles = resp.data.json

                                if (obj.IdCita == 0) {
                                    this.selectHorarioDisponible = resp.data.json[0]
                                }
                            }
                        })
                        .catch(() => {

                        })
                },
                ListarMotivosCitas: () => {
                    this.axios.post('/app/citas/ListarMotivosCita', {})
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.listaMotivoCita = resp.data.json
                            }
                        })
                        .catch(() => {

                        })
                },
                ContratoCliente: (obj) => {
                    this.infoCitas.IdContrato = null
                    this.infoCitas.IdCopago = null
                    this.infoCitas.IdPlan = null
                    this.infoCitas.Numeroadhesion = null
                    this.infoCitas.Comentario = null

                    this.axios.post('/app/citas/BusquedaContratoCliente', { "IdCliente": obj.IdCliente })
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.listaContratosCliente = resp.data.json
                                this.listaContratosCliente.length == 0 ? this.verContratos = true : this.verContratos = false
                            }
                        })
                },
                ContratoClienteCambio: (obj) => {
                    this.ClienteNuevo = obj

                    if (this.infoDetalleCita.IdCliente == obj.IdCliente) {
                        this.$vs.notify({
                            title: "Error al seleccionar cliente",
                            text: "Debe seleccionar un cliente diferente",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                    } else {
                        this.axios.post('/app/citas/BusquedaContratoCliente', { "IdCliente": obj.IdCliente })
                            .then(resp => {
                                if (resp.data.codigo == 0) {
                                    this.listaContratosCliente = resp.data.json
                                    this.listaContratosCliente.length == 0 ? this.verContratos = true : this.verContratos = false
                                }
                            })
                    }
                },
                ValidarContratoCliente: (obj) => {
                    var fechaCitaPartes = this.infoRolClinica.Fecha.split('/')
                    var fechaCita = new Date(fechaCitaPartes[3] + '-' + fechaCitaPartes[2] + '-' + fechaCitaPartes[1]).getTime()

                    var fechaInicioCoberturaPartes = obj.FechaInicioCobertura.split('/')
                    var fechaInicioCobertura = new Date(fechaInicioCoberturaPartes[3] + '-' + fechaInicioCoberturaPartes[2] + '-' + fechaInicioCoberturaPartes[1]).getTime()

                    if (fechaInicioCobertura > fechaCita) {
                        if (obj.Status !== 'P') {
                            this.$vs.notify({
                                title: "Error al seleccionar contrato",
                                text: "La fecha de inicio de cobertura del afiliado aun no esta vigente.",
                                iconPack: 'feather',
                                icon: 'icon-alert-circle',
                                color: 'danger',
                                position: 'bottom-center'
                            })

                            this.infoCitas.IdContrato = null
                        }
                    } else {
                        this.infoCopago = {}
                        this.infoCopago.IdPlan = obj.IdPlan
                        this.infoCopago.IdContrato = obj.IdContrato
                        this.infoCopago.TipoHospital = this.infoRolClinica.TipoDeHospital
                        this.infoCopago.TipoEnMedax = this.infoRolClinica.TipoEnMedax
                        this.infoCopago.IdCliente = this.infoCitas.IdCliente
                        this.infoCopago.Numeroadhesion = obj.Numeroadhesion
                        
                        this.axios.post('/app/citas/BuscarCopagoCliente', this.infoCopago)
                            .then(resp => {
                                if(resp.data.codigo == 0 && resp.data.json[0].PermiteCita == 'N'){
                                    this.$vs.dialog({
                                        type: 'confirm',
                                        color: 'success',
                                        title: 'Autorización',
                                        acceptText: 'Aceptar',
                                        cancelText: "Cancelar",
                                        text: "Afiliado ha llegado a su máximo de citas permitidas en el año, para seguir gozando del precio preferencial solicite autorización. Presione aceptar para cobrar la cita a precio normal",
                                        accept: () => {
                                            this.infoCitas.IdContrato = null
                                            this.infoCitas.Numeroadhesion = null
                                            this.infoCitas.IdPlan = null
                                            this.infoCitas.IdCopago = null    
                                            this.infoCitas.ValidarContrato = false                                        
                                        },
                                        cancel: () => {
                                            this.infoCitas.IdContrato = null
                                            this.infoCitas.Numeroadhesion = null
                                            this.infoCitas.IdPlan = null
                                            this.infoCitas.IdCopago = null                                            

                                            this.$vs.notify({
                                                title: "Error al generar cita",
                                                text: "Cita no agendada.",
                                                iconPack: 'feather',
                                                icon: 'icon-alert-circle',
                                                color: 'danger',
                                                position: 'bottom-center'
                                            })
                                        }

                                    })
                                } else {
                                    this.infoCitas.IdPlan = obj.IdPlan
                                    this.infoCitas.Numeroadhesion = obj.Numeroadhesion
                                    this.infoCitas.ValidarContrato = true
                                    this.infoCitas.IdCopago = resp.data.json[0].IdCopago
                                    this.infoCitas.Comentario = "Valor de copago es de : Q" + Number(resp.data.json[0].Monto).toFixed(2)
                                }
                            })
                    }
                },
                ValidarContratoClienteCambio: (obj) => {
                    var fechaCitaPartes = this.infoDetalleCita.Fecha.split('/')
                    var fechaCita = new Date(fechaCitaPartes[3] + '-' + fechaCitaPartes[2] + '-' + fechaCitaPartes[1]).getTime()

                    var fechaInicioCoberturaPartes = obj.FechaInicioCobertura.split('/')
                    var fechaInicioCobertura = new Date(fechaInicioCoberturaPartes[3] + '-' + fechaInicioCoberturaPartes[2] + '-' + fechaInicioCoberturaPartes[1]).getTime()

                    if (fechaInicioCobertura > fechaCita) {
                        if (obj.Status !== 'P') {
                            this.$vs.notify({
                                title: "Error al seleccionar contrato",
                                text: "La fecha de inicio de cobertura del afiliado aun no esta vigente.",
                                iconPack: 'feather',
                                icon: 'icon-alert-circle',
                                color: 'danger',
                                position: 'bottom-center'
                            })

                            this.infoCitas.IdContrato = null
                        }
                    } else {
                        this.infoCopago = {}
                        this.infoCopago = obj
                    }
                },
                ListarMotivosCitaCancelar: () => {
                    this.axios.post('/app/citas/ListarMotivosCitaCancelar', {})
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.listaMotivoCitaCancela = resp.data.json
                            }
                        })
                        .catch(() => {

                        })
                },
                CargarDataRolClinica: () => {
                    this.objetoConsultaClinicas.Base = this.infoRolClinica.IdBase
                    this.Consulta().ListarClinicas('listarClinicasVentana', 'selectClinicaVentana', false)
                    this.info.Medico = this.infoRolClinica.IdMedico
                    this.info.HoraInicio = this.infoRolClinica.HoraInicio
                    this.info.HoraFin = this.infoRolClinica.HoraFin
                    //Cargar combo box
                    this.selectBaseVentana = {
                        Codigo: this.infoRolClinica.IdBase,
                        Nombre: this.infoRolClinica.Base
                    }

                    this.selectClinicaVentana = {
                        Codigo: this.infoRolClinica.IdClinica,
                        Nombre: this.infoRolClinica.Clinica
                    }

                    //Setear fecha en ventana
                    var date = this.infoRolClinica.Fecha.split('-')
                    date = date[2] + "-" + date[1] + "-" + date[0]
                    date = new Date(date)
                    date.setDate(date.getDate() + 1)

                    this.configFromdateTimePicker.minDate = new Date(date)

                    if (date.getTime() >= new Date().getTime()) {
                        this.configFromdateTimePicker.minDate = new Date()
                    }

                    this.info.FechaDesde = date
                    this.info.FechaHasta = date
                    this.info.FechaDesdeInicial = this.infoRolClinica.Fecha

                },
                CargarDataCitas: () => {
                    this.infoCitas = { QuienLlama: null, IdCliente: null, Comentario: null, TelefonoLlama: null, Fecha: null, HoraCita: null, IdAgendaClinica: null }
                    this.infoCitas.IdAgendaClinica = this.infoDetalleCita.IdAgendaClinica

                    this.objetoConsultaClinicas.Base = this.infoDetalleCita.IdBase
                    this.Consulta().ListarClinicas('listarClinicasVentana', 'selectClinicaVentana', false)
                    this.Consulta().ListarHorariosDisponibles({ "IdAgendaClinica": this.infoDetalleCita.IdAgendaClinica, "IdCita": this.infoDetalleCita.IdCita })



                    //Setear fecha en ventana
                    var date = this.infoDetalleCita.Fecha.split('-')
                    date = date[2] + "-" + date[1] + "-" + date[0]
                    date = new Date(date)
                    date.setDate(date.getDate() + 1)

                    this.configFromdateTimePicker.minDate = new Date(date)

                    if (date.getTime() >= new Date().getTime()) {
                        this.configFromdateTimePicker.minDate = new Date()
                    }

                    this.infoCitas.Fecha = date
                    this.infoCitas.IdCita = this.infoDetalleCita.IdCita
                    this.infoDetalleCita.FechaInicial = this.infoDetalleCita.Fecha
                    this.infoDetalleCita.FechaInicialM = date.getTime()
                    this.infoCitas.HoraCita = this.infoDetalleCita.HoraCita
                    this.infoCitas.QuienLlama = this.infoDetalleCita.PersonaLlama
                    this.infoCitas.TelefonoLlama = this.infoDetalleCita.TelefonoLlama
                    this.infoCitas.IdCliente = this.infoDetalleCita.IdCliente
                    this.infoCitas.Base = this.infoDetalleCita.IdBase
                    this.infoCitas.Clinica = this.infoDetalleCita.IdClinica
                    this.infoCitas.Comentario = this.infoDetalleCita.Comentario

                    this.selectBaseVentana = {
                        Codigo: this.infoCitas.Base,
                        Nombre: this.infoDetalleCita.NombreBase
                    }

                    this.selectClinicaVentana = {
                        Codigo: this.infoCitas.Clinica,
                        Nombre: this.infoDetalleCita.NombreClinica
                    }

                    this.selectHorarioDisponible = { Hora: null }
                    this.selectHorarioDisponible.Hora = this.infoDetalleCita.HoraCita
                },
                BusquedaCliente: (obj) => {
                    this.infoDetalleCita = obj
                    this.Otros().AbrirVentana(5)

                },
                ListarConfirmarCita: (url, lista, objeto = {}) => {
                    this[lista] = []
                    lista === 'listaTipoAfiliado' ? this.selectTipoAfiliado = null : null

                    this.axios.post('/app/citas/' + url, objeto)
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this[lista] = resp.data.json
                            }
                        })
                        .catch(() => {

                        })
                }
            }
        },
        //=======================================================================================================
        // Agregar
        //=======================================================================================================
        Agregar: function () {
            return {
                AgregarRol: (EsInsertV, isInvalid) => {
                    if (isInvalid) {
                        this.$vs.notify({
                            title: "Error al bloquear horario",
                            text: "Debe ingresar todos los campos",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                    } else {
                        this.info.TipoGeneracion = this.selectGeneraciónRol.Codigo
                        this.info.Base = this.selectBaseVentana.Codigo
                        this.info.Clinica = this.selectClinicaVentana.Codigo
                        this.info.EsInsert = EsInsertV

                        if (this.selectGeneraciónRol.Codigo == 1 && EsInsertV == 1) {
                            this.info.FechaDesde = '01-' + this.info.mesAnio
                            this.info.FechaHasta = this.info.FechaDesde
                            this.info.Dias = this.info.Dias.toString()
                        } else if (this.info.EsInsert == 0) {
                            this.info.TipoGeneracion = 2
                            this.info.IdAgendaClinica = this.infoRolClinica.IdAgendaClinica

                            if (this.info.FechaDesde == this.info.FechaHasta) {
                                this.info.FechaDesde = this.info.FechaDesdeInicial
                                this.info.FechaHasta = this.info.FechaDesdeInicial
                            } else {
                                this.info.FechaHasta = this.info.FechaDesde
                            }
                        }

                        this.axios.post('/app/citas/IngresaRolClinica', this.info)
                            .then(resp => {
                                if (resp.data.codigo == 0) {
                                    this.ventana.mostrar = false
                                    this.Consulta().ListarRolesClinicas()
                                }
                            })
                            .catch(() => {

                            })
                    }
                },
                BloquearHorarios: (isInvalid) => {
                    if (isInvalid) {
                        this.$vs.notify({
                            title: "Error al bloquear horario",
                            text: "Debe ingresar todos los campos",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                    } else if (this.counterDanger) {
                        this.$vs.notify({
                            title: "Error al bloquear horario",
                            text: "Comentario debe tener menos de 200 caracteres.",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                    } else {
                        const arrayTemp = []
                        this.infoHorarios.Horas.forEach(element => arrayTemp.push(element))
                        this.infoHorarios.Horarios = arrayTemp.join(',')
                        this.infoHorarios.IdAgendaClinica = this.infoRolClinica.IdAgendaClinica

                        this.axios.post('/app/citas/BloquearHorario', this.infoHorarios)
                            .then(resp => {
                                if (resp.data.codigo == 0) {
                                    this.infoHorarios = { Horas: [], Comentario: null, Horarios: null, bloquearDesbloquear: true }
                                    this.Otros().AbrirVentana(2)
                                }
                            })
                            .catch(() => {

                            })
                    }
                },
                GuardarCita: (isInvalid, EsCitaIntercalada) => {
                    if (isInvalid) {
                        this.$vs.notify({
                            title: "Error al crear cita",
                            text: "Debe ingresar todos los campos",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                    } else if (this.listaContratosCliente.length > 0 && this.infoCitas.ValidarContrato && this.infoCitas.IdContrato == null) {
                        this.$vs.notify({
                            title: "Error al crear cita",
                            text: "Debe seleccionar contrato",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                    } else if (this.counterDanger) {
                        this.$vs.notify({
                            title: "Error al crear cita",
                            text: "Comentario debe tener menos de 150 caracteres.",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                    } else {
                        this.infoCitas.Fecha = this.infoRolClinica.Fecha
                        this.infoCitas.EsCitaIntercalada = EsCitaIntercalada

                        if (EsCitaIntercalada) {
                            this.infoCitas.MotivoCita = "3"
                            this.infoCitas.HoraCita = "00:00"
                            this.infoCitas.IdAgendaClinica = this.infoRolClinica.IdAgendaClinica
                        } else {
                            this.infoCitas.MotivoCita = this.selectMotivoCita.Codigo
                            this.infoCitas.HoraCita = this.selectHorarioDisponible.Hora
                        }

                        this.axios.post('/app/citas/IngresarCita', this.infoCitas)
                            .then(resp => {
                                if (resp.data.codigo == 0) {
                                    if (resp.data.descripcion == "Horario no disponible, causas posibles: Algún otro usuario ya asignó la cita antes que usted") {
                                        this.Consulta().ListarHorariosDisponibles({ "IdAgendaClinica": this.infoRolClinica.IdAgendaClinica, "IdCita": 0 })
                                    } else {
                                        this.ventana.mostrar = false
                                    }
                                }
                            })
                            .catch(() => {

                            })
                    }
                },
                AgregarCliente: () => {
                    this.Otros().AbrirVentana(10)
                },
                GuardarCliente: () => {
                    this.$refs["PacienteNuevo"].submit()
                },
                AgregarConfirmacion: (invalid, EsCortesia) => {
                    if (invalid) {
                        this.$vs.notify({
                            title: "Error al crear cita",
                            text: "Debe ingresar todos los campos",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                    } else if (this.selectTipoAfiliado.RequiereAutorizacion == 1 && this.infoConfirmar.NumeroAutorizacion == '') {
                        this.$vs.notify({
                            title: "Error al crear cita",
                            text: "Debe ingresar Número Autorización",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })

                        this.$refs["iNumeroAutorizacion"].focusInput()
                    } else {
                        this.infoConfirmar.TipoCita = this.selectTipoCita.Id
                        this.infoConfirmar.TipoAfiliado = this.selectTipoAfiliado.Id
                        this.infoConfirmar.Propiedad = this.selectPropiedad.Id
                        this.infoConfirmar.TomaSignos = EsCortesia
                        this.infoConfirmar.IdCita = this.infoDetalleCita.IdCita

                        this.axios.post('/app/citas/ConfirmarCita', this.infoConfirmar)
                            .then(resp => {
                                if (resp.data.codigo == 0) {
                                    this.Consulta().ListarCitas()
                                    this.calendario.repaint()
                                    this.bloquearConfirmar = true
                                }
                            })
                            .catch(() => {

                            })
                    }
                },
                CambiarCliente: (invalid) => {
                    if (invalid) {
                        this.$vs.notify({
                            title: "Error al cambiar paciente",
                            text: "Debe seleccionar un nuevo cliente.",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                    } else if (this.infoDetalleCita.IdCliente == this.ClienteNuevo.IdCliente) {
                        this.$vs.notify({
                            title: "Error al seleccionar cliente",
                            text: "Debe seleccionar un cliente diferente",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                    } else {
                        this.CambioPaciente = { Base: this.infoDetalleCita.CodigoHospital, Cliente: this.infoDetalleCita.IdCliente, ClienteNuevo: this.ClienteNuevo.IdCliente, Cita: this.infoDetalleCita.IdCita }

                        if (Object.keys(this.infoCopago).length !== 0) {
                            this.CambioPaciente.Plan = this.infoCopago.IdPlan
                            this.CambioPaciente.NumeroAdhesion = this.infoCopago.Numeroadhesion
                        }

                        this.axios.post('/app/citas/CambioPerfilCita', this.CambioPaciente)
                            .then(resp => {
                                if (resp.data.codigo == 0) {
                                    this.Consulta().ListarCitas()
                                    this.calendario.repaint()
                                    this.ventana.mostrar = false
                                    this.CambioPaciente = {}
                                }
                            })
                            .catch(() => {

                            })
                    }
                }
            }
        },
        Modificar: function () {
            return {
                CancelarCita: (isInvalid) => {
                    if (isInvalid) {
                        this.$vs.notify({
                            title: "Error al cancelar cita",
                            text: "Debe ingresar todos los campos",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                    } else {
                        this.infoCancelaCita.IdStatus = this.selectMotivoCitaCancela.IdCorrStatus
                        this.infoCancelaCita.IdCita = this.infoDetalleCita.IdCita

                        this.axios.post('/app/citas/CancelarCita', this.infoCancelaCita)
                            .then(resp => {
                                if (resp.data.codigo == 0) {
                                    this.ventana.mostrar = false
                                    this.Consulta().ListarCitas()
                                    this.Consulta().ListarMedicos()
                                }
                            })
                            .catch(() => {

                            })
                    }
                },
                CancelarRolClinica: (isInvalid) => {
                    if (isInvalid) {
                        this.$vs.notify({
                            title: "Error al cancelar cita",
                            text: "Debe ingresar todos los campos",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                    } else {
                        this.infoCancelaRolClinica.IdAgendaClinica = this.infoRolClinica.IdAgendaClinica

                        this.axios.post('/app/citas/AnularRol', this.infoCancelaRolClinica)
                            .then(resp => {
                                if (resp.data.codigo == 0) {
                                    this.ventana.mostrar = false
                                    this.Consulta().ListarRolesClinicas()
                                }
                            })
                            .catch(() => {

                            })
                    }
                },
                ModificarCita: (isInvalid) => {
                    if (isInvalid) {
                        this.$vs.notify({
                            title: "Error al modificar cita",
                            text: "Debe ingresar todos los campos",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                    } else {
                        this.infoCitas.HoraCita = this.selectHorarioDisponible.Hora
                        if (new Date(this.infoCitas.Fecha).getTime() == this.infoDetalleCita.FechaInicialM) {
                            this.infoCitas.Fecha = this.infoDetalleCita.FechaInicial
                        }

                        this.axios.post('/app/citas/ModificarCita', this.infoCitas)
                            .then(resp => {
                                if (resp.data.codigo == 0) {
                                    this.ventana.mostrar = false
                                    this.Consulta().ListarCitas()
                                    this.Consulta().ListarMedicos()
                                }
                            })
                            .catch(() => {

                            })
                    }
                }
            }
        },
        Otros: function () {
            return {
                ChangeHospital: (arrayListar, objeto, desdeVentana = false, desdeVentanaCitas = false) => {
                    if (desdeVentanaCitas) {
                        this.infoCitas.IdAgendaClinica = 0
                        this.infoCitas.Base = this.selectBaseVentana.Codigo
                        this.selectHorarioDisponible = null
                        this.listaHorariosDisponibles = []
                    }

                    if (desdeVentana) {
                        this.objetoConsultaClinicas.Base = this.selectBaseVentana.Codigo
                        this.selectClinicaVentana = {
                            Codigo: null,
                            Nombre: null
                        }
                    } else {
                        this.data = []
                        this.objetoConsultaRoles.Base = this.selectBase.Codigo
                        this.objetoConsultaClinicas.Base = this.selectBase.Codigo

                        this.selectClinica = {
                            Codigo: null,
                            Nombre: null
                        }

                        this.selectBaseVentana = {
                            Codigo: this.selectBase.Codigo,
                            Nombre: this.selectBase.Nombre
                        }
                    }

                    this.Consulta().ListarClinicas(arrayListar, objeto)
                },
                ChangeClinica: (desdeVentanaCitas = false) => {
                    if (desdeVentanaCitas) {
                        this.infoCitas.IdAgendaClinica = 0
                        this.infoCitas.Clinica = this.selectClinicaVentana.Codigo
                        this.selectHorarioDisponible = null
                        this.listaHorariosDisponibles = []
                    } else {
                        this.selectClinicaVentana = {
                            Codigo: this.selectClinica.Codigo,
                            Nombre: this.selectClinica.Nombre
                        }

                        if (this.vistaCalendario == 'day') {
                            this.objetoConsultaCitas.Clinica = this.selectClinica.Codigo
                            this.objetoConsultaRoles.Clinica = this.selectClinica.Codigo
                            this.Consulta().ListarCitas()
                            this.Consulta().ListarMedicos()
                        } else {
                            this.objetoConsultaCitas.Clinica = this.selectClinica.Codigo
                            this.objetoConsultaRoles.Clinica = this.selectClinica.Codigo
                            this.Consulta().ListarRolesClinicas()
                        }
                    }
                },
                ChangeDate: () => {
                    this.infoCitas.IdAgendaClinica = 0
                    this.infoDetalleCita.Fecha = this.infoCitas.Fecha
                    this.selectHorarioDisponible = null
                    this.listaHorariosDisponibles = []
                },
                AbrirVentana: (opcion) => {
                    this.ventana.opcion = opcion

                    if (opcion == 1) {
                        this.ventana.mostrar = true
                        this.ventana.titulo = 'Crear Rol Clinica'
                        this.selectBaseVentana = this.selectBase
                        this.selectClinicaVentana = {
                            Codigo: this.selectClinica.Codigo,
                            Nombre: this.selectClinica.Nombre
                        }

                        this.selectGeneraciónRol = {
                            Codigo: 1,
                            Nombre: 'Por dia de la semana'
                        }

                        this.configFromdateTimePicker = {
                            minDate: new Date(),
                            locale: SpanishLocale,
                            dateFormat: "m-Y",
                        }

                        this.listarClinicasVentana = this.listarClinicas
                        this.Otros().Limpiar()
                    } else if (opcion == 2) {
                        this.ventana.mostrar = true
                        this.ventana.titulo = 'Información Rol Clínica'
                        this.infoHorarios = { Horas: [], Comentario: null, Horarios: null, bloquearDesbloquear: true }
                    } else if (opcion == 3) {
                        this.ventana.titulo = 'Agregar Cita'
                        this.Otros().CargarInfoCitas()
                        this.listaContratosCliente = []
                    } else if (opcion == 4) {
                        this.ventana.titulo = 'Bloquear Horario'
                    } else if (opcion == 5) {
                        this.ventana.mostrar = true
                        this.ventana.titulo = 'Información Cita'
                        this.ventana.opcion = opcion
                    } else if (opcion == 6) {
                        this.selectMotivoCitaCancela = null
                        this.infoCancelaCita = { Comentario: null, IdStatus: null, IdCita: null }
                        this.ventana.titulo = 'Cancelar Cita'
                        this.Consulta().ListarMotivosCitaCancelar()
                    } else if (opcion == 7) {
                        this.infoCancelaRolClinica = { Observaciones: null, IdAgendaClinica: null }
                        this.ventana.titulo = 'Anular Rol Clínica'
                    } else if (opcion == 8) {
                        this.ventana.titulo = 'Editar Rol Clínica'
                        this.configFromdateTimePicker = {
                            minDate: new Date(),
                            locale: SpanishLocale,
                            dateFormat: "d-m-Y",
                        }
                        this.Otros().Limpiar()
                        this.Consulta().CargarDataRolClinica()
                    } else if (opcion == 9) {
                        this.ventana.titulo = 'Editar Cita'
                        //scope = this.infoDetalleCita
                        this.configFromdateTimePicker = {
                            minDate: new Date(),
                            locale: SpanishLocale,
                            dateFormat: "d-m-Y"
                        }

                        this.Consulta().CargarDataCitas()
                    } else if (opcion == 10) {
                        this.ventana.titulo = 'Crear Cliente'
                        this.$store.dispatch('paciente/initPaciente')
                    } else if (opcion == 11) {
                        this.ventana.titulo = 'Confirmar Cita'
                        this.bloquearConfirmar = false
                        this.infoConfirmar = { NumeroAutorizacion: '', Comentario: '' }
                        this.Otros().LimpiarObjeto('selectTipoCita')
                        this.Otros().LimpiarObjeto('selectTipoAfiliado')
                        this.Otros().LimpiarObjeto('selectPropiedad')
                        this.listaTipoAfiliado = []
                        this.Consulta().ListarConfirmarCita('ListarTipoCita', 'listaTipoCita')
                        this.Consulta().ListarConfirmarCita('ListarPropiedadCita', 'listaPropiedades')
                        this.Consulta().ListarConfirmarCita('CitaContadores', 'listaCitaContadores', { IdCita: this.infoDetalleCita.IdCita })
                    } else if (opcion == 12) {
                        this.ventana.titulo = 'Intercalar Cita'
                        this.infoCitas = { QuienLlama: null, IdCliente: null, IdContrato: null, Comentario: null, TelefonoLlama: null, ValidarContrato: true, IdCopago: null, IdPlan: null, Numeroadhesion: null, Fecha: null, HoraCita: null, IdAgendaClinica: null, MotivoCita: null }
                    }
                    else if (opcion == 13) {
                        this.ventana.titulo = 'Cambiar Paciente'
                        this.ClienteNuevo = {}
                        this.listaContratosCliente = []
                    }
                },
                Limpiar: () => {
                    this.info = {
                        IdAgendaClinica: 0,
                        Base: null,
                        Clinica: null,
                        Medico: null,
                        Especialidad: null,
                        TipoGeneracion: null,
                        FechaDesde: null,
                        FechaHasta: null,
                        Dias: null,
                        MesAnio: null,
                        HoraInicio: null,
                        HoraFin: null,
                        Observaciones: null,
                        EsInsert: null
                    }
                },
                changeGeneracion: () => {
                    if (this.selectGeneraciónRol.Codigo == 1) {
                        this.configFromdateTimePicker = {
                            minDate: new Date(),
                            locale: SpanishLocale,
                            dateFormat: "m-Y",
                        }
                    } else {
                        this.configFromdateTimePicker = {
                            minDate: new Date(),
                            locale: SpanishLocale,
                            dateFormat: "d-m-Y",
                        }
                    }
                },
                ChangeMedico: () => {
                    if (this.selectMedico.length > 0) {
                        this.groupByDate = true
                    } else {
                        this.groupByDate = false
                    }
                },
                CargarInfoCitas: () => {
                    this.infoCitas = { QuienLlama: null, IdCliente: null, IdContrato: null, Comentario: null, TelefonoLlama: null, ValidarContrato: true, IdCopago: null, IdPlan: null, Numeroadhesion: null, Fecha: null, HoraCita: null, IdAgendaClinica: null, MotivoCita: null }
                    this.infoCitas.IdAgendaClinica = this.infoRolClinica.IdAgendaClinica
                    this.Consulta().ListarHorariosDisponibles({ "IdAgendaClinica": this.infoRolClinica.IdAgendaClinica, "IdCita": 0 })
                    this.Consulta().ListarMotivosCitas()
                },
                CambioSwitch: () => {
                    if (this.infoHorarios.bloquearDesbloquear) {
                        this.ventana.titulo = 'Desbloquear Horario'
                    } else {
                        this.ventana.titulo = 'Bloquear Horario'
                    }

                    this.infoHorarios.Horas = []
                    this.infoHorarios.Horarios = null
                },
                QuitarContrato: () => {
                    this.infoCitas.ValidarContrato = false
                    this.infoCitas.IdCopago = null
                    this.infoCitas.Comentario = null
                    this.infoCitas.IdContrato = null
                },
                ChangeTipoCita: () => {
                    this.Consulta().ListarConfirmarCita('ListarTipoAfiliado', 'listaTipoAfiliado', { "TipoCita": this.selectTipoCita.Id })
                },
                LimpiarObjeto: (objeto) => {
                    this[objeto] = null
                }
            }
        },
        handlePropertyChange: function (e) {
            if (e.name === 'currentView') {
                if (e.value == 'day') {
                    this.vistaCalendario = e.value
                    this.tituloModulo = 'Citas '
                    this.Consulta().ListarCitas()
                    this.Consulta().ListarMedicos()
                } else {
                    this.vistaCalendario = e.value
                    this.selectMedico = []
                    this.tituloModulo = 'Roles Clinicas'
                    this.Consulta().ListarRolesClinicas()
                }
            } else if (e.name === 'currentDate') {
                if (this.vistaCalendario == 'day') {
                    this.objetoConsultaCitas.FechaVista = e.value
                    this.objetoConsultaRoles.FechaVista = e.value
                    this.Consulta().ListarCitas()
                    this.Consulta().ListarMedicos()
                } else {
                    this.objetoConsultaCitas.FechaVista = e.value
                    this.objetoConsultaRoles.FechaVista = e.value
                    this.Consulta().ListarRolesClinicas()
                }
            }
        },
        okClicked2: function (e) {
            e.cancel = true
        },
        onAppointmentFormOpening(e) {
            e.cancel = true

            if (e.appointmentData.Tipo == 'RolClinica') {
                this.Otros().AbrirVentana(2)
                this.infoRolClinica = e.appointmentData
            } else if (e.appointmentData.Tipo == 'Cita') {
                this.Otros().AbrirVentana(5)
                this.infoDetalleCita = e.appointmentData
            }
        },
        notificacion(response) {
            this.$vs.notify({
                title: "Creación de cliente",
                text: response.data.json[0].descripcion,
                iconPack: 'feather',
                icon: 'icon-alert-circle',
                color: 'success',
                position: 'bottom-center'
            })
            this.$store.dispatch('paciente/initPaciente')
            this.Otros().AbrirVentana(3)
        }
    },
    mounted() {
        this.permisos.Visualizar = this.$validar_privilegio('VISUALIZAR').status
        this.permisos.CrearRol = this.$validar_privilegio('CREARROL').status
        this.permisos.EditarRol = this.$validar_privilegio('EDITARROL').status
        this.permisos.AnularRol = this.$validar_privilegio('ANULARROL').status
        this.permisos.BloquearHorarios = this.$validar_privilegio('BLOQUEARHORARIO').status
        this.permisos.CrearCita = this.$validar_privilegio('CREARCITA').status
        this.permisos.CrearCliente = this.$validar_privilegio('CREARCLIENTE').status
        this.permisos.EditarCita = this.$validar_privilegio('EDITARCITA').status
        this.permisos.CancelarCita = this.$validar_privilegio('CANCELARCITA').status
        this.permisos.ConfirmarCita = this.$validar_privilegio('CONFIRMARCITA').status
        this.permisos.IntercalarCita = this.$validar_privilegio('INTERCALARCITA').status
        this.permisos.CambiarCliente = this.$validar_privilegio('CAMBIARCLIENTE').status


        this.Consulta().ListarBases()
        this.Consulta().ListarClinicas('listarClinicas', 'selectClinica')
        this.selectBase.Codigo = this.sesion.sesion_sucursal
        this.selectBase.Nombre = this.sesion.sesion_sucursal_nombre
    },
    created() {
        loadMessages(esMessages);
        locale(navigator.language);

        this.objetoConsultaRoles.Base = this.sesion.sesion_sucursal
        this.objetoConsultaClinicas.Base = this.sesion.sesion_sucursal
    }
}
</script>