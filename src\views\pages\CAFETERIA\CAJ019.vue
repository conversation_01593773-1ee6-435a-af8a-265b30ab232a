<template>
    <vx-card type = "2" title="Modificar Cargos a la Habitación" class="justify-center "> 
        <vs-popup classContent="popup-example" :title="pantalla_cargo.titulo" :active.sync="pantalla_cargo.mostrar">
            <ValidationObserver ref="formCargo" v-slot="{ invalid, handleSubmit }" mode="lazy">
                <form ref="formCargo" method="post" @submit.prevent="handleSubmit(Guardar().nuevoEdicionCargo())">
                    <div class="flex flex-wrap">
                        <div class="xs:w-full md:w-5/12 lg:w-5/12 xl:w-2/12 m-2">
                            <SM-Buscar label="Código Producto" v-model="producto.codigo" api="app/v1_JefeCaja/busqueda_productos" :api_campos="['Categoria','CategoriaNombre','Codigo','Nombre','PrecioVenta']" :api_titulos="['Cód. Categoría','Nombre Categoría','Código Producto', 'Producto', 'Precio de Venta']" :api_filtro="{'Activa':0,'Categoria':24, 'NivelPrecioPaciente':info.nivelPrecio}" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Codigo" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="true" :callback_buscar="Consulta().cargarProducto"  class="w-full" />                                
                        </div>
                        <div class="xs:w-full md:w-6/12 lg:w-6/12 xl:w-6/12 m-2">
                            <vs-input label="Producto" class="w-full" :value="producto.nombre" disabled />
                        </div>                        
                    </div>
                    <div class="flex flex-wrap">
                        <div class="xs:w-full md:w-full lg:w-full xl:w-2/12 m-2">
                            <label>Cantidad:</label>
                            <ValidationProvider rules="numero_entero" v-slot="{ errors }">    
                                <vs-input-number style="justify-content:left" min="0" v-model="producto.cantidad" class="w-full"/> 
                                <span style="position:relative;top:-2px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>                                                      
                            </ValidationProvider>       
                        </div>  
                        <div class="xs:w-full md:w-full lg:w-full xl:w-3/12 m-2">
                            <label>Precio:</label>
                            <vx-input-group class="mb-base">
                                <template slot="prepend">
                                <div class="prepend-text bg-primary">
                                    <span>Q</span>
                                </div>
                                </template>
                                <ValidationProvider rules="required" v-slot="{ errors }">                                    
                                    <vs-input type="number" v-model="producto.precio" disabled
                                            :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"/>
                                </ValidationProvider>
                            </vx-input-group>
                        </div>
                        <div class="xs:w-full md:w-full lg:w-full xl:w-3/12 m-2">
                            <label>Valor:</label>
                            <vx-input-group class="mb-base">
                                <template slot="prepend">
                                <div class="prepend-text bg-primary">
                                    <span>Q</span>
                                </div>
                                </template>
                                <ValidationProvider rules="required" v-slot="{ errors }" >
                                    <vs-input type="number"  v-model="valorProducto" disabled
                                            :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"/>
                                </ValidationProvider>
                            </vx-input-group>
                        </div>
                        <div class="xs:w-full md:w-full lg:w-full xl:w-3/12 m-2"></div>
                    </div>
                    <div class="flex flex-wrap ">
                        <div class="xs:w-full md:w-full lg:w-full xl:w-2/12 m-2">
                            <vs-button color="success" class="m-1" 
                                @click="handleSubmit(Guardar().nuevoEdicionCargo(invalid))">
                                Guardar
                            </vs-button>
                        </div>
                        <div class="xs:w-full md:w-full lg:w-full xl:w-2/12 m-2">
                            <vs-button class="m-1" color="warning" @click="Otros().cerrarPopUpProducto()">
                                Cancelar
                            </vs-button>
                        </div>                                                       
                    </div>
                </form>
            </ValidationObserver>
        </vs-popup>
        <div class="flex flex-wrap">
            <ValidationObserver ref="formProducto" v-slot="{ invalid, handleSubmit }" mode="lazy" class="w-full">
                <form ref="formProducto" method="post" @submit.prevent="handleSubmit(Guardar().modificarCargos())">
                    <div class="flex flex-wrap w-full p-2 pb-4" style="border: 1px solid #888;">            
                        <div class="md:w-full lg:w-6/12 xl:w-4/12 m-2 lg:pr-6">
                            <label>Paciente:</label> 
                            &nbsp;&nbsp;
                            <label style="color:black">{{info.paciente}}</label>      
                        </div>
                        <div class="md:w-full lg:w-full xl:w-7/12"></div>

                        <div class="md:w-full lg:w-6/12 xl:w-4/12 m-2 lg:pr-6">
                            <label>Nivel de Precios:</label> 
                            &nbsp;&nbsp;
                            <label style="color:black">{{info.nivelPrecio}}</label>      
                        </div>
                        <div class="md:w-full lg:w-full xl:w-7/12"></div>

                        <div class="md:w-full lg:w-6/12 xl:w-2/12 m-2 lg:pr-6">
                            <ValidationProvider rules="required" v-slot="{ errors }">
                                <vs-input label="*Serie Admisión" class="w-full"  v-model="info.serieAdmision" 
                                            :danger="errors.length > 0"
                                            :danger-text="(errors.length > 0) ? errors[0] : null" v-on:change="CargarAdmision"/>
                            </ValidationProvider>
                        </div>
                        <div class="md:w-full lg:w-6/12 xl:w-2/12 m-2 xl:pr-5">
                            <ValidationProvider rules="required" v-slot="{ errors }">
                                <vs-input label="*No. Admisión" type="number"  class="w-full"  v-model="info.noAdmision" 
                                            :danger="errors.length > 0"
                                            :danger-text="(errors.length > 0) ? errors[0] : null" v-on:change="CargarAdmision"/>
                            </ValidationProvider>
                        </div>
                        <div class="md:w-full lg:w-full xl:w-7/12"></div>

                        <div class="md:w-full lg:w-6/12 xl:w-4/12 ml-2">
                                <ValidationProvider rules="max:100|required" v-slot="{ errors }">
                                    <vs-input size="large" label="*Razón" class="w-full"  v-model="info.razon" 
                                            :danger="errors.length > 0"
                                            :danger-text="(errors.length > 0) ? errors[0] : null"/>
                                </ValidationProvider>
                        </div>  
                        <div class="md:w-full lg:w-full xl:w-7/12"></div> 
                    </div>
                    <vs-divider></vs-divider> 
                    <div class="flex flex-wrap w-full" style="border: 1px solid #888;">
                        <div class="xl:w-full m-5">
                            <div class="w-full">
                                <vs-button color="primary" class="md:w-full lg:w-6/12 xl:w-2/12 mb-4"
                                    icon-pack="fas" icon="fa-plus" :disabled="!ingresarCargos"
                                    @click="handleSubmit(AgregarCargo()) ">
                                    Nuevo Cargo 
                                </vs-button>
                                <div class="md:w-full lg:5/12 xl:w-9/12"></div> 
                            </div>
                            <vs-table2 max-items="10" search pagination :data="cargos" class="mr-5">
                                <template slot="thead">
                                    <th order="Cantidad" width="50px">Cantidad</th>
                                    <th order="Codigo" width="70px">Código</th>
                                    <th order="Descripcion">Descripción</th>
                                    <th order="Precio">Precio</th>
                                    <th order="Valor">Valor</th>
                                    <th order="Accion" width="150px">Acción</th>
                                </template>
                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td2>
                                            {{ tr.cantidad }}
                                        </vs-td2>
                                        <vs-td2>
                                            {{ tr.codigo }}
                                        </vs-td2>
                                        <vs-td2>
                                            {{ tr.nombre }}
                                        </vs-td2>
                                        <vs-td2>
                                            {{parseFloat(tr.precio).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}                                            
                                        </vs-td2>
                                        <vs-td2>
                                            {{parseFloat(tr.valor).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}                                            
                                        </vs-td2>
                                        <vs-td2>
                                            <vs-button style="margin-left:1px;display:inline-block;" @click.native="EditarCargo(indextr,tr)" icon-pack="fas" icon="fa-edit"></vs-button>
                                            <vs-button  color="danger" style="margin-left:1px;display:inline-block;" @click.native="EliminarCargo(indextr,tr)" icon-pack="fas" icon="fa-trash"></vs-button>                               
                                        </vs-td2>
                                    </tr>
                                </template>
                            </vs-table2>
                        </div>
                    </div>

                    <div class="flex bottom">
                        <vs-button color="danger" class="mr-5" @click="confirmarBorradoAdmision()">Limpiar Datos Admisión</vs-button>
                        <vs-spacer></vs-spacer>
                        <div style="width:200px">
                            <tr>
                                <td class="p-3">
                                    <h4>TOTAL</h4>
                                </td>
                                <td class="p-3" width="200px" style="text-align:right">
                                    <h4>{{totalCargos.toLocaleString("es-GT",  {
                                                                                    style: "currency",
                                                                                    currency: "GTQ"
                                                                                })
                                        }}
                                    </h4>
                                </td>
                            </tr>
                        </div>
                        <vs-spacer></vs-spacer>
                        <!-- <vs-button color="success" @click="Guardar().reporte()">Reporte</vs-button> -->
                        <vs-button @click="Guardar().modificarCargos(invalid)" :disabled="!ingresarCargos">Modificar Cargos</vs-button>
                    </div>
                </form>
            </ValidationObserver>
        </div>
    </vx-card>
</template>
<script>
export default {
    data (){
        return{
            info: {
                paciente: null,
                nivelPrecio: null,
                serieAdmision: null,
                noAdmision: null,
                razon: null                
            },
            producto: {
                cantidad: 1,
                codigo: null,
                nombre: null,
                precio: null,
                valor: null,
                costo: null,
                unidadMedida: null,
                factor: null,
                subNivel: null,
                indice: null
            },
            pantalla_cargo: {
                titulo: 'Nuevo Cargo',
                mostrar: false,
                tipoCargo: 'NUEVO' /*NUEVO/EDICION*/
            },
            cargos: [],
            desactivarModificar: true,
            ingresarCargos: false
        }
    },
    methods:{
        CargarAdmision(){            
            if(!this.info.serieAdmision || !this.info.noAdmision)
                return            
            
            
            this.info.paciente = null
            this.info.nivelPrecio = null
            this.ingresarCargos = false
            this.cargos = []

            this.axios.post('/app/v1_JefeCaja/buscar_admision_cargos_habitacion', {
                Opcion: "CONSULTA",
                SubOpcion: "ADMISION",
                SerieAdmision: this.info.serieAdmision,
                NumeroAdmision: this.info.noAdmision,
                AdmisionActiva: 0,
                AdmisionConHabitacion: 0,
                CategoriaProducto: "24"
            }).then((resp) => {
                    let error 
                    let mensajeError
                    
                    if( resp.data.json.length == 0 ){
                        this.$vs.notify({
                            time: 4000,
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'No se encontraron datos para la admisión'
                        })
                        return
                    }
                    
                    if(resp.data.json){
                        error = resp.data.json[0].tipo_error
                        mensajeError = resp.data.json[0].descripcion
                    }else{
                        error = resp.data.tipo_error
                        mensajeError = resp.data.descripcion
                    }

                    if(error && error != 0){
                        this.$vs.notify({
                            time: 4000,
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: mensajeError
                        })
                        return
                    }
                    
                    
                    if( resp.data.json[0].NumeroCargos == 0){
                        this.$vs.notify({
                            time: 4000,
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'La admisión no cuenta con cargos a la habitación'
                        })
                        return 
                    }

                    this.info.paciente = resp.data.json[0].Paciente
                    this.info.nivelPrecio = resp.data.json[0].NivelPrecios
                    this.ingresarCargos = true
                    this.ObtenerCargos()
                }
            )
        },
        ObtenerCargos(){            

            this.axios.post('/app/v1_JefeCaja/buscar_cargos_habitacion', {
                Opcion: "CONSULTA",
                SubOpcion: "CARGOS",
                SerieAdmision: this.info.serieAdmision,
                NumeroAdmision: this.info.noAdmision,
                AdmisionActiva: 0,
                AdmisionConHabitacion: 0,
                CategoriaProducto: "24"
            }).then((resp) => {
                    let error 
                    let mensajeError

                    if( resp.data.json.length == 0 ){
                        this.$vs.notify({
                            time: 4000,
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'No se encontraron cargos para la admisión'
                        })
                        return
                    }

                    if(resp.data.json[0].tipo_error){
                        error = resp.data.json[0].tipo_error
                        mensajeError = resp.data.json[0].descripcion
                    }else if(resp.data.tipo_error){
                        error = resp.data.tipo_error
                        mensajeError = resp.data.descripcion
                    }

                    if(error && error != 0){
                        this.$vs.notify({
                            time: 4000,
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: mensajeError
                        })
                        return
                    }

                    this.cargos = resp.data.json.map( cargo => ({
                                                                    cantidad: cargo.Cantidad,
                                                                    codigo: cargo.Producto,
                                                                    nombre: cargo.Nombre,
                                                                    precio: cargo.PrecioUnitario,
                                                                    valor: cargo.Valor,
                                                                    unidadMedida: cargo.UnidadMedida,
                                                                    subNivel: cargo.SubNivel,
                                                                    factor: cargo.Factor,
                                                                    costo: cargo.Costo,
                                                                    indice: null
                                                                }))
                }
            )
        },
        AgregarCargo(){
            this.Otros().limpiarProducto()
            this.pantalla_cargo.titulo =  'Nuevo Cargo'            
            this.pantalla_cargo.tipoCargo =  'NUEVO'   
            this.pantalla_cargo.mostrar =  true         
        },
        EditarCargo(indice, cargo){
            this.pantalla_cargo.titulo =  'Editar Cargo'            
            this.pantalla_cargo.tipoCargo =  'EDICION'   
            
            this.producto.cantidad = cargo.cantidad
            this.producto.codigo = cargo.codigo
            this.producto.nombre = cargo.nombre
            this.producto.precio = cargo.precio
            this.producto.valor = cargo.valor   
            this.producto.costo = cargo.costo 
            this.producto.unidadMedida = cargo.unidadMedida
            this.producto.factor = cargo.factor
            this.producto.subNivel = cargo.subNivel
            this.producto.indice = indice                    

            this.pantalla_cargo.mostrar =  true
        },
        EliminarCargo(indice,cargo){
            if(this.cargos.length <= 1 ){
                this.$vs.notify({
                                    time: 4000,
                                    title:'Alerta',
                                    text:'No se pueden eliminar todos los cargos',
                                    color: '#B71C1C',
                                })     
                return
            }

            this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Producto',
                    text: `¿Desea eliminar el cargo ${cargo.nombre}?`,
                    acceptText: 'Aceptar',
                    accept: () => {
                        this.cargos.splice(indice,1)
                    }
                })
        },
        Guardar(){
            return{
                modificarCargos: (invalid) => {
                    if(invalid){
                        this.$vs.notify({
                                            time: 4000,
                                            title:'Alerta',
                                            text:'Validar los campos obligatorios',
                                            color: '#B71C1C',
                                        })     
                        return
                    }

                    if(!this.cargos || this.cargos.length == 0){
                        this.$vs.notify({
                                            time: 4000,
                                            title:'Alerta',
                                            text:'Debe ingresar al menos 1 cargo',
                                            color: '#B71C1C',
                                        })     
                        return
                    }

                    this.axios.post('/app/v1_JefeCaja/modificar_cargos_habitacion', {
                        Opcion: "INSERCCION",
                        SubOpcion: "CARGOS",
                        SerieAdmision: this.info.serieAdmision,
                        NumeroAdmision: this.info.noAdmision,
                        AdmisionActiva: 0,
                        AdmisionConHabitacion: 0,
                        CategoriaProducto: "24",
                        Razon: this.info.razon,
                        CargosModificados: this.cargos.map( producto => producto.codigo+','+producto.cantidad+','+producto.precio+','+producto.valor+','+producto.unidadMedida+','+producto.factor+','+producto.subNivel+','+producto.costo)
                    }).then((resp) => {
                            let error 
                            let mensajeError

                            if(resp.data.json && resp.data.json.length > 0 && resp.data.json[0].tipo_error){
                                error = resp.data.json[0].tipo_error
                                mensajeError = resp.data.json[0].descripcion
                            }else if(resp.data && resp.data.tipo_error){
                                error = resp.data.tipo_error
                                mensajeError = resp.data.descripcion
                            }

                            if(error && error != 0){
                                this.$vs.notify({
                                    time: 4000,
                                    color: '#B71C1C',
                                    title: 'Alerta',
                                    text: mensajeError
                                })
                                return
                            }

                            this.$vs.notify({        
                                            time: 4000,                                    
                                            title:'Exito',
                                            text:'Se modificaron los cargos de habitación',
                                            color:'success'
                                        })

                            this.Otros().limpiarDatos()
                            this.ingresarCargos = false
                        }
                    )


                },
                nuevoEdicionCargo: (invalid) => {
                    if(invalid){
                        this.$vs.notify({
                                            time: 4000,
                                            title:'Alerta',
                                            text:'Validar los campos obligatorios',
                                            color: '#B71C1C',
                                        })     
                        return
                    }

                    if(this.pantalla_cargo.tipoCargo == 'EDICION'){                                         
                        this.cargos[this.producto.indice].codigo = this.producto.codigo
                        this.cargos[this.producto.indice].nombre = this.producto.nombre
                        this.cargos[this.producto.indice].precio = this.producto.precio
                        this.cargos[this.producto.indice].valor = this.producto.valor
                        this.cargos[this.producto.indice].cantidad = this.producto.cantidad 
                        this.cargos[this.producto.indice].UnidadMedida = this.producto.UnidadMedida 
                        this.cargos[this.producto.indice].factor = this.producto.factor 
                        this.cargos[this.producto.indice].subNivel = this.producto.subNivel 
                    }else{
                        this.cargos.push({                            
                            ...this.producto
                        })
                    }                    
                    this.Otros().cerrarPopUpProducto()

                }
            }
        },
        Consulta(){
            return{
                cargarProducto: (datos) => {
                    this.producto.categoria = datos.Categoria
                    this.producto.codigo = datos.Codigo
                    this.producto.nombre = datos.Nombre                    
                    this.producto.precio = parseFloat(datos.PrecioVenta?datos.PrecioVenta:0.00).toFixed(2)
                    this.producto.costo = parseFloat(datos.Costo?datos.Costo:0.00).toFixed(2)
                    this.producto.unidadMedida = datos.UnidadMedida
                    this.producto.factor = datos.Factor
                    this.producto.subNivel = datos.SubNivel                    
                    
                }
            }
        },
        Otros(){
            return {
                limpiarDatos: () => {
                    this.info.paciente = null
                    this.info.nivelPrecio = null
                    this.info.serieAdmision = null
                    this.info.noAdmision = null
                    this.info.razon = null
                    this.cargos = []
                    this.desactivarModificar = true
                },
                limpiarProducto: () => {
                    this.producto.cantidad = 1
                    this.producto.codigo = null
                    this.producto.nombre = null
                    this.producto.precio = null
                    this.producto.valor = null 
                    this.producto.unidadMedida = null 
                    this.producto.factor = null 
                    this.producto.subNivel = null 
                    this.producto.indice = null 
                    this.producto.costo = null                 
                },
                cerrarPopUpProducto: ()=> {
                    this.Otros().limpiarProducto()
                    this.pantalla_cargo.mostrar = false
                }
            }
        },        
        confirmarBorradoAdmision(){
            this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Limpiar Datos Admisión',
                    text: `¿Desea limpiar todos los campos ingresados?`,
                    acceptText: 'Aceptar',
                    accept: () => {
                        this.Otros().limpiarDatos()
                        this.ingresarCargos = false
                    }
                })
        }
    },
    computed:{
        totalCargos() {
            return this.cargos.reduce((acumulador, cargo) => acumulador + parseFloat(cargo.valor), 0)
        },
        valorProducto() {            
            this.producto.valor = parseFloat(this.producto.cantidad * this.producto.precio).toFixed(2)
            return this.producto.valor
        }
    },
    mounted(){

    }
}
</script>