<template>
<div id="preegreso-enfermeria-container">
    <!--Se puso fuera del DxForm ya que cada vez que cambia de tamaño repinta el componente dentro de un DxItem-->
    <tempo v-if="PreEgreso.HabilitarRegistro" :horas="0" :minutos="0" :segundos="TiempoRestante" :sonido="true" style="margin-left: 150px;" @onTimeUp="TimeUp" />
    <span style="font-size: 20px;">{{ TextoInfo }}</span>

    <DxButton :visible="!this.PreEgreso.FechaPreegreso" icon="fas fa-play" text="Iniciar Egreso" type="success" @click="() => {this.IniciarPreegreso()}"/>
    <form @submit="handleSubmit">
        <DxForm :form-data.sync="PreEgreso" label-location="left" validation-group="validacionPreEgreso" :col-count="2" :col-count-by-screen="{sm:1}" required-message="Deben de estar finalizadas todas las tareas previas al egreso para continuar" :disabled="!PreEgreso.HabilitarRegistro">
            <DxGroupItem :col-count="2" :col-span="2">
                <DxGroupItem caption="Tareas previas al egreso" :col-count="1" :col-span="1" css-class="egreso-enf-group">
                    <DxSimpleItem :validation-rules="ReglasValidacion" editor-type="dxCheckBox" data-field="MedicamentosAsignados" :label="{text:'Todos los medicamentos tienen estado asignado'}" />
                    <DxSimpleItem :validation-rules="ReglasValidacion" editor-type="dxCheckBox" data-field="DevolucionesProducto" :label="{text:'Se realizaron las devoluciones de producto asignado'}" />
                    <DxSimpleItem :validation-rules="ReglasValidacion" editor-type="dxCheckBox" data-field="DevolucionesFarmacia" :label="{text:'Se tramitaron las devoluciones pendientes con farmacia'}" />
                    <DxSimpleItem :validation-rules="ReglasValidacion" editor-type="dxCheckBox" data-field="NotaEgresoEnfermeria" :label="{text:'Se colocó nota de egreso de enfermería'}" />
                    <DxSimpleItem :validation-rules="ReglasValidacion" editor-type="dxCheckBox" data-field="CargosRealizados" :label="{text:'Se realizaron cargos GMT, cirugías, hemodiálisis'}" />
                </DxGroupItem>
                <DxGroupItem css-class="egreso-enf-group">
                    <DxGroupItem caption="Datos de egreso enfermería" template="datosEgreso">
                        <template #default>
                            <div>
                                <p class="egreso-enf-parrafo"> <span class="egreso-enf-titulo"> Colaborador: </span> <span> {{ PreEgreso.EmpleadoPreegreso }}</span> </p>
                                <p class="egreso-enf-parrafo"> <span class="egreso-enf-titulo"> Fecha: </span> <span> {{ $formato_fecha(PreEgreso.FechaPreegreso.replace('T',' ')) }}</span> </p>
                                <p class="egreso-enf-parrafo"> <span class="egreso-enf-titulo"> Puesto: </span> <span> {{ PreEgreso.PuestoPreegreso }}</span> </p>
                            </div>
                        </template>
                    </DxGroupItem>
                    <DxGroupItem caption="Confirmación egreso" template="datosEgreso">
                        <template #default>
                            <div>
                                <p class="egreso-enf-parrafo"> <span class="egreso-enf-titulo"> Colaborador: </span> <span> {{ PreEgreso.EmpleadoConfirma }}</span> </p>
                                <p class="egreso-enf-parrafo"> <span class="egreso-enf-titulo"> Fecha: </span> <span> {{ $formato_fecha(PreEgreso.FechaConfirma.replace('T',' ')) }}</span> </p>
                                <p class="egreso-enf-parrafo"> <span class="egreso-enf-titulo"> Puesto: </span> <span> {{ PreEgreso.PuestoConfirma }}</span> </p>
                            </div>
                        </template>
                    </DxGroupItem>
                </DxGroupItem>
                <DxButtonItem :visible=" StatusAdmision === 'A' && PreEgreso.HabilitarRegistro" :button-options="submitButtonOptions" horizontal-alignment="right" verical-alignment="buttom" />
            </DxGroupItem>

        </DxForm>
    </form>

</div>
</template>

<script>
import EXPBase from './EXPBase.vue'
import {
    DxForm,
    DxSimpleItem,
    DxGroupItem,
    DxButtonItem,
} from 'devextreme-vue/form'
import DxButton from 'devextreme-vue/button'
import {
    confirm
} from 'devextreme/ui/dialog'

export default {
    name: 'PreEgreso',
    extends: EXPBase,
    components: {
        DxForm,
        DxGroupItem,
        DxSimpleItem,
        DxButtonItem,
        DxButton,
        tempo: () => import('../../../components/sermesa/global/Temporizador.vue')
    },
    data() {
        return {
            PreEgreso: {
                IdEgreso: null,
                MedicamentosAsignados: false,
                DevolucionesProducto: false,
                DevolucionesFarmacia: false,
                NotaEgresoEnfermeria: false,
                CargosRealizados: false,

                FechaPreegreso: '',
                EmpleadoPreegreso: '',
                PuestoPreegreso: '',

                FechaConfirma: '',
                EmpleadoConfirma: '',
                PuestoConfirma: '',

                HabilitarRegistro: false,
                Minutos: 0, //minutos transcurridos luego de iniciar el egreso de enfermeria

            },
            TextoInfo: '',
            ReglasValidacion: [{
                type: 'required'
            }],

            submitButtonOptions: {
                text: 'Confirmar Egreso',
                type: 'success',
                icon: 'save',
                useSubmitBehavior: true,
                disabled: false
            },
        }
    },
    computed: {
        //Para calcular el tiempo restante en base al dato devuelto de backend
        TiempoRestante() {
            let diff = 900000 //15 minutos en milisegundos
            if (this.PreEgreso.HabilitarRegistro) {
                diff = 900000 - (new Date() - new Date(this.PreEgreso.FechaPreegreso))
            }
            return Math.round(diff / 1000)
        }
    },
    methods: {
        Cargar() {
            return new Promise((resolve, reject) => {
                this.axios.post("/app/v1_ExpedienteEvolucion/BusquedaEgresoEnfermeria", {
                        Serie: this.SerieAdmision,
                        Admision: this.CodigoAdmision
                    })
                    .then(resp => {
                        if (resp.data.codigo == 0)
                            if (resp.data.json.length > 0) {
                                const {
                                    Preegreso,
                                    minutos,
                                    fecha_confirma,
                                    EmpleadoPreEgreso,
                                    PuestoPreegreso,
                                    EmpleadoConfirma,
                                    PuestoConfirma
                                } = resp.data.json[0]

                                this.PreEgreso.FechaPreegreso = Preegreso
                                this.PreEgreso.EmpleadoPreegreso = EmpleadoPreEgreso
                                this.PreEgreso.PuestoPreegreso = PuestoPreegreso

                                this.PreEgreso.FechaConfirma = fecha_confirma
                                this.PreEgreso.EmpleadoConfirma = EmpleadoConfirma
                                this.PreEgreso.PuestoConfirma = PuestoConfirma

                                this.PreEgreso.HabilitarRegistro = !fecha_confirma
                                this.PreEgreso.Minutos = minutos

                                if (this.PreEgreso.FechaConfirma !== '')
                                    this.TextoInfo = 'La admisión ya tiene la confirmación de egreso'

                                this.$emit('onLoaded', this.PreEgreso)
                                // this.IniciarPreegreso()

                            } else { //no tiene proceso de egreso iniciado 
                                // this.IniciarPreegreso()
                            }
                        resolve()
                    })
                    .catch(
                        (error) => reject(error)
                    )
            })
        },
        Grabar() {
            if (this.PreEgreso.HabilitarRegistro) {

                this.axios.post("/app/v1_ExpedienteEvolucion/InsertarConfirmacionEgresoEnfermeria", {
                        SerieAdmision: this.SerieAdmision,
                        Admision: this.CodigoAdmision
                    })
                    .then(resp => {
                        if (resp.data.codigo == 0) {
                            let x = this.Cargar()
                            x.then(
                                () => this.$emit('onSaved', this.PreEgreso)
                            )
                        }
                    })
            }
        },
        handleSubmit(e) {
            e.preventDefault()
            if (this.ExpedienteCargado && this.PreEgreso.HabilitarRegistro)
                this.Grabar()
        },
        IniciarPreegreso() {
            if (this.PreEgreso.FechaPreegreso === '') {
                this.$nextTick(function () {
                    let result = confirm("<i>¿Desea dar egreso al paciente?</i> <p>Tendrá 15 minutos para completar las tareas previas al egreso</p>", "Iniciar egreso del paciente");
                    result.then((dialogResult) => {
                        if (dialogResult) {
                            this.GrabarPreegreso()
                        } else {
                            this.PreEgreso.HabilitarRegistro = false
                            this.submitButtonOptions.disabled = false
                        }
                    });
                })
            }
        },
        ///actualiza la fecha y usuario que inicia el proceso de egreso del paciente
        GrabarPreegreso() {
            this.axios.post("/app/v1_ExpedienteEvolucion/InsertarEgresoEnfermeria", {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.Cargar()
                    }
                })
        },
        TimeUp() {
            this.PreEgreso.Minutos = 15
            this.$emit('onLoaded', this.PreEgreso)
        }
    },
}
</script>

<style>
.egreso-enf-group {
    margin: 10px;
    padding: 10px;
    background-color: rgba(191, 191, 191, 0.15);
    max-height: 500px;
}

.egreso-enf-titulo {
    font-weight: bold;
}

.egreso-enf-parrafo {
    padding-bottom: 1.5px;
}

#preegreso-enfermeria-container .dx-invalid .dx-checkbox-container .dx-checkbox-icon {
    border: 1px solid rgba(217, 83, 79, .9);
}

#preegreso-enfermeria-container .dx-layout-manager .dx-label-h-align .dx-field-item-label {
    white-space: pre-wrap;
    vertical-align: middle !important;
    word-break: break-word;
}

#preegreso-enfermeria-container .dx-layout-manager .dx-label-h-align.dx-flex-layout:not(.dx-field-item-label-align) {
    align-items: center !important;
}

#preegreso-enfermeria-container .dx-item-content .dx-box-item-content {
    place-content: center;
    padding: 5px !important;
    padding-right: 10px !important;
}

#preegreso-enfermeria-container .dx-layout-manager .dx-label-h-align.dx-flex-layout .dx-field-item-label {
    min-width: 90%;
    max-width: 90%;
}

#preegreso-enfermeria-container .dx-layout-manager .dx-label-h-align.dx-flex-layout .dx-field-item-content,
.dx-layout-manager .dx-label-h-align.dx-flex-layout .dx-field-item-content-wrapper {
    min-width: 10%;
    max-width: 10%;
}

#preegreso-enfermeria-container .dx-field-item-label-content {
    width: 90%;
}
</style>
