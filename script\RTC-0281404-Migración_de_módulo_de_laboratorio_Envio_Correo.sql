
USE [master]
GO
IF (SELECT SUSER_ID('USRCORREO')) IS NULL
/*PROD -> */ CREATE LOGIN [USRCORREO] WITH PASSWORD=N'U$rC0rr303l3(t5', DEFAULT_DATABASE=[HOSPITAL], DEFAULT_LANGUAGE=[us_english], CHECK_EXPIRATION=OFF, CHECK_POLICY=OFF



-->> Usuario para cada base de datos a utilizar.

USE [HOSPITAL]
GO
IF (SELECT USER_ID('USRCORREO')) IS NULL
	CREATE USER [USRCORREO] FOR LOGIN [USRCORREO] WITH DEFAULT_SCHEMA=[dbo]
GO


USE [CONTADB]
GO
IF (SELECT USER_ID('USRCORREO')) IS NULL
	CREATE USER [USRCORREO] FOR LOGIN [USRCORREO] WITH DEFAULT_SCHEMA=[dbo]
GO




USE [PLANESMEDICOS]
GO
IF (SELECT USER_ID('USRCORREO')) IS NULL
	CREATE USER [USRCORREO] FOR LOGIN [USRCORREO] WITH DEFAULT_SCHEMA=[dbo]
GO





--->>> Insertar parametros

USE [PCorrelativos]
GO


INSERT INTO PLANESMEDICOS.dbo.PCorrelativos (IdEmpresa,IdControl,Descripcion,Siguiente,Valor,Observaciones,Estado,ValoresWeb) VALUES
	 ('MED','WEB_CORREO_CLAVE    ','Clave de correo origen para envio de correos',0,NULL,'Parametro para envio de correo','A','hbAt28_6GyQ?JYNx'),
	 ('MED','WEB_CORREO_MINUTOS  ','Minutos recursividad servicio',0,NULL,'Minutos para volver a consultar la tabla de correos pendientes de enviar','A','5'),
	 ('MED','WEB_CORREO_ORIGEN   ','Correo Origen para envios',0,NULL,'Parametro para envio de correo','A','<EMAIL>'),
	 ('MED','WEB_CORREO_PUERTO   ','Puerto para envio correo',0,NULL,'Parametro para envio de correo','A','587'),
	 ('MED','WEB_CORREO_SMTP     ','Servidor SMTP',0,NULL,'Parametro para envio de correo','A','smtp.gmail.com');
	 
--->>>> Crear tabla para encabezado correo.

USE [HOSPITAL]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[His_Enviar_Correo_Electronico](
	[Codigo] [int] IDENTITY(1,1) NOT NULL,
	[Empresa] [char](3) NULL,
	[Enviar_a] [varchar](200) NULL,
	[Copiar_a] [varchar](200) NULL,
	[Copiar_Oculto_a] [varchar](200) NULL,
	[Asunto] [varchar](200) NULL,
	[Mensaje_titulo] [varchar](400) NULL,
	[Mensaje_descripcion] [varchar](1000) NULL,
	[Mensaje_Firma] [varchar](max) NULL,
	[Fecha_insertado] [datetime] NULL,
	[Estado] [int] NULL,
	[Resultado_envio] [varchar](200) NULL,
	[Fecha_envio] [datetime] NULL,
	[html] [char](1) NULL,
PRIMARY KEY CLUSTERED 
(
	[Codigo] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[His_Enviar_Correo_Electronico] ADD  DEFAULT (getdate()) FOR [Fecha_insertado]
GO

ALTER TABLE [dbo].[His_Enviar_Correo_Electronico] ADD  DEFAULT ((0)) FOR [Estado]
GO

ALTER TABLE [dbo].[His_Enviar_Correo_Electronico] ADD  DEFAULT ('S') FOR [html]
GO




--->>>> Crear tabla para lectura de archivos.
USE [HOSPITAL]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[His_Enviar_Correo_Electronico_Archivos](
	[Codigo] [int] IDENTITY(1,1) NOT NULL,
	[Id_Enviar_Correo] [int] NULL,
	[Direccion] [varchar](200) NULL,
	[Fecha_Observaciones] [datetime] NULL,
	[Observaciones] [varchar](200) NULL,
	[datos_byte] [varchar](max) NULL,
	[nombre_archivo] [varchar](200) NULL,
PRIMARY KEY CLUSTERED 
(
	[Codigo] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO






--->>>> Permisos 
USE CONTADB;
GRANT SELECT ON seguridadconexiones TO USRCORREO;


USE PLANESMEDICOS;
GRANT SELECT ON PCorrelativos TO USRCORREO;




USE HOSPITAL;
GRANT UPDATE ON His_Enviar_Correo_Electronico TO USRCORREO;
GRANT SELECT ON His_Enviar_Correo_Electronico TO USRCORREO;

GRANT UPDATE ON His_Enviar_Correo_Electronico_Archivos TO USRCORREO;
GRANT SELECT ON His_Enviar_Correo_Electronico_Archivos TO USRCORREO;
GO