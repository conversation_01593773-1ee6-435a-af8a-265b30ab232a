
<!-- ADM021.vue -->
<template ref="admisionExterna">
    <div>

        <DatosPaciente ref="DatosPacienteComponent" v-show="false">
        </DatosPaciente>

        <div class="info-adm-process">
            <div class="datos-paciente">
                <SerieCodigoAdmision ref="serieCodigoAdmisionComponent" 
                                    @selected-option-changed="onSelectedOptionChanged"
                                    v-model="AdmisionData.TipoAdmision" 
                                    :show-options="false"
                                    :location-data="'upper'">
                </SerieCodigoAdmision>
            </div>
            <div class="numero-proceso">
                <div class="row justify-content-md-center">
                    <div class="col-lg-4">


                        <vs-row v-show="AdmisionData.Codigo">
                            <h4 class="icon-bubbles"> Codigo Admisión: </h4>
                            <strong>{{ AdmisionData.Serie+ ' ' + AdmisionData.Codigo }}</strong>
                        </vs-row>
                    </div>
  
                </div>
            </div>
        </div>

        <ConsultarMedicos ref="ConsultarMedicosComponent" 
                          v-on:codigo-medico="obtenerCodigoMedico"
                          v-on:no-mail-checked="validaPoseeCorreo"
                          v-on:mail-changed="obtieneCorreoMedico"            
                          >
        </ConsultarMedicos>

        <DiagnosticoCovid ref="diagnosticoCovidComponent" 
                          @checked-options-changed="onCheckedOptionChanged"
                          @vacunas-seleccionadas="obtenerListaVacunas" 
                          :id-paciente="parseInt(PacienteData.Codigo)">
        </DiagnosticoCovid>

        <TiposDescuento ref="tipoDescuentoComponent" 
                        @selected-tipodes-changed="onSelectedTipoChanged"
                        v-model="AdmisionData.TipoDescuento" 
                        :tipo-admision="parseInt(AdmisionData.TipoAdmision)"
                        :tipo-descuento="'Salud Siempre/Percapitados'"                        
                        >
        </TiposDescuento>

        <div v-show="showButtons">
            <vs-button style="margin-right:1px" v-on:click="submit()">Crear Admisión Interna
            </vs-button>
        </div>
    </div>
</template>
  
<script>

import { mapFields } from "vuex-map-fields";

import DatosPaciente from "/src/components/sermesa/modules/paciente/DatosPaciente.vue"
import DiagnosticoCovid from "/src/components/sermesa/modules/admisiones/DiagnosticoCovid.vue";
import TiposDescuento from "/src/components/sermesa/modules/admisiones/TiposDescuento.vue";
import SerieCodigoAdmision from "/src/components/sermesa/modules/admisiones/SerieCodigoAdmision.vue";
import ConsultarMedicos from "/src/components/sermesa/modules/admisiones/ConsultarMedicos.vue"


export default {
    name: "AdmisionesExternas",
    components: {
        DatosPaciente,
        DiagnosticoCovid,
        TiposDescuento,
        SerieCodigoAdmision,
        ConsultarMedicos,
    },
    data() {
        return {
            IdPaciente: 0,
            PaqueteQx: '',
            initialSerie: '',
            selectedTipo: '',
            NombreMedico: '',
            showButtons: false
        }
    },
    computed: {
        ...mapFields('admision', ["AdmisionData"]),
        ...mapFields('paciente', ["PacienteData"])

    },
    updated() {

    },
    created() {
        this.AdmisionData.TipoAdmision = 2
        this.IdPaciente = this.PacienteData.Codigo

    },
    beforeMount() {

    },
    mounted() {
        this.AdmisionData.TipoAdmision = 2
    },
    methods: {
        async submit() {

            if (this.AdmisionData.Codigo !== undefined && this.AdmisionData.Codigo > 0) {
                     // El código existe y es mayor que cero
                return;
            }
                
                
            
            this.AdmisionData.Paciente = this.PacienteData.Codigo
            this.AdmisionData.NombreFactura = this.PacienteData.NombreFactura
            this.AdmisionData.DireccionFactura = this.PacienteData.DireccionFactura
            this.AdmisionData.IdCliente = this.PacienteData.IdCliente
            this.AdmisionData.DPI       = this.PacienteData.DPI
            this.AdmisionData.Nit       = this.PacienteData.Nit
            this.AdmisionData.FechaNacimiento = this.PacienteData.Nacimiento
            
            

            this.$store.dispatch('admision/crearAdmision')
                .then(() => {
                    
                    this.$vs.dialog({
                        type: 'confirm',
                        color: '#ED8C72',
                        acceptText: 'Continuar',
                        title: 'Se creo la Admisión No.',
                        text: this.AdmisionData.Serie+ ' '+ this.AdmisionData.Codigo,
                        // clientWidth: 100,
                        accept: () => {
                            this.$emit("ready", true);
                        }
                    })

                })
                .catch((error) => {    
                    this.$vs.dialog({
                        type: 'confirm',
                        color: '#ED8C72',
                        acceptText: 'Continuar',
                        title: 'Error en gestión de Paciente',
                        text: error.descripcion[0].DescError,
                        clientWidth: 100,
                        accept: () => {
                            this.$emit("ready", false);
                        }
                    });
                })


        },
        onCheckedOptionChanged(diagnostico) {

            this.DisgnosticoCovid = diagnostico
            this.AdmisionData.DiagnosticoCOVID = (diagnostico.length === 0) ? 0 : 1
        },
        obtenerListaVacunas(vacunas) {
            this.AdmisionData.PrimeraVacunaCOVID = vacunas[0]?.Codigo || 0
            this.AdmisionData.SegundaVacunaCOVID = vacunas[1]?.Codigo || 0
            this.AdmisionData.TerceraVacunaCOVID = vacunas[2]?.Codigo || 0
        },
        onSelectedOptionChanged(admision) {
            this.AdmisionData.TipoAdmision = admision.TipoAdmision            
            this.AdmisionData.Serie = admision.Serie
            this.AdmisionData.NivelPrecios = admision.NivelDePrecios                    
        },
        onSelectedTipoChanged() {

        },
        obtenerCodigoMedico(datosMedico) {
            this.AdmisionData.Medico = datosMedico.CodigoMedico
            this.AdmisionData.CorreoElectronicoMedico = datosMedico.CorreoMedico        
        },
        validaPoseeCorreo(poseeCorreo){

            this.AdmisionData.Medico = poseeCorreo.CodigoMedico
            this.AdmisionData.CorreoElectronicoMedico = poseeCorreo.CorreoMedico
            
        },
        obtieneCorreoMedico(correoMedico){            
            this.AdmisionData.Medico = correoMedico.CodigoMedico
            this.AdmisionData.CorreoElectronicoMedico = correoMedico.CorreoMedico    
        },
    },
};
</script>


<style scoped>
* {
    box-sizing: border-box;
    padding: 2px;
    margin: 0;
}


.info-adm-process {
    display: grid;
    width: 100%;
    height: 100%;
    grid-template-areas: "datos-paciente numero-proceso";
    grid-template-columns: 80% 20%;
    grid-template-rows: 100%;
}

.info-adm-process>div {
   
    border-radius: 5px;
    margin: 5px;
}

.datos-paciente {

    grid-area: datos-paciente;
}

.numero-proceso {
    grid-area: numero-proceso;
    padding-top: 1rem;
    padding-bottom: 1rem;
    padding-left: 1rem;
    padding-right: 1rem;
    border: 1px solid #888;
    border-radius: 5px;
    margin: 5px;
}
</style>