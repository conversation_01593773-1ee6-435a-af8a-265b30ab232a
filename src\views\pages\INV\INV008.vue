<template>
<vx-card title="Consulta de Producto">
    <!--  -->
    <vs-popup classContent="popup-example" :button-close-hidden="false" :title="'Características del Producto'" style="z-index:999999" :active.sync="show">
        <div v-if="show" id="loading" class="vs-con-loading__container" style="padding:10px;border-radius:5px; border:1px solid #ccc; font: 14px Arial;">
            <h6 class="font-bold mb-3">Vista:</h6>
            <div class="flex flex-wrap">
                <div class="w-full">
                    <vs-radio style="float:left;padding:1px 5px" v-for="(fila, index) in ListadoVista" :key="index" v-model="selectvista" :vs-name="'Vista_'+index" :vs-value="fila.Codigo">
                        {{fila.Nombre}}
                    </vs-radio>
                </div>
            </div>

            <vs-divider>Existencia</vs-divider>
            <vs-table2 max-items="10" pagination search :data="existencia_filtro" noDataText="Sin datos disponibles">
                <template slot="thead">
                    <th order="Bodega" orderType="number">Bodega</th>
                    <th order="Nombre">Nombre Bodega</th>
                    <th>Existencia</th>
                    <!-- <vs-th>Fecha Inicial</vs-th>
                                <vs-th>E. Inicial</vs-th>
                                <vs-th>Ultimo Inventario</vs-th> -->
                    <!-- <vs-th>Opciones</vs-th> -->
                </template>
                <template slot-scope="{data}">
                    <!-- <template slot-scope="{data}"> -->
                    <!-- <vs-tr :key="indextr" v-for="(tr, indextr) in data"> -->
                    <tr :key="indextr" v-for="(tr, indextr) in data">
                        <vs-td2>{{tr.Bodega}}</vs-td2>
                        <vs-td2>{{tr.Nombre}}</vs-td2>
                        <vs-td2>{{tr.Existencia}}</vs-td2>

                        <!-- <td><span class="subtotal">{{ tr.reduce((a, c) => a + parseInt(c.Existencia), 0) }}</span></td> -->

                        <!-- <td><span class="subtotal">{{ Totalexistencia.reduce((a, c) => a + parseInt(tr.Existencia), 0) }}</span></td> -->

                        <!-- <vs-td :data="tr.FechaInicial">{{tr.FechaInicial}}</vs-td>
                                <vs-td :data="tr.ExistenciaFisico">{{tr.ExistenciaFisico}}</vs-td>
                                <vs-td :data="tr.UltimoInventario">{{tr.UltimoInventario}}</vs-td> -->
                        <!-- <vs-td>
                                        <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px"></vs-button> 
                                </vs-td> -->
                    </tr>
                </template>
            </vs-table2>

            <vs-divider></vs-divider>
            <!-- <vs-button color="primary" style="float:right" type="filled" icon-pack="feather" icon="icon-file-text" @click="fichaproducto_reporte()" > Ver Especificaciones</vs-button> -->
            <div class="border-top border-right d-flex align-items-between flex-column py-1 col-6">
                <p class="card-text text-muted mb-0">
                    <font style="vertical-align: inherit;">
                        <font style="vertical-align: inherit;">TOTAL EXISTENCIA: </font>
                    </font>
                </p>
                <h3 class="font-weight-bolder mb-0">
                    <font style="vertical-align: inherit;">
                        <font style="vertical-align: inherit;">{{Totalexistencia}}</font>
                    </font>
                </h3>
            </div>

            <vs-button color="primary" style="float:right" type="border" @click="show=false"> Cancelar</vs-button>
            <div style="clear:both"></div>

        </div>
    </vs-popup>

    <!-- reportes -->
    <vs-popup id="contentreport" classContent="popup-generar" title="Informe" :active.sync="reporte" fullscreen style="z-index:99999;height:100%">
        <embed v-if="reporte_src!=''" type="application/pdf" :src="reporte_src" ref="pdfDocument" width="100%" height="98%" />
    </vs-popup>

    <div class="content content-pagex">

        <div class="algolia-header mb-4">
            <div class="flex justify-between items-end flex-grow">
                <div class="ais-Stats">
                    <p class="font-semibold md:block hidden">
                        {{producto.length }} resultados encontrados
                    </p>
                </div>

            </div>
        </div>

        <div class="ais-Hits">

            <!-- Busqueda Producto. -->
            <div class="ais-SearchBox">
                <div>
                    <div class="relative mb-8">
                        <div class="vs-component vs-con-input-label vs-input w-full vs-input-shadow-drop vs-input-no-border d-theme-input-dark-bg vs-input-primary">
                            <div class="vs-con-input">
                                <input type="text" class="vs-inputx vs-input--input large" placeholder="Buscar" style="border: 1px solid rgba(0, 0, 0, 0.2);" v-model="producto_buscar" @change="cargar_producto">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Listado de Producto -->
            <div class="flex flex-wrap">
                <!-- producto producto_filtro -->
                <div pagination class="lg:w-1/3 sm:w-1/2 w-full" style="padding:0px 8px" v-for="(tr, indextr) in producto" :key="indextr">
                    <div class="vx-card grid-view-item mb-base overflow-hidden">

                        <div class="vx-card__collapsible-content vs-con-loading__container">

                            <div class="item-img-container bg-white h-64 flex items-center justify-center mb-4 cursor-pointer">
                                <!-- <img :src="'data:image/png;base64, ' +tr.Imagen" v-if="tr.Imagen"  alt="" class="grid-view-img px-4" style="max-height:250px"/> -->
                                <expandable-image class="image" :src="'data:image/png;base64, ' +tr.Imagen" v-if="tr.Imagen">
                                </expandable-image>
                                <img v-else src="@/assets/images/others/notfound.png" alt="" class="grid-view-img px-4" style="max-height:250px" />
                            </div>

                            <div style="display: block" class="flex flex-wrap">
                                <h6 style="display: block;margin: 0 0 0 auto" class="font-bold">COSTO PROMEDIO</h6>
                            </div>

                            <div class="item-details px-4">
                                <div class="flex justify-between items-center">
                                    <div class="text-warning border border-solid border-warning flex py-1 px-2 rounded">
                                        <span class="text-sm mr-1">{{tr.Clase}}</span>
                                        <span class="feather-icon select-none relative">
                                            <i class="feather icon-star"></i>
                                        </span>
                                    </div>

                                    <h3 class="font-bold">{{tr.Codigo}} </h3>
                                    <h6 class="font-bold">Q {{tr.CostoPromedio}}</h6>
                                </div>

                                <div class="my-4">
                                    <!-- <h6 class="truncate font-semibold mb-1 hover:text-primary cursor-pointer">{{tr.Nombre}}
                                    </h6> -->
                                    <h6 style="vertical-align: inherit;" class="font-semibold mb-1 hover:text-primary cursor-pointer">{{tr.Nombre}}
                                    </h6>
                                    <p class="item-description text-sm">{{tr.NombreGenerico}}</p>
                                    <p class="item-description text-sm">PROVEEDOR:{{tr.Proveedor}}</p>
                                </div>
                            </div>

                            <div class="flex flex-wrap">
                                <div @click="especificacion(tr)" class="item-view-primary-action-btn bg-success p-3 flex flex-info items-center justify-center  text-white cursor-pointer">
                                    <span class="feather-icon select-none relative">
                                        <i class="feather icon-package"></i>
                                    </span>
                                    <!-- <span class="text-sm font-semibold ml-2">{{tr.Codigo}}</span> -->
                                    <span class="text-sm font-semibold ml-2">VER PRODUCTO</span>
                                </div>
                                <div @click="opciones(tr)" class="item-view-secondary-action-btn bg-primary p-3 flex flex-grow items-center justify-center text-white cursor-pointer">
                                    <span class="feather-icon select-none relative">
                                        <i class="feather icon-external-link"></i>
                                    </span>
                                    <span class="text-sm font-semibold ml-2">VER EXISTENCIAS</span>
                                </div>
                            </div>

                        </div>

                        <div class="vx-card__code-container collapsed" style="max-height: 0px; display: none;">
                            <div class="code-content">
                                <pre class="language-markup"><code class="language-markup"></code></pre>
                            </div>
                        </div>

                    </div>
                </div>

            </div>

        </div>

    </div>

</vx-card>
</template>

<script>
export default {
    data() {
        return {
            show: false,
            producto: [],
            producto_buscar: '',
            ListadoExistencia: [],
            productos: {
                codigo: '',
            },
            ListadoVista: [],
            selectvista: {},
            //Reporte Producto
            reporte_src: null,
            reporte: false,
            Totalexistencia: 0,
        }
    },
    computed: {
        existencia_filtro() {

            let acum = 0;

            if (this.selectvista == '0') {

                // this.ListadoExistencia.forEach(p => {
                //     acum += parseFloat(p.Existencia)
                //     this.Totalexistencia = acum 
                // })

                // return this.ListadoExistencia
                const newexistencia = this.ListadoExistencia.map(a => ({
                    ...a
                }))

                newexistencia.forEach(p => {
                    acum += parseFloat(p.Existencia)
                    this.Totalexistencia = acum
                })

                return newexistencia

            } else if (this.selectvista == '1') {

                // this.ListadoExistencia.filter(data => data.Existencia > 0).forEach(p => {
                //     acum += parseFloat(p.Existencia)
                //     this.Totalexistencia = acum 
                // })

                // return  this.ListadoExistencia.filter(data => data.Existencia > 0)

                const newexistencia2 = this.ListadoExistencia.map(a => ({
                    ...a
                })).filter(data => data.Existencia > 0)

                newexistencia2.forEach(p => {
                    acum += parseFloat(p.Existencia)
                    this.Totalexistencia = acum
                })

                return newexistencia2

            }
            return false
        },
        // total() {
        //     const newexistencia = this.existencia_filtro.map(a => ({...a})) // crea una copia de movimientos
        //     let acum = 0
        //     newexistencia.forEach(p => {
        //         acum += parseFloat(p.Existencia)
        //         this.Totalexistencia = acum // agrego esta propiedad para guardar el acumulado
        //     })
        //     return newexistencia
        // },
        // filteredArray() {
        //     return this.ListadoExistencia.filter(data => this.Totalexistencia==data.Existencia);
        // },
    },
    methods: {
        cargar_producto() {
            this.show = false

            this.producto = []
            this.axios.post('/app/inventario/invFichaProducto', {
                    Nombre: this.producto_buscar.trim(),
                })
                .then(resp => {

                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {

                        this.producto = []
                        resp.data.json.map(data => {
                            this.producto.push({
                                ...data
                            })
                        })
                    } else {

                        this.producto = []
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Producto',
                            text: (resp.data.codigo < 0) ? resp.data.descripcion : 'Sin Registros',
                        })

                    }

                })
                .catch(() => {

                    this.$vs.notify({
                        color: 'danger',
                        title: 'Producto',
                        text: 'Error al cargar los Productos.',
                    })
                })
        },
        cargar_Existencia_Productos() {
            setTimeout(() => {
                this.$vs.loading({
                    container: "#loading",
                })
            }, 100)
            this.ListadoExistencia = []
            this.axios.post('/app/inventario/invListaExistencia', {
                CodigoNuevo: this.Codigo
            }).then(resp => {
                setTimeout(() => {
                    this.$vs.loading.close("#loading > .con-vs-loading")
                }, 200)
                this.ListadoExistencia = []
                resp.data.json.map(data => {
                    this.ListadoExistencia.push({
                        ...data
                    })
                })
            }).catch(() => {
                setTimeout(() => {
                    this.$vs.loading.close("#loading > .con-vs-loading")
                }, 200)
            })

        },
        opciones(op) {
            this.show = true
            this.Codigo = op.Codigo
            this.ListadoExistencia = []
            this.Totalexistencia = 0
            this.cargar_Existencia_Productos()
            this.ListadoVista = [
                {
                    Codigo: '0',
                    Nombre: 'Todas'
                },
                {
                    Codigo: '1',
                    Nombre: 'Diferente de Cero'
                },
            ]
            this.selectvista = '1'
        },
        especificacion(op) {
            this.Codigo = op.Codigo
            this.fichaproducto_reporte()
        },
        fichaproducto_reporte() {
            this.$reporte_modal({
                Nombre: "Ficha del Producto",
                Opciones: {
                    Codigo: this.Codigo,
                }
            })
        },
    },
    mounted() {
        this.cargar_producto()
    },
}
</script>

<style>
.popup-generar {
    height: 100%
}
</style>
