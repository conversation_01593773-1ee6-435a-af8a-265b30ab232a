USE [HOSPITAL]
GO
/****** Object:  UserDefinedFunction [dbo].[FnNombreCompletoPaciente]    Script Date: 12/09/2023 23:10:14 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Author:		<PERSON><PERSON><PERSON>
-- Create date: 2023-01-25
-- Description:	Funcion que devuelve el nombre completo del paciente en una sola cadena 
-- =============================================
CREATE FUNCTION [dbo].[FnNombreCompletoPaciente]
(
	-- Add the parameters for the function here
	@ICodigoPaciente int
)
RETURNS VARCHAR(200)
AS
BEGIN
	DECLARE @WNombre VARCHAR(30),
	@WApellido VARCHAR(30),
	@WApellidoCasada VARCHAR(30),
	@WSexo CHAR(1),
	@WEstadoCivil CHAR(1),

	@WResultado VARCHAR(200) = ''

	SELECT 
		@WNombre = [Nombre], @WApellido = [Apellido], @WApellidoCasada = [ApellidoCasada], @WSexo = [Sexo], @WEstadoCivil = [Estado_civil]
	FROM 
		[HOSPITAL].[dbo].[Pacientes]
	WHERE Empresa = 'MED' 
		AND Codigo = @ICodigoPaciente

	SET @WResultado = CONCAT(RTRIM(@WNombre), ' ', RTRIM(@WApellido))
	SET @WApellidoCasada = RTRIM(REPLACE(@WApellidoCasada,'-',''))
	IF @WSexo = 'F' AND @WEstadoCivil IN ('C','U') AND @WApellidoCasada <> ''
	BEGIN
		SET @WResultado = CONCAT(@WResultado, ' DE ' , @WApellidoCasada )
	END
	
	RETURN REPLACE (@WResultado, 'DE DE DE ', 'DE DE ')

END
