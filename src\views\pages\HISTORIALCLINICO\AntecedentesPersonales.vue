<template>
<div class="p-2">
    <vx-card>
        <vs-collapse accordion ref="collapse" type="margin" class="collapse-color" :disabled="IdCliente !== null ? false: true">
            <vs-collapse-item v-for="(item, index) in tabsAntecedentes" v-bind:key="index" icon-arrow="label" v-show=" item.value !== 9 || (item.value === 9 && Sexo === 'F' && Sexo !== '' && Sexo !== null)">
                <div slot="header" style="display: flex; flex-wrap: wrap; justify-content: start; align-items: center;">
                    <font-awesome-icon :icon="['fas', item.icon]" class="i-size pr-2" /> {{item.name}}
                </div>
                <div v-if="item.value === 9" class="pb-4">
                    <DxForm :form-data.sync="formularioGinecologico" labelMode="floating">
                        <DxFormGroupItem :col-count-by-screen="colCountByScreen">
                            <DxFormGroupItem :col-count-by-screen="colCountByScreen1">
                                <DxFormGroupItem>
                                    <DxFormItem data-field="Gestas" editor-type="dxNumberBox" :editor-options="{ width: 100, readOnly: true, value: antecedentes.Gestas }">
                                        <DxFormLabel :text="'Gestas totales'" />
                                    </DxFormItem>
                                    <DxFormItem data-field="Abortos" editor-type="dxNumberBox" :editor-options="{ width: 100, readOnly: true, value: antecedentes.Abortos }" />
                                </DxFormGroupItem>
                                <DxFormGroupItem>
                                    <DxFormItem data-field="Partos" editor-type="dxNumberBox" :editor-options="{ width: 100, readOnly: true, value: antecedentes.Partos }" />
                                    <DxFormItem data-field="Cesáreas" editor-type="dxNumberBox" :editor-options="{ width: 100, readOnly: true, value: antecedentes.Cesareas }" />
                                </DxFormGroupItem>
                            </DxFormGroupItem>
                            <DxFormGroupItem :col-count-by-screen="colCountByScreen1" :caption="colCountByScreen === 1 ? ' ':''">
                                <DxFormGroupItem>
                                    <DxFormItem data-field="Menarquia" editor-type="dxNumberBox" :editor-options="{ min: 0, width: 120, value: formularioGinecologico.Menarquia, step: 1, format: '#0', readOnly: TipoBusqueda }">
                                        <DxFormLabel :text="'Edad menarquia'" />
                                    </DxFormItem>
                                    <DxFormItem data-field="Menopausia" editor-type="dxNumberBox" :editor-options="{ min: 0, width: 120, value: formularioGinecologico.Menopausia, step: 1, format: '#0', readOnly: TipoBusqueda }">
                                        <DxFormLabel :text="'Edad menopausia'" />
                                    </DxFormItem>
                                    <DxFormItem data-field="Gestando" editor-type="dxCheckBox" :editor-options="{ value: formularioGinecologico.Gestando, onValueChanged: CalcularFechaAproximadaParto, readOnly: TipoBusqueda }">
                                        <DxFormLabel :text="'¿Está gestando?'" />
                                    </DxFormItem>
                                </DxFormGroupItem>
                                <DxFormGroupItem>
                                    <DxFormItem data-field="FUR" editor-type="dxDateBox" :editor-options="{ width: 120, readOnly: TipoBusqueda }">
                                        <DxFormLabel :text="'Fecha última regla'" />
                                    </DxFormItem>
                                    <DxFormItem data-field="FURC" editor-type="dxDateBox" :editor-options="{ width: 120, readOnly: TipoBusqueda }">
                                        <DxFormLabel :text="'FUR confiable'" />
                                    </DxFormItem>
                                    <DxFormItem data-field="FechaParto" editor-type="dxDateBox" :visible="formularioGinecologico.Gestando" :editor-options="{ width: 120, readOnly: true }">
                                        <DxFormLabel :text="'Fecha parto aprox.'" />
                                    </DxFormItem>
                                </DxFormGroupItem>
                            </DxFormGroupItem>
                            <DxFormEmptyItem />
                            <DxFormGroupItem :col-count-by-screen="colCountByScreen1" :visible="contShowButton !== 0">
                                <DxFormButtonItem :button-options="submitButtonOptions" horizontal-alignment="left" verical-alignment="center" />
                            </DxFormGroupItem>
                        </DxFormGroupItem>
                    </DxForm>

                </div>
                <div>
                    <DxDataGrid :ref="dataGridRefKey" v-bind="DefaultDxGridConfiguration" :data-source="antecedentes[item.data]" :paging="{enabled: item.value === 8 ? false : true}" :filter-sync-enabled="true" :headerFilter="{visible:false,allowSearch:false}" :searchPanel="{visible: true }" :sorting="{mode: 'none'}" :height="'100%'" :columns="item.columnas" @editor-preparing="Preparing" :on-row-inserting="item.onRowInserting" :on-row-inserted="item.onRowInserted" :on-row-updating="item.value === 8 ? item.onRowUpdating : null" :on-row-updated="item.value === 8 ? item.onRowUpdated : null" :on-row-removing="item.onRowRemoving" :on-row-removed="item.onRowRemoved" @cell-prepared="onCellPreparedVacunas" @editing-start="editingStart">
                        <DxDataGridSelection mode="single" />
                        <DxDataGridEditing :allow-updating="(item.value === 8 && !TipoBusqueda && !permisoEditar) ? true : false" :allow-adding="(item.value === 8 || TipoBusqueda || !permisoEditar) ? false : true" :allow-deleting="item.allowDeleting" :mode="item.value === 8 ? 'batch' : 'form'" :use-icons="true" :form="{ showValidationSummary: true }" />

                    </DxDataGrid>
                </div>
            </vs-collapse-item>
        </vs-collapse>

    </vx-card>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'
import 'devextreme-vue/lookup'
import 'devextreme-vue/calendar'
import 'devextreme-vue/select-box'
import 'devextreme-vue/check-box'
import 'devextreme-vue/radio-group'
import 'devextreme-vue/date-box'

const dataGridRefKey = 'antecedentesGrid'

export default {
    name: 'AntecedentesPersonales',
    components: {},
    data() {
        return {
            indiceTab: 0,
            buttons: [{
                name: 'Evoluciones',
                icon: 'arrow-up-right-dots'
            }, {
                name: 'Laboratorios',
                icon: 'vial-circle-check'
            }, {
                name: 'Exámenes',
                icon: 'radiation'
            }, {
                name: 'Encamamiento',
                icon: 'bed'
            }, ],

            tabsAntecedentes: [
                //-------------------------------
                //---------- ALERGICOS ----------
                //-------------------------------
                {
                    name: 'Alérgicos',
                    icon: 'head-side-cough',
                    value: 1,
                    open: false,
                    data: 'AntAlergicos',
                    onRowInserting: this.AgregarAlergia,
                    onRowInserted: this.BusquedaAntecedentesAlergicos,
                    onRowRemoving: this.EliminarAlergia,
                    onRowRemoved: this.BusquedaAntecedentesAlergicos,
                    allowDeleting: this.AllowDeletingAlergias,
                    columnas: [{
                            dataField: "FechaRegistro",
                            caption: "Fecha registro",
                            alignment: "center",
                            format: "dd/MM/yyyy",
                            dataType: "datetime",
                            formItem: {
                                visible: false
                            }
                        },
                        {
                            dataField: "Descripcion",
                            caption: "Descripción",
                            editorType: "dxSelectBox",
                            alignment: "center",
                            editorOptions: {
                                dataSource: this.Alergias,
                                displayExpr: "Descripcion",
                                displayValue: "Descripcion",
                                valueExpr: "Codigo",
                                placeholder: "Seleccione alergia",
                                name: "Alergias",
                                dropDownOptions: {
                                    width: '750px'
                                },
                            },
                            formItem: {
                                isRequired: true,
                                label: {
                                    visible: false
                                }
                            }
                        },
                        {
                            dataField: 'Observaciones',
                            alignment: "center",
                            editorOptions: {
                                maxLenght: 100,
                                labelMode: 'floating'
                            },
                            formItem: {
                                label: {
                                    text: 'Observaciones',
                                    visible: false
                                }
                            }
                        },
                    ],
                },
                //----------------------------------
                //---------- MEDICAMENTOS ----------
                //----------------------------------
                {
                    name: 'Medicamentos',
                    icon: 'pills',
                    value: 2,
                    open: false,
                    data: 'AntMedicamentos',
                    onRowInserting: this.AgregarMedicamento,
                    onRowInserted: this.BusquedaAntecedentesMedicamentos,
                    onRowRemoving: this.EliminarMedicamento,
                    onRowRemoved: this.BusquedaAntecedentesMedicamentos,
                    allowDeleting: this.AllowDeletingMedicamentos,
                    columnas: [{
                            dataField: "FechaRegistro",
                            caption: "Fecha registro",
                            alignment: "center",
                            format: "dd/MM/yyyy",
                            dataType: "datetime",
                            formItem: {
                                visible: false,
                            }
                        },
                        {
                            dataField: 'Descripcion',
                            caption: "Descripción",
                            alignment: "center",
                            editorOptions: {
                                maxLenght: 60,
                                labelMode: 'floating'
                            },
                            formItem: {
                                isRequired: true,
                                label: {
                                    text: 'Descripción',
                                    visible: false
                                }
                            }
                        },
                    ]
                },
                //-----------------------------
                //---------- MEDICOS ----------
                //-----------------------------
                {
                    name: 'Médicos',
                    icon: 'user-doctor',
                    value: 3,
                    open: false,
                    data: 'AntMedicos',
                    onRowInserting: (x) => {
                        x.data.Tipo = 'M';
                        x.data.Fecha = new Date();
                        this.AgregarAntecedentePersonal(x)
                    },
                    onRowInserted: () => {
                        this.BusquedaAntecedentesPersonales(2)
                    },
                    onRowRemoving: this.EliminarAntecedentePersonal,
                    onRowRemoved: () => {
                        this.BusquedaAntecedentesPersonales(2)
                    },
                    allowDeleting: this.AllowDeletingAntecedentesPersonales,
                    columnas: [{
                            dataField: "FechaRegistro",
                            caption: "Fecha registro",
                            alignment: "center",
                            format: "dd/MM/yyyy",
                            dataType: "datetime",
                            formItem: {
                                visible: false
                            }
                        },
                        {
                            dataField: "Descripcion",
                            caption: "Descripción",
                            alignment: "center",
                            editorOptions: {
                                labelMode: 'floating'
                            },
                            formItem: {
                                isRequired: true,
                                label: {
                                    text: 'Descripción',
                                    visible: false
                                }
                            }
                        },
                    ]
                },
                //---------------------------------
                //---------- QUIRURGICOS ----------
                //---------------------------------
                {
                    name: 'Quirúrgicos',
                    icon: 'bed-pulse',
                    value: 4,
                    open: false,
                    data: 'AntQuirurgicos',
                    onRowInserting: (x) => {
                        x.data.Tipo = 'Q';
                        this.AgregarAntecedentePersonal(x)
                    },
                    onRowInserted: () => {
                        this.BusquedaAntecedentesPersonales(2)
                    },
                    onRowRemoving: this.EliminarAntecedentePersonal,
                    onRowRemoved: () => {
                        this.BusquedaAntecedentesPersonales(2)
                    },
                    allowDeleting: this.AllowDeletingAntecedentesPersonales,
                    columnas: [{
                            dataField: "FechaRegistro",
                            caption: "Fecha registro",
                            alignment: "center",
                            format: "dd/MM/yyyy",
                            dataType: "datetime",
                            formItem: {
                                visible: false
                            }
                        },
                        {
                            dataField: "Fecha",
                            caption: "Fecha cirugía",
                            alignment: "center",
                            format: "dd/MM/yyyy",
                            dataType: "datetime",
                            editorType: "dxCalendar",
                            editorOptions: {
                                max: new Date()
                            },
                            formItem: {
                                isRequired: true,
                                label: {
                                    visible: false
                                }
                            }
                        },
                        {
                            dataField: "Descripcion",
                            caption: "Descripción",
                            alignment: "center",
                            editorOptions: {
                                colCount: 3,
                                labelMode: 'floating'
                            },
                            formItem: {
                                isRequired: true,
                                label: {
                                    text: 'Descripción',
                                    visible: false
                                }
                            }
                        },
                    ]
                },
                //---------------------------------
                //---------- TRAUMATICOS ----------
                //---------------------------------
                {
                    name: 'Traumáticos',
                    icon: 'crutch',
                    value: 5,
                    open: false,
                    data: 'AntTraumaticos',
                    onRowInserting: (x) => {
                        x.data.Tipo = 'T';
                        x.data.Fecha = new Date();
                        this.AgregarAntecedentePersonal(x)
                    },
                    onRowInserted: () => {
                        this.BusquedaAntecedentesPersonales(2)
                    },
                    onRowRemoving: this.EliminarAntecedentePersonal,
                    onRowRemoved: () => {
                        this.BusquedaAntecedentesPersonales(2)
                    },
                    allowDeleting: this.AllowDeletingAntecedentesPersonales,
                    columnas: [{
                            dataField: "FechaRegistro",
                            caption: "Fecha registro",
                            alignment: "center",
                            format: "dd/MM/yyyy",
                            dataType: "datetime",
                            formItem: {
                                visible: false
                            }
                        },
                        {
                            dataField: "Descripcion",
                            caption: "Descripción",
                            alignment: "center",
                            editorOptions: {
                                labelMode: 'floating'
                            },
                            formItem: {
                                isRequired: true,
                                label: {
                                    text: 'Descripción',
                                    visible: false
                                }
                            }
                        },
                    ]
                },
                //--------------------------------
                //---------- FAMILIARES ----------
                //--------------------------------
                {
                    name: 'Familiares',
                    icon: 'people-group',
                    value: 6,
                    open: false,
                    data: 'AntFamiliares',
                    onRowInserting: (x) => {
                        x.data.Tipo = 'F';
                        x.data.Fecha = new Date();
                        this.AgregarAntecedentePersonal(x)
                    },
                    onRowInserted: () => {
                        this.BusquedaAntecedentesPersonales(2)
                    },
                    onRowRemoving: this.EliminarAntecedentePersonal,
                    onRowRemoved: () => {
                        this.BusquedaAntecedentesPersonales(2)
                    },
                    allowDeleting: this.AllowDeletingAntecedentesPersonales,
                    columnas: [{
                            dataField: "FechaRegistro",
                            caption: "Fecha registro",
                            alignment: "center",
                            format: "dd/MM/yyyy",
                            dataType: "datetime",
                            formItem: {
                                visible: false
                            }
                        },
                        {
                            dataField: "Descripcion",
                            caption: "Descripción",
                            alignment: "center",
                            editorOptions: {
                                labelMode: 'floating'
                            },
                            formItem: {
                                isRequired: true,
                                label: {
                                    text: 'Descripción',
                                    visible: false
                                }
                            }
                        },
                    ]
                },
                //-----------------------------
                //---------- HABITOS ----------
                //-----------------------------
                {
                    name: 'Hábitos',
                    icon: 'brain',
                    value: 7,
                    open: false,
                    data: 'AntHabitos',
                    onRowInserting: (x) => {
                        x.data.Tipo = 'H';
                        x.data.Fecha = new Date();
                        this.AgregarAntecedentePersonal(x)
                    },
                    onRowInserted: () => {
                        this.BusquedaAntecedentesPersonales(2)
                    },
                    onRowRemoving: this.EliminarAntecedentePersonal,
                    onRowRemoved: () => {
                        this.BusquedaAntecedentesPersonales(2)
                    },
                    allowDeleting: this.AllowDeletingAntecedentesPersonales,
                    columnas: [{
                            dataField: "FechaRegistro",
                            caption: "Fecha registro",
                            alignment: "center",
                            format: "dd/MM/yyyy",
                            dataType: "datetime",
                            formItem: {
                                visible: false
                            }
                        },
                        {
                            dataField: "Descripcion",
                            caption: "Descripción",
                            alignment: "center",
                            editorOptions: {
                                labelMode: 'floating'
                            },
                            formItem: {
                                isRequired: true,
                                label: {
                                    text: 'Descripción',
                                    visible: false
                                }
                            }
                        },
                    ]
                },
                //-----------------------------
                //---------- VACUNAS ----------
                //-----------------------------
                {
                    name: 'Vacunas',
                    icon: 'syringe',
                    value: 8,
                    open: false,
                    data: 'AntVacunas',
                    onRowUpdating: this.AgregarAntecedenteVacuna,
                    onRowUpdated: this.BusquedaAntecedentesVacunas,
                    columnas: [{
                            dataField: "Edad",
                            alignment: "center",
                            editorOptions: {
                                disabled: true
                            },
                        },
                        {
                            dataField: "Protege",
                            caption: "Me protege de",
                            alignment: "center",
                            editorOptions: {
                                disabled: true
                            },
                        },
                        {
                            dataField: "FechaAplicacion",
                            caption: "Fecha de aplicación",
                            alignment: "center",
                            editorType: "dxDateBox",
                            format: "dd/MM/yyyy",
                            dataType: "date",
                            editorOptions: {
                                disabled: false
                            },
                            validationRules: [{
                                type: 'required',
                                message: 'Campo obligatorio'
                            }]
                        },
                        {
                            dataField: "Lote",
                            caption: "No. de lote",
                            alignment: "center",
                            editorOptions: {
                                disabled: false,
                            },
                            validationRules: [{
                                type: 'required',
                                message: 'Campo obligatorio'
                            }]
                        },
                        {
                            dataField: "Observaciones",
                            alignment: "center",
                            editorOptions: {
                                disabled: false
                            }
                        },
                        {
                            dataField: "Usuario",
                            caption: "Nombre del vacunador",
                            alignment: "center",
                            editorOptions: {
                                disabled: false
                            },
                            validationRules: [{
                                type: 'required',
                                message: 'Campo obligatorio'
                            }]
                        },
                    ]
                },
                //-----------------------------------
                //---------- GINECOLOGICOS ----------
                //-----------------------------------
                {
                    name: 'Ginecológicos',
                    icon: 'venus',
                    value: 9,
                    open: false,
                    data: 'AntGinecologicos',
                    onRowInserting: this.AgregarGinecologico,
                    onRowInserted: this.BusquedaAntecedentesGinecologicos,
                    onRowRemoving: this.EliminarAntecedenteGinecologico,
                    onRowRemoved: this.BusquedaAntecedentesGinecologicos,
                    allowDeleting: this.AllowDeletingAntecedentesGinecologicos,
                    columnas: [{
                            dataField: "FechaRegistro",
                            caption: "Fecha registro",
                            alignment: "center",
                            format: "dd/MM/yyyy",
                            dataType: "datetime",
                            formItem: {
                                visible: false
                            }
                        },
                        {
                            dataField: "Fecha",
                            caption: "Fecha gesta",
                            alignment: "center",
                            format: "dd/MM/yyyy",
                            dataType: "datetime",
                            editorType: "dxCalendar",
                            editorOptions: {
                                width: 300,
                                max: new Date()
                            },
                            formItem: {
                                isRequired: true,
                                label: {
                                    visible: false
                                },
                                width: 300,
                                colSpan: 1
                            }
                        },
                        {
                            dataField: "Descripcion",
                            caption: "Gesta",
                            alignment: "center",
                            formItem: {
                                visible: false
                            }
                        },
                        //Este campo es el que se muestra al momento de agregar un nuevo registro
                        {
                            dataField: "Tipo",
                            caption: "Gesta",
                            editorType: "dxRadioGroup",
                            alignment: "center",
                            visible: false,
                            editorOptions: {
                                dataSource: this.registroGinecologico,
                                displayExpr: "text",
                                displayValue: "text",
                                valueExpr: "tipo",
                                // placeholder: "Seleccione gesta",
                                layout: 'vertical',
                                name: "TipoGesta",
                            },
                            formItem: {
                                isRequired: true,
                                label: {
                                    location: 'top',
                                    text: 'Gesta'
                                }
                            }
                        },
                    ]
                }
            ],

            formularioGinecologico: {
                Menarquia: null,
                Menopausia: null,
                FUR: null,
                FURC: null,
                Gestando: false,
                FechaParto: null
            },
            formularioRespaldo: {},

            dataGridRefKey,
            DefaultDxGridConfiguration,
            antecedentes: {
                AntAlergicos: [],
                AntMedicamentos: [],
                AntMedicos: [],
                AntQuirurgicos: [],
                AntTraumaticos: [],
                AntFamiliares: [],
                AntHabitos: [],
                AntVacunas: [],
                AntGinecologicos: [],
                AntGestas: [],
                Abortos: 0,
                Partos: 0,
                Cesareas: 0,
                Gestas: 0,
                FUR: null, //Identificador de fecha última regla
                FURC: null, //Identificador de fecha última regla confiable
            },

            catalogoAlergias: [],
            codigosAlergiaCargados: [],
            codigosAlergiaAgregados: [],
            medicamentosCargados: [],
            medicamentosAgregados: [],
            antecedentesPersonalesCargados: [],
            antecedentesPersonalesAgregados: [],

            registroGinecologico: [{
                    text: 'Aborto',
                    tipo: 'A'
                },
                {
                    text: 'Parto',
                    tipo: 'P'
                },
                {
                    text: 'Cesárea',
                    tipo: 'C'
                },
            ],

            contShowButton: 0,
            submitButtonOptions: {
                text: 'Guardar',
                type: 'success',
                icon: 'save',
                useSubmitBehavior: false,
                onClick: () => {
                    this.ActualizarDatosGinecologicos()
                },
            },
            alergias: [],

            permisoEditar: false
        }
    },
    props: {
        Sexo: null,
        IdCita: null,
        IdCliente: null,
        IdPaciente: null,
        IdMedico: null,
        IdFicha: null,
        EdadMenarquia: null,
        EdadMenopausia: null,
        IdConsulta: null,
        FUR: null,
        TipoBusqueda: null,

        Limpiar: null
    },
    methods: {
        CargarAlergias() {
            this.axios.post('/app/v1_Historial_Clinico/CatalogoAlergias', {})
                .then(resp => {
                    this.alergias = resp.data.json
                })
        },
        Preparing(e) {
            //SelectBox para seleccionar el tipo de alergia
            if (e.editorName === 'dxSelectBox' && e.editorOptions.name === 'Alergias') {
                this.catalogoAlergias = []
                this.alergias.map((x) => {
                    if (this.codigosAlergiaCargados.includes(x.Codigo) === false) {
                        this.catalogoAlergias.push(x)
                    }
                })
                e.editorOptions.dataSource = this.catalogoAlergias
            }

            //SelectBox para seleccionar el tipo de gesta
            if (e.editorName === 'dxRadioGroup' && e.editorOptions.name === 'TipoGesta') {
                e.editorOptions.dataSource = this.registroGinecologico
            }
        },
        AllowDeleting(e) {
            // let fecha = new Date(e.row.data.FechaRegistro)
            if (e.row.data.FechaRegistro) {
                let val = e.row.data.FechaRegistro.split(' ');
                let fecha = val[0].split('/')
                const year = fecha[2];
                const month = fecha[1];
                const day = fecha[0];

                let fecha1 = [month, day, year].join('/');

                const year1 = new Date().toLocaleString('default', {
                    year: 'numeric'
                });
                const month1 = new Date().toLocaleString('default', {
                    month: '2-digit'
                });
                const day1 = new Date().toLocaleString('default', {
                    day: '2-digit'
                });

                let fecha2 = [month1, day1, year1].join('/');

                if (fecha1 === fecha2) {
                    return true
                }

                return false
            }
            return false
        },

        //--------------------------------------------------
        //-------------------- ALERGIAS --------------------
        //--------------------------------------------------
        BusquedaAntecedentesAlergicos() {
            this.axios.post('/app/v1_Historial_Clinico/BusquedaAntecedentesAlergicos', {
                    IdCliente: this.IdCliente
                })
                .then(resp => {
                    this.antecedentes.AntAlergicos = resp.data.json

                    this.codigosAlergiaCargados = []
                    resp.data.json.map((x) => {
                        this.codigosAlergiaCargados.push(x.CodigoDiagnostico)
                    })
                })
        },
        AgregarAlergia(e) {
            e.cancel = new Promise((resolve, reject) => {
                this.codigosAlergiaAgregados.push(e.data.Descripcion)
                this.axios.post('/app/v1_Historial_Clinico/InsertarAntecedentesAlergicos', {
                    IdCliente: this.IdCliente,
                    CodigoDiagnostico: e.data.Descripcion,
                    Observaciones: e.data.Observaciones
                }).then((resp) => {
                    resp.data.codigo == 0 ? resolve(false) : resolve(true)
                }).catch((err) => {
                    this.codigosAlergiaAgregados.pop()
                    reject(err.descripcion ? err.descripcion : err)
                })
            })
        },
        EliminarAlergia(e) {
            e.cancel = new Promise((resolve, reject) => {
                this.axios.post('/app/v1_Historial_Clinico/EliminarAntecedentesAlergicos', {
                    IdAlergia: e.data.IdAlergia
                }).then((resp) => {
                    resp.data.codigo == 0 ? resolve(false) : resolve(true)
                }).catch((err) => {
                    reject(err.descripcion ? err.descripcion : err)
                })
            })
        },
        AllowDeletingAlergias(e) {
            if (this.codigosAlergiaAgregados.includes(e.row.data.CodigoDiagnostico)) {
                return true
            }
            return false
        },

        //------------------------------------------------------
        //-------------------- MEDICAMENTOS --------------------
        //------------------------------------------------------
        BusquedaAntecedentesMedicamentos() {
            this.axios.post('/app/v1_Historial_Clinico/BusquedaAntecedentesMedicamentos', {
                    IdCliente: this.IdCliente
                })
                .then(resp => {
                    this.antecedentes.AntMedicamentos = resp.data.json

                    this.medicamentosCargados = []
                    resp.data.json.map((x) => {
                        this.medicamentosCargados.push(x.Descripcion.toUpperCase().replace(/\s/g, ''))
                    })
                })
        },
        AgregarMedicamento(e) {
            e.cancel = new Promise((resolve, reject) => {
                if (!this.medicamentosCargados.includes(e.data.Descripcion.toUpperCase().replace(/\s/g, ''))) {
                    this.medicamentosAgregados.push(e.data.Descripcion)
                    this.axios.post('/app/v1_Historial_Clinico/InsertarAntecedentesMedicamentos', {
                        IdCliente: this.IdCliente,
                        Descripcion: e.data.Descripcion
                    }).then((resp) => {
                        resp.data.codigo == 0 ? resolve(false) : resolve(true)
                    }).catch((err) => {
                        this.medicamentosAgregados.pop()
                        reject(err.descripcion ? err.descripcion : err)
                    })
                } else {
                    resolve(false)
                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'No se agregó el medicamento',
                        acceptText: 'Aceptar',
                        text: 'El medicamento ya se ha ingresado anteriormente.',
                        buttonCancel: 'border',
                        clientWidth: 100,
                        accept: () => {},
                    })
                }
            })
        },
        EliminarMedicamento(e) {
            e.cancel = new Promise((resolve, reject) => {
                this.axios.post('/app/v1_Historial_Clinico/EliminarAntecedentesMedicamentos', {
                    IdMedicamento: e.data.IdMedicamento
                }).then((resp) => {
                    resp.data.codigo == 0 ? resolve(false) : resolve(true)
                }).catch((err) => {
                    reject(err.descripcion ? err.descripcion : err)
                })
            })
        },
        AllowDeletingMedicamentos(e) {
            if (this.medicamentosAgregados.includes(e.row.data.Descripcion)) {
                return true
            }
            return false
        },

        //----------------------------------------------------
        //-------------------- PERSONALES --------------------
        //----------------------------------------------------
        BusquedaAntecedentesPersonales(carga) {
            this.axios.post('/app/v1_Historial_Clinico/BusquedaAntecedentesPersonales', {
                    IdCliente: this.IdCliente
                })
                .then(resp => {
                    this.antecedentes.AntMedicos = []
                    this.antecedentes.AntQuirurgicos = []
                    this.antecedentes.AntTraumaticos = []
                    this.antecedentes.AntHabitos = []
                    this.antecedentes.AntFamiliares = []

                    this.antecedentesPersonalesAgregados = []

                    resp.data.json.map(x => {
                        if (carga === 1) {
                            this.antecedentesPersonalesCargados.push(x.IdAntecedente)
                        } else if (carga === 2) {
                            this.antecedentesPersonalesAgregados.push(x.IdAntecedente)
                        }

                        //Antecedentes médicos
                        if (x.Tipo === 'M') {
                            this.antecedentes.AntMedicos.push(x)
                        }

                        //Antecedentes quirúrgicos
                        if (x.Tipo === 'Q') {
                            this.antecedentes.AntQuirurgicos.push(x)
                        }

                        //Antecedentes tramáticos
                        if (x.Tipo === 'T') {
                            this.antecedentes.AntTraumaticos.push(x)
                        }

                        //Antecedentes familiares
                        if (x.Tipo === 'F') {
                            this.antecedentes.AntFamiliares.push(x)
                        }

                        //Antecedentes hábitos
                        if (x.Tipo === 'H') {
                            this.antecedentes.AntHabitos.push(x)
                        }
                    })
                })
        },
        AgregarAntecedentePersonal(e) {
            e.cancel = new Promise((resolve, reject) => {
                this.axios.post('/app/v1_Historial_Clinico/InsertarAntecedentesPersonales', {
                    IdCliente: this.IdCliente,
                    Descripcion: e.data.Descripcion,
                    Tipo: e.data.Tipo,
                    Fecha: e.data.Fecha
                }).then((resp) => {
                    resp.data.codigo == 0 ? resolve(false) : resolve(true)
                }).catch((err) => {
                    this.medicamentosAgregados.pop()
                    reject(err.descripcion ? err.descripcion : err)
                })
            })
        },
        EliminarAntecedentePersonal(e) {
            e.cancel = new Promise((resolve, reject) => {
                this.axios.post('/app/v1_Historial_Clinico/EliminarAntecedentesPersonales', {
                    IdAntecedente: e.data.IdAntecedente
                }).then((resp) => {
                    resp.data.codigo == 0 ? resolve(false) : resolve(true)
                }).catch((err) => {
                    reject(err.descripcion ? err.descripcion : err)
                })
            })
        },
        AllowDeletingAntecedentesPersonales(e) {
            if (!this.antecedentesPersonalesCargados.includes(e.row.data.IdAntecedente)) {
                return true
            }
            return false
        },

        //-------------------------------------------------------
        //-------------------- GINECOLOGICOS --------------------
        //-------------------------------------------------------
        BusquedaAntecedentesGinecologicos() {
            this.axios.post('/app/v1_Historial_Clinico/BusquedaAntecedentesGinecologicos', {
                    IdCliente: this.IdCliente
                })
                .then(resp => {
                    this.antecedentes.AntGinecologicos = resp.data.json.filter(x => x.Tipo !== "E" && x.Tipo !== "F" && x.Tipo !== "G" && x.Tipo !== "N")
                    this.antecedentes.AntGestas = resp.data.json.filter(x => x.Tipo === "G" || x.Tipo === "N")

                    this.antecedentes.Abortos = this.antecedentes.AntGinecologicos.filter(x => x.Tipo === "A").length
                    this.antecedentes.Partos = this.antecedentes.AntGinecologicos.filter(x => x.Tipo === "P").length
                    this.antecedentes.Cesareas = this.antecedentes.AntGinecologicos.filter(x => x.Tipo === "C").length
                    this.antecedentes.Gestas = this.antecedentes.Abortos + this.antecedentes.Partos + this.antecedentes.Cesareas
                    this.antecedentes.FURC = resp.data.json.filter(x => x.Tipo === "E")

                    let gestando = 'N'
                    if (this.antecedentes.AntGestas.length > 0) {
                        gestando = this.antecedentes.AntGestas[0].Tipo
                    }

                    this.formularioGinecologico = {
                        Menarquia: this.EdadMenarquia,
                        Menopausia: this.EdadMenopausia,
                        FUR: this.FUR,
                        FURC: this.antecedentes.FURC.length > 0 ? this.antecedentes.FURC[0].Fecha : null,
                        Gestando: gestando === 'G',
                        FechaParto: this.formularioGinecologico.FechaParto
                    }
                    this.formularioRespaldo = {
                        ...this.formularioGinecologico
                    }

                    //Se utiliza esta función para resetear el contador, ya que al asignarle los valores al formulario, el watch ya toma los cambios y le suma al contador
                    setTimeout(() => {
                        this.contShowButton = 0
                    }, 200);
                })
        },
        AgregarGinecologico(e) {
            e.cancel = new Promise((resolve, reject) => {
                this.axios.post('/app/v1_Historial_Clinico/InsertarAntecedentesGinecologicos', {
                    IdCliente: this.IdCliente,
                    Tipo: e.data.Tipo,
                    Fecha: e.data.Fecha
                }).then((resp) => {
                    resp.data.codigo == 0 ? resolve(false) : resolve(true)
                }).catch((err) => {
                    reject(err.descripcion ? err.descripcion : err)
                })

                // if (e.data.Gestando) {
                //     this.axios.post('/app/v1_Historial_Clinico/InsertarAntecedentesGinecologicos', {
                //         IdCliente: this.IdCliente,
                //         Tipo: 'G',
                //         Fecha: new Date()
                //     }).then((resp) => {
                //         resp.data.codigo == 0 ? resolve(false) : resolve(true)
                //     }).catch((err) => {
                //         reject(err.descripcion ? err.descripcion : err)
                //     })
                // }
            })
        },
        EliminarAntecedenteGinecologico(e) {
            e.cancel = new Promise((resolve, reject) => {
                this.axios.post('/app/v1_Historial_Clinico/EliminarAntecedentesGinecologicos', {
                    IdAntecedente: e.data.IdGinecologico
                }).then((resp) => {
                    resp.data.codigo == 0 ? resolve(false) : resolve(true)
                }).catch((err) => {
                    reject(err.descripcion ? err.descripcion : err)
                })
            })
        },
        ActualizarDatosGinecologicos() {
            this.axios.post('/app/v1_Historial_Clinico/ActualizarDatosGinecologicos', {
                IdFicha: this.IdFicha,
                EdadMenarquia: this.formularioGinecologico.Menarquia,
                EdadMenopausia: this.formularioGinecologico.Menopausia,
                IdGinecologico: this.antecedentes.FURC[0].IdGinecologico ? this.antecedentes.FURC[0].IdGinecologico : null,
                FURC: this.formularioGinecologico.FURC,
                IdCliente: this.IdCliente,
                Tipo: 'E',
                IdConsulta: this.IdConsulta,
                FUR: this.formularioGinecologico.FUR
            }).then(() => {
                if (this.formularioRespaldo.Gestando !== this.formularioGinecologico.Gestando) {
                    let tipo = this.formularioGinecologico.Gestando ? 'G' : 'N'
                    this.axios.post('/app/v1_Historial_Clinico/InsertarAntecedentesGinecologicos', {
                        IdCliente: this.IdCliente,
                        Tipo: tipo,
                        Fecha: new Date()
                    }).then(() => {})
                }
                this.$emit('actualizarDatosGinecologicos', this.formularioGinecologico)
                setTimeout(() => {
                    this.BusquedaAntecedentesGinecologicos()
                }, 500);
            })
        },
        CalcularFechaAproximadaParto(e) {
            if (e.value) {
                let fecha = null
                if (this.formularioGinecologico.FURC !== null && this.formularioGinecologico.FURC !== undefined) {
                    fecha = new Date(this.formularioGinecologico.FURC)
                } else if (this.FUR !== null && this.FUR !== undefined) {
                    fecha = new Date(this.FUR)
                }
                fecha.setHours(0, 0, 0, 0)
                // Suma 266 días, por ser 38 semanas
                // Total de milisegundos = días * horas/día * minutos/hora * segundos/minuto * milisegundos/segundo
                let dia = fecha.getTime() + (266 * 24 * 60 * 60 * 1000)

                fecha.setTime(dia)
                this.formularioGinecologico.FechaParto = fecha
            }

        },

        //-------------------------------------------------
        //-------------------- VACUNAS --------------------
        //-------------------------------------------------
        BusquedaAntecedentesVacunas() {
            this.axios.post('/app/v1_Historial_Clinico/BusquedaAntecedentesVacunas', {
                    IdCliente: this.IdCliente
                })
                .then(resp => {
                    this.antecedentes.AntVacunas = resp.data.json
                })
        },
        AgregarAntecedenteVacuna(e) {
            e.cancel = new Promise((resolve, reject) => {
                this.axios.post('/app/v1_Historial_Clinico/InsertarAntecedentesVacuna', {
                    IdCliente: this.IdCliente,
                    IdCatalogo: e.oldData.Id,
                    FechaAplicacion: e.newData.FechaAplicacion,
                    Lote: e.newData.Lote,
                    Observaciones: e.newData.Observaciones,
                    Usuario: e.newData.Usuario,
                    IdHistorial: e.oldData.IdVacunacion
                }).then((resp) => {
                    resp.data.codigo == 0 ? resolve(false) : resolve(true)
                }).catch((err) => {
                    reject(err.descripcion ? err.descripcion : err)
                })
            })
        },
        editingStart(e) {
            if (e.column.name !== "Protege" && e.column.name !== "Edad") {
                if (e.data.Activo === "1") {
                    e.column.editorOptions.disabled = true
                } else if (e.data.Activo === "0") {
                    e.column.editorOptions.disabled = false
                }
            }
        },
        onCellPreparedVacunas(e) {
            if (e.rowType === 'data' && e.column.dataField === 'Edad') {
                if (e.data.Id < 3) { // Recien nacido
                    e.cellElement.style.cssText = "background-color: #BDB76B; text-align: center; font-weight: bold; font-size: 16px;";
                } else if (e.data.Id >= 3 && e.data.Id < 6) // 2 meses
                {
                    e.cellElement.style.cssText = "background-color: #B8860B; text-align: center; font-weight: bold; font-size: 16px;";
                } else if (e.data.Id >= 6 && e.data.Id < 9) // 4 meses
                {
                    e.cellElement.style.cssText = "background-color: #A9A9A9; text-align: center; font-weight: bold; font-size: 16px;";
                } else if (e.data.Id >= 9 && e.data.Id < 12) // 6 meses
                {
                    e.cellElement.style.cssText = "background-color: #FF8C00; text-align: center; font-weight: bold; font-size: 16px;";
                } else if (e.data.Id >= 12) // 1 año
                {
                    e.cellElement.style.cssText = "background-color: #E9967A; text-align: center; font-weight: bold; font-size: 16px;";
                }
                // if (e.data.FechaAplicacion !== '') {

                // }
            }
        },

        AllowDeletingAntecedentesGinecologicos(e) {
            if (e.row.data.Tipo === 'E' || this.TipoBusqueda) {
                return false
            }
            return true
        },

        LimpiarVariables() {
            this.antecedentes = {
                AntAlergicos: [],
                AntMedicamentos: [],
                AntMedicos: [],
                AntQuirurgicos: [],
                AntTraumaticos: [],
                AntFamiliares: [],
                AntHabitos: [],
                AntVacunas: [],
                AntGinecologicos: [],
                Abortos: 0,
                Partos: 0,
                Cesareas: 0,
                Gestas: 0
            }

            this.codigosAlergiaAgregados = []
            this.codigosAlergiaCargados = []

            this.medicamentosCargados = []
            this.medicamentosAgregados = []

            this.antecedentesPersonalesCargados = []
            this.antecedentesPersonalesAgregados = []

            this.$refs.collapse.closeAllItems()
        },
    },
    mounted() {
        this.CargarAlergias()

        this.$validar_funcionalidad('/HISTORIALCLINICO/HC002', 'EDITAR', (d) => {
            this.permisoEditar = d.status
        })
    },
    computed: {
        dataGrid: function () {
            return this.$refs[dataGridRefKey].instance
        },
        colCountByScreen() {
            return this.calculateColCountAutomatically ?
                null : {
                    xs: 1,
                    sm: 2,
                    md: 2,
                    lg: 2
                };
        },
        colCountByScreen1() {
            return this.calculateColCountAutomatically ?
                null : {
                    xs: 2,
                    sm: 2,
                    md: 2,
                    lg: 2
                };
        },

    },
    watch: {
        'IdCliente'(newval) {
            if (newval !== '' && newval !== undefined && newval !== null) {
                this.BusquedaAntecedentesAlergicos()
                this.BusquedaAntecedentesMedicamentos()
                this.BusquedaAntecedentesPersonales(1)
                this.BusquedaAntecedentesVacunas()
                if (this.Sexo === 'F') {
                    this.BusquedaAntecedentesGinecologicos()
                }
            }
        },
        'Limpiar'(newval) {
            if (newval === true) {
                this.LimpiarVariables()
            }
        },
        formularioGinecologico: {
            deep: true,
            handler() {
                this.contShowButton = this.contShowButton + 1
            }
        }
    }
}
</script>

<style scoped>
.collapse-color {
    color: #2980B9 !important;
}

.i-size {
    font-size: 25px;
    padding-bottom: 5px;
}
</style>

<style>
::ng-deep .dx-datagrid-form-buttons-container .dx-widget.dx-button {
    background-color: green;
}

::ng-deep .dx-datagrid-form-buttons-container .dx-widget.dx-button:last-child {
    background-color: red;
}

.vs-collapse-item.open-item .vs-collapse-item--content {
    max-height: 10000px !important;
}
</style>
