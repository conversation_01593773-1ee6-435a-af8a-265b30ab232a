<template>
<div class="flex flex-wrap">
    <InfoP ref="componentDatosAdicionales" :visible="mostrarCliente" :titulo="'Datos adicionales del paciente'" :Datos="DatosAdicionales" :mostrarDoc="false" :mostrarBitacora="false" tabColor="rgb(201, 32, 178)"> </InfoP>
    <vs-popup :active.sync="popupBusquedaCitas" :title="busquedaPaciente ? 'Búsqueda de pacientes' : 'Búsqueda de citas'">
        <BusquedaCitas class="historial-container" v-bind="$props" :Citas="citas" :TipoBusqueda="busquedaPaciente" ref="busquedaPop" @cita="CargarCita" />
    </vs-popup>
    <vs-popup :active.sync="popupDatosAdicionales" title="Editar datos adicionales">
        <DatosAdicionales v-bind="$props" ref="datosAdicionalesPop" @datosAdicionales="AsignarDatosAdicionales" />
    </vs-popup>
    <div class="flex-none w-fit p-1">
        <vx-input-group class="w-80">
            <vs-tooltip :text="busquedaPaciente ? 'Ingrese nombre del paciente a consultar' : 'Ingrese el nombre del médico para consultar las citas confirmadas'">
                <vs-input class="w-full" size="small" v-model="nombreMedico" name="Medico" :label-placeholder="busquedaPaciente ? 'Nombre del paciente' : 'Nombre del médico'" @change="CargarBusqueda()" />
            </vs-tooltip>
            <template slot="append">
                <div class="append-text btn-addon pt-5">

                    <vs-tooltip v-if="mostrarCliente" text="Ver datos adicionales del paciente" class="vs-button vs-button-warning vs-button-filled includeIcon includeIconOnly small">
                        <vs-button size="small" color="warning" icon-pack="fas" @click="MostrarDatosAdmision()" icon="fa-info-circle"></vs-button>
                    </vs-tooltip>

                    <vs-tooltip v-if="mostrarCliente && !Encabezado.TipoBusqueda" text="Editar datos adicionales" class="vs-button vs-button-danger vs-button-filled includeIcon includeIconOnly small">
                        <vs-button size="small" color="danger" icon-pack="feather" icon="icon-edit" @click="() => { popupDatosAdicionales = true}"></vs-button>
                    </vs-tooltip>
                    <vs-tooltip :text="busquedaPaciente ? 'Buscar pacientes' : 'Buscar citas'" class="vs-button vs-button-filled includeIcon includeIconOnly small">
                        <vs-button size="small" color="primary" icon-pack="feather" @click="CargarBusqueda()" icon="icon-search"></vs-button>
                    </vs-tooltip>
                </div>
            </template>
        </vx-input-group>
        <vs-checkbox class="pt-4" v-model="busquedaPaciente" style="width: 160px !important">Buscar por paciente</vs-checkbox>
        <!-- <vs-select class="select-small w-full" label="Hospital" v-model="hospitalSeleccionado">
            <vs-select-item :key="index" :value="item.Codigo" :text="item.Nombre" v-for="item,index in hospitales" />
        </vs-select> -->
    </div>
    <!-- <div class="flex-none w-fit p-1">
        <Transition v-on:before-enter="beforeEnter" v-on:enter="enter" v-on:leave="leave" v-bind:css="false">
            <div v-if="mostrarCliente">
                <span class="font-bold">{{ Encabezado.Nombre + ' ' + Encabezado.Apellido}} </span>
            </div>
        </Transition>

    </div> -->

    <transition name="slide-up">
        <div v-if="mostrarCliente" class="w-52 p-1 info-enc">
            <table>
                <tr>
                    <td class="font-semibold">Paciente:</td>
                    <td>{{ Encabezado.NombreCompleto}}</td>
                </tr>
                <tr v-if="!Encabezado.TipoBusqueda">
                    <td class="font-semibold">Médico:</td>
                    <td>{{ Encabezado.NombreMedico }}</td>
                </tr>
            </table>
        </div>
    </transition>
    <transition name="slide-up">
        <div v-if="mostrarCliente" class="w-52 p-1 info-enc">
            <table>
                <tr>
                    <td class="font-semibold">Edad:</td>
                    <td>{{ Encabezado.EdadEspecifica }}</td>
                </tr>
                <tr>
                    <td class="font-semibold">Sexo:</td>
                    <td>{{ Encabezado.Sexo === 'F' ? 'Femenino' : 'Masculino' }}</td>
                </tr>
                <tr>
                    <td colspan="1" class="font-semibold">Nacimiento:</td>
                    <td colspan="1">{{ this.$formato_fecha(Encabezado.FechaNacimiento, 'dd/MM/yyyy') }}</td>
                </tr>
            </table>
        </div>
    </transition>
    <!-- <transition name="slide-fade">
        <div v-if="mostrarCliente" class="w-52 p-1 info-enc">
            <table>
                <tr>
                    <td class="font-semibold">Peso:</td>
                    <td> <span style="color: #ed1b24" v-if="Encabezado.Peso==''">0</span> {{ Encabezado.Peso + ' kg'}} </td>

                    <td class="font-semibold">Estatura:</td>
                    <td> <span style="color: #ed1b24" v-if="Encabezado.Estatura==''">0</span> {{ Encabezado.Estatura + ' cm'}} </td>
                </tr>

                <tr>
                    <td colspan="1" class="font-semibold">IMC:</td>
                    <td> {{ this.$formato_decimal(Encabezado.IMC, 2) }} </td>
                </tr>
                <tr>
                    <td colspan="2">
                        {{ Encabezado.DescripcionIMC }}
                    </td>
                </tr>
            </table>
        </div>
    </transition> -->
    <transition name="slide-up">
        <div v-if="mostrarCliente" class="w-56 p-1 info-enc">
            <table>
                <tr>
                    <td class="font-semibold">Código de cliente:</td>
                    <td>{{ Encabezado.IdCliente }} </td>
                </tr>
                <tr>
                    <td class="font-semibold">Código de paciente:</td>
                    <td>{{ Encabezado.IdPaciente }} </td>
                </tr>
                <!-- <tr>
                    <td class="font-semibold">Fecha Egreso:</td>
                    <td>{{ this.$formato_fecha(Encabezado.FechaEgreso) }}</td>
                </tr> -->
            </table>
        </div>
    </transition>
    <transition name="slide-up">
        <div v-if="mostrarCliente && !Encabezado.TipoBusqueda" :class="'w-auto p-1 info-enc '">
            <table>
                <tr>
                    <td class="font-semibold">Seguro:</td>
                    <td>{{ Encabezado.TipoAfiliacion}}</td>
                </tr>
                <!-- <tr>
                    <td class="font-semibold">Tipo Seguro:</td>
                    <td>{{ Encabezado.TipoSeguro }} </td>
                </tr>
                <tr>
                    <td class="font-semibold">Visitas previas:</td>
                    <td>{{ Encabezado.VisitasPrevias}}</td>
                </tr> -->
            </table>
        </div>
    </transition>
</div>
</template>

<script>
import '../../../assets/utils/velocity.min.js'

import BusquedaCitas from './BusquedaCitas.vue'
import DatosAdicionales from './DatosAdicionales.vue'

export default {
    name: 'EncabezadoExpediente',
    data() {
        return {
            No_Admision: '',
            mostrarCliente: false,
            Encabezado: this.LimpiarEncabezado(),
            show: false,
            popupBusquedaCitas: false,
            popupDatosAdicionales: false,

            nombreMedico: '',
            hospitales: [],
            hospitalSeleccionado: null,
            citas: [],
            citaSeleccionada: null,

            busquedaPaciente: false //Variable para determinar si se hará búsqueda de citas o de pacientes
        }
    },
    components: {
        BusquedaCitas,
        DatosAdicionales,
        InfoP: () => import("../../../components/sermesa/global/SMInfo.vue"),
    },
    props: {
        Sexo: null,
        IdCita: null,
        IdCliente: null,
        IdPaciente: null,
        IdConsulta: null,
        Correo: null,
        RHPaciente: null,
        NombreConyugue: null,
        RHConyugue: null
    },
    methods: {
        CargarHospitales() {
            this.axios.post('/app/v1_Historial_Clinico/BusquedaHospitales', {})
                .then(resp => {
                    this.hospitales = resp.data.json
                })
        },
        CargarBusqueda() {
            this.mostrarCliente = false
            this.Encabezado = this.LimpiarEncabezado()
            this.$emit('onLoadedData', this.Encabezado)
            this.$emit('limpiarVariables', true)
            this.citas = [] //Esto cargara el listado de los pacientes o de las citas

            if (!this.busquedaPaciente) {
                this.axios.post("/app/v1_Historial_Clinico/BusquedaCitasMedicos", {
                        NombreMedico: this.nombreMedico,
                        IdHospital: this.sesion.sesion_sucursal
                    })
                    .then(resp => {
                        if (resp.data.json.length > 0) {
                            this.popupBusquedaCitas = true
                            this.citas = resp.data.json
                        } else {
                            if (this.nombreMedico !== null && this.nombreMedico !== '') {
                                this.$vs.dialog({
                                    type: 'alert',
                                    color: '#ed8c72',
                                    title: 'Médico sin citas',
                                    acceptText: 'Aceptar',
                                    text: 'El médico no cuenta con citas programadas para el día de hoy.',
                                    buttonCancel: 'border',
                                    clientWidth: 100,
                                    accept: () => {},
                                })
                            } else {
                                this.$vs.dialog({
                                    type: 'alert',
                                    color: '#ed8c72',
                                    title: 'No hay citas programadas',
                                    acceptText: 'Aceptar',
                                    text: 'No hay citas programadas en el hospital.',
                                    buttonCancel: 'border',
                                    clientWidth: 100,
                                    accept: () => {},
                                })
                            }
                        }
                    })
            } else {
                if (this.nombreMedico !== null && this.nombreMedico !== '') {
                    this.axios.post("/app/v1_Historial_Clinico/BusquedaPacientes", {
                            NombrePaciente: this.nombreMedico
                        })
                        .then(resp => {
                            if (resp.data.json.length > 0) {
                                this.popupBusquedaCitas = true
                                this.citas = resp.data.json
                            } else {
                                if (this.nombreMedico !== null && this.nombreMedico !== '') {
                                    this.$vs.dialog({
                                        type: 'alert',
                                        color: '#ed8c72',
                                        title: 'Paciente no encontrado',
                                        acceptText: 'Aceptar',
                                        text: 'No existen coincidencias con el texto.',
                                        buttonCancel: 'border',
                                        clientWidth: 100,
                                        accept: () => {},
                                    })
                                }
                            }
                        })
                } else {
                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'Nombre paciente',
                        acceptText: 'Aceptar',
                        text: 'Debe ingresar el nombre del paciente para realizar la búsqueda.',
                        buttonCancel: 'border',
                        clientWidth: 100,
                        accept: () => {},
                    })
                }
            }
        },
        LimpiarEncabezado() {
            return {
                FechaConfirma: "",
                FechaCita: "",
                FechaModificacion: "",
                FechaConfirmacion: "",
                IdCita: "",
                IdMedico: "",
                Edad: "",
                EdadEspecifica: "",
                IdPaciente: "",
                FechaNacimiento: "",
                IdEmpresa: "",
                IdCliente: "",
                Nombre: "",
                Apellido: "",
                NombreCompleto: "",
                PathFoto: "",
                Especialidad: "",
                NombreMedico: "",
                CodigoMedico: "",
                Correo: "",
                TipoAfiliacion: "",
                Sexo: "",
                IdFicha: "",
                NombreConyugue: "",
                RHPaciente: "",
                RHConyugue: "",
                EdadMenarquia: "",
                EdadMenopausia: "",
                SexoFeto: "",
            }
        },
        CargarCita(e) {
            this.popupBusquedaCitas = false
            this.popupDatosAdicionales = false
            this.Encabezado = e
            if (!this.Encabezado.TipoBusqueda) {
                this.Encabezado.IdCita = parseInt(this.Encabezado.IdCita)
                this.Encabezado.EdadMenarquia = parseInt(this.Encabezado.EdadMenarquia)
                this.Encabezado.EdadMenopausia = parseInt(this.Encabezado.EdadMenopausia)
            }

            this.mostrarCliente = true;
            this.$emit('onLoadedData', this.Encabezado)
        },
        LimpiarBusqueda() {
            this.mostrarCliente = false
            this.No_Admision = ''
            this.Encabezado = this.LimpiarEncabezado()
            this.$emit('onLoadedData', this.Encabezado)
        },
        MostrarDatosAdmision() {
            this.$refs.componentDatosAdicionales.mostrar = !this.$refs.componentDatosAdicionales.mostrar
        },
        EditarDatosAdicionales() {
            this.popupDatosAdicionales = true
        },
        //Este método actualiza los datos adicionales del paciente cuando el componente DatosAdicionales reciba una respuesta exitosa al actualizarlos en base de datos
        AsignarDatosAdicionales(e) {
            this.Encabezado.Correo = e.Correo
            this.Encabezado.RHPaciente = e.RHPaciente
            this.Encabezado.NombreConyugue = e.NombreConyugue
            this.Encabezado.RHConyugue = e.RHConyugue
            this.$emit('onLoadedData', this.Encabezado)
        },
        ///Transiciones
        /*eslint-disable */
        beforeEnter: function (el) {
            el.style.opacity = 0
            el.style.transformOrigin = 'left'
        },
        enter: function (el, done) {
            Velocity(el, {
                opacity: 1,
                fontSize: '1.4em'
            }, {
                duration: 300
            })
            Velocity(el, {
                fontSize: '1em'
            }, {
                complete: done
            })
        },
        leave: function (el, done) {
            Velocity(el, {
                translateX: '15px',
                rotateZ: '50deg'
            }, {
                duration: 600
            })
            Velocity(el, {
                rotateZ: '100deg'
            }, {
                loop: 2
            })
            Velocity(el, {
                rotateZ: '45deg',
                translateY: '30px',
                translateX: '30px',
                opacity: 0
            }, {
                complete: done
            })
        }

        /*eslint-enable */
    },
    mounted() {
        // this.CargarHospitales()
    },
    computed: {
        DatosAdicionales() {
            if (this.Encabezado) {
                let result = {
                    'Correo electrónico': this.Encabezado.Correo,
                    'Grupo sanguíneo': this.Encabezado.RHPaciente,
                    'Nombre del cónyugue': this.Encabezado.NombreConyugue,
                    'Grupo sanguíneo cónyugue': this.Encabezado.RHConyugue
                }

                return [{
                    label: 'Datos del paciente',
                    contenido: result,
                    forma: 'i'
                }, ]
            }
            return []
        },

        sesion() {
            return this.$store.state.sesion
        },
    },
}
</script>

<style scoped>
.info-enc {
    border: 1px solid #ccc !important;
    border-radius: 5px;
    margin: 1px;
    background-color: #f8f8f8;
    height: 95px !important;
}

.info-enc-height {
    height: 93px !important;
}
</style><style>
/*sobrescribiendo los estilos se SM-INFO*/
.info-contenido {
    width: 400px !important;
}

.info i {
    font-size: 14px;
}

.salud-siempre {
    background-color: #ffa500 !important;
}

.privado {
    background-color: #c0dcc0 !important;
}

.seguro {
    background-color: #ffff00 !important;
}

.vacio {
    background-color: #fffdd0 !important;
}

/**transitions */
.fade-enter-active,
.fade-leave-active {
    transition: opacity .9s;
}

.fade-enter,
.fade-leave-to

/* .fade-leave-active below version 2.1.8 */
    {
    opacity: 0;
}

.slide-fade-enter-active {
    transition: all .3s ease;
}

.slide-fade-leave-active {
    transition: all .8s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.slide-fade-enter,
.slide-fade-leave-to

/* .slide-fade-leave-active below version 2.1.8 */
    {
    transform: translateX(10px);
    opacity: 0;
}

.bounce-enter-active {
    animation: bounce-in .5s;
}

.bounce-leave-active {
    animation: bounce-in .5s reverse;
}

@keyframes bounce-in {
    0% {
        transform: scale(0);
    }

    50% {
        transform: scale(1.5);
    }

    100% {
        transform: scale(1);
    }
}

.select-small input {
    padding: 0.4rem !important;
    font-size: 0.8rem !important;
    height: 28px !important;
}

.btn-container {
    display: inline-block;
    position: relative;
    height: 1em;
}

button {
    position: absolute;
}

.slide-up-enter-active,
.slide-up-leave-active {
    transition: all 0.5s ease-out;
}

.slide-up-enter-from {
    opacity: 0;
    transform: translateY(30px);
}

.slide-up-leave-to {
    opacity: 0;
    transform: translateY(30px);
}
</style>
