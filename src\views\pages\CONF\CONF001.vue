<template>
<div id="containter">

    <vs-popup title="Administrar Carpetas" :active.sync="carpetas_mostrar">
        <tree ref="adminCarpetas" :titulo="aplicativos_select.Nombre" :id-aplicativo="Number(aplicativos_select.IdAplicativo)" @carpetaSeleccionada="(e)=>{aplicativos_funcionalidades_editar.IdSub = e.IdSub}" @loadedData="(e)=>{LoadCarpetas(e)}" :allowDelete = "HabBorrarCarpeta" @deletedData="(e)=>DeletedFolder(e)"/>
    </vs-popup>

    <!-- NUEVO/EDITAR APLICATIVO -->
    <!-- ****************************************************************************************************************************************************************** -->
    <vs-popup classContent="popup-example" :title="((aplicativos_editar.is_new)?'Nuevo':aplicativos_editar.Nombre+' / Editar')" :active.sync="aplicativos_editar.mostrar">
        <div class="flex flex-wrap">
            <div class="w-full sm:w-1/2" style="margin-bottom:15px">
                <label>Nombre</label>
                <vs-input class="w-full" v-model="aplicativos_editar.Nombre" />
            </div>
            <div class="w-full " style="margin-bottom:15px">
                <label>Descripción</label>
                <vs-input class="w-full" v-model="aplicativos_editar.Descripcion" />
            </div>

            <div class="w-full sm:w-1/3" style="margin-bottom:15px">
                <label>Prefijo</label>
                <vs-input class="w-full" v-model="aplicativos_editar.Prefijo" />
            </div>

            <div class="w-full sm:w-1/3" style="margin-bottom:15px">
                <label>Controlador (Carpeta)</label>
                <vs-input class="w-full" v-model="aplicativos_editar.Controlador" />
            </div>

            <div class="w-full sm:w-1/3" style="margin-bottom:15px">
                <label>Icono</label>
                <vs-input class="w-full" v-model="aplicativos_editar.Icono" />
            </div>

            <div class="w-full sm:w-1/2" style="margin-bottom:15px">
                <label>Activo</label>
                <vs-switch v-model="aplicativos_editar.Activo" />
            </div>

        </div>

        <vs-button color="primary" style="float:right;margin-bottom:12px" type="filled" v-on:click="editar_aplicativo()">Guardar</vs-button>
    </vs-popup>

    <!-- NUEVO/EDITAR FUNCIONALIDADES -->
    <!-- ****************************************************************************************************************************************************************** -->
    <vs-popup classContent="popup-example" :title="aplicativos_select.Nombre + ' / '+((aplicativos_funcionalidades_editar.IdFuncionalidad==0)?'Nuevo':'Editar')" :active.sync="aplicativos_funcionalidades_editar.mostrar">
        <div id="func-container" style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
            <div class="flex flex-wrap">
                <div class="w-full sm:w-1/2 mb-4 sm:pr-2">
                    <label>Codigo</label>
                    <vx-input-group>
                        <vs-input type="number" class="w-full" v-model="aplicativos_funcionalidades_editar.Codigo_Numero" />
                        <template slot="prepend">
                            <div class="prepend-text btn-addon">
                                <vs-input v-model="aplicativos_funcionalidades_editar.Prefijo" style="width:80px;color:black" disabled />
                            </div>
                        </template>
                    </vx-input-group>
                </div>
                <div class="w-full pl-0 sm:w-1/2 sm:pl-2 mb4" >
                    <label>Orden</label>
                    <vx-input-group>
                        <vs-input type="number" :icon-after="true" icon="swap_vert" class="w-full" v-model="aplicativos_funcionalidades_editar.Orden" />
                    </vx-input-group>
                </div>
                <div class="w-full mb-4">
                    <vs-input label="Nombre" class="w-full" v-model="aplicativos_funcionalidades_editar.Nombre" />
                </div>
                <div class="w-full mb-4">
                    <vs-input label="Descripción" class="w-full" v-model="aplicativos_funcionalidades_editar.Descripcion" />
                </div>
                <div class="w-full mb-4" >
                    <label class="vs-input--label">Carpeta asignada</label>
                    <vx-input-group>
                        <vs-select class=" w-full" name="item-category" v-model="aplicativos_funcionalidades_editar.IdSub" required>
                            <vs-select-item :key="key" :value="item.IdSub" :text="item.Nombre" v-for="(item,key) in subCarpetas" />
                        </vs-select>
                        <template slot="append">
                            <div class="append-text btn-addon">
                                <vs-button icon-pack="far" icon="fa-folder-open" @click="()=> {HabBorrarCarpeta = false; ManejarCarpetas(aplicativos_select)}"></vs-button>
                            </div>
                        </template>
                    </vx-input-group>
                </div>
                <div class="w-full mb-4" >
                    <SM-Buscar v-model="aplicativos_funcionalidades_editar.IdFunPadre" label="Componente Padre" :api="lista_funcionalidades"
                    :api_preload="true"
                    :api_campos="['Nombre', 'Codigo', 'Nombre_Funcionalidad', 'Url']" 
                    :api_titulos="['Aplicativo', 'Código', 'Funacionalidad', 'URL']"
                    api_campo_respuesta = "IdFuncionalidad"
                    api_campo_respuesta_mostrar = "Url"
                    :disabled_texto = "true"
                    :callback_cancelar="()=>{}" 
                    :callback_buscar="()=>{}"
                    />
                </div>
                <div class="w-full mb-4 md:w-1/2 md:pr-2">
                    <vs-input label="URL" class="w-full" v-model="aplicativos_funcionalidades_editar.Url" disabled />
                </div>
                <div class="w-full mb-4 md:w-1/2 md:pl-2">
                    <vs-input label="Ruta" class="w-full" v-model="aplicativos_funcionalidades_editar.Ruta"/>
                </div>
                <div class="w-1/2 mb-4">
                    <label>Activo</label>
                    <vs-switch v-model="aplicativos_funcionalidades_editar.Activo" />
                </div>
                <div class="w-1/2 mb-4">
                    <label>Oculto</label>
                    <vs-switch v-model="aplicativos_funcionalidades_editar.Oculto" />
                </div>
            </div>
        </div>
        <vs-button style="float:right;margin-bottom:12px" v-on:click="guardar_funcionalidad()">Guardar</vs-button>
    </vs-popup>

    <!-- EDITAR PRIVILEGIOS -->
    <!-- ****************************************************************************************************************************************************************** -->
    <vs-popup classContent="popup-example" :title="aplicativos_select.Nombre" :active.sync="aplicativos_privilegios">
        <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
            <div class="flex flex-wrap">
                <div class="w-full sm:w-1/2" style="margin-bottom:15px">
                    <label>Nombre</label>
                    <h5>{{aplicativos_funcionalidades.Nombre_Funcionalidad}}</h5>
                </div>
                <div class="w-full sm:w-1/2" style="margin-bottom:15px">
                    <label>Descripción</label>
                    <h5>{{aplicativos_funcionalidades.Descripcion_Funcionalidad}}</h5>
                </div>

                <div class="w-full" style="margin-bottom:15px">
                    <label>URL</label>
                    <h5>{{aplicativos_funcionalidades.Url}}</h5>
                </div>

                <div class="w-full sm:w-1/2" style="margin-bottom:15px">
                    <label>Activo</label>
                    <h5><span style="font-size:20px" :class="aplicativos_funcionalidades.Activo_Funcionalidad==true?'far fa-check-square':'far fa-square'"></span></h5>
                </div>

                <div class="w-full sm:w-1/2" style="margin-bottom:15px">
                    <label>Oculto</label>
                    <h5><span style="font-size:20px" :class="aplicativos_funcionalidades.Oculto_Funcionalidad==true?'far fa-check-square':'far fa-square'"></span></h5>
                </div>
            </div>
        </div>

        <div class="flex flex-wrap">
            <div class="w-full sm:w-1/4" style="margin-bottom:15px">
                <vs-input label="Nombre" class="w-full" v-model="aplicativos_funcionalidades.nombre" @change="aplicativos_funcionalidades.nombre=aplicativos_funcionalidades.nombre.toUpperCase()" />
            </div>

            <div class="w-full sm:w-1/2" style="margin-bottom:15px">
                <vs-input label="Descripción" class="w-full" v-model="aplicativos_funcionalidades.descripcion" />
            </div>

            <div class="w-full sm:w-1/6" style="margin-bottom:15px">
                <vs-button  style="margin-top:20px" v-on:click="guardar_privilegios()">Guardar</vs-button>
            </div>
        </div>

        <vs-table v-if="aplicativos_funcionalidades._level && aplicativos_funcionalidades._level.length>0 && aplicativos_privilegios" max-items="5" pagination search :data="aplicativos_funcionalidades._level">
            <template slot="thead">
                <vs-th>Nombre</vs-th>
                <vs-th>Descripción</vs-th>
                <vs-th>Activo</vs-th>
                <vs-th></vs-th>
            </template>

            <template slot-scope="{data}">
                <vs-tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">
                    <vs-td :data="data[indextr].Nombre_Privilegio" width="250px">
                        {{ data[indextr].Nombre_Privilegio }}
                    </vs-td>

                    <vs-td :data="data[indextr].Descripcion_Privilegio">
                        {{ data[indextr].Descripcion_Privilegio }}
                    </vs-td>

                    <vs-td :data="data[indextr].Activo_Privilegio" width="80px">
                        <vs-switch v-model="data[indextr].Activo_Privilegio" v-on:click="guardar_privilegios_activo(data[indextr])" />
                    </vs-td>
                    <vs-td width="50px">
                        <vs-button color="danger" icon-pack="fas" icon="fa-trash-alt" v-on:click="eliminar_privilegios(data[indextr])"></vs-button>
                    </vs-td>
                </vs-tr>
            </template>
        </vs-table>
    </vs-popup>

    <!-- LISTADO DE FUNCIONALIDADES -->
    <!-- ****************************************************************************************************************************************************************** -->
    <vs-popup classContent="popup-example" :title="aplicativos_select.Nombre" :active.sync="aplicativos_mostrar" fullscreen>
        <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
            <div class="flex flex-wrap">
                <div class="w-full sm:w-1/2" style="margin-bottom:15px">
                    <label>Nombre</label>
                    <h5>{{aplicativos_select.Nombre}}</h5>
                </div>
                <div class="w-full sm:w-1/2" style="margin-bottom:15px">
                    <label>Descripción</label>
                    <h5>{{aplicativos_select.Descripcion}}</h5>
                </div>

                <div class="w-full sm:w-1/2" style="margin-bottom:15px">
                    <label>Prefijo</label>
                    <h5>{{aplicativos_select.Prefijo}}</h5>
                </div>

                <div class="w-full sm:w-1/2" style="margin-bottom:15px">
                    <label>Controlador (Carpeta)</label>
                    <h5>{{aplicativos_select.Controlador}}</h5>
                </div>

                <div class="w-full sm:w-1/2" style="margin-bottom:15px">
                    <label>Icono</label>
                    <h5><span :class="aplicativos_select.Icono" style="font-size:22px"></span></h5>
                </div>

                <div class="w-full sm:w-1/2" style="margin-bottom:15px">
                    <label>Activo</label>
                    <h5><span style="font-size:20px" :class="aplicativos_select.Activo==true?'far fa-check-square':'far fa-square'"></span></h5>
                </div>

            </div>
        </div>
        <vs-button color="primary" style="float:right;margin-bottom:12px" type="filled" v-on:click="nuevo_funcionalidad(aplicativos_select)">Nueva Funcionalidad</vs-button>
        <vs-divider />
        <vs-table2 v-if="aplicativos_select && aplicativos_select._level && aplicativos_select._level.length>0" max-items="5" pagination search :data="aplicativos_select._level" tooltip>
            <template slot="thead">
                <th width="100px">Codigo</th>
                <th>Nombre</th>
                <th width="150px">Carpeta</th>
                <!-- <th>Descripción</th> -->
                <th width="150px">Url</th>
                <th width="50px">Activo</th>
                <th width="50px">Oculto</th>
                <th width="70px">Acciones</th>
            </template>

            <template slot-scope="{data}">
                <tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">
                    <vs-td2>
                        {{ data[indextr].Codigo }}
                    </vs-td2>

                    <vs-td2>
                        {{ data[indextr].Nombre_Funcionalidad }}
                        <br>
                        <small>{{ data[indextr].Descripcion_Funcionalidad }}</small>
                    </vs-td2>

                    <vs-td2 noTooltip>
                        {{tr.Sub_Nombre}}
                    </vs-td2>

                    <vs-td2>
                        {{ data[indextr].Url }}
                    </vs-td2>

                    <vs-td2 noTooltip>
                        <span style="font-size:20px" :class="data[indextr].Activo_Funcionalidad==true?'far fa-check-square':'far fa-square'"></span>
                    </vs-td2>

                    <vs-td2 noTooltip>
                        <span style="font-size:20px" :class="data[indextr].Oculto_Funcionalidad==true?'far fa-check-square':'far fa-square'"></span>
                    </vs-td2>

                    <vs-td2 noTooltip width="120px">
                        <vx-tooltip text="Editar Aplicativo" style="display:inline-block;margin-right:2px">
                            <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" v-on:click="editar_funcionalidad(aplicativos_select,tr)"></vs-button>
                        </vx-tooltip>
                        <vx-tooltip text="Editar Aplicativo" style="display:inline-block;margin-right:2px;z-index:9999">
                            <vs-button color="primary" icon-pack="feather" icon="icon-lock" v-on:click="cargar_privilegios(data[indextr])"></vs-button>
                        </vx-tooltip>
                    </vs-td2>

                </tr>
            </template>
        </vs-table2>
    </vs-popup>

    <!-- ****************************************************************************************************************************************************************** -->
    <vx-card id="" title="Aplicativos y Funcionalidades">
        <div class="content content-pagex">
            <!-- {{aplicativos}} -->
            <div class="flex flex-wrap">
                <div class="w-full sm:w-1/3" style="margin-bottom:15px">
                    <label class="vs-input--label">Buscar</label>
                    <vx-input-group>
                        <vs-input v-model="filtro_funcionalidad" />
                        <template slot="append">
                            <div class="append-text btn-addon">
                                <button type="submit" v-show="false" name="button"></button>
                                <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="funcionalidades_filtrar()" icon="icon-search"></vs-button>
                            </div>
                        </template>
                    </vx-input-group>
                </div>
                <div class="w-full sm:w-1/3">
                </div>
                <div class="w-full sm:w-1/3">
                    <div style="float:right;padding-bottom:14px">
                        <br>
                        <vs-button color="primary" type="filled" v-on:click="nuevo_aplicativo()">Nueva Aplicativo</vs-button>
                    </div>
                </div>
            </div>

            <vs-table2 v-if="aplicativos_filtro.length>0" max-items="15" pagination search :data="aplicativos_filtro">
                <template slot="thead">
                    <!-- <vs-th>Id</vs-th> -->
                    <th width="45px">Icono</th>
                    <th width="200px">Nombre</th>
                    <!-- <th>Descripción</th> -->

                    <th width="80px">Prefijo</th>
                    <th width="80px">Controlador</th>
                    <th width="45px">Activo</th>
                    <th>Funcionalidades</th>
                    <th width="145px">Acciones</th>
                </template>

                <template slot-scope="{data}">
                    <tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">

                        <vs-td2>
                            <span style="font-size:25px" :class="data[indextr].Icono"></span>
                        </vs-td2>

                        <vs-td2>
                            {{ data[indextr].Nombre }}
                        </vs-td2>


                        <vs-td2>
                            {{ data[indextr].Prefijo }}
                        </vs-td2>

                        <vs-td2>
                            {{ data[indextr].Controlador }}
                        </vs-td2>

                        <vs-td2>
                            <span style="font-size:20px" :class="data[indextr].Activo==true?'far fa-check-square':'far fa-square'"></span>
                        </vs-td2>

                        <vs-td2>
                            {{ (data[indextr]._level.length==0)?'--':'' }}

                            <ul>
                                <li v-for="(data,index) in data[indextr]._level" :key="index">
                                    * {{data.Nombre_Funcionalidad}}
                                </li>
                            </ul>

                        </vs-td2>
                        <vs-td2>
                            <vx-tooltip text="Editar Aplicativo" style="display:inline-block;margin-right:2px">
                                <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" v-on:click="editarAplicativo(data[indextr])"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip text="Funcionalidades" style="display:inline-block;margin-right:2px">
                                <vs-button color="primary" icon-pack="far" icon="fa-window-restore" v-on:click="handleSelected(data[indextr],indextr)"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip text="Administrar Carpetas" style="display:inline-block;margin-right:2px">
                                <vs-button color="primary" icon-pack="far" icon="fa-folder-open" v-on:click="()=> {HabBorrarCarpeta = true; ManejarCarpetas(data[indextr],indextr)}"></vs-button>
                            </vx-tooltip>
                        </vs-td2>

                    </tr>
                </template>
            </vs-table2>
        </div>
    </vx-card>
</div>
</template>

<script>
import tree from './CONF006.vue'
export default {
    data() {
        return {
            aplicativos: [],
            subCarpetas: [],
            carpetas_mostrar: false,
            aplicativos_mostrar: false,
            aplicativos_privilegios: false,
            aplicativos_select: {},
            aplicativos_select_index: 0,
            aplicativos_funcionalidades: {},
            // filtro
            filtro_funcionalidad: '',
            //nuevos / editar
            temp_aplicativos: {},
            aplicativos_editar: {
                mostrar: false,
                IdAplicativo: 0,
                Nombre: '',
                Descripcion: '',
                Prefijo: '',
                Controlador: '',
                Icono: '',
                Activo: '',
                is_new: false,
            },
            aplicativos_funcionalidades_editar: {
                mostrar: false,
                IdAplicativo: 0,
                IdFuncionalidad: 0,
                Codigo_Numero: 0,
                Codigo: 0,
                Nombre: '',
                Descripcion: '',
                Url: '',
                Orden: 0,
                Oculto: false,
                Activo: true,
                Prefijo: '',
                Controlador: '',
                IdSub: null,
                mostrar_subcarpeta: false,
                nueva_subcarpeta: '',
                IdFunPadre: null,
                Ruta: null,
            },
            HabBorrarCarpeta: false,//variable para manejar la aliminacion de carpertas solo desde el listado de funcionalidades
        }
    },
    watch: {
        'aplicativos_funcionalidades_editar.Codigo_Numero': function(value) {
            this.aplicativos_funcionalidades_editar.Codigo = this.aplicativos_funcionalidades_editar.Prefijo + this.$global_lpad(value, 3)
            this.aplicativos_funcionalidades_editar.Url = '/' + this.aplicativos_funcionalidades_editar.Controlador + '/' + this.aplicativos_funcionalidades_editar.Prefijo + this.$global_lpad(value, 3)
        }
    },
    computed: {
        aplicativos_filtro() {
            if (this.filtro_funcionalidad != '') {
                let arr = this.aplicativos.filter(data =>
                    data.Nombre.toUpperCase().indexOf(this.filtro_funcionalidad.toUpperCase()) >= 0 ||
                    data._level.filter(data2 =>
                        data2.Codigo.indexOf(this.filtro_funcionalidad.toUpperCase()) >= 0 ||
                        data2.Nombre_Funcionalidad.toUpperCase().indexOf(this.filtro_funcionalidad.toUpperCase()) >= 0
                    ).length > 0)
                return arr
            } else {
                return this.aplicativos
            }
        },
        lista_funcionalidades(){
            let t = []
            this.aplicativos.map(app => {
                app._level.map(func =>{
                        t.push({...app, ...func})
                    }
                )
            })
            return t
        },
    },
    components: {
        tree
    },
    methods: {
        DeletedFolder(){
            this.cargar_aplicativos().then(()=>{
                //this.aplicativos_funcionalidades_editar.IdSub = (folder.IdSubPadre) ? folder.IdSubPadre : 0
            })
        },
        LoadCarpetas(data){
            this.subCarpetas = [{
                 IdSub: 0,
                 Nombre: '- Ninguno -'
            }]
            this.subCarpetas.push(...data)
        },

        cargar_aplicativos() {
            return new Promise(resolve => {
                this.axios.post('/app/administracion/obtener_aplicativos', {})
                    .then(resp => {
                        if (resp.data.codigo == 0) this.aplicativos = resp.data.json
                        resolve(true)
                    })
            })

        },

        cargar_privilegios(data) {
            this.aplicativos_privilegios = true
            this.aplicativos_funcionalidades = data
            this.aplicativos_funcionalidades = {
                ...this.aplicativos_funcionalidades,
                nombre: '',
                descripcion: '',
                activo: true
            }
        },

        nuevo_funcionalidad(aplicativos_select) {
            let CantidadFuncionalidades = aplicativos_select._level.length

            this.aplicativos_funcionalidades_editar.mostrar = true
            this.aplicativos_funcionalidades_editar.Prefijo = aplicativos_select.Prefijo
            this.aplicativos_funcionalidades_editar.Controlador = aplicativos_select.Controlador
            this.aplicativos_funcionalidades_editar.IdAplicativo = aplicativos_select.IdAplicativo
            this.aplicativos_funcionalidades_editar.IdFuncionalidad = 0
            this.aplicativos_funcionalidades_editar.Codigo_Numero = CantidadFuncionalidades + 1
            this.aplicativos_funcionalidades_editar.Codigo = aplicativos_select.Prefijo + this.$global_lpad(CantidadFuncionalidades + 1, 3)
            this.aplicativos_funcionalidades_editar.Nombre = ""
            this.aplicativos_funcionalidades_editar.Descripcion = ''
            this.aplicativos_funcionalidades_editar.Url = '/' + aplicativos_select.Controlador + '/' + aplicativos_select.Prefijo + this.$global_lpad(CantidadFuncionalidades + 1, 3)
            this.aplicativos_funcionalidades_editar.Orden = 0
            this.aplicativos_funcionalidades_editar.Oculto = false
            this.aplicativos_funcionalidades_editar.Activo = true
            this.aplicativos_funcionalidades_editar.IdSub = 0
            this.aplicativos_funcionalidades_editar.nueva_subcarpeta = ''
            this.aplicativos_funcionalidades_editar.IdFunPadre = null
            this.aplicativos_funcionalidades_editar.Ruta = null
        },

        editar_funcionalidad(aplicativos_select, funcionalidad) {
            this.temp_aplicativos = aplicativos_select
            this.aplicativos_funcionalidades_editar.mostrar = true
            this.aplicativos_funcionalidades_editar.Prefijo = aplicativos_select.Prefijo
            this.aplicativos_funcionalidades_editar.Controlador = aplicativos_select.Controlador
            this.aplicativos_funcionalidades_editar.IdAplicativo = aplicativos_select.IdAplicativo
            this.aplicativos_funcionalidades_editar.IdFuncionalidad = funcionalidad.IdFuncionalidad
            this.aplicativos_funcionalidades_editar.Codigo_Numero = parseInt(funcionalidad.Codigo.substring(funcionalidad.Codigo.length - 3))
            this.aplicativos_funcionalidades_editar.Codigo = funcionalidad.Codigo
            this.aplicativos_funcionalidades_editar.Nombre = funcionalidad.Nombre_Funcionalidad
            this.aplicativos_funcionalidades_editar.Descripcion = funcionalidad.Descripcion_Funcionalidad
            this.aplicativos_funcionalidades_editar.Url = funcionalidad.Url
            this.aplicativos_funcionalidades_editar.Orden = funcionalidad.Orden_Funcionalidad
            this.aplicativos_funcionalidades_editar.Oculto = (funcionalidad.Oculto_Funcionalidad) ? true : false
            this.aplicativos_funcionalidades_editar.Activo = (funcionalidad.Activo_Funcionalidad) ? true : false
            this.aplicativos_funcionalidades_editar.IdSub = (funcionalidad.IdSub) ? funcionalidad.IdSub : 0
            this.aplicativos_funcionalidades_editar.nueva_subcarpeta = ''
            this.aplicativos_funcionalidades_editar.IdFunPadre = funcionalidad.IdFunPadre
            this.aplicativos_funcionalidades_editar.Ruta = funcionalidad.Ruta
        },

        guardar_funcionalidad() {
            let info = this.aplicativos_funcionalidades_editar

            //VALIDACIONES
            if (!info.Codigo > 0) {
                this.$vs.notify({
                    title: 'Alerta',
                    text: 'Debe de ingresar un codigo valído',
                    color: 'danger'
                })
                return false
            }
            if (info.Descripcion.trim() == '') {
                this.$vs.notify({
                    title: 'Alerta',
                    text: 'Debe de ingresar una descripción',
                    color: 'danger'
                })
                return false
            }

            if (this.aplicativos_funcionalidades_editar.IdSub == 0) this.aplicativos_funcionalidades_editar.IdSub = null

            //GUARDAR INFORMACIÓN
            this.axios.post(
                    '/app/administracion/' +
                    ((this.aplicativos_funcionalidades_editar.IdFuncionalidad == 0) ? 'guardar_funcionalidad' : 'editar_funcionalidad'),
                    this.aplicativos_funcionalidades_editar)
                .then(() => {
                    if (this.aplicativos_funcionalidades_editar.IdFuncionalidad != 0) {
                        let arr = this.temp_aplicativos._level.filter(data => data.IdFuncionalidad == this.aplicativos_funcionalidades_editar.IdFuncionalidad)
                        arr[0].Codigo = this.aplicativos_funcionalidades_editar.Codigo
                        arr[0].Nombre_Funcionalidad = this.aplicativos_funcionalidades_editar.Nombre
                        arr[0].Descripcion_Funcionalidad = this.aplicativos_funcionalidades_editar.Descripcion
                        arr[0].Url = this.aplicativos_funcionalidades_editar.Url
                        arr[0].Activo_Funcionalidad = this.aplicativos_funcionalidades_editar.Activo
                        arr[0].Oculto_Funcionalidad = this.aplicativos_funcionalidades_editar.Oculto
                        arr[0].Orden_Funcionalidad = this.aplicativos_funcionalidades_editar.Orden
                        arr[0].IdSub = this.aplicativos_funcionalidades_editar.IdSub
                        arr[0].Sub_Nombre = this.aplicativos_funcionalidades_editar.IdSub > 0 ? this.subCarpetas.filter(f => f.IdSub == this.aplicativos_funcionalidades_editar.IdSub)[0].Nombre : null
                        this.aplicativos_funcionalidades_editar.mostrar = false
                        this.temp_aplicativos = null
                        this.aplicativos_funcionalidades_editar.mostrar = false
                    } else {
                        this.cargar_aplicativos()
                            .then(() => {
                                let arr = this.aplicativos.filter(data => data.IdAplicativo == this.aplicativos_select_index)
                                this.aplicativos_select = arr[0]
                            })
                        this.aplicativos_funcionalidades_editar.mostrar = false
                    }
                })
                .catch(() => {
                    
                })
        },

        guardar_privilegios() {
            let IdFuncionalidad = this.aplicativos_funcionalidades.IdFuncionalidad
            let nombre = this.aplicativos_funcionalidades.nombre
            let descripcion = this.aplicativos_funcionalidades.descripcion
            this.axios.post('/app/administracion/guardar_privilegios', {
                    IdFuncionalidad: IdFuncionalidad,
                    nombre: nombre.toUpperCase(),
                    descripcion: descripcion,
                    activo: 1
                })
                .then(resp => {
                    if (!resp.data.error) {
                        this.aplicativos_funcionalidades.nombre = ""
                        this.aplicativos_funcionalidades.descripcion = ""
                        this.aplicativos_funcionalidades._level.push({
                            "IdPrivilegio": resp.data.codigo,
                            "Nombre_Privilegio": nombre.toUpperCase(),
                            "Descripcion_Privilegio": descripcion,
                            "Activo_Privilegio": "True"
                        })
                    }
                })
        },

        guardar_privilegios_activo(privilegio) {
            let temp_status = privilegio.Activo_Privilegio

            this.axios.post('/app/administracion/guardar_privilegio_estado', {
                    IdPrivilegio: privilegio.IdPrivilegio,
                    activo: ((privilegio.Activo_Privilegio) ? 0 : 1)
                })
                .then(() => {

                })
                .catch(() => {
                    privilegio.Activo_Privilegio = temp_status
                })
        },

        nuevo_aplicativo() {
            this.aplicativos_editar.mostrar = true
            this.aplicativos_editar.IdAplicativo = 0
            this.aplicativos_editar.Nombre = ""
            this.aplicativos_editar.Descripcion = ""
            this.aplicativos_editar.Prefijo = ""
            this.aplicativos_editar.Controlador = ""
            this.aplicativos_editar.Icono = ""
            this.aplicativos_editar.Activo = 1
            this.aplicativos_editar.is_new = true
        },

        editarAplicativo(data) {

            this.aplicativos_editar.mostrar = true
            this.aplicativos_editar.IdAplicativo = data.IdAplicativo
            this.aplicativos_editar.Nombre = data.Nombre
            this.aplicativos_editar.Descripcion = data.Descripcion
            this.aplicativos_editar.Prefijo = data.Prefijo
            this.aplicativos_editar.Controlador = data.Controlador
            this.aplicativos_editar.Icono = data.Icono
            this.aplicativos_editar.Activo = data.Activo
            this.aplicativos_editar.is_new = false
        },

        editar_aplicativo() {
            this.axios.post('/app/administracion/' + ((this.aplicativos_editar.is_new) ? 'nuevo_aplicativo' : 'editar_aplicativo'), this.aplicativos_editar)
                .then(() => {
                    this.aplicativos_editar.mostrar = false
                    this.cargar_aplicativos()
                })
                .catch(() => {

                })
        },

        eliminar_privilegios(item) {
            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Confirmación',
                text: '¿Desea eliminar el privilegio ' + item.Nombre_Privilegio + '?',
                accept: () => {
                    
                    this.axios.post('/app/administracion/eliminar_privilegio', {
                            idPrivilegio: item.IdPrivilegio
                        })
                        .then(() => {
                            this.aplicativos_editar.mostrar = false
                            this.aplicativos_funcionalidades._level = this.aplicativos_funcionalidades._level.filter(data => data.IdPrivilegio != item.IdPrivilegio)
                            
                        })
                }
            })
        },

        handleSelected(tr) {
            this.aplicativos_mostrar = true
            this.aplicativos_select = tr
            this.aplicativos_select_index = tr.IdAplicativo
        },
        ManejarCarpetas(tr) {
            this.aplicativos_select = tr
            this.carpetas_mostrar = true
        },
    },
    created() {
        this.cargar_aplicativos()
    }
}
</script>

<style>
#func-container .vs-input--icon {
    font-size: 1.5rem !important;
}
</style>
