<template>
<div id="busqueda-paciente">
    <div class="p-2">
        <form @submit="handleSubmit">
            <DxForm :visible="true" :form-data.sync="formulario" labelMode="floating">
                <DxGroupItem item-type="group" :col-count="2">
                    <DxGroupItem item-type="group">
                        <DxItem data-field="Nombre" editor-type="dxTextBox" :editor-options="{ maxLength: 1000 }" />
                        <DxButtonItem :button-options="submitButtonOptions" horizontal-alignment="center" verical-alignment="center" />
                    </DxGroupItem>
                    <DxGroupItem item-type="group">
                        <DxGroupItem item-type="group" :col-count="2">
                            <DxItem data-field="FechaInicio" v-if="busquedaExpediente" :is-required="busquedaExpediente" editor-type="dxDateBox" :editor-options="dateOptionsInicio">
                                <DxLabel :text="'Fecha inicial'" />
                            </DxItem>
                            <DxItem data-field="FechaFinal" v-if="busquedaExpediente" :is-required="busquedaExpediente" editor-type="dxDateBox" :editor-options="dateOptionsFinal">
                                <DxLabel :text="'Fecha final'" />
                            </DxItem>
                        </DxGroupItem>
                        <DxItem data-field="Buscar" editor-type="dxCheckBox" :editor-options="checkBoxOptions" :visible="FiltrosBusquedaPaciente.SeleccionTipoBusqueda">
                            <DxLabel :text="'Buscar paciente con expediente'" />
                        </DxItem>
                    </DxGroupItem>
                </DxGroupItem>
            </DxForm>
        </form>
    </div>
    <DxDataGrid v-if="!buscoConExpediente" :ref="dataGridRefKey" v-bind="DefaultDxGridConfiguration" :data-source="pacientes" :headerFilter="{visible:true, allowSearch: true }" :searchPanel="{visible:true}" :sorting="{mode: 'none'}" :paging="{ enabled:true, pageSize:10 }" :height="'100%'" :width="'100%'" @row-dbl-click="seleccionarPaciente">
        <DxSelection mode="single" />

        <DxGroupPanel :visible="true" />
        <DxGrouping :auto-expand-all="false" />
        <DxFilterPanel :visible="true"/>
        
        <DxColumn :width="250" data-field="Nombre" alignment="center" />
        <DxColumn :width="90" data-field="Edad" alignment="center" />
        <DxColumn :width="100" data-field="Genero" caption="Género" alignment="center" />
        <DxColumn :width="100" data-field="Admision" caption="Admisión" alignment="center" />
        <DxColumn :width="110" data-field="Habitacion" caption="Habitación" alignment="center" />
        <DxColumn :width="150" data-field="Ubicacion" caption="Ubicación" alignment="center" />
        <DxColumn :width="120" data-field="TipoAdmision" caption="Tipo admisión" alignment="center" />
       
        <DxColumn :width="100" data-field="FechaEntrada" caption="Fecha ingreso" format="dd/MM/yyyy HH:mm" data-type="datetime" alignment="center" />
       
        <DxColumn :width="200" data-field="Hospital" alignment="center" />
            <DxColumn :width="150" data-field="Seguro" alignment="center" />
        <DxColumn :width="100" data-field="CodigoPaciente" caption="Código" alignment="center" />
    </DxDataGrid>

    <DxDataGrid v-else-if="buscoConExpediente" :ref="dataGridRefKey" v-bind="DefaultDxGridConfiguration" :data-source="pacientes" :headerFilter="{visible:true, allowSearch: true }" :searchPanel="{visible:true}" :sorting="{mode: 'none'}" :paging="{ enabled:true, pageSize:10 }" :height="'100%'" :width="'100%'" @row-dbl-click="seleccionarPaciente">
        <DxSelection mode="single" />

        <DxGroupPanel :visible="true" />
        <DxGrouping :auto-expand-all="false" />


        <DxColumn :width="100" data-field="CodigoPaciente" caption="Código" alignment="center" />
        <DxColumn :width="250" data-field="Nombre" alignment="center" />
        <DxColumn :width="100" data-field="Edad" alignment="center" />
        <DxColumn :width="100" data-field="Genero" caption="Género" alignment="center" />
        <DxColumn :width="250" data-field="Motivoconsulta" caption="Motivo consulta" alignment="center" />
        <DxColumn :width="250" data-field="MedicoTratante" caption="Médico tratante" alignment="center" />
        <DxColumn :width="250" data-field="Especialidad" caption="Especialidad médico" alignment="center" />
        <DxColumn :width="130" data-field="Admision" caption="Admisión" alignment="center" />
        <DxColumn :width="120" data-field="Tipo_Admision" caption="Tipo admisión" alignment="center" />
        <DxColumn :width="130" data-field="FechaRegistro" caption="Fecha registro" format="dd/MM/yyyy HH:mm" data-type="datetime" alignment="center" />
        <DxColumn :width="100" data-field="FechaSalida" caption="Fecha alta" format="dd/MM/yyyy HH:mm" data-type="datetime" alignment="center" />
        <DxColumn :width="200" data-field="Hospital" alignment="center" />
        <DxColumn :width="150" data-field="Habitacion" caption="Habitación" alignment="center" />
        <DxColumn :width="150" data-field="Ubicacion" caption="Ubicación" alignment="center" />
        <DxColumn :width="150" data-field="Seguro" alignment="center" />
    </DxDataGrid>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'
import {
    DxForm,
    DxItem,
    DxGroupItem,
    DxButtonItem,
    DxLabel,
} from 'devextreme-vue/form'
import {
    DxDataGrid,
    DxColumn,
    DxSelection,
    DxGrouping,
    DxGroupPanel,
    DxFilterPanel,
} from 'devextreme-vue/data-grid'

const dataGridRefKey = 'grid'

export default {
    name: 'BusquedaPacientes',
    components: {
        DxForm,
        DxItem,
        DxGroupItem,
        DxButtonItem,
        DxDataGrid,
        DxColumn,
        DxSelection,
        DxLabel,
        DxGrouping,
        DxGroupPanel,
        DxFilterPanel,
    },
    data() {
        return {
            DefaultDxGridConfiguration,
            dataGridRefKey,
            submitButtonOptions: {
                text: 'Buscar paciente',
                type: 'success',
                icon: 'search',
                width: 200,
                useSubmitBehavior: true,
                disabled: false
            },
            pacientes: [],
            formulario: {
                Nombre: '',
                FechaInicio: '',
                FechaFinal: ''
            },

            dateOptionsInicio: {
                max: new Date(),
                type: "date",
            },
            dateOptionsFinal: {
                min: new Date(),
                max: new Date(),
                type: "date",
            },
            checkBoxOptions: {
                onValueChanged: this.checkBoxChanged
            },
            busquedaExpediente: false,
            buscoConExpediente: false
        }
    },
    props: {
        /**Filtros adicionales para ser enviados a la API objeto con el nombre de filtro y valor  {Hospital:'HLA'}, por ejemplo*/
        FiltrosBusquedaPaciente: {
            type:Object,
            default() {
                return {SeleccionTipoBusqueda:true}
            }
        },
    },
    methods: {
        handleSubmit(e) {
            e.preventDefault()
            if (this.formulario.Buscar) {
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaPacientesExpedientexFecha', {
                        NombrePaciente: this.formulario.Nombre,
                        Fechai: this.formatFecha(this.formulario.FechaInicio),
                        FechaF: this.formatFecha(this.formulario.FechaFinal),
                        ...this.FiltrosBusquedaPaciente
                    })
                    .then(resp => {
                        this.pacientes = resp.data.json.map((x) => {
                            return {
                                ...x,
                                Admision: x.SerieAdmision + x.Admision,
                            }
                        })
                        this.buscoConExpediente = true
                    })
            } else {
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaPacientesAdmision', {
                        NombrePaciente: this.formulario.Nombre,
                        ...this.FiltrosBusquedaPaciente
                    })
                    .then(resp => {
                        this.pacientes = resp.data.json.map((x) => {
                            return {
                                ...x,
                                Admision: x.SerieAdmision + x.Admision,
                            }
                        })
                        this.buscoConExpediente = false
                    })
            }

        },
        checkBoxChanged(e) {
            if (e.value) {
                this.busquedaExpediente = true
                this.formulario.FechaInicio = new Date()
                this.dateOptionsInicio = {
                    value: new Date(),
                    max: new Date(),
                    type: "date",
                    onValueChanged: this.valueChangedAnestesiaInicio,
                }
                this.dateOptionsFinal = {
                    value: null,
                    min: new Date(),
                    max: new Date(),
                    type: "date",
                }
            } else {
                this.busquedaExpediente = false
            }
        },
        valueChangedAnestesiaInicio(e) {
            this.dateOptionsFinal = {
                min: new Date(e.value),
                type: "date",
            }
        },
        formatFecha(date) {
            const year = new Date(date).toLocaleString('default', {
                year: 'numeric'
            });
            const month = new Date(date).toLocaleString('default', {
                month: '2-digit'
            });
            const day = new Date(date).toLocaleString('default', {
                day: '2-digit'
            });

            return [year, month, day].join('-');
        },
        seleccionarPaciente(e) {
            this.$emit('admision', e.data.Admision);
        }
    },
    mounted() {
        this.dateOptionsInicio = {
            value: new Date(),
            type: "date",
            onValueChanged: this.valueChangedAnestesiaInicio,
        }

        this.dateOptionsFinal = {
                min: new Date(),
                max: new Date(),
                type: "date",
            },
        this.buscoConExpediente = false
    },
    watch: {

    },
    computed: {
        dataGrid: function () {
            return this.$refs[dataGridRefKey].instance;
        },
    },
}
</script>

<style>
.dx-overlay-wrapper.dx-popup-wrapper.dx-dropdowneditor-overlay.dx-datebox-wrapper.dx-datebox-wrapper-date.dx-datebox-wrapper-calendar {
    z-index: 999999 !important;
}

.dx-overlay-wrapper.dx-popup-wrapper.dx-header-filter-menu {
    z-index: 999999 !important;
}
</style>
