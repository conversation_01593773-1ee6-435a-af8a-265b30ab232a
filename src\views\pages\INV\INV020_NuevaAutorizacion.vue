<template>
    <vx-card style="display: none">
        <!--------------Inicio Vista Previa información _---------->
        <!---------AUTORIZACIOON PARA SOLICITUD DE COMPRA------------------------->
        <vs-popup align="center" classContent="popup-example" title="Autorización Solicitud de Compra" :active.sync="Estado_VentanaNueva">
            <form>
                <div class="flex flex-wrap">
                    <!----- Clase ---->
                    <div style="margin-top: 30px" class="center" v-show="claseShow">
                        <ValidationProvider name="Responsable" rules="required" v-slot="{ errors }" class="required">
                            <label class="typo__label">Clase</label>
                            <multiselect v-model="cb_clase" :options="Listado_Clase" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Clase_seleccionada" placeholder="Seleccionar clase" @input="onChangeClase" :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>
    
                    <!----- Departamentos ---->
                    <div style="margin-top: 30px" class="center">
                        <ValidationProvider name="Responsable" rules="required" v-slot="{ errors }" class="required">
                            <label class="typo__label">Departamentos</label>
                            <multiselect v-model="cb_departamentos" :options="Lista_departamentos" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Departamento_seleccionado" placeholder="Seleccionar departamento" @input="onChangeDepartamento" :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>
    
                    <!----- Responsables  1---->
                    <div style="margin-top: 30px" class="center_2">
                        <ValidationProvider name="Responsable" rules="required" v-slot="{ errors }" class="required">
                            <label style="font-size: 12px">Responsable </label>
                            <multiselect v-model="cb_busqueda_corporativo" :options="Lista_responsable" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Corporativo_seleccionado" placeholder="Búsqueda responsable" track-by="Corporativo" name="id_responsable" @input="onChangeResponsable" :disabled="responsable.deshabilitado" :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>
    
                    <div class="center_2">
                        <td class="font-semibold">{{ responsable.corporativo }}</td>
                    </div>
                    <div class="center_2">
                        <td class="font-semibold">{{ responsable.nombre }}</td>
                    </div>
                    <div style="margin-bottom: 100px" class="center_2">
                        <td class="font-semibold">{{ responsable.correo }}</td>
                    </div>
    
                    <!----- Responsables 2 ---->
                </div>
                <vs-divider></vs-divider>
    
                <vs-button :disabled="Deshabilitar_campos" color="primary" style="float: right" type="filled" icon-pack="feather" icon="icon-save" @click="Guardar_Asociacion">
                    Grabar</vs-button>
                <vs-button color="danger" style="float: right" type="border" icon-pack="feather" icon="icon-x" @click="Estado_VentanaNueva = false">
                    Cancelar</vs-button>
            </form>
        </vs-popup>
    
        <!--------------Fin Vista Previa información _---------->
    
    </vx-card>
    </template>
    
    <script>
    /**
     * @General
     * Modulo registra los responsables de las solicitudes de compras por departamento
     */
    import Multiselect from "vue-multiselect";
    import "vue-multiselect/dist/vue-multiselect.min.css";
    
    export default {
        name: "ComponenteNuevaAutorizacion",
        data() {
            return {
                Deshabilitar_campos: false,
                Estado_VentanaNueva: true, //mostrar las opciones del producto
                Estado_VentanaNueva_Cotizacion: true,
                Nombre_depto: "",
                Codigo_depto: "",
    
                id_documento_selec: "1",
                id_numero_doc: "1",
    
                cb_departamentos: "",
                Lista_departamentos: [],
                id_departamento_selec: "",
    
                cb_busqueda_corporativo: "",
                Lista_responsable: [],
                id_responsable_seleccionado: "",
    
                Lista_autorizaciones: [],
                responsable: {
                    corporativo: "",
                    nombre: "",
                    correo: "",
                    enviar: false,
                    deshabilitado: false
                },
    
                cb_busqueda_corporativo2: "",
                Lista_responsable2: [],
                id_responsable_seleccionado2: "",
    
                Lista_autorizaciones2: [],
                responsable2: {
                    corporativo: "",
                    nombre: "",
                    correo: "",
                    enviar: false,
                    deshabilitado: false
                },
    
                res_variables: false,
    
                cb_clase: "",
                Listado_Clase: [],
                id_clase_seleccionada: "1",
                claseShow: false
    
            };
        },
        components: {
            Multiselect,
        },
        props: {
            callback: {
                default: null,
            },
            cerrar: {
                default: null,
            },
        },
        watch: {
            Estado_VentanaNueva() {
                this.cerrar();
            },
    
        },
        methods: {
            Clase_seleccionada({
                Nombre
            }) {
                return ` ${Nombre} `;
            },
            onChangeClase(value) {
                //Si existe seleccionada un elemento
                if (value !== null && value.length !== 0) {
                    this.id_clase_seleccionada = value.Codigo;
    
                    if (this.id_departamento_selec > 0 || this.id_departamento_selec != "")
                        this.Consultar_Autorizaciones();
                } else {
                    this.responsable.corporativo = '';
                    this.responsable.nombre = '';
                    this.responsable.correo = '';
                    this.responsable2.corporativo = '';
                    this.responsable2.nombre = '';
                    this.responsable2.correo = '';     
                    this.cb_busqueda_corporativo = '';
                    this.cb_busqueda_corporativo2 = '';
                }
    
            },
            Departamento_seleccionado({
                NOMBRE
            }) {
                return ` ${NOMBRE} `;
            },
            onChangeDepartamento(value) {
                //Si existe seleccionada un elemento
                if (value !== null && value.length !== 0) {
                    this.id_departamento_selec = value.CODIGO;
                    if (this.id_clase_seleccionada > 0)
                        this.Consultar_Autorizaciones();
                } else {
                    this.id_departamento_selec = "";
                    this.Lista_autorizaciones = "";
                    this.responsable.corporativo = '';
                    this.responsable.nombre = '';
                    this.responsable.correo = '';
                    this.cb_busqueda_corporativo = '';
    
    
                    this.responsable.corporativo = '';
                    this.responsable.nombre = '';
                    this.responsable.correo = '';
                    this.responsable2.corporativo = '';
                    this.responsable2.nombre = '';
                    this.responsable2.correo = '';                
                    this.cb_busqueda_corporativo = '';
                    this.cb_busqueda_corporativo2 = '';
    
                }
            },
            async Consultar_Departamentos() {
                this.cb_departamentos = null;
                this.id_departamento_selec = "";
    
                const url = this.$store.state.global.url;
                const sesion = this.$store.state.sesion;
                this.$vs.loading();
                this.axios
                    .post(url + "app/v1_OrdenCompra/consulta_departamento", {
                        id_empresa: sesion.sesion_empresa,
                        tipo_consulta: "",
                    })
                    .then((resp) => {
                        this.$vs.loading.close();
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: "#B71C1C",
                                title: "Orden de compra",
                                text: resp.data.mensaje,
                            });
                            this.Lista_departamentos = [];
                        } else {
                            this.Lista_departamentos = resp.data.json;
                        }
                    })
                    .catch(() => {
                        this.$vs.loading.close();
                    });
                this.$vs.loading.close();
            },
            Corporativo_seleccionado({
                Corporativo,
                nombre
            }) {
                return `${Corporativo} - ${nombre} `;
            },
            Corporativo_seleccionado2({
                Corporativo,
                nombre
            }) {
                return `${Corporativo} - ${nombre} `;
            },
            onChangeResponsable(value) {
    
                if (value !== null && value.length !== 0) {
                    this.id_responsable_seleccionado = value.Corporativo;
                    this.responsable.corporativo = value.Corporativo;
                    this.responsable.nombre = value.nombre;
                    this.responsable.correo = value.correo;
                } else {
                    this.id_responsable_seleccionado = "";
                    this.responsable.corporativo = "";
                    this.responsable.nombre = "";
                    this.responsable.correo = "";
                }
            },
            onChangeResponsable2(value) {
    
                if (value !== null && value.length !== 0) {
                    this.id_responsable_seleccionado2 = value.Corporativo;
                    this.responsable2.corporativo = value.Corporativo;
                    this.responsable2.nombre = value.nombre;
                    this.responsable2.correo = value.correo;
                } else {
                    this.id_responsable_seleccionado2 = "";
                    this.responsable2.corporativo = "";
                    this.responsable2.nombre = "";
                    this.responsable2.correo = "";
                }
            },
    
            Consultar_Responsable(Tipo_Busqueda = "", Busqueda = "") {
                const url = this.$store.state.global.url;
                this.$vs.loading();
                this.axios
                    .post(url + "app/v1_OrdenCompra/consulta_corporativo", {
                        TIPO_BUSQUEDA: Tipo_Busqueda,
                        BUSQUEDA: Busqueda,
                    })
                    .then((resp) => {
                        this.$vs.loading.close();
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: "#B71C1C",
                                title: "Orden de compra",
                                text: resp.data.mensaje,
                            });
                            //Limpia la tabla si no existen registros
                            this.Lista_responsable = "";
                            this.Lista_responsable2 = "";
                        } else {
                            this.Lista_responsable = resp.data.json;
                            this.Lista_responsable2 = resp.data.json;
                        }
                    })
                    .catch(() => {
                        this.$vs.loading.close();
                    });
                this.$vs.loading.close();
            },
            Guardar_Asociacion() {
                const sesion = this.$store.state.sesion;
                this.Corporativo_Sesion = sesion.corporativo
                this.res_variables = false;
    
                this.$vs.loading();
    
                if (this.responsable.enviar === true && this.id_responsable_seleccionado > 0) {
                    const url = this.$store.state.global.url;
    
                 
                    this.axios.post(url + 'app/v1_OrdenCompra/autorizacion_doc', {
                            empresa: sesion.sesion_empresa,
                            codigo: 0,
                            id_documento: this.id_documento_selec,
                            id_responsable: this.id_responsable_seleccionado,
                            id_departamento: this.id_departamento_selec,
                            num_documento: this.id_numero_doc,
                            operacion: 'N',
                            corporativo: this.Corporativo_Sesion,
                            activo: 'S',
                            clase_sol: this.id_clase_seleccionada
                        })
                        .then(resp => {
                            this.$vs.loading.close();
                            if (resp.data.codigo !== 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Autorización Documentos',
                                    text: resp.data.mensaje,
                                })
    
                            } else {
                               
                                    this.$vs.loading.close();
                                    this.Estado_VentanaNueva = false;
                                    this.$root.$refs.A.Consultar_Autorizaciones();
                                
                            }
                        })
                        .catch(() => {
                            this.$vs.loading.close();
                        })
                    this.$vs.loading.close();
                }
                
                //}
    
            },
            Validacion_Campos(Tipo, Nombre, valor, obligatorio, cant_maxima) {
                /**
                 * @General
                 * Tipo : N = Númerio, Nombre = Descripción del campo, Valor = A evaluar, Obligatorio= Verdadero o falso,cant_maxima  = Aplica unicamente para un string y define la cantidad de caracteres 
                 * Devuelve un mensaje al usuario si las validaciones no se cumple y aborta, de lo contrario devuelve verdadero
                 */
    
                if (Tipo == 'ID') {
                    if (valor <= 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Autorización Documentos',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else if (Tipo == 'N') {
    
                    if (valor < 0 || valor === "") {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Autorización Documentos',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else {
                    if (valor == '' && obligatorio == true) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Autorización Documentos',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
    
                        if (cant_maxima < valor.length) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Autorización Documentos',
                                text: 'Campo ' + Nombre + ' no debe superar los ' + cant_maxima + ' carácteres.',
                            });
                            return false;
                        } else {
                            return true;
                        }
    
                    }
                }
            },
            Consultar_Autorizaciones() {
    
                if (this.id_documento_selec !== null && this.id_documento_selec.length !== 0) {
                    const url = this.$store.state.global.url;
                    const sesion = this.$store.state.sesion;
                    this.$vs.loading();
                    this.axios.post(url + 'app/v1_OrdenCompra/consulta_listado_autorizaciones', {
                            empresa: sesion.sesion_empresa,
                            id_documento: '1',
                            num_documento: '1',
                            tipo_consulta: 'E',
                            id_departamento: this.id_departamento_selec,
                            clase_sol: this.id_clase_seleccionada
    
                        })
                        .then(resp => {
                            this.$vs.loading.close();
                            if (resp.data.codigo != 0) {
    
                                //Limpia la tabla si no existen registros
                                this.Lista_autorizaciones = "";
                                this.responsable.corporativo = '';
                                this.responsable.nombre = '';
                                this.responsable.correo = '';
                                this.cb_busqueda_corporativo = '';
                                this.responsable.enviar = true;
                                this.responsable.deshabilitado = false;
                            } else {
                                this.Lista_autorizaciones = resp.data.json;
    
                                this.Lista_autorizaciones = []
                                resp.data.json.map(data => {
                                    this.Lista_autorizaciones.push({
                                        ...data
                                    })
                                })
    
                                this.responsable.corporativo = this.Lista_autorizaciones.find(obj => obj.ID_DOCUMENTO === this.id_documento_selec).ID_RESPONSABLE;
                                this.responsable.nombre = this.Lista_autorizaciones.find(obj => obj.ID_DOCUMENTO === this.id_documento_selec).NOMBRE;
                                this.responsable.correo = this.Lista_autorizaciones.find(obj => obj.ID_DOCUMENTO === this.id_documento_selec).CORREO;
                                this.cb_busqueda_corporativo = {
                                    Corporativo: this.responsable.corporativo,
                                    nombre: this.responsable.nombre
                                };
                                this.responsable.enviar = false;
                                this.responsable.deshabilitado = true;
    
                            }
                        })
                        .catch(() => {
                            this.$vs.loading.close();
                        })
                    this.$vs.loading.close();
                    /******************RESPONSABLE NUMERO DOS******* */
    
                    setTimeout(() => {
                        this.$vs.loading();
                        this.axios.post(url + 'app/v1_OrdenCompra/consulta_listado_autorizaciones', {
                                empresa: sesion.sesion_empresa,
                                id_documento: '1',
                                num_documento: '2',
                                tipo_consulta: 'E',
                                id_departamento: this.id_departamento_selec,
                                clase_sol: this.id_clase_seleccionada
    
                            })
                            .then(resp => {
                                this.$vs.loading.close();
                                if (resp.data.codigo != 0) {
    
                                    //Limpia la tabla si no existen registros
                                    this.Lista_autorizaciones2 = "";
                                    this.responsable2.corporativo = '';
                                    this.responsable2.nombre = '';
                                    this.responsable2.correo = '';
                                    this.cb_busqueda_corporativo2 = '';
                                    this.responsable2.enviar = true;
                                    this.responsable2.deshabilitado = false;
    
                                    this.Deshabilitar_campos = false;
    
                                } else {
                                    this.Lista_autorizaciones2 = resp.data.json;
                                    //this.Deshabilitar_campos = true;
    
                                    this.Lista_autorizaciones2 = []
                                    resp.data.json.map(data => {
                                        this.Lista_autorizaciones2.push({
                                            ...data
                                        })
                                    })
    
                                    this.responsable2.corporativo = this.Lista_autorizaciones2.find(obj => obj.ID_DOCUMENTO === this.id_documento_selec).ID_RESPONSABLE;
                                    this.responsable2.nombre = this.Lista_autorizaciones2.find(obj => obj.ID_DOCUMENTO === this.id_documento_selec).NOMBRE;
                                    this.responsable2.correo = this.Lista_autorizaciones2.find(obj => obj.ID_DOCUMENTO === this.id_documento_selec).CORREO;
                                    this.cb_busqueda_corporativo2 = {
                                        Corporativo: this.responsable2.corporativo,
                                        nombre: this.responsable2.nombre
                                    };
    
                                    this.responsable2.enviar = false;
                                    this.responsable2.deshabilitado = true;
                                    if (this.responsable.enviar === false) {
                                        this.Deshabilitar_campos = true;
                                    }
                                }
                            })
                            .catch(() => {
                                this.$vs.loading.close();
                            })
                        this.$vs.loading.close();
                    }, 500)
    
                } else {
    
                    this.Lista_autorizaciones = "";
                    this.responsable.corporativo = '';
                    this.responsable.nombre = '';
                    this.responsable.correo = '';
                    this.responsable.deshabilitado = false;
                    this.responsable.enviar = false;
                    this.cb_busqueda_corporativo = '';
    
                    this.Lista_autorizaciones2 = "";
                    this.responsable2.corporativo = '';
                    this.responsable2.nombre = '';
                    this.responsable2.correo = '';
                    this.responsable2.deshabilitado = false;
                    this.responsable2.enviar = false;
                    this.cb_busqueda_corporativo2 = '';
                }
    
            },
            cargar_clase() {
    
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/inventario/invClaselist', {})
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.Listado_Clase = [];
                        } else {
                            this.Listado_Clase = resp.data.json;
                        }
                    })
                    .catch(() => {
    
                    })
    
            },
        },
        mounted() {
            this.Consultar_Departamentos();
            this.Consultar_Responsable();
            this.cargar_clase();
        },
    };
    </script>
    
    <style scoped>
    .center {
        margin: auto;
        width: 60%;
        padding: 10px;
    }
    
    .center_2 {
        margin: auto;
        width: 60%;
        padding: 3px;
    }
    </style>
    