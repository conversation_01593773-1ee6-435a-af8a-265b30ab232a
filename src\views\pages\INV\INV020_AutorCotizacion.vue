<template>
    <vx-card style="display: none">
        <!--------------Inicio Vista Previa información _---------->
    
        <!---------AUTORIZACIOON COTIZACION------------------------->
        <vs-popup align="center" classContent="popup-example" title="Autorización Cotización" :active.sync="Estado_VentanaNueva_Cotizacion">
            <form>
                <div class="flex flex-wrap">
    
                    <!----- Responsables 1 ---->
                    <div style="margin-top: 30px" class="center_2">
                        <ValidationProvider name="Responsable" rules="required" v-slot="{ errors }" class="required">
                            <label style="font-size: 12px">Responsable No. 1</label>
                            <multiselect v-model="cb_busqueda_corporativo" :options="Lista_responsable" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Corporativo_seleccionado" placeholder="Búsqueda responsable" track-by="Corporativo" name="id_responsable" @input="onChangeResponsable" :disabled="Deshabilitar_campos" :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>
    
                    <div class="center_2">
                        <td class="font-semibold">{{ responsable.corporativo }}</td>
                    </div>
                    <div class="center_2">
                        <td class="font-semibold">{{ responsable.nombre }}</td>
                    </div>
                    <div style="margin-bottom: 100px" class="center_2">
                        <td class="font-semibold">{{ responsable.correo }}</td>
                    </div>
                </div>
                <vs-divider></vs-divider>
                <vs-button :disabled="Deshabilitar_campos" color="primary" style="float: right" type="filled" icon-pack="feather" icon="icon-save" @click="Guardar_Asociacion">
                    Grabar</vs-button>
                <vs-button color="danger" style="float: right" type="border" icon-pack="feather" icon="icon-x" @click="Estado_VentanaNueva_Cotizacion = false">
                    Cancelar</vs-button>
            </form>
        </vs-popup>
    
        <!--------------Fin Vista Previa información _---------->
    
    </vx-card>
    </template>
    
    <script>
    /**
     * @General
     * Modulo registra los responsables de las cotizaciones
     */
    import Multiselect from "vue-multiselect";
    import "vue-multiselect/dist/vue-multiselect.min.css";
    
    export default {
        name: "ComponenteNuevaCotizacion",
        data() {
            return {
                Deshabilitar_campos: false,
                Estado_VentanaNueva_Cotizacion: true,
                Nombre_depto: "",
                Codigo_depto: "",
    
                id_documento_selec: "2",
                id_numero_doc: "1",
    
                cb_departamentos: "",
                Lista_departamentos: [],
                id_departamento_selec: "0",
    
                cb_busqueda_corporativo: "",
                Lista_responsable: [],
                id_responsable_seleccionado: "",
    
                Lista_autorizaciones: [],
                responsable: {
                    corporativo: "",
                    nombre: "",
                    correo: "",
                },
    
                res_variables: false,
    
            };
        },
        components: {
            Multiselect,
        },
        props: {
            callback: {
                default: null,
            },
            cerrar: {
                default: null,
            },
        },
        watch: {
            Estado_VentanaNueva_Cotizacion() {
                this.cerrar();
            },
    
        },
        methods: {
    
            Departamento_seleccionado({
                NOMBRE
            }) {
                return ` ${NOMBRE} `;
            },
            onChangeDepartamento(value) {
                //Si existe seleccionada un elemento
                if (value !== null && value.length !== 0) {
                    this.id_departamento_selec = value.CODIGO;
                    this.Consultar_Autorizaciones();
                } else {
                    this.id_departamento_selec = "";
                    this.Lista_autorizaciones = "";
                    this.responsable.corporativo = '';
                    this.responsable.nombre = '';
                    this.responsable.correo = '';
                    this.cb_busqueda_corporativo = '';
                }
            },
            Corporativo_seleccionado({
                Corporativo,
                nombre
            }) {
                return `${Corporativo} - ${nombre} `;
            },
            onChangeResponsable(value) {
    
                if (value !== null && value.length !== 0) {
                    this.id_responsable_seleccionado = value.Corporativo;
                    this.responsable.corporativo = value.Corporativo;
                    this.responsable.nombre = value.nombre;
                    this.responsable.correo = value.correo;
                } else {
                    this.id_responsable_seleccionado = "";
                    this.responsable.corporativo = "";
                    this.responsable.nombre = "";
                    this.responsable.correo = "";
                }
            },
            Consultar_Responsable(Tipo_Busqueda = "", Busqueda = "") {
                const url = this.$store.state.global.url;
                this.$vs.loading();
                this.axios
                    .post(url + "app/v1_OrdenCompra/consulta_corporativo", {
                        TIPO_BUSQUEDA: Tipo_Busqueda,
                        BUSQUEDA: Busqueda,
                    })
                    .then((resp) => {
                        this.$vs.loading.close();
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: "#B71C1C",
                                title: "Orden de compra",
                                text: resp.data.mensaje,
                            });
                            //Limpia la tabla si no existen registros
                            this.Lista_responsable = "";
                        } else {
                            this.Lista_responsable = resp.data.json;
                        }
                    })
                    .catch(() => {
                        this.$vs.loading.close();
                    });
                this.$vs.loading.close();
            },
            Guardar_Asociacion() {
                const sesion = this.$store.state.sesion;
                this.Corporativo_Sesion = sesion.corporativo
                this.res_variables = false;
    
                this.res_variables = this.Validacion_Campos('ID', 'Responsable', this.id_responsable_seleccionado, true, 0);
    
                if (this.res_variables) {
                    this.$vs.loading();
                    const url = this.$store.state.global.url;
                    this.axios.post(url + 'app/v1_OrdenCompra/autorizacion_doc', {
                            empresa: sesion.sesion_empresa,
                            codigo: 0,
                            id_documento: this.id_documento_selec,
                            id_responsable: this.id_responsable_seleccionado,
                            id_departamento: this.id_departamento_selec,
                            num_documento: this.id_numero_doc,
                            operacion: 'N',
                            corporativo: this.Corporativo_Sesion,
                            activo: 'S',
                        })
                        .then(resp => {
                            this.$vs.loading.close();
                            if (resp.data.codigo !== 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Autorización Documentos',
                                    text: resp.data.mensaje,
                                })
    
                            } else {
    
                                this.Estado_VentanaNueva_Cotizacion = false;
                                this.$root.$refs.A.Consultar_Autorizaciones();
                            }
                        })
                        .catch(() => {
                            this.$vs.loading.close();
                        })
                    this.$vs.loading.close();
                }
    
            },
            Validacion_Campos(Tipo, Nombre, valor, obligatorio, cant_maxima) {
                /**
                 * @General
                 * Tipo : N = Númerio, Nombre = Descripción del campo, Valor = A evaluar, Obligatorio= Verdadero o falso,cant_maxima  = Aplica unicamente para un string y define la cantidad de caracteres 
                 * Devuelve un mensaje al usuario si las validaciones no se cumple y aborta, de lo contrario devuelve verdadero
                 */
                if (Tipo == 'ID') {
                    if (valor <= 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Autorización Documentos',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else if (Tipo == 'N') {
    
                    if (valor < 0 || valor === "") {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Autorización Documentos',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else {
                    if (valor == '' && obligatorio == true) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Autorización Documentos',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
    
                        if (cant_maxima < valor.length) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Autorización Documentos',
                                text: 'Campo ' + Nombre + ' no debe superar los ' + cant_maxima + ' carácteres.',
                            });
                            return false;
                        } else {
                            return true;
                        }
    
                    }
                }
            },
            Consultar_Autorizaciones() {
    
                if (this.id_documento_selec !== null && this.id_documento_selec.length !== 0) {
                    const url = this.$store.state.global.url
                    const sesion = this.$store.state.sesion;
                    this.$vs.loading();
                    this.axios.post(url + 'app/v1_OrdenCompra/consulta_listado_autorizaciones', {
                            empresa: sesion.sesion_empresa,
                            id_documento: this.id_documento_selec,
                            num_documento: this.id_numero_doc,
                            tipo_consulta: 'E',
                            id_departamento: this.id_departamento_selec
    
                        })
                        .then(resp => {
                            this.$vs.loading.close();
                            if (resp.data.codigo != 0) {
                                this.Deshabilitar_campos = false;
                                //Limpia la tabla si no existen registros
                                this.Lista_autorizaciones = "";
                                this.responsable.corporativo = '';
                                this.responsable.nombre = '';
                                this.responsable.correo = '';
                                this.cb_busqueda_corporativo = '';
                            } else {
                                this.Lista_autorizaciones = resp.data.json;
                                //this.Deshabilitar_campos = true;
    
                                this.Lista_autorizaciones = []
                                resp.data.json.map(data => {
                                    this.Lista_autorizaciones.push({
                                        ...data
                                    })
                                })
    
                                this.responsable.corporativo = this.Lista_autorizaciones.find(obj => obj.ID_DOCUMENTO === this.id_documento_selec).ID_RESPONSABLE;
                                this.responsable.nombre = this.Lista_autorizaciones.find(obj => obj.ID_DOCUMENTO === this.id_documento_selec).NOMBRE;
                                this.responsable.correo = this.Lista_autorizaciones.find(obj => obj.ID_DOCUMENTO === this.id_documento_selec).CORREO;
                                this.cb_busqueda_corporativo = {
                                    Corporativo: this.responsable.corporativo,
                                    nombre: this.responsable.nombre
                                };
                                this.Deshabilitar_campos = true;
                            }
                        })
                        .catch(() => {
                            this.$vs.loading.close();
                        })
                    this.$vs.loading.close();
    
                } else {
    
                    this.Lista_autorizaciones = "";
                    this.responsable.corporativo = '';
                    this.responsable.nombre = '';
                    this.responsable.correo = '';
                    this.cb_busqueda_corporativo = '';
    
                }
    
            },
        },
        mounted() {
            this.Consultar_Responsable();
            this.Consultar_Autorizaciones();
        },
    };
    </script>
    
    <style scoped>
    .center {
        margin: auto;
        width: 60%;
        padding: 10px;
    }
    
    .center_2 {
        margin: auto;
        width: 60%;
        padding: 3px;
    }
    </style>
    