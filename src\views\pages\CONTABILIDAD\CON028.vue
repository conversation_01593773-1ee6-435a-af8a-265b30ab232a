<template>
    <div class="container">
    
        <div class="a-lRmOp-0 flex flex-wrap">
    
            <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6  p-1">
                <SM-Buscar v-model="PeriodoInicial" label="Periodo Inicial" api="app/v1_contabilidad_general/BusquedaPeriodos" :api_campo_respuesta_mostrar="['codigo', 'descripcion']" :api_campos="['codigo', 'descripcion']" :api_titulos="['codigo', 'descripcion']" api_campo_respuesta="codigo" :api_preload="true" :disabled_texto="true" :callback_buscar="ConsultarPeriodoInicial">
                </SM-Buscar>
            </div>
            <div class="w-full md:w-4/5 lg:w-4/5 xl:w-4/5  p-1">
                <vs-input label="Descripción Periodo" class="w-full" v-model="DescPeriodoInicial" readonly />
            </div>
    
        </div>
        <div class="a-6Wv60-0 flex flex-wrap">
            <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6  p-1">
    
                <SM-Buscar v-model="PeriodoFinal" label="Periodo Final" api="app/v1_contabilidad_general/BusquedaPeriodos" :api_campo_respuesta_mostrar="['codigo', 'descripcion']" :api_campos="['codigo', 'descripcion']" :api_titulos="['codigo', 'descripcion']" api_campo_respuesta="codigo" :api_preload="true" :disabled_texto="true"  :callback_buscar="ConsultarPeriodoFinal">
                </SM-Buscar>
            </div>
            <div class="w-full md:w-4/5 lg:w-4/5 xl:w-4/5  p-1">
                <vs-input label="Descripción Periodo" class="w-full" v-model="DescPeriodoFinal" readonly />
            </div>
        </div>
    
        <div class="a-lRmOp-1 flex flex-wrap">
    
            <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6  p-1">
    
                <SM-Buscar v-model="Cuenta" label="Mascara Cuenta" api="app/v1_contabilidad_general/ConsultaCatalogoCuentas" :api_campo_respuesta_mostrar="['Codigo', 'Nombre']" :api_campos="['Codigo', 'Nombre']" :api_titulos="['codigo', 'Nombre']" :api_filtro="{
                            'Codigo': this.Cuenta
                            , 'Nombre': this.DescripcionCuenta
                        }" api_campo_respuesta="Codigo" :api_preload="true" :disabled_texto="true" :callback_buscar="ConsultarCuenta">
                </SM-Buscar>
            </div>
            <div class="w-full md:w-4/5 lg:w-4/5 xl:w-4/5  p-1">
                <vs-input label="Descripción Periodo" class="w-full" v-model="DescripcionCuenta" readonly />
            </div>
    
            <div class="flex flex-row p-4">
    
                <vs-button @click="generarReporte ('PDF')" color="danger" type="border" style="margin-right:5px"><i class="fas fa-file-pdf "></i>
                    Generar
                </vs-button>
    
                <vs-button @click="generarReporte ('EXCEL')" color="success" type="border"><i class="far fa-file-excel"></i>
                    Generar
                </vs-button>
    
            </div>
    
        </div>
        <div class="a-6Wv60-1 input-group">
    
            <vs-row>
                <div class="w-full md:w-5/6 lg:w-5/6 xl:w-5/6  p-1">
                    <vs-row>
                        <vs-checkbox v-model="ConsolidarDatos">
                            ConsolidarDatos
                        </vs-checkbox>
                    </vs-row>
    
                    <vs-row>
                        <vs-checkbox v-model="honorariosDetallados">
                            Honorarios Detallados
                        </vs-checkbox>
                    </vs-row>
    
                    <vs-row>
                        <vs-checkbox v-model="obviarFacturasCredito">
                            Obviar Facturas y Recibos Crédito
                        </vs-checkbox>
                        <vs-checkbox v-model="obviarFacturasContado">
                            Obviar Facturas y Recibos Contado
                        </vs-checkbox>
                        <vs-checkbox v-model="NoMostrarApertura">
                            No mostrar partidas de apertura
                        </vs-checkbox>
                    </vs-row>
    
                </div>
    
                <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6  p-1">
                    <vs-row>
                        <vs-checkbox v-model="incluirDebe">
                            Incluir Debe
                        </vs-checkbox>
    
                        <vs-checkbox v-model="incluirHaber">
                            Incluir Haber
                        </vs-checkbox>
                    </vs-row>
                </div>
    
            </vs-row>
    
        </div>
    
    </div>
    </template>
    
    <script>
    export default {
        components: {
    
        },
        data() {
            return {
                reportOptions: [{
                        label: 'Obviar Facturas y Recibos "Crédito"',
                        value: '1',
                    },
                    {
                        label: 'Obviar Facturas y Recibos "Contado"',
                        value: '2',
                    }
                ],
                optionChecked: null,
                PeriodoInicial: 0,
                DescPeriodoInicial: '',
                PeriodoFinal: 0,
                DescPeriodoFinal: '',
                Cuenta: '',
                DescripcionCuenta: '',
                ConsolidarDatos: false,
                obviarFacturasCredito: false,
                obviarFacturasContado: false,
                honorariosDetallados: false,
                incluirDebe: false,
                incluirHaber: false,
                NoMostrarApertura: false
            }
        },
        watch: {
            async optionChecked(checked) {
                
            },
            ConsolidarDatos(newValue) {
                if (newValue) {
                    this.obviarFacturasCredito = false;
                    this.obviarFacturasContado = false;
                }
            },
            obviarFacturasCredito(newValue) {
                if (newValue) {
                    this.ConsolidarDatos = false;
                }
            },
            obviarFacturasContado(newValue) {
                if (newValue) {
                    this.ConsolidarDatos = false;
                }
            }
        },
        methods: {
            ConsultarPeriodoInicial(data) {
              
                this.DescPeriodoInicial = data.descripcion
            },
            ConsultarPeriodoFinal(data) {
          
                this.DescPeriodoFinal = data.descripcion
            },
            ConsultarCuenta(cuenta) {
                
                this.DescripcionCuenta = cuenta.Nombre
            },
            
            async generarReporte() {
                let CConsolidarDatos = this.ConsolidarDatos == true ? 1 : 0;
                let ChonorariosDetallados = this.honorariosDetallados == true ? 1 : 0;
                let CobviarFacturasCredito = this.obviarFacturasCredito == true ? 1 : 0;
                let CobviarFacturasContado = this.obviarFacturasContado == true ? 1 : 0;
                let CincluirDebe = this.incluirDebe == true ? 1 : 0;
                let CincluirHaber = this.incluirHaber == true ? 1 : 0;
                let CNoMostrarApertura = this.NoMostrarApertura == true ? 1 : 0;
                this.$reporte_modal({
    
                    Nombre: "ReporteMovimientoAuxiliarCuentas",
                    Opciones: {
                        tiporeporte: "application/pdf",
                        nombrereporte: "ReporteMovimientoAuxiliarCuentas",
                        PeriodoInicial: this.PeriodoInicial,
                        PeriodoFinal: this.PeriodoFinal,
                        mascara: this.Cuenta,
                        ConsolidadoB: CConsolidarDatos,
                        iHonosDetalladoB: ChonorariosDetallados,
                        iCreditoB: CobviarFacturasCredito,
                        iContadoB: CobviarFacturasContado,
                        iDebeB: CincluirDebe,
                        iHaberB: CincluirHaber,
                        iSinApertura: CNoMostrarApertura,
                        json: "[{\"Etiqueta\":\"nombrereporte\",\"Parametro\":\"nombrereporte\",\"value\":\"ReporteMovimientoAuxiliarCuentas\"},{\"Etiqueta\":\"Del Período\",\"Parametro\":\"PeriodoInicial\",\"value\":\"" + this.PeriodoInicial + "\"},{\"Etiqueta\":\"Período\",\"Parametro\":\"PeriodoFinal\",\"value\":\"" + this.PeriodoFinal + "\"},{\"Etiqueta\":\"Máscara\",\"Parametro\":\"mascara\",\"value\":\"" + this.Cuenta + "\"},{\"Etiqueta\":\"Consolidado\",\"Parametro\":\"ConsolidadoB\",\"value\":\"" + CConsolidarDatos + "\"},{\"Etiqueta\":\"Honorarios Detallados\",\"Parametro\":\"iHonosDetalladoB\",\"value\":\"" + ChonorariosDetallados + "\"},{\"Etiqueta\":\"Obviar Facturas y Recibos de \\\"Crédito\\\"\",\"Parametro\":\"iCreditoB\",\"value\":\"" + CobviarFacturasCredito + "\"},{\"Etiqueta\":\"Obviar Facturas y Recibos de \\\"Contado\\\"\",\"Parametro\":\"iContadoB\",\"value\":\"" + CobviarFacturasContado + "\"},{\"Etiqueta\":\"Incluir Debe?\",\"Parametro\":\"iDebeB\",\"value\":\"" + CincluirDebe + "\"},{\"Etiqueta\":\"Incluir Haber?\",\"Parametro\":\"iHaberB\",\"value\":\"" + CincluirHaber + "\"},{\"Etiqueta\":\"No mostrar partidas de aperturas\",\"Parametro\":\"iSinApertura\",\"value\":\"" + CNoMostrarApertura + "\"}]"
    
                    }
                })
            }
        }
    }
    </script>
    
    <style scoped>
    * {
        box-sizing: border-box;
        padding: 2px;
        margin: 0;
    }
    
    .container {
        display: grid;
        width: 100%;
        height: 100%;
        grid-gap: 5px;
        grid-template-areas: "a-lRmOp-0 a-lRmOp-0 a-6Wv60-0 a-6Wv60-0"
            "a-lRmOp-1 a-lRmOp-1 a-6Wv60-1 a-6Wv60-1";
        grid-template-columns: 1fr 1fr 1fr 1fr;
        grid-template-rows: 0.5fr 0.5fr;
    }
    
    .container>div {
        border: 1px solid #888;
    }
    
    .a-lRmOp-0 {
        grid-area: a-lRmOp-0;
    }
    
    .a-6Wv60-0 {
        grid-area: a-6Wv60-0;
    }
    
    .a-lRmOp-1 {
        grid-area: a-lRmOp-1;
    }
    
    .a-6Wv60-1 {
        grid-area: a-6Wv60-1;
    }
    
    .container {
        max-width: 100%;
    }
    
    .input-group {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        /* Ajusta según el espacio deseado */
    }
    </style>
    