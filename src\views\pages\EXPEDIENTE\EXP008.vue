<template>
<div>
    <DxTabPanel :height="'100%'" :data-source="tabs">
        <template #title="{ data: tab }">
            <span>{{ tab.name }}</span>
        </template>
        <template #item="{ data: tab }">
            <!-- Signos vitales -->
            <div v-if="tab.value === 1" class="tabpanel-item">
                <SignosVitales v-bind="$props" :horarios="horarios" :dateBoxOptions="dateBoxOptions" @onWeightButton="sendEmitterWeight" />
            </div>

            <!-- Ingesta -->
            <div v-else-if="tab.value === 2" class="tabpanel-item" :width="'100%'">
                <Ingesta v-bind="$props" :horarios="horarios" :dateBoxOptions="dateBoxOptions" />
            </div>

            <!-- Excreta -->
            <div v-else-if="tab.value === 3" class="tabpanel-item" :width="'100%'">
                <Excreta v-bind="$props" :horarios="horarios" :dateBoxOptions="dateBoxOptions" />
            </div>

            <!-- Balance -->
            <div v-else-if="tab.value === 4" class="tabpanel-item">
                <Balance v-bind="$props" :horarios="horarios" />
            </div>
        </template>
    </DxTabPanel>
</div>
</template>

<script>
import 'devextreme-vue/lookup'
import DxTabPanel from 'devextreme-vue/tab-panel';
import {
    DefaultDxGridConfiguration
} from './data'
import SignosVitales from './EXP017.vue'
import Ingesta from './EXP018.vue'
import Excreta from './EXP019.vue'
import Balance from './EXP020.vue'


export default {
    name: 'SIEB',
    components: {
        DxTabPanel,
        SignosVitales,
        Ingesta,
        Excreta,
        Balance
    },
    data() {
        return {
            tabs: [{
                name: 'Signos vitales',
                value: 1
            }, {
                name: 'Ingesta',
                value: 2
            }, {
                name: 'Excreta',
                value: 3
            }, {
                name: 'Balance',
                value: 4
            }],
            horarios: [],
            selectHorarios: {},
            DefaultDxGridConfiguration,
            dateBoxOptions: {
                max: this.formatToday(),
                value: new Date(),
            }
        }
    },
    props: {
        SerieAdmision: null,
        CodigoAdmision: null,
        NumeroExpediente: null,

        StatusAdmision: null,
    },
    methods: {
        Cargar() {},
        CargarHorarios() {
            this.axios.post('/app/v1_ExpedienteEvolucion/Busqueda_Horarios', {})
                .then(resp => {
                    this.horarios = {
                        items: resp.data.json.map(x => {
                            return {
                                hora: x.hora,
                                value: parseInt(x.idHorario),
                            }
                        }),
                        searchEnabled: true,
                        valueExpr: 'value',
                        displayExpr: 'hora',
                        value: new Date().getHours(),
                    }
                })
        },
        sendEmitterWeight() {
            this.$emit('onWeightButton', true);
        },
        formatToday(date = new Date()) {
            const year = date.toLocaleString('default', {
                year: 'numeric'
            });
            const month = date.toLocaleString('default', {
                month: '2-digit'
            });
            const day = date.toLocaleString('default', {
                day: '2-digit'
            });

            return [year, month, day].join('/');
        },
    },
    beforeMount() {
        this.CargarHorarios();
    },
    computed: {
        ShouldRender() {
            return Boolean(this.SerieAdmision) && Boolean(this.CodigoAdmision) && Boolean(this.NumeroExpediente)
        },
    },
}
</script>
