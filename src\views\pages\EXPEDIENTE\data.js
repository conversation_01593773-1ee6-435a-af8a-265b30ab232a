import axios from 'axios'
import Vue from 'vue'

export const tabs = [
    { name: 'From This Device', value: ['file'] },
    { name: 'From the Web', value: ['url'] },
    { name: 'Both', value: ['file', 'url'] },
  ];
  
export const markup = `
      <h2>
          <img src="images/widgets/HtmlEditor.svg" alt="HtmlEditor">
          Formatted Text Editor (HTML Editor)
      </h2>
      <br>
      <p>DevExtreme JavaScript HTML Editor is a client-side WYSIWYG text editor that allows its users to format textual and visual content and store it as HTML or Markdown.</p>
      <p>Supported features:</p>
      <ul>
          <li>Inline formats:
              <ul>
                  <li><strong>Bold</strong>, <em>italic</em>, <s>strikethrough</s> text formatting</li>
                  <li>Font, size, color changes (HTML only)</li>
              </ul>
          </li>
          <li>Block formats:
              <ul>
                  <li>Headers</li>
                  <li>Text alignment</li>
                  <li>Lists (ordered and unordered)</li>
                  <li>Code blocks</li>
                  <li>Quotes</li>
              </ul>
          </li>
          <li>Custom formats</li>
          <li>HTML and Markdown support</li>
          <li>Mail-merge placeholders (for example, %username%)</li>
          <li>Adaptive toolbar for working images, links, and color formats</li>
          <li>Image upload: drag-and-drop images onto the form, select files from the file system, or specify a URL.</li>
          <li>Copy-paste rich content (unsupported formats are removed)</li>
          <li>Tables support</li>
      </ul>
      <br>
      <p>Supported frameworks and libraries</p>
      <table>
          <tr>
              <td><strong>jQuery</strong></td>
              <td style="text-align: right;">v2.1 - v2.2 and v3.x</td>
          </tr>
          <tr>
              <td><strong>Angular</strong></td>
              <td style="text-align: right;">v7.0+</td>
          </tr>
          <tr>
              <td><strong>React</strong></td>
              <td style="text-align: right;">v16.2+</td>
          </tr>
          <tr>
              <td><strong>Vue</strong></td>
              <td style="text-align: right;">v2.6.3+</td>
          </tr>
      </table>
  `;
export const fieldLabelIconActuales = [
    {campo: 'AntUltimaComida', etiqueta: 'Última Comida', icono:'icon dx-icon-food'},
    {campo: 'AntAlergicos', etiqueta: 'Alérgicos', icono:'fas fa-bug'},
    {campo: 'AntMedicamentos', etiqueta: 'Medicamentos', icono:'fas fa-capsules'},
    {campo: 'AntGinecoGestas', etiqueta: 'Gestas', icono:''},
    {campo: 'AntGinecoAbortos', etiqueta: 'Abortos', icono:''},
    {campo: 'AntGinecoCesareas', etiqueta: 'Cesáreas', icono:''},
    {campo: 'AntGinecoPartos', etiqueta: 'Partos', icono:''},
    {campo: 'AntGinecoFUR', etiqueta: 'FUR', icono:''},
    {campo: 'AntMedicos', etiqueta: 'Médicos', icono:'fas fa-user-md'},
    {campo: 'AntQuirurgicos', etiqueta: 'Quirúrgicos', icono:'fas fa-procedures'},
    {campo: 'AntTraumaticos', etiqueta: 'Traumáticos', icono:'fas fa-band-aid'},
    {campo: 'AntOtros', etiqueta: 'Otros', icono:''},
    {campo: 'AntGinecologico', etiqueta: 'Ginecobstétricos', icono:'fas fa-baby'},
    {campo: 'AntHabitos', etiqueta: 'Habitos', icono:'fas fa-walking'},
    {campo: 'AntFamiliares', etiqueta: 'Familiares', icono:'fas fa-users'},
    {campo: 'AntSistemas', etiqueta: 'Sistemas', icono:'fas fa-assistive-listening-systems'},
]//ojo si cambia el orden verificar la propiedad computada antSel()
export const fieldLabelIconPrevios = [
    {campo: 'MotivoConsulta', etiqueta: 'Motivo de Consulta', icono:'fas fa-question'},
    {campo: 'HistoriaEnfermedad', etiqueta: 'Historia de la enfermedad', icono:'fas fa-notes-medical'},
    {campo: 'ProblemaEncontrado', etiqueta: 'Problema Encontrado', icono:'fas fa-search'},
    {campo: 'DiagnosticoIngreso', etiqueta: 'Diagnóstico de Ingreso', icono:'fas fa-stethoscope'},
    {campo: 'Seguro', etiqueta: 'Seguro', icono:'fas fa-user-shield'},
]
export const DefaultDxGridConfiguration = {
  visible: true,
  showRowLines: true,
  showColumnLines: true,
  showBorders: true,
  'load-panel': {enabled: false},
  selection: {mode: 'single'},
  searchPanel: {visible: true},
  focusedRowEnabled: false,
  rowAlternationEnabled: true,  
  columnHidingEnabled: true,
  hoverStateEnabled: true,
  width: '100%',
  height:'calc(100vh - 310px)',
  columnAutoWidth: true,
  allowColumnReordering: true,
  allowColumnResizing: true,
  columnResizingMode:'widget',
  headerFilter:{
    visible:true,
    allowSearch:true
  },
  wordWrapEnabled: true,
  paging:{ enabled:true, pageSize:10 },
  scrolling: {
    showScrollbar: 'always',
    useNative: false,
  },
  
}
export const visorDocumentos = 'C:\\Aplicaciones\\visualizadorfm.exe \\\\192.168.221.3\\dpersonal\\'
/**Mensajes en español para Devextreme */
export const CustomDevExtremeEsMessages = {
  "dxHtmlEditor-dialogColorCaption": "Cambiar el color de la fuente",
  "dxHtmlEditor-dialogBackgroundCaption": "Cambiar el color de fondo",
  "dxHtmlEditor-dialogLinkCaption": "Añadir enlace",
  "dxHtmlEditor-dialogLinkUrlField": "URL",
  "dxHtmlEditor-dialogLinkTextField": "Texto",
  "dxHtmlEditor-dialogLinkTargetField": "Abrir enlace en nueva ventana",
  "dxHtmlEditor-dialogImageCaption": "Añadir imagen",
  "dxHtmlEditor-dialogImageUrlField": "URL",
  "dxHtmlEditor-dialogImageAltField": "Texto alternativo",
  "dxHtmlEditor-dialogImageWidthField": "Anchura (px)",
  "dxHtmlEditor-dialogImageHeightField": "Altura (px)",
  "dxHtmlEditor-dialogInsertTableRowsField": "Filas",
  "dxHtmlEditor-dialogInsertTableColumnsField": "Columnas",
  "dxHtmlEditor-dialogInsertTableCaption": "Insertar Tabla",
  "dxHtmlEditor-dialogUpdateImageCaption": "Actualizar Imagen",
  "dxHtmlEditor-dialogImageUpdateButton": "Actualizar",
  "dxHtmlEditor-dialogImageAddButton": "Agregar",
  "dxHtmlEditor-dialogImageSpecifyUrl": "Desde la Web",
  "dxHtmlEditor-dialogImageSelectFile": "Desde este dispositivo",
  "dxHtmlEditor-dialogImageKeepAspectRatio": "Keep Aspect Ratio",
  "dxHtmlEditor-dialogImageEncodeToBase64": "Codificar en Base64",
  "dxHtmlEditor-heading": "Encabezamiento",
  "dxHtmlEditor-normalText": "Texto normal",
  "dxHtmlEditor-background": "Color de fondo",
  "dxHtmlEditor-bold": "Negrilla",
  "dxHtmlEditor-color": "Color de Fuente",
  "dxHtmlEditor-font": "Fuente",
  "dxHtmlEditor-italic": "Cursiva",
  "dxHtmlEditor-link": "Agregar Enlace",
  "dxHtmlEditor-image": "Agregar Imagen",
  "dxHtmlEditor-size": "Tamaño",
  "dxHtmlEditor-strike": "Tachado",
  "dxHtmlEditor-subscript": "Subscript",
  "dxHtmlEditor-superscript": "Superscript",
  "dxHtmlEditor-underline": "Subrayado",
  "dxHtmlEditor-blockquote": "Entre Comillas",
  "dxHtmlEditor-header": "Encabezado",
  "dxHtmlEditor-increaseIndent": "Agregar Identación",
  "dxHtmlEditor-decreaseIndent": "Quitar Identación",
  "dxHtmlEditor-orderedList": "Lista Ordenada",
  "dxHtmlEditor-bulletList": "Bullet List",
  "dxHtmlEditor-alignLeft": "Alinear a la Izquierda",
  "dxHtmlEditor-alignCenter": "Alinear al Centro",
  "dxHtmlEditor-alignRight": "Alinear a la Derecha",
  "dxHtmlEditor-alignJustify": "Justificar",
  "dxHtmlEditor-codeBlock": "Code Block",
  "dxHtmlEditor-variable": "Agregar Variable",
  "dxHtmlEditor-undo": "Deshacer",
  "dxHtmlEditor-redo": "Rehacer",
  "dxHtmlEditor-clear": "Limpiar Formato",
  "dxHtmlEditor-insertTable": "Insertar Tabla",
  "dxHtmlEditor-insertHeaderRow": "Insert Header Row",
  "dxHtmlEditor-insertRowAbove": "Insertar Fila Arriba",
  "dxHtmlEditor-insertRowBelow": "Insertar Fila Debajo",
  "dxHtmlEditor-insertColumnLeft": "Insertar Columna Izquierda",
  "dxHtmlEditor-insertColumnRight": "Insertar Columna Derecha",
  "dxHtmlEditor-deleteColumn": "Borrar Columna",
  "dxHtmlEditor-deleteRow": "Borrar Fila",
  "dxHtmlEditor-deleteTable": "Borrar Tabla",
  "dxHtmlEditor-cellProperties": "Propiedades de Celda",
  "dxHtmlEditor-tableProperties": "Propiedades de Tabla",
  "dxHtmlEditor-insert": "Insertar",
  "dxHtmlEditor-delete": "Borrar",
  "dxHtmlEditor-border": "Borde",
  "dxHtmlEditor-style": "Estilo",
  "dxHtmlEditor-width": "Ancho",
  "dxHtmlEditor-height": "Alto",
  "dxHtmlEditor-borderColor": "Color",
  "dxHtmlEditor-tableBackground": "Background",
  "dxHtmlEditor-dimensions": "Dimensionsiones",
  "dxHtmlEditor-alignment": "Alineación",
  "dxHtmlEditor-horizontal": "Horizontal",
  "dxHtmlEditor-vertical": "Vertical",
  "dxHtmlEditor-paddingVertical": "Vertical Padding",
  "dxHtmlEditor-paddingHorizontal": "Horizontal Padding",
  "dxHtmlEditor-pixels": "Pixeles",
  "dxHtmlEditor-list": "Lista",
  "dxHtmlEditor-ordered": "Ordered",
  "dxHtmlEditor-bullet": "Bullet",
  "dxHtmlEditor-align": "Align",
  "dxHtmlEditor-center": "Center",
  "dxHtmlEditor-left": "Left",
  "dxHtmlEditor-right": "Right",
  "dxHtmlEditor-indent": "Indent",
  "dxHtmlEditor-justify": "Justify",
}

const menuenbledamd = function(admData) {
    return ((!admData.SerieAdmision && !admData.CodigoAdmision) || admData.NumeroExpediente === '0') ? true : false
}
const menuenbledexp = function(admData) {
    return (!admData.SerieAdmision && !admData.CodigoAdmision) ? true : false
}
  
export const navigation = [{
    id: 1,
    title: "Antecedentes personales",
    text: "Antecedentes personales",
    icon: "address-card",
    path: "EXPEDIENTE/EXP333#EXP003",
    animation: "",
    disabled: menuenbledamd
},
{
    id: 2,
    title: "Diagnósticos",
    text: "Diagnósticos",
    icon: "laptop-medical",
    path: "",
    animation: "",
    disabled: menuenbledamd
},
{
    id: 3,
    title: "Evolución",
    text: "Evolución",
    icon: "arrow-up-right-dots",
    path: "",
    animation: "",
    disabled: menuenbledamd
},
{
    id: 4,
    title: "Órdenes médicas",
    text: "Órdenes médicas",
    icon: "clipboard-list",
    path: "",
    animation: "",
    disabled: menuenbledamd
},
{
    id: 5,
    title: "Control de órdenes",
    text: "Control de órdenes",
    icon: "clipboard-check",
    path: "",
    animation: "",
    disabled: menuenbledamd
},
{
    id: 6,
    title: "Signos vitales, ingesta y excreta",
    text: "Signos vitales",
    icon: "file-waveform",
    path: "",
    animation: "",
    disabled: menuenbledamd
},
{
    id: 7,
    title: "Dieta",
    text: "Dieta",
    icon: "utensils",
    path: "",
    animation: "",
    disabled: menuenbledamd
},
{
    id: 8,
    title: "Salud siempre",
    text: "Salud siempre",
    icon: "heart-pulse",
    path: "",
    animation: "",
    disabled: menuenbledamd
},
{
    id: 9,
    title: "Plan de egreso",
    text: "Plan de egreso",
    icon: "person-walking-arrow-right",
    path: "",
    animation: "",
    disabled: menuenbledamd
},
{
    id: 10,
    title: "Sala de operaciones",
    text: "SOP",
    icon: "bed-pulse",
    path: "",
    animation: "",
    disabled: menuenbledamd
    // submenu: [
    //     {
    //         id: 11,
    //         title: "Órdenes",
    //         text: "Órdenes",
    //         icon: "house-medical",
    //         path: "",
    //         animation: ""
    //     },
    //     {
    //         id: 12,
    //         title: "Anestesia preoperatoria",
    //         text: "Anestesia preoperatoria",
    //         icon: "bed",
    //         path: "",
    //         animation: ""
    //     },
    //     {
    //         id: 13,
    //         title: "Anestesia datos generales",
    //         text: "Anestesia datos generales",
    //         icon: "hospital-user",
    //         path: "",
    //         animation: ""
    //     },
    //     {
    //         id: 14,
    //         title: "Registro anestésico",
    //         text: "Registro anestésico",
    //         icon: "house-medical-circle-check",
    //         path: "",
    //         animation: ""
    //     },
    //     {
    //         id: 15,
    //         title: "Registro transoperatorio",
    //         text: "Registro transoperatorio",
    //         icon: "heartbeat",
    //         path: "",
    //         animation: ""
    //     },
    //     {
    //         id: 16,
    //         title: "Recuento de compresas y otros materiales",
    //         text: "Materiales",
    //         icon: "bandage",
    //         path: "",
    //         animation: ""
    //     },
    //     {
    //         id: 17,
    //         title: "Test de aldrete",
    //         text: "Test de aldrete",
    //         icon: "user-clock",
    //         path: "",
    //         animation: ""
    //     },
    //     {
    //         id: 18,
    //         title: "Estadística",
    //         text: "Estadística",
    //         icon: "arrow-trend-up",
    //         path: "",
    //         animation: ""
    //     },
    //     {
    //         id: 19,
    //         title: "Récord operatorio",
    //         text: "Récord operatorio",
    //         icon: "notes-medical",
    //         path: "",
    //         animation: ""
    //     },]
},
{
    id: 20,
    title: "Resultados de laboratorio",
    text: "Resultados laboratorio",
    icon: "vial-circle-check",
    path: "",
    animation: "",
    disabled: menuenbledexp
},
{
    id: 21,
    title: "Exámenes",
    text: "Exámenes",
    icon: "radiation",
    path: "",
    animation: "",
    disabled: menuenbledexp
},
{
    id: 22,
    title: "Documentos adjuntos",
    text: "Documentos adjuntos",
    icon: "folder-plus",
    path: "",
    animation: "",
    disabled: menuenbledamd
},
{
    id: 23,
    title: "Consulta externa",
    text: "CoEx",
    icon: "book-medical",
    path: "",
    animation: "",
    disabled: menuenbledexp
},
{
    id: 24,
    title: "Hospitalizaciones previas",
    text: "Hospitalizaciones previas",
    icon: "user-injured",
    path: "",
    animation: "",
    disabled: menuenbledamd
},
{
    id: 25,
    title: "",
    text: "Peso y altura",
    icon: "weight-scale",
    path: "",
    animation: "",
    disabled: menuenbledamd
},
{
    id: 26,
    title: "Catéteres",
    text: "Cateterismo",
    icon: "syringe",
    path: "",
    animation: "",
    disabled: menuenbledamd
},
{
    id: 27,
    title: "",
    text: "Estadísticas SOP",
    icon: "arrow-trend-up",
    path: "",
    animation: "",
    disabled: ()=>{return false}
},

];
export function ObtenerPlantilla (tipo, okCallback, failCallback = ()=>{}){
  axios.post("/app/v1_ExpedienteEvolucion/BusquedaPlantillasExpediente", {
      TipoPlantilla: tipo
    })
    .then(resp => {
        if (resp.data.codigo == 0) {
          okCallback( Vue.prototype.$limpiar_saltos_tabulares( resp.data.json[0].Plantilla ) )
        }
    }).catch((error)=> {
      failCallback(error)
    })
} 

export const popupConf = {
    showTitle: true,
    maxWidth:'90vw', 
    maxHeight:'95vh',
    showCloseButton: true,
    position: { 
        my: 'center', 
        at: 'center', 
        of: 'window', 
        offset: {
            x:50, y:0
        }
    },
    wrapperAttr:{
        class:'expediente-popup dx-color-scheme-light'
    }
}