/*=========================================================================================
  File Name: store.js
  Description: Vuex store
  ----------------------------------------------------------------------------------------
  Item Name: Vuexy - Vuejs, HTML & Laravel Admin Dashboard Template
  Author: Pixinvent
  Author URL: http://www.themeforest.net/user/pixinvent
==========================================================================================*/


import Vue from 'vue'
import Vuex from 'vuex'
import state from "./state"
import getters from "./getters"
import mutations from "./mutations"
import actions from "./actions"
Vue.use(Vuex)
import WorkFlowModule from './workflow/WorkFlowModule'
import modulePaciente from './paciente/modulePaciente'
import moduleAdmisiones from './Admisiones/moduleAdmisiones'
export default new Vuex.Store({
    getters,
    mutations,
    state,
    actions,
    modules: {
       workflow : WorkFlowModule,
       paciente: modulePaciente,
       admision:moduleAdmisiones
    },
    strict: false
    //Se comenta para evitar el deep nofity por event handler de VUEX
    //----------------------------------------------------------------
    //strict: process.env.NODE_ENV !== 'production'
})