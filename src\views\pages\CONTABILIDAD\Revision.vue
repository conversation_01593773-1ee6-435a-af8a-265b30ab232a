<template>
<div>
    <form @submit="handleSubmit">
        <DxForm :ref="formConsulta" :form-data.sync="formulario" label-mode="floating">
            <DxFormGroupItem :col-count="2">
                <DxFormGroupItem>
                    <DxFormItem data-field="Fecha" editor-type="dxDateBox" :validationRules="[{ type: 'required' }]" :editor-options="editorOptionsFecha" />
                    <DxFormItem data-field="Cuenta" editor-type="dxSelectBox" :editor-options="{ width: 'auto', searchEnabled: true, items: cuentas,  displayExpr: 'Valor', valueExpr: 'Codigo' }" :validationRules="[{ type: 'required' }]" />
                </DxFormGroupItem>
                <DxFormGroupItem>
                    <DxFormItem data-field="ChequeInicial" editor-type="dxNumberBox" :editor-options="{ min: 0, format: '#0', step: 0 }">
                        <DxFormLabel text="Del número" />
                    </DxFormItem>
                    <DxFormItem data-field="ChequeFinal" editor-type="dxNumberBox" :editor-options="{ min: 0, format: '#0', step: 0 }">
                        <DxFormLabel text="Al número" />
                    </DxFormItem>
                </DxFormGroupItem>
                <DxFormButtonItem :col-span="2" :button-options="buttonConsultar" name="Consultar" horizontal-alignment="center" verical-alignment="center" />
            </DxFormGroupItem>
            <DxFormGroupItem :visible="infoCheque.Numero != null" class="pt-4" caption=" ">
                <DxFormItem template="navegacion" />
                <DxFormItem :visible="infoCheque.Numero != null" template="infoCheque" />
            </DxFormGroupItem>

            <template #navegacion="{}">
                <div class="navigation-buttons flex flex-row">
                    <vs-button class="ml-1 text-lg" type="filled" color="rgb(110, 110, 110)" size="small" @click="BuscarCheques(chequeInicial - 1, 1)">
                        <vs-tooltip text="Primero">
                            <font-awesome-icon :icon="['fas', 'backward-step']" />
                        </vs-tooltip>
                    </vs-button>
                    <vs-button class="ml-1 text-xl" type="filled" color="rgb(110, 110, 110)" size="small" @click="BuscarCheques(chequeActual, chequeActual == chequeInicial ? 0 : -1)">
                        <vs-tooltip text="Anterior">
                            <font-awesome-icon :icon="['fas', 'caret-left']" />
                        </vs-tooltip>
                    </vs-button>
                    <vs-button class="ml-1 text-xl" type="filled" color="rgb(110, 110, 110)" size="small" @click="BuscarCheques(chequeActual, chequeActual == chequeFinal ? 0 : 1)">
                        <vs-tooltip text="Siguiente">
                            <font-awesome-icon :icon="['fas', 'caret-right']" />
                        </vs-tooltip>
                    </vs-button>
                    <vs-button class="ml-1 text-lg" type="filled" color="rgb(110, 110, 110)" size="small" @click="BuscarCheques(chequeFinal + 1, -1)">
                        <vs-tooltip text="Último">
                            <font-awesome-icon :icon="['fas', 'forward-step']" />
                        </vs-tooltip>
                    </vs-button>

                    <div v-if="formularioCheque.Status !== 'A' && formularioCheque.Impresion == null && editarFormulario == false">
                        <DxButton width="auto" height="36px" type="success" class="p-1 ml-1" styling-mode="contained" @click="ModificarCheque">
                            <font-awesome-icon class="mr-2" :icon="['fas', 'edit']" style="font-size: 18px; vertical-align: middle" />
                            <span>Modificar</span>
                        </DxButton>
                    </div>

                    <div v-if="formularioCheque.Status !== 'A' && formularioCheque.Impresion == null && editarFormulario == true">
                        <DxButton width="auto" height="36px" type="success" class="p-1 ml-1" styling-mode="contained" @click="AceptarModificacion">
                            <font-awesome-icon class="mr-2" :icon="['fas', 'edit']" style="font-size: 18px; vertical-align: middle" />
                            <span>Aceptar</span>
                        </DxButton>
                        <DxButton width="auto" height="36px" type="danger" class="p-1 ml-1" styling-mode="contained" @click="CancelarModificacion">
                            <font-awesome-icon class="mr-2" :icon="['fas', 'edit']" style="font-size: 18px; vertical-align: middle" />
                            <span>Cancelar</span>
                        </DxButton>
                    </div>
                </div>
            </template>

            <template #infoCheque="{}">
                <div class="pt-4">
                    <DxForm :ref="formCheque" :form-data.sync="formularioCheque" label-mode="floating">
                        <DxFormGroupItem :col-count="2">

                            <DxFormGroupItem :col-count="3">
                                <DxFormItem data-field="Numero" editor-type="dxNumberBox" :editor-options="{ readOnly: true }">
                                    <DxFormLabel text="Número" />
                                </DxFormItem>
                                <DxFormItem data-field="Fecha" editor-type="dxDateBox" :editor-options="{ readOnly: true, dataType: 'date', displayFormat: 'dd/MM/yyyy' }" />
                                <DxFormItem data-field="Monto" editor-type="dxNumberBox" :editor-options="{ readOnly: true, format: '#0.00', step: 0.01 }" />
                                <DxFormItem :col-span="1" data-field="Proveedor" editor-type="dxTextBox" :editor-options="{ readOnly: true, }" />
                                <DxFormItem :col-span="2" data-field="NombreBeneficiario" editor-type="dxTextBox" max-length="80" :editor-options="{ readOnly: !editarFormulario, maxLength: 80 }">
                                    <DxFormLabel text="Pagar a la orden de" />
                                </DxFormItem>
                                <DxFormItem :col-span="3" data-field="Observaciones" editor-type="dxTextArea" :editor-options="{ readOnly: !editarFormulario, height: '100%', }" />
                            </DxFormGroupItem>

                            <DxFormGroupItem :col-count="3">
                                <DxFormGroupItem>
                                    <DxFormItem data-field="Entregado" editor-type="dxDateBox" :editor-options="{ readOnly: true, dataType: 'date', displayFormat: 'dd/MM/yyyy' }" />
                                    <DxFormItem data-field="Recibo" editor-type="dxNumberBox" :editor-options="{ readOnly: true }" />
                                </DxFormGroupItem>
                                <DxFormGroupItem>
                                    <DxFormItem data-field="FechaConcil" editor-type="dxDateBox" :editor-options="{ readOnly: true, dataType: 'date', displayFormat: 'dd/MM/yyyy' }">
                                        <DxFormLabel text="Fecha conciliación" />
                                    </DxFormItem>
                                    <DxFormItem data-field="Conciliado" editor-type="dxCheckBox" :editor-options="{ readOnly: true }" />
                                </DxFormGroupItem>

                                <DxFormGroupItem>
                                    <DxFormItem data-field="DescripcionPeriodo" editor-type="dxTextBox" :editor-options="{ readOnly: true }">
                                        <DxFormLabel text="Periodo" />
                                    </DxFormItem>
                                    <DxFormItem data-field="NoNegociable" editor-type="dxCheckBox" :editor-options="{ readOnly: !editarFormulario }">
                                        <DxFormLabel text="No negociable" />
                                    </DxFormItem>
                                </DxFormGroupItem>
                                <DxFormGroupItem :col-span="3">
                                    <DxFormItem template="textoEstado" :col-span="3" />
                                </DxFormGroupItem>
                            </DxFormGroupItem>

                        </DxFormGroupItem>

                        <template #textoEstado="{}">
                            <div>
                                <span style="color: red; font-size: 14px; font-weight: bold;" v-if="formularioCheque.Status == 'A'">**ANULADO**</span>
                                <span style="color: blue; font-size: 14px; font-weight: bold;" v-if="formularioCheque.Impresion != null && formularioCheque.Status != 'A'">**IMPRESO**</span>
                                <span style="color: blue; font-size: 14px; font-weight: bold;" v-if="formularioCheque.Impresion == null && formularioCheque.Status != 'A'">**PENDIENTE DE IMPRESIÓN**</span>
                                <br>
                            </div>
                        </template>
                    </DxForm>
                    <div class="pt-4">
                        <DxDataGrid :ref="gridDetalle" v-bind="DefaultDxGridConfiguration" :data-source="valoresTabla" :headerFilter="{ visible: false, allowSearch: false }" :searchPanel="{ visible: false }" :width="'100%'" height="auto" @editor-preparing="editorDatagrid" :on-saving="GuardaCambiosTabla">
                            <DxDataGridToolbar>
                                <DxFormItem location="after" template="vistoBueno" />
                                <DxFormItem name="addRowButton" />
                                <DxFormItem name="saveButton" />
                                <DxFormItem name="revertButton" />
                            </DxDataGridToolbar>
                            <DxDataGridSelection mode="single" />
                            <DxDataGridEditing :allow-updating="mostrarBotones && editarRevision" :allow-adding="mostrarBotones && editarRevision" :allow-deleting="mostrarBotones && editarRevision" mode="batch" :use-icons="true" />

                            <DxDataGridColumn width="10%" data-field="Documento" alignment="center" data-type="number" />
                            <DxDataGridColumn width="30%" data-field="Detalle" alignment="center" data-type="string" />
                            <DxDataGridColumn width="10%" data-field="Cuenta" alignment="center" data-type="string" />
                            <DxDataGridColumn width="30%" data-field="Nombre" alignment="center" data-type="string" />
                            <!-- <DxDataGridColumn width="30%" data-field="Nombre" caption="Nombre de la cuenta" alignment="center" data-type="string">
                                <DxDataGridLookup :dataSource="cuentasAuxiliarDataSource" value-expr="Nombre" display-expr="Nombre" :show-cancel-button="false" :show-clear-button="true" :search-enabled="true" drop-down-options="dropDownOptions" />
                                <DxDataGridValidationRule type="required" message="Debe seleccionar una cuenta" />
                            </DxDataGridColumn> -->
                            <DxDataGridColumn width="10%" data-field="Debe" alignment="center" data-type="number" :customize-text="customizeTextValores" :editor-options="editorMontos" :value-format="{ type: 'fixedPoint', precision: 2 }" />
                            <DxDataGridColumn width="10%" data-field="Haber" alignment="center" data-type="number" :customize-text="customizeTextValores" :editor-options="editorMontos" :value-format="{ type: 'fixedPoint', precision: 2 }" />
                            <DxDataGridSummary :recalculate-while-editing="true">
                                <DxDataGridTotalItem column="Debe" summary-type="sum" :customize-text="customizeTextValores" />
                                <DxDataGridTotalItem column="Haber" summary-type="sum" :customize-text="customizeTextValores" />
                            </DxDataGridSummary>

                            <template #vistoBueno>
                                <div>
                                    <DxButton :disabled="!editarRevision" :visible="formularioCheque.Revisado == 'S'" width="36px" height="36px" type="success" styling-mode="contained" @click="VistoBuenoAuxiliar">
                                        <font-awesome-icon :icon="['fas', 'lock']" style="font-size: 18px; vertical-align: middle" />
                                    </DxButton>
                                    <DxButton :disabled="!editarRevision" :visible="formularioCheque.Revisado == 'N'" width="36px" height="36px" type="success" styling-mode="contained" @click="VistoBuenoAuxiliar">
                                        <font-awesome-icon :icon="['fas', 'lock-open']" style="font-size: 18px; vertical-align: middle" />
                                    </DxButton>
                                </div>
                            </template>
                        </DxDataGrid>
                    </div>
                </div>
            </template>
        </DxForm>
    </form>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration,
    customizeTextValores
} from './data'

const formConsulta = 'formConsulta'
const formCheque = 'formCheque'
const gridDetalle = 'gridDetalle'

export default {
    name: 'Revision',
    components: {},
    data() {
        return {
            DefaultDxGridConfiguration,
            formConsulta,
            formCheque,
            gridDetalle,

            formulario: {
                Fecha: null,
                Cuenta: null, // Codigo de la cuenta de banco de 2 dígitos
                NumeroCuenta: null, // Número de la cuenta bancaria
                BancoContabilidad: null, // Cuenta contable de la cuenta bancaria seleccionada
                ChequeInicial: null, // Primer cheque del rango a imprimir
                ChequeFinal: null, // Último cheque del rango a imprimir
                NombreCuentaBanco: null, // Nombre de la cuenta contable de la cuenta bancaria 
            },
            cuentas: [],

            buttonConsultar: {
                width: 'auto',
                icon: 'fas fa-magnifying-glass-dollar',
                text: 'Consultar',
                type: 'success',
                onClick: () => {
                    this.chequeInicial = this.formulario.ChequeInicial
                    this.chequeFinal = this.formulario.ChequeFinal
                    this.BuscarCheques(this.chequeInicial - 1, 1)
                },
                useSubmitBehavior: false,
            },

            cheques: [],

            //Solo se asigna con status para que no cause error al validarlo cuando se cargan los cheques para mostrar el botón de anular
            infoCheque: {
                Status: null
            },
            periodo: null,
            cuentaIVACobrar: null,
            porcentajeIVA: null,
            retencion: null,
            indiceSeleccionado: null, //Variable para saber cual es el índice en la tabla de cheques del cheque seleccionado

            formularioCheque: {
                DescripcionPeriodo: null
            },
            valoresTabla: null,
            periodos: null,

            editarFormulario: false, // Variable para saber si se puede ditar el formulario del cheque 

            proveedores: [],
            proveedoresDataSource: {
                paginate: true,
                pageSize: 10,
                sort: 'Codigo',
                key: 'Codigo',
                // loadMode: 'raw',
                byKey: (e) => {
                    return this.proveedores.find(x => x.Codigo === e)
                },
                load: (e) => {
                    if (e.take) {
                        if (e.skip === e.take) {
                            e.take = e.take + 10
                        }
                    }
                    return this.$filtrado_avanzado_palabras(e.searchValue, this.proveedores, 'Display', e.skip, e.skip + e.take)
                }
            },
            dropDownOptions: {
                showTitle: false,
                hideOnOutsideClick: true,
            },

            infoInicialCheque: null, //Variable para guarda la información del cheque antes de la edición, para restaurar la información en dado caso se cancele la edición
            mostrarBotones: false,

            agregarAuxiliar: null, //Variable para almacenar las nuevas líneas del auxiliar de bancos
            eliminarAuxiliar: null, //Variable para almacenar las líneas a eliminar del auxiliar de bancos

            cuentasAuxiliar: [],
            cuentasAuxiliarDataSource: {
                paginate: true,
                pageSize: 10,
                sort: 'Nombre',
                key: 'Codigo',
                // loadMode: 'raw',
                byKey: (e) => {
                    return this.cuentasAuxiliar.find(x => x.Codigo === e)
                },
                load: (e) => {
                    if (!e.searchValue && !e.take)
                        return this.cuentasAuxiliar
                    if (e.take) {
                        if (e.skip === e.take) {
                            e.take = e.take + 10
                        }
                    }
                    return this.$filtrado_avanzado_palabras(e.searchValue, this.cuentasAuxiliar, 'Display', e.skip, e.skip + e.take)
                }
            },

            inicialValoresTabla: null,

            editarRevision: false, //Variable para indicar si el usuario tiene el permiso para editar el auxiliar de banco

            indiceAuxiliar: 0, //Variable para guardar el índice maximo del auxiliar de banco que se cargó y poder agregar nuevos indices empezando desde 1 más al que carge aquí

            editorMontos: {
                min: 0,
                format: '#0.##',
                step: 0.01
            },

            editorOptionsFecha: {
                dataType: 'date',
                displayFormat: 'dd/MM/yyyy',
                max: new Date()
            },

            chequeInicial: null,
            chequeFinal: null,
            chequeActual: null
        }
    },
    props: {
        ModalidadPago: null,
        TipoCheque: null,
        // ImpresionLotes: null,
        Corporativo: null
    },
    methods: {
        customizeTextValores,
        handleSubmit(e) {
            e.preventDefault()
        },

        CargarCuentas() {
            this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 1
                })
                .then(resp => {
                    this.cuentas = resp.data.json

                    this.formulario.Cuenta = this.cuentas[0].Codigo
                })
        },

        async CargarCuentasAuxiliar() {
            await this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 11
                })
                .then(resp => {
                    this.cuentasAuxiliar = resp.data.json.map((x) => {
                        return {
                            Codigo: x.Codigo,
                            Nombre: x.Nombre,
                            Display: x.Codigo + ' - ' + x.Nombre
                        }
                    })
                })
        },

        ValidarCheque(cheque) {
            if (cheque !== null && cheque >= 0) {
                return true
            }
            return false
        },

        editorDatagrid(e) {
            if (e.parentType === 'dataRow' && e.dataField === 'Nombre') {
                let row = null
                row = e.row.data

                e.editorOptions = {
                    ...e.editorOptions,
                    onSelectionChanged: (f) => {
                        if (row.__KEY__) //Si la variable __KEY__ existe quiere decir que es una fila nueva
                        {
                            e.row.data.Cuenta = f.selectedItem.Codigo
                            e.row.data.Nombre = f.selectedItem.Nombre

                        } else {
                            for (let i = 0; i < this.valoresTabla.length; i++) {
                                if (this.valoresTabla[i].Indice == row.Indice) {
                                    this.valoresTabla[i].Cuenta = f.selectedItem.Codigo
                                    this.valoresTabla[i].Nombre = f.selectedItem.Nombre
                                }
                            }
                        }
                    },
                }
            }
            if (e.parentType === 'dataRow' && e.dataField === 'Cuenta') {
                const defaultValueChangeHandler = e.editorOptions.onValueChanged;

                e.editorOptions.onValueChanged = (args) => {
                    const cuenta = args.value; // Captura el valor ingresado de la cuenta
                    const updatedNombre = this.cuentasAuxiliar.find((x) => x.Codigo == cuenta).Nombre
                    const rowIndex = e.row.rowIndex;
                    // Actualizamos la celda de "Nombre"
                    this.$refs.gridDetalle.instance.cellValue(
                        rowIndex,
                        "Nombre",
                        updatedNombre
                    );
                    // Actualizamos la fila para reflejar los cambios
                    this.$refs.gridDetalle.instance.refresh();
                    defaultValueChangeHandler(args);
                }
            }

            if (e.parentType === 'dataRow' && e.dataField === 'Nombre') {
                e.editorOptions.disabled = true
            }
        },

        ValidarMontos(e) {
            const value = e.value;

            // Verificar si el valor es un número y es positivo
            if (value <= 0 || isNaN(value)) {
                e.isValid = false;
                e.errorText = "El valor debe ser un número positivo.";
                return;
            }

            // Verificar si el valor tiene exactamente 2 decimales
            const regex = /^\d+(\.\d{1,2})?$/; // Expresión regular para números con hasta 2 decimales
            if (!regex.test(value)) {
                e.isValid = false;
                e.errorText = "El valor debe tener hasta dos decimales.";
                return;
            }

            // Si pasa ambas validaciones, es válido
            e.isValid = true;
        },

        async CargarPeriodos() {
            await this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 3,
                })
                .then(resp => {
                    this.periodos = resp.data.json
                })
        },

        async BuscarCheques(cheque, direccion) {
            await this.axios.post('/app/v1_bancos/ConsultaAnulacion', {
                    Opcion: 1,
                    Cuenta: this.formulario.Cuenta,
                    TipoCheque: this.TipoCheque,
                    Fecha: this.formulario.Fecha,
                    ChequeInicial: cheque,
                    ChequeFinal: cheque,
                    Direccion: direccion
                })
                .then(async resp => {
                    if (resp.data.json.length > 0) {
                        //Validación para no recargar información si la información del cheque sale del rango indicado
                        if (resp.data.json[0].Numero >= this.chequeInicial && resp.data.json[0].Numero <= this.chequeFinal) {
                            this.infoCheque = {}
                            this.formularioCheque = {}

                            this.infoCheque = resp.data.json[0]
                            this.formularioCheque = resp.data.json[0]

                            this.chequeActual = this.infoCheque.Numero

                            this.editarFormulario = false // Asigna falso esta variable para que, si estaba editando un cheque, los campos se deshabiliten

                            this.formularioCheque.NoNegociable = this.formularioCheque.NoNegociable == 'S' ? true : false
                            this.formularioCheque.Conciliado = this.formularioCheque.Conciliado == 'S' ? true : false

                            let descripcion = null
                            descripcion = this.periodos.filter(item => item.Codigo == this.formularioCheque.Periodo)

                            this.formularioCheque.DescripcionPeriodo = descripcion[0].Descripcion

                            if (this.formularioCheque.Revisado == 'S') {
                                this.mostrarBotones = false
                            } else {
                                this.mostrarBotones = true
                            }

                            // this.indiceSeleccionado = e.rowIndex

                            await this.BuscarOrden(this.formularioCheque)
                        }

                    }
                })
        },

        async BuscarOrden(e) {
            await this.axios.post('/app/v1_bancos/ConsultaAnulacion', {
                    Opcion: 3,
                    EmpresaCheque: e.Empresa,
                    Cuenta: e.Cuenta,
                    TipoCheque: e.Tipo,
                    ChequeInicial: e.Numero,
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.valoresTabla = resp.data.json
                        // this.inicialValoresTabla = resp.data.json // Se realiza una copia de los valores iniciales para utilizalos y comparar con los cambios que se puedan realizar

                        for (let i = 0; i < this.valoresTabla.length; i++) {
                            if (this.valoresTabla[i].Indice > this.indiceAuxiliar) {
                                this.indiceAuxiliar = this.valoresTabla[i].Indice
                            }
                        }
                    }
                })
        },

        async BuscarProveedores() {
            await this.axios.post('/app/v1_bancos/Proveedores', {
                    Opcion: 4
                })
                .then(resp => {
                    this.proveedores = resp.data.json.map((x) => {
                        return {
                            Codigo: x.Codigo,
                            Nombre: x.Nombre,
                            Nit: x.Nit,
                            Retencion: parseFloat(x.Retencion).toFixed(2),
                            TipoRetencion: x.TipoRetencion,
                            CuentaRetencion: x.CuentaRetencion,
                            ContaAnticipo: x.ContaAnticipo,
                            NombreCheque: x.NombreCheque,
                            CuentaBanco: x.CuentaBanco,
                            Display: x.Nit + ' - ' + x.Nombre + ' - ' + x.Codigo
                        }
                    })
                })
        },

        ConsultarPeriodo() {
            this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 1,
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.periodo = resp.data.json[0].Codigo
                    }
                })
        },

        async ConsultarSaldoFinal(e) {
            return await this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 2,
                    Cuenta: e.Cuenta,
                    Periodo: e.Periodo
                })
                .then(resp => {
                    if (resp.data.json.length === 0) {
                        return false
                    } else if (resp.data.json[0].SaldoFinal !== null) {
                        return true
                    }
                })
        },

        async CargarCuentasDefault() {
            await this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 9
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.cuentaIVACobrar = resp.data.json[0].IVAPorCobrar
                    }
                })
        },
        CargarOpciones() {
            this.axios.post('/app/v1_bancos/Opciones', {
                    Opcion: 1
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.porcentajeIVA = parseFloat(resp.data.json[0].IVA / 100).toFixed(2)
                    }
                })
        },

        async CargarRetencion(e) {
            await this.axios.post('/app/v1_bancos/ConsultaAnulacion', {
                    Opcion: 4,
                    EmpresaCheque: e.Empresa,
                    ChequeInicial: e.Numero,
                    TipoCheque: e.Tipo,
                    Cuenta: e.Cuenta
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.retencion = resp.data.json[0].Monto
                    }
                })
        },

        ModificarCheque() {
            this.editarFormulario = true
            this.infoCheque = {
                ...this.formularioCheque
            }
        },

        async AceptarModificacion() {
            await this.axios.post('/app/v1_bancos/Revision', {
                    Opcion: 5,
                    Observaciones: this.formularioCheque.Observaciones,
                    NoNegociable: this.formularioCheque.NoNegociable == true ? 'S' : 'N',
                    CuentaBanco: this.formularioCheque.Cuenta,
                    Tipo: this.formularioCheque.Tipo,
                    Referencia: this.formularioCheque.Numero,
                    Beneficiario: this.formularioCheque.NombreBeneficiario
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.editarFormulario = false
                    }
                })
        },

        CancelarModificacion() {
            this.editarFormulario = false

            this.formularioCheque = {
                ...this.infoCheque
            }
        },

        async VistoBuenoAuxiliar() {
            if (this.formularioCheque.Impresion == null && this.formularioCheque.Status == 'P') {
                if (this.formularioCheque.Revisado == 'S') {
                    await this.ModificarVistoBueno('N')
                } else {
                    await this.ModificarVistoBueno('S')
                }
            } else {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'Póliza',
                    acceptText: 'Aceptar',
                    text: 'No se puede reacceder a la póliza debido a que el cheque está anulado o impreso.',
                    buttonCancel: 'border',
                    accept: () => {
                        return true
                    },
                })
                return
            }
        },

        async ModificarVistoBueno(estado) {
            await this.axios.post('/app/v1_bancos/Revision', {
                    Opcion: 4,
                    Estado: estado,
                    CuentaBanco: this.formularioCheque.Cuenta,
                    Tipo: this.formularioCheque.Tipo,
                    Referencia: this.formularioCheque.Numero,
                    Usuario: this.Corporativo
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.BuscarCheques(this.chequeActual - 1, 1)
                    }
                })
        },

        async GuardaCambiosTabla(e) {
            let revision = {
                Agregados: [],
                Eliminados: [],
                Modificados: []
            }

            let valid = true;

            if (e.changes.length > 0) {
                for (let i = 0; i < e.changes.length; i++) {
                    if ((e.changes[i].type === "insert" || e.changes[i].type === "update") && ((!e.changes[i].data.Debe && !e.changes[i].data.Haber) && (!e.changes[i].key.Debe && !e.changes[i].key.Haber))) {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Valores incorrectos',
                            acceptText: 'Aceptar',
                            text: 'No puede grabar si alguna línea cuenta con Debe y Haber nulos.',
                            buttonCancel: 'border',
                            accept: () => {},
                        })

                        e.cancel = true;
                        valid = false;
                        break;
                    } else if ((e.changes[i].type === "insert" || e.changes[i].type === "update") && ((e.changes[i].data.Debe < 0 || e.changes[i].data.Haber < 0) || (e.changes[i].key.Debe < 0 || e.changes[i].key.Haber < 0))) {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Valores incorrectos',
                            acceptText: 'Aceptar',
                            text: 'No puede grabar si alguna línea cuenta con monto menor a 0 en Debe o Haber.',
                            buttonCancel: 'border',
                            accept: () => {},
                        })

                        e.cancel = true;
                        valid = false;
                        break;
                    } else if ((e.changes[i].type === "insert" || e.changes[i].type === "update") && (e.changes[i].data.Cuenta == null || e.changes[i].data.Cuenta == '' || e.changes[i].data.Nombre == null || e.changes[i].data.Nombre == '')) {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Cuenta no válida',
                            acceptText: 'Aceptar',
                            text: 'Debe ingresar un número de cuenta válido.',
                            buttonCancel: 'border',
                            accept: () => {},
                        })

                        e.cancel = true;
                        valid = false;
                        break;
                    } else {
                        // Aquí se almacena cada una de las acciones que se realizarán en la tabla de auxiliares
                        let insertar = null;
                        if (e.changes[i].type == 'insert') {
                            insertar = {
                                ...e.changes[i].data,
                                Opcion: 1,
                                CuentaBanco: this.formularioCheque.Cuenta,
                                Tipo: this.formularioCheque.Tipo,
                                Referencia: this.formularioCheque.Numero,
                                Periodo: this.formularioCheque.Periodo,
                                Fecha: this.formularioCheque.Fecha,
                                Usuario: this.Corporativo
                            };
                            revision.Agregados.push(insertar);
                        } else if (e.changes[i].type == 'update') {
                            insertar = {
                                ...e.changes[i].key,
                                Opcion: 2,
                                Usuario: this.Corporativo
                            };

                            // Recorrer las columnas del 'data' y actualizar el 'key' con los nuevos valores
                            for (const field in e.changes[i].data) {
                                if (Object.prototype.hasOwnProperty.call(e.changes[i].data, field)) {
                                    insertar[field] = e.changes[i].data[field]; // Actualiza el campo en 'key' con el valor de 'data'
                                }
                            }

                            revision.Modificados.push(insertar);
                        } else if (e.changes[i].type == 'remove') {
                            insertar = {
                                ...e.changes[i].key,
                                Opcion: 3,
                                Usuario: this.Corporativo
                            };

                            revision.Eliminados.push(insertar);
                        }

                        valid = true;
                    }
                }
            }

            if (valid) {
                if (parseFloat(this.dataGridDetalle.getTotalSummaryValue('Debe')).toFixed(2) === parseFloat(this.dataGridDetalle.getTotalSummaryValue('Haber')).toFixed(2)) //Verifica que el monto cuadre entre el debe y el haber
                {
                    if (parseFloat(this.dataGridDetalle.getTotalSummaryValue('Debe')).toFixed(2) === parseFloat(this.formularioCheque.Monto).toFixed(2)) { //Ya que debe y haber son iguales, se compara cualquiera de los dos con el monto del encabezado del cheque
                        await this.axios.post('/app/v1_bancos/RevisionAgrupado', {
                                ...revision
                            })
                            .then(resp => {
                                if (resp.data.codigo == 0) {
                                    this.$vs.dialog({
                                        type: 'alert',
                                        color: '#ed8c72',
                                        title: 'Actualización correcta',
                                        acceptText: 'Aceptar',
                                        text: 'El detalle del cheque fue actualizado correctamente',
                                        buttonCancel: 'border',
                                        accept: () => {},
                                    })
                                }
                            })
                    } else {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Monto cheque',
                            acceptText: 'Aceptar',
                            text: 'El monto no coincide con el valor del cheque.',
                            buttonCancel: 'border',
                            accept: () => {},
                        })
                        e.cancel = true
                        return
                    }
                } else {
                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'Montos erroneos',
                        acceptText: 'Aceptar',
                        text: 'Los montos de Debe y Haber no coinciden.',
                        buttonCancel: 'border',
                        accept: () => {},
                    })
                    e.cancel = true
                    return
                }
            }

        }
    },
    created() {},
    mounted() {
        this.formulario.Fecha = new Date()
        this.$validar_funcionalidad('/CONTABILIDAD/CON004', 'EDITARREVISION', (d) => {
            this.editarRevision = d.status
        })
    },
    beforeMount() {
        this.CargarCuentas()
        this.ConsultarPeriodo()
        this.CargarOpciones()
        this.CargarCuentasDefault()
        this.CargarPeriodos()
        this.BuscarProveedores()
        this.CargarCuentasAuxiliar()
    },
    watch: {
        'formulario.ChequeInicial'(newval) {
            if (this.formulario.ChequeFinal == 0 || this.formulario.ChequeFinal == null || parseInt(this.formulario.ChequeFinal) < parseInt(this.formulario.ChequeInicial)) {
                this.formulario.ChequeFinal = newval
            }
        }
    },
    computed: {
        formConsultaInstance: function () {
            return this.$refs[formConsulta].instance;
        },

        formChequeInstance: function () {
            return this.$refs[formCheque].instance;
        },

        dataGridDetalle: function () {
            return this.$refs[gridDetalle].instance;
        },
    }
}
</script>

<style>
.navigation-buttons button:focus {
    background-color: blue !important;
}
</style>
