<template>
   <div class="sem004-container flex-container"> 
    <vx-card>
      <vs-row>
        <DxForm :form-data="formData" label-mode="static" width="100%">
          <DxFormGroupItem
            :col-span="2"
            :col-count="10"
            :caption="datosParaCobroCaption"
          >
            <template v-slot:default>
              <vx-card>
                <!-- Contenido del vx-card -->

                <div class="flex flex-wrap">
                  <div id="Cuenta" class="pl-4 pb-4 w-full lg:w-1/2 xl:w-1/2">
                    <table class="responsive-table">
                      <thead>
                        <tr>
                          <th>Copago</th>
                          <th>Coaseguro</th>
                          <th>Descuento</th>
                          <th>Monto pago</th>
                          <th>Última actualización</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td data-label="Copago">
                            {{ formatCurrency(Cobro[0].copago) }}
                          </td>
                          <td data-label="Coaseguro">
                            {{ Cobro[0].coaseguro + "%" }}
                          </td>
                          <td data-label="Descuento">
                            {{ formatCurrency(Cobro[0].descuento) }}
                          </td>
                          <td data-label="Pago sugerido">
                            {{ formatCurrency(Cobro[0].montoPago) }}
                          </td>
                          <td data-label="Actualización">
                            {{ currentDate + " " + currentTime }}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>

                  <div id="Cuenta2" class="pl-4 pb-4 w-full lg:w-1/2 xl:w-1/2">
                    <table class="responsive-table">
                      <thead>
                        <tr>
                          <th>Cargos Hospital</th>
                          <th>Extra ordinarios</th>
                          <th>Cta ajena no elegible</th>
                          <th>Total a cobrar</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td data-label="Cargos Hospital">
                            {{ formatCurrency(Cobro[0].cargosHospital) }}
                          </td>
                          <td data-label="Extra Ordinarios">
                            {{ formatCurrency(formData.extraordinarios) }}
                          </td>
                          <td data-label="Cta ajena no elegible">
                            {{ formatCurrency(formData.ctaAjeno) }}
                          </td>
                          <td data-label="Total a cobrar">
                            {{ formatCurrency(formData.totalCobrar) }}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </vx-card>
            </template>
          </DxFormGroupItem>

          <DxFormGroupItem :col-span="2" :col-count="8">
            <DxFormItem
              data-field="deducible"
              editor-type="dxNumberBox"
              :editor-options="{ ...currencyOptions, hint: 'Deducible' }"
              :help-text="Cobro.deducible"
         
             
            >
            </DxFormItem>

            <DxFormItem
              data-field="copago"
              type="dxNumberBox"
            :editor-options="currencyOptions"
           
            ></DxFormItem>

            <DxFormItem
              id="Gsadicionales"
              data-field="gastosEspeciales"
              type="dxNumberBox"
              :help-text="Cobro.gastosEspeciales"
              :editor-options="{
                ...currencyOptions,
                hint: 'Gastos especiales no cubiertos',
              }"
            >
              <DxFormLabel text="Gastos especiales no cubiertos" />
            </DxFormItem>
            <DxFormItem
              data-field="coaseguro"
              type="dxNumberBox"
              :editor-options="currencyOptions"
              :help-text="Cobro.coaseguro"
            >
            </DxFormItem>

            <DxFormItem
              data-field="Facturado"
              type="dxNumberBox"
              :editor-options="currencyOptions"
              :help-text="Cobro.facturado"
            >
            </DxFormItem>
            <DxFormItem
              item-type="button"
              :button-options="buttonGrabarCalcular"
            ></DxFormItem>
            <DxFormItem
              item-type="button"
              :button-options="buttonGrabarCambios"
            ></DxFormItem>
            <DxFormItem
              item-type="button"
              :button-options="buttonGrabarActualizar"
            ></DxFormItem>
          </DxFormGroupItem>
          <DxFormGroupItem :col-span="8" :col-count="22" caption="Descuentos">
            <DxFormItem
              data-field="tipoDescuento"
              editor-type="dxSelectBox"
              :col-span="4"
              :editor-options="{
                items: dsDescuentos,
                displayExpr: 'Descripcion',
                valueExpr: 'idDescuento',
                searchEnabled: 'true',
                onValueChanged: onNombreChanged,
              }"
              v-model="selectedDescuento"
            >
            </DxFormItem>
            <DxFormItem
              data-field="descuento"
              editor-type="dxNumberBox"
              :editor-options="{ ...PorcentajeOptions, disabled: true }"
              :col-span="2"
            ></DxFormItem>
            <DxFormItem
              data-field="totalDescuento"
              editor-type="dxNumberBox"
              :editor-options="{ ...currencyOptions, disabled: true }"
              :col-span="2"
            ></DxFormItem>
            <DxFormItem
              data-field="noAutorizacion"
              editor-type="dxNumberBox"
              :col-span="2"
              ref="noAutorizacion"
            ></DxFormItem>
            <DxFormItem
              item-type="button"
              :button-options="{
                ...buttonObtenerDescuento,
                hint: 'Asigna descuento a la admisión seleccionada',
                disabled: obtenerDescuento,
              }"
              :col-span="3"
            ></DxFormItem>

            <DxFormItem
              item-type="button"
              :button-options="{
                ...buttonElminardescuento,
                hint: 'Elimina el descuento de la admisión seleccionada',
                disabled: eliminarDescuento,
              }"
              :col-span="3"
            >
            </DxFormItem>
          </DxFormGroupItem>

        </DxForm>

        <div class = "w-full">
          
                <vs-textarea label="Observaciones" 
                v-model="formData.observaciones"  
                counter = "250" rows="2" 
                @input="checkLength"
                />
           
            </div>
      </vs-row>
    </vx-card>
    <vx-card>
      <div style="overflow-x: auto">
        <DxDataGrid
          v-bind="DefaultDxGridConfiguration"
          :dataSource="AdmisionesObservacion"
          class="custom-datagrid"
          height="auto"
        >
          <DxDataGridColumn
            dataField="Observacion"
            caption="Observación"
            :customizeText="DisplayExpression"
            :encode-html="false"
            :cellTemplate="observacionCellTemplate"
          >
          </DxDataGridColumn>
          <DxDataGridColumn
            dataField="fecharegistro"
            caption="Fecha de Registro"
            dataType="datetime"
            :width="150"
            format="dd/MM/yyyy HH:mm:ss"
          />
          <DxDataGridColumn dataField="usuario" caption="Usuario" :width="100" />
        </DxDataGrid>
      </div>
    </vx-card>
    <dx-popup
      :visible.sync="popupCalculoCobro"
      :show-title="true"
      title="Resumen para cobro"
      width="auto"
      max-width="100%"
      height="auto"
      max-height="100%"
      @shown="adjustRows"
    >
    <dx-scroll-view>
      <table class="styled-table" style="width: 100%; table-layout: auto">
        <tr>
          <th class="titulo-th" colspan="2">
            Desglose de cobro admisión {{ this.formData.Admision }}
          </th>
        </tr>
        <tr>
          <td style="white-space: nowrap">Copago</td>
          <td>{{ formatCurrency(formData.copago) }}</td>
        </tr>
        <tr>
          <td style="white-space: nowrap">Coaseguro</td>
          <td>{{ formatCurrency(formData.coaseguro) }}</td>
        </tr>
        <tr>
          <td style="white-space: nowrap">Deducible</td>
          <td>{{ formatCurrency(formData.deducible) }}</td>
        </tr>
        <tr>
          <td style="white-space: nowrap">Gastos especiales no cubiertos</td>
          <td>{{ formatCurrency(formData.gastosEspeciales) }}</td>
        </tr>
        <tr>
          <td style="white-space: nowrap">Extraordinarios</td>
          <td>{{ formatCurrency(formData.extraordinarios) }}</td>
        </tr>
        <tr>
          <td style="white-space: nowrap">Cuenta ajena no elegible</td>
          <td>{{ formatCurrency(formData.ctaAjeno) }}</td>
        </tr>
        <tr>
          <td style="white-space: nowrap">Facturado o cobrado en cuenta</td>
          <td :class="{ rojo: isPositive(formData.Facturado) }">
            {{ formatCurrency(formData.Facturado) }}
          </td>
        </tr>
        <tr>
          <td style="white-space: nowrap">
            Descuento {{ this.formData.nombreDescuento }}
          </td>
          <td :class="{ rojo: isPositive(formData.totalDescuento) }">
            {{ formatCurrency(formData.totalDescuento) }}
          </td>
        </tr>
        <tr>
          <td style="white-space: nowrap">Total a cobrar</td>
          <td style="font-weight: bold">
            --{{ formatCurrency(formData.totalCobrar) }}--
          </td>
        </tr>
      </table>
      <div class = "w-full">
          
          <vs-textarea label="Observaciones" 
          v-model="formData.observaciones"  
          :rows="rows"
          @input="adjustRows"
          readonly
          />
     
      </div>
      <div style="margin-top: 20px; display: flex; justify-content: center">
        <div class="btn-group pl-4">
          <vs-button
            class="w-full"
            color="success"
            type="filled"
            icon-pack="fas"
            icon="fa-save"
            @click="InsertarCalculoCobro()"
            title="Graba el dato para cobro con los datos generados en pantalla"
          >
            Guardar
          </vs-button>
        </div>
        <div class="btn-group pl-4">
          <vs-button
            class="w-full"
            color="danger"
            type="filled"
            icon-pack="fas"
            icon="fa-times"
            @click="popupCalculoCobro = false"
            title="Cancelar la solicitud"
          >
            Cancelar
          </vs-button>
        </div>
      </div>
    </dx-scroll-view>
    </dx-popup>

    <SM-Validar-Pass
      ref="refValidarPass"
    

    />
  </div>
</template>

<script>
import VxCard from "../../../../components/vx-card/VxCard.vue";
import { DefaultDxGridConfiguration } from "../../EXPEDIENTE/data.js";
import CustomStore from "devextreme/data/custom_store";
import esMessages from "devextreme/localization/messages/es.json";
import { locale, loadMessages } from "devextreme/localization";
import { CustomDevExtremeEsMessages } from "../../EXPEDIENTE/data.js";
export default {
  name: "CalculoForm",
  components: {
    VxCard,


  },
  props: {
    callbackInsertarRecalculo: Function,
  },
  data() {
    return {
      DefaultDxGridConfiguration,
      buttonObtenerDescuento: {
        text: "Obtener Descuento",
        type: "success",
        onClick: () => this.Descuentos("I"),
        icon: "add",
        //   disabled: this.obtenerDescuento
      },
      buttonElminardescuento: {
        width: "150",
        text: "Eliminar Descuento",
        type: "default",
        icon: "minus",

        onClick: () => this.Descuentos("E"),
        stylingMode: "contained",
        elementAttr: {
          style: "white-space: pre-wrap; text-align: center;",
        },
      },
      buttonCalcularDescuento: {
        text: "Calcular",
        type: "default",
        icon: "minus",
        onClick: this.handleButtonClick,
      },

      buttonGrabarCambios: {
        text: "Grabar",
        type: "default",
        icon: "floppy",

        onClick: this.GrabarCambios,
        hint: "Grabar cambios",
        width: "200",
      },
      buttonGrabarCalcular: {
        text: "Calcular",
        type: "default",
        icon: "money",
        onClick: this.CalculoCobro,
        hint: "Calcular cobro a paciente",
        width: "200",
      },
      buttonGrabarActualizar: {
        text: "Actualizar",
        type: "default",
        icon: "refresh",
        onClick: this.actualizarCargos,
        width: "200",

        hint: "Actualizar información",
      },
      buttonGrabarReCalcular: {
        text: "Re-Calcular",
        type: "default",
        icon: "revert",
        onClick: () => {
          this.ValidarPermisos(); //this.popupRecalculo = true;
        },
        hint: "Abrir ventana para recalcular cuenta",
      },
      Areas: {
        dataSource: [],
        displayExpr: "Area",
        valueExpr: "Id",
        searchEnabled: true,
      },
      notificationOptions: {
        time: 4000,
        title: "Semaforo egresos",
        iconPack: "feather",
        icon: "icon-alert-circle",
        position: "top-center",
      },

      formData: {
        deducible: 0.0,
        copago: 0.0,
        gastosEspeciales: 0,
        Facturado: 0.0,
        coaseguro: 0.0,
        facturado: 0.0,
        extraordinarios: 0.0,
        ctaAjeno: 0.0,
        totalCobrar: 0.0,
        Hospital: "",
        Servicio: "",
        Paciente: "",
        Seguro: "",
        Admision: "",
        SerieAdmision: "",
        noAdmision: 0,
        tipoDescuento: "",
        descuento: 0.0,
        totalDescuento: 0,
        noAutorizacion: 0,
        observaciones: "",
        tipoAdmision: "",
        nombreDescuento: "",
      }, //dat
      rows: 2,
      opcionRecalculo: "0",
      CobroProcesado: false,
      CorporativoGraba:null,
      NombreDescuentoProvisional: "",
      PermisoOpcion:false,
      localIP: '',
        ip: '',

      Cobro: [
        {
          deducible: "",
          copago: "",
          gastosEspeciales: "",
          coaseguro: "",
          facturado: "",
          extraordinarios: "",
          ctaAjeno: "",
          totalCobrar: "",
          cargosHospital: "",
          descuento: "",
          montoPago: "",
          CtaAjenaNoElegible: "",
        },
      ],

      positionEditorOptions: {
        items: this.dsDescuentos,
        searchEnabled: true,
        value: "",
      },
      observacionesAdmision: "Notas para la admisión",
      ListaCuentasx: new CustomStore({
        key: "ID",
        load: () => {
          return this.ListaCuentas;
        },
      }),
      dsCobros: [
        {
          deducible: "",
          copago: "",
          gastosEspeciales: "",
          coaseguro: "",
          facturado: "",
          extraordinarios: "",
          ctaAjeno: "",
          totalCobrar: "",
          cargosHospital: "",
          descuento: "",
          montoPago: "",
          ultimaActualizacion: "",
        },
      ],
      dsDescuentos: [],
      selectedDescuento: null,
      dsAreas: [],
      dsListadoAdmisiones: [],
      obtenerDescuento: true,
      eliminarDescuento: true,
      popupCalculoCobro: false,
      popupRecalculo: false,
      AdmisionesObservacion: [],
      ListaPermisos: "X",

      currencyOptions: {
        format: "Q ###,##0.##",
        showClearButton: true,
        onValueChanged: function (e) {
          if (e.value === null || e.value < 0) {
            e.component.option("value", 0);
          }
        },
        min: 0,
      },
      PorcentajeOptions: {
        format: "#0%",
        showClearButton: true,
        onValueChanged: this.handleValueChanged,
      },
      currentDate: new Date().toLocaleDateString(),

      currentTime: "",
    }; //return
  },

  methods: {
    DisplayExpression(e) {
      return this.$saltos_tab_to_html(e.value);
    },

    ValidarPermisos(Permiso,Titulo,Detalle) {
      this.PermisoOpcion=false;
      this.ListaPermisos = "X";
      this.$refs.refValidarPass.iniciar((x) => {
        this.CorporativoGraba=null;
        if (x != null) {
          
          this.ListaPermisos = x.permisos.find((t) => t == Permiso);
        }
        
        if (this.ListaPermisos === "X") {
          this.$vs.notify({
            time: 4000,
            title: "Validación cancelada",
            text: "Se cancelo la validación de permisos",
            iconPack: "feather",
            icon: "icon-alert-circle",
            color: "warning",
            position: "top-center",
          });

          return;
        }

        if (this.ListaPermisos != undefined) {
          this.popupCalculoCobro = true;
          this.PermisoOpcion=true;
          this.CorporativoGraba=this.$refs.refValidarPass.info.corporativo
          return;
        } else {
          this.$vs.notify({
            time: 4000,
            title: Titulo,
            text: Detalle,
            iconPack: "feather",
            icon: "icon-alert-circle",
            color: "warning",
            position: "top-center",
          });

          return;
        }
      });
    },
    observacionCellTemplate(cellElement, cellInfo) {
      const formattedValue = cellInfo.value
        .replace(/\t/g, "   ") // Reemplaza tabuladores por tres espacios
        .replace(/\n/g, "<br>") // Reemplaza saltos de línea por <br>
        .replace(/\r/g, "<br>"); // Reemplaza saltos de línea por <br>
      cellElement.innerHTML = `<span class="observacion-text">${formattedValue}</span>`;
    },

    ValidarCuenta(SerieAdmision, Admision) {
      this.axios
        .post("/app/v1_SemaforoEgresos/ValidarCuenta", {
          SerieAdmision: SerieAdmision,
          Admision: Admision,
        })
        .then((resp) => {
          this.formData.nombreDescuento = "";
          if (resp.data) {
            this.formData.totalDescuento = resp.data[0].BeneficioTerceros;

            if (resp.data[0].idTipoDescuento === -1) {
              this.eliminarDescuento = true;
              this.obtenerDescuento = false;
            } else {
              this.formData.nombreDescuento = resp.data[0].TipoDescuento;
              this.eliminarDescuento = false;
              this.obtenerDescuento = true;
            }
          }

          if (resp.data && resp.data[0].ctaCalculada === 1) {
            (this.Cobro = [
              {
                deducible: "",
                copago: "",
                gastosEspeciales: "",
                coaseguro: 0.0,
                facturado: "",
                extraordinarios: "",
                ctaAjeno: "",
                totalCobrar: "",
                cargosHospital: "",
                descuento: "",
                montoPago: "",
                CtaAjenaNoElegible: "",
              },
            ]),
              this.$vs.notify({
                time: 4000,
                title:
                  "Admisión " +
                  SerieAdmision +
                  "-" +
                  Admision +
                  " Ya fue calculada",
                text: "La cuenta seleccionada ya fue calculada",
                iconPack: "feather",
                icon: "icon-alert-circle",
                color: "warning",
                position: "top-center",
              });
            this.dsListadoAdmisiones = this.dsListadoAdmisiones.filter(
              (record) =>
                record.serie !== SerieAdmision || record.Admision !== Admision
            );
          } else {
            this.CalcularCuenta(SerieAdmision, Admision);
          }
        });
    },

    CalcularCuenta(SerieAdmision, Admision) {
      this.axios
        .post("/app/v1_SemaforoEgresos/ConsultaCuenta", {
          SerieAdmision: SerieAdmision,
          Admision: Admision,
        })
        .then((resp) => {
          
          if (resp.data.json && resp.data.json.length > 0) {
            this.Cobro = resp.data.json.map((x, index) => {
              return {
                id: index,
                cargosHospital: x.CargosHospital,
                copago: x.Copago,
                coaseguro: x.Coaseguro,
                Hora: x.Hora,
                Copago: x.Copago,
                montoPago: x.MontoPago,
                descuento: x.descuento,
                porcentajedescuento: x.porcentajedescuento,
                extraordinarios: x.ExtraOrdinarios,
                ctaAjeno: x.CtaAjenaNoElegible,
                            
              };
            });
        
            this.formData.extraordinarios = this.Cobro[0].extraordinarios;
            this.formData.ctaAjeno = this.Cobro[0].ctaAjeno;
            this.formData.totalCobrar = this.Cobro[0].montoPago;

          }

          this.updateTime();
        });
    },

    CalcularCuentaRecalculo(SerieAdmision, Admision) {
      this.axios
        .post("/app/v1_SemaforoEgresos/ConsultaCuentaRecalculo", {
          SerieAdmision: SerieAdmision,
          Admision: Admision,
        })
        .then((resp) => {
          if (resp.data.json && resp.data.json.length > 0) {
            this.Cobro = resp.data.json.map((x, index) => {
              return {
                id: index,
                cargosHospital: x.CargosHospital,
                copago: x.Copago,
                coaseguro: x.Coaseguro,
                Hora: x.Hora,
                Copago: x.Copago,
                montoPago: x.MontoPago,
                descuento: x.descuento,
                porcentajedescuento: x.porcentajedescuento,
                extraordinarios: x.ExtraOrdinarios,
                ctaAjeno:x.CtaAjenaNoElegible,
              };
            });
            this.formData.extraordinarios = this.Cobro[0].extraordinarios;
            this.formData.ctaAjeno = this.Cobro[0].ctaAjeno;
            this.formData.totalCobrar = this.Cobro[0].montoPago;
          }

          this.updateTime();
        });
    },

    actualizarCargos() {
      if (this.opcionRecalculo === 1) {
        this.axios
          .post("/app/v1_SemaforoEgresos/ConsultaCuentaRecalculo", {
            SerieAdmision: this.formData.SerieAdmision,
            Admision: this.formData.noAdmision,
          })
          .then((resp) => {
            if (resp.data.json && resp.data.json.length > 0) {
              this.Cobro = resp.data.json.map((x, index) => {
                return {
                  id: index,
                  cargosHospital: x.CargosHospital,
                  copago: x.Copago,
                  coaseguro: x.Coaseguro,
                  Hora: x.Hora,
                  Copago: x.Copago,
                  montoPago: x.MontoPago,
                  descuento: x.descuento,
                  porcentajedescuento: x.porcentajedescuento,
                  extraordinarios: x.ExtraOrdinarios,
                };
              });
            }

            this.updateTime();
          });
      } else {
        this.CalcularCuenta(
          this.formData.SerieAdmision,
          this.formData.noAdmision
        );
      }
      this.MostrarobservacionesAdmision();
    },

    Descuentos(X) {
      const notificationOptions = {
        time: 4000,
        title: "Notificación descuentos",
        iconPack: "feather",
        icon: "icon-alert-circle",
        position: "top-center",
      };

      if (X === "I") {
        if (this.formData.descuento <= 0) {
          this.$vs.notify({
            time: 4000,
            title: "Notificación descuentos",
            text: "El descuento seleccionado es '0%' no se puede continuar",
            iconPack: "feather",
            icon: "icon-alert-circle",
            color: "warning",
            position: "top-center",
          });
          return; // Salir de la función si el descuento es 0 o menor
        }
      }

      if (X === "E") {
        this.formData.tipoDescuento = 0;
      }

      if ((X === "I") & (this.formData.noAutorizacion === 0)) {
        this.$vs.notify({
          ...notificationOptions,
          text: "Debe ingresar el numero de autorización",
          color: "warning",
        });

        return;
      }
      this.axios
        .post("/app/v1_SemaforoEgresos/Descuentos", {
          admision: this.formData.noAdmision,
          serieAdmision: this.formData.SerieAdmision,
          copago: this.formData.copago,
          coaseguro: this.formData.coaseguro,
          deducible: this.formData.deducible,
          gastosnocubiertos: this.formData.gastosEspeciales,
          autorizacion: this.formData.noAutorizacion,
          idDescuento: this.formData.tipoDescuento,
          opcion: X,
          equipo:this.localIP,
        })
        .then((response) => {
          const data = response.data[0];

          if (data.codigo === 0) {
            this.formData.descuento = null;
            this.$vs.notify({
              ...notificationOptions,
              text: `${data.descripcion} por un monto de: Q${data.descuento}`,
              color: "success",
            });
            //eliminar descuento
            if (X === "E") {
              this.obtenerDescuento = false;
              this.eliminarDescuento = true;
              this.formData.totalDescuento = 0;
              this.formData.nombreDescuento = "";
            }

            // otener descuento
            if (X === "I") {
              this.obtenerDescuento = true;
              this.eliminarDescuento = false;
              this.formData.totalDescuento = data.descuento;
              this.formData.nombreDescuento = this.NombreDescuentoProvisional;
            }
          } else {
            this.$vs.notify({
              ...notificationOptions,
              text: data.descripcion,
              color: "warning",
            });
          }

          this.updateTime();
        });
    },

    porcentajedescuento() {
      if (
        (this.formData.tipoAdmision !== "") &
        (this.formData.tipoDescuento != "")
      ) {
        this.axios
          .post("/app/v1_SemaforoEgresos/PorcentajeDescuento", {
            tipoAdmision: this.formData.tipoAdmision,
            idDescuento: this.formData.tipoDescuento,
          })
          .then((resp) => {
            this.formData.descuento = resp.data[0].PorcentajeDescuento;
          });
      }
    },

    formatCurrency(value) {
      if (!value) return "0";
      value = value.toString();
      return "Q" + value.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    updateTime() {
      const now = new Date();
      const hours = String(now.getHours()).padStart(2, "0");
      const minutes = String(now.getMinutes()).padStart(2, "0");
      const seconds = String(now.getSeconds()).padStart(2, "0");
      this.currentTime = `${hours}:${minutes}:${seconds}`;
    },
    resetFormData() {
      (this.formData.deducible = 0),
        (this.formData.copago = 0),
        (this.formData.gastosEspeciales = 0),
        (this.formData.Facturado = 0),
        (this.formData.coaseguro = 0.0),
        (this.formData.facturado = 0),
        (this.formData.extraordinarios = 0),
        (this.formData.ctaAjeno = 0),
        (this.formData.totalCobrar = 0),
        (this.formData.Paciente = ""),
        (this.formData.Seguro = ""),
        (this.formData.Admision = ""),
        (this.formData.SerieAdmision = ""),
        (this.formData.noAdmision = 0),
        (this.formData.tipoDescuento = ""),
        (this.formData.descuento = 0),
        (this.formData.totalDescuento = 0),
        (this.formData.noAutorizacion = 0),
        (this.formData.observaciones = ""),
        (this.formData.tipoAdmision = "");
      this.formData.nombreDescuento = "";
    },
    resetCalculoCobro() {
      this.Cobro = [
        {
          deducible: "",
          copago: "",
          gastosEspeciales: "",
          coaseguro: "0.0",
          facturado: "",
          extraordinarios: "",
          ctaAjeno: "",
          totalCobrar: "",
          cargosHospital: "",
          descuento: "",
          montoPago: "",
          CtaAjenaNoElegible: "",
        },
      ];
    },

    handleValueChanged(e) {
      if (e.value === null) {
        this.formData.tipoDescuento = "";
      }
    },
    CalculoCobro() {
      const notificationOptions = {
        time: 4000,
        title: "Notificación cálculo admisión",
        iconPack: "feather",
        icon: "icon-alert-circle",
        position: "top-center",
      };

      if (this.formData.SerieAdmision === "") {
        this.$vs.notify({
          ...notificationOptions,
          text: "No ha seleccionado una admisión",
          color: "warning",
        });
        return;
      }
      const sumatoria = (
        this.formData.coaseguro +
        this.formData.copago +
        this.formData.deducible +
        this.formData.gastosEspeciales +
        this.formData.extraordinarios +
        this.formData.ctaAjeno -
        (this.formData.Facturado + this.formData.totalDescuento)
      ).toFixed(2);

      this.formData.totalCobrar = parseFloat(sumatoria);
      if (this.formData.totalCobrar > this.Cobro[0].cargosHospital) {
        this.$vs.notify({
          ...notificationOptions,
          text: "El cálculo realizado, supera el valor total de cargos",
          color: "warning",
        });
        return;
      }
    },

    GrabarCambios() {
      const notificationOptions = {
        time: 4000,
        title: "Notificación cálculo admisión",
        iconPack: "feather",
        icon: "icon-alert-circle",
        position: "top-center",
      };

      if (this.formData.SerieAdmision === "") {
        this.$vs.notify({
          ...notificationOptions,
          text: "No ha seleccionado una admisión",
          color: "warning",
        });
        return;
      }

      
      this.CalculoCobro();
      let nombrePermiso=""
      if (this.opcionRecalculo === 1) {
        nombrePermiso="RECALCULOCUENTAS"
      }
      else{
        nombrePermiso="VISTACUENTAS"
      }
//aqui es la chamba
this.ValidarPermisos(nombrePermiso,'No Tiene permiso a la opción ','Solicite Acceso a '+nombrePermiso);
    //  this.popupCalculoCobro = true;
    },
    isPositive(value) {
      return parseFloat(value) > 0;
    },

    InsertarCalculoCobro() {
      const notificationOptions = {
        time: 4000,
        title: "Notificación Cálculo admisión",
        iconPack: "feather",
        icon: "icon-alert-circle",
        position: "top-center",
      };
      if (this.formData.SerieAdmision === "") {
        this.$vs.notify({
          ...notificationOptions,
          text: "No ha seleccionado una admisión",
          color: "warning",
        });
        return;
      }
      this.axios
        .post("/app/v1_SemaforoEgresos/InsertarCalculo", {
          Copago: this.formData.copago,
          Coaseguro: this.formData.coaseguro,
          Deducible: this.formData.deducible,
          Gastosnocubiertos: this.formData.gastosEspeciales,
          ExtraOrdinarios: this.formData.extraordinarios,
          CtaAjena: this.formData.ctaAjeno,
          Facturado: this.formData.Facturado,
          Descuento: this.formData.totalDescuento,
          Total: this.formData.totalCobrar,
          Comentario: this.formData.observaciones,
          SerieAdmision: this.formData.SerieAdmision,
          Admision: this.formData.noAdmision,
          NombreDescuento: this.formData.nombreDescuento,
          Recalculo: this.opcionRecalculo,
          Corporativo:this.CorporativoGraba
        })
        .then((response) => {
          const data = response.data[0];

          if (data.codigo === 0) {
            this.$vs.notify({
              ...notificationOptions,
              text: `${data.descripcion}`,
              color: "success",
            });

            this.popupCalculoCobro = false;
            this.AdmisionesObservacion = [];
            this.CobroProcesado = true;
            this.MostrarobservacionesAdmision();

            //actualizar listado principal
            // this.$emit('calculo-cobro-insertado')
          } else {
            this.$vs.notify({
              ...notificationOptions,
              text: data.descripcion,
              color: "warning",
            });
          }
        });
    },
    MostrarobservacionesAdmision() {
      // Validar datos antes de la solicitud
      if (!this.formData.SerieAdmision || !this.formData.noAdmision) {
        this.$vs.notify({
          ...this.notificationOptions,
          text: "Serie de admisión o número de admisión no proporcionados",
          color: "warning",
        });
        return;
      }

      // Bloquear la UI mientras se realiza la solicitud
      this.loading = true;

      this.axios
        .post("/app/v1_SemaforoEgresos/ObservacionesAdmision", {
          SerieAdmision: this.formData.SerieAdmision,
          Admision: this.formData.noAdmision,
        })
        .then((response) => {
          const data = response.data;

          if (data) {
            this.$set(this, "AdmisionesObservacion", data);
          } else {
            this.$vs.notify({
              ...this.notificationOptions,
              text: "No se encontraron observaciones",
              color: "info",
            });
          }
        });
    },

    formatObservacion(observacion) {
     // Reemplaza diferentes tipos de saltos de línea con <br/>
  let textoFormateado = observacion.replace(/(\r\n|\n|\r)/g, "<br/>");
  

// Opcional: Escapar caracteres HTML para evitar problemas de seguridad
textoFormateado = textoFormateado.replace(/&/g, "&amp;")
                             .replace(/</g, "&lt;")
                             .replace(/>/g, "&gt;")
                             .replace(/"/g, "&quot;")
                             .replace(/'/g, "&#039;");

return textoFormateado;
      
    },

    onNombreChanged(e) {
      const selectedNombre = e.component.option("displayValue");
      this.NombreDescuentoProvisional = selectedNombre;
    },
    getLocalIP() {
      this.localIP = localStorage.IpLocal
    },
    checkLength() {
      if (this.formData.observaciones.length > 250) {
        this.formData.observaciones = this.formData.observaciones.slice(0, 250);
      }
  
  },
  adjustRows() {
      const lines = this.formData.observaciones.split('\n').length;
      const newRows = Math.min(6, Math.max(2, Math.ceil(this.formData.observaciones.length / 50) + lines - 1));
      this.rows = newRows || 2; // Asegura que haya al menos 2 filas
    },

},

  watch: {
    NumeroAdmision(newVal) {
      if (newVal != undefined) this.calcular();
    },
    "formData.tipoDescuento"(newVal) {
      if (newVal != undefined) this.porcentajedescuento();
    },
  },
  computed: {
    datosParaCobroCaption() {
      const { Paciente, Seguro, Admision } = this.formData;
      if (Paciente || Seguro || Admision) {
        return `Admisión: ${Admision}\nPaciente: ${Paciente}\nSeguro: ${Seguro}`;
      }
      return "Datos para cobro";
    },
    captionHtml() {
      const { Paciente, Seguro, Admision } = this.formData;
      if (Paciente || Seguro || Admision) {
        return `Datos para cobro:<br>Paciente: ${Paciente}<br>Seguro: ${Seguro}<br>Admisión: <span style="color: blue;">${Admision}</span>`;
      }
      return "Datos para cobro";
    },
  },
  created() {
    loadMessages(esMessages);
    loadMessages({
      es: CustomDevExtremeEsMessages,
    });
    locale(navigator.language);
  },
  mounted() {
    this.axios
      .post("/app/v1_SemaforoEgresos/CatalogoSemaforo", {
        Catalogo: "Descuentos",
      })
      .then((resp) => {
        this.dsDescuentos = resp.data;
      });
      this.getLocalIP()
  },
};
</script>
<style>
.sem004-container .dx-field-item-content {
  padding: 0px;
  margin-bottom: 3px;
  border: 2px;
}

.sem004-container .dx-widget {
  line-height: 1.1;
}
[dir] .sem004-container .dx-form-group-with-caption > .dx-form-group-content {
  padding-top: 3px;
  margin-top: 6px;
  border-top: 1px solid #ddd;
  padding-bottom: 7px;
}
.sem004-container .dx-datagrid-rowsview
  .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed)
  > td {
  background-color: #00ccff !important;
  color: black !important;
}

.sem004-container .dx-datagrid-rowsview .dx-row.dx-data-row.dx-column-lines.dx-state-hover > td {
  background-color: #79e4ff !important;
  color: black !important;
  font-size: 16px;
}

.sem004-container .dx-datagrid-headers {
  background-color: #008fbe !important;
  color: rgb(252, 255, 254) !important;
  font-weight: bold;
}

.sem004-container .dx-layout-manager .dx-field-item:not(.dx-first-row) {
  padding-top: 2px;
}

.sem004-container .dx-layout-manager .dx-field-item:not(.dx-first-col) {
  padding-left: 5px;
  padding-right: 5px;
}

[dir="ltr"] .sem004-container .dx-layout-manager .dx-field-item:not(.dx-last-col) {
  padding-right: 5px;
}

.sem004-container .responsive-table {
  width: 100%;
  border-collapse: collapse;
  padding: 2px;
}

.sem004-container .responsive-table th,
.sem004-container .responsive-table td {
  padding: 2px;
  text-align: left;
  border: 1px solid #ddd;
}

/* Añadir altura mínima a las filas */
.sem004-container .dx-datagrid-rowsview .dx-row {
  min-height: 30px; /* Ajusta este valor según sea necesario */
}

/* Evitar desbordamiento de contenido */
.sem004-container .dx-datagrid-rowsview .dx-row td {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Estilos generales para la tabla */
.sem004-container .responsive-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: auto; /* Permite que las filas se ajusten automáticamente al contenido */
}

.sem004-container.responsive-table th,
.sem004-container .responsive-table td {
  border: 1px solid #ddd;
  padding: 4px;
  text-align: left;
  vertical-align: top; /* Asegura que el contenido se alinee en la parte superior */
}

.sem004-container .responsive-table th {
  background-color: #f2f2f2;
}

/* Estilos responsivos */
@media screen and (max-width: 600px) {
  .sem004-container .responsive-table thead {
    display: none;
  }

  .sem004-container .responsive-table tr {
    display: block;
    margin-bottom: 10px;
  }

  .sem004-container .responsive-table td {
    display: block;
    text-align: right;
    padding-left: 50%;
    position: relative;
    white-space: normal; /* Evitar que el texto se desborde */
    min-width: 200px; /* Ancho mínimo para celdas en dispositivos móviles */
    line-height: 1.5;
  }

  .sem004-container .responsive-table td::before {
    content: attr(data-label);
    position: absolute;
    left: 0;
    width: 50%;
    padding-left: 5px;
    font-weight: bold;
    text-align: left;
    white-space: nowrap;
  }
  .sem004-container .responsive-table td:last-child {
    margin-bottom: 10px; /* Añadir espacio al final de cada fila */
  }
}
.rojo {
  color: red;
}
.titulo-th {
  background-color: #008fbe !important;
  color: rgb(252, 255, 254) !important;
  font-weight: bold;
  font-size: 20px; /* Puedes ajustar el tamaño según tus necesidades */
}

.sem004-container .popup-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: center;
}

.sem004-container .centered-text {
  font-weight: bold;
}
.sem004-container .dx-popup-title {
  background-color: #008fbe !important;
  color: #f3f4f7 !important;
}
.sem004-container td {
  vertical-align: middle !important;
}
.sem004-container .observacion-text {
  font-family: "Consolas", sans-serif;
  font-size: 16px;
}

.sem004-container .half-width {
  width: 40%; /* 50% del ancho del contenedor */
  background-color: lightblue; /* Color de fondo para visualizar el tamaño */
}

.sem004-container .styled-table {
  border-collapse: collapse;
  width: 100%;
}

.sem004-container .styled-table th,
.sem004-container .styled-table td {
  border: 1px solid black; /* Bordes de color negro */
  padding: 4px;
}

.sem004-container .styled-table td:nth-child(2) {
  text-align: right; /* Alinear la segunda columna a la izquierda */
}

.sem004-container .styled-table th {
  background-color: #f2f2f2;
  text-align: left;
}

.sem004-container .styled-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

.sem004-container .styled-table tr:hover {
  background-color: #008fbe !important;
  color: #f3f4f7 !important;
}

.sem004-container .popup-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.flex-container {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 10px;
}

.flex-item {
  background-color: #f0f0f0;
  padding: 10px;
  border: 1px solid #ccc;
  flex: 1;
}
.sem004-container .dx-datagrid .dx-data-row .dx-cell {
  user-select: text;
}

.sem004-container .custom-label {
  color: rgb(0, 255, 115);
}
.sem004-container .custom-dx-item .dx-field-item-label-text {
  color: blue !important;
}
</style>
