<template>
    <vx-card title="Reporte" class="report001-container">
        <vs-popup :active.sync="emailPopup" title="Envío de correo electrónico">
            <sm-email v-bind="emailConfig" :allowAddAttachment="false" :allowDeleteAttachment="false" @success="emailPopup=false" :maxAttachment="20" />
        </vs-popup>
        <vs-popup classContent="popup-example" :title="reporte.titulo" :active.sync="reporte.popup">
            <ValidationObserver ref="formValidate" v-slot="{ invalid, validate }" mode="lazy">
                <form @submit.prevent="validate().then(submitForm(reporte.opciones))">
                    <div class="flex flex-wrap">
                        <template v-for="(opcion,index) in reporte.opciones">
                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2  p-1 " v-if="opcion.Tipo!='Oculto'" :key="index">
                                <div v-if="opcion.ValidarPermiso && opcion.TienePermiso">
                                    {{opcion.Etiqueta}}
                                </div>
                                <div v-else-if="opcion.ValidarPermiso != true">
                                    {{opcion.Etiqueta}}
                                </div>
                                <ValidationProvider :name="index.toString()" :rules="getValidatorRules(opcion)" class="required">
                                    <template>
    
                                        <!-- <flat-pickr :config="configdateDatePicker" style="width:100%" :name="'Campo_'+index" placeholder="Date Time" v-model="opcion.value" :required="opcion.Obligatorio==1" /> -->
                                        <vs-input v-if="!opcion.Buscador && opcion.Tipo=='Fecha'" :name="'Campo_'+index" class="w-full" type="date" v-model="opcion.value" :required="opcion.Obligatorio==1" @blur="(e)=>validateDate(e,opcion)" />
                                        <vs-input v-else-if="!opcion.Buscador && opcion.Tipo=='Fecha/Hora'" :name="'Campo_'+index" class="w-full" type="date" v-model="opcion.value" :required="opcion.Obligatorio==1" @blur="(e)=>validateDate(e,opcion)" />
                                        <vs-input v-else-if="!opcion.Buscador && opcion.Tipo=='Fecha/Hora-ISO'" :name="'Campo_'+index" class="w-full" type="datetime-local" v-model="opcion.value" :required="opcion.Obligatorio==1" @blur="(e)=>validateDate(e,opcion)" />
                                        <vs-input v-else-if="!opcion.Buscador && opcion.Tipo=='Fecha-ISO'" :name="'Campo_'+index" class="w-full" type="date" v-model="opcion.value" :required="opcion.Obligatorio==1" @blur="(e)=>validateDate(e,opcion)" />
                                        <!-- <flat-pickr v-else-if="!opcion.Buscador && opcion.Tipo=='Fecha/Hora'" :config="configdateTimePicker" style="width:100%" :name="'Campo_'+index" placeholder="Date Time" v-model="opcion.value" :required="opcion.Obligatorio==1" /> -->
                                        <vs-input v-if="!opcion.Buscador && opcion.Tipo=='Texto'" :name="'Campo_'+index" class="w-full" v-model="opcion.value" v-on:change="Otros().actualizarParametrosBusqueda(opcion)" :required="opcion.Obligatorio==1" />
                                        <vs-input v-else-if="!opcion.Buscador && opcion.Tipo=='Numero'" :name="'Campo_'+index" class="w-full" type="number" v-model="opcion.value" v-on:change="Otros().actualizarParametrosBusqueda(opcion)" :required="opcion.Obligatorio==1" />
                                        <vs-checkbox v-else-if="!opcion.Buscador && opcion.Tipo=='Checkbox' && opcion.ValidarPermiso && opcion.TienePermiso" v-model="opcion.value" :required="opcion.Obligatorio==1"></vs-checkbox>
                                        <vs-checkbox v-else-if="!opcion.Buscador && opcion.Tipo=='Checkbox' && opcion.ValidarPermiso != true " v-model="opcion.value" :required="opcion.Obligatorio==1"></vs-checkbox>
    
                                        <SM-Buscar :ref="`buscador_${index}`" v-else-if="opcion.Buscador" v-model="opcion.value" label="" v-bind="opcion.Buscador[0].params" :callback_buscar="e=>Otros().buscador(e,opcion)" />
                                    </template>
                                </ValidationProvider>
                                <span class="text-danger text-sm" v-show="opcion.Obligatorio==1">Campo obligatorio</span>
                            </div>
                        </template>
                    </div>
                    <vs-divider />
    
                    <div class="flex flex-row">
                        <vs-button v-if="enviarCorreo" @click.native="validate().then(submitForm(reporte.opciones,'PDF',true))" color="dark" type="border" :disabled="invalid" class="mr-1 append-text btn-addon" size="medium">
                            <font-awesome-icon :icon="['fa', 'envelope-circle-check']" />
                            Enviar PDF
                        </vs-button>
                        <vs-button v-if="generarPDF" @click="validate().then(submitForm(reporte.opciones,'PDF'))" color="danger" type="border" style="margin-right:5px" :disabled="invalid"><i class="fas fa-file-pdf "></i> Generar</vs-button>
                        <vs-button v-if="generarExcel" @click="validate().then(submitForm(reporte.opciones,'EXCEL'))" color="success" type="border" :disabled="invalid"><i class="far fa-file-excel"></i> Generar</vs-button>&nbsp;&nbsp;
                        <vs-button v-if="generarCSV_1" @click="validate().then(submitForm(reporte.opciones,'CSV'))" color="#17202a" type="border" :disabled="invalid"><i class="fas fa-file-csv"></i> Generar</vs-button>
                    </div>
                </form>
            </ValidationObserver>
        </vs-popup>
        <!-- <form> -->
        <div v-if="mostrarBuscador" class="content content-pagex">
            <vs-input label="Buscar" v-model="reporte.buscar" />
            <br>
        </div>
        <!-- </form> -->
        <slot v-bind:data="{listado: listadoFiltro, generar: generar }">
        <div v-for="(rep, index) in listadoFiltro" :key="index">
            <div @click="rep.Visible = !rep.Visible" class="mb-5" style="cursor:pointer;position:relative">
                <h3 style="color:rgb(52, 152, 219)">
                    <i style="font-size:15px" class="fas fa-layer-group mr-2"></i>
                    <div class="badge">{{rep._level[0]._level.length}}</div> {{rep.Aplicativo}}
                </h3>
            </div>
    
            <div v-if="rep.Visible">
                <div class="vx-row" v-for="(rep, index) in rep._level" :key="index">
                    <!-- {{rep}} -->
                    <div class="vx-col w-full">
                        <h3>{{rep.Funcionalidad}}</h3>
                    </div>
                    <vs-divider />
                    <div class="vx-col w-full sm:w-1/2 lg:w-1/3 mb-base" v-for="(rep, index) in rep._level" :key="index">
                        <div class="vx-card" v-if="rep.visible == '1'">
                            <div class="vx-card__collapsible-content vs-con-loading__container">
    
                                <div class="vx-card__body">
                                    <h5 class="mb-2">{{rep.Nombre}}</h5>
                                    <p class="text-grey">{{rep.descripcion}}</p>
                                    <br>
                                    <div class="flex flex-wrap">
                                        <vs-button color="primary" type="filled" @click="generar(rep.Nombre,rep.UrlApi,rep._level)">Generar</vs-button>
                                    </div>
                                </div>
                            </div>
    
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </slot>
    </vx-card>
    </template>
    
    <script>
    export default {
        data() {
            return {
                parametrosDinamicosBusqueda: {},
                configdateDatePicker: {
                    enableTime: false,
                    dateFormat: 'd/m/Y' //'d-m-Y H:i'
                },
                configdateTimePicker: {
                    enableTime: true,
                    dateFormat: 'd/m/Y H:i:S' //'d-m-Y H:i'
                },
                listado_reportes: [],
                reportes: [],
                generarPDF: true,
                generarExcel: true,
                generarCSV: true,
                reporte: {
                    generar: false,
                    popup: false,
                    buscar: '',
                    titulo: '',
                    url: '',
                    url_pdf: '',
                    opciones: [],
                    pdf: '',
                    buscador: null
                },
                emailPopup: false,
                emailConfig: null,            
            }
        },
        components: {
    
        },
        props: {
            /** 
             * Aplica filtrado de reportes adicional en base al listado de nombres de reportes 
             * Se puede colocar el nombre que se visualiza en el reporte como por ejemplo 'Estado de Cuenta' o
             * si el reporte tiene el paramento nombrereporte tambien se puede incluir, por ejemplo estado_cuenta_ajena
             */
            filtroNombres: {
                type: Array,//Arrray<string>
                required: false,
                default: null,
            },
            mostrarBuscador: {
                type: Boolean,
                default : true,
            }
        },
        computed: {
            /**
             * El filtrado de listado reportes contiene multiples niveles de agrupacion
             * |- Aplicativo
             *    |-- Funcionalidad
             *      |-- Reporte
             *          |-- Parametro de reporte
             */
            listadoFiltro() {

                let result = this.listado_reportes
                let _filtroNombre = (this.filtroNombres?? []).map(x=> this.normalizarTexto(x))
                let filtro = this.normalizarTexto(this.reporte.buscar)

                //filtrado por nombre de reporte "props"
                if (_filtroNombre.length>0){
                    result = this.$filter_nodes(
                        {
                            _level: result
                        },
                        '_level',
                        (x) =>  x.Nombre && //esta en nodo de reporte
                                (   
                                    //por nombre del reporte    encabezado
                                    _filtroNombre.includes(this.normalizarTexto(x.Nombre))
                                    //por el parámetro nombrereporte detalle
                                    || _filtroNombre.includes( this.normalizarTexto( x._level.find(param => param.Parametro = "nombrereporte").ValorDefecto ) )
                                    //url del reporte 
                                    || _filtroNombre.includes( this.obtenerPathCompleto( this.normalizarTexto(x.UrlApi) ) )
                                )
                                ? x: null
                    ) ?? {_level:[]}
                    result = result._level
                    //colocar el visible true es para expander todos los filtros de busqueda ya que la propiedad computada no actualiza al visible al hacer click
                    result.forEach( t=> t.Visible = true)
                }
                   
                //filtrado por buscardor
                if (this.reporte.buscar != '') {
                    result = this.$filter_nodes( 
                        {
                            _level: result 
                        }, 
                        '_level',
                        (x) =>      x.Nombre && this.normalizarTexto( x.Nombre ).indexOf(filtro) >= 0 
                                ||  x.Aplicativo && this.normalizarTexto( x.Aplicativo ).toLowerCase().indexOf(filtro) >= 0
                                ? x: null
                    ) ?? {_level:[]}
                    result = result._level
                    //colocar el visible true es para expander todos los filtros de busqueda ya que la propiedad computada no actualiza al visible al hacer click
                    result.forEach( t=> t.Visible = true)
                }

                return result
            },
            errorValid() {
                return this.errors.items.map(data => {
                    return data.field
                })
            },
            enviarCorreo() {
                return this.reporte.opciones.filter(x => x.Parametro == 'EnvioCorreo' && x.ValorDefecto == 'S').length > 0 && this.$validar_privilegio('ENVIARCORREO').status
            },
            generarCSV_1() {
                return this.reporte.opciones.filter(x => x.Parametro == 'EnvioCSV' && x.ValorDefecto == 'S').length > 0 && this.$validar_privilegio('GENERARCSV').status;
            }  
        },
        watch: {
            "reporte.popup"(value) {
                if (!value) {
                    Object.entries(this.$refs).map((referencia) => {
                        if (referencia[0].includes("buscador_")) {
                            referencia[1][0].buscador_valor = null
                        }
                    })
                }
            }
        },
        methods: {
            //=======================================================================================================
            // CONSULTA
            //=======================================================================================================
            Consulta: function () {
                return {
                    listado_reporte: () => {
                        this.axios.post("/app/reporte/ReporteListadoPrivilegio", {}).then(resp => {
    
                            this.listado_reportes = resp.data.json.map(data => {
    
                                return {
                                    Aplicativo: data.Aplicativo,
                                    IdFuncionalidad: data.IdFuncionalidad,
                                    Visible: data.visible,
                                    _level: data._level.map(d => {
                                        let l = []
                                        /*Se cambió el filtrado para excluir opciones con parámetros de tipo Oculto y se colocó conteo de los parámetros para mostrar los reportes (dibujar la tarjeta)*/
                                        d._level.map(data => {
                                            if (data._level.length > 0 && data.visible == 1)
                                                l.push(data)
                                            return true
                                        })
    
                                        return {
                                            Funcionalidad: d.Funcionalidad,
                                            IdFuncionalidad: d.IdFuncionalidad,
                                            _level: l.map(m => {
                                                return {
                                                    ...m,
                                                    _level: m._level.map(m => {
                                                        const extraer = this.Otros().extraerBuscador(m.Eval)
                                                        return {
                                                            ...m,
                                                            Buscador: ((extraer) ? extraer[0] : null),
                                                            Buscador_Campo: ((extraer) ? extraer[1] : null)
                                                        }
                                                    })
                                                }
                                            })
                                        }
                                    })
                                }
                            })
                        })
                    }
                }
            },
            async generar(titulo, url, opciones) {
    
                this.$refs.formValidate.reset();
                this.generarPDF = true
                this.generarExcel = true
                this.generarCSV  = true 
                this.reporte.generar = false
                this.reporte.titulo = titulo
                this.reporte.url = url
                this.reporte.opciones = opciones.map(data => {
                    return {
                        ...data,
                        value: this.calcularValor(data) //calculo de los valores por defecto
                    }
                })
    
                const arrGeneracion = this.reporte.opciones.filter(f => f.Parametro == 'generacionreporte')
                if (arrGeneracion.length > 0) {
                    const configuracionGeneracion = arrGeneracion[0].ValorDefecto
                    this.generarPDF = false
                    this.generarExcel = false
                    this.generarCSV = false
                    if (configuracionGeneracion.toUpperCase().indexOf('PDF') >= 0) this.generarPDF = true
                    if (configuracionGeneracion.toUpperCase().indexOf('EXCEL') >= 0) this.generarExcel = true
                    if (configuracionGeneracion.toUpperCase().indexOf('CSV') >= 0) this.generarCSV = true
                }
                /**********************INFORMACIÓN PARA VALIDAR PERMISOS ******
                 *  El campo valor por defecto debe quedar igual a "ValorDefecto": "--VALIDARPERMISO"
                 *  Si el campo ValorDefecto = "--VALIDARPERMISO", y no esta creado en mantenimiento, no se muestra el checkbox
                 *  El campo no debe de ser obligatorio, de lo contrario no se podra generar el reporte
                 *  Temporalmente solo se valida permisos para los checkbox, pero con ajustar los if en el html se pueden trabajar los demas.
                 *  El permiso debe de crearse manual en acciones en el mantenimiento de aplicaciones REPORT0001.
                 *  El permiso en mantenimiento debe ir todo en mayuscúlas. 
                 *  El formato del permisos es el parametro "nombrereporte" más un guión medio más Parametro
                 *  Ejemplo CARGOS_POR_ADMISION-MOSTRARPRECIOS, VER función --> "RevisarPermiso"
                 */
                //  Busqueda de permisos a vlaidar
                
                const arrar_datos_reporte = this.reporte.opciones.filter(f => f.Parametro == 'nombrereporte')
                let NombreReporte = arrar_datos_reporte[0]?.ValorDefecto;
                const arrPermisos = this.reporte.opciones.filter(f => f.ValorDefecto == '--VALIDARPERMISO')
                if (arrPermisos.length > 0) {
                    this.reporte.opciones = await Promise.all(this.reporte.opciones.map(async (m) => {
                        return {
                            ...m,
                            ValidarPermiso: (arrPermisos.filter(arrPerm => m.linea == arrPerm.linea )).length > 0 ? true : false,
                            TienePermiso: (arrPermisos.filter(arrPerm =>   m.linea == arrPerm.linea )).length > 0 ?
                                await this.RevisarPermiso(   
                                    (arrPermisos.filter(arrPerm =>   m.linea == arrPerm.linea ))[0],
                                    NombreReporte
                            ).then(resultado => {
                                    return resultado;
                                })
                                .catch(() => {
                                    return false;
                                }) : false
    
                        }
                    }))
    
                }
                this.reporte.popup = true;
            },
            async RevisarPermiso(array, NombreReporte) {
                const NombrePermiso = (NombreReporte+'-'+ array.Parametro).toUpperCase();
                return new Promise((resolve, reject) => {
                    this.$validar_funcionalidad('/REPORTE/REPORT001', NombrePermiso, (d) => {
                        if (d.status) {
                            resolve(d.status)
                        } else {
                            reject(false)
                        }
                    })
                })
            },
    
            async submitForm(opciones, formato = 'PDF', correo = false) {
    
                this.$refs.formValidate.validate().then(isValid => {
                    if (!isValid) return
    
                    let _opciones = {},
                        arrayObjetos = []
                    opciones.map(data => {
                        _opciones[data.Parametro] = ((data.Parametro.indexOf("=") < 0) ? ((data.Tipo == 'Checkbox') ? (data.value == true ? 1 : 0) : (data.value != null && data.value != "") ? data.value : data.ValorDefecto) : data.Parametro.split('=')[1])
    
                        if (Array.isArray(_opciones[data.Parametro])) _opciones[data.Parametro] = _opciones[data.Parametro].join(',')
    
                        // Transformación de fecha
                        if ((data.Tipo == 'Fecha' || data.Tipo == 'Fecha/Hora') && data.value) {
                            const a = data.value.split(' ')
                            const f = a[0].split('-')
                            if (f[0] > 1900) {
                                _opciones[data.Parametro] = `${f[2]}/${f[1]}/${f[0]}`
                            } else {
                                _opciones[data.Parametro] = `${f[0]}/${f[1]}/${f[2]}`
                            }
    
                            if (a.length > 1) {
                                _opciones[data.Parametro] = _opciones[data.Parametro] + ' ' + a[1]
                            } else if (data.Tipo == 'Fecha/Hora') {
                                _opciones[data.Parametro] = _opciones[data.Parametro] + ' 00:00:00'
                            }
                        }
                        this.calcularValorEval({
                            ...data,
                            _this: this,
                            _opciones
                        })
                        arrayObjetos.push({
                            "Etiqueta": data.Etiqueta,
                            "Parametro": data.Parametro,
                            "value": _opciones[data.Parametro]
                        })
                    })
    
                    _opciones.json = JSON.stringify(arrayObjetos)
                    // generando la configuracion del correo, si el reporte esta parametrizado para enviar por correo
                    let confEnvioCorreo = null
                    /**Parámetros de configuracion para generar el pdf en modal */
                    const confReporte = {
                        Nombre: this.reporte.titulo,
                        Opciones: _opciones,
                        Formato: formato
                    }
                    if (this.enviarCorreo) {
                        //se hacen los splice por la linea 239
                        /*_opciones.ConfigCorreo */
                        confEnvioCorreo = {
                            EnvioCorreo: _opciones.EnvioCorreo,
                            to: _opciones.EnviarA ? _opciones.EnviarA.split(',') : [],
                            cc: _opciones.CopiarA ? _opciones.CopiarA.split(',') : [],
                            bcc: _opciones.CopiarEnOculto ? _opciones.CopiarEnOculto.split(',') : [],
                            msg: _opciones.MensajeDescripcion,
                            sbjct: _opciones.Asunto,
    
                            attachLimit: 5, //_opciones.maxAdjuntos || 1,
                            attachTypes: _opciones.tipoAdjuntos || '.pdf',
                            allowAddAttachment: Boolean(_opciones.agregarAdjuntos),
                            allowDeleteAttachment: Boolean(_opciones.eliminarAdjuntos),
                            msgTitle: _opciones.MensajeTitulo,
                            msgSignature: _opciones.MensajeFirma,
                            fileName: _opciones.NombreDelArchivo
                        }
                    }
                    //si el usuario quiere enviarlo directamente sin viauslización previa
                    if (correo) {
                        this.emailPopup = true
                        this.emailConfig = {
                            ...confEnvioCorreo,
                            reporte: confReporte
                        } //se asignan las propiedades al componete de correo y los parámetros del reporte
                    } else {
                        _opciones.ConfigCorreo = confEnvioCorreo //para que el reporte modal envie la configuracion luego de generar el pdf
                        this.$reporte_modal(confReporte)
                            .catch(() => {
    
                            })
                    }
    
                })
            },
            Otros: function () {
                return {
                    validarBuscador: (evaluador) => {
                        if (evaluador === '') return false
                        const separacion = evaluador.split('***')
                        if (separacion.length !== 2) return false
                        let texto = (separacion[0]) ? separacion[0] : ''
                        return [((texto) ? `[${texto.replace(/(\r\n|\n|\\n|\r)/gm, '')}]` : null), separacion[1]]
                    },
                    extraerBuscador: (evaluador) => {
                        try {
                            const texto = this.Otros().validarBuscador(evaluador)
                            if (!texto) return null
                            const conversion = JSON.parse(texto[0])
                            return [conversion, texto[1]]
                        } catch (error) {
                            return null
                        }
                    },
                    actualizarParametrosBusqueda: (campo) => {
                        const evalArreglo = campo.Eval.split('***')
                        if (evalArreglo.length < 2) return false
    
                        const sm_buscar_arreglo = evalArreglo[1].split('(')
                        const nombre_buscador = sm_buscar_arreglo[0]
                        const campo_asignar = sm_buscar_arreglo[1].split(')')[0]
    
                        if (campo.value && campo.value.trim().length > 0) {
                            let findBuscador = false;
                            for (var buscador in this.parametrosDinamicosBusqueda) {
                                if (buscador == nombre_buscador) {
                                    this.parametrosDinamicosBusqueda[nombre_buscador][campo_asignar] = campo.value.trim()
                                    findBuscador = true;
                                    break;
                                }
                            }
                            if (!findBuscador) {
                                this.parametrosDinamicosBusqueda[nombre_buscador] = {}
                                this.parametrosDinamicosBusqueda[nombre_buscador][campo_asignar] = campo.value.trim()
                            }
    
                        }
    
                        this.reporte.opciones.forEach(parametro => {
                            if (parametro.Buscador) {
                                for (var buscador in this.parametrosDinamicosBusqueda) {
                                    if (buscador == parametro.Buscador[0].nombre) {
                                        parametro.Buscador[0].params.api_filtro = {
                                            ...parametro.Buscador[0].params.api_filtro,
                                            ...this.parametrosDinamicosBusqueda[buscador]
                                        }
                                        break;
                                    }
                                }
                            }
                        })
                    },
                    buscador: (reporte, parametros) => {
                        const nombre = parametros.Buscador[0].nombre
                        this.reporte.opciones.forEach(item => {
    
                            const ev = item.Eval.split('***')
                            if (ev.length < 2) return false
    
                            const buscar = ev[1].split('(')
                            const buscador = buscar[0]
                            const campos = buscar[1].split(')')[0]
    
                            if (buscador == nombre) {
                                let valor = null
                                let divisor = null
                                // No es un campo con split
                                if (campos.indexOf('[') == -1) {
                                    if (!Array.isArray(reporte)) {
                                        valor = reporte[campos]
                                        item.value = valor
                                    } else {
                                        valor = reporte.map(m => m[campos])
                                        item.value = valor
                                    }
                                } else {
                                    const c = campos.split('[')
                                    valor = reporte[c[0]]
                                    divisor = c[1].replace(']', '').replace(/"/g, '').split(',')
    
                                    item.value = valor.split(divisor[0])[divisor[1]]
                                }
                            }
                        })
                    },
                }
            },
            /**
             * Recibe la configuración de un parámetro de reporte y asigna el valor por defecto calculado, si ocurre algun error en la evaluación retorna nulo
             * @param {*} rptOpt Objeto con el parámetro del reporte
             */
            calcularValor(rptOpt) {
                try {
                    return eval(rptOpt.ValorDefecto)
                } catch (err) {
                    return null
                }
            },
    
            calcularValorEval(e) {
                if (e && e.Eval && e.Eval.includes('calcular#')) {
                    try {
                        //crea una funcion y la ejecuta
                        (new Function('e', e.Eval.split('#')[1]))(e)
                    } catch {
                        return null
                    }
                }
    
            },
            /**Recibe la configuracion de un paámetro de reporte y devuelve las reglas de validación de vee validate */
            getValidatorRules(rptOpt) {
                let rules = []
                if (rptOpt.Obligatorio == 1)
                    rules.push('required')
                // if(rptOpt.Tipo == 'Numero')
                //     rules.push('numeric')
                if (rptOpt.Tipo == 'Fecha')
                    rules.push('fecha')
    
                return rules.join('|')
            },
            /**Funcion para validar formatos invalidos del input vs-input a veces permite 31-02-2023 por ejemplo pero el texto se sigue mostrando */
            validateDate(e, e1) {
                if (e.target.validationMessage)
                    this.$nextTick(() => {
                        e1.value = new Date().toJSON().slice(0, e.target.type == 'datetime-local' ? 19 : 10)
                        this.$nextTick(() => {
                            e1.value = null
                        })
                    })
            },
            /**
             * obteine la ultima seccion de la ruta del reporte por ejemplo
             * http://reporte-abcd:8080/CartaRemision	        ----> /CartaRemision
             * http://reporte-abcd:8080/CartaRemision/otro/1    ----> /CartaRemision/otro/1
             * http://reporte-abcd:8080	                        ----> (cadena vacía)
             * @param urlStr cadena de una url
             */
            obtenerPathCompleto(urlStr) {
                try {
                    const url = new URL(urlStr);
                    const path = url.pathname;
                return path === '/' ? '' : path;
                } catch (e) {
                    //console.error('URL inválida:', e);
                    return '';
                }
            },
            /** Remueve caracteres especiales y convierte a minusculas para findes de comparacion de cadenas */
            normalizarTexto(str) {
                return typeof str == 'string' ? str.normalize('NFD').replace(/[\u0300-\u036f]/g, '').toLowerCase() : str
            },
        },
        created() {
            this.Consulta().listado_reporte()
    
        }
    }
    </script>
    
    <style scoped>
    .badge {
        background-color: rgb(41, 128, 185);
        display: inline-block;
        height: 15px;
        width: 15px;
        border-radius: 100%;
        font-size: 10px;
        line-height: 13px;
        text-align: center;
        position: absolute;
        color: white;
        left: 7px;
        border: 1px solid white;
    }
    </style>
    