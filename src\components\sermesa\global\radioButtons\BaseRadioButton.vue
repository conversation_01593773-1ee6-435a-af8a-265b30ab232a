
<!-- BaseRadioButton.vue -->
<template>
  <label class="base-radio-label" :for="option.value">
    
    <input
      class="base-radio-input"
      type="radio"
      :value="option"
      :id="id"
      @change="onChange"
      :name="groupName" 
      :checked="optionSelected === option.Descripcion"/>
    <span>
      {{ option.Descripcion  }}
    </span>  
    
  </label>
</template>
  

<script>
export default {
  name:"BaseRadioButton",
  props: {
    option: {
      required: true,
      type: Object
    },
    id:{
      required: true,
      type: String
    },
    groupName :{
      required: true,
      type: String
    },
    optionSelected: {
      required: false,
      type: String
    }
  },
  methods: {
    onChange() {
      this.$emit("radio-selected", this.option);
    },
  },

};
</script>

<style scoped>
input[type="radio"] {
  margin-right: 20px !important;
  margin-left: 15px !important;

}

label {
  cursor: pointer !important;
  margin-right: 20px;
  margin-left: 20px;
  vertical-align: middle;
}
</style>