<template>
  <div class="comandas-container">
    <div style="display: flex;">
      <DxButton
        :width="130"
        text="Regrescar"
        type="normal"
        icon="refresh"
        @click="cargarComandas"
      />
      &nbsp;
      <vs-chip color="#fdebd0" >
        <vs-avatar color="#ff9800" icon=""/>
        <span  style="color:black;font-size: medium;" >Mesa</span> 
      </vs-chip>
      &nbsp;
      <vs-chip color="#E3F2FD" >
          <vs-avatar color="#2196F3" icon=""/>
          <span  style="color:black;font-size: medium;" >Pedido</span> 
      </vs-chip>
      &nbsp;
      <vs-chip color="#d4efdf" >
          <vs-avatar color="#4caf50" icon=""/>
          <span style="color:black;font-size: medium;" >En Proceso</span> 
      </vs-chip>
    </div>

      <!-- Sección de Comandas Pendientes -->
    <div class="comandas-section">
      <vx-card>
      <h3>Comandas Pendientes</h3>
      <div class="cards-container">
      <div
          v-for="comanda in filteredComandas.pending"
          :key="'pending-' + comanda.Comanda"
          :class="getCardStyle(comanda)"
        >
          <div class="comanda-header">
            <span class="comanda-number">Comanda #{{ comanda.Comanda }}</span>
            <span class="comanda-time">{{ comanda.Hora }}</span>
          </div>
          
          <div v-show="comanda.Pedido === 'P'" >
            <span><a style="color: black;font-size: medium;">Pedido de:</a>{{ comanda.Nombre }}</span>
          </div>

          <div class="comanda-details">
          <div v-for="(item, index) in comanda.Detalle" :key="index" class="comanda-item">
            <div>
              <span class="item-quantity"> x{{ item.CantidadDes }}</span>&nbsp;
              <span class="item-name">{{ item.NombreProducto }}</span>
            </div>
            <div v-if="item.Nota" class="item-notes">
              <span> {{ item.Nota }}</span>
            </div>
          </div>
          </div>
          <div style="text-align:right;">
            <DxButton
              :width="50"
              icon="arrowright"
              type="default"
              @click="UpdatePreparando(comanda)" 
            />
            <!-- como puedo campurar los datos de mi comanda seleccionada  -->
          </div>
        </div>
      </div>
      </vx-card>
    </div>

    <!-- Sección de Comandas Entregadas -->
    <div class="comandas-section">
      <vx-card>
        <h3>Comandas En Proceso</h3>
        <div class="cards-container">
          <div v-for="comanda in filteredComandas.delivered" 
            :key="'delivered-'+ comanda.Comanda" 
            class="comanda-card delivered">
            <div class="comanda-header">
              <span class="comanda-number">Comanda #{{ comanda.Comanda }}</span>
              <span class="comanda-time">{{ comanda.Hora }}</span>
            </div>
            <div class="comanda-details">
            <div v-for="(item, index) in comanda.Detalle" :key="index" class="comanda-item">
              <div>
                <span class="item-quantity"> x{{ item.CantidadDes }}</span>&nbsp;
                <span class="item-name">{{ item.NombreProducto }}</span>
              </div>
              <div v-if="item.Nota" class="item-notes">
                <span> {{ item.Nota }}</span>
              </div>
            </div>
            <div style="text-align:right;">
              <DxButton
                :width="50"
                icon="check"
                type="success"
                @click="UpdateTerminsado(comanda)"
              />
            </div>
          </div>
          </div>
        </div>
      </vx-card>
    </div>

  </div>
</template>

<script>
export default {
  data() {
    return {
      Comandas:[],
      filteredComandas: {
        pending: [],
        delivered: []
      }
    };
  },
  watch: {
    Comandas: {
      immediate: true,
      handler(newVal) {
        this.filterComandas(newVal);
      }
    }
  },
  methods: {

    getCardStyle(comanda) {
      return comanda.Pedido === 'P' ? 'comanda-card card-p' : 'comanda-card card-other';
    },

    cargarComandas() {
      this.axios.post("/app/v1_ExpedienteEvolucion/ConsultaComandas", { Opcion: 4 })
        .then(resp => {
          if (resp.data && resp.data.json) {
            this.Comandas = resp.data.json;
            this.filterComandas(this.Comandas);
            this.$forceUpdate();
          }
        })
    },

    filterComandas(comandas) {
      // Función para agrupar comandas por número de comanda y filtrar por estado
      const groupByComanda = (comandas, statusFilter) => {
        const agrupadas = {};

        comandas
          .filter(statusFilter)
          .sort((a, b) => new Date(b.Hora) - new Date(a.Hora))
          .forEach(item => {
            const comandaNum = item.Comanda;
            if (!agrupadas[comandaNum]) {
              agrupadas[comandaNum] = {
                Comanda: comandaNum,
                Hora: item.Hora,
                Pedido: item.Pedido,
                Nombre: item.Nombre,
                Detalle: []
              };
            }
            agrupadas[comandaNum].Detalle.push(item);
            
            // Actualizar la hora si algún item tiene una hora más reciente
            if (new Date(item.Hora) > new Date(agrupadas[comandaNum].Hora)) {
              agrupadas[comandaNum].Hora = item.Hora;
            }
          });

        // Convertir a array y ordenar por hora
        return Object.values(agrupadas).sort((a, b) => new Date(a.Hora) - new Date(b.Hora));
      };

      // Filtrar comandas pendientes (Status = 'P' y Proceso ≠ 'S')
      this.filteredComandas.pending = groupByComanda(comandas, c => c.Status === 'P' && c.Proceso !== 'S');
      
      // Filtrar comandas entregadas (Proceso = 'S')
      this.filteredComandas.delivered = groupByComanda(comandas, c => c.Proceso === 'S');
    },

    UpdatePreparando(comanda) {
      this.$vs.dialog({
        type: 'confirm',
        color: 'warning',
        title: '¿Está seguro?',
        text: `¿Desea Iniciar el Proceso de la comanda ${comanda.Comanda}?`,
        acceptText: 'Sí',
        cancelText: 'No',
        accept: () => {
          this.axios.post('/app/v1_ExpedienteEvolucion/UpdateComanda', {
            Opcion: 8,
            Comanda: comanda.Comanda,
          }).then(() => {
            this.cargarComandas();
            this.$vs.notify({
             title: 'Exitosa',
             text: 'La acción fue Exitosa!.',
             color: 'success',
             icon: 'check_circle'
           });
          })
        },
        cancel: () =>{
           this.$vs.notify({
             title: 'Cancelado',
             text: 'La acción fue cancelada.',
             color: 'warning',
             icon: 'warning'
           });
        }
      });
    },

    UpdateTerminsado(comanda) {
      this.$vs.dialog({
        type: 'confirm',
        color: 'success',
        title: '¿Confirmar acción?',
        text: `¿Desea marcar como terminada la comanda ${comanda.Comanda}?`,
        acceptText: 'Sí',
        cancelText: 'No',
        accept: () => {
          this.axios.post('/app/v1_ExpedienteEvolucion/UpdateComanda', {
            Opcion: 9,
            Comanda: comanda.Comanda,
          }).then(() => {
            this.cargarComandas();
            this.$vs.notify({
             title: 'Exitosa',
             text: 'La acción fue Exitosa!.',
             color: 'success',
             icon: 'check_circle'
           });
          })
        },
        cancel: () =>{
           this.$vs.notify({
             title: 'Cancelado',
             text: 'La acción fue cancelada.',
             color: 'warning',
             icon: 'warning'
           });
        }
      });
    }

  },
  beforeMount() {
    this.intervalId = setInterval(() => {
     this.cargarComandas();
    }, 60000);  
  },

  beforeDestroy() {
    if (this.intervalId) {
        clearInterval(this.intervalId);
    }
  },

  mounted() {
    this.cargarComandas();
 
  }
};
</script>

<style>
.comandas-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
}

.comandas-section {
  margin-bottom: 2rem;
  max-height: 700px; /* Puedes ajustar este valor según lo que necesites */
  overflow-y: auto;
}

.comandas-section h3 {
  color: #333;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #eee;
}

.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
}

.comanda-card {
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
  height: fit-content; /* Ajusta el alto al contenido */
  min-height: fit-content; /* Mínimo necesario, pero puede crecer si hay más contenido */
}

.comanda-card:hover {
  transform: translateY(-5px);
}

.comanda-card.card-p {
  border-left: 4px solid #2196F3 ;
  background-color: #E3F2FD ;
}

.comanda-card.card-other {
  border-left: 4px solid #FF9800 ;
  background-color: #FDEBD0 ;
}
 

.comanda-card.delivered {
  border-left: 4px solid #4caf50;
  background-color:  #d4efdf; /* #e8f5e9; */
  opacity: 0.8;
  }

.comanda-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px dashed #ccc;
}

.comanda-number {
  font-weight: bold;
  color: #333;
}

.comanda-time {
  color: #666;
  font-size: 0.9rem;
}

.comanda-details {
  margin-bottom: 1rem;
}

.comanda-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  padding: 0.3rem 0;
  border-bottom: 1px solid #eee;
}

.comanda-item:last-child {
  border-bottom: none;
}

.item-name {
  color: black !important;
  text-align: center !important;
  font-size: small;
}

.item-quantity {
  margin-left: 1rem;
  font-weight: bold;
}

.item-notes {
  display: block;
  width: 100%;
  font-size: 0.8rem;
  color: #666;
  font-style: italic;
}

.delivered-badge {
  text-align: center;
  padding: 0.3rem;
  background-color: #4caf50;
  color: white;
  border-radius: 4px;
  font-size: 0.9rem;
  margin-top: 0.5rem;
}

.delivered-badge i {
  margin-right: 0.3rem;
}

.comanda-item {
  display: block; /* Esto hace que cada item ocupe su propia línea */
  margin-bottom: 10px; /* Espacio opcional entre items */
}

/* Estilos adicionales para mejor visualización */
.item-name {
  font-weight: bold;
}

.item-quantity {
  margin-left: 5px;
}

.item-notes {
  font-style: italic;
  color: #666;
  margin-top: 3px;
}

@media (max-width: 768px) {
  .cards-container {
    grid-template-columns: 1fr;
  }
}

</style>