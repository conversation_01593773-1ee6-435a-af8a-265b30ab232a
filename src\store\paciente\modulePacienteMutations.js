export function getDefaultState() {

    return {
        PacienteData: {
            Empresa: '',
            Codigo: '',
            Primer<PERSON>ombre: '',
            SegundoNombre: '',
            PrimerApellido: '',
            SegundoApellido: '',
            ApellidoCasada: '',
            Sexo: '',
            EstadoCivil: '',
            Religion: '',
            Nacimiento: '',
            LugarNacimiento: '',
            Nacionalidad: '',
            Direccion: '',
            Ciudad: '',
            Telefonos: '',
            Profesion: '',
            LugarTrabajo: '',
            TelefonoTrabajo: '',
            Padre: '',
            Madre: '',
            Conyuge: '',
            Internado: '',
            DireccionFactura: '',
            NombreFactura: '',
            Nit: '',
            NumeroArchivo: '',
            Status: '',
            UltimaAdmision: '',
            SerieRayos: '',
            NumeroRayos: '',
            Carnet: '',
            Socio: '',
            Pasaporte: '',
            NumeroCarnet: '',
            NumeroUltraSonido: '',
            SeguroSocial: '',
            NumeroExpedienteClinico: '',
            TieneSeguroMedico: '',
            Aseguradora: '',
            Etina: '',
            Celular: '',
            Email: '',
            DireccionTrabajo: '',
            Ocupacion: '',
            EmergenciaQuien: '',
            EmergenciaDireccion: '',
            EmergenciaTelefono: '',
            EmergenciaCelular: '',
            DatosFamNombreMadre: '',
            DatosFamNombrePadre: '',
            ConyugeDireccion: '',
            ConyugeTelefono: '',
            FechaPrimeraConsulta: '',
            Marcador: '',
            NombrePatrono: '',
            Nopatronal: '',
            PesoNacerLb: '',
            PesoNacerOz: '',
            HoraNac: '',
            TallaNac: '',
            IdEmpresa: '',
            IdCliente: '',
            ClienteSolicitaNoSerLLamado: true,
            PacienteInvalido: false,
            PacienteBlackList: false,
            PctHemodialisis: false,
            PctSinHuellas: true,
            ClaseAfiliado: '',
            Horario: '',
            Etnia: '',
            FechaPrimeraVisita: '',
            FechaUltimaVisita: '',
            CalleResidencia: '',
            AvenidaResidencia: '',
            NoCasaResidencia: '',
            ZonaResidencia: '',
            ColoniaResidencia: '',
            EdificioResidencia: '',
            DeptoResidencia: '',
            MunicipioResidencia: '',
            CalleTrabajo: '',
            AvenidaTrabajo: '',
            NoCasaTrabajo: '',
            ZonaTrabajo: '',
            ColoniaTrabajo: '',
            EdificioTrabajo: '',
            OficinaTrabajo: '',
            DeptoTrabajo: '',
            MunicipioTrabajo: '',
            PadreResponsable: '',
            MadreResponsable: '',
            DPIPadreResponsable: '',
            DPIMadreResponsable: '',
            TelefonoPadreResponsable: '',
            TelefonoMadreResponsable: '',
            TelefonoMedicoCabecera: '',
            OtrosMedicos: '',
            TelefonoOtrosMedicos: '',
            TipoDeSangre: '',
            AlergiasMedicamento: '',
            AlergiasComidas: '',
            AlergiasSustanciasAmbiente: '',
            AlergiasSustanciasPiel: '',
            AlergiasOtros: '',
            AlimentosNoCarne: '',
            AlimentosNoFrutas: '',
            AlimentosNoVegetales: '',
            AlimentosVegetariano: '',
            AlimentosNoOtros: '',
            DPI: '',
            MedicoCabecera: '',
            SaludSiempre: '',
            Vacunado: '',
            PaqueteQX: 0
        }

    }
}