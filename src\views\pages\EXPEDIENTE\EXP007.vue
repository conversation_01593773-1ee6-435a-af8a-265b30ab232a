<template>
<div>
    <vs-row vs-justify="flex-end">
        <ExpedienteGridToolBar v-bind="$props" :visible="true" :pdfExportItems="exportItems" :report-param="reporte" :showItems="['exportPdf', 'refresh']" @refresh="Recargar" />
    </vs-row>
    <vs-row class="p-2 w-full">
        <vs-col vs-w="2" vs-justify="center" vs-align="center" class="pr-2">
            <vx-card :title="textoFiltro">
                <!-- Se dejó toda la configuración, ya que si se usa la configuración global, se reinicia el pageIndex cada vez que se selecciona una fecha -->
                <DxDataGrid :ref="dataGridFechaKey" v-bind="DefaultDxGridConfiguration" :headerFilter="{visible:true, allowSearch:true}" :data-source="admisiones" :width="'100%'" :height="'100%'" @selection-changed="onSelectionChanged">
                    <DxSelection mode="single" />
                    <DxColumn :width="120" data-field="Admision" caption="Admisión" alignment="center" />
                    <DxColumn :width="150" data-field="FechaAntecedente" caption="Registro" format="dd/MM/yyyy HH:mm" data-type="date" alignment="center" />
                </DxDataGrid>
            </vx-card>
        </vs-col>
        <vs-col vs-justify="center" vs-align="center" vs-w="10">
            <vx-card>
                <vs-collapse accordion type="margin">
                    <vs-collapse-item ref="collapse" v-for="(item, index) in tabsAntecedentes" v-bind:key="index" icon-arrow="label">
                        <div slot="header" style="display: flex; flex-wrap: wrap; justify-content: start; align-items: center;">
                            <font-awesome-icon :icon="['fas', item.icon]" class="i-size pr-2" /> {{item.name}}
                        </div>
                        <div>

                            <DxDataGrid :ref="dataGridRefKey" :filter-sync-enabled="true" :filter-value="admision" v-bind="DefaultDxGridConfiguration" :headerFilter="{visible:false,allowSearch:true}" :searchPanel="{visible: admision === null ? true : false }" :data-source="antecedentesPorCategoria[item.data]" :height="'100%'">
                                <DxSelection mode="single" />

                                <DxColumn :width="150" data-field="Admision" caption="Admisión" alignment="center" :visible="admision===null" />
                                <DxColumn :width="150" data-field="FechaRegistro" caption="Fecha registro" format="dd/MM/yyyy HH:mm" data-type="date" alignment="center" />
                                <DxColumn :width="150" data-field="FechaAntecedente" caption="Fecha antecedente" format="dd/MM/yyyy HH:mm" data-type="date" alignment="center" :visible="item.data!== 'Ingreso' && item.data !== 'DiagnosticoProblema'" />
                                <DxColumn :visible="item.data !== 'DiagnosticoProblema' && item.data !== 'AntGinecologico' && item.data !== 'Ingreso'" :width="150" data-field="Contenido" :caption="item.name" :allowHeaderFiltering="false" alignment="center" />
                                <DxColumn :visible="item.data === 'DiagnosticoProblema'" cell-template="Diagnostico" :width="150" :caption="item.name" :allowHeaderFiltering="false" alignment="center" />
                                <DxColumn :visible="item.data === 'AntGinecologico'" cell-template="Gineco" :width="150" :caption="item.name" :allowHeaderFiltering="false" alignment="center" />
                                <DxColumn :visible="item.data === 'Ingreso'" cell-template="Ingreso" :width="150" :caption="item.name" :allowHeaderFiltering="false" alignment="center" />

                                <template #Diagnostico="{data}">
                                    <div style="white-space:pre-wrap; text-align: left">
                                        <p>
                                            <b>Problema encontrado: </b>
                                        </p>
                                        <p class="pl-4 ml-4">
                                            {{ data.data.Contenido.ProblemaEncontrado }}
                                        </p>
                                        <p>
                                            <b>Diagnóstico: </b>
                                        </p>
                                        <p class="pl-4 ml-4">
                                            {{ data.data.Contenido.DiagnosticoIngreso }}
                                        </p>
                                        <p>
                                            <b>Seguro: </b>
                                        </p>
                                        <p class="pl-4 ml-4">
                                            {{ data.data.Contenido.Seguro }}
                                        </p>
                                    </div>
                                </template>
                                <template #Gineco="{data}">
                                    <div style="white-space:pre-wrap; text-align: left">
                                        <p>
                                            <b>Gestas: </b>
                                            {{ data.data.Contenido.AntGinecoGestas }}
                                        </p>
                                        <p>
                                            <b>Partos: </b>
                                            {{ data.data.Contenido.AntGinecoPartos }}
                                        </p>
                                        <p>
                                            <b>Abortos: </b>
                                            {{ data.data.Contenido.AntGinecoAbortos }}
                                        </p>
                                        <p>
                                            <b>Cesáreas: </b>
                                            {{ data.data.Contenido.AntGinecoCesareas }}
                                        </p>
                                        <p>
                                            <b>Fecha última regla: </b>
                                            {{ data.data.Contenido.AntGinecoFUR }}
                                        </p>
                                        <p>
                                            <b>Ginecológicos: </b>
                                        </p>
                                        <p class="pl-4 ml-4">
                                            {{ data.data.Contenido.AntGinecologico }}
                                        </p>
                                    </div>
                                </template>
                                <template #Ingreso="{data}">
                                    <div style="white-space:pre-wrap; text-align: left">
                                        <p>
                                            <b>Motivo de consulta: </b>
                                        </p>
                                        <p class="pl-4 ml-4">
                                            {{ data.data.Contenido.MotivoConsulta }}
                                        </p>
                                        <p>
                                            <b>Historia de enfermedad: </b>
                                        </p>
                                        <p class="pl-4 ml-4">
                                            {{ data.data.Contenido.HistoriaEnfermedad }}
                                        </p>
                                    </div>
                                </template>
                            </DxDataGrid>
                        </div>
                    </vs-collapse-item>
                </vs-collapse>
            </vx-card>
        </vs-col>
    </vs-row>
</div>
</template>

<script>
import EXPBase from './EXPBase.vue'
import {
    DxDataGrid,
    DxColumn,
    DxSelection,
} from 'devextreme-vue/data-grid'
import 'devextreme-vue/text-area'
import CustomStore from 'devextreme/data/custom_store'
import 'devextreme-vue/popup';

const dataGridRefKey = 'antPreviosGrid'
const dataGridFechaKey = 'gridFiltro'

import {
    DefaultDxGridConfiguration
} from './data'
export default {
    name: 'AntecedentesPrevios',
    extends: EXPBase,
    components: {
        DxDataGrid,
        DxColumn,
        DxSelection,
        ExpedienteGridToolBar: () => import('./ExpedienteGridToolBar.vue'),
    },
    data() {
        return {
            tabsAntecedentes: [{
                    name: 'Alérgicos',
                    icon: 'head-side-cough',
                    value: 1,
                    data: 'AntAlergicos'
                },
                {
                    name: 'Medicamentos',
                    icon: 'pills',
                    value: 2,
                    data: 'AntMedicamentos'
                },
                {
                    name: 'Médicos',
                    icon: 'user-doctor',
                    value: 3,
                    data: 'AntMedicos'
                },
                {
                    name: 'Quirúrgicos',
                    icon: 'syringe',
                    value: 4,
                    data: 'AntQuirurgicos'
                },
                {
                    name: 'Traumáticos',
                    icon: 'crutch',
                    value: 5,
                    data: 'AntTraumaticos'
                },
                {
                    name: 'Otros antecedentes',
                    icon: 'otter',
                    value: 6,
                    data: 'AntOtros'
                },
                {
                    name: 'Datos ingreso',
                    icon: 'clipboard-user',
                    value: 7,
                    data: 'Ingreso'
                },
                {
                    name: 'Problema encontrado/Diagnóstico',
                    icon: 'laptop-medical',
                    value: 8,
                    data: 'DiagnosticoProblema'
                }
            ],
            DefaultDxGridConfiguration,
            admisiones: [],
            antecedentes: [],
            diagnosticos: [],
            motivos: [],
            /**Registro de antecedente que se selecciona el el grid */
            antecedentesDataSource: new CustomStore({
                key: 'id',
                load: () => {
                    return this.antecedentes
                },
            }),
            numericOptionGineco: {
                max: 100,
                min: 0,
                format: '#0',
            },
            //configuracion de menu de exportar pdf, estos son los nombres de los reportes :)
            exportItems: [{
                text: 'Antecedentes Previos',
                reportName: 'Antecedentes Previos',
                key: 'previos'
            }],
            /**Listado de antecedentes asociados a la admisión se hizo en otra variable ya que el custom store de devextreme no funciona bien ya que sighos hace dos peticiones y no solo una*/
            antecedentesPrevios: [],
            previosLoaded: false,
            dataGridRefKey,
            dataGridFechaKey,
            /**Indica si se muestran los antecedentes previos o actuales en el grid */
            vistaPrevia: false,
            textoFiltro: 'Filtrar admisión',
            admision: null,
            reporte: {
                SerieAdmision: this.SerieAdmision,
                CodigoAdmision: this.CodigoAdmision,
            }
        }
    },
    props: {
        SerieAdmision: null,
        CodigoAdmision: null,
        NumeroExpediente: null,
        Sexo: null,
    },
    methods: {
        Cargar() {
            if (this.ExpedienteCargado) {
                this.CargarPrevios()
            } else {
                this.antecedentesPrevios = []
                this.previosLoaded = false
                this.refreshDataGrid()
            }
        },
        CargarPrevios() {
            if (!this.previosLoaded) ///los previo en teoria no cambiarian en el momento, por ello se trabaja con lo ya cargado
            {
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaAntecedentesPreviosAntecedentes', {
                        Serie: this.SerieAdmision,
                        Admision: this.CodigoAdmision,
                    })
                    .then(resp => {
                        this.antecedentes = resp.data.json.map((x, index) => {
                            return {
                                id: index,
                                Admision: x.Admision,
                                FechaExpediente: x.FechaExpediente,
                                FechaAntecedente: x.FechaAntecedente,
                                AntAlergicos: this.$limpiar_saltos_tabulares(x.AntAlergicos),
                                AntMedicamentos: this.$limpiar_saltos_tabulares(x.AntMedicamentos),
                                AntGinecoGestas: this.$limpiar_saltos_tabulares(x.AntGinecoGestas),
                                AntGinecoPartos: this.$limpiar_saltos_tabulares(x.AntGinecoPartos),
                                AntGinecoAbortos: this.$limpiar_saltos_tabulares(x.AntGinecoAbortos),
                                AntGinecoCesareas: this.$limpiar_saltos_tabulares(x.AntGinecoCesareas),
                                AntMedicos: this.$limpiar_saltos_tabulares(x.AntMedicos),
                                AntQuirurgicos: this.$limpiar_saltos_tabulares(x.AntQuirurgicos),
                                AntTraumaticos: this.$limpiar_saltos_tabulares(x.AntTraumaticos),
                                AntOtros: this.$limpiar_saltos_tabulares(x.AntOtros),
                                AntGinecologico: this.$limpiar_saltos_tabulares(x.AntGinecologico),
                            }
                        })
                        let objeto = {}
                        this.antecedentes.map((item) => {
                            objeto[item.Admision.trim()] = {
                                Admision: item.Admision.trim(),
                                FechaAntecedente: item.FechaExpediente
                            }
                        })
                        this.admisiones = Object.values(objeto)
                    })

                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaAntecedentesPreviosMotivoConsulta', {
                        Serie: this.SerieAdmision,
                        Admision: this.CodigoAdmision,
                    })
                    .then(resp => {
                        this.motivos = resp.data.json.map((x, index) => {
                            return {
                                id: index,
                                Admision: x.Admision,
                                FechaExpediente: x.FechaExpediente,
                                MotivoConsulta: this.$limpiar_saltos_tabulares(x.MotivoConsulta),
                                HistoriaEnfermedad: this.$limpiar_saltos_tabulares(x.HistoriaEnfermedad),
                                Seguro: this.$limpiar_saltos_tabulares(x.Seguro),
                            }
                        })
                    })

                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaAntecedentesPreviosDiagnostico', {
                        Serie: this.SerieAdmision,
                        Admision: this.CodigoAdmision,
                    })
                    .then(resp => {
                        this.diagnosticos = resp.data.json.map((x, index) => {
                            return {
                                id: index,
                                Admision: x.Admision,
                                FechaExpediente: x.FechaExpediente,
                                ProblemaEncontrado: this.$limpiar_saltos_tabulares(x.ProblemaEncontrado),
                                DiagnosticoIngreso: this.$limpiar_saltos_tabulares(x.Diagnostico),
                                Seguro: this.$limpiar_saltos_tabulares(x.Seguro),
                            }
                        })
                    })
            } else {
                this.antecedentesDataSource.load()
                    .then(
                        () => {
                            this.refreshDataGrid()
                            this.previosLoaded = true
                        },
                        (error) => {
                           // console.error('error loading data customstore' + JSON.stringify(error))
                        }
                    )
            }
        },
        formatFecha(fecha) {
            fecha = fecha.split('-');
            const year = fecha[0];
            const month = fecha[1];
            const day = fecha[2].split('T')[0];
            let horas = fecha[2].split('T');
            horas = horas[1].split(':');
            const h = horas[0]
            const m = horas[1]
            const s = horas[2]

            let res = day + '/' + month + '/' + year + ' ' + h + ':' + m + ':' + s

            return res;
        },
        refreshDataGrid() {
            this.dataGrid.refresh()
        },

        //Método para detectar cuando se selecciona una fila de la tabla de filtrado de fecha
        onSelectionChanged(e) {
            if (e.selectedRowsData.length > 0) {
                this.textoFiltro = e.selectedRowsData[0].Admision; //Texto a mostrar en la tarjeta del filtro
                this.admision = ["Admision", "=", e.selectedRowsData[0].Admision]; //Variable para poder realizar el filtrado

                let serie = e.selectedRowsData[0].Admision.substring(0, 1)
                let codigo = e.selectedRowsData[0].Admision.substring(1, e.selectedRowsData[0].Admision.length)

                this.reporte = {
                    SerieAdmision: this.SerieAdmision,
                    CodigoAdmision: this.CodigoAdmision,
                    SerieAdmisionFiltrado: serie,
                    CodigoAdmisionFiltrado: codigo,
                }
            }
        },

        Recargar() {

            this.$refs.collapse.map(c => c.maxHeight = '0px')
            this.$refs.antPreviosGrid.map(c => {
                c.$_instance.clearSelection()
                c.$_instance.clearGrouping()
                c.$_instance.clearSorting()
                c.$_instance.clearFilter()
            })
            // this.Cargar()
            this.textoFiltro = 'Filtrar admisión'
            this.admision = null

            this.dataGridFecha.clearSelection()
            this.dataGridFecha.clearGrouping()
            this.dataGridFecha.clearSorting()
            this.dataGridFecha.clearFilter()

            this.reporte = {
                SerieAdmision: this.SerieAdmision,
                CodigoAdmision: this.CodigoAdmision,
            }
        }
    },
    watch: {
        //tambien se puede con la serie y numero de admisión
        'NumeroExpediente'(newval) {
            if (newval != undefined)
                this.Cargar()
        },
    },
    mounted() {
        if (this.Sexo === 'F') {
            this.tabsAntecedentes.push({
                name: 'Ginecológicos',
                icon: 'venus',
                value: 9,
                data: 'AntGinecologico'
            })
        }
    },
    computed: {
        dataGrid: function () {
            return this.$refs[dataGridRefKey].instance
        },
        dataGridFecha: function () {
            return this.$refs[dataGridFechaKey].instance;
        },
        antecedentesPorCategoria: function () {
            let res = {
                AntUltimaComida: [],
                AntAlergicos: [],
                AntMedicamentos: [],
                AntGinecologico: [],
                AntMedicos: [],
                AntQuirurgicos: [],
                AntTraumaticos: [],
                AntOtros: [],
                AntSistemas: [],
                AntFamiliares: [],
                AntHabitos: [],
                Ingreso: [],
                DiagnosticoProblema: [],
            }

            //Mapea la información de Antecedentes
            this.antecedentes.map(
                x => {
                    const item = {
                        Admision: x.Admision,
                        FechaRegistro: x.FechaExpediente,
                        FechaAntecedente: x.FechaAntecedente,
                        Contenido: null
                    }
                    if (x.AntAlergicos) {
                        item.Contenido = x.AntAlergicos
                        res.AntAlergicos.push({
                            ...item
                        })
                    }
                    if (x.AntMedicamentos) {
                        item.Contenido = x.AntMedicamentos
                        res.AntMedicamentos.push({
                            ...item
                        })
                    }
                    if (x.AntMedicos) {
                        item.Contenido = x.AntMedicos
                        res.AntMedicos.push({
                            ...item
                        })
                    }
                    if (x.AntQuirurgicos) {
                        item.Contenido = x.AntQuirurgicos
                        res.AntQuirurgicos.push({
                            ...item
                        })
                    }
                    if (x.AntTraumaticos) {
                        item.Contenido = x.AntTraumaticos
                        res.AntTraumaticos.push({
                            ...item
                        })
                    }
                    if (x.AntOtros) {
                        item.Contenido = x.AntOtros
                        res.AntOtros.push({
                            ...item
                        })
                    }
                    if (Boolean(x.AntGinecoGestas) || Boolean(x.AntGinecoPartos) || Boolean(x.AntGinecoAbortos) || Boolean(x.AntGinecoCesareas) || Boolean(x.AntGinecologico) || Boolean(x.AntGinecoFUR)) {
                        item.Contenido = ({
                            AntGinecoGestas: x.AntGinecoGestas,
                            AntGinecoPartos: x.AntGinecoPartos,
                            AntGinecoAbortos: x.AntGinecoAbortos,
                            AntGinecoCesareas: x.AntGinecoCesareas,
                            AntGinecologico: x.AntGinecologico,
                            AntGinecoFUR: x.AntGinecoFUR
                        })
                        res.AntGinecologico.push({
                            ...item
                        })
                    }

                    return item
                }
            )

            this.motivos.map(
                x => {
                    const item = {
                        Admision: x.Admision,
                        FechaRegistro: x.FechaExpediente,
                        FechaAntecedente: x.FechaAntecedente,
                        Contenido: null
                    }
                    if (Boolean(x.MotivoConsulta) || Boolean(x.HistoriaEnfermedad)) {
                        item.Contenido = ({
                            MotivoConsulta: x.MotivoConsulta,
                            HistoriaEnfermedad: x.HistoriaEnfermedad
                        })
                        res.Ingreso.push({
                            ...item
                        })
                    }
                    return item
                }
            )

            this.diagnosticos.map(
                x => {
                    const item = {
                        Admision: x.Admision,
                        FechaRegistro: x.FechaExpediente,
                        FechaAntecedente: x.FechaAntecedente,
                        Contenido: null
                    }
                    if (Boolean(x.ProblemaEncontrado) || Boolean(x.DiagnosticoIngreso) || Boolean(x.Seguro)) {
                        item.Contenido = ({
                            ProblemaEncontrado: x.ProblemaEncontrado,
                            DiagnosticoIngreso: x.DiagnosticoIngreso,
                            Seguro: x.Seguro
                        })
                        res.DiagnosticoProblema.push({
                            ...item
                        })
                    }

                    return item
                }
            )
            return res
        }
    },
}
</script>

<style>
.i-size {
    font-size: 25px;
    padding-bottom: 5px;
}

.vs-collapse-item--header {
    padding-bottom: 0;
}

.con-content--item {
    padding-top: 0 !important;
}
</style>
