<template>
    <div>
        <vs-popup :title="tituloPaqueteEncabezado" :active.sync="isVisibleEncabezado" >
            <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                <div class="flex flex-wrap-nowrap">
                    <div class="default-div">
                        <ValidationProvider name="codigo" rules="required|max:14" class="required" v-slot="{ errors }">
                            <label class="typo_label">Código</label>
                            <vs-input class="codigo d-inline-block" v-model="codigo" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="isModificar" />
                        </ValidationProvider>
                    </div>
                    <div class="default-div">
                        <ValidationProvider name="nombre" rules="required|max:40" class="required" v-slot="{ errors }">
                            <label class="typo_label">Nombre</label>
                            <vs-input class="nombre d-inline-block" v-model="nombre" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                </div>
                <div class="flex flex-wrap-nowrap">
                    <div class="default-div">
                        <ValidationProvider name="valor" rules="max:27" class="required" v-slot="{ errors }">
                            <label class="typo_label">Valor</label>
                            <vs-input class="valor d-inline-block" v-model="valor" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" disabled/>
                        </ValidationProvider>
                    </div>
                    <div class="default-div">
                        <ValidationProvider name="valorHospital" rules="required|max:27" class="required" v-slot="{ errors }">
                            <label class="typo_label">Valor Hospital</label>
                            <vs-input class="valorHospital d-inline-block" v-model="valorHospital" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" disabled />
                        </ValidationProvider>
                    </div>
                </div>
                <div class="flex flex-wrap-nowrap">
                    <div class="default-div">
                        <ValidationProvider name="descuento" rules="numero_entero|max:2" class="required" v-slot="{ errors }">
                            <label class="typo_label">Descuento Porcentaje</label>
                            <vs-input class="descuento d-inline-block" v-model="descuento" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                </div>
                <div class="flex flex-wrap div-container">
                    <div>
                        <vs-button @click="handleSubmit(GuardarPaquete)" :disabled="invalid" color="success" class="btn btn-primary mx-3 my-3" icon-pack="fas" icon="fa-save">
                            Guardar
                        </vs-button>
                    </div>
                </div>
            </ValidationObserver>
        </vs-popup>
        <vs-popup :title="tituloPaqueteDetalle" :active.sync="isVisibleDetalle" >
            <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                <div class="flex flex-wrap-nowrap">
                    <ValidationProvider name="producto" rules="required" class="required" v-slot="{ errors }">
                        <div class="buscar">
                            <SM-Buscar v-model.trim="codigoProducto" 
                                label="Producto" 
                                api="app/v1_JefeCaja/BusquedaProductos"
                                :api_campos="['Codigo', 'Nombre']"
                                :api_titulos="['Codigo', 'Nombre']" 
                                :api_filtro="{Opcion: 'C', SubOpcion: '1', Hospital: '0'}"
                                api_campo_respuesta="Codigo"
                                api_campo_respuesta_mostrar="Nombre" 
                                :api_preload="true" :disabled_texto="true" 
                                :danger="errors.length>0" :dangertext="(errors.length>0)?errors[0]:null"                   
                            />
                        </div>
  
                    </ValidationProvider>
                    <ValidationProvider name="automatico" rules="required" class="required" v-slot="{ errors }">
                        <div class="multi-span">
                            <label class="typo_label">Automático</label>
                            <multiselect class="automatico" v-model="selectAutomatico" :options="listadoAutomatico" placeholder="Seleccione Deshabilitada" track-by="Codigo" :show-labels="false" :custom-label="DatosMostrarAutomatico"></multiselect>
                            <span style="position:relative;top:-2px" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                        </div>
                    </ValidationProvider>     
                </div>
                <div class="flex flex-wrap div-container">
                    <div class="default-div">
                        <ValidationProvider name="precioUnitario" rules="required|max:20" class="required" v-slot="{ errors }">
                            <label class="typo_label">Precio Unitario</label>
                            <vs-input class="precioUnitario d-inline-block" v-model="precioUnitario" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"  />
                        </ValidationProvider>
                    </div>
                    <div class="default-div">
                        <ValidationProvider name="cantidad" rules="required|max:20" class="required" v-slot="{ errors }">
                            <label class="typo_label">Cantidad</label>
                            <vs-input class="cantidad d-inline-block" v-model="cantidad" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"  />
                        </ValidationProvider>
                    </div>
                    <div class="default-div">
                        <ValidationProvider name="unidadMedida" rules="required|max:20" class="required" v-slot="{ errors }">
                            <label class="typo_label">Unidad Medida</label>
                            <vs-input class="unidadMedida d-inline-block" v-model="unidadMedida" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" disabled />
                        </ValidationProvider>
                    </div>
                    <div class="default-div">
                        <ValidationProvider name="valorProducto" rules="required|max:20" class="required" v-slot="{ errors }">
                            <label class="typo_label">Valor</label>
                            <vs-input class="valorProducto d-inline-block" v-model="valorProducto" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" disabled />
                        </ValidationProvider>
                    </div>
                </div>
                <div class="flex flex-wrap div-container">
                    <div>
                        <vs-button @click="handleSubmit(GuardarDetallePaquete)" :disabled="invalid" color="success" class="btn btn-primary mx-3 my-3" icon-pack="fas" icon="fa-save">
                            Guardar
                        </vs-button>
                    </div>
                </div>
            </ValidationObserver>
        </vs-popup>
        <vx-card title="Paquetes">
            <div>
                <vs-row>
                    <vs-col vs-w="6">
                        <vs-button @click="NuevoPaquete" color="success" icon-pack="fas" icon="fa-plus-circle" >Nuevo Paquete</vs-button>
                    </vs-col>
                    <vs-col vs-w="6">
                        <vs-button @click="NuevoDetallePaquete" color="success" icon-pack="fas" icon="fa-plus-circle" :disabled="paqueteSelected">Nuevo Paquete Detalle</vs-button>
                    </vs-col>
                </vs-row>
                <vs-divider border-style="solid" color="dark"></vs-divider>
                <vs-row>
                    <vs-col vs-w="6" class="tablas">
                        <vs-table class="tabla-paquete" maxHeight="600px" max-items="10" search filter pagination stripe :data="tablaPaquetes" v-model="selected" @selected="handleSelected">
                            <template slot="header">
                                <h2>Paquetes</h2>
                            </template>
                            <template slot="thead">
                                <th>Codigo</th>
                                <th>Nombre</th>
                                <th>Valor</th>
                                <th>Valor Hospital</th>
                                <th>Descuento Procentaje</th>
                                <th>Editar|Eliminar</th>
                            </template>
                            <template slot-scope="{data}">
                                <vs-tr :key="indextr" v-for="(tr, indextr) in data" :data="tr" >
                                    <vs-td>{{ tr.Codigo }}</vs-td>
                                    <vs-td>{{ tr.Nombre }}</vs-td>
                                    <vs-td>Q.{{ tr.Valor }}</vs-td>
                                    <vs-td>{{ tr.ValorHospital }}</vs-td>
                                    <vs-td>{{ tr.Porcentaje }}</vs-td>
                                    <vs-td class="th_editar">
                                        <vs-button @click="EditaPaquete(tr)" color="primary" size="small" icon-pack="fas" icon="fa-edit" class="btn_editar mr-1"></vs-button>
                                        <vs-button @click="EliminarPaquete(tr)" color="danger" size="small" icon-pack="fas" icon="fa-trash-alt" class="btn_editar mr-1"></vs-button>
                                    </vs-td>
                                </vs-tr>
                            </template>
                        </vs-table>
                    </vs-col>
                    <vs-col vs-w="6" class="tablas">
                        <vs-table class="tabla-paquete" maxHeight="600px" max-items="10" search filter pagination stripe :data="tablaPaquetesDetalle">
                            <template slot="header">
                                <h2>{{ tituloDetalle }}</h2>
                            </template>
                            <template slot="thead">
                                <th>Codigo</th>
                                <th>Nombre</th>
                                <th>Automático</th>
                                <th>Categoría</th>
                                <th>Precio Unitario</th>
                                <th>Cantidad</th>
                                <th>Unidad Medida</th>
                                <th>Valor</th>
                                <th width="250px">Editar|Eliminar</th>
                            </template>
                            <template slot-scope="{data}">
                                <vs-tr :key="indextr" v-for="(tr, indextr) in data" :data="tr" >
                                    <vs-td>{{ tr.Producto }}</vs-td>
                                    <vs-td>{{ tr.NombreProducto }}</vs-td>
                                    <vs-td>{{ tr.Automatico }}</vs-td>
                                    <vs-td>{{ tr.Categoria }}</vs-td>
                                    <vs-td>{{ tr.PrecioUnitario }}</vs-td>
                                    <vs-td>{{ tr.Cantidad }}</vs-td>
                                    <vs-td>{{ tr.UnidadMedida }}</vs-td>
                                    <vs-td>Q.{{ tr.Valor }}</vs-td>
                                    <vs-td class="th_editar">
                                        <vs-button @click="EditarProducto(tr)" color="primary" size="small" icon-pack="fas" icon="fa-edit" class="btn_editar mr-1"></vs-button>
                                        <vs-button @click="EliminarDetallePaquete(tr)" color="danger" size="small" icon-pack="fas" icon="fa-trash-alt" class="btn_editar mr-1"></vs-button>
                                    </vs-td>
                                </vs-tr>
                            </template>
                        </vs-table>
                    </vs-col>
                </vs-row>     
            </div>
        </vx-card>
    </div>
</template>
<script>
    import Multiselect from "vue-multiselect"
    import "vue-multiselect/dist/vue-multiselect.min.css"

    export default {
        data() {
            return{
                tablaPaquetes: [],
                tablaPaquetesDetalle: [],
                selected: [],
                paqueteSelected: true,
                tituloDetalle: '',
                isVisibleEncabezado: false,
                isVisibleDetalle: false,
                tituloPaqueteEncabezado: '',
                tituloPaqueteDetalle: '',
                isModificar: false,
                codigo: '',
                nombre: '',
                valor: '0',
                valorHospital: '0',
                descuento: '',
                isModificarProducto: false,
                codigoProducto:'',
                selectAutomatico: '',
                listadoAutomatico: [
                    {
                        Codigo: 'S',
                        Nombre: 'Si se carga en la admisión automáticamente'
                    },
                    {
                        Codigo: 'N',
                        Nombre: 'No se carga automáticamente'
                    }
                ],
                precioUnitario: '',
                cantidad: 1,
                unidadMedida: '',
                valorProducto: '',
                lineaProductos: [],
                nuevaLineaProducto: '',
                linea: '',
                paquete: ''
            }
        },
        components: {
            Multiselect
        },
        methods: {
            ObtenerPaquetes(){
                this.axios.post('/app/v1_JefeCaja/ObtenerPaquetesEncabezado', {
                    "Opcion": "C",
                    "SubOpcion": "1",
                    "Hospital": "0"
                })
                .then( resp => {
                    this.tablaPaquetes = resp.data.json.map(m => {
                        return{
                            ...m
                        }
                    })
                })
            },
            handleSelected(){
                this.tituloDetalle = `PRODUCTOS: ${this.selected.Nombre}`
                this.axios.post('/app/v1_JefeCaja/ObtenerPaquetesDetalle', {
                    "Opcion": "C",
                    "SubOpcion": "2",
                    "Hospital": "0",
                    "Paquete": this.selected.Codigo
                })
                .then( resp => {
                    this.tablaPaquetesDetalle = resp.data.json.map(m => {
                        this.lineaProductos.push(m.Linea)
                        return{
                            ...m
                        }
                    })
                    this.paqueteSelected = false
                })
            },
            NuevoPaquete(){
                this.isVisibleEncabezado = true
                this.tituloPaqueteEncabezado = 'Nuevo Paquete'
                this.isModificar = false
            },
            EditaPaquete(item){
                this.tituloPaqueteEncabezado = 'Modificar Paquete'
                this.isVisibleEncabezado = true
                this.isModificar = true
                this.codigo = item.Codigo
                this.nombre = item.Nombre
                this.valor = item.Valor
                this.valorHospital = item.ValorHospital
                this.descuento = item.Porcentaje
            },
            GuardarPaquete() {
                if(this.isModificar){
                    this.axios.post('/app/v1_JefeCaja/AlctualizarPaquete', {
                        "Opcion": "A",
                        "SubOpcion": "1",
                        "Hospital": "0",
                        "Codigo": this.codigo,
                        "Nombre": this.nombre,
                        "Valor": this.valor,
                        "ValorHospital": this.valorHospital,
                        "Descuento": this.descuento
                    })
                    .then(resp => {
                        this.codigo = null
                        this.nombre = null
                        this.valor = '0'
                        this.valorHospital = '0'
                        this.descuento = null
                        this.isVisibleEncabezado = false
                        this.ObtenerPaquetes()
                        this.$vs.notify({
                            color: 'success',
                            title: 'Paquetes',
                            text: resp.data.json[0].descripcion
                        })
                    })
                }else{
                    this.axios.post('/app/v1_JefeCaja/AlmacenarPaquete', {
                        "Opcion": "I",
                        "SubOpcion": "1",
                        "Hospital": "0",
                        "Codigo": this.codigo,
                        "Nombre": this.nombre,
                        "Valor": this.valor,
                        "ValorHospital": this.valorHospital,
                        "Descuento": this.descuento
                    })
                    .then( resp => {
                        if(resp.data.json[0].tipo_error == '1') {
                            this.$vs.dialog({
                                color: 'danger',
                                title: 'Paquetes',
                                text: resp.data.json[0].descripcion,
                                acceptText: 'Aceptar'
                            })
                        }else{
                            this.codigo = null
                            this.nombre = null
                            this.valor = '0'
                            this.valorHospital = '0'
                            this.descuento = null
                            this.ObtenerPaquetes()
                            this.isVisibleEncabezado = false
                            this.$vs.notify({
                                color: 'success',
                                title: 'Paquetes',
                                text: resp.data.json[0].descripcion
                            })
                        }
                    })
                }
            },
            EliminarPaquete(item){
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmacion',
                    text: `¿Desea eleminar el paquete: ${ item.Codigo }?`,
                    acceptText: 'Aceptar',
                    accept: () => {
                        this.axios.post('/app/v1_JefeCaja/EliminarPaquete', {
                            "Opcion": "E",
                            "SubOpcion": "1",
                            "Hospital": "0",
                            "Codigo": item.Codigo
                        })
                        .then(resp => {
                            if(resp.data.json[0].tipo_error == '0'){
                                this.$vs.notify({
                                    color: 'success',
                                    title: 'Paquetes',
                                    text: resp.data.json[0].descripcion
                                })
                                this.ObtenerPaquetes()
                                this.selected = ''
                                // this.handleSelected()
                                this.tablaPaquetesDetalle = []
                                this.tituloDetalle = ''
                                this.paqueteSelected = true
                            }else{
                                this.$vs.notify({
                                    color: 'danger',
                                    title: 'Paquetes',
                                    text: resp.data.json[0].descripcion
                                })
                            }
                        })
                    }
                })
                
            },
            DatosMostrarAutomatico({
                Nombre
            }) {
                return `${Nombre}`
            },
            ProductosDetalle(valor){
                this.axios.post('/app/v1_JefeCaja/BusquedaProductosDetalle', {
                    "Opcion": "C",
                    "SubOpcion": "3",
                    "Hospital": "0",
                    "Codigo": valor
                })
                .then(resp => {
                    const {Precio, UnidadMedida, UnidadVenta} = resp.data.json[0]
                    this.precioUnitario = Precio
                    this.unidadMedida = UnidadMedida
                    this.cantidad = UnidadVenta
                    this.valorProducto = Precio * UnidadVenta
                    this.selectAutomatico = {
                        Codigo: 'N',
                        Nombre: 'No se carga automáticamente'
                    }
                })
            },
            NuevoDetallePaquete(){
                // console.log(this.selected.Codigo)
                this.tituloPaqueteDetalle = 'Nuevo Producto Paquete'
                this.isVisibleDetalle = true
                this.nuevaLineaProducto = (Math.max(...this.lineaProductos) + 1)
                this.isModificarProducto = false
                // console.log(this.nuevaLineaProducto)
            },
            EditarProducto(item){
                this.paquete = item.Paquete
                this.codigoProducto = item.Producto
                this.selectAutomatico = {
                    Codigo: item.Automatico,
                    Nombre: item.Automatico == 'N' ? 'No se carga automáticamente' : 'Si se carga en la admisión automáticamente'
                }
                this.precioUnitario = item.PrecioUnitario
                this.cantidad = item.Cantidad
                this.unidadMedida = item.UnidadMedida
                this.valorProducto = item.Valor
                this.linea = item.Linea

                this.tituloPaqueteDetalle = 'Modificar Producto Paquete'
                this.isVisibleDetalle = true
                this.isModificarProducto = true
                
            },
            GuardarDetallePaquete(){
                if(this.isModificarProducto){
                    this.axios.post('/app/v1_JefeCaja/ActualizaProductosDetalle', {
                        "Opcion": "A",
                        "SubOpcion": "2",
                        "Hospital": "0",
                        "Paquete": this.selected.Codigo,
                        "Linea": this.linea,
                        "Codigo": this.codigoProducto,
                        "PrecioUnitario": this.precioUnitario,
                        "Cantidad": this.cantidad,
                        "UnidadMedida": this.unidadMedida,
                        "ValorProducto": this.valorProducto,
                        "Automatico": this.selectAutomatico.Codigo
                    })
                    .then(resp => {
                        this.codigo = null
                        this.selectAutomatico = null
                        this.codigoProducto = null
                        this.precioUnitario = null
                        this.cantidad = null
                        this.unidadMedida = null
                        this.valorProducto = null
                        this.handleSelected()
                        this.ObtenerPaquetes()
                        this.isVisibleDetalle = false
                        this.$vs.notify({
                            color: 'success',
                            title: 'Paquetes',
                            text: resp.data.json[0].descripcion
                        })
                    })
                }else{
                    this.axios.post('/app/v1_JefeCaja/AlmacenarProductosDetalle', {
                        "Opcion": "I",
                        "SubOpcion": "2",
                        "Hospital": "0",
                        "Paquete": this.selected.Codigo,
                        "Linea": this.nuevaLineaProducto,
                        "Codigo": this.codigoProducto,
                        "PrecioUnitario": this.precioUnitario,
                        "Cantidad": this.cantidad,
                        "UnidadMedida": this.unidadMedida,
                        "ValorProducto": this.valorProducto,
                        "Automatico": this.selectAutomatico.Codigo
                    })
                    .then(resp => {
                        this.nuevaLineaProducto = null
                        this.lineaProductos = []
                        this.codigoProducto = null
                        this.precioUnitario = null
                        this.cantidad = null
                        this.unidadMedida = null
                        this.valorProducto = null
                        this.selectAutomatico = null
                        this.handleSelected()
                        this.ObtenerPaquetes()
                        this.isVisibleDetalle = false
                        this.$vs.notify({
                            color: 'success',
                            title: 'Paquetes',
                            text: resp.data.json[0].descripcion
                        })
                    })
                }
            },
            EliminarDetallePaquete(item){
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmacion',
                    text: `¿Desea eliminar el producto ${item.NombreProducto}?`,
                    acceptText: 'Aceptar',
                    accept: () => {
                        this.axios.post('/app/v1_JefeCaja/EliminarProductosDetalle', {
                            "Opcion": "E",
                            "SubOpcion": "2",
                            "Hospital": "0",
                            "Paquete": item.Paquete,
                            "Linea": item.Linea,
                            "Codigo": item.Producto
                        })
                        .then(resp => {
                            if(resp.data.json[0].tipo_error == 0){
                                this.$vs.notify({
                                    color: 'success',
                                    title: 'Paquetes',
                                    text: resp.data.json[0].descripcion
                                })
                                this.handleSelected()
                                this.ObtenerPaquetes()
                            }else{
                                this.$vs.notify({
                                    color: 'danger',
                                    title: 'Paquetes',
                                    text: resp.data.json[0].descripcion
                                })
                            }
                        })
                    }
                })
            }
        },
        watch: {
            codigoProducto(valor, oldValor){
                if(valor != oldValor){
                    if(valor != null && oldValor != null && this.isModificarProducto){
                        this.ProductosDetalle(valor)
                    }else if(valor != null && !this.isModificarProducto){
                        this.ProductosDetalle(valor)
                    }
                }
            },
            precioUnitario(valor){
                if(valor){
                    this.valorProducto = this.precioUnitario * this.cantidad
                }
            },
            cantidad(valor){
                if(valor){
                    this.valorProducto = this.precioUnitario * this.cantidad
                }
            },
            isVisibleEncabezado(valor){
                if(!valor){
                    this.codigo = null
                    this.nombre = null
                    this.valor = '0'
                    this.valorHospital = '0'
                    this.descuento = null
                }
            },
            isVisibleDetalle(valor){
                if(!valor){
                    this.codigoProducto = null
                    this.selectAutomatico = null
                    this.precioUnitario = '0'
                    this.cantidad = null
                    this.unidadMedida = null
                    this.valorProducto = '0'

                }
            }
        },
        activated() {
            this.ObtenerPaquetes()
        }
    }
</script>
<style lang="scss" scoped>
    .tablas {
        padding: 5px;
        box-shadow:0 2px 3px; 
        color:rgba(0,0,0,0.4);
        border-radius:5px;
        .tabla-paquete {
            border-spacing: 0;
        }
    }
    .default-div {
        padding-left: 5px;

        .nombre{
            width: 450px;
        }
        .precioUnitario{
            width: 150px;
        }
        .cantidad{
            width: 125px;
        }
        .unidadMedida{
            width: 165px;
        }
        .valorProducto{
            width: 100px;
            padding-left: 5px;
        }

    }
    .buscar{
        width: 460px;
        padding: 5px;
    }
    .multi-span {
        padding: 5px;
        .automatico{
            width: 460px;
        }
    }
    .th_editar {
        text-align: center;
        width: 150px;
        .btn_editar{
            display: inline-block;
        }
    }
    .contenedor-tabla {
    overflow: auto;
    width: 100%;
    padding: 2px;
    min-height: 230px;

}
</style>