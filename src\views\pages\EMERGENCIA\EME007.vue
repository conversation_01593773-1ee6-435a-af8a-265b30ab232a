<template>
    <div>
        
        <vx-card :title="`Cotización de Servicios - ${sesion.sesion_sucursal_nombre}`">                            
            <vx-card>    
                <vs-row>

                    <vx-input-group>
                        <vs-col vs-type="flex" vs-w="6">
                            <div class="ws-full p-2">
                                <validationProvider name="Nivel de Precio">
                                    <validationProvider name="Nivel de precios">
                                        <vs-input label="Nivel de precios" class="4/5"
                                            v-model="NivelSeleccionado" readonly />
                                    </validationProvider>
                                </validationProvider>
                            </div>
                        </vs-col>

                        <vs-col vs-type="flex" vs-w="6">
                            <div class="ws-full p-2">
                                <BuscaProductos v-model="niveles.Nombre"
                                    label="Desc. Nivel de precio" type="input" id="ListaPrecios"
                                    api="app/inventario/ListaNivelesPrecios" api_campo_respuesta_mostrar="Nombre"
                                    :api_campos="['Nivel', 'Nombre']" :api_titulos="['Nivel', 'Nombre']"
                                    api_campo_respuesta="Nombre" :api_campo_respuesta_estricto="false"
                                    :api_preload="true" :disabled_texto="true"
                                    :callback_buscar="DespuesSeleccionarNivelPrecio" :api_filtro="{Pagina: 1,                                        Busqueda: '',                                        Nombre: ''}">
                                </BuscaProductos>
                            </div>
                        </vs-col>
                    </vx-input-group>

                <!-- </vs-row>

                <vs-row> -->
                    <vx-input-group>
                        <vs-col vs-type="flex" vs-w="6">
                            <div class="ws-full p-2">
                                <validationProvider name="PorcentajeCoseguro">
                                    <vs-input v-model="PorcentajeCoaseguro" class="w-full" type="number" 
                                        label="Porcentaje Coseguro" v-on:change="ConsultaProductos().calculaTotal()"/>
                                </validationProvider>
                            </div>
                        </vs-col>

                        <vs-col vs-type="flex" vs-w="6">
                            <div class="ws-full  p-2">
                                <validationProvider name="MontoCopago">
                                    <vs-input v-model="MontoCopago" class="w-full" label="Monto Copago" type="number" 
                                        v-on:change="ConsultaProductos().calculaTotal()" />
                                </validationProvider>
                            </div>
                        </vs-col>
                    </vx-input-group>

                    <div class="flex flex-wrap gap-4 p-4 bg-gray-100 rounded-lg ml-6">                        
                    <div class="w-full sm:w-1/2 md:w-1/3 lg:w-1/6">
                        <h4 class="font-semibold">TOTAL</h4>
                        <p class="font-bold text-xl">{{ totalCargos.toLocaleString('es-GT', { style: 'currency', currency: 'GTQ' }) }}</p>
                    </div>

                    <div class="w-full sm:w-1/2 md:w-1/3 lg:w-1/4 ml-12">
                        <h4 class="font-semibold">Coaseguro +</h4>
                        <p class="font-bold text-xl">{{ MCoaseguro.toLocaleString('es-GT', { style: 'currency', currency: 'GTQ' }) }}</p>
                    </div>

                    <div class="w-full sm:w-1/2 md:w-1/3 lg:w-1/5">
                        <h4 class="font-semibold">Copago =</h4>
                        <p class="font-bold text-xl">{{ MCopago.toLocaleString('es-GT', { style: 'currency', currency: 'GTQ' }) }}</p>
                    </div>

                    <div class="w-full sm:w-1/2 md:w-1/3 lg:w-1/6">
                        <h4 class="font-semibold">Participación</h4>
                        <p class="font-bold text-xl">{{ SumaTotal.toLocaleString('es-GT', { style: 'currency', currency: 'GTQ' }) }}</p>
                    </div>
                    </div>  
                
                </vs-row> 

                    <div>   
                                Preparaciones:
                                <vs-textarea class="textarea clearfix  " id="Preparaciones" v-model="Preparaciones" readonly rows=4 />
                    </div>                  
                
            </vx-card>    

            <!-- CARGOS AGREGADOS -->
            <div class="flex flex-wrap mt-4" >                
                <div class="sm:w-full md:w-full lg:w-full xl:w-7/12  p-0">
                    <div class="w-full p-0">
                        <h3 style="color: #3498db; margin: 0; line-height: 1;"><b>Productos a Cargar</b></h3>
                    </div>
                    <vs-table2 ref="tablacargos" max-items="8" search pagination :data="cargos" height="470px">
                        <template slot="thead">
                            <th order="Código" width="10%">Código</th>
                            <th order="Nombre" width="33%" >Nombre</th>
                            <th order="Nombre" width="9%" >U. Medida</th>
                            <th  order="NivelSeleccionado" width="16%" >Nivel</th>
                            <th order="Precio" width="12%" > Precio.U</th>
                            <th order="CoaseguroS" width="12%" >Coaseguro</th>
                            <th order="Cantidad" width="15%">Cant.</th>
                            <th order="Accion" width="5%"></th>
                        </template>
                        <template slot-scope="{data}">
                            <tr :key="tr.Codigo+indextr" v-for="(tr, indextr) in data"  style="line-height: 1; height: 30px;">
                                <vs-td2 class = "w-1/12"  style="padding: 0; line-height: 1;">
                                    {{ tr.Codigo }}
                                </vs-td2>
                                <vs-td2 class = "w-3/12" style="line-height: 1;">
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">
                                        <small>
                                            {{ tr.Nombre }}
                                        </small>
                                    </div>
                                </vs-td2>
                                <vs-td2 class = "w-1/12" style="word-wrap: break-word; white-space: wrap; padding: 0; line-height: 1;">
                                    {{ tr.UnidadMedida }}
                                </vs-td2>
                                
                                <vs-td2 class = "w-1/12" style="word-wrap: break-word; white-space: wrap; padding: 0; line-height: 1;">                                    
                                     {{ tr.Nivel}}
                                </vs-td2>                                                           

                                <vs-td2 v-if="tr.Precio==0.00" class="w-1/12" style="padding: 0; line-height: 1;"> 
                                    <vs-input type="number" class="w-full" v-model="tr.PrecioLibre"/>                      
                                </vs-td2>
                                <vs-td2 v-if="tr.Precio!=0.00" class="w-1/12" style="padding: 0; line-height: 1;">
                                    {{parseFloat(tr.Precio).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                </vs-td2>


                                <vs-td2 class = "w-1/12" style="word-wrap: break-word; white-space: wrap; padding: 0; line-height: 1;">                                    
                                    {{parseFloat(tr.CoaseguroS).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}                                    
                                </vs-td2>    

                                <vs-td2 class="w-4/12" style="padding: 0; line-height: 1;">
                                    <ValidationProvider rules="required|numero_min:1" v-slot="{ errors }" class="required">                                        
                                        <vs-input class="w-full" type="number" v-model="tr.Cantidad"  
                                                  v-on:blur="validarMaxMin(tr)" @keyup.enter="validarMaxMin(tr)"  @keyup.tab="validarMaxMin(tr)"
                                                  :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                    </ValidationProvider>                                    
                                </vs-td2>
                                <vs-td2 class = "w-1/12" width='2%' style="padding: 0; line-height: 1; transform: scale(0.9);">
                                    <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="EliminarProducto(tr)"></vs-button>
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
                    <!-- {{cargos}} -->
    
                </div>
    
                <!-- LISTADO DE PRODUCTOS -->
                <div class="sm:w-full md:w-full lg:w-full xl:w-5/12  pl-2">
                    <div class="w-full pl-2">
                        <h3 style="color: #3498db;"><b>Lista de Productos</b></h3>
                    </div>
                    <div>
                        <vs-table2 max-items="10" search pagination :data="productos" height="470px">
                            <template slot="thead">
                                <th order="Código" width="10%">Código</th>
                                <th order="Nombre" width="70%">Nombre</th>
                                <th order="Precio" width="20%">Precio</th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data" @click="TempProductoSelec = [];ObservacionesRegion='';(tr.Existencia>0 || tr.Tipo == 'S')? Interfaz().agregaProducto(tr) :null; " :style="{'cursor':tr.Existencia<=0?'default':'pointer','opacity':tr.Existencia<=0?0.5:1}">
                                    <vs-td2>
                                        {{ tr.Codigo }}
                                    </vs-td2>
                                    <vs-td2>
                                        <div style="word-wrap: break-word;white-space: wrap;">
                                            {{ tr.Nombre }}
                                        </div>
                                    </vs-td2>
                                    <vs-td2 style="min-width: 120px;">
                                        {{parseFloat(tr.Precio).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                </div>
            </div>
            <vs-divider></vs-divider>
            <div class="flex bottom">
                <vs-button color="danger" class="mr-5" @click="LimpiarOrden">Limpiar datos</vs-button>
                <vs-spacer></vs-spacer>
            </div>


        </vx-card>

    </div>
    </template>
    
    <script>
    import BuscaProductos from '@/components/sermesa/global/SMBuscar.vue'

    export default {
        components:{
            BuscaProductos,
        },
        data() {    
            return {
                
                info: {
                    serie:null,
                    numeroAdmision:null,  
                    verificacionSeguro:null,                  
                    nivelprecio: null,
                    bodega: null,
                    tipoOrden: null,
                    filtroTipoOrden: [],
                    filtroTipoOrdenGeneral: [],
                    medico:null,
                    nombreMedico:'SIN REFERENCIA',
                    observaciones:null,
                    fechaEstudio:null,
                    habitacion: null,
                    paciente: null
                },
                cargos: [],
                productos: [],  
                niveles: {
                                Nivel: '',
                                Nombre: '',
                                TipoDescuento: ''
                },                
                Preparaciones: [],
                TotalSinSeguro: 0,
                MontoCopago: 0,
                MCopago: 0,     
                MCoaseguro: 0,
                MontoCoaseguro: 0,
                SumaTotal: 0,         // Total asegurado
                NivelSeleccionado: '',
                NombreNivel: '',
                CoaseguroS: '',
                PorcentajeCoaseguro: 0
            }
        },
        computed: {
            totalCargos() {
                const total = this.cargos.reduce(
                (acumulador, cargo) =>
                    acumulador + ((cargo.PrecioLibre ? parseFloat(cargo.PrecioLibre) : parseFloat(cargo.Precio)) * cargo.Cantidad),
                0
                );

                this.MCoaseguro =  (  total * parseFloat((this.PorcentajeCoaseguro / 100))).toFixed(2);
                this.SumaTotal = ((this.MCoaseguro === 0 && this.MCopago === 0)? 0 : parseFloat(this.MCoaseguro)) + parseFloat(this.MontoCopago);

                return parseFloat(total.toFixed(2));
            },
            sesion() {
                return this.$store.state.sesion
            }
        },
        methods: {   
            DespuesSeleccionarNivelPrecio( datos){
                this.NivelSeleccionado = datos.Nivel
                this.NombreNivel = datos.Nombre
                this.DatosProductos(datos.Nivel)
            },
            fillPreparation(examen, preparacion) {
                if (this.Preparaciones === '' || this.Preparaciones === null || this.Preparaciones === undefined) {
                    this.Preparaciones = ''                
                    }
            
                this.Preparaciones += ' ' + examen.toUpperCase() + ' ' + preparacion.toUpperCase() + '\r\n'
                },       
            EliminarProducto(producto){
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',                    
                    acceptText: 'Aceptar',
                    cancelText: 'Cancelar',
                    text: '¿Dar eliminar el producto  \'' + producto.Nombre + '\' con cantidad ' + producto.Cantidad + ' ? ',
                    accept: () => {
                        let indice = this.cargos.indexOf(producto)
                        if(indice > -1){
                            this.cargos.splice(indice,1)  

                            this.ConsultaProductos().calculaTotal()

                            this.Preparaciones = ''
                            Object.keys(this.cargos).forEach(key => {
                                if (this.cargos[key].Preparacion && this.cargos[key].Preparacion.trim() !== ''
                                ) {
                                this.fillPreparation( this.cargos[key].Nombre, this.cargos[key].Preparacion)
                                }
                            });     

                        }                        
                    }
                })
                
            },
            DatosProductos( nivelPrecioSelec){

                if(!nivelPrecioSelec){
                     this.productos = []
                     return
                 }
                this.axios.post('/app/emergencia/BusquedaProductos', {
                                    MostrarServicios : 'S',
                                    NivelPrecio: nivelPrecioSelec,
                                    Bodega: null
                                })
                                .then(resp => {                                  
                                    if (resp.data && resp.data.json.length > 0) this.productos = resp.data.json
                                })
            },            

            LimpiarOrden(){
                this.mostrarOrden = false
                this.tipoOrdenGrabada = null
                this.LimpiarPantalla()
                this.PorcentajeCoaseguro = 0
                this.MontoCopago = 0
                this.MCoaseguro = 0
                this.MCopago = 0
                this.Preparaciones = null
                this.SumaTotal = 0
                this.niveles.Nombre = null
                this.NivelSeleccionado = null
            },
            LimpiarPantalla(){
                this.info.serie = null
                this.info.numeroAdmision = null
                this.info.verificacionSeguro = null
                this.info.paciente = null
                this.info.habitacion = null
                this.info.nivelprecio = null  
                this.info.bodega = null
                this.info.tipoOrdenGrabada = null
                this.info.tipoOrden = null
                this.info.medico = null
                this.info.correlativoOrden = null
                this.info.nombreMedico = 'SIN REFERENCIA'
                this.info.observaciones = null
                this.info.fechaEstudio = new Date()
                this.cargos = []   
                this.productos = []                
                this.mostrarOrden = false
                
            },
            ConsultaProductos() {
            return {
                ActualizaTotal: (value) => {
                    this.ListaProductos = this.ListaProductos.map(function (productos) {

                        let ListaPrecios = JSON.parse(productos.ProductoPrecio)

                        return {
                            ...productos,
                            PrecioProducto: (ListaPrecios.findIndex(element => element.NivelPrecios === value.toString()) < 0) ? 0 : ListaPrecios.find((element) => element.NivelPrecios == value.toString()).Precio
                        };
                    })

                    this.ConsultaProductos().calculaTotal()
                },
                calculaTotal: () => {                    
                    this.MCopago = parseFloat(this.MontoCopago)

                    if (this.PorcentajeCoaseguro === '' || this.PorcentajeCoaseguro === null || this.PorcentajeCoaseguro === undefined) {
                        this.MCoaseguro = 0
                        this.PorcentajeCoaseguro = 0                 
                    }
                    this.MontoCoaseguro = (  this.totalCargos * parseFloat((this.PorcentajeCoaseguro / 100))).toFixed(2)
                    
                    this.cargos.forEach(item => {
                        item.CoaseguroS = parseFloat(this.PorcentajeCoaseguro).toFixed(2) * parseFloat(item.Precio).toFixed(2) * item.Cantidad / 100
                    });   

                    this.MCoaseguro = parseFloat(this.MontoCoaseguro)  
                    if (this.MontoCopago === '' || this.MontoCopago === null || this.MontoCopago === undefined) {
                        this.MCopago = 0
                        this.MontoCopago = 0 ;
                    }
                    this.SumaTotal = ((this.MCoaseguro === 0 && this.MCopago === 0)? 0 : parseFloat(this.MontoCoaseguro)) + parseFloat(this.MontoCopago)
                }
            }
            },  
            validarMaxMin(tr) {
            if (!tr) return;

            if (tr.Cantidad < 1 || isNaN(tr.Cantidad)) {
                tr.Cantidad = 1; 
                this.$vs.notify({
                color: 'warning',
                title: 'Cantidad inválida',
                text: 'La cantidad mínima es 1.'
                });
            }
            },
            Interfaz() {
                return {
                    agregaProducto: (producto) => {
                        let Cantidad = 1
                            this.cargos.unshift({
                                ...producto,
                                Cantidad: Cantidad,
                                Nivel: this.NivelSeleccionado + ' ' + this.NombreNivel,
                                CoaseguroS: Cantidad * producto.Precio * this.PorcentajeCoaseguro / 100
                            })

                            this.TempProductoSelec = [];
                        if (producto.Preparacion && producto.Preparacion.trim() !== '') {  
                            this.fillPreparation(producto.Nombre, producto.Preparacion)
                        }
                        this.ConsultaProductos().calculaTotal()
                    }
                }
            },
        },
    }

</script>
    
    <style scoped>
    .popNuevo .vs-popup {
        width: 990px !important;
    }
    .content-wrapper {
        overflow-y: auto; /* Permitir desplazamiento vertical si el contenido es grande */
        width: 100%;
        height: 100%;
    }
    .cantidad button {
        height: 25px;
        width: 30px;
        border: 1px solid #ccc;
        border-radius: 5px;
    
    }
    
    .cantidad div {
        display: inline-block;
        height: 25px;
        width: 30px;
        text-align: center;
    }

    .quitar_espacio {
        padding: 0;
        margin: 0;        
    }

    .header_valores {
    grid-area: header_valores;
    display: flex;
    }

    </style>    