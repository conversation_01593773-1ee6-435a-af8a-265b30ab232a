<template>
    <vx-card type = "2" title="Recalcular Cargos Admisión" class="justify-center ">    
        <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
            <form ref="formAnulacion" method="post" @submit.prevent="handleSubmit(recalcular_admision())">
                <div class="flex flex-wrap">
                    <div class="md:w-full lg:w-4/12 xl:w-2/12 m-2">
                        <label>Tipo Admisión:</label> 
                        &nbsp;&nbsp;
                        <label style="color:black">{{info.tipoAdmisionNombre}}</label>                                 
                    </div>
                    <div v-if="!info.tipoAdmision || info.tipoAdmision != 'S'" class="w-full md:w-full lg:w-7/12 xl:w-9/12"></div>
    
                    <div v-if="info.tipoAdmision && info.tipoAdmision == 'S'" class="w-full md:w-full lg:w-6/12 xl:w-4/12 m-2 lg:pr-6">
                        <label>Seguro:</label> 
                        &nbsp;&nbsp;
                        <label style="color:black">{{info.poliza}}</label>      
                    </div>
                    <div v-if="info.tipoAdmision && info.tipoAdmision == 'S'" class="w-full md:w-full lg:w-1/12 xl:w-5/12"></div>
    
                    <div class="w-full md:w-full lg:w-1/12 xl:w-1/12 m-1">
                        <ValidationProvider rules="required" v-slot="{ errors }">
                            <vs-input label="Serie" v-model="info.serie" 
                                        class="w-full" 
                                        v-on:keyup="info.serie = info.serie?.toUpperCase() || ''" 
                                        :disabled = "info.desactivarCamposAdmision"
                                        :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" /> 
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-full lg:w-2/12 xl:w-2/12 m-1">
                        <ValidationProvider rules="required" v-slot="{ errors }">
                            <SM-Buscar class="w-full" 
                                       label="Admisión" v-model="info.Admision" api="app/v1_JefeCaja/consulta_admision_recalculo" 
                                       :api_campos="['Serie','Codigo','Nombres','Apellidos']" 
                                       :api_titulos="['Serie#','Admision#','Nombres','Apellidos']" 
                                       :api_filtro="{'Opcion':'CONSULTA','SubOpcion':'ADMISION','activo':0, 'Serie':info.serie}" 
                                       api_campo_respuesta="Codigo" :api_preload="false" :disabled_texto="info.desactivarCamposAdmision" :mostrar_busqueda="true" 
                                       :callback_buscar="cargaAdmision" :callback_cancelar="quitarAdmision"                                                                                                          
                                       :danger="errors.length > 0"
                                       :danger-text="(errors.length > 0) ? errors[0] : null" />                                
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-full lg:w-7/12 xl:w-3/12 m-1">
                        <ValidationProvider rules="required" v-slot="{ errors }">
                            <vs-input label="Nombre del paciente:"  
                                      class="w-full" 
                                      :value="info.paciente" disabled 
                                      :danger="errors.length > 0"
                                      :danger-text="(errors.length > 0) ? errors[0] : null" />                                
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-full lg:w-1/12 xl:w-5/12"></div>
    
                    <vs-divider position="left">Recalcular</vs-divider>
                    <vx-input-group class="w-full md:w-full lg:w-7/12 xl:w-5/12 m-2">
                        <ul class="centerx">
                            <li v-for="(item,index) in TiposRecalculos" :key="index">
                                <vs-radio v-model="info.TipoRecalculo" :vs-value="item.Id" :disabled="!item.Activa" @change="cambioCheckbox($event)">{{item.Nombre}}</vs-radio>
                            </li>
                        </ul>
                    </vx-input-group>
                    <div class="w-full md:w-full lg:w-4/12 xl:w-5/12"></div>
    
                    <vs-divider v-if="permisos.recalculoCopago" position="left">Recalcular Con Copago</vs-divider>
    
                    <vx-input-group v-if="permisos.recalculoCopago" class="w-full md:w-full lg:w-7/12 xl:w-5/12 m-2">
                        <ul class="centerx">
                            <li v-for="(item,index) in TiposRecalculosCopago" :key="index">
                                <vs-radio v-model="info.TipoRecalculo" :vs-value="item.Id" :disabled="!item.Activa" @change="cambioCheckbox($event)">{{item.Nombre}}</vs-radio>
                            </li>
                        </ul>
                    </vx-input-group>
                    <div v-if="permisos.recalculoCopago" class="w-full md:w-full lg:w-4/12 xl:w-5/12"></div>
                    <vs-divider></vs-divider>
    
                    <div v-if="habitilarBusquedaPoliza" class="w-full md:w-full lg:w-4/12 xl:w-2/12 m-2">
                            <SM-Buscar label="Poliza" v-model="info.codigoSeguroRecalculo" api="app/v1_JefeCaja/consulta_lista_seguros" :api_campos="['Codigo','Poliza','Aseguradora']" :api_titulos="['Codigo', 'Poliza','Aseguradora']" :api_filtro="{'Opcion':'CONSULTA','SubOpcion':'SEGUROS'}"  api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Codigo" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="true" :callback_buscar="cargarSeguroRecalculo" />                             
                    </div>
                    <div v-if="habitilarBusquedaPoliza" class="w-full md:w-full lg:w-6/12 xl:w-4/12 m-2 lg:pr-6">
                        <ValidationProvider rules="required" v-slot="{ errors }">
                            <vs-input label="Descripción:" class="w-full" :value="info.polizaRecalculo" disabled :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />                            
                        </ValidationProvider>
                    </div>
                    <div v-if="habitilarBusquedaPoliza" class="w-full md:w-full lg:w-1/12 xl:w-5/12"></div>
    
                    <div v-if="permisos.cambioPoliza && info.solicitarIdAfiliado == 'S'" class="w-full md:w-full lg:w-10/12 xl:w-6/12">                
                        <PolizaSaludSiempre   @poliza-seleccionada="obtenerPolizaSeleccionada"
                                            :IdPaciente="parseInt(info.idPaciente)"
                                            :SubOpcion="'CONTRATO'">
                        </PolizaSaludSiempre>
                    </div>
                    <div v-if="permisos.cambioPoliza && info.solicitarIdAfiliado == 'S'" class="w-full md:w-full lg:w-1/12 xl:w-5/12"></div>
    
                    <div class="w-full md:w-full lg:w-10/12 xl:w-6/12 m-2 mt-0">
                        <ValidationProvider rules="min:8|max:40|required" v-slot="{ errors }">
                            <vs-input size="large" label="*Razon" class="w-full"  v-model="info.razon" 
                                      :danger="errors.length > 0"
                                      :danger-text="(errors.length > 0) ? errors[0] : null"/>
                        </ValidationProvider>
                    </div>                  
                    <div class="w-full m-2">
                        <vs-button color="success" class="w-full md:w-full lg:w-4/12 xl:w-1/12 m-2"
                            :disabled="info.desactivarBotonRecalculo"
                            @click="handleSubmit(recalcular_admision(invalid))">
                            Recalcular Admsión
                        </vs-button>
    
                        <vs-button class="w-full Emd:w-full lg:w-4/12 xl:w-1/12 m-2" color="warning" @click="confirmar_limpia_campos()">
                            Limpiar Campos
                        </vs-button>
                    </div>
                </div>
            </form>
        </ValidationObserver>        
    </vx-card>
    </template>
    
    <script>
    
    import PolizaSaludSiempre from "/src/components/sermesa/modules/admisiones/ConsultaSaludSiempre.vue"
    
    export default {
        components: {
            PolizaSaludSiempre
        },
        data(){
            return {
                info:{
                    nivelprecio: null,
                    serie: null,
                    Admision: null,
                    admisionConSerie: null,
                    paciente: null,
                    tipoAdmision: null,
                    codigoSeguro: null,
                    tipoAdmisionNombre: null,
                    poliza: null,
                    razon: null,
                    idPaciente: 0,
                    TipoRecalculo: '',
                    codigoSeguroRecalculo: null,
                    codigoAseguradoraRecalculo: null,
                    polizaRecalculo: null,
                    codigoAfiliado:null,
                    estadoContratoAfiliado:null,
                    solicitarCambioPoliza: '',
                    solicitarIdAfiliado: '',
                    desactivarBotonRecalculo: true,
                    desactivarCamposAdmision: false
                },
                permisos: {
                    cambioPoliza: false,
                    recalculoCopago: false
                },            
                TiposRecalculos: [{
                        Id: 'P',
                        Nombre: 'Privada a Seguro',
                        Activa: false
                    },
                    {
                        Id: 'S',
                        Nombre: 'Seguro a Privada',
                        Activa: false
                    },
                    {
                        Id: 'O',
                        Nombre: 'Seguro a Otro Seguro',
                        Activa: false
                    },
                    {
                        Id: 'A',
                        Nombre: 'Paquete A Seguro',
                        Activa: false
                    }
                ],
                TiposRecalculosCopago: [{
                    Id: 'OC',
                    Nombre: 'Seguro a Otro Seguro',
                    Activa: false
                }]
            };
        },computed:{
            habitilarBusquedaPoliza(){
                return this.info.TipoRecalculo == 'P' || this.info.TipoRecalculo == 'O' || this.info.TipoRecalculo == 'A' || this.info.TipoRecalculo == 'OC' 
            },
            habilitarBusquedaClientePoliza(){
                return false
            }
        },methods:{
            cambioCheckbox(event){
                if (event.target.value == 'S'){
                    this.limpiarCamposSeguro()
                }
    
            },
            limpiarCamposSeguro(){
                this.info.codigoSeguroRecalculo = null
                this.info.codigoAseguradoraRecalculo = null
                this.info.polizaRecalculo = null
                this.info.solicitarCambioPoliza = ''
                this.info.solicitarIdAfiliado = '' 
            },
            cargaAdmision(datos) {
                if(datos.NivelPrecios == null || datos.NivelPrecios == 0){
                    this.$vs.notify({
                        position: 'top-center',
                        color: '#B71C1C',
                        title: 'Alerta',
                        text: 'Debe configurar el nivel de precios para la admision seleccionada',                        
                    })
                    return
                }
                this.info.nivelprecio = datos.NivelPrecios
                this.info.paciente = datos.Paciente
                this.info.serie = datos.Serie
                this.info.Admision = datos.Codigo
                this.info.tipoAdmision = datos.TipoDescuento
                this.info.codigoSeguro = datos.Seguro
                this.info.admisionConSerie = datos.Admision
                this.info.idPaciente = datos.IdPaciente
                this.info.desactivarBotonRecalculo = false
                this.info.desactivarCamposAdmision = true 
                if(this.info.tipoAdmision == 'S'){
                    this.info.tipoAdmisionNombre = 'Seguro'
                    if(this.info.codigoSeguro && this.info.codigoSeguro.trim().length > 0)
                        this.cargarSeguroAdmision()             
                    this.TiposRecalculos[0].Activa = false           
                    this.TiposRecalculos[1].Activa = true           
                    this.TiposRecalculos[2].Activa = true
                    this.TiposRecalculos[3].Activa = false           
                    this.TiposRecalculosCopago[0].Activa = true
                }else if(this.info.tipoAdmision == 'N' || this.info.tipoAdmision == 'A'){
                    this.info.tipoAdmisionNombre = 'Privada'
                    this.TiposRecalculos[0].Activa = true
                    this.TiposRecalculos[1].Activa = false    
                    this.TiposRecalculos[2].Activa = false    
                    this.TiposRecalculos[3].Activa = false    
                    this.TiposRecalculosCopago[0].Activa = false    
                }else if(this.info.tipoAdmision == 'Q'){
                    this.info.tipoAdmisionNombre = 'Paquete'    
                    this.TiposRecalculos[0].Activa = false          
                    this.TiposRecalculos[1].Activa = false 
                    this.TiposRecalculos[2].Activa = false                        
                    this.TiposRecalculos[3].Activa = true
                    this.TiposRecalculosCopago[0].Activa = false    
                }else{
                    this.info.desactivarBotonRecalculo = true
                }            
            },
            cargarSeguroRecalculo(datos){
                this.info.codigoSeguroRecalculo = datos.Codigo
                this.info.codigoAseguradoraRecalculo = datos.codigoAseguradora
                this.info.polizaRecalculo = datos.Poliza
                this.info.solicitarCambioPoliza = datos.PermisoCambioPoliza    
                this.info.solicitarIdAfiliado = datos.SolicitarIdAfiliado 
                this.info.codigoAfiliado = null
                this.info.estadoContratoAfiliado = null
                if(this.info.solicitarCambioPoliza == 'S'){
                    if(!this.permisos.cambioPoliza){
                        this.$vs.dialog({
                                type: 'confirm',
                                color: 'danger',
                                title: 'Alerta',
                                acceptText: 'Cerrar',
                                text: `No cuenta con permisos para cambiar la admsión a este tipo de seguro`,
                                accept: () => {
                                    this.info.codigoSeguroRecalculo = null
                                    this.info.codigoAseguradoraRecalculo = null
                                    this.info.polizaRecalculo = null
                                    this.info.solicitarCambioPoliza = ''
                                    this.info.solicitarIdAfiliado = ''                            
                                },
                                cancel: () => {
                                    this.info.codigoSeguroRecalculo = null
                                    this.info.polizaRecalculo = null
                                    this.info.solicitarCambioPoliza = ''
                                    this.info.solicitarIdAfiliado = ''                             
                                }
                            })
                    }
                }
            },
            obtenerPolizaSeleccionada(poliza) {
                
                this.info.codigoAfiliado = poliza.CodigoAfiliado
                this.info.estadoContratoAfiliado = poliza.Estado
            },
            cargarSeguroAdmision() {
                this.axios.post(                    
                '/app/v1_JefeCaja/obtener_seguro', {
                    'Opcion': 'Consulta',
                    'SubOpcion': 'Seguro',
                    'Codigo':this.info.codigoSeguro
                }).then((resp) =>{
                    
                    if(resp.data.json[0].Poliza && resp.data.json[0].Poliza!='')
                        this.info.poliza = this.info.codigoSeguro +' '+resp.data.json[0].Poliza
                    else
                        this.info.poliza = this.info.codigoSeguro
                })
            },     
            quitarAdmision(){
                this.limpiar_campos()
            },
            recalcular_admision(invalid){
                if(invalid){
                    this.$vs.notify({
                                        time: 4000,
                                        color: '#B71C1C',
                                        title: 'Alerta',
                                        text: 'Llenar los campos obligatorios.',
                                        position: 'top-center'
                                    })
                    return
                }
                if(!this.info.TipoRecalculo || this.info.TipoRecalculo == ''){
                    this.$vs.notify({
                                        time: 4000,
                                        color: '#B71C1C',
                                        title: 'Alerta',
                                        text: 'Seleccionar el tipo de recalculo a realizar.',
                                        position: 'top-center'
                                    })
                    return
                }
                
                if(this.info.solicitarIdAfiliado == 'S' && !this.info.codigoAfiliado){
                    this.$vs.notify({
                                        time: 4000,
                                        color: '#B71C1C',
                                        title: 'Alerta',
                                        text: 'Seleccionar el codigo de afiliado correspondiente a la poliza.',
                                        position: 'top-center'
                                    })
                    return
                }
                
                if(this.info.solicitarIdAfiliado == 'S' && this.info.estadoContratoAfiliado != 'A'){
                    this.$vs.notify({
                                        time: 4000,
                                        color: '#B71C1C',
                                        title: 'Alerta',
                                        text: 'El contrato seleccionado no esta activo.',
                                        position: 'top-center'
                                    })
                    return
                }
    
                this.axios.post('/app/v1_JefeCaja/recalculo_admision',{
                                                Opcion: 'INSERTAR',
                                                SubOpcion: 'RECALCULO',                                            
                                                Serie: this.info.serie,
                                                CodigoAdmision: this.info.Admision,
                                                TipoRecalculo: this.info.TipoRecalculo,
                                                CodigoSeguroRecalculo: this.info.codigoSeguroRecalculo,
                                                AseguradoraRecalculo: this.info.codigoAseguradoraRecalculo,
                                                Razon: this.info.razon,
                                                CodigoAfiliado: this.info.codigoAfiliado
                                            })
                                  .then((resp) => {
                                                        let error 
                                                        let mensajeError
                                                        
                                                        if(resp.data.json && resp.data.json.length != 0){
                                                            error = resp.data.json[0].tipo_error
                                                            mensajeError = resp.data.json[0].descripcion
                                                        }else{
                                                            error = resp.data.tipo_error
                                                            mensajeError = resp.data.descripcion
                                                        }
    
                                                        if(error && error != 0){
                                                            this.$vs.notify({
                                                                time: 6000,
                                                                color: '#B71C1C',
                                                                title: 'Alerta',
                                                                text: mensajeError,
                                                                position: 'top-center'
                                                            })  
                                                        }
                                                        else{                               
                                                            this.limpiar_campos()      
                                                            this.$vs.notify({   
                                                                time: 5000,                                         
                                                                title:'Exito',
                                                                text: resp.data.json[0].descripcion,
                                                                color:'success',
                                                                position: 'top-center'
                                                            })
                                                        }
                                                    }
                                        )
    
            },
            limpiar_campos(){            
                this.info.nivelprecio=null
                this.info.serie=null
                this.info.Admision=null
                this.info.admisionConSerie=null
                this.info.paciente=null
                this.info.tipoAdmision=null
                this.info.codigoSeguro=null
                this.info.tipoAdmisionNombre=null
                this.info.poliza=null
                this.info.razon=null
                this.info.idPaciente=0
                this.info.TipoRecalculo=''
                this.info.codigoSeguroRecalculo=null
                this.info.codigoAseguradoraRecalculo=null
                
                this.info.polizaRecalculo=null
                this.info.codigoAfiliado = null
                this.info.estadoContratoAfiliado = null
                this.info.solicitarCambioPoliza=''
                this.info.solicitarIdAfiliado=''
                this.info.desactivarBotonRecalculo=true    
                this.info.desactivarCamposAdmision=false
    
                this.TiposRecalculos[0].Activa = false           
                this.TiposRecalculos[1].Activa = false           
                this.TiposRecalculos[2].Activa = false
                this.TiposRecalculos[3].Activa = false           
                this.TiposRecalculosCopago[0].Activa = false
            },
            limpiar_campos_exito(){            
                this.info.codigoOrden = null
                this.info.admisionConSerie = null
                this.info.paciente = null
                this.info.razon = null            
            },
            confirmar_limpia_campos(){
                this.$vs.dialog({
                                type: 'confirm',
                                color: 'danger',
                                title: 'Confirmación',
                                acceptText: 'Limpiar',
                                cancelText: 'Cancelar',
                                text: `¿Desea limpiar los campos?`,
                                accept: () => {
                                    this.limpiar_campos()
                                }
                            })
            }
        },mounted(){        
            this.permisos.cambioPoliza = this.$validar_privilegio('CAMBIO_POLIZA').status
            this.permisos.recalculoCopago = this.$validar_privilegio('RECALCULO_COPAGO').status
        }
    }
    
    </script>
    <style>
    .centerx {
        display: flex;
    }
    
    .centerx li {
        margin-right: 10px
    }
    
    .tabla-polizas .contenedor-tabla {
        min-height: auto;
    }
    </style>