<template>
    <vx-card type = "2" title="Modificación de Recibos" class="justify-center">
        <vs-popup id="datosRecibo" :title = this.tituloModalNewEdit  :active.sync="dialogNewEdit" :button-close-hidden = "true">
            <ValidationObserver ref="formValidate" v-slot="{ invalid,handleSubmit }" mode="lazy">
                <form method="post" @submit.prevent="handleSubmit(ValidarForma())">
                    <div class="flex flex-wrap">
                        
                        <div class="w-full xl:w-1/3 lg:w-1/2 md:w-full sm:w-full xs:w-full">
                            <ValidationProvider rules="required" v-slot="{ errors }">
                                <vs-select class="w-full" filter  label = "Estado del Pago" placeholder="Seleccione un estado para el pago" 
                                v-model="newEditPago.Status" :disabled="editMode"
                                :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" required>
                                    <vs-select-item :key="index" :value="item.codigo" :text="$find_catalogo(item.codigo, catalogoEstadoPago, true)" v-for="(item,index) in estadosPagoHabilitados" />
                                </vs-select>
                            </ValidationProvider>
                        </div>
                        <div class="w-full xl:w-1/3 lg:w-1/2 md:w-full sm:w-full xs:w-full">
                            <ValidationProvider rules="required" v-slot="{ errors }">
                                <vs-select class="w-full" filter  label = "Forma de Pago" placeholder="Seleccione forma del pago" v-model="newEditPago.Forma" 
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" required>
                                    <vs-select-item :key="index" :value="item.codigo" :text="$find_catalogo(item.codigo, catalogoFormaPago, true)" v-for="(item,index) in catalogoFormaPago" />
                                </vs-select>
                            </ValidationProvider>
                        </div>
                        <div class="w-full xl:w-1/3 lg:w-1/2 md:w-full sm:w-full xs:w-full">
                            <SM-Buscar v-model.trim="newEditPago.BancoTarjeta"
                                    label="Banco / Tarjeta"
                                    api="app/v1_JefeCaja/BusquedaBancoTarjeta"
                                    :api_campos="['Codigo', 'Nombre']"
                                    :api_titulos="['Código', 'Nombre']"
                                    :api_filtro="{Tipo: newEditPago.Forma}"
                                    api_campo_respuesta="Codigo"
                                    api_campo_respuesta_mostrar="Nombre"
                                    :api_preload="true" 
                                    :disabled_texto="true"
                                />
                        </div>
                    
                        <div class="w-full xl:w-1/3 lg:w-1/2 md:w-full sm:w-full xs:w-full">
                            <ValidationProvider rules="between:0,**********|required" v-slot="{ errors }" class="required">
                                <vs-input class="w-full" label="Monto del pago"  type="number" v-model="newEditPago.Monto" 
                                :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" required/>
                            </ValidationProvider>
                        </div>
                        <div class="w-full xl:w-1/3 lg:w-1/2 md:w-full sm:w-full xs:w-full">
                            <ValidationProvider rules="numero_entero|between:0,**********" v-slot="{ errors }">
                                <vs-input label="Documento" class="w-full" type="number" v-model="newEditPago.Documento"
                                :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"/>
                            </ValidationProvider>
                        </div>
                        <div class="w-full xl:w-1/3 lg:w-1/2 md:w-full sm:w-full xs:w-full">
                            <ValidationProvider rules="max:50" v-slot="{ errors }">
                                <vs-input label="Cuenta / Número Tarjeta" class="w-full" type="text" v-model="newEditPago.Cuenta"
                                :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full xl:w-1/3 lg:w-1/2 md:w-full sm:w-full xs:w-full">
                            <ValidationProvider rules="max:10" v-slot="{ errors }">
                                <vs-input label="Autorización" class="w-full" type="text" v-model="newEditPago.Autorizacion"
                                :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full xl:w-1/3 lg:w-1/2 md:w-full sm:w-full xs:w-full">
                            <ValidationProvider rules="between:0,300" v-slot="{ errors }">
                                <vs-input class="w-full" label="Tasa"  type="number" v-model="newEditPago.Tasa" disabled 
                                :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full xl:w-1/3 lg:w-1/2 md:w-full sm:w-full xs:w-full">
                            <vs-input class="w-full" label="Fecha Efectividad" type="date" v-model="newEditPago.FechaEfectividad"/>
                        </div>
                        <div class="w-full xl:w-1/3 lg:w-1/2 md:w-full sm:w-full xs:w-full">
                            <label>Previsado</label>
                            <vs-switch color="success" v-model="newEditPago.Previsado"> 
                                <span slot="on">SI</span>
                                <span slot="off">NO</span>
                            </vs-switch>
                        </div>
                    </div>
                    <vs-divider></vs-divider>
                    <ValidationProvider rules="min:10|max:40|required" v-slot="{ errors }" class="required">
                        <vs-input class="w-full" label="Razón"  type="text" v-model="razon" placeholder="Ingrese una razón para la modificación"
                            :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" required />
                    </ValidationProvider>
                    <div style="text-align:right;margin:5px 0;padding:5px ">
                        <vs-button @click="ValidarForma()" color="success" icon-pack="fa" icon="fa-save" style="float:right" :disabled="invalid">Guardar</vs-button>
                        <vs-button @click="dialogNewEdit = false" color="danger" icon-pack="fa" icon="fa-ban" style="float:right">Cancelar</vs-button>
                    </div>
                </form>
            </ValidationObserver>
        </vs-popup>
<!-- ---------------------------------------------------------------------------------------------------------------- -->

        <div ref="infoAdmision" style="padding:10px;border-radius:5px; border:5px solid #ccc;">
            <vs-row>
                <vs-input class="w-full md:w-32 sm:w-32 xs:w-32" label = "Serie" v-model="serieRecibo" maxlength="3" v-on:keyup = "serieRecibo = serieRecibo.toUpperCase()"/>
                <vs-input class="w-full md:w-32 sm:w-32 xs:w-32" label = "Número"  type="number" min = "0" step="0.01" v-model = "numeroRecibo" v-on:keyup.enter = "CargarRecibo()" v-on:blur = "CargarRecibo()"/>
            </vs-row>
            <div v-if = "objrecibo.Codigo" style="top:-8px;left:30px" class="vs-con-loading__container">
                <div class="vx-card__body">
                    <div class="vx-row">
                        <div id="account-info-col-1" class="vx-col flex-1">
                            <table>
                                <tr>
                                    <td class="font-semibold">Total del recibo:</td>
                                    <td>{{this.$formato_moneda(objrecibo.Total)}}</td>
                                </tr>
                                <tr>
                                    <td class="font-semibold">Recibo a nombre de:</td>
                                    <td>{{objrecibo.Nombre}}</td>
                                </tr>
                                <tr>
                                    <td class="font-semibold">Paciente:</td>
                                    <td>{{objrecibo.NombrePaciente}}</td>
                                </tr>
                                <tr>
                                    <td class="font-semibold">Admisión:</td>
                                    <td>{{objrecibo.SerieAdmision + ' - ' + objrecibo.Admision}}</td>
                                </tr>
                                <tr>
                                    <td class="font-semibold">Fecha:</td>
                                    <td>{{$formato_fecha(objrecibo.Fecha, 'dMMy')}}</td>
                                </tr>
                                <tr>
                                    <td class="font-semibold">Caja:</td>
                                    <td>{{objrecibo.Caja}}</td>
                                </tr>
                                <tr>
                                    <td class="font-semibold">Corte:</td>
                                    <td>{{objrecibo.Corte}}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
            
        <vs-divider></vs-divider>
        
        <vs-button color="success" icon-pack="feather" @click="AgregarPago()" icon="icon-plus-circle" v-if="objrecibo.Codigo" style="float:inline-block">Agregar Pago</vs-button>
        
        <vs-table v-model="selected" v-if = "objrecibo.Codigo" :data="objrecibo.pagos" noDataText="Recibo no tiene pagos registrados">
            <template slot="thead">
                    <vs-th>Estado</vs-th>
                    <vs-th>Forma de Pago</vs-th>
                    <vs-th>Monto</vs-th>
                    <vs-th>Banco / Tarjeta</vs-th>
                    
                    <vs-th>Previsado</vs-th>
                    <vs-th>Documento</vs-th>
                    <vs-th>Cuenta / Numero Tarjeta</vs-th>
                    <vs-th>Autorización</vs-th>
                    <!-- <vs-th>Tasa</vs-th> -->
                    <!-- <vs-th>Valor en Dólares</vs-th> -->
                    <vs-th>Fecha Efectivo</vs-th>
                    <vs-th>Opciones</vs-th>
            </template>
            <template slot-scope="{data}">
                <vs-tr :key="indextr" v-for="(tr, indextr) in data " :data="tr">
                    <vs-td :data="tr.Status" width="75px">{{$find_catalogo(tr.Status, catalogoEstadoPago)}}</vs-td>
                    <vs-td :data="tr.Forma" width="75px">{{$find_catalogo(tr.Forma, catalogoFormaPago)}}</vs-td>
                    <vs-td :data="tr.Monto" width="75px">{{$formato_moneda(tr.Monto)}}</vs-td>
                    <vs-td :data="tr.BancoTarjeta" width="175px">{{tr.NombreBancoTarjeta}}</vs-td>
                    
                    <vs-td :data="tr.Previsado" width="75px">{{$to_SINO(tr.Previsado)}}</vs-td>
                    <vs-td :data="tr.Documento" width="75px">{{tr.Documento}}</vs-td>
                    <vs-td :data="tr.Cuenta" width="75px">{{tr.Cuenta}}</vs-td>
                    <vs-td :data="tr.Autorizacion" width="75px">{{tr.Autorizacion}}</vs-td>
                    <!-- <vs-td :data="null" width="250px">{{tr.Tasa}}</vs-td> -->
                    <!-- <vs-td :data="null" width="250px">{{null}}</vs-td> -->
                    <vs-td :data="tr.FechaEfectividad" width="75px">{{$formato_fecha(tr.FechaEfectividad)}}</vs-td>

                    <vs-td width="50px">
                        <vs-row>
                            <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" @click="EditarPago(tr)" ></vs-button>
                        </vs-row>
                    </vs-td>

                </vs-tr>
            </template>
        </vs-table>
    </vx-card>
</template>
<script>
    export default {
        data() {
            return {
               serieRecibo:'',
               numeroRecibo: '',
               objrecibo: {},
               NombreTarjeta:'',
               newEditPago: {
                    Linea: 0,
                    Monto: null,
                    Forma: '',
                    BancoTarjeta: '',
                    Cuenta: null,
                    Documento: null,
                    Autorizacion: null,
                    Tasa: null,
                    Vuelto: null,
                    Remision: null,
                    FechaEfectividad: null,
                    Tipo: null,
                    Status: null,
                    Previsado: null,
                    PagoDirecto: null,
                    FechaPago: null,
               },
               razon: '',
               editMode:false,

               tituloModalNewEdit : '',
               dialogNewEdit : null,
               catalogoEstadoPago: [],
               catalogoFormaPago: [],
               estadosPagoHabilitados: [],
               selected: [],
               
               BancoTarjeta:''
            }
        },
        methods: {
            
            LimpiarDatos(){
                this.LimpiarRecibo()
                this.LimpiarPago()
                this.numeroRecibo = ''
                this.razon = ''
            },
            LimpiarRecibo(){
                this.objrecibo = {
                    "Empresa": null,
                    "Serie": null,
                    "Codigo": null,
                    "Fecha": null,
                    "Caja": null,
                    "Usuario": null,
                    "SerieAdmision": null,
                    "Admision": null,
                    "Paciente": null,
                    "Nombre": null,
                    "Tipo": null,
                    "Status": null,
                    "Corte": null,
                    "Total": null,
                    "Registro": null,
                    "Periodo": null,
                    "Aplicado": null,
                    "Observaciones": null,
                    "AnticipoAplienMes": null,
                    "Sucursal": null,
                    "Contrato": null,
                    "Afiliado": null,
                    "IdPQTPaciente": null,
                    "Sobregiro": null,
                    "NombrePaciente":null,
                    "Pagos": [],
                }
            },
            LimpiarPago(){
                this.newEditPago = {
                    "Linea": 0,
                    "Monto": null,
                    "Forma": null,
                    "BancoTarjeta": null,
                    "Cuenta": null,
                    "Documento": null,
                    "Autorizacion": null,
                    "Tasa": null,
                    "Vuelto": null,
                    "Remision": null,
                    "FechaEfectividad": null,
                    "Tipo": null,
                    "Status": null,
                    "Previsado": null,
                    "PagoDirecto": null,
                    "FechaPago": null,
                }
            },
            CargarRecibo(){
                if(this.serieRecibo && this.numeroRecibo )
                {
                    if ( this.objrecibo.Serie != this.serieRecibo && this.objrecibo.Codigo != this.numeroRecibo)
                    this.axios.post('/app/v1_JefeCaja/ObtenerReciboMod', {
                            serie: this.serieRecibo,
                            codigo: this.numeroRecibo
                        })
                        .then(resp => {
                            if(resp.data.codigo === 0 && resp.data.json){
                                if(resp.data.json.Status == 'A'){
                                    this.$vs.notify({
                                    time: 8000,
                                    title: 'Modificar recibos dice . . .',
                                    text: 'El recibo ya fue anulado',
                                    iconPack: 'feather',
                                    icon: 'icon-alert-circle',
                                    color: 'danger'})
                                    this.LimpiarDatos()
                                }
                                else{
                                    this.razon = ''
                                    this.objrecibo = resp.data.json
                                    this.objrecibo.pagos = this.objrecibo.pagos.map(data => {
                                        data.Previsado = data.Previsado == "S" ? true:false
                                        return data    
                                    })
                                }
                            }
                            else {
                                this.$vs.notify({
                                    time: 8000,
                                    title: 'Modificar recibos dice . . .',
                                    text: 'No se encontraron recibos con los datos proporcionados',
                                    iconPack: 'feather',
                                    icon: 'icon-alert-circle',
                                    color: 'warning'
                                })
                                this.LimpiarDatos()
                            }
                        })
                        .catch( () => {
                            this.LimpiarDatos()
                        })
                }
                else
                    this.LimpiarDatos()
            },
            EditarPago(trprueba){

                this.newEditPago.Tipo = trprueba.Tipo
                this.newEditPago.Linea = trprueba.Linea
                this.newEditPago.Monto = trprueba.Monto
                this.newEditPago.Forma = trprueba.Forma.trim().toUpperCase()
                // this.newEditPago.BancoTarjeta = trprueba.BancoTarjeta // se asigna hasta que terminde de pintarse la pantalla ver updated()
                ///para almacenar el valor y asignarlo luego que se pinte la pantalla, asi sm-buscar ya reconoce el api filtro y pinta el valor
                this.BancoTarjeta = trprueba.BancoTarjeta
                
                this.newEditPago.Cuenta = trprueba.Cuenta
                this.newEditPago.Documento = trprueba.Documento
                this.newEditPago.Autorizacion = trprueba.Autorizacion ? trprueba.Autorizacion.trim() : trprueba.Autorizacion
                this.newEditPago.Tasa = trprueba.Tasa
                this.newEditPago.Vuelto = trprueba.Vuelto
                this.newEditPago.Remision = trprueba.Remision
                this.newEditPago.FechaEfectividad = trprueba.FechaEfectividad ? trprueba.FechaEfectividad.slice(0,10) : trprueba.FechaEfectividad
                this.newEditPago.Status = trprueba.Status
                this.newEditPago.Previsado = trprueba.Previsado
                this.newEditPago.PagoDirecto = trprueba.PagoDirecto
                this.newEditPago.FechaPago = trprueba.FechaPago

                this.tituloModalNewEdit = "Editar Pago del recibo"
                this.editMode = true
                
               

                this.dialogNewEdit = true
            },
            AgregarPago() {
                
                this.tituloModalNewEdit = "Agregar Pago a Recibo"  
                this.editMode = false
                this.dialogNewEdit = true
            },
            async ObtenerCatalogos() {
                this.axios.post('/app/v1_JefeCaja/CatalogoEstadoPagoRecibo', {})
                    .then(resp => {
                        if(resp.data.codigo === 0 && resp.data.json){
                            this.catalogoEstadoPago = resp.data.json
                            this.estadosPagoHabilitados = this.catalogoEstadoPago.filter(data => {return data.codigo == 'H' || data.codigo == 'M' })
                        }
                    })
                this.axios.post('/app/v1_JefeCaja/CatalogoFormasPagoRecibo', {})
                .then(resp => {
                    if(resp.data.codigo === 0 && resp.data.json){
                        this.catalogoFormaPago = resp.data.json
                    }
                })
            },
            SavePago(){
                /// si es nuevo el pago la linea va con valor 0
                let postData = {...this.newEditPago}
                postData.Previsado = postData.Previsado? 'S':'N'
                postData.Serie = this.objrecibo.Serie
                postData.Codigo = this.objrecibo.Codigo
                postData.Razon = this.razon
                this.axios.post('/app/v1_JefeCaja/GrabarPago', postData)
                .then( () => {
                    this.dialogNewEdit = false
                    this.LimpiarRecibo()
                    this.CargarRecibo()
                        })
                
            },
            ValidarForma(){
                let total = 0
                //forma de pago con seguro no se suma
                total = this.objrecibo.pagos.reduce( (total, pago) => { 
                    return total + (pago.Forma != 'S' ?  pago.Monto : 0) 
                }, 0 )
                total = total - this.selected.Monto +  (this.newEditPago.Forma != 'S'? parseFloat(this.newEditPago.Monto): 0)

                if(total != this.objrecibo.Total)
                    this.$vs.dialog({
                        type: 'confirm',
                        color: 'danger',
                        title: 'Confirmación',
                        acceptText: 'SI',
                        cancelText: 'NO',
                        text: '¿El nuevo total es diferente al total del recibo, grabar así?',
                        accept: () => {this.SavePago()},
                        cancel: () => {}
                    })
                else
                    this.SavePago()  
            },
        },
        mounted() {
            this.ObtenerCatalogos()
        },
        watch: {
            'newEditPago.Forma'() {
                if(this.dialogNewEdit)
                    this.newEditPago.BancoTarjeta = null
            },
        },
        updated(){
            if(this.dialogNewEdit)
                this.newEditPago.BancoTarjeta = this.BancoTarjeta? this.BancoTarjeta.trim().toUpperCase() : null
        }
    }
</script>