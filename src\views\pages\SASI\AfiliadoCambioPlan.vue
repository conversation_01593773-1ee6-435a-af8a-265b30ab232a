<template>
<div class="afiliado-cambio-plan-container">
    
    <h5 class="mb-2">{{ formData.NombreContrato }}</h5>
    <form @submit="SubmitCambioPlan">
        <DxForm ref="fromCambioPlan" :form-data.sync="formData" label-location="left" label-mode="floating" validation-group="validacionCambioPlan" :col-count="2">
            <DxFormSimpleItem data-field="IdPlan" caption="Plan" :validation-rules="[{type: 'required', message: 'Seleccione Plan'}]" :col-span="2">
                <template #default>
                    <SeleccionPlan @value-change="onIdPlanChange" :IdPlan="formData.IdPlan" :editor-options="{label:'Nuevo Plan', labelMode:'floating', }" />
                </template>
            </DxFormSimpleItem>
            <DxFormSimpleItem data-field="Observacion" :label="{text:'Observación'}" :validation-rules="[{ type: 'required', message: 'Ingrese observación o motivo del cambio de estado' }, {type: 'stringLength', min:2, max: 255, message: 'Las observaciones debes tener entre 2 y 255 caracteres' }]" :col-span="2" />
            <DxFormButtonItem :visible="true" :button-options="{ text: 'Guardar',  type: 'success',  icon: 'save', useSubmitBehavior: true, }" horizontal-alignment="center" verical-alignment="buttom" :col-span="1" />
            <DxFormButtonItem :visible="true" :button-options="cancelOptions" horizontal-alignment="center" verical-alignment="buttom" :col-span="1" />
        </DxForm>
    </form>
   
</div>
</template>

<script>


export default {
    props: {
        
        IdPlan: {
            type: String,
            required: true
        },
        NumeroAdhesion: {
            type: String,
            required: true
        },
        
    },
    components: {
        SeleccionPlan: () => import('./SeleccionPlan.vue'),
    },
    data() {
        return {
            //los datos nuevos
            formData: {
                IdPlan: null,
                IdContrato: null,
                Observacion: null,
                NombreContrato: null,
            },
            cancelOptions: {
                text: 'Cancelar',
                type: 'danger',
                icon: 'save',
                useSubmitBehavior: false,
                onClick: () => {
                    this.$emit('cancel')
                }
            }
        }
    },
    methods: {

        onIdPlanChange(data) {
            this.formData.IdPlan = data?.IdPlan
            this.formData.IdContrato = data?.IdContrato
            this.formData.NombrePlan = data?.NombrePlan
            this.formData.NombreContrato = data?.NombreContrato
        },

        InitPlanChange() {
            this.formData.IdPlan = null
            this.formData.IdContrato = null
            this.formData.Observacion = null
            this.formData.NombreContrato = null
        },

        SubmitCambioPlan(e) {
            e.preventDefault()
            this.axios.post("/app/v1_afiliados/CambiarPlan", {
                    IdPlanAnterior: this.IdPlan,
                    NumeroAdhesion: this.NumeroAdhesion,
                    //lave
                    IdContrato: this.formData.IdContrato,
                    IdPlan: this.formData.IdPlan,
                    Observacion: this.formData.Observacion,
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {

                        this.$emit('plan-changed', {
                            IdPlan: this.formData.IdPlan,
                            IdContrato: this.formData.IdContrato,
                            NombrePlan: this.formData.NombrePlan,
                            NombreContrato: this.formData.NombreContrato,
                        })
                        this.InitPlanChange()
                    }
                })
        },
    }
}
</script>
