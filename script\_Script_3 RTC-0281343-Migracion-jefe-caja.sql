USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[SpModificaciones]    Script Date: 12/09/2023 22:01:15 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- =======================================================================================
--	CREADO POR: Esteban Palacios Kestler
--	FECHA CREACIÓN: 18/01/2023
--	PRODUCTO: RTC281343	- Jefe de <PERSON>aja
--  DESCRIPCION: Implementa operaciones C sobre la tabla[HOSPITAL][dbo].[Modificaciones]
-- =======================================================================================
CREATE PROCEDURE [dbo].[SpModificaciones](

	@IOpcion VARCHAR(3) = '', 
	-- C = Consulta de
	-- C1 Obtener todos los pagos de un recibo
	-- I = Ingreso 
	-- A = Actualizar 
	-- E = Eliminación 	
	@IEmpresa char(3), 
	@ITipo char(2), 
	@ISerie char(3), 
	@IDocumento int, 
	@IFecha smalldatetime = NULL , 
	@IUsuario char(5) = NULL , 
	@IObservaciones varchar(100) = NULL , 
	@IAutorizadoPor varchar(10) = NULL
)
AS
BEGIN
	DECLARE 
	@ErrRaise varchar (99)

	SET NOCOUNT ON
	
	IF @IOpcion = 'I'
	BEGIN
		INSERT INTO [dbo].[Modificaciones]
			(Empresa, Tipo, Serie, Documento, Fecha, Usuario, Observaciones, AutorizadoPor)
		VALUES
			(@IEmpresa, @ITipo, @ISerie, @IDocumento, @IFecha, @IUsuario, @IObservaciones, @IAutorizadoPor)
		SELECT 0 AS codigo, 'Datos registrados correctamente' AS descripcion, 0 AS tipo_error, @@rowcount as filas;	
	END
	ELSE
	BEGIN
		SET	@ErrRaise = CONCAT('No se ha encontrado la opción ', @IOpcion, ' ','[HOSPITAL].[dbo].[Modificaciones]')
		RAISERROR(@ErrRaise, 16, 1)
	END
END


-->>


USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[SpAuxiliarClientes]    Script Date: 12/09/2023 22:00:56 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- =======================================================================================
--	CREADO POR: Esteban Palacios Kestler
--	FECHA CREACIÓN: 18/01/2023
--	PRODUCTO: RTC281343	- Jefe de Caja
--  DESCRIPCION: Implementa operaciones CRUD sobre la tabla[CONTADB].[dbo].[SpAuxiliarClientes]
-- =======================================================================================
CREATE PROCEDURE [dbo].[SpAuxiliarClientes](

	@IOpcion VARCHAR(3) = '', 
	-- C = Consulta de
	-- I = Ingreso 
	-- A = Actualizar 
	-- E = Eliminación 	
	@IEmpresa char(3), 
	@ITipo char(1), 
	@ISerieReferencia char(3), 
	@IReferencia int, 
	@IIndice smallint  = NULL, 
	@IDocumento varchar(20) = NULL , 
	@IPeriodo tinyint  = NULL, 
	@IFecha smalldatetime  = NULL, 
	@ICuenta char(16)  = NULL, 
	@IDebe money = NULL , 
	@IHaber money = NULL , 
	@IStatus char(1)  = NULL, 
	@IUsuarioModificacion char(5) = NULL , 
	@IFechaModificacion smalldatetime = NULL , 
	@IUsuario char(5)  = NULL, 
	@IFechaRegistro smalldatetime = NULL,
	@IPartida smallint = NULL , 
	@ICliente int = NULL , 
	@ICaja tinyint = NULL , 
	@ICorte smallint = NULL , 
	@ISucursal char(3) = NULL , 
	@ICategoria varchar(2) = NULL , 
	@ITipoVenta varchar(1) = NULL  
)
AS
BEGIN
	DECLARE 
	@ErrRaise varchar (99)

	SET NOCOUNT ON
	
	IF @IOpcion = 'C'
	BEGIN
		SELECT * FROM [CONTADB].[dbo].[AuxiliarClientes]
		WHERE [Empresa] = @IEmpresa
			AND [Tipo] = @ITipo
			AND [SerieReferencia] = @ISerieReferencia
			AND [Referencia] = @IReferencia
			AND [Indice] = @IIndice
	END
	ELSE IF @IOpcion = 'C0'
	BEGIN
		SELECT * FROM [CONTADB].[dbo].[AuxiliarClientes]
		WHERE [Empresa] = @IEmpresa
			AND [Tipo] = @ITipo
			AND [SerieReferencia] = @ISerieReferencia
			AND [Referencia] = @IReferencia
	END
	ELSE IF @IOpcion = 'I'
	BEGIN
		INSERT INTO [CONTADB].[dbo].[AuxiliarClientes]
			(Empresa, Tipo, SerieReferencia, Referencia, Indice,
				Documento, Periodo, Fecha, Cuenta, Debe, Haber, Status, UsuarioModificacion, FechaModificacion, Usuario, FechaRegistro, 
				Partida, Cliente, Caja, Corte, Sucursal, Categoria, TipoVenta )
		VALUES
			(@IEmpresa, @ITipo, @ISerieReferencia, @IReferencia, @IIndice, 
				@IDocumento, @IPeriodo, @IFecha, @ICuenta, @IDebe, @IHaber, @IStatus, @IUsuarioModificacion, @IFechaModificacion, @IUsuario, COALESCE(@IFechaRegistro, CAST(GETDATE() AS SMALLDATETIME)),
				@IPartida, @ICliente, @ICaja, @ICorte, @ISucursal, @ICategoria, @ITipoVenta )
		SELECT 0 AS codigo, 'Datos registrados correctamente' AS descripcion, 0 AS tipo_error, @@rowcount as filas;	
	END
	ELSE IF @IOpcion = 'A'
	BEGIN
		UPDATE [CONTADB].[dbo].[AuxiliarClientes] SET 
			[Empresa]=@IEmpresa, 
			[Tipo]=@ITipo, 
			[SerieReferencia]=@ISerieReferencia, 
			[Referencia]=@IReferencia, 
			[Indice]=@IIndice, 
			[Documento]=@IDocumento, 
			[Periodo]=@IPeriodo, 
			[Fecha]=@IFecha, 
			[Cuenta]=@ICuenta, 
			[Debe]=@IDebe, 
			[Haber]=@IHaber, 
			[Status]=@IStatus, 
			[UsuarioModificacion]=@IUsuarioModificacion, 
			[FechaModificacion]=@IFechaModificacion, 
			[Usuario]=@IUsuario, 
			[FechaRegistro]=@IFechaRegistro, 
			[Partida]=@IPartida, 
			[Cliente]=@ICliente, 
			[Caja]=@ICaja, 
			[Corte]=@ICorte, 
			[Sucursal]=@ISucursal, 
			[Categoria]=@ICategoria, 
			[TipoVenta]=@ITipoVenta
		WHERE [Empresa] = @IEmpresa
			AND [Tipo] = @ITipo
			AND [SerieReferencia] = @ISerieReferencia
			AND [Referencia] = @IReferencia
			AND [Indice] = @IIndice
			
		SELECT 0 AS codigo, 'Registro Actualizado' AS descripcion, 0 AS tipo_error, @@rowcount as filas;
	END
	ELSE IF @IOpcion = 'E'
	BEGIN
		DELETE FROM [CONTADB].[dbo].[AuxiliarClientes] 
		WHERE [Empresa] = @IEmpresa
			AND [Tipo] = @ITipo
			AND [SerieReferencia] = @ISerieReferencia
			AND [Referencia] = @IReferencia
			AND [Indice] = @IIndice
		SELECT 0 AS codigo, 'Registro Eliminado' AS descripcion, 0 AS tipo_error, @@rowcount as filas;
	END
	ELSE
	BEGIN
		SET	@ErrRaise = CONCAT('No se ha encontrado la opción ', @IOpcion, ' ','[HOSPITAL].[dbo].[SpAuxiliarClientes]')
		RAISERROR(@ErrRaise, 16, 1)
	END
END


-->>
USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[SpBusquedaBancosTarjetas]    Script Date: 12/09/2023 22:00:39 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


-- ==========================================================
-- Author: Esteban Palacios Kestler
-- Create date: 2023-02-13
-- Description: Para búsqueda de catálogo [INVENTARIO].[dbo].[BancosTarjetas] en base a la empresa real
-- ==========================================================

CREATE PROCEDURE [dbo].[SpBusquedaBancosTarjetas]
        @IPagina int = 1,
        @IEmpresa varchar(3) = null,
		@ITipo varchar(1) = null,
        @ICodigo varchar(3) = null,
		@INombre varchar(60) = null
AS
BEGIN
        SET NOCOUNT ON;

        SELECT TOP 50
		RTRIM(Codigo) as Codigo, RTRIM(Nombre) as Nombre
        FROM [INVENTARIO].[dbo].[BancosTarjetas]
        WHERE 
			Empresa = @IEmpresa
			AND Tipo = @ITipo
			AND (@ICodigo IS NULL OR Codigo like '%'+ UPPER(@ICodigo) + '%' )
			AND (@INombre IS NULL OR Nombre like '%'+ @INombre + '%' )
        ORDER BY Codigo, Nombre
END



-->>

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[SpRecibosPagos]    Script Date: 12/09/2023 21:59:59 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


-- =======================================================================================
--	CREADO POR: Esteban Palacios Kestler
--	FECHA CREACIÓN: 09/01/2023
--	PRODUCTO: RTC281343	- Jefe de Caja
--  DESCRIPCION: Implementa operaciones CRUD sobre la tabla[HOSPITAL][dbo].[RecibosPagos]
-- =======================================================================================
CREATE PROCEDURE [dbo].[SpRecibosPagos](

	@IOpcion VARCHAR(3) = '', 
	-- C = Consulta de
	-- C1 Obtener todos los pagos de un recibo
	-- I = Ingreso 
	-- A = Actualizar 
	-- E = Eliminación 	
	@IEmpresa char(3),
	@ISerieRecibo char(3),
	@IRecibo int,
	@ILinea tinyint= NULL,
	@IMonto money = NULL ,
	@IForma char(1)= NULL,
	@IBancoTarjeta char(3) = NULL ,
	@ICuenta varchar(50) = NULL ,
	@IDocumento int = NULL, 
	@IAutorizacion char(10) = NULL, 
	@ITasa smallmoney = NULL, 
	@IVuelto money = NULL ,
	@IRemision char(10) = NULL ,
	@IFechaEfectividad smalldatetime = NULL ,
	@ITipo char(1) = NULL ,
	@IStatus char(1) = 'H',
	@IPrevisado char(1) = 'N',
	@IPagoDirecto char(1) = NULL, 
	@IFechaPago smalldatetime = NULL 
)
AS
BEGIN
	DECLARE 
	@ErrRaise varchar (99)

	SET NOCOUNT ON
	
	IF @IOpcion = 'C'
	BEGIN
		SELECT * FROM [dbo].[RecibosPagos] 
		WHERE [Empresa] = @IEmpresa
			AND [SerieRecibo] = @ISerieRecibo
			AND [Recibo] = @IRecibo
			AND [Linea] = @ILinea
	END
	IF @IOpcion = 'C0'
	BEGIN
		SELECT * FROM [dbo].[RecibosPagos] 
		WHERE [Empresa] = @IEmpresa
			AND [SerieRecibo] = @ISerieRecibo
			AND [Recibo] = @IRecibo
			AND [Linea] = @ILinea
	END
	ELSE IF @IOpcion = 'C1' --Obtener todos los pagos de un recibo
	BEGIN
		SELECT rp.* ,bt.nombre as NombreBancoTarjeta
		FROM [dbo].[RecibosPagos] rp
		LEFT JOIN [INVENTARIO].[dbo].[BancosTarjetas] bt on bt.Empresa = rp.Empresa AND bt.tipo = rp.Forma AND bt.Codigo = rp.BancoTarjeta
		WHERE rp.[Empresa] = @IEmpresa
			AND rp.[SerieRecibo] = @ISerieRecibo
			AND rp.[Recibo] = @IRecibo
	END
	ELSE IF @IOpcion = 'I'
	BEGIN
		INSERT INTO [dbo].[RecibosPagos]
			(Empresa, SerieRecibo, Recibo, Linea, Monto, Forma, BancoTarjeta, Cuenta, Documento, Autorizacion, Tasa, Vuelto, Remision, FechaEfectividad, Tipo, Status, Previsado, PagoDirecto, FechaPago)
		VALUES
			(@IEmpresa, @ISerieRecibo, @IRecibo, @ILinea, @IMonto,	@IForma, @IBancoTarjeta, @ICuenta, @IDocumento, @IAutorizacion, @ITasa, @IVuelto, @IRemision, @IFechaEfectividad, @ITipo, @IStatus, @IPrevisado, @IPagoDirecto, @IFechaPago)
		SELECT 0 AS codigo, 'Pago registrado correctamente' AS descripcion, 0 AS tipo_error, @@rowcount as filas;	
	END
	ELSE IF @IOpcion = 'A'
	BEGIN
		UPDATE [dbo].[RecibosPagos] SET 
			[Monto]=@IMonto,
			[Forma]=@IForma,
			[BancoTarjeta]=@IBancoTarjeta,
			[Cuenta]=@ICuenta,
			[Documento]=@IDocumento, 
			[Autorizacion]=@IAutorizacion, 
			[Tasa]=@ITasa, 
			[Vuelto]=@IVuelto,
			[Remision]=@IRemision,
			[FechaEfectividad]=@IFechaEfectividad,
			[Tipo]=@ITipo,
			[Status]=@IStatus,
			[Previsado]=@IPrevisado,
			[PagoDirecto]=@IPagoDirecto, 
			[FechaPago]=@IFechaPago 
		WHERE	[Empresa] = @IEmpresa
			AND [SerieRecibo] = @ISerieRecibo
			AND [Recibo] = @IRecibo
			AND [Linea] = @ILinea
		SELECT 0 AS codigo, 'Pago Actualizado' AS descripcion, 0 AS tipo_error, @@rowcount as filas;
	END
	ELSE IF @IOpcion = 'E'
	BEGIN
		DELETE FROM [dbo].[RecibosPagos] 
		WHERE [Empresa] = @IEmpresa
			AND [SerieRecibo] = @ISerieRecibo
			AND [Recibo] = @IRecibo
			AND [Linea] = @ILinea
		SELECT 0 AS codigo, 'Pago Eliminado' AS descripcion, 0 AS tipo_error, @@rowcount as filas;
	END
	ELSE
	BEGIN
		SET	@ErrRaise = CONCAT('No se ha encontrado la opción ', @IOpcion, ' ','[HOSPITAL][dbo].[RecibosPagos]')
		RAISERROR(@ErrRaise, 16, 1)
	END
END



-->>

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[SpHisAnulaFacClinica]    Script Date: 12/09/2023 21:59:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[SpHisAnulaFacClinica] 

(
	-- PARAMETROS DEFECTO 
	-- ========================================================== 
	-- Tipos de Operaciones
	@IOpcion VARCHAR(1) = ''
		-- C = Consulta 
		-- I = Ingreso 
		-- A = Actualizar 
		-- E = Eliminación 
	, @IEmpresa VARCHAR(3) =''
	, @ISerieFactura VARCHAR(20) = ''
	, @INumeroFactura VARCHAR(60) = ''
	, @IUsuario VARCHAR(5) = ''
	, @IRazon CHAR(40) = ''
	, @ITipoAnulacion VARCHAR(4) = ''
		-- Tipos de Anulaciones
		-- AF = Anulaciones Facturas
		-- AFC = Anulaciones Facturas Clinicas
		-- AFCT = Anulaciones Facturas Cafeteria
)
AS  
BEGIN 

	-- DEFINICIÓN DE VARIABLES 
	-- ========================================================== 
	 DECLARE
	  @SubOpcion VARCHAR(2) = ''
	, @FacFecha SMALLDATETIME = ''
	, @FacNit VARCHAR(10) = ''
	, @FacNombre VARCHAR(60) = ''
	, @FacStatus CHAR(1) = ''
	, @FacAnulaTipo CHAR(1) = ''
	, @FacCorte SMALLINT = 0
	, @FacBodega INT = 0
	, @Porcentaje SMALLMONEY = 0
    , @FacDetalleProducto VARCHAR(13)
	, @FacDetalleCantidad FLOAT = 0
    , @CantTotal INT = 0
    , @RowID INT = 1
	, @FacTipo TINYINT = 0

	-- VARIABLES ERROR DEL SISTEMA
    -- ========================================================== 
	, @ErrRaise VARCHAR(99) = ''
	, @ErrRaiseNum INT = 0


    SET NOCOUNT ON; 

    IF @ITipoAnulacion = 'AFC' 
	BEGIN
		

		IF @IOpcion = 'A'
		BEGIN 

			-- FacturasQuery
			-- ==========================================================
			-- Recuperando datos de Factura
			-- ==========================================================
			SELECT 
				DISTINCT 
				@IEmpresa = f.Empresa 
				, @ISerieFactura = f.Serie
				, @INumeroFactura = f.Codigo
				, @FacFecha = Fecha 
				, @FacNit = Nit
				, @FacNombre = Nombre
				, @FacStatus =f.Status
				, @FacAnulaTipo = CASE WHEN numeroface IS NULL THEN 'A' 
									ELSE 'X'
								 END 
				, @FacCorte =f.Corte
				, @FacBodega = f.Bodega
				, @FacTipo = f.tipo
			FROM Facturas f WITH (NOLOCK)
			LEFT JOIN FacturasFace e WITH (NOLOCK) 
				ON e.empresa = f.empresa 
				AND e.serie = f.serie 
				AND e.codigo = f.codigo
				and e.tipodoc = 1
			WHERE f.Empresa = @IEmpresa
				AND f.Serie = @ISerieFactura
				AND f.Codigo = @INumeroFactura

			-- ==========================================================
			-- PROCESO SpHisAnulaTbFactorizadas
			-- ==========================================================
			-- Actualizando estado en facturas 
			-- ========================================================== 
			SET @SubOpcion = '1'
			EXEC SpHisAnulaTbFactorizadas 
			@IOpcion, @IEmpresa, @ISerieFactura, @INumeroFactura, '', '', NULL, NULL, '', NULL, @FacFecha, @FacAnulaTipo, @SubOpcion, '', @ITipoAnulacion;


			-- ==========================================================
			-- Actualizar existencia
			-- ==========================================================

			WITH TB_VENTA_DIRECTA_DETALLE AS (
				SELECT 
					  vdd.Empresa
					, vdd.SerieFactura
					, vdd.Factura
					, vdd.Linea
					, vdd.Producto
					, getdate() as 'FechaActual'
					, vdd.Categoria
					, vdd.Valor
					, vdd.Cantidad
					, vdd.PrecioUnitario
					, vdd.Factor
					, vdd.UnidadMedida
					, vdd.Costo
					, vdd.Sucursal
					--, (vdd.Costo * @Porcentaje) as CostoHospital
					, vdd.CostoUltimo
					, ff.Bodega as Bodega
					, ffc.SerieFace
					, ffc.NumerofACE
			FROM VentaDirectaDetalle vdd
			INNER JOIN Facturas ff --Obtener bodega
					on		ff.serie = vdd.seriefactura
					and		ff.codigo = vdd.factura
					and		ff.Empresa = vdd.Empresa
			INNER JOIN hospital..facturasface ffc --Obtener datos FEL
					on		ffc.serie = ff.serie 
					and		ffc.codigo = ff.codigo
					and		ffc.empresa  = ff.empresa
					and		ffc.TipoDoc = 1 --Busqueda unicamente facturas

			WHERE vdd.empresa = @IEmpresa
				AND vdd.seriefactura = @ISerieFactura
				AND vdd.factura = @INumeroFactura
			)
			SELECT * INTO #TB_VENTA_DIRECTA_DETALLE FROM TB_VENTA_DIRECTA_DETALLE;


			UPDATE EB SET EB.EXISTENCIA = EXISTENCIA + VDD.CANTIDAD
					   FROM #TB_VENTA_DIRECTA_DETALLE AS VDD
					   INNER JOIN  INVENTARIO..EXISTENCIASBODEGA EB
					   ON   EB.BODEGA	 = VDD.Bodega
					   AND  EB.PRODUCTO  = VDD.Producto;



			-- ==========================================================
			-- Insertando en Anulación
			-- ========================================================== 
			SET @SubOpcion = '2'
			SET @FacFecha = CAST(GETDATE() AS SMALLDATETIME)

			EXEC SpHisAnulaTbFactorizadas 
			@IOpcion, @IEmpresa, @ISerieFactura, @INumeroFactura ,'F', @IRazon, NULL, NULL, @IUsuario, NULL, @FacFecha, '', @SubOpcion,'', @ITipoAnulacion


			-- ==========================================================
			-- Devolución de dinero.
			-- ========================================================== 
			UPDATE HOSPITAL..VENTADIRECTAPAGOS SET MONTO = 0
			WHERE SERIEFACTURA = @ISerieFactura
			AND	  FACTURA = @INumeroFactura
			AND EMPRESA = @IEmpresa

			-- ==========================================================
			-- Descargando de Nomina
			-- ========================================================== 
			DELETE FROM Nominadb..EmpAnticiposDetalle
			WHERE SERIEFACTURA = @ISerieFactura
			AND	  FACTURA = @INumeroFactura
			AND EMPRESA = @IEmpresa


			-- ==========================================================
			-- Eliminando detalle de fac
			-- ========================================================== 
			Delete from Hospital..VentaDirectaDetalle
			where factura = @INumeroFactura
			and SerieFactura = @ISerieFactura
			and Empresa = @IEmpresa





			-- ==========================================================
			-- Inserta bitacora de facturas de citas anuladas
			-- ========================================================== 
			--use planesmedicos
			INSERT PLANESMEDICOS..FacturasCitasAnuladas 
				   SELECT IdEmpresa, base, idcita, seriefactura,
						  factura, getdate(), @IUsuario
				   FROM PLANESMEDICOS..PagoCitas
				   WHERE SERIEFACTURA = @ISerieFactura 
					     AND FACTURA = @INumeroFactura;



			-- ==========================================================
			-- Cambio de estado de cita a activa para anularla.
			-- ========================================================== 
			UPDATE PLANESMEDICOS..CITAS SET IDSTATUS= 1
				   From PLANESMEDICOS..pagocitas pc 
				   inner join PLANESMEDICOS..Citas c 
						on (pc.IdCita = c.IdCita)
				   Where pc.Factura    = @INumeroFactura 
					 and pc.SerieFactura = @ISerieFactura 




			-- ==========================================================
			-- Reactiva cita confirmada y elimina siniestralidad
			-- ========================================================== 
			update PLANESMEDICOS..PagoCitas Set Factura = Null, SerieFactura = Null,
												Monto_Siniestralidad = 0, TomaSignos ='N'
					WHERE SerieFactura = @ISerieFactura AND Factura = @INumeroFactura  



			-- ==========================================================
			-- Revierte recibos ssgh emitidos
			-- ========================================================== 
			Update PLANESMEDICOS..AfiliadosRecibosDeCuotas Set IdFactura = NULL where IdFactura = concat(rtrim(@ISerieFactura), '-', rtrim(@INumeroFactura));
			Update PLANESMEDICOS..AfiliadosPagosDetalle  set IdFacturoproceso = NULL  Where IdFacturoProceso =  ( select IdProceso from Hospital..FacturasCuotasPlanXAseguradora where ceface = concat(rtrim(@ISerieFactura), '-', rtrim(@INumeroFactura)));

			-- ==========================================================
			--Obtiene datos de Afiliadospagosdetalle
			-- ==========================================================
			DECLARE 
				@INumeroAdhesion varchar(25) = '',
				@INumeroTransaccion int = 0,
				@IIdFactura varchar(35) = '';


			select top 1
			@INumeroAdhesion  = NumeroAdhesion,  
			@INumeroTransaccion = NumeroTransaccion, 
			@IIdFactura = IdFactura
			from PLANESMEDICOS..afiliadospagosdetalle
			where idfactura = concat(rtrim(@ISerieFactura) , '-', rtrim(@INumeroFactura))
--			USE PLANESMEDICOS
			-- ==========================================================
			-- elimina cuotas pagadas
			-- ========================================================== 
			Delete from PLANESMEDICOS..AfiliadosPagoDeCuotas
			where NumeroAdhesion  = @INumeroAdhesion
			and NumeroTransaccion =  @INumeroTransaccion


			-- ==========================================================
			-- elimina detalle (encabezado de cuotas)
			-- ========================================================== 
			delete from PLANESMEDICOS..AfiliadosPagosDetalle where IdFactura =  concat(rtrim(@ISerieFactura) , '-', rtrim(@INumeroFactura))
			and NumeroAdhesion = @INumeroAdhesion;


			-- ==========================================================
			-- elimina la provision de comision por planes
			-- ========================================================== 
			delete from PLANESMEDICOS..MultinivelesPagos  
			Where IdEmpresa =  @IEmpresa
			and NumeroAdhesion  = @INumeroAdhesion
			and NumeroTransaccion = @INumeroTransaccion;

			-- ==========================================================
			-- quita liquidacion de cupon (actualiza)
			-- ========================================================== 
			update PLANESMEDICOS..Pagocupones Set Factura = Null, SerieFactura = Null
			Where Factura    = @INumeroFactura
			and SerieFactura = @ISerieFactura;


			-- ==========================================================
			-- desamarra las facturas de manejo de cuenta - web sighos 2022.01.25
			-- ========================================================== 
			Update Hospital..His_honorarios_Lotes_Factura 
			SET FacturaHospitalSerie  = NULL, 
			    FacturaHospitalNumero  = NULL 
			Where EmpresaReal = @IEmpresa
			and FacturaHospitalSerie  =  @ISerieFactura
			and FacturaHospitalNumero =  @INumeroFactura;


			-- ==========================================================
			-- Insertando en INVENTARIO..InvCargosDetalle
			-- ========================================================== 
			
			if (@FacTipo = 8)
			BEGIN
				SET @SubOpcion = '3'
				SET @FacFecha = CAST(GETDATE() AS SMALLDATETIME)

				 
				EXEC SpHisAnulaTbFactorizadas 
				@IOpcion, @IEmpresa, @ISerieFactura, @INumeroFactura,'', '', NULL, NULL,'', NULL,'', '', @SubOpcion, 'CL', @ITipoAnulacion
			END;


			/********************* FINALIZACIÓN ANULACION CLINICA ******************/
			/***********************************************************************/


		END --- Final Opción 'A'

		-- ========================================================== 
		-- ERROR DEL SISTEMA
		-- ==========================================================
			
		SET @ErrRaise = ERROR_MESSAGE() 
		SET @ErrRaiseNum = ERROR_NUMBER() 

		IF @ErrRaise  IS NULL
		BEGIN
			SELECT 0 AS codigo, 'Ha finalizado el proceso de anulacion de Factura' 
				AS descripcion, 0 AS tipo_error
			RETURN
		END 
		ELSE 
		BEGIN
			RAISERROR(@ErrRaise,16,@ErrRaiseNum) 
		END  
	END -- 
END	

-->>


USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[SpRecibos]    Script Date: 12/09/2023 21:59:34 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


-- =======================================================================================
--	CREADO POR: Esteban Palacios Kestler
--	FECHA CREACIÓN: 09/01/2023
--	PRODUCTO: RTC281343	- Jefe de Caja
--  DESCRIPCION: Implementa operaciones CRUD sobre la tabla[HOSPITAL][dbo].[Recibos]
-- =======================================================================================
CREATE PROCEDURE [dbo].[SpRecibos](

	@IOpcion VARCHAR(3) = '', 
	-- C = Consulta de
	-- I = Ingreso 
	-- A = Actualizar 
	-- E = Eliminación 	
	@IEmpresa char(3), 
	@ISerie char(3), 
	@ICodigo int, 
	@IFecha smalldatetime = NULL, 
	@ICaja tinyint = NULL, 
	@IUsuario char(5) = NULL, 
	@ISerieAdmision char(1) = NULL , 
	@IAdmision int = NULL , 
	@IPaciente int = NULL , 
	@INombre varchar(60) = NULL, 
	@ITipo char(1) = NULL, 
	@IStatus char(1) = 'P', 
	@ICorte smallint = NULL , 
	@ITotal money = NULL, 
	@IRegistro smalldatetime = NULL, 
	@IPeriodo tinyint = NULL , 
	@IAplicado money = 0, 
	@IObservaciones varchar(100) = NULL , 
	@IAnticipoAplienMes char(1) = NULL , 
	@ISucursal char(3) = NULL , 
	@IContrato int = NULL , 
	@IAfiliado int = NULL , 
	@IIdPQTPaciente int = NULL , 
	@ISobregiro bit = NULL 
)
AS
BEGIN
	DECLARE 
	@ErrRaise varchar (99)

	SET NOCOUNT ON
	
	IF @IOpcion = 'C'
	BEGIN
		SELECT * FROM [dbo].[Recibos] 
		WHERE [Empresa] = @IEmpresa
			AND [Serie] = @ISerie
			AND [Codigo] = @ICodigo
	END
	ELSE IF @IOpcion = 'C0'---Datos del recibo con información adicional que se requiera
	BEGIN
		SELECT R.*, [dbo].[FnNombreCompletoPaciente](R.Paciente) AS NombrePaciente FROM [dbo].[Recibos] AS R
		WHERE [Empresa] = @IEmpresa
			AND [Serie] = @ISerie
			AND [Codigo] = @ICodigo
	END
	ELSE IF @IOpcion = 'I'
	BEGIN
		INSERT INTO [dbo].[Recibos]
			(Empresa, Serie, Codigo, Fecha, Caja, Usuario, SerieAdmision, Admision, Paciente, Nombre, Tipo, Status, Corte, Total, Registro, Periodo, Aplicado, Observaciones, AnticipoAplienMes, Sucursal, Contrato, Afiliado, IdPQTPaciente, Sobregiro )
		VALUES
			(@IEmpresa, @ISerie, @ICodigo, @IFecha, @ICaja, @IUsuario, @ISerieAdmision, @IAdmision, @IPaciente, @INombre, @ITipo, @IStatus, @ICorte, @ITotal, @IRegistro, @IPeriodo, @IAplicado, @IObservaciones, @IAnticipoAplienMes, @ISucursal, @IContrato, @IAfiliado, @IIdPQTPaciente, @ISobregiro )
		SELECT 0 AS codigo, 'Recibo registrado correctamente' AS descripcion, 0 AS tipo_error, @@rowcount as filas;	
	END
	ELSE IF @IOpcion = 'A'
	BEGIN
		UPDATE [dbo].[Recibos] SET 
			[Empresa]=@IEmpresa, 
			[Serie]=@ISerie, 
			[Codigo]=@ICodigo, 
			[Fecha]=@IFecha, 
			[Caja]=@ICaja, 
			[Usuario]=@IUsuario, 
			[SerieAdmision]=@ISerieAdmision, 
			[Admision]=@IAdmision, 
			[Paciente]=@IPaciente, 
			[Nombre]=@INombre, 
			[Tipo]=@ITipo, 
			[Status]=@IStatus, 
			[Corte]=@ICorte, 
			[Total]=@ITotal, 
			[Registro]=@IRegistro, 
			[Periodo]=@IPeriodo, 
			[Aplicado]=@IAplicado, 
			[Observaciones]=@IObservaciones, 
			[AnticipoAplienMes]=@IAnticipoAplienMes, 
			[Sucursal]=@ISucursal, 
			[Contrato]=@IContrato, 
			[Afiliado]=@IAfiliado, 
			[IdPQTPaciente]=@IIdPQTPaciente, 
			[Sobregiro]=@ISobregiro
		WHERE	[Empresa] = @IEmpresa
			AND [Serie] = @ISerie
			AND [Codigo] = @ICodigo
			
		SELECT 0 AS codigo, 'Recibo Actualizado' AS descripcion, 0 AS tipo_error, @@rowcount as filas;
	END
	ELSE IF @IOpcion = 'E'
	BEGIN
		DELETE FROM [dbo].[Recibos] 
		WHERE [Empresa] = @IEmpresa
			AND [Serie] = @ISerie
			AND [Codigo] = @ICodigo
		SELECT 0 AS codigo, 'Recibo Eliminado' AS descripcion, 0 AS tipo_error, @@rowcount as filas;
	END
	ELSE
	BEGIN
		SET	@ErrRaise = CONCAT('No se ha encontrado la opción ', @IOpcion, ' ','[HOSPITAL].[dbo].[Recibos]')
		RAISERROR(@ErrRaise, 16, 1)
	END
END


-->>Permisos
USE HOSPITAL
GO	
GRANT EXECUTE ON OBJECT::dbo.SpModificaciones TO USRHISJEFECAJA;
GRANT EXECUTE ON OBJECT::dbo.SpAuxiliarClientes TO USRHISJEFECAJA;
GRANT EXECUTE ON OBJECT::dbo.SpBusquedaBancosTarjetas TO USRHISJEFECAJA;
GRANT EXECUTE ON OBJECT::dbo.SpRecibosPagos TO USRHISJEFECAJA;
GRANT EXECUTE ON OBJECT::dbo.SpHisAnulaFacClinica TO USRHISJEFECAJA;
GRANT EXECUTE ON OBJECT::dbo.SpRecibos TO USRHISJEFECAJA;
	
GRANT SELECT ON dbo.Modificaciones  TO USRHISJEFECAJA; 
GRANT INSERT ON dbo.Modificaciones  TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.RecibosPagos  TO USRHISJEFECAJA; 
GRANT INSERT ON dbo.RecibosPagos  TO USRHISJEFECAJA; 
GRANT UPDATE ON dbo.RecibosPagos  TO USRHISJEFECAJA; 
GRANT DELETE ON dbo.RecibosPagos  TO USRHISJEFECAJA; 
​
GRANT SELECT ON dbo.Facturas  TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.FacturasFace  TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.VentaDirectaDetalle  TO USRHISJEFECAJA; 
GRANT DELETE ON dbo.VentaDirectaDetalle  TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.VENTADIRECTAPAGOS  TO USRHISJEFECAJA;
GRANT SELECT ON dbo.FacturasCuotasPlanXAseguradora  TO USRHISJEFECAJA;
GRANT SELECT ON dbo.His_honorarios_Lotes_Factura  TO USRHISJEFECAJA;
GRANT UPDATE ON dbo.His_honorarios_Lotes_Factura  TO USRHISJEFECAJA;
​
GRANT SELECT ON dbo.Recibos  TO USRHISJEFECAJA;
GRANT UPDATE ON dbo.Recibos  TO USRHISJEFECAJA;
GRANT INSERT ON dbo.Recibos  TO USRHISJEFECAJA;
GRANT DELETE ON dbo.Recibos  TO USRHISJEFECAJA;
GRANT SELECT ON dbo.FnNombreCompletoPaciente  TO USRHISJEFECAJA;					
										
							
-->>											
USE CONTADB
GO		
GRANT SELECT ON dbo.AuxiliarClientes  TO USRHISJEFECAJA;
GRANT INSERT ON dbo.AuxiliarClientes  TO USRHISJEFECAJA;
GRANT UPDATE ON dbo.AuxiliarClientes  TO USRHISJEFECAJA;
GRANT DELETE ON dbo.AuxiliarClientes  TO USRHISJEFECAJA;
​
-->>																		
USE INVENTARIO
GO		
GRANT SELECT ON dbo.BancosTarjetas  TO USRHISJEFECAJA;
GRANT SELECT ON dbo.EXISTENCIASBODEGA  TO USRHISJEFECAJA;
GRANT UPDATE ON dbo.EXISTENCIASBODEGA  TO USRHISJEFECAJA;
GRANT DELETE ON dbo.AuxiliarClientes  TO USRHISJEFECAJA;							
​
-->>																		
USE Nominadb
GO		
GRANT SELECT ON dbo.EmpAnticiposDetalle  TO USRHISJEFECAJA;
GRANT DELETE ON dbo.EmpAnticiposDetalle  TO USRHISJEFECAJA;
			
			
​
USE PLANESMEDICOS
GO	
GRANT SELECT ON dbo.FacturasCitasAnuladas  TO USRHISJEFECAJA;
GRANT INSERT ON dbo.FacturasCitasAnuladas  TO USRHISJEFECAJA;
GRANT SELECT ON dbo.PagoCitas  TO USRHISJEFECAJA;
GRANT UPDATE ON dbo.PagoCitas  TO USRHISJEFECAJA;
GRANT UPDATE ON dbo.CITAS  TO USRHISJEFECAJA;	
GRANT SELECT ON dbo.AfiliadosRecibosDeCuotas  TO USRHISJEFECAJA;
GRANT UPDATE ON dbo.AfiliadosRecibosDeCuotas  TO USRHISJEFECAJA;
GRANT SELECT ON dbo.AfiliadosPagosDetalle  TO USRHISJEFECAJA;
GRANT UPDATE ON dbo.AfiliadosPagosDetalle  TO USRHISJEFECAJA;
GRANT DELETE ON dbo.AfiliadosPagosDetalle  TO USRHISJEFECAJA;
GRANT SELECT ON dbo.AfiliadosPagoDeCuotas  TO USRHISJEFECAJA;
GRANT UPDATE ON dbo.AfiliadosPagoDeCuotas  TO USRHISJEFECAJA;		
GRANT SELECT ON dbo.MultinivelesPagos  TO USRHISJEFECAJA;
GRANT DELETE ON dbo.MultinivelesPagos  TO USRHISJEFECAJA;
GRANT SELECT ON dbo.Pagocupones  TO USRHISJEFECAJA;
GRANT UPDATE ON dbo.Pagocupones  TO USRHISJEFECAJA;
									