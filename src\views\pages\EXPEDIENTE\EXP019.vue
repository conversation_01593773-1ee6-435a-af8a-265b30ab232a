<template>
<vs-row class="p-2 w-full">
    <vs-col vs-justify="center" vs-align="center" vs-w="10">
        <vx-card>
            <DxDataGrid :ref="dataGridRefKey" v-bind="DefaultDxGridConfiguration" :headerFilter="{visible:false,allowSearch:false}" :data-source="excretaDataSource" :height="'100%'" :no-data-text="'Seleccione una fecha para cargar información'" @editor-preparing="editorHorarioValue">
                <DxSelection mode="single" />
                <DxToolbar>
                    <DxItem name="groupPanel" />
                    <DxItem name="searchPanel" />
                    <DxItem location="after" template="opcionesTemplate" />
                </DxToolbar>

                <DxGroupPanel :visible="true" />
                <DxGrouping :auto-expand-all="false" />

                <DxEditing :allow-updating="false" :allow-adding="true" :allow-deleting="false" mode="popup">
                    <DxPopup v-bind="popupConf" :height="400" title="Registro de ingesta">
                        <DxToolbarItem widget="dxButton" toolbar="bottom" :options="{
                                        text: 'Guardar',
                                        type: 'success',
                                        useSubmitBehavior: true,
                                        onClick: submit
                                    }" location="after">
                        </DxToolbarItem>
                        <DxToolbarItem widget="dxButton" toolbar="bottom" :options="{
                                        text: 'Cancelar',
                                        type: 'danger',
                                        useSubmitBehavior: true,
                                        onClick: ()=>{this.dataGrid.cancelEditData()}
                                    }" location="after">
                        </DxToolbarItem>
                    </DxPopup>

                    <DxForm labelMode="floating" :ref="dataExcretaFormRefKey">
                        <DxItem item-type="group" :col-span="2" :col-count="2">
                            <DxItem item-type="group" :col-span="1" :col-count="4">
                                <DxItem data-field="Horario" editor-type="dxSelectBox" :col-span="2" :editor-options="horarios" :is-required="true">
                                    <DxLabel :text="'Horario'" />
                                </DxItem>
                                <DxItem data-field="FechaExcreta" editor-type="dxDateBox" :col-span="2" :is-required="true" :editor-options="dateBoxOptions">
                                    <DxLabel :text="'Fecha de excreta'" />
                                </DxItem>
                                <DxItem data-field="Orina" editor-type="dxNumberBox" :editor-options="{ min: 0, max: 5000, format: '#', step: 0 }" />
                                <DxItem data-field="Gastrico" editor-type="dxNumberBox" :editor-options="{ min: 0, max: 5000, format: '#', step: 0 }">
                                    <DxLabel :text="'Gástrico'" />
                                </DxItem>
                                <DxItem data-field="Drenajes" editor-type="dxNumberBox" :editor-options="{ min: 0, max: 5000, format: '#', step: 0 }" />
                                <DxItem data-field="Otros" editor-type="dxNumberBox" :editor-options="{ min: 0, max: 5000, format: '#', step: 0 }" />
                                <DxItem data-field="Heces" editor-type="dxNumberBox" :editor-options="{ min: 0, max: 5000, format: '#', step: 0 }" />
                                <DxItem data-field="Dens" editor-type="dxNumberBox" :editor-options="{ min: 0, max: 5000, format: '#', step: 0 }">
                                    <DxLabel :text="'Dens.'" />
                                </DxItem>
                            </DxItem>
                            <DxItem item-type="group" :col-span="1">
                                <DxItem data-field="Comentarios" editor-type="dxTextArea" :editor-options="{ height: 100}">
                                    <DxLabel :text="'Comentarios'" />
                                </DxItem>
                            </DxItem>
                        </DxItem>
                    </DxForm>
                </DxEditing>

                <DxColumn :width="100" data-field="Horario" alignment="center" />
                <DxColumn :width="110" data-field="FechaExcreta" caption="Fecha excreta" format="dd/MM/yyyy" data-type="date" alignment="center" />
                <DxColumn :width="100" data-field="Orina" alignment="center" />
                <DxColumn :width="100" data-field="Gastrico" caption="Gástrico" alignment="center" />
                <DxColumn :width="100" data-field="Drenajes" alignment="center" />
                <DxColumn :width="100" data-field="Otros" alignment="center" />
                <DxColumn :width="100" data-field="Heces" alignment="center" />
                <DxColumn :width="100" data-field="Dens" caption="Dens." alignment="center" />
                <DxColumn :width="300" data-field="Comentarios" alignment="center" />
                <DxColumn :width="300" data-field="Usuario" alignment="center" />
                <DxColumn :width="150" data-field="Puesto" alignment="center" />
                <DxColumn :width="150" data-field="FechaRegistro" caption="Fecha registro" format="dd/MM/yyyy HH:mm:ss" data-type="date" alignment="center" />

                <template #opcionesTemplate>
                    <ExpedienteGridToolBar v-bind="$props" :visible="true" :pdfExportItems="[{text:'Excreta', reportName: 'Excreta'}]" @add="addRow" @refresh="Cargar(true)" :report-param="$props" />
                </template>
            </DxDataGrid>
        </vx-card>
    </vs-col>
    <vs-col vs-w="2" vs-justify="center" vs-align="center" class="pl-2">
        <vx-card title="Filtrar fecha">
            <DxDataGrid :ref="dataGridFechaKey" v-bind="DefaultDxGridConfiguration" :searchPanel="{visible: false}" :data-source="fechas" key-expr="id" :height="'100%'" @selection-changed="onSelectionChanged">
                <DxSelection mode="single" />
                <DxColumn :width="100" data-field="fecha" caption="Fecha" format="dd/MM/yyyy" data-type="date" alignment="center" />
            </DxDataGrid>
        </vx-card>
    </vs-col>
</vs-row>
</template>

<script>
import {
    DxDataGrid,
    DxColumn,
    DxEditing,
    DxForm,
    DxToolbar,
    DxLabel,
    DxPopup,
    DxSelection,
    DxGrouping,
    DxGroupPanel,
} from 'devextreme-vue/data-grid'
import CustomStore from 'devextreme/data/custom_store'
import ExpedienteGridToolBar from './ExpedienteGridToolBar.vue'
import {
    DxItem
} from 'devextreme-vue/form'
import 'devextreme-vue/lookup'
import {
    DxToolbarItem
} from 'devextreme-vue/popup'
import {
    DefaultDxGridConfiguration,
    popupConf
} from './data'

const dataGridRefKey = 'gridExcreta'
const dataGridFechaKey = 'gridFechaKey'
const dataSignosFormRefKey = 'my-ant-form'
const dataExcretaFormRefKey = 'excreta-form'

export default {
    name: 'Excreta',
    components: {
        DxDataGrid,
        DxColumn,
        ExpedienteGridToolBar,
        DxEditing,
        DxForm,
        DxItem,
        DxToolbar,
        DxPopup,
        DxLabel,
        DxToolbarItem,
        DxSelection,
        DxGrouping,
        DxGroupPanel,
    },
    data() {
        return {
            //Listado de diagnosticos asociados a la admisión
            excreta: [],
            excretaCompleto: [],
            excretaDataSource: new CustomStore({
                key: "id",
                load: () => {
                    return this.excreta;
                },
                insert: (values) => {
                    this.GrabarExcreta(values);
                },
            }),
            fechas: [], //Listado de fechas de excreta
            dataGridRefKey,
            dataSignosFormRefKey,
            dataGridFechaKey,
            dataExcretaFormRefKey,
            DefaultDxGridConfiguration,
            popupConf,
        }
    },
    props: {
        SerieAdmision: null,
        CodigoAdmision: null,
        NumeroExpediente: null,

        StatusAdmision: null,
        horarios: null,
        dateBoxOptions: null,
    },
    methods: {
        submit() {
            this.dataGrid.saveEditData()
            this.getFechasExcreta()
        },
        Cargar(update, dia = 0, mes = 0, año = 0) {
            if (this.SerieAdmision && this.CodigoAdmision)
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaExcretaGrid', {
                    Serie: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    dia: dia,
                    mes: mes,
                    año: año
                })
                .then(resp => {
                    this.excreta = resp.data.json.map((x, index) => {
                        return {
                            id: index,
                            Horario: x.Horario,
                            FechaExcreta: x.Fecha_excreta,
                            Orina: x.orina,
                            Dens: x.dens,
                            Gastrico: x.gastrico,
                            Drenajes: x.drenajes,
                            Otros: x.otros,
                            Heces: x.heces,
                            Comentarios: this.$saltos_tab_to_html(this.$limpiar_saltos_tabulares(x.comentario)),
                            FechaRegistro: x.fecha,
                            Usuario: x.Empleado,
                            Puesto: x.Puesto
                        }
                    })
                    this.excretaDataSource.load().then(
                        () => {
                            if (update) {
                                this.getFechasExcreta();
                                this.refreshFechaDataGrid();
                                this.refreshDataGrid();
                            } else {

                                this.refreshDataGrid();
                            }
                        },
                        () => {
                            //console.error('error loading data customstore' + JSON.stringify(error))
                        }
                    )
                })
            else {
                this.excreta = [];
                this.excretaCompleto = [];
                this.refreshDataGrid();
                this.refreshFechaDataGrid();
            }
        },

        GrabarExcreta(values) {
            let postData = {
                SerieAdmision: this.SerieAdmision,
                Admision: this.CodigoAdmision,
                IdHorario: values.Horario,
                Orina: values.Orina,
                Dens: values.Dens,
                Gastrico: values.Gastrico,
                Drenaje: values.Drenajes,
                Otros: values.Otros,
                Heces: values.Heces,
                Comentario: this.$reemplazar_tabulares(values.Comentarios),
                Fechaexcreta: this.formatFecha(values.FechaExcreta),
            }
            this.axios.post("/app/v1_ExpedienteEvolucion/InsertarExcreta", {
                ...postData
            }).then(() => {
                this.Cargar(true)
                this.$emit('onSaved', )
            })
        },
        refreshDataGrid() {
            this.dataGrid.refresh()
                .then(function () {

                })
                .catch(function () {
                    //console.error('error refreshing: ' + error)
                });
            this.dataGrid.clearSelection();
            this.dataGrid.clearSorting();
            this.dataGrid.pageIndex(0);
            this.dataGrid.clearGrouping()
        },
        refreshFechaDataGrid() {
            this.dataGridFecha.pageIndex(0);
            this.dataGridFecha.clearSelection();
            this.dataGridFecha.clearSorting();
            this.dataGridFecha.columnOption('Fecha', 'sortOrder', 'desc');
        },
        addRow() {
            if (this.StatusAdmision != 'A')
                this.$vs.notify({
                    time: 4000,
                    title: 'Antecedentes',
                    text: 'La admisión no está activa, no se puede agregar nuevo registro',
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'warning',
                    position: 'bottom-center'
                })
            else
                this.dataGrid.addRow();
        },
        formatFecha(date) {
            const year = new Date(date).toLocaleString('default', {
                year: 'numeric'
            });
            const month = new Date(date).toLocaleString('default', {
                month: '2-digit'
            });
            const day = new Date(date).toLocaleString('default', {
                day: '2-digit'
            });

            return [year, month, day].join('');
        },
        formatFechaFiltro(fecha) {
            fecha = fecha.split('/');
            const year = fecha[2];
            const month = fecha[1];
            const day = fecha[0];

            return [month, day, year].join('/');
        },
        getFechasExcreta() {
            if (this.SerieAdmision && this.CodigoAdmision)
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaFechaIngSolExc', {
                    Serie: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    TipoBusqueda: "Excreta"
                })
                .then(resp => {
                    this.fechas = resp.data.json.map((x, index) => {
                        return {
                            id: index,
                            fecha: this.formatFechaFiltro(x.Fecha)
                        }
                    })
                })
            else {
                this.fechas = [];
                this.refreshFechaDataGrid();
            }
        },
        editorHorarioValue(e) {
            if (e.parentType === 'dataRow' && e.dataField === 'Horario') {
                e.setValue(new Date().getHours());
                e.editorOptions.value = new Date().getHours();
            }
            if (e.parentType === 'dataRow' && e.dataField === 'FechaExcreta') {
                e.setValue(new Date());
                e.editorOptions.value = new Date();
            }
        },
        filtrarFecha(fecha) {
            fecha = fecha.split('/');
            const year = fecha[2];
            const month = fecha[0];
            const day = fecha[1];
            this.Cargar(false, day, month, year);
        },
        //Método para detectar cuando se selecciona una fila de la tabla de filtrado de fecha
        onSelectionChanged(e) {
            if (e.selectedRowsData.length > 0) {
                const data = e.selectedRowsData[0].fecha;
                this.filtrarFecha(data);
            }
        },
    },
    watch: {
        'NumeroExpediente'(newval) {
            if (newval != undefined)
                this.Cargar(true)
        },
    },
    mounted() {
        this.Cargar(true);
    },
    computed: {
        ShouldRender() {
            return Boolean(this.SerieAdmision) && Boolean(this.CodigoAdmision) && Boolean(this.NumeroExpediente)
        },

        dataGrid: function () {
            return this.$refs[dataGridRefKey].instance;
        },
        dataGridFecha: function () {
            return this.$refs[dataGridFechaKey].instance;
        }
    },
}
</script>
