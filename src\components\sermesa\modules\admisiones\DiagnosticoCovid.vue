<!-- DiagnosticoCovid.vue -->

<template>
    <div id="app">

        <div class="datosVacunasCovid ">
            <vs-row>
                <div class="checkboxStyle">
                    <vs-col vs-w="4">
                        <div class="w-full  ">
                            <BaseCheckboxGroup
                              :options="checkboxOptions"
                              v-model="optionChecked"
                              :groupName="'diagnostico'">
                              </BaseCheckboxGroup>
                        </div>
                    </vs-col>
                    <vs-col vs-type="flex" vs-w="8">
                        <vs-col vs-w="4">
                            <div class="w-full">
                               <multiselect
                                 v-model="selPrimeraVacuna"
                                 :options="ListadoVacunas"
                                 :disabled="false"
                                 :allow-empty="false"
                                 :taggable="true"
                                 placeholder="Primera Dosis"
                                 track-by="Nombre"
                                 label="Nombre"
                                 @input="onVacunaChanges">
                                 </multiselect>
                            </div>
                        </vs-col>

                        <vs-col vs-type="flex " vs-w="4" class="pl-2">
                            <div class="w-full">
                                <multiselect
                                  v-model="selSegundaVacuna"
                                  :options="ListadoVacunas"
                                  :disabled="false"
                                  :allow-empty="false"
                                  :taggable="true"
                                  placeholder="Segunda Dosis"
                                  track-by="Nombre"
                                  label="Nombre"
                                  @input="onVacunaChanges">
                                  </multiselect>
                            </div>
                        </vs-col>
                        <vs-col vs-type="flex " vs-w="4" class="pl-2">
                            <div class="w-full">
                               <multiselect
                                 v-model="selTerceraVacuna"
                                 :options="ListadoVacunas"
                                 :disabled="false"
                                 :allow-empty="false"
                                 :taggable="true"
                                 placeholder="Tercera Dosis"
                                 track-by="Nombre"
                                 label="Nombre"
                                 @input="onVacunaChanges">
                                 </multiselect>
                            </div>
                        </vs-col>


                    </vs-col>
                </div>
            </vs-row>
        </div>

    </div>
</template>


  
<script>


import BaseCheckboxGroup from "@/components/sermesa/global/checkbox/BaseCheckboxGroup";
import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";

export default {
    name: "DiagnosticoCovid",
    props: {
        IdPaciente: {
            required: true,
            type: Number
        }
    },
    components: {
        BaseCheckboxGroup,
        Multiselect
    },
    data() {
        return {
            DisgnosticoCovid: '',
            selPrimeraVacuna: '',
            selSegundaVacuna: '',
            selTerceraVacuna: '',
            checkboxOptions: [],
            optionChecked: [],
            vacunasSeleccioanadas: [],
            ListadoVacunas: [
                {
                    Codigo: '1',
                    Nombre: 'Moderna'
                },
                {
                    Codigo: '2',
                    Nombre: 'Oxford'
                },
                {
                    Codigo: '3',
                    Nombre: 'AstraZeneca'
                },
                {
                    Codigo: '4',
                    Nombre: 'BioNTech Pfizer'
                },
                {
                    Codigo: '5',
                    Nombre: 'Sputnik V'
                },
                {
                    Codigo: '6',
                    Nombre: 'Jhonson&Jhonson'
                }
            ],
        };
    },
    watch: {
        optionChecked(newValues) {
            this.$emit("checked-options-changed", newValues);
        }
    },
    created(){
    
        this.ConsultarVacunasPaciente()
    },
    activated() {
        
    },
    mounted() {

        this.CargarOpcionesCovid()
        
    },
    methods: {
        async ConsultarVacunasPaciente() {

            if( isNaN( parseInt( this.IdPaciente)) )
            return
            
            const resp = await this.axios.post('/app/v1_admision/ObtenerPacVacunasCovid', {
                Empresa: '',
                IdPaciente: this.IdPaciente

            })
            
            const vacunas = resp.data.json     
            
            this.selPrimeraVacuna= this.ListadoVacunas.find(option => option.Codigo === vacunas.find(dosis => dosis.NumeroDosis === '1')?.Vacuna||'');
            this.selSegundaVacuna= this.ListadoVacunas.find(option => option.Codigo === vacunas.find(dosis => dosis.NumeroDosis === '2')?.Vacuna||'');
            this.selTerceraVacuna= this.ListadoVacunas.find(option => option.Codigo === vacunas.find(dosis => dosis.NumeroDosis === '3')?.Vacuna||'')
            
            
        },
        CargarOpcionesCovid() {
            this.checkboxOptions = [
                {
                    label: 'Diagnostico positivo COVID-19',
                    value: '1',
                }
            ]
        },
        onVacunaChanges() {
            this.vacunasSeleccioanadas = []
            const vacunas = [this.selPrimeraVacuna, this.selSegundaVacuna, this.selTerceraVacuna];
            vacunas.forEach((vacuna, index) => {
                this.vacunasSeleccioanadas.push({
                    Id: (index+1).toString(),
                    Codigo:vacuna?.Codigo||0
                })

            })
            this.$emit("vacunas-seleccionadas", this.vacunasSeleccioanadas);

        }
    }
};
</script>


<style scoped>

.datosVacunasCovid {
    display: flex;
    align-items: center;
    padding-top: 1rem;
    padding-bottom: 1rem;
    padding-left: 1rem;
    padding-right: 1rem;
    border: 1px solid #888;  
    border-radius: 5px;
    margin: 5px;     
}

.checkboxStyle {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}
</style>
