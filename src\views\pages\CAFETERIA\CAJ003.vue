<template>
    <div>
        <vs-popup :title="tituloTipoOrdenes" :active.sync="isVisible" style="z-index:99998;">
            <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                <div class="flex flex-wrap">
                    <div class="w-full">
                        <div class="flex flex-wrap">
                            <div class="default-div">
                                <ValidationProvider name="codigo" rules="required|max:3" class="required" v-slot="{ errors }">
                                    <label class="typo_label">Código</label>
                                    <vs-input class="d-inline-block" v-model="codigo" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="isActualizarGuardar" />
                                </ValidationProvider>
                            </div>
                            <div class="default-div">
                                <ValidationProvider name="nombre" rules="required|max:30" class="required" v-slot="{ errors }">
                                    <label class="typo_label">Nombre</label>
                                    <vs-input class="d-inline-block" v-model="nombre" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                </ValidationProvider>
                            </div>
                            <div class="default-div">
                                <ValidationProvider name="bodegaDefault" rules="required|numero_entero|numero_min:1" class="required" v-slot="{ errors }">
                                    <label class="typo_label">Bodega Default</label>
                                    <vs-input class="d-inline-block" v-model="bodegaDefault" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                </ValidationProvider>
                            </div>
                            <div class="default-div">
                                <ValidationProvider name="categoria" rules="required" class="required" v-slot="{ errors }">
                                    <label class="typo_label">Categoria</label>
                                    <vs-input class="d-inline-block" v-model="categoria" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap">
                            <div class="default-div">
                                <ValidationProvider name="ubicacionDiag" rules="required|numero_entero|max:4" class="required" v-slot="{ errors }">
                                    <label class="typo_label">Ubicacion Diag</label>
                                    <vs-input class="d-inline-block" v-model="ubicacionDiag" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                </ValidationProvider>
                            </div>
                            <div class="default-div">
                                <ValidationProvider name="ubicacionEmer" rules="required|numero_entero|max:4" class="required" v-slot="{ errors }">
                                    <label class="typo_label">Ubicacion Emer</label>
                                    <vs-input class="d-inline-block" v-model="ubicacionEmer" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                </ValidationProvider>
                            </div>
                            <div class="default-div">
                                <ValidationProvider name="ubicacionHosp" rules="required|numero_entero|max:4" class="required" v-slot="{ errors }">
                                    <label class="typo_label">Ubicacion Hosp</label>
                                    <vs-input class="d-inline-block" v-model="ubicacionHosp" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap">
                            <div class="default-div">
                                <ValidationProvider name="selectNumAutomatica" rules="required" class="required" v-slot="{ errors }">
                                    <!-- <vs-input label-placeholder="Hospital" class="d-inline-block" v-model="hospital" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"/> -->
                                    <label class="typo_label">Num#. Automática</label>
                                    <multiselect v-model="selectNumAutomatica" :options="listNumAutomatica" :show-labels="false" track-by="Codigo" label="Nombre"></multiselect>
                                    <span style="position:relative;top:-2px" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                                </ValidationProvider>
                            </div>
                            <div class="default-div">
                                <ValidationProvider name="selectHospital" rules="required" class="required" v-slot="{ errors }">
                                    <!-- <vs-input label-placeholder="Hospital" class="d-inline-block" v-model="hospital" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"/> -->
                                    <label class="typo_label">Hospital</label>
                                    <multiselect v-model="selectHospital" :options="listHospTiposOrdenes" placeholder="Seleccione Hospital" track-by="Codigo" :show-labels="false" :custom-label="DatosMostrarHospital"></multiselect>
                                    <span style="position:relative;top:-2px" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap">
                            <div>
                                <vs-button @click="handleSubmit(GuardarTiposOrdenes)" :disabled="invalid" color="success" class="btn btn-primary mx-3 my-3" icon-pack="fas" icon="fa-save">
                                    Guardar
                                </vs-button>
                            </div>
                        </div>
                    </div>
                </div>
            </ValidationObserver>
        </vs-popup>
        <vx-card title="Tipos de Ordenes">
            <div>
                <vs-button @click="NuevoTipoOrden" color="success" icon-pack="fas" icon="fa-plus-circle" >Nuevo Tipo Orden</vs-button>
            </div>
            <vs-divider border-style="solid" color="dark"></vs-divider>
            <div class="con-tab-ejemplo">
                <vs-table2 max-items="10" tooltip search filter pagination :data="tablaTiposOrdenes" noSelectText>
                    <template slot="thead">
                        <th>Código</th>
                        <th width="300px">Nombre</th>
                        <th filtro="BodegaDefault">Bodega Default</th>
                        <th filtro="NumAutomatica">Num# Automática</th>
                        <th width="200px">Categorías</th>
                        <th width="150px" filtro="NombreCorto">Hospital</th>
                        <th>Ubicación Diag</th>
                        <th>Ubicación Emer</th>
                        <th>Ubicación Hosp</th>
                        <th>Editar</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data" :data="tr">
                            <vs-td2>{{ tr.Codigo }}</vs-td2>
                            <vs-td2>{{ tr.Nombre }}</vs-td2>
                            <vs-td2>{{ tr.BodegaDefault }}</vs-td2>
                            <vs-td2>{{ tr.NumAutomatica }}</vs-td2>
                            <vs-td2>{{ tr.Categorias }}</vs-td2>
                            <vs-td2>{{ tr.NombreCorto }}</vs-td2>
                            <vs-td2>{{ tr.UbicacionDiag }}</vs-td2>
                            <vs-td2>{{ tr.UbicacionEmer}}</vs-td2>
                            <vs-td2>{{ tr.UbicacionHosp }}</vs-td2>
                            <vs-td2 class="th_editar" noTooltip>
                                <vs-button @click="EditarTipoOrden(tr)" color="primary" size="small" icon-pack="fas" icon="fa-edit" class="btn_editar mr-1"></vs-button>
                                <!-- <vs-button @click="EliminarTipoOrden(tr)" color="danger" size="small" icon-pack="fas" icon="fa-trash-alt" class="btn_editar mr-1"></vs-button> -->
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
        </vx-card>
    </div>
</template>
<script>

    import Multiselect from "vue-multiselect"
    import "vue-multiselect/dist/vue-multiselect.min.css"

    export default {
        data() {
            return {
                tablaTiposOrdenes: [],
                isVisible: false,
                tituloTipoOrdenes: '',
                isActualizarGuardar: false,
                codigo: '',
                nombre: '',
                bodegaDefault: '',
                listNumAutomatica: [
                    {
                        Codigo: 'S',
                        Nombre: 'Si'
                    },
                    {
                        Codigo: 'N',
                        Nombre: 'No'
                    }
                ],
                selectNumAutomatica: '',
                categoria: '',
                listHospTiposOrdenes: [],
                selectHospital: '',
                ubicacionDiag: '',
                ubicacionEmer: '',
                ubicacionHosp: ''
            }
        },
        components: {
            Multiselect
        },
        methods: {
            DatosMostrarHospital({
                Codigo,
                NombreCorto
            }){
                return Codigo ? ` ${Codigo} - ${NombreCorto} ` : ''
                
            },
            ObtenerTiposOrdenes(){
                this.axios.post('/app/v1_JefeCaja/ObtenerTiposOrdenes', {
                    "Opcion": "C",
                    "SubOpcion": "1",
                    "Hospital": "0"
                })
                .then(resp => {
                    this.tablaTiposOrdenes = resp.data.json.map(m => {
                        return {
                            NumAutomatica: m.NumeracionAutomatica == 'S' ? 'Si' : 'No',
                            ...m
                        }
                    })
                })
            },
            ObtenerHospitalesTiposOrdenes(){
                this.axios.post('/app/v1_JefeCaja/ObtenerHospitalesTiposOrdenes', {
                    "Opcion": "C",
                    "SubOpcion": "2",
                    "Hospital": "0"
                })
                .then(resp => {
                    this.listHospTiposOrdenes = resp.data.json.map(m => {
                        return {
                            ...m
                        }
                    })
                })
            },
            NuevoTipoOrden(){
                this.tituloTipoOrdenes = 'Nuevo Tipo de Orden'
                this.isActualizarGuardar = false
                this.isVisible = true
                this.ObtenerHospitalesTiposOrdenes()

            },
            GuardarTiposOrdenes() {
                if(this.isActualizarGuardar){
                    this.axios.post('/app/v1_JefeCaja/ActualizarTipoOrden', {
                        "Opcion": "A",
                        "SubOpcion": "1",
                        "Hospital": "0",
                        "codigo": this.codigo,
                        "nombre": this.nombre,
                        "bodegaDefault": this.bodegaDefault,
                        "numAutomatica": this.selectNumAutomatica.Codigo,
                        "categoria": this.categoria,
                        "hospitalCorrelativo": this.selectHospital.Codigo,
                        "ubicacionDiag": this.ubicacionDiag,
                        "ubicacionEmer": this.ubicacionEmer,
                        "ubicacionHosp": this.ubicacionHosp
                    })
                    .then(resp => {
                        this.codigo = null
                        this.nombre = null
                        this.bodegaDefault = null
                        this.selectNumAutomatica = null
                        this.categoria = null
                        this.selectHospital = null
                        this.ubicacionDiag = null
                        this.ubicacionEmer = null
                        this.ubicacionHosp = null
                        this.isVisible = false
                        this.ObtenerTiposOrdenes()
                        this.$vs.notify({
                            color: 'success',
                            title: 'Tipos de Ordenes',
                            text: resp.data.json[0].descripcion
                        })
                    })
                }else{
                    this.axios.post('/app/v1_JefeCaja/AlmacenarTipoOrden', {
                        "Opcion": "I",
                        "SubOpcion": "1",
                        "Hospital": "0",
                        "codigo": this.codigo,
                        "nombre": this.nombre,
                        "bodegaDefault": this.bodegaDefault,
                        "numAutomatica": this.selectNumAutomatica.Codigo,
                        "categoria": this.categoria,
                        "hospitalCorrelativo": this.selectHospital.Codigo,
                        "ubicacionDiag": this.ubicacionDiag,
                        "ubicacionEmer": this.ubicacionEmer,
                        "ubicacionHosp": this.ubicacionHosp
                    })
                    .then(resp => {
                        if(resp.data.json[0].tipo_error == '1'){
                            this.$vs.dialog({
                                color: 'danger',
                                title: 'Tipos de Ordenes',
                                text: resp.data.json[0].descripcion,
                                acceptText: 'Aceptar'
                            })
                            return
                        }else{
                            this.isVisible = false
                            this.codigo = null
                            this.nombre = null
                            this.bodegaDefault = null
                            this.selectNumAutomatica = null
                            this.categoria = null
                            this.selectHospital = null
                            this.ubicacionDiag = null
                            this.ubicacionEmer = null
                            this.ubicacionHosp = null
                            this.ObtenerTiposOrdenes()
                            this.$vs.notify({
                                color: 'success',
                                title: 'Tipos de Ordenes',
                                text: resp.data.json[0].descripcion
                            })
                        }
                    })
                }
            },
            EditarTipoOrden(item) {
                this.ObtenerHospitalesTiposOrdenes()
                this.tituloTipoOrdenes = 'Modificar Tipo de Orden'
                this.isActualizarGuardar = true
                this.isVisible = true
                this.codigo = item.Codigo
                this.nombre = item.Nombre
                this.bodegaDefault = item.BodegaDefault
                this.categoria = item.Categorias
                this.ubicacionDiag = item.UbicacionDiag
                this.ubicacionEmer = item.UbicacionEmer
                this.ubicacionHosp = item.UbicacionHosp
                this.selectHospital = {
                    Codigo: item.Hospital,
                    NombreCorto: item.NombreCorto
                }
                this.selectNumAutomatica = {
                    Codigo: item.NumeracionAutomatica,
                    Nombre: item.NumeracionAutomatica == 'S' ? 'Si' : 'No'
                }
            },
            EliminarTipoOrden(item) {
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmacion',
                    text: `¿Desea eleminar el tipo de orden: ${ item.Codigo }?`,
                    acceptText: 'Aceptar',
                    accept: () => {
                        this.axios.post('/app/v1_JefeCaja/EliminarTipoOrden', {
                            "Opcion": "E",
                            "SubOpcion": "1",
                            "Hospital": "0",
                            "codigo": item.Codigo
                        })
                        .then( resp => {
                            if(resp.data.json[0].tipo_error == '0'){
                                this.$vs.notify({
                                    color: 'success',
                                    title: 'Tipos de Ordenes',
                                    text: resp.data.json[0].descripcion
                                })
                                this.ObtenerTiposOrdenes()
                            }else{
                                this.$vs.notify({
                                    color: 'danger',
                                    title: 'Tipos de Ordenes',
                                    text: resp.data.json[0].descripcion
                                })
                            }
                        })
                    }
                })
                
            }
        },
        activated() {
            this.ObtenerTiposOrdenes()
            
        },
        watch: {
            isVisible(valor){
                if(!valor){
                    this.codigo = null
                    this.nombre = null
                    this.bodegaDefault = null
                    this.selectNumAutomatica = null
                    this.categoria = null
                    this.selectHospital = null
                    this.ubicacionDiag = null
                    this.ubicacionEmer = null
                    this.ubicacionHosp = null
                }
            }
        }
    }
</script>
<style lang="scss" scoped>
    .default-div {
        .vs-input{
            padding: 5px;
        }
        .multiselect {
            width: 300px;
            padding: 5px;
        }
        
    }
    .th_editar {
        text-align: center;
        .btn_editar{
            display: inline-block;
        }
    }
</style>