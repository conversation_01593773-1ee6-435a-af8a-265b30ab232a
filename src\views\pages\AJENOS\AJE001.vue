<template>
<vx-card title="Ficha del Médico" :class="[(editar)?'edicion':'']">
    <vs-divider position="left">Información Básica</vs-divider>
    <ValidationObserver ref="formValidate" v-slot="{ invalid,handleSubmit }" mode="lazy">
        <form method="post" @submit.prevent="handleSubmit(Guardar().Guardar_Ajenos)">
            <div class="flex mb-5 " style="justify-content:space-between;border:1px solid rgba(0,0,0,0.1);padding:5px">
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <ValidationProvider name="Codigo" rules="required|numero_entero|numero_min:0" v-slot="{ errors }" class="required">
                        <SM-Buscar v-model="info.Codigo" label="Código del Médico" api="app/Ajenos/Busqueda_Ajenos" :api_campos="['Codigo','Nombre','Apellido','Corporativo','Tipo']" :api_titulos="['Codigo','Nombre','Apellido','Corporativo','Tipo']" api_campo_respuesta="Codigo" :callback_buscar="Consulta().Consulta_Ajenos" :callback_nuevo="(permisos.crear)?Nuevo().Ajeno_Nuevo:null" :callback_editar="(e)=>{editar=true}" :callback_cancelar="Otros().Limpiar_Datos" :disabled_texto="!nuevo" :danger="errors.length>0" :dangertext="(errors.length>0)?errors[0]:null" :disabled_editar="editar" :disabled_search_input="nuevo" />
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5  p-1 " v-if="!(editar || nuevo) && (info.Codigo)">
                    <vs-button class="w-full" title="Crear Procedimiento" color="success" icon-pack="fas"
                        icon="fa-print" @click="Otros().ImprimirFicha(info.Codigo)">
                        Ficha Medica
                    </vs-button>
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5  p-1 " v-if="!editar && (info.Codigo || nuevo)">
                    <ValidationProvider name="Autorizado Sala Op." rules="required" v-slot="{ errors }" class="required">
                        <SM-Buscar v-model="info.AutorizadoSOp" label="Autorizado Sala Op." :api="[{Codigo:'S',Descripcion:'Activo'},{Codigo:'N',Descripcion:'Inactivo'}]" :api_cache="true" :api_campos="['#Codigo','Descripcion']" :api_titulos="['#Codigo','Descripción']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Descripcion" :api_preload="true" :disabled_texto="true" :disabled_busqueda="bloquear" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5  p-1 " v-if="!editar && (info.Codigo || nuevo)">
                    <ValidationProvider name="Estado" rules="required" v-slot="{ errors }" class="required">
                        <SM-Buscar v-model="info.Activo" label="Estado" :api="[{Codigo:'S',Descripcion:'Activo'},{Codigo:'N',Descripcion:'Inactivo'}]" :api_cache="true" :api_campos="['#Codigo','Descripcion']" :api_titulos="['#Codigo','Descripción']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Descripcion" :api_preload="true" :disabled_texto="true" :disabled_busqueda="bloquear" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                    </ValidationProvider>
                </div>
            </div>

            <div v-if="editar">
                <div v-if="editar_opcion==0" class="btn-editar" :class="[(!permisos.editar)?'disabled':'']" @click="(permisos.editar)? Editar().Editar_Opcion(1):null">
                    <i class="fas fa-edit"></i><br>
                    Ficha Médica
                </div>
                <div v-if="editar_opcion==0" class="btn-editar" :class="[(!permisos.cambiar_tipo)?'disabled':'']" @click="(permisos.cambiar_tipo)?Editar().Editar_Opcion(2):null">
                    <i class="fas fa-edit"></i><br>
                    Tipo de Médico
                </div>
                <div v-if="editar_opcion==0" class="btn-editar" :class="[(!permisos.cambiar_estado)?'disabled':'']" @click="(permisos.cambiar_estado)?Editar().Editar_Opcion(3):null">
                    <i class="fas fa-edit"></i><br>
                    Estado del Médico
                </div>
                <div v-if="editar_opcion==0" class="btn-editar" :class="[(!permisos.cambiar_firma)?'disabled':'']" @click="(permisos.cambiar_firma)?Editar().Editar_Opcion(4):null">
                    <i class="fas fa-signature"></i><br>
                    Firma
                </div>
                <br>
            </div>

            <div v-if="editar_opcion == 4">
                <div class="w-full  md:w-1/5 lg:w-1/5 xl:w-1/5  pt-6">
                    <SM-Archivo icono="fa-signature" texto="Adjuntar Firma" api="/app/externo/ArchivosCorp_BD" :api_parametros="{codigo:info.Codigo}" :firma="true" :disabled="bloquear || !editar"></SM-Archivo>
                </div>
            </div>
            <div v-if="(info.Codigo || nuevo) && (!editar ||( editar_opcion > 0 &&  editar_opcion < 4))">
                <div class="flex flex-wrap" v-if="editar_opcion==0 || editar_opcion==1">
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1 ">
                        <ValidationProvider name="Colegiado" rules="required|numero_entero|numero_min:1" v-slot="{ errors }" class="required">
                            <vs-input label="No. de Colegiado" type="number" class="w-full" v-model="info.Colegiado" :disabled="bloquear" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" required />
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                        <SM-Buscar v-model="info.Corporativo" label="Corporativo" api="app/Ajenos/Busqueda_Corporativo" :api_campos="['Corporativo',['Nombres','Nombre'],['Apellidos','Apellido']]" :api_titulos="['Corporativo','Nombre','Apellido']" api_campo_respuesta="Corporativo" :callback_buscar="Consulta().Consulta_Corporativo" :callback_cancelar="true" :disabled_texto="true" :disabled_busqueda="bloquear" />
                    </div>
                </div>

                <div class="flex flex-wrap" v-if="editar_opcion==0 || editar_opcion==1">
                    <div class="w-full  p-1">
                        <ValidationProvider name="Nombres" rules="required" v-slot="{ errors }" class="required">
                            <vs-input label="Nombres" class="w-full" v-model="info.Nombre" :disabled="bloquear" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                </div>
                <div class="flex flex-wrap" v-if="editar_opcion==0 || editar_opcion==1">
                    <div class="w-full  p-1">
                        <ValidationProvider name="Apellidos" rules="required" v-slot="{ errors }" class="required">
                            <vs-input label="Apellidos" class="w-full" v-model="info.Apellido" :disabled="bloquear" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                </div>
                <div class="flex flex-wrap" v-if="editar_opcion==0 || editar_opcion==1">
                    <div class="w-full  p-1">
                        <ValidationProvider name="Correo Electrónico" rules="email|required" v-slot="{ errors }" class="required">
                            <vs-input label="Correo Electrónico" class="w-full" v-model="info.CorreoElectronico" :disabled="bloquear" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                </div>
                <div class="flex flex-wrap" v-if="editar_opcion==0 || editar_opcion==1">
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2  p-1">
                        <ValidationProvider name="DPI" rules="required|numero_entero|min:13" v-slot="{ errors }" class="required">
                            <vs-input label="DPI" class="w-full" v-model="info.DPI" :disabled="bloquear" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2  p-1">
                        <ValidationProvider name="NIT" rules="min:5|max:12" v-slot="{ errors }" class="">
                            <vs-input label="NIT" class="w-full" v-model="info.Nit" :disabled="bloquear" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                </div>
                <div class="flex flex-wrap" v-if="editar_opcion==0 || editar_opcion==1">
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2  p-1">
                        <ValidationProvider name="Celular" rules="required|min:8" v-slot="{ errors }" class="required">
                            <vs-input label="Teléfono Celular" class="w-full" v-model="info.Celular" :disabled="bloquear" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2  p-1">
                        <ValidationProvider name="Telefono1" rules="min:8" v-slot="{ errors }">
                            <vs-input label="Teléfono 1" class="w-full" v-model="info.Telefono" :disabled="bloquear" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2  p-1">
                        <ValidationProvider name="TelefonoClinica" rules="min:8" v-slot="{ errors }">
                            <vs-input label="Teléfono Clínica" class="w-full" v-model="info.ClinicaTel" :disabled="bloquear" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                </div>
                <vs-divider position="left" v-if="editar_opcion==0 || editar_opcion==1">Datos Médicos</vs-divider>
                <div class="flex flex-wrap" v-if="editar_opcion==0 || editar_opcion==1">
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1 pt-6">
                        <ValidationProvider name="Especialidad" rules="required" v-slot="{ errors }" class="required">
                            <SM-Buscar v-model="info.EspecialidadPrincipal" label="Especialidad" api="app/Ajenos/Busqueda_Especialidad" :api_cache="true" :api_campos="['Codigo','Descripcion']" :api_titulos="['Codigo','Descripción']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Descripcion" :api_preload="true" :callback_cancelar="()=>info.Especialidad=null" :disabled_texto="true" :disabled_busqueda="bloquear" :danger="errors.length>0" :dangertext="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1 pt-6">
                        <ValidationProvider name="SubEspecialidad" rules="required" v-slot="{ errors }" class="required">
                            <SM-Buscar v-model="info.Especialidad" label="SubEspecialidad" api="app/Ajenos/Busqueda_SubEspecialidad" :api_campos="['Codigo','Nombre']" :api_titulos="['Codigo','Nombre']" :api_filtro="{Codigo_Especialidad:info.EspecialidadPrincipal}" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :callback_cancelar="true" :disabled_texto="true" :disabled_busqueda="bloquear" :danger="errors.length>0" :dangertext="(errors.length>0)?errors[0]:null"/>
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1 pt-6">
                        <ValidationProvider name="Titulo" rules="required" v-slot="{ errors }" class="required">
                            <label style="font-size:12px">Título Médico</label>
                            <v-select :options="listado_select" v-model="info.Titulo" label="Título Médico" :disabled="bloquear" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                </div>

                <vs-divider position="left" v-if="editar_opcion==0 || editar_opcion!=3"></vs-divider>

                <SM-Tabs v-if="editar_opcion==0 || editar_opcion!=3">
                    <div class="tab required" label="Dirección" v-if="editar_opcion==0 || editar_opcion==1">
                        <div class="tab-text card-border p-2">
                            <vs-card>
                                <vs-divider position="left">Dirección para Correspondencia (clínica)</vs-divider>
                                <div class="flex flex-wrap">
                                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                        <ValidationProvider name="Clinica Calle" rules="required" v-slot="{ errors }" class="required">
                                            <vs-input label="Calle y Avenida" class="w-full" v-model="info.ClinicaCalleAvenida" :disabled="bloquear" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                        </ValidationProvider>
                                    </div>
                                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                        <ValidationProvider name="Clinica Zona" rules="required" v-slot="{ errors }" class="required">
                                            <vs-input label="Zona" class="w-full" type="number" v-model="info.ClinicaZona" :disabled="bloquear" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                        </ValidationProvider>
                                    </div>
                                </div>
                                <div class="flex flex-wrap">
                                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                        <ValidationProvider name="Clinica Municipio" rules="required" v-slot="{ errors }" class="required">
                                            <SM-Buscar label="Municipio" v-model="info.ClinicaMunicipio" api="app/Ajenos/Busqueda_Municipios" :api_campos="['Municipio','Departamento']" :api_titulos="['Municipio','Departamento']" api_campo_respuesta="Codigo_Municipio" api_campo_respuesta_mostrar="Municipio" :callback_buscar="(e)=>{info.ClinicaDepto=e.IdDepartamento}" :callback_cancelar="(e)=>{info.ClinicaDepto=null}" :disabled_texto="true" :disabled_busqueda="bloquear" :api_preload="true" :danger="errors.length>0" :dangertext="(errors.length>0)?errors[0]:null" />
                                        </ValidationProvider>
                                    </div>
                                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                        <ValidationProvider name="Clinica Departamento" rules="required" v-slot="{ errors }" class="required">
                                            <SM-Buscar label="Departamento" v-model="info.ClinicaDepto" api="app/Ajenos/Busqueda_Departamentos" api_campo_respuesta="IdDepartamento" api_campo_respuesta_mostrar="Departamento" :disabled_texto="true" :disabled_busqueda="bloquear" :mostrar_busqueda="false" :danger="errors.length>0" :dangertext="(errors.length>0)?errors[0]:null" />
                                        </ValidationProvider>
                                        
                                    </div>
                                </div>
                            </vs-card>

                            <vs-card>
                                <vs-divider position="left">Dirección Domicilio</vs-divider>
                                <div class="flex flex-wrap">
                                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                        <vs-input label="Calle y Avenida" class="w-full" v-model="info.DomicilioCalleAvenida" :disabled="bloquear" />
                                    </div>
                                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                        <vs-input label="Zona" class="w-full" v-model="info.DomicilioZona" :disabled="bloquear" />
                                    </div>
                                </div>
                                <div class="flex flex-wrap">
                                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                        <SM-Buscar label="Municipio" v-model="info.DomicilioMunicipio" api="app/Ajenos/Busqueda_Municipios" :api_campos="['Municipio','Departamento']" :api_titulos="['Municipio','Departamento']" api_campo_respuesta="Codigo_Municipio" api_campo_respuesta_mostrar="Municipio" :callback_buscar="(e)=>{info.DomicilioDepto=e.IdDepartamento}" :callback_cancelar="(e)=>{info.DomicilioDepto=null}" :disabled_texto="true" :disabled_busqueda="bloquear" :api_preload="true" />
                                    </div>
                                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                        <SM-Buscar label="Departamento" v-model="info.DomicilioDepto" api="app/Ajenos/Busqueda_Departamentos" api_campo_respuesta="IdDepartamento" api_campo_respuesta_mostrar="Departamento" :disabled_texto="true" :disabled_busqueda="bloquear" :mostrar_busqueda="false" />
                                    </div>
                                </div>
                            </vs-card>
                        </div>
                    </div>
                    <div class="tab required" label="Honorarios">
                        <div class="tab-text card-border p-2">
                            <div class="flex flex-wrap">
                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1" v-if="editar_opcion==0 || editar_opcion==2">
                                    <ValidationProvider name="Tipo de Médico" rules="required" v-slot="{ errors }" class="required">
                                        <SM-Buscar label="Tipo de Médico" v-model="info.Tipo" :api="[{Codigo:'C',Nombre:'Cortesía'},{Codigo:'M',Nombre:'Casa'},{Codigo:'CC',Nombre:'Casa-Cortesía'},{Codigo:'R',Nombre:'Referencia'}]" :api_campos="['Nombre']" :api_titulos="['Nombre']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :callback_cancelar="(e)=>{info.TipoCortesiaCasa=0}" :disabled_texto="true" :disabled_busqueda="bloquear" :danger="errors.length>0" :dangertext="(errors.length>0)?errors[0]:null" />
                                    </ValidationProvider>
                                </div>

                            </div>
                            <vs-divider position="left" v-if="editar_opcion==0 || editar_opcion==2">SubClasificación</vs-divider>
                            <div class="flex flex-wrap" v-if="editar_opcion==0 || editar_opcion==2">
                                <ul style="display:flex">
                                    <li>
                                        <vs-radio v-model="info.TipoEnMedax" vs-name="TipoEnMedax" vs-value="P" :disabled="bloquear">Primario</vs-radio>
                                    </li>
                                    <li class="ml-5">
                                        <vs-radio v-model="info.TipoEnMedax" vs-name="TipoEnMedax" vs-value="S" :disabled="bloquear">SubEspecialista</vs-radio>
                                    </li>
                                    <li class="ml-5">
                                        <vs-radio v-model="info.TipoEnMedax" vs-name="TipoEnMedax" :vs-value="null" :disabled="bloquear">Sin Clasificación</vs-radio>
                                    </li>
                                </ul>
                            </div>
                            <vs-divider position="left" v-if="editar_opcion==0 || editar_opcion==1">Honorarios</vs-divider>
                            <div class="flex flex-wrap" v-if="editar_opcion==0 || editar_opcion==1">
                                <div class="w-full p-1">
                                    <ValidationProvider name="Honorarios" v-slot="{ errors }">
                                        <vs-input label="Honorarios por paciente de consulta externa con cupones" class="w-full" type="number" v-model="info.HonorariosPacienteCOEX" :disabled="bloquear" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                    </ValidationProvider>
                                </div>
                            </div>
                            <vs-divider position="left" v-if="editar_opcion==0 || editar_opcion==1">Cargos</vs-divider>
                            <div class="flex flex-wrap" v-if="editar_opcion==0 || editar_opcion==1">
                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                    <ValidationProvider name="Manejo de Cuenta" rules="required" v-slot="{ errors }" class="required">
                                        <vs-input type="number" label="Porcentaje Manejo de Cuenta Médico" class="w-full" v-model="info.ManejoCuenta" :disabled="bloquear" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                    </ValidationProvider>
                                </div>
                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                    <vs-input type="number" label="Porcentaje Manejo de Honorarios" class="w-full" v-model="info.ManejoCuentaAjena" :disabled="bloquear" />
                                </div>
                            </div>
                            <vs-divider position="left" v-if="editar_opcion==0 || editar_opcion==1">Comisiones</vs-divider>
                            <div class="flex flex-wrap" v-if="editar_opcion==0 || editar_opcion==1">
                                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                                    <vs-checkbox :value="(info.PagaPreconsa=='S')?true:false" @change="(e)=>info.PagaPreconsa=(e.srcElement.checked)?'S':'N'" :disabled="bloquear">Recibe honorarios por cuenta ajena (recibe cheque)</vs-checkbox>
                                </div>
                                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                                    <vs-checkbox :value="(info.PagaInformesPlanDirecto=='S')?true:false" @change="(e)=>info.PagaInformesPlanDirecto=(e.srcElement.checked)?'S':'N'" :disabled="bloquear">Recibe honorarios por informes (Plan Directo)</vs-checkbox>
                                </div>
                                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                                    <vs-checkbox :value="(info.PagaHonorariosXInformes=='S')?true:false" @change="(e)=>info.PagaHonorariosXInformes=(e.srcElement.checked)?'S':'N'" :disabled="bloquear">Recibe honorarios por informes</vs-checkbox>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="tab" label="Facturación" v-if="editar_opcion==0 || editar_opcion==1">
                        <div class="tab-text card-border p-2">
                            <div class="flex flex-wrap">
                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                    <SM-Buscar label="Tipo de Régimen" v-model="info.Proveedor_Tipo_Contribuyente" :api="[{Codigo:'P',Nombre:'Pequeño Contribuyente'}]" :api_campos="['Codigo','Nombre']" :api_titulos="['Código','Nombre']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :callback_cancelar="true" :disabled_texto="true" :mostrar_busqueda="false" :disabled_busqueda="bloquear" />
                                </div>
                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                    <SM-Buscar label="Tipo de Retención" v-model="info.Proveedor_Tipo_Retencion" :api="[{Codigo:'D',Nombre:'Definitiva'},{Codigo:'0',Nombre:'Cero'}]" :api_campos="['Codigo','Nombre']" :api_titulos="['Código','Nombre']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :callback_cancelar="true" :disabled_texto="true" :mostrar_busqueda="false" :disabled_busqueda="bloquear" />
                                </div>
                            </div>
                            <vs-divider position="left">Datos del Proveedor</vs-divider>
                            <vs-card class="card-border">
                                <div class="flex flex-wrap">
                                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                        <SM-Buscar label="Cód. de Proveedor" v-model="info.Proveedor" api="app/Ajenos/Busqueda_Proveedores" :api_cache="true" :api_campos="['Codigo','Nombre','NombreCheque','Nit']" :api_titulos="['Codigo','Nombre','Nombre del Cheque','Nit']" api_campo_respuesta="Codigo" api_disable_seleccion="Estado!='Activo'" :callback_buscar="(e)=>Consulta().Consulta_Ajenos_Proveedores(e.Codigo)" :callback_cancelar="Otros().Limpiar_Proveedor" :disabled_texto="true" :disabled_busqueda="bloquear" :disabled_nuevo="bloquear" />
                                    </div>
                                    <div class="w-full p-1">
                                        <vs-input label="Nombre:" class="w-full" :value="info.Proveedor_Nombre" :disabled="true" />
                                    </div>
                                    <div class="w-full p-1">
                                        <vs-input label="Dirección Facturación" class="w-full" :value="info.Proveedor_Direccion" :disabled="true" />
                                    </div>
                                    <div class="w-full sm:w-1/3 p-1">
                                        <vs-input label="% Retencion" class="w-full" :value="info.Proveedor_Retencion" :disabled="true" />
                                    </div>
                                    <div class="w-full sm:w-1/3 p-1">
                                        <vs-input label="NIT Proveedor" class="w-full" v-model="info.Proveedor_Nit" :disabled="true" />
                                    </div>
                                </div>

                                <vs-divider position="left">Cuenta Principla</vs-divider>
                                <vs-card class="card-border">
                                    <div class="flex flex-wrap">
                                        <div class="w-full p-1">
                                            <vs-input label="Banco" class="w-full" :value="(info.Proveedor_Cuenta!=null && info.Proveedor_Cuenta!='')?'Banco Industrial':''" :disabled="true" />
                                        </div>
                                        <div class="w-full p-1">
                                            <vs-input label="No. de Cuenta" class="w-full" :value="info.Proveedor_Cuenta" :disabled="true" />
                                        </div>
                                        <div class="w-full p-1">
                                            <vs-input label="Cheque a Nombre" class="w-full" :value="info.Proveedor_Nombre_Cheque" :disabled="true" />
                                        </div>
                                    </div>
                                </vs-card>

                                <vs-divider position="left">Cuenta Secundaria</vs-divider>
                                <vs-card class="card-border">
                                    <div class="flex flex-wrap">
                                        <div class="w-full p-1">
                                            <vs-input label="Banco" class="w-full" :value="info.Proveedor_CuentaOtroBancoNombre" :disabled="true" />
                                        </div>
                                        <div class="w-full p-1">
                                            <vs-input label="No. de Cuenta" class="w-full" :value="info.Proveedor_CuentaOtroBancoCuenta" :disabled="true" />
                                        </div>
                                        <div class="w-full p-1">
                                            <vs-input label="Cheque a Nombre" class="w-full" :value="info.Proveedor_CuentaOtroBancoNombreCheque" :disabled="true" />
                                        </div>
                                    </div>
                                </vs-card>
                            </vs-card>

                            <vs-divider position="left">Afiliado Pronto Pago</vs-divider>
                            <div class="flex flex-wrap">
                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                    <SM-Buscar label="Cód. Proveedor" v-model="info.ProveedorPP" :api="[{Codigo:'SERMES',Nombre:'SERMESA'}]" :api_campos="['Codigo','Nombre']" :api_titulos="['Código','Nombre']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :callback_cancelar="true" :disabled_texto="true" :disabled_busqueda="bloquear" />
                                </div>
                            </div>
                            <vs-divider position="left">Dirección de Entrega</vs-divider>
                            <div class="flex flex-wrap">
                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                    <SM-Buscar label="Entrega En" api="app/Ajenos/Busqueda_Hospitales" v-model="info.HospRecogerCheque" :api_campos="['Codigo','Nombre']" :api_titulos="['Código','Nombre']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :disabled_texto="true" :callback_cancelar="true" :disabled_busqueda="bloquear" />
                                </div>
                            </div>
                        </div>
                    </div>
                </SM-Tabs>

                <vs-divider position="left"></vs-divider>
                <div class="flex flex-wrap">
                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5  p-1 pt-6" v-if="editar">
                        <ValidationProvider v-if="editar_opcion==0 || editar_opcion==3" name="Autorizado Sala Op." rules="required" v-slot="{ errors }" class="required">
                            <SM-Buscar v-model="info.AutorizadoSOp" label="Autorizado Sala Op." :api="[{Codigo:'S',Descripcion:'Activo'},{Codigo:'N',Descripcion:'Inactivo'}]" :api_cache="true" :api_campos="['#Codigo','Descripcion']" :api_titulos="['#Codigo','Descripción']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Descripcion" :api_preload="true" :disabled_texto="true" :disabled_busqueda="bloquear" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5  p-1 pt-6" v-if="editar">
                        <ValidationProvider v-if="editar_opcion==0 || editar_opcion==3" name="Estado" rules="required" v-slot="{ errors }" class="required">
                            <SM-Buscar v-model="info.Activo" label="Estado" :api="[{Codigo:'S',Descripcion:'Activo'},{Codigo:'N',Descripcion:'Inactivo'}]" :api_cache="true" :api_campos="['#Codigo','Descripcion']" :api_titulos="['#Codigo','Descripción']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Descripcion" :api_preload="true" :disabled_texto="true" :disabled_busqueda="bloquear" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-3/3 lg:w-3/3 xl:w-3/3  p-1 pt-6">
                        <ValidationProvider name="Observaciones" rules="max:200" v-slot="{ errors }">
                            <vs-textarea label="Observaciones" v-model="info.Observaciones" counter="200" :counter-danger.sync="observacionesMaximo" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="bloquear" />
                        </ValidationProvider>
                    </div>
                </div>

                <vs-divider></vs-divider>

                <vs-button v-if="nuevo" style="float:right" @click.native="Otros().Mensaje_Error(invalid);handleSubmit(Guardar().Guardar_Ajenos)"> Guardar</vs-button>
                <vs-button v-if="editar && editar_opcion > 0"  style="float:right" @click.native="Otros().Mensaje_Error(invalid);handleSubmit(Editar().Editar)"> Guardar Cambios</vs-button>

                <div style="clear:both"></div>
            </div>
        </form>
    </ValidationObserver>
</vx-card>
</template>

<script>
import bitacora from '@/components/sermesa/funciones/SMBitacora'
import vSelect from 'vue-select'
export default {
    data() {
        return {
            /**
             * Documentación del módulo
             */
            doc_: [
                'Pantalla de administración de médicos',
            ],

            permisos: {
                crear: false,
                editar: false,
                cambiar_estado: false,
                cambiar_tipo: false,
                cambiar_firma: false
            },

            observacionesMaximo: false,

            popupActive: false,

            opciones: {
                Tipo_Regimen: [{
                    label: 'Definitivo',
                    value: 'D'
                }, {
                    label: 'Cero',
                    value: '0'
                }]
            },

            /**
             * Habilita los campos para crear un nuevo ajeno
             */
            nuevo: false,

            /**
             * Habilita los campos para editar
             */
            editar: false,

            /**
             * Opcion de editar
             */
            editar_opcion: 0,

            /**
             * Indica si se bloqueara el contenido para su edición
             */
            bloquear: true,

            /**
             * Modelo de datos
             */
            info: {
                Codigo: null,
                Colegiado: null,
                Corporativo: null,
                Nombre: null,
                Apellido: null,
                CorreoElectronico: null,
                DPI: null,
                Nit: null,
                Telefono: null,
                ClinicaTel: null,
                Celular: null,
                EspecialidadPrincipal: null,
                Especialidad: null,
                Titulo: null,
                ClinicaCalleAvenida: null,
                ClinicaZona: null,
                ClinicaMunicipio: null,
                ClinicaDepto: null,
                DomicilioCalleAvenida: null,
                DomicilioZona: null,
                DomicilioMunicipio: null,
                DomicilioDepto: null,
                Tipo: null,
                TipoEnMedax: null,
                TipoCortesiaCasa: 0,
                HonorariosPacienteCOEX: null,
                ManejoCuenta: null,
                ManejoCuentaAjena: null,
                Proveedor: null,
                PagaPreconsa: 'N',
                PagaInformesPlanDirecto: 'N',
                PagaHonorariosXInformes: 'N',
                Activo: 'S',
                AutorizadoSOp: 'N',
                Observaciones: null,
                ProveedorPP: null,
                EmpresaPP: null,
                Proveedor_Tipo_Contribuyente: null,
                Proveedor_Tipo_Retencion: null,
                Proveedor_Nombre: null,
                Proveedor_Direccion: null,
                Proveedor_Retencion: null,
                Proveedor_Nit: null,
                Proveedor_Cuenta: null,
                Proveedor_Nombre_Cheque: null,
                Proveedor_CuentaOtroBancoNombre: null,
                Proveedor_CuentaOtroBancoCuenta: null,
                Proveedor_CuentaOtroBancoNombreCheque: null,
            },

            /*
             * Listado de títulos de doctores
             */
            listado_select: ['DR', 'DRA', 'EMP'],
        };
    },
    watch: {
        'info.EspecialidadPrincipal'() {
            if (this.nuevo) this.info.Especialidad = null
        },
        'info.Tipo'(value) {
            if (this.bloquear) return false
            if (value == 'CC' && this.info.TipoCortesiaCasa == 0) {
                this.info.TipoCortesiaCasa = 1
                this.Otros().Validar_Facturacion(this.info.TipoCortesiaCasa)
            } else {
                this.info.TipoCortesiaCasa = 0
            }
        }
    },
    props: {},
    components: {
        'v-select': vSelect
    },
    methods: {

        //=======================================================================================================
        // CONSULTA
        //=======================================================================================================
        Consulta: function() {
            return {
                /**
                 * Consulta de Ajenos
                 * Permite realizar una consulta con todos los ajenos
                 */
                Consulta_Ajenos: (datos) => {
                    this.axios.post('/app/ajenos/Consulta_Ajenos', {
                            Codigo: datos.Codigo
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                let datos = resp.data.json[0]
                                this.bloquear = true

                                // Remplazando el tipo  si se ha seleccionado tipocasacortesia
                                if (datos['TipoCortesiaCasa'] == true) {
                                    datos['Tipo'] = 'CC'
                                    datos['TipoCortesiaCasa'] = 1
                                }

                                Object.entries(datos).forEach(([key, value]) => {
                                    let valor = (typeof value == 'string') ? ((value.trim() != "") ? value.trim() : null) : value
                                    this.info[key] = valor
                                });

                                //Cambiando el tipo de respuesta para salaop
                                this.info.AutorizadoSOp = (datos.AutorizadoSOP == 1) ? 'S' : 'N'

                                // Consultando la información del proveedor
                                if (this.info.Proveedor) this.Consulta().Consulta_Ajenos_Proveedores(this.info.Proveedor)

                                //Remplazando codigo del municipio
                                if (this.info.ClinicaMunicipio && this.info.ClinicaMunicipio.trim() != "" && this.info.ClinicaDepto) {
                                    this.info.ClinicaMunicipio = this.info.ClinicaMunicipio.replace(this.info.ClinicaDepto, "")
                                }
                                if (this.info.DomicilioMunicipio && this.info.DomicilioMunicipio.trim() != "" && this.info.DomicilioDepto) {
                                    this.info.DomicilioMunicipio = this.info.DomicilioMunicipio.replace(this.info.DomicilioDepto, "")
                                }

                                setTimeout(() => {
                                    bitacora.registrar(this.info, {
                                        Codigo: datos.Codigo
                                    })
                                }, 2000)

                            }
                        })
                },

                /**
                 * Consulta de Proveedores por ajenos
                 * Permite realizar una consulta con todos los proveedores disponibles
                 */
                Consulta_Ajenos_Proveedores: (data) => {
                    let Codigo = data
                    if (data.Codigo) Codigo = data.Codigo
                    this.axios.post('/app/ajenos/Consulta_Ajenos_Proveedores', {
                            Codigo_Proveedor: Codigo
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                let datos = resp.data.json[0]

                                Object.entries(datos).forEach(([key, value]) => {
                                    this.info[key] = (typeof value == 'string') ? value.trim() : value
                                });
                            }
                        })
                },
                /**
                 * Consula de Corporativo
                 * Permite obtener los nombres y apellidos del corporativo
                 */
                Consulta_Corporativo: (data) => {
                    this.info.Nombre = data.Nombres
                    this.info.Apellido = data.Apellidos
                }
            }
        },

        //=======================================================================================================
        // NUEVO
        //=======================================================================================================
        Nuevo: function() {
            return {
                Ajeno_Nuevo: () => {
                    this.Otros().Limpiar_Datos
                    this.bloquear = false
                    this.nuevo = true
                    // this.consulta_ajenos()
                },
            }
        },

        //=======================================================================================================
        // GUARDAR
        //=======================================================================================================
        Guardar: function() {
            return {
                /**
                 * Guarda la información del ajeno
                 */
                Guardar_Ajenos: () => {
                    //Validando tipo Casa y su subclasificación
                    if (this.info.Tipo == 'M' && !this.info.TipoEnMedax) {
                        this.$vs.dialog({
                            color: 'danger',
                            title: 'Ajenos - Error',
                            text: "Falta definir SubClasificación en Honorarios"
                        })
                        return false
                    }

                    let valor = {
                        ...this.info
                    }

                    Object.entries(valor).forEach(([key]) => {
                        valor[key] = (valor[key] == "") ? null : valor[key]
                    });

                    //Cambiando el tipo de médico
                    if (valor.tipo == 'CC') valor.tipo = 'C'

                    valor.EmpresaPP = (valor.ProveedorPP != null) ? 'MED' : null

                    //Cambiando el tipo de respuesta salaop
                    valor.AutorizadoSOp = (valor.AutorizadoSOp == 'S') ? 1 : null

                    this.axios.post('/app/ajenos/Guardar_Ajenos', valor).then(resp => {
                            if(resp.data.codigo == 0){
                                this.info.Codigo = resp.data.json[0].descripcion

                                this.$vs.notify({
                                            title: 'Ajenos',
                                            text: "Ajeno creado correctamente",
                                            color: 'success',
                                            position: 'top-center'
                                        })
                            }                            
                        })
                },
            }
        },

        //=======================================================================================================
        // EDITAR
        //=======================================================================================================
        Editar: function() {
            return {
                Editar: () => {
                    //Validando tipo Casa y su subclasificación
                    if (this.info.Tipo == 'M' && !this.info.TipoEnMedax) {
                        this.$vs.dialog({
                            color: 'danger',
                            title: 'Ajenos - Error',
                            text: "Falta definir SubClasificación en Honorarios"
                        })
                        return false
                    }

                    let valor = {}
                    let url = null
                    if (this.editar_opcion == 1) {
                        url = '/app/ajenos/Editar_Ficha'
                        //Mapeando todos los valores del obj info
                        valor = {
                            ...this.info
                        }
                        //Limpiando valores 
                        Object.entries(valor).forEach(([key]) => {
                            valor[key] = (valor[key] == "") ? null : valor[key]
                        });
                        valor.EmpresaPP = (valor.ProveedorPP != null) ? 'MED' : null
                    }
                    if (this.editar_opcion == 2) {
                        url = '/app/ajenos/Editar_Tipo'
                        valor = {
                            Codigo: this.info.Codigo,
                            Tipo: (this.info.Tipo == 'CC') ? 'C' : this.info.Tipo,
                            TipoCortesiaCasa: this.info.TipoCortesiaCasa,
                            TipoEnMedax: this.info.TipoEnMedax,
                            Observaciones: this.info.Observaciones
                        }
                    }
                    if (this.editar_opcion == 3) {
                        //Cambiando el tipo de respuesta salaop
                        url = '/app/ajenos/Editar_Estado'
                        valor = {
                            Codigo: this.info.Codigo,
                            AutorizadoSOp: (this.info.AutorizadoSOp == 'S') ? 1 : null,
                            Activo: this.info.Activo,
                            Observaciones: this.info.Observaciones
                        }
                    }
                                        valor.Bitacora2 = bitacora.obtener()

                    this.axios.post(url, valor)
                        .then(() => {
                            this.nuevo = false
                            this.editar = false
                            this.editar_opcion = 0
                            this.bloquear = true
                        })
                },

                Editar_Opcion: (opcion) => {
                    this.bloquear = false;
                    this.editar_opcion = opcion
                }
            }
        },

        //=======================================================================================================
        // ELIMINAR
        //=======================================================================================================
        Eliminar: function() {
            return {}
        },

        //=======================================================================================================
        // OTROS
        //=======================================================================================================
        Otros: function() {
            return {

                Validar_Facturacion: (valor) => {
                    if (valor == 1)
                        this.$vs.dialog({
                            color: 'warning',
                            title: 'Ajenos - Facturas',
                            text: "Se sugiere revisar los porcentajes de cargo, comisiones y datos de proveedor.\nSi el médico atenderá COEX, necesita tener Subclasificación"
                        })

                },
                Limpiar_Proveedor: () => {
                    this.info.Proveedor_Tipo_Contribuyente = null
                    this.info.Proveedor_Tipo_Retencion = null
                    this.info.Proveedor_Nombre = null
                    this.info.Proveedor_Direccion = null
                    this.info.Proveedor_Retencion = null
                    this.info.Proveedor_Nit = null
                    this.info.Proveedor_Cuenta = null
                    this.info.Proveedor_Nombre_Cheque = null
                    this.info.Proveedor_CuentaOtroBancoNombre = null
                    this.info.Proveedor_CuentaOtroBancoCuenta = null
                    this.info.Proveedor_CuentaOtroBancoNombreCheque = null
                },

                Limpiar_Datos: () => {
                    this.nuevo = false
                    this.editar = false
                    this.editar_opcion = 0

                    Object.entries(this.info).forEach(([key]) => {
                        this.info[key] = null
                    });
                    this.info.TipoCortesiaCasa = 0
                    this.info.PagaPreconsa = 'N'
                    this.info.PagaInformesPlanDirecto = 'N'
                    this.info.PagaHonorariosXInformes = 'N'
                    this.info.Activo = 'S'
                    this.info.AutorizadoSOp = 'N'

                    setTimeout(() => {
                        this.$refs.formValidate.reset()
                    }, 100)

                },
                Mensaje_Error: (valido) => {
                    if (valido)
                        this.$vs.dialog({
                            color: 'danger',
                            title: 'Ajenos - Validación',
                            text: "Existen campos obligatorios "
                        })
                },
                ImprimirFicha: (codigo) => {
                   
                   this.axios.post("/app/reporte/ReporteGenerador", {
                    Nombre: "Estado de Cuenta",
                        Opciones: {
                            tiporeporte: "application/pdf",
                            nombrereporte: "Ficha_Medica",
                            Ajeno: codigo
                        }
                    }, {
                        responseType: 'arraybuffer'
                    })
                    .then(resp => {
                        
                        //if (Formato == 'PDF') {
                            this.$store.dispatch('reporte', {
                                pdf: 'data:application/pdf;base64,' + Buffer.from(resp.data, 'binary').toString('base64'),
                                abrirTab: false, // Abrir nueva pestaña
                                imprimir: false,
                                titulo: 'Estado de Cuenta'
                            })
                        //}
                    })
                }
            }
        }

    },
    mounted() {
        this.permisos.crear = this.$validar_privilegio('NUEVO').status
        this.permisos.editar = this.$validar_privilegio('EDITAR').status
        this.permisos.cambiar_estado = this.$validar_privilegio('CAMBIAR_ESTADO').status
        this.permisos.cambiar_tipo = this.$validar_privilegio('CAMBIAR_TIPO').status
        this.permisos.cambiar_firma = this.$validar_privilegio('CAMBIAR_FIRMA').status

        // this.nuevo = (this.$route.query.nuevo == undefined) ? true : this.$route.query.nuevo
        // this.permisos.crear = this.$validar_privilegio('CREAR').status && this.nuevo
    },
};
</script>

<style scoped>
.btn-editar i {
    font-size: 20px;
}

.btn-editar {
    cursor: pointer;
    text-align: center;
    padding: 20px;
    display: inline-block;
    box-shadow: 0 2px 5px rgba(0, 0, 0, .3);
    margin-right: 10px;
    width: 300px;
    transition: all ease 0.2s;
}

.btn-editar.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-editar:hover {
    box-shadow: 0 2px 5px rgba(0, 0, 0, .5);
}
</style>
