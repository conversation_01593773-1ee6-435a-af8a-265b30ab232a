<template>
<vx-card class="Costos">
    <div class="content content-pagex">
        <vs-divider class="label-size"> AUXILIAR COSTOS </vs-divider>
        <div class="flex-container">
            <div class="w-full flex flex-wrap">
                <vs-col class="md:w-full lg:w-full xl:w-4/12 p-2">
                    <vx-card class="w-full" style="padding:10px 20px">
                        <div class="w-full" align="right">
                            <div class="w-full" style="padding:5px 5px">
                                <!-- <vs-button color="success" class="label-size" type="filled" @click="GenerarPartida()"> Partida </vs-button> -->
                                <DxButton :width="120" text="Partida" type="success" @click="GenerarPartida()" />
                                &nbsp;
                                <DxButton :width="50" icon="pulldown" type="normal" @click="ConsultaAuxiliarG()" /> 
                            </div>
                        </div>
                        <div class="w-full">
                            <label class="label-size">Movimientos</label>
                               <DxSelectBox
                                    :items="ListaMovimientos"
                                    :show-clear-button="true"
                                    :searchable="true" 
                                    v-model="MovimientoSeleccionado"
                                    display-expr="Descripcion"
                                    value-expr="Tipo"
                                    :search-enabled="true"
                                    :accept-custom-value="true"
                                    @value-changed="guardarMovimiento"
                                />  
                        </div>
                        <div class="w-full" style="margin-top: 10px;">
                            <label class="label-size">Periodo</label>
                                <DxSelectBox
                                    :data-source="ListaPeriodos"
                                    :display-expr="formatPeriodo"
                                    value-expr="CodigoPeriodo"
                                    v-model="cbPeriodos"
                                    :show-clear-button="true"
                                    :search-enabled="true"
                                    :accept-custom-value="true"
                                    @value-changed="guardarPeriodoCompleto"
                                />
                        </div>

                        <div class="w-full" style="margin-top: 10px;">
                            <label class="label-size">Sucursal</label>
                              <DxSelectBox
                                    :items="ListaSucursal"
                                    :show-clear-button="true"
                                    :searchable="true" 
                                    v-model="SucursalSeleccionada"
                                    display-expr="Nombre"
                                    value-expr="Codigo"
                                    :search-enabled="true"
                                    :accept-custom-value="true"
                                    @value-changed="guardarSucursal"

                                />  
                        </div>
                       <div style="margin-top: 10px;">
                        <vs-alert v-if="DescMayorizada === 'M'" active="true" color="warning" style="text-align: center; height: 40px;">
                            <label>Hay una partida mayorizada en este período.</label>                    
                        </vs-alert>          
                        </div>
                    </vx-card>
                </vs-col>
               <!--  Reportes -->
                <vs-col class="w-full md:w-full lg:w-full xl:w-8/12 p-1">
                    <vx-card class="w-full">
                        <vs-row class="w-full">
                            <vs-row class="md:w-6/12 lg:w-6/12 xl:w-6/12">
                                <div class="w-full">

                                    <div class="w-full" v-show="FechaV">
                                        <label class="label-size"> Fecha Inicial </label>
                                        <DxDateBox v-model="FechaI" placeholder="Fecha"  display-format="dd/MM/yyyy"/>
                                    </div>
                                    <div class="w-full" style="margin-top: 10px;" v-show="FechaV">
                                        <label class="label-size"> Fecha Final </label>
                                        <DxDateBox v-model="FechaF" placeholder="Fecha" display-format="dd/MM/yyyy"/>
                                    </div>

                                    <div class="w-full"  style="margin-top: 10px;">
                                        <ValidationProvider name="Cuentas">
                                            <label class="label-size">Cuenta</label>
                                            <multiselect
                                                    v-model="cbCuentas"
                                                    :options="cuentasFormateadas"
                                                    :searchable="true"
                                                    :close-on-select="true"
                                                    :show-labels="false"
                                                    placeholder="Seleccionar Cuenta"
                                                    label="label"
                                                    track-by="CodigoCuenta"
                                                    
                                                >
                                                <span slot="noOptions">Lista no disponible.</span>
                                            </multiselect>
                                        </ValidationProvider>
                                    </div>
                                    <div class="w-full" style="padding:2px; margin-top: 10px" v-show="AdmisionV">
                                        <vs-checkbox icon-pack="feather" icon="icon-check" color="success" v-model="Admision">Filtrar Por Admisión.</vs-checkbox>
                                    </div>
                                    <div style="margin-top: 5px;">
                                        <vs-alert active="true"  color="#842993" style="text-align: center; height: 35px;">
                                            <label>Tipo Operación: {{ mostrarDescripcionSeleccionada }}</label>                    
                                        </vs-alert>          
                                    </div>
                                    <div style="margin-top: 5px;">
                                        <vs-alert  active="true" class="color" style="text-align: center; height: 35px;">
                                            <label style="color: #2471a3;">Las Lineas con este color debe factura del empleado.</label>                    
                                        </vs-alert>          
                                    </div>
                                </div>
                            </vs-row>
                            <vs-row class="w-full md:w-6/12 lg:w-6/12 xl:w-6/12">
                                <div>
                                    <div class="w-full bg-white rounded-xl shadow-sm p-4">
                                        <div class="space-y-3">
                                            <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors">
                                                <label class="flex items-center cursor-pointer w-full">
                                                    <span class="text-gray-700 font-medium mr-3 w-6">1.</span>
                                                    <vs-radio v-model="IdReporte" vs-name="Reporte" vs-value="1" class="radio-custom">
                                                        <span class="ml-2 text-gray-800">Detallado por Referencia</span>
                                                    </vs-radio>
                                                </label>
                                            </div>

                                            <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors">
                                                <label class="flex items-center cursor-pointer w-full">
                                                    <span class="text-gray-700 font-medium mr-3 w-6">2.</span>
                                                    <vs-radio v-model="IdReporte" vs-name="Reporte" vs-value="2" class="radio-custom">
                                                        <span class="ml-2 text-gray-800">Cuentas Integradas</span>
                                                    </vs-radio>
                                                </label>
                                            </div>

                                            <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors">
                                                <label class="flex items-center cursor-pointer w-full">
                                                    <span class="text-gray-700 font-medium mr-3 w-6">3.</span>
                                                    <vs-radio v-model="IdReporte" vs-name="Reporte" vs-value="3" class="radio-custom">
                                                        <span class="ml-2 text-gray-800">Detalle por Cuenta</span>
                                                    </vs-radio>
                                                </label>
                                            </div>

                                            <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors">
                                                <label class="flex items-center cursor-pointer w-full">
                                                    <span class="text-gray-700 font-medium mr-3 w-6">4.</span>
                                                    <vs-radio v-model="IdReporte" vs-name="Reporte" vs-value="4" class="radio-custom">
                                                        <span class="ml-2 text-gray-800">Siniestralidad SaSi (Liquidaciones)</span>
                                                    </vs-radio>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="flex flex-wrap">
                                        <div class="md:w-1/7 lg:w-1/7 xl:w-1/7">
                                            <div class="w-full" style="padding:10px 50px">
                                                     <DxButton v-show="ButtonPDF" :width="180" text="Generar Reporte" icon="pdffile" type="danger" @click="GenerarReporte('application/pdf','PDF')" /> &nbsp;
                                                    <DxButton v-show="ButtonExcel" :width="180"  text="Generar Reporte" icon="tableproperties" type="success" @click="GenerarReporte('application/vnd.ms-excel','EXCEL')" />
                                             </div>
                                        </div>
                                    </div>
                                </div>
                            </vs-row>

                        </vs-row>
                    </vx-card>
                </vs-col>

            </div>
        </div>
        <br>
        <div class="w-full" align="center" v-if="DocSinCuentasV || DescPorDocumentoV">
            <div class="w-full" style="padding:10px 10px">
                <DxButton :width="200" text="Ocultar Información" type="normal" styling-mode="outlined" @click="OcultarInformacion()" />
            </div>
        </div>
        <!--GRID DE AUXILIAR SIN CUENTA-->
        <div v-if="DocSinCuentasV">
            <DxDataGrid
                :data-source="AuxiliarSinCuenta"
                :column-auto-width="true"
                :allow-column-reordering="true" 
                :show-borders="true"
            >
                <DxDataGridColumn data-field="Tipo" caption="Tipo" data-type="string"/>
                <DxDataGridColumn data-field="Referencia" caption="Referencia" data-type="string"/>
                <DxDataGridColumn data-field="Documento" caption="Documento" data-type="string"/>
                <DxDataGridColumn data-field="Fecha" caption="Fecha" data-type="date" :format="'dd/MM/yyyy'" />
                <DxDataGridColumn data-field="Debe" caption="Debe" data-type="number"  :format="{ formatter: formatMoneda }"/>
                <DxDataGridColumn data-field="Haber" caption="Haber" data-type="number"  :format="{ formatter: formatMoneda }"/>
                <DxDataGridPaging :page-size="6" />   
            </DxDataGrid>
        </div>

        <!--GRID DE DESCUADRE POR DOCUMENTO-->
        <div v-if="DescPorDocumentoV">
            <DxDataGrid
               :data-source="DescPorDocumento"  
               :column-auto-width="true"
               :allow-column-reordering="true" 
               :show-borders="true"
            >
                <DxDataGridColumn data-field="Tipo" caption="Tipo" data-type="string"  />
                <DxDataGridColumn data-field="Referencia" caption="Referencia" data-type="string"  />
                <DxDataGridColumn data-field="Fecha" caption="Fecha" data-type="date" :format="'dd/MM/yyyy'" />
                <DxDataGridColumn data-field="SumaDebe" caption="Debe" data-type="number"  :format="{ formatter: formatMoneda }" />
                <DxDataGridColumn data-field="SumaHaber" caption="Haber" data-type="number"  :format="{ formatter: formatMoneda }" />
                <DxDataGridPaging :page-size="6" />              
            </DxDataGrid>
        </div>
        <DxDataGrid
            :data-source="AuxiliarCostos"
            :column-auto-width="true"
            key-expr="Documento"
            :allow-column-reordering="true"
            :show-borders="true"
            @selection-changed="onSelectionChanged"
            ref="myDataGridRef"
            @row-dbl-click="onRowDblClick"
            @row-prepared="onRowPrepared"
        >               
            <DxDataGridSelection mode="single" />
            <DxDataGridColumn data-field="TipoOperacion" caption="TipoOperacion" data-type="string"/>
            <DxDataGridColumn data-field="Fecha" caption="Fecha" />
            <DxDataGridColumn data-field="SerieDocumento" caption="Serie Documento" data-type="string"/>
            <DxDataGridColumn data-field="Documento" caption="Documento" data-type="number"/>
            <DxDataGridColumn data-field="SerieDocumentoOrigen" caption="Serie Documento Origen" data-type="string"/>
            <DxDataGridColumn data-field="DocumentoOrigen" caption="Documento Origen" data-type="number"/>
            <DxDataGridColumn data-field="SerieAdmision" caption="Serie Admision" data-type="string"/>
            <DxDataGridColumn data-field="Admision" caption="Admision" data-type="number"/>
            <DxDataGridColumn data-field="Cuenta" caption="Cuenta" data-type="number"/>
            <DxDataGridColumn data-field="Debe" caption="Debe" data-type="number" :format="{ formatter: formatMoneda }"/>
            <DxDataGridColumn data-field="Haber" caption="Haber" data-type="number" :format="{ formatter: formatMoneda }"/>
            <DxDataGridColumn data-field="Corte" caption="Corte" data-type="number" />
            <DxDataGridColumn data-field="EmpresaReal" caption="EmpresaReal" data-type="string"/>
            <DxDataGridColumn data-field="Empresa" caption="Empresa" data-type="string"/>
            <DxDataGridColumn data-field="Periodo" caption="Periodo" data-type="number"/>
            <DxDataGridColumn data-field="Partida" caption="Partida" data-type="number"/>
            <DxDataGridColumn data-field="Departamento" caption="Departamento" data-type="string"/>  
            <DxDataGridPaging  :page-size="10" />      
            <DxDataGridDxSearchPanel :visible="true" :highlight-case-sensitive="true" />       
            <DxDataGridHeaderFilter :visible="true" :allow-search="true" />
        </DxDataGrid>
    </div>

    
    <DxPopup :visible.sync="PartidaDiario" title="Generar Partida de Diario" height="460" width="500">
        <form>
            <vs-divider class="label-size"> OPCIONES </vs-divider>
            <vs-radio v-model="OpcionSel" vs-name="OpcionSel" vs-value="1"> Nueva Partida </vs-radio>
              <DxTextBox v-model="NuevaPartida" />
            <label> Fecha </label>
            <div style="width: 100%;">
                <flat-pickr
                    v-model="FechaPartida"
                    :config="configFromdateTimePicker"
                    class="devextreme-like-datepicker"
                />
            </div>
            <label>Referencia </label>
             <DxNumberBox :value="Referencia" :show-spin-buttons="true" v-model="Referencia" />
            
            <label for="">Descripción</label>
            <vs-textarea rows="3" class="w-full" v-model="Descripcion" />
            <div style="text-align: center;">
                <DxButton :width="120" icon="save" text="Ok" type="success" @click=" ResumenQuery()" />
            </div>
        </form>
    </DxPopup>



    <DxPopup           :wrapper-attr="popupAttributes" :visible.sync="ModificarAuxiliar" title="Modificar Auxiliar" height="auto" @hidden="onPopupHiding">
        <form>
            <div class="w-full" style="text-align: center;">
                  <vs-alert v-if="DescMayorizada === 'M'" active="true" color="danger" style="text-align: center; height: 40px;" >
                            <label>Periodo Mayorizado solo Visualización.</label>                    
                  </vs-alert>
                   <vs-alert v-else active="true" color="success" style="text-align: center; height: 40px;">
                            <label>Modificación Permitida.</label>                    
                  </vs-alert>
            </div>
            <div class="w-full" style="text-align: center; margin-top: 10px;" v-if="ListaDetalle && ListaDetalle.length > 0 && DescMayorizada !== 'M'">
            <DxButton 
                :width="120" 
                text="Guardar" 
                type="success" 
                icon="save" 
                @click="validarSumasYGuardar"
            />
            </div>
            <div style="margin-top: 5px;">
            <DxDataGrid
                :data-source="ListaDetalle"
                :column-auto-width="true"
                :allow-column-reordering="true"
                :show-borders="true"
                @editor-preparing="onEditorPreparing"
                ref="gridDetalle"
            >
                <DxDataGridPager :show-page-size-selector="true"  />
                <DxDataGridDxColumnFixing :enabled="true" />
                <DxDataGridColumn data-field="Cuenta" caption="Cuenta" data-type="number"  :cell-template="cuentaCellTemplate" :fixed="true" :allow-editing="DescMayorizada !== 'M'"/>    
                <DxDataGridColumn data-field="Debe" caption="Debe" data-type="number" :format="{ formatter: formatMoneda }" :cell-template="debeCellTemplate" :fixed="true" :allow-editing="DescMayorizada !== 'M'"/>
                <DxDataGridColumn data-field="Haber" caption="Haber" data-type="number" :format="{ formatter: formatMoneda }" :cell-template="haberCellTemplate" :fixed="true" :allow-editing="DescMayorizada !== 'M'"/>
                <DxDataGridColumn data-field="Empresa" caption="Empresa" data-type="string" :allow-editing="false"  />
                <DxDataGridColumn data-field="EmpresaReal" caption="Empresa Real" data-type="string" :allow-editing="false" />
                <DxDataGridColumn data-field="TipoOperacion" caption="Tipo Operación" data-type="string" :allow-editing="false" />
                <DxDataGridColumn data-field="SerieDocumento" caption="Serie Documento" data-type="string" :allow-editing="false" />
                <DxDataGridColumn data-field="Documento" caption="Documento" data-type="string" :allow-editing="false" />
                <DxDataGridColumn data-field="Linea" caption="Linea" data-type="number" :allow-editing="false" />
                <DxDataGridColumn data-field="Id" caption="Correlatico" data-type="number" :allow-editing="false" />
                <DxDataGridColumn data-field="Periodo" caption="Periodo" data-type="number" :allow-editing="false" />
                <DxDataGridColumn data-field="SerieAdmision" caption="Serie Admisión" data-type="string" :allow-editing="false" />
                <DxDataGridColumn data-field="Admision" caption="Admisión" data-type="number" :allow-editing="false" />
                <DxDataGridColumn data-field="Paciente" caption="Paciente" data-type="number" :allow-editing="false" />
                <DxDataGridColumn data-field="Producto" caption="Producto" data-type="number" :allow-editing="false" />
                <DxDataGridColumn data-field="Cantidad" caption="Cantidad" data-type="number" :allow-editing="false" />
                <DxDataGridColumn data-field="CostoUnitario" caption="Costo Unitariotidad" data-type="number" :format="{ formatter: formatMoneda }" :allow-editing="false" />
                <DxDataGridColumn data-field="CostoTotal" caption="Costo Total" data-type="number" :format="{ formatter: formatMoneda }" :allow-editing="false" />
                <DxDataGridColumn data-field="CostoUnitarioHospital" caption="Costo Unitario Hospital" data-type="number" :format="{ formatter: formatMoneda }" :allow-editing="false" />
                <DxDataGridColumn data-field="CostoTotalHospital" caption="Costo Total Hospital" data-type="number" :format="{ formatter: formatMoneda }" :allow-editing="false" />
                <DxDataGridColumn data-field="Categoria" caption="Categoria" data-type="number" :allow-editing="false" />
                <DxDataGridColumn data-field="SerieDocumentoOrigen" caption="Serie Documento Origen" data-type="string" :allow-editing="false" />
                <DxDataGridColumn data-field="DocumentoOrigen" caption="Documento Origen" data-type="number" :allow-editing="false" />
                <DxDataGridColumn data-field="LineaDocumento" caption="Linea Documento" data-type="number" :allow-editing="false" />
                <DxDataGridColumn data-field="Corte" caption="Corte" data-type="number" :allow-editing="false" />    
                <DxDataGridColumn data-field="Usuario" caption="Usuario" data-type="string" :allow-editing="false" />    
                <DxDataGridColumn data-field="UsuarioMod" caption="Usuario Modificaq" data-type="string" :allow-editing="false" />    
                <DxDataGridColumn data-field="Fecha" caption="Fecha" data-type="date" :allow-editing="false"/>    
                <DxDataGridColumn data-field="FechaTranEnInvCDet" caption="Fecha Tran En InvCDet" data-type="date" :allow-editing="false" />    
                <DxDataGridColumn data-field="FechaMod" caption="Fecha Modifica" data-type="date" :allow-editing="false" />    
                <DxDataGridColumn data-field="PeriodoPartida" caption="Periodo Partida" data-type="number" :allow-editing="false" />    
                <DxDataGridColumn data-field="Partida" caption="Partida" data-type="number" :allow-editing="false" />    
                <DxDataGridEditing :allow-updating="true" :use-icons="true" mode="cell"/>
                <DxDataGridHeaderFilter :visible="true" :allow-search="true" />
                <DxDataGridDxSearchPanel :visible="true" :highlight-case-sensitive="true" />       
                <DxDataGridPaging  :page-size="10" />
                  <!-- Sumarios -->
                <DxDataGridSummary v-if="mostrarResumen">
                    <DxDataGridTotalItem column="Debe" summary-type="sum" :format="{ formatter: formatMoneda }" />
                    <DxDataGridTotalItem column="Haber" summary-type="sum" :format="{ formatter: formatMoneda }"  />
                </DxDataGridSummary>
            </DxDataGrid>
            </div>

        </form>
    </DxPopup>
</vx-card>
</template>

<script>
import Multiselect from 'vue-multiselect'
import "vue-multiselect/dist/vue-multiselect.min.css"
import flatPickr from 'vue-flatpickr-component';
import 'flatpickr/dist/flatpickr.css';
import {
    Spanish as SpanishLocale
} from 'flatpickr/dist/l10n/es.js';


import moment from "moment";
export default {
    components: {
        Multiselect,
        flatPickr

    },
    data() {
        return {
            cbPeriodos: '',
            ListaPeriodos: [],
            CodigoPeriodo: '',
            DescPeriodo: '',
            cbMovimientos: null,
            ListaMovimientos: [],
            TipoMovimiento: '',
            DescMovimiento: '',
            FechaI: '',
            FechaF: '',
            FechaPartida: '',
            FechaV: true,
            OrdenV: true,
            Referencia: 0,
            Cuenta: '',
            CuentaV: true,
            Descripcion:null,
            OpcionSel: 1,
            NuevaPartida: null,
            Bloqueo1: false,
            Bloqueo2: true,
            Periodo: '',
            Tipo: 'F',
            TipoDoc: 1,
            cbCuentas: {
                CodigoCuenta: null,
                Nombre: null
            },
            ModificarAuxiliar:false,
            ListaCuentas: [],
            CodCuenta: null,
            NombreCuenta: '',
            FacturasContado: false,
            FacturasContadoV: true,
            FacturasCredito: false,
            FacturasCreditoV: true,
            IdReporte: 0,
            AuxiliarCostos: [],
            AuxiliarCostosDataSurce: null, // Inicializar como null o undefined
            ListadoReportes: [],
            CorrelativoPartida: '',
            FechaIPeriodoA: '',
            PartidaDiario: false,
            DocSinCuentas: [],
            DocSinCuentasV: false,
            DescPorDocumento: [],
            DescPorDocumentoV: false,
            Orden: '',
            DescMayorizada: null,
            DescMayorizadaV: false,
            InsertaV: false,
            ModificarV: false,
            ListaSucursal: [],
            cbSucursal: '',
            ListaFace: [],
            ReferenciaSel: '',
            mostrarResumen:false,
            SaSi: false,
            periodoSeleccionado:null,
            FilaSeleccionada: null,
            ListaDetalle: [], // tu array original
            configFromdateTimePicker: {
                minDate: '',
                maxDate: '',
                locale: SpanishLocale,
                dateFormat: "d/m/Y",
            },
             isClearingSelection: false,

             MovimientoSeleccionado:null,
             SucursalSeleccionada: null,
             ListaDetalleSrt:[],
             Admision: false,
             AdmisionV: false,
             dataSource: null,

            popupAttributes: {
                class: 'Costos'
            },
            ButtonPDF: false,
            ButtonExcel: false 

        }
    },
    mounted() {
        if (this.periodoSeleccionado && this.periodoSeleccionado.CodigoPeriodo) {
            this.ConsultaAuxiliarG();
            this.Mayorizar();
            this.limpiarFiltrosCostos();
        }
        this.IdReporte = '1'
        this.ConsultaMovimientos(),
        this.ConsultaPeriodo(),
        this.ConsultasCuentas(),
        this.ConsultaSucursal(),
        this.ListadoReportes = this.$recupera
        this.DescPorDocumentoV = false
        this.DocSinCuentasV = false
    },
    watch: {
        'IdReporte'() {
         
            if (this.IdReporte == '1') {
                this.AdmisionV = true   
                this.ButtonExcel = true
                this.ButtonPDF = false
                this.limpiarCuenta()
            }
             if (this.IdReporte == '2') {
                this.AdmisionV = false   
                this.ButtonExcel = false
                this.ButtonPDF = true
                this.limpiarCuenta()
            }
             if (this.IdReporte == '3') {
                this.AdmisionV = false  
                this.ButtonExcel = true
                this.ButtonPDF = true
                this.limpiarCuenta()
            }
            if (this.IdReporte == '4') {
                this.SaSi = true
                this.ButtonExcel = true
                this.ButtonPDF = true
                this.limpiarCuenta()
            } else {
                this.SaSi = false
            }
        }
    },
    computed: {
        mostrarDescripcionSeleccionada() {
            if (!this.MovimientoSeleccionado && this.MovimientoSeleccionado !== '') {
                return 'Seleccione un movimiento';
            }
            
            // Caso especial para "Todos los Tipos"
            if (this.MovimientoSeleccionado === '') {
                return 'Todos los Tipos';
            }
            
            // Buscar el movimiento correspondiente
            const movimiento = this.ListaMovimientos.find(item => item.Tipo === this.MovimientoSeleccionado);
            return movimiento ? movimiento.Descripcion : 'Movimiento no encontrado';
        },
        cuentasFormateadas() {
            return this.ListaCuentas.map(cuenta => ({
            ...cuenta,
            label: `${cuenta.CodigoCuenta} - ${cuenta.Nombre}`
            }));
        }
    },

    methods: {
        getDateValue(value) {
            if (value === undefined) {
                return '';
            } else if (value === null) {
                return '';
            } else if (value == '') {
                return '';
            }
            return moment(value).format('YYYY-MM-DD');
        },
        onRowPrepared(e) {
            if (e.rowType === 'data' && e.data.Departamento === 'RF') {
                e.rowElement.style.backgroundColor = 'aqua'; // Puedes usar '#00FFFF' si prefieres
            }
        },

        formatPeriodo(item) {
            if (!item) return '';
            return `${item.CodigoPeriodo} - ${item.DescPeriodo}`;
        },

        formatMoneda(value) {
            // Si el valor es 0 (o equivalente), retorna vacío
            if (value === 0 || value === 0.0000 || value === "0.0000") {
                return "";
            }
            
            // Si es null o undefined, retorna "Q 0.00" (como en tu lógica original)
            if (value === null || value === undefined) {
                return "Q 0.00";
            }

            // Formato normal para números distintos de cero
            const formatted = `Q ${Math.abs(value).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",")}`;
            return value < 0 ? `<span style="color: red;">-${formatted}</span>` : formatted;
        },

        onRowDblClick(){
            this.$vs.dialog({
                    type: 'confirm',
                    color: '#273746',
                    title: 'Contabilidad',
                    text: 'Advertencia: Pueden Ocurrir descuadre si no digita partidas cuadradas ',
                    acceptText: 'Ok',
                    cancelText: 'Cancelar',
                    buttonCancel: 'border',
                    clientWidth: 100,
                    accept: () => {
                        this.ConsultaDetalleDocumento(); // Llama a la función aquí después de aceptar
                    },
                    cancel: () => {
                        this.clearGridSelection()
                    }
                });
        },

        onSelectionChanged({ selectedRowsData }) {
            if (this.isClearingSelection) {
                this.isClearingSelection = false;
                return;
            }

            const fila = selectedRowsData[0];
            this.FilaSeleccionada = fila;
            if (this.Admision) {
                if (!this.filtrosAdmision) {
                    this.filtrosAdmision = [];
                }

                const yaExiste = this.filtrosAdmision.some(f => 
                    f.SerieAdmision === fila.SerieAdmision && 
                    f.Admision === fila.Admision
                );


                if (!yaExiste) {
                    this.filtrosAdmision.push({
                        SerieAdmision: fila.SerieAdmision,
                        Admision: fila.Admision
                    });
                }
            }
        },


        onPopupHiding() {
            // Limpia la selección al cerrar
            this.clearGridSelection();
        },


        clearGridSelection() {
            this.isClearingSelection = true;
            this.$refs.myDataGridRef.instance.clearSelection();
            this.FilaSeleccionada = null;
        },
        
        SeleccionReferencia() {
            var ListaSeleccionada = [];
            var ReferenciaS = ''
            if (this.AuxiliarCostos.length > 0) {
                ListaSeleccionada = this.AuxiliarCostos.filter(ab => ab.Seleccion == true);
                for (let i = 0; i < ListaSeleccionada.length; i++) {
                    ReferenciaS = ListaSeleccionada[i].Referencia
                }
                this.ReferenciaSel = ReferenciaS;
            }

        },

        OcultarInformacion() {
            this.DocSinCuentasV = false
            this.DescPorDocumentoV = false
        },

        //PERIODOS
        guardarMovimiento(e) {
            this.MovimientoSeleccionado = e.value;
            if (this.periodoSeleccionado && this.periodoSeleccionado.CodigoPeriodo) {
                this.ConsultaAuxiliarG();  // Si es necesario
                this.Mayorizar();          // Si es necesario
                this.limpiarFiltrosCostos()
            }
        },

        guardarPeriodoCompleto(e) {
            let codigoSeleccionado = e.value;
            this.periodoSeleccionado = this.ListaPeriodos.find(
                (periodo) => periodo.CodigoPeriodo === codigoSeleccionado
            );
            
            if (this.periodoSeleccionado && this.periodoSeleccionado.CodigoPeriodo) {
                this.FechaI = this.periodoSeleccionado.FechaInicial;
                this.FechaF = this.periodoSeleccionado.FechaFinal;
                
                // Solo ejecutar las consultas si el periodo está definido
                this.ConsultaAuxiliarG();
                this.Mayorizar();
                this.limpiarFiltrosCostos()
            }
        },

        guardarSucursal(e) {
            this.SucursalSeleccionada = e.value;
            if (this.periodoSeleccionado && this.periodoSeleccionado.CodigoPeriodo) {
                this.ConsultaAuxiliarG();  // Si es necesario
                this.Mayorizar();          // Si es necesario
                this.limpiarFiltrosCostos()
            }
        },

        ConsultaPeriodo() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/ConsultaPeriodo', {
                    Opcion: "C",
                    SubOpcion: "6",
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaPeriodos = resp.data.json;
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido cargar los periodos, favor validar.',
                        })
                    }
                    this.ConsultaPeriodoActual();
                })
                .catch(() => {})
        },

        ConsultaPeriodoActual() {
            const url = this.$store.state.global.url;
            const endpoint = url + 'app/v1_contabilidad_general/ConsultaPeriodoActual';

            this.axios.post(endpoint, {
                Opcion: "C",
                SubOpcion: "2",
            })
            .then(resp => {
                if (resp.data.codigo === 0 && Array.isArray(resp.data.json) && resp.data.json.length > 0) {
                    this.DatosPeriodo = resp.data.json;

                    const codigoPeriodoActual = resp.data.json[0]?.CodigoPeriodo;

                    if (codigoPeriodoActual && this.ListaPeriodos && this.ListaPeriodos.length > 0) {
                        const periodo = this.ListaPeriodos.find(p => p.CodigoPeriodo === codigoPeriodoActual);

                        if (periodo) {
                            this.cbPeriodos = periodo.CodigoPeriodo;
                        }
                    }
                } else {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Cuentas',
                        text: 'No se ha podido cargar los periodos, favor validar.',
                    });
                }
            })
            .catch(() => {});
        },
onChangePeriodos(value) {
    if (value !== null && value.length !== 0) {
        this.CodigoPeriodo = value.CodigoPeriodo;
        this.DescPeriodo = value.DescPeriodo;

        // Establecer FechaMin y FechaMax
        this.FechaMin = new Date(value.FechaInicial);

        let fechaFinal = new Date(value.FechaFinal);
        fechaFinal.setDate(fechaFinal.getDate() + 1); // incluir el día final
        this.FechaMax = fechaFinal;

        // Configurar flatpickr con minDate, maxDate y enable
        this.configFromdateTimePicker.minDate = this.FechaMin;
        this.configFromdateTimePicker.maxDate = this.FechaMax;
        this.configFromdateTimePicker.enable = [
            (date) => date >= this.FechaMin && date <= this.FechaMax
        ];

        // Obtener el último día del mes de FechaFinal
        let fechaFinMes = new Date(value.FechaFinal);
        let ultimoDiaMes = new Date(fechaFinMes.getFullYear(), fechaFinMes.getMonth() + 1, 0);

        // Verificar que el último día del mes esté dentro del rango permitido
        if (ultimoDiaMes >= this.FechaMin && ultimoDiaMes <= this.FechaMax) {
            this.FechaPartida = ultimoDiaMes;
        } else {
            // Si no está en el rango, usar FechaMin como fallback
            this.FechaPartida = this.FechaMin;
        }

    } else {
        // Reset de los campos si value es nulo o vacío
        this.CodigoPeriodo = '';
        this.DescPeriodo = '';

        this.Fecha = new Date(); // o también ''
        this.FechaMin = '';
        this.FechaMax = '';
        this.configFromdateTimePicker.enable = [];
        this.FechaPartida = '';
    }
},


        //Movimientos
        ConsultaMovimientos() {
            const url = this.$store.state.global.url;
            this.axios.post(url + 'app/v1_contabilidad_general/CAuxiliarCostos', {
                OpcionC: 0,
                SubOpcionC: 0,
            })
            .then(resp => {
                if (resp.data.codigo == 0) {
                    this.ListaMovimientos = resp.data.json;
                    this.addTodos();

                    // Seleccionar automáticamente el primer movimiento si existe
                    if (this.ListaMovimientos.length > 0) {
                        this.MovimientoSeleccionado = this.ListaMovimientos[0].Tipo;
                        this.guardarMovimiento({ value: this.MovimientoSeleccionado }); // Llama el handler si es necesario
                    }
                } else {
                    this.ListaMovimientos = [];
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Auxiliar Costos',
                        text: 'No se ha podido cargar los tipos de transacciones, favor validar.',
                    });
                }
            });
        },


        ConsultaSucursal() {
            const url = this.$store.state.global.url;
            this.axios.post(url + 'app/v1_contabilidad_general/CAuxiliarCostos', {
                OpcionC: 0,
                SubOpcionC: 2
            })
            .then(resp => {
                if (resp.data.codigo != 0) {
                    this.ListaSucursal = [];
                } else {
                    this.ListaSucursal = resp.data.json;
                    this.addTodas();

                    // Seleccionar automáticamente la primera sucursal si existe
                    if (this.ListaSucursal.length > 0) {
                        this.SucursalSeleccionada = this.ListaSucursal[0].Codigo;
                        this.guardarSucursal({ value: this.SucursalSeleccionada }); // Llama el handler si lo necesitas
                    }
                }
            });
        },

        addTodas() {
            this.ListaSucursal.unshift({
                Codigo: '',
                Nombre: 'Todas'
            });
            this.cbSucursal = {
                Codigo: '',
                Nombre: 'Todas'
            }
        },
        addTodos() {
            this.ListaMovimientos.unshift({
                Tipo: '',
                Codigo:'',
                Descripcion: 'Todos los Tipos'
            });
            this.cbMovimientos = {
                Tipo: '',
                Codigo:'',
                Descripcion: 'Todos los Tipos'
            }
        },
      
        //Grid

        ConsultaAuxiliarG(){
            const movimiento = this.ListaMovimientos.find(item => item.Tipo === this.MovimientoSeleccionado);
            const sucursal = this.ListaSucursal.find(item => item.Codigo === this.SucursalSeleccionada);
            const url = this.$store.state.global.url;
            this.axios.post(url + 'app/v1_contabilidad_general/CAuxiliarCostos', {
                      OpcionC: 0,
                      SubOpcionC: 5,
                       Periodo: this.periodoSeleccionado.CodigoPeriodo,
                                TipoOP: movimiento.Tipo,
                                Sucursal: sucursal.Codigo
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.AuxiliarCostos = resp.data.json;
                    }
                })

        }, 


        
        recargarGrid() {
            this.$refs.myDataGridRef?.instance?.refresh();
        },

        //MAYORIZAR
       Mayorizar() {
            const url = this.$store.state.global.url;
            this.axios.post(url + 'app/v1_contabilidad_general/Mayorizacion_SM', {
                    Opcion: 'C',
                    SubOpcion: '1',
                    Periodo: this.periodoSeleccionado.CodigoPeriodo
                })
                .then(resp => {
                    this.DescMayorizada = null
                    if (resp.data.codigo === 0) {
                        this.DescMayorizada = resp.data.json[0].DescripcionM;
                        // No necesitas el if/else aquí, el v-if en el template manejará la visualización
                    }
                })
                .catch(() => {
                });
        },

        //Cuentas
        ConsultasCuentas() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/ConsultasCuentas', {
                    Opcion: 'C',
                    SubOpcion: '1',
                    Activa: 'S'
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaCuentas = resp.data.json
                    } else {
                        this.ListaCuentas = []
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Auxiliar Bancos',
                            text: 'No se ha podido cargar las cuentas, favor validar.',
                        })
                    }
                })

        },

        onChangeCuenta(value) {

            if (value !== null && Object.keys(value).length !== 0) {
            this.cbCuentas = {
                CodigoCuenta: value.CodigoCuenta,
                Nombre: value.Nombre
            };
            } else {
            this.cbCuentas = {
                CodigoCuenta: null,
                Nombre: null
            };
            }
        },

        //ReportesFiltros
        //Reporte 1 


        async GenerarReporte(formatoInterno, formato) {
            let nombreReporte = '';
            let OpcionInterna = this.IdReporte;

            const movimiento = this.ListaMovimientos.find(item => item.Tipo === this.MovimientoSeleccionado);
            if (!movimiento || !this.periodoSeleccionado) return;

            let admision = '';
            let serie = '';

            if (this.IdReporte == 1) {
                nombreReporte = 'ReporteDetalladoPorRefCS';
                this.ReferenciaSel = '';
                OpcionInterna = 9.1;

                // Validación: Si Admision está activado, debe tener datos correctos
                if (this.Admision) {
                    // Si no hay fila seleccionada, error
                    if (!this.FilaSeleccionada) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Error en Admisión',
                            text: 'Debe seleccionar una fila para generar el reporte.',
                        });
                        return;
                    }

                    admision = this.FilaSeleccionada.Admision || '0';
                    serie = this.FilaSeleccionada.SerieAdmision || '';

                    // Si admision es '0' o vacía, error
                    if (admision === '0' || !admision.trim()) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Error en Admisión',
                            text: 'La admisión no es válida (debe tener un valor).',
                        });
                        return;
                    }

                    // Si serie tiene valor, error
                    if (serie === "") {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Error en Serie',
                            text: 'No se puede generar el reporte con una serie asignada.',
                        });
                        return;
                    }
                }
            } else if (this.IdReporte == 2) {
                nombreReporte = 'ReporteCuentasIntegradasCS';
                OpcionInterna = 9.2;
            } else if (this.IdReporte == 3) {
                nombreReporte = 'ReporteDetallePorCuentaCS';
                OpcionInterna = 9.3;
            } else if (this.IdReporte == 4) {
                nombreReporte = 'ReporteSiniestralidad';
                OpcionInterna = 9.4;
            }

            // Generar el reporte solo si pasó todas las validaciones
            let postData = {
                nombrereporte: nombreReporte,
                tiporeporte: formatoInterno,
                OpcionC: 0,
                SubOpcionC: OpcionInterna,
                Periodo: this.periodoSeleccionado.CodigoPeriodo,
                FechaInicial: this.getDateValue(this.FechaI),
                FechaFinal: this.getDateValue(this.FechaF),
                TipoOP: movimiento.Tipo,
                Cuenta: this.cbCuentas.CodigoCuenta,
                Admision: admision,
                Serie: serie,
                Pop: '',
                Documento: ''
            };

            this.$reporte_modal({
                Nombre: 'ReportesAuxiliarCostos',
                Opciones: {
                    ...postData
                },
                Formato: formato
            });
        },

        GenerarPartida() {
                    this.AuxiliarSinCuenta()
                    this.Correlativo()
                    const info = this.periodoSeleccionado

                    if (info && info.CodigoPeriodo) {
                        // Llamar a onChangePeriodos solo si la información es válida
                        this.onChangePeriodos(info)
                    } else {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#e74c3c',
                            title: 'Valores incorrectos',
                            acceptText: 'Aceptar',
                            text: 'No hay información válida en periodoSeleccionado',
                            buttonCancel: 'border',
                            accept: () => {},
                        })
                        return
                    }
        },

        AuxiliarSinCuenta() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/CAuxiliarCostos', {
                    OpcionC: 0,
                    SubOpcionC: 7.2,
                    Periodo: this.periodoSeleccionado.CodigoPeriodo

                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.DocSinCuentas = resp.data.json

                        if (this.AuxiliarSinCuenta.length > 0) {
                            this.DocSinCuentasV = true
                          this.$vs.dialog({
                                    type: 'alert',
                                    color: '#e74c3c',
                                    title: 'Error al Generar Partida de Diario',
                                    acceptText: 'Aceptar',
                                    text: 'Favor revisar los documentos sin número de cuenta.',
                                    buttonCancel: 'border',
                                    accept: () => {},
                            })
                            return;
                        } else {
                            this.DocSinCuentasV = false
                            this.DescuadrePorDoc()
                        }
                    }
                })

        },

        DescuadrePorDoc() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/CAuxiliarCostos', {
                    OpcionC: 0,
                    SubOpcionC: 7.3,
                    Periodo: this.periodoSeleccionado.CodigoPeriodo

                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.DescPorDocumento = resp.data.json

                        if (this.DescPorDocumento.length > 0) {
                            this.DescPorDocumentoV = true
                            this.$vs.dialog({
                                    type: 'alert',
                                    color: '#e74c3c',
                                    title: 'Error al Generar Partida de Diario',
                                    acceptText: 'Aceptar',
                                    text: 'Partida total no cuadra por más de un centavo, a continuacion se detalla el total de los no cuadrados.',
                                    buttonCancel: 'border',
                                    accept: () => {},
                                })
                            return;
                        } else {
                            this.OpcionSel = 1
                            this.PartidaDiario = true
                            this.Periodo = this.cbPeriodos.CodigoPeriodo
                            this.DescPorDocumentoV = false
                        }
                    }
                })

        },

        ConsultaDetalleDocumento() {
            this.ListaDetalle = []

            const url = this.$store.state.global.url;
            this.axios.post(url + 'app/v1_contabilidad_general/CAuxiliarCostos', {
                OpcionC: 0,
                SubOpcionC: 6,
                TipoOP: this.FilaSeleccionada.TipoOperacion, // Asume que existe esta propiedad
                Serie: this.FilaSeleccionada.Serie,   // Asume que existe esta propiedad
                Documento: this.FilaSeleccionada.Doc
            })
            .then((resp) => {
                // Maneja la respuesta aquí
                this.ListaDetalle = resp.data.json
                this.ModificarAuxiliar = true;
            })

        },

        ResumenQuery() {
            if (this.Partida == 0 || this.Partida == '') {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Generar Partida de Diario',
                    text: 'Ingrese Número de partida.',
                })
                return;
            }
            if (this.Periodo == 0 || this.Periodo == '') {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Generar Partida de Diario',
                    text: 'Selecciones el periodo.',
                })
                return;
            } else {
                const url = this.$store.state.global.url
                 let fechaFormateada = null;
                if (this.FechaPartida) {
                    const [day, month, year] = this.FechaPartida.split('/');
                    const fechaObj = new Date(`${year}-${month}-${day}`);
                    
                    // Verificar si la fecha es válida
                    if (!isNaN(fechaObj.getTime())) {
                        fechaFormateada = fechaObj.toISOString().split('T')[0].replace(/-/g, '/');
                    }
                }
                const periodoTexto = this.periodoSeleccionado.CodigoPeriodo; // e.g., "2025-05"
                const periodoNumerico = parseInt(periodoTexto.replace('-', ''));
                this.axios.post(url + 'app/v1_contabilidad_general/TAuxiliarCostos', {
                    Opcion: 1,
                    Periodo: periodoNumerico,
                    Partida: this.NuevaPartida,
                    Fecha: fechaFormateada,
                    Descripcion: this.Descripcion,
                    OPartida: 1
                }).then(()=>{
                      this.PartidaDiario = false
                        this.Descripcion = null
                        this.ReferenciaSel = 0
                     
                }).catch(()=>{
                 this.PartidaDiario = false
                 this.Descripcion = null
                 this.ReferenciaSel = 0
                })
               
            }
        },

        validarSumasYGuardar() {
            const totalDebe = this.ListaDetalle.reduce((sum, row) => sum + (parseFloat(row.Debe) || 0), 0);
            const totalHaber = this.ListaDetalle.reduce((sum, row) => sum + (parseFloat(row.Haber) || 0), 0);

            // Comparar con precisión numérica
            const precision = 0.01;
            if (Math.abs(totalDebe - totalHaber) > precision) {
             this.$vs.dialog({
                        type: 'alert',
                        color: '#e74c3c',
                        title: 'Ups, hubo un problema!',
                        acceptText: 'Aceptar',
                        text: 'Para guardar el registro, el Debe y el Haber deben estar cuadrados (iguales).',
                        buttonCancel: 'border',
                        accept: () => {},
                    })
            return;
            }
            this.ResumenQueryOpcion2()
            
        },  



        ResumenQueryOpcion2() {
            this.ListaDetalleSrt = this.ListaDetalle
                .map(item => {
                    const campos = [
                        item.Id,
                        item.Cuenta,
                        parseFloat(item.Debe) !== 0.00 ? parseFloat(item.Debe).toFixed(2) : "",
                        parseFloat(item.Haber) !== 0.00 ? parseFloat(item.Haber).toFixed(2) : ""
                    ];
                    return campos.join(",");
                })
                .join(";"); // Separador entre registros

            const url = this.$store.state.global.url;
            this.axios.post(url + 'app/v1_contabilidad_general/TAuxiliarCostos', {
                Opcion: 2,
                Periodo: this.periodoSeleccionado.CodigoPeriodo,
                TipoOP: this.FilaSeleccionada.TipoOperacion,
                Serie: this.FilaSeleccionada.Serie,
                Documento: this.FilaSeleccionada.Doc,
                Str: this.ListaDetalleSrt
            });
        },

        Correlativo() {
            this.NuevaPartida = null
            const url = this.$store.state.global.url;
            this.axios.post(url + 'app/v1_contabilidad_general/CorrelativoCorrecto', {
                Opcion: "C",
                SubOpcion: "8",
                Periodo: this.periodoSeleccionado.CodigoPeriodo,
            }).then((resp) => {
                if (resp.data && resp.data.json && resp.data.json.length > 0) {
                    const corr = resp.data.json[0].CorrelativoPartidas;
                    this.NuevaPartida = corr === '' ? '1' : corr; // ahora es string
                }
            }).catch(() => {
            });
        },

        onEditorPreparing(e) {
             if (e.dataField === "Cuenta" && e.parentType === "dataRow") {
                const defaultValueChangeHandler = e.editorOptions.onValueChanged;
                e.editorOptions.onValueChanged = (args) => {
                    const cuenta = args.value;
                    const grid = this.$refs.gridDetalle.instance;
                    
                    // Guardar el valor anterior de la cuenta y nombre de cuenta
                    const previousCuenta = grid.cellValue(e.row.rowIndex, "Cuenta");
                    const previousNombreCuenta = grid.cellValue(e.row.rowIndex, "NombreCuenta");
                    
                    this.axios.post("/app/v1_contabilidad_general/BusquedaCuentas", {
                        Opcion: 11,
                        Contabilidad: cuenta,
                    }).then((resp) => {
                        if (resp.data.codigo == 0 && resp.data.json && resp.data.json.length > 0) {
                            // Si hay datos válidos, establece el nombre de la cuenta
                            grid.cellValue(e.row.rowIndex, "NombreCuenta", resp.data.json[0].Nombre);
                        } else {
                            // Si no hay datos válidos, restaura los valores anteriores
                            grid.cellValue(e.row.rowIndex, "Cuenta", previousCuenta);
                            grid.cellValue(e.row.rowIndex, "NombreCuenta", previousNombreCuenta);
                            
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#e74c3c',
                                title: 'Error Cuenta',
                                acceptText: 'Aceptar',
                                text: 'La cuenta no es válida, verificar',
                                buttonCancel: 'border',
                                accept: () => {},
                            });
                        }
                        grid.refresh();
                    }).catch(() => {
                        // Restaura los valores anteriores en caso de error
                        grid.cellValue(e.row.rowIndex, "Cuenta", previousCuenta);
                        grid.cellValue(e.row.rowIndex, "NombreCuenta", previousNombreCuenta);
                        grid.refresh();
                        
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#e74c3c',
                            title: 'Error',
                            acceptText: 'Aceptar',
                            text: 'Ocurrió un error al validar la cuenta',
                            buttonCancel: 'border',
                            accept: () => {},
                        });
                    });
                    
                    if (defaultValueChangeHandler) {
                        defaultValueChangeHandler(args);
                    }
                };
            }
        },

        limpiarFiltrosCostos() {
            const gridInstance = this.$refs.myDataGridRef?.instance;
            if (gridInstance) {
                gridInstance.clearFilter();                      // Limpia filtros de columna
                gridInstance.option('searchPanel.text', '');     // Limpia texto de búsqueda
                gridInstance.option('paging.pageIndex', 0);      // Vuelve a la primera página
            }
        },


        cuentaCellTemplate(cellElement, cellInfo) {
            cellElement.innerText = cellInfo.value;
            cellElement.style.backgroundColor = '#aed6f1'; // Verde claro
        },

        debeCellTemplate(cellElement, cellInfo) {
            cellElement.innerText = this.formatMoneda(cellInfo.value);
            cellElement.style.backgroundColor = '#e6ffe6'; // Verde claro
        },

        haberCellTemplate(cellElement, cellInfo) {
            cellElement.innerText = this.formatMoneda(cellInfo.value);
            cellElement.style.backgroundColor = '#ffe6e6'; // Rojo claro
        },
        limpiarCuenta() {
                this.cbCuentas = {
                    CodigoCuenta: null,
                    Nombre: null
                };
                this.onChangeCuenta(this.cbCuentas);
        }



    }
}
</script>   

<style scoped>
.footer {
    height: 70px;
    margin-top: -65px;
    margin-left: 10px;
}

.label-size {
    font-size: 12px;
    font-weight: bold;
}

.label-StatusM {
    font-size: 14px;
    font-weight: bold;
    color: darkred;
}

.label-i {
    font-size: 18px;
    font-weight: bold;
    color: rgb(240, 158, 6);
}

.cuadro-i {
    background: #faf2f1;
}

.flex-container {
    display: flex;
    flex-direction: row;
}

.padre {
    position: relative;
}

.hijo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.dx-datagrid-headers {
  background-color: #2471a3 !important;
  color: #fdfefe !important;
  font-weight: bold;
}   



.radio-custom:hover {
  --vs-radio-border-color: #9ca3af;
}

.vs-radio--text {
  font-size: 0.9375rem;
}

.radio-custom {
  --vs-radio-border-color: #d1d5db;
  --vs-radio-background-color-checked: #3b82f6; /* Azul más moderno */
  --vs-radio-border-color-checked: #3b82f6;
  --vs-radio-size: 1.1rem;
}

.radio-custom:hover {
  --vs-radio-border-color: #93c5fd;
}

.vs-radio--text {
  font-size: 0.9375rem;
  color: #374151; /* Color de texto más oscuro */
}

/* Efecto de transición suave */
.vs-radio-con {
  transition: all 0.2s ease;
}


</style>

<style>

.Costos .no-selection-style .dx-selection.dx-row > td {
  background-color: transparent !important;
}

 .devextreme-like-datepicker {
  border: 1px solid #d3d3d3;
  border-radius: 4px;
  padding: 6px 10px;
  font-size: 14px;
  height: 36px;
  width: 100%;
  max-width: 100%;
  font-family: "Segoe UI", Roboto, sans-serif;
  box-sizing: border-box;
  transition: border 0.2s ease-in-out;
}


.Costos .dx-datagrid .dx-selection.dx-row > td {
  background-color: #f7dc6f !important;
   color: black !important;
  
}

.Costos .color{
    background-color: aqua !important;
}

.Costos .dx-datagrid-headers .dx-datagrid-table .dx-header-row {
    background-color: #2980b9; /* Verde */
    color: white;
}

.Costos .dx-header-filter:not(.dx-header-filter-empty) {
    color: yellow;
} 




</style>
