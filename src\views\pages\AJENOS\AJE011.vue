<template>
<div>
    <vx-card title="Pago de Honorarios Cupones">
        <ValidationObserver ref="formValidate" v-slot="{ invalid,handleSubmit }" mode="lazy">
            <div class="flex mb-5 " style="justify-content:space-between;border:1px solid rgba(0,0,0,0.1);padding:5px">
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <SM-Buscar v-model="info.Codigo" label="Código del Médico" api="app/Ajenos/Busqueda_Ajenos" :api_campos="['Codigo','Nombre','Apellido','Corporativo','Tipo']" :api_titulos="['Codigo','Nombre','Apellido','Corporativo','Tipo']" api_campo_respuesta="Codigo" :callback_buscar="Consulta().Consulta_Ajenos" :callback_cancelar="Otros().Limpiar_Datos" :disabled_texto="true" />
                </div>
            </div>
            <div class="flex mb-5 ">
                <div class="w-full md:w-2/3 lg:w-2/3 xl:w-2/3 p-1">
                    <vs-input label="Nombre Médico" class="w-full" v-model="info.Medico" :disabled="true" />
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <vs-input label="NIT" class="w-full" v-model="info.NIT" :disabled="true" />
                </div>
            </div>
            <div class="flex mb-5 ">
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <SM-Buscar label="Tipo de Médico" v-model="info.Tipo" :api="[{Codigo:'C',Nombre:'Cortesía'},{Codigo:'M',Nombre:'Casa'},{Codigo:'CC',Nombre:'Casa-Cortesía'},{Codigo:'R',Nombre:'Referencia'}]" :api_campos="['Nombre']" :api_titulos="['Nombre']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="false" />
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <SM-Buscar v-model="info.Especialidad" label="Especialidad" api="app/Ajenos/Busqueda_Especialidad" :api_cache="true" :api_campos="['Codigo','Descripcion']" :api_titulos="['Codigo','Descripción']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Descripcion" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="false " />
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <SM-Buscar v-model="info.SubEspecialidad" label="SubEspecialidad" api="app/Ajenos/Busqueda_SubEspecialidad" :api_campos="['Codigo','Nombre']" :api_titulos="['Codigo','Nombre']" :api_filtro="{Codigo_Especialidad:info.Especialidad}" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="false" />
                </div>
            </div>
            <vs-divider></vs-divider>
            <div class="flex flex-wrap mb-5 ">
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <ValidationProvider name="TelefonoClinica" v-slot="{ errors }">
                        <vs-input label="Fecha de Última Facturación" type="datetime" style="padding:0" disabled="false" class="w-full" v-model="info.FacturaFecha" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1 mydanger">
                    <SM-Buscar label="Tipo de Régimen" v-model="info.Proveedor_Tipo_Contribuyente" :api="[{Codigo:'P',Nombre:'Pequeño Contribuyente'}]" :api_campos="['Codigo','Nombre']" :api_titulos="['Código','Nombre']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :callback_cancelar="true" :disabled_texto="true" :mostrar_busqueda="false" :disabled_busqueda="true" />
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1 mydanger">
                    <SM-Buscar label="Tipo de Retención" v-model="info.Proveedor_Tipo_Retencion" :api="[{Codigo:'D',Nombre:'Definitiva'},{Codigo:'0',Nombre:'Cero'}]" :api_campos="['Codigo','Nombre']" :api_titulos="['Código','Nombre']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :callback_cancelar="true" :disabled_texto="true" :mostrar_busqueda="false" :disabled_busqueda="true" />
                </div>
            </div>
            <vs-divider></vs-divider>

            <vs-table2 max-items="20" search tooltip pagination :data="pagos">
                <template slot="header">
                    <vs-button color="primary" type="filled" @click.native="Consulta().Consulta_Datos()" icon-pack="feather" icon="icon-refresh-cw" :disabled="info.Codigo==null">
                        Actualizar
                    </vs-button>
                </template>

                <template slot="thead">
                    <th width="50px">Selección</th>
                    <th order="NombreSucursal" width="120px">Hospital</th>
                    <th order="Cupon" width="120px">Cupón</th>
                    <th order="Fecha" width="160px">Fecha</th>
                    <th order="NombrePaciente">Paciente</th>
                    <th order="MontoCoEx" orderType="number" width="120px">MontoCoEx</th>
                    <th order="MontoSiniestralidad" orderType="number" width="120px">MontoSiniestralidad</th>
                </template>

                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">
                        <vs-td2>
                            <vs-checkbox v-model="tr.seleccion" indeterminate @change="Otros().seleccionCargo(tr)" />
                        </vs-td2>

                        <vs-td2>
                            {{ tr.NombreSucursal.toUpperCase() }}
                        </vs-td2>

                        <vs-td2>
                            {{ tr.Cupon }}
                        </vs-td2>

                        <vs-td2>
                            {{ tr.Fecha }}
                        </vs-td2>

                        <vs-td2>
                            {{tr.NombreCliente }}
                        </vs-td2>

                        <vs-td2>
                            {{tr.MontoCoEx}}
                        </vs-td2>

                        <vs-td2>
                            {{ tr.MontoSiniestralidad }}
                        </vs-td2>
                    </tr>
                </template>
            </vs-table2>
            <table width="100%" style="border-spacing: 0;">
                <tr style="background-color:#bdc3c7; color:#34495e">
                    <td style="padding:10px">

                    </td>
                    <td width="250px" style="padding:10px; text-align:right;font-weight:bold">
                        <b>Total COEX</b><br>
                        {{calcularMontoCoex}}
                    </td>
                    <td width="250px" style="padding:10px; text-align:right;font-weight:bold">
                        <b>Total Siniestralidad</b><br>
                        {{calcularMontoSiniestralidad}}
                    </td>
                </tr>
            </table>
            <vs-divider></vs-divider>
            <div class="flex bottom">
                <vs-spacer></vs-spacer>
                <vs-button color="success" type="filled" @click.native="handleSubmit(()=>Guardar().Cupon_Pago_Honorarios())" :disabled="invalid || pagos.filter(f=>f.seleccion).length==0">Enviar a Pagos de Honorarios</vs-button>
            </div>
        </ValidationObserver>
    </vx-card>
</div>
</template>

<script>
export default {
    data() {
        return {
            /**
             * Documentación del módulo
             */
            doc_: [
                'Genera el listado de todos los los cupones que no se han asignado a los honorarios.',
                'Los cupones mostrados estan en estado <b>No Pagado</b> y no cuentan con factura.',
                'Al utilizar <b>Enviar a Pagos de Honorarios</b> se envían los cupones a  la planilla de honorarios.',
                '* Se obtienen los datos del sp <i>sp_Ajenos_Honorarios_Cupones</i>.',
                '** Validar bitacora'
            ],

            /**
             * Editar el monto de la tabla
             */
            info: {
                Codigo: null,
                Medico: null,
                NIT: null,
                Tipo: null,
                Especialidad: null,
                SubEspecialidad: null,
                FacturaFecha: null,
                FacturaSerie: null,
                FacturaNumero: null,
                Total: '0.00',
                MostrarFactura: false,
                Proveedor: null,
                Proveedor_Tipo_Contribuyente: null,
                Proveedor_Tipo_Retencion: null,
            },

            /**
             * Listado de permisos
             */
            permisos: {
                editar: false
            },

            /**
             * Información de los pagos
             */
            pagos: [],

            pagosSeleccion: [],

            /**
             * Información de los cargos
             */
            infoCargo: {
                mostrar: false,
                info: null
            }

        }
    },
    computed: {
        calcularMontoCoex() {
            const arr = this.pagos.filter(f => f.seleccion)
            return this.$formato_moneda(arr.reduce((acumulador, valor) => acumulador + parseFloat(valor.MontoCoEx), 0))
        },
        calcularMontoSiniestralidad() {
            const arr = this.pagos.filter(f => f.seleccion)
            return this.$formato_moneda(arr.reduce((acumulador, valor) => acumulador + parseFloat(valor.MontoSiniestralidad), 0))
        }
    },
    components: {

    },
    methods: {
        //=======================================================================================================
        // CONSULTA
        //=======================================================================================================
        Consulta: function () {
            return {
                Consulta_Ajenos: (item) => {
                    this.info.Medico = `${item.Nombre.trim()} ${item.Apellido.trim()}`
                    this.Consulta().Consulta_InfoAjenos(this.info.Codigo)
                        .then(() => {
                            this.Consulta().Consulta_Datos()
                        })
                },

                Consulta_Datos: () => {
                    this.Consulta().Consulta_Cupones(this.info.Codigo)
                    this.Consulta().Consulta_UltimaActualizacion(this.info.Codigo)
                    this.Consulta().Consulta_Ajenos_Proveedores(this.info.Proveedor)
                },

                Consulta_InfoAjenos: (codigo) => {
                    return this.axios.post('/app/ajenos/Consulta_Ajenos', {
                            Codigo: codigo
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                let datos = resp.data.json[0]

                                // Remplazando el tipo  si se ha seleccionado tipocasacortesia
                                if (datos['TipoCortesiaCasa'] == true) datos['Tipo'] = 'CC'
                                this.info.Tipo = datos.Tipo
                                this.info.Especialidad = datos.EspecialidadPrincipal.trim()
                                this.info.SubEspecialidad = datos.Especialidad.trim()
                                this.info.Proveedor = datos.Proveedor.trim()
                                return datos.Proveedor.trim()
                            }
                        })
                },

                Consulta_Cupones: (Ajeno) => {

                    this.pagos = []
                    return this.axios.post('/app/ajenos/Cupones_Listado', {
                            Ajeno
                        })
                        .then(resp => {

                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.pagos = resp.data.json.map(m => {
                                    return {
                                        seleccion: false,
                                        Accion: null,
                                        ...m
                                    }
                                })
                            }
                        })
                        .catch(() => {

                        })
                },

                Consulta_UltimaActualizacion: (Ajeno) => {
                    return this.axios.post('/app/ajenos/HonorariosConsultaEstados', {
                            Opcion: 'A',
                            Ajeno
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.info.FacturaFecha = resp.data.json[0].Fecha
                            }
                        })
                },

                Consulta_Ajenos_Proveedores: (Codigo) => {
                    if (Codigo)
                        return this.axios.post('/app/ajenos/Consulta_Ajenos_Proveedores', {
                                Codigo_Proveedor: Codigo
                            })
                            .then(resp => {
                                if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                    let datos = resp.data.json[0]

                                    Object.entries(datos).forEach(([key, value]) => {
                                        this.info[key] = (typeof value == 'string') ? value.trim() : value
                                    });
                                }
                            })
                },
            }
        },

        //=======================================================================================================
        // NUEVO
        //=======================================================================================================
        Nuevo: function () {
            return {}
        },

        //=======================================================================================================
        // GUARDAR
        //=======================================================================================================
        Guardar: function () {
            return {
                Cupon_Pago_Honorarios: () => {

                    const arr = {
                        cupones: this.pagos.filter(f => f.seleccion).map(m => m.Cupon),
                        ajeno: this.info.Codigo
                    }
                    return this.axios.post('/app/ajenos/CuponesHonorarios', arr)
                        .then(resp => {

                            if (resp.data.codigo == 0) {
                                this.Consulta().Consulta_Cupones(this.info.Codigo)
                            }
                        })
                        .catch(() => {

                        })
                }
            }
        },

        //=======================================================================================================
        // EDITAR
        //=======================================================================================================
        Editar: function () {
            return {}
        },

        //=======================================================================================================
        // ELIMINAR
        //=======================================================================================================
        Eliminar: function () {
            return {}
        },

        //=======================================================================================================
        // OTROS
        //=======================================================================================================
        Otros: function () {
            return {
                Limpiar_Datos: () => {
                    Object.entries(this.info).forEach(([key]) => {
                        this.info[key] = null
                    });
                    this.pagos = []
                },

                seleccionCargo: (item) => {
                    if (item.Accion) {
                        this.pagos.filter(f => f.Categoria == item.Accion).map(m => m.seleccion = item.seleccion)
                    }
                },
            }
        }
    }
}
</script>

<style scoped>
.facturas {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100em;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 50px;
    z-index: 99999;
}
</style>
<style>
.mydanger input:disabled {
    font-weight: 900 !important;
    color: #ea5455 !important;
    pointer-events:none;
    border: 1.5px solid #ea5455 !important;
    /* background: green !important; */
}

.mydanger .vs-con-input{
    border-color: #ea5455 !important;
}
</style>