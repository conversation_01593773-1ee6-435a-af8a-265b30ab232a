<template>
<div>
    <DxTabPanel ref="tabRegistro" :height="'100%'" :data-source="menu">
        <template #title="{ data: tab }">
            <span>
                <font-awesome-icon class="pr-1" :icon="['fas', tab.icon]" />
                {{ tab.title }}
            </span>
        </template>
        <template #item="{ data: tab }">
            <div>
                <div v-if="tab.id === 1" class="p-2">
                    <Proveedores :TipoDoc="2" ref="Proveedores" :Corporativo="Corporativo" />
                </div>
                <div v-if="tab.id === 2" class="p-2">
                    <BoletasPagosMedicos :TipoDoc="2" :Corporativo="Corporativo" ref="BoletasPagosMedicos" />
                </div>
                <div v-if="tab.id === 3" class="p-2">
                    <GeneracionChequesLotes :TipoDoc="2" :Corporativo="Corporativo" ref="GeneracionAcreditamientoLotes" />
                </div>
            </div>
        </template>
    </DxTabPanel>
</div>
</template>

<script>
import Proveedores from './Proveedores.vue';
import GeneracionChequesLotes from './GeneracionChequesLotes.vue';
import BoletasPagosMedicos from './BoletasPagosMedicos.vue';

export default {
    name: 'Transferencias',
    components: {
        Proveedores,
        GeneracionChequesLotes,
        BoletasPagosMedicos
    },
    data() {
        return {

            SelectedOption: 1,

            menu: [{
                    id: 1,
                    title: "Proveedores",
                    icon: "people-carry-box",
                },
                {
                    id: 2,
                    title: "Boletas de pago médicos",
                    icon: "hospital-user",
                },
                {
                    id: 3,
                    title: "Generación de acreditamientos de lotes",
                    icon: "money-bills",
                },
            ],

        }
    },
    props: {
        TipoDoc: null, // Indica desde qué módulo se invoca el componente  1 = Cheques / 2 = Transferencias
        Corporativo: null
    },
    methods: {},
    created() {},
    mounted() {},
    watch: {},
    computed: {}
}
</script>

<style>

</style>
