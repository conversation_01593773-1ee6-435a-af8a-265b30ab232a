/**
 * Permite convertir una lista a una estructura de arbol, los objetos deben tener un identificador de cada elemento, y una propiedad para identificar al padre al que pertenece
 * 
 * @param collection arreglo con los elementos a manipular con objetos de la forma 
 * {
 *      id: ..., 
 *      idPadre:...,
 * } 
 * @param {String} idName         que indica el nombre de la propiedad que es el identificador de cada elemento del array, en algunos casos sea 'ID', 'identificado', 'index', ...
 * @param {String} parentIdName   nombre de la propiedad que hace referencia la padre al que pertenece el objeto para formar el árbol (como el caso anterior)
 * @param {String} childsName     indica el nombre de la propiedad del array de hijos de cada nodo del arbol, 'children', 'elementos', 'hijos', 'ChidNodes', 'Submenu', ...
 * @param {Boolean} clearChilds    @default false indica si limpian los hijos del nodo evaluado en base a childsName, por ejemplo si el nodo tiene la propiedad  llamada hijos = [ {... hijos, ...}, ... {...} ]
 * Si es verdadero limpiará el arreglo previo a generar el arbol de lo contrario se hará el anidamiento con lo elementos de collection y los 'hijos' de cada nodo si existen
 * @param {Function} callbackElement funcion que se ejecuta con cada elemento antes de armar el árbol, puede ahorrar el uso de un map o foreach
 * @return  {Array} Con la estructura del árbol Objeto resultante con el atributo adicional llamado childsName agrupando a los descendientes 
 * {
 *      id: 1,
 *      parent: null,
 *      ...
 *      otras propiedades de cada elmento del array original
 *      ...
 *      chidren: [
 *          {
 *              id: 12,
 *              parent: 1,
 *              ...
 *              otras propiedades de cada elmento del array original
 *              ...
 *              chidren: [...]
 *          }, 
 *          ...
 *      ]
 * }
 */
export const ListoToTree = function (collection, idName, parentIdName, childsName, clearChilds = false, callbackElement = null) {
    // NOTA: el acceso a propiedades de los objetos se esta haciendo por diccionario Objeto['atributo'] en vez de Objeto.atributo
    const hashTable = Object.create(null)//para busquedas de elementos mientras se agregan al árbol
    collection.forEach(aData => {
                                    if(callbackElement)
                                        callbackElement(aData)
                                    hashTable[aData[idName]] = {...aData}
                                    if(!hashTable[aData[idName]][childsName] || clearChilds) //si la propiedad que anida de los hijos [childsName] no existe
                                        hashTable[aData[idName]][childsName] = []
                                })
    const dataTree = []
    collection.forEach(aData => {
        if(aData[parentIdName] && hashTable[aData[parentIdName]]) 
            hashTable[aData[parentIdName]][childsName].push(hashTable[aData[idName]])
        else 
            dataTree.push(hashTable[aData[idName]])
    })
    return dataTree
}

/**
 * Funcion de recorrido del árbol, se peuede utilizar para búsqueda o ejecutar acciones con cada elemento del arbol
 * Si es para búsqueda retornar algo diferente de null en la función @see compFun, el elemento encontrado por ejemplo ;)
 * @param {Object} node nodo actual que se esta recorriendo, para utilizarlo por primera vez, eviar la Raíz.
 * @param {Function} childsGetter funcion aplicable sobre el nodo la cual debe retornar el valor de sus hijos ej: (arbol)=>{return arbol.hijos}
 * @param {Function} preFunc función de evalucaión para busquedas se ejecuta en preorden, recibe el elemento actual y allí se pueden hace las comparaciones necesarias. si e solo para recorrido retornar null o no retornar valor así recorrerá todo el árbol
 * @param {Function} postFunc función de evalucaión para busquedas seejecuta en postorden, recibe el elemento actual y allí se pueden hace las comparaciones necesarias. si e solo para recorrido retornar null o no retornar valor así recorrerá todo el árbol
 * @returns retorna null si el elemento no se encontróde lo contrario retorna el elemento encontrado o bien lo que retorne la función compFun
 */

export const TravelTree = function(node, childsGetter, preFunc = null, postFunc = null){
    let r = null
    let chidren = childsGetter(node)
    if(!node)
        return null

    if(typeof preFunc == 'function'){
        r = preFunc(node)//validar si el elemento cumple la condición de búsqueda
        if(r)
            return r
    }
        
    if( Array.isArray(chidren) ) //si tiene hijos
    {
        var i;
        for(i=0; r == null && i < chidren.length; i++) {
            r = TravelTree(chidren[i], childsGetter, preFunc, postFunc)
        }
        return r
    }

    if(typeof postFunc == 'function'){
        r = postFunc(node)
        if(r)
            return r
    }

    return null
}
/**
 * Filtra los nodos del árbol en base a la condición dada en @see compFun NOTA: esta función recorre todo el árbol
 * @param {Object} node - object Raíz del árbol
 * @param {Function} compFun Función de evaluación para filtrar, esa se aplicará, enviando por parámetro cada nodo que visite
 * @param {String} childsName Nombre del atributo que contiene los hijos del nodo
 * @returns la misma estructura de árbol per con únicamente lo elementos que cuinciden con la condición dada
 */
export const FilterNodes = function(node, childsName, compFun) {
// Verifica si el nodo actual coincide con el nombre buscado
    if (compFun(node)) {
        return { ...node/*, submenu: []*/ }; // Devuelve un nuevo objeto con la misma estructura
    }

    // Inicializa un nuevo objeto que conservará la estructura jerárquica
    let resultado = { ...node }
    resultado[childsName] = []

    // Recorre los nodos hijos (si los hay) y busca en cada uno de ellos
    if (Array.isArray(node[childsName])) {
        for (const nodo of node[childsName]) {
            const nodoEncontrado = FilterNodes(nodo, childsName, compFun);

            // Si se encontró un nodo coincidente, agrégalo al resultado
            if (nodoEncontrado) {
                resultado[childsName].push(nodoEncontrado);
            }
        }
    }
    // Devuelve el resultado solo si tiene elementos en su submenu
    return resultado[childsName].length > 0 ? resultado : null;
}


/**
 * Función que devuelve un array con las hojas del árbol
 * @param {Object} node nodo raíz del árbol a transformar
 * @param {Function} callBackFunc Función que hará el retrono el elemento hoja hallado
 * @param {Function} chidrenGetter Función que se ejecuta para obtener el array de hijos sobre el nodo evaluado
 * @param {Boolean} allNodes Por defecto false: solo se incluyen únicamente las hojas del árbol. true: indica si se incluyen los nodos intermedios en la contrucción
 * @returns {Array} con los nodos del árbol según el recorrido
 */
export const TreeToList = function(node, callBackFunc, chidrenGetter, allNodes = false) {
    let hijos = chidrenGetter(node)// deberia ser array
    let arr = []
    if(allNodes)
        arr.push(node)
    if (Array.isArray(hijos)) {
        for (const nodo of hijos) {
            arr = arr.concat(TreeToList(nodo, callBackFunc, chidrenGetter, allNodes)) 
        }
        return arr
    }
    else {//es hoja
        let nodoTranformado = callBackFunc(node)//si la función callback manipula el objeto antes de retornarlo
        return Array.isArray(nodoTranformado)? nodoTranformado: [nodoTranformado]
    }
}