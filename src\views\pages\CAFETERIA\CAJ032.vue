<template>
    <vx-card title="Bitácora de la Admisión">
        <!-- Ingreso de datos de admisión -->
        <div class="w-full">
            <div class="w-full">
                <BusquedaAdmision ref="componenteAdmisiones" 
                    @datos_admision="DatosAdmision"
                    :api_filtro="{'Opcion':'CONSULTA','SubOpcion':'ADMISION','activo':0,'tieneHabitacion':0}" 
                    @limpiar_datos_admision="LimpiarPantalla"
                    :api_campos="['Serie','Codigo','Nombres','Apellidos','Habitacion', 'Entrada']" 
                    :api_titulos="['Serie#','Admision#','Nombres','Apellidos','Habitación', 'Entrada#']">
                </BusquedaAdmision>
            </div>
            <br>
        </div>
        <div class="w-full" v-if="datosCheckIn">
            <vs-divider></vs-divider>
            <!--Datos de Check IN -->
            <div class="xs:w-full md:w-12/12 lg:w-12/12 xl:w-12/12">
                <h5>Chek In</h5>
                <br>
                <vs-table2 max-items="10" :data="CheckIn" height="200px" class="mt-0 mb-0">
                    <template slot="thead">
                        <th order="Usuario" width="50px">Usuario</th>
                        <th order="NombreUsuario" width="150px">Nombre Usuario</th>
                        <th order="Seleccion" width="150px">Acepta Chek In</th>
                        <th order="Fecha" width="50px">Fecha</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2>{{tr.Usuario}}</vs-td2>
                            <vs-td2>{{tr.NombreUsuario}}</vs-td2>
                            <vs-td2>{{tr.Seleccion}}</vs-td2>
                            <vs-td2>{{tr.Fecha}}</vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
        </div>
        <div class="w-full" v-if="datosEstadoCuenta">
            <vs-divider></vs-divider>
            <!--Datos estado de cuenta entregado-->
            <div class="xs:w-full md:w-12/12 lg:w-12/12 xl:w-12/12">
                <h5>Entrega estado de cuenta</h5>
                <br>
                <vs-table2 max-items="10" :data="EntregaEstadoCuenta" height="200px" class="mt-0 mb-0">
                    <template slot="thead">
                        <th order="Usuario" width="50px">Usuario</th>
                        <th order="NombreUsuario" width="150px">Nombre Usuario</th>
                        <th order="Fecha" width="50px">Fecha</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2>{{tr.Usuario}}</vs-td2>
                            <vs-td2>{{tr.NombreUsuario}}</vs-td2>
                            <vs-td2>{{tr.Fecha}}</vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
        </div>
        <div class="w-full" v-if="datosCalculoCobro">
            <vs-divider></vs-divider>
            <!--Datos de cobro -->
            <div class="xs:w-full md:w-12/12 lg:w-12/12 xl:w-12/12">
                <h5>Datos de cobro ingresados</h5>
                <br>
                <vs-table2 max-items="10" :data="CalculoCobro" height="200px" class="mt-0 mb-0">
                    <template slot="thead">
                        <th order="Usuario" width="50px">Usuario</th>
                        <th order="NombreUsuario" width="150px">Nombre Usuario</th>
                        <th order="Fecha" width="50px">Fecha</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2>{{tr.Usuario}}</vs-td2>
                            <vs-td2>{{tr.NombreUsuario}}</vs-td2>
                            <vs-td2>{{tr.Fecha}}</vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
        </div>

        <div class="w-full" v-if="datosDevolucion">
            <vs-divider></vs-divider>
            <!--Datos Devoluciones-->
            <div class="xs:w-full md:w-12/12 lg:w-12/12 xl:w-12/12">
                <h5>Devoluciones</h5>
                <br>
                <vs-table2 max-items="10" :data="devoluciones" height="200px" class="mt-0 mb-0">
                    <template slot="thead">
                        <th order="Usuario" width="50px">Usuario</th>
                        <th order="NombreUsuario" width="150px">Nombre Usuario</th>
                        <th order="TipoOrden" width="50px">Tipo Orden</th>
                        <th order="Orden" width="70px">Orden</th>
                        <th order="Fecha" width="50px">Fecha</th>
                        <th order="Producto" width="50px">Producto</th>
                        <th order="Cantidad" width="50px">Cantidad</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2>{{tr.Usuario}}</vs-td2>
                            <vs-td2>{{tr.NombreUsuario}}</vs-td2>
                            <vs-td2>{{tr.TipoOrden}}</vs-td2>
                            <vs-td2>{{tr.Orden}}</vs-td2>
                            <vs-td2>{{tr.Fecha}}</vs-td2>
                            <vs-td2>{{tr.Producto}}</vs-td2>
                            <vs-td2>{{tr.Cantidad}}</vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
            <br>
        </div>
        <div class="w-full" v-if="datosAnulaciones">
            <vs-divider></vs-divider>
            <!--Datos Anulaciones-->
            <div class="xs:w-full md:w-12/12 lg:w-12/12 xl:w-12/12">
                <h5>Anulaciones</h5>
                <br>
                <vs-table2 max-items="10" :data="anulaciones" height="200px" class="mt-0 mb-0">
                    <template slot="thead">
                        <th order="Usuario" width="50px">Usuario</th>
                        <th order="NombreUsuario" width="150px">Nombre Usuario</th>
                        <th order="Serie" width="50px">Serie</th>
                        <th order="Numero" width="70px">Número</th>
                        <th order="Fecha" width="50px">Fecha</th>
                        <th order="Razon" width="50px">Razón</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2>{{tr.Usuario}}</vs-td2>
                            <vs-td2>{{tr.NombreUsuario}}</vs-td2>
                            <vs-td2>{{tr.Serie}}</vs-td2>
                            <vs-td2>{{tr.Numero}}</vs-td2>
                            <vs-td2>{{tr.Fecha}}</vs-td2>
                            <vs-td2>{{tr.Razon}}</vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
            <br>
        </div>
        <div class="w-full" v-if="datosRecalculo">
            <vs-divider></vs-divider>
            <!--Datos Racálculos-->
            <div class="xs:w-full md:w-12/12 lg:w-12/12 xl:w-12/12">
                <h5>Racálculos</h5>
                <br>
                <vs-table2 max-items="10" :data="recalculos" height="200px" class="mt-0 mb-0">
                    <template slot="thead">
                        <th order="Usuario" width="10px">Usuario</th>
                        <th order="NombreUsuario" width="30px">Nombre Usuario</th>
                        <th order="Tipo" width="200px">Tipo</th>
                        <th order="Calculo" width="10px">Calculo</th>
                        <th order="Fecha" width="30px">Fecha</th>
                        <th order="Observaciones" width="260px">Observaciones</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2>{{tr.Usuario}}</vs-td2>
                            <vs-td2>{{tr.NombreUsuario}}</vs-td2>
                            <vs-td2>{{tr.Tipo}}</vs-td2>
                            <vs-td2>{{tr.Calculo}}</vs-td2>
                            <vs-td2>{{tr.Fecha}}</vs-td2>
                            <vs-td2>{{tr.Observaciones}}</vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
            <br>
        </div>
        <div class="w-full" v-if="datosReactivaciones">
            <vs-divider></vs-divider>
            <!--Datos Reactivar-->
            <div class="xs:w-full md:w-12/12 lg:w-12/12 xl:w-12/12">
                <h5>Reactivaciones</h5>
                <br>
                <vs-table2 max-items="10" :data="reactivaciones" height="200px" class="mt-0 mb-0">
                    <template slot="thead">
                        <th order="Usuario" width="50px">Usuario</th>
                        <th order="NombreUsuario" width="150px">Nombre Usuario</th>
                        <th order="Fecha" width="50px">Fecha</th>
                        <th order="Observaciones" width="50px">Observaciones</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2>{{tr.Usuario}}</vs-td2>
                            <vs-td2>{{tr.NombreUsuario}}</vs-td2>
                            <vs-td2>{{tr.Fecha}}</vs-td2>
                            <vs-td2>{{tr.Observaciones}}</vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
            <br>
        </div>
        <div class="w-full" v-if="datosAltas">
            <vs-divider></vs-divider>
            <!--Datos Altas-->
            <div class="xs:w-full md:w-12/12 lg:w-12/12 xl:w-12/12">
                <h5>Altas</h5>
                <br>
                <vs-table2 max-items="10" :data="altas" height="200px" class="mt-0 mb-0">
                    <template slot="thead">
                        <th order="Usuario" width="50px">Usuario</th>
                        <th order="NombreUsuario" width="150px">Nombre Usuario</th>
                        <th order="Fecha" width="50px">Fecha</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2>{{tr.Usuario}}</vs-td2>
                            <vs-td2>{{tr.NombreUsuario}}</vs-td2>
                            <vs-td2>{{tr.Fecha}}</vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
            <br>
        </div>
        <div class="w-full" v-if="datosAjenos">
            <vs-divider></vs-divider>
            <!--Facturas Ajenas-->
            <div class="xs:w-full md:w-12/12 lg:w-12/12 xl:w-12/12">
                <h5>Ajenos</h5>
                <br>
                <vs-table2 max-items="10" :data="ajenos" height="200px" class="mt-0 mb-0">
                    <template slot="thead">
                        <th order="Documento" width="50px">Documento</th>
                        <th order="Status" width="150px">Status</th>
                        <th order="Medico" width="50px">Médico</th>
                        <th order="Nombre" width="50px">Nombre</th>
                        <th order="Valor" width="150px">Valor</th>
                        <th order="Fecha" width="50px">Fecha</th>
                        <th order="Proveedor" width="50px">Proveedor</th>
                        <th order="Periodo" width="150px">Periodo</th>
                        <th order="Usuario" width="50px">Usuario</th>
                        <th order="Registro" width="50px">Registro</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2>{{tr.Documento}}</vs-td2>
                            <vs-td2>{{tr.Status}}</vs-td2>
                            <vs-td2>{{tr.Medico}}</vs-td2>
                            <vs-td2>{{tr.Nombre}}</vs-td2>
                            <vs-td2>{{tr.Valor}}</vs-td2>
                            <vs-td2>{{tr.Fecha}}</vs-td2>
                            <vs-td2>{{tr.Proveedor}}</vs-td2>
                            <vs-td2>{{tr.Periodo}}</vs-td2>
                            <vs-td2>{{tr.Usuario}}</vs-td2>
                            <vs-td2>{{tr.Registro}}</vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
            <br>
        </div>
        <div class="w-full" v-if="datosDescuentos">
            <vs-divider></vs-divider>
            <!--Datos Descuento-->
            <div class="xs:w-full md:w-12/12 lg:w-12/12 xl:w-12/12">
                <h5>Descuentos</h5>
                <br>
                <vs-table2 max-items="10" :data="descuentos" height="200px" class="mt-0 mb-0">
                    <template slot="thead">
                        <th order="Usuario" width="50px">Usuario</th>
                        <th order="NombreUsuario" width="150px">Nombre Usuario</th>
                        <th order="Fecha" width="50px">Fecha</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2>{{tr.usuario}}</vs-td2>
                            <vs-td2>{{tr.Nombre}}</vs-td2>
                            <vs-td2>{{tr.fecha}}</vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
        </div>
    
    </vx-card>
    </template>
    
    <script>
    import BusquedaAdmision from "/src/components/sermesa/modules/admisiones/BusquedaAdmision.vue"
    export default {
        components: {
            BusquedaAdmision
        },
        data() {
            return {
                serieAdmision: '',
                admision: '',
                nombrePaciente: '',
                //Datos Grids
                devoluciones: [],
                anulaciones: [],
                recalculos: [],
                reactivaciones: [],
                altas: [],
                ajenos: [],
                descuentos: [],
                EntregaEstadoCuenta:[],
                CalculoCobro:[],
                CheckIn:[],
                //Mostrar o no los grids
                datosDevolucion: false,
                datosAnulaciones: false,
                datosRecalculo: false,
                datosReactivaciones: false,
                datosAltas: false,
                datosAjenos: false,
                datosDescuentos: false,
                datosEstadoCuenta: false,
                datosCalculoCobro:false,
                datosCheckIn:false,
            }
    
        },
        methods: {
            DatosAdmision(datos) {
                this.nombrePaciente = datos.Paciente
                this.admision = datos.Codigo
                this.serieAdmision = datos.Serie
                this.Cargar().CargaDevoluciones()
                this.Cargar().BitacoraACPEstadoCuenta()
              
            },
            Cargar() {
                return {
                    CargaDevoluciones: () => {
                        this.axios.post("/app/v1_JefeCaja/CargarDevolucionesAd", {
                                SerieAdmision: this.serieAdmision,
                                Admision: this.admision
                            })
                            .then(resp => {
                                if (resp.data.codigo == '0') {
                                    if (!this.isEmptyObject(resp.data.json)) {
                                        //Mapeo de datos
                                        this.devoluciones = resp.data.json
                                        this.datosDevolucion = true
                                    } else {
                                        this.datosDevolucion = false
                                    }
                                }
                                this.Cargar().CargaAnulaciones()
                            })
                    },
                    CargaAnulaciones: () => {
                        this.axios.post("/app/v1_JefeCaja/CargarAnulaciones", {
                                SerieAdmision: this.serieAdmision,
                                Admision: this.admision
                            })
                            .then(resp => {
                                if (resp.data.codigo == '0') {
                                    if (!this.isEmptyObject(resp.data.json)) {
                                        //Mapeo de datos
                                        this.anulaciones = resp.data.json.map(m => {
                                            this.datosAnulaciones = true
                                            return {
                                                ...m,
                                            }
                                        })
                                    } else {
                                        this.datosAnulaciones = false
                                    }
                                }
                                this.Cargar().CargaRecalculo()
                            })
                    },
                    CargaRecalculo: () => {
                        this.axios.post("/app/v1_JefeCaja/CargarRecalculo", {
                                SerieAdmision: this.serieAdmision,
                                Admision: this.admision
                            })
                            .then(resp => {
                                if (resp.data.codigo == '0') {
                                    if (!this.isEmptyObject(resp.data.json)) {
                                        //Mapeo de datos
                                        this.recalculos = resp.data.json.map(m => {
                                            this.datosRecalculo = true
                                            return {
                                                ...m,
                                            }
                                        })
                                    } else {
                                        this.datosRecalculo = false
                                    }
                                }
                                this.Cargar().CargaReactivaciones()
                            })
                    },
                    CargaReactivaciones: () => {
                        this.axios.post("/app/v1_JefeCaja/CargarReactivaciones", {
                                SerieAdmision: this.serieAdmision,
                                Admision: this.admision
                            })
                            .then(resp => {
                                if (resp.data.codigo == '0') {
                                    if (!this.isEmptyObject(resp.data.json)) {
                                        //Mapeo de datos
                                        this.reactivaciones = resp.data.json.map(m => {
                                            this.datosReactivaciones = true
                                            return {
                                                ...m,
                                            }
                                        })
                                    } else {
                                        this.datosReactivaciones = false
                                    }
                                }
                                this.Cargar().CargaAltas()
                            })
                    },
                    CargaAltas: () => {
                        this.axios.post("/app/v1_JefeCaja/CargarAltas", {
                                SerieAdmision: this.serieAdmision,
                                Admision: this.admision
                            })
                            .then(resp => {
                                if (resp.data.codigo == '0') {
                                    if (!this.isEmptyObject(resp.data.json)) {
                                        //Mapeo de datos
                                        this.altas = resp.data.json.map(m => {
                                            this.datosAltas = true
                                            return {
                                                ...m,
                                            }
                                        })
                                    } else {
                                        this.datosAltas = false
                                    }
                                }
                                this.Cargar().CargaAjenos()
                            })
                    },
                    CargaAjenos: () => {
                        this.axios.post("/app/v1_JefeCaja/CargarFactAjenos", {
                                SerieAdmision: this.serieAdmision,
                                Admision: this.admision
                            })
                            .then(resp => {
                                if (resp.data.codigo == '0') {
                                    if (!this.isEmptyObject(resp.data.json)) {
                                        //Mapeo de datos
                                        this.ajenos = resp.data.json.map(m => {
                                            this.datosAjenos = true
                                            return {
                                                ...m,
                                            }
                                        })
                                    }
                                } else {
                                    this.datosAjenos = false
                                }
                                this.Cargar().CargaDescuentos()
                            })
                    },
                    CargaDescuentos: () => {
                        this.axios.post("/app/v1_JefeCaja/CargarDescuentos", {
                                SerieAdmision: this.serieAdmision,
                                Admision: this.admision
                            })
                            .then(resp => {
                                if (resp.data.codigo == '0') {
                                    if (!this.isEmptyObject(resp.data.json)) {
                                        //Mapeo de datos
                                        this.descuentos = resp.data.json.map(m => {
                                            this.datosDescuentos = true
                                            return {
                                                ...m,
                                            }
                                        })
                                    } else {
                                        this.datosDescuentos = false
                                    }
                                }
                               
                               this.Cargar().BitacoraACPEstadoCuenta()
                               
                            })
                    },
                    BitacoraACPEstadoCuenta: () => {
                        
                        this.axios.post("/app/v1_JefeCaja/BitacoraACP", {
                                Opcion:4,
                                SerieAdmision: this.serieAdmision,
                                Admision: this.admision
                            })
                            .then(resp => {                       
                                if (resp.data.codigo == '0') {
                                    if (!this.isEmptyObject(resp.data.json)) {
                                        //Mapeo de datos
                                        this.EntregaEstadoCuenta = resp.data.json.map(m => {
                                            this.datosEstadoCuenta = true
                                            return {
                                                ...m,
                                            }
                                        })
                                    } else {
                                        this.datosEstadoCuenta = false
                                    }
                                }
                                this.Cargar().BitacoraCalculoCobro()
                            })
                    },
                    BitacoraCalculoCobro: () => {
                        
                        this.axios.post("/app/v1_JefeCaja/BitacoraACP", {
                                Opcion:1,
                                SerieAdmision: this.serieAdmision,
                                Admision: this.admision
                            })
                            .then(resp => {                       
                                if (resp.data.codigo == '0') {
                                    if (!this.isEmptyObject(resp.data.json)) {
                                        //Mapeo de datos
                                        this.CalculoCobro = resp.data.json.map(m => {
                                            this.datosCalculoCobro = true
                                            return {
                                                ...m,
                                            }
                                        })
                                    } else {
                                        this.datosCalculoCobro = false
                                    }
                                }
                                this.Cargar().BitacoraCheckIn()
                            })
                    },
                    BitacoraCheckIn: () => {
                        
                        this.axios.post("/app/v1_JefeCaja/BitacoraACP", {
                                Opcion:3,
                                SerieAdmision: this.serieAdmision,
                                Admision: this.admision
                            })
                            .then(resp => {                       
                                if (resp.data.codigo == '0') {
                                    if (!this.isEmptyObject(resp.data.json)) {
                                        //Mapeo de datos
                                        this.CheckIn = resp.data.json.map(m => {
                                            this.datosCheckIn = true
                                            return {
                                                ...m,
                                            }
                                        })
                                    } else {
                                        this.datosCheckIn = false
                                    }
                                }
                            })
                    }
                }
    
            },
            //LimpiarAdmisiones() {
            //    this.$refs.componenteAdmisiones.limpiar_campos() 
            //},
            LimpiarPantalla() {
                this.serieAdmision = ''
                this.admision = ''
                this.devoluciones = []
                this.anulaciones = []
                this.recalculos = []
                this.reactivaciones = []
                this.altas = []
                this.ajenos = []
                this.descuentos = []
                this.EntregaEstadoCuenta=[]
                this.CalculoCobro=[]
                this.CheckIn=[]
                //Ocultar grid
                this.datosDevolucion = false
                this.datosAnulaciones = false
                this.datosRecalculo = false
                this.datosReactivaciones = false
                this.datosAltas = false
                this.datosAjenos = false
                this.datosDescuentos = false
                this.datosEstadoCuenta= false
                this.datosCalculoCobro = false
                this.datosCheckIn = false
                
            },
    
            //Método para validar si el objeto trae datos (JasonTable)
            isEmptyObject(obj) {
                return Object.keys(obj).length === 0;
            }
        }
    }
    </script>
    
    <style lang="scss" scoped>
    .div-input {
        padding: 10px;
    
        .input-razon {
            width: 200px;
        }
    }
    
    .label-info {
        margin-left: 10px;
        color: #E74C3C;
        opacity: 0.7;
        border: 0;
        font-size: 16px;
        background-color: white;
        font-weight: bold;
    }
    
    .label-staticRef {
        font-weight: bold;
        padding-left: 15px;
    }
    
    .label-static {
        font-weight: bold;
        padding-left: 5px;
    }
    
    .align_rigth {
        text-align: right;
    }
    </style>
    