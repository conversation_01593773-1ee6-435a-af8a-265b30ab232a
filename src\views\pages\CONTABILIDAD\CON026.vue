<template>
    <div>
      <h2>Generación de Reportes</h2>
  
      <ReporteTemplate 
        ref="generadorReporte"
        :ListaReportes="listaReportes"
        :formato="'PDF'"
        :limpiar-pantalla="true" 
        @v-on:click="generarReporte"
      />
    </div>
  </template>
  
<script>
import ReporteTemplate from "../REPORTE/ReporteTemplate.vue";


export default {
  components: {
    ReporteTemplate,
  },
  data() {
    return {
      /*{nombreRpt: 'ReporteBalanceSaldos',Descripcion: 'Balance de Saldos'},
        {nombreRpt: 'ReporteFacturasEnConsignacion',Descripcion: 'Facturas en Consignacion'},
        {nombreRpt: 'ReporteFlujoDeEfectivo',Descripcion: 'Flujo de Efectivo'},
        {nombreRpt: 'ReporteDifAuxiliaresBalance',Descripcion: 'Diferencias Entre Auxiliares y Balance'},
      */

      listaReportes: [
                {nombreRpt: 'ReporteCatalogoCuentas',Descripcion: 'Catálogo de Cuentas'},
                {nombreRpt: 'ReporteBalanceAnalitico', Descripcion: 'Balance Análitico'},
                {nombreRpt: 'ReporteHojaBalanceSaldos',Descripcion: 'Hoja de Balance de Saldos'},
                {nombreRpt: 'ReportePartidasDeDiario',Descripcion: 'Partidas de Diario Detalladas'},
                {nombreRpt: 'ReporteLibroMayor',Descripcion: 'Libro Mayor'},
                {nombreRpt: 'ReporteBalanceGeneral',Descripcion: 'Balance General'},
                {nombreRpt: 'ReporteBalanceGeneralComparativo',Descripcion: 'Balance General Comparativo'},
                {nombreRpt: 'ReporteLibroDiario',Descripcion: 'Libro Diario'},
                {nombreRpt: 'ReportePartidaDiarioGenerada',Descripcion: 'Partida de Diario Generada'},
                {nombreRpt: 'ReportePartidaMensual',Descripcion: 'Partida Mensual'},
                {nombreRpt: 'ReporteEstadoDeResultados',Descripcion: 'Estado de Resultados'},
                {nombreRpt: 'ReporteEstadoDeResultadosComparativo',Descripcion: 'Estado de Resultados Comparativo'}
            ],
    };
  },
  methods: {
    requerirValor(datos, requerido){

      const values = {...datos.opciones}
      const found = Object.hasOwn(values,requerido)

      if (!found) {      
        this.$vs.dialog({                                                  
              acceptText: 'Aceptar',
              text: requerido,
              color: '#ED8C72',              
              title: 'Valor requerido',            
          })
          return false
      }
      return true
    },
    validarValores(datos){      
      let valid = true;
      switch (datos.nombre) {
        case 'ReporteBalanceAnalitico':
        case 'ReporteHojaBalanceSaldos':
        case 'ReporteBalanceSaldos':
        case 'ReportePartidasDeDiario':   		
        case 'ReporteBalanceGeneral':     		
        case 'ReporteLibroDiario':        		
        case 'ReportePartidaDiarioGenerada':    
        case 'ReportePartidaMensual':           
        case 'ReporteBalanceGeneralComparativo':
        case 'ReporteDifAuxiliaresBalance':   

        valid = this.requerirValor(datos, 'Periodo');
                     
          break;     
        case 'ReporteLibroMayor':
        case 'ReporteFlujoDeEfectivo':
        case 'ReporteEstadoDeResultados':
        case 'ReporteEstadoDeResultadosComparativo':
        case 'ReporteFacturasEnConsignacion': 

        valid = this.requerirValor(datos, 'PeriodoInicial');

        break;
        default:
          
      }
      return valid;
    },
    generarReporte(datos) 
    {

       const ref = this.$refs.generadorReporte
      

       if (!this.validarValores(datos)) {         
         return
       }

      if (ref && typeof ref.submitForm === 'function') {
        ref.submitForm(datos.opciones, datos.formato)
      } else {
        return
      }
      
      
      
    }
  },
};
</script>
