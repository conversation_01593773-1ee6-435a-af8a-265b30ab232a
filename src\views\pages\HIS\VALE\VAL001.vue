<template>
    <div class="flex flex-wrap">
      <div class="Encabezado">
        <p>Algo de texto</p>
            <!-- <EncabezadoExpediente ref="EncabezadoExpediente"></EncabezadoExpediente> -->
            <Encabezado2 ref="refEncabezado2" TipoBusqudaPaciente = "TODO" @onLoadedData="onLoadedDataEXP001"></Encabezado2>
        </div>

        <vs-button id="button-with-loading" @click="Metodo()">Limpiar</vs-button>
        <vs-input label="TipoBusqudda" v-model="tipoBuquedaPac" />


        <div>
          <DxDataGrid
          :data-source="Customers"
          key-expr="ID"
          
          :show-borders="true"
          :editing="{allowAdding:true,allowDeleting:false,allowUpdating:true,mode:'form' }"
          
        />
        <!-- <DxEditing
      :allow-adding="true"
      :allow-updating="true"
      :allow-deleting="true"
      mode="row"
    /> -->
        <!-- :columns="columns" -->
        </div>
        
  </div>
</template>
<script>
// import EncabezadoExpediente from '../../EXPEDIENTE/EXP001.vue'
import DxDataGrid from 'devextreme-vue/data-grid';
export default {
  components:{
    // EncabezadoExpediente,
    Encabezado2: ()=> import('./VALENCHerencia.vue'),
    DxDataGrid,
  },
  props:{
    SerieAdmision: String
  },
  methods:{
    Metodo(){
      this.$refs.refEncabezado2.LimpiarAdmision()
    },
    


  },
  data() {
        return {
          tipoBuquedaPac: 'TODO3',
          Customers: [{
            ID: 1,
            Propiedad: 'Saludo',
            CompanyName: 'Super Mart of the West',
            Address: '702 SW 8th Street',
            City: 'Bentonville',
            State: 'Arkansas',
            Zipcode: 72716,
            Phone: '(*************',
            Fax: '(*************',
          }, {
            ID: 2,
            CompanyName: 'Electronics Depot',
            Address: '2455 Paces Ferry Road NW',
            City: 'Atlanta',
            State: 'Georgia',
            Zipcode: 30339,
            Phone: '(*************',
            Fax: '(*************',
          }, {
            ID: 3,
            CompanyName: 'K&S Music',
            Address: '1000 Nicllet Mall',
            City: 'Minneapolis',
            State: 'Minnesota',
            Zipcode: 55403,
            Phone: '(*************',
            Fax: '(*************',
          }, {
            ID: 4,
            CompanyName: "Tom's Club",
            Address: '999 Lake Drive',
            City: 'Issaquah',
            State: 'Washington',
            Zipcode: 98027,
            Phone: '(*************',
            Fax: '(*************',
          }, {
            ID: 5,
            CompanyName: 'E-Mart',
            Address: '3333 Beverly Rd',
            City: 'Hoffman Estates',
            State: 'Illinois',
            Zipcode: 60179,
            Phone: '(*************',
            Fax: '(*************',
          }, {
            ID: 6,
            CompanyName: 'Walters',
            Address: '200 Wilmot Rd',
            City: 'Deerfield',
            State: 'Illinois',
            Zipcode: 60015,
            Phone: '(*************',
            Fax: '(*************',
          }, {
            ID: 7,
            CompanyName: 'StereoShack',
            Address: '400 Commerce S',
            City: 'Fort Worth',
            State: 'Texas',
            Zipcode: 76102,
            Phone: '(*************',
            Fax: '(*************',
          }, {
            ID: 8,
            CompanyName: 'Circuit Town',
            Address: '2200 Kensington Court',
            City: 'Oak Brook',
            State: 'Illinois',
            Zipcode: 60523,
            Phone: '(*************',
            Fax: '(*************',
          }, {
            ID: 9,
            CompanyName: 'Premier Buy',
            Address: '7601 Penn Avenue South',
            City: 'Richfield',
            State: 'Minnesota',
            Zipcode: 55423,
            Phone: '(*************',
            Fax: '(*************',
          }, {
            ID: 10,
            CompanyName: 'ElectrixMax',
            Address: '263 Shuman Blvd',
            City: 'Naperville',
            State: 'Illinois',
            Zipcode: 60563,
            Phone: '(*************',
            Fax: '(*************',
          }, {
            ID: 11,
            CompanyName: 'Video Emporium',
            Address: '1201 Elm Street',
            City: 'Dallas',
            State: 'Texas',
            Zipcode: 75270,
            Phone: '(*************',
            Fax: '(*************',
          }, {
            ID: 12,
            CompanyName: 'Screen Shop',
            Address: '1000 Lowes Blvd',
            City: 'Mooresville',
            State: 'North Carolina',
            Zipcode: 28117,
            Phone: '(*************',
            Fax: '(*************',
          }]
        }
      },
  mounted (){
    this.$refs.refEncabezado2.$refs.busquedaPop.handleSubmit = (e) => {
          e.preventDefault()
              this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaPacientesAdmision', {
                      NombrePaciente: this.formulario.Nombre,
                  })
                  .then(resp => {
                      this.pacientes = resp.data.json.map((x) => {
                          return {
                              ...x,
                              Admision: x.SerieAdmision + x.Admision,
                          }
                      })
                      this.buscoConExpediente = false
                  })
        }
  },
}

</script>


