<template>
<vx-card :title="(!buscar)?'Pacientes':null">
    <div v-if="buscar">
        <div class="flex flex-wrap" style="padding:10px 20px">
            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                <vs-input label="Nombres" class="w-full" v-on:change="info.nombre1=info.nombre1.toUpperCase()" v-model="info.nombre1" />
            </div>
            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                <vs-input label="Apellidos" class="w-full" v-on:change="info.apellido1=info.apellido1.toUpperCase()" v-model="info.apellido1" />
            </div>
            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <vs-input label="Fecha de Nacimiento" class="w-full" v-on:change="info.apellido2=info.apellido2.toUpperCase()" v-model="info.apellido2" />
            </div>
            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <vs-input label="DPI" class="w-full" v-on:change="info.apellido2=info.apellido2.toUpperCase()" v-model="info.apellido2" />
            </div>
            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <vs-input label="Pasaporte" class="w-full" v-on:change="info.apellido2=info.apellido2.toUpperCase()" v-model="info.apellido2" />
            </div>
            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
            </div>
            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <label class="vs-input--label"></label>
                <vs-button id="button-with-loading" color="primary" class="w-full" icon-pack="feather" icon="icon-search">Buscar</vs-button>
            </div>
        </div>
        <hr>
         <vs-table :data="buscador_listado">
            <template slot="thead">
                <vs-th>Primer Nombre</vs-th>
                <vs-th>Segundo Nombre</vs-th>
                <vs-th>Primer Apellido</vs-th>
                <vs-th>Segundo Apellido</vs-th>
                <vs-th>Fecha de Nacimiento</vs-th>
                <vs-th>DPI</vs-th>
            </template>
            <template slot-scope="{data}">
                <vs-tr :key="indextr" v-for="(tr, indextr) in data">
                    <vs-td :data="tr.examen_nombre">
                        [{{ tr.examen_codigo }}] {{ tr.examen_nombre }}
                    </vs-td>
                    <vs-td :data="tr.especialista_nombre">
                        <span v-if="tr.especialista_codigo">[{{ tr.especialista_codigo }}] {{ tr.especialista_nombre }}</span>
                    </vs-td>
                    <vs-td>
                        <vx-tooltip :text="(tr.congelado==false)?'Descongelado':'Congelado'" style="display:inline-block;text-align:center;margin-right:2px;width:20px" position="bottom">
                            <i v-if="tr.congelado" style="color:#2ecc71" class="fas fa-lock"></i>
                            <i v-else style="color:#e74c3c" class="fas fa-lock-open"></i>
                        </vx-tooltip>
                        <vx-tooltip :text="(tr.sms==false)?'Entrega Personalmente':'Envío SMS'" style="display:inline-block;text-align:center;margin-right:2px;width:20px" position="bottom">
                            <i v-if="tr.sms" style="color:#2ecc71" class="fas fa-comment"></i>
                            <i v-else style="color:#e74c3c" class="fas fa-comment-slash"></i>
                        </vx-tooltip>
                    </vs-td>
                    <vs-td>
                        <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" @click="opciones(tr)"></vs-button>

                        <!-- <a :href="'http:\\srvpacs001\explore_v5.asp?path=/SynapseURL/AccessionNumber=RA1-289830-1'" target="_blank"> -->
                        <a :href="'http://srvpacs001/explore_v5.asp?path=/SynapseURL/AccessionNumber='+info.orden_tipo+'-'+info.orden_numero+'-'+tr.examen_linea" target="_blank" style="display:inline-block;">
                            <vs-button style="padding:10px 15px;" icon-pack="fas" icon="fa-image"></vs-button>
                        </a>
                        <vs-button style="margin-left:1px;display:inline-block;" :disabled="!tr.congelado" v-on:click="radiologia_reporte(tr.examen_linea)" v-if="bloqueoBusqueda" icon-pack="fas" icon="fa-print"></vs-button>
                    </vs-td>
                </vs-tr>
            </template>
        </vs-table>
        <hr>
        <br>
        <vs-button id="button-with-loading" color="primary" disabled style="float:right" type="filled" icon-pack="feather" icon="icon-plus">Crear Paciente</vs-button>
    </div>
    <div v-else>
        <div class="flex flex-wrap" style="padding:10px 20px" v-if="!nuevo">
            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">

                <label class="vs-input--label">Código</label>
                <vx-input-group class="">
                    <vs-input v-model="info.codigo" disabled />
                    <template slot="append">
                        <div class="append-text btn-addon">
                            <button type="submit" v-show="false" name="button"></button>
                            <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_radiologias()" icon="icon-search"></vs-button>
                            <vs-button color="success" icon-pack="feather" @click="info.paciente_mostrar=true" icon="icon-plus"></vs-button>
                        </div>
                    </template>
                </vx-input-group>
            </div>
        </div>

        <vs-tabs position="left" color="success">
            <vs-tab label="Información Básica" icon-pack="fas" icon="fa-info-circle">
                <h5 style="float:left">Información Básica</h5><br>
                <div class="flex flex-wrap" style="padding:10px 20px">
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <label class="vs-input--label">Paciente Inválido</label>
                        <vs-switch v-model="info.invalido" style="height: 23px;" />
                    </div>
                </div>

                <div class="flex flex-wrap" style="padding:10px 20px">
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <vs-input label="Primer Nombre *" class="w-full" v-on:change="info.nombre1=info.nombre1.toUpperCase()" v-model="info.nombre1" />
                    </div>
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <vs-input label="Segundo Nombre" class="w-full" v-on:change="info.nombres2=info.nombres2.toUpperCase()" v-model="info.nombres2" />
                    </div>
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <vs-input label="Primer Apellido *" class="w-full" v-on:change="info.apellido1=info.apellido1.toUpperCase()" v-model="info.apellido1" />
                    </div>
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <vs-input label="Segundo Apellido" class="w-full" v-on:change="info.apellido2=info.apellido2.toUpperCase()" v-model="info.apellido2" />
                    </div>
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <vs-input label="Apellido Casada" class="w-full" v-on:change="info.casada=info.casada.toUpperCase()" v-model="info.casada" />
                    </div>
                </div>
                <div class="flex flex-wrap" style="padding:10px 20px">
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Fecha de Nacimiento *" v-mask="'##/##/####'" class="w-full" v-model="info.nacimiento" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Edad" class="w-full" v-model="info.edad" disabled />
                    </div>
                </div>
                <div class="flex flex-wrap" style="padding:10px 20px">
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="DPI" v-mask="'####-#####-####'" class="w-full" v-model="info.dpi" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Pasaporte" class="w-full" v-on:change="info.pasaporte=info.pasaporte.toUpperCase()" v-model="info.pasaporte" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Celular *" class="w-full" v-model="info.celular" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Teléfono" v-mask="'####-####'" class="w-full" v-model="info.telefono" />
                    </div>

                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <label class="vs-input--label">Estado Civil *</label>
                        <multiselect v-model="info.estado_civil" :options="EstadosCiviles" :disabled="false" :allow-empty="false" placeholder="Seleccione el estado civil" track-by="Nombre" label="Nombre"></multiselect>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <label class="vs-input--label">Sexo *</label>
                        <ul class="centerx center">
                            <li>
                                <vs-radio v-model="info.sexo" vs-value="Hombre">Hombre</vs-radio>
                            </li>
                            <li>
                                <vs-radio v-model="info.sexo" vs-value="Mujer">Mujer</vs-radio>
                            </li>
                            <li class="modelx">
                                {{ info.sexo }}
                            </li>
                        </ul>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Religion" class="w-full" v-model="info.religion" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Nacionalidad" class="w-full" v-model="info.nacionalidad" />
                    </div>
                    <div class="w-full md:w-3/3 lg:w-3/3 xl:w-3/3">
                        <vs-input label="Lugar Nacimiento" class="w-full" v-model="info.lugar_nacimiento" />
                    </div>

                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                        <label class="vs-input--label">No tiene correo:</label>
                        <vs-switch v-model="info.invalido" style="height: 23px; margin-top:7px" />
                    </div>
                    <div class="w-full md:w-4/5 lg:w-4/5 xl:w-4/5">

                        <vs-input label="Correo Electronico" class="w-full" v-model="info.email" />
                    </div>

                </div>
            </vs-tab>
            <!--  -->
            <vs-tab label="Dirección" icon-pack="fas" icon="fa-route">
                <h5 style="float:left">Dirección</h5><br>
                <div class="flex flex-wrap" style="padding:10px 20px;margin-bottom:200px">
                    <div class="w-full md:w-5/5 lg:w-5/5 xl:w-5/5">
                        <vs-input label="Dirección" class="w-full" v-on:change="info.direccion=info.direccion.toUpperCase()" v-model="info.direccion" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Ciudad" class="w-full" v-on:change="info.calle=info.calle.toUpperCase()" v-model="info.ciudad" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Calle" class="w-full" v-on:change="info.calle=info.calle.toUpperCase()" v-model="info.calle" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Avenida" class="w-full" v-on:change="info.avenida=info.avenida.toUpperCase()" v-model="info.avenida" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="No. de Casa / Apartamento" class="w-full" v-on:change="info.casa=info.casa.toUpperCase()" v-model="info.casa" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Zona" class="w-full" v-on:change="info.zona=info.zona.toUpperCase()" v-model="info.zona" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Colonia / Residencial" class="w-full" v-on:change="info.colonia=info.colonia.toUpperCase()" v-model="info.colonia" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Edificio" class="w-full" v-on:change="info.edificio=info.edificio.toUpperCase()" v-model="info.edificio" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3" style="POSITION:RELATIVE">
                        <label class="vs-input--label">Departamento</label>
                        <multiselect style="position:RELATIVE;z-index:99999" v-model="info.departamento" :options="Tipos" :disabled="false" :allow-empty="false" placeholder="Seleccione  departamento" track-by="Nombre" label="Nombre"></multiselect>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <label class="vs-input--label">Municipio</label>
                        <multiselect v-model="info.municipio" :options="Tipos" :disabled="false" :allow-empty="false" placeholder="Seleccione el municipio" track-by="Nombre" label="Nombre"></multiselect>
                    </div>
                </div>
            </vs-tab>

            <vs-tab label="Facturación" icon-pack="fas" icon="fa-cash-register">
                <h5 style="float:left">Facturación</h5><br>
                <div class="flex flex-wrap" style="padding:10px 20px">

                    <div class="w-full md:w-2/3 lg:w-2/3 xl:w-2/3">
                        <vs-input label="Nombre Factura" class="w-full" v-on:change="info.nombre_factura=info.nombre_factura.toUpperCase()" v-model="info.nombre_factura" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="NIT" class="w-full" v-on:change="info.nit=info.nit.toUpperCase()" v-model="info.nit" />
                    </div>
                    <div class="w-full md:w-5/5 lg:w-5/5 xl:w-5/5">
                        <vs-input label="Dirección Factura" class="w-full" v-on:change="info.direccion_factura=info.direccion_factura.toUpperCase()" v-model="info.direccion_factura" />
                    </div>
                </div>

            </vs-tab>

            <vs-tab label="Documentación" icon-pack="far" icon="fa-file-alt">
                <h5 style="float:left">Documentación</h5><br>
                <div class="flex flex-wrap" style="padding:10px 20px">
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Profesión" class="w-full" v-model="info.profesion" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Carnet" class="w-full" v-on:change="info.carnet=info.carnet.toUpperCase()" v-model="info.carnet" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Socio" class="w-full" v-on:change="info.socio=info.socio.toUpperCase()" v-model="info.socio" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Seguro Social" class="w-full" v-on:change="info.segurosocial=info.segurosocial.toUpperCase()" v-model="info.segurosocial" />
                    </div>
                </div>

            </vs-tab>

            <vs-tab label="Trabajo" icon-pack="far" icon="fa-building">
                <h5 style="float:left">Trabajo</h5><br>
                <div class="flex flex-wrap" style="padding:10px 20px">

                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Ocupación" class="w-full" v-model="info.ocupacion" />
                    </div>

                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Calle:" class="w-full" v-model="info.CalleTrabajo" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Avenida/Manzana:" class="w-full" v-model="info.AvenidaTrabajo" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="No. Casa o Apto:" class="w-full" v-model="info.NoCasaTrabajo" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Zona:" class="w-full" v-model="info.ZonaTrabajo" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Colonia:" class="w-full" v-model="info.ColoniaTrabajo" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Edificio:" class="w-full" v-model="info.EdificioTrabajo" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="No. de Oficina" class="w-full" v-model="info.OficinaTrabajo" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Departamento:" class="w-full" v-model="info.DeptoTrabajo" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Municipio:" class="w-full" v-model="info.MunicipioTrabajo" />
                    </div>
                </div>
            </vs-tab>

            <vs-tab label="Parientes" icon-pack="fas" icon="fa-users">
                <h5 style="float:left">Parientes</h5><br>

                <div class="flex flex-wrap" style="padding:10px 20px">
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Padre" class="w-full" v-on:change="info.padre=info.padre.toUpperCase()" v-model="info.padre" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="DPI" class="w-full" v-on:change="info.madre=info.madre.toUpperCase()" v-model="info.madre" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Teléfono" class="w-full" v-on:change="info.conyuge=info.conyuge.toUpperCase()" v-model="info.conyuge" />
                    </div>
                </div>
                <div class="flex flex-wrap" style="padding:10px 20px">
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Madre" class="w-full" v-on:change="info.padre=info.padre.toUpperCase()" v-model="info.padre" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="DPI" class="w-full" v-on:change="info.madre=info.madre.toUpperCase()" v-model="info.madre" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                        <vs-input label="Teléfono" class="w-full" v-on:change="info.conyuge=info.conyuge.toUpperCase()" v-model="info.conyuge" />
                    </div>
                </div>

            </vs-tab>

            <!-- <vs-tab label="Dirección">
            <h5 style="float:left">Dirección</h5><br>

        </vs-tab> -->
        </vs-tabs>

        <vs-divider></vs-divider>
        <vs-button style="float:right" @click="guardar_informe(true)"> Guardar</vs-button>
        <!-- <vs-button color="primary" style="float:right" type="border" @click="info.opcion.show=false" v-else> Salir</vs-button> -->
        <div style="clear:both"></div>
    </div>

</vx-card>
</template>

<script>
import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";

export default {
    data() {
        return {
            examenes: [],
            buscador_listado:[],
            info: {
                invalido: false,
                nombre1: null,
                nombre2: null,
                apellido1: null,
                apellido2: null,
                casada: null,
                nacimiento: null,
                edad: null,
                dpi: null,
                telefono: null,
                sexo: null,
                estado_civil: null,
                religion: null,
                lugar_nacimiento: null,
                nacionalidad: null,
                telefonos: null,
                celular: null,
                email: null,

                profesion: null,

                direccion_factura: null,
                nombre_factura: null,
                nit: null,

                carnet: null,
                socio: null,
                pasaporte: null,
                segurosocial: null,

                direccion: null,
                ciudad: null,
                calle: null,
                avenida: null,
                casa: null,
                zona: null,
                colonia: null,
                edificio: null,
                departamento: null,
                municipio: null,

                direccion_trabajo: null,
                telefono_trabajo: null,
                ocupacion: null,
                CalleTrabajo: null,
                AvenidaTrabajo: null,
                NoCasaTrabajo: null,
                ZonaTrabajo: null,
                ColoniaTrabajo: null,
                EdificioTrabajo: null,
                OficinaTrabajo: null,
                DeptoTrabajo: null,
                MunicipioTrabajo: null,

                padre: null,
                madre: null,
                conyuge: null,
            },
            Tipos: [{
                    Id: 1,
                    Nombre: 'Interno'
                },
                {
                    Id: 2,
                    Nombre: 'Emergencia'
                },
                {
                    Id: 3,
                    Nombre: 'IGSS'
                },
                {
                    Id: 4,
                    Nombre: 'RN en Hospital'
                },
                {
                    Id: 5,
                    Nombre: 'Paquete'
                },
            ],
            EstadosCiviles: [{
                    Id: 'C',
                    Nombre: 'Casado(a)'
                },
                {
                    Id: 'D',
                    Nombre: 'Divorsiado(a)'
                },
                {
                    Id: 'S',
                    Nombre: 'Soltero(a)'
                },
                {
                    Id: 'U',
                    Nombre: 'Unido(a)'
                },
                {
                    Id: 'V',
                    Nombre: 'Viudo(a)'
                },
            ]
        }
    },
    props: {
        paciente: {
            default: null
        },
        nuevo: {
            default: false
        },
        buscar: {
            default: false
        }
    },
    components: {
        Multiselect
    },
    methods: {

        cargar_estudios() {
            this.axios.post('/app/admision/Medicamento', {
                    Corporativo: 0
                })
                .then(resp => {
                    
                    this.examenes = []
                    resp.data.json.map(data => {
                        this.examenes.push({
                            ControlMedicamento: data.ControlMedicamento,
                            Admision: data.Admision,
                            Codigo: data.Codigo,
                            FechaAsignada: data.FechaAsignada,
                            Frecuencia: data.Frecuencia,
                            DIAS: data.DIAS,
                            Serie: data.Serie,
                            Paciente: data.Paciente,
                            Tipo: data.tipo
                        })
                    })

                })
        }
    },
    created() {

    }
}
</script>

<style scoped>
.center li {
    display: inline !important;
    padding: 2px 10px;
}

.button {
    margin: 0px !important;
}

.btnx {
    margin-left: 10px !important;
    background: red;
    border-radius: 5px 0px 0px 5px;
}

.btn-drop {
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: red;

}

.menu-funcionalidad {
    /* padding-top:0 !important; */
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    padding: 15px;
    display: inline;
    float: right;
    border-radius: 10px;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 10px;
}

.menu-funcionalidad td {
    text-align: center;
    padding-right: 10px;
}

.vs-switch {
    height: 30px;
    width: 60px !important;
}

.vs-circle-switch {
    height: 25px;
    width: 25px;
}

.vs-switch--icon {
    font-size: 15px
}

.vs-switch.vs-switch-active .vs-switch--circle {
    margin-left: calc(100% - 30px);
}
</style>
