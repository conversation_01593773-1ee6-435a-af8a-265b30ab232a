<template>
  <div>
    <div class="padre">
      <div>
        <vx-card :title="'Cambio de Categoría'">
          <form>
            <div style="display: flex;">
              <div class="Izquierda">
                    <div style="display: flex;"> 
                      <div style=" margin-right: 5px;">
                      <ValidationProvider rules="required" v-slot="{ errors }">
                        <vs-input v-model="info.Serie" class="w-full" :danger="errors.length > 0 " placeholder="Serie" style="width: 100px !important"
                          :danger-text="(errors.length > 0) ? errors[0] : null"  />
                      </ValidationProvider>
                    </div>

                    <div style="margin-bottom: 15px; margin-right: 5px;">
                      <vx-input-group>
                        <ValidationProvider rules="required" v-slot="{ errors }">
                          <vs-input v-model="info.CodigoAdmision"  @keydown="handleTabKey"    placeholder="Admisión" style="width: 160px !important"
                          @keyup.enter="handleSearch" class="w-full"
                            :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                               />
                        </ValidationProvider>
                        <template slot="append">
                          <div class="append-text btn-addon">
                            <vs-button v-if="!info.Serie && !info.CodigoAdmision && !isSearching"
                              id="button-with-loading" :disabled="!info.Serie && !info.CodigoAdmision && !isSearching"
                              color="primary" icon-pack="feather" icon="icon-search"></vs-button>
                            <vs-button v-else id="button-with-locking" @click="LimpiarCampos()" color="danger"
                              icon-pack="feather" icon="icon-delete"></vs-button>
                          </div>
                        </template>
                      </vx-input-group>
                    </div>
                    <div style=" margin-right: 5px;">
                      <vs-button color="primary" @click="handleSearch()">Buscar</vs-button>
                    </div>
                  </div>
                  <div>
                    <div style="display: flex;">
                      <div style=" margin-right: 5px;">
                      <ValidationProvider name="Categoria" rules="required" v-slot="{ errors }">
                        <vs-input 
                          placeholder="Categoria" type="number"   style="width: 100px !important"
                         :step="5" 
                          v-model="info.Categoria" :danger="errors.length > 0"
                          :danger-text="(errors.length > 0) ? errors[0] : null" />
                      </ValidationProvider>
                    </div>

                    <div>
                      <vs-button color="primary" :disabled="info.CodigoAdmision == null" icon-pack="feather"
                        icon="icon-save" @click="actualizarCargos()">Cambiar</vs-button>
                    </div>
                    
                    </div>
                    <br>
                    <vs-textarea class="sm:w-full md:w-full lg:w-full xl:w-1/1 m-1" :label="desc[0]" counter="200" style="background-color: white;" v-model="info.observaciones" />
                    
                  </div>
              </div>
              <div class="Derecha" v-if="info.paciente"  >
                <div style="display:flex;">  
                  <div >
                    <div class="notificacion2">
                        <i class="fas fa-heartbeat"></i>
                        <div class="titulo">AFILIADO</div>
                    </div>
                  </div>
                  <div style="margin-top: 25px;">
                    <div class="w-full sm:w-1/1 p-1">
                      <label style="font-size:medium; font-family: sans-serif;">Paciente:</label>
                      &nbsp;&nbsp;
                      <label style="color:black;">{{ info.paciente }}</label>
                    </div>
                    <div class="w-full sm:w-1/1 p-1">
                      <label style="font-size:medium;font-family: sans-serif;" >Aseguradora:</label>
                      &nbsp;&nbsp;
                      <label style="color: black">{{ info.NombreAseguradora }}</label>
                    </div>
                    <div class="w-full sm:w-1/1 p-1">
                      <label  style="font-size:medium;font-family: sans-serif;">Seguros:</label>
                      &nbsp;&nbsp;
                      <label style="color: black">{{ info.NombreSeguro }}</label>
                    </div>
                    <div class="w-full sm:w-1/1 p-1">
                      <label  style="font-size:medium;font-family: sans-serif;">Copago:</label>
                      &nbsp;
                      <label style="color: black">{{ info.Copago }}</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
          <div>
          </div>
        </vx-card>
      </div>
      <div class="Derecha">
        <vx-card  >
          <div class="flex flex-wrap">
            <div class="xl:w-full lg:w-full md:w-full sm:w-full p-2" >
                <vs-table2 max-items="10" class="OverFlow colors" tooltip filter :data="cargos" noSelectText>
                      <!--<vs-table2 max-items="10" search tooltip filter pagination :data="tabla" noSelectText> -->
                      <template slot="thead">
                          <th width="50px"></th>
                          <th order="linea" width="50px" filtro="linea">Linea</th>
                          <th order="TipoOrden" width="10px" filtro="TipoOrden">Orden</th>
                          <th order="Orden" width="100px" filtro="Orden">Orden</th>
                          <th order="Fecha" width="120px" filtro="Fecha">Fecha</th>
                          <th order="Categoria" width="50px" filtro="Categoria">Categoria</th>
                          <th order="Producto" width="100px" filtro="Producto">Código</th>
                          <th order="NombreProducto" width="300px" filtro="NombreProducto">Producto</th>
                          <th order="Cantidad" width="50px" filtro="Cantidad">Cantidad</th>
                          <th order="Valor" width="50px" filtro="Valor">Valor</th>
                          <th order="NombreCategoria" width="50px" filtro="NombreCategoria">Categoría</th>
                          <th width="50px" >Acciones</th>
                      </template>
      
                      <template slot-scope="{data}">
                          <tr :key="indextr" v-for="(tr, indextr) in data" :ref="`list_${indextr}`" @contextmenu="Menu(tr)" @dblclick="Menu(tr)" @click="Menu(null)" :class="{ 'activo': tr.activo }">
                              <vs-td2>
                                <vs-checkbox
                                  color="success"
                                  v-model="tr.Autorizado"
                                  @change="handleCheckboxChange(tr)"
                                ></vs-checkbox>
                              </vs-td2>
                              <vs-td2>
                                  {{ tr.linea }} 
                              </vs-td2>
      
                              <vs-td2>  
                                  {{ tr.TipoOrden }}
                              </vs-td2>
      
                              <vs-td2>
                                  {{ tr.Orden }}
                              </vs-td2>
      
                              <vs-td2>
                                  {{ tr.Fecha }}
                              </vs-td2>
                              <vs-td2>
                                  {{ tr.Categoria }}
                              </vs-td2>  
                              <vs-td2>
                                  {{ tr.Producto }}
                              </vs-td2>  
                              <vs-td2>
                                  {{ tr.NombreProducto }}
                              </vs-td2>  
                              <vs-td2>
                                  {{ tr.Cantidad }}
                              </vs-td2>  
                              <vs-td2>
                                  {{ tr.Valor }}
                              </vs-td2> 
                              <vs-td2>
                                  {{ tr.NombreCategoria }}
                              </vs-td2>      
                              <div style="display: flex">

                              <vs-button color="warning" @click="Regresion().RegresionCategoria(tr)" icon-pack="feather"
                                :disabled="tr.CategoriaValida == ''" icon="icon-refresh-ccw"></vs-button>
                              </div>
                          </tr>
                      </template>
                  </vs-table2>  
            </div>
          </div>
        </vx-card>
      </div>
    </div>
  </div>
</template>

<script>

export default {

  data() {
    return {

      info: {
        Empresa: null,
        Serie: null,
        CodigoAdmision: null,
        TotalCargos: '',
        TipoOrden: null,
        Orden: null,
        Categoria: null,
        cCopago: null,
        cCoaseguro: null,
        msjExito: null,
        CodigoProducto: null,
        CategoriaActual: null,
        //Datos Paciente
        paciente: null,
        NombreAseguradora: null,
        NombreSeguro: null,
        Copago: null,
        observaciones: null
      },


      inputDisabled: false,
      isSearching: false,

      textarea: '',
      counterDanger: false,
      //Observaciones
      desc: [
        "Motivo",
        "Descripción"
      ],

      //Cantenido Actualizar 
      selectedItems: [],
      // Cargos Admisión 
      cargos: [],


    };
  },
  computed: {
    sesion() {
      return this.$store.state.sesion
    },
    global() {
      return this.$store.state.global
    }
  },

  methods: {
    // ========================================================== 
    //  ACCION DE CONSULTA    
    // ==========================================================
    handleSearch() {
      this.isSearching = true;
      this.Consulta().consultaAutorizaciones()
    },
    handleTabKey(event) {
      if (event.key === 'Tab') {
        this.performActionOnTab(); 
      }
    },
    performActionOnTab() {
      this.Consulta().consultaAutorizaciones()
    },
    Menu(item) {
                // console.log(item)
                this.cargos.map(m => m.activo = false)
                if (item) item.activo = true
    },
    // ========================================================== 
    //  CONSULTA    
    // ==========================================================
    Consulta: function () {
      return {
        consultaAutorizaciones: () => {
          this.axios.post('/app/v1_autorizaciones/ConsultasAutorizaciones', {
            Operacion: 'CONSULTA',
            Serie: this.info.Serie,
            CodigoAdmision: this.info.CodigoAdmision
          })
            .then(resp => {
              if (resp.data.json) {
                this.$map({
                  objeto: this.info,
                  respuestaAxios: resp,

                });
                // Llamar al método ConsultaCargosAutorizaciones después de completar la consulta de autorizaciones
                this.ConsultaCargos().consultaAutorizacionesCargos();
              }
            })
        }
      };
    },
    // ========================================================== 
    //  CONSULTA CARGOS UPDATE
    // ==========================================================
    ConsultaCargosUpdate: function () {
      return {
        consultaAutorizacionesCargosUpdate: () => {
          this.axios.post('/app/v1_autorizaciones/ConsultasAutorizaciones', {
            Operacion: 'CONSULTACARGOS',
            Serie: this.info.Serie,
            CodigoAdmision: this.info.CodigoAdmision
          })
            .then(resp => {
              if (resp.data.codigo == '0') {
                this.cargos = resp.data.json;
              }
            })
        }
      };
    },
    // ========================================================== 
    //  CONSULTA CARGOS   
    // ==========================================================
    ConsultaCargos: function () {
      return {
        consultaAutorizacionesCargos: () => {
          this.axios.post('/app/v1_autorizaciones/ConsultasAutorizaciones', {
            Operacion: 'CONSULTACARGOS',
            Serie: this.info.Serie,
            CodigoAdmision: this.info.CodigoAdmision
          })
            .then(resp => {
              if (resp.data.codigo == '0') {
                  if (resp.data.json == null || resp.data.json == undefined || resp.data.json.length == 0) {
                    // Mostrar notificación indicando que 'res.data.json' está vacío
                    this.$vs.notify({
                      title: 'ADVERTENCIA!',
                      text: 'Esta Admisión no tiene Cargos',
                      iconPack: 'feather',
                      icon: 'icon-alert-circle',
                      color: 'danger'
                    })
                  } else {
                    this.cargos = resp.data.json;
                    this.$vs.notify({
                      title: 'NOTIFICACION!',
                      text: 'Datos Cargados!',
                      iconPack: 'feather',
                      icon: 'icon-alert-circle',
                      color: 'success'
                    })
                  }
              }
            })
        }
      };
    },
    // ========================================================== 
    //  CONSULTA  CATEGORIAS
    // ==========================================================
    ConsultaCategorias: function () {
      return {
        consultacategoriasprueba: () => {
          this.axios.post('/app/v1_autorizaciones/ConsultasAutorizaciones', {
            Operacion: 'BUSQUEDACATEGORIA',
          })
            .then(resp => {
              if (resp.data.codigo == '0') {
                this.categoria = resp.data.json
                  .map((categoria) => ({
                    Codigo: categoria.codigo,
                    Nombre: categoria.Nombre
                  }));
              }
            })
        }
      }
    },
    // ========================================================== 
    //  ACTUALIZAR CATEGORIA   
    // ==========================================================
  handleCheckboxChange(row) {
      // No inicializar selectedItems aquí, ya que esto lo reinicia
      if (row.Autorizado) {
        if (!this.selectedItems.includes(row)) {
          this.selectedItems.push(row);

          // Obtener las dos primeras letras de TipoOrden
          const tipoOrdenPrefix = row.TipoOrden.substring(0, 2);

          // Verificar si las dos primeras letras coinciden con RA o LA
          if (['RA', 'LA'].includes(tipoOrdenPrefix)) {
            // Mostrar notificación con Swal
            this.$vs.dialog({
              type: 'alert',
              color: '#5dade2',
              title: 'ADVERTENCIA!',
              acceptText: 'Aceptar',
              text: `Verificar que no tenga informe antes de cambiar la categoría: ${row.TipoOrden + '-' + row.Orden}`,
              accept: () => {},
            })
          }

          // Validación adicional para la línea
          if (row.Linea && row.Linea !== 'EsperadoValorDeLinea') {
            this.$vs.dialog({
                type: 'alert',
                color: '#E74C3C', // Color rojo similar al de Swal
                title: 'Error',
                text: `La línea ${row.Linea} no es válida para esta operación.`,
                acceptText: 'Aceptar',
                buttonCancel: 'border',
                accept: () => {}
            });


            // Remover de selectedItems si la línea no es válida
            const index = this.selectedItems.indexOf(row);
            if (index !== -1) {
              this.selectedItems.splice(index, 1);
            }
            return; // Detener el flujo si la línea no es válida
          }
        }
      } else {
        const index = this.selectedItems.indexOf(row);
        if (index !== -1) {
          this.selectedItems.splice(index, 1);
        }
      }
      this.$forceUpdate();
    },
    
    actualizarCargos() {
        this.axios.post('/app/v1_autorizaciones/UpdateCategoria', {
          Operacion: 'CATEGORIA',
          Serie: this.info.Serie,
          CodigoAdmision: this.info.CodigoAdmision,
          Categoria: this.info.Categoria,
          Observaciones: this.info.observaciones,
          Cargos: this.selectedItems // Usar selectedItems aquí
          
        }).then((resp) => {
          if(resp.data.codigo == '0'){
            this.ConsultaCargosUpdate().consultaAutorizacionesCargosUpdate();
            this.Limpiaritem();
          }
        }).catch((error) => {
          if(error) {
            this.$vs.notify({
                time: 400,
                title: 'Error',
                text: 'No se puedo cambiar la categoria',
                iconPack: 'feather',
                icon: 'icon-alert-circle',
                color: 'danger',
                position: 'bottom-center'
            })
          }
        });
      },
    // ========================================================== 
    //  REGRESION CATEGORIA   
    // ==========================================================
    Regresion: function () {
      return {
        RegresionCategoria: (valor) => {
          this.axios.post('/app/v1_autorizaciones/RegresionCategoria', {
            Operacion: 'REGRESION',
            Serie: this.info.Serie,
            CodigoAdmision: this.info.CodigoAdmision,
            TipoOrden: valor.TipoOrden,
            CodigoProducto: valor.Producto,
            Orden: valor.Orden,
            Categoria: valor.Categoria
          })
            .then(resp => {
              if (resp.data.codigo == '0') {
                this.info.Categoria = valor.Categoria
                this.ConsultaCargosUpdate().consultaAutorizacionesCargosUpdate();
                this.Limpiaritem();
              }
            })
        }
      }
    },
    // ========================================================== 
    //  LIMPIAR DATOS   
    // ==========================================================
    LimpiarCampos() {
      this.info.Serie = null
      this.info.CodigoAdmision = null
      this.info.Categoria = null
      this.info.paciente = null
      this.info.NombreAseguradora = null
      this.info.NombreSeguro = null
      this.info.Copago = null
      this.cargos = []
      this.isSearching = false
      this.selectedItems = []
      this.$vs.notify({
        title: 'NOTIFICACION!',
        text: 'Datos Limpiados!',
        iconPack: 'feather',
        icon: 'icon-alert-circle',
        color: 'warning'
      })
    },
    // ========================================================== 
    //  LIMPIAR ITEM   
    // ==========================================================
    Limpiaritem() {
      this.info.Categoria = null
      this.info.observaciones = null
      this.selectedItems = []
    }
  },
}

</script>

<style scoped>
.Izquierda_Dos {
  width: 0%;
  padding: 5px;
  border-radius: 5px;

}



.notificacion2 {
    text-align: center;
    font-size: 70px;
    cursor: pointer;
    position: relative;
    margin: 20px;
}

.notificacion2:hover {
    color: black;
}

.notificacion2 .titulo {
    font-weight: bold;
    font-size: 20px;
    position: relative;
    top: -5px;
}

.Izquierda {

  width: 50%;
  padding: 5px;
  border-radius: 5px;
}

.Derecha {
  width: 100%;
  padding: 5px;
  border-radius: 5px;
}


</style>
<style>
.OverFlow .contenedor-tabla
{
  overflow-y: auto !important;
  max-height: 300px !important; 
}

</style>