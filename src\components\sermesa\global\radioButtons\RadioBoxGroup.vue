



<script>
import BaseRadionButtonGroup from "./BaseRadioButtonGroup.vue";
export default {
  name: 'RadioBoxGroup',
  components: {

  },
  extends: BaseRadionButtonGroup,

  inheritAttrs: false,
  beforeMount() {
  },
  mounted() {
    this.$el.classList.add('radioBox')
    this.$el.classList.add('radio-box')
  },
  methods: {
    getData(data) {
      this.onRadioSelected(data)
    }
  }
}


</script>


<style>
.radio-box {
  margin-top: 0px;
}



.radio-box label {
  margin-right: 10px;
  display: inline-block;
  cursor: pointer;
  padding: 5px;
}

.radioBox span {
  display: block;
  padding: 15px 80px 15px 45px;
  border: 2px solid #ddd;
  border-radius: 5px;
  position: relative;
  transition: all 0.25s linear;
}

.radioBox span:before {
  content: '';
  position: absolute;
  left: 5px;
  top: 50%;
  -webkit-transform: translatey(-50%);
  transform: translatey(-50%);
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #ddd;
  transition: all 0.25s linear;
}

.radio-box input {
  display: none;
}

.radioBox input:checked+span {
  background-color: #fff;
  box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.1);
}

.radioBox input:checked+span {
  color: rgb(96, 162, 203);
  border-color: rgb(143, 143, 160);
}

.radioBox input:checked+span:before {
  background-color: rgb(98, 155, 209);
}

.radio-box.radioBox .radio-container {
  display: flex;
  flex-direction: column;
}
</style>