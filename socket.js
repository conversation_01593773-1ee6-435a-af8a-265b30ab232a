module.exports.iniciar = function (app) {
    const http = require('http').createServer(app);
    var io = require('socket.io')(http);
    var uniqid = require('uniqid');



    // listado de usuarios conectados
    var arr_users = []
    // listado de app conectados
    var arr_app = []

    // =============================================================================================================================
    //  ESTADO DE CONEXION
    // =============================================================================================================================
    io.on('connection', function (socket) {
        socket.on('disconnect', function () {
            console.log('[-] ' + ((socket.user && socket.user.usuario) ? socket.user.usuario : ' No determinado'));

            // eliminando usuario del listado
            if (socket.user) delete arr_users[socket.user.ip]

            //enviando la información al dashboard
            if (socket.user) io.to('dashboard').emit('usuario_desconectado', socket.user);

            if (socket.user && socket.user.tipo == 'appPC') io.to(arr_users[socket.user.ip]).emit('desconexion_appPC');
        });

        socket.on('registrar_usuario', function (msg) {
            if (typeof msg != 'object') msg = JSON.parse(msg)
            if (!socket.user || msg.dashboard) {

                //   registrando usuarios
                if (!msg.dashboard) {
                    socket.user = {
                        uid: uniqid(),
                        corporativo: msg.corporativo,
                        usuario: msg.usuario,
                        ip: msg.ip,
                        tipo: ((msg.tipo == '') ? 'usuario' : msg.tipo)
                    }

                    if (msg.usuario) arr_users[msg.ip] = socket
                    if (!msg.usuario) arr_app[msg.ip] = socket.id

                    if (msg.tipo != 'appPC') io.to((arr_users[msg.ip]) ? arr_users[msg.ip].id : null).emit('conexion_usuario');
                    if (msg.tipo == 'appPC') io.to((arr_users[msg.ip]) ? arr_users[msg.ip].id : null).emit('conexion_appPC', msg);

                    // mensaje de usuarios conectados
                    console.log('[+] [' + msg.ip + '] ' + msg.corporativo + ' / ' + ((!msg.tipo || msg.tipo == 'usuario') ? socket.user.usuario : msg.tipo));

                    // enviando la señal de nuevo usuarios
                    io.to('dashboard').emit('usuario_conectado', socket.user);

                } else {
                    // estos usuarios se les envia el listado de todos los usuarios conectados
                    // console.log('asosiando dashboard')
                    socket.join('dashboard');
                    let usuarios_listado = []
                    Object.keys(arr_users).map((objectKey) => {
                        var usuarios = arr_users[objectKey];
                        if (usuarios.user) usuarios_listado.push(usuarios.user)
                        return true
                    });
                    io.to('dashboard').emit('usuario_conectado_listado', usuarios_listado);
                }


            }
        });

        // utilizado para determinar el tiempo de respuesta de un api
        socket.on('usuario_request', function (msg) {
            io.to('dashboard').emit('usuario_request', {
                ...msg,
                uid: (socket.user) ? socket.user.uid : null
            });
        })

        socket.on('usuario_response', function (msg) {
            io.to('dashboard').emit('usuario_response', {
                ...msg,
                uid: (socket.user) ? socket.user.uid : null
            });
        })

        //utilizado para conocer la url donde se encuentra el usuario
        socket.on('usuario_ubicacion', function (msg) {
            io.to('dashboard').emit('usuario_ubicacion', {
                ...msg,
                uid: (socket.user) ? socket.user.uid : null
            });
        })



        // =============================================================================================================================
        //   AppPC
        // =============================================================================================================================
        socket.on("estado", () => {
            io.to(arr_app[socket.user.ip]).emit('estado');
        })

        socket.on("resultado_estado", (data) => {
            let arr = JSON.parse(data)
            io.to(arr_users[arr.ip].id).emit('resultado_estado', arr);
        })


        socket.on("actualizar", () => {

            io.to(arr_app[socket.user.ip]).emit('actualizar');
        })

        socket.on("resultado_actualizar", (data) => {
            let arr = JSON.parse(data)
            io.to(arr_users[arr.ip].id).emit('resultado_estado', arr);
        })




        // =============================================================================================================================
        //   visor de radiología
        // =============================================================================================================================
        socket.on("radiologia_visor", (data) => {
            io.to(arr_app[socket.user.ip]).emit('cmd_radiologia_visor', data.url + " " + data.tipo + " " + data.orden + " SI NO " + data.url_actualiza + "");
        })

        // =============================================================================================================================
        //   llamada a escanear
        // =============================================================================================================================
        socket.on("scanner", (data) => {
            //io.to(arr_app[socket.user.ip]).emit('scanner', {
            console.log(data)
            io.to(arr_app['**************']).emit('scanner', {
                socket: socket.id,
                quality: data.quality
            });
        })
        //respuesta escaner
        socket.on("scanner_result", (data) => {
            let d = JSON.parse(data);
            // io.to(arr_users[d.ip]).emit('scanner_result', d.data);
            io.to(arr_users['***************']).emit('scanner_result', d.data);
        })

        // =============================================================================================================================
        //  CHAT
        // =============================================================================================================================
        socket.on('chat_message', function (msg) {
            io.emit('chat_message', msg);
        });

        socket.on('chat_usuario_nuevo', function () {
            io.emit('chat_usuario_nuevo', socket.user);

        });

        socket.on('chat_usuario_salir', function () {
            io.emit('chat_usuario_salir', socket.user);
        });
    });

    http.listen(3001, function () {
        console.log('listening on *:3001');
    });

}
