<template>
<vx-card title="Filtro de Pago de Interpretaciones">
    <Nuevo :nuevo="permisos.nuevo" :polizas="listadoPolizas" :callback="Consulta().Filtros" :tipoAdmision="tipoAdmision" />

    <vs-popup ref="buscador" title="Editar Filtro" :active.sync="editarFiltro.mostrar" style="z-index:52000" id="div-with-loading" class="vs-con-loading__container">
        <Nuevo :nuevo="permisos.nuevo" :polizas="listadoPolizas" :callback="Consulta().Filtros" :tipoAdmision="tipoAdmision" :editarFiltro.sync="editarFiltro" />
    </vs-popup>

    <vs-popup ref="buscador" title="Reglas de Pago" :active.sync="reglaFiltro.mostrar" style="z-index:52000" id="div-with-loading" class="vs-con-loading__container">
        <Reglas :callback="Consulta().Filtros" :polizas="listadoPolizas" :reglaFiltro.sync="reglaFiltro" />
    </vs-popup>

    <vs-divider></vs-divider>
    <vs-table2 max-items="10" pagination search :data="filtros" tooltip>
        <template slot="thead">
            <th width="110px">Tipo Admisión</th>
            <th>Descripción</th>
            <th width="100px">Polizas Asociadas</th>
            <th width="100px">Regla de Pago</th>
            <th width="100px">Activo</th>
            <th width="130px">Acciones</th>
        </template>
        <template slot-scope="{data}">
            <tr :key="indextr" v-for="(tr, indextr) in data" :is-selected="tr.Seleccion" not-click-selected>
                <vs-td2 width="90px">
                    {{ (tr.TipoAdmision==1)?'Privada':'Seguro' }}
                </vs-td2>
                <vs-td2>
                    {{ tr.Descripcion }}
                </vs-td2>

                <vs-td2 width="100px" style="text-align:right" noTooltip>
                    <div class="badge" v-if="tr.TipoAdmision==2">
                        {{ tr.Polizas }}
                    </div>
                </vs-td2>

                <vs-td2>
                    <vs-checkbox class="tabla" :value="tr.Reglas>0" disabled />
                </vs-td2>

                <vs-td2 noTooltip>
                    <vs-switch v-model="tr.Estado" @click="(permisos.deshabilitar)?Editar().Estado(tr):null" style="height: 23px;" :disabled="!permisos.deshabilitar" />
                </vs-td2>

                <vs-td2 width="150px">
                    <vs-button :disabled="!permisos.reglas" color="success" size="small" icon-pack="far" icon="fa-money-bill-alt" class="mr-1" style="display:inline-block" v-on:click="(permisos.reglas)?Otros().reglas(tr):null"></vs-button>
                    <vs-button :disabled="!permisos.editar_poliza" color="primary" size="small" icon-pack="fas" icon="fa-edit" class="mr-1" style="display:inline-block" v-on:click="(permisos.editar_poliza)?Otros().editar(tr):null"></vs-button>
                    <!-- <vs-button :disabled="!permisos.deshabilitar" color="danger" size="small" icon-pack="far" icon="fa-trash-alt" style="display:inline-block" v-on:click="(permisos.deshabilitar)?Eliminar().filtro(tr):null"></vs-button> -->
                </vs-td2>
            </tr>
        </template>
    </vs-table2>

</vx-card>
</template>

<script>
export default {
    data() {
        return {
            /**
             * Documentación del módulo
             */
            doc_:[
                'Permite indicar los tipos de admisión y los seguros que seran pagados como interpretaciones.',
                'Se debe especificar una regla de pago para poder tomar la interpretación como valída.',
                'Los filtros y las reglas de pago no son retroactivas.',
                'Todos las interpretaciones que cuenten con un filtro y regla de pago se visualizaran en el módulo <b>Planilla de Honorarios</b>.',
                '* Se obtienen los datos del sp <i>sp_Ajenos_Honorarios_FiltroInterpretacion_Consulta</i>.' ,
            ],

            tipoAdmision: [{
                Codigo: '1',
                Nombre: 'Privada'
                }, {
                Codigo: '2',
                Nombre: 'Seguro'
            }],

            valor: null,

            selected: [],

            listadoPolizas: [],

            /**
             * Información de los tabla
             */
            filtros: [],
            tablaSeleccion: [],

            /**
             * Editar Poliza
             */
            editarFiltro: {
                mostrar: false,
                info: null
            },

            /*
             * Editar Poliza
             */
            reglaFiltro: {
                mostrar: false,
                info: null
            },

            /**
             * Listado de permisos
             */
            permisos: {
                nuevo: false,
                deshabilitar: false,
                editar_poliza: false,
                reglas: false
            }
        }
    },
    components: {
        Nuevo: () => import('./AJE010_Nuevo'),
        Reglas: () => import('./AJE003_Reglas')
    },
    computed: {

    },
    methods: {
        //=======================================================================================================
        // CONSULTA
        //=======================================================================================================
        Consulta: function() {
            return {
                Filtros: () => {
                    this.filtros = []
                    
                    return this.axios.post('/app/ajenos/Filtros_Interpretacion_ListadoFiltros', {})
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.filtros = resp.data.json.map(m => {
                                    return {
                                        ...m,
                                        Estado: (m.Estado == 'A') ? true : false
                                    }
                                })
                            }
                            
                        })
                        .catch(() => {
                            
                        })
                },
                Polizas: () => {
                    return this.axios.post('/app/ajenos/FiltrosCargosListadoPolizas', {})
                        .then(resp => {
                            
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.listadoPolizas = resp.data.json.map(m => {
                                    return {
                                        ...m,
                                        Seleccionar: false
                                    }
                                })
                            }
                        })
                        .catch(() => {
                            
                        })
                },

            }
        },

        //=======================================================================================================
        // NUEVO
        //=======================================================================================================
        Nuevo: function() {
            return {}
        },

        //=======================================================================================================
        // GUARDAR
        //=======================================================================================================
        Guardar: function() {
            return {
                filtro: () => {
                    
                }
            }
        },

        //=======================================================================================================
        // EDITAR
        //=======================================================================================================
        Editar: function() {
            return {
                Estado: (item) => {
                    if (!item.Estado == false) {
                        this.$vs.dialog({
                            type: 'confirm',
                            color: 'danger',
                            title: 'Confirmación',
                            acceptText: 'Deshabilitar',
                            cancelText: 'Cancelar',
                            text: `¿Desea deshabilitar el filtro de interpretación?`,
                            accept: () => {
                                this.axios.post('/app/ajenos/FiltrosInterpretacionEstado', {
                                        idFiltro: item.IdFiltro,
                                        Estado: 'I'
                                    })
                                    .then(() => {
                                        this.Consulta().Filtros()
                                    })
                            },
                            cancel: () => {
                                console.log(item)
                                setTimeout(() => {
                                    item.Estado = true
                                }, 100)
                            }
                        })
                    } else {
                        this.axios.post('/app/ajenos/FiltrosInterpretacionEstado', {
                                idFiltro: item.IdFiltro,
                                Estado: 'A'
                            })
                            .catch(() => {
                                item.Estado = false
                            })
                    }
                }
            }
        },

        //=======================================================================================================
        // ELIMINAR
        //=======================================================================================================
        Eliminar: function() {
            return {}
        },

        //=======================================================================================================
        // OTROS
        //=======================================================================================================
        Otros: function() {
            return {
                Limpiar_Datos: () => {
                    Object.entries(this.info).forEach(([key]) => {
                        this.info[key] = null
                    });
                },
                filtrarSeleccionados: () => {
                    this.poliza.filtrarSeleccionados = !this.poliza.filtrarSeleccionados;
                    this.poliza.pagina = null;
                    setTimeout(() => {
                        this.poliza.pagina = 1
                    }, 1000)
                },
                editar: (item) => {
                    this.editarFiltro.mostrar = true;
                    this.editarFiltro.info = item
                },
                reglas: (item) => {
                    this.reglaFiltro.mostrar = true;
                    this.reglaFiltro.info = item
                }
            }
        }
    },
    mounted() {
        this.Consulta().Filtros()
        this.Consulta().Polizas()

        this.permisos.nuevo = this.$validar_privilegio('NUEVO').status
        this.permisos.deshabilitar = this.$validar_privilegio('DESHABILITAR').status
        this.permisos.editar_poliza = this.$validar_privilegio('EDITAR_POLIZA').status
        this.permisos.reglas = this.$validar_privilegio('REGLAS').status
    }
}
</script>

<style>
.badge {
    /* border:1px solid #ccc; */
    border-radius: 10px;
    text-align: center;
    color: white;
    background-color: rgba(var(--vs-primary), 1);
    padding: 4px 15px;
    font-size: 12px;
    display: inline;
}
</style>
