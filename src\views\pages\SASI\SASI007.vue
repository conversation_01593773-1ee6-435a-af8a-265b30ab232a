<template>
<div>
    <div class="principal">

        <vs-tabs v-model="activeTab" alignment="center">

            <!--=========================================================================================================================
            ESTRUCTURA SOLICITUDES     
            ==========================================================================================================================-->
            <vs-tab label="Solicitudes"  icon="account_balance"  @click="Consulta().ConsultaSolicitud();colorx ='#FFA500'">
                <div class="con-tab-ejemplo">
                    <vx-card :title="'Solicitudes Diagnóstico'">
                        <div>
                            <vs-button color="success" @click="Consulta().ConsultaSolicitud()" type="filled">Actualizar</vs-button>

                        </div>
                        <br />
                        <div>
                            <vs-table2 max-items="10" pagination :data="Solicitudes" class="mt-0 mb-0">
                                <template slot="thead">
                                    <th width="125px">No.Solicitud Laboratorio</th>
                                    <th width="225px">Fecha Solicitud</th>
                                    <th width="250px">Código Afiliafo</th>
                                    <th width="400px">Paciente</th>
                                    <th width="150px">Trabajado Por Corporativo </th>
                                    <th>Trabajado Por Nombre </th>
                                    <th>Detalle</th>
                                </template>
                                <template slot-scope="{ data }">
                                    <tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td2><label>{{ tr.idSolicitud }}</label></vs-td2>
                                        <vs-td2><label>{{ tr.Column1 }}</label></vs-td2>
                                        <vs-td2><label>{{ tr.Afiliado }}</label></vs-td2>
                                        <vs-td2><label>{{ tr.nombre }}</label></vs-td2>
                                        <vs-td2><label>{{ tr.CorporativoTrabajando }}</label></vs-td2>
                                        <vs-td2><label>{{ tr.NombreAutoriza }}</label></vs-td2>
                                        <vs-td2>
                                            <div style="display: flex;">
                                                <vs-tooltip text="Iniciar Solicitud">
                                                    <button class="BotonInicio" @click="ConsultaGeneral().EjecutarConsultas(tr) || changeTab(1);ConsultarSerieAdmision(tr);"><i class="fas fa-sign-out-alt"></i></button>
                                                </vs-tooltip>
                                            </div>
                                        </vs-td2>
                                    </tr>
                                </template>
                            </vs-table2>
                        </div>
                    </vx-card>
                </div>

                <vs-popup title="Información Previa" :active.sync="popupActivoprueba">
                    <vx-card>
                        <div class="Contenido" style="display: flex">

                        </div>
                    </vx-card>
                    <!--Plan Medico-->
                    <vx-card style="margin-top: 5px;" title="Plan Medico">
                        <div class="con-tab-ejemplo">
                            <vs-table2 style="height: 150px !important" max-items="2" pagination :data="plan" class="mt-0 mb-0 OverFlow">
                                <template slot="thead">
                                    <th width="200px">Afiliado</th>
                                    <th width="300px">Plan</th>
                                    <th width="400px">Contrato</th>
                                    <th width="110px">F.Inicio</th>
                                    <th width="25px">Meses.Cob</th>
                                    <th width="130px">M.Vitalicio</th>
                                    <th width="130px">M.Anual</th>
                                    <th width="130px">M.Evento</th>
                                    <th width="110px">Fecha Nacimiento</th>
                                    <th width="75x">Edad</th>
                                </template>
                                <template slot-scope="{ data }">
                                    <tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td2><label>{{ tr.afiliado }}</label></vs-td2>
                                        <vs-td2><label>{{ tr.plan_nom }}</label></vs-td2>
                                        <vs-td2><label>{{ tr.contrato_nom }} </label></vs-td2>
                                        <vs-td2><label>{{ tr.fecha_inicio }}</label></vs-td2>
                                        <vs-td2><label>{{ tr.meses_vigencia }}</label></vs-td2>
                                        <vs-td2><label>{{ tr.MaximoVitalicio }}</label></vs-td2>
                                        <vs-td2><label>{{ tr.MaximoAnual }}</label></vs-td2>
                                        <vs-td2><label>{{ tr.MaximoEvento }}</label></vs-td2>
                                        <vs-td2><label>{{ tr.fecha_nacimiento }}</label></vs-td2>
                                        <vs-td2><label>{{ tr.edad }}</label></vs-td2>
                                    </tr>
                                </template>
                            </vs-table2>
                        </div>
                    </vx-card>
                    <!--Beneficios-->
                    <vx-card style="margin-top: 5px" title="Beneficios">
                        <div class="con-tab-ejemplo">
                            <vs-table2 max-items="5" pagination search :data="Beneficios" class="mt-0 mb-0">
                                <template slot="thead">
                                    <th>Descripción</th>
                                    <th>B / E</th>
                                    <th>Meses Carencia</th>
                                    <th>Limite Veces</th>
                                    <th>Limite Montos</th>
                                </template>
                                <template slot-scope="{ data }">
                                    <tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td2><label>{{ tr.descripcion }}</label></vs-td2>
                                        <vs-td2><label>{{ tr.beneficio }}</label></vs-td2>
                                        <vs-td2><label>{{ tr.periodoesperameses }}</label></vs-td2>
                                        <vs-td2><label>{{ tr.limiteveces }}</label></vs-td2>
                                        <vs-td2><label> {{ tr.limitemonto }}</label></vs-td2>
                                    </tr>
                                </template>
                            </vs-table2>
                        </div>
                    </vx-card>

                </vs-popup>
            </vs-tab>

            <!--=========================================================================================================================
            ESTRUCTURA SOLICITUD 
            ==========================================================================================================================-->
            <vs-tab  icon="edit" label="Detalle Cargos" >
                <div>

                    <div v-show="mostrarDiv">
                        <vx-card>
                            <!--Diagnosticos-->
                            <div class="Derecha">

                                <vs-divider position="left" background="primary" color="#ade6d4"><label style="color: white">Datos de Solicitud Autorización</label></vs-divider>
                                <vs-alert v-if="!DatosAdmision.Paciente && IdSolicitudSelec" title="Paciente no cuenta con perfil 'Paciente' en afiliados, necesario para crear admisiones" active="true" color="danger">

                                    <vs-button @click="()=> $nextTick(() => popupClientePaciente = true)" > 
                                    <font-awesome-icon :icon="['fas', 'handshake']" />
                                        Asociación Cliente-Paciente
                                    </vs-button>

                                </vs-alert>
                                <label>Afiliado:</label>&nbsp;<label style="color: black">{{ info.idAdiliado }}</label>&nbsp;&nbsp; <label>Paciente:</label>&nbsp;<label style="color: black">{{ info.Paciente }}</label>&nbsp;&nbsp; <label>IdCliente:</label>&nbsp;<label style="color: black">{{ info.idCliente }}</label>&nbsp;&nbsp; <label>K:</label>&nbsp;<label style="color: black">{{ info.idContrato }}</label>
                                <div v-if="DatosAdmision.Seguro !='' ">
                                    <label>Aseguradora:</label>&nbsp;<label style="color: black">{{ DatosAdmision.Aseguradora }}</label>&nbsp;&nbsp; <label>Póliza:</label>&nbsp;<label style="color: black">{{ DatosAdmision.Seguro }}</label>&nbsp;&nbsp; <label> Nivel Precio :</label>&nbsp;<label style="color: black">{{ DatosAdmision.NivelPrecioSasi }}</label>
                                </div>
                                <label>Privada - Nivel Precio:</label>&nbsp;<label style="color: black">{{ DatosAdmision.NivelPrecios }}</label>
                                <br>
                                <label>Serie Admisión:</label>&nbsp;<label style="color: black">{{ DatosAdmision.Serie }}</label>&nbsp;

                            </div>
                            <br>
                        </vx-card>

                        <vx-card style="margin-top: 5px">
                            <div class="continer" style="display: flex">
                                <div class="Izquierda2">
                                    <vs-table2 :data="Autorizaciones" max-items="3" class="mt-0 mb-0 OverFlow">
                                        <template slot="thead">
                                            <th>Aut</th>
                                            <th width="100px">Código</th>
                                            <th width="300px">Producto</th>
                                            <th>W</th>
                                            <th>Precio</th>
                                            <th>Detalle</th>
                                            <th>Tipo Pago</th>
                                        </template>
                                        <template slot-scope="{ data }">
                                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                                <vs-td2>
                                                    <vs-checkbox v-model="tr.seleccion" color="success" @change="AutorizarRechazarEstudio(tr)"></vs-checkbox>
                                                </vs-td2>
                                                <vs-td2>
                                                    <label>{{ tr.Producto }} </label></vs-td2>
                                                <vs-td2>
                                                    <label>{{ tr.NombreProducto }} </label></vs-td2>
                                                <vs-td2>
                                                    <label>{{ tr.warning }} </label></vs-td2>
                                                <vs-td2>
                                                    <label>{{ tr.Precio }} </label></vs-td2>

                                                <vs-td2>
                                                    <label>{{ tr.DefEstado }} </label>
                                                </vs-td2>
                                                <vs-td2 v-if="tr.status_analista == 'D'">
                                                    <label> Privado </label>
                                                </vs-td2>
                                                <vs-td2 v-else>
                                                    <label> Seguro </label></vs-td2>
                                            </tr>
                                        </template>
                                    </vs-table2>
                                </div>
                                <div class="Derecha2">

                                    <!--Observaciones -->
                                    <div style="display: auto">
                                        <vs-textarea label="Diagnóstico" disabled v-model="DatosAdmision.InfoDiagnostico" class="sm:w-full md:w-full lg:w-full xl:w-1/1 m-2" counter="600" />
                                        <br />

                                        <vs-textarea label="Observaciones SASI " disabled  v-model="DatosAdmision.Notas" class="sm:w-full md:w-full lg:w-full xl:w-1/1 m-2" counter="200" />
                                    </div>

                                    <div style="display: flex;margin-top: 30px; ">

                                        <vs-button icon="save" @click="PreEstadoCuenta(IdSolicitudSelec) " color="success">PreEstadoCuenta</vs-button>&nbsp;

                                        <vs-button icon="add" @click="InsertarAdmisionOrdenes() " color="warning">Crear Admisión</vs-button>&nbsp;


                                    </div>
                                </div>
                            </div>
                        </vx-card>
                    </div>

                </div>
            </vs-tab>
        </vs-tabs>
    </div>
    <vs-popup class="sasi007" title="Relación Clientes-Pacientes" :active.sync="popupClientePaciente">
        <ClientePaciente v-if="popupClientePaciente" :datos-cliente="clientePacienteProps" :load-on-mount="true" @asociated="onClientePacienteChanged" @paciente-created="onClientePacienteChanged"/>
    </vs-popup>

                
</div>
</template>

<script>
import 'devextreme-vue/lookup'
import 'devextreme-vue/text-area'
import ClientePaciente from '@/components/sermesa/modules/paciente/ClientePaciente.vue'
export default {
    components: {
        ClientePaciente,//: () => import('@/components/sermesa/modules/paciente/ClientePaciente.vue')
    },
    data() {
        return {
            colorx: '#00C853',
            IdSolicitudSelec: '',
            SolicitudSeleccionada: null,
            // =========================================================================================================================
            //  DATOS
            // =========================================================================================================================
            //--Cambio de Tabs--// 
            activeTab: 0,
            numberOfTabs: 2,

            //--Operacion--// 
            total: 0,
            declinado: 0,
            totalGeneral: 0,

            //--Activacion Popup--//
            popupActivo: false,
            popupActivoMedico: false,
            popupActivoSinestrilidad: false,
            popupActivoListodoCupones: false,
            popupActivoAunacion: false,
            popupEilimarCupon: false,
            popupActivoprueba: false,
            popupClientePaciente: false,

            //--Selecciones--//
            selectedMotivo: null,
            selectedMotivo2: null,
            selectedAction: null,
            select4: 2,

            //--Datos Extra--//
            showCard: false,
            number0: 0.00,
            isButtonDisabled: false, // Variable para controlar el estado del botón

            //Dataos Para Cupon
            MontoSiniestralidad: "150",
            isActive: false,

            //Datos 
            mostrarLaboratorio: false,
            mostrarExamenes: false,

            mostrarDiv: true,

            info: {
                //DAtos Afiliado1
                idSolicitud: null,
                idAdiliado: null,
                idPaciente: null,
                Paciente: null,
                idCliente: null,
                idContrato: null,

                //Datos Solicitud
                FechaIngreso: null,
                NombreMedico: null,
                medico: null,
                observacion: null,
                idCita: null,
                //ConsultaMedicoCupon
                Nombre_Medico: null,
                ApellidoMedico: null,
                Especialidad: null,
                //NumeroAdhesion
                NumeroAdhesion: null,
                NombrePlan: null,
                CodigoPlan: null,

                //Datos Cupon
                NUMERO: null,
                motivo_declinacion: null,
                ObsercacionesAuto: null,
                ObsercacionesCupon: null
            },

            //Datos tablas
            Solicitudes: [],
            Beneficios: [],
            Autorizaciones: [],
            Diagnosticos: [],
            plan: [],
            ResultadosMedico: [],
            CuponGenerado: [],
            ListadoCupones: [],
            selectedItems: [],

            no_declinacion: 5,

            options4: [{
                    text: 'Inactivar',
                    value: 'I'
                },
                {
                    text: 'Eliminar',
                    value: 'E'
                }
            ],

            MedicoSelecionado: {
                Codigo: "",
                Nombre: "",
                Especialidad: "",
                Direccion: "",
            },

            DatosListadoCupones: {
                idAfiliado: "",
                NombreAfiliado: "",
                NombrePlan: "",
            },

            permisos: {
                Habilitar: false,
            },

            Restricciones: {
                CitasAcumuladas: "",
                CitasAnuales: "",
                CitasAutorizadas: "",
                PermiteCita: "",
                Permitidas: "",
                Restriccion: "",
                ValidaCitas: "",
            },

            Cuponedit: {
                cupon: "",
            },

            DatosAdmision: {
                Serie: '',
                Paciente: '',
                IdCliente: '',
                NivelPrecios: 0,
                TipoDescuento: 'S',
                Seguro: '',
                NombreFactura: '',
                Nit: '',
                DireccionFactura: '',
                Medico: 0,
                Otros: '',
                PacienteBuscoHospital: 'S',
                IdAfiliado: '',
                PaqueteQx: false,
                DPI: '',
                TipoAdmision: 2,
                CodigoPaqueteBebe: 0,
                PrecioPaquete: 0,
                FechaNacimiento: '',
                Aseguradora: '',
                NivelPrecioSasi: '',
                InfoDiagnostico: ''
            },
            DatosAdmisionPrivada: {
                Serie: '',
                Paciente: '',
                IdCliente: '',
                NivelPrecios: 0,
                TipoDescuento: 'S',
                Seguro: '',
                NombreFactura: '',
                Nit: '',
                DireccionFactura: '',
                Medico: 0,
                Otros: '',
                PacienteBuscoHospital: 'S',
                IdAfiliado: '',
                PaqueteQx: false,
                DPI: '',
                TipoAdmision: 2,
                CodigoPaqueteBebe: 0,
                PrecioPaquete: 0,
                FechaNacimiento: '',
                Aseguradora: '',
                NivelPrecioSasi: ''
            },
            AdmisionPrivadaExterna: [],
            AdmisionSeguroExterna: [],
            AdmisionPrivada: '',
            AdmisionSeguro: '',

        };

    },

    computed: {
        currentDate() {
            return new Date().toISOString().substring(0, 10);
        },

        sesion() {
            return this.$store.state.sesion

        },

        global() {
            return this.$store.state.global
        },

        clientePacienteProps() {
            const element = this.Solicitudes.find(x=>x.idSolicitud == this.IdSolicitudSelec)
            return { 
                IdCliente: element?.idCliente.replace(/[\s]+/g, ""), 
                IdAfiliado: element?.Afiliado.replace(/[\s]+/g, ""), 
            }
        },
    },

    watch: {
        Autorizaciones: {
            handler() {
                this.calcularTotales();
            },
        },
    },

    methods: {
        ConsultarSerieAdmision(tr) {
            //Consultar Serie y Nivel Precio Privada
            this.DatosAdmision.Medico = tr.medico; // resp.data.json.map(m =>  m.Serie )               
            this.axios.post('/app/v1_admision/BusquedaSerieAdmision', {
                Empresa: '',
                Codigo: '',
                TipoAdmision: 2

            }).then(resp => {
                this.DatosAdmision.NivelPrecios = resp.data.json.length > 0 ? resp.data.json[0].NivelDePrecios : null; // resp.data.json.map(m =>  m.NivelDePrecios )
                this.DatosAdmision.Serie = resp.data.json.length > 0 ? resp.data.json[0].Serie : null; // resp.data.json.map(m =>  m.Serie )               

            })

        },
        // CrearAdmision() {
        //     this.AdmisionSeguroExterna = this.Autorizaciones.filter(f => f.seleccion == 1 && f.status_analista == 'A')
        //     this.AdmisionPrivadaExterna = this.Autorizaciones.filter(f => f.seleccion == 1 && f.status_analista == 'D')

        InsertarAdmisionOrdenes() {
            if (!this.ValidarPaciente())
                return

            this.AdmisionSeguroExterna = this.Autorizaciones.filter(f => f.seleccion == 1 && f.status_analista == 'A')
            this.AdmisionPrivadaExterna = this.Autorizaciones.filter(f => f.seleccion == 1 && f.status_analista == 'D')

            if (this.AdmisionSeguroExterna.length > 0) {

                this.CrearAdmisionSeguro()
                    .then((resp) => {

                        if (resp.data.codigo == 0) {

                            if (this.AdmisionPrivadaExterna.length > 0) {
                                /***********CREACION ADMISION PRIVADA **** */
                                this.CrearAdmisionPrivada().then((resp) => {

                                    if (resp.data.codigo == 0) {
                                        this.$vs.dialog({
                                            type: 'confirm',
                                            color: 'dark',
                                            title: 'Admisión:',
                                            text: 'Admisión de Aseguradora: ' + this.AdmisionSeguro + " /  Admisión Privada: " + this.AdmisionPrivada,
                                            acceptText: 'Continuar',
                                            cancelText: 'Cancelar',
                                            clientWidth: 100,
                                            accept: () => {
                                                this.AdmisionSeguro = '';
                                                this.AdmisionPrivada = '';
                                            },
                                            cancel: () => {
                                                this.AdmisionSeguro = '';
                                                this.AdmisionPrivada = '';
                                            }
                                        })
                                        // this.AdmisionSeguro = 'N/A';
                                        // this.AdmisionPrivada = 'N/A';
                                        this.activeTab = 0;
                                        this.Consulta().ConsultaSolicitud();
                                    } else {

                                        this.$vs.dialog({
                                            type: 'confirm',
                                            color: 'dark',
                                            title: 'Admisión:',
                                            text: 'Admisión de Aseguradora: ' + this.AdmisionSeguro + " /  Admisión Privada: Error",
                                            acceptText: 'Continuar',
                                            cancelText: 'Cancelar',
                                            clientWidth: 100,
                                            accept: () => {
                                                this.AdmisionSeguro = '';
                                                this.AdmisionPrivada = '';
                                            },
                                            cancel: () => {
                                                this.AdmisionSeguro = '';
                                                this.AdmisionPrivada = '';
                                            }
                                        })
                                        // this.AdmisionSeguro = 'N/A';
                                        // this.AdmisionPrivada = 'N/A';
                                        //this.activeTab = 0;
                                        //this.Consulta().ConsultaSolicitud();

                                    }

                                })
                                // .catch(
                                //     this.$vs.dialog({
                                //                 type: 'confirm',
                                //                 color: 'dark',
                                //                 title: 'Admisión:',
                                //                 text: 'Admisión de Aseguradora: ' + this.AdmisionSeguro + " //  Admisión Privada: Error"  ,
                                //                 acceptText: 'Continuar',
                                //                 cancelText: 'Cancelar',
                                //                 clientWidth: 100,
                                //                 accept: () => {},
                                //                 cancel: () => {}
                                //             })

                                // )

                                /***********             FIN          **** */

                            } else {

                                this.$vs.dialog({
                                    type: 'confirm',
                                    color: 'dark',
                                    title: 'Admisión:',
                                    text: 'Admisión de Aseguradora: ' + this.AdmisionSeguro,
                                    acceptText: 'Continuar',
                                    cancelText: 'Cancelar',
                                    clientWidth: 100,
                                    accept: () => {
                                        this.AdmisionSeguro = '';
                                        this.AdmisionPrivada = '';
                                    },
                                    cancel: () => {
                                        this.AdmisionSeguro = '';
                                        this.AdmisionPrivada = '';
                                    }
                                })
                                // this.AdmisionSeguro = 'N/A';
                                // this.AdmisionPrivada = 'N/A';
                                //actualiza la pantalla principal
                                this.activeTab = 0;
                                this.Consulta().ConsultaSolicitud();
                                return;
                            }

                        }

                    });
            } else if (this.AdmisionPrivadaExterna.length > 0) {
                /***********CREACION ADMISION PRIVADA **** */
                this.CrearAdmisionPrivada().then((resp) => {
                    if (resp.data.codigo == 0) {
                        this.$vs.dialog({
                            type: 'confirm',
                            color: 'dark',
                            title: 'Admisión:',
                            text: 'Admisión Privada:' + this.AdmisionPrivada,
                            acceptText: 'Continuar',
                            cancelText: 'Cancelar',
                            clientWidth: 100,
                            accept: () => {
                                this.AdmisionSeguro = '';
                                this.AdmisionPrivada = '';
                            },
                            cancel: () => {
                                this.AdmisionSeguro = '';
                                this.AdmisionPrivada = '';
                            }
                        })
                        this.activeTab = 0;
                        this.Consulta().ConsultaSolicitud();
                    }
                })
                /***********             FIN          **** */
            }
        },
        CrearAdmisionSeguro() {
            /***********************************ADMISION SEGURO ****************************/
            return new Promise((resolve, reject) => {

                //Creación Admision Seguro
                this.axios.post('/app/v1_admision/CrearAdmision', {
                        Serie: this.DatosAdmision.Serie,
                        Paciente: this.DatosAdmision.Paciente,
                        IdCliente: this.DatosAdmision.IdCliente,
                        NivelPrecios: this.DatosAdmision.NivelPrecioSasi,
                        TipoDescuento: 'S',
                        Seguro: this.DatosAdmision.Seguro,
                        NombreFactura: this.DatosAdmision.NombreFactura,
                        Nit: this.DatosAdmision.Nit,
                        DireccionFactura: this.DatosAdmision.DireccionFactura,
                        Medico: null, //this.DatosAdmision.Medico,
                        Otros: this.DatosAdmision.Otros,
                        PacienteBuscoHospital: 'S',
                        IdAfiliado: this.DatosAdmision.IdAfiliado,
                        PaqueteQx: false,
                        DPI: this.DatosAdmision.DPI,
                        TipoAdmision: 2,
                        CodigoPaqueteBebe: 0,
                        PrecioPaquete: 0,
                        FechaNacimiento: this.DatosAdmision.FechaNacimiento
                    })
                    .then(resp => {

                        if (resp.data.json[0].Codigo > 0) {
                            var codigo = resp.data.json.length > 0 ? resp.data.json[0].Codigo : null;

                            // this.$vs.notify({
                            //     color: '#B71C1C',
                            //     title: 'Alerta',
                            //     text: 'Admisión creada Serie' + this.DatosAdmision.Serie + ' Número: ' + codigo

                            // })
                            this.AdmisionSeguro = this.DatosAdmision.Serie + '-' + codigo;
                            if (codigo > 0) {
                                this.axios.post('/app/v1_autorizaciones/CreacionCargos', {
                                    SerieAdmision: this.DatosAdmision.Serie,
                                    Admision: codigo,
                                    Tipo: 'A',
                                    IdSolicitud: this.AdmisionSeguroExterna[0].no_solicitud_clinica,
                                    MedicoOrden: this.DatosAdmision.Medico

                                }).then(resp => {
                                    if (resp.data.codigo == 0) //&& this.AdmisionPrivadaExterna.length <= 0) {
                                    {
                                        // this.activeTab = 0;
                                        // this.Consulta().ConsultaSolicitud();
                                        resolve(resp);
                                    } else {
                                        reject(new Error('Error'));
                                    }

                                });
                            } else {
                                reject(new Error('Error'));
                            }

                        } else {
                            reject(new Error('Error'));
                        }

                    })

            })

        },
        CrearAdmisionPrivada() {
            /***********************************ADMISION PRIVADA ****************************/
            return new Promise((resolve, reject) => {
                this.axios.post('/app/v1_admision/CrearAdmision', {
                        Serie: this.DatosAdmision.Serie,
                        Paciente: this.DatosAdmision.Paciente,
                        IdCliente: this.DatosAdmision.IdCliente,
                        NivelPrecios: this.DatosAdmision.NivelPrecios,
                        TipoDescuento: 'N',
                        Seguro: null,
                        NombreFactura: this.DatosAdmision.NombreFactura,
                        Nit: this.DatosAdmision.Nit,
                        DireccionFactura: this.DatosAdmision.DireccionFactura,
                        Medico: null, //this.DatosAdmision.Medico,
                        Otros: null,
                        PacienteBuscoHospital: 'S',
                        IdAfiliado: null,
                        PaqueteQx: false,
                        DPI: this.DatosAdmision.DPI,
                        TipoAdmision: 2,
                        CodigoPaqueteBebe: 0,
                        PrecioPaquete: 0,
                        FechaNacimiento: this.DatosAdmision.FechaNacimiento
                    })
                    .then(resp => {
                        if (resp.data.json[0].Codigo > 0) {
                            var codigo = resp.data.json.length > 0 ? resp.data.json[0].Codigo : null;

                            this.AdmisionPrivada = this.DatosAdmision.Serie + '-' + codigo;
                            if (codigo > 0) {
                                this.axios.post('/app/v1_autorizaciones/CreacionCargos', {
                                        SerieAdmision: this.DatosAdmision.Serie,
                                        Admision: codigo,
                                        Tipo: 'D',
                                        IdSolicitud: this.AdmisionPrivadaExterna[0].no_solicitud_clinica,
                                        MedicoOrden: this.DatosAdmision.Medico

                                    })
                                    .then(resp => {
                                        if (resp.data.codigo == 0) {
                                            resolve(resp);
                                        } else {
                                            reject(new Error('Error'));
                                        }
                                    });
                            } else {
                                reject(new Error('Error'));

                            }

                        } else {
                            reject(new Error('Error'));
                        }

                    })

            })

        },
        CrearAdmision() {
            this.AdmisionSeguroExterna = this.Autorizaciones.filter(f => f.seleccion == 1 && f.status_analista == 'A')
            this.AdmisionPrivadaExterna = this.Autorizaciones.filter(f => f.seleccion == 1 && f.status_analista == 'D')

            //Creación Admision Seguro
            if (this.AdmisionSeguroExterna.length > 0) {
                this.axios.post('/app/v1_admision/CrearAdmision', {
                        Serie: this.DatosAdmision.Serie,
                        Paciente: this.DatosAdmision.Paciente,
                        IdCliente: this.DatosAdmision.IdCliente,
                        NivelPrecios: this.DatosAdmision.NivelPrecioSasi,
                        TipoDescuento: 'S',
                        Seguro: this.DatosAdmision.Seguro,
                        NombreFactura: this.DatosAdmision.NombreFactura,
                        Nit: this.DatosAdmision.Nit,
                        DireccionFactura: this.DatosAdmision.DireccionFactura,
                        Medico: null, //this.DatosAdmision.Medico,
                        Otros: this.DatosAdmision.Otros,
                        PacienteBuscoHospital: 'S',
                        IdAfiliado: this.DatosAdmision.IdAfiliado,
                        PaqueteQx: false,
                        DPI: this.DatosAdmision.DPI,
                        TipoAdmision: 2,
                        CodigoPaqueteBebe: 0,
                        PrecioPaquete: 0,
                        FechaNacimiento: this.DatosAdmision.FechaNacimiento
                    })
                    .then(resp => {

                        if (resp.data.json[0].Codigo > 0) {
                            var codigo = resp.data.json.length > 0 ? resp.data.json[0].Codigo : null;

                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'Admisión creada Serie' + this.DatosAdmision.Serie + ' Número: ' + codigo
                            })

                            if (codigo > 0) {
                                this.axios.post('/app/v1_autorizaciones/CreacionCargos', {
                                    SerieAdmision: this.DatosAdmision.Serie,
                                    Admision: codigo,
                                    Tipo: 'A',
                                    IdSolicitud: this.AdmisionSeguroExterna[0].no_solicitud_clinica,
                                    MedicoOrden: this.DatosAdmision.Medico

                                }).then(resp => {
                                    if (resp.data.codigo == 0 && this.AdmisionPrivadaExterna.length <= 0) {
                                        this.activeTab = 0;
                                        this.Consulta().ConsultaSolicitud();
                                    }

                                });
                            } else {
                                return;
                            }

                        } else {
                            return;
                        }

                    })
            }
        },
        CreacionPrivada() {
            //Creación Admision PRIVADA
            if (this.AdmisionPrivadaExterna.length > 0) {

                this.axios.post('/app/v1_admision/CrearAdmision', {
                        Serie: this.DatosAdmision.Serie,
                        Paciente: this.DatosAdmision.Paciente,
                        IdCliente: this.DatosAdmision.IdCliente,
                        NivelPrecios: this.DatosAdmision.NivelPrecios,
                        TipoDescuento: 'N',
                        Seguro: null,
                        NombreFactura: this.DatosAdmision.NombreFactura,
                        Nit: this.DatosAdmision.Nit,
                        DireccionFactura: this.DatosAdmision.DireccionFactura,
                        Medico: null, //this.DatosAdmision.Medico,
                        Otros: null,
                        PacienteBuscoHospital: 'S',
                        IdAfiliado: null,
                        PaqueteQx: false,
                        DPI: this.DatosAdmision.DPI,
                        TipoAdmision: 2,
                        CodigoPaqueteBebe: 0,
                        PrecioPaquete: 0,
                        FechaNacimiento: this.DatosAdmision.FechaNacimiento
                    })
                    .then(resp => {
                        if (resp.data.json[0].Codigo > 0) {
                            var codigo = resp.data.json.length > 0 ? resp.data.json[0].Codigo : null;

                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'Admisión Privada creada Serie' + this.DatosAdmision.Serie + ' Número: ' + codigo
                            })

                            if (codigo > 0) {
                                this.axios.post('/app/v1_autorizaciones/CreacionCargos', {
                                        SerieAdmision: this.DatosAdmision.Serie,
                                        Admision: codigo,
                                        Tipo: 'D',
                                        IdSolicitud: this.AdmisionPrivadaExterna[0].no_solicitud_clinica,
                                        MedicoOrden: this.DatosAdmision.Medico

                                    })
                                    .then(resp => {
                                        if (resp.data.codigo == 0) {
                                            this.activeTab = 0;
                                            this.Consulta().ConsultaSolicitud();
                                        }
                                    });
                            } else {
                                return;
                            }

                        } else {
                            return;
                        }

                    })
            }
        },
        //SASI FASE 2
        PreEstadoCuenta(IdSolicitud) {
            this.$reporte_modal({
                Nombre: "Pre Estado Cuenta",
                Opciones: {
                    tiporeporte: "application/pdf",
                    nombrereporte: "ReportePreEstadoCuenta",
                    IdCodigoUnico: IdSolicitud,
                    json: "[{\"Etiqueta\":\"nombrereporte\",\"Parametro\":\"nombrereporte\",\"value\":\"ReportePreEstadoCuenta\"},{\"Etiqueta\":\"IdCodigoUnico\",\"Parametro\":\"IdCodigoUnico\",\"value\":\"" + IdSolicitud + "\"}]",
                    ConfigCorreo: null
                }
            })
        },

        /*

          */
        AutorizarRechazarEstudio(tr) {

            let selec = 0;
            if (tr.seleccion) {
                selec = 1;
            }
            this.axios
                .post("/app/v1_autorizaciones/AutorizarRechazarEstudio", {
                    IdSolicitud: tr.no_solicitud_clinica,
                    IdLinea: tr.Correlativo,
                    Valor: selec
                })
                .then((resp) => {
                    if (resp.data.codigo == "0") {
                        this.Beneficios = resp.data.json;
                    }
                });
        },
        // ==========================================================
        //  COSULTA DATOS SOLICITUDES
        // ==========================================================

        Consulta: function () {
            return {
                ConsultaSolicitud: () => {
                    this.axios
                        .post("/app/v1_autorizaciones/ConsultaAutorizaciones", {
                            Operacion: "CONSULTASOLICITUDPF",
                        })
                        .then((resp) => {
                            if (resp.data.codigo == '0') {
                                this.Solicitudes = resp.data.json;
                            }
                        });
                },
            };
        },

        // ==========================================================
        //  BUSCAR AFILIADO POR CODIGO
        // ==========================================================
        ConsultaInfo: function () {
            return {
                ConsultaDetalle: (valor) => {
                    this.axios
                        .post("/app/v1_autorizaciones/ConsultaDatosAfiliado", {
                            Operacion: "CONSULTADATOS",
                            Afiliado: valor.Afiliado,
                        })
                        .then((resp) => {
                            if (resp.data.json) {

                                this.DatosAdmision.Paciente = resp.data.json.length > 0 ? resp.data.json[0].idPaciente : null;
                                this.DatosAdmision.IdAfiliado = resp.data.json.length > 0 ? resp.data.json[0].idAdiliado : null;
                                this.DatosAdmision.IdCliente = resp.data.json.length > 0 ? resp.data.json[0].idCliente : null;
                                this.DatosAdmision.NombreFactura = resp.data.json.length > 0 ? resp.data.json[0].Nombre_Paciente : null;
                                this.DatosAdmision.Nit = resp.data.json.length > 0 ? resp.data.json[0].NIT_Paciente : null;
                                this.DatosAdmision.DireccionFactura = resp.data.json.length > 0 ? resp.data.json[0].Direccion_paciente : null;
                                this.DatosAdmision.Otros = resp.data.json.length > 0 ? resp.data.json[0].CodigoPlan : null;
                                this.DatosAdmision.FechaNacimiento = resp.data.json.length > 0 ? resp.data.json[0].FechaNacimiento_paciente : null;
                                this.DatosAdmision.DPI = resp.data.json.length > 0 ? resp.data.json[0].DPI_paciente : null;
                                this.DatosAdmision.PacienteBuscoHospital = 'S';

                                this.axios.post("/app/v1_autorizaciones/PlanMedico", {
                                        Operacion: "CONSULTAPLANMEDICO",
                                        Afiliado: this.DatosAdmision.IdAfiliado,
                                    })
                                    .then((resp) => {
                                        if (resp.data.codigo == "0") {
                                            this.DatosAdmision.Seguro = resp.data.json.length > 0 ? resp.data.json[0].IdPoliza : null;
                                            //Consultar  Nivel Precio SASI
                                            if (this.DatosAdmision.Seguro != '') {
                                                this.axios.post('/app/v1_autorizaciones/ConsultarNivelPrecioSASI', {
                                                    CodigoPlan: this.DatosAdmision.Seguro

                                                }).then(respNivelPrecio => {
                                                    this.DatosAdmision.NivelPrecioSasi = respNivelPrecio.data.json.length > 0 ? respNivelPrecio.data.json[0].nivelpreciosext : null; // resp.data.json.map(m =>  m.NivelDePrecios )
                                                    this.DatosAdmision.Aseguradora = respNivelPrecio.data.json.length > 0 ? respNivelPrecio.data.json[0].CodigoAseguradora : null; // resp.data.json.map(m =>  m.Serie )               

                                                })
                                            }
                                        }
                                    });
                                if(resp.data.json.length)
                                    this.$map({
                                        objeto: this.info,
                                        respuestaAxios: resp,
                                    })
                            }

                        });
                    this.ConsultaDiagnostico().Diagnostios(valor);
                },
            };
        },
        // ==========================================================
        //  BUSCAR LAS SOLICITUD INFO
        // ==========================================================
        ConsultaDatos: function () {
            return {
                ConsultaDatosSolicitud: (valor) => {
                    this.axios
                        .post("/app/v1_autorizaciones/ConsultaDatosAutorizacion", {
                            Operacion: "CONSULTASOLICITUDAUTO",
                            Solicitud: valor.idSolicitud,
                        })
                        .then((resp) => {
                            if (resp.data.json) {
                                this.$map({
                                    objeto: this.info,
                                    respuestaAxios: resp,
                                });
                            }
                        });
                },
            };
        },
        // ==========================================================
        //  BUSCA EL O LOS DIAGNOSTIVOS
        // ==========================================================
        ConsultaDiagnostico: function () {
            return {
                Diagnostios: (valor) => {
                    this.axios
                        .post("/app/v1_autorizaciones/ConsultaDiagnosticos", {
                            Operacion: "CONSULTADIAGNOSTICO",
                            Solicitud: valor.idSolicitud,
                        })
                        .then((resp) => {
                            if (resp.data.codigo == "0") {
                                this.Diagnosticos = resp.data.json;
                                this.DatosAdmision.InfoDiagnostico = this.Diagnosticos[0].descripcion.length > 0 ?  this.Diagnosticos[0].descripcion : ''
                            }
                        });

                },
            };
        },
        // ==========================================================
        //  BUSCA LOS CARGOS A AUTORIZAR
        // ==========================================================
        ConsultaCargos: function () {
            return {
                Autorizaciones: (valor) => {
                    this.axios
                        .post("/app/v1_autorizaciones/ConsultaDeCargosPeAutorizar", {
                            Operacion: "CONSULTADEAUTORIZACIONESPF",
                            Solicitud: valor.idSolicitud,
                        })
                        .then((resp) => {
                            if (resp.data.codigo == "0") {
                                this.Autorizaciones = resp.data.json;

                            }

                        });
                    this.ConsultaInfo().ConsultaDetalle(valor);
                },
            };
        },

        // ==========================================================
        //  BUSCA LOS BENEFICIOS
        // ==========================================================
        ConsultaBeneficios: function () {
            return {
                Beneficios: (valor) => {
                    this.axios
                        .post("/app/v1_autorizaciones/Beneficios", {
                            Operacion: "BENEFICIOS",
                            Afiliado: valor.Afiliado,
                        })
                        .then((resp) => {
                            if (resp.data.codigo == "0") {
                                this.Beneficios = resp.data.json;
                            }
                            this.ConsultaDatos().ConsultaDatosSolicitud(valor);
                        });
                },
            };
        },

        // =========================================================================================================================
        //  CAMBIO DE TAB
        // =========================================================================================================================
        changeTab(index) {
            this.activeTab = index;

        },

        // ==========================================================
        //  CALCULAR PRECIOS 
        // ==========================================================
        calcularTotales() {
            this.total = 0;
            this.declinado = 0;
            this.totalGeneral = 0;

            for (const Autorizacion of this.Autorizaciones) {
                if (Autorizacion.Autorizado) {
                    this.total += parseFloat(Autorizacion.Precio);
                } else {
                    this.declinado += parseFloat(Autorizacion.Precio);
                }
                this.totalGeneral += parseFloat(Autorizacion.Precio);
            }
            // Format the totals after the loop
            this.total = this.total.toLocaleString("es-GT", {
                style: "currency",
                currency: "GTQ",
            });
            this.declinado = this.declinado.toLocaleString("es-GT", {
                style: "currency",
                currency: "GTQ",
            });
            this.totalGeneral = this.totalGeneral.toLocaleString("es-GT", {
                style: "currency",
                currency: "GTQ",
            });
        },
        // =========================================================================================================================
        //  EJECUCION GENERAL ConsultaGeneral().Actualizar
        // =========================================================================================================================
        ConsultaGeneral: function () {
            return {
                EjecutarConsultas: (valor) => {

                    this.DatosAdmision.Notas = valor.Notas;
                    this.IdSolicitudSelec = valor.idSolicitud
                    this.SolicitudSeleccionada = valor
                    if (valor) {
                        this.ConsultaCargos().Autorizaciones(valor)
                    }
                    this.Consulta().ConsultaSolicitud();
                    //this.Consulta().ConsultaSolicitud(); 
                    //this.UsuarioTrabajando().Usuario(valor)
                },

            };
        },

        SeleccionEstudioRealizar() {
            //A
            // Fuerza una actualización de la vista en Vue 2
            this.$forceUpdate();

            // Calcula los totales después de actualizar selectedItems
            this.calcularTotales();
        },

        // =========================================================================================================================
        //  LIMPIAR CAMPOS
        // =========================================================================================================================

        limpiarCampos() {

            //Campos
            this.changeTab(0);
            this.info.FechaIngreso = null;
            this.info.NombreMedico = null;
            this.info.medico = null;
            this.info.idSolicitud = null;
            this.info.idAdiliado = null;
            this.info.Paciente = null;
            this.info.idCliente = null;
            this.info.idContrato = null;
            this.info.observacion = null;
            this.info.motivo_declinacion = null;
            this.info.ObsercacionesAuto = null;

            //Tablas
            this.Diagnosticos = [];
            this.Autorizaciones = [];
            this.selectedItems = [];
            this.plan = [];
            this.Beneficios = [];
            this.Consulta().ConsultaSolicitud();
            this.$vs.notify({
                title: 'ADVERTENCIA!',
                text: 'Datos Limpiados.',
                iconPack: 'feather',
                icon: 'icon-alert-circle',
                color: 'danger'
            })
        },

        ErrorRegresar() {
            this.limpiarCampos()
            this.LimpiarCamposCupon()
        },

        regresar() {
            this.cancelarSolicitud()
            this.limpiarCampos()
            this.LimpiarCamposCupon()
        },

        CargarPDF() {
            this.$reporte_modal({
                Nombre: 'Historial',
                Opciones: {
                    IdCliente: this.info.idCliente
                }
            })
        },

        LimpiarCamposCupon() {
            this.info.NUMERO = null
            this.info.NumeroAdhesion = null
            this.MedicoSelecionado.Codigo = null
            this.info.idCliente = null
            this.info.idAdiliado = null

            this.CuponGenerado = []
            this.isButtonDisabled = true
            this.info.NumeroAdhesion = null,
                this.MedicoSelecionado.Codigo = null,
                this.info.idCliente = null,
                this.info.idAdiliado = null,
                this.number0 = null,
                this.IdServicioCoex = null,
                this.MontoSiniestralidad = null,
                this.info.ObsercacionesCupon = null,
                this.status = null,
                this.MedicoSelecionado = {}
        },
        /**
         * Valida que el cliente seleccionado tenga asociado un registro de paciente, si no lo tiene permite crear o asocoarlo
         */
        ValidarPaciente() {
            
            if(this.DatosAdmision.Paciente)
                return true

            this.$vs.notify({
                title: 'Creación de Paciente',
                text: 'Paciente no cuenta con perfil "Paciente" en afiliados, necesario para crear estas admisiones',
                iconPack: 'feather',
                icon: 'icon-alert-circle',
                color: 'danger'
            })
        },

        onClientePacienteChanged() {
            this.popupClientePaciente = false
            this.ConsultaInfo().ConsultaDetalle(this.SolicitudSeleccionada)
        },

    },

    mounted() {
        this.Consulta().ConsultaSolicitud();
        setTimeout(() => {
            this.$validar_funcionalidad('/SASI/SASI003', 'HABILITAR', (d) => {
                this.permisos.Habilitar = d.status

            })
        }, 1000)
    },

};
</script>

<style scoped>
.Izquierda {
    width: 150%;
    padding: 5px;
    border-radius: 5px;
}

.Izquierda2 {
    width: 130%;
    padding: 5px;
    border-radius: 5px;
}

.Derecha {
    width: 100%;
    padding: 5px;
    border-radius: 5px;
}

.Derecha2 {
    width: 70%;
    padding: 5px;
    border-radius: 5px;
}

.card {
    margin-top: 5px;
}

.notificacion {
    text-align: center;
    font-size: 25px;
    height: 40px;
    color: rgba(var(--vs-primary), 0.7);
    margin-left: 10px;
    cursor: pointer;
    position: relative;
}

.notificacion:hover {
    color: rgba(var(--vs-primary), 1);
}

.notificacion .titulo {
    font-weight: bold;
    font-size: 11px;
    position: relative;
    top: -5px;
}

.Left {
    width: 100%;
    padding: 5px;
    border-radius: 5px;
}

.Ringht {
    width: 100%;
    padding: 5px;
    border-radius: 5px;
}

.Medico {
    margin-top: 10px;
}

.Mid {
    margin-top: 10px;
    width: 100%;
    padding: 5px;
    border-radius: 5px;
}

.Mid2 {
    width: 100%;
    padding: 5px;
    border-radius: 5px;
}

.ButtomGenerar {
    width: 100%;
}

.ButtomCupon {
    width: 100%;
    text-align: right;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    /* Fondo oscuro semi-transparente */
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    padding: 20px;
    border-radius: 5px;
}

.regresar {
    display: flex;
    justify-content: right;

}

.BotonInicio {
    border-radius: 5px;
}

button {
    margin-right: 10px;
    padding: 10px 20px;
    background-color: green;
    color: white;
    border: none;
    cursor: pointer;
}

button:hover {
    background-color: #52BE80;
}

.buttonh {
    height: 50px;
    width: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px !important;
}

.buttonh:hover {
    background-color: blue !important;
}

.div-button {
    display: flex;
    justify-content: center;
}
</style><style>
.OverFlow .contenedor-tabla {
    overflow-y: auto !important;
    max-height: 400px !important;
}

.swal2-container {
    z-index: 55000 !important
}
.sasi007.con-vs-popup .vs-popup:has(.clientes-pacientes-container) {
    width: 99% !important;
}
</style>
