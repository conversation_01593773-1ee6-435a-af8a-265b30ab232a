<template>
<div class="main-historial">
    <vs-popup ref="buscador" title="Nuevo Grupo" :active.sync="grupo.mostrar" style="z-index:52000" id="div-with-loading" class="vs-con-loading__container">
        <ValidationObserver ref="formValidate" v-slot="{ invalid,handleSubmit }" mode="lazy">
            <ValidationProvider name="Especialidad" rules="required|max:50" v-slot="{ errors }" class="required">
                <vs-input label="Nombre del Grupo" class="w-full" v-model="grupo.nombre" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
            </ValidationProvider>
            <div class="flex">
                <vs-spacer></vs-spacer>
                <vs-button :disabled="invalid"  class="mr-1 mt-5" @click.native="handleSubmit(Nuevo().grupo)">Guardar</vs-button>
            </div>
        </ValidationObserver>
    </vs-popup>

    <br>
    <vs-card>
        <ValidationObserver ref="formValidate" v-slot="{ invalid,handleSubmit }" mode="lazy">
            <div class="flex flex-wrap">
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5  p-1 pt-6">
                    <ValidationProvider name="Especialidad" rules="required" class="required">
                        <vs-select label="Grupo" v-model="info.grupo" style="display:inline-block;width:calc(100% - 50px)">
                            <vs-select-item :key="key" :value="item" :text="item" v-for="(item,key) in grupos" />
                        </vs-select>
                        <vs-button color="success" size="medium" icon-pack="fas" icon="fa-plus" class="mr-1 mt-5" style="display:inline-block" @click="grupo.mostrar= true"></vs-button>

                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5  p-1 pt-6">
                    <ValidationProvider name="Proteccion" rules="required" class="required">
                        <vs-input label="Protección" class="w-full" v-model="info.proteccion" />
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5  p-1 pt-6">
                    <ValidationProvider name="Descripcion" rules="max:100" v-slot="{ errors }">
                        <vs-input label="Descripción" class="w-full" v-model="info.descripcion" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 pt-6">
                    <vs-button :disabled="invalid"  class="mr-1 mt-5" @click.native="handleSubmit(Guardar().catalogo)">Guardar</vs-button>
                </div>

            </div>
        </ValidationObserver>
    </vs-card>
    <!-- {{vacunas}} -->
    <vs-table2 max-items="10" search tooltip pagination :data="vacunas">
        <template slot="thead">
            <th order="Grupo" width="150px">Grupo</th>
            <th order="Nombre" width="170px">Protección</th>
            <th order="Descripción">Descripción</th>
            <th order="Activo" width="110px">Activo</th>
        </template>

        <template slot-scope="{data}">
            <tr :key="indextr" v-for="(tr, indextr) in data">
                <vs-td2>
                    {{ tr.Grupo }}
                </vs-td2>

                <vs-td2>
                    {{ tr.Nombre }}
                </vs-td2>

                <vs-td2>
                    {{ tr.Descripcion }}
                </vs-td2>

                <vs-td2 noTooltip>
                    <vs-switch v-model="tr.Activo" @click="Editar().estado(tr.Id,!tr.Activo)"></vs-switch>
                    <!-- {{tr.Activo}} -->
                </vs-td2>
            </tr>
        </template>
    </vs-table2>

</div>
</template>

<script>
export default {
    data() {
        return {
            info: {
                grupo: null,
                proteccion: null,
                descripcion: null
            },

            grupo: {
                mostrar: false,
                nombre: null
            },

            vacunas: [],

            grupos: []
        }
    },
    methods: {
        //=======================================================================================================
        // CONSULTA
        //=======================================================================================================
        Consulta: function() {
            return {
                listado: (Tipo, Estado) => {
                    return this.axios.post('/app/clinica/Vacunacion_Catalogo_Cargar', {
                            Tipo,
                            Estado
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.vacunas = resp.data.json
                                this.vacunas = this.vacunas.map(m => {
                                    return {
                                        ...m,
                                        Activo: m.Activo == "1" ? true : false
                                        // Activo321154: Activo == "1"?true:false
                                    }
                                })
                                this.vacunas.forEach(vacuna => {
                                    if (this.grupos.indexOf(vacuna.Grupo) < 0) this.grupos.push(vacuna.Grupo)
                                });

                                this.info.grupo = null
                                this.info.proteccion = null
                                this.info.descripcion = null
                            }
                        })
                        .catch(() => {

                        })
                }
            }
        },
        //=======================================================================================================
        // NUEVO
        //=======================================================================================================
        Nuevo: function() {
            return {
                grupo: () => {
                    this.grupos.push(this.grupo.nombre.trim())
                    this.info.grupo = this.grupo.nombre.trim()
                    this.grupo.nombre = null
                    this.grupo.mostrar = false
                }
            }
        },
        //=======================================================================================================
        // GUARDAR
        //=======================================================================================================
        Guardar: function() {
            return {
                catalogo: () => {
                    return this.axios.post('/app/clinica/Vacunacion_Catalogo_Nuevo', this.info)
                        .then(() => {
                            this.Consulta().listado()
                        })
                        .catch(() => {})
                }
            }
        },
        //=======================================================================================================
        // EDITAR
        //=======================================================================================================
        Editar: function() {
            return {
                estado: (id, activo) => {
                    return this.axios.post('/app/clinica/Vacunacion_Catalogo_Editar', {
                            id,
                            activo: activo ? 1 : 0
                        })
                        .then(() => {
                            this.Consulta().listado()
                        })
                        .catch(() => {})
                }
            }
        }
    },
    mounted() {
        this.Consulta().listado()
    }
}
</script>
