

//import { getField } from 'vuex-map-fields';

import Vue from 'vue'
import axios from 'axios'


//axios
import VueAxios from 'vue-axios'
Vue.use(VueAxios, axios)
//  import { createHelpers } from 'vuex-map-fields';
// const { getField, updateField } = createHelpers({
//     getterType: 'getField',
//     mutationType:'updateField',
//   });

import { getField, updateField } from "vuex-map-fields";

import { getDefaultState } from './modulePacienteMutations';

//import modulePacienteActions from "./modulePacienteActions"
//import  getters from "./modulePacienteGetters"
//import modulePacienteMutations from "./modulePacienteMutations"
//import modulePacienteState from "./modulePacienteState"

const state = {
    PacienteData: {
        Empresa: '',
        Codigo: '',
        PrimerNombre: '',
        SegundoNombre: '',
        PrimerApellido: '',
        SegundoApellido: '',
        ApellidoCasada: '',
        Sexo: '',
        EstadoCivil: '',
        Religion: '',
        Nacimiento: '',
        LugarNacimiento: '',
        Nacionalidad: '',
        Direccion: '',
        Ciudad: '',
        Telefonos: '',
        Profesion: '',
        LugarTrabajo: '',
        TelefonoTrabajo: '',
        Padre: '',
        Madre: '',
        Conyuge: '',
        Internado: '',
        DireccionFactura: '',
        NombreFactura: '',
        Nit: '',
        NumeroArchivo: '',
        Status: '',
        UltimaAdmision: '',
        SerieRayos: '',
        NumeroRayos: '',
        Carnet: '',
        Socio: '',
        Pasaporte: '',
        NumeroCarnet: '',
        NumeroUltraSonido: '',
        SeguroSocial: '',
        NumeroExpedienteClinico: '',
        TieneSeguroMedico: '',
        Aseguradora: '',
        Etina: '',
        Celular: '',
        Email: '',
        DireccionTrabajo: '',
        Ocupacion: '',
        EmergenciaQuien: '',
        EmergenciaDireccion: '',
        EmergenciaTelefono: '',
        EmergenciaCelular: '',
        DatosFamNombreMadre: '',
        DatosFamNombrePadre: '',
        ConyugeDireccion: '',
        ConyugeTelefono: '',
        FechaPrimeraConsulta: '',
        Marcador: '',
        NombrePatrono: '',
        Nopatronal: '',
        PesoNacerLb: '',
        PesoNacerOz: '',
        HoraNac: '',
        TallaNac: '',
        IdEmpresa: '',
        IdCliente: '',
        ClienteSolicitaNoSerLLamado: true,
        PacienteInvalido: false,
        PacienteBlackList: false,
        PctHemodialisis: false,
        PctSinHuellas: true,
        ClaseAfiliado: '',
        Horario: '',
        Etnia: '',
        FechaPrimeraVisita: '',
        FechaUltimaVisita: '',
        CalleResidencia: '',
        AvenidaResidencia: '',
        NoCasaResidencia: '',
        ZonaResidencia: '',
        ColoniaResidencia: '',
        EdificioResidencia: '',
        DeptoResidencia: '',
        MunicipioResidencia: '',
        CalleTrabajo: '',
        AvenidaTrabajo: '',
        NoCasaTrabajo: '',
        ZonaTrabajo: '',
        ColoniaTrabajo: '',
        EdificioTrabajo: '',
        OficinaTrabajo: '',
        DeptoTrabajo: '',
        MunicipioTrabajo: '',
        PadreResponsable: '',
        MadreResponsable: '',
        DPIPadreResponsable: '',
        DPIMadreResponsable: '',
        TelefonoPadreResponsable: '',
        TelefonoMadreResponsable: '',
        TelefonoMedicoCabecera: '',
        OtrosMedicos: '',
        TelefonoOtrosMedicos: '',
        TipoDeSangre: '',
        AlergiasMedicamento: '',
        AlergiasComidas: '',
        AlergiasSustanciasAmbiente: '',
        AlergiasSustanciasPiel: '',
        AlergiasOtros: '',
        AlimentosNoCarne: '',
        AlimentosNoFrutas: '',
        AlimentosNoVegetales: '',
        AlimentosVegetariano: '',
        AlimentosNoOtros: '',
        DPI: '',
        MedicoCabecera: '',
        SaludSiempre: '',
        Vacunado: ''
    }
}

const mutations = {
    updateField,
    CREATE_PACIENTE(state, paciente) {
        state.PacienteData = paciente
   },
    SET_PACIENTE(state, paciente) {
        state.PacienteData = paciente
    },
    SET_CODIGO_PACIENTE(state, codigo) {        
        state.PacienteData.Codigo = codigo
    },
    SET_CODIGO_CLIENTE(state, cliente) {
        state.PacienteData.IdCliente = cliente
    },
    SET_INIT_PACIENTE(state){
        Object.assign(state, getDefaultState());
    }
}

const getters = {
    //getPacienteField,
    getField,
    getPaciente: state => {
        return state.PacienteData
    },
    getCodigoPaciente(state) {
        return state.PacienteData.Codigo;
    },
    getnombrePaciente(state){
        const NombrePaciente = state.PacienteData.PrimerNombre + ' '+ ' ' +state.PacienteData.SegundoNombre + ' ' + state.PacienteData.PrimerApellido+ ' ' +state.PacienteData.SegundoApellido
        return NombrePaciente
    }
}
 
const actions = {
    async crearPaciente({ commit }) {

    
        return new Promise((resolve, reject) => {
            axios.post('/app/v1_admision/CrearPaciente', state.PacienteData)
                .then(function (response) {                    
                    if (!state.PacienteData.Codigo) {
                        commit("SET_CODIGO_PACIENTE", response.data.json[0].IdPaciente)
                        //commit("SET_CODIGO_CLIENTE", response.data.json[0].IdCliente)
                    }
                    if (!state.PacienteData.IdCliente) {
                        //commit("SET_CODIGO_PACIENTE", response.data.json[0].IdPaciente)
                        commit("SET_CODIGO_CLIENTE", response.data.json[0].IdCliente)
                    }
                    resolve(response)
                })
                .catch(function (error) {                    
                    reject(error)
                })
        })
       
    },
    setPaciente({ commit }, paciente) {
        commit('SET_PACIENTE', paciente)
    },
    asigarPaciente({ commit }, paciente) {
        commit('SET_CODIGO_PACIENTE', paciente)
    },
    asigarCliente({ commit }, paciente) {  
        commit('SET_CODIGO_CLIENTE', paciente)
    },
    initPaciente({commit}){
        let initState = state.PacienteData
        commit("SET_INIT_PACIENTE", initState)                        
    }
}

export default {
    //isRegistered: false,//
    namespaced: true,
    state,
    mutations,
    //state: modulePacienteState,
    //mutations:modulePacienteMutations,
    getters,
    // getters:{
    //     getField,
    //     getPaciente: state => {
    //         return state.PacienteData
    //     },
    //     getCodigoPaciente(state) {
    //         return getField(state.PacienteData.Codigo);
    //       },
    //  },
    actions
}