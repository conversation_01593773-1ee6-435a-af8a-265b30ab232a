


<template>
    <vx-card title="Reporte Kardex">
        <div class="container">
            <div class="a-79JYK p-4 pl-4 ">
                <h3>Selección Bodega</h3>
                <br>

                <vs-row>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <SM-Buscar
                          v-model="CodigoBodega"
                          label="Codigo Bodega"
                          api="app/v1_OrdenCompra/ConsultarBodegas"
                          :api_campo_respuesta_mostrar="['Bodega', 'Nombre']"
                          :api_campos="['Bodega', 'Nombre']"
                          :api_titulos="['Bodega', 'Nombre']"
                          api_campo_respuesta="Bodega"
                          :api_filtro="{Bodega:CodigoBodega}"
                          :api_preload="true"
                          :disabled_texto="true"
                          :callback_buscar="ConsultarBodega"
                        >
                        </SM-Buscar>

                    </div>
                    <div class="w-full md:w-2/3 lg:w-2/3 xl:w-2/3 p-1">

                        <vs-input label="NombreBodega" class="w-full" v-model="this.NombreBodega" readonly />

                    </div>
                </vs-row>

            </div>
            <div class="a-46KOm-0 p-4 pl-4">

                <h3>Selección Producto</h3>
                <br>

                <vs-row>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <SM-Buscar v-model="CodigoProducto" label="Codigo" type="input"
                            api="app/inventario/ListaProdsInventario" api_campo_respuesta_mostrar="Codigo"
                            :api_campos="['Codigo', 'Nombre']" :api_titulos="['Codigo', 'Nombre']"
                            api_campo_respuesta="Codigo" :api_campo_respuesta_estricto="false" :api_preload="false"
                            :disabled_texto="true" :callback_buscar="ConsultaProductos" :api_filtro="{
                                Empresa: '',
                                Clase: '0',
                                Nombre: '',
                                Tipo: '0'
                            }">
                        </SM-Buscar>
                    </div>
                    <div class="w-full md:w-2/3 lg:w-2/3 xl:w-2/3 p-1">
                        <vs-input label="Descripción Producto" class="w-full" v-model="this.DescripcionProducto" readonly />
                    </div>
                </vs-row>


            </div>
            <div class="a-nRJ1Q">
                <vs-row class="m-2">
                    <vs-col vs-type="flex" vs-w="12">
                        <vs-col vs-type="flex" class="m-2">

                            <div class="md:pl-2">
                                <vs-button @click="GenerarReporte('PDF')" color="danger"
                                    style="margin-right:5px; margin-top: 20px"><i class="fas fa-file-pdf "></i>
                                    PDF</vs-button>
                            </div>

                            <div class="md:pl-2">
                                <vs-button @click="GenerarReporte('EXCEL')" color="success"
                                    style="margin-top: 20px"><i class="far fa-file-excel"></i> Excel</vs-button>
                            </div>

                        </vs-col>

                    </vs-col>
                </vs-row>

            </div>
        </div>
    </vx-card>
</template>


<script>

export default {
    data() {
        return {
            CodigoBodega: 0,
            CodigoProducto: '',
            NombreBodega: '',
            DescripcionProducto: ''


        }
    },
    methods: {
        ConsultarBodega(datos) {
            this.NombreBodega = datos.Nombre
        },
        ConsultaProductos(datos) {
            this.DescripcionProducto = datos.Nombre

        },
        async GenerarReporte(tiporeporte) {

            let postData = {
                Bodega: this.CodigoBodega,
                ListaProductos: this.CodigoProducto
            }

            this.$reporte_modal({
                Nombre: 'ReporteKardexProductoBodega',
                Opciones: {
                    ...postData
                },
                Formato: tiporeporte
            })

        }

    }
}


</script>


<style scoped>
* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

.container {
    display: grid;
    grid-gap: 5px;
    width: 100%;
    height: 100%;
    grid-template-areas: "a-79JYK a-79JYK a-46KOm-0"
        "a-nRJ1Q a-nRJ1Q a-nRJ1Q";
    grid-template-columns: 0.5fr 0.5fr 1fr;
    grid-template-rows: 0.25fr 0.25fr;
}

.container>div {
    border: 1px solid #888;
}

.container {
    max-width: 100%;
}


.a-79JYK {
    grid-area: a-79JYK;
}

.a-46KOm-0 {
    grid-area: a-46KOm-0;
}

.a-nRJ1Q {
    grid-area: a-nRJ1Q;
}
</style>