<template>
    <div>
        <h5 class="mt-2">{{item.name}}</h5>
        <div class="ml-4" v-for="(sub2,key) in item.submenu" :key="key">
             
            <div v-if = "sub2.props && sub2.props.IdFuncionalidad" ref="ruta" class = "ruta selectorClass" @keydown="$emit('onItemClick', item)">
                <vs-icon    :icon-pack="favoritos.filter(f=>f.id==sub2.props.IdFuncionalidad).length>0?'fas':'far'" 
                            icon="fa-star" style="cursor:pointer" 
                            @click="GuardarLocal(item.name, sub2.name, sub2.props.IdFuncionalidad, sub2.url)" />
                <router-link :to="sub2.url" @click.native="$emit('onItemClick', item)" >
                    {{sub2.name}}
                </router-link>
            </div> 
            <div v-else>
                <fav-nav-search-item :item="sub2" :favoritos="favoritos" @onGuardarLocal="arg => $emit('onGuardarLocal', arg)" ref="menuitem"/>
            </div>        
        </div>
    </div>
</template>
<script>
export default {
    name: 'fav-nav-search-item',
    props: {
            item: Object, // El objeto de menú actual
            favoritos: Array
        },
    methods: {
        
        GuardarLocal(padre, nombre, id, url) {
            let menuFav = localStorage.getItem('menuFav')
            if (!menuFav) {
                menuFav = [{
                    padre,
                    nombre,
                    id,
                    url
                }]
                localStorage.setItem('menuFav', JSON.stringify(menuFav))
                this.store = menuFav
            } else {
                menuFav = JSON.parse(menuFav)
                if (menuFav.filter(f => f.id == id).length > 0) {
                    menuFav = menuFav.filter(f => f.id != id)
                    this.store = menuFav
                } else {
                    if (menuFav.length < 9) {
                        menuFav.push({
                            padre,
                            nombre,
                            id,
                            url
                        })
                        this.store = menuFav
                    }
                }
                localStorage.setItem('menuFav', JSON.stringify(menuFav))
            }
            this.$emit("onGuardarLocal", null)
        },
    },
}
</script>
<style scoped>
.ruta {
    padding: 5px;
    border-radius: 5px;
}
.far {
    color: #ccc !important;
}
.search .Seleccion {
    background-color: #ccc
}
</style>