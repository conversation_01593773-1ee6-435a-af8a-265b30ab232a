<template>
<vx-card type = "2" title="Anular Ordenes" class="justify-center ">    
    <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
        <form ref="formAnulacion" method="post" @submit.prevent="handleSubmit(anular_orden())">
            <div class="flex flex-wrap">
                <div class="md:w-full lg:w-4/12 xl:w-2/12 m-2">
                    <label>Admision:</label> 
                    &nbsp;&nbsp;
                    <label style="color:black">{{info.admision}}</label>                                 
                </div>
                <div class="md:w-full lg:w-6/12 xl:w-4/12 m-2 lg:pr-6">
                    <label>Paciente:</label> 
                    &nbsp;&nbsp;
                    <label style="color:black">{{info.paciente}}</label>      
                </div>
                <div class="md:w-full lg:w-1/12 xl:w-5/12"></div>
                <div class="md:w-full lg:w-4/12 xl:w-2/12 m-2">
                    <ValidationProvider rules="required" v-slot="{ errors }">
                        <SM-Buscar class="w-full" label="*Tipo de Orden" v-model="info.tipoOrden" api="app/v1_JefeCaja/ObtenerTiposOrdenesPorRol" :api_campos="['Codigo','Nombre']" :api_titulos="['Tipo de Orden','Descripción']" :api_filtro="{'Activa':0,'opcion':'C','subOpcion':'3','filtroTipoOrdenHospital':info.filtroTipoOrden.toString(),'filtroTipoOrdenGeneral':info.filtroTipoOrdenGeneral.toString()}" 
                                   api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Codigo" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="true" :callback_buscar="cargar().datos_tipo_orden" 
                                   :danger="errors.length > 0"
                                   :danger-text="(errors.length > 0) ? errors[0] : null"/>                                
                    </ValidationProvider>            
                </div>
                <div class="md:w-full lg:w-6/12 xl:w-4/12 m-2 lg:pr-6">
                    <vs-input label="Descripción:" class="w-full" :value="info.descripcionOrden" disabled/>
                </div>
                <div class="md:w-full lg:w-10/12 xl:w-6/12 m-2">
                    <ValidationProvider rules="required" v-slot="{ errors }">
                        <vs-input label="*No. Orden" type="number"  class="w-full"  v-model="info.codigoOrden" 
                                  :danger="errors.length > 0"
                                  :danger-text="(errors.length > 0) ? errors[0] : null" v-on:change="consultar_orden"/>
                    </ValidationProvider>
                </div>
                <div class="md:w-full lg:w-10/12 xl:w-6/12 m-2">
                    <ValidationProvider rules="max:40|required" v-slot="{ errors }">
                        <vs-input size="large" label="*Motivo" class="w-full"  v-model="info.razon" 
                                  :danger="errors.length > 0"
                                  :danger-text="(errors.length > 0) ? errors[0] : null"/>
                    </ValidationProvider>
                </div>                  
                <div class="w-full m-2">
                    <vs-button color="success" class="md:w-full lg:w-4/12 xl:w-1/12 m-2"
                        :disabled="info.desactivarBotonAnulacion"
                        @click="handleSubmit(anular_orden(invalid))">
                        Anular Orden
                    </vs-button>

                    <vs-button class="md:w-full lg:w-4/12 xl:w-1/12 m-2" color="warning" @click="confirmar_limpia_campos()">
                        Limpiar Campos
                    </vs-button>
                </div>
            </div>
        </form>
    </ValidationObserver>        
</vx-card>
</template>

<script>

export default {
    data(){
        return {
            info:{
                tipoOrden: null,
                descripcionOrden: null,
                codigoOrden: null,
                admision: null,
                paciente: null,
                razon: null,
                desactivarBotonAnulacion: true,
                filtroTipoOrden: [],
                filtroTipoOrdenGeneral: [] 
            },
        };
    },computed:{

    },methods:{
        consultar_orden(){            
            this.cargar_paciente()
        },
        cargar_paciente(){
            if (this.cargar().validar_campos()) {                
                this.cargar().datos_orden()
            }else{                
                this.info.admision = null
                this.info.paciente = null  
                this.info.desactivarBotonAnulacion = true
            }
        },
        cargar(){
            return {
                datos_tipo_orden: (datos)=>{
                    this.info.tipoOrden = datos.Codigo
                    this.info.descripcionOrden = datos.Nombre
                    this.cargar_paciente()
                },
                datos_orden: () => {
                    this.axios.post('/app/v1_JefeCaja/obtener_orden',{
                                            iOpcion: 'C',
                                            iSubOpcion: 'A',                                            
                                            tipoOrden: this.info.tipoOrden,
                                            orden: this.info.codigoOrden,
                                            status: 'P,I'
                                        })
                              .then((resp) => {
                                                    let error 
                                                    let mensajeError
                                                    
                                                    if(resp.data.json && resp.data.json.length != 0){
                                                        error = resp.data.json[0].tipo_error
                                                        mensajeError = resp.data.json[0].descripcion
                                                    }else{
                                                        error = resp.data.tipo_error
                                                        mensajeError = resp.data.descripcion
                                                    }

                                                    if(error && error != 0){
                                                        this.$vs.notify({
                                                            time: 4000,
                                                            color: '#B71C1C',
                                                            title: 'Alerta',
                                                            text: mensajeError
                                                        })

                                                        this.info.admision = null
                                                        this.info.paciente = null  
                                                        this.info.desactivarBotonAnulacion = true  
                                                    }else if(resp.data.json.length==0){
                                                        this.$vs.notify({
                                                            time: 4000,
                                                            color: '#B71C1C',
                                                            title: 'Alerta',
                                                            text: 'No existe la orden o ya fue anulada'
                                                        })

                                                        this.info.admision = null
                                                        this.info.paciente = null  
                                                        this.info.desactivarBotonAnulacion = true   
                                                    }
                                                    else{ //TipoOrden,CodigoOrden                                
                                                        this.info.admision = resp.data.json[0].SerieAdmision + '-' + resp.data.json[0].Admision
                                                        this.info.paciente = resp.data.json[0].NombrePaciente   
                                                        this.info.desactivarBotonAnulacion = false                        
                                                    }     
                                                }
                                    )
                },
                validar_campos:()=>{
                    return this.info.tipoOrden && this.info.descripcionOrden && this.info.codigoOrden                        
                }
            }
        },
        anular_orden(invalid){
            if(invalid){
                this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'Falta ingresar campos obligatorios',
                        })
                return
            }

            this.axios.post('/app/v1_JefeCaja/anular_orden',{
                iOpcion: 'A',
                tipoOrden: this.info.tipoOrden,
                orden: this.info.codigoOrden,
                razon: this.info.razon
            }).then((resp) => {
                            let error 
                            let mensajeError
                            if(resp.data.json && resp.data.json.length != 0){
                                if(resp.data.json[0].tipo_error){
                                    error = resp.data.json[0].tipo_error
                                    mensajeError = resp.data.json[0].descripcion
                                }                                    
                                else if(resp.data.json[0].ErrorNumber){
                                    error = resp.data.json[0].ErrorNumber
                                    mensajeError = resp.data.json[0].ErrorMessage
                                }                                                                    
                            }else{
                                error = resp.data.tipo_error
                                mensajeError = resp.data.descripcion
                            }

                            if(error && error != 0){
                                this.$vs.notify({
                                    time: 5000,
                                    color: '#B71C1C',
                                    title: 'Alerta',
                                    text: mensajeError
                                })
                            }else{ //TipoOrden,CodigoOrden       
                                const sesion = this.$store.state.sesion;
                          
                                const bitacora = {
                                    TipoOrden: this.info.tipoOrden,
                                    Orden: this.info.codigoOrden
                                }

                                this.axios.post('/app/bitacora/registro_bitacora',{
                                    info:"{\"empresa\":"+sesion.sesion_empresa+",\"info\":{\"tipobitacora\":\"Tipo=>>Inserción\",\"Anulacion\":"+JSON.stringify(bitacora)+"}}",
                                    Tabla:"CargosAnuladosDetalle,InventarioCargos,InvCargosDetalle"
                                })

                                this.$vs.notify({   
                                            time: 5000,                                         
                                            title:'Exito',
                                            text: resp.data.json[0].descripcion,
                                            color:'success'
                                        })
                                this.limpiar_campos_exito()                                
                            }     
                        }
                    )
        },
        limpiar_campos(){            
            this.info.tipoOrden = null
            this.info.descripcionOrden = null
            this.info.codigoOrden = null
            this.info.admision = null
            this.info.paciente = null
            this.info.razon = null            
        },
        limpiar_campos_exito(){            
            this.info.codigoOrden = null
            this.info.admision = null
            this.info.paciente = null
            this.info.razon = null            
        },
        confirmar_limpia_campos(){
            this.$vs.dialog({
                            type: 'confirm',
                            color: 'danger',
                            title: 'Confirmación',
                            acceptText: 'Limpiar',
                            cancelText: 'Cancelar',
                            text: `¿Desea limpiar los campos?`,
                            accept: () => {
                                this.limpiar_campos()
                            }
                        })
        }
    },mounted(){
        this.info.filtroTipoOrden = []
        this.info.filtroTipoOrdenGeneral = []
        for(let privilegio of this.$store.state.privilegios){
            if(privilegio.Privilegio.includes("ANULA") && privilegio.Privilegio.split('_')[0].length == 2 ){
                let tipo_orden = privilegio.Privilegio.split('_')[0]                
                this.info.filtroTipoOrden.push(tipo_orden)                    
            }else if(privilegio.Privilegio.includes("ANULA") && privilegio.Privilegio.split('_')[0].length == 3 ){
                let tipo_orden_general = privilegio.Privilegio.split('_')[0]                 
                this.info.filtroTipoOrdenGeneral.push(tipo_orden_general)                    
            }
        }        
    }
}

</script>