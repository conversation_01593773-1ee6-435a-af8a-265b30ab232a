<template>
    <div>
        <form @submit="handleSubmit">
            <DxForm :ref="formTransaccion"   :form-data.sync="formulario" label-mode="floating" style="margin: 5px">
                <DxFormGroupItem>
                    <DxFormGroupItem :col-count="2">
                        <DxFormItem data-field="Cuenta" editor-type="dxSelectBox" :editor-options="{
                                    width: 'auto',
                                    searchEnabled: true,
                                    items: cuentas,
                                    displayExpr: 'Valor',
                                    valueExpr: 'Codigo',
                                    onValueChanged: AsignarSiguienteCheque,
                                }" :validationRules="[{ type: 'required' }]" />

                        <DxFormItem data-field="NumeroBusqueda" editor-type="dxTextBox" v-model="formulario.NumeroBusqueda"  />

                    </DxFormGroupItem>
                    <DxFormGroupItem :col-count="2">
                        <DxFormItem data-field="FechaI" editor-type="dxDateBox" v-model="formulario.FechaI" :editor-options="{
                          
                            valueChangeEvent: 'change',
                            openOnFieldClick: true, 
                            onValueChanged: (e) => {
                                const newDate = e.value ? new Date(e.value) : null;
                                formulario.FechaI = newDate ? newDate.toISOString().split('T')[0] : null;
                            }
                        }" />

                        <DxFormItem data-field="FechaF" editor-type="dxDateBox" v-model="formulario.FechaF" :editor-options="{
                           
                            valueChangeEvent: 'change',
                            openOnFieldClick: true, 
                            onValueChanged: (e) => {
                                const newDate = e.value ? new Date(e.value) : null;
                                formulario.FechaF = newDate ? newDate.toISOString().split('T')[0] : null;
                            }
                        }" />


                    </DxFormGroupItem>
                    <DxFormGroupItem >
                        <DxFormButtonItem :button-options="buttonGrabar" name="Grabar" horizontal-alignment="center" verical-alignment="center" />
                    </DxFormGroupItem>
                    
                </DxFormGroupItem>

                <DxFormGroupItem  v-if="mostrarGroupItem" caption=" ">

                    <DxFormItem  template="Transac" />

                    <DxFormItem template="form"  />

                </DxFormGroupItem>

                <template #Transac="{}">
                    <div>
                        <DxDataGrid
                            :data-source="Transacciones"
                            :show-borders="true"
                            :column-auto-width="true"
                            @rowClick="onRowClick"
                            @contentReady="onContentReady"
                            ref="dataGrid"
                            v-show="visible"
                        >
                            <DxDataGridSelection mode="single" />
                            <DxDataGridColumn caption="Fecha" data-field="Fecha" data-type="date" />
                            <DxDataGridColumn caption="Transacciones" data-field="Numero" />
                            <DxDataGridPaging :page-size="5" />
                        </DxDataGrid>

                        <div style="display: flex; width:100%">
                                <DxButton id="saveButton" style="margin-right: 2px;" :disabled="filaSeleccionadaIndex === 0" icon="fas fa-step-backward"   type="normal" @click="irPrimero" />
                                <DxButton id="saveButtonDos" style="margin-right: 2px;" :disabled="filaSeleccionadaIndex === 0" icon="fas fa-caret-left" type="normal"   @click="irAnterior" />
                                <DxButton id="saveButtonDos" style="margin-right: 2px;" :disabled="filaSeleccionadaIndex === Transacciones.length - 1" icon="fas fa-caret-right"   type="normal" @click="irSiguiente" />
                                <DxButton id="saveButton" style="margin-right: 2px;" icon="fas fa-step-forward"  :disabled="filaSeleccionadaIndex === Transacciones.length - 1"  type="normal"  @click="irUltimo" />

                                <DxButton 
                                    style="margin-right: 2px;" 
                                    icon="fas fa-edit" 
                                    v-if="!modoEdicion && selectedRow && tieneSaldo.SaldoFinal === '1'" 
                                    :width="130" 
                                    text="Modificar" 
                                    type="success" 
                                    @click="Grabar"
                                />

                                <DxButton 
                                    style="margin-right: 2px;" 
                                    icon="fas fa-ban"
                                    v-if="selectedRow  && statusAlertActive === false" 
                                    :width="130" 
                                    text="Anular" 
                                    type="default"
                                    @click="anular()" 
                                />

                                <DxButton 
                                    style="margin-right: 2px;" 
                                    icon="fas fa-window-close"  
                                    :width="130" 
                                    text="Cerrar" 
                                    type="danger" 
                                    @click="limpiarDatos"
                                />
                                <div style="display: flex; justify-content: flex-end; width: 100%;">
                                    <div style="display: flex; justify-content: flex-end; align-items: center;">
                                        <vs-alert style="width: 200px; text-align: center;margin-right: 5px;" :active="respuesta.EstadoConsulta === '1'" color="warning">
                                            Periodo Mayorizado.
                                        </vs-alert>
                                        <vs-alert style="width: 100px; margin-right: 2px; text-align: center;" :active="statusAlertActive" color="danger">
                                            Anulado.
                                        </vs-alert>
                                    </div>
                                </div>
                            </div>

                    </div>
                </template>

                <template #form="{}">
                    <div>
                        <form @submit="handleSubmit">
                            <DxForm :ref="formTransaccion" :read-only="deshabilitarForm" :form-data.sync="formulario" label-mode="floating" style="margin: 5px">
                                <DxFormGroupItem>
                                    <DxFormGroupItem :col-count="3">
                                        <DxFormItem data-field="NumeroModificado" editor-type="dxTextBox" v-model="formulario.NumeroModificado"  />
                                        <DxFormItem data-field="Caja" editor-type="dxTextBox"   />
                                        <DxFormItem data-field="Corte" editor-type="dxTextBox"   />
                                    </DxFormGroupItem>

                                    <DxFormGroupItem :col-count="2">
                                         <DxFormItem data-field="Fecha" editor-type="dxDateBox" v-model="formulario.Fecha"/>
                                         <DxFormGroupItem :col-count="2">
                                            <DxFormItem data-field="Monto" editor-type="dxTextBox" v-model="formulario.Monto" :editor-options="{ disabled: isMontoDisabled }" />                                            
                                            <DxFormButtonItem :button-options="HabilitarButtom" style="color: white;" v-if="HabilitarButtom.visible || respuesta.EstadoConsulta === '1'" horizontal-alignment="center" verical-alignment="center"  />
                                        </DxFormGroupItem>             
                                    </DxFormGroupItem>

                                    <DxFormGroupItem>
                                        <DxFormItem data-field="Tipo" editor-type="dxRadioGroup" :editor-options="{
                                                items: funciones,
                                                displayExpr: 'text',
                                                valueExpr: 'id',
                                                layout: 'horizontal',
                                            }" />

                                    </DxFormGroupItem>
                                    <DxFormGroupItem>
                                        <DxFormItem data-field="Observaciones" editor-type="dxTextArea"  :editor-options="{ height: '100%' }" v-model="formulario.Observaciones" />
                                    </DxFormGroupItem>
                                    <DxFormGroupItem :col-count="5">
                                        <DxFormItem data-field="Efectivo" editor-type="dxTextBox" v-model="formulario.Efectivo" />
                                        <DxFormItem data-field="ChequesLocales" editor-type="dxTextBox" v-model="formulario.ChequesLocales" />
                                        <DxFormItem data-field="ChequesOtros" editor-type="dxTextBox" v-model="formulario.ChequesOtros" />
                                        <DxFormItem data-field="Giros" editor-type="dxTextBox" v-model="formulario.Giros" />
                                        <DxFormButtonItem   :button-options="buttonModificarDetalle" name="Detalle" horizontal-alignment="center" verical-alignment="center" />
                                    </DxFormGroupItem>
                                </DxFormGroupItem>
                            </DxForm>
                            <br>

                            <DxDataGrid :ref="gridDetalle" v-bind="DefaultDxGridConfiguration" :data-source="Vauchers" :headerFilter="{ visible: false, allowSearch: false }" :width="'100%'" height="auto" @editor-preparing="overrideOnValueChanged" @init-new-row="onInitNewRow" @row-removing="onRowRemoving">
                                <!-- Habilitar la edición de celdas -->
                                <DxDataGridEditing mode="cell" :allow-updating="respuesta.EstadoConsulta !== '1' && tieneSaldo.SaldoFinal === '1' && statusAlertActive === false" :allow-adding="respuesta.EstadoConsulta !== '1' && tieneSaldo.SaldoFinal === '1' && statusAlertActive === false" :allow-deleting="respuesta.EstadoConsulta !== '1' && tieneSaldo.SaldoFinal === '1' && statusAlertActive === false" :use-icons="true" />
                                <!-- Selección de fila única -->
                                <DxDataGridSelection mode="single" />
                                <!-- Definir columnas editables -->
                                <DxDataGridColumn :visible="false" width="10%" data-field="Indice" alignment="center" data-type="string" />
                                <DxDataGridColumn width="10%" data-field="Documento" alignment="center" data-type="number" />
                                <DxDataGridColumn width="30%" data-field="Detalle" alignment="center" data-type="string" />
                                <DxDataGridColumn width="10%" data-field="Cuenta" alignment="center" data-type="string" :validation-rules="[{ type: 'required', message: 'El campo Cuenta es obligatorio' }]" />
                                <DxDataGridColumn width="30%" data-field="Nombre" caption="Nombre de la cuenta" alignment="center" data-type="string" :allow-editing="false" />
                                <DxDataGridColumn width="10%" data-field="Debe" alignment="center" data-type="number" :customize-text="customizeTextValores" :editor-options="{ min: 0 }">
                                    <DxDataGridFormat type="decimal" :precision="2" />
                                </DxDataGridColumn>
                                <DxDataGridColumn width="10%" data-field="Haber" alignment="center" data-type="number"  :customize-text="customizeTextValores" :editor-options="{ min: 0 }" />
                                <!-- Sumarios -->
                                <DxDataGridSummary>
                                    <DxDataGridTotalItem column="Debe" summary-type="sum" :customize-text="customizeTextValores" :value-format="{ type: 'fixedPoint', precision: 2 }" />
                                    <DxDataGridTotalItem column="Haber" summary-type="sum" :customize-text="customizeTextValores" />
                                </DxDataGridSummary>
                            </DxDataGrid>

                        </form>
                    </div>
                </template>
            </DxForm>
        </form>
    </div>
</template>

<script>

const formTransaccion = "formTransaccion";
const gridDetalle = "gridDetalle";

import { DefaultDxGridConfiguration } from "./data";


export default {
    name: "ConsultaTransacciones",
  
    data(){
        return {
            formTransaccion,
            gridDetalle,
            DefaultDxGridConfiguration,

            formulario: {
                Cuenta: null,
                FechaI: null,
                FechaF: null,
                Numero: null,
                NumeroModificado:null,
                Caja:0,
                Corte:0,
                Fecha:null,
                Monto:null,
                Efectivo:null,
                Observaciones:null,
                ChequesOtros: null,
                ChequesLocales:null,
                Giros: null,
                Tipo: null,
                TipoO: null,
                NumeroBusqueda:null
            },

            tieneSaldo : {
                SaldoFinal:"",
                Cuenta:null,
                Periodo:null,
                Resultado:null
            },
            isMontoDisabled: false,

            cuentas:[],

            Transacciones:[],
            DetalleDepositos:[],
            Vauchers:[],
            respuesta:{
                EstadoConsulta: ''
            },
            selectedRowIndex: 0, // Índice de la fila seleccionada
            visible: false,
            statusAlertActive: false,  // Controla la visibilidad del alerta

            currentDate: new Date().toJSON().substr(0, 10),

            funciones: [
                {
                    id: "DP",
                    text: "DP",
                },
                {
                    id: "ND",
                    text: "ND ",
                },
                {
                    id: "NC",
                    text: "NC",
                },
                {
                    id: "CV",
                    text: "CV",
                },
                {
                    id: "CA",
                    text: "CA",
                },
                {
                    id: "NH",
                    text: "NH",
                },
                {
                    id:"NP",
                    text:"NP"
                },
                {
                    id:"NA",
                    text:"NA"
                }
            ],
            

            buttonGrabar: {
                width: "auto",
                icon: "fas fa-search-dollar",
                text: "Consultar",
                type: "success",
                onClick: () => {
                    if (this.formulario.NumeroBusqueda === null || this.formulario.NumeroBusqueda === '') {
                        this.limpiar()
                        this.ConsultaInformacionFechas();
                        
                    } else {
                        this.limpiar()
                        this.ConsultaNumero();
                    }
                },
                useSubmitBehavior: false,
            },

            buttonModificarDetalle: {
                width: "auto",
                icon: "fas fa-save",
                text: "Detalle",
                type: "success",
            
                onClick: () => {
                   this.GrabarDetalle()
                },
                useSubmitBehavior: false,
            },

            HabilitarButtom: {
                width: "auto",
                icon: "unlock",
                type: "warning",
                color:"white",
                onClick: () => this.confirmarHabilitacion(),
                useSubmitBehavior: false,
                visible: false, // Inicialmente oculto
            },

            mostrarGroupItem:false,
            filaSeleccionada: 0, // Índice de la fila seleccionada

            filaSeleccionadaIndex: 0,

            selectedRow: null,
            modoEdicion: false
        }
    },
    computed: {
        formattedFechaI() {
            return this.formulario.FechaI ? this.formatoFecha(this.formulario.FechaI) : '';
        },
        formattedFechaF() {
            return this.formulario.FechaF ? this.formatoFecha(this.formulario.FechaF) : '';
        },
        dataGridDetalle: function () {
          return this.$refs[gridDetalle].instance.refresh();
        },
        totalFilas() {
            return this.Transacciones.length;
        },
        deshabilitarForm() {
            return this.invertirValor(this.respuesta.EstadoConsulta)  || (this.tieneSaldo.SaldoFinal === '0' && !this.statusAlertActive);

        }
    },
    watch: {
        'formulario.Monto'(newVal) {
            if (newVal !== 0 && newVal !== '0.00') {
                this.isMontoDisabled = false;
                this.HabilitarButtom.visible = false; // Mostrar botón
            } else {
                this.isMontoDisabled = true;
                this.HabilitarButtom.visible = true; // Ocultar botón
            }
        }
    },
    methods:{

    handleSubmit(e) {
        e.preventDefault();
    },

    activarEdicion() {
        this.modoEdicion = true;
    },
    reiniciar() {
        this.modoEdicion = false;
    },

    formatoFecha(fecha) {
        return new Date(fecha).toISOString().split('T')[0]; // Convierte a YYYY-MM-DD
    },

    CargarCuentas(asignar) {
      // La variable de asignar indica cuando debe seleccionar la primera cuenta de la lista
      this.axios
        .post("/app/v1_bancos/Cuentas", {
          Opcion: 1,
        })
        .then((resp) => {
          this.cuentas = resp.data.json;

          if (asignar) {
            this.AsignarBanco(this.cuentas[0]);
          }
        });
    },

    AsignarSiguienteCheque(e) {
      let cuenta = this.cuentas.find((x) => x.Codigo == e.value);
      this.AsignarBanco(cuenta);
    },

    AsignarBanco(e) {
      //Asignar los valores del banco a los campos correspondientes del formulario
      this.formulario.Cuenta = e.Codigo;
    },

    ConsultaInformacionFechas() {
        this.axios.post("/app/v1_bancos/ConsultasModificacionesTransacciones", {
            Opcion: 1,
            FechaI: this.formulario.FechaI,
            Cuenta: this.formulario.Cuenta,
            FechaF: this.formulario.FechaF
        }).then((resp) => {
            this.Transacciones = resp.data.json;

            // Verificar si 'resp.data.json' tiene datos
            if (this.Transacciones && this.Transacciones.length > 0) {
                this.mostrarGroupItem = true;
            } else {
                this.mostrarGroupItem = false;
                this.$vs.dialog({
                    type: 'alert',
                    color: '#e74c3c',
                    title: 'Valores incorrectos',
                    acceptText: 'Aceptar',
                    text: 'Para poder realizar la consulta debes de colocar Fechas correctas.',
                    buttonCancel: 'border',
                    accept: () => {},
                })
            }
        })
    },

  
    confirmarHabilitacion() {
      this.$vs.dialog({
        type: 'confirm',
        color: 'danger',
        title: 'Advertencia',
        text: '¿Está seguro de modificar el monto?',
        accept: () => {
          this.isMontoDisabled = false;
          this.$vs.notify({
            title: 'Campo habilitado',
            text: 'Ahora puede modificar el monto',
            color: 'success'
          });
        },
        cancel: () => {
          this.$vs.notify({
            title: 'Acción cancelada',
            text: 'El monto seguirá bloqueado',
            color: 'danger'
          });
        }
      });
    },

    ConsultaNumero() {

        const hoy = new Date();
        const fechaLocal = hoy.toISOString().split("T")[0]; // Ajustado para que tome la fecha local correctamente

        this.axios.post("/app/v1_bancos/ConsultasModificacionesTransacciones", {
            Opcion: 2,
            Cuenta: this.formulario.Cuenta,
            Numero: this.formulario.NumeroBusqueda,
            FechaI: this.formulario.FechaI || fechaLocal,  // Usa la fecha actual si está vacío
            FechaF: this.formulario.FechaF || fechaLocal   // Usa la fecha actual si está vacío
        }).then((resp) => {
            this.Transacciones = resp.data.json;

            // Verificar si 'resp.data.json' tiene datos
            if (this.Transacciones && this.Transacciones.length > 0) {
                this.mostrarGroupItem = true;
            } else {
                this.mostrarGroupItem = false;
                this.$vs.dialog({
                    type: 'alert',
                    color: '#e74c3c',
                    title: 'Valores incorrectos',
                    acceptText: 'Aceptar',
                    text: 'Para realizar la consulta, asegúrese de ingresar un número válido o verificar que el tipo de transacción sea el correcto.',
                    buttonCancel: 'border',
                    accept: () => {},
                })
            }
        }).catch(() => {
            this.$vs.dialog({
                    type: 'alert',
                    color: '#e74c3c',
                    title: 'Valores incorrectos',
                    acceptText: 'Aceptar',
                    text: 'Para poder realizar la consulta de colocar un número valido',
                    buttonCancel: 'border',
                    accept: () => {},
                })
        });
    }, 

    onRowClick(e) {
        this.formulario.Efectivo = ''
        this.formulario.ChequesOtros = ''
        this.formulario.ChequesLocales = ''
        this.formulario.Giros = ''

        this.selectedRow = e;
        this.DepostisoDetalle(e)
        this.Vaucher(e)
        this.formulario.NumeroModificado = e.data.Numero;
        this.formulario.Numero = e.data.Numero;
        this.formulario.Caja = e.data.Caja;
        this.formulario.Corte = e.data.Corte;
        this.formulario.Fecha = e.data.Fecha;
        this.formulario.Monto = e.data.Monto
        this.formulario.Observaciones = e.data.Observaciones
        this.formulario.Tipo = e.data.Tipo;
        this.formulario.TipoO = e.data.Tipo;
        this.ValidacionMayorizacion();
        this.validaciones(); 
    },

    ValidacionMayorizacion() {
        this.axios.post("/app/v1_bancos/ValidacionMayorizado", {
            Opcion: 9,
            Fecha: this.formulario.Fecha,
        }).then((resp) => {
            if (resp.data.codigo === 0) {
            this.$map({
                objeto: this.respuesta,
                respuestaAxios: resp,
            });

            }
        })
    },

    invertirValor(valor) {
      if (valor === '0') {
        return false; // Si es '1', devuelve false
      } else if (valor === '1') {
        return true; // Si es '0', devuelve true
      } else if (valor.trim() === '') {
        return true; // Si es una cadena vacía, devuelve true (puedes ajustar este comportamiento)
      }
      return true; // Puedes devolver true por defecto para otros casos si es necesario
    },

    DepostisoDetalle(e) {
        this.axios.post("/app/v1_bancos/ConsultaDepositosDetalle", {
            Opcion: 3,
            Cuenta: this.formulario.Cuenta,
            Numero: e.data.Numero,
            Tipo: e.data.Tipo
        }).then((resp) => {
            // Asumiendo que la respuesta es un array en resp.data.json
            if (resp.data.json && resp.data.json.length > 0) {
                const detalle = resp.data.json[0];  // Accede al primer objeto del array
                this.formulario.Efectivo = detalle.Efectivo;
                this.formulario.ChequesLocales = detalle.ChequesLocales;
                this.formulario.ChequesOtros = detalle.ChequesOtros;
                this.formulario.Giros = detalle.Giros;
            }
        });
    },

    Vaucher(e) {
        this.axios.post("/app/v1_bancos/ConsultaVaucher", {
            Opcion: 4,
            Cuenta: this.formulario.Cuenta,
            Numero: e.data.Numero,
            Fecha: e.data.Fecha,
            Tipo:  e.data.Tipo
        }).then((resp) => {
            if (resp.data.codigo === 0) {
                // Eliminar espacios en cada campo de cada objeto en resp.data.json
                this.Vauchers = resp.data.json.map(item => {
                    return {
                        ...item,  // Copiar todos los atributos
                        Cuenta: item.Cuenta ? item.Cuenta.trim() : item.Cuenta, // Eliminar espacios de Cuenta, si existe
                        Numero: item.Numero ? item.Numero.trim() : item.Numero, // Eliminar espacios de Numero, si existe
                        Detalle: item.Detalle ? item.Detalle.trim() : item.Detalle,
                        Nombre: item.Nombre ? item.Nombre.trim() : item.Nombre,
                        Debe: item.Debe ? item.Debe.trim() : item.Debe
                    };
                });
            }
        });
    },

    onInitNewRow(e) {
      e.data.Debe = '';
      e.data.Haber = '';
    },

    overrideOnValueChanged(e) {
      if (e.dataField === "Cuenta" && e.parentType === "dataRow") {
        const defaultValueChangeHandler = e.editorOptions.onValueChanged;
        e.editorOptions.onValueChanged = (args) => {
          const cuenta = args.value; // Captura el valor ingresado de la cuenta
          this.axios
            .post("/app/v1_bancos/Transacciones", {
              Opcion: 1,
              SubOpcion: 3,
              Contabilidad: cuenta,
            })
            .then((resp) => {
              if (resp.data.codigo == 0) {
                const updatedNombre = resp.data.json[0].Nombre;
                const rowIndex = e.row.rowIndex;
                this.$refs.gridDetalle.instance.cellValue(
                  rowIndex,
                  "Nombre",
                  updatedNombre
                );
                this.$refs.gridDetalle.instance.refresh();
              }
            })
            .catch(() => {
              
            });
          defaultValueChangeHandler(args);
        };
      }
    },

    customizeTextValores(cellInfo) {
        if (cellInfo.value !== null && cellInfo.value !== "" && Number(cellInfo.value) !== 0) {
            const valorRedondeado = Number(cellInfo.value).toFixed(2);
            return "Q. " + valorRedondeado;
        }
        return null;
    },


    async Grabar() {
        const grid = this.$refs.gridDetalle.instance;
        const totalDebe = grid.getTotalSummaryValue("Debe");
        const totalHaber = grid.getTotalSummaryValue("Haber");

        if (totalDebe !== totalHaber) {
            this.$vs.dialog({
            type: 'alert',
            color: '#e74c3c',
            title: 'Error en el voucher',
            acceptText: 'Aceptar',
            text: 'Columnas del voucher no cuadran. Verifique.',
            buttonCancel: 'border',
            customClass: 'custom-dialog',
            accept: () => {}
            });
            return;
        }

        try {

            await this.axios.post("/app/v1_bancos/ActualizarTransaccion", {
            Opcion: 6,
            Cuenta: this.formulario.Cuenta,
            Numero: this.formulario.Numero,
            NumeroModi: this.formulario.NumeroModificado,
            Monto: this.formulario.Monto,
            Tipo: this.formulario.Tipo,       // Tipo actual (modificable)
            TipoO: this.formulario.TipoO,     // Tipo original (se congela la primera vez)
            Caja: this.formulario.Caja,
            Corte: this.formulario.Corte,
            Fecha: this.formulario.Fecha,
            Observaciones: this.formulario.Observaciones
            });

            await this.Grabar1();

        } catch (error) {
            this.$vs.dialog({
            type: 'alert',
            color: '#e74c3c',
            title: 'Error con el guardado',
            acceptText: 'Aceptar',
            text: "Error: El tipo actual del registro no está permitido para modificaciones.",
            buttonCancel: 'border',
            customClass: 'custom-dialog',
            accept: () => {}
            });
        }
    },


    async Grabar1() {       
      const Valor = this.Vauchers;
      const Valores = Valor.map((linea, index) => {
        return {
          Indice: index + 1,
          Documento: linea.Documento,
          Detalle: linea.Detalle,
          Cuenta: linea.Cuenta,
          Nombre: linea.Nombre,
          Debe: linea.Debe,
          Haber: linea.Haber,
        };
      });     
        await this.axios.post("/app/v1_bancos/InsertarVaucherTransac", {
          Opcion: 7,
          CuentaBanco: this.formulario.Cuenta,
          Referencia: this.formulario.Numero,
          Fecha: this.formulario.Fecha,
          Tipo: this.formulario.Tipo,
          Detalle: Valores,
        })
        this.$vs.dialog({
          type: 'alert',
          color: '#27ae60',
          title: 'Transacciones Modificada',
          acceptText: 'Aceptar',
          text: 'Se Grabo Exitosamente la Modificación! Si quieres ver los resultados actualizados recargar la información',
          buttonCancel: 'border',
          accept: () => {
           
          },
        })
    },



  

    GrabarDetalle() {
        const { Efectivo, ChequesLocales, ChequesOtros, Giros } = this.formulario;

        // Convertir strings a números (float)
        const efectivoNum = parseFloat(Efectivo) || 0.00;
        const chequesLocalesNum = parseFloat(ChequesLocales) || 0.00;
        const chequesOtrosNum = parseFloat(ChequesOtros) || 0.00;
        const girosNum = parseFloat(Giros) || 0.00;

        // Validación corregida (ahora compara números)
        const esValido = valor => valor !== null && valor !== undefined && valor !== 0.00;

        // Verificar si AL MENOS UNO tiene valor
        if (
            !esValido(efectivoNum) &&
            !esValido(chequesLocalesNum) &&
            !esValido(chequesOtrosNum) &&
            !esValido(girosNum)
        ) {
            this.$vs.dialog({
                type: 'alert',
                color: '#e74c3c',
                title: 'Error',
                text: 'Ingresa al menos un valor numérico válido.',
                acceptText: 'Aceptar'
            });
            return;
        }



        // --- Enviar al backend (ahora con números) ---
        this.axios.post("/app/v1_bancos/GrabarDetalle", {
            Opcion: 5,
            Cuenta: this.formulario.Cuenta,
            Numero: this.formulario.Numero,
            Tipo: this.formulario.Tipo,
            Efectivo: efectivoNum,
            ChequesLocales: chequesLocalesNum,
            ChequesOtros: chequesOtrosNum,
            Giros: girosNum
});
    },


    
    validaciones(){
        this.axios.post("/app/v1_bancos/ConsultaSalfoFinalModificaciones",{
            Opcion: 8,
            Cuenta: this.formulario.Cuenta,
            Fecha: this.formulario.Fecha
        }).then((resp)=>{
            if(resp.data.codigo === 0){
                this.$map({
                        objeto: this.tieneSaldo,
                        respuestaAxios: resp,
                });
            }
        })
    },

    anular() {
            this.$vs.dialog({
                    type: 'confirm',
                    color: '#e74c3c',
                    title: 'Advertencia',
                    acceptText: 'Anular',
                    cancelText: 'Cancelar',
                    text: `¿Estas Seguro de Anular esta transacción?`,
                    buttonCancel: 'border',
                    clientWidth: 100,
                    accept: () => {
                    
                    this.axios.post("/app/v1_bancos/anular", {
                        Opcion: 10,
                        Cuenta: this.filaSeleccionada.Cuenta,
                        Periodo:  this.filaSeleccionada.Periodo,
                        Tipo:  this.filaSeleccionada.Tipo,
                        Fecha:  this.filaSeleccionada.Fecha,
                        Numero:  this.filaSeleccionada.Numero
                    })
                    .then((resp) => {
                        if(resp.data.codigo === 0){
                        
                            if (this.formulario.NumeroBusqueda === null || this.formulario.NumeroBusqueda === '') {
                                this.ConsultaInformacionFechas();
                                
                            } else {
                                this.ConsultaNumero();
                            }
                        }
                    })
                    .catch(() => {
                      
                    });
                },
                cancel: () => {

                }
            })
    },

    onContentReady() {
        this.$nextTick(() => {
            if (this.Transacciones.length > 0) {
                const dataGrid = this.$refs.dataGrid.instance;
                dataGrid.selectRowsByIndexes([0]); // Selecciona la primera fila
                this.onRowClick({ data: this.Transacciones[0] }); // Llama a la función como si se hiciera clic
                this.seleccionarFila(0);
            }
            
        });
    },


    async onRowRemoving(e) {
        e.cancel = true;
        
        if (!e.data.Indice) {
            const index = this.Vauchers.findIndex(item => item.Indice === e.data.Indice);
            if (index !== -1) {
                this.Vauchers.splice(index, 1);
            }
            
            this.$vs.notify({
                text: 'Eliminación Exitosa.',
                color: 'success',
                icon: 'check_circle'
            });
            return;
        }
        
        try {
            await this.axios.post("/app/v1_bancos/EliminarFila", {
                Opcion: 10,
                Indice: e.data.Indice,
                Tipo: this.formulario.Tipo,
                CuentaBanco: this.formulario.Cuenta,
                Referencia: this.formulario.Numero,
                Periodo: this.filaSeleccionada.Periodo,
            });
            
            const index = this.Vauchers.findIndex(item => item.Indice === e.data.Indice);
            if (index !== -1) {
                this.Vauchers.splice(index, 1);
            }
            
            this.$vs.notify({
                text: 'Eliminación Exitosa.',
                color: 'success',
                icon: 'check_circle'
            });
            
        } catch (error) {
            this.$vs.notify({
                text: 'Error al eliminar el registro',
                color: 'danger',
                icon: 'error'
            });
        }
    },

    seleccionarFila(index) {
    if (index >= 0 && index < this.Transacciones.length) {
        this.filaSeleccionadaIndex = index;
        this.filaSeleccionada = this.Transacciones[index];
        this.statusAlertActive = this.filaSeleccionada.Status === 'A';
        this.$refs.dataGrid.instance.selectRows([this.filaSeleccionada]);

        // Espera a que Vue actualice todo antes de disparar el evento
        this.$nextTick(() => {
        this.onRowClick({ data: this.filaSeleccionada });
        });
    }
    },

    
    irPrimero() {
      this.seleccionarFila(0);
    },
    
    irAnterior() {
            if (this.filaSeleccionadaIndex > 0) {
                this.seleccionarFila(this.filaSeleccionadaIndex - 1);
            }
    },
    irSiguiente() {            
        if (this.filaSeleccionadaIndex < this.Transacciones.length - 1) {
                this.seleccionarFila(this.filaSeleccionadaIndex + 1);
            }
    },
    irUltimo() {
        this.seleccionarFila(this.Transacciones.length - 1);
    },



    limpiarDatos() {
        const hoy = new Date().toISOString().split('T')[0];
        this.formulario.FechaI = hoy;
        this.formulario.FechaF = hoy;
        this.formulario.Numero = null;
        this.formulario.NumeroBusqueda = null;
        this.formulario.Caja = null;
        this.formulario.Corte = null;
        this.formulario.Fecha = null;
        this.formulario.Monto = null;
        this.formulario.Efectivo = '';
        this.formulario.Observaciones = null;
        this.formulario.ChequesOtros = '';
        this.formulario.ChequesLocales = '';
        this.formulario.Giros = '';
        this.formulario.Tipo = '';
        this.Transacciones = [];
        this.DetalleDepositos = [];
        this.tieneSaldo.SaldoFinal = '';
        this.Vauchers = [];
        this.mostrarGroupItem = false;
        this.selectedRow = null;
        this.respuesta.EstadoConsulta = '';
        this.modoEdicion = false;
    },

    
    limpiar(){
            this.formulario.Caja = null
            this.formulario.Corte = null
            this.formulario.Fecha = null
            this.formulario.Monto = null
            this.formulario.Efectivo = ''
            this.formulario.Observaciones = null
            this.formulario.ChequesOtros = ''
            this.formulario.ChequesLocales = ''
            this.formulario.Giros = ''
            this.formulario.Tipo = ''
            this.tieneSaldo.SaldoFinal = ''
            this.Transacciones = []
            this.DetalleDepositos = []
            this.Vauchers = []
            this.mostrarGroupItem = false;
            this.selectedRow = null
            this.respuesta.EstadoConsulta = ''
            this.modoEdicion = false

    }
    
    },

    beforeMount() {
        this.CargarCuentas(true);
    },

    mounted() {
        const today = new Date().toISOString().split('T')[0];
        this.formulario.FechaI = today;
        this.formulario.FechaF = today;
    }

}


</script>
<style>

.dx-datagrid .dx-header-row th,
.dx-datagrid .dx-data-row td {
  text-align: center !important;
}


.custom-icon .vs-icon {
    font-size: 27px !important; /* Aumenta el tamaño del ícono */
}

#saveButton .dx-icon {
    font-size: 17px;
    color: black;

}

#saveButton{
    width: 65px !important;
}

#saveButtonDos .dx-icon {
    font-size: 25px;
    color: black;
}

#saveButtonDos{
    width: 65px !important;
}

</style>