<template>
    <vx-card :title="`Caja Cobros - ${sesion.sesion_sucursal_nombre}`">
        <vs-popup classContent="popup-example" title="Resultado Búsqueda" :active.sync="buscarChequesRemision"  v-on:keydown.esc="$emit('close')" tabindex="0" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px" >

                <form>

                    <vs-divider></vs-divider>
                    <vs-table2 max-items="10" pagination :data="listas.listasCheques" id="tb_departamentos">

                        <template slot="thead">
                            <th>Remesa</th>
                            <th>Banco</th>
                            <th>Nombre</th>
                            <th>Monto</th>
                            <th>Saldo</th>
                            <th>Cheque</th>
                            <th>Cuenta</th>
                            <th></th>
                        </template>

                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">                               
                                <vs-td2 :data="tr.Remesa">
                                    {{tr.Remesa}}
                                </vs-td2>
                                <vs-td2 :data="tr.BancoEmisor">
                                    {{tr.BancoEmisor}}
                                </vs-td2>
                                <vs-td2 :data="tr.Nombre">
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">
                                        {{tr.Nombre}}
                                    </div>
                                </vs-td2>
                                <vs-td2 :data="tr.Monto">
                                    {{tr.Monto}}
                                </vs-td2>
                                <vs-td2 :data="tr.Saldo">
                                    {{tr.Saldo}}
                                </vs-td2>
                                <vs-td2 :data="tr.ChequeNumero">
                                    {{tr.ChequeNumero}}
                                </vs-td2>
                                <vs-td2 :data="tr.CuentaCheque">
                                    {{tr.CuentaCheque}}
                                </vs-td2>

                                <vs-td2 align="right">
                                    <vs-button color="primary" icon-pack="fas" icon="fa-check-circle" style="display:inline-block;margin-right:2px" @click="resolver(tr)"></vs-button>
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
                </form>

            </div>
        </vs-popup>
        <buscador ref="BusquedaFacturaRecibo" buscador_titulo="Buscador / Facturas Admisión"
                            :api="sourceRecibos.facturasPorAdmisionPresentacion" 
                            :campos="['SerieFace','Numero','Serie', 'Codigo','Fecha','Total','Saldo']" 
                            :titulos="['Serie Face', 'Numero Face','Serie','Codigo','Fecha', '#Total','#Saldo']" 
                            :api_cache="true" :api_preload="true" :disabled_texto="true" />
        <vs-row> 
            <ConfiguracionCaja @CargarCaja="CargarCaja" ref="ConfiguracionCajaRef"
                               :FiltrarAgrupacionCaja=FiltrarAgrupacionCaja
                               TipoCajaSeleccionada="TipoCajaFacturacion" CajaSeleccionada="CajaFacturacion" 
                               class="w-full">
            </ConfiguracionCaja>
            <vs-row class="w-full">
                <vs-col class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12 p-2">
                    <vs-row class="w-full">
                        <BusquedaAdmision class="w-full" ref="componenteAdmisiones"   
                                @datos_admision="DatosAdmision"
                                size = "media-fila"
                                @limpiar_datos_admision="LimpiarPantallaAdmision"
                                :api_campos="['Serie','Codigo','Nombres','Apellidos','Habitacion']"
                                :api_titulos="['Serie#','Admision#','Nombres','Apellidos','Habitación']"
                                :api_filtro="{'Opcion':'CONSULTA','SubOpcion':'CAJERO','activo':0,'tieneHabitacion':0}"
                                >                
                        </BusquedaAdmision>            
                    </vs-row>
                    <vs-row class="w-full">
                        <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 pl-2">
                            <div v-if="tipoAdmision !== undefined && tipoAdmision?.trim()?.length > 0">
                                <b>Admisión:</b> {{ tipoAdmision }}
                            </div>
                        </vs-col> 
                        <vs-col class="sm:w-full md:w-full lg:w-8/12 xl:w-8/12 pl-2">
                            <div v-if="info.seguro !== undefined && info.seguro?.trim()?.length > 0">
                                <p><b>Seguro: </b> {{ info.seguro + ' - ' + info.nombreSeguro }}</p>
                            </div>
                        </vs-col>  
                    </vs-row>
                    <vs-row class="w-full">
                        <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1">
                            <vs-input label="Serie" 
                                    v-model="sourceRecibos.serieFactura" 
                                    class="w-full"
                                    :disabled="true"
                                    />   
                        </vs-col>  
                        <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-4/12 p-1">
                            <label class="typo__label" style="color: rgba(0,0,0,.7); font-size: 10pt;">No. Factura&nbsp;</label>
                            <vs-col vs-type="flex" vs-align="center" class="pb-2">
                                <vs-input  v-model=sourceRecibos.numeroFactura :disabled="true" class="w-full"></vs-input>
                                <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="MostrarBusquedaFacturaPorAdmision()" icon="fa-search"></vs-button>                                                                        
                            </vs-col> 
                        </vs-col>
                        <vs-col class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12 p-1 flex" style="color: rgba(0,0,0,.7); font-size: 14pt; align-items: center;">
                            <div v-if="sourceRecibos.serieFel">                     
                                <p><b>{{ sourceRecibos.serieFel + ' - ' + sourceRecibos.numeroFel }}</b></p>
                            </div>
                        </vs-col>              
                    </vs-row>            
                </vs-col>
                <vs-col class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12 p-2">
                    <vs-row class="w-full">
                        <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1" style="height: 60px;">   
                            <label class="typo__label label-shrink" style="white-space: nowrap; color:#626262; font-size:12px;  padding-bottom:2px; padding-top:0px;"> Tipo Documento  </label>  
                            <multiselect 
                                            v-model="documento.tipoDocumento" :custom-label="tipo_documento_seleccionado" 
                                            :options="documento.tiposDocumentoLista"  :allow-empty="false" :showLabels="false"
                                            placeholder="Tipo Doc"                                      
                                            label="Tipo Documento" >
                            </multiselect>      
                        </vs-col>           
                        <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-4/12 p-1">
                            <vs-input label="No. documento" 
                                    v-model="documento.noDocumento" 
                                    class="w-full"
                                    @keydown.enter="ValidacionDocumentoTributario()" @keydown.tab.prevent="ValidacionDocumentoTributario()"
                                    />      
                        </vs-col>  
                        <vs-col class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12 p-1">
                            <vs-input id="idNombre"
                                    label="Nombre"  
                                    class="w-full"
                                    v-model="documento.nombre" />      
                        </vs-col>                                                                                             
                    </vs-row> 
                    <vs-row>
                        <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1">
                            <DxSelectBox    style="margin-top: 20px;"
                                            :items="tipoReciboSelected"
                                            :value="sourceRecibos.tipoRecibo"
                                            :disabled="true"
                                            display-expr="Descripcion"
                                            placeholder="Seleccionar.."
                                            value-expr="Valor"
                                            label="Tipo Recibo"
                            />
                        </vs-col>
                        <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-4/12 p-1">
                            <vs-input label="Dirección"   
                                    class="w-full" 
                                    v-model="documento.direccion" />      
                        </vs-col>  
                        <vs-col class="w-full lg:w-full xl:w-6/12 p-1 flex" style="align-items: center;">
                            <div style="color: rgba(0,0,0,.7); font-size: 18pt;">Saldo: <b>{{ $formato_moneda(totalPago.sumatoriaPago) }}</b></div>
                        </vs-col>   
                    </vs-row>  
                </vs-col>
            </vs-row>
            <vs-row class="w-full p-1">                         
                <DxDataGrid :ref="PagosRef" :data-source="sourceRecibos.recibos" :visible="true"  
                        :hoverStateEnabled="false"
                        @editor-preparing="onCellPrepared"
                         v-bind="DefaultDxGridConfiguration"
                        :showColumnLines="true" :showBorders="true" 
                        :rowAlternationEnabled="true" width="100%" height="250px" 
                        @init-new-row="nuevoRecibo"
                        :word-wrap-enabled="false"
                        @saved="agregarLinea">

                            <DxDataGridToolbar>                                                 
                                <DxDataGridItem location="before"
                                        name="addRowButton"
                                        showText="always"
                                        :options="{
                                            type:'success',
                                            text:'Nuevo',
                                            disabled:this.facturas.length != 1
                                        }"
                                />                                                                                
                            </DxDataGridToolbar>

                            <DxDataGridSelection mode="none" />


                            <DxDataGridEditing :allow-updating="true" :allow-adding="true" :allow-deleting="true" mode="row" />

                            <DxDataGridColumn :width="250" data-field="status" :set-cell-value="actualizarStatus" :visible="true" caption="Status" :allow-editing="true" :allow-filtering="false" >  
                                <DxDataGridLookup :dataSource="sourceRecibos.status" value-expr="Valor" display-expr="Descripcion"  />
                                <DxDataGridRequiredRule/>                                                  
                            </DxDataGridColumn>  
                            <DxDataGridColumn :width="125" data-field="formaDePago" :set-cell-value="actualizarFormaDePago" :visible="true" caption="Forma Pago" :allow-editing="true" :allow-filtering="false">                                                    
                                <DxDataGridLookup :dataSource="sourceRecibos.formasPago" value-expr="Valor" display-expr="Descripcion"  />                                                
                                <DxDataGridRequiredRule  message="La forma de pago es requerida"/>                                                  
                            </DxDataGridColumn>                                              
                            <DxDataGridColumn :width="200" data-field="monto" caption="Monto"  :editor-options="{format:'Q #,##0.00'}" format="Q ##,##0.00" data-type="number" alignment="center">
                                <DxCustomRule message="El monto es mayor al permitido o al saldo de la remesa" type="custom" :validation-callback="validarMonto" :reevaluate="true" />
                                <DxDataGridRangeRule  message="El monto del recibo debe ser mayor a cero" :min="0.01"/>
                                <DxDataGridRequiredRule/>
                            </DxDataGridColumn>
                            <DxDataGridColumn :width="300" data-field="banco" :visible="true" caption="Banco" :allow-editing="true" :allow-filtering="false" >                                                    
                                <DxCustomRule message="El banco es requerido para Tarjeta y Cheque" type="custom" :validation-callback="validarBanco" :reevaluate="true" />
                                <DxDataGridLookup :dataSource="sourceRecibos.listaBancos" value-expr="Codigo" :display-expr="(data)=>data.Codigo.trim()+' - '+data.Nombre.trim()" />
                            </DxDataGridColumn>
                            <DxDataGridColumn :width="150" data-field="previsado" caption="Previsado" >
                                <DxDataGridLookup :dataSource="previsadoLista" /> <!--:allow-clearing="true"-->
                            </DxDataGridColumn>
                            <DxDataGridColumn :width="150" data-field="cheque" caption="Cheque/Slip" :set-cell-value="actualizarCheque" data-type="number" alignment="center" >
                                <DxCustomRule message="Obligatorio para pago con remesa" type="custom" :validation-callback="validarCheque" :reevaluate="true" />
                            </DxDataGridColumn>
                            <DxDataGridColumn :width="150" data-field="cuenta" caption="Cuenta/Num Tarjeta" alignment="center" >
                                <DxCustomRule message="Obligatorio para pago con cheque ó tarjeta" type="custom" :validation-callback="validarCuenta" :reevaluate="true" />
                            </DxDataGridColumn>
                            <DxDataGridColumn :width="150" data-field="autorizacion" caption="Autorización" alignment="center" >
                                <DxCustomRule message="Obligatorio para pago con tarjeta" type="custom" :validation-callback="validarAutorizacion" :reevaluate="true" />
                            </DxDataGridColumn>
                            <DxDataGridColumn :width="150" data-field="tasa" caption="Tasa" data-type="number" alignment="center" />
                            <DxDataGridColumn type="buttons" caption="Acción">
                                <DxDataGridButton name="edit" icon="edit" text="Editar"/>
                                <DxDataGridButton name="delete" type="danger" icon="fas fa-trash" text="Borrar"/>
                                <DxDataGridButton name="save" icon="save" text="Guardar" />
                                <DxDataGridButton name="cancel" type="danger" icon="feather icon-x" text="Cancelar" />
                            </DxDataGridColumn>
                            <DxDataGridSummary>
                                <DxDataGridTotalItem  column="monto" summary-type="sum" value-format="Q ##,##0.00" data-type="number" display-format="Total: {0}"/>
                            </DxDataGridSummary>                          
                </DxDataGrid>
            </vs-row>
            <vs-row class="w-full p-2">
                <vs-row class="w-full">                                                                     
                    <vs-row class="w-full">
                        <vs-col class="sm:w-full md:w-full lg:w-5/12 xl:w-3/12 p-1" >
                            <DxDataGrid v-bind="DefaultDxGridConfiguration" 
                                                :data-source="facturas" :height="200" >
                                <!-- <DxDataGridColumn :width="200" data-field="Material" :disabled="consulta" caption="" alignment="center" :customize-text="customizeText" /> -->
                                <DxDataGridColumn width="150px" data-field="Factura" alignment="center" :calculate-display-value="(rowData)=>rowData.Serie+' '+rowData.Codigo" />
                                <DxDataGridColumn width="100px" data-field="Saldo" caption="Saldo" format="Q ##,##0.00" data-type="number" alignment="right" />
                                <DxDataGridSummary>
                                    <DxDataGridTotalItem  column="Saldo" summary-type="sum"  value-format="Q ##,##0.00" data-type="number" display-format="Saldo Hospital: {0}"/>
                                </DxDataGridSummary>   
                            </DxDataGrid>
                        </vs-col>
                        <vs-col class="sm:w-full md:w-full lg:w-5/12 xl:w-4/12 p-1">
                            <DxDataGrid v-bind="DefaultDxGridConfiguration"                                                
                                                :data-source="cargosAjenos" :height="200" >
                                <DxDataGridColumn width="150px" css-class="fuente-label"  data-field="Medico/Empresa" caption="Médico/Empresa" alignment="center" />
                                <DxDataGridColumn width="150px" data-field="Saldo" caption="Saldo" format="Q ##,##0.00" data-type="number" alignment="right" />
                                <DxDataGridSummary>                                                                
                                    <DxDataGridTotalItem  column="Saldo"  data-type="text" style="font-size: 10pt;" :customize-text="()=>{return 'Saldo Ajenos: ' + $formato_moneda(cargosAjenosDetalle.saldoAjenos)}" />                                                    
                                    <DxDataGridTotalItem  column="Saldo"  data-type="text" style="font-size: 10pt;" :customize-text="()=>{return 'Saldo Ajenos Extra: ' + $formato_moneda(cargosAjenosDetalle.saldoAjenosExtra)}" />                                                   
                                </DxDataGridSummary>   
                            </DxDataGrid>
                        </vs-col> 
                        <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1" style="min-width: 150px; border: 1px solid #ddd; color: black; font-weight: bold; white-space: nowrap;">
                            <div class="w-full p-1">
                                <vs-col class="w-1/2" style="text-align: left">
                                    <div class="pt-4">Hospital:</div>
                                    <div class="pt-2">Cuenta:</div>
                                    <div class="pt-2">AjenosExtra:</div>
                                    <div class="pt-2">- Abonos:</div>
                                    <vs-divider class="p-0 m-0"></vs-divider>
                                    <div >A Pagar:</div>                                                                
                                </vs-col>
                                <vs-col class="w-1/2" style="text-align: right">
                                    <div class="pt-4">{{ $formato_moneda(facturasDetalle.hospital) }}</div>
                                    <div class="pt-2">{{ $formato_moneda(cargosAjenosDetalle.cuentaAjena) }}</div>
                                    <div class="pt-2">{{ $formato_moneda(cargosAjenosDetalle.ajenosExtra) }}</div>
                                    <div class="pt-2">{{ $formato_moneda(facturasDetalle.abonos) }}</div>
                                    <vs-divider class="p-0 m-0"></vs-divider>
                                    <div >{{ $formato_moneda(totalPago.sumatoriaPago) }}</div>                                                                
                                </vs-col>
                            </div> 
                        </vs-col>
                    </vs-row>                                                                                             
                </vs-row>  
            </vs-row>
            <vs-row class = 'w-full pt-5'>
                <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 m-1">
                    <vs-button color="success" class="w-full" id="botonRecibo"
                        :disabled="!configuracion.activarRecibos"
                        :type="reciboSeleccionado?'border':'filled'" 
                        @keyup.tab="reciboSeleccionado=true"
                        v-on:blur="reciboSeleccionado=false"
                        @click="GrabarReciboCobro()">
                        Recibo
                    </vs-button>
                </vs-col>
                <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 m-1">
                    <vs-button color="warning" class="w-full"
                        @click="LimpiarPantalla()">
                        Limpiar
                    </vs-button>
                </vs-col>
            </vs-row>    
        </vs-row>
    </vx-card>
</template>
<script>

import BusquedaAdmision from "/src/components/sermesa/modules/admisiones/BusquedaAdmision.vue";  
import "vue-multiselect/dist/vue-multiselect.min.css"; 
import 'flatpickr/dist/flatpickr.css';
import ConfiguracionCaja from "/src/components/sermesa/modules/caja/ConfiguracionCaja.vue";    
import {DefaultDxGridConfiguration,
        CalcularExencion } from './data';
   
const PagosRef = "PagosRef"

export default{    
    name:'Recibo_Cobros',
    components:{
        BusquedaAdmision,
        ConfiguracionCaja
    },
    data() {
        return{ 
            reciboSeleccionado:false,
            FiltrarAgrupacionCaja:['5'],    
            resolver:function SeleccionarCheque(pago){
                let cheque = 0
                if(pago){
                    cheque = pago               
                }                                                                 
                return cheque
            },
            buscarChequesRemision:false,
            DefaultDxGridConfiguration:{...DefaultDxGridConfiguration, 
                                        searchPanel: {visible: false},
                                        headerFilter:{
                                            visible:false,
                                            allowSearch:false
                                        }},   
            previsadoLista:['S','N'],
            info:{  serie:null,
                    numeroAdmision:null,
                    paciente: null,
                    habitacion: null,                         
                    tipoDescuento:null,
                    seguro:null,
                    nombreSeguro:null,
                    copago:null,
                    coaseguro:null,
                    dataAdmision:null
            },
            documento:{ tiposDocumentoLista:[],
                        tipoDocumento:null,
                        noDocumento:null,
                        nombre:null,
                        direccion:null			
            },	 
            sourceRecibos:{ facturasPorAdmision:[],
                            facturasPorAdmisionPresentacion:[],
                            serieFactura:null,
                            numeroFactura:null,
                            serieFel:null,
                            numeroFel:null,
                            saldoFactura:null,
                            tipoRecibo:'1',
                            status:[],
                            formasPago:[],
                            listaBancos:[],
                            recibos:[]
            },
            listas:{
                listasCheques:[]
            },
            configuracion:{
                cajaSeleccionada:null,
                activarFacturacion: false,
                activarRecibos: false  
            },	
            noCheque:null,            
            tipoReciboSelected:[{Valor:'1',Descripcion:'Cancelación'}],
            PagosRef,
            facturas:[],
            facturasDetalle:{
                hospital:0.00,
                abonos:0.00
            },
            totalPago:{
                sumatoriaPago:0.00,
                restaAbonos:0.00
            },
            cargosAjenos:[],
            cargosAjenosDetalle:{
                cuentaAjena:0.00,
                ajenosExtra:0.00,
                saldoAjenos:0.00,
                saldoAjenosExtra:0.00,
                IVA:0.00
            },
        }
    },
    mounted(){
        this.init()
    },
    activated() {
        window.addEventListener('focus', this.actualizarCajero);   
    },
    deactivated() {
        window.removeEventListener('focus',this.actualizarCajero);
    },
    methods:{
        actualizarCajero(){  
            this.$refs.ConfiguracionCajaRef.CargarCajaInicial();
        },
        async init(){
            this.LimpiarPantalla();
            this.CargarTipoDocumento();
            await this.CargarAgrupaciones('StatusDePago');
            await this.CargarAgrupaciones('FormasDePago');
            await this.CargarAgrupaciones('Bancos',4);  
        },   
        onCellPrepared(e) {
            if (e.dataField == 'banco' && (e.row.data.formaDePago == 'B' || e.row.data.formaDePago == 'N') ) {
                e.editorOptions.dataSource = this.sourceRecibos.listaBancos.filter(banco=>banco.Tipo=='B')
            }else if (e.dataField == 'banco' && (e.row.data.formaDePago == 'T' || e.row.data.formaDePago == 'R' || e.row.data.formaDePago == 'X')) {
                e.editorOptions.dataSource = this.sourceRecibos.listaBancos.filter(banco=>banco.Tipo == e.row.data.formaDePago)
            }else if(e.dataField == 'banco'){
                e.editorOptions.dataSource = []
            }
        },            
        async GenerarRecibo(Serie,NumeroRecibo) {

            let reporte = "Recibo"

            let postData = {
                SerieRecibo:   Serie ,
                NumeroRecibo: NumeroRecibo
            }


            this.$reporte_modal({
                Nombre: reporte,
                Opciones: {
                    ...postData
                },
                Formato:this.FormatoReporte,
                NombreArchivoDescargar:Serie+'_'+NumeroRecibo,
                Descargar:true
            })                

        },  
        CargarCaja(cajaSeleccionada){                
            this.configuracion.cajaSeleccionada   = cajaSeleccionada.Cajero
            this.configuracion.activarFacturacion = cajaSeleccionada.Factura
            this.configuracion.activarRecibos     = cajaSeleccionada.Recibo
        },
        nuevoRecibo(e){ 
            e.data.previsado = 'N'
            e.data.status = 'H'
            e.data.monto = this.facturas.reduce( (total, factura)=> total + this.ConvertirFloat(factura.Saldo),0.00)
                            - this.sourceRecibos.recibos.reduce( (total,recibo) => recibo.status == 'H'? total + recibo.monto : total ,0.00)  
        
        },
        agregarLinea(e){
            if(e.changes && e.changes.length == 1 && e.changes[0].type == "insert"){
                this.$nextTick(() => {
                    setTimeout(() => {
                        this.dataGridPagos.addRow()
                    }, 100)                    
                })
            }             
        },  
        GrabarReciboCobro(){
            if(!this.sourceRecibos.serieFactura && !this.ValidarVariable(this.sourceRecibos.serieFactura)){
                this.Consulta().MostrarError('Seleccione una factura para aplicar el pago',3000)
                return
            }

            if(this.sourceRecibos.recibos.length==0){
                this.Consulta().MostrarError('Ingrese una forma de pago para generar el recibo',3000)
                return
            }
            
            if(!this.configuracion.cajaSeleccionada.SerieRecibo){
                this.Consulta().MostrarError('Seleccionar una caja para realizar el recibo',3000)
                return
            }         

            const botonRecibo = document.getElementById("botonRecibo");
            if(botonRecibo) botonRecibo.blur();

            this.axios.post('/app/v1_caja/GeneracionRecibosHospitales',{
                opcion:'I',
                subOpcion:3,
                empresaSeguro:  this.info.seguro,
                serieAdmision:  this.info.serie,
                numeroAdmision: this.info.numeroAdmision,
                serieFactura:   this.sourceRecibos.serieFactura ,
                numeroFactura:  this.ConvertirEntero(this.sourceRecibos.numeroFactura),
                serieRecibo:    this.configuracion.cajaSeleccionada.SerieRecibo,
                nombreFactura:  this.documento.nombre,
                nombreRecibo:   this.documento.nombre,
                listaRecibos:   this.sourceRecibos.recibos,
                ipoc:0
            }).then(resp=>{
                if(resp.data.codigo==0){
                    this.GenerarRecibo(resp.data.Serie,resp.data.Recibo)
                    this.$vs.dialog({
                                        type: 'confirm',
                                        color: 'success',
                                        title: 'Recibo Caja Cobros',
                                        acceptText: 'Aceptar',
                                        cancelText: 'Cancelar',
                                        text: resp.data.descripcion+' '+resp.data.Serie+'-'+resp.data.Recibo,
                                        accept: () => {this.LimpiarPantalla()},
                                        cancel: () => {this.LimpiarPantalla()}                                               
                                    })
                }
            })
        },
        ActualizarTotales(){
            this.totalPago.sumatoriaPago = this.facturas.reduce((total,f) => total+this.ConvertirFloat(f.Saldo),0.00) 
              + this.ConvertirFloat(this.cargosAjenosDetalle.saldoAjenos) + this.ConvertirFloat(this.cargosAjenosDetalle.saldoAjenosExtra) 

            this.facturasDetalle.hospital = this.facturas.reduce((total,f) => total+this.ConvertirFloat(f.Total),0.00)
           
        },
        async DatosAdmision(datos) {
            this.info.dataAdmision = datos
            this.info.serie = datos.Serie
            this.info.numeroAdmision = datos.Codigo
            this.info.paciente = datos.Paciente
            this.info.habitacion = datos.Habitacion 
            this.info.tipoDescuento = datos.TipoDescuento
            this.info.seguro = datos.Seguro
            this.info.nombreSeguro = datos.Nombre

            this.documento.tipoDocumento = { Valor:4,Descripcion:'NIT'}  //NIT    
            this.documento.direccion = datos.DireccionFactura

            let facturas = await this.CargarFacturasPorAdmision()
            if(facturas){
                //await this.ObtenerFacturasCargosAjenos('FACTURAS',1);
                await this.ObtenerFacturasCargosAjenos('FACTURAS',2);
                await this.ObtenerFacturasCargosAjenos('AJENOS',1);
                await this.ObtenerFacturasCargosAjenos('AJENOS',2);
                this.ActualizarTotales()
            }
        }, 
        async CargarFacturasPorAdmision(){
            let respuesta = 
            await this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision',
                            {opcion:'C',
                             subOpcion:6,   
                             agrupacion:3,  // para traer tipo 1,2,3
                             serieAdmision:this.info.serie,
                             numeroAdmision:this.info.numeroAdmision
                            }
                           )
                        .then(resp=>{ 
                                        if(resp.data.json[0] && resp.data.json[0].codigo && resp.data.json[0].codigo < 0){
                                            this.$vs.dialog({
                                                type: 'confirm',
                                                color: 'danger',
                                                title: 'Caja Cobros',
                                                acceptText: 'Aceptar',
                                                cancelText: 'Cancelar',
                                                text: resp.data.json[0].descripcion,
                                                accept: () => {this.LimpiarPantalla()},
                                                cancel: () => {this.LimpiarPantalla()}                    
                                            })
                                            return false;
                                        }

                                        this.sourceRecibos.facturasPorAdmision = resp.data.json
                                        this.sourceRecibos.facturasPorAdmisionPresentacion = JSON.parse(JSON.stringify(this.sourceRecibos.facturasPorAdmision))
                                        this.sourceRecibos
                                            .facturasPorAdmisionPresentacion
                                            .map((factura) =>{
                                                    factura.Fecha = factura.Fecha.slice(0, 10)
                                                    factura.Total = this.$formato_moneda(factura.Total)
                                                    factura.Saldo = this.$formato_moneda(factura.Saldo)
                                                    return factura
                                                })     

                                        return this.sourceRecibos.facturasPorAdmisionPresentacion.length > 0;                          
                                    }
                             )
                            .catch( () => {return false;})
            return respuesta;
        },  
        async ObtenerFacturasCargosAjenos(SubOpcion,ipoc){
            var subOpcionNumero = 0;                        
            if(SubOpcion == 'FACTURAS'){
                subOpcionNumero = 50;
            }else if (SubOpcion == 'AJENOS'){
                subOpcionNumero = 51;
            }else{
                return;
            }

            await this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision',
                            {opcion:'I',
                                subOpcion:subOpcionNumero,
                                serieAdmision:this.info.serie,
                                numeroAdmision:this.info.numeroAdmision,
                                ipoc                        
                            })                                  
                        .then(resp=>{
                                if(resp.data.codigo == 0){
                                    if(SubOpcion == 'FACTURAS' && ipoc == 1){
                                        this.facturas = resp.data.json;
                                    }else if(SubOpcion == 'FACTURAS' && ipoc == 2){
                                        //this.facturasDetalle.hospital = resp.data.json.length > 0 ? resp.data.json[0].Hospital : 0.00
                                        this.facturasDetalle.abonos = resp.data.json.length > 0 ? resp.data.json[0].Abonos : 0.00
                                    }else if (SubOpcion == 'AJENOS' && ipoc == 1){
                                        this.cargosAjenos = resp.data.json
                                    }else if (SubOpcion == 'AJENOS' && ipoc == 2){
                                        this.cargosAjenosDetalle.cuentaAjena = resp.data.json.length > 0 ? resp.data.json[0].CuentaAjena : 0.00
                                        this.cargosAjenosDetalle.ajenosExtra = resp.data.json.length > 0 ? resp.data.json[0].AjenosExtra : 0.00

                                        this.cargosAjenosDetalle.saldoAjenos = resp.data.json.length > 0 ? resp.data.json[0].SaldoAjenos : 0.00
                                        this.cargosAjenosDetalle.saldoAjenosExtra = resp.data.json.length > 0 ? resp.data.json[0].SaldoAjenosExtra : 0.00
                                        this.cargosAjenosDetalle.IVA = resp.data.json.length > 0 ? resp.data.json[0].IVA : 0.00
                                    }
                                }
                            })
        },
        MostrarBusquedaFacturaPorAdmision(){
            if(!this.ValidarVariable(this.info.serie) || 
               !this.ValidarVariable(this.info.numeroAdmision)
              ){
                this.Consulta().MostrarError('Ingrese una admisión valida para realizar la busqueda de facturas.',3000)
                return
            }

            if(this.sourceRecibos.facturasPorAdmision.length <= 0){
                this.Consulta().MostrarError('La admisión no cuenta con facturas con saldo para esta sucursal.',4000)
            }else{
                this.$refs.BusquedaFacturaRecibo.iniciar((factura) => {
                                if(factura){
                                    let facturaFind = 
                                    this.sourceRecibos.facturasPorAdmision.find((facturaF)=> facturaF.Serie==factura.Serie && facturaF.Codigo == factura.Codigo)                                                           
                                    this.sourceRecibos.serieFactura  = facturaFind.Serie
                                    this.sourceRecibos.numeroFactura = facturaFind.Codigo
                                    this.sourceRecibos.serieFel      = facturaFind.SerieFace
                                    this.sourceRecibos.numeroFel     = facturaFind.Numero
                                    this.sourceRecibos.saldoFactura  = facturaFind.Saldo

                                    this.documento.tipoDocumento     = { Valor:4,Descripcion:'NIT'} 
                                    this.documento.noDocumento       = factura.Nit
                                    this.documento.nombre            = factura.Nombre

                                    this.facturas = [facturaFind]                                    
                                }else{
                                    this.sourceRecibos.serieFactura  = null
                                    this.sourceRecibos.numeroFactura = null
                                    this.sourceRecibos.serieFel      = null
                                    this.sourceRecibos.numeroFel     = null
                                    this.sourceRecibos.saldoFactura  = null
                                    this.facturas = []
                                }
                                this.sourceRecibos.recibos = []
                                this.ActualizarTotales()
                            })

            }
        },
        async CargarPagosPorNoCheque(noCheque){
            let saldo = 
            await this.axios.post('/app/v1_caja/ConsultaRecibosHospitales',{
                opcion:'C',
                subOpcion:'4',
                noCheque:noCheque
            }).then(resp => {
                if(resp.data.json && resp.data.json.length == 1 ){
                    if(this.parseNumber(resp.data.json[0].Saldo) > 0){
                       return resp.data.json[0]
                    }else{
                        this.Consulta().MostrarError('No cuenta con saldo en la remesa',3000)
                        return 0
                    }

                }else if(resp.data.json.length == 0){
                    this.Consulta().MostrarError('No se encontro el no de cheque ingresado',3000)
                    return 0                    
                }else{
                    this.listas.listasCheques = resp.data.json
                    return -1 
                }
            }).catch(()=>{return 0})
            
            if(saldo==-1){
                saldo = await this.prometerSeleccionCheque()
                saldo.seleccion = 1
                this.buscarChequesRemision = false 
            }                   

            return saldo
        },
        prometerSeleccionCheque(){
            return new Promise(resolve => {
                this.buscarChequesRemision = true           
                this.resolver = resolve//aqui es donde se asigna la fución de resolve...
            })
        }, 
        async CargarAgrupaciones(agrupacion, subOpcion=2, tipoPago ='B'){
            await this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision', {
                                opcion:'C',
                                subOpcion:subOpcion,
                                agrupacion,
                                tipoPago
                            })
                        .then(resp => {                            
                                if (resp.data && resp.data.json.length > 0){
                                    if(agrupacion == 'StatusDePago'){
                                        this.sourceRecibos.status = resp.data.json.filter(status => status.Valor == 'H' || status.Valor == 'E');
                                    }
                                    if(agrupacion == 'FormasDePago'){
                                        this.sourceRecibos.formasPago = resp.data.json.filter(forma => forma.Valor == 'B' || forma.Valor == 'R' || forma.Valor == 'T' || forma.Valor == 'X');                                        
                                    }
                                    if(agrupacion == 'Bancos'){                                        
                                        if(this.sourceRecibos.listaBancos && this.sourceRecibos.listaBancos.length>0){                                                
                                            this.sourceRecibos.listaBancos.push(...resp.data.json)                                                
                                        }else{
                                            this.sourceRecibos.listaBancos = resp.data.json;
                                        }                                                                                  
                                    }
                                }
                            }
                        )
        },   
        CargarTipoDocumento(){
            this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision', { opcion:'C',
                                                                          subOpcion:2,
                                                                          agrupacion:'TipoReceptor'
                                                                        }
                           )
                      .then(resp => {                            
                                if (resp.data && resp.data.json.length > 0){
                                        this.documento.tiposDocumentoLista = resp.data.json;
                                    }
                                }
                            )
        },  
        ValidacionDocumentoTributario(){
            if(this.Consulta().EsVacia(this.documento.tipoDocumento)){
                this.Consulta().MostrarError('Seleccione un tipo de documento');
                return;
            }
            if(this.Consulta().EsVacia(this.documento.noDocumento)){
                this.Consulta().MostrarError('Ingrese el numero de documento');
                return;
            }
            this.axios.post('/app/v1_caja/ValidacionDocumentoTributario',{
                                tipoReceptor: parseInt(this.documento.tipoDocumento.Valor),
                                documento: this.documento.noDocumento
                            })
                        .then(resp=>{
                                if ( resp.data.codigo == 0 && resp.data.json.length > 0 && resp.data.json[0].codigo == 0){
                                    this.documento.nombre = resp.data.json[0].NOMBRE
                                    this.documento.correo = resp.data.json[0].EMAIL
                                    this.documento.direccion = resp.data.json[0].DIRECCION
                                    const campoNombre = document.getElementById("idNombre");
                                    if(campoNombre) campoNombre.focus();
                                }else if ( resp.data.codigo == 0 && resp.data.json.length > 0 && resp.data.json[0].codigo > 0){
                                    this.Consulta().MostrarError(resp.data.json[0].descripcion);
                                    this.documento.noDocumento = null
                                }
                                else if(resp.data.json.length == 0){
                                    this.Consulta().MostrarError('No se encontraron datos para el documento ingresado');
                                    this.documento.noDocumento = null
                                }
                            })
                        
        },       
        ConvertirEntero(Valor){
            let conversion = parseInt(Valor)
            if(isNaN(conversion)){
                return 0
            }
            return conversion;
        },
        ConvertirFloat(Valor,Decimales=2){
            let conversion = parseFloat(Valor?Valor:0.00)
            if(isNaN(conversion)){
                return 0.00
            }
            return parseFloat(conversion.toFixed(Decimales))
        },
        ValidarVariable(entrada){   
            if(typeof(entrada) == 'number'){
                return entrada ? true : false;
            }else if(typeof(entrada) == 'string'){
                return entrada && entrada.length > 0 ? true : false;
            }else if(typeof(entrada) == 'object'){
                return entrada ? true : false;
            }            
            return false;
        },        
        Consulta(){
            return{
                EsVacia:(variable)=>{
                    return variable?false:true;
                },
                MostrarError:(mensaje,tiempo=2000)=>{
                    this.$vs.notify({
                        time:tiempo,
                        title: 'Cajero',
                        color: 'danger',
                        text:mensaje,
                        position: 'top-center'
                    })
                },
                MostrarSatisfactorio:(mensaje)=>{
                    this.$vs.notify({
                        time:2000,
                        title: 'Cajero',
                        color: 'success',
                        text:mensaje,
                        position: 'top-center'
                    })
                }
            }
        },
        parseNumber(entrada){
            let numero = 0
            if(typeof entrada == "string"){
                if(Number.isNaN(Number(entrada))) numero = 0
                else numero = Number(entrada)
            }else  if(typeof entrada == "number"){
                numero = entrada
            }
            return numero
        },
        tipo_documento_seleccionado(tipoDocumento){
            return `${tipoDocumento.Descripcion}`
        },
        LimpiarPantalla(){
            this.$refs.componenteAdmisiones.limpiar_campos() // por el emit se ejecuta LimpiarPantallaAdmision
        },
        LimpiarPantallaAdmision(){
            this.info.serie             = null
            this.info.numeroAdmision    = null
            this.info.paciente          = null
            this.info.habitacion        = null     
            this.info.dataAdmision      = null       
            this.info.tipoDescuento     = null
            this.info.seguro            = null
            this.info.nombreSeguro      = null


            this.documento.tipoDocumento = { Valor:4,Descripcion:'NIT'}  //NIT    
            this.documento.noDocumento   = null
            this.documento.nombre        = null
            this.documento.direccion     = null            

            this.sourceRecibos.facturasPorAdmision = []
            this.sourceRecibos.facturasPorAdmisionPresentacion = []
            this.sourceRecibos.recibos = []
            this.sourceRecibos.serieFactura        = null
            this.sourceRecibos.numeroFactura       = null
            this.sourceRecibos.serieFel            = null
            this.sourceRecibos.numeroFel           = null
            this.sourceRecibos.saldoFactura        = null    
            
            this.facturas = []
            
            this.facturasDetalle.hospital = 0.00
            this.facturasDetalle.abonos = 0.00            
            this.totalPago.sumatoriaPago = 0.00
            this.totalPago.restaAbonos = 0.00            
            this.cargosAjenos=[]
            
            this.cargosAjenosDetalle.cuentaAjena = 0.00,
            this.cargosAjenosDetalle.ajenosExtra = 0.00,
            this.cargosAjenosDetalle.saldoAjenos = 0.00,
            this.cargosAjenosDetalle.saldoAjenosExtra = 0.00,
            this.cargosAjenosDetalle.IVA = 0.00
                  
        },
        validarMonto(linea){
            let montoMaximo = 0.00       
            let montoValido = true   

            if(this.sourceRecibos.tipoRecibo == 'A'){
                montoValido = true;
            }else if(this.reciboPorFactura && this.sourceRecibos.saldoFactura){
                montoMaximo = this.sourceRecibos.saldoFactura
                                - this.sourceRecibos.recibos.reduce( (total,recibo) => total + this.ConvertirFloat(recibo.monto) ,0.00)
                if(linea.data.monto > montoMaximo ) montoValido = false 
            }else if(linea.data.status == 'H'){                    
                montoMaximo = this.facturas.reduce( (total, factura)=> total + this.ConvertirFloat(factura.Saldo),0.00) 
                                - this.sourceRecibos.recibos
                                        .reduce( (total,recibo) => recibo.status == 'H' && recibo.__KEY__ != linea.data.__KEY__ ? total + this.ConvertirFloat(recibo.monto) : total ,0.00)
                if(linea.data.monto > montoMaximo ) montoValido = false                                    
            }else if(linea.data.status == 'A'){                    
                montoMaximo = this.cargosAjenosDetalle.saldoAjenos
                                - this.sourceRecibos.recibos
                                        .reduce( (total,recibo) => recibo.status == 'A' && recibo.__KEY__ != linea.data.__KEY__? total + this.ConvertirFloat(recibo.monto) : total ,0.00)
                if(linea.data.monto > montoMaximo ) montoValido = false                    
            }else if(linea.data.status == 'E'){   
                montoMaximo = this.cargosAjenosDetalle.saldoAjenosExtra
                                - this.sourceRecibos.recibos
                                        .reduce( (total,recibo) => recibo.status == 'E' && recibo.__KEY__ != linea.data.__KEY__? total + this.ConvertirFloat(recibo.monto) : total ,0.00)
                if(linea.data.monto > montoMaximo ) montoValido = false                                       
            }

            if(linea.data.formaDePago == 'B' && (!linea.data.saldo || linea.data.monto > linea.data.saldo)) montoValido = false                    

            return montoValido
        },
        validarBanco(linea){
            let bancoValido = true;
            if( (linea.data.formaDePago == 'T' || linea.data.formaDePago == 'B' || linea.data.formaDePago == 'N')){
                if(!linea.data.banco || linea.data.banco.trim() == ''){
                    bancoValido = false
                }                    
            }  
            return bancoValido              
        },
        validarCheque(linea){
            let respuesta = true;
            if((linea.data.formaDePago=='B' || linea.data.formaDePago=='N') && (!linea.data.cheque || linea.data.cheque=='')){
                respuesta = false
            }
            return respuesta;
        },
        validarCuenta(linea){            
            let respuesta = true;
            if(linea.data.formaDePago=='B' || linea.data.formaDePago=='T' || linea.data.formaDePago=='N'){
                if(!linea.data.cuenta || linea.data.cuenta==''){
                    respuesta = false
                }
            }
            return respuesta;
        },
        validarAutorizacion(linea){
            let respuesta = true;
            if(linea.data.formaDePago=='T' && (!linea.data.autorizacion || linea.data.autorizacion.trim()=='')){
                respuesta = false
            }
            return respuesta;
        },            
        setValorBanco(fila,value){
            let banco = this.sourceRecibos.listaBancos.filter(banco=>banco.Codigo==value) 
            fila.banco = value
            fila.Banco = banco.Codigo + '-' + banco.Nombre
        },
        actualizarFormaDePago(fila,value, filaActual){
            fila.formaDePago = value
            fila.banco = null 
            fila.cheque = null 
            fila.cuenta = null 
            this.actualizarMonto(fila,value,filaActual.status)               
        },
        actualizarStatus(fila, value, filaActual){
            fila.status = value      
            this.actualizarMonto(fila,filaActual.formaDePago, value)                          
        },
        actualizarMonto(fila, formaDePago, status){                
            let montoBase = 0.00
            let valorIva = this.ConvertirFloat(this.cargosAjenosDetalle.IVA)/100 

            if(this.reciboPorFactura && this.sourceRecibos.saldoFactura){
                montoBase = this.sourceRecibos.saldoFactura
                fila.monto =
                formaDePago && formaDePago == 'X' ? CalcularExencion(montoBase,valorIva)
                                        : montoBase - this.sourceRecibos.recibos.reduce( (total,recibo) => total + this.ConvertirFloat(recibo.monto) ,0.00)                  
            }else if(status == 'H'){                    
                montoBase = this.facturas.reduce( (total, factura)=> total + this.ConvertirFloat(factura.Saldo),0.00)       
                fila.monto =           
                formaDePago && formaDePago == 'X' ? CalcularExencion(montoBase,valorIva)
                                        : montoBase - this.sourceRecibos.recibos.reduce( (total,recibo) => recibo.status == 'H'? total + this.ConvertirFloat(recibo.monto) : total ,0.00)      
            }else if(status == 'A'){       
                montoBase = this.cargosAjenosDetalle.saldoAjenos      
                fila.monto =       
                formaDePago && formaDePago == 'X' ? CalcularExencion(montoBase,valorIva)
                                        : montoBase - this.sourceRecibos.recibos.reduce( (total,recibo) => recibo.status == 'A'? total + this.ConvertirFloat(recibo.monto) : total ,0.00)
            }else if(status == 'E'){ 
                montoBase = this.cargosAjenosDetalle.saldoAjenosExtra   
                fila.monto =        
                formaDePago && formaDePago == 'X' ? CalcularExencion(montoBase,valorIva)
                                        : montoBase - this.sourceRecibos.recibos.reduce( (total,recibo) => recibo.status == 'E'? total + this.ConvertirFloat(recibo.monto) : total ,0.00)                                   
            }else{
                fila.monto = null
            }
        },
        async actualizarCheque(fila, value,filaActual){
            if(filaActual.formaDePago != 'B'){
                fila.cheque = value
                return
            }

            let datosRemesa = await this.CargarPagosPorNoCheque(value)
            if(datosRemesa == 0){
                fila.cheque = ''
                fila.saldo = 0
                fila.cuenta = ''
            }else if(datosRemesa.seleccion == 1){
                let fila = this.dataGridPagos.getVisibleRows().find(fila => fila.isEditing)
                fila.data.cheque = value
                fila.data.saldo = datosRemesa.Saldo
                fila.data.cuenta = datosRemesa.CuentaCheque               
            }else{
                fila.cheque = value
                fila.saldo = datosRemesa.Saldo
                fila.cuenta = datosRemesa.CuentaCheque
            }                
        },
    },
    computed:{
        tipoAdmision(){
            return this.info.tipoDescuento == 'N' ? 'Privada' : this.info.tipoDescuento == 'R' ? 'Reingreso' : 
                    this.info.tipoDescuento == 'Q' ? 'Paquete' : this.info.tipoDescuento == 'S' ? 'Seguro' :
                    this.info.tipoDescuento == 'E' ? 'Empleado' : ''
        },
        dataGridPagos() {
            return this.$refs['PagosRef'].instance
        },
        sesion() {
            return this.$store.state.sesion
        }
    }
}
</script>
<style src="./styles_cajero.css"></style>