<template>
    <div>
        <buscador ref="BuscarOrden" buscador_titulo="Buscador / Listado de Ordenes de Laboratorio"
            :api="'app/v1_JefeCaja/ListaOrdenesDiagnostico'"
            :campos="['Apellido', 'ApellidoCasada', 'Nombre', 'TipoOrden', 'Orden', 'Fe<PERSON>', 'ApellidoMedico', 'NombreMedico']"
            :titulos="['Apellido', 'Apellido Casada', 'Nombre', 'Tipo Orden', 'Orden', '<PERSON>cha', 'Apellido Medico', 'Nombre Medico']"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" />

        <vx-card title="Resultados de Laboratotio">
            <vs-popup classContent="popup-example" title="Enviar Resultado" :active.sync="show_newEdit">
                <ValidationObserver ref="formValidate" v-slot="{ handleSubmit }" mode="lazy">
                    <form @submit.prevent="handleSubmit(submitForm(reporte.opciones))">
                        <div class="flex flex-wrap">
                            <vs-row>


                                <vs-col vs-type="flex" vs-w="6">
                                    <div class="w-4/5  p-2">
                                        <validationProvider name="Destinatario">
                                            <vs-input v-model="datosResultado.EnviarA" label="Destinatario"
                                                class="w-full" />
                                        </validationProvider>
                                    </div>
                                </vs-col>


                                <vs-col vs-type="flex" vs-w="6">
                                    <div class="w-4/5  p-2">
                                        <validationProvider name="Copiar a">
                                            <vs-input v-model="datosResultado.CopiarA" label="Copiar a" class="w-full" />
                                        </validationProvider>
                                    </div>
                                </vs-col>

                            </vs-row>

                            <vs-row>

                                <vs-col vs-type="flex" vs-w="12">
                                    <div class="w-full  p-2">
                                        <validationProvider name="Asunto">
                                            <vs-input v-model="datosResultado.Asunto" label="Asunto" class="w-full" />
                                        </validationProvider>
                                    </div>
                                </vs-col>
                            </vs-row>

                            <br>
                            <vs-row>

                                <vs-col vs-type="flex" vs-w="12">
                                    <div class="w-full  p-2">
                                        <vue-editor v-model="datosResultado.MensajeDescripcion"
                                            :editorToolbar="customToolbar"></vue-editor>
                                        <hr>
                                    </div>
                                </vs-col>
                            </vs-row>

                            <vs-row>
                                <vs-button color="success" v-if="type_newEdit == 1" type="border" style="float:right" submit
                                    @click="handleSubmit(EnviarReultado(reporte.opciones))">Enviar</vs-button>
                            </vs-row>
                        </div>
                    </form>
                </ValidationObserver>

            </vs-popup>
            <vs-popup id="contentreport" title="Editar Edad" :active.sync="editaredad" style="z-index:99998;">
                <div class="flex flex-wrap">
                    <div class="w-4">
                        <datepicker :inline="true" :language="languages['es']" v-model="nuevaFechaNacimiento"
                            :format="'yyyy-MM-dd'"></datepicker>
                    </div>
                </div>
                <vs-divider></vs-divider>
                <div style="text-align:right;margin:5px 0;padding:5px ">
                    <vs-button right @click="guardar_edad()" :disabled="!datosResultado.Edad">Guardar</vs-button>
                </div>
            </vs-popup>


            <div class="container_div">


                <div class="lside">

                    <div class="container mt-4">

                        <vs-row>

                            <div class="pl-4 pb-3">
                                <vx-input-group>
                                    <template slot="prepend">
                                        <div class="vx-input-group-prepend">

                                        </div>
                                        <validationProvider name="Tipo" class="required" rules="required|min:3"
                                            v-slot="{ errors }">

                                            <vs-input
                                              label="Tipo Orden"
                                              class="w-full"
                                              v-on:keyup="TipoOrden = TipoOrden?.toUpperCase() || ''"
                                              v-model.trim="TipoOrden"
                                              :danger="errors.length > 0"
                                              :danger-text="(errors.length > 0) ? errors[0] : null"
                                              :maxlength="10" 
                                              @keyup.enter="ConsultaResultados().ConsultaInicialOrden()"
                                              @blur="ConsultaResultados().ConsultaInicialOrden()" />

                                        </validationProvider>
                                        <validationProvider name="Orden" class="required" rules="required|min:5"
                                            v-slot="{ errors }">

                                            <vs-input
                                              label="Número"
                                              class="w-full input-numero input-field pl-2"
                                              v-model="NumeroOrden"
                                              :danger-text="(errors.length > 0) ? errors[0] : null"
                                              :maxlength="10"
                                              @keyup.enter="ConsultaResultados().ConsultaInicialOrden()"
                                              @blur="ConsultaResultados().ConsultaInicialOrden()"
                                              >
                                            </vs-input>

                                        </validationProvider>

                                        <vs-button class="mt-6" id="button-with-loading" color="primary" icon-pack="feather"
                                            icon="icon-search" @click="BuscarOrden()"></vs-button>


                                        <span class="rounded-lg feather-icon select-none relative ml-4 pt-8"
                                            style="color:red">{{ datosResultado.DescripcionOrden }}</span>

                                    </template>
                                </vx-input-group>
                            </div>
                        </vs-row>


                        <vs-row>

                            <vs-col vs-type="flex-wrap" vs-w="2" class="pl-2">
                                <validationProvider name="Admision">
                                    <vs-input label="Admisión" class="w-full p-1" v-model="datosResultado.Admision"
                                        readonly />
                                </validationProvider>
                            </vs-col>


                            <vs-col vs-type="flex-wrap" vs-w="6" class="pl-2">

                                <validationProvider name="NombrePaciente">
                                    <vs-input label="Nombre del paciente" class="w-full p-1"
                                        v-model="datosResultado.NombrePaciente" readonly />
                                </validationProvider>
                            </vs-col>

                            <vs-col vs-type="flex-wrap" vs-w="2" class="pl-2">
                                <label class="vs-input--label">Edad</label>
                                <vx-input-group class="w-full">
                                    <vs-input class="w-full" v-model="datosResultado.Edad" disabled readonly />
                                    <!-- {{info}} -->
                                    <template slot="append">
                                        <div class="append-text btn-addon">
                                            <vs-button v-if="$validar_privilegio('EDITAREDAD').status"
                                                id="button-with-loading-3" color="primary" icon-pack="feather"
                                                @click="editar_edad()" icon="icon-edit"></vs-button>
                                        </div>
                                    </template>
                                </vx-input-group>
                            </vs-col>


                        </vs-row>

                        <vs-row>

                            <vs-col vs-type="flex-wrap" vs-w="4" class="pl-2">
                                <div class="w-full  p-2" vs-w="8">
                                    <validationProvider name="Medico">
                                        <vs-input label="Médico" class="w-full" v-model="datosResultado.Medico" readonly />
                                    </validationProvider>
                                </div>
                            </vs-col>

                            <vs-col vs-type="flex-wrap" vs-w="6" class="pl-0">
                                <div class="w-full  p-2" vs-w="8">
                                    <validationProvider name="Registro">
                                        <vs-input label="Registro" class="w-full" v-model="datosResultado.Registro"
                                            readonly />
                                    </validationProvider>
                                </div>
                            </vs-col>
                        </vs-row>


                        <vs-row>

                            <vs-col vs-type="flex" vs-w="10">
                                <div id="OpcionExamen" class="w-full  p-2" vs-w="8">
                                    <validationProvider name="Examen">
                                        <vs-input v-model="datosResultado.CodExamen" class="w-full p-2" type="hidden" />
                                        <SM-Buscar v-if="datosResultado.Orden > 0" v-model="datosResultado.CodExamen"
                                            class="pl-4 pr-4" label="Nombre del examen" type="input" id="OpExamen"
                                            api="app/laboratorio/BusquedaPorExamen"
                                            api_campo_respuesta_mostrar="NombreExamen"
                                            :api_campos="['CodExamen', 'NombreExamen', 'CodigoExamen']"
                                            :api_titulos="['CodExamen', 'NombreExamen', 'CodigoExamen']"
                                            api_campo_respuesta="CodExamen" :api_preload="true" :disabled_texto="true"
                                            :api_filtro="{
                                                TipoOrden: this.datosResultado.Tipo,
                                                Orden: this.datosResultado.Orden,
                                                LineaResultado: 0,
                                                TipoConsulta: 'C'
                                            }" :callback_buscar="ConsultaResultados().ReiniciarBusquedaExamen"
                                            :callback_cancelar="ConsultaResultados().ReiniciarBusquedaExamen" />
                                    </validationProvider>

                                </div>

                            </vs-col>

                        </vs-row>
                        <vs-row>
                            <vs-col vs-type="flex" vs-w="10">
                                <div id="OpcionSeccion" class="w-full" vs-w="12">
                                    <validationProvider name="Seccion">

                                        <vs-input v-model="datosResultado.Seccion" class="w-full p-2" type="hidden" />

                                        <SM-Buscar v-if="datosResultado.Orden > 0" v-model="datosResultado.Seccion"
                                            class="pl-4 pr-4 pb-4" label="Nombre de la Sección"
                                            api="app/laboratorio/BusquedaPorSeccion"
                                            api_campo_respuesta_mostrar="NombreSeccion"
                                            :api_campos="['CodExamen', 'NombreSeccion', 'CodigoExamen']"
                                            :api_titulos="['CodExamen', 'NombreSeccion', 'CodigoExamen']"
                                            api_campo_respuesta="CodExamen" :api_preload="true" :disabled_texto="true"
                                            :api_filtro="{
                                                TipoOrden: this.datosResultado.Tipo,
                                                Orden: this.datosResultado.Orden,
                                                LineaResultado: 0,
                                                TipoConsulta: 'S'
                                            }" :callback_buscar="ConsultaResultados().ReiniciarBusquedaSeccion"
                                            :callback_cancelar="ConsultaResultados().ReiniciarBusquedaSeccion" />

                                    </validationProvider>
                                </div>

                            </vs-col>
                        </vs-row>
                    </div>
                </div>

                <div class="rside">

                    <div class="container">

                        <vs-row>
                            <vs-col>
                                <div class="example ex1">
                                    <h3>Selección impresión Estado de exámenes</h3>
                                    <br>
                                    <br>
                                    <label class="radio blue pt-2">
                                        <input v-model="datosResultado.EstadoExamen" type="radio" name="group1" value="A"
                                            @change="ConsultaResultados().ActualizarDatosBusqueda()" />
                                        <span>Todos</span>
                                    </label>
                                    <label class="radio blue">
                                        <input v-model="datosResultado.EstadoExamen" type="radio" name="group1" value="T"
                                            @change="ConsultaResultados().ActualizarDatosBusqueda()" />
                                        <span>Terminados</span>
                                    </label>
                                    <label class="radio blue">
                                        <input v-if="Permisos.BotonImpresas" v-model="datosResultado.EstadoExamen"
                                            type="radio" name="group1" value="I"
                                            @change="ConsultaResultados().ActualizarDatosBusqueda()" />
                                        <span>Impresas</span>
                                    </label>
                                    <p :style="{ color: $options.COLOR }">
                                    </p>
                                </div>
                            </vs-col>

                        </vs-row>


                        <vs-row class="pt-4">

                            <h3 class="pl-4">Envío e Impresión</h3>
                            <vs-col vs-type="flex" vs-w="12">
                                <vs-col vs-type="flex" class="ml-6">
                                    <vs-button class="block" @click.native="GenerarReultado(reporte.opciones)">
                                        <i class="fas fa-file-pdf height:25px"></i>
                                        Generar
                                    </vs-button>

                                    <vs-button class="ml-2 block" v-on:click="enviar_resultado()">
                                        <i class="fas fa-file-export"></i>
                                        Enviar
                                    </vs-button>
                                </vs-col>
                            </vs-col>
                        </vs-row>
                        <br>
                        <br>

                    </div>


                </div>

                <div class="footer">

                    <vs-row class="p-2">

                        <vs-col class="w-full p-2">

                            <vs-table2 max-items="10" search tooltip pagination :data="lista_resultados">
                                <template slot="thead">
                                    <th width="50px" filtro="CodigoExamen">Código Examen</th>
                                    <th width="330px" filtro="Examen">Examen</th>
                                    <th width="330px" filtro="Prueba">Prueba</th>
                                    <th width="30px" filtro="LineaPrueba">Linea Prueba</th>
                                    <th width="300px" filtro="Resultado">Resultado</th>
                                    <th width="110px" filtro="Medida">Medida</th>
                                    <th width="300px" filtro="FechaValidacion">Fecha Validación</th>
                                </template>

                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data" :ref="`list_${indextr}`">

                                        <vs-td2>
                                            {{ tr.CodigoExamen }}
                                        </vs-td2>

                                        <vs-td2>
                                            {{ tr.Examen }}
                                        </vs-td2>

                                        <vs-td2>
                                            {{ tr.Prueba }}
                                        </vs-td2>

                                        <vs-td2>
                                            {{ tr.LineaPrueba }}
                                        </vs-td2>

                                        <vs-td2>
                                            {{ tr.Resultado }}
                                        </vs-td2>

                                        <vs-td2>
                                            {{ tr.Medida }}
                                        </vs-td2>

                                        <vs-td2>
                                            {{ tr.FechaValidacion }}
                                        </vs-td2>

                                    </tr>
                                </template>
                            </vs-table2>

                        </vs-col>
                    </vs-row>

                </div>
            </div>


        </vx-card>
    </div>
</template>
  
<script>


import { VueEditor } from "vue2-editor";
import Datepicker from 'vuejs-datepicker';
import * as lang from 'vuejs-datepicker/src/locale';
//import DatosPaciente from "../../../components/sermesa/global/DatosPaciente.vue";

const TIPO = 'LA1'
const ESTADO_EXAMEN = 'A'
const ASUNTO = 'PRUEBAS DE LABORATORIO GRUPO SERMESA'
const MENSAJE_TITULO = "<h2>Resultados laboratorios Hospitales la Paz</h2>"
const MENSAJE_DESCRIPCION = "<p>Buen d&iacute;a estimad@, adjunto encontrar&aacute; sus resultados de laboratorios.</p> <p>Departamento Laboratorio y Radiologia.</p>"
const DISCLAIMER = '<p><b>DISCLAIMER:</b>  La informaci&oacute;n contenida en este correo es de uso confidencial y exclusivo de las personas aquien se realizaron los ex&aacute;menes. Dicha informaci&oacute;n no se proporciona para ser utilizada de otra manera, circulada, referida, o divulgada a terceros. CENTRO HOSPITALARIO LA PAZ y las personas relacionadas con la preparaci&oacute;n, an&aacute;lisis, redacci&oacute;n o publicaci&oacute;n de estos resultados, no son responsables por el uso que se le d&eacute; a la informaci&oacute;n que por este medio se proporciona. Si usted no es el destinatario de este mensaje, por favor llame por tel&eacute;fono o escriba un correo electr&oacute;nico al remitente y borre este mensaje y cualquier adjunto de su sistema; no est&aacute; permitido divulgar de forma parcial o total el contenido de este correo.  Esta informaci&oacute;n no reemplaza una consulta m&eacute;dica o profesional de la salud, los resultados son preliminares o copia de los originales, y no debe ser interpretado como un tratamiento m&eacute;dico o una segunda opini&oacute;n de un s&iacute;ntoma o enfermedad, ya que requiere la interpretaci&oacute;n de un profesional de la salud. Si usted requiere de un resultado definitivo debe solicitarlo al &aacute;rea de laboratorio para que le proporcionen el informe, el cual debe estar sellado y firmado por Qu&iacute;mico Bi&oacute;logo responsable para que tenga validez.</p>'
const RESULTADOS_OCULTOS = 'O'
const TODOS_LOS_RESULTADOS = 'T'
const ORDEN_ANULADA = 'Orden Anulada'

export default {

    data() {
        return {

            DISCLAIMER,
            RESULTADOS_OCULTOS,
            TODOS_LOS_RESULTADOS,
            show_newEdit: false,
            type_newEdit: 0,
            lista_resultados: [],
            lista_examenes: [],
            tabla: [],
            tablaEstado: [],
            TipoConsulta: {
                Seccion: 'S',
                Examen: 'C',
            },
            TipoOrden: TIPO,
            NumeroOrden: '',
            listado_reportes: [],
            copy_listado_reportes: [],
            datosResultado: {
                Empresa: '',
                Tipo: TIPO,//'LA1',
                Orden: '',
                LineaResultado: '',
                EstadoExamen: ESTADO_EXAMEN,//"A",
                Admision: '',
                Medico: '',
                NombrePaciente: '',
                CorreoElectronico: '',
                CodigoExamen: '',
                CodExamen: '',
                TipoConsulta: '',
                NombreExamen: '',
                Registro: '',
                Seccion: '',
                EnviarA: '',
                CopiarA: '',
                CopiarEnOculto: '',
                Asunto: ASUNTO,//"PRUEBAS DE LABORATORIO GRUPO SERMESA",
                Mensajetitulo: MENSAJE_TITULO,//"<h2>Resultados laboratorios Hospitales la Paz</h2>",
                MensajeDescripcion: MENSAJE_DESCRIPCION,//"<p>Buen d&iacute;a estimad@, adjunto encontrar&aacute; sus resultados de laboratorios.</p> <p>Departamento Laboratorio y Radiologia.</p>",
                MensajeFirma: DISCLAIMER,
                ModuloEnvio: '',
                NombreDelArchivo: '',
                MostrarResultados: '',
                FechaNacimiento: '',
                Edad: '',
                EstadoOrden: ORDEN_ANULADA,
                DescripcionOrden: ''
            },
            reporte: {
                generarPDF: true,
                generarExcel: true,
                generar: false,
                popup: false,
                buscar: '',
                titulo: '',
                url: '',
                url_pdf: '',
                opciones: [],
                pdf: '',
                buscador: null
            },
            edit: {
                Tipo: null,
                Nombre: null,
                Detalle: null,
                productos_seleccion: {},

            },
            customToolbar: [
                [{
                    'header': [1, 2, 3, 4, 5, 6, false]
                }],
                [{
                    'font': []
                }],
                ['bold', 'italic', 'underline', 'strike'],
                [{
                    list: "ordered"
                }, {
                    list: "bullet"
                }],
                [{
                    'indent': '-1'
                }, {
                    'indent': '+1'
                }],
                [{
                    'color': []
                }, {
                    'background': []
                }],
            ],
            Permisos: {
                BotonTerminados: false,
                BotonImpresas: false
            },
            editaredad: false,
            languages: lang,
            nuevaFechaNacimiento: null,
            EsteExamen: '',
            EstaSeccion: ''
        }
    },
    components: {
        VueEditor,
        Datepicker
    },

    mounted: function () {
        this.incializaValores()
    },
    watch: {
        'NumeroOrden': function () {

            this.incializaValores()
            this.ConsultaResultados().ConsultaInicialOrden()
        }
    }
    ,
    methods: {

        BuscarOrden() {
            this.$refs.BuscarOrden.iniciar((data) => {
                if (data != null) {
                    this.TipoOrden = data.TipoOrden
                    this.NumeroOrden = data.Orden
                }
            })
        },
        ConsultaResultados() {

            return {
                ActualizarDatosBusqueda: async () => {

                    this.ConsultaResultados().ListarResultados()
                },
                ConsultaInicialOrden: async () => {


                    if (this.NumeroOrden === (this.datosResultado?.Orden || ''))
                        return

                    if (this.isEmpty(this.TipoOrden) || this.isEmpty(this.NumeroOrden))
                        return


                    this.ConsultaResultados().ListarResultados()
                },
                ListarResultados: async () => {
            
                    this.iniciarValoresEnvio()

                    if (this.datosResultado.CodExamen === null)
                        this.datosResultado.CodExamen = 0

                    if (!this.isEmpty(this.datosResultado.CodExamen)) {
                        this.datosResultado.TipoConsulta = 'C'
                    }
                    if (!this.isEmpty(this.datosResultado.Seccion)) {
                        this.datosResultado.TipoConsulta = 'S'
                    }
                    if (this.isEmpty(this.datosResultado.Seccion) && this.isEmpty(this.datosResultado.CodExamen)) {
                        this.datosResultado.TipoConsulta = ''
                    }

                    if (!this.isEmpty(this.datosResultado.CodExamen)) {
                        this.datosResultado.LineaResultado = this.datosResultado.CodExamen
                    }
                    if (!this.isEmpty(this.datosResultado.Seccion)) {
                        this.datosResultado.LineaResultado = this.datosResultado.Seccion
                    }


                    const resp = await this.axios.post('/app/laboratorio/ListaResultadosLab',
                        {
                            "Empresa": this.datosResultado.Empresa,
                            "TipoOrden": this.TipoOrden,
                            "Orden": this.NumeroOrden,
                            "LineaResultado": this.datosResultado.LineaResultado,
                            "EstadoExamen": this.datosResultado.EstadoExamen,
                            "TipoConsulta": this.datosResultado.TipoConsulta
                        })

                    this.lista_resultados = resp.data.json.map(m => {
                        this.datosResultado.Admision = resp.data.json[0].Admision,
                            this.datosResultado.NombrePaciente = resp.data.json[0].Paciente,
                            this.datosResultado.Registro = resp.data.json[0].Registro,
                            this.datosResultado.CodigoExamen = null
                        this.datosResultado.Medico = resp.data.json[0].Medico,
                            this.datosResultado.EnviarA = resp.data.json[0].CorreoElectronico.trim(),
                            this.datosResultado.CopiarA = resp.data.json[0].MedCorreoElectronico.trim()
                        this.datosResultado.Edad = resp.data.json[0].Edad + " " + resp.data.json[0].EdadMedida
                        this.datosResultado.FechaNacimiento = resp.data.json[0].Nacimiento
                        this.datosResultado.Tipo = resp.data.json[0].TipoOrden
                        this.datosResultado.Orden = resp.data.json[0].Orden
                        this.datosResultado.EstadoOrden = resp.data.json[0].EstadoOrden
                        this.datosResultado.DescripcionOrden = (resp.data.json[0].EstadoOrden == 'A' ? ORDEN_ANULADA : '')
                        return {
                            ...m,
                        }
                    })
                },

                ReiniciarBusquedaExamen: async () => {

                    if (this.isEmpty(this.datosResultado.Orden) || this.isEmpty(this.datosResultado.Tipo))
                        return

                    this.datosResultado.LineaResultado = 0
                    this.datosResultado.Seccion = 0
                    this.ConsultaResultados().ActualizarDatosBusqueda()

                },
                ReiniciarBusquedaSeccion: async () => {

                    if (this.isEmpty(this.datosResultado.Orden) || this.isEmpty(this.datosResultado.Tipo))
                        return

                    this.datosResultado.LineaResultado = 0
                    this.datosResultado.CodExamen = 0
                    this.ConsultaResultados().ActualizarDatosBusqueda()

                },
                OrdenDiagnostico() {
                    this.axios.post('/app/v1_JefeCaja/ConsultaOrdenDiagnostico', {
                        "TipoOrden": this.tipoOrden,
                        "NumeroOrden": this.numeroOrden
                    })
                        .then(resp => {
                            if (!this.isEmptyObject(resp.data.json)) {
                                const { SerieAdmision, Admision, Nombre, Apellido, ApellidoCasada, Titulo, NombreMed, ApellidoMed, Observaciones } = resp.data.json[0]
                                this.codPaciente = SerieAdmision + Admision
                                this.nombrePaciente = ApellidoCasada == '' ? Nombre + ' ' + Apellido : Nombre + ' ' + Apellido + ' ' + ApellidoCasada
                                this.nombreMedico = Titulo + ' ' + NombreMed + ' ' + ApellidoMed
                                this.observaciones = Observaciones

                            } else {
                                this.codPaciente = ''
                                this.nombrePaciente = ''
                                this.nombreMedico = ''
                                this.observaciones = ''
                            }
                        })
                },

            }
        },
        EnviarReultado() {

            var newArray = this.lista_resultados.filter(function (element) {
                if (element.Resultado != '') return element;
            });

            if (newArray.length === 0)
                return

            this.datosResultado.MostrarResultados = this.RESULTADOS_OCULTOS

            if (this.isEmpty(this.datosResultado.Orden) || this.isEmpty(this.datosResultado.Tipo))
                return

            this.show_newEdit = false

            if (!this.isEmpty(this.datosResultado.CodExamen)) {
                this.datosResultado.LineaResultado = this.datosResultado.CodExamen
            }
            if (!this.isEmpty(this.datosResultado.Seccion)) {
                this.datosResultado.LineaResultado = this.datosResultado.Seccion
            }

            this.datosResultado.NombreDelArchivo = this.datosResultado.Tipo + '_' + this.datosResultado.Orden + '.' + 'pdf'
            
         
            this.$genera_reporte_envio({
                Nombre: "ResultadosLaboratorio",
                Data_source: this.datosResultado,
                Data_report: this.listado_reportes
            }).catch(() => {                
            })


        },
        GenerarReultado() {


            var newArray = this.lista_resultados.filter(function (element) {
                if (element.Resultado != '') return element;
            });

            if (newArray.length === 0)
                return

            this.datosResultado.MostrarResultados =  this.TODOS_LOS_RESULTADOS

            if (this.isEmpty(this.datosResultado.Orden) || this.isEmpty(this.datosResultado.Tipo))
                return

            if (!this.isEmpty(this.datosResultado.CodExamen)) {
                this.datosResultado.LineaResultado = this.datosResultado.CodExamen
            }
            if (!this.isEmpty(this.datosResultado.Seccion)) {
                this.datosResultado.LineaResultado = this.datosResultado.Seccion
            }

            this.datosResultado.NombreDelArchivo = this.datosResultado.Tipo + '_' + this.datosResultado.Orden + '.' + 'pdf'


            this.$genera_reporte({
                Nombre: "ResultadosLaboratorio",
                Data_source: this.datosResultado,
                Data_report: this.listado_reportes
            }).catch(() => {
                
            })


        },
        Touch() {
            return {
                start: (item) => {
                    this.temp = item
                },
                end: () => {
                    this.temp = 2
                }
            }
        },
        enviar_resultado() {
            this.type_newEdit = 1;
            this.show_newEdit = true
            this.edit.Tipo = ""
            this.edit.Nombre = ""
            this.edit.Detalle = ""
            this.edit.TituloInforme = ""
        },
        isEmpty(value) {
            if (typeof value === "undefined" || value === null || value === "" || value === 0)
                return true;
        },
        iniciarValoresEnvio() {

            this.datosResultado.EnviarA = ''
            this.datosResultado.CopiarA = ''
            this.datosResultado.NombreDelArchivo = ''

        },
        guardar_edad() {
            if (this.nuevaFechaNacimiento) {
                let d = new Date(this.nuevaFechaNacimiento)

                this.axios.post('/app/radiologia/RadActualizaEdad', {
                    Tipo: this.datosResultado.Tipo,
                    Orden: this.datosResultado.Orden,
                    FechaNacimiento: d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate() + ' 00:00:00'
                })
                    .then(() => {
                        this.ConsultaResultados().ListarResultados()
                        this.editaredad = false
                        this.nuevaFechaNacimiento = null
                    })
            }
        },
        editar_edad() {
            try {
                let n = this.datosResultado.FechaNacimiento.split(' ')[0].split('/')
                this.nuevaFechaNacimiento = new Date(n[2], n[1] - 1, n[0])
                this.editaredad = true
            } catch {
                this.editaredad = false
                this.nuevaFechaNacimiento = null
            }
        },
        incializaValores() {
            this.lista_resultados = []
            this.datosResultado = []
            this.datosResultado.EstadoOrden = ''
            this.datosResultado.DescripcionOrden = ''
            this.datosResultado.Tipo = null
            this.datosResultado.Orden = null
            this.datosResultado.EstadoExamen = ESTADO_EXAMEN
            this.datosResultado.Asunto = ASUNTO
            this.datosResultado.Mensajetitulo = MENSAJE_TITULO
            this.datosResultado.MensajeDescripcion = MENSAJE_DESCRIPCION
            this.datosResultado.MensajeFirma = DISCLAIMER
            this.EsteExamen = ''
            this.EstaSeccion = ''            
            
        }
    },
    async beforeCreate() {
        this.listado_reportes = await this.$recupera_parametros_reporte('ResultadosLaboratorio')
    },
    async created() {

    }
}



</script>
  
<style scoped>
* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

.container_div {
    display: grid;
    grid-gap: 10px;
    width: 100%;
    height: 100%;
    grid-template-areas: "lside rside"
        "footer footer";
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr;
}

.container_div>div {
    border: 1px solid #888;
}

.lside {
    grid-area: lside;
    display: flex;
}

.rside {
    grid-area: rside;
    display: flex;
    padding: 10px;

}

.footer {
    grid-area: footer;
    display: flex;
}

#inner-grid {
    display: grid;
    grid-template-columns: 1fr;
    width: 100%;
}

#inner-grid>div {
    background: rgb(248, 251, 252);
    padding: 20px;

    border-radius: .25rem;
    margin-top: 1px;
    margin-bottom: 1px;
}

.example {
    margin: 20px;
}

.example input {
    display: none;
}

.example label {
    margin-right: 20px;
    display: inline-block;
    cursor: pointer;
}

.ex1 span {
    display: block;
    padding: 15px 20px 15px 45px;
    border: 2px solid #ddd;
    border-radius: 5px;
    position: relative;
    transition: all 0.25s linear;
}

.ex1 span:before {
    content: '';
    position: absolute;
    left: 5px;
    top: 50%;
    -webkit-transform: translatey(-50%);
    transform: translatey(-50%);
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background-color: #ddd;
    transition: all 0.25s linear;
}

.ex1 input:checked+span {
    background-color: #fff;
    box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.1);
}

.ex1 .blue input:checked+span {
    color: rgb(96, 162, 203);
    border-color: rgb(143, 143, 160);
}

.ex1 .blue input:checked+span:before {
    background-color: rgb(98, 155, 209);
}


.block {
    width: 25%;
    height: 40px;
    border: none;
    background-color: #04AA6D;
    color: white;
    padding: 14px 28px;
    font-size: 12px;
    cursor: pointer;
    margin-top: 20px;
}

.block:hover {
    background-color: #ddd;
    color: black;
}

.quantity {
    max-width: 100px;
    width: 100%;
}

.quantity div {
    margin-right: 0;
    margin-left: 0;
    display: flex;
    align-items: center;
}

</style>
  


