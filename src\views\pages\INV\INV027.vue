<template>
<vx-card title="Bodegas">
    <div class="content content-pagex">
        <vs-divider></vs-divider>
        <div class="terms">
            <vs-button color="primary" style="float:left;margin: 5px" type="filled" icon-pack="fas" icon="fa-plus" @click="Abrir_Ventana_Emergente_Nuevo()"> Nueva</vs-button>

            <div class=" md:w-1/5 lg:w-1/5 xl:w-1/5, label-size" style="float:left;margin: 15px" align="right">
                <vs-radio v-model="activaF" vs-name="idActivo" vs-value="S"> Activa</vs-radio>
            </div>
            <div class=" md:w-1/5 lg:w-1/5 xl:w-1/5, label-size" style="float:left;margin: 15px" align="center">
                <vs-radio v-model="activaF" vs-name="idActivo" vs-value="N"> Inactiva</vs-radio>
            </div>
            <div class=" md:w-1/5 lg:w-1/5 xl:w-1/5, label-size" style="float:left;margin: 15px" align="left">
                <vs-radio v-model="activaF" vs-name="idActivo" vs-value=""> Todas</vs-radio>

            </div>
            <vs-button color="primary" style="float:left;margin: 5px" type="filled" icon-pack="fas" icon="fa-search" @click="Consultar_Bodega()"> Buscar Bodegas</vs-button>
        </div>
    </div>
    <div>

        <div>

            <vs-divider></vs-divider>
            <vs-table2 max-items="10" pagination :data="Lista_bodegas" noDataText="Sin datos disponibles" search id="tb_departamentos">

                <template slot="thead">
                    <th width="20px">Código</th>
                    <th width="250px">Nombre</th>
                    <th width="200px">Agrupación</th>
                    <th width="200px">Tipo Bodega</th>
                    <th width="30px">Cuenta en Tránsito</th>
                    <th width="30px">Cuenta Local</th>
                    <th width="30px">Cuenta Despacho Paciente</th>
                    <th width="30px">Editar</th>
                    <th width="30px">Asignar Categoría</th>
                    <th width="80px" style="text-align: center;">Estado</th>
                    <th width="30px">Cambio de Estado</th>
                </template>

                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">
                        <vs-td2 :data="data[indextr].Codigo">
                            {{data[indextr].Codigo}}
                        </vs-td2>

                        <vs-td2 :data="data[indextr].Nombre">
                            {{data[indextr].Nombre}}
                        </vs-td2>

                        <vs-td2 :data="data[indextr].CodigoAgrupacion">
                            {{data[indextr].CodigoAgrupacion}} - {{data[indextr].Agrupacion}}
                        </vs-td2>
                        <vs-td2 :data="data[indextr].TipoBodega">
                            {{data[indextr].TipoBodega}} - {{data[indextr].NombreTipoBodega}}
                        </vs-td2>

                        <vs-td2 :data="data[indextr].CuentaEnTransito">
                            {{data[indextr].CuentaEnTransito}}
                        </vs-td2>

                        <vs-td2 :data="data[indextr].CuentaLocal">
                            {{data[indextr].CuentaLocal}}
                        </vs-td2>
                        <vs-td2 :data="data[indextr].CuentaDespachoPaciente">
                            {{data[indextr].CuentaDespachoPaciente}}
                        </vs-td2>
                        <vs-td2 align="center">
                            <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" v-if="data[indextr].Activa == 'INACTIVA'" disabled="true" style="display:inline-block;margin-right:2px" @click="Abrir_Ventana_Emergente_Editar(data[indextr])"></vs-button>
                            <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" v-else style="display:inline-block;margin-right:2px" @click="Abrir_Ventana_Emergente_Editar(data[indextr])"></vs-button>
                        </vs-td2>
                        <vs-td2 align="center">
                            <vs-button color="green" icon-pack="far" icon="fa-window-restore" v-if="data[indextr].Activa == 'INACTIVA'" disabled="true" style="display:inline-block;margin-right:2px" @click="Asignar_Categoria(data[indextr])"></vs-button>
                            <vs-button color="green" icon-pack="far" icon="fa-window-restore" v-else style="display:inline-block;margin-right:2px" @click="Asignar_Categoria(data[indextr])"></vs-button>
                        </vs-td2>
                        <vs-td2 style="text-align: center;" variant="success" width="50px" :data="data[indextr].Activa">
                            {{data[indextr].Activa==='ACTIVA'? 'ACTIVA':'INACTIVA'}}
                        </vs-td2>
                        <vs-td2 align="center" :data="data[indextr].Estado">
                            <vs-switch v-model="data[indextr].Estado" :v-value="data[indextr].Estado" @click="Eliminar_Registro(data[indextr])"> </vs-switch>
                        </vs-td2>
                    </tr>
                </template>
            </vs-table2>
        </div>
    </div>

    <!--------------- Nuevo / Editar / Información   (Ventana Emergente)-------------->
    <vs-popup classContent="popup-example" :title="Titulo_emergente" :active.sync="Estado_VentanaEmergente">
        <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">

            <form>
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="md:w-1/5 lg:w-1/5 xl:w-1/5">
                        <ValidationProvider name="Código" rules="required|numero_entero|numero_min:0" v-slot="{ errors }" class="required">
                            <label class="label-size"> Código</label>
                            <vs-input class="w-full" type="number" v-model="Codigo_bodega" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="Deshabilitar_campo_codigo" />
                        </ValidationProvider>
                    </div>
                    <div class=" md:w-2/5 2g:w-2/5 xl:w-2/5"></div>
                    <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                        <ValidationProvider name="Nombre_bodega" rules="required|max:40" v-slot="{ errors }" class="required">
                            <label class="label-size"> Nombre</label>
                            <vs-input class="w-full" count="40" v-model="Nombre_bodega" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="Deshabilitar_controles" />
                        </ValidationProvider>
                    </div>
                </div>
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                        <label class="label-size"> Email</label>
                        <vs-input v-model="Direccion_correo" class="w-full" :disabled="Deshabilitar_controles" />
                    </div>
                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5"></div>
                </div>
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                        <ValidationProvider name="Cuenta_local" rules="max:12">
                            <label class="label-size"> Cuenta local</label>
                            <vs-input :disabled="true" class="w-full" count="12" v-model="Cuenta_local" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5"></div>
                    <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                        <ValidationProvider name="Cuenta_transito" rules="max:12">
                            <label class="label-size"> Cuenta en Tránsito</label>
                            <vs-input :disabled="true" count="12" class="w-full" v-model="Cuenta_transito" />
                        </ValidationProvider>
                    </div>
                </div>
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                        <ValidationProvider name="tipo_bodega_contable" rules="required|numero_entero|numero_min:0">
                            <label class="label-size"> Tipo Bodega Contable</label>
                            <vs-input :disabled="true" class="w-full" type="number" v-model="tipo_bodega_contable" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5"></div>
                    <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                        <ValidationProvider name="cuenta_despacho_paciente" rules="max:12" v-slot="{ errors }">
                            <label class="label-size"> Cuenta Despacho</label>
                            <vs-input :disabled="true" class="w-full" count="12" v-model="cuenta_despacho_paciente" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                </div>

                <vs-divider class="label-size">LISTAS</vs-divider>
                <!--- Inicio listas desplegables -->
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                        <ValidationProvider name="cb_agrupacion" class="required">
                            <label class="label-size"> Tipo Agrupación</label>
                            <multiselect v-model="cb_agrupacion" :options="Lista_agrupacion" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Agrupacion_seleccionada" placeholder="Búsqueda tipo" @input="onChangeAgrupacion" :disabled="Deshabilitar_controles">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5"></div>
                    <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                        <ValidationProvider name="cb_busqueda_empresa">
                            <label class="label-size">Empresa Real x</label>
                            <multiselect v-model="cb_busqueda_empresa" :options="Lista_empresa" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Empresa_seleccionada" placeholder="Búsqueda empresa" @input="onChangeEmpresa" :disabled="DeshabilitarEmpx">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>
                </div>
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                        <ValidationProvider name="cb_hospital">
                            <label class="label-size"> Sucursal</label>
                            <multiselect v-model="cb_hospital" :options="Lista_hospital" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Hospital_seleccionado" placeholder="Búsqueda hospital" @input="onChangeHospital" :disabled="Deshabilitar_controles">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5"></div>
                    <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                        <ValidationProvider name="cb_bodegaPadre">
                            <label class="label-size"> Bodega Movimiento / Devolución</label>
                            <multiselect v-model="cb_bodegaPadre" :options="Lista_bodegaPadre" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="BodegaP_seleccionado" placeholder="Búsqueda Bodega Movimiento" @input="onChangeBodegaP" :disabled="DeshabilitarBodMov">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>
                </div>
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                        <ValidationProvider name="cb_bodegaDespacho">
                            <label class="label-size"> Bodega Despacho</label>
                            <multiselect v-model="cb_bodegaDespacho" :options="Lista_bodegaDespacho" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="BodegaD_seleccionado" placeholder="Búsqueda Bodega Despacho" @input="onChangeBodegaD" :disabled="DeshabilitarBodD">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5"></div>
                    <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                        <ValidationProvider name="cb_agrupacion" class="required">
                            <label class="label-size"> Tipo Bodega</label>
                            <multiselect v-model="cb_tipoBodega" :options="Lista_tipoBodega" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="TipoBodega_seleccionada" placeholder="Búsqueda Tipo Bodega" @input="onChangeTipoBodega" :disabled="Deshabilitar_controles">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>

                </div>

                <!----- Fin listas desplegables -->

                <vs-divider class="label-size">OTRAS OPCIONES</vs-divider>
                <!----- INICIO SWITCH --->
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="w-full sm:w-1/2" style="margin-bottom:15px">
                        <label class="label-size">Control Máximos y Mínimos</label>
                        <vs-switch v-model="control_maximo_minino" :disabled="Deshabilitar_controles"> </vs-switch>
                    </div>
                    <div class="w-full sm:w-1/2" style="margin-bottom:15px">
                        <label class="label-size">Autorización Traslado</label>
                        <vs-switch v-model="autorizado_despacho" :disabled="Deshabilitar_controles"> </vs-switch>
                    </div>
                </div>
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="w-full sm:w-1/2" style="margin-bottom:15px">
                        <label class="label-size">Crear Existencia Inicial</label>
                        <vs-switch v-model="crear_existencia_inicial" :disabled="Deshabilitar_controles"> </vs-switch>
                    </div>
                    <div class="w-full sm:w-1/2" style="margin-bottom:15px">
                        <label class="label-size">Despacho Habitaciones</label>
                        <vs-switch v-model="usoVale" :disabled="Deshabilitar_controles"> </vs-switch>
                    </div>
                </div>
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="w-full sm:w-1/2" style="margin-bottom:15px">
                        <label class="label-size">Estado - {{ idActiva === true? 'ACTIVA':'INACTIVA' }}</label>
                        <vs-switch v-model="idActiva" :disabled="Deshabilitar_controles"> </vs-switch>
                    </div>
                </div>

                <!---- FIN SWITCH --->
                <vs-divider></vs-divider>
                <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" id="btn_confirmacion" @click="Confirmacion_Transaccion()"> Grabar</vs-button>
                <vs-divider></vs-divider>
            </form>
        </div>
    </vs-popup>
    <!---------------Fin Ventana Emergente _---------->

    <!--------------- Asignación de Categorias   (Ventana Emergente)-------------->
    <vs-popup classContent="popup-example" :title="Titulo_emergente" :active.sync="Estado_VentanaCategoria">
        <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">

            <form>
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="md:w-1/5 lg:w-1/5 xl:w-1/5">
                        <ValidationProvider name="Código" rules="required|numero_entero|numero_min:0" v-slot="{ errors }" class="required">
                            <label class="label-size"> Código</label>
                            <vs-input class="w-full" type="number" v-model="Codigo_bodega" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="Deshabilitar_controles" />
                        </ValidationProvider>
                    </div>
                    <div class=" md:w-2/5 2g:w-2/5 xl:w-2/5"></div>
                    <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                        <ValidationProvider name="Nombre_bodega" rules="required|max:40" v-slot="{ errors }" class="required">
                            <label class="label-size"> Nombre</label>
                            <vs-input class="w-full" count="40" v-model="Nombre_bodega" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="Deshabilitar_controles" />
                        </ValidationProvider>
                    </div>
                </div>
                <br>
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                        <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
                            <ValidationProvider name="Operacion">
                                <label class="label-size">Operación</label>
                                <multiselect v-model="cb_lista_operacion" :options="Lista_operacion" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Operacion_seleccionada" :disabled="Deshabilitar_controles" placeholder="Seleccionar departamento" @input="onChangeTipo">
                                    <span slot="noOptions">Lista no disponible.</span>
                                </multiselect>
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class=" md:w-1/5 2g:w-1/5 xl:w-1/5"></div>
                    <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                        <ValidationProvider name="Agencia">
                            <label class="label-size">Categorías disponibles</label>
                            <multiselect v-model="id_categoria_selec" :options="Lista_categorias_disponibles" :multiple="true" :close-on-select="false" :clear-on-select="false" :preserve-search="true" placeholder="Seleccionar categoría" :custom-label="Categoria_seleccionada" label="NOMBRE" track-by="NOMBRE" :preselect-first="true" @input="onChangeCategoria" :disabled="Deshabilitado_categoria">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                        <div class="flex flex-wrap">
                            <br>
                            <div class="w-full">
                                <br>
                                <input labelt="TODOS" type="checkbox" id="checkbox" v-model="Enviar_Todos" :disabled="Estado_Todos" name="lst_check" @change="onChangeCheck()" />
                                <label class="label-size"> Seleccionar todos</label>
                            </div>
                        </div>
                        <br>
                        <div class="flex flex-wrap">
                            <div align="left">
                                <vs-button color="primary"  type="filled" icon-pack="fas" icon="fa-plus" @click.native="Guardar_Asociacion()"> Asignar Categorías</vs-button>
                            </div>
                        </div>

                    </div>

                </div>
                <div>

                    <vs-divider position="left" class="label-size">CATEGORIAS ASIGNADAS</vs-divider>
                    <vs-table2 max-items="10" pagination :data="Lista_Asignaciones_categoria" search id="tb_asignacion_categoria">
                        <template slot="thead">
                            <th width="10">Código Categoria</th>
                            <th>Categoria</th>
                            <th>Operación</th>
                            <th></th>
                        </template>

                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 :data="data[indextr].CODIGO_CATEGORIA">
                                    {{data[indextr].CODIGO_CATEGORIA}}
                                </vs-td2>
                                <vs-td2 :data="data[indextr].CATEGORIA">
                                    {{data[indextr].CATEGORIA}}
                                </vs-td2>

                                <vs-td2 :data="data[indextr].DESCRIPCION_TIPO">
                                    {{data[indextr].DESCRIPCION_TIPO}}
                                </vs-td2>

                                <vs-td2 width="50px">
                                    <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="Eliminar_Categoria(data[indextr])"></vs-button>
                                </vs-td2>
                            </tr>
                        </template>

                    </vs-table2>

                </div>

            </form>
        </div>
    </vs-popup>
</vx-card>
</template>

<script>
/**
 * @General
 * Registro de bodegas
 */
import Multiselect from 'vue-multiselect'
import "vue-multiselect/dist/vue-multiselect.min.css"
export default {

    components: {
        Multiselect
    },
    data() {
        return {

            Lista_bodegas: [],
            Titulo_emergente: '',
            Estado_VentanaEmergente: false,
            Estado_VentanaCategoria: false,

            //Campos para la bodega
            Codigo_bodega: '',
            Nombre_bodega: '',
            Cuenta_transito: '',
            Cuenta_local: '',
            cuenta_despacho_paciente: '',
            tipo_bodega_contable: '',
            Direccion_correo: '',
            control_maximo_minino: false,
            crear_existencia_inicial: false,
            autorizado_despacho: false,
            Lista_empresa: [],
            cb_busqueda_empresa: '',
            id_empresa_seleccionado: '',
            cb_agrupacion: '',
            Lista_agrupacion: [],
            id_agrupacion_selec: '',
            cb_tipoBodega: '',
            Lista_tipoBodega: [],
            id_tipoBodega_selec: '',
            cb_hospital: '',
            Lista_hospital: [],
            id_hospital_selec: '',
            cb_bodegaPadre: '',
            Lista_bodegaPadre: [],
            id_bodegaPadre_selec: '',
            cb_bodegaDespacho: '',
            Lista_bodegaDespacho: [],
            id_bodegaDespacho_selec: '',
            //Campos para Habilitar o deshabilitar todos los campos de la ventana emergente.
            Deshabilitar_campo_codigo: false,
            Deshabilitar_controles: false,
            DeshabilitarBodD: true,
            DeshabilitarBodMov: true,
            DeshabilitarEmpx: true,
            activaF: '',
            Operacion: '', // N = Nuevo Registro / A = Actualización registor
            SubOpcion: '',
            idActiva: '',
            usoVale: false,

            cb_lista_operacion: '',
            Lista_operacion: [{
                'CODIGO': 'R',
                'DESCRIPCION': 'Requerimientos'
            }],
            id_operacion_selec: 'R',
            Lista_Asignaciones_categoria: [],
            cb_categoria_disponibles: '',
            id_categoria_selec: '',
            Lista_categorias_disponibles: [],
            lista_categorias_select: [],
            Deshabilitado_categoria: false,
            Enviar_Todos: false,
            Estado: '',
            EstadoAD: '',
            Estado_Todos: false
        };
    },
    mounted() {

    },
    watch: {
        id_agrupacion_selec(value) {
            if (value == 1) {
                this.DeshabilitarBodD = true
                this.DeshabilitarBodMov = true
                this.DeshabilitarEmpx = true
                this.cb_busqueda_empresa = {
                     Codigo: 'SEM',
                     Nombre: 'SERVICIOS MEDICOS Y HOSPITALARIOS CENTROAMERICANOS S.A.(De)'
                 }
            } else {
                this.DeshabilitarBodD = false
                this.DeshabilitarBodMov = false
                this.DeshabilitarEmpx = false
                this.cb_busqueda_empresa = ''
                this.Consultar_Empresa();
            }
        }
    },
    methods: {

        Empresa_seleccionada({
            Codigo,
            Nombre
        }) {
            return `${Codigo} - ${Nombre} `
        },
        Agrupacion_seleccionada({
            CODIGO,
            DESCRIPCION
        }) {
            return `${CODIGO} - ${DESCRIPCION} `
        },
        TipoBodega_seleccionada({
            CODIGO,
            NOMBRE
        }) {
            return `${CODIGO} - ${NOMBRE} `
        },
        Hospital_seleccionado({
            CODIGO,
            NOMBRE
        }) {
            return `${CODIGO} - ${NOMBRE} `
        },
        BodegaP_seleccionado({
            Codigo,
            Nombre
        }) {
            return `${Codigo} - ${Nombre} `
        },
        BodegaD_seleccionado({
            Codigo,
            Nombre
        }) {
            return `${Codigo} - ${Nombre} `
        },
        Operacion_seleccionada({
            DESCRIPCION
        }) {
            return `${DESCRIPCION} `
        },
        Categoria_seleccionada({
            CODIGO,
            NOMBRE
        }) {
            return `${CODIGO} - ${NOMBRE} `
        },
        onChangeTipo(value) {
            //Si existe seleccionada un element
            this.Deshabilitado_categoria = false;
            this.Enviar_Todos = false;
            if (value !== null && value.length !== 0) {
                this.id_operacion_selec = value.CODIGO;
                this.cb_categoria_disponibles = null;
                this.id_categoria_selec = '';
                this.Consultar_CategoriasDisponibles()
                this.Consultar_Asignaciones();
                this.Estado_Todos = false;
            } else {
                this.id_operacion_selec = '';
                this.Lista_Asignaciones_categoria = [];
                this.Lista_categorias_disponibles = [];
                this.Estado_Todos = true;
                this.Check_todos = false;
            }

        },
        onChangeCategoria(value) {

            this.lista_categorias_select = JSON.stringify(value);

        },
        onChangeCheck() {
            if (this.Enviar_Todos) {
                this.Deshabilitado_categoria = true;
                this.id_categoria_selec = '',
                    this.lista_categorias_select = [];
                this.lista_categorias_select = JSON.stringify(this.Lista_categorias_disponibles);

            } else {

                this.Deshabilitado_categoria = false;
            }

        },
        onChangeEmpresa(value) {

            //Si existe seleccionada un elemento
            if (value !== null && value.length !== 0) {
                this.id_empresa_seleccionado = value.Codigo;
            } else {
                this.id_empresa_seleccionado = ''
            }
        },

        onChangeAgrupacion(value) {
            //Si existe seleccionada un elemento
            if (value !== null && value.length !== 0) {
                this.id_agrupacion_selec = value.CODIGO;
            } else {
                this.id_agrupacion_selec = '';
            }

        },
        onChangeTipoBodega(value) {
            //Si existe seleccionada un elemento
            if (value !== null && value.length !== 0) {
                this.id_tipoBodega_selec = value.CODIGO;
            } else {
                this.id_tipoBodega_selec = '';
            }

        },
        onChangeHospital(value) {
            //Si existe seleccionada un elemento
            if (value !== null && value.length !== 0) {
                this.id_hospital_selec = value.CODIGO;
            } else {
                this.id_hospital_selec = '';
            }

        },
        onChangeBodegaP(value) {
            //Si existe seleccionada un elemento
            if (value !== null && value.length !== 0) {
                this.id_bodegaPadre_selec = value.Codigo;
            } else {
                this.id_bodegaPadre_selec = '';
            }

        },
        onChangeBodegaD(value) {
            if (value !== null && value.length !== 0) {
                this.id_bodegaDespacho_selec = value.Codigo;
            } else {
                this.id_bodegaDespacho_selec = '';
            }
        },
        Abrir_Ventana_Emergente_Nuevo() {
            document.getElementById("btn_confirmacion").style.visibility = "visible";
            this.Titulo_emergente = 'Nueva Bodega';
            this.Estado_VentanaEmergente = true;
            //Ejecución de metodos. 
            this.LimpiarCampos();
            this.Consultar_Empresa();
            this.Consultar_Agrupacion();
            this.Consultar_TipoBodega()
            this.Consultar_Hospital();
            this.Consultar_BodegaPadre();
            this.Consultar_BodegaDespacho();
            //Estado de los campos de la ventana emergente
            this.Codigo_bodega = 0;
            this.Nombre_bodega = "";
            this.idActiva = true
            this.Estado = 'ACTIVA'
            this.control_maximo_minino = 1
            this.crear_existencia_inicial = 1
            this.Deshabilitar_campo_codigo = false;
            this.Deshabilitar_controles = false;
            this.Operacion = 'I'; // Indica la variable que es un nuevo registro
            this.SubOpcion = '1'
            this.DeshabilitarBodD = true;
            this.DeshabilitarBodMov = true;

        },
        Cerrar_Ventana_Emergente_Nuevo() {
            this.Estado_VentanaEmergente = false;
        },
        Abrir_Ventana_Emergente_Editar(datos_editar) {
            document.getElementById("btn_confirmacion").style.visibility = "visible";

            this.Titulo_emergente = 'Editar Bodega';

            //Campos abiertos
            this.Codigo_bodega = datos_editar.Codigo;
            this.Nombre_bodega = datos_editar.Nombre;
            this.Cuenta_transito = datos_editar.CuentaEnTransito;
            this.Cuenta_local = datos_editar.CuentaLocal;
            this.cuenta_despacho_paciente = datos_editar.CuentaDespachoPaciente;
            this.tipo_bodega_contable = datos_editar.TipoBodegaContable;
            this.Direccion_correo = datos_editar.DireccionMail;

            //Switch
            this.control_maximo_minino = datos_editar.ControlMaximoMinimo;
            this.crear_existencia_inicial = datos_editar.CreaExistenciaInicial;
            this.Estado = datos_editar.Activa;
            if (datos_editar.Activa == 'ACTIVA')
                this.idActiva = true;
            else
                this.idActiva = false;

            if (datos_editar.AutDespacho == 'S')
                this.autorizado_despacho = true;
            else
                this.autorizado_despacho = false;

            if (datos_editar.UsoVale == 1)
                this.usoVale = true;
            else
                this.usoVale = false;
            //Cargar  listas
            this.Lista_empresa = [];
            this.Lista_agrupacion = [];
            this.Lista_hospital = [];
            this.Lista_tipoBodega = [];
            this.Consultar_Empresa();
            this.id_empresa_seleccionado = datos_editar.EmpresaRealx;
            this.Consultar_Agrupacion();
            this.Consultar_TipoBodega();
            this.Consultar_Hospital();
            this.Consultar_BodegaPadre();
            this.Consultar_BodegaDespacho();
            this.id_agrupacion_selec = datos_editar.CodigoAgrupacion;
            this.id_tipoBodega_selec = datos_editar.TipoBodega;
            this.id_hospital_selec = datos_editar.Hospital;
            this.id_bodegaPadre_selec = datos_editar.BodegaPadre;
            this.id_bodegaDespacho_selec = datos_editar.BodegaDespacho;

            //Setear Multilistas
            if (datos_editar.EmpresaRealx == '') {
                this.cb_busqueda_empresa = ''
            } else {
                this.cb_busqueda_empresa = {
                    Codigo: datos_editar.EmpresaRealx,
                    Nombre: datos_editar.NombreEmp
                }

            }
            if (datos_editar.CodigoAgrupacion == '') {
                this.cb_agrupacion = ''
            } else {
                this.cb_agrupacion = {
                    CODIGO: datos_editar.CodigoAgrupacion,
                    DESCRIPCION: datos_editar.Agrupacion
                }
                if (datos_editar.CodigoAgrupacion != 1) {
                    this.DeshabilitarBodD = false
                    this.DeshabilitarBodMov = false
                }
            }
            if (datos_editar.TipoBodega == '') {
                this.cb_tipoBodega = ''
            } else {
                this.cb_tipoBodega = {
                    CODIGO: datos_editar.TipoBodega,
                    NOMBRE: datos_editar.NombreTipoBodega
                }
            }
            if (datos_editar.Hospital == '') {
                this.cb_hospital = ''
            } else {
                this.cb_hospital = {
                    CODIGO: datos_editar.Hospital,
                    NOMBRE: datos_editar.NombreHosp
                }
            }
            if (datos_editar.BodegaPadre == '') {
                this.cb_bodegaPadre = ''
            } else {
                this.cb_bodegaPadre = {
                    Codigo: datos_editar.BodegaPadre,
                    Nombre: datos_editar.NombreBodegaP
                }
            }
            if (datos_editar.BodegaDespacho == '') {
                this.cb_bodegaDespacho = ''
            } else {
                this.cb_bodegaDespacho = {
                    Codigo: datos_editar.BodegaDespacho,
                    Nombre: datos_editar.NombreBodegaD
                }
            }

            this.Estado_VentanaEmergente = true;

            //Estado de los campos de la ventana emergente
            this.Deshabilitar_campo_codigo = true;
            this.Deshabilitar_controles = false;
            this.Operacion = 'A'; // Indica la variable que es una actualización
            this.SubOpcion = '1'

        },

        Consultar_Bodega() {
            const url = this.$store.state.global.url
            this.$vs.loading();
            this.axios.post(url + 'app/v1_ordencompra/ListaBodegas', {
                    Opcion: 'C',
                    SubOpcion: '1',
                    ActivaF: this.activaF
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Bodegas',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.Lista_bodegas = "";

                    } else {
                        //Decodificación
                        this.Lista_bodegas = resp.data.json;

                    }

                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },
        Consultar_Empresa() {

            const url = this.$store.state.global.url;
            this.$vs.loading();
            this.axios.post(url + 'app/v1_ordencompra/ListaEmpresas', {
                    Opcion: 'C',
                    SubOpcion: '5'
                })

                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Empresas',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.Lista_empresa = "";
                    } else {
                        this.Lista_empresa = resp.data.json;
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },
        Consultar_Agrupacion() {
            const url = this.$store.state.global.url
            this.$vs.loading();
            this.axios.post(url + 'app/v1_ordencompra/consulta_agrupacion', {
                    Opcion: 'C',
                    SubOpcion: '4'
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Agrupación',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.Lista_agrupacion = [];

                    } else {
                        this.Lista_agrupacion = resp.data.json;
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },
        Consultar_TipoBodega() {
            const url = this.$store.state.global.url
            this.$vs.loading();
            this.axios.post(url + 'app/v1_ordencompra/consulta_TipoBodega', {
                    Opcion: 'C',
                    SubOpcion: '6'
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Tipo Bodega',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.Lista_tipoBodega = [];

                    } else {
                        this.Lista_tipoBodega = resp.data.json;
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();

        },
        Consultar_Hospital() {

            const url = this.$store.state.global.url;
            this.$vs.loading();
            this.axios.post(url + 'app/v1_ordencompra/consulta_hospital', {
                    Opcion: 'C',
                    SubOpcion: '7'
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Hospital',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.Lista_hospital = [];
                    } else {
                        this.Lista_hospital = resp.data.json;
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();

        },
        Consultar_BodegaPadre() {

            const url = this.$store.state.global.url;
            this.$vs.loading();
            this.axios.post(url + 'app/v1_ordencompra/ListaBodegaPadre', {
                    Opcion: 'C',
                    SubOpcion: '2',

                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Bodegas',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.Lista_bodegaPadre = [];
                    } else {
                        this.Lista_bodegaPadre = resp.data.json;

                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();

        },
        Consultar_BodegaDespacho() {

            const url = this.$store.state.global.url;
            this.$vs.loading();
            this.axios.post(url + 'app/v1_ordencompra/ListaBodegaDespacho', {
                    Opcion: 'C',
                    SubOpcion: '3',

                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Bodegas',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.Lista_bodegaDespacho = [];
                    } else {
                        this.Lista_bodegaDespacho = resp.data.json;
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();

        },
        Confirmacion_Transaccion() {
            /**
             * @General
             * Función Permite  actualizar y  almacenar un nuevo registro;
             */
            if (this.Nombre_bodega === '' || this.Nombre_bodega === null) {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Bodegas',
                    text: 'Ingresar Nombre de Bodega',
                })
                return;
            }

            if (this.id_agrupacion_selec === 0 || this.id_agrupacion_selec === '' || this.id_agrupacion_selec === null) {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Bodegas',
                    text: 'Ingresar Tipo de Agrupación de Bodega',
                })
                return;
            }
            if (this.id_agrupacion_selec !=1 && this.id_empresa_seleccionado === 'SEM'){
                this.$vs.notify({
                    color: 'danger',
                    title: 'Bodegas',
                    text: 'La empresa seleccionada es solo para Bodegas de Despacho, Seleccione otra.',
                })
                return;
            }

            if (this.cb_tipoBodega === '' || this.cb_tipoBodega === null) {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Bodegas',
                    text: 'Ingresar Tipo de Bodega',
                })
                return;
            }

            // //Switch
            if (this.control_maximo_minino)
                this.control_maximo_minino = 1;
            else
                this.control_maximo_minino = 0;

            if (this.crear_existencia_inicial)
                this.crear_existencia_inicial = 1;
            else
                this.crear_existencia_inicial = 0;
            if (this.autorizado_despacho)
                this.autorizado_despacho = 'S';
            else
                this.autorizado_despacho = 'N';

            if (this.usoVale)
                this.usoVale = 1;
            else
                this.usoVale = 0;

            // Guarda / Actualizar datos
            const url = this.$store.state.global.url
            this.$vs.loading();
            this.axios.post(url + 'app/v1_ordencompra/bodegaNActualiza', {
                    Id: this.Codigo_bodega,
                    Opcion: this.Operacion,
                    SubOpcion: this.SubOpcion,
                    Nombre: this.Nombre_bodega,
                    Activa: this.idActiva == true ? 'S' : 'N',
                    ControlMaximoMinimo: this.control_maximo_minino,
                    CodigoAgrupacion: this.id_agrupacion_selec,
                    Hospital: this.id_hospital_selec,
                    CreaExistenciaInicial: this.crear_existencia_inicial,
                    //BodegaStock: this.bodega_stock,
                    BodegaPadre: this.id_bodegaPadre_selec,
                    BodegaDespacho: this.id_bodegaDespacho_selec,
                    AutDespacho: this.autorizado_despacho,
                    DireccionMail: this.Direccion_correo,
                    EmpresaRealx: this.id_empresa_seleccionado,
                    UsoVale: this.usoVale,
                    TipoBodega: this.id_tipoBodega_selec
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Bodegas',
                            text: resp.data.mensaje,
                        })

                    } else {
                        this.LimpiarCampos();
                        this.Cerrar_Ventana_Emergente_Nuevo();
                        this.Consultar_Bodega(); //Carga  la tabla inicial
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();

        },
        Eliminar_Registro(value) {
            /**
             * @General
             * Función eliminar registro seleccionado;
             */
            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Confirmación',
                text: '¿Cambiar estado a bodega \'' + value.Codigo + '\'? ',
                acceptText: 'Aceptar',
                cancelText: 'Cancelar',
                accept: () => {
                    const url = this.$store.state.global.url
                    //Condisión de estado es por que toma el valor de swith y no el valor del registro
                    this.$vs.loading();
                    this.axios.post(url + 'app/v1_ordencompra/bodegaBaja', {
                            Id: value.Codigo,
                            Activa: (!value.Estado ? 'N' : 'S'),
                            Opcion: 'E',
                            SubOpcion: '1'
                        })
                        .then(resp => {
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Bodegas',
                                    text: resp.data.mensaje,
                                })

                            } else {
                                this.$vs.notify({
                                    color: '#danger',
                                    title: 'Bodegas',
                                    text: resp.data.mensaje,
                                });

                                this.Lista_bodegas = [];
                                this.Consultar_Bodega(); //Carga  la tabla inicial
                            }
                        })
                        .catch(() => {
                            this.$vs.loading.close();
                        })
                    this.$vs.loading.close();

                },
                cancel: () => {
                    value.Estado = !value.Estado
                }
            })
        },
        Validacion_Campos(Tipo, Nombre, valor, obligatorio) {
            /**
             * @General
             * Tipo : N = Númerio, Nombre = Descripción del campo, Valor = A evaluar, Obligatorio= Verdadero o falso.
             * Devuelve un mensaje al usuario si las validaciones no se cumple y aborta, de lo contrario devuelve verdadero
             */
            if (Tipo == 'ID') {
                if (valor <= 0) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Bodegas',
                        text: 'Campo \'' + Nombre + '\' es obligatorio.',
                    });
                    return false;
                } else {
                    return true;
                }
            } else if (Tipo == 'N') {

                if (valor < 0 || valor === "") {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Bodegas',
                        text: 'Campo \'' + Nombre + '\' es obligatorio.',
                    });
                    return false;
                } else {
                    return true;
                }
            } else {
                if (valor == '' && obligatorio == true) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Bodegas',
                        text: 'Campo \'' + Nombre + '\' es obligatorio.',
                    });
                    return false;
                } else {
                    return true;
                }
            }
        },
        Asignar_Categoria(datos) {

            this.Estado_VentanaCategoria = true
            this.Deshabilitar_controles = true
            this.Codigo_bodega = datos.Codigo,
                this.Nombre_bodega = datos.Nombre
            this.cb_lista_operacion = {
                DESCRIPCION: 'Requerimientos'
            }
            this.Consultar_CategoriasDisponibles()
            this.Consultar_Asignaciones()

        },
        async Consultar_Asignaciones() {
            const url = this.$store.state.global.url;
            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/consulta_bodega_categoria', {
                    codigo_bodega: this.Codigo_bodega,
                    tipo: this.id_operacion_selec
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Asignación Categoría',
                            text: resp.data.mensaje,
                        })
                        this.Lista_Asignaciones_categoria = [];
                    } else {
                        this.Lista_Asignaciones_categoria = resp.data.json;
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },
        async Consultar_CategoriasDisponibles() {
            const url = this.$store.state.global.url;
            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/consulta_categorias_disponibles_bodegas', {
                    bodega: this.Codigo_bodega,
                    tipo: this.id_operacion_selec,
                    operacion: 'A'
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.Lista_categorias_disponibles = [];
                    } else {
                        this.Lista_categorias_disponibles = [];
                        this.Lista_categorias_disponibles = resp.data.json;
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();

        },
        Guardar_Asociacion() {
            /**
             * @General
             * Función Permite  actualizar y  almacenar un nuevo registro;
             */
            var cantidad = Object.keys(this.lista_categorias_select).length;
            if (cantidad == '0' || cantidad == '2') { //Validación que el array tenga datos, que tenga mas de dos caracteres, 
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Asignación Categoría',
                    text: 'Seleccionar categoría',
                })
                return;
            }
            this.$vs.loading();
            this.axios.post('/app/v1_OrdenCompra/bodega_categoria', {
                    codigo: 0,
                    codigo_bodega: this.Codigo_bodega,
                    json_datos: this.lista_categorias_select,
                    estado: 'S',
                    tipo: this.id_operacion_selec,
                    operacion: 'N',
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo !== 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Asignación Categoría',
                            text: resp.data.mensaje,
                        })

                        this.lista_categorias_select = [];
                        this.id_categoria_selec = '';

                        this.cb_categoria_disponibles = '';
                        this.Consultar_Asignaciones();
                        this.Consultar_CategoriasDisponibles();

                    } else {
                        this.Lista_categorias_disponibles = [];
                        this.lista_categorias_select = [];
                        this.id_categoria_selec = '';
                        this.cb_categoria_disponibles = '';
                        this.Deshabilitado_categoria = false;
                        this.Enviar_Todos = false;
                        this.Consultar_Asignaciones();
                        this.Consultar_CategoriasDisponibles();

                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },
        Eliminar_Categoria(value) {
            /**
             * @General
             * Función eliminar registro;
             */
            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Confirmación',
                text: '¿Dar de baja Asignación  \'' + value.CODIGO_CATEGORIA + '\' -  \'' + value.CATEGORIA + '\' ? ',
                acceptText: 'Aceptar',
                cancelText: 'Cancelar',
                accept: () => {
                    const url = this.$store.state.global.url
                    this.$vs.loading();
                    this.axios.post(url + 'app/v1_OrdenCompra/bodega_categoria', {
                            codigo: value.CODIGO,
                            estado: 'N',
                            tipo: value.TIPO,
                            operacion: 'B',
                        })
                        .then(resp => {
                            this.$vs.loading.close();
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Asignación Categoría',
                                    text: resp.data.mensaje,
                                })

                            } else {
                                this.Lista_Asignaciones_categoria_depto = [];
                                this.Consultar_Asignaciones();
                                this.Consultar_CategoriasDisponibles()
                                this.$vs.notify({
                                    color: '#danger',
                                    title: 'Asignación Categoría',
                                    text: resp.data.mensaje,
                                });

                            }
                        })
                        .catch(() => {
                            this.$vs.loading.close();
                        })
                    this.$vs.loading.close();

                }
            })

        },

        // },
        LimpiarCampos() {
            this.cb_busqueda_empresa = ''; //Limpiar los valores seleccionados multiselec
            this.cb_agrupacion = ''; //Limpiar los valores seleccionados multiselec
            this.cb_hospital = ''; //Limpiar los valores seleccionados multiselec
            this.cb_bodegaDespacho = '';
            this.cb_bodegaPadre = '';
            this.cb_tipoBodega = '';
            this.Lista_empresa = [];
            this.id_empresa_seleccionado = '';
            this.Lista_departamentos = [];
            this.Lista_agrupacion = [];
            this.id_agrupacion_selec = '';
            this.Lista_hospital = [];
            this.id_hospital_selec = '';
            this.Lista_bodegaDespacho = [];
            this.id_bodegaDespacho_selec
            this.Lista_bodegaPadre = [];
            this.id_bodegaPadre_selec = '';
            this.Lista_tipoBodega = [];
            this.id_tipoBodega_selec = '';
            this.Codigo_bodega = '';
            this.Nombre_bodega = '';
            this.Direccion_correo = '';
            this.Cuenta_transito = '';
            this.Cuenta_local = '';
            this.cuenta_despacho_paciente = '';
            this.tipo_bodega_contable = '';
            this.control_maximo_minino = false;
            this.crear_existencia_inicial = false;
            this.autorizado_despacho = false;
        },
    },
}
</script>

<style scoped>
.footer {
    height: 50px;
    margin-top: -65px;
    margin-left: 10px;
}

.label-size {
    font-size: 16px;
    font-weight: bold;
}

.label-sizep {
    font-size: 15px;
}
</style>
