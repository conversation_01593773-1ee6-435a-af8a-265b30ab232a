<template>
<div class="w-full pr-2" style="min-width: 370px;">
    <DxDataGrid v-bind="DefaultDxGridConfiguration" :ref="dataGridRefKey" :data-source="antecedentesDataSource" :visible="ExpedienteCargado" @edit-canceled="(e)=> {vistaPrevia=false; this.editorSave(e)}" @option-changed="editorDatagrid">
        <DxToolbar>
            <DxItem name="groupPanel" />
            <DxItem name="searchPanel" />
            <DxItem location="after" template="opcionesTemplate" />
        </DxToolbar>

        <DxGroupPanel :visible="true" />
        <DxGrouping :auto-expand-all="false" />

        <DxEditing :allow-updating="false" :allow-adding="true" :allow-deleting="false" mode="popup">
            <DxPopup v-bind="popupConf" :title="vistaPrevia? 'Visualización de Antecedentes Personales':'Registro de Antecedentes Personales'" >
                <DxToolbarItem widget="dxButton" toolbar="bottom" :options="{
                                        text: 'Guardar',
                                        type: 'success',
                                        useSubmitBehavior: true,
                                        onClick: submit
                                    }" location="after" :visible="!vistaPrevia">
                </DxToolbarItem>
                <DxToolbarItem widget="dxButton" toolbar="bottom" :options="{
                                        text: 'Cancelar',
                                        type: 'danger',
                                        useSubmitBehavior: true,
                                        onClick: ()=>{this.dataGrid.cancelEditData()}
                                    }" location="after" :visible="!vistaPrevia">
                </DxToolbarItem>
            </DxPopup>

            <DxForm labelMode="floating">
                <DxItem item-type="group" :col-span="2" :col-count="2">
                    <DxItem data-field="AntUltimaComida" editor-type="dxTextArea" :editor-options="{ height: 100, readOnly:vistaPrevia }">
                        <DxLabel :text="'Última comida'" />
                    </DxItem>
                    <DxItem data-field="AntMedicos" editor-type="dxTextArea" :editor-options="{ height: 100, readOnly:vistaPrevia }">
                        <DxLabel :text="'Médicos'" />
                    </DxItem>
                    <DxItem data-field="AntQuirurgicos" editor-type="dxTextArea" :editor-options="{ height: 100, readOnly:vistaPrevia }">
                        <DxLabel :text="'Quirúrgicos'" />
                    </DxItem>
                    <DxItem data-field="AntTraumaticos" editor-type="dxTextArea" :editor-options="{ height: 100, readOnly:vistaPrevia }">
                        <DxLabel :text="'Traumáticos'" />
                    </DxItem>
                    <DxItem data-field="AntAlergicos" editor-type="dxTextArea" :editor-options="{ height: 100, readOnly:vistaPrevia }">
                        <DxLabel :text="'Alérgicos'" />
                    </DxItem>
                    <DxItem data-field="AntMedicamentos" editor-type="dxTextArea" :editor-options="{ height: 100, readOnly:vistaPrevia }">
                        <DxLabel :text="'Medicamentos que toma'" />
                    </DxItem>
                </DxItem>
                <DxItem :col-count="2" :col-span="2" item-type="group" :visible="Sexo=='F'" caption="Antecedentes Ginecológicos">
                    <DxItem item-type="group" :col-span="1" :col-count="4">
                        <DxItem data-field="AntGinecoFUR" editor-type="dxDateBox" :col-span="4" :editor-options="{readOnly:vistaPrevia, max:new Date()}">
                            <DxLabel :text="'Fecha de última regla'" />
                        </DxItem>
                        <DxItem data-field="AntGinecoGestas" editor-type="dxNumberBox" :editor-options="numericOptionGineco" />
                        <DxItem data-field="AntGinecoPartos" editor-type="dxNumberBox" :editor-options="numericOptionGineco" />
                        <DxItem data-field="AntGinecoAbortos" editor-type="dxNumberBox" :editor-options="numericOptionGineco" />
                        <DxItem data-field="AntGinecoCesareas" editor-type="dxNumberBox" :editor-options="numericOptionGineco" />
                    </DxItem>
                    <DxItem item-type="group" :col-span="1">
                        <DxItem data-field="AntGinecologico" editor-type="dxTextArea" :editor-options="{ height: 100, readOnly:vistaPrevia}" :col-span="1">
                            <DxLabel :text="'Ginecobstétricos'" />
                        </DxItem>
                    </DxItem>
                </DxItem>
                <DxItem item-type="group" :col-span="2" :col-count="2">
                    <DxItem data-field="AntFamiliares" editor-type="dxTextArea" :editor-options="{ height: 100, readOnly:vistaPrevia }">
                        <DxLabel :text="'Familiares'" />
                    </DxItem>
                    <DxItem data-field="AntHabitos" editor-type="dxTextArea" :editor-options="{ height: 100, readOnly:vistaPrevia }">
                        <DxLabel :text="'Hábitos'" />
                    </DxItem>
                    <DxItem data-field="AntOtros" editor-type="dxTextArea" :editor-options="{ height: 100, readOnly:vistaPrevia }">
                        <DxLabel :text="'Otros'" />
                    </DxItem>
                    <DxItem data-field="AntSistemas" editor-type="dxTextArea" :editor-options="{ height: 100, readOnly:vistaPrevia }">
                        <DxLabel :text="'Revisión por sistemas'" />
                    </DxItem>
                </DxItem>
            </DxForm>
        </DxEditing>

        <DxColumn :width="50" type="buttons" :buttons="[
                {
                    icon:'fas fa-eye',
                    hint:'Ver',
                    onClick: (obj)=>{
                        this.vistaPrevia = true
                        this.dataGrid.editRow(obj.row.rowIndex)
                    }
                }
            ]" :allow-reordering="false" />

        <DxColumn :width="150" data-field="FechaExpediente" caption="Fecha expediente" format="dd/MM/yyyy HH:mm:ss" data-type="date" :visible="false" />
        <DxColumn :width="130" data-field="FechaAntecedente" caption="Fecha antecedente" format="dd/MM/yyyy HH:mm:ss" data-type="date" sort-order="desc" />

        <DxColumn :width="200" data-field="AntUltimaComida" caption="Última comida" :customizeText="DisplayExpression" :encode-html="false" />
        <DxColumn :width="210" data-field="AntAlergicos" caption="Alérgicos" :customizeText="DisplayExpression" :encode-html="false" />
        <DxColumn :width="210" data-field="AntMedicamentos" caption="Medicamentos" :customizeText="DisplayExpression" :encode-html="false" />
        <DxColumn :width="210" data-field="AntMedicos" caption="Médicos" :customizeText="DisplayExpression" :encode-html="false" />
        <DxColumn :width="210" data-field="AntQuirurgicos" caption="Quirúrgicos" :customizeText="DisplayExpression" :encode-html="false" />
        <DxColumn :width="210" data-field="AntTraumaticos" caption="Traumáticos" :customizeText="DisplayExpression" :encode-html="false" />
        <DxColumn :width="210" data-field="AntOtros" caption="Otros antecedentes" :customizeText="DisplayExpression" :encode-html="false" />
        <DxColumn :width="210" data-field="AntSistemas" caption="Revisión sistemas" :customizeText="DisplayExpression" :encode-html="false" />
        <DxColumn :width="210" data-field="AntHabitos" caption="Habitos" :customizeText="DisplayExpression" :encode-html="false" />
        <DxColumn :width="210" data-field="AntFamiliares" caption="Familiares" :customizeText="DisplayExpression" :encode-html="false" />

        <DxColumn caption="Ginecológicos" alignment="center" :visible="Sexo=='F'">
            <DxColumn :width="90" data-field="AntGinecoGestas" caption="Gestas" />
            <DxColumn :width="90" data-field="AntGinecoPartos" caption="Partos" />
            <DxColumn :width="90" data-field="AntGinecoAbortos" caption="Abortos" />
            <DxColumn :width="90" data-field="AntGinecoCesareas" caption="Cesáreas" />
            <DxColumn :width="100" data-field="AntGinecoFUR" caption="FUR" data-type="date" format="dd/MM/yyyy" />
            <DxColumn :width="200" data-field="AntGinecologico" caption="Ginecológico" :customizeText="DisplayExpression" :encode-html="false" />
        </DxColumn>

        <DxColumn caption="Persona ingresa" alignment="center">
            <DxColumn :width="150" data-field="PersonaRegistro" caption="Usuario" />
            <DxColumn :width="150" data-field="Puesto" caption="Puesto" />
        </DxColumn>

        <DxColumn :width="110" data-field="IdAntecedente" :visible="false" />
        <DxColumn :width="110" data-field="idHistorial" :visible="false" />
        <DxColumn :width="110" data-field="Empresa" :visible="false" />
        <DxColumn data-field="empresaEmpleado" :visible="false" />
        <DxColumn data-field="cod_empleado" :visible="false" />
        <DxColumn data-field="tipomedico" :visible="false" />
        <DxColumn data-field="Equipo" :visible="false" />

        <template #opcionesTemplate>

            <ExpedienteGridToolBar v-bind="$props" :visible="ExpedienteCargado" :pdfExportItems="expotItems" @add="addRow" @refresh="Cargar" :report-param="$props">

                <DxButton id="botonBarra" icon="fas fa-history" hint="Ver Antecedente previos" @click="() => {ModalPrevios = true}" />
            </ExpedienteGridToolBar>

        </template>
    </DxDataGrid>
    <InfoPopup v-bind="popupConf" :width="popupConf.maxWidth" :visible.sync="ModalPrevios" title="Antecedente Previos del Paciente">
        <DxScrollView :scroll-by-conent="true" width="100%" height="100%">
            <div class="expediente-evolucion-container">
                <AntecedentesPrevios v-bind="$props" />
            </div>
        </DxScrollView>
    </InfoPopup>
</div>
</template>

<script>
import {
    DxDataGrid,
    DxColumn,
    DxEditing,
    DxPopup,
    DxForm,
    DxLabel,
    DxToolbar,
    DxGrouping,
    DxGroupPanel
} from 'devextreme-vue/data-grid'

import {
    DxItem
} from 'devextreme-vue/form'
import 'devextreme-vue/text-area'
import {
    DxScrollView
} from 'devextreme-vue/scroll-view';

import CustomStore from 'devextreme/data/custom_store'
import DxButton from 'devextreme-vue/button'

import {
    DxToolbarItem
} from 'devextreme-vue/popup'
import {
    DxPopup as InfoPopup
} from 'devextreme-vue/popup'
import {
    DefaultDxGridConfiguration,
    fieldLabelIconActuales
} from './data'
import ExpedienteBase from './EXPBase.vue'
const dataGridRefKey = 'my-data-grid'
export default {
    name: 'Antecedentes',
    extends: ExpedienteBase,
    components: {
        DxDataGrid,
        DxColumn,
        DxEditing,
        DxPopup,
        DxForm,
        DxItem,
        DxLabel,
        DxButton,
        DxScrollView,
        DxGrouping,
        DxGroupPanel,

        DxToolbar,
        DxToolbarItem,
        ExpedienteGridToolBar: () => import("./ExpedienteGridToolBar.vue"),
        AntecedentesPrevios: () => import("./EXP007.vue"),
        InfoPopup,
    },
    data() {
        return {
            /**Listado de campos de ingreso */
            fieldLabelIconActuales,
            /**Registro de antecedente que se selecciona el el grid */
            antecedenteSeleccionado: null,
            antecedentesDataSource: new CustomStore({
                key: 'id',
                load: () => {
                    return this.antecedentes
                },
                insert: (values) => {
                    this.GrabarAntecedente(values)
                },
            }),

            /**Configuraciones multiview Es el objeto que se visualiza en el detalle ej. Antecedente quirurgico */
            ViewSelectedItem: null,
            ViewSelectedIndex: 0,
            //configuracion de menu de exportar pdf, estos son los nombres de los reportes :)
            expotItems: [{
                text: 'Antecedentes Personales',
                reportName: 'Antecedentes Personales',
                key: 'actuales'
            }, ],
            /**Listado de antecedentes asociados a la admisión se hizo en otra variable ya que el custom store de devextreme no funciona bien ya que sighos hace dos peticiones y no solo una*/
            antecedentes: [],
            antecedentesPrevios: [],

            previosLoaded: false,
            dataGridRefKey,
            /**Indica si se muestran los antecedentes previos o actuales en el grid */
            antShowOption: 'actuales',
            vistaPrevia: true,
            ModalPrevios: false,
            numericOptionGineco: {
                max: 100,
                min: 0,
                format: '#',
                readOnly: this.vistaPrevia,
                step: 0
            },

            DefaultDxGridConfiguration,
            infoLocal: null,
        }
    },
    props: {
        Sexo: null,
    },
    methods: {
        submit() {
            this.dataGrid.saveEditData()
        },
        Cargar() {
            if (this.SerieAdmision && this.CodigoAdmision) {
                this.fieldLabelIcon
                this.CargarActuales()
            } else {
                this.antecedentes = []
                this.antecedentesPrevios = []
                this.previosLoaded = false
                this.refreshDataGrid()
            }
        },
        CargarActuales() {
            if (this.SerieAdmision && this.CodigoAdmision)
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaAntecedentesPreviosPDF', {
                    Serie: this.SerieAdmision,
                    Admision: this.CodigoAdmision
                })
                .then(resp => {
                    this.antecedentes = resp.data.json.map((x, index) => {
                        return {
                            id: index,
                            AntUltimaComida: this.$limpiar_saltos_tabulares(x.AntUltimaComida),
                            AntAlergicos: this.$limpiar_saltos_tabulares(x.AntAlergicos),
                            AntMedicamentos: this.$limpiar_saltos_tabulares(x.AntMedicamentos),
                            AntGinecoGestas: this.$limpiar_saltos_tabulares(x.AntGinecoGestas),
                            AntGinecoPartos: this.$limpiar_saltos_tabulares(x.AntGinecoPartos),
                            AntGinecoAbortos: this.$limpiar_saltos_tabulares(x.AntGinecoAbortos),
                            AntGinecoCesareas: this.$limpiar_saltos_tabulares(x.AntGinecoCesareas),
                            AntMedicos: this.$limpiar_saltos_tabulares(x.AntMedicos),
                            AntQuirurgicos: this.$limpiar_saltos_tabulares(x.AntQuirurgicos),
                            AntTraumaticos: this.$limpiar_saltos_tabulares(x.AntTraumaticos),
                            AntOtros: this.$limpiar_saltos_tabulares(x.AntOtros),
                            IdAntecedente: null,
                            IdHistorial: null,
                            AntGinecologico: this.$limpiar_saltos_tabulares(x.AntGinecologico),
                            Empresa: 'MED',
                            FechaAntecedente: x.FechaAntecedente,
                            FechaExpediente: x.FechaExpediente,
                            AntGinecoFUR: x.AntGinecoFUR,
                            AntSistemas: this.$limpiar_saltos_tabulares(x.AntSistemas),
                            AntFamiliares: this.$limpiar_saltos_tabulares(x.AntFamiliares),
                            AntHabitos: this.$limpiar_saltos_tabulares(x.AntHabitos),
                            PersonaRegistro: x.PersonaRegistra,
                            Puesto: x.Puesto,

                            empresaEmpleado: null,
                            cod_empleado: null,
                            tipomedico: null,
                            Corporativo: null,
                            Equipo: null,
                        }
                    })
                    this.antecedentesDataSource.load().then(
                        () => {
                            this.refreshDataGrid()
                        },
                        () => {
                           // console.error('error loading data customstore' + JSON.stringify(error))
                        }
                    )
                })
            else {
                this.antecedentes = []
                this.refreshDataGrid()
            }
        },
        GrabarAntecedente(values) {
            let postData = {
                "SerieAdmision": this.SerieAdmision,
                "Admision": this.CodigoAdmision,
                "AntecedenteUltimaComida": this.$reemplazar_tabulares(values.AntUltimaComida),
                "AntecedenteAlergia": this.$reemplazar_tabulares(values.AntAlergicos),
                "AntecedenteMedicamento": this.$reemplazar_tabulares(values.AntMedicamentos),
                "AntecedenteginecologicoG": values.AntGinecoGestas,
                "AntecedenteGinecologicoP": values.AntGinecoPartos,
                "AntecedenteGinecologicoAb": values.AntGinecoAbortos,
                "AntecendenteGinecologicoCesarea": values.AntGinecoCesareas,
                "AntecedenteGinecologicoFUR": values.AntGinecoFUR,
                "AntecedenteGinecologico": this.$reemplazar_tabulares(values.AntGinecologico),
                "AntecedenteMedicos": this.$reemplazar_tabulares(values.AntMedicos),
                "AntecedenteQuirurgicos": this.$reemplazar_tabulares(values.AntQuirurgicos),
                "AntecedenteTraumaticos": this.$reemplazar_tabulares(values.AntTraumaticos),
                "AntecedenteOtros": this.$reemplazar_tabulares(values.AntOtros),
                "RevisionSistemas": this.$reemplazar_tabulares(values.AntSistemas),
                "AFamiliares": this.$reemplazar_tabulares(values.AntFamiliares),
                "AHabitos": this.$reemplazar_tabulares(values.AntHabitos),
            }
            this.axios.post("/app/v1_ExpedienteEvolucion/InsertarExpedienteAntecedentes", {
                ...postData
            }).then(() => {
                this.Cargar()
                sessionStorage.removeItem('AntecedentesPersonales')
                this.$emit('onSaved', )
            })
        },
        refreshDataGrid() {
            this.dataGrid.refresh()
            this.dataGrid.clearGrouping()
        },

        addRow() {
            this.vistaPrevia = false
            this.infoLocal = JSON.parse(sessionStorage.getItem('AntecedentesPersonales'))
            setTimeout(() => {
                this.dataGrid.addRow()
            }, 50);
        },
        DisplayExpression(e) {
            return this.$saltos_tab_to_html(e.value)
        },
        editorSave(e) {
            if (e.changes && e.changes.length) {
                sessionStorage.setItem('AntecedentesPersonales', JSON.stringify(e.changes[0].data))
            }
        },
        editorDatagrid(e) {
            if (e.fullName === "editing.changes" && e.value[0] !== undefined && this.infoLocal !== null) {
                e.value[0].data = this.infoLocal
                this.infoLocal = null
            }
        },
    },
    watch: {
        'antecedenteSeleccionado'(newval, oldval) {
            if (!oldval)
                this.ViewSelectedItem = this.antSel[0]
        },
        //tambien se puede con la serie y numero de admisión
        'NumeroExpediente'(newval) {
            if (newval != undefined)
                this.Cargar()
        },
        'antShowOption'() {
            this.Cargar()
        },
    },
    computed: {
        dataGrid: function () {
            return this.$refs[dataGridRefKey].instance
        },
        fieldLabelIcon() {
            return this.antShowOption == 'actuales' ?
                this.fieldLabelIconActuales : this.fieldLabelIconPrevios.concat(this.fieldLabelIconActuales)
        }
    },

}
</script>
