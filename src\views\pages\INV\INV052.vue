<template>
    <vx-card title="Aceptación Traslados de Farmacias">

        <vs-popup classContent="popup-example" title="Enviar Correo Electrónico" :active.sync="popUpEmail"  v-on:keydown.esc="$emit('close')" tabindex="0" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
            <sm-email v-bind=confEnvioCorreo  @success="popUpEmail=false" />
        </vs-popup>

        <vs-popup classContent="popup-example" title="Confirmación Anulación" :active.sync="popUpAnulacion" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
                <form>

                    <div class="flex flex-wrap">
                        <br>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                            <label class="label-sizem">Anular aceptación de traslado {{ trasladoAnular.IDREQUISICIONENC }} </label>                            
                        </div>
                        <br>      
                    </div>
                    <!---------- Motivo ------------>
                    <div class="w-full md:w-full lg:w-full xl:w-full">
                        <label class="typo__label">Motivo:</label>
                        <div class="vs-con-input">
                            <input type="text" class="vs-inputx vs-input--input large" cols="40" rows="5" v-model="motivoAnulacion">
                        </div>
                    </div>
                    <vs-divider></vs-divider>

                    <div class="w-full md:w-full lg:w-full xl:w-full">
                        <vs-button color="warning"  style="float:right;margin-left:10px;" @click="motivoAnulacion=''; popUpAnulacion = false;">Cancelar</vs-button>                    </div>
                        <vs-button color="primary" style="float:right" type="filled" icon-pack="feather" icon="icon-save" @click="AnularMovimiento(trasladoAnular)"> Anular</vs-button>
                    <vs-divider></vs-divider>

                </form>
            </div>
        </vs-popup>

        <!----------------------- Consulta de movimientos ---------->
        <vs-popup classContent="popup-example" :title="descripcionEmergente" :active.sync="popUpConsultaAceptacionTraslado">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">   
                <div style="border:2px solid #1B5E20;border-radius:5px;padding:5px;font-size:12px;background-color: #C8E6C9">
                    Sucursal Origen: <b style="font-size:1vw">{{sesion.sesion_sucursal_nombre}}</b>
                </div>  
                <div class="flex">
                    <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <b> Movimiento No. </b>&nbsp;
                        <b style="font-size:2vw">{{idMovimiento}}</b>
                    </div>   
                    <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2" style="align-self:right; direction: rtl;">
                        <b> <small>Solicitud No. </small></b>&nbsp;
                        <b style="font-size:2vw"><small>{{idSolicitud}}</small></b>
                    </div>
                </div>
                <div class="w-1/2 md:w-1/2 lg:w-6/12 xl:w-6/12">
                        <b> <small>Fecha Solicitud: </small></b>
                    <b>{{movimientoSeleccionado.FECHAFINALIZADO}}</b>
                </div>
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2" v-if="consultaTraslado || consultaAceptacion">
                    <b> <small>Estado: </small></b>&nbsp;
                    <b>{{estadoMovimiento}}</b>
                </div> 

                <!--- Ubicación Destino -->
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                    <br>
                    <label class="typo__label">Bodega Fuente:</label>
                    <strong>{{idBodegaFuente}}</strong>
                    <vs-input  type="text" v-model="bodegaFuente.NOMBRE" :disabled="deshabilitarBodegaFuente" class="w-full"/>
                </div>  
                <br>                      
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                    <br>
                    <label class="typo__label">Bodega Destino:</label>
                    <strong>{{idBodegaDestino}}</strong>
                    <vs-input  type="text" v-model="bodegaDestino.NOMBRE" :disabled="deshabilitarBodegaDestino" class="w-full"/>
                </div>
                <vs-divider>Detalle</vs-divider>

                <!--- Mostrar detalle de la orden--->
                <vs-table2 max-items="10" pagination :data="listaDetalleMovimiento">

                    <template slot="thead">
                        <th>Linea</th>
                        <th>Codigo</th>
                        <th>Producto</th>
                        <th>Unidad Medida</th>
                        <th>Cant. Trasladar</th>
                        <th>Cant. Aceptada</th>
                    </template>

                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2 width='5%'>
                                {{ indextr+1 }}
                            </vs-td2>
                            <vs-td2 width='5%'>
                                {{ tr.IDPRODUCTOFK }}
                            </vs-td2>  
                            <vs-td2 width='50%'>
                                <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                                        
                                    {{tr.DESCRIPCION_PRODUCTO}}                                        
                                </div>
                            </vs-td2>
                            <vs-td2 width='15%'>
                                <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                                        
                                    {{tr.DESCRIPCION_UNIDAD}}
                                </div>
                            </vs-td2>
                            <vs-td2 width='10%'>
                                {{tr.CANTIDAD_PEDIDA}}
                            </vs-td2>
                            <vs-td2 width='10%'>
                                <ValidationProvider name="Existencias" :rules="'required|min:0|max:'+tr.CANTIDAD_PEDIDA" v-slot="{ errors }" class="required">
                                    <vs-input  type="number"  v-model="tr.CANTIDAD_ENTREGA" class="w-full" disabled @input="VerificarExistencias(tr)" 
                                                :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"/>                                    
                                </ValidationProvider>
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>
                <vs-divider></vs-divider>
                <div class="flex flex-wrap">
                    <div class="w-full md:w-full lg:w-full xl:w-full">
                        <vs-textarea  :disabled="consultaTraslado" label="Observaciones" type="text" class="w-full" v-model="observaciones" />
                    </div>
                </div>
                <vs-divider></vs-divider>
                <div class="flex">
                    <div class="w-full">
                        <b><small >Trasladado por: </small></b>&nbsp;
                        <b>{{movimientoSeleccionado.CORPORATIVO_SOLICITANTE +' - '+movimientoSeleccionado.IDCORPORATIVOSOLICITAFK}}</b>&nbsp;
                        <b><small v-if="movimientoSeleccionado.ESTADO=='F'">/ Aceptado por: </small></b>&nbsp;
                        <b v-if="movimientoSeleccionado.ESTADO=='F'">{{movimientoSeleccionado.CORPORATIVO_FINALIZA +' - '+movimientoSeleccionado.IDCORPORATIVOFINALIZAFK}}</b>
                    </div> 
                </div>             
                <vs-divider></vs-divider>
                <div>
                    <vs-button v-if="consultaAceptacion" color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" id="btn_confirmacion" @click="MovimientoRecepcion()">Aceptar Traslado</vs-button>
                    <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-printer" id="btn_movimiento" @click="ConsultarMovimientoReporte(movimientoSeleccionado)"> Imprimir </vs-button>                                        
                </div>
                <vs-divider></vs-divider>
            </div>
        </vs-popup>

        <div class="content content-pagex">
            <vs-divider></vs-divider>
            <br>
            <div class="flex flex-wrap">                
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                    <label class="typo__label">Bodega Destino:</label>      
                    <strong>{{ bodegaDestino.CODIGO }}</strong>  
                    <multiselect
                        v-model="bodegaDestino"
                        :options="listaBodegasDestino"
                        :show-labels="false"
                        :allow-empty="false"                        
                        :custom-label="BodegaSeleccionada"
                        placeholder="Seleccionar bodega destino..."
                        @input="onChangeBodegaDestino"
                        >
                    </multiselect>     
                </div>
            </div>
            <div class="flex flex-wrap">
    
                <div style="margin-right: 20px" class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <label class="typo__label">Estado</label>
                    <multiselect
                    v-model="estadoActual"
                    :options="listaEstado"
                    :searchable="true"
                    :close-on-select="true"
                    :show-labels="false"
                    :allow-empty="false"
                    :custom-label="operacionSeleccionada"
                    placeholder="Seleccionar estado..."
                    @input="onChangeEstado">
                    <span slot="noOptions">Estados no disponibles</span>
                    </multiselect>
                </div>

                <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7 m-1">
                    <label class="typo__label" >Fecha Inicial:</label>                    
                    <vs-input type="date" v-model="fechaInicio" name="date1" />
                </div>
    
                <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7 m-1">
                    <label class="typo__label" >Fecha Final:</label>                    
                    <vs-input type="date" v-model="fechaFinal" name="date2" />
                </div>

                <vs-button style="float:left;margin: 20px" color="primary" type="filled" icon-pack="fas" icon="fa-search" @click="ConsultaMovimientosEncabezado()"> Búsqueda</vs-button>                
    
            </div>
            <vs-alert v-if="bodegaDestino.VisualizarTraslados && bodegaDestino.VisualizarTraslados == 'S'" :active.sync="Alertas[0]" color="success" icon-pack="feather" icon="icon-info" closable close-icon="icon-x" class="mb-1">
                Se muestran todos los traslados para los estados: Trasladados, Aceptado, Anulado
            </vs-alert>
            <vs-divider></vs-divider>
    
            <vs-table2 max-items="10" pagination :data="listaMovimientosEncabezado" search>
    
                <template slot="thead">
                    <th width="130px">Nº. Movimiento</th>
                    <th width="130px">Nº. Solicitud</th>
                    <th>Fuente</th>
                    <th>Fecha(s)</th>
                    <th>Observaciones</th>
                    <th></th>
    
                </template>
    
                <template slot-scope="{data}" :allowResizing='true'>
                    <tr :key="indextr" v-for="(tr, indextr) in data">
    
                        <vs-td2 width='5%'>
                            {{tr.IDMOVIMIENTOFK}}
                        </vs-td2>

                        <vs-td2 width='5%'>
                            {{tr.IDREQUISICIONENC}}
                        </vs-td2>
    
                        <vs-td2 width='15%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                {{tr.IdBodegaFuentefk +' - '+ tr.NOMBRE_BODEGA_FUENTE}}
                            </div>
                        </vs-td2>
    
                        <vs-td2 width='25%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                <label v-if="tr.FECHAFINALIZADO">{{'Trasladado: '+tr.FECHAFINALIZADO+' - Usuario: '+tr.IDCORPORATIVOSOLICITAFK}}</label>
                                <br v-if="tr.FECHAFINALIZADO">      
                                <label v-if="tr.FECHARECIBIDA">{{'Aceptado: '+tr.FECHARECIBIDA+' - Usuario: '+tr.IDCORPORATIVOFINALIZAFK}}</label>
                                <br v-if="tr.FECHARECIBIDA">
                                <label v-if="tr.FECHAANULACION">{{'Anulado: '+tr.FECHAANULACION}}</label>
                            </div>    
                        </vs-td2>

                        <vs-td2  width='35%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                {{ tr.OBSERVACIONES }}
                            </div>
                        </vs-td2>  
    
                        <vs-td2 v-if="idEstadoSeleccionado  == 'E'" width='15%%' align="right" style="white-space:nowrap;min-width: 200px;">
                            <vx-tooltip text="Consultar" style="display:inline-block;margin-right:20px">
                                <vs-button color="primary" icon-pack="fas" icon="fa-eye" @click="VisualizarAceptarMovmiento(data[indextr],'ACEPTACION')"></vs-button>                              
                            </vx-tooltip>
                            <vx-tooltip text="Imprimir" style="display:inline-block;margin-right:20px">
                                <vs-button color="primary" icon-pack="feather" icon="icon-printer" @click="ConsultarMovimientoReporte(data[indextr])"></vs-button>
                            </vx-tooltip>  
                            <vx-tooltip text="Anular" style="display:inline-block;margin-right:2px">
                                <vs-button color="red" icon-pack="fas" icon="fa-times"   @click="popUpAnulacion=true; trasladoAnular = data[indextr];"></vs-button>
                            </vx-tooltip>
                        </vs-td2>                      
                        <vs-td2 v-else-if="idEstadoSeleccionado  != 'E'" width='10%' align="right" style="white-space:nowrap;min-width: 200px;">
                            <vx-tooltip text="Consultar" style="display:inline-block;margin-right:30px">
                                <vs-button color="primary" icon-pack="fas" icon="fa-eye" @click="VisualizarAceptarMovmiento(data[indextr],'CONSULTA')"></vs-button>    
                            </vx-tooltip>
                            <vx-tooltip text="Imprimir" style="display:inline-block;margin-right:2px">
                                <vs-button color="primary" icon-pack="feather" icon="icon-printer" @click="ConsultarMovimientoReporte(data[indextr])"></vs-button>
                            </vx-tooltip>
                        </vs-td2>
    
                    </tr>
                </template>
            </vs-table2>

        </div>
    </vx-card>
</template>
    
<script>
import Multiselect from 'vue-multiselect'
import moment from "moment"
import "vue-multiselect/dist/vue-multiselect.min.css"    
export default {
    components: {
        Multiselect
    },
    watch:{
        popUpEmail(value){
            if(!value){
                this.popUpNuevoTraslado = false;
                this.ConsultaMovimientosEncabezado();  
            }            
        },
        popUpNuevoTraslado(value){
            if(value==false){
                this.insertoEncabezado = false;
            }                
        }
    },     
    data() {
        return {
            confEnvioCorreo: {
                to: [],
                cc:  [],
                bcc:  [],
                msg: "",
                sbjct: "Traslado Inter Farmacia",
                maxAttachment: 3, 
                attachTypes: '.pdf, .xlsx',
                allowAddAttachment: false,
                allowDeleteAttachment: false,
                allowEditMsg:true,
                allowEditSbjct:false,
                NombreDelArchivo:"Movimiento Requerimiento.pdf",
                reporte: {
                    Nombre: "Movimiento Requerimiento",
                    Opciones: null,
                    Formato: "PDF"
                } 
            },
            popUpEmail:false,     
            popUpAnulacion:false,
            trasladoAnular:{},
            listaCorreos:[],
            motivoAnulacion:'',
            estadoActual:'',
            bodegaFuente:'',
            bodegaDestino:'',    
            Alertas: [false],
            popUpNuevoTraslado: false,   
            popUpConsultaAceptacionTraslado: false,         
            listaEstado: [
                {
                    ID: 'E',
                    DESCRIPCION: 'Trasladados'
                },
                {
                    ID: 'N,R,F',
                    DESCRIPCION: 'Aceptado/Anulado'
                }
            ],
            listaBodegasDestino:[],
            listaBodegasFuente:[],
            listaMovimientosEncabezado:[],
            listaDetalleMovimiento:[],
            permisosTipoBodegas:[],
            fechaInicio: '',
            fechaFinal: '',
            idBodegaFuente: null,  
            idBodegaDestino: null,
            idSolicitud: null,
            idMovimiento: null,
            idEstadoSeleccionado: null,
            movimientoSeleccionado: {},             
            productoSeleccionado: {
                nombre: '',
                marca_comercial: '',
                Principio_activo: '',
                Concentracion: '',
                Presentacion: '',
                Codigo: '',
                codigo_interno: '',
                Activo: 'N',
                Existencias:''
            }, 
            productoBuscar: '',
            producto: [],  
            cantidadSolicitar:'',
            existenciasProducto:'',
            observaciones:'',            
            descripcionEmergente:'',
            deshabilitarBodegaFuente:true,
            deshabilitarBodegaDestino:true,
            VentanaEmergenteProducto:false,   
            insertoEncabezado:false,
            consultaTraslado:false,
            consultaAceptacion:false,
            listaParametrosBaseReportes:{},
            listaParametrosReportes:{},
            permisos_tipos_bodegas:[]     
        }
    },
    async mounted() {
        this.estadoActual = this.listaEstado[0]
        this.idEstadoSeleccionado = this.listaEstado[0].ID;    
        for(let privilegio of this.$store.state.privilegios){
            if(privilegio.Privilegio.includes("TIPO_BODEGA")){                      
                this.permisos_tipos_bodegas.push(privilegio.Privilegio.split('_')[2])                    
            }
        }        
        this.ConsultarBodega('I',this.permisos_tipos_bodegas.join(","));
        this.ConsultarBodega('B',this.permisos_tipos_bodegas.join(","));  
        this.listaCorreos = await this.ConsultarCorreos();   
    },
    computed: {
        sesion() {
            return this.$store.state.sesion
        }
    },
    async beforeCreate(){
        this.listaParametrosBaseReportes = await this.$recupera_parametros_reporte('Movimiento Requerimiento')        
    },
    methods: {  
        async ConsultarCorreos(){
            let correos = []
            correos = await this.axios
                                .post('/app/v1_OrdenCompra/ValoresAgrupacion',{agrupacion:'CorreoInterFarmacia'})
                                .then( resp => { 
                                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                        return resp.data.json.map(correos=>correos.valor)
                                    }
                                    return []
                                })
                                .catch( () =>{return []})
            return correos;                                
        },
        MovimientoRecepcion() {                                        
                this.axios.post('/app/v1_OrdenCompra/DespachoAceptacionMovmiento', {
                        Requisicion_enc: this.movimientoSeleccionado.IDREQUISICIONENC,
                        Productos: this.listaDetalleMovimiento.map((producto)=>producto.IDPRODUCTOFK).toString(),
                        CantidadesDespacho: this.listaDetalleMovimiento.map((producto)=>producto.CANTIDAD_ENTREGA).toString(),
                        NumerosRequisicionDet: this.listaDetalleMovimiento.map((producto)=>producto.IDREQUISICIONDET).toString(),                            
                        Total: 0,
                        Cant_lineas: 0,
                        Observaciones:  this.observaciones,
                        Bodega_solicitante: 0,
                        Bodega_destino: 0,
                        Estado: 'R',
                        Operacion: 'E',
                    })
                    .then(resp => {

                        if (resp.data.codigo == 0) {
                            this.popUpConsultaAceptacionTraslado = false;
                            this.ConsultaMovimientosEncabezado();                                    
                        }
                    })
                
            
        }, 
        VerificarExistencias(producto){
            if(Number(producto.CANTIDAD_ENTREGA) < 0){
                this.$vs.notify({
                    position:'top-center',
                        color: '#B71C1C',
                        title: 'Alerta',
                        text: ' Ingreso un cant. aceptada negativa',
                    });
                producto.CANTIDAD_ENTREGA = 0
            }
            
            if(Number(producto.CANTIDAD_ENTREGA) > Number(producto.CANTIDAD_PEDIDA)){
                this.$vs.notify({
                    position:'top-center',
                        color: '#B71C1C',
                        title: 'Alerta',
                        text: 'No puede aceptar mas que la cant. trasladada',
                    });
                producto.CANTIDAD_ENTREGA = producto.CANTIDAD_PEDIDA
            }
        },        
        async ConsultarMovimientoReporte(datos) {                
            this.listaParametrosReportes.CODIGO_MOVIMIENTO = datos.IDMOVIMIENTOFK
            this.listaParametrosReportes.CODIGO_SOLICITUD = datos.IDREQUISICIONENC
            this.$genera_reporte({
                Nombre: "Movimiento Requerimiento",
                Data_source: this.listaParametrosReportes,
                Data_report: this.listaParametrosBaseReportes
            }).catch(() => {
            })
        },    
        GetDateValue(value) {
            if (value === undefined) {
                return '';
            } else if (value === null) {
                return '';
            } else if (value == '') {
                return '';
            }
            return moment(value).format('DD/MM/YYYY');          
        },  
        ConsultarBodega(Operacion,TipoBodegas) {
            const url = this.$store.state.global.url
            this.axios.post(url+'app/v1_OrdenCompra/consulta_bodega', {
                                                                    operacion: Operacion,
                                                                    lista_tipo_bodegas: TipoBodegas
                                                                    })
                        .then(resp => {
        
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    position:'top-center',
                                    color: '#B71C1C',
                                    title: 'Bodegas',
                                    text: resp.data.mensaje,
                                })
                            } else {
                                if(Operacion == 'B'){
                                    this.listaBodegasDestino = resp.data.json;                                                                     
                                }
                                else if(Operacion == 'I'){
                                    this.listaBodegasFuente = resp.data.json;                          
                                }                                    
                            }
                        })
                        .catch(() => { })
    
        },
        BodegaSeleccionada({CODIGO,NOMBRE}) {
            return `${CODIGO} - ${NOMBRE} `;
        },
        onChangeBodegaFuente(value) {
            if (value !== null && value.length !== 0) {
                this.idBodegaFuente = value.CODIGO;                
            } else {
                this.idBodegaFuente = null;
            }
        },
        onChangeBodegaDestino(value) {                
            if (value !== null && value.length !== 0) {
                this.Alertas[0] = true;
                this.idBodegaDestino = value.CODIGO;
                this.ConsultaMovimientosEncabezado();
            } else {
                this.idBodegaDestino = null;
            }
        },
        operacionSeleccionada({DESCRIPCION}) {
            return `${DESCRIPCION} `
        },
        onChangeEstado(value) {
            if (value !== null && value.length !== 0) {
                this.idEstadoSeleccionado = value.ID;
                this.ConsultaMovimientosEncabezado();
            } else {
                this.idEstadoSeleccionado = '';
            }
        },
        ConsultaMovimientosEncabezado() {
            if(!this.bodegaDestino){
                this.$vs.notify({
                    position:'top-center',
                            color: 'danger',
                            title: 'Inventario',
                            text: 'Seleccione una bodega destino',
                        })
                return
            }
            
            let operacion = this.idEstadoSeleccionado == 'E' ? 'E' : 'P'
            
            this.axios.post('/app/v1_OrdenCompra/ConsultaRequisicion_Enc', {                        
                    Estado: this.idEstadoSeleccionado,
                    fecha_inicio: this.GetDateValue(this.fechaInicio),
                    fecha_final: this.GetDateValue(this.fechaFinal),
                    Operacion: operacion,                    
                    Bodega_destino: this.bodegaDestino.CODIGO,
                    TipoMovimiento: '27,29'
                })
                .then(resp => {

                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                                            position:'top-center',
                                            color: '#B71C1C',
                                            title: 'Inventario',
                                            text: resp.data.mensaje,
                                        })
                        //Limpia la tabla si no existen registros
                        this.listaMovimientosEncabezado = [];
                    } else {
                        this.$vs.notify({
                                            position:'top-center',
                                            color: 'success',
                                            title: 'Inventario',
                                            text: 'Se actualizaron los traslados exitosamente',
                                        })
                        this.listaMovimientosEncabezado = resp.data.json;

                    }
                })
                .catch(() => {
                    this.listaMovimientosEncabezado = [];
                })
        },     
        ConsultarDetalleMovimiento(Operacion = 'CONSULTA') {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_OrdenCompra/ConsultaRequisicion_Det', {
                    Requisicion_enc: this.idSolicitud
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.listaDetalleMovimiento = [];
                    } else {
                        this.listaDetalleMovimiento = resp.data.json;
                        if(Operacion == 'ACEPTACION'){
                            this.listaDetalleMovimiento = this.listaDetalleMovimiento
                                                              .map(detalle =>{  detalle.CANTIDAD_ENTREGA = detalle.CANTIDAD_PEDIDA;
                                                                                return detalle
                                                                             })                            
                        }
                    }
                })
                .catch(() => {
                    this.listaDetalleMovimiento = [];
                })
        }, 
        FinalizarMovimiento() {
            var res_variables = false;
                res_variables = this.ValidacionCampos('ID', 'Solicitud No.', this.idSolicitud, true, 0);

            if(!res_variables) return
            
            if (this.listaDetalleMovimiento.length <= 0) {
                this.$vs.notify({
                position:'top-center',
                    color: '#B71C1C',
                    title: 'Inventario',
                    text: 'Debe tener al menos un producto a trasladar',
                })
                return;
            }

            this.axios.post('/app/v1_OrdenCompra/Requisicion_Enc', {
                    Requisicion_enc: this.idSolicitud,
                    Total: 0,
                    Cant_lineas: this.listaDetalleMovimiento.length,
                    Observaciones: this.observaciones,            
                    Estado: 'E',
                    Operacion: 'F',
                })
                .then(resp => {                            
                    if (resp.data.codigo == 0) {
                        this.popUpNuevoTraslado = false;
                        this.ConsultaMovimientosEncabezado();                        
                    }
                })
            
        }, 
        AnularMovimiento(Datos){
            let bodegaFuente = this.listaBodegasFuente.find(bodega=>bodega.CODIGO == Datos.IdBodegaFuentefk)
            let bodegaDestino = this.listaBodegasDestino.find(bodega=>bodega.CODIGO == Datos.IdBodegaDestinofk)
            var res_variables = false;
                res_variables = this.ValidacionCampos('CADENA', 'Motivo', this.motivoAnulacion, true, 150);

            if(!res_variables) return

            if(!bodegaFuente || !bodegaFuente.CODIGO){
                this.$vs.notify({   position:'top-center',
                                    color: '#B71C1C',
                                    title: 'Inventario',
                                    text: 'No se pudo obtener la bodega fuente para la anulación.',
                                    time: 5000})
                return
            }

            if(!bodegaDestino || !bodegaDestino.CODIGO){
                this.$vs.notify({   position:'top-center',
                                    color: '#B71C1C',
                                    title: 'Inventario',
                                    text: 'No se pudo obtener la bodega fuente para la anulación.',
                                    time: 5000})
                return
            }
            
            this.axios.post('/app/v1_OrdenCompra/Requisicion_Enc', {
                        Requisicion_enc: Datos.IDREQUISICIONENC,       
                        Operacion: 'A'
                    }).then(resp =>{
                        if (resp.data.codigo == 0) {   
                            let cuerpo = this.MensajeCorreoAnulacion(Datos.IDREQUISICIONENC,Datos.NOMBRE_BODEGA_FUENTE,Datos.NOMBRE_BODEGA_DESTINO,this.motivoAnulacion)
                            let destinatarios = [bodegaFuente.DIRECCIONMAIL,bodegaDestino.DIRECCIONMAIL]
                            if(this.listaCorreos.length > 0){
                                destinatarios.push(...this.listaCorreos)
                            }
                            this.confEnvioCorreo.to = destinatarios
                            this.confEnvioCorreo.msg = cuerpo
                            this.CargaReportePorCorreo(Datos,'Anulación Traslado de Farmacias')                                        
                        }
                    })
        },  
        async CargaReportePorCorreo(datos,titulo = 'Traslado Inter Farmacia'){
            this.listaParametrosReportes.CODIGO_MOVIMIENTO = datos.IDMOVIMIENTOFK
            this.listaParametrosReportes.CODIGO_SOLICITUD = datos.IDREQUISICIONENC            
            let opciones =  await this.$prepara_valores_reporte({
                                Nombre: "Movimiento Requerimiento",
                                Data_source: this.listaParametrosReportes,
                                Data_report: this.listaParametrosBaseReportes                                
                            })            
            this.confEnvioCorreo.reporte.Opciones = opciones   
            this.confEnvioCorreo.sbjct = titulo    
            //this.confEnvioCorreo.reporte.Opciones.tiporeporte = "application/vnd.ms-excel"     
            //this.confEnvioCorreo.reporte.Opciones.NombreDelArchivo = "Movimiento Requerimiento.xlsx"   
            this.popUpAnulacion = false  
            this.popUpEmail = true 
            this.$vs.loading.close();    
        },
        MensajeCorreoAnulacion(noSolicitud,bodegaFuente,bodegaDestino,motivo){
            return `Se le informa que tiene una ACEPTACIÓN de traslado de PRODUCTO que ha sido ANULADA, Solicitud No. '${noSolicitud}' De: ${bodegaFuente} Hacia: ${bodegaDestino}\n\nMotivo: ${motivo} Por el Usuario con corporativo: ${this.sesion.corporativo}\n\nSe adjunta el detalle de la solicitud.`
        },
        VisualizarAceptarMovmiento(Datos,Operacion) {            
            if(Operacion == 'CONSULTA'){
                this.descripcionEmergente = 'Consulta Solicitud Traslado';
                this.consultaTraslado = true
                this.consultaAceptacion = false
                this.deshabilitarIngresoAceptacion = true
            }

            if(Operacion == 'ACEPTACION'){
                this.descripcionEmergente = 'Aceptación Traslado';
                this.consultaTraslado = false
                this.consultaAceptacion = true
                this.deshabilitarIngresoAceptacion = false
            }

            this.movimientoSeleccionado = Datos
            this.observaciones = Datos.OBSERVACIONES;
            this.idSolicitud = Datos.IDREQUISICIONENC;
            this.estadoMovimiento = Datos.ESTADOREQUISICION;
            this.idMovimiento = Datos.IDMOVIMIENTOFK;
            this.idBodegaFuente = Datos.IdBodegaFuentefk;
            this.idBodegaDestino = Datos.IdBodegaDestinofk;
            this.bodegaFuente = { CODIGO: Datos.IdBodegaFuentefk,
                                   NOMBRE: Datos.NOMBRE_BODEGA_FUENTE
                                 }
            this.deshabilitarBodegaDestino = true;
            this.deshabilitarBodegaFuente = true;                      
            this.ConsultarDetalleMovimiento(Operacion)

            this.popUpConsultaAceptacionTraslado = true;
        },
        LimpiarCamposProducto() {
            this.productoSeleccionado.Codigo = '';
            this.productoSeleccionado.marca_comercial = '';
            this.productoSeleccionado.Principio_activo = '';
            this.productoSeleccionado.Concentracion = '';
            this.productoSeleccionado.codigo_interno = '';
            this.productoSeleccionado.Presentacion = '';
            this.productoSeleccionado.Existencias = '';
        },
        SeleccionarProducto(obj) {    
            this.productoSeleccionado.Codigo = obj.Codigo;
            this.productoSeleccionado.marca_comercial = obj.Descripcion;
            this.productoSeleccionado.Concentracion = obj.Concentracion;
            this.productoSeleccionado.codigo_interno = obj.codigo_interno;
            this.productoSeleccionado.Presentacion = obj.PresentacionNombre;
            this.productoSeleccionado.Existencias = obj.Existencia;
            document.getElementById("cantidad_sol").focus();
            this.VentanaEmergenteProducto = false;
        },
        CargarProducto() {    
            this.LimpiarCamposProducto();
            let res_variables = false;
                res_variables = this.ValidacionCampos('ID', 'Bodega Destino', this.idBodegaDestino, true, 0);
            if (!res_variables) return
                res_variables = this.ValidacionCampos('ID', 'Bodega Fuente', this.idBodegaFuente, true, 0);                        
            if(!res_variables) return
                res_variables = this.ValidacionCampos('C', 'Tipo Bodega', this.bodegaFuente.TipoBodega, true, 5);
            if(!res_variables) return                

            this.axios.post('/app/v1_OrdenCompra/ConsultaProductosExistenciaBodegaFuente', {
                    Producto: this.productoBuscar.trim(),
                    TipoBodega: this.bodegaFuente.TipoBodega,
                    bodega_fuente:this.bodegaFuente.CODIGO
                })
                .then(resp => {

                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        this.producto = []

                        if(resp.data.json.length==1 && resp.data.json[0].tipo_error == "-1"){
                            this.$vs.notify({
                                time: 5000,
                                position:'top-center',
                                color: 'danger',
                                title: 'Producto',
                                text: resp.data.json[0].descripcion,
                            })
                            return
                        }

                        resp.data.json.map(data => {
                            this.producto.push({
                                ...data
                            })
                        })

                        this.MostrarResultadoProducto(this.producto,this.productoBuscar.trim());

                    } else {

                        this.producto = []
                        this.$vs.notify({
                            position:'top-center',
                            color: 'danger',
                            title: 'Producto',
                            text: (resp.data.codigo < 0) ? resp.data.descripcion : 'Código no existe, es un servicio ó no esta activo',
                        })

                    }

                })
                .catch(() => {
                    this.$vs.loading.close();
                })

        },
        MostrarResultadoProducto(value,codigo_busqueda) {                
            if (value.length === 1) {
                if(codigo_busqueda != value[0].Codigo){
                    this.$vs.notify({
                        timer:6000,
                        position:'top-center',
                        color: 'warning',
                        title: 'Inventario',
                        text: `El código encontrado (${value[0].Codigo}) no corresponde exactamente al buscado.`,
                    })
                }

                value.map(obj => {
                    this.productoSeleccionado.Codigo = obj.Codigo;
                    this.productoSeleccionado.marca_comercial = obj.Descripcion;
                    this.productoSeleccionado.Concentracion = obj.Concentracion;
                    this.productoSeleccionado.codigo_interno = obj.codigo_interno;
                    this.productoSeleccionado.Presentacion = obj.PresentacionNombre;     
                    this.productoSeleccionado.Existencias = obj.Existencia;                   
                    document.getElementById("cantidad_sol").focus();

                })

            } else if (value.length > 1) {
                var productoEncontrado = value.find(obj =>obj.Codigo == codigo_busqueda)
                if(productoEncontrado){                        
                    this.productoSeleccionado.Codigo = productoEncontrado.Codigo;
                    this.productoSeleccionado.marca_comercial = productoEncontrado.Descripcion;
                    this.productoSeleccionado.Concentracion = productoEncontrado.Concentracion;
                    this.productoSeleccionado.codigo_interno = productoEncontrado.codigo_interno;
                    this.productoSeleccionado.Presentacion = productoEncontrado.PresentacionNombre;
                    this.productoSeleccionado.Existencias = productoEncontrado.Existencia;
                    document.getElementById("cantidad_sol").focus();                             
                    return
                }
                this.VentanaEmergenteProducto = true;
            }

        },
        Redondeo(monto){
            return Math.round(((monto) + Number.EPSILON) * 100) / 100
        },
        ValidacionCampos(Tipo, Nombre, valor, obligatorio, cant_maxima) {
                /**
                 * @General
                 * Tipo : N = Númerio, Nombre = Descripción del campo, Valor = A evaluar, Obligatorio= Verdadero o falso,cant_maxima  = Aplica unicamente para un string y define la cantidad de caracteres 
                 * Devuelve un mensaje al usuario si las validaciones no se cumple y aborta, de lo contrario devuelve verdadero
                 */
                if (Tipo == 'ID') {
                    if (valor <= 0) {
                        this.$vs.notify({
                            position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else if (Tipo == 'N') {
    
                    if (valor <= 0 || valor === "") {
                        this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else {
                    if (valor == '' && obligatorio == true) {
                        this.$vs.notify({
                            position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
    
                        if (cant_maxima < valor.length) {
                            this.$vs.notify({
                                position:'top-center',
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: 'Campo ' + Nombre + ' no debe superar los ' + cant_maxima + ' carácteres.',
                            });
                            return false;
                        } else {
                            return true;
                        }
    
                    }
                }
            },
    }
    
    }
</script>
<style>
.label-sizem {
    font-size: 16px;
    font-weight: bold;
}
</style>