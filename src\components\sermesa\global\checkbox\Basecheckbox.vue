<template>
    <label :for="option.value">
      <input
        type="checkbox"
        :value="option.value"
        :id="id"
        v-model="internalChecked"
        name="checkbox-input" />
      {{ option.label }}
    </label>
  </template>
  
  <script>
  export default {
    name: "Basecheckbox",
    props: {
      option: {
        required: true,
        type: Object
      },
      id:{
        required: true,
        type: String
      },
      checked: {
        type: Boolean,
        default: false
      }
    },
    computed: {
      internalChecked: {
        get() {
          return this.checked;
        },
        set(newValue) {
          this.$emit("change", this.option, newValue);
        }
      }
    }
  };
  </script>