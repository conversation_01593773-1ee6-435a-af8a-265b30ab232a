<template>
<div id="Reportes">
    <form @submit="descargarPDF">
        <DxForm :form-data.sync="reportesSelect" labelMode="floating" :show-validation-summary="true">
            <DxGroupItem :col-count-by-screen="colCountByScreenReportes" item-type="group">
                <DxItem v-for="(item, index) in reportes" v-bind:key="index" :data-field="item.Reporte" editor-type="dxCheckBox" css-class="check-box" :visible="(item.Reporte === '101' || item.Reporte === '102') && reportesSelect['100'] !== true ? false : true">
                    <DxLabel :text="item.Nombre" />
                </DxItem>
            </DxGroupItem>
            <DxGroupItem item-type="group">
                <DxButtonItem :button-options="buttonDescargar" horizontal-alignment="center" verical-alignment="center" />
            </DxGroupItem>
        </DxForm>
    </form>

    <!-- <div class="mt-4" style="display: flex; justify-content: center;">
        <vs-button color="success" type="filled" style="width: 200px; size: 20px" @click="descargarPDF">
            <font-awesome-icon :icon="['fas', 'file-pdf']" class="pr-2" style="font-size: 16px" />
            <span>Descargar</span>
        </vs-button>
    </div> -->
</div>
</template>

<script>
import {
    DxForm,
    DxItem,
    DxLabel,
    DxGroupItem,
    DxButtonItem,
} from 'devextreme-vue/form'

export default {
    name: 'MainExpedienteEvolucion',
    components: {
        DxForm,
        DxItem,
        DxLabel,
        DxGroupItem,
        DxButtonItem,
    },
    data() {
        return {
            reportesBackOffice: [{
                    Reporte: '1',
                    Nombre: '1. Antecedentes Personales',
                    NombreReporte: 'Antecedentes Personales',
                    Visible: true
                },
                {
                    Reporte: '2',
                    Nombre: '2. Diagnósticos',
                    NombreReporte: 'Diagnósticos',
                    Visible: true
                },
                {
                    Reporte: '3',
                    Nombre: '3. Evolución',
                    NombreReporte: 'Evolución',
                    Visible: true
                },
                {
                    Reporte: '4',
                    Nombre: '4. Órdenes Médicas',
                    NombreReporte: 'Órdenes Médicas',
                    Visible: true
                },
                {
                    Reporte: '5',
                    Nombre: '5. Control de Órdenes',
                    NombreReporte: 'Control Órdenes',
                    Visible: true
                },
                {
                    Reporte: '6',
                    Nombre: '6. Signos Vitales',
                    NombreReporte: 'Signos Vitales',
                    Visible: true
                },
                {
                    Reporte: '7',
                    Nombre: '7. Sala de Operaciones',
                    NombreReporte: '',
                    Visible: true
                },
                {
                    Reporte: '100', //Se le asignó un valor tan grande, para que al momento de ordenar los reportes, siempre esté de último
                    Nombre: '8. Detalle de Cargos Hospital',
                    NombreReporte: 'Detalle de Cargos Hospital',
                    Visible: true
                },
                {
                    Reporte: '101', //Se le asignó un valor tan grande, para que al momento de ordenar los reportes, siempre esté de último
                    Nombre: 'Cuenta ajena',
                    NombreReporte: '',
                    Visible: true
                },
                {
                    Reporte: '102', //Se le asignó un valor tan grande, para que al momento de ordenar los reportes, siempre esté de último
                    Nombre: 'Precios',
                    NombreReporte: '',
                    Visible: true
                },
            ],

            otrosReportes: [{
                    Reporte: '8',
                    Nombre: 'Plan de Egreso',
                    NombreReporte: 'Egreso'
                },
                {
                    Reporte: '9',
                    Nombre: 'Salud siempre',
                    NombreReporte: 'Formulario Hospitalización',
                },
                {
                    Reporte: '10',
                    Nombre: 'Estadística Sala de Operaciones',
                    NombreReporte: 'Estadistica Sala Operaciones',
                },
                {
                    Reporte: '11',
                    Nombre: 'Cateterismo',
                    NombreReporte: 'Catéteres',
                },
                {
                    Reporte: '12',
                    Nombre: 'Hospitalizaciones previas',
                    NombreReporte: 'Antecedentes Previos',
                },
            ],

            reportes: [],

            reportesSelect: {},

            buttonDescargar: {
                width: 200,
                icon: 'fas fa-file-pdf',
                text: 'Descargar',
                type: 'success',
                useSubmitBehavior: true,
            },
        }
    },
    props: {
        SerieAdmision: null,
        CodigoAdmision: null,
        CodigoPaciente: null,
    },
    methods: {
        async descargarPDF(e) {

            e.preventDefault()

            var obj = []

            var keys = Object.keys(this.reportesSelect)
            keys.sort((a, b) => a - b);

            for (const i of keys) {
                if (this.reportesSelect[i]) {
                    for (const j of this.reportes) {
                        if (j.Reporte === i) {
                            if (i === '100') {
                                await this.obtenerReporteCargosHospital(obj, j.NombreReporte)
                            } else if (i === '7') {
                                await this.buscarRecordOperatorio(obj)
                                await this.buscarRegistroAnestesico(obj)
                            } else if (i === '10') {
                                this.formatFechaEstadisticaSOP(obj, j.NombreReporte)
                            } else if (i === '11') {
                                await this.buscarCateteres(obj, j.NombreReporte)
                            } else {
                                if (j.NombreReporte !== '') {
                                    obj.push({
                                        Nombre: j.NombreReporte,
                                        Opciones: {
                                            tiporeporte: 'application/pdf',
                                            SerieAdmision: this.SerieAdmision,
                                            CodigoAdmision: this.CodigoAdmision,
                                        },
                                    })
                                }
                            }
                        }
                    }
                }
            }

            this.$reporte_unificador(obj)
        },

        async obtenerReporteCargosHospital(obj, nombre) {
            var rep = {
                CodigoAdmision: this.CodigoAdmision,
                Serie: this.SerieAdmision,
                MostrarPrecios: this.reportesSelect['102'] !== undefined && this.reportesSelect['102'] ? 1 : 0,
                IncluirAjenos: this.reportesSelect['101'] !== undefined && this.reportesSelect['101'] ? 1 : 0,
            }
            var parametros = await this.$recupera_parametros_reporte(nombre)

            var x = await this.$prepara_valores_reporte({
                Nombre: nombre,
                Data_source: rep,
                Data_report: parametros
            })

            obj.push({
                Nombre: nombre,
                Opciones: {
                    ...x
                },
            })
        },

        async buscarRegistroAnestesico(obj) {
            await this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaHojasAnestesicas', {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                })
                .then(async resp => {
                    var idHojas = [] //Variable para almacenar todas las hojas de anestesia
                    // var registrosAnestesicos = [] //Variable para almacenar todos los registros anestésico, los cuales son utilizados para generar el PDF

                    resp.data.json.map((x) => {
                        idHojas.push(x.IdHojaAnestesia)
                    })

                    for (const i of idHojas) {

                        await this.axios.post('/app/v1_ExpedienteEvolucion/BuscarIDsRegistroAnestesicoHoja', {
                                Id: i
                            })
                            .then(resp => {
                                if (resp.data.json.length > 0) {
                                    for (const i of resp.data.json) {
                                        obj.push({
                                            Nombre: 'Registro Anestésico',
                                            Opciones: {
                                                SerieAdmision: this.SerieAdmision,
                                                CodigoAdmision: this.CodigoAdmision,
                                                Id: i,
                                                tiporeporte: 'application/pdf',
                                            }
                                        })
                                    }
                                }
                            })
                    }
                })
        },

        async buscarRecordOperatorio(obj) {
            await this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaRecordOperatorio', {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                })
                .then(resp => {
                    if (resp.data.json.IdRecordOperatorio !== undefined && resp.data.json.IdRecordOperatorio !== null) {
                        obj.push({
                            Nombre: 'Record Operatorio',
                            Opciones: {
                                SerieAdmision: this.SerieAdmision,
                                CodigoAdmision: this.CodigoAdmision,
                                Id: resp.data.json.IdRecordOperatorio, //Esta variable es obligatoria, pero el valor no importa ya que siempre trae el último record de la admisión
                                tiporeporte: 'application/pdf',
                            }
                        })
                    }
                })
        },

        async buscarCateteres(obj, nombre) {
            await this.axios.post("/app/v1_ExpedienteEvolucion/BusquedaFechasCateteres", {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision
                })
                .then((resp) => {
                    if (resp.data.length > 0) {
                        for (const i of resp.data) {
                            obj.push({
                                Nombre: nombre,
                                Opciones: {
                                    tiporeporte: 'application/pdf',
                                    IdCateter: i.IdCateter
                                }
                            })
                        }
                    }
                })
        },

        formatFechaEstadisticaSOP(obj, nombre) {
            const year = new Date().toLocaleString('default', {
                year: 'numeric'
            });
            const month = new Date().toLocaleString('default', {
                month: '2-digit'
            });

            obj.push({
                Nombre: nombre,
                Opciones: {
                    Año: year,
                    Mes: month,
                    Opcion: 'Mes',
                    tiporeporte: 'application/pdf',
                }
            })
        },
    },
    mounted() {

        this.reportes.push(
            ...this.reportesBackOffice
        )

        // this.reportes.push(
        //     ...this.otrosReportes
        // )

        for (const i of this.reportes) {
            this.reportesSelect = {
                ...this.reportesSelect,
                [i.Reporte]: true
            }
        }
    },
    computed: {
        colCountByScreenReportes() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 2,
                    md: 3,
                    lg: 4,
                };
        },
    },
}
</script>

<style>
#Reportes .dx-layout-manager .dx-label-h-align .dx-field-item-label {
    white-space: pre-wrap;
    vertical-align: middle !important;
    word-break: keep-all;
}

#Reportes .dx-layout-manager .dx-label-h-align.dx-flex-layout:not(.dx-field-item-label-align) {
    align-items: center !important;
}

#Reportes .dx-item-content .dx-box-item-content {
    place-content: center;
    padding: 5px !important;
    padding-right: 10px !important;
}

#Reportes .dx-field-item-label-content {
    width: 100% !important;
}

#Reportes .dx-layout-manager .dx-label-h-align.dx-flex-layout .dx-field-item-label {
    min-width: 90%;
    max-width: 90%;
}

#Reportes .dx-layout-manager .dx-label-h-align.dx-flex-layout .dx-field-item-content,
.dx-layout-manager .dx-label-h-align.dx-flex-layout .dx-field-item-content-wrapper {
    min-width: 10%;
    max-width: 10%;
}

#Reportes .dx-field-item-label-content {
    width: 90%;
}

#Reportes .check-box {
    border-color: #e3e3e3;
    border-style: dotted;
    border-width: 1px;
}

#Reportes .dx-layout-manager .dx-label-h-align.dx-flex-layout {
    padding: 0px !important;
    padding-left: 5px !important;
    padding-right: 5px !important;
}
</style>
