--SCRIPT CONSIGNACIÓN 30 DE SEPTIEMPRE 2024

-->>MODIFICACIÓN TABLA

-->> Se agrega columna de liquidación para Orden de Compra
USE [HOSPITAL]
GO
ALTER TABLE [dbo].[inv_orden_compra_enc] 
ADD Consignacion CHAR(1) DEFAULT 'N',
	Liquidacion INT DEFAULT NULL
GO

-->> Descripción campo Consignación
EXEC sys.sp_addextendedproperty @name=N'MS_Description'
, @value=N' N - Indica si la Orden no es de Consignación - S - Indica que la Orden es de Consignacióon' 
, @level0type=N'SCHEMA',@level0name=N'dbo'
, @level1type=N'TABLE',@level1name=N'inv_orden_compra_enc', 
@level2type=N'COLUMN',@level2name=N'Consignacion'
GO

-->> Descripción campo Liquidación
EXEC sys.sp_addextendedproperty @name=N'MS_Description'
, @value=N'No. de Liquidación si es con Consignación' 
, @level0type=N'SCHEMA',@level0name=N'dbo'
, @level1type=N'TABLE',@level1name=N'inv_orden_compra_enc', 
@level2type=N'COLUMN',@level2name=N'Liquidacion'
GO


-->> Descripciones de recepción o validación 
-->> Descripción del campo recepcionada
EXEC sys.sp_addextendedproperty @name=N'MS_Description'
, @value=N'N = No recpcionada - S = Si recpecionada' 
, @level0type=N'SCHEMA',@level0name=N'dbo'
, @level1type=N'TABLE',@level1name=N'inv_orden_compra_enc', 
@level2type=N'COLUMN',@level2name=N'recepcionada'
GO

-->> Validacion 
EXEC sys.sp_addextendedproperty @name=N'MS_Description'
, @value=N'No. de validación o contraseña generada.' 
, @level0type=N'SCHEMA',@level0name=N'dbo'
, @level1type=N'TABLE',@level1name=N'inv_orden_compra_enc', 
@level2type=N'COLUMN',@level2name=N'Validacion'
GO

-->> Descripción del campo RevisoConformidad
EXEC sys.sp_addextendedproperty @name=N'MS_Description'
, @value=N'Corporativo del personal que realizo la validación o recepción' 
, @level0type=N'SCHEMA',@level0name=N'dbo'
, @level1type=N'TABLE',@level1name=N'inv_orden_compra_enc', 
@level2type=N'COLUMN',@level2name=N'RevisoConformidad'
GO

-->> Descripción del campo FechaRevisoConformidad
EXEC sys.sp_addextendedproperty @name=N'MS_Description'
, @value=N'Fecha en la que se realizo la validación o recepción' 
, @level0type=N'SCHEMA',@level0name=N'dbo'
, @level1type=N'TABLE',@level1name=N'inv_orden_compra_enc', 
@level2type=N'COLUMN',@level2name=N'FechaRevisoConformidad'
GO
 
-->>MODIFICACIÓN PROCESOS

-->> ALTER sp_inv_orden_compra_manual

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[sp_inv_orden_compra_manual]    Script Date: 18/09/2024 02:11:15 p.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



/********************************************************************************************/
/*	CREADO POR:  		 Blanca Beatríz Cetino Rodríguez  	Numero de Rational:  322393  	*/
/*	FECHA CREACIÓN:		 20/07/2023									                		*/
/*	PRODUCTO - PROCESO:  SOLICITUD COMPRA - ORDEN DE COMPRA	MANUAL		    				*/
/**************************************** DESCRIPCION ***************************************/
/*	Este procedimiento tiene como objetivo insertar la orden de compra de forma manual,		*/
/*	valida la empresas para identificar si es una empresa que pueda ingresar OC de productos*/
/*  o de servicios.	Muestra campos de validación de Orden de Compra, anulación de OC		*/
/*																							*/
/*************************************** MODIFICACIÓN ***************************************/
/*					FECHA				 RESPONSABLE				DESCRIPCION				*/
/*				09/10/2023			Javier Castillo			Ordenes de compra de ortopedia  */
/*	Se agregan parametros para empresa y sucursal para identificar la farmacia a despachar  */
/*	Y un identificador para marcar las ordenes creadas desde las proformas de ortopedia     */
/*																						    */
/********************************************************************************************/
/*				06/11/2023			Javier Castillo			Ordenes de compra de ortopedia  */
/*	Se consultan los dias de credito en la tabla de proveedores en la columna DiasCredito   */
/*	Si esta es mayor a cero se utiliza ese valor de lo contrario se utiliza por defecto 60 dias   */
/*																						    */
/********************************************************************************************/
/*			    11/03/2024 - Blanca Beatríz Cetino Rodríguez - Órdenes de compra Honorarios */
/* Se agregan campos nuevos en el insert Órdenes de Compra para Honorarios					*/
/*				25/03/2024 - Blanca Beatríz Cetino Rodríguez - Órdenes de compra Servicos F.*/
/* Se aagrega validación de si son servicios fijos ingresar Null en campos.					*/
/********************************************************************************************/
/*			   18/09/2024 - Blanca Beatríz Cetino Rodríguez - Órdenes de compra Consignación*/
/* Se agregan campos nuevos en el insert Órdenes de Compra para consignación				*/


ALTER PROCEDURE [dbo].[sp_inv_orden_compra_manual]
	
	 @i_corporativo VARCHAR(5) = ''
	, @i_id_empresas CHAR(3) = ''
	, @i_empresa_real char(3) = ''
	, @i_empresa_u char(3) = ''
	, @i_empresa_sucursal char(3) = ''
	, @i_empresa_bod CHAR(3) = ''
	, @i_id_productoClase CHAR(1)= ''
	, @i_ProductoSubClase INT = 1
	, @i_id_deparamento CHAR(2) = ''
	, @i_observaciones TEXT = ''
	, @i_id_proveedor CHAR(12) = ''
	, @i_granTotal MONEY = 0
	, @i_dias_credito INT = 0
	, @i_pagos INT = 0
	, @i_id_documento INT = 0
	, @i_fecha_inicio DATE = ''
	, @i_fecha_fin DATE = ''
	, @i_estado CHAR(1) = '' 
	, @i_opcion CHAR(1)= ''
	, @i_subopcion CHAR(1)= ''
	, @i_cantLineas INT = 0
	, @i_repcionada CHAR(1) = ''
	, @i_dOrden INT = 0
	, @i_aOrden INT = 0
	, @i_tipoSeleccion CHAR(1) = ''
	, @i_orden_ortopedia CHAR(1) = 'N'
	, @i_numero_proforma int = 0
	, @i_empresaSucursal char(3) = ''
	, @i_ordenCompraA INT = 0 --Orden de Compra para anulación
	, @i_medico INT = 0 --Código de Medico para Orden
	, @i_tipoHono CHAR(1) = ''
	, @i_idOtroPagos INT = 0 --Para Órdenes de Honorarios
	, @i_serief CHAR(3) = ''
	, @i_factura INT = 0
	, @i_liquidacion INT = NULL
	, @i_consignacion CHAR(1) = NULL
	

AS

	--Solicitud creada de forma manual 
	DECLARE @c_empresaProv CHAR(3)  = ''
		, @c_existecodigo CHAR(2)  = ''
		, @c_id_solicitud INT = 0
		, @ErrRaise	VARCHAR(99)	= ''
		, @ErrRaiseNum INT		= 0
		, @Cant INT = 0
		, @RowID INT = 1
		, @CorporativoR1 CHAR(5) = ''
		, @CorrelativoOC INT = 0
		, @w_IdBodegafk INT = 0
		
BEGIN	
	--Inicio de transaccion 
	BEGIN TRAN


	--Inicio de excepcion
	BEGIN TRY
		IF (@i_id_documento = '3') 
		BEGIN

			--Validación de Empresa para Proveedor
			SET @c_empresaProv = (SELECT
									CASE  WHEN EmpresaUnificadora = @i_empresa_u 
										THEN EmpresaUnificadora
										ELSE Empresa
									END	AS EMPRESAPROV
								  FROM ContaDB..EmpresasDefaults
								  WHERE Empresa = @i_id_empresas)

			IF (@i_opcion = 'C')
			BEGIN
				SELECT 
					ICE.IDORDENCOMPRAENC
					, ICE.IDSOLICITUDENCFK, 
					RTRIM(ICE.IDEMPRESASFK) AS IDEMPRESASFK, 
					ICE.TOTAL, ICE.CANT_LINEAS, ICE.OBSERVACIONES, RTRIM(ICE.IDDEPARTAMENTOFK) AS IDDEPARTAMENTOFK,
					DEPT.NOMBRE AS DESCRIPCION_DEPTO, 
					'' AS DESCRIPCION_BODEGA,
					CASE WHEN ICE.IDPRODUCTOCLASEFK = 'P' THEN 'BIEN'
					WHEN ICE.IDPRODUCTOCLASEFK = 'S' THEN 'SERVICIO'
					END AS TIPO_OC,
					RTRIM(ICE.IDPRODUCTOSUBFK) AS IDPRODUCTOSUBFK, ICE.FECHA_MAXIMA_RECEPCION, 
					RTRIM(ICE.IDPROVEEDORFK) AS IDPROVEEDORFK, PROV.NOMBRE AS DESCRIPCION_PROVEEDOR, PROV.NIT ,
					RTRIM( ICE.estado) AS ESTADO,
					CASE WHEN  RTRIM( ICE.estado)  = 'P' THEN 'PROCESO'
					WHEN RTRIM( ICE.estado)  = 'A' THEN 'AUTORIZADO'
					WHEN RTRIM( ICE.estado)  = 'C' THEN 'ANULADO'
					ELSE 'RECHAZADO'
					END AS DESCRIPCION_ESTADO, 
					'' AS EMPRESA_DIRECCION,
					'' AS EMPRESA_CORREO,
					'' AS EMPRESA_TELEFONO,
					FORMAT(ICE.fecha_creacion, 'dd/MM/yyyy') AS FECHA_CREACION_ORDEN,
					'' AS NOMBRE_EMPRESA,
					RTRIM(PROV.Nit) AS PROV_NIT,
					RTRIM(PROV.CONTACTO) AS PROV_CONTACTO,
					RTRIM(PROV.EMAIL) AS PROV_CORREO,
					CONCAT(CONCAT(ICE.IDCORPORATIVOSOLICITAFK, '-'), CONCAT(C.NOMBRES, ' ', C.APELLIDOS)) AS USUARIO,					
					ICE.RECEPCIONADA VALIDADA,
					CASE WHEN  RTRIM(ICE.RECEPCIONADA)  = 'S' THEN 'VALIDADA'
					WHEN RTRIM( ICE.RECEPCIONADA)  = 'N' THEN 'NO VALIDADA'
					END AS DESCRIP_VALIDADA,
					ICE.Validacion VALIDACION,
					ICE.RevisoConformidad USUARIO_MOV
					FROM INV_ORDEN_COMPRA_ENC AS ICE
					INNER JOIN INV_AUTORIZACION_DOCUMENTO_HISTORIAL AS ADH
					ON ICE.IdSolicitudEncfk = ADH.IdSolicitudEncfk
					AND ICE.IdOrdenCompraEnc = ADH.IDORDENCOMPRAENCFK
					INNER JOIN INV_SOLICITUD_COMPRA_ENC AS SC
					ON ICE.IdSolicitudEncfk = SC.IdSolicitudEnc
					INNER JOIN INVENTARIO..DepartamentosINV  DEPT
					ON ICE.IdDepartamentofk = DEPT.CODIGO
					AND DEPT.Empresa = @i_empresa_bod
					INNER JOIN INVENTARIO..PROVEEDORES  AS PROV
					ON ICE.IDPROVEEDORFK = PROV.CODIGO
					AND PROV.EMPRESA = @c_empresaProv
					INNER JOIN CONTADB..CORPORATIVOS AS C
					ON ICE.IdCorporativoSolicitafk = C.CORPORATIVO
					WHERE  ICE.IdCorporativoSolicitafk = @i_corporativo 
					AND ICE.IDEMPRESASFK = @i_id_empresas
					AND (@i_estado IS NULL OR CONCAT(RTRIM(ICE.estado),' ') LIKE '%' + LTRIM(RTRIM(@i_estado )) +'%')
					AND CONVERT(VARCHAR,  ICE.FECHA_CREACION, 23)  >= @i_fecha_inicio 
					AND CONVERT(VARCHAR,  ICE.FECHA_CREACION, 23)  <= @i_fecha_fin
					AND (@i_id_deparamento IS NULL OR CONCAT(RTRIM(ICE.IDDEPARTAMENTOFK),' ') LIKE '%' + LTRIM(RTRIM(@i_id_deparamento)) +'%')
					AND (@i_id_proveedor IS NULL OR CONCAT(RTRIM(PROV.CODIGO),' ') LIKE '%' + LTRIM(RTRIM(@i_id_proveedor)) +'%')
					AND (@i_repcionada IS NULL OR CONCAT(RTRIM(ICE.RECEPCIONADA),' ') LIKE '%' + LTRIM(RTRIM(@i_repcionada)) +'%')
					AND (ICE.IdOrdenCompraEnc >= @i_dOrden OR @i_dOrden = 0)
					AND (ICE.IdOrdenCompraEnc <= @i_aOrden OR @i_aOrden = 0)
					AND (@i_tipoSeleccion IS NULL OR CONCAT(RTRIM(ICE.IdProductoClasefk),' ') LIKE '%' + LTRIM(RTRIM(@i_tipoSeleccion)) +'%')
					ORDER BY ICE.IDORDENCOMPRAENC DESC

					COMMIT TRAN
			END
			ELSE IF (@i_opcion = 'I')
			BEGIN
				IF (@i_subopcion = '1')
				BEGIN
					--VALIDACIÓN DE EMPRESA SEGUN PRODUCTO CLASE SELECCIONADO
					IF (@i_id_productoClase = 'P')
					BEGIN 
						IF @i_id_empresas = 'SEM'
						BEGIN 
							
							SELECT 0 AS codigo, 'Empresa para Ordenes Compra Servicios - Productos ' AS descripcion,  0 AS resultado;
							COMMIT TRAN
						END
						ELSE
						BEGIN
							
							SELECT 0 AS codigo, 'Empresa para Ordenes Compra Servicios' AS descripcion,  1 AS resultado;
							COMMIT TRAN
							
						END 
					END
				END 
				ELSE IF (@i_subopcion = '2')
				BEGIN

					--CORRELATIVO ORDEN DE COMPRA POR EMPRESA	
					SELECT
						@CorrelativoOC = SigOrdenCompra
					FROM ContaDB..Correlativos
					WHERE Empresa = @i_id_empresas


					--ACTUALIZACIÓN DE CORRELATIVO ORDEN DE COMPRA
					UPDATE ContaDB..Correlativos
						SET SigOrdenCompra = @CorrelativoOC +1
					WHERE Empresa = @i_id_empresas


					--INSERTAR NUEVA SOLICITUD
					INSERT INTO  dbo.inv_solicitud_compra_enc
						(IdEmpresasfk
						,total
						,cant_lineas
						,observaciones
						,IdDepartamentofk
						,IdBodegafk
						,IdProductoClasefk	
						,IdProductoSubfk
						,fecha_creacion_orden
						,IdCorporativoSolicitafk
						,estado
						,ProveedorEmpresa )
					VALUES
						( @i_id_empresas
						, @i_granTotal
						, @i_cantLineas
						, CONCAT('REFERENCIA ORDEN DE COMPRA MANUAL - ',@i_observaciones)
						, @i_id_deparamento
						, 0
						, @i_id_productoClase 
						, @i_ProductoSubClase
						, GETDATE()
						, @i_corporativo
						,'P'
						, @c_empresaProv)


					--OBTIENE EL NUMERO DE SOLICITUD GENERADA
					SET @c_id_solicitud = (SELECT @@Identity);

					--SE AGREGA HISTORIAL - SOLICITUD
					INSERT INTO inv_autorizacion_documento_historial
						(IdCorporativofk
						, IdSolicitudEncfk
						, fecha_hora_inserto
						, IdDocumentofk
						, num_documento
						, estado
						, observaciones
						, IdOrdenCompraEncFk)
					VALUES
						(@i_corporativo
						, @c_id_solicitud
						, GETDATE()
						,'4'
						, '1'
						, 'C'
						, 'REFERENCIA ORDEN DE COMPRA MANUAL'
						, @CorrelativoOC)
					
					

					-- inserta el numero de orden de compra en la proforma de ortopedia
					-- Obtiene la bodega de farmacia segun el hospital
					if @i_orden_ortopedia = 'S'
					begin
						update HisProformasOrtopedia 
						   set OrdenCompra = @CorrelativoOC
						 where IdProforma = @i_numero_proforma 

						 select @w_IdBodegafk = codigo 
						   from inventario..bodegas 
						  where empresa = @i_empresa_bod 						   
						   and ACTIVA = 'S' 
						   and codigoAgrupacion = '2' 
						   and Codigo between 11 and 19
						   and Hospital = @i_empresa_sucursal
						   and EmpresaRealX = @i_empresa_real
						
						set @i_dias_credito = (select ISNULL(DiasCredito,@i_dias_credito) from inventario..proveedores
																		 			where empresa = @c_empresaProv and codigo = @i_id_proveedor)

						if @w_IdBodegafk = 0
						begin
							
							SELECT -1 AS codigo, 'La sucursal no cuenta con una bodega de farmacia configurada' AS descripcion,  -1 AS tipo_error;;
							return;
							ROLLBACK TRAN
						end

					end

					--INSERTA EL ENCABEZADO DE LA ORDEN DE COMPRA 
					INSERT INTO dbo.inv_orden_compra_enc
						(IdOrdenCompraEnc
						, IdSolicitudEncfk
						,IdEmpresasfk
						,total
						,cant_lineas
						,observaciones
						,IdDepartamentofk	
						,IdBodegafk
						,IdProductoClasefk
						,IdProductoSubfk
						,IdCorporativoSolicitafk
						,fecha_creacion	
						,estado
						,Tipo
						,cant_pagos_propuesta
						,dias_credito_propuesta
						,IdProveedorfk
						,ProveedorEmpresa
						,orden_ortopedia
						,TipoHono
						,Hospital
						,Medico
						,SerieFacturaMed
						,FacturaMed
						,CodigoOtroPagos
						,Consignacion
						,Liquidacion
						)
					VALUES
						(@CorrelativoOC
						, @c_id_solicitud
						, @i_id_empresas
						, @i_granTotal
						, @i_cantLineas
						, @i_observaciones
						, @i_id_deparamento	
						, @w_IdBodegafk
						, @i_id_productoClase
						, @i_ProductoSubClase
						, @i_corporativo
						, GETDATE()
						, 'P'
						, 'E'
						, @i_pagos
						, @i_dias_credito
						, @i_id_proveedor
						, @c_empresaProv
						, @i_orden_ortopedia
						, CASE WHEN @i_id_productoClase = 'P' OR @i_ProductoSubClase = 1 THEN NULL
						ELSE @i_tipoHono 
						END 
						, CASE WHEN @i_id_productoClase = 'P' OR @i_ProductoSubClase = 1 THEN NULL
						ELSE @i_empresa_sucursal 
						END
						, CASE WHEN @i_id_productoClase = 'P' OR @i_ProductoSubClase = 1 THEN NULL
						ELSE @i_medico 
						END 
						, CASE WHEN @i_id_productoClase = 'P' OR @i_ProductoSubClase = 1 THEN NULL
						ELSE @i_serief 
						END
						, CASE WHEN @i_id_productoClase = 'P' OR @i_ProductoSubClase = 1 THEN NULL
						ELSE @i_factura 
						END
						, CASE WHEN @i_idOtroPagos = '' THEN NULL
						ELSE @i_idOtroPagos 
						END
						, @i_consignacion
						, @i_liquidacion
						
						)	
					
					--OBTIENE EL CORPORATIVO DE RESPONSABLE	
				
					SET @CorporativoR1 = (SELECT IdResponsablefk 
										FROM  inv_autorizacion_documento
										WHERE IdDocumentofk =  @i_id_documento 
											AND estado = 'S' 
											AND num_documento = 1
											AND IdEmpresaFk = @i_id_empresas
											AND IdProductoClasefk = @i_id_productoClase)
						
					--SE AGREGA HISTORIAL - ORDEN COMPRA
					INSERT INTO inv_autorizacion_documento_historial
						(IdCorporativofk
						, IdSolicitudEncfk
						, fecha_hora_inserto
						, IdDocumentofk
						, num_documento
						, estado
						, IdOrdenCompraEncFk)
					VALUES
						(@CorporativoR1
						, @c_id_solicitud
						, GETDATE()
						, @i_id_documento
						, 1
						, 'P'
						, @CorrelativoOC)

						
						COMMIT TRAN
						SELECT 0 AS codigo, CONCAT('Se ha creado la Orden de Compra: ', @CorrelativoOC) AS descripcion, @CorrelativoOC AS resultado;


				END --SUBOPCION 2
			END --OPCION I
			ELSE IF (@i_opcion = 'A') --ANULACIÓN
			BEGIN
					UPDATE dbo.inv_orden_compra_enc
						SET Estado = 'C', 
						IdCorporativoAnula = @i_corporativo,
						FechaAnulacion = getdate()
					Where IdEmpresasfk = @i_id_empresas
					AND IdOrdenCompraEnc = @i_ordenCompraA

					COMMIT TRAN
					SELECT 0 AS codigo, CONCAT('Se ha Anulado la Orden de Compra: ', @i_ordenCompraA) AS descripcion, @i_ordenCompraA AS resultado;
			END
			ELSE BEGIN
				SELECT -1 AS codigo, 'Opción incorrecta' AS descripcion, -1 AS tipo_error;
				ROLLBACK TRAN
			END
		END
		ELSE BEGIN
				SELECT -1 AS codigo, 'Id documento incorrecto' AS descripcion,  -1 AS tipo_error;
				ROLLBACK TRAN
			END 
	END TRY
	BEGIN CATCH
		ROLLBACK TRAN
		SET @ErrRaise = ERROR_MESSAGE()
		SET @ErrRaiseNum = ERROR_NUMBER()
		
		RAISERROR(@ErrRaise,16,@ErrRaiseNum)
	END CATCH

END


-->> ALTER SP_Margen_Tolerancia_Orden_Compra

USE [HOSPITAL]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


/********************************************************************************************/
/*	CREADO POR:		 Juan Carlos Girón	Numero de Rational:322393	 						*/
/*	FECHA CREACIÓN:		 10/08/2023															*/
/*	PRODUCTO - PROCESO: COTIZACIÓN - ORDEN COMPRA MANUAL									*/
/**************************************** DESCRIPCION ***************************************/
/*	Control de margen de tolerancia															*/
/*																							*/
/*************************************** MODIFICACIÓN ***************************************/
/*	FECHA	    RESPONSABLE		DESCRIPCION													*/
/*	10/08/2023	Blanca Cetino 	Se agrega respuesta según validación para interpretación DLL*/
/*	31/01/2024	Juan Carlos Giron   Para impuesto y el Costo del Ultimo Incremento se envia */	
/* 									la empresa Bodega										*/
/*	23/09/2024	Blanca Cetino	Se agrega el código del artículo para identificarlo.		*/
/********************************************************************************************/




ALTER PROCEDURE [dbo].[SP_Margen_Tolerancia_Orden_Compra]
------- Parametros de Entrada -----------

@precio money = 0,
@producto char(13) = NULL

AS
BEGIN


DECLARE @EmpresaBodega char(3)
DECLARE @Empresa char(3)
DECLARE @Empresasucursal char(3)
DECLARE @costoUltimo money
DECLARE @InvCostoUltimoIncremento real
DECLARE @VtmpCostoMenosAllow real
DECLARE @VtmpCostoMayorAllow real
DECLARE @InvFactorImpuestoCompra real
DECLARE @VtmpCostoUnitario real
DECLARE @PorcGanancia real

	
	---1)Averiguando la empresa Default
	SELECT
		@empresa = CODIGO
	FROM CONTADB..EMPRESAS
	WHERE DEFAULTEMPRESA = 'S'

	---2)Averiguando la empresa bodega
	SELECT
		@empresabodega = bodega
	FROM CONTADB..EMPRESASDefaults
	WHERE EMPRESA = @empresa

	---3)Averiguando la empresaDefault de la empresa bodega
	------------------------------------------------------------------------------------
	SELECT
		@empresasucursal = Empresasucursal
	FROM CONTADB..EMPRESASDefaults
	WHERE EMPRESA = @empresabodega

	SELECT
		@InvFactorImpuestoCompra = InvFactorImpuestoCompra,
		@InvCostoUltimoIncremento = InvCostoUltimoIncremento
	FROM Contadb..EmpresasDefaults WITH (NOLOCK)
	WHERE Empresa = @empresabodega---@empresa

	-----------------------------------------------------
	SELECT
		@costoUltimo = CostoUltimo
	FROM Inventario..Productos
	WHERE empresa = @empresabodega
	AND Codigo = @producto
	----------------------------------------------


	-------------------------------------------------------------------------------------
	SELECT	@VtmpCostoMenosAllow = (@costoUltimo * (1 - @InvCostoUltimoIncremento / 100))

	IF (@VtmpCostoMenosAllow < 0)
	BEGIN
		SET @VtmpCostoMenosAllow = 0
	END

	--------------------------------------------------------------------------------------
	SELECT	@VtmpCostoMayorAllow = (@costoUltimo * (1 + @InvCostoUltimoIncremento / 100))

	-----------------------------------------------------------------------------------------------

	SELECT 	@VtmpCostoUnitario = (ROUND(@precio / (1 + @InvFactorImpuestoCompra / 100), 4))

	IF (@VtmpCostoUnitario > @VtmpCostoMayorAllow)	OR (@VtmpCostoUnitario < @VtmpCostoMenosAllow)
	BEGIN


		SELECT	-1 AS codigo,	'El costo unitario del artículo - '+@producto+', Q.' + RTRIM(CONVERT(char, (ROUND(@VtmpCostoUnitario, 4)))) + ' Está fuera del margen permitido para nuevas compras, Verifique por favor, Limites actuales: De Q ' + RTRIM(CONVERT(char, (ROUND(@VtmpCostoMenosAllow, 4)))) + ' a Q.' + CONVERT(char, (ROUND(@VtmpCostoMayorAllow, 4))) AS descripcion,	0 AS tipo_error;
		END

	ELSE
	BEGIN
		SELECT	0 AS codigo, 'El costo unitario es valido del artículo - '+@producto AS descripcion,	0 AS tipo_error;
	END
END


-->>CREACIÓN PROCESO

-->> CREACIÓN SpInvOCConsignacion
USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[SpInvOCConsignacion]    Script Date: 26/09/2024 03:30:53 p.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


/********************************************************************************************/
/*	CREADO POR:			Blanca Beatríz Cetino Rodríguez  	Número de Rational:  RTC-322254 */
/*	FECHA CREACIÓN:		13/09/2024									                		*/
/*	PRODUCTO - PROCESO: ORDEN DE COMPRA POR CONSIGNACIÓN									*/
/**************************************** DESCRIPCION ***************************************/
/*	Este procedimiento tiene como objetivo validar y actualiar liquidación, según la orden 	*/
/*  de compra por consignación.																*/
/*																							*/
/*************************************** MODIFICACIÓN ***************************************/
/*					FECHA				 RESPONSABLE				DESCRIPCION				*/
/*																							*/
/********************************************************************************************/


CREATE PROCEDURE [dbo].[SpInvOCConsignacion]
	@IEmpresaU CHAR(3) = ''
	, @IEmpresa  CHAR(3) = ''
	, @ISucursal CHAR(3) = ''
	, @IEmpresaBod CHAR(3) = ''
	, @ILiquidacion INT = 0
	, @IOrdenCompra INT = 0
	, @IOpcion  CHAR(1) = ''
	, @ISubOpcion CHAR(1) = ''
	
AS
BEGIN	
	DECLARE  @OrdenCompra INT = 0,
			 @Factura CHAR(10) = '',
			 @Status CHAR(1) = '',
			 @Resultado INT = 0
	
	IF(@IOpcion = 'C')
	BEGIN
		--Busqueda de Liquidación y validación 
		IF (@ISubOpcion = '1')
		BEGIN
		--ValLiquidacion
			SELECT @Status = Status
				, @OrdenCompra = ISNULL(OrdenCompra,0)
				, @Factura = Factura
			FROM Inventario..LiquidacionConsignacionEnc
			WHERE Empresa = @IEmpresaBod 
			AND Correlativo = @ILiquidacion

			SET @Resultado = (SELECT	@@RowCount);
			-- Valida que exista la Liquidación 

			IF @Resultado > 0 
			BEGIN
				-- Valida que el estado sea Ingresada
				IF @Status <> 'I'
				BEGIN
					-- Valida si tiene Factura relacionada
					IF  @Status = 'F' OR @Factura <> NULL
					BEGIN
						SELECT -1 AS codigo, 'El número de Liquidación ingresado tiene Factura relacionada, favor verificar.' AS descripcion, -1 AS tipo_error;
					END
					ELSE IF @Status = 'O' OR @OrdenCompra <> NULL
					BEGIN
						SELECT -1 AS codigo, 'El número de Liquidación ingresado tiene Orden de Compra relacionada, favor verificar.' AS descripcion, -1 AS tipo_error;
					END
					ELSE IF @Status = 'N'
					BEGIN
						SELECT -1 AS codigo, 'El número de Liquidación ingresado esta anulada, favor verificar.' AS descripcion, -1 AS tipo_error;
					END
				END
				ELSE
				BEGIN
					SELECT 0 AS codigo, 'El número de Liquidación ingresado es valido' AS descripcion, 0  AS 'resultado';
				END

			END
			ELSE
			BEGIN
				SELECT -1 AS codigo, 'El número de Liquidación ingresado no existe, favor verificar.' AS descripcion, -1 AS tipo_error;
				
			END
		END
		--Obteniendo Datos y detalle  de consignación
		ELSE IF (@ISubOpcion = '2')
		BEGIN
		-- QryLiq
			SELECT L.Correlativo
				, L.Proveedor
				, L.Fecha
				, L.Status
				, CASE WHEN Status = 'I' THEN 'INGRESADA' 
					ELSE (CASE WHEN Status = 'O' THEN 'ORDEN' 
					ELSE (CASE WHEN Status = 'F' THEN 'FACTURADA' END) END) 
				END AS DesStatus
				, L.OrdenCompra
				, L.Factura
				, L.usuario
				, P.Nit
				, P.Nombre
			FROM Inventario..LiquidacionConsignacionEnc AS L 
			LEFT JOIN Inventario..Proveedores AS P 
				ON L.Empresa = P.Empresa 
				AND L.Proveedor = P.Codigo
			WHERE L.Empresa = @IEmpresaBod 
			AND L.Correlativo =@ILiquidacion

		END
		--Recuperara cargos 
		ELSE IF (@ISubOpcion = '3')
		BEGIN 

			SELECT 
				C.producto
				, P.Nombre AS descripcion
				, SUM(C.Cantidad) AS cantidad
				, ISNULL(C.PrecioC,0)  AS precio_unitario --CostoUltimo
				, SUM(C.Precioc * C.Cantidad) 
				, ISNULL(SUM(C.Precioc * C.Cantidad),0) AS subtotal --CostoUltimoTotal
				, C.CostoConvenio
				, ISNULL(SUM(C.Precioc * C.cantidad),0) AS CostoConvenioTotal
			FROM  hospital..cargos C
			LEFT JOIN INVENTARIO..Productos P
			ON P.Empresa = @IEmpresaBod
			AND C.Producto = P.Codigo
			WHERE  (C.Empresa = @IEmpresaU)
				AND (C.Liquidacion = @ILiquidacion )
			GROUP  BY C.Producto, P.Nombre, C.PrecioC, C.CostoConvenio 

		END
		ELSE
		BEGIN
			SELECT -1 AS codigo, 'Sub Opción no existe, validar' AS descripcion, -1 AS tipo_error;
		END 
	END
	ELSE IF (@IOpcion = 'U')
	BEGIN 
		---Actualizar Liquidación
		IF (@ISubOpcion = '1')
		BEGIN 
			--Inicio de transaccion 
			BEGIN TRAN

			UPDATE Inventario..LiquidacionConsignacionEnc
			SET OrdenCompra = @IOrdenCompra,
				Status = 'O'
			WHERE (Empresa =@IEmpresaBod) 
			AND (Correlativo = @ILiquidacion)

			SET @Resultado = (SELECT	@@RowCount);
			
			IF (@Resultado > 0)
			BEGIN
				COMMIT TRAN
				SELECT 0 AS codigo, 'Se actualizo la liquidación correctamente.' AS descripcion, 0  AS 'resultado';
			END
			ELSE 
			BEGIN 
			ROLLBACK TRAN
				SELECT -1 AS codigo, 'No se pudo actualizar Liquidación, favor validar' AS descripcion, -1 AS tipo_error;
			END

		END 
	END
	ELSE 
	BEGIN 
		SELECT -1 AS codigo, 'Opción no existe, validar' AS descripcion, -1 AS tipo_error;
	END 
	  
END

-->>PERMISOS A OBJECTOS
-->>    Permiso Ejecución Store Procedures   USRPROINVENTARIO              

USE [HOSPITAL]
GO
GRANT EXECUTE ON [HOSPITAL].[dbo].[SpInvOCConsignacion] TO [USRPROINVENTARIO];



-->>     Permiso para objetos Consignación 
USE [INVENTARIO]
GO
GRANT SELECT ON [INVENTARIO].[dbo].[LiquidacionConsignacionEnc] TO [USRPROINVENTARIO]; 
GRANT UPDATE ON [INVENTARIO].[dbo].[LiquidacionConsignacionEnc] TO [USRPROINVENTARIO];

USE [HOSPITAL]
GO			
GRANT SELECT ON [HOSPITAL].[dbo].[cargos] TO [USRPROINVENTARIO];

