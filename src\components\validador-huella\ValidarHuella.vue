<template>
    <vx-card title="Validar Huella">
        <div v-if="isToken">
            <form>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-2">
                    <vs-radio class="radioReference" v-model="referencia" vs-value="1" disabled>Corporativo</vs-radio>
                    <vs-radio class="radioReference" v-model="referencia" vs-value="2" disabled>Ajenos</vs-radio>
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-2">
                    <vs-textarea class="textEstado" label="Estado" cols="30" rows="3" v-model="estado" disabled></vs-textarea>
                </div>
                <div>
                    <img v-if="!imgHuella" src="@/assets/images/others/finger.png" alt="">
                    <img v-if="imgHuella" :src="imgHuella" alt="">
                </div>
            </form>
        </div> 
    </vx-card>    
</template>
<script>
    import { fechaActualSistema, isEmptyObject, getIpLocal } from './funciones/funciones'
    import moment from 'moment'

    export default {
        data() {
            return {
                corporativo: null,
                nombre: null,
                referencia: "1",
                imgHuella: null,
                estado: null,
                isToken: false,
                isLectura: false,
                corporativoEnviar: '',
                IpLocal: ''
            }
        },
        props: {
            tipoHuella: {
                type: String,
                default: "2"
            },
            corporativoBuscar: {
                type: String
            }
        },
        methods: {
            push() {
                if(this.referencia == "1"){
                    let fechaBD = 0
                    const fechaActual = fechaActualSistema()

                    while( fechaBD <= fechaActual){
                        this.axios.post('/app/huella/FechaLector', {
                            Opcion: 'C',
                            SubOpcion: '1',
                            Hospital: '0',
                            IpLocal: this.IpLocal
                        })
                        .then( resp => {
                            !isEmptyObject(resp.data.json) ? fechaBD = moment(resp.data.json[0].FechaCreacion, "DD/MM/YYYY hh:mm:ss").toDate().getTime() : 0
                        })

                        new Promise(r => setTimeout(r, 1000))
                    }

                    this.axios.post('/app/huella/FechaLector', {
                        Opcion: 'C',
                        SubOpcion: '5',
                        Hospital: '0',
                        IpLocal: this.IpLocal
                    })
                    .then( resp => {
                        if(!isEmptyObject(resp.data.json)){
                            const resultado = resp.data.json[0]
                            resultado.ImagenHuella ? this.imgHuella = `data:image/png;base64,${resultado.ImagenHuella}` : null
                            this.estado = resultado.Texto + resultado.StatusPlantilla 
                            this.nombre = resultado.Nombre
                            this.corporativo = resultado.Corporativo

                            if(resultado.StatusPlantilla != ''){
                                this.$emit("getValidacionHuella", resultado)
                                if(resultado.StatusPlantilla == 'VERIFICADO'){
                                    this.axios.post('/app/huella/LimpiarTemporalHuella', {
                                        Opcion: 'E',
                                        SubOpcion: "1",
                                        Hospital: "0",
                                        IpLocal: this.IpLocal
                                    })
                                    this.isLectura = true
                                }
                            }
                            
                        }
                    })
                }else{
                    let fechaBD = 0
                    const fechaActual = fechaActualSistema()

                    while( fechaBD <= fechaActual){
                        this.axios.post('/app/huella/FechaLector', {
                            Opcion: 'C',
                            SubOpcion: '1',
                            Hospital: '0',
                            IpLocal: this.IpLocal
                        })
                        .then( resp => {
                            !isEmptyObject(resp.data.json) ? fechaBD = moment(resp.data.json[0].FechaCreacion, "DD/MM/YYYY hh:mm:ss").toDate().getTime() : 0
                        })

                        new Promise(r => setTimeout(r, 1000))
                    }

                    this.axios.post('/app/huella/FechaLector', {
                        Opcion: 'C',
                        SubOpcion: '5',
                        Hospital: '0',
                        IpLocal: this.IpLocal
                    })
                    .then( resp => {
                        if(!isEmptyObject(resp.data.json)){
                            const resultado = resp.data.json[0]
                            resultado.ImagenHuella ? this.imgHuella = `data:image/png;base64,${resultado.ImagenHuella}` : null
                            this.estado = resultado.Texto + " - " + resultado.StatusPlantilla
                            this.nombre = resultado.Nombre
                            this.corporativo = resultado.Corporativo

                            if(resultado.StatusPlantilla != ''){
                                this.$emit("getValidacionHuella", resultado)
                                if(resultado.StatusPlantilla == 'VERIFICADO'){
                                    this.axios.post('/app/huella/LimpiarTemporalHuella', {
                                        Opcion: 'E',
                                        SubOpcion: "1",
                                        Hospital: "0",
                                        IpLocal: this.IpLocal
                                    })
                                    this.isLectura = true
                                }
                            }
                        }
                    })
                }
                if(!this.isLectura){
                    setTimeout( () => {
                        this.push()
                    }, 1000)
                }
                
            },
            activarValidador(corporativo){
                this.IpLocal = getIpLocal()
                this.isToken = true
                this.axios.post('/app/huella/LimpiarTemporalHuella', {
                    Opcion: 'E',
                    SubOpcion: "1",
                    Hospital: "0",
                    IpLocal: this.IpLocal
                })
                .then( () => {
                    this.axios.post('/app/huella/Sensor', {
                        Opcion: 'I',
                        SubOpcion: '2',
                        Hospital: '0',
                        Tipo: this.referencia,
                        Corporativo: corporativo,
                        IpLocal: this.IpLocal
                    })
                    .then( () => {
                        this.isLectura = false
                        this.push()
                    })  
                })
            },
            desactivarValidador(){
                this.axios.post('/app/huella/LimpiarTemporalHuella', {
                    Opcion: 'E',
                    SubOpcion: "1",
                    Hospital: "0",
                    IpLocal: this.IpLocal
                })
                this.isLectura = true
            }
        },
        async mounted() {
            this.referencia = this.tipoHuella
            this.corporativoEnviar = this.corporativoBuscar
            this.isToken = true
        }, 
        activated(){
            this.referencia = this.tipoHuella
            this.corporativoEnviar = this.corporativoBuscar
            this.isToken = true
        },
        created(){
            window.onbeforeunload = this.desactivarValidador
            this.referencia = this.tipoHuella
            this.corporativoEnviar = this.corporativoBuscar
            this.isToken = true
        },
        deactivated(){
            this.axios.post('/app/huella/LimpiarTemporalHuella', {
                Opcion: 'E',
                SubOpcion: "1",
                Hospital: "0",
                IpLocal: this.IpLocal
            })
            this.isLectura = true
        },
        destroyed() {
            this.axios.post('/app/huella/LimpiarTemporalHuella', {
                Opcion: 'E',
                SubOpcion: "1",
                Hospital: "0",
                IpLocal: this.IpLocal
            })
            this.isLectura = true
        },
        watch: {
            tipoHuella(value){
                this.referencia = value
            },
            corporativoBuscar(value){
                this.corporativo = value
            }
        }
    }
    
</script>
<style lang="scss" scoped>
    img {
        width: 200px;
        box-shadow: 0px 5px 10px rgba($color: #000000, $alpha: 0.2);
    }
    .radioReference{
        padding: 5px 5px 5px 5px;
    }
    .textEstado{
        color: #fc0909;
        font-weight: bold;
        font-size: 16pt;
    }
</style>