<template>
<div>
    <vs-popup ref="buscador" :title="`Denegar Factura`" :active.sync="info.Denegado.Mostrar" style="z-index:52000" id="div-with-loading" class="vs-con-loading__container">
        <!-- {{info.Denegado.Datos}} -->
        <ValidationObserver ref="solicitud" v-slot="{ invalid,handleSubmit }" mode="lazy">
            <table v-if="info.Denegado.Datos" width="100%">
                <tr style="font-weight:bold">
                    <td>
                        No. Factura
                    </td>
                    <td>
                        Nombre Médico
                    </td>
                    <td>
                        Valor
                    </td>
                    <td>
                        Lote
                    </td>
                    <td>
                        Motivo
                    </td>
                    <td v-if="info.Denegado.Motivo=='Otros'">
                        Especificar "Otros"
                    </td>
                </tr>
                <tr>
                    <td>
                        {{info.Factura}}
                    </td>
                    <td>
                        {{info.Medico}}
                    </td>
                    <td>
                        {{info.Denegado.Datos.Valor}}
                    </td>
                    <td>
                        {{info.LoteNombre}}
                    </td>
                    <td>
                        <ValidationProvider name="Motivo" rules="required|max:250" v-slot="{ errors }" class="required">
                            <vs-select class=" w-full" v-model="info.Denegado.Motivo" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null">
                                <vs-select-item :key="key" :value="item" :text="item" v-for="(item,key) in info.Denegado.Motivos" />
                            </vs-select>
                        </ValidationProvider>
                    </td>
                    <td v-if="info.Denegado.Motivo=='Otros'">
                        <ValidationProvider name="DenegarOtros" rules="required|max:250" v-slot="{ errors }" class="required">
                            <vs-input class="w-full" v-model="info.Denegado.MotivoOtros" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" required />
                        </ValidationProvider>
                    </td>
                </tr>
            </table>
            <vs-divider></vs-divider>
            <div class="flex">
                <vs-spacer></vs-spacer>
                <vs-button @click.native="handleSubmit(Otros().Denegar)" :disabled="invalid">Aceptar</vs-button>
            </div>
        </ValidationObserver>
    </vs-popup>

    <!-- CONSOLIDACIÓN DE LOTE -->
    <vs-popup :title="`Partida por Lote`" :active.sync="Consolidacion.Mostrar" style="z-index:52000" id="div-with-loading" class="vs-con-loading__container">
        <!-- {{Consolidacion.Partida}} -->
        <vs-table2 tooltip :data="Consolidacion.Partida" exportarExcel>
            <template slot="header">
                <h4>Consolidado de cuentas</h4>
                <small>La consolidación es solo con fines de verificación, Se guardara cada partida contable por factura </small>
            </template>
            <template slot="thead">
                <th width="100px">No. Cuenta</th>
                <th>Cuenta</th>
                <th width="100px">Debe</th>
                <th width="100px">Haber</th>
            </template>

            <template slot-scope="{data}">
                <tr :key="indextr" v-for="(tr, indextr) in data">
                    <vs-td2>
                        {{tr.cuenta}}
                    </vs-td2>
                    <vs-td2>
                        {{tr.nombre}}
                    </vs-td2>
                    <vs-td2 noTooltip>
                        <div v-if="tr.debe!=0" style="text-align:right">
                            {{$formato_moneda(tr.debe,4)}}
                        </div>
                    </vs-td2>
                    <vs-td2 noTooltip>
                        <div v-if="tr.haber!=0" style="text-align:right">
                            {{$formato_moneda(tr.haber,4)}}
                        </div>
                    </vs-td2>
                </tr>
                <tr style="background-color:rgb(52, 152, 219)" v-if="Consolidacion.Partida.length>0">
                    <td class="p-2 pl-5" style="color:white" colspan="2">TOTAL</td>
                    <td class="p-2 pl-5" style="color:white;text-align:right">{{$formato_moneda(totalConsolidado.debe,4)}}</td>
                    <td class="p-2 pl-5" style="color:white;text-align:right">{{$formato_moneda(totalConsolidado.haber,4)}}</td>
                </tr>
            </template>
        </vs-table2>
        <vs-divider></vs-divider>
        <div class="flex">
            <vs-spacer></vs-spacer>
            <vs-button color="success" type="filled" @click="Guardar().Generar()">Confirmar Lote</vs-button>
        </div>
    </vs-popup>

    <vx-card title="Pago de Honorarios">
        <ValidationObserver ref="formValidate" v-slot="{  }" mode="lazy">
            <div class="flex mb-5 ">
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                    <SM-Buscar v-model="info.Lote" label="Lote" api="app/Ajenos/Busqueda_Honorarios_Lotes" :api_campos="['Tipo','Periodo','Lote','FechaCreado']" :api_titulos="['Tipo','Año','Lote','Creado']" :api_filtro="{Estado: 'L',Trasladado: 1}" api_campo_respuesta="Id" api_campo_respuesta_mostrar="Lote" :callback_buscar="Consulta().Lotes" :callback_cancelar="Otros().Limpiar_Datos" :disabled_texto="true" :api_preload="true" />
                </div>
                <vs-spacer></vs-spacer>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                    <vs-input label="Hospital" class="w-full" :value="info.Empresa" disabled />
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                    <vs-input label="Fecha de Creación" class="w-full" :value="info.FechaCreado" disabled />
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                    <vs-input label="Fecha de Traslado" class="w-full" :value="info.FechaTraslado" disabled />
                </div>
            </div>
            <div v-if="info.Lote">
                <vs-alert v-if="!info.CuentaAjena">
                    Lote de Honorarios
                </vs-alert>
                <vs-alert v-else color="success">
                    Lote de Cuenta Ajena
                </vs-alert>
            </div>

            <vs-divider></vs-divider>
            <div class="flex mb-5 " v-if="info.Lote">
                <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5 p-1">
                    <div class="contenido">
                        
                        
                        <vs-table2 max-items="10" search tooltip pagination :data="facturas" height="300px" exportarExcel>
                            <template slot="header">
                                <h4>Facturas por Lote</h4>
                                <div><b>Facturas Revisadas: {{facturas.filter(f=>f.EstadoValidacion>0).length}} / {{facturas.length}}</b></div>
                            </template>
                            <template slot="thead">
                                <th width="80px">Cod. Méd</th>
                                <th>Médico</th>
                                <th>Factura</th>
                                <th width="120px">Fecha Fac.</th>
                                <th>Valor</th>
                                <th>Estado</th>
                                <th>Accción</th>
                            </template>

                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data" :class="[tr.EstadoValidacion==1?'autorizado':tr.EstadoValidacion==2?'denegado':tr.EstadoValidacion==3?'revisado':'']">
                                    <vs-td2>
                                        {{tr.Ajeno}}
                                    </vs-td2>
                                    <vs-td2>
                                        {{tr.Medico}}
                                    </vs-td2>
                                    <vs-td2>
                                        {{tr.FacturaSerie}}-{{tr.FacturaNumero}}
                                    </vs-td2>
                                    <vs-td2>
                                        {{tr.FacturaFecha}}
                                    </vs-td2>
                                    <vs-td2 style="text-align:right">
                                        <!-- {{$formato_moneda(tr.Valor,4)}} -->
                                        {{$formato_moneda(Otros().CalcularFacturaCA(tr),4)}}
                                    </vs-td2>
                                    <vs-td2>
                                        {{tr.Estado}}
                                        <!-- {{tr.Partida}} -->
                                    </vs-td2>
                                    <vs-td2 noTooltip>
                                        <div v-if="!info.CuentaAjena">
                                            <vs-button v-if="tr.MostrarPartida" size="small" style="display:inline" :disabled="validarPartida" @click="!validarPartida?Consulta().Consulta_Facturas(tr):null"> Ver Partida</vs-button>
                                            <div v-else-if="tr.EstadoValidacion == null">
                                                <vs-button @click="Otros().Validacion(tr,true)" color="success" size="small" style="display:inline" class="mr-2" icon-pack="feather" icon="icon-check"></vs-button>
                                                <vs-button @click="Otros().Validacion(tr,false)" color="danger" size="small" style="display:inline" class="mr-2" icon-pack="feather" icon="icon-x"></vs-button>
                                            </div>
                                            <vs-button v-else @click="tr.EstadoValidacion=null;tr.Estado='Pendiente';tr.MostrarPartida=true;tr.DenegadoMotivo=null" color="gray" size="small" style="display:inline" class="mr-2" icon-pack="feather" icon="icon-rotate-ccw"></vs-button>
                                        </div>
                                        <div v-else>
                                            <vs-button size="small" style="display:inline" @click="!validarPartida?Consulta().Consulta_Facturas(tr,true):null"> Ver Honorarios</vs-button>
                                        </div>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                </div>
                <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5 p-1">
                    <div class="contenido">
                        <vs-table2 v-if="!info.CuentaAjena" max-items="10" tooltip :data="partida" height="300px" exportarExcel>
                            <template slot="header">
                                <h4>Partida por Factura</h4>
                                <div style="overflow:hidden;height:20px">
                                    <b>Médico:</b> {{info.Medico}}
                                </div>
                                <div style="overflow:hidden;height:20px">
                                    <b>Factura:</b> {{info.Factura}}
                                </div>
                            </template>
                            <template slot="thead">
                                <th width="100px">No. Cuenta</th>
                                <th>Cuenta</th>
                                <th width="120px">Debe</th>
                                <th width="120px">Haber</th>
                            </template>

                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2>
                                        {{tr.cuenta}}
                                    </vs-td2>
                                    <vs-td2>
                                        {{tr.nombre}}
                                    </vs-td2>
                                    <vs-td2 noTooltip>
                                        <div v-if="tr.debe!=0" style="text-align:right">
                                            {{$formato_moneda(tr.debe,4)}}
                                        </div>
                                    </vs-td2>
                                    <vs-td2 noTooltip>
                                        <div v-if="tr.haber!=0" style="text-align:right">
                                            {{$formato_moneda(tr.haber,4)}}
                                        </div>
                                    </vs-td2>
                                </tr>
                                <tr style="background-color:rgb(52, 152, 219)" v-if="partida.length>0">
                                    <td class="p-2 pl-5" style="color:white" colspan="2">TOTAL</td>
                                    <td class="p-2 pl-5" style="color:white;text-align:right">{{$formato_moneda(total.debe,4)}}</td>
                                    <td class="p-2 pl-5" style="color:white;text-align:right">{{$formato_moneda(total.haber,4)}}</td>
                                </tr>
                            </template>
                        </vs-table2>

                        <vs-table2 v-else max-items="10" tooltip :data="honorarios" height="300px" exportarExcel>
                            <template slot="header">
                                <h4>Honorarios por Factura</h4>
                                <div style="overflow:hidden;height:20px">
                                    <b>Médico:</b> {{info.Medico}}
                                </div>
                                <div style="overflow:hidden;height:20px">
                                    <b>Factura:</b> {{info.Factura}}
                                </div>
                            </template>
                            <template slot="thead">
                                <th width="100px">Admisión</th>
                                <th>Cargo</th>
                                <th width="120px">Valor</th>
                                <th width="90px">Acción</th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data" :class="[tr.EstadoValidacion==1?'autorizado':tr.EstadoValidacion==2?'denegado':tr.EstadoValidacion==3?'revisado':'']">
                                    <vs-td2>
                                        {{tr.SerieAdmision}}{{tr.Admision}}
                                    </vs-td2>
                                    <vs-td2>
                                        {{tr.Documento}}
                                    </vs-td2>
                                    <vs-td2 style="text-align:right">
                                        {{$formato_moneda(tr.ValorCalculado,4)}} </vs-td2>
                                    <vs-td2 noTooltip>
                                        <vs-button v-if="tr.EstadoValidacion==null" @click="Otros().Validacion(tr,false)" color="danger" size="small" style="display:inline" class="mr-2" icon-pack="feather" icon="icon-x"></vs-button>
                                        <vs-button v-else @click="tr.EstadoValidacion=null;tr.Estado='Pendiente';tr.MostrarPartida=true;tr.DenegadoMotivo=null" color="gray" size="small" style="display:inline" class="mr-2" icon-pack="feather" icon="icon-rotate-ccw"></vs-button>
                                    </vs-td2>
                                </tr>
                                <tr style="background-color:rgb(52, 152, 219)" v-if="partida.length>0">
                                    <td class="p-2 pl-5" style="color:white" colspan="3">TOTAL</td>
                                    <td class="p-2 pl-5" style="color:white;text-align:right">{{$formato_moneda(totalHonorario,4)}}</td>
                                </tr>
                            </template>
                        </vs-table2>
                        <!-- {{honorariosDenegados}} -->
                        <!-- {{honorarios}} -->
                        <!-- {{cuentas}}
                        <hr>
                        {{partida}} -->
                        <!-- {{honorariosDenegados}} -->
                    </div>
                </div>
            </div>
            <vs-divider></vs-divider>
            <div class="flex bottom">
                <vs-spacer></vs-spacer>
                <vs-button v-if="!info.CuentaAjena" color="success" type="filled" :disabled="!validacionFacturas" @click="validacionFacturas?Guardar().Partida():null">Confirmar Partida Contable</vs-button>
                <vs-button v-else color="success" type="filled" :disabled="!validacionFacturas" @click="validacionFacturas?Guardar().GenerarCuentaAjena():null">Confirmar Lote</vs-button>
            </div>
        </ValidationObserver>
    </vx-card>
    <div class="facturas" v-if="info.MostrarFactura">
    </div>
</div>
</template>

<script>
export default {
    data() {
        return {
            /**
             * Documentación del módulo
             */
            doc_: [],

            /**
             * Editar el monto de la tabla
             */
            info: {
                Lote: null,
                LoteNombre: null,
                CuentaAjena: false,
                Empresa: null,
                FechaCreado: null,
                FechaTraslado: null,
                Medico: null,
                Factura: null,
                Denegado: {
                    Mostrar: false, // Popup
                    Datos: null, // Datos del tr,
                    Motivos: [
                        'Factura Vencida',
                        'Médico cambia de régimen',
                        'Factura con mala descripción',
                        'Factura con datos erroneos',
                        'Otros'
                    ],
                    Motivo: null,
                    MotivoOtros: null
                }
            },
            /**
             * Listado de facturas
             */
            facturas: [],

            /**
             * Información de la partida
             */
            partida: [],

            /**
             * Listado de Honorarios
             */
            honorarios: [],

            /**
             * Listado de Honorarios Denegados
             */
            honorariosDenegados: [],

            /**
             * Cuentas
             */
            cuentas: null,

            /**
             * Consolidación de lote
             */
            Consolidacion: {
                Mostrar: false,
                Partida: []
            },

            /**
             * Listado de permisos
             */
            permisos: {
                editar: false
            }

        }
    },
    computed: {
        // Devuelve si existe otra partida abierta sin validar
        validarPartida() {
            return this.facturas.filter(f => f.MostrarPartida == false && f.EstadoValidacion == null).length > 0
        },
        // Indica si todas las facturas ya se han validado
        validacionFacturas() {
            return this.facturas.filter(f => f.EstadoValidacion == null).length == 0 && this.facturas.length > 0
        },
        total() {
            let debe = 0
            let haber = 0
            this.partida.forEach(item => {
                debe += parseFloat(item.debe)
                haber += parseFloat(item.haber)
            })
            return {
                debe: debe.toFixed(2),
                haber: haber.toFixed(2)
            }
        },
        totalHonorario() {
            let total = 0
            this.honorarios.filter(f => f.EstadoValidacion == null).forEach(item => {
                total += parseFloat(item.ValorCalculado)
            })
            return total
        },
        totalConsolidado() {
            let debe = 0
            let haber = 0
            this.Consolidacion.Partida.forEach(item => {
                debe += parseFloat(item.debe)
                haber += parseFloat(item.haber)
            })
            return {
                debe: debe.toFixed(2),
                haber: haber.toFixed(2)
            }
        }

    },
    components: {
        // Facturas: () => import('./AJE005_Factura.vue')
    },
    methods: {
        //=======================================================================================================
        // CONSULTA
        //=======================================================================================================
        Consulta: function() {
            return {
                Lotes: (item) => {
                    this.info.CuentaAjena = item.EsCuentaAjena == 1
                    this.info.FechaCreado = item.FechaCreado
                    this.info.Empresa = item.Empresa
                    this.info.FechaTraslado = item.TrasladoFecha
                    this.info.LoteNombre = item.Lote
                    this.Consulta().Consulta_Pagos(item.Id)
                },

                Consulta_Pagos: (Lote) => {
                    return this.axios.post('/app/ajenos/Honorarios_Facturas_Consulta', {
                            Lote,
                            Estado: 'L'
                        })
                        .then(resp => {
                            // console.log(resp)
                            this.facturas = []
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.facturas = resp.data.json.map(m => {
                                    return {
                                        ...m,
                                        Estado: 'Pendiente',
                                        MostrarPartida: true, // Indica que el boton de ver partida esta activo
                                        EstadoValidacion: null, // 1:Autorizado 2:Anulado,
                                        DenegadoMotivo: null, // Descripción del la denegación
                                        Partida: [] // Partida contable
                                    }
                                })
                            }
                        })
                },

                Consulta_Facturas: (tr, cuentaAjena = false) => {
                    this.info.Medico = `(${tr.Ajeno}) ${tr.Medico}`
                    this.info.Factura = `${tr.FacturaSerie}-${tr.FacturaNumero}`

                    tr.MostrarPartida = false
                    if (cuentaAjena) {
                        tr.EstadoValidacion = 3;
                        tr.Estado = 'Revisado'
                    }
                    return this.axios.post('/app/ajenos/Honorarios_Facturas_Consulta_Detalle', {
                            IdFactura: tr.Id
                        })
                        .then(resp => {
                            this.honorarios = []
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.honorarios = resp.data.json.map(m => {
                                    return {
                                        ...m,
                                        idFactura: tr.Id,
                                        EstadoValidacion: null, // 1:Autorizado 2:Anulado,
                                        DenegadoMotivo: null, // Descripción del la denegación
                                    }
                                })
                                const a = resp.data.json
                                const b = []

                                // Primer agrupamiento
                                // Se agrupan los tipos y los valores
                                a.forEach(item => {
                                    // Buscando si existe el elemento
                                    const existe = b.filter(f => f.Tipo == item.Tipo && f.EsSeguroSaludSiempre == item.Es_SeguroSaludSiempre)
                                    if (existe.length > 0) {
                                        existe[0].ValorCalculado = parseFloat(existe[0].ValorCalculado) + parseFloat(item.ValorCalculado)
                                    } else {
                                        b.push({
                                            Tipo: item.Tipo,
                                            TipoOrden:item.TipoOrden,
                                            Orden:item.Orden,
                                            Linea:item.Linea,
                                            ValorCalculado: item.ValorCalculado,
                                            EsSeguroSaludSiempre: item.Es_SeguroSaludSiempre,
                                            AjenoTipo: item.AJE_Tipo
                                        })
                                    }
                                })

                                // Buscando la cuenta
                                const cuentas = []
                                const nuevaCuenta = ({
                                    cuenta,
                                    nombre,
                                    debe = 0,
                                    haber = 0
                                }) => {
                                    if (parseFloat(debe) == 0 && parseFloat(haber) == 0) return false
                                    const existe = cuentas.filter(f => f.cuenta == cuenta.trim())
                                    if (existe.length == 0) {
                                        cuentas.push({
                                            cuenta: cuenta.trim(),
                                            nombre: nombre.trim(),
                                            debe: (parseFloat(debe)).toFixed(2),
                                            haber: (parseFloat(haber)).toFixed(2)
                                        })
                                    } else {
                                        existe[0].debe = (parseFloat(existe[0].debe) + parseFloat(debe)).toFixed(2)
                                        existe[0].haber = (parseFloat(existe[0].haber) + parseFloat(haber)).toFixed(2)
                                    }
                                }

                                const EsPequenoContribuyente = tr.TipoContribuyente.trim().toUpperCase() == 'P'
                                
                                let IvaXCobrar = 0
                                b.map(item => {
                                    // Honorarios
                                    const valor = EsPequenoContribuyente ? item.ValorCalculado : parseFloat(item.ValorCalculado) / (((parseFloat(this.cuentas['IVA']) / 100) + 1))
                                    if (!EsPequenoContribuyente) IvaXCobrar += valor * (parseFloat(this.cuentas['IVA']) / 100)
                                    //console.log(item.Tipo,item.EsSeguroSaludSiempre)
                                    if (item.Tipo == 1) {
                                        // Si no es salud siempre
                                        if (item.EsSeguroSaludSiempre == 0) {
                                            // Si es diferente a médico de casa
                                            if (item.AjenoTipo != 'M') {
                                                nuevaCuenta({
                                                    cuenta: this.cuentas['HonorariosMdicosCortesia'],
                                                    nombre: this.cuentas['HonorariosMdicosCortesia_Nombre'],
                                                    debe: valor
                                                })
                                            } else {
                                                nuevaCuenta({
                                                    cuenta: this.cuentas['ServContratadosProfesionales'],
                                                    nombre: this.cuentas['ServContratadosProfesionales_Nombre'],
                                                    debe: valor
                                                })
                                            }
                                        } else {
                                            
                                            nuevaCuenta({
                                                cuenta: this.cuentas['HonorariosSaludSiempre'],
                                                nombre: this.cuentas['HonorariosSaludSiempre_Nombre'],
                                                debe: valor
                                            })
                                        }
                                        nuevaCuenta({
                                            cuenta: this.cuentas['ProveedoresHonorariosMedicos'],
                                            nombre: this.cuentas['ProveedoresHonorariosMedicos_Nombre'],
                                            haber: item.ValorCalculado
                                        })

                                    }
                                    // Interpretacion
                                    if (item.Tipo == 2) {
                                        // if (item.EsSeguroSaludSiempre == 0) {
                                            nuevaCuenta({
                                                cuenta: this.cuentas['HonorariosPorInterpretacion'],
                                                nombre: this.cuentas['HonorariosPorInterpretacion_Nombre'],
                                                debe: valor
                                            })
                                        // } else {
                                        //     nuevaCuenta({
                                        //         cuenta: this.cuentas['HonorariosSaludSiempre'],
                                        //         nombre: this.cuentas['HonorariosSaludSiempre_Nombre'],
                                        //         debe: valor
                                        //     })
                                        // }
                                        nuevaCuenta({
                                            cuenta: this.cuentas['ProveedoresHonorariosMedicos'],
                                            nombre: this.cuentas['ProveedoresHonorariosMedicos_Nombre'],
                                            haber: item.ValorCalculado
                                        })
                                    }
                                    // Cupones
                                    if (item.Tipo == 3) {
                                        nuevaCuenta({
                                            cuenta: this.cuentas['HonorariosSaludSiempre'],
                                            nombre: this.cuentas['HonorariosSaludSiempre_Nombre'],
                                            debe: valor
                                        })
                                        nuevaCuenta({
                                            cuenta: this.cuentas['ProveedoresHonorariosMedicos'],
                                            nombre: this.cuentas['ProveedoresHonorariosMedicos_Nombre'],
                                            haber: item.ValorCalculado
                                        })
                                    }
                                    // Compromiso
                                    if (item.Tipo == 4) {
                                         if (item.EsSeguroSaludSiempre == 0) {
                                            // Si es diferente a médico de casa
                                            if (item.AjenoTipo != 'M') {
                                                nuevaCuenta({
                                                    cuenta: this.cuentas['HonorariosMdicosCortesia'],
                                                    nombre: this.cuentas['HonorariosMdicosCortesia_Nombre'],
                                                    debe: valor
                                                })
                                            } else {
                                                nuevaCuenta({
                                                    cuenta: this.cuentas['ServContratadosProfesionales'],
                                                    nombre: this.cuentas['ServContratadosProfesionales_Nombre'],
                                                    debe: valor
                                                })
                                            }
                                        } else {
                                            nuevaCuenta({
                                                cuenta: this.cuentas['HonorariosSaludSiempre'],
                                                nombre: this.cuentas['HonorariosSaludSiempre_Nombre'],
                                                debe: valor
                                            })
                                        }
                                        nuevaCuenta({
                                            cuenta: this.cuentas['ProveedoresHonorariosMedicos'],
                                            nombre: this.cuentas['ProveedoresHonorariosMedicos_Nombre'],
                                            haber: item.ValorCalculado
                                        })
                                    }
                                })

                                if (!EsPequenoContribuyente) {
                                    nuevaCuenta({
                                        cuenta: this.cuentas['IvaporCobrar'],
                                        nombre: this.cuentas['IvaporCobrar_Nombre'],
                                        debe: IvaXCobrar
                                    })
                                }
                                if (parseFloat(tr.CalculoManejoCuenta) != 0) {
                                    nuevaCuenta({
                                        cuenta: this.cuentas['HonorariosManejoCuenta'],
                                        nombre: this.cuentas['HonorariosManejoCuenta_Nombre'],
                                        haber: tr.CalculoManejoCuenta
                                    })
                                    // Si existe ProveedoresHonorariosMedicos
                                    if (cuentas.filter(f => f.cuenta == this.cuentas['ProveedoresHonorariosMedicos'].trim()).length > 0) {
                                        nuevaCuenta({
                                            cuenta: this.cuentas['ProveedoresHonorariosMedicos'],
                                            nombre: this.cuentas['ProveedoresHonorariosMedicos_Nombre'],
                                            haber: -tr.CalculoManejoCuenta
                                        })
                                    }
                                }

                                if (parseFloat(tr.CalculoIVA) != 0) {
                                    nuevaCuenta({
                                        cuenta: this.cuentas['RetencionIvaPorPagar'],
                                        nombre: this.cuentas['RetencionIvaPorPagar_Nombre'],
                                        haber: tr.CalculoIVA
                                    })
                                    // Si existe ProveedoresHonorariosMedicos
                                    if (cuentas.filter(f => f.cuenta == this.cuentas['ProveedoresHonorariosMedicos'].trim()).length > 0) {
                                        nuevaCuenta({
                                            cuenta: this.cuentas['ProveedoresHonorariosMedicos'],
                                            nombre: this.cuentas['ProveedoresHonorariosMedicos_Nombre'],
                                            haber: -tr.CalculoIVA
                                        })
                                    }
                                }
                                if (parseFloat(tr.CalculoISR) != 0) {
                                    nuevaCuenta({
                                        cuenta: this.cuentas['RetencionIsr'],
                                        nombre: this.cuentas['RetencionIsr_Nombre'],
                                        haber: tr.CalculoISR
                                    })
                                    // Si existe ProveedoresHonorariosMedicos
                                    if (cuentas.filter(f => f.cuenta == this.cuentas['ProveedoresHonorariosMedicos'].trim()).length > 0) {
                                        nuevaCuenta({
                                            cuenta: this.cuentas['ProveedoresHonorariosMedicos'],
                                            nombre: this.cuentas['ProveedoresHonorariosMedicos_Nombre'],
                                            haber: -tr.CalculoISR
                                        })
                                    }
                                }

                                // Recalculando valores IVA
                                setTimeout(() => {
                                    const total = this.total
                                    nuevaCuenta({
                                        cuenta: this.cuentas['IvaporCobrar'],
                                        nombre: this.cuentas['IvaporCobrar_Nombre'],
                                        debe: (total.haber - total.debe)
                                    })
                                }, 100)

                                cuentas.sort((a, b) => (a.debe < b.debe) ? 1 : ((b.debe < a.debe) ? -1 : 0))
                                // console.warn(cuentas)
                                this.partida = cuentas
                                tr.Partida = cuentas

                            }
                        })
                        .catch(() => {

                        })
                },

                Consulta_Cuentas: () => {
                    return this.axios.post('/app/ajenos/Pagos_Partida_Cuentas_Consulta', {})
                        .then(resp => {
                            this.cuentas = null
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.cuentas = resp.data.json[0]
                            }
                        })
                }
            }
        },

        //=======================================================================================================
        // NUEVO
        //=======================================================================================================
        Nuevo: function() {
            return {}
        },

        //=======================================================================================================
        // GUARDAR
        //=======================================================================================================
        Guardar: function() {
            return {
                Partida: () => {
                    this.Consolidacion.Partida = []
                    const partidaConsolidada = []
                    this.facturas.filter(f => f.EstadoValidacion == 1).forEach(el => {
                        el.Partida.forEach(p => {
                            // console.log(p)
                            const existe = partidaConsolidada.filter(f => f.cuenta == p.cuenta)
                            if (existe.length == 0) {
                                partidaConsolidada.push({
                                    ...p
                                })
                            } else {
                                existe[0].debe = (parseFloat(existe[0].debe) + parseFloat(p.debe)).toFixed(2)
                                existe[0].haber = (parseFloat(existe[0].haber) + parseFloat(p.haber)).toFixed(2)
                            }
                        })
                    });
                    partidaConsolidada.sort((a, b) => (a.debe < b.debe) ? 1 : ((b.debe < a.debe) ? -1 : 0))
                    // console.log(partidaConsolidada)
                    this.Consolidacion.Mostrar = true
                    this.Consolidacion.Partida = partidaConsolidada
                },
                Generar: () => {
                    this.$vs.dialog({
                        type: 'confirm',
                        color: 'success',
                        title: 'Confirmación',
                        acceptText: 'Confirmar Lote',
                        cancelText: 'Cancelar',
                        text: `¿Desea confirmar el lote ${this.info.LoteNombre}?`,
                        accept: () => {
                            const factura = []
                            const factura_Denegada = []
                            const factura_Denegada_Motivo = []
                            const cuenta = []
                            const debe = []
                            const haber = []
                            this.facturas.filter(f => f.EstadoValidacion == 1).forEach(el => {
                                el.Partida.forEach(p => {
                                    factura.push(el.Id)
                                    cuenta.push(p.cuenta)
                                    debe.push(p.debe)
                                    haber.push(p.haber)
                                })
                            })

                            this.facturas.filter(f => f.EstadoValidacion == 2).forEach(el => {
                                factura_Denegada.push(el.Id)
                                factura_Denegada_Motivo.push(el.DenegadoMotivo.replace(/,/g, ''))
                            })

                            this.axios.post('/app/ajenos/Pagos_Partida_Nueva', {
                                    lote: this.info.Lote,
                                    factura,
                                    factura_Denegada,
                                    factura_Denegada_Motivo,
                                    cuenta,
                                    debe,
                                    haber
                                })
                                .then(() => {
                                    this.Otros().Limpiar_Datos()
                                })
                        }
                    })
                },
                GenerarCuentaAjena: () => {
                    this.$vs.dialog({
                        type: 'confirm',
                        color: 'success',
                        title: 'Confirmación',
                        acceptText: 'Confirmación de Lote',
                        cancelText: 'Cancelar',
                        text: `¿Desea confirmar el lote ${this.info.LoteNombre}?`,
                        accept: () => {
                            const Honorarios_Denegados = []
                            const Honorarios_Denegados_Motivo = []
                            this.honorariosDenegados.forEach(item => {
                                Honorarios_Denegados.push(item.idHonorario)
                                Honorarios_Denegados_Motivo.push(item.motivo.replace(/,/g, ''))
                            })

                            this.axios.post('/app/ajenos/Pagos_Partida_Nueva', {
                                    lote: this.info.Lote,
                                    Honorarios_Denegados,
                                    Honorarios_Denegados_Motivo,
                                    Cuenta_Ajena: 1
                                })
                                .then(() => {
                                    this.Otros().Limpiar_Datos()
                                })
                        }
                    })
                }
            }
        },

        //=======================================================================================================
        // EDITAR
        //=======================================================================================================
        Editar: function() {
            return {}
        },

        //=======================================================================================================
        // ELIMINAR
        //=======================================================================================================
        Eliminar: function() {
            return {}
        },

        //=======================================================================================================
        // OTROS
        //=======================================================================================================
        Otros: function() {
            return {
                Limpiar_Datos: () => {
                    Object.entries(this.info).forEach(([key]) => {
                        if (typeof this.info[key] != 'object') this.info[key] = null
                    });
                    this.partida = []
                    this.facturas = []
                    this.honorarios = []
                    this.info.CuentaAjena = false
                    this.info.Denegado.Mostrar = false
                    this.info.Denegado.Datos = null
                    this.info.Denegado.Motivo = null
                    this.info.Denegado.MotivoOtros = null
                    this.Consolidacion.Mostrar = false
                    this.Consolidacion.Partida = []
                },
                CalcularFacturaCA: (item) => {
                    // console.log(item)
                    const valor = this.honorariosDenegados.filter(f => f.idFactura == item.Id).reduce((a, b) => a + parseFloat(b.valor), 0)
                    // console.log(valor)
                    return parseFloat(item.Valor) - parseFloat(valor)
                },
                facturasCancelar: () => {
                    this.info.MostrarFactura = false
                    // if (actualizar) this.Consulta().Consulta_Pagos(this.info.Codigo)
                },
                Validacion: (tr, estado) => {
                    if (estado) {
                        tr.EstadoValidacion = 1;
                        tr.Estado = 'Autorizado'
                        this.info.Medico = null
                        this.info.Factura = null
                        this.partida = [];
                    } else {
                        this.info.Denegado.Mostrar = true
                        this.info.Denegado.Motivo = null
                        this.info.Denegado.MotivoOtros = null
                        this.info.Denegado.Datos = tr
                    }
                },
                Denegar: () => {
                    this.info.Denegado.Datos.EstadoValidacion = 2;
                    this.info.Denegado.Datos.Estado = 'Denegado'
                    this.info.Denegado.Datos.DenegadoMotivo = (this.info.Denegado.MotivoOtros) ? this.info.Denegado.MotivoOtros : this.info.Denegado.Motivo
                    if (!this.info.CuentaAjena) {
                        this.info.Medico = null
                        this.info.Factura = null
                        this.partida = []
                    } else {
                        this.honorariosDenegados.push({
                            idHonorario: this.info.Denegado.Datos.Id,
                            idFactura: this.info.Denegado.Datos.idFactura,
                            valor: this.info.Denegado.Datos.ValorCalculado,
                            motivo: (this.info.Denegado.MotivoOtros) ? this.info.Denegado.MotivoOtros : this.info.Denegado.Motivo
                        })
                    }
                    this.info.Denegado.Mostrar = false
                    this.info.Denegado.Datos = null
                    this.info.Denegado.Motivo = null
                    this.info.Denegado.MotivoOtros = null
                }
            }
        }
    },
    mounted() {
        this.Consulta().Consulta_Cuentas()
    }
}
</script>

<style scoped>
.facturas {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100em;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 50px;
    z-index: 99999;
}

.contenido .autorizado {
    background-color: rgba(46, 204, 113, 0.2) !important;
    border: 2px solid rgba(46, 204, 113, 0.7);
}

.contenido .denegado {
    background-color: rgba(231, 76, 60, 0.2) !important;
    border: 2px solid rgba(231, 76, 60, 0.7);
}

.contenido .revisado {
    background-color: rgba(52, 152, 219, 0.2) !important;
    border: 2px solid rgba(52, 152, 219, 0.7);
}
</style>
