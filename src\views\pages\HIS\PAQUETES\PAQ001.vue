<template>
    <vx-card title="Cuadre Admision <PERSON> (Empatador)">
        <div class="container">

            <div class="resumenCuenta">

                <div class="box e">
                    <div style="text-align: center;">
                        <h4>Resumen de la cuenta</h4>
                    </div>
                </div>

                <div class="box f" clearfix>
                    <vs-row>
                        <vs-col vs-justify="center" vs-align="center" vs-w="2">
                            <!-- <Transition name="fade"> -->
                            <!-- <div v-if="resumenCuenta.TotalCuenta"> -->
                            <vs-card class="height:15px">
                                <div slot="header">
                                    <h6>
                                        TotalCuenta
                                    </h6>
                                </div>
                                <div>
                                    <h5>{{ this.$formato_moneda(this.resumenCuenta.TotalCuenta) }}</h5>

                                </div>
                            </vs-card>
                            <!-- </div> -->
                            <!-- </Transition> -->
                        </vs-col>

                        <vs-col vs-justify="center" vs-align="center" vs-w="2">

                            <!-- <Transition name="fade"> -->
                            <!-- <div v-if="resumenCuenta.TotalExcedente"> -->
                            <vs-card class="height:15px">
                                <div slot="header">
                                    <h6>
                                        TotalExcedente
                                    </h6>
                                </div>


                                <h5>{{ this.$formato_moneda(this.resumenCuenta.TotalExcedente) }} </h5>

                            </vs-card>
                            <!-- </div> -->
                            <!-- </Transition> -->
                        </vs-col>

                        <vs-col vs-justify="center" vs-align="center" vs-w="2">
                            <!-- <Transition name="fade"> -->
                            <!-- <div v-if="resumenCuenta.PrecioPaquete"> -->
                            <vs-card class="height:15px">
                                <div slot="header">
                                    <h6>
                                        PrecioPaquete
                                    </h6>
                                </div>
                                <div>
                                    <h5>{{ this.$formato_moneda(this.resumenCuenta.PrecioPaquete) }} </h5>
                                </div>
                            </vs-card>
                            <!-- </div> -->
                            <!-- </Transition> -->
                        </vs-col>

                        <vs-col vs-justify="center" vs-align="center" vs-w="2">
                            <!-- <Transition name="fade"> -->
                            <!-- <div v-if="resumenCuenta.TotalAnticipo"> -->
                            <vs-card class="height:15px">
                                <div slot="header">
                                    <h6>
                                        TotalAnticipo
                                    </h6>
                                </div>
                                <div>
                                    <h5>{{ this.$formato_moneda(this.resumenCuenta.TotalAnticipo) }}</h5>
                                </div>
                            </vs-card>
                            <!-- </div> -->
                            <!-- </Transition> -->
                        </vs-col>

                        <vs-col vs-justify="center" vs-align="center" vs-w="2">
                            <!-- <Transition name="fade"> -->
                            <!-- <div v-if="resumenCuenta.TotalDesuento"> -->
                            <vs-card class="height:15px">
                                <div slot="header">
                                    <h6>
                                        TotalDesuento
                                    </h6>
                                </div>
                                <div>
                                    <h5>{{ this.$formato_moneda(this.resumenCuenta.TotalDesuento) }}</h5>
                                </div>
                            </vs-card>
                            <!-- </div> -->
                            <!-- </Transition> -->
                        </vs-col>

                        <vs-col vs-justify="center" vs-align="center" vs-w="2">
                            <!-- <Transition name="fade"> -->
                            <!-- <div v-if="resumenCuenta.TotalPago"> -->
                            <vs-card class="height:15px">
                                <div slot="header">
                                    <h6>
                                        TotalPago
                                    </h6>
                                </div>
                                <div>
                                    <h5>{{ this.$formato_moneda(this.resumenCuenta.TotalPago) }}</h5>
                                </div>
                            </vs-card>
                            <!-- </div> -->
                            <!-- </Transition> -->
                        </vs-col>
                    </vs-row>

                </div>

                <div class="box g">
                    <vs-button @click="ValidarPago()">
                        Validar Pago
                    </vs-button>

                    <vs-button style="margin-left: 10px;" @click="GenerarReporte()">
                        Reporte Excedentes
                    </vs-button>
                </div>

                <!-- </div> -->

            </div>

            <div class="detalleExcedentes" style="overflow:scroll;height:100%;width:100%;overflow:auto ">

                <div style="text-align: left; margin-left: 10px; margin-top: 10px; margin-bottom: 15px;">
                    <h5>Detalle de Excedentes</h5>
                </div>

                <vs-table2 max-items="10" scroll :data="detalleExcentePaquete">
                    <template slot="thead">
                        <th width="10px" filtro="CodigoProducto">Producto</th>
                        <th width="350px" filtro="Nombre">Nombre</th>
                        <th width="5px" filtro="Cantida">Cantidad</th>
                        <th width="50px" filtro="Excedente">Excedente</th>

                    </template>

                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data" :ref="`list_exedentes${indextr}`"
                            :class="{ 'activo': tr.activo }">
                            <vs-td2>
                                {{ tr.CodigoProducto }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.NombreProducto }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.Cantidad }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.MontoExcedente.toLocaleString("es-GT", { style: "currency", currency: "GTQ" }) }}
                            </vs-td2>

                        </tr>
                    </template>

                </vs-table2>



            </div>
            <div class="excendetesCategoria" style="overflow:scroll;height:100%;width:100%;overflow:auto">

                <div style="text-align: left; margin-left: 10px; margin-top: 10px; margin-bottom: 15px;">
                    <h5>Total Excedentes por Categoria</h5>
                </div>

                <vs-table2 max-items="5" scroll :data="excedentesPorCategoria">
                    <template slot="thead">
                        <!-- <th width="10px" >Categoria</th> -->
                        <th width="350px">Nombre</th>
                        <th width="20px">MontoExcedente</th>

                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data" :ref="`list_excetendescat${indextr}`"
                            :class="{ 'activo': tr.activo }">
                            <!-- <vs-td2>
                                {{ tr.CodigoCategoria }}
                            </vs-td2> -->

                            <vs-td2>
                                {{ tr.NombreCategoria }}
                            </vs-td2>

                            <vs-td2 style="margin-left: 10x;">
                                {{ tr.MontoExcedente.toLocaleString("es-GT", { style: "currency", currency: "GTQ" }) }}
                            </vs-td2>

                        </tr>
                    </template>
                </vs-table2>


            </div>
            <div class="consultaPacientes">



                <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                    <form @submit.prevent="handleSubmit(submitForm())">
                        <div class="flex flex-wrap">
                            <vs-row>

                                <vs-col vs-type="flex" vs-w="6">

                                    <div class="w-1/5  p-2">
                                       <vs-input
                                         label="Serie"
                                         v-model="consultaPaciente.SerieAdmision"
                                         class="w-full"                                         
                                         @focusin="clearPacienteData"
                                        />
                                    </div>

                                    <div class="w-2/5  p-2">
                                        <vs-input
                                          label="Codigo Admision"
                                          v-model="consultaPaciente.CodigoAdmision"
                                          class="w-full"                                          
                                          @focusin="clearPacienteData"
                                          />
                                    </div>


                                    <div class="w-4/5  p-2">                                        
                                        <vs-input
                                          v-model="consultaPaciente.NombrePaciente"
                                          label="Nombre Paciente"
                                          class="w-full"                                          
                                          @focusin="clearAdmisionData"
                                        />                                        
                                    </div>
                                    <div class="w-4/5  p-2">
                                        
                                        <vs-input
                                          v-model="consultaPaciente.ApellidoPaciente"
                                          label="Apellido Paciente"
                                          class="w-full"                                          
                                          @focusin="clearAdmisionData"
                                        />
                                        
                                    </div>
                                </vs-col>
                            </vs-row>
                            <vs-row>
                                <div class="flex w-full p-1">
                                    <div class="pl-2">
                                        <vs-button @click="handleSubmit(ConsultarDatos().ConsultarPaciente())"
                                            :disabled="invalid">
                                            Consultar
                                        </vs-button>
                                    </div>

                                </div>
                            </vs-row>
                        </div>
                    </form>
                </ValidationObserver>
                <vs-table2 max-items="10" search tooltip pagination :data="listaPacientes">
                    <template slot="thead">
                        <th width="10px" filtro="CodigoPaciente">Paciente</th>
                        <th width="200px" filtro="NombrePaciente">NombrePaciente</th>
                        <th width="200px" filtro="ApellidoPaciente">ApellidoPaciente</th>
                        <th width="40px" filtro="DPI">DPI</th>
                        <th width="50px" filtro="NombrePaquete">NombrePaquete</th>
                        <!-- <th width="20px" filtro="Medax">Medax</th> -->
                        <th width="100px" filtro="NombreHospital">NombreHospital</th>
                        <th width="20px" filtro="Admision">Admisión</th>
                        <th width="20px" filtro="Medico">Medico</th>
                        <th width="30px" filtro="Tipo">Tipo</th>
                        <th width="30px" filtro="CodigoPaquete">CodigoPaquete</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data" :ref="`list_${indextr}`"
                            v-on:click=ConsultarDatos().ConsultarDetalle(tr) :class="{ 'activo': tr.activo }">
                            <vs-td2>
                                {{ tr.CodigoPaciente }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.NombrePaciente }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.ApellidoPaciente }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.DPI }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.NombrePaquete }}
                            </vs-td2>

                            <!-- <vs-td2>
                                {{ tr.Medax }}
                            </vs-td2>
             -->
                            <vs-td2>
                                {{ tr.NombreHospital }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.Admision }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.Medico }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.Tipo }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.CodigoPaquete }}
                            </vs-td2>

                        </tr>
                    </template>
                </vs-table2>
            </div>

        </div>
    </vx-card>
</template>



<script>

export default {
    name: "ConsultaPaqwuetesActivos",
    data() {
        return {
            listaPacientes: [],
            detalleExcentePaquete: [],
            excedentesPorCategoria: [],
            resumen: [],
            resumenCuenta: {
                TotalCuenta: 0.00,
                TotalExcedente: 0.00,
                PrecioPaquete: 0.00,
                TotalAnticipo: 0.00,
                TotalDesuento: 0.00,
                TotalPago: 0.00,
            },
            consultaPaciente: {
                Empresa: '',
                NombrePaciente: '',
                ApellidoPaciente: '',
                SerieAdmision: '',
                CodigoAdmision: 0,
                NumeroDocumento: ''
            },
            AplicarPago: {
                SerieAdmision: '',
                CodigoAdmision: '',
                NombrePaciente: '',
                ApellidoPaciente: '',
                CodigoPaquete: '',
                NombrePaquete: '',
                IdPaquete: '',
                NombreMedico: '',
                FechaIngreso: '',
                FechaEgreso: ''
            }

        }
    },
    methods: {
        ConsultarDatos() {
            return {
                ConsultarPaciente: async () => {

                 
                    const resp = await this.axios.post('/app/v1_paquetes_admisiones/ConsultaPaquetesAdmision', this.consultaPaciente)

                    this.listaPacientes = resp.data.json.map(m => {
                        return {
                            ...m,
                        }
                    })
                },
                ConsultarDetalle: async (item) => {




                    const resp = await this.axios.post('/app/v1_paquetes_admisiones/ConsultaDetalleExcedentePaquete', {
                        Empresa: '',
                        SerieAdmision: item.Admision.substr(0, 1),
                        CodigoAdmision: item.Admision.substr(1, item.Admision.length),
                        CodigoPaquete: item.CodigoPaquete,
                        IdPaquete: item.IdPaquete
                    })

                    this.AplicarPago.SerieAdmision = item.Admision.substr(0, 1)
                    this.AplicarPago.CodigoAdmision = item.Admision.substr(1, item.Admision.length)
                    this.AplicarPago.NombrePaciente = item.NombrePaciente
                    this.AplicarPago.ApellidoPaciente = item.ApellidoPaciente
                    this.AplicarPago.CodigoPaquete = item.CodigoPaquete
                    this.AplicarPago.NombrePaquete = item.NombrePaquete
                    this.AplicarPago.IdPaquete = item.IdPaquete
                    this.AplicarPago.NombreMedico = item.NombreMedico
                    this.AplicarPago.FechaIngreso = item.Entrada
                    this.AplicarPago.FechaEgreso = item.Salida



                    this.detalleExcentePaquete = resp.data.json.detalleExcedentes.map(m => {
                        return {
                            ...m,
                        }
                    })

                    this.excedentesPorCategoria = resp.data.json.excedentesPorCategoria.map(m => {
                        return {
                            ...m,
                        }
                    })

                    this.resumenCuenta.TotalCuenta = resp.data.json.resumenPaqueteCuenta.TotalCuenta
                    this.resumenCuenta.TotalExcedente = resp.data.json.resumenPaqueteCuenta.TotalExcedente
                    this.resumenCuenta.PrecioPaquete = resp.data.json.resumenPaqueteCuenta.PrecioPaquete
                    this.resumenCuenta.TotalAnticipo = resp.data.json.resumenPaqueteCuenta.TotalAnticipo
                    this.resumenCuenta.TotalDesuento = resp.data.json.resumenPaqueteCuenta.TotalDesuento
                    this.resumenCuenta.TotalPago = resp.data.json.resumenPaqueteCuenta.TotalPago
                },
            }
        },
        async ValidarPago() {
            
            const resp = await this.axios.post('/app/v1_paquetes_admisiones/ValidarPago', {
                Empresa: '',
                SerieAdmision: this.AplicarPago.SerieAdmision,
                CodigoAdmision: this.AplicarPago.CodigoAdmision,
                CodigoPaquete: this.AplicarPago.CodigoPaquete,
                IdPaquete: this.AplicarPago.IdPaquete,

            })

            this.$vs.dialog({
                type: 'confirm',
                color: '#ED8C72',
                acceptText: 'Continuar',
                title: 'Operación',
                text: resp.data.json[0].Descripcion,
                // clientWidth: 100,
                accept: () => {
                    this.resumenCuenta = []
                    this.detalleExcentePaquete = []
                    this.excedentesPorCategoria = []
                    this.listaPacientes = []
                },
                cancel: () => {

                }
            })

        },
        async GenerarReporte() {

            let postData = {
                Empresa: '',
                SerieAdmision: this.AplicarPago.SerieAdmision,
                CodigoAdmision: this.AplicarPago.CodigoAdmision,
                CodigoPaquete: this.AplicarPago.CodigoPaquete,
                NombrePaciente: this.AplicarPago.NombrePaciente,
                ApellidoPaciente: this.AplicarPago.ApellidoPaciente,
                NombrePaquete: this.AplicarPago.NombrePaquete,
                IdPaquete: this.AplicarPago.IdPaquete,
                NombreMedico: this.AplicarPago.NombreMedico,
                FechaIngreso: this.AplicarPago.FechaIngreso,
                FechaEgreso: this.AplicarPago.FechaEgreso
            }

            this.$reporte_modal({
                Nombre: 'ReporteExcendetesPaqueteAdmision',
                Opciones: {
                    ...postData
                },
                Formato: 'PDF'
            })
        },
        clearPacienteData() {
            this.consultaPaciente.NombrePaciente = '';
            this.consultaPaciente.ApellidoPaciente = '';
        },
        clearAdmisionData() {
            this.consultaPaciente.SerieAdmision = '';
            this.consultaPaciente.CodigoAdmision = '';
        },


    }



}

</script>

<style scoped>
* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

.container {
    display: grid;
    grid-gap: 5px;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    right: 0;
    justify-content: space-around;
    grid-template-areas: "resumenCuenta resumenCuenta"
        "detalleExcedentes excendetesCategoria"
        "consultaPacientes consultaPacientes";
    grid-template-columns: 0.60fr 0.40fr;
    grid-template-rows: 0.2fr 0.6fr 0.4fr;
}

.container>div {
    border: 1px solid #888;
    display: flexbox;
    width: 100%;
    height: 100%;
}

.container {
    max-width: 100%;
}

.resumenCuenta {
    grid-area: resumenCuenta;
    display: grid;
    grid-gap: 5px;
    grid-template-columns: 1fr;
    grid-template-rows: 0.10fr 0.5fr 0.4fr;

}

.detalleExcedentes {
    grid-area: detalleExcedentes;
}

.excendetesCategoria {
    grid-area: excendetesCategoria;
}

.consultaPacientes {
    grid-area: consultaPacientes;
}

.text-left {
    text-align: left !important;
    align-items: start;
}


.box {
    background-color: #fdf9f9;
    color: #fff;
    border-radius: 5px;
    padding: 10px;
}

/* .d {
    grid-column: col 3 / span 3;
    grid-row: row 3;
    display: grid;
    grid-gap: 5px;
    grid-template-columns: 1fr 1fr;
} */


.box .box {
    background-color: #ccc;
    color: #6e6a6a;
}


.e {
    grid-column: 1 / 3;
    grid-row: 1;
}

.f {
    grid-column: 1/3;
    grid-row: 2;
}

.g {
    grid-column: 1 / 3;
    grid-row: 3;
}

.clearfix {
    overflow: auto;
    display: flow-root;
}
</style>

