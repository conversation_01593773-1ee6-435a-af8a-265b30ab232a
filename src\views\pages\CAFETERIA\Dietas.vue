<template>
    <div class="Dietas">
        <form @submit="handleSubmit">
            <DxForm :ref="formDietas" :form-data.sync="formulario">
                <DxFormGroupItem>
                    <DxFormGroupItem :col-count="2">
                        <DxFormItem
                        data-field="Funcion"
                        editor-type="dxRadioGroup"
                        :editor-options="{
                            items: funciones,
                            displayExpr: 'text',
                            valueExpr: 'id',
                            layout: 'horizontal',
                            onValueChanged: onFuncionChanged
                        }"
                        >
                        <DxFormLabel text="Tipo" />
                        </DxFormItem>
                        <DxFormGroupItem :col-count="4">
                            <DxFormButtonItem :button-options="buttonGrabar" name="Grabar" horizontal-alignment="center" verical-alignment="center" />
                         <!--    <DxFormButtonItem   v-if="funcionSeleccionada !== '2'" :button-options="buttonDPF" name="Grabar" horizontal-alignment="center" verical-alignment="center" />
                            <DxFormButtonItem  v-if="funcionSeleccionada !== '2'" :button-options="buttonExcel" name="Grabar" horizontal-alignment="center" verical-alignment="center" /> -->
                            <DxFormButtonItem v-if="funcionSeleccionada !== '2'" :button-options="buttonVisualizar" name="Grabar" horizontal-alignment="center" verical-alignment="center" />
                        </DxFormGroupItem>
                    </DxFormGroupItem>

                    <DxFormGroupItem>
                    </DxFormGroupItem>

                    <DxFormGroupItem :col-count="2">

                        <DxFormGroupItem :col-count="2">
                            <DxFormItem  editor-type="dxSelectBox" data-field="EmpresaReal" caption="Empresa" :editor-options="{
                                        width: 'auto',
                                        searchEnabled: true,
                                        items: empresas,
                                        displayExpr: 'Nombre',
                                        valueExpr: 'EmpresaReal',  // Asegúrate que coincida con la propiedad correcta
                                        
                                        value: sesion.sesion_empresa,
                                     onValueChanged: (e) => {
                                        AsignarSiguienteCheque(e);
                                    },
                        }" :validationRules="[{ type: 'required' }]" />

                           <DxFormItem 
                            editor-type="dxSelectBox" 
                            data-field="PrimerTiempo" 
                            :editor-options="{
                                width: 'auto',
                                searchEnabled: true,
                                items: tiempos,
                                displayExpr: 'Descripcion',
                                valueExpr: 'IdTiempo',
                                onValueChanged: (e) => {
                                    AsignarSiguienteTiempo(e);
                                },
                            }" 
                            :validationRules="[{ type: 'required' }]" 
                        />


                        </DxFormGroupItem>

                        <DxFormGroupItem :col-count="2">
                            <DxFormItem template="ViewUpdate" />
                            <DxFormItem template="Buttons" />
                        </DxFormGroupItem>


                    </DxFormGroupItem>

                </DxFormGroupItem>

                <template #Buttons="{}">
                    <div class="neumorphic-time-container">
                        <div class="neumorphic-time">
                            <span class="time-value-neo">{{ horaActual }}</span>
                        </div>
                    </div>
                </template>

                <template #ViewUpdate="{}">
                    <div v-if="ultimaActualizacion" style="font-size:large;">
                        Última actualización: {{ ultimaActualizacion }}
                    </div>
                </template>
            </DxForm>
        </form>
        <div style="margin-top: 20px;">
            <DxDataGrid :data-source="Dietas" v-if="formulario.Funcion === '1'" :show-borders="true" :allow-column-reordering="true" :column-auto-width="true" :on-row-prepared="onRowPrepared" :on-row-dbl-click="onRowDblClick" key-expr="id_Dieta" height="500">
                <DxDataGridColumn data-field="EstadoPreparacion" caption="EstadoPreparacion" />
                <DxDataGridColumn data-field="id_Dieta" :visible="false" />
                <DxDataGridColumn data-field="Admision" caption="Admision" />
                <DxDataGridColumn data-field="Habitacion" caption="Habitacion" />
                <DxDataGridColumn data-field="Ubicacion" caption="Ubicacion" />
                <DxDataGridColumn data-field="Dieta" caption="Dieta" />
                <DxDataGridColumn data-field="Tiempo_Comida" caption="Tiempo Comida" />
                <DxDataGridColumn data-field="FechaDieta" caption="Fecha Dieta" data-type="date" />
                <DxDataGridColumn data-field="Paciente" caption="Paciente" />
                <DxDataGridColumn data-field="Edad_Paciente" caption="Edad Paciente" />
                <DxDataGridColumn data-field="Nombre" caption="Nombre" />

                <DxDataGridSelection mode="single" />
<!--                 <DxDataGridDxSearchPanel :visible="true" :highlight-case-sensitive="true" />
 -->                <!-- <DxDataGridPaging :page-size="15" /> -->
                <DxDataGridScrolling mode="infinite" />
                <DxDataGridHeaderFilter :visible="true" />
            </DxDataGrid>

            <DxDataGrid :data-source="Dietas" v-else-if="formulario.Funcion === '2'" :show-borders="true" :allow-column-reordering="true" :column-auto-width="true" height="500">
                <DxDataGridColumn data-field="EstadoPreparacion" caption="EstadoPreparacion" />
                <DxDataGridColumn data-field="id_Dieta" :visible="false" />
                <DxDataGridColumn data-field="Admision" caption="Admision" />
                <DxDataGridColumn data-field="Habitacion" caption="Habitacion" />
                <DxDataGridColumn data-field="Ubicacion" caption="Ubicacion" />
                <DxDataGridColumn data-field="Dieta" caption="Dieta" />
                <DxDataGridColumn data-field="Tiempo_Comida" caption="Tiempo Comida" />
                <DxDataGridColumn data-field="FechaDieta" caption="Fecha Dieta" data-type="date" />
                <DxDataGridColumn data-field="Paciente" caption="Paciente" />
                <DxDataGridColumn data-field="Edad_Paciente" caption="Edad Paciente" />
                <DxDataGridColumn data-field="Nombre" caption="Nombre" />

                <DxDataGridSelection mode="single" />
<!--                 <DxDataGridDxSearchPanel :visible="true" :highlight-case-sensitive="true" />
 -->                <DxDataGridScrolling mode="infinite" />
                <DxDataGridHeaderFilter :visible="true" />
            </DxDataGrid>
        </div>
    </div>
</template>

<script>

const formDietas = 'formDietas'


export default {
    name: "DietasPaciente",
    data(){
        return {
           formDietas,
           empresas: [],
           tiempos:[],
           Dietas:[],
           selectedIdDieta: null,
         
           formulario:{
                Opcion: null,
                EmpresaReal: null,
                Funcion: "1",
                PrimerTiempo: null
           },
           horaActual: '',
           ultimaActualizacion: null,
           desdeVisualizar: false,

            funciones: [
                {
                    id: "1",
                    text: "Dietas Pacientes",
                },
                {
                    id: "2",
                    text: "Pacientes Pendientes de dieta",
                }
            ],
            
            funcionSeleccionada: null,
            buttonGrabar: {
                width: "auto",
                icon: "fas fa-eye",
                text: "Ver Dietas",
                type: "default",
                onClick: () => {
                this.ConsultaQuery();
                },
                useSubmitBehavior: false,
            },

            buttonDPF: {
                width: "auto",
                icon: "fas fa-file-pdf",
                text: "PDF",
                type: "danger",
                onClick: () => {
                this.GenerarReporte('application/pdf','PDF');
                },
                useSubmitBehavior: false,
            },
    
            buttonVisualizar: {
                width: "auto",
                icon: "fas fa-clipboard-list",
                text: "Ver todo",
                type: "Normal",
                onClick: () => {
                    this.desdeVisualizar = true; // activas la bandera
                    this.ConsultaQuery();
                    this.desdeVisualizar = false; // restableces para futuras llamadas
                },
                useSubmitBehavior: false,
            },


            
            buttonExcel: {
                width: "auto",
                icon: "fas fa-file-excel",
                text: "Excel",
                type: "success",
                onClick: () => {
                this.ConsultaQuery();
                },
                useSubmitBehavior: false,
            },

            
        }
    },
    computed: {
        currentDate() {
            return new Date().toISOString().substring(0, 10);
        },

        sesion() {
            return this.$store.state.sesion
        },

        global() {
            return this.$store.state.global
        },

    },

    methods:{

        handleSubmit(e) {
            e.preventDefault();
        },

        actualizarHora() {
            const ahora = new Date();
            this.horaActual = ahora.toLocaleTimeString();
        }, 

        onFuncionChanged(e) {
                this.funcionSeleccionada = e.value;
                this.AsignarSiguienteCheque(e)
                this.TiempoComida()
                this.ConsultaQuery()
        },


        CargarCuentas(asignar, empresaSeleccionada = null) {
            this.axios.post("/app/v1_ExpedienteEvolucion/ConsultaEmpresas", { Opcion: 0 })
                .then((resp) => {
                    this.empresas = resp.data.json;
                    
                    if (asignar) {
                        if (empresaSeleccionada && this.empresas.some(e => e.EmpresaReal === empresaSeleccionada)) {
                            // Usar la empresa proporcionada si existe en la lista
                            this.formulario.EmpresaReal = empresaSeleccionada;
                        } else {
                            // Lógica original si no se proporciona empresa o no existe
                            const empresaSesion = this.empresas.find(
                                emp => emp.EmpresaReal == this.sesion.sesion_empresa
                            );
                            
                            this.formulario.EmpresaReal = empresaSesion 
                                ? empresaSesion.EmpresaReal 
                                : (this.empresas.length > 0 ? this.empresas[0].EmpresaReal : null);
                        }
                    }
                });
        },
        AsignarSiguienteCheque(e) {
            this.formulario.EmpresaReal = e.value;
        },

        AsignarBanco(e) {
            this.formulario.EmpresaReal = e.Codigo
        },

        TiempoComida(asignar) {
            this.axios.post('/app/v1_ExpedienteEvolucion/ConsultaTiempos', {
                Opcion: 1,
            }).then((resp) => {
                const horaActual = new Date().toTimeString().substring(0, 8);
                const todosLosTiempos = resp.data.json;

                // Filtramos condicionalmente:
                // - Si es tiempo 1 o 3, solo lo mantenemos si la hora actual está en su rango.
                // - Los demás tiempos siempre se incluyen.
                this.tiempos = todosLosTiempos.filter(t => {
                    if (t.IdTiempo === 1 || t.IdTiempo === 3) {
                        const estaEnRango = (
                            horaActual >= t.HorarioInicia.substring(11, 19) &&
                            horaActual < t.HorarioFinaliza.substring(11, 19)
                        );
                        return estaEnRango; // Solo lo mantenemos si está en su horario
                    }
                    return true; // Incluir todos los demás tiempos
                });

                if (asignar) {
                    // Buscamos el tiempo válido (ya filtrado correctamente)
                    const tiempoValido = this.tiempos.find(x =>
                        horaActual >= x.HorarioInicia.substring(11, 19) &&
                        horaActual < x.HorarioFinaliza.substring(11, 19)
                    );

                    if (tiempoValido) {
                        this.AsignarTiempo(tiempoValido);
                    } else {
                        // Si no hay coincidencia, asignamos un valor por defecto (ej: "Otros" - IdTiempo 5)
                        const tiempoPorDefecto = todosLosTiempos.find(t => t.IdTiempo === 5);
                        if (tiempoPorDefecto) {
                            this.AsignarTiempo(tiempoPorDefecto);
                        }
                    }
                   
                }
            });
        },
        AsignarSiguienteTiempo(e) {
         let tiempo = this.tiempos.find((x) => x.IdTiempo == e.value);
         this.AsignarTiempo(tiempo);
        },

        AsignarTiempo(e) {
            this.formulario.PrimerTiempo = e.IdTiempo;
        },

        ConsultaQuery() {
            let opcion = null;

            if (this.formulario.Funcion === "1") {
                opcion = 2;
            } else if (this.formulario.Funcion === "2") {
                opcion = 3;
            }

            const tiempo = this.desdeVisualizar ? null : this.formulario.PrimerTiempo;

            this.axios.post('/app/v1_ExpedienteEvolucion/ConsultaDatosDietas', {
                Opcion: opcion,
                Tiempo: tiempo,
            }).then((resp) => {

                this.Dietas = resp.data.json;

                this.ultimaActualizacion = new Date().toLocaleTimeString();
                 
                this.UpdateAnterior();
            }).catch(() => {
                  this.$vs.notify({
                            title: 'Error',
                            text: 'Ups Ocurrio un problema  con la consulta',
                            color: 'danger',
                            icon: 'error'
                        });
            });
        },


        onRowPrepared(e) {
            if (e.rowType === 'data') {
                if (e.data.EstadoPreparacion === 'Pendiente') {
                    e.rowElement.style.backgroundColor = '#fadbd8'; // rojo claro
                } else if (e.data.EstadoPreparacion === 'Preparado') {
                    e.rowElement.style.backgroundColor = '#ccffcc'; // verde claro
                }
            }
        },

        onRowDblClick(e) {
                const fila = e.data;
                this.selectedDieta = fila;
                
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'primary',
                    title: 'Confirmar',
                    text: '¿Estás Seguro de Cambiar de estado?',
                    acceptText: 'Sí',
                    cancelText: 'No',
                    accept: () => {
                        this.UpdateData(); // Ejecuta la acción si el usuario acepta
                    },
                    cancel: () => {
                        // Opcional: Notificación al cancelar (puedes omitirlo si no es necesario)
                        this.$vs.notify({
                            title: 'Cancelado',
                            text: 'La acción fue cancelada.',
                            color: 'warning',
                            icon: 'warning'
                        });
                    }
                });
            },

            UpdateData() {
                if (!this.selectedDieta) {
                    return;
                }

                const today = new Date();
                const formattedDate = today.toISOString().split('T')[0];

                this.axios.post('/app/v1_ExpedienteEvolucion/UpdateStado', {
                    Opcion: 5,
                    Dieta: this.selectedDieta.id_Dieta,
                    FechaR: formattedDate
                }).then(() => {
                    // Usar Vuesax para la notificación de éxito
                    this.$vs.notify({
                        title: '¡Actualizado!',
                        text: 'La dieta fue marcada como lista.',
                        color: 'success',
                        icon: 'check_circle'
                    });
                    
                    // Limpiar la dieta seleccionada
                    this.selectedDieta = null; // o this.selectedDieta = {};
                    
                    this.ConsultaQuery();
                }).catch(() => {
                    // Usar Vuesax para la notificación de error
                    this.$vs.notify({
                        title: 'Error',
                        text: 'Ocurrió un problema al actualizar.',
                        color: 'danger',
                        icon: 'error'
                    });
                });
            },

            UpdateAnterior() {
                // Filtra normalmente excluyendo el PrimerTiempo
                let tiposFiltrados = this.tiempos
                    .filter(t => t.IdTiempo !== this.formulario.PrimerTiempo)
                    .map(t => t.IdTiempo);
                
                // Si el idTiempo que quieres incluir es 5, lo agregamos al array
                if (this.idTiempo === 5) {
                    tiposFiltrados.push(5); // Agrega el 5 al array de tipos filtrados
                }
                
                // Convierte el array a string separado por comas
                const tiposFiltradosStr = tiposFiltrados.join(',');
                
                this.axios.post('/app/v1_ExpedienteEvolucion/UpdateFechaNull', {
                    Opcion: 6,
                    Tiempos: tiposFiltradosStr
                })
            },


        async GenerarReporte(formatoInterno, formato) {
            let postData = {
                nombrereporte: 'DietasExpediente',
                tiporeporte: formatoInterno,
                Opcion: 7,
                Tipo: this.formulario.PrimerTiempo
            };

            this.$reporte_modal({
                Nombre: 'DietasExpediente',
                Opciones: {
                    ...postData
                },
                Formato: formato
            });
        },
    },

    beforeMount() {
        this.CargarCuentas(true);
        clearInterval(this.intervalo);
        this.intervalId = setInterval(() => {
                this.TiempoComida(true);
        }, 300000);  
    },

    beforeDestroy() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }
    },

    mounted() {
        this.actualizarHora();
        this.TiempoComida(true);
        this.intervalo = setInterval(this.actualizarHora, 1000);
    },
}

</script>

<style scoped>
/* Estilos opcionales */
p {
  font-size: 1.1rem;
  font-weight: bold;
}
</style>

<style>
.Dietas .dx-datagrid-headers .dx-datagrid-table .dx-header-row {
    background-color: #2980b9; /* Verde */
    color: white;
}


.btn-iniciar .a-icon {
    display: inline-block;
    padding: 5px;
    color: #333;
}

.btn-iniciar .vs-icon {
    font-size: 18px;
}

.dx-datagrid-rowsview .dx-selection {
  background-color: transparent !important;
}

.neumorphic-time-container {
    display: flex;
    justify-content: center;
    padding: 1rem;
}

.neumorphic-time {
    padding: 1rem 2rem;
    background: #aed6f1;
    border-radius: 15px;
}

.time-value-neo {
    font-size: 1.5rem;
    font-weight: 700;
    font-family: 'Montserrat', sans-serif;
    color: #4a4a4a;
    letter-spacing: 1px;
}

</style>