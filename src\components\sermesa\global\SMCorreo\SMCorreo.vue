<template>
<div id="sm-email-container">
    <ValidationObserver ref="mailValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
        <form @submit.prevent="handleSubmit($event, SendMail)">
            <div class="w-full">

                <ValidationProvider name="ToRule" rules="required" v-slot="{errors}">
                    <SMDestinatario v-model="EnviarA" placeholder="Enviar a"></SMDestinatario>
                    <span style="position:relative" class="span-text-validation" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                </ValidationProvider>

            </div>
            <div class="w-full">
                <SMDestinatario v-model="CopiarA" placeholder="Copia a"></SMDestinatario>
            </div>
            <div class="w-full">
                <SMDestinatario v-model="OcultoA" placeholder="Copia oculta a"></SMDestinatario>
            </div>
            <div class="flex flex-wrap">
                <div class="w-full p-2">
                    <ValidationProvider name="Asunto" rules="required|max:200" v-slot="{errors}">
                        <vs-input v-model="Asunto" label="Asunto" class="w-full" :danger="errors.length>0" :danger-text="(errors.length > 0) ? errors[0] : null" :disabled="!allowEditSbjct" />
                    </ValidationProvider>
                </div>

                <br>

                <div class="w-full p-2">
                    <vue-editor v-model="MensajeDescripcion" :placeholder="allowEditMsg? 'Redacte el cuerpo del correo':'Edición del mensaje no habilitada'" :editorToolbar="customToolbar" :disabled="!allowEditMsg"></vue-editor>
                    <hr>
                </div>

                <div class="flex items-center w-full" style="{justify-content:center}">
                    <vs-upload ref="refAdjunto" multiple :show-upload-button="false" :limit="!allowAddAttachment?-1:maxAttachment" @on-delete="onDeleteFile" :class="[allowDeleteAttachment?'':'disable-delete-attach', ]" text="Adjuntar archivos" :accept="attachTypes"/>
                </div>

                <vs-row vs-justify="center">
                    <vs-button :disabled="invalid" color="success" type="border" style="float:right" submit @click="handleSubmit((e)=>SendMail(e))" icon="send" icon-after>Enviar</vs-button>
                </vs-row>
            </div>
        </form>
    </ValidationObserver>
</div>
</template>

<script>
import {
    VueEditor
} from "vue2-editor"

const emailRegex = new RegExp(/^[A-Za-z0-9_!#$%&'*+/=?`{|}~^.-]+@[A-Za-z0-9.-]+$/, "gm")

export default {
    name: 'sm-email',
    props: {
        attach: {
            type: Array
        }, //array File Object
        to: {
            type: Array
        }, //lista de destinatarios
        cc: {
            type: Array
        }, //lista de destinatarios con copia
        bcc: {
            type: Array
        }, //lista de destinatarios con copia oculta
        msg: {
            type: String
        }, //mensaje 
        sbjct: {
            type: String
        },
        reporte: null, // ver estructura de opciones reporte view/pages/REPORT/REPORT001.vue
        allowAddAttachment: {
            default: true,
            type: Boolean
        },
        allowDeleteAttachment: {
            default: true,
            type: Boolean
        },
        allowEditMsg: {
            default: true,
            type: Boolean
        },
        allowEditSbjct: {
            default: true,
            type: Boolean
        },
        maxAttachment: {
            default:5,
            type: Number
        },
        /**string con la lista de tipos de archivo que acepta */
        attachTypes:{
            default:'ALL',//'.pdf, .xlsx'
            type: String
        }
    },
    data() {
        return {
            adjuntosmodel: null,
            EnviarA: [],
            CopiarA: [],
            OcultoA: [],
            Asunto: '',
            MensajeDescripcion: '',

            //configuración

            customToolbar: [
                [{
                    'header': [1, 2, 3, 4, 5, 6, false]
                }],
                [{
                    'font': []
                }],
                ['bold', 'italic', 'underline', 'strike'],
                [{
                    list: "ordered"
                }, {
                    list: "bullet"
                }],
                [{
                    'indent': '-1'
                }, {
                    'indent': '+1'
                }],
                [{
                    'color': []
                }, {
                    'background': []
                }],
            ],
            Regex: emailRegex,
        }
    },
    components: {
        VueEditor,
        SMDestinatario: () => import("./SMDestinatario.vue")
    },
    methods: {

        /**Eliminar del array los archivos adjunto ya que el componente filtra en base */
        onDeleteFile(e) {
            this.$emit('file-deleted', e)
        },

        SendMail() {
            let postData = null
            if (this.reporte) {
                postData = {
                    ...this.reporte
                }
                postData.Opciones.EnviarA = this.EnviarA.join(";")
                postData.Opciones.CopiarA = this.CopiarA.join(";")
                postData.Opciones.CopiarEnOculto = this.OcultoA.join(";")
                postData.Opciones.Asunto = this.Asunto
                postData.Opciones.MensajeDescripcion = this.MensajeDescripcion

                if (!postData.Opciones.NombreDelArchivo)
                    postData.Opciones.NombreDelArchivo = postData.Nombre + '.pdf'

                return this.axios.post("/app/reporte/EnvioReporte", postData)
                    .then((resp) => {
                        if (resp.data.codigo == 0)
                            this.$emit('success', postData)
                    })
            } else {
                //preperando adjuntos
                let postData = {
                    Opciones: { //TODO ver si se siplifica
                        EnviarA: this.EnviarA.join(";"),
                        CopiarA: this.CopiarA.join(";"),
                        CopiarEnOculto: this.OcultoA.join(";"),
                        Asunto: this.Asunto,
                        MensajeDescripcion: this.MensajeDescripcion,
                        Adjuntos: [],
                    }

                }

                const parsePromises = this.$refs.refAdjunto.filesx.map((file, index) => {
                    return new Promise((resolve, reject) => {
                        const reader = new FileReader();
                        reader.readAsDataURL(file);
                        reader.onload = () => {
                            if (!this.$refs.refAdjunto.itemRemove.includes(index))
                                postData.Opciones.Adjuntos.push({name:file.name, content: reader.result})
                            resolve(reader.result);
                        }
                        reader.onerror = error => reject(error);
                    });
                })
                //cuando todos los adjuntos se hayan cargado :)
                const attachcargados = Promise.allSettled(parsePromises)
                attachcargados.then(() => {
                    return this.axios.post("/app/reporte/EnvioReporteStream", postData).then((resp) => {
                        if (resp.data.codigo == 0) {
                            //y limpiamos todo
                            this.$emit('success', postData)
                        }
                    })
                })
            }
        },
        /**recibe un arreglo y valida el último elemento si cumple con el formato de correo electrónico */
        clearAttach() {
            this.$refs.refAdjunto.filesx.splice(0, this.$refs.refAdjunto.filesx.length)
            this.$refs.refAdjunto.srcs.splice(0, this.$refs.refAdjunto.srcs.length)
        },
    },
    mounted() {
        this.Asunto = this.sbjct
        this.MensajeDescripcion = this.msg
        this.EnviarA = this.to
        this.CopiarA = this.cc
        this.OcultoA = this.bcc
        this.clearAttach()
        if (Array.isArray(this.attach)) {
            this.attach.map(myfile => {
                this.$refs.refAdjunto.filesx.push(myfile)
                this.$refs.refAdjunto.srcs.push({
                    src: null,
                    name: myfile.name,
                    percent: null,
                    error: false,
                    remove: null
                })
            })
        }
    },
    watch: {
        'sbjct'(newval) {
            this.Asunto = newval
        },
        'msg'(newval) {
            this.MensajeDescripcion = newval
        },
        'to'(newVal) {
            this.EnviarA = newVal
        },
        'cc'(newVal) {
            this.CopiarA = newVal
        },
        'bcc'(newVal) {
            this.OcultoA = newVal
        },
        'attach'(newVal) {
            this.clearAttach()
            if (Array.isArray(newVal)) {
                newVal.map(myfile => {
                    this.$refs.refAdjunto.filesx.push(myfile)
                    this.$refs.refAdjunto.srcs.push({
                        src: null,
                        name: myfile.name,
                        percent: null,
                        error: false,
                        remove: null
                    })
                })
            }

        },
    }
}
</script>

<style>
#sm-email-container .con-img-upload .img-upload {
    width: 100px;
    height: 100px;
    /* z-index: 100; */
}

#sm-email-container .con-img-upload {
    display: flex;
    flex-wrap: wrap-reverse;
    justify-content: center;
}

#sm-email-container .con-img-upload .con-input-upload {
    width: 100%;
    height: 110px;
    /* position: relative; */
    /* z-index: 99; */
}

#sm-email-container span {
    font-size: 1rem;
}

#sm-email-container .con-chips {
    justify-content: flex-start;
}

#sm-email-container span:focus {
    color: #06c !important;
}

#sm-email-container .con-chips .con-vs-chip {
    margin: 5px !important;
}

#sm-email-container .con-chips .con-vs-chip {
    margin: 5px !important;
}

.view-upload {
    z-index: 999999 !important;
}

.disable-delete-attach .btn-x-file {
    display: none !important;
}
</style>
