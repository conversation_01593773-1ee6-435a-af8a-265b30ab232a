<template>
<div>
    <vx-card type="2" :title="`Preparación de Pagos.`">

        <ValidationObserver ref="formValidate" v-slot="{  handleSubmit }" mode="lazy">
            <form ref="formAnulacion" method="post" @submit.prevent="handleSubmit(anular_orden())">
                <div>
                    <!-- <vs-tabs :color="colorx">
                        <vs-tab label="Consolidad listado pagos" icon="account_balance"> -->
                    <!-- INICIO ICONOS                         -->
                    <div>
                        <vs-row>

                            <vs-row style="display: flex; width: 50%;">
                                <!-- type="gradient" -->
                                <vs-button color="danger" class="m-2" @click="PopupAgregarPago();">
                                    <font-awesome-icon icon="fas fa-file-excel" class="pr-2" style="font-size: 16px" />
                                    <span>Reiniciar / Generar</span>
                                </vs-button>
                                <vs-button color="success" class="m-2" @click="CargarListadoPagos('', '', '', '', '', '1');">
                                    <font-awesome-icon icon="fas fa-file-excel" class="pr-2" style="font-size: 16px" />
                                    <span>Actualizar</span>
                                </vs-button>

                            </vs-row>
                            <vs-row style="display: flex; width: 50%;" class="flex justify-end">
                                <vs-button v-if="ListaPagosGenerados.length>0" class="m-2" @click="GenerarReporte('EXCEL')">
                                    <font-awesome-icon icon="fas fa-file-excel" class="pr-2" style="font-size: 16px" />
                                    <span>Excel</span>
                                </vs-button>
                                <vs-button v-if="ListaPagosGenerados.length>0" class="  m-2" @click="GenerarReporte('PDF')">
                                    <font-awesome-icon icon="fas fa-file-pdf" class="pr-2" style="font-size: 16px" />
                                    <span>PDF</span>
                                </vs-button>

                            </vs-row>
                        </vs-row>

                        <div>
                            <vs-divider></vs-divider>
                            <vs-table2 :data="ListaPagosGenerados" search noDataText="Sin datos disponibles">

                                <template slot="thead">
                                    <th width="50px">Código</th>
                                    <th>Nombre</th>
                                    <th width="150px">Total</th>
                                    <th width="150px"></th>
                                </template>

                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td2 width="50px" :data="data[indextr].Codigo">
                                            {{data[indextr].Codigo}}
                                        </vs-td2>

                                        <vs-td2 :data="data[indextr].Nombre">
                                            {{data[indextr].Nombre}}
                                        </vs-td2>

                                        <vs-td2 v-if="data[indextr].Total <= 0" width="150px" :data="data[indextr].Total" align="right">
                                            {{parseFloat(0).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                        </vs-td2>
                                        <vs-td2 v-else width="150px" :data="data[indextr].Total" align="right">
                                            {{parseFloat(data[indextr].Total).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                        </vs-td2>

                                        <vs-td2 width="150px" align="right">
                                            <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" @click="VentanaDetallePagos = true;EditarPago(data[indextr].Codigo)"></vs-button>
                                            <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click.native="EliminarCargos(data[indextr])"></vs-button>
                                        </vs-td2>
                                    </tr>
                                </template>
                            </vs-table2>
                        </div>

                        <div align="right" style="font-size: 20px"> 
                            Total {{parseFloat(TotalPreparacionPagos).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                        </div>

                    </div>
                    <!-- FIN ICONOS -->

                    <!-- </vs-tab>
                    </vs-tabs> -->
                </div>

                <vs-divider position="left"></vs-divider>

            </form>
        </ValidationObserver>
    </vx-card>

    <!-- ************** DETALLE PAGOS********** -->
    <vs-popup class="popNuevo" title="Generar Pagos" :active.sync="VentanaDetallePagos" id="2">
        <div>
            <div class="flex flex-wrap">
                <div class="w-full md:w-full lg:w-1/4 xl:w-1/4  m-1">
                    <label class="vs-input--label">Documento:</label>
                    <vx-input-group class="">

                        <vs-input v-model="Nuevo.NumDocumento" v-if="Nuevo.Habilitado == true" @change="ValidarDocumento()" />
                        <vs-input v-model="Nuevo.NumDocumento" v-else disabled />
                        <template slot="append">
                            <div class="append-text btn-addon">
                                <button type="submit" v-show="false" name="button"></button>
                                <vs-button id="button-with-loading-1" color="primary" icon-pack="feather" @click="ValidarDocumento()" icon="icon-search" v-if="Nuevo.Habilitado==true"></vs-button>
                                <vs-button id="button-with-loading-2" color="danger" icon-pack="feather" @click="Nuevo.NumDocumento = '';Nuevo.Habilitado=true; Nuevo.Saldo = 0; Nuevo.Abono = 0;" icon="icon-x" v-else></vs-button>
                            </div>
                        </template>
                    </vx-input-group>
                </div>
                <div class="w-full md:w-full lg:w-1/4 xl:w-1/4  m-1">
                    <vs-input label="Saldo:" disabled class="w-full" v-model="Nuevo.Saldo" />
                </div>
                <div class="w-full md:w-full lg:w-1/4 xl:w-1/4  m-1">
                    <vs-input label="Abono:" :disabled="Nuevo.Habilitado" class="w-full" v-model="Nuevo.Abono" />
                </div>
                <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6  m-6">
                    <vs-button color="green" icon-pack="feather" icon="icon-plus" class="mr-5" @click="AgregarPago();">Agregar</vs-button>
                </div>

            </div>

            <br>
            <!--- DETALLE  ADMISION A DEVOLVER--->
            <div>
                <vs-divider></vs-divider>
                <vs-table2 max-items="10" pagination :data="ListaPagosDetalle" noDataText="Sin datos disponibles" search>

                    <template slot="thead">
                        <th>Documento</th>
                        <th width="150px" align="right">Saldo</th>
                        <th width="150px" align="right">Abono</th>
                        <th width="150px"></th>
                    </template>

                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2 :data="data[indextr].Documento">
                                {{data[indextr].Documento}}
                            </vs-td2>

                            <vs-td2 v-if="data[indextr].Saldo <= 0" width="150px" :data="data[indextr].Saldo" align="right">
                                {{parseFloat(0).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                            </vs-td2>
                            <vs-td2 v-else width="150px" :data="data[indextr].Saldo" align="right">
                                {{data[indextr].Saldo}}
                            </vs-td2>

                            <vs-td2 width="150px" :data="data[indextr].Monto" align="right">
                                <vs-input v-on:change="ActualizarAbono(data[indextr],indextr)" v-model="data[indextr].Monto" class="w-full" />
                            </vs-td2>

                            <vs-td2 width="150px" align="right">
                                <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click.native="EliminarDetallePago(data[indextr])"></vs-button>
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
        </div>

    </vs-popup>

    <!-- ************** GENERAR NUEVOS PAGOS ********** -->

    <vs-popup class="popNuevo" title="Generar Pagos" :active.sync="VentanaGenerarPagos" style="z-index:9998" id="1">

        <div>
            <div class="flex flex-wrap">

                <!-- Listado de proveedores -->
                <div class="w-full md:w-full lg:w-1/6 xl:w-1/6 m-1">
                    <!-- <SM-Buscar class="w-full" label="Proveedores" v-model="info.CodigoProveedor" api="app/v2_api_compras/PreparacionPagos" :api_campos="['Empresa','Codigo','Nombre','Nit']" :api_titulos="['Empresa','Codigo','Nombre','Nit']" :api_filtro="{'Proveedor':'','ProveedorInicio':'','ProveedorFin':'','FechaInicio':'','FechaFin':'','Opcion':'9'}" api_campo_respuesta="Codigo" :api_preload="true" :mostrar_busqueda="true" :callback_buscar="CargarProveedorSeleccionado" :callback_cancelar="QuitarSeleccionProveedor" /> -->
                    <SM-Buscar class="w-full" label="Proveedor (NIT)" v-model="info.NIT" api="app/v2_api_compras/PreparacionPagos" :api_campos="['Empresa','Codigo','Nombre','Nit']" :api_titulos="['Empresa','Codigo','Nombre','Nit']" :api_filtro="{'Proveedor':'','ProveedorInicio':'','ProveedorFin':'','FechaInicio':'','FechaFin':'','Opcion':'9'}" api_campo_respuesta="Nit" :api_preload="true" :mostrar_busqueda="true" :callback_buscar="CargarProveedorSeleccionado" :callback_cancelar="QuitarSeleccionProveedor" />
                    <small v-if="info.CodigoProveedor" class="text-primary">Código: {{info.CodigoProveedor}}</small>
                </div>

                <!-- Nombre proveedor seleccionado -->
                <div class="w-full md:w-full lg:w-1/3 xl:w-1/3 m-1">
                    <ValidationProvider>
                        <vs-input disabled label="Nombre del proveedor:" class="w-full" v-model="info.NombreProveedor" />
                    </ValidationProvider>
                </div>
                <!-- Rango de fecha vencimiento -->
                <div class="w-full md:w-full lg:w-1/6 xl:w-1/6  m-1">
                    <vs-input label="Vencimiento Del" class="w-full" type="date" v-model="info.FechaInicial" />
                </div>
                <div class="w-full md:w-full lg:w-1/6 xl:w-1/6  m-1">
                    <vs-input label="Al" class="w-full" type="date" v-model="info.FechaFinal" />
                </div>
            </div>

            <br>
            <div class="flex flex-wrap">

                <vs-button class="m-1" color="primary" style="float: right" type="filled" icon-pack="feather" icon="icon-save" @click="ValidacionGeneracionPago();">
                    Generar</vs-button>

                <vs-button class="m-1" color="danger" style="float: right" type="border" icon-pack="feather" icon="icon-x" @click="VentanaGenerarPagos=false;">
                    Cancelar</vs-button>

            </div>
        </div>

    </vs-popup>
</div>
</template>

<script>
import moment from "moment";
export default {
    data() {
        return {
            colorx: '#00C853',

            // Listas
            ListaPagosGenerados: [],
            ListaPagosDetalle: [],
            TotalPreparacionPagos: 0,

            VentanaGenerarPagos: false,
            VentanaDetallePagos: false,

            info: {
                CodigoProveedor: null,
                NombreProveedor: null,
                FechaInicial: null,
                FechaFinal: null,
                NIT: null
            },

            Nuevo: {
                Habilitado: true,
                NumDocumento: null,
                Saldo: null,
                Abono: null,
                Proveedor: null
            }
        }
    },
    mounted() {
        this.CargarListadoPagos('', '', '', '', '', '1');
    },
    methods: {
        PopupAgregarPago() {
            this.info.NombreProveedor = '';
            this.info.CodigoProveedor = '';
            this.info.NIT = '';
            this.VentanaGenerarPagos = true
            let b = new Date();
            this.info.FechaInicial = moment(b).format('YYYY-MM-DD');
            this.info.FechaFinal = moment(b).format('YYYY-MM-DD');
        },
        GenerarReporte(TipoDoc) {
            this.$reporte_modal({

                Nombre: "Preparación pagos",
                Formato: TipoDoc,
                AbrirTab: true,
                Opciones: {
                    nombrereporte: "PreparacionPagos",

                    json: "[{\"Etiqueta\":\"Nombre de Reporte\",\"Parametro\":\"nombrereporte\",\"value\":\"PreparacionPagos\"}]",
                    ConfigCorreo: null
                }

            })
        },
        ValidarDocumento() {
            this.axios.post('/app/v2_api_compras/PreparacionPagos', {
                    Proveedor: this.Nuevo.Proveedor,
                    DetDocumento: this.Nuevo.NumDocumento,
                    Opcion: 14
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.Nuevo.Habilitado = false;
                        if (resp.data.Saldo > 0) {
                            this.Nuevo.Saldo = resp.data.Saldo;
                            this.Nuevo.Abono = resp.data.Saldo;

                        } else {
                            this.Nuevo.Saldo = 0;
                            this.Nuevo.Abono = 0;
                        }
                    }
                })
                .catch()
        },
        Mensaje(Titutlo, Mensaje, Icono) {
            this.$vs.notify({
                time: 8000,
                title: Titutlo,
                text: Mensaje,
                iconPack: 'feather',
                icon: Icono,
                color: 'danger',
                position: 'bottom-center'
            })
        },
        AgregarPago() {
            if (parseFloat(this.Nuevo.Saldo) < parseFloat(this.Nuevo.Abono)) {
                this.Nuevo.Abono = this.Nuevo.Saldo;
                this.Mensaje('Compras', 'Error, no puede poner una cantidad mayor al saldo.', 'icon-alert-circle')
            } else {
                this.axios.post('/app/v2_api_compras/PreparacionPagos', {
                        Proveedor: this.Nuevo.Proveedor,
                        DetDocumento: this.Nuevo.NumDocumento,
                        Opcion: 12
                    })
                    .then(resp => {

                        if (resp.data.codigo == 0) {
                            var lstTipo = resp.data.json.map(m => {
                                return {
                                    ...m,
                                }
                            })
                            if ((lstTipo.length > 0) && (lstTipo[0].TIPO == 'E') && (parseFloat(this.Nuevo.Abono) < parseFloat(this.Nuevo.Saldo))) {
                                this.Mensaje('Compras', 'No es permitido pagar parcialmente Facturas Especiales.', 'icon-alert-circle')
                                this.Nuevo.Abono = this.Nuevo.Saldo;
                            } else {
                                /************CREAR NUEVO REGISTRO *********/
                                this.axios.post('/app/v2_api_compras/PreparacionPagos', {
                                        Proveedor: this.Nuevo.Proveedor,
                                        DetDocumento: this.Nuevo.NumDocumento,
                                        DetTipo: lstTipo[0].TIPO,
                                        DetMonto: this.Nuevo.Abono,
                                        DetSaldo: this.Nuevo.Saldo,
                                        Opcion: 15
                                    })
                                    .then(resp => {
                                        if (resp.data.codigo == 0) {
                                            this.Nuevo.Habilitado = true;
                                            this.Nuevo.NumDocumento = null;
                                            this.Nuevo.Saldo = null;
                                            this.Nuevo.Abono = null;
                                            this.CargarListadoPagos('', '', '', '', '', '1');
                                            this.EditarPago(this.Nuevo.Proveedor);
                                        }

                                    })
                                /************ FINALIZACIÓN CREACIÓN REGISTRO*/
                            }
                        }

                    })
                    .catch()
            }
        },
        ActualizarAbono(datos, tr) {
            let ValorSaldo = datos.Saldo.replace(/,/g, '');
            let ValorMonto = datos.Monto.replace(/,/g, '');
            if (parseFloat(ValorSaldo) < parseFloat(ValorMonto)) {
                this.ListaPagosDetalle[tr].Monto = this.ListaPagosDetalle[tr].Saldo;
                this.Mensaje('Compras', 'Error, no puede poner una cantidad mayor al saldo.', 'icon-alert-circle')
            } else {
                this.axios.post('/app/v2_api_compras/PreparacionPagos', {
                        Proveedor: datos.Proveedor,
                        DetDocumento: datos.Documento,
                        Opcion: 12
                    })
                    .then(resp => {

                        if (resp.data.codigo == 0) {
                            var lstTipo = resp.data.json.map(m => {
                                return {
                                    ...m,
                                }
                            })
                            if ((lstTipo.length > 0) && (lstTipo[0].TIPO == 'E') && (parseFloat(datos.Monto) < parseFloat(datos.Saldo))) {
                                this.Mensaje('Compras', 'No es permitido pagar parcialmente Facturas Especiales.', 'icon-alert-circle')
                                this.ListaPagosDetalle[tr].Monto = this.ListaPagosDetalle[tr].Saldo;
                            } else {

                                /************ACTUALIZACIÓN DE SALDOS *********/
                                this.axios.post('/app/v2_api_compras/PreparacionPagos', {
                                        Proveedor: datos.Proveedor,
                                        DetDocumento: datos.Documento,
                                        DetTipo: datos.Tipo,
                                        DetMonto: ValorMonto,
                                        DetStatus: datos.Status,
                                        DetSaldo: ValorSaldo,
                                        DetValidacion: datos.Validacion,
                                        DetFechaRegistro: datos.FechaRegistro,
                                        DetUsuario: datos.Usuario,
                                        DetCorporativo: datos.Corporativo,
                                        Opcion: 13
                                    })
                                    .then(resp => {
                                        if (resp.data.codigo == 0) {
                                            this.CargarListadoPagos('', '', '', '', '', '1');
                                        }

                                    })
                                /************ FINALIZACIÓN ACTUALIZACIÓN DE SALDOS */
                            }
                        }

                    })
                    .catch()
            }
        },
        EliminarDetallePago(datos) {
            this.$vs.dialog({
                type: 'confirm',
                color: 'dark',
                title: 'Confirmación',
                cancelText: 'Cancelar',
                acceptText: 'Aceptar',
                text: '¿Eliminar este registro?',
                accept: () => {
                    //Actualiza la nueva cantidad
                    this.axios.post('/app/v2_api_compras/PreparacionPagos', {
                            DetTipo: datos.Tipo,
                            Proveedor: datos.Proveedor,
                            DetDocumento: datos.Documento,
                            DetMonto: datos.Monto,
                            DetStatus: datos.Status,
                            DetUsuario: datos.Usuario,
                            DetFechaRegistro: datos.FechaRegistro,
                            DetSaldo: datos.Saldo,
                            DetOrdenAutorizacion : datos.OrdenAutorizada,
                            DetValidacion: datos.Validacion,
                            DetCorporativo: datos.Corporativo,
                            Opcion: 11
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                //this.EditarPago(datos.Proveedor);
                                this.CargarListadoPagos('', '', '', '', '', '1');
                                this.VentanaDetallePagos = false;
                            }
                        })
                        .catch()
                },
                style: {
                    zIndex: 10001
                }
            })
        },
        EditarPago(datos) {
            this.Nuevo.Habilitado = true;
            this.Nuevo.NumDocumento = null;
            this.Nuevo.Abono = null;
            this.Nuevo.Saldo = null;
            this.Nuevo.Proveedor = null;
            this.ListaPagosDetalle = [];
            this.Nuevo.Proveedor = datos;
            this.axios.post('/app/v2_api_compras/PreparacionPagos', {
                    Proveedor: datos,
                    ProveedorInicio: null,
                    ProveedorFin: null,
                    FechaInicio: null,
                    FechaFin: null,
                    Opcion: 10
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        resp.data.json.forEach(item => {
                            const Monto = parseFloat(item.Monto); // Convertir a número
                            item.Monto = Monto.toLocaleString('es-GT', {
                                style: "decimal",
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            });
                            const Saldo = parseFloat(item.Saldo); // Convertir a número
                            item.Saldo = Saldo.toLocaleString('es-GT', {
                                style: "decimal",
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            });
                        });
                        this.ListaPagosDetalle = resp.data.json;

                        
                    }

                })
                .catch()

        },
        ValidacionGeneracionPago() {
            //if( this.info.FechaFinal == null )
            if (this.info.CodigoProveedor !== null && this.info.CodigoProveedor !== undefined) {
                this.CargarListadoPagos(this.info.CodigoProveedor, '', '', this.info.FechaInicial, this.info.FechaFinal, '3')
            } else {
                if (this.ListaPagosGenerados.length > 0) {
                    this.$vs.dialog({
                        type: 'confirm',
                        color: 'dark',
                        title: 'Confirmación',
                        cancelText: 'Cancelar',
                        acceptText: 'Aceptar',
                        text: 'Los pagos mostrados en pantalla serán eliminados, ¿Desea continuar?',
                        accept: () => {
                            //Elimina Pagos de Pantallla
                            this.EliminarPagos().then((resp) => {
                                    if (resp)
                                        this.CargarListadoPagos(this.info.CodigoProveedor, '', '', this.info.FechaInicial, this.info.FechaFinal, '3')
                                }

                            )
                        },
                        style: {
                            zIndex: 9999
                        }
                    })
                } else {
                    this.CargarListadoPagos(this.info.CodigoProveedor, '', '', this.info.FechaInicial, this.info.FechaFinal, '3')
                }

            }

        },
        QuitarSeleccionProveedor() {
            this.info.NombreProveedor = "";
            this.info.NIT = ""
            this.info.CodigoProveedor = ""
        },
        //Eliminar selección de proveedor
        CargarProveedorSeleccionado(datos) {
            this.info.NombreProveedor = datos.Nombre;
            this.info.CodigoProveedor = datos.Codigo;
        },
        // Carga información de cargos
        CargarListadoPagos(PProveedor = '', PProveedorInicio = '', PProveedorFin = '', PFechaInicio = '', PFechaFin = '', Opcion = '') {
            this.axios.post('/app/v2_api_compras/PreparacionPagos', {
                    Proveedor: PProveedor,
                    ProveedorInicio: PProveedorInicio,
                    ProveedorFin: PProveedorFin,
                    FechaInicio: PFechaInicio,
                    FechaFin: PFechaFin,
                    Opcion: Opcion
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        if (Opcion == 1)
                            this.ListaPagosGenerados = resp.data.json;

                            this.TotalPreparacionPagos = this.ListaPagosGenerados.reduce((suma, item) => parseFloat(suma )+ parseFloat(item.Total), 0);
                        if (Opcion != 1)
                            this.CargarListadoPagos('', '', '', '', '', '1');

                        if (Opcion == 3)
                            this.VentanaGenerarPagos = false;
                    }

                })
                .catch()
            return;
        },
        //Eliminar pagos cargos
        EliminarPagos() {
            return new Promise((resolve, reject) => {
                this.axios.post('/app/v2_api_compras/PreparacionPagos', {
                        Proveedor: '',
                        ProveedorInicio: '',
                        ProveedorFin: '',
                        FechaInicio: '',
                        FechaFin: '',
                        Opcion: 4
                    })
                    .then(() => {
                        resolve(true)
                    })
                    .catch(() => {
                        reject(false)
                    })
            })
        },
        //Para eliminación de una Linea
        EliminarCargos(tr) {
            this.$vs.dialog({
                type: 'confirm',
                color: 'dark',
                title: 'Confirmación',
                cancelText: 'Cancelar',
                acceptText: 'Aceptar',
                text: '¿Eliminar este registro?',
                accept: () => {
                    //Actualiza la nueva cantidad
                    this.CargarListadoPagos(tr.Codigo, '', '', '', '', '8');
                }
            })

        },

    }
}
</script>
