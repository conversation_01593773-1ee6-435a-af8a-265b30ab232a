/************************************************************************************************************
 *******************************INICIO BORRADO DE TABLAS*****************************************************
 ************************************************************************************************************/

USE HOSPITAL
GO
IF OBJECT_ID('HisCategoriasTipos', 'U') IS NOT NULL 
	drop table HisCategoriasTipos

USE HOSPITAL
GO
IF OBJECT_ID('InvNivelPreciosOrtopedia', 'U') IS NOT NULL 
	drop table InvNivelPreciosOrtopedia

USE HOSPITAL
GO
IF OBJECT_ID('HisListaProductosOrtopedia', 'U') IS NOT NULL 
	drop table HisListaProductosOrtopedia

USE HOSPITAL
GO
IF OBJECT_ID('HisProformasOrtopedia', 'U') IS NOT NULL 
	drop table HisProformasOrtopedia

/************************************************************************************************************
 **********************************FIN BORRADO DE TABLAS*****************************************************
 ************************************************************************************************************/

/************************************************************************************************************
 *************************************CREACION DE TABLAS*****************************************************
 ************************************************************************************************************/

USE HOSPITAL
GO

CREATE TABLE HisCategoriasTipos
(
	IdCategoriasTipos int IDENTITY(1,1) not null primary key,
	Empresa char(3) not null default('MED'),
	Nombre varchar(30) not null,
  CodigoOrdenTipo varchar(10) not null,
	NombreClasificacion varchar(30) not null,
	Clasificacion int not null,
	Activo char(1) not null default('S'),
	Corporativo int NOT NULL,
	FechaRegistro smalldatetime NOT NULL default(getdate())
)
GO


USE HOSPITAL
GO

CREATE TABLE InvNivelPreciosOrtopedia
(
 	IdNivelPreciosOrtopedia int IDENTITY(1,1) not null primary key,	
	Empresa char(3) not null default('MED'),
	NivelPrecio int not null,
	Nombre varchar(50) not null,
	PorcentajeIncremento int not null,
	Activo char(1) not null default('S'),
	Corporativo int NOT NULL,
  CorporativoModificacion int NULL,
	FechaRegistro smalldatetime NOT NULL default(getdate()),	
  FechaModificacion smalldatetime NULL default(getdate())	
)
GO

USE HOSPITAL
GO

CREATE TABLE HisProformasOrtopedia
(
 	IdProforma int not null primary key,
	TipoOrden char(3) null,
  CodigoOrden int null,
	OrdenCompra int null,	
	NumeroProformaProveedor varchar(20) not null,	
	EmpresaAdmision char(3) not null default('MED'),
	Serie char(1) not null,
  NoAdmision int not null,
	NivelPrecios int not null,
	EmpresaProveedor char(3) not null default('MED'),
	CodigoProveedor char(12) not null,
	Hospital char(3) not null,
  Empresa char(3) not null default('MED'),
	Corporativo int not null,
  CorporativoBorrado int null,
  Estado char(1) not null default('A'),--A:Activo, E:ELIMINADO 
	FechaRegistro smalldatetime NOT NULL default(getdate()),
	FechaBorrado smalldatetime NULL,
	MontoTotal smallmoney NOT NULL
)
GO

USE HOSPITAL
GO

CREATE TABLE HisListaProductosOrtopedia
(
	IdListaProducto int IDENTITY(1,1) not null PRIMARY KEY,
	IdProforma int not null FOREIGN KEY REFERENCES HisProformasOrtopedia(IdProforma),
	EmpresaProducto char(3),
	CodigoProducto char(13),
	NombreProducto varchar(80),
	Cantidad int not null,
	PrecioUnitario smallmoney not null,
	Corporativo int,
	FechaRegistro smalldatetime	
)
GO
/************************************************************************************************************
 *************************************CREACION DE FUNCIONES**************************************************
 ************************************************************************************************************/

/************************************************************************************************************
 *************************************FnAplicaMontoMaximoPendiente*******************************************
 ************************************************************************************************************/

USE [HOSPITAL]
GO
/****** Object:  UserDefinedFunction [dbo].[FnAplicaMontoMaximoPendiente]    Script Date: 21/02/2023 12:07:03 p.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE FUNCTION [dbo].[FnAplicaMontoMaximoPendiente](@i_EmpresaUnificadora char(3), @i_Admision INT, @i_Serie char(1))
		returns bit 
as
begin
declare @w_TipoDescuento char(1)
declare @w_AplicaMontoMaximo bit
declare @w_MontoAdeudadoMaximo money

set @w_AplicaMontoMaximo = 0

	SELECT @w_TipoDescuento=TipoDescuento
		FROM admisiones
	 WHERE empresa = @i_EmpresaUnificadora
		 AND codigo = @i_Admision
		 AND serie = @i_Serie

	--Validacion para admisiones privadas
	IF @w_TipoDescuento <> 'S'
	BEGIN
		 Select @w_MontoAdeudadoMaximo = MontoAdeudadoMaximo -- ActualizarUltimaVenta
			 from Defaults
			where Empresa = @i_EmpresaUnificadora
		
		 IF @w_MontoAdeudadoMaximo > 0
		 BEGIN
			set @w_AplicaMontoMaximo = 1
		 END
	END

RETURN @w_AplicaMontoMaximo
END
GO

/************************************************************************************************************
 *************************************FnCalculaSiniestralidad************************************************
 ************************************************************************************************************/
USE [HOSPITAL]
GO
/****** Object:  UserDefinedFunction [dbo].[FnCalculaSiniestralidad]    Script Date: 21/02/2023 12:08:22 p.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE FUNCTION [dbo].[FnCalculaSiniestralidad](@i_EmpresaUnificadora varchar(3),@i_Admision int, @i_Serie Bandera) RETURNS BIT
AS
BEGIN
DECLARE	@w_siniestralidad BIT
Declare @w_seguro varchar(3)
Declare @w_TipoAdmision char(1)
DECLARE @w_planDirecto BIT
DECLARE @w_validarPlan char(1)

set @w_siniestralidad = 1
	
	Select @w_TipoAdmision = TipoDescuento, @w_seguro = Seguro
		From Admisiones
   Where Codigo = @i_Admision and Serie = @i_Serie
	
	if @w_TipoAdmision <> 'S'
	begin
		set @w_siniestralidad = 0
		return @w_siniestralidad
	end


	Select @w_planDirecto = ISNULL(s.PolizaPlanDirecto, 0), @w_validarPlan = ISNULL(x.ValidarAfiliacionAlAsignar, 'N')
	  from Seguros s (nolock) Inner Join Aseguradoras x (nolock)
	    ON (x.Empresa = s.Empresa and x.Asegura = s.Asegura)
	 Where S.Empresa = @i_EmpresaUnificadora
	   and S.Codigo = @w_seguro
	   and s.PolizaPlanDirecto = 1

	IF @w_planDirecto <> 1 OR @w_validarPlan <> 'S'
	begin
		set @w_siniestralidad = 0
	end

	RETURN @w_siniestralidad
END
GO
/************************************************************************************************************
 *************************************FIN CREACION DE FUNCIONES**********************************************
 ************************************************************************************************************/

/************************************************************************************************************
 ***********************************CREACION DE PAQUETES*****************************************************
 ************************************************************************************************************/

/************************************************************************************************************
 ***********************************spObtenerEmpresaReal*********************************************
 ************************************************************************************************************/
 USE [HOSPITAL]
GO

/****** Object:  StoredProcedure [dbo].[spObtenerEmpresaReal]    Script Date: 06/09/2023 15:26:25 p.m. ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [dbo].[spObtenerEmpresaReal]
( @IEmpresaUnificadora varchar(3),
	@IEmpresaBodega varchar(3),
  @IEmpresaReal varchar(3) = '',
	@ICodigoOrden varchar(3),
  @ISerieAdmision char(1),
  @ICodigoAdmision int,
	@OEmpresaReal varchar(3) out
) 
AS
BEGIN
	DECLARE @WTipoProducto varchar(20)='',
					@WBodegaOrdenTipo int,
					@ODescripcionError varchar(100),
					@w_ErrRaise		Varchar(MAX)	= '',
					@w_ErrRaiseNum 	INT			= 0;
	BEGIN TRY
		set @OEmpresaReal = ''

		SELECT @WTipoProducto = ISNULL(TipoProducto,''), @WBodegaOrdenTipo = ISNULL(BodegaDefault,0)
			FROM OrdenesTipos
		 WHERE Empresa = @IEmpresaUnificadora
       AND Codigo = @ICodigoOrden
		IF @WTipoProducto = ''
			BEGIN
				SET @ODescripcionError = 'Debe configurar el tipo de producto para el tipo de orden ' + @ICodigoOrden;
				THROW 50005, @ODescripcionError, 2
			END

		IF @WTipoProducto = 'PACIENTE'
			BEGIN

				SELECT @OEmpresaReal = ISNULL(H.EmpresaReal,'')
				  FROM Habitaciones H inner join Admisiones A
				    ON (H.Empresa = A.Empresa and H.Codigo = A.Habitacion)
				 WHERE A.Empresa = @IEmpresaUnificadora  and A.Serie = @ISerieAdmision  and A.Codigo = @ICodigoAdmision
				
				IF @OEmpresaReal != ''
					BEGIN
						RETURN
					END

				
				SELECT @OEmpresaReal = ISNULL(EmpresaReal,'') 
					FROM NominaDB..BasesXTipoAdmision 
				 WHERE Empresa = @IEmpresaUnificadora
					 AND CodAdm = @ISerieAdmision

				IF @OEmpresaReal = ''
					BEGIN
						SET @ODescripcionError = 'La habitación del paciente no tiene configurado el hospital al que pertenece ';
						THROW 50005, @ODescripcionError, 2
					END
			END
		ELSE IF @WTipoProducto = 'PRODUCTO' OR @WTipoProducto = 'PRODUCTO/SERVICIO'
			BEGIN
					IF @WBodegaOrdenTipo = 0 
						BEGIN
							SET @ODescripcionError = CONCAT('El tipo de orden ', @ICodigoOrden ,' no tiene configurada la bodega de despacho');
							THROW 50005, @ODescripcionError, 2
						END

					SELECT @OEmpresaReal =  ISNULL(EmpresaRealX,'')
						FROM INVENTARIO..BODEGAS
					 WHERE Empresa = @IEmpresaBodega
						 AND Codigo = @WBodegaOrdenTipo
						 AND EmpresaRealX not in ('ADM','SEM','MEH')

					IF @OEmpresaReal = ''
						BEGIN
							SET @ODescripcionError = CONCAT('La bodega  ',@WBodegaOrdenTipo,' no tiene configurada una empresa valida');
							THROW 50005, @ODescripcionError, 2
						END
			END
		ELSE IF @WTipoProducto = 'SERVICIO'
			BEGIN
				IF @IEmpresaReal = ''
				BEGIN
					SET @ODescripcionError = 'No se envio una empresa valida para la orden de tipo servicio';
					THROW 50005, @ODescripcionError, 2
				END
			END
		ELSE
			BEGIN
				SET @ODescripcionError = 'El tipo de producto ''' + RTRIM(@WTipoProducto) + ''' no se reconoce en el tipo de orden';
				THROW 50005, @ODescripcionError, 2
			END	
	END TRY
	BEGIN CATCH
		Set @w_ErrRaise = ERROR_MESSAGE()
		Set @w_ErrRaiseNum = ERROR_STATE()
		RAISERROR(@w_ErrRaise,16,@w_ErrRaiseNum)
	END CATCH;		  
END
GO

/************************************************************************************************************
 ******************************spInventarioIngresoProductoInterno********************************************
 ************************************************************************************************************/


USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[spInventarioIngresoProductoInterno]    Script Date: 21/02/2023 11:32:34 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROC [dbo].[spInventarioIngresoProductoInterno] 
        @i_operacion CHAR(2)=NULL,
        @i_accion    CHAR(1)=NULL,--ACCION A REALIZAR EN UNA TABLA, I INSERT, U UPDATE, S SELECT
        @i_Codigo_Hospital Empresa=NULL,
                @i_Empresa Empresa=NULL, --Empresa
                @i_EmpresaBodega Empresa=NULL,--Empresa Bodega
                @i_CodigoNuevo CHAR(25)=NULL, -- Código del producto.Código Hospital
                @i_Nombre CHAR(100)=NULL, -- Nombre Comercial del producto
                @i_Generico CHAR(100)=NULL, -- Nombre Generico del producto
                @i_Descripcion CHAR(100)=NULL, -- Descripción del producto
                @i_Categoria CHAR(20)=NULL, -- Categorias
                --@i_Costo DECIMAL(20,5)=NULL, -- El Costo (con iva.) del producto
                @i_Costo DECIMAL(18,5)=NULL, -- El Costo (con iva.) del producto
                @i_CodigoHospIFar CHAR(25)=NULL, -- Codigo Farmacia
                @i_CostoReferencia INT=NULL, -- Costo Competencia
                @i_ExistenciaMIN INT=NULL,--Existenicia Minima del Producto
                @i_ExistenciaMAX INT=NULL,--Existenicia Máxima del Producto
                @i_FamiliasCodigo CHAR(25)=NULL, --Catalogo de Familia - ID
                @i_Usuario CHAR(25)=NULL, --Usuario del Token
                @i_Trasladable CHAR(1)='N', --El producto es trasladable de Bodega a Farmacia.
                @i_ExcentoIva CHAR(1)='N',--El producto es excento de IVA.
                @i_Warning CHAR(1)='N', --El producto tiene Aviso de NO cobertura.
                @i_EsActivoFijo CHAR(1)='N',--i_EsActivoFijo
                @i_Consignacion BIT=NULL,--Utilizable en Hospital y/o Consulta Externa...
                @i_TipodeUso  CHAR(20)=NULL, --Utilizable en Hospital y/o Consulta Externa.
                @i_Departamento CHAR(10)=NULL,--La Sub-categoría del producto.
                @i_SubDepartamento CHAR(10)=NULL,--La Sub-SubCategoria del producto.
                @i_Marca CHAR(20)=NULL, --Marca (Laboratorio) del producto. los suministros no tienen laboratorio.
                @i_Proveedor CHAR(20)=NULL, --El Proveedor del producto
                @i_Clase CHAR(20)=NULL, --Clase del producto... (MEDICAMENTOS, SUMINISTROS, SERVICIOS).
                @i_SubClase CHAR(20)=NULL, --SubClase del producto... (CAD, NOCAD, EXTRAORD.)
                @i_unidadesmedida CHAR(25)='UNIDAD', --Las leyendas de las unidades de medida
                @i_TipoBodega        CHAR(1)=NULL, ---Tipo Bodega
                @i_Ensamblado CHAR(1)=NULL, --Tipo Sede Cocina        
                @i_IdGrupo CHAR(10)=NULL, -- Codigo de Grupo Para creación del Servicio
                @i_estructuraIngreso VARCHAR(1200) = '',
                @i_UnidadCompra CHAR(10)=NULL,
                -----GENERAL
                @i_IdSubGrupo CHAR(10)=NULL,
                @i_IdGenerico CHAR(10)=NULL,
                -----------DERCAS 05/09/2020------------------
                @i_VentaHospital CHAR(25)=NULL,
                @i_UnidadCompraCar CHAR(25)=NULL,
                @i_RegistroSanitario CHAR(25)=NULL,
                --@i_Contraindicaciones CHAR(10)=NULL,
                --@i_atc CHAR(10)=NULL,
                @i_CadenaFrio CHAR(1)='N',
                @i_LuzDirecta CHAR(1)='N',
                @i_Controlado CHAR(1)='N',
                @i_Stock CHAR(1)='N',
                @i_BajoPedido CHAR(1)='N',
                @i_Sasi CHAR(1)='N',
                @i_CatalogoGeneral CHAR(1)='N',
                @i_CompraLocal CHAR(1)='N',
                @i_Importado CHAR(1)='N',
								@w_Message    Varchar(100) = '' OUT,
								@w_Resultado  SmallInt OUT,
								@w_ErrTipo    INT =  1 OUT
AS
BEGIN
DECLARE 
        @w_ErrRaise        Varchar(99)        = '',
        @w_ErrRaiseNum INT                = 0,
        @w_IdMyProc        varchar(50) = OBJECT_NAME(@@PROCID),
        -----------------------------------------------------------------------
        @w_ClaseProducto CHAR(10)='P',
        @w_ClaseServicio CHAR(10)='S',
    @w_consBodegasVentaDirectaCOEX CHAR(20) ='3,21,22,23,24,25',
    @w_tmpCodigoInvalido INT=0,
    @w_flagGenerarEscalasdePrecios CHAR(2)='',
    @w_Impuesto DECIMAL(18,5)=0,
    @w_ValorCosto DECIMAL(18,5)=0,--ValorCosto
    @w_ValorCostoExterno DECIMAL(18,5)=0, --ValorCostoExterno
    @w_PresentacionId INT=-1,
    @w_VentaExterna CHAR(2)='',
    @w_xEncontrado INT = NULL,
    @w_CategoriasConNivelPrecioSAME VARCHAR(2)='82';--aCadena.;
-- =============================================
-- Author:                Hánderson Siana
-- Create date: 20.05.2020
-- Description:        Mantenimiento de Producto
-- =============================================
BEGIN TRAN
BEGIN TRY

--Variables Principales  Session en MID--------------------

        SELECT
                @w_Impuesto =(1+ (InvFactorImpuestoCompra/NULLIF(100,0)))
        FROM
                CONTADB.dbo.EmpresasDefaults
        WHERE
                --Empresa =@i_Empresa;
                  Empresa =@i_EmpresaBodega;


-----------------------------------------------------------
        
IF @i_operacion = 'A'-- Operaciones de Producto
BEGIN

        IF @i_accion = 'I'                        --  PRODUCTO INSERT
        BEGIN  
----------------------------------------Validación Código----------------------------------------------
        --SET @w_ValorCosto=@i_Costo;
    --SET @w_ValorCosto =((100*(@i_Costo/NULLIF(@w_Impuesto,0)))/NULLIF(100,0));
    SET @w_ValorCostoExterno = ((100*@i_CostoReferencia)/NULLIF(100,0));
    
----------------------------------------------------------------------------------
--Obtener Unidad de Medida.

                SELECT
                        @w_PresentacionId=IdMedida
                FROM
                        INVENTARIO.dbo.NTUnidadesMedida
                WHERE
                        Unidad = @i_unidadesmedida;
                
----------------------------------------------------------------------------------
           --IF(@i_SubClase=NULL AND @i_SubClase='')
        --BEGIN
        --Validacion del produto esta Correcto en su estructura y creacion.
                SELECT
                        @w_tmpCodigoInvalido = Count(*)
                FROM
                        Inventario.dbo.ProductosSubClase p
                INNER JOIN Inventario.dbo.ProductosClase c ON
                        (c.Empresa = p.Empresa
                        AND c.Codigo = p.Clase
                        AND c.ClasIFicacion = @w_ClaseProducto)
                WHERE
                        p.Empresa = @i_EmpresaBodega
                        AND p.Clase = SUBSTRING(@i_CodigoNuevo, 1, 1)
                        AND p.Codigo = SUBSTRING(@i_CodigoNuevo, 2, 1)
                        AND p.Ancho = (LEN(@i_CodigoNuevo) - 2);
                
                --SET @w_Resultado= 0
                --SET @w_ErrTipo        = 0        --codigo de error por procedimiento
                --SET @w_Message        = 'No hay datos procede-Ingresar el Producto'
                --SELECT         @w_Resultado as codigo, @w_Message as error, @w_ErrTipo as tipo_error
        
        --END
----------------------------------------------------------------------------------
                
        SELECT
                @w_flagGenerarEscalasdePrecios =GenerarEscalasPrecios,
                @w_VentaExterna=VentaExterna
        FROM
                Inventario.dbo.ProductosSubClase p
        WHERE
                --p.Empresa = @i_Empresa
                p.Empresa = @i_EmpresaBodega
                AND p.Clase = SUBSTRING(@i_CodigoNuevo, 1, 1)
                AND p.Codigo = SUBSTRING(@i_CodigoNuevo, 2, 1);
        
        
    --Si el codigo es valido entonces verIFicamos si ya existe o no.
    --NOT , Valores Invertidos true=falso , falso=true, bit 0 a 1 , 1 a 0.
        IF(@w_tmpCodigoInvalido<>0)
        BEGIN
                
                IF NOT EXISTS(SELECT
                                                Nombre
                                        FROM
                                                Inventario.dbo.Productos
                                        WHERE
                                                Empresa = @i_EmpresaBodega
                                                AND Codigo = @i_CodigoNuevo)
                BEGIN
                        SET @w_Resultado= 0
                        SET @w_ErrTipo        = 0        --codigo de error por procedimiento
                        SET @w_Message        = 'No hay datos procede-Ingresar el Producto'
                        
                        SET @i_SubDepartamento=(CASE WHEN @i_SubDepartamento='' THEN NULL ELSE @i_SubDepartamento END);
                        SET @i_Ensamblado=(CASE WHEN @i_TipoBodega='C' THEN @i_Ensamblado ELSE 'N' END);
                        
                        --INSERT INTO Inventario.dbo.Productos(Empresa, Codigo, Nombre, NombreGenerico, Categoria, CostoUltimo, CostoPromedio, CostoAlto, Departamento, SubDepartamento, CodigoHospIFar, Clase, SubClase, Trasladable, Usuario, Descripcion,Tipo,Proveedor, Marca, Warning, TipodeUso, Presentacion, CostoExterno, Minimo, Maximo, EsActivoFijo, ExcentoIva, Familia,Consignacion,TipoBodega,Ensamblado,Grupo,SubGrupo,Generico,finalizado,Activo)
                        --VALUES(@i_EmpresaBodega,@i_CodigoNuevo,@i_Nombre,@i_Generico,@i_Categoria,CONVERT(DECIMAL(18,4),@i_Costo),CONVERT(DECIMAL(18,4),@i_Costo),CONVERT(DECIMAL(18,4),@i_Costo),@i_Departamento,@i_SubDepartamento,@i_CodigoHospIFar,@i_Clase,@i_SubClase,@i_Trasladable,@i_Usuario,@i_Descripcion,'P',@i_Proveedor,@i_Marca,@i_Warning,@i_TipodeUso,@w_PresentacionId,@w_ValorCostoExterno,@i_ExistenciaMIN,@i_ExistenciaMAX,@i_EsActivoFijo,@i_ExcentoIva,@i_FamiliasCodigo,@i_Consignacion,@i_TipoBodega,@i_Ensamblado,@i_IdGrupo,@i_IdSubGrupo,@i_IdGenerico,0,'S')
                        
                        INSERT INTO Inventario.dbo.Productos(Empresa, Codigo, Nombre, NombreGenerico, Categoria, CostoUltimo, CostoPromedio, CostoAlto, Departamento, SubDepartamento, CodigoHospIFar, Clase, SubClase, Trasladable, Usuario, Descripcion,Tipo,Proveedor, Marca, Warning, TipodeUso, Presentacion, CostoExterno, Minimo, Maximo, EsActivoFijo, ExcentoIva, Familia,Consignacion,TipoBodega,Ensamblado,Grupo,SubGrupo,Generico,finalizado,Activo)
                        VALUES(@i_EmpresaBodega,@i_CodigoNuevo,@i_Nombre,@i_Generico,@i_Categoria,@i_Costo,@i_Costo,@i_Costo,@i_Departamento,@i_SubDepartamento,@i_CodigoHospIFar,@i_Clase,@i_SubClase,@i_Trasladable,@i_Usuario,@i_Descripcion,'P',@i_Proveedor,@i_Marca,@i_Warning,@i_TipodeUso,@w_PresentacionId,@w_ValorCostoExterno,@i_ExistenciaMIN,@i_ExistenciaMAX,@i_EsActivoFijo,@i_ExcentoIva,@i_FamiliasCodigo,@i_Consignacion,@i_TipoBodega,@i_Ensamblado,@i_IdGrupo,@i_IdSubGrupo,@i_IdGenerico,0,'S')
                          
                        --BOD
                        INSERT INTO INVENTARIO.dbo.ProductoCaracteristicas
                        (Empresa, Codigo, VentaHospital, UnidadCompra, RegistroSanitario,CadenaFrio, LuzDirecta, Controlado, Stock, BajoPedido, Sasi, CatalogoGeneral, CompraLocal, Importado, Usuario)
                        VALUES(@i_EmpresaBodega,@i_CodigoNuevo,@i_VentaHospital,@i_UnidadCompra,@i_RegistroSanitario,@i_CadenaFrio,@i_LuzDirecta,@i_Controlado,@i_Stock,@i_BajoPedido,@i_Sasi,@i_CatalogoGeneral,@i_CompraLocal,@i_Importado,@i_Usuario);
                        
                        --MED
                        INSERT INTO INVENTARIO.dbo.ProductoCaracteristicas
                        (Empresa, Codigo, VentaHospital, UnidadCompra, RegistroSanitario,CadenaFrio, LuzDirecta, Controlado, Stock, BajoPedido, Sasi, CatalogoGeneral, CompraLocal, Importado, Usuario)
                        VALUES(@i_Empresa,@i_CodigoNuevo,@i_VentaHospital,@i_UnidadCompra,@i_RegistroSanitario,@i_CadenaFrio,@i_LuzDirecta,@i_Controlado,@i_Stock,@i_BajoPedido,@i_Sasi,@i_CatalogoGeneral,@i_CompraLocal,@i_Importado,@i_Usuario);
                        

                        IF(@i_SubClase<>'0' OR @i_SubClase<>'')
                        BEGIN
                                
                                UPDATE Inventario.dbo.ProductosSubClase 
                                SET Correlativo = Correlativo+1
                                WHERE
                                        Empresa = @i_EmpresaBodega
                                        AND Clase = SUBSTRING(@i_CodigoNuevo, 1, 1)
                                        AND Codigo = SUBSTRING(@i_CodigoNuevo, 2, 1);
                                
                        END
                        
            --Inserción de las existencias iniciales (en cero) del Producto en las bodegas actualizables,
            --unicamente en las bodegas de la empresa que llevan control de existencias...
                        IF(@w_VentaExterna='S')
                        BEGIN
                        --Inserción de las existencias iniciales (en cero) del Producto en las bodegas actualizables,
            --unicamente en las bodegas de la empresa que llevan control de existencias...
                                        INSERT
                                                INTO
                                                INVENTARIO.DBO.ExistenciasBodega (Empresa,
                                                Producto,
                                                Bodega,
                                                NumeroSerie,
                                                Localizacion,
                                                Existencia)
                                        SELECT
                                                @i_EmpresaBodega,
                                                @i_CodigoNuevo,
                                                b.Codigo,
                                                '',
                                                'Pendiente',
                                                0
                                        FROM
                                                INVENTARIO.DBO.Bodegas b
                                        LEFT JOIN (
                                                select
                                                a.item AS Codigo,
                                                a.keyCount AS keyCount
                                        from
                                                HOSPITAL.dbo.SplitStringIndex(@w_consBodegasVentaDirectaCOEX,',') a
                                        ) AS A        
                                        ON b.Codigo=A.Codigo
                                        WHERE
                                                b.Empresa = @i_EmpresaBodega
                                                AND b.Activa = 'S'
                                                AND b.CreaExistenciaInicial = 1 
                                                --AND b.Codigo IN (@w_consBodegasVentaDirectaCOEX)--Agregado
                                                AND NOT EXISTS(
                                                SELECT
                                                        x.Producto
                                                FROM
                                                        INVENTARIO.DBO.ExistenciasBodega x
                                                WHERE
                                                        x.Empresa = @i_EmpresaBodega
                                                        AND x.Producto = @i_CodigoNuevo
                                                        AND x.Bodega = b.Codigo );
                                                
                        --Inserción de maximos y minimos del Producto en las bodegas actualizables,
            --unicamente en las bodegas de la empresa que llevan control de existencias...
                                        Insert
                                                Into
                                                INVENTARIO.DBO.MaxMinBodega(Empresa,
                                                Producto,
                                                Bodega,
                                                Minimos,
                                                Maximos)
                                        SELECT
                                                @i_Empresa,
                                                @i_CodigoNuevo,
                                                b.Codigo,
                                                @i_ExistenciaMIN,
                                                @i_ExistenciaMAX
                                        FROM
                                                INVENTARIO.DBO.Bodegas b
                                        LEFT JOIN (
                                                select
                                                a.item AS Codigo,
                                                a.keyCount AS keyCount
                                        from
                                                HOSPITAL.dbo.SplitStringIndex(@w_consBodegasVentaDirectaCOEX,',') a
                                        ) AS A        
                                        ON b.Codigo=A.Codigo
                                        WHERE
                                                b.Empresa = @i_EmpresaBodega
                                                AND b.Activa = 'S'
                                                AND b.ControlMaximoMinimo = 1
                                                --AND b.Codigo IN (@w_consBodegasVentaDirectaCOEX) --Agregado
                                                --AND b.Codigo<606 --SE QUITA CUANDO SE PASE A 101 O QA ,CAMPOR BODEGA HAY QUE CAMBIARLO A INT.
                                                AND NOT EXISTS(
                                                SELECT
                                                        x.Producto
                                                FROM
                                                        INVENTARIO.DBO.MaxMinBodega x
                                                WHERE
                                                        x.Empresa = @i_EmpresaBodega
                                                        AND x.Producto = @i_CodigoNuevo
                                                        AND x.Bodega = b.Codigo );
                                                
                        END
                        ELSE
                        BEGIN
                        --Inserción de las existencias iniciales (en cero) del Producto en las bodegas actualizables,
            --unicamente en las bodegas de la empresa que llevan control de existencias...
                                        INSERT
                                                INTO
                                                INVENTARIO.DBO.ExistenciasBodega (Empresa,
                                                Producto,
                                                Bodega,
                                                NumeroSerie,
                                                Localizacion,
                                                Existencia)
                                        SELECT
                                                @i_EmpresaBodega,
                                                @i_CodigoNuevo,
                                                Codigo,
                                                '',
                                                'Pendiente',
                                                0
                                        FROM
                                                INVENTARIO.DBO.Bodegas b
                                        WHERE
                                                b.Empresa = @i_EmpresaBodega
                                                AND b.Activa = 'S'
                                                AND b.CreaExistenciaInicial = 1
                                                AND NOT EXISTS(
                                                SELECT
                                                        x.Producto
                                                FROM
                                                        INVENTARIO.DBO.ExistenciasBodega x
                                                WHERE
                                                        x.Empresa = @i_EmpresaBodega
                                                        AND x.Producto = @i_CodigoNuevo
                                                        AND x.Bodega = b.Codigo );
                                                
                                        --Inserción de maximos y minimos del Producto en las bodegas actualizables,
                            --unicamente en las bodegas de la empresa que llevan control de existencias...
                                        INSERT 
                                                INTO
                                                INVENTARIO.DBO.MaxMinBodega(Empresa,
                                                Producto,
                                                Bodega,
                                                Minimos,
                                                Maximos)
                                        SELECT
                                                @i_EmpresaBodega,
                                                @i_CodigoNuevo,
                                                Codigo,
                                                @i_ExistenciaMIN,
                                                @i_ExistenciaMAX
                                        FROM
                                                INVENTARIO.DBO.Bodegas b
                                        WHERE
                                                b.Empresa = @i_EmpresaBodega
                                                AND b.Activa = 'S'
                                                AND b.ControlMaximoMinimo = 1
                                                --AND b.Codigo<606 --SE QUITA CUANDO SE PASE A 101 O QA ,CAMPOR BODEGA HAY QUE CAMBIARLO A INT.
                                                AND NOT EXISTS(
                                                SELECT
                                                        x.Producto
                                                FROM
                                                        INVENTARIO.DBO.MaxMinBodega x
                                                WHERE
                                                        x.Empresa = @i_EmpresaBodega
                                                        AND x.Producto = @i_CodigoNuevo
                                                        AND x.Bodega = b.Codigo );
                                
                        END
                        
                        
                ----------------------------------------------------------ESCALA DE PRECIOS------------------------------------------------------------
                        IF(@i_EmpresaBodega='BOD'  OR @i_EmpresaBodega='DRO'   AND @w_flagGenerarEscalasdePrecios='S' OR @w_flagGenerarEscalasdePrecios='' AND @i_SubClase<>'0' AND @i_SubClase<>'' )
                        BEGIN
                                
                                SET @i_SubDepartamento=(CASE WHEN @i_SubDepartamento='' THEN NULL ELSE @i_SubDepartamento END);
                                SET @i_Ensamblado=(CASE WHEN @i_TipoBodega='C' THEN @i_Ensamblado ELSE 'N' END);        
                                
                                --Insert Into Inventario.dbo.Productos(Empresa, Codigo, Nombre, NombreGenerico, Categoria, CostoUltimo, CostoPromedio, CostoAlto, Departamento, SubDepartamento, CodigoHospIFar, Clase, SubClase, Trasladable, Usuario, Descripcion,Tipo, Proveedor, Marca, Warning, TipodeUso, Presentacion, CostoExterno, Minimo, Maximo, EsActivoFijo, ExcentoIva, Familia,Consignacion,TipoBodega,Ensamblado,finalizado,Activo)
                                --Values(@i_Empresa,@i_CodigoNuevo,@i_Nombre,@i_Generico,@i_Categoria,@i_Costo,@i_Costo,@i_Costo,@i_Departamento,@i_SubDepartamento,@i_CodigoHospIFar,@i_Clase,@i_SubClase,@i_Trasladable,@i_Usuario,@i_Descripcion,'P',@i_Proveedor,@i_Marca,@i_Warning,@i_TipodeUso,@w_PresentacionId,@w_ValorCostoExterno,@i_ExistenciaMIN,@i_ExistenciaMAX,@i_EsActivoFijo,@i_ExcentoIva,@i_FamiliasCodigo,@i_Consignacion,@i_TipoBodega,@i_Ensamblado,0,'S')
                                INSERT INTO Inventario.dbo.Productos(Empresa, Codigo, Nombre, NombreGenerico, Categoria, CostoUltimo, CostoPromedio, CostoAlto, Departamento, SubDepartamento, CodigoHospIFar, Clase, SubClase, Trasladable, Usuario, Descripcion,Tipo,Proveedor, Marca, Warning, TipodeUso, Presentacion, CostoExterno, Minimo, Maximo, EsActivoFijo, ExcentoIva, Familia,Consignacion,TipoBodega,Ensamblado,Grupo,SubGrupo,Generico,finalizado,Activo)
                                VALUES(@i_Empresa,@i_CodigoNuevo,@i_Nombre,@i_Generico,@i_Categoria,@i_Costo,@i_Costo,@i_Costo,@i_Departamento,@i_SubDepartamento,@i_CodigoHospIFar,@i_Clase,@i_SubClase,@i_Trasladable,@i_Usuario,@i_Descripcion,'P',@i_Proveedor,@i_Marca,@i_Warning,@i_TipodeUso,@w_PresentacionId,@w_ValorCostoExterno,@i_ExistenciaMIN,@i_ExistenciaMAX,@i_EsActivoFijo,@i_ExcentoIva,@i_FamiliasCodigo,@i_Consignacion,@i_TipoBodega,@i_Ensamblado,@i_IdGrupo,@i_IdSubGrupo,@i_IdGenerico,0,'S')
                           
                        END
                        ----------------------------------------------------------ESCALA DE PRECIOS------------------------------------------------------------
                        

                 INSERT INTO INVENTARIO.DBO.BitacoraModificacionCatalogo(Empresa,Codigo,Tabla,ValorGuardado,Usuario,Operacion,CodigoVarchar)
                   VALUES(@i_Empresa,CONVERT(VARCHAR,@i_CodigoNuevo),'Producto',@i_estructuraIngreso,@i_usuario,'I',0); 
                  

                SET @w_Resultado= 0
                SET @w_ErrTipo        = 0        --codigo de error por procedimiento
                SET @w_Message        = 'Producto ingresado exitosamente.'

           IF @w_ErrTipo != 0
                BEGIN
                        ROLLBACK TRAN
                        SELECT         @w_Resultado as codigo, @w_Message as error, @w_ErrTipo as tipo_error
                End
                ELSE
                BEGIN
                        COMMIT TRAN
                        SELECT         @w_Resultado as codigo, @w_Message as descripcion, @w_ErrTipo as tipo_error
                End--ELSE ErrTipo
                
                                
                END
                ELSE
                BEGIN
                        SET @w_Resultado= -2
                        SET @w_ErrTipo        = -2        --codigo de error por procedimiento
                        SET @w_Message        = 'Ya existe el Producto.El Producto tiene este código ' + @i_CodigoNuevo + ' .'
                        --COMMIT TRAN
                        ROLLBACK TRAN
                        SELECT         @w_Resultado as codigo, @w_Message as error, @w_ErrTipo as tipo_error
                END -- FIN-IF-EXISTE
        
        END
        ELSE
        BEGIN
                SET @w_Resultado        = -1
                SET @w_ErrTipo        = -1        --codigo de error por procedimiento
                SET @w_Message        = 'El código ingresado no es válido, Vérifique por favor...'
                ---COMMIT TRAN
                ROLLBACK TRAN
                SELECT         @w_Resultado as codigo, @w_Message as error, @w_ErrTipo as tipo_error
        END -- FIN - VALIDAR -CODIGO

----------------------------------------Validación Código----------------------------------------------
        
         END--FIN IF DE 'I' INSERT
------------------------------------------------------------------------------------------------------------
                
        
                                
END -- FIN IF OPERACION 'A'



IF @i_operacion = 'B'-- Operaciones de Producto - Servicio
BEGIN
        
        IF @i_accion = 'I'                        --  PRODUCTO INSERT
        BEGIN 
                
----------------------------------------Validación Código----------------------------------------------

    --SET @w_ValorCosto =((100*(@i_Costo/@w_Impuesto))/100);
    SET @w_ValorCostoExterno = ((100*@i_CostoReferencia)/NULLIF(100,0));
    
----------------------------------------------------------------------------------
--Obtener Unidad de Medida.

                SELECT
                        @w_PresentacionId=IdMedida
                FROM
                        INVENTARIO.dbo.NTUnidadesMedida
                WHERE
                        Unidad = @i_unidadesmedida;
                
----------------------------------------------------------------------------------
        --Validacion del produto esta Correcto en su estructura y creacion.
                SELECT
                        @w_tmpCodigoInvalido = Count(*)
                FROM
                        Inventario.dbo.ProductosSubClase p
                INNER JOIN Inventario.dbo.ProductosClase c ON
                        (c.Empresa = p.Empresa
                        AND c.Codigo = p.Clase
                        AND c.ClasIFicacion = @w_ClaseServicio)
                WHERE
                        p.Empresa = @i_EmpresaBodega
                        AND p.Clase = SUBSTRING(@i_CodigoNuevo, 1, 1)
                        AND p.Codigo = SUBSTRING(@i_CodigoNuevo, 2, 1)
                        AND p.Ancho = (LEN(@i_CodigoNuevo) - 2);
----------------------------------------------------------------------------------
                
        SELECT
                @w_flagGenerarEscalasdePrecios=GenerarEscalasPrecios,
                @w_VentaExterna=VentaExterna
        FROM
                Inventario.dbo.ProductosSubClase p
        WHERE
                --p.Empresa = @i_Empresa
                p.Empresa = @i_EmpresaBodega
                AND p.Clase = SUBSTRING(@i_CodigoNuevo, 1, 1)
                AND p.Codigo = SUBSTRING(@i_CodigoNuevo, 2, 1);
        
    --Si el codigo es valido entonces verIFicamos si ya existe o no.
    --NOT , Valores Invertidos true=falso , falso=true, bit 0 a 1 , 1 a 0.
        IF(@w_tmpCodigoInvalido<>0)
        BEGIN
                
                IF NOT EXISTS(SELECT
                                                Nombre
                                        FROM
                                                Inventario.dbo.Productos
                                        WHERE
                                                Empresa = @i_EmpresaBodega
                                                AND Codigo = @i_CodigoNuevo)
                BEGIN
                        SET @w_Resultado= 0
                        SET @w_ErrTipo        = 0        --codigo de error por procedimiento
                        SET @w_Message        = 'No hay datos procede-Ingresar el Servicio'
                        
                        SET @i_SubDepartamento=(CASE WHEN @i_SubDepartamento='' THEN NULL ELSE @i_SubDepartamento END);
                        SET @i_Ensamblado=(CASE WHEN @i_TipoBodega='C' THEN @i_Ensamblado ELSE 'N' END);
                 

                        Insert Into Inventario.dbo.Productos(Empresa, Codigo, Nombre, Categoria, CostoUltimo, CostoPromedio, CostoAlto, Departamento, SubDepartamento, CodigoHospifar, Clase, SubClase, Usuario, Descripcion, Tipo, Proveedor, Warning, TipodeUso, Presentacion, Grupo,SubGrupo,Generico,Activo,Ensamblado)
						Values(@i_EmpresaBodega,@i_CodigoNuevo,@i_Nombre,@i_Categoria,@i_Costo,@i_Costo,@i_Costo,@i_Departamento,@i_SubDepartamento,@i_CodigoHospIFar,@i_Clase,@i_SubClase,@i_Usuario,@i_Descripcion,'S',@i_Proveedor,@i_Warning,@i_TipodeUso,@w_PresentacionId,@i_IdGrupo,@i_IdSubGrupo,@i_IdGenerico,'S',0)

                        IF(@i_SubClase<>'0' OR @i_SubClase<>'')
                        BEGIN
                                UPDATE Inventario.dbo.ProductosSubClase 
                                SET Correlativo = Correlativo+1
                                WHERE
                                        Empresa = @i_EmpresaBodega
                                        AND Clase = SUBSTRING(@i_CodigoNuevo, 1, 1)
                                        AND Codigo = SUBSTRING(@i_CodigoNuevo, 2, 1);
                        END
                

                INSERT INTO INVENTARIO.DBO.BitacoraModificacionCatalogo(Empresa,Codigo,Tabla,ValorGuardado,Usuario,Operacion,CodigoVarchar)
                VALUES(@i_Empresa,CONVERT(VARCHAR,@i_CodigoNuevo),'Producto',@i_estructuraIngreso,@i_usuario,'I',0); 			 
				        SET @w_Resultado= 0
                SET @w_ErrTipo        = 0        --codigo de error por procedimiento
								SET @w_Message        = 'Servicio ingresado exitosamente.'
								IF @w_ErrTipo != 0
                BEGIN
                        ROLLBACK TRAN
                        SELECT         @w_Resultado as codigo, @w_Message as error, @w_ErrTipo as tipo_error
                End
                ELSE
                BEGIN
                        COMMIT TRAN
                End--ELSE ErrTipo
                
                                
                END
                ELSE
                BEGIN
                        SET @w_Resultado= -2
                        SET @w_ErrTipo        = -2        --codigo de error por procedimiento
                        SET @w_Message        = 'Ya existe el Servicio.El Servicio tiene este código ' + @i_CodigoNuevo + ' .'
                        --COMMIT TRAN
                        ROLLBACK TRAN
                        SELECT         @w_Resultado as codigo, @w_Message as error, @w_ErrTipo as tipo_error
                END -- FIN-IF-EXISTE
					PRINT('5')
        END
        ELSE
        BEGIN
                SET @w_Resultado        = -1
                SET @w_ErrTipo        = -1        --codigo de error por procedimiento
                SET @w_Message        = 'El código ingresado no es válido, Vérifique por favor...'
                --COMMIT TRAN
                ROLLBACK TRAN
                SELECT         @w_Resultado as codigo, @w_Message as error, @w_ErrTipo as tipo_error
        END -- FIN - VALIDAR -CODIGO

----------------------------------------Validación Código----------------------------------------------
                
        END --FIN ACCION 'I'        

END        -- FIN IF OPERACION 'B'

END TRY
        BEGIN CATCH
                ROLLBACK TRAN
                Set @w_ErrRaise = ERROR_MESSAGE()
                Set @w_ErrRaiseNum = ERROR_NUMBER()
                
                RAISERROR(@w_ErrRaise,16,@w_ErrRaiseNum)
                --SELECT ERROR_NUMBER() as codigo, ERROR_MESSAGE() as error, 1 as tipo_error
        END CATCH


END--procedure;
GO

/************************************************************************************************************
 *********************************sp_consulta_cobertura_rn_interno*******************************************
 ************************************************************************************************************/

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[SP_consulta_cobertura_rn_interno]    Script Date: 21/02/2023 11:39:36 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[SP_consulta_cobertura_rn_interno]
@i_empresa_default as Varchar(3),
@i_serie_admision as varchar(1),
@i_admision as int,
@o_cargos as money,
@o_monto_cobertura as money,
@o_maximo_evento as money
/**************************************************************************/
/**************************************************************************/
--MODIFICADO POR: Javier Castillo se duplico el package para calcular la siniestralidad en sighos 
--Utilizando parametros de salida
--CREADO POR: Fabrizio Martínez         Numero de Rational  220683
--FECHA CREACIÓN: 03/03/2022 
--PRODUCTO: Consulta cobertura para RN, Salud siempre
/**************************   DESCRIPCIÓN    ******************************/ 
--El objetivo del procedimiento es validar el monto de cobertura y los gastos 

/**************************************************************************/
--MODIFICACIONES:

/**************************************************************************/
/**************************************************************************/

as begin
Begin Try

		Select @o_cargos = sum(VALOR), @o_monto_cobertura = Monto_cobertura, @o_maximo_evento = Maximo_evento 
			from Hospital..Caj_limites_cobertura AS lm
			left Join Hospital..Cargos as c on(lm.Serie_admision=c.serieadmision and lm.admision=c.admision)
			WHERE lm.Serie_admision=@i_serie_admision
			and c.empresa=@i_empresa_default
			AND lm.admision=@i_admision
			and lm.status=1 And C.Categoria<>'30'
			group by Monto_cobertura,Maximo_evento
End Try
 Begin Catch
--	  Rollback Transaction
--		Set @w_Respuesta='Error no controlado al realizar la transacción '+ ERROR_MESSAGE()+' No. error: '+Cast(ERROR_NUMBER() as varchar(10)) 
--		Set @w_CodRespuesta='-1'
--		Select @w_respuesta as Respuesta,@w_CodRespuesta as CodRespuesta
End catch

End
GO

/************************************************************************************************************
 **************************sp_Ajenos_Honorarios_Editar_Interno***********************************************
 ************************************************************************************************************/
USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[sp_Ajenos_Honorarios_Editar_Interno]    Script Date: 21/02/2023 11:46:40 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- ==========================================================
-- Author:  Walter Ronquillo
-- Create date: 17.05.2021
-- Description:	Actualiza los campos de un cargo, cita, cupón definido
-- 21.04.2022 - Se agrega manejo de cuenta cuando el cambio es por médico
-- ==========================================================
-- ==========================================================
-- Author: Javier Castillo
-- Create date: 07.02.2023
-- Description:	Se agregan parametros de salida para usar internamente el store procedure
-- dentro del sp de anulacion de cargos 
-- ==========================================================
CREATE PROCEDURE [dbo].[sp_Ajenos_Honorarios_Editar_Interno]
@i_Tipo tinyint,
@i_Empresa char(3) 		= null,
@i_TipoOrden char(3) 	= null,
@i_Orden varchar(15)	= null,
@i_Linea varchar(3) 	= null,
@i_Campo varchar(40)	= null,
@i_Valor varchar(50)	= null,
@o_Codigo int = 0 out,
@o_Descripcion varchar(max) = '' out,
@o_TipoError int = 0 out
AS
BEGIN
	DECLARE
		@wTempAjenoManejoCuenta smallmoney = NULL,
		@wTempAjenoManejoCuentaAjena smallmoney = NULL,
		@w_ErrRaise		Varchar(MAX)	= '',
		@w_ErrRaiseNum 	INT			= 0,
		@SQL nvarchar(Max)		= null,
		@i_Existencia int		= null

	BEGIN TRAN
	BEGIN TRY
	SET NOCOUNT ON;
	

	-- ========================================================== 
	-- CARGOS
	-- ==========================================================
	IF @i_Tipo = 1 
	BEGIN
		IF @i_Empresa is not null AND @i_TipoOrden is not null AND @i_Orden is not null AND @i_Linea is not null 
		BEGIN 			
			-- CONVIRTIENDO LINEA Y SI NO SE ASIGNA SE OMITE
			DECLARE @w_Linea varchar(50) = '';
			SELECT @i_Linea = CONVERT(int, @i_Linea) ;
			IF (@i_Linea > 0) set @w_Linea = 'and Linea = '+@i_Linea
			-- BUSCANDO EXISTENCIA
	    	SELECT @i_Existencia = count(*) FROM His_Honorarios	WHERE EmpresaUnificadora = @i_Empresa and TipoOrden = @i_TipoOrden and Orden = @i_Orden and (@i_Linea = 0 OR Linea = @i_Linea);
			IF @i_Existencia >0
			BEGIN
				IF (charindex( UPPER(RTRIM(LTRIM(@i_Campo))),'TIPO,EMPRESA,TIPOORDEN,ORDEN,LINEA') = 0)
				BEGIN 
					-- INTENTANDO GUARDAR VALOR NUMERICO
					BEGIN TRY
						SET @SQL = 'Update His_Honorarios SET  '+@i_Campo+' = '+ @i_Valor + ' WHERE EmpresaUnificadora ='''+@i_Empresa+''' and TipoOrden = '''+@i_TipoOrden+''' and Orden = '+@i_Orden + @w_Linea+';'
						EXEC (@SQL);
					END TRY
					BEGIN CATCH
					--INTENTANDO GUARDAR VALOR STRING
						SET @SQL = 'Update His_Honorarios SET  '+@i_Campo+' = '''+ @i_Valor + ''' WHERE EmpresaUnificadora ='''+@i_Empresa+''' and TipoOrden = '''+@i_TipoOrden+''' and Orden = '+@i_Orden + @w_Linea+';'
						EXEC (@SQL);
					END CATCH
					
					
					-- Validando si es cambio es de médico
					IF @i_Campo = 'Ajeno'
					BEGIN
						SELECT @wTempAjenoManejoCuenta = ManejoCuenta, @wTempAjenoManejoCuentaAjena = ManejoCuentaAjena FROM Ajenos a where a.Codigo = @i_Valor;		
					
						Update
							His_Honorarios
						SET
							AJE_ManejoCuenta = @wTempAjenoManejoCuenta,
							AJE_ManejoCuentaAjena = @wTempAjenoManejoCuentaAjena
						WHERE
							EmpresaUnificadora = @i_Empresa
							and TipoOrden = @i_TipoOrden
							and Orden = @i_Orden
							and Linea = @i_Linea;
					END
					
					SELECT @o_Codigo = 0 , @o_Descripcion =  'Se actualizado '+ CONVERT(varchar, @i_Existencia) +' registro.' , @o_TipoError = 0
					COMMIT TRAN;
				END
				ELSE
				BEGIN
					SELECT @o_Codigo = 0, @o_Descripcion =  'No es posible editar este campo' , @o_TipoError = 1 ;
					COMMIT TRAN;
				END
			END
			ELSE 
			BEGIN 
				SELECT @o_Codigo = 0, @o_Descripcion =  'No se encontro el registro' , @o_TipoError = 2 ;
				COMMIT TRAN;
			END
		END
		ELSE
		BEGIN
			SELECT @o_Codigo = 0, @o_Descripcion =  'Campos requeridos para cargos.' , @o_TipoError = 1 ;
			COMMIT TRAN;
		END

	END
	-- ========================================================== 
	-- ! SI LA OPERACIÓN NO EXISTE
	-- ==========================================================
	ELSE 
	BEGIN
		SELECT @o_Codigo = 0, @o_Descripcion =  'Opción incorrecta' , @o_TipoError = 1 ;
		COMMIT TRAN;
	END
	
	END TRY
	BEGIN CATCH
		ROLLBACK TRAN;
		Set @w_ErrRaise = ERROR_MESSAGE()
		Set @w_ErrRaiseNum = ERROR_STATE()
		RAISERROR(@w_ErrRaise,16,@w_ErrRaiseNum)
	END CATCH
END
GO

/************************************************************************************************************
 **********************************SpMontoSiniestralidadValido***********************************************
 ************************************************************************************************************/
USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[SpMontoSiniestralidadValido]    Script Date: 21/02/2023 11:48:21 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[SpMontoSiniestralidadValido](@i_EmpresaUnificadora char(3), @i_Admision INT, @i_Serie char(1), 
																										 @i_MontoTotal money, @w_tieneSiniestralidad bit out)
as
begin
	DECLARE @w_TotalDisponibleEvento MONEY = 0
	DECLARE @w_ContratoCodigo CHAR(15)
	DECLARE @w_PlanCodigo CHAR(15)
	DECLARE @w_AfiliadoCodigo CHAR(17)	

	DECLARE @w_SumaTotal MONEY = 0
	DECLARE @w_MaximoVitalicio MONEY = 0
	DECLARE @w_MaximoAnual MONEY = 0	
	DECLARE @w_MaximoEvento MONEY = 0	
	DECLARE @w_GastoTotalSinCierre MONEY = 0
	DECLARE @w_GastoAnualSinCierre MONEY = 0
	DECLARE @w_TotalDisponible MONEY = 0
  DECLARE @w_TotalDisponibleVitalicio MONEY	= 0
	DECLARE @w_CargosRN MONEY = 0
	DECLARE @w_MontoCoberturaRN MONEY = 0
	DECLARE @w_MaximoEventoRN MONEY = 0

  SET @w_tieneSiniestralidad = 1
  SET @w_CargosRN = 0
  SET @w_MontoCoberturaRN = 0
  SET @w_MaximoEventoRN = 0

	--1ERO x Evento
	Select @w_PlanCodigo = IdPlan, @w_ContratoCodigo = IdContrato, @w_AfiliadoCodigo = IdAfiliado, 
				 @w_TotalDisponibleEvento = TotalDisponible
		from SiniestralidadAcumuladoEventoXAfiliado
	 Where Empresa = @i_EmpresaUnificadora
		 and SerieAdmision = @i_Serie and Admision = @i_Admision
	
	IF @@RowCount = 0 
	BEGIN
		SET @w_tieneSiniestralidad = 0
		GOTO ADMISION_RN 
	END

	IF (@w_TotalDisponibleEvento - @i_MontoTotal) < 0 
	BEGIN
		SET @w_tieneSiniestralidad = 0
		GOTO ADMISION_RN 
	END
	
	Select @w_GastoTotalSinCierre = SUM(ISNULL(TotalAcumulado,0) + ISNULL(HabitacionAcumulado,0))
		from SiniestralidadAcumuladoSinCierreXAfiliado
	 Where Empresa = @i_EmpresaUnificadora
		 and IdContrato = @w_ContratoCodigo and IdAfiliado = @w_AfiliadoCodigo

	Select @w_GastoAnualSinCierre = SUM(ISNULL(TotalAcumulado,0) + ISNULL(HabitacionAcumulado,0))
		from SiniestralidadAcumuladoSinCierreXAfiliado
	 Where Empresa = @i_EmpresaUnificadora
		 and IdContrato = @w_ContratoCodigo and IdAfiliado = @w_AfiliadoCodigo
		 and Annio = CONVERT(INT,Year(GetDate()))


	Select @w_MaximoVitalicio =  MaximoVitalicio, @w_MaximoAnual = MaximoAnual, @w_MaximoEvento = MaximoEvento 
	  from PlanesMedicos..PlanesXContrato
	 Where IdEmpresa = @i_EmpresaUnificadora
	   and IdPlanXContrato = @w_PlanCodigo and IdContrato = @w_ContratoCodigo
	
	-- 2DO X AÑO
	Select @w_TotalDisponible = TotalDisponible
		from SiniestralidadAcumuladoAnualXAfiliado
	 Where Empresa = @i_EmpresaUnificadora
		 and IdContrato = @w_ContratoCodigo and IdAfiliado = @w_AfiliadoCodigo
		 and Anio = CONVERT(INT,Year(GetDate()))

	IF @@ROWCOUNT = 0 
	BEGIN
		IF (@w_MaximoAnual- @w_GastoAnualSinCierre - @i_MontoTotal) < 0
		BEGIN
			SET @w_tieneSiniestralidad = 0
		END		
	END
	ELSE BEGIN
			IF (@w_TotalDisponible - @w_GastoAnualSinCierre - @i_MontoTotal) < 0
			BEGIN
				SET @w_tieneSiniestralidad = 0
			END
	END
	--3ERO X Vitalicio
	IF @w_tieneSiniestralidad = 1
	BEGIN
		Select @w_TotalDisponibleVitalicio = TotalDisponible
			from SiniestralidadAcumuladoVitalicioXAfiliado
		 Where Empresa = @i_EmpresaUnificadora
			 and IdContrato = @w_ContratoCodigo and IdAfiliado = @w_AfiliadoCodigo
		
			If @@RowCount = 0
			BEGIN
				IF (@w_MaximoAnual- @w_GastoTotalSinCierre - @i_MontoTotal) < 0
				BEGIN
					SET @w_tieneSiniestralidad = 0
				END		
			END
			ELSE BEGIN
				IF (@w_TotalDisponibleVitalicio - @w_GastoTotalSinCierre - @i_MontoTotal) < 0
				BEGIN
					SET @w_tieneSiniestralidad = 0
				END
			END
	END

	ADMISION_RN:
	--Validar si es una admision de RN con beneficio de salud siempre
	execute sp_consulta_cobertura_rn_interno @i_empresa_default = @i_EmpresaUnificadora, 
																					 @i_serie_admision = @i_Serie, @i_admision = @i_Admision,
																					 @o_cargos = @w_CargosRN,	
																					 @o_monto_cobertura = @w_MontoCoberturaRN	, 
																					 @o_maximo_evento = @w_MaximoEventoRN

	IF @w_CargosRN <> 0
	BEGIN
		SET @w_tieneSiniestralidad = 1

		IF (@w_CargosRN > @w_MontoCoberturaRN)
		BEGIN
			SET @w_tieneSiniestralidad = 0
		END

		IF (@w_CargosRN > @w_MaximoEventoRN)
		BEGIN
			SET @w_tieneSiniestralidad = 0
		END
	END
END
GO


/************************************************************************************************************
 ***********************************SpMontoMaximoPendienteValido*********************************************
 ************************************************************************************************************/
USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[SpMontoMaximoPendienteValido]    Script Date: 21/02/2023 11:49:57 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[SpMontoMaximoPendienteValido](@i_EmpresaUnificadora char(3), @i_Admision INT, @i_Serie char(1), @i_MontoTotal money,
																							@o_MontoValido bit out) 
as begin
DECLARE @w_TipoDescuento char(1)
DECLARE @w_CategoriaExtraordinarios char(2)
DECLARE @w_ServiciosMedicos char(2)
DECLARE @w_MontoAdeuadoMaximoPermitido money = 0
DECLARE @w_CodigoColumnaPlanillaHospital char(2)
DECLARE @w_TotalNeto money = 0
DECLARE @w_TotalDescNotas money = 0
DECLARE @w_TotalDescAnticipos money = 0
DECLARE @w_TotalDescAnticiposNuevos money = 0
DECLARE @w_TotalCargosAjenos money = 0
DECLARE @w_TotalCargosNoFacturados money = 0
DECLARE @w_TotalAbono money = 0
DECLARE @w_MontoAcumulado money = 0
DECLARE @w_CodigoDescuentoDeEmpleado char(1)

SET @o_MontoValido = 1
SET @w_CodigoColumnaPlanillaHospital = '20'
SET @w_CodigoDescuentoDeEmpleado = 'E'

/*
  CodigoColumnaPlanillaHospital      = 20;  // Codigo en RRHH de descuento de Hospitalizaciones...
  CodigoDescuentoDeEmpleado          = 'E'; // Codigo en admisiones de cuentas de empleados...
  CodServicioExtraordinarioCategoria = '30'; // Categoria de servicios extraordinarios (cargos exclusivos del paciente)...
  CodServicioOrdinarioCategoria      = '97'; // Categoria de servicios medicos (honorarios)...
  CodCuentaAjenaCategoria            = '99'; // Categoria de Cuenta Ajena...
  CodCuentaAjenaExtraOrdCategoria    = '98'; // Categoria de Cuenta Ajena Extraordinaria...
*/

	SELECT @w_TipoDescuento=TipoDescuento
		FROM admisiones
	 WHERE empresa = @i_EmpresaUnificadora
		 AND codigo = @i_Admision
		 AND serie = @i_Serie

	SELECT @w_CategoriaExtraordinarios = ISNULL(CategoriaExtraordinarios,'30'), 
				 @w_ServiciosMedicos = ISNULL(ServiciosMedicos,'97'), 
				 @w_MontoAdeuadoMaximoPermitido = ISNULL(MontoAdeudadoMaximo,0)
		FROM Defaults
	 WHERE Empresa = 'MED'

	IF @@rowcount = 0 
	BEGIN
		SET @w_CategoriaExtraordinarios = '30'
		SET @w_ServiciosMedicos = '97'
		SET @w_MontoAdeuadoMaximoPermitido = 0
	END

	IF @w_MontoAdeuadoMaximoPermitido <= 0
	BEGIN
		RETURN
	END

-- PRIMERA PARTE, todas las facturas con descuentos y notas de credito
-- se quito el filtro por hospital en las facturas
	Select Distinct (f.Total - ISNULL(f.Descuento,0))  TotalNeto,
									 ISNULL((Select  SUM(ISNULL(n.Servicios,0)+ ISNULL(n.Ventas,0))
														from Hospital..NotasCredito n (nolock)
														Where n.Empresa = f.Empresa and n.Status = 'P'
														and n.SerieFactura = f.Serie and n.Factura = f.Codigo ),0) TotalDescNotas,
									ISNULL((Select SUM(ISNULL(-1 * d.Monto, 0))
														from Nominadb..EmpPagosDetalle d (nolock) Inner Join Nominadb..EmpPagos p (nolock)
														ON (d.Empresa = p.Empresa and d.Periodo = p.Periodo and d.Codigo = p.Codigo)
														Where d.Columna = @w_CodigoColumnaPlanillaHospital and d.SerieFactura = f.Serie
														and d.Factura = f.Codigo ),0) TotalDescAnticipos,
									ISNULL((Select SUM(ISNULL(nd.MontoEsteMes, 0))
													from Nominadb..EmpAnticiposDetalle nd (nolock) Inner Join Nominadb..EmpPagos np (nolock)
													ON (nd.Empresa = np.Empresa and nd.Periodo = np.Periodo and nd.Planilla = np.Planilla
													and nd.Empleado = np.Empleado)
													Where nd.Columna = @w_CodigoColumnaPlanillaHospital and nd.SerieFactura = f.Serie
													and nd.Factura = f.Codigo ),0) TotalDescAnticiposNuevos
						 into #FacturasAdmision
						 from Hospital..Facturas f  (nolock)
						where f.Status = 'P'
							and f.SerieAdmision = @i_Serie and f.Admision = @i_Admision

	IF @@ROWCOUNT > 0
	BEGIN
		IF @w_TipoDescuento = @w_CodigoDescuentoDeEmpleado
		BEGIN
			SELECT @w_MontoAcumulado = ISNULL(SUM(TotalNeto),0)- ISNULL(SUM(TotalDescNotas),0) - 
																 ISNULL(SUM(TotalDescAnticipos),0) - ISNULL(SUM(TotalDescAnticiposNuevos),0)
				FROM #FacturasAdmision
		END
		ELSE BEGIN
			SELECT @w_MontoAcumulado = ISNULL(SUM(TotalNeto),0) - ISNULL(SUM(TotalDescNotas),0)
				FROM #FacturasAdmision
		END	
	END
	
-- SEGUNDA PARTE cargos ajenos ya facturados a la cuenta
-- exclusivamente "CUENTA AJENA"
		Select @w_TotalCargosAjenos = ISNULL(SUM(CASE WHEN c.ValorFacturado > 0 THEN c.ValorFacturado ELSE c.Valor END),0)
			from AjenosCobrados c  (nolock)
Inner Join Inventario.dbo.Productos P (nolock) ON (P.Empresa = @i_EmpresaUnificadora and C.Producto = P.Codigo)
	   Where c.SerieAdmision = @i_Serie and c.Admision = @i_Admision
			 and p.Categoria NOT IN (@w_CategoriaExtraordinarios, @w_ServiciosMedicos)

		IF @@ROWCOUNT > 0
		BEGIN
			SET @w_MontoAcumulado = @w_MontoAcumulado + @w_TotalCargosAjenos
		END

-- TERCERA PARTE 
-- Recuperamos el total acumulado de todos los cargos que no han sido facturados

	Select @w_TotalCargosNoFacturados = ISNULL(SUM(c.Valor),0)
	  from Cargos c  (nolock)
	 Where c.Empresa = @i_EmpresaUnificadora
	   and c.SerieAdmision = @i_Serie and c.Admision = @i_Admision
	  and c.Valor > 0 and c.FacRec IS NULL

	IF @@ROWCOUNT > 0
	BEGIN
		SET @w_MontoAcumulado = @w_MontoAcumulado + @w_TotalCargosNoFacturados
	END

-- CUARTA PARTE
--	Recuperamos todos los Abonos realizados a la admision unicamente...
--	aqui entraran tambien las extensiones de Creditos aprobadas por Gerencia y/o Coordinadoras
Select @w_TotalAbono = ISNULL(SUM(s.TotalAbono),0)
	from (
				Select Distinct R.Total as TotalAbono
					from Recibos R  (nolock)
				 where R.Status = 'P'
					 and R.SerieAdmision = @i_Serie and R.Admision = @i_Admision
				 UNION ALL
				Select Distinct (-1 * AD.Valor) AS TotalAbono
					from AdmisionesDevoluciones AD  (nolock)
				 where  AD.SerieAdmision = @i_Serie and AD.Admision = @i_Admision and AD.Valor > 0 and AD.Movimiento IS NOT NULL
					 and Exists (Select Numero from Contadb..BancosMovtos x  (nolock)
												Where x.Empresa = AD.Empresa and x.Tipo = 'CH' and x.Status = 'P'
													and x.Cuenta = AD.CuentaBanco and x.Numero = AD.Movimiento)
				 UNION ALL
				Select Distinct et.MontoAutorizado AS TotalAbono
					from AdmisionesExtensionCredito et  (nolock)
				 where et.SerieAdmision = @i_Serie and et.Admision = @i_Admision
				) s

	IF @@ROWCOUNT > 0
	BEGIN
		SET @w_MontoAcumulado = @w_MontoAcumulado - @w_TotalAbono
	END
	
	IF (@w_MontoAcumulado + @i_MontoTotal) > @w_MontoAdeuadoMaximoPermitido
	BEGIN
		SET @o_MontoValido = 0
	END
END
GO
/************************************************************************************************************
 ***********************************SpAnulacionDevolucionOrdenes*********************************************
 ************************************************************************************************************/
USE [HOSPITAL]
GO

/****** Object:  StoredProcedure [dbo].[SpAnulacionDevolucionOrdenes]    Script Date: 13/04/2023 11:49:58 a.m. ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


-- ==========================================================
-- Author: Javier Castillo
-- Create date: 07.02.2023
-- Description:	Anulacion de ordenes
-- ==========================================================
CREATE PROCEDURE [dbo].[SpAnulacionDevolucionOrdenes]
-- A = Anulacion
-- D = Devolucion
@IOpcion varchar(1) = 'A',
-- Este codigo es el mismo para devolucion y anulacion 
@i_TipoOpInv varchar(10) = 'OR', 
@i_Empresa varchar(3) = 'MED',
@i_EmpresaBodega varchar(10)='BOD',
@i_EmpresaSem varchar(3) = 'SEM',
@i_EmpresaReal varchar(3),
@i_CodigoOrden int,
@i_TipoOrden varchar(3),
@i_Razon varchar(40)='',
@i_corporativo varchar(10),
@i_CodigoProductos varchar(max) = '',
@i_CantidadProductos varchar(max) = ''
as
Begin
	DECLARE
		@serieAdmision varchar(3),
		@numeroAdmision INT,
		@w_ErrRaise		Varchar(MAX)	= '',
		@w_ErrRaiseNum 	INT				= 0,
		@w_Existe 		Int				= 0,
		@w_codigoOrden char(3) = '',
		@w_lineaOrden INT = 0,
		@w_facRec INT = 0,
		@w_ImagenUp BIT = 0,
		@w_NumeroInformesRadiologia INT = 0,
		@w_NumeroLotesProntoPago INT = 0,
		@w_IdHonorario INT = 0,
		@w_SerieFacturaTraslado char(3) = '',
		@w_IdProductoMenor INT = 0,
		@w_ProductoActual Varchar(13) = '',
		@w_CantidadActual smallmoney = 0,
		@w_MensajeObservacion Varchar(30) = '',
		@w_CorrelativoAnulado INT = 0,
		@w_VenaPaciente Cuenta = 0,
		@w_CompraFutura Cuenta = 0,
		@w_PorcGanancia smallmoney = 0,
		@w_Bodega int = 0,
		@w_SalidaSp varchar(max),
	  @w_EmpresaRealOrden varchar(3)

	BEGIN TRAN
	BEGIN TRY
	SET NOCOUNT ON;
		IF (@IOpcion = 'A') 
		BEGIN 
			--Tipo de orden
			Select @w_codigoOrden = ISNULL(Codigo,0)
			  from OrdenesTipos (nolock)
		   where Codigo = @i_TipoOrden

			IF @@ROWCOUNT = 0
			BEGIN
		    Select 1 AS "codigo", 
							 concat('No se encontro el tipo de orden : ',@i_TipoOrden, ' , como un tipo valido.') as "descripcion", 
							 1 as tipo_error
				RETURN
			END
			--revisa que existan los cargos
			SELECT C.TipoOrden, C.Orden, C.Linea, C.SerieAdmision, C.Admision, P.Nombre, P.Apellido, P.ApellidoCasada, FacRec, C.SerieFacTraslado
						 Producto, Cantidad, Valor, NumeroSerie, Factor, Categoria,Tipo = (Select I.Tipo  From  Inventario..Productos  I  Where  I.Empresa = C.Empresa and  I.Codigo = C.Producto)
				into #ListaCargos
				from Cargos C Left Join Pacientes P
		 			ON (C.Empresa = P.Empresa and C.Paciente = P.Codigo)
			 WHERE C.Empresa = @i_Empresa AND C.TipoOrden = @i_TipoOrden AND C.Orden = @i_CodigoOrden
			 Order by C.Facrec Desc

			IF @@ROWCOUNT = 0 
			BEGIN
		    Select 1 AS "codigo", 
							 concat('No se encontraron cargos para la orden : ',@i_TipoOrden, '-',@i_CodigoOrden) as "descripcion", 
							 1 as tipo_error
				RETURN
			END
			ELSE
			BEGIN
				select top 1 @serieAdmision = SerieAdmision, @numeroAdmision = Admision from #ListaCargos				
			END
			
			select top 1 @w_facRec = ISNULL(FacRec,0) from #ListaCargos			
			if @w_facRec <> 0 
			begin
					Select 1 AS "codigo", 
						'Cargos facturados, no puede eliminar hasta anular la factura' as "descripcion", 
						1 as tipo_error
					RETURN
			end

			Select Status, Bodega, StatusEstadisticas, CierreSiniestralidad, ISNULL(ImagenesUP,0) As ImagenesUP, EmpresaReal
				into #Orden
		  	from Ordenes
		   Where Tipo = @i_TipoOrden and Codigo = @i_CodigoOrden
			
			IF @@ROWCOUNT = 0
			BEGIN
					Select 1 AS "codigo", 
						concat('No se encontro la orden ',@i_TipoOrden, '-',@i_CodigoOrden) as "descripcion", 
						1 as tipo_error
					RETURN
			END
			ELSE
			 BEGIN
				 SELECT top 1 @w_EmpresaRealOrden = EmpresaReal FROM #Orden 
			 END

			select top 1 @w_ImagenUp = ISNULL(ImagenesUp,0), @w_Bodega = ISNULL(Bodega,0)  from #Orden
			IF @w_ImagenUp = 1
			BEGIN
					Select 1 AS "codigo", 
						'Esta orden Ya tiene Imágenes de Radiología Grabadas' as "descripcion", 
						1 as tipo_error
					RETURN
			END
			-- Accesa la admision y verifica que no esta desactivada o ya haya registrado su Siniestralidad...
			Select Serie, Codigo, Status, IdAfiliado, FechaCierreSiniestralidad
				Into #Admision
				From Admisiones
			 where Serie = @serieAdmision 
         and Codigo = @numeroAdmision

			IF @@ROWCOUNT = 0
			BEGIN
					Select 1 AS "codigo", 
						'No se encontro una admisión valida para la orden' as "descripcion", 
						1 as tipo_error
					RETURN
			END

			IF (select top 1 status from #Admision) = 'D' or 
				 ( (select top 1 IdAfiliado from #Admision) <> null and (select top 1 FechaCierreSiniestralidad from #Admision) <> null )
			BEGIN
				IF (select top 1 FechaCierreSiniestralidad from #Admision) <> null
				BEGIN
					Select 1 AS "codigo", 
					'Esta admisión ya registró su Siniestralidad, no puede Anular cargos' as "descripcion", 
					1 as tipo_error
					RETURN
				END
				ELSE
				BEGIN
					Select 1 AS "codigo", 
					'La admisión de esta orden está desactivada, no puede Anular cargos' as "descripcion", 
					1 as tipo_error
					RETURN
				END
			END

			IF (SELECT TOP 1 StatusEstadisticas FROM #Orden) = 'A' or
				 ( (SELECT TOP 1 IdAfiliado FROM #Admision) <> null and (SELECT TOP 1 CierreSiniestralidad FROM #Orden) <> null )
			begin
				 IF (SELECT TOP 1 CierreSiniestralidad FROM #Orden) <> null
				 BEGIN
						Select 1 AS "codigo", 
						'Esta orden ya registró su Siniestralidad, no puede Anular cargos' as "descripcion", 
						1 as tipo_error
						RETURN					
				 END
				 ELSE
				 BEGIN
						Select 1 AS "codigo", 
						'Esta orden ya fue incluida en las estadísticas, no puede Anular cargos' as "descripcion", 
						1 as tipo_error
						RETURN							
				 END
			end

			Select @w_NumeroInformesRadiologia = ISNULL(COUNT(*),0)
			  from RadiologiaInformes
			 Where (Impreso IS NOT NULL or informelock='S')
				 and TipoOrden = @i_TipoOrden
				 and Orden = @i_CodigoOrden

			IF @w_NumeroInformesRadiologia > 0
			BEGIN
					Select 1 AS "codigo", 
					'Esta orden ya posee informes de diagnóstico impresos, O está congelado.' as "descripcion", 
					1 as tipo_error
					RETURN			
			END
			
			Select @w_codigoOrden = TipoOrden
				from LaboratorioResultados
			 Where TipoOrden = @i_TipoOrden and Orden = @i_CodigoOrden
			
			IF @@ROWCOUNT <> 0
			BEGIN
					Select 1 AS "codigo", 
					'Esta orden ya posee resultados de Laboratorio grabados, no se puede anular.' as "descripcion", 
					1 as tipo_error
					RETURN			
			END

			Select @w_NumeroLotesProntoPago = Count(*)
			  from Cargos c Inner Join LotesProntoPagoDetalle d
					ON (d.SerieAdmision = c.SerieAdmision and d.Admision = c.Admision and d.Medico = c.Ajeno)
			 Where c.TipoOrden = @i_TipoOrden and c.Orden = @i_CodigoOrden

			IF @w_NumeroLotesProntoPago <> 0
			BEGIN
					Select 1 AS "codigo", 
					'Esta orden ya forma parte de un lote de facturas Pronto Pago.' as "descripcion", 
					1 as tipo_error
					RETURN			
			END

		 SELECT @w_SerieFacturaTraslado = isnull(max(SerieFacTraslado),'')
			 FROM [HOSPITAL].[dbo].[Cargos] C (nolock)
			Where Empresa = @i_Empresa
				and SerieFacTraslado is Not null
				and tipoorden = @i_TipoOrden
				and orden = @i_CodigoOrden

			IF @w_SerieFacturaTraslado <> ''
			BEGIN
					Select 1 AS "codigo", 
					'Cargos facturados (Factura de Traslados Hospitalarios),  no puede eliminar hasta anular la factura.' as "descripcion", 
					1 as tipo_error
					RETURN			
			END 
			
			-- Validar Estado
			select Id
			  into #Honorarios
				from Hospital..His_honorarios 
			 where TipoOrden=@i_TipoOrden and Orden=@i_CodigoOrden
				 and estado_honorarios <> 'A'
			
			IF @@ROWCOUNT <> 0
			BEGIN				
				Select 1 AS "codigo", 
				'Existe uno o más cargos utilizados en honorarios' as "descripcion", 
				1 as tipo_error
				RETURN			
			END

			-- Mensaje por defecto de razon
			SELECT @i_Razon = case when datalength(@i_Razon) = 0 or @i_Razon is null 
														then CONCAT('Anula Orden ',@i_TipoOrden,'-',@i_CodigoOrden)else @i_Razon end

--***********************hay que actualizar existencias, pendiente si se valida por este estado***********************--
			/*IF (SELECT TOP 1 STATUS FROM #Orden) = 'I'
			BEGIN
				
			END*/

     -- Obtencion de todos los productos de la oden
			SELECT ROW_NUMBER() OVER(ORDER BY a.productoservicio ASC) AS keyCount,
				a.productoservicio, a.cantidad, a.factor
			INTO #PRODUCTOS
			FROM(
				 select Producto productoservicio, Cantidad, c.Factor
					from Hospital..Cargos C INNER JOIN 
							 Inventario..Productos p ON (C.producto = p.Codigo and C.categoria = p.categoria)
				 where c.Empresa = @i_Empresa
				   and c.TipoOrden = @i_TipoOrden
				   and c.Orden = @i_CodigoOrden
					 and c.SerieAdmision = @serieAdmision
					 and c.Admision = @numeroAdmision
					 ) a

	--Actualizacion de existencias
			UPDATE a
				 SET a.Existencia  = a.Existencia + (b.Cantidad * b.factor)
			  FROM INVENTARIO..ExistenciasBodega a
	INNER JOIN #PRODUCTOS b on a.Producto = b.productoservicio
				 AND a.Empresa = @i_EmpresaBodega
				 AND a.Bodega = @w_Bodega

		SET @w_MensajeObservacion = CONCAT('Anula Orden ',@i_TipoOrden,'-',@i_CodigoOrden)

			-- Detalle de los productos anulados

		SELECT @w_CorrelativoAnulado = correl.siguiente 
			FROM Hospital..Correlativo_CargosAnulados correl
		 WHERE correl.Empresa = @i_Empresa					

		SELECT @w_IdProductoMenor = min(keyCount)
			FROM #PRODUCTOS

		WHILE @w_IdProductoMenor <> 0
		BEGIN

			SELECT @w_ProductoActual = productoservicio	, @w_CantidadActual = cantidad
				FROM #PRODUCTOS
			 WHERE keyCount = @w_IdProductoMenor
			
			EXEC Inventario..Proc_ActualizaExistenciaDetalle 
					 @Bodega = @w_Bodega, @EmpresaReal = @w_EmpresaRealOrden, @BodegaEmpresa = @i_EmpresaBodega,
					 @Producto = @w_ProductoActual, @Tipo = 'OR', @Serie = 'A',  @Documento = @w_CorrelativoAnulado,
					 @Sucursal = @w_EmpresaRealOrden, @Cantidad = @w_CantidadActual,  @Observacion = @w_MensajeObservacion		
						
			SELECT @w_IdProductoMenor = isnull(min(keyCount),0)
				FROM #PRODUCTOS
			 WHERE keyCount > @w_IdProductoMenor
		END
/*
		-- Cabecera Anulacion
		insert into Hospital..Anulaciones(Empresa, Serie,Numero, Tipo, Fecha, Usuario, Razon, SerieAdmision, Admision, EmpresaReal)
			values (@i_EmpresaReal,@i_TipoOrden, @i_CodigoOrden, 'F',GetDate(),@i_corporativo,@i_Razon,@serieAdmision,@numeroAdmision,@i_EmpresaReal);
*/

		-- Detalle Anulacion
		INSERT CargosAnuladosDetalle (
								Empresa, Correlativo, Linea, TipoOrden, Orden, Producto, Categoria, 
								Valor, Cantidad, Ajeno, Factor, Costo, SubNivelPrecios,EmpresaReal)
		Select Empresa, @w_CorrelativoAnulado, Linea, TipoOrden, Orden, Producto, Categoria, 
						Valor, Cantidad, Ajeno, Factor, Costo,SubNivelPrecios, @w_EmpresaRealOrden
		From Cargos
		Where Empresa = @i_Empresa  and  Tipoorden = @i_TipoOrden  and orden = @i_CodigoOrden
				--Empresa = @i_Empresa se envia empresa unificada en OrdenesElimina linea 387

		UPDATE Hospital..CargosAnuladosDetalle
			 SET CostoUltimo = Isnull( (Select P.CostoUltimo from Inventario..Productos P Where P.empresa = @i_EmpresaBodega and P.codigo = Producto) , 0)
		 WHERE (EmpresaReal = @w_EmpresaRealOrden ) and (Empresa = @i_Empresa ) AND (TipoOrden = @i_TipoOrden ) AND (Orden = @i_CodigoOrden ) AND (Correlativo = @w_IdProductoMenor )
	
	-- Aumento correlativo de anulacion 
		UPDATE correl 
			 SET correl.siguiente = correl.siguiente+1
			FROM Hospital..Correlativo_CargosAnulados correl
		 WHERE correl.Empresa = @i_Empresa;					
	
	-- Fin Detalle anulacion

		Select @w_VenaPaciente = VenaPaciente, @w_CompraFutura = CompraFutura, @w_PorcGanancia = PorcGanancia
			From Hospital..Contabilidad
		 Where Empresa = @w_EmpresaRealOrden

		Select @i_EmpresaSem = EmpresaSucursal
			from CONTADB..EmpresasDefaults
		 where empresa = @i_EmpresaBodega
		
		-- insertando contabilidad 
				--Insertar la poliza en inventario SEM
		Insert Into Inventario..InventarioCargos (Empresa, Tipo, SerieDocumento, Documento, Linea, 
																							Categoria, Costo, Debe, Haber, 
																							Fecha, PeriodoPartida, Partida,EmpresaReal)
		Select  @i_EmpresaSem, @i_TipoOpInv as Tipo, @IOpcion as SerieDoc,C.Correlativo  as Doc, Row_Number() Over (Order by C.Empresa, C.Categoria, C.TipoOrden, C.Orden) as Linea,
						C.Categoria, SUM(C.Costo * C.Cantidad) As Costo,  B.CuentaLocal As Debe, RTRIM(ccc.CuentasCostoSuministro) As Haber, 
						GetDate() as FechaRegistro, NULL as PeriodoPartida, NULL as Partida,@w_EmpresaRealOrden
		From Hospital..CargosAnuladosDetalle C
		Inner Join Inventario..Productos P On (C.Empresa = P.Empresa and C.Producto=P.Codigo and P.Tipo = 'P')
		Left Join Hospital..OrdenesTipos x ON (c.Empresa = x.Empresa and c.TipoOrden = x.Codigo)
		Inner Join Inventario..Bodegas B ON (B.Empresa = @i_EmpresaBodega  and  B.Codigo = ISNULL(x.BodegaDefault, 99))
		INNER JOIN Inventario..CategoriaCuentasCostos ccc ON (ccc.Empresa = @i_EmpresaSem  AND ccc.Categoria = P.CATEGORIA AND ccc.Hospital = B.HOSPITAL)
		Where C.Empresa = @i_Empresa AND C.Tipoorden = @i_TipoOrden AND C.ORDEN = @i_CodigoOrden
		Group By C.Empresa, C.Correlativo,C.Categoria, C.TipoOrden, C.Orden, RTRIM(ccc.CuentasCostoSuministro) , B.CuentaLocal

				--Inserta cuenta detalle Hospital SEM
		Insert Into Inventario..InvCargosDetalle
		(Empresa, TipoOperacion, SerieDocumento, Documento, Linea, Producto, Cantidad, CostoUnitario, CostoUnitarioHospital, Categoria, CostoTotal, 
														 CostoTotalHospital, SerieDocumentoOrigen, DocumentoOrigen, EmpresaFacReq, SerieFactura, Factura, FechaTransaccion, Debe, Haber, BodegaFuente, 
														 BodegaDestino, Departamento,EmpresaReal,LineaDocumento)
		Select @i_EmpresaSem, 'OR' as Tipo, 'A' as SerieDoc,C.Correlativo  as Doc, Row_Number() Over (Order by C.Empresa, C.Categoria) as Linea, C.Producto, C.Cantidad, Cast(Costo As money) As CostoUnitario,  Cast(C.Costo As Money) * (1+(@w_PorcGanancia /100.000000)) As CostoUnitarioHospital, C.Categoria, (Cast(Costo As money) * Cantidad)   As CostoTotal,
		Cast(C.Costo As Money) *C.Cantidad* (1+(@w_PorcGanancia /100.000000)) As CostoTotalHospital, C.Tipoorden, C.Orden, Null as EmpresaFacReq,Null as SerieFactura , Null as Factura,  GetDate() as FechaTransaccion , B.CuentaLocal As Debe, RTRIM(ccc.CuentasCostoSuministro)  As Haber, 
		NULL as BodegaFuente,ISNULL(x.BodegaDefault, 99) as BodegaDestino, Null as Departamento, @w_EmpresaRealOrden, C.linea
		From Hospital..CargosAnuladosDetalle C
		Inner Join Inventario..Productos P On (C.Empresa = P.Empresa and C.Producto=P.Codigo and P.Tipo = 'P')
		Inner Join Hospital..OrdenesTipos x ON (c.Empresa = x.Empresa and c.TipoOrden = x.Codigo)
		Inner Join Inventario..Bodegas B ON (B.Empresa = @i_EmpresaBodega  and  B.Codigo = ISNULL(x.BodegaDefault, 99))
		INNER JOIN Inventario..CategoriaCuentasCostos ccc ON (ccc.Empresa = @i_EmpresaSem  AND ccc.Categoria = P.CATEGORIA AND ccc.Hospital = B.HOSPITAL)
		Where C.Empresa = @i_Empresa AND C.Tipoorden = @i_TipoOrden AND C.ORDEN = @i_CodigoOrden

				--Insertar la poliza en Inventario HOSPITAL
		Insert Into Inventario..InventarioCargos (Empresa, Tipo, SerieDocumento, Documento, Linea, Categoria, Costo, Debe, Haber, Fecha, PeriodoPartida, Partida,EmpresaReal)
		Select  @w_EmpresaRealOrden, 'OR' as Tipo, 'A' as SerieDoc,C.Correlativo  as Doc,Row_Number() Over (Order by C.Empresa, C.Categoria, C.TipoOrden, C.Orden) as Linea,
		C.Categoria, SUM(C.Costo * C.Cantidad * (1+(@w_PorcGanancia /100.000000))) As Costo, @w_CompraFutura As Debe, @w_VenaPaciente As Haber, GetDate() as FechaRegistro, NULL as PeriodoPartida, NULL as Partida,@w_EmpresaRealOrden
		From Hospital..CargosAnuladosDetalle C
		Inner Join Inventario..Productos P On (C.Empresa = P.Empresa and C.Producto=P.Codigo and P.Tipo = 'P')
		Left Join Hospital..OrdenesTipos x ON (c.Empresa = x.Empresa and c.TipoOrden = x.Codigo)
		Inner Join Inventario..Bodegas B ON (B.Empresa = @i_EmpresaBodega  and  B.Codigo = ISNULL(x.BodegaDefault, 99))
		Where C.Empresa = @i_Empresa AND C.Tipoorden = @i_TipoOrden AND C.ORDEN = @i_CodigoOrden
		Group By C.Empresa, C.Correlativo,C.Categoria, C.TipoOrden, C.Orden, B.CuentaDespachoPaciente, B.CuentaLocal

				--Inserta cuenta detalle Hospital HOSPITAL
		INSERT INTO INVENTARIO.dbo.InvCargosDetalle
														 (Empresa, TipoOperacion, SerieDocumento, Documento, Linea, Producto, Cantidad, CostoUnitario, CostoUnitarioHospital, Categoria, CostoTotal, 
														 CostoTotalHospital, SerieDocumentoOrigen, DocumentoOrigen, EmpresaFacReq, SerieFactura, Factura, FechaTransaccion, Debe, Haber, BodegaFuente, 
														 BodegaDestino, Departamento,EmpresaReal,LineaDocumento)
		Select  @w_EmpresaRealOrden, 'OR' as Tipo, 'A' as SerieDoc,C.Correlativo  as Doc, Row_Number() Over (Order by C.Empresa, C.Categoria) as Linea ,C.Producto, C.Cantidad,
			 Cast(C.Costo As money) As CostoUnitario,   Cast(C.Costo As Money) * (1+(@w_PorcGanancia /100.000000)) As CostoUnitarioHospital, C.Categoria, Cast(C.Costo As money) * Cantidad   As CostoTotal,
			 (Cast(C.Costo As money) * C.Cantidad *  (1+(@w_PorcGanancia /100.000000))) As CostoTotalHospital, C.Tipoorden, C.Orden,Null as EmpresaFacReq,Null as SerieFactura , Null as Factura,  GetDate() as FechaTransaccion ,
				@w_CompraFutura As Debe, @w_VenaPaciente As Haber,NULL as BodegaFuente,ISNULL(x.BodegaDefault, 99) as BodegaDestino, Null as Departamento,@w_EmpresaRealOrden, C.linea
		From Hospital..CargosAnuladosDetalle C
		Inner Join Inventario..Productos P On (C.Empresa = P.Empresa and C.Producto=P.Codigo and P.Tipo = 'P')
		Inner Join Hospital..OrdenesTipos x ON (c.Empresa = x.Empresa and c.TipoOrden = x.Codigo)
		Inner Join Inventario..Bodegas B ON (B.Empresa = @i_EmpresaBodega  and  B.Codigo = ISNULL(x.BodegaDefault, 99))
		Where C.Empresa = @i_Empresa AND C.Tipoorden = @i_TipoOrden AND C.ORDEN = @i_CodigoOrden

		-- Fin insertando contabilidad 

		--***********************************************************************************************
		-- =========================================================================================
		-- Walter Ronquillo
		-- Función que permite modificar el estado de los cargos.
		-- Tipo 1 (Cargos)
		-- =========================================================================================

			exec @w_SalidaSp = Hospital..sp_Ajenos_Honorarios_Editar_Interno  @I_Tipo=1 ,@I_Empresa=@i_Empresa,
																																@I_TipoOrden=@i_TipoOrden,
																																@I_Orden=@i_CodigoOrden,@I_Linea=0,
																																@I_Campo='Estado_Cargo',@I_Valor='E'

			exec @w_SalidaSp = Hospital..sp_Ajenos_Honorarios_Editar_Interno  @I_Tipo=1 ,@I_Empresa=@i_Empresa,
																																@I_TipoOrden=@i_TipoOrden,
																																@I_Orden=@i_CodigoOrden,@I_Linea=0,
																																@I_Campo='Estado_Interpretacion',@I_Valor='E'
		
			--Actualizar Ordenes a status A y eliminar Cargos
			Update Ordenes
			SET Status = 'A' ---, StatusEstadisticas = 'A'
			Where Empresa = @i_Empresa and Tipo = @i_TipoOrden and Codigo = @i_CodigoOrden

			Delete from Cargos
			Where Empresa = @i_Empresa and TipoOrden = @i_TipoOrden and Orden = @i_CodigoOrden

			Insert Into Anulaciones (Empresa, Serie, Numero, Tipo, Fecha, Usuario, Razon,
															 SerieAdmision, Admision,EmpresaReal)
			Values(@i_Empresa, @i_TipoOrden, @i_CodigoOrden, 'O', GetDate(), @i_corporativo, 
					   @i_Razon, @serieAdmision, @numeroAdmision, @w_EmpresaRealOrden)

			Select 0 AS "codigo", concat('Se generó Anulación: ',@i_TipoOrden, ' - ', @i_CodigoOrden) as "descripcion", 
						 0 as tipo_error

			COMMIT TRAN
		END
		ELSE IF (@IOpcion = 'D')
		BEGIN
			-- Los productos de devolucion si se graban en la tabla Inventario..InvCargosDetalle
			-- Crea Listado de productos y servicios, almacena temporalmente #Productos
			SELECT ROW_NUMBER() OVER(ORDER BY a.productoservicio ASC) AS keyCount,
					   a.productoservicio, a.cantidad
			INTO #PRODUCTOS_DEVOLUCION
			FROM
				(
				select a.item productoservicio, sum(cast(b.item as float)) as Cantidad
				from  Contadb..SplitStringIndex(@i_CodigoProductos,',') a
				inner join  Contadb..SplitStringIndex(@i_CantidadProductos,',') b on (a.keyCount = b.keyCount)
				group by a.item
				) a;

		END
	END TRY
	BEGIN CATCH
		ROLLBACK TRAN;
		Set @w_ErrRaise = ERROR_MESSAGE()
		Set @w_ErrRaiseNum = ERROR_STATE()
		RAISERROR(@w_ErrRaise,16,@w_ErrRaiseNum)
	END CATCH

END
GO
/************************************************************************************************************
 *************************************spInsertaCargos********************************************************
 ************************************************************************************************************/
USE [HOSPITAL]
GO

/****** Object:  StoredProcedure [dbo].[spInsertaCargos]    Script Date: 06/09/2023 15:23:47 p.m. ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



-- **************************************************************************************************************************************************
CREATE PROCEDURE [dbo].[spInsertaCargos]
	@i_empresa_unificada	varchar(010) = 'MED',
	@i_empresa_bodega		varchar(010) = 'BOD',
	@i_EmpresaSem			varchar(003) = 'SEM',	-- empresa que vende
	@i_admision_serie		varchar(010),
  @i_admision_codigo		int,
	@i_empresa_real    varchar(3),
	@i_hospital				varchar(010),
	@i_medico_orden				int,
	@i_producto_codigo		varchar(max),			-- {xxx, xxx}
	@i_producto_cantidad	varchar(max),			-- {n, m}
	@i_producto_precio_libre	varchar(max),	-- {n @ m}
	@i_producto_ajeno	varchar(max),					-- {n, m}
  @i_monto_total money,
	@i_nivel_precio			int = 1,
	@i_corporativo			int,	
	@i_orden_tipo			varchar(010)='', -- FAx, C0x, No debe ser necesario ingresarlo si lleva la CategoriaOrden
	@i_TipoOpInv			varchar(010) = '',
	@o_TipoOrden		  varchar(10) = '' OUT ,
	@o_CodigoOrden		int = 0 OUT 
AS
--la bodega se obtiene desde ordenes tipo para no provocar reduccion de inventario en una bodega de otro hospital
--@i_bodega				int, 
--------------------------------------------------------------------------------
SET NOCOUNT ON

/*****************************************
Fecha:	2023.01.03
Autor:	Javier Eduardo Castillo
desc.:	procedimiento para insertar Ordenes 
		    y Cargos de una admisión especifica para ortopedia.
*******************************************/
/*****************************************
Modificacion
Fecha:	2023.08.04
Autor:	Javier Eduardo Castillo
desc.:	. Busqueda de tipo de orden sin validar hospital para tipos de orden 'AJE','BAN','CAR','PAQ'
				. Unificacion de tablas temporales en tabla #Productos
				. Validación de categorias de productos validas segun el campo categorias en ordenestipos, si es nulo puede cargar cualquier categoria
				. Validación de categorias para cargar el campo ajeno (30,99,97,98)
				. Validacion para admisiones de seguro de producto en la tabla ProductosNoCubiertos para cambio a categoria 30
				. Validacion de medico para cargos de ajeno sp_Ajenos_FiltroCargos_Validar
				. Ejecucion de sp sp_Ajenos_Honorarios_CargosNuevo para ingreso de honorarios en las categorias (30,99,97,98)
*******************************************/

declare @inventarioCount	int=0
declare @OrdenNumero		int=0
declare @CodigoBodega int=0
declare @FactorCosto		float=0.0
declare @NivelPrecioOrtogua	float=0.0;
declare @SucursalHosp	varchar(10)='';
declare @OrdenTipo		varchar(10)='';
declare @EmpresaReal	varchar(003)='';
declare @CodigosProductos varchar(max)='';
declare @LimiteSiniestralidadValido bit;
declare @TieneSiniestralidad bit;
declare @AplicaMontoMaximoPendiente bit = 0;
declare @MontoMaximoPendienteValido bit = 0;

BEGIN TRAN
	BEGIN TRY
  	    
		DECLARE
		@w_ErrRaise		Varchar(MAX)	= '',
		@w_ErrRaiseNum 	INT			= 0,
		@w_fecha datetime = getdate(),
		@w_categorias_validas  Varchar(254) = '',
		@lineaMenorProducto int = 0,
		@w_AjenoValido int = 0,
		@w_medicoAjeno int = null,
		@w_categoriaCargo int = null,
		@w_codigoProducto int = null,
		@w_valorCargo money = null,
		@w_paciente int = null,
		@w_CategoriaAjenos char(2) = '',
		@w_CategoriaAjenosExtra char(2)= '',
		@w_CategoriaExtraordinarios char(2) = '',
		@w_ServiciosMedicos char(2)= '',
		@w_PrecioLibre money,
		@w_contador int = 0,
		@w_sucursalOrden int = 0,
		@w_asegura char(3);
	

		IF @i_orden_tipo <> ''
		BEGIN
			SET @OrdenTipo = @i_orden_tipo

			SELECT @CodigoBodega = ISNULL(BodegaDefault,''), @w_categorias_validas = ISNULL(OTipo.Categorias,'')
				FROM hospital..OrdenesTipos OTipo (nolock) inner join inventario..bodegas Bod (nolock) on (Bod.Codigo = OTIPO.BodegaDefault)
			 WHERE OTIPO.Empresa = @i_empresa_unificada AND 
						 (OTIPO.Hospital = @i_hospital or @OrdenTipo in ('AJE','BAN','CAR','PAQ')) AND
			   		 OTIPO.Activo = 'S' AND OTIPO.Codigo = @OrdenTipo AND Bod.Empresa = @i_empresa_bodega AND
			  		 Bod.Activa = 'S' 
			IF @CodigoBodega = ''
			BEGIN
				ROLLBACK TRAN
				Select 0 AS "codigo", 
							'No se encontro el tipo de orden '+@i_orden_tipo+' para el código de hospital '+@i_hospital as "descripcion", 
							 1 as tipo_error
				RETURN
			END
		END
		ELSE
		BEGIN 
			BEGIN
				ROLLBACK TRAN
				Select 0 AS "codigo", 
							'No se envio el tipo de orden para realizar el cargo.' as "descripcion", 
							 1 as tipo_error
				RETURN
			END
		END

		exec spObtenerEmpresaReal @IEmpresaUnificadora = @i_empresa_unificada, @IEmpresaBodega = @i_empresa_bodega, 
															@IEmpresaReal = @i_empresa_real, @ICodigoOrden = @OrdenTipo, 
															@ISerieAdmision = @i_admision_serie,@ICodigoAdmision = @i_admision_codigo, 
															@OEmpresaReal = @EmpresaReal out
		
		exec @TieneSiniestralidad =  FnCalculaSiniestralidad @i_EmpresaUnificadora = @i_empresa_unificada ,																
																												 @i_Admision= @i_admision_codigo, @i_Serie = @i_admision_serie

		IF @TieneSiniestralidad = 1 
		BEGIN
			exec SpMontoSiniestralidadValido @i_EmpresaUnificadora=@i_empresa_unificada, 
																		   @i_Admision= @i_admision_codigo, @i_Serie = @i_admision_serie,
																			 @i_MontoTotal = @i_monto_total, @w_tieneSiniestralidad = @LimiteSiniestralidadValido out
			IF @LimiteSiniestralidadValido = 0 
			BEGIN
				ROLLBACK TRAN
				Select 0 AS "codigo", 
							'El Paciente llego a su *** Máximo Beneficio Actual ***' as "descripcion", 
							 1 as tipo_error
				RETURN
			END
		END

		exec @AplicaMontoMaximoPendiente = FnAplicaMontoMaximoPendiente @i_EmpresaUnificadora = @i_empresa_unificada ,																
																												 @i_Admision= @i_admision_codigo, @i_Serie = @i_admision_serie

		IF @AplicaMontoMaximoPendiente = 1 
		BEGIN
			set @MontoMaximoPendienteValido = 1
			exec SpMontoMaximoPendienteValido @i_EmpresaUnificadora=@i_empresa_unificada, 
																														  @i_Admision= @i_admision_codigo, @i_Serie = @i_admision_serie,
																															@i_MontoTotal = @i_monto_total,
																															@o_MontoValido = @MontoMaximoPendienteValido out
			IF @MontoMaximoPendienteValido = 0 
			BEGIN
				ROLLBACK TRAN
				Select 0 AS "codigo", 
							'El Paciente rebasa la política del *** Monto Adeudado ***' as "descripcion", 
							 1 as tipo_error
				RETURN
			END
		END

		
		-- Crea la orden, almacena temporalmente #Ordenes
		SELECT @i_empresa_unificada Empresa, @OrdenTipo Tipo, d.Siguiente Codigo, @w_fecha Fecha, @CodigoBodega Bodega, a.nivelPrecios,
					 @EmpresaReal EmpresaReal, a.Serie SerieAdmision, a.Codigo Admision, @i_corporativo Usuario, 'P' Status, a.Habitacion as Habitacion, 
					 'P' StatusEstadisticas, 'N' Inhabiles, B.Area AreaServicio, @i_empresa_unificada EmpresaUnificadora, 
					 a.Paciente Paciente, cast(e.CodigoBase as int) Sucursal, d.tipo TipoCorrelativo,
					 s.Asegura, a.Seguro, 1 + (isnull(c.PorcGanancia,10.0)/100)  factorcosto
			INTO #ORDENES
		  FROM Hospital.dbo.Admisiones a
			Left  join HOSPITAL.dbo.HABITACIONES b (nolock) on (b.empresa = a.empresa and b.codigo = a.Habitacion)
			Left  join HOSPITAL.dbo.CORRELORDENES d on (a.Empresa = d.Empresa and d.Tipo = @OrdenTipo) 
			Inner join HOSPITAL.dbo.ORDENESTIPOS o (nolock) on (d.Empresa = o.Empresa and d.Tipo = o.Codigo)
			Left  join NOMINADB.dbo.EmpHospital	 e (nolock) on (e.Empresa = a.Empresa and e.codigo = b.base)
			Left  join Hospital.dbo.Seguros s (nolock) on (s.empresa = a.empresa and s.codigo = a.seguro)
			Left  join Hospital.dbo.Contabilidad c (nolock) on (c.Empresa = @EmpresaReal)
			WHERE	a.EMPRESA = @i_empresa_unificada
				AND a.SERIE = @i_admision_serie
				AND a.CODIGO = @i_admision_codigo	

		-- Obtiene factor de costo de hospital y aseguradora 
		set @w_asegura = (select top 1 Asegura  from #ORDENES)
		set @FactorCosto = (select top 1 factorcosto  from #ORDENES)
		set @OrdenNumero = (select top 1 Codigo from #ORDENES)
		set @w_paciente = (select top 1 Paciente from #ORDENES)
		set @w_sucursalOrden = (select top 1 Sucursal from #ORDENES)

		-- Crea Listado de productos y servicios, almacena temporalmente #Productos
	  select a.keyCount linea, a.item productoservicio, cast(b.item as float) as cantidad, 
						cast(isnull(c.item,0) as money) as preciolibre, 						
						case when datalength(d.item) = 0 then null else d.item end as ajeno,
						m.tipo, 
						case when nc.producto is not null then '30' else m.categoria end as categoria, 
						m.costopromedio, m.costoultimo, m.costoalto, m.Costeo,
						q.precio, q.UnidadMedida, q.subNivel, nc.producto, 
						case when aje.TipoCortesiaCasa is null then null when aje.TipoCortesiaCasa = 0 then 'M' else 'A' end TipoMed
			into #PRODUCTOS
			from Contadb..SplitStringIndex(@i_producto_codigo,',') a 
						inner join  
					 Contadb..SplitStringIndex(@i_producto_cantidad,',') b on (a.keyCount = b.keyCount)
						inner join
					 Contadb..SplitStringIndex(@i_producto_precio_libre,'@') c on (a.keyCount = c.keyCount)
						inner join
					 Contadb..SplitStringIndex(@i_producto_ajeno,',') d on (a.keyCount = d.keyCount)
						left join
					 INVENTARIO..productos m (nolock) on (m.Empresa = @i_empresa_unificada and m.codigo = a.item and m.activo = 'S')		 
						left join
					 INVENTARIO..productosprecios q (nolock) on (q.empresa = @i_empresa_unificada and q.producto = a.item and q.nivel = @i_nivel_precio)
					  left join 
					 INVENTARIO..ProductosNoCubiertos nc (nolock) on (nc.producto = a.item and nc.aseguradora = @w_asegura)
					  left join 
					 Hospital..Ajenos aje (nolock) on (m.Empresa = aje.Empresa and aje.Codigo = (case when datalength(d.item) = 0 then null else d.item end) )

		select @CodigosProductos = @CodigosProductos +','+ ltrim(rtrim(productoservicio))			
			from #PRODUCTOS p 
		 where p.tipo is null

		if @@RowCount <> 0
		BEGIN			
			set @CodigosProductos = STUFF(@CodigosProductos,1,1,'')
						
			Select 0 AS "codigo", 
						'No estan activos los productos con codigo: '+@CodigosProductos as descripcion, 
						1 as tipo_error
			ROLLBACK TRAN
			RETURN
		END

		--Validar Categorias de productos a cargar en la orden
		IF @w_categorias_validas <> '' 
		BEGIN
				select c.keyCount noFila, c.Item categoria
				into #CategoriasValidas
				from  Contadb..SplitStringIndex(@w_categorias_validas,',') c

				select productoservicio codigo, Cantidad, categoria
					into #ProductosCategorias
					from #PRODUCTOS 
				where categoria not in (select categoria from #CategoriasValidas)

				IF  @@RowCount <> 0
				BEGIN
				 Select 0 AS "codigo", 
								concat('No se puede cargar el producto con codigo ',(select top 1 codigo from #ProductosCategorias order by codigo),
											 ' por que la categoria ',
											 ' (',(select top 1 categoria from #ProductosCategorias order by codigo),
											 ') no es valida para el tipo de orden ',@OrdenTipo) descripcion, 
								1 as tipo_error
					ROLLBACK TRAN
					RETURN
				END
		END



		-- Valida la existencia de los productos, almacena temporalmente en #Inventario
		Select a.categoria, a.Tipo, 'Sin Existencias' Descripcion, c.Producto, 
					 a.Nombre, c.Bodega, c.Existencia, b.Cantidad, b.preciolibre, b.ajeno
			INTO #INVENTARIO
			From INVENTARIO.dbo.productos a (nolock)	inner join 
					 #PRODUCTOS b on (a.Codigo = b.productoservicio and a.Empresa = @i_empresa_bodega)	inner join 
					 INVENTARIO.dbo.ExistenciasBodega c (nolock) on (c.Producto = a.Codigo and c.Empresa = @i_empresa_bodega and c.Bodega = @CodigoBodega)
		 where c.Existencia - b.Cantidad < 0 and 
					 a.Tipo = 'P' --PRODUCTO Y NO SERVICIO

		select @inventarioCount = count(*) from #INVENTARIO;

		IF (@inventarioCount <> 0) 
		BEGIN
				ROLLBACK TRAN
				Select 0 AS "codigo", 
							'Faltan existencias de productos para realizar el cargo' as "descripcion", 
							 1 as tipo_error
				RETURN
		END

		IF OBJECT_ID('tempdb..#INVENTARIO') is not null
			drop table #INVENTARIO

		IF LEFT(@OrdenTipo,2) = 'OR'
		BEGIN				
				SELECT @NivelPrecioOrtogua = ISNULL(1+(convert(float,PorcentajeIncremento)/100),0)
					FROM Hospital..InvNivelPreciosOrtopedia (nolock)
				 WHERE NivelPrecio = (select top 1 nivelPrecios from #ORDENES)
					 and Activo = 'S'

				IF @NivelPrecioOrtogua = 0 
				BEGIN					
					Select 0 AS "codigo", 
								'No se ha configurado el nivel de precios: '+convert(varchar,(select top 1 nivelPrecios from #ORDENES)) as "descripcion", 
								 1 as tipo_error
					ROLLBACK TRAN
					RETURN
				END
		END
	

		
		-- Valida que solo las categorias permitidas tenga asignados ajenos
		Select @w_CategoriaAjenos=CategoriaAjenos, @w_CategoriaAjenosExtra=CategoriaAjenosExtra,
					 @w_ServiciosMedicos=ServiciosMedicos, @w_CategoriaExtraordinarios = CategoriaExtraordinarios
			from Defaults
		 where Empresa = @i_empresa_unificada		

		set @CodigosProductos = ''		  
		select @CodigosProductos = @CodigosProductos +','+ ltrim(rtrim(ProductoServicio)) 			
			from #PRODUCTOS 
		 where ajeno is not null and
		       categoria not in ( isnull(@w_ServiciosMedicos,'97'),isnull(@w_CategoriaAjenosExtra,'98'),
															isnull(@w_CategoriaAjenos,'99'), isnull(@w_CategoriaExtraordinarios,'30') )

		if @@rowcount > 0
		begin
			set @CodigosProductos = STUFF(@CodigosProductos,1,1,'')
			SELECT 0 AS codigo, 
					CONCAT('Las cargos con codigo: ',@CodigosProductos, ' ,no pueden ser asignados a un ajeno.') AS descripcion, 
					1 AS tipo_error;	
			ROLLBACK TRAN
			RETURN
		end

		-- Valida que al medico se le pueda asignar el cargo
		IF @i_orden_tipo = 'AJE'
		BEGIN 			

  		select @lineaMenorProducto	= min(linea) from #PRODUCTOS

			while @lineaMenorProducto <> 0
			begin
			
				CREATE TABLE #FiltroAjeno (Existencias int)
				
				select @w_medicoAjeno = ajeno, @w_codigoProducto = productoservicio, @w_categoriaCargo = categoria
					from #PRODUCTOS 
				 where linea = @lineaMenorProducto

				IF isnull(@w_ServiciosMedicos,'97') = @w_categoriaCargo or
				   isnull(@w_CategoriaAjenosExtra,'98') = @w_categoriaCargo or
					 isnull(@w_CategoriaAjenos,'99') = @w_categoriaCargo or
					 isnull(@w_CategoriaExtraordinarios,'30') = @w_categoriaCargo
				BEGIN
					if @w_medicoAjeno = NULL or @w_medicoAjeno = 0
					BEGIN
						SELECT 0 AS codigo, 
									 CONCAT('Debe asignar un médico para el cargo con código ',@w_codigoProducto) AS descripcion, 
									 1 AS tipo_error;	
						ROLLBACK TRAN
						RETURN
					END

					INSERT INTO #FiltroAjeno
								 EXEC sp_Ajenos_FiltroCargos_Validar 
											@i_Empresa = @i_empresa_unificada, 
											@i_Medico = @w_medicoAjeno, 
											@i_SerieAdmision = @i_admision_serie,
											@i_Admision = @i_admision_codigo,
											@i_CategoriaCargo = @w_categoriaCargo
			
					IF @@ROWCOUNT > 0	
						SELECT TOP 1 @w_AjenoValido = Existencias  FROM #FiltroAjeno
			 
					IF OBJECT_ID('tempdb..#FiltroAjeno') IS NOT NULL
						DROP TABLE #FiltroAjeno
					
					IF @w_AjenoValido = 0 
					BEGIN 					
						SELECT 0 AS codigo, 
									 CONCAT('No es posible asignar el médico ',@w_medicoAjeno,' al cargo ',@w_codigoProducto,'. Comuníquese con su jefatura') AS descripcion, 
									 1 AS tipo_error;	
						ROLLBACK TRAN
						RETURN
					END
				END
				ELSE
				BEGIN
						SELECT 0 AS codigo, 
									 CONCAT('La categoría ',@w_categoriaCargo, ' del producto ',@w_codigoProducto, ' es inválida para una orden de tipo Ajena') AS descripcion, 
									 1 AS tipo_error;	
						ROLLBACK TRAN
						RETURN
				END

				SET @w_medicoAjeno = NULL

				select @lineaMenorProducto	= min(linea) 
					from #PRODUCTOS 
	 			 where linea > @lineaMenorProducto
			end
		END

		--Valida que el nivel de precio en productosprecio sea 0 para los productos con precio ingresado por el usuario
		 set @CodigosProductos = ''
		 select @CodigosProductos = @CodigosProductos +','+ ltrim(rtrim(ProductoServicio)) 
			 from #PRODUCTOS
			where preciolibre > 0 and
						precio is not null and
						precio > 0

		if @@rowcount > 0
		begin
			set @CodigosProductos = STUFF(@CodigosProductos,1,1,'')
			SELECT 0 AS codigo, 
						 CONCAT('No puede ingresar el precio de estos productos ',@CodigosProductos,', el sistema lo calculara automaticamente.') AS descripcion, 
						 1 AS tipo_error;	
			ROLLBACK TRAN
			RETURN
		end

			--Inserta la orden
			Insert into
				HOSPITAL..Ordenes (Empresa, Tipo, Codigo, Fecha, Bodega, Medico, SerieAdmision, Admision,
				Usuario, Status, Habitacion, StatusEstadisticas, Inhabiles, AreaServicio, EmpresaReal, EmpresaUnif)
				
			Select
				Empresa, Tipo, Codigo, Fecha, Bodega, 1, SerieAdmision, Admision,
				Usuario, Status, Habitacion, StatusEstadisticas, Inhabiles, AreaServicio, EmpresaReal , EmpresaUnificadora
			From #ORDENES;
			
			--Inserta los cargos (detalle de la orden)
			Insert into
				HOSPITAL..Cargos (Empresa, TipoOrden, Orden, 
				Linea, Fecha, Producto, Categoria, 
				Valor,
			  Cantidad, Status, PrecioUnitario, 
				UnidadMedida, NumeroSerie, Ajeno, 
				Costo, Factor, SubNivelPrecios, 
				SerieAdmision, Admision, Paciente, Sucursal, EmpresaReal, EmpresaUnif, CostoHospital,TipoMed)

			Select (select	top 1 EmpresaUnificadora from #ORDENES), (select top 1 Tipo from #ORDENES), (select	top 1 Codigo from #ORDENES),
				cast(p.linea as tinyint), @w_fecha, p.productoservicio, p.categoria, 
			  cast( case when LEFT(@OrdenTipo,2) = 'OR' THEN  (p.Cantidad *  Case WHEN p.preciolibre > 0 then round(preciolibre,2) else round(IsNull(Precio,Case When Costeo = 'U' Then CostoUltimo  Else CostoPromedio END) * @NivelPrecioOrtogua,2) end ) 
							else (Cantidad * Case WHEN preciolibre > 0 then round(preciolibre,2) else round(IsNull(Precio,Case When Costeo = 'U' Then CostoUltimo Else CostoPromedio END),2)end) end as money),
				cast(Cantidad as smallmoney), 'P' status, 
				cast( case WHEN preciolibre > 0 then round(preciolibre,2)  
				      when LEFT(@OrdenTipo,2) = 'OR' THEN  (round(IsNull(Precio,Case When Costeo = 'U' Then CostoUltimo  Else CostoPromedio END) * @NivelPrecioOrtogua,2))
							else (round(IsNull(Precio,Case When Costeo = 'U' Then CostoUltimo Else CostoPromedio END),2)) end as money) PrecioUnitario
				, UnidadMedida, NULL, ajeno,
				cast(case when Costeo = 'U' then CostoUltimo else CostoPromedio end AS smallmoney), 1, ISNULL(SubNivel,1),
				(select	top 1 serieAdmision from #ORDENES), (select	top 1 Admision from	#ORDENES), (select	top 1 Paciente from	#ORDENES),
				(select top 1 Sucursal from #ORDENES), (select	top 1 EmpresaReal from #ORDENES), (select	top 1 EmpresaUnificadora from #ORDENES),
				cast(IsNull(Case When Costeo = 'U' Then CostoUltimo Else CostoPromedio END * @FactorCosto,0) as smallmoney),
				TipoMed
			From #PRODUCTOS p
			Order by linea;

		select @lineaMenorProducto	= min(linea) from #PRODUCTOS

		while @lineaMenorProducto <> 0
			begin		
			 set @w_categoriaCargo =  (select categoria from #PRODUCTOS where linea = @lineaMenorProducto)	

			 IF isnull(@w_ServiciosMedicos,'97') = @w_categoriaCargo or
				  isnull(@w_CategoriaAjenosExtra,'98') = @w_categoriaCargo or
					isnull(@w_CategoriaAjenos,'99') = @w_categoriaCargo or
					isnull(@w_CategoriaExtraordinarios,'30') = @w_categoriaCargo
			 begin
				CREATE TABLE #Respuesta (codigo int, descripcion varchar(100) ,tipo_error int)

			  select @w_medicoAjeno = ajeno ,
							 @w_codigoProducto = productoservicio,	
							 @w_valorCargo = cast( case when LEFT(@OrdenTipo,2) = 'OR' THEN  (p.Cantidad *  Case WHEN p.preciolibre > 0 then round(preciolibre,2) else round(IsNull(Precio,Case When Costeo = 'U' Then CostoUltimo  Else CostoPromedio END) * @NivelPrecioOrtogua,2) end ) 
							               else (Cantidad * Case WHEN preciolibre > 0 then round(preciolibre,2) else round(IsNull(Precio,Case When Costeo = 'U' Then CostoUltimo Else CostoPromedio END),2)end) end as money)				
				  from #PRODUCTOS p
				 where linea = @lineaMenorProducto	
					
					insert into #Respuesta
          exec Hospital..sp_Ajenos_Honorarios_CargosNuevo
								@i_EmpresaUnificadora = @i_empresa_unificada,
								@i_EmpresaReal = @i_empresa_real,
								@i_Sucursal = @w_sucursalOrden,
								@i_TipoOrden = @i_orden_tipo,
								@i_Orden = @OrdenNumero,
								@i_Linea = @lineaMenorProducto,
								@i_SerieAdmision = @i_admision_serie,
								@i_Admision = @i_admision_codigo,
								@i_Fecha = @w_fecha,
								@i_Producto = @w_codigoProducto,
								@i_Categoria = @w_categoriaCargo,
								@i_Valor = @w_valorCargo,
								@i_Ajeno = @w_medicoAjeno,
								@i_Paciente = @w_paciente

				 IF OBJECT_ID('tempdb..#Respuesta') IS NOT NULL
						DROP TABLE #Respuesta
			 end				

				select @lineaMenorProducto	= min(linea) 
					from #PRODUCTOS 
	 			 where linea > @lineaMenorProducto
			end				

			--Rebaja de inventario
			Update a
				SET a.Existencia  = a.Existencia - b.Cantidad
			From INVENTARIO..ExistenciasBodega a
			inner join #PRODUCTOS b on
				a.Producto = b.productoservicio
				and a.Empresa = @i_empresa_bodega
				and a.Bodega = @CodigoBodega;
							
			-->Actualizando sumatoria
			update HOSPITAL..CorrelOrdenes set Siguiente = siguiente + 1 where tipo = @OrdenTipo and Empresa = @i_empresa_unificada;

			--Inserta lineas de InvCargosDetalle de Hospital y SEM
			INSERT INTO INVENTARIO.dbo.InvCargosDetalle
				(Empresa, TipoOperacion, SerieDocumento, Documento, CostoTotal, CostoTotalHospital, Linea, Producto, Cantidad,  
				CostoUnitario, CostoUnitarioHospital, Categoria, FechaTransaccion, Debe, Haber, BodegaFuente, EmpresaReal,LineaDocumento)

			Select  @EmpresaReal Empresa, @i_TipoOpInv TipoOperacion, (select top 1 Tipo from #ORDENES) as SerieDocumento, (select	top 1 Codigo from #ORDENES)  as Documento, 
				(cast(c.Costo as Money) * c.Cantidad) as CostoTotal,  (cast(c.CostoHospital as Money)* c.Cantidad) as CostoTotalHospital,  
				Row_Number() Over (Order by c.Empresa, c.Categoria, c.TipoOrden, c.Orden) AS Linea, c.Producto, c.Cantidad, 
				c.Costo as CostoUnitario, c.CostoHospital as CostoUnitarioHospital, c.Categoria, @w_fecha as FechaTransaccion, 
				k.VenaPaciente as Debe, k.CompraFutura as Haber, @CodigoBodega as BodegaFuente, @EmpresaReal as EmpresaReal, c.Linea
			From Hospital..Cargos c (nolock)
			Inner Join Inventario..Productos p  (nolock) on (p.empresa = c.empresa and p.codigo = c.producto and p.Tipo = 'P')
			left  join Hospital..Contabilidad k (nolock) on (k.empresa = @EmpresaReal)
			Where c.Empresa = @i_empresa_unificada
			and c.Tipoorden = (select top 1 Tipo from #ORDENES)
			and c.Orden		= (select	top 1 Codigo from #ORDENES)

			UNION

			Select  @i_EmpresaSem as Empresa, @i_TipoOpInv as TipoOperacion, (select top 1 Tipo from #ORDENES) as SerieDocumento, (select	top 1 Codigo from #ORDENES) as Documento, 
				(cast(c.Costo as Money) * c.Cantidad) as CostoTotal,  (cast(c.CostoHospital as Money)* c.Cantidad) as CostoTotalHospital,  
				Row_Number() Over (Order by c.Empresa, c.Categoria, c.TipoOrden, c.Orden) AS Linea, c.Producto, c.Cantidad, 
				c.Costo as CostoUnitario, c.CostoHospital as CostoUnitarioHospital, c.Categoria, @w_fecha as FechaTransaccion, 
				 rTrim(k.CuentasCostoSuministro) as Debe,  rtrim(b.CuentaLocal) as Haber, @CodigoBodega as BodegaFuente, @EmpresaReal as EmpresaReal, c.Linea
			From Hospital..Cargos c (nolock)
			Inner Join Inventario..Bodegas	 b (nolock) on (b.empresa = @i_empresa_bodega  and  b.codigo = @CodigoBodega)
			Inner Join Inventario..Productos p  (nolock) on (p.empresa = b.empresa and p.codigo = c.producto and p.Tipo = 'P')
			inner Join Inventario..CategoriaCuentasCostos k (nolock) on  (k.Empresa = @i_EmpresaSem  AND k.Categoria = p.categoria and k.hospital = b.hospital and k.TipoBodega = b.TipoBodegaContable)
			Where c.Empresa = @i_empresa_unificada
			and c.Tipoorden = (select top 1 Tipo from #ORDENES)
			and c.Orden		= (select	top 1 Codigo from #ORDENES)
			
			set @o_TipoOrden = @OrdenTipo 
			set @o_CodigoOrden = @OrdenNumero

			--Aviso de orden generada
			Select 0 AS "codigo", concat('Se generó Orden: ',@OrdenTipo, ' - ', @OrdenNumero) as "descripcion", 0 as tipo_error;
			--Select 0 AS "codigo", 'Orden ingresada exitosamente' as "descripcion", 0 as tipo_error;

			--Aplica los cambios
			COMMIT TRAN;
	END TRY
	BEGIN CATCH
		--revierte cambios y envia alerta
		ROLLBACK TRAN;
		Set @w_ErrRaise = ERROR_MESSAGE()
		Set @w_ErrRaiseNum = ERROR_STATE()
		RAISERROR(@w_ErrRaise,16,@w_ErrRaiseNum)
	END CATCH;
GO

/************************************************************************************************************
 ***********************************spBusquedaAdmisiones2****************************************************
 ************************************************************************************************************/
USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[spBusquedaAdmisiones2]    Script Date: 21/02/2023 12:03:41 p.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


-- ==========================================================
-- Author:  Javier Castillo
-- Create date: 2023.01.10
-- Description:	Busqueda de admisiones se cambio el filtro
-- ==========================================================
CREATE PROCEDURE [dbo].[spBusquedaAdmisiones2]
	@i_Admision			varchar(012) = null,
	@i_Habitacion		varchar(005) = null,
	@i_Paciente			varchar(200) = null,
	@i_Activa			int = 1,		-- Si la admision se encuentra activa
	@i_ConHabitacion	int = 0,		-- Si la admisión posee habilitación 
	@i_EmpresaUnifica	varchar(3) = 'MED',
	@i_EmpresaReal		varchar(3)

AS
BEGIN
	SET NOCOUNT ON;

	select
		top 100 a.Serie,
		a.Codigo,
		a.Entrada,
		a.Salida,
		a.NivelPrecios,
		a.Habitacion,
		rtrim(concat(rtrim(p.Nombre), ' ', rtrim(p.Apellido), ' ', p.ApellidoCasada )) Paciente,
		rtrim(concat(rtrim(a2.Nombre), ' ', rtrim(a2.Apellido))) Medico, concat(rtrim(a.Serie),'-',a.Codigo) as Admision 
	from
		Admisiones a
	LEFT JOIN Pacientes p ON
		a.Paciente = p.Codigo
		AND a.Empresa = p.Empresa
	LEFT JOIN Ajenos a2 ON
		a.Medico = a2.Codigo
		and a.Empresa = a2.Empresa
	WHERE a.Empresa = @i_EmpresaUnifica
		and (@i_Admision is null or (concat(rtrim(a.Serie),'-',a.Codigo) like '%'+@i_Admision+'%' ))
		and (@i_Paciente is null or (concat(rtrim(p.nombre),' ', rtrim(p.Apellido)) like '%'+@i_Paciente+'%'))
		and (@i_Activa = 0 or (a.Salida is null and a.FechaPreEgreso is null))
		and (@i_ConHabitacion = 0 or (a.Habitacion is not null AND replace(a.Habitacion,' ','') != '' ))
		and (@i_Habitacion is null or a.Habitacion like '%'+@i_Habitacion+'%')
		and a.EmpresaReal  = @i_EmpresaReal 
	order by
		a.Entrada desc
	
END
GO
/************************************************************************************************************
 ************************************spOpcionesProformaOrtopedia*********************************************
 ************************************************************************************************************/
USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[spOpcionesProformaOrtopedia]    Script Date: 21/02/2023 12:04:42 p.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[spOpcionesProformaOrtopedia]
		(@IOpcion INT = 1 , 
		 @ISubOpcion char(1) = 'A',
		 @IReporte varchar(50) = NULL,
		 @i_Id INT = null,
		 @i_Empresa char(3) = 'MED',
		 @i_NivelPrecio INT = 0,		 
		 @i_Descripcion varchar(50) = null,
		 @i_PorcentajeIncremento int = null,
		 @i_Estatus char(1) = 'A',
		 @i_Corporativo int = null,
		 @i_FechaInicio varchar(15) = null,
		 @i_FechaFin varchar(15) = null,
		 @i_EmpresaProveedor char(3) = 'MED',
		 @i_CodigoProveedor char(13) = null,
		 @i_IdFuncionalidad char(13) = null,
		 @i_Bitacora varchar(max) = null		 
		) AS
SET NOCOUNT ON
DECLARE @w_IdProforma int;
DECLARE @w_PorcentajeIncremento int;
BEGIN
BEGIN TRAN
	BEGIN TRY
	
	DECLARE	@w_ErrRaise		Varchar(MAX)	= '';
	DECLARE @w_ErrRaiseNum 	INT	= 0;
	DECLARE @w_Total smallmoney = 0;
	DECLARE @w_FechaInicioConvert DATETIME = CASE WHEN @i_FechaInicio = NULL THEN NULL ELSE CONVERT(DATETIME,@i_FechaInicio,103) END;
	DECLARE @w_FechaFinConvert DATETIME = CASE WHEN @i_FechaFin = NULL THEN NULL ELSE CONVERT(DATETIME,@i_FechaFin,103) END;

	if @IOpcion = 1 
	begin
		select @w_IdProforma = IsNull(max(IdProforma)+1,1) from Hospital..HisProformasOrtopedia
		select @w_IdProforma as idProforma
	end	
	else if @IOpcion = 2 
	begin		
		select @w_PorcentajeIncremento = PorcentajeIncremento 
		  from Hospital..InvNivelPreciosOrtopedia 
     where NivelPrecio = @i_NivelPrecio and Activo = 'S'
		if @@RowCount = 0
		begin
				Select 0 AS "codigo", 
				'Debe configurar el nivel de precio '+RIGHT('00'+convert(varchar(2),@i_NivelPrecio),2)+' de ortopedia para continuar'as "descripcion", 
				1 as tipo_error

				RETURN
		end
		Select Convert(decimal(3,2), 1+Convert(decimal,@w_PorcentajeIncremento)/100.00 ) as PorcentajeIncremento
	end
	else if @IOpcion = 3
	begin
		select top 100 IdProforma CodigoUnico, NumeroProformaProveedor Proforma, Serie, NoAdmision, 
					  			 isnull(LTRIM(RTRIM(p.nombre)),'') Proveedor, FechaRegistro, isnull(TipoOrden,'') TipoOrden, isnull(CodigoOrden,0) CodigoOrden
			from hospital..HisProformasOrtopedia h left join inventario..proveedores p on (h.EmpresaProveedor = p.Empresa and h.CodigoProveedor = p.Codigo)
		where Estado = 'A' and 
					FechaRegistro between DATEADD(dd, 0, DATEDIFF(dd, 0,ISNULL(@w_FechaInicioConvert,CONVERT(DATETIME,'01/01/2023')))) 
														AND  DATEADD(dd, 1, DATEDIFF(dd, 0,ISNULL( @w_FechaFinConvert, (SELECT MAX(FechaRegistro) from HisProformasOrtopedia)))) 
		order by FechaRegistro desc, CodigoUnico desc
	end
	else if @IOpcion = 4
	begin
		select IdNivelPreciosOrtopedia Id, NivelPrecio, Nombre Descripcion, PorcentajeIncremento, Activo Estatus 
			from hospital..InvNivelPreciosOrtopedia
	end
	else if @IOpcion = 5
	begin		
		if @ISubOpcion = 'A'
		begin
			insert into hospital..InvNivelPreciosOrtopedia(Empresa,NivelPrecio,Nombre,PorcentajeIncremento,
																										 Activo,Corporativo,FechaRegistro)
									values(@i_Empresa,@i_NivelPrecio,@i_Descripcion,@i_PorcentajeIncremento,
												 @i_Estatus,@i_Corporativo,GetDate())

			insert into his_bitacora(IdFuncionalidad,EmpresaReal,Detalle,CorporativoCreado,FechaCreado,Tabla)
								values(@i_IdFuncionalidad,@i_Empresa,@i_Bitacora,@i_Corporativo,GetDate(),'hospital.dbo.InvNivelPreciosOrtopedia')
		end
		else if @ISubOpcion = 'M'
		begin
				update hospital..InvNivelPreciosOrtopedia
						set Nombre = @i_descripcion,
								PorcentajeIncremento = @i_PorcentajeIncremento,
								Activo = @i_Estatus,
								FechaModificacion = GetDate(),
								CorporativoModificacion = @i_Corporativo
					where IdNivelPreciosOrtopedia = @i_Id

				insert into his_bitacora(IdFuncionalidad,EmpresaReal,Detalle,CorporativoCreado,FechaCreado,Tabla)
									values(@i_IdFuncionalidad,@i_Empresa,@i_Bitacora,@i_Corporativo,GetDate(),'hospital.dbo.InvNivelPreciosOrtopedia')
		end
		else if @ISubOpcion = 'S'
		begin
			update hospital..InvNivelPreciosOrtopedia
				 set Activo = @i_Estatus,
					   FechaModificacion = GetDate(),
						 CorporativoModificacion = @i_Corporativo
			 where IdNivelPreciosOrtopedia = @i_Id


				insert into his_bitacora(IdFuncionalidad,EmpresaReal,Detalle,CorporativoCreado,FechaCreado,Tabla)
									values(@i_IdFuncionalidad,@i_Empresa,
												concat('Id: ',@i_Id,', NivelPrecio: ',@i_NivelPrecio,', Activo: ',@i_Estatus),
												@i_Corporativo,GetDate(),'hospital.dbo.InvNivelPreciosOrtopedia')
		end
	end
	else if @IOpcion = 6 --Reportes Proformas
	begin
		if @ISubOpcion = 'C'
		begin
			if @IReporte = 'CABECERA'
			begin

				SELECT (select NombreCorto FROM nominadb..emphospital where Codigo = H.hospital AND Empresa = 'MED') Empresa,
							 NumeroProformaProveedor proforma, IdProforma codigoUnico, CONCAT(Serie,'-',NoAdmision) admision,
							 Format(FechaRegistro, 'dd/MM/yyyy HH:mm tt') fechaCreacion, 
							 CONCAT(ISNULL(TipoOrden,''),'-', (CASE WHEN CodigoOrden=0 THEN '' ELSE CodigoOrden END)) Orden, 
							 CASE WHEN OrdenCompra=0 THEN '' ELSE OrdenCompra END OrdenCompra,
							 IsNull((select Nombre from inventario..proveedores
											  where Empresa = H.EmpresaProveedor and Codigo = H.CodigoProveedor),'') proveedor,
							 Corporativo, IsNull((select concat(nombres,' ',apellidos)  from contadb..corporativos 
																		where Corporativo = H.Corporativo),'') NombresCorporativo
					FROM hospital..HisProformasOrtopedia H
				 WHERE IdProforma = @i_Id
			end	
			else if @IReporte = 'DETALLE'
			begin				
				select CodigoProducto, NombreProducto, Cantidad, PrecioUnitario,  (Cantidad*PrecioUnitario) SubTotal
				  from HisListaProductosOrtopedia where IdProforma = @i_Id
				 order by IdListaProducto
			end	
		end 
		else if @ISubOpcion = 'E'
		begin
			if @IReporte = 'CABECERA'
			begin
				SELECT (select NombreCorto FROM nominadb..emphospital where Codigo = H.hospital AND Empresa = 'MED') Empresa,
								 NumeroProformaProveedor proforma, IdProforma codigoUnico, CONCAT(Serie,'-',NoAdmision) admision,
								 Format(FechaRegistro, 'dd/MM/yyyy HH:mm tt') fechaCreacion, 
								 Format(FechaBorrado, 'dd/MM/yyyy HH:mm tt') fechaBorrado, 
								 IsNull((select Nombre from inventario..proveedores
													where Empresa = H.EmpresaProveedor and Codigo = H.CodigoProveedor),'') proveedor,
								 Corporativo, IsNull((select concat(nombres,' ',apellidos)  from contadb..corporativos 
																			where Corporativo = H.Corporativo),'') UsuarioCrea,
								 CorporativoBorrado, IsNull((select concat(nombres,' ',apellidos)  from contadb..corporativos 
																			where Corporativo = H.CorporativoBorrado),'') UsuarioElimina
					FROM hospital..HisProformasOrtopedia H
				 WHERE FechaRegistro between DATEADD(dd, 0, DATEDIFF(dd, 0,ISNULL(@w_FechaInicioConvert,CONVERT(DATETIME,'01/01/2023')))) 
					 AND  DATEADD(dd, 1, DATEDIFF(dd, 0,ISNULL( @w_FechaFinConvert, (SELECT MAX(FechaRegistro) from HisProformasOrtopedia)))) 
					 AND (EmpresaProveedor = ISNULL(@i_EmpresaProveedor,EmpresaProveedor) AND CodigoProveedor = ISNULL(@i_CodigoProveedor,CodigoProveedor))
					 AND IdProforma = ISNULL(@i_Id,IdProforma) 
					 AND Estado = 'E' 
				 Order by IdProforma
			end
			else if @IReporte = 'DETALLE'
			begin
				SELECT   (select NombreCorto FROM nominadb..emphospital where Codigo = H.hospital AND Empresa = 'MED') Empresa,
					NumeroProformaProveedor proforma, Det.IdProforma codigoUnico, CONCAT(Serie,'-',NoAdmision) admision,
					Format(H.FechaRegistro, 'dd/MM/yyyy HH:mm tt') fechaCreacion, 
					Format(H.FechaBorrado, 'dd/MM/yyyy HH:mm tt') fechaBorrado, 
					IsNull((select Nombre from inventario..proveedores
									where Empresa = H.EmpresaProveedor and Codigo = H.CodigoProveedor),'') proveedor,
					H.Corporativo, IsNull((select concat(nombres,' ',apellidos)  from contadb..corporativos 
															where Corporativo = H.Corporativo),'') UsuarioCrea,
					CorporativoBorrado, IsNull((select concat(nombres,' ',apellidos)  from contadb..corporativos 
															where Corporativo = H.CorporativoBorrado),'') UsuarioElimina,
					CodigoProducto, NombreProducto, Cantidad, PrecioUnitario,  (Cantidad*PrecioUnitario) SubTotal
				FROM hospital..HisProformasOrtopedia H inner join HisListaProductosOrtopedia Det
						 on (H.IdProforma = Det.IdProforma)
				WHERE H.FechaRegistro between DATEADD(dd, 0, DATEDIFF(dd, 0,ISNULL(@w_FechaInicioConvert,CONVERT(DATETIME,'01/01/2023')))) 
					AND  DATEADD(dd, 1, DATEDIFF(dd, 0,ISNULL( @w_FechaFinConvert, (SELECT MAX(FechaRegistro) from HisProformasOrtopedia)))) 
					AND (EmpresaProveedor = ISNULL(@i_EmpresaProveedor,EmpresaProveedor) 
					AND CodigoProveedor = ISNULL(@i_CodigoProveedor,CodigoProveedor))
					AND H.IdProforma = CASE WHEN @i_Id = 0 THEN H.IdProforma ELSE ISNULL(@i_Id,H.IdProforma) END
					AND Estado = 'E' 
				Order by Det.IdProforma, Det.IdListaProducto
				/*
				select CodigoProducto, NombreProducto, Cantidad, PrecioUnitario,  (Cantidad*PrecioUnitario) SubTotal
				  from HisListaProductosOrtopedia where IdProforma = @i_Id 
				 order by IdListaProducto
				*/
			end
		end
	end
	COMMIT TRAN;
	END TRY
	BEGIN CATCH
		--revierte cambios y envia alerta
		ROLLBACK TRAN;
		Set @w_ErrRaise = ERROR_MESSAGE()
		Set @w_ErrRaiseNum = ERROR_STATE()
		RAISERROR(@w_ErrRaise,16,@w_ErrRaiseNum)
	END CATCH;
END
GO
/************************************************************************************************************
 ***********************************spProformaOrtopedia******************************************************
 ************************************************************************************************************/
 USE [HOSPITAL]
GO

/****** Object:  StoredProcedure [dbo].[spProformaOrtopedia]    Script Date: 13/04/2023 12:29:15 p.m. ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

-- **************************************************************************************************************************************************
CREATE PROCEDURE [dbo].[spProformaOrtopedia]
	@IOpcion  char(1) = 'I' , -- I (Inserccion), E (Eliminacion) 
	@i_empresa_unificada					varchar(010) = 'MED',
	@i_empresa_bodega							varchar(010) = 'BOD',
	@i_EmpresaSem									varchar(003) = 'SEM',	-- empresa que vende
	@i_IdProforma									int,
	@i_NumeroProformaProveedor		varchar(20),
	@i_admision_serie							varchar(010),
  @i_admision_codigo						int,
  @i_NivelPrecios								int=0,
	@i_EmpresaProveedor						char(3) = 'MED',
	@i_CodigoProveedor						char(12)='',
	@i_Hospital										char(3)='',
	@i_EmpresaReal								char(3),
	@i_Corporativo								int,
	@i_MontoTotal									smallmoney=0,
	@i_ProductosNombres						varchar(max)='', -- separador Ä 
  @i_ProductosPreciosIva				varchar(max)='', -- {n, m}
	@i_ProductosPrecios						varchar(max)='', -- {n, m}
	@i_ProductosUnidades					varchar(max)='',
	@i_TipoOrden									varchar(3)=''

AS 
SET NOCOUNT ON
declare @filaMenorProducto int;
declare @CodigoProducto char(14);
declare @nombreProducto char(150);
declare @precioProducto smallmoney;
declare @precioProductoIva smallmoney;
declare @unidadesProducto int;
declare @factorNivelPrecio float;
declare @inventarioCount	int;
declare @OrdenNumero		int;
declare @CodigoBodega int;
declare @FactorCosto		float;
declare @NivelPrecioOrtogua	float;
declare @SucursalHosp	varchar(10);
declare @OrdenTipo		varchar(10);
declare @EmpresaReal	varchar(003);
declare @OutResultadoProducto SmallInt;
declare @OutErrTipoProducto INT;
declare @OutMensajeProducto varchar(100);
declare @OutTipoOrden varchar(10);
declare @OutCodigoOrden int;
BEGIN TRAN
	BEGIN TRY
		declare	@w_ErrRaise		Varchar(MAX)	= '',
						@w_ErrRaiseNum 	INT	= 0,
						@w_IdProforma INT = 0,
						@w_TipoOrden Varchar(3)='',
						@w_CodigoOrden INT = 0,
					  @w_CodigoOrdenCompra INT=0,
						@w_EstadoOrdenCompra Char(1)='',
						@IdProforma INT = 0,
						@CodigosProductos varchar(MAX) = '';

		IF @IOpcion = 'I' 
		BEGIN
			if @i_NivelPrecios <= 0 
			begin
				Select 0 AS "codigo", 
							'El nivel de precios de la admision debe ser diferente de cero.' as "descripcion", 
							1 as tipo_error
				RETURN
			end		

			select @factorNivelPrecio = 1 + (PorcentajeIncremento/100) 
				from Hospital..InvNivelPreciosOrtopedia
				where NivelPrecio = @i_NivelPrecios and Empresa = @i_empresa_unificada

			if @@RowCount = 0
			begin
				Select 0 AS "codigo", 
							'Debe configurar el nivel de precios '+ convert(varchar,@i_NivelPrecios) +' en el mantenimiento para productos de ortopedia.' as "descripcion", 
							1 as tipo_error
				RETURN
			end

			if @i_TipoOrden = ''
			begin
				Select 0 AS "codigo", 
							'No se ha configurado un tipo de orden de ortopedia para esta base hospitalaria.' as "descripcion", 
							1 as tipo_error
				RETURN
			end

			select tNombresProd.keyCount noFila, tNombresProd.Item nombreProducto, 
							tPreciosProd.Item precioProducto, tPreciosProdIva.Item precioProductoIva,
							tUnidadesProd.Item unidadesProducto, 
							0 codigoProducto
				into #ProductosOrtopedia
				from  Contadb..SplitStringIndex(@i_ProductosNombres,'Ä') tNombresProd inner join
							Contadb..SplitStringIndex(@i_ProductosPrecios,',') tPreciosProd 
									on (tNombresProd.keyCount = tPreciosProd.keyCount)		 inner join
							Contadb..SplitStringIndex(@i_ProductosPreciosIva,',') tPreciosProdIva 
									on (tNombresProd.keyCount = tPreciosProdIva.keyCount)		 inner join
							Contadb..SplitStringIndex(@i_ProductosUnidades,',') tUnidadesProd 
									on (tPreciosProd.keyCount = tUnidadesProd.keyCount)
		
			if @@RowCount = 0
			begin
				Select 0 AS "codigo", 
							'Debe ingresar al menos un producto a la proforma.' as "descripcion", 
							1 as tipo_error
				RETURN
			end
			--insertando cabecera de proforma
			select @IdProforma = IsNull(max(IdProforma)+1,1) from Hospital..HisProformasOrtopedia
			insert into Hospital..HisProformasOrtopedia(IdProforma, NumeroProformaProveedor, EmpresaAdmision,Serie,NoAdmision,
																									NivelPrecios,EmpresaProveedor,CodigoProveedor,Hospital,Empresa,Corporativo,
																									Estado,FechaRegistro,MontoTotal)
									values(@IdProforma,@i_NumeroProformaProveedor,@i_empresa_unificada,@i_admision_serie,@i_admision_codigo,
													@i_NivelPrecios,@i_EmpresaProveedor,@i_CodigoProveedor,@i_hospital,@i_EmpresaReal,@i_Corporativo,
													'A',GetDate(),@i_MontoTotal)

			--insertando productos a la tabla inventario..productos 
			select @filaMenorProducto	= min(noFila) from #ProductosOrtopedia
		 
			while @filaMenorProducto <> 0
			begin
				select @nombreProducto = nombreProducto, @precioProducto = precioProducto, @precioProductoIva = precioProductoIva,
							 @unidadesProducto= unidadesProducto
					from #ProductosOrtopedia
					where noFila = @filaMenorProducto

				exec @CodigoProducto = inventario..F_Codigo_Producto @i_clase = '8' , @i_subclase = '1', @i_Empresa = 'MED'
			  set @CodigosProductos = @CodigosProductos + ',' + @CodigoProducto				

				update #ProductosOrtopedia 
						set codigoProducto = @CodigoProducto 
					Where noFila = @filaMenorProducto

				exec HOSPITAL..spInventarioIngresoProductoInterno @i_operacion = 'B', @i_accion = 'I',@i_Codigo_Hospital = 'HLA',
																												@i_Empresa = 'MED', @i_EmpresaBodega = @i_empresa_unificada,
																												@i_CodigoNuevo = @CodigoProducto, @i_Nombre = @nombreProducto,
																												@i_Categoria  ='82'/*Ortopedia*/, @i_Costo = @precioProductoIva,
																												@i_Departamento = '063', @i_Proveedor = @i_CodigoProveedor,
																												@i_Usuario = @i_Corporativo,	@i_Clase = '8', @i_SubClase = '1',
																												@w_Resultado = @OutResultadoProducto OUT, @w_ErrTipo = @OutErrTipoProducto OUT,
																												@w_Message = @OutMensajeProducto OUT						

				insert into Hospital..HisListaProductosOrtopedia(IdProforma,EmpresaProducto,CodigoProducto,NombreProducto,Cantidad,
																													PrecioUnitario,Corporativo,FechaRegistro)
										values(@IdProforma,'MED',@CodigoProducto,@nombreProducto,@unidadesProducto,
														@precioProductoIva,@i_Corporativo,GetDATE())			

				if @OutErrTipoProducto <> 0 
				begin
					ROLLBACK TRAN
					Select 0 AS "codigo", 
									@OutMensajeProducto as "descripcion", 
									1 as tipo_error
					Return
				end

				select @filaMenorProducto	= min(noFila) 
					from #ProductosOrtopedia 
					where noFila > @filaMenorProducto
			end -- Fin ingreso de productos y proforma con detalle
			set @CodigosProductos = STUFF(@CodigosProductos,1,1,'')
			--Llamado de cargos
			exec hospital..spInsertaCargos @i_empresa_unificada = @i_empresa_unificada, @i_empresa_bodega = @i_empresa_bodega,
																		 @i_admision_serie = @i_admision_serie, @i_admision_codigo = @i_admision_codigo,
																		 @i_empresa_real = @i_EmpresaReal,@i_hospital = @i_Hospital, @i_producto_codigo = @CodigosProductos, 
																		 @i_producto_cantidad = @i_ProductosUnidades, 
																		 @i_nivel_precio = @i_NivelPrecios, @i_monto_total = @i_MontoTotal,
																	   @i_corporativo = @i_Corporativo, @i_orden_tipo = @i_TipoOrden,
																		 @o_TipoOrden = @OutTipoOrden OUT, @o_CodigoOrden = @OutCodigoOrden OUT
			

			update hospital..HisProformasOrtopedia 
					set TipoOrden = @OutTipoOrden,
							CodigoOrden = @OutCodigoOrden
        where IdProforma = @IdProforma


			Select 0 AS "codigo", 
							@IdProforma as "descripcion", 
							0 as tipo_error

		END -- FIN INSERCCION 
		ELSE IF @IOpcion = 'E'
	  BEGIN
			SELECT @w_TipoOrden = isnull(TipoOrden,''),  @w_CodigoOrden = isnull(CodigoOrden,0),
						 @w_CodigoOrdenCompra = isnull(OrdenCompra,0)
				FROM Hospital..HisProformasOrtopedia
				WHERE IdProforma = @i_IdProforma and Serie = @i_admision_serie 
					and NoAdmision = @i_admision_codigo and Estado = 'A'
			
			IF @@RowCount = 0
			Begin
					ROLLBACK TRAN
					Select  1 AS "codigo", 
									Concat('No se encontro la proforma ',@i_NumeroProformaProveedor) as "descripcion", 
									1 as tipo_error
					Return
			End
			ELSE IF @w_TipoOrden = '' or @w_CodigoOrden = 0
			Begin
					ROLLBACK TRAN
					Select  1 AS "codigo", 
									Concat('La proforma ',@i_NumeroProformaProveedor,' no tiene asignado un número de orden.') as "descripcion", 
									1 as tipo_error
					Return
			End
			ELSE IF @w_CodigoOrdenCompra <> 0
			Begin
					Select @w_EstadoOrdenCompra = EstadoOrden From inventario..OrdenesCompra 
					 Where codigo = @w_CodigoOrdenCompra
					
					IF @@RowCount > 0 and @w_EstadoOrdenCompra <> 'A'
					Begin
						
						ROLLBACK TRAN
						Select  1 AS "codigo", 
										Concat('La proforma ',@i_NumeroProformaProveedor,
													 ' tiene una orden de compra en estado autorizado o pendiente de autorización.') as "descripcion", 
										1 as tipo_error
						Return
					End
					
			End

			UPDATE Hospital..HisProformasOrtopedia
				 SET FechaBorrado = GetDate(),  CorporativoBorrado = @i_Corporativo, Estado = 'E'
			 WHERE IdProforma = @i_IdProforma and Serie = @i_admision_serie 
		 		 and NoAdmision = @i_admision_codigo and Estado = 'A'

			EXEC SpAnulacionDevolucionOrdenes @i_EmpresaReal = @i_EmpresaReal, @i_CodigoOrden = @w_CodigoOrden ,
																				@i_TipoOrden = @w_TipoOrden,
																				@i_corporativo = @i_Corporativo 
		END
		COMMIT TRAN;
	END TRY
	BEGIN CATCH
		--revierte cambios y envia alerta
		ROLLBACK TRAN;
		Set @w_ErrRaise = ERROR_MESSAGE()
		Set @w_ErrRaiseNum = ERROR_STATE()
		RAISERROR(@w_ErrRaise,16,@w_ErrRaiseNum)
	END CATCH;

GO
/************************************************************************************************************
 *************************************FIN CREACION DE PAQUETES***********************************************
 ************************************************************************************************************/

/************************************************************************************************************
 *************************************MODIFICACION DE PAQUETES***********************************************
 ************************************************************************************************************/
USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[sp_Busqueda_Proveedores]    Script Date: 25/04/2023 10:19:07 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- ==========================================================
-- Author:  Walter Ronquillo
-- Create date: 25.11.2020
-- 08.02.2021 - Se agrega top 100 en la busqueda
-- 14.06.2021 - Se agrega información del estado del proveedor
-- Description:	Busqueda de Proveedores
-- ==========================================================
-- ==========================================================
-- Author:  Javier Castillo
-- Modificacion date: 25.04.2023
-- 25.04.2023 - Se agrego filtro de tipo de orden
-- ==========================================================
ALTER PROCEDURE [dbo].[sp_Busqueda_Proveedores]
	@i_Pagina int = 1,
	@i_Empresa varchar(5) = null,
	@i_Codigo varchar(12) = null,
	@i_Nombre varchar(80) = null,
	@i_NombreCheque varchar(80) = null,
	@i_NIT varchar(50) = null,
	@i_Activo char(1) = null,
	@i_Tipo char(6) = null
AS
BEGIN
	SET NOCOUNT ON;

	select
	top 100
		p.Empresa,
		p.Codigo ,
		p.Nombre ,
		p.NombreCheque,
		p.Nit,
		p.FechaInicio,
		case when p.activo ='S' then 'Activo' else 'Inactivo' end Estado
	from
		inventario.dbo.Proveedores p  
	where
		(@i_Empresa is null or p.Empresa = @i_Empresa)
		and (@i_Codigo is null or p.Codigo = @i_Codigo)
		and (@i_Nombre is null or p.Nombre like '%' + @i_Nombre + '%')
		and (@i_NombreCheque is null or p.NombreCheque like '%' + @i_NombreCheque + '%')
		and (@i_NIT is null or p.nit like '%' + @i_NIT + '%')
		and (@i_Activo is null or p.Activo = @i_Activo )		
		and (@i_Tipo is null or (p.Tipo = @i_Tipo or p.Tipo is null))
	order by
		p.Empresa ,p.Codigo 
END
GO


 /***********************************************************************************************************
 *************************************FIN MODIFICACION DE PAQUETES*******************************************
 ************************************************************************************************************/

/************************************************************************************************************
 *************************************INICIO CREACION PERMISOS***********************************************
 ************************************************************************************************************/
USE HOSPITAL
GO

GRANT execute 
	ON dbo.SpAnulacionDevolucionOrdenes
	TO USRHISJEFECAJA

USE INVENTARIO
GO

GRANT INSERT ON InventarioCargos TO USRHISJEFECAJA;
GO

USE PLANESMEDICOS
GO

GRANT SELECT ON PCorrelativos TO USRHISJEFECAJA;
GO

USE HOSPITAL
GO

GRANT EXECUTE ON sp_Busqueda_Proveedores TO USRHISJEFECAJA;
GO

USE HOSPITAL
GO

GRANT execute
	ON dbo.spBusquedaAdmisiones2
	TO USRHISJEFECAJA


USE HOSPITAL
GO

GRANT execute 
	ON dbo.spProformaOrtopedia
	TO USRHISJEFECAJA


USE CONTADB
GO

GRANT SELECT ON CONTADB.dbo.SplitStringIndex TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO

GRANT SELECT ON dbo.NTUnidadesMedida TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO

GRANT SELECT, UPDATE ON dbo.ProductosClase TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO

GRANT SELECT,UPDATE ON dbo.ProductosSubClase TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO

GRANT INSERT ON dbo.Productos TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO

GRANT INSERT ON dbo.BitacoraModificacionCatalogo TO USRHISJEFECAJA;
GO


USE inventario
GO

GRANT EXECUTE ON INVENTARIO.dbo.F_Codigo_Producto TO USRHISJEFECAJA;
GO

USE HOSPITAL
GO

GRANT EXECUTE ON HOSPITAL.dbo.spInventarioIngresoProductoInterno TO USRHISJEFECAJA;
GO

USE CONTADB
GO

GRANT SELECT ON CONTADB.dbo.EmpresasDefaults TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO

GRANT SELECT, UPDATE ON INVENTARIO.dbo.ExistenciasBodega TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO

GRANT SELECT ON INVENTARIO.dbo.BODEGAS TO USRHISJEFECAJA;
GO

USE NOMINADB
GO

GRANT SELECT ON NOMINADB.dbo.BasesXTipoAdmision TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO

GRANT SELECT ON INVENTARIO.dbo.CategoriaCuentasCostos TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO

GRANT INSERT ON INVENTARIO.dbo.InvCargosDetalle TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO

GRANT SELECT ON INVENTARIO.dbo.proveedores TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO

GRANT SELECT ON INVENTARIO.dbo.OrdenesCompra TO USRHISJEFECAJA;
GO

USE HOSPITAL
GO

GRANT EXECUTE ON HOSPITAL.dbo.spOpcionesProformaOrtopedia TO USRHISJEFECAJA;
GO

USE HOSPITAL
GO

GRANT EXECUTE ON HOSPITAL.dbo.spOpcionesProformaOrtopedia TO USRREPORTE;
GO

USE PLANESMEDICOS
GO

GRANT SELECT ON PLANESMEDICOS.dbo.PlanesXContrato TO USRHISJEFECAJA;
GO

USE PLANESMEDICOS
GO

GRANT SELECT ON PLANESMEDICOS.dbo.AfiliadosXContrato TO USRHISJEFECAJA;
GO


/************************************************************************************************************
 *************************************FIN CREACION PERMISOS**************************************************
 ************************************************************************************************************/

/************************************************************************************************************
 *************************************INICIO INSERCCION DE DATOS*********************************************
 ************************************************************************************************************/

BEGIN TRAN
USE HOSPITAL
GO

SET IDENTITY_INSERT HisCategoriasTipos ON

INSERT INTO HisCategoriasTipos(IdCategoriasTipos,Empresa,Nombre,CodigoOrdenTipo,NombreClasificacion,Clasificacion,Activo,Corporativo,FechaRegistro) 
VALUES (1,'MED','ORTOPEDIA','OR','LABORATORIO',1,'S',63737,GetDate()) 

SET IDENTITY_INSERT HisCategoriasTipos OFF
GO

USE HOSPITAL
GO
insert into inventario..productosclase(Empresa,Codigo,Nombre,Clasificacion)
                               values ('MED','8','Ortopedia','S')

USE HOSPITAL
GO
insert into inventario..productossubclase(Empresa,Clase,Codigo,Nombre,Ancho,Correlativo,
																					VentaExterna,GenerarEscalasPrecios,RecalcularEscalasPrecios)
                                   values('MED','8','1','No Cargables',5,1,'N','N','N')

select * from inventario..productossubclase where empresa = 'med' and clase = '8'

ROLLBACK TRAN

/************************************************************************************************************
 *************************************FIN INSERCCION DE DATOS************************************************
 ************************************************************************************************************/
