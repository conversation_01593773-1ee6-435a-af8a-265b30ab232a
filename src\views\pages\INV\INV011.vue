<template class="movimiento">
    <vx-card title="Ingreso/Aceptación Movimientos Bodega">
    
        <div class="content content-pagex">
            <vs-divider></vs-divider>
            <br>
            <div class="flex flex-wrap">                
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">                    
                        <label class="typo__label">Bodega Destino:</label>       
                        <strong>{{ cod_bodegaDestino.CODIGO }}</strong> 

                        <multiselect
                        v-model="cod_bodegaDestino"
                        :options="Lista_bodegas_destino_consulta"
                        :show-labels="false"
                        :allow-empty="false"
                        
                        :custom-label="Bodegas_seleccionado"
                        placeholder="Seleccionar bodega"
                        @input="onChangeBodegaDestino"
                        >
                        </multiselect>                                                              
                </div>
                <div class="w-full md:w-full lg:w-2/12 xl:w-2/12 pt-2 pl-10" v-if="permiso_bodega_vencido == 'S'">
                    <label  class="typo__label">Solicitud a Bodega Productos Proximos a Vencer</label> 
                    <vs-switch  v-model="solicitud_bodega_vencido" />    
                </div>                
            </div>

            <div class="flex flex-wrap">
    
                <div style="margin-right: 20px" class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <label class="typo__label">Estado</label>
                    <multiselect
                      v-model="cb_lista_operacion"
                      :options="lista_estado"
                      :searchable="true"
                      :close-on-select="true"
                      :show-labels="false"
                      :allow-empty="false"
                      :custom-label="Operacion_seleccionada"
                      placeholder="Seleccionar"
                      @input="onChangeEstado">
                      <span slot="noOptions">Lista no disponible.</span>
                    </multiselect>
                </div>

                <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7 m-1">
                    <label class="typo__label" v-if="Id_estado_seleccionado == 'P'">Fecha Inicio Creación:</label>
                    <label class="typo__label" v-else>Fecha Inicio Solicitud:</label>
                    <vs-input type="date" v-model="fecha_inicio" name="date1" />
                </div>
    
                <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7 m-1">
                    <label class="typo__label" v-if="Id_estado_seleccionado == 'P'">Fecha Final Creación:</label>
                    <label class="typo__label" v-else>Fecha Final Solicitud:</label>
                    <vs-input type="date" v-model="fecha_final" name="date2" />
                </div>

                <vs-button style="float:left;margin: 20px" color="primary" type="filled" icon-pack="fas" icon="fa-search" @click="Consultar_OrdenEnc()"> Búsqueda</vs-button>
    
                <vs-button color="primary" style="float:left;margin: 20px" type="filled" icon-pack="fas" icon="fa-plus" @click="Emergente_Detalle('','N')"> Nuevo</vs-button>                                
    
            </div>
            <vs-alert v-if="cod_bodegaDestino.VisualizarTraslados && cod_bodegaDestino.VisualizarTraslados == 'S'" :active.sync="Alertas[0]" color="success" icon-pack="feather" icon="icon-info" closable close-icon="icon-x" class="mb-1">
                Se muestran todos los movimientos para los estados: Solicitado, Despachado, Aceptado, Anulado
            </vs-alert>
            <vs-divider></vs-divider>
    
            <vs-table2 max-items="10" pagination :data="Lista_vista" search id="tb_lista_solicitud">
    
                <template slot="thead">
                    <th width="130px">Nº. Movimiento</th>
                    <th width="130px">Nº. Solicitud</th>
                    <th>Fuente</th>
                    <th>Fecha(s)</th>
                    <th>Observaciones</th>
                    <th></th>
    
                </template>
    
                <template slot-scope="{data}" :allowResizing='true'>
                    <tr :key="indextr" v-for="(tr, indextr) in data">
    
                        <vs-td2 width='5%'>
                            {{tr.IDMOVIMIENTOFK}}
                        </vs-td2>

                        <vs-td2 width='5%'>
                            {{tr.IDREQUISICIONENC}}
                        </vs-td2>
    
                        <vs-td2 width='15%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                {{tr.IdBodegaFuentefk +' - '+tr.NOMBRE_BODEGA_FUENTE}}
                            </div>
                        </vs-td2>
    
                        <vs-td2 width='25%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                <label v-if="Id_estado_seleccionado  === 'P'">Creado: {{tr.FECHASOLICITUD}}</label>
                                <br v-if="Id_estado_seleccionado  === 'P'">
                                <label v-if="tr.FECHAFINALIZADO">{{'Solicitado: '+tr.FECHAFINALIZADO+' - Usuario: '+tr.IDCORPORATIVOSOLICITAFK}}</label>
                                <br v-if="tr.FECHAFINALIZADO">
                                <label v-if="tr.FECHAENTREGADA">Despachado: {{tr.FECHAENTREGADA}}</label>
                                <br v-if="tr.FECHAENTREGADA">
                                <label v-if="tr.FECHARECIBIDA">{{'Aceptado: '+tr.FECHARECIBIDA+' - Usuario: '+tr.IDCORPORATIVOFINALIZAFK}}</label>
                                <br v-if="tr.FECHARECIBIDA">
                                <label v-if="tr.FECHAANULACION">Anulado: {{tr.FECHAANULACION}}</label>
                            </div>    
                        </vs-td2>

                        <vs-td2  width='35%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                {{ tr.OBSERVACIONES }}
                            </div>
                        </vs-td2>  
    
                        <vs-td2 v-if="Id_estado_seleccionado  == 'P'" width='15%' align="right"  style="white-space:nowrap;min-width: 200px;">
                            <vx-tooltip text="Consultar" style="display:inline-block;margin-right:20px">
                                <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" @click="Emergente_Detalle(data[indextr], 'E')"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip text="Anular" style="display:inline-block;margin-right:2px">
                                <vs-button color="red" icon-pack="fas" icon="fa-times"   @click="Anular_Movimiento(data[indextr])"></vs-button>
                            </vx-tooltip>
                        </vs-td2>
                        <vs-td2 v-else-if="Id_estado_seleccionado  == 'E'" width='15%%' align="right" style="white-space:nowrap;min-width: 200px;">
                            <vx-tooltip text="Consultar" style="display:inline-block;margin-right:20px">
                                <vs-button color="primary" icon-pack="fas" icon="fa-eye" @click="Emergente_Detalle_Pedido(data[indextr])"></vs-button>                              
                            </vx-tooltip>
                            <vx-tooltip text="Excel" style="display:inline-block;margin-right:2px">
                                <vs-button icon-pack="far" icon="fa-file-excel" @click="Consultar_Movimiento(data[indextr],'EXCEL')" color="success"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip text="Imprimir" style="display:inline-block;margin-right:20px">
                                <vs-button color="primary" icon-pack="feather" icon="icon-printer" @click="Consultar_Movimiento(data[indextr])"></vs-button>
                            </vx-tooltip>  
                            <vx-tooltip text="Anular" style="display:inline-block;margin-right:2px">
                                <vs-button color="red" icon-pack="fas" icon="fa-times"   @click="Anular_Movimiento(data[indextr])"></vs-button>
                            </vx-tooltip>
                        </vs-td2>
                        <vs-td2 v-else-if="Id_estado_seleccionado  == 'R'" width='10%' align="right" style="white-space:nowrap;min-width: 200px;">
                            <vx-tooltip text="Consultar" style="display:inline-block;margin-right:30px">
                                <vs-button color="primary" icon-pack="fas" icon="fa-arrow-left"  @click="Emergente_Detalle_Recepcion(data[indextr])"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip text="Excel" style="display:inline-block;margin-right:2px">
                                <vs-button icon-pack="far" icon="fa-file-excel" @click="Consultar_Movimiento(data[indextr],'EXCEL')" color="success"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip text="Imprimir" style="display:inline-block;margin-right:30px">
                                <vs-button color="primary" icon-pack="feather" icon="icon-printer"  @click="Consultar_Movimiento(data[indextr])"></vs-button>
                            </vx-tooltip>
                        </vs-td2>                        
                        <vs-td2 v-else-if="Id_estado_seleccionado  == 'F,N'" width='10%' align="right" style="white-space:nowrap;min-width: 200px;">
                            <vx-tooltip text="Consultar" style="display:inline-block;margin-right:30px">
                                <vs-button color="primary" icon-pack="fas" icon="fa-eye" @click="Emergente_Detalle_Aceptacion(data[indextr])"></vs-button>    
                            </vx-tooltip>
                            <vx-tooltip text="Excel" style="display:inline-block;margin-right:2px">
                                <vs-button icon-pack="far" icon="fa-file-excel" @click="Consultar_Movimiento(data[indextr],'EXCEL')" color="success"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip text="Imprimir" style="display:inline-block;margin-right:2px">
                                <vs-button color="primary" icon-pack="feather" icon="icon-printer" @click="Consultar_Movimiento(data[indextr])"></vs-button>
                            </vx-tooltip>
                        </vs-td2>
    
                    </tr>
                </template>                
            </vs-table2>
    
            <!----------------------- NUEVA ASOCIACIÓN ---------->
            <vs-popup class="inventario-popup" :title="Descripcion_Emergente" :active.sync="Estado_Emergente" >
                <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
                    <div style="border:2px solid #1B5E20;border-radius:5px;padding:5px;font-size:12px;background-color: #C8E6C9">
                        Sucursal Origen: <b style="font-size:1vw">{{sesion.sesion_sucursal_nombre}}</b>
                    </div>
                    <br>
                    <vs-row class="w-full flex">
                        <div class="sm:w-6/12 md:w-5/12 lg:w-4/12 xl:w-3/12">                 
                        </div>
                        <div class="sm:w-6/12 md:w-5/12 lg:w-4/12 xl:w-3/12" style="align-self:right; direction: rtl;">
                            <b> Solicitud No. </b>&nbsp;
                            <b style="font-size:2vw">{{Id_OrdenRequisicion}}</b>
                        </div>
                    </vs-row> 
                    <!--- Ubicación Destino -->
                    <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <br>
                        <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                            <label class="typo__label">Bodega Fuente:</label>
                            <strong>{{ cod_bodegaFuente.CODIGO }}</strong>
                            <multiselect v-if="cod_bodegaDestino.CODIGO != 5"
                              v-model="cod_bodegaFuente"
                              :options="Lista_bodegas_fuente"
                              :searchable="true"
                              :close-on-select="true"
                              :show-labels="false"
                              :custom-label="Bodegas_seleccionado"
                              placeholder="Seleccionar bodega"
                              @input="onChangeBodegaFuente"
                              :danger="errors.length > 0"
                              :danger-text="errors.length > 0 ? errors[0] : null"
                              :disabled="Deshabilitar_campos">
                              <span
                              slot="noOptions">Lista no disponible.</span>
                              </multiselect>

                              <multiselect v-if="cod_bodegaDestino.CODIGO == 5"
                              v-model="cod_bodegaFuente"
                              :options="Lista_Bodegas_Fuente_Vencido"
                              :searchable="true"
                              :close-on-select="true"
                              :show-labels="false"
                              :custom-label="Bodegas_seleccionado"
                              placeholder="Seleccionar bodega"
                              @input="onChangeBodegaFuente"
                              :danger="errors.length > 0"
                              :danger-text="errors.length > 0 ? errors[0] : null"
                              :disabled="Deshabilitar_bodega_fuente">
                              <span
                              slot="noOptions">Lista no disponible.</span>
                              </multiselect>
                        </ValidationProvider>
                    </div>
                    <!--- Ubicación Destino -->
                    <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <br>
                        <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                            <label class="typo__label">Bodega Destino:</label>       
                            <strong>{{ cod_bodegaDestino.CODIGO }}</strong>                                                 
                            <multiselect
                              v-model="cod_bodegaDestino"
                              :options="Lista_bodegas_destino_consulta"
                              :searchable="true"
                              :close-on-select="true"
                              :show-labels="false"
                              :custom-label="Bodegas_seleccionado"
                              placeholder="Seleccionar bodega"
                              @input="onChangeBodegaDestino"
                              :danger="errors.length > 0"
                              :danger-text="errors.length > 0 ? errors[0] : null"
                              :disabled="Deshabilitar_campos">
                              <span
                              slot="noOptions">Lista no disponible.</span>
                              </multiselect>
                        </ValidationProvider>                                                
                    </div>

                    <br>
                    <!--- Producto -->
                    <vs-divider>Detalle</vs-divider>
    
                    <div class="flex flex-wrap">
    
                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <ValidationProvider name="producto" rules="required" v-slot="{ errors }" class="required">
                                <label class="typo__label">Producto</label>
                                <vx-input-group class="">
                                    <vs-input id="busquedaProducto" v-model="producto_buscar" 
                                            v-on:keydown.enter="cargar_producto()"
                                            v-on:keydown.tab.prevent="cargar_producto()" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                    <template slot="append">
                                        <div class="append-text btn-addon">
                                            <button type="submit" v-show="false" name="button"></button>
                                            <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="cargar_producto()" icon="fa-search"></vs-button>
                                        </div>
                                    </template>
                                </vx-input-group>
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                            <ValidationProvider name="cant_solicitada" rules="required|numero_min:0" v-slot="{ errors }" class="required">
                                <label class="typo__label">Cant. Solicitar</label>
                                <vs-input id="cantidad_sol" class="w-full" type="number" count="100" v-model="Cantida_solicitar"  :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                            </ValidationProvider>
                        </div>
                        <!------ BOTONES DE ACCION ---->
                        <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6">
                            <vs-button color="primary" style="float:left;margin: 20px" type="filled" icon-pack="fas" icon="fa-plus" id="btn_agregar"  v-on:keydown.tab.prevent="Confirmacion_Transaccion('N', '0')" @click="Confirmacion_Transaccion('N', '0')">Agregar</vs-button>
                        </div>
                    </div>
    
                    <!----- Detalle producto seleccionado--->
                    <div v-if="Producto_seleccionado.Codigo>0" class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <!---<div style="border:2px solid #1B5E20;border-radius:5px;padding:5px;font-size:12px;background-color:#A5D6A7">--->
                        <div style="border-radius:5px;padding:5px;font-size:15px;background-color:#A5D6A7">
                            Código: {{Producto_seleccionado.Codigo}}
                            <br>
                            Producto: {{Producto_seleccionado.marca_comercial}}
                            <br>
                            Presentación: {{Producto_seleccionado.Presentacion}}
                            <br>
    
                        </div>
                    </div>
                    <!--- Mostrar detalle de la orden--->
                    <vs-table2 max-items="10" pagination :data="Lista_detalle" id="tb_lista_solicitud" v-if="Estado_Emergente">
    
                        <template slot="thead">
                            <th>Línea</th>
                            <th>Código</th>
                            <th>Producto</th>
                            <th>Unidad Medida</th>
                            <th>Cant. Solicitada</th>
                            <th v-if="permiso_ver_costo_promedio">Costo promedio</th>
                            <th v-if="permiso_ver_costo_promedio">Costo promedio total</th>
                            <th></th>
                        </template>
    
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 width="5%">
                                    {{ indextr+1 }}
                                </vs-td2>
                                <vs-td2 width="5%">
                                    {{ tr.IDPRODUCTOFK }}
                                </vs-td2>                                 
                                <vs-td2 width="65%">
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                                        
                                        {{tr.DESCRIPCION_PRODUCTO}}                                        
                                    </div>
                                </vs-td2>
                                <vs-td2 width="10%">
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">
                                        {{tr.DESCRIPCION_UNIDAD}}
                                    </div>                                    
                                </vs-td2>
                                <vs-td2 width="5%">
                                    {{tr.CANTIDAD_PEDIDA}}
                                </vs-td2>
                                <vs-td2 width="15%" v-if="permiso_ver_costo_promedio">
                                    <div style="min-width: fit-content;">
                                        {{ $formato_decimal(tr.COSTO_PROMEDIO) }}
                                    </div>
                                </vs-td2>
                                <vs-td2 width="15%" v-if="permiso_ver_costo_promedio" >
                                    <div style="min-width: fit-content;">
                                        {{ $formato_decimal(tr.COSTO_PROMEDIO_TOTAL) }}
                                    </div>
                                </vs-td2>                                
                                <vs-td2 width="5%">
                                    <vx-tooltip text="Eliminar" style="display:inline-block">
                                        <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="Eliminar_Registro(data[indextr])"></vs-button>
                                    </vx-tooltip>
                                </vs-td2>
                            </tr>
                        </template>
                        <template slot="tfooter">
                            <tr v-if="permiso_ver_costo_promedio" class="foot">
                                <th colspan="6" scope="row" style="padding-right: 10px;">Total del Movimiento</th>
                                <td>{{totalCostos}}</td>
                            </tr>
                        </template>
                    </vs-table2>
    
                    <vs-divider></vs-divider>
                    <div class="flex flex-wrap">
                        <div class="w-full md:w-full lg:w-full xl:w-full">
                            <vs-textarea  label="Observaciones" type="text" class="w-full" v-model="Observaciones" />
                        </div>
                    </div>
                    <vs-button
                      color="primary"
                      style="float:right;margin: 5px"
                      type="filled"
                      icon-pack="feather"
                      icon="icon-save"
                      id="btn_confirmacion"
                      @click="Finalizar_Solicitud()"> Finalizar Solicitud</vs-button>
                    <vs-divider></vs-divider>
                </div>
            </vs-popup>
     
            <!--------------- Resultado busqueda Producto-------------->
            <vs-popup classContent="popup-example" title="Resultado Búsqueda" :active.sync="Estado_VentanaEmergente_Busqueda"  v-on:keydown.esc="$emit('close')" tabindex="0" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
                <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px" >
    
                    <form>
    
                        <vs-divider></vs-divider>
                        <vs-table2 max-items="10" pagination :data="producto" id="tb_departamentos">
    
                            <template slot="thead">
                                <th>Linea</th>
                                <th>Codigo</th>
                                <th>Descripcion</th>
                                <th>Concentración</th>
                                <th>Presentación</th>
                                <th></th>
                            </template>
    
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2 width='5%'>
                                        {{ indextr+1 }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Codigo">
                                        {{data[indextr].Codigo}}
                                    </vs-td2>
    
                                    <vs-td2 :data="data[indextr].Descripcion">
                                        <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">
                                            {{data[indextr].Descripcion}}
                                        </div>
                                    </vs-td2>
    
                                    <vs-td2 :data="data[indextr].Concentracion">
                                        {{data[indextr].Concentracion}}
                                    </vs-td2>
    
                                    <vs-td2 :data="data[indextr].PresentacionNombre">
                                        {{data[indextr].PresentacionNombre}}
                                    </vs-td2>
    
                                    <vs-td2 align="right">
                                        <vs-button color="primary" icon-pack="fas" icon="fa-check-circle" style="display:inline-block;margin-right:2px" @click="Seleccionar_producto(data[indextr])"></vs-button>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </form>
    
                </div>
            </vs-popup>
    
            <!----------------------- Consulta de movimientos ---------->
            <vs-popup class="inventario-popup" classContent="popup-example" :title="Descripcion_Emergente" :active.sync="Estado_Emergente_Recepcion">
                <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">   
                    <div style="border:2px solid #1B5E20;border-radius:5px;padding:5px;font-size:12px;background-color: #C8E6C9">
                        Sucursal Origen: <b style="font-size:1vw">{{sesion.sesion_sucursal_nombre}}</b>
                    </div>  
                    <vs-row class="w-full flex">
                        <div class="sm:w-6/12 md:w-5/12 lg:w-4/12 xl:w-3/12">
                            <b> Movimiento No. </b>&nbsp;
                            <b style="font-size:2vw">{{Id_Movimiento}}</b>
                        </div>   
                        <div class="sm:w-6/12 md:w-5/12 lg:w-4/12 xl:w-3/12" style="align-self:right; direction: rtl;">
                            <b> Solicitud No. </b>&nbsp;
                            <b style="font-size:2vw">{{Id_OrdenRequisicion}}</b>
                        </div>
                    </vs-row>  
                    <div class="w-1/2 md:w-1/2 lg:w-6/12 xl:w-6/12">
                         <b> <small>Fecha Solicitud: </small></b>
                        <b>{{Requisicion_seleccionada.FECHAFINALIZADO}}</b>
                    </div>
                    <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2" v-if="Consulta_Pedido || Consulta_Aceptados">
                        <b> <small>Estado: </small></b>&nbsp;
                        <b>{{Estado_Requisicion}}</b>
                    </div> 
    
                    <!--- Ubicación Destino -->
                    <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <br>
                        <label class="typo__label">Bodega Fuente:</label>
                        <strong>{{id_bodega_fuente}}</strong>
                        <vs-input  type="text" v-model="nombre_bodega_fuente" :disabled="Deshabilitar_campos" class="w-full"/>
                    </div>  
                    <br>                      
                    <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <br>
                        <label class="typo__label">Bodega Destino:</label>
                        <strong>{{id_bodega_destino}}</strong>
                        <vs-input  type="text" v-model="nombre_bodega_destino" :disabled="Deshabilitar_campos" class="w-full"/>
                    </div>
                    <vs-divider>Detalle</vs-divider>
    
                    <!--- Mostrar detalle de la orden--->
                    <vs-table2 max-items="10" pagination :data="Lista_detalle" id="tb_lista_solicitud" v-if="Estado_Emergente_Recepcion">
    
                        <template slot="thead">
                            <th>Linea</th>
                            <th>Codigo</th>
                            <th>Producto</th>
                            <th>Unidad Medida</th>
                            <th>Cant. Solicitada</th>
                            <th v-if="permiso_ver_costo_promedio">Costo promedio</th>
                            <th v-if="permiso_ver_costo_promedio">Costo promedio total</th>
                            <th v-if="!Consulta_Pedido">Cant. Despachada</th>
                            <th v-if="!Consulta_Pedido">Cant. Aceptada</th>
                        </template>
    
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 width='5%'>
                                    {{ indextr+1 }}
                                </vs-td2>
                                <vs-td2 width='5%'>
                                    {{ tr.IDPRODUCTOFK }}
                                </vs-td2>  
                                <vs-td2 width='60%'>
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                                        
                                        {{tr.DESCRIPCION_PRODUCTO}}                                        
                                    </div>
                                </vs-td2>
                                <vs-td2 width='15%'>
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                                        
                                        {{tr.DESCRIPCION_UNIDAD}}
                                    </div>
                                </vs-td2>                                
                                <vs-td2 width='5%'>
                                    {{tr.CANTIDAD_PEDIDA}}
                                </vs-td2>
                                <vs-td2 width="15%" v-if="permiso_ver_costo_promedio">
                                    <div style="min-width: fit-content;">
                                        {{ $formato_decimal(tr.COSTO_PROMEDIO) }}
                                    </div>
                                </vs-td2>
                                <vs-td2 width="15%" v-if="permiso_ver_costo_promedio" >
                                    <div style="min-width: fit-content;">
                                        {{ $formato_decimal(tr.COSTO_PROMEDIO_TOTAL) }}
                                    </div>
                                </vs-td2>
                                <vs-td2 v-if="!Consulta_Pedido" width='5%'>
                                    {{tr.CANTIDAD_ENTREGA}}
                                </vs-td2>
                                <vs-td2 v-if="!Consulta_Pedido" width='5%'>
                                    {{ tr.CANTIDAD_RECIBIDA }}   
                                </vs-td2>
                                <!-- <vs-switch v-if="tr.FINALIZADO ==='N'" style="margin-top:10px;margin-left:20px" v-model="tr.ESTADO_SWITCH" @input="Registrar_RecibidoDet(tr, 'R')" /> -->  
                            </tr>
                        </template>
                        <template slot="tfooter">
                            <tr v-if="permiso_ver_costo_promedio" class="foot">
                                <th colspan="6" scope="row" style="padding-right: 10px;">Total del Movimiento</th>
                                <td>{{totalCostos}}</td>
                            </tr>
                        </template>
                    </vs-table2>
                    <vs-divider></vs-divider>
                    <div class="flex flex-wrap">
                        <div class="w-full md:w-full lg:w-full xl:w-full">
                            <vs-textarea  disabled label="Observaciones" type="text" class="w-full" v-model="Observaciones" />
                        </div>
                    </div>
                    <vs-divider></vs-divider>
                    <div class="flex">
                        <div class="w-full">
                            <b><small >Solicitado por: </small></b>&nbsp;
                            <b>{{Requisicion_seleccionada.CORPORATIVO_SOLICITANTE +' - '+Requisicion_seleccionada.IDCORPORATIVOSOLICITAFK}}</b>&nbsp;
                            <b><small v-if="Requisicion_seleccionada.ESTADO=='F'">/ Aceptado por: </small></b>&nbsp;
                            <b v-if="Requisicion_seleccionada.ESTADO=='F'">{{Requisicion_seleccionada.CORPORATIVO_FINALIZA +' - '+Requisicion_seleccionada.IDCORPORATIVOFINALIZAFK}}</b>
                        </div> 
                    </div>             
                    <vs-divider></vs-divider>
                    <div>
                        <vs-button v-if="!Consulta_Pedido && !Consulta_Aceptados" color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" id="btn_confirmacion" @click="MovimientoRecepcion()">Aceptar Despacho</vs-button>
                        <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-printer" id="btn_movimiento" @click="Consultar_Movimiento(Requisicion_seleccionada)"> Imprimir </vs-button>
                        <vs-button icon-pack="far" style="float:right;margin: 5px" icon="fa-file-excel" @click="Consultar_Movimiento(Requisicion_seleccionada,'EXCEL')" color="success">Excel</vs-button>
                    </div>
                    <vs-divider></vs-divider>
                </div>
            </vs-popup>
        </div>
    </vx-card>
    </template>
    
    <script>
    import Multiselect from 'vue-multiselect'
    import moment from "moment"
    import "vue-multiselect/dist/vue-multiselect.min.css"    
    export default {
        components: {
            Multiselect,
            //Timeline,
        },
        watch:{
            Estado_Emergente(value){
                if(value==false){
                    this.InsertoEncabezado = false;
                }                
            }
        },
        /*
        watch:{
            enviar_bodega_vencido(bodegaVencido){
                if(!this.cod_bodegaDestino && this.cod_bodegaDestino.length == 0)
                    return
                
                if(bodegaVencido && this.id_bodega_fuente==5)
                    return

                for(const bodega of this.Lista_bodegas_fuente){          
                    if(bodegaVencido){
                        if(bodega.CODIGO == 5){
                            this.id_bodega_fuente = bodega.CODIGO
                            this.cod_bodegaFuente = bodega
                            break
                        }
                        
                    }                                      
                    else if( bodega.TipoBodega == this.cod_bodegaDestino.TipoBodega ){                                                
                        this.id_bodega_fuente = bodega.CODIGO
                        this.cod_bodegaFuente = bodega
                        break
                    }                            
                }                    

            }
        },*/
        data() {
            return {
                Alertas: [false],
                Estado_VentanaEmergente_Busqueda: false,
                cb_lista_operacion: '',
                Descripcion_Emergente: '',
                Consulta_Pedido:false,
                Consulta_Aceptados:false,
                producto_buscar: '',                
                lista_estado: [{
                        ID: 'P',
                        DESCRIPCION: 'Edición'
                    },
                    {
                        ID: 'E',
                        DESCRIPCION: 'Solicitado'
                    },
                    {
                        ID: 'R',
                        DESCRIPCION: 'Despachado'
                    },
                    {
                        ID: 'F,N',
                        DESCRIPCION: 'Aceptado,Anulado'
                    }
                ],
                Id_estado_seleccionado: 0,
                fecha_inicio: '',
                fecha_final: '',
                Lista_vista: [],
                Estado_Emergente: false,
                Estado_Emergente_Recepcion: false,
                enviar_bodega_vencido: false,
                solicitud_bodega_vencido: false,                
                permisos_tipos_bodegas: [],
                permiso_bodega_vencido:'',
                permiso_ver_costo_promedio: false,
                Lista_bodegas_fuente: [],
                Lista_Bodegas_Fuente_Vencido:[],
                Bodega_Vencidos:{},
                Bodega_Fuente:{},
                Bodega_Destino:{},
                Lista_bodegas_destino_consulta: [],
                id_bodega_fuente: '',
                nombre_bodega_fuente: '',
                id_bodega_destino: '',
                nombre_bodega_destino: '',
                cod_bodegaFuente: '',
                cod_bodegaDestino: '',    
                Producto_seleccionado: {
                    nombre: '',
                    marca_comercial: '',
                    Principio_activo: '',
                    Concentracion: '',
                    Presentacion: '',
                    Codigo: '',
                    codigo_interno: '',
                    Activo: 'N'
                },
                Id_OrdenRequisicion: null,
                InsertoEncabezado: false,
                Estado_Requisicion:'',
                Id_Movimiento:null,
                Cantida_solicitar: '',    
                Lista_detalle: [],
                Deshabilitar_campos: false,
                Deshabilitar_bodega_fuente: false,
                producto: [],    
                Observaciones: '',
                listado_reportes: [],
                Requisicion_seleccionada: {},
                listado_source: {
                    CODIGO_SOLICITUD: '',
                    CODIGO_MOVIMIENTO: '',
                    CODIGO_REQUERIMIENTO: '',
                }               
            }
        },
        async mounted() {
    
            this.cb_lista_operacion = {
                ID: 'P',
                DESCRIPCION: 'Edición'
            }
            this.Id_estado_seleccionado = 'P'; 
            let permisos_sub_bodegas = []                   
            for(let privilegio of this.$store.state.privilegios){
               if(privilegio.Privilegio.includes("TIPO_BODEGA")){                      
                   let tipobodega = privilegio.Privilegio.split('_')[2];
                   if(tipobodega == 'T'){
                     this.permiso_bodega_vencido = 'S'
                   }else{
                     this.permisos_tipos_bodegas.push(privilegio.Privilegio.split('_')[2])                    
                   }                                                       
                }else if(privilegio.Privilegio.includes("INGRESO_SUB_BODEGA")){
                    permisos_sub_bodegas.push(privilegio.Privilegio.split('_')[3])
                }
            }
            await this.Consultar_Bodega('F',this.permisos_tipos_bodegas.join(","),this.permiso_bodega_vencido);
            await this.Consultar_Bodega('H',this.permisos_tipos_bodegas.join(","),this.permiso_bodega_vencido);
            await this.ConsultarSubBodega('G',permisos_sub_bodegas.join(","));
            this.permiso_ver_costo_promedio = this.$validar_privilegio('VER_COSTO_PROMEDIO').status
        },
        computed: {
            totalCostos() {
                return this.Lista_detalle.reduce((acumulador, movimiento) => acumulador + parseFloat(movimiento.COSTO_PROMEDIO_TOTAL) , 0)
                           .toLocaleString("es-GT", {
                                                style: "currency",
                                                currency: "GTQ",
                                                maximumFractionDigits:2
                                            })
    
            },
            sesion() {
                return this.$store.state.sesion
            }
        },
        async beforeCreate(){
            this.listado_reportes = await this.$recupera_parametros_reporte('Movimiento Requerimiento')        
        },
        methods: {           
            async Consultar_Movimiento(datos, formato = 'PDF') {
                this.listado_source.CODIGO_MOVIMIENTO = datos.IDMOVIMIENTOFK
                this.listado_source.CODIGO_SOLICITUD = datos.IDREQUISICIONENC
                this.listado_source.VerCostoPromedio = this.permiso_ver_costo_promedio? 'S':'N'
               
                this.$reporte_modal({
                    Nombre: "Movimiento Requerimiento",
                    Opciones: this.listado_source,
                    Formato: formato
                }).catch(() => {
                })
            },    
            GetDateValue(value) {
                if (value === undefined) {
                    return '';
                } else if (value === null) {
                    return '';
                } else if (value == '') {
                    return '';
                }
                return moment(value).format('DD/MM/YYYY');          
            },        
            Anular_Movimiento(Datos){
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación Anulación',
                    acceptText: 'Anular',
                    cancelText: 'Cancelar',
                    text: `¿Desea anular la solicitud No. ${Datos.IDREQUISICIONENC}?`,
                    accept: () => {
                        return this.axios.post('/app/v1_OrdenCompra/Requisicion_Enc', {
                            Requisicion_enc: Datos.IDREQUISICIONENC,       
                            Operacion: 'A'
                        }).then(resp =>{
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                        position:'top-center',
                                    color: '#B71C1C',
                                    title: 'Alerta',
                                    text: resp.data.mensaje,
                                })
                            } else {
                                this.$vs.notify({
                        position:'top-center',
                                    color: 'sucess',
                                    title: 'Anulación',
                                    text: resp.data.mensaje
                                })    
                                this.Consultar_OrdenEnc();
                            }
                        })
                    }
                })
            },
            Operacion_seleccionada({
                DESCRIPCION
            }) {
                return `${DESCRIPCION} `
            },
            onChangeEstado(value) {
                if (value !== null && value.length !== 0) {
                    if (value.ID == 'S')
                        this.Id_estado_seleccionado = 'P';
                    else
                        this.Id_estado_seleccionado = value.ID;
                    this.Consultar_OrdenEnc();
                } else {
                    this.Id_estado_seleccionado = '';
                }
            },
            Bodegas_seleccionado({
                CODIGO,NOMBRE
            }) {
                return `${CODIGO} - ${NOMBRE} `;
            },
            onChangeBodega(value) {
                if (value !== null && value.length !== 0) {
                    this.id_bodega_fuente = value.CODIGO;
    
                } else {
                    this.id_bodega_fuente = '';
                }
            },
            onChangeBodegaFuente(value) {
                if (value !== null && value.length !== 0) {
                    this.id_bodega_fuente = value.CODIGO;
                } else {
                    this.id_bodega_fuente = '';
                }
            },
            onChangeBodegaDestino(value) {                
                if(!value){
                    
                    this.cod_bodegaDestino = ''
                    this.Bodega_Fuente = {}    
                    return
                }                    
                if (value !== null && value.length !== 0) {
                    this.id_bodega_destino = value.CODIGO;
                    this.Alertas[0] = true;
                    this.Lista_Bodegas_Fuente_Vencido = []

                    if(value.BodegaFuente && value.BodegaPadre){
                        for(const bodega of this.Lista_bodegas_fuente){
                            if( bodega.CODIGO == value.BodegaPadre){                            
                                this.Bodega_Fuente = bodega                                                
                                this.id_bodega_fuente = bodega.CODIGO
                                this.cod_bodegaFuente = bodega
                                break
                            }                            
                        } 
                    }else{
                        for(const bodega of this.Lista_bodegas_fuente){
                            if( bodega.TipoBodega == value.TipoBodega){                            
                                this.Bodega_Fuente = bodega                                                
                                this.id_bodega_fuente = bodega.CODIGO
                                this.cod_bodegaFuente = bodega
                                break
                            }                            
                        } 
                    }
                   

                    //para bodega destino proximos a vencer
                    if(this.id_bodega_destino == 5){
                        for(const bodega of this.Lista_bodegas_fuente){
                            if((bodega.TipoBodega == 'M' || bodega.TipoBodega == 'D') && bodega.CODIGO != 5){
                                this.Lista_Bodegas_Fuente_Vencido.push(bodega)
                            }                         
                        }    
                    }                                    
                    
                    this.Consultar_OrdenEnc();                
                } else {
                    this.cod_bodegaFuente = '';
                    this.id_bodega_fuente = '';
                }
            },
            Emergente_Detalle(Datos, Operacion) {                
                this.Requisicion_seleccionada = Datos
                //this.cod_bodegaFuente = '';
                this.Limpiar_Campos();
                this.producto_buscar = '';
                this.Cantida_solicitar = '';
                this.Deshabilitar_bodega_fuente = false;
                if (Operacion == 'N') {                                        
                    this.Id_OrdenRequisicion = 0;
                    this.Id_Movimiento = 0;
                    this.Observaciones = '';
                    this.Deshabilitar_campos = true;
                    this.Descripcion_Emergente = 'Ingreso Solicitud Movimiento';
                    this.Lista_detalle = [];    
                    if(this.solicitud_bodega_vencido)
                        this.Cargar_Bodega_Vencido()                
                    else
                        this.Cargar_Bodega_Fuente()
                } else {
                    
                    
                    this.Id_OrdenRequisicion = Datos.IDREQUISICIONENC;
                    this.Id_Movimiento = Datos.IDMOVIMIENTOFK;
                    this.id_bodega_fuente = Datos.IdBodegaFuentefk;
                    this.id_bodega_destino = Datos.IdBodegaDestinofk;
                    this.Observaciones = Datos.OBSERVACIONES;
                    setTimeout(() => {
                        this.cod_bodegaFuente = {
                            NOMBRE: Datos.NOMBRE_BODEGA_FUENTE,
                            CODIGO: Datos.IdBodegaFuentefk,
                            TipoBodega: Datos.TIPO_BODEGA_FUENTE
                        }
                        this.cod_bodegaDestino = {
                            NOMBRE: Datos.NOMBRE_BODEGA_DESTINO,
                            CODIGO: Datos.IdBodegaDestinofk,
                            TipoBodega: Datos.TIPO_BODEGA_DESTINO
                        }
                    }, 500);                    
                    this.Deshabilitar_campos = true;
                    this.Deshabilitar_bodega_fuente = true;
                    this.Descripcion_Emergente = 'Editar Solicitud Movimiento';
                    this.Consultar_OrdenDet()
                }
                this.Estado_Emergente = true;
            },
            Cargar_Bodega_Fuente(){
                if(!this.Bodega_Fuente || Object.keys(this.Bodega_Fuente).length === 0){
                        this.id_bodega_fuente = '';
                        setTimeout(() => {
                            this.cod_bodegaFuente = {
                                NOMBRE: '',
                                CODIGO: '',
                                TipoBodega: ''
                            }
                        }, 500);

                        this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'No se cargo la bodega fuente',
                        });
                        return
                    }
                    this.id_bodega_fuente = this.Bodega_Fuente.CODIGO;
                    setTimeout(() => {
                        this.cod_bodegaFuente = {
                                NOMBRE: this.Bodega_Fuente.NOMBRE,
                                CODIGO: this.Bodega_Fuente.CODIGO,
                                TipoBodega: this.Bodega_Fuente.TipoBodega
                            }
                    }, 500);
            },
            Cargar_Bodega_Vencido(){
                if(!this.Bodega_Vencidos || Object.keys(this.Bodega_Vencidos).length === 0){
                        this.id_bodega_fuente = '';
                        setTimeout(() => {
                            this.cod_bodegaFuente = {
                                NOMBRE: '',
                                CODIGO: '',
                                TipoBodega: ''
                            }
                        }, 500);
                        this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'No se obtuvo la bodega para envio de vencidos',
                        });
                        return
                    }
                    this.id_bodega_fuente = this.Bodega_Vencidos.CODIGO;
                    setTimeout(() => {
                        this.cod_bodegaFuente = {
                                NOMBRE: this.Bodega_Vencidos.NOMBRE,
                                CODIGO: this.Bodega_Vencidos.CODIGO,
                                TipoBodega: this.Bodega_Vencidos.TipoBodega
                            }
                    }, 500);
            },
            Emergente_Detalle_Pedido(Datos) {
               // 
               // 
                this.Estado_Emergente_Recepcion = true;
                this.Requisicion_seleccionada = Datos
                this.Consulta_Pedido = true,
                this.Consulta_Aceptados = false,
                this.producto_buscar = '';
                this.Cantida_solicitar = '';
    
                this.Observaciones = Datos.OBSERVACIONES;
                this.Id_OrdenRequisicion = Datos.IDREQUISICIONENC;
                this.Estado_Requisicion = Datos.ESTADOREQUISICION;
                this.Id_Movimiento = Datos.IDMOVIMIENTOFK;
                this.id_bodega_fuente = Datos.IdBodegaFuentefk;
                this.id_bodega_destino = Datos.IdBodegaDestinofk;
                this.nombre_bodega_fuente = Datos.NOMBRE_BODEGA_FUENTE;
                this.nombre_bodega_destino = Datos.NOMBRE_BODEGA_DESTINO;
    
                this.Deshabilitar_campos = true;
                this.Descripcion_Emergente = 'Consulta Detalle Movimiento Pedido';
                this.Consultar_OrdenDet()
    
            },
            Emergente_Detalle_Recepcion(Datos) {
               // 
               // 
                this.Estado_Emergente_Recepcion = true;
                this.Requisicion_seleccionada = Datos
                this.Consulta_Pedido = false,
                this.Consulta_Aceptados = false,
                this.producto_buscar = '';
                this.Cantida_solicitar = '';
    
                this.Observaciones = Datos.OBSERVACIONES;
                this.Id_OrdenRequisicion = Datos.IDREQUISICIONENC;
                this.Estado_Requisicion = Datos.ESTADOREQUISICION;
                this.Id_Movimiento = Datos.IDMOVIMIENTOFK;
                this.id_bodega_fuente = Datos.IdBodegaFuentefk;
                this.id_bodega_destino = Datos.IdBodegaDestinofk;
                this.nombre_bodega_fuente = Datos.NOMBRE_BODEGA_FUENTE;
                this.nombre_bodega_destino = Datos.NOMBRE_BODEGA_DESTINO;
    
                this.Deshabilitar_campos = true;
                this.Descripcion_Emergente = 'Aceptación Despachos/Pedidos';
                this.Consultar_OrdenDet()
    
            },
            Emergente_Detalle_Aceptacion(Datos) {
               // 
               // 
                this.Estado_Emergente_Recepcion = true;
                this.Requisicion_seleccionada = Datos
                this.Consulta_Pedido = false,
                this.Consulta_Aceptados = true,
                this.producto_buscar = '';
                this.Cantida_solicitar = '';
    
                this.Observaciones = Datos.OBSERVACIONES;
                this.Id_OrdenRequisicion = Datos.IDREQUISICIONENC;
                this.Estado_Requisicion = Datos.ESTADOREQUISICION;
                this.Id_Movimiento = Datos.IDMOVIMIENTOFK;
                this.id_bodega_fuente = Datos.IdBodegaFuentefk;
                this.id_bodega_destino = Datos.IdBodegaDestinofk;
                this.nombre_bodega_fuente = Datos.NOMBRE_BODEGA_FUENTE
                this.nombre_bodega_destino = Datos.NOMBRE_BODEGA_DESTINO
    
                this.Deshabilitar_campos = true;
                this.Descripcion_Emergente = 'Consulta Detalle Movimiento Aceptado';
                this.Consultar_OrdenDet()
    
            },
            Limpiar_Campos() {
                this.Producto_seleccionado.Codigo = '';
                this.Producto_seleccionado.marca_comercial = '';
                this.Producto_seleccionado.Principio_activo = '';
                this.Producto_seleccionado.Concentracion = '';
                this.Producto_seleccionado.codigo_interno = '';
                this.Producto_seleccionado.Presentacion = '';
            },
            Seleccionar_producto(obj) {
    
                this.Producto_seleccionado.Codigo = obj.Codigo;
                this.Producto_seleccionado.marca_comercial = obj.Descripcion;
                this.Producto_seleccionado.Concentracion = obj.Concentracion;
                this.Producto_seleccionado.codigo_interno = obj.codigo_interno;
                this.Producto_seleccionado.Presentacion = obj.PresentacionNombre;
                document.getElementById("cantidad_sol").focus();
                this.Estado_VentanaEmergente_Busqueda = false;
    
            },
            Mostrar_resultado_producto(value,codigo_busqueda) {
                
                if (value.length === 1) {
                    if(codigo_busqueda != value[0].Codigo){
                        this.$vs.notify({
                            timer:6000,
                            position:'top-center',
                            color: 'warning',
                            title: 'Inventario',
                            text: `El código encontrado (${value[0].Codigo}) no corresponde exactamente al buscado.`,
                        })
                    }

                    value.map(obj => {
                        this.Producto_seleccionado.Codigo = obj.Codigo;
                        this.Producto_seleccionado.marca_comercial = obj.Descripcion;
                        this.Producto_seleccionado.Concentracion = obj.Concentracion;
                        this.Producto_seleccionado.codigo_interno = obj.codigo_interno;
                        this.Producto_seleccionado.Presentacion = obj.PresentacionNombre;                        
                        document.getElementById("cantidad_sol").focus();
    
                    })
    
                } else if (value.length > 1) {
                    var productoEncontrado = value.find(obj =>obj.Codigo == codigo_busqueda)
                    if(productoEncontrado){                        
                        this.Producto_seleccionado.Codigo = productoEncontrado.Codigo;
                        this.Producto_seleccionado.marca_comercial = productoEncontrado.Descripcion;
                        this.Producto_seleccionado.Concentracion = productoEncontrado.Concentracion;
                        this.Producto_seleccionado.codigo_interno = productoEncontrado.codigo_interno;
                        this.Producto_seleccionado.Presentacion = productoEncontrado.PresentacionNombre;
                        document.getElementById("cantidad_sol").focus();                             
                        return
                    }
                    this.Estado_VentanaEmergente_Busqueda = true;
                }
    
            },
            Validacion_Campos(Tipo, Nombre, valor, obligatorio, cant_maxima) {
                /**
                 * @General
                 * Tipo : N = Númerio, Nombre = Descripción del campo, Valor = A evaluar, Obligatorio= Verdadero o falso,cant_maxima  = Aplica unicamente para un string y define la cantidad de caracteres 
                 * Devuelve un mensaje al usuario si las validaciones no se cumple y aborta, de lo contrario devuelve verdadero
                 */
                if (Tipo == 'ID') {
                    if (valor <= 0) {
                        this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else if (Tipo == 'N') {
    
                    if (valor <= 0 || valor === "") {
                        this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else {
                    if (valor == '' && obligatorio == true) {
                        this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
    
                        if (cant_maxima < valor.length) {
                            this.$vs.notify({
                        position:'top-center',
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: 'Campo ' + Nombre + ' no debe superar los ' + cant_maxima + ' carácteres.',
                            });
                            return false;
                        } else {
                            return true;
                        }
    
                    }
                }
            },
            /*****************************CONSULTAR INFORMACION*************/
            Consultar_OrdenEnc() {
                if(!this.cod_bodegaDestino){
                    this.$vs.notify({
                        position:'top-center',
                                color: 'danger',
                                title: 'Inventario',
                                text: 'Seleccione una bodega destino',
                            })
                    return
                }
                
                var Operacion = 'P'
                if(this.Id_estado_seleccionado=='P')
                    Operacion = 'T'
                
                let tipoMovimiento = this.cod_bodegaDestino.CODIGO == 5 ? '13' : '4';

                this.axios.post('/app/v1_OrdenCompra/ConsultaRequisicion_Enc', {                        
                        Estado: this.Id_estado_seleccionado,
                        fecha_inicio: this.GetDateValue(this.fecha_inicio),
                        fecha_final: this.GetDateValue(this.fecha_final),
                        Operacion: Operacion,
                        Bodega_destino: this.cod_bodegaDestino.CODIGO,
                        TipoMovimiento: tipoMovimiento
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                                position:'top-center',
                                                color: '#B71C1C',
                                                title: 'Inventario',
                                                text: resp.data.mensaje,
                                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_vista = [];
                        } else {
                            this.$vs.notify({
                                                position:'top-center',
                                                color: 'success',
                                                title: 'Inventario',
                                                text: 'Se actualizaron los movimientos exitosamente',
                                            })
                            this.Lista_vista = resp.data.json;
    
                        }
                    })
                    .catch(() => {
                        this.Lista_vista = [];
                    })
            },
            Consultar_OrdenDet() {
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/ConsultaRequisicion_Det', {
                        Requisicion_enc: this.Id_OrdenRequisicion
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                        position:'top-center',
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_detalle = [];
                        } else {
                            this.Lista_detalle = resp.data.json.map(x => {
                                x.COSTO_PROMEDIO = Number(x.COSTO_PROMEDIO)
                                x.COSTO_PROMEDIO_TOTAL = Number(x.COSTO_PROMEDIO) * (['P','E'].includes(this.Id_estado_seleccionado)? Number(x.CANTIDAD_PEDIDA): Number(x.CANTIDAD_ENTREGA))
                                return x
                            })
                        }
                    })
                    .catch(() => {
                        this.Lista_detalle = [];
                    })
            },
            async Consultar_Bodega(Operacion, BodegasDespacho, BodegaTransito) {
                const url = this.$store.state.global.url
                await this.axios.post(url+'app/v1_OrdenCompra/consulta_bodega', {
                                                                        operacion: Operacion,
                                                                        BodegasDepacho: BodegasDespacho,
                                                                        BodegaTransito: BodegaTransito
                                                                     })
                           .then(resp => {
            
                                if (resp.data.codigo != 0) {
                                    this.$vs.notify({
                        position:'top-center',
                                        color: '#B71C1C',
                                        title: 'Bodegas',
                                        text: resp.data.mensaje,
                                    })
                                } else {
                                    if(Operacion == 'F'){
                                        this.Lista_bodegas_fuente = resp.data.json;  
                                        this.Bodega_Vencidos = this.Lista_bodegas_fuente.find((bodega)=>bodega.CODIGO == 5);                         
                                    }
                                    else if(Operacion == 'H')
                                        this.Lista_bodegas_destino_consulta = resp.data.json;                         
                                }
                            })
                           .catch(() => { })
        
            },
            async ConsultarSubBodega(Operacion,TipoBodegas) {
                if(!TipoBodegas || TipoBodegas.length == 0 ){return}

                const url = this.$store.state.global.url
                await this.axios.post(url+'app/v1_OrdenCompra/consulta_bodega', {
                                                                        operacion: Operacion,
                                                                        lista_tipo_bodegas: TipoBodegas
                                                                        })
                            .then(resp => {
            
                                if (resp.data.codigo != 0) {
                                    this.$vs.notify({
                                        position:'top-center',
                                        color: '#B71C1C',
                                        title: 'Bodegas',
                                        text: resp.data.mensaje,
                                    })
                                } else {
                                    this.Lista_bodegas_fuente =
                                        this.Lista_bodegas_fuente.concat(resp.data.json.filter(bodega=>bodega.BodegaFuente == 'S')) 

                                    this.Lista_bodegas_destino_consulta =
                                        this.Lista_bodegas_destino_consulta.concat(resp.data.json.filter(bodega=>bodega.BodegaFuente == 'N'))                  
                                
                                    this.Lista_bodegas_fuente.sort((a,b)=>Number(a.CODIGO)>=Number(b.CODIGO)?1:-1)
                                    this.Lista_bodegas_destino_consulta.sort((a,b)=>Number(a.CODIGO)>=Number(b.CODIGO)?1:-1)
                                }
                            })
                            .catch(() => { })
        
            },
            cargar_producto() {
    
                this.Limpiar_Campos();
                var res_variables = true;
                    res_variables = this.Validacion_Campos('ID', 'Bodega Destino', this.cod_bodegaDestino, true, 0);
                if (!res_variables){ return }
                    res_variables = this.Validacion_Campos('ID', 'Bodega Fuente', this.cod_bodegaFuente, true, 0);            
                if (!res_variables){ return }

                let tipoBodega = this.cod_bodegaDestino.CODIGO == 5 ? this.cod_bodegaFuente.TipoBodega : this.cod_bodegaDestino.TipoBodega


                this.axios.post('/app/v1_OrdenCompra/ConsultaRequisicion_Producto', {
                        Producto: this.producto_buscar.trim(),
                        TipoBodega: tipoBodega
                    })
                    .then(resp => {
                        
                        if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                            this.producto = []

                            if(resp.data.json.length==1 && resp.data.json[0].tipo_error == "-1"){
                                this.$vs.notify({
                                    time: 5000,
                                    position:'top-center',
                                    color: 'danger',
                                    title: 'Producto',
                                    text: resp.data.json[0].descripcion,
                                })
                                return
                            }

                            resp.data.json.map(data => {
                                this.producto.push({
                                    ...data
                                })
                            })
    
                            this.Mostrar_resultado_producto(this.producto,this.producto_buscar.trim());
    
                        } else {
    
                            this.producto = []
                            this.$vs.notify({
                                timer: 5000,
                                position:'top-center',
                                color: 'danger',
                                title: 'Producto',
                                text: (resp.data.codigo < 0) ? resp.data.descripcion : 'Código no existe, es un servicio ó no esta activo',
                            })
    
                        }
    
                    })
                    .catch(() => {
                        this.$vs.loading.close();
    
                    })
    
            },
            /*************************** ACTUALIZAR, ELIMINAR NUEVO REGISTRO */
            Registrar_NuevaOrdenEnc() {
                var res_variables = false;
                res_variables = this.Validacion_Campos('ID', 'Bodega Destino', this.id_bodega_destino, true, 0);
    
                if (res_variables)
                    res_variables = this.Validacion_Campos('ID', 'Bodega Fuente', this.id_bodega_fuente, true, 0);            

                if (res_variables)
                    res_variables = this.Validacion_Campos('ID', 'Codigo Producto', this.Producto_seleccionado.Codigo, true, 0);

                if (res_variables)
                    res_variables = this.Validacion_Campos('ID', 'Cantidad Solicitar', this.Cantida_solicitar, true, 0);
    
                if (res_variables) {
                    
                    let tipoMovimiento = this.cod_bodegaDestino.CODIGO == 5 ? '13' : '4';

                    this.axios.post('/app/v1_OrdenCompra/Requisicion_Enc', {
                            Requisicion_enc: 0,
                            Total: 0,
                            Cant_lineas: 0,
                            Observaciones: '',
                            Bodega_destino: this.cod_bodegaDestino.CODIGO,
                            Bodega_fuente: this.cod_bodegaFuente.CODIGO,
                            Estado: 'P',
                            Operacion: 'N',
                            TipoMovimiento: tipoMovimiento
                        })
                        .then(resp => {
    
                            if (resp.data.codigo == 0) {
                                this.InsertoEncabezado = true;
                                this.Id_OrdenRequisicion = resp.data.resultado;
                                this.Deshabilitar_campos = true;

                                this.Confirmacion_Transaccion();
    
                            }
                        })
                }else{
                    this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Verifique el producto y la cantidad a solicitar',
                        })
                    document.getElementById("busquedaProducto").focus();
                }
            },
            Registrar_NuevaOrdenDet() {               
                
                let codigoProducto = this.Producto_seleccionado.Codigo
                let idProducto = this.Lista_detalle.findIndex( (producto) => producto.IDPRODUCTOFK.trim()===codigoProducto.trim())
                                                                           
                if(idProducto >= 0){
                    this.$vs.dialog({
                        type: 'confirm',
                        color: 'danger',
                        title: 'Confirmación',
                        acceptText: 'Aceptar',
                        cancelText: 'Cancelar',
                        text: `¿Desea actualizar la cantidad solicitada del producto  ${this.Producto_seleccionado.Codigo} de ${this.Lista_detalle[idProducto].CANTIDAD_PEDIDA} a  ${this.Cantida_solicitar}?`,
                        accept: () => {       
                            this.IngresarDetalle()     
                        }
                    })
                }else{
                    this.IngresarDetalle()   
                }

                
            },
            IngresarDetalle(){
                var res_variables = false;
                    res_variables = this.Validacion_Campos('ID', 'Número Requisición', this.Id_OrdenRequisicion, true, 0);
    
                if (res_variables)
                    res_variables = this.Validacion_Campos('ID', 'Producto', this.Producto_seleccionado.Codigo, true, 0);
    
                if (res_variables)
                    res_variables = this.Validacion_Campos('N', 'Cantidad solicitar', this.Cantida_solicitar, true, 0);

                if (res_variables) {
                    this.axios.post('/app/v1_OrdenCompra/Requisicion_Det', {
                            Requisicion_det: 0,
                            Requisicion_enc: this.Id_OrdenRequisicion,
                            Producto: this.Producto_seleccionado.Codigo,
                            cant_pedida: this.Cantida_solicitar,
                            cant_entregada: 0,
                            cant_recibida: 0,
                            Operacion: 'N'
                        })
                        .then(resp => {
    
                            if (resp.data.codigo == 0) {
                                this.InsertoEncabezado = false;
                                this.Deshabilitar_bodega_fuente = true;
                                this.Consultar_OrdenDet();
                                this.producto_buscar = '';
                                this.Cantida_solicitar = '';
                                this.Limpiar_Campos();
                                document.getElementById("busquedaProducto").focus();
                            }
                        })
                }else{
                    this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Verifique el producto y la cantidad a solicitar',
                        })
                    document.getElementById("busquedaProducto").focus();
                }
            },
            Registrar_RecibidoDet(datos, operacion) {
                
                
               // 
    
                /* VALIDACION DE ARRAY */
    
                this.axios.post('/app/v1_OrdenCompra/Requisicion_Det', {
                        Requisicion_det: datos.IDREQUISICIONDET,
                        Requisicion_enc: datos.IDREQUISICIONENCFK,
                        Producto: datos.IDPRODUCTOFK,
                        cant_pedida: 0,
                        cant_entregada: 0,
                        cant_recibida: datos.CANTIDAD_RECIBIDA,
                        Operacion: operacion,
                    })
                    .then(resp => {
    
                        if (resp.data.codigo == 0) {
                            this.Consultar_OrdenDet();
                        }
                    })
    
            },
            Confirmacion_Transaccion() {            
                if (this.Id_OrdenRequisicion > 0) {
                    this.Registrar_NuevaOrdenDet();
                }else if(!this.InsertoEncabezado) {
                    this.Registrar_NuevaOrdenEnc();
                }else{
                    this.$vs.notify({
                                    color: 'danger',
                                    title: 'Error',
                                    text: 'Cierre esta ventana y busque la primer solicitud en estado Edición para continuar.',
                                    time: 3000,
                                    position: 'top-center'
                                })   
                }
    
            },
            Eliminar_Registro(value) {
                /**
                 * @General
                 * Función eliminar registro;
                 */
    
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',
                    acceptText: 'Aceptar',
                    cancelText: 'Cancelar',
                    text: '¿Dar de baja Pedido  \'' + value.DESCRIPCION_PRODUCTO + '\'   \'' + value.DESCRIPCION_UNIDAD + '\' ? ',
                    accept: () => {
    
                        this.axios.post('/app/v1_OrdenCompra/Requisicion_Det', {
                                Requisicion_det: value.IDREQUISICIONDET,
                                Requisicion_enc: this.Id_OrdenRequisicion,
                                Producto: '0',
                                cant_pedida: 0,
                                cant_entregada: 0,
                                cant_recibida: 0,
                                Operacion: 'B'
                            })
                            .then(resp => {
    
                                if (resp.data.codigo == 0) {
                                    this.Consultar_OrdenDet();
                                    document.getElementById("busquedaProducto").focus();
                                }
                            })
    
                    }
                })
    
            },
            Finalizar_Solicitud() {

                var res_variables = false;
                res_variables = this.Validacion_Campos('ID', 'Número Solicitud', this.Id_OrdenRequisicion, true, 0);
    
                if (res_variables) {
                    if (this.Lista_detalle.length <= 0) {
                        this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Debe tener al menos un producto requerido',
                        })
                        return;
                    }
    
                }
    
                if (res_variables) {

                    
    
                    this.axios.post('/app/v1_OrdenCompra/Requisicion_Enc', {
                            Requisicion_enc: this.Id_OrdenRequisicion,
                            Total: 0,
                            Cant_lineas: this.Lista_detalle.length,
                            Observaciones: this.Observaciones,            
                            Estado: 'E',
                            Operacion: 'F',
                        })
                        .then(resp => {                            
                            if (resp.data.codigo != 0) {
                                    //Prueba mensaje
                            } else {
                                this.Estado_Emergente = false;
                                this.Consultar_OrdenEnc();
                            }
                        })
                }
            },
            MovimientoRecepcion() {
    
                this.axios.post('/app/v1_OrdenCompra/Requisicion_Enc', {
                        Requisicion_enc: this.Id_OrdenRequisicion,
                        Total: 0,
                        Cant_lineas: 0,
                        Observaciones: '',
                        Bodega_solicitante: 0,
                        Bodega_destino: 0,
                        Estado: 'F',
                        Operacion: 'R',
                    })
                    .then(resp => {
    
                        if (resp.data.codigo == 0) {
                            this.Estado_Emergente_Recepcion = false;
                            this.Consultar_OrdenEnc();
                        }
                    })
    
            }
        }
    
    }
    </script>
<style>
.inventario-popup.con-vs-popup .vs-popup {
    width: 90% !important;
}

tfoot .foot {
  border-top: 3px dotted rgb(160 160 160);
  background-color: #2c5e77;
  color: #fff;
}
</style>
<style scoped>

tfoot th {
  text-align: right;
}

tfoot td {  
  font-weight: bold;
}
</style>