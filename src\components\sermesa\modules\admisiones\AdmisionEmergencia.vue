<!-- AdmisionEmergencia.vue -->
<template>
    <div class="container-fluid">
        <vx-card title="Creación Admisión">

            <ValidationObserver ref="adressesData" v-slot="{ invalid, handleSubmit }" mode="lazy">
                <form @submit.prevent="handleSubmit(submitForm(submit))">



                    <!-- admisionesContainer  -->
                    <div class="admisionesContainer" style="margin-top: -30px;">

                        <div class="infoAdmisionL">

                            <div class="serie-codigo-admision">
                             <SerieCodigoAdmision
                               @selected-option-changed="onSelectedOptionChanged"
                               v-model="AdmisionData.TipoAdmision">
                               </SerieCodigoAdmision>
                            </div>

                        </div>


                        <div class="infoAdmisionR">

                            <vs-row>
                                <h4 class="icon-bubbles">Tipo Admisión: </h4>
                                <strong>{{ DescripcionAdmision }}</strong>
                            </vs-row>
                            <vs-row>
                                <h4 class="icon-bubbles"> Codigo Admisión: </h4>
                                <strong>{{ AdmisionData.Admision }}</strong>
                            </vs-row>

                        </div>

                        <!-- Paciente -->
                        <div class="datosPaciente">
                            <div class="container-fluid flex flex-wrap">
                                <vs-row>

                                    <vs-col vs-type="flex" vs-w="6">
                                        <div class="icon-wrapper">
                                            <label for="NombrePaciente"> NombrePaciente</label>
                                            <h6 class="icon-bubbles">{{ NombrePaciente }}</h6>
                                        </div>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-w="2">
                                        <div class="icon-wrapper">
                                            <label for="Nacimiento"> Nacimiento</label>
                                            <h6 class="icon-bubbles">{{ PacienteData.Nacimiento }}</h6>
                                        </div>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-w="2">
                                        <div class="icon-wrapper">
                                            <label for="Edad"> Edad</label>
                                            <!-- <h6 class="icon-bubbles">{{15 }}</h6> -->
                                        </div>
                                    </vs-col>
                                </vs-row>

                                <vs-row>
                                    <vs-col vs-type="flex" vs-w="2">
                                        <div class="icon-wrapper">
                                            <label for="Celular"> Celular</label>
                                            <h6 class="icon-bubbles">{{ PacienteData.Celular }}</h6>
                                        </div>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-w="4">
                                        <div class="icon-wrapper">
                                            <label for="TelefonoREsidencial"> Tel. Residencial</label>
                                            <h6 class="icon-bubbles">{{ PacienteData.Telefonos }}</h6>
                                        </div>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-w="4">
                                        <div class="icon-wrapper">
                                            <label for="Correo Electronico"> Correo Electrónico</label>
                                            <h6 class="icon-bubbles">{{ PacienteData.Email }}</h6>
                                        </div>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-w="2">
                                        <div class="icon-wrapper">
                                            <label for="Edad"> Edad</label>
                                            <h6 class="icon-bubbles">{{ }}</h6>
                                        </div>

                                    </vs-col>
                                </vs-row>

                                <vs-row>
                                    <vs-col vs-type="flex" vs-w="2">
                                        <div class="icon-wrapper">
                                            <label for="Celular"> Documento Facturación</label>
                                            <h6 class="icon-bubbles">{{ PacienteData.Celular }}</h6>
                                        </div>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-w="2">
                                        <div class="icon-wrapper">
                                            <label for="TelefonoREsidencial"> Número Documento</label>
                                            <h6 class="icon-bubbles">{{ PacienteData.Telefonos }}</h6>
                                        </div>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-w="4">
                                        <div class="icon-wrapper">
                                            <label for="Correo Electronico"> Nombre Facturacion</label>
                                            <h6 class="icon-bubbles">{{ PacienteData.NombreFactura }}</h6>
                                        </div>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-w="4">
                                        <div class="icon-wrapper">
                                            <label for="Correo Electronico"> Dirección Facturacion</label>
                                            <h6 class="icon-bubbles">{{ PacienteData.DireccionFactura }}</h6>
                                        </div>
                                    </vs-col>

                                </vs-row>
                            </div>
                        </div>
                        <!-- Paciente -->

                        <!-- Vacunas covid -->
                        <div class="datosVacunasCovid">
                            <div class="container-fluid ">


                                <div class="vertical-center">

                                    <DiagnosticoCovid 
                                        @checked-options-changed="onCheckedOptionChanged"
                                        @vacunas-seleccionadas="obtenerListaVacunas"
                                        :id-paciente="parseInt(PacienteData.Codigo)">
                                    </DiagnosticoCovid>
                                </div>

                            </div>
                        </div>
                        <!-- Vacunas covid -->

                        <!-- datosPolizas -->
                        <div class="datosPolizas">


                            <vs-row>
                                <TiposDescuento @selected-tipodes-changed="onSelectedTipoChanged"
                                    v-model="AdmisionData.PaqueteQx" 
                                    :tipo-admision="parseInt(TipoAdmision)">
                                </TiposDescuento>
                            </vs-row>


                        </div>

                        <!-- datosPolizas -->

                        <!-- datosAdmision -->
                        <div class="datosAdmision">
                            <div class="container-fluid" style="vertical-align: middle; margin-top: -15px;">
                                <vs-input ID="CodigoMedico" v-model="Codigo" type="hidden" />
                                <vs-row>
                                    <vs-col vs-w="8">

                                        <vs-col vs-w="4">
                                            <ValidationProvider name="Codigo" rules="required|numero_entero|numero_min:1"
                                                v-slot="{ errors }" class="required">
                                                <SM-Buscar 
                                                    v-model="Codigo" 
                                                    label="Código" api="app/Ajenos/Busqueda_Ajenos"
                                                    :api_campo_respuesta_mostrar="['Nombre', 'Apellido']"
                                                    :api_campos="['Codigo', 'Nombre', 'Apellido', 'Corporativo', 'Tipo']"
                                                    :api_titulos="['Codigo', 'Nombre', 'Apellido', 'Corporativo', 'Tipo']"
                                                    api_campo_respuesta="Codigo" :callback_buscar="DatosMedicos().Consultar"
                                                    :dangertext="(errors.length > 0) ? errors[0] : null" :api_preload="true"
                                                    :disabled_texto="true" />
                                            </ValidationProvider>
                                        </vs-col>
                                        <vs-col vs-w="8">
                                            <validationProvider name="NombreMedico">
                                                <vs-input v-model="NombreMedico" label="Nombre Medico" class="w-full" />
                                            </validationProvider>

                                        </vs-col>

                                    </vs-col>

                                </vs-row>
                            </div>
                        </div>
                        <!-- datosAdmision -->
                    </div>
                    <!-- admisionesContainer  -->

                    <div v-show="showButtons">
                        <vs-button style="margin-right:1px" v-on:click="handleSubmit(submit())" :disabled="invalid">
                            Guardar
                        </vs-button>
                    </div>


                </form>
            </ValidationObserver>
        </vx-card>
    </div>
</template>


<script>

import { mapFields } from "vuex-map-fields";
import SerieCodigoAdmision from "/src/components/sermesa/modules/admisiones/SerieCodigoAdmision";
import DiagnosticoCovid from "/src/components/sermesa/modules/admisiones/DiagnosticoCovid";
import TiposDescuento from "/src/components/sermesa/modules/admisiones/TiposDescuento";



export default {
    name: "AdmisionEmergencias",
    components: {
        //Multiselect,
        SerieCodigoAdmision,
        DiagnosticoCovid,
        TiposDescuento
    },
    data: () => ({
        decisions: ["Yes", "No", "Undecided"],
        selected: "",
        showButtons: true,
        seguros: false,
        paquete: true,
        PolizasPaquetes: '',
        Codigo: '',
        NombreMedico: '',
        DisgnosticoCovid: [],
        Afiliado: '',
        CodigoPaquete: '',
        NombrePaciente: '',
        DescripcionAdmision: '',
        TipoAdmision: 0,
        groupNameSerieCodigo: 'serieCodigo',
        groupNameTiposDescuento: 'tiposDescuento'


    }),
    mounted() {
  
    },
    created() {

    },
    computed: {
        ...mapFields('admision', ["AdmisionData"]),
        ...mapFields('paciente', ["PacienteData"])

    },
    activated() {
        this.NombrePaciente = this.$store.getters['paciente/getnombrePaciente']
        this.DescripcionAdmision = this.$store.getters['admision/getDescAdmision'] ? this.$store.getters['admision/getDescAdmision'] : ''
    },
    methods: {
        async submit() {
            this.AdmisionData.PrimeraVacunaCOVID = this.selPrimeraVacuna?.Codigo || 0
            this.AdmisionData.SegundaVacunaCOVID = this.selSegundaVacuna?.Codigo || 0
            this.AdmisionData.TerceraVacunaCOVID = this.selTerceraVacuna?.Codigo || 0
            this.AdmisionData.Paciente = this.PacienteData.Codigo
            this.AdmisionData.IdCliente = this.PacienteData.IdCliente
            this.AdmisionData.NivelPrecios = 1

            this.$store.dispatch('admision/crearAdmision').catch()

        },
        async Consultarmedicos() {
            const resp = await this.axios.post('/app/Ajenos/Busqueda_Departamentos', {
                IdDepartamento: '',
            })
            this.ListaDepartamentos = resp.data.json;

            const tDeptos = resp.data.json.map(m => {
                return {
                    ...m,
                }
            })
            var parsedData = JSON.stringify(tDeptos);
            const jsonData = JSON.parse(parsedData);
            const v_depto = '' + this.PacienteData.DeptoResidencia
            const val = jsonData.find(x => x.IdDepartamento === v_depto).Departamento;

            this.selDepartamento = {
                Departamento: val
            }

        },
        DatosMedicos() {
            return {

                Consultar: (datos) => {
                    this.NombreMedico = datos.Nombre.trim() + ' ' + datos.Apellido.trim()

                },
            }
        }
        , cambioPolizaPaquete() {
            switch (this.PolizasPaquetes) {
                case 'Q':
                    this.seguros = false
                    this.paquete = true
                    break
                case 'S':
                    this.seguros = true
                    this.paquete = false
                    break
                default:
                    this.seguros = false
                    this.paquete = false
                    break

            }

        },
        onSelectedOptionChanged(admision) {
          this.DescripcionAdmision = admision.DescripcionAdmision;
            this.TipoAdmision = admision.TipoAdmision


        },
        onCheckedOptionChanged(diagnostico) {
            this.DisgnosticoCovid = diagnostico
            this.AdmisionData.DiagnosticoCovid = (diagnostico.length === 0) ? 0 : 1
        },

    }
}
</script>

<style scoped>
* {
    box-sizing: border-box;
    padding: 2px;
    margin: 0;
}


.admisionesContainer {
    display: grid;
    grid-gap: 5px;
    width: 100%;
    height: 100%;
    grid-template-areas: "infoAdmisionL infoAdmisionR"
        "datosPaciente datosPaciente"
        "datosVacunasCovid datosVacunasCovid"
        "datosPolizas datosPolizas"
        "datosAdmision datosAdmision";
    grid-template-columns: 70% 30%;
    grid-template-rows: 18% 32% 15% 20% 20%;
}

.admisionesContainer>div {
    border: 1px solid #888;
}

.infoAdmisionL {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100%;
}

.infoAdmisionR {
    grid-area: infoAdmisionR;
}

.datosPaciente {
    grid-area: datosPaciente;

}

.datosVacunasCovid {
    grid-area: datosVacunasCovid;
}

.datosPolizas {
    grid-area: datosPolizas;
    display: grid;
    align-items: center;

}


.datosAdmision {
    grid-area: datosAdmision;
}

#inner-grid {
    display: grid;
    grid-template-columns: 1fr;
    width: 100%;
}

#inner-grid>div {
    background: rgb(248, 251, 252);
    padding: 20px;
    border-radius: .25rem;
    margin-top: 1px;
    margin-bottom: 1px;
}

.serie-codigo-admision {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

strong,
h6 {
    display: block;
    float: left;
    clear: left;
    color: #5ca0e4;

}


label {
    margin-right: 20px;
    margin-left: 20px;
    vertical-align: middle;

}

input[type="radio"] {
    margin-right: 20px;
    margin-left: 15px;
}

.element {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
}

.vertical-center:before {
    margin: 0;
    position: absolute;
    top: 50%;
    transform: translateY(-50%)
}

.horizontal-center:before {
    display: flex;
    justify-content: center;

}

.test {
    /* We first create a flex layout context */
    display: flex;
    /* Then we define the flow direction and if we allow the items to wrap */
    flex-flow: row wrap;
    /* Then we define how is distributed the remaining space */
    justify-content: space-around;

}

.test>div {
    margin: 5px;
    padding: 5px;
}
</style>