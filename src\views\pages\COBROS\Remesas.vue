<template>
<div class="remesas-container">
    <!-- Busqueda de cheques con remesas -->
    <div class="mb-5 my-sticky-top">
        <SM-Buscar class="buscador-remesa vs-fixed-dx-input-container " ref="refBuscadorRemesa" v-model="remesa" label="No. Lote" api="app/v1_cobros/BusquedaRemesaCheque" :api_titulos="[
             'Remesa#'
            ,'Número Cheque'
            ,'Cuenta Cheque'
            ,'#Fecha Emisión'
            ,'#Banco Emisor'
            ,'#Aseguradora'
            ,'Monto']" api_campo_respuesta="Remesa" :api_campos="[ 
            'Remesa'
            ,'ChequeNumero'
            ,'CuentaCheque'
            ,'FechaEmisionDMY'
            ,'BancoEmisor'
            ,'Aseguradora'
            ,'Monto']" :api_campo_respuesta_estricto="false" :callback_buscar="CargarRemesa" :callback_nuevo="Nuevo" :callback_cancelar="Cancelar" :disabled_texto="false" :bloqueo_agregar="false"
            :disabled_search_input="inhabilitaBuscador">
            <template #buttons>

                <vs-dropdown class="ml-1" :vs-trigger-click="true" v-if="formData.Codigo">
                    <vs-button class="btn-drop" type="filled" icon-pack="far" icon="fa-edit">
                        <vs-tooltip text="Editar campos de la remesa">
                            Editar
                        </vs-tooltip>
                    </vs-button>

                    <vs-dropdown-menu>
                        <vs-dropdown-item @click="Editar(MODOS.Editar)">
                            Responsable y observaciones
                        </vs-dropdown-item>
                        <vs-dropdown-item @click="RegistrarDeposito">
                            Registrar depósito
                        </vs-dropdown-item>
                        <vs-dropdown-item @click="EditarObsLiquidador">
                            Observaciones Liquidación
                        </vs-dropdown-item>
                    </vs-dropdown-menu>
                </vs-dropdown>

                <vs-button class="ml-1" color="dark" type="border" icon-pack="fas" icon="fa-table" v-if="callbackReimpresion" @click="callbackReimpresion">
                    <vs-tooltip text="Revisión Remesa">
                        Revisión
                    </vs-tooltip>
                </vs-button>
                <vs-button class="ml-1" color="warning" icon-pack="fas" icon="fa-print" v-if="callbackValidacion" @click="callbackValidacion">
                    <vs-tooltip text="Reimpresión de Remesa">
                        Reimpresión de Remesa
                    </vs-tooltip>
                </vs-button>
            </template>
        </SM-Buscar>
        <div class="navigation-buttons flex flex-row">
            <vs-button class="ml-1 text-lg" type="filled" color="rgb(110, 110, 110)" size="small" @click="Navegar( 0, 1)">
                <vs-tooltip text="Primero">
                    <font-awesome-icon :icon="['fas', 'backward-step']" />
                </vs-tooltip>
            </vs-button>
            <vs-button v-if="formData.Codigo" class="ml-1 text-xl" type="filled" color="rgb(110, 110, 110)" size="small" @click="Navegar(remesa, -1)">
                <vs-tooltip text="Anterior">
                    <font-awesome-icon :icon="['fas', 'caret-left']" />
                </vs-tooltip>
            </vs-button>
            <vs-button v-if="formData.Codigo" class="ml-1 text-xl" type="filled" color="rgb(110, 110, 110)" size="small" @click="Navegar(remesa, 1)">
                <vs-tooltip text="Siguiente">
                    <font-awesome-icon :icon="['fas', 'caret-right']" />
                </vs-tooltip>
            </vs-button>
            <vs-button class="ml-1 text-lg" type="filled" color="rgb(110, 110, 110)" size="small" @click="Navegar(999999, -1)">
                <vs-tooltip text="Último">
                    <font-awesome-icon :icon="['fas', 'forward-step']" />
                </vs-tooltip>
            </vs-button>
        </div>
    </div>
    <form @submit="handleSubmit">
        <dx-form 
            class="remesas-form" 
            ref="formRemesas" 
            :show-validation-summary="true"
            :form-data.sync="formData" 
            :items="formItems" 
            label-location="top" 
            label-mode="outside" 
            validation-group="validacionRemesa" 
            :col-count="2" :col-count-by-screen="{sm:1, md:2}" 
            :disabled="false" 
            :customize-item="customizeItem" 
            :read-only="modo == MODOS.Lectura">
            <template #detalle-cheques>
                <div class="p-1" :style="{ border: borderStyle, borderRadius:'5px' }">
                    <DxValidator :adapter="adapterConfig" validation-group="validacionRemesa">
                        <DxRequiredRule message="Ingrese un detalle de la remesa" />
                    </DxValidator>
                    <RemesasCreditos v-bind:remesa="formData" modo="NUEVO" @update="onDetalleChange" @loaded-bancos="CargarBancos" :catalogo-bancos="catalogoBancos" @loaded-asegura="CargarAsegura" :catalogo-aseguradoras="catalogoAsegura" />
                </div>
            </template>
        </dx-form>
    </form>
    <!-- Se agregó otro componente para prevenir la racarga de datos del detalle cada vez que se repinta el form -->
    <RemesasCreditos ref="refCheques" v-if="modo!=MODOS.Nuevo" v-show="modo == MODOS.Lectura" v-bind:remesa="formData" modo="LECTURA" v-model="formData.Detalle" @loaded-bancos="CargarBancos" :catalogo-bancos="catalogoBancos" @loaded-asegura="CargarAsegura" :catalogo-aseguradoras="catalogoAsegura" />
</div>
</template>

<script>
import {
    srtLenRule
} from './data'

import {
    dateOptions
} from './data'

import 'devextreme-vue/text-area'
import RemesasCreditos from './RemesasCreditos.vue'


import Swal from 'sweetalert2'

const cuentas = []
const MODOS = {
    Lectura: 0,
    Nuevo: 1,
    Editar: 2, 
    EditarDeposito: 3,
    EditarObsLiquidador: 4,
}
const endpoint = {
    Lectura: '',
    Nuevo: 'RemesaGrabar',
    Editar: 'RemesaActualizar', 
    EditarDeposito: 'RemesaActualizarDeposito',
    EditarObsLiquidador: 'RemesaActualizarObsLiquidador',
}
const pathPermiso = '/COBROS/COB009'

export default {
    name: 'Remesas',
    components: {
        RemesasCreditos,
    },
    data() {
        //Adapter del validador para editores personalizados -->procedimientos
        const callbacks = [];
        const adapterConfig = {
            getValue: () => {
                return (this.formData.Detalle && this.formData.Detalle.length > 0) ? 'OKIS' : null
            },
            applyValidationResults: (e) => {
                this.borderStyle = e.isValid ? "none" : "2px solid rgba(217, 83, 79, .4)"
            },
            validationRequestsCallbacks: callbacks,
        };
        const revalidate = () => {
            callbacks.forEach((func) => {
                func()
            })
        }

        ///validador personalizado para la cuenta
        const cuentaRule = {
            type: 'custom',
            reevaluate: true,
            validationCallback: (e) => {
                if ( !this.formData.CuentaBanco && (this.formData.DepositoNumero || this.formData.FechaDeposito )
                    
                ) {
                    e.rule.message = 'Debe indicar el número de cuenta'
                    return false
                } else {
                    e.rule.message = ''
                    return true
                }
            }
        }

        const depositoRule = {
            type: 'custom',
            reevaluate: true,
            validationCallback: (e) => {
                if ( e.value || !this.formData.FechaDeposito )
                {
                    e.rule.message = ''
                    return true
                } else {
                    e.rule.message = 'Debe indicar el número de deposito'
                    return false
                }
            }
        }

        const depositoRuleFecha = {
            type: 'custom',
            reevaluate: true,
            validationCallback: (e) => {
                if ( e.value || !this.formData.DepositoNumero ) {
                    e.rule.message = ''
                    return true
                } else {
                    e.rule.message = 'Debe indicar la fecha de depósito'
                    return false
                }
            }
        }

        return {
            modo: MODOS.Lectura,
            MODOS,
            pathPermiso,
            remesa: null, //para la busqueda smbuscar
            formData: null,
            tempFormData: null,//temporal para deahacer cambios en el formulario
            formItems: [
                {
                    itemType: 'group',
                    //caption: 'Remesa',
                    colCount: 2,
                    colCountByScreen: {xs: 2},
                    items: [
                        {
                            label: {
                                text: 'No. Lote'
                            },
                            dataField: 'Codigo',
                            dataType: 'number',
                            editorType: 'dxNumberBox',
                            colSpan: 1,
                            editorOptions: {
                                readOnly: true,
                            }
                        },
                        {
                            dataField: 'Fecha',
                            colSpan: 1,
                            ...dateOptions,
                            validationRules: [{
                                type: 'required'
                            }, ],
                            editorOptions: {
                                readOnly: true,
                                ...dateOptions.editorOptions
                            }
                        },
                        {
                            dataField: 'Responsable',
                            dataType: 'string',
                            editorType: 'dxTextBox',
                            editorOptions: {
                                
                            },
                            validationRules: [srtLenRule('Responsable', 0, 40), {type: 'required', message: 'Especifique el responsable'}],
                            modoReadOnly: [
                                true,
                                false,
                                false,
                                true,
                                true,
                            ],
                        },
                        {
                            dataField: 'Entrega',
                            dataType: 'string',
                            editorType: 'dxTextBox',
                            editorOptions: {
                                
                            },
                            validationRules: [srtLenRule('Entrega', 0, 40)],
                            
                            modoReadOnly: [
                                true,
                                false,
                                false,
                                true,
                                true,
                            ]
                        },
                    ]
                },
                    
                {
                    itemType: 'group',
                    colCount: 2,
                    //caption: 'Depósito',
                    items: [{
                            label: {
                                text: 'Cuenta'
                            },
                            colSpan: 2,
                            dataField: 'CuentaBanco',
                            dataType: 'string',
                            editorType: 'dxSelectBox',
                            editorOptions: {
                                dataSource: cuentas,
                                searchEnabled: true,
                                valueExpr: 'Codigo',
                                displayExpr: (x) => x ? ''.concat(x ?.Codigo, ' - ', x ?.Cuenta) : '',
                                onSelectionChanged: (e) => {
                                    this.formData.CuentaNumero = e.selectedItem ?.Cuenta
                                },
                            },

                            validationRules: null,
                            modoRules: [
                                null,
                                [srtLenRule('La cuenta de banco', 0, 2), cuentaRule],
                                null,
                                [srtLenRule('La cuenta de banco', 0, 2), {type: 'required', message:'Ingrese el número de cuenta'}, cuentaRule],
                                null,
                            ],
                            modoReadOnly: [
                                true,
                                false,
                                true,
                                false,
                                true,
                            ],

                        },
                        {
                            label: {
                                text: 'Depósito'
                            },
                            dataField: 'DepositoNumero',
                            dataType: 'number',
                            editorType: 'dxNumberBox',
                            editorOptions: {
                                format: '#'
                            },
                            validationRules: null,
                            modoRules: [
                                null,
                                [{type: 'range', min: 0, max: 2147483647, message: 'Debe tener un valor entre 0 y 2,147,483,647'}, depositoRule],
                                null,
                                [{type: 'required', message:'Ingrese el número de depósito'},{type: 'range', min: 0, max: 2147483647, message: 'Debe tener un valor entre 0 y 2,147,483,647'}, depositoRule],
                                null,
                            ],
                            modoReadOnly: [
                                true,
                                false,
                                true,
                                false,
                                true,
                            ],
                        },
                        {
                            label: {
                                text: 'Fecha Depósito'
                            },
                            dataField: 'FechaDeposito',
                            validationRules: null,
                            ...dateOptions,

                            modoRules: [
                                null,
                                [depositoRuleFecha],
                                null,
                                [{type: 'required', message:'Ingrese la fecha del depósito'},depositoRuleFecha],
                                null,
                            ],
                            modoReadOnly: [
                                true,
                                false,
                                true,
                                false,
                                true,
                            ],
                        },
                    ],
                },
                    
                {
                    label: {
                        text: 'Observaciones'
                    },
                    dataField: 'ObservacionesdeControl',
                    dataType: 'string',
                    editorType: 'dxTextArea',
                    editorOptions: {
                        height: 150,
                    },
                    validationRules: [srtLenRule('ObservacionesdeControl', 0, 2147483647)],
                    modoReadOnly: [
                        true,
                        false,
                        false,
                        true,
                        true,
                    ],
                },
                {
                    dataField: 'ObservacionesLiquidador',
                    dataType: 'string',
                    editorType: 'dxTextArea',
                    editorOptions: {
                        height: 150,
                    },
                    validationRules: null,
                    modoRules: [
                        null,
                        [srtLenRule('ObservacionesdeControl', 0, 2147483647)],
                        null,
                        null,
                        [srtLenRule('ObservacionesdeControl', 0, 2147483647), {type:'required', message: 'Ingrese una observación' }]
                    ],
                    modoReadOnly: [
                        true,
                        true,
                        true,
                        true,
                        false,
                    ],
                },
                {
                    itemType: 'group',
                    colSpan: 2,
                    items:[
                        {
                            colSpan: 2,
                            name: 'detalleCheques',
                            dataField: 'Detalle',
                            template: 'detalle-cheques',
                            label: {location: 'top', visible:false},
                            validationRules: [
                                {
                                    type: 'custom',
                                    reevaluate: true,
                                    validationCallback: (e) => {
                                        if (this.formData.Detalle.length) {
                                            e.rule.message = 'Ingrese el detalle de la remesa'
                                            return false
                                        }
                                        e.rule.message = ''
                                        return true
                                    }
                                }
                            ],
                        },
                    ]
                },
                //////Botones grabar y cancelar
                {
                    cssClass: 'eyoeyo',
                    colSpan: 2,
                    colCount: 2,
                    colCountByScreen: {
                        xs: 2,
                        sm: 2
                    },
                    itemType: 'group',
                    items: [{
                            name: 'grabar',
                            cssClass: 'mysticky',
                            colSpan: 1,
                            itemType: 'button',
                            horizontalAlignment: 'right',
                            buttonOptions: {
                                text: 'Guardar',
                                type: 'success',
                                icon: 'save',
                                useSubmitBehavior: false,
                                onClick: () => {
                                    const r = this.formInstance.validate()
                                    if (r.isValid)
                                        this.Grabar()
                                },
                                disabled: false,
                            },
                        },
                        {
                            name: 'cancelar',
                            cssClass: 'mysticky',
                            colSpan: 1,
                            itemType: 'button',
                            horizontalAlignment: 'left',
                            buttonOptions: {
                                text: 'cancelar',
                                type: 'danger',
                                icon: 'close',
                                useSubmitBehavior: false,
                                disabled: false,
                                onClick: () => {
                                    this.Cancelar()
                                }
                            },
                        },
                    ]
                },
            ],
            ///adapter validators para compoente incrustado en form
            adapterConfig,
            revalidate,
            borderStyle: "none",
            ///catalogos de componente detalle de cheques
            catalogoBancos: null,
            catalogoAsegura: null,
            inhabilitaBuscador: false,
        } //return data
    }, //data
    methods: {
        handleSubmit(e) {
            e.preventDefault()
            this.Grabar()
        },
        customizeItem(e) {

            if (['grabar', 'cancelar'].includes(e.name))
                e.visible = this.modo != MODOS.Lectura

            if (e.name == 'detalleCheques') {
                e.visible = this.modo == MODOS.Nuevo
            }

            if (['CuentaBanco', 'DepositoNumero', 'FechaDeposito', 'Responsable', 'Entrega', 'ObservacionesdeControl', 'ObservacionesLiquidador'].includes(e.dataField)) {

                if (e.modoReadOnly)
                    e.editorOptions.readOnly = e.modoReadOnly[this.modo]

                if (e.modoRules)
                    e.validationRules = e.modoRules[this.modo]
            }

        },

        onDetalleChange(e) {
            this.formData.Detalle = e
        },
        Grabar() {
            if (this.modo)
                this.axios.post('/app/v1_cobros/'.concat(Object.values(endpoint)[this.modo]),
                    {
                        Remesa: {
                            ...this.formData,
                            Codigo: this.modo==1? 0: this.formData.Codigo,
                        },
                        Cheques: this.formData.Detalle,
                    }
                ).then((resp)=>{
                    
                    if(this.modo == MODOS.Nuevo)
                        this.Reimprimir(resp.data.CodigoRemesa)

                    if(this.modo == MODOS.EditarDeposito) {
                        this.remesa = null
                        return
                    }
                    this.modo = MODOS.Lectura
                    this.remesa = resp.data.CodigoRemesa
                    
                })
            
        },
        CargarRemesa(e) {
            this.modo = MODOS.Lectura
            if (e && e.Codigo)
                this.formData = e
            else
                this.axios.post('/app/v1_cobros/BusquedaRemesaCheque', {
                    Remesa: this.remesa
                }).then(resp => {
                    this.formData = resp.data.json[0]
                    this.formData.Detalle = []
                })
        },
        Navegar(codigo, direccion) {
            
            this.axios.post('/app/v1_cobros/BusquedaRemesaCheque', {
                Remesa: codigo,
                Navegacion: direccion
            }).then(resp => {
                if (direccion && resp.data.json.length == 0) {
                    return
                }
                this.modo = MODOS.Lectura
                ///pequeño truiquillo para que no mande la segunda peticion del sm buscar
                this.inhabilitaBuscador = true
                this.remesa = resp.data.json[0].Codigo
                this.formData = resp.data.json[0]
                this.formData.Detalle = []
                this.$nextTick(()=>{
                    this.inhabilitaBuscador = false
                })
                
            })
        },
        //para que el componente hijo Remesas creditos no este cargando multiples veces el catalogo mientas el dx form se actualiza 
        CargarBancos(e) {
            this.catalogoBancos = e
        },
        CargarAsegura(e) {
            this.catalogoAsegura = e
        },
        async Nuevo() {
            this.Limpiar()
            this.modo = MODOS.Nuevo
            this.formData.Fecha = await this.$dbdate()
            this.formData.Tipo = 'R'
        },
        Editar(seccion) {
            this.modo = seccion
            this.tempFormData = {
                ...this.formData
            }
            this.formInstance.repaint()
        },
        RegistrarDeposito() {
            if (this.formData.CuentaBanco && this.formData.DepositoNumero && this.formData.FechaDeposito)
                Swal.fire({
                    icon: 'warning',
                    title: 'La remesa ya tiene registro de depositada',
                    allowOutsideClick: false,
                    confirmButtonText: 'Aceptar',
                })
            else
                this.$validar_func_usuario_permiso(pathPermiso, 'REGISTRAR_DEPOSITO').then((x) => {
                    this.corporativoAutoriza = x.corporativo
                    this.Editar(MODOS.EditarDeposito)
                })
        },
        EditarObsLiquidador() {
            if ( this.formData.Detalle?.filter(x=> x.UsuarioLiquidador == this.$store.state.sesion.corporativo).length == 0 ) {
                Swal.fire({
                    icon: 'warning',
                    title: 'No es posible editar las observaciones',
                    text: 'No hay registro de que usted haya Liquidado algún cheque de ésta Remesa por lo tanto no puede registrar Observaciones...',
                    allowOutsideClick: false,
                    confirmButtonText: 'Aceptar',
                })
            } else
                this.Editar(MODOS.EditarObsLiquidador)
        },
        Reimprimir(codigo) {
            this.$reporte_modal({
                Nombre: 'Revisión Remesas',
                Opciones: {
                    Remesa: codigo ?? this.formData.Codigo,
                    Detallado: false,
                },
            })
        },
        Validar() {
            this.$reporte_modal({
                Nombre: 'Revisión Remesas',
                Opciones: {
                    Remesa: this.formData.Codigo,
                    Detallado: true,
                },
            })
        },
        Cancelar() {

            if ([MODOS.Editar, MODOS.EditarDeposito, MODOS.EditarObsLiquidador].includes(this.modo)) {
                this.modo = MODOS.Lectura
                Object.assign(this.formData, this.tempFormData)
            } else if (this.modo == MODOS.Nuevo)
                this.Limpiar()

        },
        Limpiar() {
            this.modo = MODOS.Lectura
            this.formData = {
                Empresa: null,
                Codigo: null,
                Tipo: null,
                Fecha: null,
                CuentaBanco: null,
                CuentaNumero: null,
                DepositoNumero: null,
                Responsable: null,
                Entrega: null,
                Usuario: null,
                FechaDeposito: null,
                FechaLiquidado: null,
                FechaRegistro: null,
                ObservacionesLiquidador: null,
                ObservacionesdeControl: null,
                Detalle: null,
            }
            this.$refs.refBuscadorRemesa ?.limpiar_buscador()
        },

        async CargarCuentas() {
            await this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 1
                })
                .then(resp => {
                    cuentas.splice(0, cuentas.length, ...resp.data.json)
                })
        },

    },
    computed: {
        callbackReimpresion() {
            return (this.formData.Codigo && this.modo == MODOS.Lectura) ? () => {this.Reimprimir()} : null
        },
        callbackValidacion() {
            return (this.formData.Codigo && this.modo == MODOS.Lectura) ? this.Validar : null
        },
        formInstance() {
            return this.$refs['formRemesas'].instance
        },
    },
    created() {
        this.Limpiar()
    },
    mounted() {
        this.CargarCuentas()
        this.Navegar(999999, -1)
    },
    watch: {
        'remesa'(val) {
            if (!val)
                this.Limpiar()
        }
    }
}
</script>

<style>
@import 'styles.css';

.remesas-container .buscador-remesa div.vx-input-group-default.flex-grow {
    flex-grow: 0 !important;
}

.buscador-remesa div.append-text.btn-addon button.vs-con-dropdown:not(:last-child)>button {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.remesas-container .buscador-remesa .vs-button-border {
    border-right: 1px solid !important;
}

.eyoeyo {
    flex: content;
}

.remesas-container .navigation-buttons {
    margin-top: 5px;
}

.remesas-container .navigation-buttons .vs-button{
    height: 25px;
    width: 40px;
    padding: 0 0 0 0;
}
</style>
