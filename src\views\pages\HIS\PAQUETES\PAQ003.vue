<template>
  <div>
      <div class="search-bar-container">
          <vx-card class="search-card">
          
              <h5 style="margin-left: 20px;">Asignación del Paquete al Paciente</h5>
              <br>
              <div class="search-content">
                  <div class="search-input-container">
                      <!-- Mostrar el campo de Código si el checkbox está activo -->
                      <DxTextBox v-if="mostrarCodigo" v-model="info.Codigo" placeholder="Código del Paciente" width="100%" @enter-key="buscarPaciente()" />

                      <!-- Mostrar el campo de Nombre si el checkbox NO está activo -->
                      <DxTextBox v-else v-model="info.NombreApellidos" placeholder="Nombre del Paciente" width="100%" @enter-key="buscarPaciente()" /> &nbsp;
                      <!-- Checkbox para alternar entre los campos -->
                      <DxCheckBox v-model="mostrarCodigo" text="Usar Código Paciente" />
                  </div>

                  <div class="search-buttons">
                      <DxButton class="search-button" icon="find" text="Buscar" type="default" @click="buscarPaciente()" />

                      <DxButton class="search-button" v-if="info.NombreApellidos || info.Codigo" icon="close" type="danger" @click="limpiarDatos()" />
                  </div>
              </div>
          </vx-card>
      </div>
      <div style="margin: 5px">
          <vs-table2 max-items="5" search :data="Pacientes" class="OverFlow mt-0 mb-0">
              <template slot="thead">
                  <th order="CodigoPaciente" width="200px" filtro="CodigoPaciente">
                      Codigo Paciente
                  </th>
                  <th order="Nombre" filtro="Nombre">Nombre Paciente</th>
                  <th order="Apellido" filtro="Apellido">Apellido Paciente</th>
                  <th order="IDCLIENTE" filtro="IDCLIENTE">Id Cliente</th>
                  <th order="NACIMIENTO" filtro="NACIMIENTO">Nacimiento</th>
              </template>
              <template slot-scope="{ data }">
                  <tr :key="indextr" v-for="(tr, indextr) in data" @click="handleAffiliateSelection(tr)" :class="{ highlighted: tr == seleccionado }">
                      <vs-td2>{{ tr.CodigoPaciente }}</vs-td2>
                      <vs-td2>{{ tr.Nombre }}</vs-td2>
                      <vs-td2>{{ tr.Apellido }}</vs-td2>
                      <vs-td2>{{ tr.IDCLIENTE }}</vs-td2>
                      <vs-td2>{{ tr.NACIMIENTO }}</vs-td2>
                  </tr>
              </template>
          </vs-table2>
      </div>
      <div class="container-wrapper" style="display: flex; margin: 5px" v-if="seleccionado && Object.keys(seleccionado).length > 0">
          <div class="left-panel" style="width: 35%">
              <div style="display: flex">
                  <DxTextBox v-model="info.Paquete" :disabled="true" width="70px" style="margin-right: 5px" />
                  <DxSelectBox :search-enabled="true" :data-source="listaPqtA" width="650px" :input-attr="{ 'aria-label': 'Paquete' }" :display-expr="
          (item) => (item ? `${item.Codigo} - ${item.Nombre}` : '')
        " :value-expr="'Codigo'" v-model="info.Paquete" :search-expr="['Codigo', 'Nombre']" @value-changed="PrecioHospital" />
              </div>
              <DxDataGrid :dataSource="infoHosp" style="margin-top: 5px" :allow-column-reordering="true" height="180px" :columnAutoWidth="true" :row-alternation-enabled="true" :show-borders="true" :headerFilter="{ visible: true }" :selectedRowKeys.sync="selectedHosp"
              @row-click="trasladarDatos" class="data-grid">
                  <DxDataGridSelection mode="single" />
                  <DxDataGridColumn dataField="HOSPITAL" caption="Codigo" />
                  <DxDataGridColumn dataField="NOMBRE" caption="Nombre Hospital" />
                  <DxDataGridColumn dataField="PRECIO" caption="Precio Q." />
                  <DxDataGridColumn caption="Tipo" :calculateCellValue="calculateTipo" />
              </DxDataGrid>
          </div>
          &nbsp;
          <div class="right-panel" style="width: 65%;">
              <DxDataGrid ref="datagrid" :dataSource="listPqt" @selection-changed="seleccionarFilaPaquete" @rowRemoving="onRowRemoving" :allow-column-reordering="true" height="175px" class="data-grid" :columnAutoWidth="true" :row-alternation-enabled="true" :show-borders="true"
              :headerFilter="{ visible: true }" :selectedRowKeys.sync="selectedHosp">

                  <DxDataGridEditing :allow-deleting="true" :use-icons="true" />
                  <DxDataGridColumn dataField="Id" caption="id" />
                  <DxDataGridColumn dataField="CodigoPQT" caption="PQT" />
                  <DxDataGridColumn dataField="SERIEADMISION" caption="Serie" />
                  <DxDataGridColumn dataField="ADMISION" caption="Admisión" />
                  <DxDataGridColumn dataField="NOMBRE" caption="Nombre Paquete" />
                  <DxDataGridColumn dataField="NombreHospital" caption="Hospital" />
                  <DxDataGridColumn dataField="PRECIO" caption="Precio Q." />
                  <DxDataGridColumn caption="Tipo" :calculateCellValue="calculateTipo" />
                  <DxDataGridColumn dataField="Status" caption="Status" />
                  <DxDataGridColumn type="buttons" :buttons="[
                  {
                      template: 'dropdownTemplate',
                      cssClass: 'btn-iniciar'
                  },
                  'delete'
              ]" />
                  <template #dropdownTemplate="{ data }">
                      <vs-dropdown @click.native.stop :delay="5000">
                          <!-- 1000 ms = 1 segundo -->
                          <a class="a-icon" href="#" :class="{ 'active': selectedHosp === data.row.key }">
                              <vs-icon style="color: #2980b9;" icon="menu" title="Opciones"></vs-icon>
                          </a>

                          <vs-dropdown-menu class="custom-dropdown-menu">
                              <vs-dropdown-item @click="abrirPopupModificar(data.row.data)" class="dropdown-item">
                                  <i class="fas fa-pencil-alt"></i> Actualizar
                              </vs-dropdown-item>
                              <vs-dropdown-item @click="ListadoRecibos(data.row.data)" class="dropdown-item">
                                  <i class="fas fa-receipt"></i> Recibos
                              </vs-dropdown-item>
                          </vs-dropdown-menu>
                      </vs-dropdown>
                  </template>
              </DxDataGrid>
              <div class="button-group">
                  <DxButton :disabled="!permisos.Asignar" :width="120" text="Asignar" type="success" @click="asignar()" style="margin-top: 10px" />

                  <DxButton :width="120" text="Limpiar" type="danger" @click="limpiarDatos()" style="margin-top: 10px" />

                  <DxButton :disabled="!permisos.Reactivar" :width="170" text="Reactivar Paquete" type="default" style="margin-top: 10px" @click="ReactivarPaqueteAsignado()" />
              </div>
              <label v-if="!permisos.Asignar || !permisos.Reactivar" style="color: red">
                  Sin permiso para Asignar o Reactivar un paquete. Contacte al administrador del sistema para obtener los permisos necesarios.
              </label>
          </div>



          <DxPopup :visible.sync="popupVisible" :height="650" title="Modificación de Paquete" class="popup-modificar-paquete">
              <div>
                  <vs-list>
                      <vs-divider class="popup-divider" position="left" background="primary" color="#fdfefe">Paquete Inicial</vs-divider>

                      <div class="info-container">
                          <div class="info-item">
                              <p><b>ID:</b>
                                  <br>{{ filaSeleccionadaPopup.Id }}</p>
                          </div>
                          <div class="info-item">
                              <p><b>Codigo:</b>
                                  <br> {{ filaSeleccionadaPopup.CodigoPQT }}</p>
                          </div>
                          <div class="info-item">
                              <p><b>Nombre Paquete:</b>
                                  <br>{{ filaSeleccionadaPopup.NOMBRE }}</p>
                          </div>
                          <div class="info-item">
                              <p><b>Hospital:</b>
                                  <br> {{ filaSeleccionadaPopup.NombreHospital }}</p>
                          </div>
                          <div class="info-item">
                              <p><b>Precio:</b>
                                  <br> {{ filaSeleccionadaPopup.PRECIO }}</p>
                          </div>
                      </div>

                      <vs-divider class="popup-divider" position="left" background="primary" color="#fdfefe">Paquete Seleccionado</vs-divider>

                      <div class="info-container">
                          <div class="info-item">
                              <p><b>Codigo Paquete:</b> {{ infoPopup.Paquete_Popup }}</p>
                          </div>
                          <div class="info-item">
                              <p><b>Hospital:</b> {{ filaSeleccionada.NOMBRE }}</p>
                          </div>
                          <div class="info-item">
                              <p><b>Precio:</b> {{ filaSeleccionada.PRECIO }}</p>
                          </div>
                      </div>
                  </vs-list>
              </div>

              <div>


                  <div class="search-controls-popup ">
                      <DxTextBox v-model="infoPopup.Paquete_Popup" :disabled="true" width="70px" />
                      <DxSelectBox :search-enabled="true" :data-source="listaPqtA" width="650px" :input-attr="{ 'aria-label': 'Paquete' }" :display-expr="(item) => (item ? `${item.Codigo} - ${item.Nombre}` : '')" :value-expr="'Codigo'" v-model="infoPopup.Paquete_Popup" :search-expr="['Codigo', 'Nombre']"
                      @value-changed="PrecioHospitalPopup" />
                  </div>

                  <DxDataGrid class="popup-datagrid" :dataSource="infoHospPopup" :allow-column-reordering="true" height="200px" :columnAutoWidth="true" :row-alternation-enabled="true" :show-borders="true" :headerFilter="{ visible: true }" @row-click="seleccionarFila">
                      <DxDataGridSelection mode="single" />
                      <DxDataGridColumn dataField="HOSPITAL" caption="Codigo" />
                      <DxDataGridColumn dataField="NOMBRE" caption="Nombre Hospital" />
                      <DxDataGridColumn dataField="PRECIO" caption="Precio Q." />
                      <DxDataGridColumn caption="Tipo" :calculateCellValue="calculateTipo" />
                  </DxDataGrid>

                  <div class="button-group-popup button-container-Popup">
                      <DxButton :disabled="!permisos.Actualizar" :width="120" text="Actualizar" type="default" @click="confirmarAccion()" />
                      <DxButton :width="120" text="Cancelar" type="danger" @click="CancelarPopup()" />
                  </div>

                  <div v-if="!permisos.Actualizar" class="permission-message-popup">
                      Sin permiso para Actualizar. Contacte al administrador del sistema para obtener los permisos necesarios.
                  </div>

              </div>
          </DxPopup>

          <DxPopup :visible.sync="popupVisibleReactivar" :height="460" title="Reactivación Paquete" class="popup-reactivar-paquete">


              <vs-divider class="reactivar-divider" position="left" background="primary" color="#fdfefe">Datos Paciente</vs-divider>

              <div class="paciente-info-container">
                  <div class="paciente-info-item">
                      <p><b>Codigo:</b> {{ seleccionado.CodigoPaciente }}</p>
                  </div>
                  <div class="paciente-info-item">
                      <p><b>Id Cliente:</b> {{seleccionado.IDCLIENTE}}</p>
                  </div>
                  <div class="paciente-info-item">
                      <p><b>Paciente:</b> {{seleccionado.Nombre + " " + seleccionado.Apellido}}</p>
                  </div>
              </div>
              <DxDataGrid class="reactivar-datagrid" :dataSource="listPqtReact" :allow-column-reordering="true" height="200px" :columnAutoWidth="true" :row-alternation-enabled="true" :show-borders="true" @row-click="onRowSelected">
                  <DxDataGridSelection mode="single" />
                  <DxDataGridColumn dataField="Id" caption="ID" />
                  <DxDataGridColumn dataField="CodigoPQT" caption="PQT" />
                  <DxDataGridColumn dataField="NOMBRE" caption="Nombre Paquete" />
                  <DxDataGridColumn dataField="SERIEADMISION" caption="Serie Admision" />
                  <DxDataGridColumn dataField="ADMISION" caption="Admision" />
                  <DxDataGridColumn dataField="NombreHospital" caption="Hospital" />
                  <DxDataGridColumn dataField="PRECIO" caption="Precio" />
                  <DxDataGridColumn dataField="Status" caption="Status" />
                  <DxDataGridColumn dataField="Fecha" caption="Fecha" type="date" />
              </DxDataGrid>

              <div class="reactivar-button-group button-container-Popup">
                  <DxButton :disabled="!permisos.ReactivarSelecion" :width="170" text="Reactivar Paquete" type="default" @click="ReactivarPaqueteAsignadoUpdate()" />
                  <DxButton :width="170" text="Cancelar" type="danger" @click="CancelarPopupReactivar()" />
              </div>

              <div v-if="!permisos.ReactivarSelecion" class="reactivar-permission-message">
                  Sin permiso para Reactivar un paquete. Contacte al administrador del sistema para obtener los permisos necesarios.
              </div>
          </DxPopup>


          <DxPopup :visible.sync="popupVisibleRecibos" :height="380" title="Recibos">
              <div>
                  <vs-divider class="popup-divider" position="left" background="primary" color="#fdfefe">Paquete Seleccionado</vs-divider>

                  <div class="info-container">
                      <div class="info-item">
                          <p><b>ID:</b>
                              <br>{{ filaSeleccionadaPopup.Id }}</p>
                      </div>
                      <div class="info-item">
                          <p><b>Codigo:</b>
                              <br> {{ filaSeleccionadaPopup.CodigoPQT }}</p>
                      </div>
                      <div class="info-item">
                          <p><b>Nombre Paquete:</b>
                              <br>{{ filaSeleccionadaPopup.NOMBRE }}</p>
                      </div>
                      <div class="info-item">
                          <p><b>Hospital:</b>
                              <br> {{ filaSeleccionadaPopup.NombreHospital }}</p>
                      </div>
                      <div class="info-item">
                          <p><b>Precio:</b>
                              <br> {{ filaSeleccionadaPopup.PRECIO }}</p>
                      </div>
                  </div>
                  <DxDataGrid :dataSource="recibos" :columnAutoWidth="true" :allow-column-reordering="true" :show-borders="true">
                      <DxDataGridColumn dataField="Empresa" caption="Empresa" />
                      <DxDataGridColumn dataField="Paciente" caption="Paciente" />
                      <DxDataGridColumn dataField="Serie" caption="Serie" />
                      <DxDataGridColumn dataField="CodigoRecibo" caption="CodigoRecibo" />
                      <DxDataGridColumn dataField="NombrePaciente" caption="NombrePaciente" />
                      <DxDataGridColumn dataField="SerieAdmision" caption="SerieAdmision" />
                      <DxDataGridColumn dataField="Admision" caption="Admision" />

                  </DxDataGrid>
                  <br>
                  <div class="button-container-Popup">
                      <DxButton v-if="mostrarBoton" type="success" text="Vincular Recibos" @click="Recibos" />
                  </div>

              </div>
          </DxPopup>

      </div>
  </div>
</template>

<script>

import Swal from "sweetalert2";

export default {
  data() {
    return {
      info: {
        NombreApellidos: null,
        Nombres: null,
        Apellidos: null,
        Paquete: null,
        Hospital: null,
        Status: null,
        nombrePaquete: null,
        Estatus: null,
        Codigo: null
      },

      infoPopup: {
        Paquete_Popup: null,
      },

      infoHospPopup: [],
      selectedHospPopup: [],

      Pacientes: [],
      listaPqtA: [],
      listaEmp: [],
      infoHosp: [],
      listPqt: [],
      selectedHosp: [],
      listPqtReact: [],
      recibos:[],
      popupVisible: false,
      popupVisibleReactivar: false,
      popupVisibleRecibos: false,
      filaSeleccionadaPopup: {},
      filaSeleccionada: {},
      seleccionado: null, // Fila seleccionada de la tabla de Pacientes
      paqueteSeleccionado: null,
      paqueteSeleccionadodetalle: {}, // Fila seleccionada de la tabla de Paquetes
      selectedRowKey: null,
      isChecked: false,
      mostrarCodigo: false, // Estado inicial del checkbox


      seleccionadoReact: null, // Fila seleccionada

      permisos: {
        Asignar: false,
        Actualizar: false,
        Eliminar_Paquete: false,
        Reintentar: false,
        Reactivar:false,
        ReactivarSelecion:false
      },
    };
  },
  computed: {
    sesion() {
      return this.$store.state.sesion;
    },
    global() {
      return this.$store.state.global;
    },
    mostrarBoton() {
      return this.recibos.every(recibo => 
        recibo.Admision === "" && 
        recibo.SerieAdmision === ""
      );
    },

    dataGridPQT(){
      return  this.$refs['datagrid'].instance
    }
  },
  watch: {
    // Observador para el cambio en `mostrarCodigo`
    mostrarCodigo(newValue) {
      if (newValue) {
        // Si se activa el checkbox (true), limpia el nombre
        this.info.NombreApellidos = '';
      } else {
        // Si se desactiva (false), limpia el código
        this.info.Codigo = null;
      }
    }
  },
  methods: {
    abrirPopupModificar(obj) {
    this.filaSeleccionadaPopup = { ...obj };

    if (!this.permisos.Actualizar) {
      this.$vs.dialog({
        type: 'alert',
        color: '#ed8c72',
        title: 'Permisos Denegados',
        acceptText: 'Aceptar',
        text: 'No tienes permiso para Actualizar este paquete. Contacte al administrador del sistema para obtener los permisos necesarios.',
        buttonCancel: 'border',
        accept: () => {},
      })
      return;
    }

    this.popupVisible = true;

  },

    Recibos() {
      Swal.fire({
        title: '¿Estás seguro?',
        text: '¿Deseas relacionar los recibos con la admisión?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#52BE80',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Sí, relacionar',
        cancelButtonText: 'Cancelar',
        customClass: {
          content: 'swal2-container',
        }

      }).then((result) => {
        if (result.isConfirmed) {
          this.axios.post("/app/v1_paquetes_quirurgicos/Recibos", {
            Opcion: "8",
            CodigoPaciente: this.seleccionado.CodigoPaciente,
            Codigo: this.filaSeleccionadaPopup.Id
          })
          .then((resp) => {
              if(resp.data.codigo === 0){
                this.ListadoRecibosdos();
              }
          })
          .catch(() => {
          });
        }
      });
    },

    ListadoRecibos(obj) {
      this.filaSeleccionadaPopup = { ...obj };

      this.axios.post("/app/v1_paquetes_quirurgicos/ListRecibos", {
        Opcion: "9",
        CodigoPaciente: this.seleccionado.CodigoPaciente,
        Id: this.filaSeleccionadaPopup.Id,
        Codigo: this.filaSeleccionadaPopup.CodigoPQT
      }).then((resp) => {
        if (resp.data.codigo == 0) {
          this.recibos = resp.data.json;

          // Verifica si hay recibos
          if (this.recibos && this.recibos.length > 0) {
            this.popupVisibleRecibos = true; // Abre el popup si hay datos
          } else {
            // Muestra SweetAlert si no hay información
            Swal.fire({
              icon: 'warning',
              title: 'Sin recibos',
              text: 'No se encontraron recibos para mostrar.',
              confirmButtonText: 'Aceptar'
            });
          }
        }
      }).catch(() => {
        // SweetAlert en caso de error
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Ocurrió un error al cargar los recibos.',
          confirmButtonText: 'Aceptar'
        });
      });
    },
    
    ListadoRecibosdos() {

      this.axios.post("/app/v1_paquetes_quirurgicos/ListRecibos", {
        Opcion: "9",
        CodigoPaciente: this.seleccionado.CodigoPaciente,
        Id: this.filaSeleccionadaPopup.Id,
        Codigo: this.filaSeleccionadaPopup.CodigoPQT
      }).then((resp) => {
        if (resp.data.codigo == 0) {
          this.recibos = resp.data.json;

          // Verifica si hay recibos
          if (this.recibos && this.recibos.length > 0) {
            this.popupVisibleRecibos = true; // Abre el popup si hay datos
          } else {
            // Muestra SweetAlert si no hay información
            Swal.fire({
              icon: 'warning',
              title: 'Sin recibos',
              text: 'No se encontraron recibos para mostrar.',
              confirmButtonText: 'Aceptar'
            });


          }
        }
      }).catch(() => {
        // SweetAlert en caso de error
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Ocurrió un error al cargar los recibos.',
          confirmButtonText: 'Aceptar'
        });
      });
    },
    

    // ==========================================================
    //  Valida campo del nombre del paciente
    // ==========================================================


    buscarPaciente() {
      // Si el checkbox está habilitado (usar código)
    
      if (this.mostrarCodigo) {
        if (!this.info.Codigo) {
          Swal.fire({
            icon: "warning",
            title: "Campo vacío",
            text: "Por favor, ingrese el código del paciente.",
            confirmButtonText: "Aceptar",
          });
          return;
        }
      } 
      // Si el checkbox NO está habilitado (usar nombre)
      else {
        if (!this.info.NombreApellidos) {
          Swal.fire({
            icon: "warning",
            title: "Campo vacío",
            text: "Por favor, ingrese el nombre del paciente.",
            confirmButtonText: "Aceptar",
          });
          return;
        }
      }
      // Si pasa todas las validaciones, ejecutar la consulta
      this.consulta().Paciente();
    },

    // ==========================================================
    //  COSULTA PACIENTES
    // ==========================================================
    consulta() {
      return {
        Paciente: () => {
          (this.Pacientes = []),
            this.axios
              .post("/app/v1_paquetes_quirurgicos/ConsultaPaciente", {
                Opcion: "1",
                SubOpcion: "1",
                NombreApellido: this.info.NombreApellidos,
                Codigo:this.info.Codigo
              })
              .then((resp) => {
                if (resp.data.codigo == 0) {
                  this.Pacientes = resp.data.json;
                }
              });
        },
      };
    },

    // ==========================================================
    //  COSULTA PQTP DETALLE
    // ==========================================================
    ConsultaPQTP() {
      return new Promise((res,rej)=>{

        this.axios
        .post("/app/v1_paquetes_quirurgicos/ConsultaDetallePQTP", {
          Opcion: "1",
          SubOpcion: "2",
          CodigoPaciente: this.seleccionado.CodigoPaciente,
        })
        .then((resp) => {
          if (resp.data.codigo == "0") {
            this.listPqt = resp.data.json.map((item) => {
              return {
                ...item,
                MEDAX: item.MEDAX === 1 ? true : false,
              };

            });
          }
          res(resp.data)
        }).catch(err=>{
          rej(err)
        })


      })
      
    },

    // ==========================================================
    //  COSULTA PAQUETES ACTIVOS
    // ==========================================================
    paquetes: function () {
      return {
        Activos: () => {
          this.axios
            .post("/app/v1_paquetes_quirurgicos/obtenerPaquetesActivos", {
              Operacion: "consultaPQTA",
            })
            .then((resp) => {
              if (resp.data.codigo == "0") {
                this.listaPqtA = resp.data.json;
              }
            });
        },
      };
    },

    paquetesPopup: function () {
      return {
        ActivosPopup: () => {
          this.axios
            .post("/app/v1_paquetes_quirurgicos/obtenerPaquetesActivos", {
              Operacion: "consultaPQTA",
            })
            .then((resp) => {
              if (resp.data.codigo == "0") {
                this.listaPqtA = resp.data.json;
              }
            });
        },
      };
    },

    // ==========================================================
    //  COSULTA DE PRECIOS HOSPITAL
    // ==========================================================
    PrecioHospital() {
      // Función para realizar la consulta al API
      const consultarPreciosHospital = () => {
        return this.axios.post(
          "/app/v1_paquetes_quirurgicos/ConsultaHospitalPrecios",
          {
            Operacion: "detalleHosp1",
            CodigoPqt: this.info.Paquete,
          }
        );
      };
      // Función para manejar la respuesta de la consulta
      const manejarRespuesta = (resp) => {
        if (resp.data.codigo == "0") {
          this.infoHosp = resp.data.json;
          if (this.listPqt.length > 0) {
            // Comparar si hay algún hospital en infoHosp que coincida con alguno en listPqt
            this.listPqt.some((pqt) => {
              return this.infoHosp.some((hosp) => {
                if (hosp.HOSPITAL === pqt.HOSPITAL) {
                  // Verifica si los precios son diferentes
                  if (hosp.PRECIO !== pqt.PRECIO) {
                    // Muestra la notificación si los precios son diferentes
                    Swal.fire({
                      icon: "warning",
                      title: "Advertencia",
                      text: `El precio del hospital ${hosp.NOMBRE} (${hosp.PRECIO}) no coincide con el paquete (${pqt.PRECIO}).`,
                    });
                  }
                  return true; // Se encontró un hospital que coincide, pero los precios podrían ser diferentes
                }
                return false; // No coincide el hospital
              });
            });
          } else {
            this.listPqt = [];
            this.info.nombrePaquete = "";
          }
        }
      };

      consultarPreciosHospital()
        .then(manejarRespuesta)
        .catch(() => {
          Swal.fire({
            icon: "error",
            title: "Error",
            text: "Hubo un problema al consultar los precios del hospital.",
          });
        });
    },

    PrecioHospitalPopup() {
      // Función para realizar la consulta al API
      const consultarPreciosHospital = () => {
        return this.axios.post(
          "/app/v1_paquetes_quirurgicos/ConsultaHospitalPrecios",
          {
            Operacion: "detalleHosp1",
            CodigoPqt: this.infoPopup.Paquete_Popup,
          }
        );
      };
      // Función para manejar la respuesta de la consulta
      const manejarRespuestaUno = (resp) => {
        if (resp.data.codigo == "0") {
          this.infoHospPopup = resp.data.json;
          this.info.nombrePaquete = "";
        }
      };

      consultarPreciosHospital()
        .then(manejarRespuestaUno)
        .catch(() => {
          Swal.fire({
            icon: "error",
            title: "Error",
            text: "Hubo un problema al consultar los precios del hospital.",
          });
        });
    },

    // ==========================================================
    //  COSULTA DATOS PAQUETE
    // ==========================================================
    consultaDatosPQT() {
      this.axios
        .post("/app/v1_paquetes_quirurgicos/ConsultaDatosPQTP", {
          Opcion: "1",
          SubOpcion: "3",
          Codigo: this.seleccionado.Codigo,
        })
        .then((resp) => {
          if (resp.data.json) {
            this.$map({
              objeto: this.info,
              respuestaAxios: resp,
            });
          }
        });
    },

    // ==========================================================
    // SELECCION DE LOS DATOS DEL PAQUETE
    // ==========================================================
    seleccionarFilaPaquete(e) {
      // Capturar la fila seleccionada de la tabla de Paquetes
      this.paqueteSeleccionado = e.selectedRowsData[0];
    },

    handleAffiliateSelection(e) {
      this.seleccionado = e;
      this.ConsultaPQTP();
      this.paquetes().Activos();
    },

    // ==========================================================
    //  TRASLADO DE PQT
    // ==========================================================

    trasladarDatos() {
      // Verificar si ya hay un paquete sin hospitales asignados
      if (
        this.listPqt.length > 0 &&
        this.listPqt.every((item) => !item.HOSPITAL)
      ) {
        Swal.fire({
          icon: "warning",
          title: "Paquete sin hospitales",
          text: "El paquete anterior no tiene hospitales asignados. Por favor, agrega hospitales antes de añadir un nuevo paquete.",
          confirmButtonText: "OK",
          confirmButtonColor: "#52BE80",
        });
        return;
      }

      // Verificar que se haya seleccionado un paquete
      const paqueteSeleccionado = this.listaPqtA.find(
        (pqt) => pqt.Codigo === this.info.Paquete
      );
      if (!paqueteSeleccionado) {
        Swal.fire({
          icon: "error",
          title: "Paquete No Seleccionado",
          text: "Por favor, selecciona un paquete antes de agregar hospitales.",
          confirmButtonText: "OK",
          confirmButtonColor: "#E74C3C",
        });
        return;
      }

      // Asignar el paquete seleccionado
      this.paqueteSeleccionado = paqueteSeleccionado;

      // Verificar que haya al menos un hospital seleccionado
      if (!this.selectedHosp || this.selectedHosp.length === 0) {
        Swal.fire({
          icon: "error",
          title: "Hospital No Seleccionado",
          text: "Por favor, selecciona al menos un hospital antes de agregarlo al paquete.",
          confirmButtonText: "OK",
          confirmButtonColor: "#E74C3C",
        });
        return;
      }

      // Asignar el hospital seleccionado a paqueteSeleccionadodetalle
      this.paqueteSeleccionadodetalle = this.selectedHosp;

      // Verificar que no haya duplicados en listPqt
      for (const hosp of this.selectedHosp) {
        const hospitalExists = this.listPqt.find(
          (item) =>
            item.HOSPITAL === hosp.HOSPITAL &&
            item.CodigoPQT === paqueteSeleccionado.Codigo
        );

        if (hospitalExists) {
          Swal.fire({
            icon: "warning",
            title: "Hospital Duplicado",
            text: "El paquete ya se encuentra asignado a este paciente, Por favor intentar con otro!",
            confirmButtonText: "OK",
            confirmButtonColor: "#52BE80",
          });
          return; // Evitar continuar si hay duplicados
        }
      }

      // Agregar los hospitales seleccionados al paquete
      this.selectedHosp.forEach((hosp) => {
        this.listPqt.push({
          CodigoPQT: paqueteSeleccionado.Codigo,
          NOMBRE: paqueteSeleccionado.Nombre,
          HOSPITAL: hosp.HOSPITAL,
          NombreHospital: hosp.NOMBRE,
          PRECIO: hosp.PRECIO,
          MEDAX: hosp.MEDAX,
          Status: "A",
          Tipo: this.calculateTipo(hosp),
        });
      });
    },

    // Calcular el tipo de hospital
    calculateTipo(rowData) {
      return rowData.MEDAX ? "Sermesa" : "Cortesia";
    },

    seleccionarFila(e) {
      this.filaSeleccionada = e.data;
    },

    // ==========================================================
    //  ASIGNAR PAQUETE AL PACIENTE
    // ==========================================================

    asignar() {
      // Verificar si los datos del hospital y del paquete ya fueron trasladados
      if (
        !this.paqueteSeleccionado ||
        !this.paqueteSeleccionadodetalle ||
        this.paqueteSeleccionadodetalle.length === 0
      ) {
        Swal.fire({
          icon: "error",
          title: "Datos incompletos",
          text: "No se han trasladado los datos del hospital o del paquete. Por favor, verifica la información antes de continuar.",
          confirmButtonText: "OK",
          confirmButtonColor: "#E74C3C",
        });
        return;
      }

      // Verificar si se ha seleccionado un paciente
      if (!this.seleccionado || !this.seleccionado.CodigoPaciente) {
        Swal.fire({
          icon: "error",
          title: "Paciente no seleccionado",
          text: "Por favor, selecciona un paciente antes de asignar el hospital.",
          confirmButtonText: "OK",
          confirmButtonColor: "#E74C3C",
        });
        return;
      }

      // Verificar si ya existe un registro con el mismo Codigo y HOSPITAL en listPqt
      const detalle = this.paqueteSeleccionadodetalle[0];
      const exists = this.listPqt.some(
        (item) =>
          item.Codigo === this.paqueteSeleccionado.Codigo &&
          item.HOSPITAL === detalle.HOSPITAL
      );

      if (exists) {
        Swal.fire({
          icon: "error",
          title: "Error en la asignación",
          text: `Ya existe un registro con el mismo Codigo: ${this.paqueteSeleccionado.Codigo} y HOSPITAL: ${detalle.HOSPITAL}. Por favor, elimina uno antes de continuar.`,
          confirmButtonText: "OK",
          confirmButtonColor: "#E74C3C",
        });
        return;
      }

      // Asignar los datos al paciente mediante un POST
      this.axios
        .post("/app/v1_paquetes_quirurgicos/asignar", {
          Opcion: "2",
          CodigoPaciente: this.seleccionado.CodigoPaciente, // De la tabla de Pacientes
          Codigo: this.paqueteSeleccionado.Codigo, // De la tabla de Paquetes
          MEDAX: detalle.MEDAX, // Campo MEDAX del hospital
          HOSPITAL: detalle.HOSPITAL, // Nombre del hospital
          PRECIO: detalle.PRECIO, // Precio del hospital
        })
        .then(() => {
          Swal.fire({
            icon: "success",
            title: "Asignación exitosa",
            text: "El hospital ha sido asignado correctamente al paciente.",
            confirmButtonText: "OK",
            confirmButtonColor: "#52BE80",
          }).then((result) => {
            if (result.isConfirmed) {
              this.ConsultaPQTP();
              this.infoHosp = [];
              this.paqueteSeleccionado = null;
              this.paqueteSeleccionadodetalle = {}; // Fila seleccionada de la tabla de Paquetes
              this.selectedRowKey = null;
            }
          });
        })
        .catch(() => {
          // Mostrar el error y agregar un botón para reintentar
          if (this.permisos.Asignar) {
            // Mostrar SweetAlert solo si el permiso es válido
            Swal.fire({
              icon: "error",
              title: "Error en la asignación",
              text: "Ocurrió un error al intentar asignar el Paquete. Ya que el paciente tiene una admisión Activa ¿Deseas forzar la asignación de nuevo?",
              showCancelButton: true,
              confirmButtonText: "Asignar de Nuevo",
              cancelButtonText: "Cancelar",
              confirmButtonColor: "#2980B9",
              cancelButtonColor: "#E74C3C",
            }).then((result) => {
              // Si el usuario confirma, se verifica que el permiso siga siendo válido
              if (result.isConfirmed && this.permisos.Asignar) {
                this.axios
                  .post("/app/v1_paquetes_quirurgicos/asignar", {
                    Opcion: "4",
                    CodigoPaciente: this.seleccionado.CodigoPaciente, // De la tabla de Pacientes
                    Codigo: this.paqueteSeleccionado.Codigo, // De la tabla de Paquetes
                    MEDAX: detalle.MEDAX, // Campo MEDAX del hospital
                    HOSPITAL: detalle.HOSPITAL, // Nombre del hospital
                    PRECIO: detalle.PRECIO, // Precio del hospital
                  })
                  .then(() => {
                    Swal.fire({
                      icon: "success",
                      title: "Asignación exitosa",
                      text: "El hospital ha sido asignado correctamente al paciente.",
                      confirmButtonText: "OK",
                      confirmButtonColor: "#52BE80",
                    });
                    this.ConsultaPQTP();
                  })
                  .catch(() => {
                    // Manejar error si falla la solicitud
                  });
              }
            });
          } else {
            // Mensaje si el usuario no tiene permisos
            Swal.fire({
              icon: "error",
              title: "Permiso Denegado",
              text: "No tienes permisos para asignar un paquete a un paciente con admisión, Contacte al administrador del sistema para obtener los permisos necesarios.",
            });
          }
        });
    },

    // ==========================================================
    //  Actuailizar PAQUETE AL PACIENTE
    // ==========================================================
    
    confirmarAccion() {
      // Validar si filaSeleccionada tiene datos
      if (
        !this.filaSeleccionada ||
        Object.keys(this.filaSeleccionada).length === 0
      ) {
        Swal.fire({
          icon: "warning",
          title: "Hospital no seleccionado",
          text: "Debe seleccionar un Hospital para actualizar.",
          confirmButtonText: "OK",
          confirmButtonColor: "#27AE60",
          customClass: {
            container: "FrentePopup",
            popup: "FrentePopup",
            content: "FrentePopup",
          },
        });
        return;
      }

      // Mostrar confirmación antes de la llamada Axios
      Swal.fire({
        title: "¿Estás seguro?",
        text: "Esta acción asignará el Nuevo paquete al paciente.",
        icon: "question",
        showCancelButton: true,
        confirmButtonText: "Sí, asignar",
        cancelButtonText: "Cancelar",
        confirmButtonColor: "#52BE80",
        cancelButtonColor: "#d33",
        customClass: {
          container: "FrentePopup",
          popup: "FrentePopup",
          content: "FrentePopup",
        },
      }).then((result) => {
        if (result.isConfirmed) {
          // Primera llamada Axios
          this.axios
            .post("/app/v1_paquetes_quirurgicos/asignar", {
              Opcion: "6",
              Id: this.filaSeleccionadaPopup.Id,
              CodigoPaciente: this.seleccionado.CodigoPaciente,
              Codigo: this.infoPopup.Paquete_Popup,
              MEDAX: this.filaSeleccionada.MEDAX,
              HOSPITAL: this.filaSeleccionada.HOSPITAL,
              PRECIO: this.filaSeleccionada.PRECIO
            })
            .then((resp) => {
              if(resp.data.codigo === 0){
         
                this.ConsultaPQTP()

                
              }
            })
            .catch(() => {
              Swal.fire({
                icon: "error",
                title: "Error al asignar el paquete",
                text: "No se puede actualizar el paquete del paciente porque tiene una admisión activa?",
                showCancelButton: true,
                confirmButtonText: "Reintentar",
                cancelButtonText: "Cancelar",
                confirmButtonColor: "#52BE80",
                cancelButtonColor: "#d33",
                customClass: {
                  container: "FrentePopup",
                  popup: "FrentePopup",
                  content: "FrentePopup",
                },
              }).then((result) => {
                if (result.isConfirmed) {
               
                  // Código para reintentar (confirmado)
                  this.axios
                    .post("/app/v1_paquetes_quirurgicos/asignar", {
                      Opcion: "10",
                      Id: this.filaSeleccionadaPopup.Id,
                      CodigoPaciente: this.seleccionado.CodigoPaciente,
                      Codigo: this.infoPopup.Paquete_Popup,
                      MEDAX: this.filaSeleccionada.MEDAX,
                      HOSPITAL: this.filaSeleccionada.HOSPITAL,
                      PRECIO: this.filaSeleccionada.PRECIO
                    })
                    .then((resp) => {
                      if(resp.data.codigo === 0){
                        this.ConsultaPQTP().then(()=>{
                         this.limpiar()
                        });
                        //
                      }
                    })
                    .catch(() => {
                      Swal.fire({
                        icon: "error",
                        title: "Error al reintentar",
                        text: "No se encontró el registro a actualizar en pqtpaciente",
                        confirmButtonText: "OK",
                        confirmButtonColor: "#d33",
                      });
                    });
                } else if (result.dismiss === Swal.DismissReason.cancel) {
                  this.ConsultaPQTP();
                }
              });
            });
        }
      });
    },

    // ==========================================================
    //  REMOVER PAQUETE
    // ==========================================================

    onRowRemoving(e) {
      const removedRow = e.data;
      if (!removedRow.Id) {
        return; // Permitir la eliminación si el Id es nulo
      }

      // Verificar si el usuario tiene permiso para eliminar
      if (!this.permisos.Eliminar_Paquete) {
        // Cambia aquí la condición
        Swal.fire({
          icon: "error",
          title: "Permiso Denegado",
          text: "No tienes permiso para eliminar este paquete, Contacte al administrador del sistema para obtener los permisos necesarios.",
          confirmButtonText: "Aceptar",
        });
        this.ConsultaPQTP();
        e.cancel = true;
        return;
      }

      e.cancel = new Promise((resolve, reject) => {
        this.axios
          .post("/app/v1_paquetes_quirurgicos/UpdatePaquete", {
            Opcion: "3",
            Codigo: removedRow.Id,
            CodigoPaciente: this.seleccionado.CodigoPaciente,
          })
          .then((resp) => {
            if (resp.data.codigo == "0") {
              Swal.fire({
                icon: "success",
                title: "Eliminación exitosa",
                text: "El Paquete Eliminado correctamente al paciente.",
                confirmButtonText: "OK",
                confirmButtonColor: "#52BE80",
              });
              resolve();
              this.ConsultaPQTP();
            } else if (resp.data.codigo === "400") {
              // Error en la eliminación
              reject("No se puede eliminar el paquete con admisión: ");
              this.ConsultaPQTP();
            }
          })
          .catch(() => {
            // Error en la solicitud
            reject(
              "¿Estás seguro de que deseas eliminar este paquete?Ten en cuenta que el paciente al que está asignado tiene una admisión activa."
            );
            this.ConsultaPQTP();
          });
      }).catch((errorMessage) => {
        // Mostrar mensaje de error usando Swal con un botón "Eliminar"
        Swal.fire({
          icon: "error",
          title: "Error",
          text: errorMessage,
          showCancelButton: true,
          confirmButtonText: "Eliminar",
          cancelButtonText: "Cancelar",
          confirmButtonColor: "#2980B9",
          cancelButtonColor: "#E74C3C",
        }).then((result) => {
          if (result.isConfirmed) {
            // Verificar el permiso antes de realizar la eliminación forzada
            if (this.permisos.Eliminar_Paquete) {
              this.axios
                .post("/app/v1_paquetes_quirurgicos/UpdatePaquete", {
                  Opcion: "5",
                  Codigo: removedRow.Id,
                  CodigoPaciente: this.seleccionado.CodigoPaciente,
                })
                .then((resp) => {
                  if (resp.data.codigo == "0") {
                    Swal.fire({
                      icon: "success",
                      title: "Eliminación exitosa",
                      text: "El Paquete Eliminado correctamente al paciente.",
                      confirmButtonText: "OK",
                      confirmButtonColor: "#52BE80",
                    });
                    this.ConsultaPQTP();
                  }
                })
                .catch(() => {
                  // Error en la solicitud de eliminación forzada
                  Swal.fire({
                    icon: "error",
                    title: "Error",
                    text: "Error en la solicitud de eliminación forzada: ",
                    confirmButtonText: "Aceptar",
                  });
                });
            } else {
              Swal.fire({
                icon: "error",
                title: "Permiso Denegado",
                text: "No tienes permiso para eliminar este paquete. Contacte al administrador del sistema para obtener los permisos necesarios.",
                confirmButtonText: "Aceptar",
              });
            }
          }
        });
      });
    },

    ReactivarPaqueteAsignado() {
      this.axios
        .post("/app/v1_paquetes_quirurgicos/listaReactivar", {
          Opcion: "7",
          SubOpcion: "1",
          CodigoPaciente: this.seleccionado.CodigoPaciente,
        })
        .then((resp) => {
          if (resp.data.codigo == "0") {
            this.listPqtReact = resp.data.json;
          }
        });
      this.popupVisibleReactivar = true;
    },

    onRowSelected(e) {
      this.seleccionadoReact = e.data;
    },

    ReactivarPaqueteAsignadoUpdate() {
      this.axios
        .post("/app/v1_paquetes_quirurgicos/ReactivarPaquete", {
          Opcion: "7",
          SubOpcion: "2",
          CodigoPaciente: this.seleccionado.CodigoPaciente,
          Codigo: this.seleccionadoReact.CodigoPQT,
          Id: this.seleccionadoReact.Id,
        })
        .then((resp) => {
          if (resp.data.codigo == "0") {
            this.$vs.notify({
              title: "Reactivación Exitosa",
              iconPack: "feather",
              icon: "icon-check-circle",
              color: "success",
            });
            this.popupVisibleReactivar = false;
            this.seleccionadoReact = null;
            this.ConsultaPQTP();
          }
        });
    },

    toggleDropdown(event) {
        // Accede al componente dropdown y activa/desactiva
        const dropdown = this.$refs.dropdown;
        if (dropdown) {
            dropdown.toggle();
        }
        event.stopPropagation();
    },

    // ==========================================================
    // LIMPIAR DATOS
    // ==========================================================
    limpiarDatos() {
      //Datos Info
      this.info.NombreApellidos = null
      this.info.Nombres = null
      this.info.Apellidos = null
      this.info.Paquete = null
      this.info.Hospital = null
      this.info.Status = null
      this.info.nombrePaquete = null
      this.info.Estatus = null
      // Datos -- Arrays
      this.Pacientes = []
      this.listaPqtA = []
      this.listaEmp = []
      this.infoHosp = []
      this.listPqt = []
      this.selectedHos = []
      this.seleccionado = null // Fila seleccionada de la tabla de Pacientes
      this.paqueteSeleccionado = null
      this.paqueteSeleccionadodetalle = {} // Fila seleccionada de la tabla de Paquetes
      this.selectedRowKey = null
      this.isChecked = false;
      this.infoHospPopup = [];
      this.infoPopup = {};
      this.filaSeleccionada = {};
      this.filaSeleccionadaPopup = {};
      this.mostrarCodigo = false 
      this.info.Codigo = null
    },

    limpiar() {
      this.infoHospPopup = [];
      this.filaSeleccionada = {};
      this.filaSeleccionadaPopup = {};
      this.popupVisible = false;
    },

    CancelarPopup() {
      this.infoHospPopup = [];
      this.filaSeleccionada = {};
      this.popupVisible = false;
    },

    CancelarPopupReactivar() {
      this.popupVisibleReactivar = false;
    },
  },

  mounted() {
    this.$validar_funcionalidad("/HIS/PAQUETES/PAQ003", "REINTENTAR", (d) => {
      this.permisos.Reintentar = d.status;
    });

    this.$validar_funcionalidad("/HIS/PAQUETES/PAQ003", "ASIGNAR", (d) => {
      this.permisos.Asignar = d.status;
    });

    this.$validar_funcionalidad("/HIS/PAQUETES/PAQ003", "ACTUALIZAR", (d) => {
      this.permisos.Actualizar = d.status;
    });

    this.$validar_funcionalidad(
      "/HIS/PAQUETES/PAQ003",
      "ELIMINARPAQUETE",
      (d) => {
        this.permisos.Eliminar_Paquete = d.status;
      }
    );

    this.$validar_funcionalidad(
      "/HIS/PAQUETES/PAQ003",
      "REACTIVARPAQUETE",
      (d) => {
        this.permisos.Reactivar = d.status;
      }
    );
    this.$validar_funcionalidad("/HIS/PAQUETES/PAQ003", "REACTIVARPAQUETESELECCION", (d) => {
      this.permisos.ReactivarSelecion = d.status;
    });
  },
};
</script>

<style>
.dx-datagrid-rowsview
  .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed)
  > td {
  background-color: #d4efdf !important;
  color: black !important;
}

.dx-datagrid-rowsview .dx-row.dx-data-row.dx-column-lines.dx-state-hover > td {
  background-color: #79e4ff !important;
  color: #fdfefe !important;
  font-size: 16px;
}

.dx-datagrid-headers {
  background-color: #2471a3 !important;
  color: #fdfefe !important;
  font-weight: bold;
}

.custom-thead .vs-table--thead th {
  background-color: #2471a3 !important; /* Cambia este color por el que desees */
  color: #fdfefe !important; /* Cambia el color del texto si es necesario */
}

.contenedor-tabla thead {
  background-color: #2471a3 !important;
  color: black;
}

.highlighted {
  background-color: #d4efdf !important; /* Cambia este color*/
  color: black !important;
}

.OverFlow .contenedor-tabla {
  overflow-y: auto !important;
  max-height: 270px !important;
}

.header {
  font-size: 34px;
  text-align: center;
}

.FrentePopup {
  z-index: 52001 !important;
}

.notificacion2 {
  text-align: center;
  font-size: 30px;
  cursor: pointer;
  position: relative;
  margin: 20px;
}

.notificacion2:hover {
  color: #2e86c1;
}

.notificacion2 .titulo {
  font-weight: bold;
  font-size: 17px;
  position: relative;
  top: -5px;
}




/* Contenedor principal */
.container-wrapper {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin: 10px;
  padding: 15px;
  background-color: #e5e7e9;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Sección de paquetes */
.package-section {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

/* Panel izquierdo */
.left-panel {
  width: 35%;
  background-color: white;
  padding: 15px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Panel derecho */
.right-panel {
  width: 65%;
  background-color: white;
  padding: 15px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Controles de búsqueda */
.search-controls {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

/* DataGrids */
.data-grid {
  margin-top: 10px;
  border: 1px solid #e0e0e0;
}

/* Botones */
.button-group {
  display: flex;
  gap: 5px;
  margin-top: 10px;
  align-items: center;
}

/* Mensajes de permiso */
.permission-message {
  margin-top: 10px;
  font-size: 0.9em;
  padding: 8px;
  background-color: #fff3f3;
  border-left: 4px solid #ff5252;
}

/* Popups */
.popup-content {
  padding: 20px;
  min-width: 800px;
}

.popup-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.popup-divider {
  margin: 15px 0;
}

.popup-info-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.popup-info-item {
  flex: 1;
  min-width: 200px;
  margin-bottom: 8px;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .package-section {
    flex-direction: column;
  }
  
  .left-panel, .right-panel {
    width: 100%;
  }
}

/* Estilo para el popup principal */
.popup-modificar-paquete {
  font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  min-width: 850px;
  max-width: 900px;
}

/* Estilo para las tarjetas dentro del popup */
.popup-card {
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  border: 1px solid #e0e0e0;
}

/* Estilo para los divisores */
.popup-divider {
  margin: 20px 0;
  font-size: 1.1em;
  font-weight: 500;
  color: #2c3e50;
}

/* Contenedor de información */
.info-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin: 20px 0;
}

.info-item {
  flex: 1;
  min-width: 200px;
  padding: 8px 12px;
  background-color: #d0d3d4;
  border-radius: 6px;
  border-left: 3px solid #4a89dc;
}

.info-item p {
  margin: 0;
  font-size: 0.95em;
}

.info-item b {
  color: #2c3e50;
}

/* Estilo para los controles de búsqueda */
.search-controls-popup {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

/* Estilo para el DataGrid */
.popup-datagrid {
  margin-top: 15px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

/* Estilo para los botones */
.button-group-popup {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  align-items: center;
}

/* Mensaje de permisos */
.permission-message-popup {
  margin-top: 12px;
  font-size: 0.85em;
  padding: 8px 12px;
  background-color: #ffeeee;
  border-radius: 4px;
  color: #e74c3c;
  border-left: 3px solid #e74c3c;
}


/* Estilo principal del popup */
.popup-reactivar-paquete {
  font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  min-width: 900px !important;
  max-width: 1000px !important;
}

/* Estilo para las tarjetas */
.reactivar-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  margin-bottom: 20px;
}

/* Divisor estilizado */
.reactivar-divider {
  margin: 15px 0;
  font-size: 1.1em;
  font-weight: 500;
  color: #2c3e50;
}

/* Contenedor de información del paciente */
.paciente-info-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin: 20px 0;
}

.paciente-info-item {
  flex: 1;
  min-width: 250px;
  padding: 10px 15px;
  background-color: #d0d3d4;
  border-radius: 6px;
  border-left: 3px solid #4a89dc;
}

.paciente-info-item p {
  margin: 0;
  font-size: 0.95em;
  color: #4a5568;
}

.paciente-info-item b {
  color: #2c3e50;
}

/* Estilo para el DataGrid */
.reactivar-datagrid {
  margin: 15px 0;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

/* Grupo de botones */
.reactivar-button-group {
  display: flex;
  gap: 15px;
  margin-top: 20px;
  align-items: center;
  flex-wrap: wrap;
}

/* Mensaje de permisos */
.reactivar-permission-message {
  margin-top: 15px;
  padding: 10px 15px;
  background-color: #fff5f5;
  border-radius: 6px;
  color: #e53e3e;
  border-left: 3px solid #e53e3e;
  font-size: 0.9em;
}


/* Barra de búsqueda sticky */
.search-bar-container {
  position: sticky;
  top: 0;
  z-index: 100;
  padding: 10px 0;
}

.search-card {
  border-radius: 8px;
  border: none;
  background-color: #e5e7e9;

}

.search-content {
  display: flex;
  align-items: center;
  padding: 5px 15px;
  gap: 15px;
}

.search-input-container {
  flex-grow: 1;
  max-width: 700px;
  display: flex;
}

.search-buttons {
  display: flex;
  gap: 5px;
  align-items: center;
}

/* Efectos hover para botones */
.search-button {
  transition: all 0.3s ease;
}

.search-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Responsive */
@media (max-width: 768px) {
  .search-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-input-container {
    max-width: 100%;
    
  }
  
  .search-buttons {
    justify-content: flex-end;
  }
}

.button-container-Popup {
    display: flex;
    justify-content: center;
}

/* Estilo principal del popup */
.swal-elegante {
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  padding: 2rem;
  
}

/* Estilo del título */
.swal-title-elegante {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  color: #2c3e50;
  font-size: 1.1rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.swal-btn-elegante:hover {
  transform: translateY(-1px);
}

/* Espaciado entre botones */
.swal-actions-elegante {
  gap: 5px;
}

/* Estilo del icono */
.swal-icon-elegante {
  margin-bottom: 5px;
}

.swal2-container {
    z-index: 55000 !important
}

.btn-iniciar .a-icon {
    display: inline-block;
    padding: 5px;
    color: #333;
}

.btn-iniciar .vs-icon {
    font-size: 18px;
}

/* Estilo del ícono del menú */
.a-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 5px;
    border-radius: 4px;
    color: #52be80;;
    transition: all 0.3s ease;
}

.a-icon:hover {
    background: rgba(0, 0, 0, 0.05);
    color:#52be80 ;
}

.a-icon.active
 {
    background-color: #52be80;
    color: #52be80;
}

/* Estilo del menú desplegable */
.custom-dropdown-menu {
    width: 180px !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    padding: 8px 0 !important;
}

.dropdown-item {
    display: flex;
    align-items: center;
    padding: 8px 16px !important;
    font-size: 14px;
    color: #333;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: #52be80 !important;
    color: #2a52be !important;
}

.dropdown-item i {
    margin-right: 8px;
    font-size: 16px;
}
</style>
