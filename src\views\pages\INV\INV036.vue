<template>
    <div>
        <vx-card title="Reporte de Antimicrobianos, Psicotrópicos y Estupefacientes">
            <vs-row vs-justify="center" vs-align="center">
                <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="3">
                    <label class="typo__label">Del:</label>
                    <vs-input type="date" class="datePicker" v-model="fechaInicial"></vs-input>
                </vs-col>
                <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="3">
                    <label class="typo__label">Al:</label>
                    <vs-input type="date" class="datePicker" v-model="fechaFinal"></vs-input>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-justify="center" vs-align="center">
                <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="4">
                    <label class="typo__label">Sucursal:</label>
                    <multiselect class="datePicker" v-model="cbBodega" :options="listaBodegas" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="seleccion_bodega" placeholder="Búsqueda" >
                        <span slot="noOptions">Lista no disponible.</span>
                    </multiselect>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-justify="center" vs-align="center">
                <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="4">
                    <label class="typo__label">Familia: </label>
                    <multiselect class="datePicker" v-model="cbFamilia" :options="listaFamilias" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="seleccion_familia" placeholder="Búsqueda" @input="onChangeFamilia">
                        <span slot="noOptions">Lista no disponible.</span>
                    </multiselect>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-justify="center" vs-align="center">
                <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="4">
                    <label class="typo__label">Producto:</label>
                    <multiselect class="datePicker" v-model="cbFamiliaProducto" :options="listaFamiliasProducto" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="seleccion_familia_producto" placeholder="Búsqueda" @input="onChangeFamiliaProducto">
                        <span slot="noOptions">Lista no disponible.</span>
                    </multiselect>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-justify="center" vs-align="center">
                <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="4">
                    <vs-button type="filled" icon-pack="fas" icon="fa-file-csv" color="primary" @click="GenerarReporteUnProducto">Generar 1 Producto</vs-button>
                    &nbsp;
                    &nbsp;
                    <vs-button type="filled" icon-pack="fas" icon="fa-file-excel" color="primary" @click="GenerarReporteUnaFamilia">Generar toda la Familia</vs-button>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-justify="center" vs-align="center">
                <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="4">
                    <vs-button type="filled" icon-pack="fas" icon="fa-file-csv" color="primary" @click="GenerarReporteUnProductoIntegrado">Integrado un Producto</vs-button>
                    &nbsp;
                    &nbsp;
                    <vs-button type="filled" icon-pack="fas" icon="fa-file-excel" color="primary" @click="GenerarReporteUnaFamiliaIntegrada">Integrado Familia Completa</vs-button>
                </vs-col>
            </vs-row>
        </vx-card>
    </div>
</template>
<script>
    import moment from 'moment'
    import Multiselect from 'vue-multiselect'
    import "vue-multiselect/dist/vue-multiselect.min.css"
    export default {
        components: {
            Multiselect
        },
        data() {
            return{
                fechaInicial: '',
                fechaFinal: '',
                listaBodegas: [],
                cbBodega: '',
                listaFamilias: [],
                cbFamilia: '',
                listaFamiliasProducto: [],
                cbFamiliaProducto: '',
                codigoBodega: '',
                codigoFamilia: '',
                codigoProducto: ''
            }
        },
        methods: {
            ObtenerFechas(){
                var date = new Date()
                this.fechaInicial = moment(new Date(date.getFullYear(), date.getMonth(), 1)).format('YYYY-MM-DD')
                this.fechaFinal = moment(new Date(date.getFullYear(), date.getMonth() + 1, 0)).format('YYYY-MM-DD')
            },
            ConsultarSucursal() {
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/ConsultarBodegasFarmacia', { 
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Sucursales',
                            text: resp.data.mensaje
                        })
                        this.listaBodegas = ""
                    } else {
                        this.listaBodegas = resp.data.json
                    }
                })
            },
            seleccion_bodega({
                CodigoBodega,
                NombreBodega,
            }) {
                return `${CodigoBodega} - ${NombreBodega} `
            },
            onChangeBodega(value){
                this.codigoBodega = value.CodigoBodega
            },
            ConsultarFamilia() {
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/ConsultarFamilia', { 
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Familias',
                            text: resp.data.mensaje
                        })
                        this.listaFamilias = ""
                    } else {
                        this.listaFamilias = resp.data.json
                    }
                })
            },
            seleccion_familia({
                CodigoFamilia,
                NombreFamilia,
            }) {
                return `${CodigoFamilia} - ${NombreFamilia} `
            },
            onChangeFamilia(value){
                this.ConsultarFamiliaProducto(value)
            },
            ConsultarFamiliaProducto(value) {
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/ConsultarFamiliaProducto', {
                    CodigoFamilia: value.CodigoFamilia
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Producto',
                            text: resp.data.mensaje
                        })
                        this.listaFamiliasProducto = ""
                    } else {
                        this.listaFamiliasProducto = resp.data.json
                    }
                })
            },
            seleccion_familia_producto({
                CodigoProducto,
                NombreProducto,
            }) {
                return `${CodigoProducto} - ${NombreProducto} `
            },
            onChangeFamiliaProducto(){

            },
            GenerarReporteUnProducto(){
                this.$reporte_modal({
                    Nombre: "Psicotropico un Producto",
                    Opciones: {
                        CodigoBodega: this.cbBodega.CodigoBodega,
                        CodigoFamilia: this.cbFamilia.CodigoFamilia,
                        CodigoProducto: this.cbFamiliaProducto.CodigoProducto,
                        FechaInicial: this.fechaInicial,
                        FechaFinal: this.fechaFinal,
                        NombreBodega: this.cbBodega.NombreBodega,
                        NombreFamilia: this.cbFamilia.NombreFamilia,
                        NombreProducto: this.cbFamiliaProducto.NombreProducto
                    },
                    Formato: "EXCEL"
                })
            },
            GenerarReporteUnaFamilia(){
                this.$reporte_modal({
                    Nombre: "Psicotropico una Familia",
                    Opciones: {
                        CodigoBodega: this.cbBodega.CodigoBodega,
                        CodigoFamilia: this.cbFamilia.CodigoFamilia,
                        CodigoProducto: this.cbFamiliaProducto.CodigoProducto,
                        FechaInicial: this.fechaInicial,
                        FechaFinal: this.fechaFinal,
                        NombreBodega: this.cbBodega.NombreBodega,
                        NombreFamilia: this.cbFamilia.NombreFamilia,
                        NombreProducto: this.cbFamiliaProducto.NombreProducto
                    },
                    Formato: "EXCEL"
                })
            },
            GenerarReporteUnProductoIntegrado(){
                this.$reporte_modal({
                    Nombre: "Psicotropico un Producto Integrado",
                    Opciones: {
                        CodigoBodega: this.cbBodega.CodigoBodega,
                        CodigoFamilia: this.cbFamilia.CodigoFamilia,
                        CodigoProducto: this.cbFamiliaProducto.CodigoProducto,
                        FechaInicial: this.fechaInicial,
                        FechaFinal: this.fechaFinal,
                        NombreBodega: this.cbBodega.NombreBodega,
                        NombreFamilia: this.cbFamilia.NombreFamilia,
                        NombreProducto: this.cbFamiliaProducto.NombreProducto
                    },
                    Formato: "EXCEL"
                })
            },
            GenerarReporteUnaFamiliaIntegrada(){
                this.$reporte_modal({
                    Nombre: "Psicotropico una Familia Integrada",
                    Opciones: {
                        CodigoBodega: this.cbBodega.CodigoBodega,
                        CodigoFamilia: this.cbFamilia.CodigoFamilia,
                        CodigoProducto: this.cbFamiliaProducto.CodigoProducto,
                        FechaInicial: this.fechaInicial,
                        FechaFinal: this.fechaFinal,
                        NombreBodega: this.cbBodega.NombreBodega,
                        NombreFamilia: this.cbFamilia.NombreFamilia,
                        NombreProducto: this.cbFamiliaProducto.NombreProducto
                    },
                    Formato: "EXCEL"
                })
            }
        },
        mounted() {
            this.ObtenerFechas()
            this.ConsultarSucursal()
            this.ConsultarFamilia()
        }
    }
</script>
<style scoped>
    .datePicker{
        padding-left: 5px;
    }
</style>