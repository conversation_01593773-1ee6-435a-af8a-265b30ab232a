<template>
<vx-card title="Admisión Hospitalaria">
    <vs-alert active="true" color="success" class="mt-5" icon-pack="feather" icon="icon-star" v-if="$route.query.nuevo == undefined">
        Nueva Admisión Hospitalaria
    </vs-alert>

    <vs-popup id="contentreport" classContent="popup-generar" title="Nuevo Paciente" :active.sync="info.paciente_mostrar" style="z-index:99999;height:100%">
        <Pacientes :paciente="info.paciente" :buscar="true" />
    </vs-popup>

    <vs-popup id="contentreport" classContent="popup-generar" title="Ficha Paciente" :active.sync="info.adicional_mostrar" style="z-index:99999;height:100%">
        <Facturacion :paciente="info.paciente" :nuevo="true" />
    </vs-popup>

    <div class="content content-pagex">

        <vs-divider position="left">Tipo Admisión</vs-divider>
        <!-- 
        <vx-input-group class="">
            <multiselect v-model="TipoAdmision" :options="Tipos" :disabled="TipoAdmision!=null" :allow-empty="false" placeholder="Seleccione la admisión" track-by="Nombre" label="Nombre"></multiselect>
            <template slot="append">
                <div class="append-text btn-addon">
                    <vs-button id="button-with-loading" color="danger" icon-pack="feather" @click="TipoAdmision=null" icon="icon-x" v-if="TipoAdmision!=null"></vs-button>

                </div>
            </template>
        </vx-input-group> -->

        <vx-input-group class="">
            <!-- {{TipoAdmision}} -->
            <ul class="centerx">
                <li v-for="(item,index) in Tipos" :key="index">
                    <vs-radio v-model="TipoAdmision" :vs-value="item.Id">{{item.Nombre}}</vs-radio>
                </li>
            </ul>
        </vx-input-group>

        <!-- {{TipoAdmision}} -->

        <vs-divider />

        <div v-if="TipoAdmision!=null">
            <div class="flex flex-wrap" style="padding:10px 20px" v-if="!$route.query.nuevo == undefined">
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <vs-input label="Serie" v-mask="'AA'" class="w-full" v-on:change="info.serie=info.serie.toUpperCase()" v-model="info.serie" disabled />
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <label class="vs-input--label">Código</label>
                    <vx-input-group class="">
                        <vs-input v-model="info.codigo" disabled />
                        <template slot="append">
                            <div class="append-text btn-addon">
                                <button type="submit" v-show="false" name="button"></button>
                                <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_radiologias()" icon="icon-search" v-if="!nuevo"></vs-button>
                                <!-- <vs-button color="success" icon-pack="feather" @click="info.paciente_mostrar=true" icon="icon-plus"></vs-button> -->
                            </div>
                        </template>
                    </vx-input-group>
                </div>
            </div>

            <div class="flex flex-wrap" style="padding:10px 20px">
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <vs-button id="button-with-loading" color="primary" class="w-full" icon-pack="feather" @click="info.paciente_mostrar=true" icon="icon-search">Paciente</vs-button>
                </div>
                <div class="w-full md:w-4/5 lg:w-4/5 xl:w-4/5 ">
                    <div style="padding:10px 0 0 10px">
                        Juan Pedro Rivera
                        <!-- <div style="font-size:13px">RN</div> -->
                    </div>
                </div>
            </div>

            <div class="flex flex-wrap" style="padding:10px 20px" v-if="TipoAdmision!=2">
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    Habitación:
                    <multiselect v-model="TipoAdmision" :options="Tipos" placeholder="Habitación" track-by="Nombre" label="Nombre"></multiselect>
                </div>
                <div class="w-full md:w-4/5 lg:w-4/5 xl:w-4/5 ">
                    <!-- <div style="padding:10px 0 0 10px">
                        Juan Pedro Rivera
                    </div> -->
                </div>
            </div>

            <div v-if="TipoAdmision==3">

                <vs-divider position="left">Admisión Madre:</vs-divider>
                <div class="flex flex-wrap" style="padding:10px 20px">
                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                        <vs-input label="Serie Admisión *" class="w-full" v-on:change="info.nombre1=info.nombre1.toUpperCase()" v-model="info.nombre1" />
                    </div>
                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                        <vs-input label="Código Admisión *" class="w-full" v-on:change="info.nombres2=info.nombres2.toUpperCase()" v-model="info.nombres2" />
                    </div>
                    <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
                        <vs-input label="Paciente" class="w-full" v-on:change="info.casada=info.casada.toUpperCase()" v-model="info.casada" />
                    </div>
                </div>

            </div>

            <div>
                <vs-divider position="left">Dx (CIE-10)</vs-divider>
                <div class="flex flex-wrap" style="padding:10px 20px">
                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                        <label class="vs-input--label">Código:</label>
                        <vx-input-group class="">
                            <vs-input v-model="info.paciente" disabled />
                            <template slot="append">
                                <div class="append-text btn-addon">
                                    <button type="submit" v-show="false" name="button"></button>
                                    <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_radiologias()" icon="icon-search"></vs-button>
                                    <vs-button id="button-with-loading" color="success" icon-pack="feather" @click="buscar_radiologias()" icon="icon-plus"></vs-button>
                                </div>
                            </template>
                        </vx-input-group>
                    </div>
                </div>
                <div style="padding:10px 20px">
                    <table>
                        <thead>
                            <tr>
                                <th style="width:100px">Cod.</th>
                                <th>Descripción</th>
                                <th style="width:100px">Wrng</th>
                                <th style="width:100px">Espera</th>
                                <th style="width:100px">Inhab.</th>
                            </tr>

                        </thead>

                        <tbody>
                            <tr v-for="(tr,index) in info.dx" :key="index">
                                <td>
                                    <!-- {{tr}} -->
                                    {{ tr.codigo }}
                                </td>
                                <td>
                                    {{tr.descripcion}}
                                </td>
                                <td>
                                    {{tr.wrnq}}
                                </td>
                                <td>
                                    {{tr.espera}}
                                </td>
                                <td>
                                    {{tr.inhab}}
                                </td>
                            </tr>
                        </tbody>
                    </table>

                </div>

                <br>

                <div style="padding:9px;border:1px solid #ccc;border-radius:5px">
                    <label class="vs-input--label">Referencias</label>
                    <!-- label class="vs-input--label">Sexo</label> -->
                    <br>
                    <vs-radio v-model="info.referencia" vs-value="1" >El paciente consulto la emergencia y se llamo al médico del rol</vs-radio>
                    <br>
                    <vs-radio v-model="info.referencia" vs-value="2" >El paciente fue referido por el médico</vs-radio>

                </div>

                <div class="flex flex-wrap" style="padding:10px 20px">
                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                        <vs-input label="Status" class="w-full" v-on:change="info.serie=info.serie.toUpperCase()" v-model="info.serie" />
                    </div>

                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                        <vs-input label="Interno" class="w-full" v-on:change="info.serie=info.serie.toUpperCase()" v-model="info.serie" />
                    </div>
                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                        <vs-input label="Seguro" class="w-full" v-on:change="info.serie=info.serie.toUpperCase()" v-model="info.serie" />
                    </div>
                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                        <vs-input label="NivelPrecios" class="w-full" v-on:change="info.serie=info.serie.toUpperCase()" v-model="info.serie" />
                    </div>
                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                        <div style="padding-top:27px;padding-left:10px">
                            <vs-checkbox v-model="info.paquete">Paquete Qx.</vs-checkbox>
                        </div>
                    </div>
                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                        <vs-input label="Usuario" class="w-full" v-model="info.usuario" disabled />
                    </div>
                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                        <vs-input label="Fecha Ingreso" class="w-full" v-model="info.ingreso" disabled />
                    </div>
                </div>
            </div>

            <vs-divider></vs-divider>
            <vs-button style="float:right" @click="guardar_informe(true)" > Guardar</vs-button>
            <div style="clear:both"></div>
        </div>
        <!-- </vx-card> -->
    </div>
</vx-card>
</template>

<script>
// require styles
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'

import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";

export default {
    data() {
        return {
            nuevo: true,
            permisos: {
                crear: false
            },
            examenes: [],
            info: {
                serie: null,
                codigo: null,
                paciente: "",
                paciente_mostrar: false,
                adicional_mostrar: false,
                sexo: null,
                dx: [{
                    codigo: null,
                    descripcion: null,
                    wrnq: null,
                    espera: null,
                    inhab: null,
                }],
                referencia: 1,
                paquete: null,
                usuario: null,
                ingreso: null
            },
            TipoAdmision: null,
            Tipos: [{
                    Id: 1,
                    Nombre: 'Interno'
                },
                {
                    Id: 2,
                    Nombre: 'Emergencia'
                },
                {
                    Id: 3,
                    Nombre: 'RN Hospital'
                },
                {
                    Id: 4,
                    Nombre: 'Paquete Qx'
                }
            ],
            editorOption: {
                modules: {
                    toolbar: '#toolbar'
                },
                placeholder: 'Informe'
            },
        }
    },
    props: {
        IdFuncionalidad: {
            default: null
        }
    },
    components: {
        Multiselect,
        Pacientes: () => import('@/views/pages/ADMISION/ADM005.vue'),
        Facturacion: () => import('@/views/pages/ADMISION/ADM006.vue')

    },
    methods: {

    },
    mounted() {
        this.nuevo = (this.$route.query.nuevo == undefined) ? true : this.$route.query.nuevo
        this.permisos.crear = this.$validar_privilegio('CREAR').status && this.nuevo
    }
}
</script>

<style scoped>
.centerx {
    display: flex;
}

.centerx li {
    margin-right: 10px
}

table {
    width: 100%;
}

th {
    border-bottom: 1px solid #ccc;
}

td {
    padding: 5px;
    height: 30px;
    border-bottom: 1px solid #ccc;
    border-right: 1px solid #ccc;
}

td:nth-child(1) {
    border-left: 1px solid #ccc;
}

.center li {
    display: inline !important;
    padding: 2px 10px;
}

.button {
    margin: 0px !important;
}

.btnx {
    margin-left: 10px !important;
    background: red;
    border-radius: 5px 0px 0px 5px;
}

.btn-drop {
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: red;

}

.menu-funcionalidad {
    /* padding-top:0 !important; */
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    padding: 15px;
    display: inline;
    float: right;
    border-radius: 10px;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 10px;
}

.menu-funcionalidad td {
    text-align: center;
    padding-right: 10px;
}

.vs-switch {
    height: 30px;
    width: 60px !important;
}

.vs-circle-switch {
    height: 25px;
    width: 25px;
}

.vs-switch--icon {
    font-size: 15px
}

.vs-switch.vs-switch-active .vs-switch--circle {
    margin-left: calc(100% - 30px);
}
</style>
