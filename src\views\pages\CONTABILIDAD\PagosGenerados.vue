<template>
<div>
    <form @submit="handleSubmit">
        <DxForm :ref="formPagos" :form-data.sync="formulario" label-mode="floating">
            <DxFormGroupItem :col-count="2">
                <!-- :col-count-by-screen="colCountByScreen"> -->
                <DxFormGroupItem caption="Cuenta de banco">
                    <DxFormItem data-field="Cuenta" editor-type="dxSelectBox" :editor-options="{ disabled: this.periodo === null, width: 'auto', searchEnabled: true, items: cuentas,  showCancelButton: false, displayExpr: 'Valor', valueExpr: 'Codigo', displayValue: 'Display', dropDownOptions: dropDownOptions, onValueChanged: ConsultarSaldoFinal }" :validationRules="[{ type: 'required' }]" />
                    <DxFormItem data-field="Cheque" editor-type="dxNumberBox" :editor-options="{ disabled: this.periodo === null, min: 0, format: '#0', step: 0 }">
                        <DxFormLabel text="Primer cheque" />
                    </DxFormItem>
                </DxFormGroupItem>
                <DxFormGroupItem caption="Proveedores">
                    <DxFormItem data-field="CodigoProv1" editor-type="dxSelectBox" :editor-options="{ dataSource: proveedoresDataSource, showCancelButton: false, displayExpr: 'Display', valueExpr: 'Codigo', searchEnabled: true, showClearButton: true }" :validationRules="[{ type: 'required' }]">
                        <DxFormLabel text="Del proveedor" />
                    </DxFormItem>
                    <DxFormItem data-field="CodigoProv2" editor-type="dxSelectBox" :editor-options="{ dataSource: proveedoresDataSource, showCancelButton: false, displayExpr: 'Display', valueExpr: 'Codigo', searchEnabled: true, showClearButton: true }" :validationRules="[{ type: 'required' }]">
                        <DxFormLabel text="Al proveedor" />
                    </DxFormItem>
                </DxFormGroupItem>
                <DxFormItem :col-span="2" :button-options="buttonGuardar" :editor-options="{ disabled: this.deshabilitarGuardar }" item-type="button" horizontal-alignment="center" verical-alignment="center" />
            </DxFormGroupItem>
        </DxForm>
    </form>

</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'
import 'devextreme-vue/lookup'

const formPagos = 'formPagos'

export default {
    name: 'PagosGenerados',
    components: {},
    data() {
        return {
            DefaultDxGridConfiguration,
            formPagos,

            formulario: {
                Cuenta: null,
                Contabilidad: null,
                Cheque: null,
                CodigoProv1: null,
                CodigoProv2: null,
                SiguienteNota: null
            },
            cuentas: [],

            tiposAnticipo: [],

            proveedorActivo: null,

            mostrarModalProveedores: false,
            proveedores: [],
            proveedoresDataSource: {
                paginate: true,
                pageSize: 10,
                sort: 'Codigo',
                key: 'Codigo',
                // loadMode: 'raw',
                byKey: (e) => {
                    return this.proveedores.find(x => x.Codigo === e)
                },
                load: (e) => {
                    if (e.take) {
                        if (e.skip === e.take) {
                            e.take = e.take + 10
                        }
                    }
                    return this.$filtrado_avanzado_palabras(e.searchValue, this.proveedores, 'Display', e.skip, e.skip + e.take)
                }
            },

            dropDownOptions: {
                showTitle: false,
                hideOnOutsideClick: true
            },

            buttonGuardar: {
                width: 'auto',
                icon: 'fas fa-save',
                text: 'Guardar',
                type: 'success',
                useSubmitBehavior: false,
                onClick: () => {
                    this.Pagos()
                }
            },

            opciones: {}, // Almacena los datos de IVA y de ISR
            porcenjateIVA: null,

            periodo: null,
            existeSaldoFinal: true,
            pagos: [],
            totalServicios: 0,
            totalMercaderia: 0,
            observaciones: '',

            cuentaIVAHaber: null
        }

    },
    props: {
        TipoDoc: null, // Indica desde qué módulo se invoca el componente  1 = Cheques -> Proveedores -> Anticipos / 2 = Transferencias -> Proveedores -> Anticipos
        Corporativo: null
    },
    methods: {
        handleSubmit(e) {
            e.preventDefault()
        },
        async CargarCuentasDefault() {
            await this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 9
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.cuentaIVAHaber = resp.data.json[0].IVAPorPagar
                    }

                })
        },
        async CargarCuentas() {
            await this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 1
                })
                .then(resp => {
                    this.cuentas = resp.data.json

                    this.formulario.Cuenta = this.cuentas[0].Codigo
                })
        },
        CargarOpciones() {
            this.axios.post('/app/v1_bancos/Opciones', {
                    Opcion: 1
                })
                .then(resp => {
                    this.opciones = resp.data.json[0]

                    this.porcenjateIVA = parseFloat(resp.data.json[0].IVA / 100).toFixed(2)
                })
        },

        async BuscarProveedores() {
            await this.axios.post('/app/v1_bancos/Proveedores', {
                    Opcion: 4
                })
                .then(resp => {
                    this.proveedores = resp.data.json.map((x) => {
                        return {
                            Codigo: x.Codigo,
                            Nombre: x.Nombre,
                            Nit: x.Nit,
                            Retencion: parseFloat(x.Retencion).toFixed(2),
                            TipoRetencion: x.TipoRetencion,
                            CuentaRetencion: x.CuentaRetencion,
                            ContaAnticipo: x.ContaAnticipo,
                            NombreCheque: x.NombreCheque,
                            CuentaBanco: x.CuentaBanco,
                            Display: x.Codigo + ' - ' + x.Nit + ' - ' + x.Nombre
                        }
                    })
                })
        },
        async ProveedoresPago() {
            await this.axios.post('/app/v1_bancos/PagosGenerados', {
                    Opcion: 7
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        let proveedores = resp.data.json

                        setTimeout(() => {
                            this.formulario.CodigoProv1 = proveedores[0].Proveedor
                            this.formulario.CodigoProv2 = proveedores[proveedores.length - 1].Proveedor
                            
                            this.formPagosInstance.repaint()
                        }, 500);
                    }
                })
        },
        async ConsultarPeriodo() {
            await this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 1,
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.periodo = resp.data.json[0].Codigo
                    } else {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Período no definido',
                            acceptText: 'Aceptar',
                            text: 'No existe un período para la fecha actual.',
                            buttonCancel: 'border',
                            accept: () => {},
                        })
                        return
                    }
                })
        },
        async ConsultarSaldoFinal(e) {
            if (this.formulario.Cuenta !== null) {
                let cuenta = this.cuentas.find((x) => x.Codigo == e.value)
                this.formulario.Contabilidad = cuenta.Contabilidad
                if (this.TipoDoc === 1) {
                    this.formulario.Cheque = cuenta.SiguienteCheque
                } else if (this.TipoDoc === 2) {
                    this.formulario.Cheque = this.formulario.SiguienteNota
                }
                this.formPagosInstance.repaint()
            }

            await this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 2,
                    Cuenta: this.formulario.Cuenta,
                    Periodo: this.periodo
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        if (resp.data.json[0].SaldoFinal !== null) {
                            this.existeSaldoFinal = true

                            this.$vs.dialog({
                                type: 'alert',
                                color: '#ed8c72',
                                title: 'Período no activo',
                                acceptText: 'Aceptar',
                                text: 'Debe reabrir el registro del libro negro para este período para poder grabar esta transacción.',
                                buttonCancel: 'border',
                                accept: () => {},
                            })
                            return
                        } else {
                            this.existeSaldoFinal = false
                        }
                    } else {
                        this.existeSaldoFinal = false
                    }

                    this.formPagosInstance.repaint()
                })
        },
        async Pagos() {
            // Accesa a la información de los cheques
            await this.axios.post('/app/v1_bancos/PagosGenerados', {
                    Opcion: this.TipoDoc, // 1 = CH / 2 = NA
                    ProveedorInicio: this.formulario.CodigoProv1,
                    ProveedorFin: this.formulario.CodigoProv2,
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.pagos = resp.data.json

                        this.ComprobarCorrelativos()
                    }
                })
        },
        async ComprobarCorrelativos() {
            // Comprueba que el correlativo sea correcto
            await this.axios.post('/app/v1_bancos/PagosGenerados', {
                    Opcion: 3,
                    Cuenta: this.formulario.Cuenta,
                    TipoDoc: this.TipoDoc == 1 ? 'CH' : 'NP',
                    CodigoInicio: this.formulario.Cheque,
                    CodigoFin: parseInt(this.formulario.Cheque + parseInt(this.pagos.length - 1))
                })
                .then(resp => {
                    if (resp.data.json[0].Cuantos === 0) { // No existen registros
                        this.Grabar()
                    } else {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Registros existentes',
                            acceptText: 'Aceptar',
                            text: 'Existen cheques o acreditamientos a partir del número indicado.',
                            buttonCancel: 'border',
                            accept: () => {},
                        })
                        return
                    }
                })
        },
        async FacturasEspeciales(proveedor) {
            // Esto sirve para la retención de facturas especiales
            return await this.axios.post('/app/v1_bancos/PagosGenerados', {
                    Opcion: 4,
                    ProveedorInicio: proveedor
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.observaciones = resp.data.json[0].Observaciones
                        this.totalServicios = 0
                        this.totalMercaderia = 0

                        for (let i = 0; i < resp.data.json.length; i++) {
                            if (resp.data.json[i].Tipo == 'E') {
                                this.totalServicios = parseFloat(this.totalServicios + parseFloat(resp.data.json[i].ServiciosAfectos).toFixed(2)).toFixed(2)
                                this.totalMercaderia = parseFloat(this.totalMercaderia + parseFloat(resp.data.json[i].MercaderiaAfecta).toFixed(2)).toFixed(2)
                            }
                        }

                        return resp.data.json
                    }
                })
        },
        async BuscarEgresos(proveedor, documento) {
            return await this.axios.post('/app/v1_bancos/PagosGenerados', {
                    Opcion: 5,
                    ProveedorInicio: proveedor,
                    Documento: documento
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        return resp.data.json[0].Cuenta
                    }
                })
        },
        async BuscarComprasPagos(proveedor, cheque) {
            return await this.axios.post('/app/v1_bancos/PagosGenerados', {
                    Opcion: 6,
                    ProveedorInicio: proveedor,
                    Cuenta: this.formulario.Cuenta,
                    Cheque: cheque
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        return resp.data.json
                    }
                    return null
                })
        },
        async Grabar() {
            let numeroCheque = this.formulario.Cheque

            for (let i = 0; i < this.pagos.length; i++) {

                if (this.TipoDoc == 1 || (this.TipoDoc !== 1 && this.pagos[i].CuentaBanco !== '')) {
                    let pagosGenerados = {
                        BancoMovto: {},
                        AuxiliarBanco: [],
                        Pagos: {},
                        Referencias: [],
                        Cupones: [],
                        Multiniveles: [],
                        Saldos: {}
                    }
                    let pagosVoucher = await this.FacturasEspeciales(this.pagos[i].Proveedor),
                        baseRetencion = 0,
                        valorCheque = 0

                    if (this.totalMercaderia > 0 || this.totalServicios > 0) {
                        baseRetencion = parseFloat(this.totalMercaderia + this.totalServicios).toFixed(2)
                        valorCheque = parseFloat(baseRetencion).toFixed(2)
                    } else {
                        baseRetencion = parseFloat(parseFloat((100 * this.pagos[i].Valor) / parseFloat(1 + this.porcenjateIVA)) / 100).toFixed(2)
                        valorCheque = parseFloat(this.pagos[i].Valor).toFixed(2)
                    }

                    pagosGenerados.BancoMovto = {
                        Opcion: 1,
                        Cuenta: this.formulario.Cuenta,
                        Numero: parseInt(numeroCheque),
                        Periodo: this.periodo,
                        Proveedor: this.pagos[i].Proveedor,
                        Monto: parseFloat(valorCheque).toFixed(2),
                        Usuario: this.Corporativo,
                        Observaciones: this.observaciones,
                        TipoMov: this.TipoDoc == 1 ? 'CH' : 'NP',
                        NombreBeneficiario: this.pagos[i].NombreCheque,
                        TipoCheque: 'P',
                        Conciliado: 'N'
                    }

                    let indice = 1

                    pagosGenerados.AuxiliarBanco.push({
                        Opcion: 4,
                        TipoMov: this.TipoDoc == 1 ? 'CH' : 'NP',
                        Cuenta: this.formulario.Cuenta,
                        Numero: parseInt(numeroCheque),
                        Periodo: this.periodo,
                        CuentaBanco: this.formulario.Contabilidad,
                        Documento: parseInt(numeroCheque),
                        TipoDocumento: 'C',
                        Haber: parseFloat(valorCheque).toFixed(2),
                        Indice: indice
                    })

                    if (this.totalMercaderia > 0 || this.totalServicios > 0) {
                        indice = parseInt(indice + 1)
                        pagosGenerados.AuxiliarBanco.push({
                            Opcion: 4,
                            TipoMov: this.TipoDoc == 1 ? 'CH' : 'NP',
                            Cuenta: this.formulario.Cuenta,
                            Detalle: 'Retención IVA Factura Especial',
                            Numero: parseInt(numeroCheque),
                            Periodo: this.periodo,
                            CuentaBanco: this.cuentaIVAHaber,
                            Documento: this.formulario.Cheque,
                            TipoDocumento: 'C',
                            Haber: parseFloat(parseFloat(100 * ((this.totalMercaderia + this.totalServicios) * this.porcenjateIVA)).toFixed(2) / 100).toFixed(2),
                            Indice: parseInt(indice)
                        })
                    }

                    for (let j = 0; j < pagosVoucher.length; j++) {
                        let cuenta = await this.BuscarEgresos(this.pagos[i].Proveedor, pagosVoucher[j].Documento)

                        indice = parseInt(indice + 1)
                        pagosGenerados.AuxiliarBanco.push({
                            Opcion: 4,
                            TipoMov: this.TipoDoc == 1 ? 'CH' : 'NP',
                            Cuenta: this.formulario.Cuenta,
                            TipoDocumento: pagosVoucher[j].Tipo,
                            Documento: pagosVoucher[j].Documento,
                            Numero: parseInt(numeroCheque),
                            Periodo: this.periodo,
                            CuentaBanco: cuenta == null ? this.pagos[i].ContaCredito : cuenta,
                            OrdenCompra: pagosVoucher[j].OrdenAutorizada,
                            ValidacionCompra: pagosVoucher[j].Validacion,
                            Haber: pagosVoucher[j].Monto > 0 ? null : -parseFloat(pagosVoucher[j].Monto).toFixed(2), //Facturas con saldo negativo por notas o anticipos
                            Debe: pagosVoucher[j].Monto > 0 ? parseFloat(pagosVoucher[j].Monto).toFixed(2) : null,
                            Indice: parseInt(indice)
                        })
                    }

                    pagosGenerados.Pagos = {
                        Opcion: 8,
                        Cuenta: this.formulario.Cuenta,
                        TipoDoc: this.TipoDoc == 1 ? 'CH' : 'NP',
                        Cheque: parseInt(numeroCheque),
                        Periodo: this.periodo,
                        ProveedorInicio: this.pagos[i].Proveedor
                    }

                    let comprasPagos = await this.BuscarComprasPagos(this.pagos[i].Proveedor, numeroCheque)

                    if (comprasPagos !== null) {
                        for (let j = 0; j < comprasPagos.length; j++) {
                            let nuevo = {
                                Cuenta: this.formulario.Cuenta,
                                Cheque: parseInt(numeroCheque),
                                ProveedorInicio: this.pagos[i].Proveedor,
                                Documento: comprasPagos[j].Documento
                            }
                            switch (comprasPagos[j].Tipo) {
                                case 'C':
                                    nuevo.Opcion = 9
                                    nuevo.TipoDoc = this.TipoDoc == 1 ? 'CH' : 'NP'
                                    pagosGenerados.Referencias.push(nuevo)
                                    break;

                                case 'U':
                                    nuevo.Opcion = 10
                                    pagosGenerados.Cupones.push(nuevo)
                                    break;

                                case 'G':
                                    nuevo.Opcion = 11
                                    pagosGenerados.Multiniveles.push(nuevo)
                                    break;
                            }
                        }
                    }

                    pagosGenerados.Saldos = {
                        Opcion: 12,
                        Cuenta: this.formulario.Cuenta,
                        Cheque: parseInt(numeroCheque)
                    }

                    await this.axios.post('/app/v1_bancos/PagosGeneradosAgrupados', {
                            ...pagosGenerados
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                numeroCheque = parseInt(numeroCheque + 1)
                            }
                        })

                }
            }

            if (numeroCheque !== this.formulario.Cheque) {
                if (this.TipoDoc == 1) {
                    // Incrementa el número de cheque
                    await this.axios.post('/app/v1_bancos/Cuentas', {
                        Codigo: this.formulario.Cuenta,
                        SiguienteCheque: parseInt(numeroCheque),
                        Opcion: 6
                    })
                } else if (this.TipoDoc == 2) {
                    // Actualiza el correlativo de la nota de acreditamiento
                    await this.axios.post('/app/v1_bancos/Anticipos', {
                        Opcion: 2,
                        Numero: parseInt(numeroCheque),
                        TipoMov: 'NP',
                    })
                }
            }

            if (this.TipoDoc == 1) { // CH
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'Cheques emitidos',
                    acceptText: 'Aceptar',
                    text: 'Primero: ' + this.formulario.Cheque + '\nÚltimo: ' + parseInt(numeroCheque - 1),
                    buttonCancel: 'border',
                    accept: () => {},
                })
            } else if (this.TipoDoc == 2) { // NP
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'Acreditamientos emitidos',
                    acceptText: 'Aceptar',
                    text: 'Primero: ' + this.formulario.Cheque + '\nÚltimo: ' + parseInt(numeroCheque - 1),
                    buttonCancel: 'border',
                    accept: () => {},
                })
            }

            this.LimpiarVariables()
        },

        SeleccionarProveedor(e) {
            if (this.proveedorActivo == 1) {
                this.formulario.CodigoProv1 = e.data.Codigo
                this.formulario.NombreProv1 = e.data.Nombre
            }

            if (this.proveedorActivo == 2) {
                this.formulario.CodigoProv2 = e.data.Codigo
                this.formulario.NombreProv2 = e.data.Nombre
            }

            this.formPagosInstance.repaint()
        },
        LimpiarVariables() {
            this.formulario = {
                Cuenta: null,
                Contabilidad: null,
                Cheque: null,
                CodigoProv1: null,
                CodigoProv2: null,
                SiguienteNota: null
            }

            this.ProveedoresPago()

            if (this.TipoDoc == 1) {
                this.CargarCuentas()
            } else {
                this.BuscarSiguienteNota()
            }

        },
        BuscarSiguienteNota() {
            this.axios.post('/app/v1_bancos/Anticipos', {
                    Opcion: 7,
                    TipoMov: 'NP'
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.formulario.SiguienteNota = resp.data.json[0].SiguienteNota
                        this.formulario.Cheque = resp.data.json[0].SiguienteNota

                        this.formPagosInstance.repaint()
                    }
                })
        },
    },
    mounted() {
        this.CargarCuentas()
        this.CargarOpciones()
        this.ProveedoresPago()
        this.formPagosInstance.repaint()
    },
    beforeMount() {

        if (this.TipoDoc === 2) {
            this.BuscarSiguienteNota()
        }
        this.BuscarProveedores()
        this.CargarCuentasDefault()
        this.ConsultarPeriodo()
    },
    watch: {},
    computed: {
        colCountByScreen() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 1,
                    lg: 2
                };
        },

        formPagosInstance: function () {
            return this.$refs[formPagos].instance;
        },

        deshabilitarGuardar: function () {
            //Si no falta ningún dato, devuelve falso para que no se deshabilite el botón
            if (this.formulario.Cuenta !== null && this.formulario.Cuenta !== '' && this.formulario.CodigoProv1 !== null && this.formulario.CodigoProv1 !== '' && this.formulario.CodigoProv2 !== null && this.formulario.CodigoProv2 !== '' && !this.existeSaldoFinal) {
                return false
            }
            return true
        }
    }
}
</script>

<style>
.buttonsProveedores .dx-item-content .dx-box-item-content {
    place-content: end !important;
}

.vs-dialog-text {
    text-align: center;
}
</style>
