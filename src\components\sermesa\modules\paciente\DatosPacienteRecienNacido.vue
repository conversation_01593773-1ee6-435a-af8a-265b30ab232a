
<template>
    <div class="container">
        <vx-card title="Datos personales">

            <ValidationObserver ref="personalDataRN" v-slot="{ invalid, handleSubmit }" mode="lazy">
                <form @submit.prevent="handleSubmit(submitForm(submit))">
                    
                        <div class="container_div ">

                            <div class="container pl-4 mx-auto">

                                <vs-row>

                                    <vs-col vs-type="flex" vs-w="6">
                                        <div class="w-2/3  ">
                                            <validationProvider name="Primer Nombre Paciente" rules="required|min:3">
                                                <vs-input label="Primer Nombre Paciente" class="w-full  code"
                                                    v-model.Trim="PacienteData.PrimerNombre" />
                                            </validationProvider>
                                        </div>
                                    </vs-col>

                                    <vs-col vs-type="flex" vs-w="6">
                                        <div class="w-2/3  ">
                                            <vs-input label="Segundo Nombre Paciente" class="w-full "
                                                v-model="PacienteData.SegundoNombre" />
                                             <vs-input v-model="NombrePaciente" class="w-full"  type="hidden"/>
                                        </div>
                                    </vs-col>
                                </vs-row>

                                <vs-row>
                                    <vs-col vs-type="flex" vs-w="6">
                                        <div class="w-2/3  ">
                                            <validationProvider name="Primer Apellido Paciente" class="required"
                                                rules="required|min:3" v-slot="{ errors }">
                                                <vs-input label="Primer Apellido Paciente" class="w-full  code"
                                                    v-model="PacienteData.PrimerApellido" :danger="errors.length > 0"
                                                    :danger-text="(errors.length > 0) ? errors[0] : null" :maxlength="10" />
                                            </validationProvider>
                                        </div>
                                    </vs-col>

                                    <vs-col vs-type="flex" vs-w="6">
                                        <div class="w-2/3  ">
                                            <vs-input label="Segundo Apellido Paciente" class="w-full "
                                                v-model="PacienteData.SegundoApellido" />
                                        </div>
                                    </vs-col>
                                </vs-row>
                                <vs-row>
                                    <vs-col vs-type="flex" vs-w="6">
                                        <div class="w-2/3  ">
                                            <validationProvider name="Apellido Casada">
                                                <vs-input label="Apellido Casada" class="w-full "
                                                    v-model="PacienteData.ApellidoCasada" />
                                            </validationProvider>
                                        </div>
                                    </vs-col>

                                    <vs-col vs-type="flex" vs-w="6">
                                        <div class="w-2/3  ">
                                            <validationProvider name="Fecha de Nacimiento" class="required"
                                                rules="required|min:10" v-slot="{ errors }">
                                                <vs-input label="Fecha de Nacimiento" class="w-full " type="date"
                                                    v-model="PacienteData.Nacimiento" :danger="errors.length > 0"
                                                    :danger-text="(errors.length > 0) ? errors[0] : null" :maxlength="20" />
                                            </validationProvider>
                                        </div>
                                    </vs-col>

                                </vs-row>

                                <vs-row>
                                    <vs-col vs-type="flex" vs-w="6">
                                        <div class="w-2/3  ">
                                          
                                        </div>
                                    </vs-col>

                                   
                                </vs-row>

                                <vs-row>
                                    <vs-col vs-type="flex" vs-w="6">
                                        <div class="w-2/3  ">
                                            <validationProvider name="Sexo" class="required" rules="required" v-slot="{ errors }">
                                                <label class="vs-input--label">Sexo </label>
                                              <multiselect
                                                v-model="SelSexo"
                                                :options="Sexos"
                                                :disabled="false"
                                                :allow-empty="false"
                                                :taggable="true"
                                                placeholder="Selección de Sexo"
                                                track-by="Nombre"
                                                label="Nombre">
                                                </multiselect>
                                                <span class="error-msg">{{ errors[0] }}</span>
                                            </validationProvider>

                                        </div>
                                    </vs-col>

                                    <vs-col vs-type="flex" vs-w="6">
                                        <div class="w-2/3  ">
                                            <label class="vs-input--label">Estado Civil </label>
                                         <multiselect
                                           v-model="SelEstadoCivil"
                                           :options="EstadosCiviles"
                                           :disabled="false"
                                           :allow-empty="false"
                                           placeholder="Selección de Estado Civil"
                                           track-by="Nombre"
                                           label="Nombre">
                                           </multiselect>
                                        </div>
                                    </vs-col>
                                </vs-row>

                                <vs-row>
                                    <vs-col vs-type="flex" vs-w="6">
                                        <div class="w-2/3  ">
                                            <validationProvider name="Nacionalidad">
                                                <vs-input label="Nacionalidad" class="w-full "
                                                    v-model.trim="Nacionalidad" />
                                            </validationProvider>
                                        </div>
                                    </vs-col>

                                    <vs-col vs-type="flex" vs-w="6">
                                        <div class="w-2/3  ">
                                            <vs-input label="Profesión/Oficio" class="w-full "
                                                v-model.trim="PacienteData.Profesion" />

                                        </div>
                                    </vs-col>
                                </vs-row>

                                <vs-row>

                                </vs-row>
                                <vs-row>
                                    <vs-col vs-type="flex" vs-w="10">
                                        <div class="w-full pt-2  ml-2">
                                            <h6>Documentos de Identificación</h6>
                                            <hr
                                                style="height: 4px; border: 0; box-shadow: inset 0 12px 12px -12px rgba(0, 0, 0, 0.5)">
                                        </div>
                                    </vs-col>
                                </vs-row>

                                <vs-row>
                                    <vs-col vs-type="flex" vs-w="4">
                                        <div class="w-1/2  ">
                                            <validationProvider name="DPI">
                                                <vs-input label="DPI" class="w-full " v-model="PacienteData.DPI" />
                                            </validationProvider>
                                        </div>
                                    </vs-col>

                                    <vs-col vs-type="flex" vs-w="4">
                                        <div class="w-1/2  ">
                                            <validationProvider name="NIT">
                                                <vs-input label="NIT" class="w-full " v-model="PacienteData.Nit" />
                                            </validationProvider>
                                        </div>
                                    </vs-col>

                                    <vs-col vs-type="flex" vs-w="4">
                                        <div class="w-1/2  ">
                                            <validationProvider name="No. Pasaporte">
                                                <vs-input label="No. Pasaporte" class="w-full "
                                                    v-model="PacienteData.Pasaporte" />
                                            </validationProvider>
                                        </div>
                                    </vs-col>
                                </vs-row>


                                <!----------------------------------------------------------------------------------------------------------------------------------->
                                <!----------------------------------------------PACIENTE MENOR DE EDAD      -------------------------------------------------------->

                                <vs-row>
                                    <vs-col vs-type="flex" vs-w="10">
                                        <div class="w-full  pt-2 ml-2">
                                            <h6>Datos Persona Responsable (Menor de edad)</h6>
                                            <hr
                                                style="height: 4px; border: 0; box-shadow: inset 0 12px 12px -12px rgba(0, 0, 0, 0.5)">
                                        </div>
                                    </vs-col>
                                </vs-row>

                                <vs-row>
                                    <vs-col vs-type="flex" vs-w="5">
                                        <div class="w-2/3  ">
                                            <validationProvider name="Nombre Padre">
                                                <vs-input label="Nombre del Padre" class="w-full "
                                                    v-model="PacienteData.Padre" />
                                            </validationProvider>
                                        </div>
                                    </vs-col>

                                    <vs-col vs-type="flex" vs-w="3">
                                        <div class="w-2/3  ">
                                            <validationProvider name="DPI">
                                                <vs-input label="DPI del Padre" class="w-full "
                                                    v-model="PacienteData.DPIPadreResponsable" />
                                            </validationProvider>
                                        </div>
                                    </vs-col>

                                    <vs-col vs-type="flex" vs-w="2">
                                        <div class="w-2/3  ">
                                            <validationProvider name="Telefono">
                                                <vs-input label="Tel. Padre" class="w-full "
                                                    v-model="PacienteData.TelefonoPadreResponsable" />
                                            </validationProvider>
                                        </div>
                                    </vs-col>
                                </vs-row>

                                <vs-row>
                                    <vs-col vs-type="flex" vs-w="5">
                                        <div class="w-2/3  ">
                                            <validationProvider name="Nombre Madre">
                                                <vs-input label="Nombre de la Madre" class="w-full "
                                                    v-model="PacienteData.Madre" />
                                            </validationProvider>
                                        </div>
                                    </vs-col>

                                    <vs-col vs-type="flex" vs-w="3">
                                        <div class="w-2/3  ">
                                            <validationProvider name="DPI">
                                                <vs-input label="DPI de la Madre" class="w-full "
                                                    v-model="PacienteData.DPIMadreResponsable" />
                                            </validationProvider>
                                        </div>
                                    </vs-col>

                                    <vs-col vs-type="flex" vs-w="2">
                                        <div class="w-2/3  ">
                                            <validationProvider name="Telefono">
                                                <vs-input label="Tel. Madre" class="w-full "
                                                    v-model="PacienteData.TelefonoMadreResponsable" />
                                            </validationProvider>
                                        </div>
                                    </vs-col>
                                </vs-row>
                               
                            </div>

                        </div>
                        <div v-show="showButtons">
                            <vs-button  style="margin-right:1px" 
                                        v-on:click="handleSubmit(submit())" 
                                        :disabled="invalid"
                                        :clientWidth="20">
                                Guardar
                            </vs-button>
                        </div>
                      
                </form>
            </ValidationObserver>
        </vx-card>

    </div>
</template>
  
<script>


import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";



import { mapFields } from "vuex-map-fields";
import moment from "moment";


export default {
    props: ['renderComp'],
    name: "DatosGeneralesPacRecienNacido",
    data: () => ({
        //PacienteData: {},
        activeStep: 0,
        NumeroDocumento: null,
        NombrePaciente:'',
        SelSexo: '',
        SelEstadoCivil: '',
        SelTipoDocumento: '',
        showButtons: false,
        Sexos: [
            {
                Codigo: 'M',
                Nombre: 'MASCULINO'
            },
            {
                Codigo: 'F',
                Nombre: 'FEMENINO'
            }
        ],
        EstadosCiviles: [
            {
                Codigo: 'C',
                Nombre: 'CASADO'
            },
            {
                Codigo: 'S',
                Nombre: 'SOLTERO'
            }
        ],
        TiposDocumento: [
            {
                Codigo: 'D',
                Nombre: 'DPI'
            },
            {
                Codigo: 'P',    
                Nombre: 'PASAPORTE'
            }
        ]
    }),
    watch:{
        'SelSexo' : function (value) {
            switch(value.Codigo){
                case 'M':
                    this.PacienteData.PrimerNombre = 'RN. Hijo de/'+ this.PacienteData.PrimerNombre.replace(/\s+/g, ' ').trim()
                break
                case 'F':
                    this.PacienteData.PrimerNombre = 'RN. Hija de/'+ this.PacienteData.PrimerNombre.replace(/\s+/g, ' ').trim()
                break
                default:
                    this.PacienteData.PrimerNombre = 'RN. Hijo de/'+ this.PacienteData.PrimerNombre.replace(/\s+/g, ' ').trim()
                break
            }
            
        },
    },
    components: {
        Multiselect
    },
    beforeUpdate(){
        this.PacienteData.Nacimiento = this.Nacimiento
    },
    beforeMount(){
        this.PacienteData.Nacimiento = this.Nacimiento
    },
    mounted() {
                
        this.SelSexo = ''
        this.SelEstadoCivil = this.EstadosCiviles.find(option => option.Codigo === 'S')
        this.NombrePaciente = this.PacienteData.PrimerNombre.replace(/\s+/g, ' ').trim()
        this.PacienteData.SegundoNombre   = this.PacienteData.SegundoNombre.replace(/\s+/g, ' ').trim()
        this.PacienteData.PrimerApellido  = this.PacienteData.PrimerApellido.replace(/\s+/g, ' ').trim()
        this.PacienteData.SegundoApellido = this.PacienteData.SegundoApellido.replace(/\s+/g, ' ').trim()
        this.PacienteData.Codigo = null
        this.PacienteData.IdCliente='' 
        this.PacienteData.Nacimiento = this.Nacimiento
        // eslint-disable-next-line no-constant-condition
        this.PacienteData.Nacionalidad =  isNaN(this.PacienteData.Nacionalidad)? this.PacienteData.Nacionalidad.toUpperCase() :"GUATEMALTECO(A)"
        this.PacienteData.Profesion =  isNaN(this.PacienteData.Profesion)? this.PacienteData.Profesion.toUpperCase() :""
        
    },
    computed: {
        ...mapFields('paciente', ["PacienteData"]),
        Nacionalidad: function(){
                return isNaN(this.PacienteData.Nacionalidad)? this.PacienteData.Nacionalidad.toUpperCase() :"GUATEMALTECO(A)"
        },
        Nacimiento: function(){                    
            return  0
        }

    },
    created() {
        this.PacienteData.Direccion = 'CIUDAD'
        this.PacienteData.DireccionFactura = 'CIUDAD'
        this.PacienteData.Telefonos = '502'
        this.PacienteData.Nacimiento = 0
        this.PacienteData.FechaPrimeraVisita = this.getDateValue(this.PacienteData.FechaPrimeraVisita)
        this.PacienteData.FechaUltimaVisita = this.getDateValue(this.PacienteData.FechaUltimaVisita)
        this.PacienteData.FechaPrimeraConsulta = this.getDateValue(this.PacienteData.FechaPrimeraConsulta)
        this.PacienteData.UltimaAdmision = this.getDateValue(this.PacienteData.UltimaAdmision)
        this.PacienteData.FechaPrimeraConsulta = this.getDateValue(this.PacienteData.FechaPrimeraConsulta)
        
    },
    activated() {
        

        this.PacienteData.Nacimiento = 0
        this.PacienteData.FechaPrimeraVisita = this.getDateValue(this.PacienteData.FechaPrimeraVisita)
        this.PacienteData.FechaUltimaVisita = this.getDateValue(this.PacienteData.FechaUltimaVisita)
        this.PacienteData.FechaPrimeraConsulta = this.getDateValue(this.PacienteData.FechaPrimeraConsulta)
        this.PacienteData.UltimaAdmision = this.getDateValue(this.PacienteData.UltimaAdmision)
        this.PacienteData.FechaPrimeraConsulta = this.getDateValue(this.PacienteData.FechaPrimeraConsulta)

    },
    methods: {
        getionarDatos() {
            return new Promise((resolve) => {
                // Lógica de guardado del cliente aquí
                // Resuelve la promesa si el guardado es exitoso
                return resolve(true);
                // Rechaza la promesa si hay un error en el guardado
                // reject('Mensaje de error aquí');
            });
        },
        validacionFormulario() {
            return new Promise((resolve, reject) => {
                this.$refs.personalDataRN.validate()
                    .then((isValid) => {
                        if (isValid) {
                            
                            resolve(true);
                        } else {
                            
                            reject('Revisar campos requeridos');
                        }
                    })
                    .catch((error) => {
                        
                        reject(error);
                    });
            });
        },
        gestionarPaciente() {
            return new Promise((resolve, reject) => {
                this.$store.dispatch('paciente/crearPaciente')
                    .then((response) => {                        
                        resolve(response);
                    })
                    .catch((error) => {
                        reject(error);
                    });
            });
        },
        async submit() {

             this.PacienteData.Sexo = this.SelSexo.Codigo
             this.PacienteData.EstadoCivil = this.SelEstadoCivil.Codigo
             this.PacienteData.NombreFactura =  this.NombrePaciente   + ' ' + 
                                                this.PacienteData.SegundoNombre.replace(/\s+/g, ' ').trim() + ' ' +
                                                this.PacienteData.PrimerApellido.replace(/\s+/g, ' ').trim() + ' ' + 
                                                this.PacienteData.SegundoApellido.replace(/\s+/g, ' ').trim()
            this.PacienteData.DireccionFactura = "CIUDAD"                                   


            this.getionarDatos()
                .then(() => {
                    return this.validacionFormulario();
                })
                .then(() => {                                        
                    return this.gestionarPaciente();
                })
                .then(() => {                    
                    this.$emit("ready", true);
                })
                .catch((error) => {
                    this.$vs.dialog({
                        type: 'confirm',
                        color: '#ED8C72',
                        acceptText: 'Continuar',
                        title: 'Error en gestión de Paciente',
                        text: error,
                        clientWidth: 100,
                        accept: () => {
                            this.$emit("ready", false);                            
                        }
                    });
                })
        },
        getDateValue(value) {
            if (value === undefined) {
                return '';
            } else if (value === null) {
                return '';
            } else if (value == '') {
                return '';
            }
            return moment(value, 'DD/MM/YYYY HH:mm:ss').format('YYYY-MM-DD');          
        },
        nextStep() {
            if (this.isLastStep) {
                return this.submit();
            }

            this.activeStep++;
        }
    }
};


</script>
  
  
<style scoped>
* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}


.container_div {
    display: grid;
    grid-gap: 5px;
    width: 100%;
    height: 100%;
    overflow: hidden;
}



.container_div>div {
    border: 1px solid #888;
    padding-top: 5px;
    padding-bottom: 10px;
}


.form {
    margin-top: 20px;
}

span {
    display: block;
    margin-top: 3px;
}


 .vx-card .vx-card__collapsible-content .vx-card__body {
    padding: 1px ;
}

.container {
    max-width: 100%;
}

span.error-msg{
   color: red;
   margin-top: 5px;
}

</style>
  