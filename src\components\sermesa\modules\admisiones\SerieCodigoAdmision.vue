<!-- SerieCodigoAdmision.vue -->
<template>
    <div id="app">


        <div class="serie-codigo-admision" v-show="locationData === 'upper'">

            <div class="col-lg-12">

                <vs-row>
                    <div class="texto">
                        <strong>{{ this.NombreProceso }}</strong>
                    </div>
                </vs-row>
            </div>
        </div>


        <div class="serie-codigo-admision" v-show="showOptions">

            <div v-show="locationData === 'header'">
                <div class="col-lg-12">

                    <vs-row>
                        <div class="texto">
                            <strong>{{ this.NombreProceso }}</strong>
                        </div>
                    </vs-row>
                </div>

            </div>

            <div>
                <div class="col-lg-12">
                    <BaseRadioButtonGroup class="radio-button-group"
                        :options="radioOptions" v-model="serieSeleccionada" :groupName="'serieCodigo'" />

                </div>
            </div>

        </div>
    </div>
</template>
  
<script>

import BaseRadioButtonGroup from "@/components/sermesa/global/radioButtons/BaseRadioButtonGroup";
import { mapFields } from "vuex-map-fields";

export default {
    name: "SerieCodigoAdmision",
    props: {
        showOptions: {
            type: Boolean,
            default: true
        },
        locationData: {
            type: String,
            default: "up"
        },
        tipoAdmision: {
            type: Number,
            default: 0
        },
    },
    components: {
        BaseRadioButtonGroup
    },
    data() {
        return {
            radioOptions: [],
            serieSeleccionada: '',//this.selectedSerie
            NombreProceso: '',
            componentKey:0

        };
    },
    computed: {
        ...mapFields('admision', ["AdmisionData"]),
        selectedOption() {
            if (this.radioOptions.length > 0) {
                const option = this.radioOptions.find(
                    (option) => option.TipoAdmision === '' + this.AdmisionData.TipoAdmision
                );
                // Si encuentra una opción, devuelve la opción; de lo contrario, devuelve null
                return option ? option : null;
            }

            return null;
        }

    },
    watch: {
        serieSeleccionada(newValue) {
            this.AdmisionData.TipoAdmision =  newValue.TipoAdmision           
            this.$emit("selected-option-changed", newValue)
            this.NombreProceso = newValue.NombreProceso
            this.$emit('input', newValue.TipoAdmision);
        },
        selectedOption: {
            immediate: true,
            handler(newValue) {
                if (newValue) {
                    this.serieSeleccionada = newValue;
                }
            }
        },
    },
    mounted() {
        this.ConsultarSerieAdmision()
    },
    methods: {
        async ConsultarSerieAdmision() {
            const resp = await this.axios.post('/app/v1_admision/BusquedaSerieAdmision', {
                Empresa: '',
                Codigo: '',
                TipoAdmision: this.AdmisionData.TipoAdmision

            })
            this.radioOptions = resp.data.json
        },
        
    }
};
</script>


<style scoped>
* {
    box-sizing: border-box;
    padding: 1px;
    margin: 0;
}

/* 
span {
    font-size: 25px;
} */

.serie-codigo-admision {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    padding-left: 1rem;
    padding-right: 1rem;
    border: 1px solid #888;
    border-radius: 5px;
    margin: 5px;
}

.nombre-proceso {
    border: 1px solid #888;
    border-radius: 5px;
    margin: 5px;
    justify-content: center;
    align-items: center;
    display: table;
    text-align: center;
}

.texto {
    /* text-align: center;
    vertical-align: middle;
    justify-self: center;
    align-self: center;
    top: 50%;
    left: 50%; */
    width: 100%;
    height: 100%;
    text-align: center;
    display: table-cell;
    /* This says treat this element like a table cell */
    vertical-align: middle;
    font-size: 20px;
}

.radio-button-group {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

/* .numero-proceso {
    grid-area: numero-proceso;
} */
</style>
