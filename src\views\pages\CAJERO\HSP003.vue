<template>
<vx-card :title="`Facturación Adicional Sasi ${sesion.sesion_sucursal_nombre}`">    
    <vs-row>
            <ConfiguracionCaja  ref="ConfiguracionCajaRef"
                                @CargarCaja="CargarCaja" :FiltrarAgrupacionCaja=FiltrarAgrupacionCaja
                                TipoCajaSeleccionada="TipoCajaFacturacion" CajaSeleccionada="CajaFacturacion" 
                                class="w-full">
            </ConfiguracionCaja>         
            <vs-row class="w-full">
                <vs-col class="sm:w-full md:w-full lg:w-1/2 xl:w-1/2">                     
                    <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 pt-1 pr-1 ">   
                        <label class="typo__label label-shrink" style="white-space: nowrap;"> Tipo Documento  </label>  
                        <multiselect size="small"
                                     v-model="documento.tipoDocumento" :custom-label="tipo_documento_seleccionado" 
                                     :options="documento.tiposDocumentoLista"  :allow-empty="false" :showLabels="false"
                                     placeholder="Tipo Doc"                                      
                                     label="Tipo Documento" >
                        </multiselect>      
                    </vs-col>           
                    <vs-col class="sm:w-full md:w-full lg:w-8/12 xl:w-4/12 p-1">
                        <vs-input label="No. documento" 
                                v-model="documento.noDocumento" 
                                class="w-full"
                                @keydown.enter="ValidacionDocumentoTributario()" @keydown.tab.prevent="ValidacionDocumentoTributario()"
                                />      
                    </vs-col>  
                    <vs-col class="sm:w-full md:w-full lg:w-full xl:w-6/12 p-1">
                        <vs-input id="idNombre"
                                label="Nombre"  
                                class="w-full"
                                v-model="documento.nombre" />      
                    </vs-col>                                     
                </vs-col>                                    
            </vs-row>    
            <vs-row class="w-full">
                <vs-col class="sm:w-full md:w-full lg:w-1/2 xl:w-1/2">   
                    <vs-col class="sm:w-full md:w-full lg:w-full xl:w-2/12 pt-1 pr-1 ">                           
                    </vs-col>
                    <vs-col class="sm:w-full md:w-full lg:w-full xl:w-4/12 p-1">
                        <vs-input label="Dirección" 
                                v-model="documento.direccion" 
                                class="w-full"                               
                                />      
                    </vs-col>  
                    <vs-col class="sm:w-full md:w-full lg:w-full xl:w-6/12 p-1">
                        <vs-input label="E-Mail"  
                                class="w-full"
                                v-model="documento.correo" />      
                    </vs-col>    
                </vs-col>     
                <vs-col class="sm:w-full md:w-full lg:w-1/2 xl:w-1/2" style="text-align: center;">
                        <label class="typo__label label-total centered-element pl-3">
                            <b>Total:</b>
                        </label>
                        <label class="typo__label label-total centered-element">
                            {{ $formato_moneda(montoTotalFacturacion) }}
                        </label>
                </vs-col>              
            </vs-row>
            <vs-row class="w-full">                                        
                <vs-divider>Detalle Cargos</vs-divider>  
                <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-3/12 pr-2">
                    <vs-col style="background-color:#eee;border:1px solid #ccc;border-radius: 5px; height: 300px;">
                        <div class="w-full" style="text-align: center;">
                            <h5 class="p-1">Agregar Cargo</h5>
                        </div>
                        <div class="p-4 w-full flex flex-col" style="align-items: center;" >
                            <label class="typo__label" style="text-align: left;"> Categoria  </label> 
                            <multiselect    style="width: 250px;"
                                            v-model="categoriaSeleccionada"  :custom-label="(categoria)=>categoria.Codigo+' '+categoria.Nombre"
                                            :options="categoriasLista"  :allow-empty="false" :showLabels="false"
                                            label="Categoria"
                                            id="botonCategoria"
                                            placeholder="Categoria" >
                            </multiselect>     
                        </div>
                        <div class="p-4 w-full flex flex-col" style="align-items: center;" >
                            <label class="typo__label"> Valor  </label> 
                            <DxNumberBox v-model=nuevoCargo.VALOR format="Q #,##0.00"  style="width: 250px;"/>
                        </div>
    
                        <div class="p-4 w-full flex"  style="align-items: center;" >
                            <vs-button  class="m-2" color="primary" :type="agregarCargo?'border':'filled'" style="width:120px; padding: 10px 0px 10px 0px; margin: 5px 0px 5px 0px;"
                                @click="InsertarCargo()"
                                @keyup.tab="agregarCargo=true"
                                v-on:blur="agregarCargo=false"
                                >
                                Agregar
                            </vs-button>
                            <vs-button class="m-2" color="danger" style="width:100px; padding: 10px 0px 10px 0px; margin: 5px 0px 5px 0px;" 
                                @click="Helpers().LimpiarNuevoCargo()">
                                Cancelar
                            </vs-button>
                        </div>
                    </vs-col>  
                    <vs-row class="w-full pt-10 text-center">
                        <vs-button color="success"  style="width:150px" class="m-2" id="botonFactura" :type="facturaSeleccionado?'border':'filled'"
                                   @click="CargarFactura()" 
                                   @keyup.tab="facturaSeleccionado=true"
                                   v-on:blur="facturaSeleccionado=false"
                                   :disabled="!configuracion.activarFacturacion">
                            Facturar
                        </vs-button>
                        <vs-button color="warning" style="width:150px" class="m-2" @click="Helpers().LimpiarPantalla()">
                            Limpiar
                        </vs-button>
                    </vs-row>
                </vs-col>
                <vs-col class="sm:w-full md:w-full lg:w-9/12 xl:w-9/12 pl-4" style="text-align:center">
                    <vs-table max-items="8" pagination :data="cargos" noDataText="Sin datos disponibles">
                        <template slot="thead">
                            <vs-th>Categoria</vs-th>
                            <vs-th >Descripción</vs-th>
                            <vs-th>Valor</vs-th>
                            <vs-th>Acción</vs-th>
                        </template>
                        <template slot-scope="{data}">
                            <vs-tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td :data="indextr+'categoria'" width='10%'>{{ tr.ID }}</vs-td>
                                <vs-td :data="indextr+'descripcion'" width='50%' style="text-align: center;">{{ tr.DESCRIPCION }}</vs-td>
                                <vs-td :data="indextr+'valor'" width='35%'>       
                                    <DxNumberBox 
                                    @focus-out="()=>{montoTotalFacturacion = cargos.reduce((acumulador, cargo) => acumulador + cargo.VALOR, 0)}"
                                    v-model=tr.VALOR format="Q #,##0.00" />
                                </vs-td>                                
                                <vs-td width="5%">
                                    <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="EliminarCargo(data[indextr])">                                        
                                    </vs-button>                             
                                </vs-td>                                                
                            </vs-tr>
                        </template>
                    </vs-table>
                    
                </vs-col>
      
            </vs-row>                                      
            <!-- CARGOS AGREGADOS -->        
    </vs-row>
</vx-card>
</template>
    
<script>
    import moment from "moment";    
    import "vue-multiselect/dist/vue-multiselect.min.css";     
    import { ref } from 'vue'
    import ConfiguracionCaja from "/src/components/sermesa/modules/caja/ConfiguracionCaja.vue";         
    export default {
        components:{
            ConfiguracionCaja         
        }, 
        data() {    
            return {     
                facturaSeleccionado:false,
                agregarCargo:false,
                FiltrarAgrupacionCaja:['5'],    
                montoTotalFacturacion: 0,                            
                configuracion:{
                    cajaSeleccionada: null, 
                    activarFacturacion: false,
                    activarRecibos: false                      
                },   
                caja: { serieFactura:[],
                        serieFacturaFiltrada:[],
                }, 
                categoriasLista:[],                                       
                documento:{ tiposDocumentoLista:[],
                            tipoDocumento:null,
                            noDocumento:null,
                            nombre:null,
                            correo:null,
                            direccion:null			
                    },	   
                categoriaSeleccionada:{
                    Codigo:'',
                    Nombre:''
                },         
                nuevoCargo : {
                    VALOR: null,
                    ID: null,
                    DESCRIPCION: null
                },
                cargos:[],	 
                quitar_espacio_buscar : { padding: '0 !important;' },
                limpiarAdmision: false,                        
                money: {
                    decimal: '.',
                    thousands: ',',
                    prefix: 'Q',
                    precision: 2,
                    masked: false
                }            
            }
        },
        watch:{
            cargos(){
                this.montoTotalFacturacion = this.cargos.reduce((acumulador, cargo) => acumulador + cargo.VALOR, 0)             
            }
        },
        computed:{         
            sesion() {
                return this.$store.state.sesion
            }
        },       
        methods: {   
            CargarCaja(cajaSeleccionada){                
                this.configuracion.cajaSeleccionada   = cajaSeleccionada.Cajero
                this.configuracion.activarFacturacion = cajaSeleccionada.Factura
                this.configuracion.activarRecibos     = cajaSeleccionada.Recibo
            },
            async GenerarFactura(Serie,NumeroFactura) {
                let reporte = "Impresion FEL"

                let postData = {
                    SerieFactura: Serie ,
                    NumFactura: NumeroFactura,
                    nombrereporte: this.configuracion.cajaSeleccionada.FormatoImpresion
                }


                this.$reporte_modal({
                    Nombre: reporte,
                    Opciones: {
                        ...postData
                    }
                })                

            },     
            Fel(serieFactura,numeroFactura){
                this.axios.post('/app/v1_FacturaElectronica/GeneraFel',
                                {serieFactura: serieFactura,
                                    numeroFactura: numeroFactura
                                })                                  
                            .then(resp=>{
                                    if(resp.data.codigo == 0 && resp.data.json[0].codigo == 0){
                                        this.Consulta().MostrarSatisfactorio(resp.data.json[0].descripcion);
                                        this.GenerarFactura(serieFactura,numeroFactura);                                                                               
                                    }else{
                                        this.Consulta().MostrarError(resp.data.json[0].descripcion);
                                    }
                                })                                 
            },
            async CargarFactura(){     
                if(!this.documento.tipoDocumento || !this.documento.tipoDocumento.Valor){
                    this.Consulta().MostrarError('Seleccionar el tipo de documento')
                    return
                }
                if(!this.documento.noDocumento || this.documento.noDocumento.length<=0){
                    this.Consulta().MostrarError('Ingresar un no. de documento')
                    return
                }
                if(this.cargos.length <= 0){
                    this.Consulta().MostrarError('Ingresar un cargo para la facturación')
                    return
                }

                const botonFactura = document.getElementById("botonFactura");
                if(botonFactura) botonFactura.blur();

                this.axios.post('/app/v1_caja/GeneraFacturaSasi', {opcion:'I',
                                                                   subOpcion:1,
                                                                   serieFactura:this.configuracion.cajaSeleccionada.SerieFac,
                                                                   nombreFactura:this.documento.nombre,
                                                                   documento:this.documento.noDocumento,
                                                                   direccion:this.documento.direccion,
                                                                   correo:this.documento.correo,
                                                                   tipoReceptor:this.documento.tipoDocumento.Valor,
                                                                   fechaFactura:moment(await this.$dbdate()).format('YYYY-MM-DD'),
                                                                   cargos:this.cargos.map((cargo)=>({codigo:cargo.ID,
                                                                                                    descripcion:cargo.DESCRIPCION,
                                                                                                    valor:cargo.VALOR})
                                                                                          )
                                                                   }
                                )
                          .then(resp=>{
                            if(resp.data.codigo==0 || resp.data.codigo==200){
                                this.Fel(resp.data.Serie,resp.data.Factura)
                                this.$vs.dialog({
                                                type: 'confirm',
                                                color: 'success',
                                                title: 'Factura Generada',
                                                acceptText: 'Aceptar',
                                                cancelText: 'Cancelar',
                                                text: `Factura Generada ${resp.data.Serie} ${resp.data.Factura}`,                                               
                                                accept: () => {
                                                    this.Helpers().LimpiarPantalla()
                                                },
                                                cancel: () => {
                                                    this.Helpers().LimpiarPantalla()
                                                }
                                            })
                            }
                          })
            },        
            InsertarCargo(){          
                if(!this.categoriaSeleccionada || this.categoriaSeleccionada == '' || this.categoriaSeleccionada.Codigo.trim().length <= 0){
                    this.Consulta().MostrarError('Seleccionar una categoria valida');
                    return
                }else{
                    this.nuevoCargo.ID = this.categoriaSeleccionada.Codigo
                    this.nuevoCargo.DESCRIPCION = this.categoriaSeleccionada.Nombre
                }
                
                if(!this.nuevoCargo.VALOR || !(this.Helpers().ConvertirFloat(this.nuevoCargo.VALOR) > 0)){
                    this.Consulta().MostrarError('Debe ingresar un valor mayor mayor a cero',4000);
                    return
                }

                const indiceEncontrado = this.cargos.findIndex( cargo => cargo.ID == this.nuevoCargo.ID )                
                if(indiceEncontrado >= 0){                    
                    this.Helpers().MostrarMensaje('¿Desea actualizar el valor de la categoria '+this.nuevoCargo.DESCRIPCION+' ?',
                                                  ()=>{this.cargos.splice(indiceEncontrado,1,{...this.nuevoCargo}); this.Helpers().LimpiarNuevoCargo();})
                    
                    return
                }

                this.cargos.push({...this.nuevoCargo});
                const botonCategoria = document.getElementById("botonCategoria");
                if(botonCategoria) botonCategoria.focus();
                
                this.Helpers().LimpiarNuevoCargo();
            },   
            EliminarCargo(cargo){
                this.Helpers().MostrarMensaje('¿Eliminar el cargo de la categoria '+ cargo.DESCRIPCION+' ?',
                                              ()=>{
                                                let indiceCargo = 
                                                    this.cargos.findIndex(cargoLista=>cargo.ID==cargoLista.ID)
                                                this.cargos.splice(indiceCargo,1)
                                              })               
            },            
            Helpers(){
                return {
                    CargarCategorias: ()=>{
                        this.axios.post('/app/v1_caja/ObtenerCategoriasCargos',{opcion:'C',
                                                                                    subOpcion:1})
                          .then(resp=>{this.categoriasLista = resp.data.json})
                    },
                    ConvertirFloat: (Valor,Decimales=2)=>{
                        let conversion = parseFloat(Valor?Valor:0.00)
                        if(isNaN(conversion)){
                            return 0.00
                        }
                        return conversion.toFixed(Decimales)+0.00
                    },
                    MostrarMensaje: (Mensaje,FuncionAceptar)=>{
                        this.$vs.dialog({
                            type: 'confirm',
                            color: 'danger',
                            title: 'Confirmación',                    
                            acceptText: 'Aceptar',
                            cancelText: 'Cancelar',
                            text: Mensaje,
                            accept: () => {
                                FuncionAceptar()                     
                            }
                        })
                    },
                    LimpiarNuevoCargo: ()=>{
                        this.nuevoCargo.ID = null;
                        this.nuevoCargo.DESCRIPCION = null;
                        this.nuevoCargo.VALOR = null;
                        this.categoriaSeleccionada = '';                        
                    },
                    LimpiarPantalla: ()=>{
                        this.Helpers().LimpiarNuevoCargo()
                        this.documento.tipoDocumento = null;
                        this.documento.noDocumento = null;
                        this.documento.nombre = null;
                        this.documento.correo = null;
                        this.documento.direccion = null;
                        this.cargos = [];
                        this.montoTotalFacturacion = 0;
                    }
                }
            },            
            tipo_documento_seleccionado(tipoDocumento){
                return `${tipoDocumento.Descripcion}`
            },                        
            ValidacionDocumentoTributario(){
                if(this.Consulta().EsVacia(this.documento.tipoDocumento)){
                    this.Consulta().MostrarError('Seleccione un tipo de documento');
                    return;
                }
                if(this.Consulta().EsVacia(this.documento.noDocumento)){
                    this.Consulta().MostrarError('Ingrese el numero de documento');
                    return;
                }
                this.axios.post('/app/v1_caja/ValidacionDocumentoTributario',{
                                    tipoReceptor: parseInt(this.documento.tipoDocumento.Valor),
                                    documento: this.documento.noDocumento
                                })
                          .then(resp=>{
                                    if ( resp.data.codigo == 0 && resp.data.json.length > 0 && resp.data.json[0].codigo == 0){
                                        this.documento.nombre = resp.data.json[0].NOMBRE
                                        this.documento.correo = resp.data.json[0].EMAIL
                                        this.documento.direccion = resp.data.json[0].DIRECCION
                                        const campoNombre = document.getElementById("idNombre");
                                        if(campoNombre) campoNombre.focus();
                                    }else if ( resp.data.codigo == 0 && resp.data.json.length > 0 && resp.data.json[0].codigo != 0){
                                        this.Consulta().MostrarError(resp.data.json[0].descripcion);
                                        this.documento.nombre = null
                                        this.documento.correo = null
                                        this.documento.direccion = null
                                    }
                                    else if(resp.data.json.length == 0){
                                        this.documento.nombre = null
                                        this.documento.correo = null
                                        this.documento.direccion = null
                                        this.Consulta().MostrarError('No se encontraron datos para el documento ingresado');
                                    }
                                })
                            
            },   
            CargarTipoDocumento(){
                this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision', {
                                    opcion:'C',
                                    subOpcion:2,
                                    agrupacion:'TipoReceptor'
                                })
                          .then(resp => {                            
                                    if (resp.data && resp.data.json.length > 0){
                                        this.documento.tiposDocumentoLista = resp.data.json;
                                        this.documento.tipoDocumento = ref(this.documento.tiposDocumentoLista.find(doc=>doc.Descripcion == 'NIT'))   
                                    }
                                }
                               )
            },       
            actualizarCajero(){  
                this.$refs.ConfiguracionCajaRef.CargarCajaInicial();
            },              
            Consulta() {
                return {                  
                    init: async () => {                        
                        this.CargarTipoDocumento();                                             
                        this.Helpers().CargarCategorias();
                    },
                    EsVacia:(variable)=>{
                        return variable?false:true;
                    },
                    MostrarError:(mensaje,tiempo=2000)=>{
                        this.$vs.notify({
                            time:tiempo,
                            title: 'Cajero',
                            color: 'danger',
                            text:mensaje,
                            position: 'top-center'
                        })
                    },
                    MostrarSatisfactorio:(mensaje)=>{
                        this.$vs.notify({
                            time:2000,
                            title: 'Cajero',
                            color: 'success',
                            text:mensaje,
                            position: 'top-center'
                        })
                    }
                }
            }
        },
        mounted() {
            this.Consulta().init()                     
        },
        activated() {
            window.addEventListener('focus', this.actualizarCajero);   
        },
        deactivated() {
            window.removeEventListener('focus',this.actualizarCajero);
        },

    }
    </script>
    
    <style scoped>

    .radio-button-group {
        display: flex;
        justify-content: left;
        align-items: center;
        height: 100%;
    }

    .cantidad button {
        height: 25px;
        width: 30px;
        border: 1px solid #ccc;
        border-radius: 5px;
    
    }
    
    .cantidad div {
        display: inline-block;
        height: 25px;
        width: 30px;
        text-align: center;
    }

    .quitar_espacio {
        padding: 0;
        margin: 0;        
    }

    .quitar_espacio_buscar > .label{
        padding: 0 !important;
    }

    .money-input{
        white-space: nowrap;
        color: #008FBE;
        border-radius: 5px;
        height: 36px;
    }

    .money-input:focus{
        border:1px solid #008FBE!important;
    }

    .panel-cajero-elegir{
        background-color: rgba(var(--vs-danger), 1) !important; 
    }

    .panel-cajero {
        display:inline-block;
        border-radius: 25px;
        background-color: rgba(var(--vs-primary), 1);
        color: white;
        padding: 5px 65px 5px 10px;
        position: relative;
        width: auto;
        /* top: 12px; */
        overflow: hidden;
        /* margin-right: -14px; */
        font-size: 13px;
        /* right: 50px; */
        /* border-radius: 30px 0 0 0; */
        /* box-shadow: inset 0 2px 0px 1px rgba(0, 0, 0, 0.2); */
        cursor: pointer;
    }

    .seleccionTipoCaja .vs-popup {
        width: 250px !important;
    }
    .label-total
    {
        font-size: 25px;
        color:black;
        text-align: center;
    }

    .centered-element {
        margin: 0;
        top: 50%;
        transform: translateY(-50%);
    }
    .label-shrink{
        display: block;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        width: 100%;
    }
    </style>    