<template>
<div v-if="reglaFiltro.info">
    <!-- <div class="estado" :class="[(existente==1?'Editar':'Nuevo')]">
        {{existente==1?'Editar Regla':'Nueva Regla'}}
    </div> -->

    <vs-row>
        <vs-col vs-w="6" style="padding-right:20px">
            <div style="box-shadow:0 2px 5px rgba(0,0,0,0.5);padding:10px;border-radius:5px">
                <h5>Información de Filtro</h5>
                <br>
                <table width="100%" class="info">
                    <tr v-if="reglaFiltro.info.TipoMedico">
                        <td width="150px">
                            <b style="color:rgba(var(--vs-primary),1);">Tipo de Médico: </b>
                            <br>
                            {{tipoMedico.filter(f=>f.Codigo==reglaFiltro.info.TipoMedico.trim())[0].Nombre}}
                        </td>
                    </tr>
                    <tr v-if="reglaFiltro.info.ProntoPago">
                        <td>
                            <b style="color:rgba(var(--vs-primary),1);">Pronto Pago:</b><br>
                            {{reglaFiltro.info.ProntoPago?'Sí':'No'}}
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <b style="color:rgba(var(--vs-primary),1);">Admisión:</b><br>
                            {{tipoAdmision.filter(f=>f.Codigo==reglaFiltro.info.TipoAdmision)[0].Nombre}}
                        </td>
                    </tr>
                    <tr v-if="reglaFiltro.info.CategoriaCargo">
                        <td>
                            <b style="color:rgba(var(--vs-primary),1);">Categoría Cargo:</b><br>
                            {{reglaFiltro.info.CategoriaCargo}}
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <b style="color:rgba(var(--vs-primary),1);">Descripción:</b><br>
                            {{reglaFiltro.info.Descripcion}}
                        </td>
                    </tr>
                    <tr>
                        <td style="max-width:100px">
                            <b style="color:rgba(var(--vs-primary),1);">Seguros:</b><br>
                            <div style="overflow:auto;padding-left:20px;width:100%">
                                <vs-table2 max-items="5" pagination search :data="Aseguradoras" tooltip>
                                    <template slot="thead">
                        <th order="">Seguros</th>
                        </template>
                        <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(item, indextr) in data">
                        <vs-td2>
                            <b>({{item.Codigo}}) {{item.Nombre}}</b><br>
                            <small>{{item.NombreAseguradora}}</small>
                        </vs-td2>
                    </tr>
                    </template>
                    </vs-table2>
            </div>
            </td>
            </tr>
            </table>
</div>
</vs-col>
<vs-col vs-w="6" v-if="reglaFiltro.info">
    <h5>Reglas de Pago</h5>
    <vs-divider></vs-divider>
    <table width="100%" class="reglas">
        <tr>
            <td>

            </td>
            <td width="70px">
                Activo
            </td>
        </tr>
        <tr>
            <td>Cuenta Cerrada (Fecha Egreso)</td>
            <td>
                <vs-switch v-model="info.Cuenta_Cerrada" style="height: 23px;" />
            </td>

        </tr>
        <tr>
            <td>Candado Seguro</td>
            <td>
                <vs-switch v-model="info.Candado_Seguro" style="height: 23px;" />
            </td>
        </tr>
    </table>
    <br>

    <!-- {{reglas}} -->
    <div v-for="(item,index) in  info.Reglas" :key="index" @click="reglas_index = index">
        <div style="text-align:center;padding:2px" v-if="index>0">
            ó
        </div>
        <div class="regla activo">
            <div class="eliminar" v-if="index < info.Reglas.length-1 || item.Id" @click="info.Reglas.splice(index,1)">
                <i class="far fa-trash-alt"></i>
            </div>
            <table width="100%" class="reglas">
                <tr>
                    <td>
                    </td>
                    <td width="70px">
                        Emitido
                    </td>
                    <td width="70px">
                        Saldo 0
                    </td>
                </tr>
                <tr v-if="reglaFiltro.info.TipoAdmision==1">
                    <td>Factura #0 (Privada)</td>
                    <td>
                        <vs-switch v-model="item.Factura_0_Emitido" @click="(item.Factura_0_Cancelado)?item.Factura_0_Cancelado=false:null" style="height: 23px;" />
                    </td>
                    <td>
                        <vs-switch v-model="item.Factura_0_Cancelado" @click="(!item.Factura_0_Cancelado)?item.Factura_0_Emitido=true:null" style="height: 23px;" />
                    </td>
                </tr>
                <tr v-if="reglaFiltro.info.TipoAdmision==2">
                    <td>Factura #1 (Extraord.)</td>
                    <td>
                        <vs-switch v-model="item.Factura_1_Emitido" @click="(item.Factura_1_Cancelado)?item.Factura_1_Cancelado=false:null" style="height: 23px;" />
                    </td>
                    <td>
                        <vs-switch v-model="item.Factura_1_Cancelado" @click="(!item.Factura_1_Cancelado)?item.Factura_1_Emitido=true:null" style="height: 23px;" />
                    </td>
                </tr>
                <tr v-if="reglaFiltro.info.TipoAdmision==2">
                    <td>Factura #2 (Copago / Coaseguro)</td>
                    <td>
                        <vs-switch v-model="item.Factura_2_Emitido" @click="(item.Factura_2_Cancelado)?item.Factura_2_Cancelado=false:null" style="height: 23px;" />
                    </td>
                    <td>
                        <vs-switch v-model="item.Factura_2_Cancelado" @click="(!item.Factura_2_Cancelado)?item.Factura_2_Emitido=true:null" style="height: 23px;" />
                    </td>
                </tr>
                <tr v-if="reglaFiltro.info.TipoAdmision==2">
                    <td>Factura #3 (Seguro)</td>
                    <td>
                        <vs-switch v-model="item.Factura_3_Emitido" @click="(item.Factura_3_Cancelado)?item.Factura_3_Cancelado=false:null" style="height: 23px;" />
                    </td>
                    <td>
                        <vs-switch v-model="item.Factura_3_Cancelado" @click="(!item.Factura_3_Cancelado)?item.Factura_3_Emitido=true:null" style="height: 23px;" />
                    </td>
                </tr>
            </table>

            <div v-if="reglaFiltro.info.CodigoCategoriaCargo == 98 || reglaFiltro.info.CodigoCategoriaCargo == 99">
                <table width="100%" class="reglas">
                    <tr>
                        <td>Recibo Cuenta Ajena</td>
                        <td width="70px">
                            <vs-switch v-model="item.Factura_CA_Emitido" style="height: 23px;" />
                        </td>
                        <td width="70px">
                            <!-- <vs-switch :disabled="index!=info.Reglas.length-1" v-model="item.Factura_3_Cancelado" @click="(!item.Factura_3_Cancelado)?item.Factura_3_Emitido=true:null" style="height: 23px;" /> -->
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        <vs-button class="w-full" style="small" v-if="index==info.Reglas.length-1" :disabled="!Otros().verificar_regla(item)" @click="info.Reglas.push({...$excluir_objeto(info,['Reglas'])})">Agregar regla opcional </vs-button>
    </div>
    <br>
    <ValidationObserver ref="formValidate" v-slot="{ invalid,handleSubmit }" mode="lazy">
        <b>Descripción de la regla</b>
        <ValidationProvider name="Descripción" rules="required|max:100" v-slot="{ errors }">
            <vs-textarea class="w-full" count="100" v-model="info.Descripcion" />
            <span style="position:relative;top:-18px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
        </ValidationProvider>
        <b>Estado</b> (Activo / Inactivo)
        <vs-switch v-model="info.Estado" style="height: 23px;" />
        <vs-divider></vs-divider>
        <div style="float:left" v-if="info.Fecha_Creado">
            <b>Creado: </b>{{info.Fecha_Creado}}
        </div>
        <vs-button style="float:right" @click="handleSubmit(Guardar().regla(item))" :disabled="invalid">{{info.Id ? 'Editar Regla':'Guardar Regla'}}</vs-button>
    </ValidationObserver>
</vs-col>
</vs-row>

</div>
</template>

<script>
// import vSelect from 'vue-select'
import bitacora from '@/components/sermesa/funciones/SMBitacora'
export default {
    data() {
        return {
            existente: 0,

            tipoMedico: [{
                Codigo: 'C',
                Nombre: 'Cortesía'
            }, {
                Codigo: 'M',
                Nombre: 'Casa'
            }, {
                Codigo: 'CC',
                Nombre: 'Casa-Cortesía'
            }],

            tipoAdmision: [{
                Codigo: '1',
                Nombre: 'Privada'
            }, {
                Codigo: '2',
                Nombre: 'Seguro'
            }],

            planillas: [
                {
                    Codigo: 'H',
                    Nombre: 'Planilla Honorarios'
                }, {
                    Codigo: 'C',
                    Nombre: 'Planilla Cuenta Ajena'
                }],

            // listado de reglas (multiples reglas por filtro)
            reglas: [],
            reglas_index: 0,

            info: {
                Factura_0_Emitido: false,
                Factura_0_Cancelado: false,
                Factura_1_Emitido: false,
                Factura_1_Cancelado: false,
                Factura_2_Emitido: false,
                Factura_2_Cancelado: false,
                Factura_3_Emitido: false,
                Factura_3_Cancelado: false,
                Factura_CA_Emitido: false, //Recibo de cuenta ajena emitido
                Cuenta_Cerrada: false,
                Candado_Seguro: false,
                Tipo_Planilla: null,
                Fecha_Creado: null,
                Estado: true,
                Descripcion: '',
                Reglas: []
            },

            polizasSeleccionadas: []

        }
    },
    props: {
        reglaFiltro: {
            default: []
        },
        polizas: {
            default: []
        },
        callback: {
            default: null
        },
    },
    computed: {
        Aseguradoras() {
            const arr = this.polizas.filter(f => this.polizasSeleccionadas.filter(r => r.CodigoSeguro == f.Codigo).length > 0)
            return arr
        }
    },
    watch: {
        'reglaFiltro.mostrar'(value) {
            if (value) {
                this.Consulta().actualizar()
            }
        }
    },
    components: {
        // vSelect
    },
    methods: {
        Consulta() {
            return {
                actualizar: () => {
                    this.Otros().limpiar()
                    this.Consulta().polizas(this.reglaFiltro.info.IdFiltro)
                        .then(resp => {
                            if (resp.data.codigo == 0) this.polizasSeleccionadas = resp.data.json
                            // console.log(this.info.Reglas)
                            return this.Consulta().reglas(this.reglaFiltro.info.IdFiltro)
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                const r = resp.data.json
                                this.existente = (r.length > 0) ? 2 : 1
                                this.info.Reglas.push(...r.map(m => {
                                    return {
                                        ...m,
                                        Tipo_Planilla: this.planillas.filter(f => f.Codigo == m.Tipo_Planilla)[0],
                                        Estado: (m.Estado.toUpperCase() == 'A') ? true : false
                                    }
                                }))

                            } else {
                                this.existente = 2
                            }
                            if (this.info.Reglas.length == 0) {
                                this.info.Reglas.push({
                                    ...this.$excluir_objeto(this.info, ['Reglas'])
                                })
                            } else {
                                this.info.Cuenta_Cerrada = this.info.Reglas[0].Cuenta_Cerrada
                                this.info.Candado_Seguro = this.info.Reglas[0].Candado_Seguro
                                this.info.Descripcion = this.info.Reglas[0].Descripcion
                                this.info.Fecha_Creado = this.info.Reglas[0].Fecha_Creado
                                this.info.Estado = this.info.Reglas[0].Estado
                            }
                            this.reglas_index = this.info.Reglas.length - 1

                            bitacora.registrar(this.info, {
                                IdFiltro: this.reglaFiltro.info.IdFiltro
                            })
                        })
                        .catch(() => {
                            this.info.Reglas.push({
                                ...this.info
                            })
                            this.reglas_index = this.info.Reglas.length - 1
                        })
                },

                polizas: (id) => {
                    return this.axios.post('/app/ajenos/FiltrosCargosListadoPolizasSeleccionadas', {
                        IdFiltro: id
                    })
                },
                reglas: (id) => {
                    return this.axios.post('/app/ajenos/FiltrosCargosListadoReglas', {
                        IdFiltro: id
                    })
                },

            }
        },
        Guardar() {
            return {
                regla: () => {
                    if (this.info.Reglas.length == 1 && !this.Otros().verificar_regla(this.info.Reglas[0])) {
                        this.$vs.dialog({
                            color: 'danger',
                            title: 'Reglas',
                            acceptText: 'Aceptar',
                            text: "Debe de seleccionar una o más opciones"
                        })
                        return false
                    }

                    let info = this.info.Reglas.filter(f => this.Otros().verificar_regla(f)).map(info => {
                        // Remplazando datos 
                        info.Cuenta_Cerrada = this.info.Cuenta_Cerrada
                        info.Candado_Seguro = this.info.Candado_Seguro
                        info.Tipo_Planilla = this.info.Tipo_Planilla
                        info.Estado = this.info.Estado
                        info.Descripcion = this.info.Descripcion

                        info.Estado = (info.Estado) ? 'A' : 'I'
                        info.idFiltro = this.reglaFiltro.info.IdFiltro
                        if (info.Tipo_Planilla == "") info.Tipo_Planilla = null
                        return info
                    })

                    this.axios.post('/app/ajenos/FiltrosCargosNuevaRegla', {
                            IdFiltro: this.info.Reglas[0].IdFiltro,
                            list: info
                        })
                        .then(() => {
                            this.Otros().limpiar()
                            this.callback()
                            this.Consulta().actualizar()
                        })
                        .catch(() => {})
                }
            }
        },
        Editar() {
            return {
                poliza: () => {
                    const polizas = this.poliza.listado.filter(f => f.Seleccionar)
                    const info = {
                        IdFiltro: this.editarPoliza,
                        polizas
                    }
                    this.axios.post('/app/ajenos/FiltrosCargosEditarPolizas', info)
                        .then(() => {
                            this.poliza.mostrar = false
                            // setTimeout(() => {
                            this.Actualizar().seleccion(false)
                            this.callback()
                            // }, 100)
                        })
                }
            }
        },
        Actualizar() {
            return {
                seleccion: (todos) => {
                    this.poliza.listado.map(m => m.Seleccionar = todos)
                }
            }
        },
        Eliminar() {
            return {}
        },
        Otros() {
            return {
                limpiar: () => {
                    this.info.Reglas = []
                    // Object.entries(this.info).forEach(element => {
                    //     const elemento = element[0]
                    //     this.info[elemento] = false
                    //     if (elemento == 'descripcion' || elemento == 'Tipo_Planilla') this.info[elemento] = null
                    //     if (elemento == 'estado') this.info.estado = true
                    // })
                },
                verificar_regla: (item) => {
                    let existe = false
                    Object.entries(item).forEach(([i, value]) => {
                        if (i.substring(0, 8) == 'Factura_' && value == true) {
                            existe = true
                        }
                    })
                    return existe
                }
            }
        }
    },
    mounted() {

    }
}
</script>

<style scoped>
.reglas {
    border-spacing: 0;
}

.reglas td {
    padding: 5px;
}

.reglas tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.1);
}

.info {
    border-spacing: 0;
}

.info td {
    padding: 5px;
}

.estado {
    color: white;
    text-align: center;
    margin-bottom: 15px;
    padding: 5px;
    border-radius: 0 0 6px 6px;
    margin-top: -15px;
}

.estado.Nuevo {
    background-color: rgba(var(--vs-success), 1);
}

.estado.Editar {
    background-color: rgba(var(--vs-primary), 1);
}

.regla {
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
    padding: 5px;
    border-radius: 5px;
    margin-bottom: 5px;
    height: 45px;
    overflow: hidden;
    cursor: pointer;
    transition: all ease 0.2s;
    position: relative;
}

.regla.activo {
    height: auto;
}

.regla .titulo {
    font-weight: bold;
    padding: 10px 0;
    border-bottom: 1px solid #ccc;
    margin-bottom: 5px;
}

.regla .eliminar {
    display: flex;
    position: absolute;
    right: -4px;
    top: -4px;
    height: 24px;
    width: 24px;
    border-radius: 50px;
    background-color: #E74C3C;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    cursor: pointer;
}

.regla .eliminar i {
    font-size: 10px;
}
</style>
