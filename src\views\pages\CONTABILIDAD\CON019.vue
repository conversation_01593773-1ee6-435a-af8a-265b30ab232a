<template>
<vx-card title="Tabla Interpretación">

    <div class="container">
        <div class="a-r25qN-0">

            <vs-row>
                <vs-col>
                    <div class="example ex1 pl-10">

                        <div class="radio-container">
                            <label class="radio blue">
                                <input v-model="optionChecked" type="radio" name="group1" value="ReporteProveedores" />
                                <span>Reporte de Proveedor</span>
                            </label>
                            <label class="radio blue">
                                <input v-model="optionChecked" type="radio" name="group1" value="ReporteComprasporProveedor" />
                                <span>Reporte de compras por Proveedor</span>
                            </label>
                            <label class="radio blue">
                                <input v-model="optionChecked" type="radio" name="group1" value="ReporteMovimientoporProveedor" />
                                <span>Reporte movimientos por Proveedor</span>
                            </label>
                            <label class="radio blue">
                                <input v-model="optionChecked" type="radio" name="group1" value="ReporteSaldosporProveedor" />
                                <span>Reporte de saldos por Proveedor</span>
                            </label>
                            <label class="radio blue">
                                <input v-model="optionChecked" type="radio" name="group1" value="ReportedeImpuestos" />
                                <span>Reporte de Impuestos</span>
                            </label>
                            <label class="radio blue">
                                <input v-model="optionChecked" type="radio" name="group1" value="ReporteLibrodeCompras" />
                                <span>Reporte de Libro de Compras</span>
                            </label>
                            <label class="radio blue">
                                <input v-model="optionChecked" type="radio" name="group1" value="ReporteSaldosPorPeriodo" />
                                <span>Reporte Saldos Por Periodo </span>
                            </label>
                            <label class="radio blue">
                                <input v-model="optionChecked" type="radio" name="group1" value="ReporteAnticiposProveedor" />
                                <span>Reporte Anticipos por Proveedor </span>
                            </label>
                        </div>
                        <p :style="{ color: $options.COLOR }">
                        </p>
                    </div>
                </vs-col>

            </vs-row>

        </div>
        <div class="a-x65zy-1">

            <div v-if="contenido === 1">
                <div class="flex flex-wrap p-2 m-2">
                    <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12 pr-4 ">
                        <h5>Tipo Proveedor</h5>
                        <br>
                        <br>
                        <div class="w-full">                            
                            <multiselect
                              v-model="selProveedor"
                              :options="ListaProveedores"
                              :searchable="true"
                              :close-on-select="true"
                              :show-labels="false"
                              :custom-label="ProveedorSeleccionado"
                              placeholder="Seleccionar Proveedor"
                              label="Parentesco">
                              <span
                              slot="noOptions">Lista
                              no
                              disponible.</span>
                              </multiselect>
                        </div>
                    </div>
                </div>

            </div>

            <div v-else-if="contenido === 2">

                <div class="flex flex-wrap mt-4 p-2 m-2">
                    <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12 pr-4 ">
                        <div class="w-1/6">
                            <vs-input label="Proveedor" class="w-full" v-model="this.CodigoProveedorIni" readonly />
                        </div>

                        <div class="w-5/6">
                            <SM-Buscar
                              class="w-full"
                              label="Del Proveedor"
                              v-model="CodigoProveedorIni"
                              :disabled_busqueda="activarBusquedaProv"
                              api="app/inventario/BusquedaProveedor"
                              api_campo_respuesta_mostrar="Nombre"
                              :api_campos="['Nombre','Codigo','Nit' ]"
                              :api_titulos="['Nombre','Codigo','Nit' ]"
                              api_campo_respuesta="Codigo"
                              :api_campo_respuesta_estricto="false"
                              :api_preload="false"
                              :disabled_texto="true"
                              :api_filtro="{ Nombre: this.NombreIni
                                           , Nit: this.NitIni
                                           ,Codigo: this.CodigoIni }"
                              :mostrar_busqueda="true" />
                        </div>
                    </div>

                    <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12  pr-4">
                        <div class="w-1/6">
                            <vs-input label="Proveedor" class="w-full" v-model="this.CodigoProveedorFin" readonly />
                        </div>

                        <div class="w-5/6">
                            <SM-Buscar
                                class="w-full"
                                label="Al Proveedor"
                                v-model="CodigoProveedorFin"
                                :disabled_busqueda="activarBusquedaProv"
                                api="app/inventario/BusquedaProveedor"
                                api_campo_respuesta_mostrar="Nombre"
                                :api_campos="['Nombre','Codigo','Nit' ]"
                                :api_titulos="['Nombre','Codigo','Nit' ]"
                                api_campo_respuesta="Codigo"
                                :api_campo_respuesta_estricto="false"
                                :api_preload="false"
                                :disabled_texto="true"
                                :api_filtro="{  Nombre: this.NombreFin
                                                , Nit: this.NitFin
                                                ,Codigo: this.CodigoFin}"
                                :mostrar_busqueda="true" 
                            />
                        </div>
                    </div>
                </div>

                <div class="flex flex-wrap mt-4 p-3 m-3">
                    <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12 pr-4 flex items-center justify-start">

                        <div class="w-1/4">
                            <label>Fecha Inicial:</label>
                        </div>
                        <div class="w-3/4">
                            <datepicker v-model="FechaInicial" placeholder="Fecha Inicial" :format="customFormatter"></datepicker>
                        </div>

                    </div>
                    <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12  flex items-center justify-start pr-4">

                        <div class="w-1/4">
                            <label>Fecha Final:</label>
                        </div>
                        <div class="w-3/4">
                            <datepicker v-model="FechaFinal" placeholder="Fecha Final" :format="customFormatter"></datepicker>
                        </div>

                    </div>
                </div>

                <div class="flex flex-wrap p-2 m-2">
                    <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12 mt-4 pr-4 ">
                        <h5>Tipo Proveedor</h5>
                        <br>
                        <br>
                        <div class="w-full">
                            <multiselect
                              v-model="selProveedor"
                              :options="ListaProveedores"
                              :searchable="true"
                              :close-on-select="true"
                              :show-labels="false"
                              :custom-label="ProveedorSeleccionado"
                              placeholder="Proveedor"
                              label="Parentesco">
                              <span
                              slot="noOptions">Lista
                              no
                              disponible.</span>
                              </multiselect>
                        </div>
                    </div>
                </div>

                <!-- frin opcion 2 -->

            </div>
            <div v-else-if="contenido === 3">

                <div class="flex flex-wrap mt-4 p-2 m-2">

                    <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12 pr-4 ">
                        <div class="w-1/6">
                            <vs-input label="Proveedor" class="w-full" v-model="this.CodigoProveedorIni" readonly />
                        </div>

                        <div class="w-5/6">
                            <SM-Buscar
                              class="w-full"
                              label="Del Proveedor"
                              v-model="CodigoProveedorIni"
                              :disabled_busqueda="activarBusquedaProv"
                              api="app/inventario/BusquedaProveedor"
                              api_campo_respuesta_mostrar="Nombre"
                              :api_campos="['Nombre','Codigo','Nit' ]"
                              :api_titulos="['Nombre','Codigo','Nit' ]"
                              api_campo_respuesta="Codigo"
                              :api_campo_respuesta_estricto="false"
                              :api_preload="false"
                              :disabled_texto="true"
                              :api_filtro="{  Nombre: this.NombreIni
                                            , Nit: this.NitIni
                                            , Codigo: this.CodigoIni}"
                              :mostrar_busqueda="true" />
                        </div>
                    </div>

                    <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12  pr-4">
                        <div class="w-1/6">
                            <vs-input label="Proveedor" class="w-full" v-model="this.CodigoProveedorFin" readonly />
                        </div>

                        <div class="w-5/6">
                            <SM-Buscar
                              class="w-full"
                              label="Al Proveedor"
                              v-model="CodigoProveedorFin"
                              :disabled_busqueda="activarBusquedaProv"
                              api="app/inventario/BusquedaProveedor"
                              api_campo_respuesta_mostrar="Nombre"
                              :api_campos="['Nombre','Codigo','Nit' ]"
                              :api_titulos="['Nombre','Codigo','Nit' ]"
                              api_campo_respuesta="Codigo"
                              :api_campo_respuesta_estricto="false"
                              :api_preload="false"
                              :disabled_texto="true"
                              :api_filtro="{ Nombre: this.NombreFin
                                           , Nit: this.NitFin
                                           , Codigo: this.CodigoFin}"
                              :mostrar_busqueda="true" />
                        </div>
                    </div>
                </div>

                <div class="flex flex-wrap mt-4 p-3 m-3">
                    <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12 pr-4 flex items-center justify-start">

                        <div class="w-1/4">
                            <label>Fecha Inicial:</label>
                        </div>
                        <div class="w-3/4">
                            <datepicker v-model="FechaInicial" placeholder="Fecha Inicial" :format="customFormatter"></datepicker>
                        </div>

                    </div>
                    <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12  flex items-center justify-start pr-4">

                        <div class="w-1/4">
                            <label>Fecha Final:</label>
                        </div>
                        <div class="w-3/4">
                            <datepicker v-model="FechaFinal" placeholder="Fecha Final" :format="customFormatter"></datepicker>
                        </div>

                    </div>
                </div>

                <div class="flex flex-wrap p-2 m-2">
                    <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12 mt-4 pr-4 ">
                        <h5>Tipo Proveedor</h5>
                        <br>
                        <br>
                        <div class="w-full">
                            <multiselect
                              v-model="selProveedor"
                              :options="ListaProveedores"
                              :searchable="true"
                              :close-on-select="true"
                              :show-labels="false"
                              :custom-label="ProveedorSeleccionado"
                              placeholder="Proveedor"
                              label="Parentesco">
                              <span
                              slot="noOptions">Lista
                              no
                              disponible.</span>
                            </multiselect>
                        </div>
                    </div>
                </div>

            </div>

            <div v-else-if="contenido === 4">

                <div class="flex flex-wrap mt-4 p-2 m-2">

                    <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12 pr-4 ">
                        <div class="w-1/6">
                            <vs-input label="Proveedor" class="w-full" v-model="this.CodigoProveedorIni" readonly />
                        </div>

                        <div class="w-5/6">
                            <SM-Buscar
                              class="w-full"
                              label="Del Proveedor"
                              v-model="CodigoProveedorIni"
                              :disabled_busqueda="activarBusquedaProv"
                              api="app/inventario/BusquedaProveedor"
                              api_campo_respuesta_mostrar="Nombre"
                              :api_campos="['Nombre','Codigo','Nit' ]"
                              :api_titulos="['Nombre','Codigo','Nit' ]"
                              api_campo_respuesta="Codigo"
                              :api_campo_respuesta_estricto="false"
                              :api_preload="false"
                              :disabled_texto="true"
                              :api_filtro="{ Nombre: this.NombreIni
                                           , Nit: this.NitIni
                                           , Codigo: this.CodigoIni}"
                              :mostrar_busqueda="true" />
                        </div>
                    </div>

                    <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12  pr-4">
                        <div class="w-1/6">
                            <vs-input label="Proveedor" class="w-full" v-model="this.CodigoProveedorFin" readonly />
                        </div>

                        <div class="w-5/6">
                            <SM-Buscar
                              class="w-full"
                              label="Al Proveedor"
                              v-model="CodigoProveedorFin"
                              :disabled_busqueda="activarBusquedaProv"
                              api="app/inventario/BusquedaProveedor"
                              api_campo_respuesta_mostrar="Nombre"
                              :api_campos="['Nombre','Codigo','Nit' ]"
                              :api_titulos="['Nombre','Codigo','Nit' ]"
                              api_campo_respuesta="Codigo"
                              :api_campo_respuesta_estricto="false"
                              :api_preload="false"
                              :disabled_texto="true"
                              :api_filtro="{  Nombre: this.NombreFin
                                            , Nit: this.NitFin
                                            , Codigo: this.CodigoFin}"
                              :mostrar_busqueda="true">
                              </SM-Buscar>
                        </div>
                    </div>
                </div>

                <div class="flex flex-wrap mt-4 p-3 m-3">
                    <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12 pr-4 flex items-center justify-start">

                        <div class="w-1/4">
                            <label>Fecha Inicial:</label>
                        </div>
                        <div class="w-3/4">
                            <datepicker v-model="FechaInicial" placeholder="Fecha Inicial" :format="customFormatter"></datepicker>
                        </div>
                        

                    </div>
                    <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12  flex items-center justify-start pr-4">

                        <div class="w-1/4">
                            <label>Fecha Final:</label>
                        </div>
                        <div class="w-3/4">
                            <datepicker v-model="FechaFinal" placeholder="Fecha Final" :format="customFormatter"></datepicker>
                        </div>

                    </div>

                </div>

            </div>
            <div v-else-if="contenido === 5">

                <div class="flex flex-wrap mt-4 p-2 m-2">

                    <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12  pr-2">

                        <div class="w-1/6">

                            <SM-Buscar
                              v-model="Periodo"
                              label="Periodo Cierre"
                              api="app/v1_OrdenCompra/ConsultarPeriodoCierre"
                              :api_campo_respuesta_mostrar="['codigo', 'descripcion']"
                              :api_campos="['codigo', 'descripcion']"
                              :api_titulos="['codigo', 'descripcion']"
                              api_campo_respuesta="codigo"
                              :api_preload="true"
                              :disabled_texto="true"
                              :callback_buscar="ConsultarPeriodo">
                            </SM-Buscar>

                        </div>
                        <div class="w-5/6">
                            <vs-input label="Periodo" class="w-full" v-model="this.DescripcionPeriodo" readonly />
                        </div>
                    </div>

                    
                </div>

                <div class="flex flex-wrap p-2 m-2">
                    <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12 mt-4 pr-4 ">
                        <multiselect
                          v-model="selImpuesto"
                          :options="listaImpuestos"
                          :searchable="true"
                          :close-on-select="true"
                          :show-labels="false"
                          :custom-label="ImpuestoSeleccionado"
                          placeholder="Impuesto"
                          track-by="Nombre"
                          label="Nombre">
                          <span
                          slot="noOptions">Lista
                          no
                          disponible.</span>
                        </multiselect>

                    </div>
                </div>


                <div class="flex flex-wrap mt-4 p-3 m-3">
                    <div class="w-full">
                        <h5>Ordenar por</h5>
                        <div class="example ex1">

                            <label  class="radio blue pt-2">
                                <input v-model="OrdenarPor"  type="radio" name="group1" value="P" />
                                <span>Proveedor</span>
                            </label>
                            <label class="radio blue">
                                <input v-model="OrdenarPor" type="radio" name="group1" value="F" />
                                <span>Fecha</span>
                            </label>
                            <label class="radio blue">
                                <input v-model="OrdenarPor" type="radio" name="group1" value="N" />
                                <span>Nit</span>
                            </label>
                            <p :style="{ color: $options.COLOR }">
                            </p>
                        </div>
                    </div>
                </div>

            </div>
            <div v-else-if="contenido === 6">

                <div class="flex flex-wrap mt-2 p-2 m-2">

                    <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12  pr-4">

                        <div class="w-1/6">

                            <SM-Buscar
                              v-model="Periodo"
                              label="Periodo Cierre"
                              api="app/v1_OrdenCompra/ConsultarPeriodoCierre"
                              :api_campo_respuesta_mostrar="['Codigo', 'Descripcion']"
                              :api_campos="['Codigo', 'Descripcion']"
                              :api_titulos="['Codigo', 'Descripcion']"
                              api_campo_respuesta="Codigo"
                              :api_preload="false"
                              :disabled_texto="true"
                              :callback_buscar="ConsultarPeriodo">
                            </SM-Buscar>

                        </div>
                        <div class="w-5/6">
                            <vs-input label="Periodo" class="w-full" v-model="this.DescripcionPeriodo" readonly />
                        </div>
                    </div>

                    <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12 pr-4">
                            <vs-checkbox v-model="IncluirRecibos" >Incluir Recibos</vs-checkbox>
                            <vs-checkbox v-model="SoloRecibos" >Solo Recibos</vs-checkbox>
                            
                    </div>



                </div>

                <div class="flex flex-wrap mt-2 p-3 m-3">
                    <div class="w-full">
                        <h5>Ordenar por</h5>
                        <div class="example ex1">

                            <label class="radio blue pt-2">
                                <input v-model="OrdenarPor" type="radio" name="group1" value="P" />
                                <span>Proveedor</span>
                            </label>
                            <label class="radio blue">
                                <input v-model="OrdenarPor" type="radio" name="group1" value="F" />
                                <span>Fecha</span>
                            </label>
                            <label class="radio blue">
                                <input v-model="OrdenarPor" type="radio" name="group1" value="N" />
                                <span>Nit</span>
                            </label>
                            <p :style="{ color: $options.COLOR }">
                            </p>
                        </div>
                    </div>
                </div>

                <div class="flex flex-wrap mt-2 p-3 m-3">
                    <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12 pr-4 flex items-center justify-start">

                        <div class="w-1/4">
                            <label>Fecha de registro :</label>
                        </div>
                        <div class="w-3/4">
                            <datepicker v-model="FechaInicial" placeholder="Fecha Inicial" :format="customFormatter"></datepicker>
                        </div>

                    </div>

                </div>

                <div class="flex flex-wrap mt-2 p-2 m-2">

                    <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12  pr-4">

                        <div class="w-1/6">

                            <SM-Buscar
                              v-model="CodigoCorporativo"
                              label="Corporativo"
                              api="app/Ajenos/Busqueda_Corporativo"
                              :api_campos="['Corporativo', ['Nombres', 'Nombre'], ['Apellidos', 'Apellido'], '#NombreCompleto']"
                              :api_titulos="['Corporativo', 'Nombres', 'Apellidos', '#NombreCompleto']"
                              api_campo_respuesta="Corporativo"
                              :api_preload="true"
                              :disabled_search_input="true"
                              api_campo_respuesta_mostrar="NombreCompleto"
                              :callback_buscar="ConsultarCorporativo"
                            />

                        </div>
                        <div class="w-5/6">
                            <vs-input label="Periodo" class="w-full" v-model="this.NombreCompletoCorporativo" readonly />
                        </div>
                    </div>
                </div>

                <div class="flex flex-wrap mt-2 p-3 m-3">
                    <div class="example ex1">

                        <label class="radio blue pt-2">
                            <input v-model="FiltrarPor" type="radio" name="group2" value="R" />
                            <span>Auxiliar Revisado</span>
                        </label>
                        <label class="radio blue">
                            <input v-model="FiltrarPor" type="radio" name="group2" value="P" />
                            <span>Auxiliar Pendiente de Revisión</span>
                        </label>
                        <label class="radio blue">
                            <input v-model="FiltrarPor" type="radio" name="group2" value="T" />
                            <span>Todas</span>
                        </label>
                        <p :style="{ color: $options.COLOR }">
                        </p>
                    </div>
                </div>

            </div>

            <div v-if="contenido === 7">

                <div class="flex flex-wrap mt-2 p-2 m-2">

                    <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12  pr-4">

                        <div class="w-1/6">

                            <SM-Buscar
                              v-model="Periodo"
                              label="Periodo Cierre"
                              api="app/v1_OrdenCompra/ConsultarPeriodoCierre"
                              :api_campo_respuesta_mostrar="['codigo', 'descripcion']"
                              :api_campos="['codigo', 'descripcion']"
                              :api_titulos="['codigo', 'descripcion']"
                              api_campo_respuesta="codigo"
                              :api_preload="true"
                              :disabled_texto="true"
                              :callback_buscar="ConsultarPeriodo">
                            </SM-Buscar>

                        </div>
                        <div class="w-5/6">
                            <vs-input label="Periodo" class="w-full" v-model="this.DescripcionPeriodo" readonly />
                        </div>
                    </div>
                </div>

                <div class="flex flex-wrap mt-4 p-2 m-2">
                    <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12 pr-4 ">
                        <div class="w-1/6">
                            <vs-input label="Proveedor" class="w-full" v-model="this.CodigoProveedorIni" readonly />
                        </div>

                        <div class="w-5/6">
                            <SM-Buscar
                              class="w-full"
                              label="Del Proveedor"
                              v-model="CodigoProveedorIni"
                              :disabled_busqueda="activarBusquedaProv"
                              api="app/inventario/BusquedaProveedor"
                              api_campo_respuesta_mostrar="Nombre"
                              :api_campos="['Nombre','Codigo','Nit' ]"
                              :api_titulos="['Nombre','Codigo','Nit' ]"
                              api_campo_respuesta="Codigo"
                              :api_campo_respuesta_estricto="false"
                              :api_preload="false"
                              :disabled_texto="true"
                              :api_filtro="{  Nombre: this.NombreIni
                                            , Nit: this.NitIni
                                            , Codigo: this.CodigoIni}"
                              :mostrar_busqueda="true" 
                            />
                        </div>
                    </div>

                    <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12  pr-4">
                        <div class="w-1/6">
                            <vs-input label="Proveedor" class="w-full" v-model="this.CodigoProveedorFin" readonly />
                        </div>

                        <div class="w-5/6">
                            <SM-Buscar
                              class="w-full"
                              label="Al Proveedor"
                              v-model="CodigoProveedorFin"
                              :disabled_busqueda="activarBusquedaProv"
                              api="app/inventario/BusquedaProveedor"
                              api_campo_respuesta_mostrar="Nombre"
                              :api_campos="['Nombre','Codigo','Nit' ]"
                              :api_titulos="['Nombre','Codigo','Nit' ]"
                              api_campo_respuesta="Codigo"
                              :api_campo_respuesta_estricto="false"
                              :api_preload="false"
                              :disabled_texto="true"
                              :api_filtro="{  Nombre: this.NombreFin
                                            , Nit: this.NitFin
                                            , Codigo: this.CodigoFin}"
                              :mostrar_busqueda="true" 
                            />
                        </div>
                    </div>
                </div>

            </div>

        <div v-if="contenido === 8">

            <div class="flex flex-wrap mt-4 p-2 m-2">

                <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12 pr-4 ">
                    <div class="w-1/6">
                        <vs-input label="Proveedor" class="w-full" v-model="this.CodigoProveedorIni" readonly />
                    </div>

                    <div class="w-5/6">
                        <SM-Buscar
                        class="w-full"
                        label="Del Proveedor"
                        v-model="CodigoProveedorIni"
                        :disabled_busqueda="activarBusquedaProv"
                        api="app/inventario/BusquedaProveedor"
                        api_campo_respuesta_mostrar="Nombre"
                        :api_campos="['Nombre','Codigo','Nit' ]"
                        :api_titulos="['Nombre','Codigo','Nit' ]"
                        api_campo_respuesta="Codigo"
                        :api_campo_respuesta_estricto="false"
                        :api_preload="false"
                        :disabled_texto="true"
                        :api_filtro="{  Nombre: this.NombreIni
                                        , Nit: this.NitIni
                                        , Codigo: this.CodigoIni}"
                        :mostrar_busqueda="true" 
                        />
                    </div>
                </div>

                <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12  pr-4">
                    <div class="w-1/6">
                        <vs-input label="Proveedor" class="w-full" v-model="this.CodigoProveedorFin" readonly />
                    </div>

                    <div class="w-5/6">
                        <SM-Buscar
                        class="w-full"
                        label="Al Proveedor"
                        v-model="CodigoProveedorFin"
                        :disabled_busqueda="activarBusquedaProv"
                        api="app/inventario/BusquedaProveedor"
                        api_campo_respuesta_mostrar="Nombre"
                        :api_campos="['Nombre','Codigo','Nit' ]"
                        :api_titulos="['Nombre','Codigo','Nit' ]"
                        api_campo_respuesta="Codigo"
                        :api_campo_respuesta_estricto="false"
                        :api_preload="false"
                        :disabled_texto="true"
                        :api_filtro="{  Nombre: this.NombreFin
                                        , Nit: this.NitFin
                                        , Codigo: this.CodigoFin}"
                        :mostrar_busqueda="true" 
                        />
                    </div>
                </div>
            </div>

            <div class="flex flex-wrap mt-6 p-3 m-3">
                <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12 pr-4 flex items-center justify-start">

                    <div class="w-1/4">
                        <label>Fecha Inicial:</label>
                    </div>
                    <div class="w-3/4">
                        <datepicker v-model="FechaInicial" placeholder="Fecha Inicial" :format="customFormatter"></datepicker>
                    </div>
                
                </div>       
                <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12 pr-4">
                            <vs-checkbox v-model="FiltrarPorFecha" >Filtrar por fecha</vs-checkbox>
                            <vs-checkbox v-model="IncluirRecibos" >incluir Aplicados</vs-checkbox>                            
                </div>       

            </div>

        </div>


        </div>
        <div class="a-r25qN-1">

            <div class="sm:w-full md:w-full lg:w-full xl:w-full" style="padding:9px;border:1px solid #ccc;border-radius:5px">
                <label class="vs-input--label">Destino del reporte</label><br />                
                <BaseRadioButtonGroup :options="radioOptions" v-model="tipoImpresionSel" :groupName="'tiposImpresion'" :key="radioKey" />
            </div>

            <vs-row class="pt-4">
                <vs-col vs-w="12">
                    <vs-col class="ml-6">
                        <vs-button class="block" @click.native="GenerarReporte()">
                            <i class="fas fa-file-pdf height:25px"></i>
                            Generar
                        </vs-button>
                    </vs-col>
                </vs-col>
            </vs-row>

        </div>
    </div>

</vx-card>
</template>

<script>
import BaseRadioButtonGroup from "@/components/sermesa/global/radioButtons/BaseRadioButtonGroup";
import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";
import Datepicker from 'vuejs-datepicker';
import moment from "moment";

export default {
    components: {
        BaseRadioButtonGroup,
        Multiselect,
        Datepicker
    },
    data() {

        return {
            optionChecked: '',
            tipoImpresionSel: '',
            contenido: 1,
            radioOptions: [],
            ListaImpuestos: [],
            radioKey: 0,
            selProveedor: '',
            NombreIni: '',
            CodigoIni: '',
            NitIni: '',
            NombreFin: '',
            CodigoFin: '',
            NitFin: '',
            ListaProveedores: [{
                    Codigo: '1',
                    Descripcion: 'Proveedor'
                },
                {
                    Codigo: '2',
                    Descripcion: 'Medico'
                },
                {
                    Codigo: '3',
                    Descripcion: 'Profesional'
                },
                {
                    Codigo: '4',
                    Descripcion: 'Importaciones'
                },
                {
                    Codigo: '5',
                    Descripcion: 'Otros'
                },
                {
                    Codigo: '6',
                    Descripcion: 'Todos'
                }
            ],
            CodigoProveedorIni: '',
            CodigoProveedorFin: '',
            activarBusquedaProv: false,
            activarBusquedaPeriodo: false,
            FechaInicial: '',
            FechaFinal: '',
            CodigoPeriodoCierre: '',
            DescripcionPeriodo: '',
            selImpuesto: '',
            CodigoCorporativo: '',
            Periodo:'',
            OrdenarPor:'',
            FiltrarPor:'',
            NombreCompletoCorporativo:'',
            SoloRecibos:'', 
            IncluirRecibos:'',
            FiltrarPorFecha:''


        }
    },
    watch: {
        optionChecked(newVal) {
            // Lógica para cambiar el valor de contenido según el valor de optionChecked
            switch (newVal) {
                case 'ReporteProveedores':
                    this.contenido = 1;
                    break;
                case 'ReporteComprasporProveedor':
                    this.contenido = 2;
                    break;
                case 'ReporteMovimientoporProveedor':
                    this.contenido = 3;
                    break;
                case 'ReporteSaldosporProveedor':
                    this.contenido = 4;
                    break;
                case 'ReportedeImpuestos':
                    this.contenido = 5;
                    break;
                case 'ReporteLibrodeCompras':
                    this.contenido = 6;
                    break;
                case 'ReporteSaldosPorPeriodo':
                    this.contenido = 7;
                    break;                    
                case 'ReporteAnticiposProveedor':
                    this.contenido = 8;
                    break;                    
                default:
                    this.contenido = 0;
            }
        }
    },
    mounted() {

        this.contenido = 0

        this.ConsultarImpuestos();

        this.radioOptions = [{
                "TipoImpresion": "1",
                "Descripcion": "PDF"
            },
            {
                "TipoImpresion": "2",
                "Descripcion": "EXCEL"
            }
        ]
    },
    methods: {

        ProveedorSeleccionado({
            Descripcion
        }) {
            return `${Descripcion} `

        },
        customFormatter(date) {
            return moment(date).format('DD/MM/YYYY');
        },
        ConsultarPeriodo(datos) {

            this.DescripcionPeriodo = datos.descripcion

        },
        async ConsultarImpuestos() {
            const resp = await this.axios.post('/app/v2_api_compras/ListaImpuestos', {})

            this.listaImpuestos = resp.data.json;
        
        },
        async ConsultarCorporativo(datacorp){
            this.NombreCompletoCorporativo = datacorp.NombreCompleto
        },
        ImpuestoSeleccionado({
            Nombre
        }) {
            return `${Nombre}`
        },
        nullOrUndefined(valor) {            
            return valor === null || valor === undefined || (typeof valor === 'string' && valor.trim() === '') || (typeof valor === 'string' && valor.trim() === ' ');
        },
        validarOpciones(reporte){
        
            if ( reporte === "ReporteProveedores" || reporte  === "ReporteComprasporProveedor" || reporte ==="ReporteMovimientoPorProveedor" ){
                if ( this.nullOrUndefined(this.selProveedor.Codigo)=== true )
                {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Alerta',
                        text: 'Elegir Tipo Proveedor',
                    })
                    return false
                }
            }


            if(reporte === "ReporteComprasporProveedor" || reporte ==="ReporteMovimientoPorProveedor" 
            || reporte === "ReporteSaldosPorProveedor"  || reporte ==="ReporteSaldosPorPeriodo" ||
            reporte    === "ReporteSaldosPorPeriodo" ){
                if (this.nullOrUndefined(this.CodigoProveedorIni)=== true || this.nullOrUndefined(this.CodigoProveedorFin)=== true){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Alerta',
                        text: 'Elegir Rango Proveedores Busqueda',
                    })
                    return false
                }

            }

            if(reporte === "ReporteComprasporProveedor" || reporte ==="ReporteMovimientoPorProveedor" ||
               reporte ==="ReporteSaldosPorProveedor" ){
                if (this.nullOrUndefined(this.FechaInicial)=== true ){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Alerta',
                        text: 'Fecha Inicial es Requerida',
                    })
                    return false
                }

                if (this.nullOrUndefined(this.FechaFinal)=== true ){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Alerta',
                        text: 'Fecha Final es Requerida',
                    })
                    return false
                }    
            }

            if ( reporte === "ReporteImpuestosProveedor" || reporte ==="ReporteLibrodeComprasYServicios" ){
                if (this.Periodo === null){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Alerta',
                        text: 'Periodo es requerido',
                    })
                    return false
                }
            }

            if ( reporte === "ReporteImpuestosProveedor"  ){
                if (this.nullOrUndefined(this.selImpuesto.Nombre)){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Alerta',
                        text: 'Impuesto es requerido',
                    })
                    return false
                }

            }
            
            return true
            
        },
        async GenerarReporte() {




                if (this.nullOrUndefined(this.optionChecked) === true ){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Alerta',
                        text: 'Opción de reporte requerida',
                    })
                    return
                }

                let reporte
                switch (this.optionChecked) {
                    case 'ReporteProveedores':
                        reporte  = "ReporteProveedores";
                    break;
                    case 'ReporteComprasporProveedor':
                        reporte  = "ReporteComprasporProveedor";
                    break;
                    case 'ReporteMovimientoporProveedor':
                    reporte  = "ReporteMovimientoPorProveedor";
                    break;
                    case 'ReporteSaldosporProveedor':
                        reporte  = "ReporteSaldosPorProveedor";
                    break;     
                    case 'ReportedeImpuestos':
                        reporte  = "ReporteImpuestosProveedor";
                    break;
                    case 'ReporteLibrodeCompras':
                        reporte  = "ReporteLibrodeComprasYServicios";
                    break;
                    case 'ReporteSaldosPorPeriodo':
                        reporte  = "ReporteSaldosPorPeriodo";
                    break;
                    case 'ReporteAnticiposProveedor':
                        reporte  = "ReporteAnticiposPorProveedor";
                    break;
                                    
                    default:
                    // code block
                }                

                
                if (!this.validarOpciones(reporte))
                    return 

                const fechaInicial = moment(this.FechaInicial)
                const fechaFinal   = moment(this.FechaFinal)

            
                let postData = {
                    TipoProveedor: this.nullOrUndefined(this.selProveedor.Codigo) === true ?'':this.selProveedor.Codigo,
                    ProveedorInicial: this.nullOrUndefined(this.CodigoProveedorIni) === true ? '': this.CodigoProveedorIni,
                    ProveedorFinal: this.nullOrUndefined(this.CodigoProveedorFin) === true ?'':this.CodigoProveedorFin,
                    FechaInicio:fechaInicial.isValid()? moment(this.FechaInicial).format('YYYY-MM-DD HH:mm:ss'):'',
                    FechaFin:   fechaFinal.isValid()? moment(this.FechaFinal).format('YYYY-MM-DD HH:mm:ss'):'',
                    OrdenarPor: this.nullOrUndefined(this.OrdenarPor) === true? '': this.OrdenarPor,
                    NombreImpuesto: this.nullOrUndefined(this.selImpuesto.Nombre) === true ?'': this.selImpuesto.Nombre,
                    Periodo: this.Periodo >0 ? this.Periodo:'',
                    DescripcionPeriodo: this.nullOrUndefined(this.DescripcionPeriodo) === true ? '': this.DescripcionPeriodo,
                    FiltrarPor: this.nullOrUndefined(this.FiltrarPor) === true ? '': this.FiltrarPor,
                    Coporativo: this.nullOrUndefined(this.CodigoCorporativo) === true? '': this.CodigoCorporativo,
                    SoloRecibos: this.SoloRecibos === true ?'R':'',
                    IncluirRecibos: this.IncluirRecibos=== true ?'R':'',
                    FiltrarPorFecha: this.FiltrarPorFecha=== true ?'1':''
                }

                                
                this.$reporte_modal({
                    Nombre: reporte,
                    Opciones: {
                        ...postData
                    },
                    Formato:this.tipoImpresionSel.Descripcion
                })
            }

    }

}
</script>

<style scoped>
.container {
    grid-gap: 5px;
    margin: 0;
    padding: 0;
    right: 0;
    display: grid;
    width: 100%;
    height: 100%;
    grid-template-areas: "a-r25qN-0 a-x65zy-1"
        "a-r25qN-0 a-r25qN-1";
    grid-template-columns: 0.25fr 0.75fr;
    grid-template-rows: 0.70fr 0.30fr;
}

.container>div {
    border: 1px solid #888;
}

.a-r25qN-0 {
    grid-area: a-r25qN-0;
}

.a-x65zy-1 {
    grid-area: a-x65zy-1;
}

.a-r25qN-1 {
    grid-area: a-r25qN-1;
}

.container {
    max-width: 100%;
}

.example {
    margin-top: 0px;
}

.example input {
    display: none;
}

.example label {
    margin-right: 10px;
    display: inline-block;
    cursor: pointer;
    padding: 5px;
}

.ex1 span {
    display: block;
    padding: 15px 80px 15px 45px;
    border: 2px solid #ddd;
    border-radius: 5px;
    position: relative;
    transition: all 0.25s linear;
}

.ex1 span:before {
    content: '';
    position: absolute;
    left: 5px;
    top: 50%;
    -webkit-transform: translatey(-50%);
    transform: translatey(-50%);
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #ddd;
    transition: all 0.25s linear;
}

.ex1 input:checked+span {
    background-color: #fff;
    box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.1);
}

.ex1 .blue input:checked+span {
    color: rgb(96, 162, 203);
    border-color: rgb(143, 143, 160);
}

.ex1 .blue input:checked+span:before {
    background-color: rgb(98, 155, 209);
}

.example.ex1 .radio-container {
    display: flex;
    flex-direction: column;
}

/* .example.ex1 {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
} */
</style>
