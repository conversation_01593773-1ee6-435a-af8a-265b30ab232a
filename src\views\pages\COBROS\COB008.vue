<template>
<div class="verif-aut-medicos-container vs-fixed-dx-input-container">
    <vx-card title="Verificación de Autorización de Médicos">

        <BusquedaAdmision ref="buscadorAdmision" class="w-full" v-bind="filtrosBusquedaAdmision" @datos_admision="CargarAdmision" @limpiar_datos_admision="Limpiar" />
        <div class="flex flex-wrap gap-2 lg-icon mb-2 mt-2" v-if="Admision.Codigo">

            <vs-button :disabled="!enabledActions.OperacionCargos" icon="check" type="border" @click="AutorizarTodos"> Marcar todas con S</vs-button>
            <vs-button :disabled="!enabledActions.OperacionCargos" icon-pack="fas" icon="fa-list" color="dark" type="border" @click="ValidarAutorizacion"> Validación Autorización </vs-button>
            <vs-button :disabled="!enabledActions.OperacionCargos" icon-pack="fas" icon="fa-table" @click="ValidarNoElegible"> Validar no elegible </vs-button>
            <vs-button icon-pack="fas" icon="fa-file-pdf" color="danger" @click="Importar(false)"> Visualizar </vs-button>
            <vs-button icon-pack="fas" icon="fa-file-pdf" color="danger" @click="Importar(true)"> Importar </vs-button>

        </div>

        <dx-data-grid v-bind="gridConf" :data-source="cargosCtaAjena" :columns="columnsCargos" @row-updating="onRowUpdating" />

        <div class="flex flex-row place-items-end gap-2 mt-2">
            <vs-button icon-pack="fas" icon="fa-signature" @click="Firmar" :disabled="!enabledActions.Firma"> Firma </vs-button>
            <vs-input label="Usuario" readonly :value="Admision.VerificacionUsuario" />
            <vs-input label="Fecha" readonly :value="$formato_fecha(Admision.VerificacionFecha)" />
        </div>

    </vx-card>
</div>
</template>

<script>
import {
    gridConfiguration,
} from './data'

import BusquedaAdmision from '../../../components/sermesa/modules/admisiones/BusquedaAdmision.vue'

export default {
    name: 'AutorizaCargosMedcos',
    components: {
        BusquedaAdmision,
    },
    data() {
        return {
            filtrosBusquedaAdmision: {
                api_filtro: {
                    'Opcion': 'CONSULTA',
                    'SubOpcion': 'ADMISION',
                },
                api_campos: ['Serie', 'Codigo', 'Nombres', 'Apellidos', 'habitacion'],
                api_titulos: ['Serie#', 'Admision#', 'Nombres', 'Apellidos', 'Habitación'],
            },
            //Configuracion DataGrid
            columnsCargos: [{
                    dataField: 'Ajeno',
                    width: 400,
                    allowEditing: false,
                }, {
                    dataField: 'Valor',
                    dataType: 'number',
                    format: 'Q #.00',
                    allowEditing: false,
                }, {
                    dataField: 'Fecha',
                    dataType: 'date',
                    allowEditing: false,
                },
                'Autorizado',
                {
                    dataField: 'Categoria',
                    allowEditing: false,
                }
            ],
            Admision: null,
            cargosCtaAjena: null,
            gridConf: {
                ...gridConfiguration,
                editing: {
                    allowUpdating: true,
                    mode: 'cell',
                },
                searchPanel: null,
            },
            path: null,
            corporativoAutoriza: null,
        }
    },

    methods: {
        /**No se pueden modificar los cargos 
         * adm si ya esta firmada
         * cargo Tiene facRec 
         * adm TieneCoaseguro 
         */

        CargarCuentaAjena() {
            this.axios.post('/app/v1_cobros/CargosAjenos', {
                SerieAdmision: this.Admision.Serie,
                Admision: this.Admision.Codigo,
            }).then(resp => {
                this.cargosCtaAjena = resp.data.map(t => {
                    t.Autorizado = t.Autorizado == 'S'
                    return t
                })
            })
        },
        CargarRuta() {
            this.axios.post('/app/v1_cobros/PathAutorizacion', {
                SerieAdmision: this.Admision.Serie,
                Admision: this.Admision.Codigo,
            }).then(resp => {
                this.path = resp.data[0]
            })
        },
        Limpiar() {
            this.Admision = {
                Serie: null,
                Codigo: null,
                Paciente: null,
                Nombre: null,
                Apellido: null,
                ApellidoCasada: null,
                Tipodescuento: null,
                VerificacionSeguro: null,
                VerificacionUsuario: null,
                VerificacionFecha: null,
                Coaseguro: null,
            }
            this.cargosCtaAjena = null
            this.corporativoAutoriza = null
        },
        CargarAdmision(e) {
            this.axios.post('/app/v1_cobros/BusquedaAdmision', {
                SerieAdmision: e.Serie,
                Admision: e.Codigo,
            }).then(resp => {
                this.Admision = resp.data[0]
                this.CargarCuentaAjena()
                this.CargarRuta()
            })
        },
        Importar(cargar) {

            const paramFM = {
                Path: this.path.Path,
                raTipo: this.Admision.Serie,
                raOrden: this.Admision.Codigo,
                permisover: 'SI',
                permisoimportar: cargar ? 'SI' : 'NO', //SI para cargar
                PathActualiza: '-',
                NombrePaciente: 'HOLA',
                ImagenUnica: 'SI',
                NombreImagen: this.path.NombreImagen,
            }

            let comando = `C:\\Aplicaciones\\visualizadorfm.exe ${paramFM.Path} ${paramFM.raTipo} ${paramFM.raOrden} ${paramFM.permisover} ${paramFM.permisoimportar} ${paramFM.PathActualiza} ${paramFM.NombrePaciente} ${paramFM.ImagenUnica} ${paramFM.NombreImagen}`
            this.$socke_up(comando)
        },
        ValidarNoElegible() {
            this.$validar_func_usuario_permiso('/COBROS/COB008', 'VALIDAR_CARGO_NO_ELEGIBLE')
                .then((x) => {
                    this.corporativoAutoriza = x.corporativo
                    this.axios.post('/app/v1_cobros/DesautorizarCargos30', {
                        SerieAdmision: this.Admision.Serie,
                        Admision: this.Admision.Codigo,
                        Corporativo: this.corporativoAutoriza,
                    }).then(() => {
                        this.CargarCuentaAjena()
                    })
                })

        },
        ValidarAutorizacion() {
            this.axios.post('/app/v1_cobros/ValidacionAutorizacion', {
                SerieAdmision: this.Admision.Serie,
                Admision: this.Admision.Codigo,
            }).then(() => {
                this.CargarCuentaAjena()
            })
        },
        AutorizarTodos() {
            this.axios.post('/app/v1_cobros/ActualizarCargos', {
                SerieAdmision: this.Admision.Serie,
                Admision: this.Admision.Codigo,
            }).then(() => {
                this.CargarCuentaAjena()
            })
        },
        Firmar() {
            this.axios.post('/app/v1_cobros/FirmaValidacionAdmision', {
                SerieAdmision: this.Admision.Serie,
                Admision: this.Admision.Codigo,
            }).then(() => {
                this.CargarAdmision(this.Admision)
            })
        },
        onRowUpdating(e) {
            this.axios.post('/app/v1_cobros/ValidacionAutorizacionxOrden', {
                SerieAdmision: this.Admision.Serie,
                Admision: this.Admision.Codigo,
                TipoOrden: e.oldData.TipoOrden,
                Orden: e.oldData.Orden,
                LineaOrden: e.oldData.Linea,
                Autorizado: e.newData.Autorizado ? 'S' : 'N',
            }).finally(() => {
                this.CargarCuentaAjena()
            })
        }
    },
    computed: {
        fromInstaceRemisiones() {
            return this.$refs['formRemisiones'].instance
        },
        enabledActions() {
            return {
                Firma: Boolean(this.Admision.Codigo) && this.Admision.VerificacionSeguro != 'S',
                CargarDoc: Boolean(this.Admision),
                OperacionCargos: Boolean(this.cargosCtaAjena) && this.cargosCtaAjena.length > 0
            }
        }
    },
    beforeMount() {
        this.Limpiar()

        //cargar categorias :)        
    }
}
</script>

<style>
@import 'styles.css';
</style>
