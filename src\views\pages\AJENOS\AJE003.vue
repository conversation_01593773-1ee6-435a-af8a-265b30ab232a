<template>
<vx-card title="Filtro de cargos por tipo de médico y admisión">
    <Nuevo :nuevo="permisos.nuevo" :polizas="listadoPolizas" :callback="Consulta().Filtros" :tipoMedico="tipoMedico" :tipoAdmision="tipoAdmision" :categoriaCargo="categoriaCargo" />

    <vs-popup ref="buscador" title="Editar Filtro" :active.sync="editarFiltro.mostrar" style="z-index:52000" id="div-with-loading" class="vs-con-loading__container">
        <Nuevo :nuevo="permisos.nuevo" :polizas="listadoPolizas" :callback="Consulta().Filtros" :tipoMedico="tipoMedico" :tipoAdmision="tipoAdmision" :categoriaCargo="categoriaCargo" :editarFiltro.sync="editarFiltro" />
    </vs-popup>

    <vs-popup ref="buscador" title="Reglas de Pago" :active.sync="reglaFiltro.mostrar" style="z-index:52000" id="div-with-loading" class="vs-con-loading__container">
        <Reglas :callback="Consulta().Filtros" :polizas="listadoPolizas" :reglaFiltro.sync="reglaFiltro" />
    </vs-popup>

    <vs-divider></vs-divider>
    <vs-table2 max-items="10" pagination search :data="filtros_" tooltip>
        <template slot="header">
            <div style="border:1px solid #ccc;padding:5px;border-radius:5px">
                <vs-select label="Tipo" style="display:inline-block;margin-right:4px;width:120px" name="item-category" v-model="filtro.tipo">
                    <vs-select-item :key="key" :value="item.valor" :text="item.texto" v-for="(item,key) in listado.tipo" />
                </vs-select>
                <vs-select label="Pronto Pago" style="display:inline-block;margin-right:4px;width:120px" name="item-category" v-model="filtro.prontopago">
                    <vs-select-item :key="key" :value="item.valor" :text="item.texto" v-for="(item,key) in listado.prontopago" />
                </vs-select>
                <vs-select label="Admisión" style="display:inline-block;margin-right:4px;width:120px" name="item-category" v-model="filtro.admision">
                    <vs-select-item :key="key" :value="item.valor" :text="item.texto" v-for="(item,key) in listado.admision" />
                </vs-select>
            </div>
        </template>
        <template slot="thead">
            <th order="TipoMedico_">Tipo Médico</th>
            <th order="ProntoPago">Pronto Pago</th>
            <th order="TipoAdmision_">Tipo Admisión</th>
            <th width="190px">Categoría del Cargo</th>
            <th>Descripción</th>
            <th>Polizas Asociadas</th>
            <th>Regla de Pago</th>
            <th width="100px">Activo</th>
            <th width="150px">Acciones</th>
        </template>
        <template slot-scope="{data}">
            <tr :key="indextr" v-for="(tr, indextr) in data" :is-selected="tr.Seleccion" not-click-selected>
                <vs-td2 width="110px">
                    {{ tr.TipoMedico_}}
                </vs-td2>

                <vs-td2 width="80px">
                    <vs-checkbox :val="tr" v-model="tr.ProntoPago" disabled />
                </vs-td2>

                <vs-td2 width="90px">
                    {{ tr.TipoAdmision_}}
                </vs-td2>

                <vs-td2>
                    {{ tr.CategoriaCargo }}
                </vs-td2>

                <vs-td2>
                    {{ tr.Descripcion }}
                </vs-td2>

                <vs-td2 width="100px" style="text-align:right" noTooltip>
                    <div class="badge" v-if="tr.TipoAdmision==2">
                        {{ tr.Polizas }}
                    </div>
                </vs-td2>

                <vs-td2 width="100px" noTooltip>
                    <vs-checkbox class="tabla" :value="tr.Reglas>0" disabled />
                </vs-td2>

                <vs-td2 noTooltip>
                    <vs-switch v-model="tr.Estado" @click="(permisos.deshabilitar)?Editar().Estado(tr):null" style="height: 23px;" :disabled="!permisos.deshabilitar" />
                </vs-td2>

                <vs-td2 width="150px" noTooltip>
                    <vs-button :disabled="!permisos.reglas" color="success" size="small" icon-pack="far" icon="fa-money-bill-alt" class="mr-1" style="display:inline-block" v-on:click="(permisos.reglas)?Otros().reglas(tr):null"></vs-button>
                    <vs-button :disabled="!permisos.editar_poliza" color="primary" size="small" icon-pack="fas" icon="fa-edit" class="mr-1" style="display:inline-block" v-on:click="(permisos.editar_poliza)?Otros().editar(tr):null"></vs-button>
                </vs-td2>
            </tr>
        </template>
    </vs-table2>

</vx-card>
</template>

<script>
export default {
    data() {
        return {
            /**
             * Documentación del módulo
             */
            doc_: [
                'Permite indicar el tipo de médico, pronto pago, tipos de admisión y los seguros asociados y la categoría del producto que seran pagados.',
                'Se debe especificar una regla de pago para poder visualizar los cargos en la planilla de honorarios.',
                'Los filtros y las reglas de pago no son retroactivas.',
                '* Se obtienen los datos del sp <i>sp_Ajenos_Honorarios_FiltroCargos_Consulta</i>.',
            ],

            /**
             * Filtro
             */
            filtro: {
                tipo: null,
                prontopago: null,
                admision: null
            },

            listado: {
                tipo: [
                    {
                        valor: null,
                        texto: 'Sin Filtro'
                    }, {
                        valor: 'M',
                        texto: 'Casa'
                }, {
                        valor: 'CC',
                        texto: 'Casa-Cortesía'
                }, {
                        valor: 'C',
                        texto: 'Cortesía'
                }],
                prontopago: [
                    {
                        valor: null,
                        texto: 'Sin Filtro'
                    },
                    {
                        valor: true,
                        texto: 'Sí'
                    },
                    {
                        valor: false,
                        texto: 'No'
                    }
                ],
                admision: [
                    {
                        valor: null,
                        texto: 'Sin Filtro'
                    }, {
                        valor: '1',
                        texto: 'Privada'
                    }, {
                        valor: '2',
                        texto: 'Seguro'
                    }
                ]
            },

            tipoMedico: [{
                Codigo: 'C',
                Nombre: 'Cortesía'
            }, {
                Codigo: 'M',
                Nombre: 'Casa'
            }, {
                Codigo: 'CC',
                Nombre: 'Casa-Cortesía'
            }],

            tipoAdmision: [{
                Codigo: '1',
                Nombre: 'Privada'
            }, {
                Codigo: '2',
                Nombre: 'Seguro'
            }],

            categoriaCargo: [{
                Codigo: '97',
                Nombre: '(97) Servicios por Honorarios Médicos'
            }, {
                Codigo: '30',
                Nombre: '(30) Servicios por Honorarios Médicos No Elegible'
            }, {
                Codigo: '98',
                Nombre: '(98) Cuenta Ajena No Elegibles'
            }, {
                Codigo: '99',
                Nombre: '(99) Cuenta Ajena'
            }],

            valor: null,

            selected: [],

            listadoPolizas: [],

            /**
             * Información de los tabla
             */
            filtros: [],
            tablaSeleccion: [],

            /**
             * Editar Poliza
             */
            editarFiltro: {
                mostrar: false,
                info: null
            },

            /*
             * Editar Poliza
             */
            reglaFiltro: {
                mostrar: false,
                info: null
            },

            /**
             * Listado de permisos
             */
            permisos: {
                nuevo: false,
                deshabilitar: false,
                editar_poliza: false,
                reglas: false
            }
        }
    },
    components: {
        Nuevo: () => import('./AJE003_Nuevo'),
        Reglas: () => import('./AJE003_Reglas')
    },
    computed: {
        filtros_() {
            const f = (item) => {
                let resp = true
                if (this.filtro.tipo != null && resp) resp = item.TipoMedico.trim().toUpperCase() == this.filtro.tipo
                if (this.filtro.prontopago != null && resp) resp = item.ProntoPago == this.filtro.prontopago
                if (this.filtro.admision != null && resp) resp = item.TipoAdmision == this.filtro.admision
                return resp
            }
            return this.filtros.filter(f)
        }
    },
    methods: {
        //=======================================================================================================
        // CONSULTA
        //=======================================================================================================
        Consulta: function() {
            return {
                Filtros: () => {
                    this.filtros = []
                    return this.axios.post('/app/ajenos/FiltrosCargos_ListadoFiltros', {})
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.filtros = resp.data.json.map(m => {
                                    return {
                                        ...m,
                                        TipoAdmision_: this.tipoAdmision.filter(f => f.Codigo == m.TipoAdmision)[0].Nombre,
                                        TipoMedico_: (m.TipoMedico.trim() == 'M') ? 'Casa' : (m.TipoMedico == 'CC') ? 'Casa-Cortesía' : (m.TipoMedico.trim() == 'C') ? 'Cortesía' : '',
                                        Estado: (m.Estado == 'A') ? true : false,
                                        CategoriaCargo: this.categoriaCargo.filter(f => f.Codigo == m.CategoriaCargo)[0].Nombre,
                                        CodigoCategoriaCargo: m.CategoriaCargo
                                    }
                                })
                            }
                        })
                        .catch(() => {
                        })
                },
                Polizas: () => {
                    return this.axios.post('/app/ajenos/FiltrosCargosListadoPolizas', {})
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.listadoPolizas = resp.data.json.map(m => {
                                    return {
                                        ...m,
                                        Seleccionar: false
                                    }
                                })
                            }
                        })
                        .catch(() => {
                        })
                },

            }
        },

        //=======================================================================================================
        // NUEVO
        //=======================================================================================================
        Nuevo: function() {
            return {}
        },

        //=======================================================================================================
        // GUARDAR
        //=======================================================================================================
        Guardar: function() {
            return {
                filtro: () => {
                    
                }
            }
        },

        //=======================================================================================================
        // EDITAR
        //=======================================================================================================
        Editar: function() {
            return {
                Estado: (item) => {
                    if (!item.Estado == false) {
                        this.$vs.dialog({
                            type: 'confirm',
                            color: 'danger',
                            title: 'Confirmación',
                            acceptText: 'Deshabilitar',
                            cancelText: 'Cancelar',
                            text: `¿Desea deshabilitar el filtro de cargos?`,
                            accept: () => {
                                this.axios.post('/app/ajenos/FiltrosCargosEstado', {
                                        idFiltro: item.IdFiltro,
                                        Estado: 'I'
                                    })
                                    .then(() => {
                                        this.Consulta().Filtros()
                                    })
                            },
                            cancel: () => {
                                
                                setTimeout(() => {
                                    item.Estado = true
                                }, 100)
                            }
                        })
                    } else {
                        this.axios.post('/app/ajenos/FiltrosCargosEstado', {
                                idFiltro: item.IdFiltro,
                                Estado: 'A'
                            })
                            .catch(() => {
                                item.Estado = false
                            })
                    }
                }
            }
        },

        //=======================================================================================================
        // ELIMINAR
        //=======================================================================================================
        Eliminar: function() {
            return {
                filtro: (item) => {
                    
                    this.$vs.dialog({
                        type: 'confirm',
                        color: 'danger',
                        title: 'Confirmación',
                        text: `¿Desea eliminar el filtro con la descripcion: ${item.Descripcion}?`,
                        accept: () => {
                            this.axios.post('/app/ajenos/FiltrosCargosEliminarFiltro', {
                                    idFiltro: item.IdFiltro
                                })
                                .then(() => {
                                    this.Consulta().Filtros()
                                })
                        }
                    })
                }
            }
        },

        //=======================================================================================================
        // OTROS
        //=======================================================================================================
        Otros: function() {
            return {
                Limpiar_Datos: () => {
                    Object.entries(this.info).forEach(([key]) => {
                        this.info[key] = null
                    });
                },
                filtrarSeleccionados: () => {
                    this.poliza.filtrarSeleccionados = !this.poliza.filtrarSeleccionados;
                    this.poliza.pagina = null;
                    setTimeout(() => {
                        this.poliza.pagina = 1
                    }, 1000)
                },
                editar: (item) => {
                    this.editarFiltro.mostrar = true;
                    this.editarFiltro.info = item
                },
                reglas: (item) => {
                    this.reglaFiltro.mostrar = true;
                    this.reglaFiltro.info = item
                }
            }
        }
    },
    mounted() {
        this.Consulta().Filtros()
        this.Consulta().Polizas()

        this.permisos.nuevo = this.$validar_privilegio('NUEVO').status
        this.permisos.deshabilitar = this.$validar_privilegio('DESHABILITAR').status
        this.permisos.editar_poliza = this.$validar_privilegio('EDITAR_POLIZA').status
        this.permisos.reglas = this.$validar_privilegio('REGLAS').status
    }
}
</script>

<style>
.badge {
    /* border:1px solid #ccc; */
    border-radius: 10px;
    text-align: center;
    color: white;
    background-color: rgba(var(--vs-primary), 1);
    padding: 4px 15px;
    font-size: 12px;
    display: inline;
}
</style>
