<template>
  <div>
      <div class="principal">
          <vs-tabs v-model="activeTab" alignment="center" >
              <!--=========================================================================================================================
              ESTRUCTURA SOLICITUDES    
              ==========================================================================================================================-->
              <vs-tab label="Solicitudes" class="Solicitudes" @click="Consulta().ConsultaSolicitud()">
                  <div class="con-tab-ejemploo">
                      <vx-card :title="'Solicitudes Diagnóstico'">
                          <div>
                              <DxButton :width="200" text="Actualizar Listado" type="success" @click="Consulta().ConsultaSolicitud()" />
                          </div>
                          <br />
                          <div>
                            <DxDataGrid :data-source="Solicitudes" :show-borders="true" style="width: 100%;" class="color"  :paging="{ pageSize: 8 }">
                              <DxDataGridColumn width="auto" data-field="idSolicitud" caption="Solicitud" />
                              <DxDataGridColumn width="auto" data-field="Column1" caption="Fecha" />
                              <DxDataGridColumn width="auto" data-field="Afiliado" caption="Afiliado" />
                              <DxDataGridColumn width="auto" data-field="nombre" caption="Paciente" />
                              <DxDataGridColumn width="auto" data-field="CorporativoTrabajando" caption="Corporativo" />
                              <DxDataGridColumn width="auto" data-field="NombreAutoriza" caption="Trabajado Por Nombre" />
                              <!-- Columna para el botón "Iniciar Solicitud" -->
                              <DxDataGridColumn  type="buttons" :buttons="[
                                {
                                  icon: 'check',
                                  hint: 'Iniciar Solicitud',
                                  cssClass: 'btn-iniciar', // Clase personalizada
                                  onClick: (obj) => {
                                    ConsultaGeneral().EjecutarConsultas(obj.row.data) || changeTab(1);
                                  },
                                  visible: (e) => {
                                    return e.row.data.CorporativoTrabajando == '' || e.row.data.CorporativoTrabajando == sesion.corporativo;
                                  }
                                },

                                {
                                  icon: 'unlock',
                                  hint: 'Desbloquear Solicitud',
                                  cssClass: 'btn-desbloquear', // Clase personalizada
                                  onClick: (obj) => {
                                    Regresion().Regre(obj.row.data);
                                  }
                                }
                              ]" width="auto" :allow-reordering="false" :allow-resizing="false" />
                              
                              <DxDataGridDxSearchPanel :visible="true" :highlight-case-sensitive="true" />
                            </DxDataGrid>
                          </div>
                      </vx-card>
                  </div>
              </vs-tab>
              <!--=========================================================================================================================
                ESTRUCTURA SOLICITUD 
                ==========================================================================================================================-->
              <vs-tab label="Solicitud">
                  <div>
                      <vs-tabs :color="colorx">
                          <vs-tab label="Paciente"  @click="colorx = 'success'" icon="account_circle"  >
                              <vx-card >
                                  <div class="Contenido" style="display: flex;" >
                                    <div style="width: 100%;" class="p-1 w-full sm:w-1/1 md:w-1/1 ">
                                     <!--Datos Solicitud-->
                                      <h4>Solicitud</h4>
                                     <DxTextBox class="Buttom_color" label="Solicitud"  :disabled="true" v-model="info.idSolicitud" width="auto" />
                                     <DxTextBox class="Buttom_color" label="Fecha"  :disabled="true" v-model="info.FechaIngreso" width="auto" />
                                     <DxTextBox class="Buttom_color" label="Médico"  :disabled="true" v-model="info.medico" width="auto" />
                                     <DxTextBox class="Buttom_color" label="Nombre"  :disabled="true" v-model="info.NombreMedico" width="auto" />
                                     </div>
                                     <div style="width: 100%; margin-left: 2px;" class="p-1 w-full sm:w-1/1 md:w-1/1">
                                      <!-- Datos Afiliado -->
                                      <h4>Afiliado</h4>
                                      <DxTextBox class="Buttom_color" label="Afiliado"  :disabled="true" v-model="info.idAdiliado" width="auto" />
                                      <DxTextBox class="Buttom_color" label="Paciente"  :disabled="true" v-model="info.Paciente" width="auto" />
                                      <DxTextBox class="Buttom_color" label="Cliente"  :disabled="true" v-model="info.idCliente" width="auto" />
                                      <DxTextBox class="Buttom_color" label="Contrato"  :disabled="true" v-model="info.idContrato" width="auto" />
                                      </div>
                                      &nbsp;
                                      <div  class="p-1">
                                          <div>
                                              <button class="BotonInicio" style="background-color: #E74C3C;" @click="regresar()  || changeTab(0)">Regresar</button>
                                          </div>                      
                                      </div>
                                  </div>
                              </vx-card>
                              <vx-card title="Diagnósticos" style="margin-top: 5px;">
                                <DxDataGrid  :data-source="Diagnosticos" :paging="{ pageSize: 2 }"  :show-borders="true" :load-panel="{ enabled: false }" class="color" style="width: 100%;" >
                                  <DxDataGridColumn width="auto" data-field="CodigoDiagnostico" caption="Código" />
                                  <DxDataGridColumn width="auto" data-field="descripcion" caption="Diagnóstico" />
                                </DxDataGrid>
                              </vx-card>
                              <!--Plan Medico-->
                              <vx-card style="margin-top: 5px;" title="Plan Médico">
                                  <div class="con-tab-ejemplo">
                                      <DxDataGrid :column-auto-width="true"  :data-source="plan" :load-panel="{ enabled: false }" :show-borders="true" class="color" >
                                        <DxDataGridColumn data-field="afiliado" caption="Afiliado" />
                                        <DxDataGridColumn data-field="plan_nom" caption="Plan" />
                                        <DxDataGridColumn data-field="contrato_nom" caption="Contrato" />
                                        <DxDataGridColumn data-field="fecha_inicio" caption="Fecha Inicio"/>
                                        <DxDataGridColumn data-field="meses_vigencia" caption="Meses.Cob"/>
                                        <DxDataGridColumn data-field="MaximoVitalicio" caption="M.Vitalicio"/>
                                        <DxDataGridColumn data-field="MaximoAnual" caption="M.Anual"/>
                                        <DxDataGridColumn data-field="MaximoEvento" caption="M.Evento"/>
                                        <DxDataGridColumn data-field="fecha_nacimiento" caption="Fecha Nacimiento"/>
                                        <DxDataGridColumn data-field="edad" caption="Edad"/>
                                       </DxDataGrid>
                                    
                                  </div>
                              </vx-card>
                              <!--Beneficios-->
                              <vx-card style="margin-top: 5px" title="Beneficios">
                                  <div class="con-tab-ejemplo">
                                    <DxDataGrid  :data-source="Beneficios"  :load-panel="{ enabled: false }" :show-borders="true" class="color" :paging="{ pageSize: 5 }" >
                                      <DxDataGridColumn data-field="descripcion" caption="Descripción"/>
                                      <DxDataGridColumn data-field="beneficio" caption="B/E"/>
                                      <DxDataGridColumn data-field="periodoesperameses" caption="Meses Carencia"/>
                                      <DxDataGridColumn data-field="limiteveces" caption="Limite Veces"/>
                                      <DxDataGridColumn data-field="limitemonto" caption="Limite Montoss"/>
                                      <DxDataGridDxSearchPanel :visible="true" :highlight-case-sensitive="true" />
                                    </DxDataGrid>
                                 
                                  </div>
                              </vx-card>
                          </vs-tab>
                          <!--Datos Autorizaciones -->
                          <vs-tab label="Autorizaciones" @click="SubConsulta().Consultas()" icon="list_alt">
                              <div v-show="mostrarDiv">
                                  <vx-card title="Autorizaciones">
                                      <div>
                                          <vx-card>
                                              <vs-list-header title="Montos a pagar por el afiliado" color="primary"></vs-list-header>
                                              <div style="display: flex; flex-wrap: wrap;" >
                                                  <div class="imput" style="display: flex; flex-wrap: wrap;">
                                                      <div style="display: flex; flex-wrap: wrap;" class="p-2">
                                                          <vs-input v-model="total" disabled label="Autorizado"></vs-input>&nbsp;
                                                          <vs-input v-model="declinado" disabled label="Declinado"></vs-input>&nbsp;
                                                          <vs-input v-model="totalGeneral" disabled label="Total"></vs-input>&nbsp;
                                                      </div>
                                                  </div>
                                                  <div class="Bottones" style="display: flex; flex-wrap: wrap;" >
                                                    <div class="p-2 w-full sm:w-1/3 md:w-1/3 lg:w-1/3 xl:w-1/3 div-button" v-for="(item, index) in buttons" v-bind:key="index">
                                                      <vs-button class="buttonh" :color=" item.value === 4 ? 'danger' : 'primary'" type="filled" @click="item.click">
                                                        <font-awesome-icon :icon="['fas', item.icon]" class="pr-2" style="font-size: 16px" />
                                                        <span>{{item.name}}</span>
                                                      </vs-button>
                                                    </div>
                                                </div>
                                              </div>
                                          </vx-card>
                                          <vx-card style="margin-top: 5px">
                                              <div class="continer" style="display: flex">
                                                  <div class="Izquierda2">
                                                      <vs-table2 :data="Autorizaciones" max-items="3"  class="mt-0 mb-0 OverFlow colors">
                                                          <template slot="thead">
                                                              <th width="50px">Aut</th>
                                                              <th width="80px">Código</th>
                                                              <th width="350px">Producto</th>
                                                              <th width="40px">W</th>
                                                              <th width="80px" >Precio</th>
                                                              <th width="180px" >Motivo Declinación</th>
                                                          </template>
                                                          <template slot-scope="{ data }">
                                                              <tr :key="indextr" v-for="(tr, indextr) in data">
                                                                  <vs-td2>
                                                                      <vs-checkbox v-model="tr.Autorizado" color="success" @change="handleCheckboxChange(tr)"></vs-checkbox>
                                                                  </vs-td2>
                                                                  <vs-td2>
                                                                      <label>{{ tr.Producto }} </label>
                                                                  </vs-td2>
                                                                  <vs-td2>
                                                                      <label>{{ tr.NombreProducto }} </label>
                                                                  </vs-td2>
                                                                  <vs-td2>
                                                                      <label>{{ tr.warning }} </label>
                                                                  </vs-td2>
                                                                  <vs-td2>
                                                                    <label>{{ tr.Precio }}</label>
                                                                  </vs-td2>
                                                                  <vs-td2>
                                                                      <vs-select :disabled="tr.Autorizado" class="w-full" v-model="tr.no_declinacion" :class="{'is-required': !tr.autorizado && !tr.motivo_declinacion}" width="200px">
                                                                          <vs-select-item :key="motivos.value" :value="motivos.value" :text="motivos.text" v-for="(motivos) in motivos" />
                                                                      </vs-select>
                                                                      <vs-alert v-if="!tr.Autorizado && !tr.no_declinacion" color="danger" icon="new_releases">
                                                                          <span v-if="!tr.Autorizado && !tr.no_declinacion" style="color: red;" class="error-message">Declinación</span>
                                                                      </vs-alert>
                                                                  </vs-td2>
                                                              </tr>
                                                          </template>
                                                      </vs-table2>
                                                  </div>
                                                  <div class="Derecha2">
                                                      <!--Datos Cita-->
                                                      <div style="display: flex">
                                                          <div class="w-full sm:w-1/1 p-1">
                                                              <label>Citas Por Año:</label>
                                                              &nbsp;
                                                              <label style="color: black">
                                                                  {{ Restricciones.CitasAnuales }}</label>
                                                          </div>
                                                          <div class="w-full sm:w-1/1 p-1">
                                                              <label>Citas Anuladas:</label>
                                                              &nbsp;
                                                              <label style="color: black">
                                                                  {{ Restricciones.CitasAcumuladas }}</label>
                                                          </div>
                                                      </div>

                                                      <!--Observaciones -->
                                                      <div style="display: auto">
                                                          <vs-textarea label="Otros" disabled v-model="info.observacion" class="sm:w-full md:w-full lg:w-full xl:w-1/1 m-2" counter="600" />
                                                          <br/>

                                                          <vs-textarea label="Observaciones" v-model="info.ObsercacionesAuto" class="sm:w-full md:w-full lg:w-full xl:w-1/1 m-2" counter="200" />
                                                      </div>

                                                      <div style="display: flex; flex-wrap: wrap; margin-top: 20px; gap:5px; ">

                                                          <DxButton :width="120" text="Autorizar" type="success" @click="Autorizar().AutorizarCargo()" />

                                                          <DxButton :width="120" text="Cancelar" type="danger" @click="cancelarSolicitud()  || changeTab(0)" />

                                                          <DxButton :width="120" text="Cupones" type="default" :disabled="!Autorizaciones.some((item) => item.Autorizado && item.Producto === '313099')" @click="LevantarCupon()" />

                                                          <DxButton :width="120" text="Anulación" type="danger" styling-mode="outlined" @click="popupActivoAunacion = true" />
                                                      </div>
                                                  </div>
                                              </div>
                                          </vx-card>
                                      </div>
                                  </vx-card>
                              </div>
                          </vs-tab>
                      </vs-tabs>
                  </div>
              </vs-tab>
          </vs-tabs>
      </div>

    <!--=========================================================================================================================
    POPUP CUPONES 
    ==========================================================================================================================-->
      <vs-popup classContent="popup-cupon" title="Cupón Medico" :active.sync="popupActivo">
          <vx-card>
              <form>
                  <div class="afiliado">
                      <div style="display: flex">
                          <vs-input disabled label="Fecha Cupón" type="date" v-model="currentDate" class="w-full sm:w-1/6" />&nbsp;
                      </div>
                      <br />
                      <vs-list>
                          <div>
                              <vs-list-header style="background-color: #3498db; color: white" title="Datos Afiliado"></vs-list-header>

                              <vs-list-item style="color: black" title="Afiliado:">
                                  <label>{{ info.idAdiliado }} </label>
                              </vs-list-item>

                              <vs-list-item style="color: black" title="Adhesion:">{{ info.NumeroAdhesion }}
                              </vs-list-item>
                              <vs-list-item style="color: black" title="Cliente:">
                                  {{ info.idCliente }}
                              </vs-list-item>
                              <vs-list-item style="color: black" title="Nombre:">{{ info.Paciente }}
                              </vs-list-item>
                              <vs-list-item style="color: black" title="Código Plan:">{{ info.CodigoPlan }}
                              </vs-list-item>
                              <vs-list-item style="color: black" title="Plan:">{{ info.NombrePlan }}
                              </vs-list-item>
                              <vs-list-header style="background-color: #3498db; color: white" title="Datos Medico"></vs-list-header>

                              <vs-list-item style="color: black" title="Código:">
                                  {{ MedicoSelecionado.Codigo }}
                              </vs-list-item>

                              <vs-list-item style="color: black" title="Nombre Medico:">{{ MedicoSelecionado.Nombre }}
                              </vs-list-item>
                              <vs-list-item style="color: black" title="Especialidad">
                                  {{ MedicoSelecionado.Especialidad }}
                              </vs-list-item>
                              <vs-list-item style="color: black" title="Dirección">
                                  {{ MedicoSelecionado.Direccion }}</vs-list-item>
                          </div>
                      </vs-list>
                  </div>
                  <div style="display: flex">
                      <ValidationProvider name="Codigo" rules="required|numero_entero|numero_min:1" v-slot="{ errors }">
                          <vs-input  :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null" v-model="number0" label="Valor a Cancelar:" />
                      </ValidationProvider>
                  </div>
                  <br/>
                  <vs-textarea label="Observaciones" v-model="info.ObsercacionesCupon" class="sm:w-full md:w-full lg:w-full xl:w-1/1 m-2" counter="200" />
                  <div class="General" style="display: flex">
                      <div class="ButtomGenerar" style="display: flex">
                          <vs-button color="success" @click="consultaNumeroCupon()">Generar</vs-button>&nbsp;
                          <vs-button color="warning" @click="ConsultaListado()">Listado</vs-button>&nbsp;
                          <vs-button @click="popupActivoMedico = true" color="primary">Buscar Médico</vs-button>
                          <vs-button @click="limpiarMedico()" :disabled="!MedicoSelecionado.Codigo" color="danger">Limpiar Médico</vs-button>
                      </div>
                  </div>
              </form>

              <!--Buscar Medico-->

              <vs-popup title="Buscar Médico" :active.sync="popupActivoMedico">
                  <div style="display: flex">
                      <vs-input label="Especialidad" @keyup.enter="ConsultaMedico().MedicoCupon()" class="w-full" v-model="info.Especialidad" />&nbsp;
                      <vs-input label="Nombres" @keyup.enter="ConsultaMedico().MedicoCupon()" class="w-full" v-model="info.Nombre_Medico" />&nbsp;
                      <vs-input label="Apellidos" @keyup.enter="ConsultaMedico().MedicoCupon()" class="w-full" v-model="info.ApellidoMedico" />&nbsp;
                  </div>
                  <vs-divider />
                  <vs-table2 max-items="6" pagination search :data="ResultadosMedico" class="mt-0 mb-0 colors">
                      <template slot="thead">
                          <th>Especialidad</th>
                          <th>Nombres</th>
                          <th>Apellidos</th>
                      </template>
                      <template slot-scope="{ data }">
                          <tr :key="indextr" v-for="(tr, indextr) in data" @click="SeleccionMedico(tr)">
                              <vs-td2>
                                  <label>{{ tr.Especialidad }}</label>
                              </vs-td2>
                              <vs-td2>
                                  <label>{{ tr.Nombre }}</label>
                              </vs-td2>
                              <vs-td2>
                                  <label>{{ tr.Apellido }}</label>
                              </vs-td2>
                          </tr>
                      </template>
                  </vs-table2>
              </vs-popup>

              <!--Buscar Medico-->

              <vs-popup title="Listado de Cupones" :active.sync="popupActivoListodoCupones">
                  <vx-card :title="'Listado de Cupones por Afiliado'">
                      <vs-list-item title="Afiliado:">
                          <h6>{{ DatosListadoCupones.idAfiliado }}</h6>
                      </vs-list-item>
                      <vs-list-item title="Nombre:">
                          <h6>{{ DatosListadoCupones.NombreAfiliado }}</h6>
                      </vs-list-item>
                      <vs-list-item title="Plan:">
                          <h6>{{ DatosListadoCupones.NombrePlan }}</h6>
                      </vs-list-item>
                  </vx-card>
                  <vx-card style="margin-top: 5px">
                      <vs-table2 max-items="3" pagination search :data="ListadoCupones" class="mt-0 mb-0 colors">
                          <template slot="thead">
                              <th>Cupon</th>
                              <th>Status</th>
                              <th>Monto</th>
                              <th>Usuario</th>
                              <th>Fecha</th>
                              <th>Medico</th>
                              <th>Observaciones</th>
                              <th>Accion</th>
                          </template>
                          <template slot-scope="{ data }">
                              <tr :key="indextr" v-for="(tr, indextr) in data">
                                  <vs-td2>
                                      <label>{{ tr.cupon }}</label>
                                  </vs-td2>
                                  <vs-td2>
                                      <label>{{ tr.status }}</label>
                                  </vs-td2>
                                  <vs-td2>
                                      <label>{{ tr.montocoex }}</label>
                                  </vs-td2>
                                  <vs-td2>
                                      <label>{{ tr.usuario }}</label>
                                  </vs-td2>
                                  <vs-td2>
                                      <label>{{ tr.fecha }}</label>
                                  </vs-td2>
                                  <vs-td2>
                                      <label>{{ tr.Medico }}</label>
                                  </vs-td2>
                                  <vs-td2>
                                      <label>{{ tr.observaciones }}</label>
                                  </vs-td2>
                                  <vs-td2>
                                      <vs-button color="warning" type="filled" icon="error" @click="CargarCupon(tr)"></vs-button>
                                  </vs-td2>
                              </tr>
                          </template>
                      </vs-table2>
                  </vx-card>

                  <vx-card v-if="showCard" style=" margin-top: 10px;" title="Eliminar o Inavilitar Cupon">
                      <div style="display: flex;">
                          <div style="margin-right: 20px;">
                              <vs-input label="No.Cupon" v-model="Cuponedit.cupon" type="number" disabled />&nbsp;
                          </div>
                          <div style="display: flex;">
                              <vs-select label="Opciones" v-model="select4" icon="arrow_downward">
                                  <vs-select-item :key="index" :value="item.value" :text="item.text" v-for="item,index in options4" />
                              </vs-select>&nbsp;&nbsp;
                          </div>
                          <div style="margin-top: 20px;">
                              <vs-button color="#229954" @click="CambiarEstadoCupon().EstadoCupon()">Aceptar</vs-button>
                              <button style="background-color:#E74C3C; height: 40px; width: 100px;" class="BotonInicio" @click="CancelarCupon()" type="relief">Cancelar</button>
                          </div>
                      </div>
                  </vx-card>

              </vs-popup>
          </vx-card>
      </vs-popup>

      <!--=========================================================================================================================
    POPUP ANULACION DE Anulacion  
    ==========================================================================================================================-->
      <vs-popup title="Anulación" :active.sync="popupActivoAunacion">
          <vx-card title="Datos Solicitud">
              <div style="display: flex" class="Container">
                  
                  
                  
                  <div style="width: 100%">
                      <div style="display: flex;flex-wrap: wrap; width: 100%; margin-right: 10px" class="sm:w-1/2 lg:w-full xl:w-1/1 m-2"> 
                           <div>
                            <label for="">Solicitud</label>
                            <DxTextBox class="Buttom_color" :disabled="true"  v-model="info.idSolicitud" width="auto" />
                          </div>
                          &nbsp;
                          <div>
                            <label for="">Afiliado</label>
                            <DxTextBox class="Buttom_color" :disabled="true" v-model="info.idAdiliado" width="auto" />
                          </div>
                          &nbsp;
                          <div>
                            <label for="">Paciente</label>
                            <DxTextBox class="Buttom_color"  :disabled="true" v-model="info.Paciente" width="350px" />
                          </div>
                      </div>
                      <br>
                      <div style="display:flex;" class="De">
                        <ValidationProvider name="Motivo" rules="required" v-slot="{ errors }" class="required">
                          <vs-select class="selectExample" style="margin-top: 10px" v-model="selectedMotivo2" width="400px" :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null">
                              <vs-select-item v-for="(motivo, index) in motivos" :key="index" :value="motivo.value" :text="motivo.text" />
                          </vs-select>
                        </ValidationProvider>
                        &nbsp;
                        <vs-button style="margin-top: 10px" color="#E74C3C" type="relief" :disabled="!selectedMotivo2" @click="AnularSolicitud().Anulacion()">
                          Anular Solicitud
                        </vs-button>
                      </div>
                  </div>
                  
                  <vs-input label="Fecha" type="date" v-model="currentDate" disabled class="w-full sm:w-1/6" />&nbsp;
              </div>
          </vx-card>
      </vs-popup>

      <!-- Popup de laboratorios -->
      <DxPopup :visible.sync="mostrarLaboratorio" :width="'85%'" :height="'95%'" :show-title="true" :full-screen="false" :hide-on-outside-click="false" title="Laboratorio" :showCloseButton="true">
          <DxScrollView :scroll-by-conent="true">
              <div>
                  <Laboratorio ref="Laboratorio" :IdCliente="info.idCliente" :IdPaciente="info.idPaciente" />
              </div>
          </DxScrollView>
      </DxPopup>

      <!-- Popup de exámenes -->
      <DxPopup :visible.sync="mostrarExamenes" :width="'85%'" :height="'95%'" :show-title="true" :full-screen="false" :hide-on-outside-click="false" title="Exámenes" :showCloseButton="true">
          <DxScrollView :scroll-by-conent="true">
              <div>
                  <Examenes ref="Examenes" :IdPaciente="info.idPaciente" />
              </div>
          </DxScrollView>
      </DxPopup>

  </div>
</template>

<script>
import Laboratorio from '../HISTORIALCLINICO/Laboratorio.vue'
import Examenes from '../HISTORIALCLINICO/Examenes.vue'

export default {
   components:{
        Laboratorio,
        Examenes
  }, 
  data() {
    return {
      colorx:'success',
      // =========================================================================================================================
      //  DATOS
      // =========================================================================================================================
      isDiv1Hidden: false, // Estado reactivo para mostrar u ocultar div1
      //--Cambio de Tabs--// 
      activeTab: 0, 
      numberOfTabs: 2,

      //--Operacion--// 
      total: 0,
      declinado: 0,
      totalGeneral: 0,

      //--Activacion Popup--//
      popupActivo: false,
      popupActivoMedico: false,
      popupActivoSinestrilidad: false,
      popupActivoListodoCupones: false,
      popupActivoAunacion: false,
      popupEilimarCupon:false,
      popupActivoprueba:false,

      //--Selecciones--//
      selectedMotivo: null,
      selectedMotivo2: null,
      selectedAction:null,
      select4:2,
      
      //--Datos Extra--//
      showCard:false,
      number0:0.00,
      isButtonDisabled: false, // Variable para controlar el estado del botón

      //Dataos Para Cupon
      MontoSiniestralidad: "150",
      isActive: false,

      //Datos 
      mostrarLaboratorio: false,
      mostrarExamenes: false,

      mostrarDiv: true,

      info: {
        //DAtos Afiliado1
        idSolicitud: null,
        idAdiliado: null,
        idPaciente:null,
        Paciente: null,
        idCliente: null,
        idContrato: null,
       
        //Datos Solicitud
        FechaIngreso: null,
        NombreMedico: null,
        medico: null,
        observacion: null,
        idCita:null,
        //ConsultaMedicoCupon
        Nombre_Medico: null,
        ApellidoMedico: null,
        Especialidad: null,
        //NumeroAdhesion
        NumeroAdhesion: null,
        NombrePlan: null,
        CodigoPlan: null,

        //Datos Cupon
        NUMERO: null,
        motivo_declinacion:null,
        ObsercacionesAuto: null,
        ObsercacionesCupon:null 
      },

      //Datos tablas
      Solicitudes: [],
      Beneficios: [],
      Autorizaciones: [],
      Diagnosticos: [],
      plan: [],
      ResultadosMedico: [],
      CuponGenerado: [],
      ListadoCupones: [],
      selectedItems:[],

      no_declinacion:5,  
      
      mostrarPaciente: false,

      motivos: [
        { value: "1",   text: "No aplica medicamento" },
        { value: "2",   text: "Sin cobertura del seguro" },
        { value: "3",   text: "Uso exclusivo hospital" },
        { value: "4",   text: "Tiene otra solicitud autorizada" },
      ],

      options4:[
        {text: 'Inactivar', value: 'I'},
        {text: 'Eliminar', value: 'E'}
      ],

      MedicoSelecionado: {
        Codigo: "",
        Nombre: "",
        Especialidad: "",
        Direccion: "",
      },

      DatosListadoCupones: {
        idAfiliado: "",
        NombreAfiliado: "",
        NombrePlan: "",
      },

      permisos: {
        Habilitar: false,
      },

      Restricciones: {
        CitasAcumuladas: "",
        CitasAnuales: "",
        CitasAutorizadas: "",
        PermiteCita: "",
        Permitidas: "",
        Restriccion: "",
        ValidaCitas: "",
      },

      Cuponedit:{
        cupon:"",
      },


     buttons: [{
                    name: 'Laboratorios',
                    icon: 'vial-circle-check',
                    value: 2,
                    click: () => {
                        this.mostrarLaboratorio = true
                    }
                }, {
                    name: 'Exámenes',
                    icon: 'radiation',
                    value: 3,
                    click: () => {
                        this.mostrarExamenes = true
                    }
                },
                {
                    name: 'Diagnosticos',
                    icon: 'file-pdf',
                    value: 4,
                    click: () => {
                        this.CargarPDF()
                    }
                },
            ],
          

    }; 
    
  },

  computed: {
    currentDate() {
      return new Date().toISOString().substring(0, 10);
    },

    sesion() {
      return this.$store.state.sesion
    },

    global() {
      return this.$store.state.global
    }
  },


  watch: {
    Autorizaciones: {
      handler() {
        this.calcularTotales();
      },
     },
  },

  methods: {
    // ==========================================================
    //  COSULTA DATOS SOLICITUDES
    // ==========================================================

    toggleDiv1() {
      this.isDiv1Hidden = !this.isDiv1Hidden; // Alterna el estado
    },
    
     Consulta: function () {
      return {
         ConsultaSolicitud: () => {
          this.axios
            .post("/app/v1_autorizaciones/ConsultaAutorizaciones", {
              Operacion: "CONSULTASOLICITUD",
            })
            .then((resp) => {
              if (resp.data.codigo == '0') {
                  this.Solicitudes = resp.data.json;
             }         
            });
        },
      };
    },

    // ==========================================================
    //  BUSCAR AFILIADO POR CODIGO
    // ==========================================================
   
    ConsultaInfo: function () {
      return {
        ConsultaDetalle: (valor) => {
          this.axios
            .post("/app/v1_autorizaciones/ConsultaDatosAfiliado", {
              Operacion: "CONSULTADATOS",
              Afiliado: valor.Afiliado,
            })
            .then((resp) => {
              // Verifica si la respuesta tiene datos
              if (resp.data.json) {
                this.$map({
                  objeto: this.info,
                  respuestaAxios: resp,
                });

                
                // Solo si la consulta anterior fue exitosa, continuamos con la siguiente
                this.ConsultaDiagnostico().Diagnostios(valor);
              } 
            })
            .catch(() => {
            
              let Afiliado = valor.Afiliado ; // Este es el valor que quieres mostrar

              this.$vs.dialog({
                  type: 'confirm',
                  color: '#e74c3c',
                  title: 'Error afiliado sin Paciente',
                  acceptText: 'Regresar',
                  text: `Este Afiliado no tiene Paciente Relacionado, Por favor Sincronizar con Paciente.
                        Afiliado: ${Afiliado}`, // Usamos interpolación de cadenas para incluir el valor
                  clientWidth: 100,
                  accept: () => {
                    this.regresar();
                  },
                  cancel: () => {
                    this.regresar();
                  }
              });

            });
        },
      };
    },


    
    // ==========================================================
    //  BUSCAR LAS SOLICITUD INFO
    // ==========================================================
    ConsultaDatos: function () {
      return {
        ConsultaDatosSolicitud: (valor) => {
          this.axios
            .post("/app/v1_autorizaciones/ConsultaDatosAutorizacion", {
              Operacion: "CONSULTASOLICITUDAUTO",
              Solicitud: valor.idSolicitud,
            })
            .then((resp) => {
              if (resp.data.json) {
                this.$map({
                  objeto: this.info,
                  respuestaAxios: resp,
                });
              }
            });
        },
      };
    },
    // ==========================================================
    //  BUSCA EL O LOS DIAGNOSTIVOS
    // ==========================================================
    ConsultaDiagnostico: function () {
      return {
        Diagnostios: (valor) => {
          this.axios
            .post("/app/v1_autorizaciones/ConsultaDiagnosticos", {
              Operacion: "CONSULTADIAGNOSTICO",
              Solicitud: valor.idSolicitud,
            })
            .then((resp) => {
              if (resp.data.codigo == "0") [(this.Diagnosticos = resp.data.json)];
            });
            this.ConsultaPlan().PlanMedico(valor);
       
        },
      };
    },
    // ==========================================================
    //  BUSCA LOS CARGOS A AUTORIZAR
    // ==========================================================
    ConsultaCargos: function () {
      return {
        Autorizaciones: (valor) => {
          this.axios
            .post("/app/v1_autorizaciones/ConsultaDeCargosPeAutorizar", {
              Operacion: "CONSULTADEAUTORIZACIONES",
              Solicitud: valor.idSolicitud,
            })
            .then((resp) => {
              if (resp.data.codigo == "0") {
                this.Autorizaciones = resp.data.json;
                for(const i of this.Autorizaciones)
                {
                  i.Autorizado = false,
                  i.status_analista = 'D',
                  i.no_declinacion = null
                }
              }
              this.ConsultaInfo().ConsultaDetalle(valor);
            });
        },
      };
    },
    // ==========================================================
    //  BUSCAR DATOS DEL PLAN
    // ==========================================================
    ConsultaPlan: function () {
      return {
        PlanMedico: (valor) => {
          this.axios
            .post("/app/v1_autorizaciones/PlanMedico", {
              Operacion: "CONSULTAPLANMEDICO",
              Afiliado: valor.Afiliado,
            })
            .then((resp) => {
              if (resp.data.codigo == "0") {
                this.plan = resp.data.json
              }
              this.ConsultaBeneficios().Beneficios(valor);
            });
        },
      };
    },
    // ==========================================================
    //  BUSCA LOS BENEFICIOS
    // ==========================================================
    ConsultaBeneficios: function () {
      return {
        Beneficios: (valor) => {
          this.axios
            .post("/app/v1_autorizaciones/Beneficios", {
              Operacion: "BENEFICIOS",
              Afiliado: valor.Afiliado,
            })
            .then((resp) => {
              if (resp.data.codigo == "0") {
                this.Beneficios = resp.data.json;
              }
              this.ConsultaDatos().ConsultaDatosSolicitud(valor);
            });
        },
      };
    },
    // ==========================================================
    //  RESTRICCIONES SP
    // ==========================================================
    ConsultaRestricciones() {
      return {
        Restriccion: () => {
          this.axios
            .post("/app/v1_autorizaciones/Restricciones", {
              CodigoPlan: this.info.CodigoPlan,
              NumeroAdhesion: this.info.NumeroAdhesion,
              Afiliado: this.info.idAdiliado,
              IdCliente: this.info.idCliente,
            })
            .then((resp) => {
              if (resp.data.codigo == "0") {
                this.$map({
                  objeto: this.Restricciones,
                  respuestaAxios: resp,
                });
                this.MotivosAnulacion().Motivos();
              }
            });
        },
      };
    },

    // =========================================================================================================================
    //  FUNCIONES -- CUPONES
    // =========================================================================================================================

    ConsultaMedico: function () {
      return {
        MedicoCupon: () => {
          this.axios
            .post("/app/v1_autorizaciones/ConsultaMedicoCupon", {
              Operacion: "BUSQUEDAMEDICO",
              NombreMedico: this.info.Nombre_Medico,
              ApellidoMedico: this.info.ApellidoMedico,
              Especialidad: this.info.Especialidad,
            })
            .then((resp) => {
              if (resp.data.codigo == "0") {
                this.ResultadosMedico = resp.data.json;
              }
            });
        },
      };
    },
    // ==========================================================
    // CARGAR MEDICO CUPON  
    // ==========================================================
    SeleccionMedico(value) {
      if (value != null) {
        this.MedicoSelecionado.Codigo = value.Codigo;
        this.MedicoSelecionado.Nombre = value.Nombre;
        this.MedicoSelecionado.Especialidad = value.Nombre_Especialidad;
        this.MedicoSelecionado.Direccion = value.Direccion;
        this.popupActivoMedico = false;
        this.ResultadosMedico = [];
        this.info.Nombre_Medico = null;
        this.info.ApellidoMedico = null;
        this.info.Especialidad = null;
      }
    },
    // ==========================================================
    //  GENERAR CUPON 
    // ==========================================================
  consultaNumeroCupon() {
    if (!this.MedicoSelecionado.Codigo) {
      this.$vs.dialog({
        type: 'alert',
        color: '#e74c3c',
        title: 'Error',
        acceptText: 'OK',
        text: 'El código del médico no puede estar vacío. Seleccione un médico válido antes de generar el cupón.',
        buttonCancel: 'border',
        accept: () => {}
      });

      return;
    }

    this.axios.post("/app/v1_autorizaciones/ConsultaDatosAfiliado", {
        Operacion: "CONSULTACUPON",
      })
      .then((resp) => {
        if (resp.data.json) {
          this.$map({
            objeto: this.info,
            respuestaAxios: resp,
          });

          this.$vs.dialog({
            type: 'alert',
            color: '#27ae60',
            title: 'Cupón Generado',
            acceptText: 'Aceptar',
            text: `El número de cupón es: ${this.info.NUMERO}`,
            buttonCancel: 'border',
            accept: () => {}
          });


          // Inserta el código del cupón en el textarea
          this.insertarCodigoCupon(this.info.NUMERO);
        }
        this.GenerrarCupom().Cupon();
      })
      .catch(() => {
        this.$vs.dialog({
          type: 'alert',
          color: '#e74c3c',
          title: 'Error',
          acceptText: 'Aceptar',
          text: 'No se pudo Generar el número de cupón.',
          buttonCancel: 'border',
          accept: () => {}
        });


        this.$vs.dialog({
            type: 'alert',
            color: '#e74c3c',
            title: 'Error',
            acceptText: 'Aceptar',
            text: 'No se pudo Generar el número de cupón.',
            buttonCancel: 'border',
            accept: () => {},
        })

        return;
      });
  },

  GenerrarCupom() {
    return {
      Cupon: () => {
        this.axios
          .post("/app/v1_autorizaciones/Generar_Cupon", {
            Operacion: "GENERAR_CUPON",
            cupon: this.info.NUMERO,
            Adhesion: this.info.NumeroAdhesion,
            idAjeno: this.MedicoSelecionado.Codigo,
            idCliente: this.info.idCliente,
            Afiliado: this.info.idAdiliado,
            Valor: this.number0,
            MontoSiniestralidad: this.MontoSiniestralidad,
            observaciones: this.info.ObsercacionesCupon
          })
          .then((resp) => {
            if (resp.data.codigo == "0") {
              this.CuponGenerado = resp.data.json;
              this.popupActivo = false;
              this.MedicoSelecionado = {};
              this.info.ObsercacionesCupon = null;

          
             
            }
          })
          .catch(() => {
            this.$vs.dialog({
              type: 'alert',
              color: '#e74c3c', // Puedes mantener el color rojo similar al de Swal
              title: 'Error',
              text: 'No se pudo generar el cupón. Revisar el Médico!',
              acceptText: 'OK',
              buttonCancel: 'border',
              accept: () => {}
          });
          return;

          });
      },
    };
  },

  insertarCodigoCupon(codigoCupon) {
    // Verifica si ya existe información en el campo de observaciones
    if (this.info.ObsercacionesAuto) {
      // Si existe, agrega el código del cupón al texto existente
      this.info.ObsercacionesAuto += ` | Código del cupón: ${codigoCupon}`;
    } else {
      // Si no existe, solo coloca el código del cupón
      this.info.ObsercacionesAuto = `Código del cupón: ${codigoCupon}`;
    }
  },

    LevantarCupon(){
      this.number0 = 0;
      this.popupActivo = true
    },
    
    // ==========================================================
    //  CONSULTA LISTADOS 
    // ==========================================================
    ConsultaDatosListados: function () {
      return {
        Listados: () => {
          this.axios
            .post("/app/v1_autorizaciones/ListadoCupones", {
              Operacion: "LISTADO_CUPONES",
              Afiliado: this.info.idAdiliado,
            })
            .then((resp) => {
              if (resp.data.codigo == "0") {
                this.ListadoCupones = resp.data.json;
              }
           
            })
          this.axios
            .post("/app/v1_autorizaciones/ListadoCupones", {
              Operacion: "LISTADO_DATOS",
              Afiliado: this.info.idAdiliado,
            })
            .then((resp) => {
              if (resp.data.json) {
                this.$map({
                  objeto: this.DatosListadoCupones,
                  respuestaAxios: resp,
                });
              
              }
            });
        },
      };
    },
    // ==========================================================
    //  OPCIONES CUPON 
    // ==========================================================
    ConsultaListado() {
      this.ConsultaDatosListados().Listados();
      this.popupActivoListodoCupones = true;
    },
    CargarCupon(cuponData) {
      this.Cuponedit = cuponData; 
      this.showCard = true; 
    },
    CancelarCupon(){
      this.showCard = false;
    },
    // ==========================================================
    //  CAMBIO DE ESTADO CUPON 
    // ==========================================================
    CambiarEstadoCupon: function(){
      return{
        EstadoCupon:()=>{
          this.axios.post("/app/v1_autorizaciones/EliminarOInavilitarCupon",{
              Operacion:'UPDATESTATUSCUPONES',
              Afiliado: this.info.idAdiliado,
              Cupon: this.Cuponedit.cupon,
              NuevoStatus: this.select4
          })
          .then(()=>{
            this.ConsultaDatosListados().Listados()
            this.CancelarCupon()
          })
        }
      }
    },
    // =========================================================================================================================
    //  CAMBIO DE TAB
    // =========================================================================================================================
    changeTab(index) {
      this.activeTab = index;


    },
  
    // ==========================================================
    //  CALCULAR PRECIOS 
    // ==========================================================
    calcularTotales() {
      this.total = 0;
      this.declinado = 0;
      this.totalGeneral = 0;

      for (const Autorizacion of this.Autorizaciones) {
        if (Autorizacion.Autorizado) {
          this.total += parseFloat(Autorizacion.Precio);
        } else {
          this.declinado += parseFloat(Autorizacion.Precio);
        }
        this.totalGeneral += parseFloat(Autorizacion.Precio);
      }
      // Format the totals after the loop
      this.total = this.total.toLocaleString("es-GT", {
        style: "currency",
        currency: "GTQ",
      });
      this.declinado = this.declinado.toLocaleString("es-GT", {
        style: "currency",
        currency: "GTQ",
      });
      this.totalGeneral = this.totalGeneral.toLocaleString("es-GT", {
        style: "currency",
        currency: "GTQ",
      });
    },
    // =========================================================================================================================
    //  EJECUCION GENERAL
    // =========================================================================================================================
    ConsultaGeneral: function () {
      return {
        EjecutarConsultas: (valor) => {
          this.UsuarioTrabajando().Usuario(valor)
          this.info.idSolicitud = valor.idSolicitud
         
        },  
      };
    },

    //SUBCONSULTAS --
    SubConsulta: function () {
      return {
        Consultas: () => {
          this.ConsultaRestricciones().Restriccion();
        },
      };
    },
        // INSERTA VALOR USUARIO TABAJANDO POR 
    UsuarioTrabajando(){
      return{
        Usuario:(valor) =>{
          this.axios
            .post("/app/v1_autorizaciones/UsuarioTrabajando", {
              Operacion: "USUARIO_TRABAJANDO",
              Solicitud: valor.idSolicitud,
              configuraciones: {
                  _NOTIFICACIONES: false 
              }
            })
            .then((resp) =>{
              if(resp.data.codigo === '0'){
                this.Consulta().ConsultaSolicitud(); 
              }
              this.ConsultaCargos().Autorizaciones(valor)
            })
            .catch((error) => {
              if(error.response && error.response.status === 400){
                this.mostrarPopupConfirmacion()
              }
            })
            this.Consulta().ConsultaSolicitud(); 
        }
      }
    },
    //MODAL QUE NOS PERMITE REALIZAR LA CONFIRMACION
    mostrarPopupConfirmacion() {
      // Primer diálogo
      this.$vs.dialog({
          type: 'confirm', // Tipo de diálogo de confirmación
          color: '#e74c3c', // Color del diálogo
          title: 'Advertencia (O_o)', // Título vacío
          text: "Error, Esta Solicitud ya está siendo trabajada por algún otro Usuario. ¡Verificar!", // Mensaje
          acceptText: 'Ingresar', // Texto para el botón de aceptar
          cancelText: 'Regresar', // Texto para el botón de cancelar
          buttonCancel: 'border', // Estilo del botón cancelar (si es necesario)
          clientWidth: 100, // Ajuste del tamaño del diálogo
          accept: () => {
              // Acción al hacer clic en "Ingresar" (confirmación de la primera ventana)
              this.$vs.dialog({
                  type: 'confirm', // Segundo diálogo de confirmación
                  color: '#e74c3c', // Color del diálogo
                  title: '¿Estás seguro?', // Título del segundo diálogo
                  text: "Recuerda que al ingresar sacarás a otro usuario que está trabajando esto.", // Mensaje del segundo diálogo
                  acceptText: 'Sí, continuar', // Texto para el botón de aceptar
                  cancelText: 'Cancelar', // Texto para el botón de cancelar
                  buttonCancel: 'border', // Estilo del botón cancelar
                  clientWidth: 100, // Ajuste del tamaño del diálogo
                  accept: () => {
                      // Acción al hacer clic en "Sí, continuar"
                      this.Actualizar().Forzada();
                      this.Consulta().ConsultaSolicitud();
                  },
                  cancel: () => {
                      // Acción al hacer clic en "Cancelar"
                      this.ErrorRegresar();
                  }
              });
          },
          cancel: () => {
              // Acción al hacer clic en "Regresar"
              this.ErrorRegresar();
          }
      });
  },







    //ACTUALIZA A LA MALA
    Actualizar(){
      return {
         Forzada:() =>{
          this.axios
            .post("/app/v1_autorizaciones/UsuarioTrabajandoForzado", {
              Operacion: "UPDATE_USUARIO_TRABAJANDO",
              Solicitud: this.info.idSolicitud,
              configuraciones: {
                  _NOTIFICACIONES: false 
              }
            })
            this.Consulta().ConsultaSolicitud();
          }
       }
    },

   //LIMPIA EL CAMPO DE TRABAJANDO POR -- PARA 
   RegressionUsuario(){
      return {
        Regression:() => {
          this.axios
            .post("/app/v1_autorizaciones/RegresionUsuario", {
              Operacion: "REGRESION_USUARIO_TRABAJANDO",
              Solicitud: this.info.idSolicitud,
              configuraciones: {
                  _NOTIFICACIONES: false 
              }
            })
            this.Consulta().ConsultaSolicitud()  
          }
      }
    },


    Regresion() {
      return {
        Regre: (valor) => {
          this.axios
            .post("/app/v1_autorizaciones/RegresionUsuario", {
              Operacion: "REGRESION_USUARIO_TRABAJANDO",
              Solicitud: valor.idSolicitud,
              configuraciones: {
                  _NOTIFICACIONES: false 
              }
            })
            .then((response) => {
              // Si la solicitud es exitosa, ejecuta las consultas
              if (response.status === 200) {
                this.Consulta().ConsultaSolicitud();
              }
            })
        }
      };
    },

    //CANCELA SOLICITUD - LIMPIA LOS CAMPOS GENERALES --BASE DE DATOS-- CAMBIA A NULO   

    async cancelarSolicitud() {
        await this.RegressionUsuario().Regression();
        this.limpiarCampos();
        // Asigna los datos obtenidos a la propiedad reactiva Solicitudes
        const resultado = await this.Consulta().ConsultaSolicitud();
        this.Solicitudes = resultado; // Asegúrate de que el método `ConsultaSolicitud` retorna un array válido
    },
    // ==========================================================
    //  Listados Motivacion 
    // ==========================================================
    MotivosAnulacion: function () {
      return {
        Motivos: () => {
          this.axios
            .post("/app/v1_autorizaciones/MotivosAnulacion", {
              Operacion: "MOTIVOSANULACION_DECLINACION",
            })
            .then((resp) => {
              if (resp.data.codigo == "0") {
                this.Motivo = resp.data.json;
              }
             
            });
        },
      };
    },
    // =========================================================================================================================
    //  ANULACION AUTORIZACION
    // =========================================================================================================================
    AnularSolicitud: function () {
      return {
        Anulacion: () => {
          this.axios
            .post("/app/v1_autorizaciones/AnulacionSolicitud", {
              Operacion: "ANULACION",
              no_motivo_anulacion: this.selectedMotivo2,
              Solicitud: this.info.idSolicitud,
            })
            .then(() => {
              this.limpiarCampos();
              this.Consulta().ConsultaSolicitud();
              this.popupActivoAunacion = false;
            });
        },
      };
    },
    // =========================================================================================================================
    //  AUTORIZACION 
    // =========================================================================================================================
    handleCheckboxChange(row) {
      // Inicializa el status_analista basándose en la autorización
      if (row.Autorizado === true) {
        row.status_analista = 'A';

        if (row.Autorizado === true && row.no_declinacion) {
          // Usamos el diálogo de Vue (this.$vs.dialog) en lugar de Swal.fire
          this.$vs.dialog({
            type: 'confirm',
            color: '#27ae60',
            title: '¿Está seguro?',
            text: 'Desea autorizar este cambio.',
            acceptText: 'Sí, autorizar',
            cancelText: 'Cancelar',
            buttonCancel: 'border',
            clientWidth: 100,
            accept: () => {
              // Si el usuario acepta, autoriza y limpia el campo no_declinacion
              row.status_analista = 'A';
              row.no_declinacion = null;
            },
            cancel: () => {
              // Si el usuario cancela, limpia no_declinacion y desautoriza
              row.no_declinacion = '';
              row.Autorizado = false;
            }
          });
        }
      } else if (row.Autorizado === false) {
        row.status_analista = 'D'; // Declinado
      }

      // Actualiza selectedItems basado en el estado de Autorizado
      const index = this.selectedItems.findIndex(item => item.id === row.id);

      if (row.Autorizado) {
        // Si no está en selectedItems, lo agregamos
        if (index === -1) {
          this.selectedItems.push(row);
        }
      } else {
        // Si está en selectedItems, lo eliminamos
        if (index !== -1) {
          this.selectedItems.splice(index, 1);
        }
      }

      // Calcula los totales después de actualizar selectedItems
      this.calcularTotales();
    },


    // ==========================================================
    //  AUTORIZACION DE CARGOS  
    // ==========================================================
    Autorizar: function () {
    // Validar que todos los selects estén completos si los checkboxes están deshabilitados
    const invalidItems = this.Autorizaciones.filter(tr => !tr.Autorizado && !tr.no_declinacion);
    if (invalidItems.length > 0) {
      this.$vs.notify({
        text: 'Debe seleccionar un motivo de declinación.',
        iconPack: 'feather',
        icon: 'icon-alert-circle',
        color: 'danger'
      });
      return;
    }

    // Mostrar la confirmación de autorización
    this.$vs.dialog({
        type: 'confirm',
        color: '#27ae60', // Verde similar al botón de confirmar en Swal
        title: '¿Está seguro de autorizar?',
        text: 'Esta acción no se puede deshacer.',
        acceptText: 'Sí, autorizar',
        cancelText: 'No, cancelar',
        accept: () => {
            // Ejecutar la acción de autorización si el usuario confirma
            this.axios.post('/app/v1_autorizaciones/AutorizarSolicitud', {
                Operacion: "AUTORIZACION",
                Solicitud: this.info.idSolicitud,
                Notas: this.info.ObsercacionesAuto,
                AutoCargos: this.Autorizaciones
            })
            .then((resp) => {
                if (resp.data.codigo === 0) {
                    this.Consulta().ConsultaSolicitud();
                    this.Mensajito();
                    this.LimpiarCamposCupon();
                    this.limpiarCampos();
                    this.changeTab(0);
                    this.$vs.notify({
                        title: 'Autorización Correcta.',
                        iconPack: 'feather',
                        icon: 'icon-alert-circle',
                        color: 'success'
                    });
                }

                this.info.ObsercacionesAuto = null;
            })
            .catch(() => {
                this.$vs.notify({
                    icon: 'icon-alert-circle',
                    text: 'Hubo un error al autorizar la solicitud.',
                    iconPack: 'feather',
                    color: 'danger'
                });
            });
        }
    });
},


    Mensajito() {
        if (this.info.idCita === null || this.info.idCita === "0") {
            this.$vs.notify({
                title: 'Notificación',
                text: 'No se enviará el Mensaje SMS Ya que el Afiliado no tiene Número valido o bien no tubo cita previa.',
                iconPack: 'feather',
                icon: 'icon-alert-circle',
                color: 'warning'
            });
            return; // Detenemos la ejecución del método
        }

        this.axios.post('/app/v1_autorizaciones/EnvioMensaje', {
            Operacion: "ENVIO_MENSAJE",
            IdCliente: this.info.idCliente,
            configuraciones: {
                _NOTIFICACIONES: false 
            }
        })
       
    },

    // =========================================================================================================================
    //  LIMPIAR CAMPOS
    // =========================================================================================================================

    limpiarCampos() {
      //Campos
      this.changeTab(0);
      this.info.FechaIngreso = null;
      this.info.NombreMedico = null;
      this.info.medico = null;
      this.info.idSolicitud = null;
      this.info.idAdiliado = null;
      this.info.Paciente = null;
      this.info.idCliente = null;
      this.info.idContrato = null;
      this.info.observacion = null;
      this.info.motivo_declinacion = null;
      this.info.ObsercacionesAuto = null;

      //Tablas
      this.Diagnosticos = [];
      this.Autorizaciones = [];
      this.selectedItems = [];
      this.plan = [];
      this.Beneficios = [];
      this.Consulta().ConsultaSolicitud();
    },

    ErrorRegresar(){
      this.limpiarCampos()
      this.LimpiarCamposCupon()
    },

    regresar(){
      this.cancelarSolicitud(),  
      this.limpiarCampos()
      this.Consulta().ConsultaSolicitud();
    },

    CargarPDF() {
            this.$reporte_modal({
                Nombre: 'Historial',
                Opciones: {
                    IdCliente: this.info.idCliente
                }
      })
    },

  
    LimpiarCamposCupon(){
           this.info.NUMERO = null
           this.info.NumeroAdhesion = null
           this.MedicoSelecionado.Codigo = null
           this.info.idCliente= null
           this.info.idAdiliado= null 
           this.CuponGenerado = []
           this.isButtonDisabled = true
           this.info.NumeroAdhesion = null, 
           this.MedicoSelecionado.Codigo = null,
           this.info.idCliente = null,
           this.info.idAdiliado = null,
           this.number0 = null,
           this.IdServicioCoex= null ,
           this.MontoSiniestralidad= null,
           this.info.ObsercacionesCupon= null,
           this.status= null,
           this.MedicoSelecionado = {}
    },
  

    limpiarMedico(){
      this.MedicoSelecionado = {}
      this.$vs.notify({
            icon: 'icon-alert-circle',
            title: 'Médico Limpiado.',
            iconPack: 'feather',
            color: 'danger'
      });
    }
  },


  mounted() {
    this.Consulta().ConsultaSolicitud();
    
    setTimeout(() => {
            this.$validar_funcionalidad('/SASI/SASI003', 'HABILITAR', (d) => {
                this.permisos.Habilitar = d.status
                
            })
        }, 1000)
  },

};

</script>

<style scoped>

.detalle-botones {
    display: flex;
    justify-content: center; /* Centra los botones horizontalmente */
}

.notificacion2 {
    text-align: center;
    font-size: 70px;
    cursor: pointer;
    position: relative;
    margin: 20px;
}

.notificacion2:hover {
    color: black;
}

.notificacion2 .titulo {
    font-weight: bold;
    font-size: 20px;
    position: relative;
    top: -5px;
}

.Izquierda {
  padding: 5px;
  border-radius: 5px;
}

.Izquierda2 {
  width: 130%;
  padding: 5px;
  border-radius: 5px;
}

.Derecha {
  width: 10%;
  padding: 2px;
  border-radius: 2px;
}

.Derecha2 {
  width: 70%;
  padding: 5px;
  border-radius: 5px;
}

.card {
  margin-top: 5px;
}

.notificacion {
  text-align: center;
  font-size: 25px;
  height: 40px;
  color: rgba(var(--vs-primary), 0.7);
  margin-left: 10px;
  cursor: pointer;
  position: relative;
}

.notificacion:hover {
  color: rgba(var(--vs-primary), 1);
}

.notificacion .titulo {
  font-weight: bold;
  font-size: 11px;
  position: relative;
  top: -5px;
}



.Left {
  width: 100%;
  padding: 5px;
  border-radius: 5px;
}

.Ringht {
  width: 100%;
  padding: 5px;
  border-radius: 5px;
}

.Medico {
  margin-top: 10px;
}

.Mid {
  margin-top: 10px;
  width: 100%;
  padding: 5px;
  border-radius: 5px;
}

.Mid2 {
  width: 100%;
  padding: 5px;
  border-radius: 5px;
}

.ButtomGenerar {
  width: 100%;
  justify-content: center;
}

.ButtomCupon {
  width: 100%;
  text-align: right;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  /* Fondo oscuro semi-transparente */
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 5px;
}

.BotonInicio {
border-radius: 5px;
justify-content: right;

}

button {
  margin-right: 10px;
  padding: 10px 20px;
  background-color: green;
  color: white;
  border: none;
  cursor: pointer;
}

button:hover {
  background-color: #52BE80;
}


.buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

.buttonh {
    height: 50px;
    width: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px !important;
}

.buttonh:hover {
    background-color: blue !important;
}

.div-button {
    display: flex;
}

</style>

<style>

.OverFlow .contenedor-tabla
{
  overflow-y: auto !important;
  max-height: 400px !important; 
}

.color .dx-datagrid-headers {
  background-color:#2980b9 !important;
  color: white !important;
}

.swal2-container {
    z-index: 55000 !important
}

div .vs-popup{
  width: 500px  !important;
}

.colors thead {
background-color:#2980b9 !important;
color: white !important;
}

.btn-iniciar {
  color: #4caf50  !important;
}
.btn-desbloquear {
  color: #f44336  !important;
}

.Buttom_color {
  color: black !important;
}

</style>


