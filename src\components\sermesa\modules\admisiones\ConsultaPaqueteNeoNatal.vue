<!-- ConsultaPaqueteNeoNatal.vue -->

<template>
    <div id="app">

        <div class="datos_paquete_neonatal" v-if="ListaPaquetesNeoNatales.length > 0">

            <div class="tabla-paquetes" >

                <vs-table2 max-items="4" tooltip :data="ListaPaquetesNeoNatales">
                    <template slot="thead">
                        <th width="150px" filtro="Nombre">Nombre Paquete</th>
                        <th width="10px" filtro="Codigo">Codigo Paquete</th>
                        <th width="5px" filtro="Especialidad">Especialidad</th>
                        <th width="5px" filtro="Medax">Medax</th>
                        <th width="20px" filtro="MedicosMedax">Medicos Medax</th>
                        <th width="30px" filtro="Precio">Precio Paquete</th>
                        <th width="10px" filtro="CodigoBase">CodigoBase Hospital</th>
                    </template>

                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data" :ref="`list_${indextr}`" v-on:click=submit(tr)
                            :class="{ 'activo': tr.activo }">

                            <vs-td2>
                                {{ tr.Nombre }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.Codigo }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.Especialidad }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.Medax }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.CodigoBase }}
                            </vs-td2>

                            <vs-td2>
                                {{ parseFloat(tr.Precio).toFixed(2) }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.CodigoBase }}
                            </vs-td2>

                        </tr>
                    </template>

                </vs-table2>
            </div>
        </div>
    </div>
</template>


  
<script>

export default {


    name: "PaquetesQuirurgicosNeoNatal",
    props: {
        SerieAdmisionRaiz: {
            defataul: '',
            type: String
        },
        AdmisionRaiz: {
            defatult: 0,
            type: Number
        }
    },
    components: {
    },
    data() {
        return {
            selPolizaSaludSiempre: '',
            ListaPaquetesNeoNatales: []
        };
    },
    watch: {
    },
    activated() {

    },
    mounted() {
        this.ConsultaPaquete()
    },
    methods: {
        async ConsultaPaquete() {
            const resp = await this.axios.post('/app/v1_admision/ConsultaPaqueteNeoNatal', {
                Empresa: '',
                Hospital: '',
                SerieAdmisionRaiz: this.SerieAdmisionRaiz,
                AdmisionRaiz: this.AdmisionRaiz
            })
            this.ListaPaquetesNeoNatales = resp.data.json
        },
        submit(item) {
            this.$emit("paquete-rn-sel", item);
        }
    }
};
</script>


<style scoped></style>

<style >
.datos_paquete_neonatal {
    padding-top: 2rem;
    padding-bottom: 1rem;
    padding-left: 1rem;
    padding-right: 1rem;
    border: 1px solid #888;
    border-radius: 5px;
    margin: 4px;
}



.tabla-paquetes .contenedor-paquete {
    min-height: auto;
    margin: 5px;
}
</style>
