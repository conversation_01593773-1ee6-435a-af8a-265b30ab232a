<template>
<div id="egreso-sistema-container">
    <span style="font-size: 20px;">{{ TextoInfo }}</span>
    <form @submit="handleSubmit">
        <DxForm :form-data.sync="Egreso" label-location="left" validation-group="validacionPreEgreso" label-mode="hidden" :disabled="!HabilitarRegistro">
            <DxGroupItem :col-count="3">
                <DxGroupItem caption="Tratamientos" :col-span="2" css-class="egreso-group">
                    <DxSimpleItem :col-span="2" editor-type="dxTagBox" data-field="Tratamientos" :editor-options="{
                            dataSource: CatalogoTratamientos,
                            displayExpr: 'text',
                            valueExpr: 'value',
                            showSelectionControls: true,
                            multiline: false,
                            }" :validation-rules="ReglasValidacion" />
                </DxGroupItem>
                <DxGroupItem caption="Condición de egreso" css-class="egreso-group" :col-span="1">
                    <DxSimpleItem editor-type="dxRadioGroup" data-field="CondicionEgreso" :editor-options="{
                                items:[{id:'V', text:'Vivo'},{id:'F',text:'Fallecido'}],
                                displayExpr:'text',
                                layout:'horizontal',
                            }" :validation-rules="ReglasValidacion" />
                </DxGroupItem>
                <DxGroupItem caption="Diagnóstico de egreso" css-class="egreso-group" :col-span="3">
                    <DxSimpleItem editor-type="dxLookup" data-field="Diagnostico" :editor-options="{
                                dataSource: this.cie10DataSource,
                                valueExpr:'Codigo',
                                displayExpr: (d)=>{ return d? d.Codigo + ' - ' + d.Descripcion: 'Diagnóstico para de egreso' } ,
                                dropDownOptions: {showTitle:false},

                            }" :validation-rules="ReglasValidacion" />
                </DxGroupItem>
                <DxButtonItem :col-span="3" :visible=" StatusAdmision === 'A' || true" :button-options="submitButtonOptions" horizontal-alignment="center" verical-alignment="center" />
            </DxGroupItem>

        </DxForm>
    </form>
</div>
</template>

<script>
import EXPBase from './EXPBase.vue'
import {
    DxForm,
    DxSimpleItem,
    DxGroupItem,
    DxButtonItem,

} from 'devextreme-vue/form'
import 'devextreme-vue/tag-box';
import 'devextreme-vue/radio-group'

export default {
    name: 'EgresoSistema',
    extends: EXPBase,
    props: {
        FechaEgreso:null
    },
    components: {
        DxForm,
        DxGroupItem,
        DxSimpleItem,
        DxButtonItem,
    },
    data() {
        return {
            HabilitarRegistro: false,
            Egreso: {
                Tratamientos: null,
                CondicionEgreso: null,
                Diagnostico: null,
            },
            ReglasValidacion: [{
                type: 'required'
            }],

            submitButtonOptions: {
                text: 'Confirmar Egreso',
                type: 'success',
                icon: 'save',
                useSubmitBehavior: true,
                disabled: false
            },
            CatalogoTratamientos: [{
                    value: 'M',
                    text: 'Medicina'
                },
                {
                    value: 'Q',
                    text: 'Cirugia'
                },
                {
                    value: 'P',
                    text: 'Pediatria'
                },
                {
                    value: 'T',
                    text: 'Traumatología'
                },
                {
                    value: 'F',
                    text: 'Oftalmología'
                },
                {
                    value: 'D',
                    text: 'Odontología'
                },
                {
                    value: 'S',
                    text: 'Salud Mental'
                },
                {
                    value: 'R',
                    text: 'Radioterapia'
                },
                {
                    value: 'U',
                    text: 'Quimioterapia'
                },
                {
                    value: 'X',
                    text: 'Otros'
                },
            ],
            cie10: [],
            cie10DataSource: {
                paginate: true,
                pageSize: 10,
                sort: 'Codigo',
                // loadMode: 'raw',
                byKey: (e) => {
                    return this.cie10.Codigo === e ? this.cie10.Display : ''
                },
                load: (e) => {
                    let filtro = []
                    if (e.take) {
                        if (e.skip === e.take) {
                            e.take = e.take + 10
                        }
                    }
                    if (e.searchValue && e.searchValue.length >= 3) {
                        var searchValues = e.searchValue.trim().split(/[\s|%]/)
                        var values = '(\\w|\\s)*'
                        for (let x of searchValues) {
                            values += x.toUpperCase() + '(\\w|\\s)*'
                        }

                        let patron = RegExp(values)
                        for (let i of this.cie10) {
                            if (patron.test(i.Display)) {
                                filtro.push(i)
                            }
                        }

                        return filtro
                    } else {
                        return this.cie10.slice(e.skip, e.skip + e.take)
                    }
                }
            },
            
        }
    },

    methods: {
        Cargar() {
            if (Boolean(this.ExpedienteCargado) && !this.FechaEgreso)
                this.axios.post("/app/v1_ExpedienteEvolucion/BusquedaEgresoEnfermeria", {
                    Serie: this.SerieAdmision,
                    Admision: this.CodigoAdmision
                })
                .then(resp => {
                    if (resp.data.codigo == 0)
                        if (resp.data.json.length > 0) {
                            if (Number(resp.data.json[0].minutos) >= 15 || resp.data.json[0].fecha_confirma !== '') {
                                this.HabilitarRegistro = true
                                return
                            }
                        }
                    this.$vs.notify({
                        title: 'Egreso Sistema',
                        text: 'Aún no hay egreso del área de enfermería',
                        iconPack: 'feather',
                        icon: 'icon-alert-circle',
                        color: 'warning',
                        position: 'bottom-center',
                        time: 3000,
                    })
                    this.$emit('onPrerequisiteNeed', {name: 'EgresoEnfermeria'})
                })
        },
        CargarCIE10() {
            this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaDiagnosticos', {})
                .then(resp => {
                    this.cie10 = resp.data.json.map(x => {
                        return {
                            Codigo: x.codigoDiagnostico,
                            Descripcion: x.Diagnostico,
                            Display: x ? x.codigoDiagnostico + ' - ' + x.Diagnostico : 'Diagnóstico para Estadística'
                        }
                    })
                })
        },

        Grabar() {
            if(this.ExpedienteCargado){
                let tratam = this.CatalogoTratamientos.reduce( (trat,currVal) => { 
                        if(this.Egreso.Tratamientos.find( x=> x==currVal.value) )
                            return trat + currVal.value 
                        return trat
                    }, '')
                let postData = {
                    SerieAdmision:this.SerieAdmision,
                    Admision:this.CodigoAdmision,               
                    CondicionEgreso: this.Egreso.CondicionEgreso.id,
                    Tratamientos: tratam,
                    CodDiagnostico: this.Egreso.Diagnostico,
                }
                this.axios.post("/app/v1_ExpedienteEvolucion/InsertarEgresoSistema" , postData).then((resp)=>{
                    if(resp.data.codigo === 0){
                        setTimeout( ()=> this.$emit('onSaved'), 2500)
                    }
                    this.HabilitarRegistro=false
                    
                })
            }
        },
        handleSubmit(e) {
            e.preventDefault()
            if (this.ExpedienteCargado && this.HabilitarRegistro)
                this.Grabar()
        },
    },
    beforeMount() {
        if(!this.FechaEgreso)
            this.CargarCIE10()
    },
    computed: {
        TextoInfo() {
            return this.FechaEgreso?'Admisión ya cuenta con egreso el: ' + this.$formato_fecha(this.FechaEgreso):''
        }
    },   
}
</script>

<style>
.egreso-group {
    margin: 10px;
    padding: 10px;
    background-color: rgba(191, 191, 191, 0.15);
    height: 140px;
}

.egreso-titulo {
    font-weight: bold;
}

.egreso-parrafo {
    padding-bottom: 1.5px;
}

#preegreso-enfermeria-container .dx-invalid .dx-checkbox-container .dx-checkbox-icon {
    border: 1px solid rgba(217, 83, 79, .9);
}
</style>
