<template>
<div id="Registro-Anestesico">
    <div v-if="(consulta === true && hojaAnestesia !== null && hojaAnestesia !== '' && registrosAnestesicos !== null && registrosAnestesicos.length !== 0) || (consulta === false)">
        <div class="p-2">
            <form @submit="handleSubmit">
                <DxForm :form-data.sync="encabezado" labelMode="floating" :ref="dataEncabezadoRefKey">
                    <DxGroupItem item-type="group" :col-count="4">
                        <DxGroupItem item-type="group">
                            <DxItem data-field="InicioAnestesia" editor-type="dxDateBox" :is-required="!consulta" :editor-options="{...dateAnestesiaInicio, readOnly: consulta}">
                                <DxLabel :text="'Inicio anestesia'" />
                            </DxItem>
                            <DxItem data-field="InicioCirugia" editor-type="dxDateBox" :is-required="!consulta" :editor-options="{...dateCirugiaInicio, readOnly: consulta}">
                                <DxLabel :text="'Inicio cirugía'" />
                            </DxItem>
                        </DxGroupItem>
                        <DxGroupItem item-type="group">
                            <DxItem data-field="FinAnestesia" editor-type="dxDateBox" :is-required="!consulta" :editor-options="{...dateAnestesiaFin, readOnly: consulta}">
                                <DxLabel :text="'Fin anestesia'" />
                            </DxItem>
                            <DxItem data-field="FinCirugia" editor-type="dxDateBox" :is-required="!consulta" :editor-options="{...dateCirugiaFin, readOnly: consulta}">
                                <DxLabel :text="'Fin cirugía'" />
                            </DxItem>
                        </DxGroupItem>
                        <DxGroupItem item-type="group">
                            <DxSimpleItem v-if="!consulta">
                                <template #default>
                                    <div>
                                        <div id="detail">
                                            <b>Total de horas:</b>
                                            {{ horas }}
                                        </div>
                                        <div v-if="showHoras">
                                            <div id="detail">
                                                <b>Horas transcurridas:</b>
                                                {{ horasTranscurridas }}
                                            </div>
                                            <div id="detail">
                                                <b>Horas de anestesia:</b>
                                                {{ horasAnestesia }}
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </DxSimpleItem>
                            <DxSimpleItem v-if="consulta">
                                <template #default>
                                    <div>
                                        <div id="detail">
                                            <b>Anestesiólogo:</b>
                                            {{ anestesiologo }}
                                        </div>
                                    </div>
                                </template>
                            </DxSimpleItem>
                        </DxGroupItem>
                        <DxGroupItem v-if="!consulta" item-type="group">
                            <DxButtonItem :button-options="addTimeButtonOptions" horizontal-alignment="center" verical-alignment="center" @click="agregarTiempo" />
                            <DxButtonItem :button-options="clearButtonOptions" horizontal-alignment="center" verical-alignment="center" />
                            <DxButtonItem :button-options="submitButtonOptions" horizontal-alignment="center" verical-alignment="center" />
                            <DxButtonItem v-if="showLocal" :button-options="localButtonOptions" horizontal-alignment="center" verical-alignment="center" />
                        </DxGroupItem>
                        <DxGroupItem v-if="consulta" item-type="group">
                            <DxButtonItem :button-options="pdfButtonOptions" horizontal-alignment="center" verical-alignment="center" />
                            <DxItem data-field="RegistroAnestesico" editor-type="dxSelectBox" :editor-options="{ items: registrosAnestesicos, onItemClick: this.getDetalleRegistro }">
                                <DxLabel :text="'Registro anestésico'" />
                            </DxItem>
                        </DxGroupItem>
                    </DxGroupItem>

                </DxForm>
                <DxDataGrid :ref="dataRegistroRefKey" v-bind="DefaultDxGridConfiguration" :data-source="medicamentos" :headerFilter="{visible:false, allowSearch: false }" :searchPanel="{visible:false}" :sorting="{mode: 'none'}" :paging="{ enabled:false, pageSize:10 }" :width="'100%'" :height="'100%'" @editor-preparing="editorDatagrid" @cell-prepared="onCellPrepared">
                    <DxSelection mode="single" />

                    <DxColumn v-for="(item, index) in columnasMostrar" v-bind:key="index" :customize-text="customizeText" :width="item === 'Datos' ? 200 : (item === 'Observaciones' ? 400 : 100)" :data-field="item" :caption="item === 'Datos' ? ' ':item" alignment="center" :fixed="item === 'Datos' ? true : false" fixed-position="left" />
                    <DxEditing :allow-updating="!consulta" :allow-adding="false" :allow-deleting="false" :confirmDelete="false" mode="cell" />
                </DxDataGrid>
            </form>
        </div>
    </div>
    <vs-row id="texto" class="p-2" v-if="(hojaAnestesia === '' || hojaAnestesia === null) && consulta === true">
        <p>Seleccione una fecha para mostrar información</p>
    </vs-row>
    <vs-row id="texto" class="p-2" v-if="(registrosAnestesicos.length === 0 || registrosAnestesicos === null ) && consulta === true && hojaAnestesia !== '' && hojaAnestesia !== null">
        <p>La hoja seleccionada no cuenta con información para mostrar</p>
    </vs-row>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'
import {
    DxDataGrid,
    DxColumn,
    DxSelection,
    DxEditing,
} from 'devextreme-vue/data-grid'
import {
    DxForm,
    DxItem,
    DxLabel,
    DxGroupItem,
    DxSimpleItem,
    DxButtonItem,
} from 'devextreme-vue/form'

const dataRegistroRefKey = 'encabezado-form'
const dataEncabezadoRefKey = 'encabezado-form'

export default {
    name: 'RegistroAnestesico',
    components: {
        DxDataGrid,
        DxColumn,
        DxSelection,
        DxEditing,
        DxForm,
        DxItem,
        DxLabel,
        DxGroupItem,
        DxSimpleItem,
        DxButtonItem,
    },
    data() {
        return {
            DefaultDxGridConfiguration,
            dataRegistroRefKey,
            dataEncabezadoRefKey,
            columnasMostrar: [],
            encabezado: [],
            anestesia: [],
            observaciones: [],
            medicamentos: [],
            dateAnestesiaInicio: {
                type: "datetime"
            },
            dateAnestesiaFin: {
                min: new Date(),
                type: "datetime"
            },

            dateCirugiaInicio: {
                min: new Date(),
                type: "datetime"
            },
            dateCirugiaFin: {
                min: new Date(),
                type: "datetime"
            },
            minutos: 120,
            horas: '',
            submitButtonOptions: {
                text: 'Guardar datos',
                type: 'success',
                icon: 'save',
                width: 200,
                useSubmitBehavior: true,
                disabled: false
            },
            addTimeButtonOptions: {
                text: 'Agregar tiempo',
                type: 'default',
                icon: 'clock',
                width: 200,
                useSubmitBehavior: false,
                disabled: false,
                onClick: this.agregarTiempo
            },
            clearButtonOptions: {
                text: 'Limpiar registro',
                type: 'danger',
                icon: 'trash',
                width: 200,
                useSubmitBehavior: false,
                disabled: false,
                onClick: this.limpiarTiempo
            },
            localButtonOptions: {
                text: 'Cargar datos locales',
                type: 'warning',
                icon: 'fas fa-arrow-down',
                width: 200,
                useSubmitBehavior: false,
                disabled: false,
                onClick: this.cargarLocal
            },
            pdfButtonOptions: {
                text: 'Generar PDF',
                type: 'danger',
                icon: 'fas fa-file-pdf',
                useSubmitBehavior: false,
                disabled: true,
                onClick: this.generarPDF
            },
            showHoras: false,
            horasAnestesia: '',
            horasTranscurridas: '',
            anestesiologo: '',
            showLocal: false
        }
    },
    props: {
        SerieAdmision: null,
        CodigoAdmision: null,
        NumeroExpediente: null,

        StatusAdmision: null,
        consulta: null, //Variable para indicar si el componente se usará para consulta -> 'true' o para ingreso -> 'false'
        hojaAnestesia: null, //Variable para obtener la hoja cuando se utilice para consulta

        registrosAnestesicos: null, //Variable que obtiene la lista de registros anestésicos para el selectbox
        registrosTransoperatorios: null, //Variable para obtener la información de la apestaña de Registro transoperatorio
        ordenMedica: null //Variable para obtener información de la Orden médica
    },
    methods: {
        handleSubmit(e) {
            e.preventDefault()
            this.mapRegistro()
            if (this.SerieAdmision && this.CodigoAdmision) {
                let postData = {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    RegistroAnestesico: {
                        ...this.encabezado,
                        Anestesias: this.anestesia,
                        Observaciones: this.observaciones
                    },
                    RegistroTransoperatorio: this.mapTransoperatorio(),
                    OrdenPostOpertoria: this.mapOrden()
                }
                this.axios.post("/app/v1_ExpedienteEvolucion/InsertarAnestTransOperatorio", {
                    ...postData
                }).then(() => {

                })
            }
        },
        getQuirofanos() {
            this.axios.post('/app/salaoperaciones/ListarQuirofanos', {
                    Opcion: "C",
                    SubOpcion: "1",
                    Hospital: this.$store.state.sesion.sesion_sucursal,
                })
                .then(resp => {
                    this.quirofano = resp.data.json.map((x) => {
                        return x.text
                    })
                })
        },

        mapOrden() {
            let orden = null

            if (this.ordenMedica !== '') {
                orden = {
                    OrdenMedica: this.$reemplazar_tabulares(this.ordenMedica)
                }
            }

            return orden
        },
        mapTransoperatorio() {
            let transoperatorio = null

            if (this.registrosTransoperatorios !== {} && this.registrosTransoperatorios !== null && this.registrosTransoperatorios !== undefined && this.registrosTransoperatorios !== '') {
                transoperatorio = {
                    ...this.registrosTransoperatorios
                }
            }

            return transoperatorio
        },
        agregarTiempo() {
            for (let i = 1; i <= 7; i++) {
                this.columnasMostrar.push(String(parseInt(this.columnasMostrar[this.columnasMostrar.length - 1]) + 5));
            }
            setTimeout(() => {
                this.dataGrid.repaint();
            }, 50);
            this.minutos = this.minutos + 35;
            this.horas = this.calcularTiempo(this.minutos);
        },
        limpiarTiempo() {
            this.columnasMostrar = this.getColumnasIniciales()
            this.medicamentos = this.getMedicamentosIniciales()
            this.minutos = 120;
            this.horas = this.calcularTiempo(this.minutos);

            this.dateAnestesiaInicio = {
                value: new Date(),
                type: "datetime"
            }
            this.dateAnestesiaFin = {
                value: null,
                min: new Date(),
                type: "datetime"
            }
            this.dateCirugiaInicio = {
                value: new Date(),
                min: new Date(),
                type: "datetime"
            }
            this.dateCirugiaFin = {
                value: null,
                min: new Date(),
                type: "datetime"
            }
            setTimeout(() => {
                this.dataGrid.repaint();
            }, 50);
        },
        guardarRegistro() {
            this.mapRegistro()
        },
        getColumnasIniciales() {
            return ['Datos', 'Observaciones', '5', '10', '15', '20', '25', '30', '35', '40', '45', '50', '55', '60', '65', '70', '75', '80', '85', '90', '95', '100', '105', '110', '115', '120']
        },
        getMedicamentosIniciales() {
            return [{
                    Datos: {
                        name: 'BIS',
                        key: 'BIS'
                    }
                }, {
                    Datos: {
                        name: 'CO2',
                        key: 'CO2'
                    }
                }, {
                    Datos: {
                        name: 'EKG',
                        key: 'EKG'
                    }
                }, {
                    Datos: {
                        name: 'FC',
                        key: 'FC'
                    }
                }, {
                    Datos: {
                        name: 'Fentanil Ug',
                        key: 'FentanilUg'
                    }
                }, {
                    Datos: {
                        name: 'FIO2',
                        key: 'FIO2'
                    }
                }, {
                    Datos: {
                        name: 'Otros medicamentos',
                        key: 'OtrosMedicamentos'
                    }
                }, {
                    Datos: {
                        name: 'Oxigeno 1x',
                        key: 'Oxigeno1x'
                    }
                }, {
                    Datos: {
                        name: 'PA sistólica',
                        key: 'PAS'
                    }
                }, {
                    Datos: {
                        name: 'PA diastólica',
                        key: 'PAD'
                    }
                }, {
                    Datos: {
                        name: 'Propofol',
                        key: 'Propofol'
                    }
                }, {
                    Datos: {
                        name: 'PVC',
                        key: 'PVC'
                    }
                }, {
                    Datos: {
                        name: 'Relajante',
                        key: 'Relajante'
                    }
                }, {
                    Datos: {
                        name: 'SAT O2',
                        key: 'SATO2'
                    }
                }, {
                    Datos: {
                        name: 'Sevorane',
                        key: 'Sevorane'
                    }
                }, {
                    Datos: {
                        name: 'Soluciones',
                        key: 'Soluciones'
                    }
                }, {
                    Datos: {
                        name: 'Temperatura',
                        key: 'Temperatura'
                    }
                },
                //El Tren de cuatro , TOF (por sus siglas en inglés, Train Of Four) es un método de estimulación utilizado para evaluar el inicio, la intensidad y la recuperación del bloqueo neuromuscular en pacientes sometidos a anestesia general
                {
                    Datos: {
                        name: 'M. Neuromuscular',
                        key: 'TOF' //Se cambio TOF A M. Neuromuscular por solicitud de Jefa de sala de Operaciones
                    }
                }, {
                    Datos: {
                        name: 'T. Isquemia',
                        key: 'TIsquemia'
                    }
                },
            ]
        },
        customizeText(e) {
            if (typeof (e.value) === 'object' && typeof (e.value) !== undefined) {
                return e.value.name
            }
            return e.value;
        },
        editorDatagrid(e) {
            if (e.parentType === 'dataRow' && e.dataField === 'Datos') {
                e.editorOptions.disabled = true
                e.editorOptions.value = e.value.name
            }
            if (e.row.rowIndex >= 7 && e.row.rowIndex <= 13 && e.dataField !== 'Datos' && e.dataField !== 'Observaciones') {
                e.editorOptions.onKeyDown = this.onlyNumbers
            }

            var fecha = new Date(this.encabezado.InicioAnestesia)
            var hora = fecha.getHours() * 60
            var minutos = fecha.getMinutes()
            if (e.parentType === 'dataRow' && (e.dataField === 'Datos' || e.dataField === 'Observaciones')) {
                this.showHoras = false
                e.editorOptions.maxLength = 2000
            } else {
                this.horasTranscurridas = this.calcularTiempo(parseInt(e.dataField))

                var total = hora + minutos + parseInt(e.dataField)
                this.horasAnestesia = this.calcularTiempo(total)

                this.showHoras = true
            }
            this.guardarInfo()
        },
        onlyNumbers(e) {
            if ((e.event.keyCode < 48 || e.event.keyCode > 57) && (e.event.keyCode < 96 || e.event.keyCode > 105) && e.event.keyCode !== 8 && e.event.keyCode !== 9) {
                e.event.preventDefault()
            }
        },
        onCellPrepared(e) {
            if (e.rowType === 'data' && e.column.dataField === 'Datos') {
                e.cellElement.style.cssText = "background-color: #fbf1e6; text-align: center; font-weight: bold; font-size: 16px;";
            }
            if (e.rowType === 'data' && e.column.dataField !== 'Datos' && e.column.dataField !== 'Observaciones') {
                if (parseInt(e.column.dataField) % 60 === 0) {
                    e.cellElement.style.cssText = "color: red; text-align: center; font-weight: bold;";
                }
            }
        },
        mapRegistro() {
            let res = []
            let observaciones = []

            this.medicamentos.map(item => {
                if (item.Observaciones) {
                    observaciones.push({
                        NombreColumna: item.Datos.key,
                        Observaciones: this.$reemplazar_tabulares(item.Observaciones)
                    })
                }
                Object.entries(item).map(prop => {
                    if (prop[0] !== "Datos" && prop[0] !== "Observaciones") {
                        const found = res.find(element => element.MinutoRegistro === prop[0]);
                        if (found) {
                            //set
                            found[item.Datos.key] = prop[1]
                        } else {
                            res.push({
                                MinutoRegistro: prop[0],
                                [item.Datos.key]: prop[1]
                            })
                        }
                    }

                })
            });
            this.anestesia = res
            this.observaciones = observaciones
        },
        formatToday(date = new Date()) {
            const year = date.toLocaleString('default', {
                year: 'numeric'
            });
            const month = date.toLocaleString('default', {
                month: '2-digit'
            });
            const day = date.toLocaleString('default', {
                day: '2-digit'
            });

            return [year, month, day].join('/');
        },
        calcularTiempo(minutos) {
            var hours = (minutos / 60);
            var rhours = Math.floor(hours);
            var minutes = (hours - rhours) * 60;
            var rminutes = Math.round(minutes);
            return rhours + " hora(s) " + rminutes + " minuto(s)";
        },
        valueChangedAnestesiaInicio(e) {
            this.dateAnestesiaFin = {
                min: new Date(e.value),
                type: "datetime",
            }

            this.dateCirugiaInicio = {
                min: new Date(e.value),
                type: "datetime",
            }
        },
        valueChangedCirugiaInicio(e) {
            this.dateCirugiaFin = {
                min: new Date(e.value),
                type: "datetime",
            }
        },
        getDetalleRegistro() {
            this.axios.post('/app/v1_ExpedienteEvolucion/BuscarRegistroAnestesico', {
                    Id: this.encabezado.RegistroAnestesico,
                })
                .then(resp => {
                    this.columnasMostrar = this.getColumnasConsulta(resp.data.Anestesicos)
                    this.medicamentos = this.mapConsulta(resp.data)

                    this.pdfButtonOptions = {
                        text: 'Generar PDF',
                        type: 'danger',
                        icon: 'fas fa-file-pdf',
                        useSubmitBehavior: false,
                        disabled: false,
                        onClick: this.generarPDF
                    }
                })
        },
        getColumnasConsulta(res) {
            var i = res.map((x) => x.MinutoRegistro)
            var col = ['Datos', 'Observaciones']
            col.push(...i.map(String))
            return col
        },
        mapConsulta(res) {
            this.dateAnestesiaInicio = {
                value: new Date(res.InicioAnestesia),
                type: "datetime"
            }
            this.dateAnestesiaFin = {
                value: new Date(res.FinAnestesia),
                type: "datetime"
            }
            this.dateCirugiaInicio = {
                value: new Date(res.InicioCirugia),
                type: "datetime"
            }
            this.dateCirugiaFin = {
                value: new Date(res.FinCirugia),
                type: "datetime"
            }
            this.anestesiologo = res.Usuario
            var resultado = []
            for (const dato of this.medicamentos) {

                var itemArray = []
                itemArray.push({
                    ...dato
                })
                res.Anestesicos.map(item => {
                    if (item[dato.Datos.key] !== null && item[dato.Datos.key] !== undefined) {
                        itemArray.push({
                            [String(item.MinutoRegistro)]: item[dato.Datos.key]
                        })
                    }
                })
                res.Observaciones.find((item) => {
                    if (item.NombreColumna === dato.Datos.key && item.Observaciones !== null) {
                        itemArray.push({
                            Observaciones: item.Observaciones
                        })
                    }
                })
                resultado.push(Object.assign({}, ...itemArray))
            }
            return resultado
        },
        guardarInfo() {
            let existente = localStorage.getItem('RegistroAnestesico');

            if (existente === '' || existente === undefined || existente === null) {
                let nuevo = [{
                    Serie: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    info: this.medicamentos
                }]
                let v = JSON.stringify(nuevo)
                localStorage.setItem('RegistroAnestesico', v)
            } else {
                let admisiones = JSON.parse(existente)
                let existe = false

                for (let item of admisiones) {
                    if (item.Serie == this.SerieAdmision && item.Admision == this.CodigoAdmision) {
                        existe = true
                        this.showLocal = true
                        break;
                    }
                }

                if (!existe) {
                    let nuevo = {
                        Serie: this.SerieAdmision,
                        Admision: this.CodigoAdmision,
                        info: this.medicamentos
                    }
                    admisiones.push(nuevo)
                    localStorage.setItem('RegistroAnestesico', JSON.stringify(admisiones))
                } else {
                    let i = admisiones.findIndex(e => e.Serie == this.SerieAdmision && e.Admision == this.CodigoAdmision)
                    let nuevo = {
                        Serie: this.SerieAdmision,
                        Admision: this.CodigoAdmision,
                        info: this.medicamentos
                    }
                    admisiones[i] = nuevo
                    localStorage.setItem('RegistroAnestesico', JSON.stringify(admisiones))
                }

            }
        },
        buscarLocal() {
            let existente = localStorage.getItem('RegistroAnestesico');

            if (existente === '' || existente === undefined || existente === null) {
                this.showLocal = false
            } else {
                let admisiones = JSON.parse(existente)

                for (let item of admisiones) {
                    if (item.Serie == this.SerieAdmision && item.Admision == this.CodigoAdmision) {
                        this.showLocal = true
                        break;
                    }
                }
            }
        },
        cargarLocal() {
            let existente = localStorage.getItem('RegistroAnestesico');

            if (existente === '' || existente === undefined || existente === null) {
                this.showLocal = false
            } else {
                let admisiones = JSON.parse(existente)

                for (let item of admisiones) {
                    if (item.Serie == this.SerieAdmision && item.Admision == this.CodigoAdmision) {
                        this.medicamentos = item.info
                        break;
                    }
                }
            }
        },
        limpiarConsulta() {
            this.dateAnestesiaInicio = {
                value: ''
            }
            this.dateAnestesiaFin = {
                value: ''
            }
            this.dateCirugiaInicio = {
                value: ''
            }
            this.dateCirugiaFin = {
                value: ''
            }
            this.anestesiologo = ''

            this.encabezado.RegistroAnestesico = null

            this.pdfButtonOptions = {
                text: 'Generar PDF',
                type: 'danger',
                icon: 'fas fa-file-pdf',
                useSubmitBehavior: false,
                disabled: true,
                onClick: this.generarPDF
            }
        },
        generarPDF() {
            let postData = {
                SerieAdmision: this.SerieAdmision,
                CodigoAdmision: this.CodigoAdmision,
                Id: this.encabezado.RegistroAnestesico
            }

            this.$reporte_modal({
                Nombre: 'Registro Anestésico',
                Opciones: {
                    ...postData
                }
            })
        },
    },
    mounted() {
        if (this.consulta) {
            this.columnasMostrar = this.getColumnasIniciales()
            this.medicamentos = this.getMedicamentosIniciales()
        } else {
            this.buscarLocal()
            this.columnasMostrar = this.getColumnasIniciales()
            this.medicamentos = this.getMedicamentosIniciales()
            this.dateAnestesiaInicio = {
                value: new Date(),
                type: "datetime",
                onValueChanged: this.valueChangedAnestesiaInicio
            }
            this.dateCirugiaInicio = {
                value: new Date(),
                type: "datetime",
                onValueChanged: this.valueChangedCirugiaInicio
            }
            this.horas = this.calcularTiempo(this.minutos);
        }
    },
    watch: {
        'registrosAnestesicos'(newval) {
            if (newval !== undefined) {
                this.limpiarConsulta()
                this.medicamentos = this.getMedicamentosIniciales();
                this.columnasMostrar = this.getColumnasIniciales()
            }
        },
    },
    computed: {
        dataGrid: function () {
            return this.$refs[dataRegistroRefKey].instance;
        },
    },
}
</script>

<style>
#detail {
    padding: 5px;
    border: none;
    color: black;
}

#texto {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    height: 100px;
}

#Registro-Anestesico .dx-button.dx-button-warning {
    background-color: #FF6347;
}

#Registro-Anestesico .dx-button-mode-contained.dx-button-warning .dx-icon {
    color: #fff;
}

#Registro-Anestesico .dx-button-mode-contained.dx-button-warning {
    color: #fff;
}

#Registro-Anestesico .dx-item-content .dx-box-item-content {
    place-content: center;
    padding: 5px !important;
    padding-right: 10px !important;
}
</style>
