<template>
<div>
    <div class="p-2">
        <div v-if="SelectedOption > 0" class="compact-tabs">
            <DxTabs :selected-index="SelectedOption-1" height="auto" width="100%" :rtl-enabled="false" orientation="horizontal" styling-mode="secondary" :icon-position="'top'" :show-nav-buttons="false" :scroll-by-content="true" @item-click="(e)=>OpcionSeleccionada(menu[e.itemIndex]) " 
            :element-attr="{ class: 'custom-tabs'}">
                <DxTabsItem v-for="(item, index) in menu" :key="index" template="tabButton" />
                <template #tabButton="{index}">
                    <vs-tooltip :text="menu[index].title">
                            <span style="font-size: medium; ">{{menu[index].title}}</span>
                    </vs-tooltip>
                </template>
            </DxTabs>
        </div>
        <vx-card>
            <div v-if="SelectedOption === 1 ">
                <Dietas />
            </div>
            <div v-if="SelectedOption === 2 ">
                <Comandas />
            </div>
        </vx-card>
    </div>
</div>
</template>

<script>

import Dietas from './Dietas.vue';
import Comandas from './Comandas.vue'

export default {
    name: 'Bancos',
    components: {
        Dietas,
        Comandas
    },
    data() {
        return {
            SelectedOption: 1,
            Title: '',
            menuStack: [],
            menuActual: [],

            menu: [{
                    id: 1,
                    title: "Dietas Expediente",
                    icon: "toolbox",
                },
                {
                    id: 2,
                    title: "Comandas",
                    icon: "toolbox",
                },
            ],

            tabsAttributes: {
                class: 'tabMenu'
            },

            Corporativo: null
        }
    },
    methods: {
        OpcionSeleccionada(data) {
            this.SelectedOption = data.id;
            this.Title = data.title;

        },

        RegresarMenu() {
            this.menuStack.pop();
            let i = this.menuStack[this.menuStack.length - 1];

            if (i === 'navigation') {
                this.Title = "Expediente";
                this.SelectedOption = 0;
                this.menuActual = [];
            } else {
                this.Title = i.title
                this.SelectedOption = i.id;
                this.menuActual = [];
                if (i.submenu) {
                    this.menuActual = i.submenu;
                }
            }
        },
    },
    created() {},
    mounted() {
        this.Corporativo = this.sesion.corporativo
    },
    watch: {},
    computed: {
        sesion() {
            return this.$store.state.sesion
        },
    }
}
</script>

<style>

.compact-tabs {
    padding-bottom: 0.75rem;
}

.compact-tab-button {
    width: auto;
}

.tab-title {
    font-size: 0.90rem !important; /* 12px */
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.custom-tabs .dx-tab {
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.custom-tabs .dx-tab.dx-tab-selected {
    background-color: #3498db;
    color: white;
}

.custom-tabs .dx-tab.dx-tab-selected .tab-title {
    color: white !important;
}

.custom-tabs .dx-tabs-nav-button .dx-tab-selected:after {
    background-color: #3498db;
    height: 3px;
}
</style>