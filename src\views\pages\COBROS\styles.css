
/*Ajustar elemntos vuesax a apareiencia de devextreme*/
.vs-fixed-dx-input-container .vs-inputx,
.vs-fixed-dx-input-container .vs-input--input,
.vs-fixed-dx-input-container .vs-input--input.normal {
    background: #fff !important;
    border: 1px solid rgba(0, 0, 0, 0.2) !important;
    height: 34px !important;
}

.vs-fixed-dx-input-container .includeIconOnly {
    width: 34px !important;
}

.vs-fixed-dx-input-container .vs-button {
    height: 34px !important;
}

.vs-fixed-dx-input-container .lg-icon i.vs-icon {
    font-size: 1.4rem !important;
    margin-right: 10px !important; 
}

.dx-box-item:has(.mysticky) {
    position: sticky !important;
    bottom: 15px !important;
    background-color: whitesmoke !important;
    z-index: 1;
}

.my-sticky-top {
    position: sticky !important;
    top: 75px !important;
    background-color: whitesmoke !important;
    z-index: 1;
}

.my-cell-invalid .dx-datagrid-rowsview .dx-data-row .dx-validator.dx-datagrid-invalid.dx-cell-modified::after, 
.my-cell-invalid .dx-datagrid-rowsview .dx-data-row .dx-validator.dx-datagrid-invalid.dx-datagrid-invalid::after {
    border-width: 2px !important;
    border-color: #D9534F;
}

.swal2-container {
    z-index: 99999;
}