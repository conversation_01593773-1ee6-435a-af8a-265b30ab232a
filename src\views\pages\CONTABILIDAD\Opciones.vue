<template>
<div>
    <div>
        <DxButton width="200px" height="30px" :type="!editarFormulario ? 'default' : 'success'" class="botonOpciones mb-2 mt-2" styling-mode="contained" @click="!editarFormulario ?  EditarFormulario() : GuardarOpciones()" hint="Imprimir orden">
            <div v-if="!editarFormulario">
                <span>Editar opciones</span>
                <font-awesome-icon class="ml-2" :icon="['fas', 'edit']" style="font-size: 20px; vertical-align: middle" />
            </div>
            <div v-else>
                <span>Guardar opciones</span>
                <font-awesome-icon class="ml-2" :icon="['fas', 'save']" style="font-size: 20px; vertical-align: middle" />
            </div>
        </DxButton>
    </div>
    <DxForm :form-data.sync="opciones" labelMode="floating" :read-only="!editarFormulario">
        <DxFormGroupItem :col-count-by-screen="colCountByScreen">
            <DxFormItem data-field="IVA" editor-type="dxNumberBox" :validationRules="[{ type: 'required' }]" :editor-options="{min: 0.01, max: 100, format: '#0.00', step: 0.01}" />
            <DxFormItem data-field="ISR" editor-type="dxNumberBox" :validationRules="[{ type: 'required' }]" :editor-options="{min: 0.01, max: 100, format: '#0.00', step: 0.01}">
                <DxFormLabel text="Retención ISR" />
            </DxFormItem>
            <DxFormItem data-field="ISR_Servicios" editor-type="dxNumberBox" :validationRules="[{ type: 'required' }]" :editor-options="{min: 0.01, max: 100, format: '#0.00', step: 0.01}">
                <DxFormLabel text="ISR Servicios" />
            </DxFormItem>
            <DxFormItem data-field="ISR_Compras" editor-type="dxNumberBox" :validationRules="[{ type: 'required' }]" :editor-options="{min: 0.01, max: 100, format: '#0.00', step: 0.01}">
                <DxFormLabel text="ISR Compras" />
            </DxFormItem>
            <DxFormItem data-field="MesesVigencia" editor-type="dxNumberBox" :validationRules="[{ type: 'required' }]" :editor-options="{min: 1, max: 12, format: '#0', step: 1}">
                <DxFormLabel text="Meses de vigencia para cheques" />
            </DxFormItem>
        </DxFormGroupItem>
    </DxForm>
</div>
</template>

<script>

export default {
    name: 'Opciones',
    components: {
    },
    data() {
        return {

            opciones: [], // Variable que guardará el IVA, ISR, entre otros datos que maneja la empresa

            editarFormulario: false
        }
    },
    methods: {
        CargarOpciones() {
            this.axios.post('/app/v1_bancos/Opciones', {
                    Opcion: 1
                })
                .then(resp => {
                    this.opciones = resp.data.json[0]
                })
        },
        EditarFormulario() {
            this.editarFormulario = true
        },

        GuardarOpciones() {
            this.axios.post('/app/v1_bancos/Opciones', {
                    Opcion: 2,
                    IVA: this.opciones.IVA,
                    ISR: this.opciones.ISR,
                    Meses: this.opciones.MesesVigencia,
                    ISR_Servicios: this.opciones.ISR_Servicios,
                    ISR_Compras: this.opciones.ISR_Compras
                })
                .then(resp => {
                    if(resp.data.codigo === 0)
                    {
                        this.editarFormulario = false
                    }
                })

            this.editarFormulario = false
        },

    },
    created() {},
    mounted() {},
    beforeMount() {
        this.CargarOpciones()
    },
    watch: {},
    computed: {

        colCountByScreen() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 2,
                    md: 5,
                    lg: 5
                };
        },
    }
}
</script>

<style>
.botonOpciones .dx-button-content {
    padding: 0px !important;
}
</style>
