<template>
    <div>
        <div class="container">
                <div class="notificacion" @click="barra.mostrar = true">
                <i class="fas fa-truck"></i>
                <div class="titulo">Paqueteria</div>
                </div>
          
        </div>

        <vs-popup title="Ingreso de Paqueteria" :active.sync="barra.mostrar"
            style="margin-left: -5%;width: 110%;zoom: 70%;">
            <div class="flex flex-wrap mb-1">
                <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6  p-1">
                    <vs-button class="w-full mr-1 mt-5" title="Crear envío" color="success"
                        @click="Otros().AbrirVentana(1)">
                        Crear
                    </vs-button>
                </div>
                <div class="w-full md:w-1/1 lg:w-1/1 xl:w-1/1 p-1">
                    <vs-table2 max-items="15" pagination :data="listaEnvios" search>
                        <template slot="thead">
                            <th order="IdPaquete">ID</th>
                            <th order="FechaEnvia">Fecha Envia</th>
                            <th order="HoraEnvia">Hora Envia</th>
                            <th order="NombreEnvia">Nombre Envia</th>
                            <th order="FechaEntrega">Fecha Entrega</th>
                            <th order="HoraEntrega">Hora Entrega</th>
                            <th order="NombreEntrega">Nombre Entrega</th>
                            <th order="Estado">Estado</th>
                            <th></th>
                        </template>
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in  data ">
                                <vs-td2>
                                    {{ tr.IdPaquete }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.FechaEnvia }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.HoraEnvia }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.NombreEnvia }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.FechaEntrega }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.HoraEntrega }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.NombreEntrega }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.EstadoDescripcion }}
                                </vs-td2>
                                <vs-td2>
                                    <vs-button color="warning" size="small" icon-pack="fas" icon="fa-info-circle"
                                        title="Información" class="mr-1" style="display:inline-block"
                                        @click="Otros().AbrirVentana(2, tr)">
                                    </vs-button>
                                    <vs-button size="small" icon-pack="fas" icon="fa-truck" class="mr-1" title="Tracking"
                                        style="display:inline-block" @click="Otros().AbrirVentana(3, tr)">
                                    </vs-button>
                                    <vs-button v-if="tr.Estado == 1 && tr.TipoEnvio == 'Envia'" color="danger" size="small"
                                        icon-pack="fas" title="Elimnar" icon="fa-trash" class="mr-1"
                                        style="display:inline-block" @click="Eliminar().EliminarPaquete(tr.IdPaquete)">
                                    </vs-button>
                                    <vs-button size="small" icon-pack="fas" icon="fa-envelope" class="mr-1" title="Guia"
                                        color="success"
                                        v-if="tr.Estado == 1 && tr.TipoEnvio == 'Envia' && sesion.sesion_sucursal !== ''"
                                        style=" display:inline-block" @click="Consulta().ReporteGuia(tr)">
                                    </vs-button>
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
                </div>
            </div>
        </vs-popup>


        <vs-popup :title="ventana.titulo" v-show="ventana.mostrar" :active.sync="ventana.mostrar">
            <div v-if="ventana.opcion == 1">
                <div>
                    <vs-alert title="Impresión de guía" color="danger" active="true" v-if="sesion.sesion_sucursal == ''">
                        Debe solicitar en recepciónla impresión de la guía de su paquete al momento de entregarlo.
                    </vs-alert>
                </div>
                <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                    <form method="post" @submit.prevent="handleSubmit(Agregar().AgregarPaquete(invalid))">
                        <vs-divider position="center">Datos de salida / Persona que envía</vs-divider>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                                <ValidationProvider name="HospitalEnvia" rules="required" class="required">
                                    <label style="font-size:12px">Hospital</label>
                                    <v-select label="Descripcion" v-model="selectHospitalEnvia" :options="listaHospital"
                                        :clearable="false">
                                        <template #no-options="{ }">
                                            No hay data cargada!
                                        </template>
                                    </v-select>
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                                <vs-input label="Nombre" class="w-full" v-model="sesion.usuario" disabled="true" />
                            </div>
                            <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                                <vs-input label="Corporativo" class="w-full" v-model="sesion.corporativo" disabled="true" />
                            </div>
                            <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                                <ValidationProvider name="AreaEnvia" rules="required|max:50" v-slot="{ errors }"
                                    class="required">
                                    <vs-input label="Área" type="text" class="w-full" v-model="info.AreaEnvia"
                                        :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                        required />
                                </ValidationProvider>
                            </div>
                        </div>
                        <vs-divider position="center">Datos de entrega / Persona que recibe</vs-divider>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1 ">
                                <ValidationProvider name="HospitalEntrega" rules="required" class="required">
                                    <label style="font-size:12px">Hospital</label>
                                    <v-select label="Descripcion" v-model="selectHospitalEntrega" :options="listaHospital"
                                        :clearable="false">
                                        <template #no-options="{ }">
                                            No hay data cargada!
                                        </template>
                                    </v-select>
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1 ">
                                <ValidationProvider name="Codigo" rules="required|numero_entero|numero_min:1"
                                    v-slot="{ errors }" class="required">
                                    <SM-Buscar v-model="info.CorporativoEntrega" label="Código Corporativo"
                                        api="app/Ajenos/Busqueda_Corporativo"
                                        :api_campos="['Corporativo', ['Nombres', 'Nombre'], ['Apellidos', 'Apellido'], '#NombreCompleto']"
                                        :api_titulos="['Corporativo', 'Nombres', 'Apellidos', '#NombreCompleto']"
                                        api_campo_respuesta="Corporativo" :disabled_search_input="true"
                                        :callback_buscar="Consulta().ConsultaCorporativo" :danger="errors.length > 0"
                                        api_campo_respuesta_mostrar="NombreCompleto"
                                        :dangertext="errors.length > 0 ? errors[0] : null" />
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1 ">
                                <ValidationProvider name="AreaEntrega" rules="required|max:50" v-slot="{ errors }"
                                    class="required">
                                    <vs-input label="Área" type="text" class="w-full" v-model="info.AreaEntrega"
                                        :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                        required />
                                </ValidationProvider>
                            </div>
                        </div>
                        <vs-divider position="center">Paquete a envíar</vs-divider>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5  p-1">
                                <ValidationProvider name="Hospital" rules="required" class="required">
                                    <label style="font-size:12px">Tipo de paquete</label>
                                    <v-select label="Descripcion" v-model="selectTipoPaquete" :options="listaTipoPaquete"
                                        :clearable="false" @input="Otros().CambiarTipoPaquete()">
                                        <template #no-options="{ }">
                                            No hay data cargada!
                                        </template>
                                    </v-select>
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5  p-1"
                                v-if="selectTipoPaquete.Descripcion == 'Otros'">
                                <ValidationProvider name="DescripcionTipoPaquete" rules="required|max:50"
                                    v-slot="{ errors }" class="required">
                                    <vs-input label="Descripción tipo de paquete" type="text" class="w-full"
                                        v-model="info.DescripcionTipoPaquete" :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" required />
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5  p-1">
                                <ValidationProvider name="Cantidad" rules="numero_entero|min:1|max:9|numero_min:1"
                                    v-slot="{ errors }">
                                    <vs-input label="Cantidad" type="text" class="w-full" v-model="detallePaquete.Cantidad"
                                        :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5  p-1">
                                <ValidationProvider name="DescripcionPaquete" rules="max:60" v-slot="{ errors }">
                                    <vs-input label="Descripcion" type="text" class="w-full"
                                        v-model="detallePaquete.Descripcion" :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" />
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                                <vs-button class="w-full mr-1 mt-5" icon-pack="fas" icon="fa-plus" color="success"
                                    @click="Agregar().AgregarDetallePaquete()">
                                    Agregar
                                </vs-button>
                            </div>
                            <div class="w-full md:w-1/1 lg:w-1/1 xl:w-1/1 p-1">
                                <vs-table2 max-items="5" pagination :data="detallesPaqueteEnvio">
                                    <template slot="thead">
                                        <th order="Cantidad">Cantidad</th>
                                        <th order="Descripción">Descripción</th>
                                        <th></th>
                                    </template>
                                    <template slot-scope="{data}">
                                        <tr :key="indextr" v-for="( tr, indextr ) in  data ">
                                            <vs-td2>
                                                {{ tr.Cantidad }}
                                            </vs-td2>
                                            <vs-td2>
                                                {{ tr.Descripcion }}
                                            </vs-td2>
                                            <vs-td2 width="50px">
                                                <vs-button color="danger" size="small" icon-pack="fas" icon="fa-trash"
                                                    class="mr-1" style="display:inline-block"
                                                    @click="Eliminar().EliminarElemento(tr.Codigo)">
                                                </vs-button>
                                            </vs-td2>
                                        </tr>
                                    </template>
                                </vs-table2>
                            </div>
                        </div>
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                                <vs-button class="w-full mr-1 mt-5" icon-pack="fas" icon="fa-plus" color="success"
                                    @click="Agregar().AgregarPaquete(invalid)">
                                    Crear Paquete
                                </vs-button>
                            </div>
                        </div>
                    </form>
                </ValidationObserver>
            </div>
            <div v-if="ventana.opcion == 2">
                <div class="flex flex-wrap mb-1">
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Corporativo envia: {{ this.infoDetalle.CorporativoEnvia }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Nombre envia: {{ this.infoDetalle.NombreEnvia }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Hospital envia: {{ this.infoDetalle.HospitalEnvia }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Fecha envia: {{ this.infoDetalle.FechaEnvia }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Hora envia: {{ this.infoDetalle.HoraEnvia }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Area envia: {{ this.infoDetalle.AreaEnvia }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Corporativo entrega: {{ this.infoDetalle.CorporativoEntrega }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Nombre entrega: {{ this.infoDetalle.NombreEntrega }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Hospital entrega: {{ this.infoDetalle.HospitalEntrega }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Fecha entrega: {{ this.infoDetalle.FechaEntrega }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Hora entrega: {{ this.infoDetalle.HoraEntrega }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Area entrega: {{ this.infoDetalle.AreaEntrega }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Tipo Paquete: {{ this.infoDetalle.TipoPaquete }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1" v-if="infoDetalle.DescripcionOtroTipo !== ''">
                        <label style="font-size:12px">Descripción tipo paquete: {{ this.infoDetalle.DescripcionOtroTipo
                        }}</label>
                    </div>
                    <div class="w-full md:w-1/1 lg:w-1/1 xl:w-1/1 p-1" v-if="detallesPaqueteEnvio.length > 0">
                        <vs-table2 max-items="5" pagination :data="detallesPaqueteEnvio">
                            <template slot="thead">
                                <th order="Cantidad">Cantidad</th>
                                <th order="Descripcion">Descripción</th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="( tr, indextr ) in  data ">
                                    <vs-td2>
                                        {{ tr.Cantidad }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.Descripcion }}
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                </div>
                <div class="flex flex-wrap mb-1">
                    <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6  p-1">
                        <vs-button class="w-full" @click="Otros().VolverVentanaPrincipal()">
                            Regresar
                        </vs-button>
                    </div>
                </div>
            </div>
            <div v-if="ventana.opcion == 3">
                <Stepper :steps="steps" :onNext="nextClicked" :onBack="backClicked" ref="tracking"
                    previousStepLabel="Anterior" nextStepLabel="Siguiente" finalStepLabel="Fin">
                    <div slot="Ingreso">
                        Nombre: {{ this.infoTracking.NombreEnvia }}
                        <br>
                        Fecha y hora: {{ this.infoTracking.IngresoPaquete }}
                    </div>
                    <div slot="Aceptacion" style="text-align: center;">
                        Nombre: {{ this.infoTracking.NombreAcepta }}
                        <br>
                        Fecha y hora: {{ this.infoTracking.AceptaPaquete }}
                    </div>
                    <div slot="Asignacion" style="text-align: center;">
                        Nombre: {{ this.infoTracking.NombreAsigna }}
                        <br>
                        Fecha y hora: {{ this.infoTracking.AsignacionPaquete }}
                    </div>
                    <div slot="Recepcion" style="text-align: right;">
                        Nombre: {{ this.infoTracking.NombreRecibe }}
                        <br>
                        Fecha y hora: {{ this.infoTracking.RecibePaquete }}
                    </div>
                </Stepper>
                <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6  p-1">
                    <vs-button class="w-full" @click="Otros().VolverVentanaPrincipal()">
                        Regresar
                    </vs-button>
                </div>
            </div>
        </vs-popup>
    </div>
</template>

<script>

import vSelect from 'vue-select'

export default {
    data() {
        return {
            /**
             * Documentación del módulo
             */
            doc_: [
                'Pantalla de Ingreso de paquete',
            ],

            ventana: {
                titulo: '',
                mostrar: false,
                opcion: 0
            },
            info: {
                HospitalEnvia: null,
                AreaEnvia: null,
                FechaEntrega: null,
                HoraEntrega: null,
                HospitalEntrega: null,
                CorporativoEntrega: null,
                AreaEntrega: null,
                TipoPaquete: null,
                DescripcionTipoPaquete: null,
                Paquetes: null
            },
            infoDetalle: {},
            listaEnvios: [],
            listaHospital: [],
            selectHospitalEntrega: null,
            selectHospitalEnvia: null,
            listaTipoPaquete: [],
            selectTipoPaquete: { Codigo: null, Descripcion: null },
            codigoDetallePaquete: 0,
            detallePaquete: { Codigo: 0, Cantidad: 1, Descripcion: null },
            detallesPaqueteEnvio: [],
            steps: [
                {
                    label: 'Ingreso Paquete',
                    slot: 'Ingreso',
                },
                {
                    label: 'Aceptación Paquete',
                    slot: 'Aceptacion',
                },
                {
                    label: 'Asignación Paquete',
                    slot: 'Asignacion',
                },
                {
                    label: 'Recepción Paquete',
                    slot: 'Recepcion',
                }
            ],
            infoTracking: {},
            barra: {
                mostrar: false
            }
        }
    },
    components: {
        'v-select': vSelect,
        Stepper: () => import("../../../components/sermesa/global/VueGoodWizzard.vue")
    },

    computed: {
        sesion() {
            return this.$store.state.sesion
        }
    },
    watch: {
        'barra.mostrar'(value) {
            if (value) {
                this.Consulta().init()
            }
        }
    },
    methods: {
        Consulta() {
            return {
                init: () => {
                    this.axios.post('/app/mensajeria/ListarPaquetes', {})
                        .then(resp => {
                            this.listaEnvios = resp.data.json;
                        })
                },
                /**
                 * Consula de Corporativo
                 * Permite obtener los nombres y apellidos del corporativo
                 */
                ConsultaCorporativo: (data) => {
                    this.axios.post('/app/administracion/CorporativoConsulta', {
                        IdCorporativo: data.Corporativo
                    })
                        .then(resp => {
                            if (resp.data.json) {
                                this.$map({
                                    objeto: this.info,
                                    respuestaAxios: resp,
                                    omitir: ['Corporativo']
                                })
                            }
                        })
                },
                ListarTipoPaquete: () => {
                    this.axios.post('/app/mensajeria/ListarTipoPaquete', {})
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.listaTipoPaquete = resp.data.json
                                this.selectTipoPaquete.Codigo = resp.data.json[0].Codigo
                                this.selectTipoPaquete.Descripcion = resp.data.json[0].Descripcion
                            }
                        })
                },
                ListarHospitales: () => {
                    this.axios.post('/app/mensajeria/ListarHospitales', {})
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.listaHospital = resp.data.json
                                this.listaHospital.filter((hospital) => {
                                    if (hospital.Codigo == this.sesion.sesion_sucursal) {
                                        this.selectHospitalEnvia = hospital
                                    }
                                })
                            }
                        })
                },
                DetallesPaquete: (id) => {
                    this.axios.post('/app/mensajeria/DetallesPaquete', { IdPaquete: id })
                        .then(resp => {
                            this.detallesPaqueteEnvio = resp.data.json;
                        })
                },
                Tracking: (id) => {
                    this.axios.post('/app/mensajeria/Tracking', { IdPaquete: id })
                        .then(resp => {
                            this.infoTracking = resp.data.json[0]
                            var togo = parseInt(this.infoTracking.Estado)
                            this.$refs["tracking"].goTo(togo)
                        })
                },
                ReporteGuia: (objeto) => {
                    this.$reporte_modal(
                        {
                            Nombre: "Guia Paquete",
                            Opciones: {
                                nombrereporte: "guiapaquete",
                                IdPaquete: objeto.IdPaquete,
                                Estado: objeto.Estado
                            }
                        })
                }
            }
        },
        Agregar() {
            return {
                AgregarDetallePaquete: () => {
                    this.detallesPaqueteEnvio.push(this.detallePaquete)
                    this.detallePaquete.Paquetes = this.detallePaquete.Descripcion + "~" + this.detallePaquete.Cantidad
                    this.detallePaquete = { Codigo: (this.codigoDetallePaquete += 1), Cantidad: 1, Descripcion: null }
                },
                AgregarPaquete: (invalid) => {
                    if (!invalid) {
                        if (this.detallesPaqueteEnvio.length > 0) {
                            const arrayTemp = []

                            this.detallesPaqueteEnvio.forEach(element => arrayTemp.push(element["Paquetes"]))
                            this.info.Paquetes = arrayTemp.join('|')
                        }

                        this.info.HospitalEntrega = this.selectHospitalEntrega.Codigo
                        this.info.HospitalEnvia = this.selectHospitalEnvia.Codigo
                        this.info.TipoPaquete = this.selectTipoPaquete.Codigo

                        this.axios.post('/app/mensajeria/IngresarPaquete', this.info)
                            .then(resp => {
                                if (resp.data.codigo == 0) {
                                    this.ventana.mostrar = false
                                    this.Consulta().init()
                                    this.Otros().LimpiarObjeto()
                                    this.barra.mostrar = true
                                }
                            })
                    } else {
                        this.$vs.notify({
                            title: "Error al modificar cita",
                            text: "Debe ingresar todos los campos correctamente",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                    }
                }
            }
        },
        Eliminar() {
            return {
                EliminarElemento: (codigo) => {
                    this.detallesPaqueteEnvio = this.detallesPaqueteEnvio.filter((item) => item.Codigo !== codigo)
                },
                EliminarPaquete: (IdPaquete) => {
                    this.$vs.dialog({
                        type: 'confirm',
                        color: '#ED8C72',
                        acceptText: 'Eliminar',
                        title: 'Eliminar Paquete',
                        text: 'Desea eliminar paquete #' + IdPaquete,
                        clientWidth: 100,
                        accept: () => {
                            this.axios.post('/app/mensajeria/EliminarPaquete', { IdPaquete: IdPaquete })
                                .then(resp => {
                                    if (resp.data.codigo == 0) {
                                        this.Consulta().init()
                                        this.$vs.notify({
                                            title: "Eliminar paquete",
                                            text: "Paquete eliminado correctamente.",
                                            iconPack: 'feather',
                                            icon: 'icon-alert-circle',
                                            color: 'success',
                                            position: 'top-center'
                                        })
                                    }
                                })
                        }
                    })

                }
            }
        },
        Otros() {
            return {
                AbrirVentana: (opcion, objeto = null) => {
                    this.ventana.opcion = opcion
                    this.barra.mostrar = false

                    if (opcion == 1) {
                        this.ventana.mostrar = true
                        this.ventana.titulo = 'Crear Paquete'
                        this.Consulta().ListarTipoPaquete()
                        this.Consulta().ListarHospitales()
                        this.Otros().LimpiarObjeto()
                    } else if (opcion == 2) {
                        this.ventana.mostrar = true
                        this.infoDetalle = objeto
                        this.ventana.titulo = 'Detalles Paquete #' + this.infoDetalle.IdPaquete
                        this.Consulta().DetallesPaquete(this.infoDetalle.IdPaquete)
                    } else if (opcion == 3) {
                        this.ventana.mostrar = true
                        this.ventana.titulo = 'Tracking Paquete #' + objeto.IdPaquete
                        this.Consulta().Tracking(objeto.IdPaquete)
                    }
                },
                CambiarTipoPaquete: () => {
                    if (this.selectTipoPaquete.Descripcion !== "Otros") {
                        this.info.DescripcionTipoPaquete = null
                    }
                },
                LimpiarObjeto: () => {
                    this.info = {
                        HospitalEnvia: null,
                        AreaEnvia: null,
                        FechaEntrega: null,
                        HoraEntrega: null,
                        HospitalEntrega: null,
                        CorporativoEntrega: null,
                        AreaEntrega: null,
                        TipoPaquete: null,
                        DescripcionTipoPaquete: null,
                        Paquetes: null
                    }

                    this.detallesPaqueteEnvio = []
                    this.selectHospitalEntrega = null
                    this.selectHospitalEnvia = null
                },
                VolverVentanaPrincipal: () => {
                    this.barra.mostrar = true
                    this.ventana.mostrar = false
                }
            }
        },
        nextClicked() {
            return true
        },
        backClicked() {
            return true //no hay restricción para regresar
        }

    },
    mounted() {

    }
}
</script>
<style scoped>

.container {
    display:flex;
    margin-left: -20px;
    justify-content:center;
    align-items:center;
}

.notificacion {
    text-align: center;
    font-size: 25px;
    height: 40px;
    color: rgba(var(--vs-primary), 0.7);
    margin-left: 10px;
    cursor: pointer;
    position: relative;
}

.notificacion:hover {
    color: rgba(var(--vs-primary), 1);
    ;
}

.notificacion .titulo {
    font-weight: bold;
    font-size: 11px;
    position: relative;
    top: -5px;
}

.notificacion .notificaciones {
    font-size: 10px;
    background: #E74C3C;
    border-radius: 100%;
    position: absolute;
    top: 0px;
    right: -2px;
    height: 20px;
    min-width: 20px;
    padding: 5px;
    text-align: center;
    color: white;
    line-height: 11px;
}
</style>