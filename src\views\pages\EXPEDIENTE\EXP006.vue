<template>
        <ValidationObserver v-if="SerieAdmision && CodigoAdmision" ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
            <div class = "w-full" >
            <ValidationProvider rules="required|max:8000" v-slot="{ errors }" class="required">
                <vs-textarea label="Motivo de la Consulta" counter="8000" maxlength="8000" counter-danger.sync="false" v-model="MotivoConsulta" rows="5" />
                <span style="position:relative; top:-18px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
            </ValidationProvider>
            </div>
            <vs-divider/>
            <div class = "w-full">
            <ValidationProvider name="tipo" rules="required|max:8000" v-slot="{ errors }" class="required">
                <vs-textarea label="Historia de la enfermedad" v-model="HistoriaEnfermedad"  counter = "8000" rows="5" />
                <span style="position:relative;top:-18px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
            </ValidationProvider>
            </div>
            <vs-divider/>
            <vs-button clientWidth="150" color="success" style="float:right" type="filled" icon-pack="feather" icon="icon-save" @click="handleSubmit(Aceptar)" :disabled = "invalid"> Guardar</vs-button>
            <vs-button clientWidth="150" color="danger" style="float:right" type="border" @click="Cancelar"> Salir </vs-button>
        </ValidationObserver>
</template>
<script>
export default {
    name:'RegistroExpediente',
    data() {
        return {
            MotivoConsulta: '',
            HistoriaEnfermedad: '',
        }
    },
    props: {
        SerieAdmision: null,
        CodigoAdmision: null,
        CodigoPaciente: null,
    },
    methods: {
        IngresarExpediente() {
            this.axios.post("/app/v1_ExpedienteEvolucion/InsertarExpedienteIngreso", 
                {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    MotivoConsulta: this.MotivoConsulta,
                    HistoriaEnfermedad: this.HistoriaEnfermedad,
                    CodigoPaciente: this.CodigoPaciente
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.MotivoConsulta = ''
                        this.HistoriaEnfermedad = ''
                        let objmsg = {
                            SerieAdmision : this.SerieAdmision,
                            CodigoAdmision: this.CodigoAdmision,
                            NumeroExpediente: resp.data.correlativo
                        }
                        this.$emit('onSaved', objmsg)
                    }
                })
        },
        Cancelar(){
            this.$emit('onCancel')
        },
        Aceptar() {
            ///¿validaciones?
            this.IngresarExpediente()
        },
    }
}
</script>