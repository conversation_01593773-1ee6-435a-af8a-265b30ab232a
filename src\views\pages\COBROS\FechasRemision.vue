<template>
<div class="fechas-reclamos-container">
    <details open class="vs-card">
        <summary>
            Busqueda por Admisión / Documento
        </summary>
        <vs-card>

            <BusquedaAdmision ref="buscadorAdmision" class="w-full" v-bind="filtrosBusquedaAdmision" @datos_admision="BuscarAdmision" @cargaAdmision="CargarRemision" @limpiar_datos_admision="LimpiarBusqueda" />
            <div class="flex flex-wrap">
                <label class="vs-input--label mr-2 w-full">Seguro:</label>
                <vs-input class="w-full md:w-24 p-1" :value="formData?.Casa" disabled></vs-input>
                <vs-input class="w-full md:w-3/4 p-1" :value="formData?.SegNombre" disabled></vs-input>
            </div>
            <vs-alert v-if="!formData?.Codigo && formData.Admision" color="danger" icon="new_releases">
                <span>Admision no posee una remisión al seguro</span>
            </vs-alert>

        </vs-card>
    </details>
    <form @submit="handleSubmit">
        <dx-form 
            class="remision-form" ref="formRemisiones" 
            :form-data.sync="formData" 
            :items="formItems" label-location="left" 
            label-mode="outside" 
            validation-group="validacionRemision" 
            :col-count="2" 
            :col-count-by-screen="{sm:1, md:2}" 
            required-message="{0} es obligatorio" 
            :disabled="false" 
            :customize-item="customizeItem"
            :read-only="modo=='LECTURA'">
            
        </dx-form>
    </form>
</div>
</template>

<script>
import {
    dateTimeOptions
} from './data'
import BusquedaAdmision from '../../../components/sermesa/modules/admisiones/BusquedaAdmision.vue'

export default {
    name: 'asigna-fechas-reclamos',
    components: {
        BusquedaAdmision,
    },
    data() {
        return {
            formData: null,
            modo: 'LECTURA',
            formItems: [{
                    itemType: 'group',
                    caption: 'Fase #1',
                    colCount: 1,
                    items: [{
                            label: {
                                text: 'Traslado de la Base a Cobros'
                            },
                            dataField: 'TrasladoBaseCobros',
                            ...dateTimeOptions,
                            cssClass: 'date-width',
                        },
                        {
                            label: {
                                text: 'Cobros envía a Aseguradora'
                            },
                            dataField: 'FechaEntrega',
                            ...dateTimeOptions,
                            cssClass: 'date-width',
                        },
                        {
                            label: {
                                text: 'Recibido Aseguradora (Sello)'
                            },
                            dataField: 'FechaRecibido',
                            ...dateTimeOptions,
                            cssClass: 'date-width',
                        },
                        {
                            label: {
                                text: 'Aseguradora Devuelve por Ajustes',
                            },
                            dataField: 'DevuelveAsegLiq',
                            ...dateTimeOptions,
                            cssClass: 'danger date-width',
                        },
                    ]
                },

                {
                    itemType: 'group',
                    caption: 'Fase #2',
                    items: [{
                            label: {
                                text: 'Liquidaciones Devuelve el Reclamo a la Base',
                            },
                            dataField: 'DevuelveLiqSeg',
                            ...dateTimeOptions,
                            cssClass: 'date-width',
                        },
                        {
                            label: {
                                text: 'Base Devuelve el Reclamo a Cobros',
                            },
                            dataField: 'NuevoTrasladoBaseCobros',
                            ...dateTimeOptions,
                            cssClass: 'date-width',
                        },
                        {
                            label: {
                                text: 'Cobros/Base Traslada a Aseguradoras',
                            },
                            dataField: 'NuevaEntrega',
                            ...dateTimeOptions,
                            cssClass: 'date-width',
                        },

                        {
                            label: {
                                text: 'Recibido Aseguradora (Nuevo Sello)',
                            },
                            dataField: 'NuevoFechaRecibido',
                            ...dateTimeOptions,
                            cssClass: 'date-width',
                        },
                    ]
                },

                {
                    name: 'grabar',
                    colSpan: 2,
                    itemType: 'button',
                    buttonOptions: {
                        text: 'Guardar',
                        type: 'success',
                        icon: 'save',
                        useSubmitBehavior: true,
                        disabled: false,
                    },
                    horizontalAlignment: 'center',
                    visible: false,
                },
                {
                    name: 'editar',
                    colSpan: 2,
                    itemType: 'button',
                    buttonOptions: {
                        text: 'Editar',
                        type: 'default',
                        icon: 'edit',
                        useSubmitBehavior: false,
                        disabled: false,
                        onClick: ()=> {
                            this.modo = 'EDICION'
                            this.fromInstaceRemisiones.repaint()
                        }
                    },
                    horizontalAlignment: 'center',
                    visible: true,
                },
                {
                    name: 'cancelar',
                    colSpan: 2,
                    itemType: 'button',
                    buttonOptions: {
                        text: 'cancelar',
                        type: 'danger',
                        icon: 'close',
                        useSubmitBehavior: false,
                        disabled: false,
                        onClick: ()=> {
                            this.modo = 'LECTURA'
                            this.CargarRemision()
                        }
                    },
                    horizontalAlignment: 'center',
                    visible: true,
                },
            ],
            filtrosBusquedaAdmision: {
                api_filtro: {
                    'Opcion': 'CONSULTA',
                    'SubOpcion': 'ADMISION',
                },
                api_campos: ['Serie', 'Codigo', 'Nombres', 'Apellidos', ],
                api_titulos: ['Serie', 'Admision', 'Nombres', 'Apellidos', ],
            },
        }
    },
    methods: {
        customizeItem(e) {

            if (this.formData.DevuelveAsegLiq && ['TrasladoBaseCobros', 'FechaEntrega', 'FechaRecibido'].includes(e.dataField))
                e.editorOptions = {
                    ...e.editorOptions,
                    readOnly: true,
                }
            if (e.name=='grabar')
                e.visible = this.modo == 'EDICION'

            if (e.name == 'editar')
                e.visible = this.modo == 'LECTURA' && this.formData.Codigo != null

            if (e.name == 'cancelar')
                e.visible = this.modo == 'EDICION'

        },
        CargarRemision() {

            this.axios.post('/app/v1_cobros/FechaRecibidoRemision',
                this.formData
            ).then((resp) => {
                if (resp.data.length > 0) {
                    this.formData = resp.data[0]
                }
            })
        },
        LimpiarBusqueda() {
            this.modo = 'LECTURA'
            this.formData = 
            {
                SerieAdmision: null,
                Codigo: null,
                Admision: null,
                Casa: null,
                SegNombre: null,
                Nombre: null,
                Apellido: null,
                ApellidoCasada: null,
                FechaEntrega: null,
                FechaRecibido: null,
                DevuelveAsegLiq: null,
                DevuelveLiqSeg: null,
                NuevaEntreg: null,
                TrasladoBaseCobros: null,
                NuevoTrasladoBaseCobros: null,
                NuevoFechaRecibido: null,
                Empresa: null,
            }
        },
        BuscarAdmision(e) {
            this.formData = {
                SerieAdmision: e.Serie,
                Admision: e.Codigo,
            }
            this.CargarRemision()
        },
        Grabar() {
            this.axios.post('/app/v1_cobros/ActualizarFechaRemision',
                this.formData
            ).then(()=>{
                this.modo = 'LECTURA'
                this.$refs.buscadorAdmision.limpiar_campos()
                this.$nextTick(()=>{
                    this.$refs.buscadorAdmision.$el.querySelector("input").focus()
                })
            })
        },
        handleSubmit(e) {
            e.preventDefault()
            this.Grabar()
        },
    },
    computed: {
        fromInstaceRemisiones() {
            return this.$refs['formRemisiones'].instance
        },
    },
    created() {
        this.LimpiarBusqueda()
    },

}
</script>

<style>
.fechas-reclamos-container .vs-inputx,
.fechas-reclamos-container .vs-input--input,
.fechas-reclamos-container .vs-input--input.normal {
    background: #fff !important;
    border: 1px solid rgba(0, 0, 0, 0.2) !important;
    height: 34px !important;
}

.fechas-reclamos-container .includeIconOnly {
    width: 34px !important;
    height: 34px !important;
}

.fechas-reclamos-container .remision-form .danger .dx-field-item-label-text {
    color: maroon;
    font-weight: bold;
}

.date-width .dx-field-item-label-content { 
   width: 100%;
   min-width: 150px;
   max-width: fit-content(100%);
   white-space: normal;  
} 

.date-width .dx-field-item-label-content label,
.date-width .dx-field-item-label-content span
{
    width: 100% !important;
}

.date-width .dx-field-item-content
{
    min-width: 190px;
    max-width: 350px;
}
</style>
