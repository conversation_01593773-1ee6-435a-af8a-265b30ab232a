<template>
  <vx-card title="Listado Pedidos Sugeridos">
    <vs-row>
      <vs-col vs-justify="center" vs-align="center" vs-w="4" id="columna2">
        <!--<vs-button v-if="$validar_privilegio('VER').status" id="button-with-loading" color="primary" icon-pack="feather" icon="icon-edit">asdf</vs-button>-->
        <p>Del:</p>
          <flat-pickr  :config="configdateTimePicker" v-model="FechaDel"></flat-pickr>
        <p>Al:</p>
          <flat-pickr :config="configdateTimePicker" v-model="FechaAl" ></flat-pickr>
      </vs-col>
      <vs-col vs-justify="center" vs-align="center" vs-w="4" id="columna2">
        <p>
          <b>Filtrado por:</b>
        </p>
        <ul class="leftx" id="tipobodega">
          <li>
            <vs-radio v-model="ClasePedido" vs-name="ClasePedido" vs-value="PS">Pedido Sugerido</vs-radio>
          </li>
          <li>
            <vs-radio v-model="ClasePedido" vs-name="ClasePedido" vs-value="PSC">Pedido Sugerido Consolidado</vs-radio>
          </li>
          <li>
            <vs-radio v-model="ClasePedido" vs-name="ClasePedido" vs-value="TD">Todos los pedidos</vs-radio>
          </li>
        </ul>
      </vs-col>
      <vs-col vs-justify="center" vs-align="center" vs-w="4" id="columnaTipoB">
        <p>
          <b>Tipo de Bodega:</b>
        </p>
        <ul class="leftx" id="tipobodega">
          <li v-for="Tipo in Tipos" :key="Tipo.Id">
            <vs-radio v-model="TipoBodega" vs-name="TipoBodega" :vs-value="Tipo">{{Tipo.Nombre}}</vs-radio>
          </li>
        </ul>
      </vs-col>
    </vs-row>
    <vs-row>
      <vs-col>
        <vs-button
          color="green"
          type="filled"
          @click="VerListadoPedidoSugerido"
          icon="cached"
        >Ver Listado Pedidos</vs-button>
      </vs-col>
    </vs-row>
    <vs-row>
      <vs-col vs-justify="center" vs-align="center" vs-w="12" id="columna2">
        <vs-divider>Listado de Pedidos Sugeridos</vs-divider>
        <template lang="html">
          <div>
            <vs-table max-items="15" pagination :data="ListadoPedidos" noDataText="Sin datos disponibles">
              <template slot="thead">
                <vs-th>Pedido</vs-th>
                <vs-th>Nombre de la Bodega</vs-th>
                <vs-th>Es Consolidado?</vs-th>
                <vs-th>Fecha</vs-th>
                <vs-th>Tipo Pedido</vs-th>
                <vs-th>Solicitante</vs-th>
                <vs-th>Acciones</vs-th>
              </template>
              <template slot-scope="{data}">
                <vs-tr :key="indextr" v-for="(tr, indextr) in data">
                  <vs-td :data="tr.CodigoPS">{{tr.CodigoPS}}</vs-td>
                  <vs-td :data="tr.NombreBodDestino">{{tr.NombreBodDestino}}</vs-td>
                  <vs-td :data="tr.ClasePedido">{{tr.ClasePedido}}</vs-td>
                  <vs-td :data="tr.Fecha">{{tr.Fecha}}</vs-td>
                  <vs-td :data="tr.NombreTipoPedido">{{tr.NombreTipoPedido}}</vs-td>
                  <vs-td :data="tr.NombreUsuario">{{tr.NombreUsuario}}</vs-td>
                  <vs-td :data="tr.NombreUsuario">
                    <vs-button
                      type="filled"
                      icon="add"
                      >Consolidado</vs-button>
                    <vs-button
                      type="filled"
                      @click="VerPedido(tr)"
                      icon="visibility"
                      >Ver</vs-button>
                  </vs-td>
                </vs-tr>
              </template>
            </vs-table>
          </div>
        </template>

      </vs-col>
    </vs-row>
    <!------------------ POPUP VER PEDIDO INICIO ------------------>

    
    <vs-popup fullscreen title="Revisión de Pedidos Sugeridos" :active.sync="popupActivo4" >

        <h1>Revisión de Pedidos Sugeridos</h1>
        <vs-row>
          <vs-col  class="columnas col1"  vs-justify="center" vs-align="center" vs-w="4">
            <p>
              <b>Pedido:</b>
              {{PedidoInfo.CodigoPS}}
            </p>
            <p>
              <b>Hospital:</b>
              {{PedidoInfo.EmpresaSucursal}}
            </p>
            <p>
              <b>Clase:</b>
              
            </p>
            <p>
              <b>Fecha:</b>
              {{PedidoInfo.Fecha}}
            </p>
            <p>
              <b>Solicitante:</b>
              {{PedidoInfo.NombreUsuario}}
            </p>
            <p>
              <b>Bodega Destino:</b>
              {{PedidoInfo.NombreBodegaDestino}}
            </p>
            <p>
              <b>Número de Movimiento:</b>
              
            </p>
          </vs-col>
          <vs-col class="columnas col2"  vs-justify="center" vs-align="center" vs-w="3">
            <p>
              <b>Tipo:</b>
              {{PedidoInfo.NombreTipoPedido}}
            </p>
            <p>
              <b>Observaciones:</b>
              {{PedidoInfo.Observaciones}}
            </p>
          </vs-col>
        </vs-row>
        
        <p>
            <datatable
              title="Detalle Pedido"
              :columns="tableColumns1"
              :rows="DetallePedido"
              locale="es"
              
            >
            </datatable>
        </p>
      
    </vs-popup>
    <!------------------ POPUP VER PEDIDO FIN ------------------>
  </vx-card>
</template>

<script>
import flatPickr from 'vue-flatpickr-component';
import 'flatpickr/dist/flatpickr.css';
import DataTable from "vue-materialize-datatable";



export default {
  data() {
    return {
      configdateTimePicker: {
                allowInput: true,
                dateFormat: 'Y-m-d'
      },
      PedidoInfo:[],
      popupActivo4:false,
      FechaDel: '2020-06-28',
      FechaAl: '2020-07-03',
      ClasePedido:'PS',
      TipoBodega: {},
      ListadoPedidos:[],
      DetallePedido:[],
      tableColumns1: [
        {
          label: "Linea",
          field: "Linea",
          numeric: false,
          html: false
        },
        {
          label: "Producto",
          field: "Producto",
          numeric: false,
          html: false
        },
        {
          label: "Nombre Producto",
          field: "Nombre",
          numeric: false,
          html: false
        },
        {
          label: "UMedida",
          field: "UnidadMedida",
          numeric: false,
          html: false
        },
        {
          label: "CPM",
          field: "PromUso",
          numeric: false,
          html: false
        },
        {
          label: "Existencia",
          field: "Existencia",
          numeric: false,
          html: false
        },
        {
          label: "Costo Ultimo",
          field: "CostoUltimo",
          numeric: false,
          html: false
        },
        {
          label: "Total",
          field: "Total",
          numeric: false,
          html: false
        },
        {
          label: "PedidoSugerido",
          field: "PedidoSugerido",
          numeric: false,
          html: false
        },
        {
          label: "Mínimo",
          field: "Minimo",
          numeric: false,
          html: false
        }
        ,
        {
          label: "Máximo",
          field: "Maximo",
          numeric: false,
          html: false
        }
      ],
      Tipos: [
        {
          CodigoAgrupacion: 2,
          Tipo: "M",
          Nombre: "Medicamentos"
        },
        {
          CodigoAgrupacion: 6,
          Tipo: "R",
          Nombre: "Repuestos y Suministros"
        },
        {
          CodigoAgrupacion: 7,
          Tipo: "Z",
          Nombre: "Limpieza"
        },
        {
          CodigoAgrupacion: 2,
          Tipo: "D",
          Nombre: "Diagnostico"
        },
        {
          CodigoAgrupacion: 3,
          Tipo: "L",
          Nombre: "Libreria"
        },
        {
          CodigoAgrupacion: 4,
          Tipo: "A",
          Nombre: "Cocina"
        }
      ]
    };
  },
  methods: {
    VerListadoPedidoSugerido() {
      
      
      
      this.axios
        .post("/app/requerimientos/VerListadoPedidoSugerido", {
          ClasePedido: this.ClasePedido,
          TipoPS: this.TipoBodega.Tipo,
          FechaDel:this.FechaDel,
          FechaAl:this.FechaAl
        })
        .then(resp => {
          this.ListadoPedidos = resp.data.json;
          
        })
        .catch();
    },
    //Fin VerListadoPedidoSugerido
    VerPedido(info){
      this.popupActivo4=true
      this.PedidoInfo=info
      
      this.axios
        .post("/app/requerimientos/VerDetallePedidoSugerido", {
          CodigoPS: this.PedidoInfo.CodigoPS
        })
        .then(resp => {
          this.DetallePedido = resp.data.json;
          
          
        })
        .catch();
    },
    CerrarVerPedido(){
      this.PedidoInfo=null
    }
  },
  created() {},
  components: {
    flatPickr,
    "datatable": DataTable
  }, 
  mounted() {
    this.TipoBodega=this.Tipos[4]
  },
  computed: {
  
  }
};
</script>




<style lang="stylus">

.popup-example {
  .vs-input {
    float: left;
    width: 50%;
    margin: 10px;
    margin-top: 5px;
  }

  .con-select {
    margin-left: 10px;
    width: 50%;
    margin-bottom: 10px;
  }
}
@import url(http://fonts.googleapis.com/icon?family=Material+Icons);
</style> 

<style>
.columnas{
  padding: 35px 15px 15px 15px; 
}  
  .con-vs-popup .vs-popup {
  width: 100% !important;
  height: auto !important;
}
</style>