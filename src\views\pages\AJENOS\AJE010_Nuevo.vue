<template>
<div>
    <vs-popup ref="buscador" title="Listado de Polizas" :active.sync="poliza.mostrar" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
        <!-- {{polizaListado}} -->
        <vs-table2 ref="tabla" maxItems="10" search tooltip pagination :data="polizaListado">
            <template slot="header">
                <vs-button color="primary" size="default" @keyup.esc="poliza.mostrar=false" @click="mostrarSeleccionados=!mostrarSeleccionados" style="height:35px; margin-right:4px">{{(mostrarSeleccionados)?'Mostrar Todo':`Mostrar Seleccionados (${polizaSeleccionadas})`}}</vs-button>
                <vs-button color="orange" size="default" @keyup.esc="poliza.mostrar=false" @click="Actualizar().seleccion(polizaSeleccionadas!=poliza.listado.length)" style="height:35px; margin-right:4px">{{(polizaSeleccionadas!=poliza.listado.length)?'Seleccionar Todos':'Deseleccionar Todos'}}</vs-button>
            </template>
            <template slot="thead">
                <th>Asegurador</th>
                <th>Código</th>
                <th>Poliza</th>
                <th>Seleccionar</th>
            </template>
            <template slot-scope="{data}">
                <tr :key="indextr" v-for="(tr, indextr) in data" :is-selected="tr.Seleccion" not-click-selected>
                    <vs-td2 width="290px">
                        {{ data[indextr].NombreAseguradora }}
                    </vs-td2>

                    <vs-td2 width="50px">
                        {{ data[indextr].Codigo }}
                    </vs-td2>

                    <vs-td2>
                        {{ data[indextr].Nombre }}
                    </vs-td2>

                    <vs-td2 width="50px">
                        <vs-checkbox class="tabla" :val="tr" v-model="tr.Seleccionar" />
                    </vs-td2>
                </tr>
            </template>
        </vs-table2>
        <div>
            <vs-button color="success" style="float:right" type="filled" @click="poliza.mostrar=false">Aceptar</vs-button>
        </div>
    </vs-popup>

    <vs-alert v-if="!nuevo && (!editarFiltro || !editarFiltro.info)" color="danger" style="font-size:11px">No cuenta con los permisos para crear filtros</vs-alert>
    <ValidationObserver v-else ref="formValidate" v-slot="{ invalid,handleSubmit }" mode="lazy">
        <div class="flex mb-5">
            <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5 p-1">
                <ValidationProvider name="Admisión" rules="required" v-slot="{ errors }">
                    <SM-Buscar label="Tipo de Admisión" v-model="info.tipoAdmision" :api="tipoAdmision" :api_campos="['Nombre']" :api_titulos="['Nombre']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="true" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                </ValidationProvider>
            </div>
            <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5 p-1">
                <ValidationProvider name="Descripción" rules="required|max:70" v-slot="{ errors }">
                    <vs-input label="Descripción" class="w-full" count="70" v-model="info.descripcion" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                </ValidationProvider>
            </div>
        </div>
        <div>
            <vs-button style="float:right" @click="handleSubmit(Guardar().filtro())" :disabled="invalid">Guardar Filtros</vs-button>
            <vs-button v-if="info.tipoAdmision==='2'" class="mr-2" color="primary" style="float:right" type="filled" @click="poliza.mostrar = true">Asociar Pólizas ({{polizaSeleccionadas}})</vs-button>
            <br><br>
        </div>
    </ValidationObserver>
</div>
</template>

<script>
import bitacora from '@/components/sermesa/funciones/SMBitacora'
export default {
    data() {
        return {
            mostrarSeleccionados: false, //Inidica si se muestran solo los seleccionados
            poliza: {
                pagina: null,
                mostrar: false, //Muestra el listado de Polizas
                busqueda: null,
                filtrarSeleccionados: false, //Muestra solo las polizas seleccionadas
                listado: []
            },
            info: {
                IdFiltro: null,
                tipoAdmision: null,
                descripcion: null,
            },
        }
    },
    props: {
        polizas: {
            default: []
        },
        tipoAdmision: {
            default: []
        },
        editarFiltro: {
            default: null
        },
        callback: {
            default: null
        },
        nuevo: {
            default: false
        }
    },
    computed: {
        polizaSeleccionadas() {
            return this.poliza.listado.filter(f => f.Seleccionar).length
        },
        polizaListado() {
            return (!this.mostrarSeleccionados) ? this.poliza.listado : this.poliza.listado.filter(f => f.Seleccionar)
        }
    },
    watch: {
        polizas(value) {
            value.map(m => {
                this.poliza.listado.push({
                    ...m
                })
            })
        },
        // 'poliza.mostrar'(value) {
        //     if (!value) {
        //         setTimeout(() => {
        //             this.$refs.tabla.busqueda = null
        //             this.mostrarSeleccionados = false
        //             this.poliza.filtrarSeleccionados = false
        //             this.$emit('update:editarFiltro', null)
        //         }, 100)
        //     }
        // },
        'editarFiltro.info'(value) {
            if (value) {
                this.info.IdFiltro = value.IdFiltro
                this.info.tipoAdmision = value.TipoAdmision
                this.info.descripcion = value.Descripcion.trim()

                this.Consulta().polizas(value.IdFiltro)
                    .then((resp) => {
                        const datos = resp.data.json.map(m => m.CodigoSeguro)
                        this.poliza.listado.map(m => {
                            m.Seleccionar = datos.indexOf(m.Codigo) >= 0
                        })
                        this.mostrarSeleccionados = datos.length > 0
                    })
                bitacora.registrar(this.info,{IdFiltro:value.IdFiltro})

            }
        }
    },
    methods: {
        Consulta() {
            return {
                polizas: (id) => {
                    return this.axios.post('/app/ajenos/FiltrosCargosListadoPolizasSeleccionadas', {
                        IdFiltro: id
                    })
                }
            }
        },
        Guardar() {
            return {
                filtro: () => {
                    const info = this.info
                    const polizas = this.poliza.listado.filter(f => f.Seleccionar)
                    info.Bitacora2 = bitacora.obtener()
                    // Se comenta para evitar que impida el cargo previo a la autorización de pp
                    // if (info.tipoMedico === 'M' && info.prontoPago === '1') info.autorizadoPP = true
                    if (info.tipoAdmision === '2') {
                        if (polizas.length === 0) {
                            this.$vs.notify({
                                time: 4000,
                                title: 'Filtro de Cargos',
                                text: "Debe de seleccionar una o más pólizas",
                                iconPack: 'feather',
                                icon: 'icon-alert-circle',
                                color: 'danger',
                                position: 'bottom-right'
                            })
                            return false
                        }
                        info.polizas = polizas
                    }

                    //Si la admision es tipo 1 se eliminan las polizas
                    if (info.tipoAdmision == 1) info.polizas = null
                    
                    this.axios.post('/app/ajenos/FiltrosInterpretacionNuevoFiltro', info)
                        .then(() => {
                            this.info.tipoAdmision = null
                            this.info.descripcion = null
                            this.Actualizar().seleccion(false)
                            this.callback()
                            if (this.editarFiltro) this.$emit('update:editarFiltro', {
                                mostrar: false,
                                info: null
                            })
                            
                        })
                        .catch(() => {
                            
                        })
                }
            }
        },
        Editar() {
            return {
                poliza: () => {
                    const polizas = this.poliza.listado.filter(f => f.Seleccionar)
                    const info = {
                        IdFiltro: this.editarPoliza,
                        polizas
                    }
                    this.axios.post('/app/ajenos/FiltrosCargosEditarPolizas', info)
                        .then(() => {
                            this.poliza.mostrar = false
                            // setTimeout(() => {
                            this.Actualizar().seleccion(false)
                            this.callback()
                            // }, 100)
                        })
                }
            }
        },
        Actualizar() {
            return {
                seleccion: (todos) => {
                    this.poliza.listado.map(m => m.Seleccionar = todos)
                }
            }
        }
    },
    mounted() {
        this.polizas.map(m => {
            this.poliza.listado.push({
                ...m
            })
        })
        // console.log(this.poliza.listado)
    }
}
</script>
