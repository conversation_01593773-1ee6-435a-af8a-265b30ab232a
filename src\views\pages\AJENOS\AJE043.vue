<template>
    <vx-card title="Mantenimiento de Productos y Servicios No Autorizados">
        <div class="container">

            <div class="a-qQoBr">
                <vs-row>
                    <vs-col vs-type="flex" vs-w="6" class="m-4">

                        <div class="ws-full">
                            <vs-input label="Código" class="ws-full" v-model="CodigoSeguro" readonly />
                        </div>

                        <!--Se agrega envio parametro  Aseguradora--> 
                        <SM-Buscar  class="w-full" 
                                    label="Aseguradora"
                                    v-model="CodigoSeguro" 
                                    :disabled_busqueda="activarBusquedaProd"
                                    api="app/Ajenos/ConsultaCodigosAseguradoras" 
                                    api_campo_respuesta_mostrar="Aseguradora"
                                    :api_campos="['Codigo', 'Aseguradora']" 
                                    :api_titulos="['Codigo', 'Aseguradora']" 
                                    api_campo_respuesta="Codigo"
                                    :api_campo_respuesta_estricto="false"
                                    :api_preload="true" 
                                    :disabled_texto="true"
                                    :api_filtro="{
                                        Aseguradora:'', 
                                        PolizaPlanDirecto: null
                                    }" 
                                    
                                    
                            :callback_buscar="CosultarDatosPoliza().Consultar" >
                        </SM-Buscar>
                    </vs-col>

                </vs-row>




            </div>


            <div class="a-K8Gq2">

                <vs-row>
                    <vs-col vs-type="flex" vs-w="6" class="m-4">

                        <div class="ws-full">
                            <vs-input label="Producto" class="w-full" v-model="this.CodigoProducto" readonly/>
                        </div>

                        <SM-Buscar class="w-full" 
                                    label="Descripción Producto" 
                                    v-model="CodigoProducto"
                                    :disabled_busqueda="activarBusquedaProd" 
                                    api="app/inventario/ListaProductosInventario"
                                    api_campo_respuesta_mostrar="Nombre" 
                                    :api_campos="['Codigo', 'Nombre']"
                                    :api_titulos="['Codigo', 'Nombre']" 
                                    api_campo_respuesta="Codigo"
                                    :api_campo_respuesta_estricto="false" 
                                    :api_preload="false"
                                    :disabled_texto="true" 
                                    :api_filtro="{
                                        Nombre: this.TextoBusqueda,
                                        Tipo: null,
                                        Activo: null
                                    }" 
                                    :mostrar_busqueda="true" 
                                     />

                    

                    </vs-col>

                    <vs-col vs-type="flex" vs-w="2" class="m-6 pt-4"  >
                        <vs-button @click="RegistrarProductoNoAutorizado">
                                            Guardar
                        </vs-button>
                    </vs-col>


                    

                </vs-row>

            </div>

            <div class="a-08MZm-1" style="overflow:scroll;height:100%;width:100%;overflow:auto; padding: 10px; ">

                <DxDataGrid :data-source="listaProductos" height="400" :show-borders="true" :column-auto-width="true"
                    :paging="{ enabled: true, pageSize: 10 }" :row-alternation-enabled="true"
                    :search-panel="{ visible: true }"
                    :ref="refGridProducts"                    
                    @rowClick="onRowClick"        
                    @contentReady="onContentReady"                
                >

                    <DxColumn caption="Código" data-field="Producto" />
                    <DxColumn caption="Nombre" data-field="Nombre" />
                    <DxColumn caption="Aseguradora" data-field="Aseguradora" />
                    <DxColumn caption="Tipo" data-field="Tipo" />                    
                    <DxColumn caption="Elilminar" type="buttons" :buttons="[{icon:'fa fa-trash',onClick:(e)=> { ElliminarProductoNoAutorizado(e, 'continuar') }, id: 'continuar'   }]" ></DxColumn>
                </DxDataGrid>

            </div>
            <div class="a-NOGwR">

                <div class="a-08MZm-1" style="overflow:scroll;height:200%;width:100%;overflow:auto; padding: 10px; ">

                    <DxDataGrid :data-source="bitacoraNoAutorizados" 
                                height="100"
                                :show-borders="true" 
                                :column-auto-width="true"
                                :paging="{ enabled: true, pageSize: 10 }" 
                                :row-alternation-enabled="true" 
                                :ref="refGridBitacora"                                
                    >
                        <DxColumn caption="Aseguradora" data-field="Asegura" />
                        <DxColumn caption="Producto" data-field="Producto" />
                        <DxColumn caption="FechaRegistro" data-field="FechaRegistro" />
                        <DxColumn caption="Usuario" data-field="Usuario" />
                    </DxDataGrid>

                    </div>
                
            </div>
        </div>


    </vx-card>
</template>


<script>


import {
    DxDataGrid,
    DxColumn,
} from 'devextreme-vue/data-grid';


const refGridProducts = ''
const refGridBitacora =''
export default {
    name: "ConfiguracionMaxMinsProductos",
    components: {
        DxDataGrid,
        DxColumn
    },
    data() {
        return {
            refGridBitacora,
            refGridProducts,
            CodigoSeguro: null,
            CodigoProducto: '',            
            activarBusquedaProd: false,
            listaProductos: [],
            bitacoraNoAutorizados: [],
            TextoBusqueda: ''

        }


    },
    computed: {
       
    },
    mounted() {

    },
    methods: {       
        async ConsultarProductosTipo(codigoSeguro) {
            
            const resp = await this.axios.post('/app/Ajenos/ConsultaProductosNoCubiertos', {
                Aseguradora: codigoSeguro
            })                        
            this.listaProductos = resp.data.json
        },
        CosultarDatosPoliza() {
            return {
                Consultar: () => {
                    this.ConsultarProductosTipo(this.CodigoSeguro)
                }
            }
        },
        async RegistrarProductoNoAutorizado(){

            
            if(!this.CodigoProducto){
                return
            }
                        
            await this.axios.post('/app/Ajenos/RegistrarProductoNoCubierto', {
                Producto: this.CodigoProducto, 
                Aseguradora :this.CodigoSeguro,
                Seguro: null,
                Cubierto:0
            })
            .then(resp => {
                if(resp.data.Codigo === 0){
                    this.listaProductos = []
                    this.listaProductos = resp.data.json
                }
            }).catch((error) => {
                this.$vs.dialog({
                                type: 'confirm',
                                color: 'danger',
                                title: 'Confirmación',
                                acceptText: 'Aceptar',
                                cancelText: 'Cancelar',
                                text: error.Descripcion,
                            })
            })            
        },
        async ElliminarProductoNoAutorizado(item){

            
            await this.axios.post('/app/Ajenos/EliminarProductoNoCubierto', {
                Producto: item.row.data.Producto, 
                Aseguradora : item.row.data.Aseguradora,                
            })
            .then(resp => {
                
                if(resp.data.Codigo === 0){     
                    this.listaProductos.splice(0, this.listaProductos.length, ...resp.data.json);
                    this.CodigoProducto = '';                                                    
                    this.listaProductos = []
                    this.listaProductos = resp.data.json
                }
                else { 
                        this.listaProductos = []

                }
            }).catch((error) => {
                this.$vs.dialog({
                                type: 'confirm',
                                color: 'danger',
                                title: 'Confirmación',
                                acceptText: 'Aceptar',
                                cancelText: 'Cancelar',
                                text: error.Descripcion,
                            })
            })    

        },
        async onRowClick(dataRow) {
        
            const resp = await this.axios.post('/app/Ajenos/ConsultaBitacoraNoCubiertos', {
                Aseguradora :dataRow.data.Aseguradora,
                Producto: dataRow.data.Producto
            }) 

            this.$refs[refGridBitacora].instance.option('dataSource', resp.data.json);

            this.$nextTick(() => {
                // Restaurar el pageIndex después de que el DOM se haya actualizado
                //gridInstance.pageIndex(currentPageIndex);
            });

        },
        onContentReady(){
            this.$refs[refGridBitacora].instance.option('dataSource', []);
        }
    
    }

}

</script>



<style scoped>
* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

.container {
    display: flex;
    flex-flow: row wrap;
    justify-content: flex-start;
    display: grid;
    grid-gap: 5px;
    width: 100%;
    height: 100%;
    grid-template-areas: "a-qQoBr"
        "a-K8Gq2"
        "a-08MZm-1"
        "a-NOGwR";
    grid-template-columns: 1fr;
    grid-template-rows: 0.2fr 0.2fr 1fr 0.20fr;
}

.container>div {
    border: 1px dashed #888;
}

.a-qQoBr {
    grid-area: a-qQoBr;
}

.a-K8Gq2 {
    grid-area: a-K8Gq2;
}

.a-08MZm-1 {
    grid-area: a-08MZm-1;
}

.a-NOGwR {
    grid-area: a-NOGwR;
}


.container>div {
    border: 1px dashed #888;
}

.a-qQoBr {
    grid-area: a-qQoBr;
}

.a-K8Gq2 {
    grid-area: a-K8Gq2;
}

.a-08MZm-1 {
    grid-area: a-08MZm-1;
}

.a-NOGwR {
    grid-area: a-NOGwR;
}

.container>div {
    border: 1px solid #888;
}

.container {
    max-width: 100%;
}

.dxgvSearchPanel>table {
    width: 100%;
}
</style>