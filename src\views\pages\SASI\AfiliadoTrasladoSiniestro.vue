<template>
<div class="afiliados-traslado-preexistencia-container">

    <DxDataGrid ref="GridPolizasCliente" :data-source="Polizascliente" v-bind="DataGridOptions" @row-dbl-click="(e)=> dataGrid.editRow(e.rowIndex)" @editor-prepared="(e)=> { if(e.dataField == 'Observacion') e.setValue ('','')}" @editing-start="(e)=>actual = e.data" @row-updating="Grabar">
        <!-- Editor prepared: pequeño truco para que al editar ejecute el validador si no hay cambios en la fila o no ingresa observaciones -->
        <DxDataGridEditing :allow-updating="true" :allow-adding="false" :allow-deleting="false" mode="popup">
            <DxDataGridPopup :title="`¿Esta seguro de trasladar las preexistencias hacia la póliza?`" height="410" width="550" :toolbarItems="PopUpToolBarItems" />
            <DxDataGridForm label-location="top" label-mode="static">
                <DxFormItem data-field="NumeroAdhesion" template="texto" :col-span="2" />
                <DxFormItem data-field="Observacion" :col-span="2" :validationRules="[{message:'Debe ingresar un motivo', type:'stringLength', min:4}]" editor-type="dxTextArea" :editor-options="{ height: '200', width: '100%',  } " />
            </DxDataGridForm>
        </DxDataGridEditing>
        <DxDataGridColumn data-field="NumeroAdhesion" caption="No Póliza/Adhesion" :allow-editing="false" />
        <DxDataGridColumn data-field="IdCliente" :allow-editing="false" />
        <DxDataGridColumn data-field="NombrePlan" :allow-editing="false" />
        <DxDataGridColumn data-field="EstadoAfiliado" caption="Status" :allow-editing="false" />
        <DxDataGridColumn data-field="UltimoPago" caption="Último Pago" width="100" :allow-editing="false" />
        <DxDataGridColumn data-field="Edad" width="50" :allow-editing="false" />
        <DxDataGridColumn data-field="PrimerNombre" :allow-editing="false" />
        <DxDataGridColumn data-field="SegundoNombre" :allow-editing="false" />
        <DxDataGridColumn data-field="PrimerApellido" :allow-editing="false" />
        <DxDataGridColumn data-field="SegundoApellido" :allow-editing="false" />
        <DxDataGridColumn data-field="FechaNacimiento" v-bind="DateColumnOptions" :allow-editing="false" />
        <DxDataGridColumn data-field="IdExternoAfiliado" caption="Código Asegurado" :allow-editing="false" />
        <DxDataGridColumn data-field="FechaInicioCobertura" v-bind="DateColumnOptions" :allow-editing="false" />
        <DxDataGridColumn data-field="FechaRegistro" v-bind="DateColumnOptions" format="dd/MM/yyyy HH:mm" :allow-editing="false" />
        <DxDataGridColumn data-field="Observacion" :visible="true" />
        <DxDataGridColumn type="buttons" :allow-hiding="false" :allow-reordering="false" :fixed="false" width="50" :buttons="[{
            icon:'movetofolder',
            hint:'Trasladar la preexistencia',
            onClick: (e)=> dataGrid.editRow(e.row.rowIndex),
        }]" />
        
        <template #texto>
            <div>
                <h4 class="mb-1">
                    ¿Esta seguro de trasladar las preexistencias del número de póliza <strong>{{ actual.NumeroAdhesion }}</strong> al número <strong>{{ NumeroAdhesion }}</strong>?
                </h4>
            </div>
        </template>
       
    </DxDataGrid>
    
</div>
</template>

<script>

import 'devextreme-vue/text-area'

import {
    DefaulGridOptions,
} from './data.js'

export default {
    name: 'AfiliadosTrasladoSiniestralidad',
    components: {

    },
    data() {
        return {
            actual: {},
            Observacion: '',
            Polizascliente: null,
            BitacoraCambioNombre: null,
            DataGridOptions: {
                ...DefaulGridOptions,
                height: 'auto',
                searchPanel: null,
                groupPanel: null,
                headerFilter: null,
                columnAutoWidth: false, //true,
                columnResizingMode: 'nextColumn',
                scrolling: {
                    showScrollbar: 'always',
                    useNative: true,
                },
                columnHidingEnabled: true,
                allowEditing: true,
            },
            DateColumnOptions: {
                dataType: 'datetime',
                format: "dd/MM/yyyy",
            },
            counterDanger: false,
            ConfirmDialog: false,

            PopUpToolBarItems: [{
                    widget: "dxButton",
                    toolbar: "bottom",
                    options: {
                        text: 'Si',
                        type: 'success',
                        useSubmitBehavior: true,
                        onClick: () => {
                            this.dataGrid.saveEditData()
                        }
                    },
                },
                {
                    widget: "dxButton",
                    toolbar: "bottom",
                    options: {
                        text: 'No',
                        type: 'danger',
                        useSubmitBehavior: true,
                        onClick: () => {
                            this.dataGrid.cancelEditData()
                        }
                    },
                },
            ],
        }
    },
    props: {
        IdAfiliado: String,
        IdCliente: String,
        NumeroAdhesion: String,
        IdExternoAfiliado: String,
    },
    methods: {
        Submit() {
            this.dataGrid.saveEditData()
        },
        Cargar() {
            ///cargar las folizas del cliente que no sean iguales al código de afiliado
            this.CargarPolizasCliente()
        },
        CargarPolizasCliente() {
            this.axios.post("/app/v1_afiliados/GetPolizasCliente", {
                    IdCliente: this.IdCliente,
                    ExcludeIdAfiliado: this.IdAfiliado,

                })
                .then((resp) => {
                    this.Polizascliente = resp.data.json.map(x => {
                        x.Observacion = ''
                        return x
                    })
                })
        },

        Grabar(e) {
            e.cancel = 
            this.axios.post("/app/v1_afiliados/ActualizarPreexistencia", {
                IdCliente: this.IdCliente,
                AdhesionNueva: this.NumeroAdhesion,
                AdhesionAnterior: e.oldData.NumeroAdhesion,
                IdExternoNuevo: this.IdExternoAfiliado,
                IdExternoAnterior: e.oldData.IdExternoAfiliado,
                Observacion: e.newData.Observacion,
            }).then(()=>{
                this.dataGrid.cancelEditData()//solo para cerrar :)
            })
        },
       
    },
    mounted() {
        this.Cargar()
    },
    watch: {
        'IdCliente'() {
            this.Cargar()
        },
    },
    computed: {
        dataGrid() {
            return this.$refs["GridPolizasCliente"].instance
        },
    },

}
</script>

<style>

</style>
