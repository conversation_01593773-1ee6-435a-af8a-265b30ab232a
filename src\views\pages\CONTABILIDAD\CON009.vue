<template>
    <div>
        <vx-card title="Facturación de Manejo de Cuenta Médico Pronto Pago Posterior al Acreditamiento">
            <vs-row class="w-full mb-4" vs-justify="center">
                <ConfiguracionCaja
                    @CargarCaja="CargarCaja"
                    :FacturaCambiaria=1
                    TipoDocumento = "F"
                    TipoCajaSeleccionada = "TipoManejoCuenta"
                    CajaSeleccionada = "ManejoCuenta"
                    AgrupacionCaja="CajaManejoCuenta"
                    class="w-full">
                </ConfiguracionCaja>
            </vs-row>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="1">
                    <label class="typo__label">Fecha Factura:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                    <vs-input type="date" v-model="FechaFactura"></vs-input>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="1">
                    <label class="typo__label">No Envío:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                    <vs-input v-model="Envio" @keyup.enter="BusquedaEnvio()" @keydown.tab="BusquedaEnvio()"></vs-input>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center" v-if="Envio">
                <vs-col vs-type="flex" vs-align="center" vs-w="2" vs-justify="center">
                    <vs-button color="primary" icon-pack="feather" icon="icon-printer" @click="GeneraProntoPago()">Generar</vs-button>
                </vs-col>
            </vs-row>
        </vx-card>
    </div>
</template>
<script>
    import ConfiguracionCaja from '/src/components/sermesa/modules/caja/ConfiguracionCaja.vue'
    
    export default {
        components: {
            ConfiguracionCaja
        },
        data() {
            return {
                FechaFactura: '',
                Envio: '',
                configuracion:{
                    popUpConfiguracion: false,

                    tiposCajas: [],
                    tipoCajaSeleccionada: null,
                    indexTipoCajaSeleccionada: null,

                    cajaSeleccionada: null,
                    cajasFiltradasPorTipo: [],
                    codigosPermisosCajas: []                        
                },
                caja: {
                    serieFactura:[],
                    serieFacturaFiltrada:[],
                                        
                    tiposFacturacion: [],
                    tipoFacturacionSeleccionada:{}
                }
            }
        },
        methods:{
            BusquedaEnvio(){
                this.axios.post('/app/v1_contabilidad_hospital/BusquedaEnvio', {
                    NoEnvio: this.Envio
                })
            },
            GeneraProntoPago(){
                if(this.configuracion.cajaSeleccionada == null){
                    this.$vs.notify({
                        time: 4000,
                        title: 'Contabilidad',
                        color: 'danger',
                        text: 'Debe de seleccionar una serie de factura...',
                        position: 'top-center'
                    })

                }else{
                    this.axios.post('/app/v1_contabilidad_hospital/BusquedaProntoPago', {
                        NoEnvio: this.Envio
                    })
                    .then(resp => {
                        if(resp.data.codigo == 0){
                            this.$vs.dialog({
                                type: 'confirm',
                                color: 'primary',
                                title: 'Confirmacion',
                                text: resp.data.resultado == 1 ? `Será ${resp.data.resultado} factura.` : `Serán ${resp.data.resultado} facturas.`,
                                acceptText: 'Aceptar',
                                cancelText: 'Cancelar',
                                accept: () => {
                                    this.axios.post('/app/v1_contabilidad_hospital/GrabarProntoPago', {
                                        NoEnvio: this.Envio,
                                        FechaPlanilla: this.FechaFactura,
                                        SerieFactura: this.configuracion.cajaSeleccionada.SerieFac,
                                        FechaFactura: this.FechaFactura,
                                        CajaFactura: this.configuracion.cajaSeleccionada.CajaLocal
                                    })
                                    .then(resp => {
                                        if(resp.data.codigo == 0){
                                            let resultado = resp.data.resultado.split(',')
                                            for(let NumFactura  of resultado){
                                                this.axios.post('/app/v1_FacturaElectronica/GeneraFel', {
                                                    serieFactura: this.configuracion.cajaSeleccionada.SerieFac,
                                                    numeroFactura: NumFactura
                                                })
                                                .then(resp => {
                                                    if(resp.data.codigo == 0){
                                                        this.GenerarReporteFactura(this.configuracion.cajaSeleccionada.SerieFac, NumFactura)
                                                    }
                                                })
                                            }
                                            this.Envio = ''
                                        }
                                    })
                                }
                            })
                        }
                    })
                }
            },
            GenerarReporteFactura(SerieFactura, Factura){
                this.$reporte_modal({
                    Nombre: "Impresion FEL",
                    Opciones: {
                        SerieFactura: SerieFactura,
                        NumFactura: Factura
                    },
                    Formato: "PDF",
                    Imprimir: true,
                    Descargar: true,
                    NombreArchivoDescargar: SerieFactura + '-' + Factura
                })

            },
            CargarCaja(cajaSeleccionada){                
                this.configuracion.cajaSeleccionada   = cajaSeleccionada.Cajero
                this.configuracion.activarFacturacion = cajaSeleccionada.Factura
            }
        },
        mounted(){
            this.FechaFactura = new Date().toISOString().slice(0, 10);
        }
    }
</script>
<style scoped>
    .panel-cajero-elegir{
        background-color: rgba(var(--vs-danger), 1) !important; 
    }
    .panel-cajero {
        display:inline-block;
        border-radius: 25px;
        background-color: rgba(var(--vs-primary), 1);
        color: white;
        padding: 5px 65px 5px 10px;
        position: relative;
        width: auto;
        /* top: 12px; */
        overflow: hidden;
        /* margin-right: -14px; */
        font-size: 13px;
        /* right: 50px; */
        /* border-radius: 30px 0 0 0; */
        /* box-shadow: inset 0 2px 0px 1px rgba(0, 0, 0, 0.2); */
        cursor: pointer;
    }
</style>