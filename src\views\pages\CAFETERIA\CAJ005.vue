<template>
    <div>
        <vx-card :title="`Categorias - ${sesion.sesion_sucursal_nombre}`">
            <div v-if="permisos.crear">
                <vs-button @click="VentanaCategorias" color="success" icon-pack="fas" icon="fa-plus-circle">Nuevo
                    Categoria</vs-button>
            </div>
            <vs-table2 max-items="10" tooltip search pagination :data="Categorias" v-if="permisos.visualizar">
                <template slot="thead">
                    <th order="Codigo">Código</th>
                    <th order="Nombre">Nombre</th>
                    <th order="Descuento">Descuento</th>
                    <th order="Factura">Factura</th>
                    <th order="TipoVenta">Tipo Venta</th>
                    <th order="FacturaIGSS">Factura IGSS</th>
                    <th order="Facturaxdia">Factura por día</th>
                    <th order="DescuentoBI">Descuento BI</th>
                    <th order="TiempoPromesa">Tiempo Promesa</th>
                    <th></th>
                </template>
                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">
                        <vs-td2>
                            {{ tr.Codigo }}
                        </vs-td2>
                        <vs-td2 width='20%'>
                            {{ tr.Nombre }}
                        </vs-td2>
                        <vs-td2>
                            {{ tr.Descuento }}
                        </vs-td2>
                        <vs-td2>
                            {{ tr.Factura }}
                        </vs-td2>
                        <vs-td2>
                            {{ tr.TipoVenta }}
                        </vs-td2>
                        <vs-td2>
                            {{ tr.FacturaIGSS }}
                        </vs-td2>
                        <vs-td2>
                            {{ tr.Facturaxdia }}
                        </vs-td2>
                        <vs-td2>
                            {{ tr.DescuentoBI }}
                        </vs-td2>
                        <vs-td2>
                            {{ tr.TiempoPromesa }}
                        </vs-td2>
                        <vs-td2 width="50px">
                            <vs-button title="Editar" color="primary" size="small" icon-pack="fas" icon="fa-edit"
                                class="mr-1" style="display:inline-block" @click="Actualizar(tr)"></vs-button>
                        </vs-td2>
                    </tr>
                </template>
            </vs-table2>
        </vx-card>
        <vs-popup :title="tituloVentana" :active.sync="abrirVentana">
            <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                <form method="post" @submit.prevent="handleSubmit(Guardar().Nuevo(invalid))">
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/2  p-1">
                            <ValidationProvider name="Código" rules="alpha_num|required|max:2" v-slot="{ errors }"
                                class="required">
                                <label style="font-size:12px">Código</label>
                                <vs-input type="text" class="w-full" v-model="info.categoria.Codigo"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    required :disabled="esEditar" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2  p-1">
                            <ValidationProvider name="Nombre" rules="alpha_spaces|required|max:40" v-slot="{ errors }"
                                class="required">
                                <label style="font-size:12px">Nombre</label>
                                <vs-input type="text" class="w-full" v-model="info.categoria.Nombre"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    required />
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <vs-checkbox v-model="info.categoria.Descuento">Descuento</vs-checkbox>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <vs-checkbox v-model="info.categoria.Factura">Factura</vs-checkbox>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <vs-checkbox v-model="info.categoria.FacturaIGSS">Factura IGSS</vs-checkbox>
                        </div>
                    </div>
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <vs-checkbox v-model="info.categoria.Facturaxdia">Factura por día</vs-checkbox>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <vs-checkbox v-model="info.categoria.DescuentoBI">Descuento ClubBI</vs-checkbox>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <vs-checkbox v-model="info.categoria.TiempoPromesa">Tiempo Promesa</vs-checkbox>
                        </div>
                    </div>
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full h-full md:w-1/4 lg:w-1/4 xl:w-1/4 p-1">
                            <ul class="centerx">
                                <li>
                                    <vs-radio v-model="info.categoria.TipoVenta" vs-name="TipoVenta"
                                        vs-value="V">Venta</vs-radio>
                                </li>
                                <li>
                                    <vs-radio v-model="info.categoria.TipoVenta" vs-name="TipoVenta"
                                        vs-value="S">Servicio</vs-radio>
                                </li>
                            </ul>
                        </div>
                        <div class="w-full h-full md:w-1/4 lg:w-1/4 xl:w-1/4 p-1">
                            <vs-button height="100px" class="w-full mr-1 mt-5" icon-pack="fas" icon="fa-plus"
                                color="success" @click="Guardar().Nuevo(invalid)">
                                Guardar
                            </vs-button>
                        </div>
                    </div>
                </form>
            </ValidationObserver>
        </vs-popup>
    </div>
</template>

<script>
export default {
    data() {
        return {
            /**
             * Documentación del módulo
             */
            doc_: [
                'Pantalla de administración de cirugías',
            ],
            info: {
                categoria: {
                    Codigo: '',
                    Nombre: "",
                    Descuento: false,
                    Factura: false,
                    TipoVenta: 'V',
                    FacturaIGSS: false,
                    Facturaxdia: false,
                    DescuentoBI: false,
                    TiempoPromesa: false
                }
            },
            permisos: {
                visualizar: false,
                crear: false,
                editar: false
            },
            Categorias: [],
            texto: 'Agregar',
            abrirVentana: false,
            tituloVentana: '',
            esEditar: false
        }
    },
    computed: {
        sesion() {
            return this.$store.state.sesion
        }
    },
    methods: {
        Consulta() {
            return {
                init: () => {
                    this.axios.post('/app/v1_JefeCaja/ListarCategorias', {})
                        .then(resp => {
                            this.Categorias = resp.data.json;
                        })
                }
            }
        },
        Guardar() {
            return {
                Nuevo: (formaIsInvalid) => {
                    if (!formaIsInvalid) {
                        this.esEditar ? this.info.categoria.Opcion = 'A' : this.info.categoria.Opcion = 'I'

                        this.axios.post('/app/v1_JefeCaja/GuardarCategorias', this.info.categoria)
                            .then(resp => {
                                if (resp.data.tipo_error == 0) {
                                    this.Consulta().init();
                                    this.abrirVentana = false
                                    this.Limpiar()
                                }
                            })
                    } else {
                        this.$vs.notify({
                            title: this.texto,
                            text: 'Formulario contiene errores',
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                    }
                }
            }
        },
        Actualizar(categoriaEditar) {
            this.tituloVentana = 'Editar categoria'
            this.abrirVentana = true
            this.esEditar = true
            this.texto = 'Editar'
            this.info.categoria = {
                Codigo: categoriaEditar.Codigo,
                Nombre: categoriaEditar.Nombre,
                Descuento: categoriaEditar.Descuento == 'Si' ? true : false,
                Factura: categoriaEditar.Factura == 'Si' ? true : false,
                TipoVenta: categoriaEditar.TipoVenta == 'Venta' ? 'V' : 'S',
                FacturaIGSS: categoriaEditar.FacturaIGSS == 'Si' ? true : false,
                Facturaxdia: categoriaEditar.Facturaxdia == 'Si' ? true : false,
                DescuentoBI: categoriaEditar.DescuentoBI == 'Si' ? true : false,
                TiempoPromesa: categoriaEditar.TiempoPromesa == 'Si' ? true : false
            }
        },
        VentanaCategorias() {
            this.abrirVentana = !this.abrirVentana
            this.tituloVentana = 'Nueva categoria'
            this.Limpiar()
        },
        Limpiar() {
            this.info.categoria = {
                Codigo: '',
                Nombre: "",
                Descuento: false,
                Factura: false,
                TipoVenta: 'V',
                FacturaIGSS: false,
                Facturaxdia: false,
                DescuentoBI: false,
                TiempoPromesa: false
            }
            this.esEditar = false
        }
    },
    mounted() {
        this.permisos.visualizar = this.$validar_privilegio('VISUALIZAR').status
        this.permisos.crear = this.$validar_privilegio('CREAR').status
        this.Consulta().init()
    }
}
</script>