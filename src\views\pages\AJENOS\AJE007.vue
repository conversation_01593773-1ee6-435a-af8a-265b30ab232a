<template>
<div>
    <!-- reportes -->
    <vs-popup id="contentreport" classContent="popup-generar" title="Informe" :active.sync="info.reporte" fullscreen style="z-index:99998;height:100%">
        <embed v-if="info.reporte_src!=''" type="application/pdf" :src="info.reporte_src" ref="pdfDocument" width="100%" height="98%" />
    </vs-popup>

    <vs-popup title="Asignación de Código de Autorización" :active.sync="info.asignar.mostrar" style="z-index:52000" id="div-with-loading" class="sm">
        <ValidationObserver ref="formValidate" v-slot="{ invalid,handleSubmit }" mode="lazy">
            <div class="flex ">
                <div class="w-1/3 p-1">
                    <vs-input class="w-full" :value="info.asignar.lote" label="Lote" disabled></vs-input>
                </div>
                <div class="w-1/3 p-1">
                    <ValidationProvider name="Descripción" rules="required|numero_entero" v-slot="{ errors }">
                        <vs-input class="w-full" type="number" v-model.trim="info.asignar.autorizacion" label="Cód. de Autorización" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"></vs-input>
                    </ValidationProvider>
                </div>
            </div>
            <vs-divider></vs-divider>
            <div class="flex">
                <vs-spacer></vs-spacer>
                <vs-button  @click.native="handleSubmit(Guardar().asignar(info.asignar))" :disabled="invalid">Asignar</vs-button>
            </div>
        </ValidationObserver>
    </vs-popup>

    <vs-popup title="Asignación de Cheques" :active.sync="info.cheques.mostrar" style="z-index:52000" id="div-with-loading" class="sm">
        <b>Lote: {{info.cheques.lote}}</b>
        <vs-divider></vs-divider>
        <div v-for="(item,key) in info.cheques.facturas" :key="key">
            <div class="facturas-titulo">
                ({{item.CodigoAjeno}}) {{item.Medico}}
            </div>
            <div class="facturas-detalle">
                <!-- {{item}} -->
                <table width="100%">
                    <thead>
                        <tr>
                            <th>Factura</th>
                            <th width="200px"> Cheque</th>
                            <th width="50px">Acción</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(item,key2) in item.Facturas" :key="key2">
                            <td style="padding-left:15px">
                                {{item.Factura}}
                            </td>
                            <td>
                                <vs-input class="w-full" type="number" v-model.trim="item.Cheque"></vs-input>
                            </td>
                            <td style="text-align:center">
                                <vs-button :disabled="item.Cheque == null || item.Cheque == ''" color="success" size="small" icon-pack="far" icon="fa-save" style="margin:auto" v-on:click="Guardar().cheque(item)"></vs-button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </vs-popup>

    <vx-card title="Control de Lote">
        <vs-divider></vs-divider>
        <SM-Tabs height="auto">
            <div class="tab p-4" label="Lotes Pendientes">
                <div class="flex">
                    <vs-spacer></vs-spacer>
                    <vs-button icon-pack="feather" icon="icon-refresh-cw" @click.native="Consulta().actualizar()">Actualizar</vs-button>
                </div>
                <!-- <vs-alert color="danger" v-if="!permisos.editar"><i class="fas fa-lock"></i> No cuenta con los permisos para editar la tabla de pagos</vs-alert> -->
                <h5>Lotes Validados Depósitos</h5>
                <vs-table2 max-items="5" search tooltip pagination :data="lotesDepositos">
                    <template slot="thead">
                        <th order="Lote">Número de Lote</th>
                        <th order="Hospital" width="150px">Hospital</th>
                        <th order="FechaValidacion" width="170px">Fecha de Validación</th>
                        <th order="Estado" width="150px">Estado</th>
                        <th width="180px">Acción</th>
                    </template>

                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2>
                                {{ tr.Lote }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.Hospital }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.FechaValidacion }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.Estado }}

                            </vs-td2>

                            <vs-td2 noTooltip>
                                <vs-button :disabled="!permisos.memo" color="primary" style="padding:7px" type="filled" @click.native="permisos.memo?Otros().reporte(tr.Lote):null">Memo</vs-button>
                                <!-- <vs-button :disabled="!permisos.asignar" color="success" style="padding:7px" type="filled" class="ml-1" @click.native="permisos.asignar?Otros().asignar(tr.Lote):null">Asignar</vs-button> -->
                                <vs-button :disabled="!permisos.traslado || tr.TrasladoCorporativo !=''" color="warning" style="padding:7px" type="filled" class="ml-1" @click.native="(permisos.traslado && tr.TrasladoCorporativo =='')?Otros().traslado(tr.Lote):null">
                                    <span v-if="tr.TrasladoCorporativo==''">Trasladar</span>
                                    <i class="far fa-check-circle" v-else> Trasladado</i>
                                </vs-button>
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>
                <vs-divider></vs-divider>
                <h5>Lotes Validados Cheques</h5>
                <vs-table2 max-items="5" search tooltip pagination :data="lotesCheques">
                    <template slot="thead">
                        <th order="Lote">Número de Lote</th>
                        <th order="Hospital" width="150px">Hospital</th>
                        <th order="FechaValidacion" width="170px">Fecha de Validación</th>
                        <th order="Estado" width="150px">Estado</th>
                        <th width="180px">Acción</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2>
                                {{ tr.Lote }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.Hospital }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.FechaValidacion }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.Estado }}
                            </vs-td2>

                            <vs-td2 noTooltip>
                                <vs-button :disabled="!permisos.memo" color="primary" style="padding:7px" type="filled" @click.native="permisos.memo?Otros().reporte(tr.Lote):null">Memo</vs-button>
                                <!-- <vs-button :disabled="!permisos.asignar" color="success" style="padding:7px" type="filled" class="ml-1" @click.native="permisos.asignar?Otros().asignar(tr.Lote):null">Asignar</vs-button> -->
                                <vs-button :disabled="!permisos.traslado || tr.TrasladoCorporativo !=''" color="warning" style="padding:7px" type="filled" class="ml-1" @click.native="(permisos.traslado && tr.TrasladoCorporativo =='')?Otros().traslado(tr.Lote):null">
                                    <span v-if="tr.TrasladoCorporativo==''">Trasladar</span>
                                    <!-- <i class="far fa-check-circle" v-else> Trasladado</i> -->
                                </vs-button>
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
            <div class="tab p-4" label="Lotes Trasladados">
                <vs-table2 max-items="10" search tooltip pagination :data="lotesTrasladados">
                    <template slot="thead">
                        <th order="Lote">Número de Lote</th>
                        <th order="Hospital" width="150px">Hospital</th>
                        <th order="FechaValidacion" width="170px">Fecha de Validación</th>
                        <th order="Estado" width="150px">Estado</th>
                    </template>

                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2>
                                {{ tr.Lote }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.Hospital }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.FechaValidacion }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.Estado }}
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
            <div class="tab p-4" label="Listado de Lotes Pagados">
                <vs-table2 max-items="10" search tooltip pagination :data="lotesPagadosDeposito">
                    <template slot="thead">
                        <th order="Lote">Número de Lote</th>
                        <th order="Hospital" width="150px">Hospital</th>
                        <th order="FechaValidacion" width="170px">Fecha de Validación</th>
                        <th order="Estado" width="150px">Estado</th>
                    </template>

                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2>
                                {{ tr.Lote }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.Hospital }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.FechaValidacion }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.Estado }}
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>
                <vs-table2 max-items="10" search tooltip pagination :data="lotesPagadosCheques">
                    <template slot="thead">
                        <th order="Lote">Número de Lote</th>
                        <th order="Hospital" width="150px">Hospital</th>
                        <th order="FechaValidacion" width="170px">Fecha de Validación</th>
                        <th order="Estado" width="150px">Estado</th>
                    </template>

                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2>
                                {{ tr.Lote }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.Hospital }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.FechaValidacion }}
                            </vs-td2>

                            <vs-td2>
                                {{ tr.Estado }}
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
        </SM-Tabs>
    </vx-card>
</div>
</template>

<script>
// import DateRangePicker from 'vue2-daterange-picker'
// import 'vue2-daterange-picker/dist/vue2-daterange-picker.css'
export default {
    data() {
        return {
            /**
             * Documentación del módulo
             */
            doc_: [
                'Visualiza todos los lotes validados en los módulos <b>Validación de Lotes Cheques</b> y <b>Validación de Lotes Depositos</b>',
                'Al asignar un no. de autorización al lote se cambia el estado a <b>Pagado</b>',
                'Los lotes con iniciales CA pertenecen a los generados por la planilla de cuenta ajena.',
                '* Memo extrae la información del sp <i>sp_Ajenos_Honorarios_Memo</i>',
                '* Asignar envia la información al sp <i>sp_Ajenos_Honorarios_HabilitarPago</i>',
            ],
            /**
             * Valores generales
             */
            opciones: [{
                Tipo: 'D',
                Nombre: 'Lotes de Depósitos'
            }, {
                Tipo: 'C',
                Nombre: 'Lotes de Cheques'
            }],

            info: {
                reporte: false,
                reporte_src: null,
                ultimaFecha: null,
                asignar: {
                    mostrar: false,
                    lote: null,
                    autorizacion: null
                },
                cheques: {
                    mostrar: false,
                    lote: null,
                    facturas: []
                }
            },
            /**
             * Listado de permisos
             */
            permisos: {
                memo: false,
                traslado: false,
                asignar: false
            },

            /**
             * Información de los pagos
             */
            lotesDepositos: [],
            lotesTrasladados: [],
            lotesCheques: [],
            lotesPagadosDeposito: [],
            lotesPagadosCheques: []
        }
    },
    components: {
        // DateRangePicker
    },
    methods: {
        //=======================================================================================================
        // CONSULTA
        //=======================================================================================================
        Consulta: function() {
            return {
                actualizar: () => {
                    this.Consulta().lotes('D', 'A').then(resp => this.lotesDepositos = resp)
                    this.Consulta().lotes('C', 'A').then(resp => this.lotesCheques = resp)
                    this.Consulta().lotes('A', 'L').then(resp => this.lotesTrasladados = resp)
                    this.Consulta().lotes('A', 'P').then(resp => this.lotesPagadosDeposito = resp)
                    this.Consulta().lotes('A', 'T').then(resp => this.lotesPagadosCheques = resp)
                },
                lotes: (Tipo, Estado) => {
                    return this.axios.post('/app/ajenos/Honorarios_Lotes_Consulta', {
                            Tipo,
                            Estado
                        })
                        .then(resp => {

                            if (resp.data.codigo == 0) {
                                return resp.data.json
                            }
                        })
                        .catch(() => {

                        })
                },

                lotes_detalle: (lote) => {

                    return this.axios.post('/app/ajenos/Honorarios_Lotes_Detalle', {
                            lote
                        })
                        .then(resp => {

                            if (resp.data.codigo == 0) {
                                return resp.data.json
                            }
                        })
                        .catch(() => {

                        })
                }
            }
        },

        //=======================================================================================================
        // NUEVO
        //=======================================================================================================
        Nuevo: function() {
            return {}
        },

        //=======================================================================================================
        // GUARDAR
        //=======================================================================================================
        Guardar: function() {
            return {
                asignar: (item) => {

                    this.axios.post('/app/ajenos/Honorarios_Lotes_Asignar', item)
                        .then(() => {

                            this.info.asignar.mostrar = false
                            this.info.asignar.lote = null
                            this.info.asignar.autorizacion = null
                            this.$refs.formValidate.reset()
                            this.Consulta().actualizar()

                        })
                        .catch(() => {

                        })
                },

                cheque: (item) => {

                    this.axios.post('/app/ajenos/Honorarios_Lotes_Asignar_Cheques', {
                            Lote: item.Lote,
                            Ajeno: item.CodigoAjeno,
                            FacturaSerie: item.FacturaSerie,
                            FacturaNumero: item.FacturaNumero,
                            Cheque: item.Cheque
                        })
                        .then(() => {

                            // this.Consulta().lotes('C').then(resp => this.lotesCheques = resp)
                            this.Otros().cheques(item.Lote)
                        })
                        .catch(() => {

                        })
                }
            }
        },

        //=======================================================================================================
        // EDITAR
        //=======================================================================================================
        Editar: function() {
            return {}
        },

        //=======================================================================================================
        // ELIMINAR
        //=======================================================================================================
        Eliminar: function() {
            return {}
        },

        //=======================================================================================================
        // OTROS
        //=======================================================================================================
        Otros: function() {
            return {
                reporte: (Lote) => {
                    this.$reporte_modal({
                            Nombre: "Memo",
                            Opciones: {
                                Lote
                            }
                        })
                },
                asignar: (lote) => {
                    this.info.asignar.mostrar = true
                    this.info.asignar.lote = lote
                    this.info.asignar.autorizacion = null
                    this.$refs.formValidate.reset()
                },
                traslado: (lote) => {
                    this.$vs.dialog({
                        type: 'confirm',
                        color: 'warning',
                        title: 'Traslado',
                        acceptText: 'Trasladar',
                        cancelText: 'Cancelar',
                        text: `¿Desea indicar que se ha trasladado el lote ${lote}?`,
                        accept: () => {
                            this.axios.post('/app/ajenos/Honorarios_Lotes_Trasladar', {
                                    lote
                                })
                                .then(() => {
                                    this.Consulta().actualizar()
                                })
                        }
                    })
                },
                cheques: (lote) => {
                    this.info.cheques.mostrar = true
                    this.info.cheques.lote = lote
                    this.info.cheques.facturas = []
                    this.temp_medico = null
                    this.Consulta().lotes_detalle(lote).then(resp => {
                        let arr = []
                        resp.sort((a, b) => (a.Medico > b.Medico) ? 1 : ((b.Medico > a.Medico) ? -1 : 0)).map(m => {

                            if (m.Medico != this.temp_medico) {
                                arr.push({
                                    CodigoAjeno: m.CodigoAjeno,
                                    Medico: m.Medico,
                                    Facturas: []
                                })
                            }
                            this.temp_medico = m.Medico
                            if (arr[arr.length - 1].Facturas.filter(f => f.CodigoAjeno == m.CodigoAjeno && f.Factura == m.Factura).length == 0) arr[arr.length - 1].Facturas.push({
                                ...m,
                                Cheque: null
                            })
                        })

                        // Ordenando por medico

                        this.info.cheques.facturas = arr
                    })

                }
            }
        }
    },
    mounted() {
        this.Consulta().actualizar()

        this.permisos.memo = this.$validar_privilegio('MEMO').status
        this.permisos.traslado = this.$validar_privilegio('TRASLADO').status
        this.permisos.asignar = this.$validar_privilegio('ASIGNAR').status
    }
}
</script>

<style scoped>
.facturas-titulo {
    font-weight: bold;
}

.facturas-detalle {
    padding-left: 10px;
}

tbody tr:nth-child(odd) {
    background-color: #ccc;
}
</style>
