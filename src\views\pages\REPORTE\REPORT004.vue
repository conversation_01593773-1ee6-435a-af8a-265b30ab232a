<template>
<vx-card title="Formularios">
    <vs-popup classContent="popup-example" :title="reporte.titulo" :active.sync="reporte.popup">
        <!-- {{reporte.opciones}} -->
        <div v-for="(opcion,index) in reporte.opciones" :key="index">
            <div v-if="opcion.Tipo!='Oculto'">
                {{opcion.Etiqueta}}
                <flat-pickr :config="configdateDatePicker" v-if="opcion.Tipo=='Fecha'" style="width:100%" :name="'Campo_'+index" v-validate="'required'" placeholder="Date Time" v-model="opcion.value" :required="opcion.Obligatorio==1" />
                <flat-pickr :config="configdateTimePicker" v-if="opcion.Tipo=='Fecha/Hora'" style="width:100%" :name="'Campo_'+index" v-validate="'required'" placeholder="Date Time" v-model="opcion.value" :required="opcion.Obligatorio==1" />
                <vs-input v-if="opcion.Tipo=='Texto'" :name="'Campo_'+index" v-validate="'required'" class="w-full" v-model="opcion.value" :required="opcion.Obligatorio==1" />
                <vs-input v-if="opcion.Tipo=='Numero'" :name="'Campo_'+index" v-validate="'required'" class="w-full" type="number" v-model="opcion.value" :required="opcion.Obligatorio==1" />
                <vs-checkbox v-if="opcion.Tipo=='Checkbox'" v-model="opcion.value" :required="opcion.Obligatorio==1"></vs-checkbox>
                <span class="text-danger text-sm" v-show="opcion.Obligatorio==1">Campo obligatorio</span>

            </div>
        </div>
        <vs-divider />
        <vs-button @click="submitForm(reporte.url,reporte.opciones,true)" color="danger" type="border"><i class="fas fa-file-pdf"></i> Generar</vs-button>
        <vs-button @click="submitForm(reporte.url,reporte.opciones,false)" color="success" type="border"><i class="far fa-file-excel"></i> Generar</vs-button>

        <vs-divider />
    </vs-popup>

    <vs-popup id="contentreport" classContent="popup-generar" :title="reporte.titulo" :active.sync="reporte.generar" fullscreen style="z-index:99999">
        <embed v-if="reporte.pdf!=''" type="application/pdf" :src="reporte.pdf" ref="pdfDocument" width="100%" height="98%" />
    </vs-popup>

    <!-- <form> -->
    <div class="content content-pagex">

        <vs-input label="Buscar" v-model="reporte.buscar" />
        <br>

    </div>
    <!-- </form> -->
    
    <div v-for="(rep, index) in listadoFiltro" :key="index">
        <div>
            <h3>{{rep.Aplicativo}}</h3>
        </div>

        <div class="vx-row" v-for="(rep, index) in rep._level" :key="index">
            <div class="vx-col w-full">
                <h3>{{rep.Funcionalidad}}</h3>
            </div>
            <vs-divider />
            <div class="vx-col w-full sm:w-1/2 lg:w-1/3 mb-base" v-for="(rep, index) in rep._level" :key="index">
                <div class="vx-card">
                    <div class="vx-card__collapsible-content vs-con-loading__container">

                        <div class="vx-card__body">
                            <h5 class="mb-2">{{rep.Nombre}}</h5>
                            <p class="text-grey">{{rep.descripcion}}</p>
                            <br>
                            <div class="flex flex-wrap">
                                <vs-button color="primary" type="filled" @click="generar(rep.Nombre,rep.UrlApi,rep._level)">Generar</vs-button>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</vx-card>
</template>

<script>
import flatPickr from 'vue-flatpickr-component';
import 'flatpickr/dist/flatpickr.css';

export default {
    data() {
        return {
            configdateDatePicker: {
                enableTime: false,
                dateFormat: 'd/m/Y' //'d-m-Y H:i'
            },
            configdateTimePicker: {
                enableTime: true,
                dateFormat: 'd/m/Y H:i:S' //'d-m-Y H:i'
            },
            listado_reportes: [],
            reportes: [],

            reporte: {
                generar: false,
                popup: false,
                buscar: '',
                titulo: '',
                url: '',
                url_pdf: '',
                opciones: [],
                pdf: ''
            },
        }
    },
    components: {
        flatPickr
    },
    computed: {
        listadoFiltro() {
            if (this.reporte.buscar != '') {
                let filtro = this.reporte.buscar.toLowerCase();

                return this.listado_reportes.map(data => {
                    let arr = []
                    data._level.map(data => {
                        let ar = data._level.filter(data => data.Nombre.toLowerCase().indexOf(filtro) >= 0)
                        arr.push({
                            "IdFuncionalidad": data.IdFuncionalidad,
                            "Funcionalidad": data.Funcionalidad,
                            "_level": ar
                        })
                    })
                    return {
                        "IdAplicativo": data.IdAplicativo,
                        "Aplicativo": data.Aplicativo,
                        "_level": arr
                    }
                })

            } else {
                return this.listado_reportes
            }
        },
        errorValid() {
            return this.errors.items.map(data => {
                return data.field
            })
        }
    },
    methods: {
        listado_reporte() {
            this.axios.post("/app/reporte/ReporteListadoPrivilegio", {}).then(resp => {
                this.listado_reportes = resp.data.json;
                
            });
        },
        generar(titulo, url, opciones) {
            this.reporte.generar = false
            this.reporte.titulo = titulo
            this.reporte.url = url
            this.reporte.opciones = opciones.map(data => {
                return {
                    Etiqueta: data.Etiqueta,
                    Eval: data.Eval,
                    Obligatorio: data.Obligatorio,
                    MensajeError: data.MensajeError,
                    Parametro: data.Parametro,
                    ValorDefecto: data.ValorDefecto,
                    Tipo: data.Tipo,
                    linea: data.linea,
                    value: null,
                }
            })
            this.reporte.popup = true
        },

        submitForm(url, opciones, is_pdf) {
            this.reporte.generar = false
            this.reporte.pdf = ''

            this.$vs.loading({scale: 0.6})
            // console.log(url)
            //?origen=1&id=TOKENRPSERMESA&opcion=RAD&archivo=true&empresa=MED&tipo={{Tipo}}&orden={{Orden}}

            let _opciones = {}
            opciones.map(data => {
                _opciones[data.Parametro] = ((data.Parametro.indexOf("=") < 0) ? ((data.Tipo == 'Checkbox') ? (data.value == true ? 1 : 0) : (data.value != null && data.value != "") ? data.value : data.ValorDefecto) : data.Parametro.split('=')[1])
            })
            _opciones.tiporeporte = (is_pdf) ? "application/pdf" : "text/csv"

            this.axios.post('/app/usuario/obtener_sesion', {})
                .then(resp => {
                    return this.axios.post(url + '/Reporte', {
                        opciones: _opciones,
                        ...resp.data
                    }, {
                        responseType: 'arraybuffer'
                    })
                }).then(resp => {
                    // console.log(resp)
                    if (is_pdf) this.reporte.pdf = 'data:application/pdf;base64,' + Buffer.from(resp.data, 'binary').toString('base64')
                    

                    if (!is_pdf) {
                        const url = window.URL.createObjectURL(new Blob([resp.data]))
                        const link = document.createElement('a')
                        link.href = url
                        link.setAttribute('download', this.reporte.titulo+'.csv') //or any other extension
                        document.body.appendChild(link)
                        link.click()
                    }

                })
                .catch(err => {
                    console.log(err)
                    
                })

            if (is_pdf)
                setTimeout(() => {
                    opciones.map(data => {
                        url = url.replace('{{' + data.nombre + '}}', data.value)
                    })
                    this.reporte.url_pdf = url
                    this.reporte.generar = true
                }, 100);
        },

    },
    created() {
        this.listado_reporte()

    }
}
</script>

<style>
.button {
    margin: 0px !important;
}

.btnx {
    margin-left: 10px !important;
    background: red;
    border-radius: 5px 0px 0px 5px;
}

.btn-drop {
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: red;

}

.popup-generar {
    height: 100%
}

i {
    font-size: 18px;
}
</style>
