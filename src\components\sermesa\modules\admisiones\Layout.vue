<template>
    <div>
        <div v-for="(container, index) in layout" :key="'container-' + index" :class="container.class">
            <div v-for="(row, rowIndex) in container.rows" :key="'row-' + rowIndex" class="row">
                <div v-for="(column, colIndex) in row.columns" :key="'column-' + colIndex" :class="'col-' + column.size">
                    <component :is="column.component" :validations="column.validations" />
                </div>
            </div>
        </div>
    </div>
</template>
  
<script>

import SerieAdmision from "/src/components/sermesa/modules/admisiones/SerieCodigoAdmision.vue";
import InformacionPaciente from "/src/components/sermesa/modules/admisiones/TiposDescuento.vue";
import DiagnosticosMedicos from "/src/components/sermesa/modules/admisiones/DiagnosticoCovid.vue";


export default {

    name: "Layout",
    components: {
        SerieAdmision,
        InformacionPaciente,
        DiagnosticosMedicos,
    },
    props: {
        layout: {
            type: Array,
            default: () => [],
        },
    },
};
</script>
  
<style scoped>
.row {
    display: flex;
}

[class^="col-"] {
    padding: 15px;
    box-sizing: border-box;
}

.col-6 {
    flex: 0 0 50%;
    max-width: 50%;
}

.col-12 {
    flex: 0 0 100%;
    max-width: 100%;
}
</style>