<template>
<div class="main-historial">
    <div class="Encabezado">
        <Encabezado ref="EncabezadoHistorial" @onLoadedData="CargarCita" @limpiarVariables="LimpiarVariables" v-bind="infoCita" />
    </div>

    <div class="Contenido p-2">
        <vs-row>
            <vs-col class="buttons vx-card ml-2 mr-2 sticky">
                <div class="p-2 w-full sm:w-1/2 md:w-1/2 lg:w-1/4 xl:w-1/5 div-button" v-for="(item, index) in buttons" v-bind:key="index">
                    <vs-button class="button" :color="item.value === 5 ? 'success' :  item.value === 4 ? 'danger' : 'primary'" type="filled" @click="item.click" :disabled="!(Boolean(infoCita.IdCliente) && ((permisoEdicion && !infoCita.TipoBusqueda) || !(item.value == 5)))">
                        <font-awesome-icon :icon="['fas', item.icon]" class="pr-2" style="font-size: 16px" />
                        <span>{{item.name}}</span>
                    </vs-button>
                </div>
            </vs-col>
            <vs-col>
                <div class="p-2 pb-0">
                    <vx-card>
                        <DxForm :form-data.sync="formulario" labelMode="floating">
                            <DxItem item-type="group" :col-span="2" :col-count="2">
                                <DxItem data-field="MotivoConsulta" editor-type="dxTextArea" :editor-options="{ height: 100, disabled: infoCita.IdConsulta === undefined || infoCita.IdConsulta === null || infoCita.IdConsulta === '' || !permisoEdicion }">
                                    <DxLabel :text="'Motivo de la consulta'" />
                                </DxItem>
                                <DxItem data-field="HistoriaEnfermedad" editor-type="dxTextArea" :editor-options="{ height: 100, disabled: infoCita.IdConsulta === undefined || infoCita.IdConsulta === null || infoCita.IdConsulta === '' || !permisoEdicion }">
                                    <DxLabel :text="'Historia de la enfermedad'" />
                                </DxItem>
                            </DxItem>
                        </DxForm>
                    </vx-card>
                </div>
            </vs-col>
            <vs-col>
                <div class="p-2">
                    <vx-card>
                        <DxTabPanel id="tabPanel" ref="tabHistorial" :repaint-changes-only="true" :height="'100%'" :data-source="tabs" :selected-index="indiceTab" :showNavButtons="true" @selection-changed="onSelectionChangedTabs">
                            <template #title="{ data: tab }">
                                <span>
                                    <font-awesome-icon class="pr-1" :icon="['fas', tab.icon]" style="font-size: 16px" />
                                    {{ tab.name }}
                                </span>
                            </template>
                            <template #item="{ data: tab }">
                                <div>
                                    <div v-if="tab.value === 1" class="p-2">
                                        <Antecedentes ref="Antecedentes" v-bind="infoCita" :Alergias="alergias" @actualizarDatosGinecologicos="ActualizarDatosGinecologicos" />
                                    </div>
                                    <div v-if="tab.value === 2">
                                        <Examen ref="Examen" v-bind="infoCita" :Modulo="1" />
                                    </div>
                                    <div v-if="tab.value === 3">
                                        <Signos ref="Signos" v-bind="infoCita" :Modulo="1" />
                                    </div>
                                    <div v-if="tab.value === 4">
                                        <Diagnostico ref="Diagnostico" v-bind="infoCita" :Modulo="1" @actualizarDiagnostico="ActualizarDiagnostico" />
                                    </div>
                                    <div v-if="tab.value === 5">
                                        <Otros ref="Otros" v-bind="infoCita" :Modulo="1" />
                                    </div>
                                    <div v-if="tab.value === 6">
                                        <Receta ref="Receta" v-bind="infoCita" :Consulta="busquedaConsulta" />
                                    </div>
                                    <div v-if="tab.value === 7">
                                        <OrdenesEstudios ref="OrdenesEstudios" v-bind="infoCita" :Consulta="busquedaConsulta" />
                                    </div>
                                </div>
                            </template>
                        </DxTabPanel>
                    </vx-card>
                </div>
            </vs-col>
            <vs-col>
                <div class="p-2 fixed1" v-if="(botonGuardar !== null && botonGuardar.value !== 1 && botonGuardar.value !== 6 && botonGuardar.value !== 7) && infoCita.IdCita">
                    <vs-button class="button" color="success" type="filled" style="width: 300px; size: 20px" @click="AccionDeBoton">
                        <font-awesome-icon :icon="['fas', botonGuardar.icon]" class="pr-2" />
                        <span>Guardar {{ botonGuardar.button }}</span>
                    </vs-button>
                </div>
            </vs-col>
        </vs-row>

    </div>

    <!-- Popup de laboratorios -->
    <DxPopup :visible.sync="mostrarLaboratorio" :width="'95%'" :height="'95%'" :show-title="true" :full-screen="false" :hide-on-outside-click="false" title="Laboratorio" :showCloseButton="true">
        <DxScrollView :scroll-by-conent="true">
            <div>
                <Laboratorio ref="Laboratorio" v-bind="infoCita" />
            </div>
        </DxScrollView>
    </DxPopup>

    <!-- Popup de exámenes -->
    <DxPopup :visible.sync="mostrarExamenes" :width="'95%'" :height="'95%'" :show-title="true" :full-screen="false" :hide-on-outside-click="false" title="Exámenes" :showCloseButton="true">
        <DxScrollView :scroll-by-conent="true">
            <div>
                <Examenes ref="Examenes" v-bind="infoCita" />
            </div>
        </DxScrollView>
    </DxPopup>

    <!-- Popup de evolución -->
    <DxPopup :visible.sync="mostrarEvolucion" :width="'95%'" :height="'95%'" :show-title="true" :full-screen="false" :hide-on-outside-click="false" title="Evolución" :showCloseButton="true">
        <DxScrollView :scroll-by-conent="true">
            <div>
                <Evolucion ref="Evolucion" v-bind="infoCita" />
            </div>
        </DxScrollView>
    </DxPopup>
</div>
</template>

<script>
import 'devextreme-vue/lookup'
import 'devextreme-vue/text-area'
import DxTabPanel from 'devextreme-vue/tab-panel';
import esMessages from 'devextreme/localization/messages/es.json'
import {
    locale,
    loadMessages
} from 'devextreme/localization'
import DxPopup from 'devextreme-vue/popup'

import {
    DxScrollView
} from 'devextreme-vue/scroll-view';
// import {
//     DxDataGrid,
//     DxColumn,
//     DxSelection
// } from 'devextreme-vue/data-grid'
// import {
//     DxPopup as OrdenPopup
// } from 'devextreme-vue/popup'
import {
    DxForm,
    DxItem,
    DxLabel,
    // DxButtonItem
} from 'devextreme-vue/form'
import Encabezado from './Encabezado.vue'
import Antecedentes from './AntecedentesPersonales.vue'
import Examen from './ExamenFisico.vue'
import Signos from './Signos.vue'
import Diagnostico from './Diagnostico.vue'
import Otros from './Otros.vue'
import Laboratorio from './Laboratorio.vue'
import Examenes from './Examenes.vue'
import Evolucion from './Evolucion.vue'

import Receta from './Receta.vue'

import OrdenesEstudios from './OrdenesEstudios.vue';

const tabPanelHistorial = 'tabHistorial'

export default {
    name: 'HistorialClinico',
    components: {
        DxForm,
        DxItem,
        DxLabel,
        // DxButtonItem,
        // DxGroupItem,
        // DxButtonItem,
        DxTabPanel,
        // DxDataGrid,
        // DxColumn,
        // DxSelection,
        // OrdenPopup,
        // DxButton,
        DxScrollView,
        Encabezado,
        Antecedentes,
        Examen,
        Signos,
        Diagnostico,
        Otros,
        Laboratorio,
        Examenes,
        Evolucion,
        OrdenesEstudios,
        DxPopup,
        // LabPop,
        // ExaPop,
        // EvoPop,
        Receta
    },
    data() {
        return {
            indiceTab: 0,
            buttons: [{
                    name: 'Evoluciones',
                    icon: 'arrow-up-right-dots',
                    value: 1,
                    click: () => {
                        this.$refs.Evolucion.refresh()
                        this.$refs.Evolucion.BusquedaFechasCita()
                        this.mostrarEvolucion = true
                    }
                }, {
                    name: 'Laboratorios',
                    icon: 'vial-circle-check',
                    value: 2,
                    click: () => {
                        this.mostrarLaboratorio = true
                    }
                }, {
                    name: 'Exámenes',
                    icon: 'radiation',
                    value: 3,
                    click: () => {
                        this.mostrarExamenes = true
                    }
                },
                {
                    name: 'Generar PDF',
                    icon: 'file-pdf',
                    value: 4,
                    click: () => {
                        this.CargarPDF()
                    }
                },
                {
                    name: 'Finalizar cita',
                    icon: 'calendar-check',
                    value: 5,
                    click: () => {
                        this.FinalizarCita()
                    }
                },
            ],

            tabs: [{
                    name: 'Antecedentes personales',
                    button: 'Antecedentes',
                    value: 1,
                    disabled: true,
                    icon: "address-card",
                }, {
                    name: 'Exámen físico',
                    button: 'Exámen físico',
                    value: 2,
                    disabled: true,
                    icon: "person-falling-burst",
                }, {
                    name: 'Signos vitales',
                    button: 'Signos vitales',
                    value: 3,
                    disabled: true,
                    icon: "heart-pulse",
                }, {
                    name: 'Impresión clínica / Diagnóstico',
                    button: 'Diagnóstico',
                    value: 4,
                    disabled: true,
                    icon: "laptop-medical",
                }, {
                    name: 'Otros',
                    button: 'Otros',
                    value: 5,
                    disabled: true,
                    icon: "layer-group"
                    // icon: "person-arrow-up-from-line",
                },
                // {
                //     name: 'Tratamiento',
                //     button: 'Tratamiento',
                //     value: 6,
                //     disabled: true,
                //     icon: "pills",
                // }, {
                //     name: 'Lab. ext.',
                //     button: 'Lab. ext.',
                //     value: 7,
                //     disabled: true,
                //     icon: "vial",
                // }, {
                //     name: 'Receta',
                //     button: 'Receta',
                //     value: 8,
                //     disabled: true,
                //     icon: "file-prescription",
                // }, 
                {
                    name: 'Receta',
                    button: 'Receta',
                    value: 6,
                    disabled: true,
                    icon: "file-prescription",
                },
                {
                    name: 'Órdenes de Estudios',
                    button: 'Órdenes de Estudios',
                    value: 7,
                    disabled: true,
                    icon: "clipboard-list",
                },
            ],
            formulario: {
                MotivoConsulta: null,
                HistoriaEnfermedad: null
            },
            numericOptionGineco: {
                max: 100,
                min: 0,
                format: '#',
                step: 0
            },
            alergias: [],
            submitButtonOptionsValidar: {
                text: 'Validar',
                type: 'warning',
                icon: 'fas fa-check',
                width: 200,
                useSubmitBehavior: true,
                disabled: false,
            },

            infoCita: {},
            limpiarVariables: null,
            botonGuardar: null,
            historialClinico: {},
            historialSignos: {},
            signosCita: {},
            especialidades: {},
            mostrarLaboratorio: false,
            mostrarExamenes: false,
            mostrarEvolucion: false,
            mostrarColegiado: false,
            usuarioCorporativo: null,
            deshabilitarContraseña: true,

            permisoEdicion: false,
            permisoEdicionSignos: false,

            busquedaConsulta: false //Variable para indicar si se están llamando los componentes desde búsqueda de paciente
        }
    },
    props: {},
    methods: {
        handleSubmit(e) {
            e.preventDefault()
            if (this.usuarioCorporativo === null && this.deshabilitarContraseña) {
                this.Validar()
            } else if (this.usuarioCorporativo !== null && !this.deshabilitarContraseña) {
                this.LoginColegiado()
            }
        },
        CargarCita(e) {
            if (e.TipoBusqueda) { // Esta es la búsqueda de paciente para simular el Historial telefónico
                this.infoCita = {
                    ...e
                }
                //Al momento de cargar una nueva cita deshabilita todas las pestañas del módulo
                for (let i of this.panelHistorial._dataSource._items) {
                    i.disabled = true
                }

                this.panelHistorial._dataSource._items[0].disabled = false //Habilita la pestaña de antecedentes
                this.panelHistorial._dataSource._items[5].disabled = false //Habilita la pestaña de recetas

                if (this.infoCita.IdAfiliado !== '' && this.infoCita.IdAfiliado !== null) {
                    this.panelHistorial._dataSource._items[6].disabled = false // Habilita la pestaña de órdenes de estudio siempre que tenga Id de afiliado
                }
                this.panelHistorial.option("selectedIndex", 0)

                this.busquedaConsulta = true
            } else {
                if (e.IdCliente) {
                    if (this.permisoEdicion) {
                        for (let i of this.panelHistorial._dataSource._items) {
                            if (i.value !== 7) {
                                i.disabled = false
                            }
                        }
                    } else {
                        this.panelHistorial._dataSource._items[0].disabled = false

                        if (this.permisoEdicionSignos) {
                            this.panelHistorial._dataSource._items[2].disabled = false
                        }

                        this.panelHistorial.option("selectedIndex", 0)
                    }
                }
                if (e.IdCita !== "" && e.IdCliente !== "") {
                    this.BusquedaHistorialClinico(e)
                }

                this.busquedaConsulta = false
            }
        },
        CargarAlergias() {
            this.axios.post('/app/v1_Historial_Clinico/CatalogoAlergias', {})
                .then(resp => {
                    this.alergias = resp.data.json
                })
        },
        LimpiarVariables() {
            this.infoCita = {}
            this.$refs.Antecedentes.LimpiarVariables()
            this.$refs.Evolucion.refresh()
            if (this.$refs.Examen && this.$refs.Examen.LimpiarVariables) {
                this.$refs.Examen.LimpiarVariables()
            }
            if (this.$refs.Signos && this.$refs.Signos.LimpiarVariables) {
                this.$refs.Signos.LimpiarVariables()
            }
            if (this.$refs.Diagnostico && this.$refs.Diagnostico.LimpiarVariables) {
                this.$refs.Diagnostico.LimpiarVariables()
            }
            if (this.$refs.Otros && this.$refs.Otros.LimpiarVariables) {
                this.$refs.Otros.LimpiarVariables()
            }

            if (this.$refs.Laboratorio && this.$refs.Laboratorio.LimpiarVariables) {
                this.$refs.Laboratorio.refresh()
            }
            if (this.$refs.Examenes && this.$refs.Examenes.refresh) {
                this.$refs.Examenes.refresh()
            }
            if (this.$refs.Receta && this.$refs.Receta.LimpiarVariables) {
                this.$refs.Receta.LimpiarVariables()
            }
            if (this.$refs.Receta && this.$refs.Receta.LimpiarVariables) {
                this.$refs.Receta.LimpiarVariables()
            }
            if (this.$refs.OrdenesEstudios && this.$refs.OrdenesEstudios.LimpiarVariables) {
                this.$refs.OrdenesEstudios.LimpiarVariables()
            }

            this.formulario.MotivoConsulta = null
            this.formulario.HistoriaEnfermedad = null
            for (let i of this.panelHistorial._dataSource._items) {
                i.disabled = true
            }
            this.panelHistorial.option("selectedIndex", 0)
        },
        onSelectionChangedTabs(e) {
            this.botonGuardar = e.addedItems[0]
        },
        BusquedaHistorialClinico(e) {
            this.axios.post('/app/v1_Historial_Clinico/BusquedaHistorialClinico', {
                    IdCliente: e.IdCliente,
                    IdCita: e.IdCita
                })
                .then(resp => {
                    this.formulario.MotivoConsulta = resp.data.json[0].Motivo !== null ? resp.data.json[0].Motivo.replace(/\r\n/g, '\n') : null
                    this.formulario.HistoriaEnfermedad = resp.data.json[0].Historia !== null ? resp.data.json[0].Historia.replace(/\r\n/g, '\n') : null

                    this.infoCita = {
                        ...e,
                        ...resp.data.json[0]
                    }

                    this.infoCita.MotivoReferencia = this.infoCita.MotivoReferencia !== null ? this.infoCita.MotivoReferencia.replace(/\r\n/g, '\n') : null
                    this.infoCita.Tratamiento = this.infoCita.Tratamiento !== null ? this.infoCita.Tratamiento.replace(/\r\n/g, '\n') : null
                    this.infoCita.Laboratorio = this.infoCita.Laboratorio !== null ? this.infoCita.Laboratorio.replace(/\r\n/g, '\n') : null
                    this.infoCita.Anotaciones = this.infoCita.Anotaciones !== null ? this.infoCita.Anotaciones.replace(/\r\n/g, '\n') : null
                    this.infoCita.ImpresionClinica = this.infoCita.ImpresionClinica !== null ? this.infoCita.ImpresionClinica.replace(/\r\n/g, '\n') : null
                    this.infoCita.IdAfiliado = this.infoCita.IdAfiliado?.trim()

                    if (this.infoCita.CodigoDiagnostico !== null && this.infoCita.IdAfiliado !== '' && this.permisoEdicion) {
                        this.panelHistorial._dataSource._items[6].disabled = false
                    }
                })
        },
        ActualizarDatosGinecologicos(e) {
            this.infoCita.EdadMenarquia = e.Menarquia
            this.infoCita.EdadMenopausia = e.Menopausia
            this.infoCita.FUR = e.FUR
        },
        ActualizarDiagnostico(e) {
            this.infoCita.CodigoDiagnostico = e.CodigoDiagnostico
            this.infoCita.ImpresionClinica = e.ImpresionClinica
            this.infoCita.Anotaciones = e.Anotaciones
        },
        GuardarMotivoHistoria() {
            if (this.infoCita.IdConsulta !== null) {
                this.axios.post('/app/v1_Historial_Clinico/ActualizarMotivoHistoria', {
                    IdConsulta: this.infoCita.IdConsulta,
                    Motivo: this.formulario.MotivoConsulta ? this.formulario.MotivoConsulta.replace(/\n/g, '\r\n') : null,
                    Historia: this.formulario.HistoriaEnfermedad ? this.formulario.HistoriaEnfermedad.replace(/\n/g, '\r\n') : null,
                    IdCliente: this.infoCita.IdCliente
                })
            }
        },
        AccionDeBoton() {
            if (this.botonGuardar.value === 2) {
                return this.$refs.Examen.GuadarExamenFisico()
            }
            if (this.botonGuardar.value === 3) {
                return this.$refs.Signos.GuadarSignosVitales()
            }
            if (this.botonGuardar.value === 4) {
                return this.$refs.Diagnostico.GuadarDiagnostico()
            }
            if (this.botonGuardar.value === 5) {
                return this.$refs.Otros.GuadarOtros()
            }
            return null
        },
        FinalizarCita() {
            this.GuardarFinalizar()
            this.Validar()

        },
        Validar() {
            this.axios.post('/app/v1_Historial_Clinico/BusquedaCorporativo', {
                    Corporativo: this.sesion.corporativo,
                })
                .then((resp) => {
                    if (resp.data.json.length > 0) {
                        this.usuarioCorporativo = resp.data.json[0]

                        //Se verifica el tipo del usuario
                        if (this.usuarioCorporativo.Tipo === 'CASA-CORTESIA') {
                            this.BusquedaHonorariosCita()
                        } else {
                            this.ActualizarHonorarios(2)
                        }
                    } else {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Colegiado no válido',
                            acceptText: 'Aceptar',
                            text: 'El corporativo del usuario no está asociado a ningún Ajeno. Comuniquese con la jefatura de Honorarios.',
                            buttonCancel: 'border',
                            clientWidth: 100,
                            accept: () => {},
                        })
                    }
                })
        },
        BusquedaHonorariosCita() {
            this.axios.post('/app/v1_Historial_Clinico/BusquedaHonorariosCita', {
                    Colegiado: this.usuarioCorporativo.Colegiado,
                    IdCita: this.infoCita.IdCita
                })
                .then((resp) => {
                    let valido = resp.data.json[0].ExisteHonorario
                    if (valido === 'S') {
                        this.ActualizarHonorarios(1)
                    } else if (valido === 'N') {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Honorarios no existentes',
                            acceptText: 'Aceptar',
                            text: 'Los honorarios para la especialidad del médico o tipo de cita no existe. INFORMAR a la Coordinadora de Clínicas y vuelva a validar cuando le indiquen.',
                            buttonCancel: 'border',
                            clientWidth: 100,
                            accept: () => {},
                        })
                    }
                })
        },
        ActualizarHonorarios(tipoMedico) {
            this.axios.post('/app/v1_Historial_Clinico/ActualizarHonorarios', {
                    Codigo: this.usuarioCorporativo.Codigo,
                    IdCita: this.infoCita.IdCita,
                    TipoMedico: tipoMedico
                })
                .then(() => {
                    this.AgregarHonorariosNuevos()
                })
        },
        AgregarHonorariosNuevos() {
            this.axios.post('/app/ajenos/HonorariosCrear', {
                    Tipo: 4,
                    Cita: this.infoCita.IdCita
                })
                .then(() => {
                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'Honorarios generados correctamente',
                        acceptText: 'Aceptar',
                        text: 'Se han generado los honorarios correspondientes a la cita.',
                        buttonCancel: 'border',
                        clientWidth: 100,
                        accept: () => {},
                    })
                })
        },
        GuardarFinalizar() {
            if (this.$refs.Examen && this.$refs.Examen.GuadarExamenFisico) {
                this.$refs.Examen.GuadarExamenFisico()
            }
            if (this.$refs.Signos && this.$refs.Signos.GuadarSignosVitales) {
                this.$refs.Signos.GuadarSignosVitales()
            }
            if (this.$refs.Diagnostico && this.$refs.Diagnostico.GuadarDiagnostico) {
                this.$refs.Diagnostico.GuadarDiagnostico()
            }
            if (this.$refs.Otros && this.$refs.Otros.GuadarOtros) {
                this.$refs.Otros.GuadarOtros()
            }

            this.GuardarMotivoHistoria()
        },
        CargarPDF() {
            this.$reporte_modal({
                Nombre: 'Historial',
                Opciones: {
                    IdCliente: this.infoCita.IdCliente
                }
            })
        },
    },
    mounted() {
        this.CargarAlergias();

        this.$validar_funcionalidad('/HISTORIALCLINICO/HC002', 'EDITAR', (d) => {
            this.permisoEdicion = d.status
        })

        this.$validar_funcionalidad('/HISTORIALCLINICO/HC002', 'EDITARSIGNOS', (d) => {
            this.permisoEdicionSignos = d.status
        })
    },
    watch: {
        'mostrarColegiado'(newval) {
            if (newval === false) {
                this.usuarioCorporativo = null
                this.deshabilitarContraseña = true

                this.submitButtonOptionsValidar = {
                    text: 'Validar',
                    type: 'warning',
                    icon: 'fas fa-check',
                    width: 200,
                    useSubmitBehavior: true,
                    disabled: false,
                }
            }
        },

        'infoCita.CodigoDiagnostico'(newval) {
            if (newval !== null && newval !== '' && newval && this.infoCita.IdAfiliado !== '' && this.permisoEdicion) {
                this.panelHistorial._dataSource._items[6].disabled = false
            }
        }
    },
    created() {
        loadMessages(esMessages)
        locale(navigator.language)
    },
    computed: {
        colCountByScreen() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: this.Sexo === 'F' ? 3 : 2,
                    lg: this.Sexo === 'F' ? 3 : 2
                };
        },

        panelHistorial: function () {
            return this.$refs[tabPanelHistorial].instance;
        },

        sesion() {
            return this.$store.state.sesion
        },
    },
}
</script>

<style>
.dx-popup-title {
    background-color: #f4eade !important;
    color: #2f496e !important;
}

.dx-list-item>#itemsMenuDrawer {
    color: #2988bc !important;
    background-color: #f4eade !important;
}

.dx-list-item-selected>#itemsMenuDrawer {
    color: #f4eade !important;
    background-color: #ed8c72 !important;
}

.dx-scrollable-container {
    touch-action: auto !important;
}

/*Ancho mínmo de los grid*/
#Contenido .dx-datagrid {
    min-width: 302px;
}

/**Modal actualizacion de peso y talla */
#popupTallaPeso .vs-popup {
    width: 400px !important;
}

.dx-datagrid-headers td {
    vertical-align: middle !important;
}

.dx-resizable {
    display: inline-grid;
}

.dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed)>td {
    background-color: #00ccff !important;
    color: black !important;
}

.dx-datagrid-rowsview .dx-row.dx-data-row.dx-column-lines.dx-state-hover>td {
    background-color: #79e4ff !important;
    color: black !important;
    font-size: 16px;
}

.dx-datagrid-headers {
    background-color: linen !important;
    color: black !important;
    font-weight: bold;
}

.dx-viewport,
.dx-device-phone,
.dx-device-mobile,
.dx-device-android,
.dx-device-android-6,
.dx-theme-generic,
.dx-theme-generic-typography,
.dx-color-scheme-light,
.dx-overlay-wrapper {
    color: #2980B9 !important;
}

.buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

.button {
    height: 40px;
    width: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px !important;
}

.button:hover {
    background-color: blue !important;
}

.div-button {
    display: flex;
    justify-content: center;
}

.sticky {
    position: -webkit-sticky !important;
    /* for browser compatibility */
    position: sticky !important;
    top: 0px;
    background-color: rgb(117, 177, 255);
    z-index: 500;
}

.main-historial {
    display: grid;
    height: calc(100vh - 49px - 67px);
    grid-template-areas: "Encabezado Encabezado""Opciones Contenido";
    grid-template-columns: 0fr 1fr;
    grid-template-rows: 100px 1fr;
    margin: 0;
    padding: 0;
    right: 0;
    background-color: rgb(255, 255, 255);
    position: fixed;
    left: 80px;
    width: calc(100%-80px);
    min-width: 400px;
}

.Encabezado {
    grid-area: Encabezado;
    overflow-y: hidden;
    font-size: small;
    /* background-color: #d0e1f9; */
    background: rgb(208, 225, 249);
    background: linear-gradient(90deg, rgba(208, 225, 249, 1) 67%, rgba(119, 175, 255, 1) 94%, rgba(2, 0, 36, 1) 100%);
    color: #2f496e;
}

.Contenido {
    grid-area: Contenido;
    overflow-y: auto;
}

/*Ancho mínmo de los grid*/
#Contenido .dx-datagrid {
    min-width: 302px;
}

.fixed1 {
    /* for browser compatibility */
    position: fixed !important;
    bottom: 35px;
    /* left: 130px !important; */
    /* background-color: #5fbcfa; */
    padding: 3px;
    z-index: 9999 !important;
}

[dir] .dx-popup-title {
    height: 3em !important;
}

.dx-overlay-wrapper.dx-popup-wrapper.dx-dropdowneditor-overlay.dx-dropdownlist-popup-wrapper.dx-selectbox-popup-wrapper {
    z-index: 999999 !important;
}

.dx-overlay-wrapper.dx-popup-wrapper.dx-header-filter-menu {
    z-index: 999999 !important;
}

.dx-button.dx-button-warning {
    background-color: #ffc107;
}
</style>
