USE MASTER;
IF (SELECT SUSER_ID('USRHISJEFECAJA')) IS NULL
/*PROD -> */ CREATE LOGIN [USRHISJEFECAJA] WITH PASSWORD=N'U$H1$0J3FC@JA', DEFAULT_DATABASE=[HOSPITAL], DEFAULT_LANGUAGE=[us_english], CHECK_EXPIRATION=OFF, CHECK_POLICY=OFF

USE [HOSPITAL]
GO
--DROP USER USRHISJEFECAJA
IF (SELECT USER_ID('USRHISJEFECAJA')) IS NULL
	CREATE USER [USRHISJEFECAJA] FOR LOGIN [USRHISJEFECAJA] WITH DEFAULT_SCHEMA=[dbo]
GO

USE [master]	

IF (SELECT SUSER_ID('USRREPORTEJEFECAFA')) IS NULL
/*PROD -> */ CREATE LOGIN [USRREPORTEJEFECAFA] WITH PASSWORD=N'U$H1$R3P()J3FC@JA', DEFAULT_DATABASE=[HOSPITAL], DEFAULT_LANGUAGE=[us_english], CHECK_EXPIRATION=OFF, CHECK_POLICY=OFF

USE [HOSPITAL]
GO
--DROP USER USRREPORTEJEFECAFA
IF (SELECT USER_ID('USRREPORTEJEFECAFA')) IS NULL
	CREATE USER [USRREPORTEJEFECAFA] FOR LOGIN [USRREPORTEJEFECAFA] WITH DEFAULT_SCHEMA=[dbo]
GO

USE [CONTADB]
GO
--DROP USER USRREPORTEJEFECAFA
IF (SELECT USER_ID('USRREPORTEJEFECAFA')) IS NULL
	CREATE USER [USRREPORTEJEFECAFA] FOR LOGIN [USRREPORTEJEFECAFA] WITH DEFAULT_SCHEMA=[dbo]
GO


-->>
USE [master]	
GO
IF (SELECT SUSER_ID('USRFACTURAELECTRONICA')) IS NULL
/*PROD -> */ CREATE LOGIN [USRFACTURAELECTRONICA] WITH PASSWORD=N'U$HF@CT3l3>TR0NIC@H8320!', DEFAULT_DATABASE=[HOSPITAL], DEFAULT_LANGUAGE=[us_english], CHECK_EXPIRATION=OFF, CHECK_POLICY=OFF

USE [NOMINADB]
GO
--DROP USER USRFACTURAELECTRONICA
IF (SELECT USER_ID('USRFACTURAELECTRONICA')) IS NULL
	CREATE USER [USRFACTURAELECTRONICA] FOR LOGIN [USRFACTURAELECTRONICA] WITH DEFAULT_SCHEMA=[dbo]
GO

USE [CONTADB]
GO
--DROP USER USRFACTURAELECTRONICA
IF (SELECT USER_ID('USRFACTURAELECTRONICA')) IS NULL
	CREATE USER [USRFACTURAELECTRONICA] FOR LOGIN [USRFACTURAELECTRONICA] WITH DEFAULT_SCHEMA=[dbo]
GO

USE [CAFETERIA]
GO
--DROP USER USRFACTURAELECTRONICA
IF (SELECT USER_ID('USRFACTURAELECTRONICA')) IS NULL
	CREATE USER [USRFACTURAELECTRONICA] FOR LOGIN [USRFACTURAELECTRONICA] WITH DEFAULT_SCHEMA=[dbo]
GO

-->>
USE PLANESMEDICOS
  
IF (SELECT USER_ID('USRHISJEFECAJA')) IS NULL
	CREATE USER [USRHISJEFECAJA] FOR LOGIN [USRHISJEFECAJA] WITH DEFAULT_SCHEMA=[dbo]
  



USE HOSPITAL

  
IF (SELECT USER_ID('USRHISJEFECAJA')) IS NULL
	CREATE USER [USRHISJEFECAJA] FOR LOGIN [USRHISJEFECAJA] WITH DEFAULT_SCHEMA=[dbo]
  

USE NOMINADB
  
IF (SELECT USER_ID('USRHISJEFECAJA')) IS NULL
	CREATE USER [USRHISJEFECAJA] FOR LOGIN [USRHISJEFECAJA] WITH DEFAULT_SCHEMA=[dbo]
  

USE CONTADB
  
IF (SELECT USER_ID('USRHISJEFECAJA')) IS NULL
	CREATE USER [USRHISJEFECAJA] FOR LOGIN [USRHISJEFECAJA] WITH DEFAULT_SCHEMA=[dbo]
  

USE CAFETERIA
  
IF (SELECT USER_ID('USRHISJEFECAJA')) IS NULL
	CREATE USER [USRHISJEFECAJA] FOR LOGIN [USRHISJEFECAJA] WITH DEFAULT_SCHEMA=[dbo]
  

USE INVENTARIO
  
IF (SELECT USER_ID('USRHISJEFECAJA')) IS NULL
	CREATE USER [USRHISJEFECAJA] FOR LOGIN [USRHISJEFECAJA] WITH DEFAULT_SCHEMA=[dbo]
  

USE HOSPITAL;
GRANT EXECUTE ON OBJECT::dbo.SpHisAnulaFactura
    TO USRHISJEFECAJA;
  

USE HOSPITAL;
GRANT EXECUTE ON OBJECT::dbo.SpHisValidaAnulFac
    TO USRHISJEFECAJA;
  

USE HOSPITAL;
GRANT SELECT ON Facturas TO USRHISJEFECAJA;
  

USE HOSPITAL;
GRANT SELECT ON FacturasFace TO USRHISJEFECAJA;
  

USE HOSPITAL;
GRANT SELECT ON Correlativo_Facturas TO USRHISJEFECAJA;
  

USE NOMINADB;
GRANT SELECT ON EmpHospital TO USRHISJEFECAJA;
  

USE CONTADB;
GRANT SELECT ON ResolucionesFacturas TO USRHISJEFECAJA;
  

USE CAFETERIA;
GRANT SELECT ON Caferesolucionesfacturas TO USRHISJEFECAJA;
  

USE CONTADB;
GRANT SELECT ON Empresas TO USRHISJEFECAJA;
  

USE CONTADB;
GRANT SELECT ON EmpresasDefaults TO USRHISJEFECAJA;
  

USE HOSPITAL;
GRANT SELECT ON Recibos TO USRHISJEFECAJA;
GRANT UPDATE ON Recibos TO USRHISJEFECAJA;
  

USE HOSPITAL;
GRANT SELECT ON RecibosFacturas TO USRHISJEFECAJA;
GRANT DELETE ON RecibosFacturas TO USRHISJEFECAJA;
  

USE HOSPITAL;
GRANT SELECT ON Cargos TO USRHISJEFECAJA;
GRANT UPDATE ON Cargos TO USRHISJEFECAJA;
  

USE HOSPITAL;
GRANT SELECT ON AjenosCobrados TO USRHISJEFECAJA;
GRANT DELETE ON AjenosCobrados TO USRHISJEFECAJA;
  

USE HOSPITAL;
GRANT SELECT ON FacturasLoteAdmisiones TO USRHISJEFECAJA;
  

USE HOSPITAL;
GRANT SELECT ON FacturasAdicional TO USRHISJEFECAJA;
  

USE HOSPITAL;
GRANT SELECT ON Admisiones TO USRHISJEFECAJA;
GRANT UPDATE ON Admisiones TO USRHISJEFECAJA;
  

USE NOMINADB;
GRANT SELECT ON EmpPagosDetalle TO USRHISJEFECAJA;
GRANT UPDATE ON EmpPagosDetalle TO USRHISJEFECAJA;
  

USE HOSPITAL;
GRANT SELECT ON AjenosPagos TO USRHISJEFECAJA;
GRANT UPDATE ON AjenosPagos TO USRHISJEFECAJA;
  

USE HOSPITAL;
GRANT SELECT ON PaquetesCargos TO USRHISJEFECAJA;
GRANT DELETE ON PaquetesCargos TO USRHISJEFECAJA;
  

USE CONTADB;
GRANT SELECT ON Periodos TO USRHISJEFECAJA;
  

USE HOSPITAL;
GRANT SELECT ON FaceContingencias TO USRHISJEFECAJA;
  

USE HOSPITAL;
GRANT SELECT ON Remisiones TO USRHISJEFECAJA;
  

USE HOSPITAL;
GRANT SELECT ON FacturasAdicional TO USRHISJEFECAJA;
  

USE INVENTARIO;
GRANT SELECT ON Compras TO USRHISJEFECAJA;
  

USE NOMINADB;
GRANT SELECT ON EmpAnticipos TO USRHISJEFECAJA;
  

USE NOMINADB;
GRANT SELECT ON EmpAnticiposDetalle TO USRHISJEFECAJA;
  








USE HOSPITAL
GRANT EXECUTE ON SpHisOpcionesOrdenes TO USRHISJEFECAJA


USE HOSPITAL
GRANT EXECUTE ON spRecalcularCargos TO USRHISJEFECAJA
  

USE HOSPITAL
GRANT EXECUTE ON SpAdmObtienePolizasSaludSiemprexPaciente TO USRHISJEFECAJA
  

USE PLANESMEDICOS
GRANT SELECT ON clientes TO USRHISJEFECAJA
  

USE PLANESMEDICOS
GRANT SELECT ON contratos TO USRHISJEFECAJA
  
  

-------------------
USE HOSPITAL;
GRANT EXECUTE ON OBJECT::dbo.SpHisDescuento
    TO USRHISJEFECAJA;
  

USE HOSPITAL;
GRANT SELECT ON Admisiones TO USRHISJEFECAJA;
  

USE HOSPITAL;
GRANT SELECT ON Pacientes TO USRHISJEFECAJA;
  
USE NOMINADB;
GRANT SELECT ON emphospital TO USRHISJEFECAJA;
GO

-->> PERIMSOS PARA DEVOLUCION CON ADMISIÓN
-->> Permisos USUARIO y TABLAS

USE [HOSPITAL]
  

GRANT SELECT ON dbo.AdmisionesDevoluciones TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.ListaPacientesPorAdmision TO USRHISJEFECAJA; 

GRANT SELECT ON dbo.ListaNotasCreditoVigentes TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Cargos TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Recibos TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.RecibosPagos TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.RecibosFacturas TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.AdmisionesDevoluciones TO USRHISJEFECAJA;
GRANT UPDATE ON dbo.AdmisionesDevoluciones TO USRHISJEFECAJA;

USE [CONTADB]
  


GRANT SELECT ON dbo.BancosCuentas TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.ListaBcosCuentas TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.BancosMovtos TO USRHISJEFECAJA; 

-->>
--//Crear Procedimiento almacenado
--SpHisDevoEnAdmision

-->>
--//Permisos
USE HOSPITAL
  
GRANT EXECUTE ON OBJECT::dbo.SpHisDevoEnAdmision TO USRHISJEFECAJA;




-->> PERIMSOS PARA ANULACION FACTURAS TABLAS FACTORIZADAS 2023 FEBRERO
-->> Permisos USUARIO y TABLAS
USE HOSPITAL 
GRANT SELECT ON dbo.Facturas TO USRHISJEFECAJA;
GRANT UPDATE ON dbo.Facturas TO USRHISJEFECAJA;
GRANT INSERT ON dbo.Anulaciones TO USRHISJEFECAJA;


USE INVENTARIO
  
GRANT SELECT ON dbo.InvCargosDetalle TO USRHISJEFECAJA; 
GRANT INSERT ON dbo.InvCargosDetalle TO USRHISJEFECAJA;

-->>
--//Crear Procedimiento almacenado
--SpHisAnulaTbFactorizadas

-->>
--//Permisos
USE HOSPITAL
GRANT EXECUTE ON OBJECT::dbo.SpHisAnulaTbFactorizadas TO USRHISJEFECAJA;





-->>Permisos Blanca tercera fase

-->> PERIMSOS PARA ANULACION DE AJENOS FACTURAS
-->> Permisos USUARIO y TABLAS
USE HOSPITAL
  ;
GRANT SELECT ON dbo.OrdenesTipos TO USRHISJEFECAJA;
GRANT SELECT ON dbo.AjenosCobrados TO USRHISJEFECAJA;
GRANT SELECT ON dbo.Pacientes TO USRHISJEFECAJA;
GRANT SELECT ON dbo.Cargos TO USRHISJEFECAJA;
GRANT UPDATE ON dbo.Cargos TO USRHISJEFECAJA;
GRANT SELECT ON dbo.Anulaciones TO USRHISJEFECAJA;
GRANT INSERT ON dbo.Anulaciones TO USRHISJEFECAJA;
GRANT SELECT ON dbo.AjenosCobrados TO USRHISJEFECAJA;
GRANT DELETE ON dbo.AjenosCobrados TO USRHISJEFECAJA;

-->>
--//Crear Procedimiento almacenado
--SpHisAnulaAjenosFactura

		
-->>
--//Permisos
USE HOSPITAL
  
GRANT EXECUTE ON OBJECT::dbo.SpHisAnulaAjenosFactura TO USRHISJEFECAJA;
		
		
		
		
-->> PERIMSOS PARA ANULACION FACTURAS CAFETERIA 2023 FEBRERO

-->> Permisos USUARIO y TABLAS
USE HOSPITAL
  
GRANT SELECT ON dbo.Contabilidad TO USRHISJEFECAJA;
GRANT SELECT ON dbo.VentaDirectaDetalleAnulados TO USRHISJEFECAJA;
GRANT INSERT ON dbo.VentaDirectaDetalleAnulados TO USRHISJEFECAJA;
GRANT SELECT ON dbo.VentaDirectaDetalle TO USRHISJEFECAJA;

USE NominaDb
GRANT SELECT ON dbo.EmpAnticiposDetalle TO USRHISJEFECAJA;
GRANT DELETE ON dbo.EmpAnticiposDetalle TO USRHISJEFECAJA;

USE INVENTARIO
GRANT SELECT ON dbo.ExistenciasBodega TO USRHISJEFECAJA;
GRANT UPDATE ON dbo.ExistenciasBodega TO USRHISJEFECAJA;
GRANT SELECT ON dbo.Productos TO USRHISJEFECAJA;

USE [Cafeteria]
GRANT SELECT ON dbo.CafeFacturasPagos TO USRHISJEFECAJA;
GRANT UPDATE ON dbo.CafeFacturasPagos TO USRHISJEFECAJA;

-->>
--//Crear Procedimiento almacenado
--SpHisAnulaFacCafeteria

-->>
--//Permisos
USE HOSPITAL
  
GRANT EXECUTE ON OBJECT::dbo.SpHisAnulaFacCafeteria TO USRHISJEFECAJA;





		

-->> Permisos fase 3 y4 
USE HOSPITAL
GRANT EXECUTE ON spRecalcularCargos TO USRHISJEFECAJA
  

USE HOSPITAL
GRANT EXECUTE ON SpAdmObtienePolizasSaludSiemprexPaciente TO USRHISJEFECAJA
  

USE PLANESMEDICOS
GRANT SELECT ON clientes TO USRHISJEFECAJA
  

USE PLANESMEDICOS
GRANT SELECT ON contratos TO USRHISJEFECAJA
  


USE HOSPITAL
GRANT EXECUTE ON SpHisOpcionesOrdenes TO USRHISJEFECAJA




-->> Permisos Blanca fase 5
------------CONSULTA RECIBOS--------------
-->>
USE HOSPITAL
  
GRANT SELECT ON dbo.Recibos TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Anulaciones TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.RecibosFacturas TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.RecibosRemisiones TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Remisiones TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.RecibosAjenos TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Ajenos TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.RecibosPagos TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.AdmisionesDevoluciones TO USRHISJEFECAJA; 


USE INVENTARIO
  
GRANT SELECT ON dbo.BancosTarjetas TO USRHISJEFECAJA; 


USE HOSPITAL
  
GRANT EXECUTE ON OBJECT::dbo.SpHisConsultaRecibos TO USRHISJEFECAJA;


------------CONSULTA BITACORAS ADMISIONES--------------
-->>

USE HOSPITAL
  
GRANT SELECT ON dbo.Admisiones  TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Pacientes TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Devoluciones   TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Anulaciones  TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Recalcular  TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Reactivaciones  TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Altas  TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.DescuentosAplicados  TO USRHISJEFECAJA; 

USE Inventario
  

GRANT SELECT ON dbo.Compras  TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Proveedores  TO USRHISJEFECAJA; 


USE Contadb
  
GRANT SELECT ON dbo.Usuarios  TO USRHISJEFECAJA;

USE HOSPITAL
  
GRANT EXECUTE ON OBJECT::dbo.SpHisBitacoraAdmision TO USRHISJEFECAJA;




-------------CONSULTA DEVOLUCION DE ORDENES
-->


USE HOSPITAL
  
GRANT SELECT ON dbo.OrdenesTipos   TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Devoluciones TO USRHISJEFECAJA; 

USE Inventario
  
GRANT SELECT ON dbo.InventarioCargos   TO USRHISJEFECAJA; 


USE HOSPITAL
  
GRANT EXECUTE ON OBJECT::dbo.SpHisDevolucionOrdenes TO USRHISJEFECAJA;



-->Permisos Aaron Fase 5

USE HOSPITAL
GRANT EXECUTE ON OBJECT::dbo.SpHisConsultaOrdenesAnuladas
    TO USRHISJEFECAJA
  

USE HOSPITAL
GRANT SELECT ON CargosAnuladosDetalle TO USRHISJEFECAJA
  

USE HOSPITAL
GRANT SELECT ON Anulaciones TO USRHISJEFECAJA
  

USE HOSPITAL
GRANT SELECT ON OrdenesTipos TO USRHISJEFECAJA
  

USE INVENTARIO
GRANT SELECT ON InventarioCargos TO USRHISJEFECAJA
  


USE HOSPITAL
GRANT EXECUTE ON OBJECT::dbo.SpHisTiposOrdenes TO USRHISJEFECAJA;





USE contadb
GRANT select ON OBJECT::dbo.SplitStringIndex TO USRHISJEFECAJA;




USE HOSPITAL
GO
GRANT EXECUTE ON OBJECT::dbo.SpHisAreas TO USRHISJEFECAJA;


USE HOSPITAL
GO
GRANT EXECUTE ON OBJECT::dbo.spOpcionesProformaOrtopedia TO USRHISJEFECAJA;



USE HOSPITAL
GO
GRANT EXECUTE ON OBJECT::dbo.spOpcionesProformaOrtopedia TO USRHISJEFECAJA;


GRANT EXECUTE ON OBJECT::dbo.spProformaOrtopedia TO USRHISJEFECAJA;

GRANT EXECUTE ON OBJECT::dbo.spProformaOrtopedia TO USRHISJEFECAJA;
USE INVENTARIO
GRANT SELECT ON OrdenesCompra TO USRHISJEFECAJA;
GRANT SELECT ON ExistenciasBodega TO USRHISJEFECAJA;
GRANT SELECT ON Bodegas TO USRHISJEFECAJA;
GRANT SELECT ON InventarioCargos TO USRHISJEFECAJA;
GRANT INSERT ON InventarioCargos TO USRHISJEFECAJA;


USE PLANESMEDICOS
GO
GRANT SELECT ON PCorrelativos TO USRHISJEFECAJA;
GRANT SELECT  ON COEXRecetaLAB_det TO USRHISJEFECAJA;
GRANT INSERT  ON COEXRecetaLAB_det TO USRHISJEFECAJA;
GRANT SELECT  ON COEXRecetaLAB_det_anulacion TO USRHISJEFECAJA;
GRANT INSERT  ON COEXRecetaLAB_det_anulacion TO USRHISJEFECAJA;
GRANT SELECT ON aut_hospitalaria_enc TO USRHISJEFECAJA;


USE HOSPITAL
GRANT execute 
	ON dbo.SpAnulacionDevolucionOrdenes
	TO USRHISJEFECAJA
GO
​
USE INVENTARIO
GRANT SELECT ON InventarioCargos TO USRHISJEFECAJA;
GRANT INSERT ON InventarioCargos TO USRHISJEFECAJA;
GO
​
USE PLANESMEDICOS
GRANT SELECT ON PCorrelativos TO USRHISJEFECAJA;
GO
​
USE HOSPITAL
GRANT EXECUTE ON sp_Busqueda_Proveedores TO USRHISJEFECAJA;
GO
​
USE HOSPITAL
GRANT execute
	ON dbo.spBusquedaAdmisiones2
	TO USRHISJEFECAJA
​
USE HOSPITAL
GRANT execute 
	ON dbo.spProformaOrtopedia
	TO USRHISJEFECAJA
​
​
USE CONTADB​
GRANT SELECT ON CONTADB.dbo.SplitStringIndex TO USRHISJEFECAJA;
 
​
USE INVENTARIO​
GRANT SELECT ON dbo.NTUnidadesMedida TO USRHISJEFECAJA;
 
​
USE INVENTARIO​
GRANT SELECT, UPDATE ON dbo.ProductosClase TO USRHISJEFECAJA;
 
​
USE INVENTARIO​
GRANT SELECT,UPDATE ON dbo.ProductosSubClase TO USRHISJEFECAJA;
 
​
USE INVENTARIO​
GRANT SELECT ON dbo.Productos TO USRHISJEFECAJA;
GRANT INSERT ON dbo.Productos TO USRHISJEFECAJA;
 
​
USE INVENTARIO​
GRANT SELECT ON dbo.BitacoraModificacionCatalogo TO USRHISJEFECAJA;
GRANT INSERT ON dbo.BitacoraModificacionCatalogo TO USRHISJEFECAJA;
 
​
​
USE inventario​
GRANT EXECUTE ON INVENTARIO.dbo.F_Codigo_Producto TO USRHISJEFECAJA;
 
​
USE HOSPITAL​
GRANT EXECUTE ON HOSPITAL.dbo.spInventarioIngresoProductoInterno TO USRHISJEFECAJA;
 
​
USE CONTADB​
GRANT SELECT ON CONTADB.dbo.EmpresasDefaults TO USRHISJEFECAJA;
 
​
USE INVENTARIO​
GRANT SELECT, UPDATE ON INVENTARIO.dbo.ExistenciasBodega TO USRHISJEFECAJA;
 
​
USE INVENTARIO​
GRANT SELECT ON INVENTARIO.dbo.BODEGAS TO USRHISJEFECAJA;
 
​
USE NOMINADB​
GRANT SELECT ON NOMINADB.dbo.BasesXTipoAdmision TO USRHISJEFECAJA;
 
​
USE INVENTARIO​
GRANT SELECT ON INVENTARIO.dbo.CategoriaCuentasCostos TO USRHISJEFECAJA;
 
​
USE INVENTARIO​
GRANT SELECT ON INVENTARIO.dbo.InvCargosDetalle TO USRHISJEFECAJA;
GRANT INSERT ON INVENTARIO.dbo.InvCargosDetalle TO USRHISJEFECAJA;
 
​
USE INVENTARIO​
GRANT SELECT ON INVENTARIO.dbo.proveedores TO USRHISJEFECAJA;
 
​
USE INVENTARIO​
GRANT SELECT ON INVENTARIO.dbo.OrdenesCompra TO USRHISJEFECAJA;
 
​
USE HOSPITAL​
GRANT EXECUTE ON HOSPITAL.dbo.spOpcionesProformaOrtopedia TO USRHISJEFECAJA;
 
​
USE HOSPITAL​
GRANT EXECUTE ON HOSPITAL.dbo.spOpcionesProformaOrtopedia TO [USRREPORTEJEFECAFA];
 
​
USE PLANESMEDICOS​
GRANT SELECT ON PLANESMEDICOS.dbo.PlanesXContrato TO USRHISJEFECAJA;
 
​
USE PLANESMEDICOS​
GRANT SELECT ON PLANESMEDICOS.dbo.AfiliadosXContrato TO USRHISJEFECAJA;
 
  
USE HOSPITAL
GRANT EXECUTE ON OBJECT::dbo.SpHisConsultaNotasCredito
    TO USRHISJEFECAJA
GO

USE HOSPITAL
GRANT SELECT ON NotasCredito TO USRHISJEFECAJA
GO

USE HOSPITAL
GRANT SELECT ON Anulaciones TO USRHISJEFECAJA
GO



USE HOSPITAL;
GRANT EXECUTE ON OBJECT::dbo.SpHisDescuento
    TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON Admisiones TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON Pacientes TO USRHISJEFECAJA;
GO

USE NOMINADB;
GRANT SELECT ON emphospital TO USRHISJEFECAJA;
GO


USE HOSPITAL
GRANT EXECUTE ON OBJECT::dbo.SpHisConsultaNotasCredito
    TO USRHISJEFECAJA
GO

USE HOSPITAL
GRANT SELECT ON NotasCredito TO USRHISJEFECAJA
GO

USE HOSPITAL
GRANT SELECT ON Anulaciones TO USRHISJEFECAJA
GO


USE HOSPITAL;
GRANT EXECUTE ON OBJECT::dbo.SpHisDevoAntSinAdmision
    TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON Recibos TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON AdmisionesDevoluciones TO USRHISJEFECAJA;
GRANT INSERT ON AdmisionesDevoluciones TO USRHISJEFECAJA;
GRANT UPDATE ON AdmisionesDevoluciones TO USRHISJEFECAJA;
GO

USE CONTADB;
GRANT SELECT ON ListaBcosCuentas TO USRHISJEFECAJA;
GO

USE CONTADB;
GRANT SELECT ON BancosCuentas TO USRHISJEFECAJA;
GO

USE CONTADB;
GRANT SELECT ON BancosMovtos TO USRHISJEFECAJA;
GO


USE HOSPITAL;
GRANT EXECUTE ON OBJECT::dbo.SpHisDevoluciones
    TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON Cargos TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON Ordenes TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON Admisiones TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON Pacientes TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON RadiologiaInformes TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON LaboratorioResultados TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON LotesProntoPagoDetalle TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT EXECUTE ON OBJECT::dbo.SpHisGeneraFaces
    TO USRFACTURAELECTRONICA;
GO

USE HOSPITAL;
GRANT SELECT ON Facturas TO USRFACTURAELECTRONICA;
GRANT UPDATE ON Facturas TO USRFACTURAELECTRONICA;
GO

USE HOSPITAL;
GRANT SELECT ON FacturasFace TO USRFACTURAELECTRONICA;
GRANT UPDATE ON FacturasFace TO USRFACTURAELECTRONICA;
GO

USE HOSPITAL;
GRANT SELECT ON Correlativo_Facturas TO USRFACTURAELECTRONICA;
GO

USE NOMINADB;
GRANT SELECT ON EmpHospital TO USRFACTURAELECTRONICA;
GO

USE CONTADB;
GRANT SELECT ON ResolucionesFacturas TO USRFACTURAELECTRONICA;
GO

USE CAFETERIA;
GRANT SELECT ON Caferesolucionesfacturas TO USRFACTURAELECTRONICA;
GO

USE CONTADB;
GRANT SELECT ON Empresas TO USRFACTURAELECTRONICA;
GO

USE CONTADB;
GRANT SELECT ON EmpresasDefaults TO USRFACTURAELECTRONICA;
GO

USE HOSPITAL;
GRANT EXECUTE ON OBJECT::dbo.SpHisAnulaFactura
    TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT EXECUTE ON OBJECT::dbo.SpHisValidaAnulFac
    TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON Facturas TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON FacturasFace TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON Correlativo_Facturas TO USRHISJEFECAJA;
GO

USE NOMINADB;
GRANT SELECT ON EmpHospital TO USRHISJEFECAJA;
GO

USE CONTADB;
GRANT SELECT ON ResolucionesFacturas TO USRHISJEFECAJA;
GO

USE CAFETERIA;
GRANT SELECT ON Caferesolucionesfacturas TO USRHISJEFECAJA;
GO

USE CONTADB;
GRANT SELECT ON Empresas TO USRHISJEFECAJA;
GO

USE CONTADB;
GRANT SELECT ON EmpresasDefaults TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON Recibos TO USRHISJEFECAJA;
GRANT UPDATE ON Recibos TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON RecibosFacturas TO USRHISJEFECAJA;
GRANT DELETE ON RecibosFacturas TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT UPDATE ON Cargos TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON AjenosCobrados TO USRHISJEFECAJA;
GRANT DELETE ON AjenosCobrados TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON FacturasLoteAdmisiones TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON FacturasAdicional TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON Admisiones TO USRHISJEFECAJA;
GRANT UPDATE ON Admisiones TO USRHISJEFECAJA;
GO

USE NOMINADB;
GRANT SELECT ON EmpPagosDetalle TO USRHISJEFECAJA;
GRANT UPDATE ON EmpPagosDetalle TO USRHISJEFECAJA;
GO


USE NOMINADB;
GRANT SELECT  ON EmpPagosDetalle TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON AjenosPagos TO USRHISJEFECAJA;
GRANT UPDATE ON AjenosPagos TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON PaquetesCargos TO USRHISJEFECAJA;
GRANT DELETE ON PaquetesCargos TO USRHISJEFECAJA;
GO

USE CONTADB;
GRANT SELECT ON Periodos TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON FaceContingencias TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON Remisiones TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON FacturasAdicional TO USRHISJEFECAJA;
GO

USE INVENTARIO;
GRANT SELECT ON Compras TO USRHISJEFECAJA;
GO

USE NOMINADB;
GRANT SELECT ON EmpAnticipos TO USRHISJEFECAJA;
GO

USE NOMINADB;
GRANT SELECT ON EmpAnticiposDetalle TO USRHISJEFECAJA;
GO


USE HOSPITAL
GRANT EXECUTE ON OBJECT::dbo.SpHisReportesAuditoria
    TO [USRREPORTEJEFECAFA]
GO

USE HOSPITAL
GRANT EXECUTE ON OBJECT::dbo.SpHisReportesAuditoria
    TO USRHISJEFECAJA
GO

USE HOSPITAL
GRANT SELECT ON Anulaciones TO [USRREPORTEJEFECAFA]
GO

USE HOSPITAL
GRANT SELECT ON FacturasFacE TO [USRREPORTEJEFECAFA]
GO

USE HOSPITAL
GRANT SELECT ON Modificaciones TO [USRREPORTEJEFECAFA]
GO

USE HOSPITAL
GRANT SELECT ON Facturas TO [USRREPORTEJEFECAFA]
GO

USE HOSPITAL
GRANT SELECT ON Recibos TO [USRREPORTEJEFECAFA]
GO

USE HOSPITAL
GRANT SELECT ON Ordenes TO [USRREPORTEJEFECAFA]
GO

USE HOSPITAL
GRANT SELECT ON ListaDocumentos TO [USRREPORTEJEFECAFA]
GO

USE HOSPITAL
GRANT SELECT ON Admisiones TO [USRREPORTEJEFECAFA]
GO

USE HOSPITAL
GRANT SELECT ON Pacientes TO [USRREPORTEJEFECAFA]
GO

USE HOSPITAL
GRANT SELECT ON DescuentosAplicados TO [USRREPORTEJEFECAFA]
GO

USE HOSPITAL
GRANT SELECT ON Cargos TO [USRREPORTEJEFECAFA]
GO

USE HOSPITAL;
ALTER TABLE Paquetes
ADD Estado VARCHAR(1)

UPDATE HOSPITAL.dbo.Paquetes
SET Estado = 0
WHERE Empresa = 'MED'

USE HOSPITAL;   
GRANT EXECUTE ON OBJECT::dbo.SpHisTipoDescuento 
    TO USRHISJEFECAJA;  
GO 

USE HOSPITAL;   
GRANT EXECUTE ON OBJECT::dbo.SpHisPaquetes 
    TO USRHISJEFECAJA;  
GO 

USE HOSPITAL;   
GRANT EXECUTE ON OBJECT::dbo.SpHisHabitaciones 
    TO USRHISJEFECAJA;  
GO 

USE HOSPITAL;   
GRANT EXECUTE ON OBJECT::dbo.SpHisAreas 
    TO USRHISJEFECAJA;  
GO 

USE HOSPITAL;   
GRANT EXECUTE ON OBJECT::dbo.SpHisTiposOrdenes 
    TO USRHISJEFECAJA;  
GO 

USE HOSPITAL;   
GRANT EXECUTE ON OBJECT::dbo.SpHisCorrelativoNotasCredito 
    TO USRHISJEFECAJA;  
GO 

USE HOSPITAL;   
GRANT EXECUTE ON OBJECT::dbo.SpHisCorrelativoRecibos 
    TO USRHISJEFECAJA;  
GO 

USE HOSPITAL;   
GRANT EXECUTE ON OBJECT::dbo.SpHisCorrelativoFacturas 
    TO USRHISJEFECAJA;  
GO 

USE HOSPITAL;   
GRANT EXECUTE ON OBJECT::dbo.Sp_Busqueda_Productos 
    TO USRHISJEFECAJA;  
GO 

USE HOSPITAL;   
GRANT EXECUTE ON OBJECT::dbo.SpHisProductoServicio 
    TO USRHISJEFECAJA;  
GO 

USE HOSPITAL;
GRANT DELETE ON Areas_Habitaciones TO USRHISJEFECAJA;
GRANT INSERT ON Areas_Habitaciones TO USRHISJEFECAJA;
GRANT UPDATE ON Areas_Habitaciones TO USRHISJEFECAJA;
GRANT SELECT ON Areas_Habitaciones TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT DELETE ON Habitaciones TO USRHISJEFECAJA;
GRANT INSERT ON Habitaciones TO USRHISJEFECAJA;
GRANT UPDATE ON Habitaciones TO USRHISJEFECAJA;
GRANT SELECT ON Habitaciones TO USRHISJEFECAJA;
GO

USE INVENTARIO;
GRANT SELECT ON Productos TO USRHISJEFECAJA;
GO

USE INVENTARIO;
GRANT SELECT ON Complementarios TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT DELETE ON His_Tipo_Habitacion TO USRHISJEFECAJA;
GRANT INSERT ON His_Tipo_Habitacion TO USRHISJEFECAJA;
GRANT UPDATE ON His_Tipo_Habitacion TO USRHISJEFECAJA;
GRANT SELECT ON His_Tipo_Habitacion TO USRHISJEFECAJA;
GO

USE NOMINADB;
GRANT SELECT ON EmpHospital TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT DELETE ON Correlativo_Facturas TO USRHISJEFECAJA;
GRANT INSERT ON Correlativo_Facturas TO USRHISJEFECAJA;
GRANT UPDATE ON Correlativo_Facturas TO USRHISJEFECAJA;
GRANT SELECT ON Correlativo_Facturas TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT DELETE ON Correlativo_NotasCredito TO USRHISJEFECAJA;
GRANT INSERT ON Correlativo_NotasCredito TO USRHISJEFECAJA;
GRANT UPDATE ON Correlativo_NotasCredito TO USRHISJEFECAJA;
GRANT SELECT ON Correlativo_NotasCredito TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT DELETE ON Correlativo_Recibos TO USRHISJEFECAJA;
GRANT INSERT ON Correlativo_Recibos TO USRHISJEFECAJA;
GRANT UPDATE ON Correlativo_Recibos TO USRHISJEFECAJA;
GRANT SELECT ON Correlativo_Recibos TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT DELETE ON Paquetes TO USRHISJEFECAJA;
GRANT INSERT ON Paquetes TO USRHISJEFECAJA;
GRANT UPDATE ON Paquetes TO USRHISJEFECAJA;
GRANT SELECT ON Paquetes TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT DELETE ON PaquetesLineas TO USRHISJEFECAJA;
GRANT INSERT ON PaquetesLineas TO USRHISJEFECAJA;
GRANT UPDATE ON PaquetesLineas TO USRHISJEFECAJA;
GRANT SELECT ON PaquetesLineas TO USRHISJEFECAJA;
GO

USE INVENTARIO;
GRANT SELECT ON ProductosPrecios TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT DELETE ON TipoDescuento TO USRHISJEFECAJA;
GRANT INSERT ON TipoDescuento TO USRHISJEFECAJA;
GRANT UPDATE ON TipoDescuento TO USRHISJEFECAJA;
GRANT SELECT ON TipoDescuento TO USRHISJEFECAJA;
GO


USE HOSPITAL;
GRANT DELETE ON OrdenesTipos TO USRHISJEFECAJA;
GRANT INSERT ON OrdenesTipos TO USRHISJEFECAJA;
GRANT UPDATE ON OrdenesTipos TO USRHISJEFECAJA;
GRANT SELECT ON OrdenesTipos TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON Ordenes TO USRHISJEFECAJA;
GO

USE HOSPITAL;   
GRANT EXECUTE ON OBJECT::dbo.SpHisAnulaRecibos 
    TO USRHISJEFECAJA;  
GO 

USE CONTADB;
GRANT SELECT ON Periodos TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON Recibos TO USRHISJEFECAJA;
GRANT UPDATE ON Recibos TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON RecibosPagos TO USRHISJEFECAJA;
GRANT DELETE ON RecibosPagos TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON RemesasDetalleCreditos TO USRHISJEFECAJA;
GRANT UPDATE ON RemesasDetalleCreditos TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON RecibosRemisiones TO USRHISJEFECAJA;
GRANT DELETE ON RecibosRemisiones TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON Remisiones TO USRHISJEFECAJA;
GRANT UPDATE ON Remisiones TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON RecibosFacturas TO USRHISJEFECAJA;
GRANT DELETE ON RecibosFacturas TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON Facturas TO USRHISJEFECAJA;
GRANT UPDATE ON Facturas TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON AjenosCobrados TO USRHISJEFECAJA;
GRANT UPDATE ON AjenosCobrados TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON RecibosAjenos TO USRHISJEFECAJA;
GRANT DELETE ON RecibosAjenos TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON RecibosAplicados TO USRHISJEFECAJA;
GRANT DELETE ON RecibosAplicados TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON Anulaciones TO USRHISJEFECAJA;
GRANT INSERT ON Anulaciones TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON RecibosDevoluciones TO USRHISJEFECAJA;
GO

USE HOSPITAL;
GRANT SELECT ON AjenosPagos TO USRHISJEFECAJA;
GO

USE PLANESMEDICOS
GRANT SELECT ON coexrecetalab_det TO USRHISJEFECAJA;
GO

USE PLANESMEDICOS
GRANT SELECT ON coexrecetalab_det_anulacion TO USRHISJEFECAJA;
GRANT INSERT ON coexrecetalab_det_anulacion TO USRHISJEFECAJA;
GO

USE PLANESMEDICOS
GRANT SELECT ON aut_hospitalaria_enc TO USRHISJEFECAJA;
GO

USE PLANESMEDICOS
GRANT SELECT ON aut_hospitalaria_enc_anulacion TO USRHISJEFECAJA;
GRANT INSERT ON aut_hospitalaria_enc_anulacion TO USRHISJEFECAJA;
GO

USE PLANESMEDICOS
GRANT SELECT ON aut_hospitalaria_calculo TO USRHISJEFECAJA;
GO

USE PLANESMEDICOS
GRANT SELECT ON aut_hospitalaria_calculo_anulacion TO USRHISJEFECAJA;
GRANT INSERT ON aut_hospitalaria_calculo_anulacion TO USRHISJEFECAJA;
GO

USE PLANESMEDICOS
GRANT SELECT ON aut_hospitalaria_enc TO USRHISJEFECAJA;
GRANT UPDATE ON aut_hospitalaria_enc TO USRHISJEFECAJA;
GO

USE CONTADB
GRANT SELECT ON AuxiliarClientes TO USRHISJEFECAJA;
GRANT UPDATE ON AuxiliarClientes TO USRHISJEFECAJA;
GO




USE HOSPITAL
GRANT EXECUTE ON OBJECT::dbo.SpHisConsultaOrdenDiagnostico
    TO USRHISJEFECAJA
GO

USE HOSPITAL
GRANT SELECT ON Ordenes TO USRHISJEFECAJA
GO

USE HOSPITAL
GRANT SELECT ON Ajenos TO USRHISJEFECAJA
GO

USE HOSPITAL
GRANT SELECT ON Admisiones TO USRHISJEFECAJA
GO

USE HOSPITAL
GRANT SELECT ON Pacientes TO USRHISJEFECAJA
GO

USE HOSPITAL
GRANT SELECT ON ObservacionesOrdenes TO USRHISJEFECAJA
GO

USE HOSPITAL
GRANT SELECT ON Cargos TO USRHISJEFECAJA
GO

USE INVENTARIO
GRANT SELECT ON Productos TO USRHISJEFECAJA
GO



USE HOSPITAL
GRANT EXECUTE ON OBJECT::dbo.sp_bitacora_errores TO USRHISJEFECAJA
GRANT SELECT ON HisBitacoraErrores TO USRHISJEFECAJA;
GRANT INSERT ON HisBitacoraErrores TO USRHISJEFECAJA;
GO


USE hospital;
GRANT SELECT  ON AjenosPagos TO USRHISJEFECAJA;
GO

USE hospital;
GRANT SELECT  ON PaquetesCargos TO USRHISJEFECAJA;
GO



USE Cafeteria
GO
GRANT SELECT ON dbo.CafeFacturasPagos   TO USRHISJEFECAJA;


USE HOSPITAL;
GRANT EXECUTE ON OBJECT::dbo.SpHisBitacoraErrores TO USRHISJEFECAJA;
GRANT SELECT ON [HOSPITAL].[dbo].[HisBitacoraErrores] TO [USRHISJEFECAJA]
GRANT INSERT ON [HOSPITAL].[dbo].[HisBitacoraErrores] TO [USRHISJEFECAJA]

USE HOSPITAL;
GRANT EXECUTE ON OBJECT::dbo.SpHisBitacoraErrores TO USRFACTURAELECTRONICA;
GRANT SELECT ON [HOSPITAL].[dbo].[HisBitacoraErrores] TO [USRFACTURAELECTRONICA]
GRANT INSERT ON [HOSPITAL].[dbo].[HisBitacoraErrores] TO [USRFACTURAELECTRONICA]




use HOSPITAL;
GRANT EXECUTE ON spHisCargosHabitacion TO USRHISJEFECAJA



USE PLANESMEDICOS;

GRANT SELECT on MultinivelesPagos  to USRHISJEFECAJA

GRANT SELECT on PagoCitas  to USRHISJEFECAJA

GRANT SELECT on AfiliadosPagosDetalle  to USRHISJEFECAJA
GRANT SELECT on pagocupones  to USRHISJEFECAJA


GRANT SELECT on Citas  to USRHISJEFECAJA
GRANT update on Citas  to USRHISJEFECAJA
GRANT SELECT on PagoCitas  to USRHISJEFECAJA
GRANT update on PagoCitas  to USRHISJEFECAJA
GRANT SELECT on AfiliadosRecibosDeCuotas  to USRHISJEFECAJA
GRANT update on AfiliadosRecibosDeCuotas  to USRHISJEFECAJA

GRANT SELECT on AfiliadosPagosDetalle  to USRHISJEFECAJA
GRANT update on AfiliadosPagosDetalle  to USRHISJEFECAJA
GRANT DELETE on AfiliadosPagosDetalle  to USRHISJEFECAJA

GRANT SELECT on AfiliadosPagoDeCuotas  to USRHISJEFECAJA
GRANT update on AfiliadosPagoDeCuotas  to USRHISJEFECAJA
GRANT DELETE on AfiliadosPagoDeCuotas  to USRHISJEFECAJA

GRANT SELECT on MultinivelesPagos  to USRHISJEFECAJA
GRANT DELETE on MultinivelesPagos  to USRHISJEFECAJA
GRANT SELECT on PagoCupones  to USRHISJEFECAJA
GRANT update on PagoCupones  to USRHISJEFECAJA

GRANT SELECT on FacturasCitasAnuladas  to USRHISJEFECAJA
GRANT INSERT on FacturasCitasAnuladas  to USRHISJEFECAJA


use hospital
GRANT EXECUTE on SpHisAnulaFacClinica to USRHISJEFECAJA

GRANT EXECUTE on SpRecibos to USRHISJEFECAJA
GRANT EXECUTE on SpRecibosPagos to USRHISJEFECAJA
GRANT EXECUTE on SpBusquedaBancosTarjetas to USRHISJEFECAJA
GRANT EXECUTE on SpAuxiliarClientes to USRHISJEFECAJA
GRANT EXECUTE on SpModificaciones to USRHISJEFECAJA

USE HOSPITAL
GRANT EXECUTE ON OBJECT::dbo.SpHisReportesAuditoria
    TO USRREPORTEJEFECAFA
GO

USE HOSPITAL
GRANT EXECUTE ON OBJECT::dbo.SpHisReportesAuditoria
    TO USRHIS
GO

USE HOSPITAL
GRANT SELECT ON Anulaciones TO USRREPORTEJEFECAFA
GO

USE HOSPITAL
GRANT SELECT ON FacturasFacE TO USRREPORTEJEFECAFA
GO

USE HOSPITAL
GRANT SELECT ON Modificaciones TO USRREPORTEJEFECAFA
GO

USE HOSPITAL
GRANT SELECT ON Facturas TO USRREPORTEJEFECAFA
GO

USE HOSPITAL
GRANT SELECT ON Recibos TO USRREPORTEJEFECAFA
GO

USE HOSPITAL
GRANT SELECT ON Ordenes TO USRREPORTEJEFECAFA
GO

USE HOSPITAL
GRANT SELECT ON ListaDocumentos TO USRREPORTEJEFECAFA
GO

USE HOSPITAL
GRANT SELECT ON Admisiones TO USRREPORTEJEFECAFA
GO

USE HOSPITAL
GRANT SELECT ON Pacientes TO USRREPORTEJEFECAFA
GO

USE HOSPITAL
GRANT SELECT ON DescuentosAplicados TO USRREPORTEJEFECAFA
GO

USE HOSPITAL
GRANT SELECT ON Cargos TO USRREPORTEJEFECAFA
GO

USE CONTADB
GRANT SELECT ON Empresas TO USRREPORTEJEFECAFA
GO

USE HOSPITAL
GRANT SELECT ON TipoDescuento TO USRREPORTEJEFECAFA
GO


/**********

***********/
USE HOSPITAL
GO

GRANT EXECUTE ON SpHisDevoluciones TO USRHISJEFECAJA
GO

USE HOSPITAL
GO
GRANT SELECT,UPDATE  ON HIS_Honorarios TO USRHISJEFECAJA
GO

USE CONTADB
GO

GRANT SELECT ON CONTADB.dbo.SplitStringIndex TO USRHISJEFECAJA;
GO

USE CONTADB
GO

GRANT SELECT ON CONTADB.dbo.EmpresasDefaults TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO

GRANT SELECT, UPDATE ON INVENTARIO.dbo.ExistenciasBodega TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO

GRANT SELECT ON INVENTARIO.dbo.BODEGAS TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO

GRANT SELECT ON INVENTARIO.dbo.CategoriaCuentasCostos TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO

GRANT INSERT ON INVENTARIO.dbo.InvCargosDetalle TO USRHISJEFECAJA;
GO


USE PLANESMEDICOS
GO

GRANT SELECT ON PLANESMEDICOS.dbo.PlanesXContrato TO USRHISJEFECAJA;
GO

USE PLANESMEDICOS
GO

GRANT SELECT ON PLANESMEDICOS.dbo.AfiliadosXContrato TO USRHISJEFECAJA;
GO

USE HOSPITAL
GO

GRANT EXECUTE ON spHisCargosHabitacion TO USRHISJEFECAJA  
GO

USE INVENTARIO
GO

GRANT SELECT  ON  ProductosPrecios  TO USRHISJEFECAJA
GO

USE HOSPITAL
GO
GRANT SELECT ON HIS_Honorarios TO USRHISJEFECAJA
GO

USE HOSPITAL
GRANT EXECUTE ON spRecalcularCargos TO USRHISJEFECAJA
GO

USE HOSPITAL
GRANT EXECUTE ON SpAdmObtienePolizasSaludSiemprexPaciente TO USRHISJEFECAJA
GO

USE PLANESMEDICOS
GRANT SELECT ON clientes TO USRHISJEFECAJA
GO

USE PLANESMEDICOS
GRANT SELECT ON contratos TO USRHISJEFECAJA
GO

USE HOSPITAL
GO

GRANT execute 
	ON dbo.SpAnulacionDevolucionOrdenes
	TO USRHISJEFECAJA

USE INVENTARIO
GO
GRANT SELECT ON InventarioCargos TO USRHISJEFECAJA;
GRANT INSERT ON InventarioCargos TO USRHISJEFECAJA;
GO

USE PLANESMEDICOS
GO

GRANT SELECT ON PCorrelativos TO USRHISJEFECAJA;
GO

USE HOSPITAL
GO

GRANT EXECUTE ON sp_Busqueda_Proveedores TO USRHISJEFECAJA;
GO

USE HOSPITAL
GO

GRANT execute
	ON dbo.spBusquedaAdmisiones2
	TO USRHISJEFECAJA


USE HOSPITAL
GO

GRANT execute 
	ON dbo.spProformaOrtopedia
	TO USRHISJEFECAJA


USE CONTADB
GO

GRANT SELECT ON CONTADB.dbo.SplitStringIndex TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO

GRANT SELECT ON dbo.NTUnidadesMedida TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO

GRANT SELECT, UPDATE ON dbo.ProductosClase TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO

GRANT SELECT,UPDATE ON dbo.ProductosSubClase TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO
GRANT SELECT ON dbo.Productos TO USRHISJEFECAJA;
GRANT INSERT ON dbo.Productos TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO
GRANT SELECT ON dbo.BitacoraModificacionCatalogo TO USRHISJEFECAJA;
GRANT INSERT ON dbo.BitacoraModificacionCatalogo TO USRHISJEFECAJA;
GO


USE inventario
GO

GRANT EXECUTE ON INVENTARIO.dbo.F_Codigo_Producto TO USRHISJEFECAJA;
GO

USE HOSPITAL
GO

GRANT EXECUTE ON HOSPITAL.dbo.spInventarioIngresoProductoInterno TO USRHISJEFECAJA;
GO

USE CONTADB
GO

GRANT SELECT ON CONTADB.dbo.EmpresasDefaults TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO

GRANT SELECT, UPDATE ON INVENTARIO.dbo.ExistenciasBodega TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO

GRANT SELECT ON INVENTARIO.dbo.BODEGAS TO USRHISJEFECAJA;
GO

USE NOMINADB
GO

GRANT SELECT ON NOMINADB.dbo.BasesXTipoAdmision TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO

GRANT SELECT ON INVENTARIO.dbo.CategoriaCuentasCostos TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO
GRANT SELECT ON INVENTARIO.dbo.InvCargosDetalle TO USRHISJEFECAJA;
GRANT INSERT ON INVENTARIO.dbo.InvCargosDetalle TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO

GRANT SELECT ON INVENTARIO.dbo.proveedores TO USRHISJEFECAJA;
GO

USE INVENTARIO
GO

GRANT SELECT ON INVENTARIO.dbo.OrdenesCompra TO USRHISJEFECAJA;
GO

USE HOSPITAL
GO

GRANT EXECUTE ON HOSPITAL.dbo.spOpcionesProformaOrtopedia TO USRHISJEFECAJA;
GO

USE HOSPITAL
GO

GRANT EXECUTE ON HOSPITAL.dbo.spOpcionesProformaOrtopedia TO [USRREPORTEJEFECAFA];
GO

USE PLANESMEDICOS
GO

GRANT SELECT ON PLANESMEDICOS.dbo.PlanesXContrato TO USRHISJEFECAJA;
GO

USE PLANESMEDICOS
GO

GRANT SELECT ON PLANESMEDICOS.dbo.AfiliadosXContrato TO USRHISJEFECAJA;
GO

USE HOSPITAL
GO

GRANT SELECT ON HOSPITAL.dbo.Ordenes TO USRHISJEFECAJA;
GO

USE HOSPITAL
GO

GRANT SELECT ON HOSPITAL.dbo.Admisiones TO USRHISJEFECAJA;
GO

USE HOSPITAL
GO

GRANT SELECT ON HOSPITAL.dbo.Pacientes TO USRHISJEFECAJA;
GO

USE HOSPITAL
GO

GRANT SELECT ON HOSPITAL.dbo.His_Honorarios TO USRHISJEFECAJA;
GO

USE HOSPITAL
GO

GRANT SELECT ON HOSPITAL.dbo.Cargos TO USRHISJEFECAJA;
GO

USE HOSPITAL
GO

GRANT SELECT ON HOSPITAL.dbo.LotesProntoPagoDetalle TO USRHISJEFECAJA;
GO

USE HOSPITAL
GO

GRANT SELECT ON HOSPITAL.dbo.Ajenos TO USRHISJEFECAJA;
GO


USE HOSPITAL
GO

GRANT UPDATE ON HOSPITAL.dbo.Ordenes TO USRHISJEFECAJA;
GO

USE HOSPITAL
GO

GRANT INSERT ON HOSPITAL.dbo.Modificaciones TO USRHISJEFECAJA;
GO

USE HOSPITAL
GO

GRANT SELECT,INSERT,UPDATE,DELETE ON HOSPITAL.dbo.OrdenesTipos TO USRHISJEFECAJA;
GO

USE HOSPITAL
GO

GRANT SELECT ON HOSPITAL.dbo.Ordenes TO USRHISJEFECAJA;
GO

USE NOMINADB
GO

GRANT SELECT ON NOMINADB.dbo.EmpHospital TO USRHISJEFECAJA;
GO

/************************************************************************************************************
 *************************************INICIO INSERCCION DE DATOS*********************************************
 ************************************************************************************************************/

BEGIN TRAN
USE HOSPITAL
GO

SET IDENTITY_INSERT HisCategoriasTipos ON

INSERT INTO HisCategoriasTipos(IdCategoriasTipos,Empresa,Nombre,CodigoOrdenTipo,NombreClasificacion,Clasificacion,Activo,Corporativo,FechaRegistro) 
VALUES (1,'MED','ORTOPEDIA','OR','LABORATORIO',1,'S',63737,GetDate()) 

SET IDENTITY_INSERT HisCategoriasTipos OFF
GO

USE HOSPITAL
GO
insert into inventario..productosclase(Empresa,Codigo,Nombre,Clasificacion)
                               values ('MED','8','Ortopedia','S')

USE HOSPITAL
GO
insert into inventario..productossubclase(Empresa,Clase,Codigo,Nombre,Ancho,Correlativo,
																					VentaExterna,GenerarEscalasPrecios,RecalcularEscalasPrecios)
                                   values('MED','8','1','No Cargables',5,1,'N','N','N')

select * from inventario..productossubclase where empresa = 'med' and clase = '8'

ROLLBACK TRAN

/************************************************************************************************************
 *************************************FIN INSERCCION DE DATOS************************************************
 ************************************************************************************************************/



USE HOSPITAL
GRANT SELECT ON dbo.OrdenesTipos TO USRHISJEFECAJA;
GRANT SELECT ON dbo.AjenosCobrados TO USRHISJEFECAJA;
GRANT SELECT ON dbo.Pacientes TO USRHISJEFECAJA;
GRANT SELECT ON dbo.Cargos TO USRHISJEFECAJA;
GRANT UPDATE ON dbo.Cargos TO USRHISJEFECAJA;
GRANT INSERT ON dbo.Anulaciones TO USRHISJEFECAJA;
GRANT SELECT ON dbo.AjenosCobrados TO USRHISJEFECAJA;
GRANT DELETE ON dbo.AjenosCobrados TO USRHISJEFECAJA;


-->> PERMISO PROCEDIMIENTO
USE HOSPITAL
GO
GRANT EXECUTE ON OBJECT::dbo.SpHisAnulaAjenosFactura TO USRHISJEFECAJA;



-->> PERIMSOS PARA ANULACION FACTURAS CAFETERIA 2023 FEBRERO
USE HOSPITAL
GO
GRANT SELECT ON dbo.Facturas TO USRHISJEFECAJA;
GRANT SELECT ON dbo.FacturasFace TO USRHISJEFECAJA;
GRANT SELECT ON dbo.Contabilidad TO USRHISJEFECAJA;
GRANT INSERT ON dbo.VentaDirectaDetalleAnulados TO USRHISJEFECAJA;
GRANT SELECT ON dbo.VentaDirectaDetalle TO USRHISJEFECAJA;

USE NominaDb
GO
GRANT SELECT ON dbo.EmpAnticiposDetalle TO USRHISJEFECAJA;
GRANT DELETE ON dbo.EmpAnticiposDetalle TO USRHISJEFECAJA;



USE INVENTARIO
GO
GRANT SELECT ON dbo.ExistenciasBodega TO USRHISJEFECAJA;
GRANT UPDATE ON dbo.ExistenciasBodega TO USRHISJEFECAJA;
GRANT SELECT ON dbo.Productos TO USRHISJEFECAJA;

USE [Cafeteria]
GO
GRANT SELECT ON dbo.CafeFacturasPagos TO USRHISJEFECAJA;
GRANT UPDATE ON dbo.CafeFacturasPagos TO USRHISJEFECAJA;


-->> PERMISO PROCEDIMIENTO
USE HOSPITAL
GO
GRANT EXECUTE ON OBJECT::dbo.SpHisAnulaFacCafeteria TO USRHISJEFECAJA;


-->> PERIMSOS PARA ANULACION FACTURAS TABLAS FACTORIZADAS 2023 FEBRERO
USE HOSPITAL 
GO
GRANT SELECT ON dbo.Facturas TO USRHISJEFECAJA;
GRANT UPDATE ON dbo.Facturas TO USRHISJEFECAJA;
GRANT INSERT ON dbo.Anulaciones TO USRHISJEFECAJA;


USE INVENTARIO
GO
GRANT SELECT ON dbo.InvCargosDetalle TO USRHISJEFECAJA; 
GRANT INSERT ON dbo.InvCargosDetalle TO USRHISJEFECAJA;



-->> PERMISO PROCEDIMIENTO
USE HOSPITAL
GO
GRANT EXECUTE ON OBJECT::dbo.SpHisAnulaTbFactorizadas TO USRHISJEFECAJA;


-->> PERIMSOS PARA DEVOLUCION CON ADMISIÓN
USE [HOSPITAL]
GO

GRANT SELECT ON dbo.AdmisionesDevoluciones TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.ListaPacientesPorAdmision TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Facturas TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.ListaNotasCreditoVigentes TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Cargos TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Recibos TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.RecibosPagos TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.RecibosFacturas TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.AdmisionesDevoluciones TO USRHISJEFECAJA;
GRANT UPDATE ON dbo.AdmisionesDevoluciones TO USRHISJEFECAJA;

USE [CONTADB]
GO


GRANT SELECT ON dbo.BancosCuentas TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.ListaBcosCuentas TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.BancosMovtos TO USRHISJEFECAJA; 


-->> PERMISO PROCEDIMIENTO
USE HOSPITAL
GO
GRANT EXECUTE ON OBJECT::dbo.SpHisDevoEnAdmision TO USRHISJEFECAJA;


-->> CONSULTA RECIBOS
USE HOSPITAL
GO
GRANT SELECT ON dbo.Recibos TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Anulaciones TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.RecibosFacturas TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Facturas TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.FacturasFace TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.RecibosRemisiones TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Remisiones TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.RecibosAjenos TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Ajenos TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.RecibosPagos TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.AdmisionesDevoluciones TO USRHISJEFECAJA; 


USE INVENTARIO
GO
GRANT SELECT ON dbo.BancosTarjetas TO USRHISJEFECAJA; 


-->>  PERMISO PROCEDIMIENTO
USE HOSPITAL
GO
GRANT EXECUTE ON OBJECT::dbo.SpHisConsultaRecibos TO USRHISJEFECAJA;


------------CONSULTA BITACORAS ADMISIONES--------------
-->>

USE HOSPITAL
GO
GRANT SELECT ON dbo.Admisiones  TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Pacientes TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Devoluciones   TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Anulaciones  TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Recalcular  TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Reactivaciones  TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Altas  TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.DescuentosAplicados  TO USRHISJEFECAJA; 

USE Inventario
GO

GRANT SELECT ON dbo.Compras  TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Proveedores  TO USRHISJEFECAJA; 


USE Contadb
GO
GRANT SELECT ON dbo.Usuarios  TO USRHISJEFECAJA;


-->> PERMISO PROCEDIMIENTO

USE HOSPITAL
GO
GRANT EXECUTE ON OBJECT::dbo.SpHisBitacoraAdmision TO USRHISJEFECAJA;



-->> CONSULTA DEVOLUCION DE ORDENES


USE HOSPITAL
GO
GRANT SELECT ON dbo.OrdenesTipos   TO USRHISJEFECAJA; 
GRANT SELECT ON dbo.Devoluciones TO USRHISJEFECAJA; 

USE Inventario
GO
GRANT SELECT ON dbo.InventarioCargos   TO USRHISJEFECAJA; 

-->> PERMISO PROCEDIMIENTO
USE HOSPITAL
GO
GRANT EXECUTE ON OBJECT::dbo.SpHisDevolucionOrdenes TO USRHISJEFECAJA;