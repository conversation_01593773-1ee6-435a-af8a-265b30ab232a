<template>
<vx-card title="Autorización Orden de Compra">
    <div class="content content-pagex">
        <form>
            <div style="margin: 15px" class="flex flex-wrap">
                <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">
                    <div class="flex flex-wrap">
                        <div style="margin-left:10px;margin-right:20px" class="xs:w-full md:w-5/6 lg:w-5/6 xl:w-5/6">
                            <ValidationProvider name="Agencia">
                                <label class="typo__label">Departamentos</label>
                                <multiselect v-model="cbDepartamento" :options="ListaDepartamentos" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Departamento_seleccionada" placeholder="Seleccionar" @input="onChangeDepartamento">
                                    <span slot="noOptions">Lista no disponible.</span>
                                </multiselect>
                            </ValidationProvider>
                        </div>
                    </div>
                </div>

                <div class="xs:w-full md:w-8/12 lg:w-8/12 xl:w-8/12">
                    <div class="flex flex-wrap">
                        <div class="xs:w-full md:w-5/6 lg:w-5/6 xl:w-5/12">
                            <label class="typo__label">Estado Orden de Compra</label>
                            <multiselect v-model="cb_lista_operacion" :options="lista_estado" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Operacion_seleccionada" placeholder="Seleccionar" @input="onChangeEstado">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </div>

                        <div style="margin-left:10px;margin-right:20px" class="xs:w-full md:w-2/6 lg:w-2/6 xl:w-2/6">
                            <label class="typo__label">Fecha Inicio:</label>
                            <vs-input type="date" v-model="fecha_inicio" name="date1" />
                        </div>
                        <div class="xs:w-full md:w-3/6 lg:w-3/6 xl:w3/6">
                            <label class="typo__label">Fecha Final:</label>
                            <vs-input type="date" v-model="fecha_final" name="date1" />
                        </div>
                        <div class=" md:w-1/6 lg:w-1/6 xl:w-1/6">
                            <vs-button style="float:left;margin: 20px" color="primary" type="filled" icon-pack="fas" icon="fa-search" @click="Consulta_autorizaciones()"> Búsqueda</vs-button>
                        </div>

                    </div>
                </div>
            </div>
            <div style="margin: 15px" class="flex flex-wrap">
                <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">
                    <div class="flex flex-wrap">
                        <div style="margin-left:10px;margin-right:20px" class="xs:w-full md:w-5/6 lg:w-5/6 xl:w-5/6">
                            <ValidationProvider name="selectProv">
                                <label class="typo__label">Proveedor</label>
                                <multiselect v-model="cbProveedor" :options="listaProveedores" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="seleccion_proveedor" placeholder="Búsqueda" @input="onChangeProveedor">
                                    <span slot="noOptions">Lista no disponible.</span>
                                </multiselect>
                            </ValidationProvider>
                        </div>

                    </div>

                </div>

                <div class="xs:w-full md:w-8/12 lg:w-8/12 xl:w-8/12">
                    <div class="flex flex-wrap">

                        <div class="xs:w-full md:w-5/6 lg:w-5/6 xl:w-5/12">
                            <label class="typo__label">Estado de Validación</label>
                            <multiselect v-model="cb_lista_recepcion" :options="lista_recepcion" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="EstadoRecepcion" placeholder="Seleccionar" @input="onChangeRecepcion">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </div>
                        <div style="margin-left:10px;margin-right:20px">
                            <ValidationProvider name="nOrden" rules="required|numero_min:0|numero_entero" v-slot="{ errors }">
                                <label class="typo__label">De Orden No.</label>
                                <vs-input type="number" v-model="deOrden" :danger="errors.length>0" />
                            </ValidationProvider>
                        </div>

                        <div class="xs:w-full md:w-3/6 lg:w-3/6 xl:w-3/6">
                            <ValidationProvider name="pagos" rules="required|numero_min:0|numero_entero" v-slot="{ errors }">
                                <label class="typo__label">A Orden No.</label>
                                <vs-input type="number" v-model="aOrden" :danger="errors.length>0" />
                            </ValidationProvider>
                        </div>
                        <div class=" md:w-1/6 lg:w-1/6 xl:w-1/6" style="margin-left:10px;margin-right:20px">
                            <div class="w-full" style="float:left;margin: 10px">
                                <vs-radio v-model="idTipoSeleccionado" vs-name="idTipoSeleccionado" vs-value="S"> Servicio</vs-radio>
                            </div>
                            <div class="w-full" style="float:left;margin: 10px">
                                <vs-radio v-model="idTipoSeleccionado" vs-name="idTipoSeleccionado" vs-value="P"> Bien</vs-radio>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <label class="label-sizem">Si alguno de los filtros no es seleccionado, el resultado será obtener toda la información registrada. </label>
            <br>
            <vs-divider></vs-divider>
            <div class="flex flex-wrap">
                <div class="xs:w-full" align="left">
                    <vs-button style="float:right;margin: 20px" color="success" type="filled" icon-pack="fas" icon="fa-check-circle" @click="ReporteOrdenesValidadasCF() "> Órdenes Validadas C/Factura</vs-button>
                    <vs-button style="float:right;margin: 20px" color="success" type="filled" icon-pack="fas" icon="fa-file-excel " @click="ReporteConsolidado()" :disabled="habilitarRpt"> Consolidado Excel</vs-button>
                </div>
            </div>

        </form>

        <!---------------------- DETALLE SOLICITUDES PENDIENTES --------------->
        <vs-divider></vs-divider>
        <vs-table2 tooltip max-items="10" pagination :data="Lista_autorizaciones" noDataText="Sin datos disponibles" search id="tb_departamentos">

            <template slot="thead">
                <th width="80">Nº. Orden Compra </th>
                <th width="100">Envio</th>
                <th width="80">Nº. Solicitud </th>
                <th width="500">Departamento</th>
                <th width="130">Tipo</th>
                <th width="400">Proveedor</th>
                <th width="180">Fecha</th>
                <th width="500">Observación Orden de Compra</th>
                <th width="170">Total Orden Compra</th>
                <th width="100">No. Validación</th>
                <th width="100">Usuario de Validación</th>
                <th width="200">Estado de Validación</th>
                <th width="200">Estado Orden de Compra</th>
                <th width="100">Autorizar</th>
                <th width="120">Ver Orden de Compra </th>
            </template>

            <template slot-scope="{data}">
                <tr :key="indextr" v-for="(tr, indextr) in data">
                    <vs-td2 noTooltip :data="data[indextr].IDORDENCOMPRAENC">
                        {{data[indextr].IDORDENCOMPRAENC}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].NumeroProformaProveedor">
                        {{data[indextr].NumeroProformaProveedor}}
                    </vs-td2>
                    <vs-td2 noTooltip :data="data[indextr].IDSOLICITUDENCFK">
                        {{data[indextr].IDSOLICITUDENCFK}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].IDDEPARTAMENTOFK">
                        {{data[indextr].IDDEPARTAMENTOFK}}
                        
                        {{data[indextr].DESCRIPCION_DEPTO}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].IDPRODUCTOCLASEFK">
                        {{data[indextr].IDPRODUCTOCLASEFK}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].DESCRIPCION_PROVEEDOR">
                        {{data[indextr].DESCRIPCION_PROVEEDOR}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].FECHA_CREACION_ORDEN">
                        {{data[indextr].FECHA_CREACION_ORDEN}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].OBSERVACIONES">
                        {{data[indextr].OBSERVACIONES}}
                    </vs-td2>
                    <vs-td2 align="right" :data="data[indextr].TOTAL">
                        Q. {{data[indextr].TOTAL}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].VALIDACION">
                        {{data[indextr].VALIDACION}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].USUARIO">
                        {{data[indextr].USUARIO}}
                    </vs-td2>
                    <vs-td2 style="text-align: center;" :data="data[indextr].VALIDADA">
                        <label v-if="data[indextr].VALIDADA == 'S'" style=" height: 30px;background-color:green;width:120px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIP_VALIDADA}} </label>
                        <label v-if="data[indextr].VALIDADA == 'N'" style="height: 30px;background-color:#D9614D;width:120px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIP_VALIDADA}} </label>

                    </vs-td2>

                    <vs-td2 style="text-align: center;" :data="data[indextr].ESTADO">
                        <label v-if="data[indextr].ESTADO == 'P'" style="height: 30px;background-color:#B2BABB;width:120px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                        <label v-if="data[indextr].ESTADO == 'A'" style=" height: 30px;background-color:green;width:120px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                        <label v-if="data[indextr].ESTADO == 'R'" style="height: 30px;background-color:#D9614D;width:120px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                        <label v-if="data[indextr].ESTADO == 'C'" style="height: 30px;background-color:#e6cb32;width:120px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                    </vs-td2>

                    <vs-td2 noTooltip align="center">
                        <vs-button v-if="data[indextr].ESTADO == 'A' || data[indextr].ESTADO === 'R' || data[indextr].ESTADO === 'C'" disabled="false" color="primary" icon-pack="fas" icon="fa-check-double" style="display:inline-block;margin-right:2px" @click="Finalizar_Autorizacion(data[indextr])"></vs-button>
                        <vs-button v-else color="primary" icon-pack="fas" icon="fa-check-double" style="display:inline-block;margin-right:2px" @click="Finalizar_Autorizacion(data[indextr])"></vs-button>
                    </vs-td2>
                    <vs-td2 noTooltip align="center">
                        <vs-button color="success" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-download" @click="OrdenCompraRpt(data[indextr])"></vs-button>
                    </vs-td2>

                </tr>
            </template>
        </vs-table2>

        <!-------------------------------INICIO PARA ENVIO DE RESULTADOS-->

        <vs-popup classContent="popup-example" title="Configurar Envio correo" :active.sync="HabilitarVentanaCorreo" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
            <ValidationObserver ref="formValidate" mode="lazy">
                <form @submit.prevent="submitForm(reporte.opciones)">
                    <div class="flex flex-wrap">

                        <vs-row>
                            <vs-col vs-type="flex" vs-w="6">
                                <div class="w-4/5  p-2">
                                    <validationProvider name="Destinatario">
                                        <vs-input v-model="datosResultado.EnviarA" label="Destinatario" class="w-full" />
                                    </validationProvider>
                                </div>
                            </vs-col>

                            <vs-col vs-type="flex" vs-w="6">
                                <div class="w-4/5  p-2">
                                    <validationProvider name="Copiar a">
                                        <vs-input v-model="datosResultado.CopiarA" label="Copiar a" class="w-full" />
                                    </validationProvider>
                                </div>
                            </vs-col>

                        </vs-row>

                        <vs-row>
                            <vs-col vs-type="flex" vs-w="12">
                                <div class="w-full  p-2">
                                    <validationProvider name="Asunto">
                                        <vs-input v-model="datosResultado.Asunto" label="Asunto" class="w-full" />
                                    </validationProvider>
                                </div>
                            </vs-col>
                        </vs-row>

                        <br>
                        <vs-row>
                            <vs-col vs-type="flex" vs-w="12">
                                <div class="w-full  p-2" v-if="datosResultado.TipoOrden=='BIEN'">
                                    <vue-editor v-model="datosResultado.MensajeDescripcion" :editorToolbar="customToolbar"></vue-editor>
                                    <hr>
                                </div>
                                <div class="w-full  p-2" v-else>
                                    <vue-editor disabled :editorToolbar="customToolbar"></vue-editor>
                                    <hr>
                                </div>
                            </vs-col>
                        </vs-row>

                        <div class="w-full md:w-full lg:w-full xl:w-full">
                            <vs-button color="primary" style="float:right" type="filled" icon-pack="feather" icon="icon-save" @click="HabilitarVentanaCorreo=false;"> Guardar</vs-button>
                        </div>

                    </div>
                </form>
            </ValidationObserver>

        </vs-popup>
        <!-------------------------------FINALIZACION PARA ENVIO DE RESULTADOS-->

        <!--------------- EMERGENTE AUTORIZACION -------------->
        <vs-popup classContent="popup-example" title="Finalizar autorización" :active.sync="Estado_VentanaEmergente_Auto" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
                <form>

                    <div class="flex flex-wrap">
                        <br>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">

                            <label class="label-sizem">Orden de compra Seleccionada: </label>
                            <label class="label-sizem"> {{this.Datos_autorizacion_seleccionado.IDORDENCOMPRAENC}}</label>
                        </div>
                        <br>
                        <!---  AUTORIZAR / RECHAZAR ORDEN-->
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                            <label class="typo__label">Estado a definir: </label>
                            <multiselect v-model="cb_lista_estado_solicitud" :options="Lista_solicitud_estado" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="estado_solicitud_seleccionada" placeholder="Seleccionar" @input="onChangeEstadoSolicitud">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </div>
                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                            <vx-input-group class="">
                                <template slot="append">
                                    <vs-checkbox v-if="Id_estado_solicitu_seleccionado=='A' && Datos_autorizacion_seleccionado.NUMERO_DOCUMENTO =='1'" v-model="BanderaEnviarCorreo">Enviar Correo</vs-checkbox>
                                    <vs-button v-if="BanderaEnviarCorreo && Id_estado_solicitu_seleccionado=='A' && Datos_autorizacion_seleccionado.NUMERO_DOCUMENTO =='1' " id="button-with-loading-3" color="warning" icon-pack="fas" @click="HabilitarVentanaCorreo=true;" icon="fa-envelope">Ver mas..</vs-button>
                                    <vs-button v-else-if="BanderaEnviarCorreo== false  && Id_estado_solicitu_seleccionado=='A' && Datos_autorizacion_seleccionado.NUMERO_DOCUMENTO =='1'" v-model="BanderaEnviarCorreo" disabled id="button-with-loading-3" color="#757575" icon-pack="fas" icon="fa-envelope">Ver mas..</vs-button>

                                </template>
                            </vx-input-group>

                        </div>

                    </div>
                    <!---- ENVIAR CORREO ELECTRONICO-->
                    <div class="flex flex-wrap">
                    </div>
                    <!---------- OBSERVACIONES ------------>
                    <div class="w-full md:w-full lg:w-full xl:w-full">
                        <label class="typo__label">Observaciones</label>
                        <div class="vs-con-input">
                            <input type="text" class="vs-inputx vs-input--input large" cols="40" rows="5" v-model="Observaciones">
                        </div>
                    </div>
                    <vs-divider></vs-divider>

                    <div class="w-full md:w-full lg:w-full xl:w-full">
                        <vs-button color="primary" style="float:right" type="filled" icon-pack="feather" icon="icon-save" @click="Confirmacion_Transaccion()"> Grabar</vs-button>
                    </div>

                    <vs-divider></vs-divider>

                </form>
            </div>
        </vs-popup>

    </div>
</vx-card>
</template>

<script>
import Multiselect from 'vue-multiselect'
import {
    VueEditor
} from "vue2-editor";
import "vue-multiselect/dist/vue-multiselect.min.css"

const MENSAJE_TITULO = ""
const MENSAJE_DESCRIPCION = "<p><strong>Buen d&iacute;a estimado proveedor, adjunto encontrar&aacute; orden de compra para su pronto despacho, favor confirmar de recibido.</strong></p><p><strong>Si no despachar&aacute; completa la orden de compra por alg&uacuten inconveniente</strong></p><p><strong>informar por este medio, al analista de compras que corresponda:</strong></p><br><p>Alimentos y Bebidas - Leidy Rodr&iacute;guez <u><EMAIL></u></p><p>Medicamentos - Carmen Mejia <u><EMAIL></u></p><p>Suministro M&eacute;dico - Jorge Pirir <u><EMAIL></u></p><p>Compras No M&eacute;dicas - Dilceo Aguirre <u><EMAIL></u></p><p>Diagn&oacute;stico y Laboratorio - Lourdes Rabanales <u><EMAIL></u></p>"
const DISCLAIMER = '';

import moment from "moment";
export default {

    components: {
        Multiselect,
        VueEditor,

    },
    data() {
        return {
            HabilitarVentanaCorreo: false,
            BanderaEnviarCorreo: true,
            Estado_VentanaEmergente_Auto: false,
            autorizar_solicitud: false,
            cb_lista_operacion: '',
            fecha_inicio: this.getDateValue(new Date()),
            fecha_final: this.getDateValue(new Date()),
            Id_estado_seleccionado: '',
            lista_estado: [{
                    ID: 'P',
                    DESCRIPCION: 'Proceso'
                },
                {
                    ID: 'A',
                    DESCRIPCION: 'Autorizado'
                },
                {
                    ID: 'R',
                    DESCRIPCION: 'Rechazado'
                },
                {
                    ID: 'C',
                    DESCRIPCION: 'Anulado'
                }
            ],
            cb_lista_recepcion: '',
            Id_estado_recepcion: '',
            lista_recepcion: [{
                    ID: 'S',
                    DESCRIPCION: 'Validada'
                },
                {
                    ID: 'N',
                    DESCRIPCION: 'No Validada'
                }
            ],

            cb_lista_estado_solicitud: '',
            Id_estado_solicitu_seleccionado: '',
            Lista_solicitud_estado: [{
                    ID: 'A',
                    DESCRIPCION: 'Autorizar'
                },
                {
                    ID: 'R',
                    DESCRIPCION: 'Rechazar'
                }
            ],
            Observaciones: '',
            Lista_autorizaciones: [],
            Lista_solicitud_enc: [],
            Lista_detalle_solicitud: [],
            Datos_autorizacion_seleccionado: [],
            Id_responsable_1: '',
            Nombre_resposable_1: '',
            Id_responsable_2: '',
            Nombre_resposable_2: '',
            user: '',
            message: 'prueba mensaje',
            messages: [],
            notificaciones: [],
            // socket: io('***************:3001'),

            reporte: {
                generarPDF: true,
                generarExcel: true,
                generar: false,
                popup: false,
                buscar: '',
                titulo: '',
                url: '',
                url_pdf: '',
                opciones: [],
                pdf: '',
                buscador: null
            },
            datosResultado: {
                EnviarA: '',
                CopiarA: '',
                CopiarEnOculto: '',
                Asunto: '',
                Mensajetitulo: MENSAJE_TITULO,
                MensajeDescripcion: MENSAJE_DESCRIPCION,
                MensajeFirma: DISCLAIMER,
                NombreDelArchivo: 'OrdenCompra.pdf',
                IdOrdenCompra: '',
                TipoOrden: ''

            },
            customToolbar: [
                [{
                    'header': [1, 2, 3, 4, 5, 6, false]
                }],
                [{
                    'font': []
                }],
                ['bold', 'italic', 'underline', 'strike'],
                [{
                    list: "ordered"
                }, {
                    list: "bullet"
                }],
                [{
                    'indent': '-1'
                }, {
                    'indent': '+1'
                }],
                [{
                    'color': []
                }, {
                    'background': []
                }],
            ],
            ListaDepartamentos: [],
            cbDepartamento: '',
            IdDepartamento: '',
            Departamento: '',
            deOrden: 0,
            aOrden: 0,

            listaProveedores: [],
            cbProveedor: '',
            proveedor: {
                codigo: '',
                nombre: '',
                nit: ''
            },
            idTipoSeleccionado: '',
            listado_reportes: [],
            listado_reportes1: [],
            listado_reportes2: [],
            diasCredito: 0,
            ortopedia: '',
            habilitarRpt: true,
            permisos_departamentos: [],
            RespuestavalidacionOrtopedia: {
                codigo: '',
                descripcion: ''
            }
        };
    },

    mounted() {
        this.listado_reportes = this.$recupera_parametros_reporte('Orden de Compra');
        this.listado_reportes1 = this.$recupera_parametros_reporte('Ordenes Validadas');
        this.listado_reportes2 = this.$recupera_parametros_reporte('Consolidado');
        //Verifica los deparamentos a los que se tienen permisos para autorizar
        for(let privilegio of this.$store.state.privilegios){
               if(privilegio.Privilegio.includes("DEPARTAMENTO")){  
                let departamento   = privilegio.Privilegio.split('_')[1]       
                this.permisos_departamentos.push(departamento)                                                                       
                }
            }
    
        this.ConsultarDepartamentos(this.permisos_departamentos.join(","));    

        this.Consultar_proveedor();
        this.Consulta_autorizaciones();
    },
    methods: {

        getDateValue(value) {
            if (value === undefined) {
                return '';
            } else if (value === null) {
                return '';
            } else if (value == '') {
                return '';
            }
            return moment(value).format('YYYY-MM-DD');
        },
        ConsultarDepartamentos(departamentos) {

            this.axios.post('/app/v1_OrdenCompra/consulta_departamento', {
                tipo_consulta: 'P',
                departamentos_Permisos: departamentos
            })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.ListaDepartamentos = "";
                    } else {
                        this.ListaDepartamentos = resp.data.json;
                    }
                })
                .catch(() => {})

        },
        onChangeDepartamento(value) {

            if (value !== null && value.length !== 0) {
                this.IdDepartamento = value.CODIGO;
                this.Departamento = value.NOMBRE;
            } else {
                this.IdDepartamento = '';
                this.Departamento = '';
            }
        },
        Departamento_seleccionada({
            CODIGO,
            NOMBRE
        }) {
            return `${CODIGO} - ${NOMBRE} `
        },
        Consultar_proveedor() {
            const url = this.$store.state.global.url
            const sesion = this.$store.state.sesion;
            this.axios.post(url + 'app/v1_OrdenCompra/consulta_proveedor', {
                    empresa: sesion.sesion_empresa
                })
                .then(resp => {

                    if (resp.data.codigo != 0) {

                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Proveedores',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.listaProveedores = "";
                    } else {
                        //Decodificación
                        this.listaProveedores = resp.data.json;

                    }
                })
                .catch(() => {})
        },
        seleccion_proveedor({
            NIT,
            NOMBRE,
        }) {
            return `${NIT} - ${NOMBRE} `
        },
        onChangeProveedor(value) {
            if (value !== null && value.length !== 0) {

                this.proveedor.codigo = value.CODIGO,
                    this.proveedor.nombre = value.NOMBRE.toUpperCase();
                this.proveedor.nit = value.NIT;

            } else {
                this.proveedor.codigo = '';
                this.proveedor.nombre = '';
                this.proveedor.nit = '';
            }
        },
        VerDetalleCorreo() {
            this.HabilitarVentanaCorreo = true;

        },
        Operacion_seleccionada({
            DESCRIPCION
        }) {
            return `${DESCRIPCION} `
        },
        onChangeEstado(value) {

            if (value !== null && value.length !== 0) {
                this.Id_estado_seleccionado = value.ID;
            } else {
                this.Id_estado_seleccionado = '';

            }
        },
        estado_solicitud_seleccionada({
            DESCRIPCION
        }) {
            return `${DESCRIPCION} `
        },
        onChangeEstadoSolicitud(value) {
            if (value.ID == 'A') {
                this.BanderaEnviarCorreo = true;
            } else {
                this.BanderaEnviarCorreo = false;
            }
            if (value !== null && value.length !== 0) {
                this.Id_estado_solicitu_seleccionado = value.ID;
            } else {
                this.Id_estado_solicitu_seleccionado = '';
            }
        },
        Consulta_autorizaciones() {

            const url = this.$store.state.global.url;
            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/consultar_auto_orden_compra', {
                    estado: this.Id_estado_seleccionado,
                    fecha_inicio: this.fecha_inicio,
                    fecha_fin: this.fecha_final,
                    Departamento: this.IdDepartamento,
                    Proveedor: this.proveedor.codigo,
                    Recepcionada: this.Id_estado_recepcion,
                    DeOrden: this.deOrden,
                    AOrden: this.aOrden,
                    TipoSeleccionado: this.idTipoSeleccionado

                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Autorización Orden Compra',
                            text: resp.data.mensaje,
                        })
                        this.habilitarRpt = true
                        this.Lista_autorizaciones = [];
                    } else {
                        this.Lista_autorizaciones = [];
                        this.Lista_autorizaciones = resp.data.json.map(m => {
                            return {
                                ...m,
                                TOTAL: parseFloat(m.TOTAL).toFixed(2)
                            }
                        })
                        this.habilitarRpt = false
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },
        OrdenCompraRpt(datos) {

            this.$reporte_modal({
                    Nombre: "Orden de Compra",
                    Opciones: {
                        IdOrdenCompra: datos.IDORDENCOMPRAENC
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },
        ReporteOrdenesValidadasCF() {

            this.$reporte_modal({
                    Nombre: "Ordenes Validadas",
                    Opciones: {
                        finicial: this.fecha_inicio,
                        ffinal: this.fecha_final,
                        departamento: this.IdDepartamento,
                        ordenD: this.deOrden,
                        ordenA: this.aOrden,
                    },
                    Formato: "EXCEL"
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },

        ReporteConsolidado() {

            this.$reporte_modal({
                    Nombre: "Consolidado",
                    Opciones: {
                        estado: this.Id_estado_seleccionado,
                        finicial: this.fecha_inicio,
                        ffinal: this.fecha_final,
                        departamento: this.IdDepartamento,
                        ordenD: this.deOrden,
                        ordenA: this.aOrden,
                        proveedor: this.proveedor.codigo,
                        recepcionada: this.Id_estado_recepcion,
                        tipo: this.idTipoSeleccionado,
                        opcion: '2'
                    },
                    Formato: "EXCEL"
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },

        EstadoRecepcion({
            DESCRIPCION
        }) {
            return `${DESCRIPCION} `
        },
        onChangeRecepcion(value) {

            if (value !== null && value.length !== 0) {
                this.Id_estado_recepcion = value.ID;
            } else {
                this.Id_estado_recepcion = '';

            }
        },
        Finalizar_Autorizacion(datos) {
            if((datos.NombreSeguro == null ? false : datos.NombreSeguro.includes('Palig')) && datos.CODIGO.trim() == 'ORTO'){
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Alerta',
                    time:8000,
                    text: `De acuerdo con los convenios establecidos entre el proveedor ${ datos.DESCRIPCION_PROVEEDOR.trim() } y el seguro ${ datos.NombreSeguro.trim() }, no es posible continuar con el proceso. `,
                })
                return
            }
            this.BanderaEnviarCorreo = false;
            this.datosResultado.TipoOrden = datos.IDPRODUCTOCLASEFK;
            this.datosResultado.NombreDelArchivo = 'OrdenCompraNo_' + datos.IDORDENCOMPRAENC + ".pdf";
            this.datosResultado.EnviarA = datos.PROV_CORREO;
            this.datosResultado.CopiarA = datos.CORREO_RESPONSABLE;
            this.datosResultado.IdOrdenCompra = datos.IDORDENCOMPRAENC;
            this.datosResultado.Asunto = 'Orden de compra Autorizada No. ' + datos.IDORDENCOMPRAENC;
            this.Id_estado_solicitu_seleccionado = '';
            this.cb_lista_estado_solicitud = '';
            this.Observaciones = '';
            this.Datos_autorizacion_seleccionado = [];

            if (datos.ESTADO == 'P') {
                this.Datos_autorizacion_seleccionado = datos;
                this.Estado_VentanaEmergente_Auto = true;
            } else {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Autorización Orden Compra',
                    text: 'Autorizacion solicitud finalizada.',
                })

            }

        },
        async Confirmacion_Transaccion() {
            this.RespuestavalidacionOrtopedia = {
                codigo: '',
                descripcion: ''
            }
            if (this.Id_estado_solicitu_seleccionado == '' || this.Id_estado_solicitu_seleccionado == null) {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Autorización Orden Compra',
                    text: 'Seleccionar Estado a Definir',
                })
                return;
            }
            if (this.datosResultado.EnviarA == "" && this.BanderaEnviarCorreo == true && this.Id_estado_solicitu_seleccionado == 'A' && this.Datos_autorizacion_seleccionado.NUMERO_DOCUMENTO == '1') {

                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Autorización Orden Compra',
                    text: 'Ingresar correo destino',
                })

                return;
            }

            const sesion = this.$store.state.sesion
            this.Corporativo_Sesion = sesion.corporativo

            if(this.Id_estado_solicitu_seleccionado == 'R' && this.Datos_autorizacion_seleccionado.ORTOPEDIA == 'N'){
                await this.axios.post('/app/v1_OrdenCompra/ValidacionOrtopedia', {
                    id_orden_compra_enc: this.Datos_autorizacion_seleccionado.IDORDENCOMPRAENC,
                    num_documento: 3
                })
                .then(resp => {
                    if(resp.data.json[0].codigo == '1'){
                        this.RespuestavalidacionOrtopedia.codigo = resp.data.json[0].codigo
                        this.RespuestavalidacionOrtopedia.descripcion = resp.data.json[0].descripcion
                    }
                })
            }
            
            if(this.RespuestavalidacionOrtopedia.codigo == '1'){
                this.$vs.dialog({
                    type: 'alert',
                    color: 'danger',
                    title: 'Autorización Orden Compra',
                    text: this.RespuestavalidacionOrtopedia.descripcion,
                    acceptText: 'Aceptar'
                })
                this.Estado_VentanaEmergente_Auto = false

                return;
            }
            
            this.axios.post('/app/v1_OrdenCompra/finalizar_autorizacion', {
                    empresa: sesion.sesion_empresa,
                    corporativo: sesion.corporativo,
                    id_historial_autorizacion: this.Datos_autorizacion_seleccionado.IDAUTORIZACIONHISTORIAL,
                    id_solicitud_enc: this.Datos_autorizacion_seleccionado.IDSOLICITUDENCFK,
                    estado: this.Id_estado_solicitu_seleccionado,
                    id_documento: this.Datos_autorizacion_seleccionado.ID_DOCUMENTO_,
                    num_documento: 1,
                    observaciones: this.Observaciones,
                    id_cotizacion_enc: 0,
                    id_orden_compra_enc: this.Datos_autorizacion_seleccionado.IDORDENCOMPRAENC,
                    tipo: this.Datos_autorizacion_seleccionado.IDPRODUCTOCLASEFK,
                    ortopedia: this.Datos_autorizacion_seleccionado.ORTOPEDIA
                })
                .then(resp => {
                    if (this.datosResultado.TipoOrden == 'SERVICIO') {
                        this.datosResultado.MensajeDescripcion = '';
                    }
                    if (resp.data.codigo != 0) {

                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Autorización Orden Compra',
                            text: resp.data.mensaje,
                        })

                    } else {

                        if (this.datosResultado.EnviarA != "" && this.BanderaEnviarCorreo == true && this.Id_estado_solicitu_seleccionado == 'A' && this.Datos_autorizacion_seleccionado.NUMERO_DOCUMENTO == '1') {
                            this.$genera_reporte_envio({
                                Nombre: "Orden de Compra",
                                Data_source: this.datosResultado,
                                Data_report: this.listado_reportes
                            }).catch(() => {})
                            this.$vs.notify({
                                color: '#F9A825',
                                title: 'Autorización Orden Compra',
                                text: 'Correo enviado a la cola.',
                            })
                        }
                    }

                })
                this.Estado_VentanaEmergente_Auto = false;
                this.Consulta_autorizaciones();
                //.catch(() => {
                    //this.$vs.loading.close();
            //})
            //this.$vs.loading.close();

        },

        CambioVariable() {
            if (this.idTipoSeleccionado > 'S') {
                this.BloqueoVariables();

            } else {
                this.DesbloqueoVariables();

            }
        },
        BloqueoVariables() {
            this.estadoBotonesTipoDes = true;

        },
        DesbloqueoVariables() {
            this.estadoBotonesTipoDes = false;
        },
        async beforeCreate() {
            this.listado_reportes = await this.$recupera_parametros_reporte('Orden de Compra')
            this.listado_reportes1 = await this.$recupera_parametros_reporte('Ordenes Validadas')
            this.listado_reportes2 = await this.$recupera_parametros_reporte('Consolidado')

        }
        
    },
    async beforeCreate() {
        this.listado_reportes = await this.$recupera_parametros_reporte('Orden de Compra')
        this.listado_reportes1 = await this.$recupera_parametros_reporte('Ordenes Validadas')
        this.listado_reportes2 = await this.$recupera_parametros_reporte('Consolidado')
    }

}
</script>

<style scoped>
.footer {
    height: 50px;
    margin-top: -65px;
    margin-left: 10px;
}

.center {
    border: 5px solid;
    margin: auto;
    width: 50%;
    padding: 10px;
}

.label-size {
    font-size: 18px;
    font-weight: bold;
}

.label-sizem {
    font-size: 16px;
    font-weight: bold;
}
</style>
