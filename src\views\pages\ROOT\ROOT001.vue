<template>
<vx-card title="Administración de IP Local">
    <div class="content content-pagex">
        <form>
            <div style="margin: 15px" class="flex flex-wrap">
                <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                    <ValidationProvider name="IP Local" rules="required|max:15" v-slot="{ errors }" class="required">
                        <vs-input label="IP Local" class="w-full" count="15" v-model="IpLocal" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                    </ValidationProvider>
                </div>
            </div>
            <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5" style="margin: 15px;border-radius:5px;padding:5px;font-size:15px;background-color:#A5D6A7">
                Corroborar la IP con el comando IPCONFIG.
            </div>
            <div style="margin: 15px" class="flex flex-wrap">

                <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                    <ValidationProvider name="Comando Windows" rules="required|max:15" v-slot="{ errors }" class="required">
                        <vs-input label="Comando Windows" class="w-full" count="15" v-model="ComandoWindows" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                    </ValidationProvider>
                </div>
            </div>

            <div style="margin: 15px" class="flex flex-wrap">
                <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6">
                    <vs-button color="primary" @click="GuardarIP()">Guardar / Enviar</vs-button>
                </div>
            </div>

        </form>
    </div>


</vx-card>
</template>

<script>
import FondoPantalla from '/src/components/sermesa/modules/archivos/SMHorario.vue'
import io from 'socket.io-client';

export default {
    components: {
        FondoPantalla
    },
    data() {
        return {

            IpLocal: '',

            socket: io('https://sighos-linea.hospitaleslapaz.com/'),
            ComandoWindows: '',
         
        };
    },
    mounted() {
        this.IpLocal = localStorage.getItem('IpLocal');
     
    },
   
    methods: {
        GuardarIP() {
            localStorage.setItem('IpLocal', this.IpLocal);
            this.socket.emit('message', "WEB;" + this.IpLocal + ";" + this.ComandoWindows);
        },
        ObtenerIP() {
          
        },
      
    },
    

};
</script>
