<template>
<!-- *************************************************************************************************************************************************************************** -->
<!-- *************************************************************************************************************************************************************************** -->
<vs-popup ref="buscador" :title="buscador_titulo" :active.sync="mostrar_popup" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
    <div class="container-loading">
        <div id="loading" class="vs-con-loading__container"></div>
    </div>
    <form v-on:submit.prevent="filtrar((!all_datos))">
        <vx-input-group class="">
            <table style="width:100%" aria-describedby="Buscador General">
                <tr>
                    <td v-for="(c,index) in campo_filtro_visible" :key="index">
                        <!-- se muestran solo los filtros que no tengan el signo * -->
                        <div>
                            <vs-input v-if="titulos_mascara !=null && titulos_mascara[c.etiqueta.replace('#','').replace('*','')]" ref="campos" v-model="c.filtro" @focus="$event.target.select()" v-mask="titulos_mascara[c.etiqueta.replace('#','').replace('*','')]" :label="c.etiqueta" @keyup.esc="mostrar_popup=false" :disabled="validar_campo_grupo(c.etiqueta)" />
                            <vs-input v-else ref="campos" v-model="c.filtro" @focus="$event.target.select()" :label="c.etiqueta" @keyup.esc="mostrar_popup=false" :disabled="validar_campo_grupo(c.etiqueta)" />
                        </div>
                    </td>
                </tr>
            </table>
            <template slot="append" v-if="campo_filtro_visible.length>0">
                <div class="append-text btn-addon">
                    <button type="submit" v-show="false" name="button"></button>
                    <vs-button color="primary" icon-pack="feather" icon="icon-search" @keyup.esc="mostrar_popup=false" @click.native="filtrar((!all_datos))" style="margin-top: 21px;"></vs-button>
                </div>
            </template>
        </vx-input-group>

        <div v-if="campos_filtro_grupos.activo" style="background-color:#ecf0f1;padding:7px" class="mb-base">
            Se aplica el filtro por los campos <div v-for="(item,key) in campos_filtro_grupos.grupo" :key="'grupos_'+key" style="display:inline">
                <span v-if="key>0 && item.indexOf('*')>=0"> y </span>
                <span v-else-if="key>0 && item.indexOf('*')==-1"> y/o </span>
                <b>{{item.replace('*','')}}</b>
            </div>
            los otros campos quedaran deshabilitandos. <vs-button color="rgb(62, 201, 214)" type="filled" size="small" @click="campo_filtro.map(data=>data.filtro=null)">Limpiar Busqueda</vs-button>
        </div>
    </form>

    <div class="content-table">
        <table class="buscador_table" v-if="show_list">
            <thead>
                <tr>
                    <th v-if="multiselect"></th>
                    <th v-for="(c,index2) in campo_visible_encabezado(campo_filtro)" :key="index2">{{c.etiqueta.replace('#','')}}</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="(l,index) in list_paginacion" :key="index" v-on:click="(!l.Disabled)?select_item(l,index):mensajeDisable()" v-on:dblclick="(!l.Disabled)?((!multiselect)?seleccionar():null):null" :class="[(l.Selected)?'active':'',(l.Disabled)?'disabled':'']">
                    <td v-if="multiselect"><i class="fas fa-check-square" v-if="l.Selected"></i><i class="far fa-square" v-else></i></td>
                    <td v-for="(c,index2) in campo_visible_detalle(campos)" :key="index2" v-html="l[(Array.isArray(c)?c[0]:c)] "></td>
                </tr>
            </tbody>
        </table>

        <span v-if="multiselect">
            Elementos seleccionados: {{list_seleccion.length}}
        </span>

        <div v-if="list.length==0" style="text-align:center;color:#B0BEC5">
            Sin Datos
        </div>
    </div>
    <vs-divider></vs-divider>

    <div class="paginacion">
        <vs-pagination :total="Math.ceil(list.length/10)" v-model="pagina"></vs-pagination>
    </div>
    <div style="clear:both"></div>
    <vs-divider></vs-divider>
    <vs-button style="float:right" color="primary" type="filled" :disabled="list_seleccion.length==0" @click="seleccionar()"> Aceptar</vs-button>
</vs-popup>
</template>

<script>
export default {
    name: 'buscador',
    data() {
        return {
            url: '',
            list: [],
            //utlizado para guardar los datos de busqueda de forma temporal cuando esta activo all_datos
            temp_list: [],
            show_list: true,
            list_seleccion: [],
            buscar: '',
            buscar_filtro: '',
            //mostrar y cerrar
            mostrar_popup: false,
            mostrar_lock: false, //bloquea la llamada del watch
            //callback utilizado para devolver informacion de la busqueda
            callback: null,
            //valores temporales
            temp_campo: null,
            temp_etiqueta: null,
            backgroundLoading: 'primary',
            colorLoading: '#fff',
            //cargando los campos ingresados para poder enviar los datos de los filtros
            campo_filtro: [],
            //cargando listado de filtros agrupados
            campos_filtro_grupos: {
                activo: false,
                index: 0,
                grupo: []
            },
            // página actual
            pagina: 1,
            // Seleccion de item 
            seleccionFila: null
        }
    },
    props: {
        buscador_titulo: {
            default: 'Buscador'
        },
        //api o data
        api: {
            default: ''
        },

        //filtro estatico para realizar las consultas
        api_filtro: {
            default: null
        },

        //indica que se mostraran todos los datos sin paginación
        //es posible realizar busquedas en blanco
        all_datos: {
            default: false
        },

        /**
         * listado de campos a presentar
         * Si se agrega el # al inicio del campo se omitira el campo para su visualización
         */
        campos: {
            default: []
        },
        //index del campo principal
        campo_llave: {
            default: null
        },

        /**
         * Titulos de los campos
         * Si se agrega el # al inicio del campo se omitira el campo para su visualización
         */
        titulos: {
            default: []
        },

        //Permite agregar una mascara al input de busqueda
        // :titulos_mascara="{'Orden':'AAX-#########','Admisión':'X##########'}"
        titulos_mascara: {
            default: null
        },

        //Permite agrupar los campos de busqueda deshabilitando los otros campos cuando se ingresa valor
        //"[['Apellidos','Nombres'],['Orden','Admisión']]"
        titulos_grupos: {
            default: null
        },

        //configuraciones
        multiselect: {
            default: false
        },

        //limpiar los multiselect cada vez que se llame la funcion
        api_clear: {
            default: true
        },

        //recarga el query cada vez que se presiona buscar
        //no carga la información hasta que se entra al buscador
        api_reload: {
            default: false
        },

        //obliga a realizar un query al principio de la carga de datos
        api_preload: {
            default: true
        },

        /**
         * Indica la validación para volver inactivo un campo
         * Si la validación es correcta se deshabilita
         */
        api_disable_seleccion: {
            default: null
        },

    },
    computed: {
        list_paginacion() {
            return this.list.slice((this.pagina - 1) * 10, this.pagina * 10)
        },
        campo_filtro_visible() {
            return this.campo_filtro.filter(c => c.etiqueta.indexOf('#') == -1)
        }
    },
    watch: {
        mostrar_popup(value) {
            if (value == false) {
                this.cerrar_buscador()
                document.removeEventListener("keydown", this.movimiento)
                this.$emit('close')
            } else {
                // Enfoque del primer campo
                setTimeout(() => {
                    if (this.campo_filtro_visible.length > 0) this.$refs['campos'][0].$el.children[1].children[0].focus()
                }, 500)
                document.addEventListener("keydown", this.movimiento)
                this.$emit('open')
            }
        },
        campo_filtro: {
            deep: true,
            handler() {
                if (this.titulos_grupos && this.titulos_grupos.length > 0) {
                    let a = this.campo_filtro.filter(data => data.filtro != null && data.filtro != "")
                    if (a.length > 0) {
                        //buscando filtro
                        let campo = a[0].etiqueta
                        this.titulos_grupos.map((d, index) => {
                            if (d.indexOf(campo) >= 0 || d.indexOf(campo + '*') >= 0) {
                                this.campos_filtro_grupos.activo = true
                                this.campos_filtro_grupos.index = index
                                this.campos_filtro_grupos.grupo = d
                            }
                        })

                    } else {
                        // console.log('limpiando')
                        this.campos_filtro_grupos.activo = false
                        this.campos_filtro_grupos.index = 0
                        this.campos_filtro_grupos.grupo = []

                    }
                }
            }
        }
    },
    methods: {
        Consulta: function() {
            return {
                cargar_list: (nuevo_filtro, recargar = true) => {
                    let filtro = {
                        pagina: this.pagina,
                    }
                    filtro = {
                        ...filtro,
                        ...this.api_filtro,
                        ...nuevo_filtro,
                    }
                    let mapear = (resp) => {
                        this.list = resp.map(data => {
                            let dis = false
                            try {
                                dis = eval('data.' + this.api_disable_seleccion)
                            } catch (e) {
                                alert('Error al validar en el buscador el api_disable_seleccion')
                            }

                            if (data.Selected && data.Selected == 1) this.list_seleccion.push({
                                ...data,
                                Selected: true,
                                Disabled: dis
                            })
                            return {
                                ...data,
                                Selected: (data.Selected && data.Selected == 1) ? data.Selected : false,
                                Disabled: dis
                            }
                        })
                        // Seleccion de datos previos
                        if (this.multiselect) {
                            let campo_index = (this.campo_llave != null) ? this.campo_llave : this.campos[0]
                            this.list.map(data => {
                                let a = this.list_seleccion.filter(dat => data[campo_index] == dat[campo_index])
                                if (a.length > 0) data.Selected = true
                            })
                        }
                    }
                    return new Promise(resolve => {
                        this.show_list = false

                        if (recargar) {
                            this.list = []
                            // limpiando selección
                            // console.log('limpiando')
                            // this.list_seleccion = []

                            //si el api es un link
                            if (!Array.isArray(this.api)) {
                                this.axios.post(this.url + this.api, filtro)
                                    .then(resp => {
                                        mapear(resp.data.json)
                                        //guardando datos de forma temporal
                                        this.temp_list = [...this.list]

                                        this.show_list = true
                                        // if (this.api_preload) this.$emit('update:seleccion', this.list_seleccion)
                                        resolve(this.list)
                                    })
                                    .catch(() => {
                                        resolve(null)
                                    })
                            } else {
                                /**
                                 * Aplicando filtro
                                 */
                                let api_filtro = this.api.filter(l => {
                                    let estado = true
                                    Object.entries(filtro).forEach(([key, value]) => {
                                        if (l[key] && value) estado = (estado) ? l[key].toUpperCase().indexOf(value.toUpperCase()) >= 0 : false
                                    });
                                    return estado
                                })

                                /**
                                 * Mapeo de datos
                                 */
                                mapear(api_filtro)

                                this.temp_list = [...this.list]
                                this.show_list = true
                                resolve(this.list)

                            }
                        } else {
                            // console.log(this.temp_list)
                            let campo_index = (this.campo_llave != null) ? this.campo_llave : this.campos[0]
                            this.list = this.temp_list.filter(data_ => {
                                let status = true
                                if (nuevo_filtro == null) return true
                                Object.entries(nuevo_filtro).map(data => {
                                    if (data_[data[0]].toUpperCase().indexOf(data[1].toUpperCase()) < 0) status = false
                                })
                                return status
                            }).map(data => {
                                return {
                                    ...data,
                                    Selected: (this.list_seleccion.filter(dat => data[campo_index] == dat[campo_index]).length > 0) ? true : false
                                }
                            })

                            this.show_list = true

                            resolve(this.list)
                        }
                    })
                },
            }
        },

        // **********************************************************************************************************
        // **********************************************************************************************************
        select_item(item, index) {
            if (this.multiselect) {
                item.Selected = !item.Selected
                if (item.Selected) {
                    this.list_seleccion.push(item)
                } else {
                    let campo_index = (this.campo_llave == null) ? this.campos[0] : this.campo_llave
                    // console.log(campo_index)
                    this.list_seleccion = this.list_seleccion.filter(dat => item[campo_index] != dat[campo_index])
                }
            } else {
                this.list.map(data => {
                    data.Selected = false
                })
                item.Selected = true
                this.list_seleccion = item
                const indexRegistro = (this.pagina - 1) * 10 + index
                this.seleccionFila = indexRegistro
            }
        },
        mensajeDisable() {
            this.$vs.notify({
                time: 8000,
                title: 'Busqueda',
                text: 'El registro seleccionado esta inactivo',
                iconPack: 'feather',
                icon: 'icon-alert-circle',
                color: 'danger',
                position: 'bottom-center'
            })
        },

        filtrar(recargar_pagina) {
            // Validando si ya existe una linea seleccionada
            if (this.seleccionFila !== null) {
                this.seleccionar()
                return false
            }
            // Iniciando filtrado
            let filtro = {}
            this.show_list = false
            // convirtiendo campos vacios en null
            this.campo_filtro.map(data => {
                if (data.filtro != null && data.filtro.trim() == "") {
                    data.filtro = null
                } else {
                    if (data.filtro != null) filtro[data.campo] = data.filtro
                }
            })
            this.seleccionFila = null
            this.pagina = 1
            //validaciones
            //validando si se han llenado campos
            if (this.campo_filtro.filter(data => data.filtro != null).length == 0 && !this.all_datos) {
                this.$vs.notify({
                    time: 8000,
                    title: 'Busqueda',
                    text: 'Debe de ingresar uno o más filtros de búsqueda',
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'danger',
                    position: 'bottom-center'
                })
                return false
            }
            //validando si existen campos obligatorios
            let filtro_obligatorio = []
            this.campo_filtro.map((data) => {
                let etiqueta = data.etiqueta.replace('*', '').replace('#', '')
                if (data.etiqueta.indexOf('*') >= 0 && data.filtro == null) filtro_obligatorio.push(etiqueta)
                if (this.campos_filtro_grupos.activo) {
                    let bloq = this.validar_campo_grupo(etiqueta)
                    let index = (this.campos_filtro_grupos.grupo.indexOf(etiqueta) >= 0) ? this.campos_filtro_grupos.grupo.indexOf(etiqueta) : this.campos_filtro_grupos.grupo.indexOf(etiqueta + '*')
                    //buscando si los grupos tienen requerido
                    if (!bloq && index >= 0 && this.campos_filtro_grupos.grupo[index].indexOf('*') >= 0 && data.filtro == null) filtro_obligatorio.push(etiqueta)
                }
            })
            //validando campos obligatorios
            if (filtro_obligatorio.length > 0) {
                this.$vs.notify({
                    time: 8000,
                    title: 'Busqueda',
                    text: (filtro_obligatorio.length == 1) ? 'El campo "' + filtro_obligatorio.join(',') + '" es obligatorio' : 'Los campos ' + filtro_obligatorio.join(',') + ' son obligatorios',
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'danger',
                    position: 'bottom-center'
                })
                return false
            }
            this.Consulta().cargar_list(filtro, recargar_pagina)
        },
        // **********************************************************************************************************
        // **********************************************************************************************************
        cerrar_buscador() {
            this.pagina = 1
            this.seleccionFila = null
            if (this.mostrar_lock == false) this.callback(null)
            this.mostrar_lock = false
            setTimeout(() => {
                this.$emit('update:mostrar', false)
            }, 10);
        },
        // **********************************************************************************************************
        // *****************************************  Datos externos ************************************************
        // **********************************************************************************************************
        // callback : devolucion de datos
        // seleccion : listado de elementos seleccionados
        iniciar(info) {
            this.list = []
            this.mostrar_popup = true
            if (this.api_clear) this.list_seleccion = []

            // callback
            this.callback = (info.callback) ? info.callback : info

            // seleccion
            if (info.seleccion) {
                this.list_seleccion = (this.multiselect) ? [...info.seleccion] : {
                    ...info.seleccion
                }
                if (this.multiselect) {
                    let campo_index = (this.campo_llave != null) ? this.campo_llave : this.campos[0]
                    this.list.map(data => {
                        let a = this.list_seleccion.filter(dat => data[campo_index] == dat[campo_index])
                        if (a.length > 0) data.Selected = true
                    })
                }
            }
            this.campo_filtro.map(data => {
                data.filtro = null
            })

            if (this.api_preload) this.Consulta().cargar_list(null)
        },
        // **********************************************************************************************************
        // **********************************************************************************************************
        async validar(data, callback, error, sync = false) {
            this.campo_filtro.map(data2 => {
                data2.filtro = null
            })
            if (!sync) {
                this.Consulta().cargar_list(data)
                    .then(resp => callback(resp))
                    .catch(err => error(err))
            } else {
                await this.Consulta().cargar_list(data)
                    .then(resp => callback(resp))
                    .catch(err => error(err))
            }
        },
        validar_campo_grupo(etiqueta) {
            return !(!this.campos_filtro_grupos.activo || (this.campos_filtro_grupos.activo && (this.campos_filtro_grupos.grupo.indexOf(etiqueta) >= 0 || this.campos_filtro_grupos.grupo.indexOf(etiqueta + '*') >= 0)))
        },
        obtenerSeleccion(data, callback) {
            this.list_seleccion = []
            this.Consulta().cargar_list(data)
                .then(() => {
                    callback(this.list_seleccion)
                })
        },

        campo_visible_encabezado(campos) {
            return campos.filter(f => f.campo.indexOf("#") < 0)
        },
        campo_visible_detalle(campos) {
            if (campos) {
                return campos.filter(f => f.indexOf("#") < 0)
            } else {
                campos
            }
        },
        // **********************************************************************************************************
        // **********************************************************************************************************
        seleccionar() {
            this.pagina = 1
            this.mostrar_lock = true
            this.mostrar_popup = false
            this.callback(this.list_seleccion)
        },
        pagina_anterior() {
            if (this.pagina <= 1) return false
            this.pagina--
        },
        pagina_siguiente() {
            if (((this.pagina + 1) * 10) > this.list.length) return false
            this.pagina++
        },
        movimiento(ev) {
            if (ev.keyCode === 38) {
                this.Otros().movimientoArriba()
            } else if (ev.keyCode === 40) {
                this.Otros().movimientoAbajo()
            } else {
                if (ev.keyCode !== 13 && this.seleccionFila != null) {
                    this.seleccionFila = null
                    this.Otros().limpiarSeleccion()
                }
            }
        },
        Otros: function() {
            return {
                movimientoArriba: () => {
                    if (this.seleccionFila > 0 | this.pagina > 1) {
                        if (this.seleccionFila === 0) {
                            this.pagina--
                            this.seleccionFila = 10
                        }
                        this.Otros().limpiarSeleccion()
                        this.seleccionFila--
                        this.list_paginacion[this.seleccionFila].Selected = true
                        this.list_seleccion = this.list_paginacion[this.seleccionFila]
                    }
                },
                movimientoAbajo: () => {
                    if ((this.seleccionFila === null && this.list_paginacion.length === 1) || this.seleccionFila < this.list_paginacion.length - 1 || this.pagina < Math.ceil(this.list.length / 10)) {
                        this.Otros().limpiarSeleccion()
                        if (this.seleccionFila === null) this.seleccionFila = -1
                        this.seleccionFila++

                        if (this.seleccionFila > 9) {
                            this.seleccionFila = 0
                            this.pagina++
                        }

                        this.list_paginacion[this.seleccionFila].Selected = true
                        this.list_seleccion = this.list_paginacion[this.seleccionFila]

                    }
                },
                limpiarSeleccion: () => {
                    this.list_paginacion.map(m => {
                        m.Selected = false
                    })
                }
            }
        }
    },
    mounted() {

    },
    created() {
        if (this.titulos && this.campos)
            if (this.titulos.length != this.campos.length) {
                alert('Los campos y las etiquetas no coinciden')
                return false
            }
        this.url = this.$store.state.global.url

        //mapear los campos para poder utilizarlos cuando se filtre la información
        // console.log()
        // if (this.campo_filtro && this.campo_filtro.length > 0){
        this.campo_filtro = (this.campos) ? this.campos.map((data, index) => {
            return {
                campo: Array.isArray(data) ? data[1] : data,
                etiqueta: this.titulos[index],
                filtro: null
            }
        }) : []
        // console.log(this.campo_filtro)o
        // }

        // this.campos.map((data, index) => {
        // console.log(data,this.titulos[index])
        //     let a = {
        //         campo: data,
        //         etiqueta: this.titulos[index],
        //         filtro: null
        //     }
        //     console.log(a)
        // })

    }
}
</script>

<style scoped>
.buscador_table {
    width: 100%;
    border: none;
    border-spacing: 0;
}

.buscador_table tr {
    cursor: pointer;
    font-size: 14px;
}

.buscador_table tr.disabled td {
    color: #e74c3c;
}

.buscador_table td {
    /* font-family: Lato-Regular; */
    font-size: 15px;
    color: #808080;
    border: solid 1px transparent;
    border-style: solid none;
    padding: 6px;
}

.buscador_table tr:nth-child(even) {
    background-color: #f8f6ff;
}

.buscador_table tr i {
    font-size: 20px
}

.buscador_table tr.active {
    background-color: #03A9F4;
    color: white;
}

.buscador_table tr.active td {
    color: white;
}

.buscador_table tr:hover:not(.active) {
    background-color: #ECEFF1;
    /* color:white; */
}

.content-table {
    /* max-height: 500px; */
    max-height: calc(100vh - 350px);
    /* background-color: red; */
    overflow: auto;
}

.paginacion {
    float: right;
    clear: both
}

.paginacion div {
    display: inline-block;
    /* height: 50px; */
    /* background-color: red; */
    position: relative;
    top: -7px;
    padding: 0 10px
}

.paginacion i {
    font-size: 30px;
    color: #3498db;
    cursor: pointer;
}

.paginacion i.inactivo {
    color: #bdc3c7;
    cursor: default;
}

.filtros {
    display: inline;
    background-color: #95a5a6;
    padding: 5px 1px;
    border-radius: 5px;
    margin-left: 5px;
    font-size: 10px;
}

.filtro_activo {
    background-color: rgba(var(--vs-primary), 1);
}
</style>
