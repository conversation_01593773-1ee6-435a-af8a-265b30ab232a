<template>
    <div>
        <div class="vx-card__title mb-8" style="text-align:center">
            <span style="color:#373C74; font-size: x-large;">
                <b>Nueva Contraseña</b>
            </span>
        </div>
        <div>
            <form v-on:submit.prevent="ActualizarContrasenia()">
                <vs-input icon-no-border icon="icon icon-user" icon-pack="feather" label-placeholder="Corporativo" v-model="usuario" class="w-full pt-4" :disabled="usuario" />
    
                <vx-input-group v-if="mostrarEnvioToken" class="w-auto">
                    <vs-input icon-no-border icon-pack="feather" icon="icon-shield" :label-placeholder="labelToken" v-model="token" class="w-full pt-4" />
                    <template slot="append">
                        <div class="append-text btn-addon mt-8">
                            <vs-tooltip color="my-z-index" text="Enviar token">
                                <vs-button clientWidth="42" color="success" icon-pack="fas" @click="EnviarToken" icon="fa-share" />
                            </vs-tooltip>
                        </div>
                    </template>
                </vx-input-group>
    
                <vs-input v-else icon-no-border icon-pack="feather" icon="icon-shield" :label-placeholder="labelToken" v-model="token" class="w-full pt-4" />
    
                <vs-input type="password" name="password" icon-no-border icon="icon icon-lock" icon-pack="feather" label-placeholder="Nueva Contraseña" v-model="contrasenia" class="w-full pt-4" />
                <vs-input type="password" name="password" icon-no-border icon="icon icon-lock" icon-pack="feather" label-placeholder="Confirmar Nueva Contraseña" v-model="contrasenia_conf" class="w-full pt-4" :danger="!patterns.confirmacion" :danger-text="!patterns.confirmacion?'Las contraseñas no coinciden':''" />
    
                <div class="mt-4">
                    <b> <small>Requisitos: </small></b>
                    <br>
                    <small>Mínimo 10 caracteres</small>
                    <font-awesome-icon v-show="patterns.longitud" :icon="['fas', 'circle-check']" color="green" />
                    <br>
                    <small>Por lo menos un número</small>
                    <font-awesome-icon v-show="patterns.numero" :icon="['fas', 'circle-check']" color="green" />
                    <br>
                    <small>Por lo menos un símbolo (!@#$%.^&*)</small>
                    <font-awesome-icon v-show="patterns.simbolo" :icon="['fas', 'circle-check']" color="green" />
                    <br>
                    <small>Letras mayúsculas y minúsculas</small>
                    <font-awesome-icon v-show="patterns.letras" :icon="['fas', 'circle-check']" color="green" />
                    <br>
                </div>
    
                <div class="mt-4 flex flex-row gap-2">
                    <vs-button class="w-full" color="danger" type="border" icon-pack="feather" icon="icon-x" @click="Cancelar"> Cancelar</vs-button>
                    <vs-button class="w-full" color="primary" type="filled" icon-pack="feather" icon="icon-save" :disabled="!patterns.general || disabledAct" @click="ActualizarContrasenia()"> Actualizar</vs-button>
                </div>
                <div v-if="skipCallback" class="mt-4 flex flex-row gap-2">
                    <vs-button class="w-full" color="dark" type="border" icon-pack="fas" icon="fa-arrow-right" @click="Skip"> Omitir este paso </vs-button>
                </div>
    
                <div v-show="Boolean(error)" class="mt-2 danger">
                    <small style="color: rgb(234, 84, 85); margin-top: 15px;">{{ error }}</small>
                </div>
    
            </form>
        </div>
    </div>
    </template>
    
    <script>
    const reglaPass = /^(?=.*\d)(?=.*[!@#$%.^&*])(?=.*[a-zñ])(?=.*[A-ZÑ]).{10,}$/
    const reglaNumero = /^(?=.*\d).+$/
    const reglaSimbolo = /^(?=.*[!@#$%.^&*]).+$/
    const reglaLetras = /^(?=.*[a-zñ])(?=.*[A-ZÑ]).+$/
    
    export default {
        name: 'ChangePassword',
        props: {
            corporativo: null,
            config: null,
            skipCallback: null,
            showTokenButton: {
                type: Boolean,
                default: false
            },
            mode: {
                type: String,
                default: 'token',
                validator: (val) => {
                    return ['token', 'firstPassword', 'oldPassword', 'tokenOldPassword', 'tokenFirstPassword'].includes(val)
                },
            },
        },
        data() {
            return {
                usuario: null,
                password: null,
                token: null,
                contrasenia: '',
                contrasenia_conf: '',
                mostrarEnvioToken: false,
                textoTooltip: 'Enviar token',
                error: '',
                disabledAct: false
            }
        },
    
        computed: {
            patterns() {
                return {
                    longitud: this.contrasenia.length >= 10,
                    numero: reglaNumero.test(this.contrasenia),
                    confirmacion: this.contrasenia == this.contrasenia_conf || this.contrasenia.length == 0 && this.contrasenia_conf.length == 0,
                    simbolo: reglaSimbolo.test(this.contrasenia),
                    letras: reglaLetras.test(this.contrasenia),
                    general: reglaPass.test(this.contrasenia) && this.contrasenia == this.contrasenia_conf && Boolean(this.token),
                    token: Boolean(this.token),
                }
            },
            labelToken() {
                return this.mode == 'tokenFirstPassword'? 'Primer Contraseña o Token (Revisar correo electronico)' :
                    this.mode == 'oldPassword' ? 'Contraseña Actual' :
                    this.mode == 'tokenOldPassword' ? 'Contraseña Actual o Token (Revisar correo electronico)' :
                    'Token (Revisar correo electronico)'
            },
        },
    
        methods: {
            Skip() {
                if (typeof this.skipCallback == 'function')
                    this.skipCallback()
            },
            Cancelar() {
                this.Limpiar()
                this.$emit('cancel')
            },
            EnviarToken() {
                this.error = ''
                return new Promise((resolve, reject) => {
                    this.axios.post('/app/usuario/reiniciar_clave', {
                        Corporativo: this.usuario
                    }).then((resp) => {
                        this.mostrarEnvioToken = false
                        setTimeout(() => {
                            this.mostrarEnvioToken = true
                        }, resp.data.exipraen * 60000);
    
                        this.$emit('token-send-success', resp.data)
                        resolve(resp.data)
                    }).catch((err) => {
                        this.error = err.descripcion??err
                        this.$emit('token-send-failed', err)
                        reject(err)
                    })
                })
            },
            ActualizarContrasenia() {
                if (!this.patterns.general || !this.patterns.confirmacion) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Alerta',
                        text: "La contraseña no cumple con los requisitos.",
                    })
                    return
                }
                this.error = ''
                this.disabledAct = true
                this.axios.post('/app/usuario/cambio_clave', {
                        Corporativo: this.usuario,
                        Password: this.token,
                        NewPassword: this.contrasenia,
                    }, this.config)
                    .then((resp) => {
                        this.$emit('password-changed', {
                            Corporativo: this.usuario,
                            NewPassword: this.contrasenia,
                        })
                        this.Limpiar()
    
                        let sesion = this.$store.state.sesion
                        sesion.diasexpira = resp.data.diasexpira
                        sesion.cambiarclave = false
                        sesion.alertaclave = ''
                        this.disabledAct = false
                        this.$store.dispatch('sesionGuardar', sesion)
                    })
                    .catch((err) => {
                        this.mostrarEnvioToken = err.codigo == -2
                        this.error = err.descripcion??err
                        this.disabledAct = false
                        this.$emit('password-change-failed', {
                            Corporativo: this.usuario
                        })
                    })
    
            },
            Limpiar() {
                this.password = null
                this.token = null
                this.contrasenia = ''
                this.contrasenia_conf = ''
                this.error = ''
            },
        },
        watch: {
            'corporativo'(val) {
                this.usuario = val
            },
            'showTokenButton'(val) {
                this.mostrarEnvioToken = val
            },
            'mostrarEnvioToken'(val) {
                this.$emit('show-token-button', val)
            }
    
        },
        mounted() {
            this.usuario = this.corporativo
            this.mostrarEnvioToken = this.showTokenButton
        },
    }
    </script>
    
    <style>
    .vs-tooltip-my-z-index {
        z-index: 9999999;
    }
    </style><style lang="css" scoped>
    .my-danger {
        color: rgb(234, 84, 85) !important;
    }
    </style>
    