<template>
<div class="flex-container">
    <DxDrawer template="listMenu" :close-on-outside-click="false" :opened="true">
        <template #listMenu>
            <div id="Drawer">
                <DxList :data-source="menu" @item-click="clickOnItem" :grouped="TipoDoc == 1 ? false : true">
                    <template #item={data:item}>
                        <div :class="item.id === SelectedOption ? ['itemSelected', 'dx-template-wrapper', 'dx-item-content', 'dx-list-item-content'] : ['itemNoSelected', 'dx-template-wrapper', 'dx-item-content', 'dx-list-item-content']">
                            <div style="display: flex !important; place-items: center !important">
                                <font-awesome-icon :icon="['fas', item.icon]" style="font-size: 20px !important;" />
                                <span class="ml-2" style="font-size: 14px !important;">{{item.title}}</span>
                            </div>
                        </div>
                    </template>
                </DxList>
            </div>
        </template>
        <div id="content" class="ml-2 borderDiv" width="100%">
            <div v-if="SelectedOption === 1" class="p-2">
                <Anticipos ref="Anticipos" :TipoDoc="TipoDoc" :Corporativo="Corporativo" />
            </div>
            <div v-if="SelectedOption === 2" class="p-2">
                <PagosGenerados ref="PagosGenerados" :TipoDoc="TipoDoc" :Corporativo="Corporativo" :key="NaN" />
            </div>
            <div v-if="SelectedOption === 3" class="p-2">
                <Reembolsos ref="Reembolsos" :Corporativo="Corporativo" />
            </div>
            <div v-if="SelectedOption === 4" class="p-2">
                <ConsultaAnulacion ref="ConsultaAnulacion" :TipoCheque="'NA'" :ModalidadPago="'D'" :ImpresionLotes="'N'" :Corporativo="Corporativo" :key="NaN" />
            </div>

            <div v-if="SelectedOption === 5" class="p-2">
                <Eliminacion ref="EliminacionAnticipo" :TipoCheque="'NA'" :ModalidadPago="'D'" :Corporativo="Corporativo" :key="NaN" />
            </div>
            <div v-if="SelectedOption === 6" class="p-2">
                <Acreditamientos ref="AcreditamientoAnticipo" :TipoCheque="'NA'" :ModalidadPago="'D'" :Corporativo="Corporativo" :key="NaN" />
            </div>
            <div v-if="SelectedOption === 7" class="p-2">
                <PagosGenerados ref="PagosGenerados" :TipoDoc="TipoDoc" :Corporativo="Corporativo" :key="NaN" />
            </div>
            <div v-if="SelectedOption === 8" class="p-2">
                <ConsultaAnulacion ref="ConsultaAnulacion" :TipoCheque="'NP'" :ModalidadPago="'D'" :ImpresionLotes="'N'" :Corporativo="Corporativo" :key="NaN" />
            </div>
            <div v-if="SelectedOption === 9" class="p-2">
                <Eliminacion ref="EliminacionPagos" :TipoCheque="'NP'" :ModalidadPago="'D'" :Corporativo="Corporativo" :key="NaN" />
            </div>
            <div v-if="SelectedOption === 10" class="p-2">
                <Acreditamientos ref="AcreditamientoPagos" :TipoCheque="'NP'" :ModalidadPago="'D'" :Corporativo="Corporativo" :key="NaN" />
            </div>
        </div>
    </DxDrawer>
</div>
</template>

<script>
import Anticipos from './Anticipos.vue';
import PagosGenerados from './PagosGenerados.vue';
import Reembolsos from './Reembolsos.vue';
import ConsultaAnulacion from './ConsultaAnulacion.vue';
import Eliminacion from './Eliminacion.vue';
import Acreditamientos from './Acreditamientos.vue';

export default {
    name: 'Proveedores',
    components: {
        Anticipos,
        PagosGenerados,
        Reembolsos,
        ConsultaAnulacion,
        Eliminacion,
        Acreditamientos
    },
    data() {
        return {
            SelectedOption: 1,
            Title: 'Proveedores',

            menu: [{
                    id: 1,
                    title: "Anticipos",
                    icon: "hand-holding-dollar",
                },
                {
                    id: 2,
                    title: "Pagos generados",
                    icon: "receipt",
                },
                {
                    id: 3,
                    title: "Reembolsos",
                    icon: "scale-unbalanced",
                },
            ],

            tabsAttributes: {
                class: 'tabMenu'
            },
        }
    },
    methods: {
        clickOnItem(e) {
            this.SelectedOption = e.itemData.id
        }
    },
    created() {},
    mounted() {
        if (this.TipoDoc == 2) {
            document.getElementById("Drawer").className = 'Transferencias';
        } else {
            document.getElementById("Drawer").className = 'Cheques';
        }
    },
    beforeMount() {
        if (this.TipoDoc == 2) {
            this.menu = [{
                    key: "Anticipos",
                    items: [{
                            id: 1,
                            title: "Anticipos",
                            icon: "hand-holding-dollar",
                        }, {
                            id: 4,
                            title: "Consulta y anulación de Anticipos",
                            icon: "magnifying-glass-minus",
                        }, {
                            id: 5,
                            title: "Eliminación de anticipos",
                            icon: "circle-minus",
                        },
                        {
                            id: 6,
                            title: "Acreditamiento de anticipo",
                            icon: "money-bill-trend-up",
                        },
                    ]
                },
                {
                    key: "Pagos",
                    items: [{
                            id: 7,
                            title: "Pagos generados",
                            icon: "money-bill",
                        },
                        {
                            id: 8,
                            title: "Consulta y anulación pagos",
                            icon: "magnifying-glass-minus",
                        }, {
                            id: 9,
                            title: "Eliminación de pagos",
                            icon: "circle-minus",
                        },
                        {
                            id: 10,
                            title: "Acreditamiento de pagos",
                            icon: "money-bill-trend-up",
                        },
                    ]
                }
            ]
        }
    },
    props: {
        TipoDoc: null, // Indica desde qué módulo se invoca el componente  1 = Cheques / 2 = Transferencias
        Corporativo: null
    },
    watch: {}
}
</script>

<style>
.dx-popup-title {
    background-color: #f4eade !important;
    color: #2f496e !important;
}

.dx-list-item>#itemsMenuDrawer {
    color: #2988bc !important;
    background-color: #f4eade !important;
}

.dx-list-item-selected>#itemsMenuDrawer {
    color: #f4eade !important;
    background-color: #ed8c72 !important;
}

.dx-scrollable-container {
    touch-action: auto !important;
}

/*Ancho mínmo de los grid*/
#Contenido .dx-datagrid {
    min-width: 302px;
}

/**Modal actualizacion de peso y talla */
#popupTallaPeso .vs-popup {
    width: 400px !important;
}

.dx-datagrid-headers td {
    vertical-align: middle !important;
}

.dx-resizable {
    display: inline-grid;
}

.dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed)>td {
    background-color: #00ccff !important;
    color: black !important;
}

.dx-datagrid-rowsview .dx-row.dx-data-row.dx-column-lines.dx-state-hover>td {
    background-color: #79e4ff !important;
    color: black !important;
    font-size: 16px;
}

.dx-datagrid-headers {
    background-color: linen !important;
    color: black !important;
    font-weight: bold;
}

td {
    vertical-align: middle !important;
}

.dx-viewport,
.dx-device-phone,
.dx-device-mobile,
.dx-device-android,
.dx-device-android-6,
.dx-theme-generic,
.dx-theme-generic-typography,
.dx-color-scheme-light,
.dx-overlay-wrapper {
    color: #2980B9 !important;
}

[dir] .dx-popup-title {
    height: 3em !important;
}

.dx-button.dx-button-warning {
    background-color: #e0d100;
}

.dx-overlay-wrapper.dx-popup-wrapper.dx-dropdowneditor-overlay.dx-popover-wrapper.dx-popover-without-title.dx-lookup-popup-wrapper.dx-lookup-popup-search.dx-position-bottom {
    z-index: 999999 !important;
}

.buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

.button {
    height: 40px;
    width: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px !important;
}

.button:hover {
    background-color: blue !important;
}

.div-button {
    display: flex;
    justify-content: center;
}

/* 
    Drawer
*/

.flex-container {
    display: flex;
    flex-direction: column;
}

.itemSelected {
    color: white;
    background-color: #337ab7;
}

.itemNoSelected {
    color: black;
    background-color: transparent;
}

.borderDiv {
    border-style: solid;
    border-color: blueviolet;
}

.Cheques {
    width: 200px;
}

.Transferencias {
    width: 300px;
}
</style>
