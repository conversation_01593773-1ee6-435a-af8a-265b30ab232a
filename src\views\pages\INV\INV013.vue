<template>
    <vx-card title="Inventario Físico">
    
        <div class="content content-pagex">
            <vs-divider></vs-divider>
            <div class="flex flex-wrap">
    
                <div style="margin-right: 20px" class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <label class="typo__label">Estado</label>
                    <multiselect v-model="cb_lista_operacion" :options="lista_estado" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Operacion_seleccionada" placeholder="Seleccionar" @input="onChangeEstado">
                        <span slot="noOptions">Lista no disponible.</span>
                    </multiselect>
                </div>
    
                <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7">
                    <label class="typo__label">Fecha Inicio:</label>
                    <vs-input type="date" v-model="fecha_inicio" name="date1" />
                </div>
    
                <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7">
                    <label class="typo__label">Fecha Final:</label>
                    <vs-input type="date" v-model="fecha_final" name="date1" />
                </div>
    
                <vs-button style="float:left;margin: 20px" color="primary" type="filled" icon-pack="fas" icon="fa-search" @click="Consultar_Enc()"> Búsqueda</vs-button>
                <vs-button color="primary" style="float:left;margin: 20px" type="filled" icon-pack="fas" icon="fa-plus" @click="Abrir_Ventana_Emergente_Nuevo()"> Nuevo</vs-button>
    
            </div>
            <vs-divider></vs-divider>
    
            <vs-table2 max-items="10" pagination :data="Lista_vista" search id="tb_lista_solicitud">
    
                <template slot="thead">
                    <th width="130px">Nº. Inventario Físico</th>
                    <th>Bodega</th>
                    <th>Fecha </th>
                    <th>Responsable </th>
                    <th></th>
                    <!--<th style="text-align: center;">Recepcionada</th>-->
    
                </template>
    
                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">
    
                        <vs-td2>
                            {{tr.IDINVENTARIOENC}}
                        </vs-td2>
    
                        <vs-td2>
                            {{tr.NOMBRE_BODEGA}}
                        </vs-td2>
    
                        <vs-td2>
                            {{tr.FECHA_INICIO}}
                        </vs-td2>
                        <vs-td2>
                            {{tr.CORPORATIVO_RESPONSABLE}}
                        </vs-td2>
                        <vs-td2 v-if="tr.ESTADO == 'S'" width="150px" align="right">
                            <vs-button color="primary" icon-pack="fas" icon="fa-arrow-right" style="display:inline-block;margin-right:2px" @click="Emergente_Detalle(data[indextr], 'E')"></vs-button>
                        </vs-td2>
                        <vs-td2 v-else width="150px" align="right">
                            <vs-button color="#B2BABB" icon-pack="fas" icon="fa-arrow-right" style="display:inline-block;margin-right:2px"></vs-button>
                        </vs-td2>
    
                    </tr>
                </template>
            </vs-table2>
    
        </div>
    
        <!------------------------ CONFIRMACIÓN Y AJUSTE ------->
        <vs-popup align="center" classContent="popup-example" title=" Confirmación / Ajuste Inventario" :active.sync="Estado_Emergente_Ajuste">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
                <div style="text-align: center;" class="w-full md:w-full lg:w-full xl:w-full">
                    <b style="text-align: center;font-size:2vw">{{ Lista_detalle_Ajuste.DESCRIPCION_PRODUCTO  }} {{ Lista_detalle_Ajuste.DESCRIPCION_UNIDAD  }}</b>
                    <br>
                    <br>
    
                </div>
    
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                    <b style="font-size:1vw">Cantidad Sistema</b>
                    <vs-input v-model="Lista_detalle_Ajuste.CANTIDAD_SISTEMA" DISABLED class="w-1/2" />
                </div>
                <br>
    
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                    <b style="font-size:1vw">Cantidad Fisica</b>
                    <vs-input v-model="Lista_detalle_Ajuste.CANTIDAD_FISICA" class="w-1/2" />
                </div>
                <br>
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                    <b style="font-size:1vw">Cantidad Ajuste</b>
                    <vs-input v-model="Lista_detalle_Ajuste.CANTIDAD_AJUSTE" class="w-1/2" />
                </div>
                <br>
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                    <label class="typo__label">Tipo Ajuste</label>
                    <multiselect v-model="cb_tipo_ajuste" :options="Lista_tipos_ajustes" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="seleccion_tipo_ajuste" placeholder="Seleccionar" @input="onChangeTipo_ajuste">
                        <span slot="noOptions">Lista no disponible.</span>
                    </multiselect>
                </div>
                <br><br>
                <vs-divider> </vs-divider>
                <vs-divider></vs-divider>
                <vs-button color="primary" style="float:right" type="filled" icon-pack="feather" icon="icon-save" @click="Confirmar_ajuste()"> Grabar</vs-button>
                <vs-button color="danger" style="float:right" type="border" icon-pack="feather" icon="icon-x" @click="Consultar_InventarioDet_Editar(Lista_detalle_Ajuste.IDINVENTARIOENCFK);Estado_Emergente_Ajuste= false;"> Cancelar</vs-button>
                <vs-divider> </vs-divider>
            </div>
        </vs-popup>
        <!----------------------- DESPACHO REQUISICION ---------->
        <vs-popup classContent="popup-example" :title="Descripcion_Emergente" :active.sync="Estado_Emergente">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
                <!--
            <vs-popup fullscreen="true"  :title="Descripcion_Emergente" :active.sync="Estado_Emergente">
                <div >-->
    
                <!--- Ubicación Destino -->
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                    <b> <small> No. Inventario: </small></b>
                    <b style="font-size:2vw">{{ Id_inventario_seleccionado}}</b>
                    <br>
                    <br>
                    <label class="typo__label">Bodega a Inventariar:</label>
    
                    <multiselect :disabled="deshabilitar_botones" v-model="cb_bodegas" :options="Lista_bodegas" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Bodegas_seleccionado" placeholder="Seleccionar bodega" @input="onChangeBodega">
                        <span slot="noOptions">Lista no disponible.</span>
                    </multiselect>
                </div>
                <br>
                <!--- Categoria -->
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                    <ValidationProvider name="selectClase" v-slot="{ errors }">
                        <label style="font-size:12px">Categoría / Sub Categoria</label>
                        <multiselect :disabled="deshabilitar_botones" v-model="cb_categoria" :options="Lista_categoria" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="seleccion_categoria_a" placeholder="Búsqueda" @input="onChangeCategoria_a" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null">
                            <span slot="noOptions">Lista no disponible.</span>
                        </multiselect>
                    </ValidationProvider>
                </div>
                <div v-if="Id_inventario_seleccionado<=0" class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7">
                    <vs-button color="primary" style="float:left;margin: 5px" type="filled" icon-pack="fas" icon="fa-plus" @click="Sincronizar_datos()"> Generar Inventario</vs-button>
                </div>
                <!--- Producto -->
                <vs-divider>Detalle</vs-divider>
                <!--- Mostrar detalle de la orden--->
                <vs-table2 max-items="10" pagination :data="Lista_detalle_inventario" id="tb_lista_solicitud">
    
                    <template slot="thead">
                        <th>Producto</th>
                        <th>Unidad Medida</th>
                        <th>Max/Min</th>
                        <th width="100px">Cant. Sistema</th>
                        <th width="100px">Cant. Fisica</th>
                        <th width="100px">Cant. Ajuste</th>
                        <th width="60px"></th>
                        <!--<th width="80px"></th> -->
                    </template>
    
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
    
                            <vs-td2>
                                {{tr.DESCRIPCION_PRODUCTO}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.DESCRIPCION_UNIDAD}}
                            </vs-td2>
                            <vs-td2 style="text-align: center;">
                                <label style="width:100px; border-radius: 3px;  background-color:#00C853;text-align: center;color:white;font-size:12px; "> Máximo: {{tr.MAXIMOS}} </label>
                                <br>
                                <label style="width:100px; border-radius: 3px;  background-color:#FFD600;text-align: center;color:white;font-size:12px; "> Mínimo: {{tr.MINIMOS}} </label>
                            </vs-td2>                          
                            
    
                            <vs-td2 style="text-align: center;">
                                {{tr.CANTIDAD_SISTEMA}}
                            </vs-td2>
                          
                            <vs-td2>
                                {{tr.CANTIDAD_FISICA}}
                            </vs-td2>
    
                            
                            <vs-td2>
                                {{tr.CANTIDAD_AJUSTE}}
                            </vs-td2>
                            <vs-td2>
                                <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" style="display:inline-block" @click="Abrir_Ventana_Ajuste(tr)"></vs-button>
                            </vs-td2>
    
    
                        </tr>
                    </template>
                </vs-table2>
                <vs-divider></vs-divider>
                <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" id="btn_confirmacion" @click="Finalizar_Inventario()"> Finalizar Inventario</vs-button>
                <vs-divider></vs-divider>
    
            </div>
        </vs-popup>
    </vx-card>
    </template>
    
    <script>
    import Multiselect from 'vue-multiselect'
    import "vue-multiselect/dist/vue-multiselect.min.css"
    const axios = require('axios');
    export default {
        components: {
            Multiselect,
            //Timeline,
        },
        data() {
            return {
                Estado_Emergente_Ajuste: false,
                Estado_Emergente: false,
                cb_lista_operacion: '',
                Descripcion_Emergente: '',
    
                lista_estado: [{
                        ID: 'S',
                        DESCRIPCION: 'Proceso'
                    },
                    {
                        ID: 'F',
                        DESCRIPCION: 'Finalizado'
                    }
                ],
                Id_estado_seleccionado: 0,
                fecha_inicio: '',
                fecha_final: '',
    
                Lista_vista: [],
                Id_inventario_seleccionado: 0,
    
                Lista_bodegas: [],
                id_bodega_seleccionada: '',
                cb_bodegas: '',
    
                cb_categoria: '',
                Lista_categoria: [],
                Id_categoria_seleccionada: '',
    
                Lista_detalle_inventario: [],
                deshabilitar_botones: false,
    
                Lista_detalle_Ajuste: [],
    
                cb_tipo_ajuste: '',
                Lista_tipos_ajustes: [],
    
            }
        },
        mounted() {
            this.cb_lista_operacion = {
                ID: 'S',
                DESCRIPCION: 'Proceso'
            }
            this.Id_estado_seleccionado = 'S';
            this.Consultar_Enc();
            this.Cargar_Categoria();
        },
        computed: {
            sesion() {
                return this.$store.state.sesion
            }
        },
        methods: {
            Operacion_seleccionada({
                DESCRIPCION
            }) {
                return `${DESCRIPCION} `
            },
            onChangeEstado(value) {
                if (value !== null && value.length !== 0) {
                    this.Id_estado_seleccionado = value.ID;
                    this.Consultar_Enc();
                } else {
                    this.Id_estado_seleccionado = '';
                }
            },
    
            Bodegas_seleccionado({
                NOMBRE
            }) {
                return ` ${NOMBRE} `;
            },
            onChangeBodega(value) {
                if (value !== null && value.length !== 0) {
                    this.id_bodega_seleccionada = value.CODIGO;
    
                } else {
                    this.id_bodega_seleccionada = '';
                }
            },
            seleccion_categoria_a({
                Codigo,
                Nombre
            }) {
                return `${Codigo} - ${Nombre} `
            },
            onChangeCategoria_a(value) {
                if (value !== null && value.length !== 0) {
                    this.Id_categoria_seleccionada = value.Codigo;
                } else {
                    this.Id_categoria_seleccionada = '';
                }
            },
            seleccion_tipo_ajuste({
                NOMBRE
            }) {
                return ` ${NOMBRE} `
            },
            onChangeTipo_ajuste(value) {
              //  console.log("antes tipo ajutes: " + this.Lista_detalle_Ajuste.IDTIPOAJUSTEFK)
                if (value !== null && value.length !== 0) {
                    this.Lista_detalle_Ajuste.IDTIPOAJUSTEFK = value.CODIGO;
                } else {
                    this.Lista_detalle_Ajuste.IDTIPOAJUSTEFK = '0';
                }
               // console.log("despue tipo ajutes: " + this.Lista_detalle_Ajuste.IDTIPOAJUSTEFK)
            },
            Emergente_Detalle(datos) {
    
                this.Estado_Emergente = true;
                this.Descripcion_Emergente = "Editar Inventario"
                this.Consultar_Bodega('T', 0);
                this.Consultar_TiposAjustes();
    
                this.cb_bodegas = {
                    NOMBRE: datos.NOMBRE_BODEGA
                }
    
                this.cb_categoria = {
                    Codigo: datos.IDCATEGORIAFK,
                    Nombre: datos.DESCRIPCION_CATEGORIA
                }
                this.Lista_detalle_inventario = [];
                this.Id_inventario_seleccionado = datos.IDINVENTARIOENC;
                this.deshabilitar_botones = true;
                this.id_bodega_seleccionada = datos.IDBODEGAFK;
                this.Id_categoria_seleccionada = datos.IDCATEGORIAFK;
                this.Consultar_InventarioDet_Editar(this.Id_inventario_seleccionado);
                //this.Lista_detalle_inventario =  this.Consultar_InventarioDet(this.Id_inventario_seleccionado);
            },
            Abrir_Ventana_Emergente_Nuevo() {
                this.Estado_Emergente = true;
                this.Descripcion_Emergente = "Nuevo Inventario"
                this.Consultar_Bodega('T', 0);
                this.Consultar_TiposAjustes();
                this.cb_bodegas = '';
                this.cb_categoria = '';
                this.Lista_detalle_inventario = [];
                this.Id_inventario_seleccionado = 0;
                this.deshabilitar_botones = false;
                this.id_bodega_seleccionada = 0;
                this.Id_categoria_seleccionada = 0;
    
            },
            Validacion_Campos(Tipo, Nombre, valor, obligatorio, cant_maxima) {
                /**
                 * @General
                 * Tipo : N = Númerio, Nombre = Descripción del campo, Valor = A evaluar, Obligatorio= Verdadero o falso,cant_maxima  = Aplica unicamente para un string y define la cantidad de caracteres 
                 * Devuelve un mensaje al usuario si las validaciones no se cumple y aborta, de lo contrario devuelve verdadero
                 */
                if (Tipo == 'ID') {
                    if (valor <= 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else if (Tipo == 'N') {
    
                    if (valor < 0 || valor === "") {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else {
                    if (valor == '' && obligatorio == true) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
    
                        if (cant_maxima < valor.length) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: 'Campo ' + Nombre + ' no debe superar los ' + cant_maxima + ' carácteres.',
                            });
                            return false;
                        } else {
                            return true;
                        }
    
                    }
                }
            },
            Abrir_Ventana_Ajuste(detalle_sel) {
                this.Lista_detalle_Ajuste = detalle_sel;
                this.Estado_Emergente_Ajuste = true;
                this.cb_tipo_ajuste = '';
            },
            /*****************************CONSULTAR INFORMACION*************/
            Consultar_TiposAjustes() {
    
                this.axios.post('/app/v1_OrdenCompra/Tipos_movimientos', {
                        empresa: this.sesion.sesion_empresa
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_tipos_ajustes = [];
                        } else {
    
                            this.Lista_tipos_ajustes = resp.data.json;
                           // console.log(this.Lista_tipos_ajustes)
    
                        }
                    })
                    .catch(() => {})
            },
            Consultar_Enc() {
    
                this.axios.post('/app/v1_OrdenCompra/ConsultaInventario_Enc', {
                        empresa: this.sesion.sesion_empresa,
                        estado: this.Id_estado_seleccionado,
                        fecha_inicio: this.fecha_inicio,
                        fecha_fin: this.fecha_final,
                        tipo: 'T',
                        id_inventario_enc: 0
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_vista = [];
                        } else {
    
                            this.Lista_vista = resp.data.json;
                           // console.log(this.Lista_vista)
    
                        }
                    })
                    .catch(() => {})
            },
            Consultar_Bodega(Operacion, id_departamento) {
    
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/consulta_bodega', {
                        operacion: Operacion,
                        id_empresa: this.sesion.sesion_empresa,
                        id_departamento: id_departamento
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Bodegas',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_bodegas = [];
                            this.id_bodega_seleccionada = '';
                            this.cb_bodegas = '';
                        } else {
                            this.Lista_bodegas = resp.data.json;
                        }
                    })
                    .catch(() => {
    
                    })
    
            },
            Cargar_Categoria() {
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/inventario/BusquedaDepartamento', {
                        pagina: 1
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Categoría',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_categoria = "";
                        } else {
                            //Decodificación
    
                            this.Lista_categoria = resp.data.json;
    
                        }
                    })
                    .catch(() => {
    
                    })
            },
            Consultar_InventarioEnc: async function (Id_Inventario_Enc) {
                var datos = [];
                await axios.post('/app/v1_OrdenCompra/ConsultaInventario_Enc', {
                        empresa: this.sesion.sesion_empresa,
                        estado: 'T',
                        fecha_inicio: this.fecha_inicio,
                        fecha_fin: this.fecha_final,
                        tipo: 'E',
                        id_inventario_enc: Id_Inventario_Enc
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            //this.Inventario_seleccionada = [];
                        } else {
    
                            datos = resp.data.json;
    
                        }
                    })
                    .catch(() => {})
                return datos;
            },
            Consultar_InventarioDet: async function (Id_Inventario_Enc) {
                var datos = [];
                await axios.post('/app/v1_OrdenCompra/ConsultaInventario_DET', {
                        id_inventario_enc: Id_Inventario_Enc
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
    
                        } else {
                            datos = resp.data.json;
    
                        }
                    })
                    .catch(() => {})
                return datos;
    
            },
            Consultar_InventarioDet_Editar(Id_Inventario_Enc) {
    
                this.axios.post('/app/v1_OrdenCompra/ConsultaInventario_DET', {
                        id_inventario_enc: Id_Inventario_Enc
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
    
                        } else {
                            this.Lista_detalle_inventario = resp.data.json;
                           // console.log(this.Lista_detalle_inventario)
    
                        }
                    })
                    .catch(() => {})
    
            },
            async Sincronizar_datos() {
    
                //var Id_Inventario_enc = 0;
                this.Id_inventario_seleccionado = await this.Crear_Inventario('N');
                if (this.id_bodega_seleccionada > 0 && this.Id_categoria_seleccionada > 0) {
                    this.deshabilitar_botones = true;
                  //  console.log("Async -> NUEVO ID INVENTARIO ENC")
                  //  console.log(this.Id_inventario_seleccionado)
    
                    this.Lista_detalle_inventario = [];
                    this.Lista_detalle_inventario = await this.Consultar_InventarioDet(this.Id_inventario_seleccionado);
                   // console.log("Async -> dETALLE INVETARIO")
                  //  console.log(this.Lista_detalle_inventario)
                }
    
            },
            /******************** CREAR/ ACTUALIZAR / ELIMINAR ****************/
            Crear_Inventario: async function (operacion) {
                var ID_INVENTARIO_NUEVO = 0;
                var res_variables = false;
    
                res_variables = this.Validacion_Campos('ID', 'Bodega', this.id_bodega_seleccionada, true, 0);
    
                if (res_variables)
                    res_variables = this.Validacion_Campos('ID', 'Categoría', this.Id_categoria_seleccionada, true, 0);
    
                if (res_variables) {
                    await axios.post('/app/v1_OrdenCompra/Inventario_Enc', {
                            id_inventario_enc: 0,
                            empresa: this.sesion.sesion_empresa,
                            bodega: this.id_bodega_seleccionada,
                            categoria: this.Id_categoria_seleccionada,
                            operacion: operacion,
                            corporativo: this.sesion.corporativo,
                            estado: '',
                        })
                        .then(resp => {
    
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Inventario',
                                    text: resp.data.mensaje,
                                })
    
                            } else {
                                //var Id_enc_inventario = resp.data.resultado;
                                ID_INVENTARIO_NUEVO = resp.data.resultado;
    
                            }
    
                        })
    
                }
                return ID_INVENTARIO_NUEVO;
            },
            Confirmar_ajuste () {
                    this.axios.post('/app/v1_OrdenCompra/Inventario_Det', {
                            id_inventario_detalle: this.Lista_detalle_Ajuste.IDINVENTARIODET,
                            cantidad_fisica: this.Lista_detalle_Ajuste.CANTIDAD_FISICA,
                            cantidad_ajuste: this.Lista_detalle_Ajuste.CANTIDAD_AJUSTE ,
                            tipo_ajuste: this.Lista_detalle_Ajuste.IDTIPOAJUSTEFK ,
                            operacion: 'F',
                            corporativo: this.sesion.corporativo,
                            estado: 'N',
                        })
                        .then(resp => {
    
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Inventario',
                                    text: resp.data.mensaje,
                                })
    
                            } else {
                                //var Id_enc_inventario = resp.data.resultado;
                                this.Estado_Emergente_Ajuste =  false;
                                this.Consultar_InventarioDet_Editar(this.Lista_detalle_Ajuste.IDINVENTARIOENCFK);
    
                            }
    
                        })
    
                
            },
            Finalizar_Inventario () {
                
                    this.axios.post('/app/v1_OrdenCompra/Inventario_Enc', {
                            id_inventario_enc: this.Id_inventario_seleccionado,
                            empresa: this.sesion.sesion_empresa,
                            bodega: 0,
                            categoria: 0,
                            operacion: 'F',
                            corporativo: this.sesion.corporativo,
                            estado: '',
                        })
                        .then(resp => {
    
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Inventario',
                                    text: resp.data.mensaje,
                                })
    
                            } else {
                                //var Id_enc_inventario = resp.data.resultado;
                                this.Estado_Emergente = false;
                                this.Consultar_Enc();
    
                            }
    
                        })
    
            },        
        }
    }
    </script>
    
    <style scoped>
    .center {
        margin: auto;
        width: 60%;
        padding: 10px;
    }
    
    .center_2 {
        margin: auto;
        width: 60%;
        padding: 3px;
    }
    </style>
    