<template>
<vx-card title="Revisar Edad">

    <div v-if="!nuevo" style="border:1px solid #ccc; border-radius:10px">
        <div class="flex flex-wrap" style="padding:10px 20px" >
            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">

                <label class="vs-input--label">Admisión</label>
                <vx-input-group class="">
                    <vs-input v-model="info.codigo" />
                    <template slot="append">
                        <div class="append-text btn-addon">
                            <button type="submit" v-show="false" name="button"></button>
                            <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_radiologias()" icon="icon-search"></vs-button>
                        </div>
                    </template>
                </vx-input-group>
            </div>
        </div>
        <div class="flex flex-wrap" style="padding:10px 20px">
            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                <vs-input label="Edad Actual:" v-model="info.codigo"  class="w-full" disabled />
            </div>
            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                <vs-input label="Fecha Nacimiento:" v-model="info.codigo"  class="w-full" />
            </div>
            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                <vs-input label="Nueva Edad:" v-model="info.codigo"  class="w-full" disabled />
            </div>
        </div>
    </div>


    <vs-divider></vs-divider>
    <vs-button style="float:right" @click="guardar_informe(true)"> Guardar</vs-button>
    <!-- <vs-button color="primary" style="float:right" type="border" @click="info.opcion.show=false" v-else> Salir</vs-button> -->
    <div style="clear:both"></div>

</vx-card>
</template>

<script>
// import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";

export default {
    data() {
        return {
            examenes: [],
            info: {
                invalido: false,
                nombre1: null,
                nombre2: null,
                apellido1: null,
                apellido2: null,
                casada: null,
                nacimiento: null,
                edad: null,
                dpi: null,
                telefono: null,
                sexo: null,
                estado_civil: null,
                religion: null,
                lugar_nacimiento: null,
                nacionalidad: null,
                telefonos: null,
                celular: null,
                email: null,

                profesion: null,

                direccion_factura: null,
                nombre_factura: null,
                nit: null,

                carnet: null,
                socio: null,
                pasaporte: null,
                segurosocial: null,

                direccion: null,
                ciudad: null,
                calle: null,
                avenida: null,
                casa: null,
                zona: null,
                colonia: null,
                edificio: null,
                departamento: null,
                municipio: null,

                direccion_trabajo: null,
                telefono_trabajo: null,
                ocupacion: null,
                CalleTrabajo: null,
                AvenidaTrabajo: null,
                NoCasaTrabajo: null,
                ZonaTrabajo: null,
                ColoniaTrabajo: null,
                EdificioTrabajo: null,
                OficinaTrabajo: null,
                DeptoTrabajo: null,
                MunicipioTrabajo: null,

                padre: null,
                madre: null,
                conyuge: null,
            },
            Tipos: [{
                    Id: 1,
                    Nombre: 'Interno'
                },
                {
                    Id: 2,
                    Nombre: 'Emergencia'
                },
                {
                    Id: 3,
                    Nombre: 'IGSS'
                },
                {
                    Id: 4,
                    Nombre: 'RN en Hospital'
                },
                {
                    Id: 5,
                    Nombre: 'Paquete'
                },
            ],
            EstadosCiviles: [{
                    Id: 'C',
                    Nombre: 'Casado(a)'
                },
                {
                    Id: 'D',
                    Nombre: 'Divorsiado(a)'
                },
                {
                    Id: 'S',
                    Nombre: 'Soltero(a)'
                },
                {
                    Id: 'U',
                    Nombre: 'Unido(a)'
                },
                {
                    Id: 'V',
                    Nombre: 'Viudo(a)'
                },
            ]
        }
    },
    props: {
        paciente: {
            default: null
        },
        nuevo: {
            default: false
        }
    },
    components: {
        // Multiselect
    },
    methods: {

        cargar_estudios() {
            this.axios.post('/app/admision/Medicamento', {
                    Corporativo: 0
                })
                .then(resp => {
                    
                    this.examenes = []
                    resp.data.json.map(data => {
                        this.examenes.push({
                            ControlMedicamento: data.ControlMedicamento,
                            Admision: data.Admision,
                            Codigo: data.Codigo,
                            FechaAsignada: data.FechaAsignada,
                            Frecuencia: data.Frecuencia,
                            DIAS: data.DIAS,
                            Serie: data.Serie,
                            Paciente: data.Paciente,
                            Tipo: data.tipo
                        })
                    })

                })
        }
    },
    created() {

    }
}
</script>

<style scoped>
.center li {
    display: inline !important;
    padding: 2px 10px;
}

.button {
    margin: 0px !important;
}

.btnx {
    margin-left: 10px !important;
    background: red;
    border-radius: 5px 0px 0px 5px;
}

.btn-drop {
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: red;

}

.menu-funcionalidad {
    /* padding-top:0 !important; */
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    padding: 15px;
    display: inline;
    float: right;
    border-radius: 10px;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 10px;
}

.menu-funcionalidad td {
    text-align: center;
    padding-right: 10px;
}

.vs-switch {
    height: 30px;
    width: 60px !important;
}

.vs-circle-switch {
    height: 25px;
    width: 25px;
}

.vs-switch--icon {
    font-size: 15px
}

.vs-switch.vs-switch-active .vs-switch--circle {
    margin-left: calc(100% - 30px);
}
</style>
