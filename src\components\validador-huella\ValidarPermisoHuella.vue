<template>
    <div>   
        <buscador ref="BuscarUsuarioCorporativo" buscador_titulo="Buscador / Corporativo" :api="'app/Ajenos/Busqueda_Corporativo'" 
            :campos="['Corporativo', ['Nombres', 'Nombre'], ['Apellidos', 'Apellido']]"
            :titulos="['Corporativo', 'Nombres', 'Apellidos']"
            :api_filtro="{ Corporativo:ParametroCorporativo}"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" :api_preload="true"  />

        <InfoPopup :visible.sync="popUpSensorHuellas" :width="'60%'" :height="'60%'" 
                        :show-title="true" :full-screen="false" :hide-on-outside-click="false" 
                        :wrapper-attr="popupAttributes"
                        :title="'Validación de Huella ' + info.Nombres+ ' ' + info.Apellidos " :showCloseButton="true">
            <ValidarHuella ref="validarHuellas" :tipoHuella="huella" :corporativoBuscar="info.Corporativo" @getValidacionHuella="getValidacionHuella"/>
        </InfoPopup>                 
        <vs-popup :title="TituloPopup.length >0? TituloPopup: TituloDefecto " :active.sync="popUpPermisosHuella" z-index="100" id="pop-up-2">
            <!-- <div class="w-full md:w-1/2 lg:w-1/2 sm:w-1/2 p-1">
                <SM-Buscar 
                    v-model="info.Corporativo" label="Código Corporativo" api="app/Ajenos/Busqueda_Corporativo" 
                    :api_campos="['Corporativo',['Nombres', 'Nombre'],['Apellidos', 'Apellido'],]" 
                    :api_titulos="['Corporativo', 'Nombres', 'Apellidos']" 
                    api_campo_respuesta="Corporativo" 
                    :callback_buscar="Consulta().ConsultaCorporativo"
                    :disabled_texto="true"  />
            </div> -->
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="12">
                    <vs-row vs-align="center">
                        <vs-col vs-type="flex" vs-align="center" vs-w="6">
                            <label class="typo__label">Corporativo:&nbsp;</label>
                            <vs-input v-model="ParametroCorporativo" @keyup.enter="BuscarCorporativo()" @keydown.tab="BuscarCorporativo()" ></vs-input>
                            <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="BuscarCorporativo()" icon="fa-search"></vs-button>
                        </vs-col>
                        <vs-col vs-type="flex" vs-align="center" vs-w="6">
                            <h5>{{ info.Nombres }}  {{ info.Apellidos }}</h5>
                        </vs-col>
                    </vs-row>
                </vs-col>
            </vs-row>
            <div v-if="info.Corporativo">
                <div class="flex flex-wrap">
                    <div class="w-full md:w-1/2 lg:w-1/2 sm:w-1/2 pt-6 text-right">
                        <vs-button color="primary" @click="ValidaHuellaActiva" icon-pack="fas" icon="fa-fingerprint">Validar Huella</vs-button>
                    </div>
                </div>
            </div>
        </vs-popup>
</div>
</template>
<script>
import ValidarHuella from '/src/components/validador-huella/ValidarHuella.vue'
import { DxPopup as InfoPopup } from 'devextreme-vue/popup'

export default {
    components: {
        ValidarHuella,
        InfoPopup
    },
    props: {
        permisoHuella: {
            type: String,
            default: null
        }
    },
    data(){
        return{
            popupAttributes: {
                id: 'elementId',
                class: 'popupHuella'
            },
            TituloPopup : 'Permiso Huellas',
            TituloDefecto: 'Permiso Huellas',
            popUpPermisosHuella: false,
            popUpSensorHuellas: false,
            huellaValidada:false,           
            huella: "1", 
            info: {
                Corporativo: null,
                Status: 'A',
                Nombres: null,
                Apellidos: null,
                Correo: null,
                IdPuesto: null,
                DPI: null,
                NIT: null,
                Ajeno: null,
                Indicio: null
            },
            ParametroCorporativo: null
        }
    },
    mounted(){
        this.IpLocal = localStorage.getItem('IpLocal');
    },
    watch:{
        popUpSensorHuellas(valor){
                if(!valor){                    
                    this.$refs.validarHuellas.desactivarValidador()
                }
        },
        popUpPermisosHuella(valor){
            if(!valor){
                this.info.Corporativo = null;
                if(!this.huellaValidada){                                  
                    this.cerrarPermisosHuella()
                }else{
                    this.huellaValidada = false;
                }                    

            }else{
                if(!this.IpLocal || this.IpLocal.length <= 0){
                    this.$vs.notify({
                        title:'Configuración',
                        text:'Su maquina no esta configurada para utilizar el lector de huellas',
                        timer:6000,
                        color:'danger',
                        position: 'top-center'
                    })
                    this.popUpPermisosHuella = false;
                    return
                }                
            }
        },
        ParametroCorporativo(value){
            if(value == ''){
                this.ParametroCorporativo = null
            }
        }
    },
    methods:{
        Consulta: function() {
            return {
                /**
                 * Consula de Corporativo
                 * Permite obtener los nombres y apellidos del corporativo
                 */
                ConsultaCorporativo: (data) => {                    
                    this.axios.post('/app/administracion/CorporativoConsulta', {
                            IdCorporativo: data.Corporativo
                        })
                        .then(resp => {
                            if (resp.data.json) {
                                this.$map({
                                    objeto: this.info,
                                    respuestaAxios: resp,
                                    omitir: ['Corporativo']
                                })
                            }
                        })
                },
            };
        },
        ValidaHuellaActiva(){
            this.axios.post('/app/huella/ValidaHuellaActiva',{"Corporativo":this.info.Corporativo,"Opcion":"C","SubOpcion":13})
                .then((resp)=>{
                        if(resp.data.estado==0){
                            if(resp.data.descripcion){
                                this.Validar(this.info.Corporativo);
                            }else{
                                this.$vs.notify({
                                        title:'Validacion Huella',
                                        text:'No cuenta con una huella registrada en el sistema',
                                        timer:6000,
                                        color:'danger',
                                        position: 'top-center'
                                    })
                            }
                        }
                    })  
                            
        },
        getValidacionHuella(data){
            if(data.StatusPlantilla == 'VERIFICADO'){ 
                this.$validar_permiso_corporativo(this.info.Corporativo,this.permisoHuella)
                    .then((resp)=>{
                        let respuestaPermiso = {...data,permiso:this.permisoHuella,statusValidacion:resp.data.descripcion?"S":"N"}     
                        this.$emit("getPermisosHuellas", respuestaPermiso)                                     
                        }
                    )
                this.ParametroCorporativo = ''
                this.info = {}
                this.popUpSensorHuellas = false
                this.popUpPermisosHuella = false  
                this.huellaValidada = true;                      
            }
        },
        cerrarPermisosHuella(){
                    let respuestaPermiso = {permiso:this.permisoHuella,statusValidacion:"NA" } 
                    this.$emit("getPermisosHuellas", respuestaPermiso)  
        },
        Validar(corporativo){
            this.popUpSensorHuellas = true
            this.$refs.validarHuellas.activarValidador(corporativo)
        },
        BuscarCorporativo(){
            if(this.ParametroCorporativo != '' && this.ParametroCorporativo != null && this.ParametroCorporativo != 0){
                this.axios.post('/app/administracion/CorporativoConsulta', {
                    IdCorporativo: this.ParametroCorporativo
                })
                .then(resp => {
                    if(resp.data.codigo == 0){
                        if(resp.data.json.length == 1){
                            this.info = resp.data.json[0]
                            this.ParametroCorporativo = resp.data.json[0].Corporativo
                            
                            this.ValidaHuellaActiva();
                        }else{
                            this.$refs.BuscarUsuarioCorporativo.iniciar((data) => {
                                if(data != null){
                                    this.axios.post('/app/administracion/CorporativoConsulta', {
                                        IdCorporativo: data.Corporativo
                                    })
                                    .then(resp => {
                                        this.info = resp.data.json[0]
                                        this.ParametroCorporativo = resp.data.json[0].Corporativo
                                        this.ValidaHuellaActiva();
                                    })
                                }
                            })
                        }
                    }
                })
            }else{
                this.$refs.BuscarUsuarioCorporativo.iniciar((data) => {
                    if(data != null){
                        this.axios.post('/app/administracion/CorporativoConsulta', {
                            IdCorporativo: data.Corporativo
                        })
                        .then(resp => {
                            this.info = resp.data.json[0]
                            this.ParametroCorporativo = resp.data.json[0].Corporativo
                        })
                    }
                })
            }
        }


    }
}
</script>
<style>
.popupHuella{
    z-index: 99999 !important;
}
</style>