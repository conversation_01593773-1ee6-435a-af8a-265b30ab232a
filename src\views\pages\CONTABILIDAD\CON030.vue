<template>
<vx-card title="Contabilidad General">
    <div class="content content-pagex">
        <vs-divider class="label-size"> GENERAR PARTIDA INVENTARIO COSTOS </vs-divider>
        <!-- GenerarPatidaINV() -->
        <div class="flex-container" v-show="EmpresaSEM">

            <vs-col class="md:w-full lg:w-full xl:w-4/12 p-2">
                <vx-card class="w-full" title="" style="padding:10px 10px">

                    <div class="w-full" align="right">
                        <div class="w-full" style="padding:10px 10px">
                            <vs-button color="success" class="label-size" type="filled" @click="GenerarPatidaINV()"> Generar </vs-button>
                        </div>
                    </div>
                    <vs-divider></vs-divider>
                    <br>
                    <div class="w-full">
                        <div class="flex-container">
                            <div class="md:w-9/10 lg:w-full xl:w-9/10 p-2">
                                <ValidationProvider name="Periodo">
                                    <label class="label-size">Período:</label>
                                    <multiselect v-model="cbPeriodos" :options="ListaPeriodos" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="PeriodoSeleccionado" placeholder="Seleccionar Período" @input="onChangePeriodos">
                                        <span slot="noOptions">Lista no disponible.</span>
                                    </multiselect>
                                </ValidationProvider>
                            </div>
                            <div class="md:w-1/10 lg:w-full xl:w-1/10 p-2">
                                <label class="label-size"> No. Partida </label>
                                <vs-input type="number" class="md:w-1/10 lg:w-full xl:w-1/10" v-model="Partida" />
                            </div>
                        </div>
                    </div>
                    <br>
                    <div class="w-full">
                        <div class="w-full" style="padding:10px 10px">
                            <ValidationProvider name="DescripcionP">
                                <vs-textarea v-model="DescripcionP" type="text"></vs-textarea>
                            </ValidationProvider>
                        </div>
                    </div>
                </vx-card>
            </vs-col>
            <vs-col class="md:w-full lg:w-full xl:w-8/12 p-2">
                <vx-card class="w-full" title="" style="padding:10px 10px">
                    <div class="w-full">
                        <vs-table2 tooltip max-items="10" pagination :data="ListaDetalle" noDataText="Sin datos disponibles" search id="Detalle">
                            <template slot="thead">
                                <th width="3px">Línea</th>
                                <th width="50px">Cuenta</th>
                                <th width="50px">Debe</th>
                                <th width="50px">Haber</th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2 noTooltip :data="data[indextr].Linea" align="center">
                                        {{data[indextr].Linea}}
                                    </vs-td2>
                                    <vs-td2 noTooltip :data="data[indextr].Cuenta" align="center">
                                        {{data[indextr].Cuenta}}
                                    </vs-td2>
                                    <vs-td2 noTooltip :data="data[indextr].Debe" align="right">
                                        {{data[indextr].Debe}}
                                    </vs-td2>
                                    <vs-td2 noTooltip :data="data[indextr].Haber" align="right">
                                        {{data[indextr].Haber}}
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                    <br>
                    <div class="w-full">
                        <div class="flex-container">
                            <div class="md:w-full lg:w-full xl:w-1/10 p-2">
                                <label class="label-size"> Diferencia </label>
                                <vs-input type="number" align="right" class="md:w-1/10 lg:w-full xl:w-1/10" v-model="Diferencia" />
                            </div>
                            <div class="md:w-full lg:w-full xl:w-1/10 p-2">
                                <label class="label-size"> Total Debe </label>
                                <vs-input type="number" align="right" class="md:w-1/10 lg:w-full xl:w-1/10" v-model="TDebe" />
                            </div>
                            <div class="md:w-full lg:w-full xl:w-1/10 p-2">
                                <label class="label-size"> Total Haber </label>
                                <vs-input type="number" style="float:right" class="md:w-1/10 lg:w-full xl:w-1/10" v-model="THaber" />
                            </div>
                        </div>
                    </div>
                    <br>
                    <vs-divider></vs-divider>
                    <div class="flex flex-wrap">
                        <div class="w-full">
                            <div style="float:right;padding:10px 10px">
                                <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" @click="GuardarPatidaINV()" id="btn_guardar" :disabled="Guardar"> Guardar</vs-button>
                                <vs-button color="success" style="float:right;margin: 5px" type="filled" icon-pack="fas" icon="fa-file-excel" @click="GenerarReporte('application/vnd.ms-excel','EXCEL')" :disabled="Exportar">Exportar</vs-button>
                                <vs-button color="danger" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-x" @click="Cancelar()" id="btn_cancelar"> Cancelar</vs-button>
                            </div>
                        </div>
                    </div>
                </vx-card>
            </vs-col>

        </div>
    </div>
</vx-card>
</template>

<script>
import Multiselect from 'vue-multiselect'

import "vue-multiselect/dist/vue-multiselect.min.css"

export default {
    components: {
        Multiselect,

    },
    data() {
        return {
            cbPeriodos: '',
            ListaPeriodos: [],
            CodigoPeriodo: '',
            Descripcion: '',
            Partida: 0,
            DescripcionP: '',
            Diferencia: 0,
            TDebe: 0,
            THaber: 0,
            ListaDetalle: [],
            Exportar: true,
            Guardar: true,
            EmpresaSEM: false,
            ValidacionEmpresa: ''
        }
    },
    mounted() {
        this.Cancelar()
        this.ConsultaPeriodo()
        this.Exportar = true
        this.Guardar = true
        this.ValidarEmpresa()
    },

    methods: {
        ValidarEmpresa() {
            this.Empresa = false
            this.axios.post('/app/v1_contabilidad_general/PartidaInventarioCosto', {
                    Opcion: 'C',
                    SubOpcion: '1',
                    Visualizar: 0

                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.EmpresaSEM = true
                    } else {
                        this.ValidacionEmpresa = resp.data.json[0].descripcion
                        this.EmpresaSEM = false
                    }

                })
                .catch(() => {})
        },
        //PERIODOS
        ConsultaPeriodo() {
            this.axios.post('/app/v1_contabilidad_general/ConsultaPeriodoActual', {
                    Opcion: "C",
                    SubOpcion: "3",
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaPeriodos = resp.data.json;
                        this.cbPeriodos = {
                            CodigoPeriodo: resp.data.json[0].CodigoPeriodo,
                            Descripcion: resp.data.json[0].Descripcion
                        }
                        this.Partida = resp.data.json[0].CorrelativoPartidas
                        this.DescripcionP = 'Partida de Inventario - Costo de ' + this.cbPeriodos.Descripcion

                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido cargar los periodos, favor validar.',
                        })
                    }
                })
                .catch(() => {})
        },
        onChangePeriodos(value) {
            if (value !== null && value.length !== 0) {
                this.CodigoPeriodo = value.CodigoPeriodo
                this.Descripcion = value.Descripcion
                this.Partida = value.CorrelativoPartidas == 0 ? '1' : value.CorrelativoPartidas,
                    this.DescripcionP = 'Partida de Inventario - Costo de ' + this.cbPeriodos.Descripcion
                this.Cancelar()
            } else {
                this.CodigoPeriodo = ''
                this.Descripcion = ''
            }
        },
        PeriodoSeleccionado({
            CodigoPeriodo,
            Descripcion
        }) {
            return `${CodigoPeriodo} - ${Descripcion} `
        },

        GenerarPatidaINV() {
            this.axios.post('/app/v1_contabilidad_general/PartidaInventarioCosto', {
                    Periodo: this.cbPeriodos.CodigoPeriodo,
                    Visualizar: 1,
                    Partida: this.Partida,
                    PartidaDescripcion: this.DescripcionP,
                    Opcion: 'C',
                    SubOpcion: '2'

                })
                .then(resp => {
                    if (resp.data.codigo == 0) {

                        if (resp.data.json[0].tipo_error) {
                            this.Cancelar()

                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Partida de Inventario-Costo',
                                text: resp.data.json[0].descripcion,
                            })
                            return;

                        } else {
                            this.ListaDetalle = resp.data.json.map(data => {
                                return {
                                    ...data,
                                    Debe: parseFloat(data.Debe).toFixed(2),
                                    Haber: parseFloat(data.Haber).toFixed(2)
                                }
                            })
                            if (this.ListaDetalle.length > 0) {
                                var TotalDebe = 0;
                                var TotalHaber = 0;
                                this.ListaDetalle.forEach(element => {
                                    TotalDebe += (Number(element.Debe)),
                                        TotalHaber += (Number(element.Haber))
                                });
                                this.TDebe = (parseFloat(TotalDebe).toFixed(2));
                                this.THaber = (parseFloat(TotalHaber).toFixed(2))
                                this.Diferencia = (parseFloat(this.TDebe - this.THaber).toFixed(2))
                            }
                            if (this.Diferencia > 0 || this.Diferencia < 0) {
                                this.Exportar = true
                                this.Guardar = true
                            } else {
                                this.Exportar = false
                                this.Guardar = true
                            }
                        }
                    }
                })
                .catch(() => {})
        },
        GuardarPatidaINV() {
            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Confirmación',
                text: 'Este proceso almacena permanentemente la Partida generada. ¿Desea continuar? ',
                acceptText: 'Aceptar',
                cancelText: 'Cancelar',
                accept: () => {
                    const url = this.$store.state.global.url
                    this.axios.post('/app/v1_contabilidad_general/PartidaInventarioCosto', {
                            Periodo: this.cbPeriodos.CodigoPeriodo,
                            Visualizar: 0,
                            Partida: this.Partida,
                            PartidaDescripcion: this.DescripcionP,
                            Opcion: 'C',
                            SubOpcion: '2'
                        })
                        .then(resp => {
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Partida de Inventario-Costo',
                                    text: 'No se ha podido guardar la partida, validar datos',
                                })
                            } else {
                                this.Cancelar()
                                this.ConsultaPeriodo()
                            }
                        })
                        .catch(() => {})
                }
            })

        },
        Cancelar() {
            this.Exportar = true
            this.Guardar = true
            this.ListaDetalle = []
            this.Diferencia = 0
            this.TDebe = 0
            this.THaber = 0
        },
        async GenerarReporte(formatoInterno, formato) {
            this.Guardar = false
            let nombreReporte = 'ReporteMovPartidaInvCostos';
            let postData = {
                nombrereporte: nombreReporte,
                tiporeporte: formatoInterno,
                Opcion: 'C',
                SubOpcion: '2',
                Periodo: this.cbPeriodos.CodigoPeriodo,
                Visualizar: 1,
                Partida: this.Partida,
                PartidaDesc: this.DescripcionP
            }
            this.$reporte_modal({
                Nombre: 'ReporteTransMovPartidaInvCostos',
                Opciones: {
                    ...postData
                },
                Formato: formato
            })
        }
    }
}
</script>   

<style scoped>
.footer {
    height: 70px;
    margin-top: -65px;
    margin-left: 10px;
}

.label-size {
    font-size: 14px;
    font-weight: bold;
}

.label-i {
    font-size: 18px;
    font-weight: bold;
    color: rgb(240, 158, 6);
}

.cuadro-i {
    background: #faf2f1;
}

.flex-container {
    display: flex;
    flex-direction: row;
}

.flex-item-right {
    padding: 10px;
    flex: 50%;
    margin-left: 50px;
    margin-right: 50px;
}

/* Responsive layout - makes a one column layout instead of a two-column layout */
@media (max-width: 800px) {
    .flex-container {
        flex-direction: column;
    }
}
</style>
