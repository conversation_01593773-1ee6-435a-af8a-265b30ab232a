<template>
    <vx-card :title="`Informes de Radiología ADENDUM ${sesion.sesion_sucursal_nombre}`" class="rad009-container "> 
        <vs-row class="w-full">
            <vs-col class="sm:w-4/12 md:w-3/12 lg:w-2/12 xl:w-2/12 p-1">                                  
                <vs-input label="Fecha Orden Inicial" type="date" class="w-full" v-model="info.fechaInicial" />
            </vs-col>
            <vs-col class="sm:w-4/12 md:w-3/12 lg:w-2/12 xl:w-2/12 p-1">                                  
                <vs-input label="Fecha Orden Final" type="date" class="w-full" v-model="info.fechaFinal" />
            </vs-col>       
            <vs-col class="w-1/12">
              <vs-button color="primary" style="float:left;margin: 20px" type="filled" icon-pack="fas" icon="fa-search" id="btn_buscar"  
                         @click="CargarInformes()">Búsqueda</vs-button>
            </vs-col>     
        </vs-row>
        <vs-row class="w-full">
            <DxDataGrid
            v-bind="DefaultDxGridConfiguration"
            ID="InformesAdendum"
            :ref="InformesAdendumGridRef"
            :data-source="listaInformesAdendum"
            :show-borders="true"
            :row-alternation-enabled="false"       
            @key-down="onKeyDown"
            height="auto"            
            width="100%"
            >
          <DxDataGridDxFilterPanel :visible="true"/>
            <DxDataGridColumn
              :width="110"
              data-field="admision"
              caption="Admisión"
              :visible="true"
              sort-order="desc"
            />
            <DxDataGridColumn
              :width="110"
              data-field="ordenReporte"
              caption="Orden"
              :visible="true"
              :alignment="'left'"
            />
            <DxDataGridColumn
              :width="250"
              data-field="nombrePaciente"
              caption="Paciente"
              :visible="true"
            />
            <DxDataGridColumn
              :width="250"
              data-field="hospital"
              caption="Hospital"
              :visible="true"
            />
            <DxDataGridColumn
              :width="100"
              data-field="fecha"
              caption="Fecha Orden"
              :visible="true"
            />
            <DxDataGridColumn
              :width="75"
              data-field="hora"
              caption="Hora Orden"
              :visible="true"
            />
            <DxDataGridColumn
              :width="200"
              data-field="radiologo"
              caption="Radiologo"
              :visible="true"
            />
            <DxDataGridColumn
              :width="120"
              data-field="fechaCongelaInforme"
              caption="Fecha Congela Informe"
              :visible="true"
            />
            <DxDataGridColumn
              :width="100"
              data-field="usuarioCongelaInforme"
              caption="Usuario Congela"
              :visible="true"
            />
            <DxDataGridColumn
              :width="120"
              data-field="fechaCongelaAdendum"
              caption="Fecha Ultima Congela Adendum"
              :visible="true"
            />
            <DxDataGridColumn
              :width="100"
              data-field="usuarioCongelaAdendum"
              caption="Usuario Ultimo Congela Adendum"
              :visible="true"
            />
            <DxDataGridColumn :width="175" caption="Verificación del informe" type="buttons" name="accion" 
                :visible="true"
                :buttons="[ 
                            {icon:'fas fa-file-pdf',onClick:radiologia_reporte, id: 'idVisualizarAdendum',
                             cssClass:'btn-warning',
                             text:'Visualizar Adendum' , 
                             disabled:(e)=>e.row.data.informeLock == 'N' },
                          ]" css-class="button-icons">
            </DxDataGridColumn> 

            <DxDataGridDxSearchPanel :visible="true" :search-enabled="true" :width=400 />
            <DxDataGridPaging :page-size="15" />
            <DxDataGridPager :visible="true" />

            <DxDataGridToolbar>
              <DxFormItem name="searchPanel" locateInMenu="auto" />
              <DxFormItem
                location="after"
                template="opcionesTemplate"
                locateInMenu="auto"
              />
            </DxDataGridToolbar>

            <template #opcionesTemplate>
              <div class="flex flex-wrap">
                <div class="pl-4 pr-2">
                  <DxButton
                    id="botonPdf"
                    icon="fas fa-file-pdf"
                    hint="Exportar en pdf"
                    type="danger"
                    styling-mode="contained"
                    @click="exportarPdf"
                  />
                </div>
                <div class="pl-2 pr-2">
                    <DxButton
                        id="botonExcel"
                        icon="far fa-file-excel"
                        hint="Exportar en excel"
                        type="success"
                        styling-mode="contained"
                        @click="exportarExcel"
                    />
                </div>                
              </div>
            </template>
            
          </DxDataGrid>
        </vs-row>
    </vx-card>
</template>
<script>
import { DefaultDxGridConfiguration } from "./data";
import moment from "moment";
import { jsPDF } from 'jspdf';
import { exportDataGrid as exportDataGridToPdf } from 'devextreme/pdf_exporter';
import { exportDataGrid as exportDataGridToExcel } from 'devextreme/excel_exporter'; 
import { saveAs } from 'file-saver-es';
import { Workbook } from 'exceljs';
const InformesAdendumGridRef = 'InformesAdendumGridRef';
export default{    
    name:'INFORME_ADENDUM',
    methods: {
       onKeyDown(e) {
          if(e.event.keyCode == 27){ //ESC
            this.$refs[this.InformesAdendumGridRef].instance.deselectAll()               
          }
       },
       radiologia_reporte(linea) {
          this.$reporte_modal({
              Nombre: "Informes por Orden",
              Opciones: {
                  tipoorden: linea.row.data.TipoOrden,
                  orden: linea.row.data.Orden,
                  linea: linea.row.data.LineaOrden,
                  idioma: false
              }
          })
        },
        CargarInformes(){
          this.axios.post('/app/radiologia/ConsultaInformesRadiologia',
                          {
                            sub_opcion: '1',
                            fecha_inicio: this.GetDateValue(this.info.fechaInicial),
                            fecha_fin: this.GetDateValue(this.info.fechaFinal),
                          })
                    .then(resp=>{
                      this.listaInformesAdendum = resp.data.json.sort((a,b)=> moment(b.fecha, "DD/MM/YYYY").toDate() - moment(a.fecha, "DD/MM/YYYY").toDate())
                    })
        },
        GetDateValue(value) {
            if (value === undefined) {
                return null
            } else if (value === null) {
                return null
            } else if (value == '') {
                return null
            }
            return moment(value).format('DD/MM/YYYY');           
        },
        GetDateTimeValue(value) {
            if (value === undefined) {
                return null
            } else if (value === null) {
                return null
            } else if (value == '') {
                return null
            }
            return moment(value).format('YYYYMMDD_HHmm');                 
        },
        exportarExcel() {
            const workbook = new Workbook();
            const CargosPorAdmision = workbook.addWorksheet('Informes Adendum');
            exportDataGridToExcel({
                worksheet: CargosPorAdmision,
                component: this.$refs[this.InformesAdendumGridRef].instance              
            }).then(() => {                
                workbook.xlsx.writeBuffer().then((buffer) => {
                saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'Informes_Adendum_'+this.GetDateTimeValue(new Date())+'.xlsx');
                });
            });
        },
        exportarPdf() {
            const doc = new jsPDF({orientation:'landscape'});
            exportDataGridToPdf({
                jsPDFDocument: doc,
                component: this.$refs[this.InformesAdendumGridRef].instance,
            }).then(() => {
                doc.save('Informes_Adendum_'+this.GetDateTimeValue(new Date())+'.pdf');
            });
        }
    },
    computed:{
        sesion() {
            return this.$store.state.sesion
        }
    },
    data(){
     return {
        info:{
            fechaInicial:null,
            fechaFinal:null,
        },
        listaInformesAdendum:[],
        InformesAdendumGridRef,
        DefaultDxGridConfiguration:{...DefaultDxGridConfiguration,
                                    hoverStateEnabled: false,
                                    selection: {mode: 'single'},
                                   },  
     }   
    }
}

</script>
<style>

.rad009-container .dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed)
  > td {
  background-color: #00ccff !important;
  color: black !important;
}

.rad009-container .dx-datagrid-rowsview .dx-row.dx-data-row.dx-column-lines.dx-state-hover > td {
  background-color: #79e4ff !important;
  color: black !important;
  font-size: 16px;
}

.rad009-container .dx-datagrid-headers {
  background-color: linen !important;
  color: black !important;
  font-weight: bold;
}

.rad009-container .btn-warning{
  color: #f44336  !important;
}

.rad009-container .btn-warning.dx-state-disabled{
  color: #e3e3e3 !important;
}
</style>