<template>
  <div id="semexpContainer" class="exp029-container w-full pr-2">
    <!-- Cronómetro -->
    <h1 style="text-align: center;">Órdenes médicas pendientes</h1>

 <div class="cronometro">
    <span style="color: #2980B9; font-size:large;" v-if="tiempoRestante">⏳ Actualiza en: </span>
    <temporizador
      v-if="temporizadorActivo"
      :horas="0"
      :minutos="0"
      :segundos="segundos"
      :sonido="false"
      style="margin-left: 10px; color: #2980B9;" 
      @onTimeUp="TimeUp"
    />
  </div>

    <div class="grid-container">
      <!-- <h2 class="cronometro">{{ tiempoRestante }}</h2> -->
      <DxDataGrid
        :dataSource="dsListaOrdenes"
        id="gridListaOrdenes"
        :no-data-text="'Sin registros'"
        :headerFilter="{ visible: true, allowSearch: true }"
        :hoverStateEnabled="true"
        :show-borders="true"
        :column-auto-width="true"
        :cellHintEnabled="true"
        selection-mode="single"
        :column-min-width="100"
        height="auto"
        width="auto"
        :on-row-prepared="customRowStyle"
        :allow-column-reordering="true"
      >
        <DxDataGridColumn
          dataField="Admision"
          caption="Admision"
          :dataType="'string'"
          :width="100"
        />
        <DxDataGridColumn
          dataField="Habitacion"
          caption="Habitación"
          :width="100"
          :dataType="'string'"
        />

        <DxDataGridDxSearchPanel :visible="true" width="400px" />
        <DxDataGridPager :visible="true" />
        <DxDataGridColumn
          dataField="Ubicacion"
          caption="Ubicación"
          width="auto"
        />
        <DxDataGridColumn
          dataField="Base"
          caption="Sucursal origen"
          :dataType="'string'"
          width="auto"
        />

        <DxDataGridToolbar>
          <DxDataGridItem
            location="after"
            locateInMenu="auto"
            showText="inMenu"
            widget="dxButton"
            :options="refreshButtonOptions"
            hint="'Recargar información'"
          />

          <DxDataGridItem location="after" name="searchPanel"> </DxDataGridItem>
        </DxDataGridToolbar>

        <DxDataGridSelection mode="single" />
      </DxDataGrid>
    </div>
    <!-- <div class="conteo-tareas">
      <h2>
        ordenes pendientes:{{ totalTareas }} ordenes procesadas: {{ totalTareas24hrs }}
        <span style="text-align: center"><br />{{ totalTareas }}</span>
      </h2>
    </div> -->
<div class="conteo-tareas">
  <div class="metric-card-container">
    <div class="metric-card pending-card">
      <div class="card-icon">
        <svg viewBox="0 0 24 24">
          <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
          <path d="M12 8v4l3 3" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        </svg>
      </div>
      <div class="card-content">
        <div class="card-title">Ordenes Pendientes</div>
        <div class="card-value">{{ totalPendientes }}</div>
      </div>
    </div>

    <div class="metric-card processed-card">
      <div class="card-icon">
        <svg viewBox="0 0 24 24">
          <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" fill="none" stroke="currentColor" stroke-width="2"/>
          <polyline points="22 4 12 14.01 9 11.01" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        </svg>
      </div>
      <div class="card-content">
        <div class="card-title">Órdenes Cumplidas</div>
        <div class="card-value">{{ this.totalProcesadas }}</div>
      </div>
    </div>
  </div>
</div>

    <div>
      <DxChart
        id="chart"
        :data-source="dsListaOrdenesGrafica"
        title="Seguimiento ordenes por hora"
        :palette="['#008fbe', '#00c1d4', '#3357FF']"
      >
        <DxChartArgumentAxis
          :tick-interval="1"
          :label="{ format: { type: 'decimal' } }"
          :visual-range="{ startValue: 0, endValue: 23 }"
        />

        <DxChartSeries
          argument-field="Hora"
          value-field="Ordenes"
          name="Ordenes ingresadas"
          type="bar"
        />
        <DxChartSeries
          argument-field="Hora"
          value-field="Procesadas"
          name="Ordenes cumplidas"
          type="bar"
        />
        <DxChartTooltip
          :enabled="true"
          :customize-tooltip="customizeTooltip"
          location="edge"
          show-mode="always"
          :hide-event="{ name: 'none' }"
        />
        <DxChartLegend
          vertical-alignment="bottom"
          horizontal-alignment="center"
          :customize-items="customizeLegendItems"
        />
      </DxChart>
    </div>
  </div>
</template>
<script>
export default {

     

     components: {
        Temporizador: () => import('../../../components/sermesa/global/Temporizador.vue')    
    },
    
  data() {
    return {
      notificationOptions: {
        time: 4000,
        title: "Semaforo ordenes médicas",
        iconPack: "feather",
        icon: "icon-alert-circle",
        position: "top-center",
      },
      refreshButtonOptions: {
        icon: "refresh",
        text: "Refresh",
        onClick: () => {
          this.CargarSolicitudes();
        },
      },
      dsListaOrdenes: [],
      tiempoRestante: "02:00", // Inicia en 2 minutos
      totalProcesadas: 0,//calculo de total de ordenes durante las 24 hrs
      totalPendientes: 0, //calculo del total de ordenes pendientes
      segundos: 120,
      intervalo: null,
      temporizadorActivo:true,
      dsListaOrdenesGrafica:[],

    }; //return
  }, //data
  methods: {
    CargarSolicitudesGrafica() {
      this.dsListaOrdenesGrafica = [];
      this.axios
        .post("/app/v1_ExpedienteEvolucion/ListaSemaforoOrdenesMedicasGrafica", {})
        .then((resp) => {
          if(resp.data && resp.data.length>0){
            this.dsListaOrdenesGrafica=resp.data
              this.totalProcesadas = this.dsListaOrdenesGrafica.reduce((sum, item) => sum + item.Procesadas, 0);
              this.totalPendientes = this.dsListaOrdenesGrafica.reduce((sum, item) => sum + (item.Ordenes - item.Procesadas), 0);
          }
       
        });
   
    },

  CargarSolicitudes() {
  this.dsListaOrdenes = [];

  this.axios
    .post("/app/v1_ExpedienteEvolucion/ListaSemaforoOrdenesMedicas", {})
    .then((resp) => {
      if (resp.data && resp.data.length > 0) {
        this.dsListaOrdenes = resp.data;
         
      } else {
        this.mensajeActualizacion("No hay datos nuevos para actualizar.");
      }
      this.CargarSolicitudesGrafica();
    })
   
    .catch((error) => {
      this.mensajeActualizacion("Ocurrió un error al actualizar la información: " + error);

    });
},

    mensajeActualizacion(Mensaje) {
      this.$vs.notify({
        time: 4000,
        title: "Información actualizada",
        text: Mensaje,
        iconPack: "feather",
        icon: "icon-alert-circle",
        color: "success",
        position: "top-center",
      });
    },
    customRowStyle(e) {
      if (e.rowType === "data") {
        const index = e.rowIndex;
        // Alternar entre dos colores
        e.rowElement.style.backgroundColor =
          index % 2 === 0
            ? "#EAF3F5" // gris palido
            : "#ffffff"; // blanco
      }
    },


    customizeTooltip(pointInfo) {
      let textoBase = `${pointInfo.value} Órdenes médicas procesadas: <br>  a las  ${pointInfo.argument} Horas `;

      if (pointInfo.seriesName === "Ordenes") {
        textoBase = `📊 Ordenes médicas creadas: ${pointInfo.value}`;
      } else if (pointInfo.seriesName === "Procesadas") {
        textoBase = `🚀 Ordenes con seguimiento por enfermeria ${pointInfo.valueText}`;
      } else {
        textoBase = `ℹ️ Información general: ${pointInfo.seriesName} - ${pointInfo.valueText}`;
      }

      return { text: textoBase };
    },


    customizeLegendItems(items) {
      return items.map((item) => {
        if (item.text === "Ordenes") {
          item.marker.fill = "#33f3ff"; // Amarillo
        } else if (item.text === "Procesadas") {
          item.marker.fill = "#66a3ff"; // Azul
        }
        return item;
      });
    },
     TimeUp() {
     this.temporizadorActivo=false;
           this.segundos = 120;  
            this.CargarSolicitudes()
      this.$nextTick(()=>{this.temporizadorActivo=true} )
            //  this.$emit('onLoaded', this.iniciarCronometro)
        },
        calcularTotales() {
      
      this.totalProcesadas = this.dsListaOrdenesGrafica.reduce((sum, item) => sum + item.Procesadas, 0);
      this.totalPendientes = this.dsListaOrdenesGrafica.reduce((sum, item) => sum + (item.Ordenes - item.Procesadas), 0);
      
    }

  }, //methods fin

  mounted() {
    this.CargarSolicitudes(); //actualizar el grid al cargar la pagina
   

  },
  computed: {
    totalTareas() {
      return this.dsListaOrdenes.length;
    },
    totalTareas24hrs() {
      return this.dataSource.length;
    },

  },
};
</script>
<style scoped>
/* cambiar de color al pencabezado */
.exp029-container :deep(.dx-header-row) {
  background-color: #008fbe !important;
  color: rgb(240, 236, 236) !important;
  font-weight: bold;
}
.exp029-container .grid-container {
  display: flex;
  justify-content: center; /* Centrar horizontalmente */
  padding: 0 150px; /* Espacio de 50px a la derecha e izquierda */
}
.dx-data-grid {
  width: 60%; /* Definir el tamaño del grid */
  min-width: 500px; /* Asegurar que no sea demasiado pequeño */
}
.header {
  text-align: center;
  padding: 10px;
  background-color: #f8f9fa;
}

.refresh-info {
  margin-top: 10px;
  display: flex;
  justify-content: center; /* Centra el span */
  align-items: center;
}

.refresh-info span {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  display: block; /* Asegura que se muestre correctamente */
}
.exp029-container .conteo-tareas {
  padding: 0 150px; /* Espacio de 50px a la derecha e izquierda */
}
conteo-tareas h2 {
  font-size: 20px;
  color: #333;
}

.conteo-tareas span {
  font-weight: bold;
  color: #ff5733;
}
/* estilos para el conteo de tareas*/
.conteo-tareas {
  --pending-color: #FFA000;
  --processed-color: #4CAF50;
  --text-dark: #333333;
  --text-medium: #666666;
  --shadow: 0 4px 12px rgba(0,0,0,0.08);
  font-family: system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
  padding: 1rem;
}

.metric-card-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1.25rem;
  justify-content: center;
}

.metric-card {
  flex: 1;
  min-width: 250px;
  background: white;
  border-radius: 0.75rem;
  padding: 1.25rem;
  box-shadow: var(--shadow);
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-0.25rem);
  box-shadow: 0 8px 16px rgba(0,0,0,0.12);
}

.card-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}

.pending-card .card-icon {
  background-color: rgba(255, 160, 0, 0.15);
  color: var(--pending-color);
}

.processed-card .card-icon {
  background-color: rgba(76, 175, 80, 0.15);
  color: var(--processed-color);
}

.card-icon svg {
  width: 1.5rem;
  height: 1.5rem;
}

.card-content {
  display: flex;
  flex-direction: column;
}

.card-title {
  font-size: 0.875rem;
  color: var(--text-medium);
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.card-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-dark);
}

@media (max-width: 600px) {
  .metric-card {
    min-width: 100%;
  }
}
.exp029-container .cronometro {
  display: flex; /* Alinea los elementos en la misma línea */
  align-items: center; /* Centra verticalmente */
  justify-content: center; /* Centra horizontalmente */
  text-align: center; /* Asegura que el texto dentro se centre */
  width: 100%; /* Usa toda la pantalla si es necesario */
}

</style>
