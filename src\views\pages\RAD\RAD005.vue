<template>
<vx-card title="Fusión de Estudios">
    <!-- <buscador ref="buscar_radiologias" buscador_titulo="Buscador / Radiología" :api="'app/radiologia/RadBusquedaOrdenes'" :campos="['Apellido','Nombre','Orden','Admision','Fecha','Nacimiento']" :titulos="['Apellidos','Nombres','Orden','Admisión','Fecha#','Nacimiento#']" :titulos_mascara="{'Orden':'AAX-#########','Admisión':'X##########'}" :titulos_grupos="[['Apellidos*','Nombres*'],['Orden','Admisión']]" :multiselect="false" :api_validar_campo="true" :api_preload="false" />-->
    <buscador ref="buscar_radiologias" buscador_titulo="Buscador / Radiología" :api="'app/radiologia/RadBusquedaOrdenes'" :campos="['Apellido','Nombre','Orden','Admision','Fecha','Nacimiento']" :titulos="['Apellidos','Nombres','Orden','Admisión','Fecha#','Nacimiento#']" :titulos_mascara="{'Orden':'AAX-#########','Admisión':'X##########'}" :titulos_grupos="[['Apellidos','Nombres'],['Orden','Admisión']]" :multiselect="false" :api_validar_campo="true" :api_preload="false" />


    <buscador ref="buscar_especialista" buscador_titulo="Buscador / Especialista" :api="'app/radiologia/RadBusquedaradiologos'" :campos="['Codigo','ApellidoRad','NombreRad']" :titulos="['Especialista','Apellidos','Nombres']" :multiselect="false" :api_validar_campo="true" />

    <buscador ref="buscar_plantillas" buscador_titulo="Buscador / Radiología" :api="'app/radiologia/RadBusquedaPlantillas'" :api_filtro="{producto
        :info.opcion.examen_codigo}" :campos="['Tipo','TitutoInforme']" :titulos="['Tipo','Nombre']" :multiselect="false" :api_validar_campo="true" />

    <form>
        <vs-divider>Orden</vs-divider>
        <form v-on:submit.prevent="cargar_orden()">
            <div class="flex flex-wrap">
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <vs-input label="Tipo" class="w-full" v-on:change="info.orden_tipo=info.orden_tipo.toUpperCase()" v-model="info.orden_tipo" :disabled="bloqueoBusqueda" />
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <!-- <vs-input label="Número" class="w-full" type="number" v-model="info.orden_numero" /> -->
                    <label class="vs-input--label">Número</label>
                    <vx-input-group class="">
                        <vs-input v-model="info.orden_numero" :disabled="bloqueoBusqueda" @change="cargar_orden()" />
                        <template slot="append">
                            <div class="append-text btn-addon">
                                <button type="submit" v-show="false" name="button"></button>
                                <vs-button id="button-with-loading-1" color="primary" icon-pack="feather" @click="buscar_radiologias()" icon="icon-search" v-if="bloqueoBusqueda==false"></vs-button>
                                <vs-button id="button-with-loading-2" color="danger" icon-pack="feather" @click="limpiar_orden()" icon="icon-x" v-else></vs-button>
                                <!-- <vs-button style="margin-left:1px" v-on:click="cargar_scanner()" v-if="bloqueoBusqueda" icon-pack="fas" icon="fa-image"></vs-button> -->
                            </div>
                        </template>
                    </vx-input-group>
                </div>

            </div>
        </form>
        <div class="flex flex-wrap">
            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <vs-input label="Admisión" class="w-full" v-model="info.admision" disabled />
            </div>
            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <vs-input label="Cód. Paciente" class="w-full" v-model="info.paciente_codigo" disabled />
            </div>
            <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                <vs-input label="Nombre del paciente" class="w-full" v-model="info.paciente_nombre" disabled />
            </div>
            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <label class="vs-input--label">Edad</label>
                <vx-input-group class="">

                    <vs-input class="w-full" v-model="info.paciente_edad" disabled />
                    <!-- {{info}} -->
                    <template slot="append">
                        <div class="append-text btn-addon">
                            <vs-button v-if="$validar_privilegio('EDITAREDAD').status && bloqueoBusqueda" id="button-with-loading-3" color="primary" icon-pack="feather" @click="editar_edad()" icon="icon-edit"></vs-button>
                        </div>
                    </template>
                </vx-input-group>
            </div>
        </div>
        <div class="flex flex-wrap">
            <div class="w-full">
                <label class="vs-input--label">Refiere</label>
                <vx-input-group class="">

                    <vs-input class="w-full" v-model="info.refiere" disabled />
                    <!-- {{info}} -->
                    <template slot="append">
                        <div class="append-text btn-addon">
                            <vs-button v-if="$validar_privilegio('EDITARMEDICO').status && bloqueoBusqueda" id="button-with-loading-4" color="primary" icon-pack="feather" @click="editar_medico_orden()" icon="icon-edit"></vs-button>
                        </div>
                    </template>
                </vx-input-group>
            </div>
        </div>

        <vs-divider>Estudio</vs-divider>
        <vs-table :data="examenes" v-if="examenes.length>0">
            <template slot="thead">
                <vs-th>Examen</vs-th>
                <vs-th>Especialista</vs-th>
                <vs-th>Estado</vs-th>
                <vs-th width="50px">Acciones</vs-th>
            </template>
            <template slot-scope="{data}">
                <vs-tr :key="indextr" v-for="(tr, indextr) in data">
                    <vs-td :data="tr.examen_nombre" edit style="padding:0">
                        <div style="padding:10px;">
                            [{{ tr.examen_codigo }}] {{ tr.examen_nombre }}
                        </div>
                    </vs-td>
                    <vs-td :data="tr.especialista_nombre">
                        <span v-if="tr.especialista_codigo">[{{ tr.especialista_codigo }}] {{ tr.especialista_nombre }}</span>
                    </vs-td>
                    <vs-td>
                        <vx-tooltip :text="(tr.congelado==false)?'Descongelado':'Congelado'" style="display:inline-block;text-align:center;margin-right:2px;width:20px" position="bottom">
                            <i v-if="tr.congelado" style="color:#2ecc71" class="fas fa-lock"></i>
                            <i v-else style="color:#e74c3c" class="fas fa-lock-open"></i>
                        </vx-tooltip>
                        <vx-tooltip :text="(tr.sms==false)?'Entrega Personalmente':'Envío SMS'" style="display:inline-block;text-align:center;margin-right:2px;width:20px" position="bottom">
                            <i v-if="tr.sms" style="color:#2ecc71" class="fas fa-comment"></i>
                            <i v-else style="color:#e74c3c" class="fas fa-comment-slash"></i>
                        </vx-tooltip>
                    </vs-td>
                    <vs-td>
                        <vs-button :disabled="tr.flagOrden==0" color="primary" icon-pack="feather" icon="icon-link" style="display:inline-block;margin-right:2px" @click="opciones(tr)"></vs-button>
                    </vs-td>
                </vs-tr>
            </template>
        </vs-table>
    </form>
    <vs-divider />
</vx-card>
</template>

<script>
export default {
    data() {
        return {
            popupActive2: false,
            activeItem: 0,
            bloqueoBuscar: false, //utlizado para evitar llamar multiples llamadas a los registros
            bloqueoBusqueda: false,
            bloqueoCampos: true,
            active: false,
            switch2: false,
            examenes: [],
            listado_imagenes: [],
            info: {
                opcion: {
                    show: false, //mostrar las opciones del examen_nombre
                    examen_codigo: '',
                    examen_nombre: '',
                    examen_anterior: '',
                    especialista_codigo: '',
                    especialista_nombre: '',
                    especialista_anterior: '',
                    medico_codigo: '',
                    medico_nombre: '',
                    medico_anterior: '',
                    congelado: false,
                    sms: true,
                    observaciones: '',
                    admision: '',
                    informe: '',
                    informe_cambio: false,
                    hospital: '',
                    nacimiento: null
                },
                orden_tipo: null,
                orden_numero: null,
                paciente_codigo: null,
                paciente_nombre: '',
                paciente_edad: '',
                refiere_codigo: null,
                refiere: '',
                reporte: false,
                mostrar_imagenes: false,
                mostart_imagenes_carga: false,
                reporte_src: null,
                editarmedico: false,
                editaredad: false,
                edad: null,
                edadFormato: "yyyy-MM-dd"
            },
            validar_descripcion: false,
            validar_problema: false,
            category_choices: [{
                text: 1,
                value: 1
            }],
            customToolbar: [
                [{
                    'header': [1, 2, 3, 4, 5, 6, false]
                }],
                [{
                    'font': []
                }],
                ['bold', 'italic', 'underline', 'strike'],
                [{
                    list: "ordered"
                }, {
                    list: "bullet"
                }],
                [{
                    'indent': '-1'
                }, {
                    'indent': '+1'
                }],
                [{
                    'color': []
                }, {
                    'background': []
                }],
            ],

            scanner_img: [],
            scanner_path: '',

            actualizar_path: '',
        }
    },
    watch: {
        'info.opcion.informe'() {
            this.info.opcion.informe_cambio = true
        }
    },
    methods: {

        cargar_orden() {
            // if (this.bloqueoBuscar == true) return false;

            if (!(this.info.orden_numero > 0)) {
                this.info.orden_numero = null
                return false
            }

            this.bloqueoBuscar = true;

            

            this.axios.post('/app/radiologia/radordenes', {
                    tipo: this.info.orden_tipo,
                    orden: this.info.orden_numero
                })
                .then(resp => {
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        this.info.hospital = resp.data.json[0].Hospital
                        this.info.paciente_codigo = resp.data.json[0].Paciente
                        this.info.paciente_nombre = resp.data.json[0].NombrePaciente
                        this.info.paciente_edad = resp.data.json[0].Edad + ' ' + resp.data.json[0].EdadMedida
                        this.info.refiere = resp.data.json[0].NombreMedico
                        this.info.refiere_codigo = resp.data.json[0].Medico
                        this.info.nacimiento = resp.data.json[0].Nacimiento
                        this.info.observaciones = resp.data.json[0].Observaciones
                        this.info.admision = resp.data.json[0].SerieAdmision + '' + resp.data.json[0].Admision

                        this.cargar_estudios()
                        this.cargar_path()

                    } else {
                        this.$vs.dialog({
                            color: 'danger',
                            title: 'Radiología - Error',
                            text: (resp.data.codigo < 0) ? resp.data.descripcion : 'Sin Registros',
                        })
                        
                        return false
                    }
                })
                .catch(() => {
                    // console.log(err)
                    
                    this.bloqueoBuscar = false;
                    this.$vs.dialog({
                        color: 'danger',
                        title: 'Radiología - Error',
                        text: 'Error al obtener datos de la orden',
                    })
                })
        },

        cargar_estudios() {
            this.axios.post('/app/radiologia/radestudios', {
                    tipo: this.info.orden_tipo,
                    orden: this.info.orden_numero
                })
                .then(resp => {

                    this.examenes = []
                    resp.data.json.map(data => {
                        this.examenes.push({
                            examen_linea: data.LineaOrden,
                            examen_codigo: data.Producto,
                            examen_nombre: data.Nombre,
                            especialista_codigo: data.Radiologo,
                            especialista_anterior: data.Radiologo,
                            especialista_nombre: data.NombreMedico,
                            flagOrden: data.FlagOrden,
                            flagImagen: data.FlagImagen,
                            congelado: (data.InformeLock.toLowerCase() == "s") ? true : false,
                            sms: !data.EntregarPersonalmente,
                            informe: data.Detalle
                        })
                    })
                    
                    this.bloqueoBuscar = false;
                    this.bloqueoBusqueda = true
                    // this.bloqueoCampos = false
                })
        },

        cargar_path() {
            this.axios.post('/app/radiologia/RadObtieneRuta', {
                    categoria: this.info.orden_tipo,
                    sucursal: this.info.hospital,
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.scanner_path = resp.data.json[0].Path
                    }

                    return this.axios.post('/app/radiologia/RadObtieneRuta', {
                        categoria: 'ACTUALIZA',
                        sucursal: this.info.hospital,
                    })
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.actualizar_path = resp.data.json[0].Path
                    }

                })
        },

        buscar_radiologias() {
            this.$refs.buscar_radiologias.iniciar((data) => {
                // console.log(data)
                if (data != null) {
                    this.info.orden_tipo = data.Orden.split('-')[0]
                    this.info.orden_numero = data.Orden.split('-')[1]
                    this.cargar_orden()
                }
            })
        },

        limpiar_orden() {
            this.examenes = []
            this.info.opcion.congelar = false
            this.info.opcion.sms = false
            this.info.opcion.medico_anterior = null
            this.info.opcion.medico_codigo = null
            this.info.opcion.medico_nombre = null
            this.info.orden_tipo = null
            this.info.orden_numero = null
            this.info.paciente_codigo = null
            this.info.paciente_nombre = ''
            this.info.paciente_edad = ''
            this.info.examen_codigo = ''
            this.info.examen_nombre = ''
            this.info.examen_linea = ''
            this.info.admision = ''
            this.info.especialista_codigo = ''
            this.info.especialista_nombre = ''
            this.info.refiere = ''
            this.info.refiere_codigo = null
            this.bloqueoBusqueda = false
            this.bloqueoCampos = true
            this.info.informe = ''
            this.info.reporte = false
            this.info.reporte_src = null
        },
        opciones(op) {
            return this.axios.post('/app/radiologia/RadActualizaSynapse', {
                    Tipo: this.info.orden_tipo,
                    Orden: this.info.orden_numero,
                    LineaOrden: op.examen_linea
                })
                .then(() => {
                    this.cargar_estudios()
                })
        },
        guardar_informe(recargar, callback) {
            // return new Promise((resolve) => {
            let descripciones = []
            if (this.info.opcion.examen_anterior != this.info.opcion.examen_nombre) descripciones.push('Nombre de Examen: ' + this.info.opcion.examen_anterior + ' > ' + this.info.opcion.examen_nombre)
            if (this.info.opcion.especialista_codigo != this.info.opcion.especialista_anterior) descripciones.push('Especialista: ' + this.info.opcion.especialista_anterior + ' > ' + this.info.opcion.especialista_codigo)
            if (this.info.opcion.informe_cambio) descripciones.push('Modificación de Informe de Radiología')
            // console.log(descripciones)

            let Bitacora_ = (descripciones.length > 0) ? [{
                Empresa: 'MED',
                Serie: this.info.orden_tipo,
                Documento: this.info.orden_numero,
                Linea: this.info.opcion.linea_orden,
                Tipo: "Radiologia",
                Descripciones: descripciones
            }] : null

            // console.log(Bitacora_)

            return this.axios.post('/app/radiologia/RadActualizaEstudio', {
                    Tipo: this.info.orden_tipo,
                    Orden: this.info.orden_numero,
                    LineaOrden: this.info.opcion.linea_orden,
                    NombreEstudio: this.info.opcion.examen_nombre,
                    CodRadiologo: (this.info.opcion.especialista_codigo > 0) ? this.info.opcion.especialista_codigo : 0,
                    Informe: this.info.opcion.informe,
                    Bitacora: Bitacora_
                })
                .then(() => {
                    if (recargar) {
                        this.cargar_estudios()
                        this.info.opcion.show = false;
                    }
                    if (callback) callback(true)
                })
                .catch(() => {
                    if (callback) callback(false)
                })
            // })

        },

    },
    mounted() {

    },
    created() {

    }
}
</script>

<style>
.button {
    margin: 0px !important;
}

.btnx {
    margin-left: 10px !important;
    background: red;
    border-radius: 5px 0px 0px 5px;
}

.btn-drop {
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: red;

}

.menu-funcionalidad {
    /* padding-top:0 !important; */
    /* box-shadow: 0 0 5px rgba(0, 0, 0, 0.3); */
    /* padding: 15px; */
    display: inline;
    float: right;
    border-radius: 10px;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 10px;
}

.menu-funcionalidad a {
    text-align: center;
    padding-right: 10px;
}

.menu-funcionalidad i {
    font-size: 20px;
}

.vs-switch {
    height: 30px;
    width: 60px !important;
}

.vs-circle-switch {
    height: 25px;
    width: 25px;
}

.vs-switch--icon {
    font-size: 15px
}

.vs-switch.vs-switch-active .vs-switch--circle {
    margin-left: calc(100% - 30px);
}

.popup-generar {
    height: 100%
}
</style>
