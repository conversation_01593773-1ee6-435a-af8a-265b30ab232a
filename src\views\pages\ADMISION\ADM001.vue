<template>
<vx-card title="Mantenimiento de Admisiones">
    
    <vs-divider  position="left">Admisiones</vs-divider>
    
    <div class="flex flex-wrap">
        <div class="w-full px-2 py-2  md:w-1/3 lg:w-1/3 xl:w-1/3" v-if="permiso.admision_crear">
            <vx-card title="Nueva Admisión Diagnostico" title-color="primary" subtitle="Registro de nuevas admisiones">
                <router-link :to="{path:'/ADMISION/ADM002',params: {}}">
                    <vs-button color="primary" icon-pack="feather" icon="icon-link">Abrir</vs-button>
                </router-link>
            </vx-card>
        </div>
        <div class="w-full px-2 py-2  md:w-1/3 lg:w-1/3 xl:w-1/3" v-if="permiso.admision_crear">
            <vx-card title="Nueva Admisión Hospitalaria" title-color="primary" subtitle="Registro de nuevas admisiones">
                <router-link :to="{path:'/ADMISION/ADM003',params: {}}">
                    <vs-button color="primary" icon-pack="feather" icon="icon-link">Abrir</vs-button>
                </router-link>
            </vx-card>
        </div>
        <div class="w-full px-2 py-2  md:w-1/3 lg:w-1/3 xl:w-1/3" v-if="permiso.admision_buscar">
            <vx-card title="Buscar Admisión" title-color="primary" subtitle="Buscar admisiones">
                <router-link :to="{path:'/ADMISION/ADM004',query: {nuevo:false}}">
                    <vs-button color="primary" icon-pack="feather" icon="icon-link">Abrir</vs-button>
                </router-link>
            </vx-card>
        </div>
    </div>
    <vs-divider position="left">Mantenimiento Admisiones</vs-divider>
    <div class="flex flex-wrap">
        <div class="w-full px-2 py-2  md:w-1/3 lg:w-1/3 xl:w-1/3" v-if="permiso.aceptar_hospitalizacion">
            <vx-card title="Aceptar Hospitalización" title-color="primary" subtitle="Formularios de Admisiones">
                <router-link :to="{path:'/ADMISION/ADM007',query: { nuevo:false }}">
                    <vs-button color="primary" icon-pack="feather" icon="icon-link">Abrir</vs-button>
                </router-link>
            </vx-card>
        </div>
        <div class="w-full px-2 py-2  md:w-1/3 lg:w-1/3 xl:w-1/3" v-if="permiso.recalculo_admision">
            <vx-card title="Recalculo de Admisión" title-color="primary" subtitle="Cambio de poliza">
                <router-link :to="{path:'/ADMISION/ADM008',query: { nuevo:false }}">
                    <vs-button color="primary" icon-pack="feather" icon="icon-link">Abrir</vs-button>
                </router-link>
            </vx-card>
        </div>
        <div class="w-full px-2 py-2  md:w-1/3 lg:w-1/3 xl:w-1/3" v-if="permiso.traslados">
            <vx-card title="Traslados" title-color="primary" subtitle="Cambio de poliza">
                <router-link :to="{path:'/ADMISION/ADM009',query: { nuevo:false }}">
                    <vs-button color="primary" icon-pack="feather" icon="icon-link">Abrir</vs-button>
                </router-link>
            </vx-card>
        </div>
        <div class="w-full px-2 py-2  md:w-1/3 lg:w-1/3 xl:w-1/3" v-if="permiso.revisar_edad">
            <vx-card title="Revisar Edad" title-color="primary" subtitle="Cambio de Edad del Paciente">
                <router-link :to="{path:'/ADMISION/ADM010',query: { nuevo:false }}">
                    <vs-button color="primary" icon-pack="feather" icon="icon-link">Abrir</vs-button>
                </router-link>
            </vx-card>
        </div>
    </div>
    <vs-divider position="left">Pacientes</vs-divider>
    <div class="flex flex-wrap">
        <div class="w-full px-2 py-2  md:w-1/3 lg:w-1/3 xl:w-1/3" v-if="permiso.nuevo_paciente">
            <vx-card title="Nuevo Paciente" title-color="primary" subtitle="Registro de nuevos pacientes">
                <router-link :to="{path:'/ADMISION/ADM003',params: {  }}">
                    <vs-button color="primary" icon-pack="feather"  icon="icon-link">Abrir</vs-button>
                </router-link>
            </vx-card>
        </div>
        <div class="w-full px-2 py-2  md:w-1/3 lg:w-1/3 xl:w-1/3" v-if="permiso.buscar_paciente">
            <vx-card title="Buscar Paciente" title-color="primary" subtitle="Buscar pacientes">
                <router-link :to="{path:'/ADMISION/ADM003',params: {  }}">
                    <vs-button color="primary" icon-pack="feather"  icon="icon-link">Abrir</vs-button>
                </router-link>
            </vx-card>
        </div>
        <div class="w-full px-2 py-2  md:w-1/3 lg:w-1/3 xl:w-1/3" v-if="permiso.facturacion">
            <vx-card title="Ficha Paciente" title-color="primary" subtitle="Ficha general del paciente">
                <router-link :to="{path:'/ADMISION/ADM004',params: {  }}">
                    <vs-button color="primary" icon-pack="feather"  icon="icon-link">Abrir</vs-button>
                </router-link>
            </vx-card>
        </div>
    </div>

    <vs-divider position="left">Otros</vs-divider>
    <div class="flex flex-wrap">
        <div class="w-full px-2 py-2  md:w-1/3 lg:w-1/3 xl:w-1/3" v-if="permiso.omision_egreso">
            <vx-card title="Omisión de Egreso" title-color="primary" subtitle="Buscar datos de facturación">
                <router-link :to="{path:'/ADMISION/ADM011',params: {  }}">
                    <vs-button color="primary" icon-pack="feather"  icon="icon-link">Abrir</vs-button>
                </router-link>
            </vx-card>
        </div>
        <div class="w-full px-2 py-2  md:w-1/3 lg:w-1/3 xl:w-1/3" v-if="permiso.anulacion_preegreso">
            <vx-card title="Anulación PreEgreso" title-color="primary" subtitle="Buscar datos de facturación">
                <router-link :to="{path:'/ADMISION/ADM012',params: {  }}">
                    <vs-button color="primary" icon-pack="feather"  icon="icon-link">Abrir</vs-button>
                </router-link>
            </vx-card>
        </div>
    </div>
    
</vx-card>
</template>

<script>
export default {
    data() {
        return {
            // todos los permisos deben de empezar en falso
            permiso:{
                admision_crear:false,
                admision_buscar:false,
                aceptar_hospitalizacion:true,
                recalculo_admision:true,
                traslados:true,
                revisar_edad:true,
                nuevo_paciente:true,
                buscar_paciente:true,
                facturacion:true,
                omision_egreso:true,
                anulacion_preegreso:true,
            }
        }
    },
    computed:{
        
    },
    mounted() {
        // validacion de permisos
        this.$validar_funcionalidad('/ADMISION/ADM002','CREAR',(d)=>{this.permiso.admision_crear=d.status})
        this.permiso.admision_buscar = this.$validar_funcionalidad('/ADMISION/ADM002').status
    }
}
</script>

<style>
.button {
    margin: 0px !important;
}

.btnx {
    margin-left: 10px !important;
    background: red;
    border-radius: 5px 0px 0px 5px;
}

.btn-drop {
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: red;

}

.menu-funcionalidad {
    /* padding-top:0 !important; */
    /* box-shadow: 0 0 5px rgba(0, 0, 0, 0.3); */
    /* padding: 15px; */
    display: inline;
    float: right;
    border-radius: 10px;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 10px;
}

.menu-funcionalidad a {
    text-align: center;
    padding-right: 10px;
}

.menu-funcionalidad i {
    font-size: 20px;
}

.vs-switch {
    height: 30px;
    width: 60px !important;
}

.vs-circle-switch {
    height: 25px;
    width: 25px;
}

.vs-switch--icon {
    font-size: 15px
}

.vs-switch.vs-switch-active .vs-switch--circle {
    margin-left: calc(100% - 30px);
}

.popup-generar {
    height: 100%
}

select {
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #ccc;
}
</style>
