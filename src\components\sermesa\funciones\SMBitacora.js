
let tipo = null
let temporal = null
let valorReal = null
let _key = null
const bitacora = {
    registrar(valor,key) {
        tipo = typeof valor

        // Validando tratamiento del valor
        if (tipo == 'object') {
            temporal = { ...valor }
        }
        _key = key
        // Clonando valor
        valorReal = valor
    },

    obtener() {
        let resp = {}
        // Validando respuesta
        if (tipo == 'object') {
            Object.entries(valorReal).forEach((item) => {
                const nombreObj = item[0]
                if (valorReal[nombreObj] != temporal[nombreObj]) {
                    resp[nombreObj] = temporal[nombreObj] + ' =>> ' + valorReal[nombreObj]
                }
            })
        }
        return (Object.keys(resp).length > 0) ? JSON.stringify({..._key,info:{...resp}}) : null
    }
}

export default bitacora