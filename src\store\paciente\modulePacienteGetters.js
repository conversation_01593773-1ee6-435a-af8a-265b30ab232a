
// import { getField, updateField } from "vuex-map-fields";




// import { createHelpers } from 'vuex-map-fields';

// Because by default, getters and mutations in Vuex
// modules, are globally accessible and not namespaced,
// you most likely want to rename the getter and mutation
// helpers because otherwise you can't reuse them in multiple,
// non namespaced modules.
// import { createHelpers } from 'vuex-map-fields';
// const { getField } = createHelpers({
//     getterType: 'getField',  
// });

import { getField } from "vuex-map-fields";

//export  default {
const getters ={
    getField,
    getPaciente: state => {
        return state.PacienteData
    },
    getCodigoPaciente: state => {
        //console.log('getCodigoPaciente' + ' ' + state.PacienteData.Codigo)
        return state.PacienteData.Codigo
    }
}

export default {
    getters
}