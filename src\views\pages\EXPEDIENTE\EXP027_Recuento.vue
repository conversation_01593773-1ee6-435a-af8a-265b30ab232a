<template>
<div id="Recuento">
    <div v-if="(consulta === true && hojaAnestesia !== null && hojaAnestesia !== '' && registrosRecuento !== null && registrosRecuento.length !== 0) || (consulta === false)">
        <div class="p-2">

            <DxForm labelMode="floating" :form-data.sync="registro">
                <DxGroupItem item-type="group" :col-count="3" v-if="consulta">
                    <DxEmptyItem />
                    <DxGroupItem item-type="group">
                        <DxItem data-field="RegistroRecuento" editor-type="dxSelectBox" :editor-options="{ items: registrosRecuento, onItemClick: this.getDetalleRegistro }">
                            <DxLabel :text="'Registro recuento'" />
                        </DxItem>
                        <DxButtonItem :button-options="pdfButtonOptions" horizontal-alignment="center" verical-alignment="center"/>
                    </DxGroupItem>
                    <DxEmptyItem />
                </DxGroupItem>
            </DxForm>

            <DxDataGrid :ref="dataGridRefKey" v-bind="DefaultDxGridConfiguration" :data-source="recuento" :headerFilter="{visible:false, allowSearch: false }" :searchPanel="{visible:false}" :sorting="{mode: 'none'}" :paging="{ enabled:false}" :width="'100%'" :height="'100%'" @editor-preparing="editorDatagrid" @cell-prepared="onCellPrepared">
                <DxSelection mode="single" />

                <DxColumn :width="200" data-field="Material" :disabled="consulta" caption="" alignment="center" :customize-text="customizeText" />
                <DxColumn :width="100" data-field="Inicio" :disabled="consulta" alignment="center" data-type="number" :editor-options="{ min: 0 }" />
                <DxColumn :width="100" data-field="Adicionales" :disabled="consulta" alignment="center" data-type="number" :editor-options="{ min: 0 }" />
                <DxColumn :width="100" data-field="Recuento" :disabled="consulta" alignment="center" data-type="number" :editor-options="{ min: 0 }" />
                <DxColumn :width="100" data-field="Faltantes" :disabled="consulta" alignment="center" :calculate-cell-value="calcularFaltantes" />
                <DxColumn :width="200" data-field="Comentarios" :disabled="consulta" alignment="center" />
                <DxEditing :allow-updating="!consulta" :allow-adding="false" :allow-deleting="false" :confirmDelete="false" mode="cell" />
            </DxDataGrid>
            <form @submit="handleSubmit">
                <DxForm :form-data.sync="encabezado" labelMode="floating" :read-only="consulta" :ref="dataEncabezadoRefKey">
                    <DxGroupItem v-if="!consulta" item-type="group" :col-count="4">
                        <DxItem data-field="Anestesiologo" :is-required="true" editor-type="dxTextBox" :editor-options="{ maxLength: 1000, onFocusIn: this.validarUsuario }">
                            <DxLabel :text="'F. Anestesiólogo'" />
                        </DxItem>
                        <DxItem data-field="Instrumentista" :is-required="true" editor-type="dxTextBox" :editor-options="{ maxLength: 1000, onFocusIn: this.validarUsuario }">
                            <DxLabel :text="'F. Instrumentista'" />
                        </DxItem>
                        <DxItem data-field="Circulante" :is-required="true" editor-type="dxTextBox" :editor-options="{ maxLength: 1000, onFocusIn: this.validarUsuario }">
                            <DxLabel :text="'F. Circulante'" />
                        </DxItem>
                        <DxGroupItem v-if="!consulta" item-type="group">
                            <DxButtonItem :button-options="submitButtonOptions" horizontal-alignment="center" verical-alignment="center" />
                            <DxButtonItem :button-options="clearButtonOptions" horizontal-alignment="center" verical-alignment="center" />
                        </DxGroupItem>
                    </DxGroupItem>
                    <DxGroupItem v-if="consulta" item-type="group" :col-count="3">
                        <DxItem data-field="NombreAnestesiologo" editor-type="dxTextBox">
                            <DxLabel :text="'Anestesiólogo'" />
                        </DxItem>
                        <DxItem data-field="NombreInstrumentista" editor-type="dxTextBox">
                            <DxLabel :text="'Instrumentista'" />
                        </DxItem>
                        <DxItem data-field="NombreCirculante" editor-type="dxTextBox">
                            <DxLabel :text="'Circulante'" />
                        </DxItem>
                    </DxGroupItem>
                </DxForm>
            </form>
        </div>
    </div>
    <vs-row id="texto" class="p-2" v-if="(hojaAnestesia === '' || hojaAnestesia === null) && consulta === true">
        <p>Seleccione una fecha para mostrar información</p>
    </vs-row>
    <vs-row id="texto" class="p-2" v-if="(registrosRecuento.length === 0 || registrosRecuento === null ) && consulta === true && hojaAnestesia !== '' && hojaAnestesia !== null">
        <p>La hoja seleccionada no cuenta con información para mostrar</p>
    </vs-row>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'
import {
    DxDataGrid,
    DxColumn,
    DxSelection,
    DxEditing,
} from 'devextreme-vue/data-grid'
import {
    DxForm,
    DxItem,
    DxLabel,
    DxGroupItem,
    DxButtonItem,
    DxEmptyItem
} from 'devextreme-vue/form'

const dataGridRefKey = 'grid'
const dataEncabezadoRefKey = 'encabezado-form'

export default {
    name: 'RecuentoMateriales',
    components: {
        DxDataGrid,
        DxColumn,
        DxSelection,
        DxEditing,
        DxForm,
        DxItem,
        DxLabel,
        DxGroupItem,
        DxButtonItem,
        DxEmptyItem
    },
    data() {
        return {
            DefaultDxGridConfiguration,
            dataEncabezadoRefKey,
            dataGridRefKey,
            recuento: [],
            encabezado: {
                Anestesiologo: '',
                Instrumentista: '',
                Circulante: '',
                NombreAnestesiologo: '',
                NombreInstrumentista: '',
                NombreCirculante: ''
            },
            submitButtonOptions: {
                text: 'Guardar datos',
                type: 'success',
                icon: 'save',
                width: 200,
                useSubmitBehavior: true,
                disabled: false
            },
            clearButtonOptions: {
                text: 'Limpiar registro',
                type: 'danger',
                icon: 'trash',
                width: 200,
                useSubmitBehavior: false,
                disabled: false,
                onClick: this.limpiarTiempo
            },
            pdfButtonOptions: {
                text: 'Generar PDF',
                type: 'danger',
                icon: 'fas fa-file-pdf',
                useSubmitBehavior: false,
                disabled: true,
                onClick: this.generarPDF
            },
            registro: []
        }
    },
    props: {
        SerieAdmision: null,
        CodigoAdmision: null,
        NumeroExpediente: null,

        StatusAdmision: null,
        consulta: null, //Variable para indicar si el componente se usará para consulta -> 'true' o para ingreso -> 'false'
        hojaAnestesia: null, //Variable para obtener la hoja cuando se utilice para consulta

        registrosRecuento: null
    },
    methods: {
        handleSubmit(e) {
            e.preventDefault()
            if (this.SerieAdmision && this.CodigoAdmision) {
                let postData = {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    Recuento: this.mapRecuento()
                }
                this.axios.post("/app/v1_ExpedienteEvolucion/InsertarRecuentoCompresas", {
                    ...postData
                }).then(() => {

                })
            }
        },
        getMateriales() {
            this.axios.post('/app/v1_ExpedienteEvolucion/CatalogoMaterialesCompresa', {})
                .then(resp => {
                    this.recuento = resp.data.json.map((x) => {
                        return {
                            Material: {
                                ...x
                            }
                        }
                    })
                })
        },
        getDetalleRegistro() {
            this.axios.post('/app/v1_ExpedienteEvolucion/BuscarRecuentoCompresas', {
                    IdHoja: this.hojaAnestesia,
                    IdRecuento: this.registro.RegistroRecuento,
                })
                .then(resp => {
                    this.recuento = resp.data.json.map((x) => {
                        this.encabezado.NombreAnestesiologo = x.Anestesiologo
                        this.encabezado.NombreCirculante = x.Circulante
                        this.encabezado.NombreInstrumentista = x.Instrumentista

                        return {
                            Material: x.Descripcion,
                            Inicio: x.Inicio,
                            Adicionales: x.Adicionales,
                            Recuento: x.Recuento,
                            Comentarios: x.Comentarios
                        }
                    })
                    this.pdfButtonOptions = {
                        text: 'Generar PDF',
                        type: 'danger',
                        icon: 'fas fa-file-pdf',
                        useSubmitBehavior: false,
                        disabled: false,
                        onClick: this.generarPDF
                    }
                })
        },
        editorDatagrid(e) {
            if (e.parentType === 'dataRow' && e.dataField === 'Material') {
                e.editorOptions.disabled = true
                e.editorOptions.value = e.value.Descripcion
            }
            if (e.dataField === 'Inicio' || e.dataField === 'Adicionales' || e.dataField === 'Recuento' || e.dataField === 'Faltantes') {
                e.editorOptions.onKeyDown = this.onlyNumbers
            }
        },
        onCellPrepared(e) {
            if (e.rowType === 'data' && e.column.dataField === 'Material') {
                e.cellElement.style.cssText = "background-color: #fbf1e6; text-align: center; font-weight: bold; font-size: 16px;";
            }

            if (e.rowType === 'data' && e.column.dataField === 'Recuento') {
                e.cellElement.style.cssText = "color: red; text-align: center;"
            }

            if (e.rowType === 'data' && e.column.dataField === 'Faltantes') {
                e.cellElement.style.cssText = " text-align: center; font-weight: bold; font-size: 16px;";
                if (this.calcularFaltantes(e.data) < 0) {
                    e.cellElement.style.cssText = "color: red; text-align: center; font-weight: bold; font-size: 16px;"
                }
            }
        },
        onlyNumbers(e) {
            if ((e.event.keyCode < 48 || e.event.keyCode > 57) && (e.event.keyCode < 96 || e.event.keyCode > 105) && e.event.keyCode !== 8 && e.event.keyCode !== 9) {
                e.event.preventDefault()
            }
        },
        customizeText(e) {
            if (typeof (e.value) === 'object' && typeof (e.value) !== undefined) {
                return e.value.Descripcion
            }
            return e.value;
        },
        calcularFaltantes(e) {
            return (e.Inicio ? e.Inicio : 0) + (e.Adicionales ? e.Adicionales : 0) - (e.Recuento ? e.Recuento : 0)
        },
        mapRecuento() {
            let i = this.recuento.map((x) => {
                return {
                    Inicio: x.Inicio,
                    Adicionales: x.Adicionales,
                    Recuento: x.Recuento,
                    IdMaterial: x.Material.IdMaterial,
                    Comentarios: x.Comentarios,
                    CorporativoAnestesiologo: this.encabezado.Anestesiologo,
                    CorporativoInstrumentista: this.encabezado.Instrumentista,
                    CorporativoCirculante: this.encabezado.Circulante
                }
            })
            return i
        },
        validarUsuario(e) {
            this.$validar_usuario().then((resp) => {
                if (e.element.outerText === 'F. Anestesiólogo') {
                    this.encabezado.Anestesiologo = resp
                }
                if (e.element.outerText === 'F. Instrumentista') {
                    this.encabezado.Instrumentista = resp
                }
                if (e.element.outerText === 'F. Circulante') {
                    this.encabezado.Circulante = resp
                }
            })
        },
        limpiarTiempo() {
            this.getMateriales()
            this.encabezado = {
                Anestesiologo: '',
                Instrumentista: '',
                Circulante: '',
                NombreAnestesiologo: '',
                NombreInstrumentista: '',
                NombreCirculante: ''
            }
            this.registro.RegistroRecuento = ''
        },
        generarPDF()
        {
            let postData = {
                    SerieAdmision: this.SerieAdmision,
                    CodigoAdmision: this.CodigoAdmision,
                    IdHojaAnestesia: this.hojaAnestesia,
                    Correlativo: this.registro.RegistroRecuento
                }

                this.$reporte_modal({
                    Nombre: 'Recuento Compresas',
                    Opciones: {
                        ...postData
                    }
                })
        }
    },
    mounted() {
        this.getMateriales()
    },
    watch: {
        'registrosRecuento'(newval) {
            if (newval !== undefined) {
                this.limpiarTiempo()
            }
        },
    },
    computed: {
        dataGrid: function () {
            return this.$refs[dataGridRefKey].instance;
        },
    },
}
</script>

<style>
#Recuento .dx-item-content .dx-box-item-content {
    place-content: center;
    padding: 5px !important;
    padding-right: 10px !important;
}
</style>
