


-->>  Creación de tabla inv_solicitud_compra_enc

USE [HOSPITAL]
GO

/****** Object:  Table [dbo].[inv_solicitud_compra_enc]    Script Date: 12/03/2024 08:58:20 a.m. ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[inv_solicitud_compra_enc](
	[IdSolicitudEnc] [int] IDENTITY(1,1) NOT NULL,
	[IdEmpresasfk] [char](3) NOT NULL,
	[total] [money] NULL,
	[cant_lineas] [int] NULL,
	[observaciones] [text] NULL,
	[IdDepartamentofk] [char](2) NOT NULL,
	[IdBodegafk] [int] NULL,
	[IdProductoClasefk] [char](1) NULL,
	[IdProductoSubfk] [int] NULL,
	[fecha_creacion_orden] [datetime] NULL,
	[fecha_auto_rech_1] [datetime] NULL,
	[fecha_auto_rech_2] [datetime] NULL,
	[IdCorporativoSolicitafk] [int] NULL,
	[IdCorporativoAR1_fk] [int] NULL,
	[IdCorporativoAR2_fk] [int] NULL,
	[estado] [char](2) NULL,
	[ProveedorEmpresa] [char](3) NULL,
 CONSTRAINT [PK__inv_soli__40F9A207D8F167D2] PRIMARY KEY CLUSTERED 
(
	[IdSolicitudEnc] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[inv_solicitud_compra_enc] ADD  CONSTRAINT [DF__inv_solic__id_bo__74CE504D]  DEFAULT ((0)) FOR [IdBodegafk]
GO

ALTER TABLE [dbo].[inv_solicitud_compra_enc] ADD  CONSTRAINT [DF__inv_solic__fecha__75C27486]  DEFAULT (getdate()) FOR [fecha_creacion_orden]
GO

ALTER TABLE [dbo].[inv_solicitud_compra_enc] ADD  DEFAULT (NULL) FOR [ProveedorEmpresa]
GO


-->>  Creación de tabla inv_orden_compra_enc 

USE [HOSPITAL]
GO

/****** Object:  Table [dbo].[inv_orden_compra_enc]    Script Date: 12/03/2024 08:51:15 a.m. ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[inv_orden_compra_enc](
	[IdOrdenCompraEnc] [int] NOT NULL,
	[IdSolicitudEncfk] [int] NOT NULL,
	[IdEmpresasfk] [char](3) NOT NULL,
	[total] [money] NULL,
	[cant_lineas] [int] NULL,
	[observaciones] [text] NULL,
	[IdDepartamentofk] [char](2) NOT NULL,
	[IdBodegafk] [int] NULL,
	[IdProductoClasefk] [char](1) NULL,
	[IdProductoSubfk] [int] NULL,
	[IdCorporativoSolicitafk] [int] NULL,
	[fecha_creacion] [datetime] NULL,
	[fecha_auto_rech_1] [datetime] NULL,
	[fecha_auto_rech_2] [datetime] NULL,
	[IdCorporativoAR1_fk] [int] NULL,
	[IdCorporativoAR2_fk] [int] NULL,
	[estado] [char](2) NULL,
	[Tipo] [char](1) NOT NULL,
	[cant_pagos_propuesta] [int] NULL,
	[dias_credito_propuesta] [int] NULL,
	[fecha_maxima_recepcion] [date] NULL,
	[IdProveedorfk] [char](12) NOT NULL,
	[recepcionada] [char](1) NULL,
	[ProveedorEmpresa] [char](3) NULL,
	[orden_ortopedia] [char](1) NULL,
	[Validacion] [int] NULL,
	[RevisoConformidad] [int] NULL,
	[FechaRevisoConformidad] [dbo].[Fecha] NULL,
	[ObservacionesValidacion] [text] NULL,
	[IdCorporativoAnula] [int] NULL,
	[FechaAnulacion] [dbo].[Fecha] NULL,
	[TipoHono] [char](1) NULL,
	[Hospital] [char](3) NULL,
	[Medico] [int] NULL,
	[SerieFacturaMed] [char](3) NULL,
	[FacturaMed] [int] NULL,
	[CodigoOtroPagos] [int] NULL,
 CONSTRAINT [PK__inv_orde__5E1CEDC87DE9CDC0] PRIMARY KEY CLUSTERED 
(
	[IdOrdenCompraEnc] ASC,
	[IdEmpresasfk] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[inv_orden_compra_enc] ADD  CONSTRAINT [DF__inv_orden__cant___3335971A]  DEFAULT ((1)) FOR [cant_pagos_propuesta]
GO

ALTER TABLE [dbo].[inv_orden_compra_enc] ADD  CONSTRAINT [DF__inv_orden__dias___3429BB53]  DEFAULT ((0)) FOR [dias_credito_propuesta]
GO

ALTER TABLE [dbo].[inv_orden_compra_enc] ADD  DEFAULT ('N') FOR [recepcionada]
GO

ALTER TABLE [dbo].[inv_orden_compra_enc] ADD  DEFAULT (NULL) FOR [ProveedorEmpresa]
GO

ALTER TABLE [dbo].[inv_orden_compra_enc] ADD  DEFAULT ('N') FOR [orden_ortopedia]
GO

ALTER TABLE [dbo].[inv_orden_compra_enc] ADD  DEFAULT (NULL) FOR [TipoHono]
GO

ALTER TABLE [dbo].[inv_orden_compra_enc] ADD  DEFAULT (NULL) FOR [Hospital]
GO

ALTER TABLE [dbo].[inv_orden_compra_enc] ADD  DEFAULT (NULL) FOR [Medico]
GO

ALTER TABLE [dbo].[inv_orden_compra_enc] ADD  DEFAULT (NULL) FOR [SerieFacturaMed]
GO

ALTER TABLE [dbo].[inv_orden_compra_enc] ADD  DEFAULT (NULL) FOR [FacturaMed]
GO

ALTER TABLE [dbo].[inv_orden_compra_enc] ADD  DEFAULT (NULL) FOR [CodigoOtroPagos]
GO

ALTER TABLE [dbo].[inv_orden_compra_enc]  WITH CHECK ADD  CONSTRAINT [IdCompraSolicitudEnc_1] FOREIGN KEY([IdSolicitudEncfk])
REFERENCES [dbo].[inv_solicitud_compra_enc] ([IdSolicitudEnc])
GO

ALTER TABLE [dbo].[inv_orden_compra_enc] CHECK CONSTRAINT [IdCompraSolicitudEnc_1]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'A = Admisiones / O = Otros Pagos' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'inv_orden_compra_enc', @level2type=N'COLUMN',@level2name=N'TipoHono'
GO

--Descripción del campo Hospital 
EXEC sys.sp_addextendedproperty @name=N'MS_Description'
, @value=N'A = Campo donde se guarda el Hospital para Orden de compra Honorarios' 
, @level0type=N'SCHEMA',@level0name=N'dbo'
, @level1type=N'TABLE',@level1name=N'inv_orden_compra_enc', 
@level2type=N'COLUMN',@level2name=N'Hospital'
GO
--Descripción del campo Medico 
EXEC sys.sp_addextendedproperty @name=N'MS_Description'
, @value=N'A = Campo donde se guarda el Código del Médico para Orden de compra Honorarios' 
, @level0type=N'SCHEMA',@level0name=N'dbo'
, @level1type=N'TABLE',@level1name=N'inv_orden_compra_enc', 
@level2type=N'COLUMN',@level2name=N'Medico'
GO
--Descripción del campo SerieFacturaMed 
EXEC sys.sp_addextendedproperty @name=N'MS_Description'
, @value=N'A = Campo donde se guarda el la serie de factura para Orden de compra Honorarios' 
, @level0type=N'SCHEMA',@level0name=N'dbo'
, @level1type=N'TABLE',@level1name=N'inv_orden_compra_enc', 
@level2type=N'COLUMN',@level2name=N'SerieFacturaMed'
GO
--Descripción del campo FacturaMed 
EXEC sys.sp_addextendedproperty @name=N'MS_Description'
, @value=N'A = Campo donde se guarda el Código factura para Orden de compra Honorarios' 
, @level0type=N'SCHEMA',@level0name=N'dbo'
, @level1type=N'TABLE',@level1name=N'inv_orden_compra_enc', 
@level2type=N'COLUMN',@level2name=N'FacturaMed'
GO
--Descripción del campo CodigoOtroPagos 

EXEC sys.sp_addextendedproperty @name=N'MS_Description'
, @value=N'A = Campo donde se guarda el Código del ótros pagos para Orden de compra Honorarios' 
, @level0type=N'SCHEMA',@level0name=N'dbo'
, @level1type=N'TABLE',@level1name=N'inv_orden_compra_enc', 
@level2type=N'COLUMN',@level2name=N'CodigoOtroPagos'
GO




-->>  Creación de tabla inv_tipo_documento

USE [HOSPITAL]
GO

/****** Object:  Table [dbo].[inv_tipo_documento]    Script Date: 12/03/2024 08:58:43 a.m. ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[inv_tipo_documento](
	[IdTipoDocumento] [int] IDENTITY(1,1) NOT NULL,
	[descripcion] [varchar](50) NULL,
	[cant_responsable] [int] NOT NULL,
	[estado] [char](1) NULL,
	[IdEmpresafk] [char](3) NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[IdTipoDocumento] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[inv_tipo_documento] ADD  DEFAULT ((0)) FOR [cant_responsable]
GO

ALTER TABLE [dbo].[inv_tipo_documento] ADD  DEFAULT ('S') FOR [estado]
GO

ALTER TABLE [dbo].[inv_tipo_documento] ADD  DEFAULT ('MED') FOR [IdEmpresafk]
GO


EXEC sys.sp_addextendedproperty @name=N'MS_Description'
, @value=N'1 = Solicitud de Compra / 2 = Cotizacion / 3 = Orden de Compra / 4 = Documento' 
, @level0type=N'SCHEMA',@level0name=N'dbo'
, @level1type=N'TABLE',@level1name=N'inv_tipo_documento', 
@level2type=N'COLUMN',@level2name=N'IdTipoDocumento'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description'
, @value=N'S = Activo / N = Baja' 
, @level0type=N'SCHEMA',@level0name=N'dbo'
, @level1type=N'TABLE',@level1name=N'inv_tipo_documento', 
@level2type=N'COLUMN',@level2name=N'estado'
GO


-->>  Creación de tabla inv_autorizacion_documento

USE [HOSPITAL]
GO

/****** Object:  Table [dbo].[inv_autorizacion_documento]    Script Date: 12/03/2024 09:17:46 a.m. ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[inv_autorizacion_documento](
	[IdAutorizacionDoc] [int] IDENTITY(1,1) NOT NULL,
	[IdDocumentofk] [int] NULL,
	[IdResponsablefk] [int] NULL,
	[num_documento] [int] NULL,
	[fecha_baja] [datetime] NULL,
	[IdEmpresafk] [char](3) NOT NULL,
	[estado] [char](1) NULL,
	[IdDepartamentofk] [char](2) NULL,
	[IdClaseFk] [char](1) NULL,
	[IdProductoClasefk] [char](1) NULL,
 CONSTRAINT [PK__inv_auto__40F9A2078066591F] PRIMARY KEY CLUSTERED 
(
	[IdAutorizacionDoc] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[inv_autorizacion_documento] ADD  CONSTRAINT [DF__inv_autor__empre__68687968]  DEFAULT ('MED') FOR [IdEmpresafk]
GO

ALTER TABLE [dbo].[inv_autorizacion_documento] ADD  CONSTRAINT [DF__inv_autor__estad__6A50C1DA]  DEFAULT ('S') FOR [estado]
GO

ALTER TABLE [dbo].[inv_autorizacion_documento] ADD  DEFAULT (NULL) FOR [IdProductoClasefk]
GO

ALTER TABLE [dbo].[inv_autorizacion_documento]  WITH CHECK ADD  CONSTRAINT [FK__inv_autor__IdDoc__2A363CC5] FOREIGN KEY([IdDocumentofk])
REFERENCES [dbo].[inv_tipo_documento] ([IdTipoDocumento])
GO

ALTER TABLE [dbo].[inv_autorizacion_documento] CHECK CONSTRAINT [FK__inv_autor__IdDoc__2A363CC5]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description'
, @value=N'1 = Solicitud de Compra / 2 = Cotizacion / 3 = Orden de Compra' 
, @level0type=N'SCHEMA',@level0name=N'dbo'
, @level1type=N'TABLE',@level1name=N'inv_autorizacion_documento', 
@level2type=N'COLUMN',@level2name=N'IdDocumentofk'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description'
, @value=N'1 = Primer Responsable / 2 = Segundo Responsable' 
, @level0type=N'SCHEMA',@level0name=N'dbo'
, @level1type=N'TABLE',@level1name=N'inv_autorizacion_documento', 
@level2type=N'COLUMN',@level2name=N'num_documento'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description'
, @value=N'S = Activo / N = Baja' 
, @level0type=N'SCHEMA',@level0name=N'dbo'
, @level1type=N'TABLE',@level1name=N'inv_autorizacion_documento', 
@level2type=N'COLUMN',@level2name=N'estado'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description'
, @value=N'1 = Medicamento (solo para solicitud es requerido - las otras autorizaciones son 0)' 
, @level0type=N'SCHEMA',@level0name=N'dbo'
, @level1type=N'TABLE',@level1name=N'inv_autorizacion_documento', 
@level2type=N'COLUMN',@level2name=N'IdClaseFk'
GO


-->>  Creación de tabla inv_autorizacion_documento_historial

USE [HOSPITAL]
GO

/****** Object:  Table [dbo].[inv_autorizacion_documento_historial]    Script Date: 12/03/2024 09:18:09 a.m. ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[inv_autorizacion_documento_historial](
	[IdAutorizacionHistorial] [int] IDENTITY(1,1) NOT NULL,
	[IdCorporativofk] [int] NULL,
	[IdSolicitudEncfk] [int] NULL,
	[fecha_hora_inserto] [datetime] NULL,
	[fecha_hora_autorizo] [datetime] NULL,
	[estado] [char](1) NULL,
	[IdDocumentofk] [int] NULL,
	[num_documento] [int] NULL,
	[observaciones] [text] NULL,
	[IdOrdenCompraEncFk] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[IdAutorizacionHistorial] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[inv_autorizacion_documento_historial] ADD  DEFAULT ('P') FOR [estado]
GO

ALTER TABLE [dbo].[inv_autorizacion_documento_historial] ADD  DEFAULT ((0)) FOR [IdOrdenCompraEncFk]
GO

ALTER TABLE [dbo].[inv_autorizacion_documento_historial]  WITH CHECK ADD FOREIGN KEY([IdDocumentofk])
REFERENCES [dbo].[inv_tipo_documento] ([IdTipoDocumento])
GO

ALTER TABLE [dbo].[inv_autorizacion_documento_historial]  WITH CHECK ADD FOREIGN KEY([IdSolicitudEncfk])
REFERENCES [dbo].[inv_solicitud_compra_enc] ([IdSolicitudEnc])
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description'
, @value=N'C - Creado, P - Proceso, R - Rechazado, A - Autorizado, N - Anulada' 
, @level0type=N'SCHEMA',@level0name=N'dbo'
, @level1type=N'TABLE',@level1name=N'inv_autorizacion_documento_historial', 
@level2type=N'COLUMN',@level2name=N'estado'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description'
, @value=N'1 = Primer Responsable / 2 = Segundo Responsable' 
, @level0type=N'SCHEMA',@level0name=N'dbo'
, @level1type=N'TABLE',@level1name=N'inv_autorizacion_documento_historial', 
@level2type=N'COLUMN',@level2name=N'num_documento'
GO

-->>  Creación de tabla inv_cotizacion_enc

USE [HOSPITAL]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[inv_cotizacion_enc](
	[IdCotizacionEnc] [int] IDENTITY(1,1) NOT NULL,
	[IdSolicitudEncfk] [int] NOT NULL,
	[IdEmpresasfk] [char](3) NOT NULL,
	[total] [money] NOT NULL,
	[cant_lineas] [int] NULL,
	[observaciones] [text] NULL,
	[IdDepartamentofk] [char](2) NOT NULL,
	[IdBodegafk] [int] NULL,
	[IdProductoClasefk] [char](1) NULL,
	[IdProductoSubfk] [int] NULL,
	[fecha_creacion] [datetime] NULL,
	[fecha_autorizacion] [datetime] NULL,
	[IdCorporativoSolicitafk] [int] NULL,
	[IdCorporativoAutofk] [int] NULL,
	[estado] [char](2) NULL,
	[Tipo] [char](1) NOT NULL,
	[cant_pagos_propuesta] [int] NULL,
	[dias_credito_propuesta] [int] NULL,
	[fecha_maxima_recepcion] [date] NULL,
	[IdProveedorfk] [char](12) NOT NULL,
	[IdCorporativoIngresa] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[IdCotizacionEnc] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[inv_cotizacion_enc] ADD  DEFAULT ((1)) FOR [cant_pagos_propuesta]
GO

ALTER TABLE [dbo].[inv_cotizacion_enc] ADD  DEFAULT ((0)) FOR [dias_credito_propuesta]
GO

ALTER TABLE [dbo].[inv_cotizacion_enc] ADD  DEFAULT (NULL) FOR [IdCorporativoIngresa]
GO

ALTER TABLE [dbo].[inv_cotizacion_enc]  WITH CHECK ADD  CONSTRAINT [IdSolicitudEnc_1] FOREIGN KEY([IdSolicitudEncfk])
REFERENCES [dbo].[inv_solicitud_compra_enc] ([IdSolicitudEnc])
GO

ALTER TABLE [dbo].[inv_cotizacion_enc] CHECK CONSTRAINT [IdSolicitudEnc_1]
GO


EXEC sys.sp_addextendedproperty @name=N'MS_Description', 
@value=N'C - Creado, P - Proceso, R - Rechazado, A - Autorizado' , 
@level0type=N'SCHEMA',@level0name=N'dbo', 
@level1type=N'TABLE',@level1name=N'inv_cotizacion_enc', 
@level2type=N'COLUMN',@level2name=N'estado'
GO


EXEC sys.sp_addextendedproperty @name=N'MS_Description', 
@value=N'S = Servicio / P = Producto' , 
@level0type=N'SCHEMA',@level0name=N'dbo', 
@level1type=N'TABLE',@level1name=N'inv_cotizacion_enc', 
@level2type=N'COLUMN',@level2name=N'IdProductoClasefk'
GO


EXEC sys.sp_addextendedproperty @name=N'MS_Description', 
@value=N'I = Cotización generada por el sistema, cuando se autoriza la solicitud de compra, esta se convierte en cotización. / 
P = Cotización ingresada por el usuario y son las que nos proporciona el proveedor' , 
@level0type=N'SCHEMA',@level0name=N'dbo', 
@level1type=N'TABLE',@level1name=N'inv_cotizacion_enc', 
@level2type=N'COLUMN',@level2name=N'Tipo'
GO

-->>  Creación de tabla inv_cotizacion_det

USE [HOSPITAL]
GO

/****** Object:  Table [dbo].[inv_cotizacion_det]    Script Date: 12/03/2024 09:19:47 a.m. ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[inv_cotizacion_det](
	[IdCotizacionDet] [int] IDENTITY(1,1) NOT NULL,
	[IdCotizacionEncfk] [int] NOT NULL,
	[IdEmpresafk] [char](3) NOT NULL,
	[IdProductofk] [char](13) NULL,
	[cantidad_solicitada] [decimal](38, 3) NULL,
	[cantidad_proveedor] [decimal](38, 3) NULL,
	[estado] [char](1) NULL,
	[precio_unitario] [money] NULL,
	[sub_total] [money] NULL,
	[Comparativo] [char](1) NULL,
 CONSTRAINT [PK__inv_coti__C656022BFF6BB71E] PRIMARY KEY CLUSTERED 
(
	[IdCotizacionDet] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[inv_cotizacion_det] ADD  CONSTRAINT [DF_inv_cotizacion_det_cantidad_solicitada]  DEFAULT ((0)) FOR [cantidad_solicitada]
GO

ALTER TABLE [dbo].[inv_cotizacion_det] ADD  CONSTRAINT [DF_inv_cotizacion_det_cantidad_proveedor]  DEFAULT ((0)) FOR [cantidad_proveedor]
GO

ALTER TABLE [dbo].[inv_cotizacion_det] ADD  CONSTRAINT [DF__inv_cotiz__Compa__7226EDCC]  DEFAULT ('0') FOR [Comparativo]
GO

ALTER TABLE [dbo].[inv_cotizacion_det]  WITH CHECK ADD  CONSTRAINT [ICotizacionEnc_2] FOREIGN KEY([IdCotizacionEncfk])
REFERENCES [dbo].[inv_cotizacion_enc] ([IdCotizacionEnc])
GO

ALTER TABLE [dbo].[inv_cotizacion_det] CHECK CONSTRAINT [ICotizacionEnc_2]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', 
@value=N'0 = Detalle Cotización Interna / 1 = Cotización Externa' , 
@level0type=N'SCHEMA',@level0name=N'dbo', 
@level1type=N'TABLE',@level1name=N'inv_cotizacion_det', 
@level2type=N'COLUMN',@level2name=N'Comparativo'
GO

-->>  Creación de tabla INV_TIPO_RETENCION

USE [HOSPITAL]
GO

/****** Object:  Table [dbo].[inv_tipo_retencion]    Script Date: 12/03/2024 09:20:07 a.m. ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[inv_tipo_retencion](
	[IdTipoRetencion] [char](1) NOT NULL,
	[descripcion] [varchar](50) NULL,
	[estado] [char](1) NULL,
	[porcentaje] [smallmoney] NOT NULL
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[inv_tipo_retencion] ADD  DEFAULT ('0') FOR [porcentaje]
GO


 
-->>  Creación de tabla INV_ORDEN_COMPRA_DET 

USE [HOSPITAL]
GO

/****** Object:  Table [dbo].[inv_orden_compra_det]    Script Date: 12/03/2024 09:20:45 a.m. ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[inv_orden_compra_det](
	[IdOrdenDet] [int] IDENTITY(1,1) NOT NULL,
	[IdOrdenEncfk] [int] NOT NULL,
	[IdEmpresafk] [char](3) NOT NULL,
	[IdProductofk] [char](13) NULL,
	[cantidad] [decimal](38, 3) NULL,
	[estado] [char](1) NULL,
	[precio_unitario] [money] NULL,
	[sub_total] [money] NULL,
	[cantidad_recepcionada] [decimal](38, 3) NULL,
	[finalizado] [char](1) NULL,
	[exento_iva] [bit] NULL,
	[Bonificacion] [int] NULL,
	[SerieAdmision] [char](1) NULL,
	[Admision] [int] NULL,
	[Paciente] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[IdOrdenDet] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[inv_orden_compra_det] ADD  DEFAULT ((0)) FOR [cantidad_recepcionada]
GO

ALTER TABLE [dbo].[inv_orden_compra_det] ADD  DEFAULT ('N') FOR [finalizado]
GO

ALTER TABLE [dbo].[inv_orden_compra_det] ADD  DEFAULT ((0)) FOR [exento_iva]
GO

ALTER TABLE [dbo].[inv_orden_compra_det] ADD  DEFAULT ((0)) FOR [Bonificacion]
GO

ALTER TABLE [dbo].[inv_orden_compra_det] ADD  DEFAULT (NULL) FOR [SerieAdmision]
GO

ALTER TABLE [dbo].[inv_orden_compra_det] ADD  DEFAULT (NULL) FOR [Admision]
GO

ALTER TABLE [dbo].[inv_orden_compra_det] ADD  DEFAULT (NULL) FOR [Paciente]
GO

ALTER TABLE [dbo].[inv_orden_compra_det]  WITH CHECK ADD  CONSTRAINT [IdOrdenCompraEnc] FOREIGN KEY([IdOrdenEncfk], [IdEmpresafk])
REFERENCES [dbo].[inv_orden_compra_enc] ([IdOrdenCompraEnc], [IdEmpresasfk])
GO

ALTER TABLE [dbo].[inv_orden_compra_det] CHECK CONSTRAINT [IdOrdenCompraEnc]
GO

--Descripción del campo SerieAdmision 

EXEC sys.sp_addextendedproperty @name=N'MS_Description'
, @value=N'A = Campo donde se guarda el Código de la serie de la admision para Orden de compra Honorarios' 
, @level0type=N'SCHEMA',@level0name=N'dbo'
, @level1type=N'TABLE',@level1name=N'inv_orden_compra_det', 
@level2type=N'COLUMN',@level2name=N'SerieAdmision'
GO
--Descripción del campo Admision 

EXEC sys.sp_addextendedproperty @name=N'MS_Description'
, @value=N'A = Campo donde se guarda el Código de la admisión para Orden de compra Honorarios' 
, @level0type=N'SCHEMA',@level0name=N'dbo'
, @level1type=N'TABLE',@level1name=N'inv_orden_compra_det', 
@level2type=N'COLUMN',@level2name=N'Admision'
GO

--Descripción del campo Paciente 
EXEC sys.sp_addextendedproperty @name=N'MS_Description'
, @value=N'A = Campo donde se guarda el Código del paciente para Orden de compra Honorarios' 
, @level0type=N'SCHEMA',@level0name=N'dbo'
, @level1type=N'TABLE',@level1name=N'inv_orden_compra_det', 
@level2type=N'COLUMN',@level2name=N'Paciente'
GO


-->>  Creación de tabla INV_TIPO_PROVEEDOR

USE [HOSPITAL]
GO

/****** Object:  Table [dbo].[inv_tipo_proveedor]    Script Date: 12/03/2024 09:21:35 a.m. ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[inv_tipo_proveedor](
	[IdTipoProveedor] [char](6) NOT NULL,
	[descripcion] [varchar](50) NULL,
	[estado] [char](1) NULL,
PRIMARY KEY CLUSTERED 
(
	[IdTipoProveedor] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[inv_tipo_proveedor] ADD  DEFAULT ('S') FOR [estado]
GO


	
-->>  Creación de tabla INV_SOLICITUD_COMPRA_DET
	
USE [HOSPITAL]
GO

/****** Object:  Table [dbo].[inv_solicitud_compra_det]    Script Date: 12/03/2024 09:21:55 a.m. ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[inv_solicitud_compra_det](
	[IdSolicitudDet] [int] IDENTITY(1,1) NOT NULL,
	[IdSolicitudEncfk] [int] NOT NULL,
	[IdEmpresafk] [char](3) NOT NULL,
	[IdProductofk] [char](13) NULL,
	[cantidad] [decimal](38, 3) NULL,
	[estado] [char](1) NULL,
PRIMARY KEY CLUSTERED 
(
	[IdSolicitudDet] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[inv_solicitud_compra_det] ADD  DEFAULT ('S') FOR [estado]
GO

ALTER TABLE [dbo].[inv_solicitud_compra_det]  WITH CHECK ADD  CONSTRAINT [IdSolicitudEncfk] FOREIGN KEY([IdSolicitudEncfk])
REFERENCES [dbo].[inv_solicitud_compra_enc] ([IdSolicitudEnc])
GO

ALTER TABLE [dbo].[inv_solicitud_compra_det] CHECK CONSTRAINT [IdSolicitudEncfk]
GO

-->> Creación de tabla  inv_categoria_departamento 

USE [HOSPITAL]
GO

/****** Object:  Table [dbo].[inv_categoria_departamento]    Script Date: 12/03/2024 10:25:06 a.m. ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[inv_categoria_departamento](
	[IdCategoriaDepto] [int] IDENTITY(1,1) NOT NULL,
	[IdDepartamentofk] [char](2) NOT NULL,
	[IdCategoriafk] [char](3) NOT NULL,
	[estado] [char](1) NULL,
	[tipo] [char](1) NULL,
	[fecha_baja] [datetime] NULL,
 CONSTRAINT [PK_inv_categoria_departamento] PRIMARY KEY CLUSTERED 
(
	[IdCategoriaDepto] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[inv_categoria_departamento] ADD  CONSTRAINT [DF_inv_categoria_departamento_estado]  DEFAULT ('S') FOR [estado]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'R = Requerimientos / C= Compras' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'inv_categoria_departamento', @level2type=N'COLUMN',@level2name=N'tipo'
GO


-->> Activar Indice Admisiones 

USE [HOSPITAL]
GO
ALTER INDEX [missing_index_179_178] ON [dbo].[Admisiones] REBUILD PARTITION = ALL WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
GO


-->> Crear indice para  - OrdenCompraHonosMedicos
USE [INVENTARIO]
GO

SET ANSI_PADDING ON
GO

/****** Object:  Index [OrdenCompraHonos_Admisiones]    Script Date: 12/03/2024 02:08:03 p.m. ******/
CREATE NONCLUSTERED INDEX [OrdenCompraHonos_Admisiones] ON [dbo].[OrdenCompraHonosMedicos]
(
	[oca_empresa] ASC,
	[oca_serie_admision] ASC,
	[oca_admision] ASC,
	[oca_medico] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO


-->> Insertar datos en la tabla inv_tipo_proveedor 

USE [HOSPITAL]
GO

INSERT INTO inv_tipo_proveedor (IdTipoProveedor,descripcion,estado) VALUES
	 ('T1', 'Proveedor','S'),
	 ('T2','Médico','S'),
	 ('T3','Profesional','S'),
	 ('T4','Importaciones','S'),
	 ('T5','Otros','S');

-->> Insertar datos en la tabla inv_tipo_retencion

USE [HOSPITAL]
GO

INSERT INTO inv_tipo_retencion (IdTipoRetencion,descripcion,estado,porcentaje) VALUES
	 ('0','Sin retención','S',0.0000),
	 ('D','Retención definitiva','S',0.0000);
	 
-->> Insertar datos en la tabla inv_tipo_documento

USE [HOSPITAL]
GO

INSERT INTO inv_tipo_documento   (descripcion, cant_responsable, estado, IdEmpresafk) VALUES
	('Solicitud de Compra', 1, 'S', 'SEM'),
	('Cotización', 1, 'S', 'SEM'),
	('Orden de compra', 1, 'S', 'SEM'),
	('Documento', 1, 'N', 'SEM')
GO

-->> Insertar datos en la tabla inv_categoria_departamento - INSERT DE SUBCATEGORIAS POR DEPARTAMENTO - 

USE [HOSPITAL]
GO
	
	DECLARE
	@tblFiltroCategorias 
	TABLE (
		id int IDENTITY (1, 1),
		Departamento char(2),
		Codigo varchar(30),
		estado char(1),
		tipo char(1)
		)

	DECLARE @Categorias varchar(max) = '',
			@correl1 int,
			@codigos1 varchar(16),
			@caracteres1 int,
			@RowID int = 1,
			@CantTotal int = 0,
			@Departamento char(2)= ''


	DECLARE @tbdepartamentos
	TABLE( 
		id int, 
		codigo char(2), 
		sucategoria varchar(max)
		)

	INSERT INTO @tbdepartamentos 
		SELECT 
			row_number() over (order by codigo),
			CODIGO,
			SUBCATEGORIASACOMPRAR
		FROM INVENTARIO..DepartamentosInv	
		WHERE activa = 'S' AND CODIGO <> ''
		AND EMPRESA = 'BOD'
		SET @CantTotal = (SELECT @@ROWCOUNT)
				

	WHILE @RowID <= @CantTotal
	BEGIN

		SELECT 
			@Departamento = codigo,
			@Categorias = sucategoria
		FROM @tbdepartamentos
		WHERE id = @RowID

		PRINT @Departamento

		SET @correl1 = 1
		SET @codigos1 = ''
		SET @caracteres1 = LEN(@Categorias)

		WHILE @correl1 <= @caracteres1
		BEGIN
			--busca una coma
			IF SUBSTRING(@Categorias, @correl1, 1) = ','
			BEGIN
				INSERT INTO @tblFiltroCategorias (departamento, codigo, estado, tipo)
				VALUES (@Departamento, LTRIM(RTRIM(@codigos1)), 'S', 'C')

				SET @codigos1 = ''
					--si no encuentra la coma, suma el caracter a la cadena de codigo.
			END
			ELSE
			BEGIN
				SET @codigos1 = @codigos1 + SUBSTRING(@Categorias, @correl1, 1)
							
			END

			SET @correl1 = @correl1 + 1
		END
					
		IF LEN(@codigos1) > 0
		BEGIN
			INSERT INTO @tblFiltroCategorias (departamento, codigo, estado, tipo)
			VALUES (@Departamento, LTRIM(RTRIM(@codigos1)), 'S', 'C')
		END

		SET @RowID = @RowID + 1
	END
					
		INSERT INTO  inv_categoria_departamento
		SELECT
			DISTINCT  T.DEPARTAMENTO, T.CODIGO, T.ESTADO, T.TIPO, NULL
		FROM @tblFiltroCategorias T 
		
		
-->> Insertar datos en la tabla inv_categoria_departamento - INSERT DE REQUERIMIENTO POR DEPARTAMENTO - 

USE [HOSPITAL]
GO
	
	DECLARE
	@tblFiltroRequerimientos 
	TABLE (
		id int IDENTITY (1, 1),
		Departamento char(2),
		Codigo varchar(30),
		estado char(1),
		tipo char(1)
		)

	DECLARE @Requerimientos varchar(max) = '',
			@correl1 int,
			@codigos1 varchar(16),
			@caracteres1 int,
			@RowID int = 1,
			@CantTotal int = 0,
			@Departamento char(2)= ''


	DECLARE @tbdepartamentos
	TABLE( 
		id int, 
		codigo char(2), 
		requerimiento varchar(max)
		)

	INSERT INTO @tbdepartamentos 
		SELECT 
			row_number() over (order by codigo),
			CODIGO,
			REQUERIMIENTOS
		FROM INVENTARIO..DepartamentosInv	
		WHERE activa = 'S' AND CODIGO <> ''
		AND EMPRESA = 'BOD'
		SET @CantTotal = (SELECT @@ROWCOUNT)
				

	WHILE @RowID <= @CantTotal
	BEGIN

		SELECT 
			@Departamento = codigo,
			@Requerimientos = requerimiento
		FROM @tbdepartamentos
		WHERE id = @RowID

		PRINT @Departamento

		SET @correl1 = 1
		SET @codigos1 = ''
		SET @caracteres1 = LEN(@Requerimientos)

		WHILE @correl1 <= @caracteres1
		BEGIN
			--busca una coma
			IF SUBSTRING(@Requerimientos, @correl1, 1) = ','
			BEGIN
				INSERT INTO @tblFiltroRequerimientos (departamento, codigo, estado, tipo)
				VALUES (@Departamento, LTRIM(RTRIM(@codigos1)), 'S', 'R')

				SET @codigos1 = ''
					--si no encuentra la coma, suma el caracter a la cadena de codigo.
			END
			ELSE
			BEGIN
				SET @codigos1 = @codigos1 + SUBSTRING(@Requerimientos, @correl1, 1)
							
			END

			SET @correl1 = @correl1 + 1
		END
					
		IF LEN(@codigos1) > 0
		BEGIN
			INSERT INTO @tblFiltroRequerimientos (departamento, codigo, estado, tipo)
			VALUES (@Departamento, LTRIM(RTRIM(@codigos1)), 'S', 'R')
		END

		SET @RowID = @RowID + 1
	END
					
		INSERT INTO  inv_categoria_departamento
		SELECT
			DISTINCT  T.DEPARTAMENTO, T.CODIGO, T.ESTADO, T.TIPO, NULL
		FROM @tblFiltroRequerimientos T 	
-----------------------------------------------------------

-->>  Crear el Procedimiento Almacenado  -  sp_inv_tipo_documento 

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[sp_inv_tipo_documento]    Script Date: 13/03/2024 07:08:12 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/********************************************************************************************/
/*	CREADO POR:  		 Edvin Geovany Jucup  	Numero de Rational:  330014  	 			*/
/*	FECHA CREACIÓN:		 20/07/2023									                		*/
/*	PRODUCTO - PROCESO:  AUTORIZACIÓN DOCUMENTOS						    				*/
/**************************************** DESCRIPCION ***************************************/
/*	Este procedimiento tiene como objetivo consultar los tipos de documentos.				*/
/*																							*/
/*************************************** MODIFICACIÓN ***************************************/
/*					FECHA				 RESPONSABLE				DESCRIPCION				*/
/*																							*/
/********************************************************************************************/

CREATE PROCEDURE [dbo].[sp_inv_tipo_documento]

@i_empresa VARCHAR(3) = ''
AS
DECLARE @resultado INT = 0;
BEGIN
	SELECT
		IdTipoDocumento AS CODIGO,
		DESCRIPCION,
		CANT_RESPONSABLE
	FROM INV_TIPO_DOCUMENTO
	WHERE ESTADO = 'S' --AND IDEMPRESAFK = @i_empresa
END	




-->>  Crear el Procedimiento Almacenado  -  sp_inv_Busqueda_Corporativos

USE [HOSPITAL]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/********************************************************************************************/
/*	CREADO POR:  		 Edvin Geovany Jucup  	Número de Rational:  RTC-330014  			*/
/*	FECHA CREACIÓN:		 20/07/2023									                		*/
/*	PRODUCTO - PROCESO:  AUTORIZACIÓN DOCUMENTOS						    				*/
/**************************************** DESCRIPCION ***************************************/
/*	Este procedimiento tiene como objetivo consultar los corporativos.						*/
/*																							*/
/*************************************** MODIFICACIÓN ***************************************/
/*					FECHA				 RESPONSABLE				DESCRIPCION				*/
/*	         OCTUBRE 2023		BLANCA Beatríz CETINO R.	Se agrega la tabla EmpresasTel  */	
/* que ya contiene los datos y así no crear la tabla (inv_compania_telefonica).				*/
/********************************************************************************************/


CREATE PROCEDURE [dbo].[sp_inv_Busqueda_Corporativos]
	@tipo_busqueda char(1) = 1,
	@busqueda VARCHAR(100) = null
AS
BEGIN	
	SET NOCOUNT ON;

	IF (@tipo_busqueda = 'E')
	BEGIN
	  SELECT
		c.Corporativo,
		CONCAT(c.Nombres, ' ', c.Apellidos) AS nombre,
		c.correo,
		RTRIM(c.NIT) AS NIT,
		RTRIM(c.DPI) AS DPI		
	  FROM CONTADB..Corporativos c
	  WHERE (c.Corporativo LIKE '%' + @busqueda + '%'
	  OR c.Nombres LIKE '%' + @busqueda + '%'
	  OR c.Apellidos LIKE '%' + @busqueda + '%')
	  AND c.status = 'A'
	  ORDER BY c.Apellidos, c.nombres
	END
	ELSE
	IF (@tipo_busqueda = 'F')
	BEGIN

	  SELECT
		c.Corporativo,
		c.Nombres,
		c.Apellidos,
		c.correo,
		RTRIM(c.NIT) AS NIT,
		RTRIM(c.DPI) AS DPI
		,Status =
				CASE RTRIM(c.status)
				  WHEN 'A' THEN 'Activo'
				  WHEN 'I' THEN 'Inactivo'
				END,
		RTRIM(c.status) AS Estado
	  FROM CONTADB..Corporativos c
	  WHERE c.status IN ('I', 'A')
	  ORDER BY c.Apellidos, c.nombres
	END
	ELSE
	BEGIN
	  SELECT
		c.Corporativo,
		CONCAT(c.Nombres, ' ', c.Apellidos) AS nombre,
		c.correo,
		RTRIM(c.NIT) AS NIT,
		RTRIM(c.DPI) AS DPI
	  FROM CONTADB..Corporativos c
	  WHERE c.status = 'A'
	  ORDER BY c.Apellidos, c.nombres
	END
END

-->>  Crear el Procedimiento Almacenado  -  sp_inv_autorizacion_documento

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[sp_inv_autorizacion_documento]    Script Date: 12/03/2024 09:32:09 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/********************************************************************************************/
/*	CREADO POR:  		 Edvin Geovany Jucup  	Número de Rational:  RTC-330014   			*/
/*	FECHA CREACIÓN:		 20/07/2023									                		*/
/*	PRODUCTO - PROCESO:  AUTORIZACIÓN DOCUMENTOS						    				*/
/**************************************** DESCRIPCION ***************************************/
/*	Este procedimiento tiene como objetivo insertar y dar de baja los responsables			*/
/*	de autorizar documentos.																*/
/*	Almacena los responsables de autorizar solicitudes de compra por clase.					*/
/*																							*/
/*************************************** MODIFICACIÓN ***************************************/
/*		FECHA				 RESPONSABLE				DESCRIPCION							*/
/*	JUL - SEP 2023		Blanca Beatríz Cetino Rodríguez	 Se agrega empresa para deptos		*/
/*  Se agrega actualizacion por tipo de documento para Ordenes de compra por Servicio o Producto */
/********************************************************************************************/

CREATE PROCEDURE [dbo].[sp_inv_autorizacion_documento] 

	@i_empresa VARCHAR(3) = NULL,
	@i_codigo INT = NULL,
	@i_id_documento INT = NULL,
	@i_id_responsable INT = NULL,
	@i_id_departamento CHAR(2) = NULL,
	@i_num_documento INT = NULL,
	@i_operacion VARCHAR(1) = NULL, --N= Nuevo Ingreso / A = Actualizacion datos / B = Baja registro
	@i_corporativo INT = 1,
	@i_activa CHAR(1) = 'N',
	@i_id_clase CHAR(1) = '',
	@i_tipo CHAR(1) = ''


AS
  DECLARE @c_existecodigo CHAR(2) = '';
  DECLARE @c_resultado INT = 0;
  DECLARE @c_nombre_tabla VARCHAR(100) = 'Autorización documento';
  DECLARE @c_Id_Generado_Auto INT = 0;
  DECLARE @ErrRaise VARCHAR(99) = '';
  DECLARE @ErrRaiseNum INT = 0

  BEGIN
    SET NOCOUNT ON;
    --Inicio de transaccion 
    BEGIN TRAN


		--Inicio de excepcion
		BEGIN TRY

		/**********************   NUEVO INGRESO*********************************/
		IF @i_operacion = 'N'
		BEGIN


			--Validación si el tipo de documento es SOLICITU DE COMPRA , este se asigna por departamento y por clase
			IF (@i_id_documento = 1)
			BEGIN
				SET @c_existecodigo = (SELECT IdAutorizacionDoc
										FROM inv_autorizacion_documento
										WHERE IdDocumentoFk = @i_id_documento
										AND num_documento = @i_num_documento
										AND estado = 'S'
										AND IdDepartamentofk = @i_id_departamento
										AND IdClasefk = @i_id_clase 
										AND IdEmpresafk =@i_empresa);
			END
			ELSE
			BEGIN
				SET @c_existecodigo = (SELECT	IdAutorizacionDoc
										FROM inv_autorizacion_documento
										WHERE IdDocumentoFk = @i_id_documento
										AND num_documento = @i_num_documento
										AND estado = 'S'
										AND IdProductoClasefk = @i_tipo
										AND IdEmpresafk =@i_empresa);
			END

			IF (@c_existecodigo <> '')
			BEGIN
				SELECT	'1' AS 'codigo','Código ' + @c_nombre_tabla + ' se encuentra asignado.' AS 'descripcion',	@c_resultado AS 'resultado';
				ROLLBACK TRAN
			END
			ELSE
			BEGIN
				--Insertar nuevo 
				INSERT INTO inv_autorizacion_documento (IdDocumentoFk, IdResponsableFk, num_documento, IdDepartamentofk, IdEmpresafk, IdClasefk, IdProductoClasefk)
					VALUES (@i_id_documento, @i_id_responsable, @i_num_documento, @i_id_departamento, @i_empresa, @i_id_clase, @i_tipo);


				--Obtiene el ID generado				 
				SET @c_resultado = (SELECT	@@Identity);

				--Confirmación transacción
				COMMIT TRAN;

				--Resultado de la operación
				SELECT	'0' AS 'codigo',	@c_nombre_tabla AS 'descripcion',	@c_resultado AS 'resultado';

			END
		END

		/**********************  BAJA REGISTRO *********************************/
		ELSE
		IF @i_operacion = 'B' -- Baja registro
		BEGIN

			--Actualización departamento
			
			IF (@i_id_documento = 3)
			BEGIN 
				UPDATE inv_autorizacion_documento
				SET fecha_baja = GETDATE(),
					estado = @i_activa
				WHERE IdAutorizacionDoc = @i_codigo
				AND IdEmpresafk =@i_empresa
				AND IdProductoClasefk = @i_tipo
			END
			ELSE 
			BEGIN 
				UPDATE inv_autorizacion_documento
				SET fecha_baja = GETDATE(),
					estado = @i_activa
				WHERE IdAutorizacionDoc = @i_codigo
				AND IdEmpresafk =@i_empresa
			END 

			--Obtiene la cantidad de filas afectadas
			SET @c_resultado = (SELECT	@@RowCount);
			
			IF @c_resultado > 0
			BEGIN

				--Confirmación transacción
				COMMIT TRAN;
				--Resultado de la operación
				SELECT	'0' AS 'codigo',	@c_nombre_tabla AS 'descripcion',	@c_resultado AS 'resultado';
			END
			ELSE
			BEGIN
				--Abortar transacción
				ROLLBACK TRAN

				--Resultado de la operación
				SET @c_nombre_tabla = @c_nombre_tabla + ' No eliminado';
				SELECT	'1' AS 'codigo',	@c_nombre_tabla AS 'descripcion',	'0' AS 'resultado'; ;
			END
		END;
	END TRY
	BEGIN CATCH
		ROLLBACK TRAN
		SET @ErrRaise = ERROR_MESSAGE()
		SET @ErrRaiseNum = ERROR_NUMBER()

		RAISERROR (@ErrRaise, 16, @ErrRaiseNum)
	END CATCH
  END

-->>  Crear el Procedimiento Almacenado  -  sp_inv_autorizacion_documento_lista

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[sp_inv_autorizacion_documento_lista]    Script Date: 12/03/2024 09:35:59 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/********************************************************************************************/
/*  CREADO POR:  		 Edvin Geovany Jucup  	Número de Rational:  RTC-330014   			*/
/*	FECHA CREACIÓN:		 20/07/2023									                		*/
/*	PRODUCTO - PROCESO:  AUTORIZACIÓN DOCUMENTOS, SOLICITUD DE COMPRA, AUTORIZACIÓN			*/
/*	SOLICITUD, ORDEN DE COMPRA MANUAL														*/
/**************************************** DESCRIPCION ***************************************/
/*	Este procedimiento tiene como objetivo consultar los datos de los responsables			*/
/*	encargados de autorizar solicitud de compras, cotizaciones y ordenes de compra.			*/
/*	Tiene	parametro de clase al cargar las solicitudes de compra.							*/
/*																							*/
/*************************************** MODIFICACIÓN ***************************************/
/*		FECHA				 RESPONSABLE				       DESCRIPCION					*/
/*	JUL - SEP 2023		Blanca Beatríz Cetino Rodríguez	 Se agrega empresa para deptos.		*/
/********************************************************************************************/

CREATE PROCEDURE [dbo].[sp_inv_autorizacion_documento_lista]



@i_empresa VARCHAR(3) = '',
@i_id_documento INT = 0,
@i_num_documento INT = 0,
@i_id_departamento CHAR(2) = '01',
@tipo_consulta CHAR(1) = 'T', -- T = Consultar todos los doc / E = Consultar por doc y número. 
@i_id_clase CHAR(1) = '',
@i_empresa_bod CHAR(3) = '',
@i_tipo CHAR(1) = ''

AS
BEGIN


		IF (@tipo_consulta = 'T')
		BEGIN

			SELECT
				IAD.IDAUTORIZACIONDOC AS ID_AUTORIZACION,
				IAD.IDDOCUMENTOFK AS ID_DOCUMENTO,
				ITD.DESCRIPCION AS DESCRIPCION_DOCUMENTO,
				IAD.IDRESPONSABLEFK AS ID_RESPONSABLE,
				CONCAT(RESP.Nombres, ' ', RESP.Apellidos) AS NOMBRE_RESPONSABLE,
				IAD.IDDEPARTAMENTOFK AS ID_DEPARTAMENTO,
				CASE
				WHEN (IAD.IDDEPARTAMENTOFK = NULL) OR
					(IAD.IDDEPARTAMENTOFK = '') THEN 'N/A'
				ELSE DEPT.NOMBRE
				END AS DESCRIPCION_DEPTO,
				IDPRODUCTOCLASEFK AS SP,
				CASE WHEN IDPRODUCTOCLASEFK = 'P' THEN 'BIEN'
					WHEN IDPRODUCTOCLASEFK = 'S' THEN 'SERVICIO'
				END AS TIPO
			FROM HOSPITAL..inv_autorizacion_documento AS IAD
			INNER JOIN HOSPITAL..INV_TIPO_DOCUMENTO AS ITD
				ON IAD.IDDOCUMENTOFK = ITD.IdTipoDocumento
			INNER JOIN CONTADB..CORPORATIVOS AS RESP
				ON IAD.IDRESPONSABLEFK = RESP.Corporativo
			LEFT JOIN INVENTARIO..DEPARTAMENTOSINV AS DEPT
				ON IAD.IDDEPARTAMENTOFK = DEPT.CODIGO
				AND DEPT.EMPRESA = @i_empresa_bod
			WHERE IAD.ESTADO = 'S'
			AND IDDOCUMENTOFK = @i_id_documento
			AND (@i_tipo IS NULL OR	CONCAT(RTRIM(IAD.IdProductoClasefk), ' ') LIKE '%' + LTRIM(RTRIM(@i_tipo)) + '%')
			AND IAD.IDEMPRESAFK = @i_empresa
		END
		ELSE
		IF (@tipo_consulta = 'E')
		BEGIN
			IF (@i_id_documento = 1) -- Consultar por Solicitud de compra, departamento y clase
			BEGIN
				SELECT
					IAD.IDRESPONSABLEFK AS ID_RESPONSABLE,
					CONCAT(CRP.NOMBRES, ' ', CRP.APELLIDOS) AS NOMBRE,
					CRP.CORREO,
					@i_id_documento AS ID_DOCUMENTO,
					CASE WHEN IDPRODUCTOCLASEFK = 'P' THEN 'BIEN'
					WHEN IDPRODUCTOCLASEFK = 'S' THEN 'SERVICIO'
				END AS TIPO
				FROM HOSPITAL..INV_AUTORIZACION_DOCUMENTO AS IAD
				INNER JOIN CONTADB..CORPORATIVOS AS CRP
				ON IAD.IDRESPONSABLEFK = CRP.CORPORATIVO
				WHERE IAD.IDDOCUMENTOFK = @i_id_documento
					AND IAD.NUM_DOCUMENTO = @i_num_documento
					AND IAD.IDEMPRESAFK = @i_empresa
					AND IAD.IDDEPARTAMENTOFK = @i_id_departamento
					AND IAD.ESTADO = 'S'
			END
			ELSE
			IF (@i_id_documento = 2) -- Consultar cotizacion
			BEGIN
				SELECT
					IAD.IDRESPONSABLEFK AS ID_RESPONSABLE,
					CONCAT(CRP.NOMBRES, ' ', CRP.APELLIDOS) AS NOMBRE,
					CRP.CORREO,
					@i_id_documento AS ID_DOCUMENTO,
					CASE WHEN IDPRODUCTOCLASEFK = 'P' THEN 'BIEN'
					WHEN IDPRODUCTOCLASEFK = 'S' THEN 'SERVICIO'
				END AS TIPO
				FROM HOSPITAL..INV_AUTORIZACION_DOCUMENTO AS IAD
				INNER JOIN CONTADB..CORPORATIVOS AS CRP
					ON IAD.IDRESPONSABLEFK = CRP.CORPORATIVO
					WHERE IAD.IDDOCUMENTOFK = @i_id_documento
					AND IAD.NUM_DOCUMENTO = @i_num_documento
					AND IAD.IDEMPRESAFK = @i_empresa
					AND IAD.ESTADO = 'S';
			END
			ELSE
			IF (@i_id_documento = 3) -- Consultar orden de compra
			BEGIN
				SELECT
					IAD.IDRESPONSABLEFK AS ID_RESPONSABLE,
					CONCAT(CRP.NOMBRES, ' ', CRP.APELLIDOS) AS NOMBRE,
					CRP.CORREO,
					@i_id_documento AS ID_DOCUMENTO,
					CASE WHEN IDPRODUCTOCLASEFK = 'P' THEN 'BIEN'
					WHEN IDPRODUCTOCLASEFK = 'S' THEN 'SERVICIO'
					END AS TIPO
				FROM HOSPITAL..INV_AUTORIZACION_DOCUMENTO AS IAD
				INNER JOIN CONTADB..CORPORATIVOS AS CRP
				ON IAD.IDRESPONSABLEFK = CRP.CORPORATIVO
				WHERE IAD.IDDOCUMENTOFK = @i_id_documento
					AND IAD.NUM_DOCUMENTO = @i_num_documento
					AND IAD.IDEMPRESAFK = @i_empresa
					AND IdProductoClasefk = @i_tipo
					AND IAD.ESTADO = 'S';
			END
		END
END

-->>  Crear el Procedimiento Almacenado  -  sp_inv_solicitud_enc

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[sp_inv_solicitud_enc]    Script Date: 13/03/2024 12:26:07 p.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/********************************************************************************************/
/*	CREADO POR:  		 Edvin Geovany Jucup  	Numero de Rational:  330014  	 			*/
/*	FECHA CREACIÓN:		 20/07/2023									                		*/
/*	PRODUCTO - PROCESO:  SOLICITUD DE COMPRA							    				*/
/**************************************** DESCRIPCION ***************************************/
/*	Este procedimiento tiene como objetivo insertar una	solicitud de compra, notificación	*/
/*	para el responsable a autorizar, corporativo del responsable							*/
/*												 											*/
/*																							*/
/*************************************** MODIFICACIÓN ***************************************/
/*		FECHA				 RESPONSABLE				DESCRIPCION							*/
/* JUL - SEP 2023   Blanca Beatríz Cetino Rodríguez	Validación de Empresa para Proveedor	*/
/********************************************************************************************/


CREATE PROCEDURE [dbo].[sp_inv_solicitud_enc]

	@i_empresa CHAR(3) = NULL,
	@i_cant_lineas INT = 0,
	@i_observaciones TEXT = '',
	@i_id_departamento CHAR(2) = 0,
	@i_id_bodega INT = 0,
	@i_producto_clase CHAR(1) = '',
	@i_producto_subclase INT = 0,
	@i_corporativo int



AS
  DECLARE @c_empresaProv CHAR(3) = ''
  DECLARE @c_existecodigo CHAR(2) = '';
  DECLARE @c_resultado INT = 0;
  DECLARE @c_nombre_tabla VARCHAR(100) = 'Solicitud Compra Encabezado';
  DECLARE @c_EsUnica_Empresa CHAR(1) = '';
  DECLARE @c_Id_Generado_Auto INT = 0;
  DECLARE @c_Id_Responsable_Auto INT = 0;
  DECLARE @ErrRaise VARCHAR(99) = '';
  DECLARE @ErrRaiseNum INT = 0

  BEGIN
    SET NOCOUNT ON;

    --Inicio de excepcion
    BEGIN TRY

      /***************VALIDACIÓN DE EMPRESA PROVEEDOR****************/
		  --Validación de Empresa para Proveedor
		  SET @c_empresaProv = (SELECT
								  CASE
									  WHEN EmpresaUnificadora = 'MED' THEN EmpresaUnificadora
									  ELSE Empresa
									END AS EMPRESAPROV
								  FROM ContaDB..EmpresasDefaults
								  WHERE Empresa = @i_empresa)

		  /**********************   NUEVO INGRESO*********************************/

		  --Insertar nuevo 
		  INSERT INTO 	inv_solicitud_compra_enc (
				IdEmpresasfk
				, cant_lineas
				, observaciones
				, IdDepartamentofk
				, IdBodegafk
				, IdProductoClasefk
				, IdProductoSubfk
				, fecha_creacion_orden
				, IdCorporativoSolicitafk
				, estado
				, total
				, ProveedorEmpresa)
			VALUES (@i_empresa
				, @i_cant_lineas
				, @i_observaciones
				, @i_id_departamento
				, @i_id_bodega
				, @i_producto_clase
				, @i_producto_subclase
				, GETDATE()
				, @i_corporativo
				, 'P'
				, 0
				, @c_empresaProv);

		  --Obtiene el ID generado				 
		  SET @c_resultado = (SELECT
			@@Identity);


		  --Insertar nuevo 
			INSERT INTO inv_autorizacion_documento_historial 
				(IdCorporativofk
				, IdSolicitudEncfk
				, fecha_hora_inserto
				, IdDocumentofk
				, num_documento
				, estado)
			VALUES (@i_corporativo
				, @c_resultado
				, GETDATE()
				, '4'
				, '1'
				, 'C')


		  SET @c_Id_Responsable_Auto = (SELECT TOP 1
											IDRESPONSABLEFK
										  FROM INV_AUTORIZACION_DOCUMENTO
										  WHERE IDDOCUMENTOFK = 1
										  AND NUM_DOCUMENTO = 1
										  AND IDEMPRESAFK = @i_empresa
										  AND ESTADO = 'S'
										  AND IDDEPARTAMENTOFK = @i_id_departamento
										  --AND IdClaseFK = @i_producto_clase
										  )

		  INSERT INTO inv_autorizacion_documento_historial 
				(IdCorporativofk
				, IdSolicitudEncfk
				, fecha_hora_inserto
				, IdDocumentofk
				, num_documento)
			VALUES (@c_Id_Responsable_Auto
				, @c_resultado
				, GETDATE()
				, 1
				, 1);

		  --Resultado de la operación
		  SELECT '0' AS 'codigo', @c_Id_Responsable_Auto AS 'descripcion',	@c_resultado AS 'resultado';


		END TRY
		BEGIN CATCH
		  --	ROLLBACK TRAN
		  SET @ErrRaise = ERROR_MESSAGE()
		  SET @ErrRaiseNum = ERROR_NUMBER()

		  RAISERROR (@ErrRaise, 16, @ErrRaiseNum)
		END CATCH
  END


-->>  Crear el Procedimiento Almacenado  -  sp_inv_solicitud_det

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[sp_inv_solicitud_det]    Script Date: 13/03/2024 12:27:34 p.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


/********************************************************************************************/
/*	CREADO POR:  		 Edvin Geovany Jucup  	Numero de Rational:  330014  	 			*/
/*	FECHA CREACIÓN:		 20/07/2023									                		*/
/*	PRODUCTO - PROCESO:  SOLICITUD DE COMPRA							    				*/
/**************************************** DESCRIPCION ***************************************/
/*	Este procedimiento tiene como objetivo insertar, actualizar y dar de baja el			*/
/*	detalle de una solicitud de compra.														*/
/*																							*/
/*************************************** MODIFICACIÓN ***************************************/
/*					FECHA				 RESPONSABLE				DESCRIPCION				*/
/*																							*/
/********************************************************************************************/


CREATE PROCEDURE [dbo].[sp_inv_solicitud_det]
	@i_codigo INT = 0,
	@i_empresa CHAR(3) = null,
	@i_id_solicitud_enc INT = 0,
	@i_id_producto INT = 0,
	@i_cantidad DECIMAL (38,3) = 0.0,
	@i_corporativo INT = 0,
	@i_transaccion CHAR(1) = '' --N= Nuevo / E =Eliminar
	
AS
DECLARE @c_existecodigo CHAR(2)  = '';
DECLARE @c_resultado INT  = 0;
DECLARE @c_nombre_tabla VARCHAR(100) = 'Solicitud Compra detalle';
DECLARE	@c_EsUnica_Empresa CHAR(1)		= '';
DECLARE	@c_Id_Generado_Auto INT		= 0;
DECLARE	@ErrRaise	Varchar(99)	= '';
DECLARE	@ErrRaiseNum INT		= 0

BEGIN	
	SET NOCOUNT ON;
	--Inicio de transaccion 
	---BEGIN TRAN


	--Inicio de excepcion
	BEGIN TRY

  /**********************   NUEVO INGRESO*********************************/
	  IF (@i_transaccion = 'N')
	  BEGIN

			--Insertar nuevo 
			INSERT INTO inv_solicitud_compra_det (IdSolicitudEncfk, IdEmpresafk, IdProductofk, cantidad)
			  VALUES (@i_id_solicitud_enc, @i_empresa, @i_id_producto, @i_cantidad);

			--Obtiene el ID generado				 
			SET @c_resultado = (SELECT  @@Identity);


			--Confirmación transacción
			--	COMMIT TRAN;

			--Resultado de la operación
			SELECT   '0' AS 'codigo',  @c_nombre_tabla AS 'descripcion',  @c_resultado AS 'resultado';

	  END
	  ELSE
	  IF (@i_transaccion = 'E') -- Eliminar detalle solicitud de compra
	  BEGIN
			UPDATE inv_solicitud_compra_det
			SET estado = 'N'
			WHERE IdSolicitudDet = @i_codigo;

			SELECT  '0' AS 'codigo',  @c_nombre_tabla AS 'descripcion',	  @i_codigo AS 'resultado';

	  END
	END TRY
	BEGIN CATCH
	  --	ROLLBACK TRAN
	  SET @ErrRaise = ERROR_MESSAGE()
	  SET @ErrRaiseNum = ERROR_NUMBER()

	  RAISERROR (@ErrRaise, 16, @ErrRaiseNum)
	END CATCH
END



-->>  Crear el Procedimiento Almacenado  -  sp_inv_linea_tiempo

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[sp_inv_linea_tiempo]    Script Date: 13/03/2024 12:29:21 p.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


/********************************************************************************************/
/*	CREADO POR:  		 Edvin Geovany Jucup  	Numero de Rational:  330014  	 			*/
/*	FECHA CREACIÓN:		 20/07/2023									                		*/
/*	PRODUCTO - PROCESO:  SOLICITUD DE COMPRA							    				*/
/**************************************** DESCRIPCION ***************************************/
/*	Este procedimiento tiene como objetivo consultar los datos de las bitacora	de una		*/
/*	solicitud de compra, descripcion del documento.											*/
/*																							*/
/*************************************** MODIFICACIÓN ***************************************/
/*	FECHA					RESPONSABLE														*/
/*	JUL - SEP 2023		Blanca Beatríz Cetino Rodríguez										*/
/*  DESCRIPCION																				*/
/*  Se agrega el No. de orden de compra cuand el detalle de bitacora, esto para identificar */
/*  cuando una solicitud tiene más de una Orden de Compra generada.							*/
/*	Se agrega 																				*/
/*																							*/

/********************************************************************************************/



CREATE PROCEDURE [dbo].[sp_inv_linea_tiempo]

	
	@i_id_solicitud INT =0


AS
DECLARE @c_existecodigo CHAR(2)  = '';
DECLARE @c_resultado INT  = 0;
DECLARE @c_nombre_tabla VARCHAR(100) = 'Consulta Solicitud Compra Encabezado';
DECLARE	@c_EsUnica_Empresa VARCHAR		= '';
DECLARE	@c_Id_Generado_Auto INT		= 0;
DECLARE	@ErrRaise	Varchar(99)	= '';
DECLARE	@ErrRaiseNum INT		= 0

BEGIN	
	SET NOCOUNT ON;

		SELECT
		  CASE
			WHEN RTRIM(iad.ESTADO) = 'I' THEN 'green'
			WHEN RTRIM(iad.ESTADO) = 'A' THEN 'green'
			WHEN RTRIM(iad.ESTADO) = 'R' THEN '#C82B0A'
			WHEN RTRIM(iad.ESTADO) = 'C' THEN 'green'
			ELSE '#B2BABB'
		  END
		  AS COLOR,
		  CASE
			WHEN RTRIM(iad.ESTADO) = 'I' THEN 'INICIO '
			WHEN RTRIM(iad.ESTADO) = 'A' THEN 'AUTORIZADA '
			WHEN RTRIM(iad.ESTADO) = 'R' THEN 'RECHAZADA '
			WHEN RTRIM(iad.ESTADO) = 'C' THEN 'CREADO '
			ELSE 'PENDIENTE'
		  END
		  AS ESTADO,
		  UPPER(CONCAT(CONCAT(CONCAT('RESPONSABLE ', iad.num_documento), ': '), CR.Nombres, ' ', CR.Apellidos)) AS RESPONSABLE,
		  CASE
			WHEN RTRIM(iad.ESTADO) = 'C' THEN CONCAT('FECHA: ', FORMAT(iad.fecha_hora_inserto, 'dd/MM/yyyy, HH:mm:ss '))
			WHEN RTRIM(iad.ESTADO) = 'A' OR
			  RTRIM(iad.ESTADO) = 'R' THEN CONCAT('FECHA: ', FORMAT(iad.fecha_hora_autorizo, 'dd/MM/yyyy, HH:mm:ss '))
			ELSE ''
		  END
		  AS FECHA,
		  CASE
			WHEN iad.OBSERVACIONES IS NOT NULL THEN UPPER(CONCAT('OBSERVACIONES: ', iad.OBSERVACIONES))
			ELSE ''
		  END
		  AS OBSERVACIONES,
		  UPPER(itd.descripcion) AS DESCRIPCION_DOCUMENTO,
		  IAD.estado AS ESTADO_REAL,
		  itd.IdTipoDocumento AS T_DOC,
		  CASE
			 WHEN itd.IdTipoDocumento = 3 THEN  iad.IdOrdenCompraEncFk 
			ELSE '' 
		  END AS ORDEN_COMPRA
		FROM inv_autorizacion_documento_historial AS iad
		INNER JOIN CONTADB..corporativos AS cr
		  ON iad.IdCorporativofk = cr.corporativo
		INNER JOIN inv_tipo_documento AS itd
		  ON iad.IdDocumentofk = itd.IdTipoDocumento
		WHERE iad.IdSolicitudEncfk = @i_id_solicitud
END


-->>  Crear el Procedimiento Almacenado  -  sp_inv_solicitud_enc_lista

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[sp_inv_solicitud_enc_lista]    Script Date: 13/03/2024 12:20:19 p.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/********************************************************************************************/
/*	CREADO POR:  		 Edvin Geovany Jucup  	Número de Rational:  RTC-330014  			*/
/*	FECHA CREACIÓN:		 20/07/2023									                		*/
/*	PRODUCTO - PROCESO:  SOLICITUD DE COMPRA, AUTORIZACIÓN SOLICITUD, COTIZACIÓN			*/
/**************************************** DESCRIPCION ***************************************/
/*	Este procedimiento tiene como objetivo consultar los datos de una solicitud				*/
/*	de compra.																				*/
/*												 											*/
/*																							*/
/*************************************** MODIFICACIÓN ***************************************/
/*					FECHA				 RESPONSABLE										*/
/*			  JUL - SEP 2023			 Blanca Beatríz Cetino Rodríguez				    */
/*  DESCRIPCION: Se agrega el usuario que realiza la solicitud, se identifica el tipo de	*/
/*  Orden de Compra, se agrega empresa bodega para departamentos.							*/
/********************************************************************************************/


CREATE PROCEDURE [dbo].[sp_inv_solicitud_enc_lista]

	
	@i_empresa CHAR(3) = null,
	@i_id_departamento CHAR(2) = 0,
	@i_id_bodega INT = 0,
	@i_tipo_consulta CHAR(1) = 0,
	@i_estado CHAR(1) = 0,
	@i_id_solicitud INT = 0,
	@i_finicio DATE = '',
	@i_ffinal DATE = '',
	@i_empresaBod CHAR(3) = ''
	/*
	SELECT * FROM INV_SOLICITUD_COMPRA_ENC
	EXEC SP_INV_SOLICITUD_ENC_LISTA
	@i_empresa = 'AME',
	@i_id_departamento  = 2,
	@i_id_bodega = 3,
	@i_tipo_consulta = 'U',
	@i_Estado = ''

	*/
	
AS
DECLARE @c_existecodigo CHAR(2)  = '';
DECLARE @c_resultado INT  = 0;
DECLARE @c_nombre_tabla VARCHAR(100) = 'Consulta Solicitud Compra Encabezado';
DECLARE	@c_EsUnica_Empresa VARCHAR		= '';
DECLARE	@c_Id_Generado_Auto INT		= 0;
DECLARE	@ErrRaise	VARCHAR(99)	= '';
DECLARE	@ErrRaiseNum INT		= 0

BEGIN	
	SET NOCOUNT ON;

	IF (@i_tipo_consulta = 'T')
	BEGIN
		SELECT ISCE.IdSolicitudEnc AS CODIGO, 
			ISCE.IdEmpresasfk AS EMPRESA, 
			RTRIM(ISCE.IdDepartamentofk) AS ID_DEPARTAMENTO, 
			rtrim(DEPTO.Nombre) AS DESCRIPCION_DEPTO, 
			RTRIM(ISCE.IdBodegafk) AS ID_BODEGA,  
			rtrim(BOD.Nombre) AS DESCRIPCION_BODEGA,
			FORMAT(ISCE.FECHA_CREACION_ORDEN, 'dd/MM/yyyy') AS FECHA_CREACION_ORDEN,
			FORMAT(ISCE.FECHA_AUTO_RECH_1, 'dd/MM/yyyy')  AS FECHA_AUTO_RECH_1, 
			FORMAT(ISCE.FECHA_AUTO_RECH_2, 'dd/MM/yyyy')  AS FECHA_AUTO_RECH_2, 
			ISCE.IdCorporativoSolicitafk AS ID_CORPORATIVO_SOLICiTA, 
			ISCE.IdCorporativoAR1_fk AS ID_CORPORATIVO_AUTO_RECH_1,
			ISCE.IdCorporativoAR2_fk AS ID_CORPORATIVO_AUTO_RECH_2 , 
			ISCE.ESTADO
		FROM INV_SOLICITUD_COMPRA_ENC AS ISCE
		INNER JOIN INVENTARIO..DepartamentosINV AS DEPTO
		ON ISCE.IdDepartamentofk=  DEPTO.CODIGO 
		AND DEPTO.EMPRESA = @i_empresaBod
		LEFT JOIN INVENTARIO..BODEGAS AS BOD
		ON ISCE.IdBodegafk = BOD.CODIGO 
		WHERE ISCE.IdEmpresasfk = @i_empresa
		ORDER BY ISCE.IdSolicitudEnc ASC
	END 
	IF (@i_tipo_consulta = 'E') --- Busqueda por especifica con id
	BEGIN

		SELECT 
			ISCE.IdSolicitudEnc AS CODIGO, 
			ISCE.IdEmpresasfk AS EMPRESA, 
			RTRIM(ISCE.IdDepartamentofk) AS ID_DEPARTAMENTO , 
			rtrim(DEPTO.Nombre) AS DESCRIPCION_DEPTO, 
			RTRIM(ISCE.IdBodegafk) AS ID_BODEGA ,  
			BOD.Nombre AS DESCRIPCION_BODEGA,
			FORMAT(ISCE.FECHA_CREACION_ORDEN, 'dd/MM/yyyy') AS FECHA_CREACION_ORDEN,
			FORMAT(ISCE.FECHA_AUTO_RECH_1, 'dd/MM/yyyy')  AS FECHA_AUTO_RECH_1, 
			FORMAT(ISCE.FECHA_AUTO_RECH_2, 'dd/MM/yyyy')  AS FECHA_AUTO_RECH_2,
			ISCE.IdCorporativoSolicitafk AS ID_CORPORATIVO_SOLICiTA, 
			ISCE.IdCorporativoAR1_fk AS ID_CORPORATIVO_AUTO_RECH_1,
			ISCE.IdCorporativoAR2_fk AS ID_CORPORATIVO_AUTO_RECH_2 , 
			RTRIM(ISCE.ESTADO) AS ESTADO
			,CONCAT (CRP.Nombres, ' ', crp.Apellidos) USUARIO
			, CASE WHEN RTRIM(ISCE.ESTADO) = 'P' THEN 'PROCESO'
			WHEN ISCE.ESTADO = 'A1' THEN 'AUTORIZACION #1 '
			WHEN ISCE.ESTADO = 'A2' THEN 'AUTORIZACION #2 '
			WHEN ISCE.ESTADO = 'R1' THEN 'RECHAZADO #1 '
			WHEN ISCE.ESTADO = 'R2' THEN 'RECHAZADO #2 '
			END AS DESCRIPCION_ESTADO
			, CASE WHEN RTRIM(ISCE.ESTADO) = 'P' THEN 'success'
			WHEN RTRIM(ISCE.ESTADO) = 'A1' OR RTRIM(ISCE.ESTADO) = 'A2'  OR RTRIM(ISCE.ESTADO) = 'R1' OR  RTRIM(ISCE.ESTADO) = 'R2' THEN '#5D6D7E'
			END AS COLOR_BOTON_EDITAR
			, CASE WHEN RTRIM(ISCE.ESTADO) = 'A2'  OR RTRIM(ISCE.ESTADO) = 'R1' OR  RTRIM(ISCE.ESTADO) = 'R2' THEN 'success'
			WHEN RTRIM(ISCE.ESTADO) = 'P'  OR RTRIM(ISCE.ESTADO) = 'A1' THEN '#5D6D7E'
			END AS COLOR_BOTON_PDF,
			ISCE.IdProductoClasefk AS ID_PRODUCTOCLASE, 
			CASE WHEN ISCE.IdProductoClasefk = 'S' THEN 'SERVICIO'
				WHEN  ISCE.IdProductoClasefk = 'P' THEN 'BIEN'
			END AS TIPO_OC,
			ISCE.IdProductoSubfk AS ID_PRODUCTO_SUBCLASE
			,RTRIM(EMP.NOMBRE) AS EMPRESA_NOMBRE, RTRIM(EMP.Direccion) AS EMPRESA_DIRECCION, RTRIM(EMP.TELEFONOS) AS EMPRESA_TELEFONO, RTRIM(EMP.EMAIL) AS EMPRESA_CORREO
		FROM INV_SOLICITUD_COMPRA_ENC AS ISCE
		INNER JOIN INVENTARIO..DepartamentosINV AS DEPTO 
		ON ISCE.IdDepartamentofk=  DEPTO.CODIGO
		AND DEPTO.EMPRESA = @i_empresaBod
		LEFT JOIN INVENTARIO..BODEGAS AS BOD
		ON ISCE.IdBodegafk = BOD.CODIGO 
		INNER JOIN CONTADB..Corporativos  AS CRP
		ON ISCE.IdCorporativoSolicitafk  = CRP.Corporativo
		INNER JOIN CONTADB..Empresas AS EMP
		on ISCE.IdEmpresasfk = EMP.Codigo
		WHERE 
		ISCE.IdEmpresasfk = @i_empresa AND 
		ISCE.IdSolicitudEnc = @i_id_solicitud 
		ORDER BY ISCE.IdSolicitudEnc ASC
	END

	IF (@i_tipo_consulta = 'U') --- Por ubicacion
	BEGIN

		IF ( @i_id_bodega >  0) 
		BEGIN
			SELECT 
				
				ISCE.IdSolicitudEnc AS CODIGO
				, ISCE.IdEmpresasfk AS EMPRESA
				, RTRIM(ISCE.IdDepartamentofk) AS ID_DEPARTAMENTO
				, rtrim(DEPTO.Nombre) AS DESCRIPCION_DEPTO
				, RTRIM(ISCE.IdBodegafk) AS ID_BODEGA
				,  BOD.Nombre AS DESCRIPCION_BODEGA
				, FORMAT(ISCE.FECHA_CREACION_ORDEN, 'dd/MM/yyyy') AS FECHA_CREACION_ORDEN
				, FORMAT(ISCE.FECHA_AUTO_RECH_1, 'dd/MM/yyyy')  AS FECHA_AUTO_RECH_1
				, FORMAT(ISCE.FECHA_AUTO_RECH_2, 'dd/MM/yyyy')  AS FECHA_AUTO_RECH_2
				, ISCE.IdCorporativoSolicitafk AS ID_CORPORATIVO_SOLICITA
				, ISCE.IdCorporativoAR1_fk AS ID_CORPORATIVO_AUTO_RECH_1, ISCE.IdCorporativoAR2_fk AS ID_CORPORATIVO_AUTO_RECH_2 , RTRIM(ISCE.ESTADO) AS ESTADO
				,CONCAT (CRP.Nombres, ' ', crp.Apellidos) USUARIOS
				, CASE WHEN RTRIM(ISCE.ESTADO) = 'P' THEN 'PROCESO'
					WHEN ISCE.ESTADO = 'A1' THEN 'AUTORIZACION #1'
					WHEN ISCE.ESTADO = 'A2' THEN 'AUTORIZACION #2'
					WHEN ISCE.ESTADO = 'R1' THEN 'RECHAZADO #1'
					WHEN ISCE.ESTADO = 'R2' THEN 'RECHAZADO #2'
				END AS DESCRIPCION_ESTADO
				, CASE WHEN RTRIM(ISCE.ESTADO) = 'P' THEN 'success'
				WHEN RTRIM(ISCE.ESTADO) = 'A1' OR RTRIM(ISCE.ESTADO) = 'A2'  OR RTRIM(ISCE.ESTADO) = 'R1' OR  RTRIM(ISCE.ESTADO) = 'R2' THEN '#5D6D7E'
				END AS COLOR_BOTON_EDITAR

				, CASE WHEN RTRIM(ISCE.ESTADO) = 'A2'  OR RTRIM(ISCE.ESTADO) = 'R1' OR  RTRIM(ISCE.ESTADO) = 'R2' THEN 'success'
				WHEN RTRIM(ISCE.ESTADO) = 'P'  OR RTRIM(ISCE.ESTADO) = 'A1' THEN '#5D6D7E'
				END AS COLOR_BOTON_PDF
				, ISCE.IdProductoClasefk AS ID_PRODUCTOCLASE
				, CASE WHEN ISCE.IdProductoClasefk = 'S' THEN 'SERVICIO'
				  WHEN  ISCE.IdProductoClasefk = 'P' THEN 'BIEN'
			      END AS TIPO_OC
				, ISCE.IdProductoSubfk AS ID_PRODUCTO_SUBCLASE
				, RTRIM(EMP.NOMBRE) AS EMPRESA_NOMBRE
				, RTRIM(EMP.Direccion) AS EMPRESA_DIRECCION
				, RTRIM(EMP.TELEFONOS) AS EMPRESA_TELEFONO
				, RTRIM(EMP.EMAIL) AS EMPRESA_CORREO
				, ISCE.observaciones AS OBSERVACIONES
			FROM INV_SOLICITUD_COMPRA_ENC AS ISCE
			INNER JOIN INVENTARIO..DepartamentosINV AS DEPTO 
			ON ISCE.IdDepartamentofk=  DEPTO.CODIGO 
			AND DEPTO.EMPRESA = @i_empresaBod
			LEFT JOIN INVENTARIO..BODEGAS AS BOD
			ON ISCE.IdBodegafk = BOD.CODIGO 
			INNER JOIN CONTADB..Corporativos  AS CRP
			ON ISCE.IdCorporativoSolicitafk  = CRP.Corporativo
			INNER JOIN CONTADB..Empresas AS EMP
			on ISCE.IdEmpresasfk = EMP.Codigo
			WHERE ISCE.IdEmpresasfk = @i_empresa
			AND ISCE.IdDepartamentofk = @i_id_departamento  AND ISCE.IdBodegafk = @i_id_bodega 
			AND (@i_estado IS NULL OR CONCAT(RTRIM(ISCE.Estado),' ') LIKE '%' + LTRIM(RTRIM(@i_estado)) +'%')
			AND ISCE.fecha_creacion_orden BETWEEN CONVERT(datetime, @i_finicio, 102) AND CONVERT(datetime, @i_ffinal, 102)
			ORDER BY ISCE.IdSolicitudEnc ASC
		END
		ELSE
		BEGIN 
			SELECT 
				ISCE.IdSolicitudEnc AS CODIGO, 
				ISCE.IdEmpresasfk AS EMPRESA, 
				RTRIM(ISCE.IdDepartamentofk) AS ID_DEPARTAMENTO ,
				rtrim(DEPTO.Nombre) AS DESCRIPCION_DEPTO, 
				RTRIM(ISCE.IdBodegafk) AS ID_BODEGA ,  
				BOD.Nombre AS DESCRIPCION_BODEGA, 
				CONVERT(VARCHAR(10), 
				ISCE.FECHA_CREACION_ORDEN, 103)  AS FECHA_CREACION_ORDEN
				,ISCE.FECHA_AUTO_RECH_1, ISCE.FECHA_AUTO_RECH_2
				, ISCE.IdCorporativoSolicitafk AS ID_CORPORATIVO_SOLICITA
				, CONCAT (CRP.Nombres, ' ', crp.Apellidos) AS  USUARIO
				, ISCE.IdCorporativoAR1_fk AS ID_CORPORATIVO_AUTO_RECH_1
				, ISCE.IdCorporativoAR2_fk AS ID_CORPORATIVO_AUTO_RECH_2 , RTRIM(ISCE.ESTADO) AS ESTADO
				, CASE WHEN RTRIM(ISCE.ESTADO) = 'P' THEN 'PROCESO'
				WHEN ISCE.ESTADO = 'A1' THEN 'AUTORIZACION #1'
				WHEN ISCE.ESTADO = 'A2' THEN 'AUTORIZACION #2'
				WHEN ISCE.ESTADO = 'R1' THEN 'RECHAZADO #1'
				WHEN ISCE.ESTADO = 'R2' THEN 'RECHAZADO #2'
				END AS DESCRIPCION_ESTADO
				, CASE WHEN RTRIM(ISCE.ESTADO) = 'P' THEN 'success'
				WHEN RTRIM(ISCE.ESTADO) = 'A1' OR RTRIM(ISCE.ESTADO) = 'A2'  OR RTRIM(ISCE.ESTADO) = 'R1' OR  RTRIM(ISCE.ESTADO) = 'R2' THEN '#5D6D7E'
				END AS COLOR_BOTON_EDITAR
				, CASE WHEN RTRIM(ISCE.ESTADO) = 'A2'  OR RTRIM(ISCE.ESTADO) = 'R1' OR  RTRIM(ISCE.ESTADO) = 'R2' THEN 'success'
				WHEN RTRIM(ISCE.ESTADO) = 'P'  OR RTRIM(ISCE.ESTADO) = 'A1' THEN '#5D6D7E'
				END AS COLOR_BOTON_PDF,
				ISCE.IdProductoClasefk AS ID_PRODUCTOCLASE, 
				CASE WHEN ISCE.IdProductoClasefk = 'S' THEN 'SERVICIO'
					WHEN  ISCE.IdProductoClasefk = 'P' THEN 'BIEN'
				END AS TIPO_OC,
				ISCE.IdProductoSubfk AS ID_PRODUCTO_SUBCLASE
				,RTRIM(EMP.NOMBRE) AS EMPRESA_NOMBRE, RTRIM(EMP.Direccion) AS EMPRESA_DIRECCION, RTRIM(EMP.TELEFONOS) AS EMPRESA_TELEFONO, RTRIM(EMP.EMAIL) AS EMPRESA_CORREO
				, ISCE.observaciones AS OBSERVACIONES
				FROM INV_SOLICITUD_COMPRA_ENC AS ISCE
				INNER JOIN INVENTARIO..DepartamentosINV AS DEPTO 
				ON ISCE.IdDepartamentofk=  DEPTO.CODIGO 
				AND DEPTO.EMPRESA = @i_empresaBod
				LEFT JOIN INVENTARIO..BODEGAS AS BOD
				ON ISCE.IdBodegafk = BOD.CODIGO 
				INNER JOIN CONTADB..Corporativos  AS CRP
				ON ISCE.IdCorporativoSolicitafk  = CRP.Corporativo
				INNER JOIN CONTADB..Empresas AS EMP
				on ISCE.IdEmpresasfk = EMP.Codigo
			WHERE 
			ISCE.IdEmpresasfk = @i_empresa 
			AND ISCE.IdDepartamentofk = @i_id_departamento  AND ISCE.IdBodegafk = @i_id_bodega 
				AND (@i_estado IS NULL OR CONCAT(RTRIM(ISCE.Estado),' ') LIKE '%' + LTRIM(RTRIM(@i_estado)) +'%')
				AND CONVERT(DATE, ISCE.fecha_creacion_orden, 102) BETWEEN CONVERT(DATE, @i_finicio, 102) AND CONVERT(DATE, @i_ffinal, 102)
			ORDER BY ISCE.IdSolicitudEnc ASC
			
		END
	END 
END


-->>  Crear el Procedimiento Almacenado  -  sp_inv_solicitud_det_lista

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[sp_inv_solicitud_det_lista]    Script Date: 13/03/2024 12:23:41 p.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/********************************************************************************************/
/*	CREADO POR:  		 Edvin Geovany Jucup  	Numero de Rational:  330014  	 			*/
/*	FECHA CREACIÓN:		 20/07/2023									                		*/
/*	PRODUCTO - PROCESO:  SOLICITUD DE COMPRA, AUTORIZACIÓN SOLICITUD, COTIZACIÓN,			*/
/*	COMPARATIVO									 											*/
/**************************************** DESCRIPCION ***************************************/
/*	Este procedimiento tiene como objetivo consultar los datos del detalle					*/
/*	de una solicitud de compra.																*/
/*												 											*/
/*																							*/
/*************************************** MODIFICACIÓN ***************************************/
/*	  FECHA				 RESPONSABLE				          DESCRIPCION		      		*/
/* JUL - SEP 2023  Blanca Beatríz Cetino Rodríguez	Se agrega empresa bodega para productos */
/********************************************************************************************/


CREATE PROCEDURE [dbo].[sp_inv_solicitud_det_lista]

	@i_empresa CHAR(3) = null,
	@i_bodega  CHAR(3) = '',
	@i_id_solicitud INT =0,
	@i_TipoConsulta CHAR(1) = 0

AS
DECLARE @c_existecodigo CHAR(2)  = '';
DECLARE @c_resultado INT  = 0;
DECLARE @c_nombre_tabla VARCHAR(100) = 'Consulta Solicitud Compra Encabezado';
DECLARE	@c_EsUnica_Empresa VARCHAR		= '';
DECLARE	@c_Id_Generado_Auto INT		= 0;
DECLARE	@ErrRaise	VARCHAR(99)	= '';
DECLARE	@ErrRaiseNum INT		= 0

BEGIN	
	SET NOCOUNT ON;

	IF(@i_TipoConsulta = 'T')
	BEGIN
		SELECT
			SE.IdSolicitudDet AS id_interno,
			'0' AS correlativo,
			SE.IdProductofk AS id_producto,
			RTRIM(PR.NOMBRE) AS nombre,
			CAST(CAST(SE.cantidad AS DECIMAL (18, 5)) AS float) AS cantidad,
			SE.IdEmpresafk AS id_empresa,
			SE.IdSolicitudEncfk
		FROM INV_SOLICITUD_COMPRA_DET AS SE
		INNER JOIN INVENTARIO..PRODUCTOS AS PR
			ON SE.IdProductofk = PR.CODIGO
			AND PR.Empresa = @i_bodega
		WHERE SE.IdSolicitudEncfk = @i_id_solicitud
		AND SE.IdEmpresafk = @i_empresa
		AND SE.ESTADO = 'S'
		ORDER BY SE.IdSolicitudDet ASC
	END
END

-->>  Crear el Procedimiento Almacenado -  sp_inv_departamento_lista

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[sp_inv_departamento_lista]    Script Date: 12/03/2024 09:36:51 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


/********************************************************************************************/
/*	CREADO POR:  		 Edvin Geovany Jucup  	Numero de Rational:  330014  	 			*/
/*	FECHA CREACIÓN:		 20/07/2023									                		*/
/*	PRODUCTO - PROCESO:  SOLICITUD DE COMPRA, COTIZACIÓN, COMPARATIVO, AUTORIZACIONES,		*/
/*	ORDEN DE COMPRA AUTOMATICA Y MANUAL		    											*/
/**************************************** DESCRIPCION ***************************************/
/*	Este procedimiento tiene como objetivo consultar los datos de los departamentos.		*/
/*												 											*/
/*																							*/
/*************************************** MODIFICACIÓN ***************************************/
/*		FECHA				 RESPONSABLE						DESCRIPCION					*/
/*	JUL - SEP 2023		Blanca Beatríz Cetino Rodríguez	 Se agrega empresa para deptos		*/
/*	NOV - 14  2023 	Blanca Beatríz Cetino Rodríguez	 Se agrega cuenta de transito y hospital*/
/*					Se obtiene el nombre de hospital asignado.								*/
/********************************************************************************************/



CREATE PROCEDURE [dbo].[sp_inv_departamento_lista]			
			

@i_empresa VARCHAR(3) = '',
@i_tipo VARCHAR(1) = 'T',
@i_estado CHAR(1) = ''
			
AS		
Declare @resultado INT = 0;	
BEGIN			

	IF (@i_tipo = 'T')
	BEGIN
		SELECT
		  DEPT.CODIGO,
		  RTRIM(UPPER(DEPT.NOMBRE)) AS NOMBRE,
		  DEPT.ACTIVA,
		  RTRIM(DEPT.CUENTAENTRANSITO) AS CUENTAENTRANSITO,
		  DEPT.EMPRESA AS ID_EMPRESA,
		  '' AS ID_RESPONSABLE,
		  '' AS NOMBRE_RESPONSABLE,
		  '' AS ID_CENTRO_COSTO,
		  '' AS CENTRO_COSTO,
		  '' AS NOMBRE_EMPRESA,
		 DEPT.HOSPITAL AS ID_HOSPITAL,
		 EMP.NOMBRE AS NOMBRE_HOSPITAL
		FROM INVENTARIO..DepartamentosINV AS DEPT
		LEFT JOIN NominaDB.dbo.EmpHospital  EMP With (nolock)
			ON DEPT.HOSPITAL = EMP.CODIGO
			AND EMP.Empresa='MED'
			AND EMP.Activo='S' 
			AND EMP.EmpresaReal IS NOT NULL
			AND EMP.ReferenciaBaseFel IS NOT NULL
		WHERE DEPT.EMPRESA = @i_empresa
		AND (@i_estado IS NULL OR CONCAT(RTRIM(DEPT.ACTIVA),' ') LIKE '%' + LTRIM(RTRIM(@i_estado)) +'%')
			
	END
	ELSE
	BEGIN
		SELECT
		  DEPT.CODIGO,
		  RTRIM(UPPER(DEPT.NOMBRE)) AS NOMBRE,
		  DEPT.ACTIVA,
		  RTRIM(DEPT.CUENTAENTRANSITO) AS CUENTAENTRANSITO,
		  DEPT.EMPRESA AS ID_EMPRESA,
		  '' AS ID_RESPONSABLE,
		  '' AS NOMBRE_RESPONSABLE,
		  '' AS ID_CENTRO_COSTO,
		  '' AS CENTRO_COSTO,
		  '' AS NOMBRE_EMPRESA,
		  '' AS ID_HOSPITAL,
		  '' AS NOMBRE_HOSPITAL
		FROM INVENTARIO..DepartamentosINV AS DEPT
		WHERE DEPT.EMPRESA = @i_empresa
		AND DEPT.activa = 'S'
	END


END


-->>  Crear el Procedimiento Almacenado  -  sp_inv_solicitud_autorizacion_finalizar 

USE [HOSPITAL]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



/********************************************************************************************/
/*  CREADO POR:  		 Edvin Geovany Jucup  	Numero de Rational:  330014  	 			*/
/*	FECHA CREACIÓN:		 20/07/2023									                		*/
/*	PRODUCTO - PROCESO:  AUTORIZACIÓN SOLICITUD	 -  COMPARATIVO - ORDEN DE COMPRA			*/
/**************************************** DESCRIPCION ***************************************/
/*	Este procedimiento tiene como objetivo finalizar (Autorizado/ rechazado) una solicitud  */
/*  de compra/ Cotizacion / Orden de compra, crea cotizaciones luego de ser autorizada		*/
/*																							*/
/*************************************** MODIFICACIÓN ***************************************/
/*	FECHA	 JUL - SEP 2023				RESPONSABLE		Blanca Beatríz Cetino Rodríguez		*/
/* DESCRIPCION	Se agrega campos necesarios, se centraliza un select para asignación a		*/
/*variables. Se agrga validación para ordenes de compra de servicio							*/
/********************************************************************************************/



CREATE PROCEDURE [dbo].[sp_inv_solicitud_autorizacion_finalizar] 

@i_empresa CHAR(3) = '',
@i_corporativo INT = 0,
@i_id_historial_auto INT = 0,
@i_id_solicitud_enc INT = 0,
@i_estado VARCHAR(2) = '',
@i_id_documento INT = 0,
@i_num_documento INT = 0,
@i_observaciones TEXT = '',
@i_id_cotizacion_auto INT = 0,
@i_id_orden_compra_auto INT = 0,
@i_tipo CHAR(10) = '',
@i_dias_credito INT = 0,
@i_ortopedia CHAR(1) = ''



AS
	DECLARE @c_existecodigo CHAR(2) = '';
	DECLARE @c_resultado INT = 0;
	DECLARE @c_nombre_tabla VARCHAR(100) = 'Finalización autorización';
	DECLARE @c_corporativo_siguiente INT = 0;
	DECLARE @c_estado VARCHAR(2) = '';
	DECLARE @c_EsUnica_Empresa varchar = '';
	DECLARE @c_Id_Generado_Auto INT = 0;
	DECLARE @ErrRaise VARCHAR(99) = '';
	DECLARE @ErrRaiseNum INT = 0
	DECLARE @c_enc_cant_lineas INT = 0;
	DECLARE @c_enc_id_depto CHAR(2) = 0;
	DECLARE @c_enc_id_bodega INT = 0;
	DECLARE @c_enc_id_producto_clase CHAR(1) = 0;
	DECLARE @c_enc_id_producto_sub INT = 0;
	DECLARE @c_enc_id_corporativo_sol INT = 0;
	DECLARE @c_det_empresa CHAR(3) = '';
	DECLARE @c_det_producto CHAR(13) = 0;
	DECLARE @c_det_cantidad DECIMAL (38, 3) = 0;
	DECLARE @c_det_precio_unitario MONEY = 0;
	DECLARE @c_det_subtotal MONEY = 0;
	DECLARE @c_cot_proveedor_selec CHAR(12) = '';
	DECLARE @c_cot_total MONEY = 0;
	DECLARE @s_clase INT = 0;

	
	--CONSTANTES PARA ORDEN DE COMPRA.
	BEGIN
	SET NOCOUNT ON;

	--Inicio de transaccion 
	BEGIN TRAN


		--Inicio de excepcion
		BEGIN TRY

			--Actualizacion del historial de autorizaciones AUTORIZADO / RECHAZADO
			IF (@i_id_documento = '3')
			BEGIN
				UPDATE inv_autorizacion_documento_historial
				SET ESTADO = @i_estado,
					fecha_hora_autorizo = GETDATE(),
					observaciones = @i_observaciones
				WHERE IdAutorizacionHistorial = @i_id_historial_auto
				AND IdOrdenCompraEncfk = @i_id_orden_compra_auto;
			END
			ELSE
			BEGIN -- IdDocumento 1 = Solicitud de compra  / IdDocumento = 2 Cotizaciones
				UPDATE inv_autorizacion_documento_historial
				SET ESTADO = @i_estado,
					fecha_hora_autorizo = GETDATE(),
					observaciones = @i_observaciones
				WHERE IdAutorizacionHistorial = @i_id_historial_auto;
			END


	/****************************PROCESOS PARA SOLICITUD DE COMPRA *************************************************/
	IF (@i_id_documento = '1')
	BEGIN
		-- Actualizacion de la solicitud encabezado
		SET @c_estado = (CONCAT(@i_estado, @i_num_documento))

		IF (@i_num_documento = 1)
		BEGIN
			UPDATE inv_solicitud_compra_enc
			SET fecha_auto_rech_1 = GETDATE(),
				IdCorporativoAR1_fk = @i_corporativo,
				estado = @c_estado,
				fecha_auto_rech_2 = GETDATE(),
				IdCorporativoAR2_fk = @i_corporativo
			WHERE IdSolicitudEnc = @i_id_solicitud_enc

			SELECT 	'0' AS 'codigo', @c_nombre_tabla AS 'descripcion',	@c_corporativo_siguiente AS 'resultado';

		COMMIT TRAN;
		END
		ELSE
		BEGIN
			UPDATE inv_solicitud_compra_enc
			SET fecha_auto_rech_2 = GETDATE(),
				IdCorporativoAR2_fk = @i_corporativo,
				estado = @c_estado
			WHERE IdSolicitudEnc = @i_id_solicitud_enc
			
			
			/*SI ES RECHAZADO  CORROMPE TODO EL PROCESO***/
			IF (@i_estado = 'R')
			BEGIN
				SELECT 	'0' AS 'codigo', @c_nombre_tabla AS 'descripcion', 	0 AS 'resultado';
				COMMIT TRAN;
			END
		END

	END

	/****************************PROCESO FINALIZACION SOLICITUD DE COMPRA HE INICIO COTIZACIONES  *************************************************/
	IF (@i_id_documento = '1' 	AND @i_num_documento = '1'	AND @i_estado = 'A')
	BEGIN
	--Validación si existe responsable para cotización
			SET @c_corporativo_siguiente = (SELECT TOP 1
												IDRESPONSABLEFK
											FROM inv_autorizacion_documento
											WHERE IDEMPRESAFK = @i_empresa
											AND IDDOCUMENTOFK = '2'
											AND NUM_DOCUMENTO = '1'
											AND ESTADO = 'S')
		IF (@c_corporativo_siguiente > 0)
		BEGIN

			INSERT INTO inv_autorizacion_documento_historial 
				(IdCorporativofk
				, IdSolicitudEncfk
				, fecha_hora_inserto
				, IdDocumentofk
				, num_documento)
			VALUES (@c_corporativo_siguiente
				, @i_id_solicitud_enc
				, GETDATE()
				, '2'
				, '1')

			--Insertar notificacion para el siguiente responsable
			
			/***************CURSOR***************/

			SELECT TOP 1	
				@c_enc_cant_lineas = cant_lineas,
				@c_enc_id_depto =  IdDepartamentofk,
				@c_enc_id_bodega = IdBodegafk,
				@c_enc_id_producto_clase = IdProductoClasefk,
				@c_enc_id_producto_sub = IdProductoSubfk,
				@c_enc_id_corporativo_sol = IdCorporativoSolicitafk
			FROM inv_solicitud_compra_enc 
			WHERE IdSolicitudEnc = @i_id_solicitud_enc

			


			INSERT INTO inv_cotizacion_enc (IdSolicitudEncfk, IdEmpresasfk, total, cant_lineas, observaciones, IdDepartamentofk, IdBodegafk, IdProductoClasefk, IdProductoSubfk,  fecha_creacion, IdCorporativoSolicitafk, estado, tipo, cant_pagos_propuesta, fecha_maxima_recepcion, IdProveedorfk)
			VALUES (@i_id_solicitud_enc, @i_empresa, 0, @c_enc_cant_lineas, '', @c_enc_id_depto, @c_enc_id_bodega, @c_enc_id_producto_clase, @c_enc_id_producto_sub, GETDATE(), @c_enc_id_corporativo_sol, 'P', 'I', 0, GETDATE(), 'BIOMA')

			--Obtiene el ID generado				 
			SET @c_Id_Generado_Auto = (SELECT	@@Identity);


			--Creación detalle cotización
			DECLARE CURSOR_DETALLE CURSOR FOR
			
			SELECT
				IDEMPRESAFK,
				IDPRODUCTOFK,
				CANTIDAD
			FROM inv_solicitud_compra_det
			WHERE IdSolicitudEncfk = @i_id_solicitud_enc

			OPEN CURSOR_DETALLE;
			FETCH NEXT FROM CURSOR_DETALLE INTO
			@c_det_empresa,
			@c_det_producto,
			@c_det_cantidad;

			WHILE @@FETCH_STATUS = 0
			BEGIN
			INSERT INTO inv_cotizacion_det (IdCotizacionEncfk, IdEmpresafk, IdProductofk, cantidad_solicitada, precio_unitario, sub_total)
				VALUES (@c_Id_Generado_Auto, @c_det_empresa, @c_det_producto, @c_det_cantidad, 0, 0)

			FETCH NEXT FROM CURSOR_DETALLE INTO
			@c_det_empresa,
			@c_det_producto,
			@c_det_cantidad;

			END;
			CLOSE CURSOR_DETALLE;
		/***************CURSOR***************/

		COMMIT TRAN;
		SELECT	'0' AS 'codigo',	@c_nombre_tabla AS 'descripcion',	@c_corporativo_siguiente AS 'resultado';

		END
		ELSE
		BEGIN

			---No procede porque no existe responsable.
			SELECT	'1' AS 'codigo',	@c_nombre_tabla AS 'descripcion',	'No Finalizado, no existe responsable autorización cotización' AS 'resultado';
			ROLLBACK TRAN
		END
	END

	/****************************PROCESO COTIZACION HE INICIO ORDEN DE COMPRA  *************************************************/
	IF (@i_id_documento = '2')
	BEGIN
			DECLARE @c_cant_cot_autorizadas INT = 0;

		SET @c_cant_cot_autorizadas = (SELECT	COUNT(*)	
									FROM inv_cotizacion_enc	
									WHERE IdSolicitudEncfk = @i_id_solicitud_enc	
									AND Tipo = 'E'	AND estado = 'A')
	
		IF (@c_cant_cot_autorizadas > 0)
		BEGIN
			SET @i_estado = 'A'
		END

		IF (@i_estado = 'A')
		BEGIN
			SET @c_corporativo_siguiente = (SELECT TOP 1	IDRESPONSABLEFK		
											FROM inv_autorizacion_documento		
											WHERE IDEMPRESAFK = @i_empresa	
											AND IDDOCUMENTOFK = '3'		
											AND NUM_DOCUMENTO = '1'		
											AND ESTADO = 'S')
			IF (@c_corporativo_siguiente > 0)
			BEGIN

			--GENERA DISTINTAS NOTIFICACIONES Y AUTORIZACIONES, PARA LAS ORDENES DE COMPRA
			DECLARE @C_ID_ORDEN_COMPRA INT = 0;
						

			DECLARE CursorOrdenesCompra CURSOR FOR
			SELECT
				IdOrdenCompraEnc
			FROM inv_orden_compra_enc
			WHERE IdSolicitudEncfk = @i_id_solicitud_enc
		
			OPEN CursorOrdenesCompra
		
			FETCH NEXT FROM CursorOrdenesCompra INTO @C_ID_ORDEN_COMPRA
		
			WHILE @@fetch_status = 0
			BEGIN

				INSERT INTO inv_autorizacion_documento_historial (IdCorporativofk, IdSolicitudEncfk, fecha_hora_inserto, IdDocumentofk, num_documento, IdOrdenCompraEncFk)
				VALUES (@c_corporativo_siguiente, @i_id_solicitud_enc, GETDATE(), '3', '1', @C_ID_ORDEN_COMPRA)

			


				FETCH NEXT FROM CursorOrdenesCompra INTO @C_ID_ORDEN_COMPRA
			--FINALIZACIÓN CURSOR ORDEN DE COMPRA
			END
			CLOSE CursorOrdenesCompra
			DEALLOCATE CursorOrdenesCompra

			COMMIT TRAN;
			SELECT		'0' AS 'codigo',	@c_nombre_tabla AS 'descripcion',	@c_corporativo_siguiente AS 'resultado';
			END
			ELSE
			BEGIN
			---No procede porque no existe responsable.
			SELECT	'1' AS 'codigo',	@c_nombre_tabla AS 'descripcion',	'No Finalizado, no existe responsable autorización cotización' AS 'resultado';
			ROLLBACK TRAN
			END
		END
		ELSE
		BEGIN
			COMMIT TRAN;
			SELECT 	'0' AS 'codigo',	@c_nombre_tabla AS 'descripcion',	'0' AS 'resultado';
		END


	/*********FINALIZACION AUTORIZACION / RECHAZO COTIZACION *************/
	END

	/****************************PROCESO FINALIZACION ORDEN DE COMPRA  *************************************************/
	IF (@i_id_documento = '3')
	BEGIN

		IF (@i_num_documento = 1 )
		BEGIN
			
				UPDATE INV_ORDEN_COMPRA_ENC
				SET fecha_auto_rech_1 = GETDATE(),
					estado = @i_estado,
					IdCorporativoAR1_fk = @i_corporativo
				WHERE IdOrdenCompraEnc = @i_id_orden_compra_auto
					AND IdEmpresasfk = @i_empresa;
			

			UPDATE inv_solicitud_compra_enc
			SET estado = CONCAT(@i_estado, @i_num_documento)
			WHERE IdSolicitudEnc = @i_id_solicitud_enc
				
			COMMIT TRAN;

			IF (@i_estado = 'A')
			BEGIN
				SELECT	'0' AS 'codigo',	'Se ha autorizado' AS 'descripcion',	'0' AS 'resultado';
			END 
			ELSE
			BEGIN 
				SELECT	'0' AS 'codigo',	'Se ha rechazado' AS 'descripcion',	'0' AS 'resultado';
			END
				
		END
		ELSE
		IF (@i_num_documento = 2)
		BEGIN
			
			UPDATE INV_ORDEN_COMPRA_ENC
			SET estado = @i_estado,
				fecha_auto_rech_2 = GETDATE(),
				IdCorporativoAR2_fk = @i_corporativo
			WHERE IdOrdenCompraEnc = @i_id_orden_compra_auto
			AND IdEmpresasfk = @i_empresa;


			UPDATE inv_solicitud_compra_enc
			SET estado = CONCAT(@i_estado, @i_num_documento)
			WHERE IdSolicitudEnc = @i_id_solicitud_enc

			COMMIT TRAN;

			IF (@i_estado = 'A')
			BEGIN
				SELECT	'0' AS 'codigo',	'Se ha autorizado' AS 'descripcion',	'0' AS 'resultado';
			END 
			ELSE
			BEGIN 
				SELECT	'0' AS 'codigo',	'Se ha rechazado' AS 'descripcion',	'0' AS 'resultado';
			END
		END

	END
	END TRY
	BEGIN CATCH
		ROLLBACK TRAN
		SET @ErrRaise = ERROR_MESSAGE()
		SET @ErrRaiseNum = ERROR_NUMBER()

		RAISERROR (@ErrRaise, 16, @ErrRaiseNum)
	END CATCH

 END

-->>  Crear el Procedimiento Almacenado -  sp_inv_autorizacion_orden_compra_lista

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[sp_inv_autorizacion_orden_compra_lista]    Script Date: 12/03/2024 09:43:36 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



/********************************************************************************************/
/*  CREADO POR:  		 Edvin Geovany Jucup  	Numero de Rational:  330014  	 			*/
/*	FECHA CREACIÓN:		 20/07/2023									                		*/
/*	PRODUCTO - PROCESO:  AUTORIZACIÓN ORDEN DE COMPRA			*/
/**************************************** DESCRIPCION ***************************************/
/*	Este procedimiento tiene como objetivo consultar los datos de los responsables			*/
/*  encargados de autorizar solicitud de compras, cotizaciones y ordenes de compra			*/
/*																							*/
/*************************************** MODIFICACIÓN ***************************************/
/*								FECHA					RESPONSABLE							*/
/*							JUL - SEP 2023		Blanca Beatríz Cetino Rodríguez				*/
/* DESCRIPCION																				*/
/* Se agrega campos necesarios, datos de movimiento, usuarios necesarios y observaciones	*/
/* Se agrega campos de validacion de órdenes de compra.										*/
/********************************************************************************************/




CREATE PROCEDURE [dbo].[sp_inv_autorizacion_orden_compra_lista]			


	
@i_fecha_inicio date = '',
@i_fecha_fin date = '',
@i_empresa varchar(3) = '',
@i_corporativo int = 0,
@i_estado char(2) = '', -- T = Consultar todos los doc / E = Consultar por doc y número. 
@i_departamento char(02) = '',
@i_proveedor char(12) = '',
@i_repcionada char(1) = '',
@i_dOrden int = 0,
@i_aOrden int = 0,
@i_tipoSeleccion char(1) = '',
@i_empresaBod char(3) = '',
@i_empresaSucursal char(3) = ''

AS			


BEGIN			

	IF (@i_fecha_inicio = '' OR  @i_fecha_inicio is null)
	BEGIN
		set @i_fecha_inicio  = ('01/02/2021');
	END
	IF (@i_fecha_fin = '' OR  @i_fecha_fin is null)
	BEGIN
		set @i_fecha_fin  = (GETDATE());
	END

	

		SELECT PROV.CODIGO, 
			ICE.IDORDENCOMPRAENC, 
			ICE.IDSOLICITUDENCFK, 
			rtrim(ICE.IDEMPRESASFK) as IDEMPRESASFK, 
			ICE.TOTAL,
			ICE.CANT_LINEAS, 
			ICE.OBSERVACIONES, 
			RTRIM(ICE.IDDEPARTAMENTOFK) AS IDDEPARTAMENTOFK,
			DEPT.NOMBRE AS DESCRIPCION_DEPTO,
			'' AS DESCRIPCION_BODEGA,
			SC.OBSERVACIONES,
			CASE WHEN RTRIM(ICE.IDPRODUCTOCLASEFK) = 'P' THEN 'BIEN'
			WHEN RTRIM(ICE.IDPRODUCTOCLASEFK) = 'S' THEN 'SERVICIO'
			END AS IDPRODUCTOCLASEFK, 
			RTRIM(ICE.IDPRODUCTOSUBFK) AS IDPRODUCTOSUBFK, 
			ICE.FECHA_MAXIMA_RECEPCION, 
			RTRIM(ICE.IDPROVEEDORFK) AS IDPROVEEDORFK, 
			PROV.NOMBRE AS DESCRIPCION_PROVEEDOR, 
			PROV.NIT ,
			RTRIM( ICE.estado) AS ESTADO,
			CASE WHEN  RTRIM(ICE.estado)  = 'P' THEN 'PROCESO'
			WHEN RTRIM(ICE.estado)  = 'A' THEN 'AUTORIZADO'
			WHEN RTRIM(ICE.estado)  = 'C' THEN 'ANULADO'
			ELSE 'RECHAZADO'
			END AS DESCRIPCION_ESTADO,
			ICE.IdCorporativoAR1_fk  USUARIO_AUTORIZO,
			'' AS EMPRESA_DIRECCION,
			'' as EMPRESA_CORREO,
			'' AS EMPRESA_TELEFONO,
			FORMAT(ICE.fecha_creacion, 'dd/MM/yyyy') AS FECHA_CREACION_ORDEN,
			ADH.IDAUTORIZACIONHISTORIAL,
			ADH.num_documento AS NUMERO_DOCUMENTO,
			ADH.IdDocumentofk AS ID_DOCUMENTO_,
			'' AS NOMBRE_EMPRESA,
			RTRIM(PROV.Nit) AS PROV_NIT,
			RTRIM(PROV.CONTACTO) AS PROV_CONTACTO,
			RTRIM(PROV.EMAIL) AS PROV_CORREO ,
			RTRIM(CRP.Correo) AS CORREO_RESPONSABLE,
			ICE.RECEPCIONADA VALIDADA,
			CASE WHEN  RTRIM(ICE.RECEPCIONADA)  = 'S' THEN 'VALIDADA'
			WHEN RTRIM( ICE.RECEPCIONADA)  = 'N' THEN 'NO VALIDADA'
			END AS DESCRIP_VALIDADA,
			ICE.VALIDACION,
			ICE.RevisoConformidad USUARIO,
			ICE.ObservacionesValidacion OBSERVACION_VALIDACION,
			ICE.dias_credito_propuesta DIAS,
			ICE.cant_pagos_propuesta PAGOS,
			ICE.fecha_auto_rech_1  FECHA_UTORIZACION,
			ICE.orden_ortopedia ORTOPEDIA
		FROM INV_ORDEN_COMPRA_ENC AS ICE
		INNER JOIN INV_SOLICITUD_COMPRA_ENC AS SC
			ON ICE.IdSolicitudEncfk = SC.IdSolicitudEnc
		RIGHT JOIN INV_AUTORIZACION_DOCUMENTO_HISTORIAL AS ADH
			ON ICE.IdSolicitudEncfk = ADH.IdSolicitudEncfk
			AND ICE.IdOrdenCompraEnc = ADH.IDORDENCOMPRAENCFK
			AND  ADH.IdDocumentofk = 3 
		INNER JOIN INVENTARIO..DepartamentosINV  DEPT
			ON ICE.IDDEPARTAMENTOFK = DEPT.CODIGO
			AND DEPT.EMPRESA = @i_empresaBod
		INNER JOIN INVENTARIO..PROVEEDORES  AS PROV
			ON ICE.IDPROVEEDORFK = PROV.CODIGO
			AND PROV.EMPRESA = ICE.ProveedorEmpresa
		INNER JOIN CONTADB..CORPORATIVOS AS CRP
			ON CRP.CORPORATIVO =  ADH.IdCorporativofk
		INNER JOIN INV_AUTORIZACION_DOCUMENTO AD
			ON AD.IdDocumentofk =  3 
			AND AD.estado = 'S' 
			AND AD.IDEMPRESAFK = @i_empresa
			AND AD.IdResponsablefk = @i_corporativo
			AND AD.IdProductoClasefk = ICE.IdProductoClasefk
		WHERE  ICE.IDEMPRESASFK = @i_empresa
			AND (@i_estado IS NULL OR CONCAT(RTRIM(ICE.estado),' ') LIKE '%' + LTRIM(RTRIM(@i_estado )) +'%')
			AND (@i_corporativo IS NULL OR CONCAT(RTRIM(IdCorporativofk),' ') LIKE '%' + LTRIM(RTRIM(@i_corporativo)) +'%')
			AND (@i_departamento IS NULL OR CONCAT(RTRIM(ICE.IDDEPARTAMENTOFK),' ') LIKE '%' + LTRIM(RTRIM(@i_departamento)) +'%')
			AND (@i_proveedor IS NULL OR CONCAT(RTRIM(PROV.CODIGO),' ') LIKE '%' + LTRIM(RTRIM(@i_proveedor)) +'%')
			AND (@i_repcionada IS NULL OR CONCAT(RTRIM(ICE.RECEPCIONADA),' ') LIKE '%' + LTRIM(RTRIM(@i_repcionada)) +'%')
			AND (ICE.IdOrdenCompraEnc >= @i_dOrden OR @i_dOrden = 0)
			AND (ICE.IdOrdenCompraEnc <= @i_aOrden OR @i_aOrden = 0)
			AND (@i_tipoSeleccion IS NULL OR CONCAT(RTRIM(ICE.IdProductoClasefk),' ') LIKE '%' + LTRIM(RTRIM(@i_tipoSeleccion)) +'%')
			AND convert(varchar,  ICE.FECHA_CREACION, 23)  >= @i_fecha_inicio 
			AND convert(varchar,  ICE.FECHA_CREACION, 23)  <= @i_fecha_fin
			ORDER BY ICE.IDORDENCOMPRAENC DESC
END			




-->>  Crear el Procedimiento Almacenado  -  SpHisOCServiciosHnos
 
USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[SpHisOCServiciosHnos]    Script Date: 12/03/2024 10:33:03 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


/********************************************************************************************/
/*	CREADO POR:  		 Blanca Beatríz Cetino Rodríguez  	Número de Rational:  RTC-330014	*/
/*	FECHA CREACIÓN:		 08/03/2023 / 								                		*/
/*	PRODUCTO - PROCESO:  ORDEN DE COMPRA MANUAL - HONORARIOS			    				*/
/**************************************** DESCRIPCION ***************************************/
/* Este procedimiento tiene como objetivo obtener información para ordenes de compra		*/
/* Honorarios, carga de admisiones, consulta proveedores, validación de existencia factura	*/
/* Carga otros pagos e ingresa la orden de compra honorarios.   							*/
/*																							*/
/*************************************** MODIFICACIÓN ***************************************/
/*			FECHA					RESPONSABLE					DESCRIPCION					*/
/*																							*/
/********************************************************************************************/


CREATE PROCEDURE [dbo].[SpHisOCServiciosHnos]
	
	 @IEmpresaU CHAR(3) = ''
	, @IEmpresa  CHAR(3) = ''
	, @ISucursal CHAR(3) = ''
	, @IProductoClase CHAR(1)= ''
	, @IProductoSubClase INT = 0
	, @IFechaInicio DATE = ''
	, @IFechaFin DATE = ''
	, @IProveedor CHAR(12) = ''
	, @ISerieF  CHAR(3) = ''	
	, @IFactura  INT = 0	
	, @IOpcion  CHAR(1) = ''
	, @ISubOpcion CHAR(1) = ''


AS
BEGIN

	DECLARE  @ErrRaise	VARCHAR(99)	= ''
			, @ErrRaiseNum INT = 0
			, @Existe INT = 0
			, @CorrelativoOC INT = 0

	--Validando Sucursal 
	IF (@ISucursal IS NULL OR @ISucursal = 'HLA')
	BEGIN
		SET @ISucursal = @IEmpresa
	END
	
	--Servicios
	IF (@IProductoClase = 'S')
	BEGIN
		--Honorarios
		IF (@IProductoSubClase = 2)
		BEGIN
			IF(@IOpcion = 'C')
			BEGIN
				IF (@ISubOpcion = '1')
				BEGIN
					--CARGA DE ADMISIONES
					--QryHonosMed
					
					SELECT
						H.Admision,
						H.SerieA,
						H.NAdmision,
						H.Cod_Paciente,
						H.Paciente,
						SUM(H.valor) AS Valor,
						H.proveedor
					FROM (
					SELECT DISTINCT
							A.Salida,
							(C.SerieAdmision + ' - ' + CONVERT(varchar, C.Admision)) AS Admision,
							C.SerieAdmision AS SerieA,
							C.Admision AS NAdmision,
							 A.Paciente AS Cod_Paciente,
							(LTRIM(RTRIM(P.Nombre)) + ' ' + LTRIM(RTRIM(P.Apellido))) AS Paciente,
							C.NumeroSerie,
							C.Linea,
							C.Orden,
							C.valor,
							AJ.proveedor AS proveedor
						FROM Admisiones(NOLOCK) A
						INNER JOIN Cargos C (NOLOCK)
							ON C.Empresa = C.Empresa
							AND A.Serie = C.SerieAdmision
							AND A.Codigo = C.Admision
							AND C.Categoria = 97
							AND C.TipoOrden = 'AJE'
						INNER JOIN Facturas(NOLOCK) F
							ON  A.Empresa = F.Empresa 
							AND A.Serie = F.SerieAdmision
							AND A.Codigo = F.Admision
							AND F.saldo = 0
							AND F.status = 'P'
						INNER JOIN Pacientes(NOLOCK) P
							ON P.Empresa = A.Empresa
							AND P.codigo = A.Paciente
						INNER JOIN Ajenos(NOLOCK) AJ
							ON AJ.Empresa = A.Empresa
							AND AJ.Codigo = C.ajeno
						INNER JOIN INVENTARIO..Proveedores(NOLOCK) PR
							ON PR.Empresa = A.Empresa
							AND PR.codigo = AJ.proveedor
						WHERE A.Empresa = @IEmpresaU ---:Empresa'
						AND A.TipoDescuento IN ('N', 'A') --Privadas
						AND F.Empresa = @ISucursal ---:Hospital'
						AND A.Entrada >= '2020-01-01'--'2020/01/01 00:00'
						AND A.Salida >= @IFechaInicio
						AND A.Salida <= @IFechaFin
						AND PR.Codigo = @IProveedor
						AND NOT EXISTS (SELECT oca_orden_compra FROM INVENTARIO..OrdenCompraHonosMedicos
								WHERE  A.Empresa = oca_empresa
								AND A.Serie = oca_serie_admision
								AND A.Codigo = oca_admision
								AND oca_medico = C.ajeno
								AND oca_estado = 'A')
						 )AS H
					GROUP BY H.Admision,
							 H.SerieA,
							 H.NAdmision,
							 H.Cod_Paciente,
							 H.Paciente,
							 H.proveedor
					ORDER BY H.Admision

					
				END
				ELSE IF (@ISubOpcion = '2')
				BEGIN 
					--CONSULTAR QUE EL PROVEEDOR TENGA UN MÉDICO VÁLIDO ASOCIADO
					--VALIDA MÉDICO PROVEEDOR
					--QryValidaciones
					IF EXISTS 
						(SELECT
							1
						FROM HOSPITAL..Ajenos Aj
						INNER JOIN INVENTARIO..Proveedores P
							ON (P.Empresa = Aj.Empresa
							AND Aj.Proveedor = P.codigo)
						WHERE P.empresa = @IEmpresaU
						AND P.codigo = @IProveedor
						)
					BEGIN
						SET @Existe = 0 --existe
					END
					ELSE
					BEGIN
						SET @Existe = 1 --No existe
					END

					IF @Existe = 1
					BEGIN
						
						SELECT -1 AS codigo, 'El proveedor no está asociado a un médico válido.' AS descripcion, -1 AS tipo_error;
						
					END
					ELSE
					BEGIN
						IF EXISTS 
							(SELECT
								1
							FROM HOSPITAL..Ajenos Aj
							INNER JOIN INVENTARIO..Proveedores P
								ON (P.Empresa = Aj.Empresa
								AND Aj.Proveedor = P.codigo)
							WHERE P.empresa = @IEmpresaU
							AND P.codigo = @IProveedor
							AND ((Aj.Tipo = 'C'
							AND Aj.TipoCortesiaCasa = 1)
							OR (Aj.TipoCortesiaCasa = 1)
							OR (Aj.Tipo = 'M'))
							)
						BEGIN
							SET @Existe =  0 --existe
						END
						ELSE
						BEGIN
							SET @Existe =  1 --No existe
						END
					
						IF @Existe = 1
						BEGIN
							SELECT -1 AS codigo, 'El proveedor no está asociado a un médico de Casa o Casa Cortesía.' AS descripcion, -1 AS tipo_error;
							
						END
						ELSE
							
							SELECT 0 AS 'codigo', 	'Proveedor valido' AS 'descripcion',  '' AS 'resultado';
							
					END
				END
				--CARGA OTROS PAGOS
				--QryOtrosPagos

				ELSE IF (@ISubOpcion = '3')
				BEGIN
					SELECT dop_id AS id
						, dop_descripcion AS Descripcion
					FROM INVENTARIO..Descripcion_Otros_Pagos
					WHERE dop_empresa = @IEmpresaU
						AND dop_estado = 'A'

					 --COMMIT TRAN
				END
				ELSE IF (@ISubOpcion = '4')
				BEGIN
					--VALIDA LA EXISTENCIA DE LA FACTURA
					--QryValidaciones
					IF EXISTS 
						(SELECT
							1
						FROM inv_orden_compra_enc
						INNER JOIN Ajenos AJ
							ON (AJ.Empresa = IdEmpresasfk
							AND AJ.codigo = Medico)
						INNER JOIN INVENTARIO..Proveedores P
							ON (P.Empresa = AJ.Empresa
							AND AJ.Proveedor = P.codigo)
						WHERE IdEmpresasfk = @IEmpresaU
						AND SerieFacturaMed =  @ISerieF 
						AND FacturaMed =  @IFactura
						AND P.codigo =  @IProveedor)
					BEGIN
						SET @Existe =  0 --existe
					END
					ELSE
					BEGIN
						--BUSCAR EN HISTORIAL
						IF EXISTS 
							(SELECT
								1
							FROM INVENTARIO..OrdenCompraHonosMedicos
							INNER JOIN Ajenos AJ
								ON (AJ.Empresa = oca_empresa
								AND AJ.codigo = oca_medico)
							INNER JOIN INVENTARIO..Proveedores P
								ON (P.Empresa = AJ.Empresa
								AND AJ.Proveedor = P.codigo)
							WHERE oca_empresa = @IEmpresaU
							AND oca_serie_fac_med =  @ISerieF 
							AND oca_codigo_fac_med =  @IFactura
							AND P.codigo =  @IProveedor)
						BEGIN
							SET @Existe =  0 --existe
						END
						ELSE
						BEGIN
							SET @Existe =  1 --No existe
						END
					END

					IF @Existe = 0
					BEGIN
						SELECT -1 AS codigo, 'La factura ingresada ya existe para este proveedor.' AS descripcion, -1 AS tipo_error;	
					END
					ELSE 
					BEGIN
						SELECT 0 as 'codigo', 	'Datos de Factura Válidos!' as 'descripcion',  	0 as 'resultado';
					END
				END
				ELSE IF (@ISubOpcion = '5')
				BEGIN 
					
					DECLARE @Medico INT = 0

						SELECT
							@Medico = Aj.Codigo
						FROM HOSPITAL..Ajenos Aj
						INNER JOIN INVENTARIO..Proveedores P
							ON (Aj.Empresa = P.Empresa
							AND Aj.Proveedor = P.Codigo)
						WHERE P.Empresa = @IEmpresaU
							AND P.Codigo = @IProveedor
					
					IF @Medico > 0
					BEGIN 
						SELECT @Medico AS Medico
					END
					ELSE
					BEGIN 
						
						SELECT -1 as codigo, 'Médico no existe / o no encontrado.' AS descripcion,   -1 AS tipo_error;
						
					END
				END
				ELSE 
				BEGIN
					
						SELECT -1 as codigo, 'SubOpción no existe.' AS descripcion,   -1 AS tipo_error;
						
				END
			END
			ELSE BEGIN
				SELECT -1 as codigo, 'La Opción no existe!' AS descripcion,   -1 AS tipo_error;
			
		END
		END
		ELSE BEGIN
			SELECT -1 as codigo, 'SubClase distinta a Honorarios.' AS descripcion,   -1 AS tipo_error;
			
		END
	END
	ELSE BEGIN
		
		SELECT -1 as codigo, 'Tipo seleccionado distinto a Servicios' AS descripcion,   -1 AS tipo_error;
		
	END
	
END


-->>  Crear el Procedimiento Almacenado  -sp_inv_orden_compra_manual
USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[sp_inv_orden_compra_manual]    Script Date: 02/04/2024 07:39:26 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



/********************************************************************************************/
/*	CREADO POR:  		 Blanca Beatríz Cetino Rodríguez  	Numero de Rational:  322393  	*/
/*	FECHA CREACIÓN:		 20/07/2023									                		*/
/*	PRODUCTO - PROCESO:  SOLICITUD COMPRA - ORDEN DE COMPRA	MANUAL		    				*/
/**************************************** DESCRIPCION ***************************************/
/*	Este procedimiento tiene como objetivo insertar la orden de compra de forma manual,		*/
/*	valida la empresas para identificar si es una empresa que pueda ingresar OC de productos*/
/*  o de servicios.	Muestra campos de validación de Orden de Compra, anulación de OC		*/
/*																							*/
/*************************************** MODIFICACIÓN ***************************************/
/*					FECHA				 RESPONSABLE				DESCRIPCION				*/
/*				09/10/2023			Javier Castillo			Ordenes de compra de ortopedia  */
/*	Se agregan parametros para empresa y sucursal para identificar la farmacia a despachar  */
/*	Y un identificador para marcar las ordenes creadas desde las proformas de ortopedia     */
/*																						    */
/********************************************************************************************/
/*				06/11/2023			Javier Castillo			Ordenes de compra de ortopedia  */
/*	Se consultan los dias de credito en la tabla de proveedores en la columna DiasCredito   */
/*	Si esta es mayor a cero se utiliza ese valor de lo contrario se utiliza por defecto 60 dias   */
/*																						    */
/*************************************** MODIFICACIÓN ***************************************/
/*			    11/03/2024 - Blanca Beatríz Cetino Rodríguez - Órdenes de compra Honorarios */
/* Se agregan campos nuevos en el insert Órdenes de Compra para Honorarios					*/
/*				25/03/2024 - Blanca Beatríz Cetino Rodríguez - Órdenes de compra Servicos F.*/
/* Se aagrega validación de si son servicios fijos ingresar Null en campos.					*/
/********************************************************************************************/



CREATE PROCEDURE [dbo].[sp_inv_orden_compra_manual]
	
	 @i_corporativo VARCHAR(5) = ''
	, @i_id_empresas CHAR(3) = ''
	, @i_empresa_real char(3) = ''
	, @i_empresa_u char(3) = ''
	, @i_empresa_sucursal char(3) = ''
	, @i_empresa_bod CHAR(3) = ''
	, @i_id_productoClase CHAR(1)= ''
	, @i_ProductoSubClase INT = 1
	, @i_id_deparamento CHAR(2) = ''
	, @i_observaciones TEXT = ''
	, @i_id_proveedor CHAR(12) = ''
	, @i_granTotal MONEY = 0
	, @i_dias_credito INT = 0
	, @i_pagos INT = 0
	, @i_id_documento INT = 0
	, @i_fecha_inicio DATE = ''
	, @i_fecha_fin DATE = ''
	, @i_estado CHAR(1) = '' 
	, @i_opcion CHAR(1)= ''
	, @i_subopcion CHAR(1)= ''
	, @i_cantLineas INT = 0
	, @i_repcionada CHAR(1) = ''
	, @i_dOrden INT = 0
	, @i_aOrden INT = 0
	, @i_tipoSeleccion CHAR(1) = ''
	, @i_orden_ortopedia CHAR(1) = 'N'
	, @i_numero_proforma int = 0
	, @i_empresaSucursal char(3) = ''
	, @i_ordenCompraA INT = 0 --Orden de Compra para anulación
	, @i_medico INT = 0 --Código de Medico para Orden
	, @i_tipoHono CHAR(1) = ''
	, @i_idOtroPagos INT = 0 --Para Órdenes de Honorarios
	, @i_serief CHAR(3) = ''
	, @i_factura INT = 0
	

AS

	--Solicitud creada de forma manual 
	DECLARE @c_empresaProv CHAR(3)  = ''
		, @c_existecodigo CHAR(2)  = ''
		, @c_id_solicitud INT = 0
		, @ErrRaise	VARCHAR(99)	= ''
		, @ErrRaiseNum INT		= 0
		, @Cant INT = 0
		, @RowID INT = 1
		, @CorporativoR1 CHAR(5) = ''
		, @CorrelativoOC INT = 0
		, @w_IdBodegafk INT = 0
		
BEGIN	
	--Inicio de transaccion 
	BEGIN TRAN


	--Inicio de excepcion
	BEGIN TRY
		IF (@i_id_documento = '3') 
		BEGIN

			--Validación de Empresa para Proveedor
			SET @c_empresaProv = (SELECT
									CASE  WHEN EmpresaUnificadora = @i_empresa_u 
										THEN EmpresaUnificadora
										ELSE Empresa
									END	AS EMPRESAPROV
								  FROM ContaDB..EmpresasDefaults
								  WHERE Empresa = @i_id_empresas)

			IF (@i_opcion = 'C')
			BEGIN
				SELECT 
					ICE.IDORDENCOMPRAENC
					, ICE.IDSOLICITUDENCFK, 
					RTRIM(ICE.IDEMPRESASFK) AS IDEMPRESASFK, 
					ICE.TOTAL, ICE.CANT_LINEAS, ICE.OBSERVACIONES, RTRIM(ICE.IDDEPARTAMENTOFK) AS IDDEPARTAMENTOFK,
					DEPT.NOMBRE AS DESCRIPCION_DEPTO, 
					'' AS DESCRIPCION_BODEGA,
					CASE WHEN ICE.IDPRODUCTOCLASEFK = 'P' THEN 'BIEN'
					WHEN ICE.IDPRODUCTOCLASEFK = 'S' THEN 'SERVICIO'
					END AS TIPO_OC,
					RTRIM(ICE.IDPRODUCTOSUBFK) AS IDPRODUCTOSUBFK, ICE.FECHA_MAXIMA_RECEPCION, 
					RTRIM(ICE.IDPROVEEDORFK) AS IDPROVEEDORFK, PROV.NOMBRE AS DESCRIPCION_PROVEEDOR, PROV.NIT ,
					RTRIM( ICE.estado) AS ESTADO,
					CASE WHEN  RTRIM( ICE.estado)  = 'P' THEN 'PROCESO'
					WHEN RTRIM( ICE.estado)  = 'A' THEN 'AUTORIZADO'
					WHEN RTRIM( ICE.estado)  = 'C' THEN 'ANULADO'
					ELSE 'RECHAZADO'
					END AS DESCRIPCION_ESTADO, 
					'' AS EMPRESA_DIRECCION,
					'' AS EMPRESA_CORREO,
					'' AS EMPRESA_TELEFONO,
					FORMAT(ICE.fecha_creacion, 'dd/MM/yyyy') AS FECHA_CREACION_ORDEN,
					'' AS NOMBRE_EMPRESA,
					RTRIM(PROV.Nit) AS PROV_NIT,
					RTRIM(PROV.CONTACTO) AS PROV_CONTACTO,
					RTRIM(PROV.EMAIL) AS PROV_CORREO,
					CONCAT(CONCAT(ICE.IDCORPORATIVOSOLICITAFK, '-'), CONCAT(C.NOMBRES, ' ', C.APELLIDOS)) AS USUARIO,					
					ICE.RECEPCIONADA VALIDADA,
					CASE WHEN  RTRIM(ICE.RECEPCIONADA)  = 'S' THEN 'VALIDADA'
					WHEN RTRIM( ICE.RECEPCIONADA)  = 'N' THEN 'NO VALIDADA'
					END AS DESCRIP_VALIDADA,
					ICE.Validacion VALIDACION,
					ICE.RevisoConformidad USUARIO_MOV
					FROM INV_ORDEN_COMPRA_ENC AS ICE
					INNER JOIN INV_AUTORIZACION_DOCUMENTO_HISTORIAL AS ADH
					ON ICE.IdSolicitudEncfk = ADH.IdSolicitudEncfk
					AND ICE.IdOrdenCompraEnc = ADH.IDORDENCOMPRAENCFK
					INNER JOIN INV_SOLICITUD_COMPRA_ENC AS SC
					ON ICE.IdSolicitudEncfk = SC.IdSolicitudEnc
					INNER JOIN INVENTARIO..DepartamentosINV  DEPT
					ON ICE.IdDepartamentofk = DEPT.CODIGO
					AND DEPT.Empresa = @i_empresa_bod
					INNER JOIN INVENTARIO..PROVEEDORES  AS PROV
					ON ICE.IDPROVEEDORFK = PROV.CODIGO
					AND PROV.EMPRESA = @c_empresaProv
					INNER JOIN CONTADB..CORPORATIVOS AS C
					ON ICE.IdCorporativoSolicitafk = C.CORPORATIVO
					WHERE  ICE.IdCorporativoSolicitafk = @i_corporativo 
					AND ICE.IDEMPRESASFK = @i_id_empresas
					AND (@i_estado IS NULL OR CONCAT(RTRIM(ICE.estado),' ') LIKE '%' + LTRIM(RTRIM(@i_estado )) +'%')
					AND CONVERT(VARCHAR,  ICE.FECHA_CREACION, 23)  >= @i_fecha_inicio 
					AND CONVERT(VARCHAR,  ICE.FECHA_CREACION, 23)  <= @i_fecha_fin
					AND (@i_id_deparamento IS NULL OR CONCAT(RTRIM(ICE.IDDEPARTAMENTOFK),' ') LIKE '%' + LTRIM(RTRIM(@i_id_deparamento)) +'%')
					AND (@i_id_proveedor IS NULL OR CONCAT(RTRIM(PROV.CODIGO),' ') LIKE '%' + LTRIM(RTRIM(@i_id_proveedor)) +'%')
					AND (@i_repcionada IS NULL OR CONCAT(RTRIM(ICE.RECEPCIONADA),' ') LIKE '%' + LTRIM(RTRIM(@i_repcionada)) +'%')
					AND (ICE.IdOrdenCompraEnc >= @i_dOrden OR @i_dOrden = 0)
					AND (ICE.IdOrdenCompraEnc <= @i_aOrden OR @i_aOrden = 0)
					AND (@i_tipoSeleccion IS NULL OR CONCAT(RTRIM(ICE.IdProductoClasefk),' ') LIKE '%' + LTRIM(RTRIM(@i_tipoSeleccion)) +'%')
					ORDER BY ICE.IDORDENCOMPRAENC DESC

					COMMIT TRAN
			END
			ELSE IF (@i_opcion = 'I')
			BEGIN
				IF (@i_subopcion = '1')
				BEGIN
					--VALIDACIÓN DE EMPRESA SEGUN PRODUCTO CLASE SELECCIONADO
					IF (@i_id_productoClase = 'P')
					BEGIN 
						IF @i_id_empresas = 'SEM'
						BEGIN 
							
							SELECT 0 AS codigo, 'Empresa para Ordenes Compra Servicios - Productos ' AS descripcion,  0 AS resultado;
							COMMIT TRAN
						END
						ELSE
						BEGIN
							
							SELECT 0 AS codigo, 'Empresa para Ordenes Compra Servicios' AS descripcion,  1 AS resultado;
							COMMIT TRAN
							
						END 
					END
				END 
				ELSE IF (@i_subopcion = '2')
				BEGIN

					--CORRELATIVO ORDEN DE COMPRA POR EMPRESA	
					SELECT
						@CorrelativoOC = SigOrdenCompra
					FROM ContaDB..Correlativos
					WHERE Empresa = @i_id_empresas


					--ACTUALIZACIÓN DE CORRELATIVO ORDEN DE COMPRA
					UPDATE ContaDB..Correlativos
						SET SigOrdenCompra = @CorrelativoOC +1
					WHERE Empresa = @i_id_empresas


					--INSERTAR NUEVA SOLICITUD
					INSERT INTO  dbo.inv_solicitud_compra_enc
						(IdEmpresasfk
						,total
						,cant_lineas
						,observaciones
						,IdDepartamentofk
						,IdBodegafk
						,IdProductoClasefk	
						,IdProductoSubfk
						,fecha_creacion_orden
						,IdCorporativoSolicitafk
						,estado
						,ProveedorEmpresa )
					VALUES
						( @i_id_empresas
						, @i_granTotal
						, @i_cantLineas
						, CONCAT('REFERENCIA ORDEN DE COMPRA MANUAL - ',@i_observaciones)
						, @i_id_deparamento
						, 0
						, @i_id_productoClase 
						, @i_ProductoSubClase
						, GETDATE()
						, @i_corporativo
						,'P'
						, @c_empresaProv)


					--OBTIENE EL NUMERO DE SOLICITUD GENERADA
					SET @c_id_solicitud = (SELECT @@Identity);

					--SE AGREGA HISTORIAL - SOLICITUD
					INSERT INTO inv_autorizacion_documento_historial
						(IdCorporativofk
						, IdSolicitudEncfk
						, fecha_hora_inserto
						, IdDocumentofk
						, num_documento
						, estado
						, observaciones
						, IdOrdenCompraEncFk)
					VALUES
						(@i_corporativo
						, @c_id_solicitud
						, GETDATE()
						,'4'
						, '1'
						, 'C'
						, 'REFERENCIA ORDEN DE COMPRA MANUAL'
						, @CorrelativoOC)
					
					

					-- inserta el numero de orden de compra en la proforma de ortopedia
					-- Obtiene la bodega de farmacia segun el hospital
					if @i_orden_ortopedia = 'S'
					begin
						update HisProformasOrtopedia 
						   set OrdenCompra = @CorrelativoOC
						 where IdProforma = @i_numero_proforma 

						 select @w_IdBodegafk = codigo 
						   from inventario..bodegas 
						  where empresa = @i_empresa_bod 						   
						   and ACTIVA = 'S' 
						   and codigoAgrupacion = '2' 
						   and Codigo between 11 and 19
						   and Hospital = @i_empresa_sucursal
						   and EmpresaRealX = @i_empresa_real
						
						set @i_dias_credito = (select ISNULL(DiasCredito,@i_dias_credito) from inventario..proveedores
																		 			where empresa = @c_empresaProv and codigo = @i_id_proveedor)

						if @w_IdBodegafk = 0
						begin
							
							SELECT -1 AS codigo, 'La sucursal no cuenta con una bodega de farmacia configurada' AS descripcion,  -1 AS tipo_error;;
							return;
							ROLLBACK TRAN
						end

					end

					--INSERTA EL ENCABEZADO DE LA ORDEN DE COMPRA 
					INSERT INTO dbo.inv_orden_compra_enc
						(IdOrdenCompraEnc
						, IdSolicitudEncfk
						,IdEmpresasfk
						,total
						,cant_lineas
						,observaciones
						,IdDepartamentofk	
						,IdBodegafk
						,IdProductoClasefk
						,IdProductoSubfk
						,IdCorporativoSolicitafk
						,fecha_creacion	
						,estado
						,Tipo
						,cant_pagos_propuesta
						,dias_credito_propuesta
						,IdProveedorfk
						,ProveedorEmpresa
						,orden_ortopedia
						,TipoHono
						,Hospital
						,Medico
						,SerieFacturaMed
						,FacturaMed
						,CodigoOtroPagos
						)
					VALUES
						(@CorrelativoOC
						, @c_id_solicitud
						, @i_id_empresas
						, @i_granTotal
						, @i_cantLineas
						, @i_observaciones
						, @i_id_deparamento	
						, @w_IdBodegafk
						, @i_id_productoClase
						, @i_ProductoSubClase
						, @i_corporativo
						, GETDATE()
						, 'P'
						, 'E'
						, @i_pagos
						, @i_dias_credito
						, @i_id_proveedor
						, @c_empresaProv
						, @i_orden_ortopedia
						, CASE WHEN @i_id_productoClase = 'P' OR @i_ProductoSubClase = 1 THEN NULL
						ELSE @i_tipoHono 
						END 
						, CASE WHEN @i_id_productoClase = 'P' OR @i_ProductoSubClase = 1 THEN NULL
						ELSE @i_empresa_sucursal 
						END
						, CASE WHEN @i_id_productoClase = 'P' OR @i_ProductoSubClase = 1 THEN NULL
						ELSE @i_medico 
						END 
						, CASE WHEN @i_id_productoClase = 'P' OR @i_ProductoSubClase = 1 THEN NULL
						ELSE @i_serief 
						END
						, CASE WHEN @i_id_productoClase = 'P' OR @i_ProductoSubClase = 1 THEN NULL
						ELSE @i_factura 
						END
						, CASE WHEN @i_idOtroPagos = '' THEN NULL
						ELSE @i_idOtroPagos 
						END
						)	

					--OBTIENE EL CORPORATIVO DE RESPONSABLE	
				
					SET @CorporativoR1 = (SELECT IdResponsablefk 
										FROM  inv_autorizacion_documento
										WHERE IdDocumentofk =  @i_id_documento 
											AND estado = 'S' 
											AND num_documento = 1
											AND IdEmpresaFk = @i_id_empresas
											AND IdProductoClasefk = @i_id_productoClase)
						
					--SE AGREGA HISTORIAL - ORDEN COMPRA
					INSERT INTO inv_autorizacion_documento_historial
						(IdCorporativofk
						, IdSolicitudEncfk
						, fecha_hora_inserto
						, IdDocumentofk
						, num_documento
						, estado
						, IdOrdenCompraEncFk)
					VALUES
						(@CorporativoR1
						, @c_id_solicitud
						, GETDATE()
						, @i_id_documento
						, 1
						, 'P'
						, @CorrelativoOC)


						COMMIT TRAN
						SELECT 0 AS codigo, CONCAT('Se ha creado la Orden de Compra: ', @CorrelativoOC) AS descripcion, @CorrelativoOC AS resultado;


				END --SUBOPCION 2
			END --OPCION I
			ELSE IF (@i_opcion = 'A') --ANULACIÓN
			BEGIN
					UPDATE dbo.inv_orden_compra_enc
						SET Estado = 'C', 
						IdCorporativoAnula = @i_corporativo,
						FechaAnulacion = getdate()
					Where IdEmpresasfk = @i_id_empresas
					AND IdOrdenCompraEnc = @i_ordenCompraA

					COMMIT TRAN
					SELECT 0 AS codigo, CONCAT('Se ha Anulado la Orden de Compra: ', @i_ordenCompraA) AS descripcion, @i_ordenCompraA AS resultado;
			END
			ELSE BEGIN
				SELECT -1 AS codigo, 'Opción incorrecta' AS descripcion, -1 AS tipo_error;
				ROLLBACK TRAN
			END
		END
		ELSE BEGIN
				SELECT -1 AS codigo, 'Id documento incorrecto' AS descripcion,  -1 AS tipo_error;
				ROLLBACK TRAN
			END 
	END TRY
	BEGIN CATCH
		ROLLBACK TRAN
		SET @ErrRaise = ERROR_MESSAGE()
		SET @ErrRaiseNum = ERROR_NUMBER()
		
		RAISERROR(@ErrRaise,16,@ErrRaiseNum)
	END CATCH

END



-->>  Crear el Procedimiento Almacenado -  sp_inv_proveedor_lista 

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[sp_inv_proveedor_lista]    Script Date: 12/03/2024 09:49:04 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/********************************************************************************************/
/*	CREADO POR:  		 Edvin Geovany Jucup  	Numero de Rational:  330014  	 			*/
/*	FECHA CREACIÓN:		 20/07/2023									                		*/
/*	PRODUCTO - PROCESO:  COTIZACIÓN	 - ORDEN DE COMPRA MANUAL			    				*/
/**************************************** DESCRIPCION ***************************************/
/*	Este procedimiento tiene como objetivo consultar los datos de los proveedores.			*/
/*																							*/
/*************************************** MODIFICACIÓN ***************************************/
/*								FECHA					RESPONSABLE							*/
/*							JUL - SEP 2023		Blanca Beatríz Cetino Rodríguez				*/
/* DESCRIPCION																				*/
/* Se agrega la validación de la empresa que corresponde al proveedor.						*/
/* Se quita departamentos de planesmedicos													*/
/********************************************************************************************/



--EXEC sp_inv_proveedor_lista @i_empresa = 'MED', @iAccion = 'B'
CREATE PROCEDURE [dbo].[sp_inv_proveedor_lista]

@i_empresa varchar(3) = '',
@iAccion varchar(1) = 'A'
AS
BEGIN

  DECLARE @c_empresaProv char(3) = ''

  --Validación de Empresa para Proveedor
	SET @c_empresaProv = (SELECT
	CASE
		WHEN EmpresaUnificadora = 'MED' THEN EmpresaUnificadora
		ELSE Empresa
	END AS EMPRESAPROV
	FROM ContaDB..EmpresasDefaults
	WHERE Empresa = @i_empresa)

IF (@iAccion  = 'A') 
BEGIN 


	SELECT
		RTRIM(PR.CODIGO) AS CODIGO,
		RTRIM(PR.NIT) AS NIT,
		PR.NOMBRE,
		PR.NOMBRECHEQUE,
		PR.ACTIVO,
		RTRIM(PR.TIPO) AS TIPO,
		ITP.DESCRIPCION AS DESCRIPCION_TIPO_PROVEEDOR,
		RTRIM(PR.DIRECCION) AS DIRECCION,
		RTRIM(PR.CIUDAD) AS CIUDAD,
		RTRIM(PR.TELEFONOS) AS TELEFONOS,
		RTRIM(PR.FAX) AS FAX,
		RTRIM(PR.EMAIL) AS EMAIL,
		RTRIM(PR.CONTACTOTRATO) AS
		CONTACTOTRATO,
		RTRIM(PR.CONTACTO) AS CONTACTO,
		RTRIM(PR.LINEA) AS LINEA,
		RTRIM(PR.CONTACOMPRA) AS CONTACOMPRA,
		PR.DESCUENTO,
		PR.LIMITEDESCUENTO,
		PR.CREDITO,
		PR.LIMITECREDITO,
		PR.VENCECREDITO,
		PR.FECHAINICIO,
		PR.FECHAULTIMAFAC,
		PR.VALORULTIMAFAC,
		PR.FECHAULTIMOPAGO,
		PR.VALORULTIMOPAGO,
		RTRIM(PR.CONTAANTICIPO) AS CONTAANTICIPO,
		RTRIM(PR.CONTACREDITO) AS CONTACREDITO,
		PR.RETENCION,
		RTRIM(PR.TIPORETENCION) AS TIPORETENCION,
		ITR.DESCRIPCION AS DESCRIPCION_TIPO_RETENCION,
		RTRIM(PR.CUENTARETENCION) AS CUENTARETENCION,
		RTRIM(PR.NITRETENCION) AS NITRETENCION,
		PR.DIRECCIONCALLE,
		RTRIM(PR.DIRECCIONNUMERO) AS DIRECCIONNUMERO,
		RTRIM(PR.DIRECCIONAPTO) AS DIRECCIONAPTO,
		PR.DIRECCIONZONA,
		PR.DIRECCIONCOLONIA,
		PR.DIRECCIONDEPTO,
		--DPT_GEOG.DEPARTAMENTO 
		'' AS DEPARTAMENTO_GEOGRAFICO,
		PR.DIRECCIONMUNI,
		RTRIM(PR.CONTACOMPRAMEDICOS) AS CONTACOMPRAMEDICOS,
		RTRIM(PR.CONTACREDITOMEDICOS) AS CONTACREDITOMEDICOS,
		RTRIM(PR.CODIGOHBA) AS CODIGOHBA,
		PR.TIPODEPROVEEDOR,
		PR.ESAGENTERETENEDOR,
		PR.EMPRESAEMPLEADO,
		PR.EMPLEADO,
		PR.TIPOCONTRIBUYENTE,
		PR.PROVEEDORDELEXTERIOR,
		PR.CUENTABANCO,
		PR.BLOQUEADO,
		EMP.CODIGO AS CODIGO_EMPRESA,
		EMP.NOMBRE AS DESCRIPCION_EMPRESA,
		CASE --DEFINE EL ICONO DEL PROVEEDOR
		  WHEN PR.BLOQUEADO = 'N' THEN 'fa-unlock-alt'
		  ELSE 'fa-lock'
		END AS ICONO_BLOQUEADO,
		CASE --DEFINE EL ICONO DEL PROVEEDOR
		  WHEN PR.BLOQUEADO = 'N' THEN 'Desbloqueado'
		  ELSE 'Bloqueado'
		END AS ESTADO_BLOQUEADO,
		CASE --DEFINE COLOR DEL ICONO DEL PROVEEDOR
		  WHEN PR.BLOQUEADO = 'N' THEN 'success'
		  ELSE 'dark'
		END AS COLOR_ICONO,
		APROBADO,
		DIASCREDITO
	  FROM INVENTARIO..PROVEEDORES AS PR
	  INNER JOIN CONTADB..EMPRESAS AS EMP
		ON PR.EMPRESA = EMP.CODIGO
	  LEFT JOIN INV_TIPO_PROVEEDOR AS ITP
		ON PR.TIPO = ITP.IdTipoProveedor
	  LEFT JOIN INV_TIPO_RETENCION AS ITR
		ON PR.TIPORETENCION = ITR.IdTipoRetencion
	  WHERE PR.ACTIVO = 'S'
	  AND PR.EMPRESA = @c_empresaProv
	  ORDER BY PR.BLOQUEADO

	END
	ELSE IF (@iAccion  = 'B')
	BEGIN
		SELECT
				RTRIM(PR.CODIGO) AS CODIGO,
				RTRIM(PR.NIT) AS NIT,
				RTRIM(PR.NOMBRE) AS NOMBRE
		
			  FROM INVENTARIO..PROVEEDORES AS PR
			  INNER JOIN CONTADB..EMPRESAS AS EMP
				ON PR.EMPRESA = EMP.CODIGO
	  
			  WHERE PR.ACTIVO = 'S'
			  AND PR.EMPRESA = @c_empresaProv
			  ORDER BY PR.BLOQUEADO
	END;
END



-->>  Crear el Procedimiento Almacenado  -  SP_Margen_Tolerancia_Orden_Compra

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[SP_Margen_Tolerancia_Orden_Compra]    Script Date: 12/03/2024 09:49:41 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


/********************************************************************************************/
/*	CREADO POR:		 Juan Carlos Girón	Numero de Rational:330014	 						*/
/*	FECHA CREACIÓN:		 10/08/2023															*/
/*	PRODUCTO - PROCESO: COTIZACIÓN - ORDEN COMPRA MANUAL									*/
/**************************************** DESCRIPCION ***************************************/
/*	Control de margen de tolerancia															*/
/*																							*/
/*************************************** MODIFICACIÓN ***************************************/
/*	FECHA	    RESPONSABLE		DESCRIPCION													*/
/*	10/08/2023	Blanca Cetino 	Se agrega respuesta según validación para interpretación DLL*/
/*	31/01/2024	Juan Carlos Giron   Para impuesto y el Costo del Ultimo Incremento se envia */	
/* 									la empresa Bodega										*/
/*																							*/
/********************************************************************************************/


CREATE PROCEDURE [dbo].[SP_Margen_Tolerancia_Orden_Compra]
------- Parametros de Entrada -----------

@precio money = 0,
@producto char(13) = NULL

AS
BEGIN


DECLARE @EmpresaBodega char(3)
DECLARE @Empresa char(3)
DECLARE @Empresasucursal char(3)
DECLARE @costoUltimo money
DECLARE @InvCostoUltimoIncremento real
DECLARE @VtmpCostoMenosAllow real
DECLARE @VtmpCostoMayorAllow real
DECLARE @InvFactorImpuestoCompra real
DECLARE @VtmpCostoUnitario real
DECLARE @PorcGanancia real


	---1)Averiguando la empresa Default
	SELECT
		@empresa = CODIGO
	FROM CONTADB..EMPRESAS
	WHERE DEFAULTEMPRESA = 'S'

	---2)Averiguando la empresa bodega
	SELECT
		@empresabodega = bodega
	FROM CONTADB..EMPRESASDefaults
	WHERE EMPRESA = @empresa

	---3)Averiguando la empresaDefault de la empresa bodega
	------------------------------------------------------------------------------------
	SELECT
		@empresasucursal = Empresasucursal
	FROM CONTADB..EMPRESASDefaults
	WHERE EMPRESA = @empresabodega

	SELECT
		@InvFactorImpuestoCompra = InvFactorImpuestoCompra,
		@InvCostoUltimoIncremento = InvCostoUltimoIncremento
	FROM Contadb..EmpresasDefaults WITH (NOLOCK)
	WHERE Empresa = @empresabodega---@empresa

	-----------------------------------------------------
	SELECT
		@costoUltimo = CostoUltimo
	FROM Inventario..Productos
	WHERE empresa = @empresabodega
	AND Codigo = @producto
	----------------------------------------------


	-------------------------------------------------------------------------------------
	SELECT	@VtmpCostoMenosAllow = (@costoUltimo * (1 - @InvCostoUltimoIncremento / 100))

	IF (@VtmpCostoMenosAllow < 0)
	BEGIN
		SET @VtmpCostoMenosAllow = 0
	END

	--------------------------------------------------------------------------------------
	SELECT	@VtmpCostoMayorAllow = (@costoUltimo * (1 + @InvCostoUltimoIncremento / 100))

	-----------------------------------------------------------------------------------------------

	SELECT 	@VtmpCostoUnitario = (ROUND(@precio / (1 + @InvFactorImpuestoCompra / 100), 4))

	IF (@VtmpCostoUnitario > @VtmpCostoMayorAllow)	OR (@VtmpCostoUnitario < @VtmpCostoMenosAllow)
	BEGIN


		SELECT	-1 AS codigo,	'El costo unitario de este artículo, Q. ' + RTRIM(CONVERT(char, (ROUND(@VtmpCostoUnitario, 4)))) + ' Está fuera del margen permitido para nuevas compras, Verifique por favor, Limites actuales: De Q ' + RTRIM(CONVERT(char, (ROUND(@VtmpCostoMenosAllow, 4)))) + ' a Q.' + CONVERT(char, (ROUND(@VtmpCostoMayorAllow, 4))) AS descripcion,	0 AS tipo_error;
		END

	ELSE
	BEGIN
		SELECT	0 AS codigo, 'El costo unitario es valido.' AS descripcion,	0 AS tipo_error;
	END
END



-->>  Crear el Procedimiento Almacenado  -  sp_inv_orden_compra_detalle_manual

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[sp_inv_orden_compra_detalle_manual]    Script Date: 12/03/2024 09:54:30 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
​
/********************************************************************************************/
/*	CREADO POR:  		 Edvin Geovany Jucup  	Numero de Rational:  330014  	 			*/
/*	FECHA CREACIÓN:		 17/08/2021									                		*/
/*	PRODUCTO - PROCESO:  ORDEN COMPRA MANUAL - DETALLE					    				*/
/**************************************** DESCRIPCION ***************************************/
/*	Este procedimiento tiene como objetivo insertar los datos del detalle	de una 			*/
/*	orden de compra.																		*/
/*																							*/
/*************************************** MODIFICACIÓN ***************************************/
/*								FECHA					RESPONSABLE							*/
/*							JUL - SEP 2023		Blanca Beatríz Cetino Rodríguez				*/
/* DESCRIPCION: Se agrega el tipo de Orden de Compra, la empresa bodega para departamentos  */ 
/* y se agrenga	el usuario y filtro de departamento.										*/
/*																							*/
/*************************************** MODIFICACIÓN ***************************************/
/*								FECHA					RESPONSABLE							*/
/*							MARZO 2024		Blanca Beatríz Cetino Rodríguez					*/
/* DESCRIPCION: Se agrega validación para realizar ingreso de detalle de productos,  de		*/
/* Admiciones y Otros pagos para Ordenes de compra de Honorarios.							*/
/*																							*/
/********************************************************************************************/
​

CREATE PROCEDURE [dbo].[sp_inv_orden_compra_detalle_manual]

	@I_IdEmpresaU CHAR(3) = '',
	@I_IdEmpresa CHAR(3) = '',
	@I_IDOrdenCompraEnc  INT = 0,
	@I_Producto CHAR(13) = 0,
	@I_Cantidad DECIMAL = 0,
	@I_PrecioUnitario MONEY = 0,
	@I_Subtotal MONEY = 0, 
	@i_id_productoClase CHAR(1) = 'P',
	@I_TipoHono CHAR(3) ='',
	@I_Hospital CHAR(3) ='',
	@I_SerieAdmision CHAR(1) = '',
	@I_Admision INT = 0,
	@I_Medico INT = 0,
	@I_Paciente INT = 0,
	@I_SerieFacturaMed CHAR(3) = '',
	@I_FacturaMed  INT = 0
​
AS
BEGIN

	DECLARE	
			@Resultado  INT = 0,
			@ErrRaise	Varchar(99)	= '',
			@ErrRaiseNum INT		= 0
​
	
	--Inicio de transaccion 
	BEGIN TRAN
	--Inicio de excepcion
	BEGIN TRY​

	--Valida si es ingreso de detalle de Orden de Compra Producto ''
	IF (@i_id_productoClase = 'P')
	BEGIN 

		INSERT INTO inv_orden_compra_det (	
			IdOrdenEncfk,
			IdEmpresafk,
			IdProductofk,
			cantidad,
			precio_unitario,
			sub_total,
			cantidad_recepcionada,
			finalizado,
			exento_iva,
			Bonificacion
		)
		VALUES(@I_IDOrdenCompraEnc, @I_IdEmpresa,  @I_Producto, @I_Cantidad,   @I_PrecioUnitario, @I_Subtotal, 0, 'N',0,0)

		SET @Resultado = (SELECT	@@RowCount);

		IF @Resultado > 0
		BEGIN 
			SELECT 0 AS 'codigo', CONCAT('Se ha creado la Orden de Compra-', @I_IDOrdenCompraEnc) AS 'descripcion',@I_IDOrdenCompraEnc AS 'resultado';
			COMMIT TRAN
		END
		ELSE
		BEGIN 
			SELECT -1 AS codigo, CONCAT('No se pudo ingresar el detalle de la Orden de Compra-', @I_IDOrdenCompraEnc) AS descripcion,@I_IDOrdenCompraEnc AS resultado, -1 AS tipo_error;
			ROLLBACK TRAN
		END
		

	END
	ELSE IF (@i_id_productoClase = 'S' AND @I_TipoHono = 'A')
	BEGIN
	
		INSERT INTO  dbo.inv_orden_compra_det 
			( IdOrdenEncfk 
			, IdEmpresafk 
			, sub_total 
			, SerieAdmision 
			, Admision 
			, Paciente 
			)
		VALUES
			( @I_IDOrdenCompraEnc
			, @I_IdEmpresa
			, @I_Subtotal
			, @I_SerieAdmision
			, @I_Admision
			, @I_Paciente
			)

		SET @Resultado = (SELECT	@@RowCount);

			IF @Resultado > 0
			BEGIN 
				INSERT INTO INVENTARIO..OrdenCompraHonosMedicos
				VALUES (@I_IdEmpresaU
					, @I_IdEmpresa
					, @I_IDOrdenCompraEnc
					, @I_SerieAdmision
					, @I_Admision
					, @I_Medico
					, @I_Paciente
					, @I_SerieFacturaMed
					, @I_FacturaMed
					, @I_TipoHono
					, GETDATE()
					, @I_Subtotal
					, 'A')
			END

			IF @Resultado > 0
			BEGIN 
				
				SELECT 0 AS 'codigo', CONCAT('Se ha creado la Orden de Compra - ', @I_IDOrdenCompraEnc) AS 'descripcion',@I_IDOrdenCompraEnc AS 'resultado';
				COMMIT TRAN
			END
			ELSE BEGIN
				SELECT -1 AS codigo, CONCAT('No se ingreso el detalle, de la Orden de Compra: ', @I_IDOrdenCompraEnc) AS descripcion,@I_IDOrdenCompraEnc AS resultado, -1 AS tipo_error;;
				ROLLBACK TRAN
			END

	END
	ELSE IF (@i_id_productoClase = 'S' AND @I_TipoHono = 'O')
	BEGIN
	
		INSERT INTO INVENTARIO..OrdenCompraHonosMedicos
		VALUES (@I_IdEmpresaU
			, @I_IdEmpresa
			, @I_IDOrdenCompraEnc
			, NULL
			, NULL
			, @I_Medico
			, NULL
			, @I_SerieFacturaMed
			, @I_FacturaMed
			, @I_TipoHono
			, GETDATE()
			, @I_Subtotal
			, 'A')
		
		SET @Resultado = (SELECT	@@RowCount);

			IF @Resultado > 0
			BEGIN 
				SELECT 0 AS 'codigo', CONCAT('Se ha creado la Orden de Compra - ', @I_IDOrdenCompraEnc) AS 'descripcion',@I_IDOrdenCompraEnc AS 'resultado';
				COMMIT TRAN
			END
			ELSE BEGIN
				SELECT -1 AS codigo, CONCAT('No se ingreso el detalle, de la Orden de Compra: ', @I_IDOrdenCompraEnc) AS descripcion,@I_IDOrdenCompraEnc AS resultado, -1 AS tipo_error;;
				ROLLBACK TRAN
			END
	END
	
	END TRY
		BEGIN CATCH
		ROLLBACK TRAN
		Set @ErrRaise = ERROR_MESSAGE()
		Set @ErrRaiseNum = ERROR_NUMBER()
		
		RAISERROR(@ErrRaise, 16, @ErrRaiseNum)
	END CATCH
​END


-->>  Crear el Procedimiento Almacenado -  SpInvOrdenCompra

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[SpInvOrdenCompra]    Script Date: 12/03/2024 09:55:22 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/**************************************************************************

CREADO POR: Edvin Geovany Jucup         Numero de Rational  330014
FECHA CREACIÓN: 09/08/2023
PRODUCTO: INVENTARIO
**************************   DESCRIPCIÓN    ******************************
Este procedimiento tiene como objetivo obtener las ordenes de compra
exec [SpInvOrdenCompra]
**************************************************************************/
CREATE PROCEDURE [dbo].[SpInvOrdenCompra]
		@i_EmpresaReal char(3) = '',
		@i_EmpresaBod char(3) = '',
		@i_IdOrdenCompra int = 0,
		@i_operacion   char(1) = 'E'
AS
DECLARE @c_existecodigo int  = 0;
DECLARE @c_resultado int  = 0;
DECLARE	@ErrRaise	Varchar(99)	= '';
DECLARE	@ErrRaiseNum INT		= 0;
DECLARE @NitEmpresa Varchar(16) = '';
DECLARE @NombreEmpresa Char(80) = ''
BEGIN	
	SET NOCOUNT ON;


	--Inicio de excepcion
	BEGIN TRY

		 SELECT
			@NombreEmpresa = LTRIM(RTRIM(RazonSocial)),
			@NitEmpresa  = NIT
		FROM CONTADB..Empresas(nolock)
		WHERE (Codigo = @i_EmpresaReal
		OR @i_EmpresaReal = '')
		ORDER BY 2

			/**********************  CONSULTAR ENCABEZADO ORDEN DE COMPRA *********************************/				
			if @i_operacion = 'E' 
			begin

					SELECT
						  oce.IDORDENCOMPRAENC,
						  CONVERT(varchar(10), oce.fecha_creacion, 103) AS 'FECHA',
						  CAST(oce.Total AS DECIMAL(20,2)) AS 'TOTAL',
						  PR.Nombre AS 'PROVEEDOR',
						  PR.Nit AS 'NIT_PROV',
						  CASE
							WHEN hh1.IdCorporativofk IS NOT NULL THEN RTRIM(concat(concat(hh1.IdCorporativofk, ' - '), crp1.Nombres, ' ', crp1.Apellidos))
							ELSE ''
						  END AS 'RESPONSABLE_1',
						  RTRIM(LTRIM(oce.estado)) AS 'ESTADO1',
						  CASE
							WHEN RTRIM(oce.estado) = 'A' THEN 'Autorizada por: '
							WHEN RTRIM(oce.estado) = 'P' THEN 'Pendiente Autorización de: '
							WHEN RTRIM(oce.estado) = 'R' THEN 'Rechazada por: '
							WHEN RTRIM(oce.estado) = 'C' THEN 'Anulada por:'
							ELSE 'Pendiente Asignación'
						  END 'ESTADO_AUTORIZACION1',
						  RTRIM(LTRIM(hh2.estado)) AS 'ESTADO2',
						  CASE
							WHEN hh2.IdCorporativofk IS NOT NULL THEN RTRIM(concat(crp2.Nombres, ' ', crp2.Apellidos))
							ELSE ''
						  END AS 'RESPONSABLE_2',
						  CASE
							WHEN RTRIM(oce.estado) = 'A' THEN 'Autorizada'
							WHEN RTRIM(oce.estado) = 'P' THEN 'Pendiente'
							WHEN RTRIM(oce.estado) = 'R' THEN 'Rechazada'
							WHEN RTRIM(oce.estado)= 'C' THEN 'Anulada por: '
							ELSE 'Pendiente Asignación'
						  END 'ESTADO_AUTORIZACION2',
						  CASE
							WHEN oce.IdCorporativoAnula IS NOT NULL THEN RTRIM(concat(concat(oce.IdCorporativoAnula, ' - '), crp3.Nombres, ' ', crp3.Apellidos))
							ELSE ''
						  END AS 'USUARIO_ANULO',
						  @NombreEmpresa AS 'NOMBRE_EMPRESA',
						  @NitEmpresa AS 'NIT_EMPRESA',
						  CONCAT(CONCAT(oce.IdDepartamentofk, ' - '),  D.Nombre) AS 'DEPARTAMENTO',
						  CASE
							WHEN oce.IdProductoClasefk = 'P' THEN 'BIEN'
							WHEN oce.IdProductoClasefk = 'S' THEN 'SERVICIO'
						  END 'TIPO_OC',
						oce.cant_pagos_propuesta AS 'PAGOS',
						oce.dias_credito_propuesta AS 'DIAS_CREDITO',
						oce.observaciones AS 'OBSERVACION',
						'Calz. Aguilar Batres 48-32 zona 11, Col. Castañas, Villa Nueva' AS 'LUGAR_ENTREGA',
						'22186400' AS 'TELEFONO'
						, 
						 CONVERT(varchar(10), oce.fecha_auto_rech_1, 103) AS 'FECHA_AUTORIZACIÓN'
						FROM inv_orden_compra_enc AS oce
						INNER JOIN inventario..proveedores AS pr
						  ON oce.IDPROVEEDORFK = PR.CODIGO
						  AND PR.EMPRESA = oce.ProveedorEmpresa
						INNER JOIN INVENTARIO..DepartamentosInv D
						  ON oce.IdDepartamentofk = D.codigo
						  AND D.Empresa = @i_EmpresaBod
						LEFT JOIN inv_autorizacion_documento_historial AS hh1
						  ON oce.IdSolicitudEncfk = hh1.IdSolicitudEncfk
						  AND hh1.IdDocumentofk = 3
						  AND hh1.num_documento = 1
						  AND hh1.IdOrdenCompraEncFk = oce.IdOrdenCompraEnc
						LEFT JOIN CONTADB..CORPORATIVOS AS crp1
						  ON hh1.IdCorporativofk = crp1.Corporativo
						LEFT JOIN inv_autorizacion_documento_historial AS hh2
						  ON oce.IdSolicitudEncfk = hh2.IdSolicitudEncfk
						  AND hh2.IdDocumentofk = 3
						  AND hh2.num_documento = 2
						  AND hh2.IdOrdenCompraEncFk = oce.IdOrdenCompraEnc
						LEFT JOIN CONTADB..CORPORATIVOS AS crp2
						  ON hh2.IdCorporativofk = crp2.Corporativo
						LEFT JOIN CONTADB..CORPORATIVOS AS crp3
						  ON oce.IdCorporativoAnula = crp3.Corporativo
						WHERE oce.IdOrdenCompraEnc = @i_IdOrdenCompra
						and  oce.IdEmpresasfk = @i_EmpresaReal


			END
			/**********************  CONSULTAR DETALLE ORDEN DE COMPRA *********************************/				
			ELSE IF @i_operacion = 'D' 
			BEGIN

						SELECT ocd.IdProductofk AS 'CODIGO', pr.NOMBRE as 'PRODUCTO', ocd.CANTIDAD, ocd.PRECIO_UNITARIO, OCD.SUB_TOTAL FROM INV_ORDEN_COMPRA_DET  as ocd 
						inner join inventario..productos as pr
							on ocd.IDPRODUCTOFK = pr.codigo and pr.empresa = @i_EmpresaBod
						WHERE IDORDENENCFK = @i_IdOrdenCompra  
						and   IdEmpresafk = @i_EmpresaReal
			END


	END TRY
	BEGIN CATCH
		Set @ErrRaise = ERROR_MESSAGE()
		Set @ErrRaiseNum = ERROR_NUMBER()
		
		RAISERROR(@ErrRaise,16,@ErrRaiseNum)
	END CATCH


END


-->>  Crear el Procedimiento Almacenado -  SP_InvValidaAnulaOrdenCompraLista

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[SP_InvValidaAnulaOrdenCompraLista]    Script Date: 12/03/2024 09:55:47 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



/********************************************************************************************/
/*  CREADO POR:  		 Blanca Beatríz Cetino Rodríguez  	Numero de Rational:  330014		*/
/*	FECHA CREACIÓN:		 02/11/2023									                		*/
/*	PRODUCTO - PROCESO:  CARGA DE ORDENES A VALIDAR O ANULAR								*/
/**************************************** DESCRIPCION ***************************************/
/*	Este procedimiento tiene como objetivo consultar los datos de las ordenes de compra		*/
/*  para poder validarlas si son de servicios o anularlas									*/
/*																							*/
/*************************************** MODIFICACIÓN ***************************************/
/*	FECHA					RESPONSABLE					DESCRIPCION							*/
/*																							*/
/********************************************************************************************/
 

CREATE PROCEDURE [dbo].[SP_InvValidaAnulaOrdenCompraLista]			


	
@i_fecha_inicio date = '',
@i_fecha_fin date = '',
@i_empresa varchar(3) = '',
@i_estado char(2) = '', 
@i_departamento char(02) = '',
@i_proveedor char(12) = '',
@i_repcionada char(1) = '',
@i_dOrden int = 0,
@i_aOrden int = 0,
@i_tipoSeleccion char(1) = '',
@i_empresaBod char(3) = ''


AS			
BEGIN			

		SELECT PROV.CODIGO, 
			ICE.IDORDENCOMPRAENC, 
			ICE.IDSOLICITUDENCFK, 
			rtrim(ICE.IDEMPRESASFK) as IDEMPRESASFK, 
			ICE.TOTAL,
			ICE.CANT_LINEAS, 
			ICE.OBSERVACIONES, 
			RTRIM(ICE.IDDEPARTAMENTOFK) AS IDDEPARTAMENTOFK,
			DEPT.NOMBRE AS DESCRIPCION_DEPTO,
			'' AS DESCRIPCION_BODEGA,
			SC.OBSERVACIONES,
			CASE WHEN RTRIM(ICE.IDPRODUCTOCLASEFK) = 'P' THEN 'BIEN'
			WHEN RTRIM(ICE.IDPRODUCTOCLASEFK) = 'S' THEN 'SERVICIO'
			END AS IDPRODUCTOCLASEFK, 
			RTRIM(ICE.IDPRODUCTOSUBFK) AS IDPRODUCTOSUBFK, 
			ICE.FECHA_MAXIMA_RECEPCION, 
			RTRIM(ICE.IDPROVEEDORFK) AS IDPROVEEDORFK, 
			PROV.NOMBRE AS DESCRIPCION_PROVEEDOR, 
			PROV.NIT ,
			RTRIM( ICE.estado) AS ESTADO,
			CASE WHEN  RTRIM(ICE.estado)  = 'P' THEN 'PROCESO'
			WHEN RTRIM(ICE.estado)  = 'A' THEN 'AUTORIZADO'
			WHEN RTRIM(ICE.estado)  = 'C' THEN 'ANULADO'
			ELSE 'RECHAZADO'
			END AS DESCRIPCION_ESTADO,
			ICE.IdCorporativoAR1_fk  USUARIO_AUTORIZO,
			'' AS EMPRESA_DIRECCION,
			'' as EMPRESA_CORREO,
			'' AS EMPRESA_TELEFONO,
			FORMAT(ICE.fecha_creacion, 'dd/MM/yyyy') AS FECHA_CREACION_ORDEN,
			ADH.IDAUTORIZACIONHISTORIAL,
			ADH.num_documento AS NUMERO_DOCUMENTO,
			ADH.IdDocumentofk AS ID_DOCUMENTO_,
			'' AS NOMBRE_EMPRESA,
			RTRIM(PROV.Nit) AS PROV_NIT,
			RTRIM(PROV.CONTACTO) AS PROV_CONTACTO,
			RTRIM(PROV.EMAIL) AS PROV_CORREO ,
			RTRIM(CRP.Correo) AS CORREO_RESPONSABLE,
			ICE.RECEPCIONADA VALIDADA,
			CASE WHEN  RTRIM(ICE.RECEPCIONADA)  = 'S' THEN 'VALIDADA'
			WHEN RTRIM( ICE.RECEPCIONADA)  = 'N' THEN 'NO VALIDADA'
			END AS DESCRIP_VALIDADA,
			ICE.VALIDACION,
			ICE.RevisoConformidad USUARIO,
			ICE.ObservacionesValidacion OBSERVACION_VALIDACION,
			ICE.dias_credito_propuesta DIAS,
			ICE.cant_pagos_propuesta PAGOS,
			ICE.fecha_auto_rech_1  FECHA_UTORIZACION,
			ICE.orden_ortopedia ORTOPEDIA
		FROM INV_ORDEN_COMPRA_ENC AS ICE
		INNER JOIN INV_SOLICITUD_COMPRA_ENC AS SC
			ON ICE.IdSolicitudEncfk = SC.IdSolicitudEnc
		RIGHT JOIN INV_AUTORIZACION_DOCUMENTO_HISTORIAL AS ADH
			ON ICE.IdSolicitudEncfk = ADH.IdSolicitudEncfk
			AND ICE.IdOrdenCompraEnc = ADH.IDORDENCOMPRAENCFK
			AND  ADH.IdDocumentofk = 3 
		INNER JOIN INVENTARIO..DepartamentosINV  DEPT
			ON ICE.IDDEPARTAMENTOFK = DEPT.CODIGO
			AND DEPT.EMPRESA = @i_empresaBod
		INNER JOIN INVENTARIO..PROVEEDORES  AS PROV
			ON ICE.IDPROVEEDORFK = PROV.CODIGO
			AND PROV.EMPRESA = ICE.ProveedorEmpresa
		INNER JOIN CONTADB..CORPORATIVOS AS CRP
			ON CRP.CORPORATIVO =  ADH.IdCorporativofk
		WHERE  ICE.IDEMPRESASFK = @i_empresa
			AND (@i_estado IS NULL OR CONCAT(RTRIM(ICE.estado),' ') LIKE '%' + LTRIM(RTRIM(@i_estado )) +'%')
			AND (@i_departamento IS NULL OR CONCAT(RTRIM(ICE.IDDEPARTAMENTOFK),' ') LIKE '%' + LTRIM(RTRIM(@i_departamento)) +'%')
			AND (@i_proveedor IS NULL OR CONCAT(RTRIM(PROV.CODIGO),' ') LIKE '%' + LTRIM(RTRIM(@i_proveedor)) +'%')
			AND (@i_repcionada IS NULL OR CONCAT(RTRIM(ICE.RECEPCIONADA),' ') LIKE '%' + LTRIM(RTRIM(@i_repcionada)) +'%')
			AND (ICE.IdOrdenCompraEnc >= @i_dOrden OR @i_dOrden = 0)
			AND (ICE.IdOrdenCompraEnc <= @i_aOrden OR @i_aOrden = 0)
			AND (@i_tipoSeleccion IS NULL OR CONCAT(RTRIM(ICE.IdProductoClasefk),' ') LIKE '%' + LTRIM(RTRIM(@i_tipoSeleccion)) +'%')
			AND convert(varchar,  ICE.FECHA_CREACION, 23)  >= @i_fecha_inicio 
			AND convert(varchar,  ICE.FECHA_CREACION, 23)  <= @i_fecha_fin
END			



-->>  Crear el Procedimiento Almacenado -  SP_InvValidacionOrdenCompraS 

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[SP_InvValidacionOrdenCompraS]    Script Date: 12/03/2024 09:56:10 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/********************************************************************************************/
/*  CREADO POR:  		 Blanca Beatríz Cetino Rodríguez  	Numero de Rational:  330014 	*/
/*	FECHA CREACIÓN:		 26/10/2023									                		*/
/*	PRODUCTO - PROCESO:  VALIDACIÓN ORDEN DE COMPRA	SERVICIO								*/
/**************************************** DESCRIPCION ***************************************/
/*	Este procedimiento tiene como objetivo Validar y agregar campos de validación para las  */
/*  órdenes de compra de servicio															*/
/*																							*/
/*************************************** MODIFICACIÓN ***************************************/
/*	FECHA	 				RESPONSABLE														*/
/*	DETALLE																					*/
/********************************************************************************************/


CREATE PROCEDURE [dbo].[SP_InvValidacionOrdenCompraS]

@IEmpresa CHAR(3) = '',
@ICorporativo INT = 0,
@IEstado CHAR(2) = '',
@IIdDocumento INT = 0,
@IObservaciones TEXT = '',
@IIdOrdenCompra INT = 0,
@ITipo CHAR(10) = '',
@IEstadoVal CHAR(2) = '' --S Validada - N No Validada

AS
	
	DECLARE @CorrelativoVal INT = 0; 
	DECLARE @ErrRaise VARCHAR(99) = '';
	DECLARE @ErrRaiseNum INT = 0
BEGIN

--Inicio de transaccion 
	BEGIN TRAN
	--Inicio de excepcion
		BEGIN TRY
				/****************************PROCESO  ORDEN DE COMPRA  *************************************************/
			IF (@IIdDocumento = '3')
			BEGIN

				-- VALIDA SI ES ORDEN DE COMPRA DE SERVICIO PARA OBTENER VALIDACIÓN
				IF (@ITipo = 'SERVICIO')
				BEGIN
					IF (@IEstado = 'A')
					BEGIN

						-- RECUPERA CORRELATIVO DE VALIDACIÓN  PARA SERVICIOS
						IF (@IEstadoVal = 'S')
						BEGIN

							

							SELECT
								@CorrelativoVal = SigValidacion
							FROM CONTADB..Correlativos
							WHERE Empresa = @IEmpresa

							--  Actualización DE CORRELATIVO DE VALIDACIÓN
							UPDATE ContaDB..Correlativos
								SET SigValidacion = @CorrelativoVal +1
							WHERE Empresa = @IEmpresa

							-- ACTUALIZA ORDEN DE COMPRA -- SE AGREGA DATOS DE VALIDACIÓN
							UPDATE INV_ORDEN_COMPRA_ENC
							SET Recepcionada = @IEstadoVal,
								Validacion = @CorrelativoVal,
								RevisoConformidad = @ICorporativo,
								FechaRevisoConformidad = GETDATE(),
								ObservacionesValidacion = @IObservaciones
							WHERE IdOrdenCompraEnc = @IIdOrdenCompra
							AND IdEmpresasfk = @IEmpresa

							COMMIT TRAN;
						END 

						IF (@IEstadoVal = 'S')
						BEGIN 
							SELECT	'0' AS 'codigo',	CONCAT('Se ha Validado - No. Validación: ', @CorrelativoVal) AS 'descripcion',	'0' AS 'resultado';
						END 
						ELSE
						BEGIN 

							-- ACTUALIZA ORDEN DE COMPRA -- SE AGREGA DATOS DE VALIDACIÓN
							UPDATE INV_ORDEN_COMPRA_ENC
							SET Recepcionada = @IEstadoVal,
								Validacion = NULL,
								RevisoConformidad = NULL,
								FechaRevisoConformidad = NULL,
								ObservacionesValidacion = NULL
							WHERE IdOrdenCompraEnc = @IIdOrdenCompra
							AND IdEmpresasfk = @IEmpresa

							COMMIT TRAN;

							SELECT	'0' AS 'codigo',	'Se ha anulado la validación' AS 'descripcion',	'0' AS 'resultado';
						END

					END	
					ELSE 
					BEGIN
						SELECT	'1' AS 'codigo',	'Estado no apto para validar!' AS 'descripcion',	'0' AS 'resultado';
					END 
				END
				ELSE 
				BEGIN
					SELECT	'1' AS 'codigo',	'No es Orden de Compra - Servicio!' AS 'descripcion',	'0' AS 'resultado';
				END
			END
		END TRY
	BEGIN CATCH
		ROLLBACK TRAN
		SET @ErrRaise = ERROR_MESSAGE()
		SET @ErrRaiseNum = ERROR_NUMBER()

		RAISERROR (@ErrRaise, 16, @ErrRaiseNum)
	END CATCH
END


-->>  Crear el Procedimiento Almacenado  -  SpInvReportesOC

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[SpInvReportesOC]    Script Date: 12/03/2024 09:56:28 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


/**********************************************************************************************/
/*	CREADO POR:  		 Blanca Beatríz Cetino Rodríguez 	Numero de Rational:	330014		  */
/*	FECHA CREACIÓN:		 06/09/2023															  */
/*	PRODUCTO - PROCESO:  Ordenes de Compra - Reportes										  */
/**************************************** DESCRIPCION *****************************************/
/*	Este procedimiento tiene como objetivo generar los reportes del módulo de Autorización    */
/*  Orden de Compra	- Reporte Ordenes Validadas C/Factura - Reporte Consolidado				  */
/*	Se filtra por departamento por solicitud de usuarios									  */
/*************************************** MODIFICACIÓN *****************************************/
/*					FECHA				 RESPONSABLE				DESCRIPCION				  */
/*																							  */
/**********************************************************************************************/

CREATE PROCEDURE [dbo].[SpInvReportesOC]
(

	@IOpcion CHAR(1) = ''
	, @IEmpresaR CHAR(3) = ''
	, @IEmpresaBodega CHAR(3) = ''
	, @IFinicial DATETIME = ''
	, @IFfinal DATETIME = ''
	, @IDepartamento CHAR(2) = ''
	, @IEstado CHAR(1) = ''
	, @IdOrden INT = 0
	, @IaOrden INT = 0
	, @IProveedor CHAR(12) = ''
	, @IRecepcion CHAR(1) = ''
	, @ITipoSel CHAR(1) = ''
	, @IUsuario INT = 0
	, @IEmpresaSucursal CHAR(3) = ''

	
)
AS
BEGIN

	DECLARE	@ErrRaise	VARCHAR(99)	= '';
	DECLARE	@ErrRaiseNum INT		= 0;
	DECLARE @NitEmpresa VARCHAR(16) = '';
	DECLARE @NombreEmpresa CHAR(80) = ''
	DECLARE @FechaInicial DATE
	DECLARE @FechaFinal   DATE


	SELECT
			@NombreEmpresa = LTRIM(RTRIM(RazonSocial)),
			@NitEmpresa  = NIT
		FROM CONTADB..Empresas(nolock)
		WHERE (Codigo = @IEmpresaR
		OR @IEmpresaR = '')
		ORDER BY 2
		
	

	--REPORTE DE ORDENES VALIDADAS
	IF @IOpcion = '1'
	BEGIN

		SET @FechaInicial = CONVERT(DATE, @IFinicial )
		SET @FechaFinal = CONVERT (DATE, @IFfinal)

		
		SELECT
				CONCAT(CONCAT(O.IdEmpresasfk, ' - '), @NombreEmpresa)  AS Empresa,
				O.IdOrdenCompraEnc AS Orden,
				CONVERT(varchar(10), O.fecha_creacion, 103) AS Fecha,
				CONCAT(CONCAT(CONCAT(CONCAT(O.IdCorporativoSolicitafk, ' - '), C.Nombres), ' '), C.Apellidos) AS Solicitante,
				O.IdDepartamentofk AS Departamento,
				D.Nombre AS DeptoCompras,
				O.ObservacionesValidacion  AS ObservacionesVerificacion,
				O.Estado AS EstadoOrden,
				O.Observaciones AS DescripcionOrden,
				O.Recepcionada AS IngresoBodega,
				CONVERT(varchar(10), O.fecha_creacion, 103) AS Registro,
				O.IdProductoClasefk AS BienServicio,
				CASE
					WHEN RTRIM(O.IdProductoClasefk) = 'P' THEN 'BIEN'
					ELSE 'SERVICIO'
				END AS Tipo,
				CONCAT(CONCAT(CONCAT(CONCAT(O.RevisoConformidad, ' - '), CR.Nombres), ' '), CR.Apellidos) AS RevisoConformidad,
				CONVERT(varchar(10), O.fecha_auto_rech_1, 103) AS FechaRevisoConformidad,
				O.Validacion AS Validacion,
				M.Proveedor,
				M.Factura,
				CONVERT(varchar(10), M.Fecha, 103) AS FechaIngreso,
				P.Nombre AS Nombreproveedor,
				P.Nit,
				O.total AS ValorCotizacion,
				CONVERT(varchar(10), O.fecha_auto_rech_1, 103) AS FechaAutorizacion,
				CONCAT(CONCAT(CONCAT(CONCAT(O.IdCorporativoAR1_fk, ' - '), CF.Nombres), ' '), CF.Apellidos) AS Firma,
				COM.Valor AS ValorCompra,
				COM.Contrasena,
				BD.Codigo AS Bodega,
				BD.Nombre AS BodegaIngreso,
				M.Comentario
			FROM INVENTARIO..Bodegas bd
			LEFT JOIN INVENTARIO..Movimientos M
				ON M.Empresa	     = BD.Empresa
				AND  M.BodegaDestino = BD.Codigo
			LEFT JOIN INVENTARIO..Compras COM
				ON  COM.Empresa    = BD.EMpresaRealx
				and COM.Proveedor  = M.Proveedor
				AND COM.Documento  = M.Factura
			LEFT JOIN inv_orden_compra_enc O 
				ON O.IdOrdenCompraEnc = M.CodigoOrden
				AND O.IdEmpresasfk = BD.EMpresaRealx 			 
			LEFT JOIN INVENTARIO..DepartamentosInv D   
				ON D.Empresa	= BD.Empresa
				AND D.Codigo   = O.IdDepartamentofk 		
			LEFT JOIN INVENTARIO..Proveedores  P   
				ON P.Empresa =  O.ProveedorEmpresa
				AND P.Codigo =  O.IdProveedorfk
			--TABLA DE HISTORIAL DE AUTORIZACIONES
			LEFT JOIN  HOSPITAL..INV_AUTORIZACION_DOCUMENTO_HISTORIAL A 
				ON  A.IdOrdenCompraEncFk = O.IdOrdenCompraEnc
			--CORPORATIVO QUE SOLICITA
			INNER JOIN CONTADB..CORPORATIVOS C
				ON C.Corporativo =	O.IdCorporativoSolicitafk
			--CORPORATIVO REVISA CONFORMIDAD
			INNER JOIN CONTADB..CORPORATIVOS AS CR
				ON CR.Corporativo = O.IdCorporativoAR1_fk
			--CORPORATIVO QUE FIRMA
			INNER JOIN CONTADB..CORPORATIVOS AS CF 
				ON CF.Corporativo = O.IdCorporativoAR1_fk
			WHERE
				BD.Empresa = @IEmpresaBodega
				AND COM.Empresa IS NOT NULL
				AND M.TipoMovimiento = '1'
				AND A.IdDocumentofk = 3
				AND  CONVERT (DATE, O.FechaRevisoConformidad) >=  @FechaInicial 
				AND CONVERT (DATE, O.FechaRevisoConformidad) <= @FechaFinal
				AND O.Validacion IS NOT NULL
				AND (@IDepartamento IS NULL OR CONCAT(RTRIM(O.IdDepartamentofk),' ') LIKE '%' + LTRIM(RTRIM(@IDepartamento)) +'%')
			ORDER BY O.IdOrdenCompraEnc ASC
	END 


	--REPORTE CONSOLIDADO POR AUTORORIZACIÓN
	IF @IOpcion = '2'
	BEGIN
		SELECT  
			CONCAT(CONCAT(O.IdEmpresasfk, ' - '), @NombreEmpresa)  AS Empresa,
			O.IdOrdenCompraEnc AS Orden,
			O.IdDepartamentofk AS Departamento,
			D.Nombre,
			CONVERT(varchar(10), O.fecha_creacion, 103) AS Fecha,
			CASE WHEN RTRIM(O.IdProductoClasefk)  = 'P' THEN 'BIEN'
			ELSE'SERVICIO'
			END AS Tipo,
			CASE WHEN RTRIM(O.Estado)  = 'P' THEN 'PENDIENTE'
			WHEN RTRIM(O.Estado)  = 'A' THEN 'AUTORIZADA'
			WHEN RTRIM(O.Estado)  = 'C' THEN 'ANULADA'
			WHEN RTRIM(O.Estado)  = 'R' THEN 'RECHAZADA'
			END AS Estado,
			O.Validacion AS Validacion,
			O.observaciones AS Descripcion,
			O.TOTAL AS Monto,
			O.ObservacionesValidacion AS ObservacionesVerificacion,
			P.Nit,
			O.IdProveedorfk  Proveedor,
			P.Nombre AS NombreProveedor,
			O.dias_credito_propuesta AS DiasCredito
		FROM INVENTARIO.dbo.Proveedores AS P
		RIGHT JOIN inv_orden_compra_enc AS O
			ON P.Empresa = O.ProveedorEmpresa
			AND P.Codigo = O.IdProveedorfk
		INNER JOIN INVENTARIO..DepartamentosInv AS D
			  ON D.Codigo = O.IdDepartamentofk
			  AND D.Empresa =  @IEmpresaBodega
		INNER JOIN INV_AUTORIZACION_DOCUMENTO_HISTORIAL AS A
			ON O.IdOrdenCompraEnc = A.IdOrdenCompraEncFk
		WHERE O.IdEmpresasfk = @IEmpresaR
		AND A.IdDocumentofk = 3
		AND (@IEstado IS NULL OR CONCAT(RTRIM(O.Estado),' ') LIKE '%' + LTRIM(RTRIM(@IEstado)) +'%')
		AND CONVERT(VARCHAR,  O.fecha_creacion, 23)  >= @IFinicial 
		AND CONVERT(VARCHAR,  O.fecha_creacion, 23)  <= @IFfinal
		AND (@IDepartamento IS NULL OR CONCAT(RTRIM(O.IdDepartamentofk),' ') LIKE '%' + LTRIM(RTRIM(@IDepartamento)) +'%')
		AND (O.IdOrdenCompraEnc >= @IdOrden OR @IdOrden = 0)
		AND (O.IdOrdenCompraEnc <= @IaOrden OR @IaOrden = 0)
		AND (@IProveedor IS NULL OR CONCAT(RTRIM(O.IdProveedorfk),' ') LIKE '%' + LTRIM(RTRIM(@IProveedor)) +'%')
		AND (@IRecepcion IS NULL OR CONCAT(RTRIM(O.recepcionada),' ') LIKE '%' + LTRIM(RTRIM(@IRecepcion)) +'%')
		AND (@ITipoSel IS NULL OR CONCAT(RTRIM(O.IdProductoClasefk),' ') LIKE '%' + LTRIM(RTRIM(@ITipoSel)) +'%')
		AND A.IdCorporativofK = @IUsuario
		ORDER BY O.IdOrdenCompraEnc ASC

		
	END
	--REPORTE CONSOLIDADO POR SOLICITUD 
	IF @IOpcion = '3'
	BEGIN
		SELECT  
			CONCAT(CONCAT(O.IdEmpresasfk, ' - '), @NombreEmpresa)  AS Empresa,
			O.IdOrdenCompraEnc AS Orden,
			O.IdDepartamentofk AS Departamento,
			D.Nombre,
			CONVERT(varchar(10), O.fecha_creacion, 103) AS Fecha,
			CASE WHEN RTRIM(O.IdProductoClasefk)  = 'P' THEN 'BIEN'
			ELSE'SERVICIO'
			END AS Tipo,
			CASE WHEN RTRIM(O.Estado)  = 'P' THEN 'PENDIENTE'
			WHEN RTRIM(O.Estado)  = 'A' THEN 'AUTORIZADA'
			WHEN RTRIM(O.Estado)  = 'C' THEN 'ANULADA'
			WHEN RTRIM(O.Estado)  = 'R' THEN 'RECHAZADA'
			END AS Estado,
			O.Validacion AS Validacion,
			O.observaciones AS Descripcion,
			O.TOTAL AS Monto,
			O.ObservacionesValidacion AS ObservacionesVerificacion,
			P.Nit,
			O.IdProveedorfk  Proveedor,
			P.Nombre AS NombreProveedor,
			O.dias_credito_propuesta AS DiasCredito
		FROM INVENTARIO.dbo.Proveedores AS P
		RIGHT JOIN inv_orden_compra_enc AS O
			ON P.Empresa = O.ProveedorEmpresa
			AND P.Codigo = O.IdProveedorfk
		INNER JOIN INVENTARIO..DepartamentosInv AS D
			  ON D.Codigo = O.IdDepartamentofk
			  AND D.Empresa =  @IEmpresaBodega
		INNER JOIN INV_AUTORIZACION_DOCUMENTO_HISTORIAL AS A
			ON O.IdOrdenCompraEnc = A.IdOrdenCompraEncFk
		WHERE O.IdEmpresasfk = @IEmpresaR
		AND A.IdDocumentofk = 3
		AND (@IEstado IS NULL OR CONCAT(RTRIM(O.Estado),' ') LIKE '%' + LTRIM(RTRIM(@IEstado)) +'%')
		AND CONVERT(VARCHAR,  O.fecha_creacion, 23)  >= @IFinicial 
		AND CONVERT(VARCHAR,  O.fecha_creacion, 23)  <= @IFfinal
		AND (@IDepartamento IS NULL OR CONCAT(RTRIM(O.IdDepartamentofk),' ') LIKE '%' + LTRIM(RTRIM(@IDepartamento)) +'%')
		AND (O.IdOrdenCompraEnc >= @IdOrden OR @IdOrden = 0)
		AND (O.IdOrdenCompraEnc <= @IaOrden OR @IaOrden = 0)
		AND (@IProveedor IS NULL OR CONCAT(RTRIM(O.IdProveedorfk),' ') LIKE '%' + LTRIM(RTRIM(@IProveedor)) +'%')
		AND (@IRecepcion IS NULL OR CONCAT(RTRIM(O.recepcionada),' ') LIKE '%' + LTRIM(RTRIM(@IRecepcion)) +'%')
		AND (@ITipoSel IS NULL OR CONCAT(RTRIM(O.IdProductoClasefk),' ') LIKE '%' + LTRIM(RTRIM(@ITipoSel)) +'%')
		AND O.IdCorporativoSolicitafk = @IUsuario
		ORDER BY O.IdOrdenCompraEnc ASC

		
	END

END 







-->>INVENTARIO

-->> MODIFICACION INDICES TABLA MOVIMIENTOS

USE [INVENTARIO]
GO
--
	----------------------------------------------------------------------------------------
	DROP INDEX missing_index_54815_54814 ON Movimientos
	CREATE NONCLUSTERED INDEX IDX_02_Movimientos on Movimientos (Empresa, TipoMovimiento, CodigoOrden) include (Fecha, BodegaDestino, Proveedor, Factura, EmpresaSucursal, Comentario )
	
	
-->> MODIFICACION INDICES TABLA COMPRAS
	----------------------------------------------------------------------------------------
	----------------------------------------------------------------------------------------
	ALTER TABLE ComprasNotasCreditoDet
	DROP CONSTRAINT fk_NCCNotaCre
	
	ALTER TABLE ComprasNotasCreditoDet
	DROP CONSTRAINT fk_NCCCompra
	
	ALTER TABLE dbo.Compras
	DROP CONSTRAINT PK_Compras_2__11;
		CREATE UNIQUE  CLUSTERED INDEX IX_Compras on Compras (Empresa, Proveedor, Documento) ON [PRIMARY]
	
	ALTER TABLE [dbo].[ComprasNotasCreditoDet]  WITH CHECK ADD  CONSTRAINT [fk_NCCCompra] FOREIGN KEY([Empresa], [Proveedor], [CompraDoc])
	REFERENCES [dbo].[Compras] ([Empresa], [Proveedor], [Documento])
	GO
	
	ALTER TABLE [dbo].[ComprasNotasCreditoDet]  WITH CHECK ADD  CONSTRAINT [fk_NCCNotaCre] FOREIGN KEY([Empresa], [Proveedor], [NotaCredito])
	REFERENCES [dbo].[Compras] ([Empresa], [Proveedor], [Documento])
	GO

-->> MODIFICACION INDICE TABLA ADMISIONES
USE [HOSPITAL]
GO
ALTER INDEX [missing_index_179_178] ON [dbo].[Admisiones] REBUILD PARTITION = ALL WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
GO

	
-->> MODIFICACIÓN BODEGAS ACTUALIZANDO EMPRESA REAL SEM PARA BODEGA CODIGO 2
USE [INVENTARIO]
GO

UPDATE BODEGAS
SET EMPRESAREALX = 'SEM'
WHERE EMPRESA = 'BOD'
AND CODIGO = 2 
AND EMPRESAREALX = 'MED'


-->> Alter Store Procedure - SP_Inventario_Consulta_Producto
USE [INVENTARIO]
GO
/****** Object:  StoredProcedure [dbo].[SP_Inventario_Consulta_Producto]    Script Date: 13/03/2024 08:04:55 a.m. ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


/********************************************************************************************/
/*	CREADO POR:  		 HANDERSO SIANA  	  												*/
/*	FECHA CREACIÓN:		 11.06.2020									                		*/
/**************************************** DESCRIPCION ***************************************/
/*	Consulta de Inventario - Producto														*/
/*																							*/
/*************************************** MODIFICACIÓN ***************************************/
/*		RATIONAL  				FECHA						 RESPONSABLE					*/
/*		330014				JUL - SEP 2023			 Blanca Beatríz Cetino Rodríguez		*/
/*	PRODUCTO - PROCESO:  SOLICITUD DE COMPRA, COTIZACIÓN, ORDEN DE COMPRA MANUAL, INVENTARIO*/
/*  DESCRIPCION: Se agrega departamento, empresa bodega, subcategorias, se comenta la 		*/
/*  familia, subclase y clase que no seran tomadas en cuentas, y se fitlra	por tipo de		*/
/*  Producto P	y se agrega una apartado especifico para Ordenes de Compra / Inventario		*/
/*	06/NOV/2023																				*/
/*  BCETINO - EN LA CONVERSIÓN DE CORPORATIVO SE DEJA CONVERSIÓN DE CHAR(10) A CHAR(5)		*/
/********************************************************************************************/


ALTER PROCEDURE [dbo].[SP_Inventario_Consulta_Producto] 



   @i_operacion CHAR(2)=NULL,
   @i_accion    CHAR(1)=NULL,--ACCION A REALIZAR EN UNA TABLA, I INSERT, U UPDATE, S SELECT
   @i_Empresa Empresa=NULL, --Empresa
   @i_EmpresaBodega Empresa=NULL,--Empresa Bodega
   @i_CodigoProducto CHAR(25)=NULL, -- Código del producto.Código Hospital
   @i_Nombre CHAR(100)=NULL, -- Nombre Comercial del producto
   @i_Clase CHAR(20)=NULL, --Clase del producto... (MEDICAMENTOS, SUMINISTROS, SERVICIOS).
   @i_SubClase CHAR(1020)=NULL, --SubClase del producto... (CAD, NOCAD, EXTRAORD.)
   @i_TipoBodega CHAR(1)=NULL,
   @i_Tipo CHAR(1)=NULL,
   @i_Costo DECIMAL(18,5)=NULL, -- El Costo (con iva.) del producto
   @i_Categoria CHAR(20)=NULL, -- Categorias
   --- PRECIO 
   @i_factor DECIMAL(18,5)=NULL,
   @i_porcentaje DECIMAL(18,5)= NULL,
   @i_precio DECIMAL(18,5)=NULL,
   @i_departamento CHAR(2) = NULL
AS
BEGIN

--Variable de Trabajo
DECLARE 
    @w_Impuesto DECIMAL(18,5)=0;
    
--Variables Principales  Session en MID--------------------

	SELECT
		@w_Impuesto =(1+ (InvFactorImpuestoCompra/100))
	FROM
		CONTADB.dbo.EmpresasDefaults
	WHERE
		--Empresa =@i_Empresa;
		  Empresa =@i_EmpresaBodega;
-----------------------------------------------------------
   
		 
IF @i_operacion='A'
BEGIN

	IF @i_accion = 'A'	--PRODUCTO SELECT ACTIVOS
	BEGIN
		 
		 SELECT TOP 12
			A.Empresa,
			RTRIM(RTRIM(A.Codigo)) AS Codigo,
			RTRIM(RTRIM(A.Nombre)) AS Nombre,
			RTRIM(RTRIM(A.NombreGenerico)) AS NombreGenerico,
			A.Categoria,
			CAST(A.CostoUltimo AS DECIMAL(18,5))  AS CostoUltimo,
			CAST(A.CostoPromedio AS DECIMAL(18,5)) AS CostoPromedio,
			CAST(A.CostoAlto AS DECIMAL(18,4)) AS  CostoAlto,
			A.Departamento AS Departamento,
			A.SubDepartamento AS SubDepartamento,
			A.CodigoHospIFar AS CodigoHospIFar,
			A.Clase AS Clase,
			A.SubClase AS SubClase,
			A.Trasladable AS Trasladable,
			RTRIM(RTRIM(A.Descripcion)) AS Descripcion,
			A.Proveedor AS Proveedor,
			A.Marca AS Marca,
			A.Warning AS Warning,
			A.TipodeUso,
			A.Presentacion AS Presentacion,
			A.CostoExterno AS CostoExterno,
			A.Minimo AS Minimo,
			A.Maximo AS Maximo,
			A.EsActivoFijo AS EsActivoFijo,
			A.ExcentoIva AS ExcentoIva,
			A.Familia AS Familia,
			A.Consignacion AS Consignacion,
			A.TipoBodega AS TipoBodega,
			A.Ensamblado AS Ensamblado,
			----------CLASIFICACION---------
			A.Grupo AS Grupo,
			A.SubGrupo,
			A.Generico,
			---------Info. Extra.----------
			RTRIM(RTRIM(A.CampoExtra1)) AS CampoExtra1,
			RTRIM(RTRIM(A.CampoExtra2)) AS  CampoExtra2,
			RTRIM(RTRIM(A.CampoExtra3)) AS CampoExtra3,
			----------Max. y Min.---------
			A.MinTiempo,
			A.MaxTiempo,
			----------ESTADISTICA---------
			RTRIM(RTRIM(A.VentaEsperada)) AS VentaEsperada,
			RTRIM(RTRIM(A.UltimaVenta)) AS UltimaVenta,
			RTRIM(RTRIM(A.UltimaFacturaVenta)) AS UltimaFacturaVenta,
			A.UltimaCompra AS UltimaCompra,
			RTRIM(RTRIM(A.UltimaFacturaCompra))  AS UltimaFacturaCompra,
			A.FechaRegistro AS FechaRegistro,
			RTRIM(RTRIM(A.Usuario)) AS Usuario,
			---------COMPRAS----------
			RTRIM(RTRIM(A.Costeo)) AS Costeo,
			RTRIM(RTRIM(A.Impuestos)) AS Impuestos,
			CAST(ISNULL(A.MarkUp,0) AS DECIMAL(18,0)) AS MarkUp,
			RTRIM(RTRIM(A.Lnsp)) AS Lnsp,
			RTRIM(RTRIM(A.UnidadMedida)) AS UnidadMedida,
			RTRIM(RTRIM(A.UnidadVenta)) AS UnidadVenta,
			RTRIM(RTRIM(A.UnidadCompra)) AS UnidadCompra,
			RTRIM(RTRIM(A.Tipo)) AS Tipo,
			----------EXTRA----------	
			A.Activo,
			A.Intangible,
			A.Seriado,
			A.NOESAutorizado,
			B.Descripcion AS PresentacionNombre,
			B.Unidad AS PresentacionUnidad,
			C.Nombre AS  CategoriaNombre,
			--MANTENIMIENTO PRODUCTO.
			RTRIM(RTRIM(PC.VentaHospital)) AS VentaHospital,
			RTRIM(RTRIM(PC.UnidadCompra)) AS UnidadCompra,
			RTRIM(RTRIM(PC.RegistroSanitario)) AS RegistroSanitario,
			RTRIM(RTRIM(PC.Contraindicaciones)) AS Contraindicaciones,
			RTRIM(RTRIM(PC.ATC)) AS ATC,
			PC.CadenaFrio,
			PC.LuzDirecta,
			PC.Controlado,
			PC.Stock,
			PC.BajoPedido,
			PC.Sasi,
			PC.CatalogoGeneral,
			PC.CompraLocal,
			PC.Importado,
			PC.Usuario,
			------CAMPO FAMILIA
			RTRIM(RTRIM(F.Nombre)) AS FamiliaNombre,
			RTRIM(LTRIM(ISNULL(CO.Nombres,'')))  +' '+ RTRIM(LTRIM(ISNULL(CO.Apellidos,''))) AS NombreUsuario
		FROM
			INVENTARIO.dbo.Productos AS A
			INNER JOIN  INVENTARIO.dbo.NTUnidadesMedida AS B
			ON B.IdMedida=A.Presentacion
			LEFT JOIN HOSPITAL.DBO.Categorias AS C
			ON A.Categoria=C.Codigo
			AND A.Empresa=C.Empresa
			LEFT JOIN INVENTARIO.DBO.ProductoCaracteristicas AS PC
			ON PC.Codigo=A.Codigo
			AND A.Empresa=PC.Empresa
			LEFT JOIN INVENTARIO.DBO.Familia AS F
			ON F.Empresa=A.Empresa
			AND F.Codigo=A.Familia
			LEFT JOIN CONTADB.dbo.Corporativos AS CO
			ON A.Usuario=CONVERT(CHAR(5),CO.Corporativo)
			-------------------
			--INNER JOIN DepartamentosInv D
			--ON A.Departamento = D.CODIGO
			--INNER JOIN HOSPITAL..inv_categoria_departamento CD
			--ON CD.IdDepartamentofk = D.IdCategoriafk
		WHERE
			A.Empresa = @i_empresa
			AND A.Activo = 'S'
			--AND D.Codigo = @i_departamento
			--AND (@i_departamento IS NULL OR CONCAT(RTRIM(D.Codigo),' ') LIKE '%' + LTRIM(RTRIM(@i_departamento)) +'%')
			--AND A.Clase = @i_Clase
			--AND A.SubClase = @i_SubClase
			AND (@i_Nombre is null or  concat(rtrim(A.Nombre),' ',rtrim(A.NombreGenerico),', ',rtrim(A.Descripcion),', ',rtrim(A.Codigo)) like '%' + ltrim(rtrim(@i_Nombre)) +'%')
		ORDER BY 
			A.Codigo DESC --OFFSET ((10 *(1-1))) ROWS FETCH NEXT 12 ROWS ONLY;
		
	END
	
	
	IF @i_accion = 'B'	--Ficha del Producto
	BEGIN
		 
		SELECT TOP 12
			A.Empresa,
			RTRIM(RTRIM(A.Codigo)) AS Codigo,
			RTRIM(RTRIM(A.Nombre)) AS Nombre,
			RTRIM(RTRIM(A.NombreGenerico)) AS NombreGenerico,
			A.Categoria,
			CAST(A.CostoUltimo AS DECIMAL(18,5))  AS CostoUltimo,
			CAST(A.CostoPromedio AS DECIMAL(18,5)) AS CostoPromedio,
			CAST(A.CostoAlto AS DECIMAL(18,5)) AS  CostoAlto,
			A.Departamento AS Departamento,
			A.SubDepartamento AS SubDepartamento,
			A.CodigoHospIFar AS CodigoHospIFar,
			A.Clase AS Clase,
			A.SubClase AS SubClase,
			A.Trasladable AS Trasladable,
			RTRIM(RTRIM(A.Descripcion)) AS Descripcion,
			A.Proveedor AS Proveedor,
			A.Marca AS Marca,
			A.Warning AS Warning,
			A.TipodeUso,
			A.Presentacion AS Presentacion,
			A.CostoExterno AS CostoExterno,
			A.Minimo AS Minimo,
			A.Maximo AS Maximo,
			A.EsActivoFijo AS EsActivoFijo,
			A.ExcentoIva AS ExcentoIva,
			A.Familia AS Familia,
			A.Consignacion AS Consignacion,
			A.TipoBodega AS TipoBodega,
			A.Ensamblado AS Ensamblado,
			----------CLASIFICACION---------
			A.Grupo AS Grupo,
			A.SubGrupo,
			A.Generico,
			---------Info. Extra.----------
			A.CampoExtra1,
			A.CampoExtra2,
			A.CampoExtra3,
			----------Max. y Min.---------
			A.MinTiempo,
			A.MaxTiempo,
			----------ESTADISTICA---------
			A.VentaEsperada,
			A.UltimaVenta,
			A.UltimaFacturaVenta,
			A.UltimaCompra,
			A.UltimaFacturaCompra,
			A.FechaRegistro,
			A.Usuario,
			---------COMPRAS----------
			A.Costeo,
			A.Impuestos,
			CAST(ISNULL(A.MarkUp,0) AS DECIMAL(18,0))AS MarkUp,
			A.Lnsp,
			A.UnidadMedida,
			A.UnidadVenta,
			A.UnidadCompra,
			A.Tipo,
			----------EXTRA----------	
			A.Activo,
			A.Intangible,
			A.Seriado,
			A.NOESAutorizado,
			B.Descripcion AS PresentacionNombre,
			B.Unidad AS PresentacionUnidad,
			RTRIM(RTRIM(C.Nombre)) AS  CategoriaNombre
		FROM
			INVENTARIO.dbo.Productos AS A
			INNER JOIN  INVENTARIO.dbo.NTUnidadesMedida AS B
			ON B.IdMedida=A.Presentacion
			LEFT JOIN HOSPITAL.DBO.Categorias AS C
			ON A.Categoria=C.Codigo
			AND A.Empresa=C.Empresa
		WHERE
			A.Empresa = @i_empresa
			AND A.Activo = 'S'
			--AND Tipo='P'
			AND (@i_Nombre is null or  concat(rtrim(A.Nombre),' ',rtrim(A.NombreGenerico),', ',rtrim(A.Descripcion),', ',rtrim(A.Codigo)) like '%' + ltrim(rtrim(@i_Nombre)) +'%')
		ORDER BY 
			A.Codigo DESC --OFFSET ((10 *(1-1))) ROWS FETCH NEXT 12 ROWS ONLY;
		
	END
	
	 
	IF @i_accion = 'C'	--CLASE SELECT ACTIVOS
	BEGIN
		
		
		SELECT
			Codigo ,Nombre,Clasificacion
		FROM	INVENTARIO.dbo.ProductosClase
		WHERE	Empresa = @i_empresa
			AND Clasificacion = (CASE WHEN (@i_empresa='BOD'or @i_empresa='DRO') THEN 'P' ELSE 'S' END);
			
		
	END
	
	IF @i_accion = 'D'	--CATEGORIA ProductosSubClase - CLASE SELECT ACTIVOS
	BEGIN
		
		SELECT
			Codigo
			, Nombre
		FROM INVENTARIO.dbo.ProductosSubClase
		WHERE Empresa=@i_empresa AND Clase=@i_clase;
	
	END
	
	IF @i_accion = 'E'	-- PRESENTACION SELECT ACTIVOS
	BEGIN
		
		SELECT
			DISTINCT Unidad as Nombre,--USAR ESTE
			IdMedida,
			Descripcion
		FROM 	INVENTARIO.dbo.NTUnidadesMedida
		ORDER BY  Unidad;
	
	END
	
	IF @i_accion = 'F'	-- Categorias para Facturacion
	BEGIN
		
		SELECT
			A.Codigo,
			A.Nombre
		FROM	Hospital.DBO.Categorias AS A
		INNER JOIN INVENTARIO.dbo.DetalleTipoBodegaCat AS B
		ON A.Codigo=B.Categoria
		AND A.Empresa=B.Empresa
		WHERE	A.Empresa = @i_empresa
			AND B.TipoBodega=@i_TipoBodega
			AND B.Tipo='L'
		ORDER BY 
			A.codigo;
	
	END
	
	IF @i_accion = 'G'	-- UtilizadoOrigen
	BEGIN
		
		SELECT
			Codigo,Nombre
		FROM INVENTARIO.DBO.UtilizadoOrigen
		WHERE Estado=1
		ORDER BY  Orden ASC;
	
	END
	
	IF @i_accion = 'H'	-- Producto Opciones
	BEGIN
		
		SELECT
			LTRIM(RTRIM(Codigo)) AS Codigo,LTRIM(RTRIM(Nombre)) AS Nombre
		FROM INVENTARIO.DBO.ProductoOpciones
		WHERE Estado=1
		AND Tipo=@i_Tipo
		AND Empresa=@i_empresa
		ORDER BY  Orden ASC;
	END
	
	IF @i_accion = 'I'	--  Producto TipoBodega
	BEGIN
		
		SELECT
			Codigo,Nombre
		FROM INVENTARIO.DBO.ProductoTipoBodega
		WHERE Empresa=@i_empresa
		AND Tipo=@i_tipo
		AND Estado=1
		ORDER BY  ORDEN DESC;
			
	END
	
	IF @i_accion = 'J'	--   TIPO SEDE COCINA
	BEGIN
		
		SELECT
			C.Codigo,
			C.Nombre
		FROM Hospital.DBO.Categorias AS A
		INNER JOIN INVENTARIO.dbo.DetalleTipoBodegaCat AS B
		ON A.Codigo=B.Categoria
		AND A.Empresa=B.Empresa
		INNER JOIN INVENTARIO.dbo.TipoSedeCocina AS C
		ON C.Codigo=B.Tipo
		WHERE A.Empresa = @i_empresa
			AND B.TipoBodega=@i_TipoBodega
			AND B.Tipo NOT IN ('L') 
		ORDER BY 
			A.codigo;	
			
	END
	
	IF @i_accion = 'K'	-- Producto Opciones - Mantenimiento
	BEGIN
		
		SELECT
			LTRIM(RTRIM(Codigo)) AS  Codigo,LTRIM(RTRIM(Nombre)) AS Nombre
		FROM INVENTARIO.DBO.ProductoOpciones
		WHERE Estado=3
		AND Tipo=@i_Tipo
		AND Empresa=@i_empresa
		ORDER BY  Orden ASC;	
			
	END
	
	IF @i_accion = 'L'	--  LISTA DE PRECIOS
	BEGIN
		
		SELECT
			Nivel,
			SubNivel,
			UnidadMedida,
			Porcentaje,
			Factor,
			Precio
		FROM INVENTARIO.DBO.ProductosPrecios
		WHERE Producto = @i_CodigoProducto
			AND Empresa = @i_empresa;
			
	END
	
	
	IF @i_accion = 'M'	--  GENERACION DE COSTO SIN IVA PRODUCTO
	BEGIN
		
		--SELECT ROUND(((100*(@i_Costo/NULLIF(@w_Impuesto,0)))/NULLIF(100,0)),4)  AS costo;
		SELECT CONVERT(DECIMAL(18,5),ROUND(((100*(@i_Costo/NULLIF(@w_Impuesto,0)))/NULLIF(100,0)),5))  AS costo;
			
	END
	
	IF @i_accion = 'N'	-- REPORTE ESTADISTICA
	BEGIN
		
		SELECT
			TOP 10 o.Fecha,
			c.Sucursal, 
			c.TipoOrden,
			c.Orden,
			c.SerieAdmision,
			c.Admision,
			c.Cantidad,
			c.PrecioUnitario,
			o.Usuario,
			c.Ajeno
		FROM
			HOSPITAL.DBO.Cargos c (nolock)
		INNER JOIN HOSPITAL.DBO.Ordenes o (nolock) ON
			(o.Empresa = c.Empresa
			AND o.Tipo = c.TipoOrden
			AND o.Codigo = c.Orden)
		WHERE 	c.Empresa = @i_empresa
			AND c.Producto = @i_CodigoProducto
		ORDER BY 	o.Fecha Desc;
			
	END
	
	IF @i_accion = 'O'	--  EXISTENCIA
	BEGIN
		
		SELECT
			RTRIM(LTRIM(EB.NumeroSerie)) AS NumeroSerie ,
			EB.Bodega ,
			RTRIM(LTRIM(B.Nombre)) AS NombreBodega ,
			RTRIM(LTRIM(EB.Localizacion)) AS Localizacion,
			EB.Existencia ,
			InventariosFisicos ,
			EB.UltimoInventario
		FROM	INVENTARIO.dbo.ExistenciasBodega EB
		LEFT JOIN Inventario..Bodegas AS B 
		ON EB.Empresa = B.Empresa 
		AND EB.Bodega = B.Codigo
		WHERE	EB.Producto = @i_CodigoProducto
			AND EB.Empresa = @i_empresa;
			--SELECT TOP 1 * FROM INVENTARIO.dbo.ExistenciasBodega
	END
	
	IF @i_accion = 'P'	--  MaxMinSource
	BEGIN
		
		SELECT
			Bodega,
			Minimos,
			Maximos,
			Fecha
		FROM	INVENTARIO.DBO.MaxMinBodega
		WHERE	Producto = @i_CodigoProducto
			AND Empresa = @i_empresa;
			
	END
	
	IF @i_accion = 'Q'	-- GENERACION DE CODIGO PRODUCTO
	BEGIN
		
		SELECT	DBO.F_Codigo_Producto(@i_clase,@i_categoria,@i_empresa) AS Codigo;
			
	END
	
	IF @i_accion = 'R'	-- Bodegas
	BEGIN
		
		SELECT Codigo,  (ltrim(Rtrim(CONVERT(char,Codigo))) + '-'+ltrim(Rtrim(Nombre)) )as Nombre_Bodega
		FROM Inventario.DBO.Bodegas
		WHERE empresa = @i_empresa
		AND activa = 'S'
		AND codigo in(1,2,6,60,70,11,12,13,14,15,16,31,32,33,34,35,36,41,42,43,44,45,46,50,61,62,63,64,65,66,71,72,73,74,75,76,171,151,152,153,154,155,156,161,162,163,164,165,166)
		ORDER BY  codigoAgrupacion asc; 
			
	END
	
	IF @i_accion = 'S'	-- ProductoTipo
	BEGIN
		
		SELECT
			Empresa ,
			Codigo ,
			Nombre
		FROM	INVENTARIO.DBO.ProductoTipo
		WHERE	Estado = 1
			AND Empresa = @i_empresa
		ORDER BY Orden;
			
	END
	
	
	IF @i_accion = 'T'	--  Metodo Costeo 
	BEGIN
		
		SELECT
			Empresa ,
			Codigo ,
			Nombre
		FROM	INVENTARIO.DBO.MetodoCosteo
		WHERE	Estado = 1
			AND Empresa = @i_empresa
		ORDER BY Orden;
			
	END
	
	IF @i_accion = 'U'	--  CatCaracteristicas
	BEGIN
		
		 SELECT
			Codigo,
			Nombre
		FROM	INVENTARIO.DBO.CatCaracteristicas
		WHERE	Estado = 1
			AND Tipo = @i_Tipo
			AND Empresa = @i_empresa
		ORDER BY 	Orden;
			
	END
	
	IF @i_accion = 'V'	-- Listado de  Existencias 
	BEGIN
		
		 SELECT
				EB.Empresa,
				EB.Producto,
				EB.Bodega,
				EB.Existencia,
				EB.InventariosFisicos,
				EB.UltimoInventario,
				EB.ExistenciaFisico,
				EB.FechaInicial,
				B.Nombre,
				B.CodigoAgrupacion
			FROM	Inventario.DBO.ExistenciasBodega AS EB
			LEFT OUTER JOIN Inventario.DBO.Bodegas AS B ON
				EB.Empresa = B.Empresa
				AND EB.Bodega = B.Codigo
			WHERE	(EB.Empresa =@i_empresa)
				AND (EB.Producto =@i_CodigoProducto )
				AND B.Activa = 'S'
			ORDER BY 	EB.Bodega,
				B.CodigoAgrupacion;
			
	END
	
	 IF @i_accion = 'W'			--CAMBIO DE PRECIO - VALOR PORCENTAJE
	 BEGIN
		
		 IF(@i_factor<>1)
		 BEGIN
			 SELECT ISNULL((ROUND(100*(@i_factor * (@i_costo) *(1+ @i_porcentaje/NULLIF(100,0))),5))/NULLIF(100,0),0) AS Precio;
		 END
		 ELSE
		 BEGIN
		    SELECT ISNULL((ROUND(100*((@i_costo) *(1+ @i_porcentaje/NULLIF(100,0))),4))/NULLIF(100,0),0) AS Precio;
		 END

	 END
	 
	 IF @i_accion = 'X'			--CAMBIO DE PRECIO - VALOR FACTOR
	 BEGIN
		
		 IF(@i_factor<>1)
		 BEGIN
			 SELECT ISNULL((ROUND(100*(@i_factor * (@i_costo) *(1+ @i_porcentaje/NULLIF(100,0))),5))/NULLIF(100,0),0) AS Precio;
		 END
		 ELSE
		 BEGIN
		    SELECT ISNULL((ROUND(100*((@i_costo) *(1+ @i_porcentaje/NULLIF(100,0))),5))/NULLIF(100,0),0) AS Precio;
		 END

	 END

	 IF @i_accion = 'Y'			--  CAMBIO DE PRECIO - VALOR COSTO
	 BEGIN
		
		 IF(@i_factor<>1)
		 BEGIN

			 --SELECT ISNULL(100*(@i_costo - @i_factor * @i_costo)/NULLIF(@i_costo,0),0)   AS Porcentaje;
			  SELECT ISNULL(100*(@i_precio - @i_factor * @i_costo)/NULLIF(@i_precio,0),0)   AS Porcentaje;
			 --SELECT -2 AS Porcentaje;
		 END
		 ELSE   IF(@i_precio=0)
		 BEGIN
		     SELECT 0 AS Porcentaje;
		 END
		 ELSE
		 BEGIN
		     --SELECT ISNULL(100*(@i_costo - @i_costo)/NULLIF(@i_costo,0),0)  AS Porcentaje;
			   SELECT ISNULL(100*(@i_precio - @i_costo)/NULLIF(@i_precio,0),0)  AS Porcentaje;
		 END

	 END
	 
	 IF @i_accion = 'Z'			--  FAMILIAS SELECT ACTIVOS
	 BEGIN 
		 
		SELECT * 
			FROM INVENTARIO.DBO.Familia
		WHERE Empresa = @i_empresa;
	
	 END

    
END--FIN IF PRINCIPAL  
ELSE IF @i_operacion='B'  --MIGRACIÓN INVENTARIO / ORDEN DE COMPRA
BEGIN
	IF @i_accion = 'A'	--PRODUCTO SELECT ACTIVOS
	BEGIN
		 
		 SELECT TOP 20
			A.Empresa,
			RTRIM(RTRIM(A.Codigo)) AS Codigo,
			RTRIM(RTRIM(A.Nombre)) AS Nombre,
			RTRIM(RTRIM(A.NombreGenerico)) AS NombreGenerico,
			A.Categoria,
			CAST(A.CostoUltimo AS DECIMAL(18,5))  AS CostoUltimo,
			CAST(A.CostoPromedio AS DECIMAL(18,5)) AS CostoPromedio,
			CAST(A.CostoAlto AS DECIMAL(18,4)) AS  CostoAlto,
			A.Departamento AS Departamento,
			A.SubDepartamento AS SubDepartamento,
			A.CodigoHospIFar AS CodigoHospIFar,
			A.Clase AS Clase,
			A.SubClase AS SubClase,
			A.Trasladable AS Trasladable,
			RTRIM(RTRIM(A.Descripcion)) AS Descripcion,
			A.Proveedor AS Proveedor,
			A.Marca AS Marca,
			A.Warning AS Warning,
			A.TipodeUso,
			A.Presentacion AS Presentacion,
			A.CostoExterno AS CostoExterno,
			A.Minimo AS Minimo,
			A.Maximo AS Maximo,
			A.EsActivoFijo AS EsActivoFijo,
			A.ExcentoIva AS ExcentoIva,
			A.Familia AS Familia,
			A.Consignacion AS Consignacion,
			A.TipoBodega AS TipoBodega,
			A.Ensamblado AS Ensamblado,
			----------CLASIFICACION---------
			A.Grupo AS Grupo,
			A.SubGrupo,
			A.Generico,
			---------Info. Extra.----------
			RTRIM(RTRIM(A.CampoExtra1)) AS CampoExtra1,
			RTRIM(RTRIM(A.CampoExtra2)) AS  CampoExtra2,
			RTRIM(RTRIM(A.CampoExtra3)) AS CampoExtra3,
			----------Max. y Min.---------
			A.MinTiempo,
			A.MaxTiempo,
			----------ESTADISTICA---------
			RTRIM(RTRIM(A.VentaEsperada)) AS VentaEsperada,
			RTRIM(RTRIM(A.UltimaVenta)) AS UltimaVenta,
			RTRIM(RTRIM(A.UltimaFacturaVenta)) AS UltimaFacturaVenta,
			A.UltimaCompra AS UltimaCompra,
			RTRIM(RTRIM(A.UltimaFacturaCompra))  AS UltimaFacturaCompra,
			A.FechaRegistro AS FechaRegistro,
			RTRIM(RTRIM(A.Usuario)) AS Usuario,
			---------COMPRAS----------
			RTRIM(RTRIM(A.Costeo)) AS Costeo,
			RTRIM(RTRIM(A.Impuestos)) AS Impuestos,
			CAST(ISNULL(A.MarkUp,0) AS DECIMAL(18,0)) AS MarkUp,
			RTRIM(RTRIM(A.Lnsp)) AS Lnsp,
			RTRIM(RTRIM(A.UnidadMedida)) AS UnidadMedida,
			RTRIM(RTRIM(A.UnidadVenta)) AS UnidadVenta,
			RTRIM(RTRIM(A.UnidadCompra)) AS UnidadCompra,
			RTRIM(RTRIM(A.Tipo)) AS Tipo,
			----------EXTRA----------	
			A.Activo,
			A.Intangible,
			A.Seriado,
			A.NOESAutorizado,
			B.Descripcion AS PresentacionNombre,
			B.Unidad AS PresentacionUnidad,
			C.Nombre AS  CategoriaNombre,
			--MANTENIMIENTO PRODUCTO.
			RTRIM(RTRIM(PC.VentaHospital)) AS VentaHospital,
			RTRIM(RTRIM(PC.UnidadCompra)) AS UnidadCompra,
			RTRIM(RTRIM(PC.RegistroSanitario)) AS RegistroSanitario,
			RTRIM(RTRIM(PC.Contraindicaciones)) AS Contraindicaciones,
			RTRIM(RTRIM(PC.ATC)) AS ATC,
			PC.CadenaFrio,
			PC.LuzDirecta,
			PC.Controlado,
			PC.Stock,
			PC.BajoPedido,
			PC.Sasi,
			PC.CatalogoGeneral,
			PC.CompraLocal,
			PC.Importado,
			PC.Usuario,
			------CAMPO FAMILIA
			RTRIM(RTRIM(F.Nombre)) AS FamiliaNombre,
			'' AS NombreUsuario
		FROM
			INVENTARIO.dbo.Productos AS A
			INNER JOIN  INVENTARIO.dbo.NTUnidadesMedida AS B
			ON B.IdMedida=A.Presentacion
			LEFT JOIN HOSPITAL.DBO.Categorias AS C
			ON A.Categoria=C.Codigo
			AND A.Empresa=C.Empresa
			LEFT JOIN INVENTARIO.DBO.ProductoCaracteristicas AS PC
			ON PC.Codigo=A.Codigo
			AND A.Empresa=PC.Empresa
			LEFT JOIN INVENTARIO.DBO.Familia AS F
			ON F.Empresa=A.Empresa
			AND F.Codigo=A.Familia
			INNER JOIN HOSPITAL..inv_categoria_departamento CD 
			ON A.Departamento = CD.IdCategoriafk
			INNER JOIN DepartamentosInv D
			ON CD.IdDepartamentofk = D.CODIGO
			AND D.EMPRESA = @i_empresa
		WHERE	A.Empresa = @i_empresa
			AND A.TIPO = 'P'
			AND A.Activo = 'S'
			AND (@i_departamento IS NULL OR CONCAT(RTRIM(D.Codigo),' ') LIKE '%' + LTRIM(RTRIM(@i_departamento)) +'%')
			----09/08/2023 NO SE TOMARA EN CUENTA LA CLASE DE PRODUCTOS.
			--AND A.Clase = @i_Clase
			--AND A.SubClase = @i_SubClase
			AND (@i_Nombre is null or  concat(rtrim(A.Nombre),' ',rtrim(A.NombreGenerico),', ',rtrim(A.Descripcion),', ',rtrim(A.Codigo)) like '%' + ltrim(rtrim(@i_Nombre)) +'%')
		ORDER BY 	A.Codigo DESC --OFFSET ((10 *(1-1))) ROWS FETCH NEXT 12 ROWS ONLY;
	END
END
END;-- FIN SP.



-------GRANT

-->>    Permiso Ejecución Store Procedures   USRPROINVENTARIO              

USE [HOSPITAL]
GO
GRANT EXECUTE ON OBJECT::dbo.sp_inv_linea_tiempo TO USRPROINVENTARIO;
GRANT EXECUTE ON OBJECT::dbo.sp_inv_solicitud_enc TO USRPROINVENTARIO;
GRANT EXECUTE ON OBJECT::dbo.sp_inv_solicitud_det TO USRPROINVENTARIO;
GRANT EXECUTE ON OBJECT::dbo.sp_inv_solicitud_enc_lista TO USRPROINVENTARIO;
GRANT EXECUTE ON OBJECT::dbo.sp_inv_solicitud_det_lista TO USRPROINVENTARIO;
GRANT EXECUTE ON OBJECT::dbo.sp_inv_tipo_documento TO USRPROINVENTARIO;
GRANT EXECUTE ON OBJECT::dbo.sp_inv_Busqueda_Corporativos TO USRPROINVENTARIO;
GRANT EXECUTE ON OBJECT::dbo.sp_inv_autorizacion_documento_lista TO USRPROINVENTARIO;
GRANT EXECUTE ON OBJECT::dbo.sp_inv_autorizacion_documento TO USRPROINVENTARIO;
GRANT EXECUTE ON OBJECT::dbo.sp_inv_departamento_lista TO USRPROINVENTARIO;
GRANT EXECUTE ON OBJECT::dbo.sp_inv_orden_compra_manual TO USRPROINVENTARIO;
GRANT EXECUTE ON OBJECT::dbo.sp_inv_solicitud_autorizacion_finalizar TO USRPROINVENTARIO;
GRANT EXECUTE ON OBJECT::dbo.sp_inv_proveedor_lista TO USRPROINVENTARIO;
GRANT EXECUTE ON OBJECT::dbo.sp_Margen_Tolerancia_Orden_Compra TO USRPROINVENTARIO;
GRANT EXECUTE ON OBJECT::dbo.spHisOCServiciosHnos TO USRPROINVENTARIO;
GRANT EXECUTE ON OBJECT::dbo.sp_inv_orden_compra_detalle_manual TO USRPROINVENTARIO;
GRANT EXECUTE ON OBJECT::dbo.sp_inv_autorizacion_orden_compra_lista TO USRPROINVENTARIO;
GRANT EXECUTE ON OBJECT::dbo.SpInvOrdenCompra TO USRPROINVENTARIO;
GRANT EXECUTE ON OBJECT::dbo.SP_InvValidacionOrdenCompraS TO USRPROINVENTARIO;
GRANT EXECUTE ON OBJECT::dbo.SP_InvValidaAnulaOrdenCompraLista TO USRPROINVENTARIO;


-->>   Permiso por objetos para USRPROINVENTARIO                

USE [HOSPITAL]
GO
GRANT SELECT ON ADMISIONES TO USRPROINVENTARIO
GRANT SELECT ON AJENOS TO USRPROINVENTARIO
GRANT SELECT ON CARGOS TO USRPROINVENTARIO
GRANT SELECT ON FACTURAS TO USRPROINVENTARIO
GRANT SELECT, INSERT, UPDATE ON INV_AUTORIZACION_DOCUMENTO TO USRPROINVENTARIO
GRANT SELECT, INSERT, UPDATE ON INV_AUTORIZACION_DOCUMENTO_HISTORIAL TO USRPROINVENTARIO
GRANT SELECT, INSERT, UPDATE ON INV_COTIZACION_DET TO USRPROINVENTARIO
GRANT SELECT, INSERT, UPDATE ON INV_COTIZACION_ENC TO USRPROINVENTARIO
GRANT SELECT, INSERT ON INV_ORDEN_COMPRA_DET TO USRPROINVENTARIO
GRANT SELECT, INSERT, UPDATE ON INV_ORDEN_COMPRA_ENC TO USRPROINVENTARIO
GRANT SELECT, INSERT, UPDATE ON INV_SOLICITUD_COMPRA_ENC TO USRPROINVENTARIO
GRANT SELECT ON INV_TIPO_DOCUMENTO TO USRPROINVENTARIO
GRANT SELECT ON ORDENES TO USRPROINVENTARIO
GRANT SELECT ON PACIENTES TO USRPROINVENTARIO
GRANT INSERT, UPDATE, DELETE ON INV_CATEGORIA_DEPARTAMENTO TO USRPROINVENTARIO
GRANT SELECT ON CATEGORIAS TO USRPROINVENTARIO
GRANT SELECT, INSERT, UPDATE  ON INV_SOLICITUD_COMPRA_DET TO USRPROINVENTARIO
GRANT SELECT ON INV_TIPO_PROVEEDOR TO USRPROINVENTARIO
GRANT SELECT ON INV_TIPO_RETENCION TO USRPROINVENTARIO



-->> Permiso Ejecución Store Procedure sp_Inventario_Consulta_Producto para usuario USRPROINVENTARIO                  

USE [INVENTARIO]
GO
GRANT EXECUTE ON OBJECT::dbo.sp_Inventario_Consulta_Producto TO USRPROINVENTARIO;     



-->> Permiso usuario USRPROINVENTARIO


USE [INVENTARIO]
GO

GRANT SELECT, INSERT ON ORDENCOMPRAHONOSMEDICOS TO USRPROINVENTARIO
GRANT SELECT ON CATCARACTERISTICAS TO USRPROINVENTARIO
GRANT SELECT ON DEPARTAMENTOSINV TO USRPROINVENTARIO
GRANT SELECT ON DETALLETIPOBODEGACAT TO USRPROINVENTARIO
GRANT SELECT ON MAXMINBODEGA TO USRPROINVENTARIO
GRANT SELECT ON METODOCOSTEO TO USRPROINVENTARIO
GRANT SELECT ON PRODUCTOCARACTERISTICAS TO USRPROINVENTARIO
GRANT SELECT ON PRODUCTOOPCIONES TO USRPROINVENTARIO
GRANT SELECT ON PRODUCTOSCLASE TO USRPROINVENTARIO
GRANT SELECT ON PRODUCTOSPRECIOS TO USRPROINVENTARIO
GRANT SELECT ON PRODUCTOSSUBCLASE TO USRPROINVENTARIO
GRANT SELECT ON PRODUCTOTIPO TO USRPROINVENTARIO
GRANT SELECT ON PRODUCTOTIPOBODEGA TO USRPROINVENTARIO
GRANT SELECT ON UTILIZADOORIGEN TO USRPROINVENTARIO
GRANT SELECT ON DESCRIPCION_OTROS_PAGOS TO USRPROINVENTARIO
GRANT SELECT ON TIPOSEDECOCINA TO USRPROINVENTARIO
GRANT SELECT ON EXISTENCIASBODEGA TO USRPROINVENTARIO
GRANT SELECT ON BODEGAS TO USRPROINVENTARIO
GRANT SELECT ON FAMILIA TO USRPROINVENTARIO
GRANT SELECT ON	COMPRAS	TO USRPROINVENTARIO
GRANT SELECT ON MOVIMIENTOS TO USRPROINVENTARIO
GRANT SELECT ON NTUNIDADESMEDIDA TO USRPROINVENTARIO
GRANT SELECT ON PRODUCTOS TO USRPROINVENTARIO
GRANT SELECT ON PROVEEDORES TO USRPROINVENTARIO


-->> Permiso usuario USRPROINVENTARIO 
USE [CONTADB]
GO
GRANT SELECT  ON Corporativos TO USRPROINVENTARIO
GRANT SELECT  ON Empresas TO USRPROINVENTARIO
GRANT SELECT  ON EmpresasDefaults TO USRPROINVENTARIO
GRANT SELECT, UPDATE ON Correlativos TO USRPROINVENTARIO



-->> Permiso Ejecución Store Procedures   USRREPORTEORDENCOMPRA  
-->> Permisos usurio HOSPITAL
USE [HOSPITAL]
GO

GRANT EXECUTE ON OBJECT::dbo.SpInvReportesOC TO USRREPORTEORDENCOMPRA;
GRANT EXECUTE ON OBJECT::dbo.SpInvOrdenCompra TO USRREPORTEORDENCOMPRA;
                
GRANT SELECT ON INV_AUTORIZACION_DOCUMENTO_HISTORIAL TO USRREPORTEORDENCOMPRA
GRANT SELECT ON INV_ORDEN_COMPRA_ENC TO USRREPORTEORDENCOMPRA
GRANT SELECT ON INV_ORDEN_COMPRA_DET TO USRREPORTEORDENCOMPRA

-->> Permisos usurio INVENTARIO
-->> Permiso usuario  USRREPORTEORDENCOMPRA
USE [INVENTARIO]
GO
GRANT SELECT ON DEPARTAMENTOSINV TO USRREPORTEORDENCOMPRA
GRANT SELECT ON BODEGAS TO USRREPORTEORDENCOMPRA
GRANT SELECT ON MOVIMIENTOS TO USRREPORTEORDENCOMPRA
GRANT SELECT ON COMPRAS TO USRREPORTEORDENCOMPRA
GRANT SELECT ON PROVEEDORES TO USRREPORTEORDENCOMPRARA
GRANT SELECT ON	PRODUCTOS	TO USRREPORTEORDENCOMPRA

-->> Permisos usurio CONTADB
USE [CONTADB]
GO

GRANT SELECT  ON Corporativos TO USRREPORTEORDENCOMPRA
GRANT SELECT  ON Empresas TO USRREPORTEORDENCOMPRA
GRANT SELECT  ON EmpresasDefaults TO USRREPORTEORDENCOMPRA





	









