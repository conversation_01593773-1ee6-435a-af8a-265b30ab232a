<template>
<div class="afiliados-solicitudes-container">
    <DxToolbar id="toolbar">
        <DxToolBarItem widget="dxButton" location="before" :options=" {
            icon: openState? 'hidepanel':'showpanel',
            stylingMode: 'text',
            hint: 'Mostrar listado de solicitudes',
            onClick: () => {
                openState = !openState
            },
            text: openState? 'Ocultar listado':'Ver solicitudes',
        }" />
        <DxToolBarItem widget="dxButton" location="before" :visible="openState" :options=" {
            icon: 'refresh',
            type: 'success',
            stylingMode: 'text',
            hint: 'Recargar el listado de solicitudes',
            onClick: () => {
                Cargar()
            },
            text: 'Actualizar Listado',
        }" />
        <DxToolBarItem widget="dxButton" location="before" :visible="Boolean(Solicitud?.IdSolicitud) && IdStatus == 'A' " :options=" {
            icon: 'repeat',
            type: 'warning',
            stylingMode: 'text',
            hint: 'Reactivar solicitud para pago',
            onClick: () => {
                ReactivarSolicitud()
            },
        }" />
        <DxToolBarItem widget="dxButton" location="before" :visible="Modo != 'NUEVO' && IdStatus == 'A'" :options=" {
            icon: 'add',
            type: 'default',
            stylingMode: 'text',
            hint: 'Nueva solicitud',
            onClick: () => {
                Modo = 'NUEVO'
                dataGridSolicitudes.clearSelection()
            },
        }" />
        <DxToolBarItem widget="dxButton" location="before" :visible="Boolean(Solicitud?.IdSolicitud) && IdStatus == 'A' && Modo != 'EDITAR' && Solicitud.StatusSolicitud == 'I'" :options=" {
            icon: 'edit',
            type: 'normal',
            stylingMode: 'text',
            hint: 'Genera nueva solictud en base a los datos registrados',
            onClick: () => {
                Modo = 'EDITAR'
            },
        }" />
        <DxToolBarItem widget="dxButton" location="before" :visible="Modo == 'EDITAR'" :options=" {
            icon: 'remove',
            type: 'danger',
            stylingMode: 'text',
            hint: 'Cancelar la edición',
            onClick: () => {
                Modo = 'LECTURA'
            },
        }" />
    </DxToolbar>

    <DxDrawer opened-state-mode="shirk" position="left" :opened.sync="openState" v-bind="drawerOptions" :close-on-outside-click="true" template="listMenu">
        <template #listMenu>

            <div style="width: 245px; padding-right: 5px">

                <DxDataGrid :ref="refGridSolicitudes" :data-source="ListaSolicitudes" v-bind="GridOption" @selection-changed="onSelectionChanged" :width="drawerWidth" height="calc(100vh - 200px)">

                    <DxDataGridColumn dataField="IdSolicitud" caption="Solicitud" width="80" />
                    <DxDataGridColumn dataField="FechaIngreso" caption="Fecha" data-type="date" format="dd/MM/yyyy" sort-order="desc" width="80" />
                    <DxDataGridColumn dataField="StatusSolicitud" caption="Estado" width="80" />

                </DxDataGrid>
            </div>

        </template>
        <div id="content" class = "mr-2">
            <DxTabPanel class="prod-tab-panel" :width="tabPanelWidth" height="calc(100vh - 200px)" :animation-enabled="true" :swipe-enabled="true" tabs-position="left" styling-mode="primary" icon-position="top">
                <DxTabPanelItem title="Ingreso de Exámenes de Diagnóstico" icon="fas fa-vials" template="registro" />
                <DxTabPanelItem title="Información del Plan" icon="info" template="beneficios" />

                <template #registro>
                    <DxScrollView :scroll-by-conent="true" width="100%" height="100%">
                        <div class="ml-2 mr-2">
                            <SolicitudEstudioRegistro v-bind="$props" :Modo="Modo" :IdSolicitud="Solicitud.IdSolicitud" @saved-data="Cargar" @busqueda-medico="(e)=>$emit('busqueda-medico',e)"/>
                        </div>
                    </DxScrollView>
                </template>

                <template #beneficios>
                    <div>
                        <InfoPlanEncabezado :Afiliado="Afiliado" :DatosPlan="Plan" />
                        <AfiliadoInfoPlan v-bind="$props" />
                    </div>

                </template>

            </DxTabPanel>
        </div>

    </DxDrawer>
   
</div>
</template>

<script>

import {
    DefaulGridOptions,
    _confMoneda
} from './data.js'

const refGridSolicitudes = 'gridsolicitudess'
/**
 * Muestra el listado de solictudes de estudios 
 * Intergra componente para el registro
 */
export default {
    name: 'AfiliadoSolicitudEstudios',
    components: {
        AfiliadoInfoPlan: () => import("./AfiliadoInformacionPlan.vue"),
        SolicitudEstudioRegistro: () => import("./AfiliadoSolicitudEstudiosRegistro.vue"),
        InfoPlanEncabezado: () => import("./AfiliadoInformacionPlanEncabezado.vue"),
    },
    data() {
        return {
            openState: false,
            Solicitud: {
                IdSolicitud: null
            },

            Modo: "NUEVO",

            refGridSolicitudes,
            ListaSolicitudes: null,
            GridOption: {
                ...DefaulGridOptions,
                searchPanel: null,
                groupPanel: null,
                headerFilter: {
                    visible: false,
                },

            },
            drawerWidth: 245,
            monedaOptions:_confMoneda,

        }
    },
    props: {
        IdCliente: String,
        IdAfiliado: String,
        IdStatus: String,
        IdPlan: String,
        NivelPrecios: Number,
        IdAseguradora: String,
        drawerOptions: Object,
        Afiliado: Object, //datos del afiliado seleccionado
        Plan: Object, //datos del plan seleccionado
        TelefonoCelular: String
    },
    methods: {

        Cargar() {
            if (this.IdAfiliado)
                this.axios.post("/app/v1_afiliados/SolucitudesEncabezado", {
                    IdAfiliado: this.IdAfiliado,
                    IdCliente: this.IdCliente,
                })
                .then((resp) => {
                    this.ListaSolicitudes = resp.data
                    this.Modo = 'LECTURA'
                    this.dataGridSolicitudes.clearSelection()
                })
        },

        onSelectionChanged(e) {
            if (e.selectedRowsData.length) {
                this.Solicitud = {...e.selectedRowsData[0]}
                this.Modo = 'LECTURA'
            } else{
                this.limpiarSolicitud()
                this.openState = false
            }
        },

        limpiarSolicitud() {
            Object.keys(this.Solicitud).forEach(key => this.Solicitud[key] = null)
            this.Modo = 'NUEVO'
        },

        ReactivarSolicitud() {
            this.axios.post('/app/v1_afiliados/ReactivarSolucitud', {
                Id: this.Solicitud.IdSolicitud
            }).then( () => {
                this.Modo = 'NUEVO'
                this.$nextTick( ()=> {
                    this.Modo = 'LECTURA'
                })
            })
        },
    },
    mounted() {
        this.Cargar()
    },

    watch: {
        'IdAfiliado'() {
            this.limpiarSolicitud()
            this.Cargar()
        },
    },
    computed: {
        dataGridSolicitudes() {
            return this.$refs[refGridSolicitudes].instance
        },
        tabPanelWidth(){
            return this.openState? 'calc(100% - 245px)': '100%'
        },
    },

}
</script>

<style>
.afiliados-registro-solicitudes-container td:has(.smile-button) {
    padding-top: 0;
    padding-bottom: 0;
}

.smile-button {
    border: none;
}

.smile-button {
    background-color: transparent !important;
}

.prod-tab-panel {
    max-height: calc(100vh - 200px)
}
/**En ocaciones el css del ancho del drawer no coloca el ancho por ello se sobreescribe el estilo*/
.afiliados-solicitudes-container .dx-drawer-opened .dx-drawer-content {
    padding: 0px; 
    transform: translate(245px, 0px) !important; 
    transition: all 400ms  !important; 
    left: 0px;
}
</style>
