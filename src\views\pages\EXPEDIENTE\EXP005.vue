<template>
<DxDataGrid v-bind="DefaultDxGridConfiguration" :ref="dataGridRefKey" :data-source="evolucionesDataSource" :visible="ExpedienteCargado" @edit-canceled="()=> {VistaPrevia=false}">
    <DxToolbar>
        <DxItem name="groupPanel" />
        <DxItem name="searchPanel" />
        <DxItem location="after" template="opcionesTemplate" />
    </DxToolbar>

    <DxGroupPanel :visible="true" />
    <DxGrouping :auto-expand-all="false" />

    <DxEditing :allow-updating="false" :allow-adding="true" :allow-deleting="false" mode="popup" validation-group="validaEvolucion">
        <DxPopup v-bind="popupConf" :title="VistaPrevia?'Visualización Evolución':'Agregar Evolución'" :toolbarItems="VistaPrevia?[]:popupOptions.toolbarItems" :height="700"/>
        <DxForm label-location="top">
            <DxItem data-field="notaevolucion" :col-span="2" :is-required="true" />
        </DxForm>
    </DxEditing>
    <DxColumn :width="45" type="buttons" :buttons="[
                {
                    icon:'fas fa-eye',
                    hint:'Ver',
                    onClick: (obj)=>{
                        this.VistaPrevia = true
                        this.dataGrid.editRow(obj.row.rowIndex)
                        this.Evolucion = obj.row.data.notaevolucion
                    }
                }
            ]" :allow-reordering="false" />
    <DxColumn :width="150" data-field="fecha" format="dd/MM/yyyy HH:mm:ss" data-type="date" sort-order="desc" :allow-editing="!VistaPrevia" />
    <DxColumn :width="700" :minWidth="100" data-field="notaevolucion" caption="Evolución" :allow-editing="!VistaPrevia" edit-cell-template="myTextArea" :validationRules="[{message:'Debe ingresar una nota de evolución', type:'required'}]" :customizeText="(e) => this.$saltos_tab_to_html(e.value)" :encode-html="false" />
    <DxColumn :width="150" :minWidth="100" data-field="Nombre" caption="Responsable" :allow-editing="!VistaPrevia" />
    <DxColumn :width="150" :minWidth="100" data-field="Puesto" caption="Puesto" :allow-editing="!VistaPrevia" />
    <DxColumn :width="150" :minWidth="100" data-field="Grupo" :allow-editing="!VistaPrevia" />

    <template #opcionesTemplate>
        <ExpedienteGridToolBar v-bind="$props" :visible="ExpedienteCargado" :pdfExportItems="[{text:'Evoluciones', reportName: 'Evolución'}]" @add="addRow" @refresh="Cargar" :report-param="$props" />
    </template>
    <template #myTextArea="{ data: cellInfo }">
        <div>
            <DxTextArea :value="Evolucion" :on-value-changed="(e)=> {
                                cellInfo.setValue(e.value,e.value)
                                Evolucion=e.value
                            }" :read-only="VistaPrevia" :height="460" @key-up="(e)=> {
                                if(e.event.key ==='Escape')
                                    dataGrid.cancelEditData()
                            }" />
        </div>
    </template>
</DxDataGrid>
</template>

<script>
import ExpedienteBase from './EXPBase.vue'
import {
    DxDataGrid,
    DxColumn,
    DxEditing,
    DxForm,
    DxPopup,
    DxToolbar,
    DxGrouping,
    DxGroupPanel
} from 'devextreme-vue/data-grid'
import CustomStore from 'devextreme/data/custom_store'
import ExpedienteGridToolBar from './ExpedienteGridToolBar.vue'
import {
    DxItem
} from 'devextreme-vue/form'
import {
    DefaultDxGridConfiguration,
    ObtenerPlantilla
} from './data'
import {
    DxTextArea
} from 'devextreme-vue/text-area'
const dataGridRefKey = 'gridEvoluciones'
export default {
    name: 'Evoluciones',
    extends: ExpedienteBase,
    components: {
        DxDataGrid,
        DxColumn,
        ExpedienteGridToolBar,
        DxEditing,
        DxForm,
        DxItem,
        DxToolbar,
        DxPopup,
        DxTextArea,
        DxGrouping,
        DxGroupPanel
    },
    data() {
        return {
            DefaultDxGridConfiguration,
            Evolucion: '',
            VistaPrevia: false,
            evoluciones: [],
            evolucionesDataSource: new CustomStore({
                key: "id",
                load: () => {
                    return this.evoluciones
                },
                insert: (values) => {
                    this.Grabar(values)
                },
            }),
            dataGridRefKey,
            popupOptions: {
                toolbarItems: [{
                        widget: "dxButton",
                        toolbar: "bottom",
                        options: {
                            text: 'Guardar',
                            type: 'success',
                            useSubmitBehavior: true,
                            onClick: () => {
                                this.dataGrid.saveEditData()
                            }
                        },
                        location: "after",
                    },
                    {
                        widget: "dxButton",
                        toolbar: "bottom",
                        options: {
                            text: 'Cancelar',
                            type: 'danger',
                            useSubmitBehavior: true,
                            onClick: () => {
                                this.dataGrid.cancelEditData()
                            }
                        },
                        location: "after",
                    },
                    {
                        widget: "dxButton",
                        toolbar: "top",
                        options: {
                            widget: 'dxButton',
                            hint: 'Carga la plantilla definida para el hospital actual en el editor',
                            text: 'Cargar Examen Físico',
                            icon: 'import',
                            stylingMode: 'text',
                            showText: 'inMenu',
                            onClick: () => {
                                this.CargarPlantilla()
                            },
                        },
                        location: "after",
                    },
                    {
                        widget: "dxButton",
                        toolbar: "top",
                        options: {
                            widget: 'dxButton',
                            hint: 'Borra todo el texto del editor',
                            text: 'Borrar texto',
                            icon: 'fas fa-broom',
                            stylingMode: 'text',
                            showText: 'always',
                            onClick: () => {
                                this.Evolucion = ''
                            },
                        },
                        location: "after",
                    }
                ]
            },
            decargaPlantillaOpt: {
                widget: 'dxButton',
                hint: 'Carga la plantilla definida para el hospital actual en el editor',
                text: 'Cargar Examen Físico',
                icon: 'import',
                stylingMode: 'text',
                showText: 'inMenu',
                onClick: () => {
                    this.CargarPlantilla()
                },
            },
            definirFormatoOpt: {
                widget: 'dxButton',
                hint: 'Define localmente el formato de examen físisco para',
                text: 'Definir Formato Examen Físico',
                icon: 'upload',
                stylingMode: 'text',
                showText: 'inMenu',
                onClick: () => {
                    localStorage.examenFisico = this.Evolucion
                },
            },
            descargarFormatoOpt: {
                widget: 'dxButton',
                hint: 'Colocar en el editor formato de texto establecido para el examen físico',
                text: 'Cargar Examen Físico',
                icon: 'download',
                stylingMode: 'text',
                showText: 'inMenu',
                onClick: () => {
                    this.Evolucion = localStorage.examenFisico
                },
            },
            limpiarTextoOpt: {
                widget: 'dxButton',
                hint: 'Borra todo el texto del editor',
                text: 'Borrar texto',
                icon: 'fas fa-broom',
                stylingMode: 'text',
                showText: 'always',
                onClick: () => {
                    this.Evolucion = ''
                },
            },
        }
    },
    methods: {
        Cargar() {
            if (this.ExpedienteCargado)
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaEvolucionPDF', {
                    Serie: this.SerieAdmision,
                    Admision: this.CodigoAdmision
                })
                .then(resp => {
                    this.evoluciones = resp.data.json.map((x, index) => {
                        x.id = index
                        x.notaevolucion = this.$limpiar_saltos_tabulares(x.notaevolucion)
                        return x
                    })

                    this.evolucionesDataSource.load().then(
                        () => {
                            this.refreshDataGrid()
                        },
                        () => {
                           // console.error('error loading data customstore' + JSON.stringify(error))
                        }
                    )
                })
            else {
                this.evoluciones = []
                this.refreshDataGrid()
            }
        },
        Grabar(values) {
            if (this.SerieAdmision && this.CodigoAdmision)
                this.axios.post('/app/v1_ExpedienteEvolucion/InsertarExpedienteEvolucion', {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    DescripcionEvolucion: this.$reemplazar_tabulares(values.notaevolucion)
                }).then(() => {
                    this.Cargar()
                    this.$emit('onSaved', )
                })
        },
        CargarPlantilla() {
            ObtenerPlantilla("1", str => this.Evolucion = str)
        },
        refreshDataGrid() {
            this.dataGrid.refresh()
            this.dataGrid.clearGrouping()
        },
        addRow() {
            if (this.StatusAdmision != 'A')
                this.$vs.notify({
                    time: 4000,
                    title: 'Evolución',
                    text: 'La admisión no está activa, no se puede agregar nueva evolución',
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'warning',
                    position: 'bottom-center'
                })
            else {
                this.dataGrid.addRow()
                this.Evolucion = ''
            }
        },
    },
    watch: {
        'NumeroExpediente'(newval) {
            if (newval != undefined)
                this.Cargar()
        },
    },
    computed: {
        dataGrid: function () {
            return this.$refs[dataGridRefKey].instance
        },
    },

}
</script>
