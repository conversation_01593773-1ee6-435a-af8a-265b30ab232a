

export class AdmisionDirector {
  constructor(builder) {
    this.builder = builder;
  }

  construirAdmisionInterna(refs) {
    return this.builder
      // .setHabitacion(new Habitacion())
      .setDiagnosticoCovid(refs.DiagnosticoCovid)
      // .setDiagnosticoMedico(new DiagnosticoMedico())
      // .setInformacionPaciente(new InformacionPaciente())
      .setTiposDescuento(refs.TiposDescuento)
      .setSerieCodigoAdmision(refs.SerieCodigoAdmision)
      // .setDatosMedico(new DatosMedico())
      .build();
  }

  construirAdmisionExterna(refs) {
    return this.builder
      .setDatosPaciente(refs.DatosPaciente )
      .setSerieCodigoAdmision(refs.SerieCodigoAdmision)
      .setConsultarMedicos(refs.ConsultarMedicos)
      .setDiagnosticoCovid(refs.DiagnosticoCovid)
      .setTiposDescuento(refs.TiposDescuento)
      .build();
  }
}
