<template>
<div class="afiliados-bitacora-observaciones-container">
    <DxTabPanel width="100%" height="100%" :animation-enabled="true" :swipe-enabled="true" tabs-position="left" styling-mode="primary" icon-position="top">
        <DxTabPanelItem title="Observaciones" icon="comment" template="observaciones" />
        <DxTabPanelItem title="Cambio de Nombre" icon="edit" template="cambionombre" />

        <template #observaciones>
            <DxDataGrid class="p-2" ref="GridBitacoraObs" :data-source="BitacoraObs" v-bind="DataGridBitacoraOptions" :editing=" {
                    useIcons: true,
                    allowAdding: true,
                    allowUpdating: false,
                }" @row-inserting="Grabar" @row-inserted="CargarObs" @row-updating="Actualizar" @row-updated="CargarObs">
                <DxDataGridToolbar>
                    <DxDataGridItem name="groupPanel" />
                    <DxDataGridItem name="searchPanel" />
                    <DxDataGridItem location="after" template="opcionesTemplate" />
                </DxDataGridToolbar>
                <DxDataGridColumn data-field="Observacion" caption="Observación" :validation-rules="[{ type: 'required', message: 'Ingrese una observación' }]" />
                <DxDataGridColumn data-field="Fecha" :allow-editing="false" v-bind="DateColumnOptions" />
                <DxDataGridColumn data-field="Usuario" :allow-editing="false" />
                <DxDataGridColumn type="buttons" :buttons="['edit']" :allow-reordering="false" />

                <template #opcionesTemplate>
                    <ExpedienteGridToolBar :visible="true" :showItems="['add','refresh']" :pdfExportItems="[]" @add="()=>dataGrid.addRow()" @refresh="CargarObs" />
                </template>
            </DxDataGrid>
        </template>

        <template #cambionombre>
            <DxDataGrid class="p-2" :data-source="BitacoraCambioNombre" v-bind="DataGridBitacoraOptions">
                <DxDataGridToolbar>
                    <DxDataGridItem name="groupPanel" />
                    <DxDataGridItem name="searchPanel" />
                    <DxDataGridItem location="after" template="opcionesTemplate" />
                </DxDataGridToolbar>
                <DxDataGridColumn data-field="Nombre1" caption="Primer Nombre" />
                <DxDataGridColumn data-field="Nombre2" caption="Segundo Nombre" />
                <DxDataGridColumn data-field="Apellido1" caption="Primer Apellido" />
                <DxDataGridColumn data-field="Apellido2" caption="Segundo Apellido" />
                <DxDataGridColumn data-field="ApellidoCasa" />
                <DxDataGridColumn data-field="Observacion" caption="Observación" />
                <DxDataGridColumn data-field="Usuario" />
                <DxDataGridColumn data-field="Fecha" v-bind="DateColumnOptions" />
                <template #opcionesTemplate>
                    <ExpedienteGridToolBar :visible="true" :showItems="['refresh']" :pdfExportItems="[]" @refresh="CargarCambioNombre" />
                </template>
            </DxDataGrid>
        </template>
    </DxTabPanel>
</div>
</template>

<script>
import {
    DefaulGridOptions,
    _dateColumnConf,
} from './data.js'

export default {
    name: 'AfiliadosBitacoraObs',
    components: {
        ExpedienteGridToolBar: () => import('../EXPEDIENTE/ExpedienteGridToolBar.vue')
    },
    data() {
        return {
            BitacoraObs: null,
            BitacoraCambioNombre: null,
            DataGridBitacoraOptions: {
                ...DefaulGridOptions,
                height: '500',
            },
            DateColumnOptions: _dateColumnConf,
        }
    },
    props: {
        IdAfiliado: String,
        IdPlan: String,
    },
    methods: {

        Cargar() {
            this.CargarObs()
            this.CargarCambioNombre()
        },
        CargarObs() {
            this.axios.post("/app/v1_afiliados/BusquedaBitacoraObservaciones", {
                    IdAfiliado: this.IdAfiliado
                })
                .then((resp) => {
                    this.BitacoraObs = resp.data.json
                })
        },
        CargarCambioNombre() {
            this.axios.post("/app/v1_afiliados/BusquedaBitacoraCambioNombre", {
                    IdAfiliado: this.IdAfiliado
                })
                .then((resp) => {
                    this.BitacoraCambioNombre = resp.data.json
                })
        },
        Grabar(e) {
            e.cancel = new Promise((resolve, reject) => {
                this.axios.post("/app/v1_afiliados/RegistroBitacoraObs", {
                    IdAfiliado: this.IdAfiliado,
                    IdPlan: this.IdPlan,
                    Observacion: e.data.Observacion
                }).then((resp) => {
                    resp.data.codigo == 0 ? resolve(false) : resolve(true)
                }).catch((err) => {
                    reject(err.descripcion ? err.descripcion : err)
                })
            })
        },
        Actualizar(e) {
            e.cancel = new Promise((resolve, reject) => {
                this.axios.post("/app/v1_afiliados/ActualizarBitacoraObs", {
                    IdAfiliado: e.oldData.IdAfiliado,
                    IdPlan: e.oldData.IdPlan,
                    Correlativo: e.oldData.Correlativo,
                    Observacion: e.newData.Observacion,
                }).then((resp) => {
                    resp.data.codigo == 0 ? resolve(false) : resolve(true)
                }).catch((err) => {
                    reject(err.descripcion ? err.descripcion : err)
                })
            })
        },
    },
    mounted() {
        this.Cargar()
    },
    watch: {
        'IdAfiliado'() {
            this.Cargar()
        },
    },
    computed: {
        MostrarRecibos() {
            return this.NumeroAdhesion && this.NumeroAdhesion.substring(0, 4).toUpperCase() == "SSGH" || this.NumeroAdhesion.substring(0, 4).toUpperCase() == "SIH"
        },
        dataGrid() {
            return this.$refs["GridBitacoraObs"].instance
        },
    },

}
</script>

<style>

</style>
