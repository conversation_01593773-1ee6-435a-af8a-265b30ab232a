<template>
<div>
    <vs-prompt title="Número de autorización" color="primary" cancel-text="Cancelar" accept-text="Grabar" button-cancel="border" :is-valid="EditAutorizacion" @cancel="Otros().AutorizadoCancelar" @accept="Guardar().AutorizarHonorario()" :active.sync="modalAutorizacion">
        <div class="con-exemple-prompt">
            Ingrese el nuevo valor para los cargos
            <vs-input class="w-full" v-model="EditAutorizacion" type="text" min="0" max="200" :danger="!EditAutorizacion" danger-text="Ingrese un valor mayor que 0, y menor o igual a 200" />
        </div>
    </vs-prompt>

    <vx-card title="Validación de Admisiones">
        <ValidationObserver ref="formValidate" v-slot="{ invalid,handleSubmit }" mode="lazy">
            <div class="flex flex-wrap mb-5 " style="justify-content:space-between;border:1px solid rgba(0,0,0,0.1);padding:5px">
                <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                    <SM-Buscar v-model="info.Codigo" label="Código del Médico" api="app/Ajenos/Busqueda_Ajenos" :api_campos="['Codigo','Nombre','Apellido','Corporativo','Tipo']" :api_titulos="['Codigo','Nombre','Apellido','Corporativo','Tipo']" api_campo_respuesta="Codigo" :callback_buscar="Consulta().Consulta_Ajenos" :callback_cancelar="Otros().Limpiar_Datos" :disabled_texto="true" />
                </div>
                <div class="w-full md:w-2/3 lg:w-2/3 xl:w-2/3 p-1">
                    <vs-input label="Nombre Médico" class="w-full" v-model="info.Medico" :disabled="true" />
                </div>
                <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                    <vs-input label="NIT" class="w-full" v-model="info.NIT" :disabled="true" />
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <SM-Buscar label="Tipo de Médico" v-model="info.Tipo" :api="[{Codigo:'C',Nombre:'Cortesía'},{Codigo:'M',Nombre:'Casa'},{Codigo:'CC',Nombre:'Casa-Cortesía'},{Codigo:'R',Nombre:'Referencia'}]" :api_campos="['Nombre']" :api_titulos="['Nombre']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="false" />
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <SM-Buscar v-model="info.Especialidad" label="Especialidad" api="app/Ajenos/Busqueda_Especialidad" :api_cache="true" :api_campos="['Codigo','Descripcion']" :api_titulos="['Codigo','Descripción']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Descripcion" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="false " />
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <SM-Buscar v-model="info.SubEspecialidad" label="SubEspecialidad" api="app/Ajenos/Busqueda_SubEspecialidad" :api_campos="['Codigo','Nombre']" :api_titulos="['Codigo','Nombre']" :api_filtro="{Codigo_Especialidad:info.Especialidad}" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="false" />
                </div>
            </div>

            <vs-table2 id="valida-adm-table" exportarExcel="Honorarios Mediprocesos" v-if="HonorariosMediprocesos" search tooltip :data="HonorariosMediprocesos">
                <template slot="header">
                    <div class="flex flex-row">
                        <div class="pa-5 mr-5" style="border:1px solid #ccc;padding:7px; border-radius:5px">
                            <vs-checkbox :disabled="!info.Codigo" v-model="AllSelected" indeterminate>
                                Seleccionar todos
                            </vs-checkbox>
                        </div>
                        <vs-button color="primary" type="filled" @click.native="Consulta().Consulta_Datos()" icon-pack="feather" icon="icon-refresh-cw" :disabled="info.Codigo==null">
                            Actualizar
                        </vs-button>
                    </div>
                </template>

                <template slot="thead">
                    <th non-border width="50px"></th>
                    <th order="NombreSucursal" width="150px">Hospital</th>
                    <th order="TipoRegistro" width="150px">Tipo Doc.</th>
                    <th order="NombreCategoria">Tipo Honorario</th>
                    <th order="Admisión" width="120px">Admisión</th>
                    <th order="Documento" width="120px">Orden</th>
                    <th order="Admision_FechaEgreso" width="150px">Fecha Egreso</th>
                    <th order="NombrePaciente">Paciente</th>
                    <th order="NombreSeguro">Tipo Paciente</th>
                    <th order="Valor" orderType="number">Monto</th>
                    <th order="Observaciones" width="120px">No. Aut.</th>
                    <th width="50px" orderType="number">Acción</th>
                </template>

                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">

                        <vs-td2 noTooltip noExcel>
                            <vs-checkbox v-if="IsCheckable(tr)" v-model="tr.seleccion" danger />
                            <vs-checkbox v-else disabled="true" />
                        </vs-td2>

                        <vs-td2>
                            {{ tr.NombreSucursal.toUpperCase() }}
                        </vs-td2>

                        <vs-td2>
                            {{ tr.TipoRegistro }}
                        </vs-td2>

                        <vs-td2>
                            <span v-if="tr.Categoria">({{ tr.Categoria }})</span> {{ tr.NombreCategoria.toUpperCase() }}
                        </vs-td2>

                        <vs-td2>
                            {{ tr.SerieAdmision }}{{ tr.Admision }}
                        </vs-td2>

                        <vs-td2>
                            {{ tr.Documento }}
                        </vs-td2>

                        <vs-td2>
                            {{ $formato_fecha(tr.Admision_FechaEgreso) }}
                        </vs-td2>

                        <vs-td2>
                            {{tr.Cita_Cliente ? tr.NombreCliente : tr.NombrePaciente }}
                        </vs-td2>

                        <vs-td2>
                            {{ tr.NombreSeguro }}
                        </vs-td2>
                        <vs-td2>
                            <div style="text-align:right">
                                {{$formato_moneda(tr.Valor)}}
                            </div>
                        </vs-td2>
                        <vs-td2>
                            {{ tr.Observaciones }}
                        </vs-td2>
                        <vs-td2 noTooltip>
                            <vs-button v-if="IsCheckable(tr)" color="success" size="small" icon-pack="fas" icon="fa-edit" class="mr-1" style="display:inline-block" v-on:click="EditAutorizacion = tr.Observaciones; modalAutorizacion=true; HonorariosSeleccion=[tr]"></vs-button>
                        </vs-td2>
                    </tr>
                </template>
            </vs-table2>

            <div class="flex bottom">
                <vs-spacer></vs-spacer>
                <vs-button class="m-1" icon="check" color="success" type="filled" @click.native="handleSubmit(()=>{HonorariosSeleccion = HonorariosMediprocesos.filter(f=>f.seleccion); EditAutorizacion = ''; modalAutorizacion = true})" :disabled="invalid || HonorariosMediprocesos.filter(f=>f.seleccion).length==0">Validar seleccionados</vs-button>
                <vs-button class="m-1" icon-pack="fas" icon="fa-file-pdf" color="danger" type="border" @click.native="handleSubmit(GenerarEstadoCuenta)" :disabled="invalid || HonorariosMediprocesos.length==0 || !permisos.verReporte"> Ver Estado de Cuenta</vs-button>
            </div>
        </ValidationObserver>

    </vx-card>

</div>
</template>

<script>
const NombreReporteEstadoCuenta = 'Estado de Cuenta Mediprocesos'
export default {
    data() {
        return {

            /**
             * Documentación del módulo
             */
            doc_: [
                'Genera el listado de todos los honorarios de pólizas de Mediprocesos, los cuales se agrupan por admisión.',
                'Selecciona una admision una admision u honorarios para marcalos como autorizados.',
                'Los honorarios autorizados serán visibles en el estado de cuenta de MediProcesos (el cual únicamente es informativo no tiene proceso de pago por parte del hospital).',
                'Para editar el número de autorizacion se requiere el permiso EDITARAUTORIZACION.',
                'Para poder enviar el correo es necesario el permiso Reportes - Reporte - ENVIARCORREO.'
            ],

            /**
             * Editar el monto de la tabla
             */
            info: {
                Codigo: null,
                Medico: null,
                NIT: null,
                Tipo: null,
                Especialidad: null,
                SubEspecialidad: null,
                CorreoElectronico: null,
            },

            /**
             * Listado de permisos
             */
            permisos: {
                editarAutorizacion: false,
                agregarAutorizacion: false,
                verReporte: false,
            },

            HonorariosMediprocesos: [], //Honorarios de mediporcesos para el medicos seleccionado
            HonorariosSeleccion: [], //Honorario seleccionado para la edicion :)
            /**Valor del numero de autorización a actualizar o asignar */
            EditAutorizacion: null,
            /**Modal para visualizar el ingreso de número de autorización*/
            modalAutorizacion: false,
            ParametrosReporte: [],
        }
    },
    computed: {

        AllSelected: {
            set: function (newValue) {
                this.HonorariosMediprocesos.forEach(x => {
                    //if (x.Autorizado == 'S' && !this.permisos.editarAutorizacion)
                    //    return
                    if (this.IsCheckable(x))
                        x.seleccion = newValue
                })
            },
            get: function () {
                return this.HonorariosMediprocesos && this.HonorariosMediprocesos.length > 0 && !this.HonorariosMediprocesos.some(x => !x.seleccion && this.IsCheckable(x))
            }
        }

    },

    components: {

    },
    methods: {
        //=======================================================================================================
        // CONSULTA
        //=======================================================================================================
        Consulta: function () {
            return {
                Consulta_Ajenos: (item) => {
                    this.info.Medico = `${item.Nombre.trim()} ${item.Apellido.trim()}`
                    this.Consulta().Consulta_InfoAjenos(this.info.Codigo)
                        .then(() => {
                            this.Consulta().Consulta_Datos()
                        })
                },

                Consulta_Datos: () => {
                    this.Consulta().Consulta_Honorarios(this.info.Codigo)
                },

                Consulta_InfoAjenos: (codigo) => {
                    return this.axios.post('/app/ajenos/Consulta_Ajenos', {
                            Codigo: codigo
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                let datos = resp.data.json[0]

                                // Remplazando el tipo  si se ha seleccionado tipocasacortesia
                                if (datos['TipoCortesiaCasa'] == true) datos['Tipo'] = 'CC'
                                this.info.Tipo = datos.Tipo
                                this.info.Especialidad = datos.EspecialidadPrincipal.trim()
                                this.info.SubEspecialidad = datos.Especialidad.trim()
                                this.info.CorreoElectronico = datos.CorreoElectronico.trim()
                                return datos.Proveedor.trim()
                            }
                        })
                },
                Consulta_Honorarios: (Ajeno) => {
                    this.HonorariosMediprocesos = []
                    return this.axios.post('/app/ajenos/HonorariosNoPagadosHospital', {
                            Ajeno,
                            ValidacionReglas: 2,
                            TipoPlanilla: null // 'H' // Honorarios
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.HonorariosMediprocesos = resp.data.json.map(m => {
                                    return {
                                        seleccion: false,
                                        Accion: null,
                                        ...m,
                                    }
                                })
                            }
                        })
                        .catch(() => {

                        })
                },
            }
        },

        //=======================================================================================================
        // NUEVO
        //=======================================================================================================
        Nuevo: function () {
            return {}
        },

        //=======================================================================================================
        // GUARDAR
        //=======================================================================================================
        Guardar: function () {
            return {
                AutorizarHonorario: () => {
                    let HonorariosProcesar = this.HonorariosSeleccion
                    if (HonorariosProcesar.length == 0)
                        return
                    this.axios.post('/app/ajenos/HonorariosAutorizarGrupo', {
                        Ids: HonorariosProcesar.map(x => x.Id),
                        Observaciones: this.EditAutorizacion
                    }).then(() => {
                        this.Consulta().Consulta_Honorarios(this.info.Codigo)
                    }).catch()
                }
            }
        },

        //=======================================================================================================
        // EDITAR
        //=======================================================================================================
        Editar: function () {
            return {}
        },

        //=======================================================================================================
        // ELIMINAR
        //=======================================================================================================
        Eliminar: function () {
            return {}
        },

        //=======================================================================================================
        // OTROS
        //=======================================================================================================
        Otros: function () {
            return {
                Limpiar_Datos: () => {
                    Object.entries(this.info).forEach(([key]) => {
                        this.info[key] = null
                    });
                    this.HonorariosMediprocesos = []
                    this.HonorariosSeleccion = []
                },

                AutorizadoCancelar: () => {
                    this.EditAutorizacion = null
                },
            }
        },
        GenerarEstadoCuenta() {

            let paramReport = this.$prepara_valores_reporte({
                Nombre: NombreReporteEstadoCuenta,
                Data_source: {
                    Ajeno: this.info.Codigo,
                    EnviarA: this.info.CorreoElectronico
                },
                Data_report: this.ParametrosReporte
            })

            paramReport.ConfigCorreo = {
                EnvioCorreo: 'S', //para esta pantalla si por defecto :)
                to: this.info.CorreoElectronico,
                cc: paramReport.CopiarA ? eval(paramReport.CopiarA) : [],
                bcc: paramReport.CopiarEnOculto ? eval(paramReport.CopiarEnOculto) : [],
                msg: paramReport.MensajeDescripcion,
                sbjct: paramReport.Asunto,

                attachLimit: paramReport.maxAdjuntos || 1,
                attachTypes: paramReport.tipoAdjuntos || '.pdf',
                allowAddAttachment: Boolean(paramReport.agregarAdjuntos),
                allowDeleteAttachment: Boolean(paramReport.eliminarAdjuntos),
                msgTitle: paramReport.MensajeTitulo,
                msgSignature: paramReport.MensajeFirma,
                fileName: paramReport.NombreDelArchivo
            }

            this.$reporte_modal({
                Nombre: 'Estado de Cuenta Mediprocesos',
                Opciones: paramReport
            })

        },

        IsCheckable(honorario) {
            return !(!this.permisos.editarAutorizacion && honorario.Autorizado == 'S')
        },

    },
    mounted() {
        this.permisos.editarAutorizacion = this.$validar_privilegio('EDITARAUTORIZACION').status
        this.permisos.agregarAutorizacion = this.$validar_privilegio('AGREGARAUTORIZACION').status
        this.$validar_funcionalidad('/REPORTE/REPORT001', 'ESTADO DE CUENTA MEDIPROCESOS', (p) => {
            this.permisos.verReporte = p.status
        })
        //el permiso de envio correo se omite aquí
    },
    async beforeCreate() {
        this.ParametrosReporte = await this.$recupera_parametros_reporte(NombreReporteEstadoCuenta)
    },
}
</script>

<style>
#valida-adm-table>div.contenedor-tabla>table>thead {
    position: sticky;
    top: 0;
    background-color: #057FF4;
    color: #E5F8FE;
    z-index: 1;
}

#valida-adm-table .contenedor-tabla {
    height: calc(100vh - 500px) !important;
    min-height: 350px;
}

#valida-adm-table .contenido {
    z-index: 0;
}

#valida-adm-table>div.contenedor-tabla>table>thead>th>div {
    border-left-color: #E5F8FE !important;
}

#valida-adm-table>div.contenedor-tabla>table>thead>th:nth-child(1)>div {
    border-color: transparent !important;
}
</style>
