<template>
<div id="Bancos">
    <DxDataGrid :ref="gridCuentas" v-bind="DefaultDxGridConfiguration" :data-source="cuentas" :headerFilter="{visible:false, allowSearch:false}" :width="'100%'" :height="'500px'" @row-updating="ModificarCuenta" @row-inserting="AgregarCuenta" @row-removing="EliminarCuenta" @row-updated="CargarCuentas" @row-inserted="CargarCuentas" @row-removed="CargarCuentas" @init-new-row="addRow" @editing-start="startEditing">
        <DxDataGridSelection mode="single" />

        <DxDataGridEditing :allow-updating="true" :allow-adding="true" :allow-deleting="true" mode="popup" :use-icons="true" :confirmDelete="true">
            <DxDataGridPopup height="auto" width="50%" :hide-on-parent-scroll="true">
                <DxPopupToolbarItem widget="dxButton" toolbar="bottom" :options="{ text: 'Guardar', type: 'success', useSubmitBehavior: true, onClick: ()=>{this.dataGridCuentas.saveEditData()} }" location="after" />
                <DxPopupToolbarItem widget="dxButton" toolbar="bottom" :options="{ text: 'Cancelar', type: 'danger', useSubmitBehavior: true, onClick: ()=>{this.dataGridCuentas.cancelEditData()} }" location="after" />
            </DxDataGridPopup>
            <DxDataGridForm labelMode="floating" :customize-item="customizeItem">
                <DxFormGroupItem caption="Datos cuenta">
                    <DxFormItem data-field="Codigo" :editor-options="{ maxLength: 2, minLength: 2, format: '00' }" :validationRules="[{ type: 'required' }]" />
                    <DxFormItem data-field="Banco" :validationRules="[{ type: 'required' }]" />
                    <DxFormItem data-field="Cuenta" :editor-options="{ maxLength: 20 }" :validationRules="[{ type: 'required' }]" />
                    <DxFormItem data-field="Nombre" :editor-options="{ maxLength: 60 }" :validationRules="[{ type: 'required' }]" />
                    <DxFormItem data-field="TipoCuentaBancaria" />
                </DxFormGroupItem>
                <DxFormGroupItem caption="Datos contables">
                    <DxFormItem data-field="Contabilidad" :editor-options="{ maxLength: 16 }" />
                    <DxFormItem data-field="SaldoInicial" />
                    <DxFormItem data-field="SaldoIniBanco" />

                    <DxFormItem data-field="SiguienteCheque" />
                    <DxFormItem data-field="FormatoArchivoElectronico" />
                </DxFormGroupItem>
            </DxDataGridForm>
        </DxDataGridEditing>

        <DxDataGridColumn :width="100" type="buttons" alignment="center">
            <DxDataGridButton name="edit" />
            <DxDataGridButton name="delete" />
        </DxDataGridColumn>

        <DxDataGridColumn :width="100" caption="Código" calculate-display-value="Codigo" alignment="center" />
        <DxDataGridColumn :width="100" data-field="Codigo" caption="Código" alignment="center" data-type="number" :visible="false" :form-item="{ visible: false }" />
        <DxDataGridColumn :width="100" caption="Banco" calculate-display-value="Banco" alignment="center" />
        <DxDataGridColumn :width="100" data-field="Banco" alignment="center" calculate-display-value="Banco" :visible="false">
            <DxDataGridLookup :data-source="bancos" :drop-down-options="dropDownOptions" value-expr="CodigoBanco" display-expr="NombreBanco" />
        </DxDataGridColumn>
        <DxDataGridColumn :width="200" data-field="Cuenta" caption="No. Cuenta" alignment="center" />
        <DxDataGridColumn :width="400" data-field="Nombre" alignment="center" />
        <DxDataGridColumn :width="400" data-field="Contabilidad" alignment="center" :visible="false" />
        <DxDataGridColumn :width="400" data-field="TipoCuentaBancaria" caption="Tipo cuenta" alignment="center" :visible="false">
            <DxDataGridLookup :data-source="tipoCuentas" :drop-down-options="dropDownOptions" value-expr="value" display-expr="text" />
        </DxDataGridColumn>
        <DxDataGridColumn :width="400" data-field="SaldoInicial" alignment="center" data-type="number" :editor-options="{ format: saldoFormat }" :visible="false" />
        <DxDataGridColumn :width="400" data-field="SaldoIniBanco" caption="Saldo inicial (Banco)" alignment="center" data-type="number" :editor-options="{ format: saldoFormat }" :visible="false" />
        <DxDataGridColumn :width="400" data-field="SiguienteCheque" caption="Siguiente cheque" alignment="center" data-type="number" :visible="false" />
        <DxDataGridColumn :width="400" data-field="FormatoArchivoElectronico" caption="Archivo transferencia" alignment="center" data-type="number" :editor-options="{ maxLength: 1, format: '0' }" :visible="false" />
    </DxDataGrid>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'
import 'devextreme-vue/lookup'
import 'devextreme-vue/radio-group';

const gridCuentas = 'gridCuentas'

export default {
    name: 'Cuentas',
    components: {},
    data() {
        return {
            DefaultDxGridConfiguration,
            gridCuentas,
            cuentas: [],
            bancos: [],
            tipoCuentas: [{
                value: 3,
                text: 'Monetaria'
            }, {
                value: 4,
                text: 'Ahorro'
            }, {
                value: 0,
                text: 'Dolares'
            }],

            dropDownOptions: {
                showTitle: false,
                hideOnOutsideClick: true,
            },

            saldoFormat: {
                type: 'fixedPoint',
                precision: 2
            },

            showCodigo: false, //Varable para mostrar u ocultar el campo código en el formulario
        }
    },
    methods: {
        CargarCuentas() {
            this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 1
                })
                .then(resp => {
                    this.cuentas = resp.data.json
                })
        },
        CargarBancos() {
            this.axios.post('/app/v1_bancos/Bancos', {
                    Opcion: 1
                })
                .then(resp => {
                    this.bancos = resp.data.json.map((x) => {
                        return {
                            CodigoBanco: x.Codigo,
                            NombreBanco: x.Nombre
                        }
                    })
                })
        },
        AgregarCuenta(e) {
            e.cancel = new Promise((resolve, reject) => {
                var existe = false

                for (const i of this.cuentas) {
                    if (i.Codigo == e.data.Codigo) {
                        existe = true
                        break;
                    }
                }

                if (existe !== true) {
                    this.axios.post('/app/v1_bancos/Cuentas', {
                            Codigo: e.data.Codigo > 0 && e.data.Codigo < 10 ? '0' + e.data.Codigo : e.data.Codigo,
                            // Codigo: e.data.Codigo,
                            Banco: e.data.Banco,
                            Cuenta: e.data.Cuenta,
                            Nombre: e.data.Nombre,
                            Contabilidad: e.data.Contabilidad ? e.data.Contabilidad : null,
                            SiguienteCheque: e.data.SiguienteCheque ? e.data.SiguienteCheque : null,
                            Moneda: Number.isInteger(e.data.TipoCuentaBancaria) ? (e.data.TipoCuentaBancaria === 0 ? "$" : 'Q') : null,
                            SaldoInicial: e.data.SaldoInicial ? e.data.SaldoInicial : null,
                            SaldoIniBanco: e.data.SaldoIniBanco ? e.data.SaldoIniBanco : null,
                            FormatoArchivoElectronico: e.data.FormatoArchivoElectronico ? e.data.FormatoArchivoElectronico : null,
                            TipoCuentaBancaria: e.data.TipoCuentaBancaria ? e.data.TipoCuentaBancaria : null,
                            Opcion: 2
                        })
                        .then((resp) => {
                            resp.data.codigo == 0 ? resolve(false) : resolve(true)
                        }).catch((err) => {
                            reject(err.descripcion ? err.descripcion : err)
                        })
                } else {
                    resolve(false)
                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'Código incorrecto',
                        acceptText: 'Aceptar',
                        text: 'El código que se le asignó a la cuenta ya existe. Asigne otro código a la nueva cuenta.',
                        buttonCancel: 'border',
                        accept: () => {},
                    })
                }
            })
        },
        ModificarCuenta(e) {
            e.cancel = new Promise((resolve, reject) => {
                this.axios.post('/app/v1_bancos/Cuentas', {
                        Codigo: e.oldData.Codigo,
                        Banco: e.newData.Banco ? e.newData.Banco : e.oldData.Banco,
                        Cuenta: e.newData.Cuenta ? e.newData.Cuenta : e.oldData.Cuenta,
                        Nombre: e.newData.Nombre ? e.newData.Nombre : e.oldData.Nombre,
                        Contabilidad: e.newData.Contabilidad ? e.newData.Contabilidad : e.oldData.Contabilidad,
                        SiguienteCheque: e.newData.SiguienteCheque ? e.newData.SiguienteCheque : e.oldData.SiguienteCheque,
                        Moneda: Number.isInteger(e.newData.TipoCuentaBancaria) ? (e.newData.TipoCuentaBancaria === 0 ? "$" : 'Q') : e.oldData.Moneda,
                        SaldoInicial: e.newData.SaldoInicial ? e.newData.SaldoInicial : e.oldData.SaldoInicial,
                        SaldoIniBanco: e.newData.SaldoIniBanco ? e.newData.SaldoIniBanco : e.oldData.SaldoIniBanco,
                        FormatoArchivoElectronico: e.newData.FormatoArchivoElectronico ? e.newData.FormatoArchivoElectronico : e.oldData.FormatoArchivoElectronico,
                        TipoCuentaBancaria: e.newData.TipoCuentaBancaria ? e.newData.TipoCuentaBancaria : e.oldData.TipoCuentaBancaria,
                        Opcion: 4
                    })
                    .then((resp) => {
                        resp.data.codigo == 0 ? resolve(false) : resolve(true)
                    }).catch((err) => {
                        reject(err.descripcion ? err.descripcion : err)
                    })
            })
        },
        EliminarCuenta(e) {
            e.cancel = new Promise((resolve, reject) => {
                this.axios.post('/app/v1_bancos/Cuentas', {
                    Codigo: e.data.Codigo,
                    Opcion: 3
                }).then((resp) => {
                    resp.data.codigo == 0 ? resolve(false) : resolve(true)
                }).catch((err) => {
                    reject(err.descripcion ? err.descripcion : err)
                })
            })
        },

        addRow() {
            this.showCodigo = true
        },

        startEditing() {
            this.showCodigo = false
        },

        refreshDataGrid() {
            this.dataGridCuentas.refresh();
            this.dataGridCuentas.clearSelection();
            this.dataGridCuentas.clearSorting();
            this.dataGridCuentas.clearFilter();
            this.dataGridCuentas.pageIndex(0);
        },

        customizeItem(item) {
            if (item && item.dataField === "Codigo") {
                item.visible = this.showCodigo
            }
        }
    },
    created() {},
    mounted() {
        this.CargarCuentas()
        this.CargarBancos()

        this.showCodigo = false
        // Globalize.culture().numberFormat.currency.symbol = "Q"
    },
    watch: {},
    computed: {
        dataGridCuentas: function () {
            return this.$refs[gridCuentas].instance;
        },
    }
}
</script>

<style>

</style>
