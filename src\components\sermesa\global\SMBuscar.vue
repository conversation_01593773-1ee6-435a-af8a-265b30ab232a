<template>
<div class="smbuscador" :class="[(focusButton?'activo':'')]" @keyup="(e)=>{if (e.keyCode==115) (!disabled_busqueda)?abrir_buscador():null}">
    <buscador ref="buscador" :buscador_titulo="'Buscador / '+label" :api="api" :api_filtro="(buscador_activo && !api_filtro_buscar)?null:api_filtro" :campos="api_campos" :titulos="api_titulos" :multiselect="api_multiselect" :api_validar_campo="true" :api_preload="api_preload" :api_cache="api_cache" :api_disable_seleccion="api_disable_seleccion" />
    <label class="vs-input--label" v-if="label!=''">{{label}}</label>
    <vx-input-group class="">
        <div style="position:relative">
            <div v-if="disabled_texto && campo_respuesta_mostrar" @click="(mostrar_busqueda && !disabled_busqueda  && !bloqueo_agregar)?abrir_buscador():null">
                <vs-input v-if="!api_multiselect" :value="campo_respuesta_mostrar" disabled />
                <div v-else-if="campo_respuesta_mostrar!=''" class="multiselect">
                    {{campo_respuesta_mostrar.split(',').length}} Registro(s)
                </div>
            </div>
            <vs-input ref="buscador_texto" v-else-if="!disabled_texto" v-model="buscador_valor" :danger="danger" :danger-text="dangertext" ed @focus="focusTexto = true" @keyup.enter="actualizar_buscador()" @blur="focusTexto = false;actualizar_buscador()" />
            <div v-else @click="(mostrar_busqueda && !disabled_busqueda  && !bloqueo_agregar && (!callback_cancelar || (callback_cancelar && value == null)))?abrir_buscador():null" style="position:relative">
                <vs-input :value="buscador_valor" disabled :danger="danger" :danger-text="dangertext" />
            </div>
            <div v-if="!disabled_texto && campo_respuesta_mostrar" style="position:absolute;bottom:-25px;height:25px;background-color:white;padding:5px;font-size:12px;box-shadow:0 0 1px 1px #ccc;overflow:hidden">
                {{campo_respuesta_mostrar}}
            </div>
        </div>

        <template slot="append">
            <div class="append-text btn-addon">
                <div v-if="mostrar_busqueda" class="buscador">
                    <vs-button clientWidth="20" ref="btnBuscador" @focus="focusButton=true" @blur="focusButton=false" v-if="(!callback_cancelar || value==null || value == '' || disabled_busqueda) && !bloqueo_agregar" color="primary" icon-pack="feather" @click.native="(!disabled_busqueda)?abrir_buscador():null" icon="icon-search" :disabled="disabled_busqueda"></vs-button>
                    <vs-button clientWidth="20" ref="btnBuscador" @focus="focusButton=true" @blur="focusButton=false" v-else color="danger" icon-pack="feather" @click.native="limpiar_buscador()" icon="icon-x" style="margin-left:1px"></vs-button>
                </div>
                <vs-button clientWidth="20" v-if="callback_imprimir && (value!=null && !bloqueo_agregar && !bloqueo_editar)" style="margin-left:1px" v-on:click="callback_imprimir()" icon-pack="fas" icon="fa-print"></vs-button>
                <vs-button clientWidth="20" v-if="callback_editar && (value!=null && !bloqueo_agregar && !bloqueo_editar) " color="primary" icon-pack="feather" @click.native="(!disabled_editar)?editar():null" icon="icon-edit" style="margin-left:1px" :disabled="disabled_editar"> Editar</vs-button>
                <vs-button clientWidth="20" v-if="callback_nuevo && ((!callback_cancelar || value==null) && !bloqueo_agregar) " color="success" icon-pack="feather" @click.native="(!disabled_nuevo)?nuevo_buscador():null" icon="icon-plus" style="margin-left:1px" :disabled="disabled_nuevo"><p v-if="mostrar_texto">Nuevo</p></vs-button>
                <slot name="buttons"></slot>
            </div>
        </template>
    </vx-input-group>
</div>
</template>

<script>
export default {
    name: 'SM-Buscar',
    data() {
        return {
            focusButton: false,

            focusTexto: false,

            buscador_valor: null,
            info: null,

            //utilizado para mostrar la etiqueta 
            campo_respuesta_mostrar: null,

            //bloqueo cuando se presiona agregar
            bloqueo_agregar: false,

            //bloquear cuando se presiona editar
            bloqueo_editar: false,

            //bloqueo temporal de la etiqueta
            bloqueo_temporal: false,

            // Indica si el buscador (pantalla) es visible
            buscador_activo: false

        }
    },
    props: {
        //v-model
        value: {
            type: [String, Number,Array],
            default: null,
        },

        // Valor estatico que remplaza el v-model
        valor: {
            type: [String, Number],
            default: null,
        },

        danger: {
            type: [Boolean],
            default: false
        },
        //Información de error en input
        dangertext: {
            type: [String, Array],
            default: null
        },

        // Etiqueta del componente
        label: {
            type: [String, Number],
            default: null
        },

        //Limpiar el valor
        clear: {
            default: false
        },

        //api en donde buscara la informacion
        api: {
            type: [String, Array],
            default: null
        },

        /**
         * Filtro del api
         */
        api_filtro: {
            type: [Object],
            default: null
        },

        /**
         * Indica si el filtro se aplica al buscar un registro y no solo al escribirlo
         */
        api_filtro_buscar: {
            default: true
        },

        /** 
         * Campos devueltos desde el api
         * ['campo1','campo2',['campo3','campo3_']]
         * si se manda un array se tomara como primer campo el valor obtenido y el segundo el valor devuelto
         * Si se agrega el # al inicio del campo se omitira el campo para su visualización
         */
        api_campos: {
            default: null
        },

        /**
         * Campos a mostrar en los títulos
         * Al agregar # el campo sera invisible
         */
        api_titulos: {
            default: null
        },

        //Indica si se devolveran multiples registros
        api_multiselect: {
            default: false
        },

        //Cargar los datos previamente a ingresar una busqueda
        api_preload: {
            default: false
        },

        //Indica el campo que se devolvera si es selección única no multiple
        api_campo_respuesta: {
            default: null
        },

        /**
         * Devuelve el texto al componente
         */
        api_campo_respuesta_mostrar: {
            default: null
        },

        /**
         * Si esta activo el valor escrito debe de coincidir con la totalidad de campo a buscar
         * Si esta en falso se obtendra el primer valor que coincida
         */
        api_campo_respuesta_estricto: {
            default: true
        },

        /**
         * Guarda los datos obtenidos de forma temporal para su posterior uso
         */
        api_cache: {
            default: false
        },

        /**
         * Indica la validación para volver inactivo un campo
         * Si la validación es correcta se deshabilita
         */
        api_disable_seleccion: {
            default: null
        },

        /**
         * Permite mostrar el boton de busqueda
         */
        mostrar_busqueda: {
            default: true
        },

        /**
         * Devuelve los registros obtenidos
         */
        callback_buscar: {
            default: null
        },

        /**
         * Habilitar la opción de imprimir
         */
        callback_imprimir: {
            default: null
        },

        /**
         * Habilita la opción de editar
         */
        callback_editar: {
            default: null
        },

        /**
         * Habilita la opcion de cancelar busqueda
         */
        callback_cancelar: {
            default: false
        },

        /**
         * Habilita el boton nuevo del buscador
         */
        callback_nuevo: {
            default: null
        },

        /**
         * deshabilitar busqueda
         * Evita que se busquen valores si esta activo esta variable
         */
        disabled_search_input: {
            default: false
        },

        /**
         * Deshabilita el cuadro de texto
         */
        disabled_texto: {
            type: [Boolean],
            default: false
        },

        /**
         * Deshabilita el botón de buscar
         */
        disabled_busqueda: {
            type: [Boolean],
            default: false
        },

        disabled_editar: {
            type: [Boolean],
            default: false
        },

        disabled_nuevo: {
            type: [Boolean],
            default: false
        },
        disabled_busqueda_texto:{
            type:[Boolean],
            default: false
        }
,
        mostrar_texto: {
            type: [Boolean],
            default: true
        },
    },
    watch: {
        value() {
            if (!this.bloqueo_temporal) {
                this.campo_respuesta_mostrar = null
            }
            // console.log(1, this.value)
            this.actualizar_buscador(true)
        },

        buscador_valor(valor) {
            // Si existe valor y no existe validación de campo respuesta
            if (this.focusTexto && valor && this.disabled_search_input) {
                this.$emit('input', valor)
                return false
            }

            if (this.focusTexto) return false
            // Si esta activo el buscador se evita que vuelva a validar
            if (this.buscador_activo) {
                return false
            }

            if (!valor || !this.disabled_texto) {
                this.$emit('input', valor)
                return false
            }
        },

        /**
         * Al cambiar el disable editar se cambia el bloqueo editar (visualizacion de editar)
         */
        disabled_editar(value) {
            this.bloqueo_editar = value
        }
    },
    components: {

    },
    methods: {
        actualizar_buscador(copiar_modal = false) {
            // Solo se busca si cambio el valor
            if (this.value == this.buscador_valor) return false
            if (this.disabled_busqueda_texto){                
                this.$emit('input', this.buscador_valor)         
                return true
            } 
            if (copiar_modal) this.buscador_valor = this.value
            if (this.disabled_search_input) {
                this.$emit('input', this.buscador_valor)
                return false
            }

            let valor = this.buscador_valor
            if (valor && this.api_campo_respuesta && !this.bloqueo_temporal) {
                this.bloqueo_temporal = true
                let obj = {}
                obj[this.api_campo_respuesta] = valor
                if (this.api_campo_respuesta_mostrar) this.campo_respuesta_mostrar = 'Cargando...'
                //Esperando para actualizar valores faltantes
                // setTimeout(() => {
                    // console.log('buscando', obj)
                    this.$refs.buscador.validar(obj, (data) => {
                        if (data.length == 1) {
                            const arr = data.filter(f => f[this.api_campo_respuesta].toString().trim() == valor.toString().trim())
                            if (arr.length > 0 || !this.api_campo_respuesta_estricto) {
                                this.buscador_valor = data[0][this.api_campo_respuesta]
                                if (this.api_campo_respuesta_mostrar) this.campo_respuesta_mostrar = data[0][this.api_campo_respuesta_mostrar]
                                if (this.callback_buscar) this.callback_buscar(data[0])
                                this.$emit('input', this.buscador_valor)
                            } else {
                                this.buscador_valor = null
                                this.campo_respuesta_mostrar = null
                                this.$emit('input', null)
                            }
                        } else if (data.length > 1) {
                            // Aplicando filtro si devuelve mas de un valor
                            const arr = data.filter(f => f[this.api_campo_respuesta].trim() == valor.trim())
                            if (arr.length == 1) {
                                if (this.api_campo_respuesta_mostrar) this.campo_respuesta_mostrar = arr[0][this.api_campo_respuesta_mostrar]
                                if (this.callback_buscar) this.callback_buscar(arr[0])
                                this.buscador_valor = arr[0][this.api_campo_respuesta]
                                this.$emit('input', this.buscador_valor)
                            } else {
                                this.buscador_valor = null
                                this.campo_respuesta_mostrar = null
                                this.$emit('input', null)
                            }
                        } else {
                            this.buscador_valor = null
                            this.campo_respuesta_mostrar = null
                            this.$emit('input', null)
                        }
                        //este set time out es para que el watch de value no coloque el valor de la etiqueta en null al buscar presionando tabulador
                        setTimeout(()=>this.bloqueo_temporal = false,5)
                        
                    }, () => {
                        // console.log(err)
                        this.buscador_valor = null
                        this.campo_respuesta_mostrar = null
                        this.$emit('input', null)
                        this.bloqueo_temporal = false
                    }, true)
                // }, 100)
            }
            // Si es una busqueda normal
            else if (valor && !this.disabled_texto && !this.api_campo_respuesta) {
                alert('Debe de indicar el campo api_campo_respuesta ')
                // this.buscador_valor = valor
                // this.$emit('input', valor)
            } else if (!valor) {
                this.buscador_valor = null
                this.campo_respuesta_mostrar = null
                this.$emit('input', null)
            }
        },
        abrir_buscador() {
            this.buscador_activo = true
            this.$refs.buscador.iniciar((data) => {
                this.bloqueo_temporal = true

                if (data != null) {
                    // Si se solicita el campo respuesta
                    if (this.api_campo_respuesta) {
                        // Si no es un array
                        if (!Array.isArray(data)) {
                            if (data[this.api_campo_respuesta]) {
                                this.buscador_valor = data[this.api_campo_respuesta]
                                this.$emit('input', data[this.api_campo_respuesta])
                                /**
                                 * Llenado de etiqueta
                                 */
                                if (this.api_campo_respuesta_mostrar && data[this.api_campo_respuesta_mostrar]) this.campo_respuesta_mostrar = data[this.api_campo_respuesta_mostrar]
                            }
                        } else {
                            const arrRespuesta = []
                            const arrMostrar = []
                            data.forEach(item => {
                                if (item[this.api_campo_respuesta]) arrRespuesta.push(item[this.api_campo_respuesta].trim())
                                if (this.api_campo_respuesta_mostrar && item[this.api_campo_respuesta_mostrar]) arrMostrar.push(item[this.api_campo_respuesta_mostrar].trim())
                            })
                            // console.log(arrRespuesta.join(','))
                            this.buscador_valor = arrRespuesta.join(',')
                            this.$emit('input', arrRespuesta)
                            if (this.api_campo_respuesta_mostrar && arrMostrar.length > 0) this.campo_respuesta_mostrar = arrMostrar.join(',')
                        }
                    }
                    this.$refs.btnBuscador.$el.focus()

                    if (this.callback_buscar) this.callback_buscar(data)
                }

                setTimeout(() => {
                    // console.log('Liberando')
                    this.bloqueo_temporal = false
                    this.buscador_activo = false
                    if (!this.disabled_texto) this.$refs.buscador_texto.$el.focus()
                }, 200)
            })
        },

        nuevo_buscador() {
            if (this.callback_nuevo) this.callback_nuevo()
            this.bloqueo_agregar = true
        },

        editar() {
            if (this.callback_editar) this.callback_editar()
            this.bloqueo_editar = true
        },

        limpiar_buscador() {
            this.bloqueo_agregar = false
            this.bloqueo_editar = false
            this.$emit('input', null)
            if (typeof this.callback_cancelar == 'function') this.callback_cancelar()
        }

    },
    mounted() {
        if (this.valor) {
            this.buscador_valor = this.valor
            this.campo_respuesta_mostrar = null
        } else {
            this.actualizar_buscador(true)
        }
    }
}
</script>

<style>
.smbuscador.activo .vx-input-group.flex input {
    box-shadow: 0 0 0px 2px rgba(0, 143, 190, 1);
    /* background-color:rgba(0,143,190,0.9) */
}

</style>


<style scoped>

.multiselect {
    border: 1px solid #8f8f8f;
    overflow: hidden;
    height: 38px;
    padding: 10px 5px;
    border-radius: 5px 0 0 5px;
    text-overflow: ellipsis
}

.multiselect div {
    border: 1px solid #ccc;
    border-radius: 5px;
    margin-right: 2px;
    width: 250px;
    overflow: hidden;
    display: inline;
}
/** redondear las esquinas cuando es el ultimo elemento y con esquiena cuando hay más botones o elementos*/
.buscador:not(:last-child) button {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
</style>