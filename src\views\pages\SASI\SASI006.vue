<template>
<div class="mantenimiento-afiliado-container">
    <vx-card class="p-2" title="Mantenimiento Afiliados">
        <details open class="pb-3">
            <summary>
                Busqueda
            </summary>

            <form @submit="handleSubmit">
                <DxForm ref="CamposBusqueda" :form-data.sync="DatosBusqueda" label-location="left" label-mode="floating" :col-count="4" css-class="form-busqueda">

                    <DxFormSimpleItem data-field="PrimerNombre" :validation-rules="[BusquedaRules, StrRule]" editor-type="dxTextBox" :editor-options="{tabIndex:1, valueChangeEvent: 'keyup'}" />
                    <DxFormSimpleItem data-field="SegundoNombre" editor-type="dxTextBox" :editor-options="{tabIndex:2, valueChangeEvent: 'keyup'}" />
                    <DxFormSimpleItem data-field="PrimerApellido" :validation-rules="[BusquedaRules, StrRule]" editor-type="dxTextBox" :editor-options="{tabIndex:3, valueChangeEvent: 'keyup'}" />
                    <DxFormSimpleItem data-field="SegundoApellido" editor-type="dxTextBox" :editor-options="{tabIndex:4, valueChangeEvent: 'keyup'}" />
                    <DxFormSimpleItem data-field="NumeroAdhesion" editor-type="dxTextBox" :validation-rules="[BusquedaRules, StrRule]" :editor-options="{tabIndex:5, valueChangeEvent: 'keyup'}" />
                    <DxFormSimpleItem data-field="IdCliente" editor-type="dxTextBox" :validation-rules="[BusquedaRules, StrRule]" :editor-options="{tabIndex:6, valueChangeEvent: 'keyup',visible: DatosBusqueda.OrigenConsulta == 'sermesa'}" />
                    <DxFormSimpleItem data-field="IdAfiliado" editor-type="dxTextBox" :validation-rules="[BusquedaRules, StrRule]" :editor-options="{tabIndex:7, valueChangeEvent: 'keyup', visible: DatosBusqueda.OrigenConsulta == 'sermesa'}" />
                    <DxFormSimpleItem data-field="IdPaciente" :validation-rules="[BusquedaRules]" editor-type="dxNumberBox" :editor-options="{tabIndex:8, valueChangeEvent: 'keyup', visible: DatosBusqueda.OrigenConsulta == 'sermesa'}" />

                    <DxFormGroupItem :col-span="4" :col-count="4">
                        <DxFormSimpleItem :col-span="1" css-class="my-radio" editor-type="dxRadioGroup" data-field="OrigenConsulta" :editor-options="{
                                items:[{id:'roble', text:'El Roble'},{id:'sermesa',text:'SERMESA'}],
                                displayExpr:'text',
                                valueExpr:'id',
                                layout:'horizontal',
                                tabIndex: 9,
                                validationRules: [{type:'required'}],
                            }" />
                        <DxFormSimpleItem :col-span="1" data-field="SoloAfiliados" editor-type="dxCheckBox" :editor-options="{tabIndex:20, visible: DatosBusqueda.OrigenConsulta == 'sermesa'}" :visible = "DatosBusqueda.OrigenConsulta == 'sermesa'" />
                        <DxFormGroupItem :col-span="2" :col-count="2">
                            <DxFormButtonItem :visible="true" :button-options="{ text: 'Buscar',  type: 'default', stylingMode:'contained',  icon: 'search', useSubmitBehavior: true,
                            /*onClick: BuscarAfiliado,*/
                        }" horizontal-alignment="center" verical-alignment="buttom" />

                            <DxFormButtonItem :visible="true" :button-options="{ text: 'Limpiar', hint: 'Limpiar campos de búsqueda',  type: 'danger', stylingMode:'outlined',  icon: 'fas fa-broom', onClick: LimpiarBusqueda, }" horizontal-alignment="center" verical-alignment="buttom" />
                        </DxFormGroupItem>

                    </DxFormGroupItem>

                </DxForm>
            </form>

        </details>

        <transition name="bounce">
            <ConsultaRoble v-show="DatosBusqueda.OrigenConsulta == 'roble'" ref="ConsultaRoble" v-bind="{            
                NumeroAdhesion: DatosBusqueda.NumeroAdhesion,
                OpcionConsulta: null,
                NombresBusqueda: ''.concat(DatosBusqueda.PrimerNombre, DatosBusqueda.SegundoNombre? ' '.concat(DatosBusqueda.SegundoNombre):''),
                Apellido1Busqueda: DatosBusqueda.PrimerApellido,
                Apellido2Busqueda: DatosBusqueda.SegundoApellido,}" @selected-poliza="PolizaSeleccionadaRoble" @selected-row="PolizaSelectedRow" @pagos="onPagos" />
        </transition>

        <transition name="bounce">
            <vx-card v-show="DatosBusqueda.OrigenConsulta == 'sermesa'">
                <AfiliadoBusqueda ref="BusquedaAfiliado" v-bind="DatosBusqueda" :ShowMessage="true" @selected-row="BusquedaSelectedRow" @action-item-click="onActionClick" @nuevo="onNuevo"/>
            </vx-card>
        </transition>

    </vx-card>

    <DxPopup :visible.sync="VerFicha" content-template="ficha-afiliado-content" :position="popupPosition" width="90vw" height="95vh" :title="tituloModalFicha" :defer-rendering="true" >
        <DxPopupToolbarItem location="after" toolbar="top" locate-in-menu="never" :visible="!['DEPENDIENTE','NUEVO'].includes(FichaOptions.Modo)" template="acciones" />

        <template #acciones>
            <div>
                <AfiliadoAcciones :data="Afiliado" :action-buttons="Acciones" @action-item-click="onActionClick"/>
            </div>
        </template>

        <template #ficha-afiliado-content>
            <DxScrollView :scroll-by-conent="true" width="100%" height="100%">
                <div>
                    <FichaAfiliado ref="refFicha" v-show="VerFicha && DatosBusqueda.OrigenConsulta == 'sermesa'" v-bind="{...FichaOptions,...Afiliado}" :DisabledItems="FichaDiabledFileds" @saved="onSavedFicha" @busqueda-cliente="onBusquedaCliente" @cliente-paciente-changed="onClientePacienteChanged"/>
                </div>
            </DxScrollView>
        </template>
    </DxPopup>

    <DxPopup :visible.sync="VerPagos" :drag-enabled="false" :hide-on-outside-click="false" :show-close-button="true" :show-title="true" width="85%" :height="'95%'" :title="`Pagos - Contrato No. ${Afiliado.NumeroAdhesion}`" content-template="pagos-content" :position="popupPosition">
        <template #pagos-content>
            <DxScrollView :scroll-by-conent="true" width="100%" height="100%">
                <div>
                    <Pagos v-if="VerPagos" v-bind="Afiliado" />
                </div>
            </DxScrollView>
        </template>
    </DxPopup>

    <DxPopup :visible.sync="VerCambioPlan" :drag-enabled="false" :hide-on-outside-click="false" :show-close-button="true" :show-title="true" width="550" :height="'auto'" :title="`Cambio de Plan - Contrato No. ${Afiliado.NumeroAdhesion}`" content-template="popup-content" :position="popupPosition">
        <template #popup-content>
            <AfiliadoCambioPlan v-if="VerCambioPlan" v-bind="Afiliado" @cancel="VerCambioPlan=false" @plan-changed="()=>{VerCambioPlan=false; BuscarAfiliado()}" />
        </template>
    </DxPopup>

    <DxPopup :visible.sync="VerParentescos" :drag-enabled="false" :hide-on-outside-click="false" :show-close-button="true" :show-title="true" width="85%" :height="'auto'" :title="`Parentescos - Contrato No. ${Afiliado.NumeroAdhesion}`" content-template="popup-content" :position="popupPosition">
        <template #popup-content>
            <AfiliadoParentesco ref="refParentesco" v-if="VerParentescos" v-bind="Afiliado" @add-dependiente="onAddDependiente" />
        </template>
    </DxPopup>

    <DxPopup :visible.sync="VerSolicitudProcedimientos" :drag-enabled="false" :hide-on-outside-click="false" :show-close-button="true" :show-title="true" width="90vw" height="95vh" :title="`Ingreso de Laboratorios para Autorización -  ${Afiliado.NombreCompleto}`" content-template="popup-content" :position="popupPosition" :focus-state-enabled="false" >
        <template #popup-content>
 
                <div>
                    <SolicitudEstudio v-if="VerSolicitudProcedimientos" v-bind="Afiliado" :NivelPrecios="DatosPlan.NivelPrecios" :Afiliado="Afiliado" :Plan="DatosPlan" @busqueda-medico="onBusquedaMedicoEstudios"/>
                </div>

        </template>
    </DxPopup>
    <DxPopup :visible.sync="VerInfoPlan" :drag-enabled="false" :hide-on-outside-click="false" :show-close-button="true" :show-title="true" :width="550" :height="'auto'" :title="`Información de Plan ${Afiliado.NumeroAdhesion}`" content-template="popup-content" :position="popupPosition">
        <template #popup-content>
            <InfoPlan v-if="VerInfoPlan" v-bind="Afiliado" :data="Planes" />
        </template>
    </DxPopup>

    <DxPopup :visible.sync="VerBitacoraObs" :drag-enabled="false" :hide-on-outside-click="false" :show-close-button="true" :show-title="true" width="90%" height="90%" :title="`Bitacora de Observaciones - ${Afiliado.NombreCompleto} ${Afiliado.NumeroAdhesion}`" content-template="popup-content" :position="popupPosition">
        <template #popup-content>
            <BitacoraObs v-if="VerBitacoraObs" v-bind="Afiliado" />
        </template>
    </DxPopup>

    <DxPopup :visible.sync="VerTrasladoSiniestro" :drag-enabled="false" :hide-on-outside-click="false" :show-close-button="true" :show-title="true" width="80%" :height="'auto'" :title="`Traslado de Siniestralidad ${Afiliado.NumeroAdhesion}`" content-template="popup-content" :position="popupPosition">
        <template #popup-content>
            <TrasladoSiniestro v-if="VerTrasladoSiniestro" v-bind="Afiliado" />
        </template>
    </DxPopup>

    <DxPopup :visible.sync="VerSiniestro" :drag-enabled="false" :hide-on-outside-click="false" :show-close-button="true" :show-title="true" width="90%" height="90%" :title="`Siniestralidad   ${Afiliado.IdAfiliado}   ${Afiliado.NombreCompleto}` " content-template="popup-content" :position="popupPosition">
        <template #popup-content>
            <DxScrollView :scroll-by-conent="true" width="100%" height="100%">
                <div class="info-plan-siniestro flex flex-wrap">
                    <InfoPlanEncabezado v-if="VerSiniestro"  :Afiliado="Afiliado" :DatosPlan="DatosPlan" />
                    <Siniestro v-if="VerSiniestro"  v-bind="Afiliado" />
                </div>
            </DxScrollView>

        </template>
    </DxPopup>

    <DxPopup :visible.sync="VerHistoricoSolicitudes" :drag-enabled="false" :hide-on-outside-click="false" :show-close-button="true" :show-title="true" width="90%" height="95%" :title="`Estudios autorizados y declinados ${Afiliado.NombreCompleto}`" content-template="popup-content" :position="popupPosition">
        <template #popup-content>
            <Solicitudes v-if="VerHistoricoSolicitudes" v-bind="Afiliado" />
        </template>
    </DxPopup>

</div>
</template>

<script>
import AfiliadoCambioPlan from './AfiliadoCambioPlan.vue'

import AfiliadoBusqueda from './AfiliadoBusqueda.vue'
import 'devextreme-vue/check-box'
import 'devextreme-vue/radio-group'

import config from 'devextreme/core/config'
import repaintFloatingActionButton from 'devextreme/ui/speed_dial_action/repaint_floating_action_button'
import {
    popupPosition
} from './data.js'
import AfiliadoAcciones from './AfiliadoAcciones.vue'
import { acciones } from './data.js'
export default {
    name: 'MantenimientoAfiliados',
    components: {
        Solicitudes: () => import("./AfiliadoSolicitudes.vue"),
        SolicitudEstudio: () => import("./AfiliadoSolicitudEstudios.vue"),

        Pagos: () => import("./AfiliadoPagos.vue"),
        Siniestro: () => import("./AfiliadoSinestro.vue"),
        BitacoraObs: () => import("./AfiliadoBitacoraObs.vue"),
        TrasladoSiniestro: () => import("./AfiliadoTrasladoSiniestro.vue"),
        FichaAfiliado: () => import("./AfiliadoFicha.vue"),
        AfiliadoParentesco: () => import("./AfiliadoParentesco.vue"),
        InfoPlan: () => import("./AfiliadoInformacionPlan.vue"),
        InfoPlanEncabezado: ()=> import ("./AfiliadoInformacionPlanEncabezado.vue"),
        AfiliadoCambioPlan,
        ConsultaRoble: () => import("./AfiliadoRoble.vue"),
        AfiliadoBusqueda,
        AfiliadoAcciones,
    },
    data() {
        return {
            //Afiliado seleccionado
            Afiliado: {
                IdPlan: null,
                IdAfiliado: null,
                NumeroAdhesion: null,
                PrimerNombre: null,
                SegundoNombre: null,
                PrimerApellido: null,
                SegundoApellido: null,
                ApellidoCasada: null,
                EstadoCivil: null,
                Genero: null,
                FechaNacimiento: null,
                Edad: null,
                IdParentesco: null,
                Parentesco: null,
                Status: null,
                IdExternoAfiliado: null,
                //...nombres...
            },
            //datos genrales del seguro 
            DatosPlan: {
                MaxDependientes: 0,
                NivelPrecios: null,
            },
            FichaOptions: {
                Modo: 'NUEVO',
                show: false,
                ParentescosPermitidos: null, //para funcionalidad de agregar dependientes, limita el listado de parentescos para evitar duplicidad
                DisabledItems: {}
            },
            DatosBusqueda: {
                IdCliente: null,
                IdAfiliado: null,
                IdPaciente: null,
                NumeroAdhesion: null,
                PrimerNombre: null,
                SegundoNombre: null,
                PrimerApellido: null,
                SegundoApellido: null,
                OrigenConsulta: 'sermesa',
                SoloAfiliados: true,
            },
            BusquedaRules: {
                type: 'custom',
                reevaluate: false,
                validationCallback: (e) => {

                    if (this.DatosBusqueda.OrigenConsulta == 'roble' && !e.value) {
                        if (['NumeroAdhesion', 'PrimerNombre', 'PrimerApellido'].includes(e.formItem.dataField) && !e.value && (!this.DatosBusqueda.PrimerNombre || !this.DatosBusqueda.PrimerApellido) && !this.DatosBusqueda.NumeroAdhesion) {
                            e.rule.message = 'Ingrese un nombres y apellidos (exactos) o el número de póliza para la búsqueda'
                            return false
                        }
                    } else {
                        if (
                            ['NumeroAdhesion', 'PrimerNombre', 'PrimerApellido', 'IdPaciente', 'IdCliente', 'IdAfiliado'].includes(e.formItem.dataField) &&
                            !e.value &&
                            (!this.DatosBusqueda.PrimerNombre || !this.DatosBusqueda.PrimerApellido) &&
                            !this.DatosBusqueda.IdAfiliado &&
                            !this.DatosBusqueda.IdPaciente &&
                            !this.DatosBusqueda.IdCliente &&
                            !this.DatosBusqueda.NumeroAdhesion
                        ) {
                            e.rule.message = 'Ingrese nombre y apellido, o código de cliente, código de paciente o número de adhesion'
                            return false
                        }
                    }
                    e.rule.message = ''
                    return true
                }
            },
            StrRule: {
                type: 'stringLength',
                max: 30,
                min: 2,
                ignoreEmptyValue: true,
                message: 'Debe contener entre 2 y 30 caracteres'
            },
            ///Accion que acualmente se esta realizando
            Vista: 'principal', //busqueda, nuevo afiliado, editar afiliado, vista de la poliza

            VerPagos: false,
            VerFicha: false,
            VerCambioPlan: false,
            VerSolicitudProcedimientos: false,
            VerParentescos: false,

            VerInfoPlan: false,
            VerBitacoraObs: false,
            VerTrasladoSiniestro: false,
            VerSiniestro: false,
            VerHistoricoSolicitudes: false,

            popupPosition,
            /**
             * Variable de control para visualizar nuevamente la ficha del afiliado cuando 
             * 1. Se selecciona editar ficha, como se colocó en el toolbar del popup acciones
             * 2. Se selecciona Dependientes
             * 3. en este punto se puede agregar un nuevo dependiente o no, y se usa el mismo componente de ficha por ello 
             * 4. se cierra modal de dependientes, aquí hace la magia esta variable para mostrar nuevamente la ficha pero en modo edición ;)
             */
            RegresaFicha: false,
            Permisos: {
                CambioNombre: false,
                EditarFicha: false, //AFI002	Afiliado Crear, Dependiente, Cambio Plan
                AgregarDependiente: false, //AFI002	Afiliado Crear, Dependiente, Cambio Plan
                CambioEstado: false, //AFI001	Afiliado Cambio de Status
                CambioPlan: false, //AFI002	Afiliado Crear, Dependiente, Cambio Plan
                NuevoAfiliado: false, //AFI002	Afiliado Crear, Dependiente, Cambio Plan
                ExcluirListaNegra: false, //AFI014	Exclusión de Afiliados de Lista Negra
                TrasladoSiniestro: false, //AFI013	Traslado de Pre Existencia
            },
            Acciones: null
        }
    },
    props: {

    },
    methods: {
        handleSubmit(e) {
            e.preventDefault()
            this.BuscarAfiliado()
        },
        BusquedaItemClick(data) {
            if (data.itemData.text == 'Pagos')
                this.onPagos()
        },
        BusquedaSelectedRow(data) {
            if (data.length)
                this.Afiliado = data[0]
            else
                this.Afiliado
        },
        LimpiarBusqueda() {
            this.busquedaForm.resetValues() //deprecated v24 (usar reset), para la v22.1 si aplica reset values
            this.busquedaForm.option('formData.OrigenConsulta', 'sermesa')
            this.busquedaForm.option('formData.SoloAfiliados', true)
            this.$refs.BusquedaAfiliado.Limpiar()
            this.$refs.ConsultaRoble.Limpiar()
        },
        LimpiarSeleccionado() {
            this.Afiliado = {
                IdPlan: null,
                IdAfiliado: null,
                NumeroAdhesion: null,
                PrimerNombre: null,
                SegundoNombre: null,
                PrimerApellido: null,
                SegundoApellido: null,
                ApellidoCasada: null,
                EstadoCivil: null,
                Genero: null,
                FechaNacimiento: null,
                Edad: null,
                IdParentesco: null,
                Parentesco: null,
                Status: null,
                TelefonoCelular: null,
                IdExternoAfiliado: null,
                //...nombres...
            }
        },
        PolizaSeleccionadaRoble(data) {
            if (data && data.length) {
                this.Afiliado.NumeroAdhesion = data[0].NoPoliza
            } else
                this.Afiliado.NumeroAdhesion = null

        },
        PolizaSelectedRow(data) {
            this.PolizaSeleccionadaRoble(data)
        },
        CargarSeguro() {
            return new Promise( (resolve,reject) =>{
                this.axios.post('/app/v1_afiliados/BusquedaSegurosPolizas', {}).then(resp => {
                    this.DatosPlan.NivelPrecios = resp.data.json.find(x => x.Asegura == this.DatosPlan.IdAseguradora)?.NivelPreciosExt ?? null
                    resolve()
                }).catch(err=>{
                    reject(err)
                })
            })
            
        },
        CargarPlan() {
            return new Promise((resolve) => {
                if (this.Afiliado.IdPlan)
                    this.axios.post("/app/v1_afiliados/BusquedaPlanes", {
                        Id: this.Afiliado.IdPlan
                    })
                    .then(resp => {
                        this.DatosPlan = resp.data.json.length > 0 ? resp.data.json[0] : ({
                            MaxDependientes: 0,
                            IdPoliza: null,
                            IdAseguradora: null,
                            EmpresaAseguradora: null,
                        })
                        resolve(resp.data.json)
                    })
                else
                    resolve([])
            })

            /**
             * Si no hay numero de adhesion solo se puede editar al cliente o crear afiliado en base a ese cliente
             * Si existe numero de ahasion se muestra la informacion de la poliza y los dependientes :)
             */
        },
        onEditar() {
            if (!this.Permisos.EditarFicha)
                this.$vs.notify({
                    time: 6000,
                    title: 'Editar Afiliado / Cliente',
                    text: 'Se muestran los datos solo como solo lectura. Para editar se requiere permiso /SASI/SASI006 MODIFICAR',
                    iconPack: "feather",
                    icon: "icon-alert-circle",
                    color: "warning",
                    position: "top-center",
                })
            this.FichaOptions.Modo = this.Permisos.EditarFicha ? 'EDITAR' : 'LECTURA'
            this.VerFicha = true
        },
        onNuevo() {
            if (!this.Permisos.NuevoAfiliado)
                this.$vs.notify({
                    time: 6000,
                    title: 'Nuevo Afiliado',
                    text: 'Para registrar nuevo afiliado se requiere permiso /SASI/SASI006 CREAR',
                    iconPack: "feather",
                    icon: "icon-alert-circle",
                    color: "warning",
                    position: "top-center",
                })
            else {
                this.FichaOptions.Modo = 'NUEVO'
                this.VerFicha = true
            }

        },
        onCambioplan() {
            if (!this.Permisos.CambioPlan)
                this.$vs.notify({
                    time: 6000,
                    title: 'Modificar Plan',
                    text: 'Para modificar el Plan de seguro se requiere permiso /SASI/SASI006 CAMBIO_PLAN',
                    iconPack: "feather",
                    icon: "icon-alert-circle",
                    color: "warning",
                    position: "top-center",
                })
            else
                this.VerCambioPlan = true
        },
        onDependientes() {
            this.VerParentescos = true
            this.CargarPlan()
        },
        onPagos() {
            ///probablemente asignar el seleccionado aund que el emit BusquedaSelectedRow ya coloca el afiliado
            this.VerPagos = true
        },
        onListanegra() {
            const dat = this.Afiliado
            if (this.Permisos.ExcluirListaNegra) {
                //preguntar 
                this.$vs.dialog({
                    type: 'confirm',
                    color: '#ED8C72',
                    acceptText: 'Si',
                    cancelText: 'No',
                    title: 'Lista negra',
                    text: `¿Esta seguro que desea excluir de la lista negra al afiliado: ${dat.NombreCompleto}?`,
                    clientWidth: 100,
                    accept: () => {
                        this.axios.post('/app/v1_afiliados/ExcluirListaNegra', {
                            IdAfiliado: dat.IdAfiliado,
                            IdPlan: dat.IdPlan,
                            NumeroAdhesion: dat.NumeroAdhesion,
                        }).then(() => this.BuscarAfiliado())
                    }
                })
            } else {
                this.$vs.notify({
                    time: 6000,
                    title: "Excuir de Lista Negra",
                    text: 'No es posible realizar la operacion, se requiere permiso /SASI/SASI006 EXCLUIR_LISTA_NEGRA',
                    iconPack: "feather",
                    icon: "icon-alert-circle",
                    color: "warning",
                    position: "top-center",
                })
            }
        },
        onEstudios() {
            this.CargarPlan().then(() => {
                this.CargarSeguro().then( () => {
                    this.VerSolicitudProcedimientos = true
                })
            })
            
        },
        onInfoPlan() {
            this.VerInfoPlan = true
        },
        onBitacora() {
            this.VerBitacoraObs = true
        },
        onSiniestro() {
            this.VerSiniestro = true
        },
        onPreexistencia() {
            if (!this.Permisos.TrasladoSiniestro)
                this.$vs.notify({
                    time: 6000,
                    title: 'Traslado Preexistencias',
                    text: 'Se requiere permiso /SASI/SASI006 TRASLADO_SINIESTRO',
                    iconPack: "feather",
                    icon: "icon-alert-circle",
                    color: "warning",
                    position: "top-center",
                })
            else
                this.VerTrasladoSiniestro = true
        },
        //Historial de solicitudes de laboratorio y/o estudios
        onHistorial() {
            this.VerHistoricoSolicitudes = true
        },
        onCancelFicha() {
            this.FichaOptions.show = false
        },
        BuscarAfiliado() {
            if (!this.busquedaForm.validate().isValid)
                return

            if (this.DatosBusqueda.OrigenConsulta == 'roble') {
                if (this.DatosBusqueda.NumeroAdhesion)
                    this.$refs.ConsultaRoble.EstadoPoliza(this.DatosBusqueda.NumeroAdhesion)
                else
                    this.$refs.ConsultaRoble.ConsultarAfiliadosxNombres()
            } else {
                this.$refs.BusquedaAfiliado.Buscar()
            }
        },
        onSavedFicha() {
            this.VerFicha = false
            this.ParentescosPermitidos = null
            if (this.VerParentescos && this.$refs.refParentesco) {
                this.$refs.refParentesco.Cargar()
            } else {
                this.BuscarAfiliado()
            }
        },
        onClientePacienteChanged(){
            this.BuscarAfiliado()
        },
        onAddDependiente(e) {
            if (!this.Permisos.AgregarDependiente) {
                this.$vs.notify({
                    time: 6000,
                    title: 'Agregar dependiente',
                    text: 'Se requiere permiso /SASI/SASI006 AGREGAR_DEPENDIENTE',
                    iconPack: "feather",
                    icon: "icon-alert-circle",
                    color: "warning",
                    position: "top-center",
                })
            }
            if (e.DependientesActuales && e.DependientesActuales.length >= this.DatosPlan.MaxDependientes) {
                this.$vs.notify({
                    time: 6000,
                    title: 'Agregar dependiente',
                    text: `La definición del plan no permite agregar dependientes (máximo: ${this.DatosPlan.MaxDependientes})`,
                    iconPack: "feather",
                    icon: "icon-alert-circle",
                    color: "warning",
                    position: "top-center",
                })
            } else {
                this.FichaOptions.Modo = 'DEPENDIENTE'
                this.VerFicha = true
                this.FichaOptions.ParentescosPermitidos = e.ParentescosPermitidos
            }

        },
        onActionClick(e) {
            //los id de los items de las acciones coinciden con los nombres de las funciones de este componente
            // ejemplo e.id es 'editar', entonces se llama a la funcion 'onEditar'  
            const id = e.itemData?.id
            if(id)
                this['on'.concat(id[0].toUpperCase(), id.substring(1, id.length))]()
        },
        onBusquedaMedicoEstudios(e)
        {
            this.displayDxModal(!e)
        },
        onBusquedaCliente(e) {
            this.displayDxModal(!e)
        },
        displayDxModal(display){
            let doc = document.getElementsByClassName('dx-overlay-content dx-popup-normal')
            for (let i = 0; i < doc.length; i++) {
                if (display)
                    doc[i].style.removeProperty('display')
                else
                    doc[i].style.display = 'none'
            }
        }
    },
    mounted() {
        config({
            floatingActionButtonConfig: {
                position: {
                    at: 'right top',
                    my: 'right top',
                    //offset: '100 150',
                },
                // direction: 'down',
                of: '.afiliado-busqueda-container',
                maxSpeedDialActionCount: 7,
            },
        })
        repaintFloatingActionButton()
        this.Acciones = acciones.map(x => {return {...x}})//clon del array porque se alterá el iten de editar ficha
        this.Acciones[0].visible = ()=> false
    },
    beforeMount() {
        this.Permisos.EditarFicha = this.$validar_privilegio('MODIFICAR').status
        this.Permisos.CambioNombre = this.$validar_privilegio('MODIFICAR_NOMBRE').status
        this.Permisos.AgregarDependiente = this.$validar_privilegio('AGREGAR_DEPENDIENTE').status
        this.Permisos.CambioEstado = this.$validar_privilegio('CAMBIO_ESTADO').status
        this.Permisos.CambioPlan = this.$validar_privilegio('CAMBIO_PLAN').status
        this.Permisos.NuevoAfiliado = this.$validar_privilegio('CREAR').status
        this.Permisos.ExcluirListaNegra = this.$validar_privilegio('EXCLUIR_LISTA_NEGRA').status
        this.Permisos.TrasladoSiniestro = this.$validar_privilegio('TRASLADO_SINIESTRO').status
    },
    watch: {
        'Afiliado.NumeroAdhesion'(value) {
            if (value) {
                this.CargarPlan()
                //carga parentescos y datos generals
            } else {
                //limpiar data
            }
        },
        'DatosBusqueda.OrigenConsulta'() {
            this.busquedaForm.repaint()
        },
        'VerFicha'(val) {
            //Es para que se limpien los datos de la ficha al cerrarse el modal, ademas permite controlar la carga de datos unicamente cuando se mustre el modal en vez de cuando cambie la seleccion del cliente ya que el componente de ficha solo carga cuando existte el ecliente y no es nuevo
            if (!val) {
                if (this.FichaOptions.Modo == 'NUEVO' && !this.RegresaFicha)
                    this.$refs.refFicha.LimpiarDatos()
                else
                    this.FichaOptions.Modo = 'NUEVO'
            }       
        },
        'VerParentescos'(val) {
            if (!val) {
                this.BuscarAfiliado()

                if (this.RegresaFicha){
                    this.onEditar()
                    this.RegresaFicha = false
                }

            } else {
                this.RegresaFicha = this.VerFicha
                this.VerFicha = false
            }
        },
    },
    computed: {
        busquedaForm() {
            return this.$refs['CamposBusqueda'].instance
        },
        tituloModalFicha() {
            return this.FichaOptions.Modo == 'NUEVO' ? 'Nuevo Afiliado' :
                this.FichaOptions.Modo == 'EDITAR' ? 'Editar Datos Generales del '.concat(this.Afiliado.IdAfiliado ? 'Afiliado' : 'Cliente') :
                this.FichaOptions.Modo == 'DEPENDIENTE' ? 'Agregar Dependiente' :
                'Datos Generales Afiliado / Cliente'
        },
        FichaDiabledFileds() {
            return !this.Permisos.CambioNombre && this.FichaOptions.Modo == 'EDITAR' ? {
                Nombre1: true,
                Nombre2: true,
                Apellido1: true,
                Apellido2: true,
                ApellidoCasada: true
            } : {}
        },
        EstudiosProps() {
            return {
                ...this.Afiliado,
                ...this.DatosPlan,
            }
        },
    },
    deactivated() {
        this.VerPagos = false
        this.VerFicha = false
        this.VerCambioPlan =  false
        this.VerSolicitudProcedimientos = false
        this.VerParentescos = false
        this.VerInfoPlan = false
        this.VerBitacoraObs = false
        this.VerTrasladoSiniestro = false
        this.VerSiniestro = false
        this.VerHistoricoSolicitudes = false
    },

}
</script>

<style>

.info-plan-siniestro .dx-label {
    width: 150px !important;
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity .9s;
}

.fade-enter,
.fade-leave-to
{
    opacity: 0;
}

.slide-fade-enter-active {
    transition: all .3s ease;
}

.slide-fade-leave-active {
    transition: all .8s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.slide-fade-enter,
.slide-fade-leave-to
{
    transform: translateX(10px);
    opacity: 0;
}

.last-group {
    margin-top: 30px;
    margin-bottom: 10px;
}

.last-group .dx-item-content {
    align-items: start;
    justify-content: center;
}

.last-group .dx-field-item {
    padding: 0 !important;
}

.buttons-group {
    display: flex;
    width: 100%;
    justify-content: center;
}

.buttons-group .dx-item-content {
    gap: 10px;
}

.form-busqueda {
    padding-bottom: 10px;
}

.my-radio .dx-field-item-label {
    display: none !important;
}

.bounce-enter-active {
    animation: bounce-in 1.2s;
}

.bounce-leave-active {
    animation: bounce-in .4s reverse;
}

@keyframes bounce-in {
    0% {
        transform: scale(0);
    }

    50% {
        transform: scale(1.1);
    }

    100% {
        transform: scale(1);
    }
}

</style>
