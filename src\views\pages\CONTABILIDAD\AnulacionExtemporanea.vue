<template>
<div>
    <form @submit="handleSubmit">
        <DxForm :ref="formConsulta" :form-data.sync="formulario" label-mode="floating">
            <!-- @field-data-changed="HabilitarBoton"> -->
            <DxFormGroupItem :col-count="2">
                <DxFormItem data-field="Cuenta" editor-type="dxSelectBox" :editor-options="{ width: 'auto', searchEnabled: true, items: cuentas,  displayExpr: 'Valor', valueExpr: 'Codigo' }" :validationRules="[{ type: 'required' }]" />
                <DxFormItem data-field="Cheque" editor-type="dxNumberBox" :editor-options="{ min: 0, format: '#0', step: 0 }" />
                <DxFormButtonItem :col-span="2" :button-options="buttonAnular" :visible="infoCheque !== null && infoCheque.Status !== 'V'" name="Anular" horizontal-alignment="center" verical-alignment="center" />
                <DxFormButtonItem :col-span="2" :button-options="buttonLiberar" :visible="infoCheque !== null && infoCheque.Status == 'V' && permisoLiberarDocumentos" name="Liberar" horizontal-alignment="center" verical-alignment="center" />
            </DxFormGroupItem>
            <DxFormGroupItem :visible="infoCheque !== null" class="pt-4" caption=" ">
                <DxFormItem template="infoCheque" :col-span="2" />
            </DxFormGroupItem>

            <template #infoCheque="{}">
                <div>
                    <DxForm :ref="formCheque" :form-data.sync="formularioCheque" label-mode="floating" :read-only="true">
                        <DxFormGroupItem :col-count="3">
                            <DxFormItem data-field="NombreBeneficiario" editor-type="dxTextBox">
                                <DxLabel text="Nombre" />
                            </DxFormItem>
                            <DxFormItem data-field="Monto" editor-type="dxNumberBox" :editor-options="{ format: '#0.00', step: 0.01 }" />
                            <DxFormItem data-field="TipoCheque" editor-type="dxTextBox">
                                <DxLabel text="Tipo" />
                            </DxFormItem>
                        </DxFormGroupItem>
                    </DxForm>
                    <div class="pt-4">
                        <DxDataGrid :ref="gridDetalle" v-bind="DefaultDxGridConfiguration" :data-source="valoresTabla" :headerFilter="{ visible: false, allowSearch: false }" :searchPanel="{ visible: false }" :width="'100%'" height="auto">
                            <DxDataGridSelection mode="single" />

                            <DxDataGridColumn width="10%" data-field="Documento" alignment="center" data-type="number" />
                            <DxDataGridColumn width="30%" data-field="Detalle" alignment="center" data-type="string" />
                            <DxDataGridColumn width="10%" data-field="Cuenta" alignment="center" data-type="string" />
                            <DxDataGridColumn width="30%" data-field="Nombre" caption="Nombre de la cuenta" alignment="center" data-type="string" />
                            <DxDataGridColumn width="10%" data-field="Debe" alignment="center" data-type="number" :customize-text="customizeTextValores" />
                            <DxDataGridColumn width="10%" data-field="Haber" alignment="center" data-type="number" :customize-text="customizeTextValores" />
                            <DxDataGridSummary>
                                <DxDataGridTotalItem column="Debe" summary-type="sum" :customize-text="customizeTextValores" :value-format="{ type: 'fixedPoint', precision: 2 }" />
                                <DxDataGridTotalItem column="Haber" summary-type="sum" :customize-text="customizeTextValores" />
                            </DxDataGridSummary>
                        </DxDataGrid>
                    </div>
                </div>
            </template>
        </DxForm>
    </form>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration,
    customizeTextValores
} from './data'
import 'devextreme-vue/lookup'

// import DxButton from 'devextreme-vue/button'

const formConsulta = 'formConsulta'
const formCheque = 'formCheque'

const gridDetalle = 'gridDetalle'

export default {
    name: 'AnulacionExtemporanea',
    components: {},
    data() {
        return {
            DefaultDxGridConfiguration,
            formConsulta,
            formCheque,
            gridDetalle,

            formulario: {
                Fecha: null,
                Cuenta: null, // Codigo de la cuenta de banco de 2 dígitos
                NumeroCuenta: null, // Número de la cuenta bancaria
                BancoContabilidad: null, // Cuenta contable de la cuenta bancaria seleccionada
                Cheque: null, // Cheque para nular
                NombreCuentaBanco: null, // Nombre de la cuenta contable de la cuenta bancaria 
            },

            formularioCheque: {
                NombreBeneficiario: null,
                Monto: null,
                TipoCheque: null
            },
            cuentas: [],

            buttonAnular: {
                width: 'auto',
                icon: 'fas fa-ban',
                text: 'Anular',
                type: 'danger',
                onClick: () => {
                    this.Anular()
                },
                useSubmitBehavior: false,
            },

            buttonLiberar: {
                width: 'auto',
                icon: 'fas fa-link-slash',
                text: 'Liberar documentos',
                type: 'success',
                onClick: () => {
                    this.LiberarDocumentos()
                },
                useSubmitBehavior: false,
            },

            infoCheque: null,
            valoresTabla: null,
            chequeInterpretacion: false,
            permisoLiberarDocumentos: false,

            periodo: null,
            tieneSaldoFinal: false
        }
    },
    props: {
        ModalidadPago: null,
        TipoCheque: null,
        // ImpresionLotes: null,
        Corporativo: null
    },
    methods: {
        customizeTextValores,
        handleSubmit(e) {
            e.preventDefault()
        },

        CargarCuentas() {
            this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 1
                })
                .then(resp => {
                    this.cuentas = resp.data.json

                    this.formulario.Cuenta = this.cuentas[0].Codigo
                })
        },

        HabilitarBoton() {
            if (this.infoCheque.Status != 'A' && this.infoCheque.Status != 'X') {
                this.formConsultaInstance.itemOption('Consultar', 'visible', true)
            } else {
                this.formConsultaInstance.itemOption('Consultar', 'visible', false)
            }
        },

        ValidarCheque(cheque) {
            if (cheque !== null && cheque >= 0) {
                return true
            }
            return false
        },

        async BuscarCheque(cheque) {
            await this.axios.post('/app/v1_bancos/Eliminacion', {
                    Opcion: 1,
                    Cuenta: parseInt(this.formulario.Cuenta),
                    TipoCheque: this.TipoCheque,
                    ChequeInicial: cheque
                })
                .then(async resp => {
                    this.LimpiarVariables()

                    if (resp.data.json.length > 0) {
                        this.infoCheque = resp.data.json[0]

                        if (this.infoCheque.Status == 'A' || this.infoCheque.Status == 'X') {
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#ed8c72',
                                title: 'Anulado',
                                acceptText: 'Aceptar',
                                text: 'El cheque se encuentra anulado',
                                buttonCancel: 'border',
                                accept: () => {
                                    this.formulario.Cheque = null
                                },
                            })
                            this.LimpiarVariables()
                            return
                        } else {
                            let saldoFinal = await this.ValidarSaldoFinal(this.infoCheque)
                            if (saldoFinal) {
                                this.$vs.dialog({
                                    type: 'alert',
                                    color: '#ed8c72',
                                    title: 'Movimiento activo',
                                    acceptText: 'Aceptar',
                                    text: 'El movimiento de la cuenta del período de registro debe estar cerrado para poder registrar esta transacción',
                                    buttonCancel: 'border',
                                    accept: () => {
                                        this.formulario.Cheque = null
                                    },
                                })
                                this.LimpiarVariables()
                                return
                            } else {
                                this.ChequeInterpretacion(this.formulario.Cheque)
                                this.BuscarVoucher(this.infoCheque)

                                this.formularioCheque.NombreBeneficiario = this.infoCheque.NombreBeneficiario
                                this.formularioCheque.Monto = this.infoCheque.Monto
                                this.formularioCheque.TipoCheque = this.infoCheque.TipoCheque

                                if (this.infoCheque.Status == 'V') {
                                    if (!this.permisoLiberarDocumentos) {
                                        this.$vs.dialog({
                                            type: 'alert',
                                            color: '#ed8c72',
                                            title: 'Permiso denegado',
                                            acceptText: 'Aceptar',
                                            text: 'El cheque ingresado está vencido. No cuenta con los permisos para desvincular documentos.',
                                            buttonCancel: 'border',
                                            accept: () => {},
                                        })
                                        this.LimpiarVariables()
                                        return
                                    }
                                }
                            }
                        }
                    } else {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'No existe',
                            acceptText: 'Aceptar',
                            text: 'El cheque ha no ha sido ingresado',
                            buttonCancel: 'border',
                            accept: () => {
                                this.formulario.Cheque = null
                            },
                        })
                        this.LimpiarVariables()
                        return
                    }
                })
        },

        ConsultarPeriodo() {
            this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 1,
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.periodo = resp.data.json[0].Codigo
                    }
                })
        },

        async ValidarSaldoFinal(e) {
            return await this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 2,
                    Cuenta: e.Cuenta,
                    Periodo: e.Periodo
                })
                .then(resp => {
                    if (resp.data.json.length > 0 && resp.data.json[0].SaldoFinal == null) {
                        return true
                    } else {
                        return false
                    }
                })
        },

        async ChequeInterpretacion(cheque) {
            await this.axios.post('/app/v1_bancos/ConsultaAnulacion', {
                    Opcion: 5,
                    Cuenta: this.formulario.Cuenta,
                    ChequeInicial: cheque
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        if (resp.data.json.Cuantos > 0) {
                            this.chequeInterpretacion = true
                        } else {
                            this.chequeInterpretacion = false
                        }
                    }
                })
        },

        async BuscarVoucher(e) {
            await this.axios.post('/app/v1_bancos/ConsultaAnulacion', {
                    Opcion: 3,
                    EmpresaCheque: e.Empresa,
                    Cuenta: e.Cuenta,
                    TipoCheque: e.Tipo,
                    ChequeInicial: e.Numero,
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.valoresTabla = resp.data.json.map(item => {
                            let temp = item.Debe;
                            item.Debe = item.Haber;
                            item.Haber = temp;
                            return item;
                        });
                    }
                })
        },

        async CargarCuentasDefault() {
            await this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 9
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.cuentaIVACobrar = resp.data.json[0].IVAPorCobrar
                    }
                })
        },

        CargarOpciones() {
            this.axios.post('/app/v1_bancos/Opciones', {
                    Opcion: 1
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.porcentajeIVA = parseFloat(resp.data.json[0].IVA / 100).toFixed(2)
                    }
                })
        },

        NumeroComas(x) {
            x = parseFloat(x).toFixed(2)
            return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        },

        DetectarCambios(viejoValor, nuevoValor) {
            if (viejoValor !== nuevoValor) {
                if (this.formulario.Cuenta !== null && this.formulario.Cheque !== null && this.formulario.Cheque > 0 && this.formulario.Cheque !== '') {
                    this.BuscarCheque(this.formulario.Cheque);
                }
            }
        },

        Anular() {
            this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 1,
                })
                .then(async resp => {
                    if (resp.data.json.length > 0) {
                        this.periodo = resp.data.json[0].Codigo

                        if (this.periodo !== null) {
                            let saldoFinal = await this.ConsultarSaldoFinal(this.infoCheque)

                            if (saldoFinal) {
                                let liberar = await this.LiberarDocumentos()

                                if (!liberar) {
                                    this.Anular1()
                                } else {
                                    this.$vs.dialog({
                                        type: 'alert',
                                        color: '#ed8c72',
                                        title: 'Error al anular',
                                        acceptText: 'Aceptar',
                                        text: 'Error al liberar documentos, no se pudo anular el cheque.',
                                        buttonCancel: 'border',
                                        accept: () => {},
                                    })
                                    return
                                }
                            } else {
                                this.$vs.dialog({
                                    type: 'alert',
                                    color: '#ed8c72',
                                    title: 'Periodo abierto',
                                    acceptText: 'Aceptar',
                                    text: 'El movimiento de la cuenta del período del documento NO está cerrado aún, anule normalmente.',
                                    buttonCancel: 'border',
                                    accept: () => {
                                        this.formulario.Cheque = null
                                    },
                                })
                                this.LimpiarVariables()
                                return
                            }
                        }
                    } else {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Sin periodo',
                            acceptText: 'Aceptar',
                            text: 'No existe periodo para la fecha de hoy',
                            buttonCancel: 'border',
                            accept: () => {},
                        })
                        return
                    }
                })

        },

        async ConsultarSaldoFinal(e) {
            return await this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 2,
                    Cuenta: e.Cuenta,
                    Periodo: e.Periodo
                })
                .then(resp => {
                    if (resp.data.json.length === 0) {
                        return false
                    } else if (resp.data.json[0].SaldoFinal !== null) {
                        return true
                    }
                })
        },

        async LiberarDocumentos() {
            let liberar = {
                Referencias: [],
                Cupones: [],
                Multiniveles: [],
                EmpAnticipo: {},
                Pago: {},
                SaldoFactura: {},
                ActualizarPagos: {},
                Radiologos: {},
                EliminarPagos: {},
                Ajenos: {},
                Saldos: {},
                ProntoPago: {},
                Bonificaciones: {},
                EliminarBonificaciones: {},
                Pagos: {}
            }

            let comprasPagos = await this.BuscarComprasPagos()

            if (comprasPagos !== null) {
                for (let i = 0; i < comprasPagos.length; i++) {
                    let nuevo = {
                        Cuenta: null,
                        Cheque: null,
                        ProveedorInicio: comprasPagos[i].Proveedor,
                        Documento: comprasPagos[i].Documento,
                    }
                    switch (comprasPagos[i].Tipo) {
                        case 'C':
                            nuevo.Opcion = 9
                            liberar.Referencias.push(nuevo)
                            break;

                        case 'U':
                            nuevo.Opcion = 10
                            liberar.Cupones.push(nuevo)
                            break;

                        case 'G':
                            nuevo.Opcion = 11
                            liberar.Multiniveles.push(nuevo)
                            break;
                    }
                }
            }

            if (this.infoCheque.TipoCheque == 'E') {

                // En Delphi la actualización de EmpAnticipos (Opcion 11) y EmpPagos (Opcion 13) están en la misma consulta, pero aquí se dejas separado porque ya existían los método para otro proceso, pero seraparados
                // --------------------------------->
                liberar.EmpAnticipo = {
                    Opcion: 11,
                    EmpresaCheque: this.infoCheque.EmpesaEspecial == null ? this.infoCheque.Empresa : this.infoCheque.EmpresaEspecial,
                    Cuenta: this.infoCheque.Cuenta,
                    ChequeInicial: this.infoCheque.Numero
                }

                liberar.Pago = {
                    Opcion: 13,
                    EmpresaCheque: this.infoCheque.EmpesaEspecial == null ? this.infoCheque.Empresa : this.infoCheque.EmpresaEspecial,
                    Cuenta: this.infoCheque.Cuenta,
                    ChequeInicial: this.infoCheque.Numero
                }
                // --------------------------------->

                liberar.SaldoFactura = {
                    Opcion: 12,
                    Cuenta: this.infoCheque.Cuenta,
                    ChequeInicial: this.infoCheque.Numero
                }

                liberar.ActualizarPagos = {
                    Opcion: 1,
                    Cuenta: this.infoCheque.Cuenta,
                    TipoCheque: this.TipoCheque,
                    Cheque: this.infoCheque.Numero,
                    Tipo: 'P'
                }
            }

            if (this.infoCheque.TipoCheque == 'R' || this.chequeInterpretacion) {
                liberar.Radiologos = {
                    Opcion: 17,
                    Cuenta: this.infoCheque.Cuenta,
                    ChequeInicial: this.infoCheque.Numero,
                }

                liberar.EliminarPagos = {
                    Opcion: 2,
                    Cuenta: this.infoCheque.Cuenta,
                    Cheque: this.infoCheque.Numero,
                    TipoCheque: this.TipoCheque,
                    Tipo: 'P'
                }
            }

            if (this.infoCheque.TipoCheque == 'A' || this.infoCheque.TipoCheque == 'M') {
                liberar.Ajenos = {
                    Opcion: 21,
                    Cuenta: this.infoCheque.Cuenta,
                    ChequeInicial: this.infoCheque.Numero,
                }

                liberar.Saldos = {
                    Opcion: 15,
                    Cuenta: this.infoCheque.Cuenta,
                    ChequeInicial: this.infoCheque.Numero,
                    TipoCheque: this.TipoCheque
                }

                liberar.ProntoPago = {
                    Opcion: 7,
                    TipoCheque: this.TipoCheque,
                    Cuenta: this.infoCheque.Cuenta,
                    ChequeInicial: this.infoCheque.Numero,
                }

                liberar.ProntoPago = {
                    Opcion: 3,
                    Cuenta: this.infoCheque.Cuenta,
                    Cheque: this.infoCheque.Numero,
                }

                liberar.EliminarPagos = {
                    Opcion: 2,
                    Cuenta: this.infoCheque.Cuenta,
                    Cheque: this.infoCheque.Numero,
                    TipoCheque: this.TipoCheque,
                    Tipo: 'P'
                }
            }

            if (this.infoCheque.TipoCheque == 'B') {

                // Deja las bonificaciones sin cheque
                liberar.Bonificaciones = {
                    Opcion: 19,
                    TipoCheque: this.TipoCheque,
                    Cuenta: this.infoCheque.Cuenta,
                    ChequeInicial: this.infoCheque.Numero,
                }

                liberar.EliminarBonificaciones = {
                    Opcion: 20,
                    TipoCheque: this.TipoCheque,
                    Cuenta: this.infoCheque.Cuenta,
                    ChequeInicial: this.infoCheque.Numero,
                }

                liberar.EliminarPagos = {
                    Opcion: 2,
                    Cuenta: this.infoCheque.Cuenta,
                    Cheque: this.infoCheque.Numero,
                    TipoCheque: this.TipoCheque,
                    Tipo: 'P'
                }
            }

            if (this.infoCheque.TipoCheque == 'P') {
                liberar.Saldos = {
                    Opcion: 15,
                    Cuenta: this.infoCheque.Cuenta,
                    ChequeInicial: this.infoCheque.Numero,
                    TipoCheque: this.TipoCheque
                }
                liberar.Pagos = {
                    Opcion: 4,
                    TipoCheque: this.TipoCheque,
                    Cuenta: this.infoCheque.Cuenta,
                    Cheque: this.infoCheque.Numero,
                }

                liberar.EliminarPagos = {
                    Opcion: 2,
                    Cuenta: this.infoCheque.Cuenta,
                    Cheque: this.infoCheque.Numero,
                    TipoCheque: this.TipoCheque,
                    Tipo: 'P'
                }
            }

            await this.axios.post('/app/v1_bancos/LiberarDocumentos', {
                    ...liberar
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        return false
                    } else {
                        return true
                    }
                })
        },

        async Anular1() {
            let anular = {
                Anular: {},
                Devolucion: {},
                BancosMovtos: {},
                AuxiliarBanco: [],
            }

            anular.Anular = {
                Opcion: 5,
                TipoCheque: this.TipoCheque,
                Cuenta: this.infoCheque.Cuenta,
                Cheque: this.infoCheque.Numero,
                Usuario: this.Corporativo,
            }

            anular.Devolucion = {
                Opcion: 10,
                TipoCheque: this.TipoCheque,
                Cuenta: this.infoCheque.Cuenta,
                ChequeInicial: this.infoCheque.Numero,
            }

            anular.BancosMovtos = {
                Opcion: 6,
                Cuenta: this.formulario.Cuenta,
                TipoCheque: 'CA',
                Cheque: this.infoCheque.Numero,
                Periodo: this.periodo,
                Monto: parseFloat(this.formularioCheque.Monto).toFixed(2),
                Usuario: this.Corporativo,
            }

            for (let i = 0; i < this.valoresTabla.length; i++) {

                anular.AuxiliarBanco.push({
                    Opcion: 4,
                    TipoMov: 'CA',
                    Cuenta: this.formulario.Cuenta,
                    Numero: this.formulario.Cheque,
                    Periodo: this.periodo,
                    CuentaBanco: this.valoresTabla[i].Cuenta,
                    Documento: this.valoresTabla[i].Documento,
                    Detalle: this.valoresTabla[i].Detalle,
                    Validacion: this.formulario.TipoValidacion,
                    Haber: this.valoresTabla[i].Haber,
                    Debe: this.valoresTabla[i].Debe,
                    Indice: i + 1,
                    Usuario: this.Corporativo,
                })
            }

            await this.axios.post('/app/v1_bancos/AnulacionExtemporaneaAgrupado', {
                    ...anular
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.LimpiarVariables()
                        this.valoresTabla = []

                        let text = 'El cheque ' + this.formulario.Cheque + ' fue anulado con éxito.'

                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Anulación exitosa',
                            acceptText: 'Aceptar',
                            text: text,
                            buttonCancel: 'border',
                            accept: () => {
                                this.formulario.Cheque = null
                            },
                        })
                        return
                    }
                })
        },

        async BuscarComprasPagos() {
            return await this.axios.post('/app/v1_bancos/Eliminacion', {
                    Opcion: 2,
                    TipoCheque: this.TipoCheque,
                    Cuenta: this.formulario.Cuenta,
                    ChequeInicial: parseInt(this.formulario.Cheque),
                    ChequeFinal: this.formulario.Cheque
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        return resp.data.json
                    }
                    return null
                })
        },

        LimpiarVariables() {
            this.infoCheque = null
            this.formularioCheque.NombreBeneficiario = null
            this.formularioCheque.Monto = null
            this.formularioCheque.TipoCheque = null
        }
    },
    created() {},
    mounted() {
        this.$validar_funcionalidad('/CONTABILIDAD/CON004', 'LIBERARDOCUMENTOS', (d) => {
            this.permisoLiberarDocumentos = d.status
        })
    },
    beforeMount() {
        this.CargarCuentas()
        this.ConsultarPeriodo()
        // this.CargarOpciones()
        // this.CargarCuentasDefault()
    },
    watch: {

        'formulario.Cuenta'(nuevoValor, viejoValor) {
            this.DetectarCambios(viejoValor, nuevoValor);
        },

        'formulario.Cheque'(nuevoValor, viejoValor) {
            this.DetectarCambios(viejoValor, nuevoValor);
        },

    },
    computed: {
        formConsultaInstance: function () {
            return this.$refs[formConsulta].instance;
        },

        formChequeInstance: function () {
            return this.$refs[formCheque].instance;
        },
    }
}
</script>

<style>
        </style>
