<template>
    <div>
        <vx-card title="Recepción de paquete">
            <div class="flex flex-wrap mb-1">
                <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6  p-1" v-if="permisos.Crear">
                    <vs-button class="w-full mr-1 mt-5" title="Crear envío" color="success"
                        @click="Otros().AbrirVentana(1, null, true)">
                        Crear
                    </vs-button>
                </div>
                <div class="w-full md:w-1/1 lg:w-1/1 xl:w-1/1 p-1" v-if="permisos.Visualizar">
                    <vs-table2 :data="listaRecepciones" max-items="15" pagination search>
                        <template slot="thead">
                            <th order="IdRepecion">ID</th>
                            <th order="FechaRecibe">Fecha Recepción</th>
                            <th order="HoraRecibe">Hora Recepción</th>
                            <th order="NombreRecibe">Nombre Recibe</th>
                            <th order="HospitalRecibe">Hospital Recibe</th>
                            <th order="CantidadPaquetes">Cantidad Paquetes</th>
                            <th></th>
                        </template>
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2>
                                    {{ tr.IdRepecion }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.FechaRecibe }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.HoraRecibe }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.NombreRecibe }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.HospitalRecibe }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.CantidadPaquetes }}
                                </vs-td2>
                                <vs-td2>
                                    <vs-button color="warning" size="small" icon-pack="fas" icon="fa-info-circle"
                                        class="mr-1" style="display:inline-block" title="Información"
                                        @click="Otros().AbrirVentana(3, tr, true)">
                                    </vs-button>
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
                </div>
            </div>
        </vx-card>
        <vs-popup :title="ventana.titulo" v-show="ventana.mostrar" :active.sync="ventana.mostrar">
            <div v-if="ventana.opcion == 1">
                <ValidationObserver ref="formValidate" v-slot="{ }" mode="lazy">
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <vs-input label="Hospital Recibe" class="w-full" v-model="sesion.sesion_sucursal_nombre"
                                disabled="true" />
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <vs-input label="Corporativo Recibe" class="w-full" v-model="sesion.corporativo"
                                disabled="true" />
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <vs-input label="Nombre Recibe" class="w-full" v-model="sesion.usuario" disabled="true" />
                        </div>
                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2  p-1">
                            <ValidationProvider name="Codigo">
                                <SM-Buscar label="Paquete" api="app/mensajeria/BusquedaPaquete"
                                    :api_filtro="{ Estado: 3, CodigoHospitalEntrega: sesion.sesion_sucursal }"
                                    :api_campos="['IdPaquete', 'FechaEnvia', 'HoraEnvia', 'CorporativoEnvia', 'NombresEnvia', 'ApellidosEnvia', 'AreaEnvia', 'CorporativoEntrega', 'NombresEntrega', 'ApellidosEntrega', 'AreaEntrega']"
                                    :api_titulos="['IdPaquete', 'FechaEnvia', '#HoraEnvia', 'CorporativoEnvia', 'NombresEnvia', 'ApellidosEnvia', '#AreaEnvia', 'CorporativoEntrega', 'NombresEntrega', 'ApellidosEntrega', '#AreaEntrega']"
                                    api_campo_respuesta="IdPaquete" :callback_buscar="Consulta().ConsultaPaquete"
                                    :api_preload="true" :api_multiselect="true" :disabled_search_input="true" />
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class="w-full md:w-1/1 lg:w-1/1 xl:w-1/1 p-1">
                        <vs-table2 max-items="5" pagination :data="detallesRecepcion" search>
                            <template slot="thead">
                                <th order="IdPaquete">ID</th>
                                <th order="FechaEnvia">Fecha Envia</th>
                                <th order="HoraEnvia">Hora Envia</th>
                                <th order="NombreEnvia">Nombre Envia</th>
                                <th order="NombreEntrega">Nombre Entrega</th>
                                <th></th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2>
                                        {{ tr.IdPaquete }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.FechaEnvia }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.HoraEnvia }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.NombreEnvia }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.NombreEntrega }}
                                    </vs-td2>
                                    <vs-td2>
                                        <vs-button color="warning" size="small" icon-pack="fas" icon="fa-info-circle"
                                            class="mr-1" style="display:inline-block"
                                            @click="Otros().AbrirVentana(2, tr, true)">
                                        </vs-button>
                                        <vs-button color="danger" size="small" icon-pack="fas" icon="fa-trash" class="mr-1"
                                            style="display:inline-block"
                                            @click="Eliminar().EliminarElemento(tr.correlativoTabla)">
                                        </vs-button>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full  p-1">
                            <ValidationProvider name="Comentario" v-slot="{ errors }">
                                <label style="font-size:12px">Comentario</label>
                                <vs-textarea v-model="info.Comentario" counter="150" :counter-danger.sync="counterDanger"
                                    class="w-full" rows="2" :danger="errors.length > 0"
                                    :danger-text="(errors.length > 0) ? errors[0] : null" required />
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                            <vs-button class="w-full mr-1 mt-5" icon-pack="fas" icon="fa-plus" color="success"
                                @click="Agregar().CrearAsignacion()">
                                Recibir Paquete
                            </vs-button>
                        </div>
                    </div>
                </ValidationObserver>
            </div>
            <div v-if="ventana.opcion == 2">
                <div class="flex flex-wrap mb-1">
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Corporativo envia: {{ this.infoDetalle.CorporativoEnvia }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Nombre envia: {{ this.infoDetalle.NombreEnvia }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Hospital envia: {{ this.infoDetalle.HospitalEnvia }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Fecha envia: {{ this.infoDetalle.FechaEnvia }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Hora envia: {{ this.infoDetalle.HoraEnvia }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Area envia: {{ this.infoDetalle.AreaEnvia }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Corporativo entrega: {{ this.infoDetalle.CorporativoEntrega }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Nombre entrega: {{ this.infoDetalle.NombreEntrega }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Hospital entrega: {{ this.infoDetalle.HospitalEntrega }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Fecha entrega: {{ this.infoDetalle.FechaEntrega }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Hora entrega: {{ this.infoDetalle.HoraEntrega }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Area entrega: {{ this.infoDetalle.AreaEntrega }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Tipo Paquete: {{ this.infoDetalle.TipoPaquete }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1" v-if="infoDetalle.DescripcionOtroTipo !== ''">
                        <label style="font-size:12px">Descripción tipo paquete: {{ this.infoDetalle.DescripcionOtroTipo
                        }}</label>
                    </div>
                    <div class="w-full md:w-1/1 lg:w-1/1 xl:w-1/1 p-1">
                        <vs-table2 max-items="5" pagination :data="detallesPaqueteEnvio">
                            <template slot="thead">
                                <th order="Cantidad">Cantidad</th>
                                <th order="Descripcion">Descripción</th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2>
                                        {{ tr.Cantidad }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.Descripcion }}
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                    <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6  p-1">
                        <vs-button class="w-full mr-1 mt-5" title="Crear envío" color="danger"
                            @click="Otros().AbrirVentana(ventana.opcionRegresar)">
                            Regresar
                        </vs-button>
                    </div>
                </div>
            </div>
            <div v-if="ventana.opcion == 3">
                <div class="flex flex-wrap mb-1">
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">ID: {{ this.infoRecepcion.IdRepecion }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Fecha Recepción: {{ this.infoRecepcion.FechaRecibe }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Hora Recepción: {{ this.infoRecepcion.HoraRecibe }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Nombre Recibe: {{ this.infoRecepcion.NombreRecibe }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Hospital Recibe: {{ this.infoRecepcion.HospitalRecibe }}</label>
                    </div>
                    <div class="w-full md:w-1/1 lg:w-1/1 xl:w-1/1 p-1">
                        <vs-table2 max-items="5" pagination :data="detallesRecepcion" search>
                            <template slot="thead">
                                <th order="IdPaquete">ID</th>
                                <th order="FechaEnvia">Fecha Envia</th>
                                <th order="HoraEnvia">Hora Envia</th>
                                <th order="NombreEnvia">Nombre Envia</th>
                                <th order="NombreEntrega">Nombre Entrega</th>
                                <th></th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2>
                                        {{ tr.IdPaquete }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.FechaEnvia }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.HoraEnvia }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.NombreEnvia }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.NombreEntrega }}
                                    </vs-td2>
                                    <vs-td2>
                                        <vs-button color="warning" size="small" icon-pack="fas" icon="fa-info-circle"
                                            class="mr-1" style="display:inline-block"
                                            @click="Otros().AbrirVentana(2, tr, true)">
                                        </vs-button>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                </div>
            </div>
        </vs-popup>
    </div>
</template>

<script>

export default {
    data() {
        return {
            /**
             * Documentación del módulo
             */
            doc_: [
                'Pantalla de Ingreso de asignacion',
            ],
            ventana: {
                titulo: '',
                mostrar: false,
                opcion: 0,
                opcionRegresar: 0
            },
            info: {
                IdMensajero: 0,
                Recepciones: [],
                Comentario: null
            },
            detallesRecepcion: [],
            infoDetalle: {},
            correlativoTabla: 0,
            listaMensajeros: [],
            selectResponsable: { Codigo: null, Descripcion: null },
            counterDanger: false,
            listaRecepciones: [],
            infoRecepcion: {},
            detallesPaqueteEnvio: [],
            permisos: {
                Visualizar: null,
                Crear: null
            }
        }
    },
    components: {
    },
    computed: {
        sesion() {
            return this.$store.state.sesion
        }
    },
    methods: {
        Consulta() {
            return {
                init: () => {
                    this.axios.post('/app/mensajeria/ListarRecepciones', {})
                        .then(resp => {
                            this.listaRecepciones = resp.data.json;
                        })
                },
                ConsultaPaquete: (data) => {
                    data.forEach((element) => {
                        element.correlativoTabla = this.correlativoTabla
                        this.detallesRecepcion.push(element)
                        this.correlativoTabla = this.correlativoTabla + 1
                    })
                },
                DetallesPaquete: (id) => {
                    return new Promise(() => {
                        this.axios.post('/app/mensajeria/DetallesPaquete', { IdPaquete: id })
                            .then(resp => {
                                this.detallesPaqueteEnvio = resp.data.json;
                            })
                    });

                },
                ListaMensajeros: () => {
                    this.axios.post('/app/mensajeria/ListarMensajeros', {})
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.listaMensajeros = resp.data.json
                                this.selectResponsable.Codigo = resp.data.json[0].Codigo
                                this.selectResponsable.Descripcion = resp.data.json[0].Descripcion
                            }

                        })
                },
                detallesRecepcion: (id) => {
                    return new Promise(() => {
                        this.axios.post('/app/mensajeria/PaquetesRecibidos', { IdRepecion: id })
                            .then(resp => {
                                this.detallesRecepcion = resp.data.json;
                            })
                    });

                }
            }
        },
        Agregar() {
            return {
                CrearAsignacion: () => {
                    if (this.detallesRecepcion.length == 0) {
                        this.$vs.notify({
                            title: "Error al crear recepción",
                            text: "Debe asignar al menos 1 paquete",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                    } else if (this.counterDanger) {
                        this.$vs.notify({
                            title: "Error al crear recepción",
                            text: "Comentario debe tener menos de 150 caracteres.",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                    } else {
                        const arrayTemp = []

                        this.detallesRecepcion.forEach(element => arrayTemp.push(element.IdPaquete))
                        this.info.Recepciones = arrayTemp.join('|')
                        this.info.IdMensajero = this.selectResponsable.Codigo

                        this.axios.post('/app/mensajeria/IngresarRecepcion', this.info)
                            .then(resp => {
                                if (resp.data.codigo == 0) {
                                    this.Consulta().init()
                                    this.Otros().LimpiarObjeto()
                                    this.ventana.mostrar = false
                                }
                            })
                            .catch(() => {

                            })
                    }
                }
            }
        },
        Eliminar() {
            return {
                EliminarElemento: (correlativoTabla) => {
                    this.detallesRecepcion = this.detallesRecepcion.filter((item) => item.correlativoTabla !== correlativoTabla)
                }
            }
        },
        Otros() {
            return {
                AbrirVentana: (opcion, objeto = null, asignarObjeto = false) => {
                    this.ventana.opcion = opcion

                    if (opcion == 1) {
                        this.ventana.mostrar = true
                        this.ventana.titulo = 'Crear recepción'
                        this.ventana.opcionRegresar = 1
                        if (asignarObjeto) {
                            this.Otros().LimpiarObjeto()
                            this.Consulta().ListaMensajeros()
                        }

                    } else if (opcion == 2) {
                        this.ventana.titulo = 'Detalles Paquete'
                        this.infoDetalle = objeto
                        this.Consulta().DetallesPaquete(this.infoDetalle.IdPaquete)
                    } else if (opcion == 3) {
                        this.ventana.mostrar = true
                        this.ventana.titulo = 'Detalles recepción'
                        if (asignarObjeto) {
                            this.infoRecepcion = objeto
                        }
                        this.ventana.opcionRegresar = 3
                        this.Consulta().detallesRecepcion(this.infoRecepcion.IdRepecion)
                    }
                },
                LimpiarObjeto: () => {
                    this.info = {
                        IdMensajero: 0,
                        Recepciones: [],
                        Comentario: null
                    }

                    this.detallesRecepcion = []
                }

            }
        }
    },
    mounted() {
        this.Consulta().init()

        this.permisos.Visualizar = this.$validar_privilegio('VISUALIZAR').status
        this.permisos.Crear = this.$validar_privilegio('CREAR').status
    }
}
</script>