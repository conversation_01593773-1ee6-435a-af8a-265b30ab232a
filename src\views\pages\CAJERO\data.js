export const DefaultDxGridConfiguration = {
    visible: true,
    showRowLines: true,
    showColumnLines: true,
    showBorders: true,
    'load-panel': {enabled: false},
    selection: {mode: 'single'},
    searchPanel: {visible: true},
    focusedRowEnabled: false,
    rowAlternationEnabled: true,  
    columnHidingEnabled: true,
    hoverStateEnabled: true,
    width: '100%',
    height:'calc(100vh - 310px)',
    columnAutoWidth: true,
    allowColumnReordering: true,
    allowColumnResizing: true,
    columnResizingMode:'widget',
    headerFilter:{
      visible:true,
      allowSearch:true
    },
    wordWrapEnabled: true,
    paging:{ enabled:true, pageSize:10 },
    scrolling: {
      showScrollbar: 'always',
      useNative: false,
    },
}

export const _confMoneda = {
  dataType: 'number',
  format: 'Q\'.\' ###,###,###.00',
}

export const _format = {  
  type: "number",  
  precision: 2 , 
  currency: "GTQ"
} 

export const _formatFunction = {  
  type: "fixedPoint",  
  precision: 2 , 
  currency: "GTQ"
} 

export function CalcularExencion(monto,iva){
  return ConvertirFloat(monto) - (ConvertirFloat(monto)/(1+ConvertirFloat(iva)))
} 

const ConvertirFloat = function(Valor,Decimales=2){
  let conversion = parseFloat(Valor?Valor:0.00)
  if(isNaN(conversion)){
      return 0.00
  }
  return parseFloat(conversion.toFixed(Decimales))
}