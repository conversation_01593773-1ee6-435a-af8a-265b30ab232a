<template>
<vx-card title="Contabilidad General">
    <div class="content content-pagex">
        <vs-divider class="label-size"> MAYORIZACIÓN DE PARTIDAS </vs-divider>

        <div class="flex flex-wrap">
            <div class="padre xs:w-full md:w-1/3 lg:w-2/6 xl:w-2/6" ></div>
            <div class="padre xs:w-full md:w-1/3 lg:w-2/6 xl:w-2/6" style="padding:10px 10px">
                <label class="label-size">Períodos</label>
                <ValidationProvider name=" Períodos">
                    <multiselect v-model="cbPeriodos" :options="ListaPeriodos" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="PeriodoSeleccionado" placeholder="Seleccionar Período" @input="onChangePeriodos">
                        <span slot="noOptions">Lista no disponible.</span>
                    </multiselect>
                </ValidationProvider>
                <br>
                <br>
                <div class="xs:w-full md:w-1/3 lg:w-3/5 xl:w-3/5">
                    <label class="label-size">Fecha:</label>
                    <vs-input type="date" v-model="FechaF" />
                </div>
                <div class="xs:w-full md:w-1/3 lg:w-1/5 xl:w-1/5" align="right">
                    <div class="flex flex-wrap" style="padding:20px 4px">
                        <div class="label-size">
                            <vs-button color="success" class="label-size" type="filled" @click="Mayorizar()"> OK </vs-button>
                        </div>
                    </div>
                </div>

                <div class="cuadro-i" style="padding:20px 20px" align="center">
                    <label class="label-i">NOTA: No incluye partidas de apertura.</label>
                </div>
            </div>
        </div>
        <vs-divider></vs-divider>
    </div>
</vx-card>
</template>

<script>
import Multiselect from 'vue-multiselect'

import "vue-multiselect/dist/vue-multiselect.min.css"
import moment from "moment";
export default {
    components: {
        Multiselect
    },
    data() {
        return {
            cbPeriodos: '',
            ListaPeriodos: [],
            CodigoPeriodo: '',
            DescPeriodo: '',
            FechaF: this.getDateValue(new Date()),
            Periodo: ''

        }
    },
    mounted() {
        this.ConsultaPeriodo()
        this.ConsultaPeridoActual()
    },
    watch: {

    },
    methods: {
        getDateValue(value) {
            if (value === undefined) {
                return '';
            } else if (value === null) {
                return '';
            } else if (value == '') {
                return '';
            }
            return moment(value).format('YYYY-MM-DD');
        },
        //PERIODOS
        ConsultaPeriodo() {
            this.axios.post('/app/v1_contabilidad_general/ConsultaPeriodo', {
                    Opcion: "C",
                    SubOpcion: "6",
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaPeriodos = resp.data.json;
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido cargar los periodos, favor validar.',
                        })
                    }
                })
                .catch(() => {})
        },
        ConsultaPeridoActual() {
           this.axios.post('/app/v1_contabilidad_general/ConsultaPeriodoActual', {
                    Opcion: "C",
                    SubOpcion: "2",
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.cbPeriodos = {
                            CodigoPeriodo: resp.data.json[0].CodigoPeriodo,
                            DescPeriodo: resp.data.json[0].Descripcion
                        }
                        this.NuevaPartida = resp.data.SigPartida == '' ? 1: resp.data.json[0].SigPartida

                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido cargar los periodos, favor validar.',
                        })
                    }
                })
        },
        onChangePeriodos(value) {
            if (value !== null && value.length !== 0) {
                this.CodigoPeriodo = value.CodigoPeriodo
                this.DescPeriodo = value.DescPeriodo
                this.FechaF = value.FechaFinal
            } else {
                this.CodigoPeriodo = ''
                this.DescPeriodo = ''
                this.FechaF = ''
            }
        },
        PeriodoSeleccionado({
            CodigoPeriodo,
            DescPeriodo
        }) {
            return `${CodigoPeriodo} - ${DescPeriodo} `
        },
        Mayorizar() {
            this.axios.post('/app/v1_contabilidad_general/Mayorizacion', {
                    Opcion: "I",
                    SubOpcion: "1",
                    Periodo: this.cbPeriodos.CodigoPeriodo,
                    FechaF: this.FechaF
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido cargar los periodos, favor validar.',
                        })
                    }
                })
        },

    }
}
</script>   

<style scoped>
.footer {
    height: 70px;
    margin-top: -65px;
    margin-left: 10px;
}

.label-size {
    font-size: 14px;
    font-weight: bold;
}

.label-i {
    font-size: 18px;
    font-weight: bold;
    color: rgb(240, 158, 6);
}

.cuadro-i {
    background: #faf2f1;
}

.flex-container {
    display: flex;
    flex-direction: row;
}

.flex-item-right {
    padding: 10px;
    flex: 50%;
    margin-left: 50px;
    margin-right: 50px;
}

/* Responsive layout - makes a one column layout instead of a two-column layout */
@media (max-width: 800px) {
    .flex-container {
        flex-direction: column;
    }
}

.padre {
    position: relative;
}

.hijo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
</style>
