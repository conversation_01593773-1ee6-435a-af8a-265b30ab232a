#docker build -t docker-registry-default.apps.priv.os.bancaremota.bi.com.gt/migracion/sighos2 .
#docker push docker-registry-default.apps.priv.os.bancaremota.bi.com.gt/migracion/sighos2
FROM sighosqa:latest AS deps
FROM nikolaik/python-nodejs:python3.10-nodejs14-alpine AS build-env

WORKDIR /usr/src/app

# RUN sed -i 's/http\:\/\/dl-cdn.alpinelinux.org/https\:\/\/alpine.global.ssl.fastly.net/g' /etc/apk/repositories
# RUN apk --no-cache add --virtual native-deps \
#   g++ gcc libgcc libstdc++ linux-headers make python && \
#   npm install --quiet node-gyp -g &&\
#   npm install --quiet && \
#   apk del native-deps

RUN npm config set proxy http://192.168.221.140:8080 --global
RUN npm config set http-proxy http://192.168.221.140:8080 --global
RUN npm config set https-proxy http://192.168.221.140:8080 --global

#$env:HTTPS_PROXY="http://192.168.221.140:8080"

COPY package*.json ./

RUN npm config set registry http://registry.npmjs.org/
RUN npm config set strict-ssl false

#RUN npm cache clean --force

#RUN npm install --unsafe-perm
COPY --from=deps /usr/src/app/node_modules ./node_modules


COPY . .

RUN node --max_old_space_size=8192 node_modules/@vue/cli-service/bin/vue-cli-service.js build
#RUN npm run build

#COPY server.js ./
#COPY dist/ ./dist
#COPY package*.json ./
#RUN npm install
#RUN npm run build
#RUN rm -rf node_modules

FROM node:14-alpine 

WORKDIR /usr/src/app

COPY --from=build-env /usr/src/app/dist ./dist/
# COPY --from=build-env /usr/src/app/ssl ./ssl/
COPY --from=build-env /usr/src/app/server.js .
COPY --from=build-env /usr/src/app/socket.js .
COPY --from=build-env /usr/src/app/.env .

RUN npm config set proxy http://192.168.221.140:8080 --global
RUN npm config set http-proxy http://192.168.221.140:8080 --global
RUN npm config set https-proxy http://192.168.221.140:8080 --global
RUN npm config set registry http://registry.npmjs.org/
RUN npm config set strict-ssl false

RUN npm install express cors connect-history-api-fallback core-js dotenv socket.io@2.3.0 uniqid

EXPOSE 8080
EXPOSE 3001
# EXPOSE 443
CMD [ "node", "./server.js" ]
# ENTRYPOINT ["./entrypoint.sh"]
