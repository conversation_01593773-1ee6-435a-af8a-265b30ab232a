<template>
  <div id="valeConatainer" class="val003-container">
    <div class=".main-vale">
      <div class="encabezado">
        <Encabezado2
          ref="refEncabezado2"
          TipoBusqudaPaciente="TODO"
          @onLoadedData="onLoadedDataEXP001"
          :FiltrosBusquedaPaciente="{
            SeleccionTipoBusqueda: false,
            Hospital: $store.state.sesion.sesion_sucursal,
          }"
        ></Encabezado2>
      </div>

      <div class="contenido p-2">
        <vx-card
          :title="'Vale eléctronico - Área: ' + DatosAdmision.AreaHabitacion"
        >
          <template v-slot:prepend>
            <vx-avatar color="surface-light" size="32">🎯</vx-avatar>
          </template>

          <!-- Contenido del cuerpo del vx-card -->
          <DxDataGrid
            v-bind="DefaultDxGridConfiguration"
            ID="ProductosSolicitados"
            :data-source="SolicitudesValeDataSource"
            :show-borders="true"
            @rowDblClick="CambiarEstado"
            :row-alternation-enabled="false"
            :on-row-prepared="onRowPrepared"
            
            height="auto"
            width="auto"
          >
          <DxFilterPanel :visible="true"/>
            <DxColumn
              :width="250"
              data-field="ID"
              caption="Codigo"
              :visible="false"
              sort-order="desc"
            />
            <DxColumn
              :width="75"
              data-field="CodProducto"
              caption="Código Producto"
              :visible="true"
              :alignment="'left'"
            />
            <DxColumn
              :width="83"
              data-field="EstadoActual"
              caption="Estado"
              :visible="true"
            />
            <DxColumn
              :width="450"
              data-field="Producto"
              caption="Medicamento"
              :visible="true"
            />
            <DxColumn
              :width="85"
              data-field="Cantidad"
              caption="Dosis cantidad"
              :visible="true"
              :alignment="'left'"
            />
            <DxColumn
              :width="250"
              data-field="Presentacion"
              caption="Dosis unidad"
              :visible="true"
            />
            <DxColumn
              :width="100"
              data-field="ViadeAdministracion"
              caption="Dosis vía"
              :visible="true"
            />
            <DxColumn
              :width="100"
              data-field="Horario"
              caption="Frecuencia"
              :visible="true"
            />
            <DxColumn
              :width="250"
              data-field="Dias"
              caption="No. de días"
              :visible="false"
            />
            <DxColumn
              :width="250"
              data-field="CodMedico"
              caption="CodMedico"
              :visible="false"
            />
            <DxColumn
              :width="250"
              data-field="Medico"
              caption="Nombre medico"
              :visible="false"
            />
            <DxColumn
              :width="100"
              data-field="FechaInicio"
              caption="Fecha inicio"
              :visible="true"
              format="dd/MM/yyyy HH:mm"
              data-type="date"
            />
            <DxColumn
              :width="100"
              data-field="FechaOmision"
              caption="Fecha omisión"
              :visible="true"
              format="dd/MM/yyyy HH:mm"
              data-type="date"
            />
            <DxColumn
              :width="100"
              data-field="TipoSolicitud"
              caption="Tipo"
              :visible="true"
            />
            <DxColumn
              :width="250"
              data-field="Colaborador"
              caption="EP/AE"
              :visible="true"
            />

            <DxSearchPanel :visible="true" width="400px" />
            <DxPaging :page-size="15" />
            <DxPager :visible="true" />
            <DxSelection mode="single" />

            <DxToolbar>
              <DxItem name="groupPanel" locateInMenu="auto" />
              <DxItem name="searchPanel" locateInMenu="auto" />
              <DxItem
                location="after"
                template="opcionesTemplate"
                locateInMenu="auto"
              />
            </DxToolbar>

            <template #opcionesTemplate>
              <div class="flex flex-wrap">
                <ExpedienteGridToolBar
                  :StatusAdmision="DatosAdmision.StatusAdmision"
                  :visible="true"
                  :pdfExportItems="[{}]"
                  @refresh="CargarSolicitudes"
                  :showItems="['refresh']"
                >
                  <DxButton
                    id="botonBarra"
                    icon="fas fa-thumbs-down"
                    hint="Omite los medicamentos seleccionados"
                    text="Guardar medicamentos omitidos"
                    type="default"
                    styling-mode="outlined"
                    @click="Omitir"
                  />
                </ExpedienteGridToolBar>
              </div>
            </template>
            <DxGroupPanel :visible="true" />
            <DxGrouping :auto-expand-all="false" />
            
          </DxDataGrid>

          <template slot="actions" v-if="mostrarDiv">
            <div class="flex flex-wrap">
              <!-- w-full md:w-1/2 lg:w-1/2 xl:w-1/2 -->
              <div
                class="btn-group pl-4 pb-4 w-full sm:w-1/2 md:w-1/2 lg:w-19/50 xl:w-19/50"
              >
                <vs-button
                  icon="fa-plus"
                  icon-pack="fas"
                  class="w-full"
                  @click="showProductos()"
                  title="Habre ventana para agregar nuevos medicamentos"
                  >Agregar medicamentos</vs-button
                >
              </div>

              <div
                class="btn-group pl-4 pb-4 w-full sm:w-1/2 md:w-1/2 lg:w-1/4 xl:w-1/4"
              >
                <vs-button
                  @click="showPopupAplicacion()"
                  icon="fa-book"
                  icon-pack="fas"
                  class="w-full"
                  title="Abre ventana para indicar si un medicamento fue aplicado o desechado"
                >
                  Seguimiento</vs-button
                >
              </div>

              <div
                class="btn-group pl-4 pb-4 w-full sm:w-1/2 md:w-1/2 lg:w-1/4 xl:w-1/4"
              >
                <vs-button
                  icon="fa-history"
                  icon-pack="fas"
                  @click="showPopupAplicacionHistorial"
                  class="w-full"
                  title="Abre ventana donde se muestra el seguimiento de medicamentos (aplicados, desechados, rehusado, suspendido) "
                  >Historial
                </vs-button>
              </div>
            </div>
          </template>
        </vx-card>
      </div>
    </div>
    <DxPopup
      id="PopupAreas"
      :visible.sync="PopupAreas"
      :show-title="true"
      title="Seleccione el area de habitación del paciente"
      :showCloseButton="true"
      :wrapper-attr="{class:'main-vale-popup'}"
    >
      <div>
        <div>{{ this.TextoSeleccion }}</div>
        <DxDataGrid
          :data-source="ValeDataSource"
          v-bind="DefaultDxGridConfiguration"
        >
          <DxSearchPanel :visible="true" />

          <DxColumn :width="250" data-field="ID" caption="Codigo" />
          <DxColumn data-field="Area" caption="Nombre Area" />
          <DxColumn data-field="Base" caption="Codigo hospital" />
        </DxDataGrid>
      </div>
      <DxToolbarItem
        widget="dxButton"
        toolbar="bottom"
        :options="{
          text: 'Seleccionar',
          type: 'success',
          useSubmitBehavior: true,
        }"
        location="after"
      >
      </DxToolbarItem>
    </DxPopup>

    <DxPopup
      id="PopupMedicamentos"
      :visible.sync="PopupMedicamentos"
      :show-title="true"
      :title="'Solicitud de productos vale electrónico'"
      :showCloseButton="true"
      height="92%"
      width="66%"
      :resizeEnabled="true"
      @hiding="onPopupClose"
      :wrapper-attr="{class:'main-vale-popup'}"
      >
      <DxScrollView  :scroll-by-content="true">
        <div class="my-stickyVal mr-4">
          <P style="font-size: 15px; color: #0693e3">
            <strong>{{ "Paciente: " }}</strong
            >{{ this.DatosAdmision.Paciente }}
            <strong>{{ "    Admisión: " }}</strong>
            {{ this.SerieAdmision + "-" + CodigoAdmision }}
          </P>
          <div class="col-span-2 mb-1">
            <div class="col-span-2 mr-4 mb-1">
              <DxDropDownBox
                value-expr="ID"
                display-expr="NombreProducto"
                :data-source="ProductosValeDataSource"
                :placeholder="'Click para buscar producto'"
                :opened.sync="dropDownOpened"
              >
                <DxScrollView class="val003-container" :scroll-by-content="true">
                  <DxDataGrid
                    :data-source="ProductosValeDataSource"
                    ref="dataGrid"
                    @selectionChanged="onSelectionChanged"
                    @row-dbl-click="onGridDoubleClick"
                    v-bind="DefaultDxGridConfiguration"
                    height="auto"
                    width="auto"
                    :customize-columns="customizeColumns"
                  >
                    <DxSearchPanel :visible="true" width="400px" />
                    <DxSelection mode="single" />
                    <DxFilterPanel :visible="true"/>

                    <DxPaging :page-size="10" />
                    <DxPager :visible="true" />
                    <DxColumn :width="100" data-field="ID" caption="Código" />
                    <DxColumn
                      data-field="NombreProducto"
                      caption="Nombre producto"
                    />
                    <DxColumn
                      :width="125"
                      data-field="Unidad"
                      caption="Unidad medida"
                    />
                    <DxColumn
                      :width="100"
                      data-field="Descripcion"
                      caption="Descripción"
                    />
                    <DxColumn
                      :width="100"
                      data-field="Cubierto"
                      caption="Cubierto"
                    />
                    <DxColumn
                      :width="100"
                      data-field="Categoria"
                      caption="Categoría"
                    />
                  </DxDataGrid>
                </DxScrollView>
              </DxDropDownBox>
            </div>

            <div >
              <P style="font-size: 20px; color: #0693e3">
                {{ "Producto Seleccionado:" }}
                {{ "Código:" }}
                {{ this.ordenDetalle.Producto }}

                {{ this.NombreProductoBusqueda }}
              </P>
              <div class="table-responsiveVale">
                <table>
                  <tr>
                    <td
                      :class="{ 'text-red': this.existenciasProducto < 0 }"
                      class="font-semibold"
                      style="font-size: 20px"
                    >
                      Existencias:
                    </td>
                    <td
                      :class="{ 'text-red': this.existenciasProducto < 0 }"
                      class="font-semibold"
                      style="font-size: 20px"
                    >
                      {{ this.existenciasProducto }}
                    </td>
                   
                    <td
                      :class="getColorClass(this.DatosAdmision.TipoSeguro)"
                      class="font-semibold"
                      style="width: 200px"
                    >
                      Tipo Seguro: {{ this.DatosAdmision.TipoSeguro }}
                    </td>
                  </tr>
                </table>
              </div>
            </div>
          </div>
          <div>
            <DxForm
              :form-data="ordenDetalle"
              col-count="auto"
              :col-count-by-screen="{ xs: 1, sm: 2, md: 4, lg: 4 }"
              label-mode="static"
              ref="refForm"
            >
              <DxSimpleItem
                data-field="Cantidad"
                editor-type="dxNumberBox"
                :editor-options="{ min: 1 }"
              >
                <DxLabel text="Cantidad producto" />
              </DxSimpleItem>

              <ItemForm
                :editor-options="editorOptions.Presentacion"
                :display-value="displayValue"
                data-field="Presentacion"
                editor-type="dxSelectBox"
                @click="obtenerValorSeleccionado"
                :visible="this.OcultarPropiedadesProductos"
              >
                <DxLabel text="Presentación" />
              </ItemForm>

              <DxSimpleItem
                :editor-options="editorOptions.ViaAdministracion"
                :display-value="displayValue"
                data-field="ViaAdministracion"
                editor-type="dxSelectBox"
                :visible="this.OcultarPropiedadesProductos"
              >
                <DxLabel text="Vía administración" />
              </DxSimpleItem>
              <DxSimpleItem
                :editor-options="editorOptions.FrecuenciaAdministracion"
                data-field="FrecuenciaAdministracion"
                editor-type="dxSelectBox"
                :visible="this.OcultarPropiedadesProductos"
              >
                <DxLabel text="Frecuencia administración" />
              </DxSimpleItem>
              <DxSimpleItem
                :editor-options="editorOptions.HorarioAplicacion"
                data-field="Horario"
                editor-type="dxSelectBox"
                :editor-value="ordenDetalle.Horario"
                :visible="
                  this.OcultarPropiedadesProductos && this.PropiedadStatVisible
                "
              >
                <DxLabel text="Horario inicio" />
              </DxSimpleItem>

              <DxSimpleItem
                data-field="CantidadDias"
                editor-type="dxNumberBox"
                :editor-options="{
                  min: 1,
                  disabled: this.ocultaDias ? false : true,
                }"
                :visible="
                  this.OcultarPropiedadesProductos && this.PropiedadStatVisible
                "
              >
                <DxLabel text="Cantidad días" />
              </DxSimpleItem>

              <DxSimpleItem
                :editor-options="{
                  displayExpr: 'text',
                  label: '',
                  layout: 'horizontal',
                  items: this.TipoSolicitudVale,
                }"
                :col-span="2"
                v-model="ElementoSolicitud"
                data-field="TipoSolicitud"
                editor-type="dxRadioGroup"
              />
            </DxForm>
          </div>

          <div class="flex flex-row-reverse mb-1 mr-4">
            <div class="btn-group pl-4 w-full sm:w-1/2 md:w-32 lg:w-32 xl:w-32">
              <vs-button
                class="w-full"
                color="success"
                type="filled"
                icon-pack="fas"
                icon="fa-save"
                @click="generarSolicitu()"
                title="Genera una nueva solicitud de los medicamentos agregados"
              >
                Guardar
              </vs-button>
            </div>
            <div class="btn-group pl-4 w-full sm:w-1/2 md:w-32 lg:w-32 xl:w-32">
              <vs-button
                class="w-full"
                color="success"
                type="filled"
                icon-pack="fas"
                icon="fa-plus"
                @click="agregarProducto()"
                title="Agrega un medicamento para preparar la solicitud"
              >
                Agregar
              </vs-button>
            </div>

            <div
              v-if="this.OcultarPropiedadesProductos"
              class="btn-group pl-4 w-full"
            >
              <P style="font-size: 20px; color: #0693e3">
                {{ this.HorarioSeleccionado }}</P
              >
            </div>
          </div>
        </div>
        <div class="mb-1 mr-4 val003-container">
          <DxDataGrid
            id="gridSolicitudProductos"
            :data-source="productoPedidos"
            v-bind="DefaultDxGridConfiguration"
            :no-data-text="'Aún no hay productos cargados'"
            height="auto"
            width="auto"
          >
            <DxSearchPanel :visible="false" />
            <DxFilterPanel :visible="true"/>
            <DxPaging :page-size="15" />
            <DxPager :visible="true" />
            <!-- Columna de eliminación -->
            <DxColumn type="buttons" width="50">
              <DxButton name="delete" />
            </DxColumn>
            <DxColumn
              :width="95"
              data-field="Cantidad"
              :visible="true"
              caption="Dosis cantidad"
              :allow-updating="true"
              :alignment="'left'"
            />
            <DxColumn
              :width="450"
              data-field="IdProducto"
              :visible="true"
              caption="Medicamento"
              :allow-editing="false"
            >
              <DxLookup
                :dataSource="ProductosValeDataSource"
                value-expr="ID"
                display-expr="NombreProducto"
              />
            </DxColumn>
            <DxColumn
              :width="200"
              data-field="IdPresentacion"
              :visible="true"
              caption="Dosis unidad"
            >
              <DxLookup
                :width="200"
                :dataSource="editorOptions.Presentacion.dataSource"
                value-expr="Id"
                display-expr="Presentacion"
              />
            </DxColumn>

            <DxColumn
              :width="180"
              data-field="IdViaAdministracion"
              :visible="true"
              caption="Dosis vía"
            >
              <DxLookup
                :width="220"
                :dataSource="editorOptions.ViaAdministracion.dataSource"
                value-expr="Id"
                display-expr="ViaDeAdministracion"
              />
            </DxColumn>

            <DxColumn
              :width="180"
              data-field="IdFrecuenciaAdministracion"
              :visible="true"
              caption="Frecuencia"
            >
              <DxLookup
                :width="200"
                :dataSource="editorOptions.FrecuenciaAdministracion.dataSource"
                value-expr="Id"
                display-expr="FrecuenciaAdministracion"
              />
            </DxColumn>

            <DxColumn
              :width="60"
              data-field="NoDias"
              :visible="true"
              caption="No. dias"
              :alignment="'left'"
            >
            </DxColumn>

            <DxEditing
              :confirm-delete="false"
              :allow-deleting="true"
              mode="row"
              :use-icons="true"
            >
              <DxTexts
                delete-row="¿Eliminar producto?"
                cancel="Cancelar"
                confirm="Aceptar"
              />
            </DxEditing>
          </DxDataGrid>

          <DxSelection mode="single" />

          <!-- <DxRadioGroup> </DxRadioGroup> -->
        </div>
      </DxScrollView>
    </DxPopup>

    <SM-Validar-Pass
      ref="refValidarPass"
      in_titulo="Validación productos no cubiertos"
    />

    <dx-popup
      :visible.sync="popupAplicacion"
      :show-title="true"
      title="Seguimiento aplicación de medicamentos"
      :resizeEnabled="true"
      :wrapper-attr="{class:'main-vale-popup'}"      
    >
      <aplicacionMedicamentos v-bind="$props" ref="refaplicacionMedicamentos" />
    </dx-popup>

    <dx-popup
      :visible.sync="popupHistorialAplicacion"
      :show-title="true"
      title="Historial de aplicación de medicamentos"
      :resizeEnabled="true"
      :wrapper-attr="{class:'main-vale-popup'}"
    >
      <HistorialAplicaciones
        ref="refHistorialAplicaciones"
        @refresh="SeguimientoAplicacionHistorial"
      />
    </dx-popup>
  </div>
</template>
<script>
import {
  DxColumn,
  DxSelection,
  DxSearchPanel,
  DxHeaderFilter,
  DxItem,
  DxLookup,
  DxToolbar,
  DxEditing,
  DxPager,
  DxPaging,
  DxGroupPanel,
  DxGrouping,
  DxTexts,
  DxFilterPanel,
} from "devextreme-vue/data-grid";
import { DefaultDxGridConfiguration } from "../../EXPEDIENTE/data";
//import { DxSelectBox } from "devextreme-vue/select-box";
import { DxPopup, DxToolbarItem } from "devextreme-vue/popup";
import { DxDataGrid } from "devextreme-vue/data-grid";
import CustomStore from "devextreme/data/custom_store";
import DxDropDownBox from "devextreme-vue/drop-down-box";
import "devextreme-vue/radio-group";
import aplicacionMedicamentos from "./VAL004.vue";
import HistorialAplicaciones from "./VAL005.vue";
import DxButton from "devextreme-vue/button";
import {
  DxForm,
  DxSimpleItem,
  // DxGroupItem,
  DxLabel,
} from "devextreme-vue/form";

import { DxItem as ItemForm } from "devextreme-vue/form";

import { DxScrollView } from "devextreme-vue/scroll-view";
// import "devextreme-vue/lookup";
//const popupVisible = ref(false);
//const rowAlternationEnabled = ref(true);
import ExpedienteGridToolBar from "./../../EXPEDIENTE/ExpedienteGridToolBar.vue";
import esMessages from "devextreme/localization/messages/es.json";
import { locale, loadMessages } from "devextreme/localization";
import { CustomDevExtremeEsMessages } from "./../../EXPEDIENTE/data.js";

const closeButtonOptions = {
  text: "Cerrar",
  stylingMode: "outlined",
  type: "normal",
  onClick: () => {
    this.PopupAreas.visible = false;
  },
};
export default {
  components: {
    DxTexts,
    DxLookup,
    DxDataGrid,
    DxToolbar,
    DxColumn,
    DxSelection,
    DxPopup,
    DxToolbarItem,
    DxFilterPanel,
    //DxPosition,
    DxSearchPanel,
    DxHeaderFilter,
    //DxSelectBox,
    DxItem,
    ItemForm,
    //DxRadioGroup,
    //   DxGroupItem,
    DxDropDownBox,
    DxForm,
    DxSimpleItem,
    DxLabel,
    DxEditing,
    Encabezado2: () => import("./VALENCHerencia.vue"),
    aplicacionMedicamentos,
    HistorialAplicaciones,
    ExpedienteGridToolBar,
    DxPager,
    DxPaging,
    DxScrollView,
    DxButton,
    DxGroupPanel,
    DxGrouping,
  },
  methods: {
    Cargar() {
      this.axios
        .post("/app/v1_ValeElectronico/BusquedaAreasHospitales", {})
        .then((resp) => {
          this.ValeDataSource = resp.data.json.map((x /*, index*/) => {
            return {
              //  id: index,
              ID: x.AREA,
              Area: x.nombre,
              Base: x.base,
            };
          });
        });
    },
    convertSiNo(valor) {
      if (valor === "Si") {
        return 1;
      } else if (valor === "No") {
        return 0;
      } else {
        return null;
      }
    },

    llenarradioSolicitudes() {
      this.TipoSolicitudVale = [
        { text: "STAT Farmacia", id: "STF", visible: false },
        { text: "STAT Servicio", id: "STS", visible: false },
        { text: "Tiempo Indefinido", id: "UNI", visible: true },
        { text: "Con Horario", id: "HRS", visible: false },
      ];
      this.ocultaDias = true;
      this.ordenDetalle.TipoSolicitud = {};
    },
    llenarradioSolicitudesB() {
      this.TipoSolicitudVale = [
        { text: "STAT Farmacia", id: "STF", visible: true },
        { text: "STAT Servicio", id: "STS", visible: true },
        { text: "Tiempo Indefinido", id: "UNI", visible: false },
        { text: "Con Horario", id: "HRS", visible: false },
      ];
      this.ordenDetalle.CantidadDias = 1;
      this.ordenDetalle.TipoSolicitud = {};
    },
    llenarradioSolicitudesC() {
      this.TipoSolicitudVale = [
        { text: "STAT Farmacia", id: "STF", visible: false },
        { text: "STAT Servicio", id: "STS", visible: false },
        { text: "Tiempo Indefinido", id: "UNI", visible: false },
        { text: "Con Horario", id: "HRS", visible: false },
      ];
      this.ordenDetalle.CantidadDias = 1;
      this.ordenDetalle.TipoSolicitud = {};
    },

    CargarSolicitudes() {
      this.SolicitudesValeDataSource = [];
      if (this.SerieAdmision === null) {
        this.showValidacion();
        return;
      } else {
        this.axios
          .post("/app/v1_ValeElectronico/ListaMedicamentosVale", {
            SerieAdmision: this.SerieAdmision,
            Admision: this.CodigoAdmision,
          })
          .then((resp) => {
            this.SolicitudesValeDataSource = resp.data.json.map((x, index) => {
              return {
                id: index,
                ID: x.ID,
                CodProducto: x.CodProducto,
                EstadoActual: x.EstadoActual,
                Producto: x.Producto,
                Cantidad: x.Cantidad,
                Presentacion: x.Presentacion,
                ViadeAdministracion: x.ViadeAdministracion,
                Horario: x.Horario,
                Dias: x.Dias,
                CodMedico: x.CodMedico,
                Medico: x.Medico,
                FechaInicio: x.FechaInicio,
                FechaOmision: x.FechaOmision,
                TipoSolicitud: x.TipoSolicitud,
                Colaborador: x.Colaborador,
              };
            });
            this.mensajeActualizacion();
          });
      }
    },

    async BusquedaMedicamentosVale() {
      this.axios
        .post("/app/v1_ValeElectronico/BusquedaProductosVale", {
          // Busqueda: this.ProductoBusqueda,
          Busqueda: "",
          SerieAdmision: this.SerieAdmision,
          Admision: this.CodigoAdmision,
        })
        .then((resp) => {
          this.ProductosValeDataSource = resp.data.json.map((x) => {
            return {
              ID: x.Codigo,
              NombreProducto: x.Nombre,
              Unidad: x.Unidad,
              Descripcion: x.Descripcion,
              Cubierto: x.Cubierto,
              Categoria: x.Categoria,
            };
          });
        });
    },

    //Método para detectar cuando se selecciona una fila de la tabla de filtrado de fecha

    RegresarMenu() {
      this.menuStack.pop();
      let i = this.menuStack[this.menuStack.length - 1];

      if (i === "navigation") {
        this.Title = "Vale eléctronico";
        this.SelectedOption = 0;
        this.menuActual = [];
      } else {
        this.Title = i.title;
        this.SelectedOption = i.id;
        this.menuActual = [];
        if (i.submenu) {
          this.menuActual = i.submenu;
        }
      }
    },
    submit() {
      if (this.RegistrosSeleccion > 0) {
        this.PopupAreas = false;

        this.$vs.notify({
          time: 4000,
          title: "Area seleccionada",
          text: (this.TextoSeleccion = this.AreaProps.Area),
          iconPack: "feather",
          icon: "icon-alert-circle",
          color: "warning",
          position: "top-center",
        });
      }
    },

    showPopup() {
      this.PopupAreas = true;
    },

    showValidacion() {
      this.$vs.notify({
        time: 6000,
        title: "No hay admisión seleccionada",
        text: "No ha ingresado una admisión valida",
        iconPack: "feather",
        icon: "icon-alert-circle",
        color: "warning",
        position: "top-center",
      });
    },

    mensajeActualizacion() {
      this.$vs.notify({
        time: 4000,
        title: "Información actualizada",
        text: "se actualizo la información",
        iconPack: "feather",
        icon: "icon-alert-circle",
        color: "success",
        position: "bottom-center",
      });
    },
    showProductos() {
      if (this.SerieAdmision === null) {
        this.showValidacion();
        return;
      } else {
        this.PopupMedicamentos = true;
      }
    },

    showPopupAplicacion() {
      if (this.SerieAdmision === null) {
        this.showValidacion();
        return;
      } else {
        this.SeguimientoAplicacion();
        this.popupAplicacion = true;
      }
    },
    showPopupAplicacionHistorial() {
      if (this.SerieAdmision === null) {
        this.showValidacion();
        return;
      } else {
        this.SeguimientoAplicacionHistorial();
        this.popupHistorialAplicacion = true;
      }
    },

    onLoadedDataEXP001(arg) {
      this.SerieAdmision = arg.SerieAdmision;
      this.CodigoAdmision = arg.CodigoAdmision;
      this.DatosAdmision.TipoSeguro = arg.TipoSeguro;
      this.DatosAdmision.StatusAdmision = arg.StatusAdmision;
      this.DatosAdmision.Paciente = arg.Nombre + " " + arg.Apellido;
      this.DatosAdmision.AreaHabitacion = arg.AreaHabitacion;
    },

    LimpiarAdmision() {
      this.$refs.refEncabezado2.LimpiarAdmision();

      this.SolicitudesValeDataSource = [];
    },

    Prueba() {
      this.$refs.refaplicacionMedicamentos.SerieAdmision = this.SerieAdmision;
      this.$refs.refaplicacionMedicamentos.CodigoAdmision = this.CodigoAdmision;
      this.$refs.refaplicacionMedicamentos.StatusAdmision =
        this.DatosAdmision.StatusAdmision;
    },

    onGridDoubleClick() {
      this.dropDownOpened = false;
    },

    onSelectionChanged(selectedItems) {
      if (selectedItems.selectedRowsData.length > 0) {
        const primerRegistro = selectedItems.selectedRowsData[0];
        this.NombreProductoBusqueda = primerRegistro.NombreProducto;
        this.ordenDetalle.Producto = primerRegistro.ID;
        this.CategoriaProductoBusqueda = primerRegistro.Categoria;
        this.ordenDetalle.CantidadDias = 1;
        this.ordenDetalle.Cantidad = 1;
        this.ProductoCubierto = primerRegistro.Cubierto;
        this.dropDownOpened = false;
        (this.existenciasProducto = ""), this.MostrarExistencia();

        this.CodigoPropiedad = primerRegistro.ID[0];

        if (this.CodigoPropiedad === "1" || this.CodigoPropiedad === "6") {
          this.OcultarPropiedadesProductos = true;

          // this.llenarradioSolicitudes();
        } else {
          this.OcultarPropiedadesProductos = false;
          this.IdFrecuenciaAdministracionAlterna = 4;
          this.llenarradioSolicitudesB();
        }
      }
    },

    MostrarExistencia() {
      this.axios
        .post("/app/v1_ValeElectronico/ConsultaExistencia", {
          Producto: this.ordenDetalle.Producto,
        })

        .then((resp) => {
          this.existenciasProducto = resp.data.json[0].Existencia;
        })
        .catch((error) => {
          this.existenciasProducto = "Error al obtener existencias";
        });
    },

    CargarHorarios(frecuencia) {
      this.axios
        .post("/app/v1_ValeElectronico/CatalogosVale", {
          Catalogo: "HorarioAplicacion",
          DescripcionHorario: frecuencia,
        })
        .then((resp) => {
          //this.editorOptions.HorarioAplicacion.dataSource = resp.data;
          this.CatalogoHorariosAplicacion = resp.data;

          //this.ordenDetalle.Horario = resp.data[0].HorarioAplicacion;
        });
    },

    agregarElemento() {
      const nuevoElemento = {
        Id: this.siguienteId,
        name: this.ordenDetalle.Presentacion, //this.editorOptions.Presentacion.dataSource.find( x=> x.Id == this.ordenDetalle.Presentacion).Presentacion,
        NoDias: 0,
        IdPresentacion: this.ordenDetalle.Presentacion,
        Cantidad: this.ordenDetalle.Cantidad,
        IdProducto: this.ordenDetalle.Producto,
        IdViaAdministracion: this.ordenDetalle.ViaAdministracion,
        IdFrecuenciaAdministracion: this.ordenDetalle.FrecuenciaAdministracion,
      };

      this.productoPedidos.push(nuevoElemento);

      //  this.validarRegistro(this.productoPedidos, nuevoElemento.IdProducto)

      this.agregarProducto();
      this.siguienteId++;
    },

    guardarDatos() {
      this.datos.push(this.nuevoRegistro);
      // Supongamos que estos son tus datos
      // Asigna los valores del objeto a los campos del dxDataGrid
      this.productoPedidos = this.datos;
    },

    MensajeRegistro(titulo, texto) {
      this.$vs.notify({
        time: 4000,
        title: titulo,
        text: texto,
        iconPack: "feather",
        icon: "icon-alert-circle",
        color: "primary",
        position: "bottom-center",
      });
    },

    obtenerValorSeleccionado() {
      alert(this.valorSeleccionado);
    },
    allowDeleting(e) {
      if (e.row.data.NoEditable === true) {
        return false;
      }
      return true;
    },

    agregarProducto() {
      //

      //Validar que ingresen los datos principales
      if (this.OcultarPropiedadesProductos) {
        if (
          this.ordenDetalle.Presentacion === null ||
          this.ordenDetalle.Cantidad === 0 ||
          this.ordenDetalle.Producto === null ||
          this.ordenDetalle.ViaAdministracion === null ||
          this.ordenDetalle.FrecuenciaAdministracion === null ||
          // this.ordenDetalle.TipoSolicitud === null ||
          (this.ordenDetalle.Horario === null && this.PropiedadStatVisible)
        ) {
          this.$vs.notify({
            time: 6000,
            title: "Alerta",
            text: "Llenar los campos obligatorios.",
            iconPack: "feather",
            icon: "icon-alert-circle",
            color: "warning",
            position: "top-center",
          });
          return;
        }
      }

      if (this.PropiedadStatVisible === false) {
        if (this.ordenDetalle.TipoSolicitud.id === undefined) {
          this.$vs.notify({
            time: 6000,
            title: "No ha llenado todos los campos",
            text: "Seleccione el tipo de solicitud (Stat Farmacia ò Stat Servicio)",
            iconPack: "feather",
            icon: "icon-alert-circle",
            color: "warning",
            position: "top-center",
          });
          return;
        }
      }

      if (
        this.OcultarPropiedadesProductos === false &&
        this.ordenDetalle.TipoSolicitud.id === undefined
      ) {
        this.$vs.notify({
          time: 6000,
          title: "No ha llenado todos los campos",
          text: "Seleccione el tipo de solicitud (Stat Farmacia ò Stat Servicio)",
          iconPack: "feather",
          icon: "icon-alert-circle",
          color: "warning",
          position: "top-center",
        });
        return;
      }

      if (this.OcultarPropiedadesProductos === false) {
        if (this.ordenDetalle.TipoSolicitud === null) {
          this.$vs.notify({
            time: 6000,
            title: "No ha llenado todos los campos",
            text: "Seleccione el tipo de solicitud (Stat Farmacia ò Stat Servicio)",
            iconPack: "feather",
            icon: "icon-alert-circle",
            color: "warning",
            position: "top-center",
          });
          return;
        }
      }

      if (
        this.ordenDetalle.TipoSolicitud &&
        (this.ordenDetalle.TipoSolicitud.id === "STS" ||
          this.ordenDetalle.TipoSolicitud.id === "STF")
      ) {
        if (
          this.ordenDetalle.FrecuenciaAdministracion &&
          this.ordenDetalle.FrecuenciaAdministracion
            .FrecuenciaAdministracion !== "STAT"
        ) {
          this.$vs.notify({
            time: 6000,
            title: "Tipo de solicitud incorrecta",
            text: "Estimado usuario, debe seleccionar, 'frecuencia de administración' como STAT",
            iconPack: "feather",
            icon: "icon-alert-circle",
            color: "warning",
            position: "top-center",
          });
          return;
        }
      }

      if (
        this.OcultarPropiedadesProductos === false &&
        this.ordenDetalle.FrecuenciaAdministracion !== null &&
        this.ordenDetalle.FrecuenciaAdministracion.FrecuenciaAdministracion ===
          "STAT" &&
        this.ordenDetalle.TipoSolicitud.id === "UNI"
      ) {
        this.$vs.notify({
          time: 6000,
          title: "Tipo de solicitud incorrecta",
          text: "Estimado usuario, seleccionar stat Servicio ó Stat farmacia",
          iconPack: "feather",
          icon: "icon-alert-circle",
          color: "warning",
          position: "top-center",
        });
        return;
      }

      //validar que no ingresen stat y unidosis simultaneamente
      if (
        this.OcultarPropiedadesProductos &&
        this.ordenDetalle.FrecuenciaAdministracion.FrecuenciaAdministracion ===
          "STAT" &&
        this.ordenDetalle.TipoSolicitud.id === "UNI"
      ) {
        this.$vs.notify({
          time: 6000,
          title: "Tipo de solicitud incorrecta",
          text: "Estimado usuario, seleccionar stat Servicio ó Stat farmacia",
          iconPack: "feather",
          icon: "icon-alert-circle",
          color: "warning",
          position: "top-center",
        });
        return;
      }

      if (
        this.OcultarPropiedadesProductos === false &&
        this.ordenDetalle.TipoSolicitud.id === "UNI"
      ) {
        this.$vs.notify({
          time: 6000,
          title: "Tipo de solicitud incorrecta",
          text: "Estimado usuario, seleccionar stat Servicio ó Stat farmacia",
          iconPack: "feather",
          icon: "icon-alert-circle",
          color: "warning",
          position: "top-center",
        });
        return;
      }

      //validar los productos que no estan cubiertos
      if (this.ProductoCubierto != "Si") {
        //Validar permisos de usuario para agregar linea

        this.$refs.refValidarPass.iniciar((x) => {
          if (x != null) {
            this.PermisoProductos = x.permisos.find(
              (t) => t == "PRODUCTOS NO CUBIERTOS",
              (this.ordenDetalle.CorporativoAutoriza = x.corporativo)
            );
          }
          if (this.PermisoProductos === "X") {
            this.$vs.notify({
              time: 4000,
              title: "Validación cancelada",
              text: "Se cancelo la validación de permisos, para productos no cubiertos ",
              iconPack: "feather",
              icon: "icon-alert-circle",
              color: "warning",
              position: "top-center",
            });

            return;
          }
          if (this.PermisoProductos != undefined) {
            this.insertarLineaProducto();
          } else {
            this.$vs.notify({
              time: 4000,
              title:
                "!No tiene permisos para solicitar productos no cubiertos!",
              text: "Solicitar permiso para pedir productos no cubiertos ",
              iconPack: "feather",
              icon: "icon-alert-circle",
              color: "warning",
              position: "top-center",
            });

            return;
          }
        });
      } else {
        this.insertarLineaProducto();
      }
    },
    insertarLineaProducto() {
      // Validar si el producto no se repite
      if (
        this.ordenDetalle.TipoSolicitud.id === undefined &&
        this.ordenDetalle.FrecuenciaAdministracion &&
        this.ordenDetalle.FrecuenciaAdministracion.FrecuenciaAdministracion !==
          "STAT"
      ) {
        // Buscar el objeto con id 'HRS' en TipoSolicitudVale
        const tipoSolicitudHRS = this.TipoSolicitudVale.find(
          (solicitud) => solicitud.id === "HRS"
        );
        // Inicializar TipoSolicitud con el objeto encontrado
        this.ordenDetalle.TipoSolicitud = { ...tipoSolicitudHRS };
        tipoSolicitudId = this.ordenDetalle.TipoSolicitud.id;
      }

      //Validar que si el producto es suministro agregarlo como STF}

      const productoId = this.ordenDetalle.Producto;
      let tipoSolicitudId = this.ordenDetalle.TipoSolicitud.id;
      let solicitudActual = true;

      const productoExistente = this.productoPedidos.find(
        (producto) =>
          producto?.IdProducto === productoId &&
          producto?.TipoSolicitud === tipoSolicitudId
      );

      const solicitudUni = this.productoPedidos.find(
        (producto) =>
          producto?.IdProducto === productoId &&
          producto?.TipoSolicitud === "UNI"
      );

      const solicitudStat = this.productoPedidos.find(
        (item) =>
          item?.IdProducto === productoId &&
          ["STF", "STS"].includes(item?.TipoSolicitud)
      );

      let agregarProducto = true;
      let validarAgregar = true;

      if (
        (solicitudUni && tipoSolicitudId === "UNI") ||
        (solicitudStat && ["STF", "STS"].includes(tipoSolicitudId))
      ) {
        solicitudActual = false;
      }

      const productoActivo = this.SolicitudesValeDataSource.find(
        (elemento) =>
          elemento?.CodProducto?.trim() === productoId &&
          elemento?.EstadoActual === "Activo"
      );

      const solicitudUniActivo = this.SolicitudesValeDataSource.find(
        (producto) =>
          producto?.CodProducto === productoId &&
          producto?.TipoSolicitud === "UNI" &&
          producto?.EstadoActual === "Activo"
      );

      const solicitudStatActivo = this.SolicitudesValeDataSource.find(
        (item) =>
          item?.CodProducto === productoId &&
          ["STF", "STS"].includes(item?.TipoSolicitud) &&
          item?.EstadoActual === "Activo"
      );

      if (solicitudUniActivo || solicitudStatActivo & !productoExistente) {
        agregarProducto = true;
      }

      if (solicitudUniActivo && tipoSolicitudId === "UNI") {
        agregarProducto = false;
      }

      if (solicitudUniActivo && solicitudStatActivo) {
        agregarProducto = false;
      }
      if (
        !solicitudUniActivo &&
        !solicitudStatActivo &&
        !solicitudUni &&
        !solicitudStat
      ) {
        validarAgregar = false;
      }

      if ((solicitudUniActivo || solicitudUni) && tipoSolicitudId === "UNI") {
        agregarProducto = false;
        validarAgregar = false;
      }
      if (
        (solicitudStatActivo || solicitudStat) &&
        ["STF", "STS", "HRS"].includes(tipoSolicitudId)
      ) {
        agregarProducto = false;
        validarAgregar = false;
      }
      const statusProducto = productoActivo?.EstadoActual;

      if (
        statusProducto === "Activo" &&
        (agregarProducto === false || solicitudActual === false)
      ) {
        this.$vs.notify({
          time: 6000,
          title: "Alerta",
          text: `El medicamento ${this.NombreProductoBusqueda} se encuentra activo, omítalo en la orden anterior`,
          iconPack: "feather",
          icon: "icon-alert-circle",
          color: "warning",
          position: "top-center",
        });
        return;
      }

      if (
        this.ordenDetalle.FrecuenciaAdministracion === null &&
        !this.OcultarPropiedadesProductos
      ) {
        this.IdFrecuenciaAdministracionAlterna = 0;
      } else if (this.OcultarPropiedadesProductos) {
        this.IdFrecuenciaAdministracionAlterna =
          this.ordenDetalle.FrecuenciaAdministracion.Id;
      }

      if (this.ordenDetalle.TipoSolicitud.length === 0 && !productoExistente) {
        this.$vs.notify({
          time: 6000,
          title: "Alerta",
          text: "Llenar los campos obligatorios.",
          iconPack: "feather",
          icon: "icon-alert-circle",
          color: "warning",
          position: "top-center",
        });
        return;
      }

      const nuevoElemento = {
        Id: this.siguienteId,
        IdPresentacion: this.ordenDetalle.Presentacion,
        Cantidad: this.ordenDetalle.Cantidad,
        IdProducto: productoId,
        IdViaAdministracion: this.ordenDetalle.ViaAdministracion,
        IdFrecuenciaAdministracion: this.IdFrecuenciaAdministracionAlterna,
        NoDias: this.ordenDetalle.CantidadDias,
        HorarioAplicacion: this.ordenDetalle.Horario,
        TipoSolicitud: tipoSolicitudId,
        CorporativoAutoriza: this.ordenDetalle.CorporativoAutoriza,
        Categoria: this.CategoriaProductoBusqueda,
        serieAdmision: this.SerieAdmision,
        Admision: this.CodigoAdmision,
        Cubierto: this.convertSiNo(this.ProductoCubierto),
      };
      this.PermisoProductos = "X";

      if (validarAgregar && agregarProducto) {
        this.$vs.dialog({
          type: "confirm",
          color: "#ed8c72",
          title: "¡ADVERTENCIA!",
          acceptText: "Si",
          cancelText: "No",
          text: `El medicamento se encuentra actualmente activo ¿desea agregarlo?`,
          buttonCancel: "border",
          clientWidth: 100,
          accept: () => this.agregarNuevoProducto(nuevoElemento),
          cancel: () => {
            agregarProducto = true;
          },
        });
      } else if (productoExistente || !agregarProducto) {
        this.$vs.notify({
          time: 4000,
          title: "ALERTA",
          text: "El medicamento se encuentra actualmente activo, omita en orden anterior",
          iconPack: "feather",
          icon: "icon-alert-circle",
          color: "warning",
          position: "top-center",
        });
      } else {
        this.agregarNuevoProducto(nuevoElemento);
      }
    },

    agregarNuevoProducto(nuevoElemento) {
      if (!this.OcultarPropiedadesProductos) {
        nuevoElemento.IdPresentacion = 13;
        nuevoElemento.IdViaAdministracion = 4;
        nuevoElemento.Horario = 28;
        nuevoElemento.IdFrecuenciaAdministracion = 12;
      }

      this.productoPedidos.push(nuevoElemento);
      this.productoPedidos.sort((a, b) => b.Id - a.Id);

      this.$refs["dataGrid"].instance.deselectAll();
      this.$refs["dataGrid"].instance.clearFilter();

      this.siguienteId++;

      this.ordenDetalle.TipoSolicitud = [];

      this.$vs.notify({
        color: "success",
        title: "Registro de productos",
        text: `Se agregó un nuevo producto a la orden ${this.NombreProductoBusqueda}`,
        position: "bottom-center",
      });
    },
    generarSolicitu() {
      if (this.productoPedidos.length === 0) {
        this.$vs.notify({
          time: 4000,
          color: "#B71C1C",
          title: "Alerta",
          text: "No hay productos para generar solicitud",
          iconPack: "feather",
          icon: "icon-alert-circle",
          position: "top-center",
        });
        return;
      } else if (this.productoPedidos.length > 0) {
        this.CargarSolicitudes();
        let postData = {
          SerieAdmision: this.SerieAdmision,
          Admision: this.CodigoAdmision,
          xml: this.productoPedidos,
        };
        this.axios
          .post("/app/v1_ValeElectronico/InsertMedicamentosVale", {
            ...postData,
          })
          .then((resp) => {
            if (resp.data.codigo == 0) {
              this.productoPedidos = [];

              let objmsg = {
                SerieAdmision: this.SerieAdmision,
                CodigoAdmision: this.Admision,
              };

              this.$emit("onSaved", objmsg);

              this.CargarSolicitudes();
              this.PopupMedicamentos = false;
              this.ordenDetalle.Presentacion = null;
              this.ordenDetalle.Cantidad = 1;
              this.ordenDetalle.Producto = null;
              this.ordenDetalle.ViaAdministracion = null;
              this.ordenDetalle.FrecuenciaAdministracion = null;
              this.NombreProductoBusqueda = "";
            }
          });
      }
    },

    onPopupClose() {
      // Lógica para manejar el cierre del popup
      this.ordenDetalle.Presentacion = null;
      this.OcultarPropiedadesProductos = true;
      this.ordenDetalle.Cantidad = 1;
      this.ordenDetalle.Producto = null;
      this.existenciasProducto = "";

      this.ordenDetalle.ViaAdministracion = null;
      this.ordenDetalle.FrecuenciaAdministracion = null;
      this.ordenDetalle.TipoSolicitud = [];
      this.NombreProductoBusqueda = "";
      this.$refs["dataGrid"].instance.deselectAll();
      this.$refs["dataGrid"].instance.clearFilter();
      if (this.HorarioVisible === true) {
        this.llenarradioSolicitudes();
      } else {
        this.llenarradioSolicitudesB();
      }
      return;
    },

    onCellPrepared(e) {

      if (e.rowType === "data" && e.column.dataField === "EstadoActual") {
        if (e.data.EstadoActual === "Omitido") {
       
          e.cellElement.style.cssText = "background-color: #c0dcc0;";
          
        }
      }
    },
    onRowPrepared(e) {
      if (e.rowType === "data" && e.data.EstadoActual === "Activo") {
        e.rowElement.style.backgroundColor = "#f0f8ff";
      }
      
    },

    CambiarEstado(e) {
      const rowIndex = e.rowIndex;
      const dataField = "EstadoActual";
      const value = "Omitido";
      const productoExistente = this.ArrayEstados.find(
        (producto) => producto.Id === e.data.ID
      );

      const nuevoElemento = {
        Id: e.data.ID,
        CodigoProducto: e.data.CodProducto,
      };

      if (
        productoExistente === undefined &&
        e.data.EstadoActual !== "Omitido"
      ) {
        this.ArrayEstados.push(nuevoElemento);
      }

      e.component.cellValue(rowIndex, dataField, value);
    },
    Omitir() {
      if (this.ArrayEstados.length === 0) {
        this.$vs.notify({
          time: 4000,
          title: "Alerta",
          text: "No hay productos para omitir",
          iconPack: "feather",
          icon: "icon-alert-circle",
          color: "warning",
          position: "top-center",
        });
        return;
      } else if (this.ArrayEstados.length > 0) {
        this.axios
          .post("/app/v1_ValeElectronico/OmitirMedicamentos", {
            xml: this.ArrayEstados,
          })
          .then((resp) => {
            if (resp.data.codigo == 0) {
              this.ArrayEstados = [];

              this.$emit("onSaved");

              this.CargarSolicitudes();
            }
          });
      }
    },
    SeguimientoAplicacion() {
      const aplicacion = this.$refs.refaplicacionMedicamentos;
      aplicacion.CargarMedicamentos();
    },
    SeguimientoAplicacionHistorial() {
      const aplicacion = this.$refs.refHistorialAplicaciones;
      aplicacion.CargarHistorial();
    },
    clearSearchPanel() {
      const dataGrid = this.$refs.dataGrid.instance;
      dataGrid.clearFilter("search");
    },
    validarHabilitarOpcion() {
      this.axios
        .post("/app/v1_ValeElectronico/HabilitarFuncionalidad", {
          Hospital: this.$store.state.sesion.sesion_sucursal,
          Funcion: "Horario inicio",
          Modulo: "Vale",
        })
        .then((resp) => {
          if (resp.data.json && resp.data.json.length > 0) {
            this.HorarioVisible = resp.data.json[0].visible;
            this.llenarradioSolicitudes();
          } else {
            this.HorarioVisible = false;
            this.llenarradioSolicitudesB();
          }
        })
        .catch((error) => {
          this.HorarioVisible = false;
        });
    },

    seleccionarHorario(horario) {
      const horarioSeleccionado =
        this.editorOptions.HorarioAplicacion.dataSource.find(
          (item) => item.HorarioAplicacion === horario
        );
      if (horarioSeleccionado) {
        this.ordenDetalle.Horario = horarioSeleccionado.Id;
      } else {
      }
    },
    updateTime() {
      const now = new Date();
      const hours = String(now.getHours()).padStart(2, "0");
      this.horaActual = `${hours}:${"00"}`;
    },
    customizeColumns(columns) {
      columns.forEach((column) => {
        if (column.dataField === "NombreProducto") {
          column.calculateFilterExpression = (
            filterValue,
            selectedFilterOperation
          ) => {
            const terms = filterValue.split(" ");
            return terms.map((term) => ["NombreProducto", "contains", term]);
          };
        }
      });
    },

    getColorClass(color) {
      switch (color) {
        case "SALUD SIEMPRE":
          return "salud-siempre";
        case "PRIVADO":
          return "privado";
        case "SEGURO":
          return "seguro";
        case "vacio":
          return "vacio";
        default:
          return "";
      }
    },

    
  },

  data() {
    return {
      //datos del DXForm
      ocultaDias: true,
      OcultarPropiedadesProductos: true,
      ProductoOcultar: [1, 6],
      CodigoPropiedad: 0,
      mostrarDiv: true,
      SerieAdmision: null,
      CodigoAdmision: null,
      popupAplicacion: false,
      popupHistorialAplicacion: false,
      GridSinFiltro: [],
      xml: null,
      ArrayEstados: [],
      valorSeleccionado: "",
      PermisoProductos: "X",
      TipoSolicitudMedicamento: "",
      CodigoHorario: "",
      ordenDetalle: {
        Cantidad: 1,
        Producto: null,
        Presentacion: null,
        ViaAdministracion: null,
        FrecuenciaAdministracion: null,
        Horario: null,
        InicioAplicacion: null,
        TipoSolicitud: null, //stat tiempo indef
        CantidadDias: 1,
        CorporativoAutoriza: null,
        Categoria: null,
      },
      horaActual: "",
      siguienteId: 0,
      CatalogoHorariosAplicacion: [
        { desc: "STAT Farmacia", value: "STF" },
        { desc: "STAT Servicio", value: "STS" },
        { desc: "Tiempo Indefinido", value: "UNI" },
      ],
      estatusMedicamentos: [
        { id: "O", text: "Omitido" },
        { id: "A", text: "Activo" },
      ],
      CatalogoHorarios: [],
      HorarioSeleccionado: "",

      TipoSolicitudVale: null,
      ElementoSolicitud: null,
      ValorSolicitud: null,
      selectedItem: null,
      selectedRowsData: [],
      PresentacionDatasource: [],
      displayValue: "",
      gridVisible: true,
      dropDownOpened: false,

      editorOptions: {
        Presentacion: {
          dataSource: [],
          displayExpr: "Presentacion",
          valueExpr: "Id",
          searchEnabled: true,
        },
        ViaAdministracion: {
          dataSource: [],
          displayExpr: "ViaDeAdministracion",
          valueExpr: "Id",
          searchEnabled: true,
        },
        FrecuenciaAdministracion: {
          dataSource: [],
          searchEnabled: true,
          displayExpr: (item) => {
            if (item) {
              return item.Id, item.FrecuenciaAdministracion;
            }
          }, //"FrecuenciaAdministracion",
          valueExpr: (x) => x,
        },
        HorarioAplicacion: {
          dataSource: [],
          displayExpr: "HorarioAplicacion",
          valueExpr: "Id",
          searchEnabled: true,
          //  value: 160  // Valor por defecto
        },
      },

      editorOptionsHoraVis: {
        dataSource: [],
        displayExpr: "HoraVis",
        valueExpr: "Id",
        searchEnabled: true,
      },
      //dropDownBoxConfig,
      Horarios: [], //concatenacion horarios para aplicación
      NombreProductoSeleccion: "",
      PopupAreas: false,
      PopupMedicamentos: false,
      DatosAdmision: {
        StatusAdmision: "",
        TipoSeguro: "",
        Paciente: "",
        AreaHabitacion: "",
      },
      BusquedaProducto: "",
      productoPedidos: [],
      productovale: [],
      AreaSolicita: [],
      closeButtonOptions,
      AreaProps: [],
      TextoSeleccion: "Seleccione área",
      DefaultDxGridConfiguration,
      RegistrosSeleccion: 0,
      tipoBuquedaPac: "TODO3",
      NombreProductoBusqueda: "", //nombre del producto a cargar
      CodigoProductoBusqueda: 0, //codigo del producto a cargar
      CategoriaProductoBusqueda: "0", //Categoria del producto buscado
      IdFrecuenciaAdministracionAlterna: 0,
      ProductoCubierto: "",
      Cantidad: 0,
      HorarioVisible: false,
      PropiedadStatVisible: true,
      existenciasProducto: "",

      ValeDataSource: new CustomStore({
        key: "ID",
        load: () => {
          return this.AreaSolicita;
        },
        insert: () => {},
      }),
      SolicitudesValeDataSource: new CustomStore({
        key: "ID",
        load: () => {
          return this.productoPedidos;
        },
      }),
      ProductosValeDataSource: new CustomStore({
        key: "ID",
        load: () => {
          return this.productovale;
        },
      }),

      CambiarColor(e) {
        const rowIndex = e.rowIndex;
        const dataField = "EstadoActual";
        const value = "O";
        e.component.cellValue(rowIndex, dataField, value);
        const rowElement = e.component.getRowElement(rowIndex);
        rowElement.element.style("background:", "lightblue");
      },
    };
  },
  created() {
    loadMessages(esMessages);
    loadMessages({
      es: CustomDevExtremeEsMessages,
    });
    locale(navigator.language);
  },
  mounted() {
    this.axios
      .post("/app/v1_ValeElectronico/CatalogosVale", {
        Catalogo: "Presentacion",
      })
      .then((resp) => {
        this.editorOptions.Presentacion.dataSource = resp.data;
      });

    this.axios
      .post("/app/v1_ValeElectronico/CatalogosVale", {
        Catalogo: "ViaAdministracion",
      })
      .then((resp) => {
        this.editorOptions.ViaAdministracion.dataSource = resp.data;
        //comentarios
        //  this.ordenDetalle.ViaAdministracion = resp.data[0].ViaDeAdministracion;
      });
    this.axios
      .post("/app/v1_ValeElectronico/CatalogosVale", {
        Catalogo: "FrecuenciaAdministracion",
      })
      .then((resp) => {
        this.editorOptions.FrecuenciaAdministracion.dataSource = resp.data;
      });
    this.axios
      .post("/app/v1_ValeElectronico/CatalogosVale", {
        Catalogo: "HorariosAplicacion",
      })
      .then((resp) => {
        this.CatalogoHorarios = resp.data;
        //  this.ordenDetalle.ViaAdministracion = resp.data[0].ViaDeAdministracion;
      });

    this.CargarHorarios();
    this.llenarradioSolicitudes();
    this.validarHabilitarOpcion();
  },
  computed: {
    HorariosComputed() {
      return this.CatalogoHorariosAplicacion.filter(
        (x) => x.Nombre == this.NombreProductoSeleccion
      );
    },
    From: function () {
      return this.$refs["refForm"].instance;
    },
    isInputDisabled() {
      if (this.ElementoSolicitud === "UNI") {
        return true;
      } else {
        return false;
      }
    },
  },
  watch: {
    CodigoAdmision(newval) {
      if (newval) {
        if (this.DatosAdmision.StatusAdmision != "A") {
          (this.mostrarDiv = false),
            this.$vs.notify({
              time: 4000,
              title: "Admisión no valida",
              text: "La admisión no se encuentra activa, no se pueden agregar productos",
              iconPack: "feather",
              icon: "icon-alert-circle",
              color: "warning",
              position: "top-center",
            });

          return;
        }
        (this.mostrarDiv = true), this.CargarSolicitudes();
        this.$refs.refaplicacionMedicamentos.SerieAdmision = this.SerieAdmision;
        this.$refs.refaplicacionMedicamentos.CodigoAdmision =
          this.CodigoAdmision;
        this.$refs.refaplicacionMedicamentos.StatusAdmision =
          this.DatosAdmision.StatusAdmision;
        this.$refs.refaplicacionMedicamentos.Paciente =
          this.DatosAdmision.Paciente;

        this.$refs.refHistorialAplicaciones.SerieAdmision = this.SerieAdmision;
        this.$refs.refHistorialAplicaciones.CodigoAdmision =
          this.CodigoAdmision;
        this.$refs.refHistorialAplicaciones.StatusAdmision =
          this.DatosAdmision.StatusAdmision;
        this.$refs.refHistorialAplicaciones.Paciente =
          this.DatosAdmision.Paciente;
        this.BusquedaMedicamentosVale();
      } else {
        this.SolicitudesValeDataSource = [];
        this.productoPedidos = [];
        this.$refs.refHistorialAplicaciones.SeguimientoProductosDatasource = [];
        this.$refs.refaplicacionMedicamentos.AplicacionMedicamentos = [];

        this.$refs.refaplicacionMedicamentos.SerieAdmision = null;
        this.$refs.refaplicacionMedicamentos.CodigoAdmision = null;
        this.SerieAdmision = null;
        this.CodigoAdmision = null;
        this.DatosAdmision.Paciente = "";
        this.DatosAdmision.AreaHabitacion = "";
        this.$refs.refHistorialAplicaciones.SerieAdmision = null;
        this.$refs.refHistorialAplicaciones.CodigoAdmision = null;
      }
    },
    "ordenDetalle.FrecuenciaAdministracion.FrecuenciaAdministracion"(newVal) {
      this.editorOptions.HorarioAplicacion.dataSource =
        this.CatalogoHorariosAplicacion.filter((x) => x.Nombre === newVal);
      this.ordenDetalle.Horario = null;
      this.HorarioSeleccionado = "";
      this.updateTime();
      this.seleccionarHorario(this.horaActual);

      if (newVal === "STAT") {
        this.PropiedadStatVisible = false;
      } else if (newVal !== "STAT") {
        this.PropiedadStatVisible = true;
      }

      if (!this.PropiedadStatVisible) {
        this.llenarradioSolicitudesB();
      } else if (this.PropiedadStatVisible && this.HorarioVisible) {
        this.llenarradioSolicitudes();
      } else if (this.PropiedadStatVisible && !this.HorarioVisible) {
        this.llenarradioSolicitudesC();
      }

      return this.$refs["refForm"].instance.repaint();
    },
    "ordenDetalle.TipoSolicitud"(newVal) {
      this.TipoSolicitudMedicamento = newVal;
      this.ordenDetalle.TipoSolicitud = newVal;
      if (newVal.id === "UNI") {
        this.ordenDetalle.CantidadDias = 100;
        this.ocultaDias = false;
      } else {
        this.ordenDetalle.CantidadDias = 1;
        this.ocultaDias = true;
      }
    },
    ElementoSolicitud(newVal) {
      this.ElementoSolicitud = newVal.id;
    },
    "ordenDetalle.Horario"(newVal) {
      this.CodigoHorario = newVal;
      const DesHorarios = this.CatalogoHorarios.filter(
        (x) => x.Id == this.CodigoHorario
      );
      if (DesHorarios.length > 0) {
        this.HorarioSeleccionado = DesHorarios[0].Descripcion;
      }
    },
    "$store.state.sesion.sesion_sucursal"() {
      validarHabilitarOpcion();
    },
  },
};
</script>
<style>
.fm-72 {
  width: 384px;
}
.val003-container .dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed)
  > td {
  background-color: #00ccff !important;
  color: black !important;
}

.val003-container .dx-datagrid-rowsview .dx-row.dx-data-row.dx-column-lines.dx-state-hover > td {
  background-color: #79e4ff !important;
  color: black !important;
  font-size: 16px;
}

.val003-container .dx-datagrid-headers {
  background-color: linen !important;
  color: black !important;
  font-weight: bold;
}

/* #valeConatainer #edadpac {
  display: none;
}

 */

 
.main-vale-popup .dx-popup-title {
    background-color: #f4eade !important;
    color: #2f496e !important;
}

div.my-stickyVal {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 999999;
  background-color: linen;
  border: rgb(179, 179, 179) solid 1px;
  padding: 5px;
  /*background-color: yellow;
  padding: 50px;
  font-size: 20px;*/
}

.val003-container .salud-siempre {
  background-color: #ffa500 !important;
}

.val003-container .privado {
  background-color: #c0dcc0 !important;
}

.val003-container .seguro {
  background-color: #ffff00 !important;
}

.vacio {
  background-color: #fffdd0 !important;
}
.text-red {
  color: red;
}

 .table-responsiveVale {
  width: 100%;
  overflow-x: auto;
}

table {
  
  border-collapse: collapse;
}

</style>
<style scoped>
.main-vale {
  display: grid;
  height: calc(100vh - 49px - 67px);
  grid-template-areas: "encabezado encabezado" "opciones contenido";
  grid-template-columns: 0fr 1fr;
  grid-template-rows: 85px 1fr;
  margin: 0;
  padding: 0;
  right: 0;
  background-color: rgb(255, 255, 255);
  position: fixed;
  left: 80px;
  width: calc(100%-80px);
  min-width: 400px;
}

.encabezado {
  grid-area: encabezado;
  overflow-y: hidden;
  font-size: small;
  max-height: 85px;
  /* background-color: #d0e1f9; */
  background: rgb(208, 225, 249);
  background: linear-gradient(
    90deg,
    rgba(208, 225, 249, 1) 67%,
    rgba(119, 175, 255, 1) 94%,
    rgba(2, 0, 36, 1) 100%
  );
  color: #2f496e;
}

.contenido {
  grid-area: Contenido;
  overflow-y: auto;
}
</style>