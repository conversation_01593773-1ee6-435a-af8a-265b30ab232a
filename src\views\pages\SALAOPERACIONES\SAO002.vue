<template>
    <div>
        <vx-card :title="`Procedimientos - ${selectHospital.NombreSuc}`">
            <div class="flex flex-wrap mb-1">
                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1" v-if="permisos.crear">
                    <vs-button class="w-full mr-1 mt-5" title="Crear Procedimiento" color="success" icon-pack="fas"
                        icon="fa-plus" @click="Otros().AbrirVentana()">
                        Crear
                    </vs-button>
                </div>
                <div v-if="permisos.cambiarHospital" class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                    <label style="font-size:12px">Hospital</label>
                    <v-select v-model="selectHospital" label="NombreSuc" :options="arrayHospitales"
                        @input="Otros().changeHospital(false)" :clearable="false" />
                </div>
                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                    <label style="font-size:12px">Quirofanos</label>
                    <v-select multiple v-model="selectQuirofano" label="text" :options="arrayQuirofanos"
                        @input="Otros().changeQuirofano()" />
                </div>
                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                    <SM-Buscar label="Busqueda Procedimiento" api="app/salaoperaciones/ListarProcedimientosBusqueda"
                        :api_filtro="{ Hospital: this.HospitalesAsignados }"
                        :api_campos="['#IdProcedimiento', 'Fecha', 'NombreMedico', 'ApellidoMedico', 'Cirugía', 'Paciente', 'Admision']"
                        :api_titulos="['#IdProcedimiento', 'Fecha', 'Nombre Médico', 'Apellido Médico', 'Cirugía', 'Paciente', 'Admision']"
                        api_campo_respuesta="IdProcedimiento" :callback_buscar="Consulta().DetalleProcedimiento"
                        :callback_nuevo="null" :callback_editar="null" :callback_cancelar="null" :disabled_texto="false"
                        :disabled_editar="false" />
                </div>
            </div>
        </vx-card>
        <vs-popup fullscreen :title="tituloVentana" v-show="ventanaActiva" :active.sync="ventanaActiva">
            <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                <form method="post" @submit.prevent="handleSubmit(Agregar().AgregarProcedimiento(invalid))">
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                            <ValidationProvider name="Fecha" rules="required" class="required">
                                <label style="font-size:12px">Fecha</label>
                                <flat-pickr class="w-full" v-model="info.Fecha" :config="configFromdateTimePicker"
                                    :disabled="esEdicion" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <ValidationProvider name="HoraFin" rules="required" class="required">
                                <label style="font-size:12px">Hora Inicio</label>
                                <flat-pickr class="w-full" :config="configTimePicker" v-model="info.HoraInicio"
                                    :disabled="esEdicion" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <ValidationProvider name="HoraFin" rules="required" class="required">
                                <label style="font-size:12px">Hora Fin</label>
                                <flat-pickr class="w-full" :config="configTimePicker" v-model="info.HoraFin"
                                    :disabled="esEdicion" />
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">

                            <ValidationProvider name="Hospital" rules="required" class="required">
                                <label style="font-size:12px">Hospital</label>
                                <v-select v-model="selectHospitalAux" label="NombreSuc" :options="arrayHospitales"
                                    :clearable="false" @input="Otros().changeHospital(true)" required
                                    :disabled="esEdicion" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <ValidationProvider name="TipoProgramacion" rules="required" class="required">
                                <label style="font-size:12px">Tipo Programación</label>
                                <v-select :options="arregloTipoProgramacion" v-model="info.TipoProgramacion"
                                    :disabled="esEdicion" :clearable="false" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <vs-button class="w-full mr-1 mt-5" icon-pack="fas" icon="fa-calendar"
                                @click="Agregar().AgregarProcedimiento(invalid)">
                                Reprogramar
                            </vs-button>
                        </div>
                    </div>
                    <vs-divider position="center">Datos del paciente</vs-divider>
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5  p-1">
                            <ValidationProvider name="Nombre" rules="alpha_spaces|required|max:100" v-slot="{ errors }"
                                class="required">
                                <label style="font-size:12px">Nombre</label>
                                <vs-input type="text" class="w-full" v-model="info.Nombre" :disabled="esReprogramacion"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    required />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5  p-1">
                            <ValidationProvider name="Edad" rules="required|numero_entero|max:3" v-slot="{ errors }"
                                class="required">
                                <label style="font-size:12px">Edad (años)</label>
                                <vs-input type="number" class="w-full" v-model="info.Edad" :disabled="esReprogramacion"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    required />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5  p-1">
                            <label style="font-size:12px">Tipo Paciente</label>
                            <vs-input type="text" class="w-full" v-model="info.TipoPaciente" disabled="true" />
                        </div>
                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5  p-1">
                            <ValidationProvider name="Codigo de área" rules="required" class="required">
                                <label style="font-size:12px">Codigo de área</label>
                                <v-select v-model="selectPaisAux" label="text" :options="arrayPaises"
                                    :clearable="false" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5  p-1">
                            <ValidationProvider name="Telefono" rules="required|numero_entero|min:5|max:12"
                                v-slot="{ errors }" class="required">
                                <label style="font-size:12px">Télefono</label>
                                <vs-input type="text" class="w-full" v-model="info.Telefono"
                                    :disabled="esReprogramacion" :danger="errors.length > 0"
                                    :danger-text="(errors.length > 0) ? errors[0] : null" required />
                            </ValidationProvider>
                        </div>
                    </div>
                    <vs-divider position="left">Datos admisión</vs-divider>
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                            <label style="font-size:12px">Serie</label>
                            <vs-input type="text" class="w-full" v-model="info.Serie" disabled="true" />
                        </div>
                        <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                            <SM-Buscar v-model="info.Admision" label="Admisión" api="app/salaoperaciones/BuscarAdmision"
                                :api_campos="['Entrada', 'Serie', 'Admision', 'Nombre']"
                                :api_titulos="['#Entrada', 'Serie', 'Admision', 'Nombre']"
                                :api_filtro="{ Serie: detalleProcedimiento.Serie }" api_campo_respuesta="Admision"
                                :callback_buscar="Consulta().BusquedaAdmision" :callback_nuevo="null"
                                :callback_editar="null" :callback_cancelar="null" :disabled_texto="esReprogramacion"
                                :disabled_busqueda="esReprogramacion" :disabled_editar="false" />
                        </div>
                        <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                            <label style="font-size:12px">Habitacion</label>
                            <vs-input type="text" class="w-full" v-model="info.Habitacion" disabled="true" />
                        </div>
                        <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                            <ValidationProvider name="Quirofano" rules="required" class="required">
                                <label style="font-size:12px">Quirófano</label>
                                <v-select v-model="selectQuirofanoAux" label="text" :options="arrayQuirofanosAux"
                                    :clearable="false" />
                            </ValidationProvider>
                        </div>
                    </div>
                    <vs-divider position="center">Datos procedimiento quirúrgico</vs-divider>
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                            <vs-divider position="left"><label
                                    style="color: #E74C3C; font-weight: bold; font-size:125%">*
                                </label>Cirugia
                            </vs-divider>
                            <SM-Buscar v-model="busqueda.IdCirugia" label="Código Cirugia"
                                api="app/salaoperaciones/ListarCirugiasBusqueda"
                                :api_campos="['IdCirugia', 'Nombre', 'TiempoPromedio']"
                                :api_titulos="['Codigo', 'Nombre', 'TiempoPromedio']" api_campo_respuesta="Codigo"
                                :callback_buscar="Consulta('/app/salaoperaciones/BuscarCirugia', 'IdCirugia', 'arregloCirugias', 'Esta cirugia ya se encuentra en la lista').ConsultaItem"
                                :callback_nuevo="null" :callback_editar="null" :callback_cancelar="null"
                                :disabled_texto="esReprogramacion" :disabled_busqueda="esReprogramacion"
                                :disabled_editar="false" :api_preload="true" />
                            <vs-table2 max-items="10" pagination :data="arregloCirugias" tooltip>
                                <template slot="thead">
                                    <th order="Nombre">Nombre Cirugia</th>
                                    <th order="Tiempo Promedio">Tiempo Promedio</th>
                                    <th></th>
                                </template>
                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td2>
                                            {{ tr.Nombre }}
                                        </vs-td2>
                                        <vs-td2>
                                            {{ tr.TiempoPromedio }}
                                        </vs-td2>
                                        <vs-td2 width="50px" noTooltip>
                                            <vs-button title="Eliminar" color="danger" size="small" icon-pack="fas"
                                                icon="fa-trash" class="mr-1" style="display:inline-block"
                                                :disabled="esReprogramacion"
                                                @click="Eliminar().eliminarElemento(tr.Codigo, 'arregloCirugias', tr, '/app/salaoperaciones/DarBajaCirugiaProcedimiento', 'IdProcedimientoCirugia')">
                                            </vs-button>
                                        </vs-td2>
                                    </tr>
                                </template>
                            </vs-table2>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                            <vs-divider position="left">Partes Anatómicas</vs-divider>
                            <SM-Buscar v-model="busqueda.IdParteAnatomica" label="Código Parte Anatómica"
                                api="app/salaoperaciones/ListarPartesAnatomicasBusqueda"
                                :api_campos="['IdParteAnatomica', 'Nombre']" :api_titulos="['Codigo', 'Nombre']"
                                api_campo_respuesta="Codigo"
                                :callback_buscar="Consulta('/app/salaoperaciones/BuscarPartesAnatomicas', 'IdParteAnatomica', 'arregloPartesAnatomicas', 'Esta parte anatómica ya se encuentra en la lista').ConsultaItem"
                                :callback_nuevo="null" :callback_editar="null" :callback_cancelar="null"
                                :disabled_texto="esReprogramacion" :disabled_busqueda="esReprogramacion"
                                :disabled_editar="false" />
                            <vs-table2 max-items="10" pagination :data="arregloPartesAnatomicas" tooltip>
                                <template slot="thead">
                                    <th order="Nombre">Nombre Parte Anatómica</th>
                                    <th></th>
                                </template>
                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td2>
                                            {{ tr.Nombre }}
                                        </vs-td2>
                                        <vs-td2 width="50px" noTooltip>
                                            <vs-button title="Eliminar" color="danger" size="small" icon-pack="fas"
                                                icon="fa-trash" class="mr-1" style="display:inline-block"
                                                :disabled="esReprogramacion"
                                                @click="Eliminar().eliminarElemento(tr.Codigo, 'arregloPartesAnatomicas', tr, '/app/salaoperaciones/DarBajaParteProcedimiento', 'IdProcedimientosPartes')">
                                            </vs-button>
                                        </vs-td2>
                                    </tr>
                                </template>
                            </vs-table2>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                            <vs-divider position="left"><label
                                    style="color: #E74C3C; font-weight: bold; font-size:125%">*
                                </label>Técnicas
                            </vs-divider>
                            <SM-Buscar v-model="busqueda.IdTecnicaCirugia" label="Código Técnicas"
                                api="app/salaoperaciones/ListarTecnicasBusqueda"
                                :api_campos="['IdTecnicaCirugia', 'Nombre']" :api_titulos="['Codigo', 'Nombre']"
                                api_campo_respuesta="Codigo" :api_preload="true"
                                :callback_buscar="Consulta('/app/salaoperaciones/BuscarTecnica', 'IdTecnicaCirugia', 'arregloTecnicas', 'Esta técnica ya se encuentra en la lista').ConsultaItem"
                                :callback_nuevo="null" :callback_editar="null" :callback_cancelar="null"
                                :disabled_texto="esReprogramacion" :disabled_busqueda="esReprogramacion"
                                :disabled_editar="false" />
                            <vs-table2 max-items="10" pagination :data="arregloTecnicas" tooltip>
                                <template slot="thead">
                                    <th order="Nombre">Nombre Técnicas</th>
                                    <th></th>
                                </template>
                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td2>
                                            {{ tr.Nombre }}
                                        </vs-td2>
                                        <vs-td2 width="50px" noTooltip>
                                            <vs-button title="Eliminar" color="danger" size="small" icon-pack="fas"
                                                icon="fa-trash" class="mr-1" style="display:inline-block"
                                                :disabled="esReprogramacion"
                                                @click="Eliminar().eliminarElemento(tr.Codigo, 'arregloTecnicas', tr, '/app/salaoperaciones/DarBajaTecnicaProcedimiento', 'IdProcedimientosTecnica')">
                                            </vs-button>
                                        </vs-td2>
                                    </tr>
                                </template>
                            </vs-table2>
                        </div>
                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                            <vs-divider position="left"><label
                                    style="color: #E74C3C; font-weight: bold; font-size:125%">*
                                </label>Médicos
                                Trantantes</vs-divider>
                            <SM-Buscar v-model="busqueda.CodigoMedico" label="Código Medico"
                                api="app/salaoperaciones/ListarAjenosBusqueda"
                                :api_campos="['Codigo', 'Nombre', 'Apellido', 'Celular', 'Tipo', 'Especialidad']"
                                :api_titulos="['Codigo', 'Nombre', 'Apellido', 'Celular', 'Tipo', 'Especialidad']"
                                api_campo_respuesta="Codigo"
                                :callback_buscar="Consulta('/app/salaoperaciones/BuscarAjeno', 'Codigo', 'arregloMedicos', 'Este médico ya se encuentra en la lista', true).ConsultaItem"
                                :callback_nuevo="null" :callback_editar="null" :callback_cancelar="null"
                                :disabled_texto="esReprogramacion" :disabled_busqueda="esReprogramacion"
                                :disabled_editar="false" />
                            <vs-table2 max-items="10" pagination :data="arregloMedicos" tooltip>
                                <template slot="thead">
                                    <th order="Nombre">Nombre</th>
                                    <th order="Apellido">Apellido</th>
                                    <th order="Celular">Celular</th>
                                    <th></th>
                                </template>
                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td2>
                                            {{ tr.Nombre }}
                                        </vs-td2>
                                        <vs-td2>
                                            {{ tr.Apellido }}
                                        </vs-td2>
                                        <vs-td2>
                                            {{ tr.Celular }}
                                        </vs-td2>
                                        <vs-td2 width="50px" noTooltip>
                                            <vs-button title="Eliminar" color="danger" size="small" icon-pack="fas"
                                                icon="fa-trash" class="mr-1" style="display:inline-block"
                                                :disabled="esReprogramacion"
                                                @click="Eliminar().eliminarElemento(tr.Codigo, 'arregloMedicos', tr, '/app/salaoperaciones/DarBajaMedicoProcedimiento', 'IdProcedimientosMedico', '1')">
                                            </vs-button>
                                        </vs-td2>
                                    </tr>
                                </template>
                            </vs-table2>
                        </div>
                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                            <vs-divider position="left">Cirujano Apoyo</vs-divider>
                            <SM-Buscar v-model="busqueda.CodigoCirujano" label="Código Cirujano"
                                api="app/salaoperaciones/ListarAjenosBusqueda"
                                :api_campos="['Codigo', 'Nombre', 'Apellido', 'Celular', 'Tipo', 'Especialidad']"
                                :api_titulos="['Codigo', 'Nombre', 'Apellido', 'Celular', 'Tipo', 'Especialidad']"
                                api_campo_respuesta="Codigo"
                                :callback_buscar="Consulta('/app/salaoperaciones/BuscarAjeno', 'Codigo', 'arregloCirujanos', 'Este cirujano ya se encuentra en la lista', true).ConsultaItem"
                                :callback_nuevo="null" :callback_editar="null" :callback_cancelar="null"
                                :disabled_texto="esReprogramacion" :disabled_busqueda="esReprogramacion"
                                :disabled_editar="false" />
                            <vs-table2 max-items="10" pagination :data="arregloCirujanos" tooltip>
                                <template slot="thead">
                                    <th order="Nombre">Nombre</th>
                                    <th order="Apellido">Apellido</th>
                                    <th order="Celular">Celular</th>
                                    <th></th>
                                </template>
                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td2>
                                            {{ tr.Nombre }}
                                        </vs-td2>
                                        <vs-td2>
                                            {{ tr.Apellido }}
                                        </vs-td2>
                                        <vs-td2>
                                            {{ tr.Celular }}
                                        </vs-td2>
                                        <vs-td2 width="50px" noTooltip>
                                            <vs-button title="Eliminar" color="danger" size="small" icon-pack="fas"
                                                icon="fa-trash" class="mr-1" style="display:inline-block"
                                                :disabled="esReprogramacion"
                                                @click="Eliminar().eliminarElemento(tr.Codigo, 'arregloCirujanos', tr, '/app/salaoperaciones/DarBajaMedicoProcedimiento', 'IdProcedimientosCirujano', '2')">
                                            </vs-button>
                                        </vs-td2>
                                    </tr>
                                </template>
                            </vs-table2>
                        </div>
                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                            <vs-divider position="left">Pediatras</vs-divider>
                            <SM-Buscar v-model="busqueda.CodigoPediatra" label="Código Pediatra"
                                api="app/salaoperaciones/ListarAjenosBusqueda" :api_filtro="{ IdEspecialidad: 10 }"
                                :api_campos="['Codigo', 'Nombre', 'Apellido', 'Celular', 'Tipo', 'Especialidad']"
                                :api_titulos="['Codigo', 'Nombre', 'Apellido', 'Celular', 'Tipo', 'Especialidad']"
                                api_campo_respuesta="Codigo"
                                :callback_buscar="Consulta('/app/salaoperaciones/BuscarAjeno', 'Codigo', 'arregloPediatras', 'Este pediatra ya se encuentra en la lista', true).ConsultaItem"
                                :callback_nuevo="null" :callback_editar="null" :callback_cancelar="null"
                                :disabled_texto="esReprogramacion" :disabled_busqueda="esReprogramacion"
                                :disabled_editar="false" />
                            <vs-table2 max-items="10" pagination :data="arregloPediatras" tooltip>
                                <template slot="thead">
                                    <th order="Nombre">Nombre</th>
                                    <th order="Apellido">Apellido</th>
                                    <th order="Celular">Celular</th>
                                    <th></th>
                                </template>
                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td2>
                                            {{ tr.Nombre }}
                                        </vs-td2>
                                        <vs-td2>
                                            {{ tr.Apellido }}
                                        </vs-td2>
                                        <vs-td2>
                                            {{ tr.Celular }}
                                        </vs-td2>
                                        <vs-td2 width="50px" noTooltip>
                                            <vs-button title="Eliminar" color="danger" size="small" icon-pack="fas"
                                                icon="fa-trash" class="mr-1" style="display:inline-block"
                                                :disabled="esReprogramacion"
                                                @click="Eliminar().eliminarElemento(tr.Codigo, 'arregloPediatras', tr, '/app/salaoperaciones/DarBajaMedicoProcedimiento', 'IdProcedimientosPediatra', '3')">
                                            </vs-button>
                                        </vs-td2>
                                    </tr>
                                </template>
                            </vs-table2>
                        </div>
                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                            <vs-divider position="left">Anestesiólogos</vs-divider>
                            <SM-Buscar v-model="busqueda.CodigoAnestesiolgo" label="Código Anestesiólogo"
                                api="app/salaoperaciones/ListarAjenosBusqueda" :api_filtro="{ IdEspecialidad: 1 }"
                                :api_campos="['Codigo', 'Nombre', 'Apellido', 'Celular', 'Tipo', 'Especialidad']"
                                :api_titulos="['Codigo', 'Nombre', 'Apellido', 'Celular', 'Tipo', 'Especialidad']"
                                api_campo_respuesta="Codigo"
                                :callback_buscar="Consulta('/app/salaoperaciones/BuscarAjeno', 'Codigo', 'arregloAnestesiologos', 'Este anestesiólogo ya se encuentra en la lista', true).ConsultaItem"
                                :callback_nuevo="null" :callback_editar="null" :callback_cancelar="null"
                                :disabled_texto="esReprogramacion" :disabled_busqueda="esReprogramacion"
                                :disabled_editar="false" />
                            <vs-table2 max-items="10" pagination :data="arregloAnestesiologos" tooltip>
                                <template slot="thead">
                                    <th order="Nombre">Nombre</th>
                                    <th order="Apellido">Apellido</th>
                                    <th order="Celular">Celular</th>
                                    <th></th>
                                </template>
                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td2>
                                            {{ tr.Nombre }}
                                        </vs-td2>
                                        <vs-td2>
                                            {{ tr.Apellido }}
                                        </vs-td2>
                                        <vs-td2>
                                            {{ tr.Celular }}
                                        </vs-td2>
                                        <vs-td2 width="50px" noTooltip>
                                            <vs-button title="Eliminar" color="danger" size="small" icon-pack="fas"
                                                icon="fa-trash" class="mr-1" style="display:inline-block"
                                                :disabled="esReprogramacion"
                                                @click="Eliminar().eliminarElemento(tr.Codigo, 'arregloAnestesiologos', tr, '/app/salaoperaciones/DarBajaMedicoProcedimiento', 'IdProcedimientosAnestesiologo', '4')">
                                            </vs-button>
                                        </vs-td2>
                                    </tr>
                                </template>
                            </vs-table2>
                        </div>
                    </div>
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                            <ValidationProvider name="TipoAnestesia" rules="required" class="required">
                                <label style="font-size:12px">Tipo Anestesia</label>
                                <v-select :clearable="false" v-model="selectTipoAnestesia" label="Nombre"
                                    :options="arregloTiposAnestesia" :disabled="esReprogramacion" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                            <ValidationProvider name="TipoAnestesia" rules="required" class="required">
                                <label style="font-size:12px">Especialidad</label>
                                <v-select :clearable="false" v-model="selectEspecialidad" label="Nombre"
                                    :options="arregloEspecialidades" :disabled="esReprogramacion" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                            <ValidationProvider name="TipoProcedimiento" rules="required" class="required">
                                <label style="font-size:12px">Tipo Procedimiento</label>
                                <v-select :clearable="false" v-model="selectTipoProcedimiento" label="Nombre"
                                    :options="arregloTipoProcedimiento" :disabled="esReprogramacion" />
                            </ValidationProvider>
                        </div>
                    </div>
                    <vs-divider position="center">Datos de Proveedor</vs-divider>
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5  p-1">
                            <label style="font-size:12px">Proveedor que alquila el equipo</label>
                            <vs-input v-model="proveedor.Nombre" type="text" class="w-full"
                                :disabled="esReprogramacion" />
                        </div>
                        <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5  p-1">
                            <label style="font-size:12px">Equipo a Utilizar</label>
                            <vs-input v-model="proveedor.Equipo" type="text" class="w-full"
                                :disabled="esReprogramacion" />
                        </div>
                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                            <vs-button class="w-full mr-1 mt-5" icon-pack="fas" icon="fa-plus" color="success"
                                @click="Agregar().AgregarProveedor()" :disabled="esReprogramacion">
                                Agregar
                            </vs-button>
                        </div>
                        <div class="w-full md:w-1/1 lg:w-1/1 xl:w-1/1 p-1">
                            <vs-table2 max-items="10" pagination :data="arrayProveedores">
                                <template slot="thead">
                                    <th order="Nombre">Proveedor</th>
                                    <th order="Apellido">Equipo</th>
                                    <th></th>
                                </template>
                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td2>
                                            {{ tr.Nombre }}
                                        </vs-td2>
                                        <vs-td2>
                                            {{ tr.Equipo }}
                                        </vs-td2>
                                        <vs-td2 width="50px">
                                            <vs-button color="danger" size="small" icon-pack="fas" icon="fa-trash"
                                                class="mr-1" style="display:inline-block" :disabled="esReprogramacion"
                                                @click="Eliminar().eliminarElemento(tr.Codigo, 'arrayProveedores', tr, '/app/salaoperaciones/DarBajaProveedoresProcedimiento', 'IdProcedimientosProveedores')">
                                            </vs-button>
                                        </vs-td2>
                                    </tr>
                                </template>
                            </vs-table2>
                        </div>
                    </div>
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full md:w-5/6 lg:w-5/6 xl:w-5/6 p-1">
                            <label style="font-size:12px">Observaciones</label>
                            <vs-textarea v-model="info.Observaciones" class="w-full" :disabled="esReprogramacion" />
                        </div>
                        <div class="w-full h-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                            <vs-button height="100px" class="w-full mr-1 mt-5" icon-pack="fas" icon="fa-plus"
                                color="success" @click="Agregar().AgregarProcedimiento(invalid)"
                                :disabled="esReprogramacion">
                                Guardar
                            </vs-button>
                        </div>
                    </div>
                    <div class="flex flex-wrap mb-1">
                        <label style="color: red;"><strong>Nota: El mensaje de texto enviado a los médicos puede
                                contener como máximo 150 caracteres, de tener más el mensaje no llegara
                                completo.</strong></label>
                    </div>
                </form>
            </ValidationObserver>
        </vs-popup>
        <vs-popup :title="tituloDetalle" v-show="ventanaInfo" :active.sync="ventanaInfo">
            <div class="flex flex-wrap mb-1" v-if="opcionDetalle == 1">
                <div class="w-full p-1" v-if="permisos.editar && this.permisos.salaOperaciones">
                    <vs-button title="Editar Procedimiento" color="primary" icon-pack="fas" icon="fa-edit" class="mr-1"
                        style="display:inline-block"
                        :disabled="permisos.editar && detalleProcedimiento.Estado == '1' ? false : true"
                        @click="Modificar().CargaData(true)">
                    </vs-button>
                    <vs-button title="Enviar Mensaje" color="primary" icon-pack="fas" icon="fa-envelope" class="mr-1"
                        style="display:inline-block"
                        :disabled="detalleProcedimiento.Estado == '1' && detalleProcedimiento.EnviarMensaje ? false : true"
                        @click="Otros().EnviarMensaje()">
                    </vs-button>
                    <vs-button color="primary" icon-pack="fas" icon="fa-calendar" class="mr-1"
                        style="display:inline-block"
                        :disabled="permisos.editar && detalleProcedimiento.Estado == '1' ? false : true"
                        @click="Modificar().CargaData(false)">
                        Reprogramar</vs-button>
                    <vs-button color="danger" icon-pack="fas" icon="fa-ban" class="mr-1" style="display:inline-block"
                        :disabled="permisos.editar && detalleProcedimiento.Estado == '1' ? false : true"
                        @click="Modificar().CambiarEstado(detalleProcedimiento.IdProcedimiento, 2, detalleProcedimiento.SerieAdmision)">
                        Cancelar</vs-button>
                    <vs-button color="success" icon-pack="fas" icon="fa-check" class="mr-1"
                        :disabled="permisos.finalizar && detalleProcedimiento.Estado == '1' && detalleProcedimiento.SerieAdmision !== '' && detalleProcedimiento.Quirofano !== '' ? false : true"
                        style="display:inline-block"
                        @click="Modificar().CambiarEstado(detalleProcedimiento.IdProcedimiento, 3, detalleProcedimiento.SerieAdmision)">
                        Finalizar</vs-button>
                    <vs-checkbox v-model="detalleProcedimiento.EnviarMensaje" class="mr-1" style="display:inline-flex"
                        :disabled="detalleProcedimiento.Estado == '1' ? false : true"
                        @change="Modificar().CambiarEnviarMensaje(detalleProcedimiento.IdProcedimiento, detalleProcedimiento.EnviarMensaje)">Enviar
                        Mensaje</vs-checkbox>
                </div>
                <div class="w-full p-1" v-if="permisos.admisiones">
                    <vs-button title="Validar datos" color="success" icon-pack="fas" icon="fa-check" class="mr-1"
                        :disabled="detalleProcedimiento.IdCliente == '' || detalleProcedimiento.IdPaciente == '' ? false : true"
                        style="display:inline-block" @click="Otros().CambioVentanaDetalle(2)">
                        Validación de datos
                    </vs-button>
                    <vs-button title="Crear admisión" icon-pack="fas" icon="fa-address-book" class="mr-1"
                        :disabled="detalleProcedimiento.IdCliente == '' || detalleProcedimiento.IdPaciente == '' || detalleProcedimiento.HabilitarAdmision == 0 ? true : false"
                        style="display:inline-block" @click="Otros().CambioVentanaDetalle(3)">
                        Creación de admisión</vs-button>
                    <vs-checkbox v-model="detalleProcedimiento.DatosValidados" class="mr-1" style="display:inline-flex"
                        :disabled="detalleProcedimiento.IdCliente == '' || detalleProcedimiento.IdPaciente == '' ? true : false"
                        @change="Modificar().CambiarDatosPaciente(detalleProcedimiento.IdProcedimiento, detalleProcedimiento.DatosValidados)">¿Paciente
                        contactado?</vs-checkbox>
                </div>
                <vs-divider position="left"></vs-divider>
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1" v-if="permisos.admisiones">
                    <label style="font-size:12px">ID Cliente: {{ this.detalleProcedimiento.IdCliente }}</label>
                </div>
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1" v-if="permisos.admisiones">
                    <label style="font-size:12px">ID Paciente: {{
                        this.detalleProcedimiento.IdPaciente
                    }}</label>
                </div>
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                    <label style="font-size:12px">Hospital: {{ this.detalleProcedimiento.NombreHospital }}</label>
                </div>
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                    <label style="font-size:12px">Nombre Paciente: {{
                        this.detalleProcedimiento.NombrePaciente
                    }}</label>
                </div>
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                    <label style="font-size:12px">Edad Paciente: {{ this.detalleProcedimiento.Edad }}</label>
                </div>
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                    <label style="font-size:12px">Tipo Paciente: {{ this.detalleProcedimiento.TipoPaciente }}</label>
                </div>
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                    <label style="font-size:12px">Telefono Paciente:
                        {{ '+' + this.detalleProcedimiento.Prefijo + this.detalleProcedimiento.TelefonoPaciente
                        }}</label>
                </div>
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                    <label style="font-size:12px">Admisión: {{ this.detalleProcedimiento.SerieAdmision }}</label>
                </div>
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                    <label style="font-size:12px">Habitación: {{ this.detalleProcedimiento.Habitacion }}</label>
                </div>
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                    <label style="font-size:12px">Quirófano: {{ this.detalleProcedimiento.Quirofano }}</label>
                </div>
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                    <label style="font-size:12px">Cirugia(s): {{ this.detalleProcedimiento.Cirugias }}</label>
                </div>
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                    <label style="font-size:12px">Fecha: {{ this.detalleProcedimiento.Fecha }} De:
                        {{ this.detalleProcedimiento.HoraInicio }} A: {{ this.detalleProcedimiento.HoraFin }}</label>
                </div>
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                    <label style="font-size:12px">Médico(s): {{ this.detalleProcedimiento.Medicos }}</label>
                </div>
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                    <label style="font-size:12px">Cirujano(s): {{ this.detalleProcedimiento.Cirujanos }}</label>
                </div>
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                    <label style="font-size:12px">Anestesiólogo(s): {{
                        this.detalleProcedimiento.Anestesiologos
                    }}</label>
                </div>
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                    <label style="font-size:12px">Pediatra(s): {{ this.detalleProcedimiento.Pediatras }}</label>
                </div>
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                    <label style="font-size:12px">Tipo Anestesia: {{
                        this.detalleProcedimiento.NombreAnestesia
                    }}</label>
                </div>
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                    <label style="font-size:12px">Observaciones: {{ this.detalleProcedimiento.Observaciones }}</label>
                </div>
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                    <label style="font-size:12px">Creado Por: {{ this.detalleProcedimiento.CreadoPor }}</label>
                </div>
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                    <label style="font-size:12px">Fecha Creación: {{ this.detalleProcedimiento.FechaCreacion }}</label>
                </div>
                <div class="flex flex-wrap mb-1">
                    <label style="color: red;"><strong>Nota: El mensaje de texto enviado a los médicos puede contener
                            como máximo 150 caracteres, de tener más el mensaje no llegara completo.</strong></label>
                </div>
            </div>
            <div class="flex flex-wrap mb-1" v-if="opcionDetalle == 2">
                <div class="w-full p-1">
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                        <label style="font-size:16px">Nombre ingresado por Sala Operaciones: {{
                            this.detalleProcedimiento.NombrePaciente
                        }}</label>
                    </div>
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                        <vs-button color="success" class="mr-1" style="display:inline-block"
                            @click="Otros().CambioVentanaDetalle(4)">
                            Crear
                        </vs-button>
                    </div>
                </div>
                <ConsultaPacientes ref="ConsultaPacientes" :show-create-button="false"></ConsultaPacientes>
                <vs-divider position="left"></vs-divider>
                <vs-button title="Regresar" class="mr-1" style="display:inline-block"
                    @click="Otros().CambioVentanaDetalle(1)">
                    Atras
                </vs-button>
                <vs-button color="success" class="mr-1" style="display:inline-block"
                    @click="Otros().PacienteSeleccionado()">
                    Seleccionar
                </vs-button>
            </div>
            <div class="flex flex-wrap mb-1" v-if="opcionDetalle == 3">
                <DatosPaciente>
                </DatosPaciente>
                <AdmisionesInternas ref="AdmisionesInternas" @admision="obtenerAdmision">
                </AdmisionesInternas>
                <vs-divider position="left"></vs-divider>
                <vs-button title="Regresar" class="mr-1" style="display:inline-block"
                    @click="Otros().CambioVentanaDetalle(1)">
                    Atras
                </vs-button>
                <vs-button color="success" class="mr-1" style="display:inline-block"
                    @click="Agregar().GuardarAdmision()">
                    Guardar
                </vs-button>
            </div>
            <div class="flex flex-wrap mb-1" v-if="opcionDetalle == 4">
                <DatosPaciente>
                </DatosPaciente>
                <PacienteNuevo ref="PacienteNuevo" @mensaje-respuesta="notificacion"></PacienteNuevo>
                <vs-divider position="left"></vs-divider>
                <vs-button title="Regresar" class="mr-1" style="display:inline-block"
                    @click="Otros().CambioVentanaDetalle(2)">
                    Atras
                </vs-button>
                <vs-button color="success" class="mr-1" style="display:inline-block"
                    @click="Agregar().GuardarCliente()">
                    Guardar
                </vs-button>
            </div>
        </vs-popup>
        <div class="flex flex-wrap mb-1" v-if="permisos.visualizar">
            <div class="w-full h-full p-1">
                <!-- @option-changed="handlePropertyChange" -->
                <DxScheduler time-zone="America/Guatemala" :data-source="dataSource" :editing="editing" :views="views"
                    :show-all-day-panel="false" :shade-until-current-time="shadeUntilCurrentTime" :first-day-of-week="1"
                    :start-day-hour="hourStart" current-view="day" all-day-panel-mode="hidden"
                    @appointmentClick="okClicked2" @appointment-form-opening="onAppointmentFormOpening"
                    :group-by-date="groupByDate" :groups="groups" max-appointments-per-cell="unlimited">
                    <DxResource :use-color-as-default="true" :data-source="dataProcedimientos"
                        field-expr="Procedimiento" />
                    <DxResource :data-source="selectQuirofano.length > 0 ? selectQuirofano : arrayQuirofanos"
                        field-expr="Quirofano" />
                </DxScheduler>
            </div>
        </div>
    </div>
</template>

<script>
import vSelect from 'vue-select'
import flatPickr from 'vue-flatpickr-component';
import 'flatpickr/dist/flatpickr.css';
import {
    Spanish as SpanishLocale
} from 'flatpickr/dist/l10n/es.js';
import esMessages from "devextreme/localization/messages/es.json";
import {
  locale,
  loadMessages
} from "devextreme/localization";
import ConsultaPacientes from '@/components/sermesa/modules/paciente/ConsultaPacientes.vue'
import AdmisionesInternas from "/src/views/pages/AJENOS/AJE022.vue"
import DatosPaciente from "/src/components/sermesa/modules/paciente/DatosPaciente.vue"
import PacienteNuevo from '@/components/sermesa/modules/paciente/DatosGeneralesPaciente.vue'

export default {
    data() {
        return {
            /**
             * Documentación del módulo
             */
            doc_: [
                'Pantalla para administración de procedimientos',
            ],
            permisos: {
                crear: false,
                editar: false,
                finalizar: false,
                cambiarHospital: false,
                salaOperaciones: false,
                admisiones: false
            },
            esEdicion: false,
            esReprogramacion: false,
            ventanaActiva: false,
            ventanaInfo: false,
            arrayHospitales: [],
            selectHospital: {
                Codigo: null,
                NombreSuc: null
            },
            arregloTipoProgramacion: ['Emergencia', 'Electiva'],
            info: {
                Opcion: "",
                SubOpcion: "",
                IdProcedimiento: null,
                Fecha: null,
                HoraInicio: null,
                HoraFin: null,
                Hospital: null,
                TipoProgramacion: null,
                Nombre: null,
                Edad: 0,
                TipoPaciente: null,
                Telefono: null,
                Serie: null,
                Admision: null,
                Habitacion: null,
                Quirofano: null,
                IdCirugia: "",
                IdParteAnatomica: "",
                IdTecnicaCirugia: "",
                CodigoMedico: "",
                CodigoCirujano: "",
                CodigoPediatra: "",
                CodigoAnestesiolgo: "",
                TipoAnestesia: null,
                Especialidad: null,
                Proveedor: null,
                Observaciones: null,
                Reprogramacion: false,
                EnviarMensaje: true,
                Pais: null,
                DatosValidados: false
            },
            busqueda: {
                IdCirugia: "",
                IdParteAnatomica: "",
                IdTecnicaCirugia: "",
                CodigoMedico: "",
                CodigoCirujano: "",
                CodigoPediatra: "",
                CodigoAnestesiolgo: ""
            },
            arregloCirugias: [],
            arregloPartesAnatomicas: [],
            arregloTecnicas: [],
            arregloMedicos: [],
            arregloCirujanos: [],
            arregloPediatras: [],
            arregloAnestesiologos: [],
            arregloTiposAnestesia: [],
            selectHospitalAux: {
                Codigo: null,
                NombreSuc: null
            },
            selectQuirofanoAux: null,
            selectTipoAnestesia: null,
            arregloEspecialidades: [],
            selectEspecialidad: null,
            proveedor: {
                Codigo: 0,
                Nombre: null,
                Equipo: null
            },
            codigoProveedor: 0,
            arrayProveedores: [],
            configFromdateTimePicker: {
                minDate: new Date(),
                maxDate: this.Otros().sumarDias(new Date(), 60),
                locale: SpanishLocale,
                dateFormat: "d-m-Y"
            },
            configTimePicker: {
                enableTime: true,
                enableSeconds: false,
                noCalendar: true,
                time_24hr: true
            },
            //
            hourStart: 0,
            groups: ["Quirofano"],
            views: ['day', 'week'],
            shadeUntilCurrentTime: true,
            allowDragging: false,
            allowResizing: false,
            allowDeleting: false,
            groupByDate: false,
            objetoConsulta: {
                Vista: 'week',
                FechaVista: new Date(),
                Hospital: null
            },
            objetoConsultaQuirofanos: {
                Opcion: 'C',
                SubOpcion: '1',
                Hospital: null
            },
            arrayQuirofanos: [''],
            arrayQuirofanosAux: [],
            selectQuirofano: [],
            dataSource: [],
            dataProcedimientos: [],
            detalleProcedimiento: {},
            tituloDetalle: '',
            opcionDetalle: 0,
            tituloVentana: '',
            HospitalesAsignados: '',
            arrayPaises: [],
            selectPaisAux: null,
            pacienteSeleccionado: { IdProcedimiento: null, IdCliente: null, IdPaciente: null },
            arregloTipoProcedimiento: [],
            selectTipoProcedimiento: null,
        }
    },
    computed: {
        sesion() {
            return this.$store.state.sesion
        },
        editing() {
            return {
                allowDragging: this.allowDragging,
                allowResizing: this.allowResizing,
                allowDeleting: this.allowDeleting
            };
        },
        paciente() {
            return this.$store.getters["paciente/getPaciente"]
        }
    },
    components: {
        'v-select': vSelect,
        flatPickr,
        ConsultaPacientes,
        AdmisionesInternas,
        DatosPaciente,
        PacienteNuevo
    },
    methods: {
        Consulta: function (url, propiedad, arreglo, texto, buscarAjeno = false) {
            return {
                /**
                 * Bruno Gudiel 
                 * 22/07/2022
                 * 
                 * Consulta de item
                 * 
                 * Esta funcion permite hacer la busqueda el item seleccionado desde el pluggin SM-Buscar
                 * recibiendo como parametros en la funcion princial(Consulta), la url del api a consultar,
                 * la propiedad de busqueda, el arreglo donde se agregara la informacion del item a buscar y
                 * el texto necesario para la notificacion
                 */
                ConsultaItem: (datos) => {
                    var json = {}
                    json[propiedad] = datos[propiedad]

                    if (buscarAjeno) {
                        json.IdProcedimiento = this.info.IdProcedimiento
                        json.Fecha = this.info.Fecha
                        json.HoraInicio = this.info.HoraInicio
                        json.HoraFin = this.info.HoraFin
                    }

                    this.axios.post(url, json)
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                if (Object.prototype.hasOwnProperty.call(resp.data.json[0], "tipo_error")) {
                                    this.$vs.notify({
                                        title: 'Alerta',
                                        text: resp.data.json[0].descripcion,
                                        color: 'danger'
                                    })
                                } else {
                                    if (this[arreglo].findIndex((element) => element[propiedad] == resp.data.json[0][propiedad]) == -1) {
                                        this.busqueda[propiedad] = ""
                                        this[arreglo].push(resp.data.json[0])
                                    } else {
                                        this.$vs.notify({
                                            title: 'Alerta',
                                            text: texto,
                                            color: 'danger'
                                        })
                                    }
                                }
                            }
                        })
                },
                /**
                 * Bruno Gudiel 
                 * 22/07/2022
                 * 
                 * Consulta de select
                 * 
                 * Esta funcion permite traer la lista para llenar el select, recibiendo como parametros
                 * la url del api a consultar y el arreglo a llenar
                 */
                ConsultaSelect: (url, arreglo) => {
                    this.axios.post(url, {})
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this[arreglo] = resp.data.json;
                            }
                        })
                },
                ConsultaProcedimientos: (url, array) => {
                    if (this.selectHospital === null) {
                        this.objetoConsulta.Hospital = this.sesion.sesion_sucursal
                    } else {
                        this.objetoConsulta.Hospital = this.selectHospital.Codigo
                    }

                    this.axios.post(url, this.objetoConsulta)
                        .then(resp => {
                            this[array] = [];

                            if (array == 'dataProcedimientos') {
                                this.Consulta().ConsultaProcedimientos('/app/salaoperaciones/ListarData', 'dataSource')
                            }

                            this[array] = resp.data.json
                        })
                },
                ObtenerHospitalesAsignados: () => {
                    this.axios.post('/app/salaoperaciones/ListarHospitales', {})
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                resp.data.json.forEach(element => this.arrayHospitales.push({ "Codigo": element.CodigoSuc, "NombreSuc": element.NombreSuc }))

                                const arrayTemp = []
                                resp.data.json.forEach(element => arrayTemp.push(element.CodigoSuc))
                                this.HospitalesAsignados = arrayTemp.join(",")
                            }
                        })
                },
                ObtenerQuirofanosHospital: (objeto, array) => {
                    if (this[objeto] === null) {
                        this.objetoConsultaQuirofanos.Hospital = this.sesion.sesion_sucursal
                    } else {
                        this.objetoConsultaQuirofanos.Hospital = this[objeto].Codigo
                    }

                    this.axios.post('/app/salaoperaciones/ListarQuirofanos', this.objetoConsultaQuirofanos)
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this[array] = resp.data.json;
                            }
                        })
                },
                DetalleProcedimiento: (IdProcedimiento) => {
                    if (typeof IdProcedimiento === 'object') {
                        IdProcedimiento = IdProcedimiento.IdProcedimiento
                    }

                    this.detalleProcedimiento = {}
                    this.axios.post('/app/salaoperaciones/DetalleProcedimiento', { "IdProcedimiento": IdProcedimiento })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.detalleProcedimiento = resp.data.json[0]
                                this.detalleProcedimiento.EnviarMensaje = (this.detalleProcedimiento.EnviarMensaje == 1 ? true : false)
                                this.detalleProcedimiento.DatosValidados = (this.detalleProcedimiento.DatosValidados == 1 ? true : false)
                                this.detalleProcedimiento.TituloVentana = resp.data.json[0].Cirugias + " - " + resp.data.json[0].Medicos
                                this.Otros().CambioVentanaDetalle(1)
                            }
                        })
                },
                BusquedaAdmision: (data) => {
                    this.info.Nombre = data.Nombre
                    this.info.Edad = data.Edad
                    this.info.TipoPaciente = data.TipoPaciente
                    this.info.Serie = data.Serie
                    this.info.Admision = data.Admision
                    this.info.Habitacion = data.Habitacion
                },
                CargarDataTablas: (IdProcedimiento, url, array, SubOpcion = null) => {
                    this.axios.post(url, { "IdProcedimiento": IdProcedimiento, "SubOpcion": SubOpcion })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this[array] = resp.data.json
                            }
                        })
                },
                ObtenerDias: () => {
                    this.axios.post('/app/salaoperaciones/ObtenerDias', {})
                        .then(resp => {
                            this.configFromdateTimePicker.minDate = this.configFromdateTimePicker.minDate.setDate(this.configFromdateTimePicker.minDate.getDate() - resp.data.json[0].ValoresWeb)
                        })
                },
                ObtenerPaises: (prefijo) => {
                    this.axios.post('/app/salaoperaciones/ListarPaises', {})
                        .then(resp => {
                            this.arrayPaises = resp.data.json
                            let index = this.arrayPaises.findIndex(x => x.Prefijo == prefijo)

                            this.selectPaisAux = {
                                id: this.arrayPaises[index].id,
                                text: this.arrayPaises[index].text
                            }
                        })
                },
                CargarPaciente: () => {
                    this.axios.post('/app/salaoperaciones/BusquedaPaciente', { IdPaciente: this.detalleProcedimiento.IdPaciente })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.$store.dispatch('paciente/setPaciente', resp.data.json[0])
                            }
                        })
                }
            }
        },
        Agregar: function () {
            return {
                AgregarProcedimiento: (isInvalid) => {
                    if (isInvalid) {
                        this.$vs.notify({
                            title: "Error al ingresar Procedimiento",
                            text: "Debe ingresar todos los campos",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                    }

                    if (this.selectTipoProcedimiento.Codigo == 0) {
                        this.$vs.notify({
                            title: "Error al ingresar Procedimiento",
                            text: "Debe ingresar todos los campos",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                        isInvalid = true
                    }

                    if (this.selectEspecialidad.IdEspecialidadCirugia == 0) {
                        this.$vs.notify({
                            title: "Error al ingresar Procedimiento",
                            text: "Debe ingresar todos los campos",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                        isInvalid = true
                    }

                    if (!isInvalid && !this.Otros().ValidarTablas("arregloCirugias", "Debe ingresar al menos una cirugía")
                        && !this.Otros().ValidarTablas("arregloTecnicas", "Debe ingresar al menos una técnica")
                        && !this.Otros().ValidarTablas("arregloMedicos", "Debe ingresar al menos un médico tratante")) {
                        this.Otros().arrayString("arregloCirugias", "IdCirugia", ",")
                        this.Otros().arrayString("arregloPartesAnatomicas", "IdParteAnatomica", ",")
                        this.Otros().arrayString("arregloTecnicas", "IdTecnicaCirugia", ",")
                        this.Otros().arrayString("arregloMedicos", "CodigoMedico", ",")
                        this.Otros().arrayString("arregloCirujanos", "Codigo", ",", "CodigoCirujano")
                        this.Otros().arrayString("arregloPediatras", "Codigo", ",", "CodigoPediatra")
                        this.Otros().arrayString("arregloAnestesiologos", "Codigo", ",", "CodigoAnestesiolgo")
                        this.Otros().arrayString("arrayProveedores", "Proveedor", "|")

                        this.info.Hospital = this.selectHospitalAux.Codigo;
                        this.info.Quirofano = this.selectQuirofanoAux.id;
                        this.info.Pais = this.selectPaisAux.id
                        this.info.TipoAnestesia = this.selectTipoAnestesia.IdTipoAnestesia;
                        this.info.Especialidad = this.selectEspecialidad.IdEspecialidadCirugia;
                        this.info.TipoProcedimiento = this.selectTipoProcedimiento.Codigo;
                        this.info.Reprogramacion = this.esReprogramacion;
                        this.info.Opcion = 'I'
                        this.info.SubOpcion = '1'
                        this.info.EnviarMensaje = (this.info.EnviarMensaje == 1 ? true : false)

                        this.axios.post('/app/salaoperaciones/IngresarProcedimientos', this.info)
                            .then(resp => {
                                if (resp.data.tipo_error == 0) {
                                    setTimeout(function () {
                                        location.reload()
                                    }, 3000);
                                }
                            })
                    }
                },
                AgregarProveedor: () => {
                    this.arrayProveedores.push(this.proveedor);
                    this.proveedor.Proveedor = this.proveedor.Nombre + "-" + this.proveedor.Equipo
                    this.proveedor = {
                        Nombre: null,
                        Equipo: null
                    }

                    this.codigoProveedor = this.codigoProveedor + 1
                    this.proveedor.Codigo = this.codigoProveedor
                },
                GuardarAdmision: async () => {
                    await this.$refs["AdmisionesInternas"].submit()
                },
                GuardarCliente: () => {
                    this.$refs["PacienteNuevo"].submit()
                }
            }
        },
        Eliminar: function () {
            return {
                eliminarElemento: (codigo, nombreArreglo, objeto = null, url, propiedad, SubOpcion = null) => {
                    if (this.esEdicion) {
                        this.axios.post(url, { "Procedimiento": objeto[propiedad], "SubOpcion": SubOpcion })
                            .then(resp => {
                                if (resp.data.tipo_error == 0) {
                                    this[nombreArreglo] = this[nombreArreglo].filter((item) => item.Codigo !== codigo);
                                }
                            })
                    } else {
                        this[nombreArreglo] = this[nombreArreglo].filter((item) => item.Codigo !== codigo);
                    }
                }
            }
        },
        Modificar: function () {
            return {
                CambiarEstado: (IdProcedimiento, Estado) => {
                    var enviarPeticion = true

                    if (enviarPeticion) {
                        this.axios.post('/app/salaoperaciones/CambiarEstado', { "IdProcedimiento": IdProcedimiento, "Estado": Estado })
                            .then(resp => {
                                if (resp.data.tipo_error == 0) {
                                    setTimeout(function () {
                                        location.reload()
                                    }, 3000);
                                }
                            })
                    }
                },
                CargaData: (esEdicion) => {
                    var date = this.detalleProcedimiento.Fecha.split('-')
                    date = date[2] + "-" + date[1] + "-" + date[0]
                    date = new Date(date)
                    date.setDate(date.getDate() + 1)

                    this.configFromdateTimePicker.minDate = new Date(date)

                    if (date.getTime() >= new Date().getTime()) {
                        this.configFromdateTimePicker.minDate = new Date()
                    }

                    if (esEdicion) {
                        this.esEdicion = true
                        this.esReprogramacion = false
                    } else {
                        this.esReprogramacion = true
                        this.esEdicion = false
                    }

                    this.arrayQuirofanosAux = this.arrayQuirofanos
                    this.ventanaInfo = !this.ventanaInfo
                    this.ventanaActiva = !this.ventanaActiva
                    this.tituloVentana = "Editar Procedimiento"
                    this.info.Fecha = new Date(date)
                    this.info.HoraInicio = this.detalleProcedimiento.HoraInicio
                    this.info.IdProcedimiento = this.detalleProcedimiento.IdProcedimiento
                    this.info.HoraFin = this.detalleProcedimiento.HoraFin
                    this.info.Nombre = this.detalleProcedimiento.NombrePaciente
                    this.info.Edad = this.detalleProcedimiento.Edad
                    this.info.Telefono = this.detalleProcedimiento.TelefonoPaciente
                    this.info.Observaciones = this.detalleProcedimiento.Observaciones
                    this.selectHospitalAux.Codigo = this.detalleProcedimiento.Hospital
                    this.selectHospitalAux.NombreSuc = this.detalleProcedimiento.NombreHospital
                    this.selectTipoAnestesia = {
                        IdTipoAnestesia: this.detalleProcedimiento.IdTipoAnestesia,
                        Nombre: this.detalleProcedimiento.NombreAnestesia
                    }

                    this.selectEspecialidad = {
                        IdEspecialidadCirugia: this.detalleProcedimiento.IdEspecialidadCirugia,
                        Nombre: this.detalleProcedimiento.NombreEspecialidad
                    }

                    this.selectTipoProcedimiento = {
                        Codigo: this.detalleProcedimiento.TipoProcedimiento,
                        Nombre: this.detalleProcedimiento.NombreTipoProcedimiento
                    }

                    /* this.selectEspecialidad.IdEspecialidadCirugia = this.detalleProcedimiento.IdEspecialidadCirugia
                    this.selectEspecialidad.Nombre = this.detalleProcedimiento.NombreEspecialidad */
                    /*this.selectTipoProcedimiento.Codigo = this.detalleProcedimiento.TipoProcedimiento
                    this.selectTipoProcedimiento.Nombre = this.detalleProcedimiento.NombreTipoProcedimiento*/
                    this.info.TipoProgramacion = this.detalleProcedimiento.TipoProgramacion
                    this.detalleProcedimiento.Serie == " " || this.detalleProcedimiento.Serie == "" ? this.detalleProcedimiento.Serie = null : null
                    this.detalleProcedimiento.Serie == " " || this.detalleProcedimiento.Serie == "" ? this.info.Serie = null : this.info.Serie = this.detalleProcedimiento.Serie
                    this.info.Admision = this.detalleProcedimiento.Admision
                    this.info.Habitacion = this.detalleProcedimiento.Habitacion
                    this.info.TipoPaciente = this.detalleProcedimiento.TipoPaciente
                    this.selectQuirofanoAux = {
                        id: this.detalleProcedimiento.CodigoQuirofano,
                        text: this.detalleProcedimiento.NombreQuirofano
                    }

                    this.info.EnviarMensaje = this.detalleProcedimiento.EnviarMensaje
                    this.Consulta().ObtenerPaises(this.detalleProcedimiento.Prefijo);
                    this.Consulta().CargarDataTablas(this.detalleProcedimiento.IdProcedimiento, '/app/salaoperaciones/ListarCirugiasProcedimiento', 'arregloCirugias')
                    this.Consulta().CargarDataTablas(this.detalleProcedimiento.IdProcedimiento, '/app/salaoperaciones/ListarPartesProcedimiento', 'arregloPartesAnatomicas')
                    this.Consulta().CargarDataTablas(this.detalleProcedimiento.IdProcedimiento, '/app/salaoperaciones/ListarTecnicasProcedimiento', 'arregloTecnicas')
                    this.Consulta().CargarDataTablas(this.detalleProcedimiento.IdProcedimiento, '/app/salaoperaciones/ListarMedicosProcedimiento', 'arregloMedicos', '3')
                    this.Consulta().CargarDataTablas(this.detalleProcedimiento.IdProcedimiento, '/app/salaoperaciones/ListarMedicosProcedimiento', 'arregloCirujanos', '4')
                    this.Consulta().CargarDataTablas(this.detalleProcedimiento.IdProcedimiento, '/app/salaoperaciones/ListarMedicosProcedimiento', 'arregloPediatras', '5')
                    this.Consulta().CargarDataTablas(this.detalleProcedimiento.IdProcedimiento, '/app/salaoperaciones/ListarMedicosProcedimiento', 'arregloAnestesiologos', '6')
                    this.Consulta().CargarDataTablas(this.detalleProcedimiento.IdProcedimiento, '/app/salaoperaciones/ListarProveedoresProcedimiento', 'arrayProveedores')
                    this.Consulta().ObtenerQuirofanosHospital("selectHospitalAux", "arrayQuirofanosAux");
                },
                CambiarEnviarMensaje: (IdProcedimiento, EnviarMensaje) => {
                    this.axios.post('/app/salaoperaciones/CambiarEnviarMensaje', { "IdProcedimiento": IdProcedimiento, "EnviarMensaje": EnviarMensaje })
                        .then()
                },
                CambiarDatosPaciente: (IdProcedimiento, DatosValidados) => {
                    if (DatosValidados) {
                        this.axios.post('/app/salaoperaciones/CambiarDatosPaciente', { "IdProcedimiento": IdProcedimiento, "DatosValidados": DatosValidados })
                            .then()
                    }
                }
            }
        },
        Otros: function () {
            return {
                AbrirVentana: () => {
                    this.esEdicion = false
                    this.esReprogramacion = false
                    this.ventanaActiva = !this.ventanaActiva
                    this.tituloVentana = 'Agregar Procedimiento'
                    this.detalleProcedimiento = {}
                    this.Otros().Limpiar()
                    this.Consulta().ObtenerQuirofanosHospital("selectHospitalAux", "arrayQuirofanosAux");
                    this.Consulta().ObtenerDias();
                    this.Consulta().ObtenerPaises('502');
                },
                arrayString: (array, prop, caracter, prop2 = '') => {
                    if (this[array].length > 0) {
                        const arrayTemp = []
                        if (prop2 == '') {
                            this[array].forEach(element => arrayTemp.push(element[prop]))
                            this.info[prop] = arrayTemp.join(caracter)
                        } else {
                            this[array].forEach(element => arrayTemp.push(element[prop]))
                            this.info[prop2] = arrayTemp.join(caracter)
                        }
                    }
                },
                changeHospital: (desdeVentana) => {
                    if (!desdeVentana) {
                        this.arrayQuirofanos = ['']
                        this.selectQuirofano = []

                        this.Consulta().ObtenerQuirofanosHospital("selectHospital", "arrayQuirofanos");
                        this.Consulta().ConsultaProcedimientos('/app/salaoperaciones/ListarProcedimientos', 'dataProcedimientos')
                    } else {
                        this.Consulta().ObtenerQuirofanosHospital("selectHospitalAux", "arrayQuirofanosAux");
                        this.selectQuirofanoAux = null
                    }

                },
                changeQuirofano: () => {
                    if (this.selectQuirofano.leght > 0) {
                        this.groupByDate = true
                    } else {
                        this.groupByDate = false
                    }
                },
                Limpiar: () => {
                    this.arregloCirugias = []
                    this.arregloPartesAnatomicas = []
                    this.arregloTecnicas = []
                    this.arregloMedicos = []
                    this.arregloCirujanos = []
                    this.arregloPediatras = []
                    this.arregloAnestesiologos = []
                    this.arrayProveedores = []

                    this.info = {
                        Opcion: "",
                        SubOpcion: "",
                        IdProcedimiento: null,
                        Fecha: null,
                        HoraInicio: null,
                        HoraFin: null,
                        Hospital: null,
                        TipoProgramacion: null,
                        Nombre: null,
                        Edad: 0,
                        TipoPaciente: null,
                        Telefono: null,
                        Serie: null,
                        Admision: "",
                        Habitacion: null,
                        Quirofano: null,
                        IdCirugia: "",
                        IdParteAnatomica: "",
                        IdTecnicaCirugia: "",
                        CodigoMedico: "",
                        CodigoCirujano: "",
                        CodigoPediatra: "",
                        CodigoAnestesiolgo: "",
                        TipoAnestesia: null,
                        Especialidad: null,
                        Proveedor: null,
                        Observaciones: null,
                        EnviarMensaje: true,
                        Pais: null,
                        DatosValidados: false
                    }

                    this.selectQuirofanoAux = null

                    this.selectTipoAnestesia = null

                    this.selectEspecialidad = null

                    this.selectTipoProcedimiento = null
                },
                ValidarTablas: (array, texto) => {
                    var isInvalid = false;
                    if (this[array].length == 0) {
                        this.$vs.notify({
                            title: "Error al ingresar Procedimiento",
                            text: texto,
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                        isInvalid = true;
                    }

                    return isInvalid;
                },
                EnviarMensaje: () => {
                    this.axios.post('/app/salaoperaciones/EnviarMensaje', { "IdProcedimiento": this.detalleProcedimiento.IdProcedimiento })
                        .then()
                },
                sumarDias: (fecha, dias) => {
                    fecha.setDate(fecha.getDate() + dias);

                    return fecha;
                },
                CambioVentanaDetalle: (opcion) => {
                    this.opcionDetalle = opcion
                    this.ventanaInfo = true

                    if (opcion == 1) {
                        this.tituloDetalle = this.detalleProcedimiento.TituloVentana
                    } else if (opcion == 2) {
                        this.tituloDetalle = "Validación datos"
                    } else if (opcion == 3) {
                        this.tituloDetalle = "Creación admisión"
                        this.Consulta().CargarPaciente()
                    }
                },
                PacienteSeleccionado: () => {
                    this.pacienteSeleccionado.IdCliente = this.paciente.IdCliente
                    this.pacienteSeleccionado.IdPaciente = this.paciente.Codigo
                    this.pacienteSeleccionado.IdProcedimiento = this.detalleProcedimiento.IdProcedimiento

                    if (this.pacienteSeleccionado.IdCliente !== "" || this.pacienteSeleccionado.IdPaciente !== "") {
                        this.axios.post('/app/salaoperaciones/PacienteSeleccionado', this.pacienteSeleccionado)
                            .then(resp => {
                                if (resp.data.tipo_error == 0) {
                                    this.Otros().CambioVentanaDetalle(1)
                                    this.Consulta().DetalleProcedimiento(this.pacienteSeleccionado.IdProcedimiento)
                                }
                            })
                    } else {
                        this.$vs.notify({
                            title: "Error al seleccionar paciente",
                            text: "Debe seleccionar un paciente",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                    }
                }
            }

        },
        okClicked2: function (e) {
            e.cancel = true
        },
        onAppointmentFormOpening(e) {
            e.cancel = true
            var objeto = e.appointmentData

            if (Object.prototype.hasOwnProperty.call(objeto, "Procedimiento")) {
                this.Consulta().DetalleProcedimiento(objeto.Procedimiento)
            }
        },
        obtenerAdmision(admision) {
            if (admision.Serie !== null && admision.Admision !== null) {
                var obj = {
                    IdProcedimiento: this.detalleProcedimiento.IdProcedimiento,
                    Serie: admision.Serie,
                    Admision: admision.Admision
                }

                this.axios.post('/app/salaoperaciones/AdmisionProcedimiento', obj)
                    .then(resp => {
                        if (resp.data.tipo_error == 0) {
                            this.Consulta().DetalleProcedimiento(obj.IdProcedimiento)
                            this.Otros().CambioVentanaDetalle(1)
                        }
                    })
            }
        },
        notificacion(response) {
            this.$vs.notify({
                title: "Creación de cliente",
                text: response.data.json[0].descripcion,
                iconPack: 'feather',
                icon: 'icon-alert-circle',
                color: 'success',
                position: 'bottom-center'
            })

            this.$store.dispatch('paciente/initPaciente')
            this.Otros().CambioVentanaDetalle(2)
        }
    },
    mounted() {
        this.permisos.crear = this.$validar_privilegio('AGREGAR').status
        this.permisos.editar = this.$validar_privilegio('EDITAR').status
        this.permisos.visualizar = this.$validar_privilegio('VISUALIZAR').status
        this.permisos.finalizar = this.$validar_privilegio('FINALIZAR').status
        this.permisos.cambiarHospital = this.$validar_privilegio('CAMBIARHOSPITAL').status
        this.permisos.salaOperaciones = this.$validar_privilegio('SALAOPERACIONES').status
        this.permisos.admisiones = this.$validar_privilegio('ADMISIONES').status

        this.selectHospital.Codigo = this.sesion.sesion_sucursal
        this.selectHospital.NombreSuc = this.sesion.sesion_sucursal_nombre
        this.selectHospitalAux.Codigo = this.sesion.sesion_sucursal
        this.selectHospitalAux.NombreSuc = this.sesion.sesion_sucursal_nombre

        this.Consulta().ConsultaProcedimientos('/app/salaoperaciones/ListarProcedimientos', 'dataProcedimientos')
        this.Consulta().ObtenerHospitalesAsignados()
        this.Consulta().ObtenerQuirofanosHospital("selectHospital", "arrayQuirofanos");
        this.Consulta().ConsultaSelect('/app/salaoperaciones/ListarTipoAnestesia', 'arregloTiposAnestesia')
        this.Consulta().ConsultaSelect('/app/salaoperaciones/ListarEspecialidades', 'arregloEspecialidades')
        this.Consulta().ConsultaSelect('/app/salaoperaciones/ListarTipoProcedimiento', 'arregloTipoProcedimiento')
    },
    created() {
        loadMessages(esMessages);
        locale(navigator.language);
    }
}
</script>