<template>
<div class="p-2">
    <vx-card>
        <DxForm :ref="formRefKey" :form-data.sync="formulario" labelMode="floating" :show-validation-summary="true" :read-only="Modulo === 2 ? true : false">
            <DxFormItem item-type="group">
                <DxFormItem :col-span="2" data-field="CodigoDiagnostico" :is-required="true" editor-type="dxLookup" :editor-options="{ dataSource: cie10DataSource, valueExpr:'Codigo', displayExpr: 'Display', displayValue: 'Display', showCancelButton: false, dropDownOptions: dropDownOptions, deferRendering: false, }">
                    <DxFormLabel :text="'Diagnóstico de cita para uso estadístico'" />
                </DxFormItem>
            </DxFormItem>
            <DxFormItem item-type="group" :col-span="2" :col-count="2">
                <DxFormItem data-field="ImpresionClinica" editor-type="dxTextArea" :editor-options="{ height: 100 }">
                    <DxFormLabel :text="'Diagnóstico / Impresión clínica'" />
                </DxFormItem>
                <DxFormItem data-field="Anotaciones" editor-type="dxTextArea" :editor-options="{ height: 100 }" />
            </DxFormItem>
        </DxForm>
    </vx-card>
</div>
</template>

<script>
import 'devextreme-vue/lookup'

import 'devextreme-vue/lookup'

const formRefKey = 'form-diagnostico'

export default {
    name: 'Diagnostico',
    components: {
    },
    data() {
        return {
            formRefKey,
            formulario: {
                ImpresionClinica: null,
                Anotaciones: null,
                CodigoDiagnostico: null
            },
            cie10: [],
            cie10DataSource: {
                paginate: true,
                pageSize: 10,
                sort: 'Codigo',
                key: 'Codigo',
                // loadMode: 'raw',
                byKey: (e) => {
                    return this.cie10.find(x => x.Codigo === e)
                },
                load: (e) => {
                    if (e.take) {
                        if (e.skip === e.take) {
                            e.take = e.take + 10
                        }
                    }
                    return this.$filtrado_avanzado_palabras(e.searchValue, this.cie10, 'Display', e.skip, e.skip + e.take)
                }
            },

            dropDownOptions: {
                showTitle: false,
                // maxHeight: '300px',
                hideOnOutsideClick: true,
            },
        }
    },
    props: {
        Sexo: null,
        IdCita: null,
        IdCliente: null,
        IdPaciente: null,
        IdMedico: null,
        IdConsulta: null,

        ImpresionClinica: null,
        Anotaciones: null,
        CodigoDiagnostico: null,

        Modulo: null, //Variable para saber desde donde está siendo invocado el componente 1 = Historial | 2 = Evoluciones
    },
    methods: {
        CargarCIE10() {
            this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaDiagnosticos', {})
                .then(resp => {
                    this.cie10 = resp.data.json.map((x, item) => {
                        return {
                            Id: item,
                            Codigo: x.codigoDiagnostico,
                            Descripcion: x.Diagnostico,
                            Display: x ? x.codigoDiagnostico + ' - ' + x.Diagnostico : 'Diagnóstico para Estadística'
                        }
                    })

                    this.LimpiarVariables()
                    this.formulario.ImpresionClinica = this.ImpresionClinica
                    this.formulario.Anotaciones = this.Anotaciones
                    this.formulario.CodigoDiagnostico = this.CodigoDiagnostico

                    this.formularioDiagnostico.repaint()

                })
        },
        LimpiarVariables() {
            this.formulario = {
                ImpresionClinica: null,
                Anotaciones: null,
                CodigoDiagnostico: null
            }
        },
        GuadarDiagnostico() {
            if (this.formularioDiagnostico.validate().isValid) {
                this.axios.post('/app/v1_Historial_Clinico/ActualizarDiagnostico', {
                        IdConsulta: this.IdConsulta,
                        ImpresionClinica: this.formulario.ImpresionClinica !== null ? this.formulario.ImpresionClinica.replace(/\n/g, '\r\n') : '',
                        CodigoDiagnostico: this.formulario.CodigoDiagnostico,
                        Anotaciones: this.formulario.Anotaciones !== null ? this.formulario.Anotaciones.replace(/\n/g, '\r\n') : '',
                        IdCliente: this.IdCliente
                    })
                    .then(resp => {
                        if (resp.data.codigo === 0) {
                            this.$emit('actualizarDiagnostico', this.formulario)
                        }
                    })
            }
        },
        ActualizarDatos() {
            this.formulario.ImpresionClinica = this.ImpresionClinica
            this.formulario.Anotaciones = this.Anotaciones

            //Se asigna por defecto el código Y349 pero si ya se tiene otro seleccionado, se modificará
            this.formulario.CodigoDiagnostico = 'Y349'

            if (this.CodigoDiagnostico !== null && this.CodigoDiagnostico !== '') {
                this.formulario.CodigoDiagnostico = this.CodigoDiagnostico
            }
            this.formularioDiagnostico.repaint()
        }
    },
    mounted() {
        setTimeout(() => {
            this.ActualizarDatos()
        }, 500);
    },
    beforeMount() {
        this.CargarCIE10()
    },
    watch: {
        'IdConsulta'(newval) {
            if (newval !== '' && newval !== null) {
                this.ActualizarDatos()
            }
        },
    },
    computed: {
        formularioDiagnostico: function () {
            return this.$refs[formRefKey].instance;
        },
        colCountByScreen() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 3,
                    lg: 3
                };
        },
    },
}
</script>
