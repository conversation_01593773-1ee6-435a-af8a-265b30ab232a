<template>
<div>
    <div class="flex flex-wrap">
        <div class="w-full sm:w-1/6 ">
            <div class="barra_tabs">
                <div class="tabs" v-for="(item,key) in opciones" :key="key" @click="seleccion(item.index)" :class="[(item.index==temp_input)?'active':'',(item.required)?'required':'']">
                    {{item.nombre}}
                    <i :class="item.icono"></i>
                </div>
            </div>
        </div>

        <div class="w-full sm:w-5/6  p-1 contenedor_tabs">
            <div v-if="!finalizando" style="text-align:center;padding-top:20px">Cargando...</div>
            <div ref="tabs" :style="{'display':finalizando?'inherit':'none','max-height':height}" class="contenedor-tab">
                <slot></slot>
            </div>
        </div>
    </div>
</div>
</template>

<script>
export default {
    name: 'SM-Tabs',
    data() {
        return {
            temp_input: 0,
            opciones: [],
            refrescarTabs: null,
            finalizando: false,
        }
    },
    props: {
        //v-model
        value: {
            default: 0
        },

        height: {
            default: "600px"
        },

        opcion_confirmar: {
            default: false
        }

    },
    watch: {
        value(value) {
            this.temp_input = value
        },
        temp_input(value) {
            this.$refs.tabs.children.forEach((element, index) => {
                const indx = element.attributes.getNamedItem("index") ? element.attributes.getNamedItem("index").value : index
                if (indx != value) {
                    element.style.display = "none";
                } else {
                    element.style.display = 'block';
                }
            });
        },
    },
    components: {

    },
    methods: {
        seleccion(index) {
            if (this.opcion_confirmar) {
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'warning',
                    title: 'Confirmación',
                    text: '¿Desea cambiar a la opción ' + this.opciones[index].nombre + '?',
                    accept: () => {
                        this.$emit('input', index)
                        this.temp_input = index
                    }
                })
            } else {
                this.$emit('input', index)
                this.temp_input = index
            }

        }
    },
    mounted() {
        this.temp_input = this.value
        this.buscador_valor = this.value

        if (this.$refs.tabs) {
            let listadoTabs = this.$refs.tabs.children.length
            const tabs = () => {
                this.opciones = []

                this.$refs.tabs.children.forEach((element, index) => {
                    if (index != 0) element.style.display = "none";
                    const arr = {
                        index: element.attributes.getNamedItem("index") ? element.attributes.getNamedItem("index").value : index,
                        icono: element.attributes.getNamedItem("icon") ? element.attributes.getNamedItem("icon").value : 'fas fa-chevron-right',
                        nombre: element.attributes.getNamedItem("label").value,
                        required: element.classList.contains('required')
                    }

                    this.opciones.push(arr)
                });
                listadoTabs = this.$refs.tabs.children.length
                setTimeout(() => this.finalizando = true, 1000)
            }
            tabs()
            this.refrescarTabs = setInterval(() => {
                // console.log(1)
                if (listadoTabs != this.$refs.tabs.children.length) tabs()
            }, 1000)

        }
    },
    beforeDestroy() {
        clearInterval(this.refrescarTabs)
        this.finalizando = false
    }
}
</script>

<style scoped vars="{ height }">
.contenedor_tabs {
    border: 3px solid rgba(var(--vs-primary), 0.8);
    border-radius: 0 5px 5px 5px;
}

.tabs {
    /* display: inline; */
    position: relative;
    padding: 10px 10px;
    border: 1px solid #ccc;
    /* border-bottom:3px solid #95a5a6; */
    background-color: white;
    /* border-radius: 15px 15px 0 0; */
    /* margin-right: -4px; */
    cursor: pointer;
    color: #95a5a6;
    font-size: 15px;
    transition: all ease 1s;
    height: 70px;

    /* z-index: -1; */
}

.tabs:first-child {
    border-radius: 15px 0 0 0;
}

.tabs:last-child {
    border-radius: 0 0 0 15px;
}

.tabs i {
    /* background-color:red; */
    margin-right: 2px;
}

.tabs.active {
    background-color: rgba(var(--vs-primary), 0.8);
    border: none;
    z-index: 2;
    color: white;
}

.tabs.active.required::before {
    color: white;
}

.tabs.required::before {
    content: '*';
    color: red;
    font-weight: 600;
}

.tabs>i {
    float: right;
    margin-top: 6px;
    font-size: 11px;
    /* color:blue */
}

.contenedor-tab {
    overflow: auto;
    transition: all ease 0.5s;
}
</style>
