export const DefaultDxGridConfiguration = {
  visible: true,
  showRowLines: true,
  showColumnLines: true,
  showBorders: true,
  'load-panel': {enabled: false},
  selection: {mode: 'single'},
  searchPanel: {visible: true},
  focusedRowEnabled: false,
  rowAlternationEnabled: true,  
  columnHidingEnabled: true,
  hoverStateEnabled: true,
  width: '100%',
  height:'calc(100vh - 310px)',
  columnAutoWidth: true,
  allowColumnReordering: true,
  allowColumnResizing: true,
  columnResizingMode:'widget',
  headerFilter:{
    visible:true,
    allowSearch:true
  },
  wordWrapEnabled: true,
  paging:{ enabled:true, pageSize:10 },
  scrolling: {
    showScrollbar: 'always',
    useNative: false,
  },
  
}

export function customizeTextValores(cellInfo) {
  if (cellInfo.value !== null && typeof cellInfo.value === 'number') {
      let x = parseFloat(cellInfo.value).toFixed(2)
      return 'Q. ' + x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }
  return null
}
  
export const CustomDevExtremeEsMessages = {
  "dxHtmlEditor-dialogColorCaption": "Cambiar el color de la fuente",
  "dxHtmlEditor-dialogBackgroundCaption": "Cambiar el color de fondo",
  "dxHtmlEditor-dialogLinkCaption": "Añadir enlace",
  "dxHtmlEditor-dialogLinkUrlField": "URL",
  "dxHtmlEditor-dialogLinkTextField": "Texto",
  "dxHtmlEditor-dialogLinkTargetField": "Abrir enlace en nueva ventana",
  "dxHtmlEditor-dialogImageCaption": "Añadir imagen",
  "dxHtmlEditor-dialogImageUrlField": "URL",
  "dxHtmlEditor-dialogImageAltField": "Texto alternativo",
  "dxHtmlEditor-dialogImageWidthField": "Anchura (px)",
  "dxHtmlEditor-dialogImageHeightField": "Estatura (px)",
  "dxHtmlEditor-dialogInsertTableRowsField": "Filas",
  "dxHtmlEditor-dialogInsertTableColumnsField": "Columnas",
  "dxHtmlEditor-dialogInsertTableCaption": "Insertar Tabla",
  "dxHtmlEditor-dialogUpdateImageCaption": "Actualizar Imagen",
  "dxHtmlEditor-dialogImageUpdateButton": "Actualizar",
  "dxHtmlEditor-dialogImageAddButton": "Agregar",
  "dxHtmlEditor-dialogImageSpecifyUrl": "Desde la Web",
  "dxHtmlEditor-dialogImageSelectFile": "Desde este dispositivo",
  "dxHtmlEditor-dialogImageKeepAspectRatio": "Keep Aspect Ratio",
  "dxHtmlEditor-dialogImageEncodeToBase64": "Codificar en Base64",
  "dxHtmlEditor-heading": "Encabezamiento",
  "dxHtmlEditor-normalText": "Texto normal",
  "dxHtmlEditor-background": "Color de fondo",
  "dxHtmlEditor-bold": "Negrilla",
  "dxHtmlEditor-color": "Color de Fuente",
  "dxHtmlEditor-font": "Fuente",
  "dxHtmlEditor-italic": "Cursiva",
  "dxHtmlEditor-link": "Agregar Enlace",
  "dxHtmlEditor-image": "Agregar Imagen",
  "dxHtmlEditor-size": "Tamaño",
  "dxHtmlEditor-strike": "Tachado",
  "dxHtmlEditor-subscript": "Subscript",
  "dxHtmlEditor-superscript": "Superscript",
  "dxHtmlEditor-underline": "Subrayado",
  "dxHtmlEditor-blockquote": "Entre Comillas",
  "dxHtmlEditor-header": "Encabezado",
  "dxHtmlEditor-increaseIndent": "Agregar Identación",
  "dxHtmlEditor-decreaseIndent": "Quitar Identación",
  "dxHtmlEditor-orderedList": "Lista Ordenada",
  "dxHtmlEditor-bulletList": "Bullet List",
  "dxHtmlEditor-alignLeft": "Alinear a la Izquierda",
  "dxHtmlEditor-alignCenter": "Alinear al Centro",
  "dxHtmlEditor-alignRight": "Alinear a la Derecha",
  "dxHtmlEditor-alignJustify": "Justificar",
  "dxHtmlEditor-codeBlock": "Code Block",
  "dxHtmlEditor-variable": "Agregar Variable",
  "dxHtmlEditor-undo": "Deshacer",
  "dxHtmlEditor-redo": "Rehacer",
  "dxHtmlEditor-clear": "Limpiar Formato",
  "dxHtmlEditor-insertTable": "Insertar Tabla",
  "dxHtmlEditor-insertHeaderRow": "Insert Header Row",
  "dxHtmlEditor-insertRowAbove": "Insertar Fila Arriba",
  "dxHtmlEditor-insertRowBelow": "Insertar Fila Debajo",
  "dxHtmlEditor-insertColumnLeft": "Insertar Columna Izquierda",
  "dxHtmlEditor-insertColumnRight": "Insertar Columna Derecha",
  "dxHtmlEditor-deleteColumn": "Borrar Columna",
  "dxHtmlEditor-deleteRow": "Borrar Fila",
  "dxHtmlEditor-deleteTable": "Borrar Tabla",
  "dxHtmlEditor-cellProperties": "Propiedades de Celda",
  "dxHtmlEditor-tableProperties": "Propiedades de Tabla",
  "dxHtmlEditor-insert": "Insertar",
  "dxHtmlEditor-delete": "Borrar",
  "dxHtmlEditor-border": "Borde",
  "dxHtmlEditor-style": "Estilo",
  "dxHtmlEditor-width": "Ancho",
  "dxHtmlEditor-height": "Alto",
  "dxHtmlEditor-borderColor": "Color",
  "dxHtmlEditor-tableBackground": "Background",
  "dxHtmlEditor-dimensions": "Dimensionsiones",
  "dxHtmlEditor-alignment": "Alineación",
  "dxHtmlEditor-horizontal": "Horizontal",
  "dxHtmlEditor-vertical": "Vertical",
  "dxHtmlEditor-paddingVertical": "Vertical Padding",
  "dxHtmlEditor-paddingHorizontal": "Horizontal Padding",
  "dxHtmlEditor-pixels": "Pixeles",
  "dxHtmlEditor-list": "Lista",
  "dxHtmlEditor-ordered": "Ordered",
  "dxHtmlEditor-bullet": "Bullet",
  "dxHtmlEditor-align": "Align",
  "dxHtmlEditor-center": "Center",
  "dxHtmlEditor-left": "Left",
  "dxHtmlEditor-right": "Right",
  "dxHtmlEditor-indent": "Indent",
  "dxHtmlEditor-justify": "Justify",
}
 