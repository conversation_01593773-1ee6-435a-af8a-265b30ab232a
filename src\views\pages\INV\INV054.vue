<template>
    <vx-card title="Devolución Traslados de Sub Bodegas">
        <buscador ref="BuscarUsuarioCorporativo" 
            style="z-index:9999999"
            buscador_titulo="Buscador / Corporativo" :api="'app/Ajenos/Busqueda_Corporativo'" 
            :campos="['Corporativo', ['Nombres', 'Nombre'], ['Apellidos', 'Apellido']]"
            :titulos="['Corporattivo', 'Nombres', 'Apellidos']"
            :api_filtro="{ Corporativo:ParametroCorporativo}"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" :api_preload="true"  />

        <vs-popup classContent="popup-example" title="Enviar Correo Electrónico" :active.sync="popUpEmail"  v-on:keydown.esc="$emit('close')" tabindex="0" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
            <sm-email v-bind=confEnvioCorreo  @success="popUpEmail=false" />
        </vs-popup>

        <vs-popup classContent="popup-example" title="Corporativo Aceptación" :active.sync="popUpCorporativo" style="z-index:999999" id="div-with-loading" class="vs-con-loading__container">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
                    <vs-row vs-align="center">
                        <vs-col vs-type="flex" vs-align="center" vs-w="12">
                            <vs-row vs-align="center">
                                <vs-col vs-type="flex" vs-align="center" vs-w="6">
                                    <label class="typo__label">Corporativo Aceptación:&nbsp;</label>
                                    <vs-input type="number" v-model="ParametroCorporativo" @keyup.enter="BuscarCorporativo()" @keydown.tab="BuscarCorporativo()" ></vs-input>
                                    <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="BuscarCorporativo()" icon="fa-search"></vs-button>
                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="6">
                                    <h5>{{ usuarioAceptacion.Nombres }}  {{ usuarioAceptacion.Apellidos }}</h5>
                                </vs-col>
                            </vs-row>
                        </vs-col>
                    </vs-row>
                    <vs-divider></vs-divider>

                    <div class="w-full md:w-full lg:w-full xl:w-full">
                        <vs-button color="warning"  style="float:right;margin-left:10px;" @click="popUpCorporativo = false;">Cancelar</vs-button>                    </div>
                        <vs-button color="primary" style="float:right" type="filled" icon-pack="feather" icon="icon-save" @click="FinalizarMovimiento()"> Grabar</vs-button>
                    <vs-divider></vs-divider>
            </div>
        </vs-popup>

        <!--------------- Resultado busqueda Producto-------------->
        <vs-popup classContent="popup-example" title="Resultado Búsqueda" :active.sync="VentanaEmergenteProducto"  v-on:keydown.esc="$emit('close')" tabindex="0" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px" >
                <form>
                    <vs-divider></vs-divider>
                    <vs-table2 max-items="10" pagination :data="producto" id="tb_departamentos">

                        <template slot="thead">
                            <th>Linea</th>
                            <th>Codigo</th>
                            <th>Descripcion</th>
                            <th>Concentración</th>
                            <th>Presentación</th>
                            <th>Existencias</th>
                            <th></th>
                        </template>

                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 width='5%'>
                                    {{ indextr+1 }}
                                </vs-td2>
                                <vs-td2 :data="data[indextr].Codigo" width='10%'>
                                    {{data[indextr].Codigo}}
                                </vs-td2>

                                <vs-td2 :data="data[indextr].Descripcion" width='30%'>
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">
                                        {{data[indextr].Descripcion}}
                                    </div>
                                </vs-td2>

                                <vs-td2 :data="data[indextr].Concentracion" width='15%'>
                                    {{data[indextr].Concentracion}}
                                </vs-td2>

                                <vs-td2 :data="data[indextr].PresentacionNombre" width='15%'>
                                    {{data[indextr].PresentacionNombre}}
                                </vs-td2>

                                <vs-td2 style="text-align:right" :data="data[indextr].Existencia" width='15%'>
                                    {{data[indextr].Existencia}}
                                </vs-td2>

                                <vs-td2 style="text-align:right" width='10%'>
                                    <vs-button color="primary" icon-pack="fas" icon="fa-check-circle" style="display:inline-block;margin-right:2px" @click="SeleccionarProducto(data[indextr])"></vs-button>
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
                </form>
            </div>
        </vs-popup>

        <vs-popup classContent="popup-example" :title="descripcionEmergente" :active.sync="popUpNuevoTraslado" style="z-index: 99999">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
                <div style="border:2px solid #1B5E20;border-radius:5px;padding:5px;font-size:12px;background-color: #C8E6C9">
                    Sucursal Origen: <b style="font-size:1vw">{{sesion.sesion_sucursal_nombre}}</b>
                </div>
                <br>
                <div class="flex"> 
                    <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">                         
                    </div>
                    <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2" style="align-self:right; direction: rtl;">
                        <b> <small>Solicitud No. </small></b>&nbsp;
                        <b style="font-size:2vw"><small>{{idSolicitud}}</small></b>
                    </div>
                </div>
                <!--- Ubicación Destino -->
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                    <br>
                    <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                        <label class="typo__label">Bodega Fuente:</label>
                        <strong>{{ bodegaFuente.CODIGO }}</strong>
                        <multiselect
                            v-model="bodegaFuente"
                            :options="listaBodegasFuente"
                            :searchable="true"
                            :close-on-select="true"
                            :show-labels="false"
                            :custom-label="BodegaSeleccionada"
                            @input="onChangeBodegaFuente"
                            placeholder="Seleccionar bodega.."
                            :danger="errors.length > 0"
                            :danger-text="errors.length > 0 ? errors[0] : null"
                            :disabled="deshabilitarBodegaFuente">
                            <span
                            slot="noOptions">Lista no disponible..</span>
                            </multiselect>
                    </ValidationProvider>
                </div>
                <!--- Ubicación Destino -->
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                    <br>
                    <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                        <label class="typo__label">Bodega Destino:</label>       
                        <strong>{{ bodegaDestino.CODIGO }}</strong>                                                 
                        <multiselect
                            v-model="bodegaDestino"
                            :options="listaBodegasDestinoFiltradas"
                            :searchable="true"
                            :close-on-select="true"
                            :show-labels="false"
                            :custom-label="BodegaSeleccionada"
                            placeholder="Seleccionar bodega"
                            @input="onChangeBodegaDestino"
                            :danger="errors.length > 0"
                            :danger-text="errors.length > 0 ? errors[0] : null"
                            :disabled="deshabilitarBodegaDestino">
                            <span
                            slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                    </ValidationProvider>                                                
                </div>

                <br>
                <!--- Producto -->
                <vs-divider>Detalle</vs-divider>

                <div class="flex flex-wrap">

                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <ValidationProvider name="producto" rules="required" v-slot="{ errors }" class="required">
                            <label class="typo__label">Producto</label>
                            <vx-input-group class="">
                                <vs-input id="busquedaProducto" v-model="productoBuscar" 
                                        v-on:keydown.enter="CargarProducto()"
                                        v-on:keydown.tab.prevent="CargarProducto()" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                <template slot="append">
                                    <div class="append-text btn-addon">
                                        <button type="submit" v-show="false" name="button"></button>
                                        <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="CargarProducto()" icon="fa-search"></vs-button>
                                    </div>
                                </template>
                            </vx-input-group>
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                        <ValidationProvider name="cant_solicitada" rules="required|numero_min:0" v-slot="{ errors }" class="required">
                            <label class="typo__label">Cant. Trasladar</label>
                            <vs-input id="cantidad_sol" class="w-full" type="number" count="100" v-model="cantidadSolicitar"  :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                        <div style="word-wrap: break-word;white-space: wrap;">
                            <label v-if="Redondeo(parseFloat(productoSeleccionado.Existencias)) > 0" style="width:100px; border-radius: 3px;  background-color:#69F0AE;text-align: center;color:black;font-size:15px; "> Disponible: {{productoSeleccionado.Existencias}} </label>
                            <label v-else-if="Redondeo(parseFloat(productoSeleccionado.Existencias)) <= 0" style="width:100px; border-radius: 3px;  background-color:#FF5252;text-align: center;color:white;font-size:15px; "> Disponible: {{productoSeleccionado.Existencias}} </label>                              
                        </div>
                    </div>
                    <!------ BOTONES DE ACCION ---->
                    <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6">
                        <vs-button color="primary" style="float:left;margin: 20px" type="filled" icon-pack="fas" icon="fa-plus" id="btn_agregar"  v-on:keydown.tab.prevent="GrabarRegistro()" @click="GrabarRegistro()">Agregar</vs-button>
                    </div>
                </div>

                <!----- Detalle producto seleccionado--->
                <div v-if="productoSeleccionado.Codigo>0" class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                    <!---<div style="border:2px solid #1B5E20;border-radius:5px;padding:5px;font-size:12px;background-color:#A5D6A7">--->
                    <div style="border-radius:5px;padding:5px;font-size:15px;background-color:#A5D6A7">
                        Código: {{productoSeleccionado.Codigo}}
                        <br>
                        Producto: {{productoSeleccionado.marca_comercial}}
                        <br>
                        Presentación: {{productoSeleccionado.Presentacion}}
                        <br>

                    </div>
                </div>
                <!--- Mostrar detalle de la orden--->
                <vs-table2 max-items="10" pagination :data="listaDetalleMovimiento">

                    <template slot="thead">
                        <th>Línea</th>
                        <th>Código</th>
                        <th>Producto</th>
                        <th>Unidad Medida</th>
                        <th>Cant. Trasladar</th>
                        <th></th>
                    </template>

                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2 width='5%'>
                                {{ indextr+1 }}
                            </vs-td2>
                            <vs-td2 width='5%'>
                                {{ tr.IDPRODUCTOFK }}
                            </vs-td2>                                 
                            <vs-td2 width='65%'>
                                <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                                        
                                    {{tr.DESCRIPCION_PRODUCTO}}                                        
                                </div>
                            </vs-td2>
                            <vs-td2 width='15%'>
                                <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">
                                    {{tr.DESCRIPCION_UNIDAD}}
                                </div>                                    
                            </vs-td2>
                            <vs-td2 width='5%'>
                                {{tr.CANTIDAD_PEDIDA}}
                            </vs-td2>
                            <vs-td2 width='5%'>
                                <vx-tooltip text="Eliminar" style="display:inline-block">
                                    <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="EliminarProducto(data[indextr])"></vs-button>
                                </vx-tooltip>
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>

                <vs-divider></vs-divider>
                <div class="flex flex-wrap">
                    <div class="w-full md:w-full lg:w-full xl:w-full">
                        <vs-textarea  label="Observaciones" type="text" counter="200"  class="w-full" v-model="observaciones" />
                    </div>
                </div>
                <vs-button
                    color="primary"
                    style="float:right;margin: 5px"
                    type="filled"
                    icon-pack="feather"
                    icon="icon-save"
                    id="btn_confirmacion"
                    @click="ValidarMovimiento()"> Realizar Despacho/Aceptación</vs-button>
                <vs-divider></vs-divider>
            </div>
        </vs-popup>

        <!----------------------- Consulta de movimientos ---------->
        <vs-popup classContent="popup-example" :title="descripcionEmergente" :active.sync="popUpConsultaAceptacionTraslado" style="z-index: 99999">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">   
                <div style="border:2px solid #1B5E20;border-radius:5px;padding:5px;font-size:12px;background-color: #C8E6C9">
                    Sucursal Origen: <b style="font-size:1vw">{{sesion.sesion_sucursal_nombre}}</b>
                </div>  
                <div class="flex">
                    <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <b> Movimiento No. </b>&nbsp;
                        <b style="font-size:2vw">{{idMovimiento}}</b>
                    </div>   
                    <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2" style="align-self:right; direction: rtl;">
                        <b> <small>Solicitud No. </small></b>&nbsp;
                        <b style="font-size:2vw"><small>{{idSolicitud}}</small></b>
                    </div>
                </div>
                <div class="w-1/2 md:w-1/2 lg:w-6/12 xl:w-6/12">
                        <b> <small>Fecha Solicitud: </small></b>
                    <b>{{movimientoSeleccionado.FECHAFINALIZADO}}</b>
                </div>
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2" v-if="consultaPedido || consultaAceptado">
                    <b> <small>Estado: </small></b>&nbsp;
                    <b>{{estadoMovimiento}}</b>
                </div> 

                <!--- Ubicación Destino -->
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                    <br>
                    <label class="typo__label">Bodega Fuente:</label>
                    <strong>{{idBodegaFuente}}</strong>
                    <vs-input  type="text" v-model="bodegaFuente.NOMBRE" :disabled="deshabilitarBodegaFuente" class="w-full"/>
                </div>  
                <br>                      
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                    <br>
                    <label class="typo__label">Bodega Destino:</label>
                    <strong>{{idBodegaDestino}}</strong>
                    <vs-input  type="text" v-model="bodegaDestino.NOMBRE" :disabled="deshabilitarBodegaDestino" class="w-full"/>
                </div>
                <vs-divider>Detalle</vs-divider>

                <!--- Mostrar detalle de la orden--->
                <vs-table2 max-items="10" pagination :data="listaDetalleMovimiento">

                    <template slot="thead">
                        <th>Linea</th>
                        <th>Codigo</th>
                        <th>Producto</th>
                        <th>Unidad Medida</th>
                        <th>Cant. Trasladar</th>
                        <th v-if="!consultaPedido">Cant. Aceptada</th>
                    </template>

                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2 width='5%'>
                                {{ indextr+1 }}
                            </vs-td2>
                            <vs-td2 width='5%'>
                                {{ tr.IDPRODUCTOFK }}
                            </vs-td2>  
                            <vs-td2 width='50%'>
                                <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                                        
                                    {{tr.DESCRIPCION_PRODUCTO}}                                        
                                </div>
                            </vs-td2>
                            <vs-td2 width='15%'>
                                <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                                        
                                    {{tr.DESCRIPCION_UNIDAD}}
                                </div>
                            </vs-td2>
                            <vs-td2 width='10%'>
                                {{tr.CANTIDAD_PEDIDA}}
                            </vs-td2>
                            <vs-td2 v-if="!consultaPedido" width='10%'>
                                {{ tr.CANTIDAD_RECIBIDA }}   
                            </vs-td2>
                            <!-- <vs-switch v-if="tr.FINALIZADO ==='N'" style="margin-top:10px;margin-left:20px" v-model="tr.ESTADO_SWITCH" @input="Registrar_RecibidoDet(tr, 'R')" /> -->  
                        </tr>
                    </template>
                </vs-table2>
                <vs-divider></vs-divider>
                <div class="flex flex-wrap">
                    <div class="w-full md:w-full lg:w-full xl:w-full">
                        <vs-textarea  disabled label="Observaciones" type="text"  class="w-full" v-model="observaciones" />
                    </div>
                </div>
                <vs-divider></vs-divider>
                <div class="flex">
                    <div class="w-full">
                        <b><small >Trasladado por: </small></b>&nbsp;
                        <b>{{movimientoSeleccionado.CORPORATIVO_SOLICITANTE +' - '+movimientoSeleccionado.IDCORPORATIVOSOLICITAFK}}</b>&nbsp;
                        <b><small v-if="movimientoSeleccionado.ESTADO=='F'">/ Aceptado por: </small></b>&nbsp;
                        <b v-if="movimientoSeleccionado.ESTADO=='F'">{{movimientoSeleccionado.CORPORATIVO_FINALIZA +' - '+movimientoSeleccionado.IDCORPORATIVOFINALIZAFK}}</b>
                    </div> 
                </div>             
                <vs-divider></vs-divider>
                <div>
                    <vs-button v-if="!consultaPedido && !consultaAceptado" color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" id="btn_confirmacion" @click="MovimientoRecepcion()">Aceptar Despacho</vs-button>
                    <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-printer" id="btn_movimiento" @click="ConsultarMovimientoReporte(movimientoSeleccionado)"> Imprimir </vs-button>                                        
                </div>
                <vs-divider></vs-divider>
            </div>
        </vs-popup>

        <div class="content content-pagex">
            <vs-divider></vs-divider>
            <br>
            <div class="flex flex-wrap">                
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                    <label class="typo__label">Bodega Fuente:</label>      
                    <strong>{{ bodegaFuente.CODIGO }}</strong>  
                    <multiselect
                        v-model="bodegaFuente"
                        :options="listaBodegasFuente"
                        :show-labels="false"
                        :allow-empty="false"                        
                        :custom-label="BodegaSeleccionada"
                        placeholder="Seleccionar bodega fuente..."
                        @input="onChangeBodegaFuente"
                        >
                    </multiselect>     
                </div>
            </div>
            <div class="flex flex-wrap">
    
                <div style="margin-right: 20px" class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <label class="typo__label">Estado</label>
                    <multiselect
                    v-model="estadoActual"
                    :options="listaEstado"
                    :searchable="true"
                    :close-on-select="true"
                    :show-labels="false"
                    :allow-empty="false"
                    :custom-label="operacionSeleccionada"
                    placeholder="Seleccionar estado..."
                    @input="onChangeEstado">
                    <span slot="noOptions">Estados no disponibles</span>
                    </multiselect>
                </div>

                <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7 m-1">
                    <label class="typo__label" >Fecha Inicial:</label>                    
                    <vs-input type="date" v-model="fechaInicio" name="date1" />
                </div>
    
                <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7 m-1">
                    <label class="typo__label" >Fecha Final:</label>                    
                    <vs-input type="date" v-model="fechaFinal" name="date2" />
                </div>

                <vs-button style="float:left;margin: 20px" color="primary" type="filled" icon-pack="fas" icon="fa-search" @click="ConsultaMovimientosEncabezado()"> Búsqueda</vs-button>
    
                <vs-button color="primary" style="float:left;margin: 20px" type="filled" icon-pack="fas" icon="fa-plus" @click="CrearMovimiento('','N')"> Nuevo</vs-button>                                               
                
    
            </div>
            <vs-alert v-if="bodegaFuente.VisualizarTraslados && bodegaFuente.VisualizarTraslados == 'S'" :active.sync="Alertas[0]" color="success" icon-pack="feather" icon="icon-info" closable close-icon="icon-x" class="mb-1">
                Se muestran todos los traslados para los estados: Traslados, Aceptado, Anulado
            </vs-alert>
            <vs-divider></vs-divider>
    
            <vs-table2 max-items="10" pagination :data="listaMovimientosEncabezado" search>
    
                <template slot="thead">
                    <th width="130px">Nº. Movimiento</th>
                    <th width="130px">Nº. Solicitud</th>
                    <th>Destino</th>
                    <th>Fecha(s)</th>
                    <th>Observaciones</th>
                    <th></th>
    
                </template>
    
                <template slot-scope="{data}" :allowResizing='true'>
                    <tr :key="indextr" v-for="(tr, indextr) in data">
    
                        <vs-td2 width='5%'>
                            {{tr.IDMOVIMIENTOFK}}
                        </vs-td2>

                        <vs-td2 width='5%'>
                            {{tr.IDREQUISICIONENC}}
                        </vs-td2>
    
                        <vs-td2 width='15%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                {{tr.IdBodegaDestinofk +' - '+ tr.NOMBRE_BODEGA_DESTINO}}
                            </div>
                        </vs-td2>
    
                        <vs-td2 width='25%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                <label v-if="idEstadoSeleccionado  === 'P'">Creado: {{tr.FECHASOLICITUD}}</label>
                                <br v-if="idEstadoSeleccionado  === 'P'">
                                <label v-if="tr.FECHAFINALIZADO">{{'Trasladado: '+tr.FECHAFINALIZADO+' - Usuario: '+tr.IDCORPORATIVOSOLICITAFK}}</label>
                                <br v-if="tr.FECHAFINALIZADO">
                                <label v-if="tr.FECHARECIBIDA">{{'Aceptado: '+tr.FECHARECIBIDA+' - Usuario: '+tr.IDCORPORATIVOFINALIZAFK}}</label>
                                <br v-if="tr.FECHARECIBIDA">
                                <label v-if="tr.FECHAANULACION">Anulado: {{tr.FECHAANULACION}}</label>
                            </div>    
                        </vs-td2>

                        <vs-td2  width='35%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                {{ tr.OBSERVACIONES }}
                            </div>
                        </vs-td2>  
    
                        <vs-td2 v-if="idEstadoSeleccionado  == 'P'" width='15%' align="right"  style="white-space:nowrap;min-width: 200px;">
                            <vx-tooltip text="Consultar" style="display:inline-block;margin-right:20px">
                                <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" @click="CrearMovimiento(data[indextr], 'E')"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip text="Anular" style="display:inline-block;margin-right:2px">
                                <vs-button color="red" icon-pack="fas" icon="fa-times"   @click="AnularMovimiento(data[indextr])"></vs-button>
                            </vx-tooltip>
                        </vs-td2>
                        <vs-td2 v-else-if="idEstadoSeleccionado  == 'E'" width='15%%' align="right" style="white-space:nowrap;min-width: 200px;">
                            <vx-tooltip text="Consultar" style="display:inline-block;margin-right:20px">
                                <vs-button color="primary" icon-pack="fas" icon="fa-eye" @click="VisualizarAceptarMovmiento(data[indextr],'CONSULTA')"></vs-button>                              
                            </vx-tooltip>
                            <vx-tooltip text="Imprimir" style="display:inline-block;margin-right:20px">
                                <vs-button color="primary" icon-pack="feather" icon="icon-printer" @click="ConsultarMovimientoReporte(data[indextr])"></vs-button>
                            </vx-tooltip>  
                            <vx-tooltip text="Anular" style="display:inline-block;margin-right:2px">
                                <vs-button color="red" icon-pack="fas" icon="fa-times"   @click="AnularMovimiento(data[indextr])"></vs-button>
                            </vx-tooltip>
                        </vs-td2>                      
                        <vs-td2 v-else-if="idEstadoSeleccionado  == 'F'" width='10%' align="right" style="white-space:nowrap;min-width: 200px;">
                            <vx-tooltip text="Consultar" style="display:inline-block;margin-right:30px">
                                <vs-button color="primary" icon-pack="fas" icon="fa-eye" @click="VisualizarAceptarMovmiento(data[indextr],'CONSULTA')"></vs-button>    
                            </vx-tooltip>
                            <vx-tooltip text="Imprimir" style="display:inline-block;margin-right:2px">
                                <vs-button color="primary" icon-pack="feather" icon="icon-printer" @click="ConsultarMovimientoReporte(data[indextr])"></vs-button>
                            </vx-tooltip>
                        </vs-td2>
    
                    </tr>
                </template>
            </vs-table2>

        </div>
    </vx-card>
</template>
    
<script>
import Multiselect from 'vue-multiselect'
import moment from "moment"
import "vue-multiselect/dist/vue-multiselect.min.css"    
export default {
    components: {
        Multiselect
    },
    watch:{
        popUpCorporativo(value){
            if(!value){
                this.ParametroCorporativo = null
                this.usuarioAceptacion.Corporativo = null
                this.usuarioAceptacion.Status = 'A'
                this.usuarioAceptacion.Nombres = null
                this.usuarioAceptacion.Apellidos = null
                this.usuarioAceptacion.Correo = null
                this.usuarioAceptacion.IdPuesto = null
                this.usuarioAceptacion.DPI = null
                this.usuarioAceptacion.NIT = null
                this.usuarioAceptacion.Ajeno = null
                this.usuarioAceptacion.Indicio = null                
            }
        },
        popUpEmail(value){
            if(!value){
                this.popUpNuevoTraslado = false;
                this.ConsultaMovimientosEncabezado();  
            }            
        },
        popUpNuevoTraslado(value){
            if(!value){
                this.insertoEncabezado = false;
                this.bodegaDestino = {CODIGO:"",NOMBRE:""}
            }                
        },
        popUpConsultaAceptacionTraslado(value){
            if(!value){
                this.insertoEncabezado = false;
                this.bodegaDestino = {CODIGO:"",NOMBRE:""}
            }     
        }
    },     
    data() {
        return {
            ParametroCorporativo:null,
            usuarioAceptacion: {
                Corporativo: null,
                Status: 'A',
                Nombres: null,
                Apellidos: null,
                Correo: null,
                IdPuesto: null,
                DPI: null,
                NIT: null,
                Ajeno: null,
                Indicio: null
            },
            confEnvioCorreo: {
                to: [],
                cc:  [],
                bcc:  [],
                msg: "",
                sbjct: "Traslado Inter Farmacia",
                maxAttachment: 3, 
                attachTypes: '.pdf, .xlsx',
                allowAddAttachment: false,
                allowDeleteAttachment: false,
                allowEditMsg:true,
                allowEditSbjct:false,
                NombreDelArchivo:"Movimiento Requerimiento.pdf",
                reporte: {
                    Nombre: "Movimiento Requerimiento",
                    Opciones: null,
                    Formato: "PDF"
                } 
            },
            estadoActual:'',
            bodegaFuente:'',
            bodegaDestino:'',    
            Alertas: [false],
            popUpNuevoTraslado: false,   
            popUpConsultaAceptacionTraslado: false,    
            popUpEmail:false,     
            popUpCorporativo:false,  
            listaEstado: [
                {
                    ID: 'P',
                    DESCRIPCION: 'Edición'
                },
                {
                    ID: 'F',
                    DESCRIPCION: 'Trasladado/Aceptado'
                }
            ],
            listaBodegasDestino:[],
            listaBodegasDestinoFiltradas:[],
            listaBodegasFuente:[],
            listaMovimientosEncabezado:[],
            listaDetalleMovimiento:[],
            permisosTipoBodegas:[],
            fechaInicio: '',
            fechaFinal: '',
            idBodegaFuente: null,  
            idBodegaDestino: null,
            idSolicitud: null,
            idMovimiento: null,
            idEstadoSeleccionado: null,
            trasladoAnular:{},
            motivoAnulacion:'',
            movimientoSeleccionado: {},             
            productoSeleccionado: {
                nombre: '',
                marca_comercial: '',
                Principio_activo: '',
                Concentracion: '',
                Presentacion: '',
                Codigo: '',
                codigo_interno: '',
                Activo: 'N',
                Existencias:''
            }, 
            productoBuscar: '',
            producto: [],  
            cantidadSolicitar:'',
            existenciasProducto:'',
            observaciones:'',            
            descripcionEmergente:'',
            deshabilitarBodegaFuente:true,
            deshabilitarBodegaDestino:true,
            VentanaEmergenteProducto:false,   
            insertoEncabezado:false,
            consultaPedido:false,
            consultaAceptado:false,
            listaParametrosBaseReportes:{},
            listaParametrosReportes:{},
            reporte:{},
            listaCorreos:[],
            permisos_tipos_bodegas:[]     
        }
    },
    async mounted() {
        this.estadoActual = this.listaEstado[0]
        this.idEstadoSeleccionado = 'P';  
        for(let privilegio of this.$store.state.privilegios){
            if(privilegio.Privilegio.includes("TIPO_BODEGA")){                      
                this.permisos_tipos_bodegas.push(privilegio.Privilegio.split('_')[2])                    
            }
        }        

        this.ConsultarBodega('H',this.permisos_tipos_bodegas.join(","));
        this.ConsultarBodega('K',this.permisos_tipos_bodegas.join(","));      
        //this.listaCorreos = await this.ConsultarCorreos();
    },
    computed: {
        sesion() {
            return this.$store.state.sesion
        }
    },
    async beforeCreate(){
        this.listaParametrosBaseReportes = await this.$recupera_parametros_reporte('Movimiento Requerimiento')        
    },
    methods: {   
        EliminarProducto(value) {
            /**
             * @General
             * Función eliminar registro;
             */

            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Confirmación',
                acceptText: 'Aceptar',
                cancelText: 'Cancelar',
                text: '¿Dar de baja Pedido  \'' + value.DESCRIPCION_PRODUCTO + '\'   \'' + value.DESCRIPCION_UNIDAD + '\' ? ',
                accept: () => {

                    this.axios.post('/app/v1_OrdenCompra/Requisicion_Det', {
                            Requisicion_det: value.IDREQUISICIONDET,
                            Requisicion_enc: this.idSolicitud,
                            Producto: '0',
                            cant_pedida: 0,
                            cant_entregada: 0,
                            cant_recibida: 0,
                            Operacion: 'B'
                        })
                        .then(resp => {

                            if (resp.data.codigo == 0) {
                                this.ConsultarDetalleMovimiento();
                                document.getElementById("busquedaProducto").focus();
                            }
                        })

                }
            })

        },
        BuscarCorporativo(){
            if(this.ParametroCorporativo != '' && this.ParametroCorporativo != null && this.ParametroCorporativo != 0){
                this.axios.post('/app/administracion/CorporativoConsulta', {
                    IdCorporativo: this.ParametroCorporativo
                })
                .then(resp => {
                    if(resp.data.codigo == 0){
                        if(resp.data.json.length == 1){
                            this.usuarioAceptacion = resp.data.json[0]
                        }else{
                            this.$refs.BuscarUsuarioCorporativo.iniciar((data) => {
                                if(data != null){
                                    this.axios.post('/app/administracion/CorporativoConsulta', {
                                        IdCorporativo: data.Corporativo
                                    })
                                    .then(resp => {
                                        this.usuarioAceptacion = resp.data.json[0]
                                        this.ParametroCorporativo = this.usuarioAceptacion.Corporativo
                                    })
                                }
                            })
                        }
                    }
                })
            }else{
                this.ParametroCorporativo = null
                this.$refs.BuscarUsuarioCorporativo.iniciar((data) => {
                    if(data != null){
                        this.axios.post('/app/administracion/CorporativoConsulta', {
                            IdCorporativo: data.Corporativo
                        })
                        .then(resp => {
                            this.usuarioAceptacion = resp.data.json[0]
                            this.ParametroCorporativo = this.usuarioAceptacion.Corporativo
                        })
                    }
                })
            }
        }, 
        async CargaReportePorCorreo(datos,titulo = 'Traslado Inter Farmacia'){
            this.listaParametrosReportes.CODIGO_MOVIMIENTO = datos.IDMOVIMIENTOFK
            this.listaParametrosReportes.CODIGO_SOLICITUD = datos.IDREQUISICIONENC            
            let opciones =  await this.$prepara_valores_reporte({
                                Nombre: "Movimiento Requerimiento",
                                Data_source: this.listaParametrosReportes,
                                Data_report: this.listaParametrosBaseReportes                                
                            })            
            this.confEnvioCorreo.reporte.Opciones = opciones   
            this.confEnvioCorreo.sbjct = titulo    
            //this.confEnvioCorreo.reporte.Opciones.tiporeporte = "application/vnd.ms-excel"     
            //this.confEnvioCorreo.reporte.Opciones.NombreDelArchivo = "Movimiento Requerimiento.xlsx"   
            this.popUpCorporativo = false  
            this.popUpEmail = true 
            this.$vs.loading.close();    
        },               
        async ConsultarMovimientoReporte(datos) {                
            this.listaParametrosReportes.CODIGO_MOVIMIENTO = datos.IDMOVIMIENTOFK
            this.listaParametrosReportes.CODIGO_SOLICITUD = datos.IDREQUISICIONENC
            this.$genera_reporte({
                Nombre: "Movimiento Requerimiento",
                Data_source: this.listaParametrosReportes,
                Data_report: this.listaParametrosBaseReportes
            }).catch(() => {
            })
        },    
        GetDateValue(value) {
            if (value === undefined) {
                return '';
            } else if (value === null) {
                return '';
            } else if (value == '') {
                return '';
            }
            return moment(value).format('DD/MM/YYYY');          
        },  
        ConsultarBodega(Operacion,TipoBodegas) {
            const url = this.$store.state.global.url
            this.axios.post(url+'app/v1_OrdenCompra/consulta_bodega', {
                                                                    operacion: Operacion,
                                                                    lista_tipo_bodegas: TipoBodegas,
                                                                    BodegasDepacho: TipoBodegas
                                                                    })
                        .then(resp => {
        
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    position:'top-center',
                                    color: '#B71C1C',
                                    title: 'Bodegas',
                                    text: resp.data.mensaje,
                                })
                            } else {
                                if(Operacion == 'H'){
                                    this.listaBodegasDestino = resp.data.json;                                  
                                }
                                else if(Operacion == 'K'){
                                    this.listaBodegasFuente = resp.data.json;                         
                                }
                                    
                            }
                        })
                        .catch(() => { })
    
        },
        BodegaSeleccionada({CODIGO,NOMBRE}) {
            return `${CODIGO} - ${NOMBRE} `;
        },
        onChangeBodegaFuente(value) {
            if (value !== null && value.length !== 0) {
                this.Alertas[0] = true;
                this.idBodegaFuente = value.CODIGO;
                this.listaBodegasDestinoFiltradas = this.listaBodegasDestino.filter(bodega=>bodega.TipoBodega == value.TipoBodega);
                this.bodegaDestino = {CODIGO:"",NOMBRE:""}
                this.ConsultaMovimientosEncabezado();
            } else {
                this.idBodegaFuente = null;
            }
        },
        onChangeBodegaDestino(value) {                
            if (value !== null && value.length !== 0) {
                this.idBodegaDestino = value.CODIGO;
            } else {
                this.idBodegaDestino = null;
            }
        },
        operacionSeleccionada({DESCRIPCION}) {
            return `${DESCRIPCION} `
        },
        onChangeEstado(value) {
            if (value !== null && value.length !== 0) {
                if (value.ID == 'S')
                    this.idEstadoSeleccionado = 'P';
                else
                    this.idEstadoSeleccionado = value.ID;
                this.ConsultaMovimientosEncabezado();
            } else {
                this.idEstadoSeleccionado = '';
            }
        },
        ConsultaMovimientosEncabezado() {
            if(!this.bodegaFuente){
                this.$vs.notify({
                    position:'top-center',
                            color: 'danger',
                            title: 'Inventario',
                            text: 'Seleccione una bodega fuente',
                        })
                return
            }
            
            var operacion = 'P'
            if(this.idEstadoSeleccionado=='P'){
                operacion = 'T'
            }
            
            this.axios.post('/app/v1_OrdenCompra/ConsultaRequisicion_Enc', {                        
                    Estado: this.idEstadoSeleccionado,
                    fecha_inicio: this.GetDateValue(this.fechaInicio),
                    fecha_final: this.GetDateValue(this.fechaFinal),
                    Operacion: operacion,
                    Bodega_fuente: this.bodegaFuente.CODIGO,                    
                    TipoMovimiento: '4'
                })
                .then(resp => {

                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                                            position:'top-center',
                                            color: '#B71C1C',
                                            title: 'Inventario',
                                            text: resp.data.mensaje,
                                        })
                        //Limpia la tabla si no existen registros
                        this.listaMovimientosEncabezado = [];
                    } else {
                        this.$vs.notify({
                                            position:'top-center',
                                            color: 'success',
                                            title: 'Inventario',
                                            text: 'Se actualizaron las devoluciones exitosamente',
                                        })
                        this.listaMovimientosEncabezado = resp.data.json;

                    }
                })
                .catch(() => {
                    this.listaMovimientosEncabezado = [];
                })
        },     
        ConsultarDetalleMovimiento() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_OrdenCompra/ConsultaRequisicion_Det', {
                    Requisicion_enc: this.idSolicitud
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.listaDetalleMovimiento = [];
                    } else {
                        this.listaDetalleMovimiento = resp.data.json;
                    }
                })
                .catch(() => {
                    this.listaDetalleMovimiento = [];
                })
        }, 
        ValidarMovimiento(){
            var res_variables = false;
                res_variables = this.ValidacionCampos('ID', 'Solicitud No.', this.idSolicitud, true, 0);

            if(!res_variables) return

            res_variables = this.ValidacionCampos('CAD', 'Observaciones', this.observaciones, true, 200);
            if(!res_variables) return

            if (this.listaDetalleMovimiento.length <= 0) {
                this.$vs.notify({
                position:'top-center',
                    color: '#B71C1C',
                    title: 'Inventario',
                    text: 'Debe tener al menos un producto a trasladar',
                })
                return;
            }

            this.popUpCorporativo = true;
        },
        FinalizarMovimiento() {
            var res_variables = false;
                res_variables = this.ValidacionCampos('ID', 'Solicitud No.', this.idSolicitud, true, 0);

            if(!res_variables) return

            res_variables  = this.ValidacionCampos('ID', 'Corporativo Aceptación', this.usuarioAceptacion.Corporativo, true, 0);
            if(!res_variables) return

            res_variables = this.ValidacionCampos('CAD', 'Observaciones', this.observaciones, true, 200);
            if(!res_variables) return

            if (this.listaDetalleMovimiento.length <= 0) {
                this.$vs.notify({
                position:'top-center',
                    color: '#B71C1C',
                    title: 'Inventario',
                    text: 'Debe tener al menos un producto a trasladar',
                })
                return;
            }

            this.axios.post('/app/v1_OrdenCompra/MovimientoUnPaso', {
                    Requisicion_enc: this.idSolicitud,
                    Productos: this.listaDetalleMovimiento.map((producto)=>producto.IDPRODUCTOFK).toString(),
                    CantidadesDespacho: this.listaDetalleMovimiento.map((producto)=>producto.CANTIDAD_PEDIDA).toString(),
                    NumerosRequisicionDet: this.listaDetalleMovimiento.map((producto)=>producto.IDREQUISICIONDET).toString(),                                                
                    Total: 0,
                    Cant_lineas: this.listaDetalleMovimiento.length,
                    Observaciones: this.observaciones,            
                    Estado: 'E',
                    Operacion: 'F',
                    Corporativo_finaliza: this.usuarioAceptacion.Corporativo
                })
                .then(resp => {                            
                    if (resp.data.codigo == 0) {  
                        this.popUpCorporativo = false
                        this.popUpNuevoTraslado = false
                        this.ConsultaMovimientosEncabezado() 
                    }
                })
            
        }, 
        MensajeCorreo(noSolicitud,bodegaFuente,bodegaDestino){
            return `Se le informa que tiene una SOLICITUD de traslado de PRODUCTO generada en Solicitud No. '${noSolicitud}' De: ${bodegaFuente} Hacia: ${bodegaDestino}. Se adjunta el detalle de la solicitud.`
        },
        MensajeCorreoAnulacion(noSolicitud,bodegaFuente,bodegaDestino,motivo){
            return `Se le informa que tiene un INGRESO de traslado de PRODUCTO que ha sido ANULADO, Solicitud No. '${noSolicitud}' De: ${bodegaFuente} Hacia: ${bodegaDestino}\n\nMotivo: ${motivo} Por el Usuario con corporativo: ${this.sesion.corporativo}\n\nSe adjunta el detalle de la solicitud.`
        },
        async ConsultarCorreos(){
            let correos = []
            correos = await this.axios
                                .post('/app/v1_OrdenCompra/ValoresAgrupacion',{agrupacion:'CorreoInterFarmacia'})
                                .then( resp => { 
                                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                        return resp.data.json.map(correos=>correos.valor)
                                    }
                                    return []
                                })
                                .catch( () =>{return []})
            return correos;                                
        },
        AnularMovimiento(Datos){
            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Confirmación Anulación',
                acceptText: 'Anular',
                cancelText: 'Cancelar',
                text: `¿Desea anular la solicitud No. ${Datos.IDREQUISICIONENC}?`,
                accept: () => {
                    return this.axios.post('/app/v1_OrdenCompra/Requisicion_Enc', {
                        Requisicion_enc: Datos.IDREQUISICIONENC,       
                        Operacion: 'A'
                    }).then(resp =>{
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                position:'top-center',
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: resp.data.mensaje,
                            })
                        } else {
                            this.$vs.notify({
                                position:'top-center',
                                color: 'sucess',
                                title: 'Anulación',
                                text: resp.data.mensaje
                            })    
                            this.ConsultaMovimientosEncabezado();
                        }
                    })
                }
            })
        },
        CrearMovimiento(Datos, Operacion) {                
            this.LimpiarCamposProducto();   
            this.movimientoSeleccionado = Datos                
            this.productoBuscar = '';
            this.cantidadSolicitar = '';
            if (Operacion == 'N') {                                        
                this.idSolicitud = 0;
                this.idMovimiento = 0;
                this.observaciones = '';
                this.deshabilitarBodegaFuente = true;
                this.deshabilitarBodegaDestino = false;
                this.descripcionEmergente = 'Ingreso Devolución Traslado';
                this.listaDetalleMovimiento = [];    
            } else {                                
                this.idSolicitud = Datos.IDREQUISICIONENC;
                this.idMovimiento = Datos.IDMOVIMIENTOFK;
                this.idBodegaFuente = Datos.IdBodegaFuentefk;
                this.idBodegaDestino = Datos.IdBodegaDestinofk;
                this.observaciones = Datos.OBSERVACIONES;
                setTimeout(() => {
                    this.bodegaFuente = {
                        NOMBRE: Datos.NOMBRE_BODEGA_FUENTE,
                        CODIGO: Datos.IdBodegaFuentefk,
                        TipoBodega: Datos.TIPO_BODEGA_FUENTE                        
                    }
                    this.bodegaDestino = {
                        NOMBRE: Datos.NOMBRE_BODEGA_DESTINO,
                        CODIGO: Datos.IdBodegaDestinofk,
                        TipoBodega: Datos.TIPO_BODEGA_DESTINO
                    }
                }, 500);                    
                this.deshabilitarBodegaFuente = true;
                this.deshabilitarBodegaDestino = true;
                this.descripcionEmergente = 'Editar Devolución Traslado';
                this.ConsultarDetalleMovimiento()
            }
            this.popUpNuevoTraslado = true;
        },
        VisualizarAceptarMovmiento(Datos,Operacion) {            
            if(Operacion == 'CONSULTA'){
                this.descripcionEmergente = 'Consulta Devolución Traslado';
                this.consultaPedido = true
                this.consultaAceptado = false
                this.deshabilitarIngresoAceptacion = true
            }

            if(Operacion == 'ACEPTACION'){
                this.descripcionEmergente = 'Aceptación Traslado';
                this.consultaPedido = false
                this.consultaAceptado = true
                this.deshabilitarIngresoAceptacion = false
            }

            this.movimientoSeleccionado = Datos
            this.observaciones = Datos.OBSERVACIONES;
            this.idSolicitud = Datos.IDREQUISICIONENC;
            this.estadoMovimiento = Datos.ESTADOREQUISICION;
            this.idMovimiento = Datos.IDMOVIMIENTOFK;
            this.idBodegaFuente = Datos.IdBodegaFuentefk;
            this.idBodegaDestino = Datos.IdBodegaDestinofk;
            this.bodegaDestino = { CODIGO: Datos.IdBodegaDestinofk,
                                   NOMBRE: Datos.NOMBRE_BODEGA_DESTINO
                                 }
            this.deshabilitarBodegaDestino = true;
            this.deshabilitarBodegaFuente = true;                      
            this.ConsultarDetalleMovimiento()

            this.popUpConsultaAceptacionTraslado = true;
        },
        LimpiarCamposProducto() {
            this.productoSeleccionado.Codigo = '';
            this.productoSeleccionado.marca_comercial = '';
            this.productoSeleccionado.Principio_activo = '';
            this.productoSeleccionado.Concentracion = '';
            this.productoSeleccionado.codigo_interno = '';
            this.productoSeleccionado.Presentacion = '';
            this.productoSeleccionado.Existencias = '';
        },
        SeleccionarProducto(obj) {    
            this.productoSeleccionado.Codigo = obj.Codigo;
            this.productoSeleccionado.marca_comercial = obj.Descripcion;
            this.productoSeleccionado.Concentracion = obj.Concentracion;
            this.productoSeleccionado.codigo_interno = obj.codigo_interno;
            this.productoSeleccionado.Presentacion = obj.PresentacionNombre;
            this.productoSeleccionado.Existencias = obj.Existencia;
            document.getElementById("cantidad_sol").focus();
            this.VentanaEmergenteProducto = false;
        },
        CargarProducto() {    
            this.LimpiarCamposProducto();
            let res_variables = false;
                res_variables = this.ValidacionCampos('ID', 'Bodega Destino', this.bodegaDestino, true, 0);
            if (!res_variables) return
                res_variables = this.ValidacionCampos('ID', 'Bodega Destino', this.bodegaDestino.CODIGO, true, 0);
            if (!res_variables) return
                res_variables = this.ValidacionCampos('ID', 'Bodega Fuente', this.bodegaFuente, true, 0);                        
            if (!res_variables) return
                res_variables = this.ValidacionCampos('ID', 'Bodega Fuente', this.bodegaFuente.CODIGO, true, 0);
            if(!res_variables) return
                res_variables = this.ValidacionCampos('C', 'Tipo Bodega', this.bodegaFuente.TipoBodega, true, 5);
            if(!res_variables) return                

            this.axios.post('/app/v1_OrdenCompra/ConsultaProductosExistenciaBodegaFuente', {
                    Producto: this.productoBuscar.trim(),
                    TipoBodega: this.bodegaFuente.TipoBodega,
                    bodega_fuente:this.bodegaFuente.CODIGO
                })
                .then(resp => {

                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        this.producto = []

                         if(resp.data.json.length==1 && resp.data.json[0].tipo_error == "-1"){
                            this.$vs.notify({
                                time: 5000,
                                position:'top-center',
                                color: 'danger',
                                title: 'Producto',
                                text: resp.data.json[0].descripcion,
                            })
                            return
                        }

                        resp.data.json.map(data => {
                            this.producto.push({
                                ...data
                            })
                        })

                        this.MostrarResultadoProducto(this.producto,this.productoBuscar.trim());

                    } else {

                        this.producto = []
                        this.$vs.notify({
                            position:'top-center',
                            color: 'danger',
                            title: 'Producto',
                            text: (resp.data.codigo < 0) ? resp.data.descripcion : 'Código no existe, es un servicio ó no esta activo',
                        })

                    }

                })
                .catch(() => {
                    this.$vs.loading.close();
                })

        },
        MostrarResultadoProducto(value,codigo_busqueda) {                
            if (value.length === 1) {
                
                if(codigo_busqueda != value[0].Codigo){
                    this.$vs.notify({
                        timer:6000,
                        position:'top-center',
                        color: 'warning',
                        title: 'Inventario',
                        text: `El código encontrado (${value[0].Codigo}) no corresponde exactamente al buscado.`,
                    })
                }

                value.map(obj => {
                    this.productoSeleccionado.Codigo = obj.Codigo;
                    this.productoSeleccionado.marca_comercial = obj.Descripcion;
                    this.productoSeleccionado.Concentracion = obj.Concentracion;
                    this.productoSeleccionado.codigo_interno = obj.codigo_interno;
                    this.productoSeleccionado.Presentacion = obj.PresentacionNombre;     
                    this.productoSeleccionado.Existencias = obj.Existencia;                   
                    document.getElementById("cantidad_sol").focus();

                })

            } else if (value.length > 1) {
                var productoEncontrado = value.find(obj =>obj.Codigo == codigo_busqueda)
                if(productoEncontrado){                        
                    this.productoSeleccionado.Codigo = productoEncontrado.Codigo;
                    this.productoSeleccionado.marca_comercial = productoEncontrado.Descripcion;
                    this.productoSeleccionado.Concentracion = productoEncontrado.Concentracion;
                    this.productoSeleccionado.codigo_interno = productoEncontrado.codigo_interno;
                    this.productoSeleccionado.Presentacion = productoEncontrado.PresentacionNombre;
                    this.productoSeleccionado.Existencias = productoEncontrado.Existencia;
                    document.getElementById("cantidad_sol").focus();                             
                    return
                }
                this.VentanaEmergenteProducto = true;
            }

        },
        GrabarRegistro() {  
            if(parseFloat(this.productoSeleccionado.Existencias) < parseFloat(this.cantidadSolicitar)){
                this.$vs.notify({
                                color: 'danger',
                                title: 'Error',
                                text: 'No se cuenta con sufientes existencias, ajuste la cant. a solicitar',
                                time: 3000,
                                position: 'top-center'
                            })   
                return
            }            
            
            if (this.idSolicitud > 0) {
                this.RegistrarDetalle();
            }else if(!this.insertoEncabezado) {
                this.RegistrarEncabezado();
            }else{
                this.$vs.notify({
                                color: 'danger',
                                title: 'Error',
                                text: 'Cierre esta ventana y busque la primer solicitud en estado Edición para continuar.',
                                time: 3000,
                                position: 'top-center'
                            })   
            }

        },
        RegistrarEncabezado() {
            var res_variables = false;
                res_variables = this.ValidacionCampos('ID', 'Bodega Destino', this.bodegaDestino, true, 0);

            if (res_variables)
                res_variables = this.ValidacionCampos('ID', 'Bodega Fuente', this.bodegaFuente, true, 0);            

            if (res_variables)
                res_variables = this.ValidacionCampos('ID', 'Codigo Producto', this.productoSeleccionado.Codigo, true, 0);

            if (res_variables)
                res_variables = this.ValidacionCampos('ID', 'Cant. Trasladar', this.cantidadSolicitar, true, 0);

            if (res_variables) {

                this.axios.post('/app/v1_OrdenCompra/Requisicion_Enc', {
                        Requisicion_enc: 0,
                        Total: 0,
                        Cant_lineas: 0,
                        Observaciones: '',
                        Bodega_destino: this.bodegaDestino.CODIGO,
                        Bodega_fuente: this.bodegaFuente.CODIGO,
                        Estado: 'P',
                        Operacion: 'N',
                        TipoMovimiento: '4'
                    })
                    .then(resp => {
                        if (resp.data.codigo == 0) {
                            this.insertoEncabezado = true;
                            this.idSolicitud = resp.data.resultado;
                            this.deshabilitarBodegaFuente = true;
                            this.deshabilitarBodegaDestino = true;
                            this.GrabarRegistro();
                        }
                    })
            }else{
                this.$vs.notify({
                    position:'top-center',
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'Verifique el producto y la cantidad a solicitar',
                    })
                document.getElementById("busquedaProducto").focus();
            }
        },
        RegistrarDetalle() {                               
            let codigoProducto = this.productoSeleccionado.Codigo
            let idProducto = this.listaDetalleMovimiento.findIndex( (producto) => producto.IDPRODUCTOFK.trim()===codigoProducto.trim())
                                                                        
            if(idProducto >= 0){
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',
                    acceptText: 'Aceptar',
                    cancelText: 'Cancelar',
                    text: `¿Desea actualizar la cantidad solicitada del producto  ${this.productoSeleccionado.Codigo} de ${this.listaDetalleMovimiento[idProducto].CANTIDAD_PEDIDA} a  ${this.cantidadSolicitar}?`,
                    accept: () => {       
                        this.IngresarDetalle()     
                    }
                })
            }else{
                this.IngresarDetalle()   
            }
        },
        IngresarDetalle(){
            var res_variables = false;
                res_variables = this.ValidacionCampos('ID', 'Solicitud No.', this.idSolicitud, true, 0);

            if (res_variables)
                res_variables = this.ValidacionCampos('ID', 'Producto', this.productoSeleccionado.Codigo, true, 0);

            if (res_variables)
                res_variables = this.ValidacionCampos('N', 'Cant. Trasladar', this.cantidadSolicitar, true, 0);

            if (res_variables) {
                this.axios.post('/app/v1_OrdenCompra/Requisicion_Det', {
                        Requisicion_det: 0,
                        Requisicion_enc: this.idSolicitud,
                        Producto: this.productoSeleccionado.Codigo,
                        cant_pedida: this.cantidadSolicitar,
                        cant_entregada: 0,
                        cant_recibida: 0,
                        Operacion: 'N'
                    })
                    .then(resp => {

                        if (resp.data.codigo == 0) {
                            this.insertoEncabezado = false;
                            this.ConsultarDetalleMovimiento();
                            this.productoBuscar = '';
                            this.cantidadSolicitar = '';
                            this.LimpiarCamposProducto();
                            document.getElementById("busquedaProducto").focus();
                        }
                    })
            }else{
                this.$vs.notify({
                    position:'top-center',
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'Verifique el producto y la cantidad a solicitar',
                    })
                document.getElementById("busquedaProducto").focus();
            }
        },
        Redondeo(monto){
            return Math.round(((monto) + Number.EPSILON) * 100) / 100
        },
        ValidacionCampos(Tipo, Nombre, valor, obligatorio, cant_maxima) {
                /**
                 * @General
                 * Tipo : N = Númerio, Nombre = Descripción del campo, Valor = A evaluar, Obligatorio= Verdadero o falso,cant_maxima  = Aplica unicamente para un string y define la cantidad de caracteres 
                 * Devuelve un mensaje al usuario si las validaciones no se cumple y aborta, de lo contrario devuelve verdadero
                 */
                if (Tipo == 'ID') {
                    if (valor <= 0) {
                        this.$vs.notify({
                            position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else if (Tipo == 'N') {
    
                    if (valor <= 0 || valor === "") {
                        this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else {
                    if (valor == '' && obligatorio == true) {
                        this.$vs.notify({
                            position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
    
                        if (cant_maxima < valor.length) {
                            this.$vs.notify({
                                position:'top-center',
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: 'Campo ' + Nombre + ' no debe superar los ' + cant_maxima + ' carácteres.',
                            });
                            return false;
                        } else {
                            return true;
                        }
    
                    }
                }
            },
    }
    
    }
</script>
<style>
.label-sizem {
    font-size: 16px;
    font-weight: bold;
}
.con-vs-dialog  {
    z-index: 999999;
}
</style>
    