<template>
<vx-card style="display:none">

    <vs-popup classContent="popup-example" :title="Titulo_emergente" :active.sync="Estado_VentanaProducto" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
        <form data-vv-scope="step1">
            <div style="padding:10px;border-radius:5px; border:1px solid #ccc;">

                <!----- CODIGO --->
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <!---- CODIGO PRODUCTO -------->

                        <ValidationProvider name="selectClase" v-slot="{ errors }" rules="required" class="required">
                            <vs-input label="Código" disabled class="w-full" v-model="codeProducto" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>

                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <!---- CODIGO INTERNO -------->

                        <vs-input label="Código Interno" disabled class="w-full" v-model="codigo_interno" />

                    </div>
                </div>

                <!---- CLASE / TIPO- CATEGORIA--->

                <div style="margin: 15px" class="flex flex-wrap">
                    <!---- CLASE -------->
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">

                        <ValidationProvider name="selectClase" v-slot="{ errors }" rules="required" class="required">
                            <label style="font-size:12px">Clase</label>
                            <multiselect :disabled="Estado_editar_Producto" v-model="cb_clase" :options="ListadoClase" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="seleccion_clase" placeholder="Búsqueda" @input="onChangeClase" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>

                    <!---- CATEGORIA -------->
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">

                        <ValidationProvider name="selectClase" v-slot="{ errors }" rules="required" class="required">
                            <label style="font-size:12px">Tipo</label>
                            <multiselect v-model="selectCategoria" :options="ListadoCategoria" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Operacion_seleccionada" placeholder="Búsqueda Tipo" @input="onChangeCategoria" :disabled="Boton_Tipo_estado" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>

                </div>

                <!--- DESCRIPCION PRODUCTO--->
                <div style="margin: 15px" class="flex flex-wrap">

                    <div class="w-full">
                        <ValidationProvider name="DescripcionProductoss" rules="max:160" v-slot="{ errors }">
                            <vs-input :disabled="Estado_deshabilitado_botones" label="Descripción Producto" class="w-full" count="160" v-model="Descripcion_Producto" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                </div>

                <!----MARCA COMERCIAL / PRINCPIO ACTIVO -------->
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                        <ValidationProvider name="MarcaComercial" rules="max:80" v-slot="{ errors }">
                            <vs-input :disabled="Estado_deshabilitado_botones" label="Marca Comercial" class="w-full" count="80" v-model="Marca" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>

                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                        <ValidationProvider name="PrincipioActivo" rules="max:80" v-slot="{ errors }">
                            <vs-input :disabled="Estado_deshabilitado_botones" label="Principio Activo" class="w-full" count="80" v-model="Principio_Activo" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                </div>

                <!---CONTRAINIDICACIONES / CONCENTRACION-------->
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                        <ValidationProvider name="Contraindicaciones" rules="max:300" v-slot="{ errors }">
                            <vs-input :disabled="Estado_deshabilitado_botones" label="Contraindicaciones" class="w-full" count="300" v-model="Contraindicaciones" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>

                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                        <ValidationProvider name="Concentracion" rules="max:160" v-slot="{ errors }">
                            <vs-input :disabled="Estado_deshabilitado_botones" label="Concentración" class="w-full" count="160" v-model="concentracion" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                </div>

                <!-------------------------- NUEVOS CAMPOS---------------->
                <!--- INDICACIONES / FORMA FARMACEUTICA -------->

                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                        <ValidationProvider name="Indicaciones" rules="max:300" v-slot="{ errors }">
                            <vs-input :disabled="Estado_deshabilitado_botones" label="Indicaciones" class="w-full" count="300" v-model="indicaciones" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>

                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                        <ValidationProvider name="forma_farmaceutica" rules="max:250" v-slot="{ errors }">
                            <vs-input :disabled="Estado_deshabilitado_botones" label="Forma Farmacéutica" class="w-full" count="250" v-model="forma_farmaceutica" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                </div>

                <!--- GRUPO TERAPEUTICO-------->

                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                        <ValidationProvider name="grupo_terapeutico" rules="max:250" v-slot="{ errors }">
                            <vs-input :disabled="Estado_deshabilitado_botones" label="Grupo Terapéutico" class="w-full" count="250" v-model="grupo_terapeutico" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>

                </div>

                <vs-divider>Listas</vs-divider>
                <!--- Objetov  LABORTORIO O MARCA /     PRESENTACION--->
                <div style="margin: 15px" class="flex flex-wrap">

                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <ValidationProvider name="selectClase" v-slot="{ errors }" >
                            <label style="font-size:12px">Laboratorio</label>
                            <multiselect :disabled="Estado_deshabilitado_botones" v-model="cb_laboratorio" :options="Lista_laboratorio" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="seleccion_laboratorio" placeholder="Búsqueda" @input="onChangeLaboratorio" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>

                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <ValidationProvider name="selectClase" v-slot="{ errors }">
                            <label style="font-size:12px">Presentación</label>
                            <multiselect :disabled="Estado_deshabilitado_botones" v-model="cb_presentacion" :options="ListadoPresentacion" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="seleccion_presentacion_a" placeholder="Búsqueda" @input="onChangePresentacion_a" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>

                </div>

                <!---  CATEGORIA-- SubCategoría / SUB SUB CATEGORÍA --->
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <ValidationProvider name="selectClase" v-slot="{ errors }"  >
                            <label style="font-size:12px">Categoría / Sub Categoria</label>
                            <multiselect :disabled="Estado_deshabilitado_botones" v-model="cb_categoria" :options="Lista_categoria" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="seleccion_categoria_a" placeholder="Búsqueda" @input="onChangeCategoria_a" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <ValidationProvider name="selectClase" v-slot="{ errors }" >
                            <label style="font-size:12px">Sub Categoria / Sub-Sub Categoria</label>
                            <multiselect :disabled="Estado_deshabilitado_botones" v-model="cb_sub_categoria" :options="Lista_sub_categoria" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="seleccion_sub_categoria_a" placeholder="Búsqueda" @input="onChangeSubCategoria_a" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>
                </div>

                <!----- GRUPO / SUBGRUPO---->
                <!--<div style="margin: 15px" class="flex flex-wrap">
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <ValidationProvider name="selectClase" v-slot="{ errors }" rules="required" class="required">
                            <label style="font-size:12px">Grupo</label>
                            <multiselect :disabled="Estado_deshabilitado_botones" v-model="cb_grupo" :options="Lista_grupo" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="seleccion_grupo_a" placeholder="Búsqueda" @input="onChangeGrupo_a" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>

                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <ValidationProvider name="selectClase" v-slot="{ errors }" rules="required" class="required">
                            <label style="font-size:12px">Sub Grupo</label>
                            <multiselect :disabled="Estado_deshabilitado_botones" v-model="cb_sub_grupo" :options="Lista_sub_grupo" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="seleccion_Sub_grupo_a" placeholder="Búsqueda" @input="onChangeSubGrupo_a" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>

                </div>--->

                <!----- GENERICOS /  FAMILIAS---->
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <ValidationProvider name="selectClase" v-slot="{ errors }" >
                            <label style="font-size:12px">Unidad Genérico</label>
                            <multiselect :disabled="Estado_deshabilitado_botones" v-model="cb_unidad_generica" :options="Lista_unidad_generica" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="seleccion_genericos_a" placeholder="Búsqueda" @input="onChangeGenericos_a" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <ValidationProvider name="selectClase" v-slot="{ errors }">
                            <label style="font-size:12px">Familia</label>
                            <multiselect :disabled="Estado_deshabilitado_botones" v-model="cb_familia" :options="ListadoFamilia" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="seleccion_familia_a" placeholder="Búsqueda" @input="onChangeFamilia_a" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>
                </div>

                <vs-divider>Otros</vs-divider>

                <!--- Existencia minima y maxima --->
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                        <ValidationProvider name="ExistenciaMinima" v-slot="{ errors }" rules="required" class="required">
                            <vs-input :disabled="Estado_deshabilitado_botones" v-model="ExistenciaMin" @change="Validacion_ExistenciaMin" label="Existencia Minima" class="w-full" type="number" name="Codigo_v2" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                        <ValidationProvider name="ExistenciaMax" v-slot="{ errors }" rules="required" class="required">
                            <vs-input :disabled="Estado_deshabilitado_botones" v-model="ExistenciaMax" @change="Validacion_ExistenciaMax" label="Existencia Maxima" class="w-full" type="number" name="Codigo_v2" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                </div>

                <!--- COSTO IVA ---->
                <div style="margin: 15px" class="flex flex-wrap">
                    <!--<div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                <ValidationProvider name="MarcaComercial" rules="max:80" v-slot="{ errors }" >
                                    <vs-input v-model="RegistroSanitario" label="Registro Sanitario" class="w-full" name="Costo_IVA"  :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"  />
                                </ValidationProvider>
                            </div>    --->
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                        <ValidationProvider name="MarcaComercial" v-slot="{ errors }" rules="required" class="required">
                            <vs-input :disabled="Estado_deshabilitado_botones" v-model="Costo_IVA" @change="validacion_costo" label="Costo con IVA" class="w-full" type="number" name="Costo_IVA" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                </div>

                <!---- OPCIONES -------->

                <div class="flex flex-wrap">
                    <div class="w-full">
                        <ul class="demo-alignment">
                            <li>
                                <P class="font-bold mb-3">Opciones:</P>
                                <vs-checkbox :disabled="Estado_deshabilitado_botones" style="float:left;padding:0px 0px" icon-pack="feather" icon="icon-check" color="success" v-for="(option, indextr) in ListadoProductoOpc" v-model="selectProductoOpc[option.Codigo]" :key="indextr">
                                    {{option.Nombre}}
                                </vs-checkbox>

                            </li>
                        </ul>
                    </div>
                </div>

                <vs-divider></vs-divider>
                <div v-if="bloqueoBusqueda">
                    <vs-divider class="font-bold mb-3">Archivos </vs-divider>

                    <div class="flex flex-wrap archivos">
                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">

                            <div v-if="!image">
                                <div style="width:100%">
                                    <img :disabled="Estado_deshabilitado_botones" src="@/assets/images/others/notfound.png" alt="" style="max-width:100%;max-height:100%">
                                </div>
                                <input :disabled="Estado_deshabilitado_botones" accept="image/*" id="fileInput" type="file" style="display:none" @change="onFileChange($event)" />
                                <vs-button :disabled="Estado_deshabilitado_botones" class="icon-camara" onclick="document.getElementById('fileInput').click()" icon-pack="fas" icon="fa-camera-retro"></vs-button>
                            </div>
                            <div v-else style="display:flex">
                                <img :disabled="Estado_deshabilitado_botones" :src="image" class="image" style="max-width:100%;max-height:100%;" />
                                <div style="background-color:rgba(0,0,0,0.5);padding:10px;position:absolute;width:100%;text-align:center;top:45%">
                                    <vs-button color="danger" :disabled="bloqueoScanner" style="position:relative;top:0;left:0;margin-right:5px;display:inline-block" class="icon-eliminar" v-on:click="removeImage" icon-pack="fas" icon="fa-trash"></vs-button>
                                    <vs-button :disabled="Estado_deshabilitado_botones" color="primary" style="position:relative;top:0;left:0;display:inline-block" type="filled" @click="guardarScanner" icon-pack="fas" icon="fa-save"></vs-button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex flex-wrap archivos">
                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5" v-for="(i,index) in list" :key="index">

                            <expandable-image class="image" :src="'data:image/png;base64, ' +i.src" v-if="i.archivo_nombre">
                            </expandable-image>

                            <vs-button color="danger" :id="'scanner_'+index" class="icon-eliminar" :disabled="i.archivo_nombre==null" v-on:click="remove_scanner(index)" icon-pack="fas" icon="fa-trash" v-if="i.archivo_nombre"></vs-button>
                        </div>
                    </div>
                </div>
                <!--- CODIGO DE BARRAS  --->

                <vs-divider></vs-divider>
                <div v-if="bloqueoBusqueda">
                    <vs-divider>Codigo de Barra</vs-divider>
                    <barcode tag="img" :id="id_barcode" :value="codeProducto.trim()" :options="{ displayValue: true , format:'CODE128',fonyt:'Arial',textAlign:'center', fontOptions: 'bold'}"></barcode>
                </div>

                <vs-divider></vs-divider>
                <vs-button :disabled="Estado_deshabilitado_botones" color="primary" style="float:right" type="filled" icon-pack="feather" icon="icon-save" @click="guardar_producto"> Grabar</vs-button>
                <vs-button :disabled="Estado_deshabilitado_botones" color="danger" style="float:right" type="border" icon-pack="feather" icon="icon-x" @click="show=false"> Cancelar</vs-button>

                <div style="clear:both"></div>

                <!---</div>                     -->

            </div>
        </form>
    </vs-popup>

</vx-card>
</template>

<script>
// import {
//     generateAndDownloadBarcodeInPDF
// } from './generateBarcode';
import Multiselect from 'vue-multiselect'
// import jsPDF from 'jspdf'
import "vue-multiselect/dist/vue-multiselect.min.css"
export default {

    name: 'ComponenteNuevoProducto',
    /*created(){
        this.$root.$ref.EMERGENTE_PRODUCTO = this;
    },    */
    data() {
        return {

            Estado_VentanaProducto: true,
            Estado_editar_Producto: false,
            Estado_deshabilitado_botones: false,

            show: false, //mostrar las opciones del producto
            showTiposede: false, //Opcion Vista Tipo Sede Cocina
            showprecio: false, //Opcion Precio del Producto
            bloqueoBusqueda: false,
            bloqueoPrecioCampo: false,
            bloqueoPrecioBtn: true,
            bloqueoGPrecioBtn: false,
            codeClase: '',
            codeClasificacion: '',
            codigo_interno: '',
            ListadoClase: [],
            ListadoCategoria: [],
            ListadoClaseGeneral: [],
            ListadoCategoriaGeneral: [],
            ListadoFamilia: [],
            ListadoPresentacion: [],
            ListadoCategoriasFact: [],
            ListadoCatFacturacion: [],
            ListadoUtilizadoOrigen: [],
            ListadoProductoOpc: [],
            ListadoTipoBodega: [],
            ListadoTipoSedeCocina: [],
            ListadoCaractProcucto: [],
            selectBodega: null,
            selectClase: null,
            selectCategoria: null,
            selectFamilia: null,
            selectPresentacion: null,
            selectCategoriasFact: null,
            codeCategoria: '',
            selectProductoOpc: {},
            selectTipoBodega: '',
            selectUtilizadoOrigen: '',
            selectTipoSedeCocina: '',
            selectCaractProducto: {},
            codeCategoriaFact: '',
            codeFamilia: '',
            codePresentacion: '',
            //Campos Productos Nuevos
            concentracion: '',
            indicaciones: '',
            forma_farmaceutica: '',
            grupo_terapeutico: '',

            codeProducto: '',
            codeLaboratorio: '',
            NombreLaboratorio: '',
            codeProveedor: '0',
            NombreProveedor: '',
            codedepartamento: '',
            Nombredepartamento: '',
            codesubdepartamento: '',
            Nombresubdepartamento: '',
            codegrupo: '',
            Nombregrupo: '',
            codesubgrupo: '',
            Nombresubgrupo: '',
            Cod_Farmacia: '0',
            Costo_IVA: '0',
            ValorCosto: '',
            Costo_Competencia: '0',
            ExistenciaMin: 0,
            ExistenciaMax: 0,
            counterDanger: false,
            Marca: '',
            Principio_Activo: '',
            Descripcion_Producto: '',
            prod: {
                GenericosCodigo: '',
                GenericosNombre: '',
                codeBodega: '',
            },
            list: [],
            image: '',
            PNivel: '',
            PSubNivel: '',
            PUnidadM: null,
            codePUnidadM: '',
            Ppor: '',
            PFactor: '',
            PPrecio: '',
            //Campos Nuevos
            VentaHospital: '',
            UnidadCompra: '',
            RegistroSanitario: '',
            Contraindicaciones: '',
            usuario: '',
            nombreusuario: '',
            fechacreacion: '',
            atc: '',

            // Variables AM
            cb_laboratorio: '',
            Lista_laboratorio: [],

            cb_categoria: '',
            Lista_categoria: [],

            cb_sub_categoria: '',
            Lista_sub_categoria: [],

            cb_presentacion: '',

            cb_grupo: '',
            Lista_grupo: [],

            cb_unidad_generica: '',
            Lista_unidad_generica: [],

            cb_sub_grupo: '',
            Lista_sub_grupo: [],

            cb_familia: '',

            //EDJ - Campos para multiempresa.
            Parametro_UniEmpresa: '',
            Parametro_Codigo_empresa_unica: '',
            Parametro_Descripcion_empresa_unica: '',
            Parametro_Bloqueado_Cat_cargable: '',
            Titulo_emergente: 'Informacion Producto',

            //Estado botones  AM
            Boton_Tipo_estado: true,
            res_variables: false,

            cb_clase: '',
            tipo_transaccion: '', //tipo_transaccion : N= Nuevo producto, E= Edición producto , V = Vista Producto

        }
    },
    components: {
        Multiselect,
    },
    methods: {

        //cargar_producto: function()  {
        Ficha_producto() {
            //tipo_transaccion : N= Nuevo producto, E= Edición producto , V = Vista Producto
            this.tipo_transaccion = 'N';

            this.bloqueoScanner = false;
            if (this.tipo_transaccion === 'N' || this.tipo_transaccion === 'E') {
                this.cargar_clase();
                this.cargar_presentacion();
                this.cargar_familia();
                this.cargar_TipoBodega();
                this.cargar_UtilizadoOrigen();
                this.onChangeTipoBodega();
                this.Consultar_Parametros();
                this.Cargar_Categoria_a();
                this.Cargar_grupo_a();
                this.Cargar_generico_a();

            }

            this.selectProductoOpc = {
                T: true
            };

            if (this.tipo_transaccion === 'N') {
                this.Titulo_emergente = 'Nuevo Producto';
            }

            this.prod.selectCategoriasFact = {
                Codigo: this.prod.Categoria,
                Nombre: this.prod.CategoriaNombre
            };

        },
        seleccion_clase({
            Nombre
        }) {
            return `${Nombre} `
        },
        /* Inicio metodos para AM */
        Operacion_seleccionada({
            Nombre
        }) {
            return `${Nombre} `
        },
        seleccion_laboratorio({
            Codigo,
            Nombre
        }) {
            return `${Codigo} - ${Nombre} `
        },
        onChangeLaboratorio(value) {

            if (value !== null && value.length !== 0) {
                this.codeLaboratorio = value.Codigo;
            } else {
                this.codeLaboratorio = '';
            }

        },
        Cargar_laboratorio() {
            const url = this.$store.state.global.url
            this.$vs.loading();
            this.axios.post(url + 'app/inventario/BusquedaLaboratorio', {
                    pagina: 1
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Laboratorio',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.Lista_laboratorio = "";
                    } else {
                        //Decodificación

                        this.Lista_laboratorio = resp.data.json;

                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },
        seleccion_categoria_a({
            Codigo,
            Nombre
        }) {
            return `${Codigo} - ${Nombre} `
        },
        onChangeCategoria_a(value) {

            if (value !== null && value.length !== 0) {
                this.codedepartamento = value.Codigo;
                this.cb_sub_categoria = '';
                this.codesubdepartamento = '';
                this.Cargar_Sub_Categoria_a();
            } else {
                this.codedepartamento = '';
            }

        },
        Cargar_Categoria_a() {
            const url = this.$store.state.global.url
            this.$vs.loading();
            this.axios.post(url + 'app/inventario/BusquedaDepartamento', {
                    pagina: 1
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Categoría',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.Lista_categoria = "";
                    } else {
                        //Decodificación

                        this.Lista_categoria = resp.data.json;

                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },
        seleccion_sub_categoria_a({
            Nombre
        }) {
            return `${Nombre} `
        },
        onChangeSubCategoria_a(value) {

            if (value !== null && value.length !== 0) {
                this.codesubdepartamento = value.Codigo;
            } else {
                this.codesubdepartamento = '';
            }

        },
        Cargar_Sub_Categoria_a() {
            const url = this.$store.state.global.url
            this.$vs.loading();
            this.axios.post(url + 'app/inventario/BusquedaSubDepartamento', {
                    pagina: 1,
                    iddepartamento: this.codedepartamento
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Categoría',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.Lista_sub_categoria = "";
                    } else {
                        //Decodificación

                        this.Lista_sub_categoria = resp.data.json;

                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },
        seleccion_presentacion_a({
            Nombre
        }) {
            return `${Nombre} `
        },
        onChangePresentacion_a(value) {

            if (value !== null && value.length !== 0) {
                this.codePresentacion = value.IdMedida;
            } else {
                this.codePresentacion = '';
            }

        },
        seleccion_grupo_a({
            Nombre
        }) {
            return `${Nombre} `
        },
        onChangeGrupo_a(value) {

            if (value !== null && value.length !== 0) {
                this.codegrupo = value.Codigo;
                this.codesubgrupo = '';
                this.cb_sub_grupo = '';
                this.Cargar_sub_grupo_a();
            } else {
                this.codegrupo = '';
            }

        },
        Cargar_grupo_a() {
            const url = this.$store.state.global.url
            this.$vs.loading();
            this.axios.post(url + 'app/inventario/BusquedaGrupo', {
                    pagina: 1,
                    iddepartamento: this.codedepartamento
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Categoría',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.Lista_grupo = "";
                    } else {
                        //Decodificación

                        this.Lista_grupo = resp.data.json;

                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },
        seleccion_Sub_grupo_a({
            Nombre
        }) {
            return `${Nombre} `
        },
        onChangeSubGrupo_a(value) {

            if (value !== null && value.length !== 0) {
                this.codesubgrupo = value.Codigo;
            } else {
                this.codesubgrupo = '';
            }

        },
        Cargar_sub_grupo_a() {
            const url = this.$store.state.global.url
            this.$vs.loading();
            this.axios.post(url + 'app/inventario/BusquedaSubGrupo', {
                    pagina: 1,
                    idgrupo: this.codegrupo
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Categoría',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.Lista_sub_grupo = "";
                    } else {
                        //Decodificación

                        this.Lista_sub_grupo = resp.data.json;

                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },
        seleccion_genericos_a({
            Nombre
        }) {
            return `${Nombre} `
        },
        onChangeGenericos_a(value) {

            if (value !== null && value.length !== 0) {
                this.prod.GenericosCodigo = value.Codigo;
            } else {
                this.prod.GenericosCodigo = '';
            }

        },
        Cargar_generico_a() {
            const url = this.$store.state.global.url
            this.$vs.loading();
            this.axios.post(url + 'app/inventario/BusquedaGenericos', {
                    pagina: 1,
                    idgrupo: this.codegrupo
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Categoría',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.Lista_unidad_generica = "";
                    } else {
                        //Decodificación

                        this.Lista_unidad_generica = resp.data.json;

                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },

        seleccion_familia_a({
            Nombre
        }) {
            return `${Nombre} `
        },
        onChangeFamilia_a(value) {

            if (value !== null && value.length !== 0) {
                this.codeFamilia = value.Codigo;
            } else {
                this.codeFamilia = '';
            }

        },
        cargar_familia() {
            this.$vs.loading();
            this.axios.post('/app/inventario/invFamiliaslist', {})
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Categoría',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.ListadoFamilia = [];
                    } else {
                        //Decodificación

                        this.ListadoFamilia = resp.data.json;

                    }
                })
                .catch(err => {
                    this.$vs.loading.close();
                    
                })
            this.$vs.loading.close();
        }, //Fin de metodo Familia        
        /* Finalizacion metodos para AM */
        onFileChange(e) {
            var files = e.target.files || e.dataTransfer.files;
            if (!files.length)
                return;
            this.createImage(files[0]);
        },
        createImage(file) {
            //var image = new Image();
            var reader = new FileReader();
            var vm = this;
            reader.onload = (e) => {
                vm.image = e.target.result;
            };
            reader.readAsDataURL(file);
        },
        removeImage() {
            this.image = '';
        },
        onChangeClase(value) {

            this.codeProducto = '';
            this.selectCategoria = null;
            this.ListadoCategoria = [];

            this.codeClase = value.Codigo;
            this.codeClasificacion = value.Clasificacion;
            this.cargar_categoria(this.codeClase);
            this.cargar_ProductoOpciones();
            this.cargar_CaracteristicasProducto();
            this.Cargar_laboratorio();
        },
        onChangeCategoria(value) {
            if (value !== null && value.length !== 0) {
                this.codeCategoria = value.Codigo
                this.Generar_codigo_prod(this.codeClase, this.codeCategoria)
            } else {
                this.codeProducto = ''
            }
        },
        onChangeTipoBodega() {
            this.cargar_CatFacturacion()
            if (this.selectTipoBodega == 'C') {
                this.showTiposede = true;
            } else {
                this.showTiposede = false;
            }
            this.cargar_TipoSedeCocina()
        },
        Generar_codigo_prod(codigoclase, codigocategoria) {

            if (this.tipo_transaccion === 'N') {

                this.$vs.loading();
                this.axios.post('/app/inventario/invGenerarCodigoProd', {
                        //Empresa: 'MED',
                        claseid: codigoclase,
                        categoriaid: codigocategoria
                    })
                    .then(resp => {
                        this.$vs.loading.close();
                        this.bloqueoBusqueda = true
                        this.list = [];
                        this.codeProducto = resp.data.json.CODIGO;

                        resp.data.json.map(data => {
                            this.codeProducto = data.Codigo
                        })
                        //this.cargar_scanner()          
                    })
                    .catch(err => {
                        this.$vs.loading.close();
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Producto',
                            text: 'Error al Generar Codigo del Producto.',
                        })
                        this.$vs.loading.close();
                        
                        
                    })
                this.$vs.loading.close();
            } else {
                this.$vs.loading.close();
                this.bloqueoBusqueda = true
                this.list = [];
            }
        }, //Fin de metodo Categoria
guardar_producto() {
            //v-model="selectCaractProducto[option.Codigo]"
            

            this.res_variables = false;
            this.res_variables = this.Validacion_Campos('ID', 'Código', this.codeProducto, true, 0);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('ID', 'Clase', this.codeClase, true, 0);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('ID', 'Tipo', this.codeCategoria, true, 0);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('A', 'Descripción', this.Descripcion_Producto, false, 160);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('A', 'Marca comercial', this.Marca, false, 80);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('A', 'Principio activo', this.Principio_Activo, false, 80);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('A', 'Contraindicaciones', this.Contraindicaciones, false, 300);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('A', 'Concentración', this.concentracion, false, 160);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('A', 'Indicaciones', this.indicaciones, false, 300);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('A', 'Forma Farmacéutica', this.forma_farmaceutica, false, 250);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('A', 'Grupo Terapéutico', this.grupo_terapeutico, false, 250);

            //Listas no obligatorios
            if (this.codeLaboratorio == "" || this.codeLaboratorio == "") {
                this.codeLaboratorio = "N/A     ";
            }
            this.res_variables = this.Validacion_Campos('N', 'Laboratorio', this.codeLaboratorio, false, 500);

            if (this.codePresentacion == "" || this.codePresentacion == "") {
                this.codePresentacion = "92";
            }
            this.res_variables = this.Validacion_Campos('N', 'Presentación', this.codePresentacion, false, 500);
            /*if (this.res_variables== false){
                this.codePresentacion = "92";
            }                */

            if (this.codedepartamento == "" || this.codedepartamento == "") {
                this.codedepartamento = "92";
            }
            this.res_variables = this.Validacion_Campos('N', 'Categoría ', this.codedepartamento, false, 500);
            
            if (this.codesubdepartamento == "" || this.codesubdepartamento == "") {
                this.codesubdepartamento = "00";
            }
            this.res_variables = this.Validacion_Campos('N', 'Sub Categoría ', this.codesubdepartamento, false, 500);

            if (this.codegrupo == "" || this.codegrupo == "") {
                this.codegrupo = "0";
            }
            this.res_variables = this.Validacion_Campos('N', 'Grupo', this.codegrupo, false, 500);

            if (this.codesubgrupo == "" || this.codesubgrupo == "") {
                this.codesubgrupo = "0";
            }
            this.res_variables = this.Validacion_Campos('N', 'Sub Grupo', this.codesubgrupo, false, 500);

            if (this.prod.GenericosCodigo == "" || this.prod.GenericosCodigo == "") {
                this.prod.GenericosCodigo = "0";
            }
            this.res_variables = this.Validacion_Campos('N', 'Unidad Genérico', this.prod.GenericosCodigo, false, 500);

            if (this.codeFamilia == "" || this.codeFamilia == "") {
                this.codeFamilia = "0";
            }
            this.res_variables = this.Validacion_Campos('N', 'Familia', this.codeFamilia, false, 500);


            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('A', 'Registro Sanitario', this.RegistroSanitario, false, 10);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('ID', 'Costo con IVA', this.Costo_IVA, true, 0);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('N', 'Existencia Minima', this.ExistenciaMin, true, 0);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('N', 'Existencia Maxima', this.ExistenciaMax, true, 0);

            //Opciones
            if (this.selectProductoOpc.W) {
                this.selectProductoOpc.W = 'S'
            } else {
                this.selectProductoOpc.W = 'N'
            }
            if (this.res_variables) {
                this.$vs.loading();
                this.axios.post('/app/inventario/invProductoins', {
                        CodigoNuevo: this.codeProducto,
                        Nombre: this.Marca.trim(),
                        Generico: this.Principio_Activo.trim(),
                        Contraindicaciones: this.Contraindicaciones,
                        Descripcion: this.Descripcion_Producto.trim(),
                        Categoria: this.codeCategoriaFact,
                        Costo: this.ValorCosto,
                        CodigoHospIFar: this.Cod_Farmacia,
                        CostoReferencia: this.Costo_Competencia,
                        ExistenciaMIN: this.ExistenciaMin,
                        ExistenciaMAX: this.ExistenciaMax,
                        FamiliasCodigo: this.codeFamilia,
                        Trasladable: this.selectProductoOpc.T,
                        ExcentoIva: this.selectProductoOpc.E,
                        Warning: this.selectProductoOpc.W,
                        EsActivoFijo: this.selectProductoOpc.A,
                        Consignacion: this.selectProductoOpc.C,
                        TipodeUso: 'H',
                        Departamento: this.codedepartamento,
                        SubDepartamento: this.codesubdepartamento,
                        Marca: this.codeLaboratorio,
                        Proveedor: this.codeProveedor,
                        Clase: this.codeClase,
                        SubClase: this.codeCategoria,
                        unidadesmedida: this.codePresentacion,
                        TipoBodega: this.selectTipoBodega,
                        Ensamblado: this.selectTipoSedeCocina,
                        IdGrupo: this.codegrupo,
                        IdSubGrupo: this.codesubgrupo,
                        IdGenerico: this.prod.GenericosCodigo,
                        Tipo: this.codeClasificacion,
                        bitacora: [{
                            llave: this.codeProducto + "-" + this.Marca + "-" + this.Principio_Activo,
                            tipo: "Producto",
                            registros: [
                                'Mantenimiento Producto-Guardar'
                            ]
                        }],
                        Indicaciones: this.indicaciones,
                        Forma_farmaceutica: this.forma_farmaceutica,
                        grupo_terapeutico: this.grupo_terapeutico,
                        Concentracion: this.concentracion,
                        tipo_transaccion: this.tipo_transaccion,
                        RegistroSanitario: this.RegistroSanitario
                    })
                    .then(resp => {
                        this.$vs.loading.close();
                        if (resp.data.codigo == 0 && resp.data.json.length > 0) {

                            if (resp.data.json[0].codigo == -1) {
                                this.$vs.notify({
                                    color: 'danger',
                                    title: 'Producto',
                                    text: resp.data.json[0].error,
                                })
                            } else if (resp.data.json[0].codigo == -2) {
                                //this.codeProducto=codeProducto+1;
                                //this.cargar_producto()
                                this.callback()
                                this.$vs.notify({
                                    color: 'danger',
                                    title: 'Producto',
                                    text: resp.data.json[0].error,
                                })
                            } else {
                                this.$vs.notify({
                                    color: 'success',
                                    title: 'Producto',
                                    text: resp.data.json[0].descripcion,
                                })
                                this.Estado_VentanaProducto = false;
                                this.$root.$refs.A.cargar_producto();
                                this.show = false;
                                this.showprecio = true;
                            }
                        }
                    })
                    .catch(() => {
                        this.$vs.loading.close();
                        
                    })
            }
        },        
        guardar_producto_original() {
            //v-model="selectCaractProducto[option.Codigo]"
            

            this.res_variables = false;
            this.res_variables = this.Validacion_Campos('ID', 'Código', this.codeProducto, true, 0);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('ID', 'Clase', this.codeClase, true, 0);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('ID', 'Tipo', this.codeCategoria, true, 0);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('A', 'Descripción', this.Descripcion_Producto, false, 160);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('A', 'Marca comercial', this.Marca, false, 80);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('A', 'Principio activo', this.Principio_Activo, false, 80);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('A', 'Contraindicaciones', this.Contraindicaciones, false, 300);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('A', 'Concentración', this.concentracion, false, 160);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('A', 'Indicaciones', this.indicaciones, false, 300);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('A', 'Forma Farmacéutica', this.forma_farmaceutica, false, 250);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('A', 'Grupo Terapéutico', this.grupo_terapeutico, false, 250);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('N', 'Laboratorio', this.codeLaboratorio, true, 0);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('N', 'Presentación', this.codePresentacion, true, 0);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('N', 'Categoría ', this.codedepartamento, true, 0);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('N', 'Sub Categoría ', this.codesubdepartamento, true, 0);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('N', 'Grupo', this.codegrupo, true, 0);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('N', 'Sub Grupo', this.codesubgrupo, true, 0);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('N', 'Unidad Genérico', this.prod.GenericosCodigo, true, 0);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('N', 'Familia', this.codeFamilia, true, 0);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('A', 'Registro Sanitario', this.RegistroSanitario, false, 10);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('ID', 'Costo con IVA', this.Costo_IVA, true, 0);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('N', 'Existencia Minima', this.ExistenciaMin, true, 0);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('N', 'Existencia Maxima', this.ExistenciaMax, true, 0);

            //Opciones
            if (this.selectProductoOpc.W) {
                this.selectProductoOpc.W = 'S'
            } else {
                this.selectProductoOpc.W = 'N'
            }
            if (this.res_variables) {
                this.$vs.loading();
                this.axios.post('/app/inventario/invProductoins', {
                        CodigoNuevo: this.codeProducto,
                        Nombre: this.Marca.trim(),
                        Generico: this.Principio_Activo.trim(),
                        Contraindicaciones: this.Contraindicaciones,
                        Descripcion: this.Descripcion_Producto.trim(),
                        Categoria: this.codeCategoriaFact,
                        Costo: this.ValorCosto,
                        CodigoHospIFar: this.Cod_Farmacia,
                        CostoReferencia: this.Costo_Competencia,
                        ExistenciaMIN: this.ExistenciaMin,
                        ExistenciaMAX: this.ExistenciaMax,
                        FamiliasCodigo: this.codeFamilia,
                        Trasladable: this.selectProductoOpc.T,
                        ExcentoIva: this.selectProductoOpc.E,
                        Warning: this.selectProductoOpc.W,
                        EsActivoFijo: this.selectProductoOpc.A,
                        Consignacion: this.selectProductoOpc.C,
                        TipodeUso: 'H',
                        Departamento: this.codedepartamento,
                        SubDepartamento: this.codesubdepartamento,
                        Marca: this.codeLaboratorio,
                        Proveedor: this.codeProveedor,
                        Clase: this.codeClase,
                        SubClase: this.codeCategoria,
                        unidadesmedida: this.codePresentacion,
                        TipoBodega: this.selectTipoBodega,
                        Ensamblado: this.selectTipoSedeCocina,
                        IdGrupo: this.codegrupo,
                        IdSubGrupo: this.codesubgrupo,
                        IdGenerico: this.prod.GenericosCodigo,
                        Tipo: this.codeClasificacion,
                        bitacora: [{
                            llave: this.codeProducto + "-" + this.Marca + "-" + this.Principio_Activo,
                            tipo: "Producto",
                            registros: [
                                'Mantenimiento Producto-Guardar'
                            ]
                        }],
                        Indicaciones: this.indicaciones,
                        Forma_farmaceutica: this.forma_farmaceutica,
                        grupo_terapeutico: this.grupo_terapeutico,
                        Concentracion: this.concentracion,
                        tipo_transaccion: this.tipo_transaccion,
                        RegistroSanitario: this.RegistroSanitario
                    })
                    .then(resp => {
                        this.$vs.loading.close();
                        if (resp.data.codigo == 0 && resp.data.json.length > 0) {

                            if (resp.data.json[0].codigo == -1) {
                                this.$vs.notify({
                                    color: 'danger',
                                    title: 'Producto',
                                    text: resp.data.json[0].error,
                                })
                            } else if (resp.data.json[0].codigo == -2) {
                                //this.codeProducto=codeProducto+1;
                                //this.cargar_producto()
                                this.callback()
                                this.$vs.notify({
                                    color: 'danger',
                                    title: 'Producto',
                                    text: resp.data.json[0].error,
                                })
                            } else {
                                this.$vs.notify({
                                    color: 'success',
                                    title: 'Producto',
                                    text: resp.data.json[0].descripcion,
                                })
                                this.Estado_VentanaProducto = false;
                                this.$root.$refs.A.cargar_producto();
                                this.show = false;
                                this.showprecio = true;
                            }
                        }
                    })
                    .catch(() => {
                        this.$vs.loading.close();
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Producto',
                            text: 'Error al Guardar la información',
                        })
                        this.$vs.loading.close();
                    })
                this.$vs.loading.close();
            }
        },
        guardar_prodprecio_niv1() {
            this.$vs.loading();
            this.axios.post('/app/inventario/invProductoPrecioNivel1', {
                    CodigoNuevo: this.codeProducto,
                    Nivel: this.PNivel,
                    SubNivel: this.PSubNivel,
                    factor: this.PFactor,
                    unidadesmedida: this.codePUnidadM,
                    porcentaje: this.Ppor,
                    precio: this.PPrecio,
                    Tipo: this.codeClasificacion,
                    bitacora: [{
                        llave: this.CodigoNuevo + "-" + this.Nivel + "-" + this.SubNivel,
                        tipo: "Producto",
                        registros: [
                            'Mantenimiento Producto Precio Nivel 1-Guardar'
                        ]
                    }]
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        this.$vs.notify({
                            color: 'success',
                            title: 'Producto',
                            text: resp.data.json[0].descripcion,
                        })
                        this.bloqueoPrecioBtn = false;
                        this.bloqueoGPrecioBtn = true;
                        this.bloqueoPrecioCampo = true;
                        //this.show = false;
                        //this.showprecio = true;                     
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Producto',
                        text: 'Error al Guardar la información',
                    })
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
            //this.bloqueoPrecioBtn=false;
            //this.bloqueoGPrecioBtn=true;
        },
        guardar_precios() {
            this.$vs.loading();
            this.axios.post('/app/inventario/invProductoPrecio', {
                    CodigoNuevo: this.codeProducto,
                    Categoria: this.codeCategoriaFact,
                    Tipo: this.codeClasificacion,
                    bitacora: [{
                        llave: this.CodigoNuevo + "-" + this.Categoria,
                        tipo: "Producto",
                        registros: [
                            'Mantenimiento Producto Precio Guardar - General'
                        ]
                    }]
                })
                .then(resp => {

                    this.$vs.loading.close();
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        //this.cargar_producto()
                        this.callback()
                        this.$vs.notify({
                            color: 'success',
                            title: 'Producto',
                            text: resp.data.json[0].descripcion,
                        })
                        this.limpiar()
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Producto',
                        text: 'Error al Guardar la información',
                    })
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },
        cargar_clase() {
            this.$vs.loading();
            this.axios.post('/app/inventario/invClaselist', {})
                .then(resp => {
                    this.$vs.loading.close();
                    this.ListadoClase = resp.data.json
                })
                .catch(err => {
                    this.$vs.loading.close();
                    
                })
            this.$vs.loading.close();
        },
        cargar_categoria(codigoclase) {
            this.$vs.loading();
            this.axios.post('/app/inventario/invCategorialist', {
                    claseid: codigoclase
                })
                .then(resp => {
                    this.$vs.loading.close();
                    this.ListadoCategoria = resp.data.json
                    if (this.Parametro_Bloqueado_Cat_cargable == 'S') {
                        this.selectCategoria = {
                            Nombre: 'NO CARGABLES'
                        };
                        this.codeCategoria = '2';
                        this.Generar_codigo_prod(this.codeClase, this.codeCategoria)
                        this.Boton_Tipo_estado = true;

                    } else {
                        this.Boton_Tipo_estado = false;
                    }
                })
                .catch(err => {
                    this.$vs.loading.close();
                    
                })
            this.$vs.loading.close();
        },
        cargar_ProductoOpciones() {
            this.$vs.loading();
            this.axios.post('/app/inventario/invProdopcioneslist', {
                    Tipo: this.codeClasificacion
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        this.ListadoProductoOpc = resp.data.json
                    }
                })
                .catch(err => {
                    this.$vs.loading.close();
                    
                })
            this.$vs.loading.close();
        },
        cargar_UtilizadoOrigen() {
            this.$vs.loading();
            this.axios.post('/app/inventario/invutilorigenlist', {})
                .then(resp => {
                    this.$vs.loading.close();
                    this.ListadoUtilizadoOrigen = resp.data.json
                })
                .catch(err => {
                    this.$vs.loading.close();
                    
                })
            this.$vs.loading.close();
        }, //Fin de metodo Utilizado Origen

        cargar_presentacion() {
            this.$vs.loading();
            this.axios.post('/app/inventario/invPresentacionlist', {})
                .then(resp => {
                    this.$vs.loading.close();
                    this.ListadoPresentacion = resp.data.json
                })
                .catch(err => {
                    this.$vs.loading.close();
                    
                })
            this.$vs.loading.close();
        }, //Fin de metodo Presentación
        cargar_TipoBodega() {
            this.$vs.loading();
            this.axios.post('/app/inventario/invCatTipoBodegalist', {})
                .then(resp => {
                    this.$vs.loading.close();
                    this.ListadoTipoBodega = resp.data.json
                })
                .catch(err => {
                    this.$vs.loading.close();
                    
                })
            this.$vs.loading.close();
        }, //Fin de metodo Tipo Bodega
        cargar_CatFacturacion() {
            this.ListadoCatFacturacion = []
            this.selectCategoriasFact = null
            this.$vs.loading();
            this.axios.post('/app/inventario/invCatFacturacionlist', {
                    TipoBodega: this.selectTipoBodega
                })
                .then(resp => {
                    this.$vs.loading.close();
                    this.ListadoCatFacturacion = resp.data.json
                })
                .catch(err => {
                    this.$vs.loading.close();
                    
                })
            this.$vs.loading.close();
        }, //Fin de metodo Categoria Facturacion
        cargar_TipoSedeCocina() {
            this.$vs.loading();
            this.axios.post('/app/inventario/invCatTipoSedeCocinalist', {
                    TipoBodega: this.selectTipoBodega
                })
                .then(resp => {
                    this.$vs.loading.close();
                    this.ListadoTipoSedeCocina = resp.data.json
                })
                .catch(err => {
                    this.$vs.loading.close();
                    
                })
            this.$vs.loading.close();
        }, //Fin de metodo Tipo Sede Cocina
        cargar_CaracteristicasProducto() {
            this.$vs.loading();
            this.axios.post('/app/inventario/invCaractProducto', {
                    Tipo: this.codeClasificacion
                })
                .then(resp => {
                    this.$vs.loading.close();
                    this.ListadoCaractProcucto = resp.data.json
                })
                .catch(err => {
                    this.$vs.loading.close();
                    
                })
            this.$vs.loading.close();
        }, //Fin de metodo Tipo Bodega
        buscar_laboratorio() {
            this.$refs.buscar_laboratorio.iniciar((data) => {
                
                if (data != null) {
                    this.codeLaboratorio = data.Codigo
                    this.NombreLaboratorio = data.Nombre
                }
            })
        },
        buscar_proveedor() {
            this.$refs.buscar_proveedor.iniciar((data) => {
                if (data != null) {

                    this.codeProveedor = data.Codigo
                    this.NombreProveedor = data.Nombre
                }
            })
        },
        buscar_departamento() {
            this.$refs.buscar_departamento.iniciar((data) => {
                if (data != null) {
                    this.codedepartamento = data.Codigo
                    this.Nombredepartamento = data.Nombre
                    this.codesubdepartamento = ''
                    this.Nombresubdepartamento = ''
                }
            })
        },
        buscar_subdepartamento() {

            this.$refs.buscar_subdepartamento.iniciar((data) => {
                if (data != null) {
                    this.codesubdepartamento = data.Codigo
                    this.Nombresubdepartamento = data.Nombre
                }
            })
        },
        buscar_grupo() {
            this.$refs.buscar_grupo.iniciar((data) => {
                if (data != null) {
                    this.codegrupo = data.Codigo
                    this.Nombregrupo = data.Nombre
                    this.codesubgrupo = ''
                    this.Nombresubgrupo = ''
                }
            })
        },
        buscar_subgrupo() {
            this.$refs.buscar_subgrupo.iniciar((data) => {
                
                if (data != null) {
                    
                    this.codesubgrupo = data.Codigo
                    this.Nombresubgrupo = data.Nombre
                }
            })
        },
        buscar_producto() {
            this.$refs.buscar_producto.iniciar((data) => {
                if (data != null) {
                    //this.codeProducto = data.Codigo
                    //this.NombreProducto = data.Nombre
                    this.prod.Codigo = data.Codigo
                    this.prod.Nombre = data.Nombre
                }
            })
        },
        buscar_Genericos() {
            this.$refs.buscar_Genericos.iniciar((data) => {

                if (data != null) {
                    this.prod.GenericosCodigo = data.Codigo
                    this.prod.GenericosNombre = data.Nombre
                }
            })
        },
        cargar_scanner() {
            this.$vs.loading();
            this.axios.post('/app/inventario/InvObtenerImagen', {
                    Categoria: 'PROD',
                    subfolder: 'PROD' + '-' + this.codeProducto,
                    // orden: this.info.orden_numero
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo == 0) {
                        this.list = []
                        resp.data.listado.map(data => {
                            this.list.push({
                                src: data.src,
                                archivo_nombre: data.nombreArchivo
                            })
                        })
                        this.list.push({
                            src: null,
                            archivo_nombre: null
                        })
                    }
                    this.$vs.loading.close("#button-with-loading > .con-vs-loading")
                })
            this.$vs.loading.close();
        },
        guardarScanner() {
            this.$vs.loading();
            this.axios.post('/app/inventario/InvGuardarImagen', {
                    base64String: this.image.replace('data:image/jpeg;base64,', '').replace('data:image/png;base64,', ''),
                    Categoria: 'PROD',
                    subfolder: 'PROD' + '-' + this.codeProducto,
                    nombreArchivo: 'PRODUCTO'
                })
                .then(() => {
                    this.$vs.loading.close();
                    this.removeImage()
                    this.cargar_scanner()
                })
            this.$vs.loading.close();
        },
        remove_scanner(index) {
            this.$vs.loading();
            this.axios.post('/app/inventario/InvEliminarImagen', {
                    Categoria: 'PROD',
                    subfolder: 'PROD' + '-' + this.codeProducto,
                    nombreArchivo: this.list[index].archivo_nombre
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo == 0) {
                        this.list.splice(index, 1)
                    }

                })
            this.$vs.loading.close();
        },
        precio_porcentaje() {
            if (this.Costo_IVA > 0) {
                this.$vs.loading();
                this.axios.post('/app/inventario/invGenerarPrecioPorcentaje', {
                        factor: ((this.PFactor == '' || this.PFactor == 0) ? 1 : this.PFactor),
                        Costo: this.ValorCosto,
                        porcentaje: this.Ppor,
                    })
                    .then(resp => {
                        this.$vs.loading.close();
                        resp.data.json.map(data => {
                            this.PPrecio = data.Precio
                        })
                    })
                    .catch(err => {
                        this.$vs.loading.close();
                        
                    })
                this.$vs.loading.close();
            }
        },
        precio_factor() {
            if (this.PFactor > 0) {
                this.$vs.loading();
                this.axios.post('/app/inventario/invGenerarPrecioFactor', {
                        factor: ((this.PFactor == '' || this.PFactor == 0) ? 1 : this.PFactor),
                        Costo: this.ValorCosto,
                        porcentaje: this.Ppor,
                    })
                    .then(resp => {
                        this.$vs.loading.close();
                        resp.data.json.map(data => {
                            this.PPrecio = data.Precio
                        })
                    })
                    .catch(err => {
                        this.$vs.loading.close();
                        
                    })
                this.$vs.loading.close();
            }
            this.$vs.loading.close();
        },
        precio_costo() {
            if (this.PPrecio > 0 && this.PPrecio != '') {
                this.$vs.loading();
                this.axios.post('/app/inventario/invGenerarPrecioCosto', {
                        factor: ((this.PFactor == '' || this.PFactor == 0) ? 1 : this.PFactor),
                        Costo: this.ValorCosto,
                        porcentaje: this.Ppor,
                        precio: this.PPrecio,
                    })
                    .then(resp => {
                        this.$vs.loading.close();
                        resp.data.json.map(data => {
                            this.Ppor = data.Porcentaje
                        })
                    })
                    .catch(err => {
                        this.$vs.loading.close();
                        
                    })
                this.$vs.loading.close();
            }
        },
        Validacion_ExistenciaMax() {
            if (this.ExistenciaMin > 0) {
                if (this.ExistenciaMin > this.ExistenciaMax) {
                    const Temporal = this.ExistenciaMin
                    this.ExistenciaMin = this.ExistenciaMax
                    this.ExistenciaMax = Temporal
                }
            }
        },
        Validacion_ExistenciaMin() {
            if (this.ExistenciaMax > 0) {
                if (this.ExistenciaMin > this.ExistenciaMax) {
                    const Temporal = this.ExistenciaMin
                    this.ExistenciaMin = this.ExistenciaMax
                    this.ExistenciaMax = Temporal
                }
            }
        },
        validacion_costo() {
            if (this.Costo_IVA > 0) {
                this.$vs.loading();
                this.axios.post('/app/inventario/invGenerarCotoSinIVA', {
                        Costo: this.Costo_IVA
                    })
                    .then(resp => {
                        this.$vs.loading.close();
                        resp.data.json.map(data => {
                            this.ValorCosto = data.costo
                            this.Costo_IVA = Math.round(this.ValorCosto);
                        })
                    })
                    .catch(err => {
                        this.$vs.loading.close();
                        
                    })
                this.$vs.loading.close();
            }
        },
        limpiar() {
            this.bloqueoPrecioBtn = true
            this.bloqueoGPrecioBtn = false
            this.bloqueoPrecioCampo = false
            this.show = false
            this.showprecio = false
            //--------------------PRODUCTO--------------------
            this.Cod_Farmacia = '0'
            this.Marca = ''
            this.Principio_Activo = ''
            this.Descripcion_Producto = ''
            this.codedepartamento = ''
            this.Nombredepartamento = ''
            this.codesubdepartamento = ''
            this.Nombresubdepartamento = ''
            this.codeLaboratorio = ''
            this.NombreLaboratorio = ''
            this.codeProveedor = '0'
            this.NombreProveedor = ''
            this.codegrupo = ''
            this.Nombregrupo = ''
            this.codesubgrupo = ''
            this.Nombresubgrupo = ''
            this.codeProducto = ''
            this.codigo_interno = '',
                this.NombreProducto = ''
            this.Costo_Competencia = '0'
            this.ExistenciaMin = '0'
            this.ExistenciaMax = '0'
            this.Costo_IVA = '0'
            this.ValorCosto = '0'
            this.selectClase = null
            this.selectCategoria = null
            this.selectFamilia = null
            this.selectTipoBodega = null
            this.PUnidadM = null
            this.selectPresentacion = null
            this.ListadoCatFacturacion = []
            this.selectCategoriasFact = null
            this.selectProductoOpc = {
                T: true
            } //this.selectProductoOpc= {},
            this.selectUtilizadoOrigen = ''
            this.ListadoTipoSedeCocina = []
            this.selectTipoSedeCocina = ''
            this.showTiposede = false
            this.list = [] //Fotos
            this.image = ''
            //--------------------PRODUCTO PRECIO--------------------
            this.PNivel = '0'
            this.PSubNivel = '0'
            this.Ppor = '0'
            this.PFactor = '0'
            this.PPrecio = '0'

            //NUEVOS CAMPOS PARA AM
            this.cb_clase = ''
        },
        nuevo() {

            this.show = true
            this.showprecio = false
            //--------------------PRODUCTO--------------------
            this.codePresentacion = '';
            this.Cod_Farmacia = '0'
            this.Marca = ''
            this.Principio_Activo = ''
            this.Descripcion_Producto = ''
            this.codedepartamento = ''
            this.Nombredepartamento = ''
            this.codesubdepartamento = ''
            this.Nombresubdepartamento = ''
            this.codeLaboratorio = ''
            this.NombreLaboratorio = ''
            this.codeProveedor = '0'
            this.NombreProveedor = ''
            this.codegrupo = ''
            this.Nombregrupo = ''
            this.codesubgrupo = ''
            this.Nombresubgrupo = ''
            this.codeProducto = ''
            this.NombreProducto = ''
            this.prod.GenericosCodigo = ''
            this.prod.GenericosNombre = ''
            this.Costo_Competencia = '0'
            this.ExistenciaMin = '0'
            this.ExistenciaMax = '0'
            this.Costo_IVA = '0'
            this.ValorCosto = '0'
            this.selectClase = null
            this.selectCategoria = null
            this.selectFamilia = null
            this.selectTipoBodega = null
            this.selectPresentacion = null
            this.PUnidadM = null
            this.ListadoCatFacturacion = []
            this.selectCategoriasFact = null
            this.selectProductoOpc = {
                T: true
            } //this.selectProductoOpc= {},
            this.selectUtilizadoOrigen = 'H'
            this.ListadoTipoSedeCocina = []
            this.selectTipoSedeCocina = ''
            this.showTiposede = false;
            this.list = [] //Fotos
            this.prod.list = [] //Fotos
            this.image = ''
            //--------------------PRODUCTO PRECIO--------------------
            this.PNivel = '0'
            this.PSubNivel = '0'
            this.Ppor = '0'
            this.PFactor = '0'
            this.PPrecio = '0'
        },
        async Consultar_Parametros() {

            var result = '';
            const url = this.$store.state.global.url;
            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/consulta_parametros', {})
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: resp.data.mensaje,
                        })
                        this.Lista_Parametros = [];
                    } else {
                        this.Lista_Parametros = resp.data.json;

                        //Parametro para saber si existe una sola empresa.
                        result = this.Lista_Parametros.find(obj => obj.CODIGO_INTERNO === 'GEN001CDUE').VALOR;
                        if (result === 'S') {
                            //Si el resultado es SI, busca el codigo de empresa y descripción definida.
                            this.Parametro_UniEmpresa = result;
                            this.Parametro_Codigo_empresa_unica = this.Lista_Parametros.find(obj => obj.CODIGO_INTERNO === 'GEN001CEUD').VALOR;
                            this.Parametro_Descripcion_empresa_unica = this.Lista_Parametros.find(obj => obj.CODIGO_INTERNO === 'GEN001DLED').VALOR;
                            this.Parametro_Bloqueado_Cat_cargable = this.Lista_Parametros.find(obj => obj.CODIGO_INTERNO === 'INV007BCC').VALOR;
                            this.Campos_Defecto();

                        } else {
                            this.Parametro_UniEmpresa = '';
                            this.Parametro_Codigo_empresa_unica = '';
                            this.Parametro_Descripcion_empresa_unica = '';
                            this.Campos_Defecto();
                        }
                    }
                })
                .catch(() => {
                    
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
            return result;
        },
        Validacion_Campos(Tipo, Nombre, valor, obligatorio, cant_maxima) {

            if (Tipo == 'ID') {
                if (valor <= 0) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Alerta',
                        text: 'Campo \'' + Nombre + '\' es obligatorio.',
                    });
                    return false;
                } else {
                    return true;
                }
            } else if (Tipo == 'N') {

                if (valor < 0 || valor === "") {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Alerta',
                        text: 'Campo \'' + Nombre + '\' es obligatorio.',
                    });
                    return false;
                } else {
                    return true;
                }
            } else {
                if (valor == '' && obligatorio == true) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Alerta',
                        text: 'Campo \'' + Nombre + '\' es obligatorio.',
                    });
                    return false;
                } else {

                    if (cant_maxima < valor.length) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'Campo ' + Nombre + ' no debe superar los ' + cant_maxima + ' carácteres.',
                        });
                        return false;
                    } else {
                        return true;
                    }

                }
            }
        },
        // GenerarPDF(img) {
        //     img = "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";
        //     let pdfName = 'Codigos Productos';
        //     var doc = new jsPDF();

        //     //var jpegUrl = $("#id_barcode").toDataURL("image/jpeg");

        //     /*doc.text("Hello World", 10, 10);
        //     doc.addImage(img,'JPEG',20,20);*/
        //     doc.addImage(img, 'JPEG', 100, 20);
        //     //doc.addImage(jpegUrl, 'JPEG', 15, 40);

        //     doc.save(pdfName + '.pdf');
        // },
        // GenerarPDF_Codigo() {
        //     generateAndDownloadBarcodeInPDF(this.codeProducto);
        // }

        /************** GENERAR CODIGO DE BARRAS */

    },
    mounted() {

        /// this.$validator.localize('en', dict);

        const sesion = this.$store.state.sesion;
        this.usuario = sesion.corporativo;
        this.nombreusuario = sesion.usuario;

        var f = new Date();
        //this.fechacreacion='04/09/2020'
        this.fechacreacion = (f.getDate() + "/" + (f.getMonth() + 1) + "/" + f.getFullYear());

    },
    props: {
        lista_datos: String,
        callback: {
            default: null
        },
        cerrar: {
            default: null
        },
        callbackFuncion: {
            default: null
        }
    },
    watch: {
        Estado_VentanaProducto() {
            this.cerrar()
        }
    },

}
</script>
