<template>
<vx-card style="display:none">

    <!--Busqueda Mantenimiento-->
    <buscador buscador_titulo="Buscador / Laboratorio" ref="buscar_laboratorioMan" :bloqueo="false" :api="'app/inventario/BusquedaLaboratorio'" :campos="['Codigo','Nombre']" :titulos="['Codigo','Nombre']" :multiselect="false" :api_reload="true" />
    <buscador buscador_titulo="Buscador / Proveedor" ref="buscar_proveedorMan" :bloqueo="false" :api="'app/inventario/BusquedaProveedor'" :campos="['Codigo','Nombre','Nit']" :titulos="['Codigo','Nombre','Nit']" :multiselect="false" :api_reload="true" api_disable_seleccion="Estado!='Activo'" />
    <buscador buscador_titulo="Buscador / Departamento" ref="buscar_departamentoMan" :bloqueo="false" :api="'app/inventario/BusquedaDepartamento'" :campos="['Codigo','Nombre']" :titulos="['Codigo','Nombre']" :multiselect="false" :api_reload="true" />
    <buscador buscador_titulo="Buscador / SubDepartamento" ref="buscar_subdepartamentoMan" :bloqueo="false" :api="'app/inventario/BusquedaSubDepartamento'" :api_filtro="{iddepartamento:prod.lista.codedepartamento}" :campos="['Codigo','Nombre']" :titulos="['Codigo','Nombre']" :multiselect="false" :api_reload="true" />
    <buscador buscador_titulo="Buscador / Grupo" ref="buscar_grupoMan" :bloqueo="false" :api="'app/inventario/BusquedaGrupo'" :campos="['Codigo','Nombre']" :titulos="['Codigo','Nombre']" :multiselect="false" :api_reload="true" />
    <buscador buscador_titulo="Buscador / SubGrupo" ref="buscar_subgrupoMan" :bloqueo="false" :api="'app/inventario/BusquedaSubGrupo'" :api_filtro="{idgrupo:prod.lista.codegrupo}" :campos="['Codigo','Nombre']" :titulos="['Codigo','Nombre']" :multiselect="false" :api_reload="true" />
    <buscador buscador_titulo="Buscador / Presentación" ref="buscar_presentacion" :bloqueo="false" :api="'app/inventario/BusquedaPresentacion'" :campos="['Codigo','Nombre']" :titulos="['Codigo','Nombre']" :multiselect="false" :api_reload="true" />
    <buscador buscador_titulo="Buscador / Genericos" ref="buscar_Genericos" :bloqueo="false" :api="'app/inventario/BusquedaGenericos'" :campos="['Codigo','Nombre']" :titulos="['Codigo','Nombre']" :multiselect="false" :api_reload="true" />
    <!-- Presentacion en Listado de Precios -->
    <buscador buscador_titulo="Buscador / Presentación" ref="buscar_presentacionPrecio" :bloqueo="false" :api="'app/inventario/BusquedaPresentacion'" :campos="['Codigo','Nombre']" :titulos="['Codigo','Nombre']" :multiselect="false" :api_reload="true" />

    <!-- ========================================================================================================================================================== -->
    <!-- POPUP INICIO -->
    <!-- ========================================================================================================================================================== -->

    <vs-popup classContent="popup-example" data-backdrop="'static'" :data-keyboard="false" :title="'Mantenimiento Producto - Precios'" :active.sync="showmanprecio">
        <ValidationObserver v-slot="{ errors,invalid,handleSubmit }">
            <form>

                <div style="padding:10px;border-radius:5px; border:1px solid #ccc;">
                    <vs-button color="primary" style="float:left;display:none" type="border" icon-pack="feather" icon="icon-save" @click="Actualizar_producto()"> Actualizar Producto</vs-button>
                    <div style="clear:both"></div>

                    <div class="flex flex-wrap">

                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <label class="vs-input--label"> Codigo:</label>
                            <vx-input-group class="">
                                <vs-input v-model="prod.Codigo" disabled />
                            </vx-input-group>
                        </div>

                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <vs-input v-model="prod.CodigoHospIFar" label="Código Farmacia:" class="w-full" />
                        </div>

                    </div>

                    <div class="flex flex-wrap">
                        <vs-divider></vs-divider>
                        <div class="w-full">
                            <P>Tipo:</P>
                            <ul class="centerx">
                                <li>
                                    <vs-radio style="float:left;padding:1px 5px" v-for="(fila, index) in prod.ListadoProductoTipo" :key="index" :vs-name="'ProductoTipoM_'+index" v-model="prod.selectProductoTipo" :vs-value="fila.Codigo">
                                        {{fila.Nombre}}
                                    </vs-radio>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="flex flex-wrap">
                        <vs-divider></vs-divider>
                        <div class="w-full">
                            <ul class="centerx">
                                <li>
                                    <P>Opciones:</P>
                                    <vs-checkbox style="float:left;padding:1px 13px" icon-pack="feather" icon="icon-check" color="success" v-for="(option, indextr) in prod.ListadoProductoOpc" v-model="prod.selectProductoOpc[option.Codigo]" :key="indextr">
                                        {{option.Nombre}}
                                    </vs-checkbox>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="flex flex-wrap">
                        <vs-divider></vs-divider>
                        <div class="w-full">
                            <P>Utilizado en:</P>
                            <ul class="centerx">
                                <li>
                                    <vs-radio style="float:left;padding:1px 5px" v-for="(fila, index) in prod.ListadoUtilizadoOrigen" :key="index" :vs-name="'UtilizadoOrigenM_'+index" v-model="prod.selectUtilizadoOrigen" :vs-value="fila.Codigo">
                                        {{fila.Nombre}}
                                    </vs-radio>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <vs-divider></vs-divider>

                    <!-- <div class="flex flex-wrap">
                        <div class="w-full">
                            <ValidationProvider name="PrincipioActivo" rules="required|max:80" v-slot="{ errors }">
                                <vs-textarea counter="80" label="Principio Activo" :counter-danger.sync="counterDanger" v-model="prod.NombreGenerico" name="PrincipioActivo" />
                                <span style="position:relative;top:-18px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                            </ValidationProvider>
                        </div>
                    </div> -->

                    <div class="flex flex-wrap">
                        <div class="w-full">
                            <ValidationProvider name="Marca-Comercial" rules="required|max:80" v-slot="{ errors }">
                                <vs-textarea counter="80" :label="(prod.Tipo=='S'?'Nombre de Servicio':'Nombre (Marca Comercial)')" :counter-danger.sync="counterDanger" v-model="prod.Nombre" />
                                <span style="position:relative;top:-18px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                            </ValidationProvider>
                        </div>
                    </div>

                    <!-- <div class="flex flex-wrap" style="margin-bottom: 100px">
                        <div v-if="prod.Tipo=='P' && prod.Codigo.substring(0,1)!='7'" class="w-full md:w-1/1 lg:w-1/1 xl:w-1/1">
                            <P>Familia:</P>
                            <multiselect v-model="prod.selectFamilia" :options="prod.ListadoFamilia" track-by="Codigo" label="Nombre" placeholder="Seleccione Familia"></multiselect>
                        </div>
                    </div> -->

                    <vs-divider></vs-divider>

                    <div class="btn-group">
                        <vs-button v-if="permiso.ver_compra==true" :type="(opcion==1)?'filled':'line'" style="display:inline;height:50px" color="primary" icon-pack="feather" @click="opcion=1" icon="icon-shopping-cart">Compras</vs-button>
                        <vs-button v-if="permiso.ver_clasificacion==true" :type="(opcion==2)?'filled':'line'" style="display:inline;height:50px" color="primary" icon-pack="feather" @click="opcion=2" icon="icon-layers">Clasificación</vs-button>
                        <vs-button v-if="permiso.ver_estadistica==true" :type="(opcion==3)?'filled':'line'" style="display:inline;height:50px" color="primary" icon-pack="feather" @click="opcion=3" icon="icon-trending-up">Estadística</vs-button>
                        <vs-button v-if="permiso.ver_existencia==true" :type="(opcion==4)?'filled':'line'" style="display:inline;height:50px" color="primary" icon-pack="feather" @click="opcion=4" icon="icon-box">Existencia</vs-button>
                        <vs-button v-if="permiso.ver_max_min==true" :type="(opcion==5)?'filled':'line'" style="display:inline;height:50px" color="primary" icon-pack="feather" @click="opcion=5" icon="icon-minimize-2">Max. y Min.</vs-button>
                        <vs-button v-if="permiso.ver_informacion==true" :type="(opcion==6)?'filled':'line'" style="display:inline;height:50px" color="primary" icon-pack="feather" @click="opcion=6" icon="icon-grid">Información</vs-button>
                        <vs-button v-if="prod.Tipo=='P' && permiso.ver_carac_producto==true" :type="(opcion==7)?'filled':'line'" style="display:inline;height:50px" color="primary" icon-pack="feather" @click="opcion=7" icon="icon-list">Características del Producto</vs-button>
                        <vs-button v-if="permiso.ver_info_tecnico==true" :type="(opcion==8)?'filled':'line'" style="display:inline;height:50px" color="primary" icon-pack="feather" @click="opcion=8" icon="icon-edit">Información Técnico y Farmacológico</vs-button>
                        <vs-button v-if="permiso.select_complementarios==true" :type="(opcion==9)?'filled':'line'" style="display:inline;height:50px" color="primary" icon-pack="feather" @click="opcion=9" icon="icon-star">Complementarios</vs-button>
                    </div>

                    <!-- <vs-tabs value="8" > -->
                    <div v-show="opcion==1 && permiso.ver_compra==true" label="Compras" icon-pack="feather" icon="icon-shopping-cart">
                        <div style="box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.2);padding:10px;border-radius:5px; border:1px solid #ccc; font: 14px Arial;">
                            <vs-divider>Compras</vs-divider>

                            <div class="flex flex-wrap">

                                <div class="w-full">
                                    <P>Método de costeo:</P>
                                    <ul class="centerx">
                                        <li>
                                            <vs-radio style="float:left;padding:1px 5px" v-for="(fila, index) in prod.ListadoMetodoCosteo" :key="index" v-model="prod.selectMetodoCosteo" :vs-name="'Metododecosteo'+index" :vs-value="fila.Codigo">
                                                {{fila.Nombre}}
                                            </vs-radio>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <div class="flex flex-wrap">
                                <vs-divider></vs-divider>
                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <label class="vs-input--label">Proveedor:{{prod.lista.NombreProveedor}}</label>
                                    <vx-input-group class="">
                                        <vs-input v-model="prod.lista.codeProveedor" disabled />
                                        <template slot="append">
                                            <div class="append-text btn-addon">
                                                <button type="submit" v-show="false" name="button"></button>
                                                <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_proveedorMan()" icon="icon-search"></vs-button>
                                            </div>
                                        </template>
                                    </vx-input-group>
                                </div>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <label class="vs-input--label">Laboratorio:{{prod.lista.NombreLaboratorio}}</label>
                                    <vx-input-group class="">
                                        <vs-input v-model="prod.lista.codeLaboratorio" disabled />
                                        <template slot="append">
                                            <div class="append-text btn-addon">
                                                <button type="submit" v-show="false" name="button"></button>
                                                <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_laboratorioMan()" icon="icon-search"></vs-button>
                                            </div>
                                        </template>
                                    </vx-input-group>
                                </div>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input disabled v-model="prod.CostoPromedio" label="Costo Promedio:" class="w-full" />
                                </div>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input disabled v-model="prod.CostoUltimo" label="Costo Ultimo:" class="w-full" />
                                </div>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input disabled v-model="prod.CostoAlto" label="Costo Alto:" class="w-full" />
                                </div>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input disabled v-model="prod.Impuesto" label="Impuesto:" class="w-full" />
                                </div>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input v-model="prod.MarkUp" label="MarkUp %:" class="w-full" />
                                </div>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <label class="vs-input--label">Lnsp:</label>
                                    <vx-input-group class="">
                                        <vs-input disabled v-model="prod.Lnsp" />
                                        <template slot="append">
                                            <div class="append-text btn-addon">
                                                <button type="submit" v-show="false" name="button"></button>
                                                <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="cargar_Lnsp()" icon="icon-zap"></vs-button>
                                            </div>
                                        </template>
                                    </vx-input-group>
                                </div>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input v-model="prod.CostoReferencia" type="number" label="Costo Referencia:" class="w-full" />
                                </div>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input v-model="prod.UnidadMedida" type="number" label="Unidad Medida-Existencia:" class="w-full" />
                                </div>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input v-model="prod.UnidadVenta" type="number" label="Unidad Venta:" class="w-full" />
                                </div>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input v-model="prod.UnidadCompra" type="number" label="Unidad Compra-Precio:" class="w-full" />
                                </div>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input v-model="prod.Minimo" label="Minimo:" class="w-full" />
                                </div>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input v-model="prod.Maximo" label="Maximo:" class="w-full" />
                                </div>

                                <div v-if="prod.Tipo=='P'" class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input icon-no-border :success="true" icon="add" v-model="VentaHospital" label="Venta Hospital:" class="w-full" />
                                </div>

                                <div v-if="prod.Tipo=='P'" class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input icon-no-border :success="true" icon="add" v-model="UnidadCompraCar" max=25 label="Unidad Compra - Bodega:" class="w-full" />
                                </div>

                            </div>

                            <vs-divider>Lista de Precios:</vs-divider>
                            <vs-table max-items="5" pagination :data="prod.ListadoPrecio" noDataText="Sin datos disponibles">
                                <template slot="thead">
                                    <vs-th>Nivel</vs-th>
                                    <vs-th>Sub Nivel</vs-th>
                                    <vs-th>U.Medida</vs-th>
                                    <vs-th>%</vs-th>
                                    <vs-th>Factor</vs-th>
                                    <vs-th>Precio</vs-th>
                                    <vs-th v-if="permiso.editar_precio">Opciones</vs-th>
                                </template>

                                <template slot-scope="{data}">
                                    <vs-tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td :data="tr.Nivel">{{tr.Nivel}}</vs-td>
                                        <vs-td :data="tr.SubNivel">{{tr.SubNivel}}</vs-td>
                                        <vs-td :data="tr.UnidadMedida">{{tr.UnidadMedida}}</vs-td>
                                        <vs-td :data="tr.Porcentaje">{{tr.Porcentaje}}</vs-td>
                                        <vs-td :data="tr.Factor">{{tr.Factor}}</vs-td>
                                        <vs-td :data="tr.Precio">{{tr.Precio}}</vs-td>

                                        <vs-td v-if="permiso.editar_precio">
                                            <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" @click="opcionesListP(tr)"></vs-button>
                                        </vs-td>

                                    </vs-tr>
                                </template>
                            </vs-table>

                        </div>
                    </div>
                    <div v-show="opcion==2" label="Clasificación" icon-pack="feather" icon="icon-layers">
                        <div style="box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.2);padding:10px;border-radius:5px; border:1px solid #ccc; font: 14px Arial;">
                            <vs-divider>Clasificación</vs-divider>

                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                <label class="vs-input--label">Unidad Presentación:{{prod.lista.PresentacionNombre}}</label>
                                <vx-input-group class="">
                                    <vs-input v-model="prod.lista.PresentacionUnidad" disabled />
                                    <template slot="append">
                                        <div class="append-text btn-addon">
                                            <button type="submit" v-show="false" name="button"></button>
                                            <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_presentacion()" icon="icon-search"></vs-button>
                                        </div>
                                    </template>
                                </vx-input-group>
                            </div>

                            <vs-divider></vs-divider>

                            <div class="flex flex-wrap">
                                <div class="w-full">
                                    <P>Tipo Bodega:</P>
                                    <ul class="centerx">
                                        <li>
                                            <vs-radio style="float:left;padding:1px 5px" v-for="(fila, index) in prod.ListadoTipoBodega" :key="index" v-model="prod.selectTipoBodega" :vs-name="'TipoBodega_'+index" :vs-value="fila.Codigo" @input="onChangeTipoBodegaMant">
                                                {{fila.Nombre}}
                                            </vs-radio>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <div class="vx-col md:w-1/1 w-full mt-5">
                                <P>Categoría para Facturación:</P>
                                <multiselect v-model="prod.selectCategoriasFact" :options="prod.ListadoCatFacturacion" track-by="Codigo" label="Nombre" placeholder="Seleccione Tipo Bodega"></multiselect>
                            </div>

                            <div class="flex flex-wrap">
                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <label class="vs-input--label">Sub Categoría:{{prod.lista.Nombredepartamento}}</label>
                                    <vx-input-group class="">
                                        <vs-input v-model="prod.lista.codedepartamento" disabled />
                                        <template slot="append">
                                            <div class="append-text btn-addon">
                                                <button type="submit" v-show="false" name="button"></button>
                                                <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_departamentoMan()" icon="icon-search"></vs-button>
                                            </div>
                                        </template>
                                    </vx-input-group>
                                </div>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <label class="vs-input--label">Sub-SubCategoría:{{prod.lista.Nombresubdepartamento}}</label>
                                    <vx-input-group class="">
                                        <vs-input v-model="prod.lista.codesubdepartamento" disabled />
                                        <template slot="append">
                                            <div class="append-text btn-addon">
                                                <button type="submit" v-show="false" name="button"></button>
                                                <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_subdepartamentoMan()" icon="icon-search"></vs-button>
                                            </div>
                                        </template>
                                    </vx-input-group>
                                </div>
                            </div>

                        </div>

                    </div>
                    <div v-show="opcion==3" label="Estadística" icon-pack="feather" icon="icon-trending-up">
                        <div style="box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.2);padding:10px;border-radius:5px; border:1px solid #ccc; font: 14px Arial;">

                            <vs-tabs position="left" alignment="fixed" color="success">
                                <vs-tab label="Estadística" icon-pack="feather" icon="icon-database">

                                    <vs-divider>Estadística</vs-divider>

                                    <div class="flex flex-wrap">

                                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                            <vs-input disabled v-model="prod.VentaEsperada" label="Venta Esperada" class="w-full" />
                                        </div>

                                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                            <vs-input disabled v-model="prod.UltimaVenta" label="Ultima Venta" class="w-full" />
                                        </div>

                                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                            <vs-input disabled v-model="prod.UltimaFacturaVenta" label="Ultima FacturaVenta" class="w-full" />
                                        </div>

                                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                            <vs-input disabled v-model="prod.UltimaCompra" label="Ultima Compra" class="w-full" />
                                        </div>

                                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                            <vs-input disabled v-model="prod.UltimaFacturaCompra" label="Ultima FacturaCompra" class="w-full" />
                                        </div>

                                    </div>

                                    <vs-divider></vs-divider>
                                    <vs-button color="primary" style="float:right" type="border" icon-pack="feather" icon="icon-save" @click="cargar_Estadistica_Producto"> Reacceder cargos...</vs-button>
                                    <div style="clear:both"></div>

                                    <vs-divider>Estadística</vs-divider>
                                    <vs-table max-items="5" pagination :data="prod.ListadoEstadistica" noDataText="Sin datos disponibles">
                                        <template slot="thead">
                                            <vs-th>Fecha</vs-th>
                                            <vs-th>Sucursal</vs-th>
                                            <vs-th>TipoOrden</vs-th>
                                            <vs-th>Orden</vs-th>
                                            <vs-th>Serie Admisión</vs-th>
                                            <vs-th>Admisión</vs-th>
                                            <vs-th>Cantidad</vs-th>
                                            <vs-th>Precio Unitario</vs-th>
                                            <vs-th>Usuario</vs-th>
                                            <vs-th>Ajeno</vs-th>
                                            <vs-th></vs-th>
                                        </template>

                                        <template slot-scope="{data}">
                                            <vs-tr :key="indextr" v-for="(tr, indextr) in data">
                                                <vs-td :data="tr.Fecha">{{tr.Fecha}}</vs-td>
                                                <vs-td :data="tr.Sucursal">{{tr.Sucursal}}</vs-td>
                                                <vs-td :data="tr.TipoOrden">{{tr.TipoOrden}}</vs-td>
                                                <vs-td :data="tr.Orden">{{tr.Orden}}</vs-td>
                                                <vs-td :data="tr.SerieAdmision">{{tr.SerieAdmision}}</vs-td>
                                                <vs-td :data="tr.Admision">{{tr.Admision}}</vs-td>
                                                <vs-td :data="tr.Cantidad">{{tr.Cantidad}}</vs-td>
                                                <vs-td :data="tr.PrecioUnitario">{{tr.PrecioUnitario}}</vs-td>
                                                <vs-td :data="tr.Usuario">{{tr.Usuario}}</vs-td>
                                                <vs-td :data="tr.Ajeno">{{tr.Ajeno}}</vs-td>
                                            </vs-tr>
                                        </template>
                                    </vs-table>

                                </vs-tab>
                                <vs-tab label="Reporte" icon-pack="feather" icon="icon-trending-up">

                                    <vs-divider>Reporte</vs-divider>

                                    <div class="flex flex-wrap">
                                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                                            <label>Fecha Inicial:</label>
                                            <!-- <flat-pickr   :config="configdateTimePicker" class="form-control datepicker"  style="width:100%"  placeholder="Fecha Inicial" v-model="FechaInicio" /> -->
                                            <date-range-picker class="form-control datepicker" style="width:90%" ref="picker" v-model="FechaInicio" :locale-data="{ firstDay: 1, format: 'dd/mm/yyyy' }" opens="center" :minDate="minDate" :maxDate="maxDate" singleDatePicker="single" :timePicker="timePicker" :timePicker24Hour="timePicker" :showWeekNumbers="timePicker" :showDropdowns="showDropdowns" :autoApply="autoApply" :ranges="timePicker">
                                            </date-range-picker>
                                        </div>

                                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                                            <label>Fecha Final:</label>
                                            <!-- <flat-pickr   :config="configdateTimePicker" class="form-control datepicker" style="width:100%" placeholder="Fecha Final" v-model="FechaFin"  /> -->
                                            <date-range-picker class="form-control datepicker" style="width:90%" ref="picker" v-model="FechaFin" :locale-data="{ firstDay: 1, format: 'dd/mm/yyyy' }" opens="center" :minDate="minDate" :maxDate="maxDate" singleDatePicker="single" :timePicker="timePicker" :timePicker24Hour="timePicker" :showWeekNumbers="timePicker" :showDropdowns="showDropdowns" :autoApply="autoApply" :ranges="timePicker">
                                            </date-range-picker>
                                        </div>

                                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                                            <vs-button color="primary" style="float:right;width:100%;top:15px" type="border" icon-pack="feather" @click="generarreporte()" icon="icon-save"> Generar</vs-button>
                                            <div style="clear:both"></div>
                                        </div>

                                    </div>

                                    <vs-divider>Bodega Madre - Cargo</vs-divider>

                                    <div>
                                        <div class="vx-row">
                                            <div class="vx-col w-full  md:w-1/3 lg:w-1/3 xl:w-1/3">
                                                <statistics-card-line hideChart class="mb-base" icon="ShoppingBagIcon" :statistic="TotalCargo" statisticTitle="Cargos" color='success' />
                                            </div>

                                            <div class="vx-col w-full  md:w-1/3 lg:w-1/3 xl:w-1/3">
                                                <statistics-card-line hideChart class="mb-base" icon="PackageIcon" :statistic="TotalDespacho" statisticTitle="Movimientos Despachado" color='warning' />
                                            </div>

                                            <div class="vx-col w-full  md:w-1/3 lg:w-1/3 xl:w-1/3">
                                                <statistics-card-line hideChart class="mb-base" icon="ActivityIcon" :statistic="BodegaMadreCPM" :statisticTitle="'CPM Bodega Madre ' + BodegaFuente" color='warning' />
                                            </div>

                                        </div>
                                    </div>

                                    <vs-divider>Sub Bodega</vs-divider>

                                    <div>
                                        <div class="vx-row" v-for="(tr, indextr) in DataSubBodega" :key="indextr">

                                            <div class="vx-col w-full  md:w-1/2 lg:w-1/2 xl:w-1/2">
                                                <statistics-card-line hideChart class="mb-base" icon="PackageIcon" v-if="tr.SumaRequerimientos != ''" :statistic="tr.SumaRequerimientos" :statisticTitle="'Requerimientos Despachado. Bodega ' + tr.Bodega" />
                                            </div>

                                            <div class="vx-col w-full  md:w-1/2 lg:w-1/2 xl:w-1/2">
                                                <statistics-card-line hideChart class="mb-base" icon="ActivityIcon" v-if="tr.SumaRequerimientos != ''" :statistic="tr.PromedioMensual" :statisticTitle="'CPM Sub-Bodegas ' + tr.Bodega" />
                                            </div>

                                        </div>
                                    </div>

                                </vs-tab>
                            </vs-tabs>

                        </div>
                    </div>
                    <div v-show="opcion==4" label="Existencia" icon-pack="feather" icon="icon-box">
                        <div style="box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.2);padding:10px;border-radius:5px; border:1px solid #ccc; font: 14px Arial;">

                            <h6 class="font-bold mb-3">Vista:</h6>
                            <div class="flex flex-wrap">
                                <div class="w-full">
                                    <vs-radio style="float:left;padding:1px 5px" v-for="(fila, index) in ListadoVista" :key="index" v-model="selectvista" :vs-name="'Vista_'+index" :vs-value="fila.Codigo">
                                        {{fila.Nombre}}
                                    </vs-radio>
                                </div>
                            </div>

                            <vs-divider>Existencia</vs-divider>
                            <!-- <vs-table max-items="5" pagination search :data="prod.ListadoExistencia" noDataText="Sin datos disponibles"> -->

                            <div>
                                <vs-table max-items="5" pagination search :data="existencia_filtro" noDataText="Sin datos disponibles">
                                    <template slot="thead">
                                        <vs-th>Número de Serie</vs-th>
                                        <vs-th>Bodega</vs-th>
                                        <vs-th>Localización</vs-th>
                                        <vs-th>Existencia</vs-th>
                                        <vs-th>Inv. Físicos</vs-th>
                                        <vs-th>Ultimo Inv. Físico</vs-th>
                                        <vs-th v-if="permiso.editar_existencia">Opciones</vs-th>
                                    </template>

                                    <template slot-scope="{data}">
                                        <vs-tr :key="indextr" v-for="(tr, indextr) in data">
                                            <vs-td :data="tr.NumeroSerie">{{tr.NumeroSerie}}</vs-td>
                                            <vs-td :data="tr.Bodega">{{tr.Bodega}}</vs-td>
                                            <vs-td :data="tr.Localizacion">{{tr.Localizacion}}</vs-td>
                                            <vs-td :data="tr.Existencia">{{tr.Existencia}}</vs-td>
                                            <vs-td :data="tr.InventariosFisicos">{{tr.InventariosFisicos}}</vs-td>
                                            <vs-td :data="tr.UltimoInventario">{{tr.UltimoInventario}}</vs-td>
                                            <vs-td v-if="permiso.editar_existencia">
                                                <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" @click="opcionesExistencia(tr)"></vs-button>
                                            </vs-td>

                                        </vs-tr>
                                    </template>
                                </vs-table>

                            </div>

                            <div class="border-top border-right d-flex align-items-between flex-column py-1 col-6">
                                <p class="card-text text-muted mb-0">
                                    <font style="vertical-align: inherit;">
                                        <font style="vertical-align: inherit;">TOTAL EXISTENCIA: </font>
                                    </font>
                                </p>
                                <h3 class="font-weight-bolder mb-0">
                                    <font style="vertical-align: inherit;">
                                        <font style="vertical-align: inherit;">{{Totalexistencia}}</font>
                                    </font>
                                </h3>
                            </div>

                        </div>
                    </div>
                    <div v-show="opcion==5" label="Max. y Min." icon-pack="feather" icon="icon-minimize-2">
                        <div style="box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.2);padding:10px;border-radius:5px; border:1px solid #ccc; font: 14px Arial;">
                            <vs-divider>Max. y Min.</vs-divider>

                            <div class="flex flex-wrap">

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input v-model="prod.Minimo" label="Mínimo" class="w-full" />
                                </div>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input v-model="prod.Maximo" label="Máximo" class="w-full" />
                                </div>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <P>Bodega:</P>
                                    <multiselect v-model="selectBodega" :options="prod.ListadoBodega" track-by="Codigo" label="Nombre_Bodega" placeholder="Seleccione Bodega"></multiselect>
                                </div>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input v-model="prod.UnidadEmpaque" label="Unidad/Empaque Para Unidades por Embalaje" class="w-full" />
                                </div>

                                <vs-divider>Limite Máximos/Mínimos Tiempo:</vs-divider>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input v-model="prod.MinTiempo" label="Mínimo" class="w-full" />
                                </div>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input v-model="prod.MaxTiempo" label="Máximo" class="w-full" />
                                </div>

                            </div>

                            <vs-divider></vs-divider>
                            <vs-button style="float:right" @click="guardar_Bodega"> Agregar</vs-button>
                            <div style="clear:both"></div>

                            <vs-divider>Max. y Min. Bodega</vs-divider>
                            <vs-table max-items="5" pagination :data="prod.ListadoMaxMinBod" noDataText="Sin datos disponibles">
                                <template slot="thead">
                                    <vs-th>Bodega</vs-th>
                                    <vs-th>Minimos</vs-th>
                                    <vs-th>Maximos</vs-th>
                                    <vs-th>Fecha</vs-th>
                                </template>

                                <template slot-scope="{data}">
                                    <vs-tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td :data="tr.Bodega">{{tr.Bodega}}</vs-td>
                                        <vs-td :data="tr.Minimos">{{tr.Minimos}}</vs-td>
                                        <vs-td :data="tr.Maximos">{{tr.Maximos}}</vs-td>
                                        <vs-td :data="tr.Fecha">{{tr.Fecha}}</vs-td>
                                    </vs-tr>
                                </template>
                            </vs-table>

                        </div>
                    </div>
                    <div v-show="opcion==6" label="Información" icon-pack="feather" icon="icon-grid">
                        <div style="box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.2);padding:10px;border-radius:5px; border:1px solid #ccc; font: 14px Arial;">

                            <vs-divider>Información</vs-divider>

                            <div class="flex flex-wrap">

                                <div v-show='false' class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input disabled v-model="prod.CampoExtra1" label="CampoExtra1" class="w-full" />
                                </div>

                                <div v-show='false' class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input disabled v-model="prod.CampoExtra2" label="CampoExtra2" class="w-full" />
                                </div>

                                <div v-show='false' class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input disabled v-model="prod.CampoExtra3" label="CampoExtra3" class="w-full" />
                                </div>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input disabled v-model="prod.fechacreacion" label="Fecha de Registro:" class="w-full" />
                                </div>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input disabled v-model="prod.Usuario" label="Creado por:" class="w-full" />
                                </div>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input disabled v-model="prod.NombreUsuario" label="Nombre Usuario:" class="w-full" />
                                </div>

                            </div>

                        </div>

                    </div>
                    <div v-show="opcion==7 && prod.Tipo=='P'" label="Características del Producto" icon-pack="feather" icon="icon-list">
                        <div style="box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.2);padding:10px;border-radius:5px; border:1px solid #ccc; font: 14px Arial;">

                            <div v-if="prod.Tipo=='P'" class="flex flex-wrap">
                                <div class="w-full">
                                    <ul class="demo-alignment">
                                        <li>
                                            <P class="font-bold mb-3">Características del Producto:</P>
                                            <vs-checkbox style="float:left;padding:2px 2px" icon-pack="feather" icon="icon-check" color="success" v-for="(option, indextr) in ListadoCaractProcucto" v-model="selectCaractProducto[option.Codigo]" :key="indextr">
                                                {{option.Nombre}}
                                            </vs-checkbox>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div v-show="opcion==8" label="Información Técnico y Farmacológica" icon-pack="feather" icon="icon-edit">
                        <div style="box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.2);padding:10px;border-radius:5px; border:1px solid #ccc; font: 14px Arial;">

                            <div class="flex flex-wrap">

                                <div class="w-full">
                                    <ValidationProvider name="Descripcion-Producto" :rules="(prod.Tipo=='P'?'required|max:160':null)" v-slot="{ errors }">
                                        <vs-textarea counter="160" label="Acción Terapéutica" :counter-danger.sync="counterDanger" name="DescripcionProducto" v-model="prod.Descripcion" />
                                        <span style="position:relative;top:-18px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                                    </ValidationProvider>
                                </div>

                                <div class="w-full">
                                    <ValidationProvider name="PrincipioActivo" :rules="(prod.Tipo=='P'?'required|max:80':null)" v-slot="{ errors }">
                                        <vs-textarea counter="80" :label="(prod.Tipo=='S'?'Descripción':'Principio Activo')" :counter-danger.sync="counterDanger" v-model="prod.NombreGenerico" name="PrincipioActivo" />
                                        <span style="position:relative;top:-18px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                                    </ValidationProvider>
                                </div>

                                <div class="w-full" v-if="prod.Tipo=='P'">
                                    <ValidationProvider name="Contraindicaciones" rules="required|max:1000" v-slot="{ errors }">
                                        <vs-textarea counter="1000" label="Contraindicaciones" :counter-danger.sync="counterDanger" name="Contraindicaciones" v-model="Contraindicaciones" />
                                        <span style="position:relative;top:-18px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                                    </ValidationProvider>
                                </div>

                            </div>

                            <div class="flex flex-wrap">

                                <div v-if="prod.Tipo=='P'" class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input icon-no-border :success="true" icon="add" v-model="RegistroSanitario" label="Registro Sanitario" class="w-full" />
                                </div>

                                <div v-if="prod.Tipo=='P'" class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <vs-input icon-no-border :success="true" icon="add" v-model="atc" label="Código ATC" class="w-full" />
                                </div>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <label class="vs-input--label">Grupo:{{prod.lista.Nombregrupo}}</label>
                                    <vx-input-group class="">
                                        <vs-input v-model="prod.lista.codegrupo" disabled />
                                        <template slot="append">
                                            <div class="append-text btn-addon">
                                                <button type="submit" v-show="false" name="button"></button>
                                                <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_grupoMan()" icon="icon-search"></vs-button>
                                            </div>
                                        </template>
                                    </vx-input-group>
                                </div>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <label class="vs-input--label">Sub-Grupo:{{prod.lista.Nombresubgrupo}}</label>
                                    <vx-input-group class="">
                                        <vs-input v-model="prod.lista.codesubgrupo" disabled />
                                        <template slot="append">
                                            <div class="append-text btn-addon">
                                                <button type="submit" v-show="false" name="button"></button>
                                                <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_subgrupoMan()" icon="icon-search"></vs-button>
                                            </div>
                                        </template>
                                    </vx-input-group>
                                </div>

                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                    <label class="vs-input--label">Unidad Genérico:{{prod.lista.GenericosNombre}}</label>
                                    <vx-input-group class="">
                                        <vs-input v-model="prod.lista.GenericosCodigo" disabled />
                                        <template slot="append">
                                            <div class="append-text btn-addon">
                                                <button type="submit" v-show="false" name="button"></button>
                                                <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_Genericos()" icon="icon-search"></vs-button>
                                            </div>
                                        </template>
                                    </vx-input-group>
                                </div>

                            </div>
                            <!-- style="margin-bottom: 100px" -->
                            <div class="flex flex-wrap">
                                <div v-if="prod.Tipo=='P' && prod.Codigo.substring(0,1)!='7'" class="w-full md:w-1/1 lg:w-1/1 xl:w-1/1">
                                    <ValidationProvider name="ListadoFamilia" rules="required" v-slot="{ errors }">
                                        <P class="font-bold mb-3">Familia:</P>
                                        <multiselect v-model="prod.selectFamilia" :options="prod.ListadoFamilia" track-by="Codigo" label="Nombre" placeholder="Seleccione Familia"></multiselect>
                                        <span style="position:relative;top:-2px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                                    </ValidationProvider>
                                </div>
                            </div>

                            <div>

                                <vs-divider>Archivos</vs-divider>

                                <div class="flex flex-wrap archivos">

                                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5" v-for="(i,index) in prod.list" :key="index" v-show="prod.list.length !=0">

                                        <expandable-image class="image" :src="'data:image/png;base64, ' +i.src" v-if="i.archivo_nombre">
                                        </expandable-image>

                                        <vs-button color="danger" :id="'scanner_'+index" class="icon-eliminar" :disabled="i.archivo_nombre==null" v-on:click="remove_Imagen_Mant(index)" icon-pack="fas" icon="fa-trash" v-if="i.archivo_nombre"></vs-button>

                                        <div v-else style="width:100%">
                                            <div v-if="!image">
                                                <div style="width:100%">
                                                    <img src="@/assets/images/others/notfound.png" alt="" style="max-width:100%;max-height:100%">
                                                </div>
                                                <input accept=".png, .jpg, .jpeg" id="fileInput" type="file" style="display:none" @change="onFileChangeMant($event)" />
                                                <vs-button class="icon-camara" onclick="document.getElementById('fileInput').click()" icon-pack="fas" icon="fa-camera-retro"></vs-button>
                                            </div>
                                            <div v-else style="display:flex">
                                                <div style="width:100%">
                                                    <img :src="image" class="image" style="max-width:100%;max-height:100%" />
                                                </div>
                                                <div style="background-color:rgba(0,0,0,0.5);padding:10px;position:absolute;width:100%;text-align:center;top:45%">
                                                    <vs-button style="position:relative;top:0;left:0;margin-right:5px;display:inline-block" color="danger" class="icon-eliminar" v-on:click="removeImageMant" icon-pack="fas" icon="fa-trash"></vs-button>
                                                    <vs-button style="position:relative;top:0;left:0;display:inline-block" color="primary" type="filled" @click="guardarImagenMant" icon-pack="fas" icon="fa-save"></vs-button>
                                                </div>
                                            </div>
                                        </div>

                                    </div>

                                    <div v-show="prod.list.length == 0" class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                                        <div v-if="!image">
                                            <div style="width:100%">
                                                <img src="@/assets/images/others/notfound.png" alt="" style="max-width:100%;max-height:100%">
                                            </div>
                                            <input accept=".png, .jpg, .jpeg*" id="fileInput" type="file" style="display:none" @change="onFileChangeMant($event)" />
                                            <vs-button class="icon-camara" onclick="document.getElementById('fileInput').click()" icon-pack="fas" icon="fa-camera-retro"></vs-button>
                                        </div>
                                        <div v-else style="display:flex">
                                            <div style="width:100%">
                                                <img :src="image" class="image" style="max-width:100%;max-height:100%" />
                                            </div>
                                            <div style="background-color:rgba(0,0,0,0.5);padding:10px;position:absolute;width:100%;text-align:center;top:45%">
                                                <vs-button style="position:relative;top:0;left:0;margin-right:5px;display:inline-block" color="danger" class="icon-eliminar" v-on:click="removeImageMant" icon-pack="fas" icon="fa-trash"></vs-button>
                                                <vs-button style="position:relative;top:0;left:0;display:inline-block" color="primary" type="filled" @click="guardarImagenMant" icon-pack="fas" icon="fa-save"></vs-button>
                                            </div>
                                        </div>
                                    </div>

                                </div>

                            </div>

                            <div>
                                <vs-divider>Codigo de Barra</vs-divider>

                                <div class="flex flex-wrap">
                                    <barcode :value="prod.Codigo.trim()" :options="{ displayValue: true , format:'CODE128',fonyt:'Arial',textAlign:'center', fontOptions: 'bold'}"></barcode>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div v-show="opcion==9" label="Complementarios" icon-pack="feather" icon="icon-box">
                    <div style="box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.2);padding:10px;border-radius:5px; border:1px solid #ccc;">

                        <vs-tabs position="left" alignment="fixed" color="success">
                            <vs-tab label="Complementado por" class="label-sizep" icon-pack="feather" icon="icon-layers">
                                <vs-divider class="label-sizep">Complementado Por</vs-divider>
                                <div class="flex flex-wrap" v-if="AgregarComp|EditarComp">
                                    <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                                        <ValidationProvider name="Producto" rules="required|max:50" class="required">
                                            <label class="label-sizep"> Código Complementario</label>
                                            <vs-input type="text" class="w-full" placeholder="Buscar" v-model="produtoc_buscar" @change="cargar_productoc" :disebleb="BloquearCodigo" />
                                        </ValidationProvider>
                                    </div>
                                    <div v-if="productoSeleccionado.Codigo > 0 " class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5" style="float:left;margin: 16px">
                                        <div style="border-radius:5px;padding:5px;font-size:15px;background-color:#09af00;color:white">
                                            {{productoSeleccionado.Codigo}}
                                            <br>
                                            {{productoSeleccionado.marca_comercial}}
                                            <br>
                                        </div>
                                    </div>
                                </div>
                                <br>
                                <div class="flex flex-wrap">
                                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                                        <ValidationProvider name="Cantidad" rules="required|max:50" class="required" v-if="AgregarComp|EditarComp">
                                            <label class="label-sizep"> Cantidad </label>
                                            <vs-input type="number" class="w-full" v-model="Cantidad" :disabled="BloqueoComplementarios==true" />
                                        </ValidationProvider>
                                    </div>
                                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5" v-if="AgregarComp|EditarComp">
                                        <label class="label-sizep"> X</label>
                                        <vs-checkbox style="float:left;padding:1px 13px" icon-pack="feather" icon="icon-check" color="success" v-model="xCargos" :disabled="BloqueoComplementarios==true">
                                        </vs-checkbox>
                                    </div>
                                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5" v-if="AgregarComp|EditarComp">
                                        <label class="label-sizep"> Duplos</label>
                                        <vs-checkbox style="float:left;padding:1px 13px" icon-pack="feather" icon="icon-check" color="success" v-model="Duplos" :disabled="BloqueoComplementarios==true">
                                        </vs-checkbox>
                                    </div>
                                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5" v-if="AgregarComp|EditarComp">
                                        <label class="label-sizep"> Activo</label>
                                        <vs-checkbox style="float:left;padding:1px 13px" icon-pack="feather" icon="icon-check" color="success" v-model="Activo" :disabled="BloqueoComplementarios==true">
                                        </vs-checkbox>
                                    </div>
                                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5" v-if="AgregarComp|EditarComp">
                                        <label class="label-sizep"> Automático</label>
                                        <vs-checkbox style="float:left;padding:1px 13px" icon-pack="feather" icon="icon-check" color="success" v-model="Automatico" :disabled="BloqueoComplementarios==true">
                                        </vs-checkbox>
                                    </div>
                                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5" v-if="AgregarComp|EditarComp"></div>
                                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5" v-if="AgregarComp|EditarComp">
                                        <label class="label-sizep"> Único</label>
                                        <vs-checkbox style="float:left;padding:1px 13px" icon-pack="feather" icon="icon-check" color="success" v-model="Unico" :disabled="BloqueoComplementarios==true">
                                        </vs-checkbox>
                                    </div>
                                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5" v-if="AgregarComp|EditarComp">
                                        <label class="label-sizep"> 1ra. Vez</label>
                                        <vs-checkbox style="float:left;padding:1px 13px" icon-pack="feather" icon="icon-check" color="success" v-model="PrimeraVez" :disabled="BloqueoComplementarios==true">
                                        </vs-checkbox>
                                    </div>
                                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5" v-if="AgregarComp|EditarComp">
                                        <label class="label-sizep"> Replicar</label>
                                        <vs-checkbox style="float:left;padding:1px 13px" icon-pack="feather" icon="icon-check" color="success" v-model="Replicar" :disabled="BloquearReplicar==true">
                                        </vs-checkbox>
                                    </div>
                                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5" v-if="AgregarComp|EditarComp">
                                        <label class="label-sizep"> Actualizable</label>
                                        <vs-checkbox style="float:left;padding:1px 13px" icon-pack="feather" icon="icon-check" color="success" v-model="Actualizable" :disabled="BloquearActualizable==true">
                                        </vs-checkbox>
                                    </div>

                                    <br>
                                </div>
                                <div class="flex flex-wrap">
                                    <div class="w-full">
                                        <br>
                                        <div>
                                            <div v-if="EditarComp == false && permiso.add_complementarios">
                                                <vs-button style="float:right" @click="AgregarComplementario()" :disabled="BloquearAgregar"> Agregar Complementario</vs-button>
                                            </div>
                                            <div v-if="EditarComp && permiso.upd_complementarios">
                                                <vs-button style="float:right" @click="EditarComplementarioGrabar()"> Editar Complementario</vs-button>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                                <div class="flex flex-wrap">
                                    <div class="w-full">
                                        <br>
                                        <div v-if="BCancelar">
                                            <vs-button color="warning" style="float:right" @click="Cancelar()">Cancelar</vs-button>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <br>
                                    <vs-divider></vs-divider>

                                    <vs-table class="label-sizep" max-items="5" pagination :data="listaComplementarios" noDataText="Sin datos disponibles" search id="tb_complementarios">
                                        <template slot="thead">
                                            <vs-th>Código</vs-th>
                                            <vs-th>Descripción</vs-th>
                                            <vs-th>Cantidad</vs-th>
                                            <vs-th>X</vs-th>
                                            <vs-th>Duplos</vs-th>
                                            <vs-th>Activo</vs-th>
                                            <vs-th>Automático</vs-th>
                                            <vs-th>Único</vs-th>
                                            <vs-th>1ra. Vez</vs-th>
                                            <vs-th>Replicar</vs-th>
                                            <vs-th>Actualizable</vs-th>
                                            <vs-th v-if="permiso.upd_complementarios">Editar</vs-th>
                                            <vs-th v-if="permiso.del_complementarios">Eliminar</vs-th>
                                        </template>

                                        <template slot-scope="{data}">
                                            <vs-tr :key="indextr" v-for="(tr, indextr) in data">
                                                <vs-td :data="tr.ProductoSecundario">{{tr.ProductoSecundario}}</vs-td>
                                                <vs-td :data="tr.Nombre">{{tr.Nombre}}</vs-td>
                                                <vs-td :data="tr.Cantidad">{{tr.Cantidad}}</vs-td>
                                                <vs-td :data="tr.MultiplicarXCargos">{{tr.MultiplicarXCargos}}</vs-td>
                                                <vs-td :data="tr.PermitirDuplicidad">{{tr.PermitirDuplicidad}}</vs-td>
                                                <vs-td :data="tr.Activo">{{tr.Activo}}</vs-td>
                                                <vs-td :data="tr.Cargable">{{tr.Cargable}}</vs-td>
                                                <vs-td :data="tr.Unico">{{tr.Unico}}</vs-td>
                                                <vs-td :data="tr.UnaVezXArea">{{tr.UnaVezXArea}}</vs-td>
                                                <vs-td :data="tr.ReplicarCambios">{{tr.ReplicarCambios}}</vs-td>
                                                <vs-td :data="tr.Actualizable">{{tr.Actualizable}}</vs-td>
                                                <vs-td v-if="permiso.upd_complementarios" align="center">
                                                    <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" @click="EditarComplementario(tr)"></vs-button>
                                                </vs-td>
                                                <vs-td v-if="permiso.del_complementarios" align="center">
                                                    <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="EliminarComplementario(data[indextr])"></vs-button>
                                                </vs-td>
                                            </vs-tr>
                                        </template>
                                    </vs-table>

                                </div>
                            </vs-tab>

                            <vs-tab label="Complementa A" icon-pack="feather" icon="icon-trending-up">
                                <vs-divider class="label-sizep">Complementado A</vs-divider>
                                <div>
                                    <vs-table max-items="5" pagination :data="ListaComplementaa" noDataText="Sin datos disponibles" search id="tb_complementarios">
                                        <template slot="thead">
                                            <vs-th>Código</vs-th>
                                            <vs-th>Descripción</vs-th>
                                            <vs-th>Cantidad</vs-th>
                                            <vs-th>X</vs-th>
                                            <vs-th>Duplos</vs-th>
                                            <vs-th>Activo</vs-th>
                                            <vs-th>Automático</vs-th>
                                            <vs-th>Único</vs-th>
                                            <vs-th>1ra. Vez</vs-th>
                                            <vs-th>Replicar</vs-th>
                                            <vs-th>Actualizable</vs-th>
                                        </template>

                                        <template slot-scope="{data}">
                                            <vs-tr :key="indextr" v-for="(tr, indextr) in data">
                                                <vs-td :data="tr.ProductoPrimario">{{tr.ProductoPrimario}}</vs-td>
                                                <vs-td :data="tr.Nombre">{{tr.Nombre}}</vs-td>
                                                <vs-td :data="tr.Cantidad">{{tr.Cantidad}}</vs-td>
                                                <vs-td :data="tr.MultiplicarXCargos">{{tr.MultiplicarXCargos}}</vs-td>
                                                <vs-td :data="tr.PermitirDuplicidad">{{tr.PermitirDuplicidad}}</vs-td>
                                                <vs-td :data="tr.Activo">{{tr.Activo}}</vs-td>
                                                <vs-td :data="tr.Cargable">{{tr.Cargable}}</vs-td>
                                                <vs-td :data="tr.Unico">{{tr.Unico}}</vs-td>
                                                <vs-td :data="tr.UnaVezXArea">{{tr.UnaVezXArea}}</vs-td>
                                                <vs-td :data="tr.ReplicarCambios">{{tr.ReplicarCambios}}</vs-td>
                                                <vs-td :data="tr.Actualizable">{{tr.Actualizable}}</vs-td>
                                            </vs-tr>
                                        </template>
                                    </vs-table>
                                </div>

                            </vs-tab>
                            <vs-tab label="Agendar cambio de precio" icon-pack="feather" icon="icon-list">
                                <vs-divider class="label-sizep">Agendar cambio de precio</vs-divider>

                                <div>
                                    <br>
                                    <div class="flex flex-wrap" v-if="AgregarCambioP">
                                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                                            <ValidationProvider name="Nivel" class="required">
                                                <label class="label-sizep"> Nivel</label>
                                                <vs-input type="number" class="w-full" v-model="NivelAP" @change="PrecioActual" style="float:left;padding:1px 13px" />
                                            </ValidationProvider>
                                        </div>
                                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                                            <ValidationProvider name="SubNivel" class="required">
                                                <label class="label-sizep"> SubNivel</label>
                                                <vs-input type="number" class="w-full" v-model="SubNivelAP" style="float:left;padding:1px 13px" :disabled="true" />
                                            </ValidationProvider>
                                        </div>
                                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                                            <ValidationProvider name="PrecioN" class="required">
                                                <label class="label-sizep"> Precio Nuevo</label>
                                                <vs-input type="money" class="w-full" v-model="PrecioNAP" style="float:left;padding:1px 13px" />
                                            </ValidationProvider>
                                        </div>
                                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                                            <ValidationProvider name="PrecioA" class="required">
                                                <label class="label-sizep"> Precio Anterior</label>
                                                <vs-input type="money" class="w-full" v-model="PrecioAAP" style="float:left;padding:1px 13px" :disabled="true" />
                                            </ValidationProvider>
                                        </div>
                                    </div>
                                    <br>
                                    <div class="flex flex-wrap" v-if="AgregarCambioP">
                                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                                            <label class="label-sizep"> Status</label>
                                            <multiselect v-model="cbListaStatus" style="float:left;padding:1px" :options="ListaStatus" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Status_seleccionadoAP" :disabled="AgregarCambioP" placeholder="Seleccionar" @input="onChangeStatus">
                                                <span slot="noOptions">Lista no disponible.</span>
                                            </multiselect>
                                        </div>

                                    </div>
                                    <div class="flex flex-wrap">
                                        <div class="w-full">
                                            <br>
                                            <div>
                                                <div v-if="permiso.add_programacion_precios">
                                                    <vs-button style="float:right" @click="AgregarAgendaPrecio()" :disabled="BloquearAgregarP"> Agregar Cambio de Precio</vs-button>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                    <div class="flex flex-wrap">
                                        <div class="w-full">
                                            <br>
                                            <div v-if="BCancelarAP">
                                                <vs-button color="warning" style="float:right" @click="CancelarAP()">Cancelar</vs-button>
                                            </div>
                                        </div>
                                    </div>
                                    <vs-divider></vs-divider>

                                    <div align="center">
                                        <label class="label-sizep"> Leyenda de Colores</label>
                                    </div>
                                    <br>
                                    <div v-if="permiso.select_programacion_precios" class="flex flex-wrap" align="center">
                                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                                            <vs-button color="primary" type="filled" disabled><label class="label-sizep">Pendiente ejecutar</label></vs-button>
                                        </div>
                                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                                            <vs-button color="success" type="filled" disabled><label class="label-sizep">Cambio realizado</label></vs-button>
                                        </div>
                                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                                            <vs-button color="danger" type="filled" disabled><label class="label-sizep">Sucedio un error</label></vs-button>
                                        </div>
                                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                                            <vs-button color="warning" type="filled" disabled><label class="label-sizep">Acción cancelada</label></vs-button>
                                        </div>
                                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                                            <vs-button color="dark" type="filled" disabled><label class="label-sizep">Ningun reg. afecto</label></vs-button>
                                        </div>

                                        <br>
                                    </div>
                                </div>
                                <div v-if="permiso.select_programacion_precios">
                                    <vs-table max-items="5" pagination :data="ListaCambioPrecios" noDataText="Sin datos disponibles" search id="tb_cambioprecios">
                                        <template slot="thead">
                                            <vs-th>Fecha</vs-th>
                                            <vs-th>Nivel</vs-th>
                                            <vs-th>SubNivel</vs-th>
                                            <vs-th>Precio Nuevo</vs-th>
                                            <vs-th>Precio Anterior</vs-th>
                                            <vs-th>Status</vs-th>
                                            <vs-th>Usuario</vs-th>
                                            <vs-th>Registrado</vs-th>
                                            <vs-th>Fecha Cancela/Reactiva</vs-th>
                                            <vs-th>Cancelar/Reactivar</vs-th>

                                        </template>

                                        <template slot-scope="{data}">
                                            <vs-tr :key="indextr" v-bind:class="{'StatusAgendaPendiente': tr.Status === 'P', 'StatusAgendaFinalizado': tr.Status === 'F', 'StatusAgendaError': tr.Status === 'E', 'StatusAgendaCancelada': tr.Status === 'C', 'StatusAgendaNingun': tr.Status === 'N'}" v-for="(tr, indextr) in data">
                                                <vs-td :data="tr.Fecha">
                                                    <label> {{tr.Fecha}} </label>
                                                </vs-td>
                                                <vs-td :data="tr.Nivel">{{tr.Nivel}}</vs-td>
                                                <vs-td :data="tr.SubNivel">{{tr.SubNivel}}</vs-td>
                                                <vs-td :data="tr.PrecioNuevo">{{tr.PrecioNuevo}}</vs-td>
                                                <vs-td :data="tr.PrecioAnterior">{{tr.PrecioAnterior}}</vs-td>
                                                <vs-td :data="tr.Status">{{tr.Status}}</vs-td>
                                                <vs-td :data="tr.UsuarioAgenda">{{tr.UsuarioAgenda}}</vs-td>
                                                <vs-td :data="tr.FechaRegistro">{{tr.FechaRegistro}}</vs-td>
                                                <vs-td :data="tr.FechaReactivaCancela">{{tr.FechaReactivaCancela}}</vs-td>
                                                <vs-td v-if="permiso.canc_programacion_precios" align="center" style="background-color:#fcfcfc">
                                                    <vs-button v-if="tr.Status =='P'" color="warning" style="margin-left:1px;display:inline-block;" icon-pack="feather" icon="icon-x" @click="CancelarReactivarAgendaPrecio(tr)"></vs-button>
                                                    <vs-button v-if="tr.Status =='C'" color="primary" style="margin-left:1px;display:inline-block;" icon-pack="feather" icon="icon-check-circle" @click="CancelarReactivarAgendaPrecio(tr)"></vs-button>
                                                </vs-td>
                                            </vs-tr>
                                        </template>
                                    </vs-table>
                                </div>
                            </vs-tab>

                        </vs-tabs>
                    </div>
                </div>
                    <!-- </vs-tabs> -->

                    <vs-divider></vs-divider>
                    <!-- :disabled="invalid" -->
                    <!-- {{errors}} -->

                    <div class="con-vs-alert con-vs-alert-danger con-icon">
                        <div v-for="(error,index) in errors" :key="index">
                            <span style="font-size:18px" v-if="error.length!=0" class="feather-icon select-none relative">
                                <i class="feather icon-alert-circle"></i>
                            </span>
                            <span style="font-size:14px;margin-left:5px" v-if="error.length!=0"><b>{{index+':'}}</b>{{error}}</span>
                        </div>
                    </div>

                    <vs-button v-if="permiso.editar_producto" color="primary" style="float:right" type="border" icon-pack="feather" icon="icon-save" @click="handleSubmit(Actualizar_producto(invalid))"> Actualizar Producto</vs-button>
                    <div style="clear:both"></div>
                </div>
            </form>
        </ValidationObserver>
    </vs-popup>

    <vs-popup classContent="popup-example" :button-close-hidden="true" :title="'Mantenimiento - Precios'" :active.sync="showlistprecio" style="z-index:999999">
        <div style="padding:10px;border-radius:5px; border:1px solid #ccc; font: 22px Arial;">

            <div class="flex flex-wrap">

                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                    <vs-input v-model="prod.PreciosLista.Nivel" disabled label="Nivel:" class="w-full" name="Codigo_v2" />
                </div>

                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                    <vs-input v-model="prod.PreciosLista.SubNivel" disabled label="Sub Nivel:" class="w-full" name="Codigo_v2" />
                </div>

                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                    <label class="vs-input--label">Unidad Presentación:{{prod.PreciosLista.PresentacionNombre}}</label>
                    <vx-input-group class="">
                        <vs-input v-model="prod.PreciosLista.PresentacionUnidad" disabled />
                        <template slot="append">
                            <div class="append-text btn-addon">
                                <button type="submit" v-show="false" name="button"></button>
                                <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_presentacionPrecio()" icon="icon-search"></vs-button>
                            </div>
                        </template>
                    </vx-input-group>
                </div>

                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                    <vs-input v-model="prod.PreciosLista.Porcentaje" disabled label="% Porcentaje:" class="w-full" type="number" name="Codigo_v2" />
                </div>

                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                    <vs-input v-model="prod.PreciosLista.Factor" disabled label="Factor:" class="w-full" type="number" name="Codigo_v2" />
                </div>

                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                    <vs-input v-model="prod.PreciosLista.Precio" label="Precio:" @keydown="numbersOnlyWithDecimal(PPrecio,$event)" class="w-full" type="number" name="Codigo_v2" />
                </div>

            </div>

            <vs-divider></vs-divider>
            <vs-button color="success" style="float:right" type="filled" icon-pack="feather" icon="icon-save" @click="Actualizar_productoprecio()"> Modificar</vs-button>
            <vs-button color="primary" style="float:right" type="border" @click="showlistprecio=false;showmanprecio=true"> Cancelar</vs-button>
            <div style="clear:both"></div>

        </div>
    </vs-popup>

    <vs-popup classContent="popup-example" :button-close-hidden="true" :title="'Mantenimiento - Existencia'" :active.sync="showexistencia" style="z-index:999999">
        <div style="padding:10px;border-radius:5px; border:1px solid #ccc; font: 22px Arial;">

            <div class="flex flex-wrap">

                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                    <vs-input v-model="prod.Existencia.NumeroSerie" label="Número de Serie:" class="w-full" name="Codigo_v2" />
                </div>

                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                    <vs-input v-model="prod.Existencia.Bodega" disabled label="Bodega:" class="w-full" type="number" name="Codigo_v2" />
                </div>

                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                    <vs-input v-model="prod.Existencia.Localizacion" label="Localización:" class="w-full" name="Codigo_v2" />
                </div>

                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                    <vs-input v-model="prod.Existencia.Existencia" label="Existencia:" class="w-full" type="number" name="Codigo_v2" />
                </div>

                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                    <vs-input v-model="prod.Existencia.InventariosFisicos" label="Inv. Físicos:" class="w-full" type="number" name="Codigo_v2" />
                </div>

                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                    <vs-input v-model="prod.Existencia.UltimoInventario" disabled label="Ultimo Inv. Físico:" class="w-full" name="Codigo_v2" />
                </div>

            </div>

            <vs-divider></vs-divider>
            <vs-button color="success" style="float:right" type="filled" icon-pack="feather" icon="icon-save" @click="Actualizar_Existencia()"> Modificar</vs-button>
            <vs-button color="primary" style="float:right" type="border" @click="showlistprecio=false;showmanprecio=true;showexistencia=false"> Cancelar</vs-button>
            <div style="clear:both"></div>

        </div>
    </vs-popup>

    <!-- ========================================================================================================================================================== -->
    <!-- POPUP FIN -->
    <!-- ========================================================================================================================================================== -->

</vx-card>
</template>

<script>
// For custom error message
import {
    ValidationObserver,
    ValidationProvider,

} from "vee-validate";

//Multiselect
import Multiselect from 'vue-multiselect'
import "vue-multiselect/dist/vue-multiselect.min.css"
//Card
import StatisticsCardLine from '@/components/statistics-cards/StatisticsCardLine.vue'
//Calendario.
// import flatPickr from 'vue-flatpickr-component';
import 'flatpickr/dist/flatpickr.css';

//Vue2 date range picker
import DateRangePicker from 'vue2-daterange-picker'
//you need to import the CSS manually (in case you want to override it)
import 'vue2-daterange-picker/dist/vue2-daterange-picker.css'
var fhoy = new Date();
let finicio = new Date()
finicio.setDate(fhoy.getDate() - 365)

export default {
    name: 'componenteProductosMant',
    data() {
        return {
            configdateTimePicker: {
                allowInput: true,
                dateFormat: 'd/m/Y'
            },
            opcion: 1, //opcion seleccionada
            showmsg: false, //mensajes
            showmanprecio: true,
            showexistencia: false,
            showlistprecio: false, //Listado de Precio Mantenimiento
            selectFamilia: null,
            selectPresentacion: null,
            selectCategoriasFact: null,
            PUnidadM: null,
            selectBodega: null,
            codeCategoriaFact: '',
            codeFamilia: '',
            codePresentacion: 'UNIDAD',
            codePUnidadM: '',
            prod: {
                Empresa: '',
                Codigo: '0',
                Nombre: '',
                NombreGenerico: '',
                Categoria: '',
                CategoriaNombre: '',
                GenericosCodigo: '',
                GenericosNombre: '',
                CostoUltimo: '',
                CostoPromedio: '',
                CostoAlto: '',
                Departamento: '',
                SubDepartamento: '',
                CodigoHospIFar: '',
                Clase: '',
                SubClase: '',
                Trasladable: '',
                Descripcion: '',
                Proveedor: '',
                Marca: '',
                Warning: '',
                TipodeUso: '',
                Presentacion: '',
                CostoExterno: '',
                Minimo: '',
                Maximo: '',
                EsActivoFijo: '',
                ExcentoIva: '',
                Familia: '',
                FamiliaNombre: '',
                Consignacion: '',
                TipoBodega: '',
                Ensamblado: '',
                Foto: '',
                lista: {
                    codeLaboratorio: '',
                    NombreLaboratorio: '',
                    codeProveedor: '',
                    NombreProveedor: '',
                    codedepartamento: '',
                    Nombredepartamento: '',
                    codesubdepartamento: '',
                    Nombresubdepartamento: '',
                    codegrupo: '',
                    Nombregrupo: '',
                    codesubgrupo: '',
                    Nombresubgrupo: '',
                    PresentacionNombre: '',
                    PresentacionUnidad: '',
                    GenericosCodigo: '',
                    GenericosNombre: '',
                },
                PreciosLista: {
                    Nivel: '',
                    SubNivel: '',
                    UnidadMedida: '',
                    Porcentaje: '',
                    Factor: '',
                    Precio: '',
                    PresentacionUnidad: '',
                    PresentacionNombre: '',
                },
                Existencia: {
                    NumeroSerie: '',
                    Bodega: '',
                    Localizacion: '',
                    Existencia: '',
                    InventariosFisicos: '',
                    UltimoInventario: '',
                },
                ListadoPrecio: [],
                ListadoEstadistica: [],
                ListadoExistencia: [],
                ListadoMaxMinBod: [],
                ListadoBodega: [],
                codeBodega: '',
                ListadoMetodoCosteo: [],
                selectMetodoCosteo: '',
                ListadoTipoBodega: [],
                selectTipoBodega: '',
                ListadoCatFacturacion: [],
                selectCategoriasFact: null,
                ListadoUtilizadoOrigen: [],
                selectUtilizadoOrigen: '',
                list: [], //Fotos       
                //Datos de Mantenimiento
                SubGrupo: '',
                CampoExtra1: '',
                CampoExtra2: '',
                CampoExtra3: '',
                MinTiempo: '',
                MaxTiempo: '',
                VentaEsperada: '',
                UltimaVenta: '',
                UltimaFacturaVenta: '',
                UltimaCompra: '',
                UltimaFacturaCompra: '',
                FechaRegistro: '',
                Usuario: '',
                Costeo: '',
                Impuestos: '',
                MarkUp: '',
                Lnsp: '',
                UnidadMedida: '',
                UnidadVenta: '',
                UnidadCompra: '',
                UnidadEmpaque: '',
                Generico: '',
                Tipo: '',
                Activo: '',
                Intangible: '',
                Seriado: '',
                NOESAutorizado: '',
                ListadoFamilia: [],
                selectFamilia: null,
                ListadoProductoOpc: [],
                selectProductoOpc: {},
                ListadoProductoTipo: [],
                selectProductoTipo: '',
                PresentacionNombre: '',
                //Campos Mantenimiento Ficha del Producto
                CadenaFrio: '',
                LuzDirecta: '',
                Controlado: '',
                Stock: '',
                BajoPedido: '',
                Sasi: '',
                CatalogoGeneral: '',
                CompraLocal: '',
                Importado: '',
                NombreUsuario: '',
            },
            counterDanger: false,
            image: '',
            //Campos Nuevos 04/09/2020
            ListadoCaractProcucto: [],
            selectCaractProducto: {},
            //Campos Nuevos
            VentaHospital: '',
            UnidadCompra: '',
            UnidadCompraCar: '',
            RegistroSanitario: '',
            Contraindicaciones: '',
            usuario: '',
            nombreusuario: '',
            fechacreacion: '',
            atc: '',
            //REPORTE DE CARGO,BODEGA MADRE Y SUBBODEGA.
            FechaInicio: '',
            FechaFin: '',
            TotalCargo: '0',
            TotalDespacho: '0',
            BodegaMadreCPM: '0',
            BodegaFuente: '0',
            DataSubBodega: [],
            //Variables para Rango de Fecha
            dateRange: '',
            direction: 'ltr',
            format: 'dd/mm/yyyy',
            separator: ' - ',
            applyLabel: 'Apply',
            cancelLabel: 'Cancel',
            weekLabel: 'W',
            customRangeLabel: 'Custom Range',
            daysOfWeek: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
            monthNames: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            firstDay: 0,
            timePicker: false,
            showDropdowns: true,
            autoApply: true,
            //minDate:finicio,
            minDate: (finicio.getFullYear() + "-" + (finicio.getMonth() + 1) + "-" + finicio.getDate()) + " 00:00:00",
            // minDate:"2020-05-02 04:00:00",
            //maxDate:"2020-10-27 14:00:00",
            maxDate: (fhoy.getFullYear() + "-" + (fhoy.getMonth() + 1) + "-" + fhoy.getDate()) + " 00:00:00",
            Totalexistencia: 0,
            ListadoVista: [],
            selectvista: {},
            permiso: {
                editar_precio: false,
                editar_existencia: false,
                ver_compra: false,
                ver_clasificacion: false,
                ver_estadistica: false,
                ver_existencia: false,
                ver_max_min: false,
                ver_informacion: false,
                ver_carac_producto: false,
                ver_info_tecnico: false,
                editar_producto: false,
                add_complementarios: false,
                upd_complementarios: false,
                del_complementarios: false,
                select_complementarios: false,
                select_programacion_precios: false,
                add_programacion_precios: false,
                upd_programacion_precios: false,
                canc_programacion_precios: false
            },
            listaComplementarios: [],
            TotalComplementarios: 0,
            producto: [],
            produtoc_buscar: '',
            productoSeleccionado: {
                Codigo: '',
                marca_comercial: ''
            },
            BloqueoComplementarios: true,
            Activo: '',
            Cantidad: '',
            xCargos: '', //MULTIPLOS
            Duplos: '',
            Automatico: '', //CARGABLE
            Unico: '',
            PrimeraVez: '',
            Actualizable: '',
            Replicar: '',
            EditarComp: false,
            BCancelar: false,
            ModoEdicion: false,
            AgregarComp: false,
            ModoAgregar: false,
            BloquearCodigo: false,
            BloquearReplicar: true,
            BloquearActualizable: true,
            CodigoParent: '',
            ListaComplementaa: [],
            ValidacionesOK: '',
            BloquearAgregar: false,
            ExistenComplementos: false,
            ComplementoEliminar: '',
            ListaCambioPrecios: [],
            AgregarCambioP: false,
            BloquearAgregarP: false,
            BCancelarAP: false,
            EditarCambioP: false,
            FechaAP: '',
            NivelAP: 0,
            SubNivelAP: 1,
            PrecioNAP: 0,
            PrecioAAP: 0,
            cbListaStatus: '',
            ListaStatus: [{
                    ID: 'P',
                    DESCRIPCION: 'PENDIENTE'
                },
                {
                    ID: 'F',
                    DESCRIPCION: 'FINALIZADO'
                },
                {
                    ID: 'C',
                    DESCRIPCION: 'CANCELADA'
                },
            ],
            CorporativoAP: '',
            RegistradoAP: '',
            FechaCR_AP: '',
            IdStatus_seleccionadoAP: 'P',
        }
    },
    computed: {
        existencia_filtro() {

            // let acum = 0;       
            // const newexistencia = this.prod.ListadoExistencia.map(a => ({...a})) 
            // newexistencia.forEach(p => {
            //          acum += parseFloat(p.Existencia)
            //          this.Totalexistencia = acum 
            // })

            //  return newexistencia

            // if (this.selectvista=='0') {
            //      this.Totalexistencia =  this.prod.ListadoExistencia.reduce(function (suma,item){return suma+=parseFloat(item.Existencia)},0)
            //     return this.prod.ListadoExistencia;

            // } else if (this.selectvista=='1') {
            //      this.Totalexistencia =  this.prod.ListadoExistencia.filter(data => data.Existencia > 0).reduce(function (suma,item){return suma+=parseFloat(item.Existencia)},0)
            //     return this.prod.ListadoExistencia.filter(data => data.Existencia > 0);
            // }
            // return false

            let acum = 0;

            if (this.selectvista == '0') {
                const newexistencia = this.prod.ListadoExistencia.map(a => ({
                    ...a
                }))
                newexistencia.forEach(p => {
                    acum += parseFloat(p.Existencia)
                    this.Totalexistencia = acum
                })
                return newexistencia

            } else if (this.selectvista == '1') {
                const newexistencia2 = this.prod.ListadoExistencia.map(a => ({
                    ...a
                })).filter(data => data.Existencia > 0)
                newexistencia2.forEach(p => {
                    acum += parseFloat(p.Existencia)
                    this.Totalexistencia = acum
                })
                return newexistencia2
            }
            return false
        },
        //  total_existencia:function(){
        //     return this.prod.ListadoExistencia.filter(data => data.Existencia > 0).reduce(function (suma,item){return suma+=parseFloat(item.Existencia)},0)
        // },
    },
    mounted() {
        this.$validar_funcionalidad('/INV/INV007', 'EDITAR_PRECIO', (d) => {
            this.permiso.editar_precio = d.status
        })
        this.$validar_funcionalidad('/INV/INV007', 'EDITAR_EXISTENCIA', (d) => {
            this.permiso.editar_existencia = d.status
        })
        this.$validar_funcionalidad('/INV/INV007', 'VER_COMPRA', (d) => {
            this.permiso.ver_compra = d.status
        })
        this.$validar_funcionalidad('/INV/INV007', 'VER_CLASIFICACION', (d) => {
            this.permiso.ver_clasificacion = d.status
        })
        this.$validar_funcionalidad('/INV/INV007', 'VER_ESTADISTICA', (d) => {
            this.permiso.ver_estadistica = d.status
        })
        this.$validar_funcionalidad('/INV/INV007', 'VER_EXISTENCIA', (d) => {
            this.permiso.ver_existencia = d.status
        })
        this.$validar_funcionalidad('/INV/INV007', 'VER_MAX_MIN', (d) => {
            this.permiso.ver_max_min = d.status
        })
        this.$validar_funcionalidad('/INV/INV007', 'VER_INFORMACION', (d) => {
            this.permiso.ver_informacion = d.status
        })
        this.$validar_funcionalidad('/INV/INV007', 'VER_CARAC_PRODUCTO', (d) => {
            this.permiso.ver_carac_producto = d.status
        })
        this.$validar_funcionalidad('/INV/INV007', 'VER_INFO_TECNICO', (d) => {
            this.permiso.ver_info_tecnico = d.status
        })
        this.$validar_funcionalidad('/INV/INV007', 'EDITAR_PRODUCTO', (d) => {
            this.permiso.editar_producto = d.status
        })
        this.$validar_funcionalidad('/INV/INV007', 'ADD_COMPLEMENTARIOS', (d) => {
            this.permiso.add_complementarios = d.status
        })
        this.$validar_funcionalidad('/INV/INV007', 'UPD_COMPLEMENTARIOS', (d) => {
            this.permiso.upd_complementarios = d.status
        })
        this.$validar_funcionalidad('/INV/INV007', 'DEL_COMPLEMENTARIOS', (d) => {
            this.permiso.del_complementarios = d.status
        })
        this.$validar_funcionalidad('/INV/INV007', 'SELECT_COMPLEMENTARIOS', (d) => {
            this.permiso.select_complementarios = d.status
        })
        this.$validar_funcionalidad('/INV/INV007', 'SELECT_PROGRAMACION_PRECIOS', (d) => {
            this.permiso.select_programacion_precios = d.status
        })
        this.$validar_funcionalidad('/INV/INV035', 'ADD_PROGRAMACION_PRECIOS', (d) => {
            this.permiso.add_programacion_precios = d.status
        })
        this.$validar_funcionalidad('/INV/INV035', 'UPD_PROGRAMACION_PRECIOS', (d) => {
            this.permiso.upd_programacion_precios = d.status
        })
        this.$validar_funcionalidad('/INV/INV035', 'CANC_PROGRAMACION_PRECIOS', (d) => {
            this.permiso.canc_programacion_precios = d.status
        })
        // this.Totalexistencia =  this.existencia_filtro.filter(data => data.Existencia > 0).reduce(function (suma,item){return suma+=parseFloat(item.Existencia)},0)

    },
    props: {
        callback: {
            default: null
        },
        cerrar: {
            default: null
        }
    },
    components: {
        Multiselect,
        ValidationProvider,
        ValidationObserver,
        StatisticsCardLine,
        // flatPickr,
        DateRangePicker,
    },
    watch: {
        selectCategoriasFact(value) {
            this.codeCategoriaFact = value.Codigo
        },
        selectFamilia(value) {
            this.codeFamilia = value.Codigo
        },
        selectPresentacion(value) {
            this.codePresentacion = value.Nombre
        },
        PUnidadM(value) {
            this.codePUnidadM = value.Nombre
        },
        selectBodega(value) {
            this.prod.codeBodega = value.Codigo
        },
        showmanprecio() {
            this.cerrar()
        }
    },
    methods: {
        onFileChangeMant(e) {
            var files = e.target.files || e.dataTransfer.files;
            if (!files.length)
                return;
            this.createImageMant(files[0]);

        },
        removeImageMant() {
            this.image = '';
        },
        createImageMant(file) {
            //var image = new Image();
            var reader = new FileReader();
            var vm = this;
            reader.onload = (e) => {
                vm.image = e.target.result;
                this.guardarImagenMant();
            };
            reader.readAsDataURL(file);
        },
        Actualizar_producto(validar) {

            if (validar) {
                //false todo limpio.
                //true todo ok.
                this.$vs.notify({
                    color: 'danger',
                    title: 'Producto',
                    text: 'Estimado Usuario, Tiene campos obligatorios por Completar.',
                })

            } else {

                
                this.axios.post('/app/inventario/invProductoMant', {
                        CodigoNuevo: this.prod.Codigo,
                        Proveedor: this.prod.lista.codeProveedor.trim(),
                        Marca: this.prod.lista.codeLaboratorio,
                        Costeo: this.prod.selectMetodoCosteo.trim(),
                        CostoReferencia: this.prod.CostoReferencia,
                        UnidadMedida: this.prod.UnidadMedida,
                        UnidadVenta: this.prod.UnidadVenta,
                        UnidadCompra: this.prod.UnidadCompra,
                        Minimo: this.prod.Minimo,
                        Maximo: this.prod.Maximo,
                        unidadesmedida: this.prod.lista.PresentacionUnidad,
                        categoriaid: (this.prod.selectCategoriasFact == null || this.prod.selectCategoriasFact == "" ? "0" : this.prod.selectCategoriasFact.Codigo),
                        Departamento: this.prod.lista.codedepartamento,
                        SubDepartamento: this.prod.lista.codesubdepartamento,
                        IdGrupo: this.prod.lista.codegrupo,
                        IdSubGrupo: this.prod.lista.codesubgrupo,
                        IdGenerico: this.prod.lista.GenericosCodigo,
                        UnidadEmpaque: this.prod.UnidadEmpaque,
                        MinTiempo: this.prod.MinTiempo,
                        MaxTiempo: this.prod.MaxTiempo,
                        Tipo: this.prod.selectProductoTipo,
                        CodigoHospIFar: this.prod.CodigoHospIFar,
                        Nombre: this.prod.Nombre,
                        Generico: this.prod.NombreGenerico,
                        Descripcion: this.prod.Descripcion,
                        Activo: this.prod.selectProductoOpc.ACT,
                        Intangible: this.prod.selectProductoOpc.I,
                        Trasladable: this.prod.selectProductoOpc.T,
                        EsActivoFijo: this.prod.selectProductoOpc.A,
                        ExcentoIva: this.prod.selectProductoOpc.E,
                        Seriado: this.prod.selectProductoOpc.CS,
                        Ensamblado: this.prod.selectProductoOpc.EN,
                        NOESAutorizado: this.prod.selectProductoOpc.NAG,
                        Warning: this.prod.selectProductoOpc.W,
                        Consignacion: this.prod.selectProductoOpc.C,
                        TipodeUso: this.prod.selectUtilizadoOrigen,
                        FamiliasCodigo: (this.prod.selectFamilia == null || this.prod.selectFamilia == "" ? "0" : this.prod.selectFamilia.Codigo),
                        TipoBodega: this.prod.selectTipoBodega,
                        MarkUp: this.prod.MarkUp,
                        // Campos Nuevos 25/09/2020 Descas Ficha Del Producto.
                        VentaHospital: this.VentaHospital,
                        UnidadCompraCar: this.UnidadCompraCar,
                        RegistroSanitario: this.RegistroSanitario,
                        Contraindicaciones: this.Contraindicaciones,
                        atc: this.atc,
                        CadenaFrio: this.selectCaractProducto.CF,
                        LuzDirecta: this.selectCaractProducto.LD,
                        Controlado: this.selectCaractProducto.MC,
                        Stock: this.selectCaractProducto.PS,
                        BajoPedido: this.selectCaractProducto.BP,
                        Sasi: this.selectCaractProducto.SS,
                        CatalogoGeneral: this.selectCaractProducto.CG,
                        CompraLocal: this.selectCaractProducto.CL,
                        Importado: this.selectCaractProducto.PI,
                        bitacora: [{
                            llave: this.prod.Codigo + "-" + this.prod.Nombre + "-" + this.prod.NombreGenerico,
                            tipo: "Producto",
                            registros: [
                                'Mantenimiento Producto-Guardar'
                            ]
                        }]
                    })
                    .then(resp => {
                        
                        if (resp.data.codigo == 0 && resp.data.json.length > 0) {

                            if (resp.data.json[0].codigo == -1) {
                                this.$vs.notify({
                                    color: 'danger',
                                    title: 'Producto',
                                    text: resp.data.json[0].error,
                                })
                            } else {
                                //this.cargar_producto()
                                this.callback()
                                //SE QUITO EL 15/10/2020 COLOCAR OTRA VEZ
                                this.$vs.notify({
                                    color: 'success',
                                    title: 'Producto',
                                    text: resp.data.json[0].descripcion,
                                })
                                this.showmanprecio = false;
                            }
                        }
                    })
                    .catch(() => {
                        
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Producto',
                            text: 'Error al Actualizar la información',
                        })
                    })

            }

        },
        Actualizar_productoprecio() {
            
            this.axios.post('/app/inventario/invMntProductoPrecio', {
                    CodigoNuevo: this.prod.Codigo,
                    Nivel: this.prod.PreciosLista.Nivel,
                    SubNivel: this.prod.PreciosLista.SubNivel,
                    factor: this.prod.PreciosLista.Factor,
                    UnidadMedida: this.prod.PreciosLista.PresentacionUnidad,
                    porcentaje: this.prod.PreciosLista.Porcentaje,
                    precio: this.prod.PreciosLista.Precio,
                    bitacora: [{
                        llave: this.prod.Codigo + "-" + this.prod.PreciosLista.Nivel + "-" + this.prod.PreciosLista.SubNivel,
                        tipo: "Producto",
                        registros: [
                            'Mantenimiento Producto Precio-Actualizar'
                        ]
                    }]
                })
                .then(resp => {
                    
                    this.showmanprecio = true;
                    this.showlistprecio = false;
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        this.$vs.notify({
                            color: 'success',
                            title: 'Producto',
                            text: resp.data.json[0].descripcion,
                        })

                        this.cargar_PrecioProductos()

                    }
                })
                .catch(() => {
                    
                    this.showmanprecio = true;
                    this.showlistprecio = false;
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Producto',
                        text: 'Error al Actualizar la información',
                    })
                })
        },
        Actualizar_Existencia() {
            
            this.axios.post('/app/inventario/invExistenciaBodega', {
                    CodigoNuevo: this.prod.Codigo,
                    Bodega: this.prod.Existencia.Bodega,
                    NumeroSerie: this.prod.Existencia.NumeroSerie,
                    Localizacion: this.prod.Existencia.Localizacion,
                    Existencia: this.prod.Existencia.Existencia,
                    InventariosFisicos: this.prod.Existencia.InventariosFisicos,
                    bitacora: [{
                        llave: this.prod.Codigo + "-" + this.prod.Existencia.Existencia + "-" + this.prod.Existencia.Localizacion,
                        tipo: "Producto",
                        registros: [
                            'Mantenimiento Existencia - Actualizar'
                        ]
                    }]
                })
                .then(resp => {
                    
                    this.showmanprecio = true;
                    this.showlistprecio = false;
                    this.showexistencia = false;
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        this.$vs.notify({
                            color: 'success',
                            title: 'Producto',
                            text: resp.data.json[0].descripcion,
                        })
                        this.cargar_Existencia_Productos()
                    }
                })
                .catch(() => {
                    
                    this.showmanprecio = true;
                    this.showlistprecio = false;
                    this.showexistencia = false;
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Producto',
                        text: 'Error al Actualizar la información',
                    })
                })
        },
        guardar_Bodega() {
            this.axios.post('/app/inventario/invCatBodegaMant', {
                    CodigoNuevo: this.prod.Codigo,
                    Bodega: this.prod.codeBodega,
                    Minimo: this.prod.Minimo,
                    Maximo: this.prod.Maximo,
                    bitacora: [{
                        llave: this.codeProducto + "-" + this.Bodega + "-" + this.Minimo,
                        tipo: "Bodega",
                        registros: [
                            'Mantenimiento Bodega'
                        ]
                    }]
                })
                .then(resp => {

                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {

                        if (resp.data.json[0].codigo == -1) {
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Producto',
                                text: resp.data.json[0].error,
                            })
                        } else {
                            this.prod.ListadoMaxMinBod = []
                            this.cargar_MaxMin_Bodega()
                            this.$vs.notify({
                                color: 'success',
                                title: 'Producto',
                                text: resp.data.json[0].descripcion,
                            })
                        }
                    }
                })
                .catch(() => {
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Bodega',
                        text: 'Error al Guardar la información',
                    })
                })
        },
        opciones(op) {
            this.showmanprecio = true
            this.codeProducto = op.Codigo
            this.NombreProducto = op.Nombre
            this.prod.Empresa = op.Empresa
            this.prod.Codigo = op.Codigo
            this.prod.Nombre = op.Nombre
            this.prod.NombreGenerico = op.NombreGenerico
            this.prod.Categoria = op.Categoria
            this.prod.CostoUltimo = op.CostoUltimo
            this.prod.CostoPromedio = op.CostoPromedio
            this.prod.CostoAlto = op.CostoAlto
            this.prod.Departamento = op.Departamento
            this.prod.SubDepartamento = op.SubDepartamento
            this.prod.CodigoHospIFar = op.CodigoHospIFar
            this.prod.Clase = op.Clase
            this.prod.SubClase = op.SubClase
            this.prod.Trasladable = op.Trasladable
            this.prod.Descripcion = op.Descripcion
            this.prod.Proveedor = op.Proveedor
            this.prod.Marca = op.Marca
            this.prod.Warning = op.Warning
            this.prod.TipodeUso = op.TipodeUso
            this.prod.Presentacion = op.Presentacion
            this.prod.CostoExterno = op.CostoExterno
            this.prod.Minimo = op.Minimo
            this.prod.Maximo = op.Maximo
            this.prod.EsActivoFijo = op.EsActivoFijo
            this.prod.ExcentoIva = op.ExcentoIva
            this.prod.Familia = op.Familia
            this.prod.FamiliaNombre = op.FamiliaNombre
            this.prod.Consignacion = op.Consignacion
            this.prod.TipoBodega = op.TipoBodega
            this.prod.Ensamblado = op.Ensamblado
            //this.prod.Foto=op.Foto
            this.prod.lista.codeLaboratorio = op.Marca
            this.prod.lista.codeProveedor = op.Proveedor
            this.prod.lista.codedepartamento = (op.Departamento == "" || op.Departamento == null ? "" : op.Departamento)
            this.prod.lista.codesubdepartamento = (op.SubDepartamento == "" || op.SubDepartamento == null ? "" : op.SubDepartamento)
            this.prod.lista.codegrupo = op.IdGrupo
            this.prod.lista.codesubgrupo = op.IdSubGrupo
            this.prod.SubGrupo = op.SubGrupo
            this.prod.CampoExtra1 = op.CampoExtra1
            this.prod.CampoExtra2 = op.CampoExtra2
            this.prod.CampoExtra3 = op.CampoExtra3
            this.prod.MinTiempo = op.MinTiempo
            this.prod.MaxTiempo = op.MaxTiempo
            this.prod.VentaEsperada = op.VentaEsperada
            this.prod.UltimaVenta = op.UltimaVenta
            this.prod.UltimaFacturaVenta = op.UltimaFacturaVenta
            this.prod.UltimaCompra = op.UltimaCompra
            this.prod.UltimaFacturaCompra = op.UltimaFacturaCompra
            this.prod.FechaRegistro = op.FechaRegistro
            this.prod.Usuario = op.Usuario
            this.prod.fechacreacion = op.FechaRegistro
            this.prod.Costeo = op.Costeo
            this.prod.Impuestos = op.Impuestos
            this.prod.MarkUp = op.MarkUp
            this.prod.Lnsp = op.Lnsp
            this.prod.UnidadMedida = op.UnidadMedida
            this.prod.UnidadVenta = op.UnidadVenta
            this.prod.UnidadCompra = op.UnidadCompra
            this.prod.Generico = op.Generico
            this.prod.Tipo = op.Tipo
            this.prod.Activo = op.Activo
            this.prod.Intangible = op.Intangible
            this.prod.Seriado = op.Seriado
            this.prod.NOESAutorizado = op.NOESAutorizado
            //Variables Ficha del poducto.
            this.VentaHospital = op.VentaHospital
            this.UnidadCompraCar = op.UnidadCompraCar
            this.RegistroSanitario = op.RegistroSanitario
            this.Contraindicaciones = op.Contraindicaciones
            this.atc = op.atc
            this.prod.CadenaFrio = op.CadenaFrio
            this.prod.LuzDirecta = op.LuzDirecta
            this.prod.Controlado = op.Controlado
            this.prod.Stock = op.Stock
            this.prod.BajoPedido = op.BajoPedido
            this.prod.Sasi = op.Sasi
            this.prod.CatalogoGeneral = op.CatalogoGeneral
            this.prod.CompraLocal = op.CompraLocal
            this.prod.Importado = op.Importado
            this.prod.NombreUsuario = op.NombreUsuario
            this.cargar_PrecioProductos()
            this.cargar_Existencia_Productos()
            this.cargar_MaxMin_Bodega()
            this.cargar_Bodega()
            this.cargar_Imagen_Mant()
            this.cargar_ProductoOpcionesM()
            this.cargar_TipoProducto()
            this.cargar_MetodoCosteo()
            this.cargar_TipoBodegaMant()
            this.cargar_familiaMant()
            this.cargar_UtilizadoOrigenMant()
            //Mantenimiento Valores
            this.prod.selectProductoTipo = this.prod.Tipo
            this.prod.selectUtilizadoOrigen = this.prod.TipodeUso
            this.prod.selectMetodoCosteo = this.prod.Costeo
            this.prod.lista.PresentacionNombre = op.PresentacionNombre
            this.prod.lista.PresentacionUnidad = op.PresentacionUnidad
            this.prod.lista.GenericosCodigo = op.Generico
            this.prod.selectTipoBodega = op.TipoBodega
            this.prod.CategoriaNombre = op.CategoriaNombre
            this.prod.selectProductoOpc = {
                ACT: this.prod.Activo == '' || this.prod.Activo == null || this.prod.Activo == 'N' ? false : true,
                I: this.prod.Intangible == '' || this.prod.Intangible == null || this.prod.Intangible == 'N' ? false : true,
                T: this.prod.Trasladable == '' || this.prod.Trasladable == null || this.prod.Trasladable == 'N' ? false : true,
                A: this.prod.EsActivoFijo == '' || this.prod.EsActivoFijo == null || this.prod.EsActivoFijo == 'N' ? false : true,
                E: this.prod.ExcentoIva == '' || this.prod.ExcentoIva == null || this.prod.ExcentoIva == 'N' ? false : true,
                CS: this.prod.Seriado == '' || this.prod.Seriado == null || this.prod.Seriado == 'N' ? false : true,
                EN: this.prod.Ensamblado == '' || this.prod.Ensamblado == null || this.prod.Ensamblado == 'N' ? false : true,
                NAG: this.prod.NOESAutorizado == '' || this.prod.NOESAutorizado == null || this.prod.NOESAutorizado == 'False' ? false : true,
                C: this.prod.Consignacion == '' || this.prod.Consignacion == null || this.prod.Consignacion == 'False' ? false : true,
                W: this.prod.Warning == '' || this.prod.Warning == null || this.prod.Warning == 'N' ? false : true
            }
            this.cargar_CatFacturacionMant()
            this.cargar_CaracteristicasProducto()
            //this.prod.selectCategoriasFact=this.prod.selectTipoBodega=='' || this.prod.selectTipoBodega==null ? this.cargar_CatFacturacionMant() :{ Codigo: "01", Nombre: "Medicamentos "} ;
            this.prod.selectCategoriasFact = {
                    Codigo: this.prod.Categoria,
                    Nombre: this.prod.CategoriaNombre
                },
                // this.prod.selectCategoriasFact = {
                //     Codigo: this.prod.Categoria,
                //     Nombre: this.prod.CategoriaNombre
                // },
                this.prod.selectFamilia = {
                    Codigo: this.prod.Familia,
                    Nombre: this.prod.FamiliaNombre
                },
                this.selectCaractProducto = {
                    CF: this.prod.CadenaFrio == '' || this.prod.CadenaFrio == null || this.prod.CadenaFrio == 'N' ? false : true,
                    LD: this.prod.LuzDirecta == '' || this.prod.LuzDirecta == null || this.prod.LuzDirecta == 'N' ? false : true,
                    MC: this.prod.Controlado == '' || this.prod.Controlado == null || this.prod.Controlado == 'N' ? false : true,
                    PS: this.prod.Stock == '' || this.prod.Stock == null || this.prod.Stock == 'N' ? false : true,
                    BP: this.prod.BajoPedido == '' || this.prod.BajoPedido == null || this.prod.BajoPedido == 'N' ? false : true,
                    SS: this.prod.Sasi == '' || this.prod.Sasi == null || this.prod.Sasi == 'N' ? false : true,
                    CG: this.prod.CatalogoGeneral == '' || this.prod.CatalogoGeneral == null || this.prod.CatalogoGeneral == 'N' ? false : true,
                    CL: this.prod.CompraLocal == '' || this.prod.CompraLocal == null || this.prod.CompraLocal == 'N' ? false : true,
                    PI: this.prod.Importado == '' || this.prod.Importado == null || this.prod.Importado == 'N' ? false : true
                },
                this.image = '',
                this.Totalexistencia = 0,
                this.ListadoVista = [{
                        Codigo: '0',
                        Nombre: 'Todas'
                    },
                    {
                        Codigo: '1',
                        Nombre: 'Diferente de Cero'
                    },
                ]
            this.selectvista = '1'
            //Complementarios
            this.Complementarios()
            this.Complementaa()
            this.ListaAgendaPrecios()
        },
        opcionesListP(op) {
            //this.showmanprecio = false
            this.showlistprecio = true
            this.prod.PreciosLista.Nivel = op.Nivel
            this.prod.PreciosLista.SubNivel = op.SubNivel
            this.prod.PreciosLista.UnidadMedida = op.UnidadMedida
            this.prod.PreciosLista.Porcentaje = op.Porcentaje
            this.prod.PreciosLista.Factor = op.Factor
            this.prod.PreciosLista.Precio = op.Precio
            this.prod.PreciosLista.PresentacionUnidad = op.UnidadMedida
        },
        opcionesExistencia(op) {
            //this.showmanprecio = false
            this.showlistprecio = false
            this.showexistencia = true
            this.prod.Existencia.NumeroSerie = op.NumeroSerie
            this.prod.Existencia.Bodega = op.Bodega
            this.prod.Existencia.Localizacion = op.Localizacion
            this.prod.Existencia.Existencia = op.Existencia
            this.prod.Existencia.InventariosFisicos = op.InventariosFisicos
            this.prod.Existencia.UltimoInventario = op.UltimoInventario
        },
        cargar_familiaMant() {
            this.axios.post('/app/inventario/invFamiliaslist', {})
                .then(resp => {
                    this.prod.ListadoFamilia = resp.data.json
                })
                .catch(() => {})
        }, //Fin de metodo Familia - Mantenimiento
        cargar_CatFacturacionMant() {
            this.prod.ListadoCatFacturacion = []
            this.prod.selectCategoriasFact = null
            this.axios.post('/app/inventario/invCatFacturacionlist', {
                    TipoBodega: this.prod.selectTipoBodega
                })
                .then(resp => {
                    this.prod.ListadoCatFacturacion = resp.data.json
                })
                .catch(() => {})
        }, //Fin de metodo Categoria Facturacion - Mantenimiento
        cargar_TipoBodegaMant() {
            this.axios.post('/app/inventario/invCatTipoBodegalist', {
                    Tipo: this.prod.Tipo
                })
                .then(resp => {
                    this.prod.ListadoTipoBodega = resp.data.json
                })
                .catch(() => {})
        }, //Fin de metodo Tipo Bodega - Mantenimiento
        cargar_UtilizadoOrigenMant() {
            this.axios.post('/app/inventario/invutilorigenlist', {})
                .then(resp => {
                    this.prod.ListadoUtilizadoOrigen = resp.data.json
                })
                .catch(() => {})
        }, //Fin de metodo Utilizado Origen - Mantenimiento
        cargar_Bodega() {
            this.axios.post('/app/inventario/invCatBodegalist', {})
                .then(resp => {
                    this.prod.ListadoBodega = resp.data.json
                })
                .catch(() => {})
        }, //Fin de metodo Bodega
        cargar_MetodoCosteo() {
            this.axios.post('/app/inventario/InvListaMetodoCosteo', {})
                .then(resp => {
                    this.prod.ListadoMetodoCosteo = resp.data.json
                })
                .catch(() => {})
        }, //Fin de metodo Costeo
        onChangeTipoBodegaMant() {
            this.cargar_CatFacturacionMant()
        },
        buscar_laboratorioMan() {
            this.$refs.buscar_laboratorioMan.iniciar((data) => {
                if (data != null) {
                    this.prod.lista.codeLaboratorio = data.Codigo
                    this.prod.lista.NombreLaboratorio = data.Nombre
                }
            })
        },
        buscar_proveedorMan() {
            this.$refs.buscar_proveedorMan.iniciar((data) => {
                if (data != null) {
                    this.prod.lista.codeProveedor = data.Codigo
                    this.prod.lista.NombreProveedor = data.Nombre
                }
            })
        },
        buscar_departamentoMan() {
            this.$refs.buscar_departamentoMan.iniciar((data) => {
                if (data != null) {
                    this.prod.lista.codedepartamento = data.Codigo
                    this.prod.lista.Nombredepartamento = data.Nombre
                    this.prod.lista.codesubdepartamento = ''
                    this.prod.lista.Nombresubdepartamento = ''
                }
            })
        },
        buscar_subdepartamentoMan() {
            this.$refs.buscar_subdepartamentoMan.iniciar((data) => {
                if (data != null) {
                    this.prod.lista.codesubdepartamento = data.Codigo
                    this.prod.lista.Nombresubdepartamento = data.Nombre
                }
            })
        },
        buscar_grupoMan() {
            this.$refs.buscar_grupoMan.iniciar((data) => {
                if (data != null) {
                    this.prod.lista.codegrupo = data.Codigo
                    this.prod.lista.Nombregrupo = data.Nombre
                    this.prod.lista.codesubgrupo = ''
                    this.prod.lista.Nombresubgrupo = ''
                }
            })
        },
        buscar_subgrupoMan() {
            this.$refs.buscar_subgrupoMan.iniciar((data) => {
                if (data != null) {
                    this.prod.lista.codesubgrupo = data.Codigo
                    this.prod.lista.Nombresubgrupo = data.Nombre
                }
            })
        },
        buscar_presentacion() {
            this.$refs.buscar_presentacion.iniciar((data) => {
                if (data != null) {
                    this.prod.lista.PresentacionUnidad = data.Codigo
                    this.prod.lista.PresentacionNombre = data.Nombre
                }
            })
        },
        buscar_Genericos() {
            this.$refs.buscar_Genericos.iniciar((data) => {
                if (data != null) {
                    this.prod.lista.GenericosCodigo = data.Codigo
                    this.prod.lista.GenericosNombre = data.Nombre
                }
            })
        },
        buscar_presentacionPrecio() {
            this.$refs.buscar_presentacionPrecio.iniciar((data) => {
                if (data != null) {
                    this.prod.PreciosLista.PresentacionUnidad = data.Codigo
                    this.prod.PreciosLista.PresentacionNombre = data.Nombre
                }
            })
        },
        cargar_Imagen_Mant() {
            
            this.prod.list = []
            this.axios.post('/app/inventario/InvObtenerImagen', {
                    Categoria: 'PROD',
                    subfolder: 'PROD' + '-' + this.prod.Codigo.trim(),
                })
                .then(resp => {
                    
                 
                    if (resp.data.codigo == 0) {
                        this.prod.list = []
                        resp.data.listado.map(data => {
                            this.prod.list.push({
                                src: data.src,
                                archivo_nombre: data.nombreArchivo
                            })
                        })
                        this.prod.list.push({
                            src: null,
                            archivo_nombre: null
                        })
                    }
                    this.$vs.loading.close("#button-with-loading > .con-vs-loading")
                })
            
        },
        cargar_PrecioProductos() {
            this.axios.post('/app/inventario/InvListaPreciosProducto', {
                    CodigoNuevo: this.prod.Codigo
                })
                .then(resp => {
                    this.prod.ListadoPrecio = []
                    resp.data.json.map(data => {
                        this.prod.ListadoPrecio.push({
                            ...data
                        })
                    })

                })
        },
        cargar_Existencia_Productos() {
            this.axios.post('/app/inventario/InvListaExistenciaProducto', {
                    CodigoNuevo: this.prod.Codigo
                })
                .then(resp => {
                    this.prod.ListadoExistencia = []
                    resp.data.json.map(data => {
                        this.prod.ListadoExistencia.push({
                            ...data
                        })
                    })

                })
        },
        cargar_MaxMin_Bodega() {
            this.axios.post('/app/inventario/InvListaMaxMinBodProducto', {
                    CodigoNuevo: this.prod.Codigo
                })
                .then(resp => {
                    this.prod.ListadoMaxMinBod = []
                    resp.data.json.map(data => {
                        this.prod.ListadoMaxMinBod.push({
                            ...data
                        })
                    })

                })
        },
        cargar_Estadistica_Producto() {
            
            this.axios.post('/app/inventario/InvListaEstadisticaProducto', {
                    CodigoNuevo: this.prod.Codigo
                })
                .then(resp => {

                    this.prod.ListadoEstadistica = []
                    resp.data.json.map(data => {
                        this.prod.ListadoEstadistica.push({
                            ...data
                        })
                    })

                })
        },
        //Mantenimiento
        cargar_ProductoOpcionesM() {
            this.axios.post('/app/inventario/invProdopcionesMant', {
                    Tipo: this.prod.Tipo
                })
                .then(resp => {
                    this.prod.ListadoProductoOpc = resp.data.json
                })
                .catch(() => {})
        },
        cargar_TipoProducto() {
            this.axios.post('/app/inventario/InvListaProductoTipo', {})
                .then(resp => {
                    this.prod.ListadoProductoTipo = resp.data.json
                })
                .catch(() => {})
        },
        cargar_Lnsp() {
            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Confirmación',
                text: '¿Estimado Usuario Esta Seguro de realizar el cálculo.??',
                accept: () => {
                    
                    this.axios.post('/app/inventario/InvProcesoLnsp', {
                            CodigoNuevo: this.prod.Codigo,
                            MarkUp: this.prod.MarkUp,
                            bitacora: [{
                                llave: this.prod.Codigo + "-" + this.prod.MarkUp,
                                tipo: "Producto",
                                registros: [
                                    'cargar Lnsp Producto-Modificar'
                                ]
                            }]
                        })
                        .then(resp => {
                            
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {

                                if (resp.data.json[0].codigo == -1) {
                                    this.$vs.notify({
                                        color: 'danger',
                                        title: 'Producto',
                                        text: resp.data.json[0].error,
                                    })
                                } else if (resp.data.json[0].codigo == -2) {
                                    //this.cargar_producto()
                                    this.callback()
                                    //SE QUITO EL 15/10/2020 COLOCAR OTRA VEZ
                                    this.$vs.notify({
                                        color: 'danger',
                                        title: 'Producto',
                                        text: resp.data.json[0].error,
                                    })
                                } else {
                                    this.$vs.notify({
                                        color: 'success',
                                        title: 'Producto',
                                        text: resp.data.json[0].descripcion,
                                    })

                                    this.showmsg = false;
                                    this.showmanprecio = true;
                                    this.prod.Lnsp = resp.data.json[0].Lnsp;
                                    this.prod.MarkUp = resp.data.json[0].MarkUp;
                                    //this.showmsg = false;

                                }
                            }
                        })
                        .catch(() => {
                            
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Producto',
                                text: 'Error al Actualizar la información',
                            })
                        })

                }
            })

        },
        cargar_CaracteristicasProducto() {
            this.axios.post('/app/inventario/invCaractProducto', {
                    Tipo: this.prod.Tipo
                })
                .then(resp => {
                    this.ListadoCaractProcucto = resp.data.json
                })
               .catch(() => {})
        }, //Fin de metodo Tipo Bodega
        remove_Imagen_Mant(index) {
            
            this.axios.post('/app/inventario/InvEliminarImagen', {
                    Categoria: 'PROD',
                    subfolder: 'PROD' + '-' + this.prod.Codigo.trim(),
                    nombreArchivo: this.prod.list[index].archivo_nombre
                })
                .then(resp => {
                    
                    if (resp.data.codigo == 0) {
                        this.prod.list.splice(index, 1)
                    }

                })
        },
        guardarImagenMant() {

            if (this.image == "" || this.image == null || this.prod.Codigo == "" || this.prod.Codigo == "0" || this.prod.Codigo == null) {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Producto',
                    text: 'Estimado Usuario,Cargue una Imagen.',
                })
            } else {

                
                this.axios.post('/app/inventario/InvGuardarImagen', {
                        base64String: this.image.replace('data:image/jpeg;base64,', '').replace('data:image/png;base64,', '').replace('data:image/jpg;base64,', ''),
                        Categoria: 'PROD',
                        subfolder: 'PROD' + '-' + this.prod.Codigo.trim(),
                        nombreArchivo: 'PRODUCTO'
                    })
                    .then(resp => {
                        
                        this.removeImageMant()
                        this.cargar_Imagen_Mant()
                    })

            }

        },
        //COMPLEMENTARIOS
        cargar_productoc() {

            this.producto = [];
            if (this.produtoc_buscar != this.prod.Codigo) {
                this.axios.post('/app/inventario/ProductosQuery', {
                        Opcion: 'C',
                        SubOpcion: '2',
                        CNombreP: this.produtoc_buscar.trim(),
                        CodigoP: this.prod.Codigo
                    })
                    .then(resp => {
                        if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                            this.producto = []
                            resp.data.json.map(data => {
                                this.producto.push({
                                    ...data
                                })
                            })
                            this.AgregarComp = true
                            this.ModoAgregar = true
                            this.Mostrar_resultado_producto(this.producto);
                            this.BloqueoComplementarios = false,
                                this.Activo = true,
                                this.Cantidad = 1,
                                this.xCargos = false, //MULTIPLOS
                                this.Duplos = true,
                                this.Automatico = true, //CARGABLE
                                this.Unico = false,
                                this.PrimeraVez = false,
                                this.Actualizable = false,
                                this.Replicar = true
                            this.BloquearActualizable = true
                            this.BloquearReplicar = true
                            this.ValidacionesPrevias()
                        } else {

                            this.producto = []
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Producto',
                                text: (resp.data.codigo < 0) ? resp.data.descripcion : 'Sin Registros',
                            })
                        }
                    })

            } else {
                this.AgregarComp = false
                this.ModoAgregar = false
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Complementarios',
                    text: "Ingrese código correcto de Complentario.",
                })
            }

        },
        Mostrar_resultado_producto(value) {
            if (value.length == 1) {
                this.productoSeleccionado.Codigo = value.find(obj => obj.Codigo != '').Codigo;
                this.productoSeleccionado.marca_comercial = value.find(obj => obj.Nombre != '').Nombre;
            } else if (value.length > 1) {
                this.estadoVentanaEmergenteBusqueda = true;
            }

        },
        Seleccionar_Producto(value) {
            this.producto = [];
            this.productoSeleccionado.Codigo = value.Codigo;
            this.productoSeleccionado.marca_comercial = value.Nombre;
            this.estadoVentanaEmergenteBusqueda = false;

        },
        Complementarios() {
            this.axios.post('/app/inventario/ListaComplementarios', {
                    Opcion: 'C',
                    SubOpcion: '1',
                    CodigoP: this.prod.Codigo,
                })
                .then(resp => {

                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Complementarios',
                            text: "Este Producto no tiene asignado complementarios",
                        })

                        this.listaComplementarios = [];
                    } else {
                        this.listaComplementarios = [];
                        this.listaComplementarios = resp.data.json
                    }
                })
        },
        ValidacionesPrevias() {

            if (this.Cantidad == '' || this.Cantidad <= 0) {
                this.Cantidad = 1;
            }

            //No se debe permitir ingresar el mismo código del producto/servicio que se esta complementand
            if (this.productoSeleccionado.Codigo == this.prod.Codigo) {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Complementarios',
                    text: "Código no valido, no debe ingresar el mismo codigo del producto a complementar.",
                })
            } else {
                //Verificamos que el código recien ingresado no sea un código con complementos, que ya exista previamente en la orden actual
                //qryExisteCodigoLocalmente
                if (this.productoSeleccionado.Codigo != '' && this.ModoAgregar == true) {
                    this.axios.post('/app/inventario/ExisteCodigoLocalmente', {
                            Opcion: 'C',
                            SubOpcion: '4',
                            CodigoP: this.prod.Codigo,
                            CodigoP2: this.productoSeleccionado.Codigo
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.axios.post('/app/inventario/PoseeComplementarios', {
                                        Opcion: 'C',
                                        SubOpcion: '3',
                                        CodigoP2: this.productoSeleccionado.Codigo
                                    })
                                    .then(resp => {
                                        if (resp.data.codigo == 0) {
                                            this.CodigoParent = resp.data.json[0].CodigoParent
                                            if (this.CodigoParent != '') {
                                                if (this.productoSeleccionado.Codigo == this.CodigoParent) {
                                                    this.BloquearReplicar = false
                                                    this.BloquearActualizable = true
                                                } else {
                                                    this.BloquearReplicar = true
                                                    this.BloquearActualizable = false
                                                }
                                            } else {
                                                this.BloquearReplicar = false
                                                this.BloquearActualizable = true
                                            }
                                        }
                                    })
                                this.BloquearAgregar = false

                            } else {
                                //this.BloquearAgregar = true
                                this.Cancelar()
                                this.AgregarComp = false
                            }
                        })
                }
            }
        },
        EditarComplementario(datos) {
            this.EditarComp = true
            this.AgregarComp = false
            this.ModoEdicion = true
            this.ModoAgregar = false
            this.BloquearCodigo = true
            this.BloqueoComplementarios = false,
                this.produtoc_buscar = datos.ProductoSecundario
            this.productoSeleccionado.Codigo = datos.ProductoSecundario
            this.productoSeleccionado.marca_comercial = datos.Nombre
            this.productoParentGrid = datos.ProductoParent
            this.Cantidad = datos.Cantidad
            this.Nombre = datos.Nombre
            this.xCargos = datos.MultiplicarXCargos == 'N' ? false : true
            this.Duplos = datos.PermitirDuplicidad == 'N' ? false : true
            this.Activo = datos.Activo == 'N' ? false : true
            this.Automatico = datos.Cargable == 'N' ? false : true
            this.Unico = datos.Unico == 'N' ? false : true
            this.UnaVezXArea = datos.UnaVezXArea == 'N' ? false : true
            this.Replicar = datos.ReplicarCambios == 'N' ? false : true
            this.Actualizable = datos.Actualizable == 'N' ? false : true
        },
        EditarComplementarioGrabar() {
            
            this.ValidacionesPrevias()
            this.AgregarComplementario()
        },
        AgregarComplementario() {
            this.AgregarComp = true
            if (this.productoSeleccionado.Codigo != '') {
                if (this.ModoAgregar == true | this.ModoEdicion) {
                    this.ModoEdicion = false
                    this.axios.post('/app/inventario/IngresoComplementarios', {
                            Opcion: 'I',
                            SubOpcion: '1',
                            CodigoP: this.prod.Codigo,
                            CodigoP2: this.productoSeleccionado.Codigo,
                            CodigoPParent: this.CodigoParent,
                            Cantidad: this.Cantidad,
                            MultiplicarXCargos: this.xCargos ? 'S' : 'N',
                            PermiteD: this.Duplos ? 'S' : 'N',
                            CActivo: this.Activo ? 'S' : 'N',
                            Cargable: this.Automatico ? 'S' : 'N',
                            Unico: this.Unico ? 'S' : 'N',
                            UnaVezXArea: this.PrimeraVez ? 'S' : 'N',
                            Replicar: this.Replicar ? 'S' : 'N',
                            Actualizar: this.Actualizable ? 'S' : 'N'
                        })

                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.Actualizable = true,
                                    this.Replicar = false
                                this.axios.post('/app/inventario/IngresoSusComplementarios', {
                                        Opcion: 'I',
                                        SubOpcion: '2',
                                        CodigoP: this.prod.Codigo,
                                        CodigoP2: this.productoSeleccionado.Codigo,
                                        Replicar: this.Replicar ? 'S' : 'N',
                                        Actualizar: this.Actualizable ? 'S' : 'N',
                                        Cantidad: this.Cantidad,
                                    })
                                    .then(() => {
                                        if (this.Replicar == true) {
                                            this.ActualizarHerenciaComplementos()
                                        }
                                        if (this.Actualizar == true) {
                                            this.ActualizarRegistroOriginal()
                                        }
                                        this.AgregarComp = false
                                        this.Complementarios()
                                        this.Cancelar()
                                        // this.BloquearAgregar = false
                                    })
                            }
                        })

                } else if (this.ModoEdicion == true && this.ModoAgregar == false) {
                    this.axios.post('/app/inventario/EditarComplementario', {
                            Opcion: 'A',
                            SubOpcion: '1',
                            CodigoP: this.prod.Codigo,
                            CodigoP2: this.productoSeleccionado.Codigo,
                            Cantidad: this.Cantidad,
                            MultiplicarXCargos: this.xCargos ? 'S' : 'N',
                            PermiteD: this.Duplos ? 'S' : 'N',
                            CActivo: this.Activo ? 'S' : 'N',
                            Cargable: this.Automatico ? 'S' : 'N',
                            Unico: this.Unico ? 'S' : 'N',
                            UnaVezXArea: this.PrimeraVez ? 'S' : 'N',
                            Replicar: this.Replicar ? 'S' : 'N',
                            Actualizar: this.Actualizable ? 'S' : 'N'
                        })
                        .then(() => {
                            this.Cancelar()
                            this.Complementarios()
                        })
                }
               this.EditarComp = false
               
            }

        },
        ActualizarHerenciaComplementos() {
            this.axios.post('/app/inventario/ActualizaHerencia', {
                Opcion: 'A',
                SubOpcion: '2',
                CodigoP: this.prod.Codigo,
                CodigoP2: this.productoSeleccionado.Codigo,
                Cantidad: this.Cantidad,
                MultiplicarXCargos: this.xCargos ? 'S' : 'N',
                PermiteD: this.Duplos ? 'S' : 'N',
                CActivo: this.Activo ? 'S' : 'N',
                Cargable: this.Automatico ? 'S' : 'N',
                Unico: this.Unico ? 'S' : 'N',
                UnaVezXArea: this.PrimeraVez ? 'S' : 'N',
                Replicar: this.Replicar ? 'S' : 'N',
                Actualizar: this.Actualizable ? 'S' : 'N',
            })
        },
        ActualizarRegistroOriginal() {
            //Recupera Definición Oridginal 
            this.axios.post('/app/inventario/RecuperarDefinicionOriginal', {
                    Opcion: 'C',
                    SubOpcion: '5',
                    CodigoP: this.productoParentGrid,
                    CodigoP2: this.productoSeleccionado.Codigo,
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Complemenatarios',
                            text: "Actualización Registro Original",
                        })
                    } else {
                        //Actualizamos los registros heredados a otras definiciones
                        this.axios.post('/app/inventario/ActualizaEstaDefinicion', {
                                Opcion: 'A',
                                SubOpcion: '3',
                                CodigoP: this.prod.Codigo,
                                CodigoP2: this.productoSeleccionado.Codigo,
                                CodigoPParent: this.CodigoParent,
                                Cantidad: resp.data.Cantidad,
                                MultiplicarXCargos: resp.data.MultiplicarXCargos ? 'S' : 'N',
                                PermiteD: resp.data.PermitirDuplicidad ? 'S' : 'N',
                                CActivo: resp.data.Activo ? 'S' : 'N',
                                Cargable: resp.data.Automatico ? 'S' : 'N',
                                Unico: resp.data.Unico ? 'S' : 'N',
                                UnaVezXArea: resp.data.PrimeraVez ? 'S' : 'N',
                            })
                            .then(resp => {
                                if (resp.data.codigo != 0) {
                                    this.$vs.notify({
                                        color: '#B71C1C',
                                        title: 'Complemenatarios',
                                        text: "Definición Actualizada",
                                    })
                                }
                            })

                    }
                })

        },
        EliminarComplementario(datos) {
            var tempSec = '';
            var TempBand = false;
            var tempPrincipal = '';
            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Confirmación',
                text: '¿Desea borrar este registro.? ',
                acceptText: 'Aceptar',
                cancelText: 'Cancelar',
                accept: () => {

                    tempSec = datos.ProductoSecundario;
                    if (tempSec == datos.ProductoParent) {
                        TempBand = true;
                    } else {
                        TempBand = false;
                    }
                    tempPrincipal = datos.ProductoPrimario
                    this.axios.post('/app/inventario/EliminarComplementos', {
                            Opcion: 'E',
                            SubOpcion: '1',
                            CodigoP: datos.ProductoPrimario,
                            CodigoP2: datos.ProductoSecundario,
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                if (TempBand == true) {
                                    this.$vs.dialog({
                                        type: 'confirm',
                                        color: 'danger',
                                        title: 'Confirmación',
                                        text: '¿Desea eliminar los productos heredados.? ',
                                        acceptText: 'Aceptar',
                                        cancelText: 'Cancelar',
                                        accept: () => {
                                            this.axios.post('/app/inventario/BorraLineasC', {
                                                    Opcion: 'E',
                                                    SubOpcion: '2',
                                                    CodigoP: tempPrincipal,
                                                    CodigoPParent: tempSec,
                                                })
                                                .then(() => {
                                                    this.Complementarios()
                                                })
                                        },
                                        cancel: () => {
                                            this.axios.post('/app/inventario/Desrelacionar', {
                                                    Opcion: 'A',
                                                    SubOpcion: '4',
                                                    CodigoP: this.prod.Codigo,
                                                    CodigoPParent: tempSec,
                                                })
                                                .then(() => {})
                                        }

                                    })
                                }
                                this.Complementarios()
                            }

                        })
                },
                cancel: () => {}
            })

        },
        Cancelar() {
            this.EditarComp = false
            this.produtoc_buscar = ''
            this.productoSeleccionado.Codigo = ''
            this.productoSeleccionado.marca_comercial = ''
            this.Cantidad = ''
            this.Nombre = ''
            this.xCargos = false
            this.Duplos = false
            this.Activo = false
            this.Automatico = false
            this.Unico = false
            this.PrimeraVez = false
            this.Replicar = false
            this.Actualizable = false

        },
        Complementaa() {

            this.axios.post('/app/inventario/ListaComplementaa', {
                    Opcion: 'C',
                    SubOpcion: '6',
                    CodigoP2: this.prod.Codigo,
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Complementarios',
                            text: "",
                        })
                        this.ListaComplementaa = [];
                    } else {
                        this.ListaComplementaa = [];
                        this.ListaComplementaa = resp.data.json
                    }
                })
        },
        ListaAgendaPrecios() {

            this.axios.post('/app/inventario/ListaCambioPrecios', {
                    Opcion: 'C',
                    SubOpcion: '7',
                    CodigoP: this.prod.Codigo,
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Complementarios',
                            text: "",
                        })
                        this.ListaCambioPrecios = [];
                    } else {
                        this.ListaCambioPrecios = [];
                        this.ListaCambioPrecios = resp.data.json
                    }
                })
        },

        AgregarAgendaPrecio() {
            this.cbListaStatus = {
                ID: 'P',
                DESCRIPCION: 'PENDIENTE'
            };
            this.AgregarCambioP = true
            this.BCancelarAP = true

            if (this.PrecioAAP != 0) {
                this.axios.post('/app/inventario/IngresoAgendaPrecios', {
                        Opcion: 'I',
                        SubOpcion: '3',
                        CodigoP: this.prod.Codigo,
                        Nivel: this.NivelAP,
                        SubNivel: this.SubNivelAP,
                        StatusNuevo: this.IdStatus_seleccionadoAP,
                        APrecioNuevo: this.PrecioNAP,
                        APrecioAnterior: this.PrecioAAP
                    })
                    .then(resp => {
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Complementarios',
                                text: "",
                            })
                            this.CancelarAP()
                        } else {
                            this.AgregarCambioP = false
                            this.BCancelarAP = false
                            this.ListaAgendaPrecios()
                            this.CancelarAP()
                        }
                    })

            }
        },
        PrecioActual() {
            this.PrecioNAP = parseFloat(this.PrecioNAP).toFixed(2)
            this.axios.post('/app/inventario/PrecioActual', {
                    Opcion: 'C',
                    SubOpcion: '8',
                    CodigoP: this.prod.Codigo,
                    Nivel: this.NivelAP,
                    SubNivel: this.SubNivelAP
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Complementarios',
                            text: "",
                        })
                        this.PrecioAAP = '';
                    } else {
                        this.PrecioAAP = '';
                        this.PrecioAAP = parseFloat(resp.data.json[0].Precio).toFixed(2)
                        this.AgregarCambioP = true
                    }
                })
        },
        CancelarReactivarAgendaPrecio(datos) {
            this.axios.post('/app/inventario/CambiarEstatus', {
                    Opcion: 'A',
                    SubOpcion: '5',
                    CodigoP: this.prod.Codigo,
                    Nivel: datos.Nivel,
                    SubNivel: datos.SubNivel,
                    StatusNuevo: datos.Status == 'P' ? 'C' : 'P',
                    FechaCambio: datos.Fecha
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Complementarios',
                            text: "",
                        })
                    } else {
                        this.AgregarCambioP = false
                        this.BCancelarAP = false
                        this.ListaAgendaPrecios()
                        this.CancelarAP()
                    }
                })

        },
        Status_seleccionadoAP({
            DESCRIPCION
        }) {
            return `${DESCRIPCION} `
        },
        onChangeStatus(value) {
            if (value !== null && value.lengh !== 0) {
                this.IdStatus_seleccionadoAP = value.ID
            } else {
                this.IdStatus_seleccionadoAP = '';
            }
        },
        CancelarAP() {
            this.NivelAP = 0
            this.PrecioNAP = 0
            this.PrecioAAP = 0
            this.BCancelarAP = false
            this.AgregarCambioP = false

        },
        //REPORTE
        generarreporte() {

            if (this.FechaInicio == "") {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Producto',
                    text: 'Ingrese Fecha Inicial.',
                })
                return false;
            }

            if (this.FechaFin == "") {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Producto',
                    text: 'Ingrese Fecha Final.',
                })
                return false;
            }

            this.Reporte_CargosProducto();
            this.Reporte_BodegaMadre();
            this.Reporte_SubBodega();
        },
        Reporte_CargosProducto() {
            
            //this.$vs.loading((this.FechaInicio.startDate.getDate().length==1 ? "0"+this.FechaInicio.startDate.getDate() : this.FechaInicio.startDate.getDate()));

            this.axios.post('/app/inventario/invReporteCargosProd', {
                    opciones: {
                        Codigo: this.prod.Codigo,
                        // fechainicio: this.FechaInicio,
                        // fechafin: this.FechaFin,
                        fechainicio: ((this.FechaInicio.startDate.getDate() < 10 ? "0" + this.FechaInicio.startDate.getDate() : this.FechaInicio.startDate.getDate()) + "/" + ((this.FechaInicio.startDate.getMonth() + 1) < 10 ? "0" + (this.FechaInicio.startDate.getMonth() + 1) : (this.FechaInicio.startDate.getMonth() + 1)) + "/" + this.FechaInicio.startDate.getFullYear()),
                        fechafin: ((this.FechaFin.startDate.getDate() < 10 ? "0" + this.FechaFin.startDate.getDate() : this.FechaFin.startDate.getDate()) + "/" + ((this.FechaFin.startDate.getMonth() + 1) < 10 ? "0" + (this.FechaFin.startDate.getMonth() + 1) : (this.FechaFin.startDate.getMonth() + 1)) + "/" + this.FechaFin.startDate.getFullYear()),
                        //fechafin: ( this.FechaFin.startDate.getDate() + "/" + ( this.FechaFin.startDate.getMonth() + 1) + "/" +  this.FechaFin.startDate.getFullYear()),
                    },
                    bitacora: [{
                        llave: this.prod.Codigo,
                        tipo: "Producto",
                        registros: [
                            'Reporte CargosProducto'
                        ]
                    }]
                })
                .then(resp => {
                    

                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {

                        if (resp.data.json[0].codigo == -1) {
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Producto',
                                text: resp.data.json[0].error,
                            })
                        } else if (resp.data.json[0].codigo == -2) {
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Producto',
                                text: resp.data.json[0].error,
                            })
                        } else {
                            this.TotalCargo = resp.data.json[0].Cantidad;
                        }
                    } else {
                        this.TotalCargo = '0';
                    }
                })
                .catch(() => {
                    
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Producto',
                        text: 'Error al obtener Reporte de Cargos de Producto.',
                    })
                })
        },
        Reporte_BodegaMadre() {
            
            this.axios.post('/app/inventario/invReporteBodegaMadre', {
                    opciones: {
                        Codigo: this.prod.Codigo,
                        // fechainicio: this.FechaInicio,
                        // fechafin: this.FechaFin,
                        //fechainicio: (this.FechaInicio.startDate.getDate() + "/" + ( this.FechaInicio.startDate.getMonth() + 1) + "/" +  this.FechaInicio.startDate.getFullYear()),
                        //fechafin: ( this.FechaFin.startDate.getDate() + "/" + ( this.FechaFin.startDate.getMonth() + 1) + "/" +  this.FechaFin.startDate.getFullYear()),
                        fechainicio: ((this.FechaInicio.startDate.getDate() < 10 ? "0" + this.FechaInicio.startDate.getDate() : this.FechaInicio.startDate.getDate()) + "/" + ((this.FechaInicio.startDate.getMonth() + 1) < 10 ? "0" + (this.FechaInicio.startDate.getMonth() + 1) : (this.FechaInicio.startDate.getMonth() + 1)) + "/" + this.FechaInicio.startDate.getFullYear()),
                        fechafin: ((this.FechaFin.startDate.getDate() < 10 ? "0" + this.FechaFin.startDate.getDate() : this.FechaFin.startDate.getDate()) + "/" + ((this.FechaFin.startDate.getMonth() + 1) < 10 ? "0" + (this.FechaFin.startDate.getMonth() + 1) : (this.FechaFin.startDate.getMonth() + 1)) + "/" + this.FechaFin.startDate.getFullYear()),
                    },
                    bitacora: [{
                        llave: this.prod.Codigo,
                        tipo: "Producto",
                        registros: [
                            'Reporte Bodega Madre Producto.'
                        ]
                    }]
                })
                .then(resp => {
                    
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {

                        if (resp.data.json[0].codigo == -1) {
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Producto',
                                text: resp.data.json[0].error,
                            })
                        } else if (resp.data.json[0].codigo == -2) {
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Producto',
                                text: resp.data.json[0].error,
                            })
                        } else {
                            this.TotalDespacho = (resp.data.json[0].SumadDespachos == null || resp.data.json[0].SumadDespachos == "" ? "0" : resp.data.json[0].SumadDespachos);
                            this.BodegaMadreCPM = (resp.data.json[0].PromedioMensual == null || resp.data.json[0].PromedioMensual == "" ? "0" : resp.data.json[0].PromedioMensual);
                            this.BodegaFuente = (resp.data.json[0].BodegaFuente == null || resp.data.json[0].BodegaFuente == "" ? "0" : resp.data.json[0].BodegaFuente);
                        }
                    } else {
                        this.TotalDespacho = '0';
                        this.BodegaMadreCPM = '0';
                        this.BodegaFuente = '0';
                    }
                })
                .catch(() => {
                    
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Producto',
                        text: 'Error al obtener Reporte de Cargos de Producto.',
                    })
                })
        },
        Reporte_SubBodega() {
            
            this.axios.post('/app/inventario/invReporteSubBodega', {
                    opciones: {
                        Codigo: this.prod.Codigo,
                        // fechainicio: this.FechaInicio,
                        // fechafin: this.FechaFin,
                        //fechainicio: (this.FechaInicio.startDate.getDate() + "/" + ( this.FechaInicio.startDate.getMonth() + 1) + "/" +  this.FechaInicio.startDate.getFullYear()),
                        //fechafin: ( this.FechaFin.startDate.getDate() + "/" + ( this.FechaFin.startDate.getMonth() + 1) + "/" +  this.FechaFin.startDate.getFullYear()),
                        fechainicio: ((this.FechaInicio.startDate.getDate() < 10 ? "0" + this.FechaInicio.startDate.getDate() : this.FechaInicio.startDate.getDate()) + "/" + ((this.FechaInicio.startDate.getMonth() + 1) < 10 ? "0" + (this.FechaInicio.startDate.getMonth() + 1) : (this.FechaInicio.startDate.getMonth() + 1)) + "/" + this.FechaInicio.startDate.getFullYear()),
                        fechafin: ((this.FechaFin.startDate.getDate() < 10 ? "0" + this.FechaFin.startDate.getDate() : this.FechaFin.startDate.getDate()) + "/" + ((this.FechaFin.startDate.getMonth() + 1) < 10 ? "0" + (this.FechaFin.startDate.getMonth() + 1) : (this.FechaFin.startDate.getMonth() + 1)) + "/" + this.FechaFin.startDate.getFullYear()),

                    },
                    bitacora: [{
                        llave: this.prod.Codigo,
                        tipo: "Producto",
                        registros: [
                            'Reporte Sub-Bodega Producto.'
                        ]
                    }]
                })
                .then(resp => {
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {

                        if (resp.data.json[0].codigo == -1) {
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Producto',
                                text: resp.data.json[0].error,
                            })
                        } else if (resp.data.json[0].codigo == -2) {
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Producto',
                                text: resp.data.json[0].error,
                            })
                        } else {
                            this.DataSubBodega = []
                            resp.data.json.map(data => {
                                this.DataSubBodega.push({
                                    ...data
                                })
                            })
                        }
                    }
                })
                .catch(() => {
                    
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Producto',
                        text: 'Error al obtener Reporte de Cargos de Producto.',
                    })
                })
        },
        numbersOnlyWithDecimal(objectHtml, event) {

            if (event.keyCode == 46 || event.keyCode == 190 || event.keyCode == 110 ||
                event.keyCode == 8 || event.keyCode == 9 || event.keyCode == 27 ||
                event.keyCode == 13 ||
                (event.keyCode == 65 && event.ctrlKey === true) ||
                (event.keyCode >= 35 && event.keyCode <= 39)) {
                return;
            } else {
                // If it's not a number stop the keypress
                if (event.shiftKey || (event.keyCode < 48 || event.keyCode > 57) &&
                    (event.keyCode < 96 || event.keyCode > 105)) {
                    event.preventDefault();
                } // else {

                // }
            }

        },

    },

}
</script>
<style scoped>
.label-size {
    font-size: 16px;
    font-weight: bold;
}

.label-sizep {
    font-size: 15px;
}

.StatusAgendaPendiente {
    background: #7ec7df;
    color: white;
    font-weight: bold;
    font-size: 16px;
    border-style: solid;
    border-color: white;
}

.StatusAgendaFinalizado {
    background: #92d2ad;
    color: white;
    font-weight: bold;
    font-size: 16px;
    border-style: solid;
    border-color: white;
}

.StatusAgendaError {
    background: #f5a9ab;
    color: white;
    font-weight: bold;
    font-size: 16px;
    border-style: solid;
    border-color: white;
}

.StatusAgendaCancelada {
    background: #fbd999;
    color: white;
    font-weight: bold;
    font-size: 16px;
    border-style: solid;
    border-color: white;
}

.StatusAgendaNingun {
    background: #8e8e8e;
    color: white;
    font-weight: bold;
    font-size: 16px;
    border-style: solid;
    border-color: white;
}
</style>
