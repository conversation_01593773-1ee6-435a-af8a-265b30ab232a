<template>
    <vx-card title="Recepción Orden de Compra.">
        <div class="content content-pagex">
            <vs-divider></vs-divider>
            <!----------------------- VISTA GENERAL ---------->
            <div class="flex flex-wrap">
    
                <div style="margin-right: 20px" class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <label class="typo__label">Estado</label>
                    <multiselect v-model="cb_lista_operacion" :options="lista_estado" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Operacion_seleccionada" placeholder="Seleccionar" @input="onChangeEstado">
                        <span slot="noOptions">Lista no disponible.</span>
                    </multiselect>
                </div>
    
                <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7">
                    <label class="typo__label">Fecha Inicio:</label>
                    <vs-input type="date" v-model="fecha_inicio" name="date1" />
                </div>
    
                <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7">
                    <label class="typo__label">Fecha Final:</label>
                    <vs-input type="date" v-model="fecha_final" name="date1" />
                </div>
    
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <vs-button style="float:left;margin: 20px" color="primary" type="filled" icon-pack="fas" icon="fa-search" @click="Consultar_OrdenEnc()"> Búsqueda</vs-button>
                </div>
            </div>
            <div>
                <vs-divider></vs-divider>
                <vx-card>
                    <div class="buttonNuevo">
                        <vs-button @click="NuevoIngreso" color="success" icon-pack="fas" icon="fa-plus-circle" >Ingreso por Caja Chica</vs-button>
                    </div>
                    <div>
                        <vs-table2 max-items="10" pagination tooltip :data="Lista_OrdenesEnc" search id="tb_lista_solicitud">
                            <template slot="thead">
                                <th>Nº. Orden Compra</th>
                                <th>Envio</th>
                                <th>Nº. Movimiento</th>
                                <th>Proveedor</th>
                                <th>Fecha</th>
                                <th>Documento</th>
                                <th>Fecha Documento</th>
                                <th style="text-align: center;">Recepcionada</th>
                                <th width="30px">Ingreso</th>
                                <th width="30px">Reversión</th>
                                <th width="30px">Información</th>
                                <th width="30px">Contraseña</th>
                                <th width="30px">Reversión Conta</th>
                            </template>
            
                            <template slot-scope="{data}">
                                <tr :style="getStyle(tr.DiasTransucrridos, tr.orden_ortopedia)" :key="indextr" v-for="(tr, indextr) in data">
            
                                    <vs-td2 :data="data[indextr].IDORDENCOMPRAENC">
                                        {{data[indextr].IDORDENCOMPRAENC}}
                                    </vs-td2>

                                    <vs-td2 :data="data[indextr].NumeroProformaProveedor">
                                        {{data[indextr].NumeroProformaProveedor}}
                                    </vs-td2>

                                    <vs-td2 :data="data[indextr].CODIGO_MOVIMIENTO">
                                        {{data[indextr].CODIGO_MOVIMIENTO}}
                                    </vs-td2>
            
                                    <vs-td2 :data="data[indextr].PROVEEDOR">
                                        {{data[indextr].PROVEEDOR}}
                                    </vs-td2>
            
                                    <vs-td2 :data="data[indextr].FECHA_REGISTRO">
                                        {{data[indextr].FECHA_REGISTRO}}
                                    </vs-td2>

                                    <vs-td2 :data="data[indextr].DOCUMENTO">
                                        {{data[indextr].DOCUMENTO}}
                                    </vs-td2>

                                    <vs-td2 :data="data[indextr].FECHA_DOCUMENTO">
                                        {{data[indextr].FECHA_DOCUMENTO}}
                                    </vs-td2>
            
                                    <vs-td2 style="text-align: center;" :data="data[indextr].ESTADO">
                                        <label v-if="data[indextr].ESTADO == 'PENDIENTE'" style="height: 30px;background-color:#B2BABB;width:150px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].ESTADO_RECEPCION}} </label>
                                        <label v-else style=" height: 30px;background-color:green;width:150px;display: inline-table;color:white;text-align: center;  border-radius: 1%;"> {{data[indextr].ESTADO_RECEPCION}} </label>
                                    </vs-td2>
            
                                    <vs-td2 align="right" noTooltip>
                                        <!----BOTÓN RECEPCIONAR --->
                                        <vs-button v-if="data[indextr].ESTADO == 'N'" color="success" icon-pack="fas" icon="fa-arrow-right" @click="Estado_RecepionOrden=true;Consultar_OrdenDet(data[indextr]);LimpiarCampos(); " class="mr-1" style="display:inline-block">
                                        </vs-button>
                                        <vs-button v-else color="#B2BABB" icon-pack="fas" icon="fa-arrow-right" class="mr-1" style="display:inline-block">
                                        </vs-button>
                                    </vs-td2>
                                    <vs-td2 align="right" noTooltip>
                                        <!----BOTÓN REVERSAR --->
                                        <vs-button v-if="data[indextr].ESTADO == 'S' && permiso_reversion" color="danger" icon-pack="fas" icon="fa-rectangle-xmark" @click="Reversar_Movimiento(data[indextr])" class="mr-1" style="display:inline-block">
                                        </vs-button>
                                        <vs-button v-else color="#B2BABB" icon-pack="fas" icon="fa-rectangle-xmark" class="mr-1" style="display:inline-block">
                                        </vs-button>
                                    </vs-td2>
                                    <vs-td2 align="right" noTooltip>
                                        <!----BOTÓN PDF------>
                                        <vs-button v-if="data[indextr].ESTADO == 'S'" color="success" icon-pack="fas" icon="fa-download" class="mr-1" style="display:inline-block" @click="finalizaMovimiento=false;Consultar_Movimiento(data[indextr])" >
                                        </vs-button>
                                        <vs-button v-else color="#B2BABB" icon-pack="fas" icon="fa-download" class="mr-1" style="display:inline-block">
                                        </vs-button>
                                    </vs-td2>
                                    <vs-td2 align="right" noTooltip>
                                        <!--BOTÓN CONTRASEÑA -->
                                        <vs-button v-if="data[indextr].ESTADO == 'S'" color="warning" icon-pack="fas" icon="fa-file-invoice" @click="finalizaMovimiento=false;Genera_Contrasena(data[indextr])" class="mr-1" style="display:inline-block" >
                                        </vs-button>
                                        <vs-button v-else color="#B2BABB" icon-pack="fas" icon="fa-file-invoice" class="mr-1" style="display:inline-block">
                                        </vs-button>
                                    </vs-td2>
                                    <vs-td2 v-if="data[indextr].CANDADO == 'S'" align="right" noTooltip>
                                        <!--BLOQUEO CONTA -->
                                        <vs-button v-if="data[indextr].ESTADO == 'S'" color="primary" icon-pack="fas" icon="fa-lock" @click="permisoContabilidad='N';PermisoConta(data[indextr])" class="mr-1" style="display:inline-block" >
                                        </vs-button>
                                        <vs-button v-else color="#B2BABB" icon-pack="fas" icon="fa-lock" class="mr-1" style="display:inline-block">
                                        </vs-button>
                                    </vs-td2>
                                    <vs-td2 v-else align="right" noTooltip>
                                        <!--DESBLOQUEO CONTA -->
                                        <vs-button v-if="data[indextr].ESTADO == 'S'" color="primary" icon-pack="fas" icon="fa-lock-open" @click="permisoContabilidad='S';PermisoConta(data[indextr])" class="mr-1" style="display:inline-block" >
                                        </vs-button>
                                        <vs-button v-else color="#B2BABB" icon-pack="fas" icon="fa-lock-open" class="mr-1" style="display:inline-block">
                                        </vs-button>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                </vx-card>
            </div>
    
            <!----------------------- RECEPCIONAR DETALLE ---------->
            <vs-popup classContent="popup-example" :title="titulo" :active.sync="Estado_RecepionOrden">
                <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
    
                    <table width="100%">
                        <tr>
    
                            <td width="50%" v-if="isNumeroOrden">
                                <b> <small>Orden Compra Nº. </small></b>

                                <b style="font-size:2vw">{{ Seleccionada_OrdenEnc.IDORDENCOMPRAENC}}</b>
                                <br>
                                <br>
                                <label class="typo__label">Fecha</label>
                                <br>
                                {{Seleccionada_OrdenEnc.FECHA_CREACION}}
                                <br>
                                <br>
                                <label class="typo__label">PROVEEDOR</label>
                                <br>
                                {{Seleccionada_OrdenEnc.PROVEEDOR}}
                                <br><br><br><br><br><br><br><br><br>

                            </td>
                            <td width="50%" v-else>
                                <vs-row vs-w="12">
                                    <div v-show="isNumeroOrden">
                                        <ValidationProvider name="ingresoNuevo" rules="" vid="ingresoNuevo" >
                                            <vs-input v-model="ingresoNuevo"></vs-input>
                                        </ValidationProvider>
                                    </div>
                                    
                                    <label class="typo__label">Fecha</label>
                                    <br>
                                    {{fechaNuevo}}
                                    <br>
                                        
                                </vs-row>
                                <br>
                                <vs-row vs-w="12">
                                    <vs-col  vs-w="10">
                                        <div class="div-input">
                                            <label class="typo__label">Proveedor</label>
                                        </div>
                                        <div class="flex flex-wrap-nowrap">                                                            
                                            <multiselect v-model="cbProveedor" :options="listaProveedores" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="seleccion_proveedor" placeholder="Búsqueda" @input="onChangeProveedor">
                                                <span slot="noOptions">Lista no disponible.</span>
                                            </multiselect>
                                        </div>
                                    </vs-col>
                                </vs-row>
                                <br><br><br><br><br>
                                <!-- <vs-row vs-type="flex" vs-w="12">
                                    <vs-col vs-w="12">
                                        {{NombreProveedor}}
                                    </vs-col>
                                </vs-row> -->
                                <!-- <vs-row vs-w="12">
                                    <vs-col vs-w="6">
                                        <br>
                                        <label class="typo__label">E-Face</label>
                                        <br>
                                        <vs-input v-model="FacturaFace" class="w-full"/>
                                    </vs-col>
                                </vs-row> -->
                                <br><br><br><br>
                            </td>
                            <td>
                                <b> <small>Ubicación Destino:</small></b>
                                <br>
                                <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                                    <label class="typo__label">Bodegas</label>
                                    <multiselect v-model="cb_bodegas" :options="Lista_bodegas" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Seleccionada_OrdenEnc.Consignacion == 'S' && Seleccionada_OrdenEnc.orden_ortopedia == 'N' ? BodegaSeleccionadaConsignacion : Seleccionada_OrdenEnc.Consignacion == 'S' && Seleccionada_OrdenEnc.orden_ortopedia == 'S' ? BodegaConsignacionOrtopedia : Bodegas_seleccionado" placeholder="Seleccionar bodega" @input="Seleccionada_OrdenEnc.Consignacion == 'S' && Seleccionada_OrdenEnc.orden_ortopedia == 'N' ? onChangeBodegaConsignacion() : Seleccionada_OrdenEnc.Consignacion == 'S' && Seleccionada_OrdenEnc.orden_ortopedia == 'S' ? onChangeBodegaConsignacionOrtopedia() : onChangeBodega()" :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null">
                                        <span slot="noOptions">Lista no disponible.</span>
                                    </multiselect>
                                </ValidationProvider>
                                <br>
                                <b> <small>Datos Facturación:</small></b>
                                <br>
                                <div class="flex flex-wrap">
                                    <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                                        <ValidationProvider name="FactSerie" rules="required|max:10" v-slot="{ errors }" class="required">
                                            <vs-input label="Serie" type="text" class="w-full" v-model="Fact_Serie" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                        </ValidationProvider>
                                    </div>
                                    <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
                                        <ValidationProvider name="FactNumero" rules="required|numero_min:0|numero_entero|max:19" v-slot="{ errors }" class="required">
                                            <vs-input label="Número" type="number" class="w-full" v-model="Fact_Numero" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                        </ValidationProvider>
                                    </div>
    
                                </div>
                                <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                                    <ValidationProvider name="FactFECHA" rules="required" v-slot="{ errors }" class="required">
                                        <label class="typo__label">Fecha Factura:</label>
                                        <vs-input type="date" v-model="Fact_Fecha" name="date1" @keyup.enter="ValidarFecha(Fact_Fecha)" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                    </ValidationProvider>
                                </div>
                                <br>
                                <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                                    <label class="typo__label">ExentoIva:</label>
                                    <vs-switch style="margin-top:10px" v-model="ExcentoIva" />
                                </div>
                            </td>
                        </tr>
                    </table>
                    <br>
                    <table v-if="isCajaChica" width="100%">
                        <vx-card title="Asignado">
                            <!-- <vs-row>
                                <vs-col vs-w="12">
                                    <label class="typo__label">Motivo</label>
                                    <vs-input v-model="MotivoCajaChica" class="w-full"></vs-input>
                                </vs-col>
                            </vs-row> -->
                            <vs-row vs-type="flex" vs-w="12">
                                <vs-col style="padding-right: 5px;" vs-w="6">
                                    <label class="typo__label">Caja</label>
                                    <multiselect  v-model="Cb_CajaChica" :options="Lista_CajaChica" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="CajaChica_seleccionado" placeholder="Seleccionar" @input="onChangeCajaChica">
                                        <span slot="noOptions">Lista no disponible.</span>
                                    </multiselect>
                                </vs-col>
                                <vs-col vs-w="6">
                                    <label class="typo__label">Lote</label>
                                    <multiselect v-model="Cb_LoteCajaChica" :options="Lista_LoteCajaChica" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="LoteCajaChica_seleccionado" placeholder="Seleccionar" @input="onChangeLoteCajaChica">
                                        <span slot="noOptions">Lista no disponible.</span>
                                    </multiselect>
                                </vs-col>
                            </vs-row>
                            <vs-row vs-type="flex" vs-w="12">
                                <vs-col style="padding-top: 25px;" vs-w="4">
                                    <vs-button style="float:left;" color="success" type="filled" icon-pack="fas" icon="fa-plus-circle" @click="Nuevo_Lote()">Nuevo Lote</vs-button>
                                </vs-col>
                                <vs-col style="padding-top: 25px;" vs-w="4">
                                    <vs-button style="float:left;" color="primary" type="filled" icon-pack="fas" icon="fa-print" @click="Genera_Lote()">Imprimir Lote</vs-button>
                                </vs-col>
                            </vs-row>
                        </vx-card>
                    </table>
                    <vx-card v-if="isCajaChica" title="Producto">
                        <vs-row vs-type="flex" vs-w="12">
                            <vs-col vs-w="2">
                                <label class="typo__label"> Producto</label>
                                <vs-input type="text" class="w-full" placeholder="Buscar" v-model="producto_buscar" @keyup.enter="cargar_producto" @change="cargar_producto"/>
                            </vs-col>
                            <vs-col  vs-w="2">
                                <label class="typo__label"> Cantidad</label>
                                <vs-input class="w-full" type="number" count="50" v-model="cantidadProducto" />
                            </vs-col>
                            <vs-col  vs-w="3">
                                <label class="typo__label"> Precio Unitario</label>
                                <vs-input id="precio_untario_" type="number" class="w-full" v-model="ingresoPrecioUnitario" @change="ValidarMargenPrecio" disabled/>
                            </vs-col>
                            <vs-col  vs-w="3">
                                <label class="typo__label">SubTotal</label>
                                <vs-input id="precio_untario_" type="number" class="w-full" v-model="IngresoSubTotalProducto" @change="ValidarMargenPrecio"/>
                            </vs-col>
                            <vs-col  vs-w="2">
                                <label class="typo__label"> Bonificación</label>
                                <vs-input id="precio_untario_" type="number" class="w-full" v-model="cantidadBonificacion" />
                            </vs-col>
                        </vs-row>
                        <br>
                        <vs-row vs-type="flex" vs-w="12">
                            <vs-col>
                                <div v-if="productoSeleccionado.Codigo > 0 " class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                                    <!-- DESCUENTO-->
                                    <div style="border-radius:5px;padding:5px;font-size:15px;background-color:#09af00;color:white">
                                        {{productoSeleccionado.Codigo}}
                                        <br>
                                        {{productoSeleccionado.marca_comercial}}
                                        <br>
                                        {{productoSeleccionado.Presentacion}}
                                        <br>

                                    </div>
                                </div>
                            </vs-col>
                        </vs-row>
                        <br>
                        <vs-row vs-type="flex" vs-w="12">
                            <vs-col>
                                <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7">
                                    <vs-button color="primary" style="float:left" type="filled" icon-pack="fas" icon="fa-plus" @click="Agregar_Detalle()"> Agregar</vs-button>
                                </div>
                            </vs-col>
                        </vs-row>
                    </vx-card>

                    <vs-divider> </vs-divider>

                    <vs-table2 v-show="isCajaChica" max-items="10" tooltip pagination :data="detalleProductosManual">
                        <template slot="thead">
                            <th order="producto" width="20px">Codigo Producto</th>
                            <th order="descripcion" width="100px">Producto</th>
                            <th order="cantidad" width="20px">Cantidad</th>
                            <th order="precio_unitario" width="20px">Precio Unitario</th>
                            <th order="bonificacion" width="20px">Bonificación</th>
                            <th order="subtotal" width="20px">Precio Total</th>
                            <th width="20px">Eliminar</th>
                        </template>
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 noTooltip>
                                    {{ tr.producto }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.descripcion.toUpperCase() }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ tr.cantidad }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ tr.precio_unitario }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ tr.bonificacion}}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ tr.subtotal}}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="EliminarProducto(indextr, data[indextr])"></vs-button>
                                </vs-td2>
    
                            </tr>
                        </template>
                    </vs-table2>
    
                    <vs-table2 v-show="isNumeroOrden" max-items="10" tooltip pagination :data="Lista_DetalleOrden">
                        <template slot="thead">
                            <th order="IDPRODUCTOFK" width="20px">Codigo Producto</th>
                            <th order="Descripcion" width="20px">Producto</th>
                            <th order="CANTIDAD" width="20px">Cant. Solicitada</th>
                            <th order="PRECIO_UNITARIO" width="20px">Precio Unitario</th>
                            <th order="CANTIDAD_RECEPCIONADA" width="20px">Cant. Recepcionada</th>
                            <th order="SUB_TOTAL" width="20px">Precio Total</th>
                            <th order="COSTO_UNITARIO" width="20px">Costo Unitario</th>
                            <th order="COSTO_ULTIMO" width="20px">Costo Ultimo</th>
                            <th order="BONIFICACION" width="20px">Bonificacion</th>
                            <th order="ESTADO_RECEPCION" width="20px">Modificar</th>
                            <!-- <th width="20px">Excento IVA</th> -->
                        </template>
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2>
                                    {{ tr.IDPRODUCTOFK }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.Descripcion.toUpperCase() }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.CANTIDAD }}
                                </vs-td2>
                                <vs-td2>
                                    {{ $formato_moneda(tr.PRECIO_UNITARIO) }}
                                </vs-td2>
                                <!-- <vs-td2 v-if="tr.ESTADO_RECEPCION">
                                    {{ $formato_moneda(tr.PRECIO_UNITARIO) }}
                                </vs-td2>
                                <vs-td2 v-else noTooltip>
                                    <vs-input v-on:blur="Actualizar_OrdenDet(tr)" v-model="tr.PRECIO_UNITARIO" class="w-full" />
                                </vs-td2> -->
                                <vs-td2 v-if="tr.ESTADO_RECEPCION" noTooltip>
                                    <vs-input disabled v-model="tr.CANTIDAD_RECEPCIONADA" class="w-full" />
                                </vs-td2>
                                <vs-td2 v-else noTooltip>
                                    <vs-input v-on:keyup.enter="Actualizar_OrdenDet(tr)" v-model="tr.CANTIDAD_RECEPCIONADA" class="w-full" />
                                </vs-td2>
                                <vs-td2 v-if="tr.ESTADO_RECEPCION">
                                    {{ $formato_moneda(tr.SUB_TOTAL) }}
                                </vs-td2>
                                <vs-td2 v-else noTooltip>
                                    <vs-input v-model="tr.SUB_TOTAL" class="w-full" @change="onChangeSubTotal(tr)" />
                                </vs-td2>
                                <vs-td2>
                                    {{ $formato_moneda(tr.COSTO_UNITARIO) }}
                                </vs-td2>
                                <vs-td2>
                                    {{ $formato_moneda(tr.COSTO_ULTIMO) }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    <vs-input v-on:keyup.enter="Actualizar_OrdenDet(tr)" v-model="tr.BONIFICACION" class="w-full" />
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    <vs-switch style="margin-top:10px;margin-left:20px" :key="tr.ESTADO_RECEPCION" v-model="tr.ESTADO_RECEPCION" @input="Actualizar_OrdenDet(tr)" />
                                </vs-td2>
                                <!-- <vs-td2 noTooltip>
                                    <vs-switch style="margin-top:10px;margin-left:20px" :key="tr.EXENTO_IVA" v-model="tr.EXENTO_IVA" @input="Guardar_ExentoIVA(tr);Actualizar_OrdenDet(tr)" />
                                </vs-td2> -->
    
                            </tr>
                        </template>
                    </vs-table2>
                    <div class="flex flex-wrap w-full">
                        <label class="typo__label divAlign"> Total: </label>
                        <label class="divAlign"> {{ $formato_moneda(granTotal) }} </label>
                    </div>
                    <br>
                    <br>
                    <vs-divider></vs-divider>
                    <div class="flex flex-wrap">
                        
                        <div class="w-full md:w-full lg:w-full xl:w-full">
                            <ValidationProvider name="Observaciones" rules="required_if:ingresoNuevo,0" v-slot="{ errors }" class="required">
                                <vs-input label="Observaciones" type="text" class="w-full" v-model="Observaciones" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"/>
                            </ValidationProvider>
                        </div>
                        
                    </div>
                    <vs-divider></vs-divider>
                    <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" id="btn_confirmacion" @click="finalizaMovimiento=true;Confirmacion_Transaccion()"> Finalizar Recepción</vs-button>
                    <vs-divider></vs-divider>
                </div>
            </vs-popup>
            <!----------------------- FIN Cotizaciones Externas---------->
            <!-- PopUp productos -->
            <vs-popup classContent="popup-example" title="Resultado Búsqueda" :active.sync="estadoVentanaEmergenteBusqueda" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
                <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">

                    <form>

                        <vs-divider></vs-divider>
                        <vs-table2 max-items="10" pagination :data="producto" tooltip id="tb_departamentos">

                            <template slot="thead">
                                <th>Codigo</th>
                                <th>Descripcion</th>
                                <!-- <th>Concentracion</th>
                                <th>Codigo Interno</th> -->
                                <th>Presentación</th>
                                <th></th>
                            </template>

                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2 :data="data[indextr].Codigo">
                                        {{data[indextr].Codigo}}
                                    </vs-td2>

                                    <vs-td2 :data="data[indextr].Descripcion">
                                        {{data[indextr].Descripcion}}
                                    </vs-td2>

                                    <!-- <vs-td2 :data="data[indextr].Concentracion">
                                        {{data[indextr].Concentracion}}
                                    </vs-td2>

                                    <vs-td2 :data="data[indextr].codigo_interno">
                                        {{data[indextr].codigo_interno}}
                                    </vs-td2> -->

                                    <vs-td2 :data="data[indextr].PresentacionNombre">
                                        {{data[indextr].PresentacionNombre}}
                                    </vs-td2>

                                    <vs-td2 align="right">
                                        <vs-button color="primary" icon-pack="fas" icon="fa-check-circle" style="display:inline-block;margin-right:2px" @click="Seleccionar_Producto(data[indextr])"></vs-button>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </form>

                </div>
            </vs-popup>
            <vs-popup classContent="popup-example" title="Impresión" :active.sync="isReportContrasena" >
                <div class="flex-wrap-nowrap" width="50%" style="height: 600px">
                    <object v-if="archivoMovimiento!=''" type="application/pdf" :data="archivoMovimiento" ref="pdfDocument" width="100%" height="98%">
                        <p>Su navegador es incompatible con el plugin pdf.</p>
                    </object>
                </div>
                <div class="flex-wrap-nowrap" width="50%" style="height: 600px">
                    <object v-if="archivoContrasena!=''" type="application/pdf" :data="archivoContrasena" ref="pdfDocument" width="100%" height="98%">
                        <p>Su navegador es incompatible con el plugin pdf.</p>
                    </object>
                </div>
            </vs-popup>
        </div>
    
    </vx-card>
    </template>
    
    <script>
    /**
     * @General
     * Modulo registra las ordenes de compra de Departamento/Bodega
     */
    //import {generateAndDownloadBarcodeInPDF} from './generateBarcode'; 
    // import {
    //     jsPDF
    // } from 'jspdf';
    // import 'jspdf-autotable'
    
    import Multiselect from 'vue-multiselect'
    
    import "vue-multiselect/dist/vue-multiselect.min.css"
    //import Timeline from 'timeline-vuejs'
    export default {
    
        components: {
            Multiselect,
            //Timeline,
        },
        data() {
            return {
                titulo: 'Ingreso de Bodega',
                Estado_RecepionOrden: false,
                Lista_DetalleOrden: [],
                Seleccionada_OrdenEnc: [],
                Lista_OrdenesEnc: [],
    
                cb_lista_operacion: '',
                lista_estado: [{
                        ID: 'N',
                        DESCRIPCION: 'Pendiente'
                    },
                    {
                        ID: 'S',
                        DESCRIPCION: 'Finalizado'
                    }
                ],
                Id_estado_seleccionado: '',
                fecha_inicio: '',
                fecha_final: '',
                cb_departamentos: '',
                id_departamento_selec: '',
                Lista_departamentos: [],
    
                Lista_bodegas: [],
                id_bodega_seleccionada: '',
                cb_bodegas: '',
                tipoBodega: '',
                Fact_Serie: '',
                Fact_Numero: '',
                Fact_Fecha: '',
                Observaciones: '',
    
                Lista_Movimiento: [],
                Lista_Movimiento_det: [],
                ExcentoIva: false,
                listado_reportes: [],
                listado_reportes_contrasena: [],
                listado_reportes_lote: [],
                ParametrosReporte: [],
                listado_source: {
                    CODIGO_MOVIMIENTO: '',
                    DOCUMENTO: '',
                    IDPROVEEDORFK: '',
                    CajaChica: '',
                    Lote: ''
                },
                isNumeroOrden: false,
                fechaNuevo: '',
                ingresoNuevo: '0',
                granTotal: 0,
                cbProveedor: '',
                listaProveedores: [],
                proveedor:{
                    codigo: '',
                    nombre: '',
                    nit: ''
                },
                isCajaChica: false,
                MotivoCajaChica: '',
                Cb_CajaChica: '',
                Lista_CajaChica: [],
                CodigoCajaChica: '',
                Cb_LoteCajaChica: '',
                Lista_LoteCajaChica: [],
                CodigoLoteCajaChica: '',
                estadoVentanaEmergenteBusqueda: false,
                productoSeleccionado: {
                    nombre: '',
                    marca_comercial: '',
                    Principio_activo: '',
                    Concentracion: '',
                    Presentacion: '',
                    Codigo: '',
                    codigo_interno: '',
                    Activo: 'N',
                    bonificacion: ''
                },
                producto: [],
                producto_buscar: '',
                cantidadProducto: '',
                ingresoPrecioUnitario: '',
                IngresoSubTotalProducto: '',
                cantidadBonificacion: '',
                detalleProductosManual: [{
                    producto: '',
                    cantidad: 0,
                    precio_unitario: 0,
                    subtotal: 0,
                    descripcion: '',
                    bonificacion: 0,
                    unidad_medida: ''
                }],
                reporteProps: [],
                datosContrasena: {
                    DOCUMENTO: '',
                    IDPROVEEDORFK: ''
                },
                datosMovimiento: {
                    CODIGO_MOVIMIENTO: '',
                    ExcentoIva: ''
                },
                datosLote: {
                    CajaChica: '',
                    Lote: ''
                },
                finalizaMovimiento: false,
                isReportContrasena: false,
                archivoContrasena: '',
                archivoMovimiento: '',
                permisos_tipos_bodegas: [],
                permiso_bodega_vencido:'',
                permisoContabilidad: '',
                isProductoCorrecto: false,
                permiso_reversion: false,
                IsSubTotalChange: false,
                IsPrecioUnitarioChange: false,
                IsMargenPermitido: false,
                PermisosAgrupacionBodega: ''
            };
        },
        mounted() {
            this.Id_estado_seleccionado = 'N';
            this.cb_lista_operacion = {
                ID: 'N',
                DESCRIPCION: 'Pendiente'
            }
            this.permiso_reversion = this.$validar_privilegio('REVERSION').status

            for(let privilegio of this.$store.state.privilegios){
               if(privilegio.Privilegio.includes("TIPO_BODEGA")){                      
                   let tipobodega = privilegio.Privilegio.split('_')[2];
                   if(tipobodega == 'T'){
                     this.permiso_bodega_vencido = 'S'
                   }else{
                     this.permisos_tipos_bodegas.push(privilegio.Privilegio.split('_')[2])                    
                   }                                                       
                }
                if(privilegio.Privilegio.includes("CODIGO_AGRUPACION")){
                    if(this.PermisosAgrupacionBodega != ''){
                        this.PermisosAgrupacionBodega += `,${privilegio.Privilegio.split('_')[2]}`
                    }else{
                        this.PermisosAgrupacionBodega = privilegio.Privilegio.split('_')[2]
                    }
                }
            }
            this.Consultar_Bodega('F',this.permisos_tipos_bodegas.join(","),this.permiso_bodega_vencido);

    
            this.Consultar_OrdenEnc(); //Carga  la tabla inicial
        },
        async beforeCreate() {
            this.ParametrosReporte = await this.$recupera_parametros_reporte('Lista de Precios')
            this.listado_reportes = await this.$recupera_parametros_reporte('Detalle Movimiento')
            this.listado_reportes_contrasena = await this.$recupera_parametros_reporte('Genera Contraseña Inventario')
            this.listado_reportes_lote = await this.$recupera_parametros_reporte('Detalle Lote')
        },
        methods: {
            isEmptyObject(obj){
                return Object.keys(obj).length === 0;
            },
            Operacion_seleccionada({
                DESCRIPCION
            }) {
                return `${DESCRIPCION} `
            },
            onChangeEstado(value) {
                if (value !== null && value.length !== 0) {
                    this.Id_estado_seleccionado = value.ID;
                } else {
                    this.Id_estado_seleccionado = '';
                }
            },
            Bodegas_seleccionado({
                CODIGO,
                NOMBRE
            }) {
                return `${CODIGO} - ${NOMBRE} `;
            },
            onChangeBodega() {
                if (this.cb_bodegas !== null && this.cb_bodegas.length !== 0) {
                    this.id_bodega_seleccionada = this.cb_bodegas.CODIGO
                    this.tipoBodega = this.cb_bodegas.TipoBodega
    
                } else {
                    this.id_bodega_seleccionada = ''
                    this.tipoBodega = ''
                }
            },
            BodegaSeleccionadaConsignacion({
                CodigoBodega,
                NombreBodega
            }){
                return `${CodigoBodega} - ${NombreBodega} `;
            },
            BodegaConsignacionOrtopedia({
                CodigoBodega,
                NombreBodega
            }){
                return `${CodigoBodega} - ${NombreBodega} `;
            },
            onChangeBodegaConsignacion() {
                
                if (this.cb_bodegas !== null && this.cb_bodegas.length !== 0) {
                    this.id_bodega_seleccionada = this.cb_bodegas.CodigoBodega
                    this.tipoBodega = 'G'
    
                } else {
                    this.id_bodega_seleccionada = ''
                    this.tipoBodega = ''
                }
            },
            onChangeBodegaConsignacionOrtopedia(){
                if (this.cb_bodegas !== null && this.cb_bodegas.length !== 0) {
                    this.id_bodega_seleccionada = this.cb_bodegas.CodigoBodega
                    this.tipoBodega = 'O'
    
                } else {
                    this.id_bodega_seleccionada = ''
                    this.tipoBodega = ''
                }
            },
            ValidarFecha(FechaDitada) {
                 Number(new Date(FechaDitada));
            },
            /*****************************CONSULTAR INFORMACION*************/
            Consultar_OrdenEnc() {
    
                const url = this.$store.state.global.url
                const sesion = this.$store.state.sesion

                this.$vs.loading();
                this.axios.post(url + 'app/v1_OrdenCompra/Recepcion_ConsultaEnc', {
                        Empresa: sesion.sesion_empresa,
                        estado: this.Id_estado_seleccionado,
                        fecha_inicio: this.fecha_inicio,
                        fecha_fin: this.fecha_final
                    })
                    .then(resp => {
                        this.$vs.loading.close();
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_OrdenesEnc = [];
                        } else {
                            
                            this.Lista_OrdenesEnc = resp.data.json
                            
                            
                        }
                    })
                    .catch(() => {
                        this.$vs.loading.close();
                    })
                this.$vs.loading.close();
    
            },
            Consultar_OrdenDet(datos) {
                if((datos.NombreSeguro == null ? false : datos.NombreSeguro.includes('Palig')) && datos.IDPROVEEDORFK.trim() == 'ORTO'){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Alerta',
                        time:8000,
                        text: `De acuerdo con los convenios establecidos entre el proveedor ${ datos.PROVEEDOR.trim() } y el seguro ${ datos.NombreSeguro.trim() }, no es posible continuar con el proceso. `,
                    })
                    this.Estado_RecepionOrden = false
                    return
                }
                this.titulo = 'Ingreso de Bodega'
                if(datos.Consignacion == 'S' && datos.orden_ortopedia == 'N'){
                    this.ConsultaBodegaConsignacionOrdenCompra()
                }else if(datos.Consignacion == 'S' && datos.orden_ortopedia == 'S'){
                    this.ConsultaBodegaConsignacionOrdenCompraOrtopedia()
                }else{
                    this.Consultar_Bodega('F',this.permisos_tipos_bodegas.join(","),this.permiso_bodega_vencido);
                }
                
                this.ingresoNuevo = datos.IDORDENCOMPRAENC
                this.isNumeroOrden = true
                this.isCajaChica = false
                this.Seleccionada_OrdenEnc = datos
                this.granTotal = 0
                this.Detalle_Orden(datos.IDORDENCOMPRAENC)
    
            },
            Detalle_Orden(datos){
                const url = this.$store.state.global.url;
                this.axios.post(url + 'app/v1_OrdenCompra/Recepcion_ConsultaDet', {
                    IdOrdenCompraEnc: datos,
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.Lista_DetalleOrden = [];
                    } else {
                        this.Lista_DetalleOrden = resp.data.json.map(m => {
                            return{
                                ...m,
                                ESTADO_RECEPCION: 'S',
                                PRECIO_UNITARIO: parseFloat(m.SUB_TOTAL) / parseFloat(m.CANTIDAD_RECEPCIONADA),//m.PRECIO_UNITARIO,
                                SUB_TOTAL_BACK: m.SUB_TOTAL,
                                CANTIDAD: parseInt(m.CANTIDAD),
                                CANTIDAD_RECEPCIONADA: parseInt(m.CANTIDAD_RECEPCIONADA),
                                PRECIO_UNITARIO_BACK: parseFloat(m.PRECIO_UNITARIO_BACK).toFixed(2),
                                COSTO_UNITARIO: parseInt(m.CANTIDAD) > 0 && parseInt(m.FACTOR) ? ((1000 * (m.PRECIO_UNITARIO / (1 + (m.POCENTAJE_IMPUESTO / 100)))).toFixed(2) / 1000) * m.FACTOR : ((1000 * (m.PRECIO_UNITARIO / (1 + (m.POCENTAJE_IMPUESTO / 100)))).toFixed(2) / 1000)
                            }
                        })
                        this.granTotal = 0
                        this.Lista_DetalleOrden.forEach(valor => {
                            this.granTotal = this.granTotal + parseFloat(valor.SUB_TOTAL)
                        })
                    }
                })
                .catch(() => {})
            },
    
            Consultar_solicitud_detalle(datos) {
    
                const url = this.$store.state.global.url
    
                this.$vs.loading();
                this.axios.post(url + 'app/v1_OrdenCompra/consultar_solicitud_compra_det', {
                        id_empresa: datos.EMPRESA,
                        id_solicitud_enc: datos.CODIGO,
                        tipo_transaccion: 'T'
                    })
                    .then(resp => {
                        this.$vs.loading.close();
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_detalle_solicitud = [];
                        } else {
                            
                            this.Lista_detalle_solicitud = resp.data.json;
                        }
                    })
                    .catch(() => {
                        this.$vs.loading.close();
                    })
                this.$vs.loading.close();
    
            },
            Consultar_Departamentos() {
                this.cb_departamentos = null;
                this.id_departamento_selec = "";
    
                const sesion = this.$store.state.sesion;
                const url = this.$store.state.global.url;
                this.axios
                    .post(url + "app/v1_OrdenCompra/consulta_departamento", {
                        id_empresa: sesion.sesion_empresa,
                        tipo_consulta: "",
                    })
                    .then((resp) => {
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: "#B71C1C",
                                title: "Alerta",
                                text: resp.data.mensaje,
                            });
                            this.Lista_departamentos = [];
                        } else {
                            this.Lista_departamentos = resp.data.json;
    
                        }
                    })
                    .catch(() => {});
            },
            Consultar_Bodega(Operacion, BodegasDespacho, BodegaTransito) {
                const url = this.$store.state.global.url
                this.axios.post(url+'app/v1_OrdenCompra/consulta_bodegaEmpReal', {
                    operacion: Operacion,
                    BodegasDepacho: BodegasDespacho,
                    BodegaTransito: BodegaTransito
                })
                .then(resp => {

                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Bodegas',
                            text: resp.data.mensaje,
                        })
                    } else {
                        this.Lista_bodegas = resp.data.json;                                              
                    }
                })
                .catch(() => { })
        
            },
            ConsultaBodegaConsignacionOrdenCompra(){
                const url = this.$store.state.global.url
                this.axios.post(url+'app/v1_OrdenCompra/ConsultaBodegaConsignacionOrdenCompra', {
                })
                .then(resp => {

                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Bodegas',
                            text: resp.data.mensaje,
                        })
                    } else {
                        this.Lista_bodegas = resp.data.json;                                              
                    }
                })
                .catch(() => { })
            },
            ConsultaBodegaConsignacionOrdenCompraOrtopedia(){
                const url = this.$store.state.global.url
                this.axios.post(url+'app/v1_OrdenCompra/ConsultaBodegaConsignacionOrtopedia', {
                    CodigoAgrupacion: this.PermisosAgrupacionBodega
                })
                .then(resp => {

                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Bodegas',
                            text: resp.data.mensaje,
                        })
                    } else {
                        this.Lista_bodegas = resp.data.json;                                              
                    }
                })
                .catch(() => { })
            },
            async Genera_Contrasena(datos){
                if(this.finalizaMovimiento){
                    this.listado_source.DOCUMENTO = datos.DOCUMENTO
                    this.listado_source.IDPROVEEDORFK = datos.IDPROVEEDORFK
                    let _opciones = {}
                    _opciones = this.$prepara_valores_reporte({
                        Nombre: 'Genera Contraseña Inventario',
                        Data_source: this.listado_source,
                        Data_report: this.listado_reportes_contrasena
                    })
                    this.axios.post("/app/reporte/ReporteGenerador", {
                        Nombre: 'Genera Contraseña Inventario',
                        Opciones: {
                            tiporeporte: "application/pdf",
                            ..._opciones
                        }
                    }, {
                        responseType: 'arraybuffer'
                    })
                    .then(resp => {
                        this.isReportContrasena = true
                        this.archivoContrasena = 'data:application/pdf;base64,' + Buffer.from(resp.data, 'binary').toString('base64');
                    })
                }else{
                    this.listado_source.DOCUMENTO = datos.DOCUMENTO
                    this.listado_source.IDPROVEEDORFK = datos.IDPROVEEDORFK

                    this.$genera_reporte({
                        Nombre: "Genera Contraseña Inventario",
                        Data_source: this.listado_source,
                        Data_report: this.listado_reportes_contrasena
                    }).catch(() => {
                    })
                }
                
            },
            async Consultar_Movimiento(datos) {
                if(this.finalizaMovimiento){
                    this.listado_source.CODIGO_MOVIMIENTO = datos.CODIGO_MOVIMIENTO;
                    this.listado_source.ExcentoIva = datos.ExcentoIva;
                    let _opciones = {}
                    _opciones = this.$prepara_valores_reporte({
                        Nombre: 'Detalle Movimiento',
                        Data_source: this.listado_source,
                        Data_report: this.listado_reportes
                    })
                    this.axios.post("/app/reporte/ReporteGenerador", {
                        Nombre: 'Detalle Movimiento',
                        Opciones: {
                            tiporeporte: "application/pdf",
                            ..._opciones
                        }
                    }, {
                        responseType: 'arraybuffer'
                    })
                    .then(resp => {
                        this.archivoMovimiento = 'data:application/pdf;base64,' + Buffer.from(resp.data, 'binary').toString('base64');
                    })
                }else{
                    this.listado_source.CODIGO_MOVIMIENTO = datos.CODIGO_MOVIMIENTO
                    this.listado_source.ExcentoIva = datos.ExcentoIva
                    this.$genera_reporte({
                        Nombre: "Detalle Movimiento",
                        Data_source: this.listado_source,
                        Data_report: this.listado_reportes
                    }).catch(() => {
                    })
                }
            },
            async Genera_Lote(){
                
                if(this.CodigoCajaChica != '' && this.CodigoLoteCajaChica != ''){
                    this.listado_source.CajaChica = this.CodigoCajaChica
                    this.listado_source.Lote = this.CodigoLoteCajaChica
                    this.$genera_reporte({
                        Nombre: "Detalle Lote",
                        Data_source: this.listado_source,
                        Data_report: this.listado_reportes_lote
                    }).catch(() => {
                    })
                }else{
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Lote',
                        text: 'Debe de seleccionar una caja chica y un lote...',
                    })
                }
                
            },
            async Consultar_Movimiento_det(OrdenEnc) {
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/Rep_MovimientosDet', {
                        IdOrdenCompraEnc: OrdenEnc.IDORDENCOMPRAENC
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Bodegas',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_Movimiento_det = [];
                        } else {
                            this.Lista_Movimiento_det = resp.data.json;
    
                        }
                    })
                    .catch(() => {
    
                    })
    
            },
            async Reversar_Movimiento(value){
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Reversión de Movimiento',
                    text: `¿Está seguro que desea reversar el movimiento ${value.CODIGO_MOVIMIENTO}?`,
                    acceptText: 'Aceptar',
                    accept: () => {
                        
                        this.axios.post('/app/v1_OrdenCompra/ReversionIngresoInventario', {
                            Proveedor: value.IDPROVEEDORFK,
                            Documento: value.DOCUMENTO
                        })
                        .then(resp => {
                            if(resp.data.codigo == 0){
                                this.$vs.notify({
                                    color: '#success',
                                    title: 'Inventario',
                                    text: resp.data.descripcion,
                                    position:'top-center'
                                })
                                this.Consultar_OrdenEnc();
                            }
                        })
                    }
                })

            },
            /***********************************ACTUALIZACIONES********************* */
    
            Actualizar_OrdenDet(datos) {
                if(datos.CANTIDAD_RECEPCIONADA > datos.CANTIDAD){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Bodegas',
                        text: 'La cantidad recepcionada no puede ser mayor a la cantidad solicitada',
                        position:'top-center'
                    })
                    datos.CANTIDAD_RECEPCIONADA = datos.CANTIDAD
                }else{
                    // var finalizado = 'N';
                    // if (datos.ESTADO_RECEPCION == true) {
                    //     finalizado = 'S';
                    // } else {
                    //     finalizado = 'N';
                    // }
                    
                    if((datos.CANTIDAD_RECEPCIONADA != datos.CANTIDAD) || (datos.PRECIO_UNITARIO_BACK != datos.PRECIO_UNITARIO)){ //Corregir aqui
                        datos.SUB_TOTAL = parseFloat(datos.CANTIDAD_RECEPCIONADA) * parseFloat(datos.PRECIO_UNITARIO)
                        datos.PRECIO_UNITARIO_BACK = datos.PRECIO_UNITARIO
                        this.granTotal = 0
                        this.Lista_DetalleOrden.forEach(valor => {
                            this.granTotal = this.granTotal + parseFloat(valor.SUB_TOTAL)
                        })
                    }
                    
                    
                    // const sesion = this.$store.state.sesion;
        
                    // this.axios.post('/app/v1_OrdenCompra/RecepcionarOrden_detalle', {
                    //         corporativo: sesion.corporativo,
                    //         cantidad: datos.CANTIDAD_RECEPCIONADA,
                    //         IdDetalle: datos.IDORDENDET,
                    //         finalizado: finalizado,
                    //         exento_iva: datos.EXENTO_IVA ? 1 : 0,
                    //         Bonificacion: datos.BONIFICACION,
                    //         PrecioUnitario: datos.PRECIO_UNITARIO
        
                    //     }).then(() => {
                    //         if (datos.ESTADO_RECEPCION == true) {
                    //             this.Detalle_Orden(datos.IDORDERENC)
                    //         }
                    //     })
                }
    
            },
            onChangeSubTotal(datos){
                let diferenciaPrecio = 0
                if(datos.CANTIDAD_RECEPCIONADA > datos.CANTIDAD){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Bodegas',
                        text: 'La cantidad recepcionada no puede ser mayor a la cantidad solicitada',
                        position:'top-center'
                    })
                    datos.CANTIDAD_RECEPCIONADA = datos.CANTIDAD
                }else{
                    if((parseFloat(datos.SUB_TOTAL) / parseFloat(datos.CANTIDAD_RECEPCIONADA)) < parseFloat(datos.PRECIO_UNITARIO_BACK)){
                        diferenciaPrecio = parseFloat(parseFloat(datos.PRECIO_UNITARIO_BACK) - parseFloat(datos.SUB_TOTAL) / parseFloat(datos.CANTIDAD_RECEPCIONADA)).toFixed(2)
                    }else{
                        diferenciaPrecio = parseFloat(parseFloat(datos.SUB_TOTAL) / parseFloat(datos.CANTIDAD_RECEPCIONADA) - parseFloat(datos.PRECIO_UNITARIO_BACK)).toFixed(2)
                    }
                    if(diferenciaPrecio > 0.99){
                        datos.PRECIO_UNITARIO = datos.PRECIO_UNITARIO_BACK
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Ordenes de Compra',
                            text: 'Supera el margen permitido',
                        })
                        datos.SUB_TOTAL = datos.SUB_TOTAL_BACK
                    }else{
                        datos.COSTO_UNITARIO = parseInt(datos.CANTIDAD_RECEPCIONADA) > 0 && parseInt(datos.FACTOR) ? ((1000 * (datos.PRECIO_UNITARIO / (1 + (datos.POCENTAJE_IMPUESTO / 100)))).toFixed(2) / 1000) * datos.FACTOR : ((1000 * (datos.PRECIO_UNITARIO / (1 + (datos.POCENTAJE_IMPUESTO / 100)))).toFixed(2) / 1000)
                        datos.PRECIO_UNITARIO = datos.SUB_TOTAL / datos.CANTIDAD_RECEPCIONADA
                        datos.PRECIO_UNITARIO_BACK = datos.SUB_TOTAL / datos.CANTIDAD_RECEPCIONADA

                        this.granTotal = 0
                        this.Lista_DetalleOrden.forEach(valor => {
                            this.granTotal = this.granTotal + parseFloat(valor.SUB_TOTAL)
                        })
                    }
                    
                }
            },
            async Confirmacion_Transaccion() {
                /**
                 * @General
                 * Función Permite   almacenar un nuevo registro;
                 */
                /* VALIDACION DE ARRAY */
                for (var i = 0; i < this.Lista_DetalleOrden.length; i++) {
                    var slug = this.Lista_DetalleOrden[i].ESTADO_RECEPCION;
                    if (slug == false) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Orden de Compra',
                            text: 'Existen producto sin recepcionar.',
                        });
                        return false;
                    }
                }

                let res_exento = ''

                if (this.ExcentoIva)
                    res_exento = 'N'
                else
                    res_exento = 'S'
    
                if (!this.Validacion_Campos('ID', 'Bodega', this.id_bodega_seleccionada, true, 0)) {
                    return;
                }
                if (!this.Validacion_Campos('C', 'Factura - Serie', this.Fact_Serie, true, 10)) {
                    return;
                }
                if (!this.Validacion_Campos('C', 'Factura - Número', this.Fact_Numero, true, 19)) {
                    return;
                }
                if (!this.Validacion_Campos('C', 'Factura - Fecha', this.Fact_Fecha, true, 10)) {
                    return;
                }
                if(this.ingresoNuevo == '0'){
                    if(!this.Validacion_Campos('ID', 'Proveedor', this.proveedor.codigo, true, 0)) {
                        return
                    }
                    if(!this.Validacion_Campos('C', 'Observaciones', this.Observaciones, true, 60)) {
                        return
                    }
                    if(!this.Validacion_Campos('G', 'Productos', this.detalleProductosManual.length, true, 0)){
                        return
                    }
                }

                if(this.ingresoNuevo == '0'){
                    this.axios.post('/app/v1_OrdenCompra/FinalizarIngresoManual', {
                        Bodega: this.id_bodega_seleccionada,
                        Observaciones: this.Observaciones,
                        Fact_Serie: this.Fact_Serie,
                        Fact_numero: this.Fact_Numero,
                        Fact_fecha: this.Fact_Fecha,
                        ExentoIva: res_exento,
                        Proveedor: this.proveedor.codigo,
                        CajaChica: this.CodigoCajaChica,
                        Lote: this.CodigoLoteCajaChica,
                        TotalFact: this.granTotal,
                        datos: JSON.stringify(this.detalleProductosManual)
                    })
                    .then(resp => {
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: resp.data.mensaje,
                            })
    
                        } else {

                            this.axios.post('/app/v1_OrdenCompra/ConteoIncidentes', {
                                Fact_serie: this.Fact_Serie.trim(),
                                Fact_numero: this.Fact_Numero.trim(),
                                Proveedor: this.proveedor.codigo
                            })
                            .then(resp => {
                                if (!this.isEmptyObject(resp.data.json)) {
                                    this.$genera_reporte_envio({
                                        Nombre: 'Lista de Precios',
                                        Data_source: {
                                            Proveedor: this.proveedor.codigo,
                                            Documento: this.Fact_Serie.trim() + '-' + this.Fact_Numero.trim()
                                            // EmpresaInventario: "BOD",
                                            // Estado: "I",
                                            // EnviarA: "<EMAIL>",
                                            // CopiarA: "<EMAIL>",
                                            // Asunto: "Prueba de envio",
                                            // Mensajetitulo: "TITULO",
                                            // MensajeDescripcion: "Prueba",
                                            // MensajeFirma: "Yo firmo",
                                            // NombreDelArchivo: "ReporteListadoPrecios.pdf",
                                            // MostrarResultados: "O"
                                        },
                                        Data_report: this.ParametrosReporte
                                    })
                                }
                            })

                            this.datosContrasena.DOCUMENTO = this.Fact_Serie.trim() + '-' + this.Fact_Numero.trim()
                            this.datosContrasena.IDPROVEEDORFK = this.proveedor.codigo
                            this.datosMovimiento.CODIGO_MOVIMIENTO = resp.data.resultado
                            this.datosMovimiento.ExcentoIva = res_exento
                            this.Estado_RecepionOrden = false;
                            this.Consultar_OrdenEnc();
                            this.Consultar_Movimiento(this.datosMovimiento)
                            this.isReportContrasena = true
                        }
    
                    })
                    .catch(() => {
    
                    })
                }else{
                    this.axios.post('/app/v1_OrdenCompra/RecepcionarOrdenEnc_Finalizar', {
                        
                        Departamento: this.id_departamento_selec,
                        Bodega: this.id_bodega_seleccionada,
                        IdOrdenCompraEnc: this.Seleccionada_OrdenEnc.IDORDENCOMPRAENC,
                        Observaciones: this.Observaciones,
                        Fact_serie: this.Fact_Serie,
                        Fact_numero: this.Fact_Numero,
                        Fact_fecha: this.Fact_Fecha,
                        Proveedor: this.Seleccionada_OrdenEnc.IDPROVEEDORFK,
                        ExentoIva: res_exento,
                        GranTotal: this.granTotal,
                        OrdenOrtopedia: this.Seleccionada_OrdenEnc.orden_ortopedia,
                        datos: JSON.stringify(this.Lista_DetalleOrden),
                        Consignacion: this.Seleccionada_OrdenEnc.Consignacion,
                        Liquidacion: this.Seleccionada_OrdenEnc.Liquidacion
                    })
                    .then(resp => {
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: resp.data.mensaje,
                            })
    
                        } else {
                            this.axios.post('/app/v1_OrdenCompra/ConteoIncidentes', {
                                Fact_serie: this.Fact_Serie.trim(),
                                Fact_numero: this.Fact_Numero.trim(),
                                Proveedor: this.Seleccionada_OrdenEnc.IDPROVEEDORFK
                            })
                            .then(resp => {
                                if (!this.isEmptyObject(resp.data.json)) {
                                    this.$genera_reporte_envio({
                                        Nombre: 'Lista de Precios',
                                        Data_source: {
                                            Proveedor: this.Seleccionada_OrdenEnc.IDPROVEEDORFK,
                                            Documento: this.Fact_Serie.trim() + '-' + this.Fact_Numero.trim()
                                        },
                                        Data_report: this.ParametrosReporte
                                    })
                                }
                            })
                            
                            this.datosContrasena.DOCUMENTO = this.Fact_Serie.trim() + '-' + this.Fact_Numero.trim()
                            this.datosContrasena.IDPROVEEDORFK = this.Seleccionada_OrdenEnc.IDPROVEEDORFK
                            this.datosMovimiento.CODIGO_MOVIMIENTO = resp.data.resultado
                            this.datosMovimiento.ExcentoIva = res_exento
                            this.Estado_RecepionOrden = false;
                            this.Consultar_OrdenEnc();
                            this.Genera_Contrasena(this.datosContrasena)
                            this.Consultar_Movimiento(this.datosMovimiento)

                            
                        }
    
                    })
                    .catch(() => {
    
                    })
                }
                
    
            },
            LimpiarCampos() {
                this.Fact_Serie = '';
                this.Fact_Numero = '';
                this.Fact_Fecha = '';
                this.cb_departamentos = '';
                this.cb_bodegas = '';
                this.id_departamento_selec = '';
                this.id_bodega_seleccionada = '';
                this.Observaciones = ''
                this.granTotal = 0
            },
            Validacion_Campos(Tipo, Nombre, valor, obligatorio, cant_maxima) {
                /**
                 * @General
                 * Tipo : N = Númerio, Nombre = Descripción del campo, Valor = A evaluar, Obligatorio= Verdadero o falso,cant_maxima  = Aplica unicamente para un string y define la cantidad de caracteres 
                 * Devuelve un mensaje al usuario si las validaciones no se cumple y aborta, de lo contrario devuelve verdadero
                 */
                if (Tipo == 'ID') {
                    if (valor <= 0 || valor === null || valor === undefined) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else if (Tipo == 'N') {
    
                    if (valor < 0 || valor === "") {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                }else if(Tipo == 'G'){
                    if(valor <= 0){
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'El grid de \'' + Nombre + '\' debe de contener al menos 1 linea.',
                        });
                        return false;
                    }else{
                        return true
                    }
                } else {
                    if (valor == '' && obligatorio == true) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
    
                        if (cant_maxima < valor.length) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: 'Campo ' + Nombre + ' no debe superar los ' + cant_maxima + ' carácteres.',
                            });
                            return false;
                        } else {
                            return true;
                        }
    
                    }
                }
            },
            Guardar_ExentoIVA(Datos) {
                this.res_variables = false;
                var res_exento = false;
    
                if (Datos.EXENTO_IVA)
                    res_exento = 'S'
                else
                    res_exento = 'N'
    
                this.axios.post('/app/inventario/AsociacionProductoProveedor', {
                        codigoAsociacion: 0,
                        CodigoNuevo: Datos.IDPRODUCTOFK,
                        Proveedor: this.Seleccionada_OrdenEnc.IDPROVEEDORFK,
                        exento_iva: res_exento,
                        Activo: 'S',
                        tipo_transaccion: 'N'
    
                    })
                    .then()
                    .catch(() => {})
    
            },
            /*********************************Reporte ******************* */
            CentrarTexto(text, y, doc) {

                var textWidth = doc.getStringUnitWidth(text) * doc.internal.getFontSize() / doc.internal.scaleFactor;
                var textOffset = (doc.internal.pageSize.width - textWidth) / 2;
                doc.text(textOffset, y, text);

            },
            /**************** Ingreso de compra por medio de caja chica ****************/
            NuevoIngreso(){
                this.titulo = 'Compras de Caja Chica'
                this.Estado_RecepionOrden = true
                this.isNumeroOrden = false
                this.isCajaChica = true
                this.Seleccionada_OrdenEnc.Consignacion = ''
                const NewFechaPago = new Date(Date.now())
                const[month, day, year, hours, minutes, seconds] = [ NewFechaPago.getMonth() + 1, NewFechaPago.getDate(), NewFechaPago.getFullYear(),
                                                    NewFechaPago.getHours(), NewFechaPago.getMinutes(), NewFechaPago.getSeconds() ]
                const dia = day < 10 ? '0' + day : day
                const mes = month < 10 ? '0' + month : month
                const hora = hours
                const minutos = minutes
                const segundos = seconds
                this.fechaNuevo = `${dia}/${mes}/${year} ${hora}:${minutos}:${segundos}`
                this.ingresoNuevo = '0'
                this.Lista_DetalleOrden = []
                this.detalleProductosManual = []
                this.archivoContrasena = ''
                this.archivoMovimiento =''
                this.ExcentoIva = false
                this.Consultar_proveedor()
                this.Consultar_Bodega('F',this.permisos_tipos_bodegas.join(","),this.permiso_bodega_vencido);
                this.CajaChica()
                this.LimpiarCampos()
            },
            Consultar_proveedor() {
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/ConsultarProveedor', {
                        
                    })
                    .then(resp => {

                        if (resp.data.codigo != 0) {

                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Proveedores',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.listaProveedores = "";
                        } else {
                            //Decodificación
                            this.listaProveedores = resp.data.json;

                        }
                    })
                    .catch(() => {})
            },
            seleccion_proveedor({
                NIT,
                NOMBRE,
            }) {
                return `${NIT} - ${NOMBRE} `
            },
            onChangeProveedor(value) {
                if (value !== null && value.length !== 0) {

                    this.proveedor.codigo = value.CODIGO,
                    this.proveedor.nombre = value.NOMBRE.toUpperCase();
                    this.proveedor.nit = value.NIT;

                } else {
                    this.proveedor.codigo = '';
                    this.proveedor.nombre = '';
                    this.proveedor.nit = '';
                }
            },
            CajaChica(){
                this.axios.post("/app/v1_OrdenCompra/CajaChicaInventarioProductos", {
                    
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {

                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Proveedores',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.Lista_CajaChica = "";
                    } else {
                        //Decodificación
                        this.Lista_CajaChica = resp.data.json;

                    }
                })
            },
            CajaChica_seleccionado({
                CodigoCajaChica,
                NombreEmpleado
            }) {
                return  CodigoCajaChica ? `${CodigoCajaChica} - ${NombreEmpleado} `: ''
            },
            onChangeCajaChica(value){
                this.CodigoLoteCajaChica = ''
                this.Cb_LoteCajaChica = ''
                if (value !== null && value.length !== 0) {
                    this.CodigoCajaChica = value.CodigoCajaChica;
                    this.LoteCajaChica()
                } else {
                    this.CodigoCajaChica = '';
                }
            },
            LoteCajaChica(){
                this.axios.post("/app/v1_OrdenCompra/CajaChicaLoteInventarioProductos", {
                    "CodigoCajaChica": this.CodigoCajaChica
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {

                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Proveedores',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.Lista_LoteCajaChica = "";
                    } else {
                    //Decodificación
                     this.Lista_LoteCajaChica = resp.data.json;

                    }
                })
            },
            LoteCajaChica_seleccionado({
                Siguiente
            }) {
                return  Siguiente
            },
            onChangeLoteCajaChica(value){
                if (value !== null && value.length !== 0) {
                    this.CodigoLoteCajaChica = value.Siguiente

                } else {
                    this.CodigoLoteCajaChica = ''
                }
            },
            Nuevo_Lote(){
                this.axios.post('/app/v1_OrdenCompra/CajaChicaLoteNuevo', {
                    CodigoCajaChica: this.CodigoCajaChica
                })
                .then(() => {
                    this.LoteCajaChica()
                })
            },
            
            PermisoConta(value){
                this.axios.post('/app/v1_OrdenCompra/PermisoContabilidad', {
                    CodigoMovimiento: value.CODIGO_MOVIMIENTO,
                    EstadoContabilidad: this.permisoContabilidad
                })
                .then(() => {
                    this.Consultar_OrdenEnc();
                })
            },
            cargar_producto: function () {
                if(this.id_bodega_seleccionada != ''){
                    this.$vs.loading();
                    this.axios.post('/app/v1_OrdenCompra/ConsultaRequisicion_Producto', {
                            Producto: this.producto_buscar.trim(),
                            TipoBodega: this.tipoBodega
                        })
                        .then(resp => {

                            this.$vs.loading.close();
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.producto = []
                                resp.data.json.map(data => {
                                    this.producto.push({
                                        ...data
                                    })
                                })

                                this.Mostrar_resultado_producto(this.producto);

                            } else {
                                this.$vs.loading.close();
                                this.producto = []
                                this.$vs.notify({
                                    color: 'danger',
                                    title: 'Producto',
                                    text: (resp.data.codigo < 0) ? resp.data.descripcion : 'Sin Registros',
                                })
                            }
                        })
                        .catch(() => {
                            this.$vs.loading.close();

                        })
                    this.$vs.loading.close();
                }else{
                    this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Productos',
                            text: 'Debe de seleccionar una bodega',
                        })
                }
                    
            },
            Mostrar_resultado_producto(value) {

                if (value.length == 1) {
                    this.productoSeleccionado.Codigo = value.find(obj => obj.Codigo != '').Codigo;
                    this.productoSeleccionado.marca_comercial = value.find(obj => obj.Descripcion != '').Descripcion;
                    this.productoSeleccionado.Principio_activo = value.find(obj => obj.Principio_activo != '').Principio_activo;
                    this.productoSeleccionado.Concentracion = value.find(obj => obj.Concentracion != '').Concentracion;
                    this.productoSeleccionado.codigo_interno = value.find(obj => obj.codigo_interno != '').codigo_interno;
                    this.productoSeleccionado.Presentacion = value.find(obj => obj.PresentacionNombre != '').PresentacionNombre;
                } else if (value.length > 1) {
                    this.estadoVentanaEmergenteBusqueda = true;
                }

            },
            Seleccionar_Producto(value) {
                this.producto_buscar = value.Codigo;
                this.productoSeleccionado.Codigo = value.Codigo;
                this.productoSeleccionado.marca_comercial = value.Descripcion;
                this.productoSeleccionado.Principio_activo = value.Principio_activo;
                this.productoSeleccionado.Concentracion = value.Concentracion;
                this.productoSeleccionado.codigo_interno = value.codigo_interno;
                this.productoSeleccionado.Presentacion = value.PresentacionNombre;
                this.estadoVentanaEmergenteBusqueda = false;
                this.producto = [];
            },
            Limpiar_CamposDetP() {
                this.productoSeleccionado.Codigo = '';
                this.productoSeleccionado.marca_comercial = '';
                this.productoSeleccionado.Principio_activo = '';
                this.productoSeleccionado.Concentracion = '';
                this.productoSeleccionado.codigo_interno = '';
                this.productoSeleccionado.Presentacion = '';
                this.cantidadProducto = '';
                this.ingresoPrecioUnitario = '';
                this.IngresoSubTotalProducto = ''
                this.bonificacion = ''

            },
            ValidarMargenPrecio(){
                let res_exento = ''
                this.isProductoCorrecto = false
                this.ingresoPrecioUnitario = this.IngresoSubTotalProducto / this.cantidadProducto
                if (this.ExcentoIva)
                    res_exento = 'N'
                else
                    res_exento = 'S'
                this.axios.post('/app/v1_OrdenCompra/MargenPrecioProducto', {
                    PrecioUnitario: this.ingresoPrecioUnitario,
                    CodigoProducto: this.productoSeleccionado.Codigo,
                    CantidadProducto: this.cantidadProducto,
                    ExentoIva: res_exento
                })
                .then(() => {
                    this.isProductoCorrecto = true;
                })

            },
            ValidarMargenPrecioOrden(){
                let res_exento = ''
                this.isProductoCorrecto = false
                if (this.ExcentoIva)
                    res_exento = 'N'
                else
                    res_exento = 'S'
                this.axios.post('/app/v1_OrdenCompra/MargenPrecioProducto', {
                    PrecioUnitario: this.ingresoPrecioUnitario,
                    CodigoProducto: this.productoSeleccionado.Codigo,
                    CantidadProducto: this.cantidadProducto,
                    ExentoIva: res_exento
                })
                .then(() => {
                    this.isProductoCorrecto = true;
                })

            },
            Agregar_Detalle() {
                /**
                 * @General
                 * Función Permite  actualizar y  almacenar un nuevo registro detalle;
                 */
                if(!this.isProductoCorrecto){
                    return
                }
                if (this.productoSeleccionado.Codigo === 0 || this.productoSeleccionado.Codigo === '' || this.productoSeleccionado.Codigo === null || this.productoSeleccionado.Codigo === undefined) {
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Ordenes de Compra',
                        text: 'Seleccionar producto.',
                    })
                    return;
                }
                if (this.ingresoPrecioUnitario === 0 || this.ingresoPrecioUnitario === '') {
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Ordenes de Compra',
                        text: 'Ingresar Precio Unitario',
                    })
                    return;
                }

                if (this.cantidadProducto <= 0) {
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Ordenes de Compra',
                        text: 'Cantidad Inválida',
                    })
                } else {
                    
                    this.detalleProductosManual.push({
                        producto: this.productoSeleccionado.Codigo,
                        cantidad: this.cantidadProducto,
                        precio_unitario: parseFloat(this.ingresoPrecioUnitario).toFixed(2),
                        unidad_medida: this.productoSeleccionado.unidadMedida,
                        subtotal: parseFloat(this.cantidadProducto * this.ingresoPrecioUnitario).toFixed(2),
                        descripcion: this.productoSeleccionado.marca_comercial,
                        bonificacion: this.cantidadBonificacion

                    })
                    this.producto_buscar = ''
                    var total = 0;
                    this.detalleProductosManual.forEach(element => {
                        total += (Number(element.subtotal));
                        
                    });
                    this.granTotal = parseFloat(total).toFixed(2);
                    this.cantDetalle = this.cantDetalle + 1
                    this.Limpiar_CamposDetP()

                }

            },
            EliminarProducto(index, datos) {
                /**
                 * @General
                 * Función eliminar registro;
                 */
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',
                    text: '¿Eliminar detalle? ',
                    accept: () => {
                        this.granTotal = parseFloat(this.granTotal - datos.subtotal).toFixed(2)
                        this.$delete(this.detalleProductosManual, index)
                    }
                })
            },
            getStyle(dias, ortopedia){
                return dias > 3 && ortopedia == 'S' ? 'background-color: #FFD404; opacity: 0.7;': 'background-color: white;'
            }
        },
    
    }
    </script>
    <style scoped>
        .divAlign{
            margin-left: 10px;
            color:#E74C3C;
            opacity: 0.7;
            border: 0;
            font-size: 24px;
            background-color: white;
            font-weight: bold;

        }
    </style>
    