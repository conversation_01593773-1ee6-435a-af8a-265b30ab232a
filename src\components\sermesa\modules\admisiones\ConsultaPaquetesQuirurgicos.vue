<!-- ConsultaPaquetesQuirurgicos.vue -->

<template>
    <div id="app">

        <div class="tabla-paquetes">

            <vs-table2 max-items="4" tooltip :data="ListaPaquetes">
                <template slot="thead">
                    <th width="50px" filtro="CodigoPaciente">CodigoPaciente</th>
                    <th width="50px" filtro="Id">Codigo Paquete</th>
                    <th width="300px" filtro="Descripcion">Descripcion</th>
                    <th width="150px" filtro="Nombre">Nombre</th>
                    <th width="30px" filtro="Apellido">Apellido</th>
                    <th width="30px" filtro="ApellidoCasada">ApellidoCasada</th>
                </template>

                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data" :ref="`list_${indextr}`" v-on:click=submit(tr)
                        :class="{ 'activo': tr.activo }">

                        <vs-td2>
                            {{ tr.Co<PERSON>e }}
                        </vs-td2>
                        <vs-td2>
                            {{ tr.Id }}
                        </vs-td2>

                        <vs-td2>
                            {{ tr.Descripcion }}
                        </vs-td2>

                        <vs-td2>
                            {{ tr.Nombre }}
                        </vs-td2>

                        <vs-td2>
                            {{ tr.Apellido }}
                        </vs-td2>

                        <vs-td2>
                            {{ tr.ApellidoCasada }}
                        </vs-td2>

                    </tr>
                </template>

            </vs-table2>
        </div>
        <!-- </div> -->
    </div>
</template>


  
<script>

export default {


    name: "PaquetesQuirurgicos",
    props: {
        IdPaciente: {
            required: true,
            type: Number
        }
    },
    components: {
    },
    data() {
        return {
            selPolizaSaludSiempre: '',
            ListaPaquetes: []

        };
    },
    watch: {
    },
    activated() {
        
    },
    mounted() {
        this.ConsultaPaquete()
    },
    methods: {
        async ConsultaPaquete() {
            const resp = await this.axios.post('/app/v1_admision/ConsultaPaquetesQuirurgico', {
                Empresa: '',
                Hospital:'',
                IdPaciente: this.IdPaciente

            })
            this.ListaPaquetes = resp.data.json
            
            
        },
        submit(item) {
            this.$emit("paquete-seleccionado", item);
        }


    }
};
</script>


<style scoped>


</style>

<style >
.tabla-paquetes .contenedor-paquete {
    min-height: auto;
    margin: 5px;
}
</style>
