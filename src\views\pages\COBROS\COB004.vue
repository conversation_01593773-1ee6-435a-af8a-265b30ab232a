<template>
<vx-card title="Reporte Honorarios">

    <!-- buscadores -->
    <buscador ref="buscar_colegiado" buscador_titulo="Buscador / Colegiados" :api="'app/honorarios/BuscarColegiado'" :campos="['Colegiado','MedicoNombre']" :titulos="['Colegiado','Nombre']" :multiselect="false" :api_validar_campo="true" />

    <!-- reportes -->
    <vs-popup id="contentreport" classContent="popup-generar" title="Informe" :active.sync="info.reporte" fullscreen style="z-index:99999;height:100%">
        <embed v-if="info.reporte_src!=''" type="application/pdf" :src="info.reporte_src" ref="pdfDocument" width="100%" height="98%" />
    </vs-popup>

    <form>
        <vs-divider>Información de Médico</vs-divider>
        <form v-on:submit.prevent="cargar_colegiado()">
            <div class="flex flex-wrap">

                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <!-- <vs-input label="Número" class="w-full" type="number" v-model="info.orden_numero" /> -->
                    <label class="vs-input--label">Colegiado Médico</label>
                    <vx-input-group class="">
                        <vs-input v-model="info.colegiado" :disabled="bloqueoBusqueda" @change="info.colegiado=info.colegiado.toUpperCase(); cargar_colegiado()" />
                        <template slot="append">
                            <div class="append-text btn-addon">
                                <button type="submit" v-show="false" name="button"></button>
                                <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_colegiado()" icon="icon-search" v-if="bloqueoBusqueda==false"></vs-button>
                                <vs-button id="button-with-loading" color="danger" icon-pack="feather" @click="limpiar_colegiado()" icon="icon-x" v-else></vs-button>
                                <vs-button style="margin-left:1px" v-on:click="honorarios_reporte()" icon-pack="fas" icon="fa-print" v-if="bloqueoBusqueda"></vs-button>
                            </div>
                        </template>
                    </vx-input-group>
                </div>

            </div>
        </form>
        <div class="flex flex-wrap">
            <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
                <vs-input label="Nombre Completo" class="w-full" v-model="info.medico_nombre" disabled />
            </div>
            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <vs-input label="Tipo Médico" class="w-full" v-model="info.medico_tipo" disabled />
            </div>
            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <vs-input label="NIT" class="w-full" v-model="info.medico_nit" disabled />
            </div>
        </div>
        <div class="flex flex-wrap">
            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <vs-input label="Especialidad" class="w-full" v-model="info.medico_especialidad" disabled />
            </div>
            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <vs-input label="Estado" class="w-full" v-model="info.medico_estado" disabled />
            </div>
        </div>

        <vs-divider>Listado de Cargos</vs-divider>
        <!-- {{listado_cargos_select}} -->
        <div v-if="listado_cargos_empty" style="padding:10px; text-align:center">
            Sin Registros
        </div>
        <vs-table v-model="listado_cargos_select" :data="listado_cargos" v-if="listado_cargos.length>0">
            <template slot="thead">
                <vs-th>Admisión</vs-th>
                <vs-th>Paciente</vs-th>
                <vs-th>Categoría Cargo</vs-th>
                <vs-th>Código Cargo </vs-th>
                <vs-th>Descripción Cargo </vs-th>
                <vs-th>Fecha Cargo </vs-th>
                <vs-th>Motivo Bloqueo </vs-th>
                <vs-th>Honorarios a Pagar </vs-th>
                <vs-th>Médico </vs-th>
                <vs-th>Colegiado </vs-th>
            </template>
            <template slot-scope="{data}">
                <vs-tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">
                    <vs-td :data="tr.admision">
                        {{ tr.admision }}
                    </vs-td>
                    <vs-td :data="tr.paciente">
                        {{ tr.paciente }}
                    </vs-td>
                    <vs-td :data="tr.categoria">
                        {{ tr.categoria }}
                    </vs-td>
                    <vs-td :data="tr.codigo">
                        {{ tr.codigo}}
                    </vs-td>
                    <vs-td :data="tr.descripcion">
                        {{ tr.descripcion}}
                    </vs-td>
                    <vs-td :data="tr.fecha">
                        [{{ tr.fecha }}]
                    </vs-td>
                    <vs-td :data="tr.motivo">
                        <!-- {{ tr.motivo }} -->
                        <!-- {{tr}} -->
                        {{listado_tipologia_filtro(tr.motivo)}}
                    </vs-td>
                    <vs-td :data="tr.pagar">
                        {{ tr.pagar}}
                    </vs-td>
                    <vs-td :data="tr.medico">
                        {{ tr.medico}}
                    </vs-td>
                    <vs-td :data="tr.colegiado">
                        {{ tr.colegiado}}
                    </vs-td>
                </vs-tr>
            </template>
        </vs-table>

    </form>
    <vs-divider />
</vx-card>
</template>

<script>
// require styles
// import {
//     VueEditor
// } from "vue2-editor";

export default {
    data() {
        return {
            popupActive2: false,
            bloqueoBuscar: false, //utlizado para evitar llamar multiples llamadas a los registros
            bloqueoBusqueda: false,
            listado_cargos_select: [],
            listado_cargos: [],
            listado_cargos_empty: false,
            listado_tipologia: [],
            info: {
                colegiado: null,
                medico_nombre: null,
                medico_tipo: null,
                medico_nit: null,
                medico_especialidad: null,
                medico_estado: null,
                reporte: null,
                reporte_src: null
            },
        }
    },
    components: {
        // quillEditor,
        // VueEditor
    },
    computed: {
        listado_cargos_count() {
            return this.listado_cargos.filter(data => data.seleccion).length
        }
    },
    methods: {
        /*
            - Buscadores
            - Cargar / Listados
            - Guardar
            - Editar
            - Limpiar
            - Otros
        */

        // -------------------------------------------------------------------------------------------
        // Buscador
        // -------------------------------------------------------------------------------------------
        buscar_colegiado() {
            this.$refs.buscar_colegiado.iniciar((data) => {
                // console.log(data)
                if (data != null) {
                    this.info.colegiado = data.Colegiado
                    this.cargar_colegiado()
                }
            })
        },

        // -------------------------------------------------------------------------------------------
        // Cargar / Listado
        // -------------------------------------------------------------------------------------------
        cargar_tipologias() {
            this.axios.post('/app/ajenos/cargarTipologia', {})
                .then(resp => {
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        this.listado_tipologia = resp.data.json
                    }
                })
        },
        cargar_colegiado() {
            this.listado_cargos_empty = false
            this.bloqueoBuscar = true;
            this.bloqueoBusqueda = true
            this.listado_cargos = []

            this.$vs.loading({
                background: this.backgroundLoading,
                color: this.colorLoading,
                container: "#button-with-loading",
                scale: 0.45
            })

            this.axios.post('/app/ajenos/cargarColegiado', {
                    colegiado: this.info.colegiado
                })
                .then(resp => {
                    // console.log(resp.data.json.length)
                    return new Promise((resolve, reject) => {
                        if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                            let res = resp.data.json[0]
                            this.info.medico_nombre = res.MedicoNombre
                            this.info.medico_tipo = res.Tipo
                            this.info.medico_nit = res.Nit
                            this.info.medico_especialidad = res.Especialidad
                            this.info.medico_estado = res.Estado

                            resolve(this.axios.post('/app/ajenos/cargarCargos', {
                                colegiado: this.info.colegiado
                            }))

                        } else {
                            this.bloqueoBuscar = false;
                            this.bloqueoBusqueda = false
                            reject(false)
                        }
                        this.$vs.loading.close("#button-with-loading > .con-vs-loading")
                    })
                })
                .then((resp) => {

                    if (resp.data.json.length == 0) this.listado_cargos_empty = true
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        let res = resp.data.json
                        this.listado_cargos = res.map(data => {
                            return {
                                seleccion: false,
                                admision: data.Admision,
                                paciente: data.Paciente,
                                tipoOrden: data.TipoOrden,
                                orden: data.Orden,
                                linea: data.Linea,
                                categoria: data.Categoria,
                                codigo: data.Producto,
                                descripcion: data.NombreProducto,
                                fecha: this.$formato_fecha(data.Fecha.split(' ')[0]),
                                motivo: data.BloqueoHonorariosMotivo,
                                pagar: data.Valor,
                                medico: data.NombreMedico.trim() + ' ' + data.ApellidoMedico,
                                colegiado: (data.Codigo) ? data.Codigo : data.Colegiado
                            }
                        })

                    }
                })
                .catch((err) => {
                    
                    this.$vs.loading.close("#button-with-loading > .con-vs-loading")

                    this.bloqueoBuscar = false;
                    this.bloqueoBusqueda = false
                    this.$vs.notify({
                        time: 4000,
                        title: 'Honorarios',
                        text: "No se ha encontrado el colegiado",
                        iconPack: 'feather',
                        icon: 'icon-alert-circle',
                        color: 'danger',
                        position: 'bottom-right'
                    })
                    this.limpiar_colegiado()
                })
        },

        // -------------------------------------------------------------------------------------------
        // Limpiar
        // -------------------------------------------------------------------------------------------
        limpiar_colegiado() {
            this.bloqueoBuscar = false //utlizado para evitar llamar multiples llamadas a los registros
            this.bloqueoBusqueda = false

            this.listado_cargos_select = []
            this.listado_cargos = []

            this.info.colegiado = null
            this.info.medico_nombre = null
            this.info.medico_tipo = null
            this.info.medico_nit = null
            this.info.medico_especialidad = null
            this.info.medico_estado = null
        },

        listado_tipologia_filtro(IdMotivo) {
            let arr = this.listado_tipologia.filter(data => data.IdMotivo == IdMotivo)
            return (arr.length > 0) ? arr[0].Nombre : ""
        },

        // -------------------------------------------------------------------------------------------
        // Otros
        // -------------------------------------------------------------------------------------------
        honorarios_reporte() {
            this.$reporte_modal({
                Nombre: "Honorarios Bloqueados",
                Opciones: {
                    colegiado: this.info.colegiado
                }
            })
        },

    },
    mounted() {
        this.cargar_tipologias()
    },
    created() {

    }
}
</script>

<style>
.button {
    margin: 0px !important;
}

.btnx {
    margin-left: 10px !important;
    background: red;
    border-radius: 5px 0px 0px 5px;
}

.btn-drop {
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: red;

}

.menu-funcionalidad {
    /* padding-top:0 !important; */
    /* box-shadow: 0 0 5px rgba(0, 0, 0, 0.3); */
    /* padding: 15px; */
    display: inline;
    float: right;
    border-radius: 10px;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 10px;
}

.menu-funcionalidad a {
    text-align: center;
    padding-right: 10px;
}

.menu-funcionalidad i {
    font-size: 20px;
}

.vs-switch {
    height: 30px;
    width: 60px !important;
}

.vs-circle-switch {
    height: 25px;
    width: 25px;
}

.vs-switch--icon {
    font-size: 15px
}

.vs-switch.vs-switch-active .vs-switch--circle {
    margin-left: calc(100% - 30px);
}

.popup-generar {
    height: 100%
}
</style>
