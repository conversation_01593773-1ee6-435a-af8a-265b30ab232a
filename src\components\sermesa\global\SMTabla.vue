<template>
<div class="smtabla" :class="{tooltip:(tooltip!==false),noSelectText:(noSelectText!==false)}">
    <div class="header">
        <div>
            <slot name="header"></slot>
        </div>
        <vs-spacer></vs-spacer>
        <vs-button v-if="exportarExcel!=null" :disabled="listadoLimitado.length==0" color="success" size="small" class="mr-1" style="font-size:12px;padding:10px; height:37px" @click="exportTableToExcel(exportarExcel)"> <i class="fas fa-file-excel"></i> Exportar a Excel</vs-button>
        <input v-if="search!==false" class="busqueda" v-model="busqueda" type="text" placeholder="Busqueda">
    </div>
    <div class="contenedor-tabla" :style="{'height':(height?height:'auto')}">
        <table ref="tabla"  v-on="$listeners">
            <thead ref="thead" v-if="header.length==0">
                <slot name="thead" :encabezado="encabezado"></slot>
            </thead>

            <thead v-else style="box-shadow:0 2px 3px rgba(0,0,0,0.4);border-radius:5px;">
                <th v-for="(item,index) in header" :key="index" class="smtablehead" :class="{order:item.order,filtro:item.filtroShow}" @click="accionHeader(item)" :width="item.width">
                    <!-- Filtrado -->
                    <div class="filtro" v-if="item.filtroShow" v-clickoutside="salirFiltro">
                        <table style="border-spacing: 0;">
                            <tr>
                                <td>
                                    <vs-input size="small" v-model="filtroBusqueda"></vs-input>
                                </td>
                                <td>
                                    <!-- <vs-button size="small"><i class="fas fa-trash-alt"></i></vs-button> -->
                                    <i style="color:#E74C3C;cursor:pointer" class="fas fa-trash-alt" @click="limpiarFiltro(item)"></i>
                                </td>
                            </tr>
                        </table>
                        <div class="filtro-listado">
                            <vs-checkbox v-for="(item, key) in filtroListadoBusqueda" @change="pagina = 1" :key="`select${key}`" v-model="item.seleccion">{{(Array.isArray(item.valor)?item.valor.join(' '): item.valor)}}</vs-checkbox>

                            <div v-if="filtroListado.length==0"> Sin Datos</div>
                        </div>
                    </div>

                    <!-- Data -->
                    <div width="100%" style="border-left:2px solid rgba(0,0,0,0.1);overflow:hidde;padding:5px;">
                        <table class="ttable" style=" ">
                            <tr>
                            <td v-if="item.order" style="padding-right:4px">
                                <i v-if="item.orderAsc == 0" style="opacity:0.5" class="fas fa-sort"></i>
                                <i v-else-if="item.orderAsc == 1" class="fas fa-sort-down"></i>
                                <i v-else-if="item.orderAsc == 2" class="fas fa-sort-up"></i>
                            </td>
                            <td>
                                {{item.etiqueta}}
                            </td>
                            <td v-if="item.filtro"  class="tdFiltro" @click="mostrarFiltro(item)">
                                <i style="font-size:10px" class="fas fa-filter" :class="{activo:item.filtroActivo}"></i>
                            </td>
                        </tr>
                        </table>
                    </div>
                </th>
            </thead>
            <tbody ref="body" @click="header.map(m=>m.filterShow = false)">
                <slot name="default" @click="header.map(m=>m.filterShow = false)" :data="listadoLimitado"></slot>
            </tbody>            
            <tfoot>
                <slot name="tfooter"></slot>
            </tfoot>
        </table>
        <div class="nodata" v-if="paginas===0" @click="header.map(m=>m.filterShow = false)">
            Sin Datos
        </div>
    </div>

    <div class="paginas" v-if="pagination!==false && paginas>0">
        <i class="fas fa-chevron-left" :class="[(pagina===1)?'disabled':'']" @click="(pagina>1)?Actualizacion().pagina(pagina-1):null"></i>
        <div class="pagina">
            {{pagina}} de {{paginas}}
        </div>
        <i class="fas fa-chevron-right" :class="[(pagina>=paginas)?'disabled':'']" @click="(pagina<paginas)?Actualizacion().pagina(pagina+1):null"></i>
    </div>
    <div class="paginas" v-if="pagination!==false && paginas==0"></div>
</div>
</template>

<script>
/*
ATRIBUTOS DEL ENCBAZADO
    order:  Permite ordenar un campo 
            order="nombre del campo"
    filtro: Permite filtrar el campo
            filtro="nombre del campo"
*/

export default {
    name: 'vs-table2',
    data() {
        return {
            header: [],
            pagina: 1,
            busqueda: null,
            filtroListado: [],
            filtroSeleccion: [],
            filtroBusqueda: '',
            filtroShowTimer: false
        }
    },
    props: {
        data: {
            default: [],
        },
        encabezado: {
            default: null
        },
        maxItems: {
            default: 0
        },
        tooltip: {
            default: false
        },
        noSelectText: {
            default: false
        },
        pagination: {
            default: false
        },
        search: {
            default: false
        },
        // Permite mostrar los campos a filtrar
        filter: {
            default: false
        },
        order: {
            default: null
        },
        exportarExcel: {
            default: null
        },
        /**
         * Diseño
         */
        height: {
            default: null
        },
    },
    computed: {
        paginas() {
            return Math.ceil(this.listadoFiltrado_Ordenamiento.length / this.maxItems)
        },
        paginasListado() {
            const arr = []
            for (let index = 1; index < 6; index++) {
                arr.push(index)
            }
            arr.push('...')
            arr.push(this.paginas)
            return arr
        },

        listadoFiltroCampos() {
            // Devuelve el total de registros
            if (this.filtroSeleccion.length == 0) return this.data

            const campos = []
            this.filtroSeleccion.forEach(item => {
                if (campos.indexOf(item.campo) == -1) campos.push(item.campo)
            })

            let result = []
            const DataCampos = (campoIndex, arrData) => {
                // Finalización de los campos
                if (campoIndex >= campos.length) {
                    result = arrData
                    return false
                }
                // Iniciando busqueda
                let arr = []

                // Mapeando selección
                this.filtroSeleccion.filter(f => f.campo == campos[campoIndex]).forEach(filtro => {
                    // Campo normal
                    if (filtro.campo.indexOf(',') == -1) {
                        arr.push(...arrData.filter(f => f[filtro.campo] == filtro.valor))
                    }
                    // Campo array
                    else {
                        const campos = filtro.campo.split(',')
                        // tipo,orden
                        const validarCampo = (item) => {
                            let resultado = true
                            campos.forEach((campo, key) => {
                                if (item[campo] != filtro.valor[key]) resultado = false
                            })
                            return resultado
                        }
                        arr.push(...arrData.filter(validarCampo))
                    }
                })
                DataCampos(campoIndex + 1, arr)
            }
            DataCampos(0, this.data)
            return result
        },

        listadoFiltrado_Busqueda() {
            const validar = (item) => {
                let rencuentra = false
                let encontrarPalabra = false
                Object.entries(item).forEach(([, value]) => {
                    let encuentra = true
                    if ((value && typeof value != "boolean")) {
                        //Buscando por palabras
                        this.busqueda.split(' ').map(m => {
                            const text = m.trim().toUpperCase()
                            if (text != '' && value.toString().toUpperCase().indexOf(text) < 0) encuentra = false
                        })
                    } else {
                        encuentra = false
                    }
                    if (encuentra) encontrarPalabra = true
                })
                if (encontrarPalabra) rencuentra = true
                return rencuentra
            }
            return (!this.busqueda) ? this.listadoFiltroCampos : this.listadoFiltroCampos.filter(validar)
        },

        // Ordenamiento
        listadoFiltrado_Ordenamiento() {
            let d = this.listadoFiltrado_Busqueda
            const header = this.header.filter(f => f.order && f.orderAsc != 0)
            if (header.length > 0) {
                return d.sort((a, b) => {
                    return header.map(m => {
                        let f1 = m.order.split(';').map(m => a[m]).join('')
                        let f2 = m.order.split(';').map(m => b[m]).join('')
                        if (m.orderType == 'number') {
                            f1 = parseFloat(f1)
                            f2 = parseFloat(f2)
                        }
                        // console.log(m.order, m.orderType == 'number', f1, f2)
                        return (m.orderAsc == 1) ? f1 > f2 ? 1 : f1 < f2 ? -1 : 0 : f1 < f2 ? 1 : f1 > f2 ? -1 : 0
                    })
                    // (a.last_nom > b.last_nom) ? 1: ((b.last_nom > a.last_nom) ? -1 : 0)
                })
            } else {
                return d
            }
        },

        // Filtrado por paginación
        listadoLimitado() {
            if (this.pagination !== false) {
                const pInicial = ((this.pagina - 1) * parseInt(this.maxItems))
                const pFinal = pInicial + parseInt(this.maxItems)
                const arr = (!this.maxItems) ? this.listadoFiltrado_Ordenamiento : this.listadoFiltrado_Ordenamiento.slice(pInicial, pFinal)
                // this.Actualizacion().tooltip()
                return arr
            } else {
                return this.listadoFiltrado_Ordenamiento
            }
        },
        filtroListadoBusqueda() {
            if (this.filtroBusqueda == '') return this.filtroListado
            return this.filtroListado.filter(item => ((Array.isArray(item.valor)) ? item.valor.join(' ').toUpperCase() : item.valor.toUpperCase()).indexOf(this.filtroBusqueda.toUpperCase()) != -1)
        }

    },
    watch: {
        data() {
            this.pagina = 1
            this.busqueda = null

        },
        busqueda() {
            this.pagina = 1
        },
        encabezado() {
            this.actualizacionEncabezado()
        },
        filtroListado: {
            handler: function(newValue) {
                if (newValue.length == 0) return false

                this.filtroSeleccion = this.filtroSeleccion.filter(f => f.campo != (Array.isArray(newValue[0].campo) ? newValue[0].campo.join(',') : newValue[0].campo))
                this.header.filter(f => f.filtro == newValue[0].campo)[0].filtroActivo = false
                const arr = newValue.filter(f => f.seleccion).map(m => {
                    return {
                        valor: m.valor,
                        campo: (Array.isArray(m.campo) ? m.campo.join(',') : m.campo)
                    }
                })
                if (arr.length > 0) this.header.filter(f => f.filtro == newValue[0].campo)[0].filtroActivo = true
                arr.forEach(item => {
                    this.filtroSeleccion.push(item)
                })
            },
            deep: true
        }
    },
    methods: {
        accionHeader(item) {
            // Ordenamiento
            this.header.map(m => m.orderAsc = (m.index != item.index) ? 0 : m.orderAsc)
            if (item.order) {
                if (item.orderAsc < 2) {
                    item.orderAsc++
                } else {
                    item.orderAsc = 0
                }
            }
        },
        actualizacionEncabezado() {
            this.header = []
            if (this.$refs.thead)
                this.$refs.thead.children.forEach((element, index) => {
                    this.header.push({
                        etiqueta: element.innerHTML,
                        filtro: element.hasAttribute("filtro") ? element.attributes.getNamedItem("filtro").value : null,
                        filtroShow: false,
                        filtroSelect: [],
                        filtroActivo: false,
                        index,
                        order: element.hasAttribute("order") ? element.attributes.getNamedItem("order").value : null,
                        orderAsc: 0,
                        orderType: element.hasAttribute("orderType") ? element.attributes.getNamedItem("orderType").value : 'string',
                        width: (element.hasAttribute("width")) ? element.attributes.getNamedItem("width").value : 'auto'
                    })
                })
        },
        Actualizacion: function() {
            return {
                pagina: (pagina) => {
                    this.pagina = pagina
                }
            }
        },
        exportTableToExcel(filename = '') {
            let downloadLink;
            let dataType = 'application/vnd.ms-excel';
            let tableSelect = this.$refs.tabla;
            let tableClone = tableSelect.cloneNode(true)
            let eliminar = tableClone.querySelectorAll('thead > th > div')
            eliminar.forEach(function(anchors) {
                const el = anchors.querySelectorAll('td')
                const texto = (el.length > 1) ? el[1].innerHTML : el[0].innerHTML
                anchors.parentElement.innerHTML = texto
            })

            eliminar = tableClone.querySelectorAll('.tooltip')
            eliminar.forEach(function(anchors) {
                anchors.remove()
            })

            eliminar = tableClone.querySelectorAll('.noExcel')
            eliminar.forEach(function(anchors) {
                anchors.remove()
            })

            let tableHTML = tableClone.outerHTML.replace(/ /g, '%20');
            tableHTML = tableHTML.replace(/á/g, '&aacute;')
            tableHTML = tableHTML.replace(/é/g, '&eacute;')
            tableHTML = tableHTML.replace(/í/g, '&iacute;')
            tableHTML = tableHTML.replace(/ó/g, '&oacute;')
            tableHTML = tableHTML.replace(/ú/g, '&uacute;')

            tableHTML = tableHTML.replace(/Á/g, '&Aacute;')
            tableHTML = tableHTML.replace(/É/g, '&Eacute;')
            tableHTML = tableHTML.replace(/Í/g, '&Iacute;')
            tableHTML = tableHTML.replace(/Ó/g, '&Oacute;')
            tableHTML = tableHTML.replace(/Ú/g, '&Uacute;')

            tableHTML = tableHTML.replace(/ñ/g, '&ntilde;')
            tableHTML = tableHTML.replace(/Ñ/g, '&Ntilde;')

            // Specify file name
            filename = filename ? filename + '.xls' : 'excel_data.xls';

            // Create download link element
            downloadLink = document.createElement("a");

            document.body.appendChild(downloadLink);

            if (navigator.msSaveOrOpenBlob) {
                var blob = new Blob(['\ufeff', tableHTML], {
                    type: dataType
                });
                navigator.msSaveOrOpenBlob(blob, filename);
            } else {
                // Create a link to the file
                downloadLink.href = 'data:' + dataType + ', ' + tableHTML;

                // Setting the file name
                downloadLink.download = filename;

                //triggering the function
                downloadLink.click();
            }
        },

        // Se llena la información de la tabla de filtros
        mostrarFiltro(item) {
            this.filtroBusqueda = ''
            this.header.map(m => m.filtroShow = false);

            setTimeout(() => {
                item.filtroShow = true
            }, 100);

            const arr = []
            this.listadoFiltroCampos.forEach(dataItem => {
                const campo = (item.filtro.indexOf(',') == -1) ? item.filtro : item.filtro.split(',')
                const valor = (!Array.isArray(campo)) ? dataItem[campo] : campo.map(m => dataItem[m])
                if (arr.findIndex(f => ((Array.isArray(f.valor)) ? f.valor.join(' ') : f.valor) == ((Array.isArray(valor)) ? valor.join(' ') : valor)) == -1) {
                    arr.push({
                        campo,
                        valor,
                        seleccion: (this.filtroSeleccion.findIndex(f => f.campo == campo && ((Array.isArray(f.valor)) ? f.valor.join(' ') : f.valor) == ((Array.isArray(valor)) ? valor.join(' ') : valor)) == -1) ? false : true
                    })
                }
            })

            this.filtroListado = arr

        },
        limpiarFiltro(item) {
            // console.log(item)
            this.filtroSeleccion = this.filtroSeleccion.filter(f => f.campo != item.filtro)
            this.filtroListado.filter(f => (Array.isArray(f.campo) ? f.campo.join(',') : f.campo == item.filtro)).map(m => m.seleccion = false)
            this.mostrarFiltro(item)
        },
        salirFiltro() {
            this.header.map(item => item.filtroShow = false)
        }

    },
    mounted() {
        this.actualizacionEncabezado()
    }
}
</script>

<style >
.smtabla {
    /* height: 500px; */
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    border-radius: 5px;
    padding: 10px;
    margin-top: 10px;
    margin-bottom: 10px;
    overflow: hidden;

    /* background-color:rgba(0,143,190,0.9) */
}

.smtabla.noSelectText {
    user-select: none;
}

.smtabla .nodata {
    padding: 40px 10px;
    text-align: center;
}

.smtabla .header {
    /* background-color: red; */
    padding: 5px;
    display: flex;
    justify-content: space-between;
}

.smtabla .header .busqueda {
    padding: 8px;
    border-radius: 10px;
    border: 1px solid rgba(0, 143, 190, 1);
    margin-bottom: 10px;
    width: 250px;
    height: 37px;
}

.smtabla table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;

    /* table-layout: fixed; */
}

.smtabla table th.order {
    cursor: pointer;
}

.smtabla table th {
    padding: 2px
}

.smtabla table th table td {
    padding: 5px
}

.smtabla tbody tr:not(.detalle):nth-child(odd) {
    background-color: #eee;
}

.smtabla tbody tr {
    border: 1px solid rgba(0, 0, 0, 0.2)
}

.smtabla tbody td {
    max-width: 100px;
    /* position: relative; */
    /* display: inline-block; */
}

.smtabla tbody td .tooltip {
    background-color: #555;
    padding: 3px 10px;
    /* text-align: center; */
    border-radius: 6px;

    white-space: nowrap;
    /* left: 0; */
    /* top: -13px; */
    margin-top: -12px;
    font-size: 12px;
    color: white;
    z-index: 1;
    display: none;
    transition: all ease 0.5s;
    min-width: 40px;
    /* max-width: 200px; */

    /* transition-delay: 5s;  */
}

.smtabla.tooltip tbody td:hover .tooltip {
    opacity: 1;
    display: block;
    position: absolute;
    visibility: visible;
    transition-delay: 0.5s;
    margin-left: -20px;
    /* display: inherit; */

}

.smtabla tbody td .contenido {
    padding: 10px 15px;
    font-size: 14px;
    overflow: hidden;
    white-space: nowrap;
    width: 100%;
    text-overflow: ellipsis;
    position: relative;

    /* display: inline-block; */
}

.smtabla tbody td .tooltip::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 30px;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #555 transparent transparent transparent;
}

.smtabla tbody tr:hover {
    background-color: rgba(0, 143, 190, 0.2) !important;
    box-shadow: inset 0px 0px 0 2px rgba(0, 0, 0, 0.3);
}

.smtabla .paginas {
    /* border: 1px solid red; */
    height: 37px;
    margin-top: 10px;
    padding: 10px 10px;
    display: flex;
    justify-content: flex-end;
}

.smtabla .paginas .pagina {
    padding: 5px 10px;
    /* padding-top: 5px; */
    cursor: pointer;
    text-align: center;
    height: 30px;
    /* width: 30px; */
    background-color: rgba(0, 143, 190, 1);
    color: white;
    border-radius: 10px;
    display: inline-block;
}

.smtabla .paginas .fas {
    padding: 8px 10px;
    cursor: pointer;
}

.smtabla .paginas .fas.disabled {
    color: #ccc;
}

.contenedor-tabla {
    overflow: auto;
    width: 100%;
    padding: 2px;
    min-height: 230px;

}
.smtablehead {
    position: relative;
    /* overflow: auto; */
}

th.filtro {
    border: 1px solid #7F8C8D
}

div.filtro {
    border: 1px solid #7F8C8D;
    position: absolute;
    /* bottom: 00px; */
    top: 100%;
    height: 140px;
    padding: 5px;
    background: white;
    z-index: 1;
    border-radius: 5px;
}

div.filtro .filtro-listado {
    overflow: auto;
    height: 90px;
    margin-top: 5px;
}

div.filtro .filtro-listado div {
    text-align: center;
}

.tdFiltro {
    text-align: right;
    color: #ccc;
    width: 10px;
}

.tdFiltro .activo {
    color: red;
}
</style>
