<template>
    <div class="flex flex-wrap">
      <div class="Encabezado">
            <!-- <EncabezadoExpediente ref="EncabezadoExpediente"></EncabezadoExpediente> -->
            <Encabezado2 ref="refEncabezado2" TipoBusqudaPaciente = "TODO" @onLoadedData="onLoadedDataEXP001"></Encabezado2>
        </div>

        <vs-button id="button-with-loading" @click="Metodo()">Limpiar</vs-button>
        <vs-input label="TipoBusqudda" v-model="tipoBuquedaPac" />
        <vs-button id="button-with-loading" @click="CAR()">OTRO</vs-button>



      <DxDataGrid :data-source="ValeDataSource"
          :editing="{allowAdding:true,allowDeleting:false,allowUpdating:true,mode:'popup' }"
          @cell-click="onCellClickGrid"
          @row-inserting="antesdeInsertar"
        > 
      
        <DxToolbar>
            <DxItem name="groupPanel" />
            <DxItem name="searchPanel" />
            <DxItem location="after" template="opcionesTemplate" />
        </DxToolbar>
        <template #opcionesTemplate>

        <ExpedienteGridToolBar :visible="true"  @add="addRow" @refresh="Cargar" :showItems="['add', 'refresh']" :pdfExportItems="[]">
            <DxButton id="botonBarra" icon="fas fa-history" hint="Ver Antecedente previos" @click="() => {}" />
        </ExpedienteGridToolBar>

</template>
      </DxDataGrid>

        
  </div>
</template>
<script>
//comentarios
// import EncabezadoExpediente from '../../EXPEDIENTE/EXP001.vue'
import {DxDataGrid, DxToolbar} from 'devextreme-vue/data-grid';
import CustomStore from 'devextreme/data/custom_store'
import ExpedienteGridToolBar from '../../EXPEDIENTE/ExpedienteGridToolBar.vue'
import {
    DxItem
} from 'devextreme-vue/form'
import DxButton from 'devextreme-vue/button'
export default {
  components:{
    // EncabezadoExpediente,
    Encabezado2: ()=> import('./VALENCHerencia.vue'),
    DxDataGrid,
    DxItem,
    DxToolbar,
    ExpedienteGridToolBar,
    DxButton,
  },
  props:{
    
  },
  methods:{
    Metodo(){

      this.$refs.refEncabezado2.LimpiarAdmision()
      
    },
    onLoadedDataEXP001(arg){
    
      this.SerieAdmision = arg.SerieAdmision
      this.CodigoAdmision = arg.CodigoAdmision
      
      
    },
    Cargar() {
                this.axios.post('/app/v1_ValeElectronico/ListaMedicamentosVale', {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                })
                .then(resp => {
                    this.ValeDataSource = resp.data.json.map((x, index) => {
                        return {
                            id: index,
                            ID: x.ID,
                            ProblemaEncontrado: (x.problemaencontrado),
                            CodProducto: x.CodProducto,
                            Producto: x.Producto,
                            CANTIDAD: x.CANTIDAD,
                            PRESENTACION: x.PRESENTACION,
                            VIADEADMINISTRACION: x.VIADEADMINISTRACION,
                            Horario: x.Horario,
                            DIAS: x.DIAS,
                            CodMedico: x.CodMedico,
                            Medico: x.Medico,
                            FECHAINICIO: x.FECHAINICIO,
                            FECHAOMISION: x.FECHAOMISION,
                            TipoSolicitud: x.TipoSolicitud,
                        }
                    })
                    // this.ValeDataSource.load().then(
                    //     () => {
                    //         this.refreshDataGrid()
                    //     },
                    //     (error) => {
                    //         console.error('error loading data customstore' + JSON.stringify(error))
                    //     }
                    // )
                })
           
        },
  },
  data() {
        return {
            SerieAdmision: null,
            CodigoAdmision: null,
            productoPedidos: [],
            
            ValeDataSource: new CustomStore({
                key: "ID",
                load: () => {
                    return this.productoPedidos;
                },
                
            }),


          tipoBuquedaPac: 'TODO3',
        
        }
      },
  mounted (){
    // this.$refs.refEncabezado2.$refs.busquedaPop.handleSubmit = (e) => {
    //       e.preventDefault()
    //           this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaPacientesAdmision', {
    //                   NombrePaciente: this.formulario.Nombre,
    //               })
    //               .then(resp => {
    //                   this.pacientes = resp.data.json.map((x) => {
    //                       return {
    //                           ...x,
    //                           Admision: x.SerieAdmision + x.Admision,
    //                       }
    //                   })
    //                   this.buscoConExpediente = false
    //               })
    //     }
  },
  watch: {
        'CodigoAdmision'(newval) {
            if (newval){
                this.Cargar()
            }
            else{
                //limpiar datos
            }
        },
    },
}

</script>


