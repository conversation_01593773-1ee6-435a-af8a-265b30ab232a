<template>
<div>
    <DxTabPanel ref="tabRegistro" :height="'100%'" :data-source="menu">
        <template #title="{ data: tab }">
            <span>
                <font-awesome-icon class="pr-1" :icon="['fas', tab.icon]" />
                {{ tab.title }}
            </span>
        </template>
        <template #item="{ data: tab }">
            <div>
                <div v-if="tab.id === 1" class="p-2">
                    <Bancos ref="Bancos" />
                </div>
                <div v-if="tab.id === 2" class="p-2">
                    <Cuentas ref="Cuentas" />
                </div>

                <!-- Opciones de banco -->
                <div v-if="tab.id === 3" class="p-2">
                    <Opciones ref="Opciones" />
                </div>
            </div>
        </template>
    </DxTabPanel>
</div>
</template>

<script>
import Bancos from './Bancos.vue';
import Cuentas from './Cuentas.vue';
import Opciones from './Opciones.vue';

export default {
    name: 'Mantenimientos',
    components: {
        Bancos,
        Cuentas,
        Opciones
    },
    data() {
        return {
            SelectedOption: 1,

            menu: [{
                    id: 1,
                    title: "Bancos",
                    icon: "landmark",
                },
                {
                    id: 2,
                    title: "Cuentas",
                    icon: "circle-user",
                },
                {
                    id: 3,
                    title: "Opciones",
                    icon: "gear",
                },
            ],

            opciones: [],

            editarFormulario: false
        }
    },
    methods: {
        CargarOpciones() {
            this.axios.post('/app/v1_bancos/Opciones', {
                    Opcion: 1
                })
                .then(resp => {
                    this.opciones = resp.data.json
                })
        },
        EditarFormulario() {
            this.editarFormulario = true
        },

        GuardarOpciones() {
            this.editarFormulario = false
        },

    },
    created() {},
    mounted() {},
    watch: {},
    computed: {

        colCountByScreen() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 2,
                    md: 5,
                    lg: 5
                };
        },
    }
}
</script>

<style>
.botonOpciones .dx-button-content {
    padding: 0px !important;
}
</style>
