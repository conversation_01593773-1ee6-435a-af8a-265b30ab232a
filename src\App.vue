<template>

<div id="app" :class="vueAppClasses">
    <reporte></reporte>
    <div class="app-contenedor">
        <router-view @setappclasses="setappclasses" />
    </div>
</div>
</template>

<script>
// import themeConfig from '@/../themeConfig.js'

export default {
    data() {
        return {
            vueAppClasses: [],
        }
    },
    components:{
        reporte:()=>import('./components/sermesa/global/SMReporte.vue')
    },
    methods: {
        setappclasses(classesStr) {
            this.vueAppClasses.push(classesStr)
        },
        
    },
    mounted() {

        // console.log(process.env.VUE_APP_URL)

        // this.toggleClassInBody(themeConfig.theme)

    },
    async created() {

        let dir = this.$vs.rtl ? "rtl" : "ltr"
        document.documentElement.setAttribute("dir", dir)

        // window.addEventListener('resize', this.handleWindowResize)
        // window.addEventListener('scroll', this.handleScroll)

    },
    destroyed() {
        // window.removeEventListener('resize', this.handleWindowResize)
        // window.removeEventListener('scroll', this.handleScroll)
    },
}
</script>
