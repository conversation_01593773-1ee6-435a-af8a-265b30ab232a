<template>
<div>
    <div class="ContenidoBancos p-2">
        <div v-if="SelectedOption > 0" class="pb-2 stickyBancos flex">
            <!-- <div class="pr-2 pl-2" style="display: grid; place-items: center; width: 75px">
                    <vs-tooltip text="Menú">
                        <vs-button :class="'buttonTab'" :color="'primary'" :type="'filled'" @click="RegresarMenu()">
                            <div style="height = '50px'; width= '50px'">
                                <font-awesome-icon :icon="['fas', 'right-from-bracket']" rotation="180" style="font-size: 30px;" />
                            </div>
                        </vs-button>
                    </vs-tooltip>
                </div> -->
            <DxTabs :selected-index="SelectedOption-1" height="10%" width="100%" :rtl-enabled="false" orientation="horizontal" styling-mode="secondary" :icon-position="'top'" :show-nav-buttons="true" :scroll-by-content="true" @item-click="(e)=>OpcionSeleccionada(menu[e.itemIndex])" :element-attr="tabsAttributes">
                <DxTabsItem v-for="(item, index) in menu" :key="index" template="tabButton" />
                <template #tabButton="{index}">
                    <vs-tooltip :text="menu[index].title">

                        <vs-button :class="menu[index].id === SelectedOption ? 'buttonTabSeleccionadaBanco': 'buttonTabBanco'" :color="menu[index].id === SelectedOption ? 'success' : 'primary'" :type="menu[index].id === SelectedOption ? 'line' : 'filled'" @click="OpcionSeleccionada(menu[index])">
                            <div style="display: flex; place-items: center">
                                <font-awesome-icon :icon="['fas', menu[index].icon]" style="font-size: 30px;" />
                                <span class="ml-2" style="font-size: 16px;">{{menu[index].title}}</span>
                            </div>
                        </vs-button>
                    </vs-tooltip>
                </template>
            </DxTabs>
        </div>
        <vx-card :title="Title">

            <div v-if="SelectedOption === 1 ">
                <Mantenimientos />
            </div>
            <div v-if="SelectedOption == 2">
                <Cheques :Corporativo="Corporativo" />
            </div>
            <div v-if="SelectedOption == 3">
                <Transferencias :Corporativo="Corporativo" />
            </div>
            <div v-if="SelectedOption == 4">
                <Transacciones />
            </div>
            <div v-if="SelectedOption == 5">
                <Reportes />
            </div>
        </vx-card>
    </div>
</div>
</template>

<script>
import Mantenimientos from './Mantenimientos.vue';
import Cheques from './Cheques.vue';
import Transferencias from './Transferencias.vue';
import Transacciones from './Transacciones.vue';
import Reportes from './Reportes.vue';

export default {
    name: 'Bancos',
    components: {
        Mantenimientos,
        Cheques,
        Transferencias,
        Transacciones,
        Reportes
    },
    data() {
        return {
            SelectedOption: 1,
            Title: 'Mantenimiento',
            menuStack: [],
            menuActual: [],

            menu: [{
                    id: 1,
                    title: "Mantenimientos",
                    icon: "toolbox",
                },
                {
                    id: 2,
                    title: "Cheques",
                    icon: "money-check-dollar",
                },
                {
                    id: 3,
                    title: "Transferencias",
                    icon: "money-bill-transfer",
                },
                {
                    id: 4,
                    title: "Transacciones",
                    icon: "file-invoice-dollar",
                },
                {
                    id: 5,
                    title: "Reportes",
                    icon: "chart-line",
                },
            ],

            tabsAttributes: {
                class: 'tabMenu'
            },

            Corporativo: null
        }
    },
    methods: {
        OpcionSeleccionada(data) {
            this.SelectedOption = data.id;
            this.Title = data.title;

        },
        RegresarMenu() {
            this.menuStack.pop();
            let i = this.menuStack[this.menuStack.length - 1];

            if (i === 'navigation') {
                this.Title = "Expediente";
                this.SelectedOption = 0;
                this.menuActual = [];
            } else {
                this.Title = i.title
                this.SelectedOption = i.id;
                this.menuActual = [];
                if (i.submenu) {
                    this.menuActual = i.submenu;
                }
            }
        },
    },
    created() {},
    mounted() {
        this.Corporativo = this.sesion.corporativo
    },
    watch: {},
    computed: {
        sesion() {
            return this.$store.state.sesion
        },
    }
}
</script>

<style>
.dx-popup-title {
    background-color: #f4eade !important;
    color: #2f496e !important;
}

.dx-list-item>#itemsMenuDrawer {
    color: #2988bc !important;
    background-color: #f4eade !important;
}

.dx-list-item-selected>#itemsMenuDrawer {
    color: #f4eade !important;
    background-color: #ed8c72 !important;
}

.ContenidoBancos .dx-scrollable-container {
    touch-action: auto !important;
}

.dx-datagrid-headers td {
    vertical-align: middle !important;
}

.dx-resizable {
    display: inline-grid;
}

.ContenidoBancos .dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed)>td {
    background-color: #00ccff !important;
    color: black !important;
}

.ContenidoBancos .dx-datagrid-rowsview .dx-row.dx-data-row.dx-column-lines.dx-state-hover>td {
    background-color: #79e4ff !important;
    color: black !important;
    font-size: 16px;
}

.ContenidoBancos .dx-datagrid-headers {
    background-color: linen !important;
    color: black !important;
    font-weight: bold;
}

.ContenidoBancos td {
    vertical-align: middle !important;
}

.dx-button.dx-button-warning {
    background-color: #e0d100;
}

.buttonTabBanco {
    height: 50px;
    width: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px !important;
}

.buttonTabBanco:hover {
    background-color: blue !important;
}

.buttonTabSeleccionadaBanco {
    height: 50px;
    width: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px !important;
}

.buttonTabSeleccionadaBanco:hover {
    background-color: transparent !important;
}

.stickyBancos {
    position: -webkit-sticky !important;
    /* for browser compatibility */
    position: sticky !important;
    top: 0px;
    z-index: 500;
}

.dx-overlay-wrapper.dx-popup-wrapper.dx-dropdowneditor-overlay.dx-popover-wrapper.dx-popover-without-title.dx-lookup-popup-wrapper.dx-lookup-popup-search.dx-position-bottom {
    z-index: 999999 !important;
}

.buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

.button {
    height: 40px;
    width: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px !important;
}

.button:hover {
    background-color: blue !important;
}

.div-button {
    display: flex;
    justify-content: center;
}

/* .tabMenu .dx-tab.dx-tab-selected{
    background-color: #7cff6b;
} */
</style>
