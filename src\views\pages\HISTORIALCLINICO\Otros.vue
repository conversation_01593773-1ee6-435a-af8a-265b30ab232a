<template>
<div class="p-2">
    <vx-card>
        <vs-row>
            <vs-col>
                <DxForm :ref="formRefKey" :form-data.sync="formulario" labelMode="floating" :read-only="Modulo === 2 ? true : false">
                    <DxFormGroupItem :col-count-by-screen="colCountByScreen">
                        <!-- REFERENCIA -->
                        <DxFormItem item-type="group" caption="Referencia">
                            <DxFormItem data-field="Especialidad" editor-type="dxLookup" :editor-options="{ dataSource: especialidades, displayExpr: 'Nombre', valueExpr:'Codigo', showCancelButton: false, dropDownOptions: dropDownOptions }" />
                            <DxFormItem data-field="MotivoReferencia" editor-type="dxTextArea" :editor-options="{ height: 100 }" />
                        </DxFormItem>

                        <!-- TRATAMIENTO -->
                        <DxFormItem item-type="group" caption="Tratamiento">
                            <DxFormItem data-field="Tratamiento" editor-type="dxTextArea" :editor-options="{ height: 100 }" />
                        </DxFormItem>

                        <!-- LAB. EXT. -->
                        <DxFormItem item-type="group" caption="Laboratorio externo">
                            <DxFormItem data-field="Laboratorio" editor-type="dxTextArea" :editor-options="{ height: 100 }" />
                        </DxFormItem>
                    </DxFormGroupItem>
                </DxForm>
            </vs-col>
        </vs-row>
    </vx-card>
</div>
</template>

<script>
import 'devextreme-vue/lookup'

import 'devextreme-vue/lookup'

const formRefKey = 'form-otros'

export default {
    name: 'Otros',
    components: {},
    data() {
        return {
            formRefKey,
            formulario: {
                Especialidad: null,
                MotivoReferencia: null,
                Tratamiento: null,
                Laboratorio: null,
            },
            dropDownOptions: {
                showTitle: false,
                hideOnOutsideClick: true,
            },
            especialidades: {}
        }
    },
    props: {
        Sexo: null,
        IdCita: null,
        IdCliente: null,
        IdPaciente: null,
        IdConsulta: null,

        Especialidad: null,
        MotivoReferencia: null,
        Tratamiento: null,
        Laboratorio: null,

        Modulo: null, //Variable para saber desde donde está siendo invocado el componente 1 = Historial | 2 = Evoluciones
    },
    methods: {
        CargarEspecialidades() {
            this.axios.post('/app/ajenos/Busqueda_SubEspecialidad', {
                    Codigo: null,
                    Nombre: null
                })
                .then(resp => {
                    this.especialidades = resp.data.json
                })
        },
        LimpiarVariables() {
            this.formulario = {
                Especialidad: null,
                MotivoReferencia: null,
                Tratamiento: null,
                Laboratorio: null,
            }
        },
        GuadarOtros() {
            this.axios.post('/app/v1_Historial_Clinico/ActualizarOtros', {
                    IdHistorialClinico: this.IdConsulta,
                    MotivoReferencia: this.formulario.MotivoReferencia !== null ? this.formulario.MotivoReferencia.replace(/\n/g, '\r\n') : null,
                    Especialidad: this.formulario.Especialidad,
                    Tratamiento: this.formulario.Tratamiento !== null ? this.formulario.Tratamiento.replace(/\n/g, '\r\n') : null,
                    Laboratorio: this.formulario.Laboratorio !== null ? this.formulario.Laboratorio.replace(/\n/g, '\r\n') : null,
                    IdCliente: this.IdCliente,
                })
                .then(() => {
                    // this.BusquedaExamenFisico()
                })
        }
    },
    mounted() {
        this.CargarEspecialidades()
        this.formulario = {
            Especialidad: this.Especialidad,
            MotivoReferencia: this.MotivoReferencia,
            Tratamiento: this.Tratamiento,
            Laboratorio: this.Laboratorio,
        }
    },
    watch: {
        'IdCliente'(newval) {
            if (newval !== '' && newval !== null && newval !== undefined) {
                this.formulario = {
                    Especialidad: this.Especialidad,
                    MotivoReferencia: this.MotivoReferencia,
                    Tratamiento: this.Tratamiento,
                    Laboratorio: this.Laboratorio,
                }
                this.formularioOtros.repaint()
            }
        },
        'IdCita'(newval) {
            if (newval !== '' && newval !== null && newval !== undefined) {
                this.formulario = {
                    Especialidad: this.Especialidad,
                    MotivoReferencia: this.MotivoReferencia,
                    Tratamiento: this.Tratamiento,
                    Laboratorio: this.Laboratorio,
                }
                this.formularioOtros.repaint()
            }
        },
    },
    computed: {
        formularioOtros: function () {
            return this.$refs[formRefKey].instance;
        },
        colCountByScreen() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 2,
                    lg: 3
                };
        },
    },
}
</script>
