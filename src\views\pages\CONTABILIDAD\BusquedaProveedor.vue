<template>
<div>
    <form @submit="handleSubmit" class="mb-4">
        <DxForm :ref="formProveedorBuscar" :form-data.sync="proveedoresBusqueda" label-mode="hidden">
            <DxGroupItem>
                <DxItem data-field="TipoBusqueda" editor-type="dxRadioGroup" :editor-options="{ items: busquedaProveedores, displayExpr:'text', valueExpr: 'id', layout:'horizontal' }">
                    <DxLabel text="" />
                </DxItem>
            </DxGroupItem>
            <DxGroupItem :col-count="2" css-class="buttonsOrdenes">
                <DxItem data-field="ValorBusqueda" editor-type="dxTextBox" />
                <DxButtonItem :button-options="buttonBuscarOrden" horizontal-alignment="center" verical-alignment="center" />
            </DxGroupItem>
        </DxForm>
    </form>

    <DxDataGrid :ref="gridProveedores" v-bind="DefaultDxGridConfiguration" :data-source="proveedores" :headerFilter="{visible:false, allowSearch:false}" :width="'100%'" height="auto">
        <DxDataGridSelection mode="single" />
        <DxDataGridColumn width="auto" data-field="Proveedor" caption="Código" alignment="center" data-type="string" />
        <DxDataGridColumn width="auto" data-field="Nit" caption="Nit proveedor" alignment="center" data-type="string" />
        <DxDataGridColumn width="auto" data-field="Nombre" alignment="center" data-type="string" />
    </DxDataGrid>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'

const formPagos = 'formPagos'
const formProveedorBuscar = 'formProveedorBuscar'
const gridProveedores = 'gridProveedores'

export default {
    components: {},
    data() {
        return {
            DefaultDxGridConfiguration,
            formPagos,
            formProveedorBuscar,
            gridProveedores,

            formulario: {
                Cuenta: null,
                SiguienteCheque: null,
                Funcion: 1,
                TipoAnticipo: null,
                NumeroOrden: null,
                AutorizadoPor: null,
                FechaAutorizacion: null,
                NombreProveedor: null,
                CodigoProveedor: null
            },
            cuentas: [],

            dropDownOptions: {
                showTitle: false,
                hideOnOutsideClick: true,
                // width: '250px'
            },

            funciones: [{
                id: 1,
                text: 'Cheque'
            }, {
                id: 2,
                text: 'Nota de débito'
            }, ],

            tiposAnticipo: [],

            buttonOrdenes: {
                width: 'auto',
                icon: 'fas fa-magnifying-glass',
                text: 'Buscar orden',
                type: 'success',
                onClick: () => {
                    this.mostrarModalOrdenes = true
                },
                useSubmitBehavior: false,
            },

            mostrarModalProveedores: false,
            proveedores: [],
            proveedoresBusqueda: {

            },

            busquedaProveedores: [{
                id: 1,
                text: 'Nombre proveedor'
            }, {
                id: 2,
                text: 'Codigo proveedor'
            }, {
                id: 3,
                text: 'Nit proveedor'
            }, ],

            buttonBuscarOrden: {
                width: 'auto',
                icon: 'fas fa-magnifying-glass',
                text: 'Buscar orden',
                type: 'success',
                useSubmitBehavior: false,
                onClick: () => {
                    // this.BuscarOrdenes()
                }
            },
        }

    },
    props: {

    },
    methods: {
        handleSubmit(e) {
            e.preventDefault()
        },

        CargarCuentas() {
            this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 1
                })
                .then(resp => {
                    this.cuentas = resp.data.json
                })
        },
        BuscarProveedores() {
            this.axios.post(this.$store.state.global.url + 'app/v1_OrdenCompra/consulta_proveedor', {
                    empresa: this.$store.state.sesion
                })
                .then(resp => {
                    this.proveedores = resp.data.json
                })
        },
    },
    mounted() {
        this.CargarCuentas()
    },
}
</script>

<style>

</style>
