<template>
    <div>
        <buscador ref="BuscarLoteFacturar" buscador_titulo="Busqueda de Lotes por Manejo de Cuenta" :api="'app/v1_contabilidad_hospital/BusquedaLote'" 
            :campos="['Trasladofecha', 'Lote', 'Tipofactura', parseFloat('TotalLote').toFixed(2)]"
            :titulos="['#Fecha Traslado', 'No Lote', '#Tipo Lote', '#Total Lote']"
            :api_filtro="{'Lote':Lote}"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" :api_preload="true" />

        <vx-card title="Facturación de lotes por manejo de cuenta">
            <vs-row class="w-full mb-4" vs-justify="center">
                <ConfiguracionCaja
                    @CargarCaja="CargarCaja"
                    :FacturaCambiaria=1
                    TipoDocumento = "F"
                    TipoCajaSeleccionada = "TipoManejoCuenta"
                    CajaSeleccionada = "ManejoCuenta"
                    AgrupacionCaja="CajaManejoCuenta"
                    class="w-full">
                </ConfiguracionCaja>
            </vs-row>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="1">
                    <label class="typo__label">Lote:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-input v-model="Lote" @keyup.enter="BuscarLote()" @keydown.tab="BuscarLote()" @change="BuscarLote()"></vs-input>
                    <vs-button color="primary" icon-pack="fa" icon="fa-search" @click="BuscarLote()"></vs-button>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2" vs-justify="center">
                    <vs-button color="primary" icon-pack="fa" icon="fa-file-invoice" @click="GrabarLotesFacturacion()">Facturar</vs-button>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <!-- <vs-button color="primary" @click="BuscarLote()">Generar</vs-button> -->
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="5">
                    <div class="almacenador w-full">
                        <div class="div-container">
                            <vs-row vs-align="center" vs-justify="center">
                                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                    <vs-radio v-model="RadioLote" vs-name="radios1" vs-value="0">Honorarios</vs-radio>
                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                    <vs-radio v-model="RadioLote" vs-name="radios1" vs-value="1">Cuenta Ajena</vs-radio>
                                </vs-col>
                            </vs-row>
                        </div>
                    </div>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center" vs-justify="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="12">
                    <div ref="tablaProductos" class="w-full">
                        <vs-table2 max-items="10" tooltip pagination search :data="LotesDetalle">
                            <template slot="thead">
                                <th>Código</th>
                                <th>Entidad/Médico</th>
                                <th>Monto Factura</th>
                                <th>Nit</th>
                                <th>Nombre en Factura</th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2 :data="data[indextr].Ajeno">
                                        {{ data[indextr].Ajeno }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Medico">
                                        {{ data[indextr].Medico }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].MontoFac">
                                        {{ parseFloat(data[indextr].MontoFac).toFixed(2) }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].NitFactura">
                                        {{ data[indextr].NitFactura }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].NombreFac">
                                        {{ data[indextr].NombreFac }}
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                </vs-col>
            </vs-row>
        </vx-card>
    </div>
</template>
<script>
    import ConfiguracionCaja from '/src/components/sermesa/modules/caja/ConfiguracionCaja.vue'
    export default {
        components: {
            ConfiguracionCaja
        },
        data() {
            return {
                FechaFactura: '',
                Lote: '',
                RadioLote: 0,
                LotesDetalle:[],
                Categoria: '',
                configuracion:{
                    popUpConfiguracion: false,

                    tiposCajas: [],
                    tipoCajaSeleccionada: null,
                    indexTipoCajaSeleccionada: null,

                    cajaSeleccionada: null,
                    cajasFiltradasPorTipo: [],
                    codigosPermisosCajas: []                        
                },
                caja: {
                    serieFactura:[],
                    serieFacturaFiltrada:[],
                                        
                    tiposFacturacion: [],
                    tipoFacturacionSeleccionada:{}
                }
            }
        },
        methods: {
            BuscarLote(){
                if(this.Lote != ''){
                    this.axios.post('/app/v1_contabilidad_hospital/BusquedaLote', {
                        Lote: this.Lote
                    })
                    .then(resp =>{
                        if(resp.data.json.length == 1){
                            this.Lote = resp.data.json[0].Lote
                            this.RadioLote = resp.data.json[0].EsCuentaAjena == '1' ? 1 : 0
                            this.axios.post('/app/v1_contabilidad_hospital/RecuperaLote', {
                                NoLote: resp.data.json[0].Lote
                            })
                            .then(resp => {
                                this.LotesDetalle = resp.data.json
                            })

                        }else{
                            this.$refs.BuscarLoteFacturar.iniciar((data) => {
                                if(data != null){
                                    this.Lote = data.Lote
                                    this.RadioLote = data.EsCuentaAjena == '1' ? 1 : 0
                                    this.axios.post('/app/v1_contabilidad_hospital/RecuperaLote', {
                                        NoLote: data.Lote
                                    })
                                    .then(resp => {
                                        this.LotesDetalle = resp.data.json
                                    })
                                }
                            })
                        }
                    })
                }else{
                    this.$refs.BuscarLoteFacturar.iniciar((data) => {
                        if(data != null){
                            this.Lote = data.Lote
                            this.RadioLote = data.EsCuentaAjena == '1' ? 1 : 0
                            this.axios.post('/app/v1_contabilidad_hospital/RecuperaLote', {
                                NoLote: data.Lote
                            })
                            .then(resp => {
                                this.LotesDetalle = resp.data.json
                            })

                        }
                    })
                }
            },
            GrabarLotesFacturacion(){
                if(this.LotesDetalle == ''){
                    this.$vs.notify({
                        time: 4000,
                        title: 'Contabilidad',
                        color: 'danger',
                        text: 'No hay facturas pendientes de emitir.',
                        position: 'top-center'
                    })
                }else{
                    this.$vs.dialog({
                        type: 'confirm',
                        color: 'primary',
                        title: 'Confirmación',
                        text: `Este proceso emite las facturas por manejo de cuenta, Desea continuar?`,
                        acceptText: 'Aceptar',
                        cancelText: 'Cancelar',
                        buttonCancel: 'border',
                        accept: () => {
                            this.axios.post('/app/v1_contabilidad_hospital/GrabarLoteAjeno', {
                                FechaFactura: this.FechaFactura,
                                SerieFactura: this.configuracion.cajaSeleccionada.SerieFac,
                                CajaFactura: this.configuracion.cajaSeleccionada.CajaLocal,
                                Categoria: this.Categoria,
                                Datos: JSON.stringify(this.LotesDetalle)
                            })
                            .then(resp => {
                                if(resp.data.codigo == 0){
                                    let resultado = resp.data.resultado.split(',')
                                    for(let NumFactura  of resultado){
                                        this.axios.post('/app/v1_FacturaElectronica/GeneraFel', {
                                            serieFactura: this.configuracion.cajaSeleccionada.SerieFac,
                                            numeroFactura: NumFactura
                                        })
                                        .then(resp => {
                                            if(resp.data.codigo == 0){
                                                this.GenerarReporteFactura(this.configuracion.cajaSeleccionada.SerieFac, NumFactura)
                                            }
                                        })
                                    }
                                    this.Lote = ''
                                    this.LotesDetalle = []
                                }
                            })
                        }
                    })
                    
                }
            },
            GenerarReporteFactura(SerieFactura, Factura){
                this.$reporte_modal({
                    Nombre: "Impresion FEL",
                    Opciones: {
                        SerieFactura: SerieFactura,
                        NumFactura: Factura
                    },
                    Formato: "PDF",
                    Imprimir: true,
                    Descargar: true,
                    NombreArchivoDescargar: SerieFactura + '-' + Factura
                })

            },
            CargarCaja(cajaSeleccionada){                
                this.configuracion.cajaSeleccionada   = cajaSeleccionada.Cajero
                this.configuracion.activarFacturacion = cajaSeleccionada.Factura
            }
        },
        mounted(){
            this.FechaFactura = new Date().toISOString().slice(0, 10);
            for(let privilegio of this.$store.state.privilegios){
                if(privilegio.Privilegio.includes("CATEGORIA_DEFAULT")){
                    if(this.Categoria != ''){
                        this.Categoria += `,${privilegio.Privilegio.split('_')[2]}`
                    }else{
                        this.Categoria = privilegio.Privilegio.split('_')[2]
                    }
                    // this.ModificarBodega = this.$validar_privilegio('BODEGA_DEFAULT_' + this.Categoria).status
                }
            }
        }
    }
</script>
<style scoped>
    .almacenador{
        border: 1px solid #888;

    }
    .div-container{
        padding: 5px;
        margin: 10px;
    }
    .panel-cajero-elegir{
        background-color: rgba(var(--vs-danger), 1) !important; 
    }
    .panel-cajero {
        display:inline-block;
        border-radius: 25px;
        background-color: rgba(var(--vs-primary), 1);
        color: white;
        padding: 5px 65px 5px 10px;
        position: relative;
        width: auto;
        /* top: 12px; */
        overflow: hidden;
        /* margin-right: -14px; */
        font-size: 13px;
        /* right: 50px; */
        /* border-radius: 30px 0 0 0; */
        /* box-shadow: inset 0 2px 0px 1px rgba(0, 0, 0, 0.2); */
        cursor: pointer;
    }

</style>