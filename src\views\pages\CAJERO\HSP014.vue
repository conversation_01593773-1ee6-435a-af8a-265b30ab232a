<template>
<div>
    <vx-card :title="`Facturación Distribuida ${sesion.sesion_sucursal_nombre}`">   
        <vs-row class="w-full">
            <ConfiguracionCaja ref="ConfiguracionCajaRef"
                            @CargarCaja="CargarCaja" :FiltrarAgrupacionCaja=FiltrarAgrupacionCaja
                            TipoCajaSeleccionada="TipoCajaFacturacion" CajaSeleccionada="CajaFacturacion" 
                            class="w-full">
            </ConfiguracionCaja>
        </vs-row>
        <vs-row class="w-full">
            <vs-col class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12">               
                <BusquedaAdmision  class="w-full"  ref="componenteAdmisionesF"   
                        @datos_admision="DatosAdmision"
                        @limpiar_datos_admision="LimpiarDatosClienteAdmision"
                        size = "media-fila"
                        :api_campos="['Serie','Codigo','Nombres','Apellidos','Habitacion']"
                        :api_titulos="['Serie#','Admision#','Nombres','Apellidos','Habitación']"
                        :api_filtro="{'Opcion':'CONSULTA','SubOpcion':'CAJERO','activo':0,'tieneHabitacion':0}"
                        >                
                </BusquedaAdmision>   
            </vs-col>
            <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 p-1">               
                <vs-input label="Porcentaje Primera Factura"  
                          type="number"
                          style="width: 200px;"
                          v-model="datosFactura.porcentajePrimerFactura"
                          @keydown.enter="CargarMontosFactura()" @keydown.tab.prevent="CargarMontosFactura()"/>   
            </vs-col>             
        </vs-row>
        <vs-row class="w-full">
            <vs-col class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12">               
                <vs-col class="flex sm:w-full md:w-full lg:w-6/12 xl:w-6/12 p-1" style="align-items: center;">
                    <label class="label-total">Cuenta de Hospital:</label> 
                    <label class="label-total"><b>{{ $formato_moneda(datosFactura.cuentaHospital) }}</b></label>
                </vs-col>                       
                <vs-col class="flex sm:w-full md:w-full lg:w-6/12 xl:w-6/12 p-1" style="align-items: center;">
                    <label class="label-total">Descuento:</label> 
                    <label class="label-total"><b>{{ $formato_moneda(datosFactura.descuento) }}</b></label>
                </vs-col>             
            </vs-col> 
            <vs-col class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12">             
                <vs-col class="flex sm:w-full md:w-full lg:w-6/12 xl:w-6/12 p-1" style="align-items: center;">
                    <label class="label-total">Valor Factura 1:</label> &nbsp;
                    <label class="label-total"><b>{{ $formato_moneda(datosFactura.valorFactura1) }}</b></label>
                </vs-col>        
                <vs-col class="flex sm:w-full md:w-full lg:w-6/12 xl:w-6/12 p-1" style="align-items: center;">
                    <label class="label-total">Valor Factura 2:</label> &nbsp;
                    <label class="label-total"><b>{{ $formato_moneda(datosFactura.valorFactura2) }}</b></label>
                </vs-col> 
            </vs-col>
        </vs-row>
        <vs-row class="w-full">           
            <vs-col class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12 p-3">
                <vs-row class="w-full p-4" style="background-color:#eee;border:1px solid #ccc;border-radius: 5px;">
                    <div class="w-full" style="text-align: left;">
                        <h5 class="p-1">Datos encabezado Primera Factura</h5>
                    </div>
                    <vs-row class="w-full">
                        <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 p-1" style="height: 60px;">   
                            <label class="typo__label label-shrink" style="white-space: nowrap; color:#626262; font-size:12px;  padding-bottom:2px; padding-top:0px;"> Tipo Documento  </label>  
                            <DxSelectBox            
                                style="padding: 2px;"
                                :items="documento.tiposDocumentoLista"
                                display-expr="Descripcion"
                                placeholder="Seleccionar.."
                                v-model="documento.tipoDocumento1"
                            />    
                        </vs-col>              
                        <vs-col class="sm:w-full md:w-full lg:w-8/12 xl:w-4/12 p-1">
                            <vs-input label="No. documento" 
                                    v-model="documento.noDocumentoFactura1" 
                                    class="w-full"
                                    ref="NoDocumentoF1"
                                    @keydown.enter="ValidacionDocumentoTributarioTab1()" @keydown.tab.prevent="ValidacionDocumentoTributarioTab1()"
                                    />      
                        </vs-col>  
                        <vs-col class="sm:w-full md:w-full lg:w-full xl:w-6/12 p-1">
                            <vs-input id="idNombre1"
                                    label="Nombre" 
                                    ref="nombreF1" 
                                    class="w-full"
                                    v-model="documento.nombreFactura1" />      
                        </vs-col>  
                    </vs-row>    
                    <vs-row class="w-full">
                        <vs-col class="sm:w-full md:w-full lg:w-full xl:w-2/12 p-1"></vs-col>
                        <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-4/12 p-1">
                            <vs-input label="E-Mail"   
                                    class="w-full" 
                                    v-model="documento.correoFactura1" />      
                        </vs-col>
                        <vs-col class="sm:w-full md:w-full lg:w-8/12 xl:w-6/12 p-1">
                            <vs-input label="Dirección"   
                                    class="w-full" 
                                    v-model="documento.direccionFactura1" />      
                        </vs-col>
                    </vs-row>
                </vs-row>                
            </vs-col>                              
            <vs-col class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12 p-3">
                <vs-row class="w-full p-4" style="background-color:#eee;border:1px solid #ccc;border-radius: 5px;">
                    <div class="w-full" style="text-align: left;">
                        <h5 class="p-1">Datos encabezado Segunda Factura</h5>
                    </div>
                    <vs-row class="w-full">                    
                        <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 p-1" style="height: 60px;">   
                            <label class="typo__label label-shrink" style="white-space: nowrap; color:#626262; font-size:12px;  padding-bottom:2px; padding-top:0px;"> Tipo Documento  </label>  
                            <DxSelectBox            
                                style="padding: 2px;"
                                :items="documento.tiposDocumentoLista"
                                display-expr="Descripcion"
                                placeholder="Seleccionar.."
                                v-model="documento.tipoDocumento2"
                            />    
                        </vs-col>              
                        <vs-col class="sm:w-full md:w-full lg:w-8/12 xl:w-4/12 p-1">
                            <vs-input label="No. documento" 
                                    v-model="documento.noDocumentoFactura2" 
                                    class="w-full"
                                    @keydown.enter="ValidacionDocumentoTributarioTab2()" @keydown.tab.prevent="ValidacionDocumentoTributarioTab2()"
                                    />      
                        </vs-col>  
                        <vs-col class="sm:w-full md:w-full lg:w-full xl:w-6/12 p-1">
                            <vs-input id="idNombre2"
                                    label="Nombre"  
                                    ref="nombreF2" 
                                    class="w-full"
                                    v-model="documento.nombreFactura2" />      
                        </vs-col>  
                    </vs-row>    
                    <vs-row class="w-full">
                        <vs-col class="sm:w-full md:w-full lg:w-full xl:w-2/12 p-1"></vs-col>
                        <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-4/12 p-1">
                            <vs-input label="E-Mail"   
                                    class="w-full" 
                                    v-model="documento.correoFactura2" />      
                        </vs-col>
                        <vs-col class="sm:w-full md:w-full lg:w-8/12 xl:w-6/12 p-1">
                            <vs-input label="Dirección"   
                                    class="w-full" 
                                    v-model="documento.direccionFactura2" />      
                        </vs-col>
                    </vs-row>
                </vs-row>
            </vs-col> 
        </vs-row>
        <vs-row class = 'w-full pt-5'>
            <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 m-1">
                <vs-button color="success" class="w-full"
                    id="botonFactura"
                    :disabled="!configuracion.activarFacturacion"
                    :type="facturaSeleccionado?'border':'filled'"                         
                    @keyup.tab="facturaSeleccionado=true"
                    v-on:blur="facturaSeleccionado=false"
                    @click="FacturacionDistribuida()">
                    Facturar
                </vs-button>
            </vs-col>
            <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 m-1">
                <vs-button color="warning" class="w-full"
                    @click="LimpiarDatosCliente()">
                    Limpiar
                </vs-button>
            </vs-col>
        </vs-row> 
    </vx-card>
</div>
</template>
<script>  
    import BusquedaAdmision from "/src/components/sermesa/modules/admisiones/BusquedaAdmision.vue";    
    import ConfiguracionCaja from "/src/components/sermesa/modules/caja/ConfiguracionCaja.vue"; 
    import { ref } from 'vue'
    export default {
        components:{
            BusquedaAdmision,
            ConfiguracionCaja          
        },
        data(){
            return {
                facturaSeleccionado:false,
                FiltrarAgrupacionCaja:['1','2'],   
                configuracion:{
                    cajaSeleccionada:null,
                    activarFacturacion: false,
                    activarRecibos: false  
                },
                info:{  serie:null,
                        numeroAdmision:null,
                        paciente: null,
                        habitacion: null,                         
                        tipoDescuento:null,
                        seguro:null,
                        nombreSeguro:null,
                        copago:null,
                        coaseguro:null,
                        dataAdmision:null
                },
                datosFactura:{
                    porcentajePrimerFactura:null,
                    cuentaHospital:null,
                    descuento:null,
                    valorFactura1:null,
                    valorFactura2:null,
                },
                documento:{ tiposDocumentoLista:[],

                            tipoDocumento1:null,
                            noDocumentoFactura1:null,
                            nombreFactura1:null,
                            correoFactura1:null,
                            direccionFactura1:null,
                            validacionFactura1:null,
                            
                            tipoDocumento2:null,
                            noDocumentoFactura2:null,
                            nombreFactura2:null,
                            correoFactura2:null,
                            direccionFactura2:null,
                            validacionFactura2:null
                }
            }
        },
        methods:{
            actualizarCajero(){  
                this.$refs.ConfiguracionCajaRef.CargarCajaInicial();
            },
            Init(){
                this.CargarTipoDocumento()
            },
            CargarMontosFactura(ObtenerCuentaHospital=false){
                if(!this.ValidarVariable(this.info.serie) || !this.ValidarVariable(this.info.numeroAdmision)){
                    this.MostrarError('Ingrese una admisión valida.',3000)
                    return
                }

                this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision',
                                    {opcion:'C',
                                     subOpcion:7,
                                     serieAdmision:this.info.serie,
                                     numeroAdmision:this.info.numeroAdmision,
                                     serieFactura:this.configuracion.cajaSeleccionada.SerieFac,
                                     porcentajePrimerFactura:ObtenerCuentaHospital?100.00:this.datosFactura.porcentajePrimerFactura
                                    }
                                )
                              .then(resp=>{
                                    let cuentaHospital = resp.data.json[0].CuentaHospital
                                    if(cuentaHospital && this.ConvertirFloat(cuentaHospital) > 0 ){   
                                        if(ObtenerCuentaHospital){
                                            this.datosFactura.cuentaHospital= this.ConvertirFloat(resp.data.json[0].CuentaHospital)
                                            this.datosFactura.descuento=this.ConvertirFloat(resp.data.json[0].TotalDescuento)
                                        }else{
                                            this.datosFactura.cuentaHospital=this.ConvertirFloat(resp.data.json[0].CuentaHospital)
                                            this.datosFactura.descuento=this.ConvertirFloat(resp.data.json[0].TotalDescuento)
                                            this.datosFactura.valorFactura1=this.ConvertirFloat(resp.data.json[0].TotalFactura1)
                                            this.datosFactura.valorFactura2=this.ConvertirFloat(resp.data.json[0].TotalFactura2)
                                            this.$refs.NoDocumentoF1.focusInput()
                                        }                                
                                        
                                    }else if(resp.data.json[0].tipo_error && resp.data.json[0].tipo_error < 0){
                                        this.$vs.dialog({
                                                            type: 'confirm',
                                                            color: 'danger',
                                                            title: 'Facturación Distribuida',
                                                            acceptText: 'Aceptar',
                                                            cancelText: 'Cancelar',
                                                            text: resp.data.json[0].descripcion ,   
                                                            accept: ()=>{this.LimpiarDatosCliente();},                                           
                                                            cancel: ()=>{this.LimpiarDatosCliente();}                                          
                                                        }) 
                                    }
                              })
            },
            Fel(serieFactura,numeroFactura){
                this.axios.post('/app/v1_FacturaElectronica/GeneraFel',
                                {serieFactura: serieFactura,
                                numeroFactura: numeroFactura
                                })                                  
                            .then(resp=>{
                                    if(resp.data.codigo == 0 && resp.data.json[0].codigo == 0){
                                        this.MostrarSatisfactorio(resp.data.json[0].descripcion);
                                        this.GenerarFactura(serieFactura,numeroFactura);                                                                               
                                    }else{
                                        this.MostrarError(resp.data.json[0].descripcion);
                                    }
                                })                                 
            },
            async GenerarFactura(Serie,NumeroFactura) {
                let reporte = "Impresion FEL"

                let postData = {
                    SerieFactura: Serie ,
                    NumFactura: NumeroFactura,
                    nombrereporte: this.configuracion.cajaSeleccionada.FormatoImpresion
                }


                this.$reporte_modal({
                    Nombre: reporte,
                    Opciones: {
                        ...postData
                    },
                    AbrirTab:true,
                    Imprimir:false
                })                

            },      
            FacturacionDistribuida(){
                if(!this.ValidarVariable(this.info.serie) || !this.ValidarVariable(this.info.numeroAdmision)){
                    this.MostrarError('Ingrese una admisión valida.',3000)
                    return
                }

                if(!this.ValidarVariable(this.documento.validacionFactura1)){
                    this.Consulta().MostrarError('Ingresar un documento valido para realizar la primera factura',4000)
                    return
                }

                if(!this.ValidarVariable(this.documento.validacionFactura2)){
                    this.Consulta().MostrarError('Ingresar un documento valido para realizar la segunda factura',4000)
                    return
                }

                const botonFactura = document.getElementById("botonFactura");
                if(botonFactura) botonFactura.blur();

                this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision',
                                    {opcion:'I',
                                     subOpcion:5,
                                     serieAdmision:this.info.serie,
                                     numeroAdmision:this.info.numeroAdmision,
                                     serieFactura:this.configuracion.cajaSeleccionada.SerieFac,
                                     porcentajePrimerFactura:this.datosFactura.porcentajePrimerFactura,

                                     nombreFactura  :this.documento.nombreFactura1,
                                     tipoReceptor   :this.documento.tipoDocumento1.Valor,
                                     documento      :this.documento.noDocumentoFactura1,
                                     direccion      :this.documento.direccionFactura1,
                                     correo         :this.documento.correoFactura1,

                                     nombreFactura2  :this.documento.nombreFactura2,
                                     tipoReceptor2   :this.documento.tipoDocumento2.Valor,
                                     documento2      :this.documento.noDocumentoFactura2,
                                     direccion2      :this.documento.direccionFactura2,
                                     correo2         :this.documento.correoFactura2,
                                    }
                                )                                
                              .then(resp=>{
                                if(resp.data.codigo == 0){
                                    this.Fel(resp.data.Serie,resp.data.Factura1);
                                    this.Fel(resp.data.Serie,resp.data.Factura2);
                                
                                    this.$vs.dialog({
                                                    type: 'confirm',
                                                    color: 'success',
                                                    title: 'Facturación Distribuida',
                                                    acceptText: 'Aceptar',
                                                    cancelText: 'Cancelar',
                                                    text: resp.data.Serie +'-'+resp.data.Factura1+' / '+resp.data.Serie +'-'+resp.data.Factura2,                                               
                                                    accept: ()=>{this.LimpiarDatosCliente();},                                           
                                                    cancel: ()=>{this.LimpiarDatosCliente();}  
                                                }) 
                                }                               
                              })
            },
            CargarTipoDocumento(){
                this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision', {
                                    opcion:'C',
                                    subOpcion:2,
                                    agrupacion:'TipoReceptor'
                                })
                          .then(resp => {                            
                                    if (resp.data && resp.data.json.length > 0){
                                        this.documento.tiposDocumentoLista = resp.data.json;
                                    }
                                }
                               )
            },
            DatosAdmision(datos){
                this.info.dataAdmision = datos
                this.info.serie = datos.Serie
                this.info.numeroAdmision = datos.Codigo
                this.info.paciente = datos.Paciente
                this.info.habitacion = datos.Habitacion 
                this.info.tipoDescuento = datos.TipoDescuento
                this.info.seguro = datos.Seguro
                this.info.nombreSeguro = datos.Nombre

                this.documento.tipoDocumento1 = ref(this.documento.tiposDocumentoLista.find(doc=>doc.Descripcion == 'NIT'))                
                this.documento.noDocumentoFactura1 = datos.Nit
                this.documento.nombreFactura1 = datos.NombreFactura
                this.documento.direccionFactura1 = datos.DireccionFactura

                this.documento.tipoDocumento2 = ref(this.documento.tiposDocumentoLista.find(doc=>doc.Descripcion == 'NIT'))                
                this.documento.noDocumentoFactura2 = datos.Nit
                this.documento.nombreFactura2 = datos.NombreFactura
                this.documento.direccionFactura2 = datos.DireccionFactura
                
                this.ValidacionDocumentoTributario1()
                this.ValidacionDocumentoTributario2()
            },  
            CargarCaja(cajaSeleccionada){                
                this.configuracion.cajaSeleccionada   = cajaSeleccionada.Cajero
                this.configuracion.activarFacturacion = cajaSeleccionada.Factura
                this.configuracion.activarRecibos     = cajaSeleccionada.Recibo
            },
            async ValidacionDocumentoTributarioTab1(){
                await this.ValidacionDocumentoTributario1();
                if(this.documento.validacionFactura1 == 1){
                    const campoNombre = document.getElementById("idNombre1");
                    if(campoNombre) campoNombre.focus();
                }
            },
            async ValidacionDocumentoTributarioTab2(){
                await this.ValidacionDocumentoTributario2();
                if(this.documento.validacionFactura2 == 1){
                    const campoNombre = document.getElementById("idNombre2");
                    if(campoNombre) campoNombre.focus();
                }
            },
            async ValidacionDocumentoTributario1(){
                if(this.EsVacia(this.documento.tipoDocumento1)){
                    this.MostrarError('Seleccione un tipo de documento para el cliente');
                    return;
                }
                if(this.EsVacia(this.documento.noDocumentoFactura1)){
                    this.MostrarError('Ingrese el NIT/DPI del cliente');
                    return;
                }
                await
                this.axios.post('/app/v1_caja/ValidacionDocumentoTributario',{
                                    tipoReceptor: parseInt(this.documento.tipoDocumento1.Valor),
                                    documento: this.documento.noDocumentoFactura1
                                })
                          .then(resp=>{
                                    if ( resp.data.codigo == 0 && resp.data.json.length > 0 && resp.data.json[0].codigo == 0){
                                        this.documento.nombreFactura1 = resp.data.json[0].NOMBRE
                                        this.documento.correoFactura1 = resp.data.json[0].EMAIL
                                        this.documento.direccionFactura1 = resp.data.json[0].DIRECCION
                                        this.documento.validacionFactura1 = 1
                                        this.$refs.nombreF1.focusInput()
                                    }else if ( resp.data.codigo == 0 && resp.data.json.length > 0 && resp.data.json[0].codigo != 0){
                                        this.MostrarError(resp.data.json[0].descripcion);
                                        this.documento.nombreFactura1 = null
                                        this.documento.correoFactura1 = null
                                        this.documento.direccionFactura1 = null
                                        this.documento.validacionFactura1 = null
                                    }
                                    else if(resp.data.json.length == 0){
                                        this.documento.nombreFactura1 = null
                                        this.documento.correoFactura1 = null
                                        this.documento.direccionFactura1 = null
                                        this.documento.validacionFactura1 = null
                                        this.MostrarError('No se encontraron datos para el documento ingresado');
                                    }
                                })
                            
            },
            async ValidacionDocumentoTributario2(){
                if(this.EsVacia(this.documento.tipoDocumento2)){
                    this.MostrarError('Seleccione un tipo de documento para el cliente');
                    return;
                }
                if(this.EsVacia(this.documento.noDocumentoFactura2)){
                    this.MostrarError('Ingrese el NIT/DPI del cliente');
                    return;
                }
                await
                this.axios.post('/app/v1_caja/ValidacionDocumentoTributario',{
                                    tipoReceptor: parseInt(this.documento.tipoDocumento2.Valor),
                                    documento: this.documento.noDocumentoFactura2
                                })
                          .then(resp=>{
                                    if ( resp.data.codigo == 0 && resp.data.json.length > 0 && resp.data.json[0].codigo == 0){
                                        this.documento.nombreFactura2 = resp.data.json[0].NOMBRE
                                        this.documento.correoFactura2 = resp.data.json[0].EMAIL
                                        this.documento.direccionFactura2 = resp.data.json[0].DIRECCION
                                        this.documento.validacionFactura2 = 1
                                        this.$refs.nombreF2.focusInput()
                                    }else if ( resp.data.codigo == 0 && resp.data.json.length > 0 && resp.data.json[0].codigo != 0){
                                        this.MostrarError(resp.data.json[0].descripcion);
                                        this.documento.nombreFactura2 = null
                                        this.documento.correoFactura2 = null
                                        this.documento.direccionFactura2 = null
                                        this.documento.validacionFactura2 = null
                                    }
                                    else if(resp.data.json.length == 0){
                                        this.documento.nombreFactura2 = null
                                        this.documento.correoFactura2 = null
                                        this.documento.direccionFactura2 = null
                                        this.documento.validacionFactura2 = null
                                        this.MostrarError('No se encontraron datos para el documento ingresado');
                                    }
                                })
                            
            },
            ConvertirFloat(Valor,Decimales=2){
                let conversion = parseFloat(Valor?Valor:0.00)
                if(isNaN(conversion)){
                    return 0.00
                }
                return parseFloat(conversion.toFixed(Decimales))
            }, 
            MostrarError(mensaje,tiempo=2000){
                this.$vs.notify({
                    time:tiempo,
                    title: 'Facturación Parcial',
                    color: 'danger',
                    text:mensaje,
                    position: 'top-center'
                })
            },
            MostrarSatisfactorio(mensaje){
                this.$vs.notify({
                    time:2000,
                    title: 'Facturación Parcial',
                    color: 'success',
                    text:mensaje,
                    position: 'top-center'
                })
            },
            EsVacia:(variable)=>{
                return variable?false:true;
            },
            ValidarVariable(entrada){   
                if(typeof(entrada) == 'number'){
                    return entrada ? true : false;
                }else if(typeof(entrada) == 'string'){
                    return entrada && entrada.length > 0 ? true : false;
                }else if(typeof(entrada) == 'object'){
                    return entrada ? true : false;
                }            
                return false;
            },
            LimpiarDatosCliente(){
                this.$refs.componenteAdmisionesF.limpiar_campos()
            },
            LimpiarDatosClienteAdmision(){
                this.info.serie=null
                this.info.numeroAdmision=null
                this.info.paciente= null
                this.info.habitacion= null                        
                this.info.tipoDescuento=null
                this.info.seguro=null
                this.info.nombreSeguro=null
                this.info.copago=null
                this.info.coaseguro=null
                this.info.dataAdmision=null
                
                this.datosFactura.porcentajePrimerFactura=null
                this.datosFactura.cuentaHospital=null
                this.datosFactura.descuento=null
                this.datosFactura.valorFactura1=null
                this.datosFactura.valorFactura2=null
                
                this.documento.tipoDocumento1=null
                this.documento.noDocumentoFactura1=null
                this.documento.nombreFactura1=null
                this.documento.correoFactura1=null
                this.documento.direccionFactura1=null
                this.documento.validacionFactura1=null		
                
                this.documento.tipoDocumento2=null
                this.documento.noDocumentoFactura2=null
                this.documento.nombreFactura2=null
                this.documento.correoFactura2=null
                this.documento.direccionFactura2=null
                this.documento.validacionFactura2=null
            }
        },
        computed:{
            sesion() {
                return this.$store.state.sesion
            }
        },
        mounted(){
            this.Init()
        },
        activated() {
            window.addEventListener('focus', this.actualizarCajero);   
        },
        deactivated() {
            window.removeEventListener('focus',this.actualizarCajero);
        },
    }
</script>
<style>
.label-total
{
    word-wrap: break-word; 
    white-space: wrap;
    font-size: 20px;
    color:black;    
}
.vs-row-especial{
    display: block !important;
}
/* Estilo tablas dev express */
.dx-popup-title {
    background-color: #f4eade !important;
    color: #2f496e !important;
}

.dx-list-item>#itemsMenuDrawer {
    color: #2988bc !important;
    background-color: #f4eade !important;
}

.dx-list-item-selected>#itemsMenuDrawer {
    color: #f4eade !important;
    background-color: #ed8c72 !important;
}

.dx-scrollable-container {
    touch-action: auto !important;
}

/*Ancho mínmo de los grid*/
#Contenido .dx-datagrid {
    min-width: 302px;
}

/**Modal actualizacion de peso y talla */
#popupTallaPeso .vs-popup {
    width: 400px !important;
}

.dx-datagrid-headers td {
    vertical-align: middle !important;
}

.dx-resizable {
    display: inline-grid;
}

.dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed)>td {
    background-color: #00ccff !important;
    color: black !important;
}

.dx-datagrid-rowsview .dx-row.dx-data-row.dx-column-lines.dx-state-hover>td {
    background-color: #79e4ff !important;
    color: black !important;
    font-size: 16px;
}

.dx-datagrid-headers {
    background-color: linen !important;
    color: black !important;
    font-weight: bold;
}

td {
    vertical-align: middle !important;
}
/* Fin Estilo tablas dev express */
</style>