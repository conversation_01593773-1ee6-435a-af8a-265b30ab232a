<template>
    <div>
        <vs-button clientWidth="20" icon-pack="fas" :icon="icono" @click="(!disabled) ? Otros().visible(true) : null"
            :disabled="disabled">{{ texto }}</vs-button>

        <vs-popup ref="buscador" title="Archivos" :active.sync="info.visible" style="z-index:99999" id="div-with-loading"
            class="vs-con-loading__container">
            <div class=" archivos">
                <vs-tabs style="height:calc(100%- 150px)" v-model="info.tab">
                    <vs-tab label="Subir Archivo" v-if="subir_archivo">
                        <!--------------------------Adjuntar y eliminar firma---------------------------->
                        <div style="margin: 2px" class="flex flex-wrap">
                            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                                <input :accept="extensiones" id="fileInput1" style="display: none;" type="file"
                                    @change="Guardar().archivo($event)" />
                                <vs-button class="icon-camara" onclick="document.getElementById('fileInput1').click()" icon-pack="fas" icon="fa-camera-retro">Adjuntar</vs-button>
                            </div>
                        </div>
                        <br>
                        <div class="detalle flex">
                            <canvas ref="png" width="200px" height="200px" style="border:1px solid #ccc;display:none">
                                <vs-divider position="left">Archivo requerido</vs-divider>
                                <ul>
                                    <li>Tamaño: 8cm x 4cm</li>
                                    <li>Formato: JPEG, PNG y JPG</li>
                                </ul>
                            </canvas>
                            <!-- {{archivos}}estaba comentada -->

                            <div class="row" v-if="imagen_src.length > 0" style="width:100%">
                                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-2 imagen" v-for="(i, index) in archivos"
                                    :key="index" style="padding:10px;width:100%;text-align:center">
                                    <img :src="'data:image/png;base64,' + i.src" alt="">
                                </div>
                            </div>
                            <div v-else style="padding:10px;width:30%;">
                                <img src="@/assets/images/others/notfound.png" alt=""
                                    style="max-width:100%;max-height:100%">
                            </div>
                        </div>
                    </vs-tab>
                </vs-tabs>

            </div>
        </vs-popup>
    </div>
</template>
    
<script>
/**
 * Consulta: Necesita 3 campos <Cualquier campo identificador del archivo>, <src> base 64, <tipo> imgen/otros
 */
import SignaturePad from 'signature_pad'


export default {
    name: 'SMHorario',
    data() {
        return {
            info: {
                visible: false,
                imagen: null,
                signaturePad: null,
                tab: 0
            },
            archivos: [],
            imagen_src: ''
        }
    },
    props: {

        disabled: {
            default: false
        },
        texto: {
            default: 'Adjuntos'
        },
        icono: {
            default: 'fa-paperclip'
        },
        extensiones: {
            default: '.png, .jpg, .jpeg*'
        },
        /**
         * Indica si se cargaran multiples archivos
         */
        multiples_archivos: {
            default: false
        },
        /**
         * Se habilita la opción de subir archivos
         */
        subir_archivo: {
            default: true
        },
        /**
         * Se habilita la opción para subir firma
         */
        horario: {
            default: false
        },
        /**
         * Indica a que url se enviara la consulta y el guardado de archivos
         */
        api: {
            default: null
        },
        /**
         * Se agrega los otros criterios del api
         */
        api_parametros: {
            default: null
        },
        /**
         * Resize de imagen
         */
        imagen_resize: {
            default: 0
        }
    },
    watch: {
        'info.visible'(value) {
            if (this.$refs.horario) this.signaturePad.clear() /// estaba comentado
            if (value && this.subir_archivo) this.ConsultaHorario().archivo()
        },
        'info.tab'() {
            setTimeout(() => {
                if (this.$refs.horario) {
                    var canvas = this.$refs.horario;
                    this.signaturePad = new SignaturePad(canvas)
                } /// 4 lineas comentadas
            }, 100)
        }
    },
    methods: {
        /**
         * CONSULTA 
         */
        ConsultaHorario() {
            return {
                archivo: () => {
                    this.axios.post(this.api, {
                        tipo: 'CONSULTA',
                        ...this.api_parametros
                    })
                        .then(resp => {
                            this.archivos = resp.data.json
                            resp.data.json.map(data => {
                                if (data.src != '') {
                                    this.imagen_src = data.src;
                                    this.$emit('metodo_emit', this.imagen_src)
                                } else {
                                    this.imagen_src = '';
                                }
                            });
                        })
                }
            }
        },

        /**
         * GUARDAR
         */
        Guardar() {
            return {
                archivo: (e) => {
                    const file = e.target.files[0]
                    const canvas = this.$refs.png;
                    const ctx = canvas.getContext('2d');
                    var fr = new FileReader();
                    fr.onload = (event) => {
                        var img = new Image();
                        img.onload = () => {
                            const ratio = (this.imagen_resize > 0) ? Math.min(this.imagen_resize / img.width, this.imagen_resize / img.height) : 1;
                            let Imgwidth = img.width * ratio
                            let Imgheight = img.height * ratio

                            canvas.width = Imgwidth;
                            canvas.height = Imgheight;
                            ctx.drawImage(img, 0, 0, Imgwidth, Imgheight);

                            const archivo = canvas.toDataURL("image/png").replace("image/png", "archivo");

                            this.axios.post(this.api, {
                                tipo: 'GUARDAR',
                                ...this.api_parametros,
                                archivo
                            })
                                .then(() => {
                                    this.ConsultaHorario().archivo()
                                })
                        }
                        img.src = event.target.result;
                    }
                    fr.readAsDataURL(file);
                },
                eliminar_firma: () => {
                    const archivo = '';
                    this.axios.post(this.api, {
                        tipo: 'ELIMINAR',
                        ...this.api_parametros,
                        archivo

                    })
                        .then(() => {
                            this.ConsultaHorario().archivo()
                        })
                }
            }
        },
        Otros() {
            return {
                visible: (visible) => {
                    this.info.visible = visible
                },
                b64toBlob: (b64Data, contentType = '', sliceSize = 512) => {
                    const byteCharacters = atob(b64Data);
                    const byteArrays = [];

                    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
                        const slice = byteCharacters.slice(offset, offset + sliceSize);

                        const byteNumbers = new Array(slice.length);
                        for (let i = 0; i < slice.length; i++) {
                            byteNumbers[i] = slice.charCodeAt(i);
                        }

                        const byteArray = new Uint8Array(byteNumbers);
                        byteArrays.push(byteArray);
                    }

                    const blob = new Blob(byteArrays, {
                        type: contentType
                    });
                    return blob;
                }

            }
        }
    },






    /// todo el mounted estaba comentado
    mounted() {
        if (this.api == null) alert('Es necesario especificar el api en SMArchivo')

        if (this.$refs.horario) {
            var canvas = this.$refs.horario;
            this.signaturePad = new SignaturePad(canvas)
        }
    }
}
</script>
    
<style scoped>
.detalle {
    border: 2px dashed #ccc;
    width: 100%;
    min-height: 150px;
}

.firma {
    border: 2px dashed #ccc;
    margin: auto;
    /* width: 100%; */
}

.imagen img {
    border: 1px solid #ccc;
    width: 100%;
    max-height: 150px;
    border-radius: 5px;
    box-shadow: 0 0 0 2px #aaa;
}</style>
    