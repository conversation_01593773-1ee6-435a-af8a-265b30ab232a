<template>
<div>
    <form @submit="handleSubmit">
        <DxForm :visible="true" :form-data.sync="formulario" labelMode="floating" :ref="dataForm">
            <DxFormGroupItem item-type="group">
                <DxFormGroupItem item-type="group" :col-count="2">
                    <DxFormItem data-field="Correo" editor-type="dxTextBox">
                        <DxFormLabel :text="'Correo electrónico'" />
                    </DxFormItem>
                    <DxFormItem data-field="NombreConyugue" editor-type="dxTextBox">
                        <DxFormLabel :text="'Nombre del cónyugue'" />
                    </DxFormItem>
                </DxFormGroupItem>

                <DxFormGroupItem item-type="group" :col-count="2">
                    <DxFormItem data-field="RHPaciente" editor-type="dxSelectBox" :editor-options="{ items: tipoSangre }">
                        <DxFormLabel :text="'Grupo sanguíneo'" />
                    </DxFormItem>
                    <DxFormItem data-field="RHConyugue" editor-type="dxSelectBox" :editor-options="{ items: tipoSangre }">
                        <DxFormLabel :text="'Grupo sanguíneo del cónyugue'" />
                    </DxFormItem>
                </DxFormGroupItem>
                <DxFormGroupItem item-type="group" :col-span="2">
                    <DxFormButtonItem :button-options="submitButtonOptions" horizontal-alignment="center" verical-alignment="center" />
                </DxFormGroupItem>
            </DxFormGroupItem>
        </DxForm>
    </form>
</div>
</template>

<script>
import 'devextreme-vue/lookup';

const dataForm = 'anestesiaForm'

export default {
    name: 'EditarDatosAdicionales',
    components: {},
    data() {
        return {
            dataForm,
            formulario: {
                Correo: null,
                RHPaciente: null,
                NombreConyugue: null,
                RHConyugue: null,
            },
            submitButtonOptions: {
                text: 'Guardar',
                type: 'success',
                icon: 'save',
                width: 200,
                useSubmitBehavior: true,
                disabled: false
            },
            dropDownOptions: {
                showTitle: false,
                hideOnOutsideClick: true,
            },
            tipoSangre: ['O-', 'O+', 'A-', 'A+', 'B-', 'B+', 'AB-', 'AB+'],
            habilitarMedico: false,
        }
    },
    props: {
        Sexo: null,
        IdCita: null,
        IdCliente: null,
        IdPaciente: null,
        IdConsulta: null,
        Correo: null,
        RHPaciente: null,
        NombreConyugue: null,
        RHConyugue: null
    },
    methods: {

        handleSubmit(e) {
            e.preventDefault()
            this.GuardarDatosAdicionales()
        },
        CargarDatos() {
            this.formulario = {
                Correo: this.Correo,
                RHPaciente: this.RHPaciente,
                NombreConyugue: this.NombreConyugue,
                RHConyugue: this.RHConyugue
            }
        },
        GuardarDatosAdicionales() {
            this.axios.post('/app/v1_Historial_Clinico/ActualizarDatosAdicionales', {
                    IdCliente: this.IdCliente,
                    Correo: this.formulario.Correo,
                    RHPaciente: this.formulario.RHPaciente,
                    NombreConyugue: this.formulario.NombreConyugue,
                    RHConyugue: this.formulario.RHConyugue
                })
                .then(resp => {
                    if (resp.data.codigo === 0) {
                        this.$emit('datosAdicionales', this.formulario)
                    }
                })
        }
    },
    mounted() {},
    watch: {
        'IdCliente'(newval) {
            if (newval !== '' && newval !== null && newval !== undefined) {
                this.CargarDatos()
            }
        },
    },
    computed: {},
}
</script>
