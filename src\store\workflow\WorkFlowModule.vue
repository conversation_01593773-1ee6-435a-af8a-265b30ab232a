

<script>

export default {
    namespaced: true,
    state: {
        data: {
            dataStep: []

        },
    }
    ,
    mutations: {
        ADD_STEP_IN_WORKFLOW(state, item) {
            
            state.data.dataStep.dataStep = item
            //console.log(state.dataStep)
        }

    },
    actions: {
        addStepWorkFlow({ commit }, step) {
            commit('ADD_STEP_IN_WORKFLOW', step)
        }
    },
    getters: {

    }
}


</script>