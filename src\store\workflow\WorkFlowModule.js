
import { getField, updateField } from "vuex-map-fields";





const state = {
    wfData: {
          TipoPaso: '',
          Paso: ''
    }
}

const getters = {
    getField,
    // getDescAdmision(state){
    //     const DescripcionAdmision = admisiones.find(obj => obj.id === state.AdmisionData.TipoAdmision)?.name||'';
    //     return DescripcionAdmision
    // },
    getTipoPaso(state) {
        return state.wfData.TipoPaso
    }
}

const mutations = {
    updateField,
    SET_TIPO_PASO(state, TipoPaso){
        state.wfData.TipoPaso = TipoPaso
    }
}


const actions = {
    guardarTipoPaso({ commit }, tipoPaso) {
        commit('SET_TIPO_PASO', tipoPaso)
    },
    

   
}
export default {
    namespaced: true,
    state,
    mutations,
    getters,
    actions
}