<template>
<vx-card title="Departamentos">
    <div class="content content-pagex">
        <vs-divider></vs-divider>
        <div class="terms">
            <div style="margin: 15px" class="flex flex-wrap">
                <vs-button color="primary" style="float:left;margin: 5px" type="filled" icon-pack="fas" icon="fa-plus" @click="Abrir_Ventana_Emergente_Nuevo()"> Nuevo</vs-button>
                <div class=" md:w-1/5 lg:w-1/5 xl:w-1/5, label-size" style="float:left;margin: 15px" align="right">
                    <vs-radio v-model="activoF" vs-name="idActivo" vs-value="S"> Activo</vs-radio>
                </div>
                <div class=" md:w-1/5 lg:w-1/5 xl:w-1/5, label-size" style="float:left;margin: 15px" align="center">
                    <vs-radio v-model="activoF" vs-name="idActivo" vs-value="N"> Inactivo</vs-radio>
                </div>
                <div class=" md:w-1/5 lg:w-1/5 xl:w-1/5, label-size" style="float:left;margin: 15px" align="left">
                    <vs-radio v-model="activoF" vs-name="idActivo" vs-value=""> Todos</vs-radio>
                </div>
                <vs-button color="primary" style="float:left;margin: 5px" type="filled" icon-pack="fas" icon="fa-search" @click="Consultar_Departamentos()"> Buscar Departamentos</vs-button>
            </div>

        </div>

        <div>

            <vs-divider></vs-divider>
            <vs-table2 max-items="10" pagination :data="lista_departamentos" search id="tb_departamentos">

                <template slot="thead">
                    <th width="50">Código</th>
                    <th width="300">Nombre</th>
                    <th width="100">Cuenta Tránsito</th>
                    <th width="100">Sucursal</th>
                    <th width="300">Nombre Sucursal</th>
                    <th width="30">Editar</th>
                    <th width="50">Asignar Categorías</th>
                    <th width="10">Estado</th>
                    <th width="30">Cambio de Estado</th>
                </template>

                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">
                        <vs-td2 :data="data[indextr].CODIGO">
                            {{data[indextr].CODIGO}}
                        </vs-td2>

                        <vs-td2 :data="data[indextr].NOMBRE">
                            {{data[indextr].NOMBRE}}
                        </vs-td2>
                        <vs-td2 :data="data[indextr].CUENTAENTRANSITO">
                            {{data[indextr].CUENTAENTRANSITO}}
                        </vs-td2>
                        <vs-td2 :data="data[indextr].ID_HOSPITAL">
                            {{data[indextr].ID_HOSPITAL}}
                        </vs-td2>
                        <vs-td2 :data="data[indextr].NOMBRE_HOSPITAL">
                            {{data[indextr].NOMBRE_HOSPITAL}}
                        </vs-td2>
                        <vs-td2 align="center">
                            <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" @click="Abrir_Ventana_Emergente_Editar(data[indextr])"></vs-button>
                        </vs-td2>
                        <vs-td2 align="center">
                            <vs-button color="green" icon-pack="far" icon="fa-window-restore" v-if="data[indextr].ACTIVA == 'N'" disabled="true" style="display:inline-block;margin-right:2px" @click="Asignar_Categoria(data[indextr])"></vs-button>
                            <vs-button color="green" icon-pack="far" icon="fa-window-restore" v-else style="display:inline-block;margin-right:2px" @click="Asignar_Categoria(data[indextr])"></vs-button>
                        </vs-td2>
                        <vs-td2 :data="data[indextr].ESTADO_DEPT">
                            {{data[indextr].ESTADO_DEPT==='ACTIVO'? 'ACTIVO':'INACTIVO'}}
                        </vs-td2>
                        <vs-td2 align="center">
                            <vs-switch v-model="data[indextr].ESTADO" :v-value="data[indextr].Estado" @click="Eliminar_Registro(data[indextr])"> </vs-switch>
                        </vs-td2>
                    </tr>
                </template>
            </vs-table2>
        </div>
    </div>

    <!--------------- Nuevo / Editar / Información  Departamento (Ventana Emergente)-------------->
    <vs-popup classContent="popup-example" :title="Titulo_emergente" :active.sync="Estado_VentanaEmergente">
        <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">

            <form>
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="md:w-1/5 lg:w-1/5 xl:w-1/5, label-size">
                        <ValidationProvider name="Código" rules="required|max:2" v-slot="{ errors }" class="required">
                            <label> Código</label>
                            <vs-input v-if="bloquear == true" disabled="true" v-model="Codigo_depto" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                            <vs-input v-if="bloquear == false" v-model="Codigo_depto" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                </div>
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="md:w-2/5 lg:w-2/5 xl:w-2/5, label-size">
                        <ValidationProvider name="Nombre" rules="required|max:40" v-slot="{ errors }" class="required">
                            <label class="label-size"> Nombre</label>
                            <vs-input class="w-full" v-model="Nombre_departamento" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                    <div class=" md:w-1/5 lg:w-1/5 xl:w-1/5"></div>
                    <div style="margin: 15px" class="flex flex-wrap">
                        <div class=" md:w-4/5 lg:w-4/5 xl:w-4/5 , label-size" align="left">
                            <label>Estado - {{Estado}}</label>
                        </div>
                        <div class=" md:w-2/5 lg:w-2/5 xl:w-2/5 , label-size" align="left">
                            <vs-switch v-model="idActivo" :disabled="Deshabilitar_controles"> </vs-switch>
                        </div>

                    </div>
                </div>

                <vs-divider></vs-divider>
                <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" @click="Guardar_departamento()" id="btn_confirmacion"> Grabar</vs-button>
                <vs-divider></vs-divider>
            </form>

        </div>
    </vs-popup>
    <!---------------Fin Ventana Emergente _---------->
    <!--------------- Asignación de Categorias   (Ventana Emergente)-------------->
    <vs-popup classContent="popup-example" :title="Titulo_emergente" :active.sync="Estado_VentanaCategoria">
        <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">

            <form>
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="md:w-1/5 lg:w-1/5 xl:w-1/5">

                        <ValidationProvider name="Código" v-slot="{ errors }" class="required">
                            <label class="label-size"> Código</label>
                            <vs-input class="w-full" v-model="Codigo_depto" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="Deshabilitar_controles" id="txt_codigo" />
                        </ValidationProvider>
                    </div>
                    <div class=" md:w-2/5 2g:w-2/5 xl:w-2/5"></div>
                    <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                        <ValidationProvider name="Nombre_bodega" rules="required|max:40" v-slot="{ errors }" class="required">
                            <label class="label-size"> Nombre</label>
                            <vs-input class="w-full" count="40" v-model="Nombre_departamento" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="Deshabilitar_controles" />
                        </ValidationProvider>
                    </div>
                </div>
                <br>
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                        <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
                            <ValidationProvider name="Operacion">
                                <label class="label-size">Operación</label>
                                <multiselect v-model="cb_lista_operacion" :options="Lista_operacion" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Operacion_seleccionada" placeholder="Seleccionar Operación" @input="onChangeTipo">
                                    <span slot="noOptions">Lista no disponible.</span>
                                </multiselect>
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class=" md:w-1/5 2g:w-1/5 xl:w-1/5"></div>
                    <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                        <ValidationProvider name="Agencia">
                            <label class="label-size">Categorías disponibles</label>
                            <multiselect v-model="id_categoria_selec" :options="Lista_categorias_disponibles" :multiple="true" :close-on-select="false" :clear-on-select="false" :preserve-search="true" placeholder="Seleccionar categoría"  :custom-label="Categoria_seleccionada" label="NOMBRE" track-by="NOMBRE" :preselect-first="true" @input="onChangeCategoria" :disabled="Deshabilitado_categoria">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                        <div class="flex flex-wrap">
                            <br>
                            <div class="w-full">
                                <br>
                                <input labelt="TODOS" type="checkbox" id="checkbox" v-model="Enviar_Todos" :disabled="Estado_Todos" name="lst_check" @change="onChangeCheck()" />
                                <label class="label-size"> Seleccionar todos</label>
                            </div>
                        </div> 
                        <br>   
                            <div class="flex flex-wrap">
                               
                                <div align="left">
                                    <vs-button color="primary"  type="filled" icon-pack="fas" icon="fa-plus" @click.native="Guardar_Asociacion()"> Asignar Categorías</vs-button>
                                </div>
                            </div>               
                    </div>
                </div>
                <div>

                    <vs-divider position="left" class="label-size">CATEGORIAS ASIGNADAS</vs-divider>
                    <vs-table2 max-items="10" pagination :data="Lista_Asignaciones_categoria_depto" search id="tb_asignacion_categoria_depto">
                        <template slot="thead">
                            <th>Departamento</th>
                            <th width="10">Código Categoria</th>
                            <th>Categoria</th>
                            <th>Operación</th>
                            <th></th>
                        </template>

                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">

                                <vs-td2 :data="data[indextr].DEPARTAMENTO">
                                    {{data[indextr].DEPARTAMENTO}}
                                </vs-td2>
                                <vs-td2 :data="data[indextr].CODIGO_CATEGORIA">
                                    {{data[indextr].CODIGO_CATEGORIA}}
                                </vs-td2>
                                <vs-td2 :data="data[indextr].CATEGORIA">
                                    {{data[indextr].CATEGORIA}}
                                </vs-td2>

                                <vs-td2 :data="data[indextr].DESCRIPCION_TIPO">
                                    {{data[indextr].DESCRIPCION_TIPO}}
                                </vs-td2>

                                <vs-td2 width="50px">
                                    <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="Eliminar_RegistroCat(data[indextr])"></vs-button>
                                </vs-td2>
                            </tr>
                        </template>

                    </vs-table2>

                </div>

            </form>
        </div>
    </vs-popup>

</vx-card>
</template>

<script>
/**
 * @General
 * Registro de departamentos
 */
import Multiselect from 'vue-multiselect'
import "vue-multiselect/dist/vue-multiselect.min.css"
export default {

    components: {
        Multiselect
    },
    data() {
        return {

            Titulo_emergente: '',
            Corporativo_Sesion: '',
            res_variables: false, //Var para validación de campos
            Codigo_depto: '',
            Nombre_departamento: '',
            Estado_VentanaEmergente: false,
            Estado_VentanaCategoria: false,
            lista_departamentos: [],
            Operacion: '', // N = Nuevo Registro / A = Actualización registor
            idActivo: '',
            bloquear: false,
            activoF: '',
            Deshabilitar_controles: false,
            Estado_Todos: true,
            Enviar_Todos: false,
            Deshabilitado_categoria: true,
            Estado_VentanaAsignacion: false,
            cb_departamentos: '',
            Lista_departamentos: [],
            id_departamento_selec: '',
            cb_lista_operacion: '',
            Lista_operacion: [{
                'CODIGO': 'R',
                'DESCRIPCION': 'Requerimientos'
            }, {
                'CODIGO': 'C',
                'DESCRIPCION': 'Compras'
            }],
            id_operacion_selec: '',
            Lista_Asignaciones_categoria_depto: [],
            cb_categoria_disponibles: '',
            id_categoria_selec: '',
            Lista_categorias_disponibles: [],
            lista_categorias_select: [],
            Estado: 'ACTIVO',

        };
    },
    mounted() {

    },
    watch: {
        idActivo(value) {
            if (value == true)
                this.Estado = 'ACTIVO'
            else
                this.Estado = 'INACTIVO'
        }
    },
    methods: {
        //Devolver los obejetos seleccionados de los  combobox

        Operacion_seleccionada({
            DESCRIPCION
        }) {
            return `${DESCRIPCION} `
        },
        Categoria_seleccionada({
            CODIGO,
            NOMBRE
        }) {
            return `${CODIGO} - ${NOMBRE} `
        },
        Consultar_Departamentos() {
            const url = this.$store.state.global.url
            this.$vs.loading();
            this.axios.post(url + 'app/v1_ordencompra/consulta_departamento', {
                    tipo_consulta: 'T',
                    activa: this.activoF
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Departamentos',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.lista_departamentos = "";
                    } else {
                        this.lista_departamentos = resp.data.json;
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },
        Abrir_Ventana_Emergente_Nuevo() {
            this.bloquear = false
            this.Titulo_emergente = 'Nuevo Departamento';
            this.Codigo_depto = "";
            this.Nombre_departamento = "";
            this.Estado_VentanaEmergente = true;
            this.Operacion = 'N'; // Indica la variable que es un nuevo registro
            this.idActivo = true
            this.Estado = 'ACTIVA'

        },
        Cerrar_Ventana_Emergente_Nuevo() {
            this.Estado_VentanaEmergente = false;
            this.Consultar_Departamentos();
        },
        Abrir_Ventana_Emergente_Editar(datos_editar) {
            this.bloquear = true
            this.Operacion = 'A'; // Indica la variable que es un nuevo registro
            this.Titulo_emergente = 'Editar Departamento';
            this.Codigo_depto = datos_editar.CODIGO;
            this.Nombre_departamento = datos_editar.NOMBRE;
            this.Deshabilitar_controles = false
            this.Estado = datos_editar.ACTIVA == 'S' ? 'ACTIVO' : 'INACTIVO'
            this.idActivo = datos_editar.ACTIVA == 'S' ? true : false;
            this.Estado_VentanaEmergente = true;

        },
        Cerrar_Ventana_Emergente_Editar() {
            this.departamento_atributos.d_estado_ventana_emergente = false;

        },

        Guardar_departamento() {
            /**
             * @General
             * Función Permite  actualizar y  almacenar un nuevo registro;
             */
            this.res_variables = false;
            //Condicionante si es Nuevo registro ó Actualización

            //    Validaciones de variables
            this.res_variables = this.Validacion_Campos('A', 'Código', this.Codigo_depto, true);
            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('A', 'Nombre', this.Nombre_departamento, true);

            if (this.res_variables) {
                const url = this.$store.state.global.url
                this.$vs.loading();
                this.axios.post(url + 'app/v1_ordencompra/departamento', {
                        Codigo: this.Codigo_depto,
                        Nombre: this.Nombre_departamento,
                        activa: this.idActivo == true ? 'S' : 'N',
                        tipo_consulta: this.Operacion
                    })
                    .then(resp => {
                        this.$vs.loading.close();
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Departamento',
                                text: resp.data.mensaje,
                            })

                        } else {
                            this.campo_busqueda = ""
                            this.lista_departamentos = [];
                            this.Cerrar_Ventana_Emergente_Nuevo();
                        }
                    })
                    .catch(() => {
                        this.$vs.loading.close();
                    })
                this.$vs.loading.close();
            }

        },
        Borrar_filtro() {
            this.campo_busqueda = "";
            this.lista_departamentos = [];
            this.Consultar_Departamentos();
        },
        Eliminar_Registro(value) {
            /**
             * @General
             * Función eliminar registro;
             */

            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Confirmación',
                text: '¿Cambiar esta a departamento \'' + value.CODIGO + ' - ' + value.NOMBRE + '\'? ',
                acceptText: 'Aceptar',
                cancelText: 'Cancelar',
                accept: () => {
                    const url = this.$store.state.global.url
                    //Condisión de estado es por que toma el valor de swith y no el valor del registro
                    this.$vs.loading();
                    this.axios.post(url + 'app/v1_ordencompra/departamento', {
                            Codigo: value.CODIGO,
                            activa: (!value.ESTADO ? 'N' : 'S'),
                            id_responsable: 0,
                            id_centro_costo: 0,
                            tipo_consulta: 'B'

                        })
                        .then(resp => {
                            this.$vs.loading.close();
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Departamento',
                                    text: resp.data.mensaje,
                                })

                            } else {
                                this.campo_busqueda = "";
                                this.lista_departamentos = [];
                                this.Consultar_Departamentos();

                                this.lista_departamentos = [];
                                this.$vs.notify({
                                    color: '#danger',
                                    title: 'Información Departamento',
                                    text: resp.data.mensaje,
                                });

                            }
                        })
                        .catch(() => {
                            this.$vs.loading.close();
                        })
                    this.$vs.loading.close();

                },
                cancel: () => {
                    value.ESTADO = !value.ESTADO
                }
            })

        },
        onChangeTipo(value) {
            //Si existe seleccionada un element
            this.Deshabilitado_categoria = false;

            this.Enviar_Todos = false;
            if (value !== null && value.length !== 0) {
                this.id_operacion_selec = value.CODIGO;
                this.cb_categoria_disponibles = null;
                this.id_categoria_selec = '';
                this.Consultar_CategoriasDisponibles()
                this.Consultar_Asignaciones();
                this.Estado_Todos = false;
            } else {
                this.id_operacion_selec = '';
                this.Lista_Asignaciones_categoria_depto = [];
                this.Lista_categorias_disponibles = [];
                this.Estado_Todos = true;
                this.Check_todos = false;
            }

        },
        onChangeCategoria(value) {

            this.lista_categorias_select = JSON.stringify(value);

        },
        // categoria_seleccionada({
        //     CODIGO,
        //     NOMBRE
        // }){
        //     return `${CODIGO} - ${NOMBRE} `
        // },
        onChangeCheck() {
            if (this.Enviar_Todos) {
                this.Deshabilitado_categoria = true;
                this.id_categoria_selec = '',
                    this.lista_categorias_select = [];
                this.lista_categorias_select = JSON.stringify(this.Lista_categorias_disponibles);

            } else {

                this.Deshabilitado_categoria = false;
            }

        },
        Asignar_Categoria(datos) {
            this.Estado_VentanaCategoria = true
            this.Deshabilitar_controles = true
            this.Codigo_depto = datos.CODIGO,
                this.Nombre_departamento = datos.NOMBRE
            this.Titulo_emergente = "Asignando Categorías"

        },
        async Consultar_Asignaciones() {
            
            const url = this.$store.state.global.url;
            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/consulta_departamento_categoria', {
                    codigo_departamento: this.Codigo_depto,
                    tipo: this.id_operacion_selec
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Mantenimiento Departamento',
                            text: resp.data.mensaje,
                        })
                        this.Lista_Asignaciones_categoria_depto = [];
                    } else {
                        this.Lista_Asignaciones_categoria_depto = resp.data.json;
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },
        async Consultar_CategoriasDisponibles() {
            this.Deshabilitado_categoria = false
            const url = this.$store.state.global.url;
            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/consulta_categorias_disponibles_departamentos', {
                    Departamento: this.Codigo_depto,
                    tipo: this.id_operacion_selec,
                    operacion: 'A'
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.Deshabilitado_categoria = true
                        this.Lista_categorias_disponibles = [];
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Asignación Categoría',
                            text: 'No exsite categoría para asignar.',
                        })
                        return;
                    } else {
                        this.Lista_categorias_disponibles = [];
                        this.Lista_categorias_disponibles = resp.data.json;
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();

        },
        Guardar_Asociacion() {
            /**
             * @General
             * Función Permite  actualizar y  almacenar un nuevo registro;
             */
            this.res_variables = this.Validacion_Campos('A', 'Departamento', this.Codigo_depto, true);
            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('A', 'Operación', this.id_operacion_selec, true);

            if (this.Lista_categorias_disponibles == [] && this.lista_categorias_select == '' ) {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Asignación Categoría',
                    text: 'No exsite categoría para asignar.',
                })
                return;
            } else {
                var cantidad = Object.keys(this.lista_categorias_select).length;
                if (cantidad == '0' || cantidad == '2') { //Validación que el array tenga datos, que tenga mas de dos caracteres, 
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Asignación Categoría',
                        text: 'Seleccionar categoría',
                    })
                    return;
                }
            }

            if (this.res_variables) {

                this.$vs.loading();
                const url = this.$store.state.global.url;
                this.axios.post(url + 'app/v1_OrdenCompra/departamento_categoria', {
                        codigo: 0,
                        codigo_departamento: this.Codigo_depto,
                        json_datos: this.lista_categorias_select,
                        estado: 'S',
                        tipo: this.id_operacion_selec,
                        operacion: 'N',
                    })
                    .then(resp => {
                        this.$vs.loading.close();
                        if (resp.data.codigo !== 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Asignación Categoría',
                                text: resp.data.mensaje,
                            })

                            this.lista_categorias_select = [];
                            this.id_categoria_selec = '';
                            this.cb_categoria_disponibles = '';
                            this.Consultar_Asignaciones();
                            this.Consultar_CategoriasDisponibles();

                        } else {
                            this.Lista_categorias_disponibles = [];
                            this.lista_categorias_select = [];
                            this.id_categoria_selec = '';
                            this.cb_categoria_disponibles = '';
                            this.Deshabilitado_categoria = false;
                            this.Enviar_Todos = false;
                            this.Consultar_Asignaciones();
                            this.Consultar_CategoriasDisponibles();

                        }
                    })
                    .catch(() => {
                        this.$vs.loading.close();
                    })
                this.$vs.loading.close();
            }

        },
        Eliminar_RegistroCat(value) {
            /**
             * @General
             * Función eliminar registro;
             */
            const sesion = this.$store.state.sesion;
            this.Corporativo_Sesion = sesion.corporativo
            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Confirmación',
                button: {
                    yes: 'Aceptar',
                    no: 'Cancelar'

                },
                text: '¿Dar de baja Asignación  \'' + value.DEPARTAMENTO + '\' -  \'' + value.CATEGORIA + '\' ? ',
                accept: () => {
                    const url = this.$store.state.global.url
                    this.$vs.loading();
                    this.axios.post(url + 'app/v1_ordenCompra/departamento_categoria', {
                            codigo: value.CODIGO,
                            codigo_departamento: '0',
                            codigo_categoria: '0',
                            estado: 'N',
                            tipo: value.TIPO,
                            operacion: 'B',

                        })
                        .then(resp => {
                            this.$vs.loading.close();
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Asignación Categoria',
                                    text: resp.data.mensaje,
                                })

                            } else {
                                this.Lista_Asignaciones_categoria_depto = [];
                                this.Consultar_Asignaciones();
                                this.Consultar_CategoriasDisponibles()
                                this.$vs.notify({
                                    color: '#danger',
                                    title: 'Mantenimiento Departamento',
                                    text: resp.data.mensaje,
                                });

                            }
                        })
                        .catch(() => {
                            this.$vs.loading.close();
                        })
                    this.$vs.loading.close();

                }
            })

        },

        Validacion_Campos(Tipo, Nombre, valor, obligatorio) {
            /**
             * @General
             * Tipo : N = Númerio, Nombre = Descripción del campo, Valor = A evaluar, Obligatorio= Verdadero o falso.
             * Devuelve un mensaje al usuario si las validaciones no se cumple y aborta, de lo contrario devuelve verdadero
             */
            if (Tipo == 'N') {
                if (valor <= 0) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Departamento',
                        text: 'Campo \'' + Nombre + '\' es obligatorio.',
                    });
                    return false;
                } else {
                    return true;
                }
            } else {
                if (valor == '' && obligatorio == true) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Departamento',
                        text: 'Campo \'' + Nombre + '\' es obligatorio.',
                    });
                    return false;
                } else {
                    return true;
                }
            }
        },

    }
}
</script>

<style scoped>
.footer {
    height: 50px;
    margin-top: -65px;
    margin-left: 10px;
}

.label-sizem {
    font-size: 15px;
}

.label-size {
    font-size: 16px;
    font-weight: bold;
}
</style>
