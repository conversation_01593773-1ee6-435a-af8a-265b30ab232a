<template>
<div class="encabezado">
    <div class="flex flex-wrap relative">
        <div v-if="Mostrar_Admision" class="w-52 p-1 info-enc">

            <table>

                <tr>
                    <td class="font-semibold">Nombre:</td>
                    <td>{{ PacienteData.PrimerNombre + ' ' + PacienteData.SegundoNombre }}</td>
                </tr>
                <tr>
                    <td class="font-semibold">Apellido:</td>
                    <td>{{ PacienteData.PrimerApellido + ' ' + PacienteData.SegundoApellido }}</td>
                </tr>
            </table>
        </div>

        <div v-if="Mostrar_Admision" class="w-52 p-1 info-enc">
            <table>

                <tr>
                    <td class="font-semibold">Edad:</td>
                    <td>{{ getAge(PacienteData.Nacimiento) }}</td>
                </tr>
                <tr>
                    <td class="font-semibold">Sexo:</td>
                    <td>{{ PacienteData.Sexo }}</td>
                </tr>
                <tr>
                    <td colspan="1" class="font-semibold">Nacimiento:</td>
                    <td colspan="1">{{ PacienteData.Nacimiento }}</td>
                </tr>
            </table>
        </div>

        <div v-if="Mostrar_Admision" class="w-52 p-1 info-enc">
            <table>

                <tr>
                    <td class="font-semibold">DPI:</td>
                    <td>{{ PacienteData.DPI }}</td>
                </tr>

            </table>
        </div>

        <div v-if="Mostrar_Admision" class="w-52 p-1 info-enc">
            <table>

                <tr>
                    <td class="font-semibold">Correo Electrónico:</td>
                    <td>{{ PacienteData.Email }}</td>
                </tr>
                <tr>
                    <td class="font-semibold">Móvil:</td>
                    <td>{{ PacienteData.Celular }}</td>
                </tr>
                <tr>
                    <td colspan="1" class="font-semibold">Teléfono Residencial:</td>
                    <td colspan="1">{{ PacienteData.Telefono }}</td>
                </tr>
            </table>

        </div>
        <div v-if="Mostrar_Admision" class="w-56 p-1 info-enc">
            <table>
                <tr>
                    <td class="font-semibold">NIT:</td>
                    <td>{{ PacienteData.Nit }} </td>
                </tr>
                <tr>
                    <td class="font-semibold">Nombre Factura:</td>
                    <td>{{ PacienteData.NombreFactura }}</td>
                </tr>
                <tr>
                    <td class="font-semibold">Dirección Factura:</td>
                    <td>{{ PacienteData.DireccionFactura }}</td>
                </tr>
            </table>
        </div>

        <div v-if="Mostrar_Admision" class="w-56 p-1 info-enc">
            <table>
                <tr>
                    <td class="font-semibold">Código Paciente:</td>
                    <td>{{ PacienteData.Codigo }} </td>
                </tr>
                <tr>
                    <td class="font-semibold">Código Cliente:</td>
                    <td>{{ PacienteData.IdCliente }} </td>
                </tr>

            </table>
        </div>
        
        <div  class="w-86 p-1 button-container"  v-show="disablePrint === false" >
            <table>
            <tr>
                <td>
                   <vs-button 
                     class="button_print " 
                     v-on:click="generarReporte()"                     
                     >
                     <font-awesome-icon :icon="['fas', 'fa-print']" style="font-size: 40px !important;" />
                    </vs-button>  
                    
                </td>
            </tr>
            </table>
        </div>

    </div>
</div>
</template>

<script>
import {
    mapFields
} from "vuex-map-fields";
import moment from "moment";
export default {
    props:{
        disablePrint:{
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            No_Admision: '',
            Mostrar_Admision: true,            
        };
    },
    components: {

    },
    created(){        

    },
    beforeMount(){
      
    },
    mounted() {
    

        // this.disablePrint = this.disablePrintReport

    },
    methods: {

        generarReporte() {

        
            this.$reporte_modal({
                Nombre: 'ReportePacienteAdmision',
                Opciones: {
                    Serie: this.AdmisionData.Serie,
                    CodigoAdmision: this.AdmisionData.Codigo,
                    CodigoPaciente: this.PacienteData.Codigo
                }
            })

        },
        getAge(dateString) {
            if (dateString === undefined || dateString === '')
                return 0
            var today = new Date();
            var dateMomentObject = moment(dateString, "YYYY-MM-DD");
            var birthDate = dateMomentObject.toDate();
            if (!this.validDate(dateString) || dateString === 0 || birthDate.getDate() > today.getDate())
                return 0
            var age = today.getFullYear() - birthDate.getFullYear();
            var m = today.getMonth() - birthDate.getMonth();
            if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
                age--;
            }
            return age;
        },
        validDate(date) {
            return (new Date(date) !== "Invalid Date") && !isNaN(new Date(date) && date !== undefined);
        },
        LimpiarEncabezado() {
            return {
                NumeroExpediente: "",
                Motivoconsulta: "",
                HistoriaEnfermedad: "",

                Empresa: "",
                SerieAdmision: "",
                CodigoAdmision: "",
                TipoAdmision: "",
                StatusAdmision: "",
                Entrada: "",
                FechaEgreso: "",

                Habitacion: "",
                HospitalHabitacion: "",

                NombreSeguro: "",
                TipoSeguro: "",
                Tratante: "",
                VisitasPrevias: "",

                CodPaciente: "",
                Nombre: "",
                Apellido: "",
                Sexo: "",
                Nacimiento: "",
                Edad: "",
                Peso: "",
                Estatura: "",
                IMC: "",
                DescripcionIMC: "",

                Celular: "",
                Ocupacion: "",

            }
        },
        LimpiarAdmision() {
            this.Mostrar_Admision = false;
            this.PacienteData = this.LimpiarEncabezado()
        },
        BuscarPaciente() {
            this.$vs.notify({
                time: 4000,
                title: 'Búsqueda de Paciente',
                text: 'Búsque de paciente aún no implementada',
                color: 'primary',
                position: 'top-left'
            })
        },
        PermiteCrearExpediente() {
            return this.PacienteData.NumeroExpediente != ""
        },
        MostrarDatosAdmision() {
            this.$refs.componentDatosAdicionales.mostrar = !this.$refs.componentDatosAdicionales.mostrar
        },
        renameKey(obj, oldKey, newKey) {
            obj[newKey] = obj[oldKey];
            delete obj[oldKey];
        },
        replaceToHTML(stringval, oldval, newval) {
            return stringval.replace(oldval, newval)
        },
        RegistrarIngreso() {
            if (this.PacienteData.NumeroExpediente != "0")
                this.$vs.dialog({
                    type: 'confirm',
                    color: '#ed8c72',
                    title: 'Existe un expediente de Ingreso para la admisión ' + this.No_Admision,
                    acceptText: 'Crear Nuevo Expediente ',
                    cancelText: 'Trabajar con con el Expediente Existente',
                    text: `¿Desea crear nuevo expediente o trabajar con el existente?`,
                    buttonCancel: false,
                    accept: () => {
                        this.$emit('onCrearExpediente', this.PacienteData)
                    },
                    cancel: () => {

                    }
                })
            else
                this.$emit('onCrearExpediente', this.PacienteData)
        },
    },
    computed: {        
        ...mapFields('paciente', ["PacienteData"]),
        ...mapFields('admision', ["AdmisionData"]),
        Nacimiento() {
            return this.PacienteData.Nacimiento;
        },
        DatosAdicionales() {
            if (this.PacienteData) {
                let result = {
                    'Número de Expediente': this.PacienteData.NumeroExpediente,
                    'Serie y número de la Admisión': this.PacienteData.SerieAdmision + ' ' + this.PacienteData.CodigoAdmision,
                    'Tipo de Admisión': this.PacienteData.TipoAdmision,
                    'Fecha de Ingreso': this.PacienteData.Entrada,
                    'Fecha de Egreso': this.PacienteData.FechaEgreso,
                    'Estado de la Admisión': this.PacienteData.StatusAdmision,
                    'Habitación': this.PacienteData.Habitacion,
                    'Hospital de Habitación': this.PacienteData.HospitalHabitacion,
                    'Seguro': this.PacienteData.NombreSeguro,
                    'Tipo de Seguro': this.PacienteData.TipoSeguro,
                    'Médico Tratante': this.PacienteData.Tratante,
                    'Visitas previas': this.PacienteData.VisitasPrevias,
                    'Código del Paciente': this.PacienteData.CodPaciente,
                    'Nombre del Paciente': this.PacienteData.Nombre + ' ' + this.PacienteData.Apellido,
                    'Sexo': this.PacienteData.Sexo,
                    'Fecha de Naciemiento': this.PacienteData.Nacimiento,
                    'Edad': this.PacienteData.Edad,
                    'Peso': this.PacienteData.Peso + 'kg',
                    'Estatura': this.PacienteData.Estatura + 'cm',
                    'Índice de Masa Corporal': this.PacienteData.IMC + this.PacienteData.DescripcionIMC,
                }
                let motivo = this.PacienteData.Motivoconsulta.replace(/\\n/g, '<br>').replace(/\\r/g, '')
                let historia = this.PacienteData.HistoriaEnfermedad.replace(/\\n/g, '<br>').replace(/\\r/g, '')

                return [{
                        label: 'Datos de la Admisión',
                        contenido: result,
                        forma: 'i'
                    },
                    {
                        label: 'Motivo de la Consulta',
                        contenido: motivo,
                        forma: 'p'
                    },
                    {
                        label: 'Historia de la Enfermedad',
                        contenido: historia,
                        forma: 'p'
                    },
                ]
            }
            return []

        }
    },

}
</script>

<style scoped>
.info-enc {
    border: 1px solid #ccc !important;
    border-radius: 5px;
    margin: 1px;
    background-color: #f8f8f8;
}

.info-enc-height {
    height: 83px !important;
}

.encabezado {
    overflow-y: hidden;
    font-size: small;
    background: rgb(238, 129, 118);
    background: linear-gradient(90deg, rgb(238, 188, 118) 30%, rgba(190, 0, 0, 0.2189250700280112) 47%, rgba(0, 143, 190, 1) 67%);
    color: #22559c;
    margin: 5px;
}
.flex {
  position: relative; /* Asegúrate de que el contenedor sea relativo para la posición absoluta del botón */
}


.button-container {
  position: absolute; /* Posiciona este div de forma absoluta */
  right: 0; /* Alineado al borde derecho */
  top: 0; /* Opcional, puedes ajustar según el diseño */
}

.button_print {  
    background:#f1f2f3 !important;
    color: rgb(244, 241, 240)!important;;
    border: none !important;;
    padding: 5px !important;;
    margin: 10px !important;;
    border-radius: 5px !important;
    background-color: transparent !important;
    background-repeat: no-repeat !important;
    border: none !important;
    cursor: pointer !important;
    overflow: hidden !important;
    outline: none !important;
}

</style>
