<template>
    <div>
        <form @submit="handleSubmit">
            <DxForm :ref="formTransaccion" :form-data.sync="formulario" label-mode="floating" style="margin: 5px">
                <DxFormGroupItem :col-count="4">
                    <DxFormItem data-field="Fecha" editor-type="dxDateBox" v-model="formulario.Fecha"  :editor-options="{
                              openOnFieldClick: true
                          }"/>
                </DxFormGroupItem>
                <DxFormGroupItem>
                
                    <DxFormItem 
                    data-field="Funcion"
                    editor-type="dxRadioGroup" :editor-options="{
                        items: funciones,
                        displayExpr: 'text',
                        valueExpr: 'id',
                        layout: 'horizontal',
                    }">

                    </DxFormItem>

                </DxFormGroupItem>
                <DxFormGroupItem :col-count="2">
                    
                    <DxFormItem data-field="Cuenta" editor-type="dxSelectBox" :editor-options="{
                        width: 'auto',
                        searchEnabled: true,
                        items: cuentas,
                        displayExpr: 'Valor',
                        valueExpr: 'Codigo',
                        onValueChanged: AsignarSiguienteCheque,
                    }" :validationRules="[{ type: 'required' }]" />
                        
                    <DxFormGroupItem :col-count="2">
                        <DxFormItem template="Efectivo" />

                        <DxFormItem template="Suma" />
                    </DxFormGroupItem>
                </DxFormGroupItem>
                <DxFormGroupItem :col-count="3">
                    <DxFormItem data-field="Cajas" v-model="formulario.Cajas" editor-type="dxNumberBox">
                        <DxFormLabel text="Caja" />
                    </DxFormItem>
                    <DxFormItem data-field="Corte" v-model="formulario.Corte" editor-type="dxNumberBox">
                        <DxFormLabel text="Corte" />
                    </DxFormItem>

                    <DxFormButtonItem   v-if="formulario.Cajas && formulario.Cajas !== 0" :button-options="buttonNewMonth" name="NewMonth" horizontal-alignment="center" verical-alignment="center" />
                    <DxFormLabel text="Caja" />
                </DxFormGroupItem>
                <DxFormGroupItem :col-count="2">
                    <DxFormItem data-field="Numero" v-model="formulario.Numero" editor-type="dxNumberBox" :editor-options="{ min: 0, format: '#0', step: 0 }">
                        <DxFormLabel text="Numero" />
                    </DxFormItem>

                </DxFormGroupItem>
                <DxFormGroupItem :col-count="2">
                    <DxFormItem data-field="Efectivo" v-model="formulario.Efectivo" editor-type="dxNumberBox" :editor-options="{ min: 0, format: '#0.00', step: 0 }">
                        <DxFormLabel text="Efectivo" />
                    </DxFormItem>

                    <DxFormItem data-field="OtrosCeques" v-model="formulario.OtrosCeques" editor-type="dxNumberBox" :editor-options="{ min: 0, format: '#0.00', step: 0 }">
                        <DxFormLabel text="OtrosCeques" />
                    </DxFormItem>
                </DxFormGroupItem>
                <DxFormGroupItem :col-count="2">
                    <DxFormItem data-field="ChequesLocales" v-model="formulario.ChequesLocales" editor-type="dxNumberBox" :editor-options="{ min: 0, format: '#0.00', step: 0 }">
                        <DxFormLabel text="ChequesLocales" />
                    </DxFormItem>

                    <DxFormItem data-field="Giros" v-model="formulario.Giros" editor-type="dxNumberBox" :editor-options="{ min: 0, format: '#0.00', step: 0 }">
                        <DxFormLabel text="Giros" />
                    </DxFormItem>
                </DxFormGroupItem>
                <DxFormGroupItem>
                    <DxFormItem template="Total" />
                </DxFormGroupItem>
                <DxFormGroupItem :col-count-by-screen="colCountByScreen3">
                    <DxFormItem data-field="Observaciones" v-model="formulario.Observaciones" editor-type="dxTextArea" :editor-options="{ height: '100%' }" />
                </DxFormGroupItem>

                <DxFormGroupItem :col-count="2">
                    <DxFormButtonItem :button-options="buttonGuardar" name="save" horizontal-alignment="center" verical-alignment="center" />

                    <DxFormButtonItem :button-options="buttonLimpiar" name="Clear" horizontal-alignment="center" verical-alignment="center" />
                </DxFormGroupItem>
                <template #Total="{}">
                    <div>
                         <label v-if="total > 0" for="" style="color:#1f618d;">Total: {{ total }}</label>
                    </div>
                </template>
                <template #Efectivo="{}">
                    <div>
                        <label for="" style="color:#2471a3" v-if="mostrarValores">Total Corte: {{ valores.Efectivo }}</label>
                    </div>
                </template>

                <template #Suma="{}">
                    <div>
                        <label for="" style="color:#27ae60" v-if="mostrarValores">Total Depositos: {{ TotalDepositos.SumofMonto || "0.00" }}</label>
                    </div>
                </template>
            </DxForm>
        </form>
        <DxPopup :visible.sync="popupVisible" :drag-enabled="false" :hide-on-outside-click="true" :show-close-button="true" :show-title="true" :width="600" :height="600">
            <div>
                <h4 style="text-align: center;"> Listado de Cortes</h4>
                <br>
                <DxDataGrid :row-alternation-enabled="true" :data-source="cortes" :show-borders="true" @rowClick="onRowClick">
                    <DxDataGridSelection mode="single" />
                    <DxDataGridColumn caption="Numero" data-field="Final" data-type="date" />
                    <DxDataGridColumn caption="Corte" data-field="Codigo" data-type="number" />
                    <DxDataGridColumn caption="Cajero" data-field="Usuario" />
                    <DxDataGridPaging :page-size="10" />
                    <DxDataGridDxSearchPanel :visible="true" />
                </DxDataGrid>
            </div>
        </DxPopup>
    </div>
</template>

<script>

const formTransaccion = "formTransaccion";
 
export default {
    name: "DepositosCorteCaja",
    data() {
        return{

            formulario:{
                Cuenta: null,
                Fecha: new Date(),
                Cajas:null,
                Corte:null,
                Numero:null,
                Efectivo:null,
                OtrosCeques:null,
                ChequesLocales: null,
                Giros:null,
                Observaciones: null,
                Contabilidad:null,
                Funcion: 'DP'
            },

            funciones: [
                {
                id: "DP",
                text: "Deposito",
                },
                {
                id: "NC",
                text: "Nota de Credito",
                },
            ],

            cuentas:[],
            cortes:[],
            corte:[],

            valores:{
                Efectivo: null 
            },


            TotalDepositos:{
                SumofMonto: null
            },

            mostrarValores: false,
            popupVisible: false, // Controla la visibilidad del popup

            formTransaccion,

            buttonNewMonth: {
                width: "auto",
                icon: "fas fa-calendar-plus",
                hint: "Cortes",
                text: "Cortes",
                type: "default",
                onClick: () => {
                    this.consultaCortes()
                },
                useSubmitBehavior: false,
            },

            buttonLimpiar:{
                width: "auto",
                icon: "fas fa-calendar-plus",
                hint: "Cancelar",
                text: "Cancelar",
                type: "danger",
                onClick: () => {
                  this.limpiar()
                },
                useSubmitBehavior: false,
            },

            buttonGuardar:{
                width: "auto",
                icon: "fas fa-calendar-plus",
                hint: "Guardar",
                text: "Guardar",
                type: "success",
                onClick: () => {
                  this.insertar()
                },
                useSubmitBehavior: false, 
            }
        }
    },
    watch:{
        'formulario.Corte': function(newValue, oldValue) {
            // Verificamos si el valor no es null y si ha cambiado respecto al valor anterior
            if (newValue !== null && newValue !== oldValue) {
                this.consultaCortesPorCorte();
            }
        }
    },  
    computed:{
        formTransformAnticipoInstanceaccion: function () {
            return this.$refs[formTransaccion].instance;
        },
        colCountByScreen3() {
        return this.calculateColCountAutomatically
            ? null
            : {
                sm: 1,
                md: 1,
                lg: 1,
            };
        },
        total() {
        return (
                parseFloat(this.formulario.Efectivo || 0) +
                parseFloat(this.formulario.OtrosCeques || 0) +
                parseFloat(this.formulario.ChequesLocales || 0) +
                parseFloat(this.formulario.Giros || 0)
            ).toFixed(2); // Puedes limitar los decimales a 2 si lo prefieres
        },

    },  
    methods:{
        handleSubmit(e) {
            e.preventDefault();
        },

        CargarCuentas(asignar) {
            // La variable de asignar indica cuando debe seleccionar la primera cuenta de la lista
            this.axios
                .post("/app/v1_bancos/Cuentas", {
                Opcion: 1,
                })
                .then((resp) => {
                this.cuentas = resp.data.json;

                if (asignar) {
                    this.AsignarBanco(this.cuentas[0]);
                }
                });
            },

        AsignarSiguienteCheque(e) {
            let cuenta = this.cuentas.find((x) => x.Codigo == e.value);
            this.AsignarBanco(cuenta);
        },

        AsignarBanco(e) {
            //Asignar los valores del banco a los campos correspondientes del formulario
            this.formulario.Cuenta = e.Codigo;
            this.formulario.Contabilidad = e.Contabilidad;
        },


        consultaCortes(){
            this.axios.post("/app/v1_bancos/ConsultaCortes",{
                Opcion:2,
                Caja: this.formulario.Cajas
            }).then((resp)=>{
                if(resp.data.codigo === 0){
                    this.cortes = resp.data.json
                }
                this.popupVisible = true; 
            })
        },

        handleTabPress(event) {
            // Evitar el comportamiento predeterminado de Tab (no cambiar el foco automáticamente)
            event.preventDefault();

            // Llamamos a la función de consulta después de presionar Tab
            this.consultaCortesPorCorte();

            // Mover el foco manualmente después de hacer la consulta (si es necesario)
            this.$nextTick(() => {
                // Este código asegura que el foco se moverá al siguiente campo después de la consulta.
                // Puedes ajustar la lógica para mover el foco a otro campo específico.
                this.$refs.nextField.focus();  // Asegúrate de tener un campo {con} ref="nextField"
            });
        },

        consultaCortesPorCorte() {
            this.axios.post("/app/v1_bancos/ConsultaCortes", {
                Opcion: 3,
                Codigo: this.formulario.Corte,
                Caja: this.formulario.Cajas
            }).then((resp) => {
               if(resp.data.codigo === 0){
                    this.corte = resp.data.json;
                    this.Efectivo();
                    this.Sumof();
                    this.mostrarValores = true;
               }
                // Verificar si hay un error en la respuesta (el array "json" contiene el objeto de error)
                if (resp.data.json.length > 0 && resp.data.json[0].codigo === "1" && resp.data.json[0].tipo_error === "1") {
                    // Mostrar una notificación con "this.$vs.notify"
                    this.$vs.dialog({
                        type: 'confirm',
                        color: '#c0392b',
                        title: 'Error de Información',
                        acceptText: 'Aceptar',
                        cancelText: 'Cancelar',
                        text: `Corte No valido (O_o).`,
                        buttonCancel: 'border',
                        clientWidth: 100,
                        accept: () => {
                        },
                        cancel: () => {}
                    });
                } else {
                    this.$vs.notify({
                        title: 'Notificación',
                        text: 'Corte Valido',
                        iconPack: 'feather',
                        icon: 'icon-alert-circle',
                        color: 'success'
                    });
               
                }
            }).catch(() => {

            });
        },

        Efectivo() {
            this.axios.post("/app/v1_bancos/ConsultaCortes", {
                Opcion: 4,
                Codigo: this.formulario.Corte,
                Caja: this.formulario.Cajas
            }).then((resp) => {
                // Suponiendo que resp.data tenga el valor que deseas asignar a 'Efectivo'
                this.$map({
                        objeto: this.valores,
                        respuestaAxios: resp,
                });
            }).catch(() => {
            });
        },


        Sumof() {
            this.TotalDepositos.SumofMonto = "0.00"
            this.axios.post("/app/v1_bancos/ConsultaCortes", {
                Opcion: 5,
                Codigo: this.formulario.Corte,
                Caja: this.formulario.Cajas
            }).then((resp) => {
                // Suponiendo que resp.data tenga el valor que deseas asignar a 'SumofMonto'
                this.$map({
                        objeto: this.TotalDepositos,
                        respuestaAxios: resp,
                });
            }).catch(() => {
            });
        },


        onRowClick(e) {
            this.formulario.Corte = e.data.Codigo
            this.popupVisible = false; 

        },


        insertar() {
            // Formatear la fecha en el formato deseado (YYYY-MM-DD)
            const fechaFormateada = this.formulario.Fecha.toISOString().split('T')[0];

            this.axios.post("/app/v1_bancos/InsertarDepositos", {
                Opcion: 6,
                Cuenta: this.formulario.Cuenta,
                CuentaL: this.formulario.Contabilidad,
                Fecha: fechaFormateada,  // Usamos la fecha formateada aquí
                Caja: this.formulario.Cajas,
                Corte: this.formulario.Corte,
                Tipo: this.formulario.Funcion,
                Numero: this.formulario.Numero,
                Efectivo: this.formulario.Efectivo,
                ChequeLocales: this.formulario.ChequesLocales,
                Otros: this.formulario.OtrosCeques,
                Giros: this.formulario.Giros,
                Debe: this.total,
                Haber: this.total,
                Monto: this.total,
                Observaciones: this.formulario.Observaciones
            })
            .then((resp) => {
                if (resp.data.codigo === 0) {
                    this.$vs.dialog({
                        type: 'confirm',
                        color: '#229954',
                        title: 'Depósito registrado ᕙ(`▿´)ᕗ',
                        acceptText: 'Si',
                        cancelText: 'No',
                        text: `¿Registrar otro? `,
                        buttonCancel: 'border',
                        clientWidth: 100,
                        accept: () => {
                            this.limpiar()
                        },
                        cancel: () => {
                            this.limpiar()
                        }
                    });
                }
            });
        },


        limpiar(){
            this.mostrarValores = false
            
            this.formulario.Cajas = null
            this.formulario.Corte = null
            this.formulario.Numero = null
            this.formulario.Efectivo= null
            this.formulario.OtrosCeques= null
            this.formulario.ChequesLocales=  null
            this.formulario.Giros =  null,
            this.formulario.Observaciones= null
            this.formulario.Contabilidad= null
            this.CargarCuentas(true)
        }


    },
    beforeMount() {
        this.CargarCuentas(true);
    }

}

</script>

<style>
</style>