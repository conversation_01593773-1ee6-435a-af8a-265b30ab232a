<template>
  <div class="sticky-notes-container">
 
    <div class="controls">
      <!-- <vs-button color="primary" @click="createNote" icon="add">Nueva Nota</vs-button> -->

      <div class="notificacion" @click="createNote" title="Nueva nota">
                <i class="fas fa-edit"  style="color: rgb(76, 175, 80);"
                
                  @mouseover="$event.target.style.color = 'rgb(60, 140, 64)'" 
                   @mouseleave="$event.target.style.color = 'rgb(76, 175, 80)'" >
                </i>
                
            </div> 

    </div>    
    <div class="notes-wrapper">
      <div 
        v-for="(note, index) in notes" 
        :key="index" 
        class="sticky-note" 
        :style="{ 
          backgroundColor: note.color, 
          top: note.posY + 'px', 
          left: note.posX + 'px',
          width: note.width + 'px',
          height: note.height + 'px',
          zIndex: 100 + index
        }"
      >
        <div class="note-header">
          <div class="note-move-icon" @mousedown.stop="startDrag($event, index)">
            <i class="material-icons">open_with</i>
          </div>
          <input 
            :disabled="note.corporativo >0? false: true"
            v-model="note.titulo" 
            class="note-title" 
            placeholder="Título" 
          @input="handleTextareaInput(index, $event)"
          maxlength="499"            
          />
          <div class="note-actions">
            <vs-button type="flat" icon="add" size="small" @click="createNote" ></vs-button>
            <vs-dropdown>
              <vs-button type="flat" icon="palette" size="small"></vs-button>
              <vs-dropdown-menu>
                <vs-dropdown-item 
                  v-for="color in noteColors" 
                  :key="color.value"
                  @click="changeNoteColor(index, color.value)"
                >
                  <div class="color-option" :style="{ backgroundColor: color.value }"></div>
                  {{ color.name }}
                </vs-dropdown-item>
              </vs-dropdown-menu>
            </vs-dropdown>
            <vs-button type="flat" icon="close" size="small" @click="deleteNote(index)"></vs-button>
          </div>
        </div>
        
        <textarea 
        :disabled="note.corporativo >0? false: true"
          v-model="note.detalle" 
          class="note-content" 
          placeholder="Escribe tu nota aquí..." 
          @input="handleTextareaInput(index, $event)"
          maxlength="499"
        ></textarea>
        <div class="character-counter" v-if="note.detalle && note.detalle.length > 0">
          {{ note.detalle.length }}/499
        </div>
        
        <!-- Bordes para redimensionar -->
        <div class="resize-handle resize-handle-top" @mousedown.stop="startResizeTop($event, index)"></div>
        <div class="resize-handle resize-handle-right" @mousedown.stop="startResizeRight($event, index)"></div>
        <div class="resize-handle resize-handle-bottom" @mousedown.stop="startResizeBottom($event, index)"></div>
        <div class="resize-handle resize-handle-left" @mousedown.stop="startResizeLeft($event, index)"></div>
        
        <!-- Esquinas para redimensionar -->
        <div class="resize-handle resize-handle-top-left" @mousedown.stop="startResizeTopLeft($event, index)"></div>
        <div class="resize-handle resize-handle-top-right" @mousedown.stop="startResizeTopRight($event, index)"></div>
        <div class="resize-handle resize-handle-bottom-left" @mousedown.stop="startResizeBottomLeft($event, index)"></div>
        <div class="resize-handle resize-handle-bottom-right" @mousedown.stop="startResizeBottomRight($event, index)"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StickyNotes',
  data() {
    return {
      notes: [],
      isDragging: false,
      isResizing: false,
      resizeDirection: null,
      currentNoteIndex: null,
      dragOffset: { x: 0, y: 0 },
      resizeStartPos: { x: 0, y: 0 },
      resizeStartDimensions: { width: 0, height: 0, posX: 0, posY: 0 },
      // Límites de posición y tamaño
      posLimits: {
        minX: -1431,
        maxX: 180,
        minY: -5,
        maxY: 720
      },
      sizeLimits: {
        minWidth: 200,
        maxWidth: 400,
        minHeight: 150,
        maxHeight: 600
      },
      noteColors: [
        { name: 'Amarillo', value: '#fff9c4' },
        { name: 'Verde', value: '#c8e6c9' },
        { name: 'Azul', value: '#bbdefb' },
        { name: 'Rosa', value: '#f8bbd0' },
        { name: 'Naranja', value: '#ffe0b2' }
      ],
      // Timer para guardar automáticamente
      autoSaveTimer: null,
      lastEditedNoteIndex: null
    }
  },
  mounted() {
    this.loadNotes();
    window.addEventListener('mousemove', this.onMouseMove);
    window.addEventListener('mouseup', this.stopDrag);
  },
  beforeDestroy() {
    window.removeEventListener('mousemove', this.onMouseMove);
    window.removeEventListener('mouseup', this.stopDrag);
    // Limpiar el timer si existe
    if (this.autoSaveTimer) {
      clearTimeout(this.autoSaveTimer);
    }
  },
  methods: {
    loadNotes() {
      this.axios.post('/app/administracion/NotasAdhesivas', {
        Opcion: 1
      })
      .then(resp => {
        if (resp.data && resp.data.json) {
          this.notes = resp.data.json.map(note => ({
            ...note,
            detalle : note.detalle.replace(/\\n/g, '\n').replace(/\\r/g, '\r'),
            posX: note.posX || 100,
            posY: note.posY || 100,
            width: note.width || 200,
            height: note.height || 200
          }));
        }
      });
    },
    createNote() {
      const newNote = {
        id: 0,
        titulo: '',
        detalle: '',
        color: this.noteColors[0].value,
        posX: 100,
        posY: 100,
        width: 200,
        height: 200
      };
      
      this.notes.push(newNote);
      this.saveNote(this.notes.length - 1);
    },
    // Manejar la entrada de texto con temporizador de autoguardado
    handleTextareaInput(index) {

      const note = this.notes[index];
      
      // Si el texto excede el límite, truncarlo
      if (note.detalle.length > 499) {
        note.detalle = note.detalle.substring(0, 499);
      }
      
      // Si estamos editando una nota diferente, guardar la anterior primero
      if (this.lastEditedNoteIndex !== null && this.lastEditedNoteIndex !== index) {
        this.saveNote(this.lastEditedNoteIndex);
        this.lastEditedNoteIndex = index;
      } else {
        this.lastEditedNoteIndex = index;
      }
      
      // Reiniciar el temporizador
      if (this.autoSaveTimer) {
        clearTimeout(this.autoSaveTimer);
      }
      
      // Configurar un nuevo temporizador (3 segundos)
      this.autoSaveTimer = setTimeout(() => {
        if (this.lastEditedNoteIndex !== null) {
          this.saveNote(this.lastEditedNoteIndex);
        }
      }, 1800);
    },
    saveNote(index) {
      const note = this.notes[index];
      let Opcion = 2; // 2 - Crear Nota, 3 - Actualizar Nota
      
      if (note.id > 0) {
        Opcion = 3;
      }
       
      this.axios.post('/app/administracion/NotasAdhesivas', {
        Opcion: Opcion,
        id: note.id,
        titulo: note.titulo,
        detalle: note.detalle,
        color: note.color,
        posX: note.posX,
        posY: note.posY,
        width: note.width,
        height: note.height,
        _NOTIFICACIONES: false 
        
      })
      .then(() => {
        if (note.id == 0) {
          this.loadNotes();
        }
      });
    },
    deleteNote(index) {
      const noteId = this.notes[index].id;
      
      this.axios.post('/app/administracion/NotasAdhesivas', {
        Opcion: 4,
        id: noteId
      })
      .then(() => {
        this.notes.splice(index, 1);
      });
    },
    changeNoteColor(index, color) {
      this.notes[index].color = color;
      this.saveNote(index);
    },
    startDrag(event, index) {
      this.isDragging = true;
      this.currentNoteIndex = index;
      const note = this.notes[index];
      
      this.dragOffset = {
        x: event.clientX - note.posX,
        y: event.clientY - note.posY
      };
      
      // Cambiar el cursor a "move" durante el arrastre
      document.body.style.cursor = 'move';
      
      event.preventDefault();
      event.stopPropagation();
    },
    // Métodos para iniciar redimensionamiento
    startResizeTop(event, index) {
      this.initResize(event, index, 'top');
    },
    startResizeRight(event, index) {
      this.initResize(event, index, 'right');
    },
    startResizeBottom(event, index) {
      this.initResize(event, index, 'bottom');
    },
    startResizeLeft(event, index) {
      this.initResize(event, index, 'left');
    },
    startResizeTopLeft(event, index) {
      this.initResize(event, index, 'top-left');
    },
    startResizeTopRight(event, index) {
      this.initResize(event, index, 'top-right');
    },
    startResizeBottomLeft(event, index) {
      this.initResize(event, index, 'bottom-left');
    },
    startResizeBottomRight(event, index) {
      this.initResize(event, index, 'bottom-right');
    },
    
    // Método común para inicializar el redimensionamiento
    initResize(event, index, direction) {
      this.isResizing = true;
      this.resizeDirection = direction;
      this.currentNoteIndex = index;
      
      const note = this.notes[index];
      this.resizeStartPos = {
        x: event.clientX,
        y: event.clientY
      };
      
      this.resizeStartDimensions = {
        width: note.width,
        height: note.height,
        posX: note.posX,
        posY: note.posY
      };
      
      event.stopPropagation();
      event.preventDefault();
    },
    
    // Limitar un valor entre un mínimo y un máximo
    clamp(value, min, max) {
      return Math.min(Math.max(value, min), max);
    },
    
    onMouseMove(event) {
      if (this.isDragging && this.currentNoteIndex !== null) {
        const note = this.notes[this.currentNoteIndex];
        
        // Calcular nueva posición
        const newPosX = event.clientX - this.dragOffset.x;
        const newPosY = event.clientY - this.dragOffset.y;
        
        // Aplicar límites
        note.posX = this.clamp(newPosX, this.posLimits.minX, this.posLimits.maxX);
        note.posY = this.clamp(newPosY, this.posLimits.minY, this.posLimits.maxY);
        
      } else if (this.isResizing && this.currentNoteIndex !== null) {
        const note = this.notes[this.currentNoteIndex];
        const deltaX = event.clientX - this.resizeStartPos.x;
        const deltaY = event.clientY - this.resizeStartPos.y;
        
        // Aplicar cambios según la dirección de redimensionamiento
        switch (this.resizeDirection) {
          case 'right':
            var newWidth = this.resizeStartDimensions.width + deltaX;
            note.width = this.clamp(newWidth, this.sizeLimits.minWidth, this.sizeLimits.maxWidth);
            break;
          case 'bottom':
            var newHeight = this.resizeStartDimensions.height + deltaY;
            note.height = this.clamp(newHeight, this.sizeLimits.minHeight, this.sizeLimits.maxHeight);
            break;
          case 'left':
            var newWidthL = this.resizeStartDimensions.width - deltaX;
            var clampedWidthL = this.clamp(newWidthL, this.sizeLimits.minWidth, this.sizeLimits.maxWidth);
            
            if (clampedWidthL !== note.width) {
              var widthDiff = this.resizeStartDimensions.width - clampedWidthL;
              note.posX = this.clamp(
                this.resizeStartDimensions.posX + widthDiff,
                this.posLimits.minX,
                this.posLimits.maxX
              );
              note.width = clampedWidthL;
            }
            break;
          case 'top':
            var newHeightT = this.resizeStartDimensions.height - deltaY;
            var clampedHeightT = this.clamp(newHeightT, this.sizeLimits.minHeight, this.sizeLimits.maxHeight);
            
            if (clampedHeightT !== note.height) {
              var heightDiff = this.resizeStartDimensions.height - clampedHeightT;
              note.posY = this.clamp(
                this.resizeStartDimensions.posY + heightDiff,
                this.posLimits.minY,
                this.posLimits.maxY
              );
              note.height = clampedHeightT;
            }
            break;
          case 'top-left':
            // Manejar ancho (izquierda)
            var newWidthTL = this.resizeStartDimensions.width - deltaX;
            var clampedWidthTL = this.clamp(newWidthTL, this.sizeLimits.minWidth, this.sizeLimits.maxWidth);
            
            if (clampedWidthTL !== note.width) {
              var widthDiffTL = this.resizeStartDimensions.width - clampedWidthTL;
              note.posX = this.clamp(
                this.resizeStartDimensions.posX + widthDiffTL,
                this.posLimits.minX,
                this.posLimits.maxX
              );
              note.width = clampedWidthTL;
            }
            
            // Manejar altura (arriba)
            var newHeightTL = this.resizeStartDimensions.height - deltaY;
            var clampedHeightTL = this.clamp(newHeightTL, this.sizeLimits.minHeight, this.sizeLimits.maxHeight);
            
            if (clampedHeightTL !== note.height) {
              var heightDiffTL = this.resizeStartDimensions.height - clampedHeightTL;
              note.posY = this.clamp(
                this.resizeStartDimensions.posY + heightDiffTL,
                this.posLimits.minY,
                this.posLimits.maxY
              );
              note.height = clampedHeightTL;
            }
            break;
          case 'top-right':
            // Manejar ancho (derecha)
            var newWidthTR = this.resizeStartDimensions.width + deltaX;
            note.width = this.clamp(newWidthTR, this.sizeLimits.minWidth, this.sizeLimits.maxWidth);
            
            // Manejar altura (arriba)
            var newHeightTR = this.resizeStartDimensions.height - deltaY;
            var clampedHeightTR = this.clamp(newHeightTR, this.sizeLimits.minHeight, this.sizeLimits.maxHeight);
            
            if (clampedHeightTR !== note.height) {
              var heightDiffTR = this.resizeStartDimensions.height - clampedHeightTR;
              note.posY = this.clamp(
                this.resizeStartDimensions.posY + heightDiffTR,
                this.posLimits.minY,
                this.posLimits.maxY
              );
              note.height = clampedHeightTR;
            }
            break;
          case 'bottom-left':
            // Manejar ancho (izquierda)
            var newWidthBL = this.resizeStartDimensions.width - deltaX;
            var clampedWidthBL = this.clamp(newWidthBL, this.sizeLimits.minWidth, this.sizeLimits.maxWidth);
            
            if (clampedWidthBL !== note.width) {
              var widthDiffBL = this.resizeStartDimensions.width - clampedWidthBL;
              note.posX = this.clamp(
                this.resizeStartDimensions.posX + widthDiffBL,
                this.posLimits.minX,
                this.posLimits.maxX
              );
              note.width = clampedWidthBL;
            }
            
            // Manejar altura (abajo)
            var newHeightBL = this.resizeStartDimensions.height + deltaY;
            note.height = this.clamp(newHeightBL, this.sizeLimits.minHeight, this.sizeLimits.maxHeight);
            break;
          case 'bottom-right':
            // Manejar ancho (derecha)
            var newWidthBR = this.resizeStartDimensions.width + deltaX;
            note.width = this.clamp(newWidthBR, this.sizeLimits.minWidth, this.sizeLimits.maxWidth);
            
            // Manejar altura (abajo)
            var newHeightBR = this.resizeStartDimensions.height + deltaY;
            note.height = this.clamp(newHeightBR, this.sizeLimits.minHeight, this.sizeLimits.maxHeight);
            break;
        }
      }
    },
    
    stopDrag() {
      if ((this.isDragging || this.isResizing) && this.currentNoteIndex !== null) {
        this.saveNote(this.currentNoteIndex);
      }
      
      // Restaurar el cursor predeterminado
      document.body.style.cursor = '';
      
      this.isDragging = false;
      this.isResizing = false;
      this.resizeDirection = null;
      this.currentNoteIndex = null;
    }
  }
}
</script>

<style scoped>
.sticky-notes-container {
  position: relative;
}

.controls {
  margin-bottom: 10px;
}

.notes-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 100;
}

.sticky-note {
  position: absolute;
  min-width: 200px;
  min-height: 150px;
  max-width: 400px;
  max-height: 600px;
  border-radius: 3px;
  box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  pointer-events: auto;
}

.note-header {
  padding: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(0,0,0,0.1);
}

.note-move-icon {
  cursor: move;
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.note-move-icon i {
  font-size: 18px;
  color: rgba(0,0,0,0.5);
}

.note-title {
  font-weight: bold;
  font-size: 1.1em;
  background: transparent;
  border: none;
  outline: none;
  width: 70%;
}

.note-actions {
  display: flex;
}

.note-content {
  flex: 1;
  padding: 8px;
  resize: none;
  border: none;
  outline: none;
  background: transparent;
  font-family: inherit;
}

.character-counter {
  position: absolute;
  bottom: 5px;
  right: 10px;
  font-size: 10px;
  color: rgba(0,0,0,0.5);
  background-color: rgba(255,255,255,0.7);
  padding: 2px 5px;
  border-radius: 3px;
}

.color-option {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
  border: 1px solid rgba(0,0,0,0.1);
}

/* Manejadores de redimensión */
.resize-handle {
  position: absolute;
  background-color: transparent;
  z-index: 10;
}

.resize-handle-right {
  top: 0;
  right: 0;
  width: 5px;
  height: 100%;
  cursor: e-resize;
}

.resize-handle-bottom {
  bottom: 0;
  left: 0;
  height: 5px;
  width: 100%;
  cursor: s-resize;
}

.resize-handle-left {
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  cursor: w-resize;
}

.resize-handle-top {
  top: 0;
  left: 0;
  height: 5px;
  width: 100%;
  cursor: n-resize;
}

/* Esquinas */
.resize-handle-top-left {
  top: 0;
  left: 0;
  height: 10px;
  width: 10px;
  cursor: nw-resize;
}

.resize-handle-top-right {
  top: 0;
  right: 0;
  height: 10px;
  width: 10px;
  cursor: ne-resize;
}

.resize-handle-bottom-left {
  bottom: 0;
  left: 0;
  height: 10px;
  width: 10px;
  cursor: sw-resize;
}

.resize-handle-bottom-right {
  bottom: 0;
  right: 0;
  height: 10px;
  width: 10px;
  cursor: se-resize;
}
.notificacion {
    text-align: center;
    font-size: 50px;
    height: 40px;
    color: rgba(var(--vs-primary), 0.7);
    margin-left: 10px;
    cursor: pointer;
    position: relative;
}

</style>


