<template>
<vx-card class="p-2" title="RTC" collapse-action data-action="collapse">

    <vs-popup fullscreen title="Información RTC - SP" :active.sync="info_rtc.temp_sql.activo">
        <h4>{{info_rtc.temp_sql.nombre}}</h4>
        <div style="clear:both"></div>
        <codemirror :value="limpiar2(info_rtc.temp_sql.sql)" :options="cmOptions"></codemirror>
        <vs-chip color="success" v-for="(item,key) in extraer(info_rtc.temp_sql.nombre)" :key="key">
            {{item.base}} - {{item.nombre_tabla}}
        </vs-chip>
    </vs-popup>

    <vs-popup fullscreen title="Información RTC - Tabla" :active.sync="info_rtc.temp_tabla.activo">
        <h4>{{info_rtc.temp_tabla.nombre}}</h4>
        <div style="clear:both"></div>
        <codemirror :value="limpiar2(info_rtc.temp_tabla.ddl)" :options="cmOptions"></codemirror>
    </vs-popup>

    <vs-popup fullscreen title="Información RTC - Pase a QA" :active.sync="pase.activo">
        <h4>Estructura</h4>
        <div style="clear:both"></div>
        <codemirror :value="limpiar2(pase.sql)" :options="cmOptions"></codemirror>
    </vs-popup>

    <vs-popup fullscreen title="Información RTC" :active.sync="popupActive">
        <p></p>
        <h5>RTC</h5>
        {{info_rtc.rtc}}
        <p></p>
        <br>
        <h5>Nombre</h5>
        {{info_rtc.nombre}}

        <p></p>
        <br>
        <!-- <h5>Apis</h5> -->

        <vs-button :disabled="seleccion_tablas.length==0 && seleccion_sp.length==0" @click="pase_qa()">Pase a QA</vs-button>
        <vs-tabs>
            <vs-tab @click="colorx = 'success'" label="GIT">
                <vs-table :data="info_rtc.gitlab">
                    <template slot="thead">
                        <vs-th>GIT</vs-th>
                    </template>
                    <template slot-scope="{data}">
                        <vs-tr :key="indextr" v-for="(item, indextr) in data">
                            <vs-td>
                                {{item.gitlab}}
                            </vs-td>
                        </vs-tr>
                    </template>
                </vs-table>

            </vs-tab>
            <vs-tab @click="colorx = 'success'" label="Apis">
                <vs-collapse accordion>

                    <vs-collapse-item v-for="(item,key) in this.info_rtc.apis" :key="key">
                        <div slot="header">
                            {{item.archivo}}
                        </div>
                        <vs-table :data="item._level">
                            <template slot="thead">
                                <vs-th>Linea</vs-th>
                                <vs-th>Controlador</vs-th>
                                <vs-th>Modelo</vs-th>
                                <vs-th>Comentario</vs-th>
                            </template>

                            <template slot-scope="{data}">
                                <vs-tr :key="indextr" v-for="(item, indextr) in data">
                                    <vs-td>
                                        {{item.linea}}
                                    </vs-td>
                                    <vs-td>
                                        {{item.controlador}}
                                    </vs-td>
                                    <vs-td width="50px">
                                        {{item.modelo}}
                                    </vs-td>
                                    <vs-td>
                                        {{item.controlador_comentario}}
                                    </vs-td>

                                </vs-tr>
                            </template>

                        </vs-table>
                    </vs-collapse-item>
                </vs-collapse>
            </vs-tab>

            <vs-tab @click="colorx = 'success'" label="SQL">
                <vs-table stripe multiple v-model="seleccion_sp"  :data="this.info_rtc.sql">
                    <template slot="thead">
                        <vs-th>SP</vs-th>
                        <vs-th>Modificado</vs-th>
                        <vs-th>Instrucción</vs-th>
                    </template>

                    <template slot-scope="{data}">
                        <vs-tr :key="indextr" v-for="(item, indextr) in data">
                            <vs-td>
                                {{item.nombre_sp}}
                            </vs-td>
                            <vs-td>
                                {{item.modificado}}
                            </vs-td>

                            <vs-td width="60px">
                                <vs-button style="display:inline;margin-right:5px" color="primary" radius icon-pack="fas" icon="fa-file-code" @click="info_rtc.temp_sql.nombre = item.nombre_sp;info_rtc.temp_sql.sql = item.instruccion_sp;info_rtc.temp_sql.activo=true"></vs-button>
                            </vs-td>

                        </vs-tr>
                    </template>
                </vs-table>
            </vs-tab>

            <vs-tab @click="colorx = 'success'" label="Tablas">
                <vs-table stripe multiple v-model="seleccion_tablas" @selected="handleSelected" :data="this.info_rtc.tablas">
                    <template slot="thead">
                        <vs-th>Base de Datos</vs-th>
                        <vs-th>Nombre Tabla</vs-th>
                        <vs-th>Modificado</vs-th>
                        <vs-th>Acciones</vs-th>
                    </template>

                    <template slot-scope="{data}">
                        <vs-tr :key="'tablas_'+indextr" v-for="(item, indextr) in data" :data="item">
                            <vs-td>
                                {{item.base}}
                            </vs-td>
                            <vs-td>
                                {{item.nombre_tabla}}
                            </vs-td>
                            <vs-td>
                                {{item.modificado}}
                            </vs-td>

                            <vs-td width="60px">
                                <vs-button style="display:inline;margin-right:5px" color="primary" radius icon-pack="fas" icon="fa-file-code" @click="ver_tabla(item)"></vs-button>
                            </vs-td>

                        </vs-tr>
                    </template>

                </vs-table>
                <!-- </vs-collapse-item>
                </vs-collapse> -->
            </vs-tab>
        </vs-tabs>

    </vs-popup>

    <vs-input placeholder="RTC" class="w-full mb-4" type="number" v-model="info.rtc" />
    <vs-input placeholder="Nombre" class="w-full mb-4" v-model="info.nombre" />
    <!-- <vs-input placeholder="Gitlab"  class="w-full mb-4" v-model="info.gitlab" /> -->
    <vs-textarea label="Gitlab" v-model="info.gitlab" />
    <v-select :options="listado_aplicativo" v-model="info.aplicativo" />
    <!-- {{info}} -->
    <!-- <vs-textarea label="Message" v-model='msg' /> -->
    <vs-button class="mr-4" @click="guardar_rtc()">Guardar</vs-button>
    <vs-button type="border" color="warning" @click="info.rtc = info.nombre = info.gitlab =''" class="mt-3">Cancel</vs-button>
    <vs-divider></vs-divider>

    <div class="vx-row text-left">
        <div class="vx-col w-full ">
            <vs-table :data="rtcs">

                <template slot="thead">
                    <vs-th>RTC</vs-th>
                    <vs-th>Nombre</vs-th>
                    <vs-th>Gitlab</vs-th>
                    <vs-th>Aplicativo</vs-th>
                    <vs-th>Corporativo</vs-th>
                    <vs-th>Acción</vs-th>
                </template>

                <template slot-scope="{data}">
                    <vs-tr :key="indextr" v-for="(item, indextr) in data">

                        <vs-td width="50px">
                            {{item.rtc}}
                        </vs-td>

                        <vs-td>
                            {{item.nombre_proyecto}}
                        </vs-td>

                        <vs-td>
                            <vs-chip v-for="(item,key) in item._level" :key="key">
                                {{item.gitlab}}
                            </vs-chip>
                            <!-- <p><small>{{item.url_tiempo}}ms</small></p> -->
                        </vs-td>

                        <vs-td width="50px">
                            {{item.id_aplicativo}}
                        </vs-td>

                        <vs-td width="60px">
                            {{item.corporativo}}
                        </vs-td>

                        <vs-td width="120px">
                            <vs-button style="display:inline;margin-right:5px" color="primary" radius icon-pack="fas" icon="fa-file-code" @click="analizar(item.id)"></vs-button>
                            <vs-button style="display:inline;" color="success" radius icon-pack="fas" icon="fa-search" @click="ver(item)"></vs-button>
                        </vs-td>
                    </vs-tr>
                </template>

            </vs-table>

        </div>

    </div>

    <!-- Support Tracker Meta Data -->

</vx-card>
</template>

<script>
import vSelect from 'vue-select'
import {
    codemirror
} from 'vue-codemirror'

// require styles
import 'codemirror/lib/codemirror.css'
// import sqlFormatter from 'sql-formatter-plus';
export default {
    data() {
        return {
            cmOptions: {
                // codemirror options
                tabSize: 4,
                mode: 'text/x-sql',
                theme: 'base16-dark',
                lineNumbers: true,
                line: true,
                readOnly:true
                // more codemirror options, 更多 codemirror 的高级配置...
            },

            listado_aplicativo: [],

            rtcs: [],
            collapse: false,
            info: {
                rtc: null,
                nombre: null,
                gitlab: null,
                aplicativo: null
            },

            popupActive: false,
            info_rtc: {
                rtc: null,
                nombre: null,
                gitlab: [],
                apis: [],
                sql: [],
                tablas: [],
                temp_sql: {
                    nombre: null,
                    sql: null,
                    activo: false
                },
                temp_tabla: {
                    nombre: null,
                    ddl: null,
                    activo: false
                }
            },

            seleccion_tablas: [],
            seleccion_sp:[],

            pase: {
                activo: false,
                sql: null
            }
        }
    },
    components: {
        'v-select': vSelect,
        codemirror
    },
    methods: {
        // -----------------------------------------------------------------------------------------------
        // Consultar
        // -----------------------------------------------------------------------------------------------
        cargar_aplicativos() {
            this.axios
                .post("/app/administracion/obtener_aplicativos", {})
                .then(resp => {
                    // console.log(resp.data.json)
                    this.listado_aplicativo = resp.data.json.filter(data => data.Activo == true).map(data => {
                        return {
                            value: data.IdAplicativo,
                            label: data.Nombre
                        }
                    });
                });
        },

        consulta_rtc() {
            this.axios
                .post("/app/documentacion/consulta_rtc", {})
                .then(resp => {
                    // console.log(resp.data.json)
                    this.rtcs = resp.data.json
                });
        },

        // -----------------------------------------------------------------------------------------------
        // GUARDAR
        // -----------------------------------------------------------------------------------------------
        guardar_rtc() {
            this.axios
                .post("/app/documentacion/guardar_RTC", {
                    rtc: this.info.rtc,
                    nombre: this.info.nombre,
                    gitlab: this.info.gitlab,
                    IdAplicativo: (this.info.aplicativo) ? this.info.aplicativo.value : null
                })
                .then(() => {
                    // console.log(resp)
                    // this.listado_aplicativo = resp.data.json;
                })
                .catch(() => {
                })
        },
        // -----------------------------------------------------------------------------------------------
        // EDITAR
        // -----------------------------------------------------------------------------------------------

        // -----------------------------------------------------------------------------------------------
        // ELIMINAR
        // -----------------------------------------------------------------------------------------------

        // -----------------------------------------------------------------------------------------------
        // OTROS
        // -----------------------------------------------------------------------------------------------
        handleSelected(tr) {
            this.$vs.notify({
                title: `Selected ${tr}`,
                text: `Email: ${tr}`
            })
        },
        analizar(id_rtc) {
            this.axios
                .post("/app/documentacion/analizar_rtc", {
                    id: id_rtc
                })
                .then(() => {
                    // console.log(resp)
                    // this.listado_aplicativo = resp.data.json;
                })
                .catch(() => {
                })
        },

        ver(item) {
            this.seleccion_tablas = []
            this.info_rtc.nombre = item.nombre_proyecto
            this.info_rtc.rtc = item.rtc
            this.info_rtc.gitlab = item._level.map(data => {
                return {
                    gitlab: data.gitlab,
                    fecha: data.fecha_gitlab
                }
            })

            this.info_rtc.apis = []
            this.info_rtc.sql = []
            this.info_rtc.tablas = []
            this.info_rtc.temp_sql.nombre = null
            this.info_rtc.temp_sql.sql = null
            this.info_rtc.temp_sql.activo = false

            this.axios
                .post("/app/documentacion/consulta_rtc_apis", {
                    rtc: item.rtc
                })
                .then((resp) => {
                    this.info_rtc.apis = resp.data.json
                    return this.axios.post("/app/documentacion/consulta_rtc_sp", {
                        rtc: item.rtc
                    })
                })
                .then(resp => {
                    this.info_rtc.sql = resp.data.json
                    return this.axios.post("/app/documentacion/consulta_rtc_tablas", {
                        rtc: item.rtc
                    })
                    // this.rtcs = resp.data.json
                })
                .then(resp => {
                    this.info_rtc.tablas = resp.data.json
                })
                .catch(() => {
                })

            this.popupActive = true
        },

        ver_tabla(tabla, callback) {
            return new Promise((resolve, reject) => {
                this.axios.post("/app/documentacion/Consulta_RTC_TABLAS_DDL", {
                        tabla: tabla.nombre_tabla,
                        tabla_base: tabla.base
                    })
                    .then(resp => {
                        let respuesta = 'USE ' + tabla.base + ';\nGO\n' + resp.data.json[0].Item
                        // console.log(resp.data.json[0].Item)
                        if (!callback) {
                            this.info_rtc.temp_tabla.nombre = tabla.nombre_tabla
                            this.info_rtc.temp_tabla.ddl = respuesta
                            this.info_rtc.temp_tabla.activo = true
                        } else {
                            callback(respuesta)
                        }

                        resolve(respuesta)
                    })
                    .catch(err => {
                        reject(err)
                    })
            })

        },

        limpiar(texto) {
            if (!texto) return null
            return texto.replace(/\\n/g, '<br>').replace(/\\r/g, '')
        },

        limpiar2(texto) {
            if (!texto) return null
            return texto.replace(/\\n/g, "\n").replace(/\\r/g, '')
        },

        extraer(sp) {
            
            return this.info_rtc.tablas.filter(data => data._level.filter(data_ => data_.sp == sp).length > 0)
        },

        pase_qa() {
            let conteo_ejecutar = 0
            this.pase.sql = ''
            this.seleccion_tablas.map(data => {
                conteo_ejecutar++
                // console.log(data.nombre_tabla)
                this.ver_tabla({
                    nombre_tabla: data.nombre_tabla,
                    base: data.base
                }, (data) => {
                    conteo_ejecutar--
                    this.pase.sql +='\n--======================================================================================\n--======================================================================================\n'+ data
                    // console.log(conteo_ejecutar)
                    //Finalizando todos
                    if (conteo_ejecutar == 0) {
                        // console.log('finalizando')
                        this.pase.activo = true
                    }
                })
            })
            // conteo_ejecutar= 0

            // this.ver_tabla()
        }

    },
    mounted() {
        this.cargar_aplicativos()
        this.consulta_rtc()
    }
}
</script>

<style>
.CodeMirror {
    height: 600px;
}
</style>
