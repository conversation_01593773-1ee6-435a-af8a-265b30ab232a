
<template>
        <div class="container">


                <div class="a-DqW6q-0  pt-2 pl-4">

                        <h3>Selección Periodo de cierre</h3>
                        <br>
                        <br>
                        <vs-row>
                                <vs-col vs-w="2">
                                        <SM-Buscar
                                          v-model="Periodo"
                                          label="Periodo Cierre"
                                          api="app/v1_OrdenCompra/ConsultarPeriodoCierre"
                                          :api_campo_respuesta_mostrar="['Codigo', 'Descripcion']"
                                          :api_campos="['Codigo', 'Descripcion']"
                                          :api_titulos="['Codigo', 'Descripcion']"
                                          api_campo_respuesta="Codigo"
                                          :api_preload="true"
                                          :disabled_texto="true"
                                          :callback_buscar="ConsultarPeriodo" />
                                </vs-col>
                                <vs-col vs-w="6" class="pl-4">

                                        <vs-input label="Descrición Periodo" class="w-full" v-model="this.DescripcionPeriodo"
                                                readonly />

                                </vs-col>
                        </vs-row>


                </div>
                <div class="a-K89z8-0">

                        <vs-row>
                                <vs-col>
                                        <div class="example ex1">
                                                <h3>Selección de tipo de busqueda existencia</h3>
                                                <br>
                                                <br>
                                                <label class="radio blue">
                                                        <input v-model="BusquedaTodos" type="radio" name="group1" value=1 />
                                                        <span>Todos</span>
                                                </label>
                                                <label class="radio blue">
                                                        <input v-model="BusquedaTodos" type="radio" name="group1" value=0 />
                                                        <span>Negativos</span>
                                                </label>
                                                <p :style="{ color: $options.COLOR }">
                                                </p>
                                        </div>
                                </vs-col>

                        </vs-row>

                </div>
                <div class="a-Og6Lg pt-4 pl-4">

                        <vs-row>
                                <vs-col vs-w="2">
                                       <SM-Buscar
                                         v-model="Bodega"
                                         label="Codigo Bodega"
                                         api="app/v1_OrdenCompra/ConsultarBodegas"
                                         :api_campo_respuesta_mostrar="['Bodega', 'Nombre']"
                                         :api_campos="['Bodega', 'Nombre']"
                                         :api_titulos="['Bodega', 'Nombre']"
                                         api_campo_respuesta="Bodega"
                                         :api_preload="true"
                                         :disabled_texto="true"
                                         :callback_buscar="ConsultarBodega" />
                                </vs-col>
                                <vs-col vs-w="6" class="pl-4">

                                        <vs-input label="NombreBodega" class="w-full" v-model="this.NombreBodega" readonly />

                                </vs-col>
                        </vs-row>

                </div>
                <div class="a-K89z8-1">

                        <vs-row class="pt-4">

                                <!-- <h3 class="pl-4">Generar Impresión</h3>
                                <br>
                                <br>

                                <br> -->
                                <vs-col vs-type="flex" vs-w="12">
                                        <vs-col vs-type="flex" class="ml-6">
                                                <vs-button class="block" @click.native="EjecutarCierre()">
                                                        <i class="fas fa-file-pdf height:25px"></i>
                                                        Generar
                                                </vs-button>
                                        </vs-col>
                                </vs-col>
                        </vs-row>
                </div>
        </div>
</template>


<script>

export default {
        data() {
                return {
                        periodoCierreSeleccionado: 0,
                        DescripcionPeriodo: '',
                        Periodo: 0,
                        BusquedaTodos: '',
                        BusquedaNegativos: '',
                        Bodega: '',
                        NombreBodega: ''
                }
        },
        methods: {
                ConsultarPeriodo(datos) {
                        
                        this.DescripcionPeriodo = datos.descripcion
                        
                },
                ConsultarBodega(datos) {
                        
                        this.NombreBodega = datos.Nombre
                },
                async EjecutarCierre(){

                        
                        
                        let postData = {
                                Bodega: this.Bodega ,
                                Periodo: this.Periodo,
                                TipoBusqueda : this.BusquedaTodos
                        }
                                
                        this.$reporte_modal({
                                Nombre: 'ReporteCierreExistenciasInventario',
                                Opciones: {
                                ...postData
                                },
                                Formato:'EXCEL'
                        })


                }

        }


}



</script>

<style scoped>
* {
        box-sizing: border-box;
        padding: 0;
        margin: 0;
}

/* .container {
        display: grid;
        grid-gap: 5px;
        width: 100%;
        height: 100%;
        grid-template-areas: "a-pZBoJ-0"
                "a-VvnoA"
                "a-pZBoJ-1";
        grid-template-columns: 0.8fr;
        grid-template-rows: 20fr 20fr 10fr;
} */


.container {
        display: grid;
        grid-gap: 5px;
        width: 100%;
        height: 100%;
        grid-template-areas: "a-DqW6q-0 a-K89z8-0"
                "a-DqW6q-0 a-K89z8-0"
                "a-DqW6q-0 a-K89z8-0"
                "a-Og6Lg a-Og6Lg"
                "a-K89z8-1 a-K89z8-1"
                "a-K89z8-1 a-K89z8-1";
        grid-template-columns: 0.40fr 0.40fr;
        grid-template-rows: 10fr 10fr 20px 20fr 1fr 10fr;
}

.container>div {
        border: 1px solid #888;
}

.a-DqW6q-0 {
        grid-area: a-DqW6q-0;
}

.a-K89z8-0 {
        grid-area: a-K89z8-0;
}

.a-Og6Lg {
        grid-area: a-Og6Lg;
}

.a-K89z8-1 {
        grid-area: a-K89z8-1;
}

.container {
        max-width: 100%;
}


.example {
        margin: 10px;
}

.example input {
        display: none;
}

.example label {
        margin-right: 20px;
        display: inline-block;
        cursor: pointer;
}

.ex1 span {
        display: block;
        padding: 15px 20px 15px 45px;
        border: 2px solid #ddd;
        border-radius: 5px;
        position: relative;
        transition: all 0.25s linear;
}

.ex1 span:before {
        content: '';
        position: absolute;
        left: 5px;
        top: 50%;
        -webkit-transform: translatey(-50%);
        transform: translatey(-50%);
        width: 15px;
        height: 15px;
        border-radius: 50%;
        background-color: #ddd;
        transition: all 0.25s linear;
}

.ex1 input:checked+span {
        background-color: #fff;
        box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.1);
}

.ex1 .blue input:checked+span {
        color: rgb(96, 162, 203);
        border-color: rgb(143, 143, 160);
}

.ex1 .blue input:checked+span:before {
        background-color: rgb(98, 155, 209);
}
</style>