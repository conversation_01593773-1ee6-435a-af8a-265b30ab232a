<template>
<vx-card title="Producto">

    <div class="content content-pagex">
    
        <div class="algolia-header mb-4">
            <div class="flex justify-between items-end flex-grow">
                <div class="ais-Stats">
                    <p class="font-semibold md:block hidden">
                        <!-- {{producto.length }} resultados encontrados -->
                    </p>
                </div>
                <div class="flex flex-wrap">
                    <div v-if="permiso.nuevo_producto"  class="ais-SortBy">
                        <div class="con-select mr-2 vs-input-shadow-drop vs-select-no-border">
                            <div class="append-text btn-addon">
                                <vs-button color="success" icon-pack="feather" icon="icon-plus" style="float:left" type="filled" @click="nuevo()">Nuevo Producto</vs-button>
                            </div>
                        </div>
                    </div>
                    <!-- <div>
                        <span class="p-2 shadow-drop rounded-lg d-theme-dark-bg cursor-pointer feather-icon select-none relative">
                            <i class="feather icon-grid text-primary" style="font-size: 24px;"></i>
                        </span>
                        <span class="p-2 ml-4 shadow-drop rounded-lg d-theme-dark-bg cursor-pointer hidden sm:inline-flex feather-icon select-none relative">
                            <i class="feather icon-list feather-list" style="font-size: 24px;"></i>
                        </span>
                    </div> -->
                </div>
            </div>
        </div>

        <div class="ais-Hits">

            <!-- Listado de Clase y Sub Clase. -->
            <!-- <div class="table-responsive">
                <div class="vs-sidebar--items">
                    <div class="p-6 filter-container">
                        <h6 class="font-bold mb-3">Clase:</h6> 
                        <div class="flex flex-wrap">
                            <div class="w-full">
                                <vs-radio style="float:left;padding:1px 5px" v-for="(fila, index) in ListadoClaseGeneral" :key="index" v-model="selectClaseGeneral" :vs-name="'Catalogo'+index" :vs-value="fila.Codigo" @input="onChangeClaseGeneral" >
                                    {{fila.Nombre}}
                                </vs-radio>
                            </div>
                        </div>
                        <vs-divider></vs-divider>
                        <h6 class="font-bold mb-4">Categorias:</h6>
                        <div class="flex flex-wrap">
                            <div class="w-full">
                                <vs-radio  style="float:left;padding:1px 5px" v-for="(categoria, index) in ListadoCategoriaGeneral" :key="index" :vs-name="'Categoria_'+index" v-model="selectCategoriaGeneral" :vs-value="categoria.Codigo" @input="cargar_producto">
                                    {{categoria.Nombre}}
                                </vs-radio>                                  
                            </div>
                        </div>
                        <vs-divider></vs-divider>
                    </div>
                </div>
            </div> -->

            <!-- Busqueda Producto. -->
            <div class="ais-SearchBox">
                <div>
                    <div class="relative mb-8">
                        <div class="vs-component vs-con-input-label vs-input w-full vs-input-shadow-drop vs-input-no-border d-theme-input-dark-bg vs-input-primary">
                            <div class="vs-con-input">
                                <input type="text" class="vs-inputx vs-input--input large" placeholder="Buscar" style="border: 1px solid rgba(0, 0, 0, 0.2);" v-model="producto_buscar" @change="cargar_producto" >
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Listado de Producto -->
            <div class="flex flex-wrap">

                <div pagination class="lg:w-1/3 sm:w-1/2 w-full" style="padding:0px 8px" v-for="(tr, indextr) in producto_filtro" :key="indextr">
                    <div class="vx-card grid-view-item mb-base overflow-hidden">
                        <!---->
                      
                        <div class="vx-card__collapsible-content vs-con-loading__container">

                            <div class="item-img-container bg-white h-64 flex items-center justify-center mb-4 cursor-pointer">
                                <!-- <img :src="'data:image/png;base64, ' +tr.Imagen" v-if="tr.Imagen"  alt="" class="grid-view-img px-4" style="max-height:250px"/>
                                 -->
                                <expandable-image class="image" :src="'data:image/png;base64, ' +tr.Imagen" v-if="tr.Imagen"  >
                                </expandable-image>
                                <img v-else src="@/assets/images/others/notfound.png"  alt="" class="grid-view-img px-4" style="max-height:250px"  />
                            
                            </div>

                            <div  style="display: block" class="flex flex-wrap">
                                <h6 style="display: block;margin: 0 0 0 auto" class="font-bold">COSTO PROMEDIO</h6>  
                            </div> 

                            <div class="item-details px-4">
                                <div class="flex justify-between items-center">
                                    <div class="text-warning border border-solid border-warning flex py-1 px-2 rounded">
                                        <span class="text-sm mr-1">{{tr.Clase}}</span>
                                        <span class="feather-icon select-none relative">
                                            <i class="feather icon-star"></i>
                                        </span>
                                    </div>
                                    <h3 class="font-bold">{{tr.Codigo}} </h3>
                                    <h6 class="font-bold">Q {{tr.CostoPromedio}}</h6>
                                </div>
                                <div class="my-4">
                                    <h6 style="vertical-align: inherit;" class="font-semibold mb-1 hover:text-primary cursor-pointer">{{tr.Nombre}}</h6>
                                    <p class="item-description text-sm">{{tr.NombreGenerico}}</p>

                                </div>
                            </div>

                            <div class="flex flex-wrap">
                                <div  @click="productomantenimiento(tr)" class="item-view-secondary-action-btn bg-primary p-3 flex flex-grow items-center justify-center text-white cursor-pointer">
                                    <span class="feather-icon select-none relative">
                                        <i class="feather icon-edit"></i>
                                    </span>
                                    <span class="text-sm font-semibold ml-2" >EDITAR PRODUCTO</span>
                                </div>
                            </div>
                            
                        </div>

                        <div class="vx-card__code-container collapsed" style="max-height: 0px; display: none;">
                            <div class="code-content">
                                <pre class="language-markup"><code class="language-markup"></code></pre>
                            </div>
                        </div>

                    </div>
                </div>

            </div>

        </div>

    </div>

        <componenteProductos v-if="show" ref="productos" :callback="cargar_producto" :cerrar="()=>show=false" />
        <componenteProductosMant v-if="showprecio" ref="productomantenimiento" :callback="cargar_producto" :callback_nuevo="nuevo" :cerrar="()=>showprecio=false" />

</vx-card>
</template>

<script>
//barcode - npm install @chenfengyuan/vue-barcode vue
import Vue from 'vue';
import VueBarcode from '@chenfengyuan/vue-barcode';

Vue.component(VueBarcode.name, VueBarcode);

export default {
    data() {
        return {
            producto: [],
            producto_buscar: '',
            show: false, //mostrar las opciones del producto
            showprecio: false, //Opcion Precio del Producto
            selectClaseGeneral: '',
            selectCategoriaGeneral:'',
            ListadoClaseGeneral: [],
            ListadoCategoriaGeneral: [],
            permiso:{
                nuevo_producto:false,
            },
        }
    },
    computed: {
        producto_filtro() {
            if (this.producto_buscar.trim() == '') {
                return this.producto
            } else {
                return this.producto.filter(data => data.Codigo == this.producto_buscar.trim() || data.Nombre.toUpperCase().indexOf(this.producto_buscar.toUpperCase()) >= 0 || data.Descripcion.toUpperCase().indexOf(this.producto_buscar.toUpperCase()) >= 0 || data.NombreGenerico.toUpperCase().indexOf(this.producto_buscar.toUpperCase()) >= 0)
            }
        }
    },
    methods: {
        cargar_producto() {
            this.show=false
            
            this.producto = []
            this.axios.post('/app/inventario/invProductolist', {
                    Pagina: 1,
                    Busqueda: '',
                    //Clase: this.selectClaseGeneral,
                    //SubClase: this.selectCategoriaGeneral,
                    Nombre: this.producto_buscar.trim(),
                })
                .then(resp => {

                     
                     if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        this.producto = []
                        resp.data.json.map(data => {
                            this.producto.push({...data})
                        })
                     } 
                     else {
                        
                        this.producto = []
                        this.$vs.notify({
                            color: 'warning',
                            title: 'Producto',
                            text: (resp.data.codigo < 0) ? resp.data.descripcion : 'Sin Registros',
                        })
                    }

                })
                .catch(() => {
                    //this.producto = []
                    
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Producto',
                        text: 'Error al cargar los Productos.',
                    })
                })
        },
        cargar_claseGeneral() {
            this.axios.post('/app/inventario/invClaselist', {})
                .then(resp => {
                    this.ListadoClaseGeneral = resp.data.json
                })
                .catch(() => { })
        }, //Fin de metodo Clase
        cargar_categoriaGeneral() {
            this.axios.post('/app/inventario/invCategorialist', {
                    //Empresa: 'MED',
                    claseid: this.selectClaseGeneral
                })
                .then(resp => {
                    this.ListadoCategoriaGeneral = resp.data.json
                })
                .catch(() => { })
        }, //Fin de metodo Categoria       
        onChangeClaseGeneral() {
            this.producto=[]
            this.ListadoCategoriaGeneral = []
            this.selectCategoriaGeneral=''
            this.cargar_categoriaGeneral()
            this.show=false
        },  
        nuevo() {
            this.show = true
            this.showprecio = false
        },
        productomantenimiento(opcion) {
            this.show=false
            this.showprecio = true
            setTimeout(()=>{
                this.$refs.productomantenimiento.opciones(opcion)
            },100)
        },
        productos() {
            this.show=false
            // this.$refs.productos.nuevo()
        },
    },
    components: {
        'componenteProductos': () => import('./INV007_Producto'),
        'componenteProductosMant': () => import('./INV007_Mantenimiento')
    },
    mounted() {
            this.cargar_claseGeneral() 
            this.$validar_funcionalidad('/INV/INV007','NUEVO_PRODUCTO',(d)=>{this.permiso.nuevo_producto=d.status})      
    },
}
</script>

<style>
.button {
    margin: 0px !important;
}

.btnx {
    margin-left: 10px !important;
    background: red;
    border-radius: 5px 0px 0px 5px;
}

.btn-drop {
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: red;

}

.menu-funcionalidad {
    /* padding-top:0 !important; */
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    padding: 15px;
    display: inline;
    float: right;
    border-radius: 10px;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 10px;
}

.menu-funcionalidad td {
    text-align: center;
    padding-right: 10px;
}

.vs-switch {
    height: 30px;
    width: 60px !important;
}

.vs-circle-switch {
    height: 25px;
    width: 25px;
}

.vs-switch--icon {
    font-size: 15px
}

.vs-switch.vs-switch-active .vs-switch--circle {
    margin-left: calc(100% - 30px);
}


.archivos .w-full {
    /* height: 150px !important; */
    /* display: flex; */
    position: relative;
    padding: 2px;
}

.archivos button {
    /* display: flex; */
    /* align-items: center; */
    /* justify-content: center; */
    /*margin: auto;
    position: absolute;
    top: 105px;
    left: calc(50% - 20px)*/
    margin: auto;
    position: absolute;
    top: 105px;
    left: calc(50% - 20px);
    /* margin-left: 2px; */
     /* align-items: center; */
}

.archivos img {
    height: 250px
}

.archivos .expandable-image {
    height: 250px
}

</style>
