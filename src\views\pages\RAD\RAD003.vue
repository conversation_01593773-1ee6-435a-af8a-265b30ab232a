<template>
<vx-card title="Plantilla">
    <buscador ref="buscar_productos" buscador_titulo="Buscador / Productos" :api="'app/radiologia/RadBusquedaProductos'" :campos="['Codigo','Nombre']" :titulos="['Código','Producto']" :multiselect="false" :api_validar_campo="true" :api_reload="true" />

    <!-- Nueva pantilla desde el listado de productos -->
    <vs-popup classContent="popup-example" title="Nueva Plantilla" :active.sync="show_newPlantilla">
        <vs-input label="Tipo de la Plantilla" class="w-full" v-model="edit.Tipo" placeholder="F" maxlength="2"/>
        <br>
        <vs-input label="Nombre de la Plantilla" class="w-full" v-model="edit.Nombre" />
        <br>
        <vue-editor v-model="edit.Detalle" :editorToolbar="customToolbar"></vue-editor>
        <hr>                          
        <br>        
        <vs-input label="Código:" class="w-full"   v-model="Edicion.CodProducto"  disabled />
        <vs-input label="Producto:" class="w-full" v-model="Edicion.NombreProducto"  disabled />
        <vs-button color="success"  type="border" style="float:right" v-on:click="crear_plantilla_producto();">Crear</vs-button>
    </vs-popup>

    <!-- Editar una plantilla -->
    <vs-popup classContent="popup-example" title="Mostrar Plantilla" :active.sync="Ventana_Editar_Plantilla">
        <vs-input label="Tipo de la Plantilla" class="w-full" v-model="Edicion.Tipo" placeholder="F" maxlength="2" disabled />
        <br>
        <vs-input label="Nombre de la Plantilla" class="w-full" v-model="Edicion.Titulo" />
        <br>
        <vue-editor v-model="Edicion.Detalle" :editorToolbar="customToolbar"></vue-editor>
        <hr>
        <br>
        
        <vs-input label="Código:" class="w-full"   v-model="Edicion.CodProducto"  disabled />
        <vs-input label="Producto:" class="w-full" v-model="Edicion.NombreProducto"  disabled />
        
        
        <vs-button color="success"  type="border" style="float:right" v-on:click="editar_plantilla();">Editar</vs-button>


    </vs-popup>

    <!-- Plantillas relacionadas aún producto -->
    <vs-popup classContent="popup-example" title="Mostrar Plantilla" :active.sync="Ventana_Detalle_Producto">
        <vs-row class="w-full">
            <vs-button color="success" type="border" style="float:left" 
                       v-on:click="show_newPlantilla=true; edit.Tipo=''; edit.Detalle=''; edit.Nombre='';">
                      Nueva Plantilla
            </vs-button>
        </vs-row>
        <div class="w-full md:w-full lg:w-full xl:w-full">
            <vs-table2 ref="tablacargos" max-items="10" search pagination :data="ListadoPlanitallProducto">
                <template slot="thead">
                    <th order="Codigo">Tipo</th>
                    <th order="Nombre">Titulo Informe</th>
                    <th>Acciones</th>
                </template>
                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data" @click="Edicion.Detalle =tr.Detalle; Ventana_Editar_Plantilla=true; Edicion.Titulo = tr.TituloInforme; Edicion.Tipo = tr.Tipo ">
                        <vs-td2 width="10px">
                            {{ tr.Tipo }}
                        </vs-td2>
                        <vs-td2>
                            {{ tr.TituloInforme }}                                                    
                        </vs-td2>                                  
                        <vs-td style="text-align: center;" width="5px">
                            <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" @click="Edicion.Detalle =tr.Detalle; Ventana_Editar_Plantilla=true; Edicion.Titulo = tr.TituloInforme; Edicion.Tipo = tr.Tipo "></vs-button>
                                                                                                                  </vs-td>
                    </tr>                                                                                                                                  
                </template>
            </vs-table2>
        </div>
    </vs-popup>
    <!-- Nueva plantilla -->
    <vs-popup classContent="popup-example" title="Nueva Plantilla" :active.sync="show_newEdit">
        <vs-input label="Tipo de la Plantilla" class="w-full" v-model="edit.Tipo" placeholder="F" maxlength="2" :disabled="type_newEdit==2" />
        <br>
        <vs-input label="Nombre de la Plantilla" class="w-full" v-model="edit.Nombre" />
        <br>
        <vue-editor v-model="edit.Detalle" :editorToolbar="customToolbar"></vue-editor>
        <hr>                  
        <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_productos()" icon="icon-plus" v-if="type_newEdit==1"></vs-button>
        <div class="demo-alignment" style="margin-top:2px">
            <vs-chip v-if="edit.productos_seleccion.Nombre" @click="producto_eliminar()" color="primary" style="margin:1px" :closable="type_newEdit==1">
                {{edit.productos_seleccion.Nombre}}
            </vs-chip>
        </div>
        <hr style="margin-top:3px;margin-bottom:7px">
        <vs-button color="success"  type="border" style="float:right" v-on:click="crear_plantilla();">Crear</vs-button>
        <!-- <vs-button color="success" v-if="type_newEdit==1" type="border" style="float:right" v-on:click="crear_plantilla();">Crear</vs-button>
        <vs-button color="success" v-else type="border" style="float:right" v-on:click="editar_plantilla();">Editar</vs-button> -->
    </vs-popup>

    <div class="flex flex-wrap">
        <div class="w-full">
            <vs-button color="success" type="border" style="float:right" v-on:click="nueva_plantilla()">Nueva Plantilla</vs-button>
        </div>
    </div>
    <br>
    <!-- {{plantillas}} -->
    <vs-input label="Buscar" v-model="buscar" />
    <br>
    <form>

        <div class="w-full md:w-full lg:w-full xl:w-full">
            <vs-table2 ref="tablacargos" max-items="10" search pagination :data="plantilla_filtro">
                <template slot="thead">

                    <th order="Codigo">Producto</th>
                    <th order="Nombre">Plantillas</th>
                    <th>Acciones</th>
                </template>
                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data" @click="Ventana_Detalle_Producto=true; ListadoPlanitallProducto = tr._level; Edicion.CodProducto = tr.Producto; Edicion.NombreProducto =tr.Nombre;">
                        <vs-td2>
                            [{{ tr.Producto }}] {{ tr.Nombre }}
                        </vs-td2>
                        <vs-td2 width="200px">
                            {{ tr._level.length }}
                        </vs-td2>
                        <vs-td style="text-align: center;" width="5px">
                            <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" @click="Ventana_Detalle_Producto=true; ListadoPlanitallProducto = tr._level; Edicion.CodProducto = tr.Producto; Edicion.NombreProducto =tr.Nombre"></vs-button>
                        </vs-td>
                    </tr>
                </template>

            </vs-table2>
        </div>
       
    </form>
    <vs-divider />
</vx-card>
</template>

<script>
// require styles

import {
    VueEditor
} from "vue2-editor";
// import Multiselect from 'vue-multiselect'
import "vue-multiselect/dist/vue-multiselect.min.css"
export default {
    data() {
        return {
            Edicion:{
                CodProducto: '',
                NombreProducto: '',
                Tipo:'',
                Titulo: '',
                Detalle: ''
            },
            
            
            Ventana_Editar_Plantilla: false,
            SelectDetallePlantilla: '',
            ListadoPlanitallProducto: [],
            Ventana_Detalle_Producto: false,
            showDetails: true,
            show_newPlantilla:false,
            buscar: '',
            type_newEdit: 0,
            show_newEdit: false,
            productos: [],
            plantillas: [],
            examenes: [],
            show_list: true,
            info: {
                plantilla_id: null,
                plantilla: null,
            },
            edit: {
                Tipo: null,
                Nombre: null,
                Detalle: null,
                productos_seleccion: {},

            },

            validar_descripcion: false,
            validar_problema: false,
            category_choices: [{
                text: 1,
                value: 1
            }],
            customToolbar: [
                [{
                    'header': [1, 2, 3, 4, 5, 6, false]
                }],
                [{
                    'font': []
                }],
                ['bold', 'italic', 'underline', 'strike'],
                [{
                    list: "ordered"
                }, {
                    list: "bullet"
                }],
                [{
                    'indent': '-1'
                }, {
                    'indent': '+1'
                }],
                [{
                    'color': []
                }, {
                    'background': []
                }],
            ]
        }
    },
    computed: {
        plantilla_filtro() {
            if (this.buscar == '') {
                return this.plantillas
            } else {
                let arr = this.plantillas.filter(data => data.Producto.indexOf(this.buscar) >= 0 || data.Nombre.toLowerCase().indexOf(this.buscar.toLowerCase()) >= 0)
                return arr
            }
        }
    },
    components: {
        VueEditor,
        // Multiselect
    },
    mounted(){
        
     
    },
    methods: {

        listado_plantillas() {
            this.show_list = false
            this.plantillas = []
            this.axios.post('/app/radiologia/radplantillas', {})
                .then(resp => {
                    if (resp.data.codigo == 0) this.plantillas = resp.data.json
                    setTimeout(() => {
                        this.show_list = true
                    }, 500)
                })
        },
        listado_plantillas_producto(codigoProducto) {
            this.show_list = false
            this.plantillas = []
            this.axios.post('/app/radiologia/radplantillas', {})
                .then(resp => {
                    if (resp.data.codigo == 0){
                        this.plantillas = resp.data.json
                        let producto = this.plantillas.find(p=>p.Producto == codigoProducto)
                        if(producto!=null){                            
                            this.ListadoPlanitallProducto = producto._level;
                        } 
                    }                         
                    setTimeout(() => {
                        this.show_list = true
                    }, 500)
                })
        },

        editar(info, tr) {

            this.type_newEdit = 2;
            this.show_newEdit = true
            this.edit.Detalle = tr.Detalle
            this.edit.Nombre = tr.TituloInforme
            this.edit.Tipo = tr.Tipo
            this.edit.productos_seleccion = {
                Codigo: info.Producto,
                Nombre: info.Nombre
            }
        },

        nueva_plantilla() {
            const sesion = this.$store.state.sesion
            if (sesion.ajeno == null || sesion.ajeno == '') {
                this.$vs.dialog({
                    color: 'danger',
                    title: 'Plantilla',
                    text: 'Su ficha medica no tiene asociado un corporativo, consultar con su jefatura!',
                    acceptText: 'Aceptar',
                    accept: this.acceptAlert
                })
                return false
            }

            this.type_newEdit = 1;
            this.show_newEdit = true
            this.edit.Tipo = ""
            this.edit.Nombre = ""
            this.edit.Detalle = ""
            this.edit.TituloInforme = ""
            this.edit.productos_seleccion = []
        },
        validar_plantilla_producto() {          

            if (this.edit.Tipo == null || this.edit.Tipo.trim() == '') {
                this.$vs.dialog({
                    color: 'danger',
                    title: 'Plantilla',
                    text: 'Debe de ingresar el Tipo de la plantilla',
                    acceptText: 'Aceptar',
                    accept: this.acceptAlert
                })
                return false
            }

            if (this.edit.Nombre == null || this.edit.Nombre.trim() == '') {
                this.$vs.dialog({
                    color: 'danger',
                    title: 'Plantilla',
                    text: 'Debe de ingresar el nombre de la plantilla',
                    acceptText: 'Aceptar',
                    accept: this.acceptAlert
                })
                return false
            }

            if (this.edit.Detalle == null || this.edit.Detalle.trim() == '') {
                this.$vs.dialog({
                    color: 'danger',
                    title: 'Plantilla',
                    text: 'Debe de ingresar el Detalle de la plantilla',
                    acceptText: 'Aceptar',
                    accept: this.acceptAlert
                })
                return false
            }

            if (!this.Edicion.CodProducto) {
                this.$vs.dialog({
                    color: 'danger',
                    title: 'Plantilla',
                    text: 'Debe de seleccionar un producto',
                    acceptText: 'Aceptar',
                    accept: this.acceptAlert
                })
                return false
            }
            return true
        },
        validar_plantilla() {

          

            if (this.edit.Tipo == null || this.edit.Tipo.trim() == '') {
                this.$vs.dialog({
                    color: 'danger',
                    title: 'Plantilla',
                    text: 'Debe de ingresar el Tipo de la plantilla',
                    acceptText: 'Aceptar',
                    accept: this.acceptAlert
                })
                return false
            }

            if (this.edit.Nombre == null || this.edit.Nombre.trim() == '') {
                this.$vs.dialog({
                    color: 'danger',
                    title: 'Plantilla',
                    text: 'Debe de ingresar el nombre de la plantilla',
                    acceptText: 'Aceptar',
                    accept: this.acceptAlert
                })
                return false
            }

            if (this.edit.Detalle == null || this.edit.Detalle.trim() == '') {
                this.$vs.dialog({
                    color: 'danger',
                    title: 'Plantilla',
                    text: 'Debe de ingresar el Detalle de la plantilla',
                    acceptText: 'Aceptar',
                    accept: this.acceptAlert
                })
                return false
            }

            if (!this.edit.productos_seleccion.Codigo) {
                this.$vs.dialog({
                    color: 'danger',
                    title: 'Plantilla',
                    text: 'Debe de seleccionar un producto',
                    acceptText: 'Aceptar',
                    accept: this.acceptAlert
                })
                return false
            }
            return true
        },
        crear_plantilla_producto() {

            if (this.validar_plantilla_producto() == false) return false
            this.axios.post('/app/radiologia/RadPlantillaNuevo', {
                    Tipo: this.edit.Tipo.toUpperCase(),
                    Producto: this.Edicion.CodProducto,
                    Titulo: this.edit.Nombre,
                    Plantilla: this.edit.Detalle
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.listado_plantillas_producto(this.Edicion.CodProducto)
                        this.edit.Tipo = null
                        this.edit.Nombre = null
                        this.edit.Detalle = null
                        this.show_newPlantilla = false

                    } else {
                        this.$vs.dialog({
                            color: 'danger',
                            title: 'Plantilla',
                            text: resp.data.descripcion,
                            accept: this.acceptAlert
                        })
                    }
                })

        },
        crear_plantilla() {

            if (this.validar_plantilla() == false) return false
            this.axios.post('/app/radiologia/RadPlantillaNuevo', {
                    Tipo: this.edit.Tipo.toUpperCase(),
                    Producto: this.edit.productos_seleccion.Codigo,
                    Titulo: this.edit.Nombre,
                    Plantilla: this.edit.Detalle
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.listado_plantillas()
                        // this.$vs.notify({
                        //     title: 'Alerta',
                        //     color: 'danger',
                        //     position: 'top-center',
                        //     time: 6000,
                        //     text: 'Se ha creado la plantilla.'
                        //  })
                        this.edit.Tipo = null
                        this.edit.Nombre = null
                        this.edit.Detalle = null
                        this.edit.productos_seleccion = {}
                        this.show_newEdit = false

                    } else {
                        this.$vs.dialog({
                            color: 'danger',
                            title: 'Plantilla',
                            text: resp.data.descripcion,
                            accept: this.acceptAlert
                        })
                    }
                })

        },

        editar_plantilla() {

            if (this.Edicion.Tipo == null || this.Edicion.Tipo.trim() == '') {
                this.$vs.dialog({
                    color: 'danger',
                    title: 'Plantilla',
                    text: 'Debe de ingresar el Tipo de la plantilla',
                    acceptText: 'Aceptar',
                    accept: this.acceptAlert
                })
                return false
            }

            if (this.Edicion.Titulo == null || this.Edicion.Titulo.trim() == '') {
                this.$vs.dialog({
                    color: 'danger',
                    title: 'Plantilla',
                    text: 'Debe de ingresar el nombre de la plantilla',
                    acceptText: 'Aceptar',
                    accept: this.acceptAlert
                })
                return false
            }

            if (this.Edicion.Detalle == null || this.Edicion.Detalle.trim() == '') {
                this.$vs.dialog({
                    color: 'danger',
                    title: 'Plantilla',
                    text: 'Debe de ingresar el Detalle de la plantilla',
                    acceptText: 'Aceptar',
                    accept: this.acceptAlert
                })
                return false
            }

        
            this.axios.post('/app/radiologia/RadPlantillaUpdate', {
                    Tipo: this.Edicion.Tipo.toUpperCase(),
                    Producto: this.Edicion.CodProducto,
                    Titulo: this.Edicion.Titulo,
                    Plantilla: this.Edicion.Detalle
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.listado_plantillas()
                        // this.$vs.dialog({
                        //     color: 'success',
                        //     title: 'Plantilla',
                        //     text: 'Se ha editado la plantilla',
                        //     accept: this.acceptAlert
                        // })

                        // this.$vs.notify({
                        //     title: 'Alerta',
                        //     color: 'danger',
                        //     position: 'top-center',
                        //     time: 6000,
                        //     text: 'Se ha editado la plantilla.'
                        //  })
                        this.Edicion.CodProducto = null
                        this.Edicion.NombreProducto = null
                        this.Edicion.Tipo = null
                        this.Edicion.Titulo = null
                        this.Edicion.Detalle = null

                        this.Ventana_Editar_Plantilla= false
                        this.Ventana_Detalle_Producto = false
                        this.Edicion.productos_seleccion = {}
                        this.show_newEdit = false
                        this.listado_plantillas();

                        
                    } else {
                        this.$vs.dialog({
                            color: 'danger',
                            title: 'Plantilla',
                            text: resp.data.descripcion,
                            accept: this.acceptAlert
                        })
                    }
                })

        },

        limpiar_orden() {
            this.examenes = []
            this.info.opcion.congelar = false
            this.info.opcion.sms = false
            this.info.orden_tipo = null
            this.info.orden_numero = null
            this.info.paciente_codigo = null
            this.info.paciente_nombre = ''
            this.info.paciente_edad = ''
            this.info.examen_codigo = ''
            this.info.examen_nombre = ''
            this.info.examen_linea = ''
            this.info.especialista_codigo = ''
            this.info.especialista_nombre = ''
            this.info.refiere = ''
            this.bloqueoBusqueda = false
            this.bloqueoCampos = true
            this.info.informe = ''
        },

        buscar_productos() {
            this.$refs.buscar_productos.iniciar({
                seleccion: this.productos_seleccion,
                callback: (data) => {
                    if (data != null) this.edit.productos_seleccion = data
                }
            })
        },

        producto_eliminar() {
            this.edit.productos_seleccion = {}
        },

        opciones(op) {
            this.info.opcion.show = true
            this.info.opcion.congelado = op.congelado
            this.info.opcion.sms = op.sms
            this.info.opcion.examen_codigo = op.examen_codigo
            this.info.opcion.examen_nombre = op.examen_nombre.trim()
            this.info.opcion.especialista_codigo = op.especialista_codigo
            this.info.opcion.especialista_nombre = op.especialista_nombre
            this.info.opcion.observaciones = op.observaciones
            this.info.opcion.informe = op.informe
            this.info.opcion.linea_orden = op.examen_linea
            this.bloqueoCampos = op.congelado

        },

    },
    created() {
        this.listado_plantillas()
    }
}
</script>

<style>
.button {
    margin: 0px !important;
}

.btnx {
    margin-left: 10px !important;
    background: red;
    border-radius: 5px 0px 0px 5px;
}

.btn-drop {
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: red;

}

.menu-funcionalidad {
    /* padding-top:0 !important; */
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    padding: 15px;
    display: inline;
    float: right;
    border-radius: 10px;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 10px;
}

.menu-funcionalidad td {
    text-align: center;
    padding-right: 10px;
}

.vs-switch {
    height: 30px;
    width: 60px !important;
}

.vs-circle-switch {
    height: 25px;
    width: 25px;
}

.vs-switch--icon {
    font-size: 15px
}

.vs-switch.vs-switch-active .vs-switch--circle {
    margin-left: calc(100% - 30px);
}
</style>
