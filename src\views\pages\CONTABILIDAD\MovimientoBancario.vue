<template>
    <div>   
        <form @submit="handleSubmit">
            <DxForm
            :ref="formTransaccion"
            :form-data.sync="formulario"
            label-mode="floating"
            style="margin: 5px"
            >
                <DxFormGroupItem :col-count="2">
                    <DxFormGroupItem>
                        <DxFormItem
                        data-field="Cuenta"
                        editor-type="dxSelectBox"
                        :editor-options="{
                            width: 'auto',
                            searchEnabled: true,
                            items: cuentas,
                            displayExpr: 'Valor',
                            valueExpr: 'Codigo',
                            disabled: true,

                            onValueChanged: AsignarSiguienteCheque,
                        }"
                        :validationRules="[{ type: 'required' }]"
                        />
                        <DxFormGroupItem :col-count="2">
                            <DxFormGroupItem>
                                <DxFormItem 
                                        data-field="CuentaBanco" 
                                        :disabled="true" 
                                        editor-type="dxTextBox" 
                                        :value="formulario.CuentaBanco"
                                    />
                            </DxFormGroupItem>
                            <DxFormGroupItem>
                                <DxFormGroupItem>
                                    <DxFormItem data-field="Descripcion" :disabled="true" editor-type="dxTextBox"  :value="formulario.Descripcion" />
                                </DxFormGroupItem>
                            </DxFormGroupItem>
                            <DxFormItem template="Buttons"/>
                        </DxFormGroupItem>
                        <DxFormGroupItem>
                            <DxFormItem template="alerta" />
                            <DxFormItem template="alertaCerrar" />
                        </DxFormGroupItem>
                    </DxFormGroupItem>
                    
                    <DxFormGroupItem :col-count="2">
                        <DxFormGroupItem>
                            <DxFormItem template="Periodos" />
                        </DxFormGroupItem>
                        <DxFormGroupItem>
                            <DxFormItem template="Transac" />
                        </DxFormGroupItem>
                    </DxFormGroupItem>
                </DxFormGroupItem>

                <template #Transac="{}">
                    <div style="display: flex;">
                        <div class="salgo" >
                            <div class="saldo-container">
                               
                                <p>
                                <span class="label">Saldo Inicial</span>
                                <span class="value strong" style="color: #2e4053;">
                                    {{ valores.SaldoInicial }}
                                </span>
                                </p><br>
                                <p><span class="label">Cheques</span> <span class="value" style="color: #2E4053;">{{ valoresCalculados.Cheques.toLocaleString('es-GT', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span></p><br>
                                <p><span class="label">Depósitos</span> <span class="value" style="color: #2E4053;">{{ valoresCalculados.Depositos.toLocaleString('es-GT', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span></p><br>
                                <p><span class="label">Otros Créditos</span> <span class="value" style="color: #2E4053;">{{ valoresCalculados.NotasCredito.toLocaleString('es-GT', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span></p><br>
                                <p><span class="label">Otros Débitos</span> <span class="value" style="color: #2E4053;">{{ valoresCalculados.NotasDebito.toLocaleString('es-GT', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span></p><br>
                                <p v-if="Number(tieneSaldo.SaldoFinal) === 1">
                                    <span class="label">Saldo</span> 
                                    <span class="value strong" 
                                        :style="{ color: Number(tieneSaldo.SaldoFinal) === 0 ? 'brown' : '#2e4053' }">
                                        {{ valoresCalculados.SaldoFinal.toLocaleString('es-GT', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}
                                    </span>
                                </p>
                                <p v-else>
                                    <span class="label">Saldo</span>
                                    <span class="value strong" :style="{ color: Number(tieneSaldo.SaldoFinal) === 0 ? 'brown' : '#2e4053' }">
                                       {{ isNaN(Number(valores.SaldoFinal)) ? valoresCalculados.SaldoFinal.toLocaleString('es-GT', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : Number(valores.SaldoFinal).toLocaleString('es-GT', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}
                                    </span>
                                </p>    
                            </div>
                        </div>
                    </div>
                </template>

                <template  #Buttons="{}"> 
                    <div>
                        <div  style="display: flex; justify-content: center; width: 100%;">
                            <DxButton id="saveButton" style="margin-right: 2px;" :disabled="selectedIndex === 0" icon="fas fa-step-backward"  type="normal" @click="irPrimero" />
                            <DxButton id="saveButtonDos" style="margin-right: 2px;" :disabled="selectedIndex === 0" icon="fas fa-caret-left"  type="normal"   @click="irAnterior" />
                            <DxButton id="saveButtonDos" style="margin-right: 2px;" :disabled="selectedIndex === cuentas.length - 1"  icon="fas fa-caret-right"   type="normal" @click="irSiguiente" />
                            <DxButton id="saveButton"  :disabled="selectedIndex === cuentas.length - 1" icon="fas fa-step-forward"  type="normal"  @click="irUltimo" />
                        </div>
                    </div>
                </template>
                <DxFormGroupItem>
                    <DxFormItem template="BotonesAccion"/>
                  <!--   <DxFormGroupItem :col-count-by-screen="colCountByScreenButtons">
                        <DxFormButtonItem
                            :button-options="buttonNewMonth"
                            name="NewMonth"
                            horizontal-alignment="center"
                            verical-alignment="center"
                        />
                        <DxFormButtonItem
                            :button-options="buttonClose"
                            name="Close"
                        
                            horizontal-alignment="center"
                            verical-alignment="center"
                        />
                        <DxFormButtonItem
                            :button-options="buttonOpen"
                            name="Open"
                            :disabled="true" 
                            horizontal-alignment="center"
                            vertical-alignment="center"
                       
                        />

                        <DxFormButtonItem
                            :button-options="buttonDelete"
                            name="delete"
                            horizontal-alignment="center"
                            verical-alignment="center"
                        />

                    </DxFormGroupItem>   -->
                </DxFormGroupItem>
                
                
                <template #Periodos="{}" >
                    <div>
                        <DxDataGrid class="solo-esta-tabla" :data-source="periodos" :show-borders="true" :focused-row-enabled="true" :focused-row-key="focusedRowKey"  :column-auto-width="true" :key-expr="'Periodo'" ref="dataGrid" @rowClick="onRowClick">
                            <DxDataGridSelection mode="single" />
                            <DxDataGridColumn data-field="Periodo" data-type="number" caption="Periodo" />
                            <DxDataGridColumn data-field="Descripcion" caption="Descripción" />
                            <DxDataGridPaging :page-size="1" />
                        </DxDataGrid>
                        <br>
                        <div style="display: flex; justify-content: center; width: 100%;">
                            <DxButton  id="saveButton" style="margin-right: 2px;" :disabled="selectedIndex2 === 0" icon="fas fa-step-backward" type="normal" @click="irPrimeroPeriodo" />
                            <DxButton  id="saveButtonDos" style="margin-right: 2px;" :disabled="selectedIndex2 === 0" icon="fas fa-caret-left" type="normal" @click="irAnteriorPeriodo" />
                            <DxButton  id="saveButtonDos" style="margin-right: 2px;" :disabled="selectedIndex2 === periodos.length - 1" icon="fas fa-caret-right" type="normal" @click="irSiguientePeriodo" />
                            <DxButton id="saveButton" :disabled="selectedIndex2 === periodos.length - 1" icon="fas fa-step-forward" type="normal" @click="irUltimoPeriodo" />
                        </div>   
                    </div>         
                </template>
                
                <template #BotonesAccion="{}">
                 <div>
                    <div style="display: flex; justify-content: center; gap: 20px;">
                        <DxButton :width="200" icon="add" text="Nuevo Mes" type="normal" styling-mode="contained" @click="IniciarNuevoPeriodo" />
                        <DxButton :width="200" icon="imgarlock" text="Cerrar Periodo" type="default" styling-mode="contained" @click="CerrarPeriodod" :disabled="tieneSaldo.SaldoFinal === '0'" />
                        <DxButton :width="200" icon="imgarunlock" text="Abrir Periodo" type="success" styling-mode="contained" @click="AbrirPeriodo"  :disabled="tieneSaldo.SaldoFinal === '1'"/>
                        <DxButton :width="200" icon="trash" text="Eliminar Registro" type="danger" styling-mode="contained" @click="Eliminar" />
                    </div>

                 </div>
                </template>

                <template #alerta="{}">
                    <vs-alert style="width: 50%;text-align: center;" :active="tieneSaldo.SaldoFinal === '1'" color="success">
                        Periodo Abierto
                    </vs-alert>
                </template>

                <template #alertaCerrar="{}">
                    <vs-alert style="width: 50%;text-align: center;" :active="tieneSaldo.SaldoFinal === '0'" color="primary">
                        Periodo Cerrado
                    </vs-alert>
                </template>
            </DxForm>
        </form>
        <br>

        <DxDataGrid  :data-source="datos" :show-borders="true"  :column-auto-width="true">
            <DxDataGridColumn  data-field="Fecha" data-type="date" caption="Fecha" />
            <DxDataGridColumn  data-field="Tipo" caption="Tipo" data-type="string" />
            <DxDataGridColumn  data-field="Numero" caption="Número" data-type="number" />
            <DxDataGridColumn  data-field="Observaciones" caption="Detalle"  data-type="string"/>
            <DxDataGridColumn 
                data-field="Monto" 
                data-type="number" 
                caption="Valor" 
                :format="formatCurrencyData"
            />
            <DxDataGridPaging :page-size="10" />
        </DxDataGrid>
    </div>
</template>
<script>

const formTransaccion = "formTransaccion";

export default {
    name:"Movimiento",
    data(){
        return{
            formulario:{
                Cuenta:null,
                Periodo:null,
                CuentaBanco: '',
                Descripcion:null
            },

            valores:{
                SaldoInicial: "0.00",
                Cheques: "0.00",
                Depositos: "0.00",
                NotasCredito: "0.00",
                NotasDebito: "0.00",
                SaldoFinal:"0.00"
            },

            valoresAbrir:{
                Cheques:"",
                Depositos:"",
                Empresa:"",
                NotasCredito:"",
                NotasDebito:"",
                Periodo:"",
                ReActUsuario:"",
                ReactFecha:"",
                SaldoFinal:"",
                SaldoInicial:"",
                cuenta:""
            },

            cuentas:[],
            datos:[],
            periodos:[],


            UltimoPeriodo:{
                Periodo:""
            },

            tieneSaldo:{
                SaldoFinal:""
            },

            formTransaccion: null,

            PeriodoSeleccionado: null,

            selectedIndex: 0,
            selectedIndex2: null, // Índice de la fila seleccionada
            focusedRowKey: 1,  // Clave de la fila enfocada


            valoresOriginales: {} ,// Almacena los valores originales antes de corregirlos
            //Botones Movimientos 
            buttonNewMonth: {
                width: "auto",
                icon: "fas fa-calendar-plus",
                hint: "Nuevo Mes",
                text: "Nuevo Mes",
                type: "normal",
                onClick: () => {
                    this.IniciarNuevoPeriodo()
                },
                useSubmitBehavior: false,
            },

            buttonClose: {
                width: "auto",
                icon: "fas fa-lock",
                hint: "Cerrar Periodo",
                text: "Cerrar Periodo",
                type: "default",

                onClick: () => {
                    this.CerrarPeriodod()
                },
                useSubmitBehavior: false,
            },


            buttonOpen: {
                width: "auto",
                icon: "fas fa-lock-open",
                hint: "Abrir Periodo",
                text: "Abrir Periodo",
                type: "success",
                onClick: () => {
                    this.AbrirPeriodo()
                },
                useSubmitBehavior: false,
              
            },

            buttonDelete:{
                width: "auto",
                icon: "fas fa-times",
                hint: "Eliminar Registro",
                text: "Eliminar Registro",
                type: "danger",
                onClick: () => {
                    this.Eliminar()
                },
                useSubmitBehavior: false,
            },


        }
    },
    computed:{
        formTransformAnticipoInstanceaccion: function () {
         return this.$refs[formTransaccion].instance;
        },
        colCountByScreenButtons() {
        return this.calculateColCountAutomatically
            ? null
            : {
                sm: 1,
                md: 2,
                lg: 4,
            };
        },

        

        valoresCalculados() {
            // Inicializar valores en 0
            let valores = {
                Cheques: 0,
                Depositos: 0,
                NotasCredito: 0,
                NotasDebito: 0,
                SaldoFinal: 0
            };

            // Obtener SaldoInicial (convertir a número, manejar comas como separadores de miles)
            const saldoInicial = parseFloat(String(this.valores.SaldoInicial).replace(/,/g, '')) || 0;

            // Calcular desde this.datos (si hay registros)
            this.datos.forEach(item => {
                const monto = parseFloat(String(item.Monto).replace(/,/g, '')) || 0;

                switch (item.Tipo) {
                    case "CH":
                        valores.Cheques += monto;
                        break;
                    case "DP":
                        valores.Depositos += monto;
                        break;
                    case "NC":
                        valores.NotasCredito += monto;
                        break;
                    case "ND":
                    case "CV":
                    case "CA":
                    case "NA":
                    case "NH":
                    case "NP":
                        valores.NotasDebito += monto;
                        break;
                }
            });

            // Calcular SaldoFinal (SaldoInicial + Depósitos + NC - Cheques - ND)
            valores.SaldoFinal = saldoInicial + valores.Depositos + valores.NotasCredito - valores.Cheques - valores.NotasDebito;

            return valores;
        },

        selectedCuenta: {
            get() {
                return this.formulario.Cuenta;
            },
            set(value) {
                this.formulario.Cuenta = value;
            },
        }
    },
    methods:{

        formatCurrencyData(value) {
            if (value == null) return ""; // Manejo de valores nulos
            return `Q ${value.toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
        },

        handleSubmit(e) {
            e.preventDefault();
        },

        CargarCuentas(asignar) {
            // La variable de asignar indica cuando debe seleccionar la primera cuenta de la lista
            this.axios.post("/app/v1_bancos/Cuentas", {
                Opcion: 1,
            },{ skipLoading: true }).then((resp) => {
                this.cuentas = resp.data.json;
                if (asignar) {
                    this.AsignarBanco(this.cuentas[0]);
                }
            });
        },

        AsignarSiguienteCheque(e) {
            let cuenta = this.cuentas.find((x) => x.Codigo == e.value);
            if (cuenta) {
                this.AsignarBanco(cuenta);
                this.Periodos(cuenta)
               
            }
        },

       AsignarBanco(cuenta) {
            this.datos = []
            this.valores = {
                SaldoInicial: "0.00",
                Cheques: "0.00",
                Depositos: "0.00",
                NotasCredito:"0.00",
                NotasDebito: "0.00",
                SaldoFinal: "0.00"
            },

            this.tieneSaldo ={
                SaldoFinal:" "
            },
            
            this.PeriodoSeleccionado = null,
            this.formulario.Descripcion = null
            // Asignar los valores correctamente a formulario
            this.formulario.Cuenta = cuenta.Codigo;
            this.formulario.CuentaBanco = cuenta.Cuenta; // Asignamos el nombre del banco

       },

       Periodos(e) {
        this.axios.post("/app/v1_bancos/ListaPeriodos", {  
            Opcion: 5,
            CuentaMontos: e.Codigo,
        },{ skipLoading: true }).then((resp) => {
                // Asignar los datos a 'periodos'
                this.periodos = resp.data.json;

                // Asegurarse de que hay datos y seleccionar el primer 'Periodo' como 'focusedRowKey'
                if (this.periodos.length > 0) {
                this.focusedRowKey = this.periodos[0].Periodo; // Seleccionar la primera fila

                // Crear un objeto 'e' similar al evento de 'onRowClick' para llamar a la función programáticamente
                const rowClickEvent = {
                    data: this.periodos[0],  // Pasar la primera fila como 'data'
                };

                // Llamar a onRowClick de forma programática con el primer elemento
                this.onRowClick(rowClickEvent);
                }
            })
       },

        onRowClick(e) {
            // Actualizar el formulario con los datos de la fila seleccionada
            this.formulario.Descripcion = e.data.Descripcion;
            this.formulario.Periodo = e.data.Periodo;

            // Seleccionar la fila
            this.selectedRowKeys = [e.data.Periodo];


            // Encontrar el índice de la fila seleccionada
            const index = this.periodos.findIndex(p => p.Periodo === e.data.Periodo);
            this.selectedIndex2 = index;

            // Limpiar saldo
            this.tieneSaldo = {
                SaldoFinal: " "
            };

            // Hacer la solicitud Axios con el periodo seleccionado
            this.axios.post("/app/v1_bancos/UltimoPeriodo", {  
                Opcion: 6,
                CuentaMontos: e.data.cuenta,
            },{ skipLoading: true }).then((resp) => {
                if (resp.data.json) {
                this.$map({
                    objeto: this.UltimoPeriodo,
                    respuestaAxios: resp,
                });

                if (this.UltimoPeriodo.Periodo === e.data.Periodo) {
                    this.ConsultaValores(); 
                    this.ConsultaDatos(e.data);
                } else {
                    this.ConsultaValores();
                    this.ConsultaDatos(e.data);
                }
                }
            });
        },

        getCurrentIndex() {
            const selectedPeriod = this.selectedRowKeys[0];
            return this.periodos.findIndex(item => item.Periodo === selectedPeriod);
        },

        ConsultaValores() {
            this.axios.post("/app/v1_bancos/ValoresTable", {
                Opcion: 1,
                Periodo: this.formulario.Periodo,
                CuentaMontos: this.formulario.Cuenta
            },{ skipLoading: true }).then((resp) => {
                if (!resp.data.json || Object.keys(resp.data.json).length === 0) {
                    this.valores = {
                        SaldoInicial: "0.00",
                        Cheques: "0.00",
                        Depositos: "0.00",
                        NotasCredito:"0.00",
                        NotasDebito: "0.00",
                        SaldoFinal: "0.00"
                    };

                    this.$vs.dialog({
                        type: 'confirm',
                        color: '#c0392b',
                        title: 'Error, Sin Información',
                        acceptText: 'Aceptar',
                        cancelText: 'Cancelar',
                        text: `No hay información en este período.`,
                        buttonCancel: 'border',
                        clientWidth: 100,
                        accept: () => {},
                        cancel: () => {}
                    });
                    return;
                }

                if (resp.data.codigo === 0) {
                    // Verificar si los valores traídos son nulos o cero
                    const data = resp.data.json;
                    this.valores = {
                        SaldoInicial: data.SaldoInicial || "0.00",
                        Cheques: data.Cheques || "0.00",
                        Depositos: data.Depositos || "0.00",
                        NotasCredito: data.NotasCredito || "0.00",
                        NotasDebito: data.NotasDebito || "0.00",
                        SaldoFinal: data.SaldoFinal || "0.00"
                    };

                    // Si todos los valores son 0 o null, calcular los valores
                    if (Object.values(this.valores).every(value => value === "0.00" || value === null)) {
                        this.valores = {
                            SaldoInicial: "0.00",
                            Cheques: "0.00",
                            Depositos: "0.00",
                            NotasCredito: "0.00",
                            NotasDebito: "0.00",
                            SaldoFinal: this.valoresCalculados.SaldoFinal
                        };
                    }

                    this.$map({
                        objeto: this.valores,
                        respuestaAxios: resp,
                    });

                    this.$map({
                        objeto: this.valoresAbrir,
                        respuestaAxios: resp,
                    });

                    this.corregirSaldoFinal(this.valores);
                }
            }).catch(() => {});
        },

        ConsultaDatos(e) {
            this.axios.post("/app/v1_bancos/datos", {
                Opcion: 2,
                Periodo: e.Periodo,
                CuentaMontos: this.formulario.Cuenta
            }).then((resp) => {
                if (resp.data.codigo === 0) {
                    // Guardamos los datos en this.datos
                    this.datos = resp.data.json;

                    // Recorremos los datos y modificamos la columna Tipo y Monto
                    this.datos.forEach((item) => {
                        if (item.Status === "A" || item.Status === "R") {
                            // Si Status es "A" o "R", agregamos "A" al valor de Tipo
                            item.Tipo = item.Tipo + " " + "A";
                            // También asignamos 0 al campo Monto
                            item.Monto = 0;
                        }
                    });
                }
            })
        },

        async corregirSaldoFinal(valores) {
            function convertirAFloat(valor) {
                const floatValue = parseFloat(valor?.toString().replace(/,/g, ''));
                return isNaN(floatValue) ? 0 : floatValue;
            }

            function formatearNumero(valor) {
                return valor.toLocaleString('en-US', { style: 'decimal', minimumFractionDigits: 2 });
            }

            this.valoresOriginales = { ...valores };

            try {
                const resp = await this.axios.post("/app/v1_bancos/ConsultaSalfoFinal", {
                    Opcion: 7,
                    CuentaMontos: this.formulario.Cuenta,
                    Periodo: this.formulario.Periodo
                });

                if (resp.data.codigo === 0) {
                    resp.data.json.map(datos => {
                        this.tieneSaldo.SaldoFinal = datos.SaldoFinal;
                    });
                }

                valores.SaldoInicial = convertirAFloat(valores.SaldoInicial);
                valores.Cheques = convertirAFloat(valores.Cheques);
                valores.Depositos = convertirAFloat(valores.Depositos);
                valores.NotasCredito = convertirAFloat(valores.NotasCredito);
                valores.NotasDebito = convertirAFloat(valores.NotasDebito);

                if (valores.SaldoFinal === "" || valores.SaldoFinal === null) {
                    valores.SaldoFinal = valores.SaldoInicial + valores.Depositos + valores.NotasCredito - valores.Cheques - valores.NotasDebito;
                }

                valores.SaldoInicial = formatearNumero(valores.SaldoInicial);
                valores.Cheques = formatearNumero(valores.Cheques);
                valores.Depositos = formatearNumero(valores.Depositos);
                valores.NotasCredito = formatearNumero(valores.NotasCredito);
                valores.NotasDebito = formatearNumero(valores.NotasDebito);
                valores.SaldoFinal = formatearNumero(valores.SaldoFinal);

            } catch (error) {
                
            }
        },

        Periodoslista() {
            return this.axios.post("/app/v1_bancos/ListaPeriodos", {  
                Opcion: 5,
                CuentaMontos: this.formulario.Cuenta,
            }).then((resp) => {
                this.periodos = resp.data.json; // Invertir orden si es necesario
                return resp.data.json; // Devuelve los datos para posibles usos adicionales
            });
        },
        
        onContentReady() {
            return new Promise((resolve) => {
                this.$nextTick(() => {
                    if (this.periodos.length > 0) {
                        const dataGrid = this.$refs.dataGrid.instance;
                        dataGrid.selectRowsByIndexes([0]); // Selecciona la primera fila
                        this.onRowClick({ data: this.periodos[0] }); // Llama a la función como si se hiciera clic
                        this.selectedIndex2 = 0; // Actualiza el índice seleccionado
                    }
                    resolve();
                });
            });
        },

        // Inicia un nuevo periodo 
        IniciarNuevoPeriodo() {
            this.$vs.dialog({
                type: 'confirm',
                color: '#8e44ad',
                title: '¿Seguro que quieres crear un nuevo período?',
                acceptText: 'Aceptar',
                cancelText: 'Cancelar',
                text: `Recuerda completar todas las transacciones del período anterior antes de proceder.`,
                buttonCancel: 'border',
                clientWidth: 100,
                accept: async () => {
                    const datosOriginales = { ...this.valoresAbrir };

                    try {
                        const resp = await this.axios.post("/app/v1_bancos/IniciarNuevoPeriodo", {
                            Opcion: 8,
                            CuentaMontos: datosOriginales.cuenta,
                        }, { skipLoading: true });

                        if (resp.data.codigo === 0) {
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#27ae60',
                                title: 'PERIODO CREADO',
                                acceptText: 'Aceptar',
                                text: 'Nuevo Periodo Inicializado',
                                buttonCancel: 'border',
                               accept: async () => {
                                    // 1. Cargar períodos
                                    await this.Periodoslista(); 

                                    // 2. Seleccionar el último período explícitamente
                                    this.irUltimoPeriodo(); // ¡Forzar la selección aquí!

                                    // 3. Opcional: Actualizar UI
                                    this.$nextTick(() => {
                                        if (this.$refs.dataGrid) {
                                            this.$refs.dataGrid.instance.refresh();
                                        }
                                    });
                                }
                            });
                        }
                    } catch (error) {
                        this.$vs.notify({
                            title: 'Error',
                            text: 'No se pudo crear el nuevo período',
                            color: 'danger'
                        });
                    }
                },
                cancel: () => {}
            });
        },
        
        // Abre Periodo
        AbrirPeriodo() {
            this.tieneSaldo = {
                SaldoFinal: " "
            };
            
            const datosOriginales = { ...this.valoresAbrir };
            // Eliminar las comas en los valores numéricos (si existen) y convertir a númer
            this.axios.post("/app/v1_bancos/Abrir", {   
                Opcion: 4,
                Cuenta: datosOriginales.cuenta,
                Periodo: datosOriginales.Periodo,
                SaldoInicial: datosOriginales.SaldoInicial,
                SaldoFinal: datosOriginales.SaldoFinal
            }).then((resp) => {
                if (resp.data.codigo === 0) {
                    this.$vs.dialog({
                        type: 'alert',
                        color: '#8e44ad',
                        title: 'Aviso Importante  (•◡•)/',
                        acceptText: 'Aceptar',
                        cancelText: 'Cancelar',
                        text: `Es importante asegurarse de haber completado todas las transacciones del periodo anterior antes de continuar.`,
                        buttonCancel: 'border',
                        accept: () => {
                            this.axios.post("/app/v1_bancos/Abrir", {
                                Opcion: 5,
                                Cuenta: datosOriginales.cuenta,
                                Periodo: datosOriginales.Periodo,
                                Cheques: datosOriginales.Cheques,
                                Depositos: datosOriginales.Depositos,
                                NotasCreditos: datosOriginales.NotasCredito,
                                NotasDebitos: datosOriginales.NotasDebito,
                                SaldoInicial: datosOriginales.SaldoInicial,
                            }).then((resp) => {
                                if (resp.data.codigo === 0) {
                                    
                                    this.corregirSaldoFinal(this.valores);
                                }
                            });
                            
                        },
                        cancel: () => {}
                    });
                }
            });
        },

        // Función para eliminar las comas y convertir el valor a número
        eliminarComasYConvertir(valor) {
            if (typeof valor === 'string') {
                // Eliminar comas y convertir a número
                return parseFloat(valor.replace(/,/g, ''));
            }
            return valor;
        },

        // Cierra Periodo
        CerrarPeriodod() {
            const { SaldoFinal } = this.valoresCalculados;

            this.tieneSaldo = { SaldoFinal: "" };
                    this.$vs.dialog({
                                type: 'alert',
                                color: '#e74c3c',
                                title: 'Aviso Importante ☠',
                                acceptText: 'Aceptar',
                                cancelText: 'Cancelar',
                                text: `El sistema ya no permite transacciones para este periodo.`,
                                buttonCancel: 'border',
                                accept: () => {
                                      this.axios.post("/app/v1_bancos/Cerrar", {
                                            Opcion: 6,
                                            Cuenta: this.formulario.Cuenta,
                                            Periodo: this.formulario.Periodo,
                                            Cheques: this.valores.Cheques,
                                            Depositos: this.valores.Depositos,
                                            NotasCreditos: this.valores.NotasCredito,
                                            NotasDebitos: this.valores.NotasDebito,
                                            SaldoInicial: this.valores.SaldoInicial,
                                            SaldoFinal: SaldoFinal
                                        }).then((resp) => {
                                            if (resp.data.codigo === 0) {
                                                this.ConsultaValores();
                                                
                                            }
                                        });
                                }
                            })
        },

        Eliminar() {
            this.$vs.dialog({
                type: 'confirm',
                color: '#e74c3c',
                title: '¿Estás seguro de eliminar este período?',
                acceptText: 'Sí, Eliminar',
                cancelText: 'Cancelar',
                text: `¿Estás seguro de que deseas eliminar el periodo ${this.formulario.Periodo}? Esta acción no se puede deshacer.`,
                buttonCancel: 'border',
                accept: async () => {
                    try {
                        const resp = await this.axios.post("/app/v1_bancos/EliminarMes", {
                            Opcion: 9,
                            Cuenta: this.formulario.Cuenta,
                            Periodo: this.formulario.Periodo,
                        }, { skipLoading: true });

                        if (resp.data.codigo === 0) {
                            // 1. Resetear estados
                            this.tieneSaldo = { SaldoFinal: "" };
                            this.valores = {
                                SaldoInicial: "0.00",
                                Cheques: "0.00",
                                Depositos: "0.00",
                                NotasCredito: "0.00",
                                NotasDebito: "0.00",
                                SaldoFinal: "0.00"
                            };
                            this.datos = [];

                            // 2. Actualizar lista de períodos
                            await this.Periodoslista();
                            
                            // 3. Seleccionar automáticamente el último período disponible
                            if (this.periodos.length > 0) {
                                await this.onContentReady();
                                this.irUltimoPeriodo(); // O seleccionar el más reciente
                            } else {
                                // Si no hay períodos, limpiar la selección
                                this.formulario.Periodo = "";
                            }

                            this.$vs.notify({
                                title: 'Período Eliminado!',
                                text: 'Período eliminado correctamente',
                                color: 'success'
                            });
                        } else {
                            throw new Error(resp.data.mensaje || 'Error al eliminar');
                        }
                    } catch (error) {
                        this.$vs.notify({
                            title: 'Error',
                            text: error.message || 'No se pudo eliminar el período',
                            color: 'danger'
                        });
                    }
                },
                cancel: () => {}
            });
        },

        //==================================================================================================//
        // Botones al moverse entre botones empresas     
        //==================================================================================================//

        irPrimero() {
                this.periodos = []  // Asegúrate de limpiar los datos antes de actualizar
                this.selectedIndex = 0;
                this.selectedCuenta = this.cuentas[this.selectedIndex].Codigo;

                // Restablecer la página a la primera (pageIndex = 0)
                const dataGrid = this.$refs.dataGrid.instance;
                dataGrid.pageIndex(0);  // Vuelve a la primera página
        },

        irAnterior() {
            this.periodos = []  // Limpiar los datos antes de cambiar el índice
            if (this.selectedIndex > 0) {
                this.selectedIndex--;
                this.selectedCuenta = this.cuentas[this.selectedIndex].Codigo;
            }

            // Restablecer la página a la primera (pageIndex = 0)
            const dataGrid = this.$refs.dataGrid.instance;
            dataGrid.pageIndex(0);  // Vuelve a la primera página
        },

        irSiguiente() {
            this.periodos = []  // Limpiar los datos antes de cambiar el índice

            if (this.selectedIndex < this.cuentas.length - 1) {
                this.selectedIndex++;
                this.selectedCuenta = this.cuentas[this.selectedIndex].Codigo;
            }

            // Restablecer la página a la primera (pageIndex = 0)
            const dataGrid = this.$refs.dataGrid.instance;
            dataGrid.pageIndex(0);  // Vuelve a la primera página
        },

        irUltimo() {
            this.periodos = []  // Limpiar los datos antes de cambiar el índice
            this.selectedIndex = this.cuentas.length - 1;
            this.selectedCuenta = this.cuentas[this.selectedIndex].Codigo;

            // Restablecer la página a la primera (pageIndex = 0)
            const dataGrid = this.$refs.dataGrid.instance;
            dataGrid.pageIndex(0);  // Vuelve a la primera página
        },

        irPrimeroPeriodo() {
            this.selectedIndex2 = 0;
            this.seleccionarFila();
            this.focusedRowKey = this.periodos[this.selectedIndex2].Periodo;

        },

        irAnteriorPeriodo() {
            if (this.selectedIndex2 > 0) {
            this.selectedIndex2--;
            this.seleccionarFila();
            this.focusedRowKey = this.periodos[this.selectedIndex2].Periodo;

            }
        },

        irSiguientePeriodo() {
            if (this.selectedIndex2 < this.periodos.length - 1) {
            this.selectedIndex2++;
            this.seleccionarFila();
            this.focusedRowKey = this.periodos[this.selectedIndex2].Periodo;

            }
        },
        
        irUltimoPeriodo() {
            this.selectedIndex2 = this.periodos.length - 1;
            this.seleccionarFila();
            this.focusedRowKey = this.periodos[this.selectedIndex2].Periodo;
        },

        seleccionarFila() {
            const dataGrid = this.$refs.dataGrid.instance;
            const filaSeleccionada = this.periodos[this.selectedIndex2];
            if (filaSeleccionada) {
            // Forzar la selección en la tabla
            dataGrid.selectRowsByIndexes([this.selectedIndex2]);
            dataGrid.option("focusedRowIndex", this.selectedIndex2);
            // Ejecutar manualmente onRowClick con la fila seleccionada
            this.onRowClick({ data: filaSeleccionada });
            }
        }
    },

    beforeMount() {
        this.CargarCuentas(true);
    },
}

</script>

<style scoped>
.salgo{
    border: 2px solid #000080;
}

.saldo-container {
    padding: 10px;
    width: 220px;
    text-align: left;
    background-color: white;
}

.label {
    float: left;
}
.value {
    float: right;
}
.strong {
    font-weight: bold;
}

#saveButton .dx-icon {
    font-size: 17px;
    color: black;

}

#saveButton{
    width: 80px !important;
}

#saveButtonDos .dx-icon {
    font-size: 25px;
    color: black;
}

#saveButtonDos{
    width: 80px !important;
}

</style>

<style>
.solo-esta-tabla .dx-datagrid-pager {
  display: none;
}
</style>