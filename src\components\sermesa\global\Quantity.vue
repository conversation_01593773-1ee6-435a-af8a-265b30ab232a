<template>

    <div class="container quantity d-flex  flex-wrap">

        <vx-input-group>
            <template slot="prepend">
                <div class="vx-input-group-prepend">
                    <vs-button variant="info" class="button" @click="decrement()">-</vs-button>
                </div>
            </template>
            <vs-input type="number" style="min-width:45px;width:100%" min="0.00" :value="internalValue" readonly></vs-input>
            <template slot="append">
                <div class="vx-input-group-append">
                    <vs-button variant="info" class="button" @click="increment()">+</vs-button>
                </div>
            </template>
        </vx-input-group>
    </div>

</template>


<script>

export default {
    name: "Quantity",
    data() {
        return {
            internalValue: this.quantity
        };
    },
    props: ["quantity"],
    methods: {
        increment() {
            this.internalValue++;
        },
        decrement() {
            if (this.internalValue === 1) {
                this.internalValue = 1
            } else {
                this.internalValue--;
            }
        }, updateQuantity() {
            this.$emit("InitQuantity", 1);
        }

    },
    watch: {
        internalValue(val) {
            this.$emit("input", val)
        },
        'quantity': function (value) {
            this.internalValue = value
        }
    }
};

</script>
<style scoped>
.quantity {
    max-width: 100px;
    width: 100%;
}

.quantity div {
    margin-right: 0;
    margin-left: 0;
    display: flex;
    align-items: center;
}

.vs-button {
    width: 10px !important;
    height: 100%;
    display: flex;
    flex-direction: column;
    min-width: 1px;
}
</style>
