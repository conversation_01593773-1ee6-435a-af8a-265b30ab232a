<template>
    <vx-card title="Mantenimiento de cargos por Productos y Servicios">
        <div class="container">

            <div class="a-K8Gq2">

                <vs-row>
                    <vs-col vs-type="flex" vs-w="6" class="m-4">

                        <div class="ws-full">
                            <vs-input label="Producto" class="w-full" v-model="this.CodigoProducto" readonly/>
                        </div>

                        <SM-Buscar class="w-full" 
                                    label="Descripción Producto" 
                                    v-model="CodigoProducto"
                                    :disabled_busqueda="activarBusquedaProd" 
                                    api="app/inventario/ListaProductosInventario"
                                    api_campo_respuesta_mostrar="Nombre" 
                                    :api_campos="['Codigo', 'Nombre']"
                                    :api_titulos="['Codigo', 'Nombre']" 
                                    api_campo_respuesta="Codigo"
                                    :api_campo_respuesta_estricto="false" 
                                    :api_preload="false"
                                    :disabled_texto="true" 
                                    :api_filtro="{
                                        Nombre: this.TextoBusqueda,
                                        Tipo: null,
                                        Activo: null
                                    }" 
                                    :mostrar_busqueda="true" 
                                    :callback_buscar="CosultarValoresCargosProducto().Consultar"
                                    
                                     />
                    </vs-col>

                    <vs-col vs-type="flex" vs-w="2" class="m-6 pt-4"  >
                        <vs-button @click="RegistrarCargosProducto">
                                            Agregar
                        </vs-button>
                    </vs-col>

                </vs-row>

            </div>

            <div class="a-08MZm-1" >

               <DxDataGrid
                    id="gridValoresCargosProducto"
                    :data-source="listaProductos"
                    height="450"
                    :show-borders="true"
                    :column-auto-width="true"
                    :paging="{ enabled: true, pageSize: 8 }"
                    :row-alternation-enabled="true"
                    :search-panel="{ visible: true }"
                    :ref="refGridProducts"
                    @rowClick="onRowClick"
                    @contentReady="onContentReady"                    
                    @row-updating="updateRow"
                    @row-removing="removeRow"            
                    :allow-column-reordering="true"
                >
                <DxEditing 
                :allow-updating="true"
                :allow-deleting="true"  
                :use-icons="true"              
                    mode="row"
                
                />
                <span aria-hidden="true" class="fa fa-edit"></span>
                    <DxColumn caption="Código" data-field="Producto" :allow-editing="false" />
                    <DxColumn caption="Nombre" data-field="Nombre" :allow-editing="false" />
                    <DxColumn caption="Cargos Admisión Máximo" data-field="MaximoXadmision" />
                    <DxColumn caption="Cargos Admisión Mínimo" data-field="MinimoXadmision" />                    
                    <DxColumn caption="Cargos Producto Máximo" data-field="MaximoXproducto" />
                    <DxColumn caption="Cargos Producto Mínimo" data-field="MinimoXproducto" />                    
                   </DxDataGrid>

            </div>
            <div class="a-NOGwR">

                <div class="a-08MZm-1" style="overflow:scroll;height:100%;width:100%;overflow:auto; padding: 10px; ">

                    <DxDataGrid :data-source="bitacoraNoAutorizados" 
                                height="150"
                                :show-borders="true" 
                                :column-auto-width="true"
                                :paging="{ enabled: true, pageSize: 10 }" 
                                :row-alternation-enabled="true" 
                                :ref="refGridBitacora"                                
                    >                        
                        <DxColumn caption="Producto" data-field="Producto" />
                        <DxColumn caption="Observaciones" data-field="Observaciones" />
                        <DxColumn caption="FechaRegistro" data-field="FechaRegistro" :calculate-cell-value="formatDate" />                                          
                        <DxColumn caption="Usuario" data-field="Usuario" />
                    </DxDataGrid>

                    </div>
                
            </div>
        </div>


    </vx-card>
</template>


<script>


import {
    DxDataGrid,
    DxColumn,
    DxEditing
} from 'devextreme-vue/data-grid';


const refGridProducts = ''
const refGridBitacora =''
export default {
    name: "ConfiguracionMaxMinsProductos",
    components: {
        DxDataGrid,
        DxColumn,
        DxEditing
    },
    data() {
        return {
            refGridBitacora,
            refGridProducts,
            CodigoSeguro: null,
            CodigoProducto: '',            
            activarBusquedaProd: false,
            listaProductos: [],
            bitacoraNoAutorizados: [],
            TextoBusqueda: ''
        }


    },
    computed: {        
    },
    mounted() {        
        this.ConsultarConfiguracionCargos()

    },
    methods: {       
        async ConsultarConfiguracionCargos() {
            
            const resp = await this.axios.post('/app/Ajenos/ConsultaCargosMaxMinProducto', {})                    
            this.listaProductos = resp.data.json
        },
        CosultarValoresCargosProducto() {
            
            return {                
                Consultar: (data) => {                    
                    this.ConsultarProductosTipo(data.Codigo)
                }
            }
        },
        async RegistrarCargosProducto(){

            
            if(!this.CodigoProducto){
                return
            }
                        
            await this.axios.post('/app/Ajenos/RegistraCargosMaxMinProducto', {
                Producto: this.CodigoProducto
            })
            .then(resp => {
                if(resp.data.Codigo === 0){
                    this.listaProductos = []
                    this.listaProductos = resp.data.json
                }
            }).catch((error) => {
                this.$vs.dialog({
                                type: 'confirm',
                                color: 'danger',
                                title: 'Confirmación',
                                acceptText: 'Aceptar',
                                cancelText: 'Cancelar',
                                text: error.Descripcion,
                            })
            })
            
            this.CodigoProducto = ''
        },
        async removeRow(item){

            await this.axios.post('/app/Ajenos/EliminaCargosMaxMinProducto', {
                Producto: item.data.Producto
            })
            .then(resp => {
                
                if(resp.data.Codigo === 0){     
                    this.listaProductos.splice(0, this.listaProductos.length, ...resp.data.json);
                    this.CodigoProducto = '';                                                    
                    this.listaProductos = []
                    this.listaProductos = resp.data.json
                }
                else {this.listaProductos = []}
            }).catch((error) => {
                this.$vs.dialog({
                                type: 'confirm',
                                color: 'danger',
                                title: 'Confirmación',
                                acceptText: 'Aceptar',
                                cancelText: 'Cancelar',
                                text: error.Descripcion,
                            })
            })    
        },
        async onRowClick(dataRow) {
        
            const resp = await this.axios.post('/app/Ajenos/ConsultaBitacoraNoCubiertos', {
                //Aseguradora :dataRow.data.Aseguradora,
                Producto: dataRow.data.Producto
            }) 

            this.$refs[refGridBitacora].instance.option('dataSource', resp.data.json);

            this.$nextTick(() => {
                // Restaurar el pageIndex después de que el DOM se haya actualizado
                //gridInstance.pageIndex(currentPageIndex);
            });

        },
        onContentReady(){
            this.$refs[refGridBitacora].instance.option('dataSource', []);
        },
        async updateRow(row){
            
            const updatedData = {
                Aseguradora: row.key.Empresa,  // Este campo parece no cambiar, lo tomamos directamente de la clave
                Producto: row.key.Producto,    // Este campo tampoco cambia, también lo tomamos de la clave
                MaximoXadmision: row.newData.MaximoXadmision !== undefined ? row.newData.MaximoXadmision : row.oldData.MaximoXadmision,
                MinimoXadmision: row.newData.MinimoXadmision !== undefined ? row.newData.MinimoXadmision : row.oldData.MinimoXadmision,
                MaximoXproducto: row.newData.MaximoXproducto !== undefined ? row.newData.MaximoXproducto : row.oldData.MaximoXproducto,
                MinimoXproducto: row.newData.MinimoXproducto !== undefined ? row.newData.MinimoXproducto : row.oldData.MinimoXproducto
            };

                
            const resp = await this.axios.post('/app/Ajenos/ActualizaCargosMaxMinProducto', updatedData);
            this.listaProductos = resp.data.json
    
        },
        formatDate(rowData) {

            const fechaISO = rowData.FechaRegistro

            if (!fechaISO) return '';  // Retorna vacío si no hay fecha

            // Crear una fecha a partir del string ISO
            const fecha = new Date(fechaISO);

            // Obtener los componentes individuales
            const dia = fecha.getDate().toString().padStart(2, '0');
            const mes = (fecha.getMonth() + 1).toString().padStart(2, '0'); // Los meses son base 0
            const año = fecha.getFullYear();
            const horas = fecha.getHours().toString().padStart(2, '0');
            const minutos = fecha.getMinutes().toString().padStart(2, '0');

            // Formato MM/DD/YYYY HH:MM
            return `${mes}/${dia}/${año} ${horas}:${minutos}`;
        }
    
    }

}

</script>



<style scoped>
* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

.container {
    display: flex;
    flex-flow: row wrap;
    justify-content: flex-start;
    display: grid;
    grid-gap: 5px;
    width: 100%;
    height: 100%;
    grid-template-areas: "a-K8Gq2"
        "a-08MZm-1"
        "a-NOGwR";
    grid-template-columns: 1fr;
    grid-template-rows: 0.2fr 0.9fr 0.40fr;
}

.container>div {
    border: 1px dashed #888;
}


.a-K8Gq2 {
    grid-area: a-K8Gq2;
}

.a-08MZm-1 {
    grid-area: a-08MZm-1;
}

.a-NOGwR {
    grid-area: a-NOGwR;
}


.container>div {
    border: 1px dashed #888;
}

.a-qQoBr {
    grid-area: a-qQoBr;
}

.a-K8Gq2 {
    grid-area: a-K8Gq2;
}

.a-08MZm-1 {
    grid-area: a-08MZm-1;
}

.container>div {
    border: 1px solid #888;
}

.container {
    max-width: 100%;
}

.dxgvSearchPanel>table {
    width: 100%;
}
</style>