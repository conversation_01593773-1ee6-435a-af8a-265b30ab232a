<template>
<div class="p-2">
    <DxDataGrid ref="confGrid" :data-source="Configuracion" :visible="true" @editor-preparing="preparing" :show-borders="true" @saved="Grabar" :showColumnLines="true" :showBorders="true" :searchPanel="{visible: true}" :rowAlternationEnabled="true" width="100%" height="calc(100vh - 310px)" :column-hiding-enabled="true" :word-wrap-enabled="false" @cell-prepared="CellPrepared" :headerFilter="{visible:true}" :toolbar="GridToolBar">

        <DxEditing :allow-updating="true" :allow-adding="true" :allow-deleting="true" mode="popup" :popup="EditorPopup">
            <DxForm ref="myForm" label-location="top" :col-count-by-screen="{lg:4, md:2}">

            </DxForm>
        </DxEditing>

        <DxColumn data-field="CodigoSubEspecialidad" :visible="true" caption="Subespecialidad" :allow-editing="true" :allow-filtering="false">
            <DxRequiredRule />
            <DxLookup :dataSource="Subespecialidades" value-expr="Codigo" :display-expr="(x)=> x?x.Codigo + ' - ' + x.Nombre:''" />
        </DxColumn>

        <DxColumn data-field="Periodicidad" width="200" caption="Periodicidad de envío del correo" :set-cell-value="setCellValue">
            <DxRequiredRule />
            <DxLookup :dataSource="PeriodicidadEnvioCorreo" value-expr="id" :display-expr="(x)=> x?x.name:''" />
        </DxColumn>

        <DxColumn data-field="Mes" data-type="number" width="100" :set-cell-value="setCellMesValue">
            <DxLookup :dataSource="Meses" value-expr="id" :display-expr="(x)=> x?x.name:''" />
        </DxColumn>

        <DxColumn data-field="Dia" data-type="number" width="100" :calculate-display-value="calculatedisplayvalue">
            <DxCustomRule message="El día es requerido" type="custom" :validation-callback="validaDias" />
            <DxLookup :dataSource="DiasDataSource" value-expr="id" :display-expr="(x)=> x?x.name:''" />
        </DxColumn>

        <DxColumn data-field="CuerpoCorreo" :encode-html="false" caption="Mensaje del correo electrónico por defecto" width="100%">
            <DxFormItem :col-span="2" :editor-options="htmlEditorOptions" editor-type="dxHtmlEditor" />
        </DxColumn>
        <DxColumn data-field="ObseracionEstadoCuenta" :encode-html="false" caption="Observaciones reporte estado de cuenta" width="100%">
            <DxFormItem :col-span="2" :editor-options="htmlEditorOptions" editor-type="dxHtmlEditor" />
        </DxColumn>
        <DxColumn caption="Acciones" type="buttons" :buttons="[{name:'edit',icon: 'edit'}, {name:'delete',icon:'trash', cssClass:'.dx-button.dx-button-warning'},]" :width="80" :allow-reordering="false" :allow-resizing="false" />
    </DxDataGrid>
</div>
</template>

<script>
import {
    DxDataGrid,
    DxColumn,
    DxFormItem,
    DxEditing,
    DxLookup,
    DxForm,
    DxRequiredRule,
    DxCustomRule,
} from 'devextreme-vue/data-grid'

import 'devextreme-vue/html-editor'
import 'devextreme-vue/lookup'

export default {
    components: {
        DxDataGrid,
        DxColumn,
        DxFormItem,
        DxEditing,
        DxLookup,
        DxRequiredRule,
        DxCustomRule,
        DxForm,
    },
    data() {
        return {

            Configuracion: [],
            //catalogos
            Subespecialidades: [],
            PeriodicidadEnvioCorreo: [],
            DiasDataSource: (data) => {
                if (!data)
                    return []
                if (data.Periodicidad == 'S')
                    return this.posCrearDiasSemana()
                if (['M', 'Q'].includes(data.Periodicidad))
                    return this.posCrearDias(1) //31 dias
                if (['A', '6M'].includes(data.Periodicidad) && data.Mes)
                    return this.posCrearDias(data.Mes)
                return []
            },

            Meses: [],
            htmlEditorOptions: {
                height: 500,
                toolbar: {
                    multiline: false,
                    items: [
                        "undo",
                        "redo",
                        'clear',
                        "separator",
                        {
                            name: "header",
                            acceptedValues: [false, 1, 2, 3, 4, 5],
                            options: {
                                inputAttr: {
                                    'aria-label': 'Encabezado'
                                }
                            }
                        },
                        "separator",
                        "bold",
                        "italic",
                        "strike",
                        "underline",
                        "separator",
                        "alignLeft",
                        "alignCenter",
                        "alignRight",
                        "alignJustify",
                        "separator",
                        'background',
                        'color', 'italic', 'link', 'increaseIndent', 'decreaseIndent',
                        'orderedList',
                        'bulletList',

                    ]
                }
            },
            GridToolBar: {
                items: [{
                    name: 'addRowButton',
                    showText: 'always',
                    options: {
                        type: 'success',
                        text: 'Nuevo'
                    }
                }],
            },
            EditorPopup: {
                toolbarItems: [{
                        widget: 'dxButton',
                        location: 'after',
                        toolbar: 'bottom',
                        options: {
                            text: 'Guardar Cambios',
                            type: 'default',
                            useSubmitBehavior: true,
                            onClick: () => {
                                this.dataGrid.saveEditData()
                            }
                        }
                    },
                    {
                        widget: 'dxButton',
                        location: 'after',
                        toolbar: 'bottom',
                        options: {
                            text: 'Cancelar',
                            type: 'danger',
                            useSubmitBehavior: true,
                            onClick: () => {
                                this.dataGrid.cancelEditData()
                            }
                        }
                    },
                ],
            },
        }
    },
    created() {

    },
    computed: {
        SubespecialidadesDisponibles() {
            let result = []
            let dips = this.Configuracion
            this.Subespecialidades.map((x) => {
                let f = dips.find((t) => t.CodigoSubEspecialidad == x.Codigo)
                if (!f)
                    result.push(x)
            })
            return result
        },
        dataGrid: function () {
            return this.$refs['confGrid'].instance
        },
    },
    beforeMount() {
        this.axios.post('/app/Ajenos/Busqueda_SubEspecialidad', {}).then(resp => {
            this.Subespecialidades = resp.data.json
        })
        this.CargarConfiguracion()
        this.PeriodicidadEnvioCorreo = [{
            id: 'A',
            name: 'Anual'
        }, {
            id: '6M',
            name: 'Semestral'
        }, {
            id: 'M',
            name: 'Mensual'
        }, {
            id: 'Q',
            name: 'Quincenal'
        }, {
            id: 'S',
            name: 'Semanal'
        }, {
            id: 'NA',
            name: 'No enviar automáticamente'
        }]
        this.Meses = this.posCrearMeses()

    },
    watch: {

    },
    methods: {
        /**
         * Grabamos el objeto y se recarga :)
         */
        //los setcellvalue se usan para provocar la actualización de los demas componentes y hacer las opciones de los demas editores dinamicas en base al valor actual
        //https://js.devexpress.com/Vue/Documentation/Guide/UI_Components/DataGrid/How_To/Dynamically_Change_Editor_Properties_in_the_Editing_State/
        //
        CellPrepared(e) {
            if (e.column && e.column.dataField == 'Dia')
                e.column.lookup.dataSource = this.DiasDataSource(e.data)
        },
        async CargarConfiguracion() {
            return this.axios.post('/app/Honorarios/ObtenerConfCorreoEsp', {}).then(resp => {
                this.Configuracion = resp.data.json
            }).catch(() => {
                this.Configuracion = []
            })
        },
        setCellValue(newData, value) {
            if (!['A', '6M'].includes(value)) {
                newData.Mes = null
            }
            newData.Dia = null
            newData.Periodicidad = value
        },

        setCellMesValue(newData, value) {
            newData.Dia = null
            newData.Mes = value
        },
        Grabar() {
            this.axios.post('/app/Honorarios/GuardarConfCorreoEsp', {
                Configuraciones: this.Configuracion
            }).catch(() => this.CargarConfiguracion())

        },
        preparing(e) {
            //Excluir las subespecialidades que ya estan agregadas
            if (e.editorName == 'dxSelectBox' && e.dataField == 'CodigoSubEspecialidad') {
                let opciones = [...this.SubespecialidadesDisponibles]
                let currentelement = this.Subespecialidades.find(x => x.Codigo == e.row.data.CodigoSubEspecialidad)
                if (currentelement)
                    opciones.push(currentelement)
                e.editorOptions.dataSource = opciones
            }

            if (e.dataField === "Mes" && e.parentType === "dataRow") {
                e.editorOptions.disabled = e.row.data && !['A', '6M'].includes(e.row.data.Periodicidad)
            }

            if (e.dataField === "Dia" && e.parentType === "dataRow") {
                e.editorOptions.validationRules = e.row.data.Periodicidad != 'NA' ? null : {
                    name: 'required'
                }
                e.editorOptions.dataSource = this.DiasDataSource(e.row.data)
            }
            if (e.editorName == 'dxSelectBox' && e.dataField == 'Dia') {
                if (e.row.data && 'NA' == e.row.data.Periodicidad) {
                    e.editorOptions.disabled = true
                    e.editorOptions.value = null
                }
            }
        },
        validaDias(e) {
            return (e.data && (e.data.Periodicidad == 'NA' || e.data.Dia != null))
        },
        posCrearDias(montIndex) {
            return Array.from(new Array(montIndex == 2 ? 28 : (montIndex > 7 ? montIndex + 1 : montIndex) % 2 == 0 ? 30 : 31), (obj, index) => {
                return {
                    id: index + 1,
                    name: String(index + 1)
                }
            })
        },
        posCrearDiasSemana() {
            //1976-02-00 para que se ajuste a lunes a Monday
            let i = 7
            let res = new Array(i)
            return Array.from(res, ((obj, i) => {
                return {
                    id: i,
                    name: new Date(Date.UTC(2017, 0, 2 + i)).toLocaleDateString('es-gt', {
                        weekday: 'long'
                    })
                }
            }))
        },
        posCrearMeses() {
            //1976-02-00
            return Array.from(new Array(12), ((obj, i) => {
                return {
                    id: i + 1,
                    name: new Date(Date.UTC(0, i, 28)).toLocaleDateString('es-gt', {
                        month: 'long'
                    })
                }
            }))
        },
        calculatedisplayvalue(e) {
            let element = this.DiasDataSource(e).find(x => x.id == e.Dia)
            return element ? element.name : ''
        },
    },
}
</script>
