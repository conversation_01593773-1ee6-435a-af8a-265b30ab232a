
<template>
    <vx-card title="Cosulta de Pacientes">
        <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
            <form @submit.prevent="handleSubmit(submitForm())">
                <div class="flex flex-wrap">
                    <vs-row>

                        <vs-col vs-type="flex" vs-w="6">
                            <div class="w-4/5  p-2">
                                <validationProvider name="Nombre Paciente">
                                    <vs-input v-model="consultaPaciente.NombrePaciente" label="Nombre Paciente"
                                        class="w-full" />
                                </validationProvider>
                            </div>
                        </vs-col>


                        <vs-col vs-type="flex" vs-w="6">
                            <div class="w-4/5  p-2">
                                <validationProvider name="Documento">
                                    <vs-input v-model="consultaPaciente.NumeroDocumento" label="No. Documento"
                                        class="w-full" />
                                </validationProvider>
                            </div>
                        </vs-col>

                    </vs-row>

                    <vs-row class="justify-end pr-10">
                        <vs-button @click="handleSubmit(ConsultarPaciente().Consultar())" :disabled="invalid">
                            Consultar
                        </vs-button>
                    </vs-row>
                </div>
            </form>
        </ValidationObserver>
        <vs-table2 max-items="10" search tooltip pagination :data="listaPacientes">
            <template slot="thead">
                <th width="50px" filtro="Serie">Serie</th>
                <th width="50px" filtro="Admision">Admision</th>
                <th width="50px" filtro="Paciente">Paciente</th>
                <th width="50px" filtro="Cliente">Cliente</th>
                <th width="330px" filtro="NombrePaciente">NombrePaciente</th>
            </template>

            <template slot-scope="{data}">
                <tr :key="indextr" v-for="(tr, indextr) in data" :ref="`list_${indextr}`" @contextmenu="Menu(tr)"
                    @dblclick="Menu(tr)" :class="{ 'activo': tr.activo }">

                    <vs-td2>
                        {{ tr.Serie }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.Admision }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.Codigo }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.IdCliente }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.NombrePaciente }}
                    </vs-td2>

                </tr>
            </template>

        </vs-table2>

    </vx-card>
</template>


<script>

export default {
    name: "ConsultaPacienteAdmision",
    data() {
        return {
            listaPacientes: [],
            consultaPaciente: {
                Empresa: '',
                NombrePaciente: '',
                NumeroDocumento: ''
            }
        }

    },
    methods: {
        ConsultarPaciente() {
            return {

                Consultar: async () => {
                    const resp = await this.axios.post('/app/v1_admision/ConsultaPacienteAdmision', this.consultaPaciente)
                    this.listaPacientes = resp.data.json.map(m => {
                        return {
                            ...m,

                        }
                    })
                },

            }
        },
        Menu(item) {

            this.$store.dispatch('admision/setSerieRaiz', item.Serie)
            this.$store.dispatch('admision/setAdmisionRaiz', item.Admision)
            delete item.Serie;
            delete item.Admision;
            this.$store.dispatch('paciente/setPaciente', item)
            
        }
    }

};
</script>
  
