<!-- Modificacion Id admision -->
<template>
<vx-card type="3" title="Modificación Id Admisión" class="justify-center ">
    <ValidationObserver ref="formValidate" mode="lazy">
        <form>
            
            <BusquedaAdmision ref="componenteAdmisiones" @datos_admision="DatosAdmision" 
                :api_filtro="{'Opcion':'CONSULTA','SubOpcion':'EMERGENCIA','activo':1,'tieneHabitacion':0}" 
                @limpiar_datos_admision="Limpiadatos" 
                :api_campos="['Serie','Codigo','Nombres','Apellidos','Habitacion']" 
                :api_titulos="['Serie#','Admision#','Nombres','Apellidos','Habitación']">
            </BusquedaAdmision>
            <div class="pt-2 pl-1">
                <label style="color: crimson;">Nota: Solo se validan las admisiones activas</label>
            </div>

            <div class="flex flex-wrap">
                <div class="sm:w-full md:w-full lg:w-full xl:w-1/12 m-1">
                    <SM-Buscar v-model="info.idCliente" 
                        label="Id Cliente" api="app/emergencia/BuscaCliente" 
                        :api_campos="['IdCliente','IdPaciente','Nombre','Apellido']" 
                        :api_titulos="['IdCliente#','IdPaciente#','Nombre','Apellido']" api_campo_respuesta="IdCliente" 
                        :api_filtro = "{'TipoBusca':1}"
                        :callback_buscar="LlenarCliente" 
                        :callback_cancelar="LimpiarCliente"
                    />                    
                </div>
                <div class="sm:w-full md:w-full lg:w-full xl:w-2/12 m-1">
                    <ValidationProvider rules="required" v-slot="{ errors }">
                        <vs-input label="Nombre del Cliente:" class="w-full" 
                        :value="info.nombreCliente" disabled 
                        :danger="errors.length > 0" 
                        :danger-text="(errors.length > 0) ? errors[0] : null" />
                    </ValidationProvider>
                </div>
                <div class="sm:w-full md:w-full lg:w-full xl:w-3/12 p-1 m-">
                        <vs-input label="Paciente:" class="w-full" 
                        :value="info.idPaciente" disabled  />
                </div>
            </div>

            <div class="w-full md:w-full lg:w-10/12 xl:w-full">                
                        <PlanesPorCliente   @poliza-seleccionada="obtenerDatosAfiliado"
                                            :IdCliente="info.idCliente?info.idCliente:''"
                                            :Opcion="2">
                        </PlanesPorCliente>
                    </div>

            <vs-textarea class="sm:w-full md:w-full lg:w-full xl:w-full m-1" label="Motivo del cambio" counter="200" v-model="info.observaciones" />
            <br />
            <div class="flex flex-wrap">
                <div class="sm:w-full md:w-full lg:w-full xl:w-full m-2 p-2"></div>
                <vs-button color="success" 
                    @click="CambioIdAdmision" 
                    :disabled="!this.info.admision || !info.idCliente || info.estadoPlan !='A'">Procesar
                </vs-button>
            </div>
        </form>
    </ValidationObserver>
</vx-card>
</template>

<script>
import BusquedaAdmision from "/src/components/sermesa/modules/admisiones/BusquedaAdmision.vue"
import PlanesPorCliente from "/src/components/sermesa/modules/admisiones/ConsultaAfiliadoPorCliente.vue"

export default {
    components: {
        BusquedaAdmision,
        PlanesPorCliente
    },
    data() {
        return {
            info: {
                admSerie: null,
                admision: null,
                observaciones: null,
                idPaciente: null,
                admPaciente:null,
                idCliente: '',
                nombreCliente: null,
                afiliado: null,
                poliza: null,
                estadoPlan: null
            }
        }
    },
    methods: {
        DatosAdmision(datos) {
            this.info.admSerie = datos.Serie
            this.info.admision = datos.Codigo
            this.info.admPaciente = datos.IdPaciente
        },
        Limpiadatos() {
            this.$refs.componenteAdmisiones.limpiar_campos()
            this.info.serie = null
            this.info.admision = null
            this.info.observaciones = null
        },
        LlenarCliente(datos) {
            this.info.idPaciente = datos.IdPaciente
            this.info.nombreCliente = datos.Nombre + ' ' + datos.Apellido

        },
        LimpiarCliente() {
            this.info.poliza   = null
            this.info.paciente = null
            this.info.afiliado = null
            this.info.idCliente  = ''
            this.info.estadoPlan = null
            this.info.nombreCliente = null
        },
        obtenerDatosAfiliado(plan){
            this.info.poliza   = plan.IdPoliza
            this.info.afiliado = plan.IdAfiliado
            this.info.paciente = plan.IdPaciente
            this.info.estadoPlan = plan.Status
        },
        CambioIdAdmision(){
            if (!this.info.observaciones) {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Alerta',
                    text: 'Debe de ingresar el motivo del cambio',
                })
                return false
            }
            this.axios.post(
                '/app/emergencia/ActualizaAdmision', {
                    serie: this.info.admSerie,
                    codigoAdmision: this.info.admision,
                    observaciones: this.info.observaciones,
                    tipoActualizacion: 5,
                    idCliente: this.info.idCliente,
                    idAfiliado: this.info.afiliado
                }
            ).then(
                (respuesta) => {
                    const Respuesta = respuesta.data ? respuesta.data.json[0].descripcion : respuesta.json.descripcion
                    const CodigoError = respuesta.data ? respuesta.data.json[0].tipo_error : respuesta.json.tipo_error
                    if (CodigoError == 1) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: Respuesta,
                            time: 4000,
                            position: 'bottom-center'
                        })
                        return
                    }

                    if (CodigoError == -1) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: Respuesta,
                            time: 4000,
                            position: 'bottom-center'
                        })
                        return
                    }

                    this.Limpiadatos()
                    this.LimpiarCliente()
                    this.$vs.notify({
                        color: 'success',
                        title: 'Cambio Id Admisión',
                        text: Respuesta
                    })
                }
            )
        }
    }
}
</script>
