<template>
<div>
    <DxForm :ref="formConsulta" :form-data.sync="formulario" label-mode="floating" :read-only="true">
        <DxFormGroupItem :col-count="2">
            <DxFormGroupItem :col-count="4" css-class="buttonsOrdenes">
                <DxFormItem data-field="NumeroOrden" editor-type="dxNumberBox">
                    <DxFormLabel text="Número" />
                </DxFormItem>
                <DxFormItem data-field="FechaAutorizacion" editor-type="dxDateBox" :editor-options="{ readOnly: true, dataType: 'date', displayFormat: 'dd/MM/yyyy' }">
                    <DxFormLabel text="Fecha" />
                </DxFormItem>
                <DxFormItem data-field="Entregado" editor-type="dxDateBox" :editor-options="{ readOnly: true, dataType: 'date', displayFormat: 'dd/MM/yyyy' }" />
                <DxFormItem data-field="Recibo" editor-type="dxNumberBox" />
            </DxFormGroupItem>
            <DxFormGroupItem :col-count="12">
                <DxFormItem :col-span="8" data-field="PagarNombre" editor-type="dxTextBox" :editor-options="{ readOnly: !((Boolean(this.formulario.NumeroOrden) && this.formulario.Funcion == 'CH') || ((this.formulario.Funcion == 'ND' || this.formulario.Funcion == 'NA') && this.formulario.Proveedor !== null && this.formulario.Proveedor !== '')) }">
                    <DxFormLabel text="Pagar a la orden de" />
                </DxFormItem>
                <DxFormItem :col-span="4" data-field="Monto" editor-type="dxNumberBox" :editor-options="{ format: '#0.00', step: 0.01 }" />
            </DxFormGroupItem>
        </DxFormGroupItem>

        <DxFormGroupItem :col-count="2">
            <DxFormGroupItem>
                <DxFormItem data-field="Observaciones" editor-type="dxTextArea" :editor-options="{ height: '100%', readOnly: !((Boolean(this.formulario.NumeroOrden) && this.formulario.Funcion == 'CH') || ((this.formulario.Funcion == 'ND' || this.formulario.Funcion == 'NA') && this.formulario.Proveedor !== null && this.formulario.Proveedor !== '')) }" />
            </DxFormGroupItem>
            <DxFormGroupItem :col-count="2">
                <DxFormItem data-field="NoNegociable" editor-type="dxCheckBox">
                    <DxFormLabel text="No negociable" />
                </DxFormItem>
                <DxFormItem template="textoEstado" />
            </DxFormGroupItem>
        </DxFormGroupItem>

        <template #textoEstado="{}">
            <div>
                <span style="color: blue; font-size: 14px; font-weight: bold;" v-if="formulario.Impresion == null && formulario.Status !== 'A' && formulario.Status !== 'X'">**PENDIENTE DE IMPRESIÓN**</span>
                <span style="color: red; font-size: 14px; font-weight: bold;" v-if="formulario.Status == 'A'">**ANULADO**</span>
                <span style="color: red; font-size: 14px; font-weight: bold;" v-if="formulario.Status == 'X'">**ANULADO EXTEMPO**</span>
                <br>
                <span style="color: red; font-size: 14px; font-weight: bold;" v-if="formulario.FechaAnula !== null">{{ formulario.UsuarioAnula }} {{ $formato_fecha(formulario.FechaAnula, 'dd/MM/yyyy hh:mm') }}</span>
            </div>
        </template>
    </DxForm>
    <div class="pt-4">
        <DxDataGrid :ref="gridDetalle" v-bind="DefaultDxGridConfiguration" :data-source="valoresTabla" :headerFilter="{ visible: false, allowSearch: false }" :searchPanel="{ visible: false }" :width="'100%'" height="auto">
            <DxDataGridSelection mode="single" />

            <DxDataGridColumn width="10%" data-field="Documento" alignment="center" data-type="string" />
            <DxDataGridColumn width="30%" data-field="Detalle" alignment="center" data-type="string" />
            <DxDataGridColumn width="10%" data-field="Cuenta" alignment="center" data-type="string" />
            <DxDataGridColumn width="30%" data-field="Nombre" caption="Nombre de la cuenta" alignment="center" data-type="string" />
            <DxDataGridColumn width="10%" data-field="Debe" alignment="center" data-type="number" :customize-text="customizeTextValores" />
            <DxDataGridColumn width="10%" data-field="Haber" alignment="center" data-type="number" :customize-text="customizeTextValores" />
            <DxDataGridSummary>
                <DxDataGridTotalItem column="Debe" summary-type="sum" :customize-text="customizeTextValores" :value-format="{ type: 'fixedPoint', precision: 2 }" />
                <DxDataGridTotalItem column="Haber" summary-type="sum" :customize-text="customizeTextValores" />
            </DxDataGridSummary>
        </DxDataGrid>
    </div>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration,
    customizeTextValores
} from './data'
import 'devextreme-vue/lookup'

const formConsulta = 'formConsulta'
const gridDetalle = 'gridDetalle'

export default {
    name: 'Consulta',
    components: {},
    data() {
        return {
            DefaultDxGridConfiguration,
            formConsulta,
            gridDetalle,

            formulario: {},

            valoresTabla: [],
        }
    },
    props: {
        TipoDoc: null, // Indica desde qué módulo se invoca el componente  1 = Cheques / 2 = Transferencias
        Corporativo: null,
        TipoCheque: null,
        ModalidadPago: null,
        InformacionCheque: null
    },
    methods: {

        async BuscarOrden(e) {
            await this.axios.post('/app/v1_bancos/ConsultaAnulacion', {
                    Opcion: 3,
                    EmpresaCheque: e.Empresa,
                    Cuenta: e.Cuenta,
                    TipoCheque: e.Tipo,
                    ChequeInicial: e.Numero,
                })
                .then(resp => {
                    this.valoresTabla = []
                    if (resp.data.json.length > 0) {
                        this.valoresTabla = resp.data.json
                    }
                })
        },

        async AsignarValores(e) {
            this.formulario.NumeroOrden = e.Numero
            this.formulario.FechaAutorizacion = e.Fecha
            this.formulario.PagarNombre = e.NombreBeneficiario
            this.formulario.Observaciones = e.Observaciones
            this.formulario.Monto = e.Monto
            this.formulario.FechaAnula = e.FechaAnula
            this.formulario.Impresion = e.Impresion
            this.formulario.Status = e.Status
            this.formulario.UsuarioAnula = e.UsuarioAnula
            this.formulario.NoNegociable = e.NoNegociable == 'S' ? true : false
            this.formulario.Entregado = e.Entregado
            this.formulario.Recibo = e.Recibo

            await this.BuscarOrden(e)

            this.formConsultaInstance.repaint()
        },

        customizeTextValores
    },
    created() {},
    mounted() {
        if (this.InformacionCheque != null) {
            this.AsignarValores(this.InformacionCheque)
        }

    },
    watch: {
        'InformacionCheque'(newval) {
            this.AsignarValores(newval)
        }
    },
    computed: {
        colCountByScreen() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 2,
                    lg: 2
                };
        },

        formConsultaInstance: function () {
            return this.$refs[formConsulta].instance;
        },

        dataGridDetalle: function () {
            return this.$refs[gridDetalle].instance;
        },
    }
}
</script>

<style>

</style>
