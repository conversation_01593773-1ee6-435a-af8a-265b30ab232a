<template>
    <div>
        <vx-card title="Aplicación de Descuentos">
            <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                <vs-row vs-type="flex" vs-w="12">
                    <div class="vsCol divCol">
                        <vs-col vs-type="flex" vs-justify="center" vs-offset="6" vs-align="center" vs-lg="1" vs-sm="1" vs-xs="12"> 
                            <label>Admisión: </label>
                        </vs-col>
                    </div>
                    <vs-col vs-justify="flex-start" vs-align="flex-start" vs-type="flex" vs-lg="3" vs-sm="4" vs-xs="12">
                            <SM-Buscar v-model.trim="codigoOrden" 
                                api="app/v1_JefeCaja/AdmisionDescuento"
                                :api_campos="['SerieAdmision', 'Admision', 'Apellido', 'ApellidoCasada', 'Nombre', 'Habitacion']"
                                :api_titulos="['Serie', 'Admision', 'Apellido', 'ApellidoCasada', 'Nombre', 'Habitacion']" 
                                :api_filtro="{Opcion: 'C', SubOpcion: '3', Hospital: '0'}"
                                api_campo_respuesta="NumeroAdmision"
                                api_campo_respuesta_mostrar="NumeroAdmision"
                                :api_preload="false" :disabled_texto="true"                  
                            />
                    </vs-col>
                    <div class="vsCol2">
                        <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="12">
                                <input class="label-info" v-model="refNombre" disabled />
                        </vs-col>
                    </div>
                </vs-row>

                <vs-row vs-type="flex" vs-w="12">
                    <div class="vsCol divCol2">
                        <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-lg="12" vs-sm="1" vs-xs="12"> 
                            <label>Porcentaje: </label>
                        </vs-col>
                    </div>
                    <div class="vsCol">
                        <vs-col vs-justify="flex-start" vs-type="flex" vs-lg="12" vs-sm="3" vs-xs="12"> 
                            <vs-input v-model="porcentajeDescuento" :disabled="isPorcentajeDescuento"></vs-input>
                        </vs-col>
                    </div>
                    <div class="vsCol divCol2">
                        <vs-col vs-type="flex" vs-justify="center"  vs-align="center" vs-lg="12" vs-sm="1" vs-xs="12"> 
                            <label>Razón: </label>
                        </vs-col>
                    </div>
                    <div class="vsCol">
                        <vs-col vs-justify="flex-start" vs-type="flex" vs-lg="12" vs-sm="3" vs-xs="12">
                            <ValidationProvider name="razon" rules="required" class="required" v-slot="{ errors }">
                                <vs-input v-model="razon" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" ></vs-input>
                            </ValidationProvider>
                        </vs-col>
                    </div>
                    <div class="vsCol divCol2">
                        <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-lg="12" vs-sm="1" vs-xs="12"> 
                            <label>Tipo Beneficio: </label>
                        </vs-col>
                    </div>
                    <div class="vsCol">
                        <vs-col vs-justify="flex-start" vs-type="flex" vs-lg="12" vs-sm="3" vs-xs="12"> 
                            <!-- <label class="typo_label">Factura IGSS</label> -->
                            <multiselect v-model="selectTipoBeneficio" :options="listTipoBeneficio" placeholder="Beneficio" :show-labels="false" track-by="IdDescuento" label="descripcion" :disabled="isBeneficio" @input="onChangeTipoBeneficio"></multiselect>
                        </vs-col>
                    </div>
                </vs-row>
                <vs-row vs-type="flex" vs-w="12">
                    <div class="vsCol" hidden>
                        <vs-col vs-justify="flex-start" vs-type="flex" vs-lg="12" vs-sm="3" vs-xs="12"> 
                            <ValidationProvider name="beneficioBI" rules="" vid="beneficioBI" >
                                <vs-input v-model="beneficioCargado" disabled></vs-input>
                            </ValidationProvider>
                        </vs-col>
                    </div>
                </vs-row>
                <vs-row vs-type="flex" vs-w="12">
                    <div class="vsCol divCol2">
                        <vs-col vs-type="flex" vs-justify="center" vs-offset="6" vs-align="center" vs-lg="1" vs-sm="1" vs-xs="12"> 
                            <label>Descuento: </label>
                        </vs-col>
                    </div>
                    <div class="vsCol">
                        <vs-col vs-justify="flex-start" vs-type="flex" vs-lg="12" vs-sm="3" vs-xs="12"> 
                            <vs-input v-model="descuento" :disabled="isDescuento"></vs-input>
                        </vs-col>
                    </div>
                    <div class="vsCol divCol2">
                        <vs-col vs-type="flex" vs-justify="center" vs-offset="12" vs-align="center" vs-lg="1" vs-sm="1" vs-xs="12"> 
                            <label>Autorización: </label>
                        </vs-col>
                    </div>
                    <div class="vsCol">
                        <vs-col vs-justify="flex-start" vs-type="flex" vs-offset="3" vs-lg="12" vs-sm="3" vs-xs="12">
                            <ValidationProvider name="autorizacion" rules="required_if:beneficioBI,1" v-slot="{ errors }">
                                <vs-input v-model="autorizacion" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" ></vs-input>
                            </ValidationProvider>
                        </vs-col>
                    </div>
                </vs-row>
                <vs-row vs-type="flex" vs-w="12">
                    <div class="vsCol divcol3">
                        <vs-col vs-type="flex" vs-justify="center" vs-offset="6" vs-align="center" vs-lg="1" vs-sm="1" vs-xs="12">
                            <label>Tipo: </label>
                        </vs-col>
                    </div>
                    <div class="vsCol">
                        <vs-col vs-justify="flex-start" vs-type="flex" vs-lg="12" vs-sm="6" vs-xs="12">
                            <vs-radio v-model="tipoDescuento" vs-name="radio1" vs-value="N" :disabled="isTipo">Sin Descuento</vs-radio>
                        </vs-col>
                    </div>
                    <div class="vsCol">
                        <vs-col vs-justify="flex-start" vs-type="flex" vs-lg="12" vs-sm="6" vs-xs="12">
                            <vs-radio v-model="tipoDescuento" vs-name="radio1" vs-value="A" :disabled="isTipo">Arbitrario</vs-radio>
                        </vs-col>
                    </div>
                </vs-row>
                <vs-row vs-type="flex" vs-w="6">
                    <div class="w-full flex flex-wrap-nowrap btnGuardar">
                        <div>
                            <vs-button @click="handleSubmit(AplicarDescuento)" :disabled="invalid" color="success" class="w-full" icon-pack="fas" icon="fa-check-circle">Guardar</vs-button>
                        </div>
                    </div> 
                </vs-row>
                <!-- <div class="w-full md:w-4/5 lg:w-4/5 xl:w-4/5">

                </div> -->   
            </ValidationObserver>
        </vx-card>
    </div>
</template>
<script>

    import Multiselect from "vue-multiselect"
    import "vue-multiselect/dist/vue-multiselect.min.css"
    // import { extend } from "vee-validate"

    export default {
        data() {
            return{
                serie: '',
                admision: '',
                codigoOrden: '',
                pacienteId: '',
                nombres: '',
                apellidos: '',
                apellidoCasada: '',
                porcentajeDescuento: '',
                razon: '',
                selectTipoBeneficio: '',
                listTipoBeneficio: [],
                descuento: '',
                autorizacion: '',
                tipoDescuento: '',
                isBeneficio: true,
                refNombre: '',
                isPorcentajeDescuento: false,
                isDescuento: false,
                beneficioCargado: '',
                isTipo: false,
                TipoBeneficioSeleccionado: ''
            }
        },
        components: {
            Multiselect
        },
        methods: {
            ObtenerBeneficios(){
                this.axios.post('/app/v1_JefeCaja/Beneficios', {
                    "SerieAdmision": this.serie,
                    "Admision": this.admision
                })
                .then(resp => {
                    this.listTipoBeneficio = resp.data.json.map(m => {
                        return{
                            ...m
                        }
                    })
                    this.isBeneficio = false
                })
            },
            Validaciones(){
                if(this.serie != '' && this.admision != ''){
                    this.axios.post('/app/v1_JefeCaja/ValidacionDescuento', {
                        "SerieAdmision": this.serie,
                        "Admision": this.admision
                    })
                    .then(resp => {
                        
                        const { Apellido, ApellidoCasada, Nombre, AutorizacionBI, IdDescuento, DescripcionDescuento, TipoDescuento, PorcentajeDescuento, Descuento} = resp.data
                        
                        if(ApellidoCasada != ''){
                            this.refNombre = Nombre + ' ' + Apellido + ' DE ' + ApellidoCasada
                        }else{
                            this.refNombre = Nombre + ' ' + Apellido
                        }
                        this.ObtenerBeneficios()
                        this.tipoDescuento = TipoDescuento
                        this.porcentajeDescuento = PorcentajeDescuento ? parseFloat(PorcentajeDescuento).toFixed(2) : PorcentajeDescuento
                        this.autorizacion = AutorizacionBI
                        this.descuento = Descuento ? parseFloat(Descuento).toFixed(2) : Descuento

                        if(IdDescuento != ''){
                            this.beneficioCargado = IdDescuento
                            this.selectTipoBeneficio = {
                                IdDescuento : IdDescuento,
                                descripcion: DescripcionDescuento

                            }
                        }
                        
                    })
                }
                
            },
            AplicarDescuento(){
                this.axios.post('/app/v1_JefeCaja/AplicarDescuento', {
                    "SerieAdmision": this.serie,
                    "Admision": this.admision,
                    "PorcentajeDescuento": this.porcentajeDescuento == '' ? '0' : this.porcentajeDescuento,
                    "Descuento": this.descuento == '' ? '0' : this.descuento,
                    "Razon": this.razon,
                    "TipoDescuento": this.tipoDescuento,
                    "AutorizacionBI": this.autorizacion,
                    "IdTipoDescuento": this.TipoBeneficioSeleccionado == '' ? '0' : this.TipoBeneficioSeleccionado
                })
                .then(() => {
                    this.LimpiarCampos()
                })
            },
            onChangeTipoBeneficio(){
                if(this.selectTipoBeneficio != null){
                    this.isTipo = true
                    this.tipoDescuento = 'A'
                    this.TipoBeneficioSeleccionado = this.selectTipoBeneficio.IdDescuento
                }else{
                    this.isTipo = false
                    this.tipoDescuento = 'N'
                    this.TipoBeneficioSeleccionado = ''
                }
            },
            LimpiarCampos(){
                this,this.TipoBeneficioSeleccionado = ''
                this.selectTipoBeneficio = ''
                this.codigoOrden = ''
                this.serie = ''
                this.admision = ''
                this.descuento = ''
                this.descuento = ''
                this.razon = ''
                this.autorizacion = ''
                this.tipoDescuento = 'N'
                this.beneficioCargado = ''
                this.isBeneficio = true,
                this.refNombre = '',
                this.isPorcentajeDescuento = false,
                this.isDescuento = false,
                this.isTipo = false
            }
        },
        watch: {
            codigoOrden(valor){
                // let regex = /(\d+)/g
                if(valor){
                    let arr = valor.split(' ')
                    this.serie = arr[0]
                    this.admision = arr[1]
                    this.ObtenerBeneficios()
                    this.Validaciones()
                }
                
            },
            porcentajeDescuento(valor){
                if(valor){
                    this.isDescuento = true
                    this.descuento = ''
                }else{
                    this.isDescuento = false
                }
            },
            descuento(valor){
                if(valor){
                    this.isPorcentajeDescuento = true
                    this.porcentajeDescuento = ''
                }else{
                    this.isPorcentajeDescuento = false
                }
            },
            selectTipoBeneficio(valor){
                if(valor){
                    this.beneficioCargado = valor.IdDescuento
                    if(valor.TipoDescuento == 'Porcentaje'){
                        this.porcentajeDescuento = valor.Descuento ? parseFloat(valor.Descuento).toFixed(2) : valor.Descuento
                    }else{
                        this.descuento = valor.Descuento ? parseFloat(valor.Descuento).toFixed(2) : valor.Descuento
                    }
                }else{
                    this.porcentajeDescuento = ''
                    this.descuento = ''
                    this.autorizacion = ''
                    this.beneficioCargado = ''
                }
                
            },
            autorizacion(){
                this.$refs.formValidate.validate()
            }
            
        }
    }
</script>
<style scoped>
    .label{
        padding-right: 5px;
    }
    .btnGuardar{
        padding-top: 30px;
    }
    .vsCol{
        padding-right: 5px;
        padding-top: 20px;
    }
    .vsCol2{
        padding-right: 5px;
        padding-top: 30px;
        /* width: 300px; */
    }
    .divCol{
        padding-top: 30px;
        padding-right: 20px;
    }
    .divCol2{
        padding-top: 30px;
        padding-right: 10px;
    }
    .divcol3{
        padding-top: 20px;
        padding-right: 10px;
    }
    .label-info{
        margin-left: 10px;
        color:#E74C3C;
        opacity: 0.7;
        border: 0;
        font-size: 14px;
        background-color: white;
        font-weight: bold;
        width: 400px;
    }
</style>