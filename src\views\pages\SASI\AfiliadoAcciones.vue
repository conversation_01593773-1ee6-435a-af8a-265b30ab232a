<template>
<div>
    <DxDropDownButton :showArrowIcon="false" , :dropDownOptions=" {
                width: 250,
            }" icon="overflow" display-expr="hint">
        <DxDropDownButtonItem v-for="(it, index) in actionButtons" :key="it.key" :visible="it.visible(data)">
            <template #default>
                <div @click="()=> onItemClick({itemData:it, rowData:data, indexItem: index})">
                    <font-awesome-icon :icon="it.icon" class="i-size pr-2" /> {{ it.text }}
                </div>
            </template>

        </DxDropDownButtonItem>

    </DxDropDownButton>
</div>
</template>

<script>
import { acciones } from './data'

export default {
    name: 'AfiliadoAcciones',
    components: {
        
    },
    props: {
        data: Object,
        actionButtons: {
            type: Array,
            default: () => acciones
        },
    },
    methods:{
        onItemClick(e) {
            this.$emit('action-item-click', e)
            this.$emit(e.itemData.id, e)
        },
    },
}
</script>
