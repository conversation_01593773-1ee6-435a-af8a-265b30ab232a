<template>
    <vx-card title="Movimientos de Inventario">
        <div class="content content-pagex">
            <buscador ref="BuscarFactura" buscador_titulo="Buscador / Factura" :api="'app/v1_OrdenCompra/ConsultaDocumento'" 
            :api_filtro="{Proveedor:proveedor.codigo, Documento:factura, TipoMovimiento:id_tipo_seleccionado}"
            :campos="['Documento', 'Fecha', 'Valor', 'Validacion', 'Saldo', 'Observaciones']"
            :titulos="['Documento o Validación', '#Fecha', '#Valor', '#Validacion', '#Saldo', '#Observaciones']"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" />
            <vs-divider></vs-divider>
    
            <div class="flex flex-wrap">
    
                <div class="w-min md:w-1/4 lg:w-1/4 xl:w-1/4">
                    <label>Tipo Movimiento</label>
                    <multiselect v-model="cb_busqueda_tipo_movimiento" :searchable="true" :close-on-select="true" :show-labels="true" :options="Lista_tipo_movimiento" :custom-label="TipoMovimiento_seleccionada" placeholder="Búsqueda Tipo Movimiento" name="1" @input="onChangeTipoMovimiento">
                        <span slot="noOptions">Lista no disponible.</span>
                    </multiselect>
                </div>
    
                <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7 p-2">
                    <label class="typo__label">Fecha Inicio:</label>
                    <vs-input type="date" v-model="fecha_inicio" name="date1" />
                </div>
    
                <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7 p-2">
                    <label class="typo__label">Fecha Final</label>
                    <vs-input type="date" v-model="fecha_final" name="date2" />
    
                </div>
    
                <vs-button style="float:left;margin: 20px" color="primary" type="filled" icon-pack="fas" icon="fa-search" @click="Consultar_MovimientoInventario()"> Búsqueda</vs-button>
    
                <vs-button style="float:left;margin: 20px" color="primary" type="filled" icon-pack="fas" icon="fa-plus" @click="Abrir_Ventana_Emergente_Nuevo()"> Nuevo</vs-button>
            </div>
    
            <vs-divider></vs-divider>
            <vs-table2 max-items="10" pagination :data="Lista_Movimientos" tooltip search noDataText="Sin datos disponibles">
    
                <template slot="thead">
                    <th>BodegaFuente</th>
                    <th>BodegaDestino</th>
                    <th>TipoAjuste</th>
                    <th>Fecha</th>
                    <th>Nota</th>
                    <th>Proveedor</th>
                    <th width="30px">Información</th>
                    <th width="30px">Anular</th>
                </template>
    
                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">
                        <vs-td2 width="50px" :data="data[indextr].BodegaFuente">
                            {{data[indextr].BodegaFuente}}
                        </vs-td2>
    
                        <vs-td2 width="150px" :data="data[indextr].BodegaDestino">
                            {{data[indextr].BodegaDestino}}
                        </vs-td2>
    
                        <vs-td2 width="100px" :data="data[indextr].TipoAjuste">
                            {{data[indextr].TipoAjuste}}
                        </vs-td2>
    
                        <vs-td2 width="150px" :data="data[indextr].Fecha">
                            {{data[indextr].Fecha}}
                        </vs-td2>

                        <vs-td2 width="150px" :data="data[indextr].Nota">
                            {{data[indextr].Nota}}
                        </vs-td2>

                        <vs-td2 width="150px" :data="data[indextr].Proveedor">
                            {{data[indextr].Proveedor}}
                        </vs-td2>

                        <vs-td2 width="150px" align="right">
                            <vs-button color="warning" icon-pack="fas" icon="fa-info" style="display:inline-block;margin-right:2px" @click="Consultar_Movimiento(data[indextr])"></vs-button>
                            
                        </vs-td2>
                        <vs-td2 width="150px" align="right" v-if="data[indextr].TipoMovimiento == '26' || data[indextr].TipoMovimiento == '23'">
                            <vs-button color="danger" icon-pack="fas" icon="fa-rectangle-xmark" style="display:inline-block;margin-right:2px" class="mr-1" @click="AnulaMovimiento(data[indextr])"></vs-button>
                        </vs-td2>
                    </tr>
                </template>
            </vs-table2>
        </div>
        <!--------------- Resultado busqueda Producto-------------->
        <vs-popup classContent="popup-example" title="Resultado Búsqueda" :active.sync="Estado_VentanaEmergente_Busqueda" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
    
                <form>
    
                    <vs-divider></vs-divider>
                    <vs-table2 max-items="10" pagination tooltip :data="producto" id="tb_departamentos">
                        <template slot="thead">
                            <th>Codigo</th>
                            <th>Descripcion</th>
                            <!-- <th>Marca</th>
                            <th>Concentración</th> -->
                            <th>Presentación</th>
                            <th></th>
                        </template>
    
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 :data="data[indextr].Codigo">
                                    {{data[indextr].Codigo}}
                                </vs-td2>
    
                                <vs-td2 :data="data[indextr].Descripcion">
                                    {{data[indextr].Descripcion}}
                                </vs-td2>
    
                                <!-- <vs-td2 :data="data[indextr].Concentracion">
                                    {{data[indextr].Concentracion}}
                                </vs-td2>
    
                                <vs-td2 :data="data[indextr].codigo_interno">
                                    {{data[indextr].codigo_interno}}
                                </vs-td2> -->
    
                                <vs-td2 :data="data[indextr].PresentacionNombre">
                                    {{data[indextr].PresentacionNombre}}
                                </vs-td2>
                                <vs-td2 align="right">
                                    <vs-button color="primary" icon-pack="fas" icon="fa-check-circle" style="display:inline-block;margin-right:2px" @click="Seleccionar_Producto(data[indextr])"></vs-button>
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
                </form>
    
            </div>
        </vs-popup>
        <!---------------------------NUEVO AJUSTE MOVIMIENTO---------------------------->
        <vs-popup classContent="popup-example" :title="Descripcion_Emergente" :active.sync="Estado_Emergente">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
    
                <div>
                    <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7 p-2">
                        <label class="typo__label">Fecha</label>
                        <vs-input type="date" v-model="fechaNuevo" name="date1" disabled />
                    </div>
                    <br>
                    <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
                        <vs-checkbox v-model="IsOrtopedia" @input="OnOrtopedia">Ortopedia</vs-checkbox>
                    </div>
                    <br>
                    <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
                        <label class="typo__label">Tipo:</label>
                        <multiselect :disabled="Estado_deshabilitado_botones" v-model="cb_busqueda_tipo" :searchable="true" :close-on-select="true" :show-labels="true" :options="Lista_tipo" :custom-label="Tipo_seleccionado" placeholder="Búsqueda Tipo Movimiento" name="1" @input="onChangeTipo">
                            <span slot="noOptions">Lista no disponible</span>
                        </multiselect>
                    </div>
                    <br>
                    <div v-if="id_tipo_seleccionado !==''" class="flex flex-wrap">
                        <div v-if="validar_bodega_fuente !== 'N'" class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5 mr-6">
                            <label class="typo__label">Bodega Fuente:</label>
                            <multiselect :disabled="Estado_deshabilitado_botones" v-model="cb_bodega_origen" :searchable="true" :close-on-select="true" :show-labels="true" :options="Lista_bodega_origen" :custom-label="IsOrtopedia ? BodegaConsignacionOrtopedia : Bodega_origen_Seleccionada  " placeholder="Búsqueda Bodega Origen" name="1" @input="IsOrtopedia ? onChangeBodegaConsignacionOrtopedia() : onChangeBodegaOrigen()">
                                <span slot="noOptions">Lista no disponible</span>
                            </multiselect>
                        </div>
    
                        <div v-if="validar_bodega_destino !== 'N'" class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                            <label class="typo__label">Bodega Destino:</label>
                            <multiselect :disabled="Estado_deshabilitado_botones" v-model="cb_bodega_destino" :searchable="true" :close-on-select="true" :show-labels="true" :options="Lista_bodega_destino" :custom-label="Bodega_destino_Seleccionada" placeholder="Búsqueda Bodega Destino" name="1" @input="onChangeBodegaDestino">
                                <span slot="noOptions">Lista no disponible</span>
                            </multiselect>
                        </div>
                    </div>
                    <br>
                    <div v-if="id_tipo_seleccionado == '26' || id_tipo_seleccionado == '23'">
                        <div>
                            <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5 mr-6">
                                <label class="typo__label">Proveedor:</label>
                                <multiselect v-model="cbProveedor" :options="listaProveedores" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="seleccion_proveedor" placeholder="Búsqueda" @input="onChangeProveedor">
                                    <span slot="noOptions">Lista no disponible.</span>
                                </multiselect>
                            </div>
                            <br>
                            <div class="w-full md:w-4/5 lg:w-4/5 xl:w-4/5 flex">
                                <div class="flex-wrap">
                                    <label class="typo__label">Factura</label>
                                    <vs-input v-model="factura" v-on:blur="BuscarFactura()"></vs-input>
                                    <!-- <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="BuscarFactura()" icon="icon-search"></vs-button> -->
                                </div>
                                <div  class="flex-wrap btnFactura">
                                    <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="BuscarFactura()" icon="icon-search"></vs-button>
                                </div>
                                <div class="div-custom flex-wrap">
                                    <label class="typo__label">Fecha Recibido:</label>
                                    <input class="w-full label-info2" v-model="seleccion_factura.Fecha" disabled/>
                                </div>
                                <div class="div-custom">
                                    <label class="typo__label">Validación:</label>
                                    <input class="w-full label-info2" v-model="seleccion_factura.Validacion" disabled/>
                                </div>
                                <div class="div-custom">
                                    <label class="typo__label">Valor:</label>
                                    <input class="w-full label-info2" v-model="seleccion_factura.Valor" disabled/>
                                </div>
                                <div class="div-custom">
                                    <label class="typo__label">Saldo:</label>
                                    <input class="w-full label-info2" v-model="seleccion_factura.Saldo" disabled/>
                                </div>
                            </div>
                            <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5 flex">
                                <div>
                                    <label class="typo__label" v-if="id_tipo_seleccionado == '26'">Nota de Abono</label>
                                    <label class="typo__label" v-else>Nota de Crédito</label>
                                    <vx-input-group class="">
                                        <vs-input v-model="NoNota"></vs-input>
                                    </vx-input-group>
                                </div>
                                <div class="div-custom">
                                    <vs-checkbox v-show="!IsOrtopedia" class="check-custom" v-model="isRebaja">Rebaja Existencias</vs-checkbox>
                                </div>
                            </div>
                            <br>
                            <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5 flex">
                                <div>
                                    <!-- <label class="typo__label">Nota FacE</label> -->
                                    <vs-input v-model="NoNotaFacE"></vs-input>
                                </div>
                                <div class="div-custom flex-wrap" v-if="id_tipo_seleccionado == 23">
                                    <label class="typo__label">IVA</label>
                                    <vs-switch v-model="isExento">
                                        <span slot="on">Afecto</span>
                                        <span slot="off">Exento</span>
                                    </vs-switch>
                                </div>
                            </div>
                            <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5 flex">
                                <div>
                                    <label class="typo__label">Detalle</label>
                                    <vx-input-group class="">
                                        <vs-input type="date" v-model="fechaNota"></vs-input>
                                    </vx-input-group>
                                </div>
                                <div class="div-custom flex-wrap">
                                    <label class="typo__label">Valor Nota Q.</label>
                                    <vs-input v-model="ValorNota" v-on:blur="onChangeValorNota"></vs-input>
                                </div>
                                <div class="div-custom flex-wrap">
                                    <label class="typo__label">Exento:</label>
                                    <vs-input v-model="ValorExento"></vs-input>
                                </div>
                                <div class="div-custom flex-wrap" v-if="id_tipo_seleccionado == 23">
                                    <label class="typo__label">Afecto:</label>
                                    <vs-input v-model="ValorAfecto"></vs-input>
                                </div>
                                <div class="div-custom flex-wrap" v-if="id_tipo_seleccionado == 23">
                                    <label class="typo__label">Iva:</label>
                                    <vs-input v-model="ValorIva"></vs-input>
                                </div>
                            </div>
                            <br>
                            <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
                                <div>
                                    <label class="typo__label">Motivo:</label>
                                    <multiselect v-model="cbMotivoSeleccionado"  :close-on-select="true" :show-labels="false" :options="listaMotivo" track-by="codigo" label="nombre" placeholder="Búsqueda Motivo" @input="onChangeMotivo">
                                        <span slot="noOptions">Lista no disponible</span>
                                    </multiselect>
                                </div>
                            </div>
                            <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5" v-if="id_motivo_seleccionado == 4">
                                <div>
                                    <label class="typo__label">Otro Motivos:</label>
                                    <vs-textarea rows="5" v-model="otrosMotivos" class="textEstado"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <br>
                    <div class="flex flex-wrap" v-if="isRebaja || id_tipo_seleccionado == 23">
                        <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5 mr-6">
                            <ValidationProvider name="producto" rules="required" v-slot="{ errors }" class="required">
                                <label class="typo__label">Producto</label>
                                <vx-input-group class="">
                                    <vs-input :disabled="Estado_deshabilitado_botones" id="busquedaProducto" v-model="producto_buscar" @keyup.enter="cargar_producto()" @change="cargar_producto" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                    <template slot="append">
                                        <div class="append-text btn-addon">
                                            <button :disabled="Estado_deshabilitado_botones" type="submit" v-show="false" name="button"></button>
                                            <vs-button :disabled="Estado_deshabilitado_botones" id="button-with-loading" color="primary" icon-pack="fa" @click="cargar_producto()" icon="fa-search"></vs-button>
                                        </div>
                                    </template>
                                </vx-input-group>
                            </ValidationProvider>
                        </div>
    
                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                            <ValidationProvider name="cant_solicitada" rules="required|numero_entero|numero_min:0" v-slot="{ errors }" class="required">
                                <label class="typo__label">Cantidad</label>
                                <vs-input :disabled="Estado_deshabilitado_botones" id="cantidad_sol" class="w-full" type="number" count="100" v-model="Cantidad_solicitar" @change="onChangeCantidad" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 divSpace">
                            <label class="typo__label">Costo Unitario</label>
                            <vs-input id="cantidad_sol" class="w-full" type="number" count="100" v-model="costoUnitario" disabled/>
                        </div>
                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                            <label class="typo__label">Total</label>
                            <vs-input id="cantidad_sol" class="w-full" type="number" count="100" v-model="costoProducto" @change="onChangeCostoProducto" />
                        </div>
                    </div>
                    <br>
                    <vs-row vs-type="flex" vs-w="12" v-if="isRebaja || id_tipo_seleccionado == 23">
                        <vs-col>
                <!----- Detalle producto seleccionado--->
                            <div v-if="Producto_seleccionado.Codigo>0" class="w-full md:w-4/5 lg:w-4/5 xl:w-4/5">
                                <div style="border-radius:5px;padding:5px;font-size:12px;background-color:#A5D6A7">
                                    Código: {{Producto_seleccionado.Codigo}}
                                    <br>
                                    Producto: {{Producto_seleccionado.marca_comercial}}
                                    <br>
                                    Presentación: {{Producto_seleccionado.PresentacionNombre}}
                                    <br>
            
                                </div>
                            </div>
                        </vs-col>
                    </vs-row>
                    <br>
                    <vs-row vs-type="flex" vs-w="12" v-if="isRebaja || id_tipo_seleccionado == 23">
                        <vs-col>
                            <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7">
                                <vs-button color="primary" style="float:left" type="filled" icon-pack="fas" icon="fa-plus" @click="Agregar_Detalle()"> Agregar</vs-button>
                            </div>
                        </vs-col>
                    </vs-row>
                    <br>
                    <vs-divider> </vs-divider>
                    <vs-table2 max-items="10" tooltip pagination :data="detalleProductosManual" v-if="isRebaja || id_tipo_seleccionado == 23">
                        <template slot="thead">
                            <th order="producto" width="20px">Código</th>
                            <th order="producto" width="20px">U. Medida</th>
                            <th order="descripcion" width="100px">Descripción</th>
                            <th order="cantidad" width="20px">Cantidad</th>
                            <th order="subtotal" width="20px">Total</th>
                            <th order="precio_unitario" width="20px">Costo Unitario</th>
                            <th width="20px">Eliminar</th>
                        </template>
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 noTooltip>
                                    {{ tr.producto }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ tr.PresentacionNombre }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.descripcion.toUpperCase() }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ tr.cantidad }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ $formato_moneda(tr.subtotal)}}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ $formato_moneda(tr.precio_unitario) }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="EliminarProducto(indextr, data[indextr])"></vs-button>
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
                    <div class="flex flex-wrap w-full" v-if="isRebaja || id_tipo_seleccionado == 23">
                        <label class="typo__label divAlign"> Total: {{  $formato_moneda(granTotal) }}</label>
                        <!-- <input class="divAlign" v-model="granTotal" disabled /> -->
                    </div>
                    <br>
                    <br>
                    <div class="flex flex-wrap" v-if="(isRebaja && id_tipo_seleccionado != 26 && id_tipo_seleccionado != 23)">
                        <div class="w-full">
                            <label class="typo__label"> Observaciones: </label>
                            <ValidationProvider name="Codigo" rules="required|max:0" v-slot="{ errors }" class="required">
                                <vs-textarea :disabled="Estado_deshabilitado_botones" v-model="observacion" class="mt-2" counter="250" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" required style="height:100px" />
                            </ValidationProvider>
                        </div>
                    </div>
                    <br>
                    <br>
                    <br>
                    <vs-divider></vs-divider>
                    <vs-button :disabled="Estado_deshabilitado_botones" color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" @click="Guardar_MovimientoInventario()" id="btn_guardar"> Grabar </vs-button>
                    <vs-button  color="danger" style="float:right;margin: 5px" type="border" icon-pack="feather" icon="icon-x" @click="Estado_Emergente=false"> Cancelar</vs-button>
                    <vs-divider></vs-divider>
                </div>
    
            </div>
        </vs-popup>
        <vs-popup classContent="popup-example" title="Anulación Nota" :active.sync="isConfirmacionDevolucion">
            <div>
                <label class="typo__label">Motivo:</label>
                <vs-textarea rows="5" v-model="ComentarioAnulacion" class="mt-2" counter="250"  />
            </div>
            <br>
            <div>
                <vs-button style="float:left;margin: 20px" color="success" type="filled" icon-pack="fas" icon="save" @click="ConfirmaAnulacion()">OK</vs-button>
            </div>
        </vs-popup>
    
    </vx-card>
    </template>
    
    <script>
    import moment from 'moment';
    import Multiselect from 'vue-multiselect'
    import "vue-multiselect/dist/vue-multiselect.min.css"
    
    export default {
        data() {
            return {
    
                Estado_VentanaEmergente_Busqueda: false,
                Estado_deshabilitado_botones: false,
                producto_buscar: '',
                cantidadProducto: '',
                ingresoPrecioUnitario: '',
                cantidadBonificacion: '',
                Lista_Movimientos: [],
                fecha_inicio: '',
                fecha_final: '',
                fechaNuevo: '',

                Lista_bodega_origen: [],
                cb_bodega_origen: '',
                id_bodega_Origen: 0,
    
                Lista_bodega_destino: [],
                cb_bodega_destino: '',
                id_bodega_destino: 0,
    
                Lista_departamentos: [],
                cb_busqueda_depto: '',
                id_departamento_selec: 0,
    
                Lista_departamentos_Destino: [],
                cb_busqueda_depto_Destino: '',
                id_departamento_selec_Destino: 0,
                producto: [],
                tipoBodega: '',
                Producto_seleccionado: {
                    nombre: '',
                    marca_comercial: '',
                    Principio_activo: '',
                    Concentracion: '',
                    PresentacionNombre: '',
                    Codigo: '',
                    codigo_interno: '',
                    Activo: 'N',
                    Costeo: '',
                    CostoUltimo: '',
                    CostoPromedio: ''
                },
                detalleProductosManual: [{
                    producto: '',
                    cantidad: 0,
                    precio_unitario: 0,
                    subtotal: 0,
                    descripcion: '',
                    bonificacion: 0,
                    unidad_medida: '',
                    PresentacionNombre: ''
                }],
                Cantidad_solicitar: '',
                costoUnitario: '0',
                costoProducto: '0',
                granTotal: '0',
                Deshabilitar_campos: false,
                Lista_tipo_movimiento: [],
                cb_busqueda_tipo_movimiento: '',
                id_tipo_movimiento_seleccionado: '',
    
                cb_busqueda_tipo: [],
                id_tipo__seleccionado: '',
                Lista_tipo: [],
    
                Descripcion_Emergente: '',
                Estado_Emergente: false,
    
                validar_bodega_fuente: '',
                validar_bodega_destino: '',
                observacion: '',
                Temp: '',
                id_tipo_seleccionado: '',
                permisos_tipos_bodegas: [],
                permiso_bodega_vencido:'',
                listado_reportes: [],
                listado_source: {
                    CODIGO_MOVIMIENTO: '',
                    DOCUMENTO: '',
                    IDPROVEEDORFK: '',
                    CajaChica: '',
                    Lote: '',
                    CodigoNota: ''
                },
                datosMovimiento: {
                    CODIGO_MOVIMIENTO: '',
                    CodigoNota: '',
                    TipoMovimiento: ''
                },
                permisos_tipos_movimientos: '',
                cbProveedor: '',
                listaProveedores: [],
                proveedor:{
                    codigo: '',
                    nombre: '',
                    nit: ''
                },
                factura: '',
                seleccion_factura: [{
                    Empresa: '',
                    Proveedor: '',
                    Documento: '',
                    Tipo: '',
                    Fecha: '',
                    Vencimiento: '',
                    Valor: 0,
                    Observaciones: '',
                    Saldo: 0,
                    Validacion: ''
                }],
                fechaFactura: '',
                NoNota: '',
                isRebaja: false,
                rebaja: '',
                NoNotaFacE: '',
                isExento: true,
                Exento: '',
                fechaNota: '',
                ValorNota: 0,
                ValorExento: 0,
                ValorAfecto: 0,
                ValorIva: 0,
                listaMotivo: [
                    {
                        codigo: 1,
                        nombre: 'Por producto vencido'
                    },
                    {
                        codigo: 2,
                        nombre: 'Por producto no funcional'
                    },
                    {
                        codigo: 3,
                        nombre: 'Por producto defectuoso'
                    },
                    {
                        codigo: 4,
                        nombre: 'Otros'
                    }
                ],
                cbMotivoSeleccionado: '',
                id_motivo_seleccionado: 0,
                otrosMotivos: '',
                isConfirmacionDevolucion: false,
                MovimientoSeleccionado: [],
                ComentarioAnulacion: '',
                IsOrtopedia: false
            }
        },
        components: {
            Multiselect
        },
        mounted() {
            for(let privilegio of this.$store.state.privilegios){
                if(privilegio.Privilegio.includes("TIPO_MOVIMIENTO")){
                    if(this.permisos_tipos_movimientos != ''){
                        this.permisos_tipos_movimientos += `,${privilegio.Privilegio.split('_')[2]}`
                    }else{
                        this.permisos_tipos_movimientos = privilegio.Privilegio.split('_')[2]
                    }
                }
            }
            for(let privilegio of this.$store.state.privilegios){
                if(privilegio.Privilegio.includes("TIPO_BODEGA")){                      
                    let tipobodega = privilegio.Privilegio.split('_')[2];
                    if(tipobodega == 'T'){
                        this.permiso_bodega_vencido = 'S'
                    }else{
                        this.permisos_tipos_bodegas.push(privilegio.Privilegio.split('_')[2])                    
                    }                                                       
                }
            }
            this.Consultar_TipoMovimiento();
            this.ObtenerFecha();
            //Seleccionar valor por defcto.
            this.cb_busqueda_tipo_movimiento = {
                Nombre: 'Todos'
            }
            this.id_tipo_movimiento_seleccionado = '0';
            setTimeout(() => {
                this.Consultar_MovimientoInventario();
            }, 1000);
    
        },
        async beforeCreate(){
            this.listado_reportes = await this.$recupera_parametros_reporte('Movimiento Nuevo')
            this.listado_reportes_nota = await this.$recupera_parametros_reporte('Movimiento Nota')
        },
        methods: {
            isEmptyObject(obj){
                return Object.keys(obj).length === 0;
            },
            Emergente_Detalle(datos) {
    
                this.Descripcion_Emergente = "Información Movimiento Inventario"
                this.Estado_Emergente = true;
    
                this.cb_busqueda_tipo = {
                    Nombre: datos.TipoAjuste
                }
    
                this.id_tipo_seleccionado = datos.TipoMovimiento;
                //Mostrar datos de bodega fuente
                this.validar_bodega_fuente = datos.AplicaBodegaFuente;
    
                this.cb_busqueda_depto = {
                    CODIGO: datos.CodigoDepartamentoOrigen,
                    NOMBRE: datos.DepartamentoOrigen
                }
                this.cb_bodega_origen = {
                    CODIGO: datos.CodigoBodegaFuente,
                    NOMBRE: datos.BodegaFuente
                }
    
                //Mostrar datos de bodega Destino
                this.validar_bodega_destino = datos.AplicaBodegaDestino;
                this.cb_busqueda_depto_Destino = {
                    CODIGO: datos.CodigoDepartamentoDestino,
                    NOMBRE: datos.DepartamentoDestino
                }
                this.cb_bodega_destino = {
                    CODIGO: datos.CodigoBodegaDestino,
                    NOMBRE: datos.BodegaDestino
                }
    
                //Información Productos
                this.Producto_seleccionado.Codigo = datos.CodigoProd;
                this.Producto_seleccionado.marca_comercial = datos.Producto;
                this.Producto_seleccionado.PresentacionNombre = datos.PresentacionNombre;
    
                this.Cantidad_solicitar = datos.Cantidad;
                this.Estado_deshabilitado_botones = true;
                this.costoProducto = '0'
                this.costoUnitario = '0'
    
                ///
                this.observacion = datos.Comentario;
            },
            ObtenerFecha() {
    
                this.fecha_inicio = moment(new Date(Date.now())).format('YYYY-MM-DD')
                this.fecha_final = moment(new Date(Date.now())).format('YYYY-MM-DD')
                this.fechaNuevo = moment(new Date(Date.now())).format('YYYY-MM-DD')
                this.fechaNota = moment(new Date(Date.now())).format('YYYY-MM-DD')
    
            },
    
            Mostrar_resultado_producto(value) {
                if (value.length === 1) {
                    value.map(obj => {
                        this.Producto_seleccionado.Codigo = obj.Codigo;
                        this.Producto_seleccionado.marca_comercial = obj.Descripcion;
                        this.Producto_seleccionado.Concentracion = obj.Concentracion;
                        this.Producto_seleccionado.codigo_interno = obj.codigo_interno;
                        this.Producto_seleccionado.PresentacionNombre = obj.PresentacionNombre;
                        this.Producto_seleccionado.Costeo = obj.Costeo
                        this.Producto_seleccionado.CostoPromedio = obj.CostoPromedio
                        this.Producto_seleccionado.CostoUltimo = obj.CostoUltimo
                        if(obj.Costeo == 'P'){
                            this.costoUnitario = parseFloat(obj.CostoPromedio).toFixed(2)
                        }else{
                            this.costoUnitario = parseFloat(obj.CostoUltimo).toFixed(2)
                        }
                    })
    
                } else if (value.length > 1) {
                    this.Estado_VentanaEmergente_Busqueda = true;
                }
    
            },
    
            Seleccionar_Producto(value) {
                this.producto_buscar = value.Codigo;
                this.Producto_seleccionado.Codigo = value.Codigo;
                this.Producto_seleccionado.marca_comercial = value.Descripcion;
                this.Producto_seleccionado.Principio_activo = value.Principio_activo;
                this.Producto_seleccionado.Concentracion = value.Concentracion;
                this.Producto_seleccionado.codigo_interno = value.codigo_interno;
                this.Producto_seleccionado.PresentacionNombre = value.PresentacionNombre;
                this.Producto_seleccionado.Costeo = value.Costeo
                this.Producto_seleccionado.CostoPromedio = value.CostoPromedio
                this.Producto_seleccionado.CostoUltimo = value.CostoUltimo
                if(value.Costeo == 'P'){
                    this.costoUnitario = parseFloat(value.CostoPromedio).toFixed(2)
                }else{
                    this.costoUnitario = parseFloat(value.CostoUltimo).toFixed(2)
                }
                
    
                this.Estado_VentanaEmergente_Busqueda = false;
                this.producto = [];
            },
            onChangeCantidad(){
                const url = this.$store.state.global.url
                if(this.Cantidad_solicitar != ''){
                    this.axios.post(url + 'app/v1_OrdenCompra/ConsultaExistencia', {
                        CodigoProducto: this.Producto_seleccionado.Codigo,
                        CodigoBodega: this.id_bodega_Origen
                    })
                    .then(resp => {
                        if(resp.data.codigo == 0){
                            let cantidadExistencias = resp.data.resultado
                            if((parseFloat(this.Cantidad_solicitar) > parseFloat(cantidadExistencias)) && this.isRebaja == true){
                                this.$vs.notify({
                                    color: 'danger',
                                    title: 'Inventario',
                                    text: 'No existencias suficientes para devolver!! ... Revise la cantidad..!!!!',
                                })
                                this.Cantidad_solicitar = ''
                                this.costoProducto = ''
                                if(this.Producto_seleccionado.Costeo == 'P'){
                                    this.costoUnitario = parseFloat(this.Producto_seleccionado.CostoPromedio).toFixed(2)
                                }else{
                                    this.costoUnitario = parseFloat(this.Producto_seleccionado.CostoUltimo).toFixed(2)
                                }
                                return;
                            }else{
                                if(this.Producto_seleccionado.Costeo == 'P'){
                                    this.costoUnitario = parseFloat(this.Producto_seleccionado.CostoPromedio).toFixed(2)
                                }else{
                                    this.costoUnitario = parseFloat(this.Producto_seleccionado.CostoUltimo).toFixed(2)
                                }
                            }
                        }
                    })

                    if(this.id_tipo_seleccionado == '10'){
                        this.costoUnitario = '0.01'
                    }
                    this.costoProducto = parseFloat(this.Cantidad_solicitar * this.costoUnitario).toFixed(2)
                }
            },
            onChangeCostoProducto(){
                //this.costoProducto = parseFloat(this.Cantidad_solicitar * this.costoUnitario).toFixed(2)
                if(this.id_tipo_seleccionado == '23'){
                    this.costoUnitario = 0
                    // this.costoUnitario = parseFloat((this.costoProducto/this.Cantidad_solicitar)).toFixed(2)
                    this.costoUnitario = parseFloat((this.costoProducto/this.Cantidad_solicitar) / 1.12).toFixed(2)
                }else if(this.id_tipo_seleccionado == '26'){
                    this.costoUnitario = 0
                    this.costoUnitario = parseFloat((this.costoProducto/this.Cantidad_solicitar)).toFixed(2)
                }
            },
            Limpiar_CamposDetP() {
                this.Producto_seleccionado.Codigo = '';
                this.Producto_seleccionado.marca_comercial = '';
                this.Producto_seleccionado.Principio_activo = '';
                this.Producto_seleccionado.Concentracion = '';
                this.Producto_seleccionado.codigo_interno = '';
                this.Producto_seleccionado.PresentacionNombre = '';
                this.Cantidad_solicitar = '';
                this.costoProducto = '';
                this.costoUnitario = ''

            },
            Agregar_Detalle() {
                /**
                 * @General
                 * Función Permite  actualizar y  almacenar un nuevo registro detalle;
                 */
                if (this.Producto_seleccionado.Codigo === 0 || this.Producto_seleccionado.Codigo === '' || this.Producto_seleccionado.Codigo === null || this.Producto_seleccionado.Codigo === undefined) {
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Ordenes de Compra',
                        text: 'Seleccionar producto.',
                    })
                    return;
                }

                if (this.Cantidad_solicitar <= 0) {
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Ordenes de Compra',
                        text: 'Cantidad Inválida',
                    })
                } else {
                    
                    this.detalleProductosManual.push({
                        producto: this.Producto_seleccionado.Codigo,
                        cantidad: this.Cantidad_solicitar,
                        precio_unitario: this.costoUnitario,
                        unidad_medida: this.Producto_seleccionado.unidadMedida,
                        subtotal: parseFloat(this.costoProducto).toFixed(2),
                        descripcion: this.Producto_seleccionado.marca_comercial,
                        bonificacion: this.cantidadBonificacion,
                        PresentacionNombre: this.Producto_seleccionado.PresentacionNombre

                    })
                    this.producto_buscar = ''
                    var total = 0;
                    this.detalleProductosManual.forEach(element => {
                        total += (Number(element.subtotal));
                        
                    });
                    this.granTotal = parseFloat(total).toFixed(2);
                    this.cantDetalle = this.cantDetalle + 1
                    this.Limpiar_CamposDetP()

                }

            },
            EliminarProducto(index, datos) {
                /**
                 * @General
                 * Función eliminar registro;
                 */
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',
                    text: '¿Eliminar detalle? ',
                    acceptText: 'Aceptar',
                    cancelText: 'Cancelar',
                    accept: () => {
                        this.granTotal = parseFloat(this.granTotal - datos.subtotal).toFixed(2)
                        this.$delete(this.detalleProductosManual, index)
                    }
                })
            },
            Limpiar_Campos_MovInventario() {
                this.cb_busqueda_tipo = '';
                this.id_tipo_seleccionado = '';
                this.cb_busqueda_depto = '';
                this.id_departamento_selec = 0;
                this.cb_busqueda_depto_Destino = '';
                this.id_departamento_selec_Destino = 0;
                this.cb_bodega_origen = '';
                this.id_bodega_Origen = 0;
                this.cb_bodega_destino = '';
                this.id_bodega_destino = 0;
                this.producto_buscar = '';
                this.Cantidad_solicitar = '';
                this.observacion = '';
                this.granTotal = 0
            },
            Limpiar_Campos_TipoMovimiento() {
                this.cb_busqueda_depto = '';
                this.id_departamento_selec = 0;
                this.cb_busqueda_depto_Destino = '';
                this.id_departamento_selec_Destino = 0;
                this.cb_bodega_origen = '';
                this.id_bodega_Origen = 0;
                this.cb_bodega_destino = '';
                this.id_bodega_destino = 0;
                this.producto_buscar = '';
                this.Cantidad_solicitar = '';
                this.observacion = '';
                this.granTotal = 0
            },
    
            Limpiar_Campos() {
                this.Producto_seleccionado.Codigo = '';
                this.Producto_seleccionado.Descripcion = '';
                this.Producto_seleccionado.PresentacionNombre = '';
                
            },
    
            Guardar_MovimientoInventario() {
                const url = this.$store.state.global.url
                if(this.id_tipo_seleccionado == '23' || this.id_tipo_seleccionado == '26'){
                    if(this.isRebaja){
                        this.rebaja = 'S'
                    }else{
                        this.rebaja = 'N'
                    }
                    if(this.isExento){
                        this.Exento = 'N'
                    }else{
                        this.Exento = 'S'
                    }

                    if(this.ValidacionNota()){
                        this.axios.post(url + 'app/v1_OrdenCompra/IngresoNota', {
                            tipo_movimiento: this.id_tipo_seleccionado,
                            bodega_origen: this.id_bodega_Origen,
                            Comentario: this.otrosMotivos != '' ? this.otrosMotivos : this.cbMotivoSeleccionado.nombre,
                            Documento: this.factura,
                            NumeroFactura: this.factura,
                            Total: this.granTotal != 0 ? this.granTotal : this.ValorNota,
                            CodigoBodega: this.id_bodega_Origen,
                            Rebaja: this.rebaja,
                            Exento: this.id_tipo_seleccionado == '23' ? this.Exento : 'S',
                            NoNota: this.NoNota,
                            NoFaceNota: this.NoNotaFacE,
                            Proveedor: this.proveedor.codigo,
                            FechaNota: this.fechaNota,
                            ValorExento: this.ValorExento,
                            ValorIva: this.ValorIva,
                            ValorAfecto: this.ValorAfecto,
                            ValorNota: this.ValorNota,
                            NotaCreditoAbono: this.id_tipo_seleccionado == '23' ? 'N' : 'A',
                            datos: JSON.stringify(this.detalleProductosManual)
                        })
                        .then(resp => {
                            if(resp.data.codigo != 0){
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Movimiento Inventario',
                                    text: resp.data.mensaje,
                                })
                            }else{
                                this.$vs.notify({
                                    color: '#0B7A0B',
                                    title: 'Movimiento Inventario',
                                    text: resp.data.descripcion,
                                })
                                this.datosMovimiento.CodigoNota = resp.data.resultado
                                this.datosMovimiento.TipoMovimiento = this.id_tipo_seleccionado
                                this.Consultar_Movimiento(this.datosMovimiento)
        
                                this.Estado_Emergente = false;
                                this.cb_busqueda_tipo = [];
        
                                this.id_tipo_seleccionado = '';
                                //Mostrar datos de bodega fuente
                                this.validar_bodega_fuente = '';
                                this.cb_busqueda_depto = [];
                                this.cb_bodega_origen = [];
        
                                //Mostrar datos de bodega Destino
                                this.validar_bodega_destino = '';
                                this.cb_busqueda_depto_Destino = [];
                                this.cb_bodega_destino = [];
        
                                //Información Productos
                                this.Producto_seleccionado.Codigo = '';
                                this.Producto_seleccionado.marca_comercial = '';
                                this.Producto_seleccionado.Presentacion = '';
                                this.granTotal = 0
        
                                this.Cantidad_solicitar = '';
                                this.observacion = '';
                                this.Consultar_MovimientoInventario();
                                this.limpiarFactura()
                                this.limpiarNota()
                                this.limpiarProveedor()
                            }
                        })
                    }
                    
                }else{
                    this.axios.post(url + 'app/v1_OrdenCompra/guarda_MovimientoInventario', {
                        tipo_movimiento: this.id_tipo_seleccionado,
                        depto_origen: this.id_departamento_selec,
                        bodega_origen: this.id_bodega_Origen,
                        depto_destino: this.id_departamento_selec_Destino,
                        bodega_destino: this.id_bodega_destino,
                        producto: 0,
                        cantidad: 0,
                        observaciones: this.observacion,
                        datos: JSON.stringify(this.detalleProductosManual)
    
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Movimiento Inventario',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_Movimientos = "";                        
                        } else {
                            this.$vs.notify({
                                color: '#0B7A0B',
                                title: 'Movimiento Inventario',
                                text: resp.data.descripcion,
                            })
                            this.datosMovimiento.CODIGO_MOVIMIENTO = resp.data.resultado
                            this.Consultar_Movimiento(this.datosMovimiento)
    
                            this.Estado_Emergente = false;
                            this.cb_busqueda_tipo = [];
    
                            this.id_tipo_seleccionado = '';
                            //Mostrar datos de bodega fuente
                            this.validar_bodega_fuente = '';
                            this.cb_busqueda_depto = [];
                            this.cb_bodega_origen = [];
    
                            //Mostrar datos de bodega Destino
                            this.validar_bodega_destino = '';
                            this.cb_busqueda_depto_Destino = [];
                            this.cb_bodega_destino = [];
    
                            //Información Productos
                            this.Producto_seleccionado.Codigo = '';
                            this.Producto_seleccionado.marca_comercial = '';
                            this.Producto_seleccionado.Presentacion = '';
                            this.granTotal = 0
    
                            this.Cantidad_solicitar = '';
                            this.observacion = '';
                            this.Consultar_MovimientoInventario();
                        }
                    })
                }
            },
    
            Consultar_MovimientoInventario() {
                if(this.id_tipo_movimiento_seleccionado == '0' || this.id_tipo_movimiento_seleccionado == ''){
                    this.id_tipo_movimiento_seleccionado = ''
                    for(let i = 0; i < this.Lista_tipo_movimiento.length; i++){
                        if(i == 0){
                            this.id_tipo_movimiento_seleccionado = this.Lista_tipo_movimiento[i].Codigo
                        }else{
                            this.id_tipo_movimiento_seleccionado = this.id_tipo_movimiento_seleccionado + ',' + this.Lista_tipo_movimiento[i].Codigo
                        }
                    }
                }
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/consulta_MovimientoInventario', {
                        tipo: this.id_tipo_movimiento_seleccionado,
                        fecha_inicio: this.fecha_inicio,
                        fecha_fin: this.fecha_final
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Movimiento Inventario',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_Movimientos = "";
                        } else {
                            //Decodificación
    
                            this.Lista_Movimientos = resp.data.json;
    
                        }
                    })
            },
    
            Consultar_TipoMovimiento() {
    
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/Consultar_TipoMovimiento', {
                    tipo: this.permisos_tipos_movimientos
                })
                    .then(resp => {
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.cb_busqueda_tipo_movimiento = null; //Limpiar los valores seleccionados multiselec
                            this.Lista_tipo_movimiento = [];
    
                        } else {
                            this.Lista_tipo_movimiento = resp.data.json;

                        }
                    })
            },
    
            TipoMovimiento_seleccionada({
                Nombre
            }) {
                return `${Nombre}`
            },
    
            onChangeTipoMovimiento(value) {
                //Si existe seleccionada un elemento
    
                if (value !== null && value.length !== 0) {

                    this.id_tipo_movimiento_seleccionado = value.Codigo;
    
                } else {
                    this.id_tipo_movimiento_seleccionado = '0'
    
                }
    
            },
    
            Consultar_Tipo() {
    
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/Consultar_TipoMovimiento', {
                    tipo: this.permisos_tipos_movimientos
                })
                    .then(resp => {
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.cb_busqueda_tipo = null; //Limpiar los valores seleccionados multiselec
                            this.Lista_tipo = [];
    
                        } else {
                            this.Lista_tipo = resp.data.json;
                        }
                    })
            },
    
            Tipo_seleccionado({
                Nombre
            }) {
                return `${Nombre}`
            },
    
            onChangeTipo(value) {
                //Si existe seleccionada un elemento
                this.Limpiar_Campos_TipoMovimiento();
                this.Limpiar_Campos();
                this.limpiarProveedor()
                this.limpiarFactura()
                this.limpiarNota()
    
                if (value !== null && value.length !== 0) {
    
                    this.id_tipo_seleccionado = value.Codigo;
    
                    this.validar_bodega_fuente = value.BodegaFuente;
    
                    this.validar_bodega_destino = value.BodegaDestino;
                    // for(let privilegio of this.$store.state.privilegios){
                    //     if(privilegio.Privilegio.includes("TIPO_BODEGA")){                      
                    //         let tipobodega = privilegio.Privilegio.split('_')[2];
                    //         if(tipobodega == 'T'){
                    //             this.permiso_bodega_vencido = 'S'
                    //         }else{
                    //             this.permisos_tipos_bodegas.push(privilegio.Privilegio.split('_')[2])                    
                    //         }                                                       
                    //     }
                    // }
                    if(value.Codigo == 26 || value.Codigo == 23){
                        this.Consultar_proveedor()
                        this.isRebaja = false
                    }else{
                        this.isRebaja = true
                    }
                    if(this.IsOrtopedia){
                        this.ConsultaBodegaConsignacionOrdenCompraOrtopedia()
                    }else{
                        this.Consultar_Bodega('F',this.permisos_tipos_bodegas.join(","),this.permiso_bodega_vencido);
                    }
                } else {
                    this.id_tipo_seleccionado = 'xx'
    
                }
    
            },
            Consultar_Bodega(Operacion, BodegasDespacho, BodegaTransito) {
                const url = this.$store.state.global.url
                this.axios.post(url+'app/v1_OrdenCompra/consulta_bodegaEmpReal', {
                    operacion: Operacion,
                    BodegasDepacho: BodegasDespacho,
                    BodegaTransito: BodegaTransito
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Bodegas',
                            text: resp.data.mensaje,
                        })
                        this.Lista_bodega_origen = [];
                        this.id_bodega_Origen = '';
                        this.cb_bodega_origen = '';                         
                    } else {
                        this.Lista_bodega_origen = resp.data.json;                           
                        this.Lista_bodega_destino = resp.data.json;    
                    }                     
                })
                .catch(() => { })
        
            },
            Bodega_origen_Seleccionada({
                CODIGO,
                NOMBRE
            }) {
                return `${CODIGO} - ${NOMBRE} `
            },
    
            onChangeBodegaOrigen() {
                if (this.cb_bodega_origen !== null && this.cb_bodega_origen.length !== 0) {
                    this.id_bodega_Origen = this.cb_bodega_origen.CODIGO;
                    this.tipoBodega = this.cb_bodega_origen.TipoBodega
    
                } else {
                    this.id_bodega_Origen = '';
                    this.tipoBodega = ''
                }
    
            },
            Bodega_destino_Seleccionada({
                CODIGO,
                NOMBRE
            }) {
                return `${CODIGO} - ${NOMBRE} `
            },
    
            onChangeBodegaDestino(value) {
    
                if (value !== null && value.length !== 0) {
                    this.id_bodega_destino = value.CODIGO;
                    if(this.tipoBodega == ''){
                        this.tipoBodega = value.TipoBodega
                    }  
    
                } else {
                    this.id_bodega_destino = '';
                    this.tipoBodega = ''
                }
    
            },
            ConsultaBodegaConsignacionOrdenCompraOrtopedia(){
                // const url = this.$store.state.global.url
                this.axios.post('/app/v1_OrdenCompra/ConsultaBodegaConsignacionOrtopedia', {
                    CodigoAgrupacion: '11'
                })
                .then(resp => {

                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Bodegas',
                            text: resp.data.mensaje,
                        })
                    } else {
                        this.Lista_bodega_origen = resp.data.json;                                              
                    }
                })
                .catch(() => { })
            },
            BodegaConsignacionOrtopedia({
                CodigoBodega,
                NombreBodega
            }){
                return `${CodigoBodega} - ${NombreBodega} `;
            },
            onChangeBodegaConsignacionOrtopedia(){
                if (this.cb_bodega_origen !== null && this.cb_bodega_origen.length !== 0) {
                    this.id_bodega_Origen = this.cb_bodega_origen.CodigoBodega;
                    this.tipoBodega = this.cb_bodega_origen.TipoBodega
    
                } else {
                    this.id_bodega_Origen = ''
                    this.tipoBodega = ''
                }
            },
            OnOrtopedia(){
                this.LimpiarOrtopedia()
                if(this.IsOrtopedia){
                    this.ConsultaBodegaConsignacionOrdenCompraOrtopedia()
                    this.isRebaja = false
                }else{
                    this.Consultar_Bodega('F',this.permisos_tipos_bodegas.join(","),this.permiso_bodega_vencido)
                }
                
            },
            cargar_producto: function () {
    
                this.Limpiar_Campos();
                this.axios.post('/app/v1_OrdenCompra/ConsultaRequisicion_Producto', {
                        Producto: this.producto_buscar.trim(),
                        TipoBodega: this.tipoBodega
                    })
                    .then(resp => {
                        if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                            this.producto = []
                            resp.data.json.map(data => {
                                this.producto.push({
                                    ...data
                                })
                            })
    
                            this.Mostrar_resultado_producto(this.producto);
    
                        } else {
                            this.producto = []
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Producto',
                                text: (resp.data.codigo < 0) ? resp.data.descripcion : 'Sin Registros',
                            })
    
                        }
    
                    })
    
            },
            currentDateTime() {
    
                const current = new Date();
                const date = current.getFullYear() + '-' + (current.getMonth() + 1) + '-' + current.getDate();
                const dateTime = date;
    
                return dateTime;
            },
    
            Abrir_Ventana_Emergente_Nuevo() {
                this.detalleProductosManual = []
                this.Estado_deshabilitado_botones = false;
                this.Estado_Emergente = true;
                this.Descripcion_Emergente = "Nuevo Movimiento Inventario"
                this.Limpiar_Campos_MovInventario();
                this.Limpiar_Campos();
                this.limpiarProveedor();
                this.limpiarFactura()
                this.limpiarNota()
                this.Consultar_Tipo()
                this.granTotal = 0
                this.IsOrtopedia = false
            },
            async Consultar_Movimiento(datos) {
                if(datos.TipoMovimiento == 23 || datos.TipoMovimiento == 26){
                    this.listado_source.CodigoNota = datos.CodigoNota
                    this.$genera_reporte({
                        Nombre: "Movimiento Nota",
                        Data_source: this.listado_source,
                        Data_report: this.listado_reportes_nota
                    })
                }else{
                    this.listado_source.CODIGO_MOVIMIENTO = datos.CODIGO_MOVIMIENTO
                    this.$genera_reporte({
                        Nombre: "Movimiento Nuevo",
                        Data_source: this.listado_source,
                        Data_report: this.listado_reportes
                    })
                }
               
            },
            Consultar_proveedor() {
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/ConsultarProveedor', {
                        
                    })
                    .then(resp => {

                        if (resp.data.codigo != 0) {

                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Proveedores',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.listaProveedores = "";
                        } else {
                            //Decodificación
                            this.listaProveedores = resp.data.json;

                        }
                    })
                    .catch(() => {})
            },
            seleccion_proveedor({
                NIT,
                NOMBRE,
            }) {
                return `${NIT} - ${NOMBRE} `
            },
            onChangeProveedor(value) {
                this.limpiarFactura()
                this.limpiarNota()
                if (value !== null && value.length !== 0) {

                    this.proveedor.codigo = value.CODIGO,
                    this.proveedor.nombre = value.NOMBRE.toUpperCase();
                    this.proveedor.nit = value.NIT;

                } else {
                    this.proveedor.codigo = '';
                    this.proveedor.nombre = '';
                    this.proveedor.nit = '';
                }
            },
            limpiarProveedor(){
                this.cbProveedor = ''
                this.proveedor = []
            },
            limpiarFactura(){
                this.factura = ''
                this.seleccion_factura = []
                this.fechaFactura = ''
            },
            limpiarNota(){
                this.NoNota = ''
                this.isRebaja = false
                this.NoNotaFacE = ''
                this.fechaNota = moment(new Date(Date.now())).format('YYYY-MM-DD')
                this.ValorNota = 0
                this.ValorExento = 0
                this.ValorAfecto = 0
                this.ValorIva = 0
                this.isExento = true
                this.cbMotivoSeleccionado = ''
                this.otrosMotivos = ''
                this.id_motivo_seleccionado = 0
            },
            LimpiarOrtopedia(){
                this.cb_bodega_origen = ''
                this.id_bodega_Origen = ''
                this.tipoBodega = ''
                this.detalleProductosManual = []
            },
            BuscarFactura(){
                if(this.factura != ''){
                    this.axios.post('/app/v1_OrdenCompra/ConsultaDocumento', {
                        Proveedor: this.proveedor.codigo,
                        Documento: this.factura,
                        TipoMovimiento: this.id_tipo_seleccionado
                    })
                    .then(resp => {
                        if(!this.isEmptyObject(resp.data.json)){
                            if(resp.data.json.length === 1){
                                this.factura = resp.data.json[0].Documento
                                this.seleccion_factura.Empresa = resp.data.json[0].Empresa
                                this.seleccion_factura.Proveedor = resp.data.json[0].Proveedor
                                this.seleccion_factura.Documento = resp.data.json[0].Documento
                                this.seleccion_factura.Tipo = resp.data.json[0].Tipo
                                this.seleccion_factura.Fecha = resp.data.json[0].Fecha
                                this.seleccion_factura.Vencimiento = resp.data.json[0].Vencimiento
                                this.seleccion_factura.Valor = resp.data.json[0].Valor == '' ? 0 : parseFloat(resp.data.json[0].Valor).toFixed(2)
                                this.seleccion_factura.Observaciones = resp.data.json[0].Observaciones
                                this.seleccion_factura.Saldo = resp.data.json[0].Saldo == '' ? 0 : parseFloat(resp.data.json[0].Saldo).toFixed(2)
                                this.seleccion_factura.Validacion = resp.data.json[0].Validacion
                            }else{
                                this.$refs.BuscarFactura.iniciar((data) => {
                                    if(data != null){
                                        this.factura = data.Documento
                                        this.seleccion_factura.Empresa = data.Empresa
                                        this.seleccion_factura.Proveedor = data.Proveedor
                                        this.seleccion_factura.Documento = data.Documento
                                        this.seleccion_factura.Tipo = data.Tipo
                                        this.seleccion_factura.Fecha = data.Fecha
                                        this.seleccion_factura.Vencimiento = data.Vencimiento
                                        this.seleccion_factura.Valor = data.Valor == '' ? 0 : parseFloat(data.Valor).toFixed(2)
                                        this.seleccion_factura.Observaciones = data.Observaciones
                                        this.seleccion_factura.Saldo = data.Saldo == '' ? 0 : parseFloat(data.Saldo).toFixed(2)
                                        this.seleccion_factura.Validacion = data.Validacion
                                    }
                                })
                            }
                        }
                        
                    })
                }else{
                    this.$refs.BuscarFactura.iniciar((data) => {
                        if(data != null){
                            this.factura = data.Documento
                            this.seleccion_factura.Empresa = data.Empresa
                            this.seleccion_factura.Proveedor = data.Proveedor
                            this.seleccion_factura.Documento = data.Documento
                            this.seleccion_factura.Tipo = data.Tipo
                            this.seleccion_factura.Fecha = data.Fecha
                            this.seleccion_factura.Vencimiento = data.Vencimiento
                            this.seleccion_factura.Valor = data.Valor == '' ? 0 : parseFloat(data.Valor).toFixed(2)
                            this.seleccion_factura.Observaciones = data.Observaciones
                            this.seleccion_factura.Saldo = data.Saldo == '' ? 0 : parseFloat(data.Saldo).toFixed(2)
                            this.seleccion_factura.Validacion = data.Validacion
                        }
                    })
                }
            },
            onChangeMotivo(){
                if(this.cbMotivoSeleccionado != null){
                    this.id_motivo_seleccionado = this.cbMotivoSeleccionado.codigo
                }else{
                    this.id_motivo_seleccionado = 0
                }
            },
            onChangeValorNota(){
                if(parseFloat(this.ValorNota) > parseFloat(this.seleccion_factura.Saldo)){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'Saldo insuficiente para cubrir la nota. Verifique'
                    })
                    this.ValorNota = 0
                    this.ValorAfecto = 0
                    this.ValorExento = 0
                    this.ValorIva = 0
                }
            },
            ValidacionNota(){
                if(parseFloat(this.seleccion_factura.Valor) < parseFloat(this.ValorNota)){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'El valor de la nota es mayor al saldo',
                    });
                    return false
                }else if(this.id_motivo_seleccionado == 4 && this.otrosMotivos == ''){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'Debe de ingreso el motivo de otros',
                    });
                    return false
                }else if(this.isEmptyObject(this.proveedor)){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'Debe de ingresar a un proveedor',
                    });
                    return false
                }else if(this.NoNota == ''){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'Debe de ingresar la nota de crédito',
                    });
                    return false
                }else if(this.id_bodega_Origen == '' || this.id_bodega_Origen == 0){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'Debe de seleccionar una bodega origen',
                    });
                    return false
                }else if(this.id_motivo_seleccionado == 0){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'Debe de seleccionar un motivo',
                    });
                    return false
                }else{
                    return true
                }
            },
            AnulaMovimiento(value){
                this.isConfirmacionDevolucion = true
                this.MovimientoSeleccionado = value 
            },
            ConfirmaAnulacion(){
                if(this.ComentarioAnulacion != ''){
                    this.isConfirmacionDevolucion = false
                    this.axios.post('/app/v1_OrdenCompra/DevolucionNota', {
                        PeriodoNota: this.MovimientoSeleccionado.Periodo,
                        CorrelativoMovimiento: this.MovimientoSeleccionado.CODIGO_MOVIMIENTO,
                        TipoMovimiento: this.MovimientoSeleccionado.TipoMovimiento,
                        Comentario: this.ComentarioAnulacion,
                        CorrelativoNota: this.MovimientoSeleccionado.CodigoNota,
                        Proveedor: this.MovimientoSeleccionado.CodigoProveedor,
                        NoNota: this.MovimientoSeleccionado.DocumentoNota,
                        CodigoBodega: this.MovimientoSeleccionado.CodigoBodegaFuente,
                        NoFactura: this.MovimientoSeleccionado.Factura,
                        ValorNota: this.MovimientoSeleccionado.TotalNota,
                        Rebaja: this.MovimientoSeleccionado.RebajaExistencias
                    })
                    .then(resp => {
                        if(resp.data.codigo != 0){
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Nota',
                                text: resp.data.mensaje,
                                time: 6000
                            });
                            this.ComentarioAnulacion = ''
                        }else{
                            this.ComentarioAnulacion = ''
                            this.Consultar_MovimientoInventario()
                        }
                    })
                }else{
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Nota',
                        text: 'Debe de ingresar un motivo para la anulación',
                    });
                }
                
            }
        },
        watch: {
            ValorNota(value){
                if(value != ''){
                    if(this.isExento){
                        this.ValorAfecto = (value / 1.12).toFixed(2)
                        this.ValorIva = (value - this.ValorAfecto).toFixed(2)
                        this.ValorExento = 0
                    }else{
                        this.ValorAfecto = 0
                        this.ValorExento = value
                        this.ValorIva = 0
                    }
                    if(this.id_tipo_seleccionado == '26'){
                        this.ValorExento = value
                    }
                }
            },
            granTotal(value){
                this.ValorNota = value
            },
            isExento(value){

                if(value){
                    this.ValorAfecto = (this.ValorNota / 1.12).toFixed(2)
                    this.ValorIva = (this.ValorNota - this.ValorAfecto).toFixed(2)
                    this.ValorExento = 0
                    this.Exento = 'N'
                }else{
                    this.ValorAfecto = 0
                    this.ValorExento = this.ValorNota
                    this.ValorIva = 0
                    this.Exento = 'S'
                }
            },
            isRebaja(value){
                if(value){
                    this.rebaja = 'S'
                }else{
                    this.rebaja = 'N'
                }
            }
        }
    }
    </script>
    <style scoped>
        .divSpace{
            margin-left: 5px;
        }
        .divAlign{
            margin-left: 10px;
            color:#E74C3C;
            opacity: 0.7;
            border: 0;
            font-size: 24px;
            background-color: white;
            font-weight: bold;

        }
        .label-info2{
            padding-top: 10px;
            color:#E74C3C;
            opacity: 0.7;
            border: 0;
            font-size: 16px;
            background-color: white;
        }
        .div-custom{
            padding-left: 15px;
        }
        .check-custom{
            padding-top: 25px;
        }
        .textEstado{
            color: #fc0909;
            font-weight: bold;
            font-size: 30px;
        }
        .btnFactura{
            padding-top: 20px;
            padding-right: 10px;
        }
    </style>