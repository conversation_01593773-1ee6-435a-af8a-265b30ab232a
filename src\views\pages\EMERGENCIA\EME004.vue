<!-- Procesos admisiones
Altas, Reactivación, Egreso contraindicado y omision de egreso -->
<template>
<div>
    <vx-card :title="`Actualización de Admisiones - ${sesion.sesion_sucursal_nombre}`">
        <div>
            <div class="sm:w-full md:w-full lg:w-full xl:w-1/2 m-2"
             style="padding:9px;border:1px solid #ccc;border-radius:5px">
                <label class="vs-input--label">Proceso a ejecutar</label><br />
                <vs-radio :disabled="!permiso.altas" class="pr-6" v-model="info.tipoupdate" vs-value="1">Alta de admisión</vs-radio>
                <vs-radio :disabled="!permiso.reactivar" class="pr-6" v-model="info.tipoupdate" vs-value="2">Reactivar admisión</vs-radio>
                <vs-radio :disabled="!permiso.omision" class="pr-6" v-model="info.tipoupdate" vs-value="3">Omisión de Egreso</vs-radio>
                <vs-radio :disabled="!permiso.cambiar" v-model="info.tipoupdate" vs-value="6">Cambiar Médico a la admisión</vs-radio>
                <vs-radio :disabled="!permiso.quitaraut" v-model="info.tipoupdate" vs-value="7">Quitar autorización de médico</vs-radio>
            </div>
        <BusquedaAdmision ref="componenteAdmisiones" @datos_admision="DatosAdmision"
            :api_filtro="{'Opcion':'CONSULTA','SubOpcion':'EMERGENCIA','activo'
            :['2','7'].includes(info.tipoupdate)?0:1,'tieneHabitacion':0}"
            @limpiar_datos_admision="LimpiarDatosAdmision"
            :api_campos="['Serie','Codigo','Nombres','Apellidos','Habitacion']" 
            :api_titulos="['Serie#','Admision#','Nombres','Apellidos','Habitación']">
        </BusquedaAdmision>

            <div class="flex flex-wrap">
                <div class="sm:w-full md:w-full lg:w-2/12 xl:w-1/12 m-1">
                    <SM-Buscar  v-if="info.tipoupdate =='6'" 
                                v-model="info.medico" label="Médico"
                                api="app/Ajenos/Busqueda_Ajenos"
                                :api_campos="['Codigo', 'Nombre', 'Apellido', 'Celular', 'Tipo', 'Especialidad']"
                                :api_titulos="['Codigo', 'Nombre', 'Apellido', 'Celular', 'Tipo', 'Especialidad']"
                                api_campo_respuesta="Codigo"
                                :callback_buscar="DatosMedico"
                                :callback_nuevo="null" :callback_editar="null" :callback_cancelar="null"
                                :disabled="this.info.tipoupdate !='6'"
                                :disabled_editar="false" />
                </div>
                <div class="w-full md:w-full lg:w-8/12 xl:w-5/12 m-1">
                    <ValidationProvider rules="required" v-slot="{ errors }">
                        <vs-input v-if="info.tipoupdate =='6'" 
                                    label="Nombre del Médico:" disabled 
                                    class="w-full" 
                                    :value="info.nombreMedico"  
                                    :danger="errors.length > 0"
                                    :danger-text="(errors.length > 0) ? errors[0] : null" />                                
                    </ValidationProvider>
                </div>
            </div>

            <vs-textarea class="sm:w-full md:w-full lg:w-full xl:w-1/2 m-1" 
            :label="desc[0]" counter="200" v-model="info.observaciones" />
            <br>
           <div class="flex flex-wrap">
                <vs-button color="danger" class="sm:w-full md:w-full lg:w-6/12 xl:w-1/12 p-5 m-2" @click="Limpiadatos">Limpiar datos</vs-button>  
                <div class="xl:w-4/12 lg:w-full md:w-full sm:w-full"></div>              
                <vs-button color="success" class="sm:w-full  md:w-full lg:w-6/12 xl:w-1/12 p-5 m-2"
                    @click="Procesar" 
                    :disabled="!this.info.admision || !info.tipoupdate">Procesar
                </vs-button>
            </div>
        </div>
    </vx-card>

</div>
</template>

<script>
import BusquedaAdmision from "/src/components/sermesa/modules/admisiones/BusquedaAdmision.vue"

export default {
    components: {
        BusquedaAdmision
    },

    data() {
        return {
            tipoAdmision: 0,

            desc: [
                "Motivo",
                "Descripción"
            ],

            info: {
                admision: null,
                observaciones: null,
                paciente: null,
                medico: null,
                nombreMedico: null,
                tipoupdate: null
            },
            permiso:{
                altas       : false,
                omision     : false,
                cambiar     : false,
                reactivar   : false,
                quitaraut   : false
            }

        }

    },
    methods: {
        DatosAdmision(datos) {
            this.info.serie = datos.Serie
            this.info.admision = datos.Codigo
            this.info.paciente = datos.Paciente
        },
        LimpiarDatosAdmision(){
            this.medico = null,
            this.info.serie = null
            this.nombreMedico = null,
            this.info.admision = null
            this.info.tipoupdate = null
            this.info.observaciones = null
        },
        Limpiadatos() {
            this.$refs.componenteAdmisiones.limpiar_campos()
            this.info.serie = null
            this.info.admision = null
            this.info.tipoupdate = null
            this.info.observaciones = null
        },
        DatosMedico(datos){
                this.info.medico =  datos.Codigo
                this.info.nombreMedico=datos.Nombre?.trim()+' '+datos.Apellido?.trim()
        },

        Procesar() {
            if (!this.info.admision) {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Alerta',
                    text: 'Debe de ingresar la admisión',
                })
                return false
            }

            if (!this.info.medico && this.info.tipoupdate == 6){
                    this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Alerta',
                    text: 'Debe de ingresar el médico',
                })
                return false
            }

            if (!this.info.observaciones) {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Alerta',
                    text: 'Debe de ingresar motivo/descripción',
                })
                return false
            }

            if (!this.info.medico && this.info.tipoupdate == 6) {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Alerta',
                    text: 'Ingrese código de médico',
                })
                return false
            }

            this.axios.post(
                '/app/emergencia/ActualizaAdmision', {
                    serie: this.info.serie,
                    codigoAdmision: this.info.admision,
                    observaciones: this.info.observaciones,
                    tipoActualizacion: this.info.tipoupdate,
                    medicoAjeno: this.info.medico
                }
            ).then(
                (respuesta) => {
                    const Respuesta = respuesta.data ? respuesta.data.json[0].descripcion : respuesta.json.descripcion
                    const CodigoError = respuesta.data ? respuesta.data.json[0].tipo_error : respuesta.json.tipo_error
                    if (CodigoError == 1) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: Respuesta,
                            time: 4000,
                            position: 'bottom-center'
                        })
                        return
                    }

                    if (CodigoError == -1) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: Respuesta,
                            time: 4000,
                            position: 'bottom-center'
                        })
                        return
                    }
                    
                    this.Limpiadatos()
                    this.$vs.notify({
                        color: 'success',
                        title: 'Actualización de admisiones',
                        text: Respuesta
                    })
                }
            ).catch( )
        }
    },

    computed: {
        sesion() {
            return this.$store.state.sesion
        }
    },
    async mounted() {
        this.permiso.altas   = this.$validar_privilegio('PERMISO_ALTAS').status
        this.permiso.cambiar = this.$validar_privilegio('PERMISO_MEDICO').status
        this.permiso.omision = this.$validar_privilegio('PERMISO_OMISION').status
        this.permiso.reactivar = this.$validar_privilegio('PERMISO_REACTIVAR').status
        this.permiso.quitaraut = this.$validar_privilegio('PERMISO_QUITARAUT').status
    }

}
</script>
