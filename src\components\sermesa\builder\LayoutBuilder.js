class LayoutBuilder {
    constructor() {
      this.containers = [];
    }
  
    addContainer(containerClass) {
      const newContainer = {
        class: containerClass,
        rows: [],
      };
      this.containers.push(newContainer);
      return this;
    }
  
    addRowToLastContainer() {
      const container = this.containers[this.containers.length - 1];
      if (container) {
        const newRow = { columns: [] };
        container.rows.push(newRow);
      }
      return this;
    }
  
    addComponentToLastRow(component, size, validations = null) {
      const lastContainer = this.containers[this.containers.length - 1];
      if (lastContainer) {
        const lastRow = lastContainer.rows[lastContainer.rows.length - 1];
        if (lastRow) {
          lastRow.columns.push({ component, size, validations });
        }
      }
      return this;
    }
  
    build() {
      return this.containers;
    }
  }
  
  export default LayoutBuilder;