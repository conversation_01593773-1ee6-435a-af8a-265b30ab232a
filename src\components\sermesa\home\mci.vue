<template>
<vx-card class="p-2"  title="Metas Crucialmente Importantes" collapse-action data-action="collapse">
    <div class="vx-row text-left">
        <div class="vx-col w-full ">
            <vs-table :data="mcis">

                <template slot="thead">
                    <vs-th width="50px">Estado</vs-th>
                    <vs-th>MCI</vs-th>
                    <vs-th width="150px">Porcentaje %</vs-th>
                </template>

                <template slot-scope="{data}">
                    <vs-tr :key="indextr" v-for="(item, indextr) in data">

                        <vs-td width="50px">
                            <i class="fa fa-circle" :style="{'color':item.estado}"></i>
                        </vs-td>

                        <vs-td>
                            {{item.mci}}

                            <vs-table :data="item.tareas">

                                <!-- <template slot="thead">
                                    <vs-th width="50px">Estado</vs-th>
                                    <vs-th>MCI</vs-th>
                                    <vs-th width="150px">Porcentaje %</vs-th>
                                </template> -->

                                <template slot-scope="{data}">
                                    <vs-tr :key="indextr" v-for="(item, indextr) in data">

                                        <vs-td>
                                            {{item.tarea}}
                                        </vs-td>
                                        <vs-td width="50px">
                                            <vs-switch v-model="item.valor" />
                                        </vs-td>
                                    </vs-tr>
                                </template>
                            </vs-table>

                        </vs-td>

                        <vs-td style="text-align:center">
                            {{mci_porcentaje(item)}}% 
                            <div style="background-color:#bdc3c7;">
                                <div style="position:relative;height:6px;" :style="{'width':mci_porcentaje(item)+'%','background-color':item.estado}">

                                </div>
                            </div>
                        </vs-td>

                    </vs-tr>
                </template>
            </vs-table>
        </div>
    </div>
    <!-- Support Tracker Meta Data -->
</vx-card>
</template>

<script>
export default {
    data() {
        return {
            mcis: [{
                    estado: '#27ae60',
                    mci: 'Entrega de proyectos a tiempo',
                    tareas: [{
                        tarea: 'Tarea 1',
                        valor: false
                    }, {
                        tarea: 'Tarea 2',
                        valor: false
                    }]
                },
                {
                    estado: '#d35400',
                    mci: 'Entrega de proyectos a tiempo',
                    tareas: [{
                        tarea: 'Tarea 1',
                        valor: false
                    }, {
                        tarea: 'Tarea 2',
                        valor: false
                    }]
                }
            ],
        }
    },
    computed: {

    },
    components: {},
    methods: {
        mci_porcentaje(item) {
            let total = item.tareas.length
            let avance = item.tareas.filter(data=>data.valor).length
            return avance * 100 / total
        }
    },
    mounted() {

    }
}
</script>
