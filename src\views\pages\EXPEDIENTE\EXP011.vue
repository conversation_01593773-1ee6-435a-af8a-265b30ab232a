<template>
<DxDataGrid v-bind="DefaultDxGridConfiguration" :ref="dataGridRefKey" :data-source="Dietas" key-expr="id" @edit-canceled="()=> { VistaPrevia=false }" :on-row-inserting="onRowInserting" :on-row-inserted="onRowInserted" @cell-prepared="onCellPrepared">

    <DxToolbar>
        <DxItem name="groupPanel" />
        <DxItem name="searchPanel" />
        <DxItem location="after" template="opcionesTemplate" />
    </DxToolbar>

    <DxGroupPanel :visible="true" />
    <DxGrouping :auto-expand-all="false" />

    <DxEditing :allow-updating="false" :allow-adding="true" :allow-deleting="false" mode="popup" :changes.sync="FormChanges">
        <DxPopup v-bind="popupConf" :title="VistaPrevia?'Visualización de Dietas':'Registro de Dietas'" :toolbarItems="VistaPrevia?[]:PopUpToolBarItems" :width="650" :height="550" />
        <DxForm label-location="top" label-mode="static">
            <DxItem data-field="IdTiempoComida" :col-span="VistaPrevia?2:1" :is-required="true" />
            <DxItem data-field="TipoDieta" :visible="!VistaPrevia" />
            <DxItem data-field="Dieta" :col-span="2" :validationRules="[{message:'Debe ingresar nota de la dieta', type:'required'}]" />
        </DxForm>
    </DxEditing>

    <DxColumn :width="45" type="buttons" :buttons="[
                {
                    icon:'fas fa-eye',
                    hint:'Ver',
                    onClick: (obj)=>{
                        this.VistaPrevia = true
                        this.dataGrid.editRow(obj.row.rowIndex)
                        this.TxtDescripcionDieta = obj.row.data.Dieta
                    }
                }
            ]" :allow-reordering="false" :allow-resizing="false" />

    <!-- <DxColumn data-field="Estado" :width="100" alignment="center"> -->
    <DxColumn :width="120" :allow-resizing="false" :allow-reordering="false" :allow-grouping="false" data-field="Estado" caption="Estado" cell-template="cambiarEstado" alignment="center" />

    <DxColumn data-field="Fecha" format="dd/MM/yyyy HH:mm:ss" data-type="date" :width="150" alignment="center" />
    <DxColumn caption="Descripción de la dieta" data-field="Dieta" :width="400" :minWidth="50" :allow-editing="!VistaPrevia" edit-cell-template="dietaTextArea" :customizeText="(e) => this.$saltos_tab_to_html(e.value)" :encode-html="false" alignment="center" />

    <DxColumn data-field="IdTiempoComida" caption="Tiempo de comida" :width="130" :allow-editing="!VistaPrevia" alignment="center">
        <DxLookup :data-source="TiemposComida" value-expr="IdTiempo" display-expr="Descripcion" />
        <DxRequiredRule />
    </DxColumn>
    <DxColumn data-field="Usuario" :width="150" alignment="center" />
    <DxColumn data-field="Puesto" :width="150" alignment="center" />
    <DxColumn data-field="TipoDieta" :visible="false" caption="Dieta">
        <DxLookup :data-source="CatalogoDietas" value-expr="IdDieta" display-expr="Dieta" />
    </DxColumn>

    <template #cambiarEstado="{data: info}">
        <div style="display: flex; flex-wrap: wrap; justify-content: left;">
            <vs-tooltip text="Cambiar estado" position="bottom">
                <font-awesome-icon v-if="info.data.Estado === 'Activa'" :icon="['fas', 'toggle-off']" style="font-size: 20px; color: #337ab7; cursor: pointer;" @click="cambiarEstado(info)" />
            </vs-tooltip>
            <span class="pl-2">{{info.data.Estado}}</span>
        </div>
    </template>
    <template #opcionesTemplate>
        <ExpedienteGridToolBar v-bind="$props" :visible="true" :pdfExportItems="[]" @add="() => {
                    TxtDescripcionDieta=''
                    dataGrid.addRow()
                    }" @refresh="Cargar" :showItems="['add', 'refresh']">
            <DxButton id="botonBarra" width="36" height="36" :hint="!verDietasCompleto ? 'Ver todas las dietas' : 'Ver dietas activas'" type="activas" @click="dietasCompletas">
                <font-awesome-icon :icon="['fas', verDietasCompleto?'clipboard-list':'clipboard-check']" style="font-size: 18px;" />
            </DxButton>
        </ExpedienteGridToolBar>
    </template>
    <template #dietaTextArea="{ data: cellInfo }">
        <div>
            <DxTextArea :value="TxtDescripcionDieta" :on-value-changed="(e)=> {
                        cellInfo.setValue(e.value, e.value)
                        TxtDescripcionDieta=e.value
                        }" :read-only="readOnlyDescripDieta" :height="300" @key-up="(e)=> {
                        if(e.event.key ==='Escape')
                            dataGrid.cancelEditData()
                        }">
            </DxTextArea>
        </div>
    </template>
</DxDataGrid>
</template>

<script>
import EXPBase from './EXPBase.vue'
import {
    DxDataGrid,
    DxToolbar,
    DxColumn,
    DxEditing,
    DxForm,
    DxPopup,
    DxLookup,
    DxRequiredRule,
    DxGrouping,
    DxGroupPanel
} from 'devextreme-vue/data-grid'
import {
    DxItem
} from 'devextreme-vue/form'
import {
    DxTextArea
} from 'devextreme-vue/text-area'
import {
    DefaultDxGridConfiguration
} from './data'
import DxButton from 'devextreme-vue/button'

const dataGridRefKey = 'gridDietas'
export default {
    name: 'Dietas',
    extends: EXPBase,
    components: {
        DxDataGrid,
        DxToolbar,
        DxItem,
        DxEditing,
        DxColumn,
        DxPopup,
        DxForm,
        DxTextArea,
        DxLookup,
        DxRequiredRule,
        DxButton,
        DxGrouping,
        DxGroupPanel,
        ExpedienteGridToolBar: () => import("./ExpedienteGridToolBar.vue"),
    },
    data() {
        return {

            dataGridRefKey,
            DefaultDxGridConfiguration,
            TxtDescripcionDieta: '',
            FormChanges: [],
            Dietas: [],
            TiemposComida: [],
            CatalogoDietas: [],
            VistaPrevia: false,
            PopUpToolBarItems: [{
                    widget: "dxButton",
                    toolbar: "bottom",
                    options: {
                        text: 'Guardar',
                        type: 'success',
                        useSubmitBehavior: true,
                        onClick: () => {
                            this.dataGrid.saveEditData()
                        }
                    },
                    location: "after",
                },
                {
                    widget: "dxButton",
                    toolbar: "bottom",
                    options: {
                        text: 'Cancelar',
                        type: 'danger',
                        useSubmitBehavior: true,
                        onClick: () => {
                            this.dataGrid.cancelEditData()
                        }
                    },
                    location: "after",
                },
            ],
            verDietasCompleto: false,
            PermisoEditarFormato: false,
        }
    },
    methods: {
        onRowInserting(e) {
            e.cancel = new Promise((resolve, reject) => {
                this.axios.post('/app/v1_ExpedienteEvolucion/InsertarDieta', {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    Dieta: this.$reemplazar_tabulares(this.TxtDescripcionDieta),
                    IdTiempo: this.FormChanges[0].data.IdTiempoComida,
                }).then((resp) => {
                    resp.data.codigo == 0 ? resolve(false) : resolve(true)
                }).catch((err) => {
                    reject(err.descripcion ? err.descripcion : err)
                })
            })
        },
        onRowInserted() {
            this.Cargar()
        },
        Cargar() {
            if (this.ExpedienteCargado)
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaDietas', {
                    Serie: this.SerieAdmision,
                    Admision: this.CodigoAdmision
                })
                .then(resp => {
                    this.Dietas = resp.data.json.map((x) => {
                        return {
                            id: x.id_dieta,
                            Dieta: this.$limpiar_saltos_tabulares(x.dieta),
                            Fecha: x.fecharegistro,
                            TiempoComida: x.descripcion, //tiempo de comida
                            Estado: x.estado,
                            Usuario: x.Empleado,
                            Puesto: x.Puesto,
                            IdTiempoComida: x.idtiempo
                        }
                    })
                    this.refreshDataGrid()
                })
            else {
                this.Dietas = []
            }
        },
        addRow() {
            this.dataGrid.addRow()
        },
        Grabar() {
            //el atributo dieta viene de el que seleccionen del catálogo o del texto libre si escojen otros
            if (this.ExpedienteCargado && this.TxtDescripcionDieta != '' && Boolean(this.FormChanges[0].data.IdTiempoComida)) {
                this.axios.post('/app/v1_ExpedienteEvolucion/InsertarDieta', {

                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,

                    Dieta: this.$reemplazar_tabulares(this.TxtDescripcionDieta),
                    IdTiempo: this.FormChanges[0].data.IdTiempoComida,
                }).then(() => {
                    this.dataGrid.cancelEditData() //para cerrar el modal
                    this.Cargar()
                })
            }

        },
        refreshDataGrid() {
            this.dataGrid.refresh()
            this.dataGrid.filter([
                ["Estado", "=", "Activa"]
            ]);
            this.dataGrid.columnOption('Fecha', 'sortOrder', 'desc');
            this.dataGrid.clearGrouping()
            this.verDietasCompleto = false
        },
        CargarTiempoComida() {
            this.axios.post('/app/v1_ExpedienteEvolucion/CatalogoTiemposComida', {})
                .then(resp => {
                    this.TiemposComida = resp.data.json
                })
        },
        CargarCatalogoDietas() {
            this.axios.post('/app/v1_ExpedienteEvolucion/CatalogoDietas', {})
                .then(resp => {
                    this.CatalogoDietas = resp.data.json
                })
        },
        onCellPrepared(e) {
            if (e.rowType === 'data' && e.data.Estado === 'Omitido') {
                e.cellElement.style.cssText = "background-color: #c0dcc0; text-align: center;";
            }
            if (e.rowType === 'data' && e.column.dataField === 'Estado') {

                if (e.data.Estado === 'Omitido') {
                    e.cellElement.style.cssText = "background-color: lime; text-align: center;";
                }
            }
        },
        cambiarEstado(e) {
            let postData = {
                SerieAdmision: this.SerieAdmision,
                Admision: this.CodigoAdmision,
                IdDieta: e.data.id,
            }
            this.axios.post("/app/v1_ExpedienteEvolucion/UpdateDieta", {
                ...postData
            }).then(() => {
                this.Cargar()
            })
        },
        dietasCompletas() {
            if (this.verDietasCompleto) {
                this.dataGrid.filter([
                    ["Estado", "=", "Activa"]
                ]);
            } else {

                this.dataGrid.clearFilter();
            }

            this.verDietasCompleto = !this.verDietasCompleto
        }
    },
    computed: {
        dataGrid: function () {
            return this.$refs[dataGridRefKey].instance
        },
        readOnlyDescripDieta: function () {
            return this.VistaPrevia || !(Boolean(this.FormChanges[0]) && this.FormChanges[0].data.IdTiempoComida === "5" || this.PermisoEditarFormato)
        },
        //propiedad calculada para los watch de los cambios en los valores del formulario ya que los cambios se almacenan en un array
        tipoDietaSelected: function () {
            if (this.FormChanges.length > 0 && this.FormChanges[0].data.TipoDieta)
                return this.FormChanges[0].data.TipoDieta
            return null
        },
        tiempoComidaSelected: function () {
            if (this.FormChanges.length > 0 && this.FormChanges[0].data.IdTiempoComida)
                return this.FormChanges[0].data.IdTiempoComida
            return null
        }
    },
    watch: {
        'tipoDietaSelected': function (newval) {
            let find = this.CatalogoDietas.find((x) => x.IdDieta === newval)
            if (newval !== undefined && find != undefined)
                this.TxtDescripcionDieta = find.Dieta
        },
        /// Solo el tiempo de comida otros permite texto libre a cualquier otra seleccion  hay que colocar en blanco si el texto no esta en el catalogo de tipo de dieta
        'tiempoComidaSelected': function (newval, oldval) {
            let find = this.CatalogoDietas.find(x => x.Dieta === this.TxtDescripcionDieta)
            if (newval !== '5' && oldval === '5' && newval !== undefined && find === undefined)
                this.TxtDescripcionDieta = ''
        }
    },
    mounted() {
        this.CargarCatalogoDietas()
        this.CargarTiempoComida()

        this.dataGrid.filter([
            ["Estado", "=", "Activa"]
        ]);

        this.$validar_funcionalidad('/EXPEDIENTE/EXP011', 'EDITAR', (d) => {
            this.PermisoEditarFormato = d.status
        })
    },
}
</script>

<style>
.dx-button.dx-button-activas {
    background-color: #FFF;
}

.dx-button-mode-contained.dx-button-activas .dx-icon {
    color: #672685;
}

.dx-button-mode-contained.dx-button-activas {
    color: #672685;
    border-color: #672685;
}
</style>
