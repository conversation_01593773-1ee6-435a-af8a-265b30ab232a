<template>
    <vx-card title="Despacho Devoluciones Requerimiento Departamentos">
    
        <div class="content content-pagex">
            <vs-divider></vs-divider>
            <br>
            <div class="flex flex-wrap">    
                <div class="sm:w-full md:w-7/12 lg:w-6/12 xl:w-5/12 pr-4">               
                    <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                        <label class="typo__label">Bodega Destino:</label>      
                        <strong>{{ cod_bodegaDestino.CODIGO }}</strong>
                        <multiselect
                            v-model="cod_bodegaDestino"
                            :options="Lista_bodegas_destino"
                            :searchable="true"
                            :close-on-select="true"
                            :show-labels="false"
                            :allow-empty="false"
                            :custom-label="Bodegas_seleccionado"
                            placeholder="Seleccionar bodega"
                            @input="onChangeBodega"
                            :danger="errors.length > 0"
                            :danger-text="errors.length > 0 ? errors[0] : null">
                            <span
                            slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                    </ValidationProvider>                                                
                </div>
                <div class="sm:w-full md:w-4/12 lg:w-3/12 xl:w-2/12 pr-4">
                    <label class="typo__label">Estado</label>
                    <multiselect v-model="cb_lista_operacion" :options="lista_estado" :allow-empty="false" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Operacion_seleccionada" placeholder="Seleccionar" @input="onChangeEstado">
                        <span slot="noOptions">Lista no disponible.</span>
                    </multiselect>
                </div>
            </div>
            <div class="flex flex-wrap">                     

                <div class="sm:w-full md:w-7/12 lg:w-6/12 xl:w-5/12 pr-4">                    
                    <label class="typo__label">Departamento Destino:</label>      
                    <multiselect
                        v-model="cod_departamentoDestino_filtro"
                        track-by="CODIGO"
                        :options="Lista_departamento_destino_filtro"
                        :searchable="true"
                        :close-on-select="true"
                        :show-labels="true"
                        :allow-empty="true"
                        :custom-label="Bodegas_seleccionado"
                        placeholder="Seleccionar departamento"
                        selected-label="Seleccionado"                            
                        deselect-label="Quitar selección"
                        select-label="Seleccionar"
                        @input="Consultar_OrdenEnc()">
                        <span slot="noOptions">Lista no disponible.</span>
                    </multiselect>
                </div>
    
                <div class="pr-4">
                    <label class="typo__label">Fecha Inicio:</label>
                    <vs-input type="date" v-model="fecha_inicio" name="date1" />
                </div>
    
                <div class="pr-4">
                    <label class="typo__label">Fecha Final:</label>
                    <vs-input type="date" v-model="fecha_final" name="date1" />
                </div>
    
                <vs-button style="float:left;margin: 18px 0px" color="primary" type="filled" icon-pack="fas" icon="fa-search" @click="Consultar_OrdenEnc()"> Búsqueda</vs-button>
    
            </div>
            <vs-divider></vs-divider>
    
            <vs-table2 max-items="10" pagination :data="Lista_vista" search id="tb_lista_solicitud">
                
                <template slot="thead">
                    <th width="130px">Nº. Movimiento</th>
                    <th>Departamento Fuente</th>
                    <th>Fecha(s)</th>
                    <th>Observaciones</th>
                    <th></th>
                    <!--<th style="text-align: center;">Recepcionada</th>-->
    
                </template>
    
                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">
    
                        <vs-td2 width='5%'>
                            {{tr.IDCODIGOMOVIMIENTO}}
                        </vs-td2>
    
                        <vs-td2 width='20%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                {{tr.IDDEPARTAMENTO+' - '+tr.NOMBREDEPARTAMENTO}}
                            </div>
                        </vs-td2>
    
                        <vs-td2 width='20%'>
                            <div style="word-wrap: break-word;white-space: wrap;">                                
                                <label v-if="tr.FECHASOLICITUD">Solicitado Dev: {{tr.FECHASOLICITUD}}</label>   
                                <br v-if="tr.FECHAENTREGADA">                             
                                <label v-if="tr.FECHAENTREGADA">Aceptado Dev: {{tr.FECHAENTREGADA}}</label>
                            </div>
                        </vs-td2>
                        
                        <vs-td2  width='30%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                {{ tr.OBSERVACIONES }}
                            </div>
                        </vs-td2>  

                        <vs-td2 v-if="Id_estado_seleccionado == 'P'" width='10%' align="right" style="white-space:nowrap;min-width: 200px;">
                            <vx-tooltip text="Consultar" style="display:inline-block;margin-right:10px">
                                <vs-button color="primary" icon-pack="fas" icon="fa-arrow-right" style="display:inline-block;margin-right:2px" @click="Emergente_Detalle(data[indextr])"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip v-if="tr.RequiereAutorizacion == 'S' && !tr.CorporativoAutorizacion" text="Autorizar" style="display:inline-block;margin-right:10px">
                                <vs-button color="danger" icon-pack="fas" icon="fa-lock" style="display:inline-block;margin-right:2px" @click="AutorizarRequerimiento(data[indextr].IDCODIGOMOVIMIENTO)" :disabled="!AUTORIZAR_REQUERIMIENTO"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip text="Excel" style="display:inline-block;margin-right:10px">
                                <vs-button icon-pack="far" icon="fa-file-excel" @click="Consultar_Movimiento(data[indextr],'EXCEL')" color="success" style="display:inline-block;margin-right:2px"></vs-button>
                            </vx-tooltip>                                                       
                            <vx-tooltip text="Imprimir" style="display:inline-block;margin-right:10px">
                                <vs-button color="primary" icon-pack="feather" icon="icon-printer" style="display:inline-block;margin-right:2px" @click="Consultar_Movimiento(data[indextr])"></vs-button>
                            </vx-tooltip>
                        </vs-td2>
                        <vs-td2 v-else width='10%' align="right" style="white-space:nowrap;min-width: 200px;">
                            <vx-tooltip text="Consultar" style="display:inline-block;margin-right:10px">
                                <vs-button color="#B2BABB" icon-pack="fas" icon="fa-arrow-right" style="display:inline-block;margin-right:2px" @click="Emergente_Consulta(data[indextr])"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip text="Excel" style="display:inline-block;margin-right:10px">
                                <vs-button icon-pack="far" icon="fa-file-excel" @click="Consultar_Movimiento(data[indextr],'EXCEL')" color="success" style="display:inline-block;margin-right:2px"></vs-button>
                            </vx-tooltip>                                                 
                            <vx-tooltip text="Imprimir" style="display:inline-block;margin-right:10px">
                                <vs-button color="primary" icon-pack="feather" icon="icon-printer" style="display:inline-block;margin-right:2px" @click="Consultar_Movimiento(data[indextr])"></vs-button>
                            </vx-tooltip>
                        </vs-td2>
    
    
                    </tr>
                </template>
            </vs-table2>
    
            <!----------------------- DESPACHO REQUERIMIENTO ---------->
            <vs-popup classContent="popup-example" :title="Descripcion_Emergente" :active.sync="Estado_Emergente">
                <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px"> 
                    <div style="border:2px solid #1B5E20;border-radius:5px;padding:5px;font-size:12px;background-color: #C8E6C9">
                        Sucursal Origen: <b style="font-size:1vw">{{sesion.sesion_sucursal_nombre}}</b>
                    </div>
                    <br>
                    <!--- Ubicación Destino -->
                    <vs-row class="w-full">
                        <vs-col class="w-1/2 md:w-1/2 lg:w-4/12 xl:w-4/12">
                            <b> <small>Movimiento No. </small></b>                        
                            <b style="font-size:2vw">{{Movimiento_seleccionado.IDCODIGOMOVIMIENTO}}</b>                        
                        </vs-col>                        
                        <vs-col class="w-1/2 md:w-1/2 lg:w-8/12 xl:w-8/12" v-if="RequiereAutorizacion">
                            <b style="color:red; font-size:2vw;"> Pendiente de autorización </b>                     
                        </vs-col>
                    </vs-row>
                    <div class="w-1/2 md:w-1/2 lg:w-6/12 xl:w-6/12">
                         <b> <small>Fecha Solictud: </small></b>
                        <b>{{Movimiento_seleccionado.FECHASOLICITUD}}</b>
                    </div>
                    <br>
                    <div>                                        
                        <label class="typo__label">Departamento Fuente:</label>
                        <strong>{{ cb_bodegas.CODIGO }}</strong>
                        <multiselect v-model="cb_bodegas" :options="Lista_departamentos_destino" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Bodegas_seleccionado" placeholder="Seleccionar bodega" :disabled="Deshabilitar_campos">
                            <span slot="noOptions">Lista no disponible.</span>
                        </multiselect>
                    </div>
                    <br>  
                    <div>
                        <label class="typo__label">Bodega Destino:</label>      
                        <strong>{{ cod_bodegaDestino.CODIGO }}</strong>
                        <multiselect v-model="cod_bodegaDestino" :options="Lista_bodegas_destino" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Bodegas_seleccionado"  placeholder="Seleccionar bodega" :disabled="Deshabilitar_campos">
                            <span slot="noOptions">Lista no disponible.</span>
                        </multiselect> 
                    </div>                    
                    <br>
                    <!--- Producto -->
                    <vs-divider>Detalle</vs-divider>
    
                    <!--- Mostrar detalle de la orden--->
                    <vs-table2 max-items="10" pagination :data="Lista_detalle" id="tb_lista_solicitud">
    
                        <template slot="thead">
                            <th>Linea</th>
                            <th>Codigo</th>
                            <th>Producto</th>
                            <th>Unidad Medida</th>
                            <th>Cant. Solicitada</th>
                            <th>Cant. Devolución</th>
                        </template>
    
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 width='5%'>
                                    {{ tr.LINEA }}
                                </vs-td2>
                                <vs-td2 width='5%'>                                    
                                    {{tr.IDPRODUCTOFK}}                                                    
                                </vs-td2>
                                <vs-td2 width='80%'>
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                         
                                        {{tr.DESCRIPCION_PRODUCTO}}                
                                    </div>
                                </vs-td2>
                                <vs-td2 width='10%'>
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                                          
                                        {{tr.DESCRIPCION_UNIDAD}}
                                    </div>
                                </vs-td2>
                                <vs-td2 width='5%'>
                                    {{tr.CANTIDAD_PEDIDA}}
                                </vs-td2>
                                <vs-td2 width='5%' >
                                    <vs-input  type="number"  :disabled="Consulta" v-model="tr.CANTIDAD_ENTREGA" class="w-full"  @input="VerificarExistencias(tr)" />                                   
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
                    <vs-divider></vs-divider>
                    <div class="flex flex-wrap">
                        <div class="w-full md:w-full lg:w-full xl:w-full">
                            <vs-textarea label="Observaciones" counter="100" :disabled="Consulta"  type="text" class="w-full" v-model="Observaciones" />
                        </div>    
                    </div>
                    <vs-divider></vs-divider>
                    <vs-button v-if="!Consulta" color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" id="btn_confirmacion" @click="Aceptar_Devolucion()" :disabled="RequiereAutorizacion"> Aceptar Devolución</vs-button>
                    <vs-button v-if="!Consulta && RequiereAutorizacion" color="danger" style="float:right;margin: 5px" type="filled" icon-pack="fas" icon="fa-lock"  @click="AutorizarRequerimiento(Movimiento_seleccionado.IDCODIGOMOVIMIENTO)" :disabled="!AUTORIZAR_REQUERIMIENTO" > Autorizar </vs-button>
                    <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-printer" id="btn_movimiento" @click="Consultar_Movimiento(Movimiento_seleccionado)"> Imprimir </vs-button>                
                    <vs-button icon-pack="far" style="float:right;margin: 5px" icon="fa-file-excel" @click="Consultar_Movimiento(Movimiento_seleccionado,'EXCEL')" color="success">Excel</vs-button>
                    <vs-divider></vs-divider>
                </div>
            </vs-popup>
    
        </div>
    </vx-card>
    </template>
    
    <script>
    import Multiselect from 'vue-multiselect'
    import moment from "moment"
    import "vue-multiselect/dist/vue-multiselect.min.css"
    export default {
        components: {
            Multiselect
        },
        data() {
            return {
                Estado_VentanaEmergente_Busqueda: false,
                cb_lista_operacion: '',
                Descripcion_Emergente: '',
                AUTORIZAR_REQUERIMIENTO: false,
                producto_buscar: '',
                lista_estado: [{
                        ID: 'P',
                        DESCRIPCION: 'No Aceptado'
                    },
                    {
                        ID: 'T',
                        DESCRIPCION: 'Aceptado'
                    }
                ],
                Id_estado_seleccionado: 0,
                fecha_inicio: '',
                fecha_final: '',
                Lista_vista: [],
                Estado_Emergente: false,
                RequiereAutorizacion:false,
                Consulta: false,
                permisos_tipos_bodegas: [],
                permiso_bodega_vencido:'',
                Lista_bodegas_destino: [],
                Lista_departamentos_destino: [],
                Lista_departamento_destino_filtro: [],
                id_bodega_seleccionada: '',
                cb_bodegas: '',
                cod_departamentoDestino_filtro:'',
                Producto_seleccionado: {
                    nombre: '',
                    marca_comercial: '',
                    Principio_activo: '',
                    Concentracion: '',
                    Presentacion: '',
                    Codigo: '',
                    codigo_interno: '',
                    Activo: 'N'
                },
                IdCodigoMovimiento: 0,
                Cantida_solicitar: '',
    
                Lista_detalle: [],
                Deshabilitar_campos: false,
                producto: [],
    
                Movimiento_seleccionado: [],
                Observaciones: '',
    
                user: '',
                message: 'prueba mensaje',
                messages: [],
                notificaciones: [],
                cod_bodegaDestino:'',
                listado_reportes: [],
                listado_source: {
                    CODIGO_SOLICITUD: '',
                    CODIGO_MOVIMIENTO: '',
                    CODIGO_REQUERIMIENTO: '',
                }           
            }
        },
        mounted() {
            this.cb_lista_operacion = {
                ID: 'P',
                DESCRIPCION: 'No Aceptado'
            }            
            this.Id_estado_seleccionado = 'P';

            for(let privilegio of this.$store.state.privilegios){
               if(privilegio.Privilegio.includes("TIPO_REQUERIMIENTO")){                      
                   let tipobodega = privilegio.Privilegio.split('_')[2];
                   if(tipobodega == 'T'){
                     this.permiso_bodega_vencido = 'S'
                   }else{
                     this.permisos_tipos_bodegas.push(privilegio.Privilegio.split('_')[2])                    
                   }                                                       
                }else if (privilegio.Privilegio == 'AUTORIZAR_REQUERIMIENTO'){
                    this.AUTORIZAR_REQUERIMIENTO = true;
                }
            }      
            this.Consultar_Bodega('L',this.permisos_tipos_bodegas.join(","),this.permiso_bodega_vencido);
            this.Consultar_Departamentos(this.permisos_tipos_bodegas.join(","));
        },
        computed: {
            sesion() {
                return this.$store.state.sesion
            }
        },
        async beforeCreate(){
            this.listado_reportes = await this.$recupera_parametros_reporte('Movimiento Requerimiento')                        
        },
        methods: {
            AutorizarRequerimiento(IdMovimiento){
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'warning',
                    title: 'Autorización de requerimiento',
                    acceptText: 'Autorizar',
                    cancelText: 'Cancelar',
                    text: `¿Desea autorizar el movimiento No. ${IdMovimiento}?`, 
                    accept: () => {       
                        this.Autorizar(IdMovimiento);     
                    }
                })
            },
            async Consultar_Movimiento(datos,formato='PDF') {                
                this.listado_source.CODIGO_MOVIMIENTO = datos.IDCODIGOMOVIMIENTO
                this.$reporte_modal({
                    Nombre: "Movimiento Requerimiento",
                    Opciones: this.listado_source,
                    Formato: formato
                }).catch(() => {
                })
            },
             Consultar_Departamentos(ListaTipoBodegas) {
                const url = this.$store.state.global.url
                this.axios.post(url+'app/v1_OrdenCompra/ConsultasRequerimientos', {
                                                                        Opcion: 'D',
                                                                        SubOpcion: 'T',
                                                                        ListaTipoBodegas: ListaTipoBodegas
                                                                     })
                           .then(resp => {
            
                                if (resp.data.codigo != 0) {
                                    this.$vs.notify({
                                        color: '#B71C1C',
                                        title: 'Departamentos',
                                        text: resp.data.mensaje,
                                        position: 'top-center'
                                    })
                                } else {
                                    this.Lista_departamento_destino_filtro = resp.data.json;                         
                                }
                            })
                           .catch(() => { })
        
            },
            GetDateValue(value) {
                if (value === undefined) {
                    return '';
                } else if (value === null) {
                    return '';
                } else if (value == '') {
                    return '';
                }
                return moment(value).format('DD/MM/YYYY');          
            },
            VerificarExistencias(producto){
                if(Number(producto.CANTIDAD_ENTREGA) < 0){
                    this.$vs.notify({
                            position:'top-center',
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'No puede ingresar cantidades negativas',
                        });
                    producto.CANTIDAD_ENTREGA = producto.CANTIDAD_PEDIDA
                }
                
                if(Number(producto.CANTIDAD_ENTREGA) > Number(producto.CANTIDAD_PEDIDA)){
                    this.$vs.notify({
                            position:'top-center',
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'No puede despachar mas que la cantidad solicitada',
                        });
                    producto.CANTIDAD_ENTREGA = producto.CANTIDAD_PEDIDA
                }
            },
            Operacion_seleccionada({
                DESCRIPCION
            }) {
                return `${DESCRIPCION} `
            },
            onChangeEstado(value) {
                if (value !== null && value.length !== 0) {
                    this.Id_estado_seleccionado = value.ID;
                    this.Consultar_OrdenEnc();
                } else {
                    this.Id_estado_seleccionado = '';
                }
            },
            Bodegas_seleccionado({
                CODIGO,NOMBRE
            }) {
                return `${CODIGO} - ${NOMBRE} `;
            },
            onChangeBodega(value) {
                if(!value){
                    this.cod_bodegaDestino = ''
                    this.id_bodega_seleccionada = ''
                    return
                }
                if (value !== null && value.length !== 0) {
                    this.id_bodega_seleccionada = value.CODIGO;
                    this.Consultar_OrdenEnc();  
                } else {
                    this.id_bodega_seleccionada = '';
                    this.cod_bodegaDestino = '';
                }
            },
            Emergente_Consulta(Datos) {
                this.Estado_Emergente = true;
                this.Consulta = true;
                this.cb_bodegas = '';    
                this.producto_buscar = '';
                this.Cantida_solicitar = '';

                this.Movimiento_seleccionado = Datos;

                this.IdCodigoMovimiento = Datos.IDCODIGOMOVIMIENTO;
                this.id_bodega_seleccionada = Datos.IdBodegaDestinofk;
                setTimeout(() => {
                    this.cb_bodegas = {
                        NOMBRE: Datos.NOMBREDEPARTAMENTO,
                        CODIGO: Datos.IDDEPARTAMENTO
                    }
                }, 500);
                this.Observaciones = Datos.OBSERVACIONES;
                this.Deshabilitar_campos = true;
                this.Descripcion_Emergente = 'Consulta Despacho Requerimiento';
                this.Consultar_OrdenDetalle()
            },
            Emergente_Detalle(Datos) {
                this.Estado_Emergente = true;
                this.Consulta = false;
                this.cb_bodegas = '';
    
                this.producto_buscar = '';
                this.Cantida_solicitar = '';
 
                this.Movimiento_seleccionado = Datos;

                this.IdCodigoMovimiento = Datos.IDCODIGOMOVIMIENTO;
                this.id_bodega_seleccionada = Datos.IdBodegaDestinofk;
                setTimeout(() => {
                    this.cb_bodegas = {
                        NOMBRE: Datos.NOMBREDEPARTAMENTO,
                        CODIGO: Datos.IDDEPARTAMENTO
                    }
                }, 500);
                this.Observaciones = Datos.OBSERVACIONES;
                this.Deshabilitar_campos = true;
                this.RequiereAutorizacion = Datos.RequiereAutorizacion == "S" && !Datos.CorporativoAutorizacion ? true : false;
                this.Descripcion_Emergente = 'Aceptación Devolución Requerimiento';
                this.Consultar_OrdenDet()
                
            },
    
            Validacion_Campos(Tipo, Nombre, valor, obligatorio, cant_maxima) {
                /**
                 * @General
                 * Tipo : N = Númerio, Nombre = Descripción del campo, Valor = A evaluar, Obligatorio= Verdadero o falso,cant_maxima  = Aplica unicamente para un string y define la cantidad de caracteres 
                 * Devuelve un mensaje al usuario si las validaciones no se cumple y aborta, de lo contrario devuelve verdadero
                 */
                if (Tipo == 'ID') {
                    if (valor <= 0) {
                        this.$vs.notify({
                            position:'top-center',
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else if (Tipo == 'N') {
                    
                    if ( valor <0 || valor == "") {
                        this.$vs.notify({
                            position:'top-center',
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else {
                    if (valor == '' && obligatorio == true) {
                        this.$vs.notify({
                            position:'top-center',
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
    
                        if (cant_maxima < valor.length) {
                            this.$vs.notify({
                            position:'top-center',
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'Campo ' + Nombre + ' no debe superar los ' + cant_maxima + ' carácteres.',
                            });
                            return false;
                        } else {
                            return true;
                        }
    
                    }
                }
            },
            /*****************************CONSULTAR INFORMACION*************/
            Consultar_OrdenEnc() {
                if(this.id_bodega_seleccionada == ''){
                    this.$vs.notify({
                            position:'top-center',
                                color: '#B71C1C',
                                title: 'Error',
                                text: 'Debe seleccionar la bodega fuente',
                                time: 4000
                            })
                    this.Lista_vista = [];
                    return;
                }
                this.axios.post('/app/v1_OrdenCompra/ConsultasRequerimientos', {                        
                        Estado: this.Id_estado_seleccionado,
                        FechaInicio: this.GetDateValue(this.fecha_inicio),
                        FechaFin: this.GetDateValue(this.fecha_final),
                        Opcion: 'M',
                        SubOpcion: 'E',
                        BodegaDestino: this.id_bodega_seleccionada,
                        DepartamentoDestino: this.cod_departamentoDestino_filtro?.CODIGO??null
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                            position:'top-center',
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_vista = [];
                        } else {
                            this.$vs.notify({
                                                position:'top-center',
                                                color: 'success',
                                                title: 'Inventario',
                                                text: 'Se actualizaron los requerimientos exitosamente',
                                            })
                            this.Lista_vista = resp.data.json;
    
                        }
                    })
                    .catch(() => {})

            },
            Consultar_OrdenDetalle() {
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/ConsultasRequerimientos', {
                        Opcion: 'M',
                        SubOpcion: 'D',
                        IdMovimiento: this.IdCodigoMovimiento
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {                            
                            this.Lista_detalle = [];
                        } else {
                            this.Lista_detalle = resp.data.json;
                        }
                    })
                    .catch(() => {})
            },
            Consultar_OrdenDet() {
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/ConsultasRequerimientos', {
                        Opcion: 'M',
                        SubOpcion: 'D',
                        IdMovimiento: this.IdCodigoMovimiento
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {                   
                            this.Lista_detalle = [];
                        } else {
                            this.Lista_detalle = resp.data.json.map(function(producto){ if(Number(producto.CANTIDAD_PEDIDA)<=Number(producto.INV_DISPONIBLE))
                                                                                                producto.CANTIDAD_ENTREGA = producto.CANTIDAD_PEDIDA

                                                                                        return producto;
                                                                                     });
    
                        }
                    })
                    .catch(() => {})
            },
            Consultar_Bodega(Operacion, ListaTipoBodegas) {
                const url = this.$store.state.global.url
                this.axios.post(url+'app/v1_OrdenCompra/consulta_bodega', {
                                                                        operacion: Operacion,
                                                                        lista_tipo_bodegas: ListaTipoBodegas
                                                                     })
                           .then(resp => {
            
                                if (resp.data.codigo != 0) {
                                    this.$vs.notify({
                            position:'top-center',
                                        color: '#B71C1C',
                                        title: 'Bodegas',
                                        text: resp.data.mensaje,
                                    })
                                } else {
                                        this.Lista_bodegas_destino = resp.data.json;   
                                      //  this.Lista_departamentos_destino= resp.data.json;                         
                                }
                            })
                           .catch(() => { })
        
            },            
    
            /*************************** ACTUALIZAR, ELIMINAR NUEVO REGISTRO */
            
            Registrar_DespachoDet(datos, Operacion) {
    
                //Operacion = 'E' -> Se va despachar producto.
                //Operacion = 'A' -> Se va despachar producto.
                var res_variables = false;
    
                
    
                res_variables = this.Validacion_Campos('N', 'Cantidad Despachar', datos.CANTIDAD_ENTREGA, true, 100000000);
    
                if (res_variables) {
                    this.axios.post('/app/v1_OrdenCompra/Requisicion_Det', {
                            Requisicion_det: datos.IDREQUISICIONDET,
                            Requisicion_enc: datos.IDREQUISICIONENCFK,
                            Empresa: this.sesion.sesion_empresa,
                            Producto: datos.IDPRODUCTOFK,
                            cant_pedida: 0,
                            cant_entregada: datos.CANTIDAD_ENTREGA,
                            cant_recibida: 0,
                            corporativo: this.sesion.corporativo,
                            Operacion: Operacion,
                        })
                        .then(resp => {
    
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                            position:'top-center',
                                    color: '#B71C1C',
                                    title: 'Alerta',
                                    text: resp.data.mensaje,
                                })
                            }
                            this.Consultar_OrdenDet();
                        })
                }
            },
            Autorizar(IdMovmiento){
                this.axios.post('/app/v1_OrdenCompra/ManejoRequerimientosDevoluciones', {
                            NoRequerimiento : IdMovmiento, 
                            TipoUpdate: 42
                        })
                        .then(resp => {                                                        
                            if (resp.data.tipo_error == 0) {
                                this.Estado_Emergente = false;
                                this.Consultar_OrdenEnc();
                            }                            
                        })      
            },
            Aceptar_Devolucion() {    
                
                let res_variables = this.Validacion_Campos('S', 'Observaciones', this.Observaciones, true, 100);
                if(!res_variables)return;

                this.axios.post('/app/v1_OrdenCompra/ManejoRequerimientosDevoluciones', {
                            NoRequerimiento : this.Movimiento_seleccionado.IDCODIGOMOVIMIENTO,                            
                            ListaProductos: this.Lista_detalle.map((producto)=>producto.IDPRODUCTOFK).toString(),
                            CantidadesDevolucion: this.Lista_detalle.map((producto)=>producto.CANTIDAD_ENTREGA).toString(),
                            TipoUpdate: 41,
                            Descripcion: this.Observaciones
                        })
                        .then(resp => {
                            
                            
                            if (resp.data.tipo_error >= 0) {
                                this.Estado_Emergente = false;
                                this.Consultar_OrdenEnc();
    
                            }
                            
                        })                   
                
            }
        }
    
    }
    </script>    