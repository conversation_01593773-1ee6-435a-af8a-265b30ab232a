<template>
    <vx-card title="Consulta Pacientes Polizas">
        <ValidationObserver ref="consultaPacientesRef" v-slot="{ invalid, handleSubmit }" mode="lazy">
            <form @submit.prevent="handleSubmit(submitForm())">

                <div class="container">

                    <div class="a-lR6GW">


                        <div class="flex flex-wrap">
                            <vs-row>
                                <vs-col vs-type="flex" vs-w="6">
                                    <div class="w-4/5  p-2">

                                        <vs-input v-model="consultaPaciente.NombrePaciente" label="Nombre Paciente"
                                            class="w-full" @keyup.enter="handleSubmit(ConsultarPaciente().Consultar())" />

                                    </div>
                                    <div class="w-4/5  p-2">

                                        <vs-input v-model="consultaPaciente.ApellidoPaciente" label="Apellido Paciente"
                                            class="w-full" @keyup.enter="handleSubmit(ConsultarPaciente().Consultar())" />

                                    </div>
                                </vs-col>

                            </vs-row>
                            <vs-row>
                                <div class="flex w-full p-1">
                                    <div class="pl-2">
                                        <vs-button @click="handleSubmit(ConsultarPaciente().Consultar())"
                                            :disabled="invalid">
                                            Consultar
                                        </vs-button>
                                    </div>
                                    <div
                                      class="pl-2"
                                      v-if="showCreateButton"
                                      :disabled="invalid"
                                      type="buttons"                                      
                                      >
                                        <vs-button  @click="submit($event,'crear')">
                                            Crear
                                        </vs-button>
                                    </div>
                                </div>
                            </vs-row>
                        </div>

                      <DxDataGrid   :data-source="listaPacientes"
                                    :show-borders="true"
                                    :column-auto-width="true"
                                    @cell-click="collapseRows"
                                    :ref="rowMethods"
                                    :paging="{ enabled: true, pageSize: 10 }"
                                    :row-alternation-enabled="true"                                    
                      >

                            <DxColumn caption="Paciente" data-field="Codigo" />
                            <DxColumn caption="Cliente" data-field="IdCliente" />
                            <DxColumn caption="NombrePaciente" data-field="NombrePaciente" />
                            <DxColumn caption="PorcentajeCoincidencia" data-field="PorcentajeCoincidencia" />
                            <DxColumn caption="Continuar" type="buttons" :buttons="[{icon:'fa fa-arrow-right',onClick:(e)=> { submit(e, 'continuar') }, id: 'continuar'   }]" >
                      
                            </DxColumn>
                            <DxMasterDetail :enabled="true" template="masterDetailTemplate" />
                            <template #masterDetailTemplate="{ data: tr }">
                                <div>
                                    <PolizaSaludSiempre :id-paciente="parseInt(tr.data.Codigo)">
                                    </PolizaSaludSiempre>
                                </div>


                            </template>

                        </DxDataGrid>

                    </div>

                </div>

            </form>
        </ValidationObserver>
    </vx-card>
</template>


<script>
import PolizaSaludSiempre from "/src/components/sermesa/modules/admisiones/ConsultaSaludSiempre.vue"


import {
    DxDataGrid,
    DxColumn,
    DxMasterDetail,

} from 'devextreme-vue/data-grid';



const TIPO_DESCUENTO_SALUD_SIEMPRE = 'Salud Siempre'
const rowMethods = ''
export default {
    name: "ConsultaPacientes",
    components: {
        PolizaSaludSiempre,
        DxDataGrid,
        DxColumn,
        DxMasterDetail,        
    },
    data() {
        return {
            listaPacientes: [],
            datosPolizas: [],
            consultaPaciente: {
                Empresa: '',
                NombrePaciente: '',
                ApellidoPaciente: '',
                NumeroDocumento: '',
                TipoDescuentoSaludSiempre: TIPO_DESCUENTO_SALUD_SIEMPRE,
            },            
            rowMethods,
            buttonId: 'crear-button'
        }

    },
    props: {
        showCreateButton: {
            type: Boolean,
            default: true
        },
        mostrarPolizas: {
            type: Boolean,
            default: true
        }
    },
    watch: {
        'consultaPaciente.NombrePaciente': function () {
            this.listaPacientes = []
            this.consultaPaciente.ApellidoPaciente ='' 
        }
    },
    computed: {
        dataGrid: function () {
            return this.$refs[rowMethods].instance
        }
    },
    mounted() {
    },
    methods: {
        ConsultarPaciente() {
            return {
                Consultar: async () => {
                    const resp = await this.axios.post('/app/v1_admision/ConsultaPacientePorNombre', this.consultaPaciente)
                    this.listaPacientes = resp.data.json.map(m => {
                        return {
                            ...m,
                        }
                    })
                },
            }
        },
        async submit(event, action) {

            if (action === 'crear') {                
                const grid = this.$refs[rowMethods].instance
                grid.collapseAll(-1)
            // Lógica específica para el botón Crear
            } else if (action === 'continuar') {
                event.component.collapseAll(-1)
            }
            
            if (action === 'continuar') {

                if (!event.row.data.Codigo)
                    return

                this.$store.dispatch('paciente/initPaciente')
                this.$store.dispatch('paciente/setPaciente', event.row.data)
                this.$store.dispatch('workflow/guardarTipoPaso', 'EDIT')
                this.$emit("ready", true);
            }
            else {                
                this.$store.dispatch('workflow/guardarTipoPaso', 'CREATE')
                this.$store.dispatch('admision/initAdmision')
                this.$store.dispatch('paciente/initPaciente')
                this.$emit("ready", true);
            }
        },        
        submitForm() {

        },
        collapseRows(e) {            
            if (e.columnIndex !== 1) {
                if (this.dataGrid.isRowExpanded(e.key)) {
                    this.dataGrid.collapseRow(e.key)
                }
                else {
                    e.component.collapseAll(-1)
                    this.dataGrid.expandRow(e.key)
                }
            }

        }


    }
};
</script>



<style scoped>
* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

.container {
    display: grid;
    grid-gap: 5px;
    width: 100%;
    height: 100%;
    grid-template-areas: "a-lR6GW"
        "a-lR6GW";

}



.container>div {
    border: 1px solid #888;
}

.a-lR6GW {
    grid-area: a-lR6GW;
}

.container {
    max-width: 100%;
}

.td-check {
    padding: 10 !important;
}


h1 {
    width: 100%;
    margin: 0 auto;
    text-align: center;
}



.inner_table {
    grid-gap: 1px;
    width: 100%;
    height: auto;
    padding-right: 5%;
    padding-left: 3%;
    padding-bottom: 0%;
    min-height: auto;
    display: flex;
    flex: row wrap;
    border: none;
    display: inline;
}

.highlight {
    background-color: red;
}

tr:hover {
    cursor: pointer;
}

.resaltar-columna {
    background-color: rgba(0, 143, 190, 0.2) !important;
    z-index: 900;

}

.expanded-column {
    background-color: linear-gradient(9deg, rgba(113, 189, 214, 0.9248074229691877) 0%, rgba(252, 252, 252, 1) 29%, rgba(255, 255, 255, 1) 53%);
    z-index: 900;

}

.to_front {
    z-index: -1;
    position: relative;
}


.inner_table.resaltar-columna {
    background-color: rgba(207, 236, 243, 0.2) !important;

    /* Ajusta otros estilos según sea necesario */
}

.sombreado {
    background-color: #f6f4f4;
    /* Color de fondo para el sombreado */
}
</style>

<style>
.table_expand .td-check {
    width: 20px !important;
}

.hidden-button {
    display: none !important;
}
</style>
