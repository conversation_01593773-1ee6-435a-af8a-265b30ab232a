<template>
<div class="afiliado-informacion-plan">
    <DxDataGrid v-bind="GridOptions" :data-source="Beneficios" />
</div>
</template>

<script>

import {
    DefaulGridOptions
} from './data.js'
export default {
    name: 'AfiliadoInfoPlan',
    props: {
        IdPlan: {
            type: String,
            default: null
        },
        IdAfiliado: {
            type: String,
            default: null,
        }
    },
    components: {
        
    },
    data() {
        return {
            Beneficios: [],
            GridOptions: {
                ...DefaulGridOptions,
                columns: [{
                        caption: 'Descripción',
                        dataField: 'descripcion',
                        sortOrder: 'asc',
                        sortIndex: 0,
                    },
                    {
                        caption: 'B / E',
                        dataField: 'beneficio',
                    },
                    {
                        caption: 'Meses Carencia',
                        dataField: 'periodoesperameses',
                    },
                    {
                        caption: 'Límite Veces',
                        dataField: 'limiteveces',
                    },
                    {
                        caption: 'Límite Monto',
                        dataField: 'limitemonto',
                    },
                ],
            },
        }
    },
    methods: {
        
        CargaBeneficios() {
            return this.axios
                .post("/app/v1_autorizaciones/Beneficios", {
                    Operacion: "BENEFICIOS",
                    Afiliado: this.IdAfiliado,
                })
                .then((resp) => {
                    if (resp.data.codigo == 0) {
                        this.Beneficios = resp.data.json;
                    }
                })
        },
       
    },
    watch: {        
        'IdAfiliado'(){
            this.CargaBeneficios()
        },
    },
    beforeMount() {
        
    },
    mounted() {
        this.CargaBeneficios()
    },
}
</script>
<styles>

.dx-popover-arrow {  
    display: none !import;  
}  

</styles>