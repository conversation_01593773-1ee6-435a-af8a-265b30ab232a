<template>
<!-- Solicitar Credencial -->
<vs-popup title="Validar Credencial" :active.sync="credencial.show" class="sm">
    <div v-if="credencial" class="flex">
        <vs-input ref="inputCorporativo" label="Corporativo" class="mr-5" v-model="credencial.corporativo"></vs-input>
        <vs-input label="Contraseña" type="password" autocomplete="off" v-model="credencial.password" @keyup.enter="Otros().ValidarCredencial()"></vs-input>
    </div>
    <div class="text-right">
        <vs-button @click="Otros().ValidarCredencial()">Aceptar</vs-button>
    </div>
</vs-popup>
</template>

<script>
export default {
    data() {
        return {
            credencial: {
                show: false,
                corporativo: null,
                password: null,
                result: null,//callback de exito en la autenticación
                reject: null,//callback al cancelar la operación
                notify: true,//indica si se muestra modal de notificación
                focus: false
            },
            tempExito: false,//para que no haga reject si el modal se cierra luego de haber autenticado
        }
    },
    watch: {
        "$store.state.extra.solicitarCredencial"(val) {
            if (val) {
                this.credencial.show = true
                this.credencial.result = val.result
                this.credencial.corporativo = val.corporativo
                this.credencial.password = null
                this.credencial.focus = true
                this.credencial.notify = val.notify
                this.credencial.reject = val.reject
            }
        },
        'credencial.show'(val) {
            if(!val && typeof this.credencial.reject == 'function' && !this.tempExito) {
                this.credencial.reject()
            }
            this.tempExito = false
        },
    },
    methods: {
        Otros() {
            return {
                ValidarCredencial: () => {
                    this.axios.post('/app/usuario/verificar', this.credencial)
                        .then(resp => {
                            if(this.credencial.notify)
                                this.$vs.notify({
                                    title: 'Usuario validado ',
                                    text: resp.data.usuario,
                                    color: 'success'
                                })
                            this.credencial.result(this.credencial.corporativo)
                            this.credencial.show = false
                            this.credencial.result = null
                            this.credencial.corporativo = null
                            this.credencial.password = null
                            this.tempExito = true
                        })
                }
            }
        },
    },
    updated() {
        if (this.credencial.focus) {
            this.$refs.inputCorporativo.focusInput()
            this.credencial.focus = false
        }
    }
}
</script>
