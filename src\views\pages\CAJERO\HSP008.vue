<template>
    <vx-card :title="`Caja Sobregiro - ${sesion.sesion_sucursal_nombre}`">

        <buscador ref="BuscarChequesRemisiones" buscador_titulo="Buscador / Pagos Remisiones" :api="'app/v1_caja/ConsultaRecibosHospitales'" 
                        :campos="['Remesa', 'FormaPago','BancoEmisor', 'Nombre','Monto', 'Saldo', 'ChequeNumero','CuentaCheque']"
                        :titulos="['Remesa', 'Forma Pago','Banco','Nombre','#Monto', '#Saldo', 'Cheque','Cuenta']"
                        :api_filtro="{'opcion':'C','subOpcion':'4'}"
                        :multiselect="false" :api_validar_campo="true" :api_reload="true" :api_preload="true"  
                        :api_cache="true"  :all_datos="true"/>
        <vs-row> 
            <ConfiguracionCaja ref="ConfiguracionCajaRef"
                               @CargarCaja="CargarCaja" :FiltrarAgrupacionCaja=FiltrarAgrupacionCaja
                               TipoCajaSeleccionada="TipoCajaFacturacion" CajaSeleccionada="CajaFacturacion" 
                               class="w-full">
            </ConfiguracionCaja>
            <vs-row class="w-full">
                <vs-col class="sm:w-full md:w-full lg:w-6/12 xl:w-3/12 p-1">
                    <vs-input label="Nombre"  
                            class="w-full"
                            v-model="recibo.nombre" 
                            :disabled="true"/>      
                </vs-col>                                                                                             
            </vs-row> 
            <vs-row class="w-full">
                <vs-col class="sm:w-full md:w-full lg:w-6/12 xl:w-3/12 p-1">
                    <label class="w-full typo__label label-shrink pl-1" style="color: rgba(0,0,0,.7); font-size: 10pt;">Concepto</label>
                    <vs-textarea rows="4" counter="80" type="text" class="w-full" v-model="recibo.concepto"/>                     
                </vs-col>    
            </vs-row>
            <vs-row class="w-full">
                <vs-col class="sm:w-11/12 md:w-11/12 lg:w-4/12 xl:w-2/12 p-1">
                    <label class="typo__label pl-1" style="color: rgba(0,0,0,.7); font-size: 10pt;">No. Cheque:&nbsp;</label>
                    <vs-col vs-type="flex" vs-align="center" class="pb-2">
                        <vs-input  ref="refCheque" v-model=noCheque @keyup.enter="CargarPagosPorNoCheque()" @keydown.tab="CargarPagosPorNoCheque()" class="w-full"></vs-input>
                        <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="AbrirBuscadorPagos()" icon="fa-search"></vs-button>                                                                        
                    </vs-col> 
                </vs-col>      
                <vs-col class="sm:w-1/12 md:w-1/12 lg:w-2/12 xl:w-1/12 p-1 pb-2" vs-type="flex" vs-align="center">
                    <label class="typo__label" style="color: rgba(0,0,0,.7); font-size: 12pt;">Pagos</label>
                </vs-col>                
            </vs-row>
            <vs-row class="w-full p-1">                         
                <DxDataGrid :ref="PagosRef" :data-source="pagos" :visible="true"  
                        v-bind="DefaultDxGridConfiguration"
                        :showColumnLines="true" :showBorders="true" :searchPanel="{visible: false}" 
                        :rowAlternationEnabled="true" width="100%" height="150px" 
                        :word-wrap-enabled="false">

                            <DxDataGridEditing :allow-updating="true" :allow-adding="false" :allow-deleting="true" mode="row" />
                            <DxDataGridColumn :width="100" data-field="Remesa" caption="Remesa" :allow-editing="false" :allow-filtering="false"/>                                                                                
                            <DxDataGridColumn :width="100" data-field="FormaPago" caption="Forma Pago" :allow-editing="false" :allow-filtering="false" >                                                    
                            </DxDataGridColumn>                                              
                            <DxDataGridColumn :width="175" data-field="SaldoIngresado" caption="Monto"  :allow-editing="true" :editor-options="{format:'Q #,##0.00'}" format="Q #,##0.00" data-type="number" alignment="center">
                                <DxCustomRule message="El monto del recibo debe ser menor o igual al monto de la remisión" type="custom" :validation-callback="ValidarMontoRecibo" />
                                <DxDataGridRangeRule  message="El monto del recibo debe ser mayor a cero" :min="0"/>
                            </DxDataGridColumn>
                            <DxDataGridColumn :width="100" data-field="BancoEmisor"  caption="Banco" :allow-editing="false" :allow-filtering="false">                                                    
                            </DxDataGridColumn>
                            <DxDataGridColumn :width="200" data-field="Nombre" caption="Nombre" alignment="center" :allow-editing="false" />
                            <DxDataGridColumn :width="150" data-field="ChequeNumero" caption="Cheque" data-type="number" :allow-editing="false" alignment="center" />
                            <DxDataGridColumn :width="150" data-field="CuentaCheque" caption="Cuenta Cheque" :allow-editing="false" alignment="center" />
                            <DxDataGridColumn type="buttons" caption="Acción">
                                <DxDataGridButton name="edit" icon="edit" text="Editar"/>
                                <DxDataGridButton name="delete" type="danger" icon="fas fa-trash" text="Borrar"/>
                                <DxDataGridButton name="save" icon="save" text="Guardar" />
                                <DxDataGridButton name="cancel" type="danger" icon="feather icon-x" text="Cancelar" />
                            </DxDataGridColumn>
                            <DxDataGridSummary>
                                <DxDataGridTotalItem  column="monto" summary-type="sum" value-format="Q #,##0.00" data-type="number" display-format="Total: {0}"/>
                            </DxDataGridSummary>                          
                </DxDataGrid>
            </vs-row>
            <vs-row class = 'w-full pt-10'>
                <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 m-1">
                    <vs-button color="success" class="w-full" id="botonRecibo"
                        :disabled="!configuracion.activarRecibos"
                        :type="reciboSeleccionado?'border':'filled'" 
                        @keyup.tab="reciboSeleccionado=true"
                        v-on:blur="reciboSeleccionado=false"
                        @click="GrabarReciboCobro()">
                        Recibo
                    </vs-button>
                </vs-col>
                <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 m-1">
                    <vs-button color="warning" class="w-full"
                        @click="LimpiarPantalla()">
                        Limpiar
                    </vs-button>
                </vs-col>
            </vs-row>    
        </vs-row>
    </vx-card>
</template>
<script>

import 'flatpickr/dist/flatpickr.css';
import ConfiguracionCaja from "/src/components/sermesa/modules/caja/ConfiguracionCaja.vue";    
import {DefaultDxGridConfiguration} from './data';

const PagosRef = "PagosRef"

export default{    
    name:'Recibo_Sobregiro',
    components:{
        ConfiguracionCaja
    }, 
    data() {
        return{ 
            reciboSeleccionado:false,
            FiltrarAgrupacionCaja:['5'],    
            DefaultDxGridConfiguration:{...DefaultDxGridConfiguration}, 
            recibo:{    nombre:null,
                        concepto:null			
            },
            configuracion:{
                cajaSeleccionada:null,
                activarFacturacion: false,
                activarRecibos: false  
            },	 
            noCheque:null,
            pagos:[],
            PagosRef,
        }
    },
    mounted(){
        this.init()
    },
    activated() {
        window.addEventListener('focus', this.actualizarCajero);   
    },
    deactivated() {
        window.removeEventListener('focus',this.actualizarCajero);
    },
    methods:{
        actualizarCajero(){  
            this.$refs.ConfiguracionCajaRef.CargarCajaInicial();
        },
        init(){
            this.recibo.nombre = 'SobreGiro realizado por '+this.sesion.corporativo
        },
        async GenerarRecibo(Serie,NumeroRecibo) {

            let reporte = "Recibo"

            let postData = {
                SerieRecibo:   Serie ,
                NumeroRecibo: NumeroRecibo
            }


            this.$reporte_modal({
                Nombre: reporte,
                Opciones: {
                    ...postData
                },
                Formato:this.FormatoReporte,
                NombreArchivoDescargar:Serie+'_'+NumeroRecibo,
                Descargar:true
            })                

        },  
        CargarCaja(cajaSeleccionada){                
            this.configuracion.cajaSeleccionada   = cajaSeleccionada.Cajero
            this.configuracion.activarFacturacion = cajaSeleccionada.Factura
            this.configuracion.activarRecibos     = cajaSeleccionada.Recibo
        },
        GrabarReciboCobro(){
            if(this.pagos.length!=1){
                this.Consulta().MostrarError('Ingrese un pago para realizar el Sobregiro.',2000)
                return
            }   
            
            if(!this.recibo.concepto || this.recibo.concepto.trim()<=0){
                this.Consulta().MostrarError('Ingrese un concepto',2000)
                return
            }  

            if(!this.configuracion.cajaSeleccionada.SerieRecibo){
                this.Consulta().MostrarError('Seleccionar una caja para realizar el sobregiro',2000)
                return
            }

            if(!this.configuracion.cajaSeleccionada.SerieFac){
                this.Consulta().MostrarError('Seleccionar una caja para realizar el sobregiro',2000)
                return
            }

            const botonRecibo = document.getElementById("botonRecibo");
            if(botonRecibo) botonRecibo.blur();

            let listaRecibos = 
                this.pagos.map(pago=>({  
                                        'status':'',
                                        'formaDePago':pago.FormaPago,
                                        'monto':pago.SaldoIngresado,
                                        'banco':pago.BancoEmisor,
                                        'nombreBanco':pago.Nombre,
                                        'previsado':'',
                                        'cheque':this.ConvertirEntero(pago.ChequeNumero),
                                        'cuenta':pago.CuentaCheque,
                                        'autorizacion':'',
                                        'tasa':0.00
                                     })
                               )

            this.axios.post('/app/v1_caja/GeneracionRecibosHospitales',{
                opcion:'I',
                subOpcion:4,
                serieFactura:this.configuracion.cajaSeleccionada.SerieFac,
                serieRecibo:this.configuracion.cajaSeleccionada.SerieRecibo,
                concepto:this.recibo.concepto,
                listaRecibos:listaRecibos,
                ipoc:0
            }).then(resp=>{
                if(resp.data.codigo==0){
                    this.$vs.dialog({
                                        type: 'confirm',
                                        color: 'success',
                                        title: 'Recibo Sobregiro',
                                        acceptText: 'Aceptar',
                                        cancelText: 'Cancelar',
                                        text: resp.data.descripcion+' '+resp.data.Serie+'-'+resp.data.Recibo,
                                        accept: ()=>{this.LimpiarPantalla();},
                                        cancel: ()=>{this.LimpiarPantalla();}                                               
                                    })   
                    this.GenerarRecibo(resp.data.Serie,resp.data.Recibo);                    
                }
            })
        },
        CargarPagosPorNoCheque(){
            this.axios.post('/app/v1_caja/ConsultaRecibosHospitales',{
                opcion:'C',
                subOpcion:'4',
                noCheque:this.noCheque
            }).then(resp => {
                if(resp.data.json && resp.data.json.length == 1 ){
                    if(this.parseNumber(resp.data.json[0].Saldo)<=0){
                        this.Consulta().MostrarError('La remesa no tiene saldo',3000)
                        this.pagos = []
                        return
                    }else{
                        resp.data.json[0].SaldoIngresado = 0.00
                    }
                    this.pagos = [resp.data.json[0]]  
                    this.dataGridPagos.refresh()     
                }else{
                    this.AbrirBuscadorPagos()
                }
            })
        },
        AbrirBuscadorPagos(){ 
            this.$refs.BuscarChequesRemisiones.iniciar((pago) => {
                if(pago){
                    if(this.parseNumber(pago.Saldo)<=0){
                        this.Consulta().MostrarError('La remesa no tiene saldo',3000)
                        this.pagos = []
                        return
                    }else{
                        pago.SaldoIngresado = 0.00
                    }
                    this.pagos = [pago]                      
                    this.noCheque = pago.ChequeNumero
                    this.dataGridPagos.refresh()
                }else{
                    this.pagos = []
                    this.noCheque = ''
                }
            })

        },
        ValidarMontoRecibo(e){
            return this.ConvertirFloat(e.data.SaldoIngresado) <= this.ConvertirFloat(e.data.Saldo)
        },
        Consulta(){
            return{
                MostrarError:(mensaje,tiempo=2000)=>{
                    this.$vs.notify({
                        time:tiempo,
                        title: 'Cajero',
                        color: 'danger',
                        text:mensaje,
                        position: 'top-center'
                    })
                },
                MostrarSatisfactorio:(mensaje)=>{
                    this.$vs.notify({
                        time:2000,
                        title: 'Cajero',
                        color: 'success',
                        text:mensaje,
                        position: 'top-center'
                    })
                }
            }
        },
        ConvertirEntero(Valor){
            let conversion = parseInt(Valor)
            if(isNaN(conversion)){
                return 0
            }
            return conversion;
        },
        ConvertirFloat(Valor,Decimales=2){
            let conversion = parseFloat(Valor?Valor:0.00)
            if(isNaN(conversion)){
                return 0.00
            }
            return parseFloat(conversion.toFixed(Decimales))
        },
        parseNumber(entrada){
            let numero = 0
            if(typeof entrada == "string"){
                if(Number.isNaN(Number(entrada))) numero = 0
                else numero = Number(entrada)
            }else  if(typeof entrada == "number"){
                numero = entrada
            }
            return numero
        },
        LimpiarPantalla(){
            this.recibo.concepto  = null
            this.noCheque = ''
            this.pagos = []
        },
    },
    computed:{
        sesion() {
            return this.$store.state.sesion
        },
        dataGridPagos: function () {
            return this.$refs[PagosRef].instance
        }
    }
}
</script>