<template>
    <div class="flex flex-wrap">
        <buscador ref="RefBuscarCorte" buscador_titulo="Buscador / Cortes de la Caja" :api="'app/v1_contabilidad_hospital/GetCortesCaja'" 
            :api_filtro="{Caja:DetalleFacturacion.NoCaja}"
            :campos="['Final', 'Codigo', 'Cajero']"
            :titulos="['Fecha/Hora', 'Codigo', 'Cajero']"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" />

        <vx-card title="Seleccione un reporte" class="box-custom1">
            <div class="containerem">
                <vs-row>
                    <vs-col>
                        <vs-radio v-model="reporte" vs-name="radios1" vs-value="0">Detalle de Facturación</vs-radio>
                    </vs-col>
                </vs-row>
                <br>
                <vs-row>
                    <vs-col>
                        <vs-radio v-model="reporte" vs-name="radios1" vs-value="1">Iva <PERSON></vs-radio>
                    </vs-col>
                </vs-row>
                <br>
                <vs-row>
                    <vs-col>
                        <vs-radio v-model="reporte" vs-name="radios1" vs-value="2">Iva Débito Resumen</vs-radio>
                    </vs-col>
                </vs-row>
            </div>
        </vx-card>
        <br>
        <vx-card title="Parámetros Detalle de Facturación" class="box-custom2" :style="parametroDetalleFacturacion">
            <vs-row vs-justify="center" vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="1">
                    <label class="typo__label">Del: </label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-input type="date" class="datePicker" v-model="DetalleFacturacion.FechaInicial"></vs-input>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="1"></vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="1">
                    <label class="typo__label">Hora: </label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-input type="time" class="datePicker" v-model="DetalleFacturacion.HoraInicial"></vs-input>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-justify="center" vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="1">
                    <label class="typo__label">Al: </label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-input type="date" class="datePicker" v-model="DetalleFacturacion.FechaFinal"></vs-input>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="1"></vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="1">
                    <label class="typo__label">Hora: </label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-input type="time" class="datePicker" v-model="DetalleFacturacion.HoraFinal"></vs-input>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-justify="center" vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="1">
                    <label class="typo__label">Caja: </label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-input type="number" v-model.number="DetalleFacturacion.NoCaja"></vs-input>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="4"></vs-col>
            </vs-row>
            <br>
            <vs-row vs-justify="center" vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="1">
                    <label class="typo__label">Corte: </label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-input v-model="DetalleFacturacion.NoCorte"></vs-input>
                    <vs-button color="primary" icon-pack="fa" icon="fa-search" @click="BuscarCorte()"></vs-button>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="4"></vs-col>
            </vs-row>
            <br>
            <vs-row vs-justify="center" vs-align="center">
                <vs-button color="primary" icon-pack="fa" icon="fa-file-invoice" @click="GenerarDetalleFacturacion()">Generar</vs-button>
            </vs-row>
        </vx-card>
        <vx-card title="Parámetros Iva Débito" class="box-custom2" :style="parametroIvaDebito">
            <vs-row vs-justify="center" vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="1">
                    <label class="typo__label">Periodo: </label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                    <multiselect v-model="IvaDebito.Periodo" :options="ListPeriodos" :show-labels="false" track-by="Codigo" label="Descripcion"></multiselect>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-justify="center" vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="1">
                    <label class="typo__label">Serie: </label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-input v-model="IvaDebito.Serie"></vs-input>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2"></vs-col>
            </vs-row>
            <br>
            <vs-row vs-justify="center" vs-align="center">
                <vs-button color="primary" icon-pack="fa" icon="fa-file-invoice" @click="GenerarIvaDebito()">Generar</vs-button>
            </vs-row>
        </vx-card>
        <vx-card title="Parámetros Iva Débito Resumen" class="box-custom2" :style="parametroIvaDebitoResumen">
            <vs-row vs-justify="center" vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="1">
                    <label class="typo__label">Periodo: </label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                    <multiselect v-model="IvaDebito.Periodo" :options="ListPeriodos" :show-labels="false" track-by="Codigo" label="Descripcion"></multiselect>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-justify="center" vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="1">
                    <label class="typo__label">Serie: </label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-input v-model="IvaDebito.Serie"></vs-input>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2"></vs-col>
            </vs-row>
            <br>
            <div  class="container">
                <label class="typo__label">Tipo de Resumen</label>
                <vs-divider/>
                <div class="div-container">
                    <vs-row vs-justify="center" vs-align="center">
                        <vs-col vs-w="10">
                            <vs-radio v-model="IvaDebito.TipoResumen" vs-name="radios2" vs-value="0">FEL (Factura Electrónica)</vs-radio>
                        </vs-col>
                    </vs-row>
                    <br>
                    <vs-row vs-justify="center" vs-align="center">
                        <vs-col vs-w="10">
                            <vs-radio v-model="IvaDebito.TipoResumen" vs-name="radios2" vs-value="1">FACE (Factura Pura)</vs-radio>
                        </vs-col>
                    </vs-row>
                </div>
            </div>
            <br>
            <vs-row vs-justify="center" vs-align="center">
                <vs-button color="primary" icon-pack="fa" icon="fa-file-invoice" @click="GenerarIvaDebitoResumen()">Generar</vs-button>
            </vs-row>
        </vx-card>
    </div>
</template>
<script>
    import Multiselect from "vue-multiselect"
    import "vue-multiselect/dist/vue-multiselect.min.css"

    export default{
        data(){
            return{
                reporte: '0',
                parametroDetalleFacturacion: '',
                parametroIvaDebito: 'display: none',
                parametroIvaDebitoResumen: 'display: none',
                DetalleFacturacion: {
                    FechaInicial: '',
                    HoraInicial: '',
                    FechaFinal: '',
                    HoraFinal: '',
                    NoCaja: '',
                    NoCorte: ''
                },
                ListPeriodos: [],
                IvaDebito: {
                    Periodo: '',
                    Serie: '',
                    TipoResumen: '0'
                }
            }
        },
        components: {
            Multiselect
        },
        methods: {
            ObtenerFechas(){
                this.DetalleFacturacion.FechaInicial = new Date().toISOString().slice(0, 10)
                this.DetalleFacturacion.HoraInicial = '00:00'
                this.DetalleFacturacion.FechaFinal = new Date().toISOString().slice(0, 10)
                this.DetalleFacturacion.HoraFinal = '23:59'
            },
            BuscarCorte(){
                if(this.DetalleFacturacion.NoCaja == ''){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Mensaje',
                        text: `Debe especificar la caja`,
                    })
                    return
                }

                this.$refs.RefBuscarCorte.iniciar((data) => {
                    if(data != null){
                        this.DetalleFacturacion.NoCorte = data.Codigo
                    }
                })

            },
            GenerarDetalleFacturacion(){
                this.$reporte_modal({
                    Nombre: "Detalle de Facturacion Excel",
                    Opciones: {
                        FechaI: this.DetalleFacturacion.FechaInicial + " " + this.DetalleFacturacion.HoraInicial,
                        FechaF: this.DetalleFacturacion.FechaFinal + " " + this.DetalleFacturacion.HoraFinal,
                        NoCaja: this.DetalleFacturacion.NoCaja,
                        NoCorte: this.DetalleFacturacion.NoCorte
                    },
                    Formato: "EXCEL"
                })
                this.$reporte_modal({
                    Nombre: "Detalle de Facturacion",
                    Opciones: {
                        FechaI: this.DetalleFacturacion.FechaInicial + " " + this.DetalleFacturacion.HoraInicial,
                        FechaF: this.DetalleFacturacion.FechaFinal + " " + this.DetalleFacturacion.HoraFinal,
                        NoCaja: this.DetalleFacturacion.NoCaja,
                        NoCorte: this.DetalleFacturacion.NoCorte
                    },
                    Formato: "PDF",
                    Descargar: true
                })
            },
            GetPeriodo(){
                this.axios.post('/app/v1_contabilidad_hospital/GetPeriodos', {
                })
                .then(resp => {
                    if(resp.data.codigo == 0){
                        this.ListPeriodos = resp.data.json
                        this.axios.post('/app/v1_contabilidad_hospital/GetPeriodos', {
                            Final: new Date().toISOString().slice(0, 10)
                        })
                        .then(resp => {
                            if(resp.data.codigo == 0){
                                this.IvaDebito.Periodo = {
                                    Codigo: resp.data.json[0].Codigo,
                                    Descripcion: resp.data.json[0].Descripcion,
                                    FechaInicial: resp.data.json[0].FechaInicial,
                                    FechaFinal: resp.data.json[0].FechaFinal
                                }
                            }
                        })
                    }
                })
            },
            GenerarIvaDebito(){
                this.$reporte_modal({
                    Nombre: "Iva Debito Excel",
                    Opciones: {
                        NombrePeriodo: this.IvaDebito.Periodo.Descripcion,
                        periodo: this.IvaDebito.Periodo.Codigo,
                        FechaInicioPeriodo: this.IvaDebito.Periodo.FechaInicial,
                        SerieFiltro: this.IvaDebito.Serie
                    },
                    Formato: "EXCEL"
                })
                this.$reporte_modal({
                    Nombre: "Iva Debito",
                    Opciones: {
                        NombrePeriodo: this.IvaDebito.Periodo.Descripcion,
                        periodo: this.IvaDebito.Periodo.Codigo,
                        FechaInicioPeriodo: this.IvaDebito.Periodo.FechaInicial,
                        SerieFiltro: this.IvaDebito.Serie
                    },
                    Formato: "PDF",
                    Descargar: true
                })
            },
            GenerarIvaDebitoResumen(){
                this.$reporte_modal({
                    Nombre: "Iva Debito Resumen Excel",
                    Opciones: {
                        NombrePeriodo: this.IvaDebito.Periodo.Descripcion,
                        periodo: this.IvaDebito.Periodo.Codigo,
                        FechaInicioPeriodo: this.IvaDebito.Periodo.FechaInicial,
                        SerieFiltro: this.IvaDebito.Serie,
                        TipoResumen: this.IvaDebito.TipoResumen
                    },
                    Formato: "EXCEL"
                })
                this.$reporte_modal({
                    Nombre: "Iva Debito Resumen",
                    Opciones: {
                        NombrePeriodo: this.IvaDebito.Periodo.Descripcion,
                        periodo: this.IvaDebito.Periodo.Codigo,
                        FechaInicioPeriodo: this.IvaDebito.Periodo.FechaInicial,
                        SerieFiltro: this.IvaDebito.Serie,
                        TipoResumen: this.IvaDebito.TipoResumen
                    },
                    Formato: "PDF",
                    Descargar: true
                })
            }
        },
        mounted(){
            this.ObtenerFechas()
            this.GetPeriodo()
        },
        watch: {
            reporte(value){
                if(value == 0){
                    this.parametroDetalleFacturacion = ''
                    this.parametroIvaDebito = 'display: none'
                    this.parametroIvaDebitoResumen = 'display: none'
                }
                if(value == 1){
                    this.parametroDetalleFacturacion = 'display: none'
                    this.parametroIvaDebito = ''
                    this.parametroIvaDebitoResumen = 'display: none'
                }
                if(value == 2){
                    this.parametroDetalleFacturacion = 'display: none'
                    this.parametroIvaDebito = 'display: none'
                    this.parametroIvaDebitoResumen = ''
                }
            }
        },

    }
</script>
<style lang="scss" scoped>
    .box-custom1{
        box-shadow: 0px 15px 40px -10px;
        padding: 15px;
        width: 25%;
    }
    .box-custom2{
        box-shadow: 0px 15px 40px -10px;
        padding: 15px;
        width: 70%;
    }
    .container{
        border: 1px solid #888;
        width: 50%;
        align-content: center;
    }
    .container-input{
        align-content: center;
    }
    .div-container{
        padding: 5px;
        margin: 10px;
    }
</style>