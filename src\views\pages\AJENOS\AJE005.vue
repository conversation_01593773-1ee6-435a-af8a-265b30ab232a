<template>
<div>
    <vs-prompt title = "Nuevo valor" color="dark" cancel-text="Cancelar" accept-text="Grabar" button-cancel="border" :is-valid="nuevoValorCeroValido" @cancel="infoCargo.nuevoValorCero = null" @accept="Guardar().montoGrupo()" :active.sync="infoCargo.confirmUptValorcero">
        <div class="con-exemple-prompt">
            Ingrese el nuevo valor para los cargos
            <vs-input class="w-full" v-model="nuevoValorCero" type="number" min="0" max="200" :danger="!nuevoValorCeroValido" danger-text="Ingrese un valor mayor que 0, y menor o igual a 200"/>
        </div>
    </vs-prompt>
    <vs-popup ref="buscador" :title="infoCargo.valorCero?'Actualización de Valor a Cargos':'Detalle de Honorarios'" :active.sync="infoCargo.mostrar" style="z-index:52000" id="div-with-loading" class="vs-con-loading__container">
        <vs-table2 v-if="infoCargo.info" max-items="10" search tooltip :exportarExcel="infoCargo.valorCero?null:'Pago de honorarios'" :data="infoCargo.info" height="380px">

            <template slot="header">
                <vs-checkbox v-model="allSelectInfoCargo">Seleccionar todo</vs-checkbox>
            </template>
            <template slot="thead">
                <th width="50px">Selección</th>
                <th order="NombreSucursal" width="150px">Hospital</th>
                <th order="TipoRegistro" width="150px">Tipo Doc.</th>
                <th order="NombreCategoria">Tipo Honorario</th>
                <th order="Admisión" width="120px">Admisión</th>
                <th order="Documento" width="120px">Documento</th>
                <th order="Admision_FechaEgreso" width="150px">Fecha Egreso</th>
                <th order="NombrePaciente">Paciente</th>
                <th order="NombreSeguro">Tipo Paciente</th>
                <th order="Valor" orderType="number" width="150px">Monto</th>
            </template>

            <template slot-scope="{data}">
                <tr :key="indextr" v-for="(tr, indextr) in data">

                    <vs-td2>
                        <!-- <vs-checkbox v-model="tr.seleccion" danger @change="Otros().seleccionCargo(tr)" /> -->
                        <vs-checkbox v-if="!IsCheckable(tr)" danger disabled />
                        <vs-checkbox v-else v-model="tr.seleccion" danger @change="Otros().seleccionCargo(tr)" />
                    </vs-td2>

                    <vs-td2>
                        {{ tr.NombreSucursal.toUpperCase() }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.TipoRegistro }}
                    </vs-td2>

                    <vs-td2>
                        <span v-if="tr.Categoria">({{ tr.Categoria }})</span> {{ tr.NombreCategoria.toUpperCase() }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.SerieAdmision }}{{ tr.Admision }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.Documento }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.Admision_FechaEgreso ? tr.Admision_FechaEgreso: tr.Fecha }}
                    </vs-td2>

                    <vs-td2>
                        {{tr.Cita_Cliente ? tr.NombreCliente : tr.NombrePaciente }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.NombreSeguro }}
                    </vs-td2>

                    <vs-td2 noTooltip>
                        <div style="text-align:right">
                            <span v-if="!tr.Cambiar">{{ $formato_moneda(tr.Valor)}}</span>
                            <vs-input v-else class="w-full" v-model="tr.ValorTemp" type="number" />

                            <!-- {{tr.Cambiar}} -->
                            <div v-if="tr.Tipo == 1 && tr.Es_SeguroSaludSiempre==1 && !infoCargo.valorCero">
                                <div v-if="!tr.Cambiar" style="font-size:11px;border:1px solid #ccc;text-align:center;border-radius:5px;cursor:pointer;padding:1px 5px" @click="tr.Cambiar = true">
                                    Cambiar
                                </div>
                                <div v-else style="font-size:11px;background-color:rgba(var(--vs-success),1);text-align:center;border-radius:5px;cursor:pointer;color:white;padding:1px 5px" @click="Guardar().monto(tr)">
                                    Guardar
                                </div>
                            </div>

                        </div>
                    </vs-td2>

                </tr>
            </template>
        </vs-table2>
        <div class="flex">
            <vs-spacer></vs-spacer>
            <vs-button v-if="infoCargo.valorCero" @click="infoCargo.confirmUptValorcero=true" :disabled="!honorariosValorCero.some(x => x.seleccion)">Actualizar Monto</vs-button>
            <vs-button v-else @click="infoCargo.mostrar=false">Aceptar</vs-button>
        </div>
    </vs-popup>

    <vx-card title="Pago de Honorarios por Hospital">
        <ValidationObserver ref="formValidate" v-slot="{ invalid,handleSubmit }" mode="lazy">
            <div class="flex mb-5 " style="justify-content:space-between;border:1px solid rgba(0,0,0,0.1);padding:5px">
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <SM-Buscar v-model="info.Codigo" label="Código del Médico" api="app/Ajenos/Busqueda_Ajenos" :api_campos="['Codigo','Nombre','Apellido','Corporativo','Tipo']" :api_titulos="['Codigo','Nombre','Apellido','Corporativo','Tipo']" api_campo_respuesta="Codigo" :callback_buscar="Consulta().Consulta_Ajenos" :callback_cancelar="Otros().Limpiar_Datos" :disabled_texto="true" />
                </div>
            </div>
            <div class="flex mb-5 ">
                <div class="w-full md:w-2/3 lg:w-2/3 xl:w-2/3 p-1">
                    <vs-input label="Nombre Médico" class="w-full" v-model="info.Medico" :disabled="true" />
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <vs-input label="NIT" class="w-full" v-model="info.NIT" :disabled="true" />
                </div>
            </div>
            <div class="flex mb-5 ">
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <SM-Buscar label="Tipo de Médico" v-model="info.Tipo" :api="[{Codigo:'C',Nombre:'Cortesía'},{Codigo:'M',Nombre:'Casa'},{Codigo:'CC',Nombre:'Casa-Cortesía'},{Codigo:'R',Nombre:'Referencia'}]" :api_campos="['Nombre']" :api_titulos="['Nombre']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="false" />
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <SM-Buscar v-model="info.Especialidad" label="Especialidad" api="app/Ajenos/Busqueda_Especialidad" :api_cache="true" :api_campos="['Codigo','Descripcion']" :api_titulos="['Codigo','Descripción']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Descripcion" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="false " />
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <SM-Buscar v-model="info.SubEspecialidad" label="SubEspecialidad" api="app/Ajenos/Busqueda_SubEspecialidad" :api_campos="['Codigo','Nombre']" :api_titulos="['Codigo','Nombre']" :api_filtro="{Codigo_Especialidad:info.Especialidad}" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="false" />
                </div>
            </div>
            <vs-divider></vs-divider>
            <div class="flex flex-wrap mb-5 ">
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <ValidationProvider name="TelefonoClinica" v-slot="{ errors }">
                        <vs-input label="Fecha de Última Facturación" type="datetime" style="padding:0" disabled="false" class="w-full" v-model="info.FacturaFecha" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1 mydanger">
                    <SM-Buscar label="Tipo de Régimen" v-model="info.Proveedor_Tipo_Contribuyente" :api="[{Codigo:'P',Nombre:'Pequeño Contribuyente'}]" :api_campos="['Codigo','Nombre']" :api_titulos="['Código','Nombre']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :callback_cancelar="true" :disabled_texto="true" :mostrar_busqueda="false" :disabled_busqueda="true" />
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1 mydanger">
                    <SM-Buscar label="Tipo de Retención" v-model="info.Proveedor_Tipo_Retencion" :api="[{Codigo:'D',Nombre:'Definitiva'},{Codigo:'0',Nombre:'Cero'}]" :api_campos="['Codigo','Nombre']" :api_titulos="['Código','Nombre']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :callback_cancelar="true" :disabled_texto="true" :mostrar_busqueda="false" :disabled_busqueda="true" />
                </div>
            </div>
            <vs-divider></vs-divider>
            <vs-table2 max-items="10" search tooltip pagination :data="pagosVisibles">
                <template slot="header">
                    <div class="flex flex-row">
                        <vs-button color="primary" type="filled" @click.native="Consulta().Consulta_Datos()" icon-pack="feather" icon="icon-refresh-cw" :disabled="info.Codigo==null">
                            Actualizar
                        </vs-button>
                        <vs-button v-if="permisos.editarValorCero" color="dark" type="filled" @click.native="InitUpdGrupo" icon-pack="feather" icon="icon-edit" :disabled="info.Codigo==null || !pagos.some(x=> parseFloat(x.Valor) == 0)" class="ml-2">
                            Valor cero
                        </vs-button>
                    </div>
                </template>

                <template slot="thead">
                    <th width="50px">Selección</th>
                    <th order="NombreSucursal" width="150px">Hospital</th>
                    <th order="TipoRegistro" width="300px">Tipo Doc.</th>
                    <th order="NombreCategoria">Tipo Honorario</th>
                    <!-- <th order="Documento" width="150px">Documento</th>
                    <th order="Admision_FechaEgreso" width="150px">Fecha Salida</th>
                    <th order="NombrePaciente">Paciente</th>
                    <th order="NombreSeguro">Tipo Paciente</th> -->
                    <th orderType="number" width="200px">Monto</th>
                    <th width="50px" orderType="number">Acción</th>
                </template>

                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">

                        <vs-td2>
                            <vs-checkbox v-model="tr.seleccion" indeterminate @change="Otros().seleccionCargo(tr)" />
                        </vs-td2>

                        <vs-td2>
                            {{ tr.NombreSucursal.toUpperCase() }}
                        </vs-td2>

                        <vs-td2>
                            {{ tr.TipoRegistro }}
                        </vs-td2>

                        <vs-td2>
                            <span v-if="tr.Categoria">({{ tr.Categoria }})</span> {{ tr.NombreCategoria.toUpperCase() }}
                        </vs-td2>

                        <vs-td2>
                            <div style="text-align:right">
                                {{ $formato_moneda(Otros().agruparValores(tr.Tipo,tr.Categoria)) }}
                                <div v-if="tr.Accion && Otros().verificarSeleccionParcial(tr.Accion)" style="color:red;font-size:12px">Parcial {{Otros().verificarSeleccionParcial(tr.Accion)}}</div>
                            </div>
                        </vs-td2>

                        <vs-td2 noTooltip>
                            <vs-button v-if="tr.Accion" color="success" size="small" icon-pack="fas" icon="fa-search-plus" class="mr-1" style="display:inline-block" v-on:click="infoCargo.mostrar=true;infoCargo.info = pagosCargos[tr.Accion]"></vs-button>
                        </vs-td2>
                    </tr>
                </template>
            </vs-table2>
            <table width="100%" style="border-spacing: 0;">
                <tr style="background-color:#bdc3c7; color:#34495e">
                    <td style="padding:10px">
                        Total a Facturar
                    </td>
                    <td width="250px" style="padding:10px; text-align:right;font-weight:bold">
                        {{calcular}}
                    </td>
                </tr>
            </table>
            <vs-divider></vs-divider>
            <div class="flex bottom">
                <vs-spacer></vs-spacer>
                <vs-button color="success" style="float:right" type="filled" @click.native="handleSubmit(Otros().facturasMostrar)" :disabled="(invalid || pagos.filter(f=>f.seleccion && f.Valor > 0).length==0)">Ingresar Factura</vs-button>
            </div>
        </ValidationObserver>
    </vx-card>
    <div class="facturas" v-if="info.MostrarFactura">
        <Facturas v-model="info" :cancelar="Otros().facturasCancelar" :tabla="pagos.filter(f=>f.seleccion && f.Valor > 0)" />
    </div>
</div>
</template>

<script>
import bitacora from '@/components/sermesa/funciones/SMBitacora'
export default {
    data() {
        return {
            /**
             * Documentación del módulo
             */
            doc_: [
                'Genera el listado de todos los cargos, interpretaciones, citas y cupones validados por las reglas de pago.',
                'Los honorarios mostrados estan en estado <b>No Pagado</b> y no se encuentran asignados a una factura.',
                'Los honorarios generados se envian a la validación de lotes Cheques o validación lotes depositos dependiendo si el proveedor asignado al médico posee cuenta BI.',
                '* Se obtienen los datos del sp <i>sp_Ajenos_Honorarios_PagosHospital</i>.',
                '** Validar bitacora'
            ],

            /**
             * Editar el monto de la tabla
             */
            info: {
                Codigo: null,
                Medico: null,
                NIT: null,
                Tipo: null,
                Especialidad: null,
                SubEspecialidad: null,
                FacturaFecha: null,
                FacturaSerie: null,
                FacturaNumero: null,
                Total: '0.00',
                MostrarFactura: false,
                Proveedor: null,
                Proveedor_Tipo_Contribuyente: null,
                Proveedor_Tipo_Retencion: null,
            },

            /**
             * Listado de permisos
             */
            permisos: {
                editar: false,
                editarValorCero: false,
            },

            /**
             * Información de los pagos
             */
            pagos: [],
            /** Son los que se muestran agrupados, vista inicial */
            pagosVisibles: [],
            /**Organiza los pagos por categoria de cargos en array */
            pagosCargos: {
                30: null,
                97: null,
                2: null,
                3: null,
                4: null
            },
            pagosSeleccion: [],

            /**
             * Información de los cargos
             */
            infoCargo: {
                mostrar: false,
                info: null,
                valorCero: false, //indica si el detalle se esta utilizando para actualizar con valor cero :)
                nuevoValorCero: 0,
                confirmUptValorcero: false,
            },
        }
    },
    computed: {
        calcular() {
            const arr = this.pagos.filter(f => f.seleccion)
            return this.$formato_moneda(arr.reduce((acumulador, valor) => acumulador + parseFloat(valor.Valor), 0))
        },
        honorariosValorCero() {
            return this.pagos.filter(x => parseFloat(x.Valor) == 0)
        },
        nuevoValorCero: {
            set: function (val) {
                if (val && val.includes('.'))
                    this.infoCargo.nuevoValorCero = parseFloat(val).toFixed(val.split('.')[1].length == 1 ? 1 : 2) //this.$formato_decimal(val,val.split('.').length==1?1:2).;
                else
                    this.infoCargo.nuevoValorCero = parseFloat(val);
            },
            get: function () {
                return this.infoCargo.nuevoValorCero
            }
        },
        allSelectInfoCargo: {
            set: function (val) {
                this.infoCargo.info.forEach(x => {
                    if(this.IsCheckable(x)) 
                        x.seleccion = val
                })
            },
            get: function () {
                return !this.infoCargo.info.some(x => !x.seleccion && this.IsCheckable(x))
            }
        },
        nuevoValorCeroValido() {
            return parseFloat(this.infoCargo.nuevoValorCero)>0 && parseFloat(this.infoCargo.nuevoValorCero) <= 200
        },
    },
    watch: {
        /**Para salir de la configuracion de actualización de valor cero :) */
        'infoCargo.mostrar'(newVal) {
            if (!newVal)
                this.infoCargo.valorCero = false
        },
    },
    components: {
        Facturas: () => import('./AJE005_Factura.vue')
    },
    methods: {
        //=======================================================================================================
        // CONSULTA
        //=======================================================================================================
        Consulta: function () {
            return {
                Consulta_Ajenos: (item) => {
                    this.info.Medico = `${item.Nombre.trim()} ${item.Apellido.trim()}`
                    this.Consulta().Consulta_InfoAjenos(this.info.Codigo)
                        .then(() => {
                            this.Consulta().Consulta_Datos()
                        })
                },

                Consulta_Datos: async () => {
                    return Promise.allSettled([
                        this.Consulta().Consulta_Pagos(this.info.Codigo),
                        this.Consulta().Consulta_UltimaActualizacion(this.info.Codigo),
                        this.Consulta().Consulta_Ajenos_Proveedores(this.info.Proveedor),
                    ]).then(() => {
                        //como ya se cargó todo inicio la bitacora
                        bitacora.registrar(this.info, {
                            Empresa: this.$store.state.sesion.sesion_empresa_unificadora,
                            Codigo: this.info.Codigo,
                            llave: {
                                Empresa: this.$store.state.sesion.sesion_empresa_unificadora,
                                Codigo: this.info.Codigo,
                            },
                            tabla: 'HIS_Honorarios',
                        })
                    })
                },

                Consulta_InfoAjenos: (codigo) => {
                    return this.axios.post('/app/ajenos/Consulta_Ajenos', {
                            Codigo: codigo
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                let datos = resp.data.json[0]

                                // Remplazando el tipo  si se ha seleccionado tipocasacortesia
                                if (datos['TipoCortesiaCasa'] == true) datos['Tipo'] = 'CC'
                                this.info.Tipo = datos.Tipo
                                this.info.Especialidad = datos.EspecialidadPrincipal.trim()
                                this.info.SubEspecialidad = datos.Especialidad.trim()
                                this.info.Proveedor = datos.Proveedor.trim()
                                return datos.Proveedor.trim()
                            }
                        })
                },

                Consulta_Pagos: (Ajeno) => {
                    this.pagos = []
                    this.pagosVisibles = []
                    return this.axios.post('/app/ajenos/Honorarios_Pagos_Consulta', {
                            Ajeno,
                            TipoPlanilla: 'H' // Honorarios
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.pagos = resp.data.json.map(m => {
                                    return {
                                        seleccion: false,
                                        Accion: null,
                                        Cambiar: false,
                                        ...m,
                                        Valor: parseFloat(m.Valor).toFixed(2),
                                        ValorTemp: parseFloat(m.Valor).toFixed(2),
                                    }
                                })
                                this.pagosVisibles = this.Otros().agrupar(this.pagos)
                            }
                        })
                        .catch(() => {})
                },

                Consulta_UltimaActualizacion: (Ajeno) => {
                    return this.axios.post('/app/ajenos/HonorariosConsultaEstados', {
                            Opcion: 'A',
                            Ajeno
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.info.FacturaFecha = resp.data.json[0].Fecha
                            }
                        })
                },
                Consulta_Ajenos_Proveedores: (Codigo) => {
                    if (Codigo)
                        return this.axios.post('/app/ajenos/Consulta_Ajenos_Proveedores', {
                                Codigo_Proveedor: Codigo
                            })
                            .then(resp => {
                                if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                    let datos = resp.data.json[0]

                                    Object.entries(datos).forEach(([key, value]) => {
                                        this.info[key] = (typeof value == 'string') ? value.trim() : value
                                    });
                                }
                            })
                },
            }
        },

        //=======================================================================================================
        // NUEVO
        //=======================================================================================================
        Nuevo: function () {
            return {}
        },

        //=======================================================================================================
        // GUARDAR
        //=======================================================================================================
        Guardar: function () {
            return {
                monto: (item) => {
                    this.axios.post('/app/ajenos/Honorarios_Cambio_Montos', {
                            id: item.Id,
                            valor: item.ValorTemp
                        })
                        .then(() => {
                            item.Valor = item.ValorTemp;
                            item.Cambiar = false
                        })
                },
                montoGrupo: () => {
                    let ids = []
                    this.infoCargo.info.forEach(x=>{ 
                        if(x.seleccion)
                            ids.push(x.Id)
                    })
                    this.axios.post('/app/ajenos/HonorariosCambioMontosGrupo', {
                            Ids: ids,
                            valor: this.nuevoValorCero,
                        })
                        .then((resp) => {
                            // grabnado bitacora de los que se actualizaron

                            let honrarioBitacora = []
                            this.infoCargo.info.map(x => {
                                if (resp.data.honorarios.some(s => s.codigo == 0 && s.IdHonorario == x.Id)) {
                                    x.tipobitacora = 'Tipo'
                                    bitacora.registrar(x, {
                                        Id: x.Id,
                                    })
                                    x.tipobitacora = 'Modificación'
                                    x.Valor = this.nuevoValorCero
                                    honrarioBitacora.push({
                                        llave: {
                                            Id: x.Id,
                                        },
                                        tabla: 'HIS_Honorarios',
                                        info: bitacora.obtener()
                                    })
                                }

                            })

                            this.axios.post('/app/bitacora/registro_lista_bitacora', {
                                Bitacoras: honrarioBitacora
                            }).then()

                            //salimos del modo actualización valor cero y recargamos :)
                            this.infoCargo.mostrar = false
                            this.nuevoValorCero = null
                            this.Consulta().Consulta_Pagos(this.info.Codigo) // ó this.Consulta().Consulta_Datos()
                        })
                }
            }
        },

        //=======================================================================================================
        // EDITAR
        //=======================================================================================================
        Editar: function () {
            return {}
        },

        //=======================================================================================================
        // ELIMINAR
        //=======================================================================================================
        Eliminar: function () {
            return {}
        },

        //=======================================================================================================
        // OTROS
        //=======================================================================================================
        Otros: function () {
            return {
                Limpiar_Datos: () => {
                    Object.entries(this.info).forEach(([key]) => {
                        this.info[key] = null
                    });
                    this.pagosVisibles = []
                    this.pagos = []
                },
                facturasCancelar: (actualizar = false) => {
                    this.info.MostrarFactura = false
                    if (actualizar) this.Consulta().Consulta_Pagos(this.info.Codigo)
                },
                agrupar: (items) => {
                    let respuesta = []

                    // Cargos
                    const cargos = items.filter(f => f.Tipo == "1")
                    const interpretacion = items.filter(f => f.Tipo == "2")
                    const cupones = items.filter(f => f.Tipo == "3")
                    const compromiso = items.filter(f => f.Tipo == "4")

                    const cargos30 = cargos.filter(f => f.Categoria == '30')
                    const cargos97 = cargos.filter(f => f.Categoria == '97')

                    // Funcion para crear el objecto
                    const crearObjeto = (info, tipo) => {
                        return {
                            seleccion: false,
                            Tipo: info.Tipo,
                            NombreSucursal: info.NombreSucursal,
                            TipoRegistro: info.TipoRegistro,
                            Documento: null,
                            Admision_FechaEgreso: null,
                            Cita_Cliente: null,
                            NombreCliente: null,
                            NombrePaciente: null,
                            NombreSeguro: null,
                            Categoria: info.Categoria,
                            NombreCategoria: info.NombreCategoria,
                            Valor: info.Valor,
                            Accion: tipo
                        }
                    }

                    // Agrupando cargos 30
                    if (cargos30.length > 0) {
                        const info = {
                            ...cargos30[0]
                        }
                        const resp = crearObjeto(info, 30)
                        this.pagosCargos["30"] = cargos30
                        respuesta.push(resp)
                    }

                    // Agrupando cargos 97
                    if (cargos97.length > 0) {
                        const info = {
                            ...cargos97[0]
                        }
                        const resp = crearObjeto(info, 97)
                        this.pagosCargos["97"] = cargos97
                        respuesta.push(resp)
                    }

                    // Tipo Interpretación
                    if (interpretacion.length > 0) {
                        const info = {
                            ...interpretacion[0]
                        }
                        const resp = crearObjeto(info, 2)
                        this.pagosCargos["2"] = interpretacion
                        respuesta.push(resp)
                    }

                    // Tipo Cupones
                    if (cupones.length > 0) {
                        const info = {
                            ...cupones[0]
                        }
                        const resp = crearObjeto(info, 3)
                        this.pagosCargos["3"] = cupones
                        respuesta.push(resp)
                    }

                    // Tipo Compromiso
                    if (compromiso.length > 0) {
                        const info = {
                            ...compromiso[0]
                        }
                        const resp = crearObjeto(info, 4)
                        this.pagosCargos["4"] = compromiso
                        respuesta.push(resp)
                    }

                    // Otros grupos
                    // respuesta.push(...items.filter(f => f.Tipo != "1"))

                    return respuesta
                },
                agruparValores: (tipo, categoria) => {
                    let arr
                    if (tipo == 1) arr = this.pagos.filter(f => f.Tipo == tipo && f.Categoria == categoria)
                    if (tipo != 1) arr = this.pagos.filter(f => f.Tipo == tipo)
                    return arr.reduce((acumulador, item) => parseFloat(acumulador) + parseFloat(item.Valor), 0).toLocaleString("es-GT", {
                        style: "decimal",
                        maximumFractionDigits: 4,
                        minimumFractionDigits: 4
                    })
                },
                seleccionCargo: (item) => {
                    if (item.Accion) {
                        this.pagos.filter(f => (f.ValorTemp >0 || f.Valor > 0 ) && ( f.Categoria == item.Accion || f.Tipo == item.Accion )).map(m => m.seleccion = item.seleccion)
                    }
                },
                verificarSeleccionParcial: (tipoCargo) => {
                    // Validando si es seleccion completa o parcial de una categoria de cargo (97 o 30)
                    const arr = this.pagosVisibles.filter(f => (tipoCargo == 97 || tipoCargo == 98) ? f.Tipo == 1 && f.Categoria == tipoCargo : f.Tipo == tipoCargo)
                    const cargos = this.pagos.filter(f => (tipoCargo == 97 || tipoCargo == 98) ? f.Tipo == 1 && f.Categoria == tipoCargo : f.Tipo == tipoCargo)
                    const seleccionados = cargos.filter(f => f.seleccion)
                    const nSeleccionados = cargos.filter(f => !f.seleccion).length
                    if (seleccionados.length > 0 && nSeleccionados > 0) {
                        if (arr.length > 0) {
                            const info = arr[0]
                            if (seleccionados.length > 0 && !info.seleccion) info.seleccion = true
                        }
                        return seleccionados.reduce((acumulador, item) => parseFloat(acumulador) + parseFloat(item.Valor), 0).toLocaleString("es-GT", {
                            style: "decimal",
                            maximumFractionDigits: 2,
                            minimumFractionDigits: 2
                        })
                    } else {

                        if (arr.length > 0) {
                            const info = arr[0]
                            if (seleccionados.length > 0 && !info.seleccion) info.seleccion = true
                            if (nSeleccionados > 0 && info.seleccion) info.seleccion = false
                        }
                        return null
                    }
                },
                facturasMostrar: () => {
                    this.$vs.dialog({
                        type: 'confirm',
                        color: 'dark',
                        title: 'Verificar régimen',
                        text: '¿Ha verificado que el regimen tributario de la factura corresponde al de la ficha del médico?',
                        buttonCancel: 'border',
                        acceptText: 'Si',
                        cancelText: 'No',
                        accept: () => {

                            let postData = {
                                Empresa: this.$store.state.sesion.sesion_empresa_unificadora,
                                Codigo: this.info.Codigo,
                                llave: {
                                    Empresa: this.$store.state.sesion.sesion_empresa_unificadora,
                                    Codigo: this.info.Codigo,
                                },
                                tabla: 'HIS_Honorarios',
                                info: JSON.stringify({
                                    Empresa: this.$store.state.sesion.sesion_empresa_unificadora,
                                    Codigo: this.info.Codigo,
                                    tipobitacora: 'Tipo=>>Confirmación',
                                    descripcion: 'Confirmación régimen factura ficha médica',
                                    ...this.info,
                                    Pagos: this.pagos,
                                })
                            }

                            this.axios.post('/app/bitacora/registro_bitacora', postData)
                                .then(resp => {
                                    if (resp.data.codigo == 0) {
                                        this.info.MostrarFactura = true
                                    }
                                })
                        },
                        cancel: () => {}
                    })

                },
            }
        },
        /**Inicializa la actualizacion de vlores cero por grupo de selección */
        InitUpdGrupo() {
            this.infoCargo.mostrar = true
            this.infoCargo.valorCero = true
            this.infoCargo.info = this.honorariosValorCero
        },
        /**Evalua si el cargo es seleccionable, si se esta editando para valor cero si permite seleccionar solo si se tiene permiso y el valor es cero
        * Para otra seleccion solo se permiten hnonorarios con valor mayor que cero :)
        */
        IsCheckable(honorario) {
            //return !(!(this.permisos.editarValorCero && this.infoCargo.valorCero) && (honorario.Valor == 0) || (honorario.ValorTemp == 0) ) 
            return this.infoCargo.valorCero || (honorario.Valor > 0 && honorario.ValorTemp > 0)
        },
    },
    mounted() {
        this.permisos.editarValorCero = this.$validar_privilegio('EDITAR_VALOR_CERO').status
    },
}
</script>

<style scoped>
.facturas {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100em;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 50px;
    z-index: 99999;
}
</style>

<style>
.mydanger input:disabled {
    font-weight: 900 !important;
    color: #ea5455 !important;
    pointer-events: none;
    border: 1.5px solid #ea5455 !important;
    /* background: green !important; */
}

.mydanger .vs-con-input {
    border-color: #ea5455 !important;
}
</style>