<template>
<div>
    <vs-popup ref="anulacion" :title="`Anulación`" :active.sync="anulacion.mostrar" style="z-index:52000" id="div-with-loading" class="vs-con-loading__container">
        <ValidationObserver ref="formValidate" v-slot="{ invalid,handleSubmit }" mode="lazy">
            <b>Médico:</b> <br>
            {{anulacion.Medico}}
            <br>
            <b>Factura:</b><br>
            {{anulacion.Factura}}
            <hr>
            <br>
            <b>Motivo de anulación:</b>
            <ValidationProvider name="Descripción" rules="required|max:200" v-slot="{ errors }">
                <vs-textarea class="w-full" counter="200" v-model="anulacion.Anulacion_Detalle" />
                <span style="position:relative;top:-18px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
            </ValidationProvider>
            <div class="flex">
                <vs-spacer></vs-spacer>
                <vs-button color="danger" type="filled" @click="handleSubmit(Guardar().anulacion(anulacion.Id))" :disabled="invalid">Anular Factura</vs-button>
            </div>
        </ValidationObserver>
    </vs-popup>

    <vs-popup ref="buscador" :title="`Información Factura`" :active.sync="infoFactura.mostrar" style="z-index:52000" id="div-with-loading" class="vs-con-loading__container">
        <vs-table2 max-items="10" search tooltip pagination :data="infoFactura.detalleFactura">
            <template slot="thead">
                <th order="NombreSucursal" width="150px">Hospital</th>
                <th order="TipoRegistro" width="150px">Tipo Doc.</th>
                <th order="NombreCategoria">Tipo Honorario</th>
                <th order="Documento" width="150px">Documento</th>
                <th order="Admision_FechaEgreso" width="150px">Fecha Egreso</th>
                <th order="NombrePaciente">Paciente</th>
                <th order="NombreSeguro">Tipo Paciente</th>
                <th order="Valor" orderType="number">Monto</th>
            </template>

            <template slot-scope="{data}">
                <tr :key="indextr" v-for="(tr, indextr) in data">
                    <vs-td2>
                        {{ tr.NombreSucursal.toUpperCase() }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.TipoRegistro }}
                    </vs-td2>

                    <vs-td2>
                        <span v-if="tr.Categoria">({{ tr.Categoria }})</span> {{ tr.NombreCategoria.toUpperCase() }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.Documento }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.Admision_FechaEgreso }}
                    </vs-td2>

                    <vs-td2>
                        {{tr.Cita_Cliente ? tr.NombreCliente : tr.NombrePaciente }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.NombreSeguro }}
                    </vs-td2>

                    <vs-td2>
                        <div style="text-align:right">
                            {{ $formato_moneda(tr.Valor) }}
                            <div v-if="tr.Accion && Otros().verificarSeleccionParcial(tr.Accion)" style="color:red;font-size:12px">Parcial {{Otros().verificarSeleccionParcial(tr.Accion)}}</div>
                        </div>
                    </vs-td2>
                </tr>
            </template>
        </vs-table2>
    </vs-popup>

    <vs-popup ref="buscador" title="Edición de Factura" :active.sync="edicion.mostrar" style="z-index:52000" id="div-with-loading" class="vs-con-loading__container">
        <ValidationObserver v-slot="{ invalid,handleSubmit }" mode="lazy">
            <div class="flex mb-5 ">
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <ValidationProvider name="serie" rules="required" v-slot="{ errors }">
                        <vs-input label="Serie Factura Médico:" style="padding:0" class="w-full" v-model="edicion.FacturaSerie" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <ValidationProvider name="factura" rules="required" v-slot="{ errors }">
                        <vs-input label="No. Factura Médico:" type="number" style="padding:0" class="w-full" v-model="edicion.FacturaNumero" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <ValidationProvider name="fecha" rules="required" v-slot="{ errors }">
                        <vs-input label="Fecha Factura Médico:" type="date" style="padding:0" class="w-full" v-model="edicion.FacturaFecha" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                    </ValidationProvider>
                </div>
            </div>
            <div class="flex mb-5 ">
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <b>Valor:</b> {{$formato_moneda(edicion.Valor)}}
                </div>
            </div>
            <vs-divider></vs-divider>
            <samp style="color:red">
                Nota: Se debe definir los rangos de fecha permitidos
            </samp>
            <vs-button style="float:right"  :disabled="invalid" @click.native="handleSubmit(Editar().factura())">Guardar Cambios</vs-button>
            <div style="clear:both"></div>
        </ValidationObserver>
    </vs-popup>

    <vx-card title="Validación De Lotes Depositos">
        <div class="flex mb-5 ">
            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                <vs-input label="Fecha de Última Validación" type="datetime" style="padding:0" disabled="false" class="w-full" v-model="info.ultimaFecha" />
            </div>
            <vs-button color="primary" style="height:37px;margin-top:25px" type="filled" @click.native="Consulta().lotes()">Generar Lotes</vs-button>
        </div>
        <vs-divider></vs-divider>
        <!-- <vs-alert color="danger" v-if="!permisos.editar"><i class="fas fa-lock"></i> No cuenta con los permisos para editar la tabla de pagos</vs-alert> -->
        <vs-table2 max-items="10" search tooltip pagination :data="lotes" exportarExcel="validación depositos">
            <template slot="thead">
                <th width="40px"></th>
                <th order="Hospital" width="130px">Hospital</th>
                <th order="FacturaSerie" width="110px">Serie Fact.</th>
                <th order="FacturaNumero" width="110px">No. Factura</th>
                <th order="FacturaFecha" width="110px">Fecha Fact.</th>
                <th order="Medico">Médico</th>
                <th order="AJE_Tipo" width="130px">Especialidad</th>
                <th order="NombreCategoria" width="120px">Tipo Médico</th>
                <th order="Fecha_Creado" width="160px">Fecha Ingreso</th>
                <th order="Valor" orderType="number" width="140px">Valor Facturación</th>
                <th width="170px">Acción</th>
            </template>

            <template slot-scope="{data}">
                <tr :key="indextr" v-for="(tr, indextr) in data">
                    <vs-td2 noTooltip>
                        <i style="font-size:18px;cursor:pointer" class="fas fa-check-square" v-if="tr.Seleccion" @click="tr.Seleccion=false"></i><i style="font-size:18px;cursor:pointer" class="far fa-square" v-else @click="tr.Seleccion=true"></i>
                    </vs-td2>

                    <vs-td2>
                        {{ tr.Hospital }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.FacturaSerie }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.FacturaNumero }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.FacturaFecha.split(' ')[0] }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.Medico }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.Especialidad }}
                    </vs-td2>

                    <vs-td2>
                        {{ (tr.AJE_Tipo?tr.AJE_Tipo.toUpperCase():null) }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.FechaCreado }}
                    </vs-td2>

                    <vs-td2>
                        <div style="text-align:right">
                            {{ $formato_moneda(tr.Valor) }}
                        </div>
                    </vs-td2>

                    <vs-td2 noTooltip noExcel>
                        <vx-tooltip delay=".5s" text="Información" style="display:inline-block">
                            <vs-button color="#27AE60" size="small" icon-pack="fas" icon="fa-info" class="mr-1" v-on:click.native="Otros().informacion(tr)"></vs-button>
                        </vx-tooltip>
                        <vx-tooltip delay=".5s" text="Edición" style="display:inline-block">
                            <vs-button color="primary" size="small" icon-pack="fas" icon="fa-edit" class="mr-1" v-on:click.native="Editar().facturaMostrar(tr)"></vs-button>
                        </vx-tooltip>
                        <vx-tooltip delay=".5s" text="Anulación de Factura" style="display:inline-block">
                            <vs-button color="danger" size="small" icon-pack="fas" icon="fa-undo-alt" class="mr-1" v-on:click.native="Otros().anular(tr)"></vs-button>
                        </vx-tooltip>
                        <vx-tooltip v-if="tr.DenegadoMotivo" style="display:inline;position:relative;top:5px;left:5px" delay=".5s" :text="tr.DenegadoMotivo">
                            <i class="fas fa-exclamation-circle" style="color:rgb(230, 126, 34);font-size:22px"></i>
                        </vx-tooltip>
                    </vs-td2>
                </tr>
            </template>
        </vs-table2>
        <table width="100%" style="border-spacing: 0;">
            <tr style="background-color:#bdc3c7; color:#34495e">
                <td style="padding:10px">
                    Total Consolidado por Lote
                </td>
                <td width="250px" style="padding:10px; text-align:right;font-weight:bold">
                    {{calculcar}}
                </td>
            </tr>
        </table>
        <vs-divider></vs-divider>
        <vs-button style="float:right"  :disabled="lotes.length==0 || lotes.filter(f=>f.Seleccion==true).length==0" @click.native="Guardar().lote()">Validar Lote</vs-button>
        <div style="clear:both"></div>
    </vx-card>
</div>
</template>

<script>
export default {
    data() {
        return {
            info: {
                ultimaFecha: null,
            },

            edicion: {
                Id: null,
                mostrar: false,
                FacturaSerie: null,
                FacturaNumero: null,
                FacturaFecha: null,
                Valor: null
            },

            /**
             * Detalle de factura
             */
            infoFactura: {
                mostrar: false,
                detalleFactura: []
            },

            /**
             * Listado de permisos
             */
            permisos: {
                editar: false
            },

            /**
             * Información de los pagos
             */
            lotes: [],

            /**
             * Anulación
             */
            anulacion: {
                Id: null,
                mostrar: false,
                Anulacion_Detalle: null,
                Factura: null,
                Medico: null
            }
        }
    },
    computed: {
        calculcar() {
            const arr = this.lotes
            return this.$formato_moneda(arr.filter(f=>f.Seleccion).reduce((acumulador, valor) => acumulador + parseFloat(valor.Valor), 0))
        }
    },
    components: {},
    methods: {
        //=======================================================================================================
        // CONSULTA
        //=======================================================================================================
        Consulta: function() {
            return {
                lotes: () => {
                    this.lotes = []
                    return this.axios.post('/app/ajenos/Honorarios_Facturas_Consulta', {
                            Tipo: 'D'
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.lotes = resp.data.json.map(m => {
                                    return {
                                        ...m,
                                        Seleccion: false
                                    }
                                })
                            }
                        })
                        .catch(() => {
                        })
                },

                detalleLotes: (IdFactura) => {
                    return this.axios.post('/app/ajenos/Honorarios_Facturas_Consulta_Detalle', {
                            IdFactura
                        })
                        .then(resp => {
                            return resp
                        })
                        .catch(() => {
                        })
                },

                Consulta_UltimaActualizacion: () => {
                    return this.axios.post('/app/ajenos/HonorariosConsultaEstados', {
                            Opcion: 'FD'
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.info.ultimaFecha = resp.data.json[0].Fecha
                            }
                        })
                }
            }
        },

        //=======================================================================================================
        // NUEVO
        //=======================================================================================================
        Nuevo: function() {
            return {}
        },

        //=======================================================================================================
        // GUARDAR
        //=======================================================================================================
        Guardar: function() {
            return {
                lote: () => {
                    const arr = this.lotes.filter(f=>f.Seleccion==true)
                    return this.axios.post('/app/ajenos/Honorarios_Lotes_Nuevo', {
                            Tipo: 'D',
                            Facturas: arr.map(m => m.Id)
                        })
                        .then(() => {
                            this.Consulta().lotes()
                            this.Consulta().Consulta_UltimaActualizacion()
                        })
                        .catch(() => {
                        })
                },

                anulacion: () => {
                    return this.axios.post('/app/ajenos/Honorarios_Factura_Anular', this.anulacion)
                        .then(() => {
                            this.Consulta().lotes()
                            this.Consulta().Consulta_UltimaActualizacion()

                            this.anulacion.mostrar = false
                            this.anulacion.Id = null
                            this.anulacion.Anulacion_Detalle = null
                            this.anulacion.Factura = null
                            this.anulacion.Medico = null
                        })
                        .catch(() => {
                        })
                }
            }
        },

        //=======================================================================================================
        // EDITAR
        //=======================================================================================================
        Editar: function() {
            return {
                factura: () => {
                    return this.axios.post('/app/ajenos/Honorarios_Factura_Editar', this.edicion)
                        .then(() => {
                            this.Consulta().lotes()
                            this.Consulta().Consulta_UltimaActualizacion()

                            this.edicion.mostrar = false
                            setTimeout(() => {
                                this.edicion.Id = null
                                this.edicion.FacturaSerie = null
                                this.edicion.FacturaNumero = null
                                this.edicion.Valor = null
                                this.edicion.FacturaFecha = null
                            }, 500)
                        })
                        .catch(() => {
                        })
                },

                facturaMostrar: (item) => {
                    const fecha = item.FacturaFecha.split(' ')[0].split('/')
                    this.edicion.Id = item.Id
                    this.edicion.mostrar = true
                    this.edicion.FacturaSerie = item.FacturaSerie
                    this.edicion.FacturaNumero = item.FacturaNumero
                    this.edicion.Valor = item.Valor
                    this.edicion.FacturaFecha = `${fecha[2]}-${fecha[1].padStart(2,"0")}-${fecha[0].padStart(2,"0")}`
                }
            }
        },

        //=======================================================================================================
        // ELIMINAR
        //=======================================================================================================
        Eliminar: function() {
            return {}
        },

        //=======================================================================================================
        // OTROS
        //=======================================================================================================
        Otros: function() {
            return {
                informacion: (item) => {
                    // console.log(item)
                    this.infoFactura.detalleFactura = []
                    this.infoFactura.mostrar = true
                    this.Consulta().detalleLotes(item.Id)
                        .then(resp => {
                            this.infoFactura.detalleFactura = resp.data.json
                        })
                },

                anular: (item) => {
                    this.anulacion.mostrar = true
                    this.anulacion.Id = item.Id
                    this.anulacion.Factura = item.FacturaSerie + '' + item.FacturaNumero
                    this.anulacion.Medico = item.Medico
                    this.anulacion.Anulacion_Detalle = null
                }
            }
        }
    },
    mounted() {
        this.Consulta().Consulta_UltimaActualizacion()
    }
}
</script>
