<template>
<div>
    <vs-popup :title="tituloHabitacion" :active.sync="isVisible" style="z-index:99998;" class="caj006-popup">
        <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
            <div class="flex flex-wrap-nowrap">
                <div class="default-div">
                    <ValidationProvider name="codigo" rules="required|customRule" class="required" v-slot="{ errors }">
                        <label class="typo_label">Código</label>
                        <vs-input class="codigo d-inline-block" v-model="codigo" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="isModificar" />
                    </ValidationProvider>
                </div>
                <div class="multi-span">
                    <ValidationProvider name="estado" rules="required" class="required" v-slot="{ errors }">

                        <label class="typo_label">Estado</label>
                        <multiselect class="estado" v-model="selectEstado" :options="listadoEstado" placeholder="Seleccione Estado" track-by="Codigo" :show-labels="false" :custom-label="DatosMostrarEstado" :disabled="isModificar && !permisoEdicion"></multiselect>
                        <span class="text-danger" style="position:relative;top:-2px" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                    </ValidationProvider>
                </div>
                <div class="multi-span">
                    <ValidationProvider name="tipoHabitacion" rules="required" class="required" v-slot="{ errors }">

                        <label class="typo_label">Tipo Habitación</label>
                        <multiselect class="estado" v-model="selectTipoHabitacion" :options="listadoTipoHabitacion" placeholder="Seleccione Tipo Habitación" track-by="Codigo" :show-labels="false" :custom-label="DatosMostrarTipoHabitacion" :disabled="isModificar && !permisoEdicion" />
                        <span class="text-danger required font-semibold" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                    </ValidationProvider>
                </div>
            </div>
            <div class="flex flex-wrap-nowrap">
                <div class="multi-span">
                    <ValidationProvider name="area" rules="required" class="required" v-slot="{ errors }">

                        <label class="typo_label">Área</label>
                        <multiselect class="servicio" v-model="selectArea" :options="listadoArea" placeholder="Seleccione Área" track-by="Codigo" :show-labels="false" :custom-label="DatosMostrarAreas" :disabled="isModificar && !permisoEdicion"></multiselect>
                        <span class="text-danger required font-semibold" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                        <span v-if="selectArea" class="font-semibold" >{{ `Área ${selectArea.Cargable? '':'NO'} cargable`}}</span>
                    </ValidationProvider>
                </div>
                <div class="default-div input-number">
                    <ValidationProvider name="servicio" :rules="validationRuleCargable" :class="validationRuleCargable" v-slot="{ errors }">
                        <label class="typo_label">Nivel Precio</label>
                        <vs-input-number label="Nivel Precio" color="warning" max="9" class="d-inline-block" v-model="nivelPrecio" :is-disabled="isModificar && !permisoEdicion || !validationRuleCargable" :disabled="isModificar && !permisoEdicion || !validationRuleCargable"/>
                        <span class="text-danger required font-semibold" v-show="errors.length>0" v-html="errors.join('asdadad <br>')"></span>
                    </ValidationProvider>
                </div>
                <div class="default-div input-number">
                    <label class="typo_label">Extensión</label>
                    <vs-input-number label="Extensión" color="warning" class="d-inline-block" v-model="extension" :is-disabled="isModificar && !permisoEdicion" :disabled="isModificar && !permisoEdicion" />
                </div>
            </div>
            <div class="flex flex-wrap-nowrap">
                <div class="multi-span">
                    <ValidationProvider name="servicio" :rules="validationRuleCargable" :class="validationRuleCargable" v-slot="{ errors }">

                        <label class="typo_label">Servicio</label>
                        <multiselect class="servicio" v-model="selectServicio" :options="listadoServicio" placeholder="Seleccione Servicio" track-by="Codigo" :show-labels="false" :custom-label="DatosMostrarServicio" :disabled="isModificar && !permisoEdicion || !validationRuleCargable" />
                        <span class="text-danger required font-semibold" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                    </ValidationProvider>
                </div>
                <div class="multi-span">
                    <ValidationProvider name="comboAutomatico" :rules="validationRuleCargable" :class="[validationRuleCargable]" v-slot="{ errors }">

                        <label class="typo_label">Combo Automático</label>
                        <multiselect class="combo" v-model="selectComboAutomatico" :options="listadoComboAutomatico" placeholder="Seleccione Combo" track-by="Codigo" :show-labels="false" :custom-label="DatosMostrarServicio" :disabled="isModificar && !permisoEdicion || !validationRuleCargable" />
                        <span class="text-danger required font-semibold" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                    </ValidationProvider>
                </div>
            </div>
            <div class="flex flex-wrap-nowrap">
                <div class="multi-span">
                    <ValidationProvider name="base" rules="required" class="required" v-slot="{ errors }">

                        <label class="typo_label">Base</label>
                        <multiselect class="base" v-model="selectBase" :options="listadoBase" placeholder="Seleccione Base" track-by="Codigo" :show-labels="false" :custom-label="DatosMostrarBase" :disabled="isModificar && !permisoEdicion" />
                        <span class="text-danger required font-semibold" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                    </ValidationProvider>
                </div>
                <div class="multi-span">
                    <ValidationProvider name="empresa" rules="required" class="required" v-slot="{ errors }">

                        <label class="typo_label">Empresa SERMESA</label>
                        <multiselect class="empresa" v-model="selectEmpresa" :options="listadoEmpresa" placeholder="Seleccione Empresa" track-by="Codigo" :show-labels="false" :custom-label="DatosMostrarEmpresa" :disabled="isModificar && !permisoEdicion" />
                        <span class="text-danger required font-semibold" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                    </ValidationProvider>
                </div>
            </div>

            <!------------------------------------------ NUEVOS CAMPOS ------------------------------------------>
            <div v-if="isModificar">
                <vs-divider border-style="solid"/>
                <div class="flex flex-wrap-nowrap">
                    <div class="multi-span">
                        <ValidationProvider name="deshabilitado" rules="required" class="required" v-slot="{ errors }">
                            <label class="typo_label">Deshabilitada</label>
                            <multiselect class="deshabilitada" v-model="selectDeshabilitada" :options="listadoDeshabilitada" placeholder="Seleccione Deshabilitada" track-by="Codigo" :show-labels="false" :custom-label="DatosMostrarDeshabilitado" :disabled="isModificar && !permisoEdicionHabilitar" />
                            <span class="text-danger required font-semibold" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                        </ValidationProvider>
                    </div>
                    <div class="multi-span">
                        <ValidationProvider name="departamento" :rules="validationRuleDepto" :class="[validationRuleDepto]" v-slot="{ errors }">

                            <label class="typo_label">Departamento</label>
                            <multiselect class="estado" v-model="selectDepartamento" :options="listadoDepartamento" placeholder="Seleccione departamento" :show-labels="false" :custom-label="DatosMostrarDepartamento" :disabled="isModificar && !permisoEdicionHabilitar" />
                            <span class="text-danger required font-semibold" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                        </ValidationProvider>
                    </div>
                    <div class="multi-span">
                        <ValidationProvider name="motivo" :rules="validationRuleMotivo" :class="[validationRuleMotivo]" v-slot="{ errors }">

                            <label class="typo_label">Motivo</label>
                            <multiselect class="motivo" v-model="selectMotivo" :options="listadoMotivo" placeholder="Seleccione motivo" :show-labels="false" :custom-label="DatosMostrarMotivo" :disabled="isModificar && !permisoEdicionHabilitar" />
                            <span class="text-danger required font-semibold" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                        </ValidationProvider>
                    </div>
                </div>
                <div class="flex flex-wrap-nowrap">
                    <div class="default-div">
                        <ValidationProvider name="descripcion" :rules="validationRuleDescripcion" v-slot="{ errors }">
                            <label class="typo_label">Descripción</label>
                            <vs-textarea class="descripcion mb-0" v-model="descripcion" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="isModificar && !permisoEdicionHabilitar"/>
                            <span  class="text-danger required font-semibold" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                        </ValidationProvider>
                    </div>
                </div>
            </div>

            <div class="flex flex-wrap div-container">
                <div>
                    <vs-button @click="handleSubmit(GuardarHabitacion)" :disabled="invalid" color="success" class="btn btn-primary mx-3 my-3" icon-pack="fas" icon="fa-save">
                        Guardar
                    </vs-button>
                </div>
            </div>
        </ValidationObserver>
    </vs-popup>
    <vx-card title="Habitaciones">
        <div>
            <vs-table2 max-items="10" tooltip search filter pagination :data="tablaHabitaciones" noSelectText>
                <template slot="header">
                    <div class="flex flex-row gap-1">
                        <vs-button v-if="permisoNuevo" @click="NuevaHabitacion" color="success" icon-pack="fas" icon="fa-plus-circle">Nueva Habitación</vs-button>
                        <REPORT001 class="reporte-caj006" ref="reporte" :mostrarBuscador="false" :filtroNombres = "['HistoricoHabitaciones','HistoricoHabitacionesDia','HistoricoHabitacionesDiaConsolidado']">
                            <template v-slot:default="data">
                                <div>
                                    <vs-dropdown >
                                        <vs-button class="btn-drop" type="flat" icon-pack="fas" icon="fa-table">
                                            <vs-tooltip text="Editar campos de la remesa">
                                                Reportes
                                            </vs-tooltip>
                                        </vs-button>
                                                            
                                        <vs-dropdown-menu width="300px">
                                            <vs-dropdown-item v-for="(rep, index) in TreeToList(data.data)" :key="index" @click="data.data.generar(rep.Nombre,rep.UrlApi,rep._level)">
                                                {{rep.Nombre}}
                                            </vs-dropdown-item>
                                        </vs-dropdown-menu>
                                    </vs-dropdown>
                                </div>
                            </template>
                        </REPORT001>
                    </div>
                </template>
                <template slot="thead">
                    <th width="80px" filtro="Deshabilitada">Deshabilitada</th>
                    <th filtro="Codigo">Código</th>
                    <th filtro="Status">St.</th>
                    <th filtro="Area">Area</th>
                    <th filtro="Nombre" width="280px">Nombre del Area</th>
                    <th filtro="NivelPrecios" width="20px">N. Precio</th>
                    <th width="80px">Extensión</th>
                    <th filtro="Servicio" width="80px">Servicio</th>
                    <th>Descripción</th>
                    <th filtro="ComboNumero" width="80px">Código Combo</th>
                    <th filtro="NombreCombo">Combo Automático</th>
                    <th>Base</th>
                    <th>Emp. SERMESA</th>
                    <th filtro="NombreTipoHabitacion">Nombre Tipo Hab.</th>
                    <th width="50px">Editar</th>
                </template>
                <template slot-scope="{data}">
                    <tr :style="getStyle(`${tr.Deshabilitada}`, `${tr.Status}`)" :key="indextr" v-for="(tr, indextr) in data" :data="tr">
                        <vs-td2>{{ tr.Deshabilitada }}</vs-td2>
                        <vs-td2>{{ tr.Codigo }}</vs-td2>
                        <vs-td2>{{ tr.Status }}</vs-td2>
                        <vs-td2>{{ tr.Area }}</vs-td2>
                        <vs-td2 width="300px">{{ tr.Nombre }}</vs-td2>
                        <vs-td2>{{ tr.NivelPrecios }}</vs-td2>
                        <vs-td2>{{ tr.Extension }}</vs-td2>
                        <vs-td2>{{ tr.Servicio }}</vs-td2>
                        <vs-td2>{{ tr.NombreServicio }}</vs-td2>
                        <vs-td2>{{ tr.ComboNumero }}</vs-td2>
                        <vs-td2>{{ tr.NombreCombo }}</vs-td2>
                        <vs-td2>{{ tr.NombreCortoBase }}</vs-td2>
                        <vs-td2>{{ tr.NombreCortoReal }}</vs-td2>
                        <vs-td2>{{ tr.NombreTipoHabitacion }}</vs-td2>
                        <vs-td2 class="th_editar" noTooltip>
                            <vs-button v-if="permisoEdicion || permisoEdicionHabilitar" @click="EditarHabitacion(tr)" color="primary" size="small" icon-pack="fas" icon="fa-edit" class="btn_editar mr-1" />
                            <vs-button v-if="permisoEliminar" @click="EliminarHabitacion(tr)" color="danger" size="small" icon-pack="fas" icon="fa-trash-alt" class="btn_editar mr-1" />
                        </vs-td2>
                    </tr>
                </template>
            </vs-table2>
        </div>
    </vx-card>
</div>
</template>

<script>
import Multiselect from "vue-multiselect"
import "vue-multiselect/dist/vue-multiselect.min.css"
import {
    extend
} from "vee-validate"
import REPORT001 from "../REPORTE/REPORT001.vue"


export default {
    data() {
        return {
            isVisiblep: false,
            tituloHabitacion: '',
            isVisible: false,
            listadoDeshabilitada: [{
                    Codigo: '',
                    Nombre: 'Utilizable'
                },
                {
                    Codigo: 'X',
                    Nombre: 'No Utilizable'
                }
            ],
            selectDeshabilitada: '',
            codigo: '',
            listadoEstado: [{
                    Codigo: 'A',
                    Nombre: 'Ocupada'
                },
                {
                    Codigo: 'D',
                    Nombre: 'Desocupada'
                }
            ],
            selectEstado: '',
            listadoArea: [],
            selectArea: '',
            nivelPrecio: '',
            extension: '',
            listadoServicio: [],
            selectServicio: '',
            listadoComboAutomatico: [],
            selectComboAutomatico: '',
            listadoBase: [],
            selectBase: '',
            // codigoBase: '',
            // nombreBase: '',
            listadoEmpresa: [],
            selectEmpresa: '',
            listadoTipoHabitacion: [],
            selectTipoHabitacion: '',
            tablaHabitaciones: [],
            isModificar: false,
            codigoHabitacion: '',

            selectDepartamento: '',
            listadoDepartamento: [],
            selectMotivo: '',
            listadoMotivo: [],
            
            descripcion: '',
            permisoEdicion: null,
            permisoEdicionHabilitar: null,
            permisoNuevo: null,
            permisoEliminar: null,
            //para evaluar los cambios realizados
            respaldofila: null,
            Corporativo: null
        }
    },
    components: {
        Multiselect,
        REPORT001,
    },
    methods: {
        ObtenerHabitaciones() {
            this.axios.post('/app/v1_JefeCaja/ObtenerHabitaciones', {
                    "Opcion": "C",
                    "SubOpcion": "1",
                    "Hospital": "0"
                })
                .then(resp => {
                    this.tablaHabitaciones = resp.data.json.map(m => {
                        return {
                            ...m
                        }
                    })
                })
        },
        ObtenerComboAutomatico() {
            this.axios.post('/app/v1_JefeCaja/ObtenerComboAutomatico', {
                    "Opcion": "C",
                    "SubOpcion": "2",
                    "Hospital": "0"
                })
                .then(resp => {
                    this.listadoComboAutomatico = resp.data.json.map(m => {
                        return {
                            Nombre: m.NombreCombo,
                            ...m
                        }
                    })
                })
        },
        DatosMostrarDeshabilitado({
            Nombre
        }) {
            return `${Nombre}`
        },
        DatosMostrarEstado({
            Nombre
        }) {
            return `${Nombre}`
        },
        DatosMostrarDepartamento({
            Nombre
        }) {
            return `${Nombre}`
        },
        DatosMostrarMotivo({
            Nombre
        }) {
            return `${Nombre}`
        },
        ObtenerAreas() {
            this.axios.post('/app/v1_JefeCaja/ObtenerAreas', {
                    "Opcion": "C",
                    "SubOpcion": "1",
                    "Hospital": "0"
                })
                .then(resp => {
                    this.listadoArea = resp.data.json.map(m => {
                        return {
                            ...m
                        }
                    })
                })
        },
        ObtenerMotivoInhabilita() {
            this.axios.post('/app/v1_JefeCaja/MotivoInhabilitaHabitacion', {

                })
                .then(resp => {
                    this.listadoMotivo = resp.data.json
                })
        },
        ObtenerDeptosMantHabitaciones() {
            this.axios.post('/app/v1_JefeCaja/DeptosMantHabitaciones', {

                })
                .then(resp => {
                    this.listadoDepartamento = resp.data.json
                })
        },

        DatosMostrarAreas({
            Codigo,
            Nombre
        }) {
            return `${Codigo} - ${Nombre}`
        },
        ObtenerServicios() {
            this.axios.post('/app/v1_JefeCaja/ObtenerHabitacionServicios', {
                    "Opcion": "C",
                    "SubOpcion": "4",
                    "Hospital": "0"
                })
                .then(resp => {
                    this.listadoServicio = resp.data.json.map(m => {
                        return {
                            ...m
                        }
                    })
                })
        },
        DatosMostrarServicio({
            Codigo,
            Nombre
        }) {
            return `${Codigo} - ${Nombre}`
        },
        ObtenerHabitacionNombreBase() {
            this.axios.post('/app/v1_JefeCaja/ObtenerHabitacionNombreBase', {
                    "Opcion": "C",
                    "SubOpcion": "5",
                    "Hospital": "0"
                })
                .then(resp => {
                    this.listadoBase = resp.data.json.map(m => {
                        this.codigoHabitacion = m.CodigoHab
                        return {
                            ...m
                        }
                    })
                })
        },
        DatosMostrarBase({
            Codigo,
            Nombre
        }) {
            return `${Codigo} - ${Nombre}`
        },
        ObtenerHabitacionEmpresa() {
            this.axios.post('/app/v1_JefeCaja/ObtenerHabitacionEmpresa', {
                    "Opcion": "C",
                    "SubOpcion": "6",
                    "Hospital": "0"
                })
                .then(resp => {
                    this.listadoEmpresa = resp.data.json.map(m => {
                        return {
                            ...m
                        }
                    })
                })
        },
        DatosMostrarEmpresa({
            Codigo,
            Nombre
        }) {
            return `${Codigo} - ${Nombre}`
        },
        ObtenerTipoHabitacion() {
            this.axios.post('/app/v1_JefeCaja/ObtenerTipoHabitacion', {
                    "Opcion": "C",
                    "SubOpcion": "7",
                    "Hospital": "0"
                })
                .then(resp => {
                    this.listadoTipoHabitacion = resp.data.json.map(m => {
                        return {
                            ...m
                        }
                    })
                })
        },
        DatosMostrarTipoHabitacion({
            Codigo,
            Nombre
        }) {
            return `${Codigo} - ${Nombre}`
        },
        NuevaHabitacion() {
            this.tituloHabitacion = 'Nueva Habitación'
            this.ObtenerAreas()
            this.ObtenerServicios()
            this.ObtenerComboAutomatico()
            this.ObtenerHabitacionNombreBase()
            this.ObtenerHabitacionEmpresa()
            this.ObtenerTipoHabitacion()
            this.validaHabitacion()
            this.isVisible = true
            this.isModificar = false
            this.selectDeshabilitada = {
                Codigo: '',
                Nombre: 'Utilizable'
            },
            this.selectDepartamento = null
        },
        EditarHabitacion(item) {
            this.ObtenerAreas()
            this.ObtenerServicios()
            this.ObtenerComboAutomatico()
            this.ObtenerHabitacionNombreBase()
            this.ObtenerHabitacionEmpresa()
            this.ObtenerTipoHabitacion()
            this.validaHabitacion()
            this.tituloHabitacion = 'Modificar Habitación'
            this.isModificar = true
            this.isVisible = true
            this.selectDeshabilitada = {
                Codigo: item.Deshabilitada,
                Nombre: item.Deshabilitada == 'X' ? 'No Utilizable' : 'Utilizable'
            }
            this.codigo = item.Codigo
            this.selectEstado = {
                Codigo: item.Status,
                Nombre: item.Status == 'A' ? 'Ocupada' : 'Desocupada'
            }
            this.selectArea = {
                Codigo: item.Area,
                Nombre: item.Nombre,
                Cargable: item.Cargable,
            }

            this.nivelPrecio = item.NivelPrecios
            this.extension = item.Extension
            this.selectServicio = {
                Codigo: item.Servicio,
                Nombre: item.NombreServicio
            }
            this.selectComboAutomatico = {
                Codigo: item.ComboNumero,
                Nombre: item.NombreCombo
            }
            this.selectBase = {
                Codigo: item.Base,
                Nombre: item.NombreCortoBase
            }
            this.selectTipoHabitacion = {
                Codigo: item.TipoHabitacion,
                Nombre: item.NombreTipoHabitacion
            }
            this.selectEmpresa = {
                Codigo: item.EmpresaReal,
                Nombre: item.NombreCortoReal
            }
            this.respaldofila = item
        },
        GuardarHabitacion() {
            if (this.isModificar) {
                this.axios.post('/app/v1_JefeCaja/ModificarHabitacion', {
                        "Opcion": "A",
                        "SubOpcion": "1",
                        "Hospital": "0",
                        "codigo": this.codigo,
                        "producto": this.selectServicio?.Codigo,
                        "status": this.selectEstado.Codigo,
                        "area": this.selectArea.Codigo,
                        "extension": this.extension,
                        "nivelPrecios": this.nivelPrecio,
                        "comboNumero": this.selectComboAutomatico?.Codigo,
                        "deshabilitada": this.selectDeshabilitada.Codigo,
                        "tipoHabitacion": this.selectTipoHabitacion.Codigo,
                        "departamento": this.selectDepartamento?.Codigo,
                        "motivo": this.selectMotivo?.Codigo,
                        "descripcion": this.descripcion,
                        "corporativo": this.Corporativo
                    })
                    .then(resp => {
                        this.codigo = null
                        this.selectServicio = null
                        this.selectEstado = null
                        this.selectArea = null
                        this.extension = null
                        this.nivelPrecio = null
                        this.selectComboAutomatico = null
                        this.selectDeshabilitada = null
                        this.selectTipoHabitacion = null
                        this.selectBase = null
                        this.selectEmpresa = null

                        this.selectDepartamento = null
                        this.selectMotivo = null
                        this.descripcion = ''

                        this.ObtenerHabitaciones()
                        this.isVisible = false
                        this.$vs.notify({
                            color: 'success',
                            title: 'Habitación',
                            text: resp.data.json[0].descripcion
                        })
                    })
            } else {
                this.axios.post('/app/v1_JefeCaja/AlmacenarHabitacion', {
                        "Opcion": "I",
                        "SubOpcion": "1",
                        "Hospital": "0",
                        "codigo": this.codigo,
                        "producto": this.selectServicio?.Codigo,
                        "status": this.selectEstado.Codigo,
                        "area": this.selectArea.Codigo,
                        "extension": this.extension,
                        "nivelPrecios": this.nivelPrecio,
                        "comboNumero": this.selectComboAutomatico?.Codigo,
                        "deshabilitada": this.selectDeshabilitada?.Codigo,
                        "tipoHabitacion": this.selectTipoHabitacion.Codigo
                    })
                    .then(resp => {
                        if (resp.data.json[0].tipo_error == '1') {
                            this.$vs.dialog({
                                color: 'danger',
                                title: 'Habitaciones',
                                text: resp.data.json[0].descripcion,
                                acceptText: 'Aceptar'
                            })
                        } else {
                            this.codigo = null
                            this.selectServicio = null
                            this.selectEstado = null
                            this.selectArea = null
                            this.extension = null
                            this.nivelPrecio = null
                            this.selectComboAutomatico = null
                            this.selectDeshabilitada = null
                            this.selectTipoHabitacion = null
                            this.selectBase = null
                            this.selectEmpresa = null
                            this.ObtenerHabitaciones()
                            this.isVisible = false
                            this.$vs.notify({
                                color: 'success',
                                title: 'Habitación',
                                text: resp.data.json[0].descripcion
                            })
                        }
                    })
            }

        },
        EliminarHabitacion(item) {
            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Confirmacion',
                text: `¿Desea eleminar la habitación: ${ item.Codigo }?`,
                acceptText: 'Aceptar',
                accept: () => {
                    this.axios.post('/app/v1_JefeCaja/EliminarHabitacion', {
                            "Opcion": "E",
                            "SubOpcion": "1",
                            "Hospital": "0",
                            "codigo": item.Codigo
                        })
                        .then(resp => {
                            this.$vs.notify({
                                color: 'success',
                                title: 'Habitación',
                                text: resp.data.json[0].descripcion
                            })
                            this.ObtenerHabitaciones()
                        })
                }
            })

        },
        getStyle(item1, item2) {
            return item1 == 'X' ? 'background-color: #E74C3C; opacity: 0.7; color: white;' :
                item2 == 'A' ? 'background-color: #27AE60; opacity: 0.7; color: white;' : 'background-color: white;'
        },
        validaHabitacion() {
            let errorMessage = ''
            extend("customRule", {
                message: () => errorMessage,
                validate: value => {
                    if (value.startsWith(this.codigoHabitacion)) {
                        return true
                    } else {
                        errorMessage = `El Código debe inciar con ${ this.codigoHabitacion }`
                        return false
                    }
                }
            })
        },
        TreeToList(data){
            return this.$tree_to_list({_level:data.listado}, hoja=>hoja, (x) => (x.IdReporte)?null:x._level)
        },
    },
    activated() {
        this.ObtenerHabitaciones()
        this.ObtenerMotivoInhabilita()
        this.ObtenerDeptosMantHabitaciones() 
    },
    mounted() {
        this.permisoEdicion = this.$validar_privilegio('EDTIARGENRALES').status
        this.permisoEdicionHabilitar = this.$validar_privilegio('EDITAREXTRA').status
        this.permisoNuevo = this.$validar_privilegio('NUEVO').status
        this.permisoEliminar = this.$validar_privilegio('ELIMINAR').status,
        this.Corporativo = this.sesion.corporativo
    },
    watch: {
        isVisible(valor) {
            if (!valor) {
                this.codigo = null
                this.selectServicio = null
                this.selectEstado = null
                this.selectArea = null
                this.extension = null
                this.nivelPrecio = null
                this.selectComboAutomatico = null
                this.selectDeshabilitada = null
                this.selectTipoHabitacion = null
                this.selectBase = null
                this.selectEmpresa = null

                this.selectDepartamento = null
                this.selectMotivo = null
                this.descripcion = ''
            }
        },
        selectArea(value){
            if (value?.Codigo && !value.Cargable){
                this.selectComboAutomatico = null
                this.selectServicio = null
                this.nivelPrecio = null
            }
        },
    },
    computed: {
        sesion() {
            return this.$store.state.sesion
        },
        validationRuleDescripcion(){
            return this.selectMotivo?.RequiereObservacion? 'required|max:500': 'max:500'
        },
        validationRuleDepto() {
            return (this.selectDeshabilitada?.Codigo != this.respaldofila?.Deshabilitada) ? 'required': ''
        },
        validationRuleMotivo() {
            return (this.selectDeshabilitada?.Codigo == 'X' && this.respaldofila?.Deshabilitada != 'X') ? 'required': ''
        },
        validationRuleCargable() {
            return this.selectArea?.Cargable ? 'required': ''
        }
    }
}
</script>

<style lang="scss" scoped>
.div-container {
    padding: 5px;
    position: sticky;
    bottom: -10px;
    background-color: aliceblue;
    width: 100%;
}

.default-div {
    padding-left: 5px;
    padding-right: 5px;

    .codigo {
        margin-top: 5px;
    }

    .input-number {
        margin-top: 20px;
        width: 200px;
    }

    .descripcion {
        margin-top: 5px;
        width: 750px;
    }

}

.multi-span {
    padding: 5px;
    // width: 30%;

    .deshabilitada {
        width: 240px;
    }

    .estado {
        width: 250px;
    }

    .departamento {
        width: 240px;
    }

    .motivo {
        width: 240px;
    }

    .area {
        width: 250px;
    }

    .servicio {
        width: 460px;
    }

    .combo {
        width: 440px;
    }

    .base {
        width: 250px;
    }

    .empresa {
        width: 250px;
    }
}

.th_editar {
    text-align: center;
    min-width: 110px;
    .btn_editar {
        display: inline-block;
    }
}
</style>

<style>
.caj006-popup.con-vs-popup .vs-popup {
    min-width: 450px !important;
    max-width: fit-content !important;
    width: fit-content !important;
    min-height: 500px !important;
}
.con-vs-dialog {
    z-index: 99999;
}

.vx-card.report001-container.reporte-caj006{
    width: auto !important;
    height: min-content;
}
.reporte-caj006 .vx-card__header{
    display: none;
}
.reporte-caj006 .vx-card__body {
    padding: 0px !important;
}
</style>
