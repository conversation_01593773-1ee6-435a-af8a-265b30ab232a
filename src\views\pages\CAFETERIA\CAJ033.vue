<template>
    <div>
        <vx-card title="Consulta de Cargos Anulados">
            <vs-divider border-style="solid" color="dark"></vs-divider>
            <div class="box-custom">
                <vs-row vs-type="flex" vs-w="12">
                    <vs-col vs-w="2">
                        <div class="div-input flex-wrap-nowrap">
                            <label class="label-static">Número de Anulación</label>
                            <vs-input type="number" class="w-full input-serie" v-model="correlativoAnulacion" ></vs-input>
                        </div>
                    </vs-col>
                </vs-row>
                <vs-row vs-type="flex" vs-w="12">
                    <vs-col vs-w="2">
                        <div class="div-input flex-wrap-nowrap">
                            <label class="label-static">Tipo</label>
                            <vs-input class="w-full input-serie" ref="tipo" v-model="tipoOrden" v-on:blur="BuscarTipoOrden()" ></vs-input>
                        </div>
                    </vs-col>
                    <vs-col vs-w="1">
                        <div class="button_search flex-wrap-nowrap">
                            <input class="w-full label-info" v-model="descripcionTipo" disabled/>
                        </div>
                    </vs-col>
                </vs-row>
                <vs-row vs-type="flex" vs-w="12">
                    <vs-col vs-w="2">
                        <div class="div-input flex-wrap-nowrap">
                            <label class="label-static">Número</label>
                            <vs-input type="number" class="w-full input-numero" v-model="numeroOrden" ></vs-input>
                        </div>
                    </vs-col>
                    <vs-col vs-w="1">
                        <div class="button_search flex-wrap-nowrap">
                            <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="BuscarOrden()" icon="icon-search">Buscar</vs-button>
                        </div>
                    </vs-col>
                </vs-row>
                <vs-row vs-justify="left" vs-w="12">
                    <vs-col type="flex" vs-justify="left" vs-align="left" vs-w="2">
                        <div class="div-input flex-wrap-nowrap">
                            <label class="label-static">Admisión</label>
                            <input class="w-full label-info" v-model="admision" disabled/>
                        </div>
                    </vs-col>
                </vs-row>
                <vs-row vs-justify="left" vs-w="12">
                    <vs-col type="flex" vs-justify="left" vs-align="left" vs-w="2">
                        <div class="div-input flex-wrap-nowrap">
                            <label class="label-static">Fecha de Anulación</label>
                            <input class="w-full label-info" v-model="fecha" disabled/>
                        </div>
                    </vs-col>
                </vs-row>
                <vs-row vs-justify="left" vs-w="12">
                    <vs-col type="flex" vs-justify="left" vs-align="left" vs-w="6">
                        <div class="div-input flex-wrap-nowrap">
                            <label class="label-static">Razón</label>
                            <input class="w-full label-info" v-model="razon" disabled/>
                        </div>
                    </vs-col>
                </vs-row>
                <br>
                <!-- <vx-card>
                    <vs-row vs-justify="center" vs-w="12">
                        <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="12">
                            <div class="div-input flex-wrap-nowrap">
                                <label class="label-static1">Observaciones Examenes</label>
                            </div>
                        </vs-col>
                    </vs-row>
                    <vs-row  vs-type="flex" vs-w="12">
                        <vs-col vs-type="flex-wrap" vs-justify="center" vs-align="center" vs-w="12">
                            <div class="div-input">
                                <vs-textarea rows="10" v-model="observaciones"  class="w-full textEstado" disabled/>
                            </div>
                        </vs-col>
                    </vs-row>
                </vx-card> -->
                <vx-card title="Detalle">
                    <div class="flex flex-wrap">
                        <div class="w-full  p-2">
                            <vs-table2 max-items="10" search pagination :data="listaOrdenes" class="mt-0 mb-0">
                                <template slot="thead">
                                    <th order="Correlativo" width="50px">Correlativo</th>
                                    <th order="TipoOrden" width="100px">Tipo Orden</th>
                                    <th order="Orden" width="50px">Orden</th>
                                    <th order="Categoria" width="100px">Categoría</th>
                                    <th order="Producto" width="250px">Producto</th>
                                    <th order="Cantidad" >Cantidad</th>
                                    <th order="Valor" width="150px">Venta Total</th>
                                    <th order="Costo" width="150px">Costo Unitario Bod</th>
                                    <th order="CostoTotal" width="150px">Costo Total Bod</th>
                                    <th order="Usuario" width="150px">Usuario</th>
                                    <th order="Ajeno" width="150px">Ajeno</th>
                                </template>
                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td2>
                                            {{ tr.Correlativo }}                               
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.TipoOrden}}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.Orden}}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.Categoria}}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.Producto}}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.Cantidad}}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.Valor}}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.Costo}}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.CostoTotal}}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.Usuario}}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.Ajeno}}
                                        </vs-td2>
                                    </tr>
                                </template>
                            </vs-table2>
                        </div>
                    </div>
                </vx-card>
                <vx-card title="Poliza de Costo">
                    <div class="flex flex-wrap">
                        <h3>
                            Nota: Modulo con datos a partir de Mayo 2013
                        </h3>
                        <div class="w-full  p-2">
                            <vs-table2 max-items="10" search pagination :data="listaInvCargos" class="mt-0 mb-0">
                                <template slot="thead">
                                    <th order="Empresa" width="50px">Empresa</th>
                                    <th order="Tipo" width="100px">Tipo</th>
                                    <th order="SerieDocumento" width="50px">Serie Documento</th>
                                    <th order="Documento" width="100px">Documento</th>
                                    <th order="Linea" width="250px">Línea</th>
                                    <th order="Categoria" >Categoría</th>
                                    <th order="Costo" width="150px">Costo</th>
                                    <th order="Debe" width="150px">Debe</th>
                                    <th order="Haber" width="150px">Haber</th>
                                    <th order="Fecha" width="150px">Fecha</th>
                                    <th order="PeriodoPartida" width="150px">Periodo Partida</th>
                                    <th order="Partida" width="150px">Partida</th>
                                    <th order="SerieFactura" width="150px">Serie Factura</th>
                                    <th order="Factura" width="150px">Factura</th>
                                </template>
                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td2>
                                            {{ tr.Empresa }}                               
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.Tipo}}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.SerieDocumento}}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.Documento}}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.Linea}}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.Categoria}}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.Costo}}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.Debe}}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.Haber}}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.Fecha}}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.PeriodoPartida}}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.Partida}}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.SerieFactura}}
                                        </vs-td2>
                                        <vs-td2>
                                            {{tr.Factura}}
                                        </vs-td2>
                                    </tr>
                                </template>
                            </vs-table2>
                        </div>
                    </div>
                </vx-card>
            </div>
        </vx-card>
    </div>
</template>
<script>
    export default {
        data(){
            return{
                correlativoAnulacion: '',
                tipoOrden: '',
                descripcionTipo: '',
                numeroOrden: '',
                admision: '',
                fecha: '',
                razon: '',
                listaOrdenes: [],
                listaInvCargos: []
            }
        },
        methods: {
            isEmptyObject(obj){
                return Object.keys(obj).length === 0;
            },
            BuscarOrden(){
                if(this.correlativoAnulacion == '' && this.tipoOrden == '' && this.numeroOrden == ''){
                    this.$vs.notify({
                        color: 'danger',
                        text: 'No coloco ningún dato para búsqueda',
                        position: 'bottom-center'
                    })
                }else{
                    this.axios.post('/app/v1_JefeCaja/ConsultaCargoAnulado', {
                        "CorrelativoCargoAnulado": this.correlativoAnulacion,
                        "TipoOrden": this.tipoOrden,
                        "NumeroOrden": this.numeroOrden
                    })
                    .then(resp => {
                        if(!this.isEmptyObject(resp.data.json)){
                            this.listaOrdenes = resp.data.json.map(m => {
                                return{
                                    ...m,
                                    Cantidad: parseFloat(m.Cantidad).toFixed(0),
                                    Valor: parseFloat(m.Valor).toFixed(2),
                                    Costo: parseFloat(m.Costo).toFixed(2)
                                }
                            })
                            this.admision = resp.data.json[0].SerieAdmision + ' ' + resp.data.json[0].Admision
                            this.fecha = resp.data.json[0].Fecha
                            this.razon = resp.data.json[0].Razon
                            this.axios.post('/app/v1_JefeCaja/ConsultaInventarioCargo', {
                                "CorrelativoCargoAnulado": resp.data.json[0].Correlativo
                            })
                            .then(resp1 => {
                                if(!this.isEmptyObject(resp1.data.json)){
                                    this.listaInvCargos = resp1.data.json.map(m => {
                                        return{
                                            ...m
                                        }
                                    })
                                }
                            })
                        }else{
                            this.$vs.notify({
                                color: 'danger',
                                text: 'La Orden anulada que busca no existe. Favor verifique...',
                                position: 'bottom-center',
                                time: 4000
                            })
                        }
                    })
                }
            },
            BuscarTipoOrden(){
                if(this.tipoOrden != ''){
                    this.axios.post('/app/v1_JefeCaja/ConsultaDescripcionTipo', {
                        "TipoOrden": this.tipoOrden
                    })
                    .then(resp => {
                        if(!this.isEmptyObject(resp.data.json)){
                            this.descripcionTipo = resp.data.json[0].Nombre
                        }
                        else{
                            this.$vs.notify({
                                color: 'danger',
                                text: 'El tipo ingresado no esta configurado',
                                position: 'bottom-center',
                                time: 4000
                            })
                            this.focusTipo()
                        }
                    })
                }     
            },
            focusTipo(){
                this.$refs.tipo.$el.querySelector('input').focus()
            }
        }
    }
</script>
<style lang="scss" scoped>
    h3{
        flex-basis:100%;
        text-align: right;
        color:#E74C3C;
    }
    .box-custom{
        box-shadow: 0px 15px 40px -10px;
        padding: 15px;

        .button_search{
            padding-top: 35px;
        }
    }
    .div-input{
        padding: 10px;
        .input-serie{
            width: 75px;
        }
        .input-numero{
            width: 200px;
        }
        .textEstado{
            color: #fc0909;
            font-weight: bold;
            font-size: 25px;
        }

    }
    .label-info{
        margin-left: 10px;
        color:#E74C3C;
        opacity: 0.7;
        border: 0;
        font-size: 16px;
        background-color: white;
    }
    .label-info2{
        margin-left: 10px;
        padding-top: 25px;
        color:#E74C3C;
        opacity: 0.7;
        border: 0;
        font-size: 16px;
        background-color: white;
    }
    .label-static{
        font-weight: bold;
        font-size: 16px;
    }
    .label-static1{
        font-weight: bold;
        font-size: 24px;
        text-align: center;
    }
</style>