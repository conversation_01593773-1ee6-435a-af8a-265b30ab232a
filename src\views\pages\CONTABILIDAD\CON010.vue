<template>
  <div class="form-container">

    <div style="width: 50%;" class="input-group">
      <label>Empresa:</label><br>
      <DxSelectBox
            :items="Empresas"
            :input-attr="{ 'aria-label': 'Product With Placeholder' }"
            :show-clear-button="true"
            placeholder="Empresa"
            value-expr="EmpresaReal"
            display-expr="Nombre"
            v-model="info.Empresa"
          />
    </div>

      <div class="input-group">
      <label >Año:</label><br>
      <input type="number" v-model="info.Año"  class="form-input">
    </div>

    <div class="input-group">
      <label>Mes:</label><br>
      <input type="number" v-model="info.Mes"  class="form-input">
    </div>
 
    <div class="input-group">
      <label for="">Incluir a Citas y Cupones</label>&nbsp;
          <DxCheckBox
            v-model="info.ReporteCompleto" 
          />
    </div>

    <div class="button-group">
      <button @click="consultaSatHonorarios" class="form-button"><i class="fas fa-search"></i> Realizar Consulta</button>
      <button @click="limpiarDatos" class="form-button" style="background-color: #dc3545;"><i class="fas fa-trash-alt"></i> Limpiar Datos</button>
   
    </div>

      <div class="info-display">
        <p><strong>Mes:</strong> {{ info.Mes }}</p>&nbsp;
        <p><strong>Año:</strong> {{ info.Año }}</p>
        <p><strong>Nit Hospital:</strong> {{ info.NitHospital }}</p>
      </div>

      <vs-table2 class="OverFlow" search tooltip filter :data="datos" exportarExcel="Sat Honorarios" noSelectText>
        <template slot="thead">
            <th>Numero</th>
            <th>Nombre Empresa</th>
            <th>Mes</th>
            <th>Año</th>
            <th>Fila</th>
            <th>Cuenta Ajena</th>
            <th>Tipo Atención</th>
            <th>Admisión</th>
            <th>Nombre Paciente</th>
            <th>Paciente</th>
            <th>Fecha Egreso</th>
            <th>Recibo</th>
            <th>Ajeno</th>
            <th>Total Asistencia</th>
            <th>Asistencia Facturada Por</th>
            <th>Nit de la Aseguradora</th>
            <th>Fecha asistencia médica</th>
            <th>Valor sobre total de asistencia cobrada al paciente</th>
            <th>Nit</th>
            <th>Especialización</th>
            <th>Presenta Factura</th>
            <th>Factura Numero</th>
            <th>Factura Serie</th>
            <th>Factura Fecha</th>
            <th>Total Factura</th>
        </template>
        <template slot-scope="{ data }">
            <tr v-for="(tr, indextr) in data" :key="indextr">
                <vs-td2>{{ tr.Numero }}</vs-td2>
                <vs-td2>{{ tr.NombreEmpresa }}</vs-td2>
                <vs-td2>{{ tr.MES }}</vs-td2>
                <vs-td2>{{ tr.AÑO }}</vs-td2>
                <vs-td2>{{ tr.Fila }}</vs-td2>
                <vs-td2>{{ tr.EsCuentaAjena }}</vs-td2>
                <vs-td2>{{ tr.TipoAtencion }}</vs-td2>
                <vs-td2>{{ tr.Admision }}</vs-td2>
                <vs-td2>{{ tr.NombrePaciente }}</vs-td2>
                <vs-td2>{{ tr.Paciente }}</vs-td2>
                <vs-td2>{{ tr.FechaEgreso }}</vs-td2>
                <vs-td2>{{ tr.Recibo }}</vs-td2>
                <vs-td2>{{ tr.Ajeno }}</vs-td2>
                <vs-td2>{{ tr.TotalAsistencia }}</vs-td2>
                <vs-td2>{{ tr.AsistenciaFacturadaPor }}</vs-td2>
                <vs-td2>{{ tr['Nit de la Aseguradora'] }}</vs-td2>
                <vs-td2>{{ tr['Fecha asistencia médica'] }}</vs-td2> 
                <vs-td2>{{ tr.Valor_sobre_total_de_asistencia_cobrada_al_paciente }}</vs-td2>
                <vs-td2>{{ tr.Nit }}</vs-td2>
                <vs-td2>{{ tr.Especializacion }}</vs-td2>
                <vs-td2>{{ tr.presentaFactura }}</vs-td2>
                <vs-td2>{{ tr.FacturaNumero }}</vs-td2>
                <vs-td2>{{ tr.FacturaSerie }}</vs-td2>
                <vs-td2>{{ tr.FacturaFecha }}</vs-td2>
                <vs-td2>{{ tr.TotalFactura }}</vs-td2>
            </tr>
        </template>
      </vs-table2>
       




  </div>
</template>

<script>
import axios from 'axios';
import { DxCheckBox } from 'devextreme-vue/check-box';
import { DxSelectBox } from 'devextreme-vue/select-box'

export default {
  components: {
    DxCheckBox,
    DxSelectBox
  },
  
  data() {
    return {
      datos: [],
      Empresas: [],
      isTreeBoxOpened:false,
      info: {
        Empresa: '',
        Año: '',
        Mes: '',
        NitHospital: '', // Inicialmente vacío
        ReporteCompleto: ''
        
      },

    };
  },
  computed: {
    isDataAvailable() {
      // Retorna true si hay datos, false si no
      return this.datos && this.datos.length > 0;
    }
  },
  methods: {
  consultaSatHonorarios() {
    const empresa = this.info.Empresa;
    const año = parseInt(this.info.Año, 10);
    const mes = parseInt(this.info.Mes, 10);
    const reporteCompleto = this.info.ReporteCompleto ? 1 : 0;

    axios.post("/app/v1_contabilidad_general/ConsultaSpHotarios", {
      Opcion: "1",
      Empresa: empresa,
      Año: año,
      Mes: mes,
      ReporteCompleto: reporteCompleto
    })
    .then((resp) => {
      if (resp.data.codigo == "0") {
        
        this.datos = resp.data.json.map((item) => {
                // Quitar las comas y convertir TotalAsistencia a número
                const totalAsistenciaText = item.TotalAsistencia || "0";
                item.TotalAsistencia = parseFloat(totalAsistenciaText.replace(/,/g, ''));
                // Quitar las comas y convertir NitHospital a número
                const nitHospitalText = item.NitHospital || "0";
                item.NitHospital = parseFloat(nitHospitalText.replace(/,/g, ''));
                return item;
            });

        if (this.datos.length > 0) {
          this.info.NitHospital = this.datos[0].NitHospital || '';
        }
      }
    })
  },

 

  consultaEmpresas(){
    axios.post("/app/v1_autorizaciones/ConsultaEmpresa",{
      Opcion:"0"
    })
    .then((resp) =>{
      if(resp.data.codigo == "0"){
        this.Empresas = resp.data.json;
      }
    })
  },

  
  limpiarDatos() {
    this.info = {
      Empresa: '',
      Año: '',
      Mes: '',
      NitHospital: '',
      ReporteCompleto: ''
    };
    this.datos = [];
  },

/*   async exportToExcel() {
    // Crear un nuevo libro de trabajo
    const workbook = XLSX.utils.book_new();
    // Crear la hoja de trabajo con los datos
    const worksheetData = [this.columns.map(col => col.caption)];
    this.datos.forEach(row => {
        worksheetData.push(this.columns.map(col => row[col.dataField] || ''));
    });
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
    // Ajustar automáticamente el ancho de las columnas
    const maxWidths = worksheetData.reduce((widths, row) => {
        row.forEach((cell, index) => {
            const width = (cell && cell.toString().length) || 10;
            if (!widths[index] || width > widths[index]) {
                widths[index] = width;
            }
        });
        return widths;
    }, []);
    worksheet['!cols'] = maxWidths.map(w => ({ wch: w + 2 }));
    // Agregar la hoja al libro
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Datos');
    // Generar el archivo
    const buffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    // Construir el nombre del archivo
    const empresa = this.info.Empresa || 'EmpresaDesconocida';
    const año = this.info.Año || 'AñoDesconocido';
    const mes = this.info.Mes || 'MesDesconocido';
    const fileName = `Sat_Honorarios_${empresa}_${mes}_${año}.xlsx`;
    // Guardar el archivo
    saveAs(new Blob([buffer]), fileName);
},

  exportToCSV() {
    // Definir la fila con los parámetros
    const parameters = [
      `${this.info.NitHospital}`, 
      `${this.info.Mes}`,
      `${this.info.Año}`
    ];

    // Ajustar las columnas para CSV
    const startColumnIndex = this.columns.findIndex(col => col.dataField === 'TotalAsistencia');
    const filteredColumns = this.columns.slice(startColumnIndex);

    // Convertir los datos eliminando las comillas dobles
    const data = this.datos.map(row => 
      filteredColumns.map(col => {
        let value = row[col.dataField];
        if (typeof value === 'string') {
          value = value.replace(/""/g, ''); // Eliminar comillas dobles de todas las cadenas
        }
        return value;
      })
    );

    // Generar el CSV incluyendo solo la información y los parámetros
    const csvRows = [
      parameters, // Fila con parámetros
      ...data // Filas con datos
    ];

    // Generar el CSV con Papa.unparse
    let csv = Papa.unparse(csvRows, {
      quotes: false // Eliminar comillas dobles de los datos
    });

    // Construir el nombre del archivo
    const empresa = this.info.Empresa || 'EmpresaDesconocida';
    const año = this.info.Año || 'AñoDesconocido';
    const mes = this.info.Mes || 'MesDesconocido';
    const fileName = `Sat_Honorarios_${empresa}_${mes}_${año}.csv`;

    // Crear un blob con el contenido CSV y descargar el archivo
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    saveAs(blob, fileName);
}
 */

},

mounted() {
    this.consultaEmpresas();
}


};
</script>


<style scoped>
.form-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.input-group {
  margin-bottom: 15px;
}

.form-input {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  width: 50%;
  box-sizing: border-box;
}

.form-checkbox {
  margin-left: 10px;
}

.button-group {
  margin-top: 20px;
}

.form-button {
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  color: #fff;
  background-color: #007bff;
  margin-right: 10px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.form-button:hover {
  background-color: #0056b3;
}

.info-display {
  margin-top: 20px;
}

.info-display p {
  margin: 5px 0;
}

.form-select {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  width: 50%;
  box-sizing: border-box;
}

</style>


<style>
.dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed)>td {
    background-color: #00ccff !important;
    color: black !important;
}

.dx-datagrid-rowsview .dx-row.dx-data-row.dx-column-lines.dx-state-hover>td {
    background-color: #79e4ff !important;
    color: black !important;
    font-size: 16px;
}

.dx-datagrid-headers {
    background-color: linen !important;
    color: black !important;
    font-weight: bold;
}

.OverFlow .contenedor-tabla
{
  overflow-y: auto !important;
  max-height: 700px !important; 
}
</style>