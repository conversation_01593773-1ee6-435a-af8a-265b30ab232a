/*=========================================================================================
  File Name: _layoutCommon.scss
  Description: Common layout styles. This style will apply to all layouts
  ----------------------------------------------------------------------------------------
  Item Name: Vuexy - Vuejs, HTML & Laravel Admin Dashboard Template
    Author: Pixinvent
  Author URL: http://www.themeforest.net/user/pixinvent
==========================================================================================*/


html {
  font-size: $font-size-base;
  height: 100%;
  width: 100%;
  line-height: 1.5;
  letter-spacing: 0.01rem;
}

body {
  font-family: $font-family-sans-serif;
  background: $theme-background;
  font-weight: 400;
  overflow-x: hidden;
  max-width: 100%;
  height: 100%;
  transition: background-color .3s ease;
}

#app {
  min-height: 100%;
}

.vx-logo {
  .vx-logo-text{
    font-size: 18px;
    font-weight: 600;
    animation: 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) 0s normal forwards 1 fadein;
  }
}

.router-view {
//   position: relative;
  padding: 0.5rem;

  @media (max-width: 576px) {
    padding: 1.2rem !important;


    .footer-sticky & {
      padding-bottom: 5rem !important;
    }
  }

  .content-area__heading {
    h2 {
      color: #636363;
    }
  }
}

.layout--main {
  height: 100%;
  min-height: 100%;
}

#content-overlay {
  position: fixed;
  opacity: 0;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  cursor: pointer;
  transition: opacity .7s;
  z-index: -1;

  .show-overlay & {
    z-index: 41001;
    opacity: 1;
  }
}

.the-footer {
  display: flex;
  align-items: center;
}

// ======================================================================================
// Content Area
// ======================================================================================

#content-area {

  height: 100%;
  transition: margin-left 0.5s;

  .content-wrapper {
    min-height: calc(var(--vh, 1vh) * 100 - 3.5rem);

    .navbar-static & {
      min-height: calc(var(--vh, 1vh) * 100 - 8rem);
    }
  }

  &.content-area-reduced {
    margin-left: 260px;
  }

  &.content-area-lg {
    margin-left: $reduced-sidebar-width;
  }

  &.content-area-full {
    margin-left: 0px;
  }
}


// ======================================================================================
// NavBar
// ======================================================================================

.navbar-floating {
  .router-content {
    margin-top: 3.5rem;
    @media (max-width: 576px) {
      margin-top: 6rem;
    }
  }
}

// ======================================================================================
// Other
// ======================================================================================

div[id$="demo"] {
  .vx-card:not(:last-of-type) {
    margin-bottom: 2.2rem;
  }
}

.vue-back-to-top {
  z-index: 51000 !important;
}


// ======================================================================================
// Component Demo
// ======================================================================================

.demo-alignment {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;

  &>* {
    margin-right: 1.5rem;
    margin-top: 1.5rem;
  }
}

.op-block {
  // background: rgb(235, 235, 235);
  box-shadow: 1px 1px 10px rgba(0, 0, 0, 0.1);
  padding: 10px;
  border-radius: 10px;
}


// ======================================================================================
// Applications Page
// ======================================================================================

.no-scroll-content {
  height: calc(var(--vh, 1vh) * 100 - 14.1rem);
}
