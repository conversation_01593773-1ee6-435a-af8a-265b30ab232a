<template>
<vx-card title="Semáforo de Diagnóstico" class="rad007-container">

    <vs-popup ref="rechazar" :title="`Rechazar`" :active.sync="rechazo.mostrar" style="z-index:52000" id="div-with-loading" class="vs-con-loading__container" width="200">
        <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
            <ValidationProvider name="Comentario" rules="required" class="required">
                <vs-textarea class="w-full" label="Comentario" counter="200" v-model="rechazo.comentario" />
            </ValidationProvider>
            <div class="flex">
                <vs-spacer></vs-spacer>
                <vs-button @click="handleSubmit(Guardar(rechazo.item).Rechazar())" color="danger" :disabled="invalid">Rechazar</vs-button>
            </div>
        </ValidationObserver>
    </vs-popup>
    <!-------------- INICIO NUEVO TAB-->
    <vs-tabs :color="colorx">
        <vs-tab label="Ordenes Pendientes" icon="edit"  @click="colorx = '#FFA500'">
            <h4>Ordenes Pendientes</h4>
            <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                <div class="flex flex-wrap">
                    <div class="w-full">
                        <div class="flex flex-wrap">
                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-2">
                                <ValidationProvider name="Hospital" rules="required" class="required">

                                    <label class="typo__label">Hospital *</label>
                                    <multiselect v-model="cb_hospital" :options="Empresas" :close-on-select="true" :show-labels="false" :custom-label="Hospital_seleccionado" placeholder="Seleccionar Hospital" @input="onChangeHospital">
                                        <span slot="noOptions">Seleccione el Hospital</span>
                                    </multiselect>
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-2">
                                <ValidationProvider name="Tipo" rules="required" class="required">

                                    <label class="typo__label">Tipo *</label>
                                    <multiselect v-model="cb_tipo" :options="Tipos" :close-on-select="true" :show-labels="false" :custom-label="Tipo_seleccionado" placeholder="Seleccionar Tipo" @input="onChangeTipo">
                                        <span slot="noOptions">Seleccione el Hospital</span>
                                    </multiselect>
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-2">
                                <ValidationProvider name="Fecha Inicial" rules="required" class="required">
                                    <vs-input label="Fecha Inicial" class="w-full" type="date" v-model="info.Inicio" />
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-2">
                                <ValidationProvider name="Fecha Final" rules="required" class="required">
                                    <vs-input label="Fecha Final" class="w-full" type="date" v-model="info.Final" />
                                </ValidationProvider>
                            </div>

                            <vs-spacer></vs-spacer>
                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-2">
                                <vs-button @click="handleSubmit(Consulta().actualizar())" :disabled="invalid">
                                    Actualización
                                </vs-button>
                            </div>

                            <!---CHECK SELECCIONAR TODOS -->

                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-2">
                                <vs-checkbox v-model="ActualizacionAutomatica" @change="Consulta().Actualizacion_Automatica()">Actualizar automáticamente cada 5 minutos.</vs-checkbox>
                            </div>

                        </div>
                    </div>
                </div>
                <br>
                <hr>
                <br>
            </ValidationObserver>

            <div style="border-radius:5px;padding:5px;font-size:15px;background-color:#A5D6A7">
                Estimado Usuario si la orden no aparece, por favor presionar el botón "Actualización" o F5 y si no aparece el estudio ya fue informado por el Radiólogo, informar a su jefatura para su productividad.
            </div>

            <DxDataGrid :data-source="tabla" v-bind="gridOptions" class="smtabla tooltip noSelectText p-3" @row-prepared="onRowPrepared">
                <DxDataGridColumn data-field="orden" caption="Tipo" 
                    :calculate-display-value="(x)=>`${x.tipoorden} ${x.orden}`" 
                    :calculate-filter-expression="calculateFilterExpression"
                    width="110"
                />
                <DxDataGridColumn  caption="Admisión" data-field="Admision" width="100"/>
                <DxDataGridColumn  caption="Paciente" data-field="Paciente" />
                <DxDataGridColumn  caption="Examen" data-field="nombre" />
                <DxDataGridColumn  caption="Subárea" data-field="Area" />
                <DxDataGridColumn  caption="Estado Status" data-field="status" cell-template="status-cell-template" width="110"/>
                <DxDataGridColumn  caption="Tipo Admision" data-field="Tipo_Admision" width="100"/>
                <DxDataGridColumn  caption="Región, Indicaciones u Observaciones" data-field="RegionObservaciones" cell-template="buttons-cell-template" />
                
                <template #status-cell-template="{ data: templateOptions }">
                    <div>
                        <i v-if="templateOptions.data.status=='Recibido'" style="color:#2ECC71" class="far fa-check-circle"></i>
                        <i v-if="templateOptions.data.status=='Rechazado'" style="color:#E74C3C" class="far fa-times-circle"></i>
                        <i v-if="templateOptions.data.status=='Pendiente'" style="color:#95A5A6" class="fas fa-dot-circle"></i>
                        {{ templateOptions.data.status }}
                    </div>
                </template>

                <template #buttons-cell-template="{ data: templateOptions }">
                    <div>
                        <vx-input-group>
                            <vs-input class="Etiqueta" v-model="templateOptions.data.RegionObservaciones" disabled />

                            <template slot="append">
                                <div v-if="templateOptions.data.RegionObservaciones.length > 0">
                                    <vs-button color="warning" icon-pack="fas" icon="fa-info" style="display:inline-block;margin-right:2px" @click="OrdenSeleccionada=[];InformacionEstudio(templateOptions.data)"></vs-button>
                                </div>
                                <div v-else>
                                    <vs-button color="warning" disabled icon-pack="fas" icon="fa-info" style="display:inline-block;margin-right:2px" ></vs-button>
                                </div>
                            </template>
                        </vx-input-group>
                    </div>
                </template>
            </DxDataGrid>

            <template>
                <div v-if="visible " :style="{ top: `${y}px`, left: `${x}px` }" class="context-menu">
                    <ul v-if="OrdenSeleccionada.status=='Pendiente'">
                        
                        <li @click="Guardar(OrdenSeleccionada).Realizar()">Recibir Estudio</li>
                        <li @click="ValidacionPre_Rechazo(OrdenSeleccionada)">Rechazar Estudio</li>
                    </ul>
                    <ul v-else>
                        <li > El estudio ya fue validado</li>
                    </ul>
                </div>
            </template>

        </vs-tab>
        <vs-tab label="Estado de Ordenes" icon="account_balance" @click="colorx = '#00C853'">
            <h4>Estado de Ordenes</h4>
            <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                <div class="flex flex-wrap">
                    <div class="w-full ">
                        <div class="flex flex-wrap">
                            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-2">
                                <ValidationProvider name="Hospital" rules="required" class="required">
                                    <label class="vs-input--label">Hospital *</label>
                                    <multiselect v-model="infoEstado.Empresa" :options="Empresas" :disabled="false" :allow-empty="false" placeholder="Seleccione el Hospital" track-by="Nombre" label="Nombre"></multiselect>
                                    <!--<label class="typo__label">Hospital *</label>
                                    <multiselect v-model="cb_hospital" :options="Empresas" :close-on-select="true" :show-labels="false" :custom-label="Hospital_seleccionado" placeholder="Seleccionar Hospital" @input="onChangeHospital">
                                        <span slot="noOptions">Seleccione el Hospital</span>
                                    </multiselect>-->
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-2">
                                <ValidationProvider name="Tipo" rules="required" class="required">
                                    <label class="vs-input--label">Tipo *</label>
                                    <multiselect v-model="infoEstado.Tipo" :options="Tipos" :disabled="false" :allow-empty="false" placeholder="Selección de Tipo" track-by="Nombre" label="Nombre"></multiselect>
                                    <!--<label class="typo__label">Tipo *</label>
                                    <multiselect v-model="cb_tipo" :options="Tipos" :close-on-select="true" :show-labels="false" :custom-label="Tipo_seleccionado" placeholder="Seleccionar Tipo" @input="onChangeTipo">
                                        <span slot="noOptions">Seleccione el Hospital</span>
                                    </multiselect>-->
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-2">
                                <ValidationProvider name="Tipo" rules="required" class="required">
                                    <label class="vs-input--label">Opción *</label>
                                    <multiselect v-model="infoEstado.Opcion" :options="Opciones" :disabled="false" :allow-empty="false" placeholder="Seleccione la Opción" track-by="Nombre" label="Nombre"></multiselect>
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-2">
                                <ValidationProvider name="Fecha Inicial" rules="required" class="required">
                                    <vs-input label="Fecha Inicial" class="w-full" type="date" v-model="infoEstado.Inicio" />
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-2">
                                <ValidationProvider name="Fecha Final" rules="required" class="required">
                                    <vs-input label="Fecha Final" class="w-full" type="date" v-model="infoEstado.Final" />
                                </ValidationProvider>
                            </div>
                            <vs-spacer></vs-spacer>
                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-2">
                                <vs-button @click="handleSubmit(Consulta().actualizarEstado())" :disabled="invalid">
                                    Actualización
                                </vs-button>
                            </div>
                        </div>
                    </div>
                </div>
                <br>
                <hr>
                <br>
            </ValidationObserver>
             
            <DxDataGrid :data-source="tablaEstado" v-bind="gridOptions" class="smtabla tooltip noSelectText p-3" >
                <DxDataGridColumn dataField="tipoorden" caption="Tipo" width="65"/>
                <DxDataGridColumn dataField="Admision" caption="Admisión"/>
                <DxDataGridColumn dataField="Paciente" caption="Paciente"/>
                <DxDataGridColumn caption="Orden" :calculate-display-value="(x)=>`${x.tipoorden} ${x.orden}`" :calculate-filter-expression="calculateFilterExpression" />
                <DxDataGridColumn dataField="nombre" caption="Examen"/>
                <DxDataGridColumn dataField="Area" caption="Subárea"/>
                <DxDataGridColumn dataField="fecha_a_realizar" caption="Requerida Fecha"/>
                <DxDataGridColumn caption="Requerida Hora" :calculate-display-value="(x)=>`${x.hora.split(' ')[1]}`"/>
                <DxDataGridColumn dataField="status" caption="Estado Status"/>
                <DxDataGridColumn dataField="Horas_retraso" caption="Estado Retraso"/>
                <DxDataGridColumn dataField="observaciones" caption="Observaciones"/>
                <DxDataGridColumn dataField="observacionesExtra" caption="Observaciones Extra"/>
                <DxDataGridColumn dataField="Tipo_Admision" caption="Tipo Admisión"/>
                <DxDataGridColumn dataField="Habitacion" caption="Habitación"/>
                <DxDataGridColumn dataField="Ubicacion" caption="Ubicación"/>
                <DxDataGridColumn dataField="fecharegistro" caption="Fecha Registro"/>
                <DxDataGridColumn dataField="usuario" caption="Usuario"/>
            </DxDataGrid>

        </vs-tab>

     
    </vs-tabs>

    <!-------- POPUP MOSTRADO INFORMACIÓN DEL REGIÓN, INDICACIONES U OBSERVACIONES-->
    <vs-popup class="popNuevo" title="Información estudio" :active.sync="VentanaObservacionesRegion" style="z-index:99999" id="4">
        <div  class="content-wrapper">

            <div class="mb-4">
                <div class=" bg-grid-color-secondary h-12 mt-5">
                    <vs-col><b><small> Paciente: </small></b> {{ OrdenSeleccionada.Paciente }}</vs-col>
                    <vs-col><b><small> Admisión: </small></b> {{ OrdenSeleccionada.Admision }}</vs-col>
                    <vs-col><b><small> Tipo: </small></b> {{ OrdenSeleccionada.NoOrden }}</vs-col>
                    <vs-col><b><small> Código: </small></b> {{ OrdenSeleccionada.codigo }}</vs-col>
                    <vs-col><b><small> Producto: </small></b> {{ OrdenSeleccionada.nombre }} </vs-col>
                    <vs-col><b><small> Línea: </small></b> {{ OrdenSeleccionada.Linea }}</vs-col>                    
                    <vs-col><b><small> Categoría: </small></b> {{ OrdenSeleccionada.categoria }}</vs-col>
                    
                </div>
            </div>
           

           <div>
            <vs-row>
                <vs-textarea disabled class="w-full" label="Región, Indicaciones u Observaciones" counter="400" v-model="OrdenSeleccionada.RegionObservaciones" />
            </vs-row>
            </div>
            
            <vs-button color="danger" style="float: right" type="border" icon-pack="feather" icon="icon-x" @click="OrdenSeleccionada = []; VentanaObservacionesRegion=false">
                Cerrar</vs-button>
          
        </div>
        </vs-popup>    

    <!-------------- INICIO PESTAÑAS ANTERRIORES-->

    <div>
        <vs-popup classContent="popup-example" title="Validar credencial" :active.sync="Band_ValidarCredencial">

            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">

                <form>

                    <div style="margin: 15px" class="flex flex-wrap">
                        <div class="w-full md:w-1/2 lg:w1/2 xl:w-1/2">
                            <ValidationProvider name="corporativo" rules="required|max:40" v-slot="{ errors }" class="required">
                                <vs-input v-on:blur="BusquedaPuesto" label="Corporativo" class="w-full" count="40" v-model="Vl_Corporativo" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                            </ValidationProvider>

                        </div>
                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <ValidationProvider name="Contraseña" rules="required|max:40" v-slot="{ errors }" class="required">
                                <vs-input type="password" ref="password" class="w-full mb-4" label="Contraseña" count="12" v-model="Vl_Clave" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                            </ValidationProvider>
                        </div>
                    </div>

                    <div v-if="ExistePuesto==false" style="border-radius:5px;padding:5px;font-size:15px;background-color:#E0884D;color:white">
                        Corporativo no contiene Puesto / Pago productividad configurado.
                    </div>

                    <vs-button color="primary" style="float:right" type="filled" icon-pack="feather" icon="icon-save" @click="ConsultarCorporativo()"> Aceptar</vs-button>
                    <vs-button color="danger" style="float:right" type="border" icon-pack="feather" icon="icon-x" @click="Band_ValidarCredencial=false;ExistePuesto = true;ItemSeleccionado = '';"> Cancelar</vs-button>
                    <vs-divider></vs-divider>

                </form>
            </div>
        </vs-popup>
    </div>
</vx-card>
</template>

<script>

import moment from 'moment';
import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";

export default {
    data() {
        return {
            VentanaObservacionesRegion: false,
            visible: false,
            x: 0,
            y: 0,
            colorx: '#FFA500',
            ActualizacionAutomatica: true,
            Band_ValidarCredencial: false,
            Vl_Corporativo: '',
            Vl_Clave: '',
            ExistePuesto: true,
            NombreCorporativo: '',
            ItemSeleccionado: '',
            estudios: [{
                valor: 0,
                color: '#E74C3C',
                title: 'Emergencia / Intensivo'
            }, {
                valor: 0,
                color: '#F39C12',
                title: 'Hospitalización'
            }, {
                valor: 0,
                color: '#27AE60',
                title: 'Consulta Externa'
            }],
            estudiosFinalizados: 0,

            temp: null,

            Tipos: [{
                    Codigo: 'L',
                    Nombre: 'LABORATORIO'
                },
                {
                    Codigo: 'D',
                    Nombre: 'DIAGNÓSTICO'
                }
            ],
            Opciones: [{
                    Codigo: '1',
                    Nombre: 'INFORMADO'
                },
                {
                    Codigo: '0',
                    Nombre: 'NO INFORMADO'
                }
            ],
            Empresas: [],

            info: {
                Empreas: null,
                Tipo: null,
                Inicio: null,
                Final: null
            },
            infoEstado: {
                Empreas: null,
                Tipo: null,
                Opcion: null,
                Inicio: null,
                Final: null
            },

            rechazo: {
                mostrar: false,
                item: null,
                comentario: null
            },
            tabla: [],
            tablaEstado: [],
            id_hospital_selec: null,
            cb_hospital: '',

            cb_tipo: '',
            id_Tipo_selec: null,

            OrdenSeleccionada: [],
            gridOptions: {
                columnAutoWidth: true,
                export: {
                    enabled: true,
                    formats: ['xlsx'],
                    fileName:'Ordenes Pendientes',
                },
                headerFilter: {
                    visible: true,
                    enabled: true,
                },
                paging: {
                    enabled: true,
                    pageSize: 10,
                },
                scrolling: {
                    showScrollbar: 'always',
                    useNative: false,
                },
                searchPanel: {
                    visible: true,
                },
                showColumnLines: false,
                showRowLines: true,
                sorting: {
                    mode: 'none'
                },
                toolbar: {
                    items: [
                        {
                            name: 'exportButton',
                            showText: true,
                            options: {type: 'success'},
                        },
                        'searchPanel',
                    ],
                    visible: true,
                },
            }
        }
    },
    components: {
        Multiselect,
        
    },
    computed: {
        sesion() {
            return this.$store.state.sesion
        }
    },
    methods: {
        InformacionEstudio(tr){
            this.VentanaObservacionesRegion = true;
            this.OrdenSeleccionada  = tr;
        },
        hideMenu() {
            this.visible = false;
        },
        showContextMenu(event, tr) {
            this.OrdenSeleccionada  = tr;
            this.x = (event.clientX );
            this.y = (event.clientY );
            this.visible = true;
        },
        fecha_hora_local() {
            var today = new Date()
            var now_date = (today.getFullYear() + '-' + (today.getMonth() + 1) + '-' + today.getDate());
            var now_time = (today.getHours() + ":" + today.getMinutes() + ":" + today.getSeconds()).toString()
            return now_date + ' ' + now_time;

        },
        ClickEstado() {

            this.ActualizacionAutomatica = false;
        },
        Hospital_seleccionado({
            Nombre
        }) {
            return ` ${Nombre} `;
        },
        onChangeHospital(value) {
            if (value !== null && value.length !== 0) {
                this.id_hospital_selec = value.Codigo;

            } else {
                this.id_hospital_selec = null;
            }
        },

        Tipo_seleccionado({
            Nombre
        }) {
            return ` ${Nombre} `;
        },
        onChangeTipo(value) {
            if (value !== null && value.length !== 0) {
                this.id_Tipo_selec = value.Codigo;

            } else {
                this.id_Tipo_selec = null;
            }
        },
        ObtenerFecha() {
            let b = new Date()
            
            this.info.Inicio = moment(b).format('YYYY-MM-DD')
            this.info.Final = moment(b).format('YYYY-MM-DD')

            this.infoEstado.Inicio = moment(b).format('YYYY-MM-DD')
            this.infoEstado.Final = moment(b).format('YYYY-MM-DD')

        },
        Consulta() {
            return {
                consultaEmpresa: () => {
                    this.axios.post('/app/administracion/Busqueda_Empresa', {})
                        .then(resp => {
                            this.Empresas = resp.data.json

                        })
                },
                Actualizacion_Automatica: () => {

                    if (this.ActualizacionAutomatica == true && this.id_hospital_selec != null && this.id_Tipo_selec) {

                        this.Consulta().listado();
                        setTimeout(() => {
                            this.Consulta().Actualizacion_Automatica();

                        }, 300000)
                    }

                },
                actualizar: () => {
                    this.Consulta().listado()
                },
                actualizarEstado: () => {
                    this.ActualizacionAutomatica = false;
                    this.Consulta().listadoEstado()
                },
                listado: () => {
                    return this.axios.post('/app/admision/BusquedaAdmisiones', {

                            "Hospital": this.id_hospital_selec,
                            "Area": this.id_Tipo_selec,
                            "FechaInicio": this.info.Inicio,
                            "FechaFinal": this.info.Final
                        })
                        .then(resp => {
                            this.tabla = resp.data.json.map(m => {
                                return {
                                    ...m,
                                    activo: false
                                }
                            })
                        })
                },
                listadoEstado: () => {
                    return this.axios.post('/app/admision/BusquedaAdmisionesEstado', {
                            
                            "Hospital": this.infoEstado.Empresa.Codigo,
                            "Area": this.infoEstado.Tipo.Codigo,
                            "Informado": this.infoEstado.Opcion.Codigo,
                            "FechaInicio": this.infoEstado.Inicio,
                            "FechaFinal": this.infoEstado.Final
                        })
                        .then(resp => {
                            this.tablaEstado = resp.data.json.map(m => {
                                return {
                                    ...m,
                                    activo: false
                                }
                            })
                        })
                },
                contadores: () => {
                    this.axios.post('/app/admision/Obtenercontadores', {
                            "Hospital": this.info.Empresa.Codigo
                        })
                        .then(resp => {
                            const r = resp.data.json[0]
                            this.estudiosFinalizados = r.Finalizados
                            this.estudios[0].valor = r.Emergencias
                            this.estudios[1].valor = r.Hospitalizacion
                            this.estudios[2].valor = r.COEX
                            // this.tabla = resp.data.json
                        })
                }
            }
        },
        Guardar(item) {
            return {
                Realizar: () => {
                    const sesion = this.$store.state.sesion;

                    this.axios.post('/app/admision/validarEstado', {
                            "TipoOrden": item.tipoorden,
                            "Orden": item.orden,
                            "Producto": item.codigo,
                            "Linea": item.Linea
                        })
                        .then(resp => {
                            if (resp.data.codigo == -1) {
                                this.Consulta().listado()
                                return;
                            } else {

                                if (item.ValorTablaProducto.trim() != '' && item.ValorTablaProducto != null) {

                                    this.Band_ValidarCredencial = true;
                                    this.ExistePuesto = true;
                                    this.ItemSeleccionado = item;
                                    this.Vl_Corporativo = '';
                                    this.Vl_Clave = '';
                                } else {

                                    this.axios.post('/app/bitacora/registro_bitacora', {
                                        "info": "{\"info\":{\"TecnicoRealizo\" :" + "0" + ",\" UsuarioSesion\" :" + sesion.corporativo + " ,\"TipoOrden\" :\"" + item.tipoorden + "\", \" Orden\" :\"" + item.orden + "\" ,\" Producto\" :\"" + item.codigo + "\" ,\" Linea\" :\"" + item.Linea + "\"  \" Observaciones\" :\"" + "**INICIO REGISTRO SIN CORPORATIVO** " + "\", \"Aplicativo\" :\"WEB\"  }}"

                                    })
                                    var observacionesRecibido = "Recibido por " + sesion.corporativo + ' Fecha: '
                                    observacionesRecibido = observacionesRecibido + this.fecha_hora_local();

                                    this.axios.post('/app/admision/ActualizarOrdenDetalle', {
                                            "Estado": "R",
                                            "TipoOrden": item.tipoorden,
                                            "Orden": item.orden,
                                            "Producto": item.codigo,
                                            "Linea": item.Linea,
                                            "Observaciones": observacionesRecibido

                                        })
                                        .then(() => {

                                            this.axios.post('/app/bitacora/registro_bitacora', {
                                                    "info": "{\"info\":{\"TecnicoRealizo\" :" + "0" + ",\" UsuarioSesion\" :" + sesion.corporativo + " ,\"TipoOrden\" :\"" + item.tipoorden + "\", \" Orden\" :\"" + item.orden + "\" ,\" Producto\" :\"" + item.codigo + "\" ,\" Linea\" :\"" + item.Linea + "\"  \" Observaciones\" :\"" + "**MENSAJE AUTOMATICO: NO VALIDADO POR CORPORATIVO** " + "\", \"Aplicativo\" :\"WEB\"  }}"

                                                })
                                                .then(() => {
                                                    this.Consulta().actualizar()
                                                })
                                        })
                                }
                            }
                        }).catch(() => {
                            this.Consulta().listado()
                        })

                },
                Rechazar: () => {

                    this.axios.post('/app/admision/validarEstado', {
                            "TipoOrden": item.tipoorden,
                            "Orden": item.orden,
                            "Producto": item.codigo,
                            "Linea": item.Linea
                        })
                        .then(resp => {
                            if (resp.data.codigo == -1) {
                                this.Consulta().listado()
                                return;
                            } else {

                                const sesion = this.$store.state.sesion;
                                this.axios.post('/app/bitacora/registro_bitacora', {
                                    "info": "{\"info\":{\"TecnicoRechazo\" :" + sesion.corporativo + ",\" UsuarioSesion\" :" + sesion.corporativo + " ,\"TipoOrden\" :\"" + item.tipoorden + "\", \" Orden\" :\"" + item.orden + "\" ,\" Producto\" :\"" + item.codigo + "\" ,\" Linea\" :\"" + item.Linea + "\"  \" Observaciones\" :\"" + "**INICIO  REGISTRO RECHAZO** " + "\", \"Aplicativo\" :\"WEB\"  }}"

                                })

                                this.axios.post('/app/admision/ActualizarOrdenDetalle', {
                                        "Estado": "A",
                                        "TipoOrden": item.tipoorden,
                                        "Orden": item.orden,
                                        "Producto": item.codigo,
                                        "Linea": item.Linea,
                                        "Observaciones": this.rechazo.comentario
                                    })
                                    .then(() => {

                                        this.axios.post('/app/bitacora/registro_bitacora', {
                                                "info": "{\"info\":{\"TecnicoRechazo\" :" + sesion.corporativo + ",\" UsuarioSesion\" :" + sesion.corporativo + " ,\"TipoOrden\" :\"" + item.tipoorden + "\", \" Orden\" :\"" + item.orden + "\" ,\" Producto\" :\"" + item.codigo + "\" ,\" Linea\" :\"" + item.Linea + "\"  \" Observaciones\" :\"" + this.rechazo.comentario + "\", \"Aplicativo\" :\"WEB\"  }}"

                                            })
                                            .then(() => {
                                                this.Consulta().actualizar()
                                                this.rechazo.mostrar = false
                                                this.rechazo.comentario = ''
                                            })
                                    })
                            }
                        }).catch(() => {
                            this.Consulta().listado()
                        })
                }
            }
        },
        Touch() {
            return {
                start: (item) => {
                    this.temp = item
                },
                end: () => {
                    this.temp = 2
                }
            }
        },
        Menu(item) {
            this.tabla.map(m => m.activo = false)
            if (item) item.activo = true
        },
        ConsultarCorporativo() {
            if ((this.Vl_Corporativo == null || this.Vl_Corporativo == '') || (this.Vl_Clave == null || this.Vl_Clave == '')) {
                this.$vs.notify({
                    color: '#FFAB00',
                    title: 'Pago Productividad',
                    text: "Ingresar credenciales.",
                });
                return;
            }
            this.axios.post('/app/usuario/verificar', {
                    corporativo: this.Vl_Corporativo,
                    password: this.Vl_Clave,
                    show: true
                })
                .then(resp => {

                    if (resp.data.codigo == 0) {

                        this.GuardarOrden();
                    }
                })

        },
        BusquedaPuesto() {

            if ((this.Vl_Corporativo == null || this.Vl_Corporativo == '')) {
                return;
            }
            this.axios.post('/app/admision/validarPuesto', {
                    corporativo: this.Vl_Corporativo
                })
                .then(resp => {

                    if (resp.data.codigo == 0) {
                        resp.data.json.map(data => {
                            if (data.Existe == 1) {
                                this.ExistePuesto = true;
                            } else if (data.Existe == 0) {
                                this.ExistePuesto = false;
                            }
                        })
                    }
                })
        },
        GuardarOrden() {
            var corporativoRecibe = 0;
            var MensajeAumatico = '';
            const sesion = this.$store.state.sesion;
            if (this.ExistePuesto) {
                corporativoRecibe = this.Vl_Corporativo;
                MensajeAumatico = '**MENSAJE AUTOMATICO** INICIO REGISTRO';
            } else {
                MensajeAumatico = '**MENSAJE AUTOMATICO** EL TECNICO REALIZO:' + this.Vl_Corporativo + ' NO CONTIENE PUESTO / PAGO PRODUCTIVIDAD CONFIGURADO';
            }
            var observacionesRecibido = "Recibido por " + sesion.corporativo + ' Fecha: '
            observacionesRecibido = observacionesRecibido + this.fecha_hora_local();

            this.axios.post('/app/bitacora/registro_bitacora', {
                    "info": "{\"info\":{\"TecnicoRealizo\" :" + corporativoRecibe + ",\" UsuarioSesion\" :" + sesion.corporativo + " ,\"TipoOrden\" :\"" + this.ItemSeleccionado.tipoorden + "\", \" Orden\" :\"" + this.ItemSeleccionado.orden + "\" ,\" Producto\" :\"" + this.ItemSeleccionado.codigo + "\" ,\" Linea\" :\"" + this.ItemSeleccionado.Linea + "\"  \" Observaciones\" :\"" + MensajeAumatico + "\", \"Aplicativo\" :\"WEB\"  }}"
                })
                .then(() => {

                    return this.axios.post('/app/admision/ActualizarOrdenDetalle', {
                            "Estado": "R",
                            "TipoOrden": this.ItemSeleccionado.tipoorden,
                            "Orden": this.ItemSeleccionado.orden,
                            "Producto": this.ItemSeleccionado.codigo,
                            "Linea": this.ItemSeleccionado.Linea,
                            "RevisionTecnico": corporativoRecibe,
                            "Observaciones": observacionesRecibido
                        })
                        .then(() => {

                            this.axios.post('/app/bitacora/registro_bitacora', {
                                    "info": "{\"info\":{\"TecnicoRealizo\" :" + corporativoRecibe + ",\" UsuarioSesion\" :" + sesion.corporativo + " ,\"TipoOrden\" :\"" + this.ItemSeleccionado.tipoorden + "\", \" Orden\" :\"" + this.ItemSeleccionado.orden + "\" ,\" Producto\" :\"" + this.ItemSeleccionado.codigo + "\" ,\" Linea\" :\"" + this.ItemSeleccionado.Linea + "\"  \" Observaciones\" :\"" + "**MENSAJE AUTOMATICO: FINALIZA REGISTRO  ** " + "\", \"Aplicativo\" :\"WEB\"  }}"

                                })
                                .then(() => {
                                    this.Consulta().actualizar()
                                    this.Band_ValidarCredencial = false;
                                    this.ExistePuesto = true;
                                    this.ItemSeleccionado = '';

                                })

                        })
                })

        },
        ValidacionPre_Rechazo(item) {
            this.axios.post('/app/admision/validarEstado', {
                    "TipoOrden": item.tipoorden,
                    "Orden": item.orden,
                    "Producto": item.codigo,
                    "Linea": item.Linea
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.rechazo.comentario = '';
                        this.rechazo.item = item;
                        this.rechazo.mostrar = true
                    } else {
                        this.Consulta().listado()
                        return;
                    }
                }).catch(() => {
                    this.Consulta().listado()
                })
        },
        onRowPrepared(e) {
            e.rowElement.oncontextmenu = ($event) => this.showContextMenu($event, e.data)
        },
        calculateFilterExpression (filterValue, selectedFilterOperation) {
            const getter = function (data) {
                return `${data['tipoorden']}${data['orden']}`?.normalize('NFD').replace(/[\u0300-\u036f]/g, "")
            }
            filterValue = filterValue.normalize('NFD').replace(/[\u0300-\u036f]/g, "").replace(/\s/g, "")
            return [getter, selectedFilterOperation || "contains", filterValue]
        }

    },
    mounted() {
        document.addEventListener('click', this.hideMenu);
        this.Consulta().consultaEmpresa()
        this.ObtenerFecha();
    },
    beforeDestroy() {
      document.removeEventListener('click', this.hideMenu);
    },

    watch: {
        'ActualizacionAutomatica'() {

            this.Consulta().Actualizacion_Automatica();

        },
        'id_hospital_selec'() {

            this.Consulta().Actualizacion_Automatica();

        },
        'id_Tipo_selec'() {
            this.Consulta().Actualizacion_Automatica();

        },

    }

}
</script>

<style>
.semaforo {
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 2px
        /* margin-left: 5px; */
}

.semaforo .valor {
    padding: 10px;
    font-size: 50px;
    text-align: center;
    color: white;
    border-radius: 5px;
}

.semaforo .titulo {
    font-weight: bold;
    color: black;
    text-align: center;
    padding: 5px;
    font-size: 15px;
    flex-wrap: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    /* margin-top: 5px */
}

tr.activo {
    border: 5px solid #27AE60
}

.menu {
    background-color: white;
    border: 1px solid #ccc;
    padding: 10px;
    position: absolute;
    left: 50px;
    z-index: 1;
    left: 50%;
    border-radius: 10px;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.7);
    z-index: 52000
}

.menu div {
    padding: 10px 45px;
    cursor: pointer
}

.menu div:hover {
    background-color: #ccc;
}

.rad007-container .Etiqueta .vs-input--input.normal {
    border: 0px solid rgba(0, 0, 0, 0.4) !important;
}
.rad007-container .context-menu {
    background-color: white;
    border: 1px solid #ccc;
    padding: 10px;
    position: fixed ;
    left: 50px;
    z-index: 1;
    left: 50%;
    border-radius: 10px;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.7);
}
.rad007-container .context-menu ul {
    list-style: none;
    padding: 0;
    margin: 0;
}
  
.rad007-container .context-menu li {
    padding: 8px 12px;
    cursor: pointer;
}
  
.rad007-container .context-menu li:hover {
    background: #f0f0f0;
}

.rad007-container .dx-datagrid {
    & td {
        color:#2980B9 !important;
        vertical-align: middle !important;
        & .dx-header-filter:not(.dx-header-filter-empty){
            color: red;
        }
    }
}
.rad007-container .dx-datagrid .dx-header-row {
    background-color: transparent !important;
    > td > .dx-datagrid-text-content{
        white-space: normal;
        word-wrap: break-word;
        font-weight: bold !important;
    };
}
</style>
