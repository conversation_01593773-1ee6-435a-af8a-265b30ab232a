
<template>
<vx-card class="Clientes">
    <div class="content content-pagex">
        <vs-divider class="label-size"> AUXILIAR CLIENTES </vs-divider>
        <div class="flex-container">
            <div class=" w-full flex flex-wrap">
                <vs-col class="md:w-full lg:w-full xl:w-4/12 p-2">
                    <vx-card class="w-full">
                        <div class="w-full" align="right">
                            <div class="w-full" style="padding:5px 5px">
                                <DxButton :width="120" text="Partida" type="success" @click="GenerarPartida()" />&nbsp;
                                <DxButton :width="50" icon="pulldown" type="normal" @click="ConsultaAuxiliarG()" /> 
                            </div>
                        </div>
                        <label for="">Tipo</label>
                        <div class="w-full" style="margin-top: 5px;" >
                            <vs-col class="md:w-6/12 lg:w-6/12 xl:w-6/12 p-2">
                                <div>
                                    <vs-radio v-model="Tipo" vs-name="Tipo" vs-value="F"> Ventas</vs-radio>
                                </div>
                                <div >
                                    <vs-radio v-model="Tipo" vs-name="Tipo" vs-value="R"> Recibos</vs-radio>
                                </div>
                            </vs-col>
                            <vs-col class="md:w-6/12 lg:w-6/12 xl:w-6/12 p-2">
                                <div>
                                    <vs-radio v-model="Tipo" vs-name="Tipo" vs-value="N"> Notas de Crédito</vs-radio>
                                </div>
                                <div>
                                    <vs-radio v-model="Tipo" vs-name="Tipo" vs-value="A"> Anticipos Aplicados</vs-radio>
                                </div>
                            </vs-col>
                        </div>
                        <vs-divider></vs-divider>
                        <div class="w-full" style="margin-top: 5px">
                            <label for="">Periodo</label>
                            <DxSelectBox
                                :items="ListaPeriodos"
                                :show-clear-button="true"
                                v-model="cbPeriodos" 
                                :searchable="true" 
                                :display-expr="formatPeriodo"
                                value-expr="CodigoPeriodo"
                                :search-enabled="true"
                                :accept-custom-value="true"
                                @value-changed="guardarPeriodoCompleto"
                            />  
                        </div>
                        <div style="margin-top: 5px; display:flex;">
                            <div style="width: 100%;">
                                <label for="">Sucursal</label>
                                <DxSelectBox
                                    :items="ListaSucursal"
                                    :show-clear-button="true"
                                    v-model="cbSucursal" 
                                    :searchable="true" 
                                    :display-expr="formatSucursal"
                                    value-expr="CodigoHospital"
                                    :search-enabled="true"
                                    :accept-custom-value="true"
                                />  
                            </div>
                        </div>
                        <div style="margin-top: 10px;">
                            <vs-alert v-if="DescMayorizada === 'M'" active="true" color="warning" style="text-align: center; height: 40px;">
                                <label>Hay una partida mayorizada en este período.</label>                    
                            </vs-alert>          
                        </div>
                    </vx-card>
                </vs-col>
                <vs-col class="md:w-full lg:w-full xl:w-8/12 p-2">
                    <vx-card class="w-full" style="padding:10px 20px">
                        <vs-row class="w-full">
                            <vs-row class="md:w-6/12 lg:w-6/12 xl:w-6/12">
                                <div class="w-full">
                                    <div class="w-full" style="padding:2px" v-show="UnirV">
                                        <vs-checkbox v-model="Unir">Unir Auxiliar Clientes con Depósitos en Bancos</vs-checkbox>
                                    </div>
                                    <div style="display: flex;">
                                        <div style="padding:2px;width: 20%" v-show="FiltroV">
                                            <vs-checkbox icon-pack="feather" icon="icon-check" color="success" v-model="Filtro">
                                                Filtrar
                                            </vs-checkbox>
                                        </div>
                                        <div style="padding:2px; width: 80%;" v-show="FechaV">
                                          <DxDateBox v-model="Fecha" placeholder="Fecha" :disabled="!Filtro"   display-format="dd/MM/yyyy"/>
                                        </div>
                                    </div>
                                    <div class="w-full" style="padding:2px" v-show="FacturasContadoV">
                                        <vs-checkbox v-model="FacturasContado">Facturas Contado</vs-checkbox>
                                    </div>
                                    <div class="w-full" style="padding:2px" v-show="FacturasCreditoV">
                                        <vs-checkbox v-model="FacturasCredito">Facturas Crédito</vs-checkbox>
                                    </div>
                                    <div class="flex flex-wrap">
                                        <div class="md:w-6/12 lg:w-6/12 xl:w-6/12 px-1"> <!-- Agregado px-2 para padding horizontal -->
                                            <div class="w-full" v-show="CajaV">
                                                <label class="block mb-1">Caja</label>
                                                <DxTextBox v-model="Caja" />
                                            </div>
                                        </div>
                                        <div class="md:w-6/12 lg:w-6/12 xl:w-6/12 px-1"> <!-- Agregado px-2 para padding horizontal -->
                                            <div class="w-full" v-show="CorteV">
                                                <label class="block mb-1">Corte</label>
                                                <DxTextBox v-model="Corte" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex flex-wrap">
                                        <div class="w-full  px-1">
                                            <div class="w-full" v-show="SerieV">
                                                    <label>Serie</label>
                                                    <DxTextBox v-model="Serie" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-full" v-show="CuentaV">
                                        <ValidationProvider name="Cuentas">
                                            <label>Cuenta</label>
                                            <multiselect
                                                    v-model="cbCuentas"
                                                    :options="cuentasFormateadas"
                                                    :searchable="true"
                                                    :close-on-select="true"
                                                    :show-labels="false"
                                                    placeholder="Seleccionar Cuenta"
                                                    label="label"
                                                    track-by="CodigoCuenta"
                                                    
                                                >
                                                <span slot="noOptions">Lista no disponible.</span>
                                            </multiselect>
                                        </ValidationProvider>
                                    </div>
                                </div>
                            </vs-row>
                            <vs-row class="md:w-6/12 lg:w-6/12 xl:w-6/12" style="padding:10px 20px">
                                <div class="w-full">
                                    <div class="w-full" style="padding:10px 10px;">
                                        <div class="flex flex-wrap">
                                            <div class="w-full" style="padding:10px 10px;" >
                                                <label style="padding:10px 10px"> 1.</label>
                                                <vs-radio  v-model="IdReporte" vs-name="Reporte" vs-value="1"> Detallado por Referencia</vs-radio>
                                            </div>
                                            <div class="w-full" style="padding:10px 10px">
                                                <label style="padding:10px 10px"> 2.</label>
                                                <vs-radio  v-model="IdReporte" vs-name="Reporte" vs-value="2"> Detallado Referencia Seleccionada</vs-radio>
                                            </div>
                                            <div class="w-full" style="padding:10px 10px">
                                                <label style="padding:10px 10px"> 3.</label>
                                                <vs-radio  v-model="IdReporte" vs-name="Reporte" vs-value="3"> Cuentas Integradas</vs-radio>
                                            </div>
                                            <div class="w-full" style="padding:10px 10px">
                                                <label style="padding:10px 10px"> 4.</label>
                                                <vs-radio  v-model="IdReporte" vs-name="Reporte" vs-value="4"> Detalle por Cuenta</vs-radio>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex flex-wrap">
                                        <div class="md:w-1/7 lg:w-1/7 xl:w-1/7">
                                            <div class="w-full"  style="padding:10px 50px">
                                            <DxButton v-show="ButtonPDF" type="danger" icon="pdffile" @click="GenerarReporte('application/pdf','PDF')" text="Generar Reporte"/>
                                            &nbsp;
                                            <DxButton v-show="ButtonExcel" :width="180"  icon="tableproperties" text="Generar Reporte" type="success" @click="GenerarReporte('application/vnd.ms-excel','EXCEL')" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-full" style="padding:10px 10px" v-show="OrdenV">
                                        <vs-divider>Orden</vs-divider>

                                        <vs-col class="md:w-6/12 lg:w-6/12 xl:w-6/12 p-2" style="text-align: center;">
                                            <div>
                                                <vs-radio v-model="Orden" vs-name="Orden" vs-value="R"> Referencia</vs-radio>
                                            </div>
                                        </vs-col>
                                        <vs-col class="md:w-6/12 lg:w-6/12 xl:w-6/12 p-2"  style="text-align: center;">
                                            <div>
                                                <vs-radio v-model="Orden" vs-name="Orden" vs-value="D"> Documento</vs-radio>
                                            </div>
                                        </vs-col>
                                    </div>
                                </div>
                            </vs-row>
                        </vs-row>
                    </vx-card>
                </vs-col>

            </div>
        </div>

        <div class="w-full" align="center" v-if="DocSinCuentasV || DescPorDocumentoV">
            <div class="w-full" style="padding:10px 10px">
<!--                 <vs-button color="warning" class="label-size" type="filled" @click="OcultarInformacion()"> Ocualtar Información </vs-button>
 -->                <DxButton type="normal" text="Ocultar Información" @click="OcultarInformacion()"  />
            </div>
        </div>

        <!--GRID DE AUXILIAR SIN CUENTA-->

        <div v-if="DocSinCuentasV">
            <DxDataGrid
                :data-source="AuxiliarSinCuentas"
                :column-auto-width="true"
                :allow-column-reordering="true"
                :show-borders="true" 

            >
                <DxDataGridColumn data-field="Tipo" caption="Tipo" data-type="string" />
                <DxDataGridColumn data-field="SerieReferencia" caption="Serie Referencia" data-type="number" />
                <DxDataGridColumn data-field="Referencia" caption="Referencia" data-type="number" />
                <DxDataGridColumn data-field="Fecha" caption="Fecha" data-type="date" />
                <DxDataGridColumn data-field="Debe" caption="Debe" data-type="number" :format="{ formatter: formatMoneda }"  />
                <DxDataGridColumn data-field="Haber" caption="Haber" data-type="number" :format="{ formatter: formatMoneda }" />

            <DxDataGridDxSearchPanel :visible="true" :highlight-case-sensitive="true" />
            <DxDataGridPaging :page-size="10" />
            <DxDataGridHeaderFilter :visible="true" />
            </DxDataGrid>
        </div>

        <!--GRID DE DESCUADRE POR DOCUMENTO-->
        <div v-if="DescPorDocumentoV">
            <DxDataGrid
                :data-source="DescPorDocumento"
                :column-auto-width="true"
                :allow-column-reordering="true"
                :show-borders="true" 
            >
                <DxDataGridColumn data-field="Tipo" caption="Tipo" data-type="string" />
                <DxDataGridColumn data-field="Referencia" caption="Referencia" data-type="number" />
                <DxDataGridColumn data-field="Fecha" caption="Fecha" data-type="date" />
                <DxDataGridColumn data-field="SumaDebe" caption="Debe" data-type="number" :format="{ formatter: formatMoneda }"  />
                <DxDataGridColumn data-field="SumaHaber" caption="Haber" data-type="number" :format="{ formatter: formatMoneda }" />

            <DxDataGridDxSearchPanel :visible="true" :highlight-case-sensitive="true" />
            <DxDataGridPaging :page-size="10" />
            <DxDataGridHeaderFilter :visible="true" />
            
            </DxDataGrid>
        </div>


        <DxDataGrid
            :data-source="AuxiliarClientes"
            :show-borders="true"
            :column-auto-width="true"
            key-expr="CompositeKey" 
            :allow-column-reordering="true"
            ref="gridDetalle"
            style="margin-top: 5px;"
            @editor-preparing="onEditorPreparing"
            @init-new-row="initNewRow"
            @row-removing="onRowRemoving"
            :focused-row-enabled="true"
            :focused-row-key="focusedRowKey"
            @focused-row-changing="onFocusedRowChanging"
            @row-prepared="onRowPrepared"
        >

            <DxDataGridSelection mode="single" />
            <DxDataGridColumn data-field="CompositeKey" :visible="false"  data-type="string" :allow-editing="false"/>
            <DxDataGridColumn data-field="CorrelativoInterno" :visible="false"  data-type="number" :allow-editing="false" />
            <DxDataGridColumn data-field="Indice" :visible="false"  caption="Indice" data-type="number" :allow-editing="false"  />
            <DxDataGridColumn data-field="Tipo"  :visible="false"  caption="Tipo" data-type="string" :allow-editing="false"/>
            <DxDataGridColumn data-field="Status" caption="Status" data-type="string" :allow-editing="false"/>
            <DxDataGridColumn data-field="Fecha" caption="Fecha" data-type="date" :format="'dd/MM/yyyy'" />
            <DxDataGridColumn data-field="SerieFace" caption="Serie" data-type="string" :allow-editing="false"  />
            <DxDataGridColumn data-field="NumeroFacE" caption="Documento" data-type="number"   :allow-editing="false"/>
            <DxDataGridColumn data-field="SerieReferencia" caption="Serie Referencia" data-type="string"/>
            <DxDataGridColumn data-field="Referencia" caption="Referencia" data-type="number" />
            <DxDataGridColumn data-field="Documento" caption="Doc.Asociado" data-type="string" />
            <DxDataGridColumn data-field="Cuenta" alignment="center" data-type="string"  />
            <DxDataGridColumn  data-field="NombreCuenta" caption="Nombre de la cuenta" data-type="string" :allow-editing="false" />
            <DxDataGridColumn data-field="Debe" caption="Debe" data-type="number" :format="{ formatter: formatMoneda }" :editor-options="{ min: 0 }" />
            <DxDataGridColumn data-field="Haber" caption="Haber" data-type="number" :format="{ formatter: formatMoneda }" :editor-options="{ min: 0 }" />
            <DxDataGridColumn data-field="Caja" caption="Caja" data-type="number" />
            <DxDataGridColumn data-field="Corte" caption="Corte" data-type="number" />
            <DxDataGridDxSearchPanel :visible="true" :highlight-case-sensitive="true" />
            <DxDataGridPaging :page-size="10" />
            <DxDataGridHeaderFilter :visible="true"  :allow-search="true" />
              <DxDataGridEditing 
                mode="cell"
                :allow-adding="allowAdding"
                :allow-deleting="allowAdding"
                :allow-updating="allowUpdating"
                :use-icons="true"
            />
        </DxDataGrid>
    </div>  

    <DxPopup :visible.sync="PartidaDiario" title="Generar Partida de Diario" height="480" width="500">
        <form action="">
            <vs-divider class="label-size"> OPCIONES </vs-divider>
            <vs-radio v-model="OpcionSel" vs-name="OpcionSel" vs-value="1"> Nueva Partida </vs-radio>
            <DxTextBox v-model="NuevaPartida" />
            <label> Fecha </label>
            <div style="width: 100%;">
                <flat-pickr style="width: 100%;"
                    v-model="FechaPartida"
                    :config="configFromdateTimePicker"
                    class="devextreme-like-datepicker"
                />
            </div>
            <label>Referencia </label>
             <DxTextBox :value="Referencia" :show-spin-buttons="true" v-model="Referencia" />
            <label for="">Descripción</label>
            <vs-textarea rows="3" class="w-full" v-model="Descripcion" />
            <div style="text-align: center;">
                <DxButton :width="120" text="Ok" type="success" @click="ResumenQuery()" />
            </div>
        </form>
    </DxPopup>

</vx-card>
</template>

<script>
import Multiselect from 'vue-multiselect'
import "vue-multiselect/dist/vue-multiselect.min.css"
import flatPickr from 'vue-flatpickr-component';
import 'flatpickr/dist/flatpickr.css';
import {
    Spanish as SpanishLocale
} from 'flatpickr/dist/l10n/es.js';

export default {
    components: {
        Multiselect,
        flatPickr
    },
    data() {
        return {
            cbPeriodos: '',
            ListaPeriodos: [],
            CodigoPeriodo: '',
            DescPeriodo: '',
            Fecha: '',
            FechaPartida: '',
            FechaF: '',
            FechaV: true,
            OrdenV: true,
            Referencia: null,
            Cuenta: '',
            CuentaV: true,
            Descripcion: '',
            OpcionSel: 1,
            NuevaPartida:null,
            ButtonExcel:true,
            Bloqueo1: false,
            Bloqueo2: true,
            Periodo: '',
            Tipo: 'F',
            TipoDoc: 1,
            cbCuentas: {
                CodigoCuenta: null,
                Nombre: null
            },
            isAddingRows: false, // Controla si ya se está agregando filas
            ListaCuentas: [],
            CodCuenta: '',
            NombreCuenta: '',
            Unir: false,
            UnirV: false,
            Filtro: false,
            FiltroV: true,
            FacturasContado: false,
            FacturasContadoV: true,
            FacturasCredito: false,
            FacturasCreditoV: true,
            Caja: null ,
            CajaV: true,
            Corte: null,
            CorteV: true,
            Serie: '',
            SerieV: false,
            IdReporte: 0,
            AuxiliarClientes: [],
            ListadoReportes: [],
            CorrelativoPartida: null,
            FechaIPeriodoA: '',
            PartidaDiario: false,
            DocSinCuentas: [],
            DocSinCuentasV: false,
            DescPorDocumento: [],
            DescPorDocumentoV: false,
            Orden: '',
            ValidaMayorizacion: 'S',
            DescMayorizada: null,
            DescMayorizadaV: false,
            InsertaV: false,
            ModificarV: false,
            ListaSucursal: [],
            cbSucursal: '',
            ListaFace: [],
            DatosPeriodo:[],
            selectedRowData: null,
            allowAdding: false,
            ReferenciaSel: null,
            configFromdateTimePicker: {
                minDate: '',
                maxDate: '',
                locale: SpanishLocale,
                dateFormat: "d/m/Y",
            },
            filaSeleccionada:null,
            DatosAnteriores: [],
            AuxiliarSinCuentas:[],
            focusedRowKey: null,
            ButtonPDF: false 
        }
    },
    mounted() {
        if (this.periodoSeleccionado && this.periodoSeleccionado.CodigoPeriodo) {
            this.ConsultaAuxiliarG();
            this.Mayorizar();
            this.limpiarFiltrosDetalle()
        }
        this.IdReporte = '1'
        //this.Face(),
        this.ConsultaPeriodo(),
        this.ConsultaSucursal(),
        this.ConsultasCuentas()
        this.ListadoReportes = this.$recupera
        this.DescPorDocumentoV = false
        this.DocSinCuentasV = false
    },
        
    watch: {
        'Tipo'() {
            if (this.Tipo == 'F') {
                this.TipoDoc = 1
                //this.Face()                                   
                this.ConsultaAuxiliarG()
                this.limpiarFiltrosDetalle()
            }
            if (this.Tipo == 'R' || this.Tipo == 'A') {
                this.TipoDoc = ''
                //this.Face()
                this.ConsultaAuxiliarG()
                this.limpiarFiltrosDetalle()

            }
            if (this.Tipo == 'N') {
                this.TipoDoc = 2
                //this.Face()
                this.ConsultaAuxiliarG()
                this.limpiarFiltrosDetalle()

            }

        },
        'IdReporte'() {
            if (this.IdReporte == '1') {
                this.UnirV = true
                this.FiltroV = true
                this.FechaV = true
                this.FacturasContadoV = false
                this.FacturasCreditoV = false
                this.CajaV = true
                this.CorteV = true
                this.CuentaV = false
                this.SerieV = true
                this.OrdenV = false
                this.ButtonExcel = true
                this.ButtonPDF = false 
                this.clearSelectedRow()
                this.limpiarCuenta()
            }
            if (this.IdReporte == '2') {
                this.UnirV = false
                this.FiltroV = false
                this.FechaV = false
                this.FacturasContadoV = false
                this.FacturasCreditoV = false
                this.CajaV = false
                this.CorteV = false
                this.CuentaV = false
                this.SerieV = false
                this.OrdenV = false
                this.ButtonExcel = false
                this.ButtonPDF = true
                this.clearSelectedRow()
                this.limpiarCuenta()

            } else if (this.IdReporte == '3') {
                this.UnirV = true
                this.FiltroV = true
                this.FechaV = true
                this.FacturasContadoV = false
                this.FacturasCreditoV = false
                this.CajaV = true
                this.CorteV = true
                this.SerieV = false
                this.CuentaV = true
                this.OrdenV = false
                this.ButtonExcel = false
                this.ButtonPDF = true
                  this.clearSelectedRow()
                  this.limpiarCuenta()
            } else if (this.IdReporte == '4') {
                this.UnirV = true
                this.FiltroV = true
                this.FechaV = true
                this.FacturasContadoV = true
                this.FacturasCreditoV = true
                this.CajaV = true
                this.CorteV = true
                this.SerieV = false
                this.CuentaV = true
                this.OrdenV = true
                this.ButtonExcel = true
                this.ButtonPDF = false
                this.clearSelectedRow()
                this.limpiarCuenta()
            }
        },
        'OpcionSel'() {
            if (this.OpcionSel == '1') {
                this.Bloqueo1 = false,
                    this.Bloqueo2 = true

            } else {
                this.Bloqueo1 = true,
                    this.Bloqueo2 = false
            }
        },
        Filtro(nuevoValor) {
            if (nuevoValor) {
                // Si el checkbox está activado, se asigna la fecha actual
                this.Fecha = this.periodoSeleccionado.FechaInicial  // Formato 'YYYY-MM-DD'
                
            } else {
                // Si está desactivado, se borra la fecha
                this.Fecha = null;
            }
        },
        
    },
    computed:{
        cuentasFormateadas() {
            return this.ListaCuentas.map(cuenta => ({
            ...cuenta,
            label: `${cuenta.CodigoCuenta} - ${cuenta.Nombre}`
            }));
        }
    },
    methods: {
        allowUpdating(e) {
            return e.row.data.Status !== 'A';
        },

        async onFocusedRowChanging(e) {
            const grid = this.$refs.gridDetalle.instance;
            await this.$nextTick(); // Esperar a que Vue actualice el estado
            
            const visibleRows = grid.getVisibleRows();
            if (e.newRowIndex < 0 || e.newRowIndex >= visibleRows.length) return;
            
            const row = visibleRows[e.newRowIndex];
            if (!row?.key) return;
            
            // Asignar la referencia de la fila seleccionada
            this.ReferenciaSel = row.data.Referencia;
            
            this.focusedRowKey = row.key;
            grid.selectRows([row.key], false);
            this.selectedRowData = row.data;
            this.allowAdding = true;
        },

        async clearSelectedRow() {
            const grid = this.$refs.gridDetalle.instance;
            
            // Limpiar la selección
            grid.clearSelection();
            
            // Limpiar la fila enfocada
            grid.option("focusedRowKey", null);
            
            // Limpiar tus variables de estado
            this.focusedRowKey = null;
            this.selectedRowData = null;
            this.ReferenciaSel = null;
            this.allowAdding = false;
        },

        initNewRow(e) {
            this.isAddingNewRow = true;
            
            if (this.selectedRowData) {
                // Asignar los valores de la fila seleccionada
                e.data.Tipo = this.selectedRowData.Tipo;
                e.data.Fecha = this.selectedRowData.Fecha;
                e.data.SerieFace = this.selectedRowData.SerieFace;
                e.data.NumeroFacE = this.selectedRowData.NumeroFacE;
                e.data.SerieReferencia = this.selectedRowData.SerieReferencia;
                e.data.Referencia = this.selectedRowData.Referencia;
                e.data.Documento = this.selectedRowData.Documento;
                
                // Obtener todas las filas existentes
                const gridInstance = this.$refs.gridDetalle.instance;
                const allRows = gridInstance.getDataSource().items();
                
                // Filtrar filas con la misma referencia
                const sameReferenceRows = allRows.filter(row => 
                    row.Referencia === this.selectedRowData.Referencia
                );
                
                // Encontrar el máximo correlativo e índice
                const maxCorrelativo = sameReferenceRows.reduce((max, row) => 
                    Math.max(max, row.CorrelativoInterno || 0), 0);

                const IndiceMax = sameReferenceRows.reduce((max, row) => 
                    Math.max(max, row.Indice || 0), 0);
                
                // Asignar nuevos valores
                e.data.CorrelativoInterno = maxCorrelativo + 1;
                e.data.Indice = IndiceMax + 1;
                
                // Generar el CompositeKey usando el mismo formato que en agregarCorrelativoPorReferencia
                e.data.CompositeKey = `${e.data.CorrelativoInterno}_${e.data.Referencia}`;
            } else {
                // Si no hay fila seleccionada, establecer valores por defecto
                e.data.CorrelativoInterno = 1;
                e.data.Indice = 1;
                e.data.CompositeKey = `1_${e.data.Referencia || 'nueva'}`;
            }

            // Establecer valores por defecto para campos editables
            e.data.Debe = e.data.Debe || 0;
            e.data.Haber = e.data.Haber || 0;
            e.data.Corte = e.data.Corte || '';
            e.data.Caja = e.data.Caja || '';
            
            this.$nextTick(() => {
                const grid = this.$refs.gridDetalle.instance;
                grid.refresh();
                grid.clearSelection();
                
                if (this.selectedRowData) {
                    grid.selectRows([this.selectedRowData.key], false);
                    grid.focus(this.selectedRowData.key);
                    this.ReferenciaSel = this.selectedRowData.Referencia;
                }
                
                // Enfocar y editar la celda de "Cuenta" en la nueva fila
                const newRowIndex = grid.getVisibleRows().findIndex(row => row.isNewRow);
                if (newRowIndex !== -1) {
                    setTimeout(() => {
                        grid.editCell(newRowIndex, 'Cuenta');
                    }, 100);
                }
                
                this.isAddingNewRow = false;
            });
        },

        onEditorPreparing(e) {
            const camposMonitoreados = ["Fecha", "SerieReferencia", "Referencia", "Documento", "Debe", "Haber", "Corte", "Caja", "Cuenta"];

            if (camposMonitoreados.includes(e.dataField) && e.parentType === "dataRow") {
                const valorAnterior = e.row.data[e.dataField];
                const defaultValueChangeHandler = e.editorOptions.onValueChanged;

                e.editorOptions.onValueChanged = async (args) => {
                    const grid = this.$refs.gridDetalle.instance;

                    // Si es el campo Cuenta, validarlo primero
                    if (e.dataField === "Cuenta") {
                        const cuenta = args.value;
                        const previousCuenta = grid.cellValue(e.row.rowIndex, "Cuenta");
                        const previousNombreCuenta = grid.cellValue(e.row.rowIndex, "NombreCuenta");

                        if (!cuenta) {
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#e74c3c',
                                title: 'Campo requerido',
                                acceptText: 'Aceptar',
                                text: 'Debe ingresar un valor para la cuenta',
                                buttonCancel: 'border',
                                accept: () => {},
                            });
                            grid.cellValue(e.row.rowIndex, "Cuenta", previousCuenta);
                            grid.cellValue(e.row.rowIndex, "NombreCuenta", previousNombreCuenta);
                            grid.refresh();
                            return;
                        }

                        try {
                            const resp = await this.axios.post("/app/v1_contabilidad_general/BusquedaCuentas", {
                                Opcion: 11,
                                Contabilidad: cuenta,
                            });

                            if (resp.data.codigo === 0 && resp.data.json?.length > 0) {
                                grid.cellValue(e.row.rowIndex, "NombreCuenta", resp.data.json[0].Nombre);
                            } else {
                                grid.cellValue(e.row.rowIndex, "Cuenta", previousCuenta);
                                grid.cellValue(e.row.rowIndex, "NombreCuenta", previousNombreCuenta);
                                grid.refresh();
                                this.$vs.dialog({
                                    type: 'alert',
                                    color: '#e74c3c',
                                    title: 'Error Cuenta',
                                    acceptText: 'Aceptar',
                                    text: 'La cuenta no es válida, verificar',
                                    buttonCancel: 'border',
                                    accept: () => {},
                                });
                                return;
                            }
                        } catch (error) {
                            grid.cellValue(e.row.rowIndex, "Cuenta", previousCuenta);
                            grid.cellValue(e.row.rowIndex, "NombreCuenta", previousNombreCuenta);
                            grid.refresh();
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#e74c3c',
                                title: 'Error',
                                acceptText: 'Aceptar',
                                text: 'Ocurrió un error al validar la cuenta',
                                buttonCancel: 'border',
                                accept: () => {},
                            });
                            return;
                        }
                    }

                    if (defaultValueChangeHandler) {
                        defaultValueChangeHandler(args);
                    }

                    // Verificar si es nueva fila
                    if (e.row.isNewRow && !e.row.data.Indice) {
                        return;
                    }

                    // Ejecutar actualización
                    const datosFilaActualizados = e.row.data;
                    try {
                        await this.axios.post(this.$store.state.global.url + 'app/v1_contabilidad_general/UpdateAuxiliarClientes', {
                            Opcion: "U",
                            Fecha: datosFilaActualizados.Fecha,
                            SeireReferencia: datosFilaActualizados.SerieReferencia,
                            Referencia: datosFilaActualizados.Referencia,
                            Documento: datosFilaActualizados.Documento,
                            Cuenta: datosFilaActualizados.Cuenta,
                            Debe: datosFilaActualizados.Debe,
                            Haber: datosFilaActualizados.Haber,
                            Caja: datosFilaActualizados.Caja,
                            Corte: datosFilaActualizados.Corte,
                            Periodo: this.periodoSeleccionado.CodigoPeriodo,
                            Tipo: this.selectedRowData.Tipo,
                            serie: this.selectedRowData.SerieReferencia,
                            Codigo: this.selectedRowData.Referencia,
                            Indice: datosFilaActualizados.Indice
                        });
                    } catch (error) {
                        e.component.cellValue(e.row.rowIndex, e.dataField, valorAnterior);
                        this.$vs.notify({
                            time: 3500,
                            title: 'Error',
                            text: 'No se pudo actualizar el registro',
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        });
                    }
                };
            }
        },

        clearGridSelection() {
            // Accede a la instancia del DataGrid y limpia la selección
            this.$refs.gridDetalle.instance.clearSelection();
            this.ReferenciaSel = null; // Limpia también tu variable local
        },

    
        formatMoneda(value) {
            // Si el valor es 0 (o equivalente), retorna vacío
            if (value === 0 || value === 0.00 || value === "0.00") {
                return "";
            }
            
            // Si es null o undefined, retorna "Q 0.00" (como en tu lógica original)
            if (value === null || value === undefined) {
                return "Q 0.00";
            }

            // Formato normal para números distintos de cero
            const formatted = `Q ${Math.abs(value).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",")}`;
            return value < 0 ? `<span style="color: red;">-${formatted}</span>` : formatted;
        },
        
        OcultarInformacion() {
            this.DocSinCuentasV = false
            this.DescPorDocumentoV = false
        },

        onRowPrepared(e) {
            if (e.rowType === 'data' && e.data.Status === 'A') {
                e.rowElement.style.backgroundColor = '#fadbd8'; // Puedes usar '#00FFFF' si prefieres
            }
        },

        //PERIODOS
        ConsultaPeriodo() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/ConsultaPeriodo', {
                    Opcion: "C",
                    SubOpcion: "6",
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaPeriodos = resp.data.json;
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido cargar los periodos, favor validar.',
                        })
                    }
                    this.ConsultaPeriodoActual();
                })
                .catch(() => {})
        },

        ConsultaPeriodoActual() {
            const url = this.$store.state.global.url;
            const endpoint = url + 'app/v1_contabilidad_general/ConsultaPeriodoActual';
            
            
            this.axios.post(endpoint, {
                Opcion: "C",
                SubOpcion: "2",
            })
            .then(resp => {
                
                if (resp.data.codigo == 0) {
                    
                    this.DatosPeriodo = resp.data.json


                    const codigoPeriodoActual = resp.data.json[0].CodigoPeriodo;

                    
                    // Buscar el periodo en this.ListaPeriodos
                    const periodo = this.ListaPeriodos.find(p => p.CodigoPeriodo === codigoPeriodoActual);
                    
                    if (periodo) {
                        this.cbPeriodos = periodo.CodigoPeriodo;
                    }
                    
                    
                } else {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Cuentas',
                        text: 'No se ha podido cargar los periodos, favor validar.',
                    });
                }
            })
            .catch(() => {
            });
        },

onChangePeriodos(value) {
    if (value !== null && value.length !== 0) {
        this.CodigoPeriodo = value.CodigoPeriodo;
        this.DescPeriodo = value.DescPeriodo;

        // Establecer FechaMin y FechaMax
        this.FechaMin = new Date(value.FechaInicial);

        let fechaFinal = new Date(value.FechaFinal);
        fechaFinal.setDate(fechaFinal.getDate() + 1); // sumar 1 día para incluir fecha final
        this.FechaMax = fechaFinal;

        // Configurar flatpickr con minDate, maxDate y enable
        this.configFromdateTimePicker.minDate = this.FechaMin;
        this.configFromdateTimePicker.maxDate = this.FechaMax;
        this.configFromdateTimePicker.enable = [
            (date) => date >= this.FechaMin && date <= this.FechaMax
        ];

        // Obtener el último día del mes de FechaFinal
        let fechaFinalMes = new Date(value.FechaFinal);
        let ultimoDiaMes = new Date(fechaFinalMes.getFullYear(), fechaFinalMes.getMonth() + 1, 0);

        // Asignar último día del mes a FechaPartida
        this.FechaPartida = ultimoDiaMes;

    } else {
        // Reset de los campos
        this.CodigoPeriodo = '';
        this.DescPeriodo = '';
        this.NuevaPartida = '';
        this.Fecha = new Date(); // o ''
        this.FechaMin = '';
        this.FechaMax = '';
        this.configFromdateTimePicker.enable = [];
    }
},

        
        formatPeriodo(item){
            if (!item) return '';
            return `${item.CodigoPeriodo} - ${item.DescPeriodo}`;
        },

        formatSucursal(item){
            if (!item) return '';
            return `${item.Hospital} - ${item.NombreHospital}`;
        },
        
        ConsultaSucursal() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/ConsultaCuentas', {
                    Opcion: 'C',
                    SubOpcion: '2'
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.ListaSucursal = [];
                    } else {
                        this.ListaSucursal = resp.data.json;
                        this.addTodas()
                    }
                })
        },

        onChangeSucursal(value) {
            //Si existe seleccionada un elemento
            if (value !== null && value.length !== 0) {
                this.CodHospital = value.CodigoHospital;
                this.NombreHospital = value.NombreHospital;

            } else {
                this.CodHospital = '';
                this.NombreHospital = ''
            }
        },

        addTodas() {
            this.ListaSucursal.unshift({
                Hospital: 'T',
                NombreHospital: 'Todas'
            });
            this.cbSucursal = {
                Hospital: 'T',
                NombreHospital: 'Todas'
            }
        },


        guardarPeriodoCompleto(e) {
            let codigoSeleccionado = e.value;
            this.periodoSeleccionado = this.ListaPeriodos.find(
                (periodo) => periodo.CodigoPeriodo === codigoSeleccionado
            );
            
            if (this.periodoSeleccionado) {
                this.ConsultaAuxiliarG(); //Consulta Información del perido
                this.Mayorizar();         //Consulta si ahi una partida con mayorizacion 
                this.limpiarFiltrosDetalle(); //Limpia Filtros, Busqueda datagrid y pagineo 1
                this.DocSinCuentasV = false
                this.DescPorDocumentoV = false
            }
        },

        ConsultaAuxiliarG() {
            const url = this.$store.state.global.url;
            this.axios.post(url + 'app/v1_contabilidad_general/ConsultaAuxiliarC', {
                    Opcion: 'C',
                    SubOpcion: '2',
                    TipoPartida: this.Tipo,
                    Periodo: this.periodoSeleccionado.CodigoPeriodo,
                    TipoDoc: this.TipoDoc,
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        // Procesar los datos antes de agregar correlativo
                        const processedData = resp.data.json.map(item => {
                            // Si NumeroFacE o SerieFace están vacíos o son null, usar Referencia y SerieReferencia
                            if (!item.NumeroFacE || !item.SerieFace) {
                                return {
                                    ...item,
                                    NumeroFacE: item.NumeroFacE || item.Referencia || '',
                                    SerieFace: item.SerieFace || item.SerieReferencia || ''
                                };
                            }
                            return item;
                        });
                        
                        this.AuxiliarClientes = this.agregarCorrelativoPorReferencia(processedData);
                        
                    }
                    this.limpiarCuenta()
                })
                .catch(() => {
                });
        },

        agregarCorrelativoPorReferencia(data) {
            // Objeto para llevar el conteo por referencia
            const conteoReferencias = {};
            
            return data.map(item => {
                const referencia = item.Referencia;
                
                // Inicializar contador si es la primera vez que aparece la referencia
                if (!conteoReferencias[referencia]) {
                    conteoReferencias[referencia] = 0;
                }
                
                // Incrementar el contador para esta referencia
                conteoReferencias[referencia] += 1;
                
                // Asignar el correlativo y crear el CompositeKey
                return {
                    ...item,
                    CorrelativoInterno: conteoReferencias[referencia],
                    CompositeKey: `${conteoReferencias[referencia]}_${referencia}`
                };
            });
        },

        //MAYORIZAR
           Mayorizar() {
            const url = this.$store.state.global.url;
            this.axios.post(url + 'app/v1_contabilidad_general/Mayorizacion_SM', {
                    Opcion: 'C',
                    SubOpcion: '1',
                    Periodo: this.periodoSeleccionado.CodigoPeriodo
                })
                .then(resp => {
                    this.DescMayorizada = null
                    if (resp.data.codigo === 0) {
                        this.DescMayorizada = resp.data.json[0].DescripcionM;
                    }
                })
                .catch(() => {
                });
        },
        //Cuentas
        ConsultasCuentas() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/ConsultasCuentas', {
                    Opcion: 'C',
                    SubOpcion: '1',
                    Activa: 'S'
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaCuentas = resp.data.json
                    } else {
                        this.ListaCuentas = []
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Auxiliar Bancos',
                            text: 'No se ha podido cargar las cuentas, favor validar.',
                        })
                    }
                })

        },

        onChangeCuenta(value) {

            if (value !== null && Object.keys(value).length !== 0) {
            this.cbCuentas = {
                CodigoCuenta: value.CodigoCuenta,
                Nombre: value.Nombre
            };
            } else {
            this.cbCuentas = {
                CodigoCuenta: null,
                Nombre: null
            };
            }
        },

        CuentaSeleccionado({
            CodigoCuenta,
            Nombre
        }) {
            return `${CodigoCuenta} - ${Nombre} `
        },

        //ReportesFiltros
        //Reporte 1 
        async GenerarReporte(formatoInterno, formato) {
            let nombreReporte = '';
            let OpcionInterna = this.IdReporte

            if (this.IdReporte == 1) {
                nombreReporte = 'ReporteDetalladoPorRefC'
                this.ReferenciaSel = ''
            } else if (this.IdReporte == 2) {
                 const necesitaReferencia = this.IdReporte == 2 && !this.Todos;
                if (necesitaReferencia && (!this.ReferenciaSel || Object.keys(this.ReferenciaSel).length === 0)) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Error al generar el reporte',
                        text: 'Seleccione una referencia antes de generar el Reporte.',
                    });
                    return;
                }
                nombreReporte = 'ReporteDetalladoPorRefSelC'
                OpcionInterna = 1
            } else if (this.IdReporte == 3) {
                nombreReporte = 'ReporteCuentasIntegradasC'
                OpcionInterna = 2
            } else if (this.IdReporte == 4) {
                nombreReporte = 'ReporteDetallePorCuentaC'
                OpcionInterna = 3
            }


            let postData = {
                nombrereporte: nombreReporte,
                tiporeporte: formatoInterno,
                Opcion: "C",
                SubOpcion: OpcionInterna,
                TipoPartida: this.Tipo,
                Periodo: this.periodoSeleccionado.CodigoPeriodo,
                TipoDoc: this.TipoDoc,
                Caja: this.Caja,
                Corte: this.Corte,
                Serie: this.Serie,
                Cuenta: this.cbCuentas.CodigoCuenta,
                Referencia: this.ReferenciaSel,
                Fecha: this.Fecha,
                Unir: this.Unir == true ? 'S' : 'N',
                CheckFacturas: this.FacturasContado == true ? 'CO' : '' || this.FacturasCredito == true ? 'CR' : '',
                Orden: this.Orden,
                Sucursal: this.Sucursal
            }

            this.$reporte_modal({
                Nombre: 'ReportesAuxiliarClientes',
                Opciones: {
                    ...postData
                },
                Formato: formato
            })
        },

        GenerarPartida() {
            if (this.Fecha >= this.FechaIPeriodoA) {
                this.AuxiliarSinCuenta()
                this.Correlativo()
            } else {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Generar Partida de Diario',
                    text: 'La Fecha seleccionada es menor a la fecha del periodo actual.',
                })
                return;
            }
        
            const info = this.periodoSeleccionado

            if (info && info.CodigoPeriodo) {
                // Llamar a onChangePeriodos solo si la información es válida
                this.onChangePeriodos(info)
            } else {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#e74c3c',
                    title: 'Valores incorrectos',
                    acceptText: 'Aceptar',
                    text: 'No hay información válida en periodoSeleccionado',
                    buttonCancel: 'border',
                    accept: () => {},
                })
                return
            }
        },
        AuxiliarSinCuenta() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/ConsultaAuxiliarC', {
                    Opcion: 'C',
                    SubOpcion: '6',
                    Periodo: this.periodoSeleccionado.CodigoPeriodo
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.AuxiliarSinCuentas = resp.data.json
                        
                        if (this.AuxiliarSinCuentas.length > 0) {
                            this.DocSinCuentasV = true
                            this.$vs.dialog({
                                    type: 'alert',
                                    color: '#e74c3c',
                                    title: 'Error al Generar Partida de Diario',
                                    acceptText: 'Aceptar',
                                    text: 'Por favor, revisar los documentos sin cuenta o con cuenta no validada.',
                                    buttonCancel: 'border',
                                    accept: () => {},
                            })
                            return;
                        }
                    }
                    this.DescuadrePorDoc()
                })
        },

        DescuadrePorDoc() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/ConsultaAuxiliarC', {
                    Opcion: 'C',
                    SubOpcion: '7',
                    Periodo: this.periodoSeleccionado.CodigoPeriodo

                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.DescPorDocumento = resp.data.json

                        if (this.DescPorDocumento.length > 0) {
                            this.DescPorDocumentoV = true
                                this.$vs.dialog({
                                    type: 'alert',
                                    color: '#e74c3c',
                                    title: 'Error al Generar Partida de Diario',
                                    acceptText: 'Aceptar',
                                    text: 'Partida total no cuadra por más de un centavo, a continuacion se detalla el total de los no cuadrados.',
                                    buttonCancel: 'border',
                                    accept: () => {},
                                })
                            return;
                        } else {
                            this.OpcionSel = 1
                            this.PartidaDiario = true
                            this.Periodo = this.cbPeriodos.CodigoPeriodo
                            this.DescPorDocumentoV = false
                        }
                    }
                })

        },


        Correlativo() {
            const url = this.$store.state.global.url;
            this.axios.post(url + 'app/v1_contabilidad_general/CorrelativoCorrecto', {
                Opcion: "C",
                SubOpcion: "8",
                Periodo: this.periodoSeleccionado.CodigoPeriodo,
            }).then((resp) => {
                if (resp.data && resp.data.json && resp.data.json.length > 0) {
                    const corr = resp.data.json[0].CorrelativoPartidas;
                    this.NuevaPartida = corr === '' ? '1' : corr; // ahora es string
                }
            }).catch(() => {
            });
        },


        ResumenQuery() {
            if (this.Partida == 0 || this.Partida == '') {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Generar Partida de Diario',
                    text: 'Ingrese Número de partida.',
                })
                return;
            }
            if (this.Periodo == 0 || this.Periodo == '') {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Generar Partida de Diario',
                    text: 'Selecciones el periodo.',
                })
                return;
            } else {
                const url = this.$store.state.global.url
                let fechaFormateada = null;
                if (this.FechaPartida) {
                    const [day, month, year] = this.FechaPartida.split('/');
                    const fechaObj = new Date(`${year}-${month}-${day}`);
                    
                    // Verificar si la fecha es válida
                    if (!isNaN(fechaObj.getTime())) {
                        fechaFormateada = fechaObj.toISOString().split('T')[0].replace(/-/g, '/');
                    }
                }
                this.axios.post(url + 'app/v1_contabilidad_general/TranAuxiliaresC', {
                        Opcion: "I",
                        SubOpcion: "1",
                        Periodo: this.periodoSeleccionado.CodigoPeriodo,
                        Partida: this.NuevaPartida,
                        Fecha: fechaFormateada,
                        Descripcion: this.Descripcion,
                    })
                    .then(() => {
                        this.PartidaDiario = false
                        this.Descripcion = null
                    }).catch(()=>{
                         this.Descripcion = null
                          this.PartidaDiario = false
                    })
            }

        },




        onRowRemoving(e) {
            e.cancel = true; // Cancela la eliminación automática del DataGrid
            const rowData = e.data;
            // Parámetros para la API
            const payload = {
                Opcion: "E",
                Periodo: this.periodoSeleccionado.CodigoPeriodo,
                Tipo: this.selectedRowData.Tipo,
                SeireReferencia: e.data.SerieReferencia,
                Referencia: e.data.Referencia,
                Indice: e.data.Indice
            };

            // Llamada API para eliminar
            this.axios.post(
                `${this.$store.state.global.url}app/v1_contabilidad_general/EliminarAuxiliarClientes`,
                payload
            )
            .then(() => {
                // Si la API responde éxito, eliminamos la fila del DataGrid manualmente
                const index = this.AuxiliarClientes.findIndex(
                    item => item.CompositeKey === rowData.CompositeKey
                );
                
                if (index !== -1) {
                    this.AuxiliarClientes.splice(index, 1); // Eliminación del array 
                }
                this.$vs.notify({
                    text: 'Eliminación Exitosa.',
                    Color: 'danger'
                });
                return;

            })
            .catch(() => {

            });
        },
      
    limpiarFiltrosDetalle() {
        const gridInstance = this.$refs.gridDetalle.instance;
        if (gridInstance) {
            gridInstance.clearFilter();                       // Limpia los filtros de columna
            gridInstance.option('searchPanel.text', '');      // Limpia búsqueda
            gridInstance.option('paging.pageIndex', 0);       // Vuelve a la página 1 (índice 0)
           
        }
    },

    limpiarCuenta() {
            this.cbCuentas = {
                CodigoCuenta: null,
                Nombre: null
            };
            this.onChangeCuenta(this.cbCuentas);
    }

    }
}
</script>   

<style scoped>
.footer {
    height: 70px;
    margin-top: -65px;
    margin-left: 10px;
}

.label-size {
    font-size: 14px;
    font-weight: bold;
}

.label-StatusM {
    font-size: 14px;
    font-weight: bold;
    color: darkred;
}

.label-i {
    font-size: 18px;
    font-weight: bold;
    color: rgb(240, 158, 6);
}

.cuadro-i {
    background: #faf2f1;
}

.flex-container {
    display: flex;
    flex-direction: row;
}

.padre {
    position: relative;
}

.hijo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.radio-custom {
  --vs-radio-border-color: #d1d5db;
  --vs-radio-background-color-checked: #4f46e5;
  --vs-radio-border-color-checked: #4f46e5;
}

.radio-custom:hover {
  --vs-radio-border-color: #9ca3af;
}

.vs-radio--text {
  font-size: 0.9375rem;
}

.radio-custom {
  --vs-radio-border-color: #d1d5db;
  --vs-radio-background-color-checked: #3b82f6; /* Azul más moderno */
  --vs-radio-border-color-checked: #3b82f6;
  --vs-radio-size: 1.1rem;
}

.radio-custom:hover {
  --vs-radio-border-color: #93c5fd;
}


</style>
<style>

.Clientes .dx-datagrid .dx-selection.dx-row > td {
  background-color: #f7dc6f !important;
  color: black !important;
}

.Clientes .dx-header-filter:not(.dx-header-filter-empty) {
    color: yellow;
}


.Clientes .dx-datagrid-headers .dx-datagrid-table .dx-header-row {
    background-color: #2980b9; /* Verde */
    color: white;
}


</style>