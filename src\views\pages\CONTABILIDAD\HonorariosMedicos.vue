<template>
<div>
    <form @submit="handleSubmit">
        <DxForm :ref="formConsulta" :form-data.sync="formulario" label-mode="floating">
            <DxFormGroupItem :col-count="2">
                <DxFormGroupItem>
                    <DxFormItem data-field="Fecha" editor-type="dxDateBox" :validationRules="[{ type: 'required' }]" :editor-options="editorOptionsFecha">
                        <DxFormLabel text="Ajenos liberados al" />
                    </DxFormItem>
                    <DxFormItem data-field="Cuenta" editor-type="dxSelectBox" :editor-options="{ width: 'auto', searchEnabled: true, items: cuentas,  displayExpr: 'Valor', valueExpr: 'Codigo' }" :validationRules="[{ type: 'required' }]" />
                </DxFormGroupItem>
                <DxFormGroupItem>
                    <DxFormItem data-field="SiguienteNota" editor-type="dxNumberBox" :editor-options="{ min: 0, format: '#0', step: 0 }">
                        <DxFormLabel text="Siguiente nota" />
                    </DxFormItem>
                    <DxFormItem data-field="Proveedor" editor-type="dxLookup" :editor-options="{ dataSource: ajenosDataSource,  showCancelButton: false, displayExpr: 'Display', valueExpr: 'Codigo', dropDownOptions: dropDownOptions, deferRendering: false, }">
                        <DxFormLabel text="Médico" />
                    </DxFormItem>
                </DxFormGroupItem>
                <DxFormButtonItem :col-span="2" :button-options="buttonConsultar" name="Consultar" horizontal-alignment="center" verical-alignment="center" />
            </DxFormGroupItem>
            <DxFormGroupItem :visible="cheques.length > 0" :col-count="12" class="pt-4" caption=" ">
                <DxFormItem template="cheques" :col-span="2" />
                <DxFormItem template="infoCheque" :col-span="10" />
            </DxFormGroupItem>
        </DxForm>
    </form>
</div>
</template>

<script>
import 'devextreme-vue/lookup'

import {
    DefaultDxGridConfiguration,
    customizeTextValores
} from './data'

const formConsulta = 'formConsulta'
const formCheque = 'formCheque'
const gridCheques = 'gridCheques'
const gridDetalle = 'gridDetalle'

export default {
    name: 'HonorariosMedicos',
    components: {},
    data() {
        return {
            DefaultDxGridConfiguration,
            formConsulta,
            formCheque,
            gridCheques,
            gridDetalle,

            formulario: {
                Fecha: null,
                Cuenta: null, // Codigo de la cuenta de banco de 2 dígitos
                SiguienteNota: null // Valor de la siguiente nota de honorarios
            },
            cuentas: [],

            buttonConsultar: {
                width: 'auto',
                icon: 'fas fa-print',
                text: 'Generar',
                type: 'success',
                onClick: () => {
                    this.RevisaPlanilla()
                },
                useSubmitBehavior: false,
            },

            cheques: [],

            //Solo se asigna con status para que no cause error al validarlo cuando se cargan los cheques para mostrar el botón de anular
            infoCheque: {
                Status: null
            },
            periodo: null,
            cuentaIVACobrar: null,
            porcentajeIVA: null,
            retencion: null,
            indiceSeleccionado: null, //Variable para saber cual es el índice en la tabla de cheques del cheque seleccionado

            formularioCheque: {
                DescripcionPeriodo: null
            },
            valoresTabla: null,
            periodos: null,

            dropDownOptions: {
                showTitle: false,
                hideOnOutsideClick: true,
            },

            infoInicialCheque: null, //Variable para guarda la información del cheque antes de la edición, para restaurar la información en dado caso se cancele la edición
            mostrarBotones: false,

            agregarAuxiliar: null, //Variable para almacenar las nuevas líneas del auxiliar de bancos
            eliminarAuxiliar: null, //Variable para almacenar las líneas a eliminar del auxiliar de bancos

            cuentasAuxiliar: [],
            cuentasAuxiliarDataSource: {
                paginate: true,
                pageSize: 10,
                sort: 'Nombre',
                key: 'Codigo',
                // loadMode: 'raw',
                byKey: (e) => {
                    return this.cuentasAuxiliar.find(x => x.Codigo === e)
                },
                load: (e) => {
                    if (!e.searchValue && !e.take)
                        return this.cuentasAuxiliar
                    if (e.take) {
                        if (e.skip === e.take) {
                            e.take = e.take + 10
                        }
                    }
                    return this.$filtrado_avanzado_palabras(e.searchValue, this.cuentasAuxiliar, 'Display', e.skip, e.skip + e.take)
                }
            },

            inicialValoresTabla: null,

            indiceAuxiliar: 0, //Variable para guardar el índice maximo del auxiliar de banco que se cargó y poder agregar nuevos indices empezando desde 1 más al que carge aquí

            editorOptionsFecha: {
                dataType: 'date',
                displayFormat: 'dd/MM/yyyy',
                max: new Date()
            },

            ajenos: [],
            ajenosDataSource: {
                paginate: true,
                pageSize: 10,
                sort: 'Codigo',
                key: 'Codigo',
                // loadMode: 'raw',
                byKey: (e) => {
                    return this.ajenos.find(x => x.Codigo === e)
                },
                load: (e) => {
                    if (e.take) {
                        if (e.skip === e.take) {
                            e.take = e.take + 10
                        }
                    }
                    return this.$filtrado_avanzado_palabras(e.searchValue, this.ajenos, 'Display', e.skip, e.skip + e.take)
                }
            },

            ajenoActual: null, // Sirve para validar el ajeno que se está evaluando
            proveedorActual: null, // Sirve para validar el proveedor que se está evaluando

            totalHonorarios: 0,
            valorComision: 0,
            valorDescuento: 0,
            valorLiquido: 0,
            porcentajeComision: 0,

            sucursalRecogeChequeDefault: 'HLA',
            hospitalRecogerCheque: null,

            HonorariosMedicos: {
                BancoMovto: {},
                AuxiliarBanco: [],
                LotesFacturas: [],
                ComprasPagos: [],
                Compras: [],
            },

            numeroCheque: null,
        }
    },
    props: {
        ModalidadPago: null,
        TipoCheque: null,
        // ImpresionLotes: null,
        Corporativo: null
    },
    methods: {
        customizeTextValores,
        handleSubmit(e) {
            e.preventDefault()
        },

        CargarCuentas() {
            this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 1
                })
                .then(resp => {
                    this.cuentas = resp.data.json

                    this.formulario.Cuenta = this.cuentas[0].Codigo
                })
        },

        CargarAjenos() {
            this.axios.post('/app/v1_bancos/GeneracionHonorariosMedicos', {
                    Opcion: 1
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.ajenos = resp.data.json.map((x) => {
                            return {
                                Codigo: x.Codigo,
                                Nombre: x.Nombre,
                                Apellido: x.Apellido,
                                Display: x.Codigo + ' - ' + x.Nombre + ' ' + x.Apellido + ' - ' + x.Especialidad
                            }
                        })
                    }
                })
        },

        async RevisaPlanilla() {
            await this.axios.post('/app/v1_bancos/GeneracionHonorariosMedicos', {
                    Opcion: 2,
                    Fecha: this.formulario.Fecha,
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        if (resp.data.json[0].Cuantos == 0) {
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#ed8c72',
                                title: 'Planilla sin cerrar',
                                acceptText: 'Aceptar',
                                text: 'No se puede generar cheques en esta fecha. La planilla aún no está cerrada.',
                                buttonCancel: 'border',
                                accept: () => {},
                            })
                            return
                        } else {
                            console.log('Entro')
                            this.AjenosLiberados()
                        }
                    }
                })
        },

        async AjenosLiberados() {
            await this.axios.post('/app/v1_bancos/GeneracionHonorariosMedicos', {
                    Opcion: 3,
                    Fecha: this.formulario.Fecha,
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        if (resp.data.json.length > 0) {
                            console.log(resp.data.json)
                        }
                    }
                })
        },

        BuscarSiguienteNota() {
            this.axios.post('/app/v1_bancos/Anticipos', {
                    Opcion: 7,
                    TipoMov: this.TipoCheque
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.formulario.SiguienteNota = resp.data.json[0].SiguienteNota
                    }
                })
        },

        async CargarCuentasAuxiliar() {
            await this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 11
                })
                .then(resp => {
                    this.cuentasAuxiliar = resp.data.json.map((x) => {
                        return {
                            Codigo: x.Codigo,
                            Nombre: x.Nombre,
                            Display: x.Codigo + ' - ' + x.Nombre
                        }
                    })
                })
        },

        ValidarCheque(cheque) {
            if (cheque !== null && cheque >= 0) {
                return true
            }
            return false
        },

        async CargarPeriodos() {
            await this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 3,
                })
                .then(resp => {
                    this.periodos = resp.data.json
                })
        },

        async BuscarOrden(e) {
            await this.axios.post('/app/v1_bancos/ConsultaAnulacion', {
                    Opcion: 3,
                    EmpresaCheque: e.Empresa,
                    Cuenta: e.Cuenta,
                    TipoCheque: e.Tipo,
                    ChequeInicial: e.Numero,
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.valoresTabla = resp.data.json
                        // this.inicialValoresTabla = resp.data.json // Se realiza una copia de los valores iniciales para utilizalos y comparar con los cambios que se puedan realizar

                        for (let i = 0; i < this.valoresTabla.length; i++) {
                            if (this.valoresTabla[i].Indice > this.indiceAuxiliar) {
                                this.indiceAuxiliar = this.valoresTabla[i].Indice
                            }
                        }
                    }
                })
        },

        async BuscarProveedores() {
            await this.axios.post('/app/v1_bancos/Proveedores', {
                    Opcion: 4
                })
                .then(resp => {
                    this.proveedores = resp.data.json.map((x) => {
                        return {
                            Codigo: x.Codigo,
                            Nombre: x.Nombre,
                            Nit: x.Nit,
                            Retencion: parseFloat(x.Retencion).toFixed(2),
                            TipoRetencion: x.TipoRetencion,
                            CuentaRetencion: x.CuentaRetencion,
                            ContaAnticipo: x.ContaAnticipo,
                            NombreCheque: x.NombreCheque,
                            CuentaBanco: x.CuentaBanco,
                            Display: x.Nit + ' - ' + x.Nombre + ' - ' + x.Codigo
                        }
                    })
                })
        },

        ConsultarPeriodo() {
            this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 1,
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.periodo = resp.data.json[0].Codigo
                    }
                })
        },

        async ConsultarSaldoFinal(e) {
            return await this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 2,
                    Cuenta: e.Cuenta,
                    Periodo: e.Periodo
                })
                .then(resp => {
                    if (resp.data.json.length === 0) {
                        return false
                    } else if (resp.data.json[0].SaldoFinal !== null) {
                        return true
                    }
                })
        },

        async CargarCuentasDefault() {
            await this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 9
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.cuentaIVACobrar = resp.data.json[0].IVAPorCobrar
                    }
                })
        },
        CargarOpciones() {
            this.axios.post('/app/v1_bancos/Opciones', {
                    Opcion: 1
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.porcentajeIVA = parseFloat(resp.data.json[0].IVA / 100).toFixed(2)
                    }
                })
        },

        async GrabarCheque(codigoAjeno, prontoP) {

            if (this.ajenoActual.Codigo !== codigoAjeno) {
                this.ajenoActual = await this.ConsultarAjeno(codigoAjeno)
            }

            // let baseRetencion = parseFloat(this.totalHonorarios / (1 + this.opciones.IVA)).toFixed(2)

            this.valorLiquido = parseFloat(this.totalHonorarios - this.valorComision)

            if (this.ajenoActual.HospitalRecogerCheque == null) {
                this.hospitalRecogerCheque = this.sucursalRecogeChequeDefault
            } else {
                this.hospitalRecogerCheque = this.ajenoActual.HospitalRecogerCheque
            }

            let observaciones = null
            observaciones = this.ajenoActual.NombreCompleto + ' al ' + ' Recoger Ch. ' + this.hospitalRecogerCheque

            let beneficiario = null
            let proveedor = null

            if (this.ajenoActual.EmpresaPP == null || this.ajenoActual.EmpresaPP == '' || this.ajenoActual.ProveedorPP == null || this.ajenoActual.ProveedorPP == '' || prontoP !== 'S') {
                if (this.ajenoActual.NombreCheque == null || this.ajenoActual.NombreCheque == '') {
                    beneficiario = this.ajenoActual.Nombre + ' ' + this.ajenoActual.Apellido
                } else {
                    beneficiario = this.ajenoActual.NombreCheque
                }
                proveedor = this.ajenoActual.Proveedor
            } else {
                this.proveedorActual = await this.ConsultarProveedor(this.ajenoActual.ProveedorPP)

                if (this.proveedorActual.NombreCheque == null) {
                    beneficiario = this.proveedorActual.Nombre
                } else {
                    beneficiario = this.proveedorActual.NombreCheque
                }

                proveedor = this.proveedorActual.Codigo
            }

            this.HonorariosMedicos.BancoMovto = {
                Opcion: 1,
                Cuenta: this.formulario.Cuenta,
                TipoMov: 'NH',
                Periodo: this.periodo,
                Usuario: this.Corporativo,
                TipoCheque: 'M',
                Numero: this.numeroCheque,
                CodigoBeneficiario: codigoAjeno,
                NombreBeneficiario: beneficiario,
                Proveedor: proveedor,
                Monto: parseFloat(this.valorLiquido).toFixed(2),
                IdLoteHonorario: this.formulario.LoteSeleccionado.Id,
                Observaciones: 'Honorarios ' + observaciones,
                Conciliado: 'N'
            }

            if (this.formulario.LoteSeleccionado.IdFactura == null && this.TipoCobro == 1) {

                if (this.valorComision > 0) {

                    this.CuentaAjena.AuxiliarBanco.push({
                        Opcion: 4,
                        TipoMov: this.TipoDoc == 1 ? 'CH' : 'NH',
                        Cuenta: this.formulario.Cuenta,
                        Periodo: this.periodo,
                        Usuario: this.Corporativo,
                        Numero: parseInt(this.numeroCheque),
                        Referencia: this.formulario.SiguienteCheque,
                        CuentaBanco: this.contabilidadCuentaAjena,
                        Haber: parseFloat(this.valorComision).toFixed(2),
                        Indice: parseInt(this.contadorIndice + 1),
                        Detalle: 'Manejo de Cuenta',
                        IdLoteHonorario: this.formulario.LoteSeleccionado.Id
                    })
                }
            }

            if (this.valorLiquido !== 0) {

                this.CuentaAjena.AuxiliarBanco.push({
                    Opcion: 4,
                    TipoMov: this.TipoDoc == 1 ? 'CH' : 'NH',
                    Cuenta: this.formulario.Cuenta,
                    Periodo: this.periodo,
                    Usuario: this.Corporativo,
                    Numero: parseInt(this.numeroCheque),
                    Documento: this.formulario.SiguienteCheque,
                    CuentaBanco: this.formulario.BancoContabilidad,
                    Haber: parseFloat(this.valorLiquido).toFixed(2),
                    Indice: parseInt(this.contadorIndice + 4),
                    Detalle: this.formulario.NombreCuentaBanco,
                    IdLoteHonorario: this.formulario.LoteSeleccionado.Id
                })
            }

            this.CuentasAjenas.CuentaAjena.push(this.CuentaAjena)
        },

        async Grabar1(ajenos) {
            let ajenoAnt = 0,
                contCheques = 0

            this.contadorIndice = 0

            this.numeroCheque = this.formulario.SiguienteNota

            this.HonorariosMedicos = {
                BancoMovto: {},
                AuxiliarBanco: [],
                LotesFacturas: [],
                ComprasPagos: [],
                Compras: [],
            }

            let i = 0

            // for (let i = 0; i < ajenos.length; i++) {
            while (i < ajenos.length) {
                let item = ajenos[i]

                let tempDetalleFactura = {}
                let tempLotesFacturas = {}

                tempDetalleFactura = {
                    Opcion: 4,
                    TipoMov: this.TipoDoc == 1 ? 'CH' : 'NH',
                    Cuenta: this.formulario.Cuenta,
                    Periodo: this.periodo,
                    Usuario: this.Corporativo,
                    Numero: parseInt(this.numeroCheque),
                    IdLoteHonorario: this.formulario.LoteSeleccionado.Id
                }

                tempLotesFacturas = {
                    Opcion: 4,
                    CuentaBanco: this.formulario.Cuenta,
                    Ajeno: item.Ajeno,
                    Estado: 'L',
                    SiguienteCheque: this.numeroCheque
                }

                if (ajenoAnt !== item.Ajeno || this.contadorIndice > this.pacientesPorCheque) {
                    if (ajenoAnt > 0) {
                        this.GrabarCheque(ajenoAnt) //-----------------------------------------------------------------------------
                        this.numeroCheque = parseInt(this.numeroCheque + 1)
                        this.contadorIndice = 0

                        this.CuentaAjena = {
                            BancoMovto: [],
                            AuxiliarBanco: [],
                            Lotes: {},
                            LotesFacturas: [],
                            ComprasPagos: [],
                            Compras: [],
                        }
                    }

                    let existencias = await this.VerificarExistencia()

                    if (!existencias) { // Si es falso significa que no hay existencias, por lo que puede ingresar el cheque 
                        ajenoAnt = item.Ajeno
                        this.LimpiarVariables()

                        this.ajenoActual = await this.ConsultarAjeno(item.Ajeno)

                        if (this.ajenoActual.ManejoCuenta === null) { //Asignación del porcentaje de comisión
                            this.porcentajeComision = 0
                        } else {
                            this.porcentajeComision = this.ajenoActual.ManejoCuenta
                        }
                    }

                }

                this.contadorIndice = this.contadorIndice + 1

                tempLotesFacturas.IdLote = item.IdLote
                tempLotesFacturas.SerieFactura = item.SerieFactura
                tempLotesFacturas.NumeroFactura = item.Factura
                tempLotesFacturas.TipoDocumento = this.TipoDoc == 1 ? 'CH' : 'NH'

                let sumaFAjusteRetencionISR = 0,
                    sumaFAjusteRetencionIVA = 0

                if (item.DescuentoRetencionISR > 0) {
                    sumaFAjusteRetencionISR = parseFloat(item.DescuentoRetencionISR)
                }

                if (item.DescuentoRetencionIVA > 0) {
                    sumaFAjusteRetencionIVA = parseFloat(item.DescuentoRetencionIVA)
                }

                tempDetalleFactura.Documento = item.Documento

                let fechaPago = null

                if (item.FechaPlanillaPago == null) {
                    fechaPago = ''
                } else {
                    fechaPago = item.FechaPlanillaPago
                }

                if (this.formulario.LoteSeleccionado.IdFactura == null && this.TipoCobro == 1) // No viene rechazado de cuenta bancaria y es Cuenta ajena
                {
                    tempDetalleFactura.Detalle = fechaPago + ' ' + item.Lote
                    tempDetalleFactura.Debe = parseFloat(item.SumaFValor - sumaFAjusteRetencionISR - sumaFAjusteRetencionIVA).toFixed(2)

                } else { // Viene rechazado de banco 
                    tempDetalleFactura.Debe = parseFloat(item.SumaFValor - sumaFAjusteRetencionISR - sumaFAjusteRetencionIVA - item.SumaFMCuenta).toFixed(2)
                    if (this.formulario.LoteSeleccionado.IdFactura != null) {
                        tempDetalleFactura.Detalle = fechaPago + ' ' + item.Lote + ' Por Depósito Rechazado'
                    } else {
                        tempDetalleFactura.Detalle = fechaPago + ' ' + item.Lote
                    }
                }

                tempDetalleFactura.Indice = this.contadorIndice

                let cuentaEgresos = null
                cuentaEgresos = await this.BuscarEgresos(item.Proveedor, item.Documento)

                if (cuentaEgresos == null || cuentaEgresos.Cuenta == null) {
                    if (item.SerieFactura.substring(1, 3) == 'PPP') {
                        tempDetalleFactura.CuentaBanco = this.formulario.CuentaProveedoresProformaDefault
                    } else {
                        tempDetalleFactura.CuentaBanco = this.formulario.CuentaProveedores
                    }
                } else {
                    tempDetalleFactura.CuentaBanco = cuentaEgresos.Cuenta
                }

                this.totalHonorarios = parseFloat(this.totalHonorarios + parseFloat(item.SumaFValor - sumaFAjusteRetencionISR - sumaFAjusteRetencionIVA))
                this.valorComision = parseFloat(this.valorComision + item.SumaFMCuenta)

                tempDetalleFactura.IdLote = this.formulario.LoteSeleccionado.Id

                // Honorarios
                if (this.TipoCobro == 2) {
                    if (this.tmpProveedor !== item.Proveedor && this.tmpDocumento !== item.Documento) {
                        this.tmpProveedor = item.Proveedor
                        this.tmpDocumento = item.Documento

                        let valor = parseFloat(item.SumaFValor - sumaFAjusteRetencionISR - sumaFAjusteRetencionIVA)

                        this.CuentaAjena.Compras.push({
                            Opcion: 5,
                            ValorDetalleCheque: parseFloat(valor).toFixed(2),
                            Proveedor: item.Proveedor,
                            Documento: item.Documento,
                        })

                        this.CuentaAjena.ComprasPagos.push({
                            Opcion: 5,
                            Cuenta: this.formulario.Cuenta,
                            TipoMov: this.TipoDoc == 1 ? 'CH' : 'NH',
                            Numero: this.numeroCheque,
                            Tipo: 'M',
                            Periodo: this.periodo,
                            Proveedor: item.Proveedor,
                            Documento: item.Documento,
                            Monto: parseFloat(valor).toFixed(2),
                            Usuario: this.Corporativo,
                            Saldo: 0
                        })
                    }
                }

                this.CuentaAjena.AuxiliarBanco.push(tempDetalleFactura)
                this.CuentaAjena.LotesFacturas.push(tempLotesFacturas)
                // }
                i++;
            }

            this.CuentasAjenas.Lote = {
                Opcion: 6,
                IdLote: ajenos[ajenos.length - 1].IdLote,
                TipoDocumento: this.TipoDoc == 1 ? 'CH' : 'NH'
            }

            if (ajenoAnt > 0 && this.totalHonorarios > 0) {
                this.GrabarCheque(ajenoAnt) //-----------------------------------------------------------------------------

                this.numeroCheque = this.numeroCheque + 1
                this.contadorIndice = 0

                this.LimpiarVariables()

                this.CuentaAjena = {
                    BancoMovto: [],
                    AuxiliarBanco: [],
                    Lotes: {},
                    LotesFacturas: [],
                    ComprasPagos: [],
                    Compras: [],
                }
            }

            contCheques = parseInt(this.numeroCheque - this.chequeInicial)

            await this.axios.post('/app/v1_bancos/CuentasAjenas', {
                ...this.CuentasAjenas
            }).then(async resp => {
                if (resp.data.codigo == 0) {
                    if (this.TipoDoc == 1) {
                        // Incrementa el número de cheque
                        await this.axios.post('/app/v1_bancos/Cuentas', {
                            Codigo: this.formulario.Cuenta,
                            SiguienteCheque: parseInt(this.numeroCheque),
                            Opcion: 6
                        }).then(resp => {
                            if (resp.data.codigo == 0) {
                                this.CargarCuentas(2)
                                this.$vs.dialog({
                                    type: 'alert',
                                    color: '#ed8c72',
                                    title: 'Procesamiento de lote correcto',
                                    acceptText: 'Aceptar',
                                    text: 'Cheques emitidos: ' + contCheques,
                                    buttonCancel: 'border',
                                    accept: () => {},
                                })
                                return
                            }
                        })
                    } else if (this.TipoDoc == 2) {
                        // Actualiza el correlativo de la nota de acreditamiento
                        await this.axios.post('/app/v1_bancos/Anticipos', {
                            Opcion: 2,
                            Numero: parseInt(this.numeroCheque),
                            TipoMov: 'NH',
                        }).then(resp => {
                            if (resp.data.codigo == 0) {
                                this.BuscarSiguienteAcreditamiento()
                                this.$vs.dialog({
                                    type: 'alert',
                                    color: '#ed8c72',
                                    title: 'Procesamiento de lote correcto',
                                    acceptText: 'Aceptar',
                                    text: 'Acreditamientos emitidos: ' + contCheques,
                                    buttonCancel: 'border',
                                    accept: () => {},
                                })
                                return
                            }
                        })
                    }
                    this.CargarLotes()

                    this.CuentaAjena = {
                        BancoMovto: {},
                        AuxiliarBanco: [],
                        LotesFacturas: [],
                        ComprasPagos: [],
                        Compras: [],
                    }

                    this.CuentasAjenas = {
                        CuentaAjena: [],
                        Lote: {}
                    }
                }
            })

        },

        async ConsultarAjeno(ajeno) {
            return await this.axios.post('/app/v1_bancos/CuentaAjena', {
                    Opcion: 3,
                    Ajeno: ajeno
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        return resp.data.json[0]
                    }
                })
        },

        async ConsultarProveedor(proveedor) {
            return await this.axios.post('/app/v1_bancos/GeneracionHonorariosMedicos', {
                    Opcion: 4,
                    Ajeno: proveedor
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        return resp.data.json[0]
                    }
                })
        },
    },
    created() {},
    mounted() {
        this.formulario.Fecha = new Date()
    },
    beforeMount() {
        this.CargarCuentas()
        this.CargarAjenos()
        this.BuscarSiguienteNota()
        this.ConsultarPeriodo()
        this.CargarOpciones()
        this.CargarCuentasDefault()
        this.CargarPeriodos()
        this.BuscarProveedores()
        this.CargarCuentasAuxiliar()
    },
    watch: {
        'formulario.ChequeInicial'(newval) {
            if (this.formulario.ChequeFinal == 0 || this.formulario.ChequeFinal == null || parseInt(this.formulario.ChequeFinal) < parseInt(this.formulario.ChequeInicial)) {
                this.formulario.ChequeFinal = newval
            }
        }
    },
    computed: {
        formConsultaInstance: function () {
            return this.$refs[formConsulta].instance;
        },

        formChequeInstance: function () {
            return this.$refs[formCheque].instance;
        },

        gridChequesInstance: function () {
            return this.$refs[gridCheques].instance;
        },

        dataGridDetalle: function () {
            return this.$refs[gridDetalle].instance;
        },
    }
}
</script>

<style>
.buttonsOrdenes .dx-item-content .dx-box-item-content {
    place-content: end !important;
}
</style>
