<template>
<div>
    <DxTabPanel ref="tabRegistro" :height="'100%'" :data-source="menu">
        <template #title="{ data: tab }">
            <span>
                <font-awesome-icon class="pr-1" :icon="['fas', tab.icon]" />
                {{ tab.title }}
            </span>
        </template>
        <template #item="{ data: tab }">
            <div>
                <div v-if="tab.id === 1" class="p-2">
                    <NuevaTransaccion ref="NuevaTransaccion"/>
                </div>
                <div v-if="tab.id === 2" class="p-2">
                    <ConsultaTransacciones ref="ConsultaTransacciones"/>
                </div>

                <div v-if="tab.id === 3" class="p-2">
                    <Conciliaciones ref="Conciliaciones" />
                </div>

                <div v-if="tab.id === 4" class="p-2">
                    <MovimientoBancario ref="MovimientoBancario" />
                </div>

                <div v-if="tab.id === 5" class="p-2">
                    <DepositosCorteCaja ref="DepositosCorteCaja" />
                </div>
            </div>
        </template> 
    </DxTabPanel>
</div>
</template>

<script>

import Conciliaciones from './Conciliaciones.vue';
import NuevaTransaccion from './NuevaTransaccion.vue';
import MovimientoBancario from './MovimientoBancario.vue';
import DepositosCorteCaja from './DepositosCorteCaja.vue'
import ConsultaTransacciones from './ConsultaTransacciones.vue'

export default {
    name: 'Transacciones',
    components: {
        Conciliaciones,
        NuevaTransaccion,
        MovimientoBancario,
        DepositosCorteCaja,
        ConsultaTransacciones
    },
    data() {
        return {

            menu: [{
                    id: 1,
                    title: "Nueva transacción",
                    icon: "comment-dollar",
                },
                {
                    id: 2,
                    title: "Consulta y modificación",
                    icon: "money-bills",
                },
                {
                    id: 3,
                    title: "Conciliaciones",
                    icon: "handshake",
                },
                {
                    id: 4,
                    title: "Movimiento bancario",
                    icon: "right-left",
                },
                {
                    id: 5,
                    title: "Depósitos / Corte caja",
                    icon: "money-bill-trend-up"
                },
            ],

        }
    },
    methods: {
       

    },
    created() {},
    mounted() {},
    watch: {},
    computed: {
        
    },
   
}
</script>

<style>

</style>
