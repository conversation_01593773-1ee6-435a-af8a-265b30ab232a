<template>
<DxDataGrid v-bind="DefaultDxGridConfiguration" :ref="dataGridRefKey" :data-source="diagnosticosDataSource" :visible="ExpedienteCargado" @edit-canceled="(e)=> {vistaPrevia=false; this.editorSave(e)}"  @option-changed="editorDatagrid">
    <DxToolbar>
        <DxItem name="groupPanel" />
        <DxItem name="searchPanel" />
        <DxItem location="after" template="opcionesTemplate" />
    </DxToolbar>

    <DxGroupPanel :visible="true" />
    <DxGrouping :auto-expand-all="false" />

    <DxEditing :allow-updating="false" :allow-adding="true" :allow-deleting="false" mode="popup">
        <DxPopup v-bind="popupConf" :title="vistaPrevia?'Visualización Diagnóstico':'Agregar Diagnóstico'" :toolbarItems="vistaPrevia?[]:popupOptions.toolbarItems" height="490" />
        <DxForm label-location="top">
            <DxItem data-field="ProblemaEncontrado" editor-type="dxTextArea" :editor-options="{ height: 230 }" :col-span="2" />
            <DxItem v-if="!vistaPrevia" :is-required="true" data-field="CodCIE" :col-span="2" />
            <DxItem v-if="vistaPrevia" data-field="Display" :allow-editing="false" editor-type="dxTextBox" :col-span="2" />
        </DxForm>
    </DxEditing>
    <DxColumn type="buttons" :buttons="[
                {
                    icon:'fas fa-eye',
                    hint:'Ver',
                    onClick: (obj)=>{
                        this.vistaPrevia = true
                        this.dataGrid.editRow(obj.row.rowIndex)
                    }
                }
            ]" :width="50" :allow-reordering="false" :allow-resizing="false" />

    <DxColumn :width="150" data-field="FechaRegistro" caption="Fecha registro" format="dd/MM/yyyy HH:mm:ss" data-type="date" sort-order="desc" />
    <DxColumn :width="400" data-field="ProblemaEncontrado" caption="Problema encontrado" :allow-editing="!vistaPrevia" :customizeText="(e) => this.$saltos_tab_to_html(e.value)" :encode-html="false" />
    <DxColumn :width="200" data-field="CodCIE" :visible="false" caption="Diagnóstico de consulta" :allow-editing="!vistaPrevia">
        <DxLookup :dataSource="cie10DataSource" value-expr="Codigo" display-expr="Display" />
    </DxColumn>
    <DxColumn :width="250" data-field="Display" :visible="true" caption="Diagnóstico de consulta" />
    <DxColumn :width="175" data-field="Examinador" caption="Usuario" />
    <DxColumn :width="175" data-field="Especialidad" caption="Puesto" />

    <template #opcionesTemplate>
        <!-- <ExpedienteGridToolBar v-bind="$props" :visible="ExpedienteCargado" :pdfExportItems="[{text:'Diagnósticos', reportName: 'Diagnósticos'}]" @add="addRow" @refresh="Cargar" :report-param="$props" /> -->
             <ExpedienteGridToolBar v-bind="$props" :visible="ExpedienteCargado" :pdfExportItems="[{text:'Diagnósticos', reportName: 'Diagnósticos'}]" @add="ValidarRegistroDatos('Diagnósticos', addRow)" @refresh="Cargar" :report-param="$props" />         
    </template>
    <!-- Por si gustan agregar componente vuesax wa ja ja 3:) -->
    <template #buscador-cie>
        <SM-Buscar v-model="codcie" label="Diagnóstico CIE10" :api="cie10" :api_campos="['Codigo','Descripcion']" :api_titulos="['Código','Diagnóstico']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Descripcion" :api_preload="false" />
    </template>
</DxDataGrid>
</template>

<script>
import ExpedienteBase from './EXPBase.vue'
import {
    DxDataGrid,
    DxColumn,
    DxEditing,
    DxForm,
    DxToolbar,
    DxLookup,
    DxPopup,
    DxGrouping,
    DxGroupPanel,
} from 'devextreme-vue/data-grid'
import CustomStore from 'devextreme/data/custom_store'
import ExpedienteGridToolBar from './ExpedienteGridToolBar.vue'
import {
    DxItem
} from 'devextreme-vue/form'
import 'devextreme-vue/lookup'
import {
    DefaultDxGridConfiguration
} from './data'
import 'devextreme-vue/text-area'

const dataGridRefKey = 'gridDiagnosticos'

export default {
    name: 'Diagnosticos',
    extends: ExpedienteBase,
    components: {
        DxDataGrid,
        DxColumn,
        ExpedienteGridToolBar,
        DxEditing,
        DxForm,
        DxItem,
        DxToolbar,
        DxLookup,
        DxPopup,
        DxGrouping,
        DxGroupPanel,
    },
    data() {
        return {
            DefaultDxGridConfiguration,
            vistaPrevia: false,
            //Listado de diagnosticos asociados a la admisión
            diagnosticos: [],
            diagnosticosDataSource: new CustomStore({
                key: "id",
                load: () => {
                    return this.diagnosticos;
                },
                insert: (values) => {
                    this.GrabarDiagnostico(values)
                },
            }),
            //Catálogo de diagnóstico CIE10
            cie10: [],
            cie10DataSource: {
                paginate: true,
                pageSize: 10,
                sort: 'Codigo',
                // loadMode: 'raw',
                byKey: (e) => {
                    return this.cie10.Codigo === e ? this.cie10.Display : ''
                },
                load: (e) => {
                    if (e.take) {
                        if (e.skip === e.take) {
                            e.take = e.take + 10
                        }
                    }
                    return this.$filtrado_avanzado_palabras(e.searchValue, this.cie10, 'Display', e.skip, e.skip + e.take)
                }
            },
            dataGridRefKey,
            popupOptions: {
                toolbarItems: [{
                        widget: "dxButton",
                        toolbar: "bottom",
                        options: {
                            text: 'Guardar',
                            type: 'success',
                            useSubmitBehavior: true,
                            onClick: () => {
                                this.dataGrid.saveEditData()
                            }
                        },
                        location: "after",
                    },
                    {
                        widget: "dxButton",
                        toolbar: "bottom",
                        options: {
                            text: 'Cancelar',
                            type: 'danger',
                            useSubmitBehavior: true,
                            onClick: () => {
                                this.dataGrid.cancelEditData()
                            }
                        },
                        location: "after",
                    },
                ],
            },

            infoLocal: null,
        }
    },

    methods: {
        Cargar() {
            if (this.SerieAdmision && this.CodigoAdmision)
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaDiagnosticosPDF', {
                    Serie: this.SerieAdmision,
                    Admision: this.CodigoAdmision
                })
                .then(resp => {
                    this.diagnosticos = resp.data.json.map((x, index) => {
                        return {
                            id: index,
                            FechaRegistro: x.fecha,
                            ProblemaEncontrado: this.$limpiar_saltos_tabulares(x.problemaencontrado),
                            DiagnosticoConsulta: x.diagnostico,
                            CodCIE: x.codigoDiagnostico,
                            Display: x.codigoDiagnostico + ' - ' + x.diagnostico,
                            Examinador: x.Nombre,
                            Especialidad: x.Puesto,
                            EstadoEmpleado: x.status_empleado,
                        }
                    })
                    this.diagnosticosDataSource.load().then(
                        () => {
                            this.refreshDataGrid()
                        },
                        () => {
                            //console.error('error loading data customstore' + JSON.stringify(error))
                        }
                    )
                })
            else {
                this.diagnosticos = []
                this.refreshDataGrid()
            }
        },
        GrabarDiagnostico(values) {
            let postData = {
                SerieAdmision: this.SerieAdmision,
                Admision: this.CodigoAdmision,
                DescripcionProblema: this.$reemplazar_tabulares(values.ProblemaEncontrado),
                CodigoDiagnostico: values.CodCIE,
            }

            this.axios.post("/app/v1_ExpedienteEvolucion/InsertarExpedienteDiagnosticos", {
                ...postData
            }).then(() => {
                this.Cargar()
                sessionStorage.removeItem('Diagnosticos')
                this.$emit('onSaved', )
            })
        },
        CargarCIE10() {
            this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaDiagnosticos', {})
                .then(resp => {
                    this.cie10 = resp.data.json.map(x => {
                        return {
                            Codigo: x.codigoDiagnostico,
                            Descripcion: x.Diagnostico,
                            Display: x ? x.codigoDiagnostico + ' - ' + x.Diagnostico : 'Diagnóstico para Estadística'
                        }
                    })
                })
        },
        refreshDataGrid() {
            this.dataGrid.refresh()
            this.dataGrid.clearGrouping()
        },
        addRow() {
            if (this.StatusAdmision != 'A')
                this.$vs.notify({
                    time: 4000,
                    title: 'Diagnóstico',
                    text: 'La admisión no está activa, no se puede agregar nuevo diagnóstico',
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'warning',
                    position: 'bottom-center'
                })
            else
                this.infoLocal = JSON.parse(sessionStorage.getItem('Diagnosticos'))
                this.dataGrid.addRow();
        },
        editorSave(e) {
            if (e.changes !== undefined) {
                delete e.changes[0].data.CodCIE
                sessionStorage.setItem('Diagnosticos', JSON.stringify(e.changes[0].data))
            }
        },
        editorDatagrid(e) {
            if (e.fullName === "editing.changes" && e.value[0] !== undefined && this.infoLocal !== null) {
                e.value[0].data = this.infoLocal
                this.infoLocal = null
            }
        },
    },
    watch: {
        'NumeroExpediente'(newval) {
            if (newval != undefined)
                this.Cargar()
        },
    },
    computed: {
        dataGrid: function () {
            return this.$refs[dataGridRefKey].instance;
        }
    },
    beforeMount() {
        this.CargarCIE10()
    },
}
</script>
