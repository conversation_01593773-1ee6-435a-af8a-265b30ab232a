/*=========================================================================================
  File Name: globalComponents.js
  Description: Here you can register components globally
  ----------------------------------------------------------------------------------------
  Item Name: Vuexy - Vuejs, HTML & Laravel Admin Dashboard Template
  Author: Pixinvent
  Author URL: http://www.themeforest.net/user/pixinvent
==========================================================================================*/


import Vue from 'vue'
import VxTooltip from './layouts/components/vx-tooltip/VxTooltip.vue'
import VxCard  from './components/vx-card/VxCard.vue'
import VxList  from './components/vx-list/VxList.vue'
import VxBreadcrumb  from './layouts/components/VxBreadcrumb.vue'
import FeatherIcon  from './components/FeatherIcon.vue'
import VxInputGroup  from './components/vx-input-group/VxInputGroup.vue'
import Buscador  from './components/sermesa/global/Buscador.vue'
import SMBuscar  from './components/sermesa/global/SMBuscar.vue'
import SMArchivo from './components/sermesa/global/SMArchivo.vue'
import SMTabla  from './components/sermesa/global/SMTabla.vue'
import SMTablaTD  from './components/sermesa/global/SMTabla_td.vue'
import SMTabs  from './components/sermesa/global/SMTabs.vue'
import SMValidarPass  from './components/sermesa/global/SMValidarPass.vue'
import SMEmail  from './components/sermesa/global/SMCorreo/SMCorreo.vue'

Vue.component(VxTooltip.name, VxTooltip)
Vue.component(VxCard.name, VxCard)
Vue.component(VxList.name, VxList)
Vue.component(VxBreadcrumb.name, VxBreadcrumb)
Vue.component(FeatherIcon.name, FeatherIcon)
Vue.component(VxInputGroup.name, VxInputGroup)
Vue.component(Buscador.name, Buscador)
Vue.component(SMBuscar.name, SMBuscar)
Vue.component(SMArchivo.name,SMArchivo)
Vue.component(SMTabla.name, SMTabla)
Vue.component(SMTablaTD.name, SMTablaTD)
Vue.component(SMTabs.name, SMTabs)
Vue.component(SMEmail.name, SMEmail)
//validadores
Vue.component(SMValidarPass.name, SMValidarPass)


//vee validate
import { ValidationObserver,ValidationProvider } from 'vee-validate';
Vue.component('ValidationProvider', ValidationProvider);
Vue.component('ValidationObserver', ValidationObserver);


// v-select component
import vSelect from 'vue-select'

// Set the components prop default to return our fresh components
vSelect.props.components.default = () => ({
  Deselect: {
    render: createElement => createElement('feather-icon', {
      props: {
        icon: 'XIcon',
        svgClasses: 'w-4 h-4 mt-1'
      }
    }),
  },
  OpenIndicator: {
    render: createElement => createElement('feather-icon', {
      props: {
        icon: 'ChevronDownIcon',
        svgClasses: 'w-5 h-5'
      }
    }),
  },
});

Vue.component(vSelect)

//DevExtreme

import {
  DxEditing as DxDataGridEditing,
  DxDataGrid as DxDataGridDataGrid,
  DxColumn as DxDataGridColumn,
  DxLookup as DxDataGridLookup,
  DxRequiredRule as DxDataGridRequiredRule,
  DxSummary as DxDataGridSummary,
  DxTotalItem as DxDataGridTotalItem,
  DxToolbar as DxDataGridToolbar,
  DxItem as DxDataGridItem,
  DxButton as DxDataGridButton,
  DxRangeRule as DxDataGridRangeRule,
  DxSelection as DxDataGridSelection,
  DxGrouping as DxDataGridGrouping,
  DxPaging as DxDataGridPaging, 
  DxPager as DxDataGridPager,
  DxScrolling as DxDataGridScrolling,
  DxHeaderFilter as DxDataGridHeaderFilter,
  DxSearchPanel as DxDataGridDxSearchPanel,
  DxPopup as DxDataGridPopup,
  DxForm as DxDataGridForm,
  DxValidationRule as DxDataGridValidationRule,
  DxGroupPanel as DxDataGridGroupPanel,
  DxSorting as DxDataGridDxSorting,
  DxFormat as DxDataGridFormat,
  DxColumnFixing as DxDataGridDxColumnFixing,
  DxFilterPanel as DxDataGridDxFilterPanel,
  DxFilterRow as DxDataGridDxFilterRow,
  DxScrolling as DxDataGridDxScrolling

} from 'devextreme-vue/data-grid';

import { 
      DxNumberBox ,
      DxButton as DxNumberBoxButton
}  from 'devextreme-vue/number-box';
import Multiselect from "vue-multiselect";
import DxRadioGroup from 'devextreme-vue/radio-group';
import {DxSelectBox} from 'devextreme-vue/select-box';
import DxTabPanel, {  
  DxItem as DxTabPanelItem
} from 'devextreme-vue/tab-panel';
import DxButton from "devextreme-vue/button";
import DxTextBox, {DxButton as DxTextBoxButton} from 'devextreme-vue/text-box';
import { DxCheckBox } from 'devextreme-vue/check-box';
import { DxPopup, DxToolbarItem as DxPopupToolbarItem } from 'devextreme-vue/popup';
import { 
  DxForm,
  DxGroupItem as DxFormGroupItem,
  DxItem as DxFormItem,
  DxLabel as DxFormLabel,
  DxButtonItem as DxFormButtonItem,
  DxEmptyItem as DxFormEmptyItem,
  DxSimpleItem as DxFormSimpleItem,
  DxTabbedItem as DxFormTabbedItem,
  DxTab as DxFormTab
 } from 'devextreme-vue/form';

import DxScrollView from 'devextreme-vue/scroll-view';
import DxValidator, {
  DxRequiredRule,
  DxCustomRule,
} from 'devextreme-vue/validator';
import { DxList } from 'devextreme-vue/list';
import { DxDrawer } from 'devextreme-vue/drawer';
import {
  DxTabs as DxTabs,
  DxItem as DxTabsItem
} from 'devextreme-vue/tabs';
import {
  DxLookup,
} from 'devextreme-vue/lookup'

import DxToolbar, {
  DxItem as DxToolBarItem
} from 'devextreme-vue/toolbar'

import DxButtonGroup from 'devextreme-vue/button-group'

import DxDropDownButton, {
  DxItem as DxDropDownButtonItem
} from 'devextreme-vue/drop-down-button'
import DxContextMenu from "devextreme-vue/context-menu"
import DxDateBox from 'devextreme-vue/date-box';


import {
  DxChart,
  DxSeries as DxChartSeries,
  DxArgumentAxis as DxChartArgumentAxis,
  DxCommonSeriesSettings as DxChartCommonSeriesSettings,
  DxCommonAxisSettings as DxChartCommonAxisSettings,
  DxGrid as DxChartGrid,
  DxExport as DxChartExport,
  DxLegend as DxChartLegend,
  DxMargin as DxChartMargin,
  DxTooltip as DxChartTooltip,
  DxLabel as DxChartLabel,
  DxFormat as DxChartFormat,
  DxValueAxis as DxChartValueAxis,
  DxZoomAndPan as DxChartZoomAndPan,
  DxCrosshair as DxChartCrosshair
} from 'devextreme-vue/chart';

import {
  exportDataGrid
} from 'devextreme/excel_exporter';

import {
  DxScheduler,
  DxResource
} from 'devextreme-vue/scheduler';
import esMessages from "devextreme/localization/messages/es.json";
import {
  locale,
  loadMessages
} from "devextreme/localization";

Vue.component('DxScrollView', DxScrollView);
Vue.component('DxValidator', DxValidator);
Vue.component('DxRequiredRule', DxRequiredRule);
Vue.component('DxNumberBox', DxNumberBox);
Vue.component('Multiselect',Multiselect);
Vue.component('DxRadioGroup',DxRadioGroup);
Vue.component('DxSelectBox',DxSelectBox);
Vue.component('DxCustomRule',DxCustomRule);
Vue.component('DxTabPanel',DxTabPanel);
Vue.component('DxDataGridGroupPanel', DxDataGridGroupPanel);
Vue.component('DxDataGridEditing', DxDataGridEditing);  
Vue.component('DxDataGrid', DxDataGridDataGrid);  
Vue.component('DxDataGridColumn', DxDataGridColumn);  
Vue.component('DxDataGridLookup', DxDataGridLookup);  
Vue.component('DxDataGridRequiredRule', DxDataGridRequiredRule);  
Vue.component('DxDataGridSummary', DxDataGridSummary);  
Vue.component('DxDataGridTotalItem', DxDataGridTotalItem);  
Vue.component('DxDataGridToolbar', DxDataGridToolbar);  
Vue.component('DxDataGridItem', DxDataGridItem);  
Vue.component('DxDataGridButton', DxDataGridButton);  
Vue.component('DxDataGridRangeRule', DxDataGridRangeRule);  
Vue.component('DxDataGridSelection', DxDataGridSelection);  
Vue.component('DxDataGridGrouping', DxDataGridGrouping);  
Vue.component('DxDataGridPaging', DxDataGridPaging );  
Vue.component('DxDataGridPager', DxDataGridPager);  
Vue.component('DxDataGridScrolling', DxDataGridScrolling);  
Vue.component('DxDataGridHeaderFilter', DxDataGridHeaderFilter);  
Vue.component('DxDataGridDxSearchPanel', DxDataGridDxSearchPanel);  
Vue.component('DxDataGridPopup', DxDataGridPopup); 
Vue.component('DxDataGridForm', DxDataGridForm);  
Vue.component('DxDataGridValidationRule', DxDataGridValidationRule);
Vue.component('DxButton', DxButton);  
Vue.component('DxTextBox', DxTextBox);  
Vue.component('DxCheckBox', DxCheckBox);
Vue.component('DxPopup', DxPopup);
Vue.component('DxForm', DxForm);
Vue.component('DxFormItem', DxFormItem);
Vue.component('DxFormLabel', DxFormLabel);
Vue.component('DxFormGroupItem', DxFormGroupItem);
Vue.component('DxFormButtonItem', DxFormButtonItem);
Vue.component('DxFormEmptyItem', DxFormEmptyItem);
Vue.component('DxList', DxList);
Vue.component('DxDrawer', DxDrawer);
Vue.component('DxTabs', DxTabs);
Vue.component('DxTabsItem', DxTabsItem);
Vue.component('DxPopupToolbarItem', DxPopupToolbarItem);
Vue.component('DxFormSimpleItem', DxFormSimpleItem);
Vue.component('DxFormTabbedItem', DxFormTabbedItem);
Vue.component('DxFormTab', DxFormTab);
Vue.component('DxTextBoxButton', DxTextBoxButton);
Vue.component('DxNumberBoxButton', DxNumberBoxButton);
Vue.component('DxDataGridFormat', DxDataGridFormat);
Vue.component('DxDataGridDxSorting', DxDataGridDxSorting);
Vue.component('DxTabPanelItem',DxTabPanelItem);
Vue.component('DxLookup', DxLookup);
Vue.component('DxToolbar', DxToolbar);
Vue.component('DxToolBarItem', DxToolBarItem);
Vue.component('DxButtonGroup', DxButtonGroup);
Vue.component('DxDropDownButton', DxDropDownButton);
Vue.component('DxDropDownButtonItem', DxDropDownButtonItem);
Vue.component('DxContextMenu', DxContextMenu);

Vue.component('DxChart', DxChart);
Vue.component('DxChartSeries', DxChartSeries);
Vue.component('DxChartArgumentAxis', DxChartArgumentAxis);
Vue.component('DxChartCommonSeriesSettings', DxChartCommonSeriesSettings);
Vue.component('DxChartCommonAxisSettings', DxChartCommonAxisSettings);
Vue.component('DxChartGrid', DxChartGrid);
Vue.component('DxChartExport', DxChartExport);
Vue.component('DxChartLegend', DxChartLegend);
Vue.component('DxChartMargin', DxChartMargin);
Vue.component('DxChartTooltip', DxChartTooltip);
Vue.component('DxChartLabel', DxChartLabel);
Vue.component('DxChartFormat', DxChartFormat);
Vue.component('DxChartValueAxis', DxChartValueAxis);
Vue.component('DxChartZoomAndPan', DxChartZoomAndPan);
Vue.component('DxChartCrosshair', DxChartCrosshair);
Vue.component('exportDataGrid', exportDataGrid);
Vue.component('DxDataGridDxColumnFixing',DxDataGridDxColumnFixing)
Vue.component('DxDataGridDxFilterPanel',DxDataGridDxFilterPanel)
Vue.component('DxDataGridDxFilterRow',DxDataGridDxFilterRow)
Vue.component('DxDateBox',DxDateBox)
Vue.component('DxDataGridDxScrolling',DxDataGridDxScrolling)
Vue.component('DxScheduler',DxScheduler)
Vue.component('DxResource',DxResource)
Vue.component('esMessages',esMessages)
Vue.component('locale',locale)
Vue.component('loadMessages',loadMessages)