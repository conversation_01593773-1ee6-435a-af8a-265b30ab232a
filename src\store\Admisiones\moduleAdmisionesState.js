

const state =
{
    AdmisionData: {
          Empresa: ''
        , Serie: ''
        , Codigo: ''
        , Entrada: ''
        , Salida: ''
        , Paciente: ''
        , Edad: ''
        , Habitacion: ''
        , NivelPrecios: ''
        , Descuento: ''
        , PorcentajeDescuento: ''
        , TipoDescuento: ''
        , <PERSON><PERSON><PERSON>: ''
        , Socio: ''
        , NombreFactura: ''
        , Nit: ''
        , DireccionFactura: ''
        , NotificarA: ''
        , DireccionNotificarA: ''
        , TelefonoNotificarA: ''
        , Medico: ''
        , Razon: ''
        , Otros: ''
        , Usuario: ''
        , Status: ''
        , Interno: ''
        , Acompanante: ''
        , Colegiado: ''
        , CoberturaHabitacion: ''
        , EdadMedida: ''
        , Paquete: ''
        , Bebe: ''
        , Preoperatorio: ''
        , Copago: ''
        , Coaseguro: ''
        , Seriefactcopago: ''
        , FactCopago: ''
        , VerificacionSeguro: ''
        , VerificacionUsuario: ''
        , VerificacionFecha: ''
        , PacienteBuscoHospital: ''
        , Auditada: ''
        , CondicionEgreso: ''
        , Tratamiento: ''
        , FechaEgreso: ''
        , SerieAdmisionRaiz: ''
        , AdmisionRaiz: ''
        , Peso: ''
        , Estatura: ''
        , PorcentajeInteresCuenta: ''
        , InteresNominalCuenta: ''
        , PlanMedax: ''
        , IdAfiliado: ''
        , MedaxABC: ''
        , FechaCierreSiniestralidad: ''
        , PaqueteQx: ''
        , CoaseguroMonto: ''
        , Clasificacion: ''
        , AreaServicio: ''
        , BloqueaEstCuenta: ''
        , HuellaResponsableReg: ''
        , HabilitadoHasta: ''
        , EmpresaReal: ''
        , CopagoACobrar: ''
        , CoaseguroACobrar: ''
        , CtaCalculada: ''
        , FechaCalculo: ''
        , UltimaHab: ''
        , GastosEspNoCubiertos: ''
        , ComentariosSemaforoE: ''
        , BeneficioTerceros: ''
        , IdTipoDescuento: ''
        , Deducible: ''
        , AutorizacionBI: ''
        , UsuarioPreEgreso: ''
        , FechaPreEgreso: ''
        , DiagnosticoCOVID: ''
        , Corporativo: ''
        , DPI: ''
        , Pasaporte: ''
    }
}


export default state