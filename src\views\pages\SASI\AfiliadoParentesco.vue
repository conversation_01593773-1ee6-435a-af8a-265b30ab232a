<template>
<div class="afiliado-parentesco-container">
    <DxPopup :visible.sync="popupVisible" :drag-enabled="false" :hide-on-outside-click="true" :show-close-button="true" :show-title="true" :width="550" :height="280" :title="`Status de Afiliados - Contrato No. ${NumeroAdhesion}`" content-template="popup-content">
        <template #popup-content>
            <form @submit="SubmitCambioStatus">
                <DxForm ref="fromCambioStatus" :form-data.sync="StatusData" label-location="left" label-mode="floating" validation-group="validacionStatus" :col-count="3">
                    <DxFormSimpleItem data-field="Estado" :label="{text:'Nuevo Estado'}" editor-type="dxSelectBox" :editor-options="{ dataSource: CatalogoEstados, displayExpr: 'Nombre', valueExpr: 'Id',}" :validation-rules="[{ type: 'required', message: 'Seleccione un nuevo estado para actualizar' }]" :col-span="3" />
                    <DxFormSimpleItem data-field="Observacion" :label="{text:'Observación'}" :validation-rules="[{ type: 'required', message: 'Ingrese observación o motivo del cambio de estado' }]" :col-span="3" />
                    <DxFormEmptyItem :col-span="3" />
                    <DxFormButtonItem :visible="true" :button-options="{ text: 'Guardar',  type: 'success',  icon: 'save', useSubmitBehavior: true, }" horizontal-alignment="center" verical-alignment="buttom" :col-span="3" />
                </DxForm>
            </form>
        </template>>

    </DxPopup>
    <DxDataGrid ref="datagridref" :data-source="Parentescos" v-bind="GridOption" @saving="onSaving">

        <DxDataGridToolbar>
            <DxDataGridItem name="saveButton" />
            <DxDataGridItem name="revertButton" :opctions="{type:'success'}" />
            <DxDataGridItem widget="dxButton" :options="OpcionesStatus" :visible="true" />
            <DxDataGridItem widget="dxButton" :options="{icon: 'refresh', type:'success', stylingMode:'outlined', hint: 'Recargar la infromación', onClick: Cargar}" />
            <DxDataGridItem widget="dxButton" :options="{icon: 'plus', type:'default', stylingMode:'outlined', hint: 'Agregar dependiente', onClick: AgregarDependiente}" />
        </DxDataGridToolbar>
        <DxDataGridColumn type="selection" :fixed="true" />
        <DxDataGridColumn dataField="IdAfiliado" caption="No. Afiliado" :allowEditing="false" />
        <DxDataGridColumn dataField="PrimerNombre" :allowEditing=" false" , />
        <DxDataGridColumn dataField="SegundoNombre" :allowEditing="false" />
        <DxDataGridColumn dataField="PrimerApellido" :allowEditing="false" />
        <DxDataGridColumn dataField="SegundoApellido" :allowEditing="false" />
        <DxDataGridColumn dataField="EstadoCivil" :allowEditing="false" />
        <DxDataGridColumn dataField="Genero" caption="Sexo" :allowEditing="false" />
        <DxDataGridColumn dataField="FechaNacimiento" dataType="date" format="dd/MM/yyyy" :allowEditing="false" />
        <DxDataGridColumn dataType="number" dataField="Edad" :allowEditing="false" format="##0 años" />
        <DxDataGridColumn dataField="IdParentesco" caption="Parentesco" :allowEditing="true" , :lookup=" { dataSource: CatalogoParentesco, valueExpr: 'Codigo' , displayExpr: 'Descripcion' ,} " :fixed="true" />
        <DxDataGridColumn dataField="Status" caption="Estado" :allow-editing="false" />

    </DxDataGrid>
</div>
</template>

<script>

import {
    DefaulGridOptions
} from './data.js'
import 'devextreme-vue/lookup'

export default {
    name: 'AfiliadosParentescos',
    components: {
        
    },
    props: {
        IdPlan: String,
        NumeroAdhesion: String,
    },
    data() {
        return {
            Parentescos: null,
            CatalogoParentesco: null,
            CatalogoEstados: null,
            GridOption: {
                ...DefaulGridOptions,
                editing: {
                    allowUpdating: true,
                    mode: 'batch',
                },
                selection: {
                    selectAllMode: true,
                    showCheckBoxesMode: 'always',
                    mode: 'multiple',
                },
                height: 'auto',
                width: 'auto',
            },
            OpcionesStatus: {
                icon: 'fas fa-arrows-alt-h',
                hint: 'Cambiar Estado',
                onClick: this.InitStatusChange,
            },
            popupVisible: false,
            StatusData: {
                Estado: null,
                Observacion: null,
            },
            Permisos: {
                CambioEstado: false,
            },
        }
    },
    methods: {
        onSaving(e) {
            const dat = []
            e.changes.forEach(x => {
                const r = {
                    ...x.key
                }
                r.IdParentesco = x.data.IdParentesco
                r.NumeroAdhesion = this.NumeroAdhesion
                return dat.push(r)
            })
            e.promise = new Promise((resolve, reject) => {
                this.axios.post('/app/v1_afiliados/AsignacionParentescos', {
                    Parentescos: dat
                }).then((resp) => {
                    resp.data.codigo == 0 ? resolve(false) : resolve(true)
                }).catch((err) => {
                    reject(err.descripcion ? err.descripcion : err)
                })
            })
        },
        InitStatusChange() {
            //todo: la definicion del plan no permite dependientes
            if (!this.Permisos.CambioEstado)
                this.$vs.notify({
                    title: 'Actualización de estado',
                    text: 'Se requiere permiso /SASI/SASI006 CAMBIO_ESTADO',
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'warning'
                })
            else if (!this.dataGrid.getSelectedRowsData().length)
                this.$vs.notify({
                    title: 'Actualización de estado',
                    text: 'Seleccione afiliados a cambiar de estado',
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'warning'
                })

            else {
                this.StatusData.Estado = null
                this.StatusData.Observacion = null
                this.popupVisible = true
            }
        },

        SubmitCambioStatus(e) {
            e.preventDefault()
            this.axios.post("/app/v1_afiliados/CambiarEstado", {
                    ...this.StatusData,
                    Afiliaciones: this.dataGrid.getSelectedRowsData(),
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        let x = this.Cargar()
                        x.then(
                            () => {
                                this.$emit('onStatusChanged', this.Parentescos)
                                this.popupVisible = false
                            }
                        )
                    }
                })
        },
        Cargar() {
            this.Parentescos = null
            if (this.IdPlan && this.NumeroAdhesion)
                return this.axios.post("/app/v1_afiliados/GetAfiliadosPlanAdhesion", {
                        NumeroAdhesion: this.NumeroAdhesion,
                        IdPlan: this.IdPlan,
                    })
                    .then((resp) => {
                        this.Parentescos = resp.data.json
                    })
        },
        AgregarDependiente() {
            const parentescosPermitidos = []
            this.CatalogoParentesco.forEach((element) => {
                if (!this.Parentescos.find(x => x.IdParentesco == element.Codigo && x.Status != 'C'))
                    parentescosPermitidos.push(element)
            })
            this.$emit('add-dependiente', {
                ParentescosPermitidos: parentescosPermitidos,
                DependientesActuales: this.Parentescos?.filter(x=> x.Status != 'C')
            })
        },
        CargarPermisos() {
            this.$validar_funcionalidad('/SASI/SASI006', 'CAMBIO_ESTADO', (resp) => {
                this.Permisos.CambioEstado = resp.status
            })
        }
    },
    watch: {
        'IdPlan'() {
            this.Cargar()
        },
        'NumeroAdhesion'() {
            this.Cargar()
        }
    },
    computed: {
        dataGrid: function () {
            return this.$refs['datagridref'].instance;
        }
    },
    mounted() {
        this.Cargar()
        this.CargarPermisos()
    },
    async beforeMount() {
        await this.axios.post('/app/v1_afiliados/BusquedaParentescos', {})
            .then((resp) => {
                this.CatalogoParentesco = resp.data.json
            })
        await this.axios.post('/app/v1_afiliados/CatalogoEstadoAfiliado', {})
            .then((resp) => {
                this.CatalogoEstados = resp.data.json
            })

    },
}
</script>

<style>

</style>
