<template>
<vx-card title="Corporativo" :class="[editar ? 'edicion' : '']">

    <vs-popup ref="buscador" title="Asignación de Puesto" :active.sync="show.Puesto" style="z-index:9999" id="div-with-loading" class="vs-con-loading__container">
        <ValidationObserver v-if="show.Puesto" ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
            <div class="flex flex-wrap">
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <SM-Buscar v-model="info.IdPuesto" 
                        label="Puesto Actual" 
                        api="app/administracion/BusquedaCorporativosPuestos" 
                        :api_campos="['Puesto']" :api_titulos="['Puesto']" 
                        api_campo_respuesta="Id" 
                        api_campo_respuesta_mostrar="Puesto" 
                        :disabled_texto="true" 
                        :mostrar_busqueda="false" 
                    />
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <ValidationProvider name="Nuevo Puesto" rules="" v-slot="{ errors }">
                        <SM-Buscar v-model="edit.IdPuesto" 
                            label="Nuevo Puesto" 
                            api="app/administracion/BusquedaCorporativosPuestos" 
                            :api_campos="['Puesto']" 
                            :api_titulos="['Puesto']" 
                            api_campo_respuesta="Id" 
                            api_campo_respuesta_mostrar="Puesto" 
                            :disabled_texto="true" 
                            :api_preload="true" 
                            :callback_cancelar="true" 
                            :danger="errors.length > 0" 
                            :dangertext="errors.length > 0 ? errors[0] : null" 
                        />
                    </ValidationProvider>
                </div>
            </div>
            <vs-divider></vs-divider>
            <div class="flex">
                <vs-spacer></vs-spacer>
                <vs-button :disabled="invalid" @click="handleSubmit(Guardar().GuardarPuesto());">
                    Guardar
                </vs-button>
            </div>
        </ValidationObserver>
    </vs-popup>

    <vs-divider position="left">Información Básica</vs-divider>
    <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
        <form method="post" @submit.prevent="handleSubmit(Guardar().Guardar_Ajenos)">
            <div class="flex mb-5" style="justify-content: space-between;border: 1px solid rgba(0, 0, 0, 0.1);padding: 5px;">
                <div class="w-full md:w-1/2 lg:w-1/2 sm:w-1/2 p-1">
                    <ValidationProvider name="Codigo" rules="required|numero_entero|numero_min:1" v-slot="{ errors }" class="required">
                        <SM-Buscar 
                            v-model="info.Corporativo" 
                            label="Código Corporativo" 
                            api="app/Ajenos/Busqueda_Corporativo" 
                            :api_campos="['Corporativo',['Nombres', 'Nombre'],['Apellidos', 'Apellido'],]" 
                            :api_titulos="['Corporativo', 'Nombres', 'Apellidos']" 
                            api_campo_respuesta="Corporativo" 
                            :disabled_search_input="nuevo" 
                            :callback_buscar="Consulta().ConsultaCorporativo" 
                            :callback_nuevo="(permisos.nuevo)?Nuevo().Corporativo:null" :callback_editar="permisos.editar?() => {editar = true;}: null" 
                            :callback_cancelar="Otros().Limpiar_Datos" :disabled_texto="!nuevo" :danger="errors.length > 0" 
                            :dangertext="errors.length > 0 ? errors[0] : null" 
                            :disabled_editar="editar" 
                        />
                    </ValidationProvider>
                </div>
                
                <div v-if="info.Corporativo" class="w-full md:w-1/2 lg:w-1/2 sm:w-1/2 pt-6 text-right">
                    <vs-button key="btn-activar" v-if="verBotonActivar" color="success" class="mr-2" @click="Guardar().Activar()"><i class="fas fa-check"></i> Activar Usuario </vs-button>    
                    <vs-button color="primary" class="mr-2" :disabled="!permisos.cambiar_firma" @click="permisos.cambiar_firma ? $refs.archivo.info.visible = true : null"><i class="fas fa-signature"></i> Asignar Firma</vs-button>
                    <SM-Archivo v-show="false" ref="archivo" icono="fa-signature" texto="Adjuntar Firma" api="/app/externo/ArchivosCorp_BD" :api_parametros="{ codigo: info.Corporativo }" :firma="true" :disabled="bloquear || !editar"></SM-Archivo>
                    <vs-button color="primary" :disabled="!permisos.cambiar_puesto" @click="permisos.cambiar_puesto ? Editar().Puesto() : null"><i class="fas fa-user"></i> Asignación Puesto</vs-button>
                </div>
            </div>

            <!-- NUEVO -->
            <div v-show="(info.Corporativo || nuevoEditar)">
                <div class="flex flex-wrap">
                    <div class="w-full sm:w-1/4 p-1">
                        <ValidationProvider name="Nombres" rules="required" v-slot="{ errors }" class="required">
                            <vs-input label="Nombres" class="w-full" v-model="info.Nombres" :disabled="!nuevoEditar" :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full sm:w-1/4 p-1">
                        <ValidationProvider name="Apellidos" rules="required" v-slot="{ errors }" class="required">
                            <vs-input label="Apellidos" class="w-full" v-model="info.Apellidos" :disabled="!nuevoEditar" :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full sm:w-1/4 p-1">
                            <ValidationProvider name="Puesto" rules="" v-slot="{ errors }">
                                <SM-Buscar 
                                    v-model="info.IdPuesto" 
                                    label="Puesto" 
                                    api="app/administracion/BusquedaCorporativosPuestos" 
                                    :api_campos="['Puesto']" 
                                    :api_titulos="['Puesto']" 
                                    api_campo_respuesta="Id" 
                                    api_campo_respuesta_mostrar="Puesto" 
                                    :disabled_texto="true" 
                                    :api_preload="true" 
                                    :callback_cancelar="true" 
                                    :disabled_busqueda="!nuevo && !editar" 
                                    :danger="errors.length > 0" 
                                    :dangertext="errors.length > 0 ? errors[0] : null" 
                                />
                            </ValidationProvider>
                        </div>
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4 p-1">
                        <SM-Buscar 
                            label="Estado" 
                            v-model="info.Status" 
                            :api="[{Codigo:'A',Nombre:'Activo'}, {Codigo:'B',Nombre:'Bloqueado'}, {Codigo:'I',Nombre:'Inactivo'}]" 
                            :api_campos="['Nombre']" :api_titulos="['Nombre']" 
                            api_campo_respuesta="Codigo" 
                            api_campo_respuesta_mostrar="Nombre" 
                            :api_preload="true" :disabled_texto="true" 
                            :disabled_busqueda="!nuevo && !editar" 
                        />
                    </div>
                </div>

                <div>
                    <vs-divider position="left">Información Adicional</vs-divider>
                    <div class="flex flex-wrap">
                        <div class="w-full sm:w-1/4 p-1">
                            <ValidationProvider name="Correo" rules="email|required" v-slot="{ errors }" >
                                <vs-input label="Correo" class="w-full" v-model="info.Correo" :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null" :disabled="!nuevoEditar"/>
                            </ValidationProvider>
                        </div>
                        <div class="w-full sm:w-1/4 p-1" v-if="nuevoEditar">
                            <ValidationProvider name="DPI" rules="" v-slot="{ errors }">
                                <vs-input label="DPI" class="w-full" v-model="info.DPI" :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full sm:w-1/4 p-1" v-if="nuevoEditar">
                            <ValidationProvider name="NIT" rules="" v-slot="{ errors }">
                                <vs-input label="NIT" class="w-full" v-model="info.NIT" :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null" />
                            </ValidationProvider>
                        </div>
                        
                        <div class="w-full sm:w-1/4 p-1" v-if="nuevoEditar">
                            <ValidationProvider name="Enlace Ajeno" rules="" v-slot="{ errors }">
                                <SM-Buscar 
                                    v-model="info.Ajeno" 
                                    label="Enlace Médico Ajeno" 
                                    api="app/Ajenos/Busqueda_Ajenos" 
                                    :api_campos="['Codigo','Apellido', 'Apellido',]" 
                                    :api_titulos="['Codigo', 'Nombres', 'Apellidos']" 
                                    api_campo_respuesta="Codigo" 
                                    api_campo_respuesta_mostrar="Nombre" 
                                    :disabled_texto="true" 
                                    :callback_cancelar="true" 
                                    :danger="errors.length > 0" :dangertext="errors.length > 0 ? errors[0] : null" 
                                />
                            </ValidationProvider>
                        </div>


                        <div class="w-full sm:w-1/4 p-1" v-if="nuevoEditar">
                            <ValidationProvider name="Telefono" v-slot="{ errors }" rules="telefono">
                                <vs-input label="Teléfono" class="w-full" v-model="info.Telefono" :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null" />
                            </ValidationProvider>
                        </div>

                        <div class="w-full sm:w-1/4 p-1" v-if="nuevoEditar">
                            <ValidationProvider name="Compania" v-slot="{ errors }" rules="numero_entero">
                                <SM-Buscar v-model="info.IdCompaniaTelefonica" 
                                    label="Compañia telefónica" 
                                    api="app/administracion/BusquedaCiaTelefonica" 
                                    :api_campos="['Id','Nombre',]" 
                                    :api_titulos="['Codigo', 'Nombre']" 
                                    api_campo_respuesta="IdCompaniaTel" 
                                    api_campo_respuesta_mostrar="Nombre" 
                                    :api_preload="true"
                                    :disabled_texto="true" 
                                    :callback_cancelar="true" 
                                    :danger="errors.length > 0" 
                                    :dangertext="errors.length > 0 ? errors[0] : null" />
                            </ValidationProvider>
                        </div>

                        <div class="w-full sm:w-1/4 p-1" v-if="nuevoEditar">
                            <ValidationProvider name="Extension" v-slot="{ errors }" rules="numero_entero">
                                <vs-input label="Extensión" class="w-full" v-model="info.Extension" :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null" />
                            </ValidationProvider>
                        </div>
                    </div>
                    <vs-divider v-if="nuevoEditar" position="left">Otros</vs-divider>
                    <div class="flex flex-wrap" >
                        <div class="w-full sm:w-1/4 p-1" v-if="nuevoEditar">
                            <ValidationProvider name="Slack" rules="" v-slot="{ errors }">
                                <vs-input label="ID Slack" class="w-full" v-model="info.IdSlack" :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null" />
                            </ValidationProvider>
                        </div>
                    </div>
                </div>

                <div v-if="nuevoEditar" class="flex bottom">
                    <vs-spacer></vs-spacer>
                    <vs-button :disabled="invalid" @click.native="handleSubmit(Guardar().Corporativo());">
                        Guardar
                    </vs-button>
                </div>
            </div>

        </form>
    </ValidationObserver>
</vx-card>
</template>

<script>
// import bitacora from "@/components/sermesa/funciones/SMBitacora";
export default {
    data() {
        return {
            /**
             * Documentación del módulo
             */
            doc_: ["Pantalla de administración de corporativos"],

            permisos: {
                nuevo: false,
                editar: false,
                cambiar_estado: false,
                cambiar_puesto: false,
                cambiar_firma: false,
                activar: false,//pasa el el estado de bloqueado a activo, para usuario que solo tendran ecceso a ese botón
            },

            show: {
                Puesto: false
            },

            /**
             * Habilita los campos para crear un nuevo ajeno
             */
            nuevo: false,

            /**
             * Habilita los campos para editar
             */
            editar: false,

            /**
             * Editar información del corporativo
             */
            editarCorporativo: false,

            /**
             * Indica si se bloqueara el contenido para su edición
             */
            bloquear: true,

            /**
             * Modelo de datos
             */
            info: {
                Corporativo: null,
                Status: 'A',
                Nombres: null,
                Apellidos: null,
                Correo: null,
                IdPuesto: null,
                DPI: null,
                NIT: null,
                Ajeno: null,
                IdSlack: null,
                Indicio: null,
            },

            edit: {
                IdPuesto: null
            }
        };
    },
    props: {},
    components: {},
    methods: {
        //=======================================================================================================
        // CONSULTA
        //=======================================================================================================
        Consulta: function() {
            return {
                /**
                 * Consula de Corporativo
                 * Permite obtener los nombres y apellidos del corporativo
                 */
                ConsultaCorporativo: (data) => {
                    this.axios.post('/app/administracion/CorporativoConsulta', {
                            IdCorporativo: data.Corporativo
                        })
                        .then(resp => {
                            if (resp.data.json) {
                                this.$map({
                                    objeto: this.info,
                                    respuestaAxios: resp,
                                    omitir: ['Corporativo']
                                })
                            }
                        })
                },
            };
        },

        //=======================================================================================================
        // NUEVO
        //=======================================================================================================
        Nuevo: function() {
            return {
                Corporativo: () => {
                    this.Otros().Limpiar_Datos;
                    this.bloquear = false;
                    this.nuevo = true;
                    // this.consulta_ajenos()
                },
            };
        },

        //=======================================================================================================
        // GUARDAR
        //=======================================================================================================
        Guardar: function() {
            return {
                Corporativo: () => {
                    this.axios.post('/app/administracion/CorporativoGuardar', {
                            ...this.info,
                            editar: this.editar
                        })
                        .then(() => {
                            const corp = this.info.Corporativo
                            this.Otros().Limpiar_Datos()
                            this.editar = false
                            this.$nextTick(()=>{
                                this.info.Corporativo = corp
                                this.editar = true
                            })
                        })
                },
                GuardarPuesto: () => {
                    this.axios.post('/app/administracion/CorporativoPuestoGuardar', {
                            IdCorporativo: this.info.Corporativo,
                            IdPuesto: this.edit.IdPuesto
                        })
                        .then(() => {
                            this.show.Puesto = false
                            this.info.IdPuesto = this.edit.IdPuesto
                        })
                },
                Activar: () => {
                    this.axios.post('/app/administracion/CorporativoActivar', {
                        Corporativo: this.info.Corporativo
                    }).then(()=>{
                        this.Consulta().ConsultaCorporativo(this.info)
                    })
                },
            };
        },

        //=======================================================================================================
        // EDITAR
        //=======================================================================================================
        Editar: function() {
            return {
                Editar: () => {
                    //Validando tipo Casa y su subclasificación
                    if (this.info.Tipo == "M" && !this.info.TipoEnMedax) {
                        this.$vs.dialog({
                            color: "danger",
                            title: "Ajenos - Error",
                            text: "Falta definir SubClasificación en Honorarios",
                        });
                        return false;
                    }
                },
                Puesto: () => {
                    this.show.Puesto = true;
                    this.edit.IdPuesto = this.info.IdPuesto
                }
            };
        },

        //=======================================================================================================
        // ELIMINAR
        //=======================================================================================================
        Eliminar: function() {
            return {};
        },

        //=======================================================================================================
        // OTROS
        //=======================================================================================================
        Otros: function() {
            return {
                Validar_Facturacion: (valor) => {
                    if (valor == 1)
                        this.$vs.dialog({
                            color: "warning",
                            title: "Ajenos - Facturas",
                            text: "Se sugiere revisar los porcentajes de cargo, comisiones y datos de proveedor.\nSi el médico atenderá COEX, necesita tener Subclasificación",
                        });
                },

                Limpiar_Datos: () => {
                    this.nuevo = false;
                    this.editar = false;
                    this.editarCorporativo = false
                    Object.entries(this.info).forEach(([key]) => {
                        this.info[key] = null;
                    });
                    this.info.Status = 'A'
                    setTimeout(() => {
                        this.$refs.formValidate.reset();
                    }, 100);
                },
                Mensaje_Error: (valido) => {
                    if (valido)
                        this.$vs.dialog({
                            color: "danger",
                            title: "Ajenos - Validación",
                            text: "Existen campos obligatorios ",
                        });
                },
                GenerarContraseña: () => {
                    this.info.Indicio = (Math.floor(Math.random() * 100000000) + 100000000).toString().substring(1);
                }
            };
        },
    },
    mounted() {
        this.permisos.nuevo = this.$validar_privilegio("NUEVO").status;
        this.permisos.editar = this.$validar_privilegio("EDITAR").status;
        this.permisos.activar = this.$validar_privilegio("ACTIVAR").status;
        this.permisos.cambiar_estado = this.$validar_privilegio("CAMBIAR_ESTADO").status;
        this.permisos.cambiar_puesto = this.$validar_privilegio("CAMBIAR_PUESTO").status;
        this.permisos.cambiar_firma = this.$validar_privilegio("CAMBIAR_FIRMA").status;
    },
    computed: {
        verBotonActivar() {
            return this.permisos.activar && this.info.Status == 'B'
        },
        /**Indica si el formulario esta editando o nuevo */
        nuevoEditar() {
            return this.nuevo || this.editar
        }
    },
};
</script>

<style scoped>
.btn-editar i {
    font-size: 20px;
}

.btn-editar {
    cursor: pointer;
    text-align: center;
    padding: 20px;
    display: inline-block;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    margin-right: 10px;
    width: 300px;
    transition: all ease 0.2s;
}

.btn-editar.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-editar:hover {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
}
</style>
