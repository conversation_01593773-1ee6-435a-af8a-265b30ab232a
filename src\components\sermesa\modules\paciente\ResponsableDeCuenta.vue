<template>
    <ValidationObserver ref="responsableData" v-slot="{ invalid, handleSubmit }" mode="lazy">
        <form @submit.prevent="handleSubmit(submitForm(submit))">
            <div class="w-full  p-4 ml-2">
                <h4>Datos responsable de la cuenta</h4>
            
            </div>
            <div class="container pl-4">
                <div class="datosResponsable pl-4 pb-2">
                    <vs-row>
                        <vs-col vs-type="flex" vs-w="4">
                            <div class="w-2/3">
                                <validationProvider name="Nombre" class="required" rules="required|min:3"
                                    v-slot="{ errors }">
                                    <vs-input label="Nombre" class="w-full" :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null"
                                        v-model="responsableCuenta.Nombre" />
                                </validationProvider>
                            </div>
                        </vs-col>

                        <vs-col vs-type="flex" vs-w="4">
                            <div class="w-2/3">
                                <validationProvider name="Apellido" class="required" rules="required|min:3"
                                    v-slot="{ errors }">
                                    <vs-input label="Apellido" class="w-full" :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null"
                                        v-model="responsableCuenta.Apellido" />
                                </validationProvider>
                            </div>
                        </vs-col>

                        <vs-col vs-type="flex" vs-w="4">
                            <div class="w-2/3 pt-1">
                                <validationProvider name="EstadosCiviles" class="required" rules="required"
                                    v-slot="{ errors }">
                                    <label class="vs-input--label">Estado Civil </label>
                                    <multiselect v-model="SelEstadoCivil" :options="EstadosCiviles" :disabled="false"
                                        :allow-empty="false" placeholder="Selección de Estado Civil" track-by="Nombre"
                                        label="Nombre" :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null">
                                    </multiselect>
                                </validationProvider>
                            </div>
                        </vs-col>
                    </vs-row>

                    <vs-row>
                        <vs-col vs-type="flex" vs-w="4">
                            <div class="w-2/3 pt-2">
                                <validationProvider
                                  name="Documento"
                                  class="required"
                                  rules="required|min:13"
                                  v-slot="{ errors }">
                                  <vs-input
                                    label="Documento Identificación"
                                    class="w-full"
                                    :maxlength="13"
                                    :danger="errors.length > 0"
                                    :danger-text="(errors.length > 0) ? errors[0] : null"
                                    v-model.trim="responsableCuenta.DPI"
                                  />
                                  </validationProvider>
                            </div>
                        </vs-col>

                        <vs-col vs-type="flex" vs-w="4">
                            <div class="w-2/3 pt-6">
                                <!-- <multiselect v-model="selDepartamento" :options="ListaDepartamentos" @input="onChangeDepto"
                                    :searchable="true" :close-on-select="true" :show-labels="false"
                                    :custom-label="DepartamentoSeleccionado" placeholder="Departamento Documento">
                                    <span slot="noOptions">Lista no disponible.</span>
                                </multiselect> -->


                            </div>
                        </vs-col>

                        <vs-col vs-type="flex" vs-w="4">
                            <div class="w-2/3 pt-5">

                                <!-- <multiselect v-model="selMunicipio" :options="ListaMunicipios" :searchable="true"
                                    :close-on-select="true" :show-labels="false" :custom-label="MunicipioSeleccionado"
                                    placeholder="Municipio Documento">
                                    <span slot="noOptions">Lista no disponible.</span>
                                </multiselect> -->
                            </div>
                        </vs-col>
                    </vs-row>

                    <vs-row>
                        <vs-col vs-type="flex" vs-w="4">
                            <div class="w-2/3 pt-2">
                                <validationProvider name="Direccion" class="required" rules="max:85"
                                    v-slot="{ errors }">>
                                    <vs-input label="Dirección" class="w-full" :maxlength="85" :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null"
                                    v-model="responsableCuenta.Direccion" />
                                </validationProvider>
                            </div>
                        </vs-col>

                        <vs-col vs-type="flex" vs-w="4">
                            <div class="w-2/3 pt-2">
                                <validationProvider name="Departamento Dirección">
                                    <vs-input label="Departamento Dirección" class="w-full"
                                        v-model.trim="responsableCuenta.Departamento" />
                                </validationProvider>
                            </div>
                        </vs-col>

                        <vs-col vs-type="flex" vs-w="4">
                            <div class="w-2/3 pt-4">
                                <validationProvider name="Teléfono">
                                    <vs-input label="Telefono" class="w-full" v-model.trim="responsableCuenta.Telefono" />
                                </validationProvider>
                            </div>
                        </vs-col>
                    </vs-row>

                    <vs-row>

                        <vs-col vs-type="flex" vs-w="4">
                            <div class="w-2/3 pt-2">
                                <validationProvider name="Edad" class="required" rules="required|min:2"
                                    v-slot="{ errors }">
                                    <vs-input label="Edad" class="w-full" :maxlength="2" :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null"
                                        v-model.trim="responsableCuenta.Edad" />
                                </validationProvider>
                            </div>
                        </vs-col>

                        <vs-col vs-type="flex" vs-w="4">
                            <div class="w-2/3 pt-8">

                                <multiselect v-model="selParentesco" :options="ListaParentescos" :searchable="true"
                                    :close-on-select="true" :show-labels="false" :custom-label="ParentescoSeleccionado"
                                    placeholder="Parentesco" label="Parentesco">

                                    <span slot="noOptions">Lista no disponible.</span>
                                </multiselect>
                            </div>
                        </vs-col>
                    </vs-row>

                    <vs-row>
                        <vs-col vs-type="flex" vs-w="4">
                            <div class="w-2/3 pt-2">
                                <validationProvider name="Nacionalidad">
                                    <vs-input label="Nacionalidad" class="w-full"
                                        v-model.trim="responsableCuenta.Nacionalidad" />
                                </validationProvider>
                            </div>
                        </vs-col>

                        <vs-col vs-type="flex" vs-w="4">
                            <div class="w-2/3 pt-2">
                                <validationProvider name="Pasaporte">
                                    <vs-input label="Pasaporte" class="w-full" v-model.trim="responsableCuenta.Pasaporte" />
                                </validationProvider>
                            </div>
                        </vs-col>

                        <vs-col vs-type="flex" vs-w="4">
                            <div class="w-2/3 pt-2">
                                <validationProvider name="Profesión">
                                    <vs-input label="Profesión" class="w-full" v-model.trim="responsableCuenta.Profesion" />
                                </validationProvider>
                            </div>
                        </vs-col>
                    </vs-row>
                </div>
            </div>
            <div v-show="showButtons">
                <vs-button style="margin-right:1px" v-on:click="handleSubmit(submit())" :disabled="invalid">
                    Guardar
                </vs-button>
            </div>


        </form>
    </ValidationObserver>
</template>
  

<script>

import { mapFields } from 'vuex-map-fields';
import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";

export default {

    name: "ResponsableDeCuenta",
    data: () => ({
        showButtons: false,
        responsableCuenta: {
            Empresa: '',
            SerieAdmision: '',
            Admision: '',
            Nombre: '',
            Apellido: '',
            Edad: '',
            EstadoCivil: '',
            Profesion: '',
            LugarTrabajo: '',
            Nacionalidad: '',
            Direccion: '',
            Departamento: '',
            Telefono: '',
            Cedula: '',
            CedulaRegistro: '',
            CedulaMunicipio: '',
            CedulaDepartamento: '',
            Pasaporte: '',
            Usuario: '',
            Parentesco: 0,
            DPI: '',
        },
        EstadosCiviles: [
            {
                Codigo: 'C',
                Nombre: 'CASADO'
            },
            {
                Codigo: 'S',
                Nombre: 'SOLTERO'
            }
        ],
        SelEstadoCivil: '',
        ListaDepartamentos: [],
        ListaMunicipios: [],
        ListaParentescos: [],
        IdDeptoSeleccionado: '',
        selDepartamento: '',
        selMunicipio: '',
        selParentesco: '',
        TDepartamentos: [],
        TMunicipios: []
    }),
    components: {
        Multiselect
    },
    mounted() {
        this.ConsularAdmisionContrato()
        this.ConsultaParentesco()
    },
    computed: {
        ...mapFields('admision', ["AdmisionData"]),
        ...mapFields('paciente', ["PacienteData"])
    },
    created() {

    },
    watch: {
        'PacienteData': function () {            
            this.ConsularAdmisionContrato()            
        }

    },
    activated() {
        if (this.AdmisionData.Codigo) {
            this.responsableCuenta.SerieAdmision = this.AdmisionData.Serie
            this.responsableCuenta.Admision = this.AdmisionData.Codigo
            this.SelEstadoCivil = this.EstadosCiviles.find(option => option.Codigo === this.responsableCuenta.EstadoCivil?.trim() || '')
        }
    },
    methods: {
        getionarDatos() {
            return new Promise((resolve) => {
                // Lógica de guardado del cliente aquí
                // Resuelve la promesa si el guardado es exitoso
                return resolve(true);
                // Rechaza la promesa si hay un error en el guardado
                // reject('Mensaje de error aquí');
            });
        },
        validacionFormulario() {
            return new Promise((resolve, reject) => {
                this.$refs.responsableData.validate()
                    .then((isValid) => {
                        if (isValid) {
                            resolve(true);
                        } else {
                            reject('Revisar campos requeridos');
                        }
                    })
                    .catch((error) => {
                        reject(error);
                    });
            });
        },
        async ConsultaParentesco() {

            const resp = await this.axios.post('/app/v1_admision/ConsultarParentesco', {
                Empresa: '',
            })
            this.ListaParentescos = resp.data.json

            const tParentescos = resp.data.json.map(m => {
                return {
                    ...m,
                }
            })
            var parsedParentescos = JSON.stringify(tParentescos);
            const jsonData = JSON.parse(parsedParentescos);

            const v_parentesco = this.responsableCuenta.Parentesco
            const val = jsonData.find(x => x.IdParentesco === v_parentesco)?.Descripcion || 'Parentesco'

            this.selParentesco = {
                Descripcion: val
            }

        },
        ParentescoSeleccionado({
            Descripcion
        }) {
            return `${Descripcion} `

        },
        ActulizarAdmisionContrato() {


            this.responsableCuenta.EstadoCivil = this.SelEstadoCivil.Codigo
            this.responsableCuenta.SerieAdmision = this.AdmisionData.Serie
            this.responsableCuenta.Admision = this.AdmisionData.Codigo
            this.responsableCuenta.Parentesco = this.selParentesco.IdParentesco

            return new Promise((resolve, reject) => {
                this.axios.post('/app/v1_admision/ActualizarAdmisionContrato', this.responsableCuenta)
                    .then(function (response) {
                        resolve(response)
                    })
                    .catch(function (error) {
                        reject(error.response.data.errors)
                    })
            })
        },
        async ConsularAdmisionContrato() {
                        
            this.responsableCuenta.Nombre =''
            this.responsableCuenta.Apellido=''
            this.responsableCuenta.EstadoCivil=''
            this.responsableCuenta.DPI=''
            this.responsableCuenta.Direccion=''
            this.responsableCuenta.Departamento=''
            this.responsableCuenta.Edad=''
            this.responsableCuenta.Nacionalidad=''
            this.responsableCuenta.Pasaporte=''
            this.responsableCuenta.Profesion=''
            this.responsableCuenta.Parentesco=''

            this.SelEstadoCivil =''
            this.selParentesco=''

            const resp = await this.axios.post('/app/v1_admision/ConsultarAdmisionContrato', {
                Empresa: '',
                SerieAdmision: this.AdmisionData.Serie,
                Admision: this.AdmisionData.Codigo
            })

            if (resp.data.json.length == 0)
                return

            this.responsableCuenta = resp.data.json.map(m => {
                return {
                    ...m,

                }
            })
                
            this.responsableCuenta = this.responsableCuenta[0];
            this.SelEstadoCivil = this.EstadosCiviles.find(option => option.Codigo === this.responsableCuenta.EstadoCivil?.trim() || '')            
            this.ConsultaParentesco()
            
        },
        async submit() {
            this.getionarDatos()
                .then(() => {
                    return this.validacionFormulario();
                })
                .then(() => {
                    return this.ActulizarAdmisionContrato();
                })
                .then(() => {
                    this.$emit("ready", true);
                })
                .catch((error) => {
                    this.$vs.dialog({
                        type: 'confirm',
                        color: '#ED8C72',
                        acceptText: 'Continuar',
                        title: 'Error en gestión de Datos',
                        text: error,
                        clientWidth: 100,
                        accept: () => {
                            this.$emit("ready", false);

                        }
                    });
                })
        },
        async ConsultarDepartamentos() {
            const resp = await this.axios.post('/app/Ajenos/Busqueda_Departamentos', {
                IdDepartamento: '',
            })
            this.ListaDepartamentos = resp.data.json;

            this.TDepartamentos = resp.data.json.map(m => {
                return {
                    ...m,
                }
            })
            var parsedData = JSON.stringify(this.TDepartamentos);
            const jsonData = JSON.parse(parsedData);


            const v_depto = this.responsableCuenta.CedulaDepartamento.toString()
            const val = jsonData.find(x => x.IdDepartamento === v_depto)?.Departamento || 'Guatemala'

            this.selDepartamento = {
                Departamento: val
            }
            
        },
        async obtenerNombreDepto(idDepto) {

            const tDeptos = this.ListaDepartamentos.map(m => {
                return {
                    ...m,
                }
            })
            var parsedData = JSON.stringify(tDeptos);
            const jsonData = JSON.parse(parsedData);

            const v_depto = '' + idDepto
            const val = jsonData.find(x => x.IdDepartamento === v_depto)?.Departamento || 'Guatemala';

            this.selDepartamento = {
                Departamento: val
            }

        },
        async ConsultarMunicipios() {            
            const resp = await this.axios.post('/app/Ajenos/Busqueda_Municipios', {
                Codigo_Departamento: this.selDepartamento || '1617',
                IdMunicipio: '',
            })
            this.ListaMunicipios = resp.data.json;

            const tMunc = resp.data.json.map(m => {
                return {
                    ...m,
                }
            })

            var parsedData = JSON.stringify(tMunc);
            const jsonData = JSON.parse(parsedData);
            const v_munic = '' + this.responsableCuenta.CedulaMunicipio.toString() || '74'
            const val = jsonData.find(x => x.IdDepartamento === v_munic)?.Municipio || 'Guatemala';

            this.selMunicipio = {
                Municipio: val//'GUATEMALA'
            }

        },
        DepartamentoSeleccionado({
            Departamento
        }) {
            return `${Departamento} `
        },
        MunicipioSeleccionado({
            Municipio
        }) {
            return `${Municipio} `
        },
        onChangeDepto(deptoSel) {
            this.selDepartamento = deptoSel.IdDepartamento
            this.ConsultarMunicipios()
            this.obtenerNombreDepto(this.selDepartamento)
        },
        onChangeParentesco() {

        }

    }

}

</script>

  
<style scoped>
* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

.container {
    display: grid;
    width: 100%;
    height: 100%;    
    grid-template-areas: "datosResponsable";
    grid-template-columns: 1fr ;
    grid-template-rows: 100% 100%;
}

.container>div {
    border: 1px solid #6a6767;
}

.datosResponsable {

    grid-area: datosResponsable;
}


span {
    display: block;
    margin-top: 1px;
}


span {
    display: block;
    margin-top: 3px;
}

.container {
    max-width: 100%;
}

span.error-msg{
   color: red;
   margin-top: 5px;
}

</style>
  