<template>
<div id="conf-honorarios-container">
    <vx-card title="Configuración Módulo Honorarios">
        <div id="center-content">
            <div class="w-full">
                <DxTabPanel>
                    <DxItem title="Configurar Subespecialidades" icon="fas fa-tools">
                        <template #default>
                            <AJE038 />
                        </template>
                    </DxItem>
                    <DxItem title="Textos por defecto" icon="rename">
                        <template #default>
                            <AJE039 />
                        </template>
                    </DxItem>
                </DxTabPanel>
            </div>
        </div>
    </vx-card>
</div>
</template>

<script>
import DxTabPanel, {
    DxItem
} from "devextreme-vue/tab-panel"

export default {
    name: 'ConfiguracionHonorarios',
    components: {
        AJE038: () => import('./AJE038.vue'),
        AJE039: () => import('./AJE039.vue'),
        DxTabPanel,
        DxItem,
    },
    data() {
        return {
            multiViewItems: ['Valores generales', 'Configurar Subespecialidades'],
            selectedItemId: 0,
        }
    },
}
</script>

<style>
#center-content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;

}
</style>
