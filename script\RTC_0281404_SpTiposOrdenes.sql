/****************************************************************************/
/*	CREADO POR: <PERSON>	Numero de Rational: RTC281343		      */
/*	FECHA CREACIÓN: 21/12/2022												                      */
/*	PRODUCTO: Je<PERSON>													                        */
/**************************** DESCRIPCION ***********************************/
/*	Este Procedure tiene como objetivo crear, actualizar, eliminar			    */
/*	y buscar las diferentes tipos de ordenes.								                */
/****************************************************************************/
/*  MODIFICADO POR: <PERSON>									                */
/*	FECHA MODIFICACIÓN: 01/03/2023											                    */
/*	PRODUCTO: Anulación de Ordenes											                    */
/*	Descripcion: Filtrar los tipos de ordenes por hospital					        */
/*	para que el usuario solo pueda anular ordenes del hospital que tiene    */
/*	asignado en el sistema													                        */
/****************************************************************************/

CREATE PROCEDURE [dbo].[SpHisTiposOrdenes](
	-- PARAMETROS DEFECTO 
	-- ========================================================== 
	@IOpcion VARCHAR(1) = '', 
	-- C = Consulta 
	-- I = Ingreso 
	-- A = Actualizar 
	-- E = Eliminación 
	@ISubOpcion VARCHAR(2) = '', 
	@IHospital INT = 0,

	@IEmpresa VARCHAR(3) = '',
	@ICodigo VARCHAR(3) = '',
	@INombre VARCHAR(30) = '',
	@IBodegaDefault INT = 0,
	@INumAutomatica VARCHAR(1) = '',
	@ICategorias VARCHAR(254) = '',
	@IHospitalTO VARCHAR(3) = '',
	@IUbicacionDiag INT = 0,
	@IUbicacionEmer INT = 0,
	@IUbicacionHosp INT = 0,
	@IFiltroTipoOrdenHospital varchar(max) = '',
	@IFiltroTipoOrdenGeneral varchar(100) = ''
)
AS

BEGIN
	DECLARE
		@ErrRaise VARCHAR(99) = '',
		@ErrRaiseNum Int = 0,
		@Existe INT = 0

	SET NOCOUNT ON

	BEGIN TRAN
		BEGIN TRY
			IF @IOpcion = 'C'
				BEGIN
					IF @ISubOpcion = '1'
						BEGIN
							SELECT
								A.Codigo
								,A.Nombre
								,B.NombreCorto
								,A.BodegaDefault
								,A.NumeracionAutomatica
								,A.Categorias
								,A.Hospital
								,A.UbicacionDiag
								,A.UbicacionEmer
								,A.UbicacionHosp
							FROM
								Hospital.dbo.OrdenesTipos A
							LEFT JOIN
								NOMINADB.dbo.EmpHospital B
								ON
									A.Empresa = B.Empresa
								AND
									A.Hospital = B.Codigo
								AND
									B.EmpresaReal IS NOT NULL
							Where
								A.Empresa = @IEmpresa 
								and (A.Hospital = CASE WHEN (@IHospitalTO is null or @IHospitalTO = '') THEN A.Hospital ELSE @IHospitalTO END
										OR A.Codigo in ('AJE','BAN') )
							ORDER BY
								A.Codigo

							COMMIT TRAN
						END
					ELSE IF @ISubOpcion = '2'
						BEGIN
							SELECT
								Codigo
								,NombreCorto
							FROM
								NOMINADB.dbo.EmpHospital
							WHERE
								Empresa = @IEmpresa
							AND
								EmpresaReal IS NOT NULL
							AND
								Nombrecorto IS NOT NULL


							COMMIT TRAN
						END
					ELSE IF @ISubOpcion = '3'
						BEGIN
							SELECT
								A.Codigo
								,A.Nombre
								,B.NombreCorto
								,A.BodegaDefault
								,A.NumeracionAutomatica
								,A.Categorias
								,A.Hospital
								,A.UbicacionDiag
								,A.UbicacionEmer
								,A.UbicacionHosp
							FROM
								Hospital.dbo.OrdenesTipos A
							LEFT JOIN
								NOMINADB.dbo.EmpHospital B
								ON
									A.Empresa = B.Empresa
								AND
									A.Hospital = B.Codigo
								AND
									B.EmpresaReal IS NOT NULL
							Where
								A.Empresa = @IEmpresa 
								and ( (A.Hospital = CASE WHEN (@IHospitalTO is null or @IHospitalTO = '') THEN A.Hospital ELSE @IHospitalTO END
								      			and 
										  ((LEFT(A.CODIGO,2) IN  (select  CodigoOrdenes.Item 
																		 from  Contadb..SplitStringIndex(@IFiltroTipoOrdenHospital,',') CodigoOrdenes ) 
															AND
										     1 = ISNUMERIC(SUBSTRING(A.CODIGO,3,1)))																		
										  )
										 )
								  OR
										 A.CODIGO IN  (select  CodigoOrdenesGeneral.Item 
																		 from  Contadb..SplitStringIndex(@IFiltroTipoOrdenGeneral,',') CodigoOrdenesGeneral )
										)
								and A.Activo = 'S'
							ORDER BY
								A.Codigo

							COMMIT TRAN
						END
					ELSE
						BEGIN
							SELECT 0 AS codigo, 'No se ha encontrado la Subopcion' AS descripcion, 1 AS tipo_error;

							COMMIT TRAN;
						END
				END
			ELSE IF @IOpcion = 'I'
				BEGIN
					IF @ISubOpcion = '1'
						BEGIN
							SELECT
								@Existe = COUNT(1)
							FROM
								Hospital.dbo.OrdenesTipos
							WHERE
								Empresa = @IEmpresa
							AND
								Codigo = @ICodigo

							IF @Existe = 0
								BEGIN
									INSERT INTO
										Hospital.dbo.OrdenesTipos (Empresa, Codigo, Nombre, BodegaDefault, NumeracionAutomatica, Categorias, Hospital, UbicacionDiag, UbicacionEmer, UbicacionHosp)
									VALUES
										(@IEmpresa,	@ICodigo, @INombre,	@IBodegaDefault, @INumAutomatica, @ICategorias,	@IHospitalTO, @IUbicacionDiag, @IUbicacionEmer,	@IUbicacionHosp)

									SELECT 0 AS codigo, 'Se ha almacenado correctamente el nuevo tipo de orden' AS descripcion, 0 AS tipo_error;
							
									COMMIT TRAN
								END
							ELSE
								BEGIN
									SELECT 0 AS codigo, 'El tipo de orden ' + @ICodigo + ' ya existe para la empresa ' + @IEmpresa AS descripcion, 1 AS tipo_error

									COMMIT TRAN
								END
							

						END
					ELSE
						BEGIN
							SELECT 0 AS codigo, 'No se ha encontrado la Subopcion' AS descripcion, 1 AS tipo_error;

							COMMIT TRAN;
						END
				END
			ELSE IF @IOpcion = 'A'
				BEGIN
					IF @ISubOpcion = '1'
						BEGIN
							UPDATE Hospital.dbo.OrdenesTipos
							SET Nombre = @INombre,
								BodegaDefault = @IBodegaDefault,
								NumeracionAutomatica = @INumAutomatica,
								Categorias = @ICategorias,
								Hospital = @IHospitalTO,
								UbicacionDiag = @IUbicacionDiag,
								UbicacionEmer = @IUbicacionEmer,
								UbicacionHosp = @IUbicacionHosp
							WHERE
								Empresa = @IEmpresa
							AND
								Codigo = @ICodigo

							SELECT 0 AS codigo, 'Se ha actualizado correctamente eL Tipo de Orden' AS descripcion, 0 AS tipo_error;

							COMMIT TRAN
						END
					ELSE
						BEGIN
							SELECT 0 AS codigo, 'No se ha encontrado la Subopcion' AS descripcion, 1 AS tipo_error;

							COMMIT TRAN;
						END
				END
			ELSE IF @IOpcion = 'E'
				BEGIN
					IF @ISubOpcion = '1'
						BEGIN
							SELECT
								@Existe = COUNT(1)
							FROM 
								Hospital.dbo.Ordenes
							WHERE 
								Empresa = @IEmpresa
							AND 
								Tipo = @ICodigo

							IF @Existe = 0
								BEGIN
									DELETE FROM
										Hospital.dbo.OrdenesTipos
									WHERE
										Empresa = @IEmpresa
									AND
										Codigo = @ICodigo

									SELECT 0 AS codigo, 'Se ha eliminado correctamente el Tipo de Orden ' + @ICodigo AS descripcion, 0 AS tipo_error

									COMMIT TRAN
								END
							ELSE
								BEGIN
									SELECT 0 AS codigo, 'No se puede eliminar el tipo de orden ' + @ICodigo + ' debido a que ya existen ordenes con este tipo ' AS descripcion, 1 AS tipo_error

									COMMIT TRAN
								END
						END
					ELSE
						BEGIN
							SELECT 0 AS codigo, 'No se ha encontrado la Subopcion' AS descripcion, 1 AS tipo_error;

							COMMIT TRAN;
						END
				END
			ELSE
				BEGIN
					SELECT 0 AS codigo, 'No se ha encontrado la opcion' AS descripcion, -1 AS tipo_error;

					COMMIT TRAN;
				END
		END TRY
		BEGIN CATCH
			ROLLBACK TRAN
			SET @ErrRaise = ERROR_MESSAGE()
			SET @ErrRaiseNum = ERROR_NUMBER()
			RAISERROR(@ErrRaise, 16, @ErrRaiseNum)
		END CATCH
END
