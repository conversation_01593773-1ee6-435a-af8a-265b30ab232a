import Vue from 'vue'
import axios from 'axios'


//axios
import VueAxios from 'vue-axios'
Vue.use(VueAxios, axios)

import { getField, updateField } from "vuex-map-fields";

//import state from './moduleAdmisionesState'
import {getDefaultState } from './moduleAdmisionesMutations.js'
//import actions from './moduleAdmisionesActions' 
//import {getters}  from './moduleAdmisionesGetters'
//import { getField } from "vuex-map-fields";


const state = {
    AdmisionData: {
        Empresa: null
        , Serie: null
        , Codigo: null
        , Entrada: null
        , Salida: null
        , Paciente: null
        , IdCliente: null
        , Edad: null
        , Habitacion: null
        , NivelPrecios: null
        , Descuento: null
        , PorcentajeDescuento: null
        , TipoDescuento: null
        , Seguro: null
        , Socio: null
        , NombreFactura: null
        , Nit: null
        , DireccionFactura: null
        , NotificarA: null
        , DireccionNotificarA: null
        , TelefonoNotificarA: null
        , Medico: null
        , Razon: null
        , Otros: null
        , Usuario: null
        , Status: null
        , Interno: null
        , Acompanante: null
        , Colegiado: null
        , CoberturaHabitacion: null
        , EdadMedida: null
        , Paquete: null
        , Bebe: null
        , Preoperatorio: null
        , Copago: null
        , Coaseguro: null
        , Seriefactcopago: null
        , FactCopago: null
        , VerificacionSeguro: null
        , VerificacionUsuario: null
        , VerificacionFecha: null
        , PacienteBuscoHospital: 'S'
        , Auditada: null
        , CondicionEgreso: null
        , Tratamiento: null
        , FechaEgreso: null
        , SerieAdmisionRaiz: null
        , AdmisionRaiz: null
        , Peso: null
        , Estatura: null
        , PorcentajeInteresCuenta: null
        , InteresNominalCuenta: null
        , PlanMedax: Boolean
        , IdAfiliado: null
        , MedaxABC: null
        , FechaCierreSiniestralidad: null
        , PaqueteQx: null
        , CoaseguroMonto: null
        , Clasificacion: null
        , AreaServicio: null
        , BloqueaEstCuenta: null
        , HuellaResponsableReg: null
        , HabilitadoHasta: null
        , EmpresaReal: null
        , CopagoACobrar: null
        , CoaseguroACobrar: null
        , CtaCalculada: null
        , FechaCalculo: null
        , UltimaHab: null
        , GastosEspNoCubiertos: null
        , ComentariosSemaforoE: null
        , BeneficioTerceros: null
        , IdTipoDescuento: null
        , Deducible: null
        , AutorizacionBI: null
        , UsuarioPreEgreso: null
        , FechaPreEgreso: null
        , DiagnosticoCOVID: null
        , Corporativo: null
        , DPI: null
        , Pasaporte: null
        , TipoAdmision: 0
        , DescripcionAmision: null
        , PrimeraVacunaCOVID: null
        , SegundaVacunaCOVID: null
        , TerceraVacunaCOVID: null
        , CorreoElectronicoMedico:''
        , CodigoPaqueteBebe:0
        , PrecioPaquete:0.0
        , CodigoBaseHospital: null
        , FechaNacimiento: null

    }
}

const getters = {
    getField,
    // getDescAdmision(state){
    //     const DescripcionAdmision = admisiones.find(obj => obj.id === state.AdmisionData.TipoAdmision)?.name||'';
    //     return DescripcionAdmision
    // },
    getCodigoAdmision(state){        
        return state.AdmisionData.Codigo
    },
    getAdmision(state) {
        return state.AdmisionData.Admision
    }
}

const mutations = {
    updateField,
    SET_CODIGO_ADMISION(state, codigo) {
        state.AdmisionData.Codigo = codigo
    },
    SET_SERIE_ADMISION(state, serie) {
        state.AdmisionData.Serie = serie
    },
    SET_INIT_ADMISION(state){
        Object.assign(state, getDefaultState());
    },
    SET_SERIE_ADMISION_RAIZ(state, serieraiz){
        state.AdmisionData.SerieAdmisionRaiz = serieraiz
    },
    SET_ADMISION_RAIZ(state, admisionraiz){
        state.AdmisionData.AdmisionRaiz = admisionraiz
    }
}


const actions = {
    crearAdmision({ commit }) {


        return new Promise((resolve, reject) => {
            axios.post('/app/v1_admision/CrearAdmision', state.AdmisionData)
                .then(function (response) {
                   
                    if (!state.AdmisionData.Codigo) {
                        commit("SET_CODIGO_ADMISION", response.data.json[0].Codigo)                        
                    }
                    resolve(response)
                })
                .catch(function (error) {

                    reject(error)
                })
        })
        
    },
    initAdmision({commit}){
        commit("SET_INIT_ADMISION")                        
    },
    setSerieRaiz({ commit }, serieraiz){
        commit("SET_SERIE_ADMISION_RAIZ", serieraiz)
    },
    setAdmisionRaiz({ commit }, admisionraiz){
        commit("SET_ADMISION_RAIZ", admisionraiz)
    },
    asignarAdmision({ commit }, admision) {
        commit('SET_ADMISION', admision.Codigo)
    }

   
}
export default {
    namespaced: true,
    state,
    mutations,
    getters,
    actions
}