<template>
<div id="reembolsos">
    <form @submit="handleSubmit">
        <DxForm :ref="formReembolso" :form-data.sync="formulario" label-mode="floating">
            <DxFormGroupItem :col-count-by-screen="colCountByScreen">
                <DxFormItem data-field="Cuenta" editor-type="dxSelectBox" :editor-options="{ width: 'auto', searchEnabled: true, items: cuentas,  displayExpr: 'Valor', valueExpr: 'Codigo', onValueChanged: AsignarSiguienteCheque }" :validationRules="[{ type: 'required' }]" />
                <DxFormItem data-field="SiguienteCheque" editor-type="dxNumberBox" :editor-options="{ min: 0, format: '#0', step: 0 }">
                    <DxFormLabel text="Número" />
                </DxFormItem>
                <DxFormItem data-field="Funcion" editor-type="dxRadioGroup" :editor-options="{ items: funciones, displayExpr:'text', valueExpr: 'id', layout:'horizontal' }">
                    <DxFormLabel text="Función" />
                </DxFormItem>
                <DxFormItem data-field="Monto" editor-type="dxNumberBox" :editor-options="{ format: '#0.00', step: 0.01, readOnly: true }" />
            </DxFormGroupItem>

            <DxFormGroupItem :col-count="2">
                <DxFormGroupItem>

                    <DxFormItem data-field="PagarNombre" editor-type="dxTextBox" :editor-options="{ readOnly: false }">
                        <DxFormLabel text="Pagar a la orden de" />
                    </DxFormItem>
                    <DxFormItem data-field="Observaciones" editor-type="dxTextArea" :editor-options="{ height: '100%' }" />
                </DxFormGroupItem>
                <DxFormGroupItem :col-count="2">
                    <DxFormItem data-field="Caja" editor-type="dxSelectBox" :editor-options="{ width: 'auto', searchEnabled: true, items: cajas,  displayExpr: 'Display', valueExpr: 'Codigo', onItemClick: CargarLotes }" :validationRules="[{ type: 'required' }]" />
                    <DxFormItem data-field="Lote" editor-type="dxSelectBox" :editor-options="{ width: 'auto', searchEnabled: true, items: lotes,  displayExpr: 'Siguiente', valueExpr: 'Siguiente', onItemClick: CargarDocumentosPorPagar }" :validationRules="[{ type: 'required' }]" />
                    <DxFormItem template="documentos" :col-span="2" />
                </DxFormGroupItem>
            </DxFormGroupItem>
            <DxFormGroupItem :col-count="3">
                <DxFormButtonItem :button-options="buttonGrabar" name="Grabar" horizontal-alignment="center" verical-alignment="center" />
                <DxFormButtonItem :button-options="buttonGenerarVoucher" name="GenerarVoucher" horizontal-alignment="center" verical-alignment="center" />
                <DxFormButtonItem :button-options="buttonLimpiar" name="Limpiar" horizontal-alignment="center" verical-alignment="center" />
            </DxFormGroupItem>

            <template #documentos="{}">
                <div class="pb-2">
                    <DxDataGrid :ref="gridDocumentos" v-bind="DefaultDxGridConfiguration" :data-source="valoresDocumentos" :paging="{ enabled: false }" :searchPanel="{ visible: false} " :headerFilter="{ visible: false, allowSearch: false }" :width="'100%'" height="216" @editor-preparing="editorDatagrid" :on-saving="GuardarCambiosDocumentos">
                        <DxDataGridSelection mode="single" />
                        <DxDataGridScrolling mode="virtual" />
                        <DxDataGridEditing mode="row" :allow-updating="false" :allow-adding="(formulario.Caja !== null && formulario.Lote !== null)? true : false" :allow-deleting="true" :use-icons="true" new-row-position="last" />

                        <DxDataGridColumn width="40%" data-field="Proveedor" alignment="center" data-type="string" :validation-rules="[{ type:'required', message: 'Ingrese el código del proveedor' }]" />
                        <DxDataGridColumn width="30%" data-field="Documento" alignment="center" data-type="string" :validation-rules="[{ type:'required', message: 'Ingrese el código del documento' }]" />
                        <DxDataGridColumn width="30%" data-field="Saldo" caption="Monto" alignment="center" data-type="number" :customize-text="customizeTextValores" :validation-rules="[{ type:'required', message: 'Ingrese el monto del documento' }]" />
                        <DxDataGridColumn width="30%" :visible="false" data-field="Observaciones" alignment="center" data-type="string" />
                        <DxDataGridColumn width="30%" :visible="false" data-field="Saldo" alignment="center" data-type="string" />
                    </DxDataGrid>
                </div>
            </template>

        </DxForm>
    </form>

    <div class="pt-2">
        <DxDataGrid :ref="gridDetalle" v-bind="DefaultDxGridConfiguration" :data-source="valoresTabla" :headerFilter="{visible:false, allowSearch:false}" :paging="{ enabled: true, pageSize: 5 }" :searchPanel="{ visible: false }" :width="'100%'" height="auto">
            <DxDataGridSelection mode="single" />

            <DxDataGridColumn width="10%" data-field="Documento" alignment="center" data-type="number" />
            <DxDataGridColumn width="30%" data-field="Detalle" alignment="center" data-type="string" />
            <DxDataGridColumn width="10%" data-field="Cuenta" alignment="center" data-type="string" />
            <DxDataGridColumn width="30%" data-field="Nombre" caption="Nombre de la cuenta" alignment="center" data-type="string" />
            <DxDataGridColumn width="10%" data-field="Debe" alignment="center" data-type="number" :customize-text="customizeTextValores" />
            <DxDataGridColumn width="10%" data-field="Haber" alignment="center" data-type="number" :customize-text="customizeTextValores" />
            <DxDataGridSummary>
                <DxDataGridTotalItem column="Debe" summary-type="sum" :customize-text="customizeTextValores" :value-format="{ type: 'fixedPoint', precision: 2 }" />
                <DxDataGridTotalItem column="Haber" summary-type="sum" :customize-text="customizeTextValores" />
            </DxDataGridSummary>
        </DxDataGrid>
    </div>

</div>
</template>

<script>
import {
    DefaultDxGridConfiguration,
    customizeTextValores
} from './data'

import 'devextreme-vue/text-area'

const gridDetalle = 'gridDetalle'
const gridDocumentos = 'gridDocumentos'
const formReembolso = 'formReembolso'

export default {
    name: 'Reembolsos',
    components: {},
    data() {
        return {
            DefaultDxGridConfiguration,
            gridDetalle,
            gridDocumentos,
            formReembolso,

            cuentas: [],
            valoresTabla: [],
            cajas: [],
            lotes: [],
            valoresDocumentos: [],

            formulario: {
                Cuenta: null, // Codigo de la cuenta de banco de 2 dígitos
                NumeroCuenta: null, // Número de la cuenta bancaria
                BancoContabilidad: null, // Cuenta contable de la cuenta bancaria seleccionada
                SiguienteCheque: null, // Siguiente cheque de la cuenta bancaria
                NombreCuentaBanco: null, // Nombre de la cuenta contable de la cuenta bancaria 
                Funcion: 'CH', // CH = Cheque / ND = Nota de débido / NA = Acreditamiento
                PagarNombre: null, // Beneficiario del cheque  
                Monto: 0, // Monto de la orden, nota de crédito o acreditamiento
                MontoMaximo: null, // Monto de la orden, sirve para validar que no se ingrese un montó mayor al monto de la orden
                Observaciones: null, // Observaciones del reembolso
                Caja: null, // Número de caja
                Lote: null // Número de lote
            },

            funciones: [{
                id: 'CH',
                text: 'Cheque'
            }, {
                id: 'ND',
                text: 'Nota de débito'
            }, ],

            buttonGrabar: {
                width: 'auto',
                icon: 'fas fa-save',
                text: 'Grabar',
                type: 'success',
                onClick: () => {
                    this.Grabar()
                },
                useSubmitBehavior: false,
            },

            buttonGenerarVoucher: {
                width: 'auto',
                icon: 'fas fa-receipt',
                text: 'Generar voucher',
                type: 'default',
                onClick: () => {
                    this.GenerarVoucher()
                },
                useSubmitBehavior: false,
            },

            buttonLimpiar: {
                width: 'auto',
                icon: 'fas fa-broom',
                text: 'Limpiar',
                type: 'warning',
                onClick: () => {
                    this.LimpiarVariables()
                },
                useSubmitBehavior: false,
            },

            cuentaIVACobrar: null,
            periodo: null,

            existeSaldoFinal: true,
        }
    },
    props: {
        Corporativo: null
    },
    methods: {
        handleSubmit(e) {
            e.preventDefault()
        },

        CargarCuentas(asignar) {
            this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 1
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.cuentas = resp.data.json

                        if (asignar == 1) {
                            this.formulario.Cuenta = this.cuentas[0].Codigo
                        } else if (asignar == 2) {
                            let codigo = this.cuentas.find((x) => x.Codigo == this.formulario.Cuenta)

                            this.formulario.SiguienteCheque = codigo.SiguienteCheque
                        }
                    }
                })
        },

        CargarCajas() {
            this.axios.post('/app/v1_bancos/Reembolsos', {
                    Opcion: 1
                })
                .then(resp => {
                    this.cajas = resp.data.json.map((x) => {
                        return {
                            Empresa: x.Empresa,
                            NombreEmpleado: x.NombreEmpleado,
                            Codigo: x.Codigo,
                            Siguiente: x.Siguiente,
                            EmpresaEmpleado: x.EmpresaEmpleado,
                            Empleado: x.Empleado,
                            Fondo: x.Fondo,
                            MaxValorCompra: x.MaxValorCompra,
                            Observacion: x.Observacion,
                            Display: x.Codigo + ' - ' + x.NombreEmpleado
                        }
                    })
                })
        },

        CargarLotes(e) {
            if (e.itemData !== null) {
                this.LimpiarSeleccionaCaja()
                this.axios.post('/app/v1_bancos/Reembolsos', {
                        Opcion: 2,
                        Caja: e.itemData.Codigo
                    })
                    .then(resp => {
                        if (resp.data.json.length > 0) {
                            this.lotes = resp.data.json
                        } else {
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#ed8c72',
                                title: 'Sin lotes',
                                acceptText: 'Aceptar',
                                text: 'La caja seleccionada no tiene lotes',
                                buttonCancel: 'border',
                                accept: () => {},
                            })
                            return
                        }
                    })
            }
        },

        CargarDocumentosPorPagar() {
            if (this.formulario.Caja !== null && this.formulario.Lote !== null) {
                this.axios.post('/app/v1_bancos/Reembolsos', {
                        Opcion: 3,
                        Caja: this.formulario.Caja,
                        Lote: this.formulario.Lote
                    })
                    .then(resp => {
                        if (resp.data.json.length > 0) {
                            this.valoresDocumentos = resp.data.json
                            this.formulario.Monto = parseFloat(this.CalcularMonto(this.valoresDocumentos)).toFixed(2)
                        } else {
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#ed8c72',
                                title: 'Sin registros',
                                acceptText: 'Aceptar',
                                text: 'No existen datos en esta caja y lote',
                                buttonCancel: 'border',
                                accept: () => {},
                            })
                            return
                        }
                    })
            }
        },

        BuscarCheque() {
            this.axios.post('/app/v1_bancos/Anticipos', {
                    Opcion: 14,
                    Cuenta: this.formulario.Cuenta,
                    Numero: this.formulario.SiguienteCheque,
                    TipoMov: 'CH'
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Cheque registrado',
                            acceptText: 'Aceptar',
                            text: 'Un cheque a nombre de ' + resp.data.json[0].Beneficiario + ' ya fue registrado con este número.',
                            buttonCancel: 'border',
                            accept: () => {},
                        })
                        return
                    }
                })
        },

        AsignarSiguienteCheque(e) {
            if (e !== null) {
                let cuenta = this.cuentas.find((x) => x.Codigo == e.value)
                if (cuenta.SiguienteCheque == null || cuenta.SiguienteCheque == '') {
                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'Cuenta sin número',
                        acceptText: 'Aceptar',
                        text: 'Debe ingresar un número para la cuenta seleccionada',
                        buttonCancel: 'border',
                        accept: () => {},
                    })
                } else {
                    this.formulario.BancoContabilidad = cuenta.Contabilidad
                    this.formulario.SiguienteCheque = cuenta.SiguienteCheque
                    this.formulario.NumeroCuenta = cuenta.Cuenta
                    this.formulario.NombreCuentaBanco = cuenta.NombreCuenta
                }
            }
        },

        customizeTextValores,

        async GenerarVoucher() {
            if (this.formulario.NombreCuentaBanco === '' || this.formulario.NombreCuentaBanco === null) {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'Cuenta no definida',
                    acceptText: 'Aceptar',
                    text: 'La cuenta de banco seleccionada no está definida.',
                    buttonCancel: 'border',
                    accept: () => {},
                })
                return
            }

            if (this.valoresDocumentos.length === 0) {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'Sin documentos',
                    acceptText: 'Aceptar',
                    text: 'No hay documentos registrados',
                    buttonCancel: 'border',
                    accept: () => {},
                })
                return
            } else {
                this.valoresTabla = []
                let haber = this.CalcularMonto(this.valoresDocumentos)
                this.formulario.Monto = parseFloat(haber).toFixed(2)

                let tablaAuxiliar = []

                for (let i = 0; i < this.valoresDocumentos.length; i++) {

                    let cuenta = null

                    cuenta = await this.BuscarEgresos(this.valoresDocumentos[i].Proveedor, this.valoresDocumentos[i].Documento)

                    if (cuenta === null) {
                        cuenta = await this.BuscarCuentaProveedor(this.valoresDocumentos[i].Proveedor)
                    }

                    // let nombreCuenta = await this.BuscarNombreCuenta(cuenta)

                    tablaAuxiliar.push({
                        Documento: this.valoresDocumentos[i].Documento,
                        Detalle: this.valoresDocumentos[i].Observaciones,
                        Cuenta: cuenta.Cuenta,
                        Nombre: cuenta.Nombre,
                        Haber: null,
                        Debe: this.valoresDocumentos[i].Saldo
                    })
                }

                // Aquí se llena la tabla con la información de la cuenta seleccionada
                this.valoresTabla.push({
                    Documento: this.formulario.SiguienteCheque,
                    Detalle: null,
                    Cuenta: this.formulario.BancoContabilidad,
                    Nombre: this.formulario.NombreCuentaBanco,
                    Haber: haber,
                    Debe: null
                })

                this.valoresTabla = this.valoresTabla.concat(tablaAuxiliar)
            }

        },

        CalcularMonto(documentos) {
            let haber = 0
            for (let i = 0; i < documentos.length; i++) {
                haber = haber + parseFloat(documentos[i].Saldo)
            }
            return haber
        },

        async BuscarEgresos(proveedor, documento) {
            return await this.axios.post('/app/v1_bancos/PagosGenerados', {
                    Opcion: 5,
                    ProveedorInicio: proveedor,
                    Documento: documento
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        return resp.data.json[0]
                    }
                    return null
                })
        },

        async BuscarCuentaProveedor(proveedor) {
            return await this.axios.post('/app/v1_bancos/Reembolsos', {
                    Opcion: 4,
                    Proveedor: proveedor,
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        return resp.data.json[0]
                    }
                })
        },

        async BuscarNombreCuenta(cuenta) {
            return await this.axios.post('/app/v1_bancos/Cuentas', {
                Opcion: 7,
                Cuenta: cuenta
            }).then(resp => {
                if (resp.data.json.length > 0) {
                    return resp.data.json[0].Nombre
                }
            })
        },

        async CargarCuentasDefault() {
            await this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 9
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.cuentaIVACobrar = resp.data.json[0].IVAPorCobrar
                    }

                })
        },

        async Grabar() {
            if (this.formulario.PagarNombre === null || this.formulario.PagarNombre === '') {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'Nombre beneficiario',
                    acceptText: 'Aceptar',
                    text: 'Debe indicar el nombre del beneficiario. No puede continuar.',
                    buttonCancel: 'border',
                    accept: () => {},
                })
                return
            }

            if (this.formulario.SiguienteCheque === null || this.formulario.SiguienteCheque <= 0) {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'Cheque no válido',
                    acceptText: 'Aceptar',
                    text: 'El número de cheque no es válido. No puede continuar.',
                    buttonCancel: 'border',
                    accept: () => {},
                })
                return
            }

            if (this.valoresDocumentos.length == 0 || this.formulario.Monto === 0) {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'Sin documentos',
                    acceptText: 'Aceptar',
                    text: 'Debe indicar los documentos a pagar.',
                    buttonCancel: 'border',
                    accept: () => {},
                })
                return
            }

            if (this.periodo !== null) {
                let saldoFinal = await this.ConsultarSaldoFinal()
                if (!saldoFinal) {
                    if (this.valoresTabla.length === 0) {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Sin documentos',
                            acceptText: 'Aceptar',
                            text: 'Debe especificar las líneas del voucher. No puede continuar.',
                            buttonCancel: 'border',
                            accept: () => {},
                        })
                        return
                    }

                    const totalDebe = this.dataGridDetalle.getTotalSummaryValue("Debe");
                    const totalHaber = this.dataGridDetalle.getTotalSummaryValue("Haber");

                    if (Math.abs(totalDebe - totalHaber) !== 0) {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Montos no cuadran',
                            acceptText: 'Aceptar',
                            text: 'Las columnas del voucher deben cuadrar y no estar a cero. No puede continuar.',
                            buttonCancel: 'border',
                            accept: () => {},
                        })
                        return
                    }

                    if (this.valoresTabla.length > 16) {
                        this.$vs.dialog({
                            type: 'confirm',
                            color: '#ed8c72',
                            title: 'Exceso de líneas',
                            acceptText: 'Aceptar',
                            text: 'El voucher tiene más de 16 líneas y no caben en el cheque, si continúa las últimas líneas no se imprimirán.',
                            buttonCancel: 'border',
                            accept: () => {
                                this.Grabar1()
                            },
                            cancelText: 'Cancelar',
                            cancel: () => {}
                        })
                        return
                    }

                    this.Grabar1()

                }
            }
        },

        ConsultarPeriodo() {
            this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 1,
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.periodo = resp.data.json[0].Codigo
                    } else {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Período no definido',
                            acceptText: 'Aceptar',
                            text: 'No existe un período para la fecha actual.',
                            buttonCancel: 'border',
                            accept: () => {},
                        })
                        return
                    }
                })
        },

        async ConsultarSaldoFinal() {
            return await this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 2,
                    Cuenta: this.formulario.Cuenta,
                    Periodo: this.periodo
                })
                .then(resp => {
                    if (resp.data.json.length === 0) {
                        return false

                    } else if (resp.data.json[0].SaldoFinal !== null) {
                        // this.existeSaldoFinal = true

                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Período no activo',
                            acceptText: 'Aceptar',
                            text: 'Debe reabrir el registro del libro negro para este período para poder grabar esta transacción.',
                            buttonCancel: 'border',
                            accept: () => {
                                return true
                            },
                        })
                        return
                    }
                })
        },

        async Grabar1() {
            let reembolsos = {
                BancoMovto: {},
                ComprasPagos: [],
                Compras: [],
                AuxiliarBanco: []
            }

            // Graba el cheque en movimientos del banco
            reembolsos.BancoMovto = {
                Opcion: 1,
                Cuenta: this.formulario.Cuenta,
                Numero: this.formulario.SiguienteCheque,
                Periodo: this.periodo,
                Proveedor: null,
                Monto: this.formulario.Monto,
                Usuario: this.Corporativo, // ******************************************* Se debe definir el corporativo
                Observaciones: this.formulario.Observaciones,
                TipoMov: this.formulario.Funcion,
                Conciliado: 'N',
                NombreBeneficiario: this.formulario.Funcion == 'ND' ? '' : this.formulario.PagarNombre,
            }

            for (let i = 0; i < this.valoresDocumentos.length; i++) {
                reembolsos.ComprasPagos.push({
                    Opcion: 5,
                    Proveedor: this.valoresDocumentos[i].Proveedor,
                    Cuenta: this.formulario.Cuenta,
                    Numero: this.formulario.SiguienteCheque,
                    Periodo: this.periodo,
                    TipoMov: this.formulario.Funcion,
                    Monto: this.valoresDocumentos[i].Saldo,
                    Tipo: 'R',
                    Usuario: this.Corporativo,
                    Documento: this.valoresDocumentos[i].Documento,
                    Saldo: this.valoresDocumentos[i].Saldo
                })

                reembolsos.Compras.push({
                    Opcion: 5,
                    Monto: parseFloat(this.valoresDocumentos[i].Saldo),
                    Proveedor: this.valoresDocumentos[i].Proveedor,
                    Documento: this.valoresDocumentos[i].Documento
                })
            }

            for (let i = 0; i < this.valoresTabla.length; i++) {
                reembolsos.AuxiliarBanco.push({
                    Opcion: 4,
                    TipoMov: this.formulario.Funcion,
                    Cuenta: this.formulario.Cuenta,
                    Numero: this.formulario.SiguienteCheque,
                    Periodo: this.periodo,
                    CuentaBanco: this.valoresTabla[i].Cuenta,
                    Documento: this.valoresTabla[i].Documento,
                    Detalle: this.valoresTabla[i].Detalle,
                    Haber: this.valoresTabla[i].Haber,
                    Debe: this.valoresTabla[i].Debe,
                    Indice: parseInt(i + 1),
                    Usuario: this.Corporativo
                })
            }

            await this.axios.post('/app/v1_bancos/ReembolsosAgrupados', {
                    ...reembolsos
                })
                .then(async resp => {
                    if (resp.data.codigo == 0) {
                        await this.axios.post('/app/v1_bancos/Cuentas', {
                                Codigo: this.formulario.Cuenta,
                                SiguienteCheque: parseInt(this.formulario.SiguienteCheque + 1),
                                Opcion: 6
                            })
                            .then(resp => {
                                if (resp.data.codigo == 0) {
                                    this.LimpiarVariables()
                                    this.CargarCuentas(2)
                                }
                            })
                    }
                })

        },

        LimpiarVariables() {
            this.formulario.Funcion = 'CH'
            this.formulario.PagarNombre = null
            this.formulario.Monto = 0
            this.formulario.MontoMaximo = null
            this.formulario.Observaciones = null
            this.formulario.Caja = null
            this.formulario.Lote = null

            this.lotes = []
            this.valoresTabla = []
            this.valoresDocumentos = []
        },

        LimpiarSeleccionaCaja() {
            this.formulario.Funcion = 'CH'
            this.formulario.PagarNombre = null
            this.formulario.Monto = 0
            this.formulario.MontoMaximo = null
            this.formulario.Observaciones = null
            this.formulario.Lote = null

            this.lotes = []
            this.valoresTabla = []
            this.valoresDocumentos = []
        },

        editorDatagrid(e) {
            if (e.parentType === 'dataRow') {

                if (e.dataField === 'Proveedor') {
                    const defaultValueChangeHandler = e.editorOptions.onValueChanged;
                    e.editorOptions.maxLength = 6;

                    e.editorOptions.onValueChanged = async (args) => {
                        args.value = args.value.toUpperCase();
                        // Actualizamos la fila para reflejar los cambios
                        this.$refs.gridDocumentos.instance.refresh();
                        defaultValueChangeHandler(args);
                    }
                }

                if (e.dataField === 'Documento') {
                    const defaultValueChangeHandler = e.editorOptions.onValueChanged;

                    e.editorOptions.onValueChanged = async (args) => {
                        const documento = args.value; // Captura el valor ingresado del proveedor
                        const docEncontrado = await this.BuscarDocumento(e.row.data.Proveedor, documento)
                        const rowIndex = e.row.rowIndex;
                        // Actualizamos la celda de "Nombre"
                        this.$refs.gridDocumentos.instance.cellValue(
                            rowIndex,
                            "Valor",
                            docEncontrado.Saldo ? parseFloat(docEncontrado.Saldo).toFixed(2) : null
                        );
                        this.$refs.gridDocumentos.instance.cellValue(
                            rowIndex,
                            "Saldo",
                            docEncontrado.Saldo ? parseFloat(docEncontrado.Saldo).toFixed(2) : null
                        );
                        this.$refs.gridDocumentos.instance.cellValue(
                            rowIndex,
                            "Observaciones",
                            docEncontrado.Observaciones
                        );
                        // Actualizamos la fila para reflejar los cambios
                        this.$refs.gridDocumentos.instance.refresh();
                        defaultValueChangeHandler(args);
                    }
                }
            }
        },

        async BuscarDocumento(proveedor, documento) {
            return await this.axios.post('/app/v1_bancos/Compras', {
                    Opcion: 2,
                    Proveedor: proveedor,
                    Documento: documento
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        return resp.data.json[0]
                    } else {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Compra no encontrada',
                            acceptText: 'Aceptar',
                            text: 'Esta compra no está registrada en el sistema.',
                            buttonCancel: 'border',
                            accept: () => {
                                return
                            },
                        })
                        return 0
                    }
                })
        },

        GuardarCambiosDocumentos(e) {
            let cambio = e.changes[0]

            if (cambio.type == 'insert') {
                // Mantiene las filas nuevas editables
                this.$nextTick(() => {

                    this.formulario.Monto = parseFloat(this.CalcularMonto(this.valoresDocumentos)).toFixed(2)
                    this.dataGridDocumentos.addRow()
                });

            }
        },
    },
    created() {},
    mounted() {},
    beforeMount() {
        this.CargarCuentas(1)
        this.CargarCajas()

        this.CargarCuentasDefault()
        this.ConsultarPeriodo()
    },
    watch: {
        'formulario.SiguienteCheque'(newval) {
            if (newval !== null && newval !== '' && newval !== undefined && newval !== 0) {
                this.BuscarCheque()
            }
        },

        'formulario.NombreCuentaBanco'(newval) {
            if (newval === null || newval === '' || newval === undefined) {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'Cuenta no definida',
                    acceptText: 'Aceptar',
                    text: 'La cuenta de banco seleccionada no está definida.',
                    buttonCancel: 'border',
                    accept: () => {},
                })
                return
            }
        },
    },
    computed: {
        colCountByScreen() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 2,
                    lg: 2
                };
        },

        dataGridDetalle: function () {
            return this.$refs[gridDetalle].instance;
        },

        dataGridDocumentos: function () {
            return this.$refs[gridDocumentos].instance;
        },

        formReembolsoInstance: function () {
            return this.$refs[formReembolso].instance;
        },
    }
}
</script>

<style>
.padding-buttons {
    padding-bottom: -1px;
}

#reembolsos .dx-button.dx-button-warning {
    background-color: #FF6347 !important;
    color: #fff !important;
}

#reembolsos .dx-button.dx-button-warning .dx-icon {
    color: #fff !important;
}
</style>
