<template>
    <div>
        <vx-card title="Devolución Productos en Consignación">
            <buscador ref="BuscarProveedorConsignacion" buscador_titulo="Buscador / Proveedores" :api="'app/v1_OrdenCompra/ConsultaProveedorConsignacion'" 
            :campos="['Proveedor', 'Nombre', 'Nit', 'Direccion', 'Telefonos']"
            :titulos="['Proveedor', '#Nombre', '#Nit', '#Direccion', '#Telefonos']"
            :api_filtro="{'Proveedor':Proveedor.Proveedor}"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" :api_preload="true" />

            <buscador ref="BuscarProductoProveedorConsignacion" buscador_titulo="Buscador / Productos" :api="'app/v1_OrdenCompra/ConsultaProductoProveedorConsignacion'" 
            :campos="['Producto', 'Descripcion']"
            :titulos="['Producto', '#Descripcion']"
            :api_filtro="{Proveedor:Proveedor.Proveedor, Producto:ProductoBuscar}"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" :api_preload="true" />
            
            <buscador ref="BuscarBodegaConsignacion" buscador_titulo="Buscador / Bodegas" :api="'app/v1_OrdenCompra/ConsultaBodegaConsignacion'" 
            :campos="['CodigoBodega', 'NombreBodega']"
            :titulos="['CodigoBodega', '#NombreBodega']"
            :api_filtro="{CodigoAgrupacion:permisos_agrupacion_bodega,CodigoBodega:CodigoBodega}"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" :api_preload="true" />

            <buscador ref="BuscarTipoConsignacion" buscador_titulo="Buscador / Tipo Movimiento" :api="'app/v1_OrdenCompra/ConsultaTipoConsignacion'" 
            :campos="['Codigo', 'Nombre']"
            :titulos="['Codigo', '#NombreBodega']"
            :api_filtro="{TipoMovimiento:TipoMovimiento.Codigo}"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" :api_preload="true" />

            <vs-row vs-type="flex" vs-align="center" vs-w="12" vs-justify="flex-end">
                <vs-col vs-type="flex" vs-align="center" vs-w="1">
                    <vs-button color="warning" icon-pack="fa" @click="Limpiar()" icon="fa-arrow-circle-right" >Limpiar</vs-button>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="6">
                    <vs-button color="success" icon-pack="fa" @click="DevolverProveedor()" icon="fa-arrow-circle-right" >Devolver</vs-button>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="1">
                    <label class="typo__label">Fecha:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                    <vs-input type="date" v-model="Fecha"></vs-input>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                    <h5>Bodega Consignación</h5>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="1">
                    <label class="typo__label">Tipo:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                    <vs-input v-model="TipoMovimiento.Codigo" @keyup.enter="CargarTipo()" @keydown.tab="CargarTipo()" @change="CargarTipo()" :disabled="ModificarTipo"></vs-input>
                    <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="CargarTipo()" icon="fa-search" :disabled="ModificarTipo"></vs-button>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                    <h5>{{ TipoMovimiento.Nombre }}</h5>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="1">
                    <label class="typo__label">Bodega Fuente:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                    <vs-input ref="BodegaDestino" v-model="CodigoBodega" @keyup.enter="Bodega()" @keydown.tab="Bodega()" :disabled="ModificarBodega"></vs-input>
                    <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="Bodega()" icon="fa-search" :disabled="ModificarBodega"></vs-button>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                    <h5>{{ NombreBodega }}</h5>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="6">
                    <div class="container">
                        <label class="typo__label">Proveedor</label>
                        <vs-divider></vs-divider>
                        <div class="div-container">
                            <vs-row vs-align="center">
                                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                    <vs-input ref="ProveedorConsignacion" v-model="Proveedor.Proveedor" @keyup.enter="Proveedores()" @keydown.tab="Proveedores()" ></vs-input>
                                    <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="Proveedores()" icon="fa-search"></vs-button>
                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                    <label class="typo__label">&nbsp;Nit:&nbsp;</label>
                                    <h5>{{ Proveedor.Nit }}</h5>
                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                    <label class="typo__label">Telefono:&nbsp;</label>
                                    <h5>{{ Proveedor.Telefonos }}</h5>
                                </vs-col>
                            </vs-row>
                            <br>
                            <vs-row vs-align="center">
                                <vs-col vs-type="flex" vs-align="center" vs-w="12">
                                    <label class="typo__label">Nombre:&nbsp;</label>
                                    <h5>{{ Proveedor.Nombre }}</h5>
                                </vs-col>
                            </vs-row>
                            <br>
                            <vs-row vs-align="center">
                                <vs-col vs-type="flex" vs-align="center" vs-w="12">
                                    <label class="typo__label">Dirección:&nbsp;</label>
                                    <h5>{{ Proveedor.Direccion }}</h5>
                                </vs-col>
                            </vs-row>
                        </div>
                    </div>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="6">
                    <div class="container">
                        <label class="typo__label">Motivo de la Devolución</label>
                        <vs-divider></vs-divider>
                        <div class="div-container">
                            <vs-row vs-align="center">
                                <vs-col vs-type="flex" vs-align="center" vs-w="12">
                                    <vs-input class="w-full" v-model="Motivo"></vs-input>
                                </vs-col>
                            </vs-row>
                            <br>
                            <vs-row vs-align="center">
                                <vs-col vs-align="center" vs-w="12">
                                    <label class="typo__label">Persona que recibe:&nbsp;</label>
                                    <vs-input class="w-full" v-model="PersonaRecibe"></vs-input>
                                </vs-col>
                            </vs-row>
                        </div>
                    </div>
                </vs-col>
            </vs-row>
            <br>
            <div>
                <vs-row vs-type="flex" vs-w="12">
                    <vs-col vs-type="flex" vs-w="12" vs-align="center">
                        <label class="vs-input--label">Producto &nbsp;</label>
                        <vs-input ref="ProductoDeseado" v-model="ProductoBuscar" @keyup.enter="CargarProducto()" @keydown.tab="CargarProducto()" />
                        <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="CargarProducto()" icon="fa-search"></vs-button>
                    </vs-col>
                </vs-row>
                <br>
                <vs-row vs-type="flex" vs-w="12" >
                    <vs-col vs-type="flex" vs-w="12" vs-align="center">
                        <!----- Detalle producto seleccionado--->
                        <div v-if="Producto_seleccionado.Producto>0" class="w-full md:w-4/5 lg:w-4/5 xl:w-4/5">
                            <div style="border-radius:5px;padding:5px;font-size:14px;background-color:#A5D6A7">
                                Código: {{Producto_seleccionado.Producto}}
                                <br>
                                Producto: {{Producto_seleccionado.Descripcion}}
                                <br>
                                Presentación: {{Producto_seleccionado.NombreUnidad}}
                                <br>
                                Precio Convenio: {{$formato_moneda(Producto_seleccionado.PrecioConvenio)}}
                            </div>
                        </div>
                    </vs-col>
                </vs-row>
                <br>
                <vs-row vs-type="flex" vs-w="12" >
                    <vs-col vs-type="flex" vs-align="center" vs-w="3">
                        <label class="vs-input--label">Cantidad &nbsp;</label>
                        <vs-input ref="CantidadProducto" @change="onChangeCantidad" v-model="Cantidad" />
                    </vs-col>
                    <vs-col vs-type="flex" vs-align="center" vs-w="5">
                        Valor Total:&nbsp;&nbsp;<h3>{{ $formato_moneda(ValorSubTotal) }}</h3>
                    </vs-col>
                </vs-row>
                <br>
                <vs-row vs-type="flex" vs-w="12" >
                    <vs-col vs-type="flex" vs-align="center" vs-w="3">
                        <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="AgregarProducto()" icon="fa-plus-circle">Agregar</vs-button>
                    </vs-col>
                </vs-row>
            </div>
            <br>
            <vs-row>
                <vs-col vs-type="flex" vs-align="center" vs-justify="center" vs-w="12">
                    <vs-table2 class="w-full" max-items="10" tooltip pagination :data="detalleProductosManual" >
                        <template slot="thead">
                            <th order="Producto" width="20px">Producto</th>
                            <th order="Cantidad" width="100px">Cantidad</th>
                            <th order="UnidadMedida" width="20px">U Medida</th>
                            <th order="Descripcion" width="20px">Nombre</th>
                            <th order="CostoPromedio" width="20px">Costo Promedio</th>
                            <th order="ValorTotal" width="20px">Costo Total</th>
                            <th width="20px">Eliminar</th>
                        </template>
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 noTooltip>
                                    {{ tr.Producto }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ tr.Cantidad }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ tr.NombreUnidad }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.Descripcion }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ $formato_moneda(tr.CostoPromedio) }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ $formato_moneda(tr.ValorTotal) }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="EliminarProducto(indextr, data[indextr])"></vs-button>
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
                </vs-col>
            </vs-row>
            <vs-row vs-type="flex" vs-w="12" vs-justify="flex-end" >
                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                    Total:&nbsp;&nbsp;<h3>{{ $formato_moneda(GranTotal) }}</h3>
                </vs-col>
            </vs-row>
        </vx-card>
    </div>
</template>
<script>
    import moment from 'moment'
    export default {
        data(){
            return{
                Fecha: '',
                TipoMovimiento: {
                    Codigo: '',
                    Nombre: '',
                    BodegaFuente: '',
                    BodegaDestino: ''
                },
                CodigoBodega: '',
                NombreBodega: '',
                NombreBodegaFuente: '',
                Proveedor: {
                    Proveedor: '',
                    Nombre: '',
                    Nit: '',
                    Telefonos: '',
                    Direccion: ''
                },
                Motivo: '',
                PersonaRecibe: '',
                detalleProductosManual: [{
                    Producto: '',
                    Cantidad: 0,
                    UnidadMedida: '',
                    NombreUnidad: '',
                    Descripcion: '',
                    PrecioUnitario: 0,
                    ValorTotal: 0,
                    Convenio: '',
                    PrecioConvenio: 0,
                    CostoConvenio: 0,
                    CostoPromedio: 0,
                    NumeroSerie: ''
                }],
                Producto_seleccionado: {
                    Producto: '',
                    Cantidad: 0,
                    UnidadMedida: '',
                    NombreUnidad: '',
                    Descripcion: '',
                    PrecioUnitario: 0,
                    ValorTotal: 0,
                    Convenio: '',
                    PrecioConvenio: 0,
                    CostoConvenio: 0,
                    CostoPromedio: 0
                },
                ProductoBuscar: '',
                Cantidad: '',
                ValorSubTotal: '',
                GranTotal: '',
                permisos_agrupacion_bodega: '',
                ModificarTipo: false,
                ModificarBodega: false,
                ValidaExistenciaDevolver: true
            }
        },
        methods: {
            ObtenerFechas(){
                this.Fecha = moment(new Date(Date.now())).format('YYYY-MM-DD')
            },
            CargarTipo(){
                if(this.TipoMovimiento.Codigo != ''){
                    this.axios.post('/app/v1_OrdenCompra/ConsultaTipoConsignacion', {
                    TipoMovimiento: this.TipoMovimiento.Codigo
                    })
                    .then(resp => {
                        this.TipoMovimiento = resp.data.json[0]
                    })
                }else{
                    this.$refs.BuscarTipoConsignacion.iniciar((data) =>{
                        if(data != null){
                            this.TipoMovimiento = data
                        }
                    })
                }
               
            },
            Proveedores(){
                if(this.Proveedor.Proveedor != ''){
                    this.axios.post('/app/v1_OrdenCompra/ConsultaProveedorConsignacion', {
                        Proveedor: this.Proveedor.Proveedor
                    })
                    .then(resp => {
                        if(resp.data.codigo == 0){
                            if(resp.data.json.length == 1){
                                this.Proveedor = resp.data.json[0]
                            }else{
                                this.$refs.BuscarProveedorConsignacion.iniciar((data) =>{
                                    if(data != null){
                                        this.Proveedor = data
                                    }
                                })
                            }
                        }
                    })
                }else{
                    this.$refs.BuscarProveedorConsignacion.iniciar((data) =>{
                        if(data != null){
                            this.Proveedor = data
                        }
                    })
                }
            },
            Bodega(){
                if(this.CodigoBodega != ''){
                    this.axios.post('/app/v1_OrdenCompra/ConsultaBodegaConsignacion', {
                        CodigoBodega: this.CodigoBodega,
                        CodigoAgrupacion: this.permisos_agrupacion_bodega
                    })
                    .then(resp => {
                        if(resp.data.codigo == 0){
                            if(resp.data.json.length == 1){
                                this.NombreBodega = resp.data.json[0].NombreBodega
                            }else{
                                this.$refs.BuscarBodegaConsignacion.iniciar((data) => {
                                    if(data != null){
                                        this.CodigoBodega = data.CodigoBodega
                                        this.NombreBodega = data.NombreBodega
                                        this.BodegaFuente = data.BodegaFuente
                                        this.BodegaDestino = data.BodegaDestino
                                    }
                                })
                            }
                        }
                    })
                }else{
                    this.$refs.BuscarBodegaConsignacion.iniciar((data) => {
                        if(data != null){
                            this.CodigoBodega = data.CodigoBodega
                            this.NombreBodega = data.NombreBodega
                            this.BodegaFuente = data.BodegaFuente
                            this.BodegaDestino = data.BodegaDestino
                        }
                    })
                }
            },
            CargarProducto(){
                if(this.Proveedor.Proveedor != ''){
                    if(this.ProductoBuscar != ''){
                        this.axios.post('/app/v1_OrdenCompra/ConsultaProductoProveedorConsignacion', {
                            Proveedor: this.Proveedor.Proveedor,
                            Producto: this.ProductoBuscar
                        })
                        .then(resp =>{
                            if(resp.data.json.length == 1){
                                this.ProductoBuscar = resp.data.json[0].Producto
                                this.Producto_seleccionado.Producto = resp.data.json[0].Producto
                                this.Producto_seleccionado.UnidadMedida = resp.data.json[0].UnidadMedida
                                this.Producto_seleccionado.NombreUnidad = resp.data.json[0].NombreUnidad
                                this.Producto_seleccionado.Descripcion = resp.data.json[0].Descripcion
                                this.Producto_seleccionado.PrecioConvenio = resp.data.json[0].PrecioConvenio
                                this.Producto_seleccionado.CostoConvenio = resp.data.json[0].CostoConvenio
                                this.Producto_seleccionado.Convenio = resp.data.json[0].Convenio
                                this.Producto_seleccionado.CostoPromedio = resp.data.json[0].CostoPromedio

                                this.$refs.CantidadProducto.focusInput()
                            }else{
                                this.$refs.BuscarProductoProveedorConsignacion.iniciar((data) => {
                                    if(data != null){
                                        this.ProductoBuscar = data.Producto
                                        this.Producto_seleccionado.Producto = data.Producto
                                        this.Producto_seleccionado.UnidadMedida = data.UnidadMedida
                                        this.Producto_seleccionado.NombreUnidad = data.NombreUnidad
                                        this.Producto_seleccionado.Descripcion = data.Descripcion
                                        this.Producto_seleccionado.PrecioConvenio = data.PrecioConvenio
                                        this.Producto_seleccionado.CostoConvenio = data.CostoConvenio
                                        this.Producto_seleccionado.Convenio = data.Convenio
                                        this.Producto_seleccionado.CostoPromedio = data.CostoPromedio

                                        this.$refs.CantidadProducto.focusInput()
                                    }
                                })
                            }
                        })
                    }else{
                        this.$refs.BuscarProductoProveedorConsignacion.iniciar((data) => {
                            if(data != null){
                                this.ProductoBuscar = data.Producto
                                this.Producto_seleccionado.Producto = data.Producto
                                this.Producto_seleccionado.UnidadMedida = data.UnidadMedida
                                this.Producto_seleccionado.NombreUnidad = data.NombreUnidad
                                this.Producto_seleccionado.Descripcion = data.Descripcion
                                this.Producto_seleccionado.PrecioConvenio = data.PrecioConvenio
                                this.Producto_seleccionado.CostoConvenio = data.CostoConvenio
                                this.Producto_seleccionado.Convenio = data.Convenio
                                this.Producto_seleccionado.CostoPromedio = data.CostoPromedio

                                this.$refs.CantidadProducto.focusInput()
                            }
                        })
                    }
                }else{
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Pedido Consignación',
                        text: 'Debe de Seleccionar un Proveddor',
                    })
                }
            },
            onChangeCantidad(){
                if(this.Producto_seleccionado.Producto != ''){
                    this.ValorSubTotal = parseFloat(this.Producto_seleccionado.CostoPromedio) * parseFloat(this.Cantidad)
                    // this.ValidaExistencia()
                }else{
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Pedido Consignación',
                        text: 'Debe de seleccionar un producto',
                    })
                    this.$refs.ProductoDeseado.focusInput()
                }
                
            },
            async AgregarProducto(){
                if(this.Producto_seleccionado.Producto == '' || this.Producto_seleccionado.Producto == null || this.Producto_seleccionado.Producto == 0){
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Pedido Consignación',
                        text: 'Debe de seleccionar un producto',
                    })
                    return
                }
                if(this.Cantidad == '' || this.Cantidad == null || this.Cantidad == 0){
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Pedido Consignación',
                        text: 'Debe de ingresar la cantidad',
                    })
                    return
                }
                await this.ValidaExistencia()
                if(this.ValidaExistenciaDevolver)
                {
                    return
                }
                const found = this.detalleProductosManual.find(element => element.Producto == this.Producto_seleccionado.Producto)
                if(found){
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Pedido Consignación',
                        text: 'Este código ya fue ingresado, no se puede volver a ingresar',
                    })

                    this.Cantidad = 0

                    return
                }

                this.detalleProductosManual.push({
                    Producto: this.Producto_seleccionado.Producto,
                    Cantidad: this.Cantidad,
                    UnidadMedida: this.Producto_seleccionado.UnidadMedida,
                    NombreUnidad: this.Producto_seleccionado.NombreUnidad,
                    Descripcion: this.Producto_seleccionado.Descripcion,
                    PrecioUnitario: this.Producto_seleccionado.PrecioUnitario,
                    ValorTotal: this.ValorSubTotal,
                    Convenio: this.Producto_seleccionado.Convenio,
                    PrecioConvenio: this.Producto_seleccionado.PrecioConvenio,
                    CostoConvenio: this.Producto_seleccionado.CostoConvenio,
                    CostoPromedio: this.Producto_seleccionado.CostoPromedio,
                    NumeroSerie: ''
                })

                let total = 0;
                this.detalleProductosManual.forEach(element => {
                    total += (Number(element.ValorTotal))
                });

                this.GranTotal = parseFloat(total)
                this.LimpiarProducto()
                this.$refs.ProductoDeseado.focusInput()
            },
            EliminarProducto(index, datos){
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',
                    text: '¿Eliminar detalle? ',
                    acceptText: 'Aceptar',
                    cancelText: 'Cancelar',
                    accept: () => {
                        this.GranTotal = parseFloat(this.GranTotal - datos.ValorTotal)
                        this.$delete(this.detalleProductosManual, index)
                    }
                })

            },
            DevolverProveedor(){
                if(this.PersonaRecibe == '' || this.PersonaRecibe == null || this.PersonaRecibe == 0){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'Por favor indique el nombre de la persona que recibirá el producto por parte del proveedor...'
                    })
                    return
                }
                if(this.Motivo == '' || this.Motivo == null || this.Motivo == 0){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'Por favor indique un motivo para realizar la devolución...'
                    })
                    return
                }
                if(this.CodigoBodega == '' && this.BodegaFuente == 'S'){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'Bodega Inválida...'
                    })
                    return
                }
                if(this.detalleProductosManual.length <= 0){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'Detalle Inválido...'
                    })
                    return
                }
                this.axios.post('/app/v1_OrdenCompra/DevolucionProductoProveedor', {
                    FechaIngreso: this.Fecha,
                    TipoMovimiento: this.TipoMovimiento.Codigo,
                    CodigoBodega: this.CodigoBodega,
                    Proveedor: this.Proveedor.Proveedor,
                    Observacion: this.Motivo,
                    CodigoAgrupacion: this.permisos_agrupacion_bodega,
                    NoDocumento: '0',
                    NoPedido: '0',
                    Motivo: '',
                    Impuesto: '1',
                    Recibe: this.PersonaRecibe,
                    NoEnvio: '0',
                    TipoBodegaFuente: this.TipoMovimiento.BodegaFuente,
                    ValorTotal: this.GranTotal,
                    Datos: JSON.stringify(this.detalleProductosManual)
                })
                .then(resp => {
                    if(resp.data.codigo == '0'){
                        let resultado = resp.data.resultado.split(',')
                        this.$reporte_modal({
                            Nombre: "Devolucion Consignacion",
                            Opciones: {
                                Devolucion: resultado[1],
                                Proveedor: this.Proveedor.Proveedor
                            },
                            Formato: "PDF"
                        })

                        this.LimpiarProducto()
                        this.Limpiar()
                    }
                })

            },
            async ValidaExistencia(){
                if(this.Cantidad != '' && this.Cantidad != null){
                    return this.axios.post('/app/v1_OrdenCompra/ValidaExistencia',  {
                        Producto: this.Producto_seleccionado.Producto,
                        CodigoBodega: this.CodigoBodega,
                        CantidadValidar: this.Cantidad
                    })
                    .then(() => {
                        this.ValidaExistenciaDevolver = false
                    })
                    .catch(() => {
                        this.ValidaExistenciaDevolver = true
                        this.ValorSubTotal = 0
                        this.Cantidad = 0
                    });
                }
            },
            LimpiarProducto(){
                    this.Producto_seleccionado.Producto = '',
                    this.Producto_seleccionado.Cantidad = '',
                    this.Producto_seleccionado.UnidadMedida = '',
                    this.Producto_seleccionado.NombreUnidad = '',
                    this.Producto_seleccionado.Descripcion = '',
                    this.Producto_seleccionado.PrecioUnitario = 0,
                    this.Producto_seleccionado.ValorTotal = 0,
                    this.Producto_seleccionado.Convenio = '',
                    this.Producto_seleccionado.PrecioConvenio = 0
                    this.Producto_seleccionado.CostoPromedio = 0
                    this.ValorSubTotal = 0
                    this.Cantidad = ''
                    this.ProductoBuscar = ''
            },
            Limpiar(){
                this.ObtenerFechas()
                this.CargarTipo()
                this.Bodega()
                this.detalleProductosManual = [],
                this.Motivo = '',
                this.Proveedor = {},
                this.PersonaRecibe = '',
                this.GranTotal = 0
            }
        },
        mounted(){
            for(let privilegio of this.$store.state.privilegios){
                if(privilegio.Privilegio.includes("AGRUPACION_BODEGA")){
                    if(this.permisos_agrupacion_bodega != ''){
                        this.permisos_agrupacion_bodega += `,${privilegio.Privilegio.split('_')[2]}`
                    }else{
                        this.permisos_agrupacion_bodega = privilegio.Privilegio.split('_')[2]
                    }
                }
                if(privilegio.Privilegio.includes("MOVIMIENTO_DEFAULT")){
                    if(this.TipoMovimiento.Codigo != ''){
                        this.TipoMovimiento.Codigo += `,${privilegio.Privilegio.split('_')[2]}`
                    }else{
                        this.TipoMovimiento.Codigo = privilegio.Privilegio.split('_')[2]
                    }
                    this.ModificarTipo = this.$validar_privilegio('MOVIMIENTO_DEFAULT_' + this.TipoMovimiento.Codigo).status
                }
                if(privilegio.Privilegio.includes("BODEGA_DEFAULT")){
                    if(this.CodigoBodega != ''){
                        this.CodigoBodega += `,${privilegio.Privilegio.split('_')[2]}`
                    }else{
                        this.CodigoBodega = privilegio.Privilegio.split('_')[2]
                    }
                    this.ModificarBodega = this.$validar_privilegio('BODEGA_DEFAULT_' + this.CodigoBodega).status
                }
            }
            this.ObtenerFechas()
            this.CargarTipo()
            this.Bodega()
            this.detalleProductosManual = []
        }
    }
</script>
<style lang="scss" scoped>
    .container{
        border: 1px solid #888;

    }
    .div-container{
        padding: 5px;
        margin: 10px;
    }

</style>