export class Admision {
    constructor() {
      this.Habitacion = null;
      this.DiagnosticoCovid = null;
      this.DiagnosticoMedico = null;
      this.InformacionPaciente = null;
      this.TiposDescuento = null;
      this.SerieCodigoAdmision = null;
      this.DatosMedico = null;
    }
  }
  
  // AdmisionBuilder.js
  export class AdmisionBuilder {
    constructor() {
      this.admision = new Admision();
    }
  
    setHabitacion(habitacionComponent) {
      this.admision.Habitacion = habitacionComponent;
      return this;
    }
  
    setDiagnosticoCovid(diagnosticoCovidComponent) {
      this.admision.DiagnosticoCovid = diagnosticoCovidComponent;
      return this;
    }
  
    setDiagnosticoMedico(diagnosticoMedicoComponent) {
      this.admision.DiagnosticoMedico = diagnosticoMedicoComponent;
      return this;
    }
  
    setInformacionPaciente(informacionPacienteComponent) {
      this.admision.InformacionPaciente = informacionPacienteComponent;
      return this;
    }
  
    setTiposDescuento(TiposDescuentoComponent) {
      this.admision.TiposDeCobro = TiposDescuentoComponent;
      return this;
    }
  
    setSerieCodigoAdmision(serieCodigoAdmisionComponent) {
      this.admision.SerieCodigoAdmision = serieCodigoAdmisionComponent;
      return this;
    }
  
    setDatosMedico(datosMedicoComponent) {
      this.admision.DatosMedico = datosMedicoComponent;
      return this;
    }
  
    build() {
      return this.admision;
    }
  }