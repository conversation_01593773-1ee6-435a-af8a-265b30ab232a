<template>
    <div>
        <buscador ref="BuscarProveedorConsignacion" buscador_titulo="Buscador / Proveedores" :api="'app/v1_OrdenCompra/ConsultaProveedorConsignacion'" 
            :campos="['Proveedor', 'Nombre', 'Nit', 'Direccion', 'Telefonos']"
            :titulos="['Proveedor', '#Nombre', '#Nit', '#Direccion', '#Telefonos']"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" :api_preload="true" />

        <vs-popup classContent="popup-example" title="Mantenimiento de Liquidaciones de Consignación Datos de la Liquidación" :active.sync="IsModificacionProveedor">
            <vs-row vs-align="center" vs-w="12">
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <label class="typo__label">Liquidación:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="3">
                    <vs-input v-model="ProveedorMod.Correlativo" disabled/>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center" vs-w="12">
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <label class="typo__label">Proveedor:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="3">
                    <vs-input ref="ProveedorConsignacion" v-model="ProveedorMod.Proveedor" @keyup.enter="Proveedores()" @keydown.tab="Proveedores()" disabled></vs-input>
                    <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="Proveedores()" icon="fa-search"></vs-button>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="3">
                    <h5>{{ ProveedorMod.Nombre }}</h5>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-type="flex" vs-align="center" vs-w="12">
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <label class="typo__label">Fecha:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="3">
                    <h5>{{ ProveedorMod.Fecha }}</h5>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <label class="typo__label">Nit:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="3">
                    <h5>{{ ProveedorMod.Nit }}</h5>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-type="flex" vs-align="center" vs-w="12">
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <label class="typo__label">Estado:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="3">
                    <h5>{{ ProveedorMod.Status }}</h5>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-type="flex" vs-align="center" vs-w="12">
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <label class="typo__label">Orden de Compra:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="3">
                    <h5>{{ ProveedorMod.OrdenCompra }}</h5>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <label class="typo__label">Factura:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="3">
                    <h5>{{ ProveedorMod.Factura }}</h5>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center" vs-justify="flex-end">
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-button color="primary" icon-pack="fas" icon="fa-save" @click="CambiarProveedor()">Actualizar</vs-button>
                </vs-col>
            </vs-row>
        </vs-popup>
        <vx-card title="Consulta de Liquidaciones">
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="12">
                    <div class="almacenador w-full">
                        <div class="div-container">
                            <vs-row vs-align="center" vs-justify="center">
                                <label class="typo__label">&nbsp;Periodo</label>
                                <vs-divider></vs-divider>
                                <vs-col vs-type="flex" vs-align="center" vs-w="12">
                                    <vs-row vs-align="center" vs-justify="flex-end">
                                        <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                            <label class="typo__label">Del:&nbsp;</label>
                                            <vs-input type="date" v-model="FechaDel"></vs-input>
                                        </vs-col>
                                        <vs-col vs-type="flex" vs-align="center" vs-w="5">
                                            <label class="typo__label">Al:&nbsp;</label>
                                            <vs-input type="date" v-model="FechaAl"></vs-input>
                                        </vs-col>
                                    </vs-row>
                                </vs-col>
                            </vs-row>
                        </div>
                    </div>
                </vs-col>
            </vs-row>
            <br>
            <vs-divider></vs-divider>
            <br>
            <vs-row vs-align="center" vs-justify="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="12">
                    <div ref="tablaProductos" class="w-full div-table">
                        <vs-table2 max-items="10" tooltip pagination search :data="DetalleLiquidacion">
                            <template slot="thead">
                                <th filtro="Tipo">Tipo</th>
                                <th>Correlativo</th>
                                <th>Fecha</th>
                                <th>Proveedor</th>
                                <th>Nombre Proveedor</th>
                                <th>Nit</th>
                                <th>Orden de Compra</th>
                                <th>Factura</th>
                                <th>Status</th>
                                <th>Usuario</th>
                                <th>Fecha Inical</th>
                                <th>Fecha Final</th>
                                <th>Comentario</th>
                                <th>Modificar Proveedor</th>
                                <th>Ver Correlativo</th>
                                <th>Ver Reporte con Costo Ultimo</th>
                                <th>Exportar a Hojas Electronicas Detalle</th>
                                <th>Desvincular Orden de Compra</th>
                                <th>Consulta y Edición de Liquidaciones</th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :style="getStyle(`${tr.Status}`)" :data="tr" :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2 :data="data[indextr].Tipo">
                                        {{ data[indextr].Tipo }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Correlativo">
                                        {{ data[indextr].Correlativo }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Fecha">
                                        {{ data[indextr].Fecha }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Proveedor">
                                        {{ data[indextr].Proveedor }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Nombre">
                                        {{ data[indextr].Nombre }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Nit">
                                        {{ data[indextr].Nit }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].OrdenCompra">
                                        {{ data[indextr].OrdenCompra }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Factura">
                                        {{ data[indextr].Factura }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Status">
                                        {{ data[indextr].Status }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Usuario">
                                        {{ data[indextr].Usuario }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].FechaInicial">
                                        {{ data[indextr].FechaInicial }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].FechaFinal">
                                        {{ data[indextr].FechaFinal }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Comentario">
                                        {{ data[indextr].Comentario }}
                                    </vs-td2>
                                    <vs-td2 noTooltip>
                                        <vs-button v-if="PermisoCambiarProveedor && tr.Tipo != 'O'" color="primary" icon-pack="fas" icon="fa-user" @click="IsModificacionProveedor=true;ProveedorMod=tr"  class="mr-1" style="display:inline-block">
                                        </vs-button>
                                        <vs-button v-else color="#B2BABB" icon-pack="fas" icon="fa-user" class="mr-1" style="display:inline-block">
                                        </vs-button>
                                    </vs-td2>
                                    <vs-td2 noTooltip>
                                        <vs-button color="primary" icon-pack="fas" icon="fa-file-invoice" @click="VerCorrelativo(tr)"  class="mr-1" style="display:inline-block">
                                        </vs-button>
                                    </vs-td2>
                                    <vs-td2 noTooltip>
                                        <vs-button color="primary" icon-pack="fas" icon="fa-file-invoice" @click="VerReporteCostoUltimo(tr)"  class="mr-1" style="display:inline-block">
                                        </vs-button>
                                    </vs-td2>
                                    <vs-td2 noTooltip>
                                        <vs-button color="primary" icon-pack="fas" icon="fa-file-excel" @click="VerExcelDetallado(tr)"  class="mr-1" style="display:inline-block">
                                        </vs-button>
                                    </vs-td2>
                                    <vs-td2 noTooltip>
                                        <vs-button v-if="PermisoDevincular && tr.Tipo != 'O'" color="danger" icon-pack="fas" icon="fa-rectangle-xmark" @click="DesvincularOrdenCompra(tr)"  class="mr-1" style="display:inline-block">
                                        </vs-button>
                                        <vs-button v-else color="#B2BABB" icon-pack="fas" icon="fa-rectangle-xmark" class="mr-1" style="display:inline-block">
                                        </vs-button>
                                    </vs-td2>
                                    <vs-td2 noTooltip>
                                        <vs-button color="warning" icon-pack="fas" icon="fa-search" @click="ConsultaEdicionLiquidaciones(tr);IsDetalle=true"  class="mr-1" style="display:inline-block">
                                        </vs-button>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-type="flex" vs-align="center" vs-w="12" vs-justify="flex-end">
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-button color="warning" icon-pack="fa" icon="fa-file-excel" @click="GenerarExcel()">Excel</vs-button>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-button color="success" icon-pack="fa" icon="fa-save" @click="ConsultaLiquidacionesConsignacion()" >Ver Listado</vs-button>
                </vs-col>
            </vs-row>
            <vs-popup classContent="popup-example" title="Detalle Liquidación Consignación" :active.sync="IsDetalle">
                <vs-row vs-type="flex" vs-align="center" vs-w="12">
                    <vs-col vs-type="flex" vs-align="center" vs-w="2">
                        <label class="typo__label">&nbsp;Liquidación:&nbsp;</label>
                        <h5>{{ selected.Correlativo }}</h5>
                    </vs-col>
                </vs-row>
                <br>
                <vs-row vs-type="flex" vs-align="center" vs-w="12">
                    <vs-col vs-type="flex" vs-align="center" vs-w="12">
                        <label class="typo__label">&nbsp;Proveedor:&nbsp;</label>
                        <h5>{{ selected.Proveedor }} - {{ selected.Nombre }}</h5>
                    </vs-col>
                </vs-row>
                <br>
                <vs-row vs-type="flex" vs-align="center" vs-w="12">
                    <vs-col vs-type="flex" vs-align="center" vs-w="12">
                        <label class="typo__label">&nbsp;Fecha:&nbsp;</label>
                        <h5>{{ selected.Fecha }}</h5>
                    </vs-col>
                </vs-row>
                <br>
                <vs-row vs-type="flex" vs-align="center" vs-w="12">
                    <vs-col vs-type="flex" vs-align="center" vs-w="12">
                        <label class="typo__label">&nbsp;Estado:&nbsp;</label>
                        <h5>{{ selected.Status }}</h5>
                    </vs-col>
                </vs-row>
                <br>
                <vs-divider></vs-divider>
                <br>
                <vs-row vs-align="center">
                    <vs-col vs-type="flex" vs-align="center" vs-w="12">
                        <div ref="tablaProductos" class="w-full div-table">
                            <vs-table multiple max-items="10" pagination search :data="DetalleProductos" v-model="selectedProducto">
                                <template slot="thead">
                                    <vs-th>Linea</vs-th>
                                    <vs-th>Código</vs-th>
                                    <vs-th>Producto</vs-th>
                                    <vs-th>Hopital</vs-th>
                                    <vs-th>Serie</vs-th>
                                    <vs-th>Admisión</vs-th>
                                    <vs-th>Cantidad</vs-th>
                                    <vs-th>Tipo</vs-th>
                                    <vs-th>Cargo</vs-th>
                                    <vs-th>Fecha</vs-th>
                                    <vs-th>Bodega</vs-th>
                                </template>
                                <template slot-scope="{data}">
                                    <vs-tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td :data="data[indextr].Linea">
                                            {{ data[indextr].Linea }}
                                        </vs-td>
                                        <vs-td :data="data[indextr].Codigo">
                                            {{ data[indextr].Codigo }}
                                        </vs-td>
                                        <vs-td :data="data[indextr].Producto">
                                            {{ data[indextr].Producto }}
                                        </vs-td>
                                        <vs-td :data="data[indextr].Hospital">
                                            {{ data[indextr].Hospital }}
                                        </vs-td>
                                        <vs-td :data="data[indextr].SerieAdmision">
                                            {{ data[indextr].SerieAdmision }}
                                        </vs-td>
                                        <vs-td :data="data[indextr].Admision">
                                            {{ data[indextr].Admision }}
                                        </vs-td>
                                        <vs-td :data="data[indextr].Cantidad">
                                            {{ parseInt(data[indextr].Cantidad) }}
                                        </vs-td>
                                        <vs-td :data="data[indextr].Tipo_Cargo">
                                            {{ data[indextr].Tipo_Cargo }}
                                        </vs-td>
                                        <vs-td :data="data[indextr].Cargo">
                                            {{ data[indextr].Cargo }}
                                        </vs-td>
                                        <vs-td :data="data[indextr].Fecha">
                                            {{ data[indextr].Fecha }}
                                        </vs-td>
                                        <vs-td :data="data[indextr].Bodega">
                                            {{ data[indextr].Bodega }}
                                        </vs-td>
                                    </vs-tr>
                                </template>
                            </vs-table>
                        </div>
                    </vs-col>
                </vs-row>
                <br>
                <vs-row vs-align="center" vs-justify="flex-end">
                    <vs-col vs-type="flex" vs-align="center" vs-w="5">
                        <vs-button v-if="PermisoRevertirInvalidar && selected.Tipo != 'O'" color="primary" @click="IsMotivo=true;TipoInvalidacion='0'">Desvincular Cargo de Liquidación</vs-button>
                        <vs-button v-else color="#B2BABB" class="mr-1" style="display:inline-block">Desvincular Cargo de Liquidación </vs-button>
                    </vs-col>
                    <vs-col vs-type="flex" vs-align="center" vs-w="4">
                        <vs-button v-if="PermisoRevertirInvalidar && selected.Tipo != 'O'" color="danger"  @click="IsMotivo=true;TipoInvalidacion='1'">Eliminar Liquidación</vs-button>
                        <vs-button v-else color="#B2BABB" class="mr-1" style="display:inline-block">Eliminar Liquidación</vs-button>
                    </vs-col>
                    <vs-col vs-type="flex" vs-align="center" vs-w="2">
                        <vs-button color="primary" icon-pack="fa" icon="fa-refresh" @click="LiquidacionDetalle(selected.Correlativo)">Actualizar</vs-button>
                    </vs-col>
                </vs-row>
                <vs-popup classContent="popup-example" title="Detalle de la Reversión" :active.sync="IsMotivo">
                    <vs-row vs-align="center">
                        <vs-col vs-type="flex" vs-align="center" vs-w="12">
                            <label class="typo__label">Motivo:&nbsp;</label>
                            <vs-textarea v-model="Motivo" rows="5" counter="240"/>
                        </vs-col>
                    </vs-row>
                    <br>
                    <vs-row vs-align="center">
                        <vs-col vs-type="flex" vs-align="center" vs-justify="center" vs-w="12">
                            <vs-button color="success" icon-pack="fa" icon="fa-save" @click="InvalidarCargo()">Confirmar</vs-button>
                        </vs-col>
                    </vs-row>
                </vs-popup>
            </vs-popup>
        </vx-card>
    </div>
</template>
<script>
    export default {
        data(){
            return{
                FechaDel: '',
                FechaAl: '',
                DetalleLiquidacion: [{
                    Correlativo: '',
                    Fecha: '',
                    Proveedor: '',
                    Nombre: '',
                    Nit: '',
                    OrdenCompra: '',
                    Factura: '',
                    Status: '',
                    Usuario: '',
                    FechaInicial: '',
                    FechaFinal: '',
                    Comentario: ''
                }],
                selected: {},
                IsDetalle: false,
                selectedProducto: [],
                DetalleProductos: [{
                    Linea: '',
                    Codigo: '',
                    Producto: '',
                    Hospital: '',
                    SerieAdmision: '',
                    Admision: '',
                    Cantidad: '',
                    Tipo_Cargo: '',
                    Cargo: '',
                    Fecha: '',
                    Bodega: ''
                }],
                Motivo: '',
                IsMotivo: false,
                TipoInvalidacion: '',
                PermisoDevincular: false,
                PermisoRevertirInvalidar: false,
                PermisoCambiarProveedor: false,
                IsModificacionProveedor: false,
                ProveedorMod: {
                    Correlativo: '',
                    Proveedor: '',
                    Nombre: '',
                    Fecha: '',
                    Nit: '',
                    Status: '',
                    OrdenCompra: '',
                    Factura: ''
                }
            }
        },
        methods: {
            ConsultaLiquidacionesConsignacion(){
                this.axios.post('/app/v1_OrdenCompra/ConsultaLiquidacionesConsignacion', {
                    FechaInicio: this.FechaDel,
                    FechaFin: this.FechaAl
                })
                .then(resp => {
                    if(resp.data.codigo == '0'){
                        this.DetalleLiquidacion = resp.data.json
                    }else{
                        this.DetalleLiquidacion = []
                    }
                })
            },
            GenerarExcel(){
                this.$reporte_modal({
                    Nombre: "Liquidacion Consignacion Excel",
                    Opciones: {
                        FechaInicial: this.FechaDel,
                        FechaFinal: this.FechaAl
                    },
                    Formato: "EXCEL"
                })
            },
            CambiarProveedor(){
                if(this.ProveedorMod.Proveedor == ''){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'Estimado Usuario seleccione un Proveedor Activo'
                    })
                    return
                }else{
                    this.axios.post('/app/v1_OrdenCompra/ActualizaProveedorConsignacion', {
                        Proveedor: this.ProveedorMod.Proveedor,
                        CodigoLiquidacion: this.ProveedorMod.Correlativo
                    })
                    .then(() => {
                        this.IsModificacionProveedor = false
                    })
                }
                
            },
            VerCorrelativo(data){
                this.$reporte_modal({
                    Nombre: "Liquidacion Detalle",
                    Opciones: {
                        CodigoLiquidacion: data.Correlativo,
                        Proveedor: data.Proveedor,
                        NombreProveedor: data.Nombre,
                        NitProveedor: data.Nit,
                        OrdenCompra: data.OrdenCompra,
                        Factura: data.Factura,
                        Estado: data.Status,
                        FechaLiquidacion: data.Fecha,
                        TipoLiquidacion: data.Tipo
                    },
                    Formato: "PDF"
                })
            },
            VerReporteCostoUltimo(data){
                this.$reporte_modal({
                    Nombre: "Liquidacion Costo Ultimo",
                    Opciones: {
                        CodigoLiquidacion: data.Correlativo,
                        Proveedor: data.Proveedor,
                        NombreProveedor: data.Nombre,
                        NitProveedor: data.Nit,
                        OrdenCompra: data.OrdenCompra,
                        Factura: data.Factura,
                        Estado: data.Status,
                        FechaLiquidacion: data.Fecha

                    },
                    Formato: "PDF"
                })
            },
            VerExcelDetallado(data){
                this.$reporte_modal({
                    Nombre: "Liquidacion Detalle Excel",
                    Opciones: {
                        CodigoLiquidacion: data.Correlativo,
                        NombreProveedor: data.Nombre

                    },
                    Formato: "EXCEL"
                })
            },
            DesvincularOrdenCompra(data){
                if(data.Status == 'Facturado'){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'Desvinculacíon no se puede realizar por estar facturado!!'
                    })
                    return
                }
                if(data.Status == 'Ingresada'){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'No hay Orden de Compra vinculada a esta liquidación!!'
                    })
                    return
                }
                this.axios.post('/app/v1_OrdenCompra/DesvincularOrdenCompra', {
                    CodigoLiquidacion: data.Correlativo
                })
                .then(resp => {
                    if(resp.data.codigo == '0'){
                        this.ConsultaLiquidacionesConsignacion()
                    }
                })
            },
            ConsultaEdicionLiquidaciones(data){
                this.selected.Correlativo = data.Correlativo
                this.selected.Proveedor = data.Proveedor
                this.selected.Nombre = data.Nombre
                this.selected.Fecha = data.Fecha
                this.selected.Status = data.Status
                this.selected.Tipo = data.Tipo

                this.LiquidacionDetalle(data.Correlativo)
            },
            LiquidacionDetalle(correlativo){
                this.axios.post('/app/v1_OrdenCompra/ConsultaLiquidacionDetalle', {
                    CodigoLiquidacion: correlativo
                })
                .then(resp => {
                    if(resp.data.codigo == '0'){
                        this.selectedProducto = resp.data.json
                        this.DetalleProductos = resp.data.json
                    }else{
                        this.selectedProducto = []
                        this.DetalleProductos = []
                    }
                })
            },
            InvalidarCargo(){
                this.IsMotivo = true
                if(this.selected.Status == 'Facturado'){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'Esta liquidación ya se encuentra Facturada, no puede realizar este procedo'
                    })
                    return
                }
                if(this.selected.Status == 'Anulado'){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'Esta liquidación se encuentra anulada, no puede realizar este procedo'
                    })
                    return
                }
                if(this.Motivo == ''){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'Ingrese un motivo válido!!'
                    })
                    return
                }
                if(this.TipoInvalidacion == '0'){
                    this.InvalidarCargoLiquidacion()
                }else{
                    this.RevertirInvalidarLiquidacion()
                }
            },
            InvalidarCargoLiquidacion(){
                this.axios.post('/app/v1_OrdenCompra/InvalidarCargoLiquidacion', {
                    CodigoLiquidacion: this.selected.Correlativo,
                    Motivo: this.Motivo,
                    Datos: JSON.stringify(this.selectedProducto)
                })
                .then(resp =>{
                    if(resp.data.codigo == '0'){
                        this.LiquidacionDetalle(this.selected.Correlativo)
                        this.IsMotivo = false
                        this.Motivo = ''
                    }
                })
            },
            RevertirInvalidarLiquidacion(){
                this.axios.post('/app/v1_OrdenCompra/InvalidarCargoLiquidacion', {
                    CodigoLiquidacion: this.selected.Correlativo,
                    Motivo: this.Motivo,
                    Datos: JSON.stringify(this.DetalleProductos)
                })
                .then(resp =>{
                    if(resp.data.codigo == '0'){
                        this.LiquidacionDetalle(this.selected.Correlativo)
                        this.IsMotivo = false
                        this.Motivo = ''
                    }
                })
            },
            Proveedores(){
                this.$refs.BuscarProveedorConsignacion.iniciar((data) =>{
                    if(data != null){
                        this.ProveedorMod.Proveedor = data.Proveedor
                        this.ProveedorMod.Nombre = data.Nombre
                    }
                })
            },
            getStyle(item1){
                return item1 == 'Anulado'?'background-color: #E74C3C; opacity: 0.7; color: white;':
                        item1 == 'Orden de Compra'? 'color: red;' :
                        item1 == 'Facturado' ? 'background-color: #27AE60; opacity: 0.7; color: white;' : 'background-color: white;'
            }
        },
        mounted() {
            this.PermisoDevincular = this.$validar_privilegio('DESVINCULAR').status
            this.PermisoRevertirInvalidar = this.$validar_privilegio('REVERTIR/INVALIDAR').status
            this.PermisoCambiarProveedor = this.$validar_privilegio('MODIFICAR_PROVEEDOR').status
            this.DetalleLiquidacion = []
        },
    }
</script>
<style scoped>
    .almacenador{
        border: 1px solid #888;

    }
    .div-container{
        padding: 5px;
        margin: 10px;
    }
    .div-table{
        border: 1px solid #888;
    }
</style>