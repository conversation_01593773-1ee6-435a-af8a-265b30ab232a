<template>
    <div>
        <vx-card title="Aceptación de paquete">
            <div class="flex flex-wrap mb-1">
                <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6  p-1" v-if="permisos.Crear">
                    <vs-button class="w-full mr-1 mt-5" title="Crear envío" color="success"
                        @click="Otros().AbrirVentana(1, null, true)">
                        Crear
                    </vs-button>
                </div>
                <div class="w-full md:w-1/1 lg:w-1/1 xl:w-1/1 p-1" v-if="permisos.Visualizar">
                    <vs-table2 :data="listaAceptaciones" max-items="15" pagination search>
                        <template slot="thead">
                            <th order="IdAceptacion">ID</th>
                            <th order="FechaAceptacion">Fecha Aceptación</th>
                            <th order="HoraAceptacion">Hora Aceptación</th>
                            <th order="NombreAcepta">Nombre Acepta</th>
                            <th order="HospitalAcepta">Hospital Acepta</th>
                            <th order="CantidadPaquetes">Cantidad Paquetes</th>
                            <th></th>
                        </template>
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2>
                                    {{ tr.IdAceptacion }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.FechaAceptacion }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.HoraAceptacion }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.NombreAcepta }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.HospitalAcepta }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.CantidadPaquetes }}
                                </vs-td2>
                                <vs-td2>
                                    <vs-button color="warning" size="small" icon-pack="fas" icon="fa-info-circle"
                                        class="mr-1" style="display:inline-block" title="Información"
                                        @click="Otros().AbrirVentana(3, tr, true)">
                                    </vs-button>
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
                </div>
            </div>
        </vx-card>
        <vs-popup :title="ventana.titulo" v-show="ventana.mostrar" :active.sync="ventana.mostrar">
            <div v-if="ventana.opcion == 1">
                <ValidationObserver ref="formValidate" v-slot="{ }" mode="lazy">
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <vs-input label="Hospital Acepta" class="w-full" v-model="sesion.sesion_sucursal_nombre"
                                disabled="true" />
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <vs-input label="Corporativo Acepta" class="w-full" v-model="sesion.corporativo"
                                disabled="true" />
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <vs-input label="Nombre Acepta" class="w-full" v-model="sesion.usuario" disabled="true" />
                        </div>
                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2  p-1">
                            <ValidationProvider name="Codigo">
                                <SM-Buscar label="Paquete" api="app/mensajeria/BusquedaPaquete"
                                    :api_filtro="{ Estado: 1, CodigoHospitalEnvia: sesion.sesion_sucursal }"
                                    :api_campos="['IdPaquete', 'FechaEnvia', 'HoraEnvia', 'CorporativoEnvia', 'NombresEnvia', 'ApellidosEnvia', 'AreaEnvia', 'HospitalEntrega', 'CorporativoEntrega', 'NombresEntrega', 'ApellidosEntrega', 'AreaEntrega']"
                                    :api_titulos="['IdPaquete', 'FechaEnvia', '#HoraEnvia', 'CorporativoEnvia', 'NombresEnvia', 'ApellidosEnvia', '#AreaEnvia', 'HospitalEntrega', 'CorporativoEntrega', 'NombresEntrega', 'ApellidosEntrega', '#AreaEntrega']"
                                    api_campo_respuesta="IdPaquete" :callback_buscar="Consulta().ConsultaPaquete"
                                    :api_preload="true" :api_multiselect="true" :disabled_search_input="true" />
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class="w-full md:w-1/1 lg:w-1/1 xl:w-1/1 p-1">
                        <vs-table2 max-items="5" pagination :data="detallesAceptacion" search>
                            <template slot="thead">
                                <th order="IdPaquete">ID</th>
                                <th order="FechaEnvia">Fecha Envia</th>
                                <th order="HoraEnvia">Hora Envia</th>
                                <th order="NombreEnvia">Nombre Envia</th>
                                <th order="NombreEntrega">Nombre Entrega</th>
                                <th></th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2>
                                        {{ tr.IdPaquete }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.FechaEnvia }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.HoraEnvia }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.NombreEnvia }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.NombreEntrega }}
                                    </vs-td2>
                                    <vs-td2>
                                        <vs-button color="warning" size="small" icon-pack="fas" icon="fa-info-circle"
                                            class="mr-1" style="display:inline-block"
                                            @click="Otros().AbrirVentana(2, tr, true)">
                                        </vs-button>
                                        <vs-button color="danger" size="small" icon-pack="fas" icon="fa-trash" class="mr-1"
                                            style="display:inline-block"
                                            @click="Eliminar().EliminarElemento(tr.correlativoTabla)">
                                        </vs-button>
                                        <vs-button size="small" icon-pack="fas" icon="fa-envelope" class="mr-1" title="Guia"
                                            color="success" style=" display:inline-block"
                                            @click="Consulta().ReporteGuia(tr)">
                                        </vs-button>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full  p-1">
                            <ValidationProvider name="Comentario" v-slot="{ errors }">
                                <label style="font-size:12px">Comentario</label>
                                <vs-textarea v-model="info.Comentario" counter="150" :counter-danger.sync="counterDanger"
                                    class="w-full" rows="2" :danger="errors.length > 0"
                                    :danger-text="(errors.length > 0) ? errors[0] : null" required />
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                            <vs-button class="w-full mr-1 mt-5" icon-pack="fas" icon="fa-plus" color="success"
                                @click="Agregar().CrearAsignacion()">
                                Aceptar Paquete
                            </vs-button>
                        </div>
                    </div>
                </ValidationObserver>
            </div>
            <div v-if="ventana.opcion == 2">
                <div class="flex flex-wrap mb-1">
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Corporativo envia: {{ this.infoDetalle.CorporativoEnvia }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Nombre envia: {{ this.infoDetalle.NombreEnvia }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Hospital envia: {{ this.infoDetalle.HospitalEnvia }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Fecha envia: {{ this.infoDetalle.FechaEnvia }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Hora envia: {{ this.infoDetalle.HoraEnvia }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Area envia: {{ this.infoDetalle.AreaEnvia }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Corporativo entrega: {{ this.infoDetalle.CorporativoEntrega }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Nombre entrega: {{ this.infoDetalle.NombreEntrega }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Hospital entrega: {{ this.infoDetalle.HospitalEntrega }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Fecha entrega: {{ this.infoDetalle.FechaEntrega }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Hora entrega: {{ this.infoDetalle.HoraEntrega }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Area entrega: {{ this.infoDetalle.AreaEntrega }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Tipo Paquete: {{ this.infoDetalle.TipoPaquete }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1" v-if="infoDetalle.DescripcionOtroTipo !== ''">
                        <label style="font-size:12px">Descripción tipo paquete: {{ this.infoDetalle.DescripcionOtroTipo
                        }}</label>
                    </div>
                    <div class="w-full md:w-1/1 lg:w-1/1 xl:w-1/1 p-1">
                        <vs-table2 max-items="5" pagination :data="detallesPaqueteEnvio">
                            <template slot="thead">
                                <th order="Cantidad">Cantidad</th>
                                <th order="Descripcion">Descripción</th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2>
                                        {{ tr.Cantidad }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.Descripcion }}
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                    <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6  p-1">
                        <vs-button class="w-full mr-1 mt-5" title="Crear envío" color="danger"
                            @click="Otros().AbrirVentana(ventana.opcionRegresar)">
                            Regresar
                        </vs-button>
                    </div>
                </div>
            </div>
            <div v-if="ventana.opcion == 3">
                <div class="flex flex-wrap mb-1">
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">ID: {{ this.infoAceptacion.IdAceptacion }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Fecha Aceptación: {{ this.infoAceptacion.FechaAceptacion }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Hora Aceptación: {{ this.infoAceptacion.HoraAceptacion }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Nombre Acepta: {{ this.infoAceptacion.NombreAcepta }}</label>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <label style="font-size:12px">Hospital Acepta: {{ this.infoAceptacion.HospitalAcepta }}</label>
                    </div>
                    <div class="w-full md:w-1/1 lg:w-1/1 xl:w-1/1 p-1">
                        <vs-table2 max-items="5" pagination :data="detallesAceptacion" search>
                            <template slot="thead">
                                <th order="IdPaquete">ID</th>
                                <th order="FechaEnvia">Fecha Envia</th>
                                <th order="HoraEnvia">Hora Envia</th>
                                <th order="NombreEnvia">Nombre Envia</th>
                                <th order="NombreEntrega">Nombre Entrega</th>
                                <th></th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2>
                                        {{ tr.IdPaquete }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.FechaEnvia }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.HoraEnvia }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.NombreEnvia }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.NombreEntrega }}
                                    </vs-td2>
                                    <vs-td2>
                                        <vs-button color="warning" size="small" icon-pack="fas" icon="fa-info-circle"
                                            class="mr-1" style="display:inline-block"
                                            @click="Otros().AbrirVentana(2, tr, true)">
                                        </vs-button>
                                        <vs-button size="small" icon-pack="fas" icon="fa-envelope" class="mr-1" title="Guia"
                                            color="success" style=" display:inline-block"
                                            v-if="tr.Estado == 1 || tr.Estado == 2" @click="Consulta().ReporteGuia(tr)">
                                        </vs-button>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                </div>
                <div class="flex flex-wrap mb-1">
                    <div class="w-full  p-1">
                        <ValidationProvider name="Comentario" v-slot="{ errors }">
                            <label style="font-size:12px">Comentario</label>
                            <vs-textarea v-model="infoAceptacion.Comentario" counter="150"
                                :counter-danger.sync="counterDanger" class="w-full" rows="2" :danger="errors.length > 0"
                                :danger-text="(errors.length > 0) ? errors[0] : null" required disabled="true" />
                        </ValidationProvider>
                    </div>
                </div>
            </div>
        </vs-popup>
    </div>
</template>

<script>

export default {
    data() {
        return {
            /**
             * Documentación del módulo
             */
            doc_: [
                'Pantalla de Ingreso de asignacion',
            ],
            ventana: {
                titulo: '',
                mostrar: false,
                opcion: 0,
                opcionRegresar: 0
            },
            info: {
                Aceptaciones: [],
                Comentario: null
            },
            detallesAceptacion: [],
            infoDetalle: {},
            correlativoTabla: 0,
            listaMensajeros: [],
            selectResponsable: { Codigo: null, Descripcion: null },
            counterDanger: false,
            listaAceptaciones: [],
            infoAceptacion: {},
            detallesPaqueteEnvio: [],
            permisos: {
                Visualizar: null,
                Crear: null
            }
        }
    },
    components: {
    },
    computed: {
        sesion() {
            return this.$store.state.sesion
        }
    },
    methods: {
        Consulta() {
            return {
                init: () => {
                    this.axios.post('/app/mensajeria/ListarAceptaciones', {})
                        .then(resp => {
                            this.listaAceptaciones = resp.data.json;
                        })
                },
                ConsultaPaquete: (data) => {
                    data.forEach((element) => {
                        element.correlativoTabla = this.correlativoTabla
                        this.detallesAceptacion.push(element)
                        this.correlativoTabla = this.correlativoTabla + 1
                    })
                },
                DetallesPaquete: (id) => {
                    this.detallesPaqueteEnvio = []
                    this.axios.post('/app/mensajeria/DetallesPaquete', { IdPaquete: id })
                        .then(resp => {
                            this.detallesPaqueteEnvio = resp.data.json
                        })
                },
                detallesAceptacion: (id) => {
                    return new Promise(() => {
                        this.axios.post('/app/mensajeria/PaquetesAceptados', { IdAceptacion: id })
                            .then(resp => {
                                this.detallesAceptacion = resp.data.json;
                            })
                    });

                },
                ReporteGuia: (objeto) => {
                    this.$reporte_modal(
                        {
                            Nombre: "Guia Paquete",
                            Opciones: {
                                nombrereporte: "guiapaquete",
                                IdPaquete: objeto.IdPaquete,
                                Estado: objeto.Estado
                            }
                        })
                }
            }
        },
        Agregar() {
            return {
                CrearAsignacion: () => {
                    if (this.detallesAceptacion.length == 0) {
                        this.$vs.notify({
                            title: "Error al crear aceptación",
                            text: "Debe asignar al menos 1 paquete",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                    } else if (this.counterDanger) {
                        this.$vs.notify({
                            title: "Error al crear aceptación",
                            text: "Comentario debe tener menos de 150 caracteres.",
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                    } else {
                        const arrayTemp = []

                        this.detallesAceptacion.forEach(element => arrayTemp.push(element.IdPaquete))
                        this.info.Aceptaciones = arrayTemp.join('|')

                        this.axios.post('/app/mensajeria/IngresarAceptacion', this.info)
                            .then(resp => {
                                if (resp.data.codigo == 0) {
                                    this.Consulta().init()
                                    this.Otros().LimpiarObjeto()
                                    this.ventana.mostrar = false
                                }
                            })
                            .catch(() => {

                            })
                    }
                }
            }
        },
        Eliminar() {
            return {
                EliminarElemento: (correlativoTabla) => {
                    this.detallesAceptacion = this.detallesAceptacion.filter((item) => item.correlativoTabla !== correlativoTabla)
                }
            }
        },
        Otros() {
            return {
                AbrirVentana: (opcion, objeto = null, asignarObjeto = false) => {
                    this.ventana.opcion = opcion

                    if (opcion == 1) {
                        this.ventana.mostrar = true
                        this.ventana.titulo = 'Crear aceptación'
                        this.ventana.opcionRegresar = 1
                        if (asignarObjeto) {
                            this.Otros().LimpiarObjeto()
                        }

                    } else if (opcion == 2) {
                        this.ventana.titulo = 'Detalles Paquete'
                        this.infoDetalle = objeto
                        this.Consulta().DetallesPaquete(this.infoDetalle.IdPaquete)
                    } else if (opcion == 3) {
                        this.ventana.mostrar = true
                        if (asignarObjeto) {
                            this.infoAceptacion = objeto
                        }
                        this.ventana.opcionRegresar = 3
                        this.ventana.titulo = 'Detalles aceptación #' + this.infoAceptacion.IdAceptacion
                        this.Consulta().detallesAceptacion(this.infoAceptacion.IdAceptacion)
                    }
                },
                LimpiarObjeto: () => {
                    this.info = {
                        Aceptaciones: [],
                        Comentario: null
                    }

                    this.detallesAceptacion = []
                }

            }
        }
    },
    mounted() {
        this.Consulta().init()

        this.permisos.Visualizar = this.$validar_privilegio('VISUALIZAR').status
        this.permisos.Crear = this.$validar_privilegio('CREAR').status
    }
}
</script>