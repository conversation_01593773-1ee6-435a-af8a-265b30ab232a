<!-- =========================================================================================
    File Name: TheFooter.vue
    Description: Footer component
    Component Name: TheFooter
    ----------------------------------------------------------------------------------------
    Item Name: Vuexy - Vuejs, HTML & Laravel Admin Dashboard Template
      Author: Pixinvent
    Author URL: http://www.themeforest.net/user/pixinvent
========================================================================================== -->


<template functional>
    <footer class="the-footer flex-wrap justify-between"   :class="classes">
        <p>
          <span>Hospitales la Paz &copy;</span>
          <span>{{ new Date().getFullYear() }} </span>
          <span class="hidden sm:inline-block">, Todos los Derechos Reservados </span>          
        </p>
        <span class="font-size:100px">v5.87.250714</span>        
    </footer>
</template>

<script>
export default {
    name: "the-footer",
    props: {
        classes: {
            type: String,
        }
    }
}
</script>
