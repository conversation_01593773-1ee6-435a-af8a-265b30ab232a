<template>
<div class="reportes-bancos">
    <DxDrawer template="listMenu" :close-on-outside-click="false" :opened="true">
        <template #listMenu>
            <div style="width: 300px">
                <DxList :data-source="reportes" @item-click="clickOnItem">
                    <template #item={data:item}>
                        <div :class="item.id === SelectedOption ? ['itemSelected', 'dx-template-wrapper', 'dx-item-content', 'dx-list-item-content'] : ['itemNoSelected', 'dx-template-wrapper', 'dx-item-content', 'dx-list-item-content']">
                            <div style="display: flex !important; place-items: center !important">
                                <font-awesome-icon v-if="item.id != 5" :icon="['fas', item.icon]" style="font-size: 20px !important;" />
                                <i v-if="item.id == 5" class="dx-icon-formula" style="font-size: 20px !important;"></i>
                                <span class="ml-2" style="font-size: 14px !important;">{{item.nombre}}</span>
                            </div>
                        </div>
                    </template>
                </DxList>
            </div>
        </template>
        <div id="content" class="ml-2 borderDiv" width="100%">
            <form @submit="handleSubmit" class="m-4">
                <DxForm :form-data.sync="formulario" label-mode="floating" :show-validation-summary="true">
                    <DxFormGroupItem>
                        <DxFormGroupItem :col-count="![2, 3, 4].includes(SelectedOption) ? 2 : 1">
                            <DxFormItem data-field="Cuenta" editor-type="dxSelectBox" :validationRules="[{ type: 'required', message: 'Debe seleccionar una cuenta' }]" :editor-options="{ isRequired: true, width: 'auto', searchEnabled: true, items: cuentas,  displayExpr: 'Valor', valueExpr: 'Codigo', onValueChanged: CargarConciliaciones }" />

                            <DxFormItem :visible="SelectedOption == 1" :validationRules="[{ type: 'required', message: 'Debe indicar el periodo del cheque' }]" data-field="Periodo" editor-type="dxSelectBox" :editor-options="{ width: 'auto', searchEnabled: true, items: periodos,  displayExpr: 'Valor', valueExpr: 'Codigo' }" />
                            <DxFormItem :visible="[5, 6, 7].includes(SelectedOption)" :validationRules="[{ type: 'required', message: 'Debe seleccionar la fecha de la conciliación' }]" data-field="Conciliacion" editor-type="dxSelectBox" :editor-options="{ width: 'auto', searchEnabled: true, items: conciliaciones,  displayExpr: 'Fecha', valueExpr: 'Valor' }">
                                <DxFormLabel text="Al" />
                            </DxFormItem>
                        </DxFormGroupItem>

                        <DxFormGroupItem :col-count="2" :visible="![1, 5, 6, 7].includes(SelectedOption)">
                            <DxFormItem :col-span="4" :validationRules="[{ type: 'required', message: 'Debe indicar la fecha inicial del rango de la consulta' }]" data-field="FechaInicial" editor-type="dxDateBox" :editor-options="editorOptionsFecha">
                                <DxFormLabel text="Del" />
                            </DxFormItem>
                            <DxFormItem :col-span="4" :validationRules="[{ type: 'required', message: 'Debe indicar la fecha final del rango de la consulta' }]" data-field="FechaFinal" editor-type="dxDateBox" :editor-options="editorOptionsFecha">
                                <DxFormLabel text="Al" />
                            </DxFormItem>
                        </DxFormGroupItem>

                        <DxFormGroupItem :col-count="2">

                        </DxFormGroupItem>

                        <DxFormGroupItem :col-count="2" :visible="SelectedOption == 2">
                            <DxFormItem data-field="Beneficiario" editor-type="dxTextBox" />
                            <DxFormItem data-field="Usuario" editor-type="dxTextBox" />
                        </DxFormGroupItem>

                        <DxFormGroupItem :col-count="2" :visible="[1, 2, 6].includes(SelectedOption)" cssClass="centered-items">
                            <DxFormEmptyItem :visible="SelectedOption == 6" />
                            <DxFormItem :visible="SelectedOption < 3" data-field="SinConcepto" editor-type="dxCheckBox">
                                <DxFormLabel text="Sin concepto" />
                            </DxFormItem>
                            <DxFormItem :visible="[2, 6].includes(SelectedOption)" data-field="Excel" editor-type="dxCheckBox">
                                <DxFormLabel text="Exportar a Excel" />
                            </DxFormItem>
                        </DxFormGroupItem>
                    </DxFormGroupItem>
                    <DxFormButtonItem :button-options="buttonGenerar" name="Generar" horizontal-alignment="center" verical-alignment="center" />
                </DxForm>
            </form>
        </div>
    </DxDrawer>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'

import * as XLSX from 'xlsx';

export default {
    name: 'Impresion',
    components: {},
    data() {
        return {
            DefaultDxGridConfiguration,

            formulario: {
                Reporte: 1,
                SinConcepto: false,
                Usuario: null,
                Beneficiario: null,
                Excel: false
            },

            cuentas: [],
            periodos: [],
            conciliaciones: [],

            editorOptionsFecha: {
                dataType: 'date',
                dateSerializationFormat: 'yyyy-MM-dd HH:mm:ss',
                displayFormat: 'dd/MM/yyyy',
                max: new Date()
            },

            buttonGenerar: {
                width: 'auto',
                icon: 'fas fa-print',
                text: 'Generar',
                type: 'success',
                onClick: () => {
                    // this.DescargarPDF()
                },
                useSubmitBehavior: true,
            },

            infoReporte: [],
            infoCuenta: [],

            reportes: [{
                id: 1,
                nombre: '1. Movimiento por Cuenta',
                icon: 'money-bill-transfer',
                reporte: 'MovimientoPorCuenta'
            }, {
                id: 2,
                nombre: '2. Lista de Cheques Emitidos',
                icon: 'list-check',
                reporte: 'ListaChequesEmitidos',
                excel: 'Lista de Cheques',
                encabezado: ['Fecha', 'Numero', 'NombreBeneficiario', 'Status', 'Entregado', 'Monto', 'Observaciones']
            }, {
                id: 3,
                nombre: '3. Cheques Pendientes de Entrega',
                icon: 'hourglass-half',
                reporte: 'ChequesPendientesEntrega'
            }, {
                id: 4,
                nombre: '4. Cheques Entregados',
                icon: 'calendar-check',
                reporte: 'ChequesEntregados'
            }, {
                id: 5,
                nombre: '5. Conciliación Cuadrática',
                icon: '',
                reporte: 'ConciliacionCuadratica'
            }, {
                id: 6,
                nombre: '6. Cheques/Depósitos en Circulación',
                icon: 'repeat',
                reporte: 'ChequesEnCirculacion',
                excel: 'Cheques en Circulación',
                encabezado: ['Fecha', 'Numero', 'Monto', 'Status', 'NombreBeneficiario', 'Tipo', 'Observaciones']
            }, {
                id: 7,
                nombre: '7. Cheques Vencidos',
                icon: 'calendar-xmark',
                reporte: 'ChequesEnCirculacion' // Se utiliza el mismo que en el reporte anterior ya que muestra los mismos campos, solo cambia el estatus del cheque
            }, ],

            SelectedOption: 1,
        }
    },
    props: {},
    methods: {
        handleSubmit(e) {
            e.preventDefault() 
            this.DescargarPDF()
        },
        clickOnItem(e) {
            this.SelectedOption = e.itemData.id
        },

        async CargarCuentas() {
            await this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 1
                })
                .then(resp => {
                    this.cuentas = resp.data.json

                    this.formulario.Cuenta = this.cuentas[0].Codigo
                })
        },

        async CargarPeriodos() {
            await this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 3,
                })
                .then(resp => {
                    this.periodos = resp.data.json

                    let hoy = new Date().setHours(0, 0, 0, 0)

                    for (let i = 0; i < this.periodos.length - 1; i++) {
                        let fin = new Date(this.periodos[i].FechaFinal).setHours(0, 0, 0, 0)
                        let inicio = new Date(this.periodos[i].FechaInicial).setHours(0, 0, 0, 0)

                        if (hoy >= inicio && hoy <= fin) {
                            this.formulario.Periodo = this.periodos[i].Codigo
                        }
                    }

                })
        },

        async CargarConciliaciones() {
            this.InformacionCuenta()
            this.formulario.Conciliacion = null
            this.conciliaciones = []
            await this.axios.post("/app/v1_bancos/DatosConsultaTransacciones", {
                    Opcion: 4,
                    SubOpcion: 1,
                    Cuenta: this.formulario.Cuenta,
                })
                .then((resp) => {
                    if (resp.data.json.length > 0) {
                        this.conciliaciones = resp.data.json.map((x) => {
                            return {
                                Fecha: this.$formato_fecha(x.Fecha, 'dd/MM/yyyy'),
                                Valor: x.FechaFormateada
                            }
                        })

                        this.formulario.Conciliacion = this.conciliaciones[0].Valor
                    } else {
                        if (this.SelectedOption > 4) {
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#ed8c72',
                                title: 'No existen conciliaciones',
                                acceptText: 'Aceptar',
                                text: 'La cuenta no seleccionada no cuenta con fechas de conciliación.',
                                buttonCancel: 'border',
                                accept: () => {
                                    return true
                                },
                            })
                            return
                        }
                    }
                })
        },

        async DescargarPDF() {
            let valido = true
            if (this.SelectedOption == 1) {
                let periodo = await this.ValidarSaldoFinal()

                if (periodo) {
                    valido = false
                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'Periodo activo',
                        acceptText: 'Aceptar',
                        text: 'Debe cerrar el periodo anterior y abrir la actividad para este periodo.',
                        buttonCancel: 'border',
                        accept: () => {},
                    })
                    return
                }
            }

            if (valido) {
                this.$reporte_modal({
                    Nombre: this.reportes.find(r => r.id == this.SelectedOption).reporte,
                    MostrarReporte: true,
                    Opciones: {
                        Opcion: this.SelectedOption,
                        Cuenta: this.formulario.Cuenta,
                        Periodo: this.formulario.Periodo,
                        SinConcepto: this.formulario.SinConcepto,
                        FechaInicial: this.formulario.FechaInicial,
                        FechaFinal: this.formulario.FechaFinal,
                        Beneficiario: this.formulario.Beneficiario,
                        Usuario: this.formulario.Usuario,
                        Fecha: this.formulario.Conciliacion
                    }
                })
            }

            let reportesExcel = [2, 6]

            if (this.formulario.Excel && reportesExcel.includes(this.SelectedOption)) {
                await this.InformacionReporte()
                this.ExportarAExcel(this.infoReporte, this.reportes.find(r => r.id == this.SelectedOption), this.infoCuenta, this.formulario.Beneficiario, this.formulario.Usuario)
            }
        },

        async InformacionReporte() {
            await this.axios.post("/app/v1_bancos/Reportes", {
                    Opcion: this.SelectedOption,
                    Cuenta: this.formulario.Cuenta,
                    Periodo: this.formulario.Periodo,
                    SinConcepto: this.formulario.SinConcepto,
                    FechaInicial: this.formulario.FechaInicial,
                    FechaFinal: this.formulario.FechaFinal,
                    Beneficiario: this.formulario.Beneficiario,
                    Usuario: this.formulario.Usuario,
                    Fecha: this.formulario.Conciliacion
                })
                .then((resp) => {
                    if (resp.data.json.length > 0) {
                        this.infoReporte = resp.data.json
                    }
                })
        },

        async InformacionCuenta() {
            await this.axios.post("/app/v1_bancos/Cuentas", {
                    Opcion: 8,
                    Codigo: this.formulario.Cuenta
                })
                .then((resp) => {
                    if (resp.data.json.length > 0) {
                        this.infoCuenta = resp.data.json[0]
                    }
                })
        },

        async ValidarSaldoFinal() {
            return await this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 2,
                    Cuenta: this.formulario.Cuenta,
                    Periodo: this.formulario.Periodo
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        return false
                    } else {
                        return true
                    }
                })
        },

        ExportarAExcel(data, reporte, cuenta, beneficiario, usuario) {
            const ws_data = []
            let encabezadoTabla = reporte.encabezado;
            let encabezadoBeneficiario = 'Beneficiario parecido a ' + beneficiario
            let encabezadoCuenta = 'Cuenta ' + cuenta.Cuenta + ' - ' + cuenta.Nombre
            let encabezado = ''

            if (beneficiario !== null && beneficiario !== undefined && beneficiario !== '') {
                encabezado = encabezadoBeneficiario
            } else {
                encabezado = encabezadoCuenta
            }

            if (usuario !== null && usuario !== undefined && usuario !== '') {
                encabezado = encabezado + ', Usuario ' + usuario
            }
            ws_data.push(['GRUPO HOSPITALARIO DE GUATEMALA']);
            ws_data.push([reporte.excel]);
            ws_data.push([encabezado]);
            ws_data.push([]);

            ws_data.push(encabezadoTabla);

            // Variable para calcular el ancho máximo de cada columna
            const colWidths = new Array(encabezadoTabla.length).fill(0);

            // Agregar los registros de la consulta (similar a cómo se iteraría sobre QueryObj)
            for (let i = 0; i < data.length; i++) {
                let row = []
                for (let j = 0; j < encabezadoTabla.length; j++) {
                    if (encabezadoTabla[j] == 'Fecha') {
                        row.push(this.formatDate(data[i][encabezadoTabla[j]]))
                    } else if (encabezadoTabla[j] == 'Entregado') {
                        row.push(this.formatDatetime(data[i][encabezadoTabla[j]]))
                    } else if (encabezadoTabla[j] == 'Monto') {
                        let monto = 'Q. ' + data[i][encabezadoTabla[j]]
                        row.push(monto)
                    } else {
                        row.push(data[i][encabezadoTabla[j]])
                    }
                }

                ws_data.push(row)

                row.forEach((cell, index) => {
                    colWidths[index] = Math.max(colWidths[index], cell ? cell.toString().length : 0);
                });
            }

            // Crear una hoja de trabajo a partir de los datos
            const ws = XLSX.utils.aoa_to_sheet(ws_data);

            // Asignar el ancho de las columnas (ajustar al contenido)
            ws['!cols'] = colWidths.map(width => ({
                wch: width + 2
            })); // Agregar un margen extra de 2

            // Crear un libro de trabajo con la hoja
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Reporte');

            // Definir el nombre del archivo
            let fileName = reporte.reporte + '.xlsx';

            // Guardar el archivo Excel en el navegador
            XLSX.writeFile(wb, fileName);

            // Mostrar mensaje al usuario
            // this.$message({
            //     message: `Archivo exportado correctamente: ${fileName}`,
            //     type: 'success'
            // });
        },

        formatDate(date) {
            if (date !== null && date !== undefined) {
                date = new Date(date)
                const day = String(date.getDate()).padStart(2, '0'); // Día con 2 dígitos
                const month = String(date.getMonth() + 1).padStart(2, '0'); // Mes con 2 dígitos (recuerda que los meses comienzan en 0)
                const year = date.getFullYear(); // Año

                return `${day}/${month}/${year}`;
            }
            return ''
        },

        formatDatetime(date) {
            if (date !== null && date !== undefined) {
                date = new Date(date)
                const day = String(date.getDate()).padStart(2, '0'); // Día con 2 dígitos
                const month = String(date.getMonth() + 1).padStart(2, '0'); // Mes con 2 dígitos (recuerda que los meses comienzan en 0)
                const year = date.getFullYear(); // Año
                const hours = String(date.getHours()).padStart(2, '0'); // Hora con 2 dígitos
                const minutes = String(date.getMinutes()).padStart(2, '0'); // Minutos con 2 dígitos

                return `${day}/${month}/${year} ${hours}:${minutes}`;
            }
            return ''
        }
    },
    created() {},
    mounted() {},
    beforeMount() {
        this.CargarCuentas()
        this.CargarPeriodos()
        this.CargarConciliaciones()
    },
    watch: {},
    computed: {}
}
</script>

<style>
.reportes-bancos .dx-item.dx-radiobutton {
    display: flex;
    border-color: #706969 !important;
    border-style: dotted !important;
    border-width: 1px !important;
    width: auto !important;
    place-content: left;
    padding: 5px !important;
    margin-bottom: 5px !important;
}

.reportes-bancos .dx-item.dx-item-selected.dx-radiobutton-checked.dx-radiobutton {
    background-color: salmon !important;
}

.reportes-bancos .dx-invalid .dx-radiobutton-icon::before {
    border-color: red !important
}

.centered-items .dx-item-content .dx-box-item-content {
    display: grid !important;
    place-content: center !important;
}

.centered-item .dx-item-content.dx-box-item-content {
    display: grid !important;
    place-content: center !important;
}

.itemSelected {
    color: white;
    background-color: #337ab7;
}

.itemNoSelected {
    color: black;
    background-color: transparent;
}

.borderDiv {
    border-style: solid;
    border-color: blueviolet;
}
</style>
