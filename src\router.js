import Vue from 'vue'
import Router from 'vue-router'
import store from './store/store'
import { ListoToTree } from './components/sermesa/funciones/Tree'

Vue.use(Router)

var router = new Router({
    mode: 'history',
    base: process.env.BASE_URL,
    scrollBehavior() {
        return {
            x: 0,
            y: 0
        }
    },
    routes: [

        {
            // =============================================================================
            // MAIN LAYOUT ROUTES
            // =============================================================================
            path: '',
            name: 'main',
            component: () => import('./layouts/main/Main.vue'),
            children: [
                // =============================================================================
                // Theme Routes
                // NO CAMBIAR
                // TODAS LAS RUTAS SERAN GENERADAS POR LOS PRIVILEGIOS QUE POSEE EL USUARIO.
                // =============================================================================
                {
                    path: '/',
                    name: 'home',
                    component: () => import('./views/Home.vue')
                },
            ],
        },
        // =============================================================================
        // FULL PAGE LAYOUTS
        // =============================================================================
        {
            path: '',
            component: () => import('@/layouts/full-page/FullPage.vue'),
            children: [
                // =============================================================================
                // PAGES
                // =============================================================================
                {
                    path: '/pages/login',
                    name: 'page-login',
                    component: () => import('@/views/pages/Login.vue')
                },
                {
                    path: '/pages/error-404',
                    name: 'page-error-404',
                    component: () => import('@/views/pages/Error404.vue')
                },

            ]
        },
        // Redirect to 404 page, if no match found
        {
            path: '*',
            redirect: '/pages/error-404'
        }
    ],
})

//================================================================================
// AGREGANDO RUTAS / Walter
// tambien carga rutas en Main.vue al ingresar por sesión
// NOTA: esta carga de rutas es para manejar el escenario en el que se pega directamente una URL en el navegador, lo que provoca que se vuelva a crear el router
// Modificacies:
//      Fecha       Autor               Descripción
//      2023-10-02  Esteban Palacios    Cambio en la estructura para manejar los submenus (recursiva)
//================================================================================
/**
 * Cosntruye las subrutas y agregas las rutas al ROUTER en base a las funcionalidades almacenadas en el localstorage
 * Permite acceder al recurso al pegar directamente la ruta en el navegador
 */
export const buildRoutes = function() {
    let _router = router.options.routes[0];// Obteniendo la ruta Raíz " / "
    let _currentRoutes = router.getRoutes();
    let funcionalidades = (localStorage.getItem("sermesa_sesion_funcionalidad")) ? JSON.parse(atob(localStorage.getItem("sermesa_sesion_funcionalidad"))) : []
    ////////////////
    let arr = []
    //Quitando repetidos y generado la estructura
    funcionalidades.map(data => {
        if (data.IdFuncionalidad && arr.filter(data_ => data_.IdFuncionalidad == data.IdFuncionalidad).length == 0)
            arr.push({...data})
    })
    funcionalidades = arr
    ////////////////
    //_router.children.pop()//elementos anidados 'nested routes' del Main.vue
    _router.children = []

    //anidar children subrutas
    funcionalidades = ListoToTree(funcionalidades, 'IdFuncionalidad', 'IdFunPadre', 'children', false, ( item ) => {
        item.props = {IdFuncionalidad: item.IdFuncionalidad}
        item.component = () => import('@/views/pages' + item.Url.toUpperCase())
    })
    //Agregar las rutas si no existen en el router
    funcionalidades.map( x=> {
        let myRoute = {
            path: x.path || x.Url.toUpperCase(),
            name: x.Funcionalidad,
            props: {
                IdFuncionalidad: x.IdFuncionalidad
            },
            component: () => import('@/views/pages' + x.Url.toUpperCase()),
            children: x.children,
        }
        if( !_currentRoutes.find(r => r.path == x.Url.toUpperCase()) )
            router.addRoute('main', myRoute)
        _router.children.push(myRoute)
    })
}
buildRoutes()
router.beforeEach((resp, from, next) => {
    //================================================================================
    // VERIFICANDO LAS RUTAS
    //================================================================================
    if (store.state.sesion.corporativo != 0) {
        Vue.axios.post('/app/usuario/obtener_privilegios', {
            Modulo: (resp.matched[resp.matched.length-1].props.default) ? resp.matched[resp.matched.length-1].props.default.IdFuncionalidad : -1
        }).then(respuesta => {
            // Guardando el listado de permisos
            store.dispatch('privilegiosListado', respuesta.data.json)
            // Guardando las tabs de las paginas activas
            store.dispatch('tabsListado', {path:resp.fullPath,name:resp.name})
            const appLoading = document.getElementById('loading-bg')
            if (appLoading) appLoading.style.display = "none";
            next()
        }).catch(() => {
            // localStorage.clear()
            localStorage.removeItem('userInfo')
            localStorage.removeItem('sermesa_sesion')
            localStorage.removeItem('sermesa_sesion_funcionalidad')
            localStorage.removeItem('sermesa_sesion_tk')
            localStorage.removeItem('sermesa_sesion_ur')
            localStorage.removeItem('sermesa_sesion_inst')

            location.replace("/pages/login");
        })
    } else {
        if (resp.path == "/pages/login") {
            const appLoading = document.getElementById('loading-bg')
            if (appLoading) appLoading.style.display = "none";
            next()
        } else {
            location.replace("/pages/login");
        }
    }
})

router.afterEach((to) => {
    store.dispatch('funcionalidadDocumentacion', null)
    Vue.nextTick(() => {
        setTimeout(() => {
            const doc = (to.matched.length > 1 && to.matched[1].instances.default && to.matched[1].instances.default.doc_) ? to.matched[1].instances.default.doc_ : null
            store.dispatch('funcionalidadDocumentacion', doc)
        }, 1000)
    })
})

export default router
