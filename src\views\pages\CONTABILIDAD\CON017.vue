<template>
<vx-card>
    <div class="content content-pagex">
        <vs-divider class="label-size"> AUXILIAR BANCOS </vs-divider>
        <div class="flex-container">
            <div class="w-full flex flex-wrap">
                <vs-col class="md:w-full lg:w-full xl:w-4/12 p-1">
                    <vx-card class="w-full" style="padding:10px 20px">
                        <div class="w-full" align="right">
                            <div class="w-full" style="padding:10px 10px">
                                <DxButton :width="120" text="Partida" type="success" @click="GenerarPartida()" />&nbsp;
                                <DxButton :width="50" icon="pulldown" type="normal" @click="ConsultaAuxiliarG()" /> 
                            </div>
                        </div>
                        <div class="w-full">
                        <label for="">Cuenta</label>
                            <DxSelectBox 
                                :data-source="ListaCuentasB"
                                :searchable="true" 
                                v-model="cbCuentasB"
                                :display-expr="formatCuenta"
                                value-expr="CodCuentaB"
                                :search-enabled="true"
                                :accept-custom-value="true"
                                @value-changed="onChangeCuentaB"
                            />
                        </div>
                        <div class="w-full" style="margin-top: 5px;">
                            <div class="space-y-2">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Tipo</label>
                                <div class="flex flex-wrap gap-4">
                                    <!-- Cheques -->
                                    <div class="flex items-center">
                                        <label class="inline-flex items-center cursor-pointer">
                                            <vs-radio v-model="Tipo" vs-name="Activa" vs-value="CH" class="radio-custom">
                                            </vs-radio>
                                            <span class="ml-2 text-gray-800">Cheques</span>
                                        </label>
                                    </div>

                                    <!-- Depósitos -->
                                    <div class="flex items-center">
                                        <label class="inline-flex items-center cursor-pointer">
                                            <vs-radio v-model="Tipo" vs-name="Activa" vs-value="DP" class="radio-custom">
                                            </vs-radio>
                                            <span class="ml-2 text-gray-800">Depósitos</span>
                                        </label>
                                    </div>

                                    <!-- Notas -->
                                    <div class="flex items-center">
                                        <label class="inline-flex items-center cursor-pointer">
                                            <vs-radio v-model="Tipo" vs-name="Activa" vs-value="N" class="radio-custom">
                                            </vs-radio>
                                            <span class="ml-2 text-gray-800">Notas</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div style="margin-top: 5px; display:flex;">
                            <div style="width: 90%;">
                                <DxSelectBox
                                    :data-source="ListaPeriodos"
                                    :display-expr="formatPeriodo"
                                    value-expr="CodigoPeriodo"
                                    :show-clear-button="true"
                                    v-model="cbPeriodos"
                                    :search-enabled="true"
                                    :accept-custom-value="true"
                                    @value-changed="guardarPeriodoCompleto"
                                />
                            </div>
                            &nbsp;
                            <div style="width: 10%;">
                                <DxButton type="danger" @click="LimpiarDatos" :width="50" icon="close" />
                            </div>
                        </div>
                         <div style="margin-top: 10px;">
                            <vs-alert v-if="DescMayorizada === 'M'" active="true" color="warning" style="text-align: center; height: 40px;">
                                <label>Hay una partida mayorizada en este período.</label>                    
                            </vs-alert>          
                        </div>
                    </vx-card>
                </vs-col>

                <!-- Reporte -->
                <vs-col class="md:w-full lg:w-full xl:w-8/12 p-1">
                    <vx-card class="w-full">
                        <vs-row class="w-full">
                            <vs-row class="md:w-6/12 lg:w-6/12 xl:w-6/12">
                                <div class="w-full">
                                    <div style="display: flex;">
                                        <div style="padding:2px;width: 20%" v-show="FiltroV">
                                            <vs-checkbox icon-pack="feather" icon="icon-check" color="success" v-model="Filtro">
                                                Filtrar
                                            </vs-checkbox>
                                        </div>
                                        <div style="padding:2px; width: 80%;" v-show="FechaV">
                                          <DxDateBox v-model="Fecha" placeholder="Fecha" :disabled="!Filtro"   display-format="dd/MM/yyyy"/>
                                        </div>
                                    </div>
                                    <div class="w-full" style="padding:2px; margin-top: 10px" v-show="PendienteV">
                                        <vs-checkbox icon-pack="feather" icon="icon-check" color="success" v-model="Pendiente">Solo Pendientes de Revisión</vs-checkbox>
                                    </div>

                                    <div class="w-full" style="padding:2px; margin-top: 5px;" v-show="TodosV">
                                        <vs-checkbox icon-pack="feather" icon="icon-check" color="success" v-model="Todos">Todas las Cuentas, Todos los Tipos</vs-checkbox>
                                    </div>

                                    <div class="w-full" style="padding:2px; margin-top: 5px;" v-show="CuentaV">
                                         <ValidationProvider name="Cuentas">
                                            <label>Cuenta</label>

                                            <multiselect
                                                v-model="cbCuentas"
                                                :options="cuentasFormateadas"
                                                :searchable="true"
                                                :close-on-select="true"
                                                :show-labels="false"
                                                placeholder="Seleccionar Cuenta"
                                                label="label"
                                                track-by="CodigoCuenta"
                                                
                                            >
                                            <span slot="noOptions">Lista no disponible.</span>
                                            </multiselect>




                                        </ValidationProvider>
                                 

                                    </div>
                                </div>
                            </vs-row>

                            <vs-row class="w-full md:w-6/12 lg:w-6/12 xl:w-6/12">
                                <div>
                                    <div class="w-full bg-white rounded-xl shadow-sm p-4">
                                        <div class="space-y-3">
                                            <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors">
                                                <label class="flex items-center cursor-pointer w-full">
                                                    <span class="text-gray-700 font-medium mr-3 w-6">1.</span>
                                                    <vs-radio v-model="IdReporte" vs-name="Reporte" vs-value="1" class="radio-custom">
                                                        <span class="ml-2 text-gray-800">Detallado por Referencia</span>
                                                    </vs-radio>
                                                </label>
                                            </div>

                                            <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors">
                                                <label class="flex items-center cursor-pointer w-full">
                                                    <span class="text-gray-700 font-medium mr-3 w-6">2.</span>
                                                    <vs-radio v-model="IdReporte" vs-name="Reporte" vs-value="2" class="radio-custom">
                                                        <span class="ml-2 text-gray-800">Referencia seleccionada detallada</span>
                                                    </vs-radio>
                                                </label>
                                            </div>

                                            <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors">
                                                <label class="flex items-center cursor-pointer w-full">
                                                    <span class="text-gray-700 font-medium mr-3 w-6">3.</span>
                                                    <vs-radio v-model="IdReporte" vs-name="Reporte" vs-value="3" class="radio-custom">
                                                        <span class="ml-2 text-gray-800">Cuentas Integradas</span>
                                                    </vs-radio>
                                                </label>
                                            </div>

                                            <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors">
                                                <label class="flex items-center cursor-pointer w-full">
                                                    <span class="text-gray-700 font-medium mr-3 w-6">4.</span>
                                                    <vs-radio v-model="IdReporte" vs-name="Reporte" vs-value="4" class="radio-custom">
                                                        <span class="ml-2 text-gray-800">Detalle por Cuenta</span>
                                                    </vs-radio>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="flex flex-wrap">
                                        <div class="md:w-1/7 lg:w-1/7 xl:w-1/7">
                                            <div class="w-full"  style="padding:10px 50px">
                                                <DxButton :width="170" text="Generar Reporte"  type="danger"  icon="pdffile" @click="GenerarReporte()" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </vs-row>
                        </vs-row>
                    </vx-card>
                </vs-col>

            </div>
        </div>
        <div class="w-full" align="center" v-if="AuxiliarSinCuentasV || DescPorDocumentoV">
            <div class="w-full" style="padding:10px 10px">
                <DxButton :width="200" text="Ocultar Información" type="normal" @click="OcultarInformacion()" />
            </div>
        </div>

        <!--GRID DE AUXILIAR SIN CUENTA-->
        <!-- v-if="AuxiliarSinCuentasV" -->
        <div v-if="AuxiliarSinCuentasV">
            <DxDataGrid :data-source="AuxiliarSinCuentas" :allow-column-reordering="true" :show-borders="true">
                <DxDataGridColumn data-field="Tipo" caption="Tipo" data-type="string" />

                <DxDataGridColumn data-field="Referencia" caption="Referencia" data-type="string" />

                <DxDataGridColumn data-field="Fecha" caption="Fecha" data-type="date" />
                <DxDataGridColumn data-field="Debe" caption="Debe" data-type="number" :format="{ formatter: formatMoneda }" />
                <DxDataGridColumn data-field="Haber" caption="Haber" data-type="number" :format="{ formatter: formatMoneda }" />

                <DxDataGridDxSearchPanel :visible="true" :highlight-case-sensitive="true" />
                <DxDataGridPaging :page-size="10" />

                <DxDataGridHeaderFilter :visible="true" />
            </DxDataGrid>
        </div>

        <!--GRID DE DESCUADRE POR DOCUMENTO-->
        <div  v-if="DescPorDocumentoV">
        
            <DxDataGrid
                :data-source="DescPorDocumento"
                :allow-column-reordering="true"
                :show-borders="true"
                :export="{ enabled: true, allowExportSelectedData: false }"
                @exporting="onExporting"
                >
                <DxDataGridColumn data-field="Tipo" caption="Tipo" />
                <DxDataGridColumn data-field="Referencia" caption="Referencia" />
                <DxDataGridColumn data-field="Fecha" caption="Fecha" data-type="date" />
                <DxDataGridColumn data-field="TotalDebe" caption="Debe" data-type="number" />
                <DxDataGridColumn data-field="TotalHaber" caption="Haber" data-type="number" />

                <DxDataGridDxSearchPanel :visible="true" :highlight-case-sensitive="true" />
                <DxDataGridPaging :page-size="10" />
                <DxDataGridHeaderFilter :visible="true" />
            </DxDataGrid>

        </div>

        <div style="margin-top: 5px;">
            <DxDataGrid  ref="myDataGridRef" :data-source="AuxiliarBancos"   :class="{'no-selection-style': IdReporte !== '2'}" :allow-column-reordering="true" :show-borders="true" key-expr="Referencia" :column-auto-width="true" @selection-changed="handleSelectionChanged"   @row-prepared="onRowPrepared">
                <DxDataGridSelection mode="single" />
                <DxDataGridColumn :width="100"  data-field="Status" caption="Status" data-type="string" />
                <DxDataGridColumn :width="100"  data-field="Revisado" caption="Revisado" data-type="string" />
                <DxDataGridColumn data-field="Fecha" caption="Fecha"/>
                <DxDataGridColumn data-field="Referencia" caption="Referencia" data-type="number" />
                <DxDataGridColumn data-field="TipoDocumento" caption="TipoDocumento" data-type="string" />
                <DxDataGridColumn data-field="Documento" caption="Documento" data-type="string" />
                <DxDataGridColumn data-field="Cuenta" caption="Cuenta" data-type="string" />
                <DxDataGridColumn data-field="NombreCuenta" caption="NombreCuenta" data-type="string" />
                <DxDataGridColumn data-field="Detalle" caption="Detalle" data-type="string" />
                <DxDataGridColumn data-field="Debe" caption="Debe" data-type="number" :format="{ formatter: formatMoneda }" />
                <DxDataGridColumn data-field="Haber" caption="Haber" data-type="number" :format="{ formatter: formatMoneda }" />
                <DxDataGridDxSearchPanel :visible="true" :highlight-case-sensitive="true" />
                <DxDataGridPaging :page-size="10" />
                <DxDataGridHeaderFilter :visible="true" :allow-search="true" />
            </DxDataGrid>
        </div>
    </div>

    <DxPopup :visible.sync="PartidaDiario" title="Generar Partida de Diario" height="460" width="500">
        <form>
            <vs-divider class="label-size"> OPCIONES </vs-divider>
            <vs-radio v-model="OpcionSel" vs-name="OpcionSel" vs-value="1"> Nueva Partida </vs-radio>
              <DxTextBox type="number" v-model="NuevaPartida" />
            <label> Fecha </label>
            <div style="width: 100%;">
                <flat-pickr
                    v-model="FechaPartida"
                    :config="configFromdateTimePicker"
                    class="devextreme-like-datepicker"

                />
            </div>
            <label>Referencia</label>
             <DxNumberBox :value="Referencia" :show-spin-buttons="true" v-model="Referencia" />
            <label for="">Descripción</label>
            <vs-textarea rows="3" class="w-full" v-model="Descripcion" />
            <div style="text-align: center;">
                <DxButton :width="120" text="Ok" type="success" @click=" ResumenQuery()" />
            </div>
        </form>
    </DxPopup>


    <DxPopup :visible.sync="BuscarUsuarioParametro" title="Buscar Usuario" height="460" width="850"   @hidden="LimpiarCampos"    >
    <div>
      <form>
        <div style="display: flex; gap: 10px; align-items: center; padding: 10px;">
          <DxTextBox v-model="Busqueda.Corporativo" placeholder="Corporativo" :width="100"  @enter-key="BuscarCorporativo()"
          />
          <DxTextBox v-model="Busqueda.Nombre" placeholder="Nombre" :width="200" @enter-key="BuscarCorporativo()"
          />
          <DxTextBox v-model="Busqueda.Apellido" placeholder="Apellido" :width="200"  @enter-key="BuscarCorporativo()"
          />
          <DxButton type="default" icon="search" text="Buscar" styling-mode="contained" @click="BuscarCorporativo()" />
          <DxButton type="danger" icon="refresh" text="Limpiar" styling-mode="outlined" @click="LimpiarCampos()" />
        </div>
      </form>
    </div>
    <br>
    <DxDataGrid :data-source="RespBusqueda" :allow-column-reordering="true" :show-borders="true" :column-auto-width="true"   @row-dbl-click="onRowDblClick">
        <DxDataGridSelection mode="single" />
        <DxDataGridColumn data-field="Corporativo" caption="Corporativo" data-type="string" />
        <DxDataGridColumn data-field="Nombres" caption="Nombres" data-type="string" />
        <DxDataGridColumn data-field="Apellidos" caption="Apellidos" data-type="string" />
        <DxDataGridDxSearchPanel :visible="true" :highlight-case-sensitive="true" />
        <DxDataGridPaging :page-size="5"/>
    </DxDataGrid>
  </DxPopup>

</vx-card>
</template>

<script>



import Multiselect from 'vue-multiselect'
import "vue-multiselect/dist/vue-multiselect.min.css"
import flatPickr from 'vue-flatpickr-component';
import 'flatpickr/dist/flatpickr.css';
import {
    Spanish as SpanishLocale
} from 'flatpickr/dist/l10n/es.js';


export default {
    components: {
        Multiselect,
        flatPickr
    },
    data() {
        return {
            ListaPeriodos: [],
            CodigoPeriodo:null,
            DescPeriodo: '',
            Fecha:null,
            FechaPartida: null,
            FechaV: true,
            Referencia:0,
            Cuenta: null,
            CuentaV: true,
            Descripcion: '',
            OpcionSel: '',
            NuevaPartida: null,
            multiselectKey: 0,
            Bloqueo1: false,
            Bloqueo2: true,
            DescMayorizada: null,
            Periodo: '',
            Tipo: 'CH',
            cbCuentasB: null,
            ListaCuentasB: [],
            CodCuentaB: '',
            NombreCuentaB: '',
            Banco: '',
            cbCuentas: {
                CodigoCuenta: null,
                Nombre: null
            },
            ListaCuentas: [],
            CodCuenta: '',
            NombreCuenta: '',
            Filtro: false,
            FiltroV: true,
            Pendiente: false,
            PendienteV: true,
            Todos: false,
            TodosV: true,
            Usuario: false,
            UsuarioV: false,
            TipoProv: null,
            TipoPV: true,
            IdReporte: 0,
            AuxiliarBancos: [],
            ListadoReportes: [],
            FechaIPeriodoA: '',
            PartidaDiario: false,
            AuxiliarSinCuentas: [],
            AuxiliarSinCuentasV: false,
            DescPorDocumento: [],
            DescPorDocumentoV: false,
            ReferenciaSel: null,
            BuscarUsuarioParametro:false,
            DatosPeriodo: [],
            cbPeriodos:null,
            periodoSeleccionado: null, // Objeto completo seleccionado

            configFromdateTimePicker: {
                minDate: '',
                maxDate: '',
                locale: SpanishLocale,
                dateFormat: "d/m/Y",
            },

            Busqueda:{
                Corporativo:null,
                Apellido:null,
                Nombre:null
            },

            RespBusqueda:[],

            UsuarioSeleccionado: {
                Corporativo:null,
                },


        }
    },
    mounted() {

        
        this.IdReporte = '1'
        this.ConsultaPeriodo(),
        this.ConsultaCuentasB(),
        this.ConsultasCuentas()
        this.ListadoReportes = this.$recupera
        this.DescPorDocumentoV = false
        this.AuxiliarSinCuentasV = false
    },
    watch: {

        'IdReporte'() {
            if (this.IdReporte == '1') {
                this.FiltroV = true
                this.FechaV = true
                this.PendienteV = true
                this.TodosV = false
                this.UsuarioV = true
                this.TipoPV = true
                this.CuentaV = false
                this.clearGridSelection()
                this.limpiarCuenta()
           


            }
            if (this.IdReporte == '2') {
                this.FiltroV = false
                this.FechaV = false
                this.PendienteV = true
                this.UsuarioV = false
                this.TodosV = false
                this.TipoPV = false
                this.CuentaV = false
                this.clearGridSelection()
                this.limpiarCuenta()

            }
            if (this.IdReporte == '3') {
                this.FiltroV = true
                this.FechaV = true
                this.PendienteV = true
                this.TodosV = true
                this.UsuarioV = false
                this.TipoPV = false
                this.CuentaV = true
                this.clearGridSelection()
                this.limpiarCuenta()


                
            }
            if (this.IdReporte == '4') {
                this.FiltroV = true
                this.FechaV = true
                this.PendienteV = true
                this.TodosV = false
                this.UsuarioV = false
                this.TipoPV = false
                this.CuentaV = true
                this.clearGridSelection()
                this.limpiarCuenta()                


            }
            if (this.IdReporte !== '2') {
                this.$nextTick(() => {
                    const gridInstance = this.$refs.miDataGrid?.instance;
                    gridInstance?.clearSelection();
                });
            }
        },
        'OpcionSel'() {

            if (this.OpcionSel == '1') {
                this.Bloqueo1 = false,
                    this.Bloqueo2 = true

            } else {
                this.Bloqueo1 = true,
                    this.Bloqueo2 = false
            }
        },
        ListaCuentasB(newList) {
        if (newList.length > 0 && !this.cbCuentasB) {
             this.cbCuentasB = newList[0].CodCuentaB;
            }
        },
        'Busqueda.Corporativo'(nuevo) {
            if (nuevo) {
                this.Busqueda.Nombre = '';
                this.Busqueda.Apellido = '';
            }
        },
            'Busqueda.Nombre'(nuevo) {
            if (nuevo) {
                this.Busqueda.Corporativo = '';
            }
        },
            'Busqueda.Apellido'(nuevo) {
            if (nuevo) {
                this.Busqueda.Corporativo = '';
            }
        },

        Filtro(nuevoValor) {
            if (nuevoValor) {
                // Si el checkbox está activado, se asigna la fecha actual
                this.Fecha = this.periodoSeleccionado.FechaInicial  // Formato 'YYYY-MM-DD'
                
            } else {
                // Si está desactivado, se borra la fecha
                this.Fecha = null;
            }
        },
        Tipo() {
            this.ConsultaAuxiliarG();
             this.limpiarFiltros()
        }
    },
    computed: {
        isSelectionAllowed() {
            return this.IdReporte === '2';
        },
        cuentasFormateadas() {
            return this.ListaCuentas.map(cuenta => ({
            ...cuenta,
            label: `${cuenta.CodigoCuenta} - ${cuenta.Nombre}`
            }));
        }
    },

    methods: {
        onSelectionChanged({ selectedRowsData }) {
            this.ReferenciaSel = selectedRowsData[0]; 
        },

        onRowPrepared(e) {
            if (e.rowType === 'data' && e.data.Status === 'A') {
                e.rowElement.style.backgroundColor = '#fadbd8'; // Puedes usar '#00FFFF' si prefieres
            }
        },

        handleSelectionChanged(e) {
            if (!this.isSelectionAllowed) {
            // Limpiamos selección si no está permitido seleccionar
            e.component.clearSelection();
            return;
            }

            // Si está permitido, ejecutamos el método original
            this.onSelectionChanged(e);
        },
        onSelectionChangedUser({ selectedRowsData }) {
            if (selectedRowsData.length > 0) {
                const usuario = selectedRowsData[0];
                this.UsuarioSeleccionado.Corporativo = usuario.Corporativo;
            }
            this.BuscarUsuarioParametro = false;
        },

        clearGridSelection() {
            // Accede a la instancia del DataGrid y limpia la selección
            this.$refs.myDataGridRef.instance.clearSelection();
            this.ReferenciaSel = null; // Limpia también tu variable local
        },

        onRowDblClick(e) {
            const usuario = e.data;

            this.UsuarioSeleccionado.Corporativo = usuario.Corporativo;
            this.UsuarioSeleccionado.Nombre = usuario.Nombres;
            this.UsuarioSeleccionado.Apellido = usuario.Apellidos;

            this.BuscarUsuarioParametro = false; // Cierra el popup
        },


        guardarPeriodoCompleto(e) {
            let codigoSeleccionado = e.value;
            this.periodoSeleccionado = this.ListaPeriodos.find(
                (periodo) => periodo.CodigoPeriodo === codigoSeleccionado
            );
            
            if (this.periodoSeleccionado) {
                
                // Solo ejecutar las consultas si el periodo está definido
                this.ConsultaAuxiliarG();
                this.Mayorizar();
                this.limpiarFiltros()
                
                this.AuxiliarSinCuentasV = false
                this.DescPorDocumentoV = false
            }
        },

        formatPeriodo(item) {
            if (!item) return '';
            return `${item.CodigoPeriodo} - ${item.DescPeriodo}`;
        },

        formatCuentas(item) {
            if (!item) return '';
            return `${item.CodigoCuenta} - ${item.Nombre}`;
        },

        formatCuenta(item){
            if (!item) return '';
            return `${item.CodCuentaB} - ${item.NombreCuentaB}`;
        },
        
        formatMoneda(value) {
            // Si el valor es 0 (o equivalente), retorna vacío
            if (value === 0 || value === 0.0000 || value === "0.0000") {
                return "";
            }
            
            // Si es null o undefined, retorna "Q 0.00" (como en tu lógica original)
            if (value === null || value === undefined) {
                return "Q 0.00";
            }

            // Formato normal para números distintos de cero
            const formatted = `Q ${Math.abs(value).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",")}`;
            return value < 0 ? `<span style="color: red;">-${formatted}</span>` : formatted;
        },


       
        OcultarInformacion() {
            this.AuxiliarSinCuentasV = false
            this.DescPorDocumentoV = false
        },

        //PERIODOS
        ConsultaPeriodo() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/ConsultaPeriodo', {
                    Opcion: "C",
                    SubOpcion: "6",
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaPeriodos = resp.data.json;
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido cargar los periodos, favor validar.',
                        })
                    }
                    this.ConsultaPeriodoActual();
                })
                .catch(() => {})
        },

        
        ConsultaPeriodoActual() {
            const url = this.$store.state.global.url;
            const endpoint = url + 'app/v1_contabilidad_general/ConsultaPeriodoActual';
            
            
            this.axios.post(endpoint, {
                Opcion: "C",
                SubOpcion: "2",
            })
            .then(resp => {
                
                if (resp.data.codigo == 0) {
                    
                    this.DatosPeriodo = resp.data.json


                    const codigoPeriodoActual = resp.data.json[0].CodigoPeriodo;

                    
                    // Buscar el periodo en this.ListaPeriodos
                    const periodo = this.ListaPeriodos.find(p => p.CodigoPeriodo === codigoPeriodoActual);
                    
                    if (periodo) {
                        this.cbPeriodos = periodo.CodigoPeriodo;
                    }
                    
                } else {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Cuentas',
                        text: 'No se ha podido cargar los periodos, favor validar.',
                    });
                }
            })
            .catch(() => {
            });
        },

    onChangePeriodos(value) {
        if (value !== null && value.length !== 0) {
            this.CodigoPeriodo = value.CodigoPeriodo;
            this.DescPeriodo = value.DescPeriodo;

            this.FechaMin = new Date(value.FechaInicial);
            this.FechaMax = new Date(value.FechaFinal); // sin sumar un día

            this.configFromdateTimePicker.minDate = this.FechaMin;
            this.configFromdateTimePicker.maxDate = this.FechaMax;
            this.configFromdateTimePicker.enable = [
                (date) => date >= this.FechaMin && date <= this.FechaMax
            ];

            this.FechaPartida = new Date(this.FechaMax); // usar FechaMax directamente

            if (this.FechaPartida < this.FechaMin) {
                this.FechaPartida = this.FechaMin;
            } else if (this.FechaPartida > this.FechaMax) {
                this.FechaPartida = this.FechaMax;
            }

        } else {
            this.DescPeriodo = null;
            this.NuevaPartida = null;
            this.Fecha = new Date();
            this.FechaMin = null;
            this.FechaMax = null;
            this.configFromdateTimePicker.enable = [];
        }
    },


        //CuentasBanco
        ConsultaCuentasB() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/ConsultaAuxiliaresB', {
                    Opcion: "C",
                    SubOpcion: "1",
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaCuentasB = resp.data.json
                        this.ConsultaAuxiliarG()
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Auxiliar Bancos',
                            text: 'No se ha podido cargar las cuentas, favor validar.',
                        })
                    }
                })
                .catch(() => {})
        },

        onChangeCuentaB(e) {
            if (!this.ListaCuentasB) return; // Verificación inicial
            
            const value = e.value;
            const cuenta = this.ListaCuentasB.find(x => x.CodCuentaB === value);
            
            if (cuenta) {
                this.AuxiliarSinCuentasV = false;
                this.DescPorDocumentoV = false;
                this.CodCuentaB = cuenta.CodCuentaB;
                this.Banco = cuenta.Banco;
                this.NombreCuentaB = cuenta.NombreCuentaB;
                
                // Validar si existe periodoSeleccionado y CodigoPeriodo antes de llamar a ConsultaAuxiliarG
                if (this.periodoSeleccionado && this.periodoSeleccionado.CodigoPeriodo) {
                    this.ConsultaAuxiliarG();
                } else {
                    // Opcional: Mostrar mensaje al usuario o limpiar datos
                    this.AuxiliarCostosDataSurce = null; // O cualquier otra acción necesaria
                }
            }
        },

        CuentaBSeleccionado({
            CodCuentaB,
            Banco,
            NombreCuentaB
        }) {
            return `${CodCuentaB} - ${Banco} - ${NombreCuentaB}`
        },

        //Cuentas
        ConsultasCuentas() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/ConsultasCuentas', {
                    Opcion: "C",
                    SubOpcion: "1",
                    Activa: "S"
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaCuentas = resp.data.json
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Auxiliar Bancos',
                            text: 'No se ha podido cargar las cuentas, favor validar.',
                        })
                    }
                })
                .catch(() => {})
        },


     

        onChangeCuenta(value) {

            if (value !== null && Object.keys(value).length !== 0) {
            this.cbCuentas = {
                CodigoCuenta: value.CodigoCuenta,
                Nombre: value.Nombre
            };
            } else {
            this.cbCuentas = {
                CodigoCuenta: null,
                Nombre: null
            };
            }
        },


        //Grid
        ConsultaAuxiliarG() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/ConsultaAuxiliaresB', {
                    Opcion: "C",
                    SubOpcion: "2",
                    TipoPartida: this.Tipo,
                    CuentaBanco: this.cbCuentasB,
                    Periodo: this.periodoSeleccionado.CodigoPeriodo
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.AuxiliarBancos = resp.data.json
                    }
                })

        },
        
        //ReportesFiltros

        //Reporte 1 
        async GenerarReporte() {
            let nombreReporte;
            let OpcionInterna = this.IdReporte;
            let referencia = null; // Variable para manejar la referencia

            if (this.IdReporte == 1) {
                nombreReporte = 'ReporteDetalladoPorRef';
                this.ReferenciaSel = '';
                referencia = this.ReferenciaSel;
            } else if (this.IdReporte == 2) {
            const necesitaReferencia = this.IdReporte == 2 && !this.Todos;
            if (necesitaReferencia && (!this.ReferenciaSel || Object.keys(this.ReferenciaSel).length === 0)) {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Error al generar el reporte',
                    text: 'Seleccione una referencia antes de generar el Reporte.',
                });
                return;
            }

                nombreReporte = 'ReporteDetalladoPorRefSel';
                OpcionInterna = this.Todos ? 1 : 2;
                referencia = this.Todos ? null : this.ReferenciaSel.Referencia;
            } else if (this.IdReporte == 3) {
                nombreReporte = 'ReporteCuentasIntegradas';
                referencia = null; // No necesitas referencia para este reporte
            } else if (this.IdReporte == 4) {
                nombreReporte = 'ReporteDetallePorCuenta';
                referencia = null; // No necesitas referencia para este reporte
            }

            let postData = {
                nombrereporte: nombreReporte,
                Opcion: 'C',
                SubOpcion: OpcionInterna,
                Fecha: this.Fecha,
                TipoPartida: this.Tipo,
                Periodo: this.periodoSeleccionado.CodigoPeriodo,
                CuentaBanco: this.cbCuentasB,
                Cuenta: this.cbCuentas.CodigoCuenta,
                Referencia: referencia, // Usamos la variable referencia que hemos controlado
                Pendiente: this.Pendiente ? 'S' : null,
                Todos: this.Todos
            };

            try {
                this.$reporte_modal({
                    Nombre: 'ReportesAuxiliarBancos',
                    Opciones: { ...postData },
                });
            } catch (err) {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#e74c3c',
                    title: 'Error Generar',
                    acceptText: 'Aceptar',
                    text: 'Hubo un problema para generar el Reporte.',
                    buttonCancel: 'border',
                    accept: () => {},
                });
                return;
            }
        },

        GenerarPartida() {
            if (this.Fecha >= this.FechaIPeriodoA) {
                this.AuxiliarSinCuenta()
                this.Correlativo()
            } else {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#e74c3c',
                    title: 'Error al Generar Partida de Diario',
                    acceptText: 'Aceptar',
                    text: 'La Fecha seleccionada es menor a la fecha del periodo actual.',
                    buttonCancel: 'border',
                    accept: () => {},
                })
                return
            }
        
            const info = this.periodoSeleccionado

            if (info && info.CodigoPeriodo) {
                // Llamar a onChangePeriodos solo si la información es válida
                this.onChangePeriodos(info)
            } else {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#e74c3c',
                    title: 'Valores incorrectos',
                    acceptText: 'Aceptar',
                    text: 'No hay información válida en periodoSeleccionado',
                    buttonCancel: 'border',
                    accept: () => {},
                })
                return
            }
        },

        AuxiliarSinCuenta() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/ConsultaAuxiliaresB', {
                    Opcion: "C",
                    SubOpcion: "5",
                    Periodo: this.periodoSeleccionado.CodigoPeriodo

                })
                .then(resp => {
                    if (resp.data.codigo === 0) {
                        this.AuxiliarSinCuentas = resp.data.json
                        
                        if (this.AuxiliarSinCuentas.length > 0) {
                            this.AuxiliarSinCuentasV = true
                             this.$vs.dialog({
                                type: 'alert',
                                color: '#e74c3c',
                                title: 'Error al Generar Partida de Diario',
                                acceptText: 'Aceptar',
                                text: 'Favor revisar los documentos sin número de cuenta.',
                                buttonCancel: 'border',
                                accept: () => {},
                            })
                            return;
                        } else {
                            this.DescuadrePorDoc()
                        }
                    }
                })
                .catch(() => {})
        },
        DescuadrePorDoc() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/ConsultaAuxiliaresB', {
                    Opcion: "C",
                    SubOpcion: "4",
                    Periodo: this.periodoSeleccionado.CodigoPeriodo
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.DescPorDocumento = resp.data.json

                        if (this.DescPorDocumento.length > 0) {
                            this.DescPorDocumentoV = true
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#e74c3c',
                                title: 'Error al Generar Partida de Diario',
                                acceptText: 'Aceptar',
                                text: 'Partida total no cuadra por más de un centavo, a continuacion se detalla el total de los no cuadrados.',
                                buttonCancel: 'border',
                                accept: () => {},
                            })
                            return;
                        } else {
                            this.OpcionSel = 1
                            this.PartidaDiario = true
                            this.Periodo = this.periodoSeleccionado.CodigoPeriodo
                            this.DescPorDocumentoV = false
                        }
                    }
                })
                .catch(() => {})
        },

        ResumenQuery() {
            if (this.Partida == 0 || this.Partida == '') {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Generar Partida de Diario',
                    text: 'Ingrese Número de partida.',
                });
                return;
            }
            if (this.Periodo == 0 || this.Periodo == '') {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Generar Partida de Diario',
                    text: 'Seleccione el periodo.',
                });
                return;
            } else {
                const url = this.$store.state.global.url;

                // Convertir fecha de 'dd/MM/yyyy' a 'yyyy/MM/dd' (Forma segura)
                let fechaFormateada = null;
                if (this.FechaPartida) {
                    const [day, month, year] = this.FechaPartida.split('/');
                    const fechaObj = new Date(`${year}-${month}-${day}`);
                    
                    // Verificar si la fecha es válida
                    if (!isNaN(fechaObj.getTime())) {
                        fechaFormateada = fechaObj.toISOString().split('T')[0].replace(/-/g, '/');
                    }
                }

                this.axios.post(url + 'app/v1_contabilidad_general/TranAuxiliaresB', {
                        Opcion: "I",
                        SubOpcion: "2",
                        Periodo: this.periodoSeleccionado.CodigoPeriodo, // Corregir si es "Periodo" o "Periodo"
                        CuentaBanco: this.cbCuentasB,
                        Partida: this.NuevaPartida,
                        Fecha: fechaFormateada, // Formato: 'yyyy/MM/dd'
                        Referencia: this.Referencia,
                        Descripcion: this.Descripcion
                    })
                    .then(resp => {
                        if (resp.data.codigo == 0) {
                            this.PartidaDiario = false;
                            this.Descripcion = null;
                        }
                    })
                    .catch(() => {
                        this.PartidaDiario = false;
                        this.Descripcion = null;
                    });
            }
        },

        BuscarUsuario(){
            this.BuscarUsuarioParametro = true
        },


        BuscarCorporativo() {
            const payload = this.construirPayload();

            if (Object.keys(payload).length === 0) {
            this.$notify?.({
                message: 'Debe ingresar al menos un criterio de búsqueda.',
                type: 'warning',
            });
            return;
            }

            this.axios.post("/app/Ajenos/Busqueda_Corporativo", payload)
            .then((resp) => {
                this.RespBusqueda = resp.data.json;
            })
            .catch(() => {
            });
        },

        construirPayload() {
            const datos = {};
            const campos = ['Corporativo', 'Nombre', 'Apellido'];

            campos.forEach((campo) => {
            if (this.Busqueda[campo]?.trim()) {
                datos[campo] = this.Busqueda[campo].trim();
            }
            });

            return datos;
        },

        Correlativo() {
            const url = this.$store.state.global.url;
            this.axios.post(url + 'app/v1_contabilidad_general/CorrelativoCorrecto', {
                Opcion: "C",
                SubOpcion: "8",
                Periodo: this.periodoSeleccionado.CodigoPeriodo,
            }).then((resp) => {
                if (resp.data && resp.data.json && resp.data.json.length > 0) {
                    const corr = resp.data.json[0].CorrelativoPartidas;
                    this.NuevaPartida = corr === '' ? '1' : corr; // ahora es string
                }
            }).catch(() => {
            });
        },

        Mayorizar() {
            const url = this.$store.state.global.url;
            this.axios.post(url + 'app/v1_contabilidad_general/Mayorizacion_SM', {
                    Opcion: 'C',
                    SubOpcion: '1',
                    Periodo: this.periodoSeleccionado.CodigoPeriodo
                })
                .then(resp => {
                    this.DescMayorizada = null
                    if (resp.data.codigo === 0) {
                        this.DescMayorizada = resp.data.json[0].DescripcionM;
                    }
                })
                .catch(() => {
                });
        },


        LimpiarCampos() {
            this.Busqueda.Corporativo = null
            this.Busqueda.Nombre = null
            this.Busqueda.Apellido = null
            this.RespBusqueda = []


        },

        LimpiarDatos(){
            this.ConsultaPeriodo()
            this.ConsultaCuentasB()
            this.AuxiliarBancos = []   
            this.UsuarioSeleccionado.Corporativo = null
            this.Fecha = null
            this.Pendiente = false 
            this.Filtro = false
            this.AuxiliarSinCuentasV = false
            this.DescPorDocumentoV = false
          
        },

        limpiarFiltros() {
            const gridInstance = this.$refs.myDataGridRef.instance;
            if (gridInstance) {
            gridInstance.clearFilter();
            gridInstance.option('searchPanel.text', '');
            gridInstance.option('paging.pageIndex', 0);
            }
        },
        limpiarCuenta() {
            this.cbCuentas = {
                CodigoCuenta: null,
                Nombre: null
            };
            this.onChangeCuenta(this.cbCuentas);
        }


    }
}
</script>   

<style>
.footer {
    height: 70px;
    margin-top: -65px;
    margin-left: 10px;
}

.label-size {
    font-size: 14px;
    font-weight: bold;
}

.label-i {
    font-size: 18px;
    font-weight: bold;
    color: rgb(240, 158, 6);
}

.cuadro-i {
    background: #faf2f1;
}

.flex-container {
    display: flex;
    flex-direction: row;
}

.padre {
    position: relative;
}

.hijo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}


.radio-custom:hover {
  --vs-radio-border-color: #9ca3af;
}

.vs-radio--text {
  font-size: 0.9375rem;
}

.radio-custom {
  --vs-radio-border-color: #d1d5db;
  --vs-radio-background-color-checked: #3b82f6; /* Azul más moderno */
  --vs-radio-border-color-checked: #3b82f6;
  --vs-radio-size: 1.1rem;
}

.radio-custom:hover {
  --vs-radio-border-color: #93c5fd;
}

.vs-radio--text {
  font-size: 0.9375rem;
  color: #374151; /* Color de texto más oscuro */
}

/* Efecto de transición suave */
.vs-radio-con {
  transition: all 0.2s ease;
}

/* Cuando no se permite selección, quitar color de fila seleccionada */
.no-selection-style .dx-selection.dx-row > td {
  background-color: transparent !important;
}

 .devextreme-like-datepicker {
  border: 1px solid #d3d3d3;
  border-radius: 4px;
  padding: 6px 10px;
  font-size: 14px;
  height: 36px;
  width: 100%;
  max-width: 100%;
  font-family: "Segoe UI", Roboto, sans-serif;
  box-sizing: border-box;
  transition: border 0.2s ease-in-out;
}

.devextreme-like-datepicker:focus-within {
  border-color: #0074c1;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0,116,193,0.2);
}



.dx-datagrid .dx-selection.dx-row > td {
  background-color: #f7dc6f !important;
   color: black !important;
}

.dx-header-filter:not(.dx-header-filter-empty) {
    color: yellow;
}


.color{
    background-color: aqua !important;
}

.dx-datagrid-headers .dx-datagrid-table .dx-header-row {
    background-color: #2980b9; /* Verde */
    color: white;
}

</style>
