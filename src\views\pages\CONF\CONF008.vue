<template>
<vx-card title="Servicio de envío" icon="fas fa-cogs">

    <div class="flex flex-wrap conf-process-ec-container">
        <h6 class="w-full p-5">Iniciar o detener el proceso de generación y envío de estado de cuenta del médico.</h6>

        <vs-input class="w-full md:w-1/3 p-2" type="text" label="Estado" v-model="ServicioEnvio.Estado" disabled />
        <vs-input class="w-full md:w-1/3 p-2" type="text" label="Instanica" v-model="ServicioEnvio.Instancia" disabled />
        <vs-input class="w-full md:w-1/3 p-2" type="number" label="Periodicidad (minutos)" v-model="ServicioEnvio.Periodicidad" />

        <Destinatarios v-model="ServicioEnvio.CorreosPruebas" placeholder="Lista de correo para pruebas" class="w-full lg: w-128 m-2" />
        <div class="flex flex-wrap w-auto">
            <vs-button icon="play_arrow" color="success" class="w-64 m-2" @click="IniciarServicio">Iniciar servicio</vs-button>
            <vs-button icon="stop" color="danger" class="w-64 m-2" @click="DetenerServicio">Detener servicio</vs-button>
            <vs-button icon="refresh" color="primary" class="m-2" @click="CargarParametrosServicio" />
        </div>

    </div>

</vx-card>
</template>

<script>
export default {
    name: 'ServicioCorreoHonorarios',
    components: {
        Destinatarios: () => import('../../../components/sermesa/global/SMCorreo/SMDestinatario.vue')
    },
    data() {
        return {

            Permisos: {
                ServicioEnvio: false,
            },
            ServicioEnvio: {
                Periodicidad: null,
                CorreosPruebas: [],
                Instancia: null,
                Estado: '',
            },

        }
    },
    mounted() {
        this.Permisos.ServicioEnvio = this.$validar_privilegio('GESTION_SERVICIO_ENVIO').status
    },
    methods: {
        LimpiarDatos() {
            this.ServicioEnvio = {
                Periodicidad: null,
                CorreosPruebas: [],
                Instancia: null,
                Estado: '',
            }
        },
        async CargarParametrosServicio() {
            this.axios.post('/app/Honorarios/get-service-params', {}).then(resp => {
                this.ServicioEnvio.Periodicidad = resp.data.Periodo
                this.ServicioEnvio.CorreosPruebas = resp.data.CorreoAmbientePruebas
                this.ServicioEnvio.Instancia = resp.data.Instancia
                this.ServicioEnvio.Estado = resp.data.Status
            }).catch(() => this.LimpiarDatos())
        },
        async IniciarServicio() {
            this.axios.post('/app/Honorarios/service-start', {
                Periodo: this.ServicioEnvio.Periodicidad,
                CorreosPrueba: this.ServicioEnvio.CorreosPruebas,
            }).then(this.CargarParametrosServicio)
        },
        async DetenerServicio() {
            this.axios.post('/app/Honorarios/service-stop', {}).then(this.CargarParametrosServicio)
        }

    }
}
</script>

<style>
.conf-process-ec-container {
    max-width: 700px;
}
</style>
