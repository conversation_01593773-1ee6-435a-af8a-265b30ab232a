<template>
    <vx-card title="Despacho Requerimientos por Bodega">
        <div class="container">

 
            <div class="a-mqN00-0">
                <div class="w-full md:w-full lg:w-10/12 xl:w-6/12  flex items-center justify-center m-4">
                    <label>Fecha Inicial:</label>
                    <datepicker v-model="FechaInicial" placeholder="Fecha Inicial" :format="customFormatter"></datepicker>
                </div>
            </div>
            <div class="a-mqN00-1">
                <div class="w-full md:w-full lg:w-10/12 xl:w-6/12  flex items-center justify-center  m-4">
                    <label>Fecha Final:</label>
                    <datepicker v-model="FechaFinal" placeholder="Fecha Final" :format="customFormatter"></datepicker>
                </div>
            </div>
            <div class="a-MZWKK pl-4">
                <vs-row>
                    <vs-col vs-w="2">
                            <SM-Buscar
                                v-model="Bodega"
                                label="Codigo Bodega"
                                api="app/v1_OrdenCompra/ConsultarBodegas"
                                :api_campo_respuesta_mostrar="['Bodega', 'Nombre']"
                                :api_campos="['Bodega', 'Nombre']"
                                :api_titulos="['Bodega', 'Nombre']"
                                api_campo_respuesta="Bodega"
                                :api_preload="true"
                                :disabled_texto="true"
                                :callback_buscar="ConsultarBodega" />
                    </vs-col>
                    <vs-col vs-w="6" class="pl-4">

                            <vs-input label="NombreBodega" class="w-full" v-model="this.NombreBodega" readonly />

                    </vs-col>
                </vs-row>


            </div>
            <div class="a-J8WKK">

                <vs-row class="m-2">
                    <vs-col vs-type="flex" vs-w="12">
                        <vs-col vs-type="flex" class="m-2">
                        
                            <vs-button class="block" @click.native="GenerarReporte()">
                                <i class="fas fa-file-pdf height:25px"></i>
                                Generar
                            </vs-button>
                        </vs-col>
                    </vs-col>
                </vs-row>
            </div>
        </div>
    
    </vx-card>
</template>



<script>

import Datepicker from 'vuejs-datepicker';
import * as lang from 'vuejs-datepicker/src/locale';
import moment from "moment";


export default {
    components: {
        Datepicker
    },
    data() {
        return {
            languages: lang,
            FechaInicial: '',
            FechaFinal: '',
            Bodega:0,
            NombreBodega:''


        }

    },
    mounted: function () {
        this.setinitialDate()
        this.setendDate();
    },
    methods: {

        setinitialDate() {
            this.FechaInicial = new Date();
            return moment(this.FechaInicial).format('DD/MM/YYYY');            
        },
        setendDate() {
            this.FechaFinal = new Date();
            return moment(this.FechaFinal).format('DD/MM/YYYY');            
        },
        customFormatter(date) {
            return moment(date).format('DD/MM/YYYY');
        },
        ConsultarBodega(datos) {
                this.NombreBodega = datos.Nombre
        },
        async GenerarReporte(){

            const FechaInicio = moment(this.FechaInicial, 'ddd MMM DD YYYY HH:mm:ss [GMT]ZZ').format('YYYY-MM-DD');
            const FechaFin = moment(this.FechaFinal, 'ddd MMM DD YYYY HH:mm:ss [GMT]ZZ').format('YYYY-MM-DD');

                let postData = {
                        Bodega: this.Bodega ,
                        FechaInicio: FechaInicio,
                        FechaFin : FechaFin
                }
                        
                this.$reporte_modal({
                        Nombre: 'ReporteDespachoPorBodega',
                        Opciones: {
                        ...postData
                        },
                        Formato:'EXCEL'
                })


        }
    }

}


</script>



<style scoped>
* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

.container {
    display: grid;
    grid-gap: 5px;
    width: 100%;
    height: 100%;
    grid-template-areas: "a-mqN00-0 a-mqN00-1 a-MZWKK"
        "a-J8WKK a-J8WKK a-J8WKK";
    grid-template-columns: 0.5fr 0.5fr 1fr;
    grid-template-rows: 0.5fr 0.5fr;
}


.container>div {
    border: 1px solid #888;
}

.a-mqN00-0 {
    grid-area: a-mqN00-0;
}

.a-mqN00-1 {
    grid-area: a-mqN00-1;
}

.a-MZWKK {
    grid-area: a-MZWKK;
}

.a-J8WKK {
    grid-area: a-J8WKK;
}


.container {
    max-width: 100%;
}
</style>