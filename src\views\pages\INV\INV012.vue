<template>
    <vx-card title="Máximos y Mínimos">
    
        <div class="content content-pagex">
            <vs-divider></vs-divider>
            <!----------------------- VISTA GENERAL ---------->
            <div class="flex flex-wrap">
    
                <div style="margin-right: 20px" class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                    <label class="typo__label">Bodega</label>
                    <multiselect v-model="cb_bodegas" :options="Lista_bodegas" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Bodega_seleccionada" placeholder="Seleccionar" @input="onChangeBodega">
                        <span slot="noOptions">Lista no disponible.</span>
                    </multiselect>
                </div>
    
                <vs-button style="float:left;margin: 20px" color="primary" type="filled" icon-pack="fas" icon="fa-search" @click="Consultar_MaxMinBodega('E', Id_Bodega_seleccionada);"> Búsqueda</vs-button>
                <vs-button color="primary" style="float:left;margin: 20px" type="filled" icon-pack="fas" icon="fa-plus" @click="Abrir_Ventana_Emergente_Nuevo()"> Nuevo</vs-button>
    
            </div>
    
            <!----------------------- MINIMOS Y MAXIMOS DETALLE ---------->
            <vs-divider></vs-divider>
    
            <vs-table2 max-items="10" order pagination :data="Lista_maxmin" tooltip search id="tb_lista_solicitud">
    
                <template slot="thead">
                    <th width="200px">Bodega</th>
                    <th>Producto</th>
                    <th width="130px">Unidad Medida</th>
                    <th width="10px">Mínimo</th>
                    <th width="10px">Máximo</th>
                    <th width="130px"></th>
    
                </template>
    
                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">
    
                        <vs-td2>
                            {{tr.DESCRIPCION_BODEGA}}
                        </vs-td2>
    
                        <vs-td2>
                            {{tr.DESCRIPCION_PRODUCTO}}
                        </vs-td2>
    
                        <vs-td2>
                            {{tr.DESCRIPCION_UNIDAD}}
                        </vs-td2>
    
                        <vs-td2>
                            {{tr.MINIMO}}
                        </vs-td2>
    
                        <vs-td2>
                            {{tr.MAXIMO}}
                        </vs-td2>
    
                        <vs-td2 align="right">
                            <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="Eliminar_Registro(tr)"></vs-button>
                        </vs-td2>
                    </tr>
                </template>
            </vs-table2>
            <!--------------------- FINALIZACION DETALLE --------------------->
    
            <!----------------------- NUEVA ASOCIACIÓN ---------->
            <vs-popup classContent="popup-example" title="Asociar Máximos y Mínimos" :active.sync="Ventana_EmergenteNuevo">
                <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
    
                    <br>
                    <!-----  CLASE /  PRODUCTO ------------->
                    <div class="flex flex-wrap">
    
                        <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                            <ValidationProvider name="cb_busqueda_corporativo" rules="required" v-slot="{ errors }" class="required">
                                <label class="typo__label">Clase</label>
                                <multiselect v-model="cb_clase" :options="Lista_clase" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Clase_seleccionada" placeholder="Seleccionar" @input="onChangeClase" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null">
                                    <span slot="noOptions">Lista no disponible.</span>
                                </multiselect>
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                            <ValidationProvider name="cb_busqueda_corporativo" rules="required" v-slot="{ errors }" class="required">
                                <label class="typo__label">Producto</label>
                                <vx-input-group class="">
                                    <vs-input id="busquedaProducto" v-model="producto_buscar" @keyup.enter="cargar_producto()" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                    <template slot="append">
                                        <div class="append-text btn-addon">
                                            <button type="submit" v-show="false" name="button"></button>
                                            <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="cargar_producto()" icon="fa-search"></vs-button>
                                        </div>
                                    </template>
                                </vx-input-group>
                            </ValidationProvider>
                            <!---<vs-input label="Producto" type="text" class="w-full" placeholder="Buscar" v-model="producto_buscar" @keyup.enter="cargar_producto" />-->
    
                        </div>
                    </div>
    
                    <!----- Detalle producto seleccionado--->
                    <div v-if="Producto_seleccionado.Codigo>0" class="w-full md:w-4/5 lg:w-4/5 xl:w-4/5">
                        <!---<div style="border:2px solid #1B5E20;border-radius:5px;padding:5px;font-size:12px;background-color:#A5D6A7">--->
                        <div style="border-radius:5px;padding:5px;font-size:12px;background-color:#A5D6A7">
                            Código: {{Producto_seleccionado.Codigo}}
                            <br>
                            Producto: {{Producto_seleccionado.marca_comercial}}
                            <br>
                            Presentación: {{Producto_seleccionado.Presentacion}}
                            <br>
    
                        </div>
                    </div>
    
                    <!-----  MAXIMOS Y MINIMOS  ------------->
                    <br>
                    <div class="flex flex-wrap">
                        <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                            <!-----------Bodeg asociar ---->
                            <ValidationProvider name="cb_busqueda_bodega" rules="required" v-slot="{ errors }" class="required">
                                <label class="typo__label">Bodegas</label>
                                <multiselect v-model="cb_Asocacion_Bodega" :options="Lista_bodegas" :multiple="true" :close-on-select="false" :clear-on-select="false" :preserve-search="true" placeholder="Seleccionar bodegas" label="NOMBRE" track-by="NOMBRE" :preselect-first="true" @input="onChangeAsociacionBodega" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null">
                                    <template slot="selection" slot-scope="{ values,  isOpen }"><span class="multiselect__single" v-if="values.length &amp;&amp; !isOpen">{{ values.length }} Bodegas seleccionadas</span></template>
                                    <span slot="noOptions">Lista no disponible.</span>
                                </multiselect>
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                            <ValidationProvider name="cant_maxima" rules="required|numero_entero|numero_min:0" v-slot="{ errors }" class="required">
                                <label class="typo__label">Cant. Mínima</label>
                                <vs-input class="w-full" type="number" count="100" v-model="Cant_minima" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                            <ValidationProvider name="cant_maxima" rules="required|numero_entero|numero_min:0" v-slot="{ errors }" class="required">
                                <label class="typo__label">Cant. Máxima</label>
                                <vs-input class="w-full" type="number" count="100" v-model="Cant_maxima" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                            </ValidationProvider>
                        </div>
                        <!------ BOTONES DE ACCION ---->
                        <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6">
                            <vs-button color="primary" style="float:left;margin: 20px" type="filled" icon-pack="fas" icon="fa-plus" @click="Confirmacion_Transaccion('N', '0')"> Agregar</vs-button>
                        </div>
    
                    </div>
                    <br>
                    <br><br>
                </div>
            </vs-popup>
            <!----------------------- FIN NUEVA ASOCIACIÓN---------->
    
            <!--------------- Resultado busqueda Producto-------------->
            <vs-popup classContent="popup-example" title="Resultado Búsqueda" :active.sync="Estado_VentanaEmergente_Busqueda" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
                <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
    
                    <form>
    
                        <vs-divider></vs-divider>
                        <vs-table2 max-items="10" pagination :data="producto" id="tb_departamentos">
    
                            <template slot="thead">
                                <th>Codigo</th>
                                <th>Descripcion</th>
                                <th>Marca</th>
                                <th>Concentración</th>
                                <th>Presentación</th>
                                <th></th>
                            </template>
    
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2 :data="data[indextr].Codigo">
                                        {{data[indextr].Codigo}}
                                    </vs-td2>
    
                                    <vs-td2 :data="data[indextr].Descripcion">
                                        {{data[indextr].Descripcion}}
                                    </vs-td2>
    
                                    <vs-td2 :data="data[indextr].Marca">
                                        {{data[indextr].Marca}}
                                    </vs-td2>
    
                                    <vs-td2 :data="data[indextr].Concentracion">
                                        {{data[indextr].Concentracion}}
                                    </vs-td2>
    
                                    <vs-td2 :data="data[indextr].PresentacionNombre">
                                        {{data[indextr].PresentacionNombre}}
                                    </vs-td2>
    
                                    <vs-td2 align="right">
                                        <vs-button color="primary" icon-pack="fas" icon="fa-check-circle" style="display:inline-block;margin-right:2px" @click="Seleccionar_Producto(data[indextr])"></vs-button>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </form>
    
                </div>
            </vs-popup>
        </div>
    
    </vx-card>
    </template>
    
    <script>
    import Multiselect from 'vue-multiselect'
    import "vue-multiselect/dist/vue-multiselect.min.css"
    export default {
        components: {
            Multiselect
        },
        data() {
            return {
                Ventana_EmergenteNuevo: false,
                cb_bodegas: '',
                Lista_bodegas: [],
                Id_Bodega_seleccionada: '',
    
                Lista_maxmin: [],
    
                Lista_seleccionada_bodegas: [],
                cb_Asocacion_Bodega: '',
    
                cb_clase: '',
                Id_clase_seleccionada: '',
                Lista_clase: [],
                producto_buscar: '',
                Producto_seleccionado: {
                    nombre: '',
                    marca_comercial: '',
                    Principio_activo: '',
                    Concentracion: '',
                    Presentacion: '',
                    Codigo: '',
                    codigo_interno: '',
                    Activo: 'N'
                },
                Estado_VentanaEmergente_Busqueda: false,
                producto: [],
                Cant_minima: 0,
                Cant_maxima: 0,
            };
        },
        mounted() {
            this.Consultar_Bodega('T', '00'); //Carga  la tabla inicial
            this.Consultar_MaxMinBodega('T', 0);
        },
        methods: {
            /***** FUNCIONES OPERACIONES - CAMBIOS ESTADOS**** */
            Bodega_seleccionada({
                NOMBRE
            }) {
                return `${NOMBRE} `
            },
            onChangeBodega(value) {
                if (value !== null && value.length !== 0) {
                    this.Id_Bodega_seleccionada = value.CODIGO;
                    this.Consultar_MaxMinBodega('E', this.Id_Bodega_seleccionada);
                } else {
                    this.Id_Bodega_seleccionada = '';
                    this.Consultar_MaxMinBodega('T', 0);
                }
    
            },
            Abrir_Ventana_Emergente_Nuevo() {
                this.Ventana_EmergenteNuevo = true;
                this.Consultar_Clase();
                this.Consultar_Bodega('T', '00'); //Carga  la tabla inicial
                this.Limpiar_CamposNuevo();
            },
            onChangeAsociacionBodega(value) {
                var array_bodegas = [];
                value.map(obj => {
                    array_bodegas.push({
                        "ID_BODEGA": obj.CODIGO
                    })
                })
                this.Lista_seleccionada_bodegas = JSON.stringify(array_bodegas);
    
            },
            Clase_seleccionada({
                Nombre
            }) {
                return `${Nombre} `
            },
            onChangeClase(value) {
                if (value !== null && value.length !== 0) {
                    this.Id_clase_seleccionada = value.Codigo;
                    document.getElementById("busquedaProducto").focus();
                } else {
                    this.Id_clase_seleccionada = '';
                }
            },
            Mostrar_resultado_producto(value) {
                if (value.length === 1) {
                    value.map(obj => {
                        this.Producto_seleccionado.Codigo = obj.Codigo;
                        this.Producto_seleccionado.marca_comercial = obj.Descripcion;
                        this.Producto_seleccionado.Concentracion = obj.Concentracion;
                        this.Producto_seleccionado.codigo_interno = obj.codigo_interno;
                        this.Producto_seleccionado.Presentacion = obj.PresentacionNombre;
    
                    })
    
                } else if (value.length > 1) {
                    this.Estado_VentanaEmergente_Busqueda = true;
                }
    
            },
            Limpiar_Campos() {
                this.Producto_seleccionado.Codigo = '';
                this.Producto_seleccionado.marca_comercial = '';
                this.Producto_seleccionado.Principio_activo = '';
                this.Producto_seleccionado.Concentracion = '';
                this.Producto_seleccionado.codigo_interno = '';
                this.Producto_seleccionado.Presentacion = '';
            },
            Seleccionar_Producto(value) {
                this.Producto_seleccionado.Codigo = value.Codigo;
                this.Producto_seleccionado.marca_comercial = value.Descripcion;
                this.Producto_seleccionado.Principio_activo = value.Principio_activo;
                this.Producto_seleccionado.Concentracion = value.Concentracion;
                this.Producto_seleccionado.codigo_interno = value.codigo_interno;
                this.Producto_seleccionado.Presentacion = value.PresentacionNombre;
    
                this.Estado_VentanaEmergente_Busqueda = false;
                this.producto = [];
            },
            Limpiar_CamposNuevo() {
                this.cb_clase = '';
                this.Id_clase_seleccionada = '';
                this.cb_Asocacion_Bodega = '';
                this.Lista_seleccionada_bodegas = [];
                this.Producto_seleccionado = [];
                this.producto_buscar = '';
                this.Cant_minima = 0;
                this.Cant_maxima = 0;
            },
            Validacion_Campos(Tipo, Nombre, valor, obligatorio, cant_maxima) {
                /**
                 * @General
                 * Tipo : N = Númerio, Nombre = Descripción del campo, Valor = A evaluar, Obligatorio= Verdadero o falso,cant_maxima  = Aplica unicamente para un string y define la cantidad de caracteres 
                 * Devuelve un mensaje al usuario si las validaciones no se cumple y aborta, de lo contrario devuelve verdadero
                 */
                if (Tipo == 'ID') {
                    if (valor <= 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else if (Tipo == 'N') {
    
                    if (valor < 0 || valor === "") {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else {
                    if (valor == '' && obligatorio == true) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
    
                        if (cant_maxima < valor.length) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: 'Campo ' + Nombre + ' no debe superar los ' + cant_maxima + ' carácteres.',
                            });
                            return false;
                        } else {
                            return true;
                        }
    
                    }
                }
            },
            /**** CONSULTAS API *****/
            Consultar_Bodega(Operacion, id_departamento) {
                const sesion = this.$store.state.sesion;
                const url = this.$store.state.global.url
    
                this.axios.post(url + 'app/v1_OrdenCompra/consulta_bodega', {
                        operacion: Operacion,
                        id_empresa: sesion.sesion_empresa,
                        id_departamento: id_departamento
                    })
                    .then(resp => {
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Bodegas',
                                text: resp.data.mensaje,
                            })
                            this.Lista_bodegas = "";
                        } else {
                            this.Lista_bodegas = resp.data.json;
                        }
                    })
                    .catch(() => {})
            },
            Consultar_MaxMinBodega(tipo, bodega) {
                if (bodega > 0) {
                    tipo = 'E'
                } else {
                    tipo = 'T';
                    bodega = 0;
                }
    
               // console.log("tipo: " + tipo + " bodega: " + bodega)
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/MaxMin_BodegasConsulta', {
                        tipo: tipo,
                        bodega: bodega
                    })
                    .then(resp => {
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Máximos y Mínimos',
                                text: resp.data.mensaje,
                            })
    
                            this.Lista_maxmin = "";
                        } else {
                            this.Lista_maxmin = resp.data.json;
                        }
                    })
                    .catch(() => {})
            },
            Consultar_Clase() {
                const url = this.$store.state.global.url
    
                this.axios.post(url + 'app/inventario/invClaselist', {
    
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Máximos y Mínimos',
                                text: resp.data.mensaje,
                            })
                            this.Lista_clase = "";
                        } else {
                            this.Lista_clase = resp.data.json;
                        }
    
                    })
                    .catch(() => {})
    
            },
            cargar_producto: function () {
    
                if (this.Id_clase_seleccionada > 0) {
    
                    this.Limpiar_Campos();
                    this.$vs.loading();
                    this.axios.post('/app/inventario/invProductolist', {
                            Pagina: 1,
                            Busqueda: '',
                            Clase: this.Id_clase_seleccionada,
                            SubClase: 0,
                            accion: 'A1',
                            Nombre: this.producto_buscar.trim(),
                        })
                        .then(resp => {
    
                            this.$vs.loading.close();
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.producto = []
                                resp.data.json.map(data => {
                                    this.producto.push({
                                        ...data
                                    })
                                })
    
                                this.Mostrar_resultado_producto(this.producto);
    
                            } else {
                                this.$vs.loading.close();
                                this.producto = []
                                this.$vs.notify({
                                    color: 'danger',
                                    title: 'Producto',
                                    text: (resp.data.codigo < 0) ? resp.data.descripcion : 'Sin Registros',
                                })
    
                            }
    
                        })
                        .catch(() => {
                            this.$vs.loading.close();
    
                        })
                    this.$vs.loading.close();
                } else {
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Producto',
                        text: 'Seleciconar clase.',
                    })
                }
            },
    
            /**** GUARDAR, ACTUALIZAR, BAJA API ****/
            Confirmacion_Transaccion(Operacion, CodigoAsociacion) {
    
                if (Operacion === 'N') {
    
                    this.res_variables = this.Validacion_Campos('ID', 'Clase', this.Id_clase_seleccionada, true, 0);
                    var cantidad = Object.keys(this.Producto_seleccionado).length;
    
                    if (cantidad <= 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Seleccionar un producto.',
                        });
                        return;
                    }
    
                    cantidad = '';
                    cantidad = Object.keys(this.Lista_seleccionada_bodegas).length;
                    if (cantidad <= 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Seleccionar Bodega(s).',
                        });
                        return;
                    }
                    if (this.res_variables)
                        this.res_variables = this.Validacion_Campos('N', 'Mínimo', this.Cant_minima, true, 0);
    
                    if (this.res_variables)
                        this.res_variables = this.Validacion_Campos('N', 'Máximo', this.Cant_maxima, true, 0);
                }
                // Guarda / Actualizar datos
    
                if (this.res_variables) {
                    //const sesion = this.$store.state.sesion;
                    this.axios.post('/app/v1_OrdenCompra/MaxMin_Bodegas', {
                            codigo: CodigoAsociacion,
                            empresa: this.sesion.sesion_empresa,
                            operacion: Operacion,
                            corporativo: this.sesion.corporativo,
                            producto: this.Producto_seleccionado.Codigo,
                            json_datos: this.Lista_seleccionada_bodegas,
                            minimo: this.Cant_minima,
                            maximo: this.Cant_maxima
                        })
                        .then(resp => {
    
                            if (resp.data.codigo == 0) {
                                this.Consultar_MaxMinBodega('T', 0);
                                this.Producto_seleccionado = [];
                                this.producto_buscar = '';
                                this.Cant_minima = 0;
                                this.Cant_maxima = 0;
    
                                document.getElementById("busquedaProducto").focus();
    
                            }
                        })
    
                }
    
            },
            Eliminar_Registro(value) {
                /**
                 * @General
                 * Función eliminar registro;
                 */
    
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',
                    button: {
                        yes: 'Aceptar',
                        no: 'Cancelar'
    
                    },
                    text: '¿Dar de baja Asignación  \'' + value.DESCRIPCION_BODEGA + '\' -  \'' + value.DESCRIPCION_PRODUCTO + '\' ? ',
                    accept: () => {
                        this.axios.post('/app/v1_OrdenCompra/MaxMin_Bodegas', {
                                codigo: value.IDMAXMINBODEGAS,
                                empresa: this.sesion.sesion_empresa,
                                operacion: 'B',
                                corporativo: this.sesion.corporativo,
                                producto: value.ID_PRODUCTO,
                                json_datos: '',
                                minimo: this.Cant_minima,
                                maximo: this.Cant_maxima
                            })
                            .then(resp => {
                                if (resp.data.codigo == 0) {
                                    this.Consultar_MaxMinBodega('T', 0);
                                }
                            })
    
                    }
                })
    
            },
    
        },
        computed: {
            sesion() {
                return this.$store.state.sesion
            }
        },
    }
    </script>
    