<template>
<vs-row class="p-2 w-full">
    <vs-col vs-w="3" vs-justify="center" vs-align="center" class="pr-2">
        <vx-card :title="textoFiltro">
            <!-- Se dejó toda la configuración, ya que si se usa la configuración global, se reinicia el pageIndex cada vez que se selecciona una fecha -->
            <DxDataGrid :ref="dataGridFechaKey" v-bind="DefaultDxGridConfiguration" :headerFilter="{visible:true, allowSearch:true}" :data-source="fechas" :width="'100%'" :height="'100%'" @selection-changed="onSelectionChanged" @cell-prepared="onCellPrepared">
                <DxToolbar>
                    <DxItem name="searchPanel" />
                    <DxItem location="after" template="opcionesTemplate" />
                </DxToolbar>
                <DxSelection mode="single" />
                <DxColumn :width="120" data-field="Fecha" caption="Fecha" format="dd/MM/yyyy HH:mm" data-type="date" alignment="center" />
                <DxColumn :width="100" data-field="Examen" :allowHeaderFiltering="false" caption="Exámen" data-type="string" alignment="center" />
                <template #opcionesTemplate>
                    <ExpedienteGridToolBar v-bind="$props" :visible="ShouldRender" :pdfExportItems="[{text:'Exámenes', reportName: 'Informes por Orden'}]" @refresh="refresh" :showItems="['refresh']"/>
                </template>
            </DxDataGrid>
        </vx-card>
    </vs-col>
    <vs-col vs-justify="center" vs-align="center" vs-w="9">
        <vx-card :title="textoImagenes" id="pdfViewer">
            <vs-row vs-justify="flex-end" vs-align="center" class="pb-2">
                <DxButton :visible="showImage" id="botonBarra" type="default" styling-mode="outlined" hint="Ver imagenes" @click="openImage">
                    <font-awesome-icon :icon="['fas', 'image']" style="font-size: 18px;" />
                </DxButton>
                <DxButton :visible="showImage" id="botonBarra" type="default" styling-mode="outlined" hint="Ver imagenes DICOM" @click="openDICOM">
                    <font-awesome-icon :icon="['fas', 'panorama']" style="font-size: 18px;" />
                </DxButton>
            </vs-row>
            <div width="100%" style="height: 600px">
                <object v-if="archivoPDF!=''" type="application/pdf" :data="archivoPDF" ref="pdfDocument" width="100%" height="98%">
                    <p>Su navegador es incompatible con el plugin pdf.</p>
                </object>
            </div>
        </vx-card>
    </vs-col>
</vs-row>
</template>

<script>
import {
    DxDataGrid,
    DxColumn,
    DxSelection,
    DxToolbar,
    DxItem,
} from 'devextreme-vue/data-grid'
import CustomStore from 'devextreme/data/custom_store'
import ExpedienteGridToolBar from './ExpedienteGridToolBar.vue'
import {
    DefaultDxGridConfiguration
} from './data'
import io from 'socket.io-client'
import DxButton from 'devextreme-vue/button'

const dataGridRefKey = 'gridExamen'
const dataGridFechaKey = 'gridFiltro'

export default {
    name: 'Radiologia',
    components: {
        DxDataGrid,
        DxColumn,
        ExpedienteGridToolBar,
        DxSelection,
        DxButton,
        DxToolbar,
        DxItem,
    },
    data() {
        return {
            //Listado de signos asociados a la admisión
            examenes: [],
            examenesDataSource: new CustomStore({
                key: "id",
                load: () => {
                    return this.laboratorios;
                },
            }),
            fechas: [], //Listado de examenes para filtrar laboratorios
            botones: [], //Vector para manejar los botones que se muestran sobre la tabla
            reporteProps: [],
            textoFiltro: 'Filtrar laboratorio',
            textoImagenes: '',
            archivoPDF: '',
            showImage: false,
            showDICOM: false,
            noDataText: 'Seleccione una fecha para cargar información',
            dataGridRefKey,
            dataGridFechaKey,
            DefaultDxGridConfiguration,
            IpLocal: '',
            socket: io('https://sighos-linea.hospitaleslapaz.com/'),
            ComandoWindows: '',
            estado: false,
            tipoOrdenSeleccionado: null,
            numeroOrdenSeleccianado: null
        }
    },
    props: {
        SerieAdmision: null,
        CodigoAdmision: null,
        NumeroExpediente: null,

        StatusAdmision: null,
    },
    methods: {
        submit() {
            this.dataGrid.saveEditData()
        },
        Cargar(tipo = '', orden = '', linea = '') {
            if (this.SerieAdmision && this.CodigoAdmision)
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaResultadosLaboratorios', {
                    TipoOrden: tipo,
                    NoOrden: orden,
                    LineaOrden: linea
                })
                .then(resp => {
                    if (linea === '-1') {
                        this.examenes = [];
                        this.noDataText = '****** LISTADO DE ESTUDIOS PENDIENTES DE REALIZAR ******'
                        resp.data.json.map((x) => {
                            this.noDataText += "\n" + x.EstudiosPendientes
                        })
                    } else {
                        this.examenes = resp.data.json.map((x, index) => {
                            return {
                                id: index,
                                Prueba: x.Prueba,
                                Resultado: x.R,
                                FechaValidacion: x.FRealizacion,
                                VEquipo: x.VEquipo,
                                ValorNormal: x.VALORNORMAL,
                                ValorRn: x.VALORNORMALRN,
                                ValorMenor: x.VALORNORMALMENOR,
                                ValorMasculino: x.VALORNORMALMASC,
                                ValorFemenino: x.VALORNORMALFEM,
                                LaboratorioTexto: x.LaboratorioTexto,
                                EstudiosPendientes: x.EstudiosPendientes
                            }
                        })
                    }
                    this.examenesDataSource.load().then(
                        () => {
                            this.refreshDataGrid();
                        },
                        (error) => {
                            //console.error('error loading data customstore' + JSON.stringify(error))
                        }
                    )
                })
            else {
                this.examenes = [];
                this.refreshDataGrid();
                this.refreshFechaDataGrid();
            }
        },
        getFechasLaboratorio() {
            if (this.SerieAdmision && this.CodigoAdmision)
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaListadoRadiologias', {
                    Serie: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                })
                .then(resp => {
                    this.fechas = resp.data.json.map((x, index) => {
                        return {
                            id: index,
                            Fecha: this.formatFechaFiltro(x.FechaRegistro),
                            tipoorden: x.TipoOrden,
                            orden: x.Orden,
                            linea: x.Lineaorden,
                            Examen: x.Nombre,
                            Codigo: x.CodProducto,
                            Imagen: x.ImagenesUp
                        }
                    })
                    this.dataGridFecha.columnOption('Fecha', 'sortOrder', 'desc')
                })
            else {
                this.fechas = [];
                this.refreshFechaDataGrid();
            }
        },
        refreshDataGrid() {
            this.dataGrid.refresh()
                .then(function () {

                })
                .catch(function (error) {
                    //console.error('error refreshing: ' + error)
                });
            this.dataGrid.clearSelection();
            this.dataGrid.clearSorting();
            this.dataGrid.pageIndex(0);
        },
        refreshFechaDataGrid() {
            this.dataGridFecha.pageIndex(0);
            this.dataGridFecha.clearSelection();
            this.dataGridFecha.clearSorting();
            this.dataGridFecha.columnOption('Fecha', 'sortOrder', 'desc')
            this.botones = [];
        },
        formatFecha(date) {
            const year = new Date(date).toLocaleString('default', {
                year: 'numeric'
            });
            const month = new Date(date).toLocaleString('default', {
                month: '2-digit'
            });
            const day = new Date(date).toLocaleString('default', {
                day: '2-digit'
            });

            return [year, month, day].join('');
        },
        formatFechaFiltro(fecha) {
            fecha = fecha.split('/');
            const year = fecha[2];
            const month = fecha[1];
            const day = fecha[0];

            return [month, day, year].join('/');
        },
        //Método para detectar cuando se selecciona una fila de la tabla de filtrado de fecha
        onSelectionChanged(e) {
            if (e.selectedRowsData.length > 0) {
                this.showImage = false
                this.reporteProps = e.selectedRowsData[0];
                this.botones = ['refresh'];
                this.textoFiltro = 'Orden ' + this.reporteProps.tipoorden + ' ' + this.reporteProps.orden
                this.showImage = Boolean(this.reporteProps.Imagen)
                this.textoImagenes = this.showImage ? 'El estudio posee imagenes para mostrar' : 'El estudio no posee imagenes para mostrar'
                this.tipoOrdenSeleccionado = this.reporteProps.tipoorden
                this.numeroOrdenSeleccianado = this.reporteProps.orden
                this.getPDF();
                this.getPathImagenes(this.reporteProps.tipoorden, this.reporteProps.orden);
            }
        },
        refresh() {
            this.examenes = [];
            this.textoFiltro = 'Filtrar laboratorio'
            this.textoImagenes = ''
            this.noDataText = 'Seleccione una fecha para cargar información'
            this.showImage = false
            this.archivoPDF = ''
            this.refreshFechaDataGrid();
        },
        onCellPrepared(e) {
            if (e.rowType === 'data' && e.column.dataField === 'Examen') {
                if (e.data.LineaExamen === '-1') {
                    e.cellElement.style.cssText = "color: red; text-align: center;"
                }
            }
        },
        openImage() {
            this.$socke_up(this.ComandoWindows)
        },
        openDICOM() {
            window.open('http://srvpacs032en/Synapse/WebQuery/Index?path=/All%20Studies/&filter=accessionnumber=' + this.tipoOrdenSeleccionado + '-' + this.numeroOrdenSeleccianado, '_blank')
        },
        getPathImagenes(tipo, orden) {
            this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaPathImagenes', {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    TipoOrden: tipo,
                    Orden: orden
                })
                .then(resp => {
                    let v = resp.data.json.map((x, index) => {
                        return {
                            id: index,
                            Aplicacion: x.Aplicacion,
                            Path: x.Path
                        }
                    })
                    this.ComandoWindows = v[0].Aplicacion
                })
        },
        getPDF() {
            this.axios.post("/app/reporte/ReporteGenerador", {
                    Nombre: 'Informes por Orden',
                    Opciones: {
                        tiporeporte: "application/pdf",
                        ...this.reporteProps
                    }
                }, {
                    responseType: 'arraybuffer'
                })
                .then(resp => {
                    this.archivoPDF = 'data:application/pdf;base64,' + Buffer.from(resp.data, 'binary').toString('base64') + "#view=FitH";
                })
        },
    },
    mounted() {
        this.IpLocal = localStorage.getItem('IpLocal');
        this.getFechasLaboratorio();
    },
    watch: {
        'NumeroExpediente'(newval) {
            if (newval != undefined)
                this.getFechasLaboratorio();
        },
    },
    computed: {
        ShouldRender() {
            return Boolean(this.SerieAdmision) && Boolean(this.CodigoAdmision) && Boolean(this.NumeroExpediente)
        },

        dataGrid: function () {
            return this.$refs[dataGridRefKey].instance;
        },

        dataGridFecha: function () {
            return this.$refs[dataGridFechaKey].instance;
        }
    },
}
</script>

<style>
#pdfViewer .dx-button-content {
    height: 50px;
    width: 50px;
}

#pdfViewer .vx-card__body {
    padding-top: 0 !important;
}
</style>
