<template>
  <div id="formaplicacionMedicamentos" class="val003-container">
    

    <div id="gdaplicacion">
      <P style="font-size: 15px; color: #0693e3">
          <strong>{{ "Paciente: " }}</strong>{{ this.Paciente }}
          <strong>{{ "    Admisión: " }}</strong> {{ this.SerieAdmision+'-'+CodigoAdmision }}  
    </P>
      <DxDataGrid
        id="gridAplicacion"
        :show-borders="true"
        :dataSource="dataFiltrada"
        @cell-click="CambioEstado"
        :focused-row-enabled="true"
        key-expr="ID"
        :repaint-changes-only="true"
        v-bind="DefaultDxGridConfiguration"
         height="auto"
            width="auto"
      >
        <DxEditing :allow-updating="true" mode="cell" />

        <DxColumn
          data-field="Medicamento"
          caption="Nombre producto"
          width="400"
          :allowEditing="false"
        />

        <DxColumn
          data-field="FechaAsignada"
          caption="Fecha asignada"
          width="75"
          :allowEditing="false"
        />

        <DxColumn data-field="Hora" caption="Hora" width="75" :allowEditing="false" />

        <DxColumn data-field="Aplicado" caption="Aplicado" width="75" />
        <DxColumn data-field="Omitido" caption="Omitido" width="75" />
        <DxColumn data-field="Desechado" caption="Desechado" width="75" />

        <DxColumn data-field="Rehusado" caption="Rehusado" width="75" />

        <DxColumn data-field="Suspendido" caption="Suspendido" width="75" />

        <DxColumn
          data-field="MotivoOmision"
          caption="Motivo no aplicación"
          width="320"
        >
          <DxLookup
            :data-source="MotivosAnulacion"
            display-expr="Descripcion"
            value-expr="Id"
          />
        </DxColumn>
        <DxColumn data-field="NoOrden" caption="No. Orden" width="115" :allowEditing="false" />
        <DxColumn data-field="Tipo" caption="Tipo" width="75"  :allowEditing="false"/>
        <DxColumn data-field="ColaboradorSolicita" caption="Colaborador Solicita" width="250"  :allowEditing="false"/>
        <DxColumn data-field="ColaboradorRecibe" caption="Colaborador Recibe" width="250"  :allowEditing="false"/>
       
       
        <DxFilterPanel :visible="true"/>
          <DxSelection mode="single" />
          <DxToolbar>
            <DxItem name="groupPanel" />
            <DxItem name="searchPanel" width="400px" />
            <DxItem location="after" template="opcionesTemplate" />
          </DxToolbar> 
          <template #opcionesTemplate>
              <ExpedienteGridToolBar :StatusAdmision="StatusAdmision" :visible="true" :pdfExportItems="[{}]" @refresh="CargarMedicamentos"  :showItems="['refresh']">
                <DxButton id="botonBarra" icon="fas fa-thumbs-down" hint="omite la aplicación y ordenes de todos los medicamentos" text="Omitir todo" type="default" styling-mode="outlined"   @click="Omitir" />
                <DxButton id="botonBarra" icon="check" hint="Valida la acción de aplicación o no aplicación de medicamentos" text="Validar" type="default" styling-mode="outlined" @click="validarAplicacion"/>

                <label>
                  <input type="checkbox" v-model="aplicarFiltro"> Unidosis
                </label>
            </ExpedienteGridToolBar>
          </template>
          <DxGroupPanel :visible="true" />
          <DxGrouping :auto-expand-all="false"/>
      </DxDataGrid>
    </div>
  </div>
</template>

<script>
import {
  DxDataGrid,
  DxLookup,
  DxColumn,
  DxEditing,
  DxSearchPanel, 
     DxToolbar,
     DxItem,
     DxSelection,
     DxFilterPanel,
     DxGroupPanel,
     DxGrouping,
} from "devextreme-vue/data-grid";
import { DefaultDxGridConfiguration } from "../../EXPEDIENTE/data";
import ExpedienteGridToolBar from './../../EXPEDIENTE/ExpedienteGridToolBar.vue'
import DxButton from 'devextreme-vue/button'
export default {
  components: {
    DxDataGrid,
    DxLookup,
    DxColumn,
    DxEditing,
    ExpedienteGridToolBar,
    DxSearchPanel, 
     DxToolbar,
     DxItem,
     DxSelection,
     DxButton,
     DxFilterPanel,
     DxGroupPanel,
     DxGrouping,
  },
  data() {
    return {
      AplicacionMedicamentos: [],
      MotivosAnulacion: [],
      DefaultDxGridConfiguration,
      FiltroGrid:null,
      aplicarFiltro: false,
      nuevosDatos: [
        {
          id: 0,
          ID: 0,
          Medicamento: "",
          FechaAsignada: "",
          Hora: "",
          Aplicado: "",
          Omitido: "",
          Desechado: "",
          Suspendido: "",
          MotivoOmision: "",
        },
      ],
      SerieAdmision: null,
      CodigoAdmision: null,
      StatusAdmision:null,
      Paciente:null,
      valorColumna: "", // Inicialmente vacío
      nombreColumna: "", // Inicialmente vacío
      editing: { allowUpdating: true },
    };
  },

  methods: {
    CargarMedicamentos() {
      this.axios
        .post("/app/v1_ValeElectronico/AplicacionMedicamentos", {
          SerieAdmision: this.SerieAdmision,
          Admision: this.CodigoAdmision,
        })
        .then((resp) => {
          this.AplicacionMedicamentos = resp.data.json.map((x, index) => {
            return {
              id: index,
              ID: x.ID,
              Medicamento: x.Medicamento,
              FechaAsignada: x.FechaAsignada,
              Hora: x.Hora,
              Aplicado: x.Aplicado,
              Omitido: x.Omitido,
              Desechado: x.Desechado,
              Rehusado: x.Rehusado,
              Suspendido: x.Suspendido,
              MotivoOmision: x.MotivoOmision,
              NoOrden:x.NoOrden,
              Tipo:x.Tipo,
              RecibidoEnfermeria:x.RecibidoEnfermeria,
              ColaboradorSolicita:x.ColaboradorSolicita,
              ColaboradorRecibe:x.ColaboradorRecibe
            };
          });
          this.mensajeActualizacion();
        });
    },

    CambioEstado(e) {
    // 
    
    const { column, data } = e; // Desestructuración de las propiedades

      // Cambiamos los estados según la columna
      switch (column.dataField) {
        case "Aplicado":
          data.Aplicado = data.Aplicado === "SI" ? "NO" : "SI";
          e.data.Suspendido = "NO";
          e.data.Omitido = "NO";
          e.data.Desechado = "NO";
          e.data.Rehusado = "NO";
          break;
        case "Suspendido":
          data.Suspendido = data.Suspendido === "SI" ? "NO" : "SI";
          e.data.Aplicado = "NO";
          e.data.Omitido = "NO";
          e.data.Desechado = "NO";
          e.data.Rehusado = "NO";
          break;
        case "Omitido":
          data.Omitido = data.Omitido === "SI" ? "NO" : "SI";
          e.data.Suspendido = "NO";
          e.data.Aplicado = "NO";
          e.data.Desechado = "NO";
          e.data.Rehusado = "NO";
          break;
        case "Desechado":
          data.Desechado = data.Desechado === "SI" ? "NO" : "SI";
          e.data.Suspendido = "NO";
          e.data.Omitido = "NO";
          e.data.Aplicado = "NO";
          e.data.Rehusado = "NO";
          break;
        case "Rehusado":
          data.Rehusado = data.Rehusado === "SI" ? "NO" : "SI";
          e.data.Suspendido = "NO";
          e.data.Omitido = "NO";
          e.data.Desechado = "NO";
          e.data.Aplicado = "NO";
          break;
        default:
          break;
      }
    },

    filtrarCambios(datos) {
      // Filtramos los registros que han cambiado en al menos una columna
      return datos.filter((registro) => {
        return (
          registro.Aplicado !== "NO" ||
          registro.Suspendido !== "NO" ||
          registro.Omitido !== "NO" ||
          registro.Desechado !== "NO" ||
          registro.Rehusado !== "NO"
        );
      });
    },

    filtrarCambiosSinDescripcion(datos) {
      // Filtramos los registros que no han llenado motivo de desecho u omision
      return datos.filter((registro) => {
        return (
          (registro.Suspendido === "SI" && registro.MotivoOmision === "") ||
          (registro.Omitido === "SI" && registro.MotivoOmision === "") ||
          (registro.Desechado === "SI" && registro.MotivoOmision === "") ||
          (registro.Rehusado === "SI" && registro.MotivoOmision === "")
        );
      });
    },


    validarAplicacion() {
      const registrosModificados = this.filtrarCambios(
        this.AplicacionMedicamentos
      );
      const sinDescripcion = this.filtrarCambiosSinDescripcion(
        this.AplicacionMedicamentos
      );

      if (sinDescripcion.length > 0) {
        this.$vs.notify({
          time: 4000,
          title: "Falta definir motivo de no aplicación del medicamento",
          text: "favor llene el motivo por el cual el medicamento no fue aplicdo",
          iconPack: "feather",
          icon: "icon-alert-circle",
          color: "warning",
          position: "top-center",
        });
        return;
      }

      if (registrosModificados.length === 0) {
        this.$vs.notify({
          time: 4000,
          title: "No hay productos para validar",
          text: "Debe cambiar el estado de productos para validar",
          iconPack: "feather",
          icon: "icon-alert-circle",
          color: "warning",
          position: "top-center",
        });
        return;
      }
      this.axios
        .post("/app/v1_ValeElectronico/SeguimientoMedicamentos", {
          xml: registrosModificados,
        })
        .then((resp) => {
          if (resp.data.codigo == 0) {
            this.registrosModificados = [];

            this.$emit("onSaved");

            this.CargarMedicamentos();
          }
        });
    },
    Omitir(){
      this.axios
        .post("/app/v1_ValeElectronico/OmitirOrdenes", {
          SerieAdmision: this.SerieAdmision,
          Admision: this.CodigoAdmision,
        })
        .then((resp) => {
          if (resp.data.codigo == 0) {
            this.$emit("onSaved");

            this.CargarMedicamentos();
          }
        });

    },
    mensajeActualizacion(){
      this.$vs.notify({
          time: 4000,
          title: "Información actualizada",
          text: "se actualizo la información",
          iconPack: "feather",
          icon: "icon-alert-circle",
          color: "success",
          position: "bottom-center",
        });
    },
  },
  mounted() {
    this.axios
      .post("/app/v1_ValeElectronico/CatalogosVale", {
        Catalogo: "MotivosAnulacion",
      })
      .then((resp) => {
        this.MotivosAnulacion = resp.data;
      });
  },

  computed: {
    From: function () {
      return this.$refs["refForm"].instance;
    },
    dataFiltrada() {
      if (!this.aplicarFiltro) {
        return this.AplicacionMedicamentos; // Sin filtro
      }
      // filtro para mostrar la logiga de lo solicitado por unidosis
     return this.AplicacionMedicamentos.filter((registro) =>registro.RecibidoEnfermeria === 1)
    },
  },
};
</script>
