<template>
    <div>
        <vx-card title="Consulta de Devoluciones">
            <buscador ref="BuscarProveedorConsignacion" buscador_titulo="Buscador / Proveedores" :api="'app/v1_OrdenCompra/ConsultaProveedorConsignacion'" 
            :campos="['Proveedor', 'Nombre', 'Nit', 'Direccion', 'Telefonos']"
            :titulos="['Proveedor', '#Nombre', '#Nit', '#Direccion', '#Telefonos']"
            :api_filtro="{'Proveedor':Proveedor.Proveedor}"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" :api_preload="true" />

            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="5">
                    <div class="container">
                        <label class="typo__label">Proveedor</label>
                        <vs-divider></vs-divider>
                        <div class="div-container">
                            <vs-row vs-align="center">
                                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                    <vs-input ref="ProveedorConsignacion" v-model="Proveedor.Proveedor" @keyup.enter="Proveedores()" @keydown.tab="Proveedores()" ></vs-input>
                                    <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="Proveedores()" icon="fa-search"></vs-button>
                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                    <label class="typo__label">&nbsp;Nit:&nbsp;</label>
                                    <h5>{{ Proveedor.Nit }}</h5>
                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                    <label class="typo__label">Telefono:&nbsp;</label>
                                    <h5>{{ Proveedor.Telefonos }}</h5>
                                </vs-col>
                            </vs-row>
                            <br>
                            <vs-row vs-align="center">
                                <vs-col vs-type="flex" vs-align="center" vs-w="12">
                                    <label class="typo__label">Nombre:&nbsp;</label>
                                    <h5>{{ Proveedor.Nombre }}</h5>
                                </vs-col>
                            </vs-row>
                            <br>
                            <vs-row vs-align="center">
                                <vs-col vs-type="flex" vs-align="center" vs-w="12">
                                    <label class="typo__label">Dirección:&nbsp;</label>
                                    <h5>{{ Proveedor.Direccion }}</h5>
                                </vs-col>
                            </vs-row>
                        </div>
                    </div>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                    <div class="container">
                        <label class="typo__label">Periodo</label>
                        <vs-divider></vs-divider>
                        <div class="div-container div-periodo">
                            <vs-row vs-align="center">
                                <vs-col vs-type="flex" vs-align="center" vs-w="6">
                                    <label class="typo__label">Del:&nbsp;</label>
                                    <vs-input type="date" v-model="FechaDel"></vs-input>
                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="6">
                                    <label class="typo__label">Al:&nbsp;</label>
                                    <vs-input type="date" v-model="FechaAl"></vs-input>
                                </vs-col>
                            </vs-row>
                        </div>
                    </div>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="3">
                    <div class="container">
                        <label class="typo__label">Estado</label>
                        <vs-divider></vs-divider>
                        <div class="div-container div-estado">
                            <vs-row vs-align="center">
                                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                    <vs-radio v-model="Estado" vs-name="radios1" vs-value="0">Anuladas</vs-radio>
                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                    <vs-radio v-model="Estado" vs-name="radios1" vs-value="1">Realizadas</vs-radio>
                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                    <vs-radio v-model="Estado" vs-name="radios1" vs-value="2">Todas</vs-radio>
                                </vs-col>
                            </vs-row>
                        </div>
                    </div>
                </vs-col>
            </vs-row>
            <br>
            <vs-divider></vs-divider>
            <br>
            <vs-row>
                <vs-col vs-type="flex" vs-align="center" vs-justify="center" vs-w="12">
                    <vs-table2 class="w-full" max-items="10" tooltip pagination :data="DetalleDevolucion">
                        <template slot="thead">
                            <th>Codigo</th>
                            <th>Fecha</th>
                            <th>Proveedor</th>
                            <th>Documento</th>
                            <th>Validacion</th>
                            <th>Usuario</th>
                            <th>Observacion</th>
                            <th>BodegaFuente</th>
                            <th>EmpresaReal</th>
                            <th>Exento</th>
                            <th>Recibe</th>
                            <th>Nombre</th>
                            <th>Nit</th>
                            <th>NombreBodega</th>
                            <th>Responsable</th>
                            <th>Estado</th>
                            <th>Ver Devolución</th>
                            <th>Anular Devolución</th>
                        </template>
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 noTooltip>
                                    {{ tr.Codigo }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ tr.Fecha }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.Proveedor }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ tr.Documento }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ tr.Validacion }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ tr.Usuario }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ tr.Observacion }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ tr.BodegaFuente }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ tr.EmpresaReal }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ tr.Exento }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ tr.Recibe }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ tr.Nombre }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ tr.Nit }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ tr.NombreBodega }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ tr.Responsable }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    {{ tr.Estado }}
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    <vs-button color="warning" icon-pack="fas" icon="fa-file-invoice" @click="VerDevolucion(tr)"  class="mr-1" style="display:inline-block">
                                    </vs-button>
                                </vs-td2>
                                <vs-td2 noTooltip>
                                    <vs-button v-if="PermisoAnular" color="danger" icon-pack="fas" icon="fa-rectangle-xmark" @click="AnularDevolucion(tr)" class="mr-1" style="display:inline-block">
                                    </vs-button>
                                    <vs-button v-else color="#B2BABB" icon-pack="fas" icon="fa-rectangle-xmark" class="mr-1" style="display:inline-block">
                                    </vs-button>
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>

                </vs-col>
            </vs-row>
            <vs-row vs-type="flex" vs-align="center" vs-w="12" vs-justify="flex-end">
                <vs-col vs-type="flex" vs-align="center" vs-w="1">
                    <vs-button color="warning" icon-pack="fa" icon="fa-file-excel" @click="GenerarExcel()">Excel</vs-button>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="6">
                    <vs-button color="success" icon-pack="fa" icon="fa-check-circle" @click="Generar()">Generar</vs-button>
                </vs-col>
            </vs-row>
        </vx-card>
    </div>
</template>
<script>
    export default {
        data(){
            return {
                Proveedor: {
                    Proveedor: '',
                    Nombre: '',
                    Nit: '',
                    Telefonos: '',
                    Direccion: ''
                },
                FechaDel: '',
                FechaAl: '',
                Estado: 1,
                DetalleDevolucion: [{
                    Codigo: '',
                    Fecha: '',
                    Proveedor: '',
                    Documento: '',
                    Validacion: '',
                    Usuario: '',
                    Observacion: '',
                    BodegaFuente: '',
                    EmpresaReal: '',
                    Exento: '',
                    Recibe: '',
                    Nombre: '',
                    Nit: '',
                    NombreBodega: '',
                    Responsable: '',
                    Estado: ''
                }],
                PermisoAnular: false
            }
        },
        methods: {
            Proveedores(){
                if(this.Proveedor.Proveedor != ''){
                    this.axios.post('/app/v1_OrdenCompra/ConsultaProveedorConsignacion', {
                        Proveedor: this.Proveedor.Proveedor
                    })
                    .then(resp => {
                        if(resp.data.codigo == 0){
                            if(resp.data.json.length == 1){
                                this.Proveedor = resp.data.json[0]
                            }else{
                                this.$refs.BuscarProveedorConsignacion.iniciar((data) =>{
                                    if(data != null){
                                        this.Proveedor = data
                                    }
                                })
                            }
                        }
                    })
                }else{
                    this.$refs.BuscarProveedorConsignacion.iniciar((data) =>{
                        if(data != null){
                            this.Proveedor = data
                        }
                    })
                }
            },
            Generar(){
                this.axios.post('/app/v1_OrdenCompra/ConsultaDevolucion', {
                    FechaInicio: this.FechaDel,
                    FechaFin: this.FechaAl,
                    Estado: this.Estado,
                    Proveedor: this.Proveedor.Proveedor
                })
                .then(resp => {
                    if(resp.data.codigo == '0'){
                        this.DetalleDevolucion = resp.data.json
                    }else{
                        this.DetalleDevolucion = []
                    }
                })
            },
            AnularDevolucion(datos){
                if(datos.Estado == 'Anulado'){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'No se puede anular...'
                    })
                    return
                }
                this.axios.post('/app/v1_OrdenCompra/AnulacionDevolucion', {
                    Proveedor: datos.Proveedor,
                    NoDevolucion: datos.Codigo,
                    NoMovimientos: datos.Validacion
                })
                .then(() => {
                    this.Limpiar()
                })
            },
            VerDevolucion(datos){
                this.$reporte_modal({
                    Nombre: "Devolucion Consignacion",
                    Opciones: {
                        Devolucion: datos.Codigo,
                        Proveedor: datos.Proveedor
                    },
                    Formato: "PDF"
                })
            },
            Limpiar(){
                this.DetalleDevolucion = []
                this.Proveedor = {}
                this.FechaDel = ''
                this.FechaAl = ''
                this.Estado = 1
            },
            GenerarExcel(){
                this.$reporte_modal({
                    Nombre: "Devolucion Consignacion Excel",
                    Opciones: {
                        FechaInicial: this.FechaDel,
                        FechaFinal: this.FechaAl,
                        Proveedor: this.Proveedor.Proveedor,
                        Estado: this.Estado,
                        NombreProveedor: this.Proveedor.Nombre
                    },
                    Formato: "EXCEL"
                })
            }
        },
        mounted() {
            this.PermisoAnular = this.$validar_privilegio('ANULAR').status
            this.DetalleDevolucion = []
        },
    }
</script>
<style lang="scss" scooped>
    .container{
        border: 1px solid #888;

    }
    .div-container{
        padding: 5px;
        margin: 10px;
    }
    .div-periodo{
        padding-bottom: 48px;
        padding-top: 48px;
    }
    .div-estado{
        padding-bottom: 57px;
        padding-top: 57px;
    }
</style>