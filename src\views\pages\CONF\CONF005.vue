<template>
<vx-card title="Puestos" :class="[editar ? 'edicion' : '']">
    <vs-popup ref="buscador" title="Pago Productividad" :active.sync="show.PagaProductividad" style="z-index:9999" id="div-with-loading" class="vs-con-loading__container">
        <ValidationObserver v-if="show.PagaProductividad" ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
            <div class="flex flex-wrap">
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <SM-Buscar label="Pago Productividad Actual" v-model="info.PagaProductividad" :api="[{Codigo:'1',Nombre:'Activo'},{Codigo:'0',Nombre:'Inactivo'}]" :api_campos="['Nombre']" :api_titulos="['Nombre']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="false" />
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <!-- <ValidationProvider name="Nuevo Puesto" rules="" v-slot="{ errors }" class=""> -->
                    <SM-Buscar label="Pago Productividad" v-model="edit.PagaProductividad" :api="[{Codigo:'1',Nombre:'Activo'},{Codigo:'0',Nombre:'Inactivo'}]" :api_campos="['Nombre']" :api_titulos="['Nombre']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :disabled_texto="true" />
                    <!-- </ValidationProvider> -->
                </div>
            </div>
            <vs-divider></vs-divider>
            <div class="flex">
                <vs-spacer></vs-spacer>
                <vs-button :disabled="invalid" @click="handleSubmit(Guardar().GuardarPagaProductividad());">
                    Guardar
                </vs-button>
            </div>
        </ValidationObserver>
    </vs-popup>

    <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
        <form method="post" @submit.prevent="handleSubmit(Guardar().Guardar_Ajenos)">
            <div class="flex mb-5" style="justify-content: space-between;border: 1px solid rgba(0, 0, 0, 0.1);padding: 5px;">
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <SM-Buscar v-model="info.Id" label="Puesto" api="app/administracion/BusquedaCorporativosPuestos" :api_campos="['Puesto']" :api_titulos="['Puesto']" :api_filtro="{'Activo':'-1'}" api_campo_respuesta="Id" api_campo_respuesta_mostrar="Puesto" :api_preload="true" :disabled_search_input="nuevo" :callback_buscar="Consulta().ConsultaPuesto" :callback_nuevo="(permisos.nuevo)?()=>nuevo = true:null" :callback_editar="(permisos.editar)?()=>editar = true:null" :callback_cancelar="Otros().LimpiarDatos" :disabled_texto="true" :disabled_editar="editar" />
                </div>
                <div v-if="info.Id" class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 pt-6 text-right">
                    <vs-button color="primary" :disabled="!permisos.pago_productividad" @click="permisos.pago_productividad ? Editar().PagaProductividad() : null"><i class="fas fa-user-nurse mr-2"></i> Asignar Pago Productividad</vs-button>
                </div>
                <!-- {{info}} -->
            </div>

            <!-- NUEVO -->
            <div v-if="nuevo || editar || info.Id">
                <div class="flex flex-wrap">
                    <div class="w-full sm:w-1/3 p-1">
                        <ValidationProvider name="Nombres" rules="required" v-slot="{ errors }" class="required">
                            <vs-input label="Nombres" class="w-full" v-model="info.Puesto" :disabled="!nuevo && !editar" :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null" />
                        </ValidationProvider>
                    </div>

                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <SM-Buscar label="Estado" v-model="info.Activo" :api="[{Codigo:'1',Nombre:'Activo'},{Codigo:'0',Nombre:'Inactivo'}]" :api_campos="['Nombre']" :api_titulos="['Nombre']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :disabled_texto="true" :disabled_busqueda="!editar && !nuevo" />
                    </div>
                    <!-- {{info}} -->
                </div>

                <vs-divider position="left">Información Adicional</vs-divider>
                <div class="flex flex-wrap">
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                        <SM-Buscar label="Pago Productividad" v-model="info.PagaProductividad" :api="[{Codigo:'1',Nombre:'Activo'},{Codigo:'0',Nombre:'Inactivo'}]" :api_campos="['Nombre']" :api_titulos="['Nombre']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :disabled_texto="true" :disabled_busqueda="(!editar && !nuevo) " />
                    </div>
                </div>

                <!-- {{info}} -->

                <div v-if="editar || nuevo" class="flex bottom">
                    <vs-spacer></vs-spacer>
                    <vs-button :disabled="invalid" @click.native="handleSubmit(Guardar().Puesto());">
                        Guardar
                    </vs-button>
                </div>
            </div>

        </form>
    </ValidationObserver>
</vx-card>
</template>

<script>
export default {
    data() {
        return {
            info: {
                Id: null,
                Puesto: null,
                PagaProductividad: '0',
                Activo: 1
            },
            permisos: {
                nuevo: false,
                editar: false,
                pago_productividad: false
            },
            edit: {
                PagaProductividad: null,
            },
            show: {
                PagaProductividad: false,
            },
            nuevo: false,
            editar: false
        }
    },
    methods: {
        Consulta() {
            return {
                ConsultaPuesto: (data) => {
                    this.axios.post('/app/administracion/PuestoConsulta', {
                            Id: data.Id
                        })
                        .then(resp => {
                            if (resp.data.json) {
                                this.$map({
                                    objeto: this.info,
                                    respuestaAxios: resp,
                                    omitir: ['Id']
                                })
                            }
                        })
                }
            }
        },
        Nuevo() {
            return {
                Puesto: () => {

                }
            }
        },
        //=======================================================================================================
        // GUARDAR
        //=======================================================================================================
        Guardar: function() {
            return {
                Puesto: () => {

                    if (this.info.Id == null){
                        this.info.Id = 0;

                    }
                    this.axios.post('/app/administracion/PuestoGuardar', {
                        ...this.info,
                        editar: this.editar
                    })
                    this.nuevo = '';
                    this.editar = '';
                    this.info.Id = '';
                    
                },
                GuardarPagaProductividad: () => {
                    this.axios.post('/app/administracion/PuestoPagaProductividadGuardar', {
                            Id: this.info.Id,
                            PagaProductividad: this.edit.PagaProductividad,
                            editar: true
                        })
                        .then(() => {
                            this.show.PagaProductividad = false
                            this.info.PagaProductividad = this.edit.PagaProductividad
                        })
                }
            };
        },

        Editar() {
            return {
                PagaProductividad: () => {
                    this.show.PagaProductividad = true;
                    this.edit.PagaProductividad = this.info.PagaProductividad
                }
            }
        },
        Otros() {
            return {
                LimpiarDatos: () => {
                    this.info.Id = null
                    this.info.Puesto = null
                    this.info.PagaProductividad = '0'
                    this.info.Activo = '1'

                    this.editar = false
                    this.nuevo = false
                }
            }
        }
    },
    mounted() {
        this.permisos.nuevo = this.$validar_privilegio("NUEVO").status;
        this.permisos.editar = this.$validar_privilegio("EDITAR").status;
        this.permisos.pago_productividad = this.$validar_privilegio("PAGO_PRODUCTIVIDAD").status;
    }
}
</script>
