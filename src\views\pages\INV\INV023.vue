<template>
    <vx-card title="Ingreso Devolución Movimientos Bodega">
    
        <div class="content content-pagex">
            <vs-divider></vs-divider>
            <br>
            <div class="flex flex-wrap">                
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">                    
                    <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                        <label class="typo__label">Bodega Fuente:</label>       
                        <strong>{{ cod_bodegaFuente.CODIGO }}</strong>                           
                        <multiselect
                        v-model="cod_bodegaFuente"
                        :options="Lista_bodegas_fuente"
                        :searchable="true"
                        :close-on-select="true"
                        :show-labels="false"
                        :allow-empty="false"
                        :custom-label="Bodegas_seleccionado"
                        placeholder="Seleccionar bodega"
                        @input="onChangeBodegaFuente"
                        :danger="errors.length > 0"
                        :danger-text="errors.length > 0 ? errors[0] : null"
                        >
                        <span
                        slot="noOptions">Lista no disponible.</span>
                        </multiselect>
                    </ValidationProvider>                                                                  
                </div>
                <div class="w-full md:w-full lg:w-2/12 xl:w-2/12 pt-2 pl-10" v-if="permiso_bodega_vencido == 'S'">
                    <label  class="typo__label">Envio Bodega Vencido Descarte</label> 
                    <vs-switch  v-on:change="actualizacion_v1()" v-model="enviar_bodega_vencido_descarte" />    
                </div>
                <div class="w-full md:w-full lg:w-2/12 xl:w-2/12 pt-2 pl-10" v-if="permiso_bodega_vencido == 'S'">
                    <label class="typo__label">Envio Bodega Vencido Cambio</label> 
                    <vs-switch   v-on:change="actualizacion_v2()" v-model="enviar_bodega_vencido_cambio" />  
                </div>
            </div>

            <div class="flex flex-wrap">
    
                <div style="margin-right: 20px" class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <label class="typo__label">Estado</label>
                    <multiselect
                      v-model="cb_lista_operacion"
                      :options="lista_estado"
                      :searchable="true"
                      :close-on-select="true"
                      :show-labels="false"
                      :allow-empty="false"
                      :custom-label="Operacion_seleccionada"
                      placeholder="Seleccionar"
                      @input="onChangeEstado">
                      <span slot="noOptions">Lista no disponible.</span>
                    </multiselect>
                </div>
    
                <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7 m-1">
                    <label class="typo__label">Fecha Inicio:</label>
                    <vs-input type="date" v-model="fecha_inicio" name="date1" />
                </div>
    
                <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7 m-1">
                    <label class="typo__label">Fecha Final:</label>
                    <vs-input type="date" v-model="fecha_final" name="date1" />
                </div>
    
                <vs-button style="float:left;margin: 20px" color="primary" type="filled" icon-pack="fas" icon="fa-search" @click="Consultar_OrdenEnc()"> Búsqueda</vs-button>
    
                <vs-button color="primary" style="float:left;margin: 20px" type="filled" icon-pack="fas" icon="fa-plus" @click="Emergente_Detalle('','N')"> Nuevo</vs-button>
    
            </div>
            <vs-alert v-if="cod_bodegaFuente.VisualizarTraslados && cod_bodegaFuente.VisualizarTraslados == 'S'" active.sync="true" color="success" icon-pack="feather" icon="icon-info" closable close-icon="icon-x" class="mb-1">
                Se muestran todos los movimientos para los estados: Enviado Devolución, Aceptado Devolución
            </vs-alert>
            <vs-divider></vs-divider>
    
            <vs-table2 max-items="10" pagination :data="Lista_vista" search id="tb_lista_solicitud">
    
                <template slot="thead">
                    <th width="130px">Nº. Movimiento</th>
                    <th width="130px">Nº. Solicitud</th>
                    <th>Destino</th>
                    <th>Fecha(s)</th>
                    <th>Observaciones</th>
                    <th></th>
    
                </template>
    
                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">
    
                        <vs-td2 width='5%'>
                            {{tr.IDMOVIMIENTOFK}}
                        </vs-td2>

                        <vs-td2 width='5%'>
                            {{tr.IDREQUISICIONENC}}
                        </vs-td2>
    
                        <vs-td2 width='20%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                {{tr.IdBodegaDestinofk +' - ' +tr.NOMBRE_BODEGA_DESTINO}}
                            </div>
                        </vs-td2>
    
                        <vs-td2 width='20%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                <label v-if="tr.FECHASOLICITUD">Creación Devolución: {{tr.FECHASOLICITUD}}</label>
                                <br v-if="tr.FECHASOLICITUD">
                                <label v-if="tr.FECHAFINALIZADO">{{"Enviado Devolución:"+ tr.FECHAFINALIZADO +" - Usuario: "+ tr.IDCORPORATIVOSOLICITAFK}}</label>
                                <br v-if="tr.FECHAFINALIZADO">
                                <label v-if="tr.FECHAENTREGADA">Aceptado Devolución: {{tr.FECHAENTREGADA}}</label>
                            </div>
                        </vs-td2>
                        <vs-td2  width='25%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                {{ tr.OBSERVACIONES }}
                            </div>
                        </vs-td2>    
    
                        <vs-td2 v-if="Id_estado_seleccionado  == 'P'" width='10%' align="right" style="white-space:nowrap;min-width: 200px;">
                            <vx-tooltip text="Consultar" style="display:inline-block;margin-right:30px">
                                <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" @click="Emergente_Detalle(data[indextr], 'E')"></vs-button>
                            </vx-tooltip>
                        </vs-td2>
                        <vs-td2 v-else-if="Id_estado_seleccionado  == 'E'" width='10%' align="right" style="white-space:nowrap;min-width: 200px;">
                            <vx-tooltip text="Consultar" style="display:inline-block;margin-right:30px">
                                <vs-button color="primary" icon-pack="fas" icon="fa-eye" style="display:inline-block;margin-right:2px" @click="Emergente_Detalle_Pedido(data[indextr])"></vs-button>    
                            </vx-tooltip>
                            <vx-tooltip text="Excel" style="display:inline-block;margin-right:10px">
                                <vs-button icon-pack="far" icon="fa-file-excel" @click="Consultar_Movimiento(data[indextr],'EXCEL')" color="success" style="display:inline-block;margin-right:2px"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip text="Imprimir" style="display:inline-block;margin-right:30px">
                                <vs-button color="primary" icon-pack="feather" icon="icon-printer" style="display:inline-block;margin-right:2px" @click="Consultar_Movimiento(data[indextr])"></vs-button>
                            </vx-tooltip>
                        </vs-td2>
                        <vs-td2 v-else-if="Id_estado_seleccionado  == 'R'" width='10%' align="right" style="white-space:nowrap;min-width: 200px;">                             
                            <vx-tooltip text="Consultar" style="display:inline-block;margin-right:30px">
                                <vs-button color="primary" icon-pack="fas" icon="fa-eye" style="display:inline-block;margin-right:2px" @click="Emergente_Detalle_Recepcion(data[indextr])"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip text="Excel" style="display:inline-block;margin-right:10px">
                                <vs-button icon-pack="far" icon="fa-file-excel" @click="Consultar_Movimiento(data[indextr],'EXCEL')" color="success" style="display:inline-block;margin-right:2px"></vs-button>
                            </vx-tooltip>                     
                            <vx-tooltip text="Imprimir" style="display:inline-block;margin-right:30px">
                                <vs-button color="primary" icon-pack="feather" icon="icon-printer" style="display:inline-block;margin-right:2px" @click="Consultar_Movimiento(data[indextr])"></vs-button>
                            </vx-tooltip>    
                        </vs-td2>    
                    </tr>
                </template>
            </vs-table2>
        </div>
                    <!----------------------- NUEVA ASOCIACIÓN ---------->
        <vs-popup class="inventario-popup" :title="Descripcion_Emergente" :active.sync="Estado_Emergente">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
                <div style="border:2px solid #1B5E20;border-radius:5px;padding:5px;font-size:12px;background-color: #C8E6C9">
                    Sucursal Origen: <b style="font-size:1vw">{{sesion.sesion_sucursal_nombre}}</b>
                </div>
                <br>
                <vs-row class="w-full flex">
                    <div class="sm:w-6/12 md:w-5/12 lg:w-4/12 xl:w-3/12">
                        <b> Movimiento No. </b>&nbsp;
                        <b style="font-size:2vw">{{Id_Movimiento}}</b>
                    </div>   
                    <div class="sm:w-6/12 md:w-5/12 lg:w-4/12 xl:w-3/12" style="align-self:right; direction: rtl;">
                        <b> Solicitud No. </b>&nbsp;
                        <b style="font-size:2vw">{{Id_OrdenRequisicion}}</b>
                    </div>
                </vs-row>  
                <!--- Ubicación Destino -->
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                    <br>
                    <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                        <label class="typo__label">Bodega Fuente:</label>       
                        <strong>{{ cod_bodegaFuente.CODIGO }}</strong>                                                 
                        <multiselect
                            v-model="cod_bodegaFuente"
                            :options="Lista_bodegas_fuente"
                            :searchable="true"
                            :close-on-select="true"
                            :show-labels="false"
                            :custom-label="Bodegas_seleccionado"
                            placeholder="Seleccionar bodega"
                            @input="onChangeBodegaFuente"
                            :danger="errors.length > 0"
                            :danger-text="errors.length > 0 ? errors[0] : null"
                            :disabled="Deshabilitar_campos">
                            <span
                            slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                    </ValidationProvider>                                                
                </div>
                    <!--- Ubicación Destino -->
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                    <br>
                    <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                        <label class="typo__label">Bodega Destino:</label>
                        <strong>{{ cod_bodegaDestino.CODIGO }}</strong>
                        <multiselect v-if="cod_bodegaFuente.CODIGO != 5"
                            v-model="cod_bodegaDestino"
                            :options="Lista_bodegas_destino_consulta"
                            :searchable="true"
                            :close-on-select="true"
                            :show-labels="false"
                            :custom-label="Bodegas_seleccionado"
                            placeholder="Seleccionar bodega"
                            @input="onChangeBodegaDestino"
                            :danger="errors.length > 0"
                            :danger-text="errors.length > 0 ? errors[0] : null"
                            :disabled="Deshabilitar_campos">
                            <span
                            slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        
                        <multiselect v-if="cod_bodegaFuente.CODIGO == 5"
                            v-model="cod_bodegaDestino"
                            :options="Lista_Bodegas_Destino_Vencido"
                            :searchable="true"
                            :close-on-select="true"
                            :show-labels="false"
                            :custom-label="Bodegas_seleccionado"
                            placeholder="Seleccionar bodega"
                            @input="onChangeBodegaDestino"
                            :danger="errors.length > 0"
                            :danger-text="errors.length > 0 ? errors[0] : null"
                            :disabled="Deshabilitar_bodega_fuente">
                            <span
                            slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                    </ValidationProvider>
                </div>
            
                <br>
                <!--- Producto -->
                <vs-divider>Detalle</vs-divider>
                <div class="flex flex-wrap">

                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <ValidationProvider name="producto" rules="required" v-slot="{ errors }" class="required">
                            <label class="typo__label">Producto</label>
                            <vx-input-group class="">
                                <vs-input id="busquedaProducto" v-model="producto_buscar" @keydown.enter="cargar_producto()"  @keydown.tab.prevent="cargar_producto()" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                <template slot="append">
                                    <div class="append-text btn-addon">
                                        <button type="submit" v-if="false" name="button"></button>
                                        <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="cargar_producto()" icon="fa-search"></vs-button>
                                    </div>
                                </template>
                            </vx-input-group>
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                        <ValidationProvider name="cant_solicitada" :rules="'required|numero_min:0|numero_max:'+this.Producto_seleccionado.existencias" v-slot="{ errors }">
                            <label class="typo__label">Cant. Devolver</label>
                            <vs-input id="cantidad_dev" class="w-full" type="number" count="100" v-model="Cantidad_devolucion" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                            <br v-if="errors.length>0">
                        </ValidationProvider>                            
                        <label v-if="parseFloat(this.Producto_seleccionado.existencias) > parseFloat(this.Producto_seleccionado.minimo_despachante)" style="width:100px; border-radius: 3px;  background-color:#69F0AE;text-align: center;color:black;font-size:15px; "> Disponible: {{this.Producto_seleccionado.existencias}} </label>
                        <label v-else-if="(parseFloat(this.Producto_seleccionado.existencias) >0) && (parseFloat(this.Producto_seleccionado.existencias) <= parseFloat(this.Producto_seleccionado.minimo_despachante))" style="width:100px; border-radius: 3px;  background-color:#FFFF00;text-align: center;color:black;font-size:15px; "> Disponible: {{this.Producto_seleccionado.existencias}}</label>
                        <label v-else-if="parseFloat(this.Producto_seleccionado.existencias)<= 0" style="width:100px; border-radius: 3px;  background-color:#FF5252;text-align: center;color:white;font-size:15px; "> Disponible: {{this.Producto_seleccionado.existencias}} </label>
                    </div>
                    <!------ BOTONES DE ACCION ---->
                    <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6">
                        <vs-button color="primary" style="float:left;margin: 20px" type="filled" icon-pack="fas" icon="fa-plus" @keydown.tab.prevent="AgregarDetalleEncabezado()" @click="AgregarDetalleEncabezado()">Agregar</vs-button>
                    </div>
                </div>
                <!----- Detalle producto seleccionado--->
                <div v-if="Producto_seleccionado.Codigo>0" class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                    <!---<div style="border:2px solid #1B5E20;border-radius:5px;padding:5px;font-size:12px;background-color:#A5D6A7">--->
                    <div style="border-radius:5px;padding:5px;font-size:12px;background-color:#A5D6A7">
                        Código: {{Producto_seleccionado.Codigo}}
                        <br>
                        Producto: {{Producto_seleccionado.marca_comercial}}
                        <br>
                        Presentación: {{Producto_seleccionado.Presentacion}}
                        <br>

                    </div>
                </div>
                <!--- Mostrar detalle de la orden--->
                <vs-table2 max-items="10" pagination :data="Lista_detalle" id="tb_lista_solicitud" v-if="Estado_Emergente">

                    <template slot="thead">
                        <th>Linea</th>
                        <th>Codigo</th>
                        <th>Producto</th>
                        <th>Unidad Medida</th>
                        <th>Cant. Solicitada</th>
                        <th v-if="permiso_ver_costo_promedio">Costo promedio</th>
                        <th v-if="permiso_ver_costo_promedio">Costo promedio total</th>
                        <th></th>
                    </template>

                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2 width='5%'>                                                                            
                                {{indextr+1}}                                                                            
                            </vs-td2>
                            <vs-td2 width='5%'>                                                                            
                                {{tr.IDPRODUCTOFK}}                                                                            
                            </vs-td2>
                            <vs-td2 width='45%'>
                                <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                                        
                                    {{tr.DESCRIPCION_PRODUCTO}}                                        
                                </div>
                            </vs-td2>
                            <vs-td2 width='15%'>
                                <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">
                                    {{tr.DESCRIPCION_UNIDAD}}
                                </div>                                    
                            </vs-td2>
                            <vs-td2 width='5%'>
                                {{tr.CANTIDAD_PEDIDA}}
                            </vs-td2>
                            <vs-td2 width="10%" v-if="permiso_ver_costo_promedio">
                                <div style="min-width: fit-content;">
                                    {{ $formato_decimal(tr.COSTO_PROMEDIO) }}
                                </div>
                            </vs-td2>
                            <vs-td2 width="10%" v-if="permiso_ver_costo_promedio" >
                                <div style="min-width: fit-content;">
                                    {{ $formato_decimal(tr.COSTO_PROMEDIO_TOTAL) }}
                                </div>
                            </vs-td2>
                            <vs-td2 width='5%'>
                                <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="Eliminar_Registro(data[indextr])"></vs-button>
                            </vs-td2>
                        </tr>
                    </template>
                    <template slot="tfooter">
                        <tr v-if="permiso_ver_costo_promedio" class="foot">
                            <th colspan="6" scope="row" style="padding-right: 10px;">Total del Movimiento</th>
                            <td>{{totalCostos}}</td>
                        </tr>
                    </template>
                </vs-table2>
                <vs-divider></vs-divider>
                <div class="flex flex-wrap">
                    <div class="w-full md:w-full lg:w-full xl:w-full">
                        <vs-textarea label="Observaciones" type="text" class="w-full" v-model="Observaciones" />
                    </div>
                </div>
                <vs-button
                    color="primary"
                    style="float:right;margin: 5px"
                    type="filled"
                    icon-pack="feather"
                    icon="icon-save"
                    id="btn_confirmacion"
                    @click="Finalizar_Devolucion()"> Finalizar Devolución</vs-button>
                <vs-divider></vs-divider>
            </div>
        </vs-popup>

        <!--------------- Resultado busqueda Producto-------------->
        <vs-popup title="Resultado Búsqueda" :active.sync="Estado_VentanaEmergente_Busqueda" style="z-index:99999" id="div-with-loading">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">

                <form>

                    <vs-divider></vs-divider>
                    <vs-table2 max-items="10" pagination :data="producto" id="tb_departamentos">

                        <template slot="thead">
                            <th>Codigo</th>
                            <th>Descripcion</th>
                            <th>Concentración</th>
                            <th>Presentación</th>
                            <th></th>
                        </template>

                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 :data="data[indextr].Codigo">
                                    {{data[indextr].Codigo}}
                                </vs-td2>

                                <vs-td2 :data="data[indextr].Descripcion">
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">
                                        {{data[indextr].Descripcion}}
                                    </div>
                                </vs-td2>

                                <vs-td2 :data="data[indextr].Concentracion">
                                    {{data[indextr].Concentracion}}
                                </vs-td2>

                                <vs-td2 :data="data[indextr].PresentacionNombre">
                                    {{data[indextr].PresentacionNombre}}
                                </vs-td2>

                                <vs-td2 align="right">
                                    <vs-button color="primary" icon-pack="fas" icon="fa-check-circle" style="display:inline-block;margin-right:2px" @click="Seleccionar_producto(data[indextr])"></vs-button>
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
                </form>

            </div>
        </vs-popup>

        <!----------------------- RECEPCION DE PRODUCTO ---------->
        <vs-popup class="inventario-popup"  :title="Descripcion_Emergente" :active.sync="Estado_Emergente_Recepcion">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">                  
                <vs-row class="w-full flex">
                    <div class="sm:w-6/12 md:w-5/12 lg:w-4/12 xl:w-3/12">
                        <b> Movimiento No. </b>&nbsp;
                        <b style="font-size:2vw">{{Id_Movimiento}}</b>
                    </div>   
                    <div class="sm:w-6/12 md:w-5/12 lg:w-4/12 xl:w-3/12" style="align-self:right; direction: rtl;">
                        <b> Solicitud No. </b>&nbsp;
                        <b style="font-size:2vw">{{Id_OrdenRequisicion}}</b>
                    </div>
                </vs-row> 
                <div class="w-1/2 md:w-1/2 lg:w-6/12 xl:w-6/12">
                        <b> <small>Fecha Enviado Devolución: </small></b>
                    <b>{{Requisicion_seleccionada.FECHAFINALIZADO}}</b>
                </div>
                <!--- Ubicación Destino -->
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                    <br>
                    <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                        <label class="typo__label">Bodega Fuente:</label>
                        <strong>{{id_bodega_fuente}}</strong>
                        <vs-input  type="text" v-model="nombre_bodega_fuente" :disabled="Deshabilitar_campos" class="w-full" 
                                    :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null"/>
                    </ValidationProvider>
                </div>  
                <br>                      
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                    <br>
                    <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                        <label class="typo__label">Bodega Destino:</label>
                        <strong>{{id_bodega_destino}}</strong>
                        <vs-input  type="text" v-model="nombre_bodega_destino" :disabled="Deshabilitar_campos" class="w-full" 
                                    :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null"/>
                    </ValidationProvider>
                </div>
                <vs-divider>Detalle</vs-divider>

                <!--- Mostrar detalle de la orden--->
                <vs-table2 max-items="10" pagination :data="Lista_detalle" id="tb_lista_solicitud" v-if="Estado_Emergente_Recepcion">

                    <template slot="thead">
                        <th>Linea</th>
                        <th>Codigo</th>
                        <th>Producto</th>
                        <th>Unidad Medida </th>
                        <th>Cant. Devuelta</th>                            
                        <th v-if="permiso_ver_costo_promedio">Costo promedio</th>
                        <th v-if="permiso_ver_costo_promedio">Costo promedio total</th>                           
                        <th v-if="!Consulta_Pedido">Cant. Aceptada</th>
                    </template>

                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2 width='5%'>
                                {{ indextr+1 }}
                            </vs-td2>
                            <vs-td2 width='5%'>
                                {{tr.IDPRODUCTOFK}}
                            </vs-td2>
                            <vs-td2 width='45%'>
                                <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                                        
                                    {{tr.DESCRIPCION_PRODUCTO}}                                        
                                </div>
                            </vs-td2>
                            <vs-td2 width='10%'>
                                <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                                        
                                    {{tr.DESCRIPCION_UNIDAD}}
                                </div>
                            </vs-td2>
                            <vs-td2 width='5%'>
                                {{tr.CANTIDAD_PEDIDA}}
                            </vs-td2>
                            <vs-td2 width="10%" v-if="permiso_ver_costo_promedio">
                                <div style="min-width: fit-content;">
                                    {{ $formato_decimal(tr.COSTO_PROMEDIO) }}
                                </div>
                            </vs-td2>
                            <vs-td2 width="10%" v-if="permiso_ver_costo_promedio" >
                                <div style="min-width: fit-content;">
                                    {{ $formato_decimal(tr.COSTO_PROMEDIO_TOTAL) }}
                                </div>
                            </vs-td2>
                            <vs-td2 v-if="!Consulta_Pedido" width='5%'>
                                {{ tr.CANTIDAD_RECIBIDA }}   
                            </vs-td2>
                            <!-- <vs-switch v-if="tr.FINALIZADO ==='N'" style="margin-top:10px;margin-left:20px" v-model="tr.ESTADO_SWITCH" @input="Registrar_RecibidoDet(tr, 'R')" /> -->  
                        </tr>
                    </template>
                    <template slot="tfooter">
                        <tr v-if="permiso_ver_costo_promedio" class="foot">
                            <th colspan="6" scope="row" style="padding-right: 10px;">Total del Movimiento</th>
                            <td>{{totalCostos}}</td>
                        </tr>
                    </template>
                </vs-table2>
                <vs-divider></vs-divider>
                <div class="flex flex-wrap">
                    <div class="w-full md:w-full lg:w-full xl:w-full">
                        <vs-textarea disabled label="Observaciones" type="text" class="w-full" v-model="Observaciones" />
                    </div>
                    <vs-divider></vs-divider>
                    <div class="flex">
                        <div class="w-full">
                            <b><small >Solicitado por: </small></b>&nbsp;
                            <b>{{Requisicion_seleccionada.CORPORATIVO_SOLICITANTE +' - '+Requisicion_seleccionada.IDCORPORATIVOSOLICITAFK}}</b>&nbsp;
                        </div> 
                    </div>                   
                    <vs-divider></vs-divider>        
                </div>
                <div >                        
                    <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-printer" id="btn_movimiento" @click="Consultar_Movimiento(Requisicion_seleccionada)"> Imprimir </vs-button>                                        
                    <vs-button icon-pack="far" style="float:right;margin: 5px" icon="fa-file-excel" @click="Consultar_Movimiento(Requisicion_seleccionada,'EXCEL')" color="success">Excel</vs-button>
                </div>
                <vs-divider></vs-divider>
            </div>
        </vs-popup>
    </vx-card>
    </template>
    
    <script>
    import Multiselect from 'vue-multiselect'   
    import moment from "moment" 
    import "vue-multiselect/dist/vue-multiselect.min.css"    
    export default {
        components: {
            Multiselect
        },
        watch:{
            Estado_Emergente(value){
                if(value==false){
                    this.InsertoEncabezado = false;
                }                
            }
        },
        data() {
            return {
                Estado_VentanaEmergente_Busqueda: false,
                cb_lista_operacion: '',
                Descripcion_Emergente: '',
                Consulta_Pedido:false,
                Consulta_Aceptados:false,
                permiso_ver_costo_promedio: false,
                producto_buscar: '',                
                lista_estado: [{
                        ID: 'P',
                        DESCRIPCION: 'Edición'
                    },
                    {
                        ID: 'E',
                        DESCRIPCION: 'Enviado Devolución'
                    },
                    {
                        ID: 'R',
                        DESCRIPCION: 'Aceptado Devolución'
                    }
                ],
                Id_estado_seleccionado: 0,
                fecha_inicio: '',
                fecha_final: '',
                Lista_vista: [],
                Estado_Emergente: false,
                Estado_Emergente_Recepcion: false,
                enviar_bodega_vencido: false,
                permisos_tipos_bodegas: [],
                permiso_bodega_vencido:'',
                enviar_bodega_vencido_descarte: false,
                enviar_bodega_vencido_cambio: false,
                Bodega_Vencidos:{},
                Bodega_Destino:{},
                Lista_bodegas_destino_consulta: [],
                Lista_Bodegas_Destino_Vencido:[],
                Lista_bodegas_fuente: [],
                id_bodega_destino: '',
                nombre_bodega_fuente: '',
                id_bodega_fuente: '',
                nombre_bodega_destino: '',
                cod_bodegaDestino: '',
                cod_bodegaFuente: '',    
                Producto_seleccionado: {
                    nombre: '',
                    marca_comercial: '',
                    Principio_activo: '',
                    Concentracion: '',
                    Presentacion: '',
                    Codigo: '',
                    codigo_interno: '',
                    existencias: 0,
                    Activo: 'N'
                },
                Requisicion_seleccionada: {},
                Id_OrdenRequisicion: 0,
                Id_Movimiento:0,
                InsertoEncabezado: false,
                Cantidad_devolucion: '',    
                Lista_detalle: [],
                Deshabilitar_campos: false,
                Deshabilitar_bodega_fuente: false,
                producto: [],    
                Observaciones: '',
                listado_reportes: [],
                listado_source: {
                    CODIGO_SOLICITUD: '',
                    CODIGO_MOVIMIENTO: '',
                    CODIGO_REQUERIMIENTO: '',
                }                      
            }
        },
        async mounted() {
            this.permiso_ver_costo_promedio = this.$validar_privilegio('VER_COSTO_PROMEDIO').status

            this.cb_lista_operacion = {
                ID: 'P',
                DESCRIPCION: 'Edición'
            }
            this.Id_estado_seleccionado = 'P';
            let permisos_sub_bodegas = []   
            for(let privilegio of this.$store.state.privilegios){
               if(privilegio.Privilegio.includes("TIPO_BODEGA")){                      
                   let tipobodega = privilegio.Privilegio.split('_')[2];
                   if(tipobodega == 'T'){
                     this.permiso_bodega_vencido = 'S'
                   }else{
                     this.permisos_tipos_bodegas.push(privilegio.Privilegio.split('_')[2])                    
                   }                                                       
                }else if(privilegio.Privilegio.includes("DEVOLUCION_SUB_BODEGA")){
                    permisos_sub_bodegas.push(privilegio.Privilegio.split('_')[3])
                }
            }      
            await this.Consultar_Bodega('F',this.permisos_tipos_bodegas.join(","),this.permiso_bodega_vencido);
            await this.Consultar_Bodega('H',this.permisos_tipos_bodegas.join(","),this.permiso_bodega_vencido)  
            await this.ConsultarSubBodega('G',permisos_sub_bodegas.join(","));                   
        },
        computed: {
            totalCostos() {
                return this.Lista_detalle.reduce((acumulador, movimiento) => 
                                acumulador + parseFloat(movimiento.COSTO_PROMEDIO_TOTAL)  , 0)
                           .toLocaleString("es-GT", {
                                                style: "currency",
                                                currency: "GTQ",
                                                maximumFractionDigits:2
                                            })
    
            },
            sesion() {
                
                return this.$store.state.sesion
            }
        },
        async beforeCreate(){
            this.listado_reportes = await this.$recupera_parametros_reporte('Movimiento Requerimiento')                  
        },
        methods: {
            async Consultar_Movimiento(datos, formato = 'PDF') {
                this.listado_source.CODIGO_MOVIMIENTO = datos.IDMOVIMIENTOFK
                this.listado_source.CODIGO_SOLICITUD = datos.IDREQUISICIONENC
                this.listado_source.VerCostoPromedio = this.permiso_ver_costo_promedio? 'S':'N'
                this.$reporte_modal({
                    Nombre: "Movimiento Requerimiento",
                    Opciones: this.listado_source,
                    Formato: formato
                }).catch(() => {
                })
            },
            GetDateValue(value) {
                if (value === undefined) {
                    return '';
                } else if (value === null) {
                    return '';
                } else if (value == '') {
                    return '';
                }
                return moment(value).format('DD/MM/YYYY');          
            },
            actualizacion_v1(){
                if(this.enviar_bodega_vencido_descarte)
                    this.enviar_bodega_vencido_cambio = false
            },
            actualizacion_v2(){
                if(this.enviar_bodega_vencido_cambio)
                    this.enviar_bodega_vencido_descarte = false
            },
            Operacion_seleccionada({
                DESCRIPCION
            }) {
                return `${DESCRIPCION} `
            },
            onChangeEstado(value) {
                if (value !== null && value.length !== 0) {
                    if (value.ID == 'S')
                        this.Id_estado_seleccionado = 'P';
                    else
                        this.Id_estado_seleccionado = value.ID;
                    this.Consultar_OrdenEnc();
                } else {
                    this.Id_estado_seleccionado = '';
                }
            },
            Bodegas_seleccionado({
                CODIGO,NOMBRE
            }) {
                return `${CODIGO} - ${NOMBRE} `;
            },
            onChangeBodega(value) {
                if (value !== null && value.length !== 0) {
                    this.id_bodega_destino = value.CODIGO;
    
                } else {
                    this.id_bodega_destino = '';
                }
            },
            onChangeBodegaDestino(value) {
                if (value !== null && value.length !== 0) {
                    this.id_bodega_destino = value.CODIGO;
                } else {
                    this.id_bodega_destino = '';
                }
            },
            onChangeBodegaFuente(value) {
                if(!value){
                    this.cod_bodegaFuente = ''
                    this.Bodega_Destino = {}
                    return
                }
                if (value !== null && value.length !== 0) {
                    this.id_bodega_fuente = value.CODIGO;                    
                    this.Lista_Bodegas_Destino_Vencido = [];      
                    
                    if(value.BodegaFuente && value.BodegaPadre){
                        for(const bodega of this.Lista_bodegas_destino_consulta){
                            if( bodega.CODIGO == value.BodegaPadre){                            
                                this.Bodega_Destino = bodega                                               
                                this.id_bodega_destino = bodega.CODIGO
                                this.cod_bodegaDestino = bodega
                                break
                            }                            
                        } 
                    }else{
                        for(const bodega of this.Lista_bodegas_destino_consulta){          
                            if( bodega.TipoBodega == value.TipoBodega){ 
                                this.Bodega_Destino = bodega                                               
                                this.id_bodega_destino = bodega.CODIGO
                                this.cod_bodegaDestino = bodega
                                break
                            }                            
                        }  
                    }

                    
                    
                    //para bodega destino proximos a vencer
                    if(this.id_bodega_fuente == 5){
                        for(const bodega of this.Lista_bodegas_destino_consulta){
                            if((bodega.TipoBodega == 'M' || bodega.TipoBodega == 'D') && bodega.CODIGO != 5){
                                this.Lista_Bodegas_Destino_Vencido.push(bodega)
                            }                         
                        }    
                    }      
                    this.Consultar_OrdenEnc();   
                } else {
                    this.cod_bodegaDestino = '';
                    this.id_bodega_destino = '';
                }
            },
            Cargar_Bodega_Destino(Bodega_Destino){
                if(!Bodega_Destino || Object.keys(Bodega_Destino).length === 0){
                        this.id_bodega_destino = '';
                        setTimeout(() => {
                            this.cod_bodegaDestino = {
                                NOMBRE: '',
                                CODIGO: '',
                                TipoBodega: ''
                            }
                        }, 500);

                        this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'No se cargo la bodega destino',
                        });
                        return
                    }
                    this.id_bodega_destino = Bodega_Destino.CODIGO;
                    setTimeout(() => {
                        this.cod_bodegaDestino = {
                                NOMBRE: Bodega_Destino.NOMBRE,
                                CODIGO: Bodega_Destino.CODIGO,
                                TipoBodega: Bodega_Destino.TipoBodega
                            }
                    }, 500);
            },
            Emergente_Detalle(Datos, Operacion) {
                this.Estado_Emergente = true;
                //this.cod_bodegaDestino = '';
                this.Limpiar_Campos();
                this.producto_buscar = '';
                this.Cantidad_devolucion = '';
                this.Deshabilitar_bodega_fuente  = false;
                if (Operacion == 'N') {                                        
                    this.Id_OrdenRequisicion = 0;
                    this.Id_Movimiento = 0;
                    this.Observaciones = '';
                    this.Deshabilitar_campos = true;
                    this.Descripcion_Emergente = 'Ingreso Devolución Movimiento';
                    this.Lista_detalle = [];
                    if(this.enviar_bodega_vencido_cambio || this.enviar_bodega_vencido_descarte)
                        this.Cargar_Bodega_Destino(this.Bodega_Vencidos)                
                    else
                        this.Cargar_Bodega_Destino(this.Bodega_Destino)
                } else {
                    this.Id_OrdenRequisicion = Datos.IDREQUISICIONENC;
                    this.Id_Movimiento = Datos.IDMOVIMIENTOFK;
                    this.id_bodega_destino = Datos.IdBodegaDestinofk;
                    this.id_bodega_fuente = Datos.IdBodegaFuentefk;
                    this.Observaciones = Datos.OBSERVACIONES;
                    setTimeout(() => {
                        this.cod_bodegaFuente = {
                            NOMBRE: Datos.NOMBRE_BODEGA_FUENTE,
                            CODIGO: Datos.IdBodegaFuentefk,
                            TipoBodega: Datos.TIPO_BODEGA_FUENTE
                        }
                        this.cod_bodegaDestino = {
                            NOMBRE: Datos.NOMBRE_BODEGA_DESTINO,
                            CODIGO: Datos.IdBodegaDestinofk,
                            TipoBodega: Datos.TIPO_BODEGA_DESTINO
                        }
                    }, 500);
    
                    this.Deshabilitar_campos = true;
                    this.Deshabilitar_bodega_fuente = true;
                    this.Descripcion_Emergente = 'Editar Devolución Movimiento';
                    this.Consultar_OrdenDet()
                }
            },
            Emergente_Detalle_Pedido(Datos) {
               // 
               // 
                this.Estado_Emergente_Recepcion = true;
                this.Consulta_Pedido = true,
                this.Consulta_Aceptados = false,
                this.producto_buscar = '';
                this.Cantidad_devolucion = '';
                
                this.Requisicion_seleccionada = Datos
                this.Observaciones = Datos.OBSERVACIONES;
                this.Id_OrdenRequisicion = Datos.IDREQUISICIONENC;
                this.id_bodega_destino = Datos.IdBodegaDestinofk;
                this.id_bodega_fuente = Datos.IdBodegaFuentefk;
                this.nombre_bodega_fuente = Datos.NOMBRE_BODEGA_FUENTE;
                this.nombre_bodega_destino = Datos.NOMBRE_BODEGA_DESTINO;
    
                this.Deshabilitar_campos = true;
                this.Descripcion_Emergente = 'Consulta Devolución Movimiento';
                this.Consultar_OrdenDet()
    
            },
            Emergente_Detalle_Recepcion(Datos) {
               // 
               // 
                this.Estado_Emergente_Recepcion = true;
                this.Consulta_Pedido = false,
                this.Consulta_Aceptados = true,
                this.producto_buscar = '';
                this.Cantidad_devolucion = '';
                this.Requisicion_seleccionada = Datos
                this.Observaciones = Datos.OBSERVACIONES;
                this.Id_OrdenRequisicion = Datos.IDREQUISICIONENC;
                this.Id_Movimiento = Datos.IDMOVIMIENTOFK;
                this.id_bodega_destino = Datos.IdBodegaDestinofk;
                this.id_bodega_fuente = Datos.IdBodegaFuentefk;
                this.nombre_bodega_fuente = Datos.NOMBRE_BODEGA_FUENTE;
                this.nombre_bodega_destino = Datos.NOMBRE_BODEGA_DESTINO;
    
                this.Deshabilitar_campos = true;
                this.Descripcion_Emergente = 'Aceptación Despachos/Pedidos';
                this.Consultar_OrdenDet()
    
            },
            Limpiar_Campos() {
                this.Producto_seleccionado.Codigo = '';
                this.Producto_seleccionado.marca_comercial = '';
                this.Producto_seleccionado.Principio_activo = '';
                this.Producto_seleccionado.Concentracion = '';
                this.Producto_seleccionado.codigo_interno = '';
                this.Producto_seleccionado.existencias = 0;
                this.Producto_seleccionado.minimo_despachante = 0;
                this.Producto_seleccionado.Presentacion = '';
            },
            Seleccionar_producto(obj) {
    
                this.Producto_seleccionado.Codigo = obj.Codigo;
                this.Producto_seleccionado.marca_comercial = obj.Descripcion;
                this.Producto_seleccionado.Concentracion = obj.Concentracion;
                this.Producto_seleccionado.codigo_interno = obj.codigo_interno;
                this.Producto_seleccionado.Presentacion = obj.PresentacionNombre;
                this.Consulta_Existecias_Producto(this.Producto_seleccionado.Codigo);
                document.getElementById("cantidad_dev").focus();
                this.Estado_VentanaEmergente_Busqueda = false;
                
            },
            Mostrar_resultado_producto(value,codigo_busqueda) {
                if (value.length === 1) {

                     if(codigo_busqueda != value[0].Codigo){
                        this.$vs.notify({
                            timer:6000,
                            position:'top-center',
                            color: 'warning',
                            title: 'Inventario',
                            text: `El código encontrado (${value[0].Codigo}) no corresponde exactamente al buscado.`,
                        })
                    }

                    value.map(obj => {
                        this.Producto_seleccionado.Codigo = obj.Codigo;
                        this.Producto_seleccionado.marca_comercial = obj.Descripcion;
                        this.Producto_seleccionado.Concentracion = obj.Concentracion;
                        this.Producto_seleccionado.codigo_interno = obj.codigo_interno;
                        this.Producto_seleccionado.Presentacion = obj.PresentacionNombre;                        
                        this.Consulta_Existecias_Producto(this.Producto_seleccionado.Codigo);
                        document.getElementById("cantidad_dev").focus();
    
                    })
    
                } else if (value.length > 1) {
                    var productoEncontrado = value.find(obj =>obj.Codigo == codigo_busqueda)
                    if(productoEncontrado){                        
                        this.Producto_seleccionado.Codigo = productoEncontrado.Codigo;
                        this.Producto_seleccionado.marca_comercial = productoEncontrado.Descripcion;
                        this.Producto_seleccionado.Concentracion = productoEncontrado.Concentracion;
                        this.Producto_seleccionado.codigo_interno = productoEncontrado.codigo_interno;
                        this.Producto_seleccionado.Presentacion = productoEncontrado.PresentacionNombre;
                        this.Consulta_Existecias_Producto(this.Producto_seleccionado.Codigo);
                        document.getElementById("cantidad_dev").focus();                             
                        return
                    }
                    this.Estado_VentanaEmergente_Busqueda = true;
                }
    
            },
            Validacion_Campos(Tipo, Nombre, valor, obligatorio, cant_maxima) {
                /**
                 * @General
                 * Tipo : N = Númerio, Nombre = Descripción del campo, Valor = A evaluar, Obligatorio= Verdadero o falso,cant_maxima  = Aplica unicamente para un string y define la cantidad de caracteres 
                 * Devuelve un mensaje al usuario si las validaciones no se cumple y aborta, de lo contrario devuelve verdadero
                 */
                if (Tipo == 'ID') {
                    if (valor <= 0) {
                        this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else if (Tipo == 'N') {
    
                    if (valor <= 0 || valor === "") {
                        this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else {
                    if (valor == '' && obligatorio == true) {
                        this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
    
                        if (cant_maxima < valor.length) {
                            this.$vs.notify({
                        position:'top-center',
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: 'Campo ' + Nombre + ' no debe superar los ' + cant_maxima + ' carácteres.',
                            });
                            return false;
                        } else {
                            return true;
                        }
    
                    }
                }
            },
            /*****************************CONSULTAR INFORMACION*************/
            Consulta_Existecias_Producto(codigoProdudcto){
                this.axios.post('/app/v1_OrdenCompra/ConsultaRequisicion_Det', {  
                        bodega_fuente: this.cod_bodegaFuente.CODIGO,                      
                        Producto: codigoProdudcto,
                        tipo: 'P'                 
                    })
                    .then(resp => {
                        
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                        position:'top-center',
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: resp.data.mensaje,
                            })
                        } else {
                            if(resp.data.json && resp.data.json[0]){                                
                                this.Producto_seleccionado.existencias = resp.data.json[0].existencia;
                                this.Producto_seleccionado.minimo_despachante = resp.data.json[0].MINIMO_DESPACHANTE;
                            }
                        }
                    })
                    .catch(() => {})
            },
            Consultar_OrdenEnc() {
                if(!this.cod_bodegaFuente){
                    this.$vs.notify({
                        position:'top-center',
                            color: 'danger',
                            title: 'Inventario',
                            text: 'Seleccione una bodega fuente',
                        })
                    return
                }

                var Operacion = 'F'
                if(this.Id_estado_seleccionado=='P')
                    Operacion = 'T'

                this.axios.post('/app/v1_OrdenCompra/ConsultaRequisicion_Enc', {                        
                        Estado: this.Id_estado_seleccionado,
                        fecha_inicio: this.GetDateValue(this.fecha_inicio),
                        fecha_final: this.GetDateValue(this.fecha_final),
                        Operacion: Operacion,
                        Bodega_fuente: this.cod_bodegaFuente.CODIGO,
                        TipoMovimiento: '6,13'
    
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                        position:'top-center',
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_vista = [];
                        } else {
                            this.$vs.notify({
                                                position:'top-center',
                                                color: 'success',
                                                title: 'Inventario',
                                                text: 'Se actualizaron los movimientos exitosamente',
                                            })
                            this.Lista_vista = resp.data.json;
    
                        }
                    })
                    .catch(() => {
                        this.Lista_vista = [];
                    })
            },
            Consultar_OrdenDet() {
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/ConsultaRequisicion_Det', {
                        Requisicion_enc: this.Id_OrdenRequisicion
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                        position:'top-center',
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_detalle = [];
                        } else {
                            this.Lista_detalle = resp.data.json;
                            this.Lista_detalle = resp.data.json.map(x => {
                                x.COSTO_PROMEDIO = Number(x.COSTO_PROMEDIO)
                                x.COSTO_PROMEDIO_TOTAL = Number(x.COSTO_PROMEDIO) * (['P','E'].includes(this.Id_estado_seleccionado)? Number(x.CANTIDAD_PEDIDA): Number(x.CANTIDAD_ENTREGA))
                                return x
                            })
                            
    
                        }
                    })
                    .catch(() => {})
            },
            async Consultar_Bodega(Operacion, BodegasDespacho, BodegaTransito) {
                const url = this.$store.state.global.url
                await this.axios.post(url+'app/v1_OrdenCompra/consulta_bodega', {
                                                                        operacion: Operacion,
                                                                        BodegasDepacho: BodegasDespacho,
                                                                        BodegaTransito: BodegaTransito
                                                                     })
                           .then(resp => {
            
                                if (resp.data.codigo != 0) {
                                    this.$vs.notify({
                        position:'top-center',
                                        color: '#B71C1C',
                                        title: 'Inventario',
                                        text: resp.data.mensaje,
                                    })
                                } else {
                                    if(Operacion == 'F'){
                                        this.Lista_bodegas_destino_consulta = resp.data.json;                           
                                        this.Bodega_Vencidos = this.Lista_bodegas_destino_consulta.find((bodega)=>bodega.CODIGO == 5);    
                                    }
                                    else if(Operacion == 'H')
                                        this.Lista_bodegas_fuente = resp.data.json;                         
                                }
                            })
                           .catch(() => { })
        
            },
            async ConsultarSubBodega(Operacion,TipoBodegas) {
                if(!TipoBodegas || TipoBodegas.length == 0 ){return}

                const url = this.$store.state.global.url
                await this.axios.post(url+'app/v1_OrdenCompra/consulta_bodega', {
                                                                        operacion: Operacion,
                                                                        lista_tipo_bodegas: TipoBodegas
                                                                        })
                            .then(resp => {
            
                                if (resp.data.codigo != 0) {
                                    this.$vs.notify({
                                        position:'top-center',
                                        color: '#B71C1C',
                                        title: 'Bodegas',
                                        text: resp.data.mensaje,
                                    })
                                } else {
                                    this.Lista_bodegas_destino_consulta =
                                        this.Lista_bodegas_destino_consulta.concat(resp.data.json.filter(bodega=>bodega.BodegaFuente == 'S')) 

                                    this.Lista_bodegas_fuente =
                                        this.Lista_bodegas_fuente.concat(resp.data.json.filter(bodega=>bodega.BodegaFuente == 'N'))                  
                                
                                    this.Lista_bodegas_fuente.sort((a,b)=>Number(a.CODIGO)>=Number(b.CODIGO)?1:-1)
                                    this.Lista_bodegas_destino_consulta.sort((a,b)=>Number(a.CODIGO)>=Number(b.CODIGO)?1:-1)
                               
                                
                                }
                            })
                            .catch(() => { })
        
            },
            cargar_producto() {
    
                this.Limpiar_Campos();
    
                var res_variables = true;
                    res_variables = this.Validacion_Campos('ID', 'Bodega Destino', this.cod_bodegaDestino, true, 0);
                if (!res_variables){ return }
                    res_variables = this.Validacion_Campos('ID', 'Bodega Fuente', this.cod_bodegaFuente, true, 0);            
                if (!res_variables){ return }

                let tipoBodega = this.cod_bodegaFuente.CODIGO == 5 ? this.cod_bodegaDestino.TipoBodega : this.cod_bodegaFuente.TipoBodega
                
                this.axios.post('/app/v1_OrdenCompra/ConsultaRequisicion_Producto', {
                        Producto: this.producto_buscar.trim(),
                        TipoBodega: tipoBodega
                    })
                    .then(resp => {
    
                        if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                            this.producto = []

                            if(resp.data.json.length==1 && resp.data.json[0].tipo_error == "-1"){
                                this.$vs.notify({
                                    time: 5000,
                                    position:'top-center',
                                    color: 'danger',
                                    title: 'Producto',
                                    text: resp.data.json[0].descripcion,
                                })
                                return
                            }

                            resp.data.json.map(data => {
                                this.producto.push({
                                    ...data
                                })
                            })
    
                            this.Mostrar_resultado_producto(this.producto,this.producto_buscar.trim());
    
                        } else {
    
                            this.producto = []
                            this.$vs.notify({
                                position:'top-center',
                                color: 'danger',
                                title: 'Inventario',
                                text: (resp.data.codigo < 0) ? resp.data.descripcion : 'Código no existe, es un servicio ó no esta activo',
                            })
    
                        }
    
                    })
                    .catch(() => {
                        this.$vs.loading.close();
    
                    })
    
            },
            /*************************** ACTUALIZAR, ELIMINAR NUEVO REGISTRO */
            Registrar_NuevaOrdenEnc() {
                var res_variables = false;
                res_variables = this.Validacion_Campos('ID', 'Bodega Origen', this.id_bodega_fuente, true, 0);
    
                if (res_variables)
                    res_variables = this.Validacion_Campos('ID', 'Bodega destino', this.id_bodega_destino, true, 0);

                if (res_variables)
                    res_variables = this.Validacion_Campos('ID', 'Codigo Producto', this.id_bodega_destino, true, 0);

                if (res_variables)
                    res_variables = this.Validacion_Campos('ID', 'Cantidad Devolver', this.Cantidad_devolucion, true, 0);                    
    
                if (res_variables) {
    
                    const movimientoDevolucion = 6
                    const movimientoVencidoDescarte = 13
                    const movimientoVencidoCambio = 13
                    var TipoMovimiento = movimientoDevolucion
                    if(this.id_bodega_destino == 5 && this.enviar_bodega_vencido_descarte)
                        TipoMovimiento = movimientoVencidoDescarte
                    else if(this.id_bodega_destino == 5 && this.enviar_bodega_vencido_cambio)
                        TipoMovimiento = movimientoVencidoCambio
                    else if(this.id_bodega_fuente == 5)
                        TipoMovimiento = movimientoVencidoDescarte

                    this.axios.post('/app/v1_OrdenCompra/Requisicion_Enc', {
                            Requisicion_enc: 0,
                            Total: 0,
                            Cant_lineas: 0,
                            Observaciones: '',
                            Bodega_destino: this.cod_bodegaDestino.CODIGO,
                            Bodega_fuente: this.cod_bodegaFuente.CODIGO,
                            Estado: 'P',
                            Operacion: 'N',
                            TipoMovimiento: TipoMovimiento
                        })
                        .then(resp => {
    
                            if (resp.data.codigo == 0) {
                                this.InsertoEncabezado = true;
                                this.Id_OrdenRequisicion = resp.data.resultado;
                                this.Deshabilitar_campos = true;

                                this.AgregarDetalleEncabezado();
    
                            }
                        })
                }else{
                    this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Verifique el producto y la cantidad a devolver',
                        })
                    document.getElementById("busquedaProducto").focus();
                }
            },
            IngresarDetalle() {
                var res_variables = false;
                res_variables = this.Validacion_Campos('ID', 'Número Solicitud', this.Id_OrdenRequisicion, true, 0);
    
                if (res_variables)
                    res_variables = this.Validacion_Campos('ID', 'Producto', this.Producto_seleccionado.Codigo, true, 0);
    
                if (res_variables)
                    res_variables = this.Validacion_Campos('N', 'Cantidad solicitar', this.Cantidad_devolucion, true, 0);
    
                if (res_variables) {
                    this.axios.post('/app/v1_OrdenCompra/Requisicion_Det', {
                            Requisicion_det: 0,
                            Requisicion_enc: this.Id_OrdenRequisicion,
                            Producto: this.Producto_seleccionado.Codigo,
                            cant_pedida: this.Cantidad_devolucion,
                            cant_entregada: 0,
                            cant_recibida: 0,
                            Operacion: 'N'
                        })
                        .then(resp => {
    
                            if (resp.data.codigo == 0) {
                                this.InsertoEncabezado = false;
                                this.Deshabilitar_bodega_fuente = true;
                                this.Consultar_OrdenDet();
                                this.producto_buscar = '';
                                this.Cantidad_devolucion = '';
                                this.Limpiar_Campos();
                                document.getElementById("busquedaProducto").focus();
                            }
                        })
                }
            },
            Registrar_NuevaOrdenDet() {               
                
                let codigoProducto = this.Producto_seleccionado.Codigo
                let idProducto = this.Lista_detalle.findIndex( (producto) => producto.IDPRODUCTOFK.trim()===codigoProducto.trim())
                                                                           
                if(idProducto >= 0){
                    
                    this.$vs.dialog({
                        type: 'confirm',
                        color: 'danger',
                        title: 'Confirmación',
                        acceptText: 'Aceptar',
                        cancelText: 'Cancelar',
                        text: `¿Desea actualizar la cantidad a devolver del producto  ${this.Producto_seleccionado.Codigo} de ${this.Lista_detalle[idProducto].CANTIDAD_PEDIDA} a  ${this.Cantidad_devolucion}?`,
                        accept: () => {                              
                            this.IngresarDetalle()     
                        }

                    })
                }else{
                    this.IngresarDetalle()   
                }

                
            },
            Registrar_RecibidoDet(datos, operacion) {
                
                
               // 
    
                /* VALIDACION DE ARRAY */
    
                this.axios.post('/app/v1_OrdenCompra/Requisicion_Det', {
                        Requisicion_det: datos.IDREQUISICIONDET,
                        Requisicion_enc: datos.IDREQUISICIONENCFK,
                        Producto: datos.IDPRODUCTOFK,
                        cant_pedida: 0,
                        cant_entregada: 0,
                        cant_recibida: datos.CANTIDAD_RECIBIDA,
                        Operacion: operacion,
                    })
                    .then(resp => {
    
                        if (resp.data.codigo == 0) {
                            this.Consultar_OrdenDet();
                        }
                    })
    
            },
            AgregarDetalleEncabezado() {     
                if(!this.Cantidad_devolucion || this.Cantidad_devolucion <= 0){
                    this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'La cant. a devolver debe ser mayor a 0',
                    })
                    document.getElementById("cantidad_dev").focus();
                    return;                
                }    

                if (Number(this.Cantidad_devolucion)>Number(this.Producto_seleccionado.existencias)) {
                    this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'No puede devolver mas productos que los disponibles en existencia ('+this.Producto_seleccionado.existencias+')',                            
                    })
                    document.getElementById("cantidad_dev").focus();
                    return;   
                } 


                if (this.Id_OrdenRequisicion > 0) {
                    this.Registrar_NuevaOrdenDet();
                } else if(!this.InsertoEncabezado) {
                    this.Registrar_NuevaOrdenEnc();
                }else{
                    this.$vs.notify({
                                    color: 'danger',
                                    title: 'Error',
                                    text: 'Cierre esta ventana y busque la primer solicitud en estado Edición para continuar.',
                                    time: 3000,
                                    position: 'top-center'
                                })   
                } 
            },
            Eliminar_Registro(value) {
                /**
                 * @General
                 * Función eliminar registro;
                 */
    
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',
                    acceptText: 'Aceptar',
                    cancelText: 'Cancelar',
                    text: '¿Dar de baja producto  '+value.IDPRODUCTOFK +' - '+ value.DESCRIPCION_PRODUCTO + ' ' + value.DESCRIPCION_UNIDAD +' ? ',
                    accept: () => {
    
                        this.axios.post('/app/v1_OrdenCompra/Requisicion_Det', {
                                Requisicion_det: value.IDREQUISICIONDET,
                                Requisicion_enc: this.Id_OrdenRequisicion,
                                Producto: '0',
                                cant_pedida: 0,
                                cant_entregada: 0,
                                cant_recibida: 0,
                                Operacion: 'B'
                            })
                            .then(resp => {
    
                                if (resp.data.codigo == 0) {
                                    this.Consultar_OrdenDet();
                                    document.getElementById("busquedaProducto").focus();
                                }
                            })
    
                    }
                })
    
            },
            Finalizar_Devolucion() {                

                var res_variables = false;
                res_variables = this.Validacion_Campos('ID', 'Número Solicitud', this.Id_OrdenRequisicion, true, 0);
    
                if (res_variables) {
                    if (this.Lista_detalle.length <= 0) {
                        this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Debe tener al menos un producto requerido',
                        })
                        return;
                    }
    
                }
    
                if (res_variables) {
                    this.axios.post('/app/v1_OrdenCompra/Requisicion_Enc', {
                            Requisicion_enc: this.Id_OrdenRequisicion,
                            Total: 0,
                            Cant_lineas: this.Lista_detalle.length,
                            Observaciones: this.Observaciones,            
                            Estado: 'E',
                            Operacion: 'F'
                        })
                        .then(resp => {                            
                            if (resp.data.codigo != 0) {
                                    //Prueba mensaje
                            } else {
                                this.Estado_Emergente = false;
                                this.Consultar_OrdenEnc();
                            }
                        })
                }
            }
        }
    
    }
    </script>
    <style>
    .inventario-popup.con-vs-popup .vs-popup {
        width: 90% !important;
    }
    tfoot .foot {
        border-top: 3px dotted rgb(160 160 160);
        background-color: #2c5e77;
        color: #fff;
      }
      
    </style>
      
    <style scoped>
      tfoot th {
        text-align: right;
      }
      
      tfoot td {  
        font-weight: bold;
      }
    </style>