<template>
<div class="expediente-evolucion-container">
    <vs-popup :title="'Registro de nuevo expediente:'" :active.sync="popupActiveExpediente">
        <NuevoExpediente v-bind="EncabezadoAdmision" @onCancel="popupActiveExpediente = false" @onSaved="RecargarAdmision"></NuevoExpediente>
    </vs-popup>
    <vs-popup id="popupTallaPeso" :title="'Registro de peso y estatura:'" :active.sync="popupActiveRegistroTallaPeso" width="450px">
        <RegistroPesoTalla v-bind="EncabezadoAdmision" @onCancel="popupActiveRegistroTallaPeso = false" @onSaved="ActualizarPesoTallaEnc" :modalMode="false"></RegistroPesoTalla>
    </vs-popup>

    <vs-popup :active.sync="popupEstadistica" title="Estadísticas">
        <EstadisticaSOP ref="estadisticaPop" v-bind="EncabezadoAdmision" />
    </vs-popup>
    <div class="main-expediente">
        <div class="Encabezado">
            <Encabezado ref="EncabezadoExpediente" @onCrearExpediente="popupActiveExpediente = true" @onLoadedData="CargarAdmision"></Encabezado>
        </div>

        <div class="Contenido p-2">
            <div v-if="SelectedOption > 0" class="pb-2 sticky flex">
                <div class="pr-2 pl-2" style="display: grid; place-items: center; width: 75px">
                    <vs-tooltip text="Menú">
                        <vs-button :class="'buttonTab'" :color="'primary'" :type="'filled'" @click="RegresarMenu()">
                            <div>
                                <font-awesome-icon :icon="['fas', 'right-from-bracket']" rotation="180" style="font-size: 30px;" />
                            </div>
                        </vs-button>
                    </vs-tooltip>
                </div>
                <DxTabs :selected-index="SelectedOption-1" height="10%" width="calc(100% - 75px)" :rtl-enabled="false" orientation="horizontal" styling-mode="secondary" :icon-position="'top'" :show-nav-buttons="true" :scroll-by-content="true" @item-click="(e)=>OpcionSeleccionada(navigation[e.itemIndex])">
                    <DxItem v-for="(item, index) in navigation" :key="index" template="tabButton" :disabled="navigation[index].disabled(EncabezadoAdmision)" />
                    <template #tabButton="{index}">
                        <vs-tooltip :text="navigation[index].text">

                            <vs-button :class="navigation[index].id === SelectedOption ? 'buttonTabSeleccionada': 'buttonTab'" :color="navigation[index].id === SelectedOption ? 'success' : 'primary'" :type="navigation[index].id === SelectedOption ? 'line' : 'filled'" @click="OpcionSeleccionada(navigation[index])">
                                <div>
                                    <font-awesome-icon :icon="['fas', navigation[index].icon]" style="font-size: 30px;" />
                                </div>
                            </vs-button>
                        </vs-tooltip>
                    </template>
                </DxTabs>
            </div>
            <div v-if="EncabezadoAdmision.NumeroExpediente">
                <vs-alert v-if="EncabezadoAdmision.Peso=='' && EncabezadoAdmision.NumeroExpediente !== '0'" :active.sync="Alertas[0]" color="danger" icon-pack="feather" icon="icon-info" closable close-icon="icon-x" class="mb-1" @click="popupActiveRegistroTallaPeso=true">
                    No ha ingresado <b>el peso del paciente</b>, este dato es de vital importancia.
                </vs-alert>
                <vs-alert v-if="EncabezadoAdmision.Estatura=='' && EncabezadoAdmision.NumeroExpediente !== '0'" :active.sync="Alertas[1]" color="danger" icon-pack="feather" icon="icon-info" closable close-icon="icon-x" class="mb-1" @click="popupActiveRegistroTallaPeso=true">
                    No ha ingresado <b>la estatura del paciente</b>, este dato es de vital importancia.
                </vs-alert>
                <vs-alert v-if="EncabezadoAdmision.FechaEgreso && EncabezadoAdmision.NumeroExpediente !== '0'" :active.sync="Alertas[1]" color="danger" icon-pack="feather" icon="icon-info" closable close-icon="icon-x" class="mb-1">
                    La admision tiene registrado egreso médico, no podrá ingresar nuevas notas sin previa autorización y reactivación por parte de coordinación.
                </vs-alert>
            </div>
            <div>
                <vs-alert v-if="EncabezadoAdmision.NumeroExpediente === '0'" :active.sync="Alertas[1]" color="danger" icon-pack="feather" icon="icon-info" closable close-icon="icon-x" class="mb-1">
                    El paciente <b>no tiene expediente</b> de ingreso.
                </vs-alert>
            </div>

            <vx-card :title="Title">
                <div v-show="SelectedOption === 0 ">
                    <div v-if="EncabezadoAdmision.NumeroExpediente && SelectedOption === 0 && permisoIntegracion" class="buttons vx-card stickyIntegracion mb-4">
                        <div class="p-2 w-full sm:w-1/2 md:w-1/2 lg:w-1/3 xl:w-1/3 div-button" v-for="(item, index) in buttons" v-bind:key="index">
                            <vs-button class="button" :color="item.value === 1 ? 'danger' : 'primary'" type="filled" @click="item.click">
                                <font-awesome-icon :icon="['fas', item.icon]" class="pr-2" style="font-size: 16px" />
                                <span>{{item.name}}</span>
                            </vs-button>
                        </div>
                    </div>
                    <vs-alert v-if="!Boolean(EncabezadoAdmision.SerieAdmision)  || !Boolean(EncabezadoAdmision.CodigoAdmision) || !Boolean(EncabezadoAdmision.NumeroExpediente)" :active.sync="Alertas[0]" color="danger" icon-pack="feather" icon="icon-info" closable close-icon="icon-x" class="mb-1">
                        No ha ingresado <b>el código de la admisión</b>, este es necesario para poder habilitar el menú
                    </vs-alert>
                    <Menu v-bind="EncabezadoAdmision" :menu="this.newNavigation" @onSelectedItem="OpcionSeleccionada">
                    </Menu>
                </div>

                <div v-show="menuActual.length !== 0">
                    <Menu v-bind="EncabezadoAdmision" :menu="menuActual" @onSelectedItem="OpcionSeleccionada" />
                </div>

                <div v-if="SelectedOption === 1 ">
                    <Antecedentes v-bind="EncabezadoAdmision" />
                </div>
                <div v-if="SelectedOption == 2">
                    <Diagnostico v-bind="EncabezadoAdmision" />
                </div>
                <div v-if="SelectedOption == 3">
                    <Evolucion v-bind="EncabezadoAdmision" />
                </div>
                <div v-if="SelectedOption == 4">
                    <OrdenesMedicas v-bind="EncabezadoAdmision" />
                </div>
                <div v-if="SelectedOption == 5">
                    <ControlOrdenes v-bind="EncabezadoAdmision" />
                </div>
                <div v-if="SelectedOption === 6">
                    <Signos v-bind="EncabezadoAdmision" @onWeightButton="popupActiveRegistroTallaPeso=true" />
                </div>
                <div v-if="SelectedOption == 7">
                    <Dietas v-bind="EncabezadoAdmision" />
                </div>
                <div v-if="SelectedOption == 8">
                    <Salud v-bind="EncabezadoAdmision" />
                </div>
                <div v-if="SelectedOption === 9">
                    <Egreso v-bind="EncabezadoAdmision" @onSaved="RecargarAdmision" />
                </div>
                <div v-if="SelectedOption === 10">
                    <SalaOperaciones v-bind="EncabezadoAdmision" />
                </div>
                <div v-if="SelectedOption === 20">
                    <Laboratorio v-bind="EncabezadoAdmision" />
                </div>
                <div v-if="SelectedOption === 21">
                    <Examen v-bind="EncabezadoAdmision" />
                </div>
                <div v-if="SelectedOption === 23">
                    <Coex v-bind="EncabezadoAdmision" />
                </div>
                <div v-if="SelectedOption === 24">
                    <Hospitalizacion v-bind="EncabezadoAdmision" />
                </div>
                <div v-if="SelectedOption === 26">
                    <Cateteres v-bind="EncabezadoAdmision" />
                </div>
            </vx-card>
        </div>
    </div>
    <vs-popup :active.sync="verIntegracion" title="Reporte auditoría" width="auto" height="auto">
        <Integracion ref="IntegracionPDF" v-bind="EncabezadoAdmision" />
    </vs-popup>
</div>
</template>

<script>

import {
    navigation,
    visorDocumentos
} from './data.js'
import {
    DxTabs,
    DxItem
} from 'devextreme-vue/tabs';

import Encabezado from './EXP001.vue'
import Antecedentes from './EXP003.vue'
import NuevoExpediente from './EXP006.vue'
import Evolucion from './EXP005.vue'
import Diagnostico from './EXP004.vue'
import OrdenesMedicas from './EXP010.vue'
import RegistroPesoTalla from './EXP009.vue'
import Dietas from './EXP011.vue'
import Egreso from './EXP015.vue'
import Signos from './EXP008.vue'
import Menu from './ExpedienteMenu.vue'
import Laboratorio from './EXP022.vue'
import Examen from './EXP023.vue'
import Hospitalizacion from './EXP007.vue'
import Coex from './EXP024.vue'
import ControlOrdenes from './EXP025.vue'
import Salud from './EXP021.vue'
import SalaOperaciones from './EXP027.vue'
import Cateteres from './EXP026.vue'
import EstadisticaSOP from './EXP028.vue'
import Integracion from './IntegracionReportes.vue'

export default {
    name: 'MainExpedienteEvolucion',
    components: {
        DxTabs,
        DxItem,
        Encabezado,
        Antecedentes,
        NuevoExpediente,
        Evolucion,
        Diagnostico,
        OrdenesMedicas,
        RegistroPesoTalla,
        Dietas,
        Egreso,
        Signos,
        Menu,
        Laboratorio,
        Examen,
        Hospitalizacion,
        Coex,
        ControlOrdenes,
        Salud,
        SalaOperaciones,
        Cateteres,
        EstadisticaSOP,
        Integracion
    },
    data() {
        ///Configuración del Menú TODO: obtener la configuracion por rol de base de datos y hacer dinamica la carga de componentes ¿routerview?

        return {
            navigation,
            newNavigation: {},
            EncabezadoAdmision: {},
            SelectedOption: 0,
            Title: 'Expediente',
            menuStack: [],
            menuActual: [],
            popupActiveExpediente: false,
            popupActiveRegistroTallaPeso: false,
            Alertas: [true, true],
            visorDocumentos,
            popupEstadistica: false,

            buttons: [{
                name: 'Integrar PDF',
                icon: 'file-pdf',
                value: 1,
                click: () => {
                    if (this.permisoReporteIntegracion) {
                        this.verIntegracion = true
                    } else {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Permiso faltante',
                            acceptText: 'Aceptar',
                            text: 'Actualmente no cuenta con permiso para visualizar el(los) siguiente(s) reporte(s): ' + this.permisosFaltantes,
                            buttonCancel: 'border',
                            clientWidth: 100,
                            accept: () => {},
                        })
                    }
                }
            }, {
                name: 'Laboratorios',
                icon: 'vial-circle-check',
                value: 2,
                click: () => {
                    this.integrarLaboratorios();
                }
            }, {
                name: 'Exámenes',
                icon: 'radiation',
                value: 3,
                click: () => {
                    this.integrarExamenes()
                }
            }, ],

            verIntegracion: false,

            permisoIntegracion: false,
            permisoLaboratorios: false,
            permisoExamenes: false,
            permisoReporteIntegracion: true,
            permisosFaltantes: ''
        }
    },
    methods: {
        OpcionSeleccionada(data) {
            if (data.id === 25) {
                this.popupActiveRegistroTallaPeso = true
            } else if (data.id === 27) {
                this.$refs.estadisticaPop.getMounted();
                this.popupEstadistica = true
            } else if (data.id === 22) {
                let comando = visorDocumentos + ' ' + this.EncabezadoAdmision.SerieAdmision + ' ' + this.EncabezadoAdmision.CodigoAdmision + ' SI NO - ' + this.EncabezadoAdmision.Nombre
                this.$socke_up(comando)
            } else {

                this.SelectedOption = data.id;
                this.Title = data.title;
                if (this.menuStack.length > 1) {
                    this.menuStack.pop()
                }
                this.menuStack.push(data);
                this.menuActual = [];
                if (data.submenu) {
                    this.menuActual = data.submenu;
                }
            }
        },
        CargarAdmision(data) {
            this.Alertas = [true, true]
            this.EncabezadoAdmision = data
            this.SelectedOption = 0
            this.Title = "Expediente";
            this.menuStack = [];
            this.menuActual = [];
            this.menuStack.push('navigation');
        },
        RecargarAdmision() {
            this.Alertas = [true, true]
            this.$refs.EncabezadoExpediente.CargarAdmision()
            this.popupActiveExpediente = false
        },
        ActualizarPesoTallaEnc(data) {
            this.$refs.EncabezadoExpediente.Encabezado.Peso = data.Peso
            this.$refs.EncabezadoExpediente.Encabezado.Estatura = data.Estatura
            this.$refs.EncabezadoExpediente.Encabezado.IMC = data.IMC
            this.$refs.EncabezadoExpediente.Encabezado.DescripcionIMC = data.DescripcionIMC
            this.popupActiveRegistroTallaPeso = false
        },
        RegresarMenu() {
            this.menuStack.pop();
            let i = this.menuStack[this.menuStack.length - 1];

            if (i === 'navigation') {
                this.Title = "Expediente";
                this.SelectedOption = 0;
                this.menuActual = [];
            } else {
                this.Title = i.title
                this.SelectedOption = i.id;
                this.menuActual = [];
                if (i.submenu) {
                    this.menuActual = i.submenu;
                }
            }
        },

        async integrarLaboratorios() {
            if (this.permisoLaboratorios) {
                await this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaListadoLaboratorios', {
                        Serie: this.EncabezadoAdmision.SerieAdmision,
                        Admision: this.EncabezadoAdmision.CodigoAdmision,
                    })
                    .then(async resp => {
                        if (resp.data.json.length > 0) {

                            //Se obtienen todo el listado de ordenes, pero separados por exámenes
                            var ordenesExamenes = []
                            for (const i of resp.data.json) {
                                if (i.LineaOrden !== '-1' && i.SerieAdmision === this.EncabezadoAdmision.SerieAdmision && i.Admision === this.EncabezadoAdmision.CodigoAdmision) {
                                    ordenesExamenes.push({
                                        Tipo: i.TIPO,
                                        Orden: i.Codigo
                                    })
                                }
                            }

                            // Se filtra el listado de ordenes para no tener ninguna orden repetida
                            var ordenes = [];

                            ordenesExamenes.filter((item) => {
                                var i = ordenes.findIndex(x => (x.Tipo == item.Tipo && x.Orden == item.Orden));
                                if (i <= -1) {
                                    ordenes.push(item);
                                }
                                return null;
                            });

                            var nombreReporte = 'Laboratorio'

                            //Se obtienen los parámetros del reporte de laboratorios
                            var parametros = await this.$recupera_parametros_reporte(nombreReporte)

                            var obj = [] //Variable para almacenar el array de los reportes de laboratorio

                            for (const i of ordenes) {
                                var x = await this.$prepara_valores_reporte({
                                    Nombre: nombreReporte,
                                    Data_source: i,
                                    Data_report: parametros
                                })

                                obj.push({
                                    Nombre: nombreReporte,
                                    Opciones: {
                                        ...x,
                                        tiporeporte: 'application/pdf',
                                        ConfigCorreo: null
                                    }
                                })
                            }
                            if (obj.length > 0) {
                                this.$reporte_unificador(obj)
                            } else {
                                this.$vs.dialog({
                                    type: 'alert',
                                    color: '#ed8c72',
                                    title: 'Laboratorios no encontrados',
                                    acceptText: 'Aceptar',
                                    text: 'La admsión actual no cuenta con laboratorios para generar.',
                                    buttonCancel: 'border',
                                    clientWidth: 100,
                                    accept: () => {},
                                })
                            }
                        } else {
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#ed8c72',
                                title: 'Laboratorios no encontrados',
                                acceptText: 'Aceptar',
                                text: 'La admsión actual no cuenta con laboratorios para generar.',
                                buttonCancel: 'border',
                                clientWidth: 100,
                                accept: () => {},
                            })
                        }
                    })
            } else {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'Permiso faltante',
                    acceptText: 'Aceptar',
                    text: 'Actualmente no cuenta con permiso para visualizar el reporte de laboratorio (Laboratorio).',
                    buttonCancel: 'border',
                    clientWidth: 100,
                    accept: () => {},
                })
            }
        },

        async integrarExamenes() {
            if (this.permisoExamenes) {
                await this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaListadoRadiologias', {
                        Serie: this.EncabezadoAdmision.SerieAdmision,
                        Admision: this.EncabezadoAdmision.CodigoAdmision,
                    })
                    .then(async resp => {
                        if (resp.data.json.length > 0) {

                            //Se obtienen todo el listado de ordenes, pero separados por exámenes
                            var radiologiasExamenes = []
                            for (const i of resp.data.json) {
                                var tipoOrden = i.TipoOrden.replace(/[0-9]/g, '') //Toma el tipo de orden y elimina los números

                                var ordenCorrecta = (tipoOrden === 'RA' || tipoOrden === 'CAR' || tipoOrden === 'PAT' || tipoOrden === 'SO' || tipoOrden === 'EN') //Compara el tipo de orden y determina si es de los tipos que desean en la integración

                                if (i.LineaOrden !== '-1' && i.SerieAdmision === this.EncabezadoAdmision.SerieAdmision && i.Admision === this.EncabezadoAdmision.CodigoAdmision && ordenCorrecta) {
                                    radiologiasExamenes.push({
                                        tipoorden: i.TipoOrden,
                                        orden: i.Orden
                                    })
                                }
                            }

                            // Se filtra el listado de ordenes para no tener ninguna orden repetida
                            var ordenes = [];
                            radiologiasExamenes.filter((item) => {
                                var i = ordenes.findIndex(x => (x.tipoorden == item.tipoorden && x.orden == item.orden));
                                if (i <= -1) {
                                    ordenes.push(item);
                                }
                                return null;
                            });

                            var nombreReporte = 'Informes por Orden'

                            //Se obtienen los parámetros del reporte de laboratorios
                            var parametros = await this.$recupera_parametros_reporte(nombreReporte)

                            var obj = [] //Variable para almacenar el array de los reportes de laboratorio
                            for (const i of ordenes) {
                                var x = await this.$prepara_valores_reporte({
                                    Nombre: nombreReporte,
                                    Data_source: i,
                                    Data_report: parametros
                                })

                                obj.push({
                                    Nombre: nombreReporte,
                                    Opciones: {
                                        ...x,
                                        tiporeporte: 'application/pdf',
                                    }
                                })
                            }

                            if (obj.length > 0) {
                                this.$reporte_unificador(obj)
                            } else {
                                this.$vs.dialog({
                                    type: 'alert',
                                    color: '#ed8c72',
                                    title: 'Exámenes no encontrados',
                                    acceptText: 'Aceptar',
                                    text: 'La admsión actual no cuenta con exámenes para generar.',
                                    buttonCancel: 'border',
                                    clientWidth: 100,
                                    accept: () => {},
                                })
                            }
                        } else {
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#ed8c72',
                                title: 'Exámenes no encontrados',
                                acceptText: 'Aceptar',
                                text: 'La admsión actual no cuenta con exámenes para generar.',
                                buttonCancel: 'border',
                                clientWidth: 100,
                                accept: () => {},
                            })
                        }
                    })
            } else {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'Permiso faltante',
                    acceptText: 'Aceptar',
                    text: 'Actualmente no cuenta con permiso para visualizar el reporte de exámenes (Informe por Orden).',
                    buttonCancel: 'border',
                    clientWidth: 100,
                    accept: () => {},
                })
            }
        },

        async validarPermisosReportes() {
            //Ciclo para determinar si cuenta con los permisos necesarios a los reportes
            for await (const i of this.$refs['IntegracionPDF'].reportesBackOffice) {
                if (i.NombreReporte !== '' && i.Reporte !== '7') {
                    this.$validar_funcionalidad('/REPORTE/REPORT001', i.NombreReporte.toUpperCase(), (d) => {
                        if (d.status === false) {
                            if (this.permisosFaltantes === '') {
                                this.permisosFaltantes = i.NombreReporte
                            } else {
                                this.permisosFaltantes = this.permisosFaltantes + ', ' + i.NombreReporte
                            }
                            this.permisoReporteIntegracion = false
                        }
                    })
                } else if (i.Reporte === '7') { //Valida el caso de los reportes que se utilizan para el reporte de Sala de Operaciones
                    this.$validar_funcionalidad('/REPORTE/REPORT001', 'REGISTRO ANESTÉSICO', (d) => {
                        if (d.status === false) {
                            if (this.permisosFaltantes === '') {
                                this.permisosFaltantes = 'Registro Anestésico'
                            } else {
                                this.permisosFaltantes = this.permisosFaltantes + ', ' + 'Registro Anestésico'
                            }
                            this.permisoReporteIntegracion = false
                        }
                    })

                    this.$validar_funcionalidad('/REPORTE/REPORT001', 'RECORD OPERATORIO', (d) => {
                        if (d.status === false) {
                            if (this.permisosFaltantes === '') {
                                this.permisosFaltantes = 'Record Operatorio'
                            } else {
                                this.permisosFaltantes = this.permisosFaltantes + ', ' + 'Record Operatorio'
                            }
                            this.permisoReporteIntegracion = false
                        }
                    })
                }
            }
        }
    },
    created() {

    },
    mounted() {
        this.newNavigation = navigation

        this.$validar_funcionalidad('/EXPEDIENTE/EXP002', 'INTEGRACIONPDF', (d) => {
            this.permisoIntegracion = d.status
        })

        this.$validar_funcionalidad('/REPORTE/REPORT001', 'LABORATORIO', (d) => {
            this.permisoLaboratorios = d.status
        })

        this.$validar_funcionalidad('/REPORTE/REPORT001', 'INFORMES POR ORDEN', (d) => {
            this.permisoExamenes = d.status
        })

        this.validarPermisosReportes()

    },
    watch: {
        'EncabezadoAdmision.VisitasPrevias'(newval) {
            if (newval !== undefined && newval !== '') {
                this.newNavigation = navigation.map(x => x.id === 24 ? {
                    ...x,
                    text: x.text + ' (' + this.EncabezadoAdmision.VisitasPrevias + ')'
                } : x)
            } else {
                this.newNavigation = navigation.map(x => x.id === 24 ? {
                    ...x,
                    text: x.title
                } : x)
            }
        },
    }
}
</script>

<style scoped>
.main-expediente {
    display: grid;
    height: calc(100vh - 49px - 67px);
    grid-template-areas: "Encabezado Encabezado""Opciones Contenido";
    grid-template-columns: 0fr 1fr;
    grid-template-rows: 85px 1fr;
    margin: 0;
    padding: 0;
    right: 0;
    background-color: rgb(255, 255, 255);
    position: fixed;
    left: 80px;
    width: calc(100%-80px);
    min-width: 400px;
}

.Encabezado {
    grid-area: Encabezado;
    overflow-y: hidden;
    font-size: small;
    /* background-color: #d0e1f9; */
    background: rgb(208, 225, 249);
    background: linear-gradient(90deg, rgba(208, 225, 249, 1) 67%, rgba(119, 175, 255, 1) 94%, rgba(2, 0, 36, 1) 100%);
    color: #2f496e;
}

.Contenido {
    grid-area: Contenido;
    overflow-y: auto;
}

.Opciones {
    grid-area: Opciones;
    overflow-y: auto;
    overflow-x: hidden;
    width: max-content;
}

.Opciones>#toolbar {
    background-color: #f4eade;
    background-color: #2988bc;
}

/**Modal actualizacion de peso y talla */
#popupTallaPeso .vs-popup {
    width: 400px !important;
}

/**Barra de visualización de exámens, laboratorios e integración del expediente PDF */
.button {
    height: 40px;
    width: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px !important;
}

.button:hover {
    background-color: blue !important;
}

.div-button {
    display: flex;
    justify-content: center;
}

.stickyIntegracion {
    position: -webkit-sticky !important;
    /* for browser compatibility */
    position: sticky !important;
    top: 0px;
    background-color: rgb(117, 177, 255);
    z-index: 500;
}

.buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

/**Barra de menu */
.buttonTab {
    height: 50px;
    width: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px !important;
}

.buttonTab:hover {
    background-color: blue !important;
}

.buttonTabSeleccionada {
    height: 50px;
    width: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px !important;
}

.buttonTabSeleccionada:hover {
    background-color: transparent !important;
}

.sticky {
    position: -webkit-sticky !important;
    /* for browser compatibility */
    position: sticky !important;
    top: 0px;
    z-index: 500;
}

</style>

<style>
.expediente-popup .dx-popup-title {
    background-color: #f4eade !important;
    color: #2f496e !important;
}

.expediente-evolucion-container .dx-datagrid-headers td {
    vertical-align: middle !important;
}

.expediente-evolucion-container .dx-resizable {
    display: inline-grid;
}

.expediente-evolucion-container .dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed)>td {
    background-color: #00ccff !important;
    color: black !important;
}

.expediente-evolucion-container .dx-datagrid-rowsview .dx-row.dx-data-row.dx-column-lines.dx-state-hover>td {
    background-color: #79e4ff !important;
    color: black !important;
    font-size: 16px;
}

.expediente-evolucion-container .dx-datagrid-headers {
    background-color: linen !important;
    color: black !important;
    font-weight: bold;
}

.expediente-evolucion-container td {
    vertical-align: middle !important;
}

.dx-overlay-wrapper.dx-popup-wrapper.dx-dropdowneditor-overlay.dx-popover-wrapper.dx-popover-without-title.dx-lookup-popup-wrapper.dx-lookup-popup-search.dx-position-bottom {
    z-index: 999999 !important;
}
</style>