<template>
<div>
    <form @submit="handleSubmit">
        <DxForm :ref="formulario" :form-data.sync="formulario" label-mode="floating">
            <DxFormGroupItem :col-count="2">
                <DxFormGroupItem :col-count="2">
                    <DxFormItem :col-span="2" data-field="Cuenta" editor-type="dxSelectBox" :editor-options="{ readOnly: true, width: 'auto', searchEnabled: true, items: cuentas,  displayExpr: 'Valor', valueExpr: 'Codigo' }" :validationRules="[{ type: 'required' }]" />

                    <DxFormItem data-field="ArchivoTexto" editor-type="dxCheckBox" v-if="mostrarBoton">
                        <DxFormLabel text="Archivo de texto" />
                    </DxFormItem>
                    <DxFormItem data-field="ArchivoExtendido" editor-type="dxCheckBox" v-if="mostrarBoton">
                        <DxFormLabel text="Archivo extendido de Excel" />
                    </DxFormItem>
                    <DxFormItem :col-span="2" template="boton" v-if="mostrarBoton" />
                </DxFormGroupItem>

                <DxFormGroupItem>
                    <DxFormItem template="lotes" />
                </DxFormGroupItem>
            </DxFormGroupItem>

            <template #boton="{}">
                <div style="display: flex; flex-wrap: wrap; justify-content: center;">
                    <DxButton :visible="true" width="auto" height="36px" type="success" class="p-1" styling-mode="contained" @click="BuscarRangoCheques()">
                        <font-awesome-icon class="mr-2" :icon="['fas', 'print']" style="font-size: 18px; vertical-align: middle" />
                        <span>Generar</span>
                    </DxButton>
                </div>
            </template>

            <template #texto="{}">
                <div>
                    <h3 style="color: red;">Lote generado automáticamente por Rechazo de cuenta bancaria</h3>
                </div>
            </template>

            <template #lotes="{}">
                <div class="pb-2">
                    <DxDataGrid :ref="gridDocumentos" v-bind="DefaultDxGridConfiguration" :data-source="lotes" :paging="{ enabled: false }" :searchPanel="{ visible: false} " :headerFilter="{ visible: false, allowSearch: false }" :width="'100%'" height="170" :on-row-click="SeleccionarLote">
                        <DxDataGridSelection mode="single" />
                        <DxDataGridScrolling mode="virtual" />

                        <DxDataGridColumn width="30%" data-field="Id" alignment="center" data-type="string" />
                        <DxDataGridColumn width="70%" data-field="Lote" alignment="center" data-type="string" />
                    </DxDataGrid>
                </div>
            </template>
        </DxForm>
    </form>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration,
    customizeTextValores
} from './data'

import {
    saveAs
} from 'file-saver';

import * as XLSX from 'xlsx';

const formConsulta = 'formConsulta'
const formCheque = 'formCheque'
const gridCheques = 'gridCheques'
const gridDetalle = 'gridDetalle'
const gridDocumentos = 'gridDocumentos'

export default {
    name: 'AcreditamientosLotes',
    components: {},
    data() {
        return {
            DefaultDxGridConfiguration,
            formConsulta,
            formCheque,
            gridCheques,
            gridDetalle,
            gridDocumentos,

            formulario: {
                Cuenta: null, // Codigo de la cuenta de banco de 2 dígitos
                ArchivoTexto: true,
                ArchivoExtendido: true,
            },
            cuentas: [],

            cheques: [],

            //Solo se asigna con status para que no cause error al validarlo cuando se cargan los cheques para mostrar el botón de anular
            infoCheque: {
                Status: null
            },
            periodo: null,
            cuentaIVACobrar: null,
            porcentajeIVA: null,
            retencion: null,
            indiceSeleccionado: null, //Variable para saber cual es el índice en la tabla de cheques del cheque seleccionado

            formularioCheque: {
                DescripcionPeriodo: null
            },
            valoresTabla: null,
            periodos: null,

            notasAcreditamiento: [],

            acreditamientosGenerar: [],
            nombreEmpresa: null,

            acreditamientos: [],

            lotes: [],
            mostrarBoton: null,
            infoRangoCheques: null,
        }
    },
    props: {
        TipoCheque: null,
        Corporativo: null
    },
    methods: {
        customizeTextValores,
        handleSubmit(e) {
            e.preventDefault()
        },

        ValidarCheque(cheque) {
            if (cheque !== null && cheque > 0) {
                return true
            }
            return false
        },

        CargarCuentas() {
            this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 1
                })
                .then(resp => {
                    this.cuentas = resp.data.json

                    // this.formulario.Cuenta = this.cuentas[0].Codigo
                })
        },

        async CargarAcreditamientos() {
            await this.axios.post('/app/v1_bancos/AcreditamientoLotes', {
                    Opcion: 1,
                })
                .then(resp => {
                    this.mostrarBoton = false
                    this.formulario.Cuenta = null
                    this.lotes = []
                    if (resp.data.json.length > 0) {
                        this.lotes = resp.data.json
                    } else {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Sin lotes pendientes',
                            acceptText: 'Aceptar',
                            text: 'No existen lotes pendientes de generar',
                            buttonCancel: 'border',
                            accept: () => {},
                        })
                    }

                })
        },

        async BuscarRangoCheques() {
            await this.axios.post('/app/v1_bancos/AcreditamientoLotes', {
                    Opcion: 2,
                    Lote: this.formulario.LoteSeleccionado.Id,
                    Cuenta: this.formulario.LoteSeleccionado.PagoCuenta
                })
                .then(resp => {
                    this.infoRangoCheques = null
                    if (resp.data.json.length > 0) {
                        this.infoRangoCheques = resp.data.json[0]
                        this.BuscarLista()
                    } else {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Sin información',
                            acceptText: 'Aceptar',
                            text: 'No se han emitido cheques para este envío o no existe el envío.',
                            buttonCancel: 'border',
                            accept: () => {},
                        })
                    }

                })
        },

        async BuscarLista() {
            await this.axios.post('/app/v1_bancos/AcreditamientoLotes', {
                    Opcion: 3,
                    ChequeInicial: this.infoRangoCheques.MinCheque,
                    ChequeFinal: this.infoRangoCheques.MaxCheque,
                    Tipo: this.TipoCheque,
                    Cuenta: this.formulario.LoteSeleccionado.PagoCuenta
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.cheques = resp.data.json

                        this.Impresion()
                    }
                })
        },

        SeleccionarLote(e) {
            this.formulario.LoteSeleccionado = e.data
            this.mostrarBoton = true

            this.formulario.Cuenta = e.data.PagoCuenta
        },
        async CargarOpciones() {
            await this.axios.post('/app/v1_bancos/Opciones', {
                    Opcion: 1
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.nombreEmpresa = resp.data.json[0].Nombre
                    }
                })
        },

        async ListarArchivo() {
            await this.axios.post('/app/v1_bancos/AcreditamientoLotes', {
                    Opcion: 4,
                    Cuenta: this.formulario.LoteSeleccionado.PagoCuenta,
                    Tipo: this.TipoCheque,
                    Lote: this.formulario.LoteSeleccionado.Id
                })
                .then(async resp => {
                    if (resp.data.json.length > 0) {
                        this.listaArchivo = resp.data.json

                        if (this.formulario.ArchivoTexto) {
                            await this.GenerarArchivo(this.listaArchivo)
                        }

                        if (this.formulario.ArchivoExtendido) {
                            await this.ExportarAExcel(this.listaArchivo)
                        }

                        this.CargarAcreditamientos()
                    }
                })
        },

        async Impresion() {
            let actualizar = {
                Impresion: [],
                Reimpresion: [],
                NotaDebito: []
            }
            for (let i = 0; i < this.cheques.length; i++) {
                if (this.cheques[i].Impresion == null) {
                    actualizar.Impresion.push({
                        Opcion: 4,
                        ChequeInicial: this.cheques[i].Numero,
                        Cuenta: this.formulario.Cuenta,
                        Tipo: this.TipoCheque,
                        Usuario: this.Corporativo
                    })
                } else {
                    actualizar.Reimpresion.push({
                        Opcion: 5,
                        ChequeInicial: this.cheques[i].Numero,
                        Cuenta: this.formulario.Cuenta,
                        Tipo: this.TipoCheque,
                        Usuario: this.Corporativo
                    })
                }
            }

            await this.axios.post('/app/v1_bancos/AcreditamientoAgrupado', {
                    ...actualizar
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ActualizarLote()
                    }
                })
        },

        async ActualizarLote() {
            await this.axios.post('/app/v1_bancos/AcreditamientoLotes', {
                    Opcion: 5,
                    Lote: this.formulario.LoteSeleccionado.Id,
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListarArchivo()
                    }
                })
        },

        async GenerarArchivo(info) {
            let contenedorArchivo = '';
            let contador = 1;
            let montoTotal = 0;
            let nombreArchivo = 'TransferenciaBancaria_Lote_' + this.formulario.LoteSeleccionado.Id + '_' + this.formatDate(new Date()) + '.txt';
            // let nombreArchivo = `TransferenciaBancaria-${new Date().toLocaleString()}.txt`;

            // Cabezera (se puede modificar según sea necesario)
            // TituloParaArchivo: 'Cuenta O' + CHR(9) + 'Tipo O' + CHR(9) + 'Monto' + CHR(9) + ...

            // Iterar sobre los datos y crear las líneas para el archivo
            for (let i = 0; i < info.length; i++) {
                let nuevoRegistro = '';
                let data = info[i]

                nuevoRegistro += data.Nit + ',' // Nit
                nuevoRegistro += data.NombreBeneficiario.substring(0, 60) + ',' // Nombre Beneficiario
                nuevoRegistro += data.CuentaBanco.substring(0, 10) + ',' // Cuenta Banco
                nuevoRegistro += data.Observaciones.substring(0, 35) + ',' // Observaciones
                nuevoRegistro += data.Documento.substring(0, 15) + ',' // Documento
                nuevoRegistro += parseFloat(data.Monto).toFixed(2) + ',' // Monto
                nuevoRegistro += data.Email ? data.Email : '' // Email

                // Agregar al contenido del archivo
                contenedorArchivo += nuevoRegistro + '\n';

                contador += 1;
                montoTotal = parseFloat(montoTotal) + parseFloat(data.Monto);
            }

            // Crear un Blob (objeto de archivo)
            const blob = new Blob([contenedorArchivo], {
                type: 'text/plain;charset=utf-8'
            });

            // Descargar el archivo
            saveAs(blob, nombreArchivo);

            // Mostrar mensaje con información
            this.$vs.dialog({
                type: 'alert',
                color: '#ed8c72',
                title: 'Archivo generado',
                acceptText: 'Aceptar',
                text: 'Proceso finalizado. Archivo generado: ' + nombreArchivo + '\n# de registros: ' + (contador - 1) + '\nMonto total: ' + parseFloat(montoTotal).toFixed(2),
                buttonCancel: 'border',
                accept: () => {},
            })
        },

        async ExportarAExcel(data) {
            const ws_data = []

            let encabezado2 = this.formulario.LoteSeleccionado.EsCuentaAjena == 1 ? 'ACREDITAMIENTO DE CUENTA AJENA - LOTE ' : 'ACREDITAMIENTO DE HONORARIOS - LOTE ',
                encabezado3 = 'Cantidades Expresadas en :   Quetzales (Q.)',
                nombreArchivo = 'Acreditamiento'

            encabezado2 = encabezado2 + this.formulario.LoteSeleccionado.Lote

            const encabezadoTabla = ['Nit', 'NombreBeneficiario', 'CuentaBanco', 'Observaciones', 'Documento', 'Monto', 'Email', 'NumNotaDebito', 'ChequeAcreditamiento'] // Una fila vacía para separar los encabezados de los datos

            ws_data.push([this.nombreEmpresa]);
            ws_data.push([encabezado2]);
            ws_data.push([encabezado3]);
            ws_data.push([]);
            ws_data.push(encabezadoTabla);

            // Variable para calcular el ancho máximo de cada columna
            const colWidths = new Array(encabezadoTabla.length).fill(0);

            // Agregar los registros de la consulta (similar a cómo se iteraría sobre QueryObj)
            for (let i = 0; i < data.length; i++) {
                const row = [
                    data[i].Nit,
                    data[i].NombreBeneficiario.substring(0, 60), // Simulación de cortar el nombre a 60 caracteres
                    data[i].CuentaBanco.substring(0, 10), // Limitar cuenta banco a 10 caracteres
                    data[i].Observaciones.substring(0, 35), // Limitar observaciones a 35 caracteres
                    data[i].Documento.substring(0, 15), // Limitar documento a 15 caracteres
                    'Q. ' + data[i].Monto.toFixed(2), // Monto formateado con 2 decimales
                    data[i].Email,
                    data[i].NumNotaDebito,
                    data[i].ChequeAcreditamiento,
                ];
                ws_data.push(row)

                row.forEach((cell, index) => {
                    colWidths[index] = Math.max(colWidths[index], cell ? cell.toString().length : 0);
                });
            }

            // Crear una hoja de trabajo a partir de los datos
            const ws = XLSX.utils.aoa_to_sheet(ws_data);

            // Asignar el ancho de las columnas (ajustar al contenido)
            ws['!cols'] = colWidths.map(width => ({
                wch: width + 2
            })); // Agregar un margen extra de 2

            // Crear un libro de trabajo con la hoja
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Reporte');

            // Definir el nombre del archivo
            let fileName = nombreArchivo + '.xlsx';

            // Guardar el archivo Excel en el navegador
            XLSX.writeFile(wb, fileName);
        },

        formatDate(date) {
            const day = String(date.getDate()).padStart(2, '0'); // Día con 2 dígitos
            const month = String(date.getMonth() + 1).padStart(2, '0'); // Mes con 2 dígitos (recuerda que los meses comienzan en 0)
            const year = date.getFullYear(); // Año
            const hours = String(date.getHours()).padStart(2, '0'); // Hora con 2 dígitos
            const minutes = String(date.getMinutes()).padStart(2, '0'); // Minutos con 2 dígitos

            return `${day}-${month}-${year}-${hours}-${minutes}`;
        }

    },
    created() {},
    mounted() {},
    beforeMount() {
        this.CargarCuentas()
        this.CargarOpciones()
        this.CargarAcreditamientos()
    },
    watch: {
        'formulario.ChequeInicial'(newval) {
            if (this.formulario.ChequeFinal == 0 || this.formulario.ChequeFinal == null || parseInt(this.formulario.ChequeFinal) < parseInt(this.formulario.ChequeInicial)) {
                this.formulario.ChequeFinal = newval
            }
        }
    },
    computed: {
        formConsultaInstance: function () {
            return this.$refs[formConsulta].instance;
        },

        formChequeInstance: function () {
            return this.$refs[formCheque].instance;
        },
    }
}
</script>

<style>
.buttonGenerar .dx-item-content .dx-box-item-content {
    display: grid !important;
    place-items: center !important;
}

.centered-item .dx-item-content .dx-box-item-content {
    display: grid !important;
    place-content: center !important;
}
</style>
