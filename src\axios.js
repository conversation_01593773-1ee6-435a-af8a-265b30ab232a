// let socket = null
// import Vue from 'vue'

// import 'sweetalert2/dist/sweetalert2.css';
let conteo = 0
const AxiosInit = (axios, store) => {
    axios.interceptors.request.use(config => {
        var number = Math.random()
        var id = number.toString(36).substr(2, 15);

        const url = store.state.global.url
        const token = store.state.sesion.token
        if (store.state.id_funcionalidad != null) config.headers["Funcionalidad"] = store.state.id_funcionalidad

        /**
         * Agregando configuraciones del axios
         */
        config.metadata = { id: id, startTime: new Date() }

        /**
         * Agregando autorización bearer
         */
        config.headers["Authorization"] = (config.headers["Authorization"]) ? config.headers["Authorization"] : 'Bearer ' + token

        /**
         * Agregando completación de la url
         * Si se llama la instancia deja la ruta actual
         */
        config.url = (config.url.substring(0, 1) == '/' && config.url != '/Instancia') ? url.substring(0, url.length - 1) + config.url : config.url

        conteo++
        // console.log(conteo, config.url)
        if (conteo === 1 && !config.skipLoading) {
            store.dispatch('loading', true);
        }

        return config
    })
}

const AxiosResponse = (axios, store) => {
    /**
     * AXIOS
     * CONFIGURACIONES
     * -------------------------------------------------------------------------------
     * _NOTIFICACIONES: Muestra el mensaje de la solicitud realizada (Default:True)
     * 
     * -------------------------------------------------------------------------------
     */
    // console.warn('intercept')

    axios.interceptors.response.use((response) => {
        conteo--
        // console.log('👌', conteo, response.config.url)
        if (conteo === 0) store.dispatch('loading', false)

        let config = null
        if (response.config.data && response.config.data) config = JSON.parse(response.config.data)
        let configuraciones = {
            _NOTIFICACIONES: true  // Muestra el estado de una respuesta proveniente del middleware
        }

        // Remplazando configuraciones
        if (config != null)
            Object.entries(config).forEach(([key]) => {
                if (configuraciones[key.toUpperCase()]) configuraciones[key.toUpperCase()] = config[key]
            });

        if (response.data && response.data.codigo && response.data.codigo < 0 && configuraciones._NOTIFICACIONES) {
            store.dispatch('notify', {
                time: 8000,
                title: 'Servidor',
                text: response.data.descripcion,
                iconPack: 'feather',
                icon: 'icon-alert-circle',
                color: 'danger',
                position: 'bottom-center'
            })

            return Promise.reject(response.data);
        } else {
            // console.log(typeof response.data.codigo ,response.data)
            if (response.data && typeof response.data.codigo == "number" && response.data.codigo >= 0 && response.data.descripcion && configuraciones._NOTIFICACIONES) {
                let nombre_funcionalidad = store.state.funcionalidades.filter(data => data.id_funcionalidad == store.state.id_funcionalidad)
                nombre_funcionalidad = nombre_funcionalidad.lenght > 0 ? nombre_funcionalidad[0].name : "Notificación"

                store.dispatch('notify', {
                    time: 4000,
                    title: nombre_funcionalidad,
                    text: response.data.descripcion,
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'success',
                    position: 'top-center'
                })
            }
            return response;
        }
    }, (error) => {
        conteo--
        // console.log('🐛', conteo)
        if (conteo === 0) store.dispatch('loading', false)
        if (error.response) {
            if (error.response.status === 400) {
                let texto = ((error.response.data.Message)) ? error.response.data.Message : error.response.data.descripcion
                if (texto != '' && isJson(texto) && JSON.parse(texto).descripcion) texto = JSON.parse(texto).descripcion
                if (texto && texto.search('Violation of UNIQUE KEY constraint') >= 0) texto = 'Los valores han sido ingresados previamente. (UNIQUE KEY)'
                if (!texto) texto = "Error al obtener información"
                store.dispatch('notify', {
                    time: 8000,
                    title: 'Servidor',
                    text: texto,
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'danger',
                    position: 'bottom-center'
                })
            }

            if (error.response.status === 401) {
                store.dispatch('notify', {
                    time: 4000,
                    title: 'Sesión',
                    text: "Sesión Finalizada",
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'danger',
                    position: 'bottom-right'
                })
            }
        } else {
            store.dispatch('notify', {
                color: 'danger',
                title: 'Sighos',
                text: 'Sin conexión al servidor',
            })
        }
        return Promise.reject(error);
    });
}

const isJson = (str) => {
    try {
        JSON.parse(str);
    } catch (e) {
        return false;
    }
    return true;
}

export { AxiosInit, AxiosResponse } 