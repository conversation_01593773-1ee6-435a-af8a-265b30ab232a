<template>
    <div>
        <vx-card title="Reporte de Cargos Productos en Consignación">
            <buscador ref="BuscarProveedorConsignacion" buscador_titulo="Buscador / Proveedores" :api="'app/v1_OrdenCompra/ConsultaProveedorConsignacion'" 
            :campos="['Proveedor', 'Nombre', 'Nit', 'Direccion', 'Telefonos']"
            :titulos="['Proveedor', '#Nombre', '#Nit', '#Direccion', '#Telefonos']"
            :api_filtro="{'Proveedor':Proveedor.Proveedor}"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" :api_preload="true" />

            <buscador ref="BuscarBodegaConsignacion" buscador_titulo="Buscador / Bodegas" :api="'app/v1_OrdenCompra/ConsultaBodegaConsignacion'" 
            :campos="['CodigoBodega', 'NombreBodega']"
            :titulos="['CodigoBodega', '#NombreBodega']"
            :api_filtro="{CodigoAgrupacion:'8',CodigoBodega:CodigoBodega}"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" :api_preload="true" />

            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="12">
                    <div class="almacenador w-full">
                        <div class="div-container w-full">
                            <vs-row vs-align="center">
                                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                    <vs-row vs-align="center">
                                        <vs-col vs-type="flex" vs-align="center" vs-w="6">
                                            <label class="typo__label">Del:&nbsp;</label>
                                            <vs-input type="date" v-model="FechaDel"></vs-input>
                                        </vs-col>
                                        <vs-col vs-type="flex" vs-align="center" vs-w="6">
                                            <label class="typo__label">Al:&nbsp;</label>
                                            <vs-input type="date" v-model="FechaAl"></vs-input>
                                        </vs-col>
                                    </vs-row>
                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                                    <div class="almacenador">
                                        <label class="typo__label">&nbsp;Tipo</label>
                                        <vs-divider></vs-divider>
                                        <div class="div-container">
                                            <vs-row vs-align="center">
                                                <vs-radio v-model="Tipo" vs-name="radios1" vs-value="0">Medicamentos</vs-radio>
                                            </vs-row>
                                            <br>
                                            <vs-row vs-align="center">
                                                <vs-radio v-model="Tipo" vs-name="radios1" vs-value="1">Suministros</vs-radio>
                                            </vs-row>
                                            <br>
                                            <vs-row vs-align="center">
                                                <vs-radio v-model="Tipo" vs-name="radios1" vs-value="2">Ambos</vs-radio>
                                            </vs-row>
                                        </div>
                                    </div>
                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="5">
                                    <div class="almacenador w-full">
                                        <label class="typo__label">&nbsp;Opciones de Búsqueda</label>
                                        <vs-divider></vs-divider>
                                        <div class="div-container">
                                            <vs-row vs-align="center">
                                                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                                <vs-radio v-model="Liquidacion" vs-name="radios2" vs-value="0">Sin Liquidación</vs-radio>
                                            </vs-col>
                                            <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                                <vs-radio v-model="Liquidacion" vs-name="radios2" vs-value="1">Con Liquidación</vs-radio>
                                            </vs-col>
                                            <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                                <vs-radio v-model="Liquidacion" vs-name="radios2" vs-value="2">Todos</vs-radio>
                                            </vs-col>
                                            </vs-row>
                                        </div>
                                    </div>
                                </vs-col>
                            </vs-row>
                            <br>
                            <vs-row vs-align="center">
                                <vs-col vs-type="flex" vs-align="center" vs-w="5">
                                    <vs-row vs-align="center">
                                        <vs-col vs-type="flex" vs-align="center" vs-w="6">
                                            <label class="typo__label">Producto:&nbsp;</label>
                                                <vs-input v-model="Producto"></vs-input>
                                        </vs-col>
                                        <vs-col vs-type="flex" vs-align="center" vs-w="6">
                                            <label class="typo__label">&nbsp;Proveedor:&nbsp;</label>
                                            <vs-input ref="ProveedorConsignacion" v-model="Proveedor.Proveedor" @keyup.enter="Proveedores()" @keydown.tab="Proveedores()" ></vs-input>
                                            <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="Proveedores()" icon="fa-search"></vs-button>
                                        </vs-col>
                                    </vs-row>
                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="7">
                                    <vs-row vs-align="center">
                                        <vs-col vs-type="flex" vs-align="center" vs-justify="flex-end" vs-w="5">
                                            <label class="typo__label">Bodega:&nbsp;</label>
                                            <vs-input ref="BodegaDestino" v-model="CodigoBodega" @keyup.enter="Bodega()" @keydown.tab="Bodega()" ></vs-input>
                                            <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="Bodega()" icon="fa-search"></vs-button>
                                        </vs-col>
                                        <vs-col vs-type="flex" vs-align="center" vs-justify="flex-end" vs-w="5">
                                            {{ this.NombreBodega }}
                                        </vs-col>
                                    </vs-row>
                                </vs-col>
                            </vs-row>
                            <br>
                            <vs-row vs-align="center">
                                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                    <label class="typo__label">&nbsp;Nit:&nbsp;</label>
                                    <h5>{{ Proveedor.Nit }}</h5>
                                </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                    <label class="typo__label">Telefono:&nbsp;</label>
                                    <h5>{{ Proveedor.Telefonos }}</h5>
                                </vs-col>
                            </vs-row>
                            <br>
                            <vs-row vs-align="center">
                                <vs-col vs-type="flex" vs-align="center" vs-w="12">
                                    <label class="typo__label">Nombre:&nbsp;</label>
                                    <h5>{{ Proveedor.Nombre }}</h5>
                                </vs-col>
                            </vs-row>
                            <br>
                            <vs-row vs-align="center">
                                <vs-col vs-type="flex" vs-align="center" vs-w="12">
                                    <label class="typo__label">Dirección:&nbsp;</label>
                                    <h5>{{ Proveedor.Direccion }}</h5>
                                </vs-col>
                            </vs-row>
                        </div>
                    </div>
                </vs-col>
            </vs-row>
            <br>
            <vs-divider></vs-divider>
            <br>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="12">
                    <div ref="tablaProductos" class="w-full div-table">
                        <vs-table multiple max-items="10" pagination search :data="DetalleProductos" v-model="selected">
                            <template slot="thead">
                                <!-- <th>Selec</th> -->
                                <vs-th>Código</vs-th>
                                <vs-th>Producto</vs-th>
                                <vs-th>Hopital</vs-th>
                                <vs-th>Serie</vs-th>
                                <vs-th>Admisión</vs-th>
                                <vs-th>Cantidad</vs-th>
                                <vs-th>Tipo</vs-th>
                                <vs-th>Cargo</vs-th>
                                <vs-th>Monto</vs-th>
                                <vs-th>Fecha</vs-th>
                                <vs-th>Correlativo</vs-th>
                                <vs-th>Bodega</vs-th>
                                <vs-th>Id Envio</vs-th>
                                <vs-th>Costo Convenio</vs-th>
                            </template>
                            <template slot-scope="{data}">
                                <vs-tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">
                                    <!-- <vs-td2 noTooltip>
                                        <vs-checkbox v-model="checkLinea"></vs-checkbox>
                                    </vs-td2> -->
                                    <vs-td :data="data[indextr].Codigo">
                                        {{ data[indextr].Codigo }}
                                    </vs-td>
                                    <vs-td :data="data[indextr].Producto">
                                        {{ data[indextr].Producto }}
                                    </vs-td>
                                    <vs-td :data="data[indextr].Hospital">
                                        {{ data[indextr].Hospital }}
                                    </vs-td>
                                    <vs-td :data="data[indextr].SerieAdmision">
                                        {{ data[indextr].SerieAdmision }}
                                    </vs-td>
                                    <vs-td :data="data[indextr].Admision">
                                        {{ data[indextr].Admision }}
                                    </vs-td>
                                    <vs-td :data="data[indextr].Cantidad">
                                        {{ parseInt(data[indextr].Cantidad) }}
                                    </vs-td>
                                    <vs-td :data="data[indextr].Tipo_Cargo">
                                        {{ data[indextr].Tipo_Cargo }}
                                    </vs-td>
                                    <vs-td :data="data[indextr].Cargo">
                                        {{ data[indextr].Cargo }}
                                    </vs-td>
                                    <vs-td :data="data[indextr].Monto">
                                        {{ $formato_moneda(data[indextr].Monto) }}
                                    </vs-td>
                                    <vs-td :data="data[indextr].Fecha">
                                        {{ data[indextr].Fecha }}
                                    </vs-td>
                                    <vs-td :data="data[indextr].Liquidacion">
                                        {{ data[indextr].Liquidacion }}
                                    </vs-td>
                                    <vs-td :data="data[indextr].Bodega">
                                        {{ data[indextr].Bodega }}
                                    </vs-td>
                                    <vs-td :data="data[indextr].IdEnvio">
                                        {{ data[indextr].IdEnvio }}
                                    </vs-td>
                                    <vs-td :data="data[indextr].CostoConvenio">
                                        {{ $formato_moneda(data[indextr].CostoConvenio) }}
                                    </vs-td>
                                </vs-tr>
                            </template>
                        </vs-table>
                    </div>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-type="flex" vs-align="center" vs-w="12" vs-justify="flex-end">
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-button color="success" icon-pack="fa" icon="fa-save" @click="CongelarCargos()" :style="styleHidden">Congelar Cargos</vs-button>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-button color="warning" icon-pack="fa" icon="fa-file-excel" @click="GenerarPDF()">Ver PDF</vs-button>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-button color="warning" icon-pack="fa" icon="fa-file-excel" @click="GenerarExcel()">Ver Excel</vs-button>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                    <vs-button color="success" icon-pack="fa" icon="fa-check-circle" @click="ConsultaCargosProductosConsignacion()">Generar Reporte</vs-button>
                </vs-col>
            </vs-row>
        </vx-card>
    </div>
</template>
<script>
    import moment from 'moment'
    export default {
        data(){
            return{
                FechaDel: '',
                FechaAl: '',
                Tipo: 0,
                Liquidacion: 0,
                Producto: '',
                Proveedor: {
                    Proveedor: '',
                    Nombre: '',
                    Nit: '',
                    Telefonos: '',
                    Direccion: ''
                },
                CodigoBodega: '',
                NombreBodega: '',
                DetalleProductos: [{
                    Codigo: '',
                    Producto: '',
                    Hopital: '',
                    Serie: '',
                    Admision: '',
                    Cantidad: '',
                    Tipo: '',
                    Cargo: '',
                    Monto: '',
                    Fecha: '',
                    Correlativo: '',
                    Bodega: '',
                    IdEnvio: '',
                    CostoConvenio: '',
                    Linea: ''
                }],
                checkLinea: true,
                selected: [],
                styleHidden: 'display: none'
            }
        },
        methods: {
            Proveedores(){
                if(this.Proveedor.Proveedor != ''){
                    this.axios.post('/app/v1_OrdenCompra/ConsultaProveedorConsignacion', {
                        Proveedor: this.Proveedor.Proveedor
                    })
                    .then(resp => {
                        if(resp.data.codigo == 0){
                            if(resp.data.json.length == 1){
                                this.Proveedor = resp.data.json[0]
                            }else{
                                this.$refs.BuscarProveedorConsignacion.iniciar((data) =>{
                                    if(data != null){
                                        this.Proveedor = data
                                    }
                                })
                            }
                        }
                    })
                }else{
                    this.$refs.BuscarProveedorConsignacion.iniciar((data) =>{
                        if(data != null){
                            this.Proveedor = data
                        }
                    })
                }
            },
            Bodega(){
                if(this.CodigoBodega != ''){
                    this.axios.post('/app/v1_OrdenCompra/ConsultaBodegaConsignacion', {
                        CodigoBodega: this.CodigoBodega,
                        CodigoAgrupacion: '8'
                    })
                    .then(resp => {
                        if(resp.data.codigo == 0){
                            if(resp.data.json.length == 1){
                                this.NombreBodega = resp.data.json[0].NombreBodega
                            }else{
                                this.$refs.BuscarBodegaConsignacion.iniciar((data) => {
                                    if(data != null){
                                        this.CodigoBodega = data.CodigoBodega
                                        this.NombreBodega = data.NombreBodega
                                    }
                                })
                            }
                        }
                    })
                }else{
                    this.$refs.BuscarBodegaConsignacion.iniciar((data) => {
                        if(data != null){
                            this.CodigoBodega = data.CodigoBodega
                            this.NombreBodega = data.NombreBodega
                        }
                    })
                }
            },
            ConsultaCargosProductosConsignacion(){
                if(this.Proveedor.Proveedor != ''){
                    this.axios.post('/app/v1_OrdenCompra/ConsultaCargosProductosConsignacion', {
                        FechaInicio: this.FechaDel,
                        FechaFin: this.stringToDate(this.FechaAl),
                        Proveedor: this.Proveedor.Proveedor,
                        Producto: this.Producto,
                        CodigoBodega: this.CodigoBodega,
                        Liquidacion: this.Liquidacion,
                        Tipo: this.Tipo
                    })
                    .then(resp => {
                        if(resp.data.codigo == '0'){
                            this.DetalleProductos = resp.data.json
                            this.selected = resp.data.json
                            if(this.Liquidacion == 0){
                                this.styleHidden = ''
                            }else{
                                this.styleHidden = 'display: none'
                            }
                        }else{
                            this.DetalleProductos = []
                            this.selected = []
                        }
                    })
                }else{
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Pedido Consignación',
                        text: 'Debe de Seleccionar un Proveddor',
                    })
                }
                
            },
            // exportTableToExcel(filename = '') {
            //     let downloadLink;
            //     let dataType = 'application/vnd.ms-excel'
            //     let tableSelect = this.$refs.tablaProductos
            //     let tableClone = tableSelect.cloneNode(true)
            //     eliminar = tableClone.querySelectorAll('.header-table')
            //     eliminar.parentElement.innerHTML = 'SERVICIOS MEDICOS Y HOSPITALARIOS CENTROAMERICANOS, S.A.'

            //     let eliminar = tableClone.querySelectorAll('vs-table')
            //     eliminar.forEach(function(anchors) {
            //         const el = anchors.querySelectorAll('vs-td')
            //         const texto = (el.length > 1) ? el[1].innerHTML : el[0].innerHTML
            //         anchors.parentElement.innerHTML = texto
            //     })
                
            //     eliminar = tableClone.querySelectorAll('.td-check')
            //     eliminar.forEach(function(anchors) {
            //         anchors.remove()
            //     })

            //     eliminar = tableClone.querySelectorAll('.con-input-search')
            //     eliminar.forEach(function(anchors) {
            //         anchors.remove()
            //     })

            //     eliminar = tableClone.querySelectorAll('.con-pagination-table')
            //     eliminar.forEach(function(anchors) {
            //         anchors.remove()
            //     })

            //     eliminar = tableClone.querySelectorAll('.vs-button-success')
            //     eliminar.forEach(function(anchors) {
            //         anchors.remove()
            //     })

            //     eliminar = tableClone.querySelectorAll('.tooltip')
            //     eliminar.forEach(function(anchors) {
            //         anchors.remove()
            //     })

            //     eliminar = tableClone.querySelectorAll('.noExcel')
            //     eliminar.forEach(function(anchors) {
            //         anchors.remove()
            //     })

            //     let tableHTML = tableClone.outerHTML.replace(/ /g, '%20');
            //     tableHTML = tableHTML.replace(/á/g, '&aacute;')
            //     tableHTML = tableHTML.replace(/é/g, '&eacute;')
            //     tableHTML = tableHTML.replace(/í/g, '&iacute;')
            //     tableHTML = tableHTML.replace(/ó/g, '&oacute;')
            //     tableHTML = tableHTML.replace(/ú/g, '&uacute;')

            //     tableHTML = tableHTML.replace(/Á/g, '&Aacute;')
            //     tableHTML = tableHTML.replace(/É/g, '&Eacute;')
            //     tableHTML = tableHTML.replace(/Í/g, '&Iacute;')
            //     tableHTML = tableHTML.replace(/Ó/g, '&Oacute;')
            //     tableHTML = tableHTML.replace(/Ú/g, '&Uacute;')

            //     tableHTML = tableHTML.replace(/ñ/g, '&ntilde;')
            //     tableHTML = tableHTML.replace(/Ñ/g, '&Ntilde;')

            //     // Specify file name
            //     filename = filename ? filename + '.xls' : 'excel_data.xls';

            //     // Create download link element
            //     downloadLink = document.createElement("a");

            //     document.body.appendChild(downloadLink);

            //     if (navigator.msSaveOrOpenBlob) {
            //         var blob = new Blob(['\ufeff', tableHTML], {
            //             type: dataType
            //         });
            //         navigator.msSaveOrOpenBlob(blob, filename);
            //     } else {
            //         // Create a link to the file
            //         downloadLink.href = 'data:' + dataType + ', ' + tableHTML;

            //         // Setting the file name
            //         downloadLink.download = filename;

            //         //triggering the function
            //         downloadLink.click();
            //     }
            // },
            GenerarExcel(){
                this.$reporte_modal({
                    Nombre: "Cargos Productos Consignacion Excel",
                    Opciones: {
                        FechaInicial: this.FechaDel,
                        FechaFinal: this.stringToDate(this.FechaAl),
                        Proveedor: this.Proveedor.Proveedor,
                        NombreProveedor: this.Proveedor.Nombre,
                        Producto: this.Producto,
                        CodigoBodega: this.CodigoBodega,
                        NombreBodega: this.NombreBodega,
                        Liquidacion: this.Liquidacion,
                        Estado: this.Tipo,

                    },
                    Formato: "EXCEL"
                })
            },
            GenerarPDF(){
                this.$reporte_modal({
                    Nombre: "Cargos Productos Consignacion PDF",
                    Opciones: {
                        FechaInicial: this.FechaDel,
                        FechaFinal: this.stringToDate(this.FechaAl),
                        Proveedor: this.Proveedor.Proveedor,
                        NombreProveedor: this.Proveedor.Nombre,
                        Producto: this.Producto,
                        CodigoBodega: this.CodigoBodega,
                        NombreBodega: this.NombreBodega,
                        Liquidacion: this.Liquidacion,
                        Estado: this.Tipo,

                    },
                    Formato: "PDF"
                })
            },
            CongelarCargos(){
                this.axios.post('/app/v1_OrdenCompra/CongelarCargos', {
                    Proveedor: this.Proveedor.Proveedor,
                    FechaInicio: this.FechaDel,
                    FechaFin: this.stringToDate(this.FechaAl),
                    CodigoBodega: this.CodigoBodega,
                    Tipo: this.Tipo,
                    Datos: JSON.stringify(this.selected)
                })
                .then(resp => {
                    if(resp.data.codigo == 0){
                        this.ConsultaCargosProductosConsignacion()
                    }
                })
            },
            stringToDate(strDate){
            // const [datePart, timePart] = strDate.split(' ');

            // Dividir la parte de la fecha y la hora en sus componentes
            const [year, month, day] = strDate.split('-');
            // const [hours, minutes, seconds] = timePart.split(':');

            // Crear el objeto Date con los componentes individuales (nota: los meses son 0-indexados en JavaScript)
            const fechaConHora = new Date(year, month - 1, day - 3, '00', '00', '00');
            return fechaConHora.toISOString().slice(0, 10);
        }
        },
        mounted() {
            this.DetalleProductos = []
            this.selected = []
        },
        watch: {
            Liquidacion(valor){
                if(valor == 0){
                    this.styleHidden = ''
                }else{
                    this.styleHidden = 'display: none'
                }
            }
        }
    }
</script>
<style lang="scss" scooped>
    .almacenador{
        border: 1px solid #888;

    }
    .div-container{
        padding: 5px;
        margin: 10px;
    }
    .div-periodo{
        padding-bottom: 48px;
        padding-top: 48px;
    }
    .div-estado{
        padding-bottom: 57px;
        padding-top: 57px;
    }
    .div-table{
        border: 1px solid #888;
    }
</style>