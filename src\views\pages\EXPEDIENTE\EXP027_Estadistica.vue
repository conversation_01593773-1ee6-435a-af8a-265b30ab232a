<template>
<div>
    <div v-if="(consulta && hojaAnestesia !== null && hojaAnestesia !== '') || (!consulta)">
        <div class="p-2">
            <form @submit="handleSubmit">
                <DxForm :form-data.sync="encabezado" labelMode="floating" :ref="dataEncabezadoRefKey" v-if="!consulta" :read-only="((codigoEstadistica !== null && codigoEstadistica !== '') || idRegistroAnestesico === null) ? true : false">
                    <DxGroupItem item-type="group">

                        <DxGroupItem item-type="group">
                            <DxItem data-field="Operacion" editor-type="dxTextArea" :editor-options="{ maxLength: 1000, height: 100, readOnly: true, value: encabezado.Operacion }">
                                <DxLabel :text="'Operación efectuada'" />
                            </DxItem>
                            <DxItem data-field="Observaciones" editor-type="dxTextArea" :editor-options="{ maxLength: 1000, height: 100, }" />
                        </DxGroupItem>

                        <DxGroupItem item-type="group" :col-count="2">
                            <DxItem data-field="FechaIngreso" editor-type="dxDateBox" :editor-options="dateIngreso">
                                <DxLabel :text="'Fecha de ingreso'" />
                            </DxItem>
                            <DxItem data-field="FechaNota" editor-type="dxDateBox" :is-required="!consulta" :editor-options="dateNota">
                                <DxLabel :text="'Fecha nota preoperatoria'" />
                            </DxItem>
                        </DxGroupItem>

                        <DxGroupItem item-type="group" caption="Cirugía">
                            <DxItem data-field="FechaCirugia" editor-type="dxDateBox" :is-required="!consulta" :editor-options="dateCirugia">
                                <DxLabel :text="'Fecha cirugía'" />
                            </DxItem>
                        </DxGroupItem>

                        <DxGroupItem item-type="group" :col-count="2" caption="Validacion requisitos">
                            <DxItem data-field="FechaValidacion" editor-type="dxDateBox" :is-required="!consulta" :editor-options="dateValidacion">
                                <DxLabel :text="'Fecha validación requisitos'" />
                            </DxItem>
                            <DxItem data-field="DifValidacion" editor-type="dxTextBox" :editor-options="{ readOnly: true, value: encabezado.DifValidacion }">
                                <DxLabel :text="'Diferencia horas validación requisitos'" />
                            </DxItem>
                        </DxGroupItem>

                        <DxGroupItem item-type="group" :col-count="2" caption="Visita preanestésica">
                            <DxItem data-field="FechaVisita" editor-type="dxDateBox" :is-required="!consulta" :editor-options="dateVisita">
                                <DxLabel :text="'Fecha visita preanestésica'" />
                            </DxItem>
                            <DxItem data-field="DifVisita" editor-type="dxTextBox" :editor-options="{ readOnly: true, value: encabezado.DifVisita } ">
                                <DxLabel :text="'Diferencia horas visita preanestésica'" />
                            </DxItem>
                        </DxGroupItem>

                        <DxGroupItem item-type="group" :col-count="2" caption="Solicitud ingreso SOP">
                            <DxItem data-field="FechaSolicitud" editor-type="dxDateBox" :is-required="!consulta" :editor-options="dateSolicitud">
                                <DxLabel :text="'Fecha solicitud de ingreso'" />
                            </DxItem>
                            <DxItem data-field="DifSolicitud" editor-type="dxTextBox" :editor-options="{ readOnly: true, value: encabezado.DifSolicitud }">
                                <DxLabel :text="'Diferencia horas solicitud de ingreso'" />
                            </DxItem>
                        </DxGroupItem>

                        <DxGroupItem item-type="group" :col-count="2" caption="Puntualidad">
                            <DxItem data-field="FechaInicioCirugia" editor-type="dxDateBox" :is-required="!consulta" :editor-options="datePuntualidad">
                                <DxLabel :text="'Fecha inicio de cirugía'" />
                            </DxItem>
                            <DxItem data-field="DifPuntualidad" editor-type="dxTextBox" :editor-options="{ readOnly: true, value: encabezado.DifPuntualidad }">
                                <DxLabel :text="'Diferencia horas puntualidad'" />
                            </DxItem>
                        </DxGroupItem>

                    </DxGroupItem>

                    <DxGroupItem item-type="group" :col-count="2" v-if="(codigoEstadistica === null || codigoEstadistica === '') && idRegistroAnestesico !== null ">
                        <DxButtonItem :button-options="submitButtonOptions" horizontal-alignment="center" verical-alignment="center" />
                        <DxButtonItem :button-options="clearButtonOptions" horizontal-alignment="center" verical-alignment="center" />
                    </DxGroupItem>
                </DxForm>
            </form>
        </div>
    </div>
    <vs-row id="texto" class="p-2" v-if="(hojaAnestesia === '' || hojaAnestesia === null) && consulta">
        <p>Seleccione una fecha para mostrar información</p>
    </vs-row>
    <vs-row id="texto" class="p-2" v-if="consulta && hojaAnestesia !== '' && hojaAnestesia !== null">
        <p>La hoja seleccionada no cuenta con información para mostrar</p>
    </vs-row>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'
import {
    DxForm,
    DxItem,
    DxLabel,
    DxGroupItem,
    DxButtonItem
} from 'devextreme-vue/form'
import 'devextreme-vue/text-area'

const dataEncabezadoRefKey = 'encabezado-form'

export default {
    name: 'Estadistica',
    components: {
        DxForm,
        DxItem,
        DxLabel,
        DxGroupItem,
        DxButtonItem,
    },
    data() {
        return {
            DefaultDxGridConfiguration,
            dataEncabezadoRefKey,
            estadistica: [],
            encabezado: {
                Operacion: '',
                Observaciones: '',
                DifValidacion: '',
                DifVisita: '',
                DifSolicitud: '',
                DifPuntualidad: '',
            },
            submitButtonOptions: {
                text: 'Guardar datos',
                type: 'success',
                icon: 'save',
                width: 200,
                useSubmitBehavior: true,
                disabled: false
            },
            clearButtonOptions: {
                text: 'Limpiar registro',
                type: 'danger',
                icon: 'trash',
                width: 200,
                useSubmitBehavior: false,
                disabled: false,
                onClick: this.limpiarTiempo
            },
            dateIngreso: {
                value: new Date(),
                type: "datetime"
            },
            dateNota: {
                value: new Date(),
                type: "datetime"
            },
            dateCirugia: {
                value: new Date(),
                type: "datetime",
                onValueChanged: this.valueChangedCirugia
            },
            dateValidacion: {
                type: "datetime",
                onValueChanged: this.valueChangedValidacion
            },
            dateVisita: {
                type: "datetime",
                onValueChanged: this.valueChangedVisita
            },
            dateSolicitud: {
                type: "datetime",
                onValueChanged: this.valueChangedSolicitud
            },
            datePuntualidad: {
                type: "datetime",
                onValueChanged: this.valueChangedPuntualidad
            },
            codigoEstadistica: '',
            idRegistroAnestesico: '', //Variable que indica que tiene registro anestésico
        }
    },
    props: {
        SerieAdmision: null,
        CodigoAdmision: null,
        NumeroExpediente: null,

        StatusAdmision: null,
        consulta: null, //Variable para indicar si el componente se usará para consulta -> 'true' o para ingreso -> 'false'
        hojaAnestesia: null, //Variable para obtener la hoja cuando se utilice para consulta
    },
    methods: {
        handleSubmit(e) {
            e.preventDefault()
            if (this.SerieAdmision && this.CodigoAdmision) {
                let postData = {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    FechaIngreso: this.encabezado.FechaIngreso,
                    FechaCirugia: this.encabezado.FechaCirugia,
                    FechaNotaPreoperatoria: this.encabezado.FechaNota,
                    FechaRequisitos: this.encabezado.FechaValidacion,
                    FechaVisitaPreAnestesica: this.encabezado.FechaVisita,
                    FechaSolicitudIngresoSop: this.encabezado.FechaSolicitud,
                    FechaInicioCirugia: this.encabezado.FechaInicioCirugia,
                    Observaciones: this.$reemplazar_tabulares(this.encabezado.Observaciones)
                }
                this.axios.post("/app/v1_ExpedienteEvolucion/InsertarEstadistica", {
                    ...postData
                }).then(() => {
                    this.getEstadistica()
                })
            }
        },
        valueChangedCirugia(e) {
            //Validación
            if (this.encabezado.FechaValidacion !== null && this.encabezado.FechaValidacion !== undefined) {
                this.encabezado.DifValidacion = this.calcularTiempo(Math.abs(new Date(this.encabezado.FechaValidacion) - new Date(e.value)))
            }

            //Visita preanestésica
            if (this.encabezado.FechaVisita !== null && this.encabezado.FechaVisita !== undefined) {
                this.encabezado.DifVisita = this.calcularTiempo(Math.abs(new Date(this.encabezado.FechaVisita) - new Date(e.value)))
            }

            //Solicitud ingreso SOP
            if (this.encabezado.FechaSolicitud !== null && this.encabezado.FechaSolicitud !== undefined) {
                this.encabezado.DifSolicitud = this.calcularTiempo(Math.abs(new Date(this.encabezado.FechaSolicitud) - new Date(e.value)))
            }

            //Puntualidad
            if (this.encabezado.FechaInicioCirugia !== null && this.encabezado.FechaInicioCirugia !== undefined) {
                this.encabezado.DifPuntualidad = this.calcularTiempo(Math.abs(new Date(this.encabezado.FechaInicioCirugia) - new Date(e.value)))
            }
        },
        valueChangedValidacion(e) {
            if (this.encabezado.FechaCirugia !== null && this.encabezado.FechaCirugia !== undefined && e.value !== null && e.value !== undefined) {
                this.encabezado.DifValidacion = this.calcularTiempo(Math.abs(new Date(this.encabezado.FechaCirugia) - new Date(e.value)))
            }

        },
        valueChangedVisita(e) {
            if (this.encabezado.FechaCirugia !== null && this.encabezado.FechaCirugia !== undefined && e.value !== null && e.value !== undefined) {
                this.encabezado.DifVisita = this.calcularTiempo(Math.abs(new Date(this.encabezado.FechaCirugia) - new Date(e.value)))
            }
        },
        valueChangedSolicitud(e) {
            if (this.encabezado.FechaCirugia !== null && this.encabezado.FechaCirugia !== undefined && e.value !== null && e.value !== undefined) {
                this.encabezado.DifSolicitud = this.calcularTiempo(Math.abs(new Date(this.encabezado.FechaCirugia) - new Date(e.value)))
            }
        },
        valueChangedPuntualidad(e) {
            if (this.encabezado.FechaCirugia !== null && this.encabezado.FechaCirugia !== undefined && e.value !== null && e.value !== undefined) {
                this.encabezado.DifPuntualidad = this.calcularTiempo(Math.abs(new Date(this.encabezado.FechaCirugia) - new Date(e.value)))
            }
        },
        calcularTiempo(milisegundos) {
            var hours = ((milisegundos / 1000 / 60) / 60);
            var rhours = Math.floor(hours);
            var minutes = (hours - rhours) * 60;
            var rminutes = Math.round(minutes);
            if (rminutes === 60) {
                rhours++
                rminutes = 0
            }
            return rhours + ":" + (rminutes < 10 ? "0" + rminutes : rminutes)
        },
        limpiarTiempo() {
            this.encabezado = {
                Operacion: '',
                DifValidacion: '',
                DifVisita: '',
                DifSolicitud: '',
                DifPuntualidad: '',
                Observaciones: ''
            }
            this.getEstadistica()
            this.estadisticaDataForm.repaint()
        },
        getEstadistica() {
            this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaEstadistica', {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision
                })
                .then(resp => {
                    this.dateIngreso = {
                        value: new Date(resp.data[0].FechaIngreso),
                        type: "datetime",
                    }

                    this.codigoEstadistica = resp.data[0].CodigoEstadistica
                    this.encabezado.Operacion = resp.data[0].OperacionEfectuada

                    this.idRegistroAnestesico = resp.data[0].IdAnestesicoE

                    if (this.codigoEstadistica !== null) {
                        this.codigoEstadistica = resp.data[0].CodigoEstadistica

                        this.encabezado.Operacion = resp.data[0].OperacionEfectuada
                        this.encabezado.Observaciones = resp.data[0].Observaciones

                        this.dateNota = {
                            value: new Date(resp.data[0].FechaNotaPreoperatoria),
                            type: "datetime"
                        }
                        this.dateCirugia = {
                            value: new Date(resp.data[0].FechaCirugia),
                            type: "datetime",
                        }
                        this.dateValidacion = {
                            value: new Date(resp.data[0].FechaRequisitos),
                            type: "datetime",
                        }
                        this.dateVisita = {
                            value: new Date(resp.data[0].FechaVisitaPreAnestesica),
                            type: "datetime",
                        }
                        this.dateSolicitud = {
                            value: new Date(resp.data[0].FechaSolicitudIngresoSop),
                            type: "datetime",
                        }
                        this.datePuntualidad = {
                            value: new Date(resp.data[0].FechaInicioCirugia),
                            type: "datetime",
                        }
                    } else {
                        this.dateIngreso = {
                            value: new Date(resp.data[0].FechaIngreso),
                            type: "datetime",
                        }

                        this.dateNota = {
                            value: new Date(),
                            type: "datetime"
                        }
                        this.dateCirugia = {
                            value: new Date(),
                            type: "datetime",
                            onValueChanged: this.valueChangedCirugia
                        }
                        this.dateValidacion = {
                            type: "datetime",
                            onValueChanged: this.valueChangedValidacion
                        }
                        this.dateVisita = {
                            type: "datetime",
                            onValueChanged: this.valueChangedVisita
                        }
                        this.dateSolicitud = {
                            type: "datetime",
                            onValueChanged: this.valueChangedSolicitud
                        }
                        this.datePuntualidad = {
                            type: "datetime",
                            onValueChanged: this.valueChangedPuntualidad
                        }
                    }

                    if (this.idRegistroAnestesico === null) {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Estadística',
                            acceptText: 'Aceptar',
                            text: `No se ha encontrado un registro anestésico para asociar la estadística`,
                            buttonCancel: 'border',
                            clientWidth: 100,
                            accept: () => {},
                        })
                    }

                })
        }
    },
    watch: {},
    computed: {
        estadisticaDataForm: function () {
            return this.$refs[dataEncabezadoRefKey].instance;
        },
    },
}
</script>
