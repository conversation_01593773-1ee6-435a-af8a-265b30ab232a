<template>
<vx-card title="Aseguradoras">
    <div class="content content-pagex">

        <vs-divider>Mantenimiento Aseguradora</vs-divider>

        <div class="ais-SearchBox">
            <div>
                <div class="relative mb-8">
                    <div class="vs-component vs-con-input-label vs-input w-full vs-input-shadow-drop vs-input-no-border d-theme-input-dark-bg vs-input-primary">
                        <div class="vs-con-input">
                            <input type="text" class="vs-inputx vs-input--input large" placeholder="Buscar" style="border: 1px solid rgba(0, 0, 0, 0.2);" v-model="buscar">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="append-text btn-addon">
               <!-- <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="cargar_Aseguradora()" icon="icon-search" ></vs-button> -->
                <vs-button color="success" icon-pack="feather" icon="icon-plus" style="float:left" type="filled"  @click="nuevo()">Nuevo</vs-button>
        </div>

        <vs-table :data="aseguradora" >
            <template slot="thead">
                <vs-th>Empresa</vs-th>
                <vs-th>Asegura</vs-th>
                <vs-th>Tipo</vs-th>
                <vs-th>Nit</vs-th>
                <vs-th>Descripción</vs-th>
                <vs-th>Días Hábiles</vs-th>
                <vs-th>Estado</vs-th>
                <vs-th>Opciones</vs-th>
            </template>
            <template >
                <vs-tr :key="indextr" v-for="(tr, indextr) in aseguradora_filtro">
                    <vs-td :data="tr.Empresa">
                        [{{ tr.Empresa }}] 
                    </vs-td>
                    <vs-td :data="tr.Asegura">
                        {{ tr.Asegura }}
                    </vs-td>
                      <vs-td :data="tr.Tipo">
                        {{ tr.Tipo }}
                    </vs-td>
                    <vs-td :data="tr.Nit">
                        {{ tr.Nit }}
                    </vs-td>
                      <vs-td :data="tr.Descripcion">
                        {{ tr.Descripcion }} 
                    </vs-td>
                    <vs-td :data="tr.Dias_Habiles">
                        {{ tr.Dias_Habiles }}
                    </vs-td>
                     <vs-td :data="tr.Estado">
                        <vx-tooltip text="Estado" position="bottom">
                         <vs-switch color="success" :disabled=true v-model="tr.Estado" icon-pack="feather" vs-icon="icon-lock"  />
                        </vx-tooltip>
                    </vs-td>
                    <vs-td>
                        <vs-button color="primary" icon-pack="feather" icon="icon-edit" style="display:inline-block;margin-right:2px" @click="opciones(tr)"></vs-button>
                        <vs-button color="danger" icon-pack="feather" icon="icon-x-square" style="display:inline-block" @click="eliminar(tr)"></vs-button>
                    </vs-td>
                </vs-tr>
            </template>
        </vs-table>
    </div>


<!-- ========================================================================================================================================================== -->
<!-- POPUP INICIO -->
<!-- ========================================================================================================================================================== -->
    <form>    
     
        <vs-popup classContent="popup-example" :title="'['+Descripcion+'] - ' + Nit"  :active.sync="show">
        <form color="rgba(var(--vs-primary), 1)" errorColor="rgba(var(--vs-danger), 1)" >
            <ValidationObserver v-slot="{invalid,handleSubmit}">
            <div style="padding:10px;border-radius:5px; border:1px solid #ccc;">
                
             
                <div class="flex flex-wrap">
                    <div class="w-full">
                        <ValidationProvider name="Aseguradora" rules="required:max:3" >
                        <vs-input label="Código Aseguradora" class="w-full"  v-model="Asegura"  />
                        <span style="position:relative;top:-2px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                        </ValidationProvider>
                    </div>
                </div>
                
                <div class="flex flex-wrap">
                    <div class="w-full">
                        <ValidationProvider name="Tipo" rules="required" >
                        <vs-input label="Tipo" class="w-full" v-model="Tipo"  />
                        <span style="position:relative;top:-2px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                        </ValidationProvider>
                    </div>
                </div>

                <div class="flex flex-wrap">
                    <div class="w-full">
                        <ValidationProvider name="Nit" rules="required" >
                        <vs-input label="Nit" class="w-full"  v-model="Nit" name="Nit_v"  />
                        <span style="position:relative;top:-2px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                        </ValidationProvider>
                    </div>
                </div>

                <div class="flex flex-wrap">
                    <div class="w-full">
                        <ValidationProvider name="Descripcion" rules="required" >
                        <vs-input label="Descripción" class="w-full"  v-model="Descripcion" />
                        <span style="position:relative;top:-2px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                        </ValidationProvider>
                    </div>
                </div>

                <div class="flex flex-wrap">
                    <div class="w-full">
                        <ValidationProvider name="Dias_v" rules="required" >
                        <vs-input label="Días Habiles" class="w-full" type="number" v-model="Dias_Habiles" />
                        <span style="position:relative;top:-2px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                        </ValidationProvider>
                           <!--  <div class="centerx">
                            <vs-input-number v-model="Dias_Habiles"/>
                             </div>-->
                    </div>
                </div>

                <div class="flex flex-wrap">
                    <vx-tooltip text="Estado" position="bottom">
                    <label>Estado</label>
                    <vs-switch color="success" v-model="Estado" icon-pack="feather" vs-icon="icon-lock"  />
                    </vx-tooltip>
                </div>
               
                <vs-divider></vs-divider>
                <vs-button style="float:right" @click="handleSubmit(post_aseguradora(invalid))"> Guardar</vs-button>
                <vs-button color="primary" style="float:right" type="border" @click="show=false"> Salir</vs-button>
                <div style="clear:both"></div>

            </div>
            </ValidationObserver>
        </form>
        </vs-popup>

        <vs-popup classContent="popup-example" :title="'Aseguradora'" :active.sync="showdel" >
         <div style="padding:10px;border-radius:5px; border:1px solid #ccc; font: 22px Arial;">
            <div class="vx-col w-full">
            <h2 class="mb-2">¿Estimado Usuario Esta Seguro de Eliminar la Aseguradora - {{Descripcion}}?</h2>
            </div>
                <!--<vs-divider></vs-divider> -->
                <vs-button color="success" style="float:right" type="filled" icon-pack="feather" icon="icon-save" @click="delete_aseguradora"> Aceptar</vs-button>
                <vs-button color="primary" style="float:right" type="border" @click="showdel=false"> Cancelar</vs-button>
                <div style="clear:both"></div>
        </div>
        </vs-popup>

    </form>
<!-- ========================================================================================================================================================== -->
<!-- POPUP FIN -->
<!-- ========================================================================================================================================================== -->
 
</vx-card>
</template>

<script>
import {
    ValidationObserver,
    ValidationProvider,

} from "vee-validate";



export default {
    data() {
        return {
            aseguradora: [],
            buscar:'',
            show: false, //mostrar las opciones de la Aseguradora
            showdel:false,//Opcion Eliminar Aseguradora                  
            IdAsegura: 0, 
            Empresa: '',
            Asegura: '',
            Tipo:'',
            Nit: '',
            Descripcion: '',
            Dias_Habiles:0,
            Estado: false,
        }
    },
    components: {
        ValidationProvider,
        ValidationObserver,
    },
    computed: {
        aseguradora_filtro(){
        if (this.buscar.trim()==''){

          return this.aseguradora
        }else{
          return this.aseguradora.filter(data=>data.Dias_Habiles==this.buscar || data.Descripcion.toUpperCase().indexOf(this.buscar.toUpperCase())>=0 || data.Nit.toUpperCase().indexOf(this.buscar.toUpperCase())>=0)
        }
      }
    },
    methods: {
        cargar_Aseguradora() {
            this.axios.post('/app/admision/GETAseguradora', {
                    Operacion: 'C'
                })
                .then(resp => {
                    this.aseguradora = []
                    resp.data.json.map(data => {
                        this.aseguradora.push({
                            IdAsegura: data.IdAsegura,
                            Empresa: data.Empresa,
                            Asegura: data.Asegura,
                            Tipo: data.Tipo,
                            Nit: data.Nit,
                            Descripcion: data.Descripcion,
                            Dias_Habiles: data.Dias_Habiles,
                            Estado: (data.Estado==1?true:false),
                            Fecha_Registro: data.Fecha_Registro   
                        })
                    })
     
                })
        },      
        post_aseguradora(validar) {

                            if (validar) {
                                
                                this.$vs.notify({
                                    color: 'danger',
                                    title: 'Producto',
                                    text: 'Estimado Usuario, Tiene campos obligatorios por Completar.',
                                })

                            }else{

                                this.axios.post('/app/admision/POSTAseguradora', {
                                Operacion: (this.IdAsegura==0) ? 'I': 'A',
                                Id: this.IdAsegura,
                                Asegura: this.Asegura.trim(),
                                Tipo: this.Tipo.trim(),
                                Nit: this.Nit.trim(),
                                Descripcion: this.Descripcion.trim(),
                                Dias: this.Dias_Habiles,
                                Estado: (this.Estado== true)?1:0,
                                bitacora: [{
                                    llave: this.Tipo + "-" + this.Nit + "-" + this.Descripcion,
                                    tipo: "Aseguradora",
                                    registros: [
                                        'Mantenimiento Aseguradora'
                                    ]
                                }]
                                })
                                .then(resp => {

                                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                        if(resp.data.json[0].codigo==-3001){
                                            this.$vs.notify({
                                                color: 'danger',
                                                title: 'Aseguradoras',
                                                text: resp.data.json[0].error,
                                            })
                                            this.show = false;
                                        }else{
                                            this.cargar_Aseguradora()
                                            this.$vs.notify({
                                                color: 'success',
                                                title: 'Aseguradoras',
                                                text:  (this.IdAsegura==0) ? 'Información guardada exitosamente': 'Información actualizada exitosamente',
                                            })
                                            this.show = false;
                                        }
                                        //resolve(true)
                                    }
                                })
                                .catch(() => {
                                    this.$vs.notify({
                                        color: 'danger',
                                        title: 'Aseguradoras',
                                        text:  (this.IdAsegura==0) ?'Error al guardar la información' :'Error al actualizar la información',
                                    })
                                    //resolve(true)
                                })


                            } 
               
            

        },
        delete_aseguradora() {
            this.axios.post('/app/admision/POSTAseguradora', {
                    Operacion: 'E',
                    Id: this.IdAsegura,
                    Asegura: this.Asegura.trim(),
                    Tipo: this.Tipo.trim(),
                    Nit: this.Nit.trim(),
                    Descripcion: this.Descripcion.trim(),
                    Dias: this.Dias_Habiles,
                    Estado: (this.Estado== true)?1:0,
                    bitacora: [{
                        llave: this.Tipo + "-" + this.Nit + "-" + this.Descripcion,
                        tipo: "Aseguradora",
                        registros: [
                            'Mantenimiento Aseguradora-Eliminar'
                        ]
                    }]
                })
                .then(() => {
                    this.cargar_Aseguradora()
                    this.$vs.notify({
                        color: 'success',
                        title: 'Aseguradoras',
                        text: 'Información eliminada exitosamente',
                    })
                    this.showdel = false;
                })
                .catch(() => {
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Aseguradoras',
                        text: 'Error al eliminar la información',
                    })
                })
        },
        opciones(op) {
            this.show = true
            this.showdel = false
            this.IdAsegura = op.IdAsegura
            this.Asegura = op.Asegura.trim()
            this.Tipo = op.Tipo.trim()
            this.Nit = op.Nit.trim()
            this.Descripcion = op.Descripcion.trim()
            this.Dias_Habiles = op.Dias_Habiles
            this.Estado = op.Estado
        },
        nuevo() {
            this.show = true
            this.showdel = false
            this.IdAsegura = 0
            this.Asegura = ''
            this.Tipo = ''
            this.Nit = ''
            this.Descripcion = ''
            this.Dias_Habiles = 0
            this.Estado =false
        },
        eliminar(op) {
            this.show = false
            this.showdel = true
            this.IdAsegura = op.IdAsegura
            this.Asegura = op.Asegura.trim()
            this.Tipo = op.Tipo.trim()
            this.Nit = op.Nit.trim()
            this.Descripcion = op.Descripcion.trim()
            this.Dias_Habiles = op.Dias_Habiles
            this.Estado = op.Estado
        },
    },
    mounted(){ 
    },
    created() {
      this.cargar_Aseguradora()
    }
}
</script>

<style>
.button {
    margin: 0px !important;
}

.btnx {
    margin-left: 10px !important;
    background: red;
    border-radius: 5px 0px 0px 5px;
}

.btn-drop {
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: red;

}

.menu-funcionalidad {
    /* padding-top:0 !important; */
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    padding: 15px;
    display: inline;
    float: right;
    border-radius: 10px;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 10px;
}

.menu-funcionalidad td {
    text-align: center;
    padding-right: 10px;
}

.vs-switch {
    height: 30px;
    width: 60px !important;
}

.vs-circle-switch {
    height: 25px;
    width: 25px;
}

.vs-switch--icon {
    font-size: 15px
}

.vs-switch.vs-switch-active .vs-switch--circle {
    margin-left: calc(100% - 30px);
}
</style>
