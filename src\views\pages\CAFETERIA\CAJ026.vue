<template>
    <vx-card  title="Devolución sin Admisión" > 
        <div>
            <vs-button @click="Editar = true, NuevaDevolucion()" color="success" icon-pack="fas" icon="fa-plus-circle" >Nueva Devolucion</vs-button>
        </div>
        <vs-divider border-style="solid" color="dark"></vs-divider>
        <SM-Tabs height="auto">
            <div class="tab p-4" label="Consultas" >
                <div class="flex">
                    <h5>Listado de Devoluciones sin Admisiones</h5>
                    <vs-spacer></vs-spacer>
                </div>
                <br>
                <div class="flex flex-wrap" >
                    <div class="w-full  p-2">
                        <vs-table2 max-items="10" search pagination :data="ListaInfo" height="470px" class="mt-0 mb-0">
                            <template slot="thead">
                                <th order="CodigoDevolucion">Codigo</th>
                                <th order="SerieRecibo">Serie</th>
                                <th order="CodigoRecibo" filtro="Fecha">Recibo</th>
                                <th order="Fecha">Fecha</th>
                                <th order="Valor">Valor</th>
                                <th order="Razon">Razon</th>                          
                                <th></th>   
                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2>{{tr.CodigoDevolucion}}</vs-td2>
                                    <vs-td2>{{tr.SerieRecibo}}</vs-td2>
                                    <vs-td2>{{tr.CodigoRecibo}}</vs-td2>
                                    <vs-td2>{{tr.Fecha}}</vs-td2>
                                    <vs-td2>{{tr.Valor}}</vs-td2>
                                    <vs-td2>{{tr.Razon}}</vs-td2>
                                    <vs-td2>
                                        <vs-button @click="Editar = true, Nuevo = false" @click.native=Cargar().CargaReciboEditar(tr) color="primary" size="small" style="display:inline" class="mr-2" icon-pack="feather" icon="icon-edit"></vs-button>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                </div>
            </div>
            <div class="tab p-4" Label="Edicion" v-if="Editar" >
                <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                    <div class="flex flex-wrap">
                        <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">
                            <label>Datos Devolución</label>
                                <div class="flex flex-wrap">
                                    <br>
                                    <div class="xs:w-full md:w-6/12 lg:w-6/12 xl:w-6/12">
                                        <vs-input label="Código" class="w-full"  v-model="CodigoDevolucion" disabled/>
                                    </div>
                                    <div class="xs:w-full md:w-1/12 lg:w-1/12 xl:w-1/12"></div>
                                    <div class="xs:w-full md:w-5/12 lg:w-5/12 xl:w-5/12">
                                        <vs-input  label="Fecha" class="w-full" v-model="Fecha" disabled/>
                                    </div>
                                </div>
                        </div>   
                        <div class="xs:w-full md:w-2/12 lg:w-2/12 xl:w-2/12">
                            <!-- <vs-input label="Recibo dis" class="w-full" :value="Info.Recibo" disabled/> -->
                        </div>
                        <div class="xs:w-full md:w-6/12 lg:w-6/12 xl:w-6/12">
                            <label>Devolución por Recibo</label>
                            <div class="flex flex-wrap">
                                <div class="xs:w-full md:w-2/12 lg:w-2/12 xl:w-2/12">
                                    <vs-input label="Serie" class="w-full" v-model="SerieRecibo" disabled/>
                                </div>
                                <div class="xs:w-full md:w-1/12 lg:w-1/12 xl:w-1/12"></div>

                                <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">
                                    <ValidationProvider name="razon" rules="required" class="required" v-slot="{ errors }">
                                        <SM-Buscar label="Recibo" v-model="CodigoRecibo"  api="app/v1_JefeCaja/CargaRecibos"
                                        :api_campos="['FechaRecibo', 'SerieRecibo', 'CodigoRecibo','ValorRecibo',	'ValorCuenta']" 
                                        :api_titulos="['#Fecha', 'Serie', 'Recibo',	'#Valor Recibo', '#Valor Cuenta']" 
                                        :disabled_texto="true"  
                                        :mostrar_busqueda="true" :callback_buscar="Cargar().CargaRecibo" 
                                        :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                                    </ValidationProvider>
                                </div>
                            </div>
                        </div>
                            <!-- <vs-divider></vs-divider> -->
                    </div>
                

                        <!-- <vs-divider></vs-divider> -->
                        <div class="w-full">
                            <!-- <label>Datos Factura</label> -->
                            <div class="flex flex-wrap">
                                <!-- <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">
                                    <div class="flex flex-wrap">
                                        <div class="xs:w-full md:w-3/12 lg:w-3/12 xl:w-3/12">
                                        <vs-input label="Serie" class="w-full"  v-model="Info.SerieFact" />
                                    </div>
                                        <div class="xs:w-full md:w-2/12 lg:w-2/12 xl:w-2/12"></div>
                                        <div class="xs:w-full md:w-6/12 lg:w-6/12 xl:w-6/12">
                                            <vs-input label="Factura" type="Factura" class="w-full"  v-model="Info.Factura" />
                                        </div>
                                    </div>
                                </div> -->
                                <div class="xs:w-full md:w-2/12 lg:w-2/12 xl:w-2/12">
                                    <br>
                                        <vx-input-group  class="mb-base" >
                                            <template  slot="prepend">
                                                <div class="prepend-text bg-primary">
                                                    <span>Q</span>
                                                </div>
                                            </template>
                                            <ValidationProvider rules="numero_minimo:0|numero_decimal:2|required" v-slot="{ errors }" >
                                                <vs-input  v-model="Valor"
                                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"/>
                                            </ValidationProvider>
                                        </vx-input-group>   
                                </div>
                                <vs-divider></vs-divider>
                            </div>    
                        </div>
                        
                        <div class="w-full">   
                            <!-- <vs-input  label="Razón" class="w-full" :value="info.Razon"/> -->
                            <ValidationProvider rules="max:250|required" v-slot="{ errors }">
                                <vs-textarea  label="*Razón"   class="w-full"  v-model="Razon" 
                                    :danger="errors.length > 0"
                                    :danger-text="(errors.length > 0) ? errors[0] : null" />
                            </ValidationProvider>
                            <br>
                            <vs-divider></vs-divider>
                        </div>
                        <div class="w-full">
                            <div class="flex flex-wrap">
                                <div class="xs:w-full md:w-2/12 lg:w-2/12 xl:w-2/12">
                                        <label>Cheque</label>
                                        <ValidationProvider name="razon" rules="required" class="required" v-slot="{ errors }">
                                        <!-- <vs-input  label="Cuenta" class="w-full" :value="info.Cuenta"/> --> 
                                        <SM-Buscar label="Cuenta" v-model="CuentaBanco"  api="app/v1_JefeCaja/ObtieneBancos"
                                        :api_campos="['Empresa',	'NCuenta',	'NombreCompleto']" 
                                        :api_titulos="['Empresa',	'Cuenta',	'Nombre Cuenta']" 
                                        :api_preload="true" :disabled_texto="true" 
                                        :mostrar_busqueda="true" :callback_buscar="Cargar() .CargaCuenta"
                                        :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />   
                                    </ValidationProvider>  
                                </div> 
                                <div class="xs:w-full md:w-1/12 lg:w-1/12 xl:w-1/12"></div>
                                <div class="xs:w-full md:w-9/12 lg:w-9/12 xl:w-9/12">
                                    <br>
                                    <br>
                                    <label>{{Banco}}</label>  
                                </div>
                                <div class="xs:w-full md:w-2/12 lg:w-2/12 xl:w-2/12">
                                    <ValidationProvider name="razon" rules="required" class="required" v-slot="{ errors }">
                                        <vs-input type="number" label="Cheque" class="w-full" v-model="Movimiento" v-on:blur="Cargar().CargaFechaCheque()"
                                        :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"/>
                                    </ValidationProvider>    
                                </div> 
                                <div class="xs:w-full md:w-1/12 lg:w-1/12 xl:w-1/12"></div>
                                <div class="xs:w-full md:w-9/12 lg:w-2/12 xl:w-2/12">
                                    <vs-input label="Fecha Cheque" class="w-full" v-model="FechaPago" disabled/>
                                </div>
                            </div>
                            <br>
                        </div>                  
                        <vs-divider></vs-divider>
                        <div class="w-full">
                            <label>Aplicada a otra Admisión</label>
                            <div class="flex flex-wrap">
                                <div class="xs:w-full md:w-6/12 lg:w-6/12 xl:w-6/12">
                                    <div class="flex flex-wrap">
                                    <div class="xs:w-full md:w-2/12 lg:w-2/12 xl:w-2/12">
                                        <vs-input label="Serie" class="w-full" v-model="RelacionSerieAdmision"/>
                                    </div>
                                    <div class="xs:w-full md:w-1/12 lg:w-1/12 xl:w-1/12"></div>
                                    <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">
                                        <vs-input label="Admisión" type="number" class="w-full" v-model="RelacionAdmision"/>
                                    </div>
                                    </div>
                                    <vs-divider></vs-divider>
                                </div>
                                <div class="xs:w-full md:w-6/12 lg:w-5/12 xl:w-6/12">
                                    <div class="flex flex-wrap">
                                        <div class="xs:w-full md:w-2/12 lg:w-2/12 xl:w-2/12">
                                            <vs-input label="Serie" class="w-full" v-model="RelacionSerieRecibo"/>
                                        </div>
                                        <div class="xs:w-full md:w-1/12 lg:w-1/12 xl:w-1/12"></div>
                                        <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">
                                            <vs-input label="Recibo" type="number" class="w-full" v-model="RelacionRecibo"/>
                                        </div>
                                        <br>
                                    </div>
                                    <vs-divider></vs-divider>
                                </div>
                                <div class="xs:w-full md:w-2/12 lg:w-2/12 xl:w-2/12">
                                    <vs-button color="success" class="md:w-full"
                                        @click="handleSubmit(Guardar())" :disabled="invalid">
                                        Guardar
                                    </vs-button>
                                </div>
                            </div>    
                        </div>
                    </ValidationObserver>        
                </div>
        </SM-Tabs> 
    </vx-card>
  </template>
  
  <script>
  
  export default {
    data(){
        return{
            CodigoDevolucion: 0,
            CodigoRecibo: null, 
            FechaRecibo: null,
            ValorRecibo: 0,
            ValorCuenta: 0,
            Valor: 0,
            Fecha: null,
            CuentaBanco: 0,
            Movimiento: 0,
            Razon: null,
            Usuario: null,
            Periodo: 0,
            RelacionSerieAdmision: null,
            RelacionAdmision: 0,
            SerieRecibo: null,
            Recibo: 0,
            RelacionSerieRecibo: null,
            RelacionRecibo: 0,
            SerieFact: null,
            Factura: 0,
            FechaPago: null,
            Banco: null,
            NombrePaciente: null,
            ListaInfo: [],
            Editar: false,
            Nuevo: false  
        }
    },
        methods:{
        Cargar(){
            return {
                CargaRecibosInfo: ()=>{
                    this.axios.post('/app/v1_JefeCaja/CargaDevolucionesSinAdmin', {})
                    .then(resp =>{
                        if (resp.data.codigo == 0) {
                            this.ListaInfo = resp.data.json.map(m => {
                                return {
                                    ...m,
                                    Valor: parseFloat(m.Valor).toFixed(2)
                                }
                            })
                        }
                    })
                },
                ObtieneInfoRecibo: ()=>{
                    let MaxCodigoDev = 0
                    const NewFechaPago = new Date(Date.now())
                    const[month, day, year] = [ NewFechaPago.getMonth() + 1, NewFechaPago.getDate(), NewFechaPago.getFullYear() ]
                    const dia = day < 10 ? '0' + day : day
                    const mes = month < 10 ? '0' + month : month
                    this.axios.post('/app/v1_JefeCaja/RecuperaCodigoDevolucion', {})
                    .then(resp => {
                        MaxCodigoDev = resp.data.CodigoAnulacion
                        this.axios.post('/app/v1_JefeCaja/ObtieneInfoRecibo', {
                            SerieRecibo: this.SerieRecibo,
                            CodigoRecibo: this.CodigoRecibo
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                const{Banco, CodigoDevolucion, CuentaBanco, CodigoRecibo, Fecha, FechaPago, Movimiento, Periodo, Razon, RelacionAdmision, RelacionRecibo, RelacionSerieAdmision, RelacionSerieRecibo, SerieRecibo, Valor} = resp.data.json[0]
                                this.Banco = Banco
                                this.CodigoDevolucion = CodigoDevolucion != '' ? CodigoDevolucion : MaxCodigoDev
                                this.Nuevo = CodigoDevolucion != '' ? false : true
                                this.CodigoRecibo = CodigoRecibo
                                this.CuentaBanco = CuentaBanco
                                this.Fecha = Fecha != '' ? Fecha : `${dia}/${mes}/${year}`
                                this.FechaPago = FechaPago 
                                this.Movimiento = Movimiento
                                this.Periodo = Periodo
                                this.Razon = Razon
                                this.RelacionAdmision = RelacionAdmision
                                this.RelacionRecibo = RelacionRecibo
                                this.RelacionSerieAdmision = RelacionSerieAdmision
                                this.RelacionSerieRecibo = RelacionSerieRecibo
                                this.SerieRecibo = SerieRecibo
                                this.Valor = Valor != '' ?  parseFloat(Valor).toFixed(2) : 0

                                this.Cargar().CargaFechaCheque()
                            }
                        })
                        
                    })
                       
                },
                CargaReciboEditar: (data)=>{
                    this.SerieRecibo = data.SerieRecibo
                    this.CodigoRecibo = data.CodigoRecibo

                    this.Cargar().ObtieneInfoRecibo()
   
                },
                CargaRecibo: (data) => {
                    this.SerieRecibo = data.SerieRecibo
                    this.CodigoRecibo = data.CodigoRecibo
                    // this.Nuevo = true
                    // this.Cargar().limpiar()
                },
                CargaCuenta: (data)=>{
                        this.CuentaBanco = data.NCuenta;
                        this.Banco = data.NombreCompleto
                },
                CargaFechaCheque: ()=>{  
                        this.axios.post('/app/v1_JefeCaja/ObtieneFechaCheque', {
                            CuentaBanco: this.CuentaBanco,
                            Movimiento: this.Movimiento
                        })
                        .then(resp =>{
                            if (resp.data.codigo == 0) {
                                resp.data.json.map(data => {
                                    this.FechaPago = data.FechaCheque
                                })
                            }
                    })
                },
                limpiar: () => {
                    let MaxCodigoDev = 0
                    const NewFechaPago = new Date(Date.now())
                    const[month, day, year] = [ NewFechaPago.getMonth() + 1, NewFechaPago.getDate(), NewFechaPago.getFullYear() ]
                    const dia = day < 10 ? '0' + day : day
                    const mes = month < 10 ? '0' + month : month
                    this.Fecha = `${dia}/${mes}/${year}`
                    this.axios.post('/app/v1_JefeCaja/RecuperaCodigoDevolucion', {})
                    .then(resp => {
                        MaxCodigoDev = resp.data.CodigoAnulacion
                        this.Banco = ''
                        this.CodigoDevolucion = MaxCodigoDev
                        this.CuentaBanco = ''
                        this.Fecha = `${dia}/${mes}/${year}`
                        this.FechaPago = '' 
                        this.Movimiento = ''
                        this.Periodo = ''
                        this.Razon = ''
                        this.RelacionAdmision = ''
                        this.RelacionRecibo = ''
                        this.RelacionSerieAdmision = ''
                        this.RelacionSerieRecibo = ''
                        this.Valor =  0
                    })
                    
                },
                init: () => {
                    this.Cargar().CargaRecibosInfo()
                    
                }
            }
        },
        NuevaDevolucion(){
            this.SerieRecibo = ''
            this.CodigoRecibo = ''
            this.Nuevo = true
            this.Cargar().limpiar()
        },
        Guardar(){
            if(this.Nuevo){
                this.axios.post('/app/v1_JefeCaja/GuardarDevolucionSinAdmin', {
                    'CodigoAnulacion': this.CodigoDevolucion,
                    'CuentaBanco': this.CuentaBanco,
                    'Movimiento': this.Movimiento,
                    'Razon': this.Razon,
                    'Valor': this.Valor,
                    'RelaSerieAdmin': this.RelacionSerieAdmision,
                    'RelaAdmin': this.RelacionAdmision,
                    'SerieRecibo': this.SerieRecibo,
                    'CodigoRecibo': this.CodigoRecibo,
                    'RelaSerieRecibo': this.RelaSerieRecibo,
                    'RelaRecibo': this.RelacionRecibo,
                    'FechaPago': this.FechaPago
                })
            }else{
                this.axios.post('/app/v1_JefeCaja/ActualizarDevolucionSinAdmin', {
                    'CodigoAnulacion': this.CodigoDevolucion,
                    'CuentaBanco': this.CuentaBanco,
                    'Movimiento': this.Movimiento,
                    'Razon': this.Razon,
                    'Valor': this.Valor,
                    'RelaSerieAdmin': this.RelacionSerieAdmision,
                    'RelaAdmin': this.RelacionAdmision,
                    'SerieRecibo': this.SerieRecibo,
                    'CodigoRecibo': this.CodigoRecibo,
                    'RelaSerieRecibo': this.RelaSerieRecibo,
                    'RelaRecibo': this.RelacionRecibo,
                    'FechaPago': this.FechaPago
                })
            }
        }
    },
    mounted() {
        this.Cargar().init();   
    }
}
  </script>