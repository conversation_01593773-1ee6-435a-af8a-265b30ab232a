<template>
    <div>
        <div>
            <vx-card>
                <h3>Búsqueda De Cupones</h3>
                <br>
                <div style="display: flex">
                    <div class="Derecha">
                        <div style="margin-bottom: 15px; display:flex;">
                            <vx-input-group>
                                <ValidationProvider rules="required" v-slot="{ errors }">
                                    <vs-input v-model="info.Afiliado" @keyup.enter="BuscarDatosAfiliadoEncabezado().BuscarDatosAfiliado()" class="w-full" :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                                </ValidationProvider>
                                <template slot="append">
                                    <div class="append-text btn-addon">
                                        <vs-button v-if="info.Afiliado == null"  id="button-with-loading" @click="popupActivo = true" color="primary" icon-pack="feather" icon="icon-search"></vs-button>
                                        <vs-button v-else id="button-with-loading" @click="LimpiarCampos()" color="danger" icon-pack="feather" icon="icon-delete"></vs-button>
                                    </div>
                                </template>
                            </vx-input-group>
                            &nbsp;
                            <div>
                                <vs-button color="primary" :disabled="!info.Afiliado" type="filled" @click="BuscarDatosAfiliadoEncabezado().BuscarDatosAfiliado()">Buscar</vs-button>
                            </div>
                            &nbsp;
                            <div>
                                <vs-button icon="print" color="warning" :disabled="!info.Afiliado" type="filled" @click="CargarPDF()">Listado de Cupones</vs-button>
                            </div>
                        </div>
                        <vx-card>

                            <div style="display: flex;" v-if="info.Nombre || info.NumeroAdhesion"> 
                                <div >
                                    <div class="notificacion2">
                                        <i class="fas fa-heartbeat"></i>
                                        <div class="titulo">AFILIADO</div>
                                    </div>
                                </div>
                                <div>
                                    <div class="w-full sm:w-1/1 p-2">
                                        <label>Código  Afiliado:</label>
                                        &nbsp;
                                        <label style="color:black">{{ info.Afiliado || cupones.Afiliado }}</label>
                                    </div>
                                    <div class="w-full sm:w-1/1 p-2">
                                        <label>Número Adhensión:</label>
                                        &nbsp;
                                        <label style="color:black">{{ info.NumeroAdhesion }}</label>
                                    </div>
                                    <div class="w-full sm:w-1/1 p-2">
                                        <label>Nombre del Paciente Afiliado:</label>
                                        &nbsp;
                                        <label style="color:black">{{ info.Nombre }}</label>
                                    </div>
                                    <div class="w-full sm:w-1/1 p-2">
                                        <label>Plan:</label>
                                        &nbsp;
                                        <label style="color:black">{{ info.Planes }}</label>
                                    </div>
                                    <div class="w-full sm:w-1/1 p-2">
                                        <label>Código  Cliente:</label>
                                        &nbsp;
                                        <label style="color:black">{{ info.Idcliente }}</label>
                                    </div>

                                </div>
                            </div>
                        </vx-card>
                    </div>
                </div>
                <div style="margin:5px">
                                             
                    <vs-table2 search :data="cupones" class="custom-table colors" pagination max-items="5" >
                    <template slot="thead">
                        <th width="80px">Cupón</th>
                        <th width="150px">Fecha</th>
                        <th>Nombre Médico</th>
                        <th width="180px" >Especialidad</th>
                        <th width="70px">Factura</th>
                        <th>Direccion</th>
                        <th width="100px">Reimpresión</th>
                        <th width="80px">Impresión</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2>{{ tr.Cupon }}</vs-td2>
                            <vs-td2>{{ tr.Fecha }}</vs-td2>
                            <vs-td2>{{ tr.NombreMedico }}</vs-td2>
                            <vs-td2>{{ tr.Especialidad }}</vs-td2>
                            <vs-td2>{{ tr.SerieFactura }}</vs-td2>
                            <vs-td2>{{ tr.Direccion }}</vs-td2>
                            <center>
                                <vs-td2>{{ tr.Reimpresion }}</vs-td2>
                            </center>
                            <vs-td2>
                                <vs-button :disabled="tr.Reimpresion == 2" color="warning" icon-pack="feather" @click="MostrarPDF(tr)" icon="icon-printer"></vs-button>
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>
                </div>

            </vx-card>
        </div>
        <div>
        </div>
        <!--Popup-->
        <vs-popup title="Buscar Afiliado" :active.sync="popupActivo" class="PopupBuscarAfiliado" >
            <div style="display: flex;" class="p-1 w-full md:w-1/4 lg:w-1/4 xl:w-1/4">
                <div class="w-full">
                    <vs-input placeholder="Nombre 1" v-model="info.Nombre1"  @keyup.enter="validarCampos" />                    
                </div>
                &nbsp;
                <div class="w-full">
                    <vs-input placeholder="Nombre 2" v-model="info.Nombre2"  @keyup.enter="validarCampos" />
                </div>
                &nbsp;
                <div class="w-full">
                    <vs-input placeholder="Apellido 1" v-model="info.Apellido1"  @keyup.enter="validarCampos" />
                </div>
                &nbsp;
                <div class="w-full">
                    <vs-input placeholder="Apellido 2" v-model="info.Apellido2"  @keyup.enter="validarCampos" />
                </div>                    
                <div style="display: flex;">
                    &nbsp;
                    <DxButton
                        icon="search"
                        type="default"
                       @click="validarCampos()"
                        />
                        &nbsp;
                    <DxButton
                       icon="clear"
                        type="danger"
                       @click="LimpiarCamposPopup()"
                        />
                </div>
            </div>
            <br>
            <div >
                <vs-table2 max-items="6" pagination search :data="resultados" class="mt-0 mb-0">
                    <template slot="thead">
                        <th>Afiliado</th>
                        <th>Nombres Afiliado</th>
                        <th>Cliente</th>
                        <th>Número Adhesión</th>
                        <th>Status</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data" @click="handleAffiliateSelection(tr)">
                            <vs-td2>{{ tr.IdAfiliado }}</vs-td2>
                            <vs-td2>{{ tr.NombreCompleto }}</vs-td2>
                            <vs-td2>{{ tr.IdCliente }}</vs-td2>
                            <vs-td2>{{ tr.NumeroAdhesion }}</vs-td2>
                            <vs-td2>{{ tr.Status }}</vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
        </vs-popup>
    </div>
</template>

<script>


import Swal from 'sweetalert2'


export default {
    data() {
        return {
            popupActivo: false,

            info: {
                Afiliado: null,
                NumeroAdhesion: null,
                Idcliente: null,
                Nombre: null,
                Planes: null,

                //Busqueda 
                Nombre1: null,
                Nombre2: null,
                Apellido1: null,
                Apelldio2: null
            },

            //Listado de Cupones 
            cupones: [],
            //Resultados De busqueda 
            resultados: [],


        };
    },


    computed: {
        sesion() {
            return this.$store.state.sesion
        },
        global() {
            return this.$store.state.global
        }
    },

    methods: {
        // ========================================================================================================== 
        // BUSCAR DATOS     
        // ==========================================================================================================
        // ========================================================== 
        // BUSCAR DATOS AFILIADO    
        // ==========================================================
        BuscarDatosAfiliadoEncabezado: function () {
            return {
                BuscarDatosAfiliado: () => {
                    this.axios.post('/app/v1_autorizaciones/BusquedaCupones', {
                        Operacion: 'DATOSAFILIADO',
                        Afiliado: this.info.Afiliado
                    })
                        .then(resp => {
                            if (resp.data.json) {
                                if (resp.data.json == null || resp.data.json == undefined || resp.data.json.length == 0) {
                                    // Mostrar notificación indicando que 'res.data.json' está vacío
                                    this.$vs.notify({
                                        title: 'ADVERTENCIA!',
                                        text: 'El Afiliado no Existe!',
                                        iconPack: 'feather',
                                        icon: 'icon-alert-circle',
                                        color: 'danger'
                                    })
                                } else {
                                    this.$map({
                                        objeto: this.info,
                                        respuestaAxios: resp,
                                    });
                                }
                                this.BuscarCupones().BuscarAfiliafo()
                            }
                        })

                }
            }
        },
        // ========================================================== 
        //  BUSCAR AFILIADO POR CODIGO 
        // ==========================================================
        BuscarCupones: function () {
            return {
                BuscarAfiliafo: () => {
                    this.axios.post('/app/v1_autorizaciones/BusquedaCupones', {
                        Operacion: 'CONSULTAXCODIGO',
                        Afiliado: this.info.Afiliado
                    })
                        .then(resp => {
                            if (resp.data.codigo == '0') {
                                if (resp.data.json == null || resp.data.json == undefined || resp.data.json.length == 0) {
                                    // Mostrar notificación indicando que 'res.data.json' está vacío
                                    this.$vs.notify({
                                        title: 'ADVERTENCIA!',
                                        text: 'No cuenta con cupones',
                                        iconPack: 'feather',
                                        icon: 'icon-alert-circle',
                                        color: 'danger'
                                    })
                                } else {
                                    this.cupones = resp.data.json;
                                }
                            }
                        })
                }
            }
        },

        // ========================================================== 
        //  BUSCAR AFILIADO POR NOMBRES Y APELLIDOS 
        // ==========================================================
        BuscarXnombre: function () {
            return {
                ConsultaPaciente: () => {
                    this.axios.post('/app/v1_autorizaciones/BusquedaCupones', {
                        Operacion: 'C1',
                        Nombre1: this.info.Nombre1,
                        Nombre2: this.info.Nombre2,
                        Apellido1: this.info.Apellido1,
                        Apellido2: this.info.Apellido2
                    })
                        .then(resp => {
                            if (resp.data.codigo == '0') {
                                this.resultados = resp.data.json;
                            }
                        })
                }
            }
        },
        // ========================================================================================================== 
        //  DATOS POPUP 
        // ==========================================================================================================
        handleAffiliateSelection(valor) {
            // Actualizar info.Afiliado y otros datos relevantes según selectedAffiliate
            this.BuscarDatosAfiliadoEncabezadoOpcion2().BuscarDatosAfiliadoOpcion2(valor);
            // Cerrar el popup
            this.popupActivo = false;
        },

        // ========================================================== 
        //  BUSCAR POPUP  CUPONES
        // ==========================================================
        BuscarCuponesOpcion2: function () {
            return {
                BuscarAfiliafoNombre: (valor) => {
                    this.axios.post('/app/v1_autorizaciones/BusquedaCupones', {
                        Operacion: 'CONSULTAXCODIGO',
                        Afiliado: valor.IdAfiliado
                    })
                        .then(resp => {
                            if (resp.data.codigo == '0') {
                                if (resp.data.json == null || resp.data.json == undefined || resp.data.json.length == 0) {
                                    // Mostrar notificación indicando que 'res.data.json' está vacío
                                    this.$vs.notify({
                                        title: 'ADVERTENCIA!',
                                        text: 'No cuenta con cupones',
                                        iconPack: 'feather',
                                        icon: 'icon-alert-circle',
                                        color: 'danger'
                                    })
                                } else {
                                    this.cupones = resp.data.json;
                                  
                                }

                            }
                        })
                }
            }
        },
        // ========================================================== 
        //  BUSCAR POPUP DATOS AFILIADOS
        // ==========================================================
        BuscarDatosAfiliadoEncabezadoOpcion2: function () {
            return {
                BuscarDatosAfiliadoOpcion2: (valor) => {
                    this.axios.post('/app/v1_autorizaciones/BusquedaCupones', {
                        Operacion: 'DATOSAFILIADO',
                        Afiliado: valor.IdAfiliado
                    })
                        .then(resp => {
                            if (resp.data.json) {
                                this.$map({
                                    objeto: this.info,
                                    respuestaAxios: resp,

                                });

                            }
                        })
                    this.BuscarCuponesOpcion2().BuscarAfiliafoNombre(valor)
                    this.LimpiarCamposPopup()
                }
            }
        },


        // ========================================================================================================== 
        //  LIMPIAR CAMPOS  
        // ==========================================================================================================
        // ========================================================== 
        //  LIMPIAR CAMPOS  
        // ==========================================================
        LimpiarCampos() {
            this.info.Afiliado = null
            this.info.Idcliente = null
            this.info.Nombre = null
            this.info.NumeroAdhesion = null
            this.info.Planes = null
            this.cupones = []
            this.LimpiarCamposPopup()
            this.$vs.notify({
                title: 'NOTIFICACION!',
                text: 'Datos Limpiados!',
                iconPack: 'feather',
                icon: 'icon-alert-circle',
                color: 'danger'
            })
        },
        

        validarCampos() {
            if (!this.info.Nombre1 || !this.info.Apellido1) {
                this.$vs.notify({
                color: "danger",
                title: "Campos requeridos",
                text: "Debe completar al menos Nombre 1 y Apellido 1",
                });
                return;
            }
            // Si los campos son válidos, continúa con la función
            this.BuscarXnombre().ConsultaPaciente();
            this.BuscarXnombre().ConsultaPaciente();

            },

        // ========================================================== 
        //  LIMPIAR CAMPOS POPUP 
        // ==========================================================
        LimpiarCamposPopup() {
            this.info.Nombre1 = null
            this.info.Nombre2 = null
            this.info.Apellido1 = null
            this.info.Apelldio2 = null
            this.resultados = []

        },


        CargarPDF() {
            this.$reporte_modal({
                Nombre: 'Lista de Cupones Médicos por Afiliado',
                Opciones: {
                    Afiliado: this.info.Afiliado
                }
               

             })
        },

        async MostrarPDF(valor) {
            try {
                await this.$reporte_modal({
                    Nombre: 'Reimpresion Cupon',
                    Opciones: {
                        Cupon: valor.Cupon,
                    }
                });

                this.CargarPDf_2().Reimpresion(valor)
             
            } catch (error) {

                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'No se generó el reporte.',
                });
            }
        },
        

        CargarPDf_2: function(){
            return {
                Reimpresion: (valor) =>{
                    this.axios.post('/app/v1_autorizaciones/Reimpresion',{
                        Operacion:'CalculoReimpresion',
                        Cupon: valor.Cupon,
                    }).then(()=>{
                        this.BuscarCupones().BuscarAfiliafo()
                    })
                    this.BuscarCupones().BuscarAfiliafo()
                }
                
            }
        }
 
    },
}

</script>

<style>
.OverFlow .contenedor-tabla
{
  overflow-y: auto !important;
  max-height: 300px !important; 
}


.custom-table center {
  font-size: 10px; /* Tamaño para el contenido centrado */
}

.custom-table .vs-button {
  font-size: 10px;
  padding: 5px 10px; /* Ajustar el espacio dentro del botón */
}

.colors thead {
background-color:#2980b9 !important;
color: white !important;
}


</style>
<style scoped>
.Izquierda {
    width: 40%;
    padding: 5px;
    border-radius: 5px;
}

.Derecha {
    width: 100%;
    padding: 5px;
    border-radius: 5px;
}


.notificacion {
    text-align: center;
    font-size: 50px;
    height: 40px;
    color: rgba(var(--vs-warning), 0.7);

    cursor: pointer;
    position: relative;
}

.notificacion:hover {
    color: rgba(var(--vs-warning), 1);
    ;
}

.notificacion .titulo {
    font-weight: bold;
    font-size: 20px;
    position: relative;
    top: -5px;
}

.notificacion2 {
    text-align: center;
    font-size: 70px;
    cursor: pointer;
    position: relative;
    margin: 20px;
}

.notificacion2:hover {
    color: black;
}

.notificacion2 .titulo {
    font-weight: bold;
    font-size: 20px;
    position: relative;
    top: -5px;
}

.buttom {
    height: 132px;

}


</style>


