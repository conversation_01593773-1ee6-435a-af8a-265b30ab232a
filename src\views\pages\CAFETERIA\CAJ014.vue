<template>
<vx-card title="Nivel de Precios Ortopedia">
    <div class="flex flex-wrap">
        <vs-button color="primary" style="float:left;margin: 5px" type="filled" icon-pack="fas" icon="fa-plus" @click.native="Consulta().cargarNuevoNivel()">Nuevo</vs-button>        
        <vs-button color="primary" style="float:left;margin: 5px" type="filled" icon-pack="fas" icon="fa-sync-alt"  @click.native="Consulta().obtenerNivelPrecios()">Actualizar</vs-button>
    </div>
    <vs-divider></vs-divider>
    <div class="flex flex-wrap">
        <div class="w-full  p-2">
            <vs-table2 max-items="10" search pagination :data="listaNivelPrecios" height="470px" class="mt-0 mb-0">
                <template slot="thead">
                    <th order="NivelPrecio" width="50px">Ni<PERSON> de Precios</th>
                    <th order="Descripcion">Descripción</th>
                    <th order="Incremento" width="50px">% de incremento</th>
                    <th order="Estatus" width="100px">Estatus</th>
                    <th order="Accion" width="150px">Acción</th>
                </template>
                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">
                        <vs-td2>
                            {{ tr.NivelPrecio }}                               
                        </vs-td2>
                        <vs-td2>
                            {{tr.Descripcion}}
                        </vs-td2>
                        <vs-td2>
                            {{tr.PorcentajeIncremento}}
                        </vs-td2>
                        <vs-td2>
                            {{tr.Estatus=='S'?'Activo':'Inactivo'}}
                        </vs-td2>
                        <vs-td2>
                            <vs-button style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-check-square" @click="Consulta().actualizarStatus(tr)"></vs-button>
                            <vs-button color="success" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-edit" @click="Consulta().editarNivelPrecio(tr)"></vs-button>
                        </vs-td2>
                    </tr>
                </template>
            </vs-table2>
        </div>
    </div>
    <vs-popup :title="Titulo_emergente" :active.sync="Estado_VentanaEmergente" class="vs-con-loading__container">
        <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
            <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
            <form method="post" @submit.prevent="handleSubmit(Mantenimiento().AgregarNivelPrecio())">
                <div class="flex flex-wrap" style="margin: 15px">
                    <div class="w-full m-2">
                        <ValidationProvider rules="numero_min:1|numero_entero|required" v-slot="{ errors }">
                            <vs-input class="w-full" label-placeholder="Nivel de Precios" v-model="info.NivelPrecio.NivelPrecio" :disabled="TipoOperacion == 'EDITAR' "
                                        :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"/>
                        </ValidationProvider>                        
                    </div>
                    <div class="w-full m-2">
                        <ValidationProvider rules="required|max:50" v-slot="{ errors }">
                            <vs-input class="w-full" label="Descripcion" v-model="info.NivelPrecio.Descripcion" 
                                        :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"/>
                        </ValidationProvider>                        
                    </div>
                    <div class="w-full m-2">
                        <ValidationProvider rules="numero_min:0|numero_entero|required" v-slot="{ errors }">
                            <vs-input class="w-full" label="% de Incremento:" v-model="info.NivelPrecio.PorcentajeIncremento" 
                                        :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"/>
                        </ValidationProvider>
                    </div>
                    <div class="w-full m-2">
                        <label>Estatus</label>
                        <vs-switch color="success" icon-pack="feather" vs-icon-on="icon-check" vs-icon-off="icon-x" v-model="info.NivelPrecio.Estado"  
                                    @click="Mantenimiento().CambioEstatus()">
                        </vs-switch>
                    </div>
                    <div class="w-full m-2">
                        <vs-button v-if="TipoOperacion == 'GRABAR' " type="filled" @click.prevent="Mantenimiento().AgregarNivelPrecio(invalid)" class="mt-5 block">Grabar</vs-button>
                        <vs-button v-if="TipoOperacion == 'EDITAR' " type="filled" @click.prevent="Mantenimiento().ModificarNivelPrecio(invalid)" class="mt-5 block">Editar</vs-button>
                    </div>
                </div>
            </form>
            </ValidationObserver>
        </div>
    </vs-popup>
    <vs-popup :title="TituloEstado" :active.sync="Estado_VentanaEstatus" class="vs-con-loading__container">        
        <div class="flex flex-wrap" style="margin: 15px">
            <div class="w-full m-2">
                <h5>Actualizar el estutus del nivel de orden: {{ info.NivelPrecio.NivelPrecio }}</h5>
            </div>
            <div class="w-full m-2">
                <label>Estatus</label>
                <vs-switch color="success" icon-pack="feather" vs-icon-on="icon-check" vs-icon-off="icon-x" v-model="info.NivelPrecio.Estado"  
                            @click="Mantenimiento().CambioEstatus()">
                </vs-switch>
            </div>
            <div class="w-full m-2">
                <vs-button type="filled" @click.prevent="Mantenimiento().CambiarEstadoNivelPrecio(info.NivelPrecio.Id,info.NivelPrecio.NivelPrecio,info.NivelPrecio.Estatus)" class="mt-5 block">Actualizar</vs-button>
            </div>           
        </div>
        
    </vs-popup>
</vx-card>
</template>

<script>
export default {
    data() {

        return {
            info: {
                desactivar: true,
                NivelPrecio: {
                    Id: 0,
                    Empresa: null,
                    NivelPrecio: null,
                    Descripcion: null,
                    PorcentajeIncremento: null,
                    Estatus: 'S',
                    Estado: true        
                },
                reporte_src: null,
                reporte: false
            },
            listaNivelPrecios: [],
            Titulo_emergente: 'Nuevo Nivel de Precio',
            Estado_VentanaEmergente: false,
            TipoOperacion: 'GRABAR',
            TituloEstado: 'Cambio de Estatus',
            Estado_VentanaEstatus: false
        }
    },
    computed: {
        sesion() {
            return this.$store.state.sesion
        }
    },
    methods: {
        Consulta() {
            return {
                init: () => {
                    this.Consulta().obtenerNivelPrecios()
                },
                obtenerNivelPrecios: ()=>{
                    this.axios.post('/app/v1_JefeCaja/obtener_nivel_precios', {})
                    .then(resp =>{                  
                        if (resp.data.codigo == 0) {
                            this.listaNivelPrecios = resp.data.json                                
                        }else{
                            this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Alerta',
                                    text: 'No se pudieron obtener los niveles de precio',
                                })
                        }
                    })
                },
                cargarNuevoNivel: ()=>{
                    this.TipoOperacion = 'GRABAR'
                    this.Titulo_emergente = 'Nuevo Nivel de Precio'
                    this.Estado_VentanaEmergente = true
                    this.Consulta().limipiarNivelPrecio()
                },
                editarNivelPrecio: (NivelPrecio)=>{
                    this.TipoOperacion = 'EDITAR'
                    this.Titulo_emergente = 'Editar Nivel de Precio'
                    this.Estado_VentanaEmergente = true
                    this.Consulta().limipiarNivelPrecio()
                    this.Consulta().cargarNivelPrecio(NivelPrecio)
                },
                actualizarStatus: (NivelPrecio) =>{                    
                    this.Estado_VentanaEstatus = true
                    this.Consulta().limipiarNivelPrecio()
                    this.Consulta().cargarNivelPrecio(NivelPrecio)
                },
                limipiarNivelPrecio: () =>{
                    this.info.NivelPrecio.Id = 0
                    this.info.NivelPrecio.Empresa = null
                    this.info.NivelPrecio.NivelPrecio = null
                    this.info.NivelPrecio.Descripcion = null
                    this.info.NivelPrecio.PorcentajeIncremento = null
                    this.info.NivelPrecio.Estatus = 'S',
                    this.info.NivelPrecio.Estado = true   
                },
                cargarNivelPrecio: (NivelPrecio) =>{
                    this.info.NivelPrecio.Id = NivelPrecio.Id
                    this.info.NivelPrecio.Empresa = NivelPrecio.Empresa
                    this.info.NivelPrecio.NivelPrecio = NivelPrecio.NivelPrecio
                    this.info.NivelPrecio.Descripcion = NivelPrecio.Descripcion
                    this.info.NivelPrecio.PorcentajeIncremento = NivelPrecio.PorcentajeIncremento
                    this.info.NivelPrecio.Estatus = NivelPrecio.Estatus,
                    this.info.NivelPrecio.Estado = NivelPrecio.Estatus == 'S'
                }                  
            }
        },
        Mantenimiento() {
            return {
                CambioEstatus: () =>{
                    this.info.NivelPrecio.Estatus = !this.info.NivelPrecio.Estado ? 'S' : 'I'
                },
                AgregarNivelPrecio: (invalid) =>{
                    if(invalid){
                        this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Alerta',
                                    text: 'Error al agregar el nivel de precio',
                                    time: 3000
                                })
                        return
                    }
                    this.info.NivelPrecio.Bitacora = null
                    this.info.NivelPrecio.Bitacora = JSON.stringify(this.info.NivelPrecio)

                    this.axios.post('/app/v1_JefeCaja/agregar_nivel_precio', this.info.NivelPrecio)
                    .then(resp =>{         
                                 
                        if (resp.data.codigo == 0) {
                            this.$vs.notify({
                                            title:'Exito',
                                            text:'Se agregó el nivel de precio',
                                            color:'success'
                                        })
                            this.Estado_VentanaEmergente = false
                            this.Consulta().obtenerNivelPrecios()
                        }else{
                            this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Alerta',
                                    text: 'Error al agregar el nivel de precio',
                                })
                        }
                    })
                },
                ModificarNivelPrecio: (invalid) =>{
                    if(invalid){
                        this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Alerta',
                                    text: 'Error al agregar el nivel de precio',
                                    time: 3000
                                })
                        return
                    }
                    this.info.NivelPrecio.Bitacora = null
                    this.info.NivelPrecio.Bitacora = JSON.stringify(this.info.NivelPrecio)

                    this.axios.post('/app/v1_JefeCaja/modificar_nivel_precio', this.info.NivelPrecio)
                    .then(resp =>{         
                                   
                        if (resp.data.codigo == 0) {
                            this.$vs.notify({
                                            title:'Exito',
                                            text:'Se modificó el nivel de precio',
                                            color:'success'
                                        })
                            this.Estado_VentanaEmergente = false
                            this.Consulta().obtenerNivelPrecios()
                        }else{
                            this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Alerta',
                                    text: 'Error al modificar el nivel de precio',
                                })
                        }
                    })
                },
                CambiarEstadoNivelPrecio: (Id,NivelPrecio,Estatus)=>{
                    this.axios.post('/app/v1_JefeCaja/cambiar_estado_nivel_precio', {
                        Id: Id,
                        Estatus: Estatus,
                        NivelPrecio: NivelPrecio    
                    })
                    .then(resp =>{         
                              
                        if (resp.data.codigo == 0) {
                            this.$vs.notify({
                                            title:'Exito',
                                            text:'Se actualizo el Estatus',
                                            color:'success'
                                        })
                            this.Estado_VentanaEstatus = false
                            this.Consulta().obtenerNivelPrecios()
                        }else{
                            this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Alerta',
                                    text: 'No se pudo actulizar el Estatus',
                                })
                        }
                    })
                }
            }
        }
    },
    mounted() {
        this.Consulta().init()
    }
}
</script>

<style scoped>
.cantidad button {
    height: 25px;
    width: 30px;
    border: 1px solid #ccc;
    border-radius: 5px;

}

.cantidad div {
    display: inline-block;
    height: 25px;
    width: 30px;
    text-align: center;
}
</style>
