----------------------------CREATE TABLE---------------------------------
----------------------------ALTER TABLE----------------------------------



----------------------------CREATE PROCEDURE-----------------------------
----------------------------ALTER PROCEDURE------------------------------
USE [PLANESMEDICOS]
GO
/****** Object:  StoredProcedure [dbo].[actualiza_Plan_Nombre_Status]    Script Date: 12/06/2024 11:45 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

ALTER procedure [dbo].[actualiza_Plan_Nombre_Status]
@Nombre1 as varchar(50),
@Apellido1 as varchar(50),
@Apellido2 as varchar(50),
@ApellidoCasada as varchar(50),
@NoPoliza as varchar(27),
@CodigoAsegurado as varchar(50),
@ls_Plan_a_Actualizar varchar(15) =null,
@ls_Status_a_Actualizar varchar(1)=null,
@ls_opcion as varchar(10), --Nombres,status,plan
@iCorporativo as varchar(10)=null
As 
Begin

Declare @Nombre2 as varchar(50),
@ls_EstadoCivil as varchar(2), 
@ls_parentesco as varchar(50),
@ls_genero as varchar(2), 
@Nombre1Temp as varchar(50),
@ls_idExternoDelAfiliado as varchar(50),
@ls_idContrato  as varchar(50),
@ls_idPlan as varchar(20),
@ls_FechaInicioCobertura as date,
@ls_idEmpresa as varchar(3),
@ls_idAfiliado as varchar(20),
@ls_idCliente as varchar(20),
@li_correlativo AS INT,
@Ls_Usuario_Tran as varchar(10),
@li_codigo_paciente as int,
@FechaNacimiento as date,
@ls_Status as varchar(1),
@obs as varchar(5000),
@Result as varchar(1000),
@Error as int,
@idcontrato as varchar(15),
@NombrePlan as varchar(50),
@EdadInicial as decimal(10,2),
@EdadFinal as decimal(10,2),
@Status as varchar(1),
@EdadCliente as decimal(10,2),
@IdCliente as varchar(20)

BEGIN TRANSACTION
--Set @Nombre1='zOILA ELSA REGINA'
--Set @Apellido1=' Revolorio '
--Set @Apellido2=' Estrada  '
--Set @ApellidoCasada='De Martinez'
--Set @NoPoliza='1142561'
--Set @CodigoAsegurado='00000000000121247648'
--SET @ls_Status_a_Actualizar='C'
--SET @ls_plan_a_Actualizar='PM1606-7'
--SET @ls_opcion='Plan'

SET @Result='Cambio registrado exitosamente'
Set @Error=0

Set @ls_idEmpresa='MED'--CONSTANTE
Set @Ls_Usuario_Tran=Substring(SYSTEM_USER,CharIndex('\',SYSTEM_USER)+1,LEN(SYSTEM_USER))


Set @Nombre1=Replace(Ltrim(Rtrim(@Nombre1))+' ',Char(9),' ')
set @Nombre1=REPLACE(REPLACE(REPLACE(@Nombre1,' ','<>'),'><',''),'<>',' ')

Set @Apellido1=Replace(Ltrim(Rtrim(@Apellido1))+' ',Char(9),' ')
set @Apellido1=REPLACE(REPLACE(REPLACE(@Apellido1,' ','<>'),'><',''),'<>',' ')

Set @Apellido2=Replace(Ltrim(Rtrim(@Apellido2))+' ',Char(9),' ')
set @Apellido2=REPLACE(REPLACE(REPLACE(@Apellido2,' ','<>'),'><',''),'<>',' ')
Set @ApellidoCasada=Replace(@ApellidoCasada,'De','')
set @ApellidoCasada=REPLACE(REPLACE(REPLACE(@ApellidoCasada,' ','<>'),'><',''),'<>',' ')

Select @IdCliente=IdCliente,@ls_idAfiliado=IdAfiliado,@ls_idCliente=IdCliente,@ls_parentesco=Parentesco
from Planesmedicos..Afiliadosxcontrato
Where Numeroadhesion=@NoPoliza
And CodigoAsegurado=@CodigoAsegurado

Select @FechaNacimiento=FechaNacimiento,@ls_genero=genero,@ls_EstadoCivil=EstadoCivil
from PlanesMedicos..Clientes
Where IdCliente=@Idcliente

if isnull(@iCorporativo,0)=0 Begin

		select @Ls_Usuario_Tran=CODIGO
			From contadb..usuarios
				Where Nombre_Red=@Ls_Usuario_Tran
End
if isnull(@icorporativo,0)<>0
Begin
SET @ls_Usuario_Tran= @iCorporativo
End
Select @Nombre1Temp=left(@Nombre1, CHARINDEX(' ', @Nombre1))
	    Select  @Nombre1Temp=left(@Nombre1, CHARINDEX(' ', @Nombre1)),
		@Nombre2=substring(@Nombre1, CHARINDEX(' ', @Nombre1)+1, len(@Nombre1)-(CHARINDEX(' ', 
		@Nombre1)-1))

Set @Nombre1=@Nombre1Temp
SET @Nombre1= Rtrim(Upper(@Nombre1))
SET @Nombre2=Upper(Ltrim(RTrim(@Nombre2)))
SET @apellido1= Rtrim(Upper(@apellido1))
SET @Apellido2=Upper(Ltrim(RTrim(@Apellido2)))
SET @ApellidoCasada=Upper(Ltrim(RTrim(@ApellidoCasada)))
     select
        @ls_idExternoDelAfiliado = idExternoDelAfiliado,
        @ls_idContrato = idContrato,
        @ls_idPlan = idPlan,
        @ls_FechaInicioCobertura = FechaInicioCobertura,
		@ls_Status=Status
      from planesmedicos.dbo.AfiliadosXContrato (nolock)
      where idEmpresa = @ls_idEmpresa
        and NumeroAdhesion = @NoPoliza
        and idAfiliado = @ls_idAfiliado
        and idCliente = @ls_idCliente
	If @ls_opcion='Nombres'	
	Begin
			 --------------------------------------------------
			  --BITACORA CORRELATIVO
			  --------------------------------------------------
			  select @li_correlativo = isnull(max(correlativo), 0) + 1
			  from planesmedicos.dbo.AfiliadosXContratoBitacora (nolock)
		  --------------------------------------------------
			  --BITACORA REGISTRO ORIGINAL
		  --------------------------------------------------
			  insert planesmedicos.dbo.AfiliadosXContratoBitacora (
				correlativo, NumeroAdhesion, idExternoDelAfiliado, idContrato, idPlan, idAfiliado, idCliente, 
				nombre1, nombre2, apellido1, apellido2, ApellidoCasada,FechaNacimiento,Genero,EstadoCivil,Parentesco, observacion,
				enviado,
				usuario_tran, fec_transac)
			  select @li_correlativo, axc.NumeroAdhesion, axc.idExternoDelAfiliado, axc.idContrato, axc.idPlan, axc.idAfiliado, axc.idCliente,
				cl.nombre1, cl.nombre2, cl.apellido1, cl.apellido2, cl.ApellidoCasada,@FechaNacimiento,@ls_genero,@ls_EstadoCivil,@ls_parentesco,observacion = 'REGISTRO ORIGINAL', enviado = 0,
				@ls_Usuario_Tran, getdate()  
			  from planesmedicos.dbo.clientes cl (nolock), planesmedicos.dbo.AfiliadosXContrato axc (nolock)
			  where cl.idEmpresa = axc.idEmpresa
				and cl.idCliente = axc.idCliente
				and cl.idEmpresa = @ls_idEmpresa
				and cl.idCliente = @ls_idCliente
				and axc.idAfiliado = @ls_idAfiliado
		  --------------------------------------------------
			  --REGISTRO ACTUALIZADO
			  --------------------------------------------------
			  insert planesmedicos.dbo.AfiliadosXContratoBitacora (
				correlativo, NumeroAdhesion, idExternoDelAfiliado, idContrato, idPlan, idAfiliado, idCliente, 
				nombre1, nombre2, apellido1, apellido2, ApellidoCasada,FechaNacimiento ,Genero,EstadoCivil,Parentesco,
				observacion, enviado, 
				usuario_tran, fec_transac)
			  select @li_correlativo, @NoPoliza, @ls_idExternoDelAfiliado, @ls_idContrato, @ls_idPlan, @ls_idAfiliado, @ls_idCliente,
				@Nombre1, @Nombre2, @apellido1, @apellido2, @ApellidoCasada,@FechaNacimiento,@ls_genero,@ls_EstadoCivil,@ls_parentesco, observacion = 'ACTUALIZADO ROBLE', enviado = 0, 
				@ls_usuario_tran, getdate()
			  from planesmedicos.dbo.clientes cl (nolock), planesmedicos.dbo.AfiliadosXContrato axc (nolock)
			  where cl.idEmpresa = axc.idEmpresa
				and cl.idCliente = axc.idCliente
				and cl.idEmpresa = @ls_idEmpresa
				and cl.idCliente = @ls_idCliente
				and axc.idAfiliado = @ls_idAfiliado
		 --------------------------------------------------
			  --UPDATE PLANESMEDICOS.DBO.CLIENTES
			  --------------------------------------------------
			  update planesmedicos.dbo.clientes
			  set
				nombre1 = @nombre1, 
				nombre2 = @nombre2, 
				apellido1 = @apellido1, 
				apellido2 = @apellido2, 
				ApellidoCasada = @ApellidoCasada 
				where idEmpresa = @ls_idEmpresa
				  and idCliente = @ls_idCliente

			   select @li_codigo_paciente = Isnull(IdPaciente,0) FROM planesmedicos.dbo.Clientes
				WHERE (IdEmpresa = @ls_idEmpresa) AND (IdCliente = @ls_idCliente)
	
		--Actualizar Ficha de Pacientes
		if Isnull(@li_codigo_paciente,0)<>0
		Begin
 			Update Hospital.dbo.Pacientes
    			SET
    				Nombre = @nombre1 +' '+@nombre2,
    				Apellido = @apellido1 +' '+ @apellido2, 
    				ApellidoCasada = @ApellidoCasada 
    		
    			Where Codigo = @li_codigo_paciente--
		End

declare
  @li_correlativo_Obs  as numeric

Select @li_correlativo_Obs = isnull(max(correlativo), 0) + 1
from Planesmedicos.dbo.AfiliadosXContratoBitacoraObservacion
Where IdEmpresa  = @ls_idEmpresa
  and IdAfiliado = @ls_idAfiliado

Insert planesmedicos.dbo.AfiliadosXContratoBitacoraObservacion (idEmpresa, idAfiliado, idPlan, Correlativo, Observacion, usuario_tran, fec_transac,TipoModificacion)
values (@ls_idEmpresa, @ls_idAfiliado, @ls_idPlan, @li_correlativo_Obs, 'Actualización nombre', @ls_usuario_tran, getdate(),5)


 Select @Result+' ('+@ls_opcion+')' as Respuesta
	End --fin actualizacion nombres

If @ls_opcion='status'
if Isnull(@ls_Status_a_Actualizar,'')<>''
Begin
   Begin

   set @obs='Estatus según axcel '+ @ls_Status_a_Actualizar +', Status Sermesa: '+@ls_Status

execute PlanesMedicos..sp_afiliado_actualiza_statusV2
		@ls_idempresa,
		@NoPoliza,
		@ls_idAfiliado,
		@ls_Status_a_Actualizar,
		@obs,
		@ls_usuario_tran,
		@ls_idPlan
	 Select @Result+' ('+@ls_opcion+')' as Respuesta
   End
   End

   If @ls_opcion='Plan'
   Begin
   if isnull(@ls_Plan_a_Actualizar,'')<>''
   Begin
   ---Validar cambio de plan
		   Select @idcontrato=idcontrato,@NombrePlan=Nombre,@EdadInicial=EdadInicial,@EdadFinal=EdadFinal,@Status=status
		from Planesmedicos..Planesxcontrato
		where idplanXcontrato=@ls_Plan_a_Actualizar

		select @FechaNacimiento=FechaNacimiento
		from planesmedicos..clientes where idcliente=@ls_idCliente


		 select @EdadCliente=cast(ROUND((DATEDIFF(dd,  Format(@FechaNacimiento, 'yyyMMdd'), GETDATE()) - DATEDIFF(yy,  Format(@FechaNacimiento, 'yyyMMdd'), GETDATE()) / 4.25) / 365, 2) as float)

			If @EdadCliente>@EdadFinal
					Begin
						SET @Result='La Edad del Afiliado ('+ Cast(@EdadCliente as varchar(4))+') excede el máximo de años para el plan ' +@NombrePlan
						set @Error=1
			End
		--select @RESULT,@Error
-----Cambiar plan si validacion ok
If @Error=0 begin
  set @obs='Plan según axcel '+ @ls_Plan_a_Actualizar +', plan Sermesa: '+@ls_idPlan
  Execute PlanesMedicos.dbo.sp_afiliado_cambio_plan
		@ls_idempresa,
		@NoPoliza,
		@idcontrato,
		@ls_Plan_a_Actualizar,
		@obs,
		@ls_usuario_tran,
		@ls_idplan
End
  Select @Result+' ('+@ls_opcion+')' as Respuesta
	End
   End
If isnull(@ls_opcion,'')='' Begin
  Select 'No selecciono opción '+' ('+@ls_opcion+')' as Respuesta
End
Commit Transaction;

End

go;






--> <INICIA CREACION USUARIOS USRCOEXAFILIADOS>
USE [master]
GO
IF (SELECT SUSER_ID('USRCOEXAFILIADOS')) IS NULL
/*DESA -> */ CREATE LOGIN [USRCOEXAFILIADOS] WITH PASSWORD=N'A@Fi!liaD0S/%', DEFAULT_DATABASE=[PLANESMEDICOS], DEFAULT_LANGUAGE=[us_english], CHECK_EXPIRATION=OFF, CHECK_POLICY=OFF
--/*QA   -> */ CREATE LOGIN [USRCOEXAFILIADOS] WITH PASSWORD=N'$A@Fi!liaD0SCO3X', DEFAULT_DATABASE=[PLANESMEDICOS], DEFAULT_LANGUAGE=[us_english], CHECK_EXPIRATION=OFF, CHECK_POLICY=OFF
--/*PROD -> */ CREATE LOGIN [USRCOEXAFILIADOS] WITH PASSWORD=N'c(0)3X.a@f1l!AD0$_.', DEFAULT_DATABASE=[PLANESMEDICOS], DEFAULT_LANGUAGE=[us_english], CHECK_EXPIRATION=OFF, CHECK_POLICY=OFF



-->> Insertar en la tabla seguridadconexiones (Ejecutado en PROD)
INSERT INTO CONTADB..seguridadconexiones (TagConexion,TipoConexion,Usuario,UserPassword,Servidor,Puerto,BaseDefault,FechaIngreso) VALUES
	 ('USR_COEX_AFILIADOS_DESA','SQLSERVER','USRCOEXAFILIADOS','QUBGaSFsaWFEMFMvJQ=='     ,'***************','1433','PLANESMEDICOS',GETDATE()),
	 ('USR_COEX_AFILIADOS_QA'  ,'SQLSERVER','USRCOEXAFILIADOS','JEFARmkhbGlhRDBTQ08zWA=='  ,'***************','1433','PLANESMEDICOS',GETDATE()),
	 ('USR_COEX_AFILIADOS_PROD','SQLSERVER','USRCOEXAFILIADOS','YygwKTNYLmFAZjFsIUFEMCRfLg==','************','1433'   ,'PLANESMEDICOS',GETDATE());

   USE [CONTADB]
GO
IF (SELECT USER_ID('USRCOEXAFILIADOS')) IS NULL
	CREATE USER USRCOEXAFILIADOS FOR LOGIN USRCOEXAFILIADOS WITH DEFAULT_SCHEMA=[dbo]
GO

GO
--> <FINALIZA CREACION USUARIOS USRCOEXAFILIADOS>



--> <INICIA LA CREACION DE USUARIO USRREPORTEAFILIADOS>
USE [master]	

IF (SELECT SUSER_ID('USRREPORTEAFILIADOS')) IS NULL
/*DESA -> */ CREATE LOGIN [USRREPORTEAFILIADOS] WITH PASSWORD=N'@F!LI1ADOSR3POrT3e', DEFAULT_DATABASE=[PLANESMEDICOS], DEFAULT_LANGUAGE=[us_english], CHECK_EXPIRATION=OFF, CHECK_POLICY=OFF
--/*QA   -> */ CREATE LOGIN [USRREPORTEAFILIADOS] WITH PASSWORD=N'R3p$AFIL1ADOS#21', DEFAULT_DATABASE=[PLANESMEDICOS], DEFAULT_LANGUAGE=[us_english], CHECK_EXPIRATION=OFF, CHECK_POLICY=OFF
--/*PROD -> */ CREATE LOGIN [USRREPORTEAFILIADOS] WITH PASSWORD=N'R3POrTe3s@AF1L1!@d(0)S&##.', DEFAULT_DATABASE=[PLANESMEDICOS], DEFAULT_LANGUAGE=[us_english], CHECK_EXPIRATION=OFF, CHECK_POLICY=OFF
GO

INSERT INTO CONTADB..seguridadconexiones (TagConexion,TipoConexion,Usuario,UserPassword,Servidor,Puerto,BaseDefault,FechaIngreso) VALUES
	 ('USR_REPORTE_AFILIADOS_DESA','SQLSERVER','USRREPORTEAFILIADOS','@F!LI1ADOSR3POrT3e'     ,'***************','1433','PLANESMEDICOS',GETDATE()),
	 ('USR_REPORTE_AFILIADOS_QA'  ,'SQLSERVER','USRREPORTEAFILIADOS','R3p$AFIL1ADOS#21'   ,'***************','1433','PLANESMEDICOS',GETDATE()),
	 ('USR_REPORTE_AFILIADOS_PROD','SQLSERVER','USRREPORTEAFILIADOS','R3POrTe3s@AF1L1!@d(0)S&##.'	,'************','1433'   ,'PLANESMEDICOS',GETDATE());

--> <FINALIZA LA CREACION DE USUARIO USRREPORTEAFILIADOS>


--> <INICIA ASIGNACION DE PERMISOS USRCOEXAFILIADOS>
GRANT EXECUTE ON actualiza_Plan_Nombre_Status TO USRCOEXAFILIADOS
GRANT EXECUTE ON sp_Actualizar_Codigo_Asegurado TO USRCOEXAFILIADOS



--> <FINALIZA ASIGNACION DE PERMISOS USRCOEXAFILIADOS>



--> <INICIA ASIGNACION DE PERMISOS USRREPORTEAFILIADOS>
--> <FINALIZA ASIGNACION DE PERMISOS USRREPORTEAFILIADOS>
