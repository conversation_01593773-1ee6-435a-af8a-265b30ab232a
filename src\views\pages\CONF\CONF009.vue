<template>
<vx-card class="conf-politica-clave-acceso-container">
    <div class="flex flex-row mb-5">
        <font-awesome-icon :icon="['fas', 'fa-cogs']" class="text-6xl" />
        <h3 class="w-full p-5">Configuración de parametros de actualización y recuperación de claves de acceso</h3>
    </div>

    <form @submit="handleSubmit">
        <DxForm :form-data="Configuracion" label-mode="outside" label-location="top" :col-count="2" :show-validation-summary="true">
            <DxFormGroupItem :col-count="3">
                <DxFormSimpleItem data-field="MinutosVencimientoToken" editor-type="dxNumberBox" :validation-rules="[{type: 'required'}, validationRules.minimo]" />
                <DxFormSimpleItem data-field="DiasVencimientoClave" editor-type="dxNumberBox" :validation-rules="[{type: 'required'}, validationRules.minimo]" />
                <DxFormSimpleItem data-field="ActualizaVencimiento" :label="{text:'Debe actualizar clave al expirar'}" editor-type="dxCheckBox" :validation-rules="[validationRules.minimo]" />
            </DxFormGroupItem>
            <DxFormEmptyItem />
            <DxFormSimpleItem data-field="TextoCorreoNuevoUsuario" editor-type="dxHtmlEditor" :editor-options="htmlEditorOptions" :validation-rules="[validationRules.nuevo]" />
            <DxFormSimpleItem data-field="TextoCorreoRecuperacionClave" editor-type="dxHtmlEditor" :editor-options="htmlEditorOptions" :validation-rules="[validationRules.recuperacion]" />
            <DxFormGroupItem :col-span="4" :col-count="2">
                <DxFormButtonItem :button-options="{ text: 'Guardar',  type: 'success',  icon: 'save', useSubmitBehavior: true, }" horizontal-alignment="center" verical-alignment="buttom" :col-span="1" />
                <DxFormButtonItem :button-options="{ text: 'Recargar Información', type: 'default', icon: 'refresh', onClick: CargarParametros}" horizontal-alignment="center" verical-alignment="buttom" :col-span="1" />
            </DxFormGroupItem>
        </DxForm>
    </form>

</vx-card>
</template>

<script>

import 'devextreme-vue/html-editor'
export default {
    name: 'ConfPoliticaClavesAcceso',
    data() {
        return {
            Configuracion: {
                TextoCorreoNuevoUsuario: '',
                TextoCorreoRecuperacionClave: '',
                MinutosVencimientoToken: null,
                DiasVencimientoClave: null,
                ActualizaVencimiento: false,
            },
            validationRules: {
                nuevo: {
                    type: 'custom',
                    reevaluate: true,
                    validationCallback: (e) => {
                        if (!this.Configuracion.TextoCorreoNuevoUsuario.includes('{clave}')) {
                            e.rule.message = 'Agregue por lo menos el texto "{clave}" para compartir la clave de primer inicio de sesión al usuario'
                            return false
                        }
                        e.rule.message = ''
                        return true
                    }
                },
                recuperacion: {
                    type: 'custom',
                    reevaluate: true,
                    validationCallback: (e) => {
                        if (!this.Configuracion.TextoCorreoRecuperacionClave.includes('{token}')) {
                            e.rule.message = 'Agregue por lo menos el texto "{token}" para compartir el token de recuperación al usuario'
                            return false
                        }
                        e.rule.message = ''
                        return true
                    }
                },
                minimo: {
                    type: 'range',
                    min: 1,
                    message: 'Debe ser mayor o igual a 1'
                },

            },
            htmlEditorOptions: {
                height: 500,
                toolbar: {
                    multiline: false,
                    items: [
                        "undo",
                        "redo",
                        'clear',
                        "separator",
                        {
                            name: "header",
                            acceptedValues: [false, 1, 2, 3, 4, 5],
                            options: {
                                inputAttr: {
                                    'aria-label': 'Encabezado'
                                }
                            }
                        },
                        "separator",
                        "bold",
                        "italic",
                        "strike",
                        "underline",
                        "separator",
                        "alignLeft",
                        "alignCenter",
                        "alignRight",
                        "alignJustify",
                        "separator",
                        'background',
                        'color', 'italic', 'link', 'increaseIndent', 'decreaseIndent',
                        'orderedList',
                        'bulletList',
                    ]
                }
            },
            submitButtonOptions: {
                text: 'Guardar',
                type: 'success',
                icon: 'save',
                useSubmitBehavior: true,
                disabled: false
            }
        }
    },
    mounted() {
        this.CargarParametros()
    },
    methods: {
        handleSubmit(e) {
            e.preventDefault()
            this.Grabar()
        },
        LimpiarDatos() {
            this.Configuracion.TextoCorreoNuevoUsuario = ''
            this.Configuracion.TextoCorreoRecuperacionClave = ''
            this.Configuracion.MinutosVencimientoToken = null
            this.Configuracion.DiasVencimientoClave = null
        },
        async CargarParametros() {
            this.axios.post('/app/administracion/PoliticaUsuario', {}).then(resp => {
                this.Configuracion = resp.data
            }).catch(() => this.LimpiarDatos())
        },
        Grabar() {
            this.axios.post('/app/administracion/GrabarPoliticaUsuario', {
                Configuracion: this.Configuracion
            }).then(() => {
                this.CargarParametros()
            })
        }
    }
}
</script>
