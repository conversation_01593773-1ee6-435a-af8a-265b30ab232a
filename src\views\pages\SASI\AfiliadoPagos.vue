<template>
<div class="afiliados-pagos-container">
    <div class="pagos-center-container flex flex-wrap mb-5">
        <DxButtonGroup :items="ButtonGroupItems" key-expr="key" styling-mode="outlined" @item-click="itemClick" :selected-item-keys.sync="selectedButton" />
    </div>
    <div v-if="selectedButton.includes('pagos')">
        <vx-card title="Pagos" :collapseAction="MostrarRecibos">
            <DxDataGrid :data-source="Pagos" v-bind="GridOptionPagos" />
        </vx-card>
        <vx-card title="Recibos" v-if="MostrarRecibos" :collapseAction="MostrarRecibos">
            <DxDataGrid :data-source="Recibos" v-bind="GridOptionRecibos" :visible="MostrarRecibos" />
        </vx-card>
    </div>

    <div v-if="selectedButton.includes('pendientes')">
        <div>
            <p>Pagos pendientes Poliza No. {{ this.NumeroAdhesion }}</p>
            <p v-if="condition" style="color: red">No se encontró información</p>
        </div>
        <DxDataGrid :data-source="dsPendientes" :no-data-text="'No hay información'" v-bind="DefaultDxGridConfiguration" height="500">
            <DxDataGridColumn :width="100" data-field="NoPoliza" :visible="true" caption="No Póliza" />
            <DxDataGridColumn :width="200" data-field="Factura" :visible="true" caption="No. Factura" />
            <DxDataGridColumn :width="100" data-field="MontoConDescuento" :visible="true" caption="Monto" />
            <DxDataGridColumn :width="100" data-field="CodPlanSermesa" :visible="true" caption="Plan" />
            <DxDataGridColumn :width="100" data-field="NoRequerimiento" :visible="true" caption="No. Req." />
            <DxDataGridColumn :width="100" data-field="NoTransaccion" :visible="true" caption="No. Trans." />
        </DxDataGrid>
    </div>

    <div v-if="selectedButton.includes('envidos')">
        <div>
            <p>Pagos enviados Poliza No. {{ this.NumeroAdhesion }}</p>
            <p v-if="condicionEnviados" style="color: red">
                No se encontró información
            </p>
        </div>
        <DxDataGrid id="gdPagosEnviados" :data-source="dsPagosEnviados" :no-data-text="'No hay información'" height="500" v-bind="DefaultDxGridConfiguration">
            <DxDataGridColumn :width="100" data-field="NoPoliza" :visible="true" caption="No Póliza" />
            <DxDataGridColumn :width="200" data-field="IdFactura" :visible="true" caption="No. Factura" />
            <DxDataGridColumn :width="100" data-field="MontoConDescuento" :visible="true" caption="Monto" />
            <DxDataGridColumn :width="100" data-field="DescPlan" :visible="true" caption="Plan" />
            <DxDataGridColumn :width="100" data-field="AnnioCubrePago" :visible="true" caption="Año" />
            <DxDataGridColumn :width="100" data-field="MesCubrePago" :visible="true" caption="Mes" />
            <DxDataGridColumn :width="100" data-field="NoRequerimiento" :visible="true" caption="No. Req." />
            <DxDataGridColumn :width="100" data-field="CodPlanSermesa" :visible="true" caption="Plan cobrado" />
            <DxDataGridColumn :width="100" data-field="FecPagoAcsel" :visible="true" caption="Fecha pago Acsel" />
            <DxDataGridColumn :width="100" data-field="IniVigAcsel" :visible="true" caption="Ini vig. Acsel" />
            <DxDataGridColumn :width="100" data-field="FinVigAcsel" :visible="true" caption="Fin vig. Acsel" />
        </DxDataGrid>
    </div>

    <div v-if="selectedButton.includes('emitidos')">

        <div>
            <p>Requerimientos emitidos Poliza No. {{ this.NumeroAdhesion }}</p>
            <p v-if="condicionRequerimientos" style="color: red">
                No se encontró información
            </p>
        </div>
        <DxDataGrid id="gdReqEmitidos" :data-source="dsRequerimientos" :no-data-text="'No hay información'" height="500"  v-bind="DefaultDxGridConfiguration">
            <DxDataGridColumn :width="100" data-field="NoPoliza" :visible="true" caption="No Póliza" />
            <DxDataGridColumn :width="100" data-field="NoRequerimiento" :visible="true" caption="No. Req" />
            <DxDataGridColumn :width="90" data-field="IndCobrado" :visible="true" caption="Cobrado" />
            <DxDataGridColumn :width="100" data-field="AnioCubre" :visible="true" caption="Año cubre" />
            <DxDataGridColumn :width="100" data-field="MesCubre" :visible="true" caption="Mes cubre" />
            <DxDataGridColumn :width="80" data-field="Status" :visible="true" caption="Status" />
            <DxDataGridColumn :width="100" data-field="FechaReq" :visible="true" caption="Fecha" />
        </DxDataGrid>

    </div>

</div>
</template>

<script>
import {
    DefaulGridOptions
} from './data.js'
import {
    DefaultDxGridConfiguration
} from "../EXPEDIENTE/data"
const Summary = {
    totalItems: [{
            column: 'Valor',
            summaryType: 'sum',
            displayFormat: "{0}",
            valueFormat: {
                type: 'currency',
                precision: 2,
                currency: 'GTQ'
            },
        },
        {
            column: 'AnnioCubrePago',
            summaryType: 'count',
            customizeText: (x) => 'Total de registros: '.concat(x.value)
        },
        {
            column: 'ValorCuota',
            summaryType: 'sum',
            displayFormat: "{0}",
            valueFormat: {
                type: 'currency',
                precision: 2,
                currency: 'GTQ'
            },
        },
    ]
}

export default {
    name: 'AfiliadosPagos',
    components: {
        
    },
    data() {
        return {
            selectedButton: [],

            Pagos: null,
            Recibos: null,

            dsPendientes: null,
            dsPagosEnviados: null,
            dsRequerimientos: null,
            PopupPendientes: false,
            PagosEnviadosPopup: false,
            RequerimientosPopup: false,
            condition: false,
            condicionRequerimientos: false,
            condicionEnviados: false,

            GridOptionPagos: {
                ...DefaulGridOptions,
                height: 500,
                summary: {
                    ...Summary
                },
                columns: [{
                        caption: 'Año',
                        dataField: 'AnnioCubrePago',
                        sortOrder: 'asc',
                        sortIndex: 0,

                    },
                    {
                        caption: 'Mes',
                        dataField: 'MesCubrePago',
                        sortOrder: 'asc',
                        sortIndex: 1,
                    },
                    {
                        caption: 'Fecha Transacción',
                        dataField: 'FechaTransaccion',
                        format: 'dd/MM/yyyy',
                        dataType: 'datetime',
                        sortOrder: 'asc',
                        sortIndex: 3,
                    },
                    {
                        caption: 'No. Factura',
                        dataField: 'IdFactura',
                    },
                    {
                        caption: 'Monto',
                        dataField: 'Valor',
                        dataType: 'number',
                        format: 'Q #.00',
                    },
                    {
                        caption: 'Plan',
                        dataField: 'IdPlan',
                    },
                    {
                        caption: 'Fecha Registro',
                        dataField: 'FechaRegistro',
                        format: 'dd/MM/yyyy',
                        dataType: 'datetime',
                    },
                ]
            },
            GridOptionRecibos: {
                ...DefaulGridOptions,
                height: 500,
                summary: {
                    ...Summary
                },
                columns: [{
                        caption: 'Año',
                        dataField: 'AnnioCubrePago',
                        sortOrder: 'asc',
                        sortIndex: 0,

                    },
                    {
                        caption: 'Mes',
                        dataField: 'MesCubrePago',
                        sortOrder: 'asc',
                        sortIndex: 1,
                    },
                    {
                        caption: 'Fecha Transacción',
                        dataField: 'FechaTransaccion',
                        format: 'dd/MM/yyyy',
                        dataType: 'datetime',
                        sortOrder: 'asc',
                        sortIndex: 3,
                    },
                    {
                        caption: 'No. Transacción',
                        dataField: 'NumeroTransaccion',
                    },
                    {
                        caption: 'Monto',
                        dataField: 'ValorCuota',
                        dataType: 'number',
                        format: 'Q #.00',
                    },
                    {
                        caption: 'Recibo',
                        calculateCellValue: (rowData) => ''.concat(rowData.SerieRecibo, ' - ', rowData.Recibo),
                    },
                    {
                        caption: 'Fecha Traslado a Banco',
                        dataField: 'FechaTraslado',
                        format: 'dd/MM/yyyy',
                        dataType: 'datetime',
                    },
                ]
            },

            DefaultDxGridConfiguration,

            ButtonGroupItems: [{
                    key: 'pagos',
                    icon: 'money',
                    call: this.Cargar,
                    hint: 'Pagos',
                    text: 'Pagos',
                },
                {
                    key: 'pendientes',
                    icon: 'clock',
                    call: this.PagosPendientes,
                    hint: 'Pendientes Envío',
                    text: 'Pendientes Envío',
                },
                {
                    key: 'emitidos',
                    icon: 'upload',
                    call: this.Requerimientos,
                    hint: 'Requerimientos Emitidos',
                    text: 'Requerimientos Emitidos',
                },
                {
                    key: 'envidos',
                    icon: 'movetofolder',
                    call: this.PagosEnviados,
                    hint: 'Pagos Enviados',
                    text: 'Pagos Enviados',
                },
            ]

        }
    },
    props: {
        NumeroAdhesion: String,
        OrigenConsulta: {
            type: String,
            default: 'sermesa'
        }
    },
    methods: {

        Cargar() {
            if (this.NumeroAdhesion)
                this.axios.post('/app/v1_afiliados/GetPagos', {
                    NumeroAdhesion: this.NumeroAdhesion
                })
                .then((resp) => {
                    this.Pagos = resp.data.Pagos
                    this.Recibos = resp.data.Recibos
                })
        },

        itemClick(e) {
            e.itemData.call()
        },
        PagosPendientes() {
            this.dsPendientes = null
            if (this.NumeroAdhesion) {
                this.axios
                    .post("/app/v1_afiliados/PagosPendientes", {
                        NumeroAdhesion: this.NumeroAdhesion,
                    })
                    .then((resp) => {
                        if (resp.data.json[0].Respuesta === "0") {
                            this.condition = true
                            return
                        }
                        this.dsPendientes = resp.data.json
                    })
            }
        },
        //Pagos enviados por Roble
        PagosEnviados() {
            this.dsPagosEnviados = []
            if (this.NumeroAdhesion) {
                this.axios
                    .post("/app/v1_afiliados/PagosEnviados", {
                        NumeroAdhesion: this.NumeroAdhesion,
                    })
                    .then((resp) => {
                        if (resp.data.json[0].Respuesta === "0") {
                            this.condicionEnviados = true
                            return
                        }
                        this.dsPagosEnviados = resp.data.json
                    })
            }
        },

        //Requerimientos de cobro
        Requerimientos() {
            this.dsRequerimientos = []
            if (this.NumeroAdhesion) {
                this.axios
                    .post("/app/v1_Afiliados/RequerimientosEmitidos", {
                        NumeroAdhesion: this.NumeroAdhesion,
                    })
                    .then((resp) => {
                        if (resp.data.json[0].Respuesta === "0") {
                            this.condicionRequerimientos = true
                            return
                        }
                        this.dsRequerimientos = resp.data.json
                    })
            }
        },

    },
    mounted() {
        this.Cargar()
    },
    watch: {
        'NumeroAdhesion'() {
            this.Cargar()
        }
    },
    computed: {
        MostrarRecibos() {
            return this.NumeroAdhesion && this.NumeroAdhesion.substring(0, 4).toUpperCase() == "SSGH" || this.NumeroAdhesion.substring(0, 3).toUpperCase() == "SIH"
        }
    },

}
</script>

<style>
.afiliados-pagos-container .vx-card {
    margin-bottom: 5px;
}

.pagos-center-container {
    align-items: center;
    justify-content: center;
}
</style>
