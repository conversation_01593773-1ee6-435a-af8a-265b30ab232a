<template>
    <div>
        <vx-card title="Consulta Envios en Consignación Ortopedia">
            <buscador ref="BuscarBodegaConsignacionOrtopedia" buscador_titulo="Buscador / Bodegas" :api="'app/v1_OrdenCompra/ConsultaBodegaConsignacionOrtopedia'" 
            :campos="['CodigoBodega', 'NombreBodega']"
            :titulos="['Codigo Bodega', '#Nombre Bodega']"
            :api_filtro="{CodigoAgrupacion:PermisosAgrupacionBodega,CodigoBodega:CodigoBodega}"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" :api_preload="true" />

            <vs-popup classContent="popup-example" title="" :active.sync="IsEnvioPdf">
                <vs-row vs-align="center" vs-justify="center">
                    <vs-col vs-type="flex" vs-align="center" vs-w="1">
                        <label class="typo__label">Para:&nbsp;</label>
                    </vs-col>
                    <vs-col vs-type="flex" vs-align="center" vs-w="6">
                        <vs-input v-model="ParaNombre" class="w-full" />
                    </vs-col>
                </vs-row>
                <br>
                <vs-row vs-align="center" vs-justify="center">
                    <vs-col vs-type="flex" vs-align="center" vs-w="1">
                        <label class="typo__label">De:&nbsp;</label>
                    </vs-col>
                    <vs-col vs-type="flex" vs-align="center" vs-w="6">
                        <vs-input v-model="DeNombre" class="w-full"/>
                    </vs-col>
                </vs-row>
                <br>
                <vs-row vs-align="center">
                    <vs-col vs-type="flex" vs-align="center" vs-justify="center" vs-w="12">
                        <vs-button color="success" icon-pack="fa" icon="fa-check" @click="GeneraEnvioOrtopediaPdf()">Generar</vs-button>
                    </vs-col>
                </vs-row>
            </vs-popup>
            <vs-row vs-type="flex" vs-w="12">
                <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="4">
                    <label class="typo__label">Bodega:&nbsp;</label>
                    <vs-input v-model="CodigoBodega" @keyup.enter="BodegasConsignacionOrtopedia()" @keydown.tab="BodegasConsignacionOrtopedia()" />
                    <vs-button color="primary" icon-pack="fa" @click="BodegasConsignacionOrtopedia()" icon="fa-search"></vs-button>
                    <h5>&nbsp;{{ NombreBodega }}</h5>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-lg="3" vs-sm="6" vs-xs="12">
                    <div class="w-full flex flex-wrap container">
                        <div class="w-full">
                            <label class="typo__label">Periodo</label>
                            <vs-divider></vs-divider>
                        </div>
                        <div class="flex flex-wrap div-container div-periodo">
                            <!-- <vs-row vs-type="flex" vs-align="center" vs-lg="2" vs-sm="4" vs-xs="12">
                                <vs-col vs-type="flex" vs-align="center" vs-lg="2" vs-sm="4" vs-xs="12"> -->
                                    <div class="w-1/2">
                                        <label class="w-1/12 typo__label">Del:&nbsp;</label>
                                        <vs-input class="w-fit w-11/12" type="date" v-model="FechaDel" @change="onChangeFechaDel"></vs-input>
                                    </div>
                                    
                                <!-- </vs-col>
                                <vs-col vs-type="flex" vs-align="center" vs-lg="2" vs-sm="4" vs-xs="12"> -->
                                    <div class="w-1/2">
                                        <label class="w-1/12 typo__label">&nbsp;Al:&nbsp;</label>
                                        <vs-input class="w-fit w-11/12" type="date" v-model="FechaAl"></vs-input>
                                    </div>
                                <!-- </vs-col>
                            </vs-row> -->
                        </div>
                    </div>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="1"></vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="3">
                    <vs-button color="primary" icon-pack="fa" @click="ConsultaEnviosOrtopedia()" icon="fa-search">Consultar</vs-button>&nbsp;
                    <vs-button color="success" icon-pack="fa" @click="GeneraEnvioOrtopediaExcel()" icon="fa-file-excel">Excel</vs-button>&nbsp;
                    <vs-button color="danger" icon-pack="fa" @click="OrtopediaPdf()" icon="fa-file-pdf">PDF</vs-button>
                </vs-col>
            </vs-row>
            <div class="card">
                <vs-table2 max-items="10" tooltip pagination :data="ListaEnvioConsignacionOrtopedia">
                    <template slot="thead">
                        <th>Número</th>
                        <th>Correlativo </th>
                        <th>Envío Proveedor</th>
                        <th>Fecha de Envío de Proveedor</th>
                        <th>Validación</th>
                        <th>Nombre Proveedor</th>
                        <th>Nit</th>
                        <th>Recibido Por</th>
                        <th>Sucursal</th>
                        <th>Monto</th>
                        <th>Bodega</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2>
                                {{tr.Numero}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.Correlativo}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.EnvioProveedor}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.FechaEnvio}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.Validacion}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.NombreProveedor}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.Nit}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.RecibidoPor}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.RecibeEmpresa}}
                            </vs-td2>
                            <vs-td2>
                                {{$formato_moneda(tr.Monto)}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.Bodega}}
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
        </vx-card>
    </div>
</template>
<script>
    export default {
        data(){
            return {
                CodigoBodega: '',
                NombreBodega: '',
                TipoBodega: '',
                PermisosAgrupacionBodega: '',
                FechaDel: '',
                FechaAl: '',
                MesGenracion: '',
                ListaEnvioConsignacionOrtopedia: [],
                IsEnvioPdf: false,
                ParaNombre: '',
                DeNombre: ''
            }
        },
        methods: {
            ObtenerFechas(){
                this.FechaDel = new Date().toISOString().slice(0, 10)
                this.FechaAl = new Date().toISOString().slice(0, 10)

                this.NombreMes(this.FechaDel.substring(5,7))
            },
            onChangeFechaDel(){
                this.NombreMes(this.FechaDel.substring(5,7))
                
            },
            BodegasConsignacionOrtopedia(){
                if(this.CodigoBodega != ''){
                    this.NombreBodega = ''
                    this.TipoBodega = ''
                    this.axios.post('/app/v1_OrdenCompra/ConsultaBodegaConsignacionOrtopedia', {
                        CodigoBodega: this.CodigoBodega,
                        CodigoAgrupacion: this.PermisosAgrupacionBodega
                    })
                    .then(resp => {
                        if(resp.data.codigo == 0){
                            if(resp.data.json.length == 1){
                                this.NombreBodega = resp.data.json[0].NombreBodega
                            }else{
                                this.$refs.BuscarBodegaConsignacionOrtopedia.iniciar((data) => {
                                    if(data != null){
                                        this.CodigoBodega = data.CodigoBodega
                                        this.NombreBodega = data.NombreBodega
                                        this.TipoBodega = data.TipoBodega
                                    }
                                })
                            }
                        }
                    })
                }else{
                    this.$refs.BuscarBodegaConsignacionOrtopedia.iniciar((data) => {
                        if(data != null){
                            this.CodigoBodega = data.CodigoBodega
                            this.NombreBodega = data.NombreBodega
                            this.TipoBodega = data.TipoBodega
                        }
                    })
                }
            },
            ConsultaEnviosOrtopedia(){
                if(this.CodigoBodega == ''){
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Envios',
                        text: 'Debe de seleccionar una bodega',
                    })
                    return
                }
                this.axios.post('/app/v1_OrdenCompra/ConsultaEnviosOrtopedia', {
                    FechaInicio: this.FechaDel,
                    FechaFin: this.FechaAl,
                    CodigoBodega: this.CodigoBodega,
                    TipoEnvio: 'O'
                })
                .then(resp => {
                    if(resp.data.codigo == '0'){
                        this.ListaEnvioConsignacionOrtopedia = resp.data.json
                    }
                })
            },
            OrtopediaPdf(){
                if(this.ListaEnvioConsignacionOrtopedia.length == 0){
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Envios',
                        text: 'No hay información a generar',
                    })
                    return
                }
                this.ParaNombre = ''
                this.DeNombre = ''
                this.IsEnvioPdf =  true
            },
            GeneraEnvioOrtopediaPdf(){
                if(this.ParaNombre == ''){
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Envios',
                        text: 'Debe de llenar el campo PARA con el nombre la persona',
                    })
                    return
                }
                if(this.DeNombre == ''){
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Envios',
                        text: 'Debe de llenar el campo DE con el nombre la persona',
                    })
                    return
                }

                this.$reporte_modal({
                    Nombre: "Envio Consignacion Ortopedia",
                    Opciones: {
                        CodigoBodega: this.CodigoBodega,
                        Mes: this.MesGenracion,
                        ParaNombre: this.ParaNombre,
                        DeNombre: this.DeNombre,
                        FechaInicial: this.FechaDel,
                        FechaFinal: this.FechaAl,
                        Tipo: 'O'
                    },
                    Formato: "PDF"
                })

                this.IsEnvioPdf =  false
            },
            GeneraEnvioOrtopediaExcel(){
                if(this.ListaEnvioConsignacionOrtopedia.length == 0){
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Envios',
                        text: 'No hay información a generar',
                    })
                    return
                }
                this.ParaNombre = ''
                this.DeNombre = ''
                this.$reporte_modal({
                    Nombre: "Envio Consignacion Ortopedia",
                    Opciones: {
                        CodigoBodega: this.CodigoBodega,
                        Mes: this.MesGenracion,
                        ParaNombre: this.ParaNombre,
                        DeNombre: this.DeNombre,
                        FechaInicial: this.FechaDel,
                        FechaFinal: this.FechaAl,
                        Tipo: 'O'
                    },
                    Formato: "EXCEL"
                })
            },
            NombreMes(val){
                this.MesGenracion = val == '01' ? 'Enero'
                                        :   val == '02' ? 'Febrero'
                                        :   val == '03' ? 'Marzo'
                                        :   val == '04' ? 'Abril'
                                        :   val == '05' ? 'Mayo'
                                        :   val == '06' ? 'Junio'
                                        :   val == '07' ? 'Julio'
                                        :   val == '08' ? 'Agosto'
                                        :   val == '09' ? 'Septiembre'
                                        :   val == '10' ? 'Octubre'
                                        :   val == '11' ? 'Noviembre'
                                        : 'Diciembre'

            }
        },
        mounted(){
            for(let privilegio of this.$store.state.privilegios){
                if(privilegio.Privilegio.includes("AGRUPACION_BODEGA")){
                    if(this.PermisosAgrupacionBodega != ''){
                        this.PermisosAgrupacionBodega += `,${privilegio.Privilegio.split('_')[2]}`
                    }else{
                        this.PermisosAgrupacionBodega = privilegio.Privilegio.split('_')[2]
                    }
                }
            }
            this.ObtenerFechas()
        }
    }
</script>
<style lang="scss" scoped>
    .container{
        border: 1px solid #888;

    }
    .div-container{
        padding: 5px;
        margin: 10px;
    }
    .div-periodo{
        padding-bottom: 38px;
        padding-top: 18px;
    }
</style>