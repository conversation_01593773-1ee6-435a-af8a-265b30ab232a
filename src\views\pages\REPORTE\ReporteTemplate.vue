<template>
    <div class="container">
        <div class="a-r25qN-0">

            <RadioBoxGroup class="a-r25qN-0 flex flex-col gap-2" :options="ListaReportes" v-model="optionChecked"
                :groupName="'listaReportes'">
            </RadioBoxGroup>
        </div>

        <div class="a-x65zy-1">

            <div v-show="ocultar">

                <ValidationObserver ref="formValidate" v-slot="{ invalid, validate }" mode="lazy" :key="generateID()">
                    <form @submit.prevent="validate().then(submitForm(reporte.opciones))">
                        <div class="flex flex-wrap   ">
                            <template v-for="(opcion, index) in reporte.opciones">
                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2  p-1 " v-if="opcion.Tipo != 'Oculto'"
                                    :key="index">

                                    <div v-if="opcion.ValidarPermiso && opcion.TienePermiso">
                                        {{ opcion.Etiqueta }}
                                    </div>

                                    <div v-else-if="opcion.ValidarPermiso != true">
                                        {{ opcion.Etiqueta }}
                                    </div>

                                    <ValidationProvider :name="index.toString()" :rules="getValidatorRules(opcion)"
                                        class="required">


                                        <vs-input
                                          v-if="!opcion.Buscador      && opcion.Tipo == 'Fecha'"
                                          :name="'Campo_' + index"
                                          class="w-full"
                                          type="date"
                                          v-model="opcion.value"
                                          :required="opcion.Obligatorio == 1"
                                          @blur="(e) => validateDate(e, opcion)" />

                                        <vs-input
                                          v-else-if="!opcion.Buscador && opcion.Tipo == 'Fecha/Hora'"
                                          :name="'Campo_' + index"
                                          class="w-full"
                                          type="date"
                                          v-model="opcion.value"
                                          :required="opcion.Obligatorio == 1"
                                          @blur="(e) => validateDate(e, opcion)" 
                                        />

                                        <vs-input
                                          v-else-if="!opcion.Buscador && opcion.Tipo == 'Fecha/Hora-ISO'"
                                          :name="'Campo_' + index"
                                          class="w-full"
                                          type="datetime-local"
                                          v-model="opcion.value"
                                          :required="opcion.Obligatorio == 1"
                                          @blur="(e) => validateDate(e, opcion)" />


                                        <vs-input
                                          v-if="!opcion.Buscador && opcion.Tipo == 'Texto'"
                                          :name="'Campo_' + index"
                                          class="w-full"
                                          v-model="opcion.value"
                                          v-on:change="Otros().actualizarParametrosBusqueda(opcion)"
                                          :required="opcion.Obligatorio == 1" 
                                        />

                                        <vs-input
                                          v-else-if="!opcion.Buscador && opcion.Tipo == 'Numero'"
                                          :name="'Campo_' + index"
                                          class="w-full"
                                          type="number"
                                          v-model="opcion.value"
                                          v-on:change="Otros().actualizarParametrosBusqueda(opcion)"
                                          :required="opcion.Obligatorio == 1" 
                                        />

                                        <vs-checkbox
                                          v-else-if="!opcion.Buscador && opcion.Tipo == 'Checkbox' && opcion.ValidarPermiso && opcion.TienePermiso"
                                          v-model="opcion.value"
                                          :required="opcion.Obligatorio == 1">
                                        </vs-checkbox>

                                        <vs-checkbox
                                          v-else-if="!opcion.Buscador && opcion.Tipo == 'Checkbox' && opcion.ValidarPermiso != true"
                                          v-model="opcion.value"
                                          :required="opcion.Obligatorio == 1">
                                        </vs-checkbox>

                                        <SM-Buscar :ref="`buscador_${index}`" v-else-if="opcion.Buscador"
                                            v-model="opcion.value" label="" v-bind="opcion.Buscador[0].params"
                                            :callback_buscar="e => Otros().buscador(e, opcion)" >
                                        </SM-Buscar>


                                    </ValidationProvider>
                                    <span class="text-danger text-sm" v-show="opcion.Obligatorio == 1">Campo
                                        obligatorio</span>
                                </div>
                            </template>
                        </div>

                        <div class="flex flex-row p-4">

                            <vs-button
                              v-if="generarPDF"
                              @click="validate().then(submitForm(reporte.opciones, 'PDF'))"
                              color="danger"
                              type="border"
                              style="margin-right:5px"
                              :disabled="invalid">
                              <i class="fas fa-file-pdf "></i>
                              Generar
                            </vs-button>

                            <vs-button
                              v-if="generarExcel"
                              @click="validate().then(submitForm(reporte.opciones, 'EXCEL'))"
                              color="success"
                              type="border"
                              :disabled="invalid">
                              <i class="far fa-file-excel"></i>
                              Generar
                            </vs-button>


                            <vs-button
                              v-if="limpiarPantalla"
                              @click="clearFields"
                              color="primary"
                              type="border"
                              class="ml-2"
                              style="margin-right: 5px">
                              <i class="fas fa-eraser"></i> 
                              Limpiar
                            </vs-button>

                        </div>
                    </form>

                </ValidationObserver>
            </div>

        </div>

    </div>
</template>
  
  <script>
  import REPORT001 from "./REPORT001.vue";
  import RadioBoxGroup from "@/components/sermesa/global/radioButtons/RadioBoxGroup";
  
  export default {
    name: "ReporteTemplate",
    extends: REPORT001,
    props: {
      ListaReportes: {
        type: Array,
        required: true,
      },
      formato: {
        type: String,
        default: 'PDF',
      },
      limpiarPantalla: { 
        type: Boolean, 
        default: false 
      },
    },
    data() {
      return {
        ocultar: false,
        optionChecked: null,
        data_reporte: [],
      };
    },
    components: {
      RadioBoxGroup,
    },
    mounted(){
        
    },
    watch: {
        async optionChecked(checked) {     

            this.ocultar = false; // Reinicia visibilidad
            this.reporte.opciones = []; // Limpia parámetros anteriores
            this.$refs.formValidate?.reset(); // Reinicia validaciones
            //this.ocultar = true
            this.reporte.buscar = checked.nombreRpt      
             

            const reporteNivel = this.listadoFiltro[0]._level[0]._level[0]            
            this.data_reporte = await this.$recupera_parametros_reporte(this.reporte.buscar)
            
            /* NO SE USAN POR EL MOMENTO
            const nivel1 = this.data_reporte[0];
            const nivel2 = nivel1._level[0];
            const reporteLevel = nivel2._level[0];
            */

            // Ahora solo extraes lo que te interesa:
            /* NO SE USAN POR EL MOMENTO
            const nombre   = reporteLevel.Nombre;    // "ReporteCatalogoCuentas"
            const urlApi   = reporteLevel.UrlApi;    // "http://192.168.232.69:5424/ReporteContabilidad"
            const params   = reporteLevel._level;    // [ { linea: "1", Etiqueta: ... }, … ]
            */
           
            this.generar(reporteNivel.Nombre, reporteNivel.UrlApi, reporteNivel._level)
            
            this.ocultar = true;

        }     
    },
    methods: {
      generateID() {
        return ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, c => (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16));
      },
      async submitForm(opciones, formato = 'PDF') {
        
        //NO SE USAN POR EL MOMENTO
        //const report_data =this.convertArrayToStructure(this.reporte.opciones, this.reporte.titulo)

        // Se Extiende el contexto actual (this) y se sobreescribe el prop temporalmente
        //-----------------------------------------------------------------------------
        //NO SE USAN POR EL MOMENTO
        /*
        const contexto = {
          ...this,
          validateFomr: false
        }
        */

        await REPORT001.methods.submitForm.call(this, opciones, formato, false)
            
        //El componente que reutiliza la abstacción de reportes
        //Implementa el metodo de generacion de reporte        
      },
  
      convertArrayToStructure(array, nombre) {
        return array.reduce((result, item) => {
          const key = item.Parametro;
          let value = item.value;
          if (typeof value === "string") value = value.trim();
          if (item.Parametro === "nombrereporte") {
            result[key] = nombre;
          } else if (value) {
            result[key] = value !== undefined && value !== null ? value : "";
          }          

          return result;
        }, {});
      },
      actualizarBusqueda(opcion) {
        this.$emit("actualizar-busqueda", opcion);
      },
      buscarElemento(evento, opcion) {
        this.$emit("buscar-elemento", evento, opcion);
      },
      clearFields() {
          
          this.reporte.opciones.forEach((opcion, index) => {

            
            // 1) Reset del valor según el Tipo de campo
            switch (opcion.Tipo) {
              case 'Fecha':
              case 'Fecha/Hora':
              case 'Fecha/Hora-ISO':
                // para fechas y datetime, suele convenir null para vaciar el picker
                opcion.value = opcion.ValorDefecto != null
                  ? opcion.ValorDefecto
                  : null;
                break;

              case 'Texto':
                // texto vacío o valor por defecto
                opcion.value = opcion.ValorDefecto != null
                  ? opcion.ValorDefecto
                  : '';
                break;

              case 'Numero':
                // null o el número por defecto
                opcion.value = opcion.ValorDefecto != null
                  ? Number(opcion.ValorDefecto)
                  : null;
                break;

              case 'Checkbox':           
                // checkbox siempre booleano
                //opcion.value = false;
                this.$set(opcion, 'value', false);
                break;

              default:
                // caso genérico
                opcion.value = opcion.ValorDefecto != null
                  ? opcion.ValorDefecto
                  : '0';
            }

            // 2) Si es un buscador (SM-Buscar), además invocamos su método interno de limpieza
            if (opcion.Buscador && Array.isArray(this.$refs[`buscador_${index}`])) {
              // en Vue2, cuando usas v-for + ref como string, $refs[refName] es array
              this.$refs[`buscador_${index}`].forEach(buscadorComp => {
                if (buscadorComp && typeof buscadorComp.clear === 'function') {
                  buscadorComp.clear();
                }
              });
            }            
          });

          // 3) (Opcional) Resetear también el estado de validación del ValidationObserver
          if (this.$refs.formValidate && typeof this.$refs.formValidate.reset === 'function') {
            this.$refs.formValidate.reset();
          }
      }
    },
  };
  </script>
  
  <style scoped>
  .container {
    display: grid;
    grid-template-areas:
      "a-r25qN-0 a-x65zy-1"
      "a-r25qN-0 a-r25qN-1";
    grid-template-columns: minmax(200px, auto) 1fr;
    grid-template-rows: 1fr;
    gap: 5px;
    width: 100%;
    height: 100%;
  }
  
  .container > div {
    border: 1px solid #888;
    display: flex;
    flex-direction: column;
    height: 100%;
    box-sizing: border-box;
  }
  
  .a-r25qN-0 {
    grid-area: a-r25qN-0;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  
  .a-x65zy-1 {
    grid-area: a-x65zy-1;
  }
  
  .a-r25qN-1 {
    grid-area: a-r25qN-1;
  }

  .container {
    max-width: 100%;
}
  </style>