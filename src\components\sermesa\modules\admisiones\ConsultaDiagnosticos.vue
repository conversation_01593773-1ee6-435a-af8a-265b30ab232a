<template>
    <div id="app">

        <div class="diagnosticos">

            <div id="outer-grid">


                <div id="inner-grid">

                    <div>
                        <vs-col vs-w="3" class="pr-1">
                        <SM-Buscar
                             v-model="selListaDiagnosticos.selCodigoDiagnostico"
                             label="Diagnostico"
                             api="app/expediente/Busqueda_diagnosticos"
                             api_campo_respuesta_mostrar="Aseguradora"
                             :api_campos="['Diagnostico', 'codigoDiagnostico']"
                             api_campo_respuesta="codigoDiagnostico"
                             :api_titulos="['Diagnostico', 'codigoDiagnostico']"
                             :api_preload="true"
                             :api_campo_respuesta_estricto="false"
                             :disabled_texto="true"
                             :callback_buscar="CosultarDiagnostico().Consultar" />
                        </vs-col>
                        <vs-col vs-w="8">

                            <vs-input label="Nombre Diagnostico" class="w-full"
                                v-model="selListaDiagnosticos.selNombreDiagnostico" readonly />

                        </vs-col>
                        <vs-col vs-w="1">
                            <div class="w-5/6 pt-6">

                                <vs-button @click="addNewRow">
                                    +
                                </vs-button>

                            </div>

                        </vs-col>
                    </div>


                    <div class="pl-5">


                        <div style="overflow:scroll;height:180px;width:100%;overflow:auto">

                            <vs-table2 :data="ListaDiagnosticos">

                                <template slot="thead">

                                    <th order="Codigo Diagnostico" width="5px">Codigo Diagnostico</th>
                                    <th order="Diagnostico" width="300px"> Diagnostico</th>
                                    <th order="delete" width="10px"> </th>

                                </template>
                                <template slot-scope="{data}">

                                    <tr v-for="( ListaDiagnosticos, k) in data" :key="k">

                                        <vs-td2>
                                            <vs-input style="width: 100%; text-align: center; vertical-align: middle;"
                                                readonly v-model="ListaDiagnosticos.CodigoDiagnostico" />
                                        </vs-td2>
                                        <vs-td2>

                                            <vs-input style="width: 100%; text-align: center; vertical-align: middle;"
                                                readonly v-model="ListaDiagnosticos.NombreDiagnostico" />

                                        </vs-td2>
                                        <vs-td2>

                                            <i class="far fa-trash-alt" style="width: 10%" @click="deleteRow(k, '')"></i>

                                        </vs-td2>
                                    </tr>
                                </template>
                            </vs-table2>
                        </div>


                    </div>


                </div>


            </div>

        </div>

    </div>
</template>


<script>
export default {
    name: "ConsultaDiagnosticos",
    data() {
        return {
            CodigoDiagnostico: '',
            NombreDiagnostico: '',
            ListaDiagnosticos: [{
                CodigoDiagnostico: '',
                NombreDiagnostico: ''
            }],
            selListaDiagnosticos: {
                selCodigoDiagnostico: '',
                selNombreDiagnostico: ''
            },
        }
    },
    watch:{
        ListaDiagnosticos:{
            handler(val){
                this.$emit('lista-diagnosticos',val )
            },
            deep:true

        }
        
    },
    mounted: function () {
        this.ListaDiagnosticos = []
    },
    methods: {
        CosultarDiagnostico() {
            return {
                Consultar: (datos) => {
                    this.selListaDiagnosticos.selNombreDiagnostico = datos.Diagnostico
                }
            }
        },
        addNewRow() {
            if (this.selListaDiagnosticos.selCodigoDiagnostico === null) {

                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Alerta',
                    text: 'Debe elegir un diagnostico',
                })
                return
            }


            let diagnosticoElegido = false
            Object.keys(this.ListaDiagnosticos).forEach(key => {

                if (this.ListaDiagnosticos[key].CodigoDiagnostico === this.selListaDiagnosticos.selCodigoDiagnostico) {
                    diagnosticoElegido = true
                }
            });

            if (diagnosticoElegido) {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Elegir otro diagnostico',
                    text: 'Diagnostico ya elegido',
                })
                return
            }
            this.ListaDiagnosticos.push(
                {
                    CodigoDiagnostico: this.selListaDiagnosticos.selCodigoDiagnostico,
                    NombreDiagnostico: this.selListaDiagnosticos.selNombreDiagnostico
                });      

            this.selListaDiagnosticos.selCodigoDiagnostico  =''
            this.selListaDiagnosticos.selNombreDiagnostico=''

        },
        deleteRow(index) {
            this.ListaDiagnosticos.splice(index, 1)
        }
    }
}




</script>



<style scoped>
.diagnosticos {
    display: flex;
    flex-flow: row wrap;
    justify-content: space-around;
    padding-top: 0.5rem;
    padding-bottom: 0rem;
    padding-left: 1rem;
    padding-right: 1rem;
    border: 1px solid #888;
    border-radius: 5px;
    margin: 5px;
}



#outer-grid {
    display: grid;
    grid-gap: 5px;
    grid-auto-flow: column;
    width: 100%;
    height: 100%;
    grid-template-columns: 100%;
    grid-template-rows: 100%;

}

#outer-grid>div {
    /* border: 1px solid #888; */
    border-radius: 5px;
    /* padding: 8px; */
}

#inner-grid {
    display: grid;
    grid-template-columns: 35% 65%;
    grid-gap: 5px;
}

#inner-grid>div {
    /* border: 1px solid #888; */
    border-radius: 5px;
    padding: 4px;
    align-items: start;
}

.smtabla .header {
    padding: 0px !important;
    display: flex !important;
    justify-content: center !important;
}
</style>