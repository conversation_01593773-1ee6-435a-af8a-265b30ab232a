<template>
    <div class="flex flex-wrap">
        <buscador ref="BuscarProducto" buscador_titulo="Buscador / Producto" :api="'app/v1_OrdenCompra/ConsultaTodosProductos'" 
            :campos="['Codigo', 'Descripcion', 'PresentacionNombre', 'Laboratorio', 'Proveedor']"
            :titulos="['Codigo / Descripción', '#Descripcion', '#PresentacionNombre', '#Marca', '#Proveedor']"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" />
            <buscador ref="BuscarMovimiento" buscador_titulo="Buscador / Movimiento" :api="'app/v1_OrdenCompra/ConsultaMovimientoXTipo'" 
            :api_filtro="{TipoMovimiento:id_tipo_movimiento_seleccionado}"
            :campos="['Codigo', 'Fecha']"
            :titulos="['Codigo', '#Fecha']"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" />
        <vs-popup classContent="popup-example" title="Resultado Búsqueda" :active.sync="Estado_VentanaEmergente_Busqueda" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
                <form>
                    <vs-divider></vs-divider>
                    <vs-table2 max-items="10" pagination :data="producto" id="tb_departamentos">
                        <template slot="thead">
                            <th>Codigo</th>
                            <th>Descripcion</th>
                            <!-- <th>Marca</th>
                            <th>Concentración</th> -->
                            <th>Presentación</th>
                            <th></th>
                        </template>
    
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 :data="data[indextr].Codigo">
                                    {{data[indextr].Codigo}}
                                </vs-td2>
    
                                <vs-td2 :data="data[indextr].Descripcion">
                                    {{data[indextr].Descripcion}}
                                </vs-td2>
    
                                <!-- <vs-td2 :data="data[indextr].Concentracion">
                                    {{data[indextr].Concentracion}}
                                </vs-td2>
    
                                <vs-td2 :data="data[indextr].codigo_interno">
                                    {{data[indextr].codigo_interno}}
                                </vs-td2> -->
    
                                <vs-td2 :data="data[indextr].PresentacionNombre">
                                    {{data[indextr].PresentacionNombre}}
                                </vs-td2>
                                <vs-td2 align="right">
                                    <vs-button color="primary" icon-pack="fas" icon="fa-check-circle" style="display:inline-block;margin-right:2px" @click="Seleccionar_Producto(data[indextr])"></vs-button>
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
                </form>
    
            </div>
        </vs-popup>
        <vx-card title="Seleccione un reporte" class="box-custom1">
            <div class="containerem">
                <vs-row>
                    <vs-col>
                        <vs-radio v-model="reporte" vs-name="radios1" vs-value="0">Movimiento de Bodega</vs-radio>
                    </vs-col>
                </vs-row>
                <br>
                <vs-row>
                    <vs-col>
                        <vs-radio v-model="reporte" vs-name="radios1" vs-value="1">Existencias</vs-radio>
                    </vs-col>
                </vs-row>
            </div>
        </vx-card>
        &nbsp;
        <vx-card title="Parámetros Movimiento por Bodega" class="box-custom2" :style="parametrosMovimientosBodega">
            <div>
                <div class="container-input">
                    <vs-row vs-justify="center" vs-align="center">
                        <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="8">
                            <label class="typo__label">Tipo</label>
                            &nbsp;
                            <multiselect :allow-empty="false" v-model="cb_busqueda_tipo_movimiento" :options="Lista_tipo_movimiento" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="TipoMovimiento_seleccionada" placeholder="Seleccionar Tipo" @input="onChangeTipoMovimiento">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </vs-col>
                    </vs-row>
                </div>
                <br>
                <br> 
                <div  class="container">
                    <label class="typo__label">Orderar Por</label>
                    <vs-divider/>
                    <div class="div-container">
                        <vs-row vs-justify="center" vs-align="center">
                            <vs-col vs-w="4">
                                <vs-radio v-model="ordenar" vs-name="radios2" vs-value="0">Movimiento</vs-radio>
                            </vs-col>
                            <vs-col vs-w="4">
                                <vs-radio v-model="ordenar" vs-name="radios2" vs-value="1">Bodega</vs-radio>
                            </vs-col>
                        </vs-row>
                    </div>
                </div>
                <br>
                <br> 
                <div class="container-input">
                    <vs-row vs-justify="center" vs-align="center">
                        <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="4">
                            <label class="typo__label">Número:</label>
                            &nbsp;
                            <vx-input-group class="">
                                <vs-input type="number" v-model="numeroMovimiento" />
                                <template slot="append">
                                    <div class="append-text btn-addon">
                                        <button type="submit" v-show="false" name="button"></button>
                                        <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="CargarMovimiento()" icon="fa-search"></vs-button>
                                    </div>
                                </template>
                            </vx-input-group>
                        </vs-col>
                    </vs-row>
                </div>
                <br>
                <br>
                <div class="container-input">
                    <vs-row vs-justify="center" vs-align="center">
                        <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="4">
                            <label class="typo__label">Solicitud:</label>
                            &nbsp;
                            <vx-input-group class="">
                                <vs-input type="number" v-model="numeroSolicitud" />
                            </vx-input-group>
                        </vs-col>
                    </vs-row>
                </div>
                <br>
                <br>
                <div class="container-input">
                    <vs-row vs-justify="center" vs-align="center">
                        <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="3">
                            <label class="typo__label">Del: </label>
                            &nbsp;
                            <vs-input type="date" class="datePicker" v-model="fechaInicial"></vs-input>
                        </vs-col>
                        <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="3">
                            <label class="typo__label">Al: </label>
                            &nbsp;
                            <vs-input type="date" class="datePicker" v-model="fechaFinal"></vs-input>
                        </vs-col>
                    </vs-row>
                </div>
                <br>
                <br>
                <div class="container-input">
                    <vs-row vs-justify="center" vs-align="center">
                        <vs-col v-if="validarBodegaOrigen !== 'N'" vs-type="flex" vs-justify="center" vs-align="center" vs-w="5">
                            <label class="typo__label">Bodega Fuente: </label>
                            &nbsp;
                            <vs-input type="number" v-model="bodegaFuente"></vs-input>
                        </vs-col>
                        <vs-col v-if="validarBodegaDestino !== 'N'" vs-type="flex" vs-justify="center" vs-align="center" vs-w="5">
                            <label class="typo__label">Bodega Destino: </label>
                            &nbsp;
                            <vs-input type="number" v-model="bodegaDestino"></vs-input>
                        </vs-col>
                    </vs-row>
                </div>
                <div>
                    <vs-button @click="Generar" color="success" class="btn btn-primary mx-3 my-3" icon-pack="fas" icon="fa-save" style="float:right;margin: 5px">
                        Generar
                    </vs-button>
                </div>
            </div>
        </vx-card>
        <vx-card title="Parámetros Existencias" class="box-custom2" :style="parametrosExisyencias">
            <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                <div>
                    <!-- <div  class="container">
                        <label class="typo__label">Orderar Por</label>
                        <vs-divider/>
                        <div class="div-container">
                            <vs-row vs-justify="center" vs-align="center">
                                <vs-col vs-w="4">
                                    <vs-radio v-model="ordenar" vs-name="radios2" vs-value="0">Departamento</vs-radio>
                                </vs-col>
                                <vs-col vs-w="4">
                                    <vs-radio v-model="ordenar" vs-name="radios2" vs-value="1">Marca</vs-radio>
                                </vs-col>
                            </vs-row>
                            <br>
                            <vs-row vs-justify="center" vs-align="center">
                                <vs-col vs-w="4">
                                    <vs-radio v-model="ordenar" vs-name="radios2" vs-value="2">Proveedor</vs-radio>
                                </vs-col>
                                <vs-col vs-w="4">
                                    <vs-radio v-model="ordenar" vs-name="radios2" vs-value="3">Categoria</vs-radio>
                                </vs-col>
                            </vs-row>
                        </div>
                    </div>
                    <br>
                    <br> -->
                    <div class="container-input">
                        <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                            <vs-row vs-justify="center" vs-align="center">
                                <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="8">
                                    <label class="typo__label">Bodega</label>
                                    &nbsp;
                                    <multiselect v-model="cb_bodegas" :options="Lista_bodegas" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Bodegas_seleccionado" placeholder="Seleccionar bodega" @input="onChangeBodega" :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null">
                                        <span slot="noOptions">Lista no disponible.</span>
                                    </multiselect>
                                </vs-col>
                            </vs-row>
                        </ValidationProvider>
                    </div>
                    <br>
                    <br>
                    <div class="container-input">
                        <vs-row vs-justify="center" vs-align="center">
                            <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="4">
                                <label class="typo__label">Solo Producto del: </label>
                                &nbsp;
                                <vx-input-group class="">
                                    <vs-input v-model="productoBuscarDel" v-on:blur="onchangeProductoDel()"/>
                                    <template slot="append">
                                        <div class="append-text btn-addon">
                                            <button type="submit" v-show="false" name="button"></button>
                                            <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="cargar_producto(0)" icon="fa-search"></vs-button>
                                        </div>
                                    </template>
                                </vx-input-group>
                            </vs-col>
                            &nbsp;
                            <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="4">
                                <label class="typo__label">al: </label>
                                &nbsp;
                                <vx-input-group class="">
                                    <vs-input v-model="productoBuscarAl" v-on:blur="onchangeProductoAl()"/>
                                    <template slot="append">
                                        <div class="append-text btn-addon">
                                            <button type="submit" v-show="false" name="button"></button>
                                            <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="cargar_producto(1)" icon="fa-search"></vs-button>
                                        </div>
                                    </template>
                                </vx-input-group>
                            </vs-col>
                        </vs-row>
                    </div>
                    <!-- <br>
                    <br>
                    <div class="container-input">
                        <vs-row vs-justify="center" vs-align="center">
                            <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="6">
                                <label class="typo__label">Proveedor</label>
                                &nbsp;
                                <multiselect v-model="cbProveedor" :options="listaProveedores" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="seleccion_proveedor" placeholder="Búsqueda" @input="onChangeProveedor">
                                    <span slot="noOptions">Lista no disponible.</span>
                                </multiselect>
                            </vs-col>
                        </vs-row>
                    </div> -->
                    <br>
                    <br>
                    <div class="container-input">
                        <vs-row vs-justify="center" vs-align="center">
                            <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="7">
                                <vs-checkbox v-model="isDiferente">Solo Diferentes a 0</vs-checkbox>
                            </vs-col>
                        </vs-row>
                    </div>
                    <div>
                        <vs-button @click="handleSubmit(Generar)" :disabled="invalid" color="success" class="btn btn-primary mx-3 my-3" icon-pack="fas" icon="fa-save" style="float:right;margin: 5px">
                            Generar
                        </vs-button>
                    </div>
                </div>
            </ValidationObserver>
        </vx-card>
    </div>
</template>
<script>
    import moment from 'moment'
    import Multiselect from 'vue-multiselect'
    import "vue-multiselect/dist/vue-multiselect.min.css"
    export default {
        components: {
            Multiselect
        },
        data() {
            return{
                Estado_VentanaEmergente_Busqueda: false,
                parametrosMovimientosBodega: 'display: none',
                parametrosExisyencias: '',
                reporte: '0',
                ordenar: '0',
                producto: [],
                Producto_seleccionado: {
                    Codigo: '',
                    Descripcion: '',
                    PresentacionNombre: ''
                },
                productoBuscarDel: '',
                productoBuscarAl: '',
                productoDelAl: '',
                cbProveedor: '',
                listaProveedores: [],
                proveedor:{
                    codigo: '',
                    nombre: '',
                    nit: ''
                },
                Lista_bodegas: [],
                id_bodega_seleccionada: '',
                cb_bodegas: '',
                bodega: '1',
                isDiferente: false,
                diferentesCero: 0,
                Lista_tipo_movimiento: [],
                cb_busqueda_tipo_movimiento: '',
                id_tipo_movimiento_seleccionado: '',
                numeroMovimiento: '',
                isMovimiento: false,
                movimiento: [],
                movimiento_seleccionado: {
                    Codigo: '',
                    Fecha: ''
                },
                fechaInicial: '',
                fechaFinal: '',
                validarBodegaOrigen: '',
                validarBodegaDestino: '',
                bodegaFuente: '',
                bodegaDestino: '',
                numeroSolicitud: ''
            }
        },
        methods:{
            Consultar_Bodega() {
                const url = this.$store.state.global.url
                this.axios.post(url+'app/v1_OrdenCompra/TodasBodegasActivas', {
                    operacion: 'T'
                })
                .then(resp => {

                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Bodegas',
                            text: resp.data.mensaje,
                        })
                    } else {
                        this.Lista_bodegas = resp.data.json                                        
                    }
                })
        
            },
            Bodegas_seleccionado({
                CODIGO,
                NOMBRE
            }) {
                return `${CODIGO} - ${NOMBRE} `;
            },
            onChangeBodega(value) {
                if (value !== null && value.length !== 0) {
                    this.id_bodega_seleccionada = value.CODIGO
                    this.tipoBodega = value.TipoBodega
    
                } else {
                    this.id_bodega_seleccionada = ''
                    this.tipoBodega = ''
                }
            },
            Limpiar_Campos() {
                this.Producto_seleccionado.Codigo = '';
                this.Producto_seleccionado.Descripcion = '';
                this.Producto_seleccionado.PresentacionNombre = '';
                
            },
            Mostrar_resultado_producto(value) {
                if (value.length === 1) {
                    value.map(obj => {
                        this.productoBuscarAl = obj.Codigo
                        this.Producto_seleccionado.Codigo = obj.Codigo;
                        this.Producto_seleccionado.PresentacionNombre = obj.PresentacionNombre;
                    })
                } else if (value.length > 1) {
                    this.Estado_VentanaEmergente_Busqueda = true;
                }
            },
            cargar_producto(value){
                this.Limpiar_Campos();
                this.productoDelAl = value
                this.$refs.BuscarProducto.iniciar((data) => {
                    if(data != null){
                        
                        if(value == 0){
                            this.productoBuscarDel = data.Codigo
                        }else{
                            this.productoBuscarAl = data.Codigo
                        }
                        // this.productoSeleccionadoRebajar.Codigo = data.Codigo
                        // this.productoSeleccionadoRebajar.Descripcion = data.Descripcion
                        // this.productoSeleccionadoRebajar.PresentacionNombre = data.PresentacionNombre
                        // this.productoSeleccionadoRebajar.Marca = data.Marca
                        // this.productoSeleccionadoRebajar.CostoPromedio = parseFloat(data.CostoPromedio).toFixed(2)

                        // this.Mostrar_resultado_producto(this.producto);
                    }
                })


                // this.productoDelAl = value
                // if(value == 0){
                //     this.axios.post('/app/v1_OrdenCompra/ConsultaTodosProductos', {
                //         Producto: this.productoBuscarDel.trim(),
                //         TipoBodega: 'M'
                //     })
                //     .then(resp => {
                //         if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                //             this.producto = []
                //             resp.data.json.map(data => {
                //                 this.producto.push({
                //                     ...data
                //                 })
                //             })

                //             this.Mostrar_resultado_producto(this.producto);

                //         } else {
                //             this.producto = []
                //             this.$vs.notify({
                //                 color: 'danger',
                //                 title: 'Producto',
                //                 text: (resp.data.codigo < 0) ? resp.data.descripcion : 'Sin Registros',
                //             })

                //         }

                //     })
                // }else{
                //     this.axios.post('/app/v1_OrdenCompra/ConsultaTodosProductos', {
                //         Producto: this.productoBuscarAl.trim(),
                //         TipoBodega: 'M'
                //     })
                //     .then(resp => {
                //         if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                //             this.producto = []
                //             resp.data.json.map(data => {
                //                 this.producto.push({
                //                     ...data
                //                 })
                //             })

                //             this.Mostrar_resultado_producto(this.producto);

                //         } else {
                //             this.producto = []
                //             this.$vs.notify({
                //                 color: 'danger',
                //                 title: 'Producto',
                //                 text: (resp.data.codigo < 0) ? resp.data.descripcion : 'Sin Registros',
                //             })

                //         }

                //     })
                // }
                

            },
            Seleccionar_Producto(value) {
                if(this.productoDelAl == 0){
                    this.productoBuscarDel = value.Codigo;
                    this.productoBuscarAl = value.Codigo;
                    this.Producto_seleccionado.Codigo = value.Codigo;
                    this.Producto_seleccionado.PresentacionNombre = value.PresentacionNombre;

                    this.Estado_VentanaEmergente_Busqueda = false;
                    this.producto = [];
                }else{
                    this.productoBuscarAl = value.Codigo;
                    this.Producto_seleccionado.Codigo = value.Codigo;
                    this.Producto_seleccionado.PresentacionNombre = value.PresentacionNombre;

                    this.Estado_VentanaEmergente_Busqueda = false;
                    this.producto = [];
                }
                
            },
            onchangeProductoDel(){
                if(this.productoBuscarAl == '' || this.productoBuscarAl == null){
                    this.productoBuscarAl = this.productoBuscarDel
                }
                if(this.productoBuscarDel == '' || this.productoBuscarDel == null){
                    this.productoBuscarAl = this.productoBuscarDel
                }
                
            },
            onchangeProductoAl(){
                if(this.productoBuscarAl == '' || this.productoBuscarAl == null){
                    this.productoBuscarAl = this.productoBuscarDel
                }
                if(this.productoBuscarDel == '' || this.productoBuscarDel == null){
                    this.productoBuscarDel = this.productoBuscarAl
                }
            },
            Consultar_proveedor() {
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/ConsultarProveedor', {
                        
                    })
                    .then(resp => {

                        if (resp.data.codigo != 0) {

                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Proveedores',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.listaProveedores = "";
                        } else {
                            //Decodificación
                            this.listaProveedores = resp.data.json;

                        }
                    })
                    .catch(() => {})
            },
            seleccion_proveedor({
                NIT,
                NOMBRE,
            }) {
                return `${NIT} - ${NOMBRE} `
            },
            onChangeProveedor(value) {
                if (value !== null && value.length !== 0) {

                    this.proveedor.codigo = value.CODIGO,
                    this.proveedor.nombre = value.NOMBRE.toUpperCase();
                    this.proveedor.nit = value.NIT;

                } else {
                    this.proveedor.codigo = '';
                    this.proveedor.nombre = '';
                    this.proveedor.nit = '';
                }
            },
            Consultar_TipoMovimiento() {
    
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/ConsultaTodosTipoMovimiento', {
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.cb_busqueda_tipo_movimiento = null //Limpiar los valores seleccionados multiselec
                        
                        this.Lista_tipo_movimiento = [];

                    } else {
                        this.Lista_tipo_movimiento = resp.data.json
                        this.cb_busqueda_tipo_movimiento = {
                            Nombre: resp.data.json[0].Nombre
                        }
                        this.id_tipo_movimiento_seleccionado = resp.data.json[0].Codigo
                    }
                })
            },
            TipoMovimiento_seleccionada({
                Nombre
            }) {
                return `${Nombre}`
            },
            onChangeTipoMovimiento(value) {
                this.bodegaFuente = ''
                this.bodegaDestino = ''
                this.numeroMovimiento = ''
                this.numeroSolicitud = ''
                this.ObtenerFechas()
                //Si existe seleccionada un elemento
                if (value !== null && value.length !== 0) {

                    this.id_tipo_movimiento_seleccionado = value.Codigo;
                    this.validarBodegaOrigen = value.BodegaFuente
                    this.validarBodegaDestino = value.BodegaDestino
                } else {
                    this.id_tipo_movimiento_seleccionado = '0'
    
                }
    
            },
            Limpiar_CamposMovimiento() {
                this.movimiento_seleccionado.Codigo = ''
                this.movimiento_seleccionado.Fecha = ''
                
            },
            Seleccionar_Movimiento(value){
                this.movimiento_seleccionado.Codigo = value.Codigo
                this.movimiento_seleccionado.Fecha = value.Fecha
                this.numeroMovimiento = value.Codigo
                this.isMovimiento = false
            },
            CargarMovimiento(){
                if(this.id_tipo_movimiento_seleccionado == '' || this.id_tipo_movimiento_seleccionado == null){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Buscar Movimiento',
                        text: 'Debe de seleccionar el tipo de movimiento que desea buscar',
                    })
                }else{
                    this.Limpiar_CamposMovimiento()
                    this.$refs.BuscarMovimiento.iniciar((data) => {
                        if(data != null){
                            this.Seleccionar_Movimiento(data)
                        }
                    })
                }
                
                // this.axios.post('/app/v1_OrdenCompra/ConsultaMovimientoXTipo',{
                //     TipoMovimiento: this.id_tipo_movimiento_seleccionado
                // })
                // .then(resp => {
                //     if (resp.data.codigo != 0) {
                //         this.$vs.notify({
                //             color: '#B71C1C',
                //             title: 'Alerta',
                //             text: resp.data.mensaje,
                //         })
                //         //Limpia la tabla si no existen registros
                //         this.movimiento = [];

                //     } else {
                //         this.movimiento = resp.data.json;

                //     }
                // })
            },
            ObtenerFechas(){
                var date = new Date()
                this.fechaInicial = moment(new Date(date.getFullYear(), date.getMonth(), 1)).format('YYYY-MM-DD')
                this.fechaFinal = moment(new Date(date.getFullYear(), date.getMonth() + 1, 0)).format('YYYY-MM-DD')
            },
            Generar(){
                if(this.reporte == 0){
                    this.$reporte_modal({
                        Nombre: "Movimientos por Bodega",
                        Opciones: {
                            CodigoMovimiento: this.numeroMovimiento,
                            TipoMovimiento: this.id_tipo_movimiento_seleccionado,
                            FechaInicial: this.fechaInicial,
                            FechaFinal: this.fechaFinal,
                            BodegaFuente: this.bodegaFuente,
                            BodegaDestino: this.bodegaDestino,
                            OrdenarPor: this.ordenar,
                            CODIGO_SOLICITUD: this.numeroSolicitud
                        },
                        Formato: "EXCEL"
                    })
                    this.$reporte_modal({
                        Nombre: "Movimientos por Bodega",
                        Opciones: {
                            CodigoMovimiento: this.numeroMovimiento,
                            TipoMovimiento: this.id_tipo_movimiento_seleccionado,
                            FechaInicial: this.fechaInicial,
                            FechaFinal: this.fechaFinal,
                            BodegaFuente: this.bodegaFuente,
                            BodegaDestino: this.bodegaDestino,
                            OrdenarPor: this.ordenar,
                            CODIGO_SOLICITUD: this.numeroSolicitud
                        },
                        Formato: "PDF"
                    })
                }else if(this.reporte == 1){
                    this.diferentesCero = this.isDiferente ? 1 : 0
                    this.$reporte_modal({
                        Nombre: "Existencia Por Producto",
                        Opciones: {
                            CodigoBodega: this.id_bodega_seleccionada,
                            ProductoDel: this.productoBuscarDel,
                            ProductoAl: this.productoBuscarAl,
                            Existencia: this.diferentesCero
                        },
                        Formato: "EXCEL"
                    })
                    this.$reporte_modal({
                        Nombre: "Existencia Por Producto",
                        Opciones: {
                            CodigoBodega: this.id_bodega_seleccionada,
                            ProductoDel: this.productoBuscarDel,
                            ProductoAl: this.productoBuscarAl,
                            Existencia: this.diferentesCero
                        },
                        Formato: "PDF"
                    })
                } 
            }
        },
        watch: {
            reporte(value){
                if(value == 0){
                    this.parametrosMovimientosBodega = ''
                    this.parametrosExisyencias = 'display: none'
                }
                if(value == 1){
                    this.parametrosMovimientosBodega = 'display: none'
                    this.parametrosExisyencias = ''
                }
            }
        },
        mounted(){
            // this.Consultar_proveedor()
            this.Consultar_Bodega()
            this.Consultar_TipoMovimiento()


            if(this.reporte == 0){
                this.parametrosMovimientosBodega = ''
                this.parametrosExisyencias = 'display: none'
                this.validarBodegaOrigen = 'N'
                this.validarBodegaDestino = 'N'
            }
            if(this.reporte == 1){
                this.parametrosMovimientosBodega = 'display: none'
                this.parametrosExisyencias = ''
            }
            this.ObtenerFechas()
        }
    }
</script>
<style lang="scss" scoped>
    .box-custom1{
        box-shadow: 0px 15px 40px -10px;
        padding: 15px;
        width: 25%;
    }
    .box-custom2{
        box-shadow: 0px 15px 40px -10px;
        padding: 15px;
        width: 70%;
    }
    .container{
        border: 1px solid #888;
        width: 50%;
        align-content: center;
    }
    .container-input{
        align-content: center;
    }
    .div-container{
        padding: 5px;
        margin: 10px;
    }
</style>