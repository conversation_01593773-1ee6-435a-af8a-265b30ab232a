<template lang="html">
<vx-card title="Pedido Sugerido">
    <vs-row>
        <vs-col vs-justify="center" vs-align="center" vs-w="5" id="columna2">
            <buscador buscador_titulo="Buscador " ref="buscar_producto" :bloqueo="false" :api="'app/inventario/BusquedaProducto'" :campos="['Codigo','Nombre']" :titulos="['Codigo','Nombre']" :multiselect="false" />
            <div>
                <vs-checkbox v-model="ClaseBodega" @change="CambiarClaseBodega">Pedido Bodega Madre</vs-checkbox>
            </div>
            <P>
                <b>Seleccione bodega Fuente/Madre:</b>
            </P>
            <multiselect v-model="bodega" :options="ListadoBodegas" :allow-empty="false" placeholder="Seleccione una bodega (Requerido)" track-by="Nombre" label="Nombre" @input="cargar_subbodegas"></multiselect>
            <P>
                <b>Seleccione bodega Destino/SubBodega:</b>
            </P>
            <multiselect v-model="subbodega" :options="ListadoSubBodegas" :disabled="ClaseBodega" :allow-empty="false" placeholder="Seleccione una subbodega (Requerido)" track-by="Nombre" label="Nombre" class="zy" z-index="105"></multiselect>
        </vs-col>
        <vs-col vs-justify="center" vs-align="center" vs-w="3" id="columnaTipoB">
            <p>
                <b>Tipo de Bodega:</b>
            </p>
            <ul class="leftx" id="tipobodega">
                <li v-for="Tipo in Tipos" :key="Tipo.Id">
                    <vs-radio v-model="TipoBodega" vs-name="TipoBodega" :vs-value="Tipo">{{Tipo.Nombre}}</vs-radio>
                </li>
            </ul>
        </vs-col>
        <vs-col vs-justify="center" vs-align="center" vs-w="4" id="columna2">
            <P>
                <b>Grupo:</b>
            </P>
            <multiselect v-model="grupo" :options="ListadoGrupos" :allow-empty="false" placeholder="Seleccione un Grupo (Requerido)" track-by="Nombre" label="Nombre" @input="cargar_subgrupos"></multiselect>

            <P>
                <b>SubGrupo.</b>
            </P>
            <multiselect v-model="subgrupo" :options="ListadoSubGrupos" :allow-empty="true" deselect-label="Presione enter para remover" select-label="Seleccione" placeholder="Seleccione un SubGrupo (opcional)" track-by="Nombre" label="Nombre"></multiselect>
        </vs-col>
    </vs-row>
    <vs-row>
        <vs-col vs-justify="center" vs-align="center" vs-w="5" id="columna2">
            <vs-textarea label="Descripción:" v-model="descripcion" />
        </vs-col>
        <vs-col vs-justify="center" vs-align="center" vs-w="3" id="columnaTipoB">
            <br>
            <br>
            <vs-button :disabled="BotonDesabilitarGenerarPedido" color="green" type="filled" @click="GenerarPedido" icon="cached">Generar Pedido</vs-button>
        </vs-col>
    </vs-row>
    <vs-row>
        <vs-col vs-justify="center" vs-align="center" vs-w="12" id="columna2">
            <vs-divider>Listado de Productos Pedido Sugerido</vs-divider>
            <template lang="html">
                <div>
                    <vs-table max-items="15" pagination :data="ListadoPedido" noDataText="Sin datos disponibles" id="zx">
                        <template slot="thead">
                            <vs-th>No.</vs-th>
                            <vs-th>Producto</vs-th>
                            <vs-th>NombreProducto</vs-th>
                            <vs-th>UMedida</vs-th>
                            <vs-th>
                                <vx-tooltip title="CPM" color="warning" text="||SUBBODEGAS||: ((Ventas - Anulaciones)  + Cargos + (Requerimientos-Devoluciones Requerimiento))/12. ||BODEGA_MADRE||:  (Requerimientos+Despachos)/12">
                                    CPM
                                </vx-tooltip>
                            </vs-th>
                            <vs-th>Exist</vs-th>

                            <vs-th>
                                <vx-tooltip title="Mínimo:" color="warning" text="Min = (Min en ficha del producto)/30 * CPM"> Min</vx-tooltip>
                            </vs-th>

                            <vs-th>
                                <vx-tooltip title="Máximo:" color="warning" text="Max = (Max en ficha del producto)/30 * CPM">Max</vx-tooltip>
                            </vs-th>

                            <vs-th>UEmpaque</vs-th>
                            <vs-th>
                                <vx-tooltip title="Pedido Sugerido:" color="warning" text="PS = (Max en ficha del producto)/30 * CPM – existencia. Aproximado a la Unidad de Empaque más cercana">
                                    PSugerido
                                </vx-tooltip>
                            </vs-th>
                            <vs-th>Transito</vs-th>
                            <vs-th>Cant Pedido</vs-th>
                            <vs-th></vs-th>
                        </template>

                        <template slot-scope="{data}">
                            <vs-tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td :data="tr.LineaDocumento">{{indextr+1}}</vs-td>
                                <vs-td :data="tr.Producto">{{tr.Producto}}</vs-td>
                                <vs-td :data="tr.NombreProducto">{{tr.NombreProducto}}</vs-td>
                                <vs-td :data="tr.UnidadMedida">{{tr.UnidadMedida}}</vs-td>

                                <vs-td :data="tr.PromedioMensual">{{tr.PromedioMensual}}</vs-td>

                                <vs-td :data="tr.Existencia">{{tr.Existencia}}</vs-td>

                                <vs-td :data="tr.Minimos">{{tr.Minimos}}</vs-td>
                                <vs-td :data="tr.Maximos">{{tr.Maximos}}</vs-td>
                                <vs-td :data="tr.UnidadEmpaque">{{tr.UnidadEmpaque}}</vs-td>

                                <vs-td :data="tr.PedidoSugerido">{{tr.PedidoSugerido}}</vs-td>

                                <vs-td :data="tr.Transito">
                                    <vs-input v-model="tr.Transito" class="inputx" type="number" placeholder="Transito" style="width:75px" />
                                </vs-td>
                                <vs-td :data="tr.PedidoCantidad">
                                    <vs-input v-model="tr.PedidoCantidad" class="inputx" type="number" placeholder="Cantidad Pedido" style="width:75px" />
                                </vs-td>
                                <vs-td>
                                    <vs-button radius color="danger" type="filled" icon="delete_outline" size="small" @click="ListadoPedido.splice(indextr,1)"></vs-button>
                                </vs-td>
                            </vs-tr>
                        </template>
                    </vs-table>
                </div>
            </template>

        </vs-col>
    </vs-row>
    <vs-row>
        <vs-col vs-justify="center" vs-align="center" vs-w="2">
            <vs-button @click="NuevoPedido" type="filled" icon="create">NuevoPedido</vs-button>
        </vs-col>
        <vs-col vs-justify="center" vs-align="center" vs-w="2">
            <vs-button :disabled="ListadoPedido.length==0" color="primary" type="filled" @click="RestarTransito" icon="remove">Menos Transito</vs-button>
        </vs-col>
        <vs-col vs-justify="center" vs-align="center" vs-w="2">
            <vs-button @click="popupActivo=true" type="filled" :disabled="(this.bodega.length == 0 ||(this.subbodega.length == 0 && this.ClaseBodega == false))" icon="add">Agregar Producto</vs-button>
        </vs-col>
        <vs-col vs-justify="center" vs-align="center" vs-w="2">
            <vs-button :disabled="ListadoPedido.length==0 || this.bodega.length == 0 || (this.subbodega.length == 0 && this.ClaseBodega == false) ||   this.TipoBodega.length == 0"  @click="GuardarPedido">Guardar Pedido</vs-button>
        </vs-col>

        <vs-popup title="Codigo del Producto" :active.sync="popupActivo" button>
            <p>
                <vx-input-group class>
                    <vs-input v-model="CodigoProducto" />
                    <template slot="append">
                        <div class="append-text btn-addon">
                            <button type="submit" v-show="false" name="button"></button>
                            <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_producto()" icon="icon-search"></vs-button>
                        </div>
                    </template>
                </vx-input-group>
                <vs-button icon="add" color="primary" type="filled" @click="AgregarProducto">Agregar Producto</vs-button>
            </p>
        </vs-popup>

    </vs-row>
</vx-card>
</template>

<script>
import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";
export default {
    data() {
        return {
            i: 0,
            ClaseBodega: false,
            ClaseBodegaAPI: "S",
            BodegaAPI: 0,
            ListadoBodegas: [],
            ListadoSubBodegas: [],
            ListadoGrupos: [],
            ListadoSubGrupos: [],
            bodega: [],
            grupo: [],
            subgrupo: [],
            no_bodega: null,
            subbodega: [],
            TipoBodega: [],
            descripcion: "",
            ListadoPedido: [],
            ListadoPedidoUnitario: [],
            popupActivo: false,
            CodigoProducto: null,
            NombreProducto: null,
            Periodo: [],
            GPeriodo: null,
            GCorrelativoPedidoSugerido: null,
            GCorrelativoMovimiento: null,
            guardadoconexito: false,
            Tipos: [
                {
                    CodigoAgrupacion: 2,
                    Tipo: "M",
                    Nombre: "Medicamentos"
        },
                {
                    CodigoAgrupacion: 6,
                    Tipo: "R",
                    Nombre: "Repuestos y Suministros"
        },
                {
                    CodigoAgrupacion: 7,
                    Tipo: "Z",
                    Nombre: "Limpieza"
        },
                {
                    CodigoAgrupacion: 2,
                    Tipo: "D",
                    Nombre: "Diagnostico"
        },
                {
                    CodigoAgrupacion: 3,
                    Tipo: "L",
                    Nombre: "Libreria"
        },
                {
                    CodigoAgrupacion: 4,
                    Tipo: "A",
                    Nombre: "Cocina"
        }
      ]
        };
    },
    methods: {
        cargar_bodegas() {
            this.axios
                .post("/app/requerimientos/ListadoBodegas", {})
                .then(resp => {
                    //                   
                    this.ListadoBodegas = resp.data.json;
                })
                .catch( );
        }, //Fin de metodo cargar bodegas
        cargar_subbodegas() {
            (this.subbodega = []), (this.ListadoSubBodegas = []);
            if (!this.ClaseBodega) {
                //es subbodega
                this.axios
                    .post("/app/requerimientos/ListadoSubBodegas", {
                        BodegaDespacho: this.bodega.Codigo
                    })
                    .then(resp => {
                        //                    console.log(resp.data)
                        this.ListadoSubBodegas = resp.data.json;
                    })
                    .catch(
                        
                    );
            } else {
                this.subbodega = this.bodega;
            }
        }, //Fin de metodo cargar subbodegas
        cargar_grupos() {
            this.axios
                .post("/app/requerimientos/ListadoGrupos", {})
                .then(resp => {
                    //                    console.log(resp.data)
                    this.ListadoGrupos = resp.data.json;
                })
                .catch(
                    
                );
        }, //Fin de metodo cargar grupos
        cargar_subgrupos() {
            (this.subgrupo = []), (this.ListadoSubGrupos = []);
            this.axios
                .post("/app/requerimientos/ListadoSubGrupos", {
                    Grupo: this.grupo.Codigo
                })
                .then(resp => {
                    this.ListadoSubGrupos = resp.data.json;
                })
                .catch(
                    
                );
        }, //Fin de metodo cargar subgrupos
        Deshabilitar_Bodega_Destino() {
            (this.subgrupo = []), (this.ListadoSubGrupos = []);
        }, //Fin de metodo cargar subgrupos
        GenerarPedido() {
            var f = new Date();
            if (!this.ClaseBodega) {
                //es subbodega
                this.ClaseBodegaAPI = "S";
                this.BodegaAPI = this.subbodega.Codigo;
                this.descripcion =
                    "Pedido Sugerido de la Sucursal " +
                    this.subbodega.Nombre +
                    " el dia " +
                    (f.getDate() + "/" + (f.getMonth() + 1) + "/" + f.getFullYear()) +
                    " para la categoria " +
                    this.TipoBodega.Nombre;
            } else {
                //es bodega madre
                this.ClaseBodegaAPI = "M";
                this.BodegaAPI = this.bodega.Codigo;
                this.descripcion =
                    "Pedido Sugerido de la Sucursal " +
                    this.bodega.Nombre +
                    " el dia " +
                    (f.getDate() + "/" + (f.getMonth() + 1) + "/" + f.getFullYear()) +
                    " para la categoria " +
                    this.TipoBodega.Nombre;
            }
            if (this.subgrupo.length <= 0) this.subgrupo.Codigo = 0;
            this.ListadoPedido = [];

            this.axios
                .post("/app/requerimientos/ListadoPedidoSugerido", {
                    Operacion: "s",
                    ClaseBodega: this.ClaseBodegaAPI,
                    Bodega: this.BodegaAPI,
                    Grupo: this.grupo.Codigo,
                    SubGrupo: this.subgrupo.Codigo
                })
                .then(resp => {
                    this.ListadoPedido = resp.data.json;
                    
                    
                })
                .catch(
                    
                );
        }, //Fin de metodo GenerarPedido
        GuardarPedido() {
            
            this.axios
                .post("/app/requerimientos/PeriodoActual", {})
                .then(resp => {
                    this.Periodo = resp.data.json;
                    //Se verifica Periodo en Contadb
                    if (this.Periodo.length <= 0 || this.Periodo[0].InvEstadisticas) {
                        return new Promise((resolve, reject) => {
                            reject(true);
                        });
                    } else {
                        this.GPeriodo = this.Periodo[0].Codigo;
                        //averiguamos el correlativo de movimiento y pedido sugerido
                        return this.axios.post("/app/requerimientos/correlativo", {
                            ClaseBodega: this.ClaseBodegaAPI
                        });
                    }
                })
                .then(resp => {
                    this.GCorrelativoPedidoSugerido =
                        resp.data.json[0].CorrelativoPedidoSugerido;
                    this.GCorrelativoMovimiento = resp.data.json[0].CorrelativoMovimiento;
                    //Grabo los encabezados de pedido sugerido y movimientos (tablas)
                    return this.axios.post(
                        "/app/requerimientos/GrabarEncabezadosPedidos", {
                            ClaseBodega: this.ClaseBodegaAPI,
                            CodigoMovimiento: this.GCorrelativoMovimiento,
                            CodigoPS: this.GCorrelativoPedidoSugerido,
                            Periodo: this.GPeriodo,
                            BodegaFuente: this.bodega.Codigo,
                            BodegaDestino: this.subbodega.Codigo,
                            Comentario: this.descripcion,
                            CodigoAgrupacion: this.TipoBodega.CodigoAgrupacion,
                            TipoPS: this.TipoBodega.Tipo
                        }
                    );
                })
                .then(() => {
                    //   console.log(resp)
                    return new Promise((resolve, reject) => {
                        this.i = 0
                        for (this.LineaPedido of this.ListadoPedido) {
                            //Grabamos el detalle del Pedido sugerido y detalle de movimiento (movimiento lineas)
                            if (this.LineaPedido.PedidoCantidad != 0) {
                                this.i++
                                this.axios
                                    .post("/app/requerimientos/GrabarDetallesPedidos", {
                                        ClaseBodega: this.ClaseBodegaAPI,
                                        CodigoMovimiento: this.GCorrelativoMovimiento,
                                        CodigoPS: this.GCorrelativoPedidoSugerido,
                                        Linea: this.i,
                                        Producto: this.LineaPedido.Producto,
                                        Cantidad: this.LineaPedido.PedidoCantidad,
                                        PromUso: this.LineaPedido.PromedioMensual,
                                        Existencia: this.LineaPedido.Existencia,
                                        PedidoSugerido: this.LineaPedido.PedidoSugerido,
                                        Minimo: this.LineaPedido.Minimos,
                                        Maximo: this.LineaPedido.Maximos,
                                        CostoPromedio: this.LineaPedido.CostoPromedio,
                                        UnidadMedida: this.LineaPedido.UnidadMedida,
                                        CostoUltimo: this.LineaPedido.CostoUltimo
                                    })
                                    .then(() => {
                                        this.guardadoconexito = true;
                                        resolve(true);
                                    })
                                    .catch(err => {
                                        reject(err);
                                    });
                            }
                        }

                    });
                })
                .then(async resp => {
                    
                    
                    if (this.guardadoconexito) {
                        if (!this.ClaseBodega)
                            this.mensaje =
                            "Guardado con éxito, Pedido No.: " +
                            this.GCorrelativoPedidoSugerido +
                            ", Movimiento/Requerimiento: " +
                            this.GCorrelativoMovimiento;
                        else
                            this.mensaje =
                            "Guardado con éxito, Pedido No.: " +
                            this.GCorrelativoPedidoSugerido;
                        this.$vs.notify({
                            title: "Guardado:",
                            text: this.mensaje,
                            color: "success",
                            fixed: true,
                            icon: 'check_box'
                        });
                        this.bodega = [];
                        this.subbodega = [];
                        this.grupo = [];
                        this.ListadoPedido = [];
                        this.TipoBodega = [];
                        this.Comentario = "";
                        this.guardadoconexito = false;
                    }
                })
                .catch(err => {
                    //mensaje de advertencia y no guarda}
                    
                    this.guardadoconexito = false;
                    if (this.Periodo.length <= 0 || this.Periodo[0].InvEstadisticas) {
                        this.$vs.notify({
                            title: "Atención:",
                            text: "Periodo no existe, ó Las estadísticas de este período ya fueron actualizadas",
                            color: "danger",
                            fixed: true
                        });
                    } else {
                        
                        this.$vs.notify({
                            title: "Atención:",
                            text: err,
                            color: "danger",
                            fixed: true,
                            icon: 'error'
                        });
                    }
                });
        }, //Fin de metodo GuardarPedido
        AgregarProducto() {
            if (!this.ClaseBodega) {
                //es subbodega
                this.BodegaAPI = this.subbodega.Codigo;
            } else {
                //es bodega madre
                this.BodegaAPI = this.bodega.Codigo;
            }
            if (this.subgrupo.length <= 0) this.subgrupo.Codigo = 0;

            this.axios
                .post("/app/requerimientos/ListadoPedidoSugerido", {
                    Operacion: "s",
                    ClaseBodega: this.ClaseBodegaAPI,
                    Bodega: this.BodegaAPI,
                    CodigoProducto: this.CodigoProducto
                })
                .then(resp => {
                    this.ListadoPedidoUnitario = resp.data.json;
                    
                    this.ListadoPedido.push(...this.ListadoPedidoUnitario);
                })
                .catch( );
        }, //Fin de metodo AgregarProducto = GenerarPedido INDIVIDUAL
        RestarTransito() {
            this.ListadoPedido.forEach(element => {
                if (element.PedidoSugerido - element.Transito > 0)
                    element.PedidoCantidad = element.PedidoSugerido - element.Transito;
            });
        },
        NuevoPedido() {
            location.reload()
        },
        buscar_producto() {
            this.$refs.buscar_producto.iniciar(data => {
                if (data != null) {
                    
                    this.CodigoProducto = data.Codigo;
                    this.NombreProducto = data.Nombre;
                }
            });
        },
        CambiarClaseBodega() {
            if (!this.ClaseBodega) {
                this.ClaseBodegaAPI = "S";
                this.subbodega = [];
            } else {
                this.ClaseBodegaAPI = "M"; //es bodega madre
                this.subbodega = this.bodega;
            }
        }
    },
    created() {},
    components: {
        Multiselect
    },
    mounted() {
        this.cargar_bodegas();
        this.cargar_grupos();
    },
    computed: {
        BotonDesabilitarGenerarPedido: function() {
            if (
                this.bodega.length == 0 ||
                (this.subbodega.length == 0 && this.ClaseBodega == false) ||
                this.grupo.length == 0 ||
                this.TipoBodega.length == 0
            )
                return true;
            else return false;
        }
    }
};
</script>

<style>
#columna2 {
    padding: 10px;
}

#tipobodega {
    padding-top: 10px;
}

#columnaTipoB {
    padding-left: 35px;
}

.vs-table--tbody {
    z-index: 1;
}
</style><style lang="stylus">
.popup-example {
    .vs-input {
        float: left;
        width: 50%;
        margin: 10px;
        margin-top: 5px;
    }

    .con-select {
        margin-left: 10px;
        width: 50%;
        margin-bottom: 10px;
    }

}
</style>
