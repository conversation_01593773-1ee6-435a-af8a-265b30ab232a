<template>
<vs-row class="p-2 row w-full">

    <vs-col vs-justify="center" vs-align="center" vs-w="10">
        <vx-card>
            <DxDataGrid :ref="dataGridRefKey" v-bind="DefaultDxGridConfiguration" :headerFilter="{visible:false,allowSearch:false}" :data-source="balanceDataSource" :height="'100%'" @cell-prepared="onCellPrepared">
                <DxSelection mode="single" />
                <DxToolbar>
                    <DxItem name="groupPanel"/>
                    <DxItem location="after" template="opcionesTemplate" />
                </DxToolbar>

                <DxColumn :width="100" data-field="Horario" caption="Horario" alignment="center" />
                <DxColumn caption="Contol de excreta" alignment="center">
                    <DxColumn :width="100" data-field="Orina" alignment="center" />
                    <DxColumn :width="100" data-field="Dens" caption="Dens." alignment="center" />
                    <DxColumn :width="100" data-field="Gastrico" caption="Gástrico" alignment="center" />
                    <DxColumn :width="100" data-field="Drenajes" alignment="center" />
                    <DxColumn :width="100" data-field="Heces" alignment="center" />
                    <DxColumn :width="100" data-field="OtrosExcreta" caption="Otros" alignment="center" />
                    <DxColumn :width="100" data-field="TotalExcreta" caption="Total" alignment="center" />
                </DxColumn>
                <DxColumn caption="Contol de ingesta" alignment="center">
                    <DxColumn :width="100" data-field="IV" alignment="center" />
                    <DxColumn :width="100" data-field="Oral" alignment="center" />
                    <DxColumn :width="100" data-field="OtrosIngesta" caption="Otros" alignment="center" />
                    <DxColumn :width="100" data-field="TotalIngesta" caption="Total" alignment="center" />
                </DxColumn>
                <DxColumn :width="100" data-field="Balance" caption="Balance" alignment="center" />

                <template #opcionesTemplate>
                    <ExpedienteGridToolBar v-bind="$props" :visible="true" :pdfExportItems="[{text:'Balance', reportName: 'Balance'}]" @add="addRow" @refresh="Cargar(true)" :report-param="$props" :showItems="['exportPdf', 'refresh']" />
                </template>
            </DxDataGrid>
        </vx-card>
    </vs-col>
    <vs-col vs-w="2" vs-justify="center" vs-align="center" class="pl-2">
        <vx-card title="Filtrar fecha">
            <DxDataGrid :ref="dataGridFechaKey" v-bind="DefaultDxGridConfiguration" :searchPanel="{visible: false}" :data-source="fechas" key-expr="id" :height="'100%'" @selection-changed="onSelectionChanged">
                <DxSelection mode="single" />
                <DxColumn :width="100" data-field="fecha" caption="Fecha" format="dd/MM/yyyy" data-type="date" alignment="center" />
            </DxDataGrid>
        </vx-card>
    </vs-col>
</vs-row>
</template>

<script>
import {
    DxDataGrid,
    DxColumn,
    DxToolbar,
    DxSelection
} from 'devextreme-vue/data-grid'
import CustomStore from 'devextreme/data/custom_store'
import ExpedienteGridToolBar from './ExpedienteGridToolBar.vue'
import {
    DxItem
} from 'devextreme-vue/form'
import 'devextreme-vue/lookup'
import {
    DefaultDxGridConfiguration
} from './data'

const dataGridRefKey = 'gridBalance'
const dataGridFechaKey = 'gridFechaKey'

export default {
    name: 'Balance',
    components: {
        DxDataGrid,
        DxColumn,
        ExpedienteGridToolBar,
        DxItem,
        DxToolbar,
        DxSelection
    },
    data() {
        return {
            //Listado de diagnosticos asociados a la admisión
            balance: [],
            balanceDataSource: new CustomStore({
                key: "id",
                load: () => {
                    return this.balance;
                },
                insert: () => {

                },
            }),
            fechas: [], //Listado de fechas de balance
            dataGridRefKey,
            DefaultDxGridConfiguration,
            dataGridFechaKey,
            selectHorarios: {
                items: this.horarios,
                searchEnabled: true,
                valueExpr: 'value',
                displayExpr: 'hora'
            },
            popupOptions: {
                showTitle: true,
                title: 'Agregar Diagnóstico',
                height: 490,
                toolbarItems: [{
                        widget: "dxButton",
                        toolbar: "bottom",
                        options: {
                            text: 'Guardar',
                            type: 'success',
                            useSubmitBehavior: true,
                            onClick: () => {
                                this.dataGrid.saveEditData()
                            }
                        },
                        location: "after"
                    },
                    {
                        widget: "dxButton",
                        toolbar: "bottom",
                        options: {
                            text: 'Cancelar',
                            type: 'danger',
                            useSubmitBehavior: true,
                            onClick: () => {
                                this.dataGrid.cancelEditData()
                            }
                        },
                        location: "after"
                    }
                ]
            }
        }
    },
    props: {
        SerieAdmision: null,
        CodigoAdmision: null,
        NumeroExpediente: null,

        StatusAdmision: null,
        horarios: null,
    },
    methods: {
        submit() {
            this.dataGrid.saveEditData()
        },
        Cargar(update, dia = 0, mes = 0, año = 0) {
            if (this.SerieAdmision && this.CodigoAdmision)
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaBalanceGrid', {
                    Serie: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    dia: dia,
                    mes: mes,
                    año: año
                })
                .then(resp => {
                    this.balance = resp.data.json.map((x, index) => {
                        return {
                            id: index,
                            Horario: x.Horario,
                            Orina: x.Orina,
                            Dens: x.Dens,
                            Gastrico: x.Gastrico,
                            Drenajes: x.Drenajes,
                            Heces: x.Heces,
                            OtrosExcreta: x.OtrosExcreta,
                            TotalExcreta: x.TotalExcreta,
                            IV: x.IV,
                            Oral: x.Oral,
                            OtrosIngesta: x.OtrosIngesta,
                            TotalIngesta: x.TotalIngesta,
                            Balance: x.Balance
                        }
                    })
                    this.balanceDataSource.load().then(
                        () => {
                            if (update) {
                                this.getFechasBalance();
                                this.refreshFechaDataGrid();
                                this.refreshDataGrid();
                            } else {
                                this.refreshDataGrid();
                            }
                        },
                        (error) => {
                            //console.error('error loading data customstore' + JSON.stringify(error))
                        }
                    )
                })
            else {
                this.balance = []
                this.refreshDataGrid()
                this.refreshFechaDataGrid()
            }
        },
        refreshDataGrid() {
            this.dataGrid.refresh()
                .then(function () {

                })
                .catch(function (error) {
                    //console.error('error refreshing: ' + error)
                });
            this.dataGrid.clearSelection();
            this.dataGrid.clearSorting();
            this.dataGrid.pageIndex(0);
        },
        refreshFechaDataGrid() {
            this.dataGridFecha.pageIndex(0);
            this.dataGridFecha.clearSelection();
            this.dataGridFecha.clearSorting();
            this.dataGridFecha.columnOption('Fecha', 'sortOrder', 'desc');
        },
        getFechasBalance() {
            if (this.SerieAdmision && this.CodigoAdmision)
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaFechaIngSolExc', {
                    Serie: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    TipoBusqueda: "Balance"
                })
                .then(resp => {
                    this.fechas = resp.data.json.map((x, index) => {
                        return {
                            id: index,
                            fecha: this.formatFechaFiltro(x.Fecha)
                        }
                    })
                })
            else {
                this.fechas = [];
                this.refreshFechaDataGrid();
            }
        },
        addRow() {
            if (this.StatusAdmision != 'A')
                this.$vs.notify({
                    time: 4000,
                    title: 'Antecedentes',
                    text: 'La admisión no está activa, no se puede agregar nuevo registro',
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'warning',
                    position: 'bottom-center'
                })
            else
                this.dataGrid.addRow();
        },
        formatFechaFiltro(fecha) {
            fecha = fecha.split('/');
            const year = fecha[2];
            const month = fecha[1];
            const day = fecha[0];

            return [month, day, year].join('/');
        },
        filtrarFecha(fecha) {
            fecha = fecha.split('/');
            const year = fecha[2];
            const month = fecha[0];
            const day = fecha[1];
            this.Cargar(false, day, month, year);
        },
        //Método para detectar cuando se selecciona una fila de la tabla de filtrado de fecha
        onSelectionChanged(e) {
            if (e.selectedRowsData.length > 0) {
                const data = e.selectedRowsData[0].fecha;
                this.filtrarFecha(data);
            }
        },
        onCellPrepared(e) {
            if (e.rowType === 'data' && e.data.Horario === '24 HRS') {
                e.cellElement.style.cssText = "background-color: beige; text-align: center; font-weight: bold; font-size: 16px;";
            }
            if (e.rowType === 'data' && e.column.dataField === 'Balance') {
                e.cellElement.style.cssText = "background-color: beige; text-align: center; font-weight: bold; font-size: 16px;";
                if (e.data.Balance < 0) {
                    e.cellElement.style.cssText = "color: white; background-color: red; text-align: center; font-weight: bold; font-size: 16px;"
                }
            }
        }
    },
    mounted() {
        this.Cargar(true);
    },
    watch: {
        'NumeroExpediente'(newval) {
            if (newval != undefined)
                this.Cargar(true)
        },
        'horarios'(hora) {
            if (hora.lenght !== 0) {
                this.selectHorarios = {
                    items: this.horarios,
                    searchEnabled: true,
                    valueExpr: 'value',
                    displayExpr: 'hora'
                }
            }
        }
    },
    computed: {
        ShouldRender() {
            return Boolean(this.SerieAdmision) && Boolean(this.CodigoAdmision) && Boolean(this.NumeroExpediente)
        },

        dataGrid: function () {
            return this.$refs[dataGridRefKey].instance;
        },
        dataGridFecha: function () {
            return this.$refs[dataGridFechaKey].instance;
        }
    },
}
</script>
