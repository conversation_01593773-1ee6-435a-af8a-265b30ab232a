<template>
<td :colspan="colspan" style="position:relative">
    <!-- <div style="position:relative;background-color:red;width:10px;overflow:hidden"> -->
        <span v-if="contenidoValido && noTooltip === 0" class="tooltip">
            <slot></slot>
        </span>
    <!-- </div> -->
    <div class="contenido" :class="[(noExcel!==0)?'noExcel':'']" ref="contenido">
        <slot></slot>
    </div>
</td>
</template>

<script>
export default {
    name: 'vs-td2',
    data() {
        return {
            contenidoValido: false
        }
    },
    props: {
        colspan: {
            default: 1
        },
        noTooltip: {
            default: 0
        },
        noExcel:{
            default:0
        }
    },
    computed: {},
    watch: {},
    components: {

    },
    methods: {

    },
    mounted() {
        const texto = this.$refs.contenido.innerText
        if (texto != 'check') this.contenidoValido = true
    }
}
</script>
