<template>
    <div>
        <vx-card title="Anulación de Facturas">
            <vs-divider border-style="solid" color="dark"></vs-divider>
            <div class="box-custom">
                <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                    <vs-row vs-justify="center">
                        <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="8">
                            <div class="flex flex-wrap-nowrap">
                                <vs-radio v-model="radioTipo" vs-name="radio1" vs-value="AF">Anulación Factura</vs-radio>
                                <vs-radio class="radio-tipo" v-model="radioTipo" vs-name="radio1" vs-value="AFCT">Anulación Factura Cafetería</vs-radio>
                                <vs-radio class="radio-tipo" v-model="radioTipo" vs-name="radio1" vs-value="AFC">Anulación Factura Clínica</vs-radio>
                            </div>
                        </vs-col>
                    </vs-row>
                    <br>
                    <vx-card>
                        <vs-row vs-justify="center">
                            <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="7">
                                <div class="flex flex-wrap-nowrap">
                                    <vs-radio v-model="radioReferencia" vs-name="radio2" vs-value="I">Factura Interna</vs-radio>
                                    <vs-radio class="radio-tipo" v-model="radioReferencia" vs-name="radio2" vs-value="F">Factura FEL</vs-radio>
                                </div>
                            </vs-col>
                        </vs-row>
                        <vs-row vs-justify="center">
                            <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="8">
                                <div class="flex flex-wrap-nowrap">
                                    <div class="div-input">
                                        <ValidationProvider name="serie" rules="required|customRule" class="required" v-slot="{ errors }">
                                            <label class="typo_label label-static">Serie</label>
                                            <vs-input class="w-full input-serie" v-model="serie" ref="inputSerie" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"></vs-input>
                                        </ValidationProvider>
                                    </div>
                                    <div class="div-input">
                                        <ValidationProvider name="numero" rules="required|numero_entero|ruleNumeroFactura" class="required" v-slot="{ errors }">
                                            <label class="typo_label label-static">Número</label>
                                            <vs-input type="number" class="w-full input-numero" ref="inputNumero" v-model="numero" v-on:keyup.enter="Validaciones" v-on:blur="Validaciones()" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="isNumeroFactura" ></vs-input>
                                        </ValidationProvider>
                                    </div>
                                    <div class="div-button">
                                        <vs-button @click="Validaciones()" color="primary" class="w-full" icon-pack="fas" icon="fa-search" >Buscar</vs-button>
                                    </div>
                                </div>
                            </vs-col>
                        </vs-row>
                        <vs-row vs-justify="center">
                            <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="10">
                                <div class="flex flex-wrap-nowrap">
                                    <div class="div-referencia">
                                        <label class="label-staticRef">Referencia:</label>
                                    </div>
                                    <div class="div-referencia">
                                        <input class="w-full label-info label-refSerie" v-model="refSerie" disabled />
                                    </div>
                                    <div class="div-referencia">
                                        <input type="number" class="w-full label-info label-refNnumero" v-model="refNumero" disabled />
                                    </div>
                                </div>
                                <div class="flex flex-wrap-nowrap">
                                    <div class="div-referencia">
                                        <label class="label-staticRef">Referencia Fel:</label>
                                    </div>
                                    <div class="div-referencia">
                                        <input class="w-full label-info label-refSerie" v-model="Fel_Serie" disabled />
                                    </div>
                                    <div class="div-referencia">
                                        <input type="number" class="w-full label-info label-refNnumero" v-model="Fel_Numero" disabled />
                                    </div>
                                </div>                                
                            </vs-col>
                        </vs-row>
                    </vx-card>
                    <br>
                    <vx-card>
                        <vs-row vs-justify="left">
                            <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="3">
                                <div class="div-input flex-wrap-nowrap">
                                    <label class="label-static">Nit:</label>
                                    <input class="w-full label-info" v-model="nit" disabled/>
                                </div>
                            </vs-col>
                            <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="3">
                                <div class="div-input flex-wrap-nowrap">
                                    <label class="label-static">Fecha:</label>
                                    <input class="w-full label-info" v-model="fecha" disabled/>
                                </div>
                            </vs-col>
                            <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="3">
                                <div class="div-input flex-wrap-nowrap">
                                    <label class="label-static">Corte Caja:</label>
                                    <input class="w-full label-info" v-model="corteCaja" disabled/>
                                </div>
                            </vs-col>
                        </vs-row>
                        <br>
                        <vs-row vs-justify="left">
                            <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="3">
                                <div class="div-input flex-wrap-nowrap">
                                    <label class="label-static">Serie: </label>
                                    <input class="w-full label-info" v-model="serieFac" disabled/>
                                </div>
                            </vs-col>
                            <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="3">
                                <div class="div-input flex-wrap-nowrap">
                                    <!-- <label class="label-static">Anulada: </label> -->
                                    <vs-checkbox class="label-static" v-model="anulada" disabled>Anulada</vs-checkbox>
                                    <!-- <input class="w-full label-info" v-model="serieFac" disabled/> -->
                                </div>
                            </vs-col>
                        </vs-row>
                        <br>
                        <vs-row vs-justify="left">
                            <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="5">
                                <div class="div-input flex-wrap-nowrap">
                                    <label class="label-static">Nombre: </label>
                                    <input class="w-full label-info" v-model="nombre" disabled/>
                                </div>
                            </vs-col>
                            <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="5">
                                <div class="div-input flex-wrap-nowrap">
                                    <label class="label-static">Total: </label>
                                    <input class="w-full label-info" v-model="GranTotal" disabled/>
                                </div>
                            </vs-col>                            
                        </vs-row>
                        <br>
                        <vs-row vs-justify="left">
                            <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="10">
                                <div class="flex-wrap-nowrap">
                                    <ValidationProvider name="razon" rules="required|max:40" class="required" v-slot="{ errors }">
                                        <label class="typo_label label-static">Razón</label>
                                        <vs-input ref="inputRazon" class="w-full input-razon" v-model="razon" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"></vs-input>
                                    </ValidationProvider>
                                </div>
                            </vs-col>
                        </vs-row>
                    </vx-card>
                    <br>
                    <vs-row vs-justify="center" class="w-full">
                        <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="10">
                            <div class="w-full flex flex-wrap-nowrap">
                                <div class="div-input">
                                    <vs-button @click="handleSubmit(AnularFactura)" :disabled="invalid" color="success" class="w-full" icon-pack="fas" icon="fa-check-circle">Anular</vs-button>
                                </div>
                            </div>        
                        </vs-col>
                    </vs-row>
                </ValidationObserver>
            </div>
        </vx-card>
    </div>
</template>
<script>
    import moment from 'moment'
    import { extend } from "vee-validate"
    export default {
        data() {
            return{
                serie: '',
                numero: '',
                refSerie: '',
                refNumero: '',
                nit: '',
                fecha: '',
                corteCaja: '',
                serieFac: '',
                anulada: false,
                nombre: '',
                razon: '',
                radioTipo: 'AF',
                isValidar: false,
                isNumeroFactura: true,
                radioReferencia: 'I',
                Fel_Serie: '',
                Fel_Numero: '',
                Total: 0,
                GranTotal: 0


            }
        },
        methods: {
            Validaciones() {
                if(this.isValidar){
                    this.axios.post("/app/v1_JefeCaja/ValidacionDocumentos", {
                        "Opcion": "C",
                        "SubOpcion": "3",
                        "Hospital": "0",
                        "SerieFactura": this.serie,
                        "NumeroFactura": this.numero,
                        "Tipo": this.radioTipo,
                        "TipoFactura": this.radioReferencia
                    })
                    .then(resp => {
                        if(resp.data.codigo == '0'){
                            const { Nit, Fecha, Corte, Serie, Status, Nombre, CodigoFactura, Fel_Serie, Fel_Numero,Total, GranTotal  } = resp.data
                            this.nit = Nit
                            this.fecha = moment(Fecha).format('DD/MM/YYYY')
                            this.corteCaja = Corte
                            this.serieFac = Serie 
                            this.anulada = Status == 'P' ? false : true
                            this.nombre = Nombre
                            this.refSerie = Serie
                            this.refNumero = CodigoFactura
                            this.Fel_Serie = Fel_Serie
                            this.Fel_Numero = Fel_Numero
                            this.Total = Total,
                            this.GranTotal = GranTotal


                            this.focusRazon()
                        }
                        

                    })
                    .catch(() => {
                        this.LimpiarCampos()
                        this.focusSerie()
                    })
                }
                
            },
            LimpiarCampos(){
                this.nit = ''
                this.fecha = ''
                this.corteCaja = ''
                this.serieFac = ''
                this.anulada = ''
                this.nombre = ''
                this.refSerie = ''
                this.refNumero = ''
                this.razon = ''
                this.Fel_Serie = ''
                this.Fel_Numero = ''
                this.Total = ''
                this.GranTotal = ''
                
            },
            AnularFactura() {
                
                this.axios.post("/app/v1_JefeCaja/AnularDocumento", {
                    "Opcion": "C",
                    "SubOpcion": "1",
                    "Hospital": "0",
                    "SerieFactura": this.refSerie,
                    "NumeroFactura": this.refNumero,
                    "TipoFactura": this.radioReferencia,
                    "Razon": this.razon,
                    "Tipo": this.radioTipo
                })
                .then(resp => {
                    if(resp.data.codigo == '0'){
                        this.axios.post("/app/v1_FacturaElectronica/AnulaFel", {
                            "Opcion": "C",
                            "SubOpcion": "1",
                            "Hospital": "0",
                            "SerieFactura": this.refSerie,
                            "NumeroFactura": this.refNumero,
                            "TipoFactura": this.radioReferencia,
                            "Razon": this.razon,
                            "Tipo": this.radioTipo,
                            "TipoOperacion": 0
                        }).then(() => {
                            this.LimpiarCampos()
                            this.focusSerie()
                        })
                    }
                })

            },
            focusSerie(){
                this.$refs.inputSerie.$el.querySelector('input').focus()
            },
            focusNumero(){
                this.$refs.inputNumero.$el.querySelector('input').focus()
            },
            focusRazon(){
                this.$refs.inputRazon.$el.querySelector('input').focus()
            },
            validaTipoSerie() {
                let errorMessage = ''
                extend("customRule", {
                    message: () => errorMessage,
                    validate: value => {
                        // if(this.radioTipo == 'AF' || this.radioTipo == 'AFCT'){
                            if(this.radioReferencia == 'F'){
                                if(value.length > 3){
                                    this.isNumeroFactura = false
                                    return true
                                }else{
                                    errorMessage = `La serie ingresada no pertenece a FEL ${value}`
                                    this.isNumeroFactura =  true
                                    return false
                                }
                            }else{
                                if(value.length <= 3){
                                    this.isNumeroFactura =  false
                                    return true
                                }else{
                                    errorMessage = `La serie ingresada no pertenece a interna ${value}`
                                    this.isNumeroFactura =  true
                                    return false
                                }
                            }
                            
          
                    }
                })
            },
            validaNumeroFactura() {
                let errorMessage = ''
                extend("ruleNumeroFactura", {
                    message: () => errorMessage,
                    validate: value => {
                        if(this.serie != '' || this.serie != null){
                            if(value != '' || value != null){
                                this.isValidar = true
                                return true
                            }else{
                                errorMessage = 'Debe de ingresar un numero de factura.'
                                return false
                            }
                        }
                        else{
                            errorMessage = `Debe de ingresar primero la serie de la factura.`
                            this.isValidar = false
                            return false
                        }
                        
                    }
                })
            }
        },
        watch: {
            serie(value){
                this.LimpiarCampos()
                if(value.length == 0){
                    this.isValidar = false
                }else{
                    if(this.numero != 0){
                        this.isValidar = true
                    }
                }
            },
            numero(value){
                this.LimpiarCampos()
                if(value.length == 0){
                    this.isValidar = false
                }
            },
            radioReferencia(value){
                this.LimpiarCampos()
                if(this.serie != null || this.serie != ''){
                    if(value == 'F'){
                        this.isValidar = this.serie.length > 3 ? true : false
                        this.serie.length > 3 ? this.focusNumero() : this.focusSerie()
                    }else{
                        this.isValidar = this.serie.length <= 3 ? true : false
                        this.serie.length <= 3 ? this.focusNumero() : this.focusSerie()
                    }
                }else{
                    this.isValidar = false
                }
            }
        },
        activated() {
            this.validaTipoSerie()
            this.validaNumeroFactura()
        }
    }
</script>
<style lang="scss" scoped>
    .box-custom{
        box-shadow: 0px 15px 40px -10px;
        padding: 15px;
    }
    .div-input{
        padding: 10px;
        .input-razon{
            width: 200px;
        }
    }
    .div-button{
        padding-top: 30px;
        padding-left: 10px;
    }

    .div-referencia{
        padding: 5px;
        .label-refNnumero{
            padding-left: 10px;
        }
        .label-refSerie{
            padding-left: 15px;
        }
    }
    .label-info{
        margin-left: 10px;
        color:#E74C3C;
        opacity: 0.7;
        border: 0;
        font-size: 16px;
        background-color: white;
        font-weight: bold;
    }
    .label-staticRef{
        font-weight: bold;
        padding-left: 15px;
    }
    .label-static{
        font-weight: bold;
        padding-left: 5px;
    }
    .radio-tipo{
        padding-left: 10px;
    }
    .checkboxAnula label {
        text-align: right;
        float: left;
    }
</style>