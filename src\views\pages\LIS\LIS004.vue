<template>
<div class="conf-nivel-critico-container">
    <vx-card title="Configuración Notificación Valores Críticos">

        <ValidationObserver ref="formValidate" v-slot="{ invalid,handleSubmit }" mode="lazy">
            <ValidationProvider name="Asunto" rules="required|min:3|max:200" v-slot="{ errors }" class="">
                <label class="vs-input--label ">Asunto correo</label>
                <vs-input class="w-full p-1" v-model="AsuntoCorreo" placeholder="Asunto Correo Electrónico" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" icon="subject" />
            </ValidationProvider>

            <label class="vs-input--label">Lista de correos electrónicos para notificación</label>
            <SMDestinatario class="p-1" v-model="Correos" placeholder="Ingrese los correos electrónicos" />

            <label class="vs-input--label">Lista de teléfonos moviles para notificación SMS</label>
            <SMDestinatario class="p-1" v-model="Telefonos" placeholder="Ingrese los números de teléfono para mensaje de texto" :regex="phoneRegularExpresion" />

            <label class="vs-input--label ">Puestos</label>
            <div class="con-chips w-full m-1">

                <div class="x-global con-chips--remove-all">
                    <font-awesome-icon :icon="['fas', 'trash']" @click="Puestos.splice(0, Puestos.length)" />
                </div>

                <vs-chip :key="chip.Id" @click="Puestos.splice(Puestos.indexOf(chip), 1)" v-for="chip in Puestos" closable>
                    <vs-avatar />
                    {{ chip.Puesto }}
                </vs-chip>

                <vs-select placeholder="Seleccione un puesto para agregar a la lista" autocomplete class="w-96 p-1 select-puesto" v-model="tempPuesto" no-data="No se encontaron coincidencias">
                    <vs-select-item :key="index" :value="item" :text="item.Puesto" v-for="(item,index) in PuestosDisponibles" />
                </vs-select>

            </div>

            <ValidationProvider name="Correo" rules="max:8000" v-slot="{ errors }" class="">
                <label class="vs-input--label ">Texto notifición por correo electrónico</label>
                <vs-input class="w-full p-1" v-model="TextoCorreo" placeholder="Texto notifición por correo electrónico" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" icon="mail" />
            </ValidationProvider>
            <ValidationProvider name="SMS" rules="max:250" v-slot="{ errors }" class="">
                <label class="vs-input--label ">Texto notifición por mensaje de texto</label>
                <vs-input class="w-full p-1" v-model="TextoSMS" placeholder="Texto notifición por mensaje de texto" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" icon="sms" />
            </ValidationProvider>

            <!-- Botones Sensuales -->
            <div class="flex bottom gap-2">
                <vs-spacer />
                <vs-button :disabled="invalid" color="success" submit @click="handleSubmit((e)=>Grabar(e))" icon-pack="fas" icon="fa-save"> Guardar</vs-button>
                <vs-button @click.native="CargarConfiguracion()">
                    <font-awesome-icon :icon="['fas', 'rotate-right']" />
                    Actualizar
                </vs-button>
            </div>
        </ValidationObserver>

    </vx-card>
</div>
</template>

<script>
import bitacora from '@/components/sermesa/funciones/SMBitacora'
export default {
    components: {
        SMDestinatario: () => import("@/components/sermesa/global/SMCorreo/SMDestinatario.vue")
    },
    data() {
        return {
            AsuntoCorreo: '',
            Correos: [],
            Telefonos: [],
            Puestos: [],
            Configuracion: [],
            TextoCorreo: '',
            TextoSMS: '',
            phoneRegularExpresion: new RegExp(/^[0-9]{8}$/),

            //catalogos
            CatalogoPuestos: null,
            tempPuesto: null,
            infoBitacora: null,
        }
    },
    created() {

    },
    beforeMount() {
        this.CargarPuestos()
    },
    mounted() {
        this.CargarPuestos().then( () =>
            this.CargarConfiguracion()
        )
    },
    computed: {
        PuestosDisponibles() {
            return this.CatalogoPuestos ? this.CatalogoPuestos.filter(x => !this.Puestos.includes(x)) : []
        }
    },
    watch: {
        'tempPuesto'(value) {
            if (value) {
                this.Puestos.push(value)
                this.$nextTick(() => {
                    this.tempPuesto = null
                    document.querySelector('.select-puesto input').focus()
                })

            }
        }
    },
    methods: {
        CargarConfiguracion() {
            this.Limpiar()
            this.axios.post('/app/laboratorio/ConfiguracionValorCritico', {}).then(resp => {
                if (resp.data.json) {
                    this.AsuntoCorreo = resp.data.json.AsuntoCorreo
                    this.Correos = resp.data.json.Correos
                    this.Telefonos = resp.data.json.Telefonos?.map(t => String(t))
                    this.Puestos = resp.data.json.Puestos?.map(p => this.CatalogoPuestos.find((cp) => cp.Id == p))
                    this.TextoCorreo = resp.data.json.TextoCorreo
                    this.TextoSMS = resp.data.json.TextoSMS

                    this.infoBitacora = resp.data.json
                } else {
                    this.infoBitacora = resp.data.json ?? {}
                }

                this.infoBitacora.tipobitacora = 'Tipo'
                this.infoBitacora.Agrupacion = resp.data.Agrupacion,
                    this.infoBitacora.Descripcion = resp.data.Descripcion,

                    bitacora.registrar(this.infoBitacora, {
                        Agrupacion: resp.data.Agrupacion,
                        Descripcion: resp.data.Descripcion,
                        llave: {
                            Agrupacion: resp.data.Agrupacion,
                            Descripcion: resp.data.Descripcion,
                        },
                        tabla: 'HisAgrupacionValores',
                    })
                this.infoBitacora.tipobitacora = 'Modificación'
            })
        },

        Grabar() {
            const postData = {
                AsuntoCorreo: this.AsuntoCorreo,
                Correos: this.Correos,
                Telefonos: this.Telefonos,
                Puestos: this.Puestos?.map(p => p.Id),
                TextoCorreo: this.TextoCorreo,
                TextoSMS: this.TextoSMS,
            }
            Object.assign(this.infoBitacora, postData)
            this.axios.post('/app/laboratorio/GrabarConfiguracionValorCritico', postData)
                .then(() => {
                    const bita = JSON.parse(bitacora.obtener())
                    bita.info = JSON.stringify(bita.info)//info se debe enviar como texto
                    this.axios.post('/app/bitacora/registro_bitacora', bita)
                })
                .finally(() => this.CargarConfiguracion())
        },
        CargarPuestos() {
            return new Promise((res,rej)=>{
                    this.axios.post('/app/administracion/BusquedaCorporativosPuestos', {}).then((resp => {
                    this.CatalogoPuestos = resp.data.json
                    res(resp.data.json)
                })).catch(err=> rej(err))
            })
        },
        Limpiar() {
            this.AsuntoCorreo = ''
            this.Correos = []
            this.Telefonos = []
            this.Puestos = []
            this.TextoCorreo = ''
            this.TextoSMS = ''
            this.infoBitacora = null
        },
    },
}
</script>

<style scoped>
.con-chips {
    justify-content: flex-end;
    flex-direction: row-reverse;
    min-height: 50px;
}

.con-chips>div {
    margin: 5px;
}

.vs-input--label {
    font-weight: 600;
}
</style>
