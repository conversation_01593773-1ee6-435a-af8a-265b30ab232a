<template lang="html">
<vx-card title="Pedido Sugerido Cocina">
    <vs-row>
        <vs-col vs-justify="center" vs-align="center" vs-w="5" id="columna2">            
            <p>
                <b>Seleccione bodega Fuente/Madre:</b>
            </p>
            <multiselect v-model="bodega" :options="ListadoBodegas" :custom-label="Bodegas_seleccionado"  :disabled="true" :allow-empty="false" placeholder="Seleccione una bodega (Requerido)" ></multiselect>
            <p>
                <b>Seleccione bodega Destino/SubBodega:</b>
            </p>
            <multiselect v-model="subbodega" :custom-label="Bodegas_seleccionado" :showLabels="false" @input="onChangeSubBodega" :options="ListadoSubBodegas" :allow-empty="false" placeholder="Seleccione una subbodega (Requerido)"  class="zy" z-index="105" ></multiselect>
        </vs-col>
        <vs-col vs-justify="center" vs-align="center" vs-w="4" id="columna2">
            <P>
                <b>Grupo:</b>
            </P>
            <multiselect v-model="grupo" :options="ListadoGrupos" :allow-empty="false" :showLabels="false" placeholder="Seleccione un Grupo (Requerido)" track-by="Nombre" label="Nombre" @input="cargar_subgrupos"></multiselect>

            <P>
                <b>SubGrupo:</b>                                                              
            </P>
            <multiselect v-model="subgrupo" :options="ListadoSubGrupos" :allow-empty="false" :showLabels="false"  placeholder="Seleccione un SubGrupo (Requerido)" track-by="Nombre" label="Nombre">
                <template #noOptions>
                    <p>Elegir un grupo primero</p>
                </template>                
            </multiselect>
        </vs-col>
    </vs-row>
    <vs-row>
        <vs-col vs-justify="center" vs-align="center" vs-w="5" id="columna2">
            <vs-textarea label="Descripción:" v-model="descripcion" />
        </vs-col>
        <vs-col vs-justify="center" vs-align="center" vs-w="6" id="columnaTipoB">
            <vs-row>                
                <vs-button class="m-3" :disabled="BotonDesabilitarGenerarPedido" color="green" type="filled" @click="GenerarPedido" icon="cached">Generar Pedido</vs-button>
                
                <vs-button class="m-3" :disabled="ListadoPedido.length==0 || this.bodega.length == 0 || this.subbodega.length  == 0 || this.grupo.length == 0  || this.subgrupo.length == 0"  @click="GuardarPedido">Guardar Pedido</vs-button>       
           
                <vs-button class="m-3" @click="NuevoPedido" type="filled" icon="create">NuevoPedido</vs-button>
            </vs-row>
        </vs-col>
    </vs-row>
    <vs-row>
        <vs-col vs-justify="center" vs-align="center" vs-w="12" id="columna2">
            <vs-divider>Listado de Productos Pedido Sugerido</vs-divider>
            <template lang="html">
                <div>
                    <vs-table max-items="15" search pagination :data="ListadoPedido" noDataText="Sin datos disponibles" id="zx" >
                        <template slot="thead">
                            <vs-th>No.</vs-th>
                            <vs-th>Producto</vs-th>
                            <vs-th>NombreProducto</vs-th>
                            <vs-th>UMedida</vs-th>
                            <vs-th color="warning">Promedio</vs-th>
                            <vs-th color="warning">Existencia</vs-th>
                            <vs-th color="warning">Pedido Semanal</vs-th>
                        </template>

                        <template slot-scope="{data}">
                            <vs-tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td :data="indextr+1">{{indextr+1}}</vs-td>
                                <vs-td :data="tr.codigoproducto">{{tr.codigoproducto}}</vs-td>
                                <vs-td :data="tr.consumible">{{tr.consumible}}</vs-td>
                                <vs-td :data="tr.unidaddemedida">{{tr.unidaddemedida}}</vs-td>
                                <vs-td :data="tr.promediosemanal">{{tr.promediosemanal}}</vs-td>
                                <vs-td :data="tr.existencia">{{tr.existencia}}</vs-td>
                                <vs-td :data="tr.pedidosemanal" color="warning">
                                    <vs-input @blur="()=>{if(tr.pedidosemanal.length == 0 || tr.pedidosemanal < 0)tr.pedidosemanal=0;}" v-model="tr.pedidosemanal" class="inputx" type="number"  style="width:75px" />
                                </vs-td>
                            </vs-tr>
                        </template>
                    </vs-table>
                </div>
            </template>

        </vs-col>
    </vs-row>
</vx-card>
</template>

<script>
import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";
export default {
    data() {
        return {
            i: 0,
            ClaseBodega: false,
            ClaseBodegaAPI: "S",
            BodegaAPI: 0,
            ListadoBodegas: [],
            ListadoSubBodegas: [],
            ListadoGrupos: [],
            ListadoSubGrupos: [],
            bodega: [],
            grupo: [],
            subgrupo: [],
            no_bodega: null,
            subbodega: [],
            TipoBodega: [],
            descripcion: "",
            ListadoPedido: [],
            ListadoPedidoUnitario: [],
            popupActivo: false,
            CodigoProducto: null,
            NombreProducto: null,
            GCorrelativoPedidoSugerido: null,
            GCorrelativoMovimiento: null,
            guardadoconexito: false
        };
    },
    methods: {
        limpiarPantalla(){
            this.i = 0
            this.ClaseBodega= false
            this.ClaseBodegaAPI= "S"
            this.BodegaAPI= 0
            this.bodega= []
            this.grupo= []
            this.subgrupo= []
            this.no_bodega= null
            this.subbodega= []
            this.TipoBodega= []
            this.descripcion= ""
            this.ListadoPedido= []
            this.ListadoPedidoUnitario= []
            this.popupActivo= false
            this.CodigoProducto= null
            this.NombreProducto= null
            this.GCorrelativoPedidoSugerido= null
            this.GCorrelativoMovimiento= null
            this.guardadoconexito= false
        },
        onChangeSubBodega(bodega){
            if(!bodega){
                this.bodega = {}
            }else{
                if(!this.ListadoBodegas && this.ListadoBodegas.length <= 0){
                    this.$vs.notify({
                        color:'alert',
                        title:'Pedido',
                        text: 'No se cargaron bodegas de despacho'
                    })
                    return
                }
                this.bodega = this.ListadoBodegas.find(bod=> bod.TipoBodega = bodega.TipoBodega)
            }
        },
        Bodegas_seleccionado(bodega) {
            return `${bodega.CODIGO}-${bodega.NOMBRE} `;
        },
        Consultar_Bodega(Operacion, BodegasDespacho, BodegaTransito) {  
            const url = this.$store.state.global.url          
            this.axios.post(url+'app/v1_OrdenCompra/consulta_bodega', {
                                                                    operacion: Operacion,
                                                                    BodegasDepacho: BodegasDespacho,
                                                                    BodegaTransito: BodegaTransito
                                                                    })
                        .then(resp => {
        
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Bodegas',
                                    text: resp.data.mensaje,
                                })
                            } else {
                                if(Operacion == 'F'){
                                    this.ListadoBodegas = resp.data.json;                         
                                }
                                else if(Operacion == 'H'){
                                    this.ListadoSubBodegas = resp.data.json;                          
                                }
                                    
                            }
                        })
                        .catch(() => { })
    
        },
        cargar_grupos() {
            this.axios
                .post("/app/v1_OrdenCompra/ConsultaGrupo", {sub_grupo:'N'})
                .then(resp => {
                    this.ListadoGrupos = resp.data.json;
                })
                .catch(() => { })
        }, //Fin de metodo cargar grupos
        cargar_subgrupos() {
            this.subgrupo = []
            this.axios
                .post("/app/v1_OrdenCompra/ConsultaGrupo", {
                    sub_grupo:'S',
                    codigo_grupo: this.grupo.Codigo
                })
                .then(resp => {
                    this.ListadoSubGrupos = resp.data.json;
                    this.ListadoPedido = [];
                })
                .catch(()=>{});
        }, //Fin de metodo cargar subgrupos
        Deshabilitar_Bodega_Destino() {
            (this.subgrupo = []), (this.ListadoSubGrupos = []);
        }, //Fin de metodo cargar subgrupos
        GenerarPedido() {
            var f = new Date();
            this.descripcion =
                "Pedido Sugerido de la subbodega " + this.subbodega.CODIGO + '-' + this.subbodega.NOMBRE +
                " el dia " +  (f.getDate() + "/" + (f.getMonth() + 1) + "/" + f.getFullYear()) +
                " para el grupo: " + this.grupo.Nombre.trim()+ " y el subgrupo: "+ this.subgrupo.Nombre.trim();

            this.ListadoPedido = [];

            this.axios.post("/app/v1_OrdenCompra/ListadoPedidoSugerido", 
                {
                    operacion: "C",                    
                    clase_bodega: "S",
                    bodega_destino: this.subbodega.CODIGO,
                    codigo_grupo: this.grupo.Codigo,
                    sub_grupo: this.subgrupo.Codigo
                })
                .then(resp => {
                    this.ListadoPedido = resp.data.json;                
                })
                .catch(()=>{});
        }, //Fin de metodo GenerarPedido
        GuardarPedido() {     
            var pedidoProductos = this.ListadoPedido.filter(producto=>producto.pedidosemanal>0)  
            if(pedidoProductos.length<=0){
                this.$vs.dialog({
                        type: 'confirm',
                        color: 'danger',
                        title: 'Confirmación',
                        button: {
                            yes: 'Aceptar',
                            no: 'Cancelar'
        
                        },
                        text: `Debe ingresar una cantidad mayor a cero en el pedido semanal`                       
                    })
                return
            }


            this.axios
                .post("/app/v1_OrdenCompra/ControlPedidoSugerido", {Opcion:'I',   
                                                                    SubOpcion:'P',
                                                                    ClaseBodega: 'S',
                                                                    BodegaFuente:this.bodega.CODIGO,
                                                                    BodegaDestino:this.subbodega.CODIGO,
                                                                    TipoPs:'C',
                                                                    Descripcion: this.descripcion,
                                                                    pedido: pedidoProductos,
                                                                    CodigoGrupo: this.grupo.Codigo,
                                                                    CodigoSubGrupo: this.subgrupo.Codigo,
                                                                    PedidosBusqueda:[]                                                             
                                                                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color:'alert',
                            title:'Inventario',
                            text:resp.data.descripcion
                        })
                    }else{
                        this.$vs.dialog({
                            type: 'confirm',
                            color: 'primary',
                            title: 'Confirmación',   
                            acceptText: 'Aceptar',
                            cancelText: 'Cancelar',                 
                            text: resp.data.descripcion                      
                        })
                        this.limpiarPantalla();
                    } 
                })
                .catch(()=>{});
        }, //Fin de metodo GuardarPedido
        AgregarProducto() {
            if (!this.ClaseBodega) {
                //es subbodega
                this.BodegaAPI = this.subbodega.Codigo;
            } else {
                //es bodega madre
                this.BodegaAPI = this.bodega.Codigo;
            }
            if (this.subgrupo.length <= 0) this.subgrupo.Codigo = 0;

            this.axios
                .post("/app/requerimientos/ListadoPedidoSugerido", {
                    Operacion: "s",
                    ClaseBodega: this.ClaseBodegaAPI,
                    Bodega: this.BodegaAPI,
                    CodigoProducto: this.CodigoProducto
                })
                .then(resp => {
                    this.ListadoPedidoUnitario = resp.data.json;
                    this.ListadoPedido.push(...this.ListadoPedidoUnitario);
                })
                .catch(()=>{});
        }, //Fin de metodo AgregarProducto = GenerarPedido INDIVIDUAL
        RestarTransito() {
            this.ListadoPedido.forEach(element => {
                if (element.PedidoSugerido - element.Transito > 0)
                    element.PedidoCantidad = element.PedidoSugerido - element.Transito;
            });
        },
        NuevoPedido() {
            this.limpiarPantalla();
        },
        buscar_producto() {
            this.$refs.buscar_producto.iniciar(data => {
                if (data != null) {
                    this.CodigoProducto = data.Codigo;
                    this.NombreProducto = data.Nombre;
                }
            });
        },
        CambiarClaseBodega() {
            if (!this.ClaseBodega) {
                this.ClaseBodegaAPI = "S";
                this.subbodega = [];
            } else {
                this.ClaseBodegaAPI = "M"; //es bodega madre
                this.subbodega = this.bodega;
            }
        }
    },
    created() {},
    components: {
        Multiselect
    },
    mounted() {
        this.Consultar_Bodega('H','C','');
        this.Consultar_Bodega('F','C','');
        this.cargar_grupos();
    },
    computed: {
        BotonDesabilitarGenerarPedido: function() {
            if (
                this.bodega.length == 0 ||
                this.subbodega.length == 0 ||
                this.grupo.length == 0 ||
                this.subgrupo.length == 0 
            )
                return true;
            else return false;
        }
    }
};
</script>

<style>
#columna2 {
    padding: 10px;
}

#tipobodega {
    padding-top: 10px;
}

#columnaTipoB {
    padding-left: 35px;
}

.vs-table--tbody {
    z-index: 1;
}
</style><style lang="stylus">
.popup-example {
    .vs-input {
        float: left;
        width: 50%;
        margin: 10px;
        margin-top: 5px;
    }

    .con-select {
        margin-left: 10px;
        width: 50%;
        margin-bottom: 10px;
    }

}
</style>
