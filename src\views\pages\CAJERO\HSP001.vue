<template>
    <div class="principal">
        <vs-popup title="Impresión de recibo" :active.sync="VentanaImpresionRecibo" style="z-index:99999" id="4">
            <div class="flex flex-wrap">

                <div class="sm:w-full md:w-full lg:w-full xl:w-full"><b>Recibo:</b> {{ serieRecibo+' '+numeroRecibo }}
                </div>
                <br>
                
                <div class="flex w-full">
                    <vs-checkbox v-model="reimpresion">Impresión media carta</vs-checkbox>
                </div>
                
                <br>
                <br>
                <div class="flex w-full ">
                    <vs-button  class="mr-4" color="green" style="float: right" type="filled" @click="GenerarRecibo(serieRecibo,numeroRecibo)">
                        <i class="fas fa-file-pdf "></i>
                        {{"PDF"}}
                    </vs-button>                    

                    <vs-button color="primary" style="float: right" type="filled" icon-pack="feather" icon="icon-save" @click="VentanaImpresionRecibo=false; serieRecibo = ''; numeroRecibo = ''; reimpresion=false;">
                        Cerrar
                    </vs-button>
                </div>
                <br><br>
            </div>
        </vs-popup>
      <vs-tabs v-model="activeTab" alignment="center">
        <vs-tab label="Facturación" @click="actualizaBusquedaAdmisionFactura">
          <div>
              <vx-card :title="`Caja Facturación ${sesion.sesion_sucursal_nombre}`">    
                  <vs-row>
                          <vs-row class="w-full" v-if="activeTab==0">
                                <ConfiguracionCaja
                                             ref="ConfiguracionCajaFactura"
                                             @CargarCaja="CargarCaja"
                                             TipoCajaSeleccionada="TipoCajaFacturacion" 
                                             CajaSeleccionada="CajaFacturacion"
                                             class="w-full">
                                </ConfiguracionCaja>
                          </vs-row>                          
                          <vs-row class="w-full mb-4">
                              <div style="border: black;">
                                    <DxRadioGroup   :items="caja.tiposFacturacion"
                                                    :display-expr="(e)=>e.Descripcion+' ('+e.Tipo+')'"
                                                    value-expr="Tipo"
                                                    v-model="caja.tipoFacturacionSeleccionada"
                                                    layout="horizontal"
                                    /> 
                              </div>
                          </vs-row>
                          <vs-row class="sm:w-full md:w-full lg:w-full xl:w-full">
                            <vs-row class="w-full">
                                <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-3/12 pr-2">
                                    <div><b>Tipo Admisión:</b> {{ tipoAdmision }}</div>
                                </vs-col>        
                                <vs-col class="sm:w-full md:w-full lg:w-8/12 xl:w-8/12">
                                    <div v-if="info.seguro !== undefined && info.seguro?.trim()?.length > 0">
                                        <p><b>Seguro: </b> {{ info.seguro + ' - ' + info.nombreSeguro }}</p>
                                    </div>
                                </vs-col> 
                            </vs-row>
                            <vs-row class="w-full"> 
                                <vs-col class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12">
                                    <BusquedaAdmision  class="w-full"  ref="componenteAdmisionesF"   
                                            @datos_admision="DatosAdmisionF"
                                            size = "media-fila"
                                            @limpiar_datos_admision="LimpiarDatosCliente"
                                            :api_campos="['Serie','Codigo','Nombres','Apellidos','Habitacion']"
                                            :api_titulos="['Serie#','Admision#','Nombres','Apellidos','Habitación']"
                                            :api_filtro="{'Opcion':'CONSULTA','SubOpcion':'CAJERO','activo':0,'tieneHabitacion':0}"
                                            >                
                                    </BusquedaAdmision>
                                </vs-col>
                                <vs-col class="sm:w-full md:w-full lg:w-6/12 xl:w-2/12 p-1">
                                    <vs-input label="E-Mail"   
                                            class="w-full" 
                                            v-model="documento.correo" />      
                                </vs-col>  
                                <vs-col v-if="caja.tipoFacturacionSeleccionada=='3'" class="sm:w-full md:w-full lg:w-6/12 xl:w-2/12 p-1">
                                    <vs-input label="No. autorización"   
                                            class="w-full" 
                                            v-model="NoAutorizacionSeguro" 
                                            maxlength="18"
                                            />      
                                </vs-col> 
                                <vs-col v-if="caja.tipoFacturacionSeleccionada=='3'" class="sm:w-full md:w-full lg:w-6/12 xl:w-2/12 p-1">
                                    <vs-input label="Póliza"   
                                            class="w-full" 
                                            v-model="NoPoliza" 
                                            maxlength="18"
                                            />      
                                </vs-col> 
                            </vs-row> 
                            <vs-row class="w-full" v-if="!facturacion.habilitarCamposTipo3">
                                <vs-divider position="left">Factura para el paciente</vs-divider>
                                <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-1/12 p-1" style="height: 60px;">   
                                    <label class="typo__label label-shrink" style="white-space: nowrap; color:#626262; font-size:12px;  padding-bottom:2px; padding-top:0px;"> Tipo Documento  </label>  
                                    <DxSelectBox            
                                        style="padding: 2px;"
                                        :items="documento.tiposDocumentoLista"
                                        display-expr="Descripcion"
                                        placeholder="Seleccionar.."
                                        v-model="documento.tipoDocumento"
                                    />    
                                </vs-col>              
                                <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 p-1">
                                    <vs-input label="No. documento" 
                                            v-model="documento.noDocumento" 
                                            class="w-full"
                                            @keydown.enter="ValidacionDocumentoTributarioTab()" @keydown.tab.prevent="ValidacionDocumentoTributarioTab()"
                                            />      
                                </vs-col>  
                                <vs-col class="sm:w-full md:w-full lg:w-6/12 xl:w-3/12 p-1">
                                    <vs-input id="idNombre"
                                            label="Nombre"  
                                            class="w-full"
                                            v-model="documento.nombre" />      
                                </vs-col>                                  
                                <vs-col class="sm:w-full md:w-full lg:w-full xl:w-3/12 p-1">
                                    <vs-input label="Dirección"   
                                            class="w-full" 
                                            v-model="documento.direccion" />      
                                </vs-col>                 
                            </vs-row>  
                            <vs-row class="w-full"  v-if="facturacion.habilitarCamposTipo2">                                
                                <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-1/12 p-1">                                          
                                    <label for="CampoCopago1" class="typo__label label-shrink" style="white-space: nowrap; color:#626262; font-size:12px;  padding-bottom:2px; padding-top:0px;">Copago a Facturar</label>                                    
                                    <DxNumberBox id="CampoCopago1" style="border: 1px solid #ccc !important;" class="w-full money-input p-0"  v-model="facturacion.copagoFacturar" format="Q #,##0.00" />
                                </vs-col>
                                <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-1/12 p-1">                                          
                                    <label for="CampoCoaseguro1" class="typo__label label-shrink" style="white-space: nowrap; color:#626262; font-size:12px;  padding-bottom:2px; padding-top:0px;">Coaseguro % A Facturar</label>
                                    <vs-input id="CampoCoaseguro1" class="w-full" type="number" v-model="facturacion.coaseguroPorcentajeFacturar" name="coaseguro_porcentaje" :max="100.00" :min="0.00"/>   
                                </vs-col> 
                            </vs-row>
                            <vs-row class="w-full" v-if="facturacion.habilitarCamposTipo13">
                                <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-1/12 p-1">
                                    <label class="w-full">Fecha Factura:</label>
                                    <vs-input class="w-full" type="date" v-model="facturacion.fechaFacturacion" name="fecha_facturacion" />
                                </vs-col>
                                <vs-col class="sm:w-full md:w-full lg:w-10/12 xl:w-8/12 p-1">
                                    <label class="w-full">Descripción Variable</label>
                                    <vs-textarea rows="4" counter="80" type="text" class="w-full" v-model="facturacion.descripcionVariable"/>
                                </vs-col>
                            </vs-row>                                                                                  
                          </vs-row>     
                          <vs-row class="sm:w-full md:w-full lg:w-full xl:w-full" v-if="facturacion.habilitarCamposTipo1 || facturacion.habilitarCamposTipo2 ||  facturacion.habilitarCamposTipo3">
                              <div><b>Factura para la aseguradora:</b> </div>
                          </vs-row>
                          <vs-row class="w-full sm:p-0 md:p-0 lg:pl-2 xl:pl-4" v-if="facturacion.habilitarCamposTipo1 || facturacion.habilitarCamposTipo2 ||  facturacion.habilitarCamposTipo3">
                              <label style="white-space: nowrap; color:#626262; font-size:14px;">Nombre: </label>         &nbsp;&nbsp;
                                  <b style="white-space: nowrap; color:black; font-size:14px;">{{ seguro.nombre }}</b>    &nbsp;&nbsp;
                              <label style="white-space: nowrap; color:#626262; font-size:14px;">Nit: </label>            &nbsp;&nbsp;
                                  <b style="white-space: nowrap; color:black; font-size:14px;">{{ seguro.noDocumento }}</b> &nbsp;&nbsp;                                    
                              <label style="white-space: nowrap; color:#626262; font-size:14px;">Dirección: &nbsp;&nbsp;<b style="white-space: nowrap; color:black; font-size:14px;">{{ seguro.direccion }}</b></label>        
                                  
                          </vs-row> 
                          <vs-row  v-if="facturacion.habilitarCamposTipo2 || facturacion.habilitarCamposTipo3" class="w-full sm:p-0 md:p-0 lg:pl-2 xl:pl-4">
                              <label style="white-space: nowrap; color:#626262; font-size:14px;">Copago en la admisión: </label>      &nbsp;&nbsp;
                                  <b style="white-space: nowrap; color:black; font-size:14px;">{{ $formato_moneda(info.copago) }}</b> &nbsp;&nbsp;
                              <label style="white-space: nowrap; color:#626262; font-size:14px;">Coaseguro en la admisión: </label>   &nbsp;&nbsp;
                                  <b style="white-space: nowrap; color:black; font-size:14px;">{{ $formato_moneda(info.coaseguro) }}</b>
                          </vs-row>
                          <vs-row class="w-full" v-if="facturacion.habilitarCamposTipo0 || facturacion.habilitarCamposTipo13">                                        
                            <vs-divider position="left">Facturas y Ajenos</vs-divider>                               
                            <vs-row class="w-full">
                                <vs-col class="sm:w-full md:w-full lg:w-5/12 xl:w-3/12 p-1" >
                                    <DxDataGrid v-bind="DefaultDxGridConfiguration"         
                                                        :data-source="facturas" :height="250" >
                                        <!-- <DxDataGridColumn :width="200" data-field="Material" :disabled="consulta" caption="" alignment="center" :customize-text="customizeText" /> -->
                                        <DxDataGridColumn width="150px" data-field="Factura" alignment="center" :calculate-display-value="(rowData)=>rowData.SerieFactura+' '+rowData.Factura" />
                                        <DxDataGridColumn width="100px" data-field="Saldo" caption="Saldo" format="Q ##,##0.00" data-type="number" alignment="right" />
                                        <DxDataGridSummary>
                                            <DxDataGridTotalItem  column="Saldo" summary-type="sum"  value-format="Q ##,##0.00" data-type="number" display-format="Saldo Hospital: {0}"/>
                                        </DxDataGridSummary>   
                                    </DxDataGrid>
                                </vs-col>
                                <vs-col class="sm:w-full md:w-full lg:w-5/12 xl:w-4/12 p-1">
                                    <DxDataGrid v-bind="DefaultDxGridConfiguration"                                                     
                                                        :data-source="cargosAjenos" :height="250" >
                                        <DxDataGridColumn width="150px" css-class="fuente-label"  data-field="Medico/Empresa" caption="Médico/Empresa" alignment="center" />
                                        <DxDataGridColumn width="150px" caption="Saldo"  data-field="Saldo" format="Q ##,##0.00" data-type="number" alignment="right" />
                                        <DxDataGridSummary>                                                                
                                            <DxDataGridTotalItem  column="Saldo"  data-type="text" style="font-size: 10pt;" :customize-text="()=>{return 'Saldo Ajenos: ' + $formato_moneda(cargosAjenosDetalle.saldoAjenos)}" />                                                    
                                            <DxDataGridTotalItem  column="Saldo"  data-type="text" style="font-size: 10pt;" :customize-text="()=>{return 'Saldo Ajenos Extra: ' + $formato_moneda(cargosAjenosDetalle.saldoAjenosExtra)}" />                                                   
                                        </DxDataGridSummary>   
                                    </DxDataGrid>
                                </vs-col>
                                <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1" style="min-width: 150px; border: 1px solid #ddd; color: black; font-weight: bold; white-space: nowrap;">
                                    <div class="w-full">
                                        <vs-col class="w-1/2" style="text-align: left">
                                            <div class="pt-4">Hospital:</div>
                                            <div class="pt-2">Cuenta:</div>
                                            <div class="pt-2">AjenosExtra:</div>
                                            <div class="pt-2">- Abonos:</div>
                                            <vs-divider class="p-0 m-0"></vs-divider>
                                            <div >A Pagar:</div>                                                                
                                        </vs-col>
                                        <vs-col class="w-1/2" style="text-align: right">
                                            <div class="pt-4">{{ $formato_moneda(facturasDetalle.hospital) }}</div>
                                            <div class="pt-2">{{ $formato_moneda(cargosAjenosDetalle.cuentaAjena) }}</div>
                                            <div class="pt-2">{{ $formato_moneda(cargosAjenosDetalle.ajenosExtra) }}</div>
                                            <div class="pt-2">{{ $formato_moneda(facturasDetalle.abonos) }}</div>
                                            <vs-divider class="p-0 m-0"></vs-divider>
                                            <div >{{ $formato_moneda(totalPago.sumatoriaPago) }}</div>                                                                
                                        </vs-col>
                                    </div>                                                   
                                </vs-col> 
                            </vs-row>                                     
                          </vs-row>                       
                          <vs-row class = 'w-full pt-5'>
                              <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 m-1">
                                  <vs-button color="success" class="w-full" :type="facturaSeleccionado?'border':'filled'" :disabled="!configuracion.activarFacturacion" id="botonFacturar"
                                      @keyup.tab="facturaSeleccionado=true"
                                       v-on:blur="facturaSeleccionado=false"
                                      @click="Guardar().Facturar()">
                                      Facturar
                                  </vs-button>
                              </vs-col>
                              <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 m-1">
                                  <vs-button color="warning" class="w-full"
                                      @click="LimpiarAdmisionesF()">
                                      Limpiar
                                  </vs-button>
                              </vs-col>
                          </vs-row>        
                          <!-- CARGOS AGREGADOS -->                                                  
                  </vs-row>        
              </vx-card>
          </div>
        </vs-tab>
        <vs-tab label="Recibos" @click="actualizaBusquedaAdmisionRecibo">
          <div>
              <vx-card :title="`Recibos - ${sesion.sesion_sucursal_nombre}`">    
                    <vs-row>
                            <vs-row class="w-full" v-if="activeTab==1">
                                <ConfiguracionCaja 
                                                ref="ConfiguracionCajaRecibo"
                                                @CargarCaja="CargarCaja"
                                                TipoCajaSeleccionada="TipoCajaFacturacion" CajaSeleccionada="CajaFacturacion" 
                                                class="w-full">
                                </ConfiguracionCaja>
                            </vs-row>
                            <vs-row class="w-full" style="min-height:0px">
                                <vs-row class="w-full">
                                    <vs-row class="sm:w-full md:w-full lg:w-full xl:w-1/2 vs-row-especial">
                                        <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1" style="height: 60px;">
                                            <label class="typo__label label-shrink" style="white-space: nowrap; color:#626262; font-size:12px;  padding-bottom:2px; padding-top:0px;"> Tipo Recibo  </label>  
                                            <DxSelectBox            
                                                style="padding: 2px;"
                                                :items="tipoReciboSelected"
                                                display-expr="Descripcion"
                                                placeholder="Seleccionar.."
                                                value-expr="Valor"
                                                v-model="sourceRecibos.tipoRecibo"
                                                :disabled="reciboPorFactura || sourceRecibos.recibos.length > 0"
                                            />
                                        </vs-col>
                                        <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1 flex-col justify-center text-center" style="text-align:center; color:gray; font-size:11pt; justify-content:center;">   
                                            <label  class="typo__label">Recibo por Factura</label>    
                                            <div class="w-full justify-center text-center" style=" display: flex; text-align:center; align-items:center; align-self: center; justify-content: center;">
                                                <vs-switch style="text-align:center; align-self: center;" v-model="reciboPorFactura" :disabled="!ValidarVariable(info.numeroAdmision)"/>                  
                                            </div>
                                        </vs-col>        
                                        <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-4/12 p-1">
                                            <vs-input label="Serie" 
                                                    v-model="sourceRecibos.serieFactura" 
                                                    class="w-full"
                                                    :disabled="true"
                                                    />   
                                        </vs-col>  
                                        <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-4/12 p-1">  
                                                    <SM-Buscar ref="BusquedaFacturaRecibo" v-model="sourceRecibos.numeroFactura" label="No Factura" 
                                                    :callback_buscar="(factura)=>{sourceRecibos.serieFactura = factura.Serie
                                                                                  sourceRecibos.saldoFactura = factura.Saldo
                                                                                 }" 
                                                    :api="sourceRecibos.facturasPorAdmision" :api_cache="true" 
                                                    :api_campos="['Serie', 'Codigo', 'Total','Saldo']" 
                                                    :api_titulos="['Serie', 'Codigo', '#Total','#Saldo']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Codigo" 
                                                    :api_preload="true" :disabled_texto="true" 
                                                    :disabled_busqueda="!reciboPorFactura"
                                                    />
                                        </vs-col>
                                    </vs-row>
                                    <vs-row class="w-full"> 
                                        <vs-col class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12">
                                            <BusquedaAdmision  class="w-full"  ref="componenteAdmisionesR"   
                                                    @datos_admision="DatosAdmisionR"
                                                    @limpiar_datos_admision="LimpiarDatosCliente"
                                                    size = "media-fila"
                                                    :api_campos="['Serie','Codigo','Nombres','Apellidos','Habitacion']"
                                                    :api_titulos="['Serie#','Admision#','Nombres','Apellidos','Habitación']"
                                                    :api_filtro="{'Opcion':'CONSULTA','SubOpcion':'CAJERO','activo':0,'tieneHabitacion':0}"
                                                    >                
                                            </BusquedaAdmision>
                                        </vs-col>
                                        <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-3/12 p-1">
                                            <vs-input label="Nombre"  
                                                    class="w-full"
                                                    v-model="documento.nombre" />      
                                        </vs-col>                                       
                                        <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-3/12 p-1">
                                            <vs-input label="Dirección"   
                                                    class="w-full" 
                                                    v-model="documento.direccion" />      
                                        </vs-col>                                                                                
                                    </vs-row>                                                
                                </vs-row>                                
                            </vs-row>
                            <vs-row class="pt-3 pb-2">
                                <DxDataGrid ref="recibosFacturacionRef" 
                                            v-bind="DefaultDxGridConfiguration"                                                                                    
                                            :hoverStateEnabled="false"
                                            :data-source="sourceRecibos.recibos" :visible="true"  
                                            :showColumnLines="true" :showBorders="true" 
                                            :rowAlternationEnabled="true" width="100%" height="250px" 
                                            :column-hiding-enabled="true" 
                                             @init-new-row="nuevoRecibo"
                                             @editor-preparing="onCellPrepared"
                                            :word-wrap-enabled="false"
                                             @saved="agregarLinea">

                                                <DxDataGridToolbar>                                                 
                                                    <DxDataGridItem location="before"
                                                            name="addRowButton"
                                                            showText="always"
                                                            :options="{
                                                                type:'success',
                                                                text:'Nuevo'
                                                            }"
                                                    />                                                                                
                                                </DxDataGridToolbar>

                                                <DxDataGridSelection mode="none" />

                                                <DxDataGridEditing :allow-updating="true" :allow-adding="true" :allow-deleting="true" mode="row" />
                                                
                                                <DxDataGridColumn :width="250" data-field="status" :set-cell-value="actualizarStatus" :visible="true" caption="Status" :allow-editing="true" :allow-filtering="false" >  
                                                    <DxDataGridLookup :dataSource="sourceRecibos.status" value-expr="Valor" display-expr="Descripcion"  />
                                                    <DxDataGridRequiredRule/>                                                  
                                                </DxDataGridColumn>  
                                                <DxDataGridColumn :width="125" data-field="formaDePago" :set-cell-value="actualizarFormaDePago" :visible="true" caption="Forma Pago" :allow-editing="true" :allow-filtering="false">                                                    
                                                    <DxDataGridLookup :dataSource="sourceRecibos.formasPago" value-expr="Valor" display-expr="Descripcion"  />                                                
                                                    <DxDataGridRequiredRule  message="La forma de pago es requerida"/>                                                  
                                                </DxDataGridColumn>                                              
                                                <DxDataGridColumn :width="200" data-field="monto" caption="Monto"  :editor-options="{format:'Q ##,##0.00'}" 
                                                          format="Q ##,##0.00" data-type="number" alignment="center">                                                               
                                                    <DxCustomRule message="El monto es mayor al permitido" type="custom" :validation-callback="validarMonto" :reevaluate="true" />
                                                    <DxDataGridRangeRule  message="El monto del recibo debe ser mayor a cero" :min="0.01"/>
                                                    <DxDataGridRequiredRule/>
                                                </DxDataGridColumn>
                                                <DxDataGridColumn :width="300" data-field="banco" :visible="true" caption="Banco" :allow-editing="true" :allow-filtering="false" >                                                    
                                                    <DxCustomRule message="El banco es requerido para Tarjeta y Cheque" type="custom" :validation-callback="validarBanco" :reevaluate="true" />
                                                    <DxDataGridLookup :dataSource="sourceRecibos.listaBancos" value-expr="Codigo" :display-expr="(data)=>data.Codigo.trim()+' - '+data.Nombre.trim()" />
                                                </DxDataGridColumn>
                                                <DxDataGridColumn :width="150" data-field="previsado" caption="Previsado" >
                                                    <DxDataGridLookup :dataSource="previsadoLista" /> <!--:allow-clearing="true"-->
                                                </DxDataGridColumn>
                                                <DxDataGridColumn :width="150" data-field="cheque" caption="Cheque/Slip" data-type="number" alignment="center" >
                                                    <DxCustomRule message="Obligatorio para pago con cheque ó Nota de crédito" type="custom" :validation-callback="validarCheque" :reevaluate="true" />
                                                </DxDataGridColumn>
                                                <DxDataGridColumn :width="150" data-field="cuenta" caption="Cuenta/Num Tarjeta" alignment="center" >
                                                    <DxCustomRule message="Obligatorio para pago con cheque, nota de crédito o tarjeta" type="custom" :validation-callback="validarCuenta" :reevaluate="true" />
                                                </DxDataGridColumn>
                                                <DxDataGridColumn :width="150" data-field="autorizacion" caption="Autorización" alignment="center" >
                                                    <DxCustomRule message="Obligatorio para pago con tarjeta" type="custom" :validation-callback="validarAutorizacion" :reevaluate="true" />
                                                </DxDataGridColumn>
                                                <DxDataGridColumn :width="150" data-field="tasa" caption="Tasa" data-type="number" alignment="center" />
                                                <DxDataGridColumn type="buttons" caption="Acción">
                                                    <DxDataGridButton name="save" icon="save" text="Guardar" />
                                                    <DxDataGridButton name="cancel" type="danger" icon="feather icon-x" text="Cancelar" />
                                                    <DxDataGridButton name="edit" icon="edit" text="Editar"/>
                                                    <DxDataGridButton name="delete" type="danger" icon="fas fa-trash" text="Borrar"/>
                                                </DxDataGridColumn>
                                                <DxDataGridSummary>
                                                    <DxDataGridTotalItem  column="monto" summary-type="sum" value-format="Q ##,##0.00" data-type="number" display-format="Total: {0}"/>
                                                </DxDataGridSummary>   
                                    </DxDataGrid>
                            </vs-row> 
                            <vs-row class="w-full p-1">                                                                                                   
                                <vs-row class="w-full">
                                    <vs-col class="sm:w-full md:w-full lg:w-5/12 xl:w-3/12 p-1" >
                                        <DxDataGrid v-bind="DefaultDxGridConfiguration"  
                                                            :data-source="facturas" :height="225">
                                            <!-- <DxDataGridColumn :width="200" data-field="Material" :disabled="consulta" caption="" alignment="center" :customize-text="customizeText" /> -->
                                            <DxDataGridColumn width="150px" data-field="Factura" alignment="center" :calculate-display-value="(rowData)=>rowData.SerieFactura+' '+rowData.Factura" />
                                            <DxDataGridColumn width="100px" data-field="Saldo" caption="Saldo" format="Q ##,##0.00" data-type="number" alignment="right" />
                                            <DxDataGridSummary>
                                                <DxDataGridTotalItem  column="Saldo" summary-type="sum"  value-format="Q ##,##0.00" data-type="number" display-format="Saldo Hospital: {0}"/>
                                            </DxDataGridSummary>   
                                        </DxDataGrid>
                                    </vs-col>
                                    <vs-col class="sm:w-full md:w-full lg:w-5/12 xl:w-4/12 p-1">
                                        <DxDataGrid v-bind="DefaultDxGridConfiguration"                                          
                                                    :data-source="cargosAjenos" :height="225" >
                                            <DxDataGridColumn width="150px" css-class="fuente-label"  data-field="Medico/Empresa" caption="Médico/Empresa" alignment="center" />
                                            <DxDataGridColumn width="150px" data-field="Saldo" caption="Saldo" format="Q ##,##0.00" data-type="number" alignment="right" />
                                            <DxDataGridSummary>                                                                
                                                <DxDataGridTotalItem  column="Saldo"  data-type="text" style="font-size: 10pt;" :customize-text="()=>{return 'Saldo Ajenos: ' + $formato_moneda(cargosAjenosDetalle.saldoAjenos)}" />                                                    
                                                <DxDataGridTotalItem  column="Saldo"  data-type="text" style="font-size: 10pt;" :customize-text="()=>{return 'Saldo Ajenos Extra: ' + $formato_moneda(cargosAjenosDetalle.saldoAjenosExtra)}" />                                                   
                                            </DxDataGridSummary>   
                                        </DxDataGrid>
                                    </vs-col>
                                    <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1" style="min-width: 150px; border: 1px solid #ddd; color: black; font-weight: bold; white-space: nowrap;">
                                        <div class="w-full">
                                            <vs-col class="w-1/2" style="text-align: left">
                                                <div class="pt-4">Hospital:</div>
                                                <div class="pt-2">Cuenta:</div>
                                                <div class="pt-2">AjenosExtra:</div>
                                                <div class="pt-2">- Abonos:</div>
                                                <vs-divider class="p-0 m-0"></vs-divider>
                                                <div >A Pagar:</div>                                                                
                                            </vs-col>
                                            <vs-col class="w-1/2" style="text-align: right">
                                                <div class="pt-4">{{ $formato_moneda(facturasDetalle.hospital) }}</div>
                                                <div class="pt-2">{{ $formato_moneda(cargosAjenosDetalle.cuentaAjena) }}</div>
                                                <div class="pt-2">{{ $formato_moneda(cargosAjenosDetalle.ajenosExtra) }}</div>
                                                <div class="pt-2">{{ $formato_moneda(facturasDetalle.abonos) }}</div>
                                                <vs-divider class="p-0 m-0"></vs-divider>
                                                <div >{{ $formato_moneda(totalPago.sumatoriaPago) }}</div>                                                                
                                            </vs-col>
                                        </div>                                                   
                                    </vs-col> 
                                </vs-row>                                     
                            </vs-row>                        
                            <vs-row class = 'w-full pt-5'>
                                <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 m-1">
                                    <vs-button color="success" :type="reciboSeleccionado?'border':'filled'" class="w-full" :disabled="!configuracion.activarRecibos" 
                                        id="botonRecibo"
                                        @keyup.tab="reciboSeleccionado=true"
                                        v-on:blur="reciboSeleccionado=false"
                                        @click="GrabarRecibosHospital()">                                        
                                        Recibo
                                    </vs-button>
                                </vs-col>
                                <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 m-1">
                                    <vs-button color="warning" class="w-full"
                                        @click="LimpiarAdmisionesR()">
                                        Limpiar
                                    </vs-button>
                                </vs-col>
                            </vs-row>        
                            <vs-divider></vs-divider>
                            
                    </vs-row>            
              </vx-card>
          </div>
        </vs-tab>
      </vs-tabs>
    </div>
</template>
    
<script>
    import "devextreme/ui/lookup";    
    import BusquedaAdmision from "/src/components/sermesa/modules/admisiones/BusquedaAdmision.vue";    
    import ConfiguracionCaja from "/src/components/sermesa/modules/caja/ConfiguracionCaja.vue";       
    import moment from "moment";
    import CustomStore from 'devextreme/data/custom_store';
    import {DefaultDxGridConfiguration,
            CalcularExencion } from './data';        
    import { ref } from 'vue'
    export default {
        components:{           
            BusquedaAdmision, 
            ConfiguracionCaja                              
        },    
        data() {    
            return {  
                reciboSeleccionado:false,
                facturaSeleccionado:false,
                VentanaImpresionRecibo: false,
                serieRecibo:'',
                numeroRecibo:'',
                reimpresion: true,
                permisos:{
                  recibo:false,
                  paso0:false,
                  paso1:false,
                  paso2:false,
                  paso3:false,
                  paso13:false,
                  recibo_por_factura:false,
                },
                format: 'Q\'.\' ###,###,###.00',              
                activeTab:0,
                popUpMonitorContingencias:false,
                reciboPorFactura:false,
                DefaultDxGridConfiguration:{...DefaultDxGridConfiguration,
                                                searchPanel: {visible: false},
                                                headerFilter:{
                                                    visible:false,
                                                    allowSearch:false
                                                }
                },   
                dataSourceResumen : new CustomStore({
                    key: 'id',
                    load: () => {
                        return this.resumenCuenta
                    }
                }),
                resumenCuenta:[
                    {id:1,descripcion:'Hospital:',monto:0.00},
                    {id:2,descripcion:'Cuenta Ajena:',monto:0.00},
                    {id:3,descripcion:'Ajenos Extra:',monto:0.00},
                    {id:4,descripcion:'-Abonos:',monto:0.00},
                    {id:5,descripcion:'A pagar:',monto:0.00},
                    {id:6,descripcion:'-Este pago:',monto:0.00},
                ],    
                tipoReciboSelected:[{Valor:'A',Descripcion:'Anticipo'},
                                    {Valor:'C',Descripcion:'Cancelación'}],
                previsadoLista:['S','N'],
                configuracion:{
                    cajaSeleccionada: null,
                    activarFacturacion: false,
                    activarRecibos: false                     
                },                    
                info:{  serie:null,
                        numeroAdmision:null,
                        paciente: null,
                        habitacion: null,                         
                        tipoDescuento:null,
                        seguro:null,
                        nombreSeguro:null,
                        copago:null,
                        coaseguro:null,
                        dataAdmision:null
                     },
                seguro: {
                    noDocumento:null,
                    nombre:null,
                    direccion:null		
                },
                documento:{ tiposDocumentoLista:[],
                            tipoDocumento:null,
                            noDocumento:null,
                            nombre:null,
                            correo:null,
                            direccion:null,
                            validacion:null			
                    },	 
                caja: { serieFactura:[],
                        serieFacturaFiltrada:[],
                                            
                        tiposFacturacion: [],
                        tipoFacturacionSeleccionada:null
                    },
                facturacion:{
                    fechaFacturacion: null,
                    descripcionVariable: null,
                    copagoFacturar: null,
                    coaseguroPorcentajeFacturar: null,
                    habilitarCamposTipo0: false,
                    habilitarCamposTipo13: false,
                    habilitarCamposTipo3: false,
                    habilitarCamposTipo2: false,
                    habilitarCamposTipo1: false,
                },
                facturas:[],
                facturasDetalle:{
                    hospital:0.00,
                    abonos:0.00
                },
                totalPago:{
                    sumatoriaPago:0.00,
                    restaAbonos:0.00
                },
                cargosAjenos:[],
                cargosAjenosDetalle:{
                    cuentaAjena:0.00,
                    ajenosExtra:0.00,
                    saldoAjenos:0.00,
                    saldoAjenosExtra:0.00,
                    IVA:0.00
                },
                cargos:[],
                sourceRecibos:{
                    autorizacionRequired:false,
                    cuentaRequired:false,
                    chequeRequired:false,
                    bancoRequired:false,
                    serieFactura:null,
                    numeroFactura:null,
                    saldoFactura:null,
                    tipoRecibo:null,
                    recibos:[],
                    status:[],
                    formasPago:[],
                    facturasPorAdmision:[],
                    listaBancosPagos:[],
                    listaBancosTarjeta:[],
                    listaBancos:[]                    
                },	 
                quitar_espacio_buscar : { padding: '0 !important;' },
                limpiarAdmision: false,   
                GridToolBar: {
                    items: [{
                        name: 'addRowButton',
                        showText: 'always',
                        options: {
                            type: 'success',
                            text: 'Nuevo'
                        }
                    }],
                },
                EditorPopup: {
                    toolbarItems: [{
                            widget: 'dxButton',
                            location: 'after',
                            toolbar: 'bottom',
                            options: {
                                text: 'Guardar Cambios',
                                type: 'default',
                                useSubmitBehavior: true,
                                onClick: () => {
                                    this.recibosFacturacionDataGrid.saveEditData()
                                }
                            }
                        },
                        {
                            widget: 'dxButton',
                            location: 'after',
                            toolbar: 'bottom',
                            options: {
                                text: 'Cancelar',
                                type: 'danger',
                                useSubmitBehavior: true,
                                onClick: () => {
                                    this.recibosFacturacionDataGrid.cancelEditData()
                                }
                            }
                        },
                    ],
                },                     
                money: {
                    decimal: '.',
                    thousands: ',',
                    prefix: 'Q',
                    precision: 2,
                    masked: false
                },
                statusEditorOptions : {
                    items: ()=>this.sourceRecibos.status
                },  
              NoAutorizacionSeguro:"",
              NoPoliza:"",            
            }
        },
        computed:{
            recibosFacturacionDataGrid(){
                return this.$refs['recibosFacturacionRef'].instance;
            },
            tipoAdmision(){
                return this.info.tipoDescuento == 'N' ? 'Privada' : this.info.tipoDescuento == 'R' ? 'Reingreso' : 
                       this.info.tipoDescuento == 'Q' ? 'Paquete' : this.info.tipoDescuento == 'S' ? 'Seguro' :
                       this.info.tipoDescuento == 'E' ? 'Empleado' : ''
            },
            sesion() {
                return this.$store.state.sesion
            }
        },
        watch:{            
            reciboPorFactura(value){
                if(value){
                    this.CargarFacturasPorAdmision()
                }else{
                    this.sourceRecibos.numeroFactura = null
                    this.sourceRecibos.serieFactura  = null
                    this.sourceRecibos.saldoFactura  = null
                }
            },
            'caja.tipoFacturacionSeleccionada'(value){
                if(value){
                    this.facturacion.habilitarCamposTipo13 = value == "13"
                    this.facturacion.habilitarCamposTipo3 = value == "3"
                    this.facturacion.habilitarCamposTipo2 = value == "2"
                    this.facturacion.habilitarCamposTipo1 = value == "1"
                    this.facturacion.habilitarCamposTipo0 = value == "0"

                    if(!this.documento.validacion && value!='3' && this.info.dataAdmision != null) this.ValidacionDocumentoTributario()
                }
                else{
                    this.facturacion.habilitarCamposTipo13 = false
                    this.facturacion.habilitarCamposTipo3 = false
                    this.facturacion.habilitarCamposTipo2 = false
                    this.facturacion.habilitarCamposTipo1 = false
                    this.facturacion.habilitarCamposTipo0 = false
                }
            }
        },
        methods: {
            agregarLinea(e){
                if(e.changes && e.changes.length == 1 && e.changes[0].type == "insert"){
                    this.$nextTick(() => {
                        setTimeout(() => {
                            this.recibosFacturacionDataGrid.addRow()
                        }, 100)                    
                    })
                }             
                //e.component.addRow()
                //this.recibosFacturacionDataGrid.refresh()

            },  
            onCellPrepared(e) {
                if (e.dataField == 'banco' && (e.row.data.formaDePago == 'B' || e.row.data.formaDePago == 'N') ) {
                    e.editorOptions.dataSource = this.sourceRecibos.listaBancos.filter(banco=>banco.Tipo=='B')
                }else if (e.dataField == 'banco' && (e.row.data.formaDePago == 'T' || e.row.data.formaDePago == 'R' || e.row.data.formaDePago == 'X')) {
                    e.editorOptions.dataSource = this.sourceRecibos.listaBancos.filter(banco=>banco.Tipo== e.row.data.formaDePago)
                }else if(e.dataField == 'banco'){
                    e.editorOptions.dataSource = []
                }                
                
                
                if (e.dataField == 'status' && this.sourceRecibos.tipoRecibo == 'A' ) {
                    e.editorOptions.dataSource = this.sourceRecibos.status.filter(s=>s.Valor == 'A')
                }else if (e.dataField == 'status') {
                    e.editorOptions.dataSource = this.sourceRecibos.status
                }
            },
            getFilteredBancos(options){
                return options.data?this.sourceRecibos.listaBancos.filter(banco=>banco.formaDePago==options.data.formaDePago):[]
            },
            getFilteredBancos2: (options) => ({
                                        store:this.sourceRecibos.listaBancos,
                                        filter:options.data?['formaDePago','=',options.data.formaDePago]:null
                                    }),
            nuevoRecibo(e){ 
                e.data.previsado = 'N'
                if(this.reciboPorFactura && this.sourceRecibos.saldoFactura){
                    e.data.status = 'H'
                    e.data.monto = this.sourceRecibos.saldoFactura - this.sourceRecibos.recibos.reduce( (total,recibo) => total + recibo.monto ,0.00)
                }else if(this.sourceRecibos.tipoRecibo == 'A'){
                    e.data.status = 'A'
                    e.data.monto = 0.00
                }else{
                    e.data.status = 'H'
                    e.data.monto = this.facturas.reduce( (total, factura)=> total + this.ConvertirFloat(factura.Saldo),0.00)
                                    - this.sourceRecibos.recibos.reduce( (total,recibo) => recibo.status == 'H'? total + recibo.monto : total ,0.00)  
                }

                if(e.data.monto < 0){
                    e.data.monto = 0.00                    
                }
            },
            actualizarBusquedaAdmision(){
                this.$refs.componenteAdmisionesF.actualizarBusqueda()
            },         
            abrirMonitorContingencia(){
                if(!this.configuracion.cajaSeleccionada){
                    this.Consulta().MostrarError('Seleccione un caja valida.')
                    return
                }
                
                this.popUpMonitorContingencias = true;
                this.$refs.monitorContingenciaRef.CargarFacturasEnContingencias()
            },
            validarMonto(linea){
                let montoMaximo = 0.00       
                let montoValido = true   
                if(this.sourceRecibos.tipoRecibo == 'A'){
                    montoValido = true;
                }else if(this.reciboPorFactura && this.sourceRecibos.saldoFactura){
                    montoMaximo = this.sourceRecibos.saldoFactura
                                    - this.sourceRecibos.recibos.reduce( (total,recibo) => total + recibo.monto ,0.00)
                    if(linea.data.monto > montoMaximo ) montoValido = false 
                }else if(linea.data.status == 'H'){                    
                    montoMaximo = this.facturas.reduce( (total, factura)=> total + this.ConvertirFloat(factura.Saldo),0.00) 
                                    - this.sourceRecibos.recibos
                                          .reduce( (total,recibo) => recibo.status == 'H' && recibo.__KEY__ != linea.data.__KEY__ ? total + recibo.monto : total ,0.00)
                    if(linea.data.monto > montoMaximo ) montoValido = false                    
                }else if(linea.data.status == 'A'){                    
                    montoMaximo = this.cargosAjenosDetalle.saldoAjenos
                                    - this.sourceRecibos.recibos
                                          .reduce( (total,recibo) => recibo.status == 'A' && recibo.__KEY__ != linea.data.__KEY__? total + recibo.monto : total ,0.00)
                    if(linea.data.monto > montoMaximo ) montoValido = false                    
                }else if(linea.data.status == 'E'){   
                    montoMaximo = this.cargosAjenosDetalle.saldoAjenosExtra
                                    - this.sourceRecibos.recibos
                                          .reduce( (total,recibo) => recibo.status == 'E' && recibo.__KEY__ != linea.data.__KEY__? total + recibo.monto : total ,0.00)
                    if(linea.data.monto > montoMaximo ) montoValido = false                                       
                }
                return montoValido
            },
            validarBanco(linea){
                let bancoValido = true;
                if( (linea.data.formaDePago == 'T' || linea.data.formaDePago == 'B' || linea.data.formaDePago == 'N')){
                    if(!linea.data.banco || linea.data.banco.trim() == ''){
                        bancoValido = false
                    }                    
                }  
                return bancoValido              
            },
            validarCheque(linea){
                let respuesta = true;
                if((linea.data.formaDePago=='B' || linea.data.formaDePago=='N') && (!linea.data.cheque || linea.data.cheque=='')){
                    respuesta = false
                }
                return respuesta;
            },
            validarCuenta(linea){            
                let respuesta = true;
                if(linea.data.formaDePago=='B' || linea.data.formaDePago=='T' || linea.data.formaDePago=='N'){
                    if(!linea.data.cuenta || linea.data.cuenta==''){
                        respuesta = false
                    }
                }
                return respuesta;
            },
            validarAutorizacion(linea){
                let respuesta = true;
                if(linea.data.formaDePago=='T' && (!linea.data.autorizacion || linea.data.autorizacion.trim()=='')){
                    respuesta = false
                }
                return respuesta;
            },            
            setValorBanco(fila,value){
                let banco = this.sourceRecibos.listaBancos.filter(banco=>banco.Codigo==value) 
                fila.banco = value
                fila.Banco = banco.Codigo + '-' + banco.Nombre
            },
            actualizarFormaDePago(fila,value, filaActual){
                fila.formaDePago = value
                fila.banco = null 
                this.actualizarMonto(fila,value,filaActual.status)               
            },
            actualizarStatus(fila, value, filaActual){
                fila.status = value      
                this.actualizarMonto(fila,filaActual.formaDePago, value)                          
            },
            actualizarMonto(fila, formaDePago, status){                
                let montoBase = 0.00
                let valorIva = this.ConvertirFloat(this.cargosAjenosDetalle.IVA)/100 
                if(this.sourceRecibos.tipoRecibo == 'A'){
                    return;
                }
                if(this.reciboPorFactura && this.sourceRecibos.saldoFactura){
                    montoBase = this.sourceRecibos.saldoFactura
                    fila.monto =
                    formaDePago && formaDePago == 'X' ? CalcularExencion(montoBase,valorIva)
                                            : montoBase - this.sourceRecibos.recibos.reduce( (total,recibo) => total + this.ConvertirFloat(recibo.monto) ,0.00)                  
                }else if(status == 'H'){                    
                    montoBase = this.facturas.reduce( (total, factura)=> total + this.ConvertirFloat(factura.Saldo),0.00)       
                    fila.monto =           
                    formaDePago && formaDePago == 'X' ? CalcularExencion(montoBase,valorIva)
                                            : montoBase - this.sourceRecibos.recibos.reduce( (total,recibo) => recibo.status == 'H'? total + this.ConvertirFloat(recibo.monto) : total ,0.00)      
                }else if(status == 'A'){       
                    montoBase = this.cargosAjenosDetalle.saldoAjenos      
                    fila.monto =       
                    formaDePago && formaDePago == 'X' ? CalcularExencion(montoBase,valorIva)
                                            : montoBase - this.sourceRecibos.recibos.reduce( (total,recibo) => recibo.status == 'A'? total + this.ConvertirFloat(recibo.monto) : total ,0.00)
                }else if(status == 'E'){ 
                    montoBase = this.cargosAjenosDetalle.saldoAjenosExtra   
                    fila.monto =        
                    formaDePago && formaDePago == 'X' ? CalcularExencion(montoBase,valorIva)
                                            : montoBase - this.sourceRecibos.recibos.reduce( (total,recibo) => recibo.status == 'E'? total + this.ConvertirFloat(recibo.monto) : total ,0.00)                                   
                }else{
                    fila.monto = null
                }

                if(fila.monto < 0){
                    fila.monto = 0.00
                }
            },
            filtrarBancos(opciones){
                return opciones.data ? this.sourceRecibos.listaBancos.filter(banco=>banco.formaDePago == opciones.data.formaDePago)  : null        
            }, 
            CargarCaja(cajaSeleccionada){                
                this.configuracion.cajaSeleccionada   = cajaSeleccionada.Cajero
                this.configuracion.activarFacturacion = cajaSeleccionada.Factura
                this.configuracion.activarRecibos     = cajaSeleccionada.Recibo
            },
            actualizaBusquedaAdmisionFactura(){
                this.$nextTick(()=>{
                    if(this.info.dataAdmision){
                    this.$refs.componenteAdmisionesF.recargarComponente(this.info.dataAdmision)
                    }
                })
                
            },
            actualizaBusquedaAdmisionRecibo(){
                this.$nextTick(()=>{
                    if(this.info.dataAdmision){
                    this.$refs.componenteAdmisionesR.recargarComponente(this.info.dataAdmision)
                    }
                })            
            },
            tipo_documento_seleccionado(tipoDocumento){
                return `${tipoDocumento.Descripcion}`
            },
            async GrabarRecibosHospital(){              
                if(!this.permisos.recibo){
                    this.Consulta().MostrarError('No cuenta con permisos para realizar recibos.',3000);  
                    return
                }

                const botonRecibo = document.getElementById("botonRecibo");
                if(this.sourceRecibos.recibos.length <= 0){
                    this.Consulta().MostrarError('Debe ingresar un pago para generar el recibo',3000);  
                    return
                }

                if(this.sourceRecibos.tipoRecibo == 'A'){
                    let reciboNoAnticipo = this.sourceRecibos.recibos.find(r=>r.status != 'A')
                    if(reciboNoAnticipo != null){
                        this.Consulta().MostrarError('Solo es valido el status Anticipo/Ajeno para recibos de Anticipo',3000);  
                        return
                    }
                }

                if(this.reciboPorFactura){
                    if(!this.ValidarVariable(this.sourceRecibos.serieFactura) || !this.ValidarVariable(this.sourceRecibos.numeroFactura)){
                        this.Consulta().MostrarError('Debe ingresar la serie y no. de factura',3000);  
                        return
                    }
                }else{
                    this.sourceRecibos.serieFactura = null
                    this.sourceRecibos.numeroFactura = 0
                }
                let resultado = await this.axios.post('/app/v1_caja/GeneracionRecibosHospitales',
                                {
                                    serieAdmision:this.info.serie,
                                    numeroAdmision:this.info.numeroAdmision,
                                    opcion:'I',
                                    subOpcion:1,                                                                        
                                    nombreFactura:this.documento.nombre,
                                    direccion:this.documento.direccion,
                                    nombreRecibo:this.documento.nombre, /* SE ESTA ENVIANDO EL MISMO QUE LA FACTURA */   
                                    serieFacturaCajero: this.configuracion.cajaSeleccionada.SerieFac,
                                    serieFactura:this.sourceRecibos.serieFactura,
                                    numeroFactura:this.sourceRecibos.numeroFactura,                                 
                                    serieRecibo:this.configuracion.cajaSeleccionada.SerieRecibo,
                                    tipoRecibo:this.sourceRecibos.tipoRecibo,
                                    ipoc:0,
                                    listaRecibos:this.sourceRecibos.recibos                    
                                }).then(resp=>{
                                    if(resp.data.codigo == 0){  
                                        if(botonRecibo) botonRecibo.blur()      
                                        this.serieRecibo = resp.data.Serie
                                        this.numeroRecibo = resp.data.Recibo  
                                        this.VentanaImpresionRecibo = true;                                                      
                                        this.sourceRecibos.recibos = []
                                        this.reciboPorFactura = false 
                                        return 1
                                    }
                                });

                    if(resultado==1){
                        await this.Consulta().ObtenerFacturasCargosAjenos('FACTURAS',1);
                        await this.Consulta().ObtenerFacturasCargosAjenos('FACTURAS',2);
                        await this.Consulta().ObtenerFacturasCargosAjenos('AJENOS',1);
                        await this.Consulta().ObtenerFacturasCargosAjenos('AJENOS',2);
                        this.Consulta().ActualizarTotales();
                    }
            },
            async GenerarRecibo(Serie,NumeroRecibo) {

                let reporte = "Recibo"

                let postData = {
                    SerieRecibo:   Serie ,
                    NumeroRecibo: NumeroRecibo,
                    AjusteImpresion: this.reimpresion ? 1 : 0
                }


                this.$reporte_modal({
                    Nombre: reporte,
                    Opciones: {
                        ...postData
                    },
                    NombreArchivoDescargar:Serie+'_'+NumeroRecibo,
                    Descargar:true
                })                

            },  
            async GenerarFactura(Serie,NumeroFactura) {
                let reporte = "Impresion FEL"

                let postData = {
                    SerieFactura: Serie ,
                    NumFactura: NumeroFactura,
                    nombrereporte: this.configuracion.cajaSeleccionada.FormatoImpresion
                }


                this.$reporte_modal({
                    Nombre: reporte,
                    Opciones: {
                        ...postData
                    }
                })                

            },       
            ConvertirFloat(Valor,Decimales=2){
                let conversion = parseFloat(Valor?Valor:0.00)
                if(isNaN(conversion)){
                    return 0.00
                }
                return parseFloat(conversion.toFixed(Decimales))
            },      
            GrabarTiempoPromesa(SerieFactura,NumeroFactura){
                this.axios.post('/app/v1_caja/EnvioTiempoPromesa',{
                                serieAdmision   : this.info.serie,
                                codigoAdmision  : this.info.numeroAdmision,
                                serieFactura    : SerieFactura,
                                numeroFactura   : NumeroFactura,
                                ipEquipo        : localStorage.getItem('IpLocal')
                            })
                          .then(resp=>{
                            if(resp.data.json[0].CodRespuesta == 0){
                                this.Consulta().MostrarSatisfactorio(resp.data.json[0].Respuesta ,2000)
                            }
                          })
            },
            ValidarVariable(entrada){   
                if(typeof(entrada) == 'number'){
                    return entrada ? true : false;
                }else if(typeof(entrada) == 'string'){
                    return entrada && entrada.length > 0 ? true : false;
                }else if(typeof(entrada) == 'object'){
                    return entrada ? true : false;
                }            
                return false;
            },
            async DatosAdmisionF(datos) {
                this.info.dataAdmision = datos
                this.info.serie = datos.Serie
                this.info.numeroAdmision = datos.Codigo
                this.info.paciente = datos.Paciente
                this.info.habitacion = datos.Habitacion 
                this.info.tipoDescuento = datos.TipoDescuento
                this.info.seguro = datos.Seguro
                this.info.nombreSeguro = datos.Nombre

                this.documento.tipoDocumento = ref(this.documento.tiposDocumentoLista.find(doc=>doc.Descripcion == 'NIT'))                
                this.documento.noDocumento = datos.Nit
                this.documento.nombre = datos.NombreFactura
                this.documento.direccion = datos.DireccionFactura

                this.seguro.noDocumento = datos.NitAse
                this.seguro.nombre = datos.NombreFacturaAse
                this.seguro.direccion = datos.DireccionFacturaAse
                this.seguro.tipoAseguradora = datos.TipoAseguradora

                this.facturacion.copagoFacturar = datos.CopagoAse == 0.00 ? null : datos.CopagoAse
                this.facturacion.coaseguroPorcentajeFacturar = datos.CoaseguroAse  == 0.00 ? null : datos.CoaseguroAse                                     

                this.info.copago = datos.Copago
                this.info.coaseguro = datos.CoaseguroMonto
                await this.Consulta().ObtenerFacturasCargosAjenos('FACTURAS',1);
                await this.Consulta().ObtenerFacturasCargosAjenos('AJENOS',1);
                await this.Consulta().ObtenerFacturasCargosAjenos('FACTURAS',2);
                await this.Consulta().ObtenerFacturasCargosAjenos('AJENOS',2);
                if(this.caja.tipoFacturacionSeleccionada != '3')
                    this.ValidacionDocumentoTributario()
                this.Consulta().ActualizarTotales()
            }, 
            async DatosAdmisionR(datos) {
                this.info.dataAdmision = datos
                this.info.serie = datos.Serie
                this.info.numeroAdmision = datos.Codigo
                this.info.paciente = datos.Paciente
                this.info.habitacion = datos.Habitacion 
                this.info.tipoDescuento = datos.TipoDescuento
                this.info.seguro = datos.Seguro
                this.info.nombreSeguro = datos.Nombre

                this.documento.tipoDocumento = ref(this.documento.tiposDocumentoLista.find(doc=>doc.Descripcion == 'NIT'))                
                this.documento.noDocumento = datos.Nit
                this.documento.nombre = datos.NombreFactura
                this.documento.direccion = datos.DireccionFactura

                this.seguro.noDocumento = datos.NitAse
                this.seguro.nombre = datos.NombreFacturaAse
                this.seguro.direccion = datos.DireccionFacturaAse
                this.seguro.tipoAseguradora = datos.TipoAseguradora

                this.facturacion.copagoFacturar = datos.CopagoAse
                this.facturacion.coaseguroPorcentajeFacturar = datos.CoaseguroAse                                     

                this.info.copago = datos.Copago
                this.info.coaseguro = datos.CoaseguroMonto
                await this.Consulta().ObtenerFacturasCargosAjenos('FACTURAS',1);
                await this.Consulta().ObtenerFacturasCargosAjenos('AJENOS',1);
                await this.Consulta().ObtenerFacturasCargosAjenos('FACTURAS',2);
                await this.Consulta().ObtenerFacturasCargosAjenos('AJENOS',2);
                this.Consulta().ActualizarTotales()
            }, 
            CargarTiposFacturacion(){
                this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision',{opcion:'C',
                                                                            subOpcion:1})
                          .then(resp=>{this.caja.tiposFacturacion = resp.data.json
                                       this.caja.tiposFacturacion = this.caja.tiposFacturacion
                                                                        .map(tipo =>{   tipo.disabled = this.permisos.paso0 && tipo.Tipo == "0" ? false
                                                                                                       :this.permisos.paso1 && tipo.Tipo == "1" ? false 
                                                                                                       :this.permisos.paso2 && tipo.Tipo == "2" ? false
                                                                                                       :this.permisos.paso3 && tipo.Tipo == "3" ? false
                                                                                                       :this.permisos.paso13 && tipo.Tipo == "13" ? false
                                                                                                       :true; 
                                                                                        return tipo})                                        
                                       //this.caja.tipoFacturacionSeleccionada = '0';
                                       //this.facturacion.habilitarCamposTipo0 = true;
                          })
            },  
            CargarFacturasPorAdmision(){               
                if(!this.ValidarVariable(this.info.serie) || 
                   !this.ValidarVariable(this.info.numeroAdmision)){
                    return
                }

                if(!this.permisos.recibo_por_factura){
                    this.Consulta().MostrarError('No cuenta con permiso para realizar recibos por factura',4000)
                    setTimeout(()=>this.reciboPorFactura = false,1000)
                    return
                }

                this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision',
                                {opcion:'C',
                                 subOpcion:6,
                                 serieAdmision:this.info.serie,
                                 numeroAdmision:this.info.numeroAdmision}
                               )
                          .then(resp=>{
                            this.sourceRecibos.facturasPorAdmision = resp.data.json
                            if(this.sourceRecibos.facturasPorAdmision.length <= 0){
                                this.Consulta().MostrarError('La admisión no cuenta con facturas con saldo',4000)
                                setTimeout(()=>this.reciboPorFactura = false,1000)
                            }else{
                                this.sourceRecibos.tipoRecibo = 'C'
                                setTimeout(()=>this.$refs.BusquedaFacturaRecibo.abrir_buscador(),1000)                                
                            }
                          })
            },      
            async ValidacionDocumentoTributarioTab(){
                await this.ValidacionDocumentoTributario();
                if(this.documento.validacion == 1){
                    const campoNombre = document.getElementById("idNombre");
                    if(campoNombre) campoNombre.focus();
                }
            },                 
            async ValidacionDocumentoTributario(){
                if(this.Consulta().EsVacia(this.documento.tipoDocumento)){
                    this.Consulta().MostrarError('Seleccione un tipo de documento para el cliente');
                    return ;
                }
                if(this.Consulta().EsVacia(this.documento.noDocumento)){
                    this.Consulta().MostrarError('Ingrese el NIT/DPI del cliente');
                    return ;
                }

                await this.axios.post('/app/v1_caja/ValidacionDocumentoTributario',{
                                    tipoReceptor: parseInt(this.documento.tipoDocumento.Valor),
                                    documento: this.documento.noDocumento
                                })
                          .then(resp=>{
                                    if ( resp.data.codigo == 0 && resp.data.json.length > 0 && resp.data.json[0].codigo == 0){
                                        this.documento.nombre = resp.data.json[0].NOMBRE
                                        this.documento.correo = resp.data.json[0].EMAIL
                                        this.documento.direccion = resp.data.json[0].DIRECCION
                                        this.documento.validacion = 1
                                    }else if ( resp.data.codigo == 0 && resp.data.json.length > 0 && resp.data.json[0].codigo != 0){
                                        this.Consulta().MostrarError(resp.data.json[0].descripcion);
                                        this.documento.nombre = null
                                        this.documento.correo = null
                                        this.documento.direccion = null
                                        this.documento.validacion = null
                                    }
                                    else if(resp.data.json.length == 0){
                                        this.documento.nombre = null
                                        this.documento.correo = null
                                        this.documento.direccion = null
                                        this.documento.validacion = null
                                        this.Consulta().MostrarError('No se encontraron datos para el documento ingresado');
                                    }
                                })
                            
            },
            async CargarAgrupaciones(agrupacion, subOpcion=2, tipoPago ='B'){
                await this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision', {
                                    opcion:'C',
                                    subOpcion:subOpcion,
                                    agrupacion,
                                    tipoPago
                                })
                            .then(resp => {                            
                                    if (resp.data && resp.data.json.length > 0){
                                        if(agrupacion == 'StatusDePago'){
                                            this.sourceRecibos.status = resp.data.json.filter(status=> status.Valor != 'M' && status.Valor != '');
                                        }
                                        if(agrupacion == 'FormasDePago'){
                                            this.sourceRecibos.formasPago = resp.data.json;
                                        }
                                        if(agrupacion == 'Bancos'){                                            
                                            if(this.sourceRecibos.listaBancos && this.sourceRecibos.listaBancos.length>0){                                                
                                                this.sourceRecibos.listaBancos.push(...resp.data.json)                                                
                                            }else{
                                                this.sourceRecibos.listaBancos = resp.data.json;
                                            }
                                            /*
                                            if(tipoPago == 'B'){
                                                this.sourceRecibos.listaBancosPagos = resp.data.json;
                                            }

                                            if(tipoPago == 'T'){
                                                this.sourceRecibos.listaBancosTarjeta = resp.data.json;
                                            } 
                                            */                                                                                      
                                        }
                                    }
                                }
                            )
            },   
            CargarTipoDocumento(){
                this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision', {
                                    opcion:'C',
                                    subOpcion:2,
                                    agrupacion:'TipoReceptor'
                                })
                          .then(resp => {                            
                                    if (resp.data && resp.data.json.length > 0){
                                        this.documento.tiposDocumentoLista = resp.data.json;
                                    }
                                }
                               )
            },  
            LimpiarAdmisionesF(){
                this.$refs.componenteAdmisionesF.limpiar_campos()
            },                   
            LimpiarAdmisionesR(){
                this.$refs.componenteAdmisionesR.limpiar_campos()
            },  
            RecargarAdmisionF(){
                if(this.caja.tipoFacturacionSeleccionada != '2') return;

                this.axios.post('/app/v1_JefeCaja/consulta_admision_recalculo',{
                                    Serie: this.info.serie,
                                    Codigo: this.info.numeroAdmision,
                                    Opcion:'CONSULTA',
                                    SubOpcion:'CAJERO',
                                    activo:0,
                                    tieneHabitacion:0
                                })
                          .then(resp=>{
                                    if(resp.data.json.length==1){
                                        this.info.dataAdmision = resp.data.json[0]
                                        this.info.copago = resp.data.json[0].Copago
                                        this.info.coaseguro = resp.data.json[0].CoaseguroMonto                                     
                                    }
                                })
            },
            LimpiarDatosCliente(){
                this.info.serie = null
                this.info.numeroAdmision = null
                this.info.paciente = null
                this.info.habitacion = null     
                this.info.tipoDescuento = null
                this.info.seguro = null
                this.info.nombreSeguro = null
                this.info.copago = null
                this.info.coaseguro = null
                this.info.tipoDescuento = null
                this.info.dataAdmision = null 

                this.seguro.noDocumento = null
                this.seguro.nombre = null
                this.seguro.direccion = null

                this.documento.tipoDocumento = null
                this.documento.noDocumento = null
                this.documento.nombre = null
                this.documento.correo = null
                this.documento.direccion = null
                this.documento.validacion = null

                this.facturacion.fechaFacturacion = null
                this.facturacion.descripcionVariable = null
                this.facturacion.copagoFacturar = null
                this.facturacion.coaseguroPorcentajeFacturar = null

                this.facturacion.habilitarCamposTipo0 = false
                this.facturacion.habilitarCamposTipo1 = false
                this.facturacion.habilitarCamposTipo2 = false
                this.facturacion.habilitarCamposTipo3 = false
                this.facturacion.habilitarCamposTipo13 = false
    
                this.facturas = []
                this.facturasDetalle.hospital = 0.00
                this.facturasDetalle.abonos = 0.00

                this.totalPago.sumatoriaPago = 0.00
                this.totalPago.restaAbonos = 0.00

                this.cargosAjenos = []
                
                this.cargosAjenosDetalle.cuentaAjena = 0.00
                this.cargosAjenosDetalle.ajenosExtra = 0.00
                this.cargosAjenosDetalle.saldoAjenos = 0.00
                this.cargosAjenosDetalle.saldoAjenosExtra = 0.00
                this.cargosAjenosDetalle.IVA = 0.00

                this.cargos = []           

                this.sourceRecibos.recibos = []
                this.sourceRecibos.tipoRecibo = 'C'
                this.reciboPorFactura = false              

                //this.caja.tipoFacturacionSeleccionada = '0';
                //this.facturacion.habilitarCamposTipo0 = true;

                this.NoAutorizacionSeguro = ""
                this.NoPoliza = ""    
            },       
            ObtenerPermisos(){
                this.permisos.recibo = this.$validar_privilegio('RECIBO').status
                this.permisos.paso0 = this.$validar_privilegio('FACTURACION_PASO_0').status
                this.permisos.paso1 = this.$validar_privilegio('FACTURACION_PASO_1').status
                this.permisos.paso2 = this.$validar_privilegio('FACTURACION_PASO_2').status
                this.permisos.paso3 = this.$validar_privilegio('FACTURACION_PASO_3').status
                this.permisos.paso13 = this.$validar_privilegio('FACTURACION_PASO_13').status
                this.permisos.recibo_por_factura = this.$validar_privilegio('RECIBO_POR_FACTURA').status
            },    
            actualizarCajero(){
                if(this.activeTab==0){
                    this.$refs.ConfiguracionCajaFactura.CargarCajaInicial();
                }else if(this.activeTab==1){
                    this.$refs.ConfiguracionCajaRecibo.CargarCajaInicial();
                }
            },
            Consulta() {
                return {                  
                    init: async () => {
                        this.ObtenerPermisos();
                        this.LimpiarDatosCliente();
                        this.CargarTiposFacturacion();
                        this.CargarTipoDocumento();
                        await this.CargarAgrupaciones('StatusDePago');
                        await this.CargarAgrupaciones('FormasDePago');
                        await this.CargarAgrupaciones('Bancos',4);
                        this.sourceRecibos.tipoRecibo = 'C'
                    },        
                    EsVacia:(variable)=>{
                        return variable?false:true;
                    },
                     ObtenerFacturasCargosAjenos : async (SubOpcion,ipoc)=>{
                        var subOpcionNumero = 0;                        
                        if(SubOpcion == 'FACTURAS'){
                            subOpcionNumero = 50;
                        }else if (SubOpcion == 'AJENOS'){
                            subOpcionNumero = 51;
                        }else{
                            return;
                        }

                        await this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision',
                                        {opcion:'I',
                                         subOpcion:subOpcionNumero,
                                         serieAdmision:this.info.serie,
                                         numeroAdmision:this.info.numeroAdmision,
                                         serieFactura: this.configuracion.cajaSeleccionada.SerieFac,
                                         ipoc                        
                                        })                                  
                                  .then(resp=>{
                                            if(resp.data.codigo == 0){
                                                if(SubOpcion == 'FACTURAS' && ipoc == 1){
                                                    this.facturas = resp.data.json;
                                                }else if(SubOpcion == 'FACTURAS' && ipoc == 2){
                                                    this.facturasDetalle.hospital = resp.data.json.length > 0 ? resp.data.json[0].Hospital : 0.00
                                                    this.facturasDetalle.abonos = resp.data.json.length > 0 ? resp.data.json[0].Abonos : 0.00
                                                }else if (SubOpcion == 'AJENOS' && ipoc == 1){
                                                    this.cargosAjenos = resp.data.json
                                                }else if (SubOpcion == 'AJENOS' && ipoc == 2){
                                                    this.cargosAjenosDetalle.cuentaAjena = resp.data.json.length > 0 ? resp.data.json[0].CuentaAjena : 0.00
                                                    this.cargosAjenosDetalle.ajenosExtra = resp.data.json.length > 0 ? resp.data.json[0].AjenosExtra : 0.00

                                                    this.cargosAjenosDetalle.saldoAjenos = resp.data.json.length > 0 ? resp.data.json[0].SaldoAjenos : 0.00
                                                    this.cargosAjenosDetalle.saldoAjenosExtra = resp.data.json.length > 0 ? resp.data.json[0].SaldoAjenosExtra : 0.00
                                                    this.cargosAjenosDetalle.IVA = resp.data.json.length > 0 ? resp.data.json[0].IVA : 0.00
                                                }
                                            }
                                        })
                    },
                    ActualizarTotales:()=>{
                        this.totalPago.sumatoriaPago = this.ConvertirFloat(this.cargosAjenosDetalle.cuentaAjena) + this.ConvertirFloat(this.cargosAjenosDetalle.ajenosExtra) + this.ConvertirFloat(this.facturasDetalle.hospital) - this.ConvertirFloat(this.facturasDetalle.abonos)
                    },
                    MostrarError:(mensaje,tiempo=2000)=>{
                        this.$vs.notify({
                            time:tiempo,
                            title: 'Cajero',
                            color: 'danger',
                            text:mensaje,
                            position: 'top-center'
                        })
                    },
                    MostrarSatisfactorio:(mensaje)=>{
                        this.$vs.notify({
                            time:2000,
                            title: 'Cajero',
                            color: 'success',
                            text:mensaje,
                            position: 'top-center'
                        })
                    }
                }
            },
            Guardar() {
                return {
                    Facturar: async ()=>{
                        //Validaciones de datos
                        const botonFacturar = document.getElementById("botonFacturar");
                        if(!this.Guardar().ValidacionesFactura()) return;
                        //fecha variable 2024-03-12
                        var fechaFacturacion = null;
                        if(this.facturacion.fechaFacturacion){
                            fechaFacturacion = moment(this.facturacion.fechaFacturacion).format('YYYY-MM-DD');
                        }
                        let nombre       = this.documento.nombre
                        let tipoReceptor = this.documento.tipoDocumento.Valor
                        let documento    = this.documento.noDocumento
                        let direccion    = this.documento.direccion
                        let correo       = this.documento.correo

                        if(this.caja.tipoFacturacionSeleccionada == '3'){ // Facturacion para seguros                          

                            tipoReceptor = '4'
                            if(this.seguro.tipoAseguradora == 'E'){ // para aseguradoras externas enviamos pasaporte                               
                                tipoReceptor = '3'
                            }
                            nombre = this.seguro.nombre
                            documento = this.seguro.noDocumento
                            direccion = this.seguro.direccion
                        }
                        let respuesta = await
                      
                        this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision',
                                        {opcion:'I',
                                         subOpcion      :this.caja.tipoFacturacionSeleccionada,
                                         serieAdmision  :this.info.serie,
                                         numeroAdmision :this.info.numeroAdmision,
                                         serieFactura   :this.configuracion.cajaSeleccionada.SerieFac,
                                         nombreFactura  :nombre,
                                         tipoReceptor   :tipoReceptor,
                                         documento      :documento,
                                         direccion      :direccion,
                                         correo         :correo,
                                         fechaFacturacion: fechaFacturacion,
                                         descripcionVariable: this.facturacion.descripcionVariable,
                                         copagoFacturar : this.facturacion.copagoFacturar == null || this.facturacion.copagoFacturar == "" ? 0.00 : this.facturacion.copagoFacturar,
                                         coaseguroPorcentajeFacturar: this.facturacion.coaseguroPorcentajeFacturar == null || this.facturacion.coaseguroPorcentajeFacturar == "" ? 0.00 : this.facturacion.coaseguroPorcentajeFacturar,
                                         NoAutorizacion:this.NoAutorizacionSeguro,
                                         NoPoliza: this.NoPoliza
                                        })                                  
                                  .then(resp=>{
                                            if(resp.data.codigo == 0){
                                                if(botonFacturar) botonFacturar.blur();

                                                this.Guardar().Fel(resp.data.Serie,resp.data.Factura);
                                                this.GrabarTiempoPromesa(resp.data.Serie,resp.data.Factura);  
                                                this.RecargarAdmisionF()                           
                                                this.$vs.dialog({
                                                    type: 'confirm',
                                                    color: 'success',
                                                    title: 'Facturación',
                                                    acceptText: 'Aceptar',
                                                    cancelText: 'Cancelar',
                                                    text: resp.data.Serie +'-'+resp.data.Factura + ' ' + resp.data.descripcion
                                                })
                                                return 1
                                            }
                                        })
                            if(respuesta == 1){
                                await this.Consulta().ObtenerFacturasCargosAjenos('FACTURAS',1);
                                await this.Consulta().ObtenerFacturasCargosAjenos('FACTURAS',2);    
                                this.Consulta().ActualizarTotales();  
                            }
                        
                    },
                    ValidacionesFactura:()=>{
                        let estado = 1
                        if(!this.ValidarVariable(this.caja.tipoFacturacionSeleccionada)){
                            this.Consulta().MostrarError('Debe elegir el tipo de facturación que desea realizar',3000);  
                            estado = 0
                        }else if(!this.ValidarVariable(this.info.serie) || !this.ValidarVariable(this.info.numeroAdmision)){
                                this.Consulta().MostrarError('Ingrese una admisión para continuar')
                                estado = 0
                        }else if(this.caja.tipoFacturacionSeleccionada != '3'){
                            if(!this.ValidarVariable(this.documento.tipoDocumento) || !this.ValidarVariable(this.documento.noDocumento)){
                                this.Consulta().MostrarError('Ingresar un documento para realizar factura')
                                estado = 0
                            }else if(!this.ValidarVariable(this.documento.validacion)){
                                this.Consulta().MostrarError('Ingresar un documento valido para realizar la factura')
                                estado = 0
                            }else if(this.caja.tipoFacturacionSeleccionada == '2'){
                                if(!this.ValidarVariable(this.facturacion.copagoFacturar) && !this.ValidarVariable(this.facturacion.coaseguroPorcentajeFacturar)){
                                    this.Consulta().MostrarError('Ingrese un monto de copago o un porcentaje de coaseguro para continuar.',4000)
                                    estado = 0
                                }
                            }else if(this.caja.tipoFacturacionSeleccionada == '13'){
                                if(!this.ValidarVariable(this.facturacion.descripcionVariable) || this.facturacion.descripcionVariable.length > 80){
                                    this.Consulta().MostrarError('Ingrese una descripción variable menor a 80 caracteres',4000)
                                    estado = 0
                                }
                            }

                        }else if(this.caja.tipoFacturacionSeleccionada=='3'){
                            if(!this.ValidarVariable(this.info.seguro) || !this.ValidarVariable(this.seguro.noDocumento)){
                                this.Consulta().MostrarError('Debe ingresar una admisión de tipo seguro.',4000)
                                estado = 0
                            }
                            if(!this.ValidarVariable(this.NoAutorizacionSeguro) || !this.ValidarVariable(this.NoPoliza)){
                                this.Consulta().MostrarError('Debe ingresar datos de remisión (No. Autorización y Póliza) .',4000)
                                estado = 0
                            }
                        }

                        return estado
                    },
                    Fel: (serieFactura,numeroFactura)=>{
                        this.axios.post('/app/v1_FacturaElectronica/GeneraFel',
                                        {serieFactura: serieFactura,
                                         numeroFactura: numeroFactura
                                        })                                  
                                  .then(resp=>{
                                            if(resp.data.codigo == 0 && resp.data.json[0].codigo == 0){
                                                this.Consulta().MostrarSatisfactorio(resp.data.json[0].descripcion);
                                                this.GenerarFactura(serieFactura,numeroFactura);                                                                               
                                            }else{
                                                this.Consulta().MostrarError(resp.data.json[0].descripcion);
                                            }
                                        })                                 
                    }
                }
            },
        },
        mounted() {
            this.Consulta().init();                
        },
        activated() {
            window.addEventListener('focus', this.actualizarCajero);   
        },
        deactivated() {
            window.removeEventListener('focus',this.actualizarCajero);
        },
    }
    </script>
    <style>
        .vs-row-especial{
            display: block !important;
        }
        /* Estilo tablas dev express */
        .dx-popup-title {
            background-color: #f4eade !important;
            color: #2f496e !important;
        }

        .dx-list-item>#itemsMenuDrawer {
            color: #2988bc !important;
            background-color: #f4eade !important;
        }

        .dx-list-item-selected>#itemsMenuDrawer {
            color: #f4eade !important;
            background-color: #ed8c72 !important;
        }

        .dx-scrollable-container {
            touch-action: auto !important;
        }

        /*Ancho mínmo de los grid*/
        #Contenido .dx-datagrid {
            min-width: 302px;
        }

        /**Modal actualizacion de peso y talla */
        #popupTallaPeso .vs-popup {
            width: 400px !important;
        }

        .dx-datagrid-headers td {
            vertical-align: middle !important;
        }

        .dx-resizable {
            display: inline-grid;
        }

        .dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed)>td {
            background-color: #00ccff !important;
            color: black !important;
        }

        .dx-datagrid-rowsview .dx-row.dx-data-row.dx-column-lines.dx-state-hover>td {
            background-color: #79e4ff !important;
            color: black !important;
            font-size: 16px;
        }

        .dx-datagrid-headers {
            background-color: linen !important;
            color: black !important;
            font-weight: bold;
        }

        td {
            vertical-align: middle !important;
        }
        /* Fin Estilo tablas dev express */
    </style>
    <style scoped>
    .fuente-label {
        font-size: 10pt;
        white-space: nowrap;
    }

    .cantidad button {
        height: 25px;
        width: 30px;
        border: 1px solid #ccc;
        border-radius: 5px;
    
    }
    
    .cantidad div {
        display: inline-block;
        height: 25px;
        width: 30px;
        text-align: center;
    }

    .quitar_espacio {
        padding: 0;
        margin: 0;        
    }

    .quitar_espacio_buscar > .label{
        padding: 0 !important;
    }

    .money-input{
        white-space: nowrap;
        color: #008FBE;
        border-radius: 5px;
        height: 36px;
    }

    .money-input:focus{
        border:1px solid #008FBE!important;
    }

    .panel-cajero-elegir{
        background-color: rgba(var(--vs-danger), 1) !important; 
    }

    .panel-cajero {
        display:inline-block;
        border-radius: 25px;
        background-color: rgba(var(--vs-primary), 1);
        color: white;
        padding: 5px 65px 5px 10px;
        position: relative;
        width: auto;
        /* top: 12px; */
        overflow: hidden;
        /* margin-right: -14px; */
        font-size: 13px;
        /* right: 50px; */
        /* border-radius: 30px 0 0 0; */
        /* box-shadow: inset 0 2px 0px 1px rgba(0, 0, 0, 0.2); */
        cursor: pointer;
    }

    .seleccionTipoCaja .vs-popup {
        width: 250px !important;
    }
    
    .label-shrink{
        display: block;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        width: 100%;
    }
    
    

    .dx-button.dx-button-warning {
        background-color: #e0d100;
    }

    .sticky {
        position: -webkit-sticky !important;
        /* for browser compatibility */
        position: sticky !important;
        top: 0px;
        z-index: 500;
    }

    .dx-overlay-wrapper.dx-popup-wrapper.dx-dropdowneditor-overlay.dx-popover-wrapper.dx-popover-without-title.dx-lookup-popup-wrapper.dx-lookup-popup-search.dx-position-bottom {
        z-index: 999999 !important;
    }


    .button:hover {
        background-color: blue !important;
    }

    .stickyIntegracion {
        position: -webkit-sticky !important;
        /* for browser compatibility */
        position: sticky !important;
        top: 0px;
        background-color: rgb(117, 177, 255);
        z-index: 500;
    }
    </style>    