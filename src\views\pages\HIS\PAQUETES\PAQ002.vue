<template>
  <div>
      <vs-tabs v-model="activeTab">
          <vs-tab style="background-color: #eaecee" label="Paquete">
              <div style="display: flex" ref="tab-0">
                  <vx-card class="main-card" title="Consulta Paquetes">
                      <vs-table2 max-items="10" pagination search :data="listaPqtA" class="mt-0 mb-0 Colorhears">
                          <template slot="header">
                              <DxButton text="Nuevo Paquete" @click="mostrarBotonSecundario()" type="success" /> &nbsp;
                              <DxButton text="Paquetes Inactivos" @click="ConsultaPaquetesI().Inactivos()" type="normal" />
                          </template>
                          <template slot="thead">
                              <th :width="50">Codigo</th>
                              <th :width="500">Nombre Paquete</th>
                              <th :width="350">Especialidad</th>
                              <th :width="70">Status</th>
                              <th :width="200">Detalle</th>
                          </template>
                          <template slot-scope="{ data }">
                              <tr :key="indextr" v-for="(tr, indextr) in data" @click="seleccionarPaquete($event, tr)" :class="{ highlighted: tr == selectedPaquete }">
                                  <vs-td2>
                                      <label>{{ tr.Codigo }}</label>
                                  </vs-td2>
                                  <vs-td2>
                                      <label>{{ tr.Nombre }}</label>
                                  </vs-td2>
                                  <vs-td2>
                                      <label>{{ tr.Nombre1 }}</label>
                                  </vs-td2>
                                  <vs-td2>
                                      <label>{{ tr.Estatus }}</label>
                                  </vs-td2>
                                  <vs-td>
                                      <div class="Container" style="display: flex; justify-content: center; align-items: center;">
                                          <div>
                                              <DxButton id="saveButton" icon="edit" @click="ConsultaEdit(tr)" type="default" />&nbsp;
                                          </div>
                                          <div>
                                              <DxButton id="buttonExcel" icon="exportxlsx" @click="GenerarExcel(tr)" type="success" />&nbsp;
                                          </div>
                                          <div>
                                              <DxButton id="buttonPDF" icon="pdffile" @click="CargarPDF(tr)" type="danger" />
                                          </div>
                                      </div>
                                  </vs-td>
                              </tr>
                          </template>
                      </vs-table2>
                  </vx-card>
                  &nbsp;
                  <div v-if="isDetailCardVisible">
                    <vx-card  title="Detalle Paquete" class="detail-card">
                      <vs-list-item title="Código:">
                          <label for="" style="color: black">{{ codigo }}</label>
                      </vs-list-item>
                      <vs-list-item title="Paquete:">
                          <label for="" style="color: black">{{ nombrePaquete }}</label>
                      </vs-list-item>
                      <vs-table max-items="5" pagination :data="infoHosp">
                          <template slot="thead">
                              <vs-th>Hospital</vs-th>
                              <vs-th>Precio</vs-th>
                              <vs-th>Médico</vs-th>
                          </template>
                          <template slot-scope="{ data }">
                              <vs-tr :key="indextr" v-for="(tr, indextr) in data">
                                  <vs-td :data="data[indextr].NOMBRE">
                                      {{ data[indextr].NOMBRE }}
                                  </vs-td>

                                  <vs-td :data="data[indextr].PRECIO">
                                      {{ data[indextr].PRECIO }}
                                  </vs-td>

                                  <vs-td :data="tr.MEDAX">
                                      {{ tr.MEDAX ? "Sermesa" : "Cortesia" }}
                                  </vs-td>
                              </vs-tr>
                          </template>
                      </vs-table>

                      <div style="display: flex; justify-content: flex-end; margin-top: 50px">
                          <DxButton icon="remove" @click="CloseDatailCard" type="danger" />
                      </div>
                  </vx-card>
                  </div>
                  
              </div>
          </vs-tab>

          <vs-tab style="background-color: #eaecee" label="Detalle">
              <div ref="tab-1">
                <vx-card>
                        <div style="display: flex;" v-show="!isCreating">
                        </div>
                        <vs-divider position="left" background="primary" color="#D0D3D4">
                            Paquete
                        </vs-divider>
                        <form>
                            <div style="display: flex;  flex-wrap: wrap;">
                                <div>
                                    <DxButton @click="LimpiarDatos()" type="danger" icon="arrowleft" /> &nbsp;
                                </div>
                                <div>
                                    <DxTextBox placeholder="Código" :disabled="true" value="No" v-model="info.Codigo" width="70px" />
                                </div>
                                &nbsp;
                                <div>
                                    <DxTextBox placeholder="Nombre del paquete" :show-clear-button="true" v-model="info.NombrePaquete" width="450px" />
                                </div>
                                &nbsp;
                                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4">
                                    <v-select placeholder="Especialidad" v-model="info.Especialidad" :options="listaEsp" :clearable="false" :append-to-body="true" label="Nombre" :value="option => option.Especialidad" />
                                </div>
                                &nbsp;
                                <div style="margin-left: 10px;" class="p-2" >
                                    <h5 v-show="!isCreating">{{ info.Nombre }}</h5>
                                </div>
                                &nbsp;
                                <div>
                                    <div v-if="mostrarBoton">
                                      <DxButton :disabled="!disabled"  text="Generar" @click="generarNuPQT()" type="success" />
                                    </div>
                                    <DxButton v-show="!isCreating" text="Desactivar" @click="DesactivarPQT()" type="danger" />
                                </div>
                            </div>
                        </form>
                    </vx-card>
                  <vx-card style="margin-top: 5px" v-if="!isVisibleCards">
                      <vs-divider position="left" background="primary" color="#D0D3D4">
                          Detalle Del Paquete
                      </vs-divider>

                      <div class="Contenedor" >
                          <div class="Productos">
                            <h4>Productos</h4>
                              <DxDataGrid ref="dataGrid"   :paging="{ pageSize: 20 }" :data-source="Productos" :remote-operations="false" :allow-column-reordering="true" :row-alternation-enabled="true" :show-borders="true" :width="'100%'" :height="780" @row-click="ActivarPopup" key-expr="Codigo">
                                  <DxDataGridColumn width="auto" data-field="Codigo" caption="Codigo" />
                                  <DxDataGridColumn width="auto" data-field="Nombre" caption="Nombre" />
                                  <DxDataGridDxSearchPanel :visible="true" :highlight-case-sensitive="true" :search-enabled="true" />
                              </DxDataGrid>
                          </div>
                          <div class="Detalle">
                            <h4>Detalle Paquete</h4>
                                 <div>
                                  <DxDataGrid :data-source="listaDetPQt" width="100%" :show-borders="true" @rowRemoving="DeleteProductoaDetalle"  :paging="{ pageSize: 8 }">
                                      <DxDataGridColumn width="auto"  data-field="Codigo" caption="Código" />
                                      <DxDataGridColumn  data-field="Nombre" caption="Nombre" />
                                      <DxDataGridColumn width="auto"  data-field="codigoCategoria" caption="Código" />
                                      <DxDataGridColumn  data-field="NombreCategoria" caption="Categoría" />
                                      <DxDataGridColumn width="auto" data-field="Cantidad" caption="Cantidad" data-type="number" :allow-editing="false" />
                                      <DxDataGridDxSearchPanel :visible="true" :highlight-case-sensitive="true" />
                                      <DxDataGridEditing :allow-deleting="true" :use-icons="true" mode="row" />
                                  </DxDataGrid>
                              </div>
                              <br>
                              <div>
                                  
                                  <DxDataGrid :data-source="listCompPQT" :show-borders="true" :column-auto-width="true"  width="100%" @rowRemoving="DeleteComplementarios" :paging="{ pageSize: 8 }">
                             
  
                                      <DxDataGridColumn width="auto"  :allowEditing="false" data-field="Codigo" caption="Código" />
                                      <DxDataGridColumn data-field="Nombre" :allowEditing="false" caption="Nombre" />
                                      <DxDataGridColumn width="auto"  data-field="Cantidad" :allowEditing="true" caption="Cantidad" data-type="number" />

                                      <DxDataGridDxSearchPanel :visible="true" :highlight-case-sensitive="true" />
                                      <DxDataGridEditing :allow-deleting="true" :allow-updating="true" :use-icons="true" mode="row" />
									
                                  </DxDataGrid>
                              </div>
                          </div>
                      </div>

                      <vs-divider position="left" background="primary" color="#D0D3D4">
                          Precios Hospitales
                      </vs-divider>

                      <div >
                          <div>
                              <div style="display: flex">
                                  <DxSelectBox v-model="info.Hospital" :data-source="listaEmp" value-expr="CodigoBase" display-expr="Nombre" placeholder="Hospitales" width="45%" /> &nbsp;
                                  <DxTextBox v-model="info.Precio" label="Precio:" type="number" /> &nbsp;
                                  <div style="display: flex">
                                      <DxCheckBox v-model="checkBox1" text="Cortesia" @valueChanged="toggleCheckbox('checkBox1')" /> &nbsp;
                                      <DxCheckBox v-model="checkBox2" text="Sermesa" @valueChanged="toggleCheckbox('checkBox2')" />
                                  </div>
                                  &nbsp; &nbsp;
                                  <div>
                                      <!-- DxButton en lugar de vs-button -->
                                      <DxButton type="success" :disabled="!canAddRow" @click="addHospitalPrecio" icon="add" />
                                  </div>
                              </div>
                              <br />
                              <!-- DxDataGrid en lugar de vs-table -->

                              <DxDataGrid :data-source="listaPreHos" :paging="{ pageSize: 5 }" @rowRemoving="DeletePrecios">
                                  <DxDataGridColumn width="auto"  data-field="hospital" />
                                  <DxDataGridColumn  data-field="nombreHospital" />
                                  <DxDataGridColumn width="auto"  data-field="precio" />
                                  <DxDataGridColumn width="auto"  data-field="Tipo De Medico" :calculate-cell-value=" (data) => (data.MEDAX ? 'Cortesía' : 'Sermesa')" />

                                  <DxDataGridDxSearchPanel :visible="true" :highlight-case-sensitive="true" />
                                  <DxDataGridEditing :allow-deleting="true" :use-icons="true" mode="row" />

                              </DxDataGrid>
                          </div>
                      </div>
                      <br />
                      <DxButton id="crear" v-show="!isEditing" text="Guardar" @click="handleButtonClick('crear')" type="success" />&nbsp;
                      <DxButton id="Actualizar" v-show="!isCreating" text="Guardar" @click="handleButtonClick('Actualizar')" type="success" />&nbsp;

                      <DxButton text="Cancelar" @click="LimpiarDatos()" type="danger" />
                  </vx-card>
              </div>
          </vs-tab>
      </vs-tabs>

      <DxPopup :visible.sync="popupActivo" :title="'Cantidades'"  :width="700" :height="265" :resize-enabled="true" :close-on-outside-click="true">
          <div>
              <vx-card>
                  <div>
                      <strong style="color: black">Producto:</strong>
                      <label style="color: #2e86c1; margin-left: 10px">
                          {{ productoSeleccionado ? productoSeleccionado.Codigo : "" }}
                      </label>
                  </div>
                  <div>
                      <strong>Nombre:</strong>
                      <label style="color: #2e86c1; margin-left: 15px">
                          {{ productoSeleccionado ? productoSeleccionado.Nombre : "" }}
                      </label>
                  </div>
                  <div>
                      <strong>Categoría:</strong>

                      <label style="color: #2e86c1; margin-left: 5px">
                          {{ productoSeleccionado ? productoSeleccionado.NombreCategoria : "" }}
                      </label>
                  </div>
              </vx-card>

              <br />
              <div style="display: flex; align-items: center">
                  <DxNumberBox v-model="info.Cantidad" :show-clear-button="true" :show-spin-buttons="true"  @keyup="handleEnterKey">
                  
                  </DxNumberBox>
                 
                  <DxButton text="Confirmar" type="success" @click="confirmarCantidad" />
              </div>
          </div>
      </DxPopup>

      <vs-popup class="Inactivos" title="Paquetes Inactivos" style="z-index: 52000" :active.sync="popupActivoInactivos">
          <vx-card>
              <h2>Listado De Paquetes Inactivos</h2>
              <br />
              <vs-table2 max-items="5" pagination search :data="listaPqtI" class="mt-0 mb-0 Colorhears">
                  <template slot="thead">
                      <th :width="70">Codigo</th>
                      <th> Nombre Paquete</th>
                      <th :width="350">Especialidad</th>
                      <th :width="70">Status</th>
                      <th :width="70" style="text-align: center">Activar</th>
                  </template>
                  <template slot-scope="{ data }">
                      <tr :key="indextr" v-for="(tr, indextr) in data">
                          <vs-td2>
                              <label>{{ tr.Codigo }}</label>
                          </vs-td2>
                          <vs-td2>
                              <label>{{ tr.Nombre }}</label>
                          </vs-td2>
                          <vs-td2>
                              <label>{{ tr.Nombre1 }}</label>
                          </vs-td2>
                          <vs-td2>
                              <label>{{ tr.Estatus }}</label>
                          </vs-td2>
                          <vs-td2>
                              <center>
                                  <vs-button color="success" type="filled" @click="ActivarPaquete(tr)" icon="check"></vs-button>
                              </center>
                          </vs-td2>
                      </tr>
                  </template>
              </vs-table2>
          </vx-card>
      </vs-popup>
  </div>
</template>

<script>

import Swal from "sweetalert2";
import vSelect from "vue-select";

export default {
	components: {
		"v-select": vSelect,
	},
	data() {
		return {
			info: {
				Codigo: null,
				Nombre: null,
				Especialidad: null,
				EstadoPaquete: null,
				Hospital: null,
				NombrePaquete: null,
				Precio: null,
				nombreHospital: null,
				Cantidad: 1,
				Prodcuto: null,
			},

			listaPqtA: [], //Array pqt Activos
			listaPqtI: [], //Array pqt Inactivos
			infoHosp: [], //Array listaPreHos pqt
			listaEsp: [], // Array lista Especialidades	
			listaDetPQt: [],
			listCompPQT: [],
			listaPreHos: [], // Array lista de precios
			listaEmp: [],
			Productos: [],
			searchText: "", // Texto del panel de búsqueda
			focusedRowIndex: null,
			isEditing: false,
			isCreating: false,
			isVisibleCards: true,
			popupActivo: false,
			popupActivoInactivos: false,
			activeTab: 0,
			checkBox1: false,
			checkBox2: false,
			areFieldsEnabled: false,
			productoSeleccionado: null,
			codigo: "",
			nombrePaquete: "",
			mostrarBoton: false,
			selectedPaquete: null,
			isDetailCardVisible: false,

			Status: [{
				value: "A",
				text: "Activo",
			}, {
				value: "I",
				text: "Inactivo",
			}, ],
		};
	},
	computed: {
		canAddRow() {
			return this.info.Hospital !== "" && this.info.Precio > 0;
		},

		sesion() {
			return this.$store.state.sesion;
		},

		disabled() {
			return this.info.NombrePaquete !== "" && this.info.Nombre !== " ";
		},


	},
	methods: {
		// Lstado de Paquetes Activos
		ConsultaPaquetes: function() {
			return {
				Activos: () => {
					this.axios
						.post("/app/v1_paquetes_quirurgicos/obtenerPaquetesActivos", {
							Operacion: "consultaPQTA",
						})
						.then((resp) => {
							if (resp.data.codigo == "0") {
								this.listaPqtA = resp.data.json; // Asegúrate de que 'Paquetes' esté en 'data'
							}
						});
				},
			};
		},

		// Lstado de Paquetes Inactivos
		ConsultaPaquetesI: function() {
			return {
				Inactivos: () => {
					this.axios
						.post("/app/v1_paquetes_quirurgicos/obtenerPaquetesInactivos", {
							Operacion: "consultaPQTI",
						})
						.then((resp) => {
							if (resp.data.codigo == "0") {
								this.listaPqtI = resp.data.json;
							}
							this.popupActivoInactivos = true;
						});
				},
			};
		},

		// Activar PQT Inactivos
		ActivarPaquete(valor) {
			Swal.fire({
				title: "¿Estás seguro?",
				text: "¿Estás seguro de que deseas activar este paquete?",
				icon: "warning",
				showCancelButton: true,
				confirmButtonColor: "#229954",
				cancelButtonColor: "#d33",
				confirmButtonText: "Sí, activar",
				cancelButtonText: "Cancelar",
				customClass: {
					container: "FrentePopup",
					popup: "FrentePopup",
					content: "FrentePopup",
				},
			}).then((result) => {
				if (result.isConfirmed) {
					this.axios
						.post("/app/v1_paquetes_quirurgicos/ActivarPaquete", {
							Operacion: "ActivacionPQTI",
							Codigo: valor.Codigo,
						})
						.then((resp) => {
							if (resp.data && resp.data.codigo == "0") {
								this.ConsultaPaquetesI().Inactivos();
								this.ConsultaPaquetes().Activos();
							}
						});
				}
			});
		},

		// Desactivar PQT Activos

		DesactivarPQT() {
			Swal.fire({
				title: "¿Estás seguro?",
				text: "Estás seguro de inavilitar Paquete ",
				icon: "warning",
				showCancelButton: true,
				confirmButtonColor: "#229954",
				cancelButtonColor: "#e74c3c",
				confirmButtonText: "Sí, desactivar paquete",
			}).then((result) => {
				if (result.isConfirmed) {
					this.axios
						.post("/app/v1_paquetes_quirurgicos/InactivarPaquete", {
							Operacion: "InactivarPQTA",
							Codigo: this.info.Codigo,
						})
						.then(() => {
							this.ConsultaPaquetes().Activos();
						});
					this.changeTab(0);
					Swal.fire(
						"¡Desactivado!",
						"El paquete ha sido desactivado.",
						"success"
					);
				}
			});
		},

		// Seleccionar Pqt
		seleccionarPaquete(event, paquete) {
			if (this.previouslySelectedRow) {
				this.previouslySelectedRow.classList.remove("highlighted");
			}
			this.previouslySelectedRow = event.currentTarget;
			this.selectedPaquete = paquete;
			this.previouslySelectedRow.classList.add("highlighted");

			this.codigo = paquete.Codigo;
			this.nombrePaquete = paquete.Nombre;
			this.consultaPaqueteHospitalPrecio().PrecioHospital(paquete);
		},

		// Consulta  informacion Hospital
		consultaPaqueteHospitalPrecio: function() {
			return {
				PrecioHospital: (valor) => {
					this.axios
						.post("/app/v1_paquetes_quirurgicos/ConsultaHospitalPrecios", {
							Operacion: "detalleHosp1",
							CodigoPqt: valor.Codigo,
						})
						.then((resp) => {
							if (resp.data.codigo == "0") {
								this.infoHosp = resp.data.json;
							}
						});

					this.isDetailCardVisible = true;
				},
			};
		},

		// Consulta Especialidadess
		consultaEspecialidades() {
			this.axios
				.post("/app/v1_paquetes_quirurgicos/Especialidades", {
					Operacion: "listEsp",
				})
				.then((resp) => {
					if (resp.data.codigo == "0") {
						this.listaEsp = resp.data.json;
					}
				});
		},

		//====================================================================================================
		//====================================================================================================
		//Consulta General Editar
		ConsultaEdit(valor) {
			try {
				this.isEditing = true;
				this.isCreating = false;
				this.isVisibleCards = false;

				this.changeTab(1);

				this.consultaEnc(valor);

			} catch (error) {
				throw new Error(
					"Error al ejecutar la consulta. Por favor, verifica los datos e intenta nuevamente."
				);
			}
		},

		// Consulta detalle PQT de Productos y servicos
		consultaDetPQT() {
			return {
				det: (valor) => {
					this.axios
						.post("/app/v1_paquetes_quirurgicos/detallePQTProd", {
							Operacion: "consultaDetyComp",
							SubOpcion: "Det",
							Codigo: valor.Codigo || this.info.codigo,
						})
						.then((resp) => {
							if (resp.data.codigo == "0") {
								// Iterar sobre los elementos de resp.data.json
								resp.data.json.forEach((item) => {
									if (item.VALIDACION === "C") {
										this.listCompPQT.push(item);
									} else if (item.VALIDACION === "P") {
										this.listaDetPQt.push(item);
									}
								});
							}
							this.consultaPreHos(valor);
						});

				},
			};
		},

		consultaPreHos(valor) {
			this.axios
				.post("/app/v1_paquetes_quirurgicos/detallePreHosp", {
					Operacion: "consultaDetyComp",
					SubOpcion: "Hosp",
					CodigoPqt: valor.Codigo,
				})
				.then((resp) => {
					if (resp.data) {
						const fetchedData = resp.data.json;
						fetchedData.forEach((item) => {
							// Formatear el precio a moneda guatemalteca
							const precioFormateado = new Intl.NumberFormat("es-GT", {
								style: "currency",
								currency: "GTQ",
								minimumFractionDigits: 2,
							}).format(item.precio);
							const existingEntry = this.listaPreHos.find(
								(entry) =>
								entry.hospital === item.hospital && entry.MEDAX === item.MEDAX
							);
							if (existingEntry) {
								existingEntry.precio = precioFormateado;
							} else {
								this.listaPreHos.push({
									...item,
									precio: precioFormateado, // Asignar el precio formateado
								});
							}
						});
					}
				});
		},

		consultaEnc(valor) {
			this.axios
				.post("/app/v1_paquetes_quirurgicos/encPQT", {
					Operacion: "consultaDetyComp",
					SubOpcion: "Enc",
					Codigo: valor.Codigo,
				})
				.then((resp) => {
					if (resp.data.json) {
						this.$map({
							objeto: this.info,
							respuestaAxios: resp,
						});
						this.consultaDetPQT().det(valor);
					}
				});
		},

		consultaPro() {
			this.axios
				.post("/app/v1_paquetes_quirurgicos/consultaProd", {
					Operacion: "consultaPro",
				})
				.then((resp) => {
					if (resp.data.codigo == "0") {
						this.Productos = resp.data.json;
					}
					this.consultaEmp();
				});
		},

		consultaEmp() {
			this.axios
				.post("/app/v1_autorizaciones/ConsultaEmpresa", {
					Opcion: "0",
				})
				.then((resp) => {
					if (resp.data.codigo == "0") {
						this.listaEmp = resp.data.json;
					}
					this.consultaEspecialidades();
				});
		},

		generarNuPQT() {
			this.axios
				.post("/app/v1_paquetes_quirurgicos/numeroPQT", {
					Operacion: "numPQT",
					Codigo: this.info.Codigo,
					Nombre: this.info.NombrePaquete,
				})
				.then((resp) => {
					if (resp.data.codigo === 0) {
						const errores = resp.data.json;
						if (errores && errores.length > 0) {
							const error = errores.find((e) => e.codigo === "1");
							if (error) {
								Swal.fire({
									title: "Error",
									text: error.descripcion,
									icon: "error",
								});
								return;
							}
						}

						this.$map({
							objeto: this.info,
							respuestaAxios: resp,
						});

						this.$vs.notify({
							title: "Notificación",
							text: "Numero de Paquete Generado",
							iconPack: "feather",
							icon: "icon-alert-circle",
							color: "success",
						});
					} else if (resp.data.codigo === 1) {
						Swal.fire({
							title: "Error",
							text: "Ocurrió un error en la operación.",
							icon: "error",
						});
					}

					this.isVisibleCards = false;
					this.crearEncabezado().Encabezado();
				})
				.catch(() => {
					Swal.fire({
						title: "Error",
						text: "Ocurrió un error al realizar la solicitud.",
						icon: "error",
					});
				});
		},

		//====================================================================================================
		//====================================================================================================

		confirmarCantidad() {
   
      if (this.productoSeleccionado) {
        let existeEnListaDetPQt = null;
				existeEnListaDetPQt = this.listaDetPQt.find(
					(producto) => producto.Codigo === this.productoSeleccionado.Codigo
				);
				if (existeEnListaDetPQt) {
					// Mostrar notificación Swal si el producto ya está en la lista
					this.mostrarSwal(
						"El producto ya fue agregado al paquete. No puede duplicarse.",
						"Error",
						"error"
					);
					return; // Salir de la función para evitar agregar el producto
				}
				// Cantidad 
				const cantidad = Number(this.info.Cantidad) || 0;

				// Convertir DataSource a array si es necesario
				const listaDetArray = Array.isArray(this.listaDetPQt) ? this.listaDetPQt : this.listaDetPQt.items();
				const listCompArray = Array.isArray(this.listCompPQT) ? this.listCompPQT : this.listCompPQT.items();

				// Actualizar cantidad en listaDetPQt y listCompPQT
				const actualizadoEnListaDetPQt = this.actualizarCantidad(
					listaDetArray,
					this.productoSeleccionado,
					cantidad
				);

				const actualizadoEnListCompPQT = this.actualizarCantidad(
					listCompArray,
					this.productoSeleccionado,
					cantidad
				);

				if (actualizadoEnListaDetPQt || actualizadoEnListCompPQT) {
					// Si el producto ya fue actualizado, cerrar el popup y mostrar mensaje
					const productoActualizado = {
						Codigo: this.productoSeleccionado.Codigo,
						Nombre: this.productoSeleccionado.Nombre,
					};
					this.popupActivo = false;
					this.productoSeleccionado = null;

					this.mostrarSwal(
						"La cantidad de los siguientes productos fue actualizada:",
						"Productos Actualizados",
						"success", [productoActualizado]
					);

					this.DetalleProducto_confirmar();
				} else {
					// Validar y agregar como nuevo producto si no existe y es válido
					this.validarProductoComplementario({
						Codigo: this.productoSeleccionado.Codigo,
						Cantidad: cantidad,
					}).then((esValido) => {
						if (esValido) {
							const productoNuevo = {
								Codigo: this.productoSeleccionado.Codigo,
								Nombre: this.productoSeleccionado.Nombre,
								codigoCategoria: this.productoSeleccionado.codigoCategoria,
								NombreCategoria: this.productoSeleccionado.NombreCategoria,
								Cantidad: cantidad,
							};

							// Agregar a listaDetPQt
							listaDetArray.push(productoNuevo);
							this.popupActivo = false;
							this.productoSeleccionado = null;
							this.info.Cantidad = 1; // Resetear cantidad
							this.mostrarSwal(
								"El siguiente producto fue agregado al paquete:",
								"Producto Agregado",
								"success", [productoNuevo]
							);

							this.DetalleProducto_confirmar();
						}
					});
				}
			}
		},

		DetalleProducto_confirmar() {
			let listaDetPQt = this.listaDetPQt;
			let listCompPQT = this.listCompPQT;
      
			if (!Array.isArray(listaDetPQt)) {
		
				listaDetPQt = []; // Inicializar como un array vacío si no lo es
			}

			if (!Array.isArray(listCompPQT)) {
			
				listCompPQT = []; // Inicializar como un array vacío si no lo es
			}
			let DetalleCompleto = listaDetPQt.concat(listCompPQT);

			let DetalleActualizado = DetalleCompleto.map((producto) => {
				return {
					Codigo: producto.Codigo,
					Cantidad: producto.Cantidad,
				};
			});
			this.axios.post("/app/v1_paquetes_quirurgicos/UpdateDetallePaquete", {
				Operacion: "UpdatePQT",
				SubOpcion: "DET",
				SubOpcion_Opcion: "A_detPQT",
				Codigo: this.info.Codigo,
				Detalle: DetalleActualizado,
			}).then((resp) => {
				if (resp.data.codigo == '0') {
					this.detalle()
						.then(() => {
							this.$vs.notify({
								icon: 'icon-alert-circle',
								text: 'Producto Agregado',
								iconPack: 'feather',
								color: 'success'
							});
						})
						.catch(() => {
							this.$vs.notify({
								icon: 'icon-alert-circle',
								text: 'Hubo un error',
								iconPack: 'feather',
								color: 'danger'
							});
						});
				}
			});
		},

		detalle: async function() {
			try {
				const resp = await this.axios.post("/app/v1_paquetes_quirurgicos/detallePQTProd", {
					Operacion: "consultaDetyComp",
					SubOpcion: "Det",
					Codigo: this.info.Codigo,
				});

				if (resp.data.codigo == "0") {
					let nuevosCompPQT = [];
					let nuevosDetPQT = [];

					resp.data.json.forEach((item) => {
						if (item.VALIDACION === "C") {
							nuevosCompPQT.push(item);
						} else if (item.VALIDACION === "P") {
							nuevosDetPQT.push(item);
						}
					});

					// Actualizar listas
					this.listCompPQT = nuevosCompPQT

					this.listaDetPQt = nuevosDetPQT
				}
			} catch (error) {
				this.$vs.notify({
							icon: 'icon-alert-circle',
							text: 'Ocurrio un error',
							iconPack: 'feather',
							color: 'danger'
						});
			}
		},

		actualizarCantidad(lista, producto, cantidad) {
			// Convertir DataSource a array si es necesario
			const listaArray = Array.isArray(lista) ? lista : lista.items();

			const item = listaArray.find((item) => item.Codigo === producto.Codigo);
			if (item) {
				item.Cantidad = (Number(item.Cantidad) || 0) + (Number(cantidad) || 0);
				return true;
			}
			return false;
		},

		validarProductoComplementario(valor) {
			return new Promise((resolve) => {
				this.axios
					.post("/app/v1_paquetes_quirurgicos/validarProdComp", {
						Operacion: "CompEval",
						Codigo: valor.Codigo,
						Cantidad: valor.Cantidad,
					})
					.then((resp) => {
						if (resp.data.codigo == "0") {
							resp.data.json.forEach((productoNuevo) => {
								const listCompArray = Array.isArray(this.listCompPQT) ?
									this.listCompPQT :
									this.listCompPQT.items();

								const existente = listCompArray.find(
									(item) => item.Codigo === productoNuevo.Codigo
								);

								if (existente) {
									existente.Cantidad =
										(Number(existente.Cantidad) || 0) + (Number(productoNuevo.Cantidad) || 0);
								} else {
									listCompArray.push({
										...productoNuevo,
										Cantidad: Number(productoNuevo.Cantidad) || 0,
									});
								}
							});
							resolve(true);
						} else {
							resolve(false);
						}
					});
			});
		},

		mostrarSwal(
			mensaje,
			titulo = "Producto Actualizado",
			icon = "success",
			productos = []
		) {
			// Generar el detalle de los productos solo si hay elementos en la lista
			const detalleProductos =
				productos.length > 0 ?
				"\n\n" +
				productos
				.map((producto) => `• ${producto.Codigo}: ${producto.Nombre}`)
				.join("\n") :
				"";
			Swal.fire({
				title: titulo,
				text: `${mensaje}${detalleProductos}`,
				icon: icon,
				confirmButtonText: "Aceptar",
				confirmButtonColor: "#229954",
				customClass: {
					container: "FrentePopup",
					popup: "FrentePopup",
					content: "FrentePopup",
				},
			});
		},



		ActivarPopup(e) {
			// Obtén los datos de la fila clickeada
			const filaSeleccionada = e.data;
			if (filaSeleccionada) {
				// Guarda los datos de la fila seleccionada
				this.productoSeleccionado = filaSeleccionada;
				this.popupActivo = true; // Activa el popup
			} else {
				// Si no hay datos seleccionados, desactiva el popup
				this.productoSeleccionado = null;
				this.popupActivo = false;
			}
		},


		handleEnterKey(event) {
			if (event.key === "Enter") {
				this.confirmarCantidad();
			}
		},

		DeleteProductoaDetalle(e) {
			const tr = e.data;
			this.axios.post("/app/v1_paquetes_quirurgicos/Eliminar_Primario_secundario", {
					Operacion: "Eliminar_2",
					Producto: tr.Codigo,
					Codigo: this.info.Codigo
				})
				.then((resp) => {
					if (resp.data.codigo == '0') {
						this.$vs.notify({
							icon: 'icon-alert-circle',
							text: 'Producto Eliminado',
							iconPack: 'feather',
							color: 'success'
						});
						this.detalle()
							.then(() => {})
							.catch(() => {});
					}
				})
		},

		DeleteComplementarios(e) {
			const codigoAEliminar = e.data.Codigo; // Accede al código de la fila
			this.listCompPQT = this.listCompPQT.filter(
				(item) => item.Codigo !== codigoAEliminar
				
			);
			this.$vs.notify({
							icon: 'icon-alert-circle',
							text: 'Hospital Eliminado',
							iconPack: 'feather',
							color: 'success'
			});
			// Detén la acción predeterminada de eliminación para evitar conflictos
			e.cancel = true;
		},

		DeletePrecios(e){
			const tr = e.data;
			this.axios.post("/app/v1_paquetes_quirurgicos/Eliminar_Precios", {
					Operacion: "Eliminar_Precios",
					Hospital: tr.hospital,
					Medax: tr.MEDAX,
					Codigo: this.info.Codigo
				}).then((resp)=>{
					if(resp.data.codigo == '0'){
						this.$vs.notify({
							icon: 'icon-alert-circle',
							text: 'Hospital Eliminado',
							iconPack: 'feather',
							color: 'success'
						});
					}
				}).catch(()=>{
					this.$vs.notify({
							icon: 'icon-alert-circle',
							text: 'Error al Eliminar Producto',
							iconPack: 'feather',
							color: 'danger'
					});
				})
		},

		//====================================================================================================
		//====================================================================================================

		addHospitalPrecio() {
			// Buscar el hospital seleccionado en listaEmp
			const hospital = this.listaEmp.find(
				(item) => item.CodigoBase === this.info.Hospital
			);
			if (!hospital) {
				Swal.fire({
					title: "Error",
					text: "El hospital seleccionado no existe.",
					icon: "error",
					confirmButtonText: "Aceptar",
				});
				return;
			}
			// Definir MEDAX basado en el checkbox seleccionado
			let medax = null;
			if (this.checkBox1) {
				medax = true;
			} else if (this.checkBox2) {
				medax = false;
			}
			if (medax === null) {
				Swal.fire({
					title: "Error",
					text: "Debe seleccionar un tipo de médico antes de agregar el hospital.",
					icon: "error",
					confirmButtonText: "Aceptar",
				});
				return;
			}
			// Formatear el precio
			const precioFormateado = new Intl.NumberFormat("es-GT", {
				style: "currency",
				currency: "GTQ",
				minimumFractionDigits: 2,
			}).format(this.info.Precio);
			// Verificar si el hospital ya está agregado con el mismo MEDAX
			const existingEntry = this.listaPreHos.find(
				(item) => item.hospital === this.info.Hospital && item.MEDAX === medax
			);
			if (existingEntry) {
				Swal.fire({
					title: "Advertencia",
					text: "El hospital ya se encuentra agregado con el mismo Tipo de Médico. ¿Desea actualizar el Precio?",
					icon: "warning",
					showCancelButton: true,
					confirmButtonText: "Actualizar",
					confirmButtonColor: "#27AE60",
					cancelButtonText: "Cancelar",
					cancelButtonColor: "#E74C3C",
				}).then((result) => {
					if (result.isConfirmed) {
						existingEntry.precio = precioFormateado; // Actualizar con formato
						existingEntry.Status = this.info.Status; // Asegúrate de que Status existe en info
						this.clearInputs();
					} else if (result.dismiss) {
						this.clearInputs();
					}
				});
			} else {
				const medaxCount = this.listaPreHos.filter(
					(item) =>
					item.CodigoBase === this.info.Hospital && item.MEDAX !== medax
				).length;
				if (medaxCount >= 2) {
					Swal.fire({
						title: "Error",
						text: "No se puede agregar más de dos veces el mismo hospital con diferentes parámetros de MEDAX.",
						icon: "error",
						confirmButtonText: "Aceptar",
					});
					return;
				}
				// Agregar nueva entrada
				const newRow = {
					hospital: this.info.Hospital,
					nombreHospital: hospital ? hospital.Nombre : "",
					precio: precioFormateado, // Agregar con formato
					MEDAX: medax,
				};
				this.listaPreHos.push(newRow);
				this.clearInputs();
			}
		},

		clearInputs() {
			this.info.Hospital = null;
			this.info.Precio = null;
			this.checkBox1 = false;
			this.checkBox2 = false;
		},

		toggleCheckbox(selectedCheckbox) {
			if (selectedCheckbox === "checkBox1" && this.checkBox2) {
				this.checkBox1 = true;
				this.checkBox2 = false;
			} else if (selectedCheckbox === "checkBox2" && this.checkBox1) {
				this.checkBox1 = false;
				this.checkBox2 = true;
			}
		},

		EliminarHospitalPrecio(index) {
			this.listaPreHos.splice(index, 1);
		},

		//====================================================================================================
		//====================================================================================================
		crearEncabezado() {
			return {
				Encabezado: () => {
					this.axios
						.post("/app/v1_paquetes_quirurgicos/UpdateEncabezado", {
							Operacion: "UpdatePQT",
							SubOpcion: "ENC",
							SubOpcion_Opcion: "C_PQT",
							Codigo: this.info.Codigo,
							NombrePaquete: this.info.NombrePaquete,
							Especialidad: this.info.Especialidad.Especialidad || this.info.Especialidad, // Asegura que se envíe correctamente
							Estatus: "A",
						})
						.then((resp) => {
							if (resp.data.json) {
								this.$map({
									objeto: this.info,
									respuestaAxios: resp,
								});
							}
						});
				},
			};
		},

		handleButtonClick(buttonId) {
			// Llama a DetalleProducto en ambos casos
			this.DetalleProducto();
			if (buttonId === "crear") {
				// Acciones específicas para el botón "crear"
				this.ActualizarEncabezado().Encabezado();
				this.changeTab(0);
				this.LimpiarDatos();
			} else if (buttonId === "Actualizar") {
				// Acciones específicas para el botón "Actualizar"
				this.ActualizarEncabezado().Encabezado();
			}
		},

		ActualizarEncabezado() {
			return {
				Encabezado: () => {
					this.axios
						.post("/app/v1_paquetes_quirurgicos/UpdateEncabezado", {
							Operacion: "UpdatePQT",
							SubOpcion: "ENC",
							SubOpcion_Opcion: "A_PQT",
							Codigo: this.info.Codigo,
							NombrePaquete: this.info.NombrePaquete,
							Especialidad: this.info.Especialidad.Especialidad || this.info.Especialidad, // Asegura que se envíe correctamente
							Estatus: "A",
						})
						.then((resp) => {
							if (resp.data.json) {
								this.$map({
									objeto: this.info,
									respuestaAxios: resp,
								});
								this.ConsultaPaquetes().Activos();
							}
						});
				},
			};
		},

		DetalleProducto() {
			let listaDetPQt = this.listaDetPQt;
			let listCompPQT = this.listCompPQT;
			if (!Array.isArray(listaDetPQt)) {

				listaDetPQt = []; // Inicializar como un array vacío si no lo es
			}
			if (!Array.isArray(listCompPQT)) {
				listCompPQT = []; // Inicializar como un array vacío si no lo es
			}
			const DetalleCompleto = listaDetPQt.concat(listCompPQT);
			const DetalleActualizado = DetalleCompleto.map((producto) => {
				let Nuevo = {};
				Nuevo.Codigo = producto.Codigo;
				Nuevo.Cantidad = producto.Cantidad;
				return Nuevo;
			});
			this.axios.post("/app/v1_paquetes_quirurgicos/UpdateDetallePaquete", {
				Operacion: "UpdatePQT",
				SubOpcion: "DET",
				SubOpcion_Opcion: "A_detPQT",
				Codigo: this.info.Codigo,
				Detalle: DetalleActualizado,
			}).then((resp) => {
				if (resp.data.codigo == "0") {
					this.detalle()
						.then(() => {
							this.$vs.notify({
								icon: "icon-alert-circle",
								text: "Paquete Actualizado",
								iconPack: "feather",
								color: "success",
							});
						})
						.catch(() => {
							this.$vs.notify({
								icon: "icon-alert-circle",
								text: "Hubo un error",
								iconPack: "feather",
								color: "danger",
							});
						});
				}
			});
			this.PreciosPaquete();
		},


		PreciosPaquete() {
			this.listaDePrecios = this.listaPreHos.map((precio) => {
				let PrecioNuevo = {};
				PrecioNuevo.hospital = precio.hospital;
				// Convertir el precio de formato de moneda a string numérico sin decimales ".00"
				if (typeof precio.precio === "string") {
					PrecioNuevo.precio = precio.precio
						.replace(/[Q,\s]/g, "") // Q, separadores, y espacios
						.replace(".00", ""); // Eliminar específicamente ".00" si está presente
				} else {
					// Si el precio ya es número, eliminar decimales ".00" al convertirlo a string
					PrecioNuevo.precio =
						precio.precio % 1 === 0 ?
						precio.precio.toString() // Es entero, convertir directamente a string
						:
						precio.precio.toFixed(2).replace(".00", ""); // Eliminar ".00" si es decimal
				}
				PrecioNuevo.MEDAX = precio.MEDAX;
				return PrecioNuevo;
			});
			this.axios.post("/app/v1_paquetes_quirurgicos/UpdatePrecios", {
				Operacion: "UpdatePQT",
				SubOpcion: "PRE",
				SubOpcion_Opcion: "A_PrePQT",
				CodigoPqt: this.info.Codigo,
				PreciosHospital: this.listaDePrecios,
			});
			this.ConsultaPaquetes().Activos();
		},

		changeTab(index) {
			this.activeTab = index;
		},

		CloseDatailCard() {
			this.isDetailCardVisible = false;
			if (this.previouslySelectedRow) {
				this.previouslySelectedRow.classList.remove("highlighted");
			}
			this.selectedPaquete = null;
			this.previouslySelectedRow = null;
		},

		mostrarBotonSecundario() {
			this.mostrarBoton = true;
			this.isCreating = true;
			this.isEditing = false;
			this.changeTab(1);
			this.consultaEspecialidades();
		},

		LimpiarDatos() {
			this.listaDetPQt = []
			this.listCompPQT = []
			this.listaPreHos = []
			this.info.EstadoPaquete = null;
			this.info.NombrePaquete = null;
			this.info.Codigo = null;
			this.info.Nombre = null;
			this.isDetailCardVisible = false;
			this.isVisibleCards = true;
			this.info.Especialidad = null;
			this.mostrarBoton = false;
			this.ConsultaPaquetes().Activos();
			this.changeTab(0);
		},

		GenerarExcel(valor) {
			this.$reporte_modal({
				Nombre: "Paquetes Quirurgicos",
				Formato: "EXCEL",
				Opciones: {
					Operacion: "Rep_No1_Excel",
					Codigo: valor.Codigo,
				},
			});
		},

		CargarPDF(valor) {
			this.$reporte_modal({
				Nombre: "Paquetes Quirurgicos",
				Opciones: {
					Operacion: "Rep_No2_PDF",
					Codigo: valor.Codigo,
				},
			});
		},
	},
	mounted() {
		this.ConsultaPaquetes().Activos();
		this.consultaPro();
	},
}; 
</script>

<style scoped>
.large-checkbox {
  --vs-checkbox-size: 25px;
  font-size: 17px;
}

.large-checkbox .vs-checkbox--input {
  width: var(--vs-checkbox-size);
  height: var(--vs-checkbox-size);
}

.large-checkbox .vs-checkbox--icon {
  font-size: var(--vs-checkbox-size);
}
</style>

<style>
.dx-datagrid-rowsview
  .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed)
  > td {
  background-color: #d4efdf !important;
  color: black !important;
}

.dx-datagrid-rowsview .dx-row.dx-data-row.dx-column-lines.dx-state-hover > td {
  background-color: #d6eaf8 !important;
  color: #273746 !important;
  font-size: 16px;
}

.dx-datagrid-headers {
  background-color:#2980b9 !important;
  color: white !important;
}

.table-container {
  flex: 1;
  transition: flex 0.3s;
}

.detail-card {
  width: 500px; /* Ajusta este valor según sea necesario */
  flex-shrink: 0;
}

.v-select .dropdown {
  z-index: 9999 !important ; /* Asegúrate de que el dropdown esté por encima */
  position: relative !important; /* Esto es necesario para que el z-index funcione correctamente */
}

.v-select {
  height: 40px; /* Cambia este valor según lo que necesites */
  font-size: 16px; /* Esto ajustará el tamaño del texto dentro del select */
}

.v-select .vs__dropdown-toggle {
  height: 40px; /* Asegúrate de que el contenedor del dropdown también tenga la altura adecuada */
}

.Ancho {
  width: 135%;
}

.Cantidad.con-vs-popup .vs-popup {
  width: 750px !important;
  min-width: 350px;
}

.Inactivos.con-vs-popup .vs-popup {
  width: 1500px !important;
  min-width: 350px;
}

.FrentePopup {
  z-index: 52001 !important;
}

.OverFlow .contenedor-tabla {
  overflow-y: auto !important;
  max-height: 270px !important;
}

.highlighted {
  background-color: #d4efdf !important; /* Cambia este color*/
}
.dx-icon-trash {
  color: #e74c3c !important; /* Cambia 'red' por el color que prefieras */
}

.Colorhears thead {

background-color:#2980b9 !important;
color: white !important;
}

.Contenedor {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.Productos, .Detalle {
  flex: 1 1 48%; /* Cada div ocupará hasta el 48% del ancho disponible */
  box-sizing: border-box;
  margin: 2px;
}

@media (max-width: 768px) {
  .Productos, .Detalle {
    flex: 1 1 100%; /* Cuando el ancho de la pantalla sea menor a 768px, los divs ocuparán el 100% */
  }
}
</style>