<template>
<div>
    <div v-if="(consulta && hojaAnestesia !== null && hojaAnestesia !== '' && recordEncontrado) || (!consulta) || (consulta && recordEncontrado)">
        <div class="p-2" id="record">
            <vs-row vs-justify="flex-end" vs-align="center" class="pb-2">
                <ExpedienteGridToolBar v-bind="$props" :reportParam="reportParams" :visible="consulta" :pdfExportItems="[{text:'Record operatorio', reportName: 'Record Operatorio'}]" :showItems="['exportPdf']" />
            </vs-row>
            <form @submit="handleSubmit">
                <DxForm :form-data.sync="encabezado" labelMode="floating" :ref="dataEncabezadoRefKey" :read-only="consulta || recordEncontrado" :show-validation-summary="true">
                    <DxGroupItem item-type="group">

                        <DxGroupItem item-type="group" caption="Códigos California">
                            <DxItem data-field="CodigosCalifornia" :is-required="true" editor-type="dxTagBox" :editor-options="{ dataSource: codigosCaliforniaDataSource, displayExpr:'Descripcion', showSelectionControls: true, searchEnabled: true, applyValueMode: 'useButtons' }">
                                <DxLabel :text="'Códigos'" />
                            </DxItem>
                        </DxGroupItem>

                        <DxGroupItem item-type="group" caption="Personal médico">
                            <DxGroupItem item-type="group" :col-count-by-screen="colCountByScreenEspecialidad" caption="Médicos tratantes">
                                <DxItem data-field="MedicoTratante1" :is-required="true" editor-type="dxTextBox" :editor-options="{ maxLength: 1000 }">
                                    <DxLabel :text="'Médico tratante 1'" />
                                </DxItem>
                                <DxItem data-field="MedicoTratante2" editor-type="dxTextBox" :editor-options="{ maxLength: 1000 }">
                                    <DxLabel :text="'Médico tratante 2'" />
                                </DxItem>
                            </DxGroupItem>

                            <DxGroupItem item-type="group" :col-count-by-screen="colCountByScreenEspecialidad" caption="Cirujanos de apoyo">
                                <DxItem data-field="CirujanoApoyo1" editor-type="dxTextBox" :editor-options="{ maxLength: 1000 }">
                                    <DxLabel :text="'Cirujano de apoyo 1'" />
                                </DxItem>
                                <DxItem data-field="CirujanoApoyo2" editor-type="dxTextBox" :editor-options="{ maxLength: 1000 }">
                                    <DxLabel :text="'Cirujano de apoyo 2'" />
                                </DxItem>
                                <DxItem data-field="CirujanoApoyo3" editor-type="dxTextBox" :editor-options="{ maxLength: 1000 }">
                                    <DxLabel :text="'Cirujano de apoyo 3'" />
                                </DxItem>
                                <DxItem data-field="CirujanoApoyo4" editor-type="dxTextBox" :editor-options="{ maxLength: 1000 }">
                                    <DxLabel :text="'Cirujano de apoyo 4'" />
                                </DxItem>
                            </DxGroupItem>

                            <DxGroupItem item-type="group" :col-count-by-screen="colCountByScreenEspecialidad" caption="Instrumentistas">
                                <DxItem data-field="Instrumentista1" :is-required="true" editor-type="dxTextBox" :editor-options="{ maxLength: 1000 }" />
                                <DxItem data-field="Instrumentista2" editor-type="dxTextBox" :editor-options="{ maxLength: 1000 }" />
                            </DxGroupItem>

                            <DxGroupItem item-type="group" :col-count-by-screen="colCountByScreenEspecialidad" caption="Circulantes">
                                <DxItem data-field="Circulante1" :is-required="true" editor-type="dxTextBox" :editor-options="{ maxLength: 1000 }" />
                                <DxItem data-field="Circulante2" editor-type="dxTextBox" :editor-options="{ maxLength: 1000 }" />
                            </DxGroupItem>
                        </DxGroupItem>

                        <DxGroupItem item-type="group" :col-count-by-screen="colCountByScreenEspecialidad" caption="Detalles">
                            <DxItem data-field="ViaAcceso" :is-required="true" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000 }">
                                <DxLabel :text="'Via de acceso'" />
                            </DxItem>
                            <DxItem data-field="Procedimiento" :is-required="true" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000 }" />

                            <DxItem data-field="Hallazgos" :is-required="true" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000 }" />
                            <DxItem data-field="TecnicaCierre" :is-required="true" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000 }">
                                <DxLabel :text="'Técnica de cierre'" />
                            </DxItem>
                        </DxGroupItem>

                        <DxGroupItem :col-count-by-screen="colCountByScreenEspecialidad">
                            <DxGroupItem caption="Anestesia">
                                <DxItem data-field="CodigoAnestesiologo" :is-required="true" editor-type="dxLookup" :editor-options="{ dataSource: anestesiologos, displayExpr: 'Descripcion', valueExpr:'Codigo', showCancelButton: false, dropDownOptions: dropDownOptions }">
                                    <DxLabel :text="'Anestesiólogo'" />
                                </DxItem>
                                <DxItem data-field="IdTipoAnestesia" :is-required="true" editor-type="dxSelectBox" :editor-options="{ items: (!consulta && !recordEncontrado)? tipoAnestesia : anestesiaSeleccionada, displayExpr: 'Nombre', valueExpr:'Id', defaultValue: (!consulta && !recordEncontrado)? null : anestesiaSeleccionada[0] }">
                                    <DxLabel :text="'Tipo anestesia'" />
                                </DxItem>
                                <!-- <DxItem v-if="consulta || recordEncontrado" data-field="IdTipoAnestesia" :is-required="true" editor-type="dxSelectBox" :editor-options="{ items: anestesiaSeleccionada, displayExpr: 'Nombre', valueExpr:'Id', defaultValue: anestesiaSeleccionada[0] }">
                                    <DxLabel :text="'Tipo anestesia'" />
                                </DxItem> -->
                            </DxGroupItem>

                            <DxGroupItem caption="Patología">
                                <DxItem data-field="CodigoPatologo" editor-type="dxLookup" :editor-options="{ dataSource: patologos, displayExpr: 'Descripcion', valueExpr:'Codigo', showCancelButton: false, dropDownOptions: dropDownOptions }">
                                    <DxLabel :text="'Patólogo'" />
                                </DxItem>
                                <DxItem data-field="PiezaQuirurgica" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000 }">
                                    <DxLabel :text="'Pieza quirúrgica'" />
                                </DxItem>
                            </DxGroupItem>
                        </DxGroupItem>

                    </DxGroupItem>

                    <DxGroupItem item-type="group" :col-count-by-screen="colCountByScreen" caption="Materiales">
                        <DxGroupItem item-type="group" caption="Compresas 1">
                            <DxItem data-field="Compresas1" :is-required="true" editor-type="dxRadioGroup" :editor-options="{ dataSource: recuento, layout:'horizontal', displayExpr: 'name', valueExpr:'value', onValueChanged: '' }">
                                <DxLabel :visible="false" />
                            </DxItem>
                            <DxItem data-field="ObservacionesCompresas1" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000 }">
                                <DxLabel :text="'Observaciones'" />
                            </DxItem>
                        </DxGroupItem>
                        <DxGroupItem item-type="group" caption="Compresas 2">
                            <DxItem data-field="Compresas2" :is-required="true" editor-type="dxRadioGroup" :editor-options="{ dataSource: recuento, layout:'horizontal', displayExpr: 'name', valueExpr:'value', onValueChanged: '' }">
                                <DxLabel :visible="false" />
                            </DxItem>
                            <DxItem data-field="ObservacionesCompresas2" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000 }">
                                <DxLabel :text="'Observaciones'" />
                            </DxItem>
                        </DxGroupItem>
                        <DxGroupItem item-type="group" caption="Gasas">
                            <DxItem data-field="Gasas" :is-required="true" editor-type="dxRadioGroup" :editor-options="{ dataSource: recuento, layout:'horizontal', displayExpr: 'name', valueExpr:'value', onValueChanged: '' }">
                                <DxLabel :visible="false" />
                            </DxItem>
                            <DxItem data-field="ObservacionesGasas" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000 }">
                                <DxLabel :text="'Observaciones'" />
                            </DxItem>
                        </DxGroupItem>
                    </DxGroupItem>

                    <DxGroupItem item-type="group" caption="Observaciones operación">
                        <DxItem data-field="Observaciones" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000 }" />
                    </DxGroupItem>

                    <DxGroupItem item-type="group" :col-count="2" v-if="!consulta && !recordEncontrado">
                        <DxButtonItem :button-options="submitButtonOptions" horizontal-alignment="center" verical-alignment="center" />
                        <DxButtonItem :button-options="clearButtonOptions" horizontal-alignment="center" verical-alignment="center" />
                    </DxGroupItem>

                </DxForm>
            </form>
        </div>
    </div>
    <vs-row id="texto" class="p-2" v-if="(hojaAnestesia === '' || hojaAnestesia === null) && consulta && !recordEncontrado">
        <p>Seleccione una fecha para mostrar información</p>
    </vs-row>
    <vs-row id="texto" class="p-2" v-if="(consulta && hojaAnestesia !== '' && hojaAnestesia !== null && !recordEncontrado)">
        <p>La admisión no cuenta con información para mostrar</p>
    </vs-row>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'
import {
    DxForm,
    DxItem,
    DxLabel,
    DxGroupItem,
    DxButtonItem
} from 'devextreme-vue/form'
import 'devextreme-vue/tag-box';
import 'devextreme-vue/radio-group';
import ExpedienteGridToolBar from './ExpedienteGridToolBar.vue'

const dataRegistroRefKey = 'encabezadoGrid'
const dataEncabezadoRefKey = 'encabezado-form'

export default {
    name: 'RecordOperatorio',
    components: {
        DxForm,
        DxItem,
        DxLabel,
        DxGroupItem,
        DxButtonItem,
        ExpedienteGridToolBar,
    },
    data() {
        return {
            DefaultDxGridConfiguration,
            dataEncabezadoRefKey,
            dataRegistroRefKey,
            encabezado: {
                CodigosCalifornia: [],
                MedicoTratante1: '',
                MedicoTratante2: '',
                CirujanoApoyo1: '',
                CirujanoApoyo2: '',
                CirujanoApoyo3: '',
                CirujanoApoyo4: '',
                Instrumentista1: '',
                Instrumentista2: '',
                Circulante1: '',
                Circulante2: '',
                CodigoAnestesiologo: '',
                IdTipoAnestesia: '',
                ViaAcceso: '',
                Procedimiento: '',
                Hallazgos: '',
                TecnicaCierre: '',
                CodigoPatologo: '',
                PiezaQuirurgica: '',
                Observaciones: ''
            },
            detalle: [],
            submitButtonOptions: {
                text: 'Guardar datos',
                type: 'success',
                icon: 'save',
                width: 200,
                useSubmitBehavior: true,
                disabled: false
            },
            clearButtonOptions: {
                text: 'Limpiar registro',
                type: 'danger',
                icon: 'trash',
                width: 200,
                useSubmitBehavior: false,
                disabled: false,
                onClick: this.limpiarRegistro
            },
            anestesiologos: [],
            patologos: [],
            dropDownOptions: {
                showTitle: false,
                maxHeight: '300px',
                hideOnOutsideClick: true,
            },
            recuento: [{
                name: 'Completo',
                value: 1
            }, {
                name: 'Incompleto',
                value: 0
            }],
            tipoAnestesia: [],
            codigosCalifornia: [],
            codigosCaliforniaDataSource: {
                paginate: true,
                pageSize: 10,
                sort: 'Codigo',
                // loadMode: 'raw',
                byKey: (e) => {
                    return this.codigosCalifornia.Codigo === e ? this.codigosCalifornia.Descripcion : ''
                },
                load: (e) => {
                    if (e.take) {
                        if (e.skip === e.take) {
                            e.take = e.take + 10
                        }
                    }
                    return this.$filtrado_avanzado_palabras(e.searchValue, this.codigosCalifornia, 'Descripcion', e.skip, e.skip + e.take)
                }
            },
            codigosCaliforniaSeleccionados: [],
            recordEncontrado: false,
            anestesiaSeleccionada: [],
            mounted: 0
        }
    },
    props: {
        SerieAdmision: null,
        CodigoAdmision: null,
        NumeroExpediente: null,

        StatusAdmision: null,
        consulta: null, //Variable para indicar si el componente se usará para consulta -> 'true' o para ingreso -> 'false'
        hojaAnestesia: null, //Variable para obtener la hoja cuando se utilice para consulta
    },
    methods: {
        handleSubmit(e) {
            e.preventDefault()
            if (this.SerieAdmision && this.CodigoAdmision) {
                this.encabezado = this.$reemplazar_tabulares_objeto(this.encabezado, /\t/g, '   ')
                let postData = {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    ...this.encabezado,
                    Recuento: this.mapRecuento()
                }
                this.axios.post("/app/v1_ExpedienteEvolucion/InsertarRecordOperatorio", {
                    ...postData
                }).then(() => {
                    this.getRecord()
                })
            }
        },
        calcularTiempo(milisegundos) {
            var hours = ((milisegundos / 1000 / 60) / 60);
            var rhours = Math.floor(hours);
            var minutes = (hours - rhours) * 60;
            var rminutes = Math.round(minutes);
            if (rminutes === 60) {
                rhours++
                rminutes = 0
            }
            return rhours + ":" + (rminutes < 10 ? "0" + rminutes : rminutes)
        },
        limpiarRegistro() {

            this.encabezado = {
                CodigosCalifornia: null,
                MedicoTratante1: '',
                MedicoTratante2: '',
                CirujanoApoyo1: '',
                CirujanoApoyo2: '',
                CirujanoApoyo3: '',
                CirujanoApoyo4: '',
                Instrumentista1: '',
                Instrumentista2: '',
                Circulante1: '',
                Circulante2: '',
                CodigoAnestesiologo: '',
                IdTipoAnestesia: '',
                ViaAcceso: '',
                Procedimiento: '',
                Hallazgos: '',
                TecnicaCierre: '',
                CodigoPatologo: '',
                PiezaQuirurgica: '',
                Observaciones: ''
            }
        },
        getCodigosCalifornia() {
            this.axios.post('/app/v1_ExpedienteEvolucion/CatalogoCodigosCalifornia', {})
                .then(resp => {
                    this.codigosCalifornia = resp.data.map(x => {
                        let desc = ''
                        if (x.CptCode !== ' ' && x.Descripcion !== '') {
                            desc = x.CptCode + ' - ' + x.Descripcion
                        } else if (x.CptCode === ' ' && x.Descripcion !== '') {
                            desc = x.Descripcion
                        } else if (x.CptCode !== ' ' && x.Descripcion === '') {
                            desc = x.CptCode
                        }

                        return {
                            Codigo: x.CptCode,
                            IdCodigoCalifornia: x.IdCodigoCalifornia,
                            Descripcion: desc
                        }
                    })
                })
        },
        getAnestesiologos() {
            this.axios.post('/app/v1_ExpedienteEvolucion/CatalogoMedicos', {
                    Especialidad: "ANESTESIOLOGIA"
                })
                .then(resp => {
                    this.anestesiologos = resp.data.map(x => {
                        return {
                            Codigo: x.Codigo,
                            Descripcion: x.Codigo + ' - ' + (x.Nombre).trim() + ' ' + (x.Apellido).trim()
                        }
                    })
                })
        },
        getPatologos() {
            this.axios.post('/app/v1_ExpedienteEvolucion/CatalogoMedicos', {
                    Especialidad: "PATOLOGIA"
                })
                .then(resp => {
                    this.patologos = resp.data.map(x => {
                        return {
                            Codigo: x.Codigo,
                            Descripcion: x.Codigo + ' - ' + (x.Nombre).trim() + ' ' + (x.Apellido).trim()
                        }
                    })
                })
        },
        getTipoAnestesia() {
            this.axios.post('/app/salaoperaciones/ListarTipoAnestesia', {})
                .then(resp => {
                    this.tipoAnestesia = resp.data.json.map((x) => {
                        return {
                            Id: x.IdTipoAnestesia,
                            Nombre: x.Nombre
                        }
                    })
                })
        },
        mapRecuento() {
            return [{
                    Completo: Boolean(this.encabezado.Compresas1),
                    Observaciones: this.encabezado.ObservacionesCompresas1
                },
                {
                    Completo: Boolean(this.encabezado.Compresas2),
                    Observaciones: this.encabezado.ObservacionesCompresas2
                },
                {
                    Completo: Boolean(this.encabezado.Gasas),
                    Observaciones: this.encabezado.ObservacionesGasas
                }
            ]
        },
        getRecord() {
            this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaRecordOperatorio', {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                })
                .then(resp => {
                    this.recordEncontrado = false
                    if (resp.data.json.IdRecordOperatorio !== undefined && resp.data.json.IdRecordOperatorio !== null) {
                        this.limpiarRegistro()
                        this.encabezado = {
                            ...resp.data.json
                        }
                        this.encabezado.CodigosCalifornia = resp.data.json.CodigosCalifornia.map((x) => {
                            let desc = ''
                            if (x.CptCode !== ' ' && x.Descripcion !== '') {
                                desc = x.CptCode + ' - ' + x.Descripcion
                            } else if (x.CptCode === ' ' && x.Descripcion !== '') {
                                desc = x.Descripcion
                            } else if (x.CptCode !== ' ' && x.Descripcion === '') {
                                desc = x.CptCode
                            }
                            return {
                                Descripcion: desc
                            }
                        })

                        this.anestesiaSeleccionada = [{
                            Id: this.encabezado.IdTipoAnestesia,
                            Nombre: this.encabezado.TipoAnestesia
                        }]
                        this.mapRecuentoConsulta(this.encabezado.Recuento)

                        this.recordEncontrado = true
                    }
                })
        },
        mapRecuentoConsulta(recuento) {
            // Compresas 1
            this.encabezado.Compresas1 = recuento[0].Completo === true ? 1 : 0
            this.encabezado.ObservacionesCompresas1 = recuento[0].Observaciones

            // Compresas 2
            this.encabezado.Compresas2 = recuento[1].Completo === true ? 1 : 0
            this.encabezado.ObservacionesCompresas2 = recuento[1].Observaciones

            // Gasas
            this.encabezado.Gasas = recuento[2].Completo === true ? 1 : 0
            this.encabezado.ObservacionesGasas = recuento[2].Observaciones
        },
    },
    mounted() {
        if (this.consulta) {
            this.getRecord();
        }
    },
    beforeMount() {
        this.getCodigosCalifornia()
        this.getTipoAnestesia()
        this.getAnestesiologos()
        this.getPatologos()
        if (!this.consulta) {
            this.getRecord()
        }

    },
    watch: {
        'hojaAnestesia'(newval) {
            if (newval !== undefined) {
                this.getRecord()
            }
        },
    },
    computed: {
        colCountByScreen() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 2,
                    lg: 3,
                };
        },
        colCountByScreenEspecialidad() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 2,
                    lg: 2
                };
        },
        dataGrid: function () {
            return this.$refs[dataRegistroRefKey].instance;
        },
        reportParams: function () {
            return {
                SerieAdmision: this.SerieAdmision,
                CodigoAdmision: this.CodigoAdmision,
                Id: this.hojaAnestesia
            }
        }
    },
}
</script>

<style>
#record .dx-item-content .dx-box-item-content {
    place-content: baseline !important;
}

#record .dx-invalid .dx-radiobutton-icon::before {
    border-color: red !important
}

.dx-tagbox-popup-wrapper .dx-list-select-all {
    display: none !important
}
</style>
