<template>
<div>
    <form @submit="handleSubmit">
        <DxForm :ref="formConsulta" :form-data.sync="formulario" label-mode="floating">
            <DxFormGroupItem>
                <DxFormItem data-field="Cuenta" editor-type="dxSelectBox" :editor-options="{ width: 'auto', searchEnabled: true, items: cuentas,  displayExpr: 'Valor', valueExpr: 'Codigo' }" :validationRules="[{ type: 'required' }]" />
                <DxFormGroupItem :col-count="2">
                    <DxFormItem data-field="ChequeInicial" editor-type="dxNumberBox" :editor-options="{ min: 0, format: '#0', step: 0 }" :validationRules="[{ type: 'required' }]">
                        <DxFormLabel text="Del número" />
                    </DxFormItem>
                    <DxFormItem data-field="ChequeFinal" editor-type="dxNumberBox" :editor-options="{ min: 0, format: '#0', step: 0 }" :validationRules="[{ type: 'required' }]">
                        <DxFormLabel text="Al número" />
                    </DxFormItem>
                </DxFormGroupItem>
                <DxFormButtonItem ref="imprimirButton" :button-options="buttonIngreso" name="Ingreso" horizontal-alignment="center" verical-alignment="center" />
            </DxFormGroupItem>
            <DxFormGroupItem :visible="notasAcreditamiento.length > 0" class="pt-4" caption=" ">
                <DxFormItem template="notascredito" :col-span="2" />
            </DxFormGroupItem>
            <DxFormGroupItem :col-count="2" class="pt-4" cssClass="centered-item">
                <DxFormItem data-field="ArchivoExtendido" editor-type="dxCheckBox">
                    <DxFormLabel text="Archivo extendido" />
                </DxFormItem>
                <DxFormButtonItem :button-options="buttonGenerar" name="Ingreso" horizontal-alignment="center" verical-alignment="center" />
            </DxFormGroupItem>

            <template #notascredito="{}">
                <div>
                    <DxDataGrid :ref="gridDetalle" v-bind="DefaultDxGridConfiguration" :data-source="notasAcreditamiento" :headerFilter="{ visible: false, allowSearch: false }" :paging="{ enabled:true, pageSize:10 }" :searchPanel="{ visible: false }" :width="'100%'" height="480" :on-saving="GuardaCambiosTabla">
                        <DxDataGridToolbar>
                            <DxFormItem location="after" />
                            <DxFormItem name="saveButton" />
                        </DxDataGridToolbar>
                        <DxDataGridSelection mode="single" />
                        <DxDataGridEditing mode="batch" :allow-updating="true" :allow-adding="false" :allow-deleting="false" />

                        <DxDataGridColumn width="10%" data-field="Proveedor" alignment="center" data-type="number" :allow-editing="false" />
                        <DxDataGridColumn width="40%" data-field="NombreBeneficiario" caption="Beneficiario" alignment="center" data-type="string" :allow-editing="false" />
                        <DxDataGridColumn width="15%" data-field="CuentaBanco" alignment="center" data-type="string" :allow-editing="false" />
                        <DxDataGridColumn width="20%" data-field="Total" alignment="center" data-type="number" :customize-text="customizeTextValores" :value-format="{ type: 'fixedPoint', precision: 2 }" :allow-editing="false" />
                        <DxDataGridColumn width="15%" data-field="NumNotaDebito" caption="Nota crédito" alignment="center" data-type="number" :allow-editing="true" :editor-options="{ min: 0, format: '#0', step: 0 }" />
                    </DxDataGrid>
                </div>
            </template>
        </DxForm>
    </form>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration,
    customizeTextValores
} from './data'

import * as XLSX from 'xlsx';

const formConsulta = 'formConsulta'
const formCheque = 'formCheque'
const gridCheques = 'gridCheques'
const gridDetalle = 'gridDetalle'

export default {
    name: 'Acreditamientos',
    components: {},
    data() {
        return {
            DefaultDxGridConfiguration,
            formConsulta,
            formCheque,
            gridCheques,
            gridDetalle,

            formulario: {
                Cuenta: null, // Codigo de la cuenta de banco de 2 dígitos
                NumeroCuenta: null, // Número de la cuenta bancaria
                BancoContabilidad: null, // Cuenta contable de la cuenta bancaria seleccionada
                ChequeInicial: null, // Primer cheque del rango a imprimir
                ChequeFinal: null, // Último cheque del rango a imprimir
                NombreCuentaBanco: null, // Nombre de la cuenta contable de la cuenta bancaria 
            },
            cuentas: [],

            buttonIngreso: {
                width: 'auto',
                icon: 'fas fa-credit-card',
                text: 'Ingreso acreditamiento',
                type: 'success',
                onClick: () => {
                    this.BuscarAcreditamiento()
                },
                useSubmitBehavior: false,
            },

            buttonGenerar: {
                width: 'auto',
                icon: 'fas fa-print',
                text: 'Generar',
                type: 'success',
                onClick: () => {
                    this.Generar()
                },
                useSubmitBehavior: false,
            },

            cheques: [],

            //Solo se asigna con status para que no cause error al validarlo cuando se cargan los cheques para mostrar el botón de anular
            infoCheque: {
                Status: null
            },
            periodo: null,
            cuentaIVACobrar: null,
            porcentajeIVA: null,
            retencion: null,
            indiceSeleccionado: null, //Variable para saber cual es el índice en la tabla de cheques del cheque seleccionado

            formularioCheque: {
                DescripcionPeriodo: null
            },
            valoresTabla: null,
            periodos: null,

            notasAcreditamiento: [],

            acreditamientosGenerar: [],
            nombreEmpresa: null
        }
    },
    props: {
        TipoCheque: null,
        Corporativo: null
    },
    methods: {
        customizeTextValores,
        handleSubmit(e) {
            e.preventDefault()
        },

        HabilitarBoton() {
            if (this.ValidarCheque(this.formulario.ChequeInicial) && this.ValidarCheque(this.formulario.ChequeFinal) && this.formulario.ChequeFinal >= this.formulario.ChequeInicial) {
                return true
            } else {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'Cheques no validos',
                    acceptText: 'Aceptar',
                    text: 'Debe ingresar ambos cheques y el cheque final debe ser mayor o igual al cheque inicial',
                    buttonCancel: 'border',
                    accept: () => {},
                })
                return false
            }
        },

        ValidarCheque(cheque) {
            if (cheque !== null && cheque > 0) {
                return true
            }
            return false
        },

        CargarCuentas() {
            this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 1
                })
                .then(resp => {
                    this.cuentas = resp.data.json

                    this.formulario.Cuenta = this.cuentas[0].Codigo
                })
        },

        async ConsultarSaldoFinal(e) {
            return await this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 2,
                    Cuenta: e.Cuenta,
                    Periodo: e.Periodo
                })
                .then(resp => {
                    if (resp.data.json.length === 0) {
                        return false
                    } else if (resp.data.json[0].SaldoFinal !== null) {
                        return true
                    }
                })
        },

        async CargarOpciones() {
            await this.axios.post('/app/v1_bancos/Opciones', {
                    Opcion: 1
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.nombreEmpresa = resp.data.json[0].Nombre
                    }
                })
        },

        BuscarAcreditamiento() {
            if (this.HabilitarBoton()) {
                this.axios.post('/app/v1_bancos/Acreditamiento', {
                        Opcion: 1,
                        ChequeInicial: this.formulario.ChequeInicial,
                        ChequeFinal: this.formulario.ChequeFinal,
                        Tipo: this.TipoCheque,
                        Cuenta: this.formulario.Cuenta
                    })
                    .then(resp => {
                        if (resp.data.json.length > 0) {
                            this.notasAcreditamiento = resp.data.json
                        }
                    })
            }
        },

        async Generar() {
            if (this.HabilitarBoton()) {
                await this.axios.post('/app/v1_bancos/ConsultaAnulacion', {
                        Opcion: 1,
                        Cuenta: this.formulario.Cuenta,
                        TipoCheque: this.TipoCheque,
                        Fecha: this.formulario.Fecha,
                        ChequeInicial: this.formulario.ChequeInicial,
                        ChequeFinal: this.formulario.ChequeFinal
                    })
                    .then(async resp => {
                        if (resp.data.json.length > 0) {
                            this.cheques = resp.data.json
                            await this.Impresion()
                        }
                    })
            }
        },

        async Impresion() {
            let actualizar = {
                Impresion: [],
                Reimpresion: [],
                NotaDebito: []
            }
            for (let i = 0; i < this.cheques.length; i++) {
                if (this.cheques[i].Impresion == null) {
                    actualizar.Impresion.push({
                        Opcion: 4,
                        ChequeInicial: this.cheques[i].Numero,
                        Cuenta: this.cheques[i].Cuenta,
                        Tipo: this.TipoCheque,
                        Usuario: this.Corporativo
                    })
                } else {
                    actualizar.Reimpresion.push({
                        Opcion: 5,
                        ChequeInicial: this.cheques[i].Numero,
                        Cuenta: this.cheques[i].Cuenta,
                        Tipo: this.TipoCheque,
                        Usuario: this.Corporativo
                    })
                }
            }

            await this.axios.post('/app/v1_bancos/AcreditamientoAgrupado', {
                    ...actualizar
                })
                .then(async resp => {
                    if (resp.data.codigo == 0) {
                        await this.GenerarArchivo()
                    }
                })
        },

        async GuardaCambiosTabla(e) {
            let notasDebito = {
                Impresion: [],
                Reimpresion: [],
                NotaDebito: []
            }

            if (e.changes.length > 0) {
                for (let i = 0; i < e.changes.length; i++) {
                    let insertar = null;
                    if (e.changes[i].type == 'update') {
                        insertar = {
                            ...e.changes[i].key,
                            Opcion: 3,
                            Usuario: this.Corporativo
                        };

                        // Recorrer las columnas del 'data' y actualizar el 'key' con los nuevos valores
                        for (const field in e.changes[i].data) {
                            if (Object.prototype.hasOwnProperty.call(e.changes[i].data, field)) {
                                insertar[field] = e.changes[i].data[field]; // Actualiza el campo en 'key' con el valor de 'data'
                            }
                        }
                    }
                }

                await this.axios.post('/app/v1_bancos/AcreditamientoAgrupado', {
                        ...notasDebito
                    })
                    .then(resp => {
                        if (resp.data.codigo == 0) {
                            this.BuscarAcreditamiento()
                        }
                    })
            }

        },

        async GenerarArchivo() {
            await this.axios.post('/app/v1_bancos/Acreditamiento', {
                    Opcion: 2,
                    ChequeInicial: this.formulario.ChequeInicial,
                    ChequeFinal: this.formulario.ChequeFinal,
                    Tipo: this.TipoCheque,
                    Cuenta: this.formulario.Cuenta,
                    Usuario: this.Corporativo
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.acreditamientosGenerar = resp.data.json

                        // let contenedorArchivo = '';
                        let contador = 1;
                        let montoTotal = 0;
                        // let nombreArchivo = 'TransferenciaBancaria ' + this.formatDate(new Date()) + '.txt';
                        // let nombreArchivo = `TransferenciaBancaria-${new Date().toLocaleString()}.txt`;

                        // Cabezera (se puede modificar según sea necesario)
                        // TituloParaArchivo: 'Cuenta O' + CHR(9) + 'Tipo O' + CHR(9) + 'Monto' + CHR(9) + ...

                        // Iterar sobre los datos y crear las líneas para el archivo
                        for (let i = 0; i < this.acreditamientosGenerar.length; i++) {

                            //************************************ Se comenta todo para que no cause problemas y porque ya no se generará el archivo TXT
                            // let nuevoRegistro = '';
                            let data = this.acreditamientosGenerar[i]

                            // nuevoRegistro += data.Nit + ',' // Nit
                            // nuevoRegistro += data.NombreBeneficiario.substring(0, 60) + ',' // Nombre Beneficiario
                            // nuevoRegistro += data.CuentaBanco.substring(0, 10) + ',' // Cuenta Banco
                            // nuevoRegistro += data.Observaciones.substring(0, 35) + ',' // Observaciones
                            // nuevoRegistro += data.Documento.substring(0, 15) + ',' // Documento
                            // nuevoRegistro += parseFloat(data.Monto).toFixed(2) + ',' // Monto
                            // nuevoRegistro += data.Email ? data.Email : '' // Email

                            // // Agregar al contenido del archivo
                            // contenedorArchivo += nuevoRegistro + '\n';

                            contador += 1;
                            montoTotal = parseFloat(montoTotal) + parseFloat(data.Monto);
                        }

                        // Crear un Blob (objeto de archivo)
                        // const blob = new Blob([contenedorArchivo], {
                        //     type: 'text/plain;charset=utf-8'
                        // });

                        // Descargar el archivo
                        // saveAs(blob, nombreArchivo);

                        if (this.formulario.ArchivoExtendido) {
                            this.ExportarAExcel(this.acreditamientosGenerar)
                        }

                        // Mostrar mensaje con información
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Archivo generado',
                            acceptText: 'Aceptar',
                            text: 'Proceso finalizado.\n# de registros: ' + (contador - 1) + '\nMonto total: Q. ' + parseFloat(montoTotal).toFixed(2),
                            buttonCancel: 'border',
                            accept: () => {},
                        })
                    }
                })
        },

        ExportarAExcel(data) {
            const ws_data = []

            let encabezado2 = this.TipoCheque == 'NA' ? 'Acreditamiento de Anticipo' : this.TipoCheque == 'NP' ? 'Acreditamiento de Pagos de proveedores' : 'Acreditamiento de Honorarios médicos',
                encabezado3 = 'Cantidades Expresadas en :   Quetzales (Q.)',
                nombreArchivo = 'Acreditamiento'

            const encabezadoTabla = ['Nit', 'NombreBeneficiario', 'CuentaBanco', 'Observaciones', 'Documento', 'Monto', 'Email', 'NumNotaDebito', 'Numero'] // Una fila vacía para separar los encabezados de los datos

            ws_data.push([this.nombreEmpresa]);
            ws_data.push([encabezado2]);
            ws_data.push([encabezado3]);
            ws_data.push([]);
            ws_data.push(encabezadoTabla);

            // Variable para calcular el ancho máximo de cada columna
            const colWidths = new Array(encabezadoTabla.length).fill(0);

            // Agregar los registros de la consulta (similar a cómo se iteraría sobre QueryObj)
            for (let i = 0; i < data.length; i++) {
                const row = [
                    data[i].Nit,
                    data[i].NombreBeneficiario.substring(0, 60), // Simulación de cortar el nombre a 60 caracteres
                    data[i].CuentaBanco.substring(0, 10), // Limitar cuenta banco a 10 caracteres
                    data[i].Observaciones.substring(0, 35), // Limitar observaciones a 35 caracteres
                    data[i].Documento.substring(0, 15), // Limitar documento a 15 caracteres
                    'Q. ' + data[i].Monto.toFixed(2), // Monto formateado con 2 decimales
                    data[i].Email,
                    data[i].NumNotaDebito,
                    data[i].Numero,
                ];
                ws_data.push(row)

                row.forEach((cell, index) => {
                    colWidths[index] = Math.max(colWidths[index], cell ? cell.toString().length : 0);
                });
            }

            // Crear una hoja de trabajo a partir de los datos
            const ws = XLSX.utils.aoa_to_sheet(ws_data);

            // Asignar el ancho de las columnas (ajustar al contenido)
            ws['!cols'] = colWidths.map(width => ({
                wch: width + 2
            })); // Agregar un margen extra de 2

            // Crear un libro de trabajo con la hoja
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Reporte');

            // Definir el nombre del archivo
            let fileName = nombreArchivo + '.xlsx';

            // Guardar el archivo Excel en el navegador
            XLSX.writeFile(wb, fileName);

            // Mostrar mensaje al usuario
            // this.$message({
            //     message: `Archivo exportado correctamente: ${fileName}`,
            //     type: 'success'
            // });
        },

        formatDate(date) {
            const day = String(date.getDate()).padStart(2, '0'); // Día con 2 dígitos
            const month = String(date.getMonth() + 1).padStart(2, '0'); // Mes con 2 dígitos (recuerda que los meses comienzan en 0)
            const year = date.getFullYear(); // Año
            const hours = String(date.getHours()).padStart(2, '0'); // Hora con 2 dígitos
            const minutes = String(date.getMinutes()).padStart(2, '0'); // Minutos con 2 dígitos

            return `${day}-${month}-${year}-${hours}-${minutes}`;
        }

    },
    created() {},
    mounted() {},
    beforeMount() {
        this.CargarCuentas()
        this.CargarOpciones()
    },
    watch: {
        'formulario.ChequeInicial'(newval) {
            if (this.formulario.ChequeFinal == 0 || this.formulario.ChequeFinal == null || parseInt(this.formulario.ChequeFinal) < parseInt(this.formulario.ChequeInicial)) {
                this.formulario.ChequeFinal = newval
            }
        }
    },
    computed: {
        formConsultaInstance: function () {
            return this.$refs[formConsulta].instance;
        },

        formChequeInstance: function () {
            return this.$refs[formCheque].instance;
        },
    }
}
</script>

<style>
.buttonsOrdenes .dx-item-content .dx-box-item-content {
    place-content: end !important;
}

.centered-item .dx-item-content .dx-box-item-content {
    display: grid !important;
    place-content: center !important;
}
</style>
