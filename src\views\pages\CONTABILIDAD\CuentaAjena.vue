<template>
<div>
    <form @submit="handleSubmit">
        <DxForm :ref="formCajaAjena" :form-data.sync="formulario" label-mode="floating">
            <DxFormGroupItem :col-count="2">
                <DxFormGroupItem>
                    <DxFormItem data-field="Cuenta" editor-type="dxSelectBox" :editor-options="{ width: 'auto', searchEnabled: true, items: cuentas,  displayExpr: 'Valor', valueExpr: 'Codigo', onValueChanged: AsignarSiguienteCheque }" :validationRules="[{ type: 'required' }]" />
                    <DxFormItem data-field="SiguienteCheque" editor-type="dxNumberBox" :editor-options="{ min: 0, format: '#0', step: 0 }">
                        <DxFormLabel text="Siguiente acreditamiento" />
                    </DxFormItem>
                    <DxFormItem template="texto" v-if="mostrarTexto" />
                    <DxFormItem template="boton" v-if="mostrarBoton" />
                </DxFormGroupItem>

                <DxFormGroupItem>
                    <DxFormItem template="lotes" />
                </DxFormGroupItem>
            </DxFormGroupItem>

            <template #boton="{}">
                <div style="display: flex; flex-wrap: wrap; justify-content: center;">
                    <DxButton :visible="true" width="auto" height="36px" type="success" class="p-1" styling-mode="contained" @click="Grabar()">
                        <font-awesome-icon class="mr-2" :icon="['fas', 'save']" style="font-size: 18px; vertical-align: middle" />
                        <span>Grabar</span>
                    </DxButton>
                </div>
            </template>

            <template #texto="{}">
                <div>
                    <h3 style="color: red;">Lote generado automáticamente por Rechazo de cuenta bancaria</h3>
                </div>
            </template>

            <template #lotes="{}">
                <div class="pb-2">
                    <DxDataGrid :ref="gridDocumentos" v-bind="DefaultDxGridConfiguration" :data-source="lotes" :paging="{ enabled: false }" :searchPanel="{ visible: false} " :headerFilter="{ visible: false, allowSearch: false }" :width="'100%'" height="170" :on-row-click="SeleccionarLote">
                        <DxDataGridSelection mode="single" />
                        <DxDataGridScrolling mode="virtual" />

                        <DxDataGridColumn width="30%" data-field="Id" alignment="center" data-type="string" />
                        <DxDataGridColumn width="70%" data-field="Lote" alignment="center" data-type="string" />
                    </DxDataGrid>
                </div>
            </template>

        </DxForm>
    </form>

</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'

import 'devextreme-vue/text-area'

const gridDetalle = 'gridDetalle'
const gridDocumentos = 'gridDocumentos'
const formCajaAjena = 'formCajaAjena'

export default {
    name: 'CuentaAjena',
    components: {},
    data() {
        return {
            DefaultDxGridConfiguration,
            gridDetalle,
            gridDocumentos,
            formCajaAjena,

            cuentas: [],
            valoresTabla: [],
            lotes: [],
            valoresDocumentos: [],

            formulario: {
                Cuenta: null, // Codigo de la cuenta de banco de 2 dígitos
                NumeroCuenta: null, // Número de la cuenta bancaria
                BancoContabilidad: null, // Cuenta contable de la cuenta bancaria seleccionada
                SiguienteCheque: null, // Siguiente cheque de la cuenta bancaria
                NombreCuentaBanco: null, // Nombre de la cuenta, se guarda como el detalle del auxiliar banco
                BancoCuentaNombre: null, // Nombre de la cuenta que define la cuenta en contabilidad
                SiguienteNota: null, // Siguiente nota cuando se utiliza desde Transferencias
                Lote: null, // Número de lote
                CuentaProveedores: null, // Número de cuenta para proveedores por defecto
                CuentaProveedoresProformaDefault: '1129903', // Default de PP cuando aún no hay factura
                LoteSeleccionado: null, // El lote que se seleccionó para generar
            },

            cuentaIVACobrar: null,
            periodo: null,

            existeSaldoFinal: true,

            totalDebe: 0,
            totalHaber: 0,

            mostrarTexto: false, // Variable para indicar si se debe mostrar el texto de Rechazo por cuenta bancaria
            mostrarBoton: false,

            contabilidadCuentaAjena: null, // Número de cuenta para Cuenta ajena
            opciones: [], // Variable que guardará el IVA, ISR, entre otros datos que maneja la empresa

            buttonGrabar: {
                width: 'auto',
                icon: 'fas fa-save',
                text: 'Grabar',
                type: 'success',
                disabled: true,
                onClick: () => {
                    this.Grabar()
                },
                useSubmitBehavior: false,
            },

            totalHonorarios: 0,
            valorComision: 0,
            valorLiquido: 0,
            porcentajeComision: 0,

            pacientesPorCheque: 0,

            sucursalRecogeChequeDefault: 'HLA',
            hospitalRecogerCheque: null,

            ajenoActual: null, // Sirve para validar el ajeno que se está evaluando

            chequeInicial: null, // Guarda el número del cheque o nota de acreditamiento inicial para restarlo con el cheque final y saber la cantidad de cheques generados
            contadorIndice: null, // Variable para llevar el control del indice de los 

            tmpProveedor: null,
            tmpDocumento: null,

            numeroCheque: null,

            CuentaAjena: {
                BancoMovto: {},
                AuxiliarBanco: [],
                LotesFacturas: [],
                ComprasPagos: [],
                Compras: [],
            },

            CuentasAjenas: {
                CuentaAjena: [],
                Lote: {}
            }
        }
    },
    props: {
        Corporativo: null,
        TipoDoc: null, // Indica desde qué módulo se invoca el componente  1 = Cheques / 2 = Transferencias 
        TipoCobro: null // Indica desde qué módulo se invoca el componente  1 = Ajenos / 2 = Honorarios 
    },
    methods: {
        handleSubmit(e) {
            e.preventDefault()
        },

        CargarCuentas(asignar) {
            this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 1
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.cuentas = resp.data.json

                        if (asignar == 1) {
                            this.formulario.Cuenta = this.cuentas[0].Codigo
                        } else if (asignar == 2) {
                            let codigo = this.cuentas.find((x) => x.Codigo == this.formulario.Cuenta)

                            this.formulario.SiguienteCheque = codigo.SiguienteCheque
                            this.chequeInicial = this.formulario.SiguienteCheque
                        }
                    }
                })
        },

        async CargarLotes() {
            await this.axios.post('/app/v1_bancos/CuentaAjena', {
                    Opcion: 1,
                    TipoCobro: this.TipoCobro, // 1 = Ajenos / 2 = Honorarios
                    TipoDocumento: this.TipoDoc == 1 ? 'CH' : 'NH'
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.lotes = resp.data.json
                    }
                })
        },

        SeleccionarLote(e) {
            this.formulario.LoteSeleccionado = e.data
            this.mostrarBoton = true

            if (e.data.IdFactura !== null) {
                this.mostrarTexto = true
            } else {
                this.mostrarTexto = false
            }
        },

        async BuscarSiguienteAcreditamiento() {
            await this.axios.post('/app/v1_bancos/Anticipos', {
                    Opcion: 7,
                    TipoMov: 'NH'
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.formulario.SiguienteNota = resp.data.json[0].SiguienteNota
                        this.formulario.SiguienteCheque = resp.data.json[0].SiguienteNota

                        this.chequeInicial = resp.data.json[0].SiguienteNota
                    }
                })
        },

        ConfiguracionInicial() {
            if (this.TipoCobro == 1) {
                this.formulario.CuentaProveedores = 3110504 // Comentario en Delphi -> Según dercas Semaforo3 anterior '3110304'; // codigo de cuenta para proveedores...  antes 3110301
            } else {
                this.formulario.CuentaProveedores = 3110305
            }
        },

        CargarOpciones() {
            this.axios.post('/app/v1_bancos/Opciones', {
                    Opcion: 1
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.opciones = resp.data.json[0]
                    }
                })
        },

        BuscarCheque() {
            this.axios.post('/app/v1_bancos/Anticipos', {
                    Opcion: 14,
                    Cuenta: this.formulario.Cuenta,
                    Numero: this.formulario.SiguienteCheque,
                    TipoMov: this.TipoDoc == 1 ? 'CH' : 'NH'
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Cheque registrado',
                            acceptText: 'Aceptar',
                            text: 'Un cheque a nombre de ' + resp.data.json[0].Beneficiario + ' ya fue registrado con este número.',
                            buttonCancel: 'border',
                            accept: () => {},
                        })
                        return
                    }
                })
        },

        AsignarSiguienteCheque(e) {
            if (e !== null) {
                let cuenta = this.cuentas.find((x) => x.Codigo == e.value)
                if (cuenta.SiguienteCheque == null || cuenta.SiguienteCheque == '') {
                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'Cuenta sin número',
                        acceptText: 'Aceptar',
                        text: 'Debe ingresar un número para la cuenta seleccionada',
                        buttonCancel: 'border',
                        accept: () => {},
                    })
                } else {
                    this.formulario.BancoContabilidad = cuenta.Contabilidad
                    this.formulario.SiguienteCheque = this.TipoDoc == 1 ? cuenta.SiguienteCheque : this.formulario.SiguienteNota
                    this.formulario.NumeroCuenta = cuenta.Cuenta
                    this.formulario.NombreCuentaBanco = cuenta.Nombre
                    this.formulario.BancoCuentaNombre = cuenta.NombreCuenta

                    this.chequeInicial = this.formulario.SiguienteCheque
                }
            }
        },

        async BuscarEgresos(proveedor, documento) {
            return await this.axios.post('/app/v1_bancos/PagosGenerados', {
                    Opcion: 5,
                    ProveedorInicio: proveedor,
                    Documento: documento
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        return resp.data.json[0]
                    } else {
                        return null
                    }
                })
        },

        async BuscarCuentaProveedor(proveedor) {
            return await this.axios.post('/app/v1_bancos/Reembolsos', {
                    Opcion: 4,
                    Proveedor: proveedor,
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        return resp.data.json[0]
                    }
                })
        },

        async BuscarNombreCuenta(cuenta) {
            return await this.axios.post('/app/v1_bancos/Cuentas', {
                Opcion: 7,
                Cuenta: cuenta
            }).then(resp => {
                if (resp.data.json.length > 0) {
                    return resp.data.json[0].Nombre
                }
            })
        },

        CargarCuentaAjena() {
            this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 10
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.contabilidadCuentaAjena = resp.data.json[0].CuentaAjena
                    }
                })
        },

        async Grabar() {
            if (this.formulario.SiguienteCheque === null || this.formulario.SiguienteCheque <= 0) {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'Cheque no válido',
                    acceptText: 'Aceptar',
                    text: 'El número de cheque no es válido. No puede continuar.',
                    buttonCancel: 'border',
                    accept: () => {},
                })
                return
            }

            if (this.periodo !== null) {
                let saldoFinal = await this.ConsultarSaldoFinal()
                if (!saldoFinal) {
                    this.AjenosLiberados()
                    // this.VerificarExistencia()
                }
            } else {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'Período no definido',
                    acceptText: 'Aceptar',
                    text: 'No existe un período para la fecha actual.',
                    buttonCancel: 'border',
                    accept: () => {},
                })
                return
            }
        },

        ConsultarPeriodo() {
            this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 1,
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.periodo = resp.data.json[0].Codigo
                    } else {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Período no definido',
                            acceptText: 'Aceptar',
                            text: 'No existe un período para la fecha actual.',
                            buttonCancel: 'border',
                            accept: () => {},
                        })
                        return
                    }
                })
        },

        async ConsultarSaldoFinal() {
            return await this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 2,
                    Cuenta: this.formulario.Cuenta,
                    Periodo: this.periodo
                })
                .then(resp => {
                    if (resp.data.json.length === 0) {
                        return false

                    } else if (resp.data.json[0].SaldoFinal !== null) {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Período no activo',
                            acceptText: 'Aceptar',
                            text: 'Debe reabrir el registro del libro negro para este período para poder grabar esta transacción.',
                            buttonCancel: 'border',
                            accept: () => {
                                return true
                            },
                        })
                        return
                    }
                })
        },

        async VerificarExistencia() { // Busca en las tablas si hay registros con los datos de la empresa, cuenta, cheque y movimiento
            return await this.axios.post('/app/v1_bancos/Anticipos', {
                    Cuenta: this.formulario.Cuenta,
                    Numero: this.formulario.SiguienteCheque,
                    TipoMov: this.TipoDoc,
                    Opcion: 12
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        let datos = resp.data.json[0] // Obtiene la cantidad de registros que hay en cada tabla con los datos ingresados

                        if (datos.BancosMovtos > 0 || datos.AuxiliarBanco > 0) {
                            if (this.TipoDoc == 1) {
                                this.$vs.dialog({
                                    type: 'alert',
                                    color: '#ed8c72',
                                    title: 'Error número de cheque',
                                    acceptText: 'Aceptar',
                                    text: 'Este cheque ya exíste',
                                    buttonCancel: 'border',
                                    accept: () => {},
                                })
                                return true
                            } else {
                                this.$vs.dialog({
                                    type: 'alert',
                                    color: '#ed8c72',
                                    title: 'Error número de acreditamiento',
                                    acceptText: 'Aceptar',
                                    text: 'Este acreditamiento ya exíste',
                                    buttonCancel: 'border',
                                    accept: () => {},
                                })
                                return true
                            }
                        } else {
                            return false
                            // this.AjenosLiberados()
                            // this.Grabar1()
                        }
                    }
                })
        },

        async AjenosLiberados() {
            await this.axios.post('/app/v1_bancos/CuentaAjena', {
                    Opcion: 2,
                    TipoCobro: this.TipoCobro, // 1 = Ajenos / 2 = Honorarios
                    TipoDocumento: this.TipoDoc == 1 ? 'CH' : 'NH',
                    IdLote: this.formulario.LoteSeleccionado.Id
                })
                .then(async resp => {
                    if (resp.data.json.length > 0) {
                        await this.Grabar1(resp.data.json)
                    } else {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'No existen registros',
                            acceptText: 'Aceptar',
                            text: 'No hay ningún registro pendiente de pago.',
                            buttonCancel: 'border',
                            accept: () => {},
                        })
                        return
                    }
                })
        },

        async ConsultarAjeno(ajeno) {
            return await this.axios.post('/app/v1_bancos/CuentaAjena', {
                    Opcion: 3,
                    Ajeno: ajeno
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        return resp.data.json[0]
                    }
                })
        },

        async GrabarCheque(codigoAjeno) {
            if (this.ajenoActual.Codigo !== codigoAjeno) {
                this.ajenoActual = await this.ConsultarAjeno(codigoAjeno)
            }

            // let baseRetencion = parseFloat(this.totalHonorarios / (1 + this.opciones.IVA)).toFixed(2)

            this.valorLiquido = parseFloat(this.totalHonorarios - this.valorComision)

            if (this.ajenoActual.HospitalRecogerCheque == null) {
                this.hospitalRecogerCheque = this.sucursalRecogeChequeDefault
            } else {
                this.hospitalRecogerCheque = this.ajenoActual.HospitalRecogerCheque
            }

            let observaciones = null
            observaciones = this.ajenoActual.NombreCompleto + ' Lote ' + this.formulario.LoteSeleccionado.Lote + ' Recoger Ch. ' + this.hospitalRecogerCheque

            let beneficiario = null

            if (this.ajenoActual.NombreCheque == null || this.ajenoActual.NombreCheque == '') {
                beneficiario = this.ajenoActual.Nombre + ' ' + this.ajenoActual.Apellido
            } else {
                beneficiario = this.ajenoActual.NombreCheque
            }

            this.CuentaAjena.BancoMovto = {
                Opcion: 1,
                Cuenta: this.formulario.Cuenta,
                TipoMov: this.TipoDoc == 1 ? 'CH' : 'NH',
                Periodo: this.periodo,
                Usuario: this.Corporativo,
                TipoCheque: 'M',
                Numero: this.numeroCheque,
                CodigoBeneficiario: codigoAjeno,
                NombreBeneficiario: beneficiario,
                Proveedor: this.ajenoActual.Proveedor,
                Monto: parseFloat(this.valorLiquido).toFixed(2),
                IdLoteHonorario: this.formulario.LoteSeleccionado.Id,
                Observaciones: this.TipoCobro == 1 ? 'Cta Ajena ' + observaciones : 'Honorarios ' + observaciones,
                Conciliado: 'N'
            }

            if (this.formulario.LoteSeleccionado.IdFactura == null && this.TipoCobro == 1) {

                if (this.valorComision > 0) {

                    this.CuentaAjena.AuxiliarBanco.push({
                        Opcion: 4,
                        TipoMov: this.TipoDoc == 1 ? 'CH' : 'NH',
                        Cuenta: this.formulario.Cuenta,
                        Numero: parseInt(this.numeroCheque),
                        Indice: parseInt(this.contadorIndice + 1),
                        Periodo: this.periodo,
                        Usuario: this.Corporativo,
                        Referencia: this.formulario.SiguienteCheque,
                        CuentaBanco: this.contabilidadCuentaAjena,
                        Haber: parseFloat(this.valorComision).toFixed(2),
                        Detalle: 'Manejo de Cuenta',
                        IdLoteHonorario: this.formulario.LoteSeleccionado.Id
                    })
                }
            }

            if (this.valorLiquido !== 0) {

                this.CuentaAjena.AuxiliarBanco.push({
                    Opcion: 4,
                    TipoMov: this.TipoDoc == 1 ? 'CH' : 'NH',
                    Cuenta: this.formulario.Cuenta,
                    Numero: parseInt(this.numeroCheque),
                    Indice: parseInt(this.contadorIndice + 4),
                    Periodo: this.periodo,
                    Usuario: this.Corporativo,
                    Documento: this.formulario.SiguienteCheque,
                    CuentaBanco: this.formulario.BancoContabilidad,
                    Haber: parseFloat(this.valorLiquido).toFixed(2),
                    Detalle: this.formulario.NombreCuentaBanco,
                    IdLoteHonorario: this.formulario.LoteSeleccionado.Id
                })
            }

            this.CuentasAjenas.CuentaAjena.push(this.CuentaAjena)

            this.CuentaAjena = {
                BancoMovto: [],
                AuxiliarBanco: [],
                Lotes: {},
                LotesFacturas: [],
                ComprasPagos: [],
                Compras: [],
            }
            this.numeroCheque = parseInt(this.numeroCheque + 1)
            this.contadorIndice = 0
        },

        async Grabar1(ajenos) {
            let ajenoAnt = 0,
                contCheques = 0

            this.contadorIndice = 0

            this.numeroCheque = this.formulario.SiguienteCheque

            this.CuentaAjena = {
                BancoMovto: {},
                AuxiliarBanco: [],
                LotesFacturas: [],
                ComprasPagos: [],
                Compras: [],
            }

            let i = 0

            // for (let i = 0; i < ajenos.length; i++) {
            while (i < ajenos.length) {
                let item = ajenos[i]

                let tempDetalleFactura = {}
                let tempLotesFacturas = {}

                if (ajenoAnt !== item.Ajeno || this.contadorIndice > this.pacientesPorCheque) {
                    if (ajenoAnt > 0) {
                        await this.GrabarCheque(ajenoAnt) //-----------------------------------------------------------------------------
                    }

                    let existencias = await this.VerificarExistencia()

                    if (!existencias) { // Si es falso significa que no hay existencias, por lo que puede ingresar el cheque 
                        ajenoAnt = item.Ajeno
                        this.LimpiarVariables()

                        this.ajenoActual = await this.ConsultarAjeno(item.Ajeno)

                        if (this.ajenoActual.ManejoCuenta === null) { //Asignación del porcentaje de comisión
                            this.porcentajeComision = 0
                        } else {
                            this.porcentajeComision = this.ajenoActual.ManejoCuenta
                        }
                    }

                }

                tempDetalleFactura = {
                    Opcion: 4,
                    TipoMov: this.TipoDoc == 1 ? 'CH' : 'NH',
                    Cuenta: this.formulario.Cuenta,
                    Numero: parseInt(this.numeroCheque),
                    Periodo: this.periodo,
                    Usuario: this.Corporativo,
                    IdLoteHonorario: this.formulario.LoteSeleccionado.Id
                }

                tempLotesFacturas = {
                    Opcion: 4,
                    CuentaBanco: this.formulario.Cuenta,
                    Ajeno: item.Ajeno,
                    Estado: 'L',
                    SiguienteCheque: this.numeroCheque
                }

                this.contadorIndice = this.contadorIndice + 1

                tempLotesFacturas.IdLote = item.IdLote
                tempLotesFacturas.SerieFactura = item.SerieFactura
                tempLotesFacturas.NumeroFactura = item.Factura
                tempLotesFacturas.TipoDocumento = this.TipoDoc == 1 ? 'CH' : 'NH'

                let sumaFAjusteRetencionISR = 0,
                    sumaFAjusteRetencionIVA = 0

                if (item.DescuentoRetencionISR > 0) {
                    sumaFAjusteRetencionISR = parseFloat(item.DescuentoRetencionISR)
                }

                if (item.DescuentoRetencionIVA > 0) {
                    sumaFAjusteRetencionIVA = parseFloat(item.DescuentoRetencionIVA)
                }

                tempDetalleFactura.Documento = item.Documento

                let fechaPago = null

                if (item.FechaPlanillaPago == null) {
                    fechaPago = ''
                } else {
                    fechaPago = item.FechaPlanillaPago
                }

                if (this.formulario.LoteSeleccionado.IdFactura == null && this.TipoCobro == 1) // No viene rechazado de cuenta bancaria y es Cuenta ajena
                {
                    tempDetalleFactura.Detalle = fechaPago + ' ' + item.Lote
                    tempDetalleFactura.Debe = parseFloat(item.SumaFValor - sumaFAjusteRetencionISR - sumaFAjusteRetencionIVA).toFixed(2)

                } else { // Viene rechazado de banco 
                    tempDetalleFactura.Debe = parseFloat(item.SumaFValor - sumaFAjusteRetencionISR - sumaFAjusteRetencionIVA - item.SumaFMCuenta).toFixed(2)
                    if (this.formulario.LoteSeleccionado.IdFactura != null) {
                        tempDetalleFactura.Detalle = fechaPago + ' ' + item.Lote + ' Por Depósito Rechazado'
                    } else {
                        tempDetalleFactura.Detalle = fechaPago + ' ' + item.Lote
                    }
                }

                tempDetalleFactura.Indice = this.contadorIndice

                let cuentaEgresos = null
                cuentaEgresos = await this.BuscarEgresos(item.Proveedor, item.Documento)

                if (cuentaEgresos == null || cuentaEgresos.Cuenta == null) {
                    if (item.SerieFactura.substring(1, 3) == 'PPP') {
                        tempDetalleFactura.CuentaBanco = this.formulario.CuentaProveedoresProformaDefault
                    } else {
                        tempDetalleFactura.CuentaBanco = this.formulario.CuentaProveedores
                    }
                } else {
                    tempDetalleFactura.CuentaBanco = cuentaEgresos.Cuenta
                }

                this.totalHonorarios = parseFloat(this.totalHonorarios + parseFloat(item.SumaFValor - sumaFAjusteRetencionISR - sumaFAjusteRetencionIVA))
                this.valorComision = parseFloat(this.valorComision + item.SumaFMCuenta)

                tempDetalleFactura.IdLote = this.formulario.LoteSeleccionado.Id

                // Honorarios
                if (this.TipoCobro == 2) {
                    if (this.tmpProveedor !== item.Proveedor && this.tmpDocumento !== item.Documento) {
                        this.tmpProveedor = item.Proveedor
                        this.tmpDocumento = item.Documento

                        let valor = parseFloat(item.SumaFValor - sumaFAjusteRetencionISR - sumaFAjusteRetencionIVA)

                        this.CuentaAjena.Compras.push({
                            Opcion: 5,
                            ValorDetalleCheque: parseFloat(valor).toFixed(2),
                            Proveedor: item.Proveedor,
                            Documento: item.Documento,
                        })

                        this.CuentaAjena.ComprasPagos.push({
                            Opcion: 5,
                            Cuenta: this.formulario.Cuenta,
                            TipoMov: this.TipoDoc == 1 ? 'CH' : 'NH',
                            Numero: this.numeroCheque,
                            Tipo: 'M',
                            Periodo: this.periodo,
                            Proveedor: item.Proveedor,
                            Documento: item.Documento,
                            Monto: parseFloat(valor).toFixed(2),
                            Usuario: this.Corporativo,
                            Saldo: 0
                        })
                    }
                }

                this.CuentaAjena.AuxiliarBanco.push(tempDetalleFactura)
                this.CuentaAjena.LotesFacturas.push(tempLotesFacturas)
                // }
                i++;
            }

            this.CuentasAjenas.Lote = {
                Opcion: 6,
                IdLote: ajenos[ajenos.length - 1].IdLote,
                TipoDocumento: this.TipoDoc == 1 ? 'CH' : 'NH'
            }

            if (ajenoAnt > 0 && this.totalHonorarios > 0) {
                await this.GrabarCheque(ajenoAnt) //-----------------------------------------------------------------------------

                this.LimpiarVariables()
            }

            contCheques = parseInt(this.numeroCheque - this.chequeInicial)

            await this.axios.post('/app/v1_bancos/CuentasAjenas', {
                ...this.CuentasAjenas
            }).then(async resp => {
                if (resp.data.codigo == 0) {
                    if (this.TipoDoc == 1) {
                        // Incrementa el número de cheque
                        await this.axios.post('/app/v1_bancos/Cuentas', {
                            Codigo: this.formulario.Cuenta,
                            SiguienteCheque: parseInt(this.numeroCheque),
                            Opcion: 6
                        }).then(resp => {
                            if (resp.data.codigo == 0) {
                                this.CargarCuentas(2)
                                this.$vs.dialog({
                                    type: 'alert',
                                    color: '#ed8c72',
                                    title: 'Procesamiento de lote correcto',
                                    acceptText: 'Aceptar',
                                    text: 'Cheques emitidos: ' + contCheques,
                                    buttonCancel: 'border',
                                    accept: () => {},
                                })
                                return
                            }
                        })
                    } else if (this.TipoDoc == 2) {
                        // Actualiza el correlativo de la nota de acreditamiento
                        await this.axios.post('/app/v1_bancos/Anticipos', {
                            Opcion: 2,
                            Numero: parseInt(this.numeroCheque),
                            TipoMov: 'NH',
                        }).then(resp => {
                            if (resp.data.codigo == 0) {
                                this.BuscarSiguienteAcreditamiento()
                                this.$vs.dialog({
                                    type: 'alert',
                                    color: '#ed8c72',
                                    title: 'Procesamiento de lote correcto',
                                    acceptText: 'Aceptar',
                                    text: 'Acreditamientos emitidos: ' + contCheques,
                                    buttonCancel: 'border',
                                    accept: () => {},
                                })
                                return
                            }
                        })
                    }
                    this.CargarLotes()

                    this.CuentaAjena = {
                        BancoMovto: {},
                        AuxiliarBanco: [],
                        LotesFacturas: [],
                        ComprasPagos: [],
                        Compras: [],
                    }

                    this.CuentasAjenas = {
                        CuentaAjena: [],
                        Lote: {}
                    }
                }
            })
        },

        LimpiarVariables() {
            this.totalHonorarios = 0
            this.valorComision = 0
        },
    },
    created() {},
    mounted() {},
    beforeMount() {
        if (this.TipoDoc == 2) {
            this.BuscarSiguienteAcreditamiento()
            this.pacientesPorCheque = 1000
        } else {
            this.pacientesPorCheque = 15
        }

        this.CargarCuentaAjena()
        this.CargarCuentas(1)
        this.CargarLotes()
        this.ConsultarPeriodo()
        this.CargarOpciones()
        this.ConfiguracionInicial()
    },
    watch: {
        'formulario.SiguienteCheque'(newval) {
            if (newval !== null && newval !== '' && newval !== undefined && newval !== 0) {
                this.BuscarCheque()
            }
        },
    },
    computed: {
        colCountByScreen() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 2,
                    lg: 2
                };
        },

        dataGridDetalle: function () {
            return this.$refs[gridDetalle].instance;
        },

        dataGridDocumentos: function () {
            return this.$refs[gridDocumentos].instance;
        },

        formCajaAjenaInstance: function () {
            return this.$refs[formCajaAjena].instance;
        },
    }
}
</script>

<style>
.padding-buttons {
    padding-bottom: -1px;
}
</style>
