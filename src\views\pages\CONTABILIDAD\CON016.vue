<template>
<vx-card title="Contabilidad General">
    <div class="content content-pagex">
        <vs-divider class="label-size"> FIN DEL EJERCICIO </vs-divider>
        <div class="flex flex-wrap">
            <div class="xs:w-full md:w-1/3 lg:w-1/3 xl:w-1/3"></div>
            <div class="padre xs:w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                <label class="label-size" align="left">Períodos</label>
                <DxSelectBox :data-source="ListaPeriodos" :display-expr="formatPeriodo" value-expr="CodigoPeriodo" v-model="cbPeriodos" :search-enabled="true" :accept-custom-value="true" @value-changed="guardarPeriodoCompleto" />
                <div style="padding:30px 30px">
                    <div class="flex flex-wrap" align="center">
                        <div class="xs:w-full md:w-1/2 lg:w-1/2 xl:w-1/2" style="padding:10px 10px">
                            <div class="label-size">
                                <vs-button color="warning" class="label-size" type="filled" @click="PartidaCierre()" :disabled="Bloqueo"> Partida de Cierre </vs-button>
                            </div>
                        </div>
                        <div class="xs:w-full md:w-1/2 lg:w-1/2 xl:w-1/2" style="padding:10px 10px">
                            <div class="label-size" align="center">
                                <vs-button color="success" class="label-size" type="filled" @click="ActualizaCActividad()"> OK </vs-button>
                            </div>
                        </div>

                    </div>
                </div>
                <br>
                <div class="cuadro-i" style="padding:20px 20px">
                    <label class="label-i">Este procedimiento registra definitivamente el saldo de cada cuenta para el período indicado. Después de este procedimiento puede mayorizar las partidas de cierre.</label>
                </div>
            </div>
        </div>
        <vs-divider></vs-divider>
    </div>

    <br>

    <vs-popup classContent="popup-example" title="Generar Partida de Diario" :active.sync="PartidaDeCierre" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
        <div>
            <form>

                <vs-divider class="label-size"> OPCIONES </vs-divider>

                <div class="flex flex-wrap mb-1">
                    <div class="w-full p-1" v-show="Bloqueo1">
                        <vs-alert color="danger" style="text-align:center;height:40px">
                            {{ this.error}}
                        </vs-alert>
                    </div>
                </div>

                <div style="padding:10px 50px" class="flex flex-wrap">
                    <div class="w-full md:w-1/2 lg:w-1/3 xl:w-2/6; label-size">
                        <vs-radio v-model="OpcionSel" vs-name="OpcionSel" vs-value="1"> Nueva Partida </vs-radio>
                        <ValidationProvider name="NuevaPartida" rules="required|numero_min:0|numero_entero" style="padding:10px 10px">
                            <vs-input type="number" v-model="NuevaPartida" />
                        </ValidationProvider>
                    </div>
                    <!--Se comenta porque el agregar partida ya no se utiliza

                        <div class="w-full md:w-1/2 lg:w-1/3 xl:w-2/6; label-size">
                            <vs-radio v-model="OpcionSel" vs-name="OpcionSel" vs-value="2"> Agregar Partida </vs-radio>
                            <ValidationProvider name="AgregarPartida" rules="required|numero_min:0|numero_entero" style="padding:10px 10px">
                                <vs-input type="number" v-model="AgregarPartida" :disabled="Bloqueo2==true" />
                            </ValidationProvider>
                        </div>-->
                </div>
                <vs-divider></vs-divider>
                <br>

                <div style="padding:10px 50px" class="flex flex-wrap">
                    <div class="w-full md:w-1/2 lg:w-1/3 xl:w-2/6">
                        <label class="label-size" style="padding:20px 8px"> Fecha </label>
                        <div class="w-full md:w-1/2 lg:w-1/3 xl:w-2/6" style="padding:1px 10px">
                            <flat-pickr v-model="Fecha" icon-pack="fas" icon="fa-calendar" :config="configFromdateTimePicker" disabled/>
                        </div>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-2/6">
                        <label class="label-size"> Referencia </label>
                        <vs-input type="text" v-model="Referencia" />
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-2/6">
                        <label class="label-size"> Cuenta Principal</label>
                        <vs-input type="text" v-model="Cuenta" @change="ValidarCuenta()" />
                    </div>
                </div>
                <br>
                <div class="w-full" style="padding:10px 50px">
                    <label class="label-size"> Descripción </label>
                    <vs-textarea rows="3" class="w-full" v-model="Descripcion" />
                </div>
                <div class="w-full" style="padding:10px 50px">
                    <div align="right">
                        <vs-button color="success" class="label-size" type="filled" @click="IngresoPartida()" :disabled="Bloqueo1"> OK </vs-button>
                    </div>
                </div>
            </form>

        </div>
    </vs-popup>
</vx-card>
</template>

<script>
import moment from "moment";
import flatPickr from 'vue-flatpickr-component';
import 'flatpickr/dist/flatpickr.css';
import {
    Spanish as SpanishLocale
} from 'flatpickr/dist/l10n/es.js';

export default {
    components: {
        flatPickr

    },
    data() {
        return {
            ListaPeriodos: [],
            CodigoPeriodo: '',
            DescPeriodo: '',
            OAgregarPartida: '',
            Fecha: '',
            Referencia: '',
            Cuenta: '',
            Descripcion: '',
            PartidaDeCierre: false,
            OpcionSel: '',
            NuevaPartida: '',
            AgregarPartida: '',
            Bloqueo: false,
            Bloqueo1: false,
            Bloqueo2: true,
            PeriodoAct: '',
            Periodo: '',
            FechaMin: '',
            FechaMax: '',

            cbPeriodos: null,
            periodoSeleccionado: null, // Objeto completo seleccionado

            configFromdateTimePicker: {
                minDate: '',
                maxDate: '',
                locale: SpanishLocale,
                dateFormat: "d/m/Y",
            },
            CuentasNValidas: [],
            error: ''

        }
    },
    mounted() {
        this.ConsultaPeriodo()
    },
    methods: {
        getDateValue(value) {
            if (value === undefined) {
                return '';
            } else if (value === null) {
                return '';
            } else if (value == '') {
                return '';
            }
            return moment(value).format('YYYY-MM-DD');
        },
        //PERIODOS
        ConsultaPeriodo() {
            this.axios.post('/app/v1_contabilidad_general/ConsultaPeriodo', {
                    Opcion: "C",
                    SubOpcion: "6",
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaPeriodos = resp.data.json;
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido cargar los periodos, favor validar.',
                        })
                    }
                    this.ConsultaPeriodoActual();
                })
                .catch(() => {})
        },
        ConsultaPeriodoActual() {
            this.axios.post('/app/v1_contabilidad_general/ConsultaPeriodoActual', {
                    Opcion: "C",
                    SubOpcion: "2",
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        const codigoPeriodoActual = resp.data.json[0].CodigoPeriodo;
                        this.FechaIPActual = resp.data.json[0].FechaInicial;
                        this.PeriodoAct = resp.data.json[0].CodigoPeriodo;
                        // Buscamos en la lista cargada
                        const periodo = this.ListaPeriodos.find(p => p.CodigoPeriodo === codigoPeriodoActual);
                        if (periodo) {
                            this.cbPeriodos = periodo.CodigoPeriodo;
                        }
                        this.NuevaPartida = resp.data.SigPartida === '' ? 1 : resp.data.json[0].SigPartida;

                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido cargar los periodos, favor validar.',
                        });
                    }
                });
        },
        guardarPeriodoCompleto(e) {
            let codigoSeleccionado = e.value;
            this.periodoSeleccionado = this.ListaPeriodos.find(
                (periodo) => periodo.CodigoPeriodo === codigoSeleccionado
            );
            // Ahora `periodoSeleccionado` contiene el objeto completo

            if (this.periodoSeleccionado.CodigoPeriodo < this.PeriodoAct) {
                this.Bloqueo = true
            } else {
                this.Bloqueo = false
            }
        },

        PartidaCierre() {
            this.Cuenta = ''
            this.Referencia = ''
            this.Descripcion = ''
            this.OpcionSel = 1
            this.PartidaDeCierre = true
            this.ConsultaPeriodoActual()

            // Asegurar que cbPeriodos y periodoSeleccionado están definidos
            if (this.cbPeriodos && this.cbPeriodos.CodigoPeriodo) {
                this.Periodo = this.cbPeriodos.CodigoPeriodo
            }
            const info = this.periodoSeleccionado

            if (info && info.CodigoPeriodo) {
                // Llamar a onChangePeriodos solo si la información es válida
                this.onChangePeriodos(info)
            }
        },

        onChangePeriodos(value) {
            if (value !== null && value.length !== 0) {

                this.Bloqueo = false
                this.CodigoPeriodo = value.CodigoPeriodo;
                this.DescPeriodo = value.DescPeriodo;
                this.NuevaPartida = value.CorrelativoPartidas === '' ? 1 : value.CorrelativoPartidas;

                // Establecer FechaMin y FechaMax
                this.FechaMin = new Date(value.FechaInicial);

                this.Fecha = this.FechaMax

                let fechaFinal = new Date(value.FechaFinal);
                fechaFinal.setDate(fechaFinal.getDate() + 1); // sumar 1 día a la final para incluirla
                this.FechaMax = fechaFinal;

                // Configurar flatpickr con minDate, maxDate y enable
                this.configFromdateTimePicker.minDate = this.FechaMin;
                this.configFromdateTimePicker.maxDate = this.FechaMax;
                this.configFromdateTimePicker.enable = [
                    (date) => date >= this.FechaMin && date <= this.FechaMax
                ];

                // Calcular la fecha por defecto: FechaMin + 1 día
                let fechaSeleccionada = new Date(this.FechaMin);
                fechaSeleccionada.setDate(fechaSeleccionada.getDate() + 1);

                // Si la fecha seleccionada está fuera del rango, usar FechaMin
                if (fechaSeleccionada > this.FechaMax) {
                    this.Fecha = this.FechaMin;
                } else {
                    this.Fecha = fechaSeleccionada;
                }
                this.Fecha = this.FechaMax

            } else {
                // Reset de los campos
                this.CodigoPeriodo = '';
                this.DescPeriodo = '';
                this.NuevaPartida = '';
                this.Fecha = new Date(); // Podrías dejar vacío también: ''
                this.FechaMin = '';
                this.FechaMax = '';
                this.configFromdateTimePicker.enable = [];
            }
        },
        formatPeriodo(item) {
            if (!item) return '';
            return `${item.CodigoPeriodo} - ${item.DescPeriodo}`;
        },
        IngresoPartida() {
            if (this.CuentasNValidas.length == 0) {
                if (this.Partida == 0 || this.Partida == '') {
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Generar Partida de Diario',
                        text: 'Ingrese Número de partida',
                    })
                    return;
                }
                if (this.Partida == 0 || this.Partida == '') {
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Generar Partida de Diario',
                        text: 'Ingrese Número de partida',
                    })
                    return;
                }
                if (this.periodoSeleccionado.CodigoPeriodo == 0 || this.periodoSeleccionado.CodigoPeriodo == '') {
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Generar Partida de Diario',
                        text: 'Seleccione el periodo',
                    })
                    return;
                } else {
                    this.axios.post('/app/v1_contabilidad_general/IngresoPartidasC', {
                            Opcion: "I",
                            SubOpcion: "1",
                            OpcionGC: this.OpcionSel, 
                            Periodo: this.periodoSeleccionado.CodigoPeriodo == '' ? this.CodigoPeriodo : this.periodoSeleccionado.CodigoPeriodo,
                            Partida: this.OpcionSel == '1' ? this.NuevaPartida : this.AgregarPartida,
                            NoLinea: 1,
                            Cuenta: this.Cuenta,
                            Fecha: this.Fecha,
                            Referencia: this.Referencia,
                            Descripcion: this.Descripcion

                        })
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.NuevaPartida = ''
                                this.PartidaDeCierre = false
                                this.ConsultaPeriodo()
                               // this.guardarPeriodoCompleto(this.periodoSeleccionado.CodigoPeriodo)
                                
                            }
                        })

                }

            }
        },
        ActualizaCActividad() {
            this.axios.post('/app/v1_contabilidad_general/ActualizaCActividad', {
                Opcion: "U",
                SubOpcion: "1",
                Periodo: this.periodoSeleccionado.CodigoPeriodo
            })
        },
        ValidarCuenta() {
            this.CuentasNValidas = []
            this.axios.post('/app/v1_contabilidad_general/ConsultasCuentas', {
                    Opcion: 'C',
                    SubOpcion: '14',
                    CodigoCuenta: this.Cuenta,
                })
                .then((resp) => {
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        this.Bloqueo1 = false
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'Validar Cuentas, o no existen o no es correcta.',
                        })
                        this.Bloqueo1 = true
                        this.error = 'La Cuenta Principal ingresada no es correcta o no existe.'
                    }
                })
        }
    }
}
</script>   

<style scoped>
.footer {
    height: 70px;
    margin-top: -65px;
    margin-left: 10px;
}

.label-size {
    font-size: 14px;
    font-weight: bold;
}

.label-i {
    font-size: 18px;
    font-weight: bold;
    color: rgb(240, 158, 6);
}

.cuadro-i {
    background: #faf2f1;
}

.flex-container {
    display: flex;
    flex-direction: row;
}

.flex-item-right {
    padding: 10px;
    flex: 50%;
    margin-left: 50px;
    margin-right: 50px;
}

/* Responsive layout - makes a one column layout instead of a two-column layout */
@media (max-width: 800px) {
    .flex-container {
        flex-direction: column;
    }
}

.padre {
    position: relative;
}

.hijo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
</style>
