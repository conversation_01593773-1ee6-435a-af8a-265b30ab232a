<template>
<vx-card title="Reporte histórico paciente" class="reporte-historico-paciente">
    <div id="content" class="ml-2" width="100%">
        <form @submit="handleSubmit">
            <DxForm :ref="formConsulta" :form-data.sync="formulario" label-mode="floating" :show-validation-summary="true">
                <DxFormGroupItem :col-count="2">
                    <DxFormItem :validationRules="[{ type: 'required', message: 'Debe indicar la fecha inicial del rango de la consulta' }]" data-field="FechaInicial" editor-type="dxDateBox" :editor-options="editorOptionsFecha">
                        <DxFormLabel text="De la fecha registro del traslado" />
                    </DxFormItem>
                    <DxFormItem :validationRules="[{ type: 'required', message: 'Debe indicar la fecha final del rango de la consulta' }]" data-field="FechaFinal" editor-type="dxDateBox" :editor-options="{ dataType: 'date', dateSerializationFormat: 'yyyy-MM-dd HH:mm:ss', displayFormat: 'dd/MM/yyyy', max: new Date(), min: minimaFechaFin }" :min="minimaFechaFin">
                        <DxFormLabel text="A la fecha registro del traslado" />
                    </DxFormItem>
                </DxFormGroupItem>
                <DxFormButtonItem :button-options="buttonGenerar" name="Buscar" horizontal-alignment="center" verical-alignment="center" />
            </DxForm>
        </form>
        <div>
            <DxDataGrid class="mt-4" v-bind="propsDataGrid" :data-source="infoData" :headerFilter="{ visible: true, allowSearch: true }" :search-panel="{ visible: false }" :paging="{ enabled:true, pageSize:10 }" height="auto" :allow-adding="false" :export="{ enabled: infoData.length > 0, allowExportSelectedData: false, fileName: 'Reporte' }" :onExporting="exportarExcel">
                <DxDataGridColumn width="300px" data-field="NombreComercial" caption="Nombre hospital" alignment="center" data-type="string" :header-filter="{ width: 330 }" />
                <DxDataGridColumn width="110px" data-field="Empresa" alignment="center" data-type="string"  />
                <DxDataGridColumn width="150px" data-field="Entrada" alignment="center" data-type="datetime" format="dd/MM/yyyy HH:mm:ss" :allow-filtering="false" />
                <DxDataGridColumn width="170px" data-field="HabitacionNueva" caption="Habitación nueva" alignment="center" data-type="string" />
                <DxDataGridColumn width="170px" data-field="HabitacionAnterior" caption="Habitación anterior" alignment="center" data-type="string" :allow-filtering="false" />
                <DxDataGridColumn width="120px" data-field="Admision" caption="Admisión" alignment="center" data-type="string" />
                <DxDataGridColumn width="150px" data-field="FechaRegistro" caption="Fecha registro" alignment="center" data-type="datetime" format="dd/MM/yyyy HH:mm:ss" :allow-filtering="false" />
                <DxDataGridColumn width="150px" data-field="FechaEgreso" caption="Fecha egreso" alignment="center" data-type="datetime" format="dd/MM/yyyy HH:mm:ss" :allow-filtering="false" />
                <DxDataGridColumn width="auto" data-field="Dias" caption="Días" alignment="center" data-type="number" :allow-filtering="false" />
                <DxDataGridColumn width="300px" data-field="NombrePaciente" caption="Nombre paciente" alignment="center" data-type="string" :allow-filtering="false" />
                <DxDataGridColumn width="auto" data-field="Asegura" alignment="center" data-type="string" :allow-filtering="false" />
                <DxDataGridColumn width="350px" data-field="Aseguradora" alignment="center" data-type="string" :allow-filtering="false" />
                <DxDataGridColumn width="300px" data-field="NombreMedico" caption="Nombre médico" alignment="center" data-type="string" :allow-filtering="false" />
                <DxDataGridColumn width="250px" data-field="Especialidad" alignment="center" data-type="string" :allow-filtering="false" />
                <DxDataGridColumn width="auto" data-field="Tipo" alignment="center" data-type="string" />
            </DxDataGrid>
        </div>
    </div>
</vx-card>
</template>

<script>
import ExcelJS from 'exceljs';
import saveAs from 'file-saver';

const formConsulta = 'formConsulta'

export default {
    name: 'HistoricoPaciente',
    components: {},
    data() {
        return {
            formConsulta,
            formulario: {
                FechaInicial: null,
                FechaFinal: null
            },

            cuentas: [],
            periodos: [],
            conciliaciones: [],

            infoData: [],

            buttonGenerar: {
                width: 'auto',
                icon: 'fas fa-search',
                text: 'Buscar',
                type: 'default',
                onClick: () => {
                    this.ObtenerInformacion()
                },
                useSubmitBehavior: true,
            },
            export: {
                enabled: true,
                formats: ['xlsx'],
            },

            propsDataGrid: {
                visible: true,
                showRowLines: true,
                showColumnLines: true,
                showBorders: true,
                'load-panel': {
                    enabled: false
                },
                selection: {
                    mode: 'single'
                },
                searchPanel: {
                    visible: true
                },
                focusedRowEnabled: false,
                rowAlternationEnabled: true,
                columnHidingEnabled: true,
                hoverStateEnabled: true,
                columnAutoWidth: true,
                allowColumnReordering: true,
                allowColumnResizing: true,
                columnResizingMode: 'widget',
                headerFilter: {
                    visible: true,
                    allowSearch: true
                },
                wordWrapEnabled: true,
                paging: {
                    enabled: true,
                    pageSize: 10
                },
                scrolling: {
                    showScrollbar: 'always',
                    useNative: false,
                },
            },

            minimaFechaFin: null,

            editorOptionsFecha: {
                dataType: 'date',
                dateSerializationFormat: 'yyyy-MM-dd HH:mm:ss',
                displayFormat: 'dd/MM/yyyy',
                max: new Date(),
                onValueChanged: this.actualizarFechaMinima,
            },
        }
    },
    props: {},
    methods: {
        handleSubmit(e) {
            e.preventDefault()
            // this.DescargarPDF()
        },

        async ObtenerInformacion() {
            const result = this.$refs.formConsulta.instance.validate(); // Ajusta el ref
            if (!result.isValid) return;
            await this.axios.post('/app/v1_JefeCaja/ObtenerHistorico', {
                    FechaInicial: this.formulario.FechaInicial,
                    FechaFinal: this.formulario.FechaFinal
                    // FechaInicial: new Date(this.formulario.FechaInicial + 'T00:00:00'),
                    // FechaFinal: new Date(this.formulario.FechaFinal + 'T23:59:59')
                })
                .then(resp => {
                    this.infoData = resp.data.json
                })
        },

        async exportarExcel(e) {
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet('Datos');

            DevExpress.excelExporter.exportDataGrid({
                worksheet: worksheet,
                component: e.component,
                autoFilterEnabled: true
            }).then(() => {
                workbook.xlsx.writeBuffer().then((buffer) => {
                    saveAs(new Blob([buffer], {
                        type: 'application/octet-stream'
                    }), 'Reporte.xlsx');
                });
            });

            e.cancel = true; // Evita la exportación por defecto
        },

        actualizarFechaMinima(e) {
            console.log(e)
            this.minimaFechaFin = new Date(e.value);
            this.formulario.FechaFinal = new Date(e.value)
            if (this.formulario.FechaInicial && this.FechaFinal < e.value) {
                this.minimaFechaFin = null; // Limpia si la fechaFin ya no es válida
            }
        }

        // async CargarCuentas() {
        //     await this.axios.post('/app/v1_bancos/Cuentas', {
        //             Opcion: 1
        //         })
        //         .then(resp => {
        //             this.cuentas = resp.data.json

        //             this.formulario.Cuenta = this.cuentas[0].Codigo
        //         })
        // },

        // async CargarPeriodos() {
        //     await this.axios.post('/app/v1_bancos/Periodos', {
        //             Opcion: 3,
        //         })
        //         .then(resp => {
        //             this.periodos = resp.data.json

        //             let hoy = new Date().setHours(0, 0, 0, 0)

        //             for (let i = 0; i < this.periodos.length - 1; i++) {
        //                 let fin = new Date(this.periodos[i].FechaFinal).setHours(0, 0, 0, 0)
        //                 let inicio = new Date(this.periodos[i].FechaInicial).setHours(0, 0, 0, 0)

        //                 if (hoy >= inicio && hoy <= fin) {
        //                     this.formulario.Periodo = this.periodos[i].Codigo
        //                 }
        //             }

        //         })
        // },

    },
    created() {},
    mounted() {},
    beforeMount() {
        // this.CargarCuentas()
        // this.CargarPeriodos()
        // this.CargarConciliaciones()
    },
    watch: {},
    computed: {}
}
</script>

<style>
.reporte-historico-paciente .dx-item.dx-radiobutton {
    display: flex;
    border-color: #706969 !important;
    border-style: dotted !important;
    border-width: 1px !important;
    width: auto !important;
    place-content: left;
    padding: 5px !important;
    margin-bottom: 5px !important;
}

.centered-items .dx-item-content .dx-box-item-content {
    display: grid !important;
    place-content: center !important;
}

.centered-item .dx-item-content.dx-box-item-content {
    display: grid !important;
    place-content: center !important;
}

.itemSelected {
    color: white;
    background-color: #337ab7;
}

.itemNoSelected {
    color: black;
    background-color: transparent;
}

.borderDiv {
    border-style: solid;
    border-color: blueviolet;
}
</style>
