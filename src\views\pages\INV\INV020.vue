<template>
    <vx-card title="Autorización Documentos">
        <div class="content content-pagex">
            <div class="terms">
    
                <div class="flex flex-wrap">
                    <div style="margin-left:10px;margin-right:20px" class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4">
                        <ValidationProvider name="selectClase" v-slot="{ errors }" rules="required" class="required">
                            <label style="font-size:12px">Documento</label>
                            <multiselect v-model="cb_documentos" :options="Lista_documentos" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Documento_seleccionado" placeholder="Búsqueda" @input="onChangeDocumento" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
    
                    </div>
    
                    <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7">
                       <vs-button color="primary" style="float:left;margin-top:20px;margin-right:20px" type="filled" icon-pack="fas" icon="fa-search" @click="Consultar_Autorizaciones('N')"> Buscar</vs-button>
                   
                    </div>
                    <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7">
                        <vs-button color="primary" style="float:left;margin-top:20px;margin-right:20px" type="filled" icon-pack="fas" icon="fa-plus" @click="Abrir_Nueva_Autorizacion()"> Nueva</vs-button>
                    </div>
    
                </div>
    
                <!----- DETALLE TABLA--->
                <div class="flex flex-wrap">
                    <!---<div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5"> --->
                    <div style="margin-left:10px" class="w-full md:w-full lg:w-full xl:w-full">
                        <vs-table2 max-items="10" pagination :data="Lista_autorizaciones" noDataText="Sin datos disponibles" search id="tb_departamentos">
    
                            <template slot="thead">
                                <th>Documento</th>
                                <th>Departamento</th>
                                <th>Responsable Autorización</th>
                                <th>Tipo</th>
                                <th></th>
                            </template>
    
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2 width="200px" :data="data[indextr].DESCRIPCION_DOCUMENTO">
                                        {{data[indextr].DESCRIPCION_DOCUMENTO}}
                                    </vs-td2>
    
                                    <vs-td2 width="200px" :data="data[indextr].DESCRIPCION_DEPTO">
                                        {{data[indextr].DESCRIPCION_DEPTO}}
                                    </vs-td2>
    
                                    <vs-td2 width="100px" :data="data[indextr].NOMBRE_RESPONSABLE">
                                        {{data[indextr].NOMBRE_RESPONSABLE}}
                                    </vs-td2>
                                    <vs-td2 width="100px" :data="data[indextr].TIPO">
                                        {{data[indextr].TIPO}}
                                    </vs-td2>
                                    <vs-td2 align="right" width="150px">
                                        <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click.native="Eliminar_Registro(data[indextr])"></vs-button>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
    
                    </div>
                </div>
            </div>
        </div>
    
        <!-----------------LLamanda de componenetes ----------------------->
        <ComponentNuevAutor v-if="Estado_VentanaNueva" ref="ComponenteNuevaAutorizacion" :callback="Abrir_Nueva_Autorizacion" :cerrar="()=>Estado_VentanaNueva=false" />
        <ComponentNuevAutorCotiz v-if="Estado_VentanaNueva_Cotizacion" ref="ComponenteNuevaCotizacion" :callback="Abrir_Nueva_Autorizacion" :cerrar="()=>Estado_VentanaNueva_Cotizacion=false" />
        <ComponentNuevOrdenCompra v-if="Estado_VentanaNueva_Orden" ref="ComponenteNuevaOrdenCompra" :callback="Abrir_Nueva_Autorizacion" :cerrar="()=>Estado_VentanaNueva_Orden=false" />
    </vx-card>
    </template>
    
    <script>
    /**
     * @General
     * Modulo registra los responsables por cada documento
     */
    import Multiselect from 'vue-multiselect'
    import "vue-multiselect/dist/vue-multiselect.min.css"
    
    export default {
        components: {
            'ComponentNuevAutor': () => import('./INV020_NuevaAutorizacion'),
            'ComponentNuevAutorCotiz': () => import('./INV020_AutorCotizacion'),
            'ComponentNuevOrdenCompra': () => import('./INV020_AutorOrdenCompra'),
    
            Multiselect
        },
        created() {
            this.$root.$refs.A = this;
        },
        data() {
            return {
                Estado_VentanaNueva: false,
                Estado_VentanaNueva_Cotizacion: false,
                Estado_VentanaNueva_Orden: false,
    
                cb_documentos: '',
                Lista_documentos: [],
                id_doc_seleccionado: '',
    
                cb_responsable: '',
                Lista_responsable: [],
                id_responsable_seleccionado: '',
    
                Lista_autorizaciones: [],
                Corporativo_Sesion: '',
            }
        },
        methods: {
            //#region  Multiseleccionables
            onChangeResponsable(value) {
    
                //Si existe seleccionada un elemento
                if (value !== null && value.length !== 0) {
                    this.id_responsable_seleccionado = value.Corporativo;
                } else {
                    this.id_responsable_seleccionado = '';
                }
    
            },
            Corporativo_seleccionado({
                Corporativo,
                nombre
            }) {
                return `${Corporativo} - ${nombre} `
            },
            Documento_seleccionado({
                DESCRIPCION
            }) {
                return `${DESCRIPCION} `
            },
            onChangeDocumento(value) {
                if (value !== null && value.length !== 0) {
                    this.id_doc_seleccionado = value.CODIGO;
                    this.Consultar_Autorizaciones(value)
                } else {
                    this.id_doc_seleccionado = '';
                }
            },
            //#endregion
            //#region Metodos datos
            Cargar_Documentos() {
                const url = this.$store.state.global.url;
                const sesion = this.$store.state.sesion;
                this.$vs.loading();
                this.axios.post(url + 'app/v1_OrdenCompra/consulta_tipo_documentos', {
                        empresa: sesion.sesion_empresa,
    
                    })
                    .then(resp => {
                        this.$vs.loading.close();
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Autorización Documentos',
                                text: resp.data.mensaje,
                            })
                            this.Lista_documentos = [];
                        } else {
                            this.Lista_documentos = resp.data.json;
    
                        }
                    })
                    .catch(() => {
                        this.$vs.loading.close();
                    })
                this.$vs.loading.close();
            },
            Consultar_Responsable(Tipo_Busqueda = '', Busqueda = '') {
    
                const url = this.$store.state.global.url
                this.$vs.loading();
                this.axios.post(url + 'app/v1_OrdenCompra/consulta_corporativo', {
                        TIPO_BUSQUEDA: Tipo_Busqueda,
                        BUSQUEDA: Busqueda
                    })
                    .then(resp => {
                        this.$vs.loading.close();
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Autorización Documentos',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_responsable = "";
                        } else {
                            this.Lista_responsable = resp.data.json;
                        }
                    })
                    .catch(() => {
                        this.$vs.loading.close();
                    })
                this.$vs.loading.close();
    
            },
            Consultar_Autorizaciones: function () {
    
                if (this.id_doc_seleccionado !== null && this.id_doc_seleccionado.length !== 0) {
                    const url = this.$store.state.global.url;
                    const sesion = this.$store.state.sesion;
                    this.$vs.loading();
                    this.axios.post(url + 'app/v1_OrdenCompra/consulta_listado_autorizaciones', {
                            empresa: sesion.sesion_empresa,
                            id_documento: this.id_doc_seleccionado,
                            num_documento: '0',
                            tipo_consulta: 'T',
                            id_departamento: '0'
    
                        })
                        .then(resp => {
                            this.$vs.loading.close();
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Autorización Documentos',
                                    text: resp.data.mensaje,
                                })
                                //Limpia la tabla si no existen registros
                                this.Lista_autorizaciones = "";
                            } else {
                                this.Lista_autorizaciones = resp.data.json;
    
                            }
                        })
                        .catch(() => {
                            this.$vs.loading.close();
                        })
                    this.$vs.loading.close();
                } else {
                    this.Lista_autorizaciones = '';
                }
    
            },
            Eliminar_Registro(value) {
                /**
                 * @General
                 * Función eliminar registro;
                 */
    
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',
                    text: '¿Dar de baja autorización \'' + value.DESCRIPCION_DOCUMENTO + ' - ' + value.NOMBRE_RESPONSABLE + '\'? ',
                    accept: () => {
                        const url = this.$store.state.global.url;
                        const sesion = this.$store.state.sesion;
                        this.$vs.loading();
                        
                        this.axios.post(url + 'app/v1_OrdenCompra/autorizacion_doc', {
                                empresa: sesion.sesion_empresa,
                                codigo: value.ID_AUTORIZACION,
                                id_documento: 0,
                                id_responsable: 0,
                                id_departamento: 0,
                                num_documento: 0,
                                operacion: 'B',
                                corporativo: sesion.corporativo,
                                activo: 'N',
                                tipo: value.SP
                            })
                            .then(resp => {
                                this.$vs.loading.close();
                                if (resp.data.codigo != 0) {
                                    this.$vs.notify({
                                        color: '#B71C1C',
                                        title: 'Autorización Documentos',
                                        text: resp.data.mensaje,
                                    })
                                    //Limpia la tabla si no existen registros
    
                                } else {
                                    this.Consultar_Autorizaciones()
    
                                }
                            })
                            .catch(() => {
                                this.$vs.loading.close();
                            })
                        this.$vs.loading.close();
                    }
                })
    
            },
            Abrir_Nueva_Autorizacion() {
    
                if (this.id_doc_seleccionado !== null && this.id_doc_seleccionado.length !== 0) {
    
                    if (this.id_doc_seleccionado === "1") {
                        this.Estado_VentanaNueva = true;
                    } else if (this.id_doc_seleccionado === "2") {
                        this.Estado_VentanaNueva_Cotizacion = true;
                    } else if (this.id_doc_seleccionado === "3") {
                        this.Estado_VentanaNueva_Orden = true;
                    }
                } else {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Autorización Documentos',
                        text: 'Seleccionar documento',
                    })
    
                }
            },
           
        },
    
        mounted() {
            this.Cargar_Documentos();
            this.Consultar_Responsable('T', '')
        }
    }
    </script>
    
    <style scoped>
    
    </style>
    