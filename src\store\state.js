/*=========================================================================================
  File Name: state.js
  Description: Vuex Store - state
  ----------------------------------------------------------------------------------------
  Item Name: Vuexy - Vuejs, HTML & Laravel Admin Dashboard Template
  Author: Pixinvent
  Author URL: http://www.themeforest.net/user/pixinvent
==========================================================================================*/
import navbarSearchAndPinList from "@/layouts/components/navbar/navbarSearchAndPinList"
import themeConfig from "@/../themeConfig.js"
import colors from "@/../themeConfig.js"

/******************************************************************************************
 *  Helper
 ******************************************************************************************/
const globalDefecto = {
    instancia: null,
    instancia_urls: null,
    tokenDefault: null,
    url: null,
}
try {
    globalDefecto.instancia = (localStorage.getItem("sermesa_sesion_inst")) ? JSON.parse(atob(localStorage.getItem("sermesa_sesion_inst"))).instancia : process.env.VUE_APP_INSTANCE
    globalDefecto.instancia_urls = (localStorage.getItem("sermesa_sesion_ur")) ? JSON.parse(atob(localStorage.getItem("sermesa_sesion_ur"))).instancias : process.env
    globalDefecto.tokenDefault = (localStorage.getItem("sermesa_sesion_tk")) ? JSON.parse(atob(localStorage.getItem("sermesa_sesion_tk"))).token : process.env.VUE_APP_TOKENDEFAULT
} catch (error) {
    localStorage.removeItem('userInfo')
    localStorage.removeItem('sermesa_sesion')
    localStorage.removeItem('sermesa_sesion_funcionalidad')
    localStorage.removeItem('sermesa_sesion_tk')
    localStorage.removeItem('sermesa_sesion_ur')
    localStorage.removeItem('sermesa_sesion_inst')
}
globalDefecto.url = globalDefecto.instancia_urls["VUE_APP_URL_" + globalDefecto.instancia]


const extraDefecto = {
    notificacion: null,
    loading: null,
    solicitarCredencial: null // Permite solicitar credenciales
}

const usuarioDefecto = {
    photoURL: "@/assets/images/others/usuario.png",
    corporativo: 0, // From Auth
    usuario: "", // From Auth
    about: "",
    ip: "",
    token: globalDefecto.tokenDefault,
    status: "online",
    // indica si el socket esta activo y en linea
    status_socket: false,
    //indica si el socket del appPC esta activo
    appPC_socket: null,
    //muestra el mensaje de bienvenida
    bienvenida: true,
    //indica si ya se valido la información
    sesion_validar: false,
    //indica se ha seleccionado la empresa
    sesion_empresa: "",
    sesion_sucursal: "",
    sesion_sucursal_actualizacion: false,
    sesion_sucursal_nombre: "",
    // 60seg * 10min -> utilizado como constante
    sesion_tiempo_defecto: 1800,
    sesion_tiempo: null, // 60seg * 5min
}

const is_touch_device = () => {
    var prefixes = ' -webkit- -moz- -o- -ms- '.split(' ');
    var mq = function (query) {
        return window.matchMedia(query).matches;
    }
    if (('ontouchstart' in window) || window.DocumentTouch) return true;
    var query = ['(', prefixes.join('touch-enabled),('), 'heartz', ')'].join('');
    return mq(query);
}

// /////////////////////////////////////////////
// State
// /////////////////////////////////////////////
const state = {
    AppActiveUser: usuarioDefecto,
    bodyOverlay: false,
    isVerticalNavMenuActive: true,
    is_touch_device: is_touch_device(),
    mainLayoutType: themeConfig.mainLayoutType || "vertical",
    navbarSearchAndPinList: navbarSearchAndPinList,
    reduceButton: themeConfig.sidebarCollapsed,
    verticalNavMenuWidth: "default",
    verticalNavMenuItemsMin: false,
    scrollY: 0,
    starredPages: navbarSearchAndPinList["pages"].data.filter((page) => page.is_bookmarked),
    theme: themeConfig.theme || "light",
    themePrimaryColor: colors.primary,

    //informacion global
    global: globalDefecto,

    //funcionalidades
    funcionalidades: null,
    id_funcionalidad: null,
    funcionalidad_doc: null, // Documentación del módulo

    //privilegios
    privilegios: [],

    //tabs
    tabs: [],
    tabPath: null,

    //sesion
    sesion: (localStorage.getItem("sermesa_sesion")) ? JSON.parse(atob(localStorage.getItem("sermesa_sesion"))) : usuarioDefecto,
    windowWidth: null,

    //extra
    extra: extraDefecto,

    reporte: {
        titulo: null,
        pdf: null, // Base 64
        abrirTab: false, // Abrir nueva pestaña
        imprimir: false, // Imprimir
    }, // Reporte base64
}

try {
    state.funcionalidades = (localStorage.getItem("sermesa_sesion_funcionalidad")) ? JSON.parse(atob(localStorage.getItem("sermesa_sesion_funcionalidad"))) : []
} catch (error) {
    // localStorage.clear()
    localStorage.removeItem('userInfo')
    localStorage.removeItem('sermesa_sesion')
    localStorage.removeItem('sermesa_sesion_funcionalidad')
    localStorage.removeItem('sermesa_sesion_tk')
    localStorage.removeItem('sermesa_sesion_ur')
    localStorage.removeItem('sermesa_sesion_inst')
}

export default state