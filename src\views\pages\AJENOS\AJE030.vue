<template>
    <vx-card type = "2" title="Traslado Paciente" class="justify-center ">    
        <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
            <form ref="formTraslado" method="post" @submit.prevent="handleSubmit(traslado_paciente())">
                <div class="flex flex-wrap">
                    <vs-divider position="left">Datos Admisión</vs-divider>
                    <div class="w-full md:w-full lg:w-1/12 xl:w-1/12 m-1">
                        <ValidationProvider rules="required" v-slot="{ errors }">
                            <vs-input label="Serie" v-model="info.serie" 
                                        class="w-full" 
                                        v-on:keyup="info.serie = info.serie?.toUpperCase() || ''" 
                                        :disabled = "info.desactivarCamposAdmision"
                                        :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" /> 
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-full lg:w-2/12 xl:w-2/12 m-1">
                        <ValidationProvider rules="required" v-slot="{ errors }">
                            <SM-Buscar class="w-full" 
                                       label="Admisión" v-model="info.Admision" api="app/v1_admision/ConsultaAdmision" 
                                       :api_campos="['Serie','Codigo','Nombres','Apellidos']" 
                                       :api_titulos="['Serie#','Admision#','Nombres','Apellidos']" 
                                       :api_filtro="{'activo':0, 'Serie':info.serie}" 
                                       api_campo_respuesta="Codigo" :api_preload="false" :disabled_texto="info.desactivarCamposAdmision" :mostrar_busqueda="true" 
                                       :callback_buscar="cargaAdmision" :callback_cancelar="quitarAdmision"                                                                                                          
                                       :danger="errors.length > 0"
                                       :danger-text="(errors.length > 0) ? errors[0] : null" />                                
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-full lg:w-7/12 xl:w-3/12 m-1">
                        <ValidationProvider rules="required" v-slot="{ errors }">
                            <vs-input label="Nombre del paciente:"  
                                      class="w-full" 
                                      :value="info.paciente" disabled 
                                      :danger="errors.length > 0"
                                      :danger-text="(errors.length > 0) ? errors[0] : null" />                                
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-full lg:w-1/12 xl:w-5/12"></div>
    
                    <vs-divider position="left">Habitación</vs-divider>

                        <div class="w-full md:w-full lg:w-3/12 xl:w-2/12 m-1 flex flex-wrap flex-column">
                            <label class="w-full">Habitación Anterior:</label>                                        
                            <label class="w-full flex flex-wrap">
                                <div class="w-4/12 text-center p-2" style="color:black; margin:auto;">{{info.habitacion}}</div>   
                                <div class="w-7/12 text-center p-2" style="color:black; margin:auto;">{{ info.nombreHabitacion }}</div>
                            </label>
                        </div>                        

                        <div class="w-full md:w-full lg:w-5/12 xl:w-3/12 m-1">
                            <label>Habitación Nueva:</label> 
                            <ConsultaHabitaciones ref="componenteHabitacionNueva" v-on:codigo-habitacion="obtenerCodigoHabitacion">
                            </ConsultaHabitaciones>
                        </div>
                        <div class="w-full md:w-full lg:w-2/12 xl:w-5/12"></div>


                        <div class="w-full md:w-full lg:w-3/12 xl:w-2/12 m-1 flex flex-wrap flex-column">
                            <label class="w-full">Acompañante Anterior:</label>                                        
                            <label class="w-full flex flex-wrap">
                                <div class="w-4/12 text-center p-2" style="color:black; margin:auto;">{{info.habitacionAcompanante}}</div>   
                                <div class="w-7/12 text-center p-2" style="color:black; margin:auto;">{{ info.nombreHabitacionAcompanante }}</div>
                            </label>
                        </div>                           

                        <div class="w-full md:w-full lg:w-5/12 xl:w-3/12 m-1">
                            <label>Habitación Nueva Acompañante:</label> 
                            <ConsultaHabitaciones  ref="componenteHabitacionNuevaAcompanante" v-on:codigo-habitacion="obtenerCodigoHabitacionAcompanante">
                            </ConsultaHabitaciones>
                        </div>
                        <div class="w-full md:w-full lg:w-2/12 xl:w-5/12"></div>

                    <vs-divider></vs-divider>                       
    
                    <div class="w-full md:w-full lg:w-10/12 xl:w-6/12 m-2 mt-0">
                        <ValidationProvider rules="required|min:8" v-slot="{ errors }">
                            <vs-input  label="Autorizó" class="w-full"  v-model="info.nombreAutorizo" 
                                      :danger="errors.length > 0"
                                      :danger-text="(errors.length > 0) ? errors[0] : null"/>
                        </ValidationProvider>
                    </div>   
                    
                    <div class="w-full md:w-full lg:w-10/12 xl:w-6/12 m-2 mt-0">
                        <ValidationProvider rules="required|min:8" v-slot="{ errors }">
                            <vs-input  label="Enfermera" class="w-full"  v-model="info.nombreEnfermera" 
                                      :danger="errors.length > 0"
                                      :danger-text="(errors.length > 0) ? errors[0] : null"/>
                        </ValidationProvider>
                    </div>  

                    <div class="w-full md:w-full lg:w-10/12 xl:w-6/12 m-2 mt-0">
                        <ValidationProvider rules="required" v-slot="{ errors }">
                            <vs-input  label="Corporativo" class="w-full"  v-model="info.corporativoIngreso"                                       
                                      v-on:blur="ValidarCorporativo()"
                                      :danger="errors.length > 0"
                                      :danger-text="(errors.length > 0) ? errors[0] : null"/>
                        </ValidationProvider>
                        <div class="pl-4" v-if="info.nombreCorporativoIngreso && info.nombreCorporativoIngreso!=''" style="background-color:#ecf0f1;">
                            {{ info.nombreCorporativoIngreso }}
                        </div>
                    </div>  

                    <div class="w-full m-2">
                        <vs-button color="success" class="w-full md:w-full lg:w-4/12 xl:w-1/12 m-2"
                            :disabled="info.desactivarBotonAceptar"
                            @click="handleSubmit(traslado_paciente(invalid))">
                            Traslado Paciente
                        </vs-button>
    
                        <vs-button class="w-full Emd:w-full lg:w-4/12 xl:w-1/12 m-2" color="warning" @click="confirmar_limpia_campos()">
                            Limpiar Campos
                        </vs-button>
                    </div>
                </div>
            </form>
        </ValidationObserver>        
    </vx-card>
    </template>
    
    <script>
    import ConsultaHabitaciones from "/src/components/sermesa/modules/admisiones/ConsultaHabitaciones.vue"
    export default {
        components: {
            ConsultaHabitaciones
        },
        data(){
            return {
                info:{                    
                    serie: null,
                    Admision: null,
                    admisionConSerie: null,
                    paciente: null,
                    desactivarBotonAceptar: true,
                    desactivarCamposAdmision: false,
                    habitacion: null,
                    nombreHabitacion: null,
                    habitacionAcompanante: null,                    
                    nombreHabitacionAcompanante: null,
                    habitacionNueva:null,
                    habitacionNuevaAcompanante:null,
                    corporativoIngreso: null,
                    nombreCorporativoIngreso: null,
                    nombreEnfermera: null,
                    nombreAutorizo: null
                }
            };
        },computed:{
        },methods:{
            cargaAdmision(datos) {
                this.info.nivelprecio = datos.NivelPrecios
                this.info.paciente = datos.Paciente
                this.info.serie = datos.Serie
                this.info.Admision = datos.Codigo           
                this.info.admisionConSerie = datos.Admision
                this.info.idPaciente = datos.IdPaciente
                this.info.habitacion = datos.Habitacion
                this.info.nombreHabitacion = datos.DescripcionHabitacion
                this.info.habitacionAcompanante =  datos.HabitacionAcompanante
                this.info.nombreHabitacionAcompanante = datos.DescripcionHabitacionAcompanante
                this.info.desactivarBotonAceptar = false
                this.info.desactivarCamposAdmision = true                        
            },   
            quitarAdmision(){
                this.limpiar_campos()
            },
            traslado_paciente(invalid){
                if(invalid){
                    this.$vs.notify({
                                        time: 4000,
                                        color: '#B71C1C',
                                        title: 'Alerta',
                                        text: 'Llenar los campos obligatorios.'
                                    })
                    return
                }         
                if(!this.info.habitacionNueva || this.info.habitacionNueva.length<=0){
                    this.$vs.notify({
                                        time: 4000,
                                        color: '#B71C1C',
                                        title: 'Alerta',
                                        text: 'Elegir una habitación nueva.'
                                    })
                    return
                }                       
                this.axios.post('/app/v1_admision/Traslado_Paciente',{                                         
                                                CorpIngresa: this.info.corporativoIngreso,
                                                SerieAdmision: this.info.serie,
                                                Admision: this.info.Admision,
                                                HabitacionNueva: this.info.habitacionNueva,
                                                HabAcompanante: this.info.habitacionNuevaAcompanante,
                                                Enfermera: this.info.nombreEnfermera,
                                                Autorizacion: this.info.nombreAutorizo                                                
                                            })
                                  .then((resp) => {
                                                        let error 
                                                        let mensajeError
                                                        if(resp.data.json && resp.data.json.length != 0){
                                                            error = resp.data.json[0].tipo_error
                                                            mensajeError = resp.data.json[0].descripcion
                                                        }else{
                                                            error = resp.data.tipo_error
                                                            mensajeError = resp.data.descripcion
                                                        }
    
                                                        if(error && error != 0){
                                                            this.$vs.notify({
                                                                time: 4000,
                                                                color: '#B71C1C',
                                                                title: 'Alerta',
                                                                text: mensajeError
                                                            })  
                                                        }
                                                        else{                               
                                                            this.limpiar_campos()      
                                                            this.$vs.notify({   
                                                                time: 5000,                                         
                                                                title:'Exito',
                                                                text: resp.data.json[0].descripcion,
                                                                color:'success'
                                                            })
                                                        }
                                                    }
                                        )  
    
            },
            ValidarCorporativo(){                
                this.axios.post('/app/v1_admision/BusquedaCorporativo',{                                         
                                                 CorporativoBusqueda: this.info.corporativoIngreso                                              
                                            })
                          .then((resp) => {
                                            let error 
                                            let mensajeError
                                            if(resp.data.json && resp.data.json.length != 0){
                                                error = resp.data.json[0].tipo_error
                                                mensajeError = resp.data.json[0].descripcion
                                            }else{
                                                error = resp.data.tipo_error
                                                mensajeError = resp.data.descripcion
                                            }

                                            if(error && error != 0){
                                                this.info.nombreCorporativoIngreso = null
                                                this.info.corporativoIngreso = null

                                                this.$vs.notify({
                                                    time: 4000,
                                                    color: '#B71C1C',
                                                    title: 'Alerta',
                                                    text: mensajeError
                                                })  
                                            }
                                            else{                               
                                                this.info.nombreCorporativoIngreso = resp.data.json[0].NombreCorporativo
                                            }
                                        }
                                )  
            },
            limpiar_campos(){                       
                this.info.serie=null
                this.info.Admision=null
                this.info.admisionConSerie=null
                this.info.paciente=null
                this.info.habitacion=null
                this.info.nombreHabitacion=null
                this.info.habitacionAcompanante=null
                this.info.nombreHabitacionAcompanante=null
                this.info.habitacionNueva=null
                this.info.habitacionNuevaAcompanante=null
                this.info.corporativoIngreso=null
                this.info.nombreCorporativoIngreso=null
                this.info.nombreEnfermera=null
                this.info.nombreAutorizo=null

                this.info.desactivarBotonAceptar=true    
                this.info.desactivarCamposAdmision=false

                this.$refs.componenteHabitacionNueva.DatosHabitacion().Limpiar()
                this.$refs.componenteHabitacionNuevaAcompanante.DatosHabitacion().Limpiar()
            },
            limpiar_campos_exito(){                            
                this.info.admisionConSerie = null
                this.info.paciente = null         
            },
            confirmar_limpia_campos(){
                this.$vs.dialog({
                                type: 'confirm',
                                color: 'danger',
                                title: 'Confirmación',
                                acceptText: 'Limpiar',
                                cancelText: 'Cancelar',
                                text: `¿Desea limpiar los campos?`,
                                accept: () => {
                                    this.limpiar_campos()
                                }
                            })
            },
            obtenerCodigoHabitacion(codigo){
                if(!codigo) return
                this.info.habitacionNueva = codigo
                if(this.info.habitacionNueva == this.info.habitacionNuevaAcompanante){
                    this.$vs.notify({
                                        time: 4000,
                                        color: '#B71C1C',
                                        title: 'Alerta',
                                        text: 'Acompañante no puede ocupar la misma habitación que el paciente'
                                    }) 
                    this.info.habitacionNueva = null  
                    this.$refs.componenteHabitacionNueva.DatosHabitacion().Limpiar()   
                    this.$nextTick(() => {
                        this.$refs.componenteHabitacionNueva.CodigoHabitacion = ''                                      
                    })          
                }
                
            },
            obtenerCodigoHabitacionAcompanante(codigo){
                if(!codigo) return
                this.info.habitacionNuevaAcompanante = codigo
                if(this.info.habitacionNueva == this.info.habitacionNuevaAcompanante){
                    this.$vs.notify({
                                        time: 4000,
                                        color: '#B71C1C',
                                        title: 'Alerta',
                                        text: 'Acompañante no puede ocupar la misma habitación que el paciente'
                                    }) 
                    this.info.habitacionNuevaAcompanante = null                   
                    this.$refs.componenteHabitacionNuevaAcompanante.DatosHabitacion().Limpiar()                  
                    this.$nextTick(() => {
                        this.$refs.componenteHabitacionNuevaAcompanante.CodigoHabitacion = ''                        
                    })                    
                }                
            }
        },mounted(){        
        }
    }
    
    </script>
    
    <style>
    .centerx {
        display: flex;
    }
    
    .centerx li {
        margin-right: 10px
    }
    
    .tabla-polizas .contenedor-tabla {
        min-height: auto;
    }

    .datos_habitacion {
        border: 0px  !important;
    }

    .codigo-habitacion {
        grid-area: codigo-habitacion;
        border: 0px;
        border-radius: 5px;
    }
    </style>
