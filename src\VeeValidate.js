import { required, email, max, min, between, alpha_spaces, alpha_num, min_value, double, required_if  } from "vee-validate/dist/rules";
import { extend } from "vee-validate";

extend("required", {
  ...required,
  message: "El campo es requerido"
});

extend("min", {
  ...min,
  message: "El campo no debe de ser inferior a los {length} carácteres."
});

extend("max", {
    ...max,
    message: "El campo no debe de superar los {length} carácteres."
  });

extend('numero_entero', {
    validate(value) {
      return  /^\d+$/.test(value);///^-?\d+$/.test(value);
    },
    message: "El campo debe de ser un número entero."
});

extend('fecha', {
    validate(value) {
        // Date.parse(value)
      return  /^(\d{4})\.(\d{2})\.(\d{2})$/.test(value.replace(/-/g,'.'));///^-?\d+$/.test(value);
    },
    message: "El campo debe de ser un fecha válida."
});

extend('numero_min', {
    validate(value, args) {
      return value >= args.length;
    },
    params: ['length'],
    message: "El campo debe de ser mayor o igual a {length}."
});

extend('numero_max', {
    validate(value, args) {
      return value <= args.length;
    },
    params: ['length'],
    message: "El campo debe de ser menor o igual a {length}."
});

extend("email", {
  ...email,
  message: "El campo debe de contener un correo válido."
});

extend("between", {
  ...between,
  message: "El campo debe estar entre los valores {min} y {max}"
});
///^\d+$\.(\d{2})/.test(value);///^-?\d+$/.test(value);
/* 
extend('numero_decimal', {
  validate(value) {
    return  /^(\d)+(\.\d{2})?$/.test(value);
  },
  message: "El campo debe de ser un numero con dos digitos decimales"
});
*/

extend('numero_minimo', {
  ...min_value,
  message: "El campo debe de ser un numero mayor a {min}"
});

extend('numero_decimal', {
  ...double,
  message: "El campo debe de ser un numero con dos digitos decimales"
});


extend('fecha', {
  validate(value) {
      // Date.parse(value)
    return  /^(\d{4})\.(\d{2})\.(\d{2})$/.test(value.replace(/-/g,'.'));///^-?\d+$/.test(value);
  },
  message: "El campo debe de ser un fecha válida."
});

extend("alpha_spaces", {
  ...alpha_spaces,
  message: "El campo solamente debe contener letras"
});

extend("alpha_num", {
  ...alpha_num,
  message: "El campo solamente debe contener letras y números"
});

// Aaron Salazar 26/04/2023 Es requerido cuando depende de otro campo
extend("required_if", {
  ...required_if,
  message: "El campo es requerido"  
})


extend('greaterThanZero',{   
   validate: (value) => {
     if (value > 0 ) return true;
     return false;
   },
   message: field =>  field + 'no puede ser cero',
 });

extend('telefono', {
  validate(value) {
    return /^[2,6,7,3,4,5][0-9]{7}$/.test(value)
  },
  message: "El campo debe de ser un número telefónico."
});

extend('telefono_celular', {
  validate(value) {
    return /^[3,4,5][0-9]{7}$/.test(value)
  },
  message: "El campo debe de ser un número telefónico movil."
});

extend('telefono_fijo', {
  validate(value) {
    return /^[2,6,7][0-9]{7}$/.test(value)
  },
  message: "El campo debe de ser un número telefónico linea fija."
});