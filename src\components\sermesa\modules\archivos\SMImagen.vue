<template>
    <!--Subir Fotografia Perfil-->
    <div>
        <vs-button clientWidth="20" icon-pack="fas" :icon="icono" @click="(!disabled) ? Otros().visible(true) : null"
            :disabled="disabled">{{ texto }}</vs-button>
        <vs-popup ref="buscador" :title="texto" :active.sync="info.visible" style="z-index:99999"
            id="div-with-loading" class="vs-con-loading__container">
            <div class=" archivos">
                <vs-tabs style="height:calc(100%- 150px)" v-model="info.tab">
                    <vs-tab label="subir_archivo" v-if="subir_archivo">
                        <!--------------------------Adjuntar y eliminar firma---------------------------->

                        <div class="detalle flex">
                            <canvas ref="png" width="200px" height="200px" style="border:1px solid #ccc;display:none">
                                <vs-divider position="left">Foto del perfil</vs-divider>
                                <ul>
                                    <li>Tamaño: 8cm x 4cm</li>
                                    <li>Formato: JPEG, PNG y JPG</li>
                                </ul>
                            </canvas>

                            <div class="row" v-if="imagen_src.length > 0" style="width:100%">
                                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-2 imagen" v-for="(i, index) in archivos"
                                    :key="index" style="padding:10px;width:100%;text-align:center">
                                    <img :src="'data:image/png;base64,' + i.src" alt="">
                                </div>
                            </div>
                            <div v-else style="padding:10px;width:30%;">
                                <img src="@/assets/images/others/notfound.png" alt=""
                                    style="max-width:100%;max-height:100%">
                            </div>
                        </div>
                        <br>

                        <div style="margin:1px" class="flex flex-center">
                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                <input :accept="extensiones" style="display: none;" id="fileInput3" type="file" ref="fileUpload"
                                     @change="Guardar().archivo($event)" />
                                <vs-button class="icon-camara" onclick="document.getElementById('fileInput3').click()"
                                    icon-pack="fas" icon="fa-folder-open">Adjuntar Imegen</vs-button>
                            </div>
                            <div v-if="imagen_src.length > 0" class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                <vs-button @click="Guardar().eliminar_Imagen()" icon-pack="fas" color="red"
                                    icon="fa-trash">Eliminar</vs-button>
                            </div>
                        </div>
                    </vs-tab>

                </vs-tabs>

            </div>

        </vs-popup>



    </div>
    <!--Subir Imegen Horario-->
</template>
    
    
<script>
/**
 * Consulta: Necesita 3 campos <Cualquier campo identificador del archivo>, <src> base 64, <tipo> imgen/otros
 */
import SignaturePad from 'signature_pad'
export default {
    name: 'SMImagen',
    data() {
        return {



            info: {
                visible: false,
                imagen: null,
                signaturePad: null,
                tab: 0
            },
            archivos: [],
            imagen_src: ''
        }
    },
    props: {
        disabled: {
            default: false
        },
        texto: {
            default: 'Adjuntos'
        },
        icono: {
            default: 'fa-paperclip'
        },
        extensiones: {
            default: '.png, .jpg, .jpeg*'
        },
        /**
         * Indica si se cargaran multiples archivos
         */
        multiples_archivos: {
            default: false
        },
        /**
         * Se habilita la opción de subir archivos
         */
        subir_archivo: {
            default: true
        },
        /**
         * Se habilita la opción para subir firma
         */
        imagen: {
            default: false
        },
        /**
         * Indica a que url se enviara la consulta y el guardado de archivos
         */
        api: {

            default: null
        },
        /**
         * Se agrega los otros criterios del api
         */
        api_parametros: {
           
            default: null
        },
        /**
         * Resize de imagen
         */
        imagen_resize: {
            default: 0
        }
    },
    watch: {
        'info.visible'(value) {
            if (this.$refs.imagen) this.signaturePad.clear() /// estaba comentado
            if (value && this.subir_archivo) this.Consulta().archivo()
        },
        'info.tab'() {
            setTimeout(() => {
                if (this.$refs.imagen) {
                    var canvas = this.$refs.imagen;
                    this.signaturePad = new SignaturePad(canvas)
                } /// 4 lineas comentadas
            }, 100)
        },


    },
    methods: {
        /**
         * CONSULTA 
         */
        Consulta() {
            return {
                archivo: () => {
                    this.axios.post(this.api, {
                        tipo: 'CONSULTA',
                        ...this.api_parametros
                    })
                        .then(resp => {
                            this.archivos = resp.data.json
                            resp.data.json.map(objeto => {
                                if (objeto.src !== '') {
                                    this.imagen_src = objeto.src;
                                    this.$emit('metodo_emit', objeto)
                                } else {
                                    this.imagen_src = '';
                                }
                            });
                        })
                }
            }
        },

        /**
         * GUARDAR
         */

        Guardar() {
            return {
                archivo: (e) => {
                    const file = e.target.files[0]
                    const canvas = this.$refs.png;
                    const ctx = canvas.getContext('2d');
                    var fr = new FileReader();
                    fr.onload = (event) => {
                        var img = new Image();
                        img.onload = () => {
                            const ratio = (this.imagen_resize > 0) ? Math.min(this.imagen_resize / img.width, this.imagen_resize / img.height) : 1;
                            let Imgwidth = img.width * ratio
                            let Imgheight = img.height * ratio

                            canvas.width = Imgwidth;
                            canvas.height = Imgheight;
                            ctx.drawImage(img, 0, 0, Imgwidth, Imgheight);

                            const archivo = canvas.toDataURL("image/png").replace("image/png", "archivo");

                            this.axios.post(this.api, {
                                    tipo: 'GUARDAR',
                                    ...this.api_parametros,
                                    archivo
                                })
                                .then(() => {
                                    this.Consulta().archivo()
                                })
                        }
                        img.src = event.target.result;
                    }
                    fr.readAsDataURL(file);
                },
                eliminar_Imagen: () => {
                    const archivo = '';
                    this.axios.post(this.api, {
                            tipo: 'ELIMINAR_IMAGEN',
                            ...this.api_parametros,
                            archivo

                        })
                        .then(() => {
                            this.Consulta().archivo()
                            this.$emit('Borrar_Imagen')
                        })
                }
            }
        },
        Otros() {
            return {
                visible: (visible) => {
                    this.info.visible = visible
                },
                b64toBlob: (b64Data, contentType = '', sliceSize = 512) => {
                    const byteCharacters = atob(b64Data);
                    const byteArrays = [];

                    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
                        const slice = byteCharacters.slice(offset, offset + sliceSize);

                        const byteNumbers = new Array(slice.length);
                        for (let i = 0; i < slice.length; i++) {
                            byteNumbers[i] = slice.charCodeAt(i);
                        }

                        const byteArray = new Uint8Array(byteNumbers);
                        byteArrays.push(byteArray);
                    }

                    const blob = new Blob(byteArrays, {
                        type: contentType
                    });
                    return blob;
                },

            }
        }
    },

    /// todo el mounted estaba comentado
    mounted() {
        if (this.api == null) alert('Es necesario especificar el api en imgwn')

        if (this.$refs.imagen) {
            var canvas = this.$refs.imagen;
            this.signaturePad = new SignaturePad(canvas)
        }

        //this.imagenGuardada = localStorage.getItem('imagenGuardada');
    }
}
</script>
        
<style scoped>
.detalle {
    border: 2px dashed #ccc;
    width: 100%;
    min-height: 150px;
}

.firma {
    border: 2px dashed #ccc;
    margin: auto;
    /* width: 100%; */
}

.imagen img {
    border: 1px solid #ccc;
    width: 100%;
    max-height: 150px;
    border-radius: 5px;
    box-shadow: 0 0 0 2px #aaa;
}
</style>
        