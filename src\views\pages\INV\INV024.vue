<template>
    <vx-card title="Aceptación Devolución Movimientos Bodega">
    
        <div class="content content-pagex">
            <vs-divider></vs-divider>
            <br>
            <vs-row class="w-full">    
                <div class="sm:w-full md:w-7/12 lg:w-6/12 xl:w-5/12 pr-4">                    
                    <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                        <label class="typo__label">Bodega Destino:</label>      
                        <strong>{{ cb_bodegas.CODIGO }}</strong>
                        <multiselect
                            v-model="cb_bodegas"
                            :options="Lista_bodegas_destino"
                            :searchable="true"
                            :close-on-select="true"
                            :show-labels="false"
                            :allow-empty="false"
                            :custom-label="Bodegas_seleccionado"
                            placeholder="Seleccionar bodega"
                            @input="onChangeBodega"
                            :danger="errors.length > 0"
                            :danger-text="errors.length > 0 ? errors[0] : null">
                            <span
                            slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                    </ValidationProvider>                                                
                </div>
                <div class="sm:w-full md:w-4/12 lg:w-3/12 xl:w-2/12 pr-4">
                    <label class="typo__label">Estado</label>
                    <multiselect v-model="cb_lista_operacion" :options="lista_estado" :allow-empty="false" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Operacion_seleccionada" placeholder="Seleccionar" @input="onChangeEstado">
                        <span slot="noOptions">Lista no disponible.</span>
                    </multiselect>
                </div>   
            </vs-row>
            <vs-divider></vs-divider>
            <vs-row class="w-full"> 
                <div class="sm:w-full md:w-7/12 lg:w-6/12 xl:w-5/12 pr-4">                    
                    <label class="typo__label">Bodega Fuente:</label>                              
                    <multiselect
                        v-model="cb_bodegas_fuente_filtro"
                        :options="Lista_bodegas_fuente_filtro"
                        :searchable="true"
                        :close-on-select="true"
                        :show-labels="true"
                        :allow-empty="true"
                        :custom-label="Bodegas_seleccionado"
                        placeholder="Seleccionar bodega"
                        deselect-label="Quitar selección"
                        select-label="Seleccionar"
                        @input="Consultar_OrdenEnc()">
                        <span slot="noOptions">Lista no disponible.</span>
                    </multiselect>                                         
                </div>
                <div class="pr-4">
                    <label class="typo__label">Fecha Inicio:</label>
                    <vs-input type="date" v-model="fecha_inicio" name="date1" />
                </div>
    
                <div class="pr-4">
                    <label class="typo__label">Fecha Final:</label>
                    <vs-input type="date" v-model="fecha_final" name="date1" />
                </div>
    
                <vs-button style="float:left;margin: 18px 0px" color="primary" type="filled" icon-pack="fas" icon="fa-search" @click="Consultar_OrdenEnc()"> Búsqueda</vs-button>
    
            </vs-row>
            <vs-divider></vs-divider>
    
            <vs-table2 max-items="10" pagination :data="Lista_vista" search id="tb_lista_solicitud">
    
                <template slot="thead">
                    <th width="130px">Nº. Movimiento</th>
                    <th width="130px">Nº. Solicitud</th>
                    <th>Bodega Fuente</th>
                    <th>Fecha solicitud</th>
                    <th>Observaciones</th>
                    <th></th>
                    <!--<th style="text-align: center;">Recepcionada</th>-->
    
                </template>
    
                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">
    
                        <vs-td2 width='5%'>
                            {{tr.IDMOVIMIENTOFK}}
                        </vs-td2>
                        <vs-td2 width='5%'>
                            {{tr.IDREQUISICIONENC}}
                        </vs-td2>
    
                        <vs-td2 width='20%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                {{tr.IdBodegaFuentefk +' - ' +tr.NOMBRE_BODEGA_FUENTE}}
                            </div>
                        </vs-td2>
    
                        <vs-td2 width='20%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                <label>Enviado Devolución: {{tr.FECHAFINALIZADO}}</label>
                                <br v-if="tr.FECHAFINALIZADO">
                                <label v-if="tr.FECHAENTREGADA">Aceptado Devolución: {{tr.FECHAENTREGADA}}</label>    
                            </div>
                        </vs-td2>
                        <vs-td2  width='25%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                {{ tr.OBSERVACIONES }}
                            </div>
                        </vs-td2>  
                        <vs-td2 v-if="Id_estado_seleccionado == 'E'" align="right" width='10%' style="white-space:nowrap;min-width: 200px;">
                            <vx-tooltip text="Consultar" style="display:inline-block;margin-right:10px">
                                <vs-button color="primary" icon-pack="fas" icon="fa-arrow-right" style="display:inline-block;margin-right:2px" @click="Emergente_Detalle(data[indextr], 'E')"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip text="Excel" style="display:inline-block;margin-right:10px">
                                <vs-button icon-pack="far" icon="fa-file-excel" @click="Consultar_Movimiento(data[indextr],'EXCEL')" color="success" style="display:inline-block;margin-right:2px"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip text="Imprimir" style="display:inline-block;margin-right:10px">
                                <vs-button color="primary" icon-pack="feather" icon="icon-printer" style="display:inline-block;margin-right:2px" @click="Consultar_Movimiento(data[indextr])"></vs-button>
                            </vx-tooltip>
                        </vs-td2>
                        <vs-td2 v-else  align="right" width='10%' style="white-space:nowrap;min-width: 200px;">                            
                            <vx-tooltip text="Consultar" style="display:inline-block;margin-right:10px">
                                <vs-button color="#B2BABB" icon-pack="fas" icon="fa-arrow-right" style="display:inline-block;margin-right:2px" @click="Emergente_Detalle(data[indextr], 'C')"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip text="Excel" style="display:inline-block;margin-right:10px">
                                <vs-button icon-pack="far" icon="fa-file-excel" @click="Consultar_Movimiento(data[indextr],'EXCEL')" color="success" style="display:inline-block;margin-right:2px"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip text="Imprimir" style="display:inline-block;margin-right:10px">
                                <vs-button color="primary" icon-pack="feather" icon="icon-printer" style="display:inline-block;margin-right:2px" @click="Consultar_Movimiento(data[indextr])"></vs-button>
                            </vx-tooltip>
                        </vs-td2>        
                    </tr>
                </template>
            </vs-table2>
    
            <!----------------------- DESPACHO REQUISICION ---------->
            <vs-popup class="inventario-popup" classContent="popup-example" :title="Descripcion_Emergente" :active.sync="Estado_Emergente">
                <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px"> 
                    <div style="border:2px solid #1B5E20;border-radius:5px;padding:5px;font-size:12px;background-color: #C8E6C9">
                        Sucursal Origen: <b style="font-size:1vw">{{sesion.sesion_sucursal_nombre}}</b>
                    </div>
                    <br>
                    <vs-row class="w-full flex">
                        <div class="sm:w-6/12 md:w-5/12 lg:w-4/12 xl:w-3/12">
                            <b> Movimiento No. </b>&nbsp;
                            <b style="font-size:2vw">{{Requisicion_seleccionada.IDMOVIMIENTOFK}}</b>
                        </div>   
                        <div class="sm:w-6/12 md:w-5/12 lg:w-4/12 xl:w-3/12" style="align-self:right; direction: rtl;">
                            <b> Solicitud No. </b>&nbsp;
                            <b style="font-size:2vw">{{Requisicion_seleccionada.IDREQUISICIONENC}}</b>
                        </div>
                    </vs-row> 
                    <div class="w-1/2 md:w-1/2 lg:w-6/12 xl:w-6/12">
                         <b> <small>Fecha Solictud: </small></b>
                        <b>{{Requisicion_seleccionada.FECHAFINALIZADO}}</b>
                    </div>
                    <div class="w-1/2 md:w-1/2 lg:w-6/12 xl:w-6/12" v-if="Requisicion_seleccionada.FECHAENTREGADA">
                         <b> <small>Fecha Despacho: </small></b>
                        <b>{{Requisicion_seleccionada.FECHAENTREGADA}}</b>
                    </div>
                    <br>
                    <div>
                        <label class="typo__label">Bodega Fuente:</label>     
                        <strong>{{ cod_bodegaFuente.CODIGO }}</strong>
                        <multiselect v-model="cod_bodegaFuente" :options="Lista_bodegas_fuente" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Bodegas_seleccionado" placeholder="Seleccionar bodega" :disabled="Deshabilitar_campos">
                            <span slot="noOptions">Lista no disponible.</span>
                        </multiselect>
                    </div>
                    <div>
                        <br>                                          
                        <label class="typo__label">Bodega Destino:</label>
                        <strong>{{ cb_bodegas.CODIGO }}</strong>
                        <multiselect v-model="cb_bodegas" :options="Lista_bodegas_destino" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Bodegas_seleccionado"  placeholder="Seleccionar bodega" :disabled="Deshabilitar_campos">
                            <span slot="noOptions">Lista no disponible.</span>
                        </multiselect>
                    </div>
                    <br>
                    <!--- Producto -->
                    <vs-divider>Detalle</vs-divider>
    
                    <!--- Mostrar detalle de la orden--->
                    <vs-table2 max-items="10" pagination :data="Lista_detalle" id="tb_lista_solicitud" v-if="Estado_Emergente">
    
                        <template slot="thead">
                            <th>Linea</th>
                            <th>Codigo</th>
                            <th>Producto</th>
                            <th>Unidad Medida</th>
                            <th>Cant. Devuelta</th>                                                  
                            <th v-if="permiso_ver_costo_promedio">Costo promedio</th>
                            <th v-if="permiso_ver_costo_promedio">Costo promedio total</th>
                            <th>Cant. Aceptada</th>
                            <th></th>
                            <th></th>
                        </template>
    
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 width='5%'>
                                    {{ indextr+1 }}
                                </vs-td2>
                                <vs-td2 width='5%'>
                                    {{ tr.IDPRODUCTOFK }}
                                </vs-td2>
                                <vs-td2 width='40%'>
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                         
                                        {{tr.DESCRIPCION_PRODUCTO}}                
                                    </div>
                                </vs-td2>
                                <vs-td2 width='10%'>
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                                          
                                        {{tr.DESCRIPCION_UNIDAD}}
                                    </div>
                                </vs-td2>
                                <vs-td2 width='5%'>
                                    {{tr.CANTIDAD_PEDIDA}}    
                                </vs-td2>
                                <vs-td2 width="10%" v-if="permiso_ver_costo_promedio">
                                    <div style="min-width: fit-content;">
                                        {{ $formato_decimal(tr.COSTO_PROMEDIO) }}
                                    </div>
                                </vs-td2>
                                <vs-td2 width="10%" v-if="permiso_ver_costo_promedio" >
                                    <div style="min-width: fit-content;">
                                        {{ $formato_decimal(tr.COSTO_PROMEDIO_TOTAL) }}
                                    </div>
                                </vs-td2>
                                <vs-td2 width='5%' >
                                    <vs-input  type="number"  v-model="tr.CANTIDAD_RECIBIDA" class="w-full" :disabled="!tr.ESTADO_SWITCH || Consulta"
                                                    @input="VerificarExistencias(tr)"/>   
                                </vs-td2>
                                <vs-td2 width='5%' v-if="!Consulta">
                                    <vx-tooltip text="Despacho Manual" style="display:inline-block">
                                        <vs-switch style="margin-top:10px;margin-left:20px;" :key="tr.ESTADO_SWITCH" v-model="tr.ESTADO_SWITCH" />
                                    </vx-tooltip>                                    
                                </vs-td2>
                                <vs-td2 width='5%' v-if="!Consulta">                                    
                                    <vx-tooltip text="Eliminar" style="display:inline-block;">
                                        <vs-button color="danger"  icon-pack="fas" icon="fa-trash" @click="Eliminar_Registro(tr)"></vs-button>
                                    </vx-tooltip>
                                </vs-td2>
                            </tr>
                        </template>
                        <template slot="tfooter">
                            <tr v-if="permiso_ver_costo_promedio" class="foot">
                                <th colspan="6" scope="row" style="padding-right: 10px;">Total del Movimiento</th>
                                <td>{{totalCostos}}</td>
                            </tr>
                        </template>
                    </vs-table2>
                    <vs-divider></vs-divider>
                    <div class="flex flex-wrap">
                        <div class="w-full md:w-full lg:w-full xl:w-full">
                            <vs-textarea label="Observaciones" type="text" class="w-full" v-model="Observaciones" :disabled="Consulta"/>
                        </div>
                        <vs-divider></vs-divider>
                        <div class="w-full">
                            <b><small>Enviado devolución por: </small></b>&nbsp;
                            <b>{{Requisicion_seleccionada.IDCORPORATIVOSOLICITAFK +' - '+Requisicion_seleccionada.CORPORATIVO_SOLICITANTE}}</b>
                        </div> 
                    </div>    
                    <vs-divider></vs-divider>
                    <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" id="btn_confirmacion" @click="Finalizar_Requisicion()" v-if="!Consulta"> Finalizar Despacho</vs-button>
                    <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-printer" id="btn_movimiento" @click="Consultar_Movimiento(Requisicion_seleccionada)"> Imprimir </vs-button>                
                    <vs-button icon-pack="far" style="float:right;margin: 5px" icon="fa-file-excel" @click="Consultar_Movimiento(Requisicion_seleccionada,'EXCEL')" color="success">Excel</vs-button>
                    <vs-divider></vs-divider>
                </div>
            </vs-popup>
    
        </div>
    </vx-card>
    </template>
    
    <script>
    import Multiselect from 'vue-multiselect'
    import moment from "moment"
    import "vue-multiselect/dist/vue-multiselect.min.css"
    export default {
        components: {
            Multiselect
        },
        data() {
            return {
                Estado_VentanaEmergente_Busqueda: false,
                permiso_ver_costo_promedio: false,
                cb_lista_operacion: '',
                Descripcion_Emergente: '',
    
                producto_buscar: '',
                lista_estado: [{
                        ID: 'E',
                        DESCRIPCION: 'No Aceptado'
                    },
                    {
                        ID: 'R',
                        DESCRIPCION: 'Aceptado'
                    }
                ],
                Id_estado_seleccionado: 0,
                fecha_inicio: '',
                fecha_final: '',
                Lista_vista: [],
                Estado_Emergente: false,
    
                permisos_tipos_bodegas: [],
                permiso_bodega_vencido:'',
                Lista_bodegas_destino: [],
                Lista_bodegas_fuente: [],
                Lista_bodegas_fuente_filtro:[],

                id_bodega_seleccionada: '',
                cod_bodegaFuente: '',
    
                Producto_seleccionado: {
                    nombre: '',
                    marca_comercial: '',
                    Principio_activo: '',
                    Concentracion: '',
                    Presentacion: '',
                    Codigo: '',
                    codigo_interno: '',
                    Activo: 'N'
                },
                Id_OrdenRequisicion: 0,
                Cantida_solicitar: '',
    
                Lista_detalle: [],
                Deshabilitar_campos: false,
                Consulta:false,
                producto: [],
    
                Requisicion_seleccionada: {},
                Observaciones: '',
    
                user: '',
                message: 'prueba mensaje',
                messages: [],
                notificaciones: [],
                cb_bodegas:'',   
                cb_bodegas_fuente_filtro:'',              
                listado_reportes: [],
                listado_source: {
                    CODIGO_SOLICITUD: '',
                    CODIGO_MOVIMIENTO: '',
                    CODIGO_REQUERIMIENTO: '',
                }        
            }
        },
        async mounted() {
            this.cb_lista_operacion = {
                ID: 'E',
                DESCRIPCION: 'No Aceptado'
            }            
            this.Id_estado_seleccionado = 'E';
            let permisos_sub_bodegas = []
            for(let privilegio of this.$store.state.privilegios){
               if(privilegio.Privilegio.includes("TIPO_BODEGA")){                      
                   let tipobodega = privilegio.Privilegio.split('_')[2];
                   if(tipobodega == 'T'){
                     this.permiso_bodega_vencido = 'S'
                   }else{
                     this.permisos_tipos_bodegas.push(privilegio.Privilegio.split('_')[2])                    
                   }                                                       
                }else if(privilegio.Privilegio.includes("DEVOLUCION_SUB_BODEGA")){
                    permisos_sub_bodegas.push(privilegio.Privilegio.split('_')[3])
                }
            }      
            await this.Consultar_Bodega('F',this.permisos_tipos_bodegas.join(","),this.permiso_bodega_vencido);
            await this.Consultar_Bodega('H',this.permisos_tipos_bodegas.join(","),this.permiso_bodega_vencido);
            await this.Consultar_Bodega('P',this.permisos_tipos_bodegas.join(","),this.permiso_bodega_vencido);
            await this.ConsultarSubBodega('G',permisos_sub_bodegas.join(","));
            this.permiso_ver_costo_promedio = this.$validar_privilegio('VER_COSTO_PROMEDIO').status
        },
        computed: {
            totalCostos() {
                return this.Lista_detalle.reduce((acumulador, movimiento) => 
                                acumulador + parseFloat(movimiento.COSTO_PROMEDIO_TOTAL)  , 0)
                           .toLocaleString("es-GT", {
                                                style: "currency",
                                                currency: "GTQ",
                                                maximumFractionDigits:2
                                            })
    
            },
            sesion() {
                return this.$store.state.sesion
            }
        },        
        async beforeCreate(){
            this.listado_reportes = await this.$recupera_parametros_reporte('Movimiento Requerimiento')                    
        },
        methods: {
            async Consultar_Movimiento(datos,formato='PDF') {
                this.listado_source.CODIGO_MOVIMIENTO = datos.IDMOVIMIENTOFK
                this.listado_source.CODIGO_SOLICITUD = datos.IDREQUISICIONENC
                this.listado_source.VerCostoPromedio = this.permiso_ver_costo_promedio? 'S':'N'
                this.$reporte_modal({
                    Nombre: "Movimiento Requerimiento",
                    Opciones: this.listado_source,
                    Formato: formato
                }).catch(() => {
                })
            },
            GetDateValue(value) {
                if (value === undefined) {
                    return '';
                } else if (value === null) {
                    return '';
                } else if (value == '') {
                    return '';
                }
                return moment(value).format('DD/MM/YYYY');          
            },
            Eliminar_Registro(value) {
                /**
                 * @General
                 * Función eliminar registro;
                 */
    
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',
                    acceptText: 'Aceptar',
                    cancelText: 'Cancelar',
                    text: '¿Dar de baja producto  '+value.IDPRODUCTOFK +' - '+ value.DESCRIPCION_PRODUCTO + ' ' + value.DESCRIPCION_UNIDAD +' ? ',
                    accept: () => {
    
                        this.axios.post('/app/v1_OrdenCompra/Requisicion_Det', {
                                Requisicion_det: value.IDREQUISICIONDET,
                                Requisicion_enc: this.Id_OrdenRequisicion,
                                Producto: '0',
                                cant_pedida: 0,
                                cant_entregada: 0,
                                cant_recibida: 0,
                                Operacion: 'B'
                            })
                            .then(resp => {
                                if (resp.data.codigo == 0) {
                                    let indice = this.Lista_detalle.findIndex(d=>d.IDREQUISICIONDET == value.IDREQUISICIONDET)
                                    this.Lista_detalle.splice(indice,1);                                       
                                }
                            })
    
                    }
                })
    
            },
            VerificarExistencias(producto){     
                
                
                if(Number(producto.CANTIDAD_RECIBIDA) < 0){
                    this.$vs.notify({
                            position:'top-center',
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'No puede ingresar cantidades negativas',
                        });
                    producto.CANTIDAD_RECIBIDA = producto.CANTIDAD_PEDIDA
                }

                if(Number(producto.CANTIDAD_RECIBIDA) > Number(producto.CANTIDAD_PEDIDA)){
                    this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'No puede aceptar mas unidades que la cantidad a devolver',
                        });
                    producto.CANTIDAD_RECIBIDA = producto.CANTIDAD_PEDIDA
                }
            },
            Operacion_seleccionada({
                DESCRIPCION
            }) {
                return `${DESCRIPCION} `
            },
            onChangeEstado(value) {
                if (value !== null && value.length !== 0) {
                    this.Id_estado_seleccionado = value.ID;
                    this.Consultar_OrdenEnc();
                } else {
                    this.Id_estado_seleccionado = '';
                }
            },
            Bodegas_seleccionado({
                CODIGO,NOMBRE
            }) {
                return `${CODIGO} - ${NOMBRE} `;
            },
            onChangeBodega(value) {
                if(!value){
                    this.cb_bodegas = ''
                    return
                }
                if (value !== null && value.length !== 0) {
                    this.id_bodega_seleccionada = value.CODIGO;
                    this.cb_bodegas_fuente_filtro = '';
                    this.Consultar_OrdenEnc();
                } else {
                    this.id_bodega_seleccionada = '';
                }
            },
            Emergente_Detalle(Datos, Operacion) {
                
                this.Estado_Emergente = true;
                this.cod_bodegaFuente = '';
    
                this.producto_buscar = '';
                this.Cantida_solicitar = '';
                
                this.Requisicion_seleccionada = Datos;                    
                this.Id_OrdenRequisicion = Datos.IDREQUISICIONENC;
                this.id_bodega_seleccionada = Datos.IdBodegaFuentefk;
                this.Observaciones = Datos.OBSERVACIONES;

                setTimeout(() => {
                    this.cod_bodegaFuente = {
                        NOMBRE: Datos.NOMBRE_BODEGA_FUENTE,
                        CODIGO: Datos.IdBodegaFuentefk
                    }
                }, 500);                    
                this.Consultar_OrdenDet(Operacion)

                if (Operacion == 'C') {
                    this.Deshabilitar_campos = true;
                    this.Consulta = true;
                    this.Descripcion_Emergente = 'Consultar Devolución Finalizada';                  
                } else {               
                    this.Deshabilitar_campos = true;
                    this.Consulta = false;
                    this.Descripcion_Emergente = 'Aceptación Devolución';
                }
            },
    
            Validacion_Campos(Tipo, Nombre, valor, obligatorio, cant_maxima) {
                /**
                 * @General
                 * Tipo : N = Númerio, Nombre = Descripción del campo, Valor = A evaluar, Obligatorio= Verdadero o falso,cant_maxima  = Aplica unicamente para un string y define la cantidad de caracteres 
                 * Devuelve un mensaje al usuario si las validaciones no se cumple y aborta, de lo contrario devuelve verdadero
                 */
                if (Tipo == 'ID') {
                    if (valor <= 0) {
                        this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else if (Tipo == 'N') {
                    
                    if ( valor <0 || valor == "") {
                        this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else {
                    if (valor == '' && obligatorio == true) {
                        this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
    
                        if (cant_maxima < valor.length) {
                            this.$vs.notify({
                        position:'top-center',
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'Campo ' + Nombre + ' no debe superar los ' + cant_maxima + ' carácteres.',
                            });
                            return false;
                        } else {
                            return true;
                        }
    
                    }
                }
            },
            /*****************************CONSULTAR INFORMACION*************/
            Consultar_OrdenEnc() {

                if(!this.cb_bodegas){
                    this.$vs.notify({
                        position:'top-center',
                                color: 'danger',
                                title: 'Inventario',
                                text: 'Seleccione una bodega destino',
                            })
                    return
                }
    
                let Operacion = 'E'

                this.axios.post('/app/v1_OrdenCompra/ConsultaRequisicion_Enc', {
                        Estado: this.Id_estado_seleccionado,
                        fecha_inicio: this.GetDateValue(this.fecha_inicio),
                        fecha_final: this.GetDateValue(this.fecha_final),
                        Operacion: Operacion,
                        Bodega_destino: this.cb_bodegas.CODIGO,
                        Bodega_fuente: this.cb_bodegas_fuente_filtro?.CODIGO??null,
                        TipoMovimiento: '6,13'
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                        position:'top-center',
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_vista = [];
                        } else {
                            this.$vs.notify({
                                                position:'top-center',
                                                color: 'success',
                                                title: 'Inventario',
                                                text: 'Se actualizaron los movimientos exitosamente',
                                            })
                            this.Lista_vista = resp.data.json;
    
                        }
                    })
                    .catch(() => {})
            },
            Consultar_OrdenDet(Operacion) {
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/ConsultaRequisicion_Det', {
                        Requisicion_enc: this.Id_OrdenRequisicion,
                        tipo: 'D'
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                        position:'top-center',
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_detalle = [];
                        } else {
                            if(Operacion == 'C'){
                                this.Lista_detalle = resp.data.json;
                            }
                            else{
                                this.Lista_detalle = resp.data.json.map(function(producto){ 
                                                                                        producto.CANTIDAD_RECIBIDA = producto.CANTIDAD_PEDIDA
                                                                                        return producto;
                                                                                      });
                            }
                            
                            this.Lista_detalle = resp.data.json.map(x => {
                                x.COSTO_PROMEDIO = Number(x.COSTO_PROMEDIO)
                                x.COSTO_PROMEDIO_TOTAL = Number(x.COSTO_PROMEDIO) * (['P','E'].includes(this.Id_estado_seleccionado)? Number(x.CANTIDAD_PEDIDA): Number(x.CANTIDAD_ENTREGA))
                                return x
                            })
                        }
                    })
                    .catch(() => {
                        this.Lista_detalle = [];
                    })
            },
            async Consultar_Bodega(Operacion, BodegasDespacho, BodegaTransito) {
                const url = this.$store.state.global.url
                await this.axios.post(url+'app/v1_OrdenCompra/consulta_bodega', {
                                                                        operacion: Operacion,
                                                                        BodegasDepacho: BodegasDespacho,
                                                                        BodegaTransito: BodegaTransito
                                                                     })
                           .then(resp => {
            
                                if (resp.data.codigo != 0) {
                                    this.$vs.notify({
                        position:'top-center',
                                        color: '#B71C1C',
                                        title: 'Bodegas',
                                        text: resp.data.mensaje,
                                    })
                                } else {
                                    if(Operacion == 'F'){ this.Lista_bodegas_destino = resp.data.json; }                                                                            
                                    else if(Operacion == 'H'){ this.Lista_bodegas_fuente = resp.data.json; }  
                                    else if(Operacion == 'P'){ this.Lista_bodegas_fuente_filtro = resp.data.json; }
                                }
                            })
                           .catch(() => { })
        
            },  
            async ConsultarSubBodega(Operacion,TipoBodegas) {
                if(!TipoBodegas || TipoBodegas.length == 0 ){return}

                const url = this.$store.state.global.url
                await this.axios.post(url+'app/v1_OrdenCompra/consulta_bodega', {
                                                                        operacion: Operacion,
                                                                        lista_tipo_bodegas: TipoBodegas
                                                                        })
                            .then(resp => {
            
                                if (resp.data.codigo != 0) {
                                    this.$vs.notify({
                                        position:'top-center',
                                        color: '#B71C1C',
                                        title: 'Bodegas',
                                        text: resp.data.mensaje,
                                    })
                                } else {
                                    this.Lista_bodegas_destino =
                                        this.Lista_bodegas_destino.concat(resp.data.json.filter(bodega=>bodega.BodegaFuente == 'S')) 

                                    this.Lista_bodegas_fuente =
                                        this.Lista_bodegas_fuente.concat(resp.data.json.filter(bodega=>bodega.BodegaFuente == 'N'))                  
                                
                                    this.Lista_bodegas_fuente.sort((a,b)=>Number(a.CODIGO)>=Number(b.CODIGO)?1:-1)
                                    this.Lista_bodegas_destino.sort((a,b)=>Number(a.CODIGO)>=Number(b.CODIGO)?1:-1)
                               
                                
                                }
                            })
                            .catch(() => { })
        
            },         
            Finalizar_Requisicion() {                
                for (var i = 0; i < this.Lista_detalle.length; i++) {
                    if(this.Lista_detalle[i].CANTIDAD_RECIBIDA < 0){
                        this.$vs.notify({
                        position:'top-center',
                                color: '#B71C1C',
                                title: 'Orden de devolución',
                                text: 'La cantidad a recibir debe ser mayor a 0, para el producto en la linea ' + (i+1),
                                time: 2000
                            });
                        return false;
                    }

                    if(Number(this.Lista_detalle[i].CANTIDAD_RECIBIDA) > Number(this.Lista_detalle[i].CANTIDAD_PEDIDA) ){
                        this.$vs.notify({
                        position:'top-center',
                                color: '#B71C1C',
                                title: 'Orden de devolución',
                                text: 'No puede recibir mas unidades que las devueltas en la linea ' + (i+1),
                                time: 2000
                            });
                        return false;                    
                    }
                        
                }
    
    
                
                    
                    this.axios.post('/app/v1_OrdenCompra/Requisicion_Enc', {
                            Requisicion_enc: this.Requisicion_seleccionada.IDREQUISICIONENC,
                            Empresa: this.sesion.sesion_empresa,
                            Productos: this.Lista_detalle.map((producto)=>producto.IDPRODUCTOFK).toString(),
                            CantidadesDespacho: this.Lista_detalle.map((producto)=>producto.CANTIDAD_RECIBIDA).toString(),
                            NumerosRequisicionDet: this.Lista_detalle.map((producto)=>producto.IDREQUISICIONDET).toString(),                            
                            Total: 0,
                            Cant_lineas: 0,
                            Observaciones:  this.Observaciones,
                            Bodega_solicitante: 0,
                            Bodega_destino: 0,
                            Corporativo_solicitante: 0,
                            Corporativo_finaliza: this.sesion.corporativo,
                            Estado: 'R',
                            Operacion: 'D',
                        })
                        .then(resp => {
    
                            if (resp.data.codigo == 0) {
                                this.Estado_Emergente = false;
                                this.Consultar_OrdenEnc();
                            }else if (resp.data.codigo > 0) {
                                this.$vs.notify({
                                    position:'top-center',
                                    color: '#B71C1C',
                                    title: 'Orden de devolución',
                                    text: resp.data.mensaje,
                                    time: 4000
                                });
                                return false
                            }
                        })
                    
                
            }
        }
    
    }
    </script>    
<style>
    .inventario-popup.con-vs-popup .vs-popup {
        width: 90% !important;
    }

    tfoot .foot {
    border-top: 3px dotted rgb(160 160 160);
    background-color: #2c5e77;
    color: #fff;
    }
    
</style>
    
<style scoped>
    tfoot th {
    text-align: right;
    }
    
    tfoot td {  
    font-weight: bold;
    }
</style>