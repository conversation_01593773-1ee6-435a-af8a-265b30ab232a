<template>
<vs-row class="p-2 w-full">

    <vs-col vs-justify="center" vs-align="center" vs-w="10">
        <vx-card>
            <DxDataGrid :ref="dataGridRefKey" v-bind="DefaultDxGridConfiguration" :headerFilter="{visible:false,allowSearch:false}" :data-source="signosDataSource" :height="'100%'" :no-data-text="'Seleccione una fecha para cargar información'" @editor-preparing="editorHorarioValue">
                <DxSelection mode="single" />
                <DxToolbar>
                    <DxItem name="groupPanel" />
                    <DxItem name="searchPanel" />
                    <DxItem location="after" template="opcionesSignos" />
                </DxToolbar>

                <DxGroupPanel :visible="true" />
                <DxGrouping :auto-expand-all="false" />

                <DxEditing :allow-updating="false" :allow-adding="true" :allow-deleting="false" mode="popup">
                    <DxPopup v-bind="popupConf" :height="450" title="Registro de signos vitales">
                        <DxToolbarItem widget="dxButton" toolbar="bottom" :options="{
                                        text: 'Guardar',
                                        type: 'success',
                                        useSubmitBehavior: true,
                                        onClick: submit
                                    }" location="after">
                        </DxToolbarItem>
                        <DxToolbarItem widget="dxButton" toolbar="bottom" :options="{
                                        text: 'Cancelar',
                                        type: 'danger',
                                        useSubmitBehavior: true,
                                        onClick: ()=>{this.dataGrid.cancelEditData()}
                                    }" location="after">
                        </DxToolbarItem>
                    </DxPopup>

                    <DxForm labelMode="floating" :ref="dataSignosFormRefKey">
                        <DxItem item-type="group" :col-span="2" :col-count="1">
                            <DxItem item-type="group" :col-span="1" class="col-sm-4" :col-count="4">
                                <DxItem data-field="Horario" editor-type="dxSelectBox" :col-span="2" :editor-options="horarios" :is-required="true">
                                    <DxLabel :text="'Horario'" />
                                </DxItem>
                                <DxItem data-field="FechaSignos" editor-type="dxDateBox" :use-mask-behavior="true" :col-span="2" :is-required="true" :editor-options="dateBoxOptions">
                                    <DxLabel :text="'Fecha toma de signos'" />
                                </DxItem>
                                <DxItem data-field="PA" editor-type="dxTextBox" :editor-options="{mask: '#00/00#'}">
                                    <DxLabel :text="'Presión arterial'" />
                                    <DxPatternRule pattern="^(\s|\d)*\d{4}(\s|\d)*$" />
                                </DxItem>
                                <DxItem data-field="FC" editor-type="dxNumberBox" :editor-options="{min: 0, max: 300, format: '#', step: 0 }">
                                    <DxLabel :text="'Frecuencia cardiaca'" />
                                </DxItem>
                                <DxItem data-field="FR" editor-type="dxNumberBox" :editor-options="{min: 0, max: 100, format: '#', step: 0 }">
                                    <DxLabel :text="'Frecuencia respiratoria'" />
                                </DxItem>
                                <DxItem data-field="Temp" editor-type="dxNumberBox" :editor-options="reglasTemp">
                                    <DxLabel :text="'Temperatura'" />
                                </DxItem>
                                <DxItem data-field="Sat" editor-type="dxNumberBox" :editor-options="reglasSat">
                                    <DxLabel :text="'Saturación'" />
                                </DxItem>
                                <DxItem data-field="PVC" editor-type="dxNumberBox" :editor-options="{min: 0, max: 300, format: '#', step: 0 }">
                                    <DxLabel :text="'Presión venosa central'" />
                                </DxItem>
                                <DxItem data-field="GMT" editor-type="dxNumberBox" :editor-options="{min: 0, max: 300, format: '#', step: 0 }">
                                    <DxLabel :text="'Glucometría'" />
                                </DxItem>
                            </DxItem>
                            <DxItem item-type="group" :col-span="1">
                                <DxItem data-field="Otros" editor-type="dxTextArea" :editor-options="{height: 100}">
                                    <DxLabel :text="'Comentarios'" />
                                </DxItem>
                            </DxItem>
                        </DxItem>
                    </DxForm>
                </DxEditing>

                <DxColumn :width="70" data-field="Horario" alignment="center" />
                <DxColumn :width="110" data-field="FechaSignos" caption="Fecha signos" format="dd/MM/yyyy" data-type="date" alignment="center" />
                <DxColumn :width="70" data-field="PA" caption="P/A" alignment="center" />
                <DxColumn :width="70" data-field="PM" caption="PM" alignment="center" />
                <DxColumn :width="70" data-field="FC" caption="FC" alignment="center" />
                <DxColumn :width="70" data-field="FR" caption="FR" alignment="center" />
                <DxColumn :width="70" data-field="Temp" caption="Temp.°" alignment="center" />
                <DxColumn :width="70" data-field="Sat" caption="Sat.%" alignment="center" />
                <DxColumn :width="70" data-field="PVC" caption="PVC" alignment="center" />
                <DxColumn :width="70" data-field="GMT" caption="GMT" alignment="center" />
                <DxColumn :width="300" data-field="Otros" caption="Comentario" alignment="center" :encode-html="false" />
                <DxColumn :width="300" data-field="Usuario" caption="Usuario" alignment="center" />
                <DxColumn :width="150" data-field="Puesto" alignment="center" />
                <DxColumn :width="150" data-field="FechaIngreso" caption="Fecha ingreso" format="dd/MM/yyyy HH:mm:ss" data-type="date" alignment="center" />

                <template #opcionesSignos>
                    <ExpedienteGridToolBar v-bind="$props" :visible="ShouldRender" :pdfExportItems="[{text:'Signos vitales', reportName: 'Signos Vitales'}]" @add="addRow" @refresh="Cargar(true)" :report-param="$props">
                        <DxButton id="botonBarra" icon="fas fa-history" hint="Ingresar peso y estatura" @click="sendEmitterWeight">
                            <font-awesome-icon :icon="['fas', 'weight-scale']" style="font-size: 18px;" />
                        </DxButton>
                    </ExpedienteGridToolBar>
                </template>
            </DxDataGrid>
        </vx-card>
    </vs-col>
    <vs-col vs-w="2" vs-justify="center" vs-align="center" class="pl-2">
        <vx-card title="Filtrar fecha">
            <DxDataGrid :ref="dataGridFechaKey" v-bind="DefaultDxGridConfiguration" :searchPanel="{visible: false}" :data-source="fechas" key-expr="id" :height="'100%'" @selection-changed="onSelectionChanged">
                <DxSelection mode="single" />
                <DxColumn :width="100" data-field="fecha" caption="Fecha" format="dd/MM/yyyy" data-type="date" alignment="center" />
            </DxDataGrid>
        </vx-card>
    </vs-col>
</vs-row>
</template>

<script>
import {
    DxDataGrid,
    DxColumn,
    DxEditing,
    DxForm,
    DxToolbar,
    DxLabel,
    DxPopup,
    DxSelection,
    DxPatternRule,
    DxGrouping,
    DxGroupPanel,
} from 'devextreme-vue/data-grid'
import CustomStore from 'devextreme/data/custom_store'
import ExpedienteGridToolBar from './ExpedienteGridToolBar.vue'
import DxButton from 'devextreme-vue/button'
import {
    DxItem
} from 'devextreme-vue/form'
import 'devextreme-vue/lookup'
import {
    DxToolbarItem
} from 'devextreme-vue/popup'
import {
    DefaultDxGridConfiguration,
    popupConf
} from './data'
import 'devextreme-vue/text-area'

const dataGridRefKey = 'gridSignos'
const dataGridFechaKey = 'gridFechaKey'
const dataSignosFormRefKey = 'my-ant-form'

export default {
    name: 'SignosVitales',
    components: {
        DxDataGrid,
        DxColumn,
        ExpedienteGridToolBar,
        DxEditing,
        DxForm,
        DxItem,
        DxToolbar,
        DxPopup,
        DxLabel,
        DxToolbarItem,
        DxButton,
        DxSelection,
        DxPatternRule,
        DxGrouping,
        DxGroupPanel,
    },
    data() {
        return {
            //Listado de signos asociados a la admisión
            signos: [],
            signosDataSource: new CustomStore({
                key: "id",
                load: () => {
                    return this.signos;
                },
                insert: (values) => {
                    this.grabarSignos(values);
                },
            }),
            fechas: [], //Listado de fechas de toma de signos
            dataGridRefKey,
            dataGridFechaKey,
            dataSignosFormRefKey,
            DefaultDxGridConfiguration,
            //Reglas para el campo de Saturación
            reglasSat: {
                min: 0,
                max: 100,
                format: "000'%'",
                step: 0
            },
            //Reglas para el campo de Temperatura
            reglasTemp: {
                min: 0,
                max: 50,
                format: "00.0'°'",
                step: 0
            },
            popupConf,
        }
    },
    props: {
        SerieAdmision: null,
        CodigoAdmision: null,
        NumeroExpediente: null,

        StatusAdmision: null,
        horarios: null,
        dateBoxOptions: null,
    },
    methods: {
        submit() {
            this.dataGrid.saveEditData()
            this.getFechasSignos()
        },
        Cargar(update, dia = 0, mes = 0, año = 0) {
            if (this.SerieAdmision && this.CodigoAdmision)
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaSignosGrid', {
                    Serie: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    dia: dia,
                    mes: mes,
                    año: año
                })
                .then(resp => {
                    this.signos = resp.data.json.map((x, index) => {
                        return {
                            id: index,
                            FechaSignos: x.Fecha_toma_Signos,
                            Horario: x.Horario,
                            PA: x.pa,
                            FC: x.fc,
                            FR: x.fr,
                            Temp: x.temp,
                            Sat: x.sat,
                            PVC: x.pvc,
                            GMT: x.gmt,
                            PM: x.presion_media,
                            Otros: this.$saltos_tab_to_html(this.$limpiar_saltos_tabulares(x.otros)),
                            FechaIngreso: x.fecha,
                            Usuario: x.Empleado,
                            Puesto: x.Puesto
                        }
                    })
                    this.signosDataSource.load().then(
                        () => {
                            if (update) {
                                this.getFechasSignos();
                                this.refreshFechaDataGrid();
                                this.refreshDataGrid();
                            } else {
                                this.refreshDataGrid();
                            }
                        },
                        () => {
                            //console.error('error loading data customstore' + JSON.stringify(error))
                        }
                    )
                })
            else {
                this.signos = [];
                this.refreshDataGrid();
                this.refreshFechaDataGrid();
            }
        },
        getFechasSignos() {
            if (this.SerieAdmision && this.CodigoAdmision)
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaFechaIngSolExc', {
                    Serie: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    TipoBusqueda: "Signos"
                })
                .then(resp => {
                    this.fechas = resp.data.json.map((x, index) => {
                        return {
                            id: index,
                            fecha: this.formatFechaFiltro(x.Fecha)
                        }
                    })
                })
            else {
                this.fechas = [];
                this.refreshFechaDataGrid();
            }
        },
        grabarSignos(values) {
            let postData = {
                SerieAdmision: this.SerieAdmision,
                Admision: this.CodigoAdmision,
                IdHorario: values.Horario,
                PA: this.getPresion(values.PA),
                FC: values.FC,
                FR: values.FR,
                Temp: values.Temp,
                Sat: values.Sat,
                PVC: values.PVC,
                Otros: this.$reemplazar_tabulares(values.Otros),
                fechasignos: this.formatFecha(values.FechaSignos),
                GMT: values.GMT
            }
            this.axios.post("/app/v1_ExpedienteEvolucion/InsertarSignosVitales", {
                ...postData
            }).then(() => {
                this.Cargar(true)
                this.$emit('onSaved', )
            })
        },
        refreshDataGrid() {
            this.dataGrid.refresh()
                .then(function () {

                })
                .catch(function () {
                    //console.error('error refreshing: ' + error)
                });
            this.dataGrid.clearSelection();
            this.dataGrid.clearSorting();
            this.dataGrid.pageIndex(0);
            this.dataGrid.clearGrouping()
        },
        refreshFechaDataGrid() {
            this.dataGridFecha.pageIndex(0);
            this.dataGridFecha.clearSelection();
            this.dataGridFecha.clearSorting();
            this.dataGridFecha.columnOption('Fecha', 'sortOrder', 'desc');
        },
        sendEmitterWeight() {
            this.$emit('onWeightButton', true);
        },
        addRow() {
            if (this.StatusAdmision != 'A')
                this.$vs.notify({
                    time: 4000,
                    title: 'Antecedentes',
                    text: 'La admisión no está activa, no se puede agregar nuevo registro',
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'warning',
                    position: 'bottom-center'
                })
            else
                this.dataGrid.addRow();
        },
        formatFecha(date) {
            const year = new Date(date).toLocaleString('default', {
                year: 'numeric'
            });
            const month = new Date(date).toLocaleString('default', {
                month: '2-digit'
            });
            const day = new Date(date).toLocaleString('default', {
                day: '2-digit'
            });

            return [year, month, day].join('');
        },
        formatFechaFiltro(fecha) {
            fecha = fecha.split('/');
            const year = fecha[2];
            const month = fecha[1];
            const day = fecha[0];

            return [month, day, year].join('/');
        },
        getPresion(presion) {
            if (presion !== '' && presion !== undefined) {
                presion = presion.replace(' ', '');
                if (presion.length === 6) {
                    presion = parseInt(presion.substring(0, 3)) + '/' + parseInt(presion.substring(3, 6));
                } else if (presion.length === 5) {
                    presion = parseInt(presion.substring(0, 3)) + '/' + parseInt(presion.substring(3, 5));
                } else if (presion.length === 4) {
                    presion = parseInt(presion.substring(0, 2)) + '/' + parseInt(presion.substring(2, 4));
                }
            }
            return presion;
        },
        editorHorarioValue(e) {
            if (e.parentType === 'dataRow' && e.dataField === 'Horario') {
                e.setValue(new Date().getHours());
                e.editorOptions.value = new Date().getHours();
            }
            if (e.parentType === 'dataRow' && e.dataField === 'FechaSignos') {
                e.setValue(new Date());
                e.editorOptions.value = new Date();
            }
        },
        filtrarFecha(fecha) {
            fecha = fecha.split('/');
            const year = fecha[2];
            const month = fecha[0];
            const day = fecha[1];
            this.Cargar(false, day, month, year);
        },
        //Método para detectar cuando se selecciona una fila de la tabla de filtrado de fecha
        onSelectionChanged(e) {
            if (e.selectedRowsData.length > 0) {
                const data = e.selectedRowsData[0].fecha;
                this.filtrarFecha(data);
            }
        },
    },
    mounted() {
        this.Cargar(true);
    },
    watch: {
        'NumeroExpediente'(newval) {
            if (newval != undefined)
                this.Cargar(true)
        },
    },
    computed: {
        ShouldRender() {
            return Boolean(this.SerieAdmision) && Boolean(this.CodigoAdmision) && Boolean(this.NumeroExpediente)
        },

        dataGrid: function () {
            return this.$refs[dataGridRefKey].instance;
        },

        dataGridFecha: function () {
            return this.$refs[dataGridFechaKey].instance;
        }
    },
}
</script>
