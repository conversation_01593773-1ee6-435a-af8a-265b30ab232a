<template>
    <vx-card style="display:none">
    
        <!--------------Inicio Vista Previa información _---------->
    
        <vs-popup classContent="popup-example" title="" :active.sync="Estado_VentanaComparativoDetalle">
            <form>
    
                <div class="terms">
                    <div class="titulo-disenio">
                        <label>COMPARATIVO COTIZACIONES</label>
                    </div>
                    <br><br>
                    <div class="espacio">
                    </div>
                    <div v-for="(field, index) in Campos_Encabezado" :key="field.Nombre">
                        <label style="font-family: 'Barmeno';font-weight: 200;font-size: 15px;" :for="field.Nombre">PROV. {{(index+1)}} {{field.Nombre}}</label>
                    </div>
                    <vs-divider></vs-divider>
                    <div v-if=" Campos_Encabezado.length ===1">
    
                        <vs-table2 max-items="10" pagination :data="ListaComparativoDetalle" tooltip id="tb_lista_comparativo_1">
    
                            <template slot="thead">
    
                                <th width="850px">Producto</th>
                                <th>Cant. Solicitada</th>
    
                               
                                <th> Prov. 1 Precio/Unitario</th>
                                <th> Seleccionar</th>
    
                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr1" v-for="(tr, indextr1) in data">
    
                                    <vs-td2 width="850px" :data="data[indextr1].PRODUCTO">
                                        {{data[indextr1].PRODUCTO}}
                                    </vs-td2>
    
                                    <vs-td2 :data="data[indextr1].SOLICITADO">
                                        {{parseInt(data[indextr1].SOLICITADO)}}
                                    </vs-td2>
                                   
                                    <vs-td2 :data="data[indextr1].PRECIO_UNITARIO_P1">
                                        {{data[indextr1].PRECIO_UNITARIO_P1}}
                                    </vs-td2>
                                    <vs-switch style="margin-top:10px;margin-left:20px" :disabled="data[indextr1].EDITABLE_P1" :key="data[indextr1].ID_DET_COMPARATIVO_P1" v-model="data[indextr1].SELECCIONADO_P1" @click="Comparativo_DetalleCambio( data[indextr1].ID_DET_COMPARATIVO_P1, data[indextr1].SELECCIONADO_P1 )" />
    
                                </tr>
                            </template>
                        </vs-table2>
                     
                    </div>
    
                    <div v-else-if=" Campos_Encabezado.length ===2">
    
                        <vs-table2 max-items="10" pagination :data="ListaComparativoDetalle" tooltip id="tb_lista_comparativo_2">
    
                            <template slot="thead">
                               
                                <th width="850px">Producto</th>
                                <th>Cant. Solicitada</th>
    
                                <th> Prov. 1 Precio/Unitario</th>
                                <th> Seleccionar</th>
    
                               
                                <th> Prov. 2 Precio/Unitario</th>
                                <th> Seleccionar</th>
    
                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr3" v-for="(tr, indextr3) in data">
                                   
    
                                    <vs-td2 width="850px" :data="data[indextr3].PRODUCTO">
                                        {{data[indextr3].PRODUCTO}}
                                    </vs-td2>
    
                                    <vs-td2 :data="data[indextr3].SOLICITADO">
                                        {{data[indextr3].SOLICITADO}}
                                    </vs-td2>
                             
                                    <vs-td2 :data="data[indextr3].PRECIO_UNITARIO_P1">
                                        {{data[indextr3].PRECIO_UNITARIO_P1}}
                                    </vs-td2>
                                    <vs-switch style="margin-top:10px;margin-left:20px" :disabled="data[indextr3].EDITABLE_P1" :key="data[indextr3].ID_DET_COMPARATIVO_P1" v-model="data[indextr3].SELECCIONADO_P1" @click="Comparativo_DetalleCambio( data[indextr3].ID_DET_COMPARATIVO_P1, data[indextr3].SELECCIONADO_P1 )" />
                                    
                                    <vs-td2 :data="data[indextr3].PRECIO_UNITARIO_P2">
                                        {{data[indextr3].PRECIO_UNITARIO_P2}}
                                    </vs-td2>
                                    <vs-switch style="margin-top:10px;margin-left:20px" :disabled="data[indextr3].EDITABLE_P2" :key="data[indextr3].ID_DET_COMPARATIVO_P2" v-model="data[indextr3].SELECCIONADO_P2" @click="Comparativo_DetalleCambio(   data[indextr3].ID_DET_COMPARATIVO_P2, data[indextr3].SELECCIONADO_P2 )" />
    
                                </tr>
                            </template>
                        </vs-table2>
                      
                    </div>
    
                    <div v-else-if=" Campos_Encabezado.length ===3">
    
                        <vs-table2 max-items="10" pagination :data="ListaComparativoDetalle" tooltip id="tb_lista_comparativo_3">
    
                            <template slot="thead">
                                <th width="850px">Producto -</th>
                                <th>Cant. Solicitada</th>
                                <th> Prov. 1 Precio/Unitario</th>
                                <th> Seleccionar</th>
                                <th> Prov. 2 Precio/Unitario</th>
                                <th> Seleccionar</th>                                 
                                <th> Prov. 3 Precio/Unitario</th>
                                <th> Seleccionar</th>
    
                            </template>
                            <template slot-scope="{data}">
    
                                <tr :key="indextr" v-for="(tr, indextr) in data">

                                    <vs-td2 width="850px" :data="data[indextr].PRODUCTO">
                                        {{data[indextr].PRODUCTO}}
                                    </vs-td2>    
                                    <vs-td2 :data="data[indextr].SOLICITADO">
                                        {{data[indextr].SOLICITADO}}
                                    </vs-td2>                                    
                                    <vs-td2 :data="data[indextr].PRECIO_UNITARIO_P1">
                                        {{data[indextr].PRECIO_UNITARIO_P1}}
                                    </vs-td2>
                                    <vs-switch style="margin-top:10px;margin-left:20px" :disabled="data[indextr].EDITABLE_P1" :key="data[indextr].ID_DET_COMPARATIVO_P1" v-model="data[indextr].SELECCIONADO_P1" @click="Comparativo_DetalleCambio( data[indextr].ID_DET_COMPARATIVO_P1, data[indextr].SELECCIONADO_P1 )" />
                                    <vs-td2 :data="data[indextr].PRECIO_UNITARIO_P2">
                                        {{data[indextr].PRECIO_UNITARIO_P2}}
                                    </vs-td2>
                                    <vs-switch style="margin-top:10px;margin-left:20px" :disabled="data[indextr].EDITABLE_P2" :key="data[indextr].ID_DET_COMPARATIVO_P2" v-model="data[indextr].SELECCIONADO_P2" @click="Comparativo_DetalleCambio(   data[indextr].ID_DET_COMPARATIVO_P2, data[indextr].SELECCIONADO_P2 )" />
                                    <vs-td2 :data="data[indextr].PRECIO_UNITARIO_P3">
                                        {{data[indextr].PRECIO_UNITARIO_P3}}
                                    </vs-td2>
                                    <vs-switch style="margin-top:10px;margin-left:20px" :disabled="data[indextr].EDITABLE_P3" :key="data[indextr].ID_DET_COMPARATIVO_P3" v-model="data[indextr].SELECCIONADO_P3" @click="Comparativo_DetalleCambio(   data[indextr].ID_DET_COMPARATIVO_P3, data[indextr].SELECCIONADO_P3 )" />
                                </tr>
                            </template>
                        </vs-table2>
                        
                    </div>
    
                    <!---------- OBSERVACIONES ------------>
                    <div class="w-full md:w-full lg:w-full xl:w-full">
                        <label class="typo__label">Observaciones</label>
                        <div class="vs-con-input">
                            <input type="text" class="vs-inputx vs-input--input large" cols="40" rows="5" v-model="Observaciones">
                        </div>
                    </div>
    
                    <!---- agregar boton para guardar-->
                    <vs-divider></vs-divider>
                    <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" id="btn_confirmacion" @click="Confirmacion_Transaccion()"> Finalizar</vs-button>
                    <vs-divider></vs-divider>
                </div>
            </form>
        </vs-popup>
    
        <!--------------Fin Vista Previa información _---------->
    
    </vx-card>
    </template>
    
    <script>
   
    export default {
        name: 'componentecomparativodetalle',
        components: {
           
        },
        data() {
            return {
                Estado_VentanaComparativoDetalle: true, //mostrar las opciones del producto
                Campos_Encabezado: [],
                ListaComparativoDetalle: [],
    
                CantProveedores: 1,
                IdComparativoEnc: 0,
                Observaciones: '',
                Cotizacion_interna_selec: [],
                notificaciones: [],
            }
        },
        props: {
            callback: {
                default: null
            },
            cerrar: {
                default: null
            }
        },
        watch: {
            Estado_VentanaComparativoDetalle() {
                this.cerrar()
            }
        },
        mounted() {
    
        },
        methods: {
            Consultar_autorizaciones(Encabezado, Detalle, CotizacionSeleccionada) {
                this.Campos_Encabezado = Encabezado;
                this.ListaComparativoDetalle = Detalle;
                this.Cotizacion_interna_selec = CotizacionSeleccionada;
                this.IdComparativoEnc = parseInt(this.ListaComparativoDetalle.find(obj => obj.ID_COMPARATIVO_ENC != '').ID_COMPARATIVO_ENC);
            },
            async Consultar_GenerarComparativo(datos) {
                this.$vs.loading();
                const url = this.$store.state.global.url;
                const sesion = this.$store.state.sesion
                this.axios.post(url + 'app/v1_OrdenCompra/comparativo_detalle', {
                        IdSolicitud: datos.IDSOLICITUDENCFK,
                        Corporativo: sesion.corporativo,
                    })
                    .then(resp => {
                        this.$vs.loading.close();
                        if (resp.data.codigo != 0) {
                            this.$vs.loading.close();
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Orden de compra',
                                text: resp.data.mensaje,
                            })
    
                            this.ListaComparativoDetalle = [];
                        } else {
                            this.$vs.loading.close();
                            this.ListaComparativoDetalle = resp.data.json;
                            this.IdComparativoEnc = parseInt(this.ListaComparativoDetalle.find(obj => obj.ID_COMPARATIVO_ENC != '').ID_COMPARATIVO_ENC);
    
                        }
                    })
                    .catch(() => {
                        this.$vs.loading.close();
                    })
                    this.$vs.loading.close();
            },
            Comparativo_DetalleCambio(id, estado) {
    
                if (estado) {
                    estado = 0;
                } else {
                    estado = 1;
                }
    
                const sesion = this.$store.state.sesion
                const url = this.$store.state.global.url
    
                this.$vs.loading();
                this.axios.post(url + 'app/v1_OrdenCompra/Seleccionar_Comparativo_detalle', {
                        id_detalle_comparativo: id,
                        estado: estado,
                        Corporativo: sesion.corporativo
                    })
                    .then(resp => {
                        this.$vs.loading.close();
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Orden de compra',
                                text: resp.data.mensaje,
                            })
                        }
                    })
                    .catch(() => {
                        this.$vs.loading.close();
                    })
                this.$vs.loading.close();
    
            },
            Confirmacion_TransaccionoTRO()
            {
                this.$root.$refs.AX.Consultar_Autorizacion_cotizacion();
                this.Estado_VentanaComparativoDetalle = false;
            },
            Confirmacion_Transaccion() {
                /**
                 * @General
                 * Función Permite  actualizar un registro;
                 */
    
                const sesion = this.$store.state.sesion
                this.Corporativo_Sesion = sesion.corporativo
                const url = this.$store.state.global.url
    
                this.$vs.loading();
                this.axios.post(url + 'app/v1_OrdenCompra/comparativo_finalizar', {
                        Id_comparativo_enc: this.IdComparativoEnc,
                        Corporativo: sesion.corporativo
                    })
                    .then(resp => {
                        this.$vs.loading.close();
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Orden de compra',
                                text: resp.data.mensaje,
                            })
    
                        } else {
    
                            this.$vs.loading();
                            this.axios.post(url + 'app/v1_OrdenCompra/finalizar_autorizacion', {
                                    empresa: sesion.sesion_empresa,
                                    corporativo: sesion.corporativo,
                                    id_historial_autorizacion: this.Cotizacion_interna_selec.IDAUTORIZACIONHISTORIAL,
                                    id_solicitud_enc: this.Cotizacion_interna_selec.IDSOLICITUDENCFK,
                                    estado: 'A',
                                    id_documento: 2,
                                    num_documento: 1,
                                    observaciones: this.Observaciones,
                                    id_cotizacion_enc: '0',
                                    id_orden_compra_enc: 0
                                })
                                .then(resp => {
                                    this.$vs.loading.close();
    
                                    if (resp.data.codigo != 0) {
                                        this.$vs.notify({
                                            color: '#B71C1C',
                                            title: 'Orden de compra',
                                            text: resp.data.mensaje,
                                        })
    
                                    } else {
                                        var not_corporativo = resp.data.resultado;
                                                                            
                                        this.$root.$refs.AX.Consultar_Autorizacion_cotizacion();
                                        this.Estado_VentanaComparativoDetalle = false;
                                        this.socket.emit('message', not_corporativo);
                                    }
                                })
                                .catch(() => {
                                    this.$vs.loading.close();
                                })
                            this.$vs.loading.close();
    
                        }
                    })
                    .catch(() => {
                        this.$vs.loading.close();
                    })
                this.$vs.loading.close();
    
            },
        }
    }
    </script>
    
    <style scoped>
    .titulo-disenio {
        font-family: 'Barmeno';
        font-weight: 300;
        font-size: 25px;
        color: #252222;
        border-radius: 15px;
        margin: 0 auto;
    
        width: 400px;
        text-align: center;
        background: #fbd72f;
        margin-bottom: -40px;
    }
    
    .espacio {
        width: 0px;
        height: 0px;
        margin: 0 auto;
        border-style: solid;
        border-width: 50px;
        border-bottom-width: 0px;
        border-color: #ffffff transparent transparent transparent;
    }
    </style>
    