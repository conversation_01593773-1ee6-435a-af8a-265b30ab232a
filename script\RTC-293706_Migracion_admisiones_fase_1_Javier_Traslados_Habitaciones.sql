/****************************************************************************************/
/*---------------------<PERSON><PERSON><PERSON> de paquete a modificar --------------------------------- */
/****************************************************************************************/
USE [HOSPITAL]
GO
IF OBJECT_ID('spHabConsultaHabitaciones', 'P') IS NOT NULL 
	drop procedure spHabConsultaHabitaciones

/****************************************************************************************/
/*---------------------Fin Borrado de paquete a modificar ----------------------------- */
/****************************************************************************************/

-->
/****************************************************************************************/
/*---------------------Creacion Store Procedure sp_HisTrasladoToolkit ----------------- */
/****************************************************************************************/
USE [HOSPITAL]
GO

/****** Object:  StoredProcedure [dbo].[sp_HisTrasladoToolkit]    Script Date: 30/05/2023 08:14:12 a.m. ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



/***********************************************************************
Autor:	Vinicio Cahuex C.
Fecha:	2023.05.25
Desc.:	ToolKit para consulta y validaciones de traslado de pacientes.
------------------------------------------------------------------------
Modificado: Javier Castillo
Fecha: 2023.05.29
Desc: Se agrega la consulta de admisiones
***********************************************************************/
Create  PROCEDURE [dbo].[sp_HisTrasladoToolkit]
	@i_EmpresaU		char(3)		= 'MED',	-- empresa unificada
	@i_Habitacion	varchar(6)	= '',		-- numero de habitacion
	@i_ValidaCorp	int			= NULL,		-- corporativo a validar
	@i_Serie char(1) = NULL,
	@i_CodigoAdmision int = NULL,
	@i_Nombres		  varchar(50) = null,
	@i_Apellidos		  varchar(50) = null,
	@i_Activa			int = 1,
	@i_Tipo			tinyint					-- tipo de consumo:
												--1 = lista habitacion
												--2 = valida habitacion
												--3 = valida corporativo
                        --4 = consulta admisiones
AS
BEGIN
	SET NOCOUNT ON;
	Declare
		@ValidaIngreso	int,
		@ErrRaiseNum	int	= 0,
		@Estado			varchar(1),
		@Area			varchar(max),
		@Nombre			varchar(max),
		@ErrRaise		Varchar(99)	= ''

		
	if @i_Tipo = 1
	begin
		--lista habitaciones disponibles
		Select h.Codigo as Habitacion, rtrim(s.nombrecorto) as Nombre, rtrim(a.nombre) as Area
		From HOSPITAL..Habitaciones h (nolock)
		left join Areas_Habitaciones	a (nolock) on (a.empresa = h.empresa and a.codigo = h.area)
		left join NOMINADB..EmpHospital s (nolock) on (s.empresa = h.empresa and s.codigo = h.base)
		Where h.Empresa = @i_EmpresaU
		and (@i_Habitacion is null or h.Codigo like '%'+ ltrim(rtrim(@i_Habitacion)) +'%')
		and h.Status = 'D' and (h.Deshabilitada is null or RTRIM(h.Deshabilitada)='')
		Order by h.Codigo

		SET @ValidaIngreso = @@ROWCOUNT
		If @ValidaIngreso <= 0 
		begin
			SELECT 0 AS codigo, 'Habitación ingresada no existe o no está disponible.' AS descripcion, -1 AS tipo_error
			Return
		end--endIf
	end-- @i_Tipo = 1


	if @i_Tipo = 2
	begin
		--valida habitacion exista/disponible
		Select @Nombre = rtrim(s.nombrecorto), @Area = rtrim(a.nombre), @Estado = Status
		From HOSPITAL..Habitaciones h (nolock)
		left join Areas_Habitaciones	a (nolock) on (a.empresa = h.empresa and a.codigo = h.area)
		left join NOMINADB..EmpHospital s (nolock) on (s.empresa = h.empresa and s.codigo = h.base)
		Where h.Empresa = @i_EmpresaU
		  and h.Codigo  = @i_Habitacion
		Order by h.Codigo

		SET @ValidaIngreso = @@ROWCOUNT
		If @ValidaIngreso <= 0 
		begin
			SELECT 0 AS codigo, 'Habitación ingresada no existe.' AS descripcion, -1 AS tipo_error
			Return
		end--endIf

		if @Estado <> 'D' 
		begin
			SELECT 0 AS codigo, 'Habitación ingresada no está disponible.' AS descripcion, -1 AS tipo_error
			Return
		end--endIf

		Select @Nombre, @Area
		Return
	end-- @i_Tipo = 2

	if @i_Tipo = 3
	begin
		--valida Corporativo exista/activo
		Select @Nombre = concat(Nombres, ' ', Apellidos), @Estado = Status 
		From CONTADB..Corporativos
		Where Corporativo = @i_ValidaCorp

		SET @ValidaIngreso = @@ROWCOUNT
		If @ValidaIngreso <= 0 
		begin
			SELECT 0 AS codigo, 'Corporativo ingresado no existe.' AS descripcion, -1 AS tipo_error
			Return
		end--endIf

		if @Estado <> 'A' 
		begin
			SELECT 0 AS codigo, 'Corporativo ingresado no está activo.' AS descripcion, -1 AS tipo_error
			Return
		end--endIf

		Select @Nombre NombreCorporativo
		Return
	end--@i_Tipo = 3

	if @i_Tipo = 4
	begin
				select
					top 100 a.Serie,
					a.Codigo,
					a.Entrada,
					a.Salida,
					a.NivelPrecios,
					a.Habitacion,
					CASE WHEN a.Habitacion is null or rtrim(a.habitacion) = '' THEN ''
					ELSE ISNULL((select nombre from Areas_Habitaciones ar inner join Habitaciones h 
																		on( ar.Empresa = h.Empresa and h.Area = ar.Codigo)
														where h.Codigo = a.Habitacion and h.Empresa = @i_EmpresaU),'') END DescripcionHabitacion,
					a.Acompanante HabitacionAcompanante,
					CASE WHEN a.Acompanante is null or rtrim(a.Acompanante) = '' THEN ''
					ELSE ISNULL((select nombre from Areas_Habitaciones ar inner join Habitaciones h 
																		on( ar.Empresa = h.Empresa and h.Area = ar.Codigo)
														where h.Codigo = a.Acompanante and h.Empresa = @i_EmpresaU),'') END DescripcionHabitacionAcompanante,
					a.TipoDescuento,
          a.Seguro,
					a.Paciente IdPaciente,
					rtrim(p.Nombre) Nombres,
					rtrim(p.Apellido) Apellidos,
					rtrim(concat(rtrim(p.Nombre), ' ', rtrim(p.Apellido), ' ', p.ApellidoCasada )) Paciente,
					rtrim(concat(rtrim(a2.Nombre), ' ', rtrim(a2.Apellido))) Medico, 
					concat(rtrim(a.Serie),'-',a.Codigo) as Admision 
				from
					Admisiones a
				LEFT JOIN Pacientes p ON
					a.Paciente = p.Codigo
					AND a.Empresa = p.Empresa
				LEFT JOIN Ajenos a2 ON
					a.Medico = a2.Codigo
					and a.Empresa = a2.Empresa
				WHERE a.Empresa = @i_EmpresaU					
					and ((@i_Serie is not null and @i_CodigoAdmision is not null and a.Serie = @i_Serie and a.Codigo = @i_CodigoAdmision ) OR
								(@i_Serie is null or @i_CodigoAdmision is null))						
					and (@i_Nombres is null or p.nombre like '%'+@i_Nombres+'%')
					and (@i_Apellidos is null or p.apellido like '%'+@i_Apellidos+'%')
					and (@i_Activa = 0 or (a.Salida is null and a.FechaPreEgreso is null))
				order by
					a.Entrada desc
	end--@i_Tipo = 4
END
GO
/****************************************************************************************/
/*-----------------Fin Creacion Store Procedure sp_HisTrasladoToolkit ----------------- */
/****************************************************************************************/

-->>

/****************************************************************************************/
/*----------------- Creacion Store Procedure sp_HisTrasladoHabitacion ------------------*/
/****************************************************************************************/

USE [HOSPITAL]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[sp_HisTrasladoHabitacion](
	@IEmpresaU				varchar(3),
	@IEmpresaReal			varchar(3),
	@ICorporativoIngresa	Int,			--corporativo del coloborador que ingresa la 
	-- ========== De la admisión ============== 
	@ISerieAdmision			varchar(1),
	@IAdmision				Int,
	-- ========== Del traslado ============== 
	@IHabitacionNueva		varchar(5) = '',  
	@IHabitacionAcompNueva	varchar(5) = '',  
	@IEnfermera				varchar(20),
	@IAutorizacion			varchar(20)

)
AS
BEGIN
/*****************************************
Fecha:	2022.01.10
Autor:	Nery Villatoro
desc.:	SP Traslado de pacientes (habitación/hospital)
		Realiza el cambio de habitación y empresa a una admision
Valida: Vinicio Cahuex
Update: 2023.05.22
*******************************************/

	DECLARE
		@Orden		int,
		@Linea		int,
		@CodigoT	int,
		@Costo		money,
		@Valor		money,
	    @Precio		money,
		@Factor		smallmoney,

		@Usuario	varchar(005),
		@UMedida	varchar(008),
		@Interno	varchar(001),
		@TipoOrden	varchar(003) = '',
		@Categoria	varchar(002) = '',
		@MsgCorreo	varchar(max),
		@AsuntoMail varchar(max), 
		@TituloMail varchar(max),

		@ProductoHab			varchar(13),
		@ProductoHabAcom		varchar(13) = NULL,
		@ProductoHabPaquete		varchar(13) = NULL,

		@DestinatarioCorreo		varchar(50),
		@DestinatarioCopia		varchar(50),
		@HospitalNuevaHabitacion varchar(3),

		-- ===================================================
		@IEntrada				Date,
		@ICodigoPac				Int,
		@ISeguro				varchar(3) = '', 
		@IPaqueteDeadmision		Int = NULL,
		@ITipoDescuento			varchar(1),
		@ICodigoHabActual		varchar(5) = '',  
		@ICodigoHabAcomActual	varchar(5) = '',  
		-- ===================================================

		@HoraActual	int = 0,
		@RecargoHabitacion		money,
		@RestaraValor			smallint = 0,
		@CantidadCargo			smallint = 0,
		@NivelPrecioNuevo		smallint = 0,
		@NivelPrecioNuevoHab	smallint = 0,
		@FlagExistenCargosAnt	smallint = 0,
		@AreaServicioHabNueva	smallint = 0

	SET NOCOUNT ON

	BEGIN TRAN
	BEGIN TRY

		DECLARE
		@FlagErr		int = 0,
		@w_ErrRaiseNum 	Int	= 0,
		@w_ErrRaise		varchar(max)= ''

		--Begin 
		/* si la hora del cambio es entre las 12 y las 12 PM, busca el cargo de la habitación
		anterior y le restas 1 a la cantidad, si no entónces graba el nuevo cargo con 0
		en la cantidad y deja el cargo anterior como estaba, de cualquier manera
		si el cambio es de dIferente categoría, le cambia el nivel del precios
		a la admisión... */

		-- valida que exista la admision
		Select @IEntrada = Entrada, @ICodigoPac = Paciente, @ISeguro = rtrim(Seguro), 
			@ITipoDescuento = TipoDescuento, @ICodigoHabActual = rtrim(Habitacion), 
			@ICodigoHabAcomActual = rtrim(Acompanante), @IPaqueteDeadmision = rtrim(Paquete)
		From Admisiones
		Where Empresa  = @IEmpresaU
			and Serie  = @ISerieAdmision
			and Codigo = @IAdmision

		SET @FlagErr = @@ROWCOUNT
		If @FlagErr <= 0 
		begin
			SELECT 0 AS codigo, 'No existe esta admision.' AS descripcion, -1 AS tipo_error
			ROLLBACK TRAN
			Return
		end--endIf

		-- 'Acompañante no puede ocupar la misma habitación que el paciente'
		-- 'La habitación no existe'
		-- 'La habitación escogida ya se encuentra ocupada'
		-- 'Las habiaciones nuevas siguen siendo las misma que las anteriores' (Ambas, paciente y acompañante sin cambios)


		-- 1ero Actualizamos el nivel y el codigo de la habitacion en la Admision...

		-- VerIficando si se mueve de área o de Base -- En este momento no se usará a pesar que en Delphi si se usa al grabar
		-- la admisión se le suma 0 o 1 al campo de AreaServicio, ahora se grabará con el área de la habitación

		-- Procederemos a registrar el tipo de orden
		SET @TipoOrden = 'AD'+ @ISerieAdmision

		-- Si tiene una habitación es Interno
		If isnull(@IHabitacionNueva,'')='' 
			SET @Interno='N' 
		else 
			SET @Interno='S'
		--endIf 

		-- Averiguamos el @HospitalNuevaHabitacion y el @NivelPrecioNuevoHab de habitaciones
		Select @HospitalNuevaHabitacion = h.Base, @NivelPrecioNuevoHab = h.NivelPrecios, 
			    @AreaServicioHabNueva = h.Area, @ProductoHab = h.Producto, @Categoria = p.Categoria,
				@Costo = Case when p.Costeo='U' then P.CostoUltimo else p.CostoPromedio END
		--, H.Codigo, H.Producto, Status, Extension, P.Nombre,
		From HOSPITAL..Habitaciones h 
		inner join  INVENTARIO.dbo.Productos p ON (h.Empresa = p.Empresa and h.Producto = p.Codigo)
		Where h.Empresa = @IEmpresaU 
		and h.Codigo = @IHabitacionNueva

		-- valida habitacion nueva
		SET @FlagErr = @@ROWCOUNT
		If @FlagErr <= 0 
		begin
			SELECT 0 AS codigo, 'No existe la habitacion nueva.' AS descripcion, -1 AS tipo_error
			ROLLBACK TRAN
			Return
		end--endIf


		Select @NivelPrecioNuevo = Nivel_Igss 
		From NominaDB..EmpHospital e (nolock) 
		Where Empresa=@IEmpresaU 
		and Codigo = @HospitalNuevaHabitacion
		and Igss=@ISerieAdmision

		Select @ISeguro = ISNULL(@ISeguro,'')

		If @NivelPrecioNuevo = 0   -- 0 si no fue seleccionado por Igss
		Begin
			If @ISeguro=''  -- Privado se usa el nivel de la habitación
				SET @NivelPrecioNuevo = @NivelPrecioNuevoHab	
			else
				-- @@INivelPrecioNuevo Viene de aseguradora (AdmisionesQuery)
				/* EN CASO DE ADMISION DE SEGUROS...
				Nivel de precio definida por el Seguro (de acuerdo a la Aseguradora)...
				solo se toma en cuenta el nivel de Internos puesto que, si se va a quedar en habitación
				se tomara como una admision tipo "Interna" (porque se quedará hospitalizada)...*/
				Select  @NivelPrecioNuevo = a.NivelPreciosInt
				From Hospital..Aseguradoras AS  a (nolock) 
				INNER JOIN Hospital..Seguros AS s (nolock) ON (a.Empresa = s.Empresa and a.Asegura = s.Asegura)
				Where (a.Empresa = @IEmpresaU) 
				and (s.Codigo = @ISeguro) 
				and (s.Activa = 'S')
			--end else/If
		end--If @NivelPrecioNuevo = 0

		Select @Usuario = LEFT(CAST(@ICorporativoIngresa AS varchar(15)), 5)

		-- Averiguando @ProductoHabPaquete de Paquetes si se tiene @IPaqueteDeadmision
		Select @ProductoHabPaquete = q.Habitacion
		From Hospital..Paquetes q (nolock)
		Where q.Empresa=@IEmpresaU 
		and q.Codigo=@IPaqueteDeadmision

		-- UpdAdmision --
		Update HOSPITAL..Admisiones 
			Set Interno = @Interno, 
				Habitacion = @IHabitacionNueva, 
				Acompanante = @IHabitacionAcompNueva, 
				NivelPrecios = @NivelPrecioNuevo, 
				AreaServicio = @AreaServicioHabNueva
		Where Empresa = @IEmpresaU 
		and Serie = @ISerieAdmision 
		and Codigo = @IAdmision

		SET @FlagErr = @@ROWCOUNT
		If @FlagErr <= 0 
			SELECT 0 AS codigo, 'No existe esta admision.' AS descripcion, -1 AS tipo_error
			SET @w_ErrRaise = @w_ErrRaise + 'Error: No se pudo actualizar la Admisión' 
		--endIf

		-- 2do Actualizamos el estado de las habitaciones (la nueva y la anterior)...
		/* Esto estaba en delphi pero no tiene sentido
		--averigua si hay registros de habitacion
		Select @FlagExistenCargosAnt = count(*)
		From Hospital..Cargos (nolock)
		Where Empresa = @IEmpresaU and SerieAdmision = @ISerieAdmision and
		Admision = @IAdmision and Status = 'H'
		*/

        --// Ahora busca los cargos con status H
        --// el status 'H' signIfica que esos son los cargos de habitacion habilitados

		-- UpdHabitaciones. se cambia el status de ambas habitaciones
		--libera habitacion actual
		Update Hospital..Habitaciones
			SET Status = 'D'
		Where Empresa = @IEmpresaU  
		and (Codigo = @ICodigoHabActual or Codigo = @ICodigoHabAcomActual)
		--ocupa habitacion nueva
		Update Habitaciones
			SET   Status = 'A'
		Where Empresa = @IEmpresaU 
		and (Codigo = @IHabitacionNueva or Codigo = @IHabitacionAcompNueva)

        -- 3ero insertaremos el registro del cambio de habitación...

		-- Inserta traslado
		Select @CodigoT = isnull(max(codigo),0)+1 
		From Hospital..Traslados (nolock) 
		Where Empresa = @IEmpresaU

		Insert Into Hospital..Traslados 
			(Empresa, Codigo, Fecha, SerieAdmision, Admision, HabitacionAnterior, HabitacionNueva, 
			Usuario, FechaRegistro, AcompananteAnterior, AcomantanteNuevo, Corporativo, Enfermera, Autorizacion) 
		Values  (@IEmpresaU , @CodigoT, getdate(), @ISerieAdmision, @IAdmision, @ICodigoHabActual, @IHabitacionNueva,
			@Usuario, getdate() , @ICodigoHabAcomActual, @IHabitacionAcompNueva, @ICorporativoIngresa, @IEnfermera, @IAutorizacion)    
               
		-- Aumenta 1 al siguiente Correlativo
		Update Hospital..Correlativo_Traslados
			Set Siguiente = @CodigoT + 1
		Where Empresa = @IEmpresaU

		/*********************************************************************
			Este es el segundo bloque del proceso de traslado...
		*********************************************************************/
		Select @FlagExistenCargosAnt = Case when sum(Cantidad)>0 then 1 else 0 End
		From Hospital..Cargos (nolock) 
		Where Empresa = @IEmpresaU 
		and SerieAdmision = @ISerieAdmision 
		and Admision = @IAdmision 
		and Status = 'H' 
		and Cantidad>0

		--Recuperar el precio de la habitación del nivel de la admision
		Select @Precio=Precio, @UMedida=UnidadMedida, @Factor=Factor
		From Inventario..ProductosPrecios (nolock)
		Where Empresa = @IEmpresaU
		and SubNivel = 1  
		and Nivel = @NivelPrecioNuevo
		and Producto = @ProductoHab
            
		If @FlagExistenCargosAnt = 1 
		Begin
			Select @HoraActual = convert(varchar(2), getdate(), 108)*1

			If (Case when (@HoraActual > 13 OR @HoraActual >= 13 and convert(date, getdate()) = convert(date, @IEntrada) ) then 1 else 0 END) = 1
			begin
			    -- deshabilita los cargos anteriores (les cambia el status) para el cargo automatico,  y les resta 1 a la cantidad  si la hora es antes de las 12 }
				-- O Si entro despues de la hora de corte o entro ese mismo dia en la mañana
				SET @RestaraValor = 1
				-- el dia que cuenta es el de la habitacion nueva
				SET @CantidadCargo = 1
			end
			else
			begin
				-- El dia de la nueva habitacion cuenta hasta la hora del cargo automatico
				SET @RestaraValor =  0
				SET @CantidadCargo = 0
			end--else if
		end--then if @FlagExistenCargosAnt = 1 
		else
		Begin
			-- Si no habian cargos debe agregar el cargo con cantidad 1
			-- A MENOS QUE SEA AMBULATORIO (ver mas adelante)...
			--If AdmisionesQueryPreoperatorio.Value ='S'        
			--     Esto ya no se está usando todos vienen 'N' x default y no ha cambiado ningun dato en 8 años
			--   SET @CantidadCargo = 0
			--else 
			SET @RestaraValor =  0
			SET @CantidadCargo = 1
		end-- else if if @FlagExistenCargosAnt = 1 
 
		-- deshabilita los cargos anteriores
		Update Cargos 
			SET Valor = Valor - @RestaraValor * PrecioUnitario, 
				Cantidad = Cantidad - @RestaraValor, 
				STATUS = 'P'
		Where Empresa = @IEmpresaU 
		and SerieAdmision = @ISerieAdmision 
		and Admision = @IAdmision 
		and Status = 'H'
			
		-- Obtener la siguiente línea del cargo
		Select @Linea = Case when Count(*)>0 then max(Linea) + 1 else 1 End
		From Hospital..Cargos (nolock)
		Where Empresa = @IEmpresaU 
		and TipoOrden = @TipoOrden 
		and Orden = @IAdmision

		SET @RecargoHabitacion=0

		If (@ITipoDescuento = 'Q' and @ProductoHab <> @ProductoHabPaquete)
		Begin
			--Recuperar el precio de la habitación del nivel de la admision
			Select @RecargoHabitacion = @Precio - Precio
			From Inventario..ProductosPrecios (nolock)
			Where Empresa = @IEmpresaU
			and SubNivel = 1  and Nivel = @NivelPrecioNuevo			
			and Producto = @ProductoHabPaquete
		end

		If (@ITipoDescuento = 'Q')  -- Cambiando el productohab, Precio y Valor si es Paquete
		Begin
			SET @ProductoHab = @ProductoHabPaquete  -- IMPORTANTE: Aqui se sustituye el productohab x que es paquete
			If @RecargoHabitacion>=0
			Begin
				Set @Precio = @Precio - @RecargoHabitacion
				Set @Valor  = @Precio * @CantidadCargo
			end
			else
			Begin
				Set @Valor = @Precio * @CantidadCargo
			end
		end--then
		else
			Set @Valor = @Precio * @CantidadCargo
		--end else-if
		

		-- Inserta el Cargo
        INSERT IntO Hospital..Cargos 
			(Empresa, TipoOrden, Orden, Linea, SerieAdmision, Admision, Fecha, Producto, 
			Categoria, Valor, Cantidad, Status, Paciente, PrecioUnitario, SubNivelPrecios, 
			Factor, Costo, UnidadMedida, EmpresaReal, EmpresaUnIf, CostoHospital, Corporativo)
		Values (@IEmpresaU, @TipoOrden, @IAdmision, @Linea, @ISerieAdmision, @IAdmision, getdate(),
				@ProductoHab, @Categoria, @Valor, @CantidadCargo, 'H', @ICodigoPac, @Precio, 1, @Factor,
				@Costo, @UMedida, @IEmpresaReal, @IEmpresaU, @Costo, @ICorporativoIngresa)

		-- VerIficarmos si existe la órden
		If (
			Select count(*) 
			From HOSPITAL..Ordenes (nolock) 
			Where Empresa= @IEmpresaU 
			and Tipo= @TipoOrden 
			and Codigo = @IAdmision) = 0	 -- Si no existe Insertamos la Orden
		--then
			INSERT IntO Ordenes(Empresa, Tipo, Codigo, Fecha, Interno, SerieAdmision, Admision, Usuario, Status, EmpresaReal, EmpresaUnIf, Habitacion, Corporativo)
			Values (@IEmpresaU, @TipoOrden, @IAdmision, getdate(), @Interno, @ISerieAdmision, @IAdmision, @Usuario, 'P', @IEmpresaReal, @IEmpresaU, @IHabitacionNueva, @ICorporativoIngresa)
		--endif
			
		/* Esto no se incluyo por que no se encontraron datos desde 2017
		If RecargoHabitacion > 0 then
			// si la admisión es de seguro y la habitacion no es del nivel de precios adecueado
			// debe tomar el precio del nivel adecuado, y cargar el recargo por hab. privada
			*/

		If @IHabitacionAcompNueva<>'' 
		Begin
			-- Incrementamos la línea
			Select @Linea = @Linea + 1

			-- Averiguamos el @HospitalNuevaHabitacion y el @NivelPrecioNuevoHab de habitaciones de Acompañante (AcompañanteQuery)
			Select @ProductoHabAcom = h.Producto, @Categoria = P.Categoria,
					@Costo = Case when p.Costeo='U' then p.CostoUltimo else p.CostoPromedio END
					/*@HospitalNuevaHabitacion = H.Base, 
					@NivelPrecioNuevoHab = H.NivelPrecios, 
					@AreaServicioHabNueva = H.Area, */
			--, H.Codigo, H.Producto, Status, Extension, P.Nombre,
			From HOSPITAL..Habitaciones h (nolock)
			inner join  INVENTARIO.dbo.Productos p (nolock) on (h.Empresa = p.Empresa and h.Producto = p.Codigo)
			Where H.Empresa = @IEmpresaU 
			and H.Codigo = @IHabitacionAcompNueva

			-- Averiguando el precio de la habitación del Acompañante
			Select @Precio=Precio, @UMedida=UnidadMedida, @Factor=Factor
			From Inventario..ProductosPrecios (nolock)
			Where Empresa = @IEmpresaU
			and SubNivel = 1  
			and Nivel = @NivelPrecioNuevo
			and Producto = @ProductoHabAcom

			-- Inserta el Cargo Habitación Acompañante
			INSERT IntO Hospital..Cargos 
				(Empresa, TipoOrden, Orden, Linea, SerieAdmision, Admision, Fecha, Producto, 
				Categoria, Valor, Cantidad, Status, Paciente, PrecioUnitario, SubNivelPrecios, 
				Factor, Costo, UnidadMedida, EmpresaReal, EmpresaUnIf, CostoHospital, Corporativo) 
			Values (@IEmpresaU, @TipoOrden, @IAdmision, @Linea, @ISerieAdmision, @IAdmision, getdate(),
					@ProductoHabAcom, @Categoria, @Precio, 1, 'H', @ICodigoPac, @Precio, 1, @Factor,
					@Costo, @UMedida, @IEmpresaReal, @IEmpresaU, @Costo, @ICorporativoIngresa)
		end--if @IHabitacionAcompNueva

		-- Genera mensaje para correo por traslado de Hospital
		--si es encamamiento no envia correo
		--si es traslado de habitación, entonces sí envia correo
		if  (@ICodigoHabActual <> '')
		and (@ICodigoHabActual <> @IHabitacionNueva)
		Begin --sí envia correo
			Select @MsgCorreo = '<big><big>Traslado IntraHospitalario de paciente. Admisión: '
			+ t.SerieAdmision + '-' + CAST(t.Admision AS varchar(10)) + '</big></big><br><br>'
			+ 'Paciente: <b>' + concat(rtrim(p.nombre),' ',rtrim(p.apellido))					+ '.</b><br>'
			+ 'Fecha y hora de traslado: '+ FORMAT(t.FechaRegistro,'dd/mm/yyyy hh:mm:s tt')		+ '<br>'
			+ 'Fecha y hora de ingreso: ' + FORMAT(a.Entrada,'dd/mm/yyyy hh:mm:s tt')			+ '<br>'
			+ 'Habitación anterior: '	+ concat(t.HabitacionAnterior, ' - ', rtrim(aa.nombre)) + '<br>'
			+ 'Habitación nueva:<b>'	+ concat(t.HabitacionNueva, ' - ', rtrim(an.nombre))	+ '.</b><br>'
			+ 'Ubicación actual: '		+ rtrim(en.Nombre) + '<br>'
			+ 'Colaborador que asigna habitación: '+ Concat(c.Corporativo,' - ', rtrim(c.Nombres),' ', rtrim(c.Apellidos)) + '<br>'
			+  Case when Autorizacion<>'' then 'Autoriza: ' + Autorizacion + '<br>' else '' END
			+ '<br><br><FONT COLOR="red">Correo generado automáticamente favor no responder al remitente.</FONT>',
			@AsuntoMail =  'Traslado Intrahospitalario de paciente. Admisión: '+ t.SerieAdmision + '-' + CAST(t.Admision AS VARCHAR(10)),
			@TituloMail =  '<H2>Traslado Intrahospitalario de paciente. Admisión: '+ t.SerieAdmision + '-' + CAST(t.Admision AS VARCHAR(10)) +'</H2>'
			From HOSPITAL..Traslados t (nolock)
			left join HOSPITAL..Habitaciones ha (nolock) on (ha.empresa = t.empresa and ha.codigo = t.habitacionanterior)
			inner join HOSPITAL..Habitaciones hn (nolock) on (hn.empresa = t.empresa and hn.codigo = t.habitacionnueva)
			left join HOSPITAL..Areas_Habitaciones  aa (nolock) on (aa.empresa = ha.empresa and aa.codigo = ha.area)
			inner join HOSPITAL..Areas_Habitaciones an (nolock) on (an.empresa = hn.empresa and an.codigo = hn.area)
			left join ContaDb..Empresas ea (nolock) on (ea.codigo = rtrim(ha.empresareal))
			inner join ContaDb..Empresas en (nolock) on (en.codigo = hn.empresareal)
			inner join ContaDb..Corporativos c (nolock) on (c.corporativo = t.corporativo)
			inner join HOSPITAL..Admisiones a (nolock) on (a.empresa = t.empresa and a.serie = t.serieadmision and a.codigo = t.admision)
			inner join HOSPITAL..Pacientes p (nolock) on (p.empresa = t.empresa and p.codigo = a.paciente)
			Where t.Empresa = @IEmpresaU
			and isnull(HabitacionNueva,'') <>''
			and SerieAdmision = @ISerieAdmision
			and Admision = @IAdmision
			Order by t.Codigo desc

			-- Selecciona a los destinatarios
			Select distinct @DestinatarioCorreo=Correo, @DestinatarioCopia=Responsablecorreo
			From Hospital..NotIficadorMail as N 
			Inner Join Hospital..NotIficadormailDetalle  as nd (nolock) ON(nd.codigoEnvio=N.codigoEnvio) 
			Inner Join Hospital..NotIficadorListaCorreos as nl (nolock) ON(nl.codigocorreo=nd.codigocorreo)
			Where Hospital= @HospitalNuevaHabitacion
		
			-- Insertando el correo
					--eliminar estas lineas en produccion
					SET @DestinatarioCorreo = '<EMAIL>'	--destino de prueba
					SET @DestinatarioCopia	= ''							--destino de prueba
			INSERT IntO Hospital..His_Enviar_Correo_Electronico 
					(Empresa, Enviar_a, Copiar_a, Asunto, Mensaje_titulo, Mensaje_descripcion)
			Values	(@IEmpresaReal, @DestinatarioCorreo, @DestinatarioCopia, @AsuntoMail,@TituloMail, @MsgCorreo)
		end--if  (@ICodigoHabActual <> '')

		COMMIT TRAN
		Select 0 CodigoError, @ISerieAdmision Serie, @IAdmision Codigo, @ISerieAdmision + '-' + CAST(@IAdmision AS varchar(12)) Admision

	END TRY
	BEGIN CATCH		
		--revierte cambios y envia alerta
		ROLLBACK TRAN;
		Set @w_ErrRaise = ERROR_MESSAGE()
		Set @w_ErrRaiseNum = ERROR_STATE()
		RAISERROR(@w_ErrRaise,16,@w_ErrRaiseNum)

	END CATCH
END

GO

/****************************************************************************************/
/*----------------- Fin Creacion Store Procedure sp_HisTrasladoHabitacion --------------*/
/****************************************************************************************/

-->>


/****************************************************************************************/
/*----------------- Creacion Store Procedure spHabConsultaHabitaciones -----------------*/
/****************************************************************************************/

USE [HOSPITAL]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [dbo].[spHabConsultaHabitaciones]
	  @i_Empresa  char(3)
	, @i_EmpresaReal   char(3)
	, @i_CodigoHabitacion char(5) = null
AS		 
		
BEGIN

	SELECT    Habitaciones.Codigo
	        , Habitaciones.Status
			, Habitaciones.NivelPrecios
			, Habitaciones.Area
			, Areas_Habitaciones.Nombre
			, Habitaciones.Producto
			, RIGHT(RTRIM(dbo.Habitaciones.Codigo)
			, { fn LENGTH(RTRIM(dbo.Habitaciones.Codigo)) } - 1) + ' ' + dbo.Areas_Habitaciones.Nombre AS CodigoArea
	FROM      Habitaciones LEFT OUTER JOIN
			  Areas_Habitaciones ON (Habitaciones.Area = Areas_Habitaciones.Codigo AND
			  Habitaciones.Empresa = Areas_Habitaciones.Empresa)
	WHERE (Habitaciones.Empresa = @i_Empresa) AND (Habitaciones.EmpresaReal = @i_EmpresaReal)
	  and (@i_CodigoHabitacion is null or Habitaciones.Codigo like '%'+ @i_CodigoHabitacion +'%')
	  and (Habitaciones.Status = 'D')
	  and (Habitaciones.Deshabilitada is null or RTRIM(Habitaciones.Deshabilitada)='')
	ORDER BY  Habitaciones.Codigo,  Areas_Habitaciones.Nombre
END
GO
/****************************************************************************************/
/*----------------- Fin Creacion Store Procedure spHabConsultaHabitaciones -------------*/
/****************************************************************************************/

-->
/****************************************************************************************/
/*------------------------- Creacion de Permisos ---------------------------------------*/
/****************************************************************************************/

use hospital
go
grant execute on sp_HisTrasladoHabitacion to USRRAD
go

use hospital
go
grant execute on sp_HisTrasladoToolkit to USRRAD
go

use HOSPITAL
GO

grant execute on spHabConsultaHabitaciones to USRRAD
GO


USE [NOMINADB]
GO
IF (SELECT USER_ID('USRRAD')) IS NULL
	CREATE USER [USRRAD] FOR LOGIN [USRRAD] WITH DEFAULT_SCHEMA=[dbo]
GO

use nominadb
go
grant select on EmpHospital to USRRAD
go

use INVENTARIO
go
grant select on ProductosPrecios to USRRAD
go

/****************************************************************************************/
/*------------------------- Fin Creacion de Permisos -----------------------------------*/
/****************************************************************************************/
