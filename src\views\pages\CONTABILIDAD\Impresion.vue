<template>
<div>
    <form @submit="handleSubmit">
        <DxForm :ref="formImpresion" :form-data.sync="formulario" label-mode="floating">
            <DxFormGroupItem :col-count="ImpresionLotes == 'S' ? 2 : 1">
                <DxFormGroupItem>
                    <DxFormItem data-field="Cuenta" editor-type="dxSelectBox" :editor-options="{ readOnly: ImpresionLotes == 'S', width: 'auto', searchEnabled: true, items: cuentas,  displayExpr: 'Valor', valueExpr: 'Codigo' }" :validationRules="[{ type: 'required' }]" />
                    <DxFormGroupItem :col-count="2">
                        <DxFormItem data-field="ChequeInicial" editor-type="dxNumberBox" :editor-options="{ readOnly: ImpresionLotes == 'S' && formulario.Cuenta == null, min: 0, format: '#0', step: 0 }">
                            <DxFormLabel text="Del número" />
                        </DxFormItem>
                        <DxFormItem data-field="ChequeFinal" editor-type="dxNumberBox" :editor-options="{ readOnly: ImpresionLotes == 'S' && formulario.Cuenta == null, min: 0, format: '#0', step: 0 }">
                            <DxFormLabel text="Al número" />
                        </DxFormItem>
                    </DxFormGroupItem>
                    <DxFormButtonItem :button-options="buttonImprimir" name="Imprimir" horizontal-alignment="center" verical-alignment="center" :visible="this.formulario.Cuenta !== null" />
                </DxFormGroupItem>

                <DxFormGroupItem v-if="ImpresionLotes == 'S'">
                    <DxFormItem template="lotes" />
                </DxFormGroupItem>

            </DxFormGroupItem>

            <template #lotes="{}">
                <div class="pb-2">
                    <DxDataGrid v-bind="DefaultDxGridConfiguration" :data-source="lotes" :paging="{ enabled: false }" :searchPanel="{ visible: false} " :headerFilter="{ visible: false, allowSearch: false }" :width="'100%'" height="170" :on-row-click="SeleccionarLote">
                        <DxDataGridSelection mode="single" />
                        <DxDataGridScrolling mode="virtual" />

                        <DxDataGridColumn width="auto" data-field="Id" alignment="center" data-type="string" />
                        <DxDataGridColumn width="auto" data-field="Lote" alignment="center" data-type="string" />
                        <DxDataGridColumn width="auto" data-field="EsCuentaAjena" alignment="center" data-type="string" />

                        <template #reporte>
                            <DxButton width="30px" height="30px" type="danger" styling-mode="contained" class="botonReporte" @click="DescargarPDF" hint="Imprimir cheque">
                                <font-awesome-icon :icon="['fas', 'file-pdf']" style="font-size: 20px;" />
                            </DxButton>
                        </template>
                    </DxDataGrid>
                </div>
            </template>
        </DxForm>
    </form>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'
import 'devextreme-vue/lookup'

const formImpresion = 'formImpresion'

export default {
    name: 'Impresion',
    components: {},
    data() {
        return {
            DefaultDxGridConfiguration,
            formImpresion,

            formulario: {
                Cuenta: null, // Codigo de la cuenta de banco de 2 dígitos
                NumeroCuenta: null, // Número de la cuenta bancaria
                BancoContabilidad: null, // Cuenta contable de la cuenta bancaria seleccionada
                ChequeInicial: null, // Primer cheque del rango a imprimir
                ChequeFinal: null, // Último cheque del rango a imprimir
                NombreCuentaBanco: null, // Nombre de la cuenta contable de la cuenta bancaria 
            },
            cuentas: [],

            buttonImprimir: {
                width: 'auto',
                icon: 'fas fa-print',
                text: 'Imprimir',
                type: 'success',
                onClick: () => {
                    this.BuscarCheques()
                },
                useSubmitBehavior: false,
            },

            cheques: [],
            lotes: []
        }
    },
    props: {
        ModalidadPago: null,
        TipoCheque: null,
        ImpresionLotes: null,
        Corporativo: null
    },
    methods: {
        handleSubmit(e) {
            e.preventDefault()
        },

        CargarCuentas() {
            this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 1
                })
                .then(resp => {
                    this.cuentas = resp.data.json

                    if (this.ImpresionLotes == 'N') {
                        this.formulario.Cuenta = this.cuentas[0].Codigo
                    }
                })
        },

        HabilitarBoton() {
            if (this.ValidarCheque(this.formulario.ChequeInicial) && this.ValidarCheque(this.formulario.ChequeFinal) && this.formulario.ChequeFinal >= this.formulario.ChequeInicial) {
                return true
            } else {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'Cheques no validos',
                    acceptText: 'Aceptar',
                    text: 'Debe ingresar ambos cheques y el cheque final debe ser mayor o igual al cheque inicial',
                    buttonCancel: 'border',
                    accept: () => {},
                })
                return false
            }
        },

        ValidarCheque(cheque) {
            if (cheque !== null && cheque > 0) {
                return true
            }
            return false
        },

        async BuscarCheques() {
            this.HabilitarBoton()
            await this.axios.post('/app/v1_bancos/Impresion', {
                    Opcion: 1,
                    Cuenta: this.formulario.Cuenta,
                    TipoCheque: this.TipoCheque,
                    ChequeInicial: this.formulario.ChequeInicial,
                    ChequeFinal: this.formulario.ChequeFinal
                })
                .then(resp => {
                    this.cheques = []
                    if (resp.data.json.length > 0) {
                        this.cheques = resp.data.json

                        let diferencia = null

                        diferencia = this.formulario.ChequeFinal - this.formulario.ChequeInicial + 1

                        if (diferencia != this.cheques.length) {
                            this.$vs.dialog({
                                type: 'confirm',
                                color: '#ed8c72',
                                title: 'Exceso de líneas',
                                acceptText: 'Aceptar',
                                text: 'El número de cheques disponibles no coincide con el correlativo. ¿Desea continuar?',
                                buttonCancel: 'border',
                                accept: () => {
                                    this.ActualizarImpresion()
                                },
                                cancelText: 'Cancelar',
                                cancel: () => {}
                            })
                            return
                        } else {
                            this.ActualizarImpresion()
                        }
                    }
                })
        },

        async ImpresionPDF() {
            this.ActualizarImpresion()
            // let i = 0
            // while (i < this.cheques.length) {
            //     await this.ActualizarImpresion(this.cheques[i])
            //     i++
            // }
            
            if (this.ImpresionLotes == 'S') {
                this.BuscarLotes()
            }

        },

        async BuscarLotes() {
            await this.axios.post('/app/v1_bancos/Impresion', {
                    Opcion: 8,
                })
                .then(resp => {
                    this.lotes = []
                    this.formulario.Cuenta = null
                    this.formulario.ChequeInicial = null
                    this.formulario.ChequeFinal = null

                    if (resp.data.json.length > 0) {

                        this.lotes = resp.data.json

                        this.formulario.Cuenta = this.lotes[0].PagoCuenta
                        this.formulario.ChequeInicial = this.lotes[0].MinCh
                        this.formulario.ChequeFinal = this.lotes[0].MaxCh
                    } else {

                        this.formulario.Cuenta = null
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Sin lotes',
                            acceptText: 'Aceptar',
                            text: 'No exísten lotes pendientes de generar.',
                            buttonCancel: 'border',
                            accept: () => {},
                        })
                    }
                })
        },

        async ActualizarImpresion() {
            await this.axios.post('/app/v1_bancos/Impresion', {
                    Opcion: 9,
                    Cuenta: this.formulario.Cuenta,
                    TipoCheque: this.TipoCheque,
                    ChequeInicial: this.formulario.ChequeInicial,
                    ChequeFinal: this.formulario.ChequeFinal,
                    Usuario: this.Corporativo
                })
                .then(resp => {
                    if (resp.data.json[0].codigo == 0) {
                        this.AccionImprimir()
                    }
                })
        },

        AccionImprimir() {
            this.DescargarPDF()
            this.ActualizarEstado()
            // this.ActualizarLote(info.Numero)
        },

        async ActualizarEstado() {
            await this.axios.post('/app/v1_bancos/Impresion', {
                    Opcion: 10,
                    Cuenta: this.formulario.Cuenta,
                    ChequeInicial: this.formulario.ChequeInicial,
                    ChequeFinal: this.formulario.ChequeFinal,
                    TipoCheque: this.TipoCheque
                })
                .then()
        },

        async ActualizarLote(numero) {
            await this.axios.post('/app/v1_bancos/Impresion', {
                    Opcion: 12,
                    Cuenta: this.formulario.Cuenta,
                    ChequeInicial: numero,
                })
                .then()
        },

        DescargarPDF() {
            this.$reporte_modal({
                Nombre: 'ImpresionCheque',
                Opciones: {
                    Cuenta: this.formulario.Cuenta,
                    TipoCheque: this.TipoCheque,
                    ChequeInicial: this.formulario.ChequeInicial,
                    ChequeFinal: this.formulario.ChequeFinal,
                    ModalidadPago: this.ModalidadPago
                }
            })
        },

        SeleccionarLote(e) {
            this.formulario.ChequeInicial = e.data.MinCh
            this.formulario.ChequeFinal = e.data.MaxCh
            this.formulario.Cuenta = e.data.PagoCuenta
        },
    },
    created() {},
    mounted() {
        if (this.ImpresionLotes == 'S') {
            this.BuscarLotes()
        }
    },
    beforeMount() {
        this.CargarCuentas()
    },
    watch: {
        'formulario.ChequeInicial'(newval) {
            if (this.formulario.ChequeFinal == 0 || this.formulario.ChequeFinal == null || parseInt(this.formulario.ChequeFinal) < parseInt(this.formulario.ChequeInicial)) {
                this.formulario.ChequeFinal = newval
            }
        }
    },
    computed: {
        formImpresionInstance: function () {
            return this.$refs[formImpresion].instance;
        },
    }
}
</script>

<style>
</style>
