<template>
<vx-card title="Validación Orden de Compra Servicio">
    <div class="content content-pagex">
        <form>
            <div style="margin: 15px" class="flex flex-wrap">
                <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">
                    <div class="flex flex-wrap">
                        <div style="margin-left:10px;margin-right:20px" class="xs:w-full md:w-5/6 lg:w-5/6 xl:w-5/6">
                            <ValidationProvider name="Agencia">
                                <label class="typo__label">Departamentos</label>
                                <multiselect v-model="cbDepartamento" :options="ListaDepartamentos" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Departamento_seleccionada" placeholder="Seleccionar" @input="onChangeDepartamento">
                                    <span slot="noOptions">Lista no disponible.</span>
                                </multiselect>
                            </ValidationProvider>
                        </div>
                    </div>
                </div>

                <div class="xs:w-full md:w-8/12 lg:w-8/12 xl:w-8/12">
                    <div class="flex flex-wrap">
                        <div class="xs:w-full md:w-5/6 lg:w-5/6 xl:w-5/12">
                            <label class="typo__label">Estado Orden de Compra</label>
                            <multiselect v-model="cb_lista_operacion" :options="lista_estado" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Operacion_seleccionada" placeholder="Seleccionar" @input="onChangeEstado">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </div>

                        <div style="margin-left:10px;margin-right:20px" class="xs:w-full md:w-2/6 lg:w-2/6 xl:w-2/6">
                            <label class="typo__label">Fecha Inicio:</label>
                            <vs-input type="date" v-model="fecha_inicio" name="date1" />
                        </div>
                        <div class="xs:w-full md:w-3/6 lg:w-3/6 xl:w3/6">
                            <label class="typo__label">Fecha Final:</label>
                            <vs-input type="date" v-model="fecha_final" name="date1" />
                        </div>
                        <div class=" md:w-1/6 lg:w-1/6 xl:w-1/6">
                            <vs-button style="float:left;margin: 20px" color="primary" type="filled" icon-pack="fas" icon="fa-search" @click="Consulta_autorizaciones()"> Búsqueda</vs-button>
                        </div>

                    </div>
                </div>
            </div>
            <div style="margin: 15px" class="flex flex-wrap">
                <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">
                    <div class="flex flex-wrap">
                        <div style="margin-left:10px;margin-right:20px" class="xs:w-full md:w-5/6 lg:w-5/6 xl:w-5/6">
                            <ValidationProvider name="selectProv">
                                <label class="typo__label">Proveedor</label>
                                <multiselect v-model="cbProveedor" :options="listaProveedores" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="seleccion_proveedor" placeholder="Búsqueda" @input="onChangeProveedor">
                                    <span slot="noOptions">Lista no disponible.</span>
                                </multiselect>
                            </ValidationProvider>
                        </div>

                    </div>

                </div>

                <div class="xs:w-full md:w-8/12 lg:w-8/12 xl:w-8/12">
                    <div class="flex flex-wrap">

                        <div class="xs:w-full md:w-5/6 lg:w-5/6 xl:w-5/12">
                            <label class="typo__label">Estado de Validación</label>
                            <multiselect v-model="cb_lista_estadovf" :options="lista_estadovf" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="EstadoValidacionf" placeholder="Seleccionar" @input="onChangeValidacionf">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </div>
                        <div style="margin-left:10px;margin-right:20px">
                            <ValidationProvider name="diasCredito" rules="required|numero_min:0|numero_entero" v-slot="{ errors }">
                                <label class="typo__label">De Orden No.</label>
                                <vs-input type="number" v-model="deOrden" :danger="errors.length>0" />
                            </ValidationProvider>
                        </div>

                        <div class="xs:w-full md:w-3/6 lg:w-3/6 xl:w-3/6">
                            <ValidationProvider name="pagos" rules="required|numero_min:0|numero_entero" v-slot="{ errors }">
                                <label class="typo__label">A Orden No.</label>
                                <vs-input type="number" v-model="aOrden" :danger="errors.length>0" />
                            </ValidationProvider>
                        </div>
                        <div class=" md:w-1/6 lg:w-1/6 xl:w-1/6">
                            <div class="w-full" style="float:left;margin: 10px">
                                <vs-radio v-model="idTipoSeleccionado" vs-name="idTipoSeleccionado" vs-value="S"> Servicio</vs-radio>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <label class="label-sizem">Si alguno de los filtros no es seleccionado, el resultado será obtener toda la información registrada. </label>
            <br>
            <vs-divider></vs-divider>
            <div class="flex flex-wrap">
                <div class="xs:w-full" align="left">
                    <vs-button style="float:right;margin: 20px" color="success" type="filled" icon-pack="fas" icon="fa-check-circle" @click="ReporteOrdenesValidadasCF() "> Ordenes Validadas C/Factura</vs-button>
                    <vs-button style="float:right;margin: 20px" color="success" type="filled" icon-pack="fas" icon="fa-file-excel " :disabled = "habilitarRpt"  @click="ReporteConsolidado()"> Consolidado Excel</vs-button>
                </div>
            </div>

        </form>

        <!---------------------- DETALLE SOLICITUDES PENDIENTES --------------->
        <vs-divider></vs-divider>
        <vs-table2 tooltip max-items="10" pagination :data="Lista_autorizaciones" noDataText="Sin datos disponibles" search id="tb_departamentos">

            <template slot="thead">
                <th width="80">Nº. Orden Compra </th>
                <th width="80">Nº. Solicitud </th>
                <th width="500">Departamento</th>
                <th width="130">Tipo</th>
                <th width="400">Proveedor</th>
                <th width="170">Total Orden Compra (Monto)</th>
                <th width="150">Fecha</th>
                <th width="400">Observación Orden de Compra</th>
                <th width="100">No. Validación</th>
                <th width="100">Usuario de Validación</th>
                <th width="200">Observación Validación</th>
                <th width="200">Estado de Validación</th>
                <th width="200">Estado Orden de Compra</th>
                <th width="100">Validar / Anular Validación</th>
                <th width="120">Ver Orden de Compra </th>
            </template>

            <template slot-scope="{data}">
                <tr :key="indextr" v-for="(tr, indextr) in data">
                    <vs-td2 noTooltip :data="data[indextr].IDORDENCOMPRAENC">
                        {{data[indextr].IDORDENCOMPRAENC}}
                    </vs-td2>

                    <vs-td2 noTooltip :data="data[indextr].IDSOLICITUDENCFK">
                        {{data[indextr].IDSOLICITUDENCFK}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].IDDEPARTAMENTOFK">
                        {{data[indextr].IDDEPARTAMENTOFK}}
                        -
                        {{data[indextr].DESCRIPCION_DEPTO}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].IDPRODUCTOCLASEFK">
                        {{data[indextr].IDPRODUCTOCLASEFK}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].DESCRIPCION_PROVEEDOR">
                        {{data[indextr].DESCRIPCION_PROVEEDOR}}
                    </vs-td2>
                    <vs-td2 align="right" :data="data[indextr].TOTAL">
                        Q. {{data[indextr].TOTAL}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].FECHA_CREACION_ORDEN">
                        {{data[indextr].FECHA_CREACION_ORDEN}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].OBSERVACIONES">
                        {{data[indextr].OBSERVACIONES}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].VALIDACION">
                        {{data[indextr].VALIDACION}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].USUARIO">
                        {{data[indextr].USUARIO}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].OBSERVACION_VALIDACION">
                        {{data[indextr].OBSERVACION_VALIDACION}}
                    </vs-td2>
                    <vs-td2 style="text-align: center;" :data="data[indextr].VALIDADA">
                        <label v-if="data[indextr].VALIDADA == 'S'" style=" height: 30px;background-color:green;width:120px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIP_VALIDADA}} </label>
                        <label v-if="data[indextr].VALIDADA == 'N'" style="height: 30px;background-color:#D9614D;width:120px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIP_VALIDADA}} </label>
                    </vs-td2>

                    <vs-td2 style="text-align: center;" :data="data[indextr].ESTADO">
                        <label v-if="data[indextr].ESTADO == 'P'" style="height: 30px;background-color:#B2BABB;width:120px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                        <label v-if="data[indextr].ESTADO == 'A'" style=" height: 30px;background-color:green;width:120px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                        <label v-if="data[indextr].ESTADO == 'R'" style="height: 30px;background-color:#D9614D;width:120px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                        <label v-if="data[indextr].ESTADO == 'C'" style="height: 30px;background-color:#e6cb32;width:120px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>

                    </vs-td2>

                    <vs-td2 noTooltip align="center">
                        <vs-button v-if="data[indextr].ESTADO == 'P' ||  data[indextr].ESTADO === 'R' || data[indextr].ESTADO == 'C'" disabled="true" color="primary" icon-pack="fas" icon="fa-check-double" style="display:inline-block;margin-right:2px" @click="Finalizar_Validacion(data[indextr])"></vs-button>
                        <vs-button v-else color="primary" icon-pack="fas" icon="fa-check-double" style="display:inline-block;margin-right:2px" @click="Finalizar_Validacion(data[indextr])"></vs-button>
                    </vs-td2>
                    <vs-td2 noTooltip align="center">
                        <vs-button color="success" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-download" @click="OrdenCompraRpt(data[indextr])"></vs-button>
                    </vs-td2>

                </tr>
            </template>
        </vs-table2>

        <!--------------- EMERGENTE AUTORIZACION -------------->
        <vs-popup classContent="popup-example" title="Finalizar Validación" :active.sync="Estado_VentanaEmergente_Auto" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
                <form>

                    <div class="flex flex-wrap">
                        <br>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">

                            <label class="label-sizem">Orden de compra Seleccionada: </label>
                            <label class="label-sizem"> {{this.Datos_autorizacion_seleccionado.IDORDENCOMPRAENC}}</label>
                        </div>
                        <br>
                        <!---  AUTORIZAR / RECHAZAR ORDEN-->
                        <div v-show="this.Datos_autorizacion_seleccionado.VALIDADA  == 'N'" class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                            <label class="typo__label">Estado a definir: </label>
                            <multiselect v-model="cb_lista_estado_validacion" :options="Lista_estado_validarS" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="validacion_seleccionada" placeholder="Seleccionar" @input="onChangeEstadoSolicitud">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </div>
                        <div v-show="this.Datos_autorizacion_seleccionado.VALIDADA  == 'S'" class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                            <label class="typo__label">Estado a definir: </label>
                            <multiselect v-model="cb_lista_estado_validacion" :options="Lista_estado_validarAN" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="validacion_seleccionada" placeholder="Seleccionar" @input="onChangeEstadoSolicitud">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </div>

                    </div>
                    <!---- ENVIAR CORREO ELECTRONICO-->
                    <br>
                    <div class="flex flex-wrap">
                    </div>

                    <!---------- OBSERVACIONES ------------>
                    <div class="w-full md:w-full lg:w-full xl:w-full">
                        <label class="typo__label">Observaciones</label>
                        <div class="vs-con-input">
                            <input type="text" class="vs-inputx vs-input--input large" cols="40" rows="5" v-model="Observaciones">
                        </div>
                    </div>
                    <vs-divider></vs-divider>

                    <div class="w-full md:w-full lg:w-full xl:w-full">
                        <vs-button color="primary" style="float:right" type="filled" icon-pack="feather" icon="icon-save" @click="Confirmacion_Transaccion()"> Grabar</vs-button>

                    </div>
                    <br>
                    <br>
                    <vs-divider></vs-divider>

                    <vs-table2 tooltip max-items="1" pagination :data="Lista_encabezado" noDataText="Sin datos disponibles">

                        <template slot="thead">
                            <th width="80">Nº. Orden Compra </th>
                            <th width="500">Proveedor</th>
                            <th width="170">Total Orden Compra (Monto)</th>
                            <th width="100">Días Crédito</th>
                            <th width="100">No. Pagos</th>
                            <th width="150">Fecha Autorización</th>
                            <th width="200">Estado Orden de Compra</th>
                            <th width="300">Usuario Autorizó</th>

                        </template>

                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 noTooltip :data="data[indextr].IDORDENCOMPRAENC">
                                    {{data[indextr].IDORDENCOMPRAENC}}
                                </vs-td2>
                                <vs-td2 :data="data[indextr].PROVEEDOR">
                                    {{data[indextr].PROVEEDOR}}
                                </vs-td2>
                                <vs-td2 align="right" :data="data[indextr].TOTAL">
                                    Q. {{data[indextr].TOTAL}}
                                </vs-td2>
                                <vs-td2 align="right" :data="data[indextr].DIAS_CREDITO">
                                    {{data[indextr].DIAS_CREDITO}}
                                </vs-td2>
                                <vs-td2 align="right" :data="data[indextr].PAGOS">
                                    {{data[indextr].PAGOS}}
                                </vs-td2>
                                <vs-td2 :data="data[indextr].FECHA_AUTORIZACIÓN">
                                    {{data[indextr].FECHA_AUTORIZACIÓN}}
                                </vs-td2>
                                <vs-td2 style="text-align: center;" :data="data[indextr].ESTADO1">
                                    <label v-if="data[indextr].ESTADO1 == 'P'" style="height: 30px;background-color:#B2BABB;width:120px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> PENDIENTE </label>
                                    <label v-if="data[indextr].ESTADO1== 'A'" style=" height: 30px;background-color:green;width:120px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> AUTORIZADA </label>
                                    <label v-if="data[indextr].ESTADO1 == 'R'" style="height: 30px;background-color:#C82B0A;width:120px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> RECHAZADA </label>
                                </vs-td2>
                                <vs-td2 :data="data[indextr].RESPONSABLE_1">
                                    {{data[indextr].RESPONSABLE_1}}
                                </vs-td2>

                            </tr>
                        </template>
                    </vs-table2>

                    <vs-divider></vs-divider>

                </form>
            </div>
        </vs-popup>

    </div>
</vx-card>
</template>

<script>
import Multiselect from 'vue-multiselect'
import "vue-multiselect/dist/vue-multiselect.min.css"

import moment from "moment";
export default {

    components: {
        Multiselect,
    },
    data() {
        return {

            Estado_VentanaEmergente_Auto: false,
            cb_lista_operacion: '',
            fecha_inicio: this.getDateValue(new Date()),
            fecha_final: this.getDateValue(new Date()),
            Id_estado_seleccionado: '',
            lista_estado: [{
                    ID: 'P',
                    DESCRIPCION: 'Proceso'
                },
                {
                    ID: 'A',
                    DESCRIPCION: 'Autorizado'
                },
                {
                    ID: 'R',
                    DESCRIPCION: 'Rechazado'
                },
                {
                    ID: 'AN',
                    DESCRIPCION: 'Anulado'
                }
            ],
            cb_lista_estadovf: '',
            Id_estado_validacionf: '',
            lista_estadovf: [{
                    ID: 'S',
                    DESCRIPCION: 'Validada'
                },
                {
                    ID: 'N',
                    DESCRIPCION: 'No Validada'
                }
            ],

            cb_lista_estado_validacion: '',
            Id_estado_validacion: '',
            Lista_estado_validarS: [{
                ID: 'S',
                DESCRIPCION: 'Validar'
            }],
            Lista_estado_validarAN: [{
                ID: 'N',
                DESCRIPCION: 'Anular Validación'
            }],
            Observaciones: '',
            Lista_autorizaciones: [],
            Lista_encabezado: [],
            Datos_autorizacion_seleccionado: [],
            reporte: {
                generarPDF: true,
                generarExcel: true,
                generar: false,
                popup: false,
                buscar: '',
                titulo: '',
                url: '',
                url_pdf: '',
                opciones: [],
                pdf: '',
                buscador: null
            },
            datosResultado: {
                IdOrdenCompra: '',
            },
            customToolbar: [
                [{
                    'header': [1, 2, 3, 4, 5, 6, false]
                }],
                [{
                    'font': []
                }],
                ['bold', 'italic', 'underline', 'strike'],
                [{
                    list: "ordered"
                }, {
                    list: "bullet"
                }],
                [{
                    'indent': '-1'
                }, {
                    'indent': '+1'
                }],
                [{
                    'color': []
                }, {
                    'background': []
                }],
            ],
            ListaDepartamentos: [],
            cbDepartamento: '',
            IdDepartamento: '',
            Departamento: '',
            deOrden: 0,
            aOrden: 0,

            listaProveedores: [],
            cbProveedor: '',
            proveedor: {
                codigo: '',
                nombre: '',
                nit: ''
            },
            idTipoSeleccionado: 'S',
            listado_reportes: [],
            listado_reportes1: [],
            listado_reportes2: [],
            habilitarRpt: true,
            permisos_departamentos: []

        };
    },

    mounted() {
        //Verifica los deparamentos a los que se tienen permisos para validar 
        for(let privilegio of this.$store.state.privilegios){
               if(privilegio.Privilegio.includes("DEPARTAMENTO")){  
                let departamento   = privilegio.Privilegio.split('_')[1]       
                this.permisos_departamentos.push(departamento)                                                                       
                }
            }
    
        this.ConsultarDepartamentos(this.permisos_departamentos.join(","));
        this.Consultar_proveedor();
        this.Consulta_autorizaciones();

        this.listado_reportes = this.$recupera_parametros_reporte('Orden de Compra');
        this.listado_reportes1 = this.$recupera_parametros_reporte('Ordenes Validadas');
        this.listado_reportes2 = this.$recupera_parametros_reporte('Consolidado');

    },
    methods: {

        getDateValue(value) {
            if (value === undefined) {
                return '';
            } else if (value === null) {
                return '';
            } else if (value == '') {
                return '';
            }
            return moment(value).format('YYYY-MM-DD');
        },
        ConsultarDepartamentos(departamentos) {

            this.axios.post('/app/v1_OrdenCompra/consulta_departamento', {
                tipo_consulta: 'P',
                departamentos_Permisos: departamentos
            })
                .then(resp => {
                    if (resp.data.codigo != 0) {

                        this.ListaDepartamentos = "";
                    } else {
                        this.ListaDepartamentos = resp.data.json;
                    }
                })
                .catch(() => {})
        },
        onChangeDepartamento(value) {

            if (value !== null && value.length !== 0) {
                this.IdDepartamento = value.CODIGO;
                this.Departamento = value.NOMBRE;
            } else {
                this.IdDepartamento = '';
                this.Departamento = '';
            }
        },
        Departamento_seleccionada({
            CODIGO,
            NOMBRE
        }) {
            return `${CODIGO} - ${NOMBRE} `
        },
        Consultar_proveedor() {
            const url = this.$store.state.global.url
            const sesion = this.$store.state.sesion;
            this.axios.post(url + 'app/v1_OrdenCompra/consulta_proveedor', {
                    empresa: sesion.sesion_empresa
                })
                .then(resp => {

                    if (resp.data.codigo != 0) {

                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Proveedores',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.listaProveedores = "";
                    } else {
                        //Decodificación
                        this.listaProveedores = resp.data.json;

                    }
                })
                .catch(() => {})
        },
        seleccion_proveedor({
            NIT,
            NOMBRE,
        }) {
            return `${NIT} - ${NOMBRE} `
        },
        onChangeProveedor(value) {
            if (value !== null && value.length !== 0) {

                this.proveedor.codigo = value.CODIGO,
                    this.proveedor.nombre = value.NOMBRE.toUpperCase();
                this.proveedor.nit = value.NIT;

            } else {
                this.proveedor.codigo = '';
                this.proveedor.nombre = '';
                this.proveedor.nit = '';
            }
        },
        Operacion_seleccionada({
            DESCRIPCION
        }) {
            return `${DESCRIPCION} `
        },
        onChangeEstado(value) {

            if (value !== null && value.length !== 0) {
                this.Id_estado_seleccionado = value.ID;
            } else {
                this.Id_estado_seleccionado = '';

            }
        },
        validacion_seleccionada({
            DESCRIPCION
        }) {
            return `${DESCRIPCION} `
        },
        onChangeEstadoSolicitud(value) {
            if (value !== null && value.length !== 0) {
                this.Id_estado_validacion = value.ID;
                ''
            } else {

                this.Id_estado_validacion = '';
            }
        },
        Consulta_autorizaciones() {

            const url = this.$store.state.global.url;
            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/ConsultaOCValidarAnular', {

                    estado: this.Id_estado_seleccionado,
                    fecha_inicio: this.fecha_inicio,
                    fecha_fin: this.fecha_final,
                    Departamento: this.IdDepartamento,
                    Proveedor: this.proveedor.codigo,
                    Recepcionada: this.Id_estado_validacionf,
                    DeOrden: this.deOrden,
                    AOrden: this.aOrden,
                    TipoSeleccionado: this.idTipoSeleccionado,
                    ValidarAnular: 'A',
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Validación Orden Compra',
                            text: resp.data.mensaje,
                        })

                        this.Lista_autorizaciones = [];
                        this.habilitarRpt = true
                    } else {
                        this.Lista_autorizaciones = [];
                        this.Lista_autorizaciones = resp.data.json.map(m => {
                            return {
                                ...m,
                                TOTAL: parseFloat(m.TOTAL).toFixed(2)
                            }
                        })
                        this.habilitarRpt = false
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },
        OrdenCompraRpt(datos) {

            this.$reporte_modal({
                    Nombre: "Orden de Compra",
                    Opciones: {
                        IdOrdenCompra: datos.IDORDENCOMPRAENC
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },
        ReporteOrdenesValidadasCF() {

            this.$reporte_modal({
                    Nombre: "Ordenes Validadas",
                    Opciones: {
                        finicial: this.fecha_inicio,
                        ffinal: this.fecha_final,
                        departamento: this.IdDepartamento,
                        ordenD: this.deOrden,
                        ordenA: this.aOrden,
                    },
                    Formato: "EXCEL"
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },

        ReporteConsolidado() {

            this.$reporte_modal({
                    Nombre: "Consolidado",
                    Opciones: {
                        estado: this.Id_estado_seleccionado,
                        finicial: this.fecha_inicio,
                        ffinal: this.fecha_final,
                        departamento: this.IdDepartamento,
                        ordenD: this.deOrden,
                        ordenA: this.aOrden,
                        proveedor: this.proveedor.codigo,
                        recepcionada: this.Id_estado_validacionf,
                        tipo: this.idTipoSeleccionado,
                        opcion: '2'
                    },
                    Formato: "EXCEL"
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },

        EstadoValidacionf({
            DESCRIPCION
        }) {
            return `${DESCRIPCION} `
        },
        onChangeValidacionf(value) {

            if (value !== null && value.length !== 0) {
                this.Id_estado_validacionf = value.ID;
            } else {
                this.Id_estado_validacionf = '';

            }
        },
        Finalizar_Validacion(datos) {

            this.datosResultado.IdOrdenCompra = datos.IDORDENCOMPRAENC;
            this.Id_estado_validacionf = '';

            if (datos.VALIDADA == 'S') {
                this.cb_lista_estado_validacion = {
                    DESCRIPCION: 'Anular Validación'
                };
                this.Id_estado_validacion = 'N'
            }
            else {
                this.cb_lista_estado_validacion = {
                    DESCRIPCION: 'Validar'
                };
                this.Id_estado_validacion = 'S'
            }
            this.Observaciones = '';
            this.Datos_autorizacion_seleccionado = [];

            if (datos.ESTADO == 'A') {
                this.Datos_autorizacion_seleccionado = datos;
                this.Estado_VentanaEmergente_Auto = true;
                this.Consultar_encabezado(datos)
            } else {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Validación Orden Compra',
                    text: 'Pendiente de Autorización',
                })
            }
        },
        Consultar_encabezado(datos) {

            const url = this.$store.state.global.url
            this.Lista_encabezado = [];
            this.OrdenCompraId = datos.IDORDENCOMPRAENC
            this.axios.post(url + 'app/v1_OrdenCompra/Consulta_eOrdenCompraManual', {
                    IdOrdenCompraEnc: this.OrdenCompraId
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Orden de Compra',
                            text: resp.data.mensaje,
                        })
                    } else {
                        this.Lista_encabezado = resp.data.json;
                        this.Ventana_Detalle = true
                    }
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },
        Confirmacion_Transaccion() {

            if (this.Id_estado_validacion == '' || this.Id_estado_validacion == null) {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Validación Orden Compra',
                    text: 'Seleccionar Estado a Definir',
                })
                return;
            }
            const sesion = this.$store.state.sesion
            this.Corporativo_Sesion = sesion.corporativo
            this.axios.post('/app/v1_OrdenCompra/ConfirmarValidacion', {

                    estado: this.Datos_autorizacion_seleccionado.ESTADO,
                    Documento: this.Datos_autorizacion_seleccionado.ID_DOCUMENTO_,
                    Observaciones: this.Observaciones,
                    IdOrdenCompraEnc: this.Datos_autorizacion_seleccionado.IDORDENCOMPRAENC,
                    TipoSeleccionado: this.Datos_autorizacion_seleccionado.IDPRODUCTOCLASEFK,
                    estadoV: this.Id_estado_validacion
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {

                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Validación Orden Compra',
                            text: resp.data.mensaje,
                        })

                    } else {
                        this.Estado_VentanaEmergente_Auto = false;
                        this.Consulta_autorizaciones();

                    }
                    this.Estado_VentanaEmergente_Auto = false;

                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();

        },

        async beforeCreate() {
            this.listado_reportes = await this.$recupera_parametros_reporte('Orden de Compra')
            this.listado_reportes1 = await this.$recupera_parametros_reporte('Ordenes Validadas')
            this.listado_reportes2 = await this.$recupera_parametros_reporte('Consolidado')

        },
    },
    async beforeCreate() {
        this.listado_reportes = await this.$recupera_parametros_reporte('Orden de Compra')
        this.listado_reportes1 = await this.$recupera_parametros_reporte('Ordenes Validadas')
        this.listado_reportes2 = await this.$recupera_parametros_reporte('Consolidado')
    }

}
</script>

<style scoped>
.footer {
    height: 50px;
    margin-top: -65px;
    margin-left: 10px;
}

.center {
    border: 5px solid;
    margin: auto;
    width: 50%;
    padding: 10px;
}

.label-size {
    font-size: 18px;
    font-weight: bold;
}

.label-sizem {
    font-size: 16px;
    font-weight: bold;
}
</style>
