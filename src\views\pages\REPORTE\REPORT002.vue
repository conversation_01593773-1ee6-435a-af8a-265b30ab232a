<template>
<vx-card title="Administración de Reporte">

    <Admin ref="admin" :infoTemp="info" :parametrosTemp="parametros" :callback="listado_reporte" />

    <vs-popup :active.sync="importar.mostrar" title="Importar">
        <textarea name="" id="" cols="30" rows="10" style="width:100%;height:400px;resize: none;" v-model="importar.json"></textarea>
        <div class="flex w-full">
            <vs-spacer></vs-spacer>
            <vs-button color="primary" @click="importarReporteAceptar()">Aceptar</vs-button>
        </div>
    </vs-popup>

    <div class="flex flex-wrap">
        <div class="w-full">
            <vs-button color="primary" @click="importarReporte()">Importar</vs-button>
            <vs-button color="primary" style="float:right" v-on:click="nuevoEditar(true)">Nuevo Reporte</vs-button>
        </div>
    </div>

    <vs-divider>Reportes</vs-divider>
    <vs-table2 search :data="listado_reportes">
        <template slot="thead">
            <th>Aplicativo</th>
            <th>Nombre</th>
            <th>Url</th>
            <th width="180px">Acciones</th>
        </template>
        <template slot-scope="{data}">
            <tr :key="indextr" v-for="(tr, indextr) in data">
                <vs-td2 :data="tr.Aplicativo">
                    {{ tr.Aplicativo }}
                    <div>
                        <small>{{tr.Funcionalidad}}</small>
                    </div>
                </vs-td2>
                <vs-td2 :data="tr.Nombre">{{tr.Nombre}}</vs-td2>
                <vs-td2 :data="tr.UrlApi">{{tr.UrlApi}}</vs-td2>

                <vs-td2>
                    <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" @click.native="nuevoEditar(false,tr)"></vs-button>
                    <vs-button color="danger" style="margin-left:1px;display:inline-block;" @click.native="eliminar_reporte(tr.IdReporte)" icon-pack="fas" icon="fa-trash"></vs-button>
                    <vs-button color="warning" style="margin-left:1px;display:inline-block;" @click.native="info_reporte(tr)" icon-pack="fas" icon="fa-info"></vs-button>
                </vs-td2>
            </tr>
        </template>
    </vs-table2>
</vx-card>
</template>

<script>
export default {
    data() {
        return {
            reporte_editar: false,

            listado_aplicativo: [],
            listado_reportes: [],

            listado_tipologia: [
                'Texto',
                'Numero',
                'Fecha',
                'Fecha/Hora',
                'Checkbox',
                'Oculto'
            ],

            info: {
                IdReporte: null,
                nombre: null,
                url: null,
                idAplicativo: 0,
                Aplicativo: "--",
                idFuncionalidad: 0,
                Funcionalidad: "--",
                listado_parametros: [],
                VisibilidadReporte: true
            },

            parametros: {
                Index: null,
                Etiqueta: null,
                Parametro: null,
                Tipo: null,
                Eval: null,
                MensajeError: null,
                Obligatorio: false,
                ValorDefecto: null
            },

            importar: {
                mostrar: false,
                json: null
            }

        };
    },
    components: {
        Admin: () => import('./REPORT002_Admin.vue')
    },

    methods: {
        listado_reporte() {
            this.axios.post("/app/reporte/ReporteListado", {}).then(resp => {
                this.listado_reportes = resp.data.json;
            });
        },

        buscar_funcionalidad() {
            this.$refs.buscar_funcionalidad.iniciar(data => {
                if (data != null) {
                    this.info.idFuncionalidad = data.IdAplicativo;
                    this.info.Funcionalidad = data.Nombre;
                }
            });
        },

        importarReporte() {
            this.importar.mostrar = true
        },
        importarReporteAceptar() {
            const j = JSON.parse(this.importar.json)
            Object.entries(this.info).forEach(([key]) => {
                this.info[key] = j[key]
                
            })
            this.$refs['admin']._data.reporte_nuevoEditar = true
            this.importar.mostrar = false
            this.importar.json = null
        },

        nuevoEditar(nuevo, data) {
            // this.reporte_nuevoEditar = true;
            this.reporte_editar = !nuevo;

            this.parametros.Index = null
            this.parametros.Etiqueta = null
            this.parametros.Parametro = null
            this.parametros.Tipo = null
            this.parametros.Eval = null
            this.parametros.MensajeError = null
            this.parametros.Obligatorio = false
            this.parametros.ValorDefecto = null

            if (nuevo) {
                this.info.IdReporte = null;
                this.info.nombre = "";
                this.info.url = "";
                this.info.idAplicativo = 0;
                this.info.Aplicativo = "--";
                this.info.idFuncionalidad = 0;
                this.info.Funcionalidad = "--";
                this.info.listado_parametros = [];
                this.info.VisibilidadReporte = true;
            } else {
                this.info.IdReporte = data.IdReporte;
                this.info.nombre = data.Nombre;
                this.info.VisibilidadReporte = data.Visible == 0? false: true;
                this.info.url = data.UrlApi;
                this.info.idAplicativo = data.IdAplicativo;
                this.info.Aplicativo = data.Aplicativo;
                this.info.idFuncionalidad = data.IdFuncionalidad;
                this.info.Funcionalidad = (data.Funcionalidad != "") ? data.Funcionalidad : "--";
                this.info.listado_parametros = [...data._level]

            }

            this.$refs['admin']._data.reporte_nuevoEditar = true
        },

        eliminar_reporte(IdReporte) {
            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Reportes',
                text: '¿Desea eliminar el reporte?',
                accept: () => {
                    this.axios
                        .post("/app/reporte/ReporteEliminar", {
                            IdReporte: IdReporte
                        })
                        .then(() => {
                            this.listado_reporte();
                        });
                }
            })
        },

        info_reporte(reporte) {
            this.axios.post("/app/reporte/ReporteGenerador",{
                Nombre:reporte.Nombre
            })
                .then(resp => {
                    this.$vs.dialog({
                        color: 'success',
                        title: 'Reportes',
                        text: resp.data,
                    });
                })
                .catch(err => {
                    this.$vs.dialog({
                        color: 'danger',
                        title: 'Reportes',
                        text: err,
                    });
                })

        },
    },
    created() {
        this.listado_reporte();
    }
};
</script>

<style>
.button {
    margin: 0px !important;
}

.btnx {
    margin-left: 10px !important;
    background: red;
    border-radius: 5px 0px 0px 5px;
}

.btn-drop {
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: red;
}

.popup-generar {
    height: 100%;
}
</style>
