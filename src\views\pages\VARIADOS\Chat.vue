<!-- =========================================================================================
    File Name: Chat.vue
    Description: Chat Application - Stay connected
    ----------------------------------------------------------------------------------------
    Item Name: Vuexy - Vuejs, HTML & Laravel Admin Dashboard Template
      Author: Pixinvent
    Author URL: http://www.themeforest.net/user/pixinvent
========================================================================================== -->


<template>
<div id="chat-app" class="border border-solid d-theme-border-grey-light rounded relative overflow-hidden">

  <vs-popup classContent="popup-example" title="Lorem ipsum dolor sit amet" :active.sync="popupActive2">
    <vs-input class="inputx mb-3" placeholder="Placeholder" v-model="user" />

    <vs-button @click="unirSala()" color="primary" type="filled">Unirse</vs-button>

  </vs-popup>


  <vs-sidebar class="items-no-padding" parent="#chat-app" :click-not-close="clickNotClose" :hidden-background="clickNotClose" v-model="isChatSidebarActive" id="chat-list-sidebar">
    <!-- USER PROFILE SIDEBAR -->
    <user-profile :active="activeProfileSidebar" :userId="userProfileId" class="user-profile-container" :isLoggedInUser="isLoggedInUserProfileView" @closeProfileSidebar="closeProfileSidebar"></user-profile>

    <div class="chat__profile-search flex p-4">
      <div class="relative inline-flex">
        <vs-avatar v-if="activeUser.photoURL" class="m-0 border-2 border-solid border-white" :src="activeUser.photoURL" size="40px" @click="showProfileSidebar(Number(activeUser.uid), true)" />
        <div class="h-3 w-3 border-white border border-solid rounded-full absolute right-0 bottom-0" :class="'bg-' + getStatusColor(true)"></div>
      </div>
      <vs-input icon-no-border icon="icon-search" icon-pack="feather" class="w-full mx-5 input-rounded-full" placeholder="Search or start a new chat" v-model="searchQuery" />

      <feather-icon class="md:inline-flex lg:hidden -ml-3 cursor-pointer" icon="XIcon" @click="toggleChatSidebar(false)" />
    </div>

    <vs-divider class="d-theme-border-grey-light m-0" />
    <component :is="scrollbarTag" class="chat-scroll-area pt-4" :settings="settings" :key="$vs.rtl">

      <!-- ACTIVE CHATS LIST -->
      <div class="chat__chats-list mb-8">
        <h3 class="text-primary mb-5 px-4">Chats</h3>
        <ul class="chat__active-chats bordered-items">
          <li class="cursor-pointer" v-for="(contact, index) in chatContacts" :key="index" @click="updateActiveChatUser(contact.uid)">
            <chat-contact showLastMsg :contact="contact" :lastMessaged="''" :unseenMsg="chatUnseenMessages(contact.uid)" :isActiveChatUser="isActiveChatUser(contact.uid)"></chat-contact>
          </li>
        </ul>
      </div>


      <!-- CONTACTS LIST -->
      <div class="chat__contacts">
        <h3 class="text-primary mb-5 px-4">Contacts</h3>
        <ul class="chat__contacts bordered-items">
          <li class="cursor-pointer" v-for="contact in contacts" :key="contact.uid" @click="updateActiveChatUser(contact.uid)">
            <chat-contact :contact="contact"></chat-contact>
          </li>
        </ul>
      </div>
    </component>
  </vs-sidebar>

  <!-- RIGHT COLUMN -->
  <div class="chat__bg no-scroll-content chat-content-area border border-solid d-theme-border-grey-light border-t-0 border-r-0 border-b-0" :class="{'sidebar-spacer--wide': clickNotClose, 'flex items-center justify-center': activeChatUser === null}">
    <template v-if="activeChatUser">
      <div class="chat__navbar">
        <chat-navbar :isSidebarCollapsed="!clickNotClose" :user-id="activeChatUser" :isPinnedProp="false" @openContactsSidebar="toggleChatSidebar(true)" @showProfileSidebar="showProfileSidebar" @toggleIsChatPinned="toggleIsChatPinned">
        </chat-navbar>
      </div>
      <component :is="scrollbarTag" class="chat-content-scroll-area border border-solid d-theme-border-grey-light" :settings="settings" ref="chatLogPS" :key="$vs.rtl">
        <div class="chat__log" ref="chatLog">
          <chat-log :userId="activeChatUser" :chatData="chatData" v-if="activeChatUser"></chat-log>
        </div>
      </component>
      <div class="chat__input flex p-4 bg-white">
        <vs-input class="flex-1" placeholder="Type Your Message" v-model="typedMessage" @keyup.enter="sendMsg" />
        <vs-button class="bg-primary-gradient ml-4" type="filled" @click="sendMsg">Send</vs-button>
      </div>
    </template>
    <template v-else>
      <div class="flex flex-col items-center" v-if="user==null">
        <feather-icon icon="MessageSquareIcon" class="mb-4 bg-white p-8 shadow-md rounded-full" svgClasses="w-16 h-16"></feather-icon>
        <h4 class=" py-2 px-4 bg-white shadow-md rounded-full cursor-pointer" @click.stop="popupActive2=true">Start Conversation</h4>
      </div>
    </template>
  </div>
</div>
</template>

<script>
import ChatContact from "./ChatContact.vue"
import ChatLog from './ChatLog.vue'
import ChatNavbar from './ChatNavbar.vue'
import UserProfile from "./UserProfile.vue"
import VuePerfectScrollbar from 'vue-perfect-scrollbar'

export default {
  data() {
    return {
      usuario: null,

      popupActive2: false,
      user: null,
      contacts: [],
      chatData: {
        msg: [],
      },
      searchQuery: '',
      active: true,
      isHidden: false,
      searchContact: "",
      activeProfileSidebar: false,
      activeChatUser: null,
      userProfileId: -1,
      typedMessage: "",
      isChatPinned: false,
      settings: {
        maxScrollbarLength: 60,
        wheelSpeed: 0.70,
      },
      clickNotClose: true,
      isChatSidebarActive: true,
      isLoggedInUserProfileView: false,
    }
  },
  watch: {
    windowWidth() {
      this.setSidebarWidth()
    }
  },
  computed: {

    chatUnseenMessages() {
      return () => {
        const unseenMsg = false
        if (unseenMsg) return unseenMsg
      }
    },
    activeUser() {
      return this.$store.state.AppActiveUser
    },
    getStatusColor() {
      return (isActiveUser) => {
        const userStatus = this.getUserStatus(isActiveUser)

        if (userStatus == "online") {
          return "success"
        } else if (userStatus == "do not disturb") {
          return "danger"
        } else if (userStatus == "away") {
          return "warning"
        } else {
          return "grey"
        }
      }
    },
    chatContacts() {
      const filteredContacts = this.contacts.filter((contact) => {
        return contact.displayName.toLowerCase() //.includes(request.params.q.toLowerCase())
      })
      return filteredContacts
    },
    // searchQuery: {
    //   get() {
    //     return this.$store.state.chat.chatSearchQuery
    //   },
    //   set(val) {
    //     this.$store.dispatch('chat/setChatSearchQuery', val)
    //   }
    // },
    scrollbarTag() {
      return this.$store.getters.scrollbarTag
    },
    isActiveChatUser() {
      return (userId) => userId == this.activeChatUser
    },
    windowWidth() {
      return this.$store.state.windowWidth
    }
  },
  methods: {
    getUserStatus(isActiveUser) {
      // return "active"
      return (isActiveUser) ? this.$store.state.AppActiveUser.status : this.contacts[this.activeChatUser].status
    },
    closeProfileSidebar(value) {
      this.activeProfileSidebar = value
    },
    updateActiveChatUser(contactId) {
      this.activeChatUser = contactId
      this.isChatPinned = true
      this.toggleChatSidebar()
      this.typedMessage = ''
    },
    showProfileSidebar(userId, openOnLeft = false) {
      this.userProfileId = userId
      this.isLoggedInUserProfileView = openOnLeft
      this.activeProfileSidebar = !this.activeProfileSidebar
    },

    sendMsg() {
      if (!this.typedMessage) return


      this.$socket.emit('chat_message', {
        user: this.usuario,
        textContent: this.typedMessage,
        time: 'Mon Dec 10 2018 07:47:00 GMT+0000 (GMT)',
        isSent: false,
        isSeen: false,
      })
      this.typedMessage = ''

      const scroll_el = this.$refs.chatLogPS.$el || this.$refs.chatLogPS
      scroll_el.scrollTop = this.$refs.chatLog.scrollHeight
    },

    unirSala() {
      // const sesion = this.$store.state.sesion
      this.$socket.emit('chat_usuario_nuevo')
      this.contacts.push({
        uid: 1,
        displayName: 'Global',
        about: '',
        photoURL: '',
        status: 'online'
      })
    },

    toggleIsChatPinned(value) {
      this.isChatPinned = value
    },
    setSidebarWidth() {
      if (this.windowWidth < 1200) {
        this.isChatSidebarActive = this.clickNotClose = false
      } else {
        this.isChatSidebarActive = this.clickNotClose = true
      }
    },
    toggleChatSidebar(value = false) {
      if (!value && this.clickNotClose) return
      this.isChatSidebarActive = value
    }
  },
  components: {
    VuePerfectScrollbar,
    ChatContact,
    UserProfile,
    ChatNavbar,
    ChatLog,
  },
  created() {
    const sesion = this.$store.state.sesion
    this.setSidebarWidth()
    this.usuario = sesion.usuario

    this.sockets.subscribe('chat_message', (data) => {
      if (this.usuario == data.user) data.isSent = true
      this.chatData.msg.push(data);
    });

    this.sockets.subscribe('chat_usuario_nuevo', (data) => {
      this.$vs.notify({
        title: data.usuario,
        text: 'Se ha unido a la sala',
        color: 'success'
      })
    });


    this.sockets.subscribe('chat_usuario_salir', (data) => {
      this.$vs.notify({
        title: data.usuario,
        text: 'Ha salido de la sala',
        color: 'danger'
      })
    });
    this.sockets.subscribe('disconnect', (data) => {
      this.$vs.notify({
        title: data.usuario,
        text: 'Ha salido de la sala',
        color: 'danger'
      })
    });
  },
  mounted() {
    this.unirSala()
    // this.$store.dispatch("chat/setChatSearchQuery", "")
  },
  beforeDestroy() {
    this.sockets.unsubscribe('disconnect');
    this.sockets.unsubscribe('chat_usuario_nuevo');
    this.sockets.unsubscribe('chat_usuario_salir');
    this.sockets.unsubscribe('message');
  }
}
</script>


<style lang="scss">
@import "@/assets/scss/vuexy/apps/chat.scss";
</style>
