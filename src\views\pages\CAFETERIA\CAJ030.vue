import { default } from '../../../layouts/components/navbar/components/SearchBar.vue';
<template>
    <div>
        <vx-card title="Consulta de Notas de Credito">
            <vs-divider border-style="solid" color="dark"></vs-divider>
            <div class="box-custom">
                <ValidationObserver ref="formValidate" mode="lazy">
                    <vs-row vs-justify="center">
                        <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="8">
                            <div class="flex flex-wrap-nowrap">
                                <div class="div-input">
                                    <ValidationProvider name="serie" rules="required|max:3" class="required" v-slot="{ errors }">
                                        <label class="typo_label label-static">Serie</label>
                                        <vs-input class="w-full input-serie" v-model="serie" autofocus :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"></vs-input>
                                    </ValidationProvider>
                                </div>
                                <div class="div-input">
                                    <ValidationProvider name="numero" rules="required|numero_entero" class="required" v-slot="{ errors }">
                                        <label class="typo_label label-static">Número</label>
                                        <vs-input type="number" class="w-full input-numero" v-model="numero" v-on:blur="NumeroNotaCredito()" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"></vs-input>
                                    </ValidationProvider>
                                </div>
                            </div>
                        </vs-col>
                    </vs-row>
                    <br>
                    <vs-row>
                        <vs-col vs-w="12">
                            <vs-row vs-justify="left" vs-w="12">
                                <vs-col type="flex" vs-justify="left" vs-align="left" vs-w="6">
                                    <vs-row vs-justify="left" vs-w="12">
                                        <vs-col type="flex" vs-justify="left" vs-align="left" vs-w="4">
                                            <div class="div-input flex-wrap-nowrap">
                                                <label class="label-static">A Nombre de:</label>
                                                <input class="w-full label-info" v-model="nombreDe" disabled/>
                                            </div>
                                        </vs-col>
                                    </vs-row>
                                    <vs-row vs-justify="left" vs-w="12">
                                        <vs-col type="flex" vs-justify="left" vs-align="left" vs-w="4">
                                            <div class="div-input flex-wrap-nowrap">
                                                <label class="label-static">Fecha:</label>
                                                <input class="w-full label-info" v-model="fecha" disabled/>
                                            </div>
                                        </vs-col>
                                    </vs-row>
                                    <vs-row vs-justify="left" vs-w="12">
                                        <vs-col type="flex" vs-justify="left" vs-align="left" vs-w="4">
                                            <div class="div-input flex-wrap-nowrap">
                                                <label class="label-static">Serie Factura:</label>
                                                <input class="w-full label-info" v-model="serieFactura" disabled/>
                                            </div>
                                        </vs-col>
                                        <vs-col type="flex" vs-justify="left" vs-align="left" vs-w="4">
                                            <div class="div-input flex-wrap-nowrap">
                                                <label class="label-static">Nit:</label>
                                                <input class="w-full label-info" v-model="nit" disabled/>
                                            </div>
                                        </vs-col>
                                    </vs-row>
                                    <vs-row vs-justify="left" vs-w="12">
                                        <vs-col type="flex" vs-justify="left" vs-align="left" vs-w="4">
                                            <div class="div-input flex-wrap-nowrap">
                                                <label class="label-static">Factura:</label>
                                                <input class="w-full label-info" v-model="factura" disabled/>
                                            </div>
                                        </vs-col>
                                        <vs-col type="flex" vs-justify="left" vs-align="left" vs-w="4">
                                            <div class="div-input flex-wrap-nowrap">
                                                <label class="label-static">Usuario:</label>
                                                <input class="w-full label-info" v-model="usuario" disabled/>
                                            </div>
                                        </vs-col>
                                    </vs-row>
                                    <vs-row vs-justify="left" vs-w="12">
                                        <vs-col type="flex" vs-justify="left" vs-align="left" vs-w="4">
                                            <div class="div-input flex-wrap-nowrap">
                                                <label class="label-static">Ventas:</label>
                                                <input class="w-full label-info" v-model="ventas" disabled/>
                                            </div>
                                        </vs-col>
                                        <vs-col type="flex" vs-justify="left" vs-align="left" vs-w="4">
                                            <div class="div-input flex-wrap-nowrap">
                                                <label class="label-static">Servicio:</label>
                                                <input class="w-full label-info" v-model="servicio" disabled/>
                                            </div>
                                        </vs-col>
                                    </vs-row>
                                </vs-col>
                                <vs-col type="flex" vs-justify="left" vs-align="left" vs-w="6">
                                    <div v-if="isAnulado" class="div-input flex-wrap-nowrap">
                                        <vx-card>
                                            <div>
                                                <vs-row vs-justify="left">
                                                    <vs-col type="flex" vs-justify="center" vs-align="left" vs-w="4">
                                                        <div class="div-input flex-wrap-nowrap">
                                                            <label class="label-static">Por:</label>
                                                            <input class="w-full label-info" v-model="anuladoPor" disabled/>
                                                        </div>
                                                    </vs-col>
                                                </vs-row>
                                                <vs-row vs-justify="left">
                                                    <vs-col type="flex" vs-justify="center" vs-align="left" vs-w="4">
                                                        <div class="div-input flex-wrap-nowrap">
                                                            <label class="label-static">Fecha:</label>
                                                            <input class="w-full label-info" v-model="anuladoFecha" disabled/>
                                                        </div>
                                                    </vs-col>
                                                </vs-row>
                                                <vs-row vs-justify="left">
                                                    <vs-col type="flex" vs-justify="center" vs-align="left" vs-w="4">
                                                        <div class="div-input flex-wrap-nowrap">
                                                            <label class="label-static">Razon:</label>
                                                            <input class="w-full label-info" v-model="anuladoRazon" disabled/>
                                                        </div>
                                                    </vs-col>
                                                </vs-row>
                                            </div>
                                        </vx-card>
                                    </div>
                                </vs-col>
                                <vs-col type="flex" vs-justify="left" vs-align="left" vs-w="2">
                                    <div class="div-input flex-wrap-nowrap">
                                    </div>
                                </vs-col>
                            </vs-row>
                        </vs-col>
                    </vs-row>
                    <vs-row>
                        <vs-col vs-w="12">
                            <div class="div-input flex-wrap-nowrap">
                                <label class="label-static">Concepto:</label>
                                <input class="w-full label-info" v-model="concepto" disabled/>
                            </div>
                        </vs-col>
                        <vs-col vs-w="12">
                            <div class="div-input flex-wrap-nowrap">
                                <label class="label-static">Rebaja Coaseguro:</label>
                                <input class="w-full label-info" v-model="rebajaCoaseguro" disabled/>
                            </div>
                        </vs-col>
                    </vs-row>
                </ValidationObserver>
            </div>
        </vx-card>
    </div>
</template>
<script>
    export default {
        data(){
            return{
                serie: '',
                numero: '',
                nombreDe: '',
                fecha: '',
                serieFactura: '',
                nit: '',
                factura: '',
                usuario: '',
                ventas: '',
                servicio: '',
                anuladoPor: '',
                anuladoFecha: '',
                anuladoRazon: '',
                isAnulado: false,
                concepto: '',
                rebajaCoaseguro: ''
            }
        },
        methods: {
            isEmptyObject(obj){
                return Object.keys(obj).length === 0;
            },
            NumeroNotaCredito(){
                this.axios.post("/app/v1_JefeCaja/ConsultaNotasCredito", {
                    "SerieNota": this.serie,
                    "CodigoNota": this.numero
                })
                .then(resp => {
                    if(!this.isEmptyObject(resp.data.json)){
                        const{ Concepto, Factura, Fecha, Nit, Nombre, RebajarSaldoAdmCopago, SerieFactura, Servicios, Status, Usuario, Ventas } = resp.data.json[0]
                        this.nombreDe = Nombre
                        this.fecha = Fecha
                        this.serieFactura = SerieFactura
                        this.nit = Nit
                        this.factura = Factura
                        this.usuario = Usuario
                        this.ventas = Ventas != '' ?  parseFloat(Ventas).toFixed(0) : 0
                        this.servicio = Servicios != '' ?  parseFloat(Servicios).toFixed(0) : 0
                        this.concepto = Concepto
                        this.rebajaCoaseguro = RebajarSaldoAdmCopago != '' ? 'SI Rebaja Copago o Coaseguro' : 'NO Rebaja Copago o Coaseguro'
                        if(Status == 'A'){
                            this.isAnulado = true
                            this.axios.post("/app/v1_JefeCaja/ConsultaAnulacionNotasCredito", {
                                "SerieNota": this.serie,
                                "CodigoNota": this.numero
                            })
                            .then(resp2 => {
                                if(!this.isEmptyObject(resp2.data.json)){
                                    const { UsuarioAnula, FechaAnula, Razon } = resp2.data.json[0]
                                    this.anuladoPor = UsuarioAnula
                                    this.anuladoFecha = FechaAnula
                                    this.anuladoRazon = Razon
                                }
                            })
                        }else{
                            this.isAnulado = false
                            this.anuladoPor = ''
                            this.anuladoFecha = ''
                            this.anuladoRazon = ''
                        }
                    }else{
                        this.nombreDe = ''
                        this.fecha = ''
                        this.serieFactura = ''
                        this.nit = ''
                        this.factura = ''
                        this.usuario = ''
                        this.ventas = ''
                        this.servicio = ''
                        this.concepto = ''
                        this.rebajaCoaseguro = ''
                        this.isAnulado = false
                        this.$vs.dialog({
                            color: 'danger',
                            title: 'Jefe de Caja',
                            text: 'Esta Nota de Crédito no está registrada.',
                            acceptText: 'Aceptar'
                        })
                    }
                    
                })
            }
        }
    }
</script>
<style lang="scss" scoped>
    .box-custom{
        box-shadow: 0px 15px 40px -10px;
        padding: 15px;
    }
    .div-input{
        padding: 10px;
        .input-serie{
            width: 75px;
        }
        .input-numero{
            width: 200px;
        }
    }
    .label-info{
        margin-left: 10px;
        color:#E74C3C;
        opacity: 0.7;
        border: 0;
        font-size: 16px;
        background-color: white;
    }
    .label-static{
        font-weight: bold;
        font-size: 16px;
    }
</style>