export const DefaulGridOptions = {
    visible: true,
    showRowLines: true,
    showColumnLines: true,
    showBorders: true,
    'load-panel': {
        enabled: false
    },
    selection: {
        mode: 'single'
    },
    searchPanel: {
        visible: true
    },
    groupPanel: {
        visible: true,
    },
    focusedRowEnabled: false,
    rowAlternationEnabled: true,
    columnHidingEnabled: true,
    hoverStateEnabled: true,
    width: '100%',
    height: 'calc(100vh - 310px)',
    columnAutoWidth: true,
    allowColumnReordering: true,
    allowColumnResizing: true,
    columnResizingMode: 'widget',
    headerFilter: {
        visible: true,
        allowSearch: true,
    },
    wordWrapEnabled: true,
    paging: {
        enabled: false,
        pageSize: 20
    },
    scrolling: {
        showScrollbar: 'always',
        useNative: false,
    },
    sorting: {
        mode: 'multiple'
    }
}
export const popupPosition = { my: 'center', at: 'center', of: 'window', offset: {x:50, y:0}}
export const popupPositionCorner = { my: 'left top', at: 'left top', of: 'window', offset: {x:50, y:10}}
export const _confMoneda = {
    dataType: 'number',
    format: 'Q\'.\' ###,###,###.00',
}

export const _dateColumnConf = {
    dataType: 'datetime',
    format: "dd/MM/yyyy HH:mm:ss",
    editorOptions: {
        showClearButton: false,
        useMaskBehavior: true,
        placeholder: 'dd/MM/yyyy HH:mm:ss',
        displayFormat: 'dd/MM/yyyy HH:mm:ss',
        dateSerializationFormat: 'yyyy-MM-ddTHH:mm:ss'
    },
    width: 150,
}

export const acciones = [{
    id: 'editar', //id sera el nombre del emit cuando se seleccione el item
    name: 'editar',
    text: 'Editar cliente',
    icon: 'fas fa-pen-square',
    hint: 'Editar datos del cliente o afiliado',
    visible: (afiliadoData) => {
        return Boolean(afiliadoData.IdCliente)
    },
},
{
    id: 'pagos',
    text: "Pagos",
    hint: 'Ver los pagos realizado a la póliza',
    icon: "fas fa-money-bill-wave",
    visible: (afiliadoData) => {
        return Boolean(afiliadoData.NumeroAdhesion)
    },
},
{
    id: 'cambioplan',
    text: "Cambiar Plan",
    hint: 'Cambiar el plan del afiliado',
    icon: "fas fa-retweet",
    visible: (afiliadoData) => {
        return Boolean(afiliadoData.NumeroAdhesion)
    },
},
{
    id: 'dependientes',
    text: "Dependientes",
    hint: 'Mantenimiento de los dependientes',
    icon: "fas fa-people-carry",
    visible: (afiliadoData) => {
        return Boolean(afiliadoData.NumeroAdhesion)
    },
},
{
    id: 'listanegra',
    text: 'Excluir de la lista negra',
    hint: 'Excluye al afiliado de la tan temida "Lista Negra"',
    icon: 'fas fa-share-square',
    visible: (afiliadoData) => {
        return Boolean(afiliadoData.NumeroAdhesion) && afiliadoData.ListaNegra
    },
},
{
    id: 'estudios',
    text: 'Ordenes de estudios',
    hint: 'Registro y consulta ordenes de estudios',
    icon: 'fas fa-vials',
    visible: (afiliadoData) => {
        return Boolean(afiliadoData.NumeroAdhesion)
    },
},
{
    id: 'bitacora',
    text: 'Bitacora',
    hint: 'Registro y consulta bitácora en observaciones sobre el afiliado',
    icon: 'fas fa-clipboard-list',
    visible: (afiliadoData) => {
        return Boolean(afiliadoData.NumeroAdhesion)
    },
},
{
    id: 'siniestro',
    text: 'Siniestro',
    hint: 'Ver información de siniestro de la póliza',
    icon: 'fas fa-person-falling-burst',
    visible: (afiliadoData) => {
        return Boolean(afiliadoData.NumeroAdhesion)
    },
},
{
    id: 'preexistencia',
    text: 'Traslado preexistencia',
    hint: 'Traslada el sinestro de de pólizas anteriores a la actual',
    icon: 'fas fa-arrows-h',
    visible: (afiliadoData) => {
        return Boolean(afiliadoData.NumeroAdhesion) && ['BSS', 'BSM'].includes(afiliadoData.CodigoAseguradora)
    },
},
{
    id: 'historial',
    text: 'Historial de Solicitudes',
    hint: 'Ver el historial de estudios',
    icon: 'fas fa-clock-rotate-left',
    visible: (afiliadoData) => {
        return Boolean(afiliadoData.NumeroAdhesion)
    },
},
]