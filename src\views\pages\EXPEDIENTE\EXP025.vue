<template>
<div>
    <vs-row class="p-2 w-full">
        <vs-col vs-justify="center" vs-align="center" class="pr-2">
            <vx-card>
                <DxDataGrid :ref="dataGridOrden" v-bind="DefaultDxGridConfiguration" :selection="{mode: 'none'}" :data-source="ordenesDataSource" :height="'100%'" @cell-click="onCellClick" @editor-preparing="editorFechaValue">

                    <DxToolbar>
                        <DxItem name="groupPanel" />
                        <DxItem name="searchPanel" />
                        <DxItem location="after" template="opcionesOrden" />
                    </DxToolbar>

                    <DxEditing :allow-updating="false" :allow-adding="true" :allow-deleting="false" mode="popup">
                        <DxPopup v-bind="popupConf" :height="300" title="Agregar orden">
                            <DxToolbarItem widget="dxButton" toolbar="bottom" :options="{
                                        text: 'Guardar',
                                        type: 'success',
                                        useSubmitBehavior: true,
                                        onClick: submit
                                    }" location="after">
                            </DxToolbarItem>
                            <DxToolbarItem widget="dxButton" toolbar="bottom" :options="{
                                        text: 'Cancelar',
                                        type: 'danger',
                                        useSubmitBehavior: true,
                                        onClick: ()=>{this.dataGrid.cancelEditData()}
                                    }" location="after">
                            </DxToolbarItem>
                        </DxPopup>

                        <DxForm labelMode="floating" :ref="dataAddOrden">
                            <DxItem data-field="Descripcion" editor-type="dxTextBox" :col-span="2" :is-required="true">
                                <DxLabel :text="'Descripción de medicamento'" />
                            </DxItem>
                            <DxItem data-field="FechaInicio" editor-type="dxDateBox" :use-mask-behavior="true" :col-span="2" :is-required="true" :editor-options="dateBoxOptions">
                                <DxLabel :text="'Fecha de inicio'" />
                            </DxItem>
                        </DxForm>
                    </DxEditing>

                    <DxColumn :width="150" :allow-resizing="false" data-field="Estado" caption="Estado" cell-template="arrowRight" alignment="center" />

                    <DxColumn :width="300" data-field="Descripcion" caption="Descripción de orden" alignment="center" />
                    <DxColumn :width="150" data-field="FechaInicio" caption="Fecha inicio" format="dd/MM/yyyy" data-type="date" alignment="center" />
                    <DxColumn :width="200" data-field="UsuarioRegistra" caption="Usuario registra" alignment="center" />
                    <DxColumn :width="150" data-field="FechaRegistro" caption="Fecha registro" format="dd/MM/yyyy" data-type="date" alignment="center" />
                    <DxColumn :width="200" data-field="UsuarioOmite" caption="Usuario omite" alignment="center" />
                    <DxColumn :width="150" data-field="FechaOmision" caption="Fecha omisión" format="dd/MM/yyyy" data-type="date" alignment="center" />
                    <DxColumn :width="200" data-field="UsuarioReactiva" caption="Usuario reactiva" alignment="center" />
                    <DxColumn :width="150" data-field="FechaReactiva" caption="Fecha reactiva" format="dd/MM/yyyy" data-type="date" alignment="center" />
                    <DxMasterDetail :enabled="true" template="gridDetalle" />

                    <template #arrowRight="{data: info}">
                        <div style="display: flex; flex-wrap: wrap; justify-content: left;">
                            <vs-tooltip text="Cambiar estado" position="bottom" style="cursor: pointer;">
                                <font-awesome-icon v-if="info.data.IdEstado === '5'" :icon="['fas', 'toggle-on']" style="font-size: 20px; color: #20a10a;" @click="cambiarEstado(info)" />
                                <font-awesome-icon v-if="info.data.IdEstado === '4'" :icon="['fas', 'toggle-off']" style="font-size: 20px; color: #337ab7;" @click="cambiarEstado(info)" />
                            </vs-tooltip>
                            <span class="pl-2">{{info.data.Estado}}</span>
                        </div>
                    </template>
                    <template #opcionesOrden>
                        <ExpedienteGridToolBar v-bind="$props" :visible="true" :pdfExportItems="[{text:'Control Órdenes', reportName: 'Control Órdenes'}]" @add="addRow" @refresh="Cargar()" :report-param="$props" />
                    </template>
                    <template #gridDetalle>
                        <DetalleOrdenes v-bind="$props" :width="'100%'" :orden="getOrden" />
                    </template>
                </DxDataGrid>
            </vx-card>
        </vs-col>
    </vs-row>
</div>
</template>

<script>
import {
    DxDataGrid,
    DxColumn,
    DxEditing,
    DxForm,
    DxToolbar,
    DxLabel,
    DxPopup,
    DxMasterDetail,
} from 'devextreme-vue/data-grid'
import CustomStore from 'devextreme/data/custom_store'
import ExpedienteGridToolBar from './ExpedienteGridToolBar.vue'
import DetalleOrdenes from './EXP025_ControlOrdenesDetalle.vue'
import {
    DxItem
} from 'devextreme-vue/form'
import 'devextreme-vue/lookup'
import {
    DxToolbarItem
} from 'devextreme-vue/popup'
import {
    DefaultDxGridConfiguration,
} from './data'
import 'devextreme-vue/text-area'
import EXPBase from './EXPBase.vue'

const dataGridOrden = 'gridOrdenes'
const dataAddOrden = 'addOrdenPop'

export default {
    name: 'ControlOrdenes',
    extends: EXPBase,
    components: {
        DxDataGrid,
        DxColumn,
        ExpedienteGridToolBar,
        DxEditing,
        DxForm,
        DxItem,
        DxToolbar,
        DxPopup,
        DxLabel,
        DxToolbarItem,
        DxMasterDetail,
        DetalleOrdenes
    },
    data() {
        return {
            //Listado de ordenes
            ordenes: [],
            ordenesDataSource: new CustomStore({
                key: "id",
                load: () => {
                    return this.ordenes;
                },
                insert: (values) => {
                    this.guardarOrden(values);
                },
            }),
            //Detalle de ordenes
            detalle: [],
            dataGridOrden,
            dataAddOrden,
            DefaultDxGridConfiguration,
            dateBoxOptions: '',
            ordenDetalle: '',
        }
    },
    methods: {
        submit() {
            this.dataGrid.saveEditData()
        },
        submitDetalle() {
            this.dataGridDetalleOrden.saveEditData()
        },
        cancelDetalle() {
            this.ordenDetalle = ''
            this.dataGridDetalleOrden.cancelEditData()
        },
        Cargar() {
            if (this.SerieAdmision && this.CodigoAdmision)
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaOrdenesMedicamento', {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                })
                .then(resp => {
                    this.ordenes = resp.data.json.map((x, index) => {
                        return {
                            id: index,
                            IdOrden: x.IdOrden,
                            Descripcion: x.DescripcionOrden,
                            FechaInicio: x.FechaInicio,
                            CodEmpleado: x.CodEmpleado,
                            EmpresaEmpleado: x.EmpresaEmpleado,
                            IdEstado: x.IdEstado,
                            Estado: x.Estado,
                            UsuarioRegistra: x.UsuarioRegistra,
                            FechaRegistro: x.FechaRegistro,
                            UsuarioOmite: x.UsuarioOmite,
                            FechaOmision: x.FechaOmision,
                            UsuarioReactiva: x.UsuarioReactiva,
                            FechaReactiva: x.FechaReactivacion,
                            IdHistorial: x.IdHistorial
                        }
                    })
                    this.ordenesDataSource.load().then(
                        () => {
                            this.refreshDataGrid()
                        }
                    )
                })
            else {
                this.ordenes = [];
                this.refreshDataGrid();
            }
        },
        refreshDataGrid() {
            this.dataGrid.refresh()
                .then(function () {

                })
                .catch(function () {
                    //console.error('error refreshing: ' + error)
                });
            this.dataGrid.clearSelection();
            this.dataGrid.clearSorting();
            this.dataGrid.clearFilter();
            this.dataGrid.pageIndex(0);
            this.dataGrid.collapseAll(-1);
            this.dataGrid.columnOption('FechaInicio', 'sortOrder', 'desc');
            this.ordenDetalle = ''
        },
        onCellClick(e) {
            if (e.rowType === "data" && e.columnIndex !== 1) {
                if (this.dataGrid.isRowExpanded(e.key)) {
                    this.ordenDetalle = ''
                    this.dataGrid.collapseRow(e.key);
                } else {
                    e.component.collapseAll(-1)

                    this.ordenDetalle = e.data.IdOrden;

                    this.dataGrid.expandRow(e.key);
                }
            }
        },
        addRow() {
            if (this.StatusAdmision != 'A')
                this.$vs.notify({
                    time: 4000,
                    title: 'Antecedentes',
                    text: 'La admisión no está activa, no se puede agregar nuevo antecedente',
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'warning',
                    position: 'bottom-center'
                })
            else
                this.dataGrid.addRow();
        },
        formatToday(date = new Date()) {
            const year = date.toLocaleString('default', {
                year: 'numeric'
            });
            const month = date.toLocaleString('default', {
                month: '2-digit'
            });
            const day = date.toLocaleString('default', {
                day: '2-digit'
            });

            return [year, month, day].join('/');
        },
        formatFecha(date) {
            const year = new Date(date).toLocaleString('default', {
                year: 'numeric'
            });
            const month = new Date(date).toLocaleString('default', {
                month: '2-digit'
            });
            const day = new Date(date).toLocaleString('default', {
                day: '2-digit'
            });

            return [year, month, day].join('-');
        },
        guardarOrden(values) {
            let postData = {
                SerieAdmision: this.SerieAdmision,
                Admision: this.CodigoAdmision,
                Descripcion: values.Descripcion,
                FechaInicio: this.formatFecha(values.FechaInicio),
            }
            this.axios.post("/app/v1_ExpedienteEvolucion/InsertarControlMedicamento", {
                ...postData
            }).then(() => {
                this.Cargar()
            })
        },
        //Asigna el valor al campo de fecha en el Popup de agregar órden
        editorFechaValue(e) {
            if (e.parentType === 'dataRow' && e.dataField === 'FechaInicio') {
                e.setValue(new Date());
                e.editorOptions.value = new Date();
            }
        },
        cambiarEstado(e) {
            let postData = {
                SerieAdmision: this.SerieAdmision,
                Admision: this.CodigoAdmision,
                IdOrden: e.data.IdOrden
            }
            this.axios.post("/app/v1_ExpedienteEvolucion/ControlMedicamentoEstado", {
                ...postData
            }).then(() => {
                this.Cargar()
            })
        }
    },
    mounted() {
        this.dateBoxOptions = {
            max: this.formatToday(),
            value: new Date(),
        }
        this.Cargar();
    },
    computed: {
        ShouldRender() {
            return Boolean(this.SerieAdmision) && Boolean(this.CodigoAdmision) && Boolean(this.NumeroExpediente)
        },

        dataGrid: function () {
            return this.$refs[dataGridOrden].instance;
        },

        getOrden: {
            get() {
                return this.ordenDetalle;
            }
        }
    },
}
</script>

<style>
[dir=ltr] .dx-datagrid-group-closed::before,
[dir=ltr] .dx-datagrid-group-opened::before {
    font-size: 40px !important;
    color: #337ab7;
}
</style>
