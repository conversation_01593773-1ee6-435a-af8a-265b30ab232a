
<template>
    <vx-card title="Reasignación Admisión RN">


        <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">

            <form ref="formAnulacion" method="post" @submit.prevent="handleSubmit(recalcular_admision())">

                <!-- <div class="flex flex-wrap"> -->
                <div class="container">

                    <div class="admision-rn">


                        <vs-divider position="left" class="mt-2">Datos Admisión Recién Nacido</vs-divider>

                        <div class="w-full md:w-full lg:w-1/12 xl:w-1/12 m-1  mt-1">

                            <ValidationProvider rules="required" v-slot="{ errors }">
                                <vs-input label="Serie" v-model="SerieRN" class="w-full"
                                    v-on:keyup="SerieRN = SerieRN?.toUpperCase() || ''" :danger="errors.length > 0"
                                    :danger-text="(errors.length > 0) ? errors[0] : null" />
                            </ValidationProvider>
                        </div>

                        <div class="w-full md:w-full lg:w-2/12 xl:w-2/12 m-1">
                            <ValidationProvider rules="required" v-slot="{ errors }">
                                <SM-Buscar v-model="CodigoAdmisionRN" label="Admision" api="app/v1_admision/AdmisionesBusqueda"
                                    :api_filtro="{
                                        Serie: SerieRN,
                                        CodigoAdmision: CodigoAdmisionRN,
                                        NombrePaciente: NombrePacienteRN
                                    }" :api_campo_respuesta_mostrar="['Codigo', 'Nombre']"
                                    :api_campos="['Serie', 'CodigoAdmision', 'NombrePaciente']"
                                    :api_titulos="['Serie#', 'CodigoAdmision#', 'NombrePaciente']"
                                    api_campo_respuesta="CodigoAdmision" :api_preload="false" :disabled_texto="false"
                                    :mostrar_busqueda="true" :callback_buscar="cargaAdmisionRN"
                                    :callback_cancelar="limpiarAdmisionRN"
                                    :dangertext="(errors.length > 0) ? errors[0] : null" />
                            </ValidationProvider>
                        </div>

                        <div class="w-full md:w-full lg:w-7/12 xl:w-3/12 m-1">
                            <ValidationProvider rules="required" v-slot="{ errors }">
                                <vs-input label="Nombre del paciente:" class="w-full" :value="NombrePacienteRN" disabled
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                            </ValidationProvider>
                        </div>

                    </div>

                    <div class="admision-raiz">

                        <vs-divider position="left" class="mt-2">Datos Admisión Madre</vs-divider>

                        <div class=" w-full md:w-full lg:w-1/12 xl:w-1/12 m-1">
                            <ValidationProvider rules="required" v-slot="{ errors }">
                                <vs-input label="Serie" v-model="SerieRaiz" class="w-full"
                                    v-on:keyup="SerieRaiz = SerieRaiz?.toUpperCase() || ''" :danger="errors.length > 0"
                                    :danger-text="(errors.length > 0) ? errors[0] : null" />
                            </ValidationProvider>
                        </div>

                        <div class=" w-full md:w-full lg:w-2/12 xl:w-2/12 m-1">
                            <ValidationProvider rules="required" v-slot="{ errors }">
                                <SM-Buscar v-model="CodigoAdmisionRaiz" label="Admision"
                                    api="app/v1_admision/AdmisionesBusqueda" :api_filtro="{
                                        Serie: SerieRaiz,
                                        CodigoAdmision: CodigoAdmisionRaiz,
                                        NombrePaciente: NombrePacienteRaiz
                                    }" :api_campo_respuesta_mostrar="['Codigo', 'Nombre']"
                                    :api_campos="['Serie', 'CodigoAdmision', 'NombrePaciente']"
                                    :api_titulos="['Serie#', 'CodigoAdmision#', 'NombrePaciente']"
                                    api_campo_respuesta="CodigoAdmision" :api_preload="false" :disabled_texto="false"
                                    :mostrar_busqueda="true" :callback_buscar="cargaAdmisionRaiz"
                                    :callback_cancelar="limpiarAdmisionRaiz"
                                    :dangertext="(errors.length > 0) ? errors[0] : null" />
                            </ValidationProvider>
                        </div>

                        <div class=" w-full md:w-full lg:w-7/12 xl:w-3/12 m-1">
                            <ValidationProvider rules="required" v-slot="{ errors }">
                                <vs-input label="Nombre del paciente:" class="w-full" :value="NombrePacienteRaiz" disabled
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                            </ValidationProvider>
                        </div>

                    </div>


                    <vs-button class="w-2/5 mt-2" @click="handleSubmit(reasignarAdmision(invalid))">
                        Vincular Admisión Raiz a RN
                    </vs-button>


                </div>

            </form>
        </ValidationObserver>
    </vx-card>
</template>


<script>

export default {
    data() {
        return {
            SerieRN: '',
            CodigoAdmisionRN: '',
            NombrePacienteRN: '',
            SerieRaiz: '',
            CodigoAdmisionRaiz: '',
            NombrePacienteRaiz: ''
        }
    }, methods: {
        cargaAdmisionRN(dataAdmision) {

            this.NombrePacienteRN = dataAdmision.NombrePaciente
            this.SerieRN = dataAdmision.Serie

        },
        limpiarAdmisionRN() {
            this.SerieRN = ''
            this.CodigoAdmisionRN = ''
            this.NombrePacienteRN = ''

        },
        cargaAdmisionRaiz(dataAdmision) {

            this.NombrePacienteRaiz = dataAdmision.NombrePaciente
            this.SerieRaiz = dataAdmision.Serie
        },
        limpiarAdmisionRaiz() {
            this.SerieRaiz = ''
            this.CodigoAdmisionRaiz = ''
            this.NombrePacienteRaiz = ''
        },
        async reasignarAdmision() {

        

            return new Promise((resolve, reject) => {
                this.axios.post('/app/v1_admision/ReasignacionAdmisionRN', {
                    Empresa: '',
                    SerieRN: this.SerieRN,
                    CodigoAdmisionRN: this.CodigoAdmisionRN,
                    SerieRaiz: this.SerieRaiz,
                    CodigoAdmisionRaiz: this.CodigoAdmisionRaiz
                })
                    .then((response) => {
                        
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Exito',
                            text: 'Reasignación Exitosa',
                        })
                        resolve(response)
                    })
                    .catch((error) => {
                        this.$vs.dialog({
                            type: 'confirm',
                            color: '#ED8C72',
                            acceptText: 'Continuar',
                            title: 'Error en ReAsignación',
                            text: error.descripcion,
                            clientWidth: 100,
                            accept: () => {
                                
                            }
                        });
                        reject(error.response.data.errors)
                    })
            })

            // console.log(respREasignacion)

        }


    }
}

</script>

<style scoped>
* {
    box-sizing: border-box;
    padding: 2px;
    margin: 0;
}

.container {
    display: grid;
    grid-gap: 5px;
    width: 100%;
    height: 100%;
    grid-template-areas: "admision-rn"
        "admision-raiz";
    grid-template-columns: 100%;
    grid-template-rows: 1fr 1.5fr;
}

.container>div {
    display: flex;
    flex-wrap: wrap;
    border: 1px solid #888;
}

.admision-rn {
    grid-area: admision-rn;
}

.admision-raiz {
    display: flex;
    grid-area: admision-raiz;

}
</style>





<!-- <template>
    <div class="margen-entre-contenedores">
        <Layout :layout="layout" />
    </div>
</template>

  
<script>

import LayoutBuilder from "/src/components/sermesa/builder/LayoutBuilder.js";
import LayoutDirector from "/src/components/sermesa/builder/LayoutDirector";
import Layout from "/src/components/sermesa/modules/admisiones/Layout.vue";



export default {
    name: "AdmisionInterna",
    components: {
        Layout,
    },
    data() {
        return {
            layout: [],
            layoutConfig: [
                {
                    containerClass: "datosPaciente",
                    rows: [
                        {
                            columns: [
                                {
                                    component: "SerieAdmision",
                                    size: 12,
                                    validations: { name: "serieAdmision", rules: "required" },
                                },
                                {
                                    component: "InformacionPaciente",
                                    size: 12,
                                    validations: { name: "informacionPaciente", rules: "required" },
                                },
                            ],
                        },
                    ],
                },
                {
                    containerClass: "datosVacunasCovid",
                    rows: [
                        {
                            columns: [
                                {
                                    component: "DiagnosticosMedicos",
                                    size: 12,
                                    validations: { name: "diagnosticosMedicos", rules: "required" },
                                },
                            ],
                        },
                    ],
                },
            ],
        };
    },
    created() {
        const layoutBuilder = new LayoutBuilder();
        const layoutDirector = new LayoutDirector(layoutBuilder);
        this.layout = layoutDirector.createLayout(this.layoutConfig);
    },
};
</script>
  



<style scoped>
* {
    box-sizing: border-box;
    padding: 0px;
    margin: 0;
}


.margen-entre-contenedores {
    margin-bottom: 1px; /* Ajusta este valor según sea necesario */
}
</style> -->