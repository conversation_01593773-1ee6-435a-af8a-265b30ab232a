<template>
    
    <div class="flex flex-wrap" id="BusquedaAdmision">
        <div :class="size =='full'? 'w-full md:w-full lg:w-1/12 xl:w-1/12 p-1' : 'w-full md:w-full lg:w-4/12 xl:w-2/12 p-1' ">
            <ValidationProvider rules="required" v-slot="{ errors }">
                <vs-input label="Serie" v-model="serie" 
                            class="w-full" 
                            v-on:keyup="serie = serie?.toUpperCase() || ''" 
                            :disabled = "desactivarCamposAdmision"
                            :danger="errors.length > 0"
                            :danger-text="(errors.length > 0) ? errors[0] : null" /> 
            </ValidationProvider>
        </div>
        <div :class="size =='full'? 'w-full md:w-full lg:w-3/12 xl:w-2/12 p-1' : 'w-full md:w-full lg:w-8/12 xl:w-4/12 p-1'">
            <ValidationProvider rules="required" v-slot="{ errors }">
                <SM-Buscar class="w-full" 
                            ref="ComponeneteBusqueda"
                            label="Admisión" v-model="Admision" :api="api"
                            :api_campos="api_campos" 
                            :api_titulos="api_titulos"
                            :api_filtro="Object.assign({},api_filtro,{'Serie':serie})"
                            api_campo_respuesta="Codigo" :api_preload="false" :disabled_texto="desactivarCamposAdmision" :mostrar_busqueda="true" 
                            :callback_buscar="cargaAdmision" :callback_cancelar="limpiar_campos"                                                                                                          
                            :danger="errors.length > 0"
                            :danger-text="(errors.length > 0) ? errors[0] : null" />                                
            </ValidationProvider>
        </div>
        <div :class="size =='full'? 'w-full md:w-full lg:w-7/12 xl:w-3/12 p-1' : 'w-full md:w-full lg:w-full xl:w-6/12 p-1'">
            <ValidationProvider rules="required" v-slot="{ errors }">
                <vs-input label="Nombre del paciente:"  
                            class="w-full" 
                            :value="paciente" disabled 
                            :danger="errors.length > 0"
                            :danger-text="(errors.length > 0) ? errors[0] : null" />                                
            </ValidationProvider>
        </div>
    </div>    
</template>
<script>
export default {
    name: "BusquedaAdmision",
    data() {
        return {
            serie: '',
            Admision: null,            
            paciente: null,
            desactivarCamposAdmision: false
        };
    },
    methods: {
        limpiar_campos() {
            this.serie = ''
            this.Admision = null            
            this.paciente = null
            this.desactivarCamposAdmision = false
            this.$emit('limpiar_datos_admision')     
        },
        cargaAdmision(datos) {           
            this.paciente = datos.Paciente
            this.serie = datos.Serie
            this.Admision = datos.Codigo
            this.desactivarCamposAdmision = true
            if(datos !== undefined)       
                this.$emit('datos_admision',datos)     
        },
        recargarComponente(datos) {           
            this.paciente = datos.Paciente
            this.serie = datos.Serie
            this.Admision = datos.Codigo
            this.$refs.ComponeneteBusqueda.buscador_valor = datos.Codigo                        
            //this.$refs.ComponeneteBusqueda.buscador_valor(datos)
            this.desactivarCamposAdmision = true 
        },
        actualizarBusqueda(){            
            this.$refs.ComponeneteBusqueda.actualizar_buscador(true);
        },
    },
    props: {
        size: {
            default: 'full'
        },
        api: {
            default: 'app/v1_JefeCaja/consulta_admision_recalculo'
        },
        api_filtro:{
            type: Object,
            default(){
                return {'Opcion':'CONSULTA','SubOpcion':'ADMISION','activo':1,'tieneHabitacion':0}
            }            
        },
        api_campos: {
            type: Array,
            default(){
                return ['Serie','Codigo','Nombres','Apellidos']
            } 
        },
        api_titulos: {
            type: Array,
            default(){
                return ['Serie#','Admision#','Nombres','Apellidos']
            }            
        }
    }
}
</script>