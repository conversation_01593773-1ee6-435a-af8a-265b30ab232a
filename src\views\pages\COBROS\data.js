export const srtLenRule = function (_fieldName = '', _min = 0, _max) {
    return {
        type: 'stringLength',
        max: _max,
        min: _min,
        message: RangeMessage(_fieldName, _min, _max)
    }
}

export const RangeMessage = function (fieldName = '', min = 0, max) {
    if (min && max)
        return `${fieldName} debe contener entre ${min} y ${max} caracteres`

    if (max)
        return `${fieldName} debe tener como máximo ${max} caracteres`

    if (min)
        return `${fieldName} debe tener por lo menos ${min} caracteres`

    return `${fieldName} es obligatorio`
}

export const dateTimeOptions = {
    editorType: 'dxDateBox',
    dataType: 'datetime',
    editorOptions: {
        type: 'datetime',
        displayFormat: 'dd/MM/yyyy HH:mm',
        dateSerializationFormat: 'yyyy-MM-ddTHH:mm:ss',
    },
}

export const dateOptions = {
    editorType: 'dxDateBox',
    dataType: 'date',
    format: 'dd/MM/yyyy',
    editorOptions: {
        type: 'date',
        displayFormat: 'dd/MM/yyyy',
        dateSerializationFormat: 'yyyy-MM-ddTHH:mm:ss',
    },
}

export const smallMoneyRule = {
   type: 'range',
   min: 0,
   max: 214000,
   message: 'Debe tener un valor entre 0 y 214000'
}

export const moneyRule = {
    type: 'range',
    min: 0,
    max: 922337203685477.58,
    message: 'Debe tener un valor entre 0 y 922,337,203,685,477.58'
}

export const confMoneda = {
    dataType: 'number',
    format: 'Q\'.\' ###,###,##0.00',
}

export const gridConfiguration = {
   editing: {
      allowUpdating: true,
      allowAdding: true,
      allowDeleting: false,
      mode: 'popup',
      useIcons: true,
      confirmDelete: true,
   },
   visible: true,
   showRowLines: true,
   showColumnLines: true,
   showBorders: true,
   loadPanel: {
       enabled: false,
   },
   selection: {
       mode: 'single'
   },
   searchPanel: {
       visible: true
   },
   focusedRowEnabled: false,
   rowAlternationEnabled: true,
   columnHidingEnabled: true,
   //columnMinWidth: 150,
   hoverStateEnabled: true,
   height: 'auto',
   allowColumnReordering: true,
   allowColumnResizing: true,
   columnResizingMode: 'widget',
   headerFilter: {
       visible: false,
       allowSearch: false
   },
   wordWrapEnabled: true,
   paging: {
       enabled: true,
       pageSize: 15
   },
}

export const gridToolbar = {
   items:['searchPanel',
   {
       location:'after',
       template:'opcionesTemplate'
   }]
}

export const catalogoTipoAseguradora = [
    {
        Codigo:'L',
        Nombre: 'Local'
    },
    {
        Codigo:'E',
        Nombre: 'Extranjero'
    }
]

/**
 * Retorna la función (filterValue, selectedFilterOperation) para filtrado sin caracteres especialies
 * @see https://js.devexpress.com/Vue/Documentation/22_1/ApiReference/UI_Components/dxDataGrid/Configuration/columns/#calculateFilterExpression
 */
export const buildFilterExpresion = (columnName) =>

   (filterValue, selectedFilterOperation) => {
         const getter = function (data) {
            return data[columnName]?.normalize('NFD').replace(/[\u0300-\u036f]/g, "")
         }
         filterValue = filterValue.normalize('NFD').replace(/[\u0300-\u036f]/g, "")
         return [getter, selectedFilterOperation || "contains", filterValue]
   }
/**
 * Configura el editor de devextreme para que trabaje con uppercase tanto visual como valor 
 * @param {*} editorOptions referencia al editor options del editor que se desea configurar 
 * si se trabaja con datagrid puede enviarse el argumento del evento onEditorPreparing
 * @see https://js.devexpress.com/Vue/Documentation/ApiReference/UI_Components/dxDataGrid/Configuration/#onEditorPreparing 
 */
export const setUpperCaseConfig = (editorOptions)=>{
    const defaultValueChangeHandler = editorOptions.onValueChanged
    editorOptions.inputAttr.class = 'uppercase'
    editorOptions.onValueChanged = function (args) { // Override the default handler
        args.value = args.value.toUpperCase()
        defaultValueChangeHandler(args);
    }
}

export const MODOS = {
    LECTURA: 0,
    NUEVO: 1,
    EDICION: 2,
    NUEVOSINGRABAR: 3,
    EDICIONSINGRABAR: 4,
}

export const DefaultDxGridConfiguration = {
  visible: true,
  showRowLines: true,
  showColumnLines: true,
  showBorders: true,
  'load-panel': {enabled: false},
  selection: {mode: 'single'},
  searchPanel: {visible: false},
  focusedRowEnabled: false,
  rowAlternationEnabled: true,  
  columnHidingEnabled: true,
  hoverStateEnabled: true,
  width: '100%',
  height:'calc(100vh - 310px)',
  columnAutoWidth: true,
  allowColumnReordering: true,
  allowColumnResizing: true,
  columnResizingMode:'widget',
  headerFilter:{
    visible:true,
    allowSearch:true
  },
  wordWrapEnabled: true,
  paging:{ enabled:true, pageSize:10 },
  scrolling: {
    showScrollbar: 'always',
    useNative: false,
  },
  
}