<template>
    <vx-card title="Cotización de servicios">

        <div class="pdiv_container">

            <div class="header_totals">

                <div class="container clearfix">
                    <div id="nested-grid">



                        <vs-col vs-justify="center" vs-align="center" vs-w="3" class="clearfix ">
                            <vs-card class="height:20px">
                                <div slot="header">
                                    <h6>
                                        Sin Seguro
                                    </h6>
                                </div>
                                <div>
                                    <h5>{{ parseFloat(this.TotalSinSeguro).toFixed(2) }}</h5>

                                </div>
                            </vs-card>
                        </vs-col>

                        <vs-col vs-justify="center" vs-align="center" vs-w="3" class="clearfix">
                            <vs-card class="height:20px">
                                <div slot="header">
                                    <h6>
                                        M/Coaseguro
                                    </h6>
                                </div>
                                <div>
                                    <h5>{{ parseFloat(this.MontoCoaseguro).toFixed(2) }} +</h5>
                                </div>
                            </vs-card>
                        </vs-col>
                        <vs-col vs-justify="center" vs-align="center" vs-w="3" class="clearfix">
                            <vs-card class="height:20px">
                                <div slot="header">
                                    <h6>
                                        Copago
                                    </h6>
                                </div>
                                <div>
                                    <h5>{{ parseFloat(this.MontoCopago).toFixed(2) }} =</h5>
                                </div>
                            </vs-card>
                        </vs-col>
                        <vs-col vs-justify="center" vs-align="center" vs-w="3" class="clearfix">
                            <vs-card class="height:20px">
                                <div slot="header">
                                    <h6>
                                        Participación
                                    </h6>
                                </div>
                                <div>
                                    <h5>{{ parseFloat(this.SumaTotal).toFixed(2) }}</h5>
                                </div>
                            </vs-card>
                        </vs-col>


                    </div>
                </div>

            </div>

            <div class="header_valores">

                <div class="container mt-4">

                    <vs-row>

                        <vx-input-group>
                            <vs-col vs-type="flex" vs-w="6">
                                <div class="ws-full pl-2">
                                    <validationProvider name="Nivel de Precio">
                                        <validationProvider name="Nivel de precios">
                                            <vs-input label="Nivel de precios" class="4/5"
                                                v-model="estima_precio_productos.NivelPrecios" readonly />
                                        </validationProvider>
                                    </validationProvider>
                                </div>
                            </vs-col>

                            <vs-col vs-type="flex" vs-w="6">
                                <div class="ws-full ">
                                    <BuscaProductos v-model="estima_precio_productos.NivelPrecios"
                                        label="Desc. Nivel de precio" type="input" id="ListaPrecios"
                                        api="app/inventario/ListaNivelesPrecios" api_campo_respuesta_mostrar="Nombre"
                                        :api_campos="['Nivel', 'Nombre']" :api_titulos="['Nivel', 'Nombre']"
                                        api_campo_respuesta="Nombre" :api_campo_respuesta_estricto="false"
                                        :api_preload="true" :disabled_texto="true"
                                        :callback_buscar="ConsultaProductos().cargarnivelesPrecios" :api_filtro="{
                                            Pagina: 1,
                                            Busqueda: '',
                                            Nombre: ''
                                        }">
                                    </BuscaProductos>
                                </div>
                            </vs-col>
                        </vx-input-group>
                    </vs-row>

                    <vs-row>

                        <vx-input-group>
                            <vs-col vs-type="flex" vs-w="6">
                                <div class="ws-full p-2">
                                    <validationProvider name="PorcentajeCoseguro">
                                        <vs-input v-model="datosPaciente.PorcentajeCoaseguro" class="w-full"
                                            label="Porcentaje Coseguro" v-on:change="ConsultaProductos().calculaTotal()" />
                                    </validationProvider>
                                </div>
                            </vs-col>

                            <vs-col vs-type="flex" vs-w="6">
                                <div class="ws-full  p-2">
                                    <validationProvider name="MontoCopago">
                                        <vs-input v-model="datosPaciente.PacMontoCopago" class="w-full" label="Monto Copago"
                                            v-on:change="ConsultaProductos().calculaTotal()" />
                                    </validationProvider>
                                </div>
                            </vs-col>
                        </vx-input-group>
                    </vs-row>


                </div>
            </div>

            <div class="prod_footer clearfix">

                <div class="container flex flex-wrap">
                    <template>
                        <!-- <vx-card title="Consulta de precios por Producto"> -->
                        <h5 class="pl-4 pt-2">Consulta de precios por Producto</h5>
                        <div class="flex flex-wrap d-flex">
                            <vs-row class="mt-4">

                                <vx-input-group>
                                    <vs-col vs-type="flex flex-wrap flex-direction:row w-ful" vs-w="4">
                                        <div class="w-full pl-4">
                                            <validationProvider name="CodigoProducto">
                                                <vs-input v-model="estima_precio_productos.CodigoProducto" label="Codigo"
                                                    class="w-full" readonly />
                                            </validationProvider>
                                        </div>
                                    </vs-col>

                                    <vs-col vs-type="flex flex-wrap flex-direction:row w-ful" vs-w="8" class="pl-4 pr-4">
                                        <div class="w-full">
                                            <validationProvider name="CodigoProducto">

                                                <BuscaProductos v-model="estima_precio_productos.CodigoProducto" n
                                                    label="Descripcion" type="input"
                                                    :disabled_busqueda="activarBusquedaProd"
                                                    api="app/inventario/ListaProdsInventario"
                                                    api_campo_respuesta_mostrar="Nombre" :api_campos="['Codigo', 'Nombre']"
                                                    :api_titulos="['Codigo', 'Nombre']" api_campo_respuesta="Nombre"
                                                    :api_campo_respuesta_estricto="false" :api_preload="true"
                                                    :disabled_texto="true"
                                                    :callback_buscar="ConsultaProductos().cargarDatosProducto" :api_filtro="{
                                                        Empresa: '',
                                                        Clase: SERVICIOS,
                                                        Nombre: ''
                                                    }">
                                                </BuscaProductos>
                                            </validationProvider>
                                        </div>
                                    </vs-col>
                                </vx-input-group>
                            </vs-row>
                            <br>
                            <br>

                            <vs-divider> </vs-divider>
                            <br>
                            <br>

                            <vs-row>

                                <vx-input-group>
                                    <vs-col vs-type="flex flex-wrap" vs-w="6">
                                        <div class="w-1/3  p-2">
                                            <Quantity ref="QuantityComponent" :quantity="CantidadProductos"
                                                @input="onUpdateProp" readonly>

                                            </Quantity>
                                        </div>
                                    </vs-col>

                                    <vs-col vs-type="flex flex-wrap" vs-w="6">
                                        <div class="w-ful  pl-2 p-2">
                                            <vx-input-group>
                                                <template slot="prepend">
                                                    <div class="prepend-text currency text-dark">
                                                        <span>Q</span>
                                                    </div>
                                                </template>

                                                <vs-input v-model="estima_precio_productos.PrecioProducto" class="w-full"
                                                    type='number' readonly step='0.01' />

                                            </vx-input-group>
                                        </div>
                                    </vs-col>
                                </vx-input-group>
                                <br>
                                <br>

                                <vs-divider> </vs-divider>
                                <br>
                                <vs-col vs-type="flex flex-wrap" vs-w="6">
                                    <div class="w-5/6  p-2">

                                        <vs-button @click="addNewRow">
                                            Cargar
                                        </vs-button>

                                    </div>
                                </vs-col>
                            </vs-row>
                        </div>
                        <!-- </vx-card> -->
                    </template>
                </div>
            </div>

            <div class="total_cost_footer">

                <div class="container">

                    <div style="overflow:scroll;height:100%;width:100%;overflow:auto">

                        <vs-table2 :data="ListaProductos" >

                            <template slot="thead">

                                <th order="Cantidad" width="20px">Cantidad</th>
                                <th order="CodigoProducto" width="100px"> Codigo Producto</th>
                                <th order="DescripcionProducto" width="350px">Descripcion Producto</th>
                                <th order="UnidadDeMedida" width="50px">U/Medida</th>
                                <th order="UnidadDeMedida" width="50px">Precio Producto</th>
                                <th order="PrecioProducto" width="50px">Total Por Producto</th>
                                <th order="Accion" width="20px">Eliminar</th>

                            </template>
                            <template slot-scope="{data}">

                                <tr v-for="( ListaProductos, k) in data" :key="k">

                                    <vs-td2>
                                        <!-- <vs-input style="width: 100%; text-align: center; vertical-align: middle;"
                                            readonly v-model="ListaProductos.CantidadProducto" /> -->
                                        {{ ListaProductos.CantidadProducto }}
                                    </vs-td2>
                                    <vs-td2>

                                        <!-- <vs-input style="width: 100%; text-align: center; vertical-align: middle;"
                                            readonly v-model="ListaProductos.CodigoProducto" /> -->
                                        {{ ListaProductos.CodigoProducto }}

                                    </vs-td2>
                                    <vs-td2>
                                        <!-- <vs-input style="width: 100%; text-align: center; vertical-align: middle;"
                                            readonly v-model="ListaProductos.DescripcionProducto" /> -->
                                        {{ ListaProductos.DescripcionProducto }}
                                    </vs-td2>
                                    <vs-td2>
                                        <!-- <vs-input style="width: 100%; text-align: center; vertical-align: middle;"
                                            readonly v-model="ListaProductos.UnidadDeMedida" /> -->
                                        {{ ListaProductos.UnidadDeMedida }}
                                    </vs-td2>
                                    <vs-td2>
                                        <!-- <vs-input style="width: 100%; text-align: center; vertical-align: middle;"
                                            readonly v-model="ListaProductos.PrecioProducto" /> -->
                                        {{
                                            ListaProductos.PrecioProducto.toLocaleString("es-GT",
                                                { style: "currency", currency: "GTQ" })
                                        }}

                                    </vs-td2>
                                    <vs-td2 s>
                                        {{
                                            parseFloat(ListaProductos.PrecioProducto *
                                                ListaProductos.CantidadProducto).toLocaleString("es-GT",
                                                    { style: "currency", currency: "GTQ" })
                                        }}
                                    </vs-td2>
                                    <vs-td2>
                                      
                                          <vs-button 
                                            size="small"
                                            color="danger" icon-pack="fas" icon="fa-trash-alt" @click="deleteRow(k, '')"></vs-button>
                                                                            
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>

                </div>
            </div>

            <div class="preps_footer">
                <div class="container">
                    <vs-textarea class="textarea clearfix  " id="Preparaciones" v-model="Preparaciones" readonly rows=4 />
                </div>

            </div>


        </div>

    </vx-card>
</template>



<script>


import Quantity from '@/components/sermesa/global/Quantity.vue'
import BuscaProductos from '@/components/sermesa/global/SMBuscar.vue'

const SERVICIOS=3

export default {
    data() {
        return {
            SERVICIOS,
            NivelesPrecio: [],
            DescripcionNivelPrecio: '',
            ListaProductos: [{
                NivelPrecios: '',
                CantidadProducto: 0,
                CodigoProducto: '',
                DescripcionProducto: '',
                UnidadDeMedida: '',
                PrecioProducto: '',
                TotalPorProducto: 0,
                ProductoPrecio: '',
                Preparacion: ''

            }],
            estima_precio_productos: {
                NivelPrecios: '',
                CantidadProducto: '',
                CodigoProducto: '',
                DescripcionProducto: '',
                UnidadDeMedida: '',
                PrecioProducto: '',
                TotalPorProducto: 0,
                ProductoPrecio: '',
                Preparacion: ''

            },
            datosPaciente: {
                NombrePaciente: '',
                PorcentajeCoaseguro: '',
                PacMontoCopago: ''
            },
            TotalSinSeguro: 0,
            MontoCoaseguro: 0,
            MontoCopago: 0,
            SumaTotal: 0,
            Preparaciones: [],
            CantidadProductos: 1,
            activarBusquedaProd: true



        }
    }, computed: {
        resetProps() {
            return { ...this.estima_precio_productos.CantidadProducto };
        },
    },
    components: {
        BuscaProductos,
        Quantity
    },
    mounted: function () {
        this.ListaProductos = []
    },
    watch: {
        'estima_precio_productos.NivelPrecios': function (value) {

            this.ConsultaProductos().ActualizaTotal(value)

            if (value)
                this.activarBusquedaProd = false

        }
    },
    methods: {
        ConsultaProductos() {
            return {

                cargarDatosProducto: (datos) => {
                    
                    this.estima_precio_productos.CantidadProducto = this.CantidadProductos
                    this.estima_precio_productos.CodigoProducto = datos.Codigo
                    this.estima_precio_productos.DescripcionProducto = datos.Nombre
                    this.estima_precio_productos.UnidadDeMedida = datos.PresentacionUnidad
                    this.estima_precio_productos.ProductoPrecio = datos.ProdPrecio
                    this.estima_precio_productos.Preparacion = datos.Preparacion
                    let ListaPrecios = JSON.parse(this.estima_precio_productos.ProductoPrecio)
                    let NivelPrecios = this.estima_precio_productos.NivelPrecios
                    this.estima_precio_productos.PrecioProducto = (ListaPrecios.findIndex(element => element.NivelPrecios === NivelPrecios.toString()) < 0) ? 0 : ListaPrecios.find((element) => element.NivelPrecios == NivelPrecios?.toString() || '').Precio

                },
                cargarnivelesPrecios: (datos) => {
                    
                    this.estima_precio_productos.NivelPrecios = datos.Nivel
                },
                ActualizaTotal: (value) => {
                    this.ListaProductos = this.ListaProductos.map(function (productos) {

                        let ListaPrecios = JSON.parse(productos.ProductoPrecio)

                        return {
                            ...productos,
                            PrecioProducto: (ListaPrecios.findIndex(element => element.NivelPrecios === value.toString()) < 0) ? 0 : ListaPrecios.find((element) => element.NivelPrecios == value.toString()).Precio
                        };
                    })

                    this.ConsultaProductos().calculaTotal()
                },
                LimpiaConsultaProductos: () => {
                    this.estima_precio_productos.CantidadProducto = 1
                    this.estima_precio_productos.CodigoProducto = ''
                    this.estima_precio_productos.DescripcionProducto = ''
                    this.estima_precio_productos.UnidadDeMedida = ''
                    this.estima_precio_productos.PrecioProducto = ''
                    this.CantidadProductos = 1

                },
                calculaTotal: () => {

                    this.TotalSinSeguro = this.ListaProductos.reduce(function (sum, product) {
                        var lineTotal = product.CantidadProducto * product.PrecioProducto
                        if (!isNaN(lineTotal)) {
                            return sum + lineTotal;
                        }
                    }, 0)

                    let monto_Copago = (this.datosPaciente.PacMontoCopago.trim() === "") ? 0 : this.datosPaciente.PacMontoCopago
                    this.MontoCopago = monto_Copago
                    let totalSinSeguro = this.TotalSinSeguro
                    this.MontoCoaseguro = (totalSinSeguro * parseFloat((this.datosPaciente.PorcentajeCoaseguro / 100))).toFixed(2)
                    this.SumaTotal = (parseFloat(this.MontoCoaseguro) === 0 ? this.TotalSinSeguro : parseFloat(this.MontoCoaseguro)) + parseFloat(monto_Copago)

                }



            }
        },
        addNewRow() {
            


            if (this.estima_precio_productos.CodigoProducto === null) {

                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Alerta',
                    text: 'Debe elegir un producto',
                })
                return
            }



            let productoElegido = false
            Object.keys(this.ListaProductos).forEach(key => {

                if (this.ListaProductos[key].CodigoProducto === this.estima_precio_productos.CodigoProducto) {
                    productoElegido = true
                }
            });

            if (productoElegido) {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Elegir otro producto',
                    text: 'Producto ya elegido',
                })
                return
            }

            this.ListaProductos.push(
                {
                    CantidadProducto: this.estima_precio_productos.CantidadProducto,
                    CodigoProducto: this.estima_precio_productos.CodigoProducto,
                    DescripcionProducto: this.estima_precio_productos.DescripcionProducto,
                    UnidadDeMedida: this.estima_precio_productos.UnidadDeMedida,
                    PrecioProducto: this.estima_precio_productos.PrecioProducto,
                    ProductoPrecio: this.estima_precio_productos.ProductoPrecio,
                    Preparacion: this.estima_precio_productos.Preparacion
                });


            if (this.estima_precio_productos.Preparacion !== null) {
                this.fillPreparation(this.estima_precio_productos.DescripcionProducto, this.estima_precio_productos.Preparacion)
            }
            
            this.ConsultaProductos().LimpiaConsultaProductos()
            this.ConsultaProductos().calculaTotal()

        },
        deleteRow(index) {
            this.ListaProductos.splice(index, 1)
            this.ConsultaProductos().calculaTotal()
            if (this.ListaProductos.length === 0) {
                this.Preparaciones = ''
                return
            }
            this.Preparaciones = ''
            Object.keys(this.ListaProductos).forEach(key => {

                if (this.ListaProductos[key].Preparacion !== null) {
                    this.Preparaciones = this.Preparaciones.concat(' ', (this.ListaProductos[key].DescripcionProducto?.toUpperCase() || ''), ' ', (this.ListaProductos[key].Preparacion?.toUpperCase() || ''), '\r\n')
                }
            });
            this.ConsultaProductos().LimpiaConsultaProductos()

        },
        fillPreparation(examen, preparacion) {
            this.Preparaciones += ' ' + examen.toUpperCase() + ' ' + preparacion.toUpperCase() + '\r\n'
        }, onUpdateProp(data) {
            this.CantidadProductos = data
            this.estima_precio_productos.CantidadProducto = data
        }

    }

}


</script>



<style scoped>
* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

.pdiv_container {
    display: grid;
    grid-gap: 5px;
    width: 100%;
    height: 100%;
    grid-template-areas: "header_valores   header_totals"
        "header_valores    total_cost_footer"
        "prod_footer       total_cost_footer"
        "prod_footer       preps_footer";
    grid-template-columns: 35% 65%;
    grid-template-rows: 20% 15% 35% 30%;

}

.pdiv_container>div {
    border: 1px solid #888;
}

.header {
    grid-area: header;
    display: flex;
}

.header_totals {
    grid-area: header_totals;
    display: flex;
}

.header_valores {
    grid-area: header_valores;
    display: flex;
}

.prod_footer {
    grid-area: prod_footer;
    background-color: #F7F7F7;
    margin-top: 2px;
    display: flex;

}

.total_cost_footer {
    grid-area: total_cost_footer;
    margin-top: 2px;
    display: flex;

}

.preps_footer {
    grid-area: preps_footer;
    display: flex;
}

#inner-grid {
    display: grid;
    grid-template-columns: 100%;
    grid-template-rows: 100%;
    width: 100%;
    grid-gap: 5px;
    padding: 4px;
    display: flex;
}

#inner-grid>div {
    border-radius: .25rem;
    display: flex;
}

#nested-grid {
    display: grid;
    grid-template-columns: 100%;
    grid-template-rows: 100%;
    width: 100%;
    padding: 1px;
    display: flex;
}

#nested-grid>div {
    border-radius: .25rem;
    display: flex;
}

.clearfix {
    overflow: auto;
    display: flow-root;
}

.currency {
    background-color: rgb(139, 151, 115) !important;
}


.vs-table2 {
    margin: 0 auto;
    border-collapse: collapse !important;
    font-size: xx-small
}

.vs-td2 {
    font-size: 0.8rem !important;
    font-size: xx-small
}

thead th {
    display: block !important;
    font-size: 0.5em;
    height: 5px;
}

tbody {
    height: 10em;
    overflow-y: scroll !important;
    display: block !important;
}

h1 {
    font-size: 2em;
}

h2 {
    font-size: 1.5em;
}

p {
    font-size: 1em;
}

/* Increase font sizes by 1.5x on bigger screens */
@media (min-width: 48rem) {
    h1 {
        font-size: 3em;
    }

    h2 {
        font-size: 2.25em;
    }

    p {
        font-size: 1.5em;
    }
}

.textarea {
    /* border: 1px solid #999999; */
    padding: 1%;
    resize: none;
    font-size: 1em;
    /* font-weight: bold; */
    font-family: Verdana, Arial, Helvetica, sans-serif;
    overflow: scroll;
    height: 100%;
    width: 100%;
    overflow: auto;
    resize: none;
    -webkit-text-stroke: 0.5px #60a0cb;

    /* font-size: 1em;
    
    font-family: Verdana, Arial, Helvetica, sans-serif;
    border: 1px solid black; */

}

.vs-card {
    padding: 5px;

}
</style>
