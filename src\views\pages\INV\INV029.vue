<template>
<vx-card title="Laboratorios (Marcas)">

    <div class="content content-pagex">
        <vs-divider></vs-divider>
        <div class="terms">
            <div style="margin: 15px" class="flex flex-wrap">
                <vs-button color="primary" style="float:left;margin: 5px" type="filled" icon-pack="fas" icon="fa-plus" @click="NuevoLaboratorio()"> Nuevo</vs-button>
                <br>
                <br>
            </div>
        </div>
        <div>

            <vs-divider></vs-divider>
            <div class="flex flex-wrap">
                <div class="xs:w-full md:w-6/12 lg:w-6/12 xl:w-6/12">
                    <vs-table2 max-items="10" pagination :data="ListaLaboratorio" search id="tb_departamentos">

                        <template slot="thead">
                            <th width="50">Código</th>
                            <th width="300">Nombre</th>
                            <th width="30">Editar</th>
                            <th width="30">Eliminar</th>
                        </template>
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 :data="data[indextr].Codigo">
                                    {{data[indextr].Codigo}}
                                </vs-td2>
                                <vs-td2 :data="data[indextr].Nombre">
                                    {{data[indextr].Nombre}}
                                </vs-td2>
                                <vs-td2 align="center">
                                    <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" @click="EditarLaboratorio(data[indextr])"></vs-button>
                                </vs-td2>
                                <vs-td2 align="center">
                                    <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="EliminarRegistro(data[indextr])"></vs-button>
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
                </div>
                <div class="padre xs:w-full md:w-6/12 lg:w-6/12 xl:w-6/12">
                    <div class="hijo">
                        <div class="xs:w-full md:w-1/12 lg:w-1/12 xl:w-1/12"></div>
                        <div class="xs:w-full md:w-10/12 lg:w-10/12 xl:w-10/12">
                            <vs-divider class="label-size">Laboratorio</vs-divider>
                            <br>
                            <div>
                                <ValidationProvider name="Código" rules="required|max:8" v-slot="{ errors }" class="required">
                                    <label class="label_sizem"> Código</label>
                                    <vs-input   v-model="CodigoLaboratorio" v-on:keyup = "CodigoLaboratorio = CodigoLaboratorio.toUpperCase()" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="BloquearCodigo" />
                                    </ValidationProvider>
                            </div>
                            <br>
                            <br>
                            <div>
                                <ValidationProvider name="Nombre" rules="required|max:40" v-slot="{ errors }" class="required">
                                    <label class="label_sizem"> Nombre</label>
                                    <vs-input    v-model="NombreLaboratorio" v-on:keyup = "NombreLaboratorio = NombreLaboratorio.toUpperCase()" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"  :disabled="BloquearComponentes" />
                                </ValidationProvider>
                            </div>
                            <br>
                            <br>
                            
                            <vs-button  color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" @click="GuardarLaboratorio()" id="btn_confirmacion" :disabled="BloquearComponentes"> Grabar</vs-button>
                        </div>
                        <div class="xs:w-full md:w-1/12 lg:w-1/12 xl:w-1/12"></div>
                    </div>
                </div>
            </div>

        </div>
    </div>


</vx-card>
</template>

<script>
/**
 * @General
 * Registro de departamentos
 */
//import Multiselect from 'vue-multiselect'
//import "vue-multiselect/dist/vue-multiselect.min.css"
export default {

    components: {

    },
    data() {
        return {

            Titulo_emergente: '',
            Corporativo_Sesion: '',
            res_variables: false, //Var para validación de campos
            NombreLaboratorio: '',
            BloquearComponentes: true,
            ListaLaboratorio: [],
            CodigoLaboratorio: '',
            Operacion: '',
            BloquearCodigo: true,
            activoF: ''

        };
    },
    mounted() {
        this.Consultar_Laboratorio();
    },
    methods: {
        //Devolver los obejetos seleccionados de los  combobox
        Consultar_Laboratorio() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_ordencompra/consulta_marcas', {
                    Opcion: 'C'
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Laboratorio',
                            text: resp.data.mensaje,
                        })
                        this.ListaLaboratorio = [];
                    } else {
                        this.ListaLaboratorio = resp.data.json;
                    }
                })
        },

        NuevoLaboratorio() {
            this.Titulo_emergente = 'Nuevo Laboratorio';
            this.CodigoLaboratorio = "";
            this.NombreLaboratorio = "";
            this.BloquearComponentes = false;
            this.BloquearCodigo= false;
            this.Operacion = 'I'; // Indica la variable que es un nuevo registro

        },
        EditarLaboratorio(datos_editar) {
            this.Operacion = 'A'; // Indica la variable que es una Edición de registro
            this.CodigoLaboratorio = datos_editar.Codigo;
            this.NombreLaboratorio = datos_editar.Nombre;
            this.BloquearComponentes = false;
            this.BloquearCodigo= true;
           
        },

        GuardarLaboratorio() {
            /**
             * @General
             * Función Permite  actualizar y  almacenar un nuevo registro;
             */
            this.res_variables = false;
            //Condicionante si es Nuevo registro ó Actualización

            //    Validaciones de variables
            this.res_variables = this.Validacion_Campos('A', 'Código', this.CodigoLaboratorio, true);
            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('A', 'Nombre', this.NombreLaboratorio, true);

            if (this.res_variables) {
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_ordencompra/mantenimiento_marcas', {
                        CodigoMarca: this.CodigoLaboratorio,
                        NombreMarca: this.NombreLaboratorio,
                        Opcion: this.Operacion,
                    })
                    .then(resp => {
                        this.$vs.loading.close();
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Laboratorio',
                                text: resp.data.mensaje,
                            })

                        } else {
                            this.Consultar_Laboratorio()
                            this.LimpiarCampos()
                        }
                    })
                    this.LimpiarCampos()
                  
            }

        },
        EliminarRegistro(datos_eliminar) {
            /**
             * @General
             * Función eliminar registro;
             */

            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Confirmación',
                text: '¿Desea eliminar el laboratorio \'' + datos_eliminar.Codigo + ' - ' + datos_eliminar.Nombre + '\'? ',
                acceptText: 'Aceptar',
                cancelText: 'Cancelar',
                accept: () => {
                    const url = this.$store.state.global.url
                    this.$vs.loading();
                    this.axios.post(url + 'app/v1_ordencompra/mantenimiento_marcas', {
                            CodigoMarca: datos_eliminar.Codigo,
                            NombreMarca: datos_eliminar.Nombre,
                            Opcion: 'E'

                        })
                        .then(resp => {
                            this.$vs.loading.close();
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Laboratorio',
                                    text: resp.data.mensaje,
                                })

                            } else {
                                this.ListaLaboratorio = [];
                                this.Consultar_Laboratorio();
                                this.$vs.notify({
                                    color: '#danger',
                                    title: 'Laboratorio',
                                    text: resp.data.mensaje,
                                });

                            }
                        })
                        .catch(() => {
                            this.$vs.loading.close();
                        })
                    this.$vs.loading.close();

                }
            })
        },
        Validacion_Campos(Tipo, Nombre, valor, obligatorio) {
            /**
             * @General
             * Tipo : N = Númerio, Nombre = Descripción del campo, Valor = A evaluar, Obligatorio= Verdadero o falso.
             * Devuelve un mensaje al usuario si las validaciones no se cumple y aborta, de lo contrario devuelve verdadero
             */
            if (Tipo == 'N') {
                if (valor <= 0) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Laboratorio',
                        text: 'Campo \'' + Nombre + '\' es obligatorio.',
                    });
                    return false;
                } else {
                    return true;
                }
            } else {
                if (valor == '' && obligatorio == true) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Laboratorio',
                        text: 'Campo \'' + Nombre + '\' es obligatorio.',
                    });
                    return false;
                } else {
                    return true;
                }
            }
        },
        LimpiarCampos(){
            this.CodigoLaboratorio = ''
            this.NombreLaboratorio = ''
            this.BloquearCodigo = true
            this.BloquearComponentes = true
        }

    }
}
</script>

<style scoped>
.footer {
    height: 50px;
    margin-top: -65px;
    margin-left: 10px;
}

.label-size {
    font-size: 20px;
    font-weight: bold;
}

.label_sizem {
    font-size: 16px;
    font-weight: bold;
}

.padre {
    position: relative;
}

.hijo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
</style>
