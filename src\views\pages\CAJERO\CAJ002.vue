<template>
<vx-card title="Reporte">
    <form v-on:submit.prevent="cargar_orden()">
        <div class="flex flex-wrap">
            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <label class="vs-input--label">Admisión</label>
                    <vx-input-group class="">
                        <vs-input  class="w-full" v-on:change="info.admision=info.admision.toUpperCase()" v-model="info.admision" :disabled="bloqueoBusqueda" />
                        <template slot="append">
                            <div class="append-text btn-addon">
                                <button type="submit" v-show="false" name="button"></button>
                                <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_admision()" icon="icon-search" v-if="bloqueoBusqueda==false"></vs-button>
                                
                            </div>
                        </template>
                    </vx-input-group>
            </div>
        </div>
    </form>
</vx-card>
</template>

<script>

export default {
    data() {
        return {
            bloqueoBusqueda:false,
            info:{
                admision:''
            }
           
        }
    },
    components: {
        // flatPickr
    },
    computed: {
    },
    methods: {

    },
    created() {

    }
}
</script>

<style>
.button {
    margin: 0px !important;
}

.btnx {
    margin-left: 10px !important;
    background: red;
    border-radius: 5px 0px 0px 5px;
}

.btn-drop {
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: red;

}

.popup-generar {
    height: 100%
}
</style>
