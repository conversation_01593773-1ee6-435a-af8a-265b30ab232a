<template>
<vs-row class="p-2 w-full historial-container">
    <vs-col vs-w="3" vs-justify="center" vs-align="center" class="pr-2">
        <vx-card :title="textoFiltro">
            <!-- Se dejó toda la configuración, ya que si se usa la configuración global, se reinicia el pageIndex cada vez que se selecciona una fecha -->
            <DxDataGrid :ref="dataGridFechaKey" v-bind="DefaultDxGridConfiguration" :headerFilter="{visible:true, allowSearch:true}" :data-source="fechas" :width="'100%'" :height="'100%'" @selection-changed="onSelectionChanged" @cell-prepared="onCellPrepared">
                <DxDataGridSelection mode="single" />
                <DxDataGridColumn :width="120" data-field="Fecha" caption="Fecha" format="dd/MM/yyyy HH:mm" data-type="date" alignment="center" />
                <DxDataGridColumn :width="100" data-field="Examen" :allowHeaderFiltering="false" caption="Exámen" data-type="string" alignment="center" />
            </DxDataGrid>
        </vx-card>
    </vs-col>
    <!-- <vs-col vs-w="3" class="pr-2">

    </vs-col> -->
    <vs-col vs-justify="center" vs-align="center" vs-w="9">
        <!-- <div class="pb-0 pr-2 pl-3"> -->
        <vx-card :title="textoImagenes" id="pdfViewer">
            <vs-row vs-justify="flex-end" vs-align="center" class="pb-2">
                <GridToolBar v-bind="$props" :visible="ShouldRender" :pdfExportItems="[{text:'Exámenes', reportName: 'Informes por Orden'}]" @refresh="refresh" :showItems="botones">
                    <DxButton :visible="showImage" id="botonBarra" type="default" styling-mode="outlined" hint="Ver imagenes" @click="openImage">
                        <font-awesome-icon :icon="['fas', 'image']" style="font-size: 18px;" />
                    </DxButton>
                    <DxButton :visible="showImage" id="botonBarra" type="default" styling-mode="outlined" hint="Ver imagenes DICOM" @click="openDICOM">
                        <font-awesome-icon :icon="['fas', 'panorama']" style="font-size: 18px;" />
                    </DxButton>
                </GridToolBar>
            </vs-row>
            <div width="100%" style="height: 790px">
                <object v-if="archivoPDF!=''" type="application/pdf" :data="archivoPDF" ref="pdfDocument" width="100%" height="98%">
                    <p>Su navegador es incompatible con el plugin pdf.</p>
                </object>
            </div>
        </vx-card>
        <!-- </div> -->
    </vs-col>
</vs-row>
</template>

<script>
import CustomStore from 'devextreme/data/custom_store'
import GridToolBar from './GridToolBar.vue'
import {
    DefaultDxGridConfiguration
} from './data'
import io from 'socket.io-client'

const dataGridRefKey = 'gridExamen'
const dataGridFechaKey = 'gridFiltro'

export default {
    name: 'Radiologia',
    components: {
        GridToolBar,
    },
    data() {
        return {
            examenes: [],
            examenesDataSource: new CustomStore({
                key: "id",
                load: () => {
                    return this.laboratorios;
                },
            }),
            fechas: [], //Listado de examenes para filtrar laboratorios
            botones: [], //Vector para manejar los botones que se muestran sobre la tabla
            reporteProps: [],
            textoFiltro: 'Filtrar exámen',
            textoImagenes: '',
            archivoPDF: '',
            showImage: false,
            showDICOM: false,
            noDataText: 'Seleccione una fecha para cargar información',
            dataGridRefKey,
            dataGridFechaKey,
            DefaultDxGridConfiguration,
            IpLocal: '',
            socket: io('https://sighos-linea.hospitaleslapaz.com/'),
            ComandoWindows: '',
            estado: false,
            tipoOrdenSeleccionado: null,
            numeroOrdenSeleccianado: null
        }
    },
    props: {
        IdCita: null,
        IdCliente: null,
        IdPaciente: null,
        IdConsulta: null,
    },
    methods: {
        submit() {
            this.dataGrid.saveEditData()
        },
        getFechasExamenes() {
            if (this.IdPaciente !== undefined && this.IdPaciente !== null && this.IdPaciente !== '') {
                this.axios.post('/app/v1_Historial_Clinico/BusquedaExamenes', {
                        IdPaciente: this.IdPaciente
                    })
                    .then(resp => {
                        this.fechas = resp.data.json.map((x, index) => {
                            return {
                                id: index,
                                Fecha: x.Fecha,
                                tipoorden: x.TipoOrden,
                                orden: x.Orden,
                                linea: x.LineaOrden,
                                Examen: x.Nombre,
                                Codigo: x.Producto,
                                Imagen: x.ImagenesUp
                            }
                        })
                        this.dataGridFecha.columnOption('Fecha', 'sortOrder', 'desc')
                    })
            } else {
                this.fechas = [];
                this.refreshFechaDataGrid();
            }
        },
        refreshDataGrid() {
            this.dataGrid.refresh()
                .then(function () {

                });
            this.dataGrid.clearSelection();
            this.dataGrid.clearSorting();
            this.dataGrid.pageIndex(0);
        },
        refreshFechaDataGrid() {
            this.dataGridFecha.pageIndex(0);
            this.dataGridFecha.clearSelection();
            this.dataGridFecha.clearSorting();
            this.dataGridFecha.columnOption('Fecha', 'sortOrder', 'desc')
            this.botones = [];
        },
        formatFecha(date) {
            const year = new Date(date).toLocaleString('default', {
                year: 'numeric'
            });
            const month = new Date(date).toLocaleString('default', {
                month: '2-digit'
            });
            const day = new Date(date).toLocaleString('default', {
                day: '2-digit'
            });

            return [year, month, day].join('');
        },
        //Método para detectar cuando se selecciona una fila de la tabla de filtrado de fecha
        onSelectionChanged(e) {
            if (e.selectedRowsData.length > 0) {
                this.showImage = false
                this.reporteProps = e.selectedRowsData[0];
                this.botones = ['refresh'];
                this.textoFiltro = 'Orden ' + this.reporteProps.tipoorden + ' ' + this.reporteProps.orden
                this.showImage = Boolean(this.reporteProps.Imagen)
                this.textoImagenes = this.showImage ? 'El estudio posee imagenes para mostrar' : 'El estudio no posee imagenes para mostrar'
                this.tipoOrdenSeleccionado = this.reporteProps.tipoorden
                this.numeroOrdenSeleccianado = this.reporteProps.orden
                this.getPDF();
                this.getPathImagenes(this.reporteProps.tipoorden, this.reporteProps.orden);
            }
        },
        refresh() {
            this.examenes = [];
            this.textoFiltro = 'Filtrar exámen'
            this.textoImagenes = ''
            this.noDataText = 'Seleccione una fecha para cargar información'
            this.showImage = false
            this.archivoPDF = ''
            this.refreshFechaDataGrid();
        },
        onCellPrepared(e) {
            if (e.rowType === 'data' && e.column.dataField === 'Examen') {
                if (e.data.LineaExamen === '-1') {
                    e.cellElement.style.cssText = "color: red; text-align: center;"
                }
            }
        },
        openImage() {
            this.$socke_up(this.ComandoWindows)
        },
        openDICOM() {
            window.open('http://srvpacs032en/Synapse/WebQuery/Index?path=/All%20Studies/&filter=accessionnumber=' + this.tipoOrdenSeleccionado + '-' + this.numeroOrdenSeleccianado, '_blank')
        },
        getPathImagenes(Tipo, Orden) {
            this.axios.post('/app/v1_Historial_Clinico/BusquedaExamenesPath', {
                    IdPaciente: this.IdPaciente,
                    TipoOrden: Tipo,
                    Orden: Orden
                })
                .then(resp => {
                    let v = resp.data.json.map((x, index) => {
                        return {
                            id: index,
                            Aplicacion: x.Aplicacion,
                            Path: x.Path
                        }
                    })
                    this.ComandoWindows = v[0].Aplicacion
                })
        },
        getPDF() {
            this.axios.post("/app/reporte/ReporteGenerador", {
                    Nombre: 'Informes por Orden',
                    Opciones: {
                        tiporeporte: "application/pdf",
                        ...this.reporteProps
                    }
                }, {
                    responseType: 'arraybuffer'
                })
                .then(resp => {
                    this.archivoPDF = 'data:application/pdf;base64,' + Buffer.from(resp.data, 'binary').toString('base64') + '#view=FitH';
                })
        },
    },
    mounted() {
        this.IpLocal = localStorage.getItem('IpLocal');
        this.getFechasExamenes();
    },
    watch: {
        'IdPaciente'() {
            this.refresh()
            this.getFechasExamenes();
        },
    },
    computed: {
        ShouldRender() {
            return Boolean(this.IdPaciente)
        },

        dataGrid: function () {
            return this.$refs[dataGridRefKey].instance;
        },

        dataGridFecha: function () {
            return this.$refs[dataGridFechaKey].instance;
        }
    },
}
</script>

<style scoped>
#pdfViewer .dx-button-content {
    height: 50px;
    width: 50px;
}

#pdfViewer .vx-card__body {
    padding-top: 0 !important;
}
</style>
