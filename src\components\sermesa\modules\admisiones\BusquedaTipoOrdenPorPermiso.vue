<template>
    <div class="sm:w-full md:w-full lg:w-2/12 xl:w-1/12 m-1" id="BusquedaTipoOrdenPorPermiso">
        <div>
            <ValidationProvider rules="required" v-slot="{ errors }">
                <SM-Buscar class="w-full" label="*Tipo de Orden" v-model="tipoOrden" api="app/v1_JefeCaja/ObtenerTiposOrdenesPorRol" :api_campos="['Codigo','Nombre']" :api_titulos="['Tipo de Orden','Descripción']" :api_filtro="{'Activa':0,'opcion':'C','subOpcion':'3','filtroTipoOrdenHospital':filtroTipoOrden.toString(),'filtroTipoOrdenGeneral':filtroTipoOrdenGeneral.toString()}" 
                            api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Codigo" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="true" 
                            :callback_buscar="cargarTipoOrden" 
                            :danger="errors.length > 0"
                            :danger-text="(errors.length > 0) ? errors[0] : null"/>                                
            </ValidationProvider>            
            <div v-if="descripcionOrden && descripcionOrden!=''" style="background-color:#ecf0f1;">
                {{ descripcionOrden }}
            </div>
        </div>
    </div>    
</template>
<script>
export default {
    name: "BusquedaTipoOrdenPorPermiso",
    data() {
        return {
            tipoOrden: null,
            descripcionOrden: null
        };
    },
    methods: {
        cargarTipoOrden(datos) {                       
            this.tipoOrden = datos.Codigo
            this.descripcionOrden = datos.Nombre                                    
            this.$emit('datos_tipo_orden',datos)     
        },
        limpiarTipoOrden(){
            this.tipoOrden = null
            this.descripcionOrden = null     
        }
    },
    props: {
        filtroTipoOrden: {
            type: Array,
            default(){
                return []
            } 
        },
        filtroTipoOrdenGeneral: {
            type: Array,
            default(){
                return []
            }            
        }
    }

}
</script>