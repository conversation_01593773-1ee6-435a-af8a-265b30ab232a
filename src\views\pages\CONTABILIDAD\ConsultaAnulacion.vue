<template>
<div>
    <form @submit="handleSubmit">
        <DxForm :ref="formConsulta" :form-data.sync="formulario" label-mode="floating">
            <DxFormGroupItem :col-count="2">
                <DxFormGroupItem>
                    <DxFormItem data-field="Fecha" editor-type="dxDateBox" :validationRules="[{ type: 'required' }]" :editor-options="{ dataType: 'date', displayFormat: 'dd/MM/yyyy' }" />
                    <DxFormItem data-field="Cuenta" editor-type="dxSelectBox" :editor-options="{ width: 'auto', searchEnabled: true, items: cuentas,  displayExpr: 'Valor', valueExpr: 'Codigo' }" :validationRules="[{ type: 'required' }]" />
                </DxFormGroupItem>
                <DxFormGroupItem>
                    <DxFormItem data-field="ChequeInicial" editor-type="dxNumberBox" :editor-options="{ min: 0, format: '#0', step: 0 }">
                        <DxFormLabel text="Del número" />
                    </DxFormItem>
                    <DxFormItem data-field="ChequeFinal" editor-type="dxNumberBox" :editor-options="{ min: 0, format: '#0', step: 0 }">
                        <DxFormLabel text="Al número" />
                    </DxFormItem>
                </DxFormGroupItem>
                <DxFormButtonItem :col-span="2" :button-options="buttonConsultar" name="Consultar" horizontal-alignment="center" verical-alignment="center" />
            </DxFormGroupItem>
            <DxFormGroupItem :visible="infoCheque.Numero != null" class="pt-4" caption=" ">
                <DxFormItem template="navegacion" />
                <DxFormItem template="infoCheque" />
            </DxFormGroupItem>

            <template #navegacion="{}">
                <div class="navigation-buttons flex flex-row">
                    <vs-button class="ml-1 text-lg" type="filled" color="rgb(110, 110, 110)" size="small" @click="BuscarCheques(chequeInicial - 1, 1)">
                        <vs-tooltip text="Primero">
                            <font-awesome-icon :icon="['fas', 'backward-step']" />
                        </vs-tooltip>
                    </vs-button>
                    <vs-button class="ml-1 text-xl" type="filled" color="rgb(110, 110, 110)" size="small" @click="BuscarCheques(chequeActual, chequeActual == chequeInicial ? 0 : -1)">
                        <vs-tooltip text="Anterior">
                            <font-awesome-icon :icon="['fas', 'caret-left']" />
                        </vs-tooltip>
                    </vs-button>
                    <vs-button class="ml-1 text-xl" type="filled" color="rgb(110, 110, 110)" size="small" @click="BuscarCheques(chequeActual, chequeActual == chequeFinal ? 0 : 1)">
                        <vs-tooltip text="Siguiente">
                            <font-awesome-icon :icon="['fas', 'caret-right']" />
                        </vs-tooltip>
                    </vs-button>
                    <vs-button class="ml-1 text-lg" type="filled" color="rgb(110, 110, 110)" size="small" @click="BuscarCheques(chequeFinal + 1, -1)">
                        <vs-tooltip text="Último">
                            <font-awesome-icon :icon="['fas', 'forward-step']" />
                        </vs-tooltip>
                    </vs-button>

                    <DxButton width="auto" height="36px" type="default" class="p-1 ml-1" styling-mode="contained" @click="MostrarCompras">
                        <font-awesome-icon class="mr-2" :icon="['fas', 'basket-shopping']" style="font-size: 18px; vertical-align: middle" />
                        <span>Compras</span>
                    </DxButton>

                    <DxButton :visible="infoCheque.Status !== 'A' && infoCheque.Status !== 'X'" width="auto" height="36px" type="danger" class="p-1 ml-1" styling-mode="contained" @click="AnularCheque">
                        <font-awesome-icon class="mr-2" :icon="['fas', 'ban']" style="font-size: 18px; vertical-align: middle" />
                        <span>Anular</span>
                    </DxButton>
                </div>
            </template>

            <template #infoCheque="{}">
                <div class="pt-4">
                    <Consulta :InformacionCheque="infoCheque" />
                </div>
            </template>
        </DxForm>
    </form>

    <DxPopup :wrapper-attr="popupAttributes" :visible.sync="mostrarModalCompras" :width="'80%'" height="auto" :show-title="true" :full-screen="false" :hide-on-outside-click="false" title="Compras" :showCloseButton="true">

        <DxDataGrid :ref="gridCompras" v-bind="DefaultDxGridConfiguration" :data-source="compras" :searchPanel="{visible: false}" :headerFilter="{visible:false, allowSearch:false}" :width="'100%'" height="auto" @editor-preparing="editorDatagridCompras" :on-saving="GuardarCambiosCompras" @init-new-row="AgregarCompra">
            <DxDataGridSelection mode="single" />

            <DxDataGridEditing :allow-updating="false" :allow-adding="true" :allow-deleting="true" mode="row" :use-icons="true" new-row-position="last" />

            <DxDataGridColumn width="5%" data-field="Tipo" alignment="center" data-type="string" :validation-rules="[{ type:'required', message: 'Ingrese el tipo de documento' }]" />
            <DxDataGridColumn width="10%" data-field="Proveedor" alignment="center" data-type="string" :validation-rules="[{ type:'required', message: 'Ingrese el código del proveedor' }]" />
            <DxDataGridColumn width="20%" :allow-editing="false" data-field="NombreProveedor" alignment="center" data-type="string" />
            <DxDataGridColumn width="10%" data-field="Documento" alignment="center" data-type="string" :validation-rules="[{ type:'required', message: 'Ingrese el código del documento' }]" />
            <DxDataGridColumn width="10%" :allow-editing="false" data-field="Fecha" alignment="center" data-type="date" format="dd/MM/yyyy" />
            <DxDataGridColumn width="15%" data-field="Monto" alignment="center" data-type="number" :customize-text="customizeTextValores" />
            <DxDataGridColumn width="15%" data-field="Saldo" alignment="center" data-type="number" :customize-text="customizeTextValores" />
            <DxDataGridColumn width="10%" :allow-editing="false" data-field="FechaRegistro" alignment="center" data-type="date" format="dd/MM/yyyy" />

            <!-- <DxDataGridColumn width="5%" type="buttons" alignment="center">
                <DxDataGridButton hint="Eliminar" icon="trash" :on-click="EliminarCompra" />
            </DxDataGridColumn> -->
        </DxDataGrid>
    </DxPopup>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration,
    customizeTextValores
} from './data'
import 'devextreme-vue/lookup'

import Consulta from './Consulta.vue';

const formConsulta = 'formConsulta'
const gridCompras = 'gridCompras'

export default {
    name: 'ConsultaAnulacion',
    components: {
        Consulta,
    },
    data() {
        return {
            DefaultDxGridConfiguration,
            formConsulta,
            gridCompras,

            formulario: {
                Fecha: null,
                Cuenta: null, // Codigo de la cuenta de banco de 2 dígitos
                NumeroCuenta: null, // Número de la cuenta bancaria
                BancoContabilidad: null, // Cuenta contable de la cuenta bancaria seleccionada
                ChequeInicial: null, // Primer cheque del rango a imprimir
                ChequeFinal: null, // Último cheque del rango a imprimir
                NombreCuentaBanco: null, // Nombre de la cuenta contable de la cuenta bancaria 
            },
            cuentas: [],

            buttonConsultar: {
                width: 'auto',
                icon: 'fas fa-magnifying-glass-dollar',
                text: 'Consultar',
                type: 'success',
                onClick: () => {
                    this.chequeInicial = this.formulario.ChequeInicial
                    this.chequeFinal = this.formulario.ChequeFinal
                    this.BuscarCheques(this.chequeInicial - 1, 1)
                },
                useSubmitBehavior: false,
            },

            popupAttributes: {
                class: 'ContenidoBancos'
            },

            cheques: [],

            //Solo se asigna con status para que no cause error al validarlo cuando se cargan los cheques para mostrar el botón de anular
            infoCheque: {
                Status: null
            },
            estadoChequeSeleccionado: null,
            periodo: null,
            cuentaIVACobrar: null,
            porcentajeIVA: null,
            retencion: null,
            chequeInterpretacion: null,
            indiceSeleccionado: null, //Variable para saber cual es el índice en la tabla de cheques del cheque seleccionado

            chequeInicial: null,
            chequeFinal: null,
            chequeActual: null,

            mostrarModalCompras: false,
            compras: [],
            proveedores: [],
        }
    },
    props: {
        ModalidadPago: null,
        TipoCheque: null,
        // ImpresionLotes: null,
        Corporativo: null
    },
    methods: {
        handleSubmit(e) {
            e.preventDefault()
        },
        customizeTextValores,

        CargarCuentas() {
            this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 1
                })
                .then(resp => {
                    this.cuentas = resp.data.json

                    this.formulario.Cuenta = this.cuentas[0].Codigo
                })
        },

        HabilitarBoton() {
            if (this.infoCheque.Status != 'A' && this.infoCheque.Status != 'X') {
                this.formConsultaInstance.itemOption('Consultar', 'visible', true)
            } else {
                this.formConsultaInstance.itemOption('Consultar', 'visible', false)
            }
        },

        async MostrarCompras() {
            await this.ConsultarCompras()
            this.gridComprasInstance.cancelEditData()
            this.gridComprasInstance.refresh()
            this.gridComprasInstance.repaint()
            this.mostrarModalCompras = true
        },

        ValidarCheque(cheque) {
            if (cheque !== null && cheque >= 0) {
                return true
            }
            return false
        },

        async BuscarCheques(cheque, direccion) {
            await this.axios.post('/app/v1_bancos/ConsultaAnulacion', {
                    Opcion: 1,
                    Cuenta: this.formulario.Cuenta,
                    TipoCheque: this.TipoCheque,
                    Fecha: this.formulario.Fecha,
                    ChequeInicial: cheque,
                    ChequeFinal: cheque,
                    Direccion: direccion
                })
                .then(async resp => {
                    // this.cheques = []
                    if (resp.data.json.length > 0) {
                        //Validación para no recargar información si la información del cheque sale del rango indicado
                        if (resp.data.json[0].Numero >= this.chequeInicial && resp.data.json[0].Numero <= this.chequeFinal) {
                            this.infoCheque = {}
                            this.infoCheque = resp.data.json[0]
                            this.chequeActual = this.infoCheque.Numero
                            this.estadoChequeSeleccionado = this.infoCheque.Status

                            await this.CargarRetencion(this.infoCheque.Numero)
                            await this.ChequeInterpretacion(this.infoCheque.Numero)
                        }
                    }
                })
        },

        SeleccionarCheque(e) {
            this.infoCheque = e.data
            this.indiceSeleccionado = e.rowIndex
            this.estadoChequeSeleccionado = e.data.Status
            this.ChequeInterpretacion(e.data.Numero)
            this.CargarRetencion(e.data)
        },

        ConsultarPeriodo() {
            this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 1,
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.periodo = resp.data.json[0].Codigo
                    }
                })
        },

        async ConsultarSaldoFinal(e) {
            return await this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 2,
                    Cuenta: e.Cuenta,
                    Periodo: e.Periodo
                })
                .then(resp => {
                    if (resp.data.json.length === 0) {
                        return false
                    } else if (resp.data.json[0].SaldoFinal !== null) {
                        return true
                    }
                })
        },

        async CargarCuentasDefault() {
            await this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 9
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.cuentaIVACobrar = resp.data.json[0].IVAPorCobrar
                    }
                })
        },

        CargarOpciones() {
            this.axios.post('/app/v1_bancos/Opciones', {
                    Opcion: 1
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.porcentajeIVA = parseFloat(resp.data.json[0].IVA / 100).toFixed(2)
                    }
                })
        },

        async CargarRetencion(e) {
            await this.axios.post('/app/v1_bancos/ConsultaAnulacion', {
                    Opcion: 4,
                    EmpresaCheque: e.Empresa,
                    ChequeInicial: e.Numero,
                    TipoCheque: e.Tipo,
                    Cuenta: e.Cuenta
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.retencion = resp.data.json[0].Monto
                    }
                })
        },

        AnularCheque() {
            if (this.estadoChequeSeleccionado == 'A') {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'No es posible anular',
                    acceptText: 'Aceptar',
                    text: 'El cheque ya ha sido anulado, no es posible anularlo nuevamente.',
                    buttonCancel: 'border',
                    accept: () => {
                        return
                    },
                })
                return
            } else {
                let saldo = this.ConsultarSaldoFinal(this.infoCheque)

                if (saldo) {
                    let text = '¿Está seguro de querer anular'

                    if (this.ModalidadPago == 'D') {
                        text = text + ' esta boleta?'
                    } else {
                        text = text + ' este cheque?'
                    }

                    this.$vs.dialog({
                        type: 'confirm',
                        color: '#ed8c72',
                        title: 'Anular',
                        acceptText: 'Aceptar',
                        text: text,
                        buttonCancel: 'border',
                        accept: () => {
                            this.ValidarConciliacion()
                        },
                        cancelText: 'Cancelar',
                        cancel: () => {}
                    })
                    return
                } else {
                    let text = 'Este período ya fue cerrado, no puede anular'

                    if (this.ModalidadPago == 'D') {
                        text = text + ' la boleta'
                    } else {
                        text = text + ' el cheque'
                    }

                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'Periodo cerrado',
                        acceptText: 'Aceptar',
                        text: text,
                        buttonCancel: 'border',
                        accept: () => {
                            return true
                        },
                    })
                    return
                }
            }
        },

        ValidarConciliacion() {
            if (this.ModalidadPago !== 'D' && (this.infoCheque.Conciliado == 'S' || this.infoCheque.Conciliado == null)) {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'Conciliado',
                    acceptText: 'Aceptar',
                    text: 'Este cheque/acreditamiento ya fue conciliado. No es posible anularlo.',
                    buttonCancel: 'border',
                    accept: () => {
                        return true
                    },
                })
                return
            } else {
                if (this.infoCheque.Periodo != this.periodo) {
                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'Periodo',
                        acceptText: 'Aceptar',
                        text: 'Este cheque pertenece a un período anterior. Debe anularlo en Anulación Extemporánea.',
                        buttonCancel: 'border',
                        accept: () => {
                            return true
                        },
                    })
                    return
                } else {
                    this.Anular()
                }
            }
        },

        async Anular() {

            let consulta = {
                Anular: {},
                ProntoPago: {},
                LotesHonorarios: {},
                Retenciones: {},
                Devoluciones: {},
                Referencias: [],
                Cupones: [],
                Multiniveles: [],
                EmpAnticipo: {},
                SaldoFactura: {},
                Pago: {},
                PagoDeposito: {},
                Saldos: {},
                Pagos: {},
                Radiologos: {},
                EliminarPagos: {},
                Bonificaciones: {},
                EliminarBonificaciones: {},
                Ajenos: {},
            }

            consulta.Anular = {
                Opcion: 6,
                EmpresaCheque: this.infoCheque.Empresa,
                TipoCheque: this.TipoCheque,
                Cuenta: this.infoCheque.Cuenta,
                ChequeInicial: this.infoCheque.Numero,
                Usuario: this.Corporativo,
            }

            consulta.ProntoPago = {
                Opcion: 7,
                TipoCheque: this.TipoCheque,
                Cuenta: this.infoCheque.Cuenta,
                ChequeInicial: this.infoCheque.Numero,
            }

            consulta.LotesHonorarios = {
                Opcion: 8,
                TipoCheque: this.TipoCheque,
                Cuenta: this.infoCheque.Cuenta,
                ChequeInicial: this.infoCheque.Numero,
            }

            consulta.Retenciones = {
                Opcion: 9,
                TipoCheque: this.TipoCheque,
                Cuenta: this.infoCheque.Cuenta,
                ChequeInicial: this.infoCheque.Numero,
            }

            consulta.Devoluciones = {
                Opcion: 10,
                TipoCheque: this.TipoCheque,
                Cuenta: this.infoCheque.Cuenta,
                ChequeInicial: this.infoCheque.Numero,
            }

            let comprasPagos = await this.BuscarComprasPagos(this.infoCheque.Numero)

            if (comprasPagos !== null) {
                for (let j = 0; j < comprasPagos.length; j++) {
                    let nuevo = {
                        Documento: comprasPagos[j].Documento,
                        Cheque: comprasPagos[j].Documento,
                        ProveedorInicio: this.infoCheque.Proveedor,
                    }
                    switch (comprasPagos[j].Tipo) {
                        case 'C':
                            nuevo.Opcion = 9
                            consulta.Referencias.push(nuevo)
                            break;

                        case 'U':
                            nuevo.Opcion = 10
                            consulta.Cupones.push(nuevo)
                            break;

                        case 'G':
                            nuevo.Opcion = 11
                            consulta.Multiniveles.push(nuevo)
                            break;
                    }
                }
            }

            // Servicios profesionales y cheques de planilla
            if (this.infoCheque.TipoCheque == 'E') {
                consulta.EmpAnticipo = {
                    Opcion: 11,
                    EmpresaCheque: this.infoCheque.EmpesaEspecial == null ? this.infoCheque.Empresa : this.infoCheque.EmpresaEspecial,
                    Cuenta: this.infoCheque.Cuenta,
                    ChequeInicial: this.infoCheque.Numero
                }

                consulta.SaldoFactura = {
                    Opcion: 12,
                    Cuenta: this.infoCheque.Cuenta,
                    ChequeInicial: this.infoCheque.Numero
                }

                if (this.ModalidadPago == 'C') {
                    consulta.Pago = {
                        Opcion: 13,
                        EmpresaCheque: this.infoCheque.EmpesaEspecial == null ? this.infoCheque.Empresa : this.infoCheque.EmpresaEspecial,
                        Cuenta: this.infoCheque.Cuenta,
                        ChequeInicial: this.infoCheque.Numero
                    }
                } else {
                    consulta.PagoDeposito = {
                        Opcion: 14,
                        EmpresaCheque: this.infoCheque.EmpesaEspecial == null ? this.infoCheque.Empresa : this.infoCheque.EmpresaEspecial,
                        Cuenta: this.infoCheque.Cuenta,
                        ChequeInicial: this.infoCheque.Numero,
                        TipoCheque: this.TipoCheque
                    }
                }

                consulta.Saldos = {
                    Opcion: 15,
                    Cuenta: this.infoCheque.Cuenta,
                    ChequeInicial: this.infoCheque.Numero,
                    TipoCheque: this.TipoCheque
                }

                consulta.Pagos = {
                    Opcion: 16,
                    Cuenta: this.infoCheque.Cuenta,
                    ChequeInicial: this.infoCheque.Numero,
                    TipoCheque: this.TipoCheque
                }
            }

            if (this.infoCheque.TipoCheque == 'R' || this.chequeInterpretacion) {
                consulta.Radiologos = {
                    Opcion: 17,
                    Cuenta: this.infoCheque.Cuenta,
                    ChequeInicial: this.infoCheque.Numero,
                }

                consulta.EliminarPagos = {
                    Opcion: 18,
                    Cuenta: this.infoCheque.Cuenta,
                    ChequeInicial: this.infoCheque.Numero,
                    TipoCheque: this.TipoCheque
                }
            }

            if (this.infoCheque.TipoCheque == 'B') {

                // Deja las bonificaciones sin cheque
                consulta.Bonificaciones = {
                    Opcion: 19,
                    TipoCheque: this.TipoCheque,
                    Cuenta: this.infoCheque.Cuenta,
                    ChequeInicial: this.infoCheque.Numero,
                }

                consulta.EliminarBonificaciones = {
                    Opcion: 20,
                    TipoCheque: this.TipoCheque,
                    Cuenta: this.infoCheque.Cuenta,
                    ChequeInicial: this.infoCheque.Numero,
                }

                // Si el cheque es un anticipo o un reembolso, elimina los pagos
                consulta.EliminarPagos = {
                    Opcion: 18,
                    Cuenta: this.infoCheque.Cuenta,
                    ChequeInicial: this.infoCheque.Numero,
                    TipoCheque: this.TipoCheque
                }
            }

            if (this.infoCheque.TipoCheque == 'A' || this.infoCheque.TipoCheque == 'M') {
                consulta.Ajenos = {
                    Opcion: 21,
                    Cuenta: this.infoCheque.Cuenta,
                    ChequeInicial: this.infoCheque.Numero,
                }
            }

            if (this.infoCheque.TipoCheque == 'P' || this.infoCheque.TipoCheque == 'A' || this.infoCheque.TipoCheque == 'M') {
                consulta.Saldos = {
                    Opcion: 15,
                    Cuenta: this.infoCheque.Cuenta,
                    ChequeInicial: this.infoCheque.Numero,
                    TipoCheque: this.TipoCheque
                }

                consulta.EliminarPagos = {
                    Opcion: 18,
                    Cuenta: this.infoCheque.Cuenta,
                    ChequeInicial: this.infoCheque.Numero,
                    TipoCheque: this.TipoCheque
                }

                consulta.Pagos = {
                    Opcion: 16,
                    Cuenta: this.infoCheque.Cuenta,
                    ChequeInicial: this.infoCheque.Numero,
                    TipoCheque: this.TipoCheque
                }
            }

            await this.axios.post('/app/v1_bancos/ConsultaAnulacionAgrupados', {
                    ...consulta
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.BuscarCheques(this.chequeActual - 1, 1)
                    }
                })
        },

        async BuscarComprasPagos(cheque) {
            return await this.axios.post('/app/v1_bancos/PagosGenerados', {
                    Opcion: 6,
                    Cuenta: this.formulario.Cuenta,
                    Cheque: cheque
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        return resp.data.json
                    }
                    return null
                })
        },

        async ChequeInterpretacion(cheque) {
            await this.axios.post('/app/v1_bancos/ConsultaAnulacion', {
                    Opcion: 5,
                    Cuenta: this.formulario.Cuenta,
                    ChequeInicial: cheque
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        if (resp.data.json.Cuantos > 0) {
                            this.chequeInterpretacion = true
                        } else {
                            this.chequeInterpretacion = false
                        }
                    }
                })
        },

        async ConsultarCompras() {
            await this.axios.post('/app/v1_bancos/Compras', {
                    Opcion: 1,
                    Cuenta: this.formulario.Cuenta,
                    TipoCheque: this.TipoCheque,
                    Cheque: this.infoCheque.Numero
                })
                .then(resp => {
                    this.compras = []
                    if (resp.data.json.length > 0) {
                        this.compras = resp.data.json
                    }
                })
        },

        editorDatagridCompras(e) {
            if (e.parentType === 'dataRow') {
                if (e.dataField === "Tipo") {
                    e.editorOptions.maxLength = 1;
                    const defaultValueChangeHandler = e.editorOptions.onValueChanged;

                    e.editorOptions.onValueChanged = (args) => {
                        args.value = args.value.toUpperCase(); // Vuelve mayuscula el dato ingresado
                        // Actualizamos la fila para reflejar los cambios
                        this.$refs.gridCompras.instance.refresh();
                        defaultValueChangeHandler(args);
                    }
                }

                if (e.dataField === 'NombreProveedor' || e.dataField === 'Fecha' || e.dataField === 'FechaRegistro') {
                    e.editorOptions.tabIndex = -1;
                }

                if (e.dataField === 'Proveedor') {
                    e.editorOptions.maxLength = 6;
                    const defaultValueChangeHandler = e.editorOptions.onValueChanged;

                    // Limpia el nombre del proveedor por si ya exite   
                    e.editorOptions.onValueChanged = (args) => {
                        this.$refs.gridCompras.instance.cellValue(
                            rowIndex,
                            "NombreProveedor",
                            ''
                        );
                        args.value = args.value.toUpperCase();
                        const proveedor = args.value; // Captura el valor ingresado del proveedor
                        const updatedNombre = this.proveedores.find((x) => (x.Codigo).toUpperCase() == proveedor)
                        const rowIndex = e.row.rowIndex;
                        // Actualizamos la celda de "Nombre"
                        this.$refs.gridCompras.instance.cellValue(
                            rowIndex,
                            "NombreProveedor",
                            updatedNombre.Nombre ? updatedNombre.Nombre : ''
                        );
                        // Actualizamos la fila para reflejar los cambios
                        this.$refs.gridCompras.instance.refresh();
                        defaultValueChangeHandler(args);
                    }
                }

                if (e.dataField === 'Documento') {
                    const defaultValueChangeHandler = e.editorOptions.onValueChanged;

                    e.editorOptions.onValueChanged = async (args) => {
                        const documento = args.value; // Captura el valor ingresado del proveedor
                        const monto = await this.BuscarDocumento(e.row.data.Proveedor, documento)
                        const rowIndex = e.row.rowIndex;
                        // Actualizamos la celda de "Nombre"
                        this.$refs.gridCompras.instance.cellValue(
                            rowIndex,
                            "Monto",
                            parseFloat(monto).toFixed(2)
                        );
                        this.$refs.gridCompras.instance.cellValue(
                            rowIndex,
                            "Saldo",
                            0
                        );
                        // Actualizamos la fila para reflejar los cambios
                        this.$refs.gridCompras.instance.refresh();
                        defaultValueChangeHandler(args);
                    }
                }

                // if (e.dataField === 'Monto') {
                //     const defaultValueChangeHandler = e.editorOptions.onValueChanged;

                //     e.editorOptions.onValueChanged = (args) => {
                //         const monto = args.value;

                //         if (parseFloat(monto) > parseFloat(this.infoCheque.Monto)) {
                //             this.$vs.dialog({
                //                 type: 'alert',
                //                 color: '#ed8c72',
                //                 title: 'Monto mayor',
                //                 acceptText: 'Aceptar',
                //                 text: 'El monto ingresado es mayor al valor del cheque.',
                //                 buttonCancel: 'border',
                //                 accept: () => {
                //                     return
                //                 },
                //             })
                //         }
                //         // Actualizamos la fila para reflejar los cambios
                //         this.$refs.gridCompras.instance.refresh();
                //         defaultValueChangeHandler(args);
                //     }
                // }
            }
        },

        async BuscarProveedores() {
            await this.axios.post('/app/v1_bancos/Proveedores', {
                    Opcion: 4
                })
                .then(resp => {
                    this.proveedores = resp.data.json.map((x) => {
                        return {
                            Codigo: x.Codigo,
                            Nombre: x.Nombre,
                            Nit: x.Nit,
                            Retencion: parseFloat(x.Retencion).toFixed(2),
                            TipoRetencion: x.TipoRetencion,
                            CuentaRetencion: x.CuentaRetencion,
                            ContaAnticipo: x.ContaAnticipo,
                            NombreCheque: x.NombreCheque,
                            CuentaBanco: x.CuentaBanco,
                            Display: x.Codigo + ' - ' + x.Nit + ' - ' + x.Nombre
                        }
                    })
                })
        },

        async BuscarDocumento(proveedor, documento) {
            return await this.axios.post('/app/v1_bancos/Compras', {
                    Opcion: 2,
                    Proveedor: proveedor,
                    Documento: documento
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        return resp.data.json[0].Saldo
                    } else {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Compra no encontrada',
                            acceptText: 'Aceptar',
                            text: 'Esta compra no está registrada en el sistema.',
                            buttonCancel: 'border',
                            accept: () => {
                                return
                            },
                        })
                        return 0
                    }
                })
        },

        GuardarCambiosCompras(e) {
            let cambio = e.changes[0]

            if (cambio.type == 'insert') {
                if (cambio.data.Monto > this.infoCheque.Monto) {
                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'Monto mayor',
                        acceptText: 'Aceptar',
                        text: 'El monto es mayor al valor del cheque.',
                        buttonCancel: 'border',
                        accept: () => {
                            this.IngresarCompra(cambio.data)
                        },
                    })
                    return
                } else {
                    this.IngresarCompra(cambio.data)
                }
            } else if (cambio.type == 'remove') {
                this.EliminarCompra(cambio.key)
            }
        },

        AgregarCompra(e) {
            e.data.Fecha = this.infoCheque.Fecha
            e.data.FechaRegistro = new Date()
        },

        async IngresarCompra(e) {
            await this.axios.post('/app/v1_bancos/Compras', {
                    Opcion: 3,
                    Cuenta: this.formulario.Cuenta,
                    TipoCheque: this.TipoCheque,
                    Cheque: this.infoCheque.Numero,
                    Tipo: e.Tipo,
                    Periodo: this.infoCheque.Periodo,
                    Proveedor: e.Proveedor,
                    Documento: e.Documento,
                    Fecha: this.infoCheque.Fecha,
                    Monto: e.Monto,
                    Usuario: this.Corporativo,
                    Saldo: e.Saldo ? e.Saldo : 0
                })
                .then(async resp => {
                    if (resp.data.codigo == 0) {
                        await this.ConsultarCompras()
                        this.gridComprasInstance.cancelEditData()
                        this.gridComprasInstance.refresh()
                        this.gridComprasInstance.repaint()
                        this.gridComprasInstance.addRow()
                    }
                })
        },

        async EliminarCompra(e) {
            await this.axios.post('/app/v1_bancos/Compras', {
                    Opcion: 4,
                    Cuenta: e.CuentaBanco,
                    TipoCheque: e.TipoMovto,
                    Cheque: e.NumeroMovto,
                    Tipo: e.Tipo,
                    Periodo: e.Periodo,
                    Proveedor: e.Proveedor,
                    Documento: e.Documento,
                    Fecha: e.Fecha,
                    Monto: e.Monto,
                    Usuario: e.Usuario,
                    Status: e.Status,
                    Saldo: e.Saldo,
                    UsuarioModifica: this.Corporativo,
                })
                .then(async resp => {
                    if (resp.data.codigo == 0) {
                        await this.ConsultarCompras()
                        this.gridComprasInstance.cancelEditData()
                        this.gridComprasInstance.refresh()
                        this.gridComprasInstance.repaint()
                    }
                })
        }
    },
    created() {},
    mounted() {
        this.formulario.Fecha = new Date()
    },
    beforeMount() {
        this.CargarCuentas()
        this.ConsultarPeriodo()
        this.CargarOpciones()
        this.CargarCuentasDefault()
        this.BuscarProveedores()
    },
    watch: {
        'formulario.ChequeInicial'(newval) {
            if (this.formulario.ChequeFinal == 0 || this.formulario.ChequeFinal == null || parseInt(this.formulario.ChequeFinal) < parseInt(this.formulario.ChequeInicial)) {
                this.formulario.ChequeFinal = newval
            }
        }
    },
    computed: {
        formConsultaInstance: function () {
            return this.$refs[formConsulta].instance;
        },

        gridComprasInstance: function () {
            return this.$refs[gridCompras].instance;
        },
    }
}
</script>

<style>
.navigation-buttons button:focus {
    background-color: blue !important;
}
</style>
