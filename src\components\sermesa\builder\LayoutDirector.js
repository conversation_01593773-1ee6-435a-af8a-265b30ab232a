class LayoutDirector {
    constructor(builder) {
      this.builder = builder;
    }
  
    createLayout(layoutConfig) {
      layoutConfig.forEach((containerConfig) => this.createContainer(containerConfig));
      return this.builder.build();
    }
  
    createContainer(containerConfig) {
      this.builder.addContainer(containerConfig.containerClass);
      containerConfig.rows.forEach((rowConfig) => this.createRow(rowConfig));
    }
  
    createRow(rowConfig) {
      this.builder.addRowToLastContainer();
      rowConfig.columns.forEach((columnConfig) => this.createColumn(columnConfig));
    }
  
    createColumn(columnConfig) {
      this.builder.addComponentToLastRow(
        columnConfig.component,
        columnConfig.size,
        columnConfig.validations
      );
    }
  }
  
  export default LayoutDirector;