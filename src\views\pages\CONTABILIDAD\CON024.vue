<template>
<vx-card title="Contabilidad General">
    <div class="content content-pagex">
        <form>

            <vs-divider class="label-size">GENERAR AUXILIARES</vs-divider>
            <div class="btn-group">
                <vs-button class="label-size" :type="(Opcion==1)?'filled':'line'" style="display:inline;height:50px" color="primary" icon-pack="feather" @click="Opcion=1" icon="icon-layers">Generación de Auxiliar Clientes</vs-button>
                <vs-button class="label-size" :type="(Opcion==2)?'filled':'line'" style="display:inline;height:50px" color="primary" icon-pack="feather" @click="Opcion=2" icon="icon-layers">Generación de Auxiliar Costos</vs-button>
            </div>
            <!-- -----------------------GENERACIÓN AXULIAR CLIENTES-------------------->
            <div v-show="Opcion==1">
                <div style="box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.2);padding:10px;border-radius:5px; border:1px solid #ccc;">

                    <div class="flex flex-wrap">
                        <vs-col class="md:w-full lg:w-6/12 xl:w-6/12 p-2">

                            <label class="label-size" style="padding:10px 20px"> Facturas del: </label>
                            <div class="w-full" style="padding:10px 20px">
                                <flat-pickr v-model="FechaClientes" icon-pack="fas" icon="fa-calendar" :config="configFromdate"></flat-pickr>
                            </div>

                            <div class="w-full" style="padding:5px 28px">
                                <div align="left">
                                    <vs-button color="success" class="label-size" type="filled" @click="AuxiliarClientes()"> OK </vs-button>
                                </div>
                            </div>

                        </vs-col>

                    </div>
                </div>
            </div>
            <!-- -----------------------GENERACIÓN AXULIAR COSTOS-------------------->
            <div v-show="Opcion==2">
                <div style="box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.2);padding:10px;border-radius:5px; border:1px solid #ccc;">
                    <div class="flex flex-wrap">
                        <vs-col class="md:w-full lg:w-4/12 xl:w-3/12 p-2">
                            <label class="label-size" style="padding:10px 20px"> Fecha: </label>
                            <div class="w-full" style="padding:10px 20px">
                                <flat-pickr v-model="FechaCostos" icon-pack="fas" ref="flatpickr"  icon="fa-calendar" :config="configFromdateTimePicker"   @input="OcultarAvance()" />
                            </div>
                            <div class="w-full" style="padding:10px 20px">
                                <div align="left">
                                    <vs-button color="success" class="label-size" type="filled" @click="GenerarAuxiliarCostos()"> OK </vs-button>
                                </div>
                            </div>
                            <div class="w-full" style="padding:10px 20px" v-show="FlagAvanceUnDia">
                                <div align="left">
                                    <vs-button color="warning" class="label-size" type="filled" @click="AvanceUnDia()"> Avance un Dia </vs-button>
                                </div>
                            </div>

                        </vs-col>

                        <vs-col class="md:w-full lg:w-full xl:w-9/12 p-2">
                            <vs-row style="padding:10px 10px">
                                <div class="w-full">
                                    <vs-table2 tooltip max-items="10" pagination :data="ListaDiasGenerados" noDataText="Sin datos disponibles" search id="DiasG">
                                        <template slot="thead">
                                            <th width="3px">Inactivar</th>
                                            <th width="50px">Corte</th>
                                            <th width="50px">Fecha</th>
                                            <th width="50px">Status</th>
                                            <th width="100PX">Registro</th>
                                            <th width="50px">Usuario</th>
                                            <th width="50px">Corporativo</th>
                                        </template>
                                        <template slot-scope="{data}">
                                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                                <vs-td2 noTooltip align="center">
                                                    <vs-button v-if="data[indextr].Status == 'I'" disabled="false" color="success" style="margin-left:1px;display:inline-block;" icon-pack="feather" icon="icon-edit-1" @click="InactivarCorte(data[indextr].Correlativo)"></vs-button>
                                                    <vs-button v-else color="success" style="margin-left:1px;display:inline-block;" icon-pack="feather" icon="icon-edit-1" @click="InactivarCorte(data[indextr].Correlativo)"></vs-button>

                                                </vs-td2>
                                                <vs-td2 noTooltip :data="data[indextr].Correlativo" align="center">
                                                    {{data[indextr].Correlativo}}
                                                </vs-td2>
                                                <vs-td2 noTooltip :data="data[indextr].Fecha" align="center">
                                                    {{data[indextr].Fecha}}
                                                </vs-td2>
                                                <vs-td2 noTooltip :data="data[indextr].Status" align="center">
                                                    {{data[indextr].Status}}
                                                </vs-td2>
                                                <vs-td2 :data="data[indextr].Registro" align="center">
                                                    {{data[indextr].Registro}}
                                                </vs-td2>
                                                <vs-td2 :data="data[indextr].Usuario" align="right">
                                                    {{data[indextr].Usuario}}
                                                </vs-td2>
                                                <vs-td2 :data="data[indextr].Corporativo" align="right">
                                                    {{data[indextr].Corporativo}}
                                                </vs-td2>
                                            </tr>
                                        </template>
                                    </vs-table2>
                                </div>
                            </vs-row>
                        </vs-col>

                    </div>
                </div>
            </div>

        </form>
    </div>
</vx-card>
</template>

<script>
import "vue-multiselect/dist/vue-multiselect.min.css"
import moment from "moment";
import 'flatpickr/dist/flatpickr.css';
import flatPickr from 'vue-flatpickr-component';
import {
    Spanish as SpanishLocale
} from 'flatpickr/dist/l10n/es.js';

export default {

    components: {
        flatPickr
    },
    data() {
        return {
            Opcion: 1,
            FechaClientes: '',
            FechaCostos: null,
            configFromdate: {
                locale: SpanishLocale,
                dateFormat: "d/m/Y",
            },
            configFromdateTimePicker: {
                minDate: '',
                maxDate: '',
                locale: SpanishLocale,
                dateFormat: "d/m/Y",
            },
            ListaDiasGenerados: [],
            NoCorrida: 0,
            FlagAvanceUnDia: false,
            CorteSel: '',
            FechaMin: null,
            FechaMax: null,

        }
    },
    mounted() {
        this.FechaClientes = new Date().fp_incr(-1)
    },
    watch: {

        Opcion(value) {
            //  
            if (value == 2) {
                this.ConsultarUltimosDias()
            }

        },
    },
    methods: {
        getDateValue(value) {
            if (value === undefined) {
                return '';
            } else if (value === null) {
                return '';
            } else if (value == '') {
                return '';
            }
            return moment(value).format('YYYY-MM-DD');
        },
        // Clientes
        AuxiliarClientes() {
            this.NoCorrida = 0
            this.axios.post('/app/v1_contabilidad_general/TranAuxiliarClientes', {
                    Opcion: 1,
                    Fecha: this.FechaClientes && this.FechaClientes != '' ? moment(this.FechaClientes, 'DD/MM/YYYY').format('DD/MM/YYYY') : null,
                    Corrida: this.NoCorrida
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.GenerarAuxiliarClientes()
                    }
                })
        },
        GenerarAuxiliarClientes() {
            this.NoCorrida = 1
            this.axios.post('/app/v1_contabilidad_general/TranAuxiliarClientes', {
                Opcion: 1,
                Fecha: this.FechaClientes && this.FechaClientes != '' ? moment(this.FechaClientes, 'DD/MM/YYYY').format('DD/MM/YYYY') : null,
                Corrida: this.NoCorrida
            })
        },

        // Costos
        ConsultarUltimosDias() {
            this.axios.post('/app/v1_contabilidad_general/ConsultaAuxiliarClientes', {
                    Opcion: 0,
                    SubOpcion: 0
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.ListaDiasGenerados = "";
                    } else {
                        //this.ListaDiasGenerados = resp.data.json;
                        this.ListaDiasGenerados = resp.data.json.map(data => {
                                return {
                                    ...data,
                                    Fecha: moment(data.Fecha).format('DD/MM/YYYY'),
                                   Registro: moment(data.Registro).format('DD/MM/YYYY HH:mm')
                                }

                            })

                    }
                })
            this.FechasValidas()
        },
       FechasValidas() {
            this.FlagAvanceUnDia = false
            this.axios.post('/app/v1_contabilidad_general/ConsultaAuxiliarClientes', {
                Opcion: 0,
                SubOpcion: 1
            })
            .then(resp => {
                if (resp.data.codigo === 0) {
                    const parseDateDMY = (dateStr) => {
                        const [day, month, year] = dateStr.split('/');
                        return new Date(year, month - 1, day);
                    };
                    this.FechaCostos = parseDateDMY(resp.data.json[0].FechaPickerFinal);
                    // Configuración básica (sin forzar setDate inicialmente)
                    this.configFromdateTimePicker = {
                        ...this.configFromdateTimePicker,
                        minDate: parseDateDMY(resp.data.json[0].FechaPickerMIN),
                        maxDate: parseDateDMY(resp.data.json[0].FechaPickerMax),
                        defaultDate: this.FechaCostos // ¡Asigna la fecha aquí!
                    };
                    // Opcional: Actualización diferida si es necesario
                    this.$nextTick(() => {
                        if (this.$refs.flatpickr && this.$refs.flatpickr.flatpickr) {
                            this.$refs.flatpickr.flatpickr.setDate(this.FechaCostos);
                        }
                    });
                }
            })
            .catch(error => {
                console.error("Error al obtener fechas:", error);
            });
        },
        OcultarAvance(){
            this.FlagAvanceUnDia = false
        },
        InactivarCorte(datos) {
            this.CorteSel = datos;
            this.axios.post('/app/v1_contabilidad_general/TranAuxiliarCostos', {
                    Opcion: 3,
                    Corte: this.CorteSel
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ConsultarUltimosDias()
                        this.FechasValidas()
                    }
                })
        },

        GenerarAuxiliarCostos() {
            this.axios.post('/app/v1_contabilidad_general/TranAuxiliarCostos', {
                    Opcion: 2,
                    Fecha: this.FechaCostos && this.FechaCostos != '' ? moment(this.FechaCostos, 'DD/MM/YYYY').format('DD/MM/YYYY') : null,
                    EmpresaSEM: 'SEM'
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.FlagAvanceUnDia = false
                        this.ConsultarUltimosDias()
                    }
                })

                .catch((error) => {
                    if (error.response.data.Message == 'Esta fecha no tiene costos') {
                        this.FlagAvanceUnDia = true
                    }
                })
        },
        AvanceUnDia() {
            this.axios.post('/app/v1_contabilidad_general/TranAuxiliarCostos', {
                    Opcion: 4,
                    Fecha: this.FechaCostos && this.FechaCostos != '' ? moment(this.FechaCostos, 'DD/MM/YYYY').format('DD/MM/YYYY') : null,
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.FlagAvanceUnDia = false
                        this.ConsultarUltimosDias()
                        this.FechasValidas()
                    }
                })
        }
    }

}
</script>   

<style scoped>
.footer {
    height: 50px;
    margin-top: -65px;
    margin-left: 10px;
}

.label-size {
    font-size: 14px;
    font-weight: bold;
}

.padre {
    position: relative;
}

.hijo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
</style>
