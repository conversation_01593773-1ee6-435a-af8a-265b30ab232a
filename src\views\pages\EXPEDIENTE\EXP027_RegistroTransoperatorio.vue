<template>
<div>
    <div v-if="(consulta && hojaAnestesia !== null && hojaAnestesia !== '' && registrosTransoperatorios !== null && registrosTransoperatorios.length !== 0) || (!consulta)">
        <div class="p-2">
            <div id="detail" style="text-align: center" v-if="!consulta">
                <b>*Este formulario se guardará automáticamente cuando se ingrese el registro anestésico</b>
            </div>
            <form @submit="handleSubmit">
                <DxForm :form-data.sync="encabezado" labelMode="floating" :ref="dataEncabezadoRefKey" @field-data-changed="sendRegistro">

                    <DxGroupItem item-type="group" :col-count="3" v-if="consulta">
                        <DxEmptyItem />
                        <DxItem data-field="RegistroTransoperatorio" editor-type="dxSelectBox" :editor-options="{ items: registrosTransoperatorios, onItemClick: this.getDetalleRegistro }">
                            <DxLabel :text="'Registro transoperatorio'" />
                        </DxItem>
                        <DxEmptyItem />
                    </DxGroupItem>

                    <DxGroupItem item-type="group" :col-count="2" caption=" ">
                        <DxGroupItem item-type="group" :col-span="2">
                            <DxItem data-field="Posicion" :disabled="consulta" editor-type="dxTextBox" :editor-options="{ maxLength: 1000 }">
                                <DxLabel :text="'Posición'" />
                            </DxItem>
                        </DxGroupItem>
                        <DxGroupItem item-type="group">
                            <DxItem data-field="VolCirculante" :disabled="consulta" editor-type="dxNumberBox" :editor-options="{ maxLength: 100,min: 0, step: 0 }">
                                <DxLabel :text="'Vol. circulante total CC'" />
                            </DxItem>
                        </DxGroupItem>
                        <DxGroupItem item-type="group">
                            <DxItem data-field="PerdidaMax" :disabled="consulta" editor-type="dxNumberBox" :editor-options="{ maxLength: 100, min: 0, step: 0 }">
                                <DxLabel :text="'Perd. máx. permisible'" />
                            </DxItem>
                        </DxGroupItem>
                    </DxGroupItem>
                    <DxGroupItem item-type="group" caption=" " :col-count-by-screen="colCountByScreen">

                        <DxItem data-field="AnestesiaRegional" :disabled="consulta" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000, }">
                            <DxLabel :text="'Anestesia regional'" />
                        </DxItem>
                        <DxItem data-field="CirugiaPlaneada" :disabled="consulta" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000, }">
                            <DxLabel :text="'Cirugía planeada'" />
                        </DxItem>
                        <DxItem data-field="CirugiaEfectuada" :disabled="consulta" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000, }">
                            <DxLabel :text="'Cirugía efectuada'" />
                        </DxItem>
                        <DxItem data-field="Ayudantes" :disabled="consulta" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000, }" />
                        <DxItem data-field="Anestesia" :disabled="consulta" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000, }" />
                        <DxItem data-field="Medicamentos" :disabled="consulta" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000, }" />
                        <DxItem data-field="Cirujanos" :disabled="consulta" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000, }" />
                        <DxItem data-field="Cirugia" :disabled="consulta" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000, }">
                            <DxLabel :text="'Cirugía'" />
                        </DxItem>
                        <DxItem data-field="Comentarios" :disabled="consulta" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000, }" />

                    </DxGroupItem>

                    <DxGroupItem v-if="!consulta" item-type="group">
                        <DxButtonItem :button-options="clearButtonOptions" horizontal-alignment="center" verical-alignment="center" />
                    </DxGroupItem>

                </DxForm>

                <DxDataGrid :ref="dataRegistroRefKey" v-bind="DefaultDxGridConfiguration" :data-source="detalle" :headerFilter="{visible:false, allowSearch: false }" :searchPanel="{visible:false}" :sorting="{mode: 'none'}" :paging="{ enabled:false, pageSize:10 }" :width="'100%'" :height="'100%'" @editor-preparing="editorDatagrid" @cell-prepared="onCellPrepared">
                    <DxSelection mode="single" />

                    <DxToolbar>
                        <DxItem name="groupPanel" />
                        <DxItem location="after" template="opcionesTemplate" />
                    </DxToolbar>

                    <DxColumn caption="Requerimienos" alignment="center">
                        <DxColumn :width="100" data-field="Hora" alignment="center" :editor-options="{ maxLength: 50 }" :validationRules="[{ type: 'required' }]" />
                        <DxColumn :width="100" data-field="Ayuno" caption="Ayuno" alignment="center" data-type="number" :editor-options="{ min: 0 }" />
                        <DxColumn :width="150" data-field="Mantenimiento" caption="Mantenimiento y herida operatoria" alignment="center" data-type="number" :editor-options="{ min: 0 }" />
                        <DxColumn :width="100" data-field="PerdidaHematica" caption="Pérdida hemática" alignment="center" data-type="number" :editor-options="{ min: 0 }" />
                        <DxColumn :width="100" data-field="OtrosReq" caption="Otros" alignment="center" data-type="number" :editor-options="{ min: 0 }" />
                        <DxColumn :width="100" data-field="TotalReq" caption="Total" alignment="center" data-type="number" :calculate-cell-value="calcularTotalRequerimiento" />
                    </DxColumn>

                    <DxColumn caption="Aporte" alignment="center">
                        <DxColumn :width="100" data-field="Cristaloides" alignment="center" data-type="number" :editor-options="{ min: 0 }" />
                        <DxColumn :width="100" data-field="Coloides" alignment="center" data-type="number" :editor-options="{ min: 0 }" />
                        <DxColumn :width="100" data-field="CelulasEmpacadas" caption="Células empacadas" alignment="center" data-type="number" :editor-options="{ min: 0 }" />
                        <DxColumn :width="100" data-field="OtrosAportes" caption="Otros" alignment="center" data-type="number" :editor-options="{ min: 0 }" />
                        <DxColumn :width="100" data-field="TotalAportes" caption="Total" alignment="center" data-type="number" :calculate-cell-value="calcularTotalAportes" />
                    </DxColumn>

                    <DxColumn :width="100" data-field="Balance" alignment="center" data-type="number" :calculate-cell-value="calcularBalance" />

                    <DxSummary v-if="consulta">
                        <DxTotalItem column="Ayuno" summary-type="sum" display-format="{0}" />
                        <DxTotalItem column="Mantenimiento" summary-type="sum" display-format="{0}" />
                        <DxTotalItem column="PerdidaHematica" summary-type="sum" display-format="{0}" />
                        <DxTotalItem column="OtrosReq" summary-type="sum" display-format="{0}" />
                        <DxTotalItem column="TotalReq" summary-type="sum" display-format="{0}" />
                        <DxTotalItem column="Cristaloides" summary-type="sum" display-format="{0}" />
                        <DxTotalItem column="Coloides" summary-type="sum" display-format="{0}" />
                        <DxTotalItem column="CelulasEmpacadas" summary-type="sum" display-format="{0}" />
                        <DxTotalItem column="OtrosAportes" summary-type="sum" display-format="{0}" />
                        <DxTotalItem column="TotalAportes" summary-type="sum" display-format="{0}" />
                        <DxTotalItem column="Balance" summary-type="sum" display-format="{0}" />
                    </DxSummary>

                    <DxEditing :allow-updating="!consulta" :allow-adding="false" :allow-deleting="false" :confirmDelete="false" mode="cell" newRowPosition="last" />

                    <template #opcionesTemplate>
                        <ExpedienteGridToolBar v-bind="$props" :visible="true" :pdfExportItems="[]" :report-param="$props" :showItems="[]">
                            <DxButton :visible="!consulta" id="botonBarra" type="default" styling-mode="outlined" hint="Agregar tiempo" @click="addRow">
                                <font-awesome-icon :icon="['fas', 'clock']" style="font-size: 18px;" />
                            </DxButton>
                            <!-- <DxButton :visible="!consulta" id="botonBarra" type="danger" styling-mode="outlined" hint="Limpiar registro" @click="limpiarTiempo">
                                <font-awesome-icon :icon="['fas', 'trash']" style="font-size: 18px;" />
                            </DxButton> -->
                        </ExpedienteGridToolBar>
                    </template>
                </DxDataGrid>
            </form>
        </div>
    </div>
    <vs-row id="texto" class="p-2" v-if="(hojaAnestesia === '' || hojaAnestesia === null) && consulta">
        <p>Seleccione una fecha para mostrar información</p>
    </vs-row>
    <vs-row id="texto" class="p-2" v-if="(registrosTransoperatorios.length === 0 || registrosTransoperatorios === null ) && consulta && hojaAnestesia !== '' && hojaAnestesia !== null">
        <p>La hoja seleccionada no cuenta con información para mostrar</p>
    </vs-row>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'
import {
    DxDataGrid,
    DxColumn,
    DxSelection,
    DxEditing,
    DxToolbar,
    DxSummary,
    DxTotalItem,
} from 'devextreme-vue/data-grid'
import {
    DxForm,
    DxItem,
    DxLabel,
    DxGroupItem,
    DxEmptyItem,
    DxButtonItem,
} from 'devextreme-vue/form'
import DxButton from 'devextreme-vue/button'

const dataRegistroRefKey = 'encabezado-form'
const dataEncabezadoRefKey = 'encabezado-form'

export default {
    name: 'RegistroTransoperatorio',
    components: {
        DxDataGrid,
        DxColumn,
        DxSelection,
        DxEditing,
        DxSummary,
        DxForm,
        DxItem,
        DxLabel,
        DxGroupItem,
        DxToolbar,
        DxButton,
        DxEmptyItem,
        DxTotalItem,
        DxButtonItem,
        ExpedienteGridToolBar: () => import('./ExpedienteGridToolBar.vue'),
    },
    data() {
        return {
            DefaultDxGridConfiguration,
            dataRegistroRefKey,
            dataEncabezadoRefKey,
            rgistro: [],
            encabezado: {
                Posicion: '',
                VolCirculante: '',
                PerdidaMax: '',
                AnestesiaRegional: '',
                CirugiaPlaneada: '',
                CirugiaEfectuada: '',
                Ayudantes: '',
                Anestesia: '',
                Medicamentos: '',
                Cirujanos: '',
                Cirugia: '',
                Comentarios: '',
                RegistroTransoperatorio: ''
            },
            detalle: [],
            addTimeButtonOptions: {
                text: 'Agregar tiempo',
                type: 'default',
                icon: 'clock',
                width: 200,
                useSubmitBehavior: false,
                disabled: false,
                onClick: this.agregarTiempo
            },
            clearButtonOptions: {
                text: 'Limpiar registro',
                type: 'danger',
                icon: 'trash',
                width: 200,
                useSubmitBehavior: false,
                disabled: false,
                onClick: this.limpiarTiempo
            },
        }
    },
    props: {
        SerieAdmision: null,
        CodigoAdmision: null,
        NumeroExpediente: null,

        StatusAdmision: null,
        consulta: null, //Variable para indicar si el componente se usará para consulta -> 'true' o para ingreso -> 'false'
        hojaAnestesia: null, //Variable para obtener la hoja cuando se utilice para consulta

        registrosTransoperatorios: null
    },
    methods: {
        handleSubmit(e) {
            e.preventDefault()
            this.mapRegistro()
            if (this.SerieAdmision && this.CodigoAdmision) {
                let postData = {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    RegistroAnestesico: {
                        ...this.encabezado,
                        Anestesias: this.anestesia,
                        Observaciones: this.observaciones
                    }
                }
                this.axios.post("/app/v1_ExpedienteEvolucion/InsertarAnestTransOperatorio", {
                    ...postData
                }).then(() => {

                })
            }
        },
        addRow() {
            this.dataGrid.addRow();
        },
        refresh() {
            this.dataGrid.refresh()
                .then(function () {

                })
                .catch(function (error) {
                    //console.error('error refreshing: ' + error)
                });
            this.dataGrid.clearSelection();
            this.dataGrid.clearSorting();
            this.dataGrid.pageIndex(0);
        },
        limpiarTiempo() {
            this.detalle = []
            this.encabezado = {
                Posicion: '',
                VolCirculante: '',
                PerdidaMax: '',
                AnestesiaRegional: '',
                CirugiaPlaneada: '',
                CirugiaEfectuada: '',
                Ayudantes: '',
                Anestesia: '',
                Medicamentos: '',
                Cirujanos: '',
                Cirugia: '',
                Comentarios: '',
                RegistroTransoperatorio: ''
            }
        },
        onlyNumbers(e) {
            if ((e.event.keyCode < 48 || e.event.keyCode > 57) && (e.event.keyCode < 96 || e.event.keyCode > 105) && e.event.keyCode !== 8 && e.event.keyCode !== 9) {
                e.event.preventDefault()
            }
        },
        getDefaultDetalle() {
            return [{}, {}, {}, {}, {}, {}]
        },
        formatToday(date = new Date()) {
            const year = date.toLocaleString('default', {
                year: 'numeric'
            });
            const month = date.toLocaleString('default', {
                month: '2-digit'
            });
            const day = date.toLocaleString('default', {
                day: '2-digit'
            });

            return [year, month, day].join('/');
        },
        sendRegistro() {
            this.encabezado = this.$reemplazar_tabulares_objeto(this.encabezado, /\t/g, '   ')
            // Object.entries(this.encabezado).forEach((t) => {
            //     if (typeof t[1] === 'string') {
            //         this.encabezado[t[0]] = this.$reemplazar_tabulares(t[1])
            //     }
            // })
            let registro = {
                ...this.encabezado,
                PerdidasAportes: this.detalle
            }
            this.$emit('registroTransoperatorio', registro);
        },
        editorDatagrid() {
            this.sendRegistro()
        },
        onCellPrepared(e) {
            if (e.rowType === 'data' && (e.column.dataField === 'Ayuno' || e.column.dataField === 'Mantenimiento' || e.column.dataField === 'PerdidaHematica' || e.column.dataField === 'OtrosReq')) {
                e.cellElement.style.cssText = "color: red; text-align: center;";
            }
            if (e.rowType === 'data' && e.column.dataField === 'TotalReq') {
                e.cellElement.style.cssText = "color: red; text-align: center; font-weight: bold; font-size: 16px;";
            }
            if (e.rowType === 'data' && e.column.dataField === 'TotalAportes') {
                e.cellElement.style.cssText = "text-align: center; font-weight: bold; font-size: 16px;";
            }
            if (e.rowType === 'data' && e.column.dataField === 'Balance') {
                e.cellElement.style.cssText = " text-align: center; font-weight: bold; font-size: 16px;";
                if (this.calcularBalance(e.data) < 0) {
                    e.cellElement.style.cssText = "color: red; text-align: center; font-weight: bold; font-size: 16px;"
                }
            }
        },
        getDetalleRegistro() {
            this.axios.post('/app/v1_ExpedienteEvolucion/BuscarRegOperatorioBalance', {
                    Id: this.encabezado.RegistroTransoperatorio,
                })
                .then(resp => {
                    this.mapConsulta(resp.data)
                })
        },
        mapConsulta(res) {
            this.encabezado.Posicion = res.Posicion
            this.encabezado.VolCirculante = res.VolCirculante
            this.encabezado.PerdidaMax = res.PerdidaMax
            this.encabezado.AnestesiaRegional = res.AnestesiaRegional
            this.encabezado.CirugiaPlaneada = res.CirugiaPlaneada
            this.encabezado.CirugiaEfectuada = res.CirugiaEfectuada
            this.encabezado.Ayudantes = res.Ayudantes
            this.encabezado.Anestesia = res.Anestesia
            this.encabezado.Medicamentos = res.Medicamentos
            this.encabezado.Cirujanos = res.Cirujanos
            this.encabezado.Cirugia = res.Cirugia
            this.encabezado.Comentarios = res.Comentarios

            this.detalle = res.Balance
        },
        calcularTotalRequerimiento(e) {
            return (e.Ayuno ? e.Ayuno : 0) + (e.Mantenimiento ? e.Mantenimiento : 0) + (e.PerdidaHematica ? e.PerdidaHematica : 0) + (e.OtrosReq ? e.OtrosReq : 0)
        },
        calcularTotalAportes(e) {
            return (e.Cristaloides ? e.Cristaloides : 0) + (e.Coloides ? e.Coloides : 0) + (e.CelulasEmpacadas ? e.CelulasEmpacadas : 0) + (e.OtrosAportes ? e.OtrosAportes : 0)
        },
        calcularBalance(e) {
            return ((e.Cristaloides ? e.Cristaloides : 0) + (e.Coloides ? e.Coloides : 0) + (e.CelulasEmpacadas ? e.CelulasEmpacadas : 0) + (e.OtrosAportes ? e.OtrosAportes : 0)) - ((e.Ayuno ? e.Ayuno : 0) + (e.Mantenimiento ? e.Mantenimiento : 0) + (e.PerdidaHematica ? e.PerdidaHematica : 0) + (e.OtrosReq ? e.OtrosReq : 0))
        },
    },
    watch: {
        'registrosTransoperatorios'(newval) {
            if (newval !== undefined) {
                this.limpiarTiempo()
            }
        },
    },
    computed: {
        dataGrid: function () {
            return this.$refs[dataRegistroRefKey].instance;
        },
        colCountByScreen() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 3,
                    lg: 3,
                };
        },
    },
}
</script>

<style>
#detail {
    padding: 5px;
    border: none;
    color: black;
}

#texto {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    height: 100px;
}

.dx-button.dx-button-warning {
    background-color: #FF6347;
}

.dx-button-mode-contained.dx-button-warning .dx-icon {
    color: #fff;
}

.dx-button-mode-contained.dx-button-warning {
    color: #fff;
}
</style>
