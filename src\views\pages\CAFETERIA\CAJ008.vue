<template>
    <div>
        <vs-popup :title="tituloTipoDescuento" :active.sync="isVisible" style="z-index:99998;">
            <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                <div class="flex flex-wrap-nowrap">
                    <div class="default-div">
                        <ValidationProvider name="descripcion" rules="required|max:50" class="required" v-slot="{ errors }">
                            <label class="typo_label">Descripcion</label>
                            <vs-input class="descripcion d-inline-block" v-model="descripcion" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                    <div class="default-div">
                        <ValidationProvider name="descuento" rules="required|numero_entero" class="required" v-slot="{ errors }">
                            <label class="typo_label">Monto Descue<PERSON></label>
                            <vs-input class="descuento d-inline-block" v-model="descuento" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                        </ValidationProvider>
                    </div>
                    <div class="default-div">
                        <vs-checkbox class="activo" v-model="activo">Activo</vs-checkbox>
                    </div>
                </div>
                <div class="flex flex-wrap-nowrap">
                    <div class="multi-span">
                        <ValidationProvider name="listTipoDescuento" rules="required" class="required" v-slot="{ errors }">                        
                            <label class="typo_label" >Tipo Descuento</label>
                            <multiselect class="tipoDescuento" v-model="selectedTipoDescuento" :options="listTipoDescuento" placeholder="Seleccione Tipo Descuento" track-by="Codigo" :show-labels="false" :custom-label="DatosMostrarTipoDesc"></multiselect>
                            <span style="position:relative;top:-2px" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                        </ValidationProvider>
                    </div>
                    <div class="default-div">
                        <label class="typo_label" :style="styleHidden">Monto Desc. Internas</label>
                        <vs-input :type="isActivo" class="descuentoInternas d-inline-block" v-model="descuentoInternas" />
                    </div>
                    <div class="default-div">
                        <label class="typo_label" :style="styleHidden">Monto Desc. Emergencias</label>
                        <vs-input :type="isActivo" class="descuentoEmergencias d-inline-block" v-model="descuentoEmergencias" />
                    </div>
                </div>
                <div class="flex flex-wrap-nowrap">
                    <vs-textarea class="observacion" label="Observación" counter="450" v-model="observacion"></vs-textarea>
                </div>
                <div class="flex flex-wrap div-container">
                    <div>
                        <vs-button @click="handleSubmit(GuardarTipoDescuento)" :disabled="invalid" color="success" class="btn btn-primary mx-3 my-3" icon-pack="fas" icon="fa-save">
                            Guardar
                        </vs-button>
                    </div>
                </div>
            </ValidationObserver>
        </vs-popup>
        <vx-card title="Tipo Descuento">
            <div>
                <vs-button @click="NuevoTipoDescuento" color="success" icon-pack="fas" icon="fa-plus-circle" >Nuevo Tipo Descuento</vs-button>
            </div>
            <vs-divider border-style="solid" color="dark"></vs-divider>
            <div class="con-tab-ejemplo">
                <vs-table2 max-items="10" tooltip search filter pagination :data="tablaTipoDescuento" noSelectText>
                    <template slot="thead">
                        <th>Descripcion</th>
                        <th>Descuento</th>
                        <th>Tipo Descuento</th>
                        <th>Observacion</th>
                        <th>Activo</th>
                        <th>Monto Descuento Internas</th>
                        <th>Monto Descuento Emergencias</th>
                        <th>Editar|Eliminar</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data" :data="tr">
                            <vs-td2>{{ tr.Descripcion }}</vs-td2>
                            <vs-td2>{{ tr.Descuento }}</vs-td2>
                            <vs-td2>{{ tr.TipoDescuento }}</vs-td2>
                            <vs-td2>{{ tr.Observacion }}</vs-td2>
                            <vs-td2>{{ tr.Activo }}</vs-td2>
                            <vs-td2>{{ tr.DescuentoIN }}</vs-td2>
                            <vs-td2>{{ tr.DescuentoEM }}</vs-td2>
                            <vs-td2 class="th_editar" noTooltip>
                                <vs-button @click="EditarTipoDescuento(tr)" color="primary" size="small" icon-pack="fas" icon="fa-edit" class="btn_editar mr-1"></vs-button>
                                <vs-button @click="EliminarTipoDescuento(tr)" color="danger" size="small" icon-pack="fas" icon="fa-trash-alt" class="btn_editar mr-1"></vs-button>
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
        </vx-card>
    </div>
</template>
<script>

    import Multiselect from "vue-multiselect"
    import "vue-multiselect/dist/vue-multiselect.min.css"

    export default {
        data() {
            return{
                tituloTipoDescuento: '',
                isVisible: false,
                tablaTipoDescuento: [],
                descripcion: '',
                descuento: '',
                listTipoDescuento: [
                    {
                        Codigo: 'Porcentaje',
                        Nombre: 'Porcentaje'
                    },
                    {
                        Codigo: 'Efectivo',
                        Nombre: 'Efectivo'
                    }
                ],
                selectedTipoDescuento: '',
                observacion: '',
                activo: true,
                descuentoInternas: 0,
                descuentoEmergencias: 0,
                isActivo: 'hidden',
                labelDescuentoInternas: '',
                labelDescuentoEmergencias: '',
                cantidadTipoDescuento: [],
                isModificar: false,
                idDescuento: 0,
                styleHidden: 'display: none'
            }
        },
        components: {
            Multiselect
        },
        methods: {
            ObtenerTiposDescuento(){
                this.axios.post('/app/v1_JefeCaja/ObtenerTiposDescuentos', {
                    "Opcion": "C",
                    "SubOpcion": "1",
                    "Hospital": "0"
                })
                .then(resp => {
                    this.tablaTipoDescuento = resp.data.json.map(m => {
                        this.cantidadTipoDescuento.push(m.IdDescuento)
                        return{
                            ...m
                        }
                    })
                })
            },
            getStyle( value ){
                
                return value == 'Porcentaje' ? '' : 'display: none'
            },
            DatosMostrarTipoDesc({
                Nombre
            }){
                return `${Nombre}`
            },
            NuevoTipoDescuento(){
                this.tituloTipoDescuento = 'Nuevo Tipo de Descuento'
                this.isVisible = true
                this.isModificar = false
                this.idDescuento = (Math.max(...this.cantidadTipoDescuento) + 1)
                // this.descripcion = (Math.max(...this.cantidadTipoDescuento) + 1)
            },
            EditarTipoDescuento(item){
                this.idDescuento = item.IdDescuento
                this.descripcion = item.Descripcion
                this.descuento = item.Descuento
                this.observacion = item.Observacion
                this.activo = item.Activo == 'S' ? true : false
                this.selectedTipoDescuento = {
                    Codigo: item.TipoDescuento,
                    Nombre: item.TipoDescuento
                },
                this.descuentoInternas = item.DescuentoIN
                this.descuentoEmergencias = item.DescuentoEM
                this.isVisible = true
                this.isModificar = true
            },
            GuardarTipoDescuento(){
                if(this.isModificar){
                    this.axios.post('/app/v1_JefeCaja/ActualizarTiposDescuentos', {
                        "Opcion": "A",
                        "SubOpcion": "1",
                        "Hospital": "0",
                        "Descripcion": this.descripcion,
                        "Descuento": this.descuento,
                        "Observacion": this.observacion,
                        "Activo": this.activo ? 'S' : 'N',
                        "TipoDescuento": this.selectedTipoDescuento.Codigo,
                        "DescuentoIn": this.descuentoInternas,
                        "DescuentoEm": this.descuentoEmergencias,
                        "IdDescuento": this.idDescuento
                    })
                    .then(resp => {
                        this.descripcion = null
                        this.descuento = 0
                        this.observacion = null
                        this.activo = true
                        this.selectedTipoDescuento = null
                        this.descuentoInternas = 0
                        this.descuentoEmergencias = 0
                        this.cantidadTipoDescuento = []
                        this.ObtenerTiposDescuento()
                        this.isVisible = false
                        this.$vs.notify({
                            color: "success",
                            title: "Tipo de Descuento",
                            text: resp.data.json[0].descripcion
                        })
                    })
                }else{
                    this.axios.post('/app/v1_JefeCaja/AlmacenarTiposDescuentos', {
                        "Opcion": "I",
                        "SubOpcion": "1",
                        "Hospital": "0",
                        "Descripcion": this.descripcion,
                        "Descuento": this.descuento,
                        "Observacion": this.observacion,
                        "Activo": this.activo ? 'S' : 'N',
                        "TipoDescuento": this.selectedTipoDescuento.Codigo,
                        "DescuentoIn": this.descuentoInternas,
                        "DescuentoEm": this.descuentoEmergencias,
                        "IdDescuento": this.idDescuento
                    })
                    .then(resp => {
                        this.descripcion = null
                        this.descuento = 0
                        this.observacion = null
                        this.activo = true
                        this.selectedTipoDescuento = null
                        this.descuentoInternas = 0
                        this.descuentoEmergencias = 0
                        this.cantidadTipoDescuento = []
                        this.ObtenerTiposDescuento()
                        this.isVisible = false
                        this.$vs.notify({
                            color: 'success',
                            title: 'Tipo de Descuento',
                            text: resp.data.json[0].descripcion
                        })
                    })
                }
            },
            EliminarTipoDescuento(item){
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmacion',
                    text: `¿Desea eliminar el tipo de descuento ${item.Descripcion}?`,
                    acceptText: 'Aceptar',
                    accept: () => {
                        this.axios.post('/app/v1_JefeCaja/EliminarTiposDescuentos', {
                            "Opcion": "E",
                            "SubOpcion": "1",
                            "Hospital": "0",
                            "IdDescuento": item.IdDescuento
                        })
                        .then(resp => {
                            if(resp.data.json[0].tipo_error == 0){
                                this.$vs.notify({
                                    color: 'success',
                                    title: 'Tipo de Descuento',
                                    text: resp.data.json[0].descripcion
                                })
                                this.ObtenerTiposDescuento()
                            }else{
                                this.$vs.notify({
                                    color: 'danger',
                                    title: 'Tipo de Descuento',
                                    text: resp.data.json[0].descripcion
                                })
                            }
                        })
                    }
                })
            }
        },
        watch: {
            selectedTipoDescuento(value){
                // console.log(value)
                if(value == null){
                    this.isActivo = 'hidden'
                    this.labelDescuentoInternas = ''
                    this.labelDescuentoEmergencias = ''
                    this.descuentoInternas = 0
                    this.descuentoEmergencias = 0
                    this.styleHidden = 'display: none'
                } else if(value.Codigo == 'Porcentaje'){
                    this.isActivo = ''
                    this.labelDescuentoInternas = 'Monto Descuento Internas'
                    this.labelDescuentoEmergencias = 'Monto Descuento Emergencias'
                    this.styleHidden = this.getStyle(value.Codigo)

                }else{
                    this.isActivo = 'hidden'
                    this.labelDescuentoInternas = ''
                    this.labelDescuentoEmergencias = ''
                    this.descuentoEmergencias = 0
                    this.descuentoInternas = 0
                    this.styleHidden = 'display: none'
                }
            },
            isVisible(value) {
                if(!value){
                    this.descripcion = ''
                    this.descuento = 0
                    this.selectedTipoDescuento = ''
                    this.observacion = ''
                    this.activo = true
                    this.descuentoInternas = ''
                    this.descuentoEmergencias = ''
                }
            }
        },
        activated(){
            this.ObtenerTiposDescuento()
        }
    }

</script>
<style lang="scss" scoped>
    .th_editar {
        text-align: center;
        width: 150px;
        .btn_editar{
            display: inline-block;
        }
    }
    .default-div {
        padding-left: 5px;
        
        .descripcion{
            width: 350px;
        }
        .activo{
            padding-top: 25px;
        }
        .descuento{
            width: 150px;
        }
        .descuentoInternas{
            margin-top: 5px;
        }
        .descuentoEmergencias{
            margin-top: 5px;
        }
    }
    .observacion{
        margin-top: 15px;
    }
    .multi-span {
        padding-left: 5px;
        padding-top: 5px;
        padding-bottom: 5px;

        .tipoDescuento{
            width: 350px;
        }
    }
</style>