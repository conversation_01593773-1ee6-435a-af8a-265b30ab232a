

<template>

<div>
    <AdmisionEmergencias></AdmisionEmergencias>
</div>
    
</template>




<script>

import AdmisionEmergencias from '@/components/sermesa/modules/admisiones/AdmisionEmergencia.vue'
import { mapFields } from "vuex-map-fields";

export default {
    components: {
        AdmisionEmergencias
    },
    computed:{
        ...mapFields('admision',["AdmisionData"])
    }
   
}


</script>


<style>

</style> 

<!-- 
<template>
    <vx-card title="Datos personales">
        <input v-model="firstName">
        <input v-model="lastName">
        <input v-model="town">
        <vs-button style="margin-right:1px" v-on:click="submit()">
             Guardar
        </vs-button>
    </vx-card>
</template>
  
<script>
import { mapFields } from 'vuex-map-fields';

export default {
    computed: {
        // When using nested data structures, the string
        // after the last dot (e.g. `firstName`) is used
        // for defining the name of the computed property.
        ...mapFields('test', [
            'user.firstName',
            'user.lastName',
            // It's also possible to access
            // nested properties in arrays.
            'addresses[0].town',
        ]),
    },
    methods:{
        submit(){
            console.log(this.$store.state)
        }
    }
};
</script>  -->


