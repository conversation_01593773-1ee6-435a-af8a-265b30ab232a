<template>
<vx-card title="Ingreso de Facturas">
    <ValidationObserver ref="formValidate" v-slot="{ invalid,handleSubmit }">
        <div class="flex mb-5 ">
            <div class="w-full md:w-2/3 lg:w-2/3 xl:w-2/3 p-1">
                <vs-input label="Nombre Médico" class="w-full" :value="value.Medico" :disabled="true" />
            </div>
            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                <vs-input label="NIT" class="w-full" :value="value.NIT" :disabled="true" />
            </div>
        </div>
        <div class="flex mb-5 ">
            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                <SM-Buscar label="Tipo de Médico" :value="value.Tipo" :api="[{Codigo:'C',Nombre:'Cortesía'},{Codigo:'M',Nombre:'Casa'},{Codigo:'CC',Nombre:'Casa-Cortesía'},{Codigo:'R',Nombre:'Referencia'}]" :api_campos="['Nombre']" :api_titulos="['Nombre']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="false" />
            </div>
            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                <SM-Buscar :value="value.Especialidad" label="Especialidad" api="app/Ajenos/Busqueda_Especialidad" :api_cache="true" :api_campos="['Codigo','Descripcion']" :api_titulos="['Codigo','Descripción']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Descripcion" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="false " />
            </div>
            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                <SM-Buscar :value="value.SubEspecialidad" label="SubEspecialidad" api="app/Ajenos/Busqueda_SubEspecialidad" :api_campos="['Codigo','Nombre']" :api_titulos="['Codigo','Nombre']" :api_filtro="{Codigo_Especialidad:value.Especialidad}" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="false" />
            </div>
        </div>
        <vs-divider></vs-divider>
        <div class="flex mb-5 ">
            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                <ValidationProvider name="Fecha de Facturación" rules="required|fecha" v-slot="{ errors }">
                    <vs-input label="Fecha de Facturación" type="date" style="padding:0" class="w-full" v-model="info.FacturaFecha" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                </ValidationProvider>
            </div>
            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                <ValidationProvider name="Serie de Factura" rules="required" v-slot="{ errors }">
                    <vs-input label="Serie de Factura" class="w-full" v-model="info.FacturaSerie" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                </ValidationProvider>
            </div>
            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                <ValidationProvider name="Número de Factura" rules="required" v-slot="{ errors }">
                    <vs-input label="Número de Factura" type="number" class="w-full" v-model="info.FacturaNumero" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                </ValidationProvider>
            </div>
        </div>
        <vs-divider></vs-divider>
        <!-- <vs-alert color="danger" v-if="!permisos.editar"><i class="fas fa-lock"></i> No cuenta con los permisos para editar la tabla de pagos</vs-alert> -->
        <!-- {{tabla}} -->
        <vs-table2 max-items="10" search tooltip pagination :data="tabla">
            <template slot="thead">
                <!-- <th width="50px">Selección</th> -->
                <th order="NombreSucursal" width="110px">Hospital</th>
                <th order="TipoRegistro" width="150px">Tipo Doc.</th>
                <th order="Documento" width="150px">Documento</th>
                <th order="Admision_FechaEgreso" width="150px">Fecha Egreso</th>
                <th order="NombrePaciente">Paciente</th>
                <th order="NombreSeguro">Tipo Paciente</th>
                <th order="NombreCategoria">Tipo Honorario</th>
                <th order="Valor" orderType="number">Monto</th>
            </template>
            <template slot-scope="{data}">
                <tr :key="indextr" v-for="(tr, indextr) in data">
                    <vs-td2>
                        {{ tr.NombreSucursal.toUpperCase() }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.TipoRegistro }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.Documento }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.Admision_FechaEgreso }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.NombrePaciente }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.NombreSeguro }}
                    </vs-td2>

                    <vs-td2>
                        ({{ tr.Categoria }}) {{ tr.NombreCategoria.toUpperCase() }}
                    </vs-td2>

                    <vs-td2>
                        <div style="text-align:right">
                            {{ $formato_moneda(tr.Valor) }}
                        </div>
                    </vs-td2>
                </tr>
            </template>
        </vs-table2>
        <table width="100%" style="border-spacing: 0;">
            <tr style="background-color:#bdc3c7; color:#34495e;border-radius:5px">
                <td style="padding:10px">
                    Total a Facturar
                </td>
                <td width="250px" style="padding:10px; text-align:right;font-weight:bold">
                    {{calculcar}}
                </td>
            </tr>
        </table>
        
        
        <div class="pt-2">
            <vs-button color="danger"  type="filled" @click.native="cancelar">Cancelar</vs-button>
            <!-- <vs-spacer /> -->
            <vs-button v-if="CuentaAjena" style="float:right;margin-right:2px" color="success" type="filled" @click.native="handleSubmit(Guardar().lote)" :disabled="invalid">Asociar Factura</vs-button>
            <vs-button v-else color="success" style="float:right;margin-right:2px" type="filled" @click.native="handleSubmit(Guardar().lote)" :disabled="invalid">Agregar Facturas a Lote</vs-button>
        </div>
    </ValidationObserver>
</vx-card>
</template>

<script>
export default {
    data() {
        return {
            info: {
                FacturaFecha: null,
                FacturaSerie: null,
                FacturaNumero: null
            },
            /**
             * Listado de permisos
             */
            permisos: {
                editar: false
            },

            /**
             * Información de los pagos
             */
            pagos: [],
            pagosSeleccion: []

        }
    },
    props: {
        value: {
            default: null
        },
        tabla: {
            default: []
        },
        CuentaAjena: {
            default: null
        },
        cancelar: {
            default: null
        }
    },
    computed: {
        calculcar() {
            const arr = this.tabla
            return this.$formato_moneda(arr.reduce((acumulador, valor) => acumulador + parseFloat(valor.Valor), 0))
        }
    },
    methods: {
        //=======================================================================================================
        // CONSULTA
        //=======================================================================================================
        Consulta: function() {
            return {
                Consulta_Ajenos: (item) => {
                    // console.log(item)
                    this.info.Medico = `${item.Nombre.trim()} ${item.Apellido.trim()}`
                    this.Consulta().Consulta_InfoAjenos(this.info.Codigo)
                        .then(() => {
                            this.Consulta().Consulta_Pagos(this.info.Codigo)
                        })
                },

                Consulta_InfoAjenos: (codigo) => {
                    return this.axios.post('/app/ajenos/Consulta_Ajenos', {
                            Codigo: codigo
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                let datos = resp.data.json[0]

                                // Remplazando el tipo  si se ha seleccionado tipocasacortesia
                                if (datos['TipoCortesiaCasa'] == true) datos['Tipo'] = 'CC'

                                // console.log(datos.Especialidad)
                                // console.log(datos.Tipo)
                                this.info.Tipo = datos.Tipo
                                this.info.Especialidad = datos.EspecialidadPrincipal.trim()
                                this.info.SubEspecialidad = datos.Especialidad.trim()

                                return datos.Proveedor
                            }
                        })
                },

                Consulta_Pagos: (Ajeno) => {
                    return this.axios.post('/app/ajenos/Honorarios_Pagos_Consulta', {
                            Ajeno
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.pagos = resp.data.json.map(m => {
                                    return {
                                        seleccion: false,
                                        ...m
                                    }
                                })
                            }
                        })
                }
            }
        },

        //=======================================================================================================
        // NUEVO
        //=======================================================================================================
        Nuevo: function() {
            return {}
        },

        //=======================================================================================================
        // GUARDAR
        //=======================================================================================================
        Guardar: function() {
            return {
                lote: () => {
                    let inf = this.info
                    inf.Ajeno = this.value.Codigo
                    inf.data = this.tabla.map((m) => {
                        return {
                            Id: m.Id
                        }
                    })
                    if (this.CuentaAjena) {
                        return this.CuentaAjena(inf)
                    } else {
                        this.axios.post('/app/ajenos/HonorariosLotesFacturasNuevo', inf)
                            .then(() => {
                                this.cancelar(true)
                            })
                            .catch(() => {})
                    }
                }
            }
        },

        //=======================================================================================================
        // EDITAR
        //=======================================================================================================
        Editar: function() {
            return {}
        },

        //=======================================================================================================
        // ELIMINAR
        //=======================================================================================================
        Eliminar: function() {
            return {}
        },

        //=======================================================================================================
        // OTROS
        //=======================================================================================================
        Otros: function() {
            return {
                Limpiar_Datos: () => {
                    Object.entries(this.info).forEach(([key]) => {
                        this.info[key] = null
                    });
                    this.pagos = []
                }
            }
        }
    }
}
</script>

