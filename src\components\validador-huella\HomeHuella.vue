<template>
    <div class="token-container">
        <label class="titulo-pagina">Aviso: Token no configurado para este navegador</label>
        <br>
        <br>
        <label>
            <b>1) </b>Si es la primera vez que abre la aplicación en este navegador debe configurar el token en el plugin..!
        </label>
        <br>
        <br>
        <label>
            <b>Token: </b>
            <span class="token-span">{{ token }}</span> 
        </label>
        <br>
        <br>
        <label>
            <a href="/">refresh</a>
        </label>
        <br>
        <br>
        <label>
            <a href="">Aqui se descarga el plugin!!</a>
        </label>
    </div>
</template>
<script>
    import { savesSrnPc } from './funciones/funciones'

export default {
    data() {
        return {
            token: null,
        }
    },
    methods: {
        tokenValid(){
            // localStorage.removeItem('srnPc')
            if( !localStorage.getItem('srnPc') ){
                savesSrnPc()
                this.token = localStorage.getItem('srnPc')
            }
            
        }
    },
    created() {
        this.tokenValid()

    }

}
</script>

<style lang="scss" scoped>
    .token-container{
        text-align: center;
        padding-top: 5%;
    }
    .titulo-pagina{
        font-size: 250%;
    }
    .span{
        font-size: 20px;
    }
    .token-span{
        font-size: 20px;
    }
</style>