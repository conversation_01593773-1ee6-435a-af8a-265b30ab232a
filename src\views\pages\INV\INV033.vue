<template>
    <div>
        <buscador ref="BuscarProducto" buscador_titulo="Buscador / Producto" :api="'app/v1_OrdenCompra/ConsultaRequisicion_Producto'" 
            :api_filtro="{TipoBodega:tipoBodega}"
            :campos="['Codigo', 'Descripcion', 'PresentacionNombre', 'Laboratorio', 'Proveedor']"
            :titulos="['Codigo / Descripción', '#Descripcion', '#PresentacionNombre', '#Marca', '#Proveedor']"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" />
        <vx-card title="Cruce de Códigos / Traslado de Existencias">
            <div class="flex flex-wrap">
                <vs-row>
                    <vs-col vs-w="12">
                        <label>Fecha</label>
                        <vs-input type="date" v-model="fechaNuevo"></vs-input>
                    </vs-col>
                </vs-row>
            </div>
            <br>
            <div>
                <vs-row>
                    <vs-col vs-w="12">
                        <label>Detalle</label>
                        <vs-textarea rows="5" v-model="detalle" class="mt-2" counter="250"  />
                    </vs-col>
                </vs-row>
            </div>
            <br>
            <div>
                <vs-row>
                    <vs-col vs-w="6">
                        <label class="typo__label">Bodega</label>
                        <multiselect v-model="cb_bodegas" :options="Lista_bodegas" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Bodegas_seleccionado" placeholder="Seleccionar bodega" @input="onChangeBodega">
                            <span slot="noOptions">Lista no disponible.</span>
                        </multiselect>
                    </vs-col>
                    <vs-col vs-w="6" class="spaceObject">
                        <label class="typo__label">Tipos</label>
                        <div class="divRadio">
                            <vs-radio v-model="tipo" vs-name="Tipo" vs-value="0" :disabled="isTipo">Cruce de Códigos</vs-radio>
                            <vs-radio class="spaceObject" v-model="tipo" vs-name="Tipo" vs-value="1" :disabled="isTipo">Traslado de Existencias</vs-radio>
                        </div>
                        
                    </vs-col>
                </vs-row>
            </div>
            <br>
            <vx-card title="Producto a Rebajar">
                <div class="box-custom">
                    <vs-card class="cardx" fixedHeight>
                        <div slot="header">
                            <h3>
                                Producto
                            </h3>
                        </div>
                        <div>
                            <vs-row class="row_margin">
                                <vs-col vs-w="4" vs-type="flex">
                                    <label class="typo__label">Código</label>
                                    <input class="inputLabel" v-model="productoSeleccionadoRebajar.Codigo" disabled />
                                </vs-col>
                                <vs-col vs-w="4" vs-type="flex">
                                    <label class="typo__label">Unidad</label>
                                    <input class="inputLabel" v-model="productoSeleccionadoRebajar.PresentacionNombre" disabled />
                                </vs-col>
                                <vs-col vs-w="4" vs-type="flex">
                                    <label class="typo__label">Cantidad</label>
                                    <vs-input class="vs_input" v-model="productoSeleccionadoRebajar.cantidad" @change="onChangeCantidadRebajar"></vs-input>
                                </vs-col>
                            </vs-row>
                            <br>
                            <br>
                            <vs-row class="row_margin">
                                <vs-col vs-w="8" vs-type="flex">
                                    <label class="typo__label">Producto</label>
                                    <input class="inputLabel" :size="getSize(productoSeleccionadoRebajar.Descripcion)" v-model="productoSeleccionadoRebajar.Descripcion" disabled />
                                </vs-col>
                                <vs-col vs-w="4" vs-type="flex">
                                    <label class="typo__label">Costo Promedio</label>
                                    <vs-input class="vs_input" v-model="productoSeleccionadoRebajar.CostoPromedio" disabled></vs-input>
                                </vs-col>
                            </vs-row>
                            <br>
                            <br>
                            <vs-row class="row_margin">
                                <vs-col vs-w="8" vs-type="flex">
                                    <label class="typo__label">Marca</label>
                                    <input class="inputLabel" v-model="productoSeleccionadoRebajar.Marca" disabled />
                                </vs-col>
                                <vs-col vs-w="4" vs-type="flex">
                                    <label class="typo__label">Total Costo Q.</label>
                                    <vs-input class="vs_input" v-model="productoSeleccionadoRebajar.total_costo"></vs-input>
                                </vs-col>
                            </vs-row>
                        </div>
                        <br>
                        <br>
                        <br>
                        <br>
                        <div slot="footer">
                            <vs-row vs-justify="flex-end">
                                <vs-button icon-pack="feather" icon="icon-search" @click="BuscarProductoRebajar()">Buscar Producto</vs-button>
                            </vs-row>
                        </div>
                    </vs-card>
                </div>
            </vx-card>
            <br>
            <vx-card title="Producto a Cargar">
                <div class="box-custom">
                    <vs-card class="cardx" fixedHeight>
                        <div slot="header">
                            <h3>
                                Producto
                            </h3>
                        </div>
                        <div>
                            <vs-row class="row_margin">
                                <vs-col vs-w="4" vs-type="flex">
                                    <label class="typo__label">Código</label>
                                    <input class="inputLabel" v-model="productoSeleccionadoCargar.Codigo" disabled />
                                </vs-col>
                                <vs-col vs-w="4" vs-type="flex">
                                    <label class="typo__label">Unidad</label>
                                    <input class="inputLabel" v-model="productoSeleccionadoCargar.PresentacionNombre" disabled />
                                </vs-col>
                                <vs-col vs-w="4" vs-type="flex">
                                    <label class="typo__label">Cantidad</label>
                                    <vs-input class="vs_input" v-model="productoSeleccionadoCargar.cantidad" @change="onChangeCantidadCargar" disabled></vs-input>
                                </vs-col>
                            </vs-row>
                            <br>
                            <br>
                            <vs-row class="row_margin">
                                <vs-col vs-w="8" vs-type="flex">
                                    <label class="typo__label">Producto</label>
                                    <input class="inputLabel" :size="getSize(productoSeleccionadoCargar.Descripcion)" v-model="productoSeleccionadoCargar.Descripcion" disabled />
                                </vs-col>
                                <vs-col vs-w="4" vs-type="flex">
                                    <label class="typo__label">Costo Promedio</label>
                                    <vs-input class="vs_input" v-model="productoSeleccionadoCargar.CostoPromedio" disabled></vs-input>
                                </vs-col>
                            </vs-row>
                            <br>
                            <br>
                            <vs-row class="row_margin">
                                <vs-col vs-w="8" vs-type="flex">
                                    <label class="typo__label">Marca</label>
                                    <input class="inputLabel" v-model="productoSeleccionadoCargar.Marca" disabled />
                                </vs-col>
                                <vs-col vs-w="4" vs-type="flex">
                                    <label class="typo__label">Total Costo Q.</label>
                                    <vs-input class="vs_input" v-model="productoSeleccionadoCargar.total_costo"></vs-input>
                                </vs-col>
                            </vs-row>
                        </div>
                        <br>
                        <br>
                        <br>
                        <br>
                        <div slot="footer">
                            <vs-row vs-justify="flex-end">
                                <vs-button icon-pack="feather" icon="icon-search" @click="BuscarProductoCargar()">Buscar Producto</vs-button>
                            </vs-row>
                        </div>
                    </vs-card>
                </div>
            </vx-card>
            <vs-row class="row_margin" vs-align="flex-start" vs-type="flex" vs-justify="center" vs-w="12">
                <vs-col vs-w="12" vs-type="flex" vs-justify="center" vs-align="center" >
                    <vs-button type="filled" icon-pack="fas" icon="fa-broom" color="warning" @click="LimpiarCampos()">Limpiar</vs-button>
                    &nbsp;
                    &nbsp;
                    <vs-button type="filled" icon-pack="fas" icon="fa-save" color="success" @click="Confirmar()">Guardar</vs-button>
                </vs-col>
            </vs-row>
        </vx-card>
    </div>
</template>
<script>
import moment from 'moment';
import Multiselect from 'vue-multiselect'    
import "vue-multiselect/dist/vue-multiselect.min.css"
export default {
    components: {
        Multiselect,
        //Timeline,
    },
    data() {
        return{
            fechaNuevo: '',
            detalle: '',
            cb_bodegas: '',
            Lista_bodegas: [],
            tipo: '0',
            isTipo: true,
            productoSeleccionadoRebajar: {
                Codigo: '',
                PresentacionNombre: '',
                cantidad: 0,
                Descripcion: '',
                CostoPromedio: 0,
                Marca: '',
                total_costo: 0
            },
            productoSeleccionadoCargar: {
                Codigo: '',
                PresentacionNombre: '',
                cantidad: 0,
                Descripcion: '',
                CostoPromedio: 0,
                Marca: '',
                TipoBodega: '',
                total_costo: 0
            },
            permisos_tipos_bodegas: [],
            permiso_bodega_vencido:'',
            id_bodega_seleccionada: '',
            tipoBodega: '',
            codigoAgrupacionBodega: 0,
            permiso_tipo_operacion: [],
            porciento_ganancia: 1
        }
    },
    methods: {
        Consultar_Bodega(Operacion, BodegasDespacho, BodegaTransito) {
            const url = this.$store.state.global.url
            this.axios.post(url+'app/v1_OrdenCompra/consulta_bodegaEmpReal', {
                operacion: Operacion,
                BodegasDepacho: BodegasDespacho,
                BodegaTransito: BodegaTransito
            })
            .then(resp => {

                if (resp.data.codigo != 0) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Bodegas',
                        text: resp.data.mensaje,
                    })
                } else {
                    this.Lista_bodegas = resp.data.json;                                              
                }
            })
            .catch(() => { })
    
        },
        Bodegas_seleccionado({
            NOMBRE
        }) {
            return ` ${NOMBRE} `;
        },
        onChangeBodega(value) {
            if(this.cb_bodegas != null){
                if(this.tipo == '0' && value.CODIGOAGRUPACION == 1){
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Cruce de Códigos / Traslado de Existencias',
                        text: 'Transacción Inválida: No se puede realizar este traslado por cruce de Códigos en Bodegas de Despacho, use el método de Ajuste.',
                    })
                    return
                }
                if(this.tipo == '1' && value.CODIGOAGRUPACION != '1'){
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Cruce de Códigos / Traslado de Existencias',
                        text: 'Transacción Inválida: No se puede realizar este traslado existencias de Códigos en en sub Bodegas, use el método Cruce de Códigos.',
                    })
                    return
                }
                if(this.tipo == '1' && value.CODIGO == '1000'){
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Cruce de Códigos / Traslado de Existencias',
                        text: 'Transacción Inválida: No se puede realizar este traslado existencias en bodegas de Consignación.',
                    })
                    return
                }
                if (value !== null && value.length !== 0) {
                    this.id_bodega_seleccionada = value.CODIGO
                    this.tipoBodega = value.TipoBodega
                    this.codigoAgrupacionBodega = value.CODIGOAGRUPACION

                } else {
                    this.id_bodega_seleccionada = ''
                    this.tipoBodega = ''
                }
            }
        },
        onChangeCantidadRebajar(){
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_OrdenCompra/ConsultaExistencia', {
                CodigoProducto: this.productoSeleccionadoRebajar.Codigo,
                CodigoBodega: this.id_bodega_seleccionada
            })
            .then(resp => {
                let cantidadExistencias = resp.data.resultado
                if(parseFloat(this.productoSeleccionadoRebajar.cantidad) > parseFloat(cantidadExistencias)){
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Inventario',
                        text: 'No hay suficientes existencias para esta operación..!!!!',
                    })
                    this.productoSeleccionadoRebajar.cantidad = 0
                }else{
                    if(this.productoSeleccionadoCargar.Codigo != '' || this.productoSeleccionadoCargar.Codigo != null){
                        this.productoSeleccionadoCargar.cantidad = this.productoSeleccionadoRebajar.cantidad
                    }
                }
            })
            this.productoSeleccionadoRebajar.total_costo = parseFloat(this.productoSeleccionadoRebajar.cantidad * this.productoSeleccionadoRebajar.CostoPromedio).toFixed(2)
        },
        onChangeCantidadCargar(){
            this.productoSeleccionadoCargar.total_costo = parseFloat(this.productoSeleccionadoCargar.cantidad * this.productoSeleccionadoCargar.CostoPromedio).toFixed(2)
        },
        getSize(valor){
            return valor ? valor.length : 0
        },
        BuscarProductoRebajar(){
            if(this.tipoBodega == ''){
                this.$vs.notify({
                    color: 'danger',
                    title: 'Cruce de Códigos / Traslado de Existencias',
                    text: 'Debe de seleccionar una bodega.',
                })
                return
            }else{
                this.$refs.BuscarProducto.iniciar((data) => {
                    if(data != null){
                        this.productoSeleccionadoRebajar.Codigo = data.Codigo
                        this.productoSeleccionadoRebajar.Descripcion = data.Descripcion
                        this.productoSeleccionadoRebajar.PresentacionNombre = data.PresentacionNombre
                        this.productoSeleccionadoRebajar.Marca = data.Marca
                        this.productoSeleccionadoRebajar.CostoPromedio = parseFloat(data.CostoPromedio).toFixed(2)

                        if(data.TipoBodega == 'G'){
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Cruce de Códigos / Traslado de Existencias',
                                text: 'No se incluye productos en consignación.',
                            })
                            this.productoSeleccionadoRebajar = {
                                Codigo: '',
                                PresentacionNombre: '',
                                cantidad: 0,
                                Descripcion: '',
                                CostoPromedio: 0,
                                Marca: '',
                                total_costo: 0
                            }
                            return
                        }
                        if(this.productoSeleccionadoCargar.Codigo == data.Codigo){
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Cruce de Códigos / Traslado de Existencias',
                                text: 'Intenta trasladar existencias al mismo producto. Intente de nuevo.',
                            })
                            this.productoSeleccionadoRebajar = {
                                Codigo: '',
                                PresentacionNombre: '',
                                cantidad: 0,
                                Descripcion: '',
                                CostoPromedio: 0,
                                Marca: '',
                                total_costo: 0
                            }
                            return
                        }
                    }
                })
            }
            
        },
        BuscarProductoCargar(){
            if(this.tipoBodega == ''){
                this.$vs.notify({
                    color: 'danger',
                    title: 'Cruce de Códigos / Traslado de Existencias',
                    text: 'Debe de seleccionar una bodega.',
                })
                return
            }else{
                this.$refs.BuscarProducto.iniciar((data) => {
                    if(data != null){
                        this.productoSeleccionadoCargar.Codigo = data.Codigo
                        this.productoSeleccionadoCargar.Descripcion = data.Descripcion
                        this.productoSeleccionadoCargar.PresentacionNombre = data.PresentacionNombre
                        this.productoSeleccionadoCargar.Marca = data.Marca
                        this.productoSeleccionadoCargar.CostoPromedio = parseFloat(data.CostoPromedio).toFixed(2)
                        this.productoSeleccionadoCargar.TipoBodega = data.TipoBodega
                        this.productoSeleccionadoCargar.cantidad = this.productoSeleccionadoRebajar.cantidad
                        this.productoSeleccionadoCargar.total_costo = parseFloat(this.productoSeleccionadoCargar.cantidad * parseFloat(data.CostoPromedio).toFixed(2)).toFixed(2)

                        if(data.TipoBodega == 'G'){
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Cruce de Códigos / Traslado de Existencias',
                                text: 'No se incluye productos en consignación.',
                            })
                            this.productoSeleccionadoCargar = {
                                Codigo: '',
                                PresentacionNombre: '',
                                cantidad: 0,
                                Descripcion: '',
                                CostoPromedio: 0,
                                Marca: '',
                                total_costo: 0
                            }
                            return
                        }
                        if(this.productoSeleccionadoRebajar.Codigo == data.Codigo){
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Cruce de Códigos / Traslado de Existencias',
                                text: 'Intenta trasladar existencias al mismo producto. Intente de nuevo.',
                            })
                            this.productoSeleccionadoCargar = {
                                Codigo: '',
                                PresentacionNombre: '',
                                cantidad: 0,
                                Descripcion: '',
                                CostoPromedio: 0,
                                Marca: '',
                                total_costo: 0
                            }
                            return
                        }
                    }
                })
            }
            
        },
        LimpiarCampos(){
            this.productoSeleccionadoRebajar = ''
            this.productoSeleccionadoCargar = ''
            this.id_bodega_seleccionada = ''
            this.cb_bodegas = ''
            this.tipoBodega = ''
            this.codigoAgrupacionBodega = ''
            this.detalle = ''
        },
        async Confirmar(){
            if(this.productoSeleccionadoCargar.cantidad <= 0){
                this.$vs.notify({
                    color: 'danger',
                    title: 'Cruce de Códigos / Traslado de Existencias',
                    text: 'No nay nada que trasladar..No puede continuar.',
                })
                return
            }
            if(this.detalle == ''){
                this.$vs.notify({
                    color: 'danger',
                    title: 'Cruce de Códigos / Traslado de Existencias',
                    text: 'Ingrese un motivo valido..',
                })
                return
            }
            if(this.id_bodega_seleccionada == ''){
                this.$vs.notify({
                    color: 'danger',
                    title: 'Cruce de Códigos / Traslado de Existencias',
                    text: 'Ingrese una bodega valida..',
                })
                return
            }
            if(this.productoSeleccionadoRebajar == ''){
                this.$vs.notify({
                    color: 'danger',
                    title: 'Cruce de Códigos / Traslado de Existencias',
                    text: 'Sin un producto a rebajar no se puede realizar esta operacion ..',
                })
                return
            }
            if(this.productoSeleccionadoCargar == ''){
                this.$vs.notify({
                    color: 'danger',
                    title: 'Cruce de Códigos / Traslado de Existencias',
                    text: 'Sin un producto a carga no se puede realizar esta operacion ..',
                })
                return
            }
            await this.PorcientoGanancia()

            if(this.tipo == 0){
                this.CruceCodigos()
            }else{
                this.TrasladoExistencias()
            }
        },
        CuentaDiversos(){
            const url = this.$store.state.global.url
           this.axios.post(url+'app/v1_OrdenCompra/ConsultaDiversos', {
            })
            .then(resp => {

                if (resp.data.codigo != 0) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Cruce de Códigos / Traslado de Existencias',
                        text: resp.data.mensaje,
                    }) 
                } 
            })
        },
        PorcientoGanancia(){
            const url = this.$store.state.global.url
            return this.axios.post(url+'app/v1_OrdenCompra/ConsultaXGanancia', {
                CodigoBodega: this.id_bodega_seleccionada
            })
            .then(resp => {

                if (resp.data.codigo != 0) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Cruce de Códigos / Traslado de Existencias',
                        text: resp.data.mensaje,
                    })
                } else {
                    this.porciento_ganancia = resp.data.resultado ? parseFloat(resp.data.resultado).toFixed(2) : 0.00
                }
            })
        },
        TrasladoExistencias(){
            const url = this.$store.state.global.url
            return this.axios.post(url+'app/v1_OrdenCompra/TrasladoExistencias', {
                CodigoProducto1: this.productoSeleccionadoRebajar.Codigo,
                CodigoBodega: this.id_bodega_seleccionada,
                Comentario: this.detalle,
                Cantidad1: this.productoSeleccionadoRebajar.cantidad,
                Costo1: this.productoSeleccionadoRebajar.CostoPromedio,
                Medida1: this.productoSeleccionadoRebajar.PresentacionNombre,
                CodigoProducto2: this.productoSeleccionadoCargar.Codigo,
                Cantidad2: this.productoSeleccionadoCargar.cantidad,
                Costo2: this.productoSeleccionadoCargar.CostoPromedio,
                Medida2: this.productoSeleccionadoCargar.PresentacionNombre,
                PorGanancia: this.porciento_ganancia
            })
            .then(resp => {

                if (resp.data.codigo != 0) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Cruce de Códigos / Traslado de Existencias',
                        text: resp.data.mensaje,
                    })
                } else {
                    this.$vs.notify({
                        color: 'success',
                        title: 'Cruce de Códigos / Traslado de Existencias',
                        text: 'Proceso realizado con exito..',
                    })
                    this.LimpiarCampos()
                }
            })
        },
        CruceCodigos(){
            const url = this.$store.state.global.url
            return this.axios.post(url+'app/v1_OrdenCompra/CruceCodigos', {
                CodigoProducto1: this.productoSeleccionadoRebajar.Codigo,
                CodigoBodega: this.id_bodega_seleccionada,
                Comentario: this.detalle,
                Cantidad1: this.productoSeleccionadoRebajar.cantidad,
                Costo1: this.productoSeleccionadoRebajar.CostoPromedio,
                Medida1: this.productoSeleccionadoRebajar.PresentacionNombre,
                CodigoProducto2: this.productoSeleccionadoCargar.Codigo,
                Cantidad2: this.productoSeleccionadoCargar.cantidad,
                Costo2: this.productoSeleccionadoCargar.CostoPromedio,
                Medida2: this.productoSeleccionadoCargar.PresentacionNombre,
                PorGanancia: this.porciento_ganancia
            })
            .then(resp => {
                if (resp.data.codigo != 0) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Cruce de Códigos / Traslado de Existencias',
                        text: resp.data.mensaje,
                    })
                } else {
                    this.$vs.notify({
                        color: 'success',
                        title: 'Cruce de Códigos / Traslado de Existencias',
                        text: 'Proceso realizado con exito..',
                    })
                    this.LimpiarCampos()
                }
            })
        }
    },
    mounted() {
        this.fechaNuevo = moment(new Date(Date.now())).format('YYYY-MM-DD')
        for(let privilegio of this.$store.state.privilegios){
            if(privilegio.Privilegio.includes("TIPO_BODEGA")){                      
                let tipobodega = privilegio.Privilegio.split('_')[2];
                if(tipobodega == 'T'){
                    this.permiso_bodega_vencido = 'S'
                }else{
                    this.permisos_tipos_bodegas.push(privilegio.Privilegio.split('_')[2])                    
                }                                                       
            }

            if(privilegio.Privilegio.includes("TIPO_OPERACION")){
                let tipoOperacion = privilegio.Privilegio.split('_')[2]
                this.permiso_tipo_operacion.push(tipoOperacion)
                if(this.permiso_tipo_operacion.length > 1){
                    this.tipo = 0
                    this.isTipo = false
                }else{
                    this.isTipo = true
                    this.tipo = tipoOperacion
                }
            }
        }

        this.Consultar_Bodega('P',this.permisos_tipos_bodegas.join(","),this.permiso_bodega_vencido);
    },
    watch: {
        tipo(value){
            if(value == '0'){
                if(this.codigoAgrupacionBodega == 1){
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Cruce de Códigos / Traslado de Existencias',
                        text: 'Transacción Inválida: No se puede realizar este traslado por cruce de Códigos en Bodegas de Despacho, use el método de Ajuste.',
                    })
                }
            }
            
        },
        'productoSeleccionadoCargar.cantidad'(){
            this.productoSeleccionadoCargar.total_costo = parseFloat(this.productoSeleccionadoCargar.cantidad * this.productoSeleccionadoCargar.CostoPromedio).toFixed(2)
        }
    }
}
</script>
<style scoped>
    .divRadio{
        padding-left: 20px;
        padding-top: 15px;
        border-style: solid;
        border-color: #CCC;
    }
    .spaceObject{
        padding-left: 20px;
    }
    .box-custom{
        box-shadow: 0px 15px 40px -10px;
        padding: 15px;
        
    }
    .row_margin{
        padding-top: 20px;
    }
    .inputLabel{
            margin-left: 10px;
            color:#E74C3C;
            opacity: 0.7;
            border: 0;
            font-size: 18px;
            background-color: white;
            font-weight: bold;

        }
    .vs_input{
        padding-left: 15px;
        padding-bottom: 12px;
    }
</style>