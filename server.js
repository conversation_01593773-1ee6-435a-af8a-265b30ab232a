const express = require('express');
const history = require('connect-history-api-fallback');
const cors = require('cors');
const { Object } = require('core-js');
const socket = require('./socket');

if (!Object.hasOwn) {
    Object.hasOwn = function (obj, prop) {
      return Object.prototype.hasOwnProperty.call(obj, prop);
    };
  }

const app = express();
socket.iniciar(app)

require('dotenv').config()

app.use(cors())

const staticFileMiddleware = express.static(__dirname + '/dist');

app.use(staticFileMiddleware);

/**
 * Habilita la página
 */
app.use(history({
    disableDotRule: true,
    verbose: false
}));
app.use(staticFileMiddleware);

let env_instancias = {}
let env_instancias_existe = false
Object.entries(process.env).forEach(([key, value]) => {
    if (key.indexOf("VUE_APP_URL_") >= 0){
        env_instancias_existe = true
        env_instancias[key] = value
    }
});


/**
 * Respondiendo información de la instancia
 */
app.get('/Instancia', function (req, res) {
    let instancia = {
        Instancia: process.env.VUE_APP_INSTANCE,
        Instancias: (env_instancias_existe)?env_instancias:null,
        Token: process.env.VUE_APP_TOKENDEFAULT
    }
    // console.log(instancia)
    res.status(200).json(instancia);
});

const port = 8080;
app.listen(port, () => {
    console.log(`Servicio en el puerto ${port}!`);
});