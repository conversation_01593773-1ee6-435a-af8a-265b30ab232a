<template>
<vx-card title="Tabla de Pagos de Honorarios SASI" :class="[(editar)?'edicion':'']">
    <vs-popup classContent="popup-example" title="Clonar" :active.sync="popup.mostrar">
        <ValidationObserver ref="formValidate" v-slot="{ invalid,handleSubmit }" mode="lazy">
            <vs-row vs-justify="center" style="height:180px">
                <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5 p-1">
                    <ValidationProvider name="Origen" rules="required" v-slot="{ errors }">
                        <v-select :options="periodo_listado" v-model="clonar.origen" label="Origen" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5 p-1">
                    <ValidationProvider name="Destino" rules="required" v-slot="{ errors }">
                        <v-select :options="[periodo_listado[0]+1]" v-model="clonar.destino" label="Destino" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                    </ValidationProvider>
                </div>
                <!-- {{clonar}} -->
            </vs-row>
            <vs-row>
                <vs-spacer></vs-spacer>
                <vs-button color="success" type="filled"  @click.native="handleSubmit(()=>Guardar().Clonar())" :disabled="invalid">Agregar</vs-button>
            </vs-row>
        </ValidationObserver>
    </vs-popup>

    <vs-popup classContent="popup-example" title="Arancel" :active.sync="popup.agregar_pago">
        <vx-input-group class="">
            <vs-input class="inputx w-full" placeholder="Nombre del Tipo de Pago" v-model="popup.pago" />
            <template slot="append">
                <div class="append-text btn-addon">
                    <vs-button color="success" type="filled" @click="Nuevo().Nuevo_TipoPago()">Agregar</vs-button>
                </div>
            </template>
        </vx-input-group>
        <vs-divider>Listado de Pagos</vs-divider>
        <vs-table max-items="10" pagination :data="pagos">
            <template slot="thead">
                <vs-th>Tipo de Pago</vs-th>
                <!-- <vs-th>Acciones</vs-th> -->
            </template>

            <template slot-scope="{data}">
                <vs-tr :key="indextr" v-for="(tr, indextr) in data">
                    <vs-td :data="tr.Arancel">
                        {{ tr.Tipo }}
                    </vs-td>
                </vs-tr>
            </template>
        </vs-table>
    </vs-popup>

    <vs-row vs-justify="center">
        <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5 p-1">
            <v-select :options="periodo_listado" v-model="periodo" label="Título Médico" />
        </div>
        <vs-spacer></vs-spacer>
        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
            <vs-button color="success" class="w-full" type="filled" @click="popup.mostrar = true">Nuevo Periodo</vs-button>
        </div>
    </vs-row>
    <div style="background-color:#E74C3C;color:white;padding:5px;border-radius:0 0 5px 5px" v-if="periodo != periodo_actual">No se pueden editar información de periodos anteriores</div>
    <vs-divider></vs-divider>
    <div style="background-color:#E74C3C;color:white;padding:5px;border-radius:0 0 5px 5px" v-if="!permisos.editar"><i class="fas fa-lock"></i> No cuenta con los permisos para editar la tabla de pagos</div>

    <div v-if="editar" class="editar">
        <div>
            <!-- {{editar}} -->
            <small>Tipo de Médico:</small><br>{{pagos.filter(f=>f.Codigo == editar.IdTipoPago)[0].Tipo}}
            <br>
            <small>Arancel:</small><br>{{aranceles.filter(f=>f.Codigo==editar.IdArancel)[0].Arancel}}
            <vs-divider></vs-divider>
            <table>
                <tr>
                    <td width="250px">
                        <vs-input class="w-full" label="Valor" v-model="editar.valor" type="number"> </vs-input>
                    </td>
                    <td width="100px" style="padding:0px 10px">
                        <label>Porcentaje</label>
                        <vs-switch v-model="editar.es_porcentaje" />
                    </td>
                </tr>
            </table>
            <vs-divider></vs-divider>
            <div class="flex">
                <vs-button color="primary" icon-pack="far" @click.native="Otros().Cancelar(editar)" style="display:inline-block">Cancelar</vs-button>
                <vs-spacer></vs-spacer>
                <vs-button  @click.native="Guardar().Guardar_Arancel(editar)">Guardar</vs-button>

            </div>
        </div>
    </div>

    <vs-table2 max-items="10" pagination :data="pagos" :encabezado="aranceles">
        <template slot="thead" slot-scope="{encabezado}">
            <th width="950px">ARANCELES / TIPOS DE MÉDICO</th>
            <th v-for="(item,key) in encabezado" :key="key" width="550px">{{item.Arancel}}</th>
        </template>

        <template slot-scope="{data}">
            <tr :key="indextr" v-for="(tr, indextr) in data">
                <vs-td2 :data="tr.Tipo">
                    {{ tr.Tipo}}
                </vs-td2>
                <vs-td2 width="250px" v-for="(item,key) in tr.Sub" :key="'aranceles_'+key">
                    <div style="cursor:pointer;border:1px dashed #ccc;padding:10px" @click="(permisos.editar && periodo == periodo_actual)?Editar().Editar_Monto(item):null">
                        <vx-tooltip :title="aranceles[key].Arancel" :text="tr.Tipo">
                            <div style="min-height:20px">
                                {{item.valor}}{{(item.es_porcentaje)?"%":""}}
                            </div>
                        </vx-tooltip>
                    </div>
                </vs-td2>
            </tr>
        </template>
    </vs-table2>

    <!-- POR SUB ESPECIALIDAD -->
    <div class="card">
        <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy"  v-if="periodo == periodo_actual">
            <h5>SubEspecialidades</h5>
            <vs-divider></vs-divider>
            <div class="flex mb-5 ">
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <ValidationProvider name="SubEspecialidad" rules="required" v-slot="{ errors }">
                        <vs-select label="Subespecialidad" class="w-full" v-model="subespecialidad.sugespecialista" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null">
                            <vs-select-item :key="key" :value="item.Codigo" :text="item.Nombre" v-for="(item,key) in subespecialidad.listado" />
                        </vs-select>
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <ValidationProvider name="Arancel" rules="required" v-slot="{ errors }">
                        <vs-select label="Arancel" class="w-full" v-model="subespecialidad.arancel" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null">
                            <vs-select-item :key="key" :value="item.Codigo" :text="item.Arancel" v-for="(item,key) in aranceles" />
                        </vs-select>
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <table>
                        <tr>
                            <td width="250px">
                                <ValidationProvider name="Valor" rules="required" v-slot="{ errors }">
                                    <vs-input class="w-full" label="Valor" v-model="subespecialidad.valor" type="number" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"> </vs-input>
                                </ValidationProvider>
                            </td>
                            <td width="100px" style="padding:0px 10px">
                                <label>Porcentaje</label>
                                <vs-switch v-model="subespecialidad.es_porcentaje" />
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="flex ">
                <vs-spacer></vs-spacer>
                <!-- <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1" style="text-align:right"> -->
                <vs-button :disabled="invalid" @click.native="handleSubmit(Guardar().SubEspecialidad())" >Guardar</vs-button>
                <!-- </div> -->
            </div>
        </ValidationObserver>
        <!-- {{subespecialidad.listado}} -->
        <!-- {{tablasubespecialista}} -->

        <!-- aranceles -->
        <vs-table2 max-items="10" pagination :data="tablasubespecialista" search>
            <template slot="thead">
                <th>SubEspecialidades</th>
                <th>Arancel</th>
                <th width="150px">Valor</th>
                <th width="100px">Acción</th>
                <!-- <th v-for="(item,key) in encabezado" :key="key" width="550px">{{item.Arancel}}</th> -->
            </template>

            <template slot-scope="{data}">
                <tr :key="indextr" v-for="(tr, indextr) in data">
                    <vs-td2>
                        {{subespecialidad.listado.filter(f=>f.Codigo == tr.IdSubEspecialidad)[0].Nombre}}
                    </vs-td2>
                    <vs-td2>
                        {{aranceles.filter(f=>f.Codigo ==tr.IdArancel)[0].Arancel}}
                    </vs-td2>
                    <vs-td2 style="text-align:right">
                        {{ tr.Valor}} <span v-if="tr.Es_Porcentaje">(%)</span>
                    </vs-td2>
                    <vs-td2>
                        <vs-button @click="Eliminar().SubEspecialidad(tr)" color="danger" size="small" style="display:inline" class="mr-2" icon-pack="feather" icon="icon-x"></vs-button>
                    </vs-td2>
                    <!-- <vs-td2 width="250px" v-for="(item,key) in tr.Sub" :key="'aranceles_'+key">
                        <div style="cursor:pointer;border:1px dashed #ccc;padding:10px" @click="(permisos.editar && periodo == periodo_actual)?Editar().Editar_Monto(item):null">
                            <vx-tooltip :title="aranceles[key].Arancel" :text="tr.Tipo">
                                <div style="min-height:20px">
                                    {{item.valor}}{{(item.es_porcentaje)?"%":""}}
                                </div>
                            </vx-tooltip>
                        </div>
                    </vs-td2> -->
                </tr>
            </template>
        </vs-table2>

    </div>
</vx-card>
</template>

<script>
import vSelect from 'vue-select'
import bitacora from '@/components/sermesa/funciones/SMBitacora'
export default {
    data() {
        return {
            /**
             * Documentación del módulo
             */
            doc_: [
                'Es utilizado para indicar el valor de los aranceles por tipo de médico.',
                'Se determina por periodo (año)'
            ],

            /**
             * Listado de todos los aranceles
             */
            aranceles: [],

            /**
             * SubEspecialidad
             */
            subespecialidad: {
                arancel: null,
                sugespecialista: null,
                valor: null,
                es_porcentaje: false,
                listado: []
            },

            /**
             * Información al editar
             */
            editar: null,

            /**
             * Listado de todos los pagos
             */
            pagos: [],

            /**
             * Tabla de honorarios
             */
            tablahonorarios: [],
            tablasubespecialista: [],

            /**
             * Popup
             */
            popup: {
                mostrar: false,
                origen: null,
                destino: null
            },

            /**
             * Clonar
             */
            clonar: {
                origen: null,
                destino: null
            },

            /**
             * Reporte
             */
            reporte: {
                mostrar: false,
                src: null,
            },

            /**
             * Permisos
             */
            permisos: {
                admin_arancel: false,
                admin_tipopago: false,
                editar: false
            },

            /**
             * Periodo
             */
            periodo: null,
            periodo_actual: null,
            periodo_listado: []

        }
    },
    watch: {
        periodo() {
            this.Consulta().Consulta_TablaHonorarios()
            this.Consulta().Consulta_TablaHonorarios("SUB")
        }
    },
    components: {
        'v-select': vSelect,
    },
    methods: {
        //=======================================================================================================
        // CONSULTA
        //=======================================================================================================
        Consulta: function() {
            return {
                /**
                 * Consulta de aranceles
                 * Permite realizar una consulta con todos los ajenos
                 */
                Consulta_Aranceles: () => {
                    return this.axios.post('/app/ajenos/Busqueda_Catalogo_Aranceles', {})
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.aranceles = resp.data.json
                            }
                        })
                },

                /**
                 * Consuta de Subespecialidad
                 */
                Consulta_SubEspecialidad: () => {
                    return this.axios.post('/app/Ajenos/Busqueda_SubEspecialidad', {})
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.subespecialidad.listado = resp.data.json
                            }
                        })
                },

                /**
                 * consulta de Pagos
                 */
                Consulta_Pagos: () => {
                    return this.axios.post('/app/ajenos/Busqueda_Sasi_Tipos_Pagos', {})
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.pagos = resp.data.json.map(p => {
                                    p.Sub = []
                                    this.aranceles.map(a => {
                                        let valor = this.Otros().Valor_TablaHonorarios(a.IdArancel, p.IdTipoPago)
                                        let valor_ = (valor) ? parseFloat(valor) : null
                                        let porcentaje_ = ((valor && valor.charAt(valor.length - 1) == "%") ? true : false)

                                        p.Sub.push({
                                            IdArancel: a.Codigo,
                                            IdTipoPago: p.Codigo,
                                            valor: valor_,
                                            valor_temp: valor_,
                                            es_porcentaje: porcentaje_,
                                            es_porcentaje_temp: porcentaje_
                                        })
                                    })
                                    return p
                                })

                            }
                        })
                },

                /**
                 * consulta de valores entre Aranceles y Pagos
                 */
                Consulta_TablaHonorarios: (operacion = 'INFO') => {
                    return this.axios.post('/app/ajenos/Consulta_TablaHonorarios', {
                            periodo: this.periodo,
                            operacion
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                if (operacion == "INFO") {
                                    this.tablahonorarios = resp.data.json
                                    this.pagos.map(p => {
                                        p.Sub.map(s => {
                                            let valor = this.Otros().Valor_TablaHonorarios(s.IdArancel, s.IdTipoPago)
                                            let valor_ = (valor) ? parseFloat(valor) : null
                                            let porcentaje_ = ((valor && valor.charAt(valor.length - 1) == "%") ? true : false)

                                            s.valor = valor_
                                            s.valor_temp = valor_
                                            s.es_porcentaje = porcentaje_
                                            s.es_porcentaje_temp = porcentaje_

                                        })
                                    })
                                } else {
                                    this.tablasubespecialista = resp.data.json
                                }
                            }
                        })
                },
            }
        },

        //=======================================================================================================
        // NUEVO
        //=======================================================================================================
        Nuevo: function() {
            return {
                Nuevo_Arancel: () => {
                    return this.axios.post('/app/ajenos/Guardar_Arancel', {
                            arancel: this.popup.arancel,
                            codigo: this.popup.arancel_codigo
                        })
                        .then(() => {
                            this.editar = null
                            this.Consulta().Consulta_Aranceles()
                        })
                },

                Nuevo_TipoPago: () => {
                    return this.axios.post('/app/ajenos/Guardar_TipoPago', {
                            tipo: this.popup.pago,
                        })
                        .then(() => {
                            this.Consulta().Consulta_Pagos()
                        })
                }
            }
        },

        //=======================================================================================================
        // GUARDAR
        //=======================================================================================================
        Guardar: function() {
            return {
                Guardar_Arancel: (item) => {
                    let valores = {
                        Periodo: this.periodo,
                        IdArancel: item.IdArancel,
                        IdTipoPago: item.IdTipoPago,
                        es_porcentaje: (item.es_porcentaje) ? 1 : 0,
                        Bitacora2: bitacora.obtener()
                    }
                    if (item.valor > 0) valores.valor = item.valor
                    return this.axios.post('/app/ajenos/Guardar_Tabla_SASI', valores)
                        .then(() => {
                            this.Consulta().Consulta_TablaHonorarios()
                        })
                },
                SubEspecialidad: () => {
                    const valores = {
                        Tipo: 2,
                        Periodo: this.periodo,
                        IdArancel: this.subespecialidad.arancel,
                        SubEspecialidad: this.subespecialidad.sugespecialista,
                        Valor: this.subespecialidad.valor,
                        es_porcentaje: (this.subespecialidad.es_porcentaje) ? 1 : 0,
                    }
                    // console.log(valores)
                    return this.axios.post('/app/ajenos/Guardar_Tabla_SASI', valores)
                        .then(() => {
                            return this.Consulta().Consulta_TablaHonorarios("SUB")
                        })
                },

                Clonar: () => {
                    return this.axios.post('/app/ajenos/Clonar_TablaPagos', this.clonar)
                }
            }
        },

        //=======================================================================================================
        // EDITAR
        //=======================================================================================================
        Editar: function() {
            return {
                Editar_Monto: (item) => {
                    this.pagos.map(d => {
                        d.Sub.map(s => {
                            s.valor = s.valor_temp
                            s.es_porcentaje = s.es_porcentaje_temp
                        })
                    })
                    this.editar = {
                        ...item
                    }
                    // console.log(this.editar)
                    bitacora.registrar(this.editar, {
                        IdArancel: this.editar.IdArancel,
                        IdTipoPago: this.editar.IdTipoPago,
                    })

                }
            }
        },

        //=======================================================================================================
        // ELIMINAR
        //=======================================================================================================
        Eliminar: function() {
            return {
                Eliminar_Arancel: (item) => {
                    this.$vs.dialog({
                        type: 'confirm',
                        color: 'danger',
                        title: 'Confirmación',
                        text: '¿Desea eliminar el arancel ' + item.Arancel + '?',
                        accept: () => {
                            this.axios.post('/app/ajenos/Eliminar_Arancel', {
                                    IdArancel: item.IdArancel
                                })
                                .then(() => {
                                    return this.Consulta().Consulta_Aranceles()
                                })
                                .then(() => {
                                    this.Consulta().Consulta_Pagos()
                                })
                        }
                    })
                },

                Eliminar_Pago: (item) => {
                    this.$vs.dialog({
                        type: 'confirm',
                        color: 'danger',
                        title: 'Confirmación',
                        text: '¿Desea eliminar el tipo de pago ' + item.Tipo + '?',
                        accept: () => {
                            this.axios.post('/app/ajenos/Eliminar_TipoPago', {
                                    IdTipoPago: item.IdTipoPago
                                })
                                .then(() => {
                                    this.Consulta().Consulta_Pagos()
                                })
                        }
                    })
                },

                SubEspecialidad: (item) => {
                    
                    this.$vs.dialog({
                        type: 'confirm',
                        color: 'danger',
                        title: 'Confirmación',
                        acceptText: 'Eliminar SubEspecialista',
                        cancelText: 'Cancelar',
                        text: `¿Desea eliminar el Subespecialista de la tabla?`,
                        accept: () => {
                            const valores = {
                                Tipo: 2,
                                Periodo: this.periodo,
                                IdArancel: item.IdArancel,
                                SubEspecialidad: item.IdSubEspecialidad,
                                Valor: 0
                            }
                            // console.log(valores)
                            return this.axios.post('/app/ajenos/Guardar_Tabla_SASI', valores)
                                .then(() => {
                                    return this.Consulta().Consulta_TablaHonorarios("SUB")
                                })
                        }
                    })
                }
            }
        },

        //=======================================================================================================
        // OTROS
        //=======================================================================================================
        Otros: function() {
            return {
                Valor_TablaHonorarios: (IdArancel, IdPago) => {
                    if (!this.tablahonorarios || this.tablahonorarios.length == 0) return null
                    let arr = this.tablahonorarios.filter(f => f.IdArancel == IdArancel && f.IdTipoPago.trim() == IdPago)
                    if (arr.length > 0) {
                        let resp = arr[0]
                        return resp.Valor + ((resp.Es_Porcentaje == true) ? "%" : "")
                    } else {
                        return null
                    }
                },

                Cancelar: () => {
                    this.editar = null
                },

                Reporte: () => {
                    this.$reporte_modal(
                        {
                            Nombre:"Tabla de Pagos",
                            Opciones: {
                                periodo: this.periodo
                            }
                        })
                }
            }
        }
    },
    async mounted() {
        this.permisos.admin_arancel = this.$validar_privilegio('ADMIN_ARANCEL').status
        this.permisos.admin_tipopago = this.$validar_privilegio('ADMIN_TIPOPAGO').status
        this.permisos.editar = this.$validar_privilegio('EDITAR_TABLA').status

        this.periodo = (await this.$ahora()).getFullYear()
        this.periodo_actual = this.periodo
        for (let index = this.periodo_actual - 4; index <= this.periodo_actual; index++) {
            this.periodo_listado.unshift(index)
        }
        this.Consulta().Consulta_Aranceles()
            .then(() => {
                return this.Consulta().Consulta_Pagos()
            })
            .then(() => {
                return this.Consulta().Consulta_TablaHonorarios()
            })
            .then(() => {
                return this.Consulta().Consulta_SubEspecialidad()
            })
            .then(() => {
                return this.Consulta().Consulta_TablaHonorarios("SUB")
            })
    }
}
</script>

<style scoped>
.editar {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 99999;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
}

.editar>div {
    background-color: white;
    padding: 10px;
    border-radius: 5px;
    width: 400px
}

.card {
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    border-radius: 5px;
    padding: 10px;
}
</style>
