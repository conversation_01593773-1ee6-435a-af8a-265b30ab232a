<template>
    <div>
        <vx-card title="Renumeración de Recibo">
            <vs-divider border-style="solid" color="dark"></vs-divider>
            <div class="box-custom">
                <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                    <vs-row vs-justify="center">
                        <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="8">
                            <div class="flex flex-wrap-nowrap">
                                <div class="div-input">
                                    <ValidationProvider name="serie" rules="required|max:3" class="required" v-slot="{ errors }">
                                        <label class="typo_label label-static">Serie</label>
                                        <vs-input class="w-full input-serie" v-model="serie" autofocus :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"></vs-input>
                                    </ValidationProvider>
                                </div>
                                <div class="div-input">
                                    <ValidationProvider name="numero" rules="required|numero_entero" class="required" v-slot="{ errors }">
                                        <label class="typo_label label-static">Número</label>
                                        <vs-input type="number" class="w-full input-numero" v-model="numero" v-on:blur="NumeroRecibo()" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"></vs-input>
                                    </ValidationProvider>
                                </div>
                            </div>
                        </vs-col>
                    </vs-row>
                    <br>
                    <vs-row vs-justify="center">
                        <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="8">
                            <div class="div-input flex-wrap-nowrap">
                                <label class="label-static">A Nombre de:</label>
                                <input class="w-full label-info" v-model="nombreDe" disabled/>
                            </div>
                        </vs-col>
                    </vs-row>
                    <br>
                    <vs-row vs-justify="center">
                        <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="4">
                            <div class="div-input flex-wrap-nowrap">
                                <label class="label-static">Del: </label>
                                <input class="w-full label-info" v-model="del" disabled/>
                            </div>
                        </vs-col>
                        <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="4">
                            <div class="div-input flex-wrap-nowrap">
                                <label class="label-static">Por: </label>
                                <input class="w-full label-info" v-model="por" disabled/>
                            </div>
                        </vs-col>
                    </vs-row>
                    <br>
                    <vs-row vs-justify="center">
                        <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="8">
                            <div class="flex flex-wrap-nowrap">
                                <ValidationProvider name="nuevoNumero" rules="required|max:40" class="required" v-slot="{ errors }">
                                    <label class="typo_label label-static">Nuevo Número</label>
                                    <vs-input class="w-full input-nuevoNumero" v-model="nuevoNumero" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="isNuevoNumero"></vs-input>
                                </ValidationProvider>
                            </div>
                        </vs-col>
                    </vs-row>
                    <vs-row vs-justify="center" class="w-full">
                        <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="8">
                            <div class="w-full flex flex-wrap-nowrap">
                                <div class="div-input">
                                    <vs-button @click="handleSubmit(ActualizarNumeroRecibo)" :disabled="invalid" color="success" class="w-full" icon-pack="fas" icon="fa-check-circle">OK</vs-button>
                                </div>
                            </div>        
                        </vs-col>
                    </vs-row>
                </ValidationObserver>
            </div>
        </vx-card>
    </div>
</template>
<script>

    export default {
        data() {
            return{
                serie: '',
                numero: '',
                nombreDe: '',
                del: '',
                por: '',
                nuevoNumero: '',
                isNuevoNumero: true
            }
        },
        methods: {
            isEmptyObject(obj){
                return Object.keys(obj).length === 0;
            },
            NumeroRecibo(){
                this.axios.post("/app/v1_JefeCaja/ObtenerRecibo", {
                    "Opcion": "C",
                    "SubOpcion": "2",
                    "Hospital": "0",
                    "Empresa": this.$store.state.sesion.sesion_empresa,
                    "Serie": this.serie,
                    "Numero": this.numero
                })
                .then(resp => {
                    if(!this.isEmptyObject(resp.data.json)){
                        const { Nombre, Fecha, Total } = resp.data.json[0]
                        this.nombreDe = Nombre
                        this.del = Fecha
                        this.por = `Q. ${Total}`
                        this.isNuevoNumero = false
                    }else{
                        this.$vs.dialog({
                            color: 'danger',
                            title: 'Renumeración de Recibo',
                            text: 'Esta Recibo no está registrado...',
                            acceptText: 'Aceptar'
                        })
                        this.nombreDe = ''
                        this.del = ''
                        this.por = ''
                        this.nuevoNumero = ''
                        this.isNuevoNumero = true
                    }
                })
            },
            ActualizarNumeroRecibo() {
                this.axios.post("/app/v1_JefeCaja/ObtenerRecibo", {
                    "Opcion": "C",
                    "SubOpcion": "2",
                    "Hospital": "0",
                    "Empresa": this.$store.state.sesion.sesion_empresa,
                    "Serie": this.serie,
                    "Numero": this.nuevoNumero
                })
                .then(resp => {
                    if(!this.isEmptyObject(resp.data.json)){
                        const { Usuario, Nombre } = resp.data.json[0]
                        this.$vs.dialog({
                            color: 'danger',
                            title: 'Renumeración de Recibo',
                            text: `Este código ya está utilizado por ${Usuario} a nombre de ${Nombre}`,
                            acceptText: 'Aceptar'
                        })
                    }else{
                        this.axios.post("/app/v1_JefeCaja/ActualizarRecibo", {
                            "Opcion": "A",
                            "SubOpcion": "1",
                            "Hospital": "0",
                            "Empresa": this.$store.state.sesion.sesion_empresa,
                            "Serie": this.serie,
                            "Numero": this.numero,
                            "NuevoNumero": this.nuevoNumero
                        })
                        .then(resp => {
                            if(resp.data.json[0].tipo_error == '0'){
                                this.$vs.notify({
                                    color: 'success',
                                    title: 'Renumeración de Recibo',
                                    text: resp.data.json[0].descripcion
                                })
                            }else{
                                this.$vs.notify({
                                    color: 'danger',
                                    title: 'Renumeración de Recibo',
                                    text: resp.data.json[0].descripcion
                                })
                            }
                            
                        })
                    }
                })
            }
        }
    }
</script>
<style lang="scss" scoped>
    .box-custom{
        box-shadow: 0px 15px 40px -10px;
        padding: 15px;
    }
    .div-input{
        padding: 10px;
        .input-serie{
            width: 75px;
        }
        .input-numero{
            width: 200px;
        }
        .input-nuevoNumero{
            width: 200px;
        }
    }
    .label-info{
        margin-left: 10px;
        color:#E74C3C;
        opacity: 0.7;
        border: 0;
        font-size: 14px;
        background-color: white;
    }
    .label-static{
        font-weight: bold;
    }
</style>