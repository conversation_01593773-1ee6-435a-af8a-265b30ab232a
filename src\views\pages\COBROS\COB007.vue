<template>
<div class="mantenimiento-formas-pago-container">
    <vx-card title="Mantenimiento Formas de Pago">
        <DxDataGrid ref="gridFormasPago" v-bind="GridConfiguration" :columns="gridColumns" :editing="gridEditing" :data-source="formasPago" :toolbar="gridToolbar" @init-new-row="onInitNewRow" @row-inserting="onRowInserting" @row-updating="onRowUpdating" @editor-preparing="onEditorPreparing" @editing-start="onEditingStart">
            <template #opcionesTemplate>
                <ExpedienteGridToolBar :visible="true" :showItems="['refresh', 'add']" :pdfExportItems="[]" @refresh="Cargar" @add="gridFormasPago.addRow()" />
            </template>
        </DxDataGrid>
    </vx-card>
</div>
</template>

<script>
import 'devextreme-vue/lookup'
import {
    srtLenRule,
    smallMoneyRule,
    gridConfiguration,
    gridToolbar,
    buildFilterExpresion,
    setUpperCaseConfig,
} from './data.js'
import ExpedienteGridToolBar from '../EXPEDIENTE/ExpedienteGridToolBar.vue'
import bitacora from '@/components/sermesa/funciones/SMBitacora'
export default {
    name: 'MantenimientoFormasPago',
    components: {
        ExpedienteGridToolBar
    },
    data() {
        return {
            formasPago: [],
            catalogoCuentas: null,
            tipoFormaPago: null,
            GridConfiguration: gridConfiguration,
            gridToolbar,
            gridColumns: [{
                    dataField: 'Empresa',
                    dataType: 'string',
                    visible: false,
                },
                {
                    dataField: 'Tipo',
                    dataType: 'string',
                    validationRules: [{
                        type: 'required'
                    }, srtLenRule('Tipo', 1, 1)],
                    width: 55,
                },
                {
                    dataField: 'Codigo',
                    caption: 'Código',
                    dataType: 'string',
                    validationRules: [{
                            type: 'required'
                        },
                        srtLenRule('Codigo', 1, 3)
                    ],
                    width: 65,
                },
                {
                    allowHeaderFiltering: false,
                    dataField: 'Nombre',
                    dataType: 'string',
                    calculateFilterExpression: buildFilterExpresion('Nombre'),
                    validationRules: [{
                            type: 'required'
                        },
                        srtLenRule('Nombre', 1, 60)
                    ],
                    minWidth: 450,
                    width: 'auto',
                },
                {
                    dataField: 'Comision',
                    caption: 'Comisión',
                    dataType: 'number',
                    minWidth: 80,
                    width: 80,
                    validationRules: [smallMoneyRule],
                },
                {
                    dataField: 'Afiliacion',
                    caption: 'Afiliación',
                    dataType: 'string',
                    minWidth: 80,
                    validationRules: [srtLenRule('Afiliacion', 0, 15)],
                },
                {
                    dataField: 'ContaDebe',
                    caption: 'Cuenta por Cobrar',
                    dataType: 'string',
                    minWidth: 80,
                    validationRules: [srtLenRule('ContaDebe', 0, 12)],
                },
                {
                    dataField: 'ContaHaber',
                    caption: 'Cuenta Gastos',
                    dataType: 'string',
                    minWidth: 80,
                    validationRules: [srtLenRule('ContaHaber', 0, 12)],
                },
                {
                    type: 'buttons',
                    caption: 'Acciones',
                    width: 80,
                    fixed: true,
                    buttons: [
                        'edit',
                    ],
                },
            ],

            gridEditing: {
                ...gridConfiguration.editing,
                mode: 'row',
            },
            objBitacora: null,
        }
    },
    methods: {
        Cargar() {
            this.axios.post('/app/v1_cobros/CatalogoBancos', {}).then(resp => {
                this.formasPago = resp.data
            })
        },
        Bitacora(newData, tipobitacora) {
            return new Promise((resolve, reject) => {
                Object.keys(newData).forEach(key => {
                    this.objBitacora[key] = newData[key]
                })
                this.objBitacora.tipobitacora = tipobitacora
                this.axios.post('/app/bitacora/registro_bitacora', {
                    llave: {
                        Empresa: this.objBitacora.Empresa,
                        Tipo: this.objBitacora.Tipo,
                        Codigo: this.objBitacora.Codigo,
                    },
                    tabla: 'BancosTarjetas',
                    info: bitacora.obtener()
                }).then((resp) => {
                    resolve(resp)
                }).catch((err) => reject(err))
            })
        },
        onRowInserting(e) {
            e.cancel = true
            this.InitBitacora({
                Empresa: e.data.Empresa,
                Tipo: e.data.Tipo,
                Codigo: e.data.Codigo,
            }) //se incicaliza aqui para settear la llave

            this.axios.post('/app/v1_cobros/InsertarCuentaBancos', e.data)
                .then(() => {
                    this.Bitacora(e.data, 'Inserción')
                    this.gridFormasPago.cancelEditData()
                    this.Cargar()
                })

        },
        onRowUpdating(e) {
            e.cancel = true
            this.axios.post('/app/v1_cobros/ActualizarCuentaBancos', {
                ...e.oldData,
                ...e.newData,
            }).then(() => {
                this.Bitacora(e.newData, 'Modificación')
                this.gridFormasPago.cancelEditData()
                this.Cargar()
            })
        },

        onEditorPreparing(e) {
            if (['Codigo', 'Tipo'].includes(e.dataField)) {
                setUpperCaseConfig(e.editorOptions)
            }
        },
        onInitNewRow(e) {
            e.data.Empresa = this.$store.state.sesion.sesion_empresa
        },
        onEditingStart(e) {
            this.InitBitacora(e.data)
        },
        InitBitacora(data) {
            this.objBitacora = data
            this.objBitacora.tipobitacora = 'Tipo'
            bitacora.registrar(this.objBitacora, {
                Empresa: data.Empresa,
                Tipo: data.Tipo,
                Codigo: data.Codigo,
            })
        },
    },
    computed: {
        gridFormasPago() {
            return this.$refs['gridFormasPago'].instance
        }
    },
    beforeMount() {
        this.Cargar()
    }
}
</script>

<style>
.mantenimiento-formas-pago-container .dx-datagrid-headers td,
.mantenimiento-formas-pago-container .dx-datagrid-headers td {
    background-color: aliceblue;
    color: black;
    font-weight: bold;
}

.mantenimiento-formas-pago-container .uppercase {
    text-transform: uppercase;
}

.mantenimiento-formas-pago-container .dx-datagrid {
    min-height: 400px;
    height: calc(100vh - 215px);
}
</style>
