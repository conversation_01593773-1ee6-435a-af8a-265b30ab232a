
import Vue from 'vue'
import axios from 'axios'


//axios
import VueAxios from 'vue-axios'
Vue.use(VueAxios, axios)

export default {  

  async crearPaciente({ commit }, paciente) {   
         
      const resp = await axios.post('/app/v1_admision/CrearPaciente', JSON.stringify(paciente))
        commit("CREATE_PACIENTE", paciente)
        commit("SET_CODIGO_PACIENTE", resp.data.json[0].IdPaciente)
        commit("SET_CODIGO_CLIENTE", resp.data.json[0].IdCliente)
  },
  asigarPaciente({commit}, paciente){
    commit('SET_CODIGO_PACIENTE', paciente)
  },
  asigarCliente({commit}, paciente){
    commit('SET_CODIGO_CLIENTE', paciente)
  }
}
