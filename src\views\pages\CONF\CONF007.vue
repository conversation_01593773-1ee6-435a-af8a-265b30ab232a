<template>
    <div>
        <vs-popup title="Registro de Huella" :active.sync="isRegistro" style="z-index:9999" id="div-with-loading" class="vs-con-loading__container">
            <div>
                <RegistroHuella ref="componenteRegistro" :tipoHuella="huella" :corporativoBusqueda="info.Corporativo" @getVentana="getVentana"/>
            </div>
        </vs-popup>
        <InfoPopup :visible.sync="isValidacion" :width="'60%'" :height="'60%'" 
                    :show-title="true" :full-screen="false" :hide-on-outside-click="false"        
                    title="Validación de Huella" :showCloseButton="true">
            <ValidarHuella ref="componenteValidacion" :tipoHuella="huella" :corporativoBuscar="info.Corporativo" @getValidacionHuella="getValidacionHuella"/>
        </InfoPopup>   
        <vx-card title="Registro Huella">
            <div class="w-full md:w-1/2 lg:w-1/2 sm:w-1/2 p-1">
                <SM-Buscar 
                    v-model="info.Corporativo" label="Código Corporativo" api="app/Ajenos/Busqueda_Corporativo" 
                    :api_campos="['Corporativo',['Nombres', 'Nombre'],['Apellidos', 'Apellido'],]" 
                    :api_titulos="['Corporativo', 'Nombres', 'Apellidos']" 
                    api_campo_respuesta="Corporativo" 
                    :disabled_search_input="nuevo" 
                    :callback_buscar="Consulta().ConsultaCorporativo" 
                    :callback_cancelar="Otros().Limpiar_Datos" 
                    :disabled_texto="true"  />
            </div>
            <div v-if="info.Corporativo">
                <div class="flex flex-wrap">
                    <div class="w-full sm:w-1/4 p-1">
                        <vs-input label="Nombres" class="w-full" v-model="info.Nombres" :disabled="!nuevo" />
                    </div>
                    <div class="w-full sm:w-1/4 p-1">
                        <vs-input label="Apellidos" class="w-full" v-model="info.Apellidos" :disabled="!nuevo" />
                    </div>
                    <div style="width: 200px;" class="pl-2 pt-6 text-right">
                        <vs-button color="primary" @click="Grabar" icon-pack="fas" icon="fa-fingerprint">Grabar Huella</vs-button>
                    </div>
                    <div style="width: 200px;" class="pt-6 text-right">
                        <vs-button color="success" @click="Validar" icon-pack="fas" icon="fa-fingerprint">Validar Huella</vs-button>
                    </div>
                </div>
                <br>
                <br>
                <div class="flex flex-wrap">
                    <ListadoHuellas ref="listadoHuellaComponente" :tipoHuella="huella" :corporativoBusqueda="info.Corporativo" />
                </div>
            </div>
        </vx-card>           
    </div>
</template>
<script>
    import { defineAsyncComponent } from 'vue'
    import ValidarHuella from '/src/components/validador-huella/ValidarHuella.vue';
    import { DxPopup as InfoPopup } from 'devextreme-vue/popup'
    export default {
        components: {
            ListadoHuellas: defineAsyncComponent( () => import('@/components/validador-huella/ListadoHuellas.vue') ),
            RegistroHuella: defineAsyncComponent( () => import('@/components/validador-huella/RegistroHuella.vue') ),
            ValidarHuella,
            InfoPopup
        },
        data(){
            return{
                IpLocal: '',
                isValidacion: false,
                info: {
                    Corporativo: null,
                    Status: 'A',
                    Nombres: null,
                    Apellidos: null,
                    Correo: null,
                    IdPuesto: null,
                    DPI: null,
                    NIT: null,
                    Ajeno: null,
                    IdSlack: null,
                    Indicio: null
                },
                nuevo: false,
                huella: "1",
                isRegistro: false,
                corporativo: ''
            }
        },
        methods: {
            Consulta: function() {
                return {
                    /**
                     * Consula de Corporativo
                     * Permite obtener los nombres y apellidos del corporativo
                     */
                    ConsultaCorporativo: (data) => {
                        this.axios.post('/app/administracion/CorporativoConsulta', {
                                IdCorporativo: data.Corporativo
                            })
                            .then(resp => {
                                if (resp.data.json) {
                                    this.$map({
                                        objeto: this.info,
                                        respuestaAxios: resp,
                                        omitir: ['Corporativo']
                                    })
                                }
                            })
                    },
                };
            },
            Grabar(){
                if(!this.IpLocal || this.IpLocal.trim().length <= 0){
                   this.MensajeErrorIp()
                   return
                }
                this.isRegistro = true
            },
            getVentana(value){
                this.isRegistro = value
                //Grabo con exito
                if(!value){
                    this.$refs.listadoHuellaComponente.consulta()
                }
            },
            Otros: function() {
                return{
                    Limpiar_Datos: () => {
                        this.nuevo = false;
                        Object.entries(this.info).forEach(([key]) => {
                            this.info[key] = null;
                        });
                        this.info.Status = 'A'
                        setTimeout(() => {
                            this.$refs.formValidate.reset();
                        }, 100);
                    }
                }
            },
            getValidacionHuella(data){
                if(data.StatusPlantilla == 'VERIFICADO'){
                    this.isValidacion = false
                    if(data.Corporativo != this.info.Corporativo){
                        this.$vs.dialog({
                            color: 'danger',
                            title: 'Invalido',
                            text: `Huella registrada con el corporativo ${data.Corporativo}`,
                            acceptText: 'Aceptar'
                        })
                    }else{
                        this.$vs.dialog({
                            color: 'success',
                            title: 'Validado',
                            text: `Gracias por confirmar ${this.info.Nombres} ${this.info.Apellidos}`,
                            acceptText: 'Aceptar'
                        })
                    }
                    
                }else{
                    this.isValidacion = true
                }
            },
            Validar(){
                if(!this.IpLocal || this.IpLocal.trim().length <= 0){
                   this.MensajeErrorIp()
                   return
                }
                this.isValidacion = true
                this.$refs.componenteValidacion.activarValidador(this.info.Corporativo)
            },
            MensajeErrorIp(){
                this.$vs.notify({
                        title: 'Configuracion Maquina',
                        text: 'Verificar que este usando el navegador configurado o contactar a soporte tecnico',
                        time: 5000,
                        iconPack: 'feather',
                        icon: 'icon-alert-circle',
                        color: 'warning',
                        position: 'top-center'
                    })
            }
        },
        watch: {
            isValidacion(value){
                if(!value){
                    this.$refs.componenteValidacion.desactivarValidador()
                } 
            },
            isRegistro(value) {
                if(!value){
                    this.$refs.componenteRegistro.DesactivarSensor()
                }
            }
        },
        mounted(){
            this.IpLocal = localStorage.getItem('IpLocal');
        }
    }
</script>