<template>
<vs-row class="p-2 w-full historial-container">
    <vs-col vs-w="3" vs-justify="center" vs-align="center" class="pr-2">
        <vx-card :title="textoFiltro">
            <!-- Se dejó toda la configuración, ya que si se usa la configuración global, se reinicia el pageIndex cada vez que se selecciona una fecha -->
            <DxDataGrid :ref="dataGridFechaKey" v-bind="DefaultDxGridConfiguration" :headerFilter="{visible:true, allowSearch:true}" :data-source="fechas" :width="'100%'" :height="'calc(95vh-60px)'" @selection-changed="onSelectionChanged" @cell-prepared="onCellPrepared">
                <DxDataGridToolbar>
                    <DxDataGridItem name="searchPanel" />
                    <DxDataGridItem location="after" template="opcionesTemplate" />
                </DxDataGridToolbar>
                <DxDataGridSelection mode="single" />
                <DxDataGridColumn :width="120" data-field="Fecha" caption="Fecha" format="dd/MM/yyyy HH:mm" data-type="date" alignment="center" />
                <DxDataGridColumn :width="100" data-field="Examen" :allowHeaderFiltering="false" caption="Exámen" data-type="string" alignment="center" />

                <template #opcionesTemplate>
                    <GridToolBar v-bind="$props" :visible="ShouldRender" :pdfExportItems="[{text:'Laboratorio', reportName: 'ResultadosLaboratorio'}]" @refresh="refresh" :showItems="['refresh']" />
                </template>
            </DxDataGrid>
        </vx-card>
    </vs-col>
    <!-- <vs-col vs-w="3" class="pr-2">

    </vs-col> -->
    <vs-col vs-justify="center" vs-align="center" vs-w="9">
        <!-- <div class="pb-0 pr-2 pl-3"> -->
        <vx-card>
            <div width="100%" style="height: 600px; display:grid; place-items: center;">
                <object v-if="archivoPDF!=''" type="application/pdf" :data="archivoPDF" ref="pdfDocument" width="100%" height="98%">
                    <p>Su navegador es incompatible con el plugin pdf.</p>
                </object>
                <p v-else style="white-space:pre-wrap;">
                    {{noDataText}}
                </p>
            </div>
            <vs-row class="pt-2" v-if="laboratorioTexto !== ''">
                <vs-textarea v-model="laboratorioTexto" :disabled="true" style="white-space: pre-wrap;" />
            </vs-row>
        </vx-card>
        <!-- </div> -->
    </vs-col>
</vs-row>
</template>

<script>
import CustomStore from 'devextreme/data/custom_store'
import GridToolBar from './GridToolBar.vue'
import {
    DefaultDxGridConfiguration
} from './data'

const dataGridRefKey = 'gridLaboratorio'
const dataGridFechaKey = 'gridFiltro'

export default {
    name: 'Laboratorio',
    components: {
        GridToolBar,
    },
    data() {
        return {
            //Listado de signos asociados a la admisión
            laboratorios: [],
            laboratorioDataSource: new CustomStore({
                key: "id",
                load: () => {
                    return this.laboratorios;
                },
            }),
            fechas: [], //Listado de examenes para filtrar laboratorios
            botones: ['refresh'], //Vector para manejar los botones que se muestran sobre la tabla
            reporteProps: [],
            archivoPDF: '',
            textoFiltro: 'Filtrar laboratorio',
            laboratorioTexto: '',
            noDataText: 'Seleccione una fecha para cargar información',
            dataGridRefKey,
            dataGridFechaKey,
            DefaultDxGridConfiguration,
        }
    },
    props: {
        IdCita: null,
        IdCliente: null,
        IdPaciente: null,
        IdConsulta: null,
    },
    methods: {
        Cargar(tipo = '', orden = '', linea = '') {
            if (this.IdCliente !== null && this.IdCliente !== '') {
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaResultadosLaboratorios', {
                        TipoOrden: tipo,
                        NoOrden: orden,
                        LineaOrden: linea
                    })
                    .then(resp => {
                        if (linea === '-1') {
                            this.laboratorios = [];
                            this.laboratorioTexto = ''
                            this.botones = ['refresh'],
                                this.noDataText = '****** LISTADO DE ESTUDIOS PENDIENTES DE REALIZAR ******'
                            resp.data.json.map((x) => {
                                this.noDataText += '\n' + x.EstudiosPendientes
                            })
                        } else {
                            this.laboratorios = resp.data.json.map((x, index) => {
                                return {
                                    id: index,
                                    Prueba: x.Prueba,
                                    Resultado: x.R,
                                    FechaValidacion: x.FRealizacion,
                                    VEquipo: x.VEquipo,
                                    ValorNormal: x.VALORNORMAL,
                                    ValorRn: x.VALORNORMALRN,
                                    ValorMenor: x.VALORNORMALMENOR,
                                    ValorMasculino: x.VALORNORMALMASC,
                                    ValorFemenino: x.VALORNORMALFEM,
                                    LaboratorioTexto: x.LaboratorioTexto,
                                    EstudiosPendientes: x.EstudiosPendientes
                                }
                            })
                            const unique = Array.from(new Set(this.laboratorios.map((item) => item.Resultado.trim()))) //Array que almacena las líneas de los resultados

                            if (unique.length === 1) {
                                if (unique[0] === '') {
                                    this.archivoPDF = ''
                                    this.noDataText = 'El exámen seleccionado no cuenta con resultados para mostrar en PDF'
                                    this.botones = ['refresh']
                                } else {
                                    this.getPDF();
                                    this.botones = ['refresh', 'exportPdf'];
                                }
                            } else if (unique.length === 0) {
                                this.archivoPDF = ''
                                this.noDataText = 'El exámen seleccionado no cuenta con resultados para mostrar en PDF'
                                this.botones = ['refresh']
                            } else {
                                this.getPDF();
                                this.botones = ['refresh', 'exportPdf'];
                            }
                            if (this.laboratorios.length > 0) {
                                this.laboratorioTexto = this.$limpiar_saltos_tabulares(this.laboratorios[0].LaboratorioTexto)
                            }
                        }
                    })
            } else {
                this.laboratorios = [];
                this.refreshFechaDataGrid();
            }
        },
        getFechasLaboratorio() {
            if (this.IdPaciente !== undefined && this.IdPaciente !== null && this.IdPaciente !== '')
                this.axios.post('/app/v1_Historial_Clinico/BusquedaLaboratorios', {
                    IdPaciente: this.IdPaciente
                })
                .then(resp => {
                    this.fechas = resp.data.json.map((x, index) => {
                        return {
                            id: index,
                            Fecha: x.Fecha,
                            Tipo: x.Tipo,
                            Orden: x.Codigo,
                            LineaResultado: x.CodExamen,
                            LineaExamen: x.LineaOrden,
                            Examen: x.Examen,
                            EstadoExamen: '',
                            MostrarResultados: 'T',
                            TipoConsulta: 'C'
                        }
                    })
                    this.dataGridFecha.columnOption('Fecha', 'sortOrder', 'desc')
                })
            else {
                this.fechas = [];
                this.refreshFechaDataGrid();
            }
        },
        getLineaPDF(tipo, orden, nombre) {
            this.axios.post('/app/laboratorio/BusquedaPorExamen', {
                    TipoOrden: tipo,
                    Orden: orden,
                    LineaResultado: 0,
                    TipoConsulta: 'C'
                })
                .then(resp => {
                    let v = resp.data.json.map((x) => {
                        return {
                            Cod: x.CodExamen,
                            Nombre: x.NombreExamen
                        }
                    }).filter(x => x.Nombre === nombre)

                    try {
                        this.reporteProps.LineaResultado = v[0].Cod;
                    } catch {
                        this.reporteProps.LineaResultado = 0;
                        this.archivoPDF = ''
                    }
                    this.Cargar(this.reporteProps.Tipo, this.reporteProps.Orden, this.reporteProps.LineaExamen);
                })
        },
        refreshFechaDataGrid() {
            this.dataGridFecha.pageIndex(0);
            this.dataGridFecha.clearSelection();
            this.dataGridFecha.clearSorting();
            this.dataGridFecha.columnOption('Fecha', 'sortOrder', 'desc')
            this.botones = ['refresh'];
        },
        formatFecha(date) {
            const year = new Date(date).toLocaleString('default', {
                year: 'numeric'
            });
            const month = new Date(date).toLocaleString('default', {
                month: '2-digit'
            });
            const day = new Date(date).toLocaleString('default', {
                day: '2-digit'
            });

            return [year, month, day].join('');
        },
        formatFechaFiltro(fecha) {
            fecha = fecha.split('/');
            const year = fecha[2];
            const month = fecha[1];
            const day = fecha[0];

            return [month, day, year].join('/');
        },
        //Método para detectar cuando se selecciona una fila de la tabla de filtrado de fecha
        onSelectionChanged(e) {
            if (e.selectedRowsData.length > 0) {
                this.laboratorioTexto = ''
                this.noDataText = ''
                this.archivoPDF = ''
                this.reporteProps = e.selectedRowsData[0]
                this.textoFiltro = 'Orden ' + this.reporteProps.Tipo + ' ' + this.reporteProps.Orden
                this.getLineaPDF(this.reporteProps.Tipo, this.reporteProps.Orden, this.reporteProps.Examen);
            }
        },
        refresh() {
            this.laboratorios = [];
            this.textoFiltro = 'Filtrar laboratorio'
            this.laboratorioTexto = ''
            this.archivoPDF = ''
            this.noDataText = 'Seleccione una fecha para cargar información'
            this.refreshFechaDataGrid();
        },
        onCellPrepared(e) {
            if (e.rowType === 'data' && e.column.dataField === 'Examen') {
                if (e.data.LineaExamen === '-1') {
                    e.cellElement.style.cssText = "color: red; text-align: center;"
                }
            }
        },
        getPDF() {
            this.axios.post("/app/reporte/ReporteGenerador", {
                    Nombre: 'ResultadosLaboratorio',
                    Opciones: {
                        tiporeporte: "application/pdf",
                        ...this.reporteProps
                    }
                }, {
                    responseType: 'arraybuffer'
                })
                .then(resp => {
                    this.archivoPDF = 'data:application/pdf;base64,' + Buffer.from(resp.data, 'binary').toString('base64') + '#view=FitH';
                })
        },
    },
    mounted() {
        this.getFechasLaboratorio();
    },
    watch: {
        'IdPaciente'() {
            this.refresh()
            this.getFechasLaboratorio();
        },
    },
    computed: {
        ShouldRender() {
            return Boolean(this.IdPaciente)
        },

        dataGridFecha: function () {
            return this.$refs[dataGridFechaKey].instance;
        }
    },
}
</script>

<style>
.dx-datagrid-nodata {
    white-space: pre-wrap;
}

.vs-textarea {
    height: 100px;
}
</style>
