<template>
<vx-card title="Periodos">
    <div class="content content-pagex">

        <vs-divider>Mantenimiento Periodos - Festivos</vs-divider>

        <div class="ais-SearchBox">
            <div>
                <div class="relative mb-8">
                    <div class="vs-component vs-con-input-label vs-input w-full vs-input-shadow-drop vs-input-no-border d-theme-input-dark-bg vs-input-primary">
                        <div class="vs-con-input">
                            <input type="text" class="vs-inputx vs-input--input large" placeholder="Buscar" style="border: 1px solid rgba(0, 0, 0, 0.2);" v-model="buscar">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="append-text btn-addon">
                 <!-- <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="cargar_Periodo()" icon="icon-search" ></vs-button> -->
                <vs-button  color="success" icon-pack="feather" icon="icon-plus" style="float:left" type="filled" @click="nuevo()">Nuevo</vs-button>
        </div>

        <vs-table :data="periodos" >
            <template slot="thead">
                <vs-th>Empresa</vs-th>
                <vs-th>Año</vs-th>
                <vs-th>Descripcion</vs-th>
                <vs-th>Fecha</vs-th>
                <vs-th>Estado</vs-th>
                <vs-th>Opciones</vs-th>
            </template>
            <template >

                <vs-tr :key="indextr" v-for="(tr, indextr) in periodo_filtro">
                    <vs-td :data="tr.Empresa">
                        {{ tr.Empresa }} 
                    </vs-td>
                    <vs-td :data="tr.Anio">
                        [{{ tr.Anio }}]
                    </vs-td>
                      <vs-td :data="tr.Descripcion">
                        {{ tr.Descripcion }}
                    </vs-td>
                    <vs-td :data="tr.Fecha">
                        {{ tr.Fecha }}
                    </vs-td>
                    <vs-td :data="tr.Estado">
                        <vx-tooltip text="Estado" position="bottom">
                         <vs-switch color="success" :disabled=true v-model="tr.Estado" icon-pack="feather" vs-icon="icon-lock"  />
                        </vx-tooltip>
                    </vs-td>
                    <vs-td>          
                        <vs-button color="primary" icon-pack="feather" icon="icon-edit" style="display:inline-block;margin-right:2px" @click="opciones(tr)"></vs-button>
                        <vs-button color="danger" icon-pack="feather" icon="icon-x-square" style="display:inline-block" @click="eliminar(tr)"></vs-button>
                    </vs-td>
                </vs-tr>
            </template>
        </vs-table>
    </div>


<!-- ========================================================================================================================================================== -->
<!-- POPUP INICIO -->
<!-- ========================================================================================================================================================== -->
    <form>    
     
        <vs-popup classContent="popup-example" :title="'['+Fecha+'] - ' + Anio" :active.sync="show">
         <form color="rgba(var(--vs-primary), 1)" errorColor="rgba(var(--vs-danger), 1)" >   
            <ValidationObserver v-slot="{invalid,handleSubmit}">
            <div style="padding:10px;border-radius:5px; border:1px solid #ccc;">
                
                <div class="flex flex-wrap">
                    <div class="w-full">
                        <ValidationProvider name="Anio_v" rules="required" >
                        <vs-input label="Año" class="w-full"  type="number" v-model="Anio" />
                        <span style="position:relative;top:-2px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                        </ValidationProvider>
                    </div>
                </div>
                
                <div class="flex flex-wrap">
                    <div class="w-full">
                        <ValidationProvider name="Descripcion_v" rules="required" >
                        <vs-input label="Descripción" class="w-full"  v-model="Descripcion"/>
                        <span style="position:relative;top:-2px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                        </ValidationProvider>
                    </div>
                </div>
                <vs-divider></vs-divider>
                <div class="flex flex-wrap">
                    <div class="w-full">
                        <ValidationProvider name="Fecha_v" rules="required" >
                        <flat-pickr   :config="configdateTimePicker" class="form-control datepicker" style="width:100%"  placeholder="Fecha" v-model="Fecha" />
                        <span style="position:relative;top:-2px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                         </ValidationProvider>
                    </div>
                </div>

                <div class="flex flex-wrap">
                    <vx-tooltip text="Estado" position="bottom">
                    <label>Estado</label>
                    <vs-switch color="success" v-model="Estado" icon-pack="feather" vs-icon="icon-lock"  />
                    </vx-tooltip>
                </div>
               
                <vs-divider></vs-divider>
                <vs-button style="float:right" @click="handleSubmit(post_Periodo(invalid))"> Guardar</vs-button>
                <vs-button color="primary" style="float:right" type="border" @click="show=false"> Salir</vs-button>
                <div style="clear:both"></div>

            </div>
            </ValidationObserver>
         </form>
        </vs-popup>

        <vs-popup classContent="popup-example" :title="'Periodo'" :active.sync="showdel" >
         <div style="padding:10px;border-radius:5px; border:1px solid #ccc; font: 22px Arial;">
            <div class="vx-col w-full">
            <h2 class="mb-2">¿Estimado Usuario Esta Seguro de Eliminar el Periodo  Festivo - {{Descripcion}}?</h2>
            </div>
                <!--<vs-divider></vs-divider> -->
                <vs-button color="success" style="float:right" type="filled" icon-pack="feather" icon="icon-save" @click="delete_Periodo"> Aceptar</vs-button>
                <vs-button color="primary" style="float:right" type="border" @click="showdel=false"> Cancelar</vs-button>
                <div style="clear:both"></div>
        </div>
        </vs-popup>

    </form>
<!-- ========================================================================================================================================================== -->
<!-- POPUP FIN -->
<!-- ========================================================================================================================================================== -->
 

</vx-card>
</template>

<script>
// require styles
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
import flatPickr from 'vue-flatpickr-component';
import 'flatpickr/dist/flatpickr.css';

import {
    ValidationObserver,
    ValidationProvider,

} from "vee-validate";



export default {
    data() {
        return {
             configdateTimePicker: {
                allowInput: true,
                dateFormat: 'd/m/Y'
            },
            periodos: [],
            buscar:'',
            show: false, //mostrar las opciones de la Aseguradora
            showdel:false,//Opcion Eliminar Aseguradora
            IdPeriodo: 0, 
            Empresa: '',
            Anio: 0,
            Descripcion:'',
            Fecha: '',
            Estado: false,
        }
    },
    computed: {
        periodo_filtro(){
        if (this.buscar.trim()==''){
          return this.periodos
        }else{
          return this.periodos.filter(data=>data.Anio==this.buscar || data.Descripcion.toUpperCase().indexOf(this.buscar.toUpperCase())>=0)
        }
      }
    },
    components: {
        flatPickr,
        ValidationProvider,
        ValidationObserver,
    },
    methods: {
        cargar_Periodo() {
            this.axios.post('/app/admision/GETPeriodoFestivo', {
                    Operacion: 'C'
                })
                .then(resp => {
                    this.periodos = []
                    resp.data.json.map(data => {
                        this.periodos.push({
                            IdPeriodo: data.IdPeriodo,
                            Empresa: data.Empresa,
                            Anio: data.Anio,
                            Descripcion: data.Descripcion,
                            Fecha: data.Fecha,
                            Estado:  (data.Estado==1?true:false),
                            Fecha_Registro: data.Fecha_Registro
                        })
                    })
     
                })
        },
        post_Periodo(validar) {

                if (validar) {  
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Producto',
                            text: 'Estimado Usuario, Tiene campos obligatorios por Completar.',
                        })

                }else{     

                        this.axios.post('/app/admision/POSTPeriodoFestivo', {
                        Operacion: (this.IdPeriodo==0) ? 'I': 'A',
                        Id: this.IdPeriodo,
                        Anio: this.Anio,
                        Descripcion: this.Descripcion.trim(),
                        Fecha: this.Fecha.trim(),
                        Estado: (this.Estado== true)?1:0,
                        bitacora: [{
                            llave: this.Anio + "-" + this.Fecha + "-" + this.Descripcion,
                            tipo: "Periodo",
                            registros: [
                                'Mantenimiento Periodo'
                            ]
                        }]
                        })
                        .then(resp => {

                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                if(resp.data.json[0].codigo==-3001){
                                    this.$vs.notify({
                                        color: 'danger',
                                        title: 'Periodo',
                                        text: resp.data.json[0].error,
                                    })
                                    this.show = false;
                                }else{
                                    this.cargar_Periodo()
                                    this.$vs.notify({
                                        color: 'success',
                                        title: 'Periodo',
                                        text:  (this.IdPeriodo==0) ? 'Información guardada exitosamente': 'Información actualizada exitosamente',
                                    })
                                    this.show = false;
                                }

                            }
                        })
                        .catch(() => {
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Periodo',
                                text:  (this.IdPeriodo==0) ?'Error al guardar la información' :'Error al actualizar la información',
                            })

                        }) 
                
                }


        },
        delete_Periodo() {
            this.axios.post('/app/admision/POSTPeriodoFestivo', {
                    Operacion: 'E',
                    Id: this.IdPeriodo,
                    Anio: this.Anio,
                    Descripcion: this.Descripcion.trim(),
                    Fecha: this.Fecha.trim(),
                    Estado: (this.Estado== true)?1:0,
                    bitacora: [{
                        llave: this.Anio + "-" + this.Fecha + "-" + this.Descripcion,
                        tipo: "Periodo",
                        registros: [
                            'Mantenimiento Periodo-Eliminar'
                        ]
                    }]
                })
                .then(() => {
                    this.cargar_Periodo()
                    this.$vs.notify({
                        color: 'success',
                        title: 'Periodo',
                        text: 'Información eliminada exitosamente',
                    })
                    this.showdel = false;
                })
                .catch(() => {
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Periodo',
                        text: 'Error al eliminar la información',
                    })
                })
        },
        opciones(op) {
            this.show = true
            this.showdel = false
            this.IdPeriodo = op.IdPeriodo
            this.Anio = op.Anio
            this.Descripcion = op.Descripcion.trim()
            this.Fecha = op.Fecha
            this.Estado = op.Estado
        },
        nuevo() {
            this.show = true
            this.showdel = false
            this.IdPeriodo = 0
            this.Anio = 0
            this.Descripcion = ''
            this.Fecha = ''
            this.Estado =false
        },
        eliminar(op) {
            this.show = false
            this.showdel = true
            this.IdPeriodo = op.IdPeriodo
            this.Anio = op.Anio
            this.Descripcion = op.Descripcion.trim()
            this.Fecha = op.Fecha
            this.Estado = op.Estado
        },
    },
    created() {
      this.cargar_Periodo()
    }
}
</script>

<style>
.button {
    margin: 0px !important;
}

.btnx {
    margin-left: 10px !important;
    background: red;
    border-radius: 5px 0px 0px 5px;
}

.btn-drop {
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: red;

}

.menu-funcionalidad {
    /* padding-top:0 !important; */
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    padding: 15px;
    display: inline;
    float: right;
    border-radius: 10px;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 10px;
}

.menu-funcionalidad td {
    text-align: center;
    padding-right: 10px;
}

.vs-switch {
    height: 30px;
    width: 60px !important;
}

.vs-circle-switch {
    height: 25px;
    width: 25px;
}

.vs-switch--icon {
    font-size: 15px
}

.vs-switch.vs-switch-active .vs-switch--circle {
    margin-left: calc(100% - 30px);
}
</style>
