<template>
<div class="p-2 historial-container">
    <vs-row>
        <!-- Semáforo -->
        <vs-col vs-justify="center" vs-align="center" class="pr-2">
            <!-- <vx-card> -->
            <div id="traffic-light">
                <div class="light red" :class="{active: current=='red'}"></div>
                <div class="light yellow" :class="{active: current=='yellow'}"></div>
                <div class="light green" :class="{active: current=='green'}"></div>
            </div>
            <!-- </vx-card> -->
        </vs-col>
    </vs-row>
    <vs-row class="pt-2">
        <!-- Sección de información general -->
        <vs-col vs-w="6" vs-justify="center" vs-align="center" class="pr-2">
            <vx-card>
                <DxForm :ref="formularioDatos" :form-data.sync="infoSeguro" labelMode="floating" :readOnly="true">
                    <DxFormGroupItem caption="Datos generales">
                        <DxFormItem item-type="group" :col-count-by-screen="colCountByScreenDatos">
                            <DxFormItem data-field="Nombre" :col-span="2">
                                <DxFormLabel text="Paciente" />
                            </DxFormItem>
                            <DxFormItem data-field="Telefono">
                                <DxFormLabel text="Teléfono" />
                            </DxFormItem>
                        </DxFormItem>
                        <DxFormItem item-type="group" :col-count-by-screen="colCountByScreenDatos">
                            <DxFormItem data-field="NombreMedico" :col-span="2">
                                <DxFormLabel text="Médico" />
                            </DxFormItem>
                            <DxFormItem data-field="IdMedico">
                                <DxFormLabel text="Id del médico" />
                            </DxFormItem>
                        </DxFormItem>
                        <DxFormItem item-type="group" :col-count-by-screen="colCountByScreenDatos">
                            <DxFormItem data-field="Hospital" :col-span="2">
                                <DxFormLabel text="Sucursal" />
                            </DxFormItem>
                            <DxFormItem data-field="CodigoFarmaciaExt">
                                <DxFormLabel text="Código" />
                            </DxFormItem>
                        </DxFormItem>
                    </DxFormGroupItem>
                </DxForm>
            </vx-card>
        </vs-col>

        <!-- Beneficio Salud Siempre -->
        <vs-col vs-w="6" vs-justify="center" vs-align="center" class="pr-2">
            <vx-card>
                <DxForm :ref="formularioBeneficios" :form-data.sync="infoSeguro" labelMode="floating" :readOnly="true">
                    <DxFormGroupItem caption="Beneficios Salud Siempre">
                        <DxFormItem item-type="group" :col-count-by-screen="colCountByScreen">
                            <DxFormItem data-field="NombrePlan">
                                <DxFormLabel text="Plan médico" />
                            </DxFormItem>
                            <DxFormItem data-field="Descripcion">
                                <DxFormLabel text="Beneficio" />
                            </DxFormItem>
                        </DxFormItem>
                        <DxFormItem item-type="group" :col-count="3">
                            <DxFormItem data-field="IdPlan">
                                <DxFormLabel text="Póliza" />
                            </DxFormItem>
                            <DxFormItem data-field="EventosDisponibles">
                                <DxFormLabel text="Eventos disponibles" />
                            </DxFormItem>
                            <DxFormItem data-field="NoPagos">
                                <DxFormLabel text="Pagos" />
                            </DxFormItem>
                        </DxFormItem>
                        <DxFormItem item-type="group" :col-count-by-screen="colCountByScreenDatos">
                            <DxFormItem data-field="Total" editor-type="dxNumberBox" :editor-option="{format: '0.00'}" />
                            <DxFormItem data-field="CopagoFijo" editor-type="dxNumberBox" :editor-option="{format: '0.00'}">
                                <DxFormLabel text="Copago" />
                            </DxFormItem>
                            <DxFormItem data-field="Beneficio" editor-type="dxNumberBox" :editor-option="{format: '0.00'}">
                                <DxFormLabel text="Beneficio SaSi" />
                            </DxFormItem>
                        </DxFormItem>
                    </DxFormGroupItem>
                </DxForm>
            </vx-card>
        </vs-col>
    </vs-row>

    <vs-row class="pt-2">
        <!-- Receta -->
        <vs-col vs-w="6" vs-justify="center" vs-align="center" class="pr-2">
            <vx-card style="height: 690px;" :title="'Total: ' + (this.infoSeguro.Total ? this.infoSeguro.Total : '0.00')">
                <DxDataGrid :ref="gridReceta" v-bind="DefaultDxGridConfiguration" :data-source="productosReceta" :headerFilter="{visible:false, allowSearch:false}" :width="'100%'" :height="'500px'" @row-updated="CalcularTotales" @row-removed="CalcularTotales">
                    <DxDataGridToolbar>
                        <DxDataGridItem location="before" template="botonGuardar" />
                        <DxDataGridItem name="searchPanel" />
                    </DxDataGridToolbar>
                    <DxDataGridSelection mode="single" />

                    <DxDataGridEditing :allow-updating="true" :allow-adding="false" :allow-deleting="true" mode="cell" :use-icons="true" :confirmDelete="false" />

                    <DxDataGridColumn :width="50" type="buttons" alignment="center">
                        <DxDataGridButton name="delete" />
                    </DxDataGridColumn>
                    <DxDataGridColumn :width="300" data-field="Descripcion" caption="Producto" alignment="center" :allow-editing="false">
                        <!-- <DxLookup :dataSource="productosDataSource" value-expr="Producto" display-expr="Descripcion" /> -->
                    </DxDataGridColumn>
                    <DxDataGridColumn :width="150" data-field="Cantidad" alignment="center" :editor-options="{ editorType: 'dxNumberBox', min: 1 }" :validation-rules="[{ type: 'required', message: 'Campo obligatorio' }]" />
                    <DxDataGridColumn :width="250" data-field="Prescripcion" caption="Prescripción" :allowHeaderFiltering="false" data-type="string" alignment="center" />

                    <template #botonGuardar>
                        <div class="w-full div-button">
                            <vs-button color="success" type="filled" :disabled="productosReceta.length == 0" @click="GuardarReceta">
                                <font-awesome-icon :icon="['fas', 'save']" class="pr-2" style="font-size: 16px" />
                                <span> Guardar </span>
                            </vs-button>
                        </div>
                    </template>
                </DxDataGrid>
                <DxForm labelMode="floating" :form-data.sync="notas" :ref='formNotas'>
                    <DxFormGroupItem>
                        <DxFormItem data-field="Notas" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000 }" />
                    </DxFormGroupItem>
                </DxForm>
            </vx-card>
        </vs-col>

        <!-- Productos -->
        <vs-col vs-justify="center" vs-align="center" vs-w="6" class="pr-2">
            <!-- <div class="pb-0 pr-2 pl-3"> -->
            <vx-card style="height: 690px;">
                <DxForm :ref="formularioDatos" :form-data.sync="infoProducto" labelMode="floating" :readOnly="true">
                    <DxFormGroupItem>
                        <DxFormItem item-type="group" :col-count-by-screen="colCountByScreen">
                            <DxFormItem data-field="DescripMarca">
                                <DxFormLabel text="Marca" />
                            </DxFormItem>
                            <DxFormItem data-field="Presentacion">
                                <DxFormLabel text="Presentación" />
                            </DxFormItem>
                        </DxFormItem>
                    </DxFormGroupItem>
                </DxForm>
                <DxDataGrid :ref="gridProductos" v-bind="DefaultDxGridConfiguration" :data-source="productos" :headerFilter="{visible:true, allowSearch:true}" :width="'100%'" :height="'600px'" @row-click="SeleccionarProducto" @row-dbl-click="AgregarProducto">
                    <DxDataGridSelection mode="single" />
                    <DxDataGridColumn :width="350" data-field="Descripcion" caption="Descripción" alignment="center" />
                    <DxDataGridColumn :width="200" data-field="PrincipioActivo" caption="Principio activo" data-type="string" alignment="center" />
                    <DxDataGridColumn :width="150" data-field="DescripMarca" caption="Marca" :allowHeaderFiltering="false" data-type="string" alignment="center" />
                </DxDataGrid>
            </vx-card>
            <!-- </div> -->
        </vs-col>
    </vs-row>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'

const gridReceta = 'gridReceta'
const gridProductos = 'gridProductos'

const formularioDatos = 'formDatos'
const formularioBeneficios = 'formBeneficios'
const formNotas = 'formNotas'

export default {
    name: 'Prescripcion',
    components: {},
    data() {
        return {
            formDatos: null,
            formBeneficios: null,
            gridReceta,
            gridProductos,
            DefaultDxGridConfiguration,

            infoSeguro: {},
            infoProducto: {},
            totalReceta: null,

            formularioDatos,
            formularioBeneficios,
            formNotas,

            productos: [], //Variable que guarda el catálogo de productos
            productosReceta: [], //Variable que guarda los productos agregados a la receta
            notas: null, //Variable para guardar las notas de la receta
            tieneBeneficio: false, // Variable para manejar si se debe calcular beneficio

            //Varibles de semaforo
            current: 'red',

            productosDataSource: {
                paginate: true,
                pageSize: 10,
                sort: 'Codigo',
                // loadMode: 'raw',
                byKey: (e) => {
                    return this.productos.Descripcion === e ? this.cie10.Descripcion : ''
                },
                load: (e) => {
                    if (e.take) {
                        if (e.skip === e.take) {
                            e.take = e.take + 10
                        }
                    }
                    return this.$filtrado_avanzado_palabras(e.searchValue, this.productos, 'Descripcion', e.skip, e.skip + e.take)
                }
            },
        }
    },
    props: {
        IdCita: null,
        IdCliente: null,
        IdPaciente: null,
        IdConsulta: null,
        NombreMedico: null
    },
    methods: {
        submit() {
            this.dataGrid.saveEditData()
        },
        CargarDatos() {
            this.axios.post('/app/v1_Historial_Clinico/BusquedaInformacionSeguro', {
                    IdCita: this.IdCita
                })
                .then(resp => {
                    var e = resp.data.json[0]
                    this.CargarProductos(e)
                    this.CargarCopago(e)
                })
        },

        CargarCopago(e) {
            this.axios.post('/app/v1_Historial_Clinico/BusquedaInformacionCopago', {
                    IdContrato: e.IdContrato,
                    IdPlan: e.IdPlan,
                    IdTipoHospital: e.TipoDeHospital,
                    IdCliente: e.IdCliente,
                    NoAdhesion: e.NumeroAdhesion
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.infoSeguro = {
                            ...e,
                            ...resp.data.json[0],
                            Beneficio: 0,
                            Total: 0
                        }

                        this.infoSeguro.EventosDisponibles = parseInt(this.infoSeguro.EventosDisponibles)
                        this.infoSeguro.NoPagos = parseInt(this.infoSeguro.NoPagos)
                        this.infoSeguro.PeriodoEsperaMeses = parseInt(this.infoSeguro.PeriodoEsperaMeses)

                        if (this.infoSeguro.AplicaBeneficio === 'SI') {
                            this.tieneBeneficio = true
                            if (this.infoSeguro.EventosDisponibles > 0 && this.infoSeguro.NoPagos >= this.infoSeguro.PeriodoEsperaMeses) {
                                this.current = 'green'
                            } else {
                                this.current = 'yellow'
                            }
                        } else {
                            this.tieneBeneficio = false
                            this.current = 'red'
                        }

                    } else {
                        this.tieneBeneficio = false
                        this.infoSeguro = {
                            ...e,
                            CopagoFijo: 0,
                            MontoBeneficio: 0,
                            Beneficio: 0,
                            Descripcion: "",
                            Poliza: "",
                            EventosDisponibles: "",
                            NoPagos: "",
                            Total: 0,
                            IdPlan: null
                        }

                        this.current = 'red'
                    }

                    if (this.productosReceta.length > 0) {
                        this.CalcularTotales()
                    }

                    this.infoSeguro.CopagoFijo = parseFloat(this.infoSeguro.CopagoFijo).toFixed(2)
                    this.infoSeguro.MontoBeneficio = parseFloat(this.infoSeguro.MontoBeneficio).toFixed(2)
                    this.infoSeguro.Beneficio = parseFloat(this.infoSeguro.Beneficio).toFixed(2)
                    this.infoSeguro.Total = parseFloat(this.infoSeguro.Total).toFixed(2)
                })
        },

        CargarProductos(e) {
            this.axios.post('/app/v1_Historial_Clinico/BusquedaProductos', {
                    IdSucursal: e.CodigoFarmaciaExt
                })
                .then(resp => {
                    this.productos = resp.data.json
                })
        },

        LimpiarVariables() {
            this.infoSeguro = {}
            this.infoProducto = {}
            this.productos = []
            this.productosReceta = []
            this.notas.Notas = ''
            this.current = 'red'
            this.formularioNotas.repaint()

            this.dataGridProductos.refresh();
            this.dataGridProductos.clearFilter();
            this.dataGridProductos.clearSelection();
            this.dataGridProductos.clearSorting();

            this.dataGridReceta.refresh();
            this.dataGridReceta.clearFilter();
            this.dataGridReceta.clearSelection();
            this.dataGridReceta.clearSorting();
        },

        AgregarProducto(e) {
            var existencia = []
            if (this.productosReceta.length > 0) {
                existencia = this.productosReceta.filter(x => x.Producto === e.data.Producto)
            }
            if (existencia.length > 0) {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'Producto existente',
                    acceptText: 'Aceptar',
                    text: 'El producto ya ha sido agregado a la receta.',
                    buttonCancel: 'border',
                    clientWidth: 100,
                    accept: () => {},
                })
            } else {
                e.data.Cantidad = 1
                e.data.Prescripcion = ""
                this.productosReceta.push(e.data)
            }
            this.dataGridProductos.clearSelection()
            this.infoProducto = {}
            this.CalcularTotales()
        },
        SeleccionarProducto(e) {
            this.infoProducto = {
                ...e.data
            }
        },
        GuardarReceta() {
            setTimeout(() => {
                this.axios.post('/app/v1_Historial_Clinico/InsertarReceta', {
                        ...this.infoSeguro,
                        IdBeneficio: this.infoSeguro.IDBENEFICIO,
                        Sucursal: this.infoSeguro.CodigoFarmaciaExt,
                        IdCopago: this.infoSeguro.idcopago,
                        Copago: this.infoSeguro.CopagoFijo,
                        Observaciones: this.notas.Notas ? this.notas.Notas : '',
                        Detalle: this.productosReceta,
                        TieneBeneficio: this.infoSeguro.AplicaBeneficio == 'SI' ? 1 : null
                    })
                    .then(resp => {
                        if (resp.data.codigo === 0) {
                            this.$emit('noReceta', resp.data.NoReceta)
                        }
                    })
            }, 1000);
        },
        CalcularTotales() {
            this.CalcularTotalReceta()
            if (this.tieneBeneficio) {
                this.CalcularBeneficioSaSi()
            }

        },
        CalcularTotalReceta() {
            var total = 0

            for (let i = 0; i < this.productosReceta.length; i++) {
                var totalU = this.productosReceta[i].Cantidad * parseFloat(this.productosReceta[i].PVentaA).toFixed(2)
                this.productosReceta[i].Total = totalU

                //Mapea la información para el backend
                this.productosReceta[i].PrecioU = parseFloat(this.productosReceta[i].PVentaA).toFixed(2)

                total = total + totalU
            }

            this.infoSeguro.Total = parseFloat(total).toFixed(2)
            this.formularioBeneficiosSaludSiempre.repaint()
        },
        CalcularBeneficioSaSi() {
            var beneficio = 0
            var total = 0

            for (let i = 0; i < this.productosReceta.length; i++) {
                if (this.productosReceta[i].CubreSaSi == 1) {
                    total = total + (this.productosReceta[i].Cantidad * parseFloat(this.productosReceta[i].PVentaA).toFixed(2))
                }
            }

            if (total >= this.infoSeguro.CopagoFijo) {
                var x = 0
                x = total - parseFloat(this.infoSeguro.CopagoFijo).toFixed(2)

                if (x >= this.infoSeguro.MontoBeneficio) {
                    beneficio = parseFloat(this.infoSeguro.MontoBeneficio).toFixed(2)
                } else {
                    beneficio = parseFloat(x).toFixed(2)
                }

                this.infoSeguro.Beneficio = parseFloat(beneficio).toFixed(2)
            } else {
                beneficio = 0
                this.infoSeguro.Beneficio = parseFloat(beneficio).toFixed(2)
            }

            this.formularioBeneficiosSaludSiempre.repaint()
        }
    },
    mounted() {},
    watch: {},
    computed: {

        dataGridReceta: function () {
            return this.$refs[gridReceta].instance;
        },

        dataGridProductos: function () {
            return this.$refs[gridProductos].instance;
        },

        colCountByScreen() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 2,
                    lg: 2
                };
        },
        colCountByScreenDatos() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 3,
                    lg: 3
                };
        },

        formularioDatosGenerales: function () {
            return this.$refs[formularioDatos].instance;
        },

        formularioBeneficiosSaludSiempre: function () {
            return this.$refs[formularioBeneficios].instance;
        },

        formularioNotas: function () {
            return this.$refs[formNotas].instance;
        },
    },
}
</script>

<style scoped>
body {
    background: linear-gradient(rgb(52, 166, 255), rgb(71, 124, 255));
    height: 100vh;
}

#traffic-light {
    width: 260px;
    height: 80px;
    background: #222;
    border-radius: 8px;
    margin: auto;
    padding: 10px;
    position: relative;
}

.light {
    display: inline-block;
    border-radius: 100%;
    width: 60px;
    height: 60px;
    margin-right: 10px;
    margin-left: 10px;
    /* margin-bottom: 8px; */
    opacity: 0.2;
    transition: opacity 0.2s;
    /* position: relative; */
}

.light.active span {
    opacity: 1;
    visibility: visible;
}

.active {
    opacity: 1;
    box-shadow: 0px 0px 30px #ffffff;
}

.red {
    background: rgb(255, 16, 16);
}

.yellow {
    background: rgb(255, 255, 16);
}

.green {
    background: rgb(16, 255, 16);
}

.div-button {
    display: flex;
    justify-content: center;
}
</style>
