/*=========================================================================================
  File Name: actions.js
  Description: Vuex Store - actions
  ----------------------------------------------------------------------------------------
  Item Name: Vuexy - Vuejs, HTML & Laravel Admin Dashboard Template
  Author: Pixinvent
  Author URL: http://www.themeforest.net/user/pixinvent
==========================================================================================*/
var timer;
const actions = {

    // /////////////////////////////////////////////
    // COMPONENTS
    // /////////////////////////////////////////////

    // Vertical NavMenu
    updateVerticalNavMenuWidth({ commit }, width) {
        commit('UPDATE_VERTICAL_NAV_MENU_WIDTH', width)
    },

    // VxAutoSuggest
    updateStarredPage({ commit }, payload) {
        commit('UPDATE_STARRED_PAGE', payload)
    },

    // The Navbar
    arrangeStarredPagesLimited({ commit }, list) {
        commit('ARRANGE_STARRED_PAGES_LIMITED', list)
    },
    arrangeStarredPagesMore({ commit }, list) {
        commit('ARRANGE_STARRED_PAGES_MORE', list)
    },

    // /////////////////////////////////////////////
    // UI
    // /////////////////////////////////////////////

    toggleContentOverlay({ commit }) {
        commit('TOGGLE_CONTENT_OVERLAY')
    },
    updateTheme({ commit }, val) {
        commit('UPDATE_THEME', val)
    },

    // /////////////////////////////////////////////
    // User/Account
    // /////////////////////////////////////////////

    updateUserInfo({ commit }, payload) {
        commit('UPDATE_USER_INFO', payload)
    },

    sesionGuardar({ commit }, sesion) {
        commit('SESION_GUARDAR', sesion)
    },

    sesionEliminar({ commit }) {
        commit('SESION_ELIMINAR')
    },

    sesionValidar({ commit }) {
        commit('SESION_VALIDAR')
        commit('SESION_TIEMPO', true)
        clearInterval(timer)
        timer = setInterval(() => {
            commit('SESION_TIEMPO', false)
        }, 1000);
    },

    sesionBienvenida({ commit }, state) {
        commit('SESION_BIENVENIDA', state)
    },

    sesionSocket({ commit }, state) {
        commit('SESION_SOCKET', state)
    },

    sessionReiniciarTiempo({ commit }) {
        commit('SESION_TIEMPO', true)
        clearInterval(timer)
        timer = setInterval(() => {
            commit('SESION_TIEMPO', false)
        }, 1000);
    },

    appPCSocket({ commit }, state) {
        commit('APPPC_SOCKET', state)
    },

    // /////////////////////////////////////////////
    // Funcionalidad
    // /////////////////////////////////////////////
    funcionalidadListado({ commit }, state) {
        commit('FUNCIONALIDAD_LISTADO', state)
    },

    funcionalidadDocumentacion({ commit }, state) {
        commit('FUNCIONALIDAD_DOCUMENTACION', state)
    },

    // /////////////////////////////////////////////
    // Privilegios
    // /////////////////////////////////////////////
    privilegiosListado({ commit }, state) {
        commit('PRIVILEGIOS_LISTADO', state)
    },

    // /////////////////////////////////////////////
    // Tabs
    // /////////////////////////////////////////////
    tabsListado({ commit }, state) {
        commit('TABS_LISTADO', state)
    },

    // /////////////////////////////////////////////
    // Instancia
    // /////////////////////////////////////////////
    instanciaToken({ commit }, state) {
        commit('INSTANCIA_TOKEN', state)
    },
    instanciaCambio({ commit }, state) {
        commit('INSTANCIA_CAMBIO', state)
    },
    instanciaListado({ commit }, state) {
        commit('INSTANCIA_LISTADO', state)
    },

    /**
    Extras
     */
    notify({ commit }, state) {
        commit('EXTRA_NOTIFY', state)
    },
    loading({commit},state){
        commit('EXTRA_LOADING', state)
    },
    reporte({commit},state){
        commit('EXTRA_REPORTE', state)
    },
    solicitarCredencial({commit},state){
        commit('EXTRA_CREDENCIAL', state)
    }
}

export default actions
