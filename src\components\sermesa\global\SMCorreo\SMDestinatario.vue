<template>
<vs-chips v-model="myMaillist" :placeholder="placeholder" remove-icon="delete" @input="cleanMailList">
    <vs-chip :key="chip" @click="remove(chip)" v-for="chip in myMaillist" closable>
        {{ chip }}
    </vs-chip>
</vs-chips>
</template>

<script>
const emailRegex = new RegExp(/^[A-Za-z0-9_!#$%&'*+/=?`{|}~^.-]+@[A-Za-z0-9.-]+$/)
export default {
    name: 'sm-mailist',
    props: {
        value: Array,
        placeholder: String,
        regex: RegExp,//debe ser un RegExp
    },
    data() {
        return {
            myMaillist: [],
            regexValidator: null,
        }
    },
    methods: {
        remove(item) {
            this.myMaillist.splice(this.myMaillist.indexOf(item), 1)
            this.$emit('input', this.myMaillist)
            this.$emit('update', this.myMaillist)
        },
        /**recibe un arreglo y valida el último elemento si cumple con el formato de correo electrónico */
        cleanMailList(item) {
            this.$emit('input', item)
            this.$emit('update', item)
        },
    },
    beforeMount(){
        this.regexValidator = this.regex?? emailRegex
    },
    watch: {
        'value'(val) {
            if (Array.isArray(val)) {
                let validAdress = []
                val.map(x => {
                    if (typeof x == 'string') {
                        x = x.replaceAll(/\s/gi, "")
                        if (this.regexValidator.test(x))
                            validAdress.push(x)
                    }
                })
                this.myMaillist = validAdress
            } else if (typeof val == 'string') {
                val = val.replaceAll(/\s/gi, "")
                this.myMaillist = this.regexValidator.test(val) ? [val] : []
            }
        },
        'regex'(val) {
            this.regexValidator = val?? emailRegex
        }
    }
}
</script>
