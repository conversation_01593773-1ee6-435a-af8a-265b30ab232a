<template>
    <vx-card :title="`Monitor de Contingencias - Serie ${serieFactura} ${sesion.sesion_sucursal_nombre}`"> 
        <vs-row>
            <vs-row class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12 p-1" >
                <vs-row class="w-full">
                    <vs-row class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12" >
                        <vs-col class="w-full p-1" >                                  
                            <vs-input label="Serie Factura/NC" class="w-full" v-model="factura.serie" :disabled="true"/>
                        </vs-col>
                        <vs-col class="w-full p-1">                                  
                            <vs-input label="Número Factura/NC" type="number" class="w-full" v-model="factura.numero" :disabled="true"/>
                        </vs-col>
                    </vs-row>
                    <vs-row class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12 " >
                        <vs-col class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12 p-1" >   
                            <label class="typo__label label-shrink" style="white-space: nowrap; color:#626262; font-size:12px;  padding-bottom:2px; padding-top:0px;"> Tipo Documento  </label>  
                            <DxSelectBox            
                                style="padding: 2px;"
                                :items="listas.tiposDocumentos"
                                display-expr="Descripcion"
                                placeholder=""
                                v-model="documento.tipoDocumento"
                                :disabled="!facturaSeleccionada"
                            />   
                            <div class="w-full">
                                <label class="w-full pl-3" style="border-radius: 3px; display: inline-flex; background-color:#F5F5F5; text-align: center;color:black;font-size:15px; "> {{factura.facturaSeleccionada.TipoDocumento}} </label>
                            </div> 
                        </vs-col>              
                        <vs-col class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12 p-1">
                            <vs-input label="No. documento" 
                                    v-model="documento.noDocumento" 
                                    class="w-full"     
                                    :disabled="!facturaSeleccionada"  
                                    @keydown.enter="Consulta().ValidacionDocumentoTributario()" @keydown.tab.prevent="Consulta().ValidacionDocumentoTributario()"                           
                                    />  
                            <div class="w-full">
                                <label class="w-full pl-3" style="border-radius: 3px; display: inline-flex; background-color:#F5F5F5; text-align: center;color:black;font-size:15px; "> {{factura.facturaSeleccionada.NIT}} </label>
                            </div>     
                        </vs-col>  
                        <vs-col class="w-full p-1">
                            <vs-input label="Nombre"  
                                    class="w-full"
                                    v-model="documento.nombre" 
                                    :disabled="!facturaSeleccionada"
                                    />  
                            <div class="w-full">
                                <label class="w-full pl-3" style="border-radius: 3px; display: inline-flex; word-wrap: break-word; white-space: wrap; background-color:#F5F5F5; text-align: left;color:black;font-size:15px; "> {{factura.facturaSeleccionada.Nombre}} </label>
                            </div>     
                        </vs-col>  
                    </vs-row>                   
                </vs-row>
            </vs-row>            
            <vs-row class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12 p-1 flex"  style="align-items:center; justify-content:center;">    
                <vs-row class="w-full" vs-justify="center">
                    <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-3/12 p-1" >
                        <vs-button @click="Consulta().Fel(factura.serie,factura.numero)" color="success" class="w-full ajustar-texto-botton"
                                   :disabled="!facturaSeleccionada">
                            <i class="fa fa-cogs"></i>
                            Sincronizar
                        </vs-button>
                    </vs-col>    
                    <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-3/12 p-1" >
                        <vs-button :disabled="!facturaSeleccionada"
                            @click="ActualizarDocumento()" color="primary" class="w-full ajustar-texto-botton">
                            <i class="fa fa-database"></i> 
                            Actualizar Doc
                        </vs-button>
                    </vs-col>                        
                    <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-2/12 p-1">
                        <vs-button @click="CargarFacturasEnContingencias(); Consulta().LimpiarDatos();" color="primary" icon-pack="feather" icon="icon-refresh-cw"
                                   class="w-full ajustar-texto-botton">                                   
                            Recargar
                        </vs-button>
                    </vs-col>                        
                    <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1">
                        <vs-button @click="Consulta().LimpiarDatos()" color="primary" icon-pack="feather" icon="icon-trash" 
                                   class="w-full ajustar-texto-botton">
                            Limpiar
                        </vs-button>
                    </vs-col> 
                </vs-row>
                <vs-row>
                </vs-row>
            </vs-row>
            <vs-row vs-justify="center" vs-align="center" class='w-full'>
                <vs-row vs-justify="center" vs-align="center" class="w-full">   
                    <DxDataGrid v-bind="DefaultDxGridConfiguration" :headerFilter="{visible:false,allowSearch:false}"   
                            @selection-changed="(factura)=>Consulta().SeleccionFactura(factura)" 
                            :ref="facturasRef"                                                                      
                            :data-source="listas.facturas" width="100%" height="'100%'" >
                            <DxDataGridSelection mode="single"/>
                            <DxDataGridColumn :width="150" data-field="Operacion" caption="Operación" alignment="center" />
                            <DxDataGridColumn :width="100" data-field="Empresa" caption="Empresa" alignment="center" />
                            <DxDataGridColumn caption="Factura" alignment="center">
                                <DxDataGridColumn :width="100" data-field="Serie" caption="Serie"    alignment="center" />
                                <DxDataGridColumn :width="150" data-field="Codigo" caption="Número"  alignment="center" />
                            </DxDataGridColumn>
                            <DxDataGridColumn caption="Admisión" alignment="center">
                                <DxDataGridColumn :width="100" data-field="SerieAdmision" caption="Serie"    alignment="center" />
                                <DxDataGridColumn :width="150" data-field="Admision" caption="Número"  alignment="center" />
                            </DxDataGridColumn>
                            <DxDataGridColumn :width="150" data-field="Fecha" caption="Fecha" alignment="center" />
                            <DxDataGridColumn :width="200" data-field="Total" caption="Total" format="Q #,##0.00" data-type="number" alignment="right" />
                            <DxDataGridColumn :width="200" data-field="Descuento" caption="Descuento" format="Q #,##0.00" data-type="number" alignment="right" />
                            <DxDataGridColumn :width="200" data-field="TotalFac" caption="Total Factura" format="Q #,##0.00" data-type="number" alignment="right" />
                            <DxDataGridColumn :width="70" data-field="Status" caption="Status"    alignment="center" />
                            <DxDataGridColumn :width="70" data-field="Tipo" caption="Tipo"    alignment="center" />
                            <DxDataGridPaging :page-size="15"/>
                            <DxDataGridPager  :show-page-size-selector="true"
                                        :allowed-page-sizes="[15, 30, 45]"
                            />
                    </DxDataGrid>         
                </vs-row>                                    
            </vs-row>
        </vs-row>
    </vx-card>
</template>
<script> 
import {DefaultDxGridConfiguration} from './data';
import { ref } from 'vue'

const facturasRef = 'grid1'
export default{
    name:'MONITOR_CONTINGENCIAS',    
    props:{
        serieFactura: {
                        type: String,
                        default: ''
                    },
        formatoImpresion:{
                        type: String,
                        default: ''
                    }
    },
    data(){
        return{
            facturasRef,
            facturaSeleccionada:false,
            admision:null,
            factura:{
                tipo:null,
                serie:null,
                numero:null,
                facturaSeleccionada:{}
            },
            documento:{
                tipoDocumento:null,
                noDocumento:null,
                nombre:null,
                validacion:null
            },
            listas:{
                tiposDocumentos:[],
                facturas:[]
            },
            DefaultDxGridConfiguration:{...DefaultDxGridConfiguration},
        }
    },
    methods:{
        init(){            
            this.Consulta().CargarTipoDocumento();
        },
        CargarFacturasEnContingencias(){
            if(!this.serieFactura || this.serieFactura.length <= 0 ){
                this.Consulta().MostrarError('No se cargo la serie de factura ')
                return
            }

            this.axios.post('/app/v1_FacturaElectronica/ContigenciaFel',{
                                serieFactura:this.serieFactura
                            })
                      .then(resp=>{
                            if(resp.data.codigo != 0) return                            
                            this.listas.facturas = resp.data.json
                      })
        },  
        ActualizarDocumento(){
            if (this.facturaSeleccionada.Tipo !='F'){
                this.Consulta().MostrarError('Acción solo aplica para facturas pendientes de emisión')
                return

            }


            if(!this.Consulta().ValidarVariable(this.documento.validacion)){
                this.Consulta().MostrarError('Ingresar un documento valido para realizar la factura')
                return
            }

            this.axios.post('/app/v1_FacturaElectronica/ActualizaFel',{
                                serieFactura:   this.factura.serie,
                                numeroFactura:  this.factura.numero,
                                tipoReceptor:   this.documento.tipoDocumento.Valor,
                                nitFactura:     this.documento.noDocumento,
                                nombreFactura:  this.documento.nombre
                            })
                       .then(resp=>{
                            if(resp.data.codigo == 0){
                                this.factura.facturaSeleccionada.TipoDocumento = this.documento.tipoDocumento.Descripcion
                                this.factura.facturaSeleccionada.Nombre = this.documento.nombre
                                this.factura.facturaSeleccionada.NIT = this.documento.noDocumento
                                this.documento.validacion = false
                            }
                       })
        },              
        Consulta(){
            return{
                Fel: (serieFactura,numeroFactura)=>{
                    if (this.factura.facturaSeleccionada.TipoOp == '0'){
                        this.axios.post('/app/v1_FacturaElectronica/AnulaDte',
                                    {serieFactura: serieFactura,
                                        numeroFactura: numeroFactura
                                    })
                        .then(resp =>{
                            if(resp.data.codigo == 0 && resp.data.json[0].codigo == 0){
                                this.Consulta().MostrarSatisfactorio(resp.data.json[0].descripcion);
                                this.CargarFacturasEnContingencias();
                            }else{
                                this.Consulta().MostrarError(resp.data.json[0].descripcion);
                            }
                        })
                    }else{
                        this.axios.post('/app/v1_FacturaElectronica/GeneraFel',
                                    {serieFactura: serieFactura,
                                        numeroFactura: numeroFactura
                                    })                                  
                        .then(resp=>{
                            if(resp.data.codigo == 0 && resp.data.json[0].codigo == 0){                                                                                        
                                this.Consulta().MostrarSatisfactorio(resp.data.json[0].descripcion);
                                this.CargarFacturasEnContingencias();

                                if (this.factura.facturaSeleccionada.tipo =='F')
                                {
                                    this.$vs.dialog({
                                        type: 'confirm',
                                        color: 'success',
                                        title: 'Factura Fel',
                                        acceptText: 'Aceptar',
                                        cancelText: 'Cancelar',
                                        text: '¿Desea imprimir la factura generada?',   
                                        accept: () => {
                                            this.GenerarFactura(serieFactura,numeroFactura);                                                                               
                                            this.Consulta().LimpiarDatos()
                                        },
                                        cancel: () => {
                                            this.Consulta().LimpiarDatos()
                                        }                                           
                                    })
                                }
                            }else{
                                this.Consulta().MostrarError(resp.data.json[0].descripcion);
                            }
                        })                                 
                    }
                },
                ValidarVariable:(entrada)=>{   
                    if(typeof(entrada) == 'number'){
                        return entrada ? true : false;
                    }else if(typeof(entrada) == 'string'){
                        return entrada && entrada.length > 0 ? true : false;
                    }else if(typeof(entrada) == 'object'){
                        return entrada ? true : false;
                    }            
                    return false;
                },
                SeleccionFactura:(factura)=>{
                    if(!factura.selectedRowsData || factura.selectedRowsData.length <= 0 ){
                        this.factura.tipo      = null
                        this.factura.serie     = null
                        this.factura.numero    = null 
                        this.factura.facturaSeleccionada = {}

                        this.documento.tipoDocumento = null    
                        this.documento.noDocumento   = null
                        this.documento.nombre        = null      
                        this.facturaSeleccionada = false          
                    }else{
                        let DatosFactura = factura.selectedRowsData[0]
                        let tipoDocumento = this.listas.tiposDocumentos.find(doc=>doc.Valor == DatosFactura.TipoReceptor)
                        
                        this.factura.tipo      = DatosFactura.Tipo
                        this.factura.serie     = DatosFactura.Serie
                        this.factura.numero    = DatosFactura.Codigo
                        
                        if(tipoDocumento){
                            this.documento.tipoDocumento = ref(tipoDocumento)
                            this.factura.facturaSeleccionada.TipoDocumento = tipoDocumento.Descripcion
                        }

                        this.documento.noDocumento   = DatosFactura.NIT
                        this.documento.nombre        = DatosFactura.Nombre
                        
                        this.factura.facturaSeleccionada = DatosFactura
                        this.facturaSeleccionada = true
                    }
                },
                MostrarError:(mensaje,tiempo=2000)=>{
                    this.$vs.notify({
                        time:tiempo,
                        title: 'Monitor Contingencias',
                        color: 'danger',
                        text:mensaje,
                        position: 'top-center'
                    })
                },
                MostrarSatisfactorio:(mensaje)=>{
                    this.$vs.notify({
                        time:2000,
                        title: 'Monitor Contingencias',
                        color: 'success',
                        text:mensaje,
                        position: 'top-center'
                    })
                },
                LimpiarDatos:()=>{
                    this.$refs[facturasRef].instance.clearSelection()
                },
                EsVacia:(variable)=>{
                    return variable?false:true;
                },
                CargarTipoDocumento:()=>{
                    this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision', {
                                        opcion:'C',
                                        subOpcion:2,
                                        agrupacion:'TipoReceptor'
                                    })
                            .then(resp => {                            
                                        if (resp.data && resp.data.json.length > 0){
                                            this.listas.tiposDocumentos = resp.data.json;
                                        }
                                    }
                                )
                },ValidacionDocumentoTributario:()=>{

                    if (this.facturaSeleccionada.Tipo !='F'){
                        this.Consulta().MostrarError('Acción solo aplica para facturas pendientes de emisión')
                        return
                    }

                    if(this.Consulta().EsVacia(this.documento.tipoDocumento)){
                        this.Consulta().MostrarError('Seleccione un tipo de documento para el cliente');
                        return;
                    }
                    if(this.Consulta().EsVacia(this.documento.noDocumento)){
                        this.Consulta().MostrarError('Ingrese el NIT/DPI del cliente');
                        return;
                    }
                    this.axios.post('/app/v1_caja/ValidacionDocumentoTributario',{
                                        tipoReceptor: parseInt(this.documento.tipoDocumento.Valor),
                                        documento: this.documento.noDocumento
                                    })
                            .then(resp=>{
                                        if ( resp.data.codigo == 0 && resp.data.json.length > 0 && resp.data.json[0].codigo == 0){
                                            this.documento.nombre = resp.data.json[0].NOMBRE                                                                                        
                                            this.documento.validacion = 1
                                        }else if ( resp.data.codigo == 0 && resp.data.json.length > 0 && resp.data.json[0].codigo != 0){
                                            this.Consulta().MostrarError(resp.data.json[0].descripcion);
                                            this.documento.nombre = null
                                            this.documento.validacion = null
                                        }
                                        else if(resp.data.json.length == 0){
                                            this.documento.nombre = null
                                            this.documento.validacion = null
                                            this.Consulta().MostrarError('No se encontraron datos para el documento ingresado');
                                        }
                                    })
                                
                },  
            }
        },
        async GenerarFactura(Serie,NumeroFactura) {
            let reporte = "Impresion FEL"

            let postData = {
                SerieFactura: Serie ,
                NumFactura: NumeroFactura,
                nombrereporte: this.formatoImpresion
            }


            this.$reporte_modal({
                Nombre: reporte,
                Opciones: {
                    ...postData
                }
            })                

        },     
    },
    mounted(){
        this.init()
    },
    computed:{
        sesion() {
            return this.$store.state.sesion
        }
    },
}
</script>
<style>
    .vs-row-especial{
        display: block !important;
    }
    /* Estilo tablas dev express */
    .dx-popup-title {
        background-color: #f4eade !important;
        color: #2f496e !important;
    }

    .dx-list-item>#itemsMenuDrawer {
        color: #2988bc !important;
        background-color: #f4eade !important;
    }

    .dx-list-item-selected>#itemsMenuDrawer {
        color: #f4eade !important;
        background-color: #ed8c72 !important;
    }

    .dx-scrollable-container {
        touch-action: auto !important;
    }

    /*Ancho mínmo de los grid*/
    #Contenido .dx-datagrid {
        min-width: 302px;
    }

    /**Modal actualizacion de peso y talla */
    #popupTallaPeso .vs-popup {
        width: 400px !important;
    }

    .dx-datagrid-headers td {
        vertical-align: middle !important;
    }

    .dx-resizable {
        display: inline-grid;
    }

    .dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed)>td {
        background-color: #00ccff !important;
        color: black !important;
    }

    .dx-datagrid-rowsview .dx-row.dx-data-row.dx-column-lines.dx-state-hover>td {
        background-color: #79e4ff !important;
        color: black !important;
        font-size: 16px;
    }

    .dx-datagrid-headers {
        background-color: linen !important;
        color: black !important;
        font-weight: bold;
    }

    td {
        vertical-align: middle !important;
    }
    /* Fin Estilo tablas dev express */
</style>