<template>
<vx-card title="Dashboard">
    <div class="flex flex-wrap">
        <div class="w-full md:w-4/5">
            <vx-card title="Emergencias Entrantes">
                <div class="panel-body">

                    <vs-table :data="emergencias">
                        <template slot="thead">
                            <vs-th>Hospital</vs-th>
                            <vs-th>Cliente</vs-th>
                            <vs-th>Paciente</vs-th>
                            <vs-th>Nombre</vs-th>
                        </template>

                        <template slot-scope="{data}">
                            <vs-tr :key="indextr" v-for="(tr, indextr) in data">

                                <vs-td :data="data[indextr].email">
                                    {{ data[indextr].email }}
                                </vs-td>

                                <vs-td :data="data[indextr].username">
                                    {{ data[indextr].name }}
                                </vs-td>

                                <vs-td :data="data[indextr].id">
                                    {{ data[indextr].website }}
                                </vs-td>

                                <vs-td :data="data[indextr].id">
                                    {{ data[indextr].id }}
                                </vs-td>

                            </vs-tr>
                        </template>
                    </vs-table>

                </div>
            </vx-card>
            <br>
            <vx-card title="Nuevos Usuarios">
                <div class="panel-body">
                    <vs-table :data="emergencias">
                        <template slot="thead">
                            <vs-th>Nombre</vs-th>
                            <vs-th>Correo</vs-th>
                            <vs-th>Telefono</vs-th>
                            <vs-th>Afiliación</vs-th>
                            <vs-th>Fecha Registro</vs-th>
                            <vs-th>Acción</vs-th>
                        </template>

                        <template slot-scope="{data}">
                            <vs-tr :key="indextr" v-for="(tr, indextr) in data">

                                <vs-td :data="data[indextr].email">
                                    {{ data[indextr].email }}
                                </vs-td>

                                <vs-td :data="data[indextr].username">
                                    {{ data[indextr].name }}
                                </vs-td>

                                <vs-td :data="data[indextr].id">
                                    {{ data[indextr].website }}
                                </vs-td>

                                <vs-td :data="data[indextr].id">
                                    {{ data[indextr].id }}
                                </vs-td>

                            </vs-tr>
                        </template>
                    </vs-table>

                </div>
            </vx-card>
            <br>
            <vx-card title="Usuarios Contraseña temporal">
                <div class="panel-body">

                    <vs-table :data="emergencias">
                        <template slot="thead">
                            <vs-th>Hospital</vs-th>
                            <vs-th>Cliente</vs-th>
                            <vs-th>Paciente</vs-th>
                            <vs-th>Nombre</vs-th>
                            <vs-th>Temporal</vs-th>
                        </template>

                        <template slot-scope="{data}">
                            <vs-tr :key="indextr" v-for="(tr, indextr) in data">

                                <vs-td :data="data[indextr].email">
                                    {{ data[indextr].email }}
                                </vs-td>

                                <vs-td :data="data[indextr].username">
                                    {{ data[indextr].name }}
                                </vs-td>

                                <vs-td :data="data[indextr].id">
                                    {{ data[indextr].website }}
                                </vs-td>

                                <vs-td :data="data[indextr].id">
                                    {{ data[indextr].id }}
                                </vs-td>

                            </vs-tr>
                        </template>
                    </vs-table>
                </div>
            </vx-card>
        </div>

        <div class="w-full md:w-1/5">
            <div style="padding:10px">
                <vx-card card-background="#4CAF50" title-color="#fff">
                    <div class="panel-body" style="color:white;text-align:center">
                        <i class="fa fa-users fa-5x" style="color:white"></i>
                        <h3 style="color:white">{{count_users}}</h3>
                    </div>
                    <div style="color:white;text-align:center">
                        Usuarios en Espera
                    </div>
                </vx-card>
                <br>
                <!-- @if (PrivilegeServices.privilegeValid(Model, "LISTEMERGENCY")) -->
                <vx-card card-background="#03A9F4">
                    <div class="panel-body" style="color:white;text-align:center">
                        <i class="fa fa-ambulance fa-5x" style="color:white"> </i>
                        <h3 style="color:white">{{count_emergency}}</h3>
                    </div>
                    <div style="color:white;text-align:center">
                        Emergencias En Proceso
                    </div>
                </vx-card>
            </div>

        </div>
    </div>
</vx-card>

<!-- <div class="">
        <vs-row vs-justify="center">
          <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="6">
            <vs-card>
              <div slot="header">
                <h3>
                  Hello world !
                </h3>
              </div>
              <div>
                <span>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</span>
              </div>
              <div slot="footer">
                <vs-row vs-justify="flex-end">
                  <vs-button type="gradient" color="danger" icon="favorite"></vs-button>
                  <vs-button color="primary" icon="turned_in_not"></vs-button>
                  <vs-button color="rgb(230,230,230)" color-text="rgb(50,50,50)" icon="settings"></vs-button>
                </vs-row>
              </div>
            </vs-card>
          </vs-col>
        </vs-row>
    </div> -->
</template>

<script>
export default {
    data() {
        return {
            emergencias: [],
            count_users: 0,
            count_emergency: 0
        }
    },
    components: {
        // flatPickr
    },
    // computed: {

    // },
    methods: {

    },
    created() {

    }
}
</script>

<style>
.button {
    margin: 0px !important;
}

.btnx {
    margin-left: 10px !important;
    background: red;
    border-radius: 5px 0px 0px 5px;
}

.btn-drop {
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: red;

}

.popup-generar {
    height: 100%
}
</style>
