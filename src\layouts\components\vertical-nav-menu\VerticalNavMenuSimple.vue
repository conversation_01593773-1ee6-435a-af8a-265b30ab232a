<template>
<div class="parentx">
    <div>
        <div>
            <router-link tag="div" to="/" style="cursor:pointer">
                <logo class="w-20 mr-2 ml-2 mt-2 fill-current text-primary" />
            </router-link>
            <div class="menu">
                <div v-for="(item, key) in menus" :key="key" class="menu-item" @mouseover="menuEstado(key,true)" @mouseleave="menuEstado(key,false)">
                    <div class="submenu" v-show="item.activo">
                        <b style="margin-bottom:15px"> <span style="font-family='Font Awesome 5 Free';font-size:20px;margin-right:10px" :class="[item.icon]" /> {{ item.name }}</b>
                        <br>
                        <br>
                        <div v-for="(sub, key) in item.submenu" :key="`sub_${key}`" class="submenus">
                            <router-link tabindex="-1" v-if="!sub.submenu" exact :to="sub.url" @click.native="item.activo = false">
                                <div>
                                    {{sub.name}}
                                </div>
                            </router-link>

                            <a v-else>
                                <div @mouseover="sub.activo = true" @mouseleave="sub.activo = false">
                                    <div class="submenu2" v-show="sub.activo">
                                        <b v-if="!sub.submenu">{{ sub.name }}</b>

                                        <div v-for="(sub, key) in sub.submenu" :key="`sub_${key}`" class="submenus">
                                            <router-link tabindex="-1" v-if="!sub.submenu" exact :to="sub.url" >
                                                <div>
                                                    {{sub.name}}
                                                </div>
                                            </router-link>
                                            <div v-else>
                                                <a>
                                                    <div>
                                                        <span style="font-family='Font Awesome 5 Free';font-size:18px;margin-right:1px;margin-left:-15px;font-size:13px" :class="[sub.icon]" />
                                                        {{sub.name}}
                                                    </div>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <span style="font-family='Font Awesome 5 Free';font-size:18px;margin-right:1px;margin-left:-15px;font-size:13px" :class="[sub.icon]" />
                                    {{sub.name}}
                                </div>
                            </a>
                        </div>
                    </div>
                    <span style="font-family='Font Awesome 5 Free';font-size:18px;margin-right:10px" :class="[item.icon]" />
                </div>
            </div>
        </div>
    </div>

    <div v-if="!isVerticalNavMenuActive" class="v-nav-menu-swipe-area" v-hammer:swipe="onSwipeAreaSwipe" />
</div>
</template>

<script>
// import VuePerfectScrollbar from 'vue-perfect-scrollbar'
// import VNavMenuGroup from './VerticalNavMenuGroup.vue'
// import VNavMenuItem from './VerticalNavMenuItem.vue'

import Logo from "../Logo.vue"

export default {
    name: 'v-nav-menu',
    components: {
        Logo
    },
    props: {
        logo: {
            type: String
        },
        openGroupHover: {
            type: Boolean,
            default: false
        },
        parent: {
            type: String
        },
        reduceNotRebound: {
            type: Boolean,
            default: true
        },
        navMenuItems: {
            type: Array,
            required: true
        },
        title: {
            type: String
        },
    },
    data: () => ({
        timer:null,
        clickNotClose: false, // disable close navMenu on outside click
        isMouseEnter: false,
        reduce: true, // determines if navMenu is reduce - component property
        showCloseButton: false, // show close button in smaller devices
        settings: { // perfectScrollbar settings
            maxScrollbarLength: 60,
            wheelSpeed: 1,
            swipeEasing: true
        },
        showShadowBottom: false,
        menus: []
    }),
    computed: {
        isVerticalNavMenuActive: {
            get() {
                return this.$store.state.isVerticalNavMenuActive
            },
            set(val) {
                this.$store.commit('TOGGLE_IS_VERTICAL_NAV_MENU_ACTIVE', val)
            }
        },
        layoutType() {
            return this.$store.state.mainLayoutType
        },
        reduceButton: {
            get() {
                return this.$store.state.reduceButton
            },
            set(val) {
                this.$store.commit('TOGGLE_REDUCE_BUTTON', val)
            }
        },
        isVerticalNavMenuReduced() {
            return Boolean(this.reduce && this.reduceButton)
        },
        verticalNavMenuItemsMin() {
            return this.$store.state.verticalNavMenuItemsMin
        },
        scrollbarTag() {
            return this.$store.getters.scrollbarTag
        },
        windowWidth() {
            return this.$store.state.windowWidth
        }
    },
    watch: {
        '$route'() {
            if (this.isVerticalNavMenuActive && this.showCloseButton) this.$store.commit('TOGGLE_IS_VERTICAL_NAV_MENU_ACTIVE', false)
        },
        // reduce(val) {
        //     const verticalNavMenuWidth = val ? "reduced" : this.$store.state.windowWidth < 1200 ? "no-nav-menu" : "default"
        //     this.$store.dispatch('updateVerticalNavMenuWidth', verticalNavMenuWidth)

        //     setTimeout(function() {
        //         window.dispatchEvent(new Event('resize'))
        //     }, 100)
        // },
        layoutType() {
            this.setVerticalNavMenuWidth()
        },
        reduceButton() {
            this.setVerticalNavMenuWidth()
        },
        windowWidth() {
            this.setVerticalNavMenuWidth()
        },
        navMenuItems() {
            this.menus = this.navMenuItems.map(m => {
                return {
                    ...m,
                    submenu: m.submenu.map(m => {
                        return {
                            ...m,
                            activo: false
                        }
                    }),
                    activo: false
                }
            })
        }
    },
    methods: {
        menuEstado(key,estado){
            const item = this.menus[key]
            clearTimeout(this.timer)
            if (estado == false){
                this.timer = setTimeout(()=>item.activo = estado,1000)
            }else{
                this.menus.map((m,index)=>{
                    if (index!=key) m.activo=false
                })
                item.activo = estado
            }
        },
        onMenuSwipe(event) {
            // Swipe Right
            if (event.direction === 4 && this.$vs.rtl) {
                if (this.isVerticalNavMenuActive && this.showCloseButton) this.isVerticalNavMenuActive = false
            }
            // Swipe Left
            else if (event.direction === 2 && !this.$vs.rtl) {
                if (this.isVerticalNavMenuActive && this.showCloseButton) this.isVerticalNavMenuActive = false
            }
        },
        onSwipeAreaSwipe(event) {

            // Swipe Right
            if (event.direction === 4 && !this.$vs.rtl) {
                if (!this.isVerticalNavMenuActive && this.showCloseButton) this.isVerticalNavMenuActive = true
            }
            // Swipe Left
            else if (event.direction === 2 && this.$vs.rtl) {
                if (!this.isVerticalNavMenuActive && this.showCloseButton) this.isVerticalNavMenuActive = true
            }
        },
        psSectionScroll() {
            const scroll_el = this.$refs.verticalNavMenuPs.$el || this.$refs.verticalNavMenuPs
            this.showShadowBottom = scroll_el.scrollTop > 0 ? true : false
        },
        // mouseEnter() {
        //     if (this.reduce) this.$store.commit('UPDATE_VERTICAL_NAV_MENU_ITEMS_MIN', false)
        //     this.isMouseEnter = true
        // },
        // mouseLeave() {
        //     if (this.reduce) this.$store.commit('UPDATE_VERTICAL_NAV_MENU_ITEMS_MIN', true)
        //     this.isMouseEnter = false;
        // },
        setVerticalNavMenuWidth() {

            if (this.windowWidth > 1200) {
                if (this.layoutType === 'vertical') {

                    // Set reduce
                    // this.reduce = this.reduceButton ? true : false

                    // Open NavMenu
                    this.$store.commit('TOGGLE_IS_VERTICAL_NAV_MENU_ACTIVE', true)

                    // Set Menu Items Only Icon Mode
                    const verticalNavMenuItemsMin = (this.reduceButton && !this.isMouseEnter) ? true : false
                    this.$store.commit('UPDATE_VERTICAL_NAV_MENU_ITEMS_MIN', verticalNavMenuItemsMin)

                    // Menu Action buttons
                    this.clickNotClose = true
                    this.showCloseButton = false

                    const verticalNavMenuWidth = this.isVerticalNavMenuReduced ? "reduced" : "default"
                    this.$store.dispatch('updateVerticalNavMenuWidth', verticalNavMenuWidth)

                    return
                }
            }

            // Close NavMenu
            this.$store.commit('TOGGLE_IS_VERTICAL_NAV_MENU_ACTIVE', false)

            // Reduce button
            if (this.reduceButton) this.reduce = false

            // Menu Action buttons
            this.showCloseButton = true
            this.clickNotClose = false

            // Update NavMenu Width
            this.$store.dispatch('updateVerticalNavMenuWidth', 'no-nav-menu')

            // Remove Only Icon in Menu
            this.$store.commit('UPDATE_VERTICAL_NAV_MENU_ITEMS_MIN', false)

        },
        toggleReduce(val) {
            this.reduceButton = val
            this.setVerticalNavMenuWidth()
        },
    },
    mounted() {
        this.setVerticalNavMenuWidth()
        this.menus = this.navMenuItems.map(m => {
            return {
                ...m,
                submenu: m.submenu.map(m => {
                    return {
                        ...m,
                        activo: false
                    }
                }),
                activo: false
            }
        })
    },
}
</script>

<style lang="scss">
// @import "@/assets/scss/vuexy/components/verticalNavMenu.scss";
</style><style scoped>
.parentx {
    /* background: red; */
    height: 100%;
    width: 80px;
    /* top: 10px; */
    position: fixed;
    padding: 5px;
    background-color: white;
    /* border-radius: 0 15px 15px 0; */
    z-index: 1002;
    box-shadow: 5px 2px 5px rgba(0, 0, 0, 0.1);
}

.menu-item {
    padding: 10px 0px;
    cursor: pointer;
    text-align: center;
    transition: all ease 0.2s;
    /* background-color:red; */
}

.menu-item:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.submenu,
.submenu2 {
    background: rgba(255, 255, 255, 1);
    /* backdrop-filter: blur(9px); */
    box-shadow: 5px 2px 5px rgba(0, 0, 0, 0.2);
    position: absolute;
    left: 55px;
    margin-top: -7px;
    padding: 15px;
    border-radius: 0 7px 7px 0;
    min-width: 270px;

}

.submenu2 {
    left: 190px !important;
    max-height: 500px;
    overflow: auto;
    border-radius: 7px;
}

.submenus {
    text-align: left;

}

.submenus>a>div {
    padding: 9px 20px;
    width: 100%;
    transition: all ease 0.2s;
}

.submenus>a:hover>div {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
}
</style>
