<template>
<div class="afiliados-solicitudes-container">

    <DxDataGrid :data-source="Solicitudes" v-bind="OptionsGridSolicitudes" @cell-prepared="onCellPrepared">
        <DxDataGridToolbar>
            <DxFormItem name="groupPanel" />
            <DxFormItem name="searchPanel" />
            <DxFormItem name="exportButton" />
            <DxFormItem location="after" template="opcionesTemplate" />
        </DxDataGridToolbar>
        <template #opcionesTemplate>
            <ExpedienteGridToolBar :visible="true" :showItems="['refresh']" :pdfExportItems="[]" @add="()=>dataGrid.addRow()" @refresh="Cargar" />
        </template>
    </DxDataGrid>

</div>
</template>

<script>

import {
    DefaulGridOptions,
    _confMoneda,
    _dateColumnConf
} from './data.js'
const Summary = {
    totalItems: [

        {
            column: 'ValorAutorizado',
            summaryType: 'sum',
            displayFormat: "{0}",
            valueFormat: {
                type: 'currency',
                precision: 2,
                currency: 'GTQ'
            },
        },
        {
            column: 'ValorDeclinado',
            summaryType: 'sum',
            displayFormat: "{0}",
            valueFormat: {
                type: 'currency',
                precision: 2,
                currency: 'GTQ'
            },
        },
        {
            column: 'IdSolicitud',
            summaryType: 'count',
            customizeText: (x) => 'Total de registros: '.concat(x.value)
        },

    ]
}

export default {
    name: 'AfiliadosSolictudes',
    components: {
        ExpedienteGridToolBar: () => import('../EXPEDIENTE/ExpedienteGridToolBar.vue')
    },
    data() {
        return {
            Solicitudes: null,
            OptionsGridSolicitudes: {
                ...DefaulGridOptions,
                summary: {
                    ...Summary
                },
                wordWrapEnabled: false,
                
                export: {
                    enabled: true,
                    formats: ['xlsx'],
                },
                columns: [{
                        caption: 'Datos de solicitud de estudios',
                        columns: [

                            {
                                caption: 'No.',
                                dataField: 'IdSolicitud',
                                sortOrder: 'desc',
                                sortIndex: 0,
                                fixed: true,
                                width: 100,
                            },
                            {
                                caption: 'Estado',
                                dataField: 'StatusAnalista',
                                sortOrder: 'asc',
                                sortIndex: 1,
                                width: 130,
                            },

                            {
                                caption: 'Médico',
                                dataField: 'NombreMedico',
                            },

                            {
                                caption: 'Fecha',
                                dataField: 'FechaIngreso',
                                ..._dateColumnConf,
                                sortOrder: 'asc',
                                sortIndex: 3,
                            },
                            {
                                caption: 'Nombre Estudio',
                                dataField: 'NombreProducto',
                            },
                            {
                                caption: 'Categoría',
                                dataField: 'NombreCategoria',
                            },
                            {
                                dataField: 'Precio',
                                ..._confMoneda,
                                allowHiding: false,
                            },
                        ],
                    },
                    {
                        caption: 'Datos de autorización',
                        columns: [

                            {
                                dataField: 'SerieAdmision',
                                caption: 'Serie Admisión',
                            },
                            {
                                caption: 'Admisión',
                                dataField: 'Admision',
                            },
                            {
                                caption: 'Fecha Admisión',
                                dataField: 'FechaAdmision',
                                ..._dateColumnConf,
                                format: 'dd/MM/yyyy',
                            },

                            {
                                dataField: 'Notas',
                                allowSorting: false,
                                allowGrouping: false,
                                width: 300,
                            },
                            {
                                caption: 'Observación',
                                dataField: 'Observacion',
                                allowSorting: false,
                                allowGrouping: false,
                                width: 300,
                            },

                            {
                                caption: 'Q. Autorizado',
                                dataField: 'ValorAutorizado',
                                ..._confMoneda,
                                //ProductoPrecio es de catalogo de productos precio mientras que precio es el que esta registrado en el detalle da la solicitud
                                calculateCellValue: (row) => row.StatusAnalista == 'Autorizado' ? row.ProductoPrecio : 0,
                                allowHiding: false,
                            },
                            {
                                caption: 'Q. Declinado',
                                dataField: 'ValorDeclinado',
                                ..._confMoneda,
                                calculateCellValue: (row) => row.StatusAnalista != 'Autorizado' ? row.ProductoPrecio : 0,
                                allowHiding: false,
                            },
                            {
                                caption: 'Usuario Ingresa',
                                dataField: 'UsuarioIngresoEnc',
                            },

                            {
                                caption: 'Usuario Autoriza',
                                dataField: 'UsuarioAutorizaEnc',
                            },
                            {
                                caption: 'Usuario Anula',
                                dataField: 'UsuarioAnulacionEnc',
                            },
                            
                        ],
                    }
                ]
            },

        }
    },
    props: {
        IdAfiliado: String
    },
    methods: {

        Cargar() {
            if(this.IdAfiliado)
                this.axios.post("/app/v1_afiliados/SolucitudesAfiliado", {
                        IdAfiliado: this.IdAfiliado
                    })
                    .then((resp) => {
                        this.Solicitudes = resp.data.map(x=> {
                            x.StatusAnalista = x.StatusAnalista == 'A'? 'Autorizado': 'Declinado'
                            
                            return x
                        })
                    })
        },

        onCellPrepared(e) {
            if (e.rowType === 'data' && e.row.data.StatusAnalista === 'Declinado') {
                e.cellElement.classList.add("declinado");
            }
        }
    },
    mounted() {
        this.Cargar()
    },
    watch: {
        'IdAfiliado'() {
            this.Cargar()
        }
    },
    computed: {

    },

}
</script>

<style>
.declinado {
    color: red;
}
</style>
