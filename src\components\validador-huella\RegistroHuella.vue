<template>
    <vx-card title="Registro Huella">
        <div v-if="isToken">
            <form>
                <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-2">
                        <ValidationProvider>
                            <vs-radio class="radioReference" v-model="referencia" vs-value="1" disabled>Corporativo</vs-radio>
                            <vs-radio class="radioReference" v-model="referencia" vs-value="2" disabled>Ajenos</vs-radio>
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-2">
                        <ValidationProvider name="coorporativo" rules="required|numero_entero" class="required" v-slot="{ errors }">
                            <vs-input :label-placeholder="labelCodigo" class="d-inline-block" v-model="coorporativo" :disabled="desactivar" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"/>
                        </ValidationProvider>      
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-2">
                        <ValidationProvider name="selectDedo" rules="required" class="required">
                            <multiselect v-model="dedo" :options="selectDedo" :disabled="false" :allow-empty="false" placeholder="Seleccione  Dedo" track-by="Nombre" label="Nombre"></multiselect>
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-2">
                        <vs-textarea label="Estado" cols="30" rows="3" v-model="estado" disabled></vs-textarea>
                    </div>
                    <div class="flex flex-wrap">
                        <vs-button @click="handleSubmit(generarHuella)" :disabled="invalid" color="primary" class="btn btn-primary mx-3 my-3" icon-pack="fas" icon="fa-fingerprint">
                            Asociar Huella
                        </vs-button>
                        <vs-button @click="handleSubmit(guardarHuella)" :disabled="invalid" color="success" class="btn btn-primary mx-3 my-3" icon-pack="fas" icon="fa-save">
                            Guardar
                        </vs-button>
                    </div>
                    <div>
                        <img v-if="!imgHuella" src="@/assets/images/others/finger.png" alt="">
                        <img v-if="imgHuella" :src="imgHuella" alt="">
                    </div>
                </ValidationObserver>
            </form>
        </div>
    </vx-card>
</template>
<script>
    import Multiselect from "vue-multiselect"
    import "vue-multiselect/dist/vue-multiselect.min.css"
    import { fechaActualSistema, isEmptyObject, getIpLocal } from './funciones/funciones'
    import moment from 'moment'

    export default {
        data(){
            return{
                coorporativo: null,
                labelCodigo:null,
                imgHuella: null,
                referencia: 1,
                estado: null,
                dedo: null,
                isToken: false,
                isCapturar: false,
                isValidaHuella: false,
                selectDedo: [
                    {
                        Id: 'Indice D',
                        Nombre: 'Indice Derecho'
                    },
                    {
                        Id: 'Indice I',
                        Nombre: 'Indice Izquierdo'
                    }
                ],
                desactivar: true,
                IpLocal: ''
            }
        },
        props: {
            tipoHuella: {
                type: String,
                default: "1"
            },
            corporativoBusqueda: {
                type: String
            },
            desctivarBusqueda: {
                type: Boolean,
                default: true
            }
        },
        components: {
            Multiselect
        },
        methods: {
            async generarHuella(){
                this.IpLocal = getIpLocal()
                if(this.referencia == 1){
                    await this.axios.post('/app/huella/ValidaUsuarioExiste', {
                        Opcion: 'C',
                        SubOpcion: '9',
                        Hospital: '0',
                        Corporativo: this.coorporativo
                    })
                    .then( async resp => {
                        if(resp.data.json[0].ContadorCorporativo == 0)
                        {
                            this.$vs.dialog({
                                color: 'danger',
                                title: 'Registro Huella',
                                text: `El corporativo ${ this.coorporativo} al cual desea asociar una huella no existe o esta inactivo..`,
                                acceptText: 'Aceptar'
                            })
                        }else{
                            if(!this.isCapturar){
                                await this.axios.post('/app/huella/LimpiarTemporalHuella', {
                                    Opcion: 'E',
                                    SubOpcion: "1",
                                    Hospital: "0",
                                    IpLocal: this.IpLocal
                                })
                                .then( async () => {
                                    await this.axios.post('/app/huella/Sensor', {
                                        Opcion: 'I',
                                        SubOpcion: '1',
                                        Hospital: '0',
                                        IpLocal: this.IpLocal,
                                        Corporativo: this.coorporativo
                                    })
                                    .then( () => {
                                        this.isCapturar = true
                                    })
                                })
                                await this.push()
                            }
                                  
                        }
                    })
                    
                }else{
                    await this.axios.post('/app/huella/ValidaUsuarioExiste', {
                        Opcion: 'C',
                        SubOpcion: '12',
                        Hospital: '0',
                        Corporativo: this.coorporativo
                    })
                    .then( async resp => {
                        if(resp.data.json[0].ContadorCorporativo == 0){
                            await this.$vs.dialog({
                                color: 'danger',
                                title: 'Registro Huella',
                                text: `El Codigo ${ this.coorporativo} al cual desea asociar una huella no existe o esta inactivo..`,
                                acceptText: 'Aceptar'
                            })
                        }else{
                            if(!this.isCapturar){
                                await this.axios.post('/app/huella/LimpiarTemporalHuella', {
                                    Opcion: 'E',
                                    SubOpcion: "1",
                                    Hospital: "0",
                                    IpLocal: this.IpLocal
                                })
                                .then( async () => {
                                    await this.axios.post('/app/huella/Sensor', {
                                        Opcion: 'I',
                                        SubOpcion: '1',
                                        Hospital: '0',
                                        IpLocal: this.IpLocal,
                                        Corporativo: this.coorporativo
                                    })
                                    .then( () => {
                                        this.isCapturar = true
                                    })
                                }) 
                                await this.push()
                            }

                            
                        }
                    })
                }
            },
            async push(){
                let fechaBD = 0
                const fechaActual = fechaActualSistema()

                while( fechaBD <= fechaActual){
                    await this.axios.post('/app/huella/FechaLector', {
                        Opcion: 'C',
                        SubOpcion: '1',
                        Hospital: '0',
                        IpLocal: this.IpLocal
                    })
                    .then( resp => {
                        !isEmptyObject(resp.data.json) ? fechaBD = moment(resp.data.json[0].FechaCreacion, "DD/MM/YYYY hh:mm:ss").toDate().getTime() : 0
                    })

                    new Promise(r => setTimeout(r, 1000))
                }

                await this.axios.post('/app/huella/FechaLector', {
                    Opcion: 'C',
                    SubOpcion: '5',
                    Hospital: '0',
                    IpLocal: this.IpLocal 
                })
                .then( resp => {
                    if(!isEmptyObject(resp.data.json)){
                        const resultado = resp.data.json[0]
                        resultado.ImagenHuella ? this.imgHuella = `data:image/png;base64,${resultado.ImagenHuella}` : null
                        this.estado = resultado.Texto + " - " + resultado.StatusPlantilla

                        if(resultado.StatusPlantilla == 'Muestras Restantes: 0'){
                            this.isCapturar = false
                        }
                    }

                    
                })

                if(this.isCapturar){
                    setTimeout( () => {
                        this.push()
                    }, 1000)
                }
            },
            guardarHuella(){
                if( this.referencia == 1 ){
                    this.axios.post('/app/huella/ValidaHuella', {
                        Opcion: 'C',
                        SubOpcion: '8',
                        Hospital: '0',
                        Corporativo: this.coorporativo,
                        NombreDedo: this.dedo.Id,
                        Tipo: this.referencia
                    })
                    .then( () => {
                        // if(isEmptyObject(resp.data.json)){
                            if(!this.imgHuella){
                                this.$vs.notify({
                                    color: 'danger',
                                    title: 'Captura de Huella',
                                    text: 'Aun no se a capturado el dedo que desea almacenar',
                                    position:'bottom-center'
                                })
                            }else{
                                this.axios.post('/app/huella/GuardarHuella', {
                                    Opcion: 'I',
                                    SubOpcion: '4',
                                    Hospital: '0',
                                    Corporativo: this.coorporativo,
                                    NombreDedo: this.dedo.Id,
                                    Estado: 'A',
                                    Tipo: this.referencia,
                                    IpLocal: this.IpLocal
                                })
                                .then( () => {
                                    this.axios.post('/app/huella/LimpiarTemporalHuella', {
                                        Opcion: 'E',
                                        SubOpcion: "1",
                                        Hospital: "0",
                                        IpLocal: this.IpLocal
                                    })
                                    .then( () => {
                                        this.estado = null
                                        this.dedo = null
                                        this.imgHuella = null
                                        this.$emit("getVentana", false)
                                        this.$vs.notify({
                                            color: 'success',
                                            title: 'Captura de Huella',
                                            text: 'Se almaceno correctamente el registro',
                                            position:'bottom-center'
                                        })
                                    })
                                })
                            }
                        // }else{
                        //     this.$vs.dialog({
                        //         color: 'danger',
                        //         title: 'Registro Huella',
                        //         text: `El dedo ${ this.dedo.Nombre } ya tiene un registro para el corporativo ${ this.coorporativo }!!!`,
                        //         acceptText: 'Aceptar'
                        //     })
                        // }
                    })
                }else{
                    this.axios.post('/app/huella/ValidaHuella', {
                        Opcion: 'C',
                        SubOpcion: '8',
                        Hospital: '0',
                        Corporativo: this.coorporativo,
                        NombreDedo: this.dedo.Id,
                        Tipo: this.referencia
                    })
                    .then( () => {
                        // if(isEmptyObject(resp.data.json)){
                            if(!this.imgHuella){
                                this.$vs.notify({
                                    color: 'danger',
                                    title: 'Captura de Huella',
                                    text: 'Aun no se a capturado el dedo que desea almacenar',
                                    position:'bottom-center'
                                })
                            }else{
                                this.axios.post('/app/huella/GuardarHuella', {
                                    Opcion: 'I',
                                    SubOpcion: '4',
                                    Hospital: '0',
                                    Corporativo: this.coorporativo,
                                    NombreDedo: this.dedo.Id,
                                    Estado: 'A',
                                    Tipo: this.referencia,
                                    IpLocal: this.IpLocal
                                })
                                .then( () => {
                                    this.axios.post('/app/huella/LimpiarTemporalHuella', {
                                        Opcion: 'E',
                                        SubOpcion: "1",
                                        Hospital: "0",
                                        IpLocal: this.IpLocal
                                    })
                                    .then( () => {
                                        this.estado = null
                                        this.dedo = null
                                        this.imgHuella = null
                                        this.$vs.notify({
                                            color: 'success',
                                            title: 'Captura de Huella',
                                            text: 'Se almaceno correctamente el registro',
                                            position:'bottom-center'
                                        })
                                    })
                                })
                            }
                        // }else{
                        //     this.$vs.dialog({
                        //         color: 'danger',
                        //         title: 'Registro Huella',
                        //         text: `El dedo ${ this.dedo.Nombre } ya tiene un registro para el codigo ${ this.coorporativo }!!!`,
                        //         acceptText: 'Aceptar'
                        //     })
                        // }
                    })
                }
                
                             
            },
            limpiarCampos(){
                this.coorporativo = ''
                this.imgHuella = ''
                this.referencia = 1
                this.estado = ''
                this.dedo = ''
            },
            DesactivarSensor(){
                this.isCapturar = false
                this.axios.post('/app/huella/LimpiarTemporalHuella', {
                    Opcion: 'E',
                    SubOpcion: "1",
                    Hospital: "0",
                    IpLocal: this.IpLocal
                })
            }
        },
        activated(){
            this.referencia = this.tipoHuella
            this.coorporativo = this.corporativoBusqueda
            this.desactivar = this.desctivarBusqueda
            this.isToken = true
            if(this.tipoHuella == '1'){
                this.labelCodigo = 'Corporativo'
            }else{
                this.labelCodigo = 'Codigo'
            }
        },
        deactivated(){
            this.isCapturar = false
                this.axios.post('/app/huella/LimpiarTemporalHuella', {
                    Opcion: 'E',
                    SubOpcion: "1",
                    Hospital: "0",
                    IpLocal: this.IpLocal
                })
        },
        destroyed(){
            this.isCapturar = false
            this.axios.post('/app/huella/LimpiarTemporalHuella', {
                Opcion: 'E',
                SubOpcion: "1",
                Hospital: "0",
                IpLocal: this.IpLocal
            })
            this.limpiarCampos()
        },
        created(){
            window.onbeforeunload = this.DesactivarSensor
            this.referencia = this.tipoHuella
            this.coorporativo = this.corporativoBusqueda
            this.desactivar = this.desctivarBusqueda
            this.isToken = true
            if(this.tipoHuella == '1'){
                this.labelCodigo = 'Corporativo'
            }else{
                this.labelCodigo = 'Codigo'
            }
        },
        watch: {
            corporativoBusqueda(value) {
                this.coorporativo = value
            },
            desctivarBusqueda(value) {
                this.desactivar = value
            },
            tipoHuella(value) {
                this.referencia = value
                if(value == '1'){
                    this.labelCodigo = 'Corporativo'
                }else{
                    this.labelCodigo = 'Codigo'
                }
            }
        }
    }
</script>
<style lang="scss" scoped>
    img {
        width: 200px;
        box-shadow: 0px 5px 10px rgba($color: #000000, $alpha: 0.2);
    }
    .radioReference{
        padding: 5px 5px 5px 5px;
    }
</style>
