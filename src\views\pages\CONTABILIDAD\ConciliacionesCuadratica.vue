<template>
  <div>
      <form @submit="handleSubmit">
          <DxForm :ref="formTransaccion" :form-data.sync="formulario" label-mode="floating" style="margin: 5px">
              <DxFormGroupItem :col-count="2">
                  <DxFormGroupItem>
                      <DxFormItem data-field="Cuenta" editor-type="dxSelectBox" :editor-options="{
                    width: 'auto',
                    searchEnabled: true,
                    items: cuentas,
                    displayExpr: 'Valor',
                    valueExpr: 'Codigo',
                    onValueChanged: AsignarSiguienteCheque,
                  }" :validationRules="[{ type: 'required' }]" />
                  
                  <DxFormItem template="Alerta" />

                   <DxFormItem v-if="datos && datos.length > 0" template="Botones" />

                  </DxFormGroupItem>
                  <DxFormGroupItem>
                      <DxFormItem template="Transac" />
                  </DxFormGroupItem>
              </DxFormGroupItem>
              <template #Transac="{}">
                  <div v-if="mostrar && datos && datos.length > 0" class="pb-2">
                    <DxDataGrid  class="solo-esta-tabla-cuadratico"  id="dataGrid" ref="dataGrid" :data-source="datos" :show-borders="true" :column-auto-width="true" :focused-row-enabled="true" :focused-row-key="focusedRowKey" key-expr="Codigo" @row-click="SeleccionarData">
                      <DxDataGridSelection mode="single" />
                      <DxDataGridColumn data-field="descripcion" caption="Periodo" />
                      <DxDataGridColumn data-field="Fecha" data-type="date" caption="Fecha" />
                      <DxDataGridColumn data-field="Codigo" caption="Conciliación" />
                      <DxDataGridPaging :page-size="1" />
                  </DxDataGrid>

                  </div>
              </template>

              <template #Botones="{}">
                  <div>
                      <div style="display: flex; justify-content: center; align-items: center; gap: 10px; margin-top: 5px;">
                          <DxButton id="saveButtonCuadratica" :disabled="focusedRowIndex === 0" icon="fas fa-step-backward" type="normal" @click="irAlPrimero" />
                          <DxButton id="saveButtonDosCuadratica" :disabled="focusedRowIndex === 0" icon="fas fa-caret-left" type="normal" @click="anteriorRegistro" />
                          <DxButton id="saveButtonDosCuadratica" :disabled="focusedRowIndex === datos.length - 1" icon="fas fa-caret-right" type="normal" @click="siguienteRegistro" />
                          <DxButton id="saveButtonCuadratica" icon="fas fa-step-forward" :disabled="focusedRowIndex === datos.length - 1" type="normal" @click="irAlUltimo" />
                      </div>
                  </div>
              </template>

              <template  #Alerta="{}">
                <div>
                  <vs-alert color="danger"  v-if="!datos || datos.length === 0" active="true" style="text-align: center;">
                    <p> Sin datos</p>
                  </vs-alert>
                </div>
              </template>

          </DxForm>
      </form>

      <div v-if="mostrar && datos && datos.length > 0">
          <div style="display: flex; justify-content: flex-end">
              <table class="tablaConciliacionesCuadraticas">
                  <thead style="border-color: black !important;">
                      <tr>
                          <th></th>
                          <th style="
                            font-size: medium;
                            text-align: center;
                            background-color: teal;
                            color: white;
                          ">
                              LIBROS
                          </th>
                          <th style="
                            font-size: medium;
                            text-align: center;
                            background-color: navy;
                            color: white;
                          ">
                              BANCOS
                          </th>
                      </tr>
                  </thead>
                  <tbody>
                      <tr>
                          <td><strong>Saldo Final</strong></td>
                          <td style="color: teal;" >{{ formulario.SaldoRestado }}</td>
                          <td style="color: navy;">{{ formulario.SaldoBanco }}</td>
                      </tr>
                      <tr>
                          <td><strong>Cheques en Circulación</strong></td>
                          <td></td>
                          <td style="color: navy;">
                            {{ formulario.ChequesCirculacion }}
                          </td>
                      </tr>
                      <tr>
                          <td><strong>Depósitos en Tránsito</strong></td>
                          <td></td>
                          <td style="color: navy;">{{ formulario.DepositosTransito }}</td>
                      </tr>
                      <tr>
                          <td><strong>Cheques Vencidos</strong></td>
                          <td></td>
                          <td>{{ formulario.ChequesVencidos }}</td>
                      </tr>
                  </tbody>
              </table>
          </div>


          <DxDataGrid :show-borders="true" :data-source="Ajustes" width="100%" @saving="onSaving" @editing-start="onCellFocused" @row-removing="onRowRemoving">
              <DxDataGridColumn data-field="Linea" caption="Línea" data-type="number" :allow-editing="false" />
              <DxDataGridColumn data-field="Concepto" width="60%" data-type="string" caption="Concepto" />
              <DxDataGridColumn data-field="MontoLibros" data-type="number" caption="Libros" />
              <DxDataGridColumn data-field="MontoBanco" data-type="number" caption="Banco" />
              <DxDataGridEditing :allow-updating="true" :allow-adding="true" :allow-deleting="true" :use-icons="true" mode="row"/>
              <DxDataGridSummary>
                  <DxDataGridTotalItem column="MontoLibros" summary-type="sum" :customize-text="customizeTextValores" :value-format="{ type: 'fixedPoint', precision: 2 }" />
                  <DxDataGridTotalItem column="MontoBanco" summary-type="sum" :customize-text="customizeTextValoresBancos" :value-format="{ type: 'fixedPoint', precision: 2 }" />
              </DxDataGridSummary>
          </DxDataGrid>

          <vs-alert v-if="alertActive" color="rgb(231, 154, 23)" active="true">
              {{ alertMessage }}
          </vs-alert>
      </div>
  </div>
</template>
<script>
const formTransaccion = "formTransaccion";

export default {
  data() {
    return {
      formulario: {
        Cuenta: null,
        Nombre_Cuenta: null,
        Fecha: null,
        MesyAño: null,
        Cheques: null,
        Periodo: null,
        Funcion: null,
        Conciliacion: null,

        SaldoBanco: null,
        SaldoFinal: null,
        ChequesVencidos: null,
        DepositosTransito: null,
        ChequesCirculacion: null,
        SaldoRestado: 0,

        Descripcion: null,
        
        visible:true,

        listaLibros: {
          SaldoFinal: null,
          Circulacion: null,
          Depositos: null,
          Vencidos: null,
        },

        listaBancos: {
          SaldoFinal: null,
          Circulacion: null,
          Depositos: null,
          Vencidos: null,
        },
      },

      funciones: [
        {
          id: "false",
          text: "Activar",
        },
        {
          id: "true",
          text: "Desactivar",
        },
      ],
      
      focusedRowIndex: 0,  // Índice de la fila seleccionada
      focusedRowKey: 1,  // Clave primaria (Código) de la fila seleccionada
      mostrar: false,
      filaSeleccionada: null,
      formTransaccion: null,
      alertActive: false,
      alertMessage: "",
      // Arays
      cuentas: [],
      datos: [],
      Ajustes: [],
      Conceptop: [],
    };
  },
  computed: {
    formTransformAnticipoInstanceaccion: function () {
      return this.$refs[formTransaccion].instance;
    },
  },
  methods: {
    handleSubmit(e) {
      e.preventDefault();
    },

    CargarCuentas(asignar) {
      this.axios
        .post("/app/v1_bancos/Cuentas", {
          Opcion: 1,
        }, { skipLoading: true })
        .then((resp) => {
          this.cuentas = resp.data.json;

          if (asignar) {
            this.AsignarBanco(this.cuentas[0]);
          }
        });
    },

    AsignarSiguienteCheque(e) {
      this.datos = [];
      this.Ajustes = [];
      this.Conceptop = [];
      this.formulario.SaldoBanco = 0;
      this.formulario.SaldoFinal = 0;
      this.formulario.ChequesVencidos = 0;
      this.formulario.DepositosTransito = 0;
      this.formulario.ChequesCirculacion = 0;
      this.formulario.SaldoRestado = 0;
      this.formulario.Descripcion = null;

      let cuenta = this.cuentas.find((x) => x.Codigo == e.value);
      this.AsignarBanco(cuenta);
      this.CargarDatosTable(cuenta);

    },

    AsignarBanco(e) {
      this.formulario.Cuenta = e.Codigo;
    },

    CargarDatosTable(cuenta) {
      this.axios
        .post("/app/v1_bancos/ConsultaIngreso", {
          Cuenta: cuenta.Codigo,
          Opcion: 3,
          SubOpcion: 1,
        },{ skipLoading: true })
        .then((resp) => {
          // Asignar los datos recibidos a la variable 'datos'
          this.datos = resp.data.json;

          // Mapear la respuesta a 'formulario'
          this.$map({
            objeto: this.formulario,
            respuestaAxios: resp,
          });

          // Verificar si hay datos y seleccionar la primera fila
          if (this.datos && this.datos.length > 0) {
            this.focusedRowKey = this.datos[0].Codigo; // Establece el 'focusedRowKey' al primer 'Codigo'

            // Llama a 'SeleccionarData' con la primera fila
            this.SeleccionarData({ data: this.datos[0] });
          }
        })
    },


    SeleccionarData(e) {
      const rowIndex = this.datos.findIndex((item) => item.Codigo === e.data.Codigo);
      if (rowIndex !== -1) {
        this.focusedRowIndex = rowIndex;
        this.focusedRowKey = e.data.Codigo;
      }
      this.filaSeleccionada = e.data;
      this.mostrar = true;
      this.CargarAjustes(e);
    },

    CargarAjustes() {
      this.axios
        .post("/app/v1_bancos/ConciliacionesAjustes", {
          Codigo: this.filaSeleccionada.Codigo,
          Opcion: 3,
          SubOpcion: 2,
        }, { skipLoading: true })
        .then((resp) => {
          if (resp.data.codigo == "0") {
            this.Ajustes = resp.data.json;
          }
        });

      this.axios
        .post("/app/v1_bancos/InfoPeriodo", {
          Periodo:this.filaSeleccionada.Periodo,
          Opcion: 4,
        }, { skipLoading: true })
        .then((resp) => {
          if (resp.data.json) {
            this.$map({
              objeto: this.formulario,
              respuestaAxios: resp,
            });
          }
        });

      this.axios
        .post("/app/v1_bancos/ConsultaConcepto", {
          Cuenta: this.formulario.Cuenta,
          Periodo: this.filaSeleccionada.Periodo,
          Opcion: 3,
          SubOpcion: 3,
        }, { skipLoading: true })
        .then((resp) => {
          if (resp.data.json) {
            this.$map({
              objeto: this.formulario,
              respuestaAxios: resp,
            });
          }
          this.Conceptop = resp.data.json;
        });
    },

    onSaving(e) {
      let cambios = e.changes;
      cambios.forEach((cambio) => {
        if (cambio.type === "insert") {
          let nuevaLinea =
            this.Ajustes.length > 0
              ? Math.max(...this.Ajustes.map((item) => item.Linea)) + 1
              : 1;
          cambio.data.Linea = nuevaLinea;
        } else if (cambio.type === "update") {
          let filaOriginal = this.Ajustes.find(
            (item) => item.Linea === cambio.key.Linea
          );
          if (filaOriginal) {
            cambio.data = { ...filaOriginal, ...cambio.data };
          }
        }
        this.guardarCambio(cambio.data);
      });
    },

    guardarCambio(cambio) {
      this.axios
        .post("/app/v1_bancos/InsertaAjustes", {
          Opcion: 3,
          SubOpcion: 4,
          Conciliacion: this.filaSeleccionada.Codigo,
          Linea: cambio.Linea,
          Concepto: cambio.Concepto,
          MontoLibros: cambio.MontoLibros,
          MontoBanco: cambio.MontoBanco,
        }, { skipLoading: true })
        .then((resp) => {
          if (resp.data.codigo === 0) {
            this.$vs.notify({
              title: "Ajuste Guardado",
              iconPack: "feather",
              icon: "icon-alert-circle",
              color: "success",
            });
            this.CargarAjustes(); 
          }
        });
    },

    onCellFocused(e) {
      let field = e.column.dataField;

      this.alertActive = true; // Activa el alert

      // Determinar el mensaje según la columna
      switch (field) {
        case "Concepto":
          this.alertMessage = "C = Errores en Libros(Contabilidad), B = Errores del Estado de Cuenta del Banco.";
          break;
        case "MontoLibros":
          this.alertMessage = "Explicación para el Ajuste.";
          break;
        case "MontoBanco":
          this.alertMessage = "Utilice valores positivos y negativos que afectarán el saldo según el tipo seleccionado.";
          break;
        default:
          this.alertMessage = "Estás en una columna desconocida.";
      }
    },

    customizeTextValores(cellInfo) {
      if (cellInfo.value !== null && !isNaN(cellInfo.value)) {
        let saldoRestado = this.formulario.SaldoRestado;
        if (typeof saldoRestado === "number") {
          saldoRestado = saldoRestado.toString();
        }
        saldoRestado = saldoRestado.replace(/,/g, ""); 

        let valorSaldoRestado = parseFloat(saldoRestado) || 0;

        let valorCellInfo = cellInfo.value;
        if (typeof valorCellInfo === "number") {
          valorCellInfo = valorCellInfo.toString();
        }
        valorCellInfo = valorCellInfo.replace(/,/g, ""); 

        valorCellInfo = parseFloat(valorCellInfo) || 0; 

        let valorConSaldo = valorSaldoRestado + valorCellInfo;

        // Aquí se agrega el formato con comas para los miles
        return "Q. " + valorConSaldo.toLocaleString('es-GT', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
      }
      return null;
  },


customizeTextValoresBancos(cellInfo) {
  // Verificar si el valor en cellInfo es un número válido
  if (cellInfo.value !== null && !isNaN(cellInfo.value)) {

    // Asegurarnos de que ChequesCirculacion y SaldoBanco sean números válidos
    // Convertimos a cadena antes de reemplazar las comas
    let saldoBanco = parseFloat(String(this.formulario.SaldoBanco).replace(/,/g, ''));
    let chequesCirculacion = parseFloat(String(this.formulario.ChequesCirculacion).replace(/,/g, ''));

    let saldoRestado = saldoBanco - chequesCirculacion;

    // Asegurarnos de que saldoRestado sea un número
    if (isNaN(saldoRestado)) {
      saldoRestado = 0; // Si la resta no es válida, establecemos en 0
    }

    // Convertir saldoRestado a cadena y eliminar comas si las hubiera
    saldoRestado = saldoRestado.toString().replace(/,/g, "");

    // Convertir saldoRestado de nuevo a un número flotante, por si hay algún formato extraño
    saldoRestado = parseFloat(saldoRestado) || 0;

    // Obtener el valor de cellInfo y eliminar comas
    let valorCellInfo = parseFloat(cellInfo.value.toString().replace(/,/g, "") || 0);

    // Sumar saldoRestado y valorCellInfo
    let valorConSaldo = saldoRestado + valorCellInfo;

    // Devolver el valor formateado con comas para los miles
    return "Q. " + valorConSaldo.toLocaleString('es-GT', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
  }

  // Si cellInfo no es válido, retornar null
  return null;
},


    obtenerDataGrid() {
      return this.$refs.dataGrid?.instance; // Obtiene la instancia del DxDataGrid
    },

    actualizarSeleccion() {
      const dataGrid = this.obtenerDataGrid();
      if (dataGrid) {
        const rowKey = this.datos[this.focusedRowIndex]?.Codigo;
        if (rowKey !== undefined) {
          this.focusedRowKey = rowKey;
          dataGrid.option("focusedRowKey", rowKey);
          dataGrid.navigateToRow(rowKey);
        }
      }
      // 🔥 Ejecuta manualmente el row-click para actualizar la selección
      this.SeleccionarData({ data: this.datos[this.focusedRowIndex] });
    },

     // Acción personalizada para la eliminación de una fila con SweetAlert2
     onRowRemoving(e) {
        if (!e.data) {

            return;
        }

        const rowData = e.data;

        this.axios
          .post("/app/v1_bancos/RemoveData", {
            Opcion: 3,
            SubOpcion: 5,
            Conciliacion: this.filaSeleccionada.Codigo,
            Linea: rowData.Linea,
            Concepto: rowData.Concepto,
            MontoLibros: rowData.MontoLibros,
            MontoBanco: rowData.MontoBanco,
          }, { skipLoading: true })
          .then((resp) => {
            if (resp.data.codigo === 0) {
              this.$vs.notify({
                title: "Ajuste Eliminado",
                iconPack: "feather",
                icon: "icon-alert-circle",
                color: "success",
              });
              this.CargarAjustes();
            }
          })
          .catch(() => {
          });
    },

    irAlPrimero() {
      this.focusedRowIndex = 0;
      this.actualizarSeleccion();
    },
    anteriorRegistro() {
      if (this.focusedRowIndex > 0) {
        this.focusedRowIndex--;
        this.actualizarSeleccion();
      }
    },
    siguienteRegistro() {
      if (this.focusedRowIndex < this.datos.length - 1) {
        this.focusedRowIndex++;
        this.actualizarSeleccion();
      }
    },
    irAlUltimo() {
      this.focusedRowIndex = this.datos.length - 1;
      this.actualizarSeleccion();
    },
  },

  beforeMount() {
    this.CargarCuentas(true);
  },
};
</script>

<style>
.tablaConciliacionesCuadraticas {
  width: 50% !important;
  border-collapse: collapse;
  background: white;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

th {
  text-align: left;
  padding: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

tbody tr:last-child {
  border-bottom: none;
}

td {
  padding: 12px;
}

td:nth-child(2),
td:nth-child(3) {
  text-align: right;
  font-weight: bold;
}

tbody tr:hover {
  background: #f1f1f1;
  transition: 0.3s;
}



#saveButtonCuadratica .dx-icon {
    font-size: 17px;
    color: black;

}

#saveButtonCuadratica{
    width: 60px !important;
}

#saveButtonDosCuadratica .dx-icon {
    font-size: 25px;
    color:black;
}

#saveButtonDosCuadratica{
    width: 60px !important;
}





/* General table styles */
.tablaConciliacionesCuadraticas {
    width: 100%;
    border-collapse: collapse; /* Makes the table borders collapse into one */
    margin: 20px 0;
    font-family: Arial, sans-serif; /* Sets a clean font */
}

/* Table header styles */
.tablaConciliacionesCuadraticas thead {
    background-color: white;
    color: white;
}

/* Header cells styling */
.tablaConciliacionesCuadraticas th {
    padding: 12px 15px;
    text-align: center;
    font-size: 1.2em;
}

/* Background colors for specific headers */
.tablaConciliacionesCuadraticas th:nth-child(2) {
    background-color: teal;
}

.tablaConciliacionesCuadraticas th:nth-child(3) {
    background-color: navy;
}

/* Table body styles */
.tablaConciliacionesCuadraticas tbody tr:nth-child(odd) {
    background-color: #f9f9f9; /* Light gray for odd rows */
}

.tablaConciliacionesCuadraticas tbody tr:nth-child(even) {
    background-color: #e2e2e2; /* Slightly darker gray for even rows */
}

/* Cell padding and text alignment */
.tablaConciliacionesCuadraticas td {
    padding: 10px 15px;
    text-align: center;
}

/* Bold styling for row labels */
.tablaConciliacionesCuadraticas td strong {
    font-weight: bold;
}

/* Negative values styling */
.tablaConciliacionesCuadraticas .negative {
    color: red; /* Red text for negative values */
}

/* Add a border to the table and cells */
.tablaConciliacionesCuadraticas, 
.tablaConciliacionesCuadraticas th, 
.tablaConciliacionesCuadraticas td {
    border: 1px solid #ddd;
}

/* Hover effect for rows */
.tablaConciliacionesCuadraticas tbody tr:hover {
    background-color: #c0c0c0;
    cursor: pointer; /* Adds a pointer cursor to hover over rows */
}

.solo-esta-tabla-cuadratico .dx-datagrid-pager {
  display: none;
}

</style>
