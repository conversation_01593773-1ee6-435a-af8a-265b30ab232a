<template>
    <vx-card title="Despacho Movimientos Bodega">
    
        <div class="content content-pagex">
            <vs-divider></vs-divider>
            <br>
            <div class="flex flex-wrap">    
                <div class="sm:w-full md:w-7/12 lg:w-6/12 xl:w-5/12 pr-4">                    
                    <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                        <label class="typo__label">Bodega Fuente:</label>      
                        <strong>{{ cod_bodegaFuente.CODIGO }}</strong>
                        <multiselect
                            v-model="cod_bodegaFuente"
                            :options="Lista_bodegas_fuente"
                            :searchable="true"
                            :close-on-select="true"
                            :show-labels="false"
                            :allow-empty="false"
                            :custom-label="Bodegas_seleccionado"
                            placeholder="Seleccionar bodega"
                            @input="onChangeBodega"
                            :danger="errors.length > 0"
                            :danger-text="errors.length > 0 ? errors[0] : null">
                            <span
                            slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                    </ValidationProvider>                                                
                </div>                   
                <div class="sm:w-full md:w-4/12 lg:w-3/12 xl:w-2/12 pr-4">
                    <label class="typo__label">Estado</label>
                    <multiselect v-model="cb_lista_operacion" :options="lista_estado" :searchable="true" :close-on-select="true" :show-labels="false" :allow-empty="false" :custom-label="Operacion_seleccionada" placeholder="Seleccionar" @input="onChangeEstado">
                        <span slot="noOptions">Lista no disponible.</span>
                    </multiselect>
                </div>
            </div>
            <vs-divider></vs-divider>
            <div class="flex flex-wrap"> 
                <vs-row class="w-full">
                    <div class="sm:w-full md:w-7/12 lg:w-6/12 xl:w-5/12 pr-4">                    
                        <label class="typo__label">Bodega Destino:</label>      
                        <multiselect
                            v-model="cod_bodegaDestino_filtro"
                            track-by="CODIGO"
                            :options="Lista_bodegas_destino_filtro"
                            :searchable="true"
                            :close-on-select="true"
                            :show-labels="true"
                            :allow-empty="true"
                            :custom-label="Bodegas_seleccionado"
                            placeholder="Seleccionar bodega"
                            deselect-label="Quitar selección"
                            select-label="Seleccionar"
                            @input="Consultar_OrdenEnc()">
                            <span slot="noOptions">Lista no disponible.</span>
                        </multiselect>
                    </div>
                    <div class="pr-4">
                        <label class="typo__label">Fecha Inicio Solicitud:</label>
                        <vs-input type="date" v-model="fecha_inicio" name="date1" />
                    </div>
                    <div class="pr-4">
                        <label class="typo__label">Fecha Final Solicitud:</label>
                        <vs-input type="date" v-model="fecha_final" name="date1" />
                    </div>
                    <vs-button style="float:left;margin: 18px 0px" color="primary" type="filled" icon-pack="fas" icon="fa-search" @click="Consultar_OrdenEnc()"> Búsqueda</vs-button>
                </vs-row>    
            </div>
            <vs-divider></vs-divider>
    
            <vs-table2 max-items="10" pagination :data="Lista_vista" search id="tb_lista_solicitud">
    
                <template slot="thead">
                    <th width="130px">Nº. Movimiento</th>
                    <th width="130px">Nº. Solicitud</th>
                    <th>Bodega Destino</th>
                    <th>Fecha(s)</th>
                    <th>Observación</th>
                    <th></th>
                    <!--<th style="text-align: center;">Recepcionada</th>-->
    
                </template>
    
                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data" :style="{'background-color':tr.MOVIMIENTOPADRE?'azure':''}" >                         
                        <vs-td2 width='5%'>
                            {{tr.IDMOVIMIENTOFK}}
                        </vs-td2>
                        <vs-td2 width='5%'>
                            {{tr.IDREQUISICIONENC}}
                        </vs-td2>                        
                        <vs-td2 width='20%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                {{tr.IdBodegaDestinofk +' - ' +tr.NOMBRE_BODEGA_DESTINO}}
                            </div>
                        </vs-td2>
    
                        <vs-td2 width='20%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                <label v-if="tr.FECHASOLICITUD">Creado: {{tr.FECHASOLICITUD}}</label>
                                <br v-if="tr.FECHASOLICITUD">
                                <label v-if="tr.FECHAFINALIZADO">Solicitado: {{tr.FECHAFINALIZADO}}</label>
                                <br v-if="tr.FECHAFINALIZADO">
                                <label v-if="tr.FECHAENTREGADA">Despachado: {{tr.FECHAENTREGADA}}</label>
                                <br v-if="tr.FECHAENTREGADA">
                                <label v-if="tr.FECHARECIBIDA">Aceptado: {{tr.FECHARECIBIDA}}</label>
                                <br v-if="tr.FECHARECIBIDA">
                                <label v-if="tr.FECHAANULACION">Anulado: {{tr.FECHAANULACION}}</label>
                            </div>
                        </vs-td2>

                        <vs-td2  width='25%'>
                            <div style="word-wrap: break-word;white-space: pre;">
                                {{ tr.OBSERVACIONES }}
                            </div>                                
                        </vs-td2>    
                        <vs-td2 v-if="Id_estado_seleccionado == 'E'" width='10%' align="right" style="white-space:nowrap;min-width: 200px;">
                            <vx-tooltip text="Despachar" style="display:inline-block;margin-right:10px">
                                <vs-button color="primary" icon-pack="fas" icon="fa-arrow-right"   @click="Emergente_Detalle(data[indextr])"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip text="Excel" style="display:inline-block;margin-right:10px">
                                <vs-button icon-pack="far" icon="fa-file-excel" @click="Consultar_Movimiento(data[indextr],'EXCEL')" color="success" style="display:inline-block;margin-right:2px"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip text="Imprimir" style="display:inline-block;margin-right:10px">
                                <vs-button color="primary" icon-pack="feather" icon="icon-printer" style="display:inline-block;margin-right:2px" @click="Consultar_Movimiento(data[indextr])"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip text="Anular" style="display:inline-block;margin-right:2px">
                                <vs-button color="red" icon-pack="fas" icon="fa-times"   @click="Anular_Movimiento(data[indextr])"></vs-button>
                            </vx-tooltip>
                        </vs-td2>
                        <vs-td2 v-else width='10%' align="right" style="white-space:nowrap;min-width: 200px;">
                            <vx-tooltip text="Consultar" style="display:inline-block;margin-right:10px">
                                <vs-button color="#B2BABB" icon-pack="fas" icon="fa-arrow-right"  @click="Emergente_Datalle_Consulta(data[indextr])"></vs-button>                                
                            </vx-tooltip>
                            <vx-tooltip text="Excel" style="display:inline-block;margin-right:10px">
                                <vs-button icon-pack="far" icon="fa-file-excel" @click="Consultar_Movimiento(data[indextr],'EXCEL')" color="success" style="display:inline-block;margin-right:2px"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip text="Imprimir" style="display:inline-block;margin-right:10px">
                                <vs-button color="primary" icon-pack="feather" icon="icon-printer" style="display:inline-block;margin-right:2px" @click="Consultar_Movimiento(data[indextr])"></vs-button>
                            </vx-tooltip>
                        </vs-td2>
    
    
                    </tr>
                </template>
            </vs-table2>
    
            <!----------------------- DESPACHO REQUISICION ---------->
            <vs-popup class="inventario-popup" classContent="popup-example" :title="Descripcion_Emergente" :active.sync="Estado_Emergente">
                <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px"> 
                    <div style="border:2px solid #1B5E20;border-radius:5px;padding:5px;font-size:12px;background-color: #C8E6C9">
                        Sucursal Origen: <b style="font-size:1vw">{{sesion.sesion_sucursal_nombre}}</b>
                    </div>
                    <br>
                    <vs-row class="w-full flex">
                        <div class="sm:w-6/12 md:w-5/12 lg:w-4/12 xl:w-3/12">
                            <b> Movimiento No. </b>&nbsp;
                            <b style="font-size:2vw">{{Id_Movimiento}}</b>
                        </div>   
                        <div class="sm:w-6/12 md:w-5/12 lg:w-4/12 xl:w-3/12" style="align-self:right; direction: rtl;">
                            <b> Solicitud No. </b>&nbsp;
                            <b style="font-size:2vw">{{Id_OrdenRequisicion}}</b>
                        </div>
                    </vs-row>                      
                    <div class="w-1/2 md:w-1/2 lg:w-6/12 xl:w-6/12">
                         <b> <small>Fecha Solicitud: </small></b>
                        <b>{{Requisicion_seleccionada.FECHAFINALIZADO}}</b>
                    </div>
                    <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2" v-if="Consulta">
                        <b> <small>Estado: </small></b>&nbsp;
                        <b>{{Estado_Requisicion}}</b>
                    </div> 
                    <br>
                    <div>
                        <label class="typo__label">Bodega Fuente:</label>      
                        <strong>{{ cod_bodegaFuente.CODIGO }}</strong>
                        <multiselect v-model="cod_bodegaFuente" :options="Lista_bodegas_fuente" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Bodegas_seleccionado"  placeholder="Seleccionar bodega" :disabled="Deshabilitar_campos">
                            <span slot="noOptions">Lista no disponible.</span>
                        </multiselect> 
                    </div>
                    <div>
                        <br>                                          
                        <label class="typo__label">Bodega Destino:</label>
                        <strong>{{ cb_bodegas.CODIGO }}</strong>
                        <multiselect v-model="cb_bodegas" :options="Lista_bodegas_destino" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Bodegas_seleccionado" placeholder="Seleccionar bodega" :disabled="Deshabilitar_campos">
                            <span slot="noOptions">Lista no disponible.</span>
                        </multiselect>
                    </div>
                    <br>
                    <!--- Producto -->
                    <vs-divider>Detalle</vs-divider>
    
                    <!--- Mostrar detalle de la orden--->
                    <vs-table2 max-items="10" pagination :data="Lista_detalle" id="tb_lista_solicitud" v-if="Estado_Emergente">
    
                        <template slot="thead">
                            <th>Linea</th>
                            <th>Código</th>
                            <th>Producto</th>
                            <th>Unidad Medida</th>
                            <th>Cant. Solicitada</th>
                            <th v-if="permiso_ver_costo_promedio">Costo promedio</th>
                            <th v-if="permiso_ver_costo_promedio">Costo promedio total</th>
                            <th>Cant. Despachada</th>
                            <th></th>
                            <th></th>
                        </template>
    
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 width='5%'>
                                    {{ indextr+1 }}
                                </vs-td2>
                                <vs-td2 width='5%'>
                                    {{ tr.IDPRODUCTOFK }}
                                </vs-td2>                                
                                <vs-td2 width='60%'>
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                         
                                        {{tr.DESCRIPCION_PRODUCTO}}                
                                    </div>
                                </vs-td2>
                                <vs-td2 width='10%'>
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                                          
                                        {{tr.DESCRIPCION_UNIDAD}}
                                    </div>
                                </vs-td2>
                                <vs-td2 width='5%'>
                                    {{ tr.CANTIDAD_PEDIDA }}
                                </vs-td2>
                                <vs-td2 v-if="permiso_ver_costo_promedio" >
                                    <div style="min-width: fit-content;">
                                        {{ $formato_decimal(tr.COSTO_PROMEDIO) }}
                                    </div>
                                </vs-td2>
                                <vs-td2 v-if="permiso_ver_costo_promedio" >
                                    <div style="min-width: fit-content;">
                                        {{$formato_decimal(tr.COSTO_PROMEDIO * (['P','E'].includes(Id_estado_seleccionado)? Number(tr.CANTIDAD_PEDIDA): Number(tr.CANTIDAD_ENTREGA))) }}
                                    </div>
                                </vs-td2>
                                <vs-td2 width='15%' >
                                    <ValidationProvider name="Existencias" :rules="'required|min:0|max:'+tr.CANTIDAD_PEDIDA" v-slot="{ errors }" class="required">
                                        <vs-input  type="number"  v-model="tr.CANTIDAD_ENTREGA" class="w-full" :disabled="!tr.ESTADO_SWITCH || Consulta" @input="VerificarExistencias(tr)" 
                                                   :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"/>                                    
                                    </ValidationProvider>
                                    <!--- Semaforo Existencias, máximos y mínimos--->
                                    <div v-if="!Consulta" style="word-wrap: break-word;white-space: wrap;">
                                        <label v-if="Redondeo(parseFloat(tr.INV_DISPONIBLE-tr.CANTIDAD_ENTREGA)) > Redondeo(parseFloat(tr.MINIMO_DESPACHANTE))" style="width:100px; border-radius: 3px;  background-color:#69F0AE;text-align: center;color:black;font-size:15px; "> Disponible: {{Redondeo(tr.INV_DISPONIBLE-tr.CANTIDAD_ENTREGA)}} </label>
                                        <label v-else-if="( Redondeo(parseFloat(tr.INV_DISPONIBLE-tr.CANTIDAD_ENTREGA)) >0) && (Redondeo(parseFloat(tr.INV_DISPONIBLE-tr.CANTIDAD_ENTREGA)) <= Redondeo(parseFloat(tr.MINIMO_DESPACHANTE)))" style="width:100px; border-radius: 3px;  background-color:#FFFF00;text-align: center;color:black;font-size:15px; "> Disponible: {{Redondeo(tr.INV_DISPONIBLE-tr.CANTIDAD_ENTREGA)}} </label>
                                        <label v-else-if="Redondeo(parseFloat(tr.INV_DISPONIBLE-tr.CANTIDAD_ENTREGA)) <= 0" style="width:100px; border-radius: 3px;  background-color:#FF5252;text-align: center;color:white;font-size:15px; "> Disponible: {{Redondeo(tr.INV_DISPONIBLE-tr.CANTIDAD_ENTREGA)}} </label>                              
                                    </div>
                                </vs-td2>
                                <vs-td2 width='5%' v-if="!Consulta">
                                    <vx-tooltip text="Despacho Manual" style="display:inline-block">
                                        <vs-switch style="margin-top:10px;margin-left:20px" :key="tr.ESTADO_SWITCH" v-model="tr.ESTADO_SWITCH" />
                                    </vx-tooltip>
                                </vs-td2>
                                <vs-td2 width='5%' v-if="!Consulta">
                                    <vx-tooltip text="Eliminar" style="display:inline-block">
                                        <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="Eliminar_Registro(tr,indextr)"></vs-button>
                                    </vx-tooltip>
                                </vs-td2>
                            </tr>
                        </template>
                        <template slot="tfooter">
                            <tr v-if="permiso_ver_costo_promedio" class="foot">
                                <th colspan="6" scope="row" style="padding-right: 10px;">Total del Movimiento</th>
                                <td>{{totalCostos}}</td>
                            </tr>
                        </template>
                    </vs-table2>
                    <vs-divider></vs-divider>
                    <div class="flex flex-wrap">
                        <div class="w-full md:w-full lg:w-full xl:w-full">
                            <vs-textarea label="Observaciones" type="text" class="w-full" v-model="Observaciones" :disabled="Consulta"/>
                        </div>    
                    </div>
                    <vs-divider></vs-divider>
                    <div class="flex">
                        <div class="w-full">
                            <b><small>Solicitado por: </small></b>&nbsp;
                            <b>{{Requisicion_seleccionada.CORPORATIVO_SOLICITANTE +' - '+Requisicion_seleccionada.IDCORPORATIVOSOLICITAFK}}</b>&nbsp;&nbsp;
                            <b><small v-if="Requisicion_seleccionada.ESTADO=='F'">/ Aceptado por: </small></b>&nbsp;
                            <b v-if="Requisicion_seleccionada.ESTADO=='F'">{{Requisicion_seleccionada.CORPORATIVO_FINALIZA +' - '+Requisicion_seleccionada.IDCORPORATIVOFINALIZAFK}}</b>
                        </div> 
                    </div>             
                    <vs-divider></vs-divider>
                    <vs-button v-if="!Consulta" color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" id="btn_confirmacion" @click="Finalizar_Despacho()"> Finalizar Despacho</vs-button>
                    <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-printer" id="btn_movimiento" @click="Consultar_Movimiento(Requisicion_seleccionada)"> Imprimir </vs-button>                
                    <vs-button icon-pack="far" style="float:right;margin: 5px" icon="fa-file-excel" @click="Consultar_Movimiento(Requisicion_seleccionada,'EXCEL')" color="success">Excel</vs-button>
                    <vs-divider></vs-divider>
                </div>
            </vs-popup>
    
        </div>
    </vx-card>
    </template>
    
    <script>
    import Multiselect from 'vue-multiselect'
    import moment from "moment"
    import "vue-multiselect/dist/vue-multiselect.min.css"
    export default {
        components: {
            Multiselect
        },
        data() {
            return {
                Estado_VentanaEmergente_Busqueda: false,
                cb_lista_operacion: '',
                Descripcion_Emergente: '',
    
                producto_buscar: '',
                lista_estado: [{
                        ID: 'E',
                        DESCRIPCION: 'No Despachado'
                    },
                    {
                        ID: 'N,R,F',
                        DESCRIPCION: 'Despachado,Aceptado,Anulado'
                    }
                ],
                Id_estado_seleccionado: 0,
                fecha_inicio: '',
                fecha_final: '',
                Lista_vista: [],
                Estado_Emergente: false,
                Consulta: false,
                permisos_tipos_bodegas: [],
                solicitud_bodega_vencido: false,
                permiso_bodega_vencido:'',
                permiso_despacho_bodega_vencido:'',
                permiso_ver_costo_promedio: false,
                Lista_bodegas_fuente: [],
                Lista_bodegas_destino: [],
                Lista_bodegas_destino_filtro: [],
                id_bodega_seleccionada: '',
                cb_bodegas: '',
    
                Producto_seleccionado: {
                    nombre: '',
                    marca_comercial: '',
                    Principio_activo: '',
                    Concentracion: '',
                    Presentacion: '',
                    Codigo: '',
                    codigo_interno: '',
                    Activo: 'N'
                },
                Id_OrdenRequisicion: 0,
                Estado_Requisicion: '',
                Id_Movimiento: null,
                Cantida_solicitar: '',
    
                Lista_detalle: [],
                Deshabilitar_campos: false,
                producto: [],
    
                Requisicion_seleccionada: {},
                Observaciones: '',
    
                user: '',
                message: 'prueba mensaje',
                messages: [],
                notificaciones: [],
                cod_bodegaFuente:'',
                cod_bodegaDestino_filtro:'',
                listado_reportes: [],
                listado_source: {
                    CODIGO_SOLICITUD: '',
                    CODIGO_MOVIMIENTO: '',
                    CODIGO_REQUERIMIENTO: '',
                }        
            }
        },
        async mounted() {
            this.cb_lista_operacion = {
                ID: 'E',
                DESCRIPCION: 'No Despachado'
            }            
            this.Id_estado_seleccionado = 'E';
            let permisos_sub_bodegas = []
            for(let privilegio of this.$store.state.privilegios){
               if(privilegio.Privilegio.includes("TIPO_BODEGA")){                      
                   let tipobodega = privilegio.Privilegio.split('_')[2];
                   if(tipobodega == 'T'){
                     this.permiso_bodega_vencido = 'S'
                   }else{
                     this.permisos_tipos_bodegas.push(privilegio.Privilegio.split('_')[2])                    
                   }                                                       
                }else if(privilegio.Privilegio.includes("PERMISO_DESPACHO_BODEGA_VENCIDO")){
                    this.permiso_despacho_bodega_vencido = 'S'
                }else if(privilegio.Privilegio.includes("DESPACHO_SUB_BODEGA")){
                    permisos_sub_bodegas.push(privilegio.Privilegio.split('_')[3])
                }
            }      
            await this.Consultar_Bodega('F',this.permisos_tipos_bodegas.join(","),this.permiso_bodega_vencido);
            await this.Consultar_Bodega('H',this.permisos_tipos_bodegas.join(","),this.permiso_bodega_vencido);
            await this.Consultar_Bodega('P',this.permisos_tipos_bodegas.join(","),this.permiso_bodega_vencido);
            await this.ConsultarSubBodega('G',permisos_sub_bodegas.join(","));   
            this.permiso_ver_costo_promedio = this.$validar_privilegio('VER_COSTO_PROMEDIO').status
        },
        computed: {
            totalCostos() {
                return this.Lista_detalle.reduce((acumulador, movimiento) => 
                                acumulador + parseFloat(movimiento.COSTO_PROMEDIO) *
                                            (['P','E'].includes(this.Id_estado_seleccionado)? Number(movimiento.CANTIDAD_PEDIDA): Number(movimiento.CANTIDAD_ENTREGA)) , 0)
                           .toLocaleString("es-GT", {
                                                style: "currency",
                                                currency: "GTQ",
                                                maximumFractionDigits:2
                                            })
    
            },
            sesion() {
                return this.$store.state.sesion
            }
        },
        async beforeCreate(){
            this.listado_reportes = await this.$recupera_parametros_reporte('Movimiento Requerimiento')                        
        },
        methods: {
            async Consultar_Movimiento(datos,formato='PDF') {                
                this.listado_source.CODIGO_MOVIMIENTO = datos.IDMOVIMIENTOFK
                this.listado_source.CODIGO_SOLICITUD = datos.IDREQUISICIONENC
                this.listado_source.VerCostoPromedio = this.permiso_ver_costo_promedio? 'S':'N'
                this.$reporte_modal({
                    Nombre: "Movimiento Requerimiento",
                    Opciones: this.listado_source,
                    Formato: formato
                }).catch(() => {
                })
            },
            Redondeo(monto){
                return Math.round(((monto) + Number.EPSILON) * 100) / 100
            },
            GetDateValue(value) {
                if (value === undefined) {
                    return '';
                } else if (value === null) {
                    return '';
                } else if (value == '') {
                    return '';
                }
                return moment(value).format('DD/MM/YYYY');          
            },
            Eliminar_Registro(value) {
                /**
                 * @General
                 * Función eliminar registro;
                 */

                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',
                    button: {
                        yes: 'Aceptar',
                        no: 'Cancelar'
    
                    },
                    acceptText: 'Aceptar',
                    cancelText: 'Cancelar',
                    text: '¿Dar de baja Pedido  \'' + value.DESCRIPCION_PRODUCTO + '\'   \'' + value.DESCRIPCION_UNIDAD + '\' ? ',
                    accept: () => {
    
                        this.axios.post('/app/v1_OrdenCompra/Requisicion_Det', {
                                Requisicion_det: value.IDREQUISICIONDET,
                                Requisicion_enc: this.Id_OrdenRequisicion,
                                Producto: '0',
                                cant_pedida: 0,
                                cant_entregada: 0,
                                cant_recibida: 0,
                                Operacion: 'B'
                            })
                            .then(resp => {
    
                                if (resp.data.codigo == 0) {
                                    let indice = this.Lista_detalle.findIndex(d=>d.IDREQUISICIONDET == value.IDREQUISICIONDET)
                                    this.Lista_detalle.splice(indice,1);                                    
                                }
                            })
    
                    }
                })
    
            },
            Anular_Movimiento(Datos){
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación Anulación',
                    acceptText: 'Anular',
                    cancelText: 'Cancelar',
                    text: `¿Desea anular la solicitud No. ${Datos.IDREQUISICIONENC}?`,
                    accept: () => {
                        return this.axios.post('/app/v1_OrdenCompra/Requisicion_Enc', {
                            Requisicion_enc: Datos.IDREQUISICIONENC,       
                            Operacion: 'A'
                        }).then(resp =>{
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                        position:'top-center',
                                    color: '#B71C1C',
                                    title: 'Alerta',
                                    text: resp.data.mensaje,
                                })
                            } else {
                                this.$vs.notify({
                        position:'top-center',
                                    color: 'sucess',
                                    title: 'Anulación',
                                    text: resp.data.mensaje
                                })    
                                this.Consultar_OrdenEnc();
                            }
                        })
                    }
                })
            },
            VerificarExistencias(producto){
                if(Number(producto.CANTIDAD_ENTREGA) < 0){
                    this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'Debe despachar cantidades mayores a 0',
                        });
                    producto.CANTIDAD_ENTREGA = 0
                }

                if(Number(producto.CANTIDAD_ENTREGA) > Number(producto.INV_DISPONIBLE)){
                    this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'No hay suficientes existencias',
                        });
                    producto.CANTIDAD_ENTREGA = 0
                }
                
                if(Number(producto.CANTIDAD_ENTREGA) > Number(producto.CANTIDAD_PEDIDA)){
                    this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'No puede despachar mas que la cantidad solicitada',
                        });
                    producto.CANTIDAD_ENTREGA = 0
                }
            },
            Operacion_seleccionada({
                DESCRIPCION
            }) {
                return `${DESCRIPCION} `
            },
            onChangeEstado(value) {
                if(!value)
                    return
                if (value !== null && value.length !== 0) {
                    this.Id_estado_seleccionado = value.ID;
                    this.Consultar_OrdenEnc();
                } else {
                    this.Id_estado_seleccionado = '';
                }
            },
            Bodegas_seleccionado({
                CODIGO,NOMBRE
            }) {
                return `${CODIGO} - ${NOMBRE} `;
            },
            onChangeBodega(value) {   
                if(!value){
                    this.cod_bodegaFuente = ''
                    return
                }                           
                if (value !== null && value.length !== 0) {
                    this.id_bodega_seleccionada = value.CODIGO;
                    this.cod_bodegaDestino_filtro = '';
                    this.Consultar_OrdenEnc();
                } else {
                    this.id_bodega_seleccionada = '';
                }
            },
            Emergente_Datalle_Consulta(Datos){
                this.Estado_Emergente = true;
                this.Consulta = true;
                this.cb_bodegas = '';    
                this.producto_buscar = '';
                this.Cantida_solicitar = '';
                
                this.Requisicion_seleccionada = Datos;

                this.Id_OrdenRequisicion = Datos.IDREQUISICIONENC;
                this.Estado_Requisicion = Datos.ESTADOREQUISICION;
                this.Id_Movimiento = Datos.IDMOVIMIENTOFK;
                this.id_bodega_seleccionada = Datos.IdBodegaFuentefk;
                setTimeout(() => {
                    this.cb_bodegas = {
                        NOMBRE: Datos.NOMBRE_BODEGA_DESTINO,
                        CODIGO: Datos.IdBodegaDestinofk
                    }
                }, 500);
                this.Observaciones = Datos.OBSERVACIONES;
                this.Deshabilitar_campos = true;
                this.Descripcion_Emergente = 'Consulta Despacho Movimiento';
                this.Consultar_OrdenDetalle()
            
            },
            Emergente_Detalle(Datos) {
                this.Estado_Emergente = true;
                this.Consulta = false;
                this.cb_bodegas = '';
    
                this.producto_buscar = '';
                this.Cantida_solicitar = '';

                this.Requisicion_seleccionada = Datos;

                this.Id_OrdenRequisicion = Datos.IDREQUISICIONENC;
                this.Id_Movimiento = Datos.IDMOVIMIENTOFK;
                this.id_bodega_seleccionada = Datos.IdBodegaFuentefk;
                setTimeout(() => {
                    this.cb_bodegas = {
                        NOMBRE: Datos.NOMBRE_BODEGA_DESTINO,
                        CODIGO: Datos.IdBodegaDestinofk
                    }
                }, 500);
                this.Observaciones = Datos.OBSERVACIONES;
                this.Deshabilitar_campos = true;
                this.Descripcion_Emergente = 'Despacho Movimiento';
                this.Consultar_OrdenDet()
            
            },
    
            Validacion_Campos(Tipo, Nombre, valor, obligatorio, cant_maxima) {
                /**
                 * @General
                 * Tipo : N = Númerio, Nombre = Descripción del campo, Valor = A evaluar, Obligatorio= Verdadero o falso,cant_maxima  = Aplica unicamente para un string y define la cantidad de caracteres 
                 * Devuelve un mensaje al usuario si las validaciones no se cumple y aborta, de lo contrario devuelve verdadero
                 */
                if (Tipo == 'ID') {
                    if (valor <= 0) {
                        this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else if (Tipo == 'N') {
                    
                    if ( valor <0 || valor == "") {
                        this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else {
                    if (valor == '' && obligatorio == true) {
                        this.$vs.notify({
                        position:'top-center',
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                        });
                        return false;
                    } else {
    
                        if (cant_maxima < valor.length) {
                            this.$vs.notify({
                        position:'top-center',
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'Campo ' + Nombre + ' no debe superar los ' + cant_maxima + ' carácteres.',
                            });
                            return false;
                        } else {
                            return true;
                        }
    
                    }
                }
            },
            /*****************************CONSULTAR INFORMACION*************/
            Consultar_OrdenEnc() {
    
                if(!this.cod_bodegaFuente){
                    this.$vs.notify({
                        position:'top-center',
                                color: 'danger',
                                title: 'Inventario',
                                text: 'Seleccione una bodega fuente',
                            })
                    return
                }
                let Operacion = 'D'
                if(this.Id_estado_seleccionado == 'E' ){
                    Operacion = this.Id_estado_seleccionado
                }

                let tipoMovimiento = '4,13';
            
                this.axios.post('/app/v1_OrdenCompra/ConsultaRequisicion_Enc', {
                        Estado: this.Id_estado_seleccionado,
                        fecha_inicio: this.GetDateValue(this.fecha_inicio),
                        fecha_final: this.GetDateValue(this.fecha_final),
                        Operacion: Operacion,
                        Bodega_fuente: this.cod_bodegaFuente.CODIGO,
                        Bodega_destino: this.cod_bodegaDestino_filtro?.CODIGO??null,
                        TipoMovimiento: tipoMovimiento
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                        position:'top-center',
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: resp.data.mensaje,
                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_vista = [];
                        } else {
                            this.$vs.notify({
                                                position:'top-center',
                                                color: 'success',
                                                title: 'Inventario',
                                                text: 'Se actualizaron los movimientos exitosamente',
                                            })
                            this.Lista_vista = resp.data.json;
    
                        }
                    })
                    .catch(() => {
                        this.Lista_vista = [];
                    })
            },
            Consultar_OrdenDetalle() {
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/ConsultaRequisicion_Det', {
                        Requisicion_enc: this.Id_OrdenRequisicion,
                        tipo: 'D'
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {                        
                            this.Lista_detalle = [];
                        } else {
                            this.Lista_detalle = resp.data.json.map(x => {
                                x.COSTO_PROMEDIO = Number(x.COSTO_PROMEDIO)
                                return x
                            });
                        }
                    })
                    .catch(() => {
                        this.Lista_detalle = [];
                    })
            },
            Consultar_OrdenDet() {
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/ConsultaRequisicion_Det', {
                        Requisicion_enc: this.Id_OrdenRequisicion,
                        tipo: 'D'
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {                        
                            this.Lista_detalle = [];
                        } else {
                            this.Lista_detalle = resp.data.json.map(function(producto){ 
                                if(Number(producto.CANTIDAD_PEDIDA)<=Number(producto.INV_DISPONIBLE))
                                    producto.CANTIDAD_ENTREGA = producto.CANTIDAD_PEDIDA

                                producto.COSTO_PROMEDIO = Number(producto.COSTO_PROMEDIO)

                                return producto;
                            });
                        }
                    })
                    .catch(() => {
                        this.Lista_detalle = [];
                    })
            },
            async Consultar_Bodega(Operacion, BodegasDespacho, BodegaTransito) {
                const url = this.$store.state.global.url
                await this.axios.post(url+'app/v1_OrdenCompra/consulta_bodega', {
                                                                        operacion: Operacion,
                                                                        BodegasDepacho: BodegasDespacho,
                                                                        BodegaTransito: BodegaTransito
                                                                     })
                           .then(resp => {
            
                                if (resp.data.codigo != 0) {
                                    this.$vs.notify({
                        position:'top-center',
                                        color: '#B71C1C',
                                        title: 'Bodegas',
                                        text: resp.data.mensaje,
                                    })
                                } else {
                                    if(Operacion == 'F')
                                        this.Lista_bodegas_fuente = resp.data.json;                           
                                    else if(Operacion == 'H')
                                        this.Lista_bodegas_destino= resp.data.json;   
                                    else if(Operacion == 'P') 
                                        this.Lista_bodegas_destino_filtro = resp.data.json;                      
                                }
                            })
                           .catch(() => { })
        
            },            
            async ConsultarSubBodega(Operacion,TipoBodegas) {
                if(!TipoBodegas || TipoBodegas.length == 0 ){return}

                const url = this.$store.state.global.url
                await this.axios.post(url+'app/v1_OrdenCompra/consulta_bodega', {
                                                                        operacion: Operacion,
                                                                        lista_tipo_bodegas: TipoBodegas
                                                                        })
                            .then(resp => {
            
                                if (resp.data.codigo != 0) {
                                    this.$vs.notify({
                                        position:'top-center',
                                        color: '#B71C1C',
                                        title: 'Bodegas',
                                        text: resp.data.mensaje,
                                    })
                                } else {
                                    this.Lista_bodegas_fuente =
                                        this.Lista_bodegas_fuente.concat(resp.data.json.filter(bodega=>bodega.BodegaFuente == 'S')) 

                                    this.Lista_bodegas_destino =
                                        this.Lista_bodegas_destino.concat(resp.data.json.filter(bodega=>bodega.BodegaFuente == 'N'))                  
                                
                                    this.Lista_bodegas_fuente.sort((a,b)=>Number(a.CODIGO)>=Number(b.CODIGO)?1:-1)
                                    this.Lista_bodegas_destino.sort((a,b)=>Number(a.CODIGO)>=Number(b.CODIGO)?1:-1)                                                                   
                                }
                            })
                            .catch(() => { })
        
            },
            /*************************** ACTUALIZAR, ELIMINAR NUEVO REGISTRO */
            
            Registrar_DespachoDet(datos, Operacion) {
    
                //Operacion = 'E' -> Se va despachar producto.
                //Operacion = 'A' -> Se va despachar producto.
                var res_variables = false;
    
                
    
                res_variables = this.Validacion_Campos('N', 'Cantidad Despachar', datos.CANTIDAD_ENTREGA, true, 100000000);
    
                if (res_variables) {
                    this.axios.post('/app/v1_OrdenCompra/Requisicion_Det', {
                            Requisicion_det: datos.IDREQUISICIONDET,
                            Requisicion_enc: datos.IDREQUISICIONENCFK,
                            Empresa: this.sesion.sesion_empresa,
                            Producto: datos.IDPRODUCTOFK,
                            cant_pedida: 0,
                            cant_entregada: datos.CANTIDAD_ENTREGA,
                            cant_recibida: 0,
                            corporativo: this.sesion.corporativo,
                            Operacion: Operacion,
                        })
                        .then(resp => {
    
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                        position:'top-center',
                                    color: '#B71C1C',
                                    title: 'Alerta',
                                    text: resp.data.mensaje,
                                })
                            }
                            this.Consultar_OrdenDet();
                        })
                }
            },
    
            Finalizar_Despacho() {
                // 
                // 
                /* VALIDACION DE ARRAY */
                
                for (var i = 0; i < this.Lista_detalle.length; i++) {
                    if(this.Lista_detalle[i].CANTIDAD_ENTREGA < this.Lista_detalle[i].CANTIDAD_PEDIDA  ){

                        
                        this.Lista_detalle[i].CANTIDAD_ENTREGA < this.Lista_detalle[i].CANTIDAD_PEDIDA
                        var slug = this.Lista_detalle[i].ESTADO_SWITCH;

                        if (slug == false) {
                            this.$vs.notify({
                                position:'top-center',
                                color: '#B71C1C',
                                title: 'Despacho Movimiento',
                                text: 'Debe realizar el despacho manual del producto en la linea: ' + (i+1),
                                time: 6000
                            });
                            return false;
                        }
                    }
                        
                }
    
    
                
                    
                    this.axios.post('/app/v1_OrdenCompra/Requisicion_Enc', {
                            Requisicion_enc: this.Requisicion_seleccionada.IDREQUISICIONENC,
                            Productos: this.Lista_detalle.map((producto)=>producto.IDPRODUCTOFK).toString(),
                            CantidadesDespacho: this.Lista_detalle.map((producto)=>producto.CANTIDAD_ENTREGA).toString(),
                            NumerosRequisicionDet: this.Lista_detalle.map((producto)=>producto.IDREQUISICIONDET).toString(),                            
                            Total: 0,
                            Cant_lineas: 0,
                            Observaciones:  this.Observaciones,
                            Bodega_solicitante: 0,
                            Bodega_destino: 0,
                            Estado: 'R',
                            Operacion: 'E',
                        })
                        .then(resp => {
    
                            if (resp.data.codigo == 0) {
                                this.Estado_Emergente = false;
                                this.Consultar_OrdenEnc();                                    
                            }
                        })
                    
                
            }
        }
    
    }
</script>
<style>
.inventario-popup.con-vs-popup .vs-popup {
    width: 80% !important;
}
tfoot .foot {
  border-top: 3px dotted rgb(160 160 160);
  background-color: #2c5e77;
  color: #fff;
}

</style>

<style scoped>

tfoot th {
  text-align: right;
}

tfoot td {  
  font-weight: bold;
}
</style>