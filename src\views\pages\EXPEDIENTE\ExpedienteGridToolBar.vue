<template>
   <div v-if="visible">
       <slot></slot>
       <DxButton v-if="showItems.find(e => e == 'refresh')" locate-in-menu="auto" icon="refresh" type="success" styling-mode="outlined" id="botonBarra" @click="$emit('refresh')" hint="Recargar información" />
       <DxDropDownButton v-if="pdfExportItems.length > 1 && showItems.find(e => e == 'exportPdf')" id="botonPDF" :items="pdfExportItems" :drop-down-options="{ width: 180, type: 'danger' }" icon="fas fa-file-pdf" type="danger" hint="Generar en PDF" @item-click="exportarPDF" />
       <DxButton v-else-if="showItems.find(e => e == 'exportPdf') && pdfExportItems" icon="fas fa-file-pdf" type="danger" styling-mode="outlined" id="botonBarra" @click="exportarPDF()" :hint="'Generar reporte: '+pdfExportItems[0].reportName" />
       <DxButton v-if="showItems.find(e => e == 'add')" icon="add" type="default" styling-mode="contained" id="botonBarra" @click="addEvent" hint="Agregar nuevo" />
   </div>
</template>

<script>
import DxButton from 'devextreme-vue/button'
import DxDropDownButton from 'devextreme-vue/drop-down-button'

export default {
    name: 'ExpedienteGridToolBar',
    components: {
        DxButton,
        DxDropDownButton,
    },
    props: {
        StatusAdmision: String,
        visible: Boolean,
        /**Arreglo con estructura JSON {disabled:false, icon: 'fas-fa', text:'Nombre para la opcion del reporte', reportname: 'nombre del reporte en bd' }*/
        pdfExportItems: Array,
        /**Para los parametros del reporte a generar puede ser de la fila seleccionada o dato en general */
        reportParam: Object,
        /**Arreglo de String con los elementos de la barra de menú a mostrar por defecto se muestran los de agregar, exportar y actualizar */
        showItems: {
            type: Array,
            default: () => {
                return ['add', 'exportPdf', 'refresh']
            }
        },
    },
    methods: {
        exportarPDF(e) {
            this.$reporte_modal({
                Nombre: e ? e.itemData.reportName || e.itemData.name : this.pdfExportItems[0].reportName,
                Opciones: {
                    ...this.reportParam
                }
            })
        },
        addEvent() {
            // if(this.StatusAdmision != 'A')
            //     this.$vs.notify({
            //             time: 4000,
            //             title: 'Agregar Nuevo',
            //             text: 'La admisión no está activa, no se puede agregar nuevo registro',
            //             iconPack: 'feather',
            //             icon: 'icon-alert-circle',
            //             color: 'warning',
            //             position: 'bottom-center'})
            // else
            this.$emit('add')
        }
    },
}
</script>

<style>
#botonBarra,
#botonPDF {
    margin-right: 5px;
}

#botonPDF .dx-button-mode-outlined {
    background-color: transparent;
    border-color: #d43f3a;
}

#botonPDF .dx-buttongroup-item.dx-button.dx-button-mode-outlined.dx-state-hover {
    background-color: rgba(212, 63, 58, .1);
    border-color: #d43f3a;
}

#botonPDF .dx-buttongroup-item.dx-button.dx-button-mode-outlined.dx-state-focused {
    background-color: rgba(212, 63, 58, .1);
    border-color: #d43f3a;
}

#botonPDF .dx-buttongroup-item.dx-button.dx-button-mode-outlined.dx-state-active {
    background-color: rgba(212, 63, 58, .4);
    border-color: #d43f3a;
}

#botonPDF .dx-icon.fas.fa-file-pdf,
#botonPDF .dx-icon.dx-icon-spindown.dx-icon-right {
    color: #d43f3a;
}
</style>
