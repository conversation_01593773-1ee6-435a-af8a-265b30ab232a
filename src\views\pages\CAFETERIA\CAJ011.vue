<template>    
    <vs-tabs alignment="left">        
        <vs-tab label="Consultas">
            <vs-popup title="Confirmación eliminación envío" :active.sync="popUpEliminacion">
                <vs-row class="w-full pb-3" style="color: black; font-size: 14pt;">
                        <p>Justifique por que desea eliminar el envío <b><H3>{{ proformaEliminar?.Proforma }}</H3></b></p>
                </vs-row>
                <vs-row class="w-full">
                    <vs-col class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12 p-1"> 
                        <vs-textarea  type="text" class="w-full" rows="5" counter="200" v-model="mensajeEliminacion" />
                    </vs-col>
                </vs-row>  
                <vs-row>
                    <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1 pt-6"> 
                        <vs-button @click="popUpEliminacion = false" color="warning" class="w-full pl-5 pr-5">
                            Regresar
                        </vs-button>
                    </vs-col>                
                    <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1 pt-6"> 
                        <vs-button @click="eliminar_proforma()" color="danger" icon-pack="feather" icon="icon-settings" class="w-full">
                            Eliminar
                        </vs-button>
                    </vs-col>                     
                </vs-row>
            </vs-popup>
            <div class="tab p-4" label="Consultas" >            
                <h5>Lista Envíos</h5>
                <div class="flex flex-wrap">
                    <div class="w-full md:w-4/12 lg:w-2/12 xl:w-2/12 p-2">
                        <vs-input label="Fecha Inicial" class="w-full" type="date" v-model="info.FiltroFechaInicial" />
                    </div>
                    <div class="w-full md:w-4/12 lg:w-3/12 xl:w-2/12 p-2">
                            <vs-input label="Fecha Final" class="w-full" type="date" v-model="info.FiltroFechaFinal" />
                    </div>
                    <div class="xs:w-full md:2/12 lg:2/12 xl:w-2/12 p-2" style="padding-top: 24px !important;">
                        <vs-button icon-pack="feather" icon="icon-refresh-cw" @click.native="Consulta().actualizarListaProformas()">
                            Actualizar
                        </vs-button>
                    </div>                 
                </div>                    
                <div class="flex flex-wrap">
                    <div class="w-full  p-2">
                        <vs-table2 max-items="8" search pagination :data="listaProformas" height="535px" class="tablaEnvios mt-0 mb-0">
                            <template slot="thead">
                                <th order="CodigoUnico" width="50px">Código Único</th>
                                <th order="Proforma" width="100">Envío</th>
                                <th order="Serie" width="25px">Serie</th>
                                <th order="Admision" width="50px">Admsión</th>
                                <th order="Fecha" width="100px">Fecha</th>
                                <th order="Proveedor"  width="100px">Proveedor</th>
                                <th order="DescripcionAnulacion"  width="200px">Justificación de anulación</th>
                                <th order="Accion" width="150px">Acción</th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data" :class="tr.Estado == 'E'?'filaAnulada':''" class="p-0">
                                    <vs-td2>
                                        {{ tr.CodigoUnico }}                               
                                    </vs-td2>
                                    <vs-td2>
                                        {{tr.Proforma}}
                                    </vs-td2>
                                    <vs-td2>
                                        {{tr.Serie}}
                                    </vs-td2>
                                    <vs-td2>
                                        {{tr.NoAdmision}}
                                    </vs-td2>
                                    <vs-td2>
                                        {{tr.FechaRegistro}}
                                    </vs-td2>
                                    <vs-td2>
                                        {{tr.Proveedor}}
                                    </vs-td2>
                                    <vs-td2>
                                        {{tr.DescripcionAnulacion}}                                        
                                    </vs-td2>
                                    <vs-td2>
                                        <vs-button size="medium" style="margin-left:4px;display:inline-block;" @click.native="imprimir_proforma(tr.CodigoUnico)" icon-pack="fas" icon="fa-print"></vs-button>
                                        <vs-button size="medium" v-if="permisos.eliminar && tr.Estado == 'A'"  color="danger" style="margin-left:14px;display:inline-block;" @click.native="advertencia_eliminacion(tr)" icon-pack="fas" icon="fa-trash"></vs-button>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                </div>
            </div>
        </vs-tab>
        <vs-tab label="Nuevo Envío">
            <vs-popup class="confirmacion-pedido-popup" title="Confirmación carga orden" :active.sync="popUpConfirmacion" style="color: black; font-size: 12pt;">                
                <vs-row class="m-2">{{ "Empresa: "+sesion.sesion_sucursal_nombre}}</vs-row>
                <vs-row class="m-2">{{ "Admisión: "+info.admision }}</vs-row>
                <vs-row class="m-2">{{ "Fecha Envío: "+ info.fechaEnvioPresentacion}}</vs-row>                
                <vs-row class="m-2">{{ "Proveedor: "+ info.nombreProveedor}}</vs-row>
                <vs-row class="m-2"><b>Responsable</b></vs-row>
                <vs-row class="m-2">{{"Corporativo: "+sesion.corporativo+"&nbsp;&nbsp;&nbsp;&nbsp; Nombres: "+sesion.usuario  }}</vs-row>
                <vs-row class="m-2 w-full flex" style="justify-content: center; font-size: 25pt;"><b>{{"Envío Ingresado: "+ info.numeroProformaProveedor }}</b></vs-row>
                <vs-row class="w-full flex" style="justify-content: center;">
                    <div class="w-full  p-2">
                        <vs-table2 max-items="10" pagination :data="cargos" height="470px" class="mt-0 mb-0">
                            <template slot="thead">
                                <th order="Nombre">Nombre</th>
                                <th order="Cantidad" width="50px">Cant.</th>
                                <th order="Precio1" width="140px" style="text-align: center;"> Precio.U IVA Paciente</th>
                                <th order="SubTotal1" width="140px" style="text-align: center;"> Sub-Total</th>
                                <th order="Precio2" width="140px" style="text-align: center;" > Precio.U IVA Proveedor</th>
                                <th order="SubTotal2" width="140px" >Sub-Total</th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2>
                                        <div style="white-space:normal;">
                                            {{ tr.objeto +" "+ tr.medida +" "+ tr.referencia }}
                                        </div>                                            
                                    </vs-td2>
                                    <vs-td2>
                                        {{tr.cantidad}}
                                    </vs-td2>
                                    <vs-td2 style="background-color: rgba(255, 120, 48,0.5);">
                                        {{parseFloat(tr.precioUnitarioIva*info.porcentajeIncremento).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                    </vs-td2>
                                    <vs-td2 style="background-color: rgba(255, 120, 48,0.5);">
                                        {{parseFloat(tr.precioUnitarioIva*info.porcentajeIncremento*tr.cantidad).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                    </vs-td2>
                                    <vs-td2 style="background-color: rgba(0, 154, 48,0.5);">
                                        {{parseFloat(tr.precioUnitarioIva).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                    </vs-td2>
                                    <vs-td2 style="background-color: rgba(0, 154, 48,0.5);">
                                        {{parseFloat(tr.precioUnitarioIva*tr.cantidad).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                    </vs-td2>
                                </tr>
                            </template>
                            <template slot="tfooter">
                                <tr class="foot" style="font-size: 15pt;">
                                    <th colspan="5" scope="row" style="padding-right: 10px;">Total</th>
                                    <td> {{totalCargos.toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}</td>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                </vs-row>               
                <vs-row class="w-full flex pb-2 pt-2" style="justify-content: center;">
                    <div class="8/12" style="font-weight: bold;">
                        Por favor si los datos ingresados del Envío del proveedor son los correctos seleccionar cargar orden,
                        <br>
                        si no están correctos seleccionar regresar.
                    </div>
                </vs-row>
                <vs-row class="w-full flex " style="justify-content: space-evenly;">
                    <vs-button @click="popUpConfirmacion=false;">Regresar</vs-button>                    
                    <vs-button @click="Guardar().cargarOrden()">Aceptar cargar la orden</vs-button>
                </vs-row>
            </vs-popup>
            <div class="tab p-4" label="Nuevo Envío" >                    
                <!-- reportes -->
                <vs-popup id="contentreport" classContent="popup-generar" title="Informe" :active.sync="info.reporte" fullscreen style="z-index:99998;height:100%">
                    <embed v-if="info.reporte_src!=''" type="application/pdf" :src="info.reporte_src" ref="pdfDocument" width="100%" height="98%" />
                </vs-popup>

                <div class="flex flex-wrap">
                    <div class="xs:w-full sm:w-full md:w-5/12 lg:w-3/12 xl:w-2/12 pr-2">
                        <SM-Buscar label="Admisión" v-model="info.admision" api="app/v1_JefeCaja/consulta_admision" :api_campos="['Admision','Paciente']" :api_titulos="['Admision','Paciente']" :api_filtro="{'Activa':1}" api_campo_respuesta="Admision" api_campo_respuesta_mostrar="Admision" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="true" :callback_buscar="Consulta().cargaAdmision" :callback_cancelar="Otros().quitarAdmision" />                                
                    </div>
                    <div class="xs:w-full sm:w-full md:w-5/12 lg:w-4/12 xl:w-3/12 pl-4 pr-4">
                        <vs-input label="Paciente:" class="w-full" :value="info.paciente" disabled />
                    </div>                   
                </div>
        
                <div class="flex flex-wrap">
                    <div class="xs:w-full sm:w-full md:w-5/12 lg:w-3/12 xl:w-2/12 pr-2">
                        <vs-input label="Número de envío del proveedor:" class="w-full"  v-model="info.numeroProformaProveedor" />
                    </div>
                    <div class="xs:w-full sm:w-full md:w-5/12 lg:w-2/12 xl:w-1/12 pl-4 pr-4">
                        <vs-input label="Número único de envío:" class="w-full" :value="info.numeroUnicoProforma" disabled/>
                    </div>
                    <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7 pr-4">                        
                        <vs-input label="Fecha Envío" type="date"  v-model="info.fechaEnvio" name="date1" />
                    </div>
                    <div class="xs:w-full sm:w-full md:w-5/12 lg:w-2/12 xl:w-1/12 pr-2">
                        <SM-Buscar label="Proveedor" v-model="info.codigoProveedor" api="app/v1_JefeCaja/busqueda_proveedor" :api_campos="['Codigo','Nombre','Nit']" :api_titulos="['Codigo','Nombre','Nit']" :api_filtro="{'Activo':'S','Empresa':'SEM','Tipo':'1,5'}" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Codigo" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="true" :callback_buscar="Consulta().cargarProveedor" :callback_cancelar="Otros().limpiarProveedor" />                            
                    </div>
                    <div class="xs:w-full sm:w-full md:w-5/12 lg:w-3/12 xl:w-3/12 pl-4 pr-4">
                        <vs-input label="Nombre proveedor:" class="w-full" :value="info.nombreProveedor" disabled />
                    </div>
                </div>                
                <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                    <form ref="formProducto" method="post" @submit.prevent="handleSubmit(Guardar().cargarProducto())">
                        <div class="flex flex-wrap p-1 mt-4" >
                            <div class="sm:w-full xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12 p-5 mt-1 pb-0" style="background-color:#eee;border:1px solid #ccc;border-radius: 5px;">
                                <h5 class="p-1">Carga de productos</h5>
                                <div style="font-size: small; color: red;">*80 caracteres máximos</div>                        
    
                                <div class="w-full p-1">
                                    <ValidationProvider rules="max:50|required" v-slot="{ errors }">
                                        <vs-input label="Objeto:" class="w-full" v-model="info.producto.objeto" 
                                                        :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"/>
                                    </ValidationProvider>
                                </div>
                                <div class="w-full p-1">
                                    <ValidationProvider rules="max:15|required" v-slot="{ errors }">
                                        <vs-input label="Medida:" class="w-full" v-model="info.producto.medida" 
                                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"/>
                                    </ValidationProvider>
                                </div>
                                <div class="w-full p-1">
                                    <ValidationProvider rules="max:15|required" v-slot="{ errors }">
                                        <vs-input label="Referencia:" class="w-full" v-model="info.producto.referencia" 
                                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"/>
                                    </ValidationProvider>
                                </div>
                                <div class="flex flex-wrap p-1">
                                    <div class="xs:w-full sm:w-full md:w-full lg:w-full xl:w-5/12">
                               <!--          <vs-input label="Cantidad"   
                                                    class="w-full"
                                                    type="number" 
                                                    v-model="info.producto.cantidad" />    -->
                                        <label>Cantidad:</label>
                                        <vs-input-number style="justify-content:left" min="1" v-model="info.producto.cantidad" class="m-2"/>              
                                    </div>  
                                    <div class="xs:w-full sm:w-full md:w-full lg:w-full xl:w-7/12">
                                        <label>Precio U. IVA Proveedor:</label>
                                        <vx-input-group class="mb-base">
                                            <template slot="prepend">
                                            <div class="prepend-text bg-primary">
                                                <span>Q</span>
                                            </div>
                                            </template>
                                            <ValidationProvider rules="numero_minimo:0|numero_decimal:2|required"
                                                    v-slot="{ errors }" >
                                                <vs-input v-model="info.producto.precioUnitarioIva"
                                                        :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"/>
                                            </ValidationProvider>
                                        </vx-input-group>
                                    </div>
                                </div>
                                <div class="flex flex-wrap ">
                                    <div class="xs:w-full md:w-full lg:w-full xl:w-4/12 m-2">
                                        <vs-button color="success" class="m-1" :disabled="info.desactivar"
                                            @click="handleSubmit(Guardar().cargarProducto(invalid))">
                                            Guardar
                                        </vs-button>
                                    </div>
                                    <div class="xs:w-full md:w-full lg:w-full xl:w-4/12 m-2">
                                        <vs-button class="m-1" color="warning" @click="Guardar().limpiarProducto()">
                                            Limpiar
                                        </vs-button>
                                    </div>                                                       
                                </div>
                            </div>
                            <div class="xs:w-full sm:w-full md:w-8/12 lg:w-8/12 xl:w-8/12  p-2">
                                <vs-table2 max-items="10" search pagination :data="cargos" height="470px" class="mt-0 mb-0">
                                    <template slot="thead">
                                        <th order="Nombre">Nombre</th>
                                        <th order="Cantidad" width="50px">Cant.</th>
                                        <th order="Precio1" width="140px" style="text-align: center;"> Precio.U IVA Paciente</th>
                                        <th order="SubTotal1" width="140px" style="text-align: center;"> Sub-Total</th>
                                        <th order="Precio2" width="140px" style="text-align: center;" > Precio.U IVA Proveedor</th>
                                        <th order="SubTotal2" width="140px" >Sub-Total</th>
                                        <th order="Accion" width="50px">Acción</th>
                                        <!-- <th  width="50px">Acción</th> -->
                                        <!-- <th order="Exist" width="50px">Existencia</th> -->
                                    </template>
                                    <template slot-scope="{data}">
                                        <tr :key="indextr" v-for="(tr, indextr) in data">
                                            <vs-td2>
                                                <div style="white-space:normal;">
                                                    {{ tr.objeto +" "+ tr.medida +" "+ tr.referencia }}
                                                </div>                                            
                                            </vs-td2>
                                            <vs-td2>
                                                {{tr.cantidad}}
                                            </vs-td2>
                                            <vs-td2 style="background-color: rgba(255, 120, 48,0.5);">
                                                {{parseFloat(tr.precioUnitarioIva*info.porcentajeIncremento).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                            </vs-td2>
                                            <vs-td2 style="background-color: rgba(255, 120, 48,0.5);">
                                                {{parseFloat(tr.precioUnitarioIva*info.porcentajeIncremento*tr.cantidad).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                            </vs-td2>
                                            <vs-td2 style="background-color: rgba(0, 154, 48,0.5);">
                                                {{parseFloat(tr.precioUnitarioIva).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                            </vs-td2>
                                            <vs-td2 style="background-color: rgba(0, 154, 48,0.5);">
                                                {{parseFloat(tr.precioUnitarioIva*tr.cantidad).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                            </vs-td2>
                                            <vs-td2>
                                                <vs-button color="danger" style="margin-left:1px;display:inline-block;" @click.native="eliminar_producto(indextr,tr.objeto)" icon-pack="fas" icon="fa-trash"></vs-button>
                                            </vs-td2>
                                        </tr>
                                    </template>
                                </vs-table2>
                            </div>
                        </div>
                    </form>
                </ValidationObserver>
    
    
                <!-- CARGOS AGREGADOS -->
                <div class="flex flex-wrap justify-center mt-4">
                    <div class="xs:w-full md:w-8/12 lg:w-8/12 xl:w-8/12 sm:w-8/12 p-2">                
                    </div>
                </div>
                <vs-divider></vs-divider>
                <div class="flex bottom">
                    <vs-button color="danger" class="mr-5" @click="Otros().confirmarBorradoProductos()">Limpiar Orden</vs-button>
                    <vs-spacer></vs-spacer>
                    <div style="width:200px">
                        <tr>
                            <td class="p-3">
                                <h4>TOTAL</h4>
                            </td>
                            <td class="p-3" width="200px" style="text-align:right">
                                <h4>{{totalCargos.toLocaleString("es-GT",  {
                                                                                style: "currency",
                                                                                currency: "GTQ"
                                                                            })
                                    }}
                                </h4>
                            </td>
                        </tr>
                    </div>
                    <vs-spacer></vs-spacer>
                    <vs-button @click="Guardar().validarOrden()" :disabled="info.desactivar || this.cargos.length == 0">Cargar Orden</vs-button>
                </div>
                
            </div>
        </vs-tab>
    </vs-tabs>
</template>
    
    <script>
    import moment from 'moment'
    export default {
        data() {
    
            return {
                mensajeEliminacion:null,
                proformaEliminar:null,
                popUpEliminacion:false,
                popUpConfirmacion:false,
                info: {
                    admision: null,
                    nivelprecio: null,
                    numeroUnicoProforma: 0,
                    paciente: null,
                    tipoOrden: null,
                    numeroProformaProveedor: null,
                    codigoProveedor: null,
                    nombreProveedor: null,
                    empresaProveedor: null,
                    porcentajeIncremento: 0,
                    desactivar: true,
                    fechaCreacion:'',
                    fechaEnvio:'',
                    fechaEnvioPresentacion:'',
                    producto: {
                        objeto: null,
                        medida: null,
                        referencia: null,
                        cantidad: 1,
                        precioUnitarioIva: null         
                    },
                    reporte_src: null,
                    reporte: false,
                    FiltroFechaFinal: null,
                    FiltroFechaInicial: null,
                    NombreAseguradora: null
                },
                cargos: [],
                listaProformas: [],
                texto: 'Agregar',
                permisos: {
                    elimninar: false
                },
                reporteconsultaproforma : [],
                datosResultado : {
                    IdCodigoUnico : null
                }
            }
        },
        computed: {
            totalCargos() {
                return this.cargos.reduce((acumulador, cargo) => acumulador + parseFloat(cargo.cantidad) * cargo.precioUnitarioIva, 0)
            },
            sesion() {
                return this.$store.state.sesion
            }
        },
        methods: {
            Consulta() {
                return {
                    TabSeleccionada: ()=>{
                        
                    },
                    actualizarListaProformas: ()=>{
                        this.axios.post('/app/v1_JefeCaja/obtener_lista_proformas', {
                            fechaInicio: this.info.FiltroFechaInicial && this.info.FiltroFechaInicial != '' ? moment(this.info.FiltroFechaInicial).format('DD/MM/YYYY'): null,
                            fechaFin: this.info.FiltroFechaFinal && this.info.FiltroFechaFinal != '' ? moment(this.info.FiltroFechaFinal).format('DD/MM/YYYY'): null
                        })
                        .then(resp =>{                   
                            if (resp.data.codigo == 0) {
                                this.listaProformas = resp.data.json    
    
                                this.$vs.notify({
                                                title:'Exito',
                                                text:'Se actualizo la lista  de envíos',
                                                color:'success'
                                            })                                                     
                            }
                        })                       
                    },
                    cargarTipoOrden: async ()=>{
                        await this.axios.post('/app/v1_JefeCaja/ObtenerTiposOrdenesPorRol', {
                                                                opcion: "C",
                                                                subOpcion: 3,
                                                                filtroTipoOrdenHospital: 'OR',
                                                                filtroTipoOrdenGeneral: '' 
                                            })
                                    .then( resp => {                                                     
                                        if(resp.data.json && resp.data.json[0]){
                                            this.info.tipoOrden = resp.data.json[0].Codigo                                        
                                        }else{
                                            this.info.tipoOrden = null
                                        }
                                    })
    
                    },
                    cargarListaProformas: ()=>{
                        this.axios.post('/app/v1_JefeCaja/obtener_lista_proformas', {})
                        .then(resp =>{                   
                            if (resp.data.codigo == 0) {
                                this.listaProformas = resp.data.json                                                        
                            }
                        })
                    },
                    cargaAdmision: (datos) => {
                        if(datos.NivelPrecios == null || datos.NivelPrecios == 0){
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'Debe configurar el nivel de precios para la admision seleccionada',
                            })
                        }else{
                            this.info.nivelprecio = datos.NivelPrecios
                            this.info.paciente = datos.Paciente
                            this.info.serie = datos.Serie
                            this.info.codigo = datos.Codigo
                        }
    
                        this.axios.post('/app/v1_JefeCaja/obtener_porcentaje_incremento', {
                            'NivelPrecio':this.info.nivelprecio
                        }).then(resp =>{
                            this.info.porcentajeIncremento = resp.data.json[0].PorcentajeIncremento
                            if(this.info.porcentajeIncremento!=null && this.info.porcentajeIncremento>0){
                                this.info.desactivar = false                              
                                this.Consulta().cargarNumeroProforma()
                            }else{
                                this.$vs.notify({
                                        color: '#B71C1C',
                                        title: 'Alerta',
                                        text: 'Debe configurar el nivel de precios ('+this.info.nivelprecio+') en el mantenimiento de precios de ortopedia.',
                                    })
    
                                this.info.desactivar = true
                            }
                        })

                        if(datos.Nombre.includes('Palig') && (this.info.codigoProveedor == null ? false : this.info.codigoProveedor.trim() == 'ORTO')){
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Alerta',
                                time:8000,
                                text: `De acuerdo con los convenios establecidos entre el proveedor ${ this.info.nombreProveedor } y el seguro ${ datos.Nombre }, no es posible continuar con el proceso. `,
                            })
                            this.info.serie = ''
                            this.info.codigo = ''
                            this.info.admision = ''
                        }else{
                            this.info.NombreAseguradora = datos.Nombre
                        }
                    },
                    cargarProveedor: (datos) => {
                        this.info.codigoProveedor = datos.Codigo
                        this.info.nombreProveedor = datos.Nombre
                        this.info.empresaProveedor = datos.Empresa

                        if((this.info.NombreAseguradora == null ? false : this.info.NombreAseguradora.includes('Palig')) && datos.Codigo.trim() == 'ORTO'){
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Alerta',
                                time:8000,
                                text: `De acuerdo con los convenios establecidos entre el proveedor ${ datos.Nombre.trim() } y el seguro ${ this.info.NombreAseguradora.trim() }, no es posible continuar con el proceso. `,
                            })
                            this.info.codigoProveedor = ''
                            this.info.nombreProveedor = ''
                            this.info.empresaProveedor = ''
                        }
                    },
                    cargarNumeroProforma: ()=>{
                        this.axios.post('/app/v1_JefeCaja/obtener_numero_proforma', {})
                            //  Consultar numero de proforma
                            .then(resp => {
                                if (resp.data.json.length > 0) {
                                    this.info.numeroUnicoProforma = resp.data.json[0].idProforma                                
                                    return this.info.numeroUnicoProforma
                                } else {
                                    return null
                                }
                            })
                            // Validar numero de proforma
                            .then(numeroUnicoProforma => {
                                if (!numeroUnicoProforma) {
                                    this.$vs.notify({
                                        color: '#B71C1C',
                                        title: 'Alerta',
                                        text: 'No se puedo cargar el numero unico de envío',
                                    })
                                    return false
                                }
                            })
                    },
                    init: () => {
                        this.Consulta().cargarListaProformas()
                        this.Consulta().cargarNumeroProforma()
                        this.Consulta().cargarTipoOrden()
                    }
                }
            },
            GetDateValue(value) {
                if (value === undefined) {
                    return '';
                } else if (value === null) {
                    return '';
                } else if (value == '') {
                    return '';
                }
                return moment(value).format('DD/MM/YYYY');          
            },  
            eliminar_producto(indice,nombre) {
                this.$vs.dialog({
                        type: 'confirm',
                        color: 'danger',
                        title: 'Producto',
                        text: `¿Desea eliminar el producto ${nombre}?`,
                        acceptText: 'Aceptar',
                        cancelText: 'Cancelar',
                        accept: () => {
                            this.cargos.splice(indice,1)
                        }
                    })
            },
            imprimir_proforma(codigoUnico){
                this.datosResultado.IdCodigoUnico = codigoUnico
                this.$genera_reporte({
                    Nombre: "Consulta Envio",
                    Data_source: this.datosResultado,
                    Data_report: this.listado_reportes
                }).catch((err) => {
                    this.$vs.notify({
                        title: 'Impresion Envío',
                        color: alert,
                        text: err
                    })
                })
            },
            advertencia_eliminacion(proforma){
                this.mensajeEliminacion = "";
                this.proformaEliminar = proforma;
                this.popUpEliminacion = true;
            },
            eliminar_proforma() {
                if(!this.mensajeEliminacion || this.mensajeEliminacion.trim().length<5 || this.mensajeEliminacion.trim().length>200){
                    this.$vs.notify({
                                        title: 'Error',
                                        text: 'La justificación es obligatoria y no puede exceder de 200 caracteres.',
                                        iconPack: 'feather',
                                        icon: 'icon-alert-circle',
                                        color: 'danger',
                                        position: 'top-center'
                                    })
                    return;
                }

                this.axios.post('/app/v1_JefeCaja/eliminar_proforma_ortopedia', {
                                IOpcion: "E",
                                idProforma:this.proformaEliminar.CodigoUnico,
                                numeroProformaProveedor:this.proformaEliminar.Proforma,
                                admision:this.proformaEliminar.Serie+'-'+this.proformaEliminar.NoAdmision,
                                mensajeEliminacion: this.mensajeEliminacion
                            }).then((resp)=>{
                                if(resp.data.tipo_error != 0){
                                    this.$vs.notify({
                                        title: 'Error',
                                        text: resp.data.json[0].descripcion,
                                        iconPack: 'feather',
                                        icon: 'icon-alert-circle',
                                        color: 'danger',
                                        position: 'top-center'
                                    })
                                }else{
                                    this.popUpEliminacion = false;
                                    this.Consulta().cargarListaProformas()
                                    this.$vs.notify({
                                                title:'Exito',
                                                text:`Se elimino el envío ${this.proformaEliminar.Proforma}`,
                                                color:'success',
                                                position: 'top-center'
                                            })                                        
                                }
                            })
            },
            Guardar() {
                return {
                    cargarProducto: (formaIsInvalid) =>{
                        if(!formaIsInvalid && !this.Guardar().productoInvalido()){
                            this.cargos.push({                            
                                ...this.info.producto
                            })
                            this.Guardar().limpiarProducto();
                        }else{
                            this.$vs.notify({
                                title: this.texto,
                                text: 'Error en los campos para agregar el producto',
                                iconPack: 'feather',
                                icon: 'icon-alert-circle',
                                color: 'danger',
                                position: 'bottom-center'
                            })
                        }
                    },
                    limpiarProducto: () => {
                        this.info.producto.cantidad = 1;
                        this.info.producto.medida = "";
                        this.info.producto.objeto = "";
                        this.info.producto.referencia = "";
                        this.info.producto.precioUnitarioIva = "";
                    },
                    productoInvalido: ()=>{
                        return  this.info.producto.medida == "" || this.info.producto.objeto == "" ||this.info.producto.objeto  == "" ||
                                this.info.producto.referencia == "" || this.info.producto.precioUnitarioIva == ""
                    },
                    confirmarOrden: ()=>{
                        
                        this.$vs.dialog({
                                            type: 'confirm',
                                            color: 'warning',
                                            title: 'Confirmación',
                                            acceptText: 'Aceptar cargar orden',
                                            cancelText: 'Cancelar',
                                            text: `¿Confirma que todos los datos ingresados son correctos para el envío del proveedor ${this.info.numeroProformaProveedor}?`,
                                            accept: () => {
                                                this.Guardar().cargarOrden();
                                            }
                                        })
                    },
                     validarOrden:async ()=>{
                        if (!this.info.numeroProformaProveedor){
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'Debe de ingresar el numero de envío del proveedor',
                            })
                            return false
                        }
    
                        if (!this.info.admision) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'Debe de ingresar una admisión',
                            })
                            return false
                        }

                        if (!this.info.fechaEnvio) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'Debe de ingresar la fecha del envío',
                            })
                            return false
                        }
    
    
                        if (!this.info.nivelprecio || this.info.nivelprecio<=0)  {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'La admisión no tiene configurado un nivel de precio valido',
                            })
                            return false
                        }
    
                        if (!this.info.codigoProveedor) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'Debe de ingresar un proveedor',
                            })
                            return false
                        }
    
                        if (!this.info.porcentajeIncremento || this.info.porcentajeIncremento <= 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'Debe configurar el nivel de precio ('+this.info.nivelprecio+') para los productos de ortopedia',
                            })
                            return false
                        }
    
                        if (!this.cargos || this.cargos.length == 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'Debe realizar al menos un cargo a la orden.',
                            })
                            return false
                        }

                        if (!this.info.tipoOrden) {
                            await this.Consulta().cargarTipoOrden()
                        }                        

                        if (!this.info.tipoOrden) {                            

                            this.$vs.notify({
                                time: 4000,
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'No se pudo obtener un tipo de orden de ortopedia para esta sucursal.',
                            })
                            return false
                        }
                        this.info.fechaCreacion =  moment(await this.$dbdate()).format('DD/MM/YYYY HH:mm');
                        this.info.fechaEnvioPresentacion =  moment(this.info.fechaEnvio).format('DD/MM/YYYY');
                        this.popUpConfirmacion=true
                    },
                    cargarOrden: async () => {
                        if (!this.info.numeroProformaProveedor){
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'Debe de ingresar el numero de envío del proveedor',
                            })
                            return false
                        }
    
                        if (!this.info.admision) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'Debe de ingresar una admisión',
                            })
                            return false
                        }

                        if (!this.info.fechaEnvio) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'Debe de ingresar la fecha del envío',
                            })
                            return false
                        }
    
    
                        if (!this.info.nivelprecio || this.info.nivelprecio<=0)  {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'La admisión no tiene configurado un nivel de precio valido',
                            })
                            return false
                        }
    
                        if (!this.info.codigoProveedor) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'Debe de ingresar un proveedor',
                            })
                            return false
                        }
    
                        if (!this.info.porcentajeIncremento || this.info.porcentajeIncremento <= 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'Debe configurar el nivel de precio ('+this.info.nivelprecio+') para los productos de ortopedia',
                            })
                            return false
                        }
    
                        if (!this.cargos || this.cargos.length == 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'Debe realizar al menos un cargo a la orden.',
                            })
                            return false
                        }

                        if (!this.info.tipoOrden) {
                            await this.Consulta().cargarTipoOrden()
                        }                        

                        if (!this.info.tipoOrden) {                            

                            this.$vs.notify({
                                time: 4000,
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'No se pudo obtener un tipo de orden de ortopedia para esta sucursal.',
                            })
                            return false
                        }
    
    
                        const cargos = this.cargos.map( producto=>{
                                return { nombreProducto: producto.objeto+" "+producto.medida+" "+producto.referencia,
                                        preciosProductoIva: parseFloat(producto.precioUnitarioIva).toFixed(2),
                                        preciosProducto: parseFloat(producto.precioUnitarioIva*this.info.porcentajeIncremento).toFixed(2),
                                        unidadesProducto: producto.cantidad
                                        }
                        })
                        
    
                        this.axios.post('/app/v1_JefeCaja/cargar_proforma_ortopedia', {
                        estructuraOrtopedia : {
                            IOpcion: "I",
                            idProforma:this.info.numeroUnicoProforma,
                            numeroProformaProveedor:this.info.numeroProformaProveedor,
                            admision:this.info.admision,
                            nivelPrecio:this.info.nivelprecio,
                            codigoProveedor:this.info.codigoProveedor,
                            empresaProveedor:this.info.empresaProveedor,
                            montoTotal:this.totalCargos,
                            tipoOrden:this.info.tipoOrden,
                            fechaEnvio:this.GetDateValue(this.info.fechaEnvio),
                            cargos
                        },
                        estructuraOrdenCompra : {
                            productoClase: 'P',
                            produtoSub: 1,
                            departamento: '55',
                            idProveedor: this.info.codigoProveedor,
                            total: this.totalCargos,
                            dias_credito: 60,
                            iddocumento: 3,
                            cant_pagos: 1,
                            cantLinea: cargos.length,
                        }}
                        ).then((resp) => {
                                if (resp.data.codigo == 0) {
                                    this.Otros().limpiar()                             
                                } 
                            }
                        )
                    }
                }
            },
            Otros() {
                return {
                    quitarAdmision: ()=>{
                        this.Otros().limpiar()
                    },
                    limpiar: () => {
                        this.popUpConfirmacion = false;
                        this.info.admision = null
                        this.info.nivelprecio = null                                        
                        this.info.numeroUnicoProforma = 0,
                        this.info.paciente = null
                        this.info.numeroProformaProveedor = null
                        this.info.codigoProveedor = null
                        this.info.nombreProveedor = null
                        this.info.porcentajeIncremento = 0
                        this.info.fechaEnvio = ''
                        this.info.desactivar = true
                        this.info.NombreAseguradora = null
                        this.cargos = []
                        this.Guardar().limpiarProducto()
                    },
                    limpiarProveedor: () =>{
                        this.info.codigoProveedor = null
                        this.info.nombreProveedor = null
                    },
                    confirmarBorradoProductos: ()=>{
                        this.$vs.dialog({
                            type: 'confirm',
                            color: 'danger',
                            title: 'Limpiar Orden',
                            text: `¿Desea borrar todos los productos cargados?`,                            
                            acceptText: 'Aceptar',
                            cancelText: 'Cancelar',
                            accept: () => {
                                this.cargos = []
                            }
                        })
                    }
                }
            }
        },
        mounted() {
            this.Consulta().init()
            this.permisos.eliminar = this.$validar_privilegio('ELIMINAR').status
        },
        async beforeCreate() {
            this.listado_reportes = await this.$recupera_parametros_reporte('Consulta Envio')
        }
    }
    </script>
    <style>
       .confirmacion-pedido-popup.con-vs-popup .vs-popup {
            width: 95% !important;
            height: 95% !important;
        }
        tfoot .foot {
        border-top: 3px dotted rgb(160 160 160);
        background-color: #2c5e77;
        color: #fff;
        }
        
    </style>    

    <style scoped>

    tfoot th {
    text-align: right;
    }
    
    tfoot td {  
    font-weight: bold;
    }
    .smtabla tbody tr:not(.detalle):nth-child(odd) {
        background-color: transparent;
    }
    
    .filaAnulada{
        background-color: rgba(255, 95, 70, 0.5) !important;
    }

    .tablaEnvios tr { 
        line-height: 10px;
        min-height: 10px; 
    }

    .cantidad button {
        height: 25px;
        width: 30px;
        border: 1px solid #ccc;
        border-radius: 5px;
    
    }
    
    .cantidad div {
        display: inline-block;
        height: 25px;
        width: 30px;
        text-align: center;
    }
    </style>
    
    