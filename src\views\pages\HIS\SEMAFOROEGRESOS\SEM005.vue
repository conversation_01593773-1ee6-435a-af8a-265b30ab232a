<template>
  <div id="semContainer" class="sem005-container w-full pr-2">
    <vx-card :title="'Admisiones con egreso'">
    
    
      <!-- Contenido del cuerpo del vx-card -->
      <DxDataGrid
        :dataSource="listaOrdenada"
        id="gridListaEgresos"
        ref="dataGridListadoRef"
        :no-data-text="'Sin registros'"
        :headerFilter="{ visible: true, allowSearch: true }"
       
        :hoverStateEnabled="true"
        :show-borders="true"
        :column-auto-width="true"
        :cellHintEnabled="true"
        @rowDblClick="handleRowClick"
        selection-mode="single"
        @selection-changed="onSelectionChanged"
        height="auto"
        :on-row-prepared="customRowStyle"
        :allow-column-reordering="true"
      >
       
        <DxDataGridColumn
          dataField="EstadoAtencion"
          caption="Estado atención"
          :dataType="'string'"
          :fixed="true"
          :width="100"
        />
        <DxDataGridColumn
          dataField="Tiempo_Gestion"
          caption="Tiempo Gestion
"
          :dataType="'string'"
        />
       <DxDataGridColumn
          dataField="NoEtapa"
           caption="Progreso"
          :cell-template="renderProgress"
          :fixed="true"
          :width="100"
          
        /> 
        <DxDataGridColumn
          dataField="EstadoGestion"
           caption="Estado gestion"
          :cell-template="renderStateColor"
          :fixed="true"
          :width="200"
          
        /> 
        <DxDataGridDxFilterPanel :visible="true" />
        <DxDataGridPaging :page-size="15" />
        <DxDataGridDxColumnFixing :enabled="true" />
        <DxDataGridDxSearchPanel :visible="true" width="400px" />
        <DxDataGridPager :visible="true" />
        <DxDataGridColumn
          dataField="NoAdmision"
          caption="Admisión"
          :fixed="true"
        />
        <DxDataGridColumn
          dataField="TipoAdmision"
          caption="TipoAdmision"
          :dataType="'string'"
        />
        <DxDataGridColumn
          dataField="TipoSeguro"
          caption="Tipo Seguro"
          :dataType="'string'"
        />
         <DxDataGridColumn
          dataField="Habitacion"
          caption="Habitación"
          :dataType="'string'"
        />
        <DxDataGridColumn
          dataField="Servicio"
          caption="Servicio"
          :dataType="'string'"
        />
        <DxDataGridColumn
          dataField="FechaPreEgreso"
          caption="Fecha pre-egreso"
          format="dd/MM/yyyy HH:mm"
          :dataType="'date'"
        />
        <DxDataGridColumn
          dataField="FechaEgresoEnfermeria"
          caption="Fecha egreso-enfermeria"
          format="dd/MM/yyyy HH:mm"
          :dataType="'date'"
        />
        <DxDataGridColumn
          dataField="AceptaCheckIn"
          caption="Acepta CheckIn"
          :dataType="'string'"
        />
        <DxDataGridColumn
          dataField="FechaCheckIn"
          caption="Hora CheckIn"
          format="dd/MM/yyyy HH:mm"
          :dataType="'date'"
        />
        <DxDataGridColumn dataField="Prioridad" caption="Prioridad" />

        <DxDataGridColumn
          dataField="FechaEgreso"
          caption="Fecha egreso"
          format="dd/MM/yyyy HH:mm"
          :dataType="'date'"
        />
        <DxDataGridColumn
          dataField="FechaCalculoCobro"
          caption="Dato cobro"
          format="dd/MM/yyyy HH:mm"
          :dataType="'date'"
        />
        <DxDataGridColumn
          dataField="FechaEntregaEstadoDeCuenta"
          caption="Fecha entrega estado cuenta"
          format="dd/MM/yyyy HH:mm"
          :dataType="'date'"
        />
        <DxDataGridColumn
          dataField="Salida"
          caption="Fecha salida"
          format="dd/MM/yyyy HH:mm"
          :dataType="'date'"
        />


        <DxDataGridColumn
          dataField="ReEvaluarCuenta"
          caption="Re- evaluar cuenta"
          :dataType="'string'"
        />
        <DxDataGridColumn
          dataField="FechaReevaluacionCuenta"
          caption="Fecha reevaluación cuenta"
          format="dd/MM/yyyy HH:mm"
          :dataType="'date'"
        />

        <DxDataGridToolbar>

      
          <DxDataGridItem
            location="after"
            locateInMenu="auto"
            showText="inMenu"
            widget="dxButton"
            :options="refreshButtonOptions"
            hint="'Recargar información'"
          />

          <DxDataGridItem location="after" name="searchPanel"> </DxDataGridItem>
        </DxDataGridToolbar>

        <DxDataGridSelection mode="single" />
      </DxDataGrid>
      <div style="display: flex; flex-wrap: wrap; gap: 10px; align-items: center;">
        
            <div v-for="(color, EstadoGestion) in stateColors" :key="EstadoGestion" style="display: flex; align-items: center; gap: 10px;">
              <div
                :style="{
                  width: '20px',
                  height: '20px',
                  backgroundColor: color,
                  borderRadius: '5px',
                }"
              ></div>
              <span style="font-size: 14px; font-weight: bold;">{{ EstadoGestion }}</span>
              <span style="font-size: 12px; color: gray;">Etapa {{ etapaMapping[EstadoGestion] }}</span>
            </div>
          </div>      
    </vx-card>
    <dx-popup
      :visible.sync="popupAcp"
      :show-title="true"
      :title="popupTitle"
      :width="auto"
      :height="auto"
      :hide-on-outside-click="false"
      :resizeEnabled="true"
      :show-close-button="true"
      
      @initialized="onContentReady"     
    :wrapperAttr="{ class: 'custom-popup-wrapper' }"
     @shown="enfocarDiv"
    >




    <DxScrollView   
      class="sem005-container" 
        :scroll-by-content="true"  
      >
    
 
    <div class="sem005-container" >
  
        <vx-card >

          <div style="display: flex; align-items: center; gap: 10px;" >
        <!-- Encabezado -->
         
        <h3>% Progreso ACP</h3>
        <!-- Contenedor de la barra de progreso -->
        <div ref="popupProgressBar" style="flex-grow: 1;" tabindex="0" class="contenido-card"></div>
      </div>
        <div style="display: flex; align-items: center; gap: 20px;">
      <p v-if="selectedRow" style="text-align: left; margin: 0;"> Admisión: <strong>{{ selectedRow.NoAdmision }}</strong></p>
      <p v-if="selectedRow" style="text-align: left; margin: 0;">Habitación: <strong>{{ selectedRow.Habitacion }}</strong>
    </p>
  </div>
          <div class="flex-container sem005-container ">

            <div class="flex w-full h-full sem005-container">

              <!-- Primera columna -->
              <div class="flex-1 bg-blue-200 p-4 sem005-container">
                <!-- Primera columna -->
                <div class="sem005-container"
                  v-if="selectedRow && Object.keys(selectedRow).length > 0"
                  
                >
                  <p style="text-align: left">
                    Etapa Actual:
                    <strong>{{ selectedRow.EstadoGestion }}</strong>
                  </p>
                  <p style="text-align: left">
                    Seguro: <strong>{{ selectedRow.NombreSeguro }}</strong>
                  </p>
                  <p style="text-align: left">
                    Hora pre-egreso:
                    <strong>{{
                      formatFecha(selectedRow?.FechaPreEgreso)
                    }}</strong>
                  </p>
                </div>
              </div>

              <!-- Segunda columna -->
              <div class="flex-1 bg-green-200 p-4 sem005-container">
                <!-- Segunda columna -->
                <div class=".columna">
                  <p style="text-align: left">
                    Tiempo en etapa actual:
                    <strong>{{ selectedRow?.Tiempo_en_etapa_actual }}</strong>
                  </p>
                  <p style="text-align: left">
                    Servicio: <strong>{{ selectedRow?.Servicio }}</strong>
                  </p>
                  <p style="text-align: left">
                    Salida:
                    <strong>{{ formatFecha(selectedRow?.FechaEgreso) }}</strong>
                  </p>
                </div>
              </div>

              <!-- Tercera columna -->
              <div class="flex-1 bg-yellow-200 p-4 sem005-container" >
                <!-- Tercera columna -->
                <div class="sem005-container">
                  <p style="text-align: left">
                    Prioridad: <strong>{{ selectedRow?.Prioridad }}</strong>
                  </p>
                  <p style="text-align: left">
                    Dato de cobro:
                    <strong>{{
                      formatFecha(selectedRow?.FechaCalculoCobro)
                    }}</strong>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </vx-card>
        <!-- Segunda CARD -->

        <vx-card class="custom-card sem005-container">
          <div class="flex-container">
            <div class="flex w-full h-full">
              <!-- Primera columna -->
              <div class="flex-1 bg-blue-200 p-4 sem005-container">
                <!-- Primera columna -->
                <div
                  v-if="selectedRow && Object.keys(selectedRow).length > 0"
                  class=".columna"
                >
                  <p style="text-align: left">
                    Paciente acepta CheckIn:
                    <label>
                      <input
                        type="radio"
                        name="checkIn"
                        value="SI"
                        v-model="formRow.AceptaCheckIn"
                        :disabled="formRow.ActivarCheckIn === 0"
                        @click="alternarCheckInRadio('SI')"
                      />
                      Sí
                    </label>
                    <label>
                      <input
                        type="radio"
                        name="checkIn"
                        value="NO"
                        v-model="formRow.AceptaCheckIn"
                        :disabled="formRow.ActivarCheckIn === 0"
                        @click="alternarCheckInRadio('NO')"
                      />
                      No
                    </label>
                  </p>
                  <p style="text-align: left">
                    Número de autorización:
                    <input
                      type="text"
                      ref="popupInput"
                      v-model="numeroAutorizacion"
                      placeholder="Ingrese el número de autorización"
                      :disabled="formRow.ActivarCheckIn === 0"
                    />
                  </p>
                </div>
              </div>

              <!-- Segunda columna -->
              <div class="flex-1 bg-green-200 p-4 sem005-container">
                <!-- Segunda columna -->
                <div class=".columna">
                  <vs-button
                    class="w-full"
                    color="success"
                    type="filled"
                    icon-pack="fas"
                    icon="fa-book"
                    title="Marca como entregado el estado de cuenta"
                    :disabled="formRow.ActivarEntregaEstadoCuenta === 0"
                    @click="entregarEstadoDeCuenta"
                  >
                    Estado de cuenta entregado
                  </vs-button>
                </div>
              </div>

              <!-- Tercera columna -->
              <div class="flex-1 bg-yellow-200 p-4 sem005-container">
                <!-- Tercera columna -->
                <div class=".columna">
                  <p style="text-align: left">
                    Reevaluación de cuenta
                    <label>
                      <input
                        type="radio"
                        name="Reevaluar"
                        value="SI"
                        v-model="ReevaluarCuenta"
                        :disabled="formRow.ActivarReevaluacion === 0"
                        @click="toggleRadio('SI', 'ReevaluarCuenta')"
                      />
                      Sí
                    </label>

                    <!--             <label>
                  <input
                  visible="false"
                    type="radio"
                    name="Reevaluar"
                    value="NO"
                    v-model="ReevaluarCuenta"
                    :disabled="formRow.ActivarReevaluacion === 0"
                  />
                  No
                </label> -->
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div style="margin-top: 20px; display: flex; justify-content: left" class="sem005-container">
            <div class="btn-group pl-4 sem005-container">
              <vs-button
                class="w-full"
                color="success"
                type="filled"
                icon-pack="fas"
                icon="fa-save"
                @click="Guardar"
                title="Graba los datos para el seguimiento del monitor ACP"
              >
                Guardar
              </vs-button>
            </div>
            <div class="btn-group pl-4">
              <vs-button
                class="w-full"
                color="danger"
                type="filled"
                icon-pack="fas"
                icon="fa-times"
                title="Cancelar la solicitud"
                @click="popupCancelar = true"
              >
                Cancelar
              </vs-button>
            </div>
          </div>
        </vx-card>
        <!--       <vx-card>
          <div style="margin-top: 20px; display: flex; justify-content: center">
            <div class="btn-group pl-4">
              <vs-button
                class="w-full"
                color="success"
                type="filled"
                icon-pack="fas"
                icon="fa-save"
                @click="Guardar"
                title="Graba los datos para el seguimiento del monitor ACP"
              >
                Guardar
              </vs-button>
            </div>
            <div class="btn-group pl-4">
              <vs-button
                class="w-full"
                color="danger"
                type="filled"
                icon-pack="fas"
                icon="fa-times"
                title="Cancelar la solicitud"
              >
                Cancelar
              </vs-button>
            </div>
          </div>
        </vx-card> -->
   
    </div>
  </DxScrollView>
    </dx-popup>
    <dx-popup
      :visible.sync="popupGuardar"
      :show-title="true"
      title="Registro de cambios"
      width="auto"
      max-width="100%"
      height="auto"
      max-height="100%"
      :wrapperAttr="{ class: 'custom-popup-wrapper' }"
    >
      <p>{{ this.MensajeGuardar }}</p>
      <div class="sem005-container" style="margin-top: 20px; display: flex; justify-content: center">
        <div class="btn-group pl-4">
          <vs-button
            class="w-full"
            color="success"
            type="filled"
            icon-pack="fas"
            icon="fa-save"
            title="Graba cambios"
            @click="this.GuardarCambios"
          >
            Guardar
          </vs-button>
        </div>
        <div class="btn-group pl-4">
          <vs-button
            class="w-full"
            color="danger"
            type="filled"
            icon-pack="fas"
            icon="fa-times"
            @click="popupGuardar = false"
            title="Cancelar la solicitud"
          >
            Cancelar
          </vs-button>
        </div>
      </div>
    </dx-popup>

    <dx-popup
      :visible.sync="popupCancelar"
      :show-title="true"
      title="Cancelar cambios"
      width="auto"
      max-width="100%"
      height="auto"
      max-height="100%"
      :wrapperAttr="{ class: 'custom-popup-wrapper' }"
    >
      <p style="text-align: center">
        Los cambios realizados no se guardarán, <br />¿Desea continuar?
      </p>
      <div style="margin-top: 20px; display: flex; justify-content: center">
        <div class="btn-group pl-4">
          <vs-button
            class="w-full"
            color="success"
            type="filled"
            title="Cancela todos los cambios y retorna a la pantalla principal"
            @click="controlCierrePopup"
          >
            Si
          </vs-button>
        </div>
        <div class="btn-group pl-4">
          <vs-button
            class="w-full"
            color="danger"
            type="filled"
            @click="popupCancelar = false,enfocarDiv()"
            title="Regresa a la pantalla de seguimiento"
          >
            No
          </vs-button>
        </div>
      </div>
    </dx-popup>
  </div>
</template>
<script>
export default {
  name: "CardWithColumns",
  data() {
    return {
      notificationOptions: {
        time: 4000,
        title: "Monitor ACP",
        iconPack: "feather",
        icon: "icon-alert-circle",
        position: "top-center",
      },
       
       stateColors: {
        "Ofrecimiento Check In": "#a6f0e3", // color-ebi-turquesa-100
        "Egreso enfermería": "#B2C3D1", // color-bi-experto-100
        "Egreso residentes": "#ABD1D8", // color-bi-financiero-200
        "Auditoría de cuenta y autorización del seguro": "#99E6EE", // color-bi-digital-200
        "Dato de cobro": "#FFF1D2", // color-bi-optimista-100
        "Entrega Estado de Cuenta": "#8ed1fc", // wp--preset--color--pale-cyan-blue
        "Cobro del servicio": "#00C1D4", // wp--preset--color--vivid-cyan-blue
      },
      etapaMapping: {
        "Ofrecimiento Check In": 1,
        "Egreso enfermería": 2,
        "Egreso residentes": 3,
        "Auditoría de cuenta y autorización del seguro": 4,
        "Dato de cobro": 5,
        "Entrega Estado de Cuenta": 6,
        "Cobro del servicio": 7,
      },
       progressColor: "#00C1D4", // Color para barra de progreso
      totalSteps: 7, // Número total de etapas

      selectedRow: null, // almacenar datos seleccionados en grid
      formRow: {}, //para el popup
      dsListaEgresos: [],
      datosEgresos: [],
      ReevaluarCuenta: null,
      AceptaCheckIn: null,
      numeroAutorizacion: 0,
      RegistroProcesado: false,
      MensajeGuardar: "",
      refreshButtonOptions: {
        icon: "refresh",
        text: "Refresh",
        onClick: () => {
          this.CargarSolicitudes();
        },
      },
      popupAcp: false,
      popupGuardar: false,
      popupCancelar: false,
      popupTitle: "Bitacora ACP",
      popupControl: false,
      firstLoad: true,
    }; /* return */
  } /* data */,

  methods: {
    CargarSolicitudes() {
      this.dsListaEgresos = [];
      this.axios
        .post("/app/v1_SemaforoEgresos/ListaEgresosACP", {})
        .then((resp) => {
          this.dsListaEgresos = resp.data;
          
        });
        this.mensajeActualizacion();
    },
    mensajeActualizacion() {
      this.$vs.notify({
        time: 4000,
        title: "Información actualizada",
        text: "se actualizo el listado de admisiones egresos",
        iconPack: "feather",
        icon: "icon-alert-circle",
        color: "success",
        position: "top-center",
      });
    },
    handleRowClick(e) {
      this.popupTitle =
        "Bitácora ACP -Admisión " +
        e.data.NoAdmision +
        " tiempo de gestión de egreso " +
        e.data.Tiempo_Gestion;
      this.popupAcp = true;
      this.ReevaluarCuenta = null;

      this.MensajeGuardar = "";
      this.selectedRowData = e.data; // Obtener datos de la fila seleccionada
      this.isPopupVisible = true;

      // Renderizar la barra de progreso dentro del popup
      this.$nextTick(() => {
        const container = this.$refs.popupProgressBar;
        container.innerHTML = ""; // Limpiar contenido previo
        const etapaNumber = this.selectedRowData.NoEtapa;
        const progressPercentage = Math.round((etapaNumber / this.totalSteps) * 100);

        const divWrapper = document.createElement("div");
        divWrapper.style.width = "100%";
        divWrapper.style.backgroundColor = "#003865";
        divWrapper.style.height = "20px";
        divWrapper.style.position = "relative";
        divWrapper.style.borderRadius = "10px"; // Borde redondeado
        const progressDiv = document.createElement("div");
        progressDiv.style.width = `${progressPercentage}%`;
        progressDiv.style.backgroundColor = this.progressColor;
        progressDiv.style.height = "100%";
        progressDiv.style.borderRadius = "10px"; // Borde redondeado para la barra también

        const textSpan = document.createElement("span");
        textSpan.textContent = `${progressPercentage}%`;
        textSpan.style.position = "absolute";
        textSpan.style.top = "0";
        textSpan.style.left = "50%";
        textSpan.style.transform = "translateX(-50%)";
        textSpan.style.color = "white";
        textSpan.style.fontSize = "12px";
        textSpan.style.fontWeight = "bold";
        textSpan.style.lineHeight = "20px";

        divWrapper.appendChild(progressDiv);
        divWrapper.appendChild(textSpan);
        container.appendChild(divWrapper);
      });
    },

    Guardar() {
      this.MensajeGuardar = "";

      // Usa notificationOptions desde el data
      //validar que la admision tenga checkIn
      if (!this.formRow.AceptaCheckIn) {
        this.$vs.notify({
          ...this.notificationOptions,
          text: "Debe seleccionar si el paciente acepta o no Check In.",
          color: "warning",
        });
        return;
      }

      //validar que AceptaCheckIn tenga un valor diferente al inicial

      if (this.formRow.AceptaCheckIn != this.selectedRow.AceptaCheckIn) {
        this.MensajeGuardar = "¿Seguro de registrar el Check In?";
        if (
          this.numeroAutorizacion === 0 &&
          this.formRow.AceptaCheckIn === "SI"
        ) {
          this.$vs.notify({
            ...this.notificationOptions,
            text: "Ingrese el número de autorización para poder continuar",
            color: "warning",
          });
          return;
        }
        // this.RegistrarCheckIn()
        this.popupGuardar = true;
        return;
      }

      if (
        this.formRow.ActivarEntregaEstadoCuenta === 0 &&
        this.ReevaluarCuenta !== null
      ) {
        this.MensajeGuardar =
          "¿Seguro de registrar la re-evaluacion de cuenta?";
        this.popupGuardar = true;
        return;
        // this.RegistrarRevalucion()
      }

      this.$vs.notify({
        ...this.notificationOptions,
        text: "No hay cambios para procesar",
        color: "warning",
      });
      return;
    },
    onSelectionChanged(e) {
      if (e.selectedRowsData.length > 0) {
        this.selectedRow = e.selectedRowsData[0] || null;
        this.formRow = { ...this.selectedRow };

        if (this.formRow.NoAutorizacion === null) {
          this.numeroAutorizacion = 0;
        } else {
          this.numeroAutorizacion = this.formRow.NoAutorizacion;
        }
      }
    },
    formatFecha(fecha) {
      if (!fecha) return ""; // Verifica si la fecha es válida
      const date = new Date(fecha);
      return date.toLocaleString("es-ES", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    },
    entregarEstadoDeCuenta() {
      this.MensajeGuardar = "";
      if (this.formRow.ActivarEntregaEstadoCuenta === 0) {
        this.$vs.notify({
          ...this.notificationOptions,
          text: "Operación no permitida!!!!, el estado de cuenta ya fue entregado",
          color: "warning",
        });
        return;
      }
      this.MensajeGuardar =
        "¿Seguro registrar como entregado el estado de cuenta?";
      this.popupGuardar = true;
    },

    RegistrarCheckIn() {
      // Validar si ActivarCheckIn o numeroAutorizacion tienen valores válidos
      const isValidCheckIn = this.formRow.ActivarCheckIn;
      const AceptoChekIN = this.formRow.AceptaCheckIn === "SI" ? 1 : 0;
      if (isValidCheckIn) {
        this.SeguimientoMonitorACP(
          this.formRow.Serie,
          this.formRow.Admision,
          3, // Funcionalidad
          AceptoChekIN,
          this.numeroAutorizacion,
        );
        if (this.RegistroProcesado) {
          formRow.ActivarEntregaEstadoCuenta = 1;
        }
      } else {
        // Notificar si la condición no se cumple
        this.$vs.notify({
          ...this.notificationOptions,
          text: "Debe activar el CheckIn e ingresar un número de autorización válido.",
          color: "warning",
        });
      }
    },

    RegistrarRevalucion() {
      // Validar si ActivarCheckIn o numeroAutorizacion tienen valores válidos
      const isValidReval = this.formRow.ActivarEntregaEstadoCuenta;
      const reEvaluarCuentaValue = this.ReevaluarCuenta === "SI" ? 1 : 0;
      if (isValidReval === 0) {
        this.SeguimientoMonitorACP(
          this.formRow.Serie,
          this.formRow.Admision,
          5, // Funcionalidad
          reEvaluarCuentaValue,
          0,
        );
        if (this.RegistroProcesado) {
          this.$vs.notify({
            ...this.notificationOptions,
            text: `${data.descripcion}`,
            color: "success",
          });
          formRow.ActivarEntregaEstadoCuenta = 1;
        }
      } else {
        // Notificar si la condición no se cumple
        this.$vs.notify({
          ...this.notificationOptions,
          text: "Para procesar la reevalución debe estar el estado de cuenta entregado",
          color: "warning",
        });
      }
    },

    SeguimientoMonitorACP(
      SerieAdmision,
      Admision,
      Funcionalidad,
      Seleccion,
      Autorizacion,
    ) {
      // Inicializar estado
      this.RegistroProcesado = false;

      // Construcción del cuerpo de la solicitud
      const requestData = {
        SerieAdmision,
        Admision,
        Funcionalidad,
        Seleccion,
        Autorizacion,
      };

      // Solicitud POST a la API
      this.axios
        .post("/app/v1_SemaforoEgresos/InsertarSeguimiento", requestData)
        .then((response) => {
          // Validación de la respuesta
          const data = response.data[0];

          if (data.codigo === 0) {
            this.RegistroProcesado = true;
            this.CargarSolicitudes();
            this.$vs.notify({
              ...this.notificationOptions,
              text: `${data.descripcion}`,
              color: "success",
            });
          } else {
            // Manejo de respuesta por si las moscas
            this.$vs.notify({
              ...this.notificationOptions,
              text: `${data.descripcion}`,
              color: "warning",
            });
          }
        })
        .catch((error) => {
          // Manejo de errores en la solicitud
      
          this.$vs.notify({
            ...this.notificationOptions,
            text: "Ocurrió un error en la conexión " + error,
            color: "danger",
          });
        });
    },
    toggleRadio(value, model) {
      // Verificar si el valor actual del modelo ya es igual al valor seleccionado
      if (this.$data[model] === value) {
        this.$data[model] = null; // Desmarcar el radio button
      } else {
        this.$data[model] = value; // Marcar el radio button
      }
    },
    alternarCheckInRadio(value) {
      // Si el radio ya está seleccionado, lo desmarca
      if (this.formRow.AceptaCheckIn === value) {
        this.formRow.AceptaCheckIn = null; // Desmarca el radio button
      } else {
        this.formRow.AceptaCheckIn = value; // Marca el radio button
      }
    },
    GuardarCambios() {
      if (this.MensajeGuardar === "¿Seguro de registrar el Check In?") {
        this.RegistrarCheckIn(); //registrar el check in
        this.popupGuardar = false;
        this.popupAcp = false;
      }

      if (
        this.MensajeGuardar ===
        "¿Seguro de registrar la re-evaluacion de cuenta?"
      ) {
        this.RegistrarRevalucion();
        this.popupGuardar = false;
        this.popupAcp = false;
      }
      if (
        this.MensajeGuardar ===
        "¿Seguro registrar como entregado el estado de cuenta?"
      ) {
        this.SeguimientoMonitorACP(
          this.formRow.Serie,
          this.formRow.Admision,
          4,
          1,
          0,
        );

        this.popupGuardar = false;
        this.popupAcp = false;
      }
    },
    CancelarCambios() {
      //si no ha realizado cambios no preguntar
      if ((this.MensajeGuardar = "")) {
        this.popupAcp = false;
      } else {
        // Notificar si esta seguro de cancelar
        this.$vs.notify({
          ...this.notificationOptions,
          text: "Debe activar el CheckIn e ingresar un número de autorización válido.",
          color: "warning",
        });
      }
    },
   
    // Cierra ambos popups
    closeFirstPopup() {
    
      this.popupCancelar = false; // Cierra el segundo popup
      this.popupAcp = false; // Cierra el primer popup
    },

    // Solo cierra el segundo popup
    cancelClose() {
     
      this.popupCancelar = false; // Cierra el segundo popup, manteniendo el primero abierto
    },
    handlePopupHiding(e) {
      // Usa try/catch para manejar e.event en caso de que no esté definido
      try {
       
        if (e.event && e.event.key === "Escape") {
          e.cancel = true; // Cancela el cierre si es necesario
        } else {
       
          if (this.popupControl === false) {
            e.cancel = false;
          } else {
            e.cancel = true;
          }
        }
      } catch (error) {
     }
    },

    controlCierrePopup() {
      this.popupControl = true;
      this.popupCancelar = false;
      this.popupAcp = false;
      this.formRow = {};
      this.$refs["dataGridListadoRef"].instance.deselectAll();
      this.$refs["dataGridListadoRef"].instance.clearFilter();
    },
    onContentReady(e) {
      // Verificar si es la primera carga
      e.component.registerKeyHandler("escape", function (arg) {
        arg.stopPropagation();
      });
    },
    // Genera la barra de progreso con el porcentaje
    renderProgress(container, options) {
      const etapaNumber = options.data.NoEtapa; // Obtiene el número de etapa actual
      
      const progressPercentage = Math.round((etapaNumber / this.totalSteps) * 100); // Calcula el porcentaje redondeado

      // Crear contenedor de la barra de progreso
      const divWrapper = document.createElement("div");
      divWrapper.style.width = "100%";
      divWrapper.style.backgroundColor = "#003865";
      divWrapper.style.height = "20px";
      divWrapper.style.position = "relative"; // Para posicionar el texto encima de la barra
      divWrapper.style.borderRadius = "10px"; // Borde redondeado
      // Crear barra de progreso
      const progressDiv = document.createElement("div");
      progressDiv.style.width = `${progressPercentage}%`; // Ajusta el ancho dinámicamente
      progressDiv.style.backgroundColor = this.progressColor; // Color de la barra
      progressDiv.style.height = "100%";
      progressDiv.style.borderRadius = "10px"; // Borde redondeado para la barra también
      // Crear texto del porcentaje
      const textSpan = document.createElement("span");
      textSpan.textContent = `${progressPercentage}%`; // Muestra el porcentaje
      textSpan.style.position = "absolute";
      textSpan.style.top = "0";
      textSpan.style.left = "50%";
      textSpan.style.transform = "translateX(-50%)";
      textSpan.style.color = "white"; // Color del texto
      textSpan.style.fontSize = "12px";
      textSpan.style.fontWeight = "bold";
      textSpan.style.lineHeight = "20px"; // Centrar verticalmente

      // Agregar elementos al contenedor
      divWrapper.appendChild(progressDiv);
      divWrapper.appendChild(textSpan);
      container.appendChild(divWrapper); // Adjuntar al contenedor
    },
        customRowStyle(e) {
      if (e.rowType === 'data') {
        const index = e.rowIndex
        // Alternar entre dos colores
        e.rowElement.style.backgroundColor = (index % 2 === 0)
          ? '#EAF3F5'// gris palido
          : '#ffffff' // blanco
      }
    },
    renderStateColor(container, options) {
      const state = options.data.EstadoGestion; // Obtener el estado actual
      const color = this.stateColors[state] || "black"; // Color por defecto si no se encuentra

      // Crear elemento para el fondo coloreado
      const stateElement = document.createElement("div");
      stateElement.textContent = state;
      stateElement.style.backgroundColor = color; // Aplicar color de fondo
      stateElement.style.color = "#003865"; // Color del texto dentro del fondo
      stateElement.style.borderRadius = "10px"; // Bordes redondeados
      stateElement.style.padding = "5px 10px"; // Espaciado interno
      stateElement.style.textAlign = "center"; // Alinear el texto al centro
      stateElement.style.fontWeight = "bold"; // Resaltar el texto
      stateElement.style.display = "inline-block"; // Mantener el tamaño del rectángulo ajustado al texto
      container.appendChild(stateElement);
    },
    updatePopupDimensions() {
      // Forzar actualización del tamaño del popup
      this.$forceUpdate();
    },
    handleEscape(event) {
      if (event.key === "Escape" & this.popupAcp) {
        event.preventDefault();
        this.popupCancelar = true; // Muestra la ventana de confirmación
      }
    },
   
    //dx-closebutton
    enfocarDiv() {
      this.$nextTick(() => {
        if (this.$refs.popupProgressBar) {
          this.$refs.popupProgressBar.focus(); // Asigna el foco al div dentro del VxCard
        }
      });
    },
    
  } /* methods*/,
  mounted() {
    // Escucha eventos de redimensionamiento de la ventana
    window.addEventListener("resize", this.updatePopupDimensions);

    window.addEventListener("keydown", this.handleEscape);
  },
  beforeDestroy() {
    // Elimina el evento de redimensionamiento cuando se destruye el componente
    window.removeEventListener("resize", this.updatePopupDimensions);

    window.removeEventListener("keydown", this.handleEscape);
  },
  computed: {
    PopupTitle() {
      return this.dsListaEgresos.join(" - "); // Une los valores del array con un guion
    },
    listaOrdenada() {
      return this.dsListaEgresos.sort((a, b) => {
        if (a.EstadoAtencion < b.EstadoAtencion) return 1;
        if (a.EstadoAtencion > b.EstadoAtencion) return -1;
        // Si EstadoAtencion es igual, ordenar por FechaEgreso (descendente)

        if (new Date(a.FechaPreEgreso) > new Date(b.FechaPreEgreso)) return -1;
        if (new Date(a.FechaPreEgreso) < new Date(b.FechaPreEgreso)) return 1;

        return 0;
      });
    },
  },
}; /* export default */
</script>
<style scoped>
/* Estilos aplicados solo dentro de este componente */
/* cambiar de color al pasar el cursor sobre el grid */
.sem005-container
  :deep(
    .dx-datagrid-rowsview
      .dx-row.dx-data-row.dx-column-lines.dx-state-hover
      > td
  ) {
  background-color: #79e4ff !important;
  color: black !important;
  font-size: 16px;
}
/* Para que el filtro funcione se deben pintar los*/
/* cambiar de color al filtro cuando este vacio*/
.sem005-container :deep(.dx-datagrid .dx-header-filter-empty)
{
  color: #FFB81C !important;

}
/* cambiar de color al filtro*/
.sem005-container :deep(.dx-datagrid .dx-header-filter) {
    position: relative;
    color: #5cb85c;
    font: 14px / 1 DXIcons;
}

/* cambiar de color al pencabezado */
.sem005-container :deep( .dx-header-row) {
  background-color: #008fbe !important;
  color: rgb(240, 236, 236) !important;
  font-weight: bold;
}




#gridListaEgresos {
  height: 450px;
}

.sem005-container .custom-card {
  padding: 5px;
  border-radius: 20px;
  background-color: #ffffff;
}

/* Estilos para las columnas */
.flex-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: space-between;
}



/* Adaptabilidad para pantallas pequeñas */
@media (max-width: 768px) {
  .sem005-container :deep(.columna) {
    flex: 1 1 100%;
  }
}

@media (max-width: 480px) {
  .columna {
    padding: 5px;
  }

}
.dx-popup-content input:focus, 
.dx-popup-content button:focus {
  box-shadow: 0px 5px 10px  #008fbe;
  outline: none;
  border-radius: 12px; /* Bordes redondeados */
}
</style>
<style >
/* los estilos quedan fuera ya que no se aplicaban correctamente */
.custom-popup-wrapper .dx-popup-title {
  background-color: #008fbe;
    color: #ffffff;
    font-weight: bold;
}


.mi-vxcard:focus {
  box-shadow: 0px 0px 10px rgba(0, 0, 255, 0.6); /* Sombra azul al recibir foco */
  outline: none;
}
.contenido-card:focus {
  box-shadow: 0px 0px 5px rgba(0, 0, 255, 0.6); /* Sombra azul cuando recibe el foco */
  outline: none;
  border-radius: 12px; /* Bordes redondeados */
}
</style>