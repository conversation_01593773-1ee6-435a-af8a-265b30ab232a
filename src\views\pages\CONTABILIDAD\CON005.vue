<template>
<vx-card title="Contabilidad General">

    <vs-divider class="label-size">PERÍODOS / CORRELATIVOS PARA LAS PARTIDAS DE DIARIO</vs-divider>
    <div class="content content-pagex">
        <div>
            <div class="flex flex-wrap">
                <div class="w-12/12 lg:w-12/12 xl:w-6/12">
                    <vs-table2 tooltip max-items="10" pagination :data="ListaPeriodos" search id="tb_departamentos">
                        <template slot="thead">
                            <th width="50">Código</th>
                            <th width="200">Descripción</th>
                            <th width="100">Inicio</th>
                            <th width="100">Final</th>
                            <th width="100">Siguiente Partida</th>
                            <th width="30">Editar</th>
                            <!-- <th width="30">Eliminar</th> -->
                        </template>
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 noTooltip :data="data[indextr].CodigoPeriodo" align="center">
                                    {{data[indextr].CodigoPeriodo}}
                                </vs-td2>
                                <vs-td2 :data="data[indextr].Descripcion">
                                    {{data[indextr].Descripcion}}
                                </vs-td2>
                                <vs-td2 :data="data[indextr].FechaInicial" align="center">
                                    {{data[indextr].FechaInicial}}
                                </vs-td2>
                                <vs-td2 :data="data[indextr].FechaFinal" align="center">
                                    {{data[indextr].FechaFinal}}
                                </vs-td2>
                                <vs-td2 noTooltip :data="data[indextr].SigPartida" align="center">
                                    {{data[indextr].SigPartida}}
                                </vs-td2>
                                <vs-td2 noTooltip align="center">
                                    <vs-button color="success" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" @click="EditarPeriodo(tr)"></vs-button>
                                </vs-td2>
                                <!-- <vs-td2 align="center">
                                    <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="EliminarRegistro(tr)"></vs-button>
                                </vs-td2> -->
                            </tr>
                        </template>
                    </vs-table2>
                </div>
                <br>
                <div class="md:w-12/12 lg:w-6/12 xl:w-6/12" style="float:left;padding:100px 100px">

                    <!-- <div style="margin: 15px" class="flex flex-wrap">
                        <vs-button color="primary" type="filled" icon-pack="fas" icon="fa-plus" @click="NuevoPeriodo()"> Nuevo</vs-button>
                        <vs-button color="warning" style="float:left;margin: 5px" type="filled" icon-pack="feather" icon="icon-layers" @click="GenerarPeriodos()"> Generar</vs-button>

                    </div> -->

                    <vs-divider class="label-size">Período</vs-divider>
                    <div class="flex flex-wrap">
                        <div class="md:w-12/12 lg:w-12/12 xl:w-6/12" style="padding:10px 20px">
                            <ValidationProvider name="Código" rules="required|max:8" v-slot="{ errors }" class="required">
                                <label class="label-size"> Código</label>
                                <vs-input v-model="CodigoPeriodo" type="number" align="center" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="BloquearCodigo" />
                            </ValidationProvider>
                        </div>

                        <div class="md:w-12/12 lg:w-12/12 xl:w-6/12" style="padding:10px 20px">
                            <ValidationProvider name="Nombre" rules="required|max:40" v-slot="{ errors }" class="required">
                                <label class="label-size"> Descripción</label>
                                <vs-input v-model="NombrePeriodo" align="center" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="BloquearCodigo" />
                            </ValidationProvider>
                        </div>
                    </div>

                    <div class="flex flex-wrap">
                        <div class="md:w-12/12 lg:w-12/12 xl:w-6/12" style="padding:10px 20px">
                            <label class="label-size"> Fecha Inicial</label>
                            <vs-input type="date" v-model="FechaI" :disabled="BloquearCodigo" />
                        </div>
                        <div class="md:w-12/12 lg:w-12/12 xl:w-6/12" style="padding:10px 20px">
                            <label class="label-size"> Fecha Final</label>
                            <vs-input type="date" v-model="FechaF" :disabled="BloquearCodigo" />
                        </div>
                    </div>

                    <div class="flex flex-wrap">
                        <div class="w-full md:w-12/12 lg:w-12/12 xl:w-6/12" style="padding:10px 20px">
                            <label class="label-size"> Siguiente Partida</label>
                            <vs-input type="number" v-model="SigPartida" align="center"  />
                        </div>
                    </div>

                    <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" @click="GuardarPeriodo()" id="btn_confirmacion" :disabled="BloquearComponentes"> Grabar</vs-button>
                    <vs-button color="danger" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-x" @click="Cancelar()" id="btn_confirmacion" :disabled="BloquearComponentes"> Cancelar</vs-button>
                </div>
            </div>

        </div>
    </div>

</vx-card>
</template>

<script>
/**
 * @General
 * Registro de departamentos
 */
//import Multiselect from 'vue-multiselect'
//import "vue-multiselect/dist/vue-multiselect.min.css"
import moment from "moment";
export default {

    components: {

    },
    data() {
        return {
            Titulo_emergente: '',
            NombrePeriodo: '',
            BloquearComponentes: true,
            ListaPeriodos: [],
            CodigoPeriodo: '',
            Opcion: '',
            SubOpcion: '',
            FechaI: '',
            FechaF: '',
            BloquearCodigo: true,
            SigPartida: '',
            BloquearCancel: true
        };
    },
    mounted() {
        this.ConsultarPeriodo();
    },
    methods: {
        getDateValue(value) {
            if (value === undefined) {
                return '';
            } else if (value === null) {
                return '';
            } else if (value == '') {
                return '';
            }
            return moment(value).format('YYYY-MM-DD');
        },
        //Devolver los obejetos seleccionados de los  combobox
        ConsultarPeriodo() {
            this.axios.post('/app/v1_contabilidad_general/ConsultaPeriodos', {
                    Opcion: 'C',
                    SubOpcion: '1'
                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Periodo',
                            text: 'No se pudo recuperar los periodos, verifique conexión.',
                        })
                        this.ListaPeriodos = [];
                    } else {
                        this.ListaPeriodos = resp.data.json;
                    }
                })
        },
        GuardarPeriodo() {
            //Condicionante si es Nuevo registro ó Actualización
            this.axios.post('/app/v1_contabilidad_general/UpdateInsertPeriodo', {
                    CodigoPeriodo: this.CodigoPeriodo,
                    Descripcion: this.NombrePeriodo,
                    FechaInicial: this.FechaI,
                    FechaFinal: this.FechaF,
                    SigPartida: this.SigPartida,
                    Opcion: this.Opcion,
                    SubOpcion: this.SubOpcion

                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Periodo',
                            text: resp.data.mensaje,
                        })

                    } else {
                        this.ConsultarPeriodo()
                        this.Cancelar()
                    }
                })
            this.ConsultarPeriodo()
            this.Cancelar()
        },
        GenerarPeriodos() {
            this.Opcion = 'I'
            this.SubOpcion = '2'
            this.axios.post('/app/v1_contabilidad_general/GenerarPeriodos', {
                    Opcion: this.Opcion,
                    SubOpcion: this.SubOpcion,

                })
                .then(resp => {
                    this.$vs.loading.close();
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Periodo',
                            text: resp.data.mensaje,
                        })

                    } else {
                        this.ConsultarPeriodo()
                        this.Cancelar()
                    }
                })
            this.ConsultarPeriodo()
            this.Cancelar()
        },
        EliminarRegistro(datos_eliminar) {
            this.Opcion = 'E'
            this.SubOpcion = '1'
            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Confirmación',
                text: '¿Desea eliminar el periodo \'' + datos_eliminar.Codigo + ' - ' + datos_eliminar.Nombre + '\'? ',
                acceptText: 'Aceptar',
                cancelText: 'Cancelar',
                accept: () => {
                    this.$vs.loading();
                    this.axios.post('/app/v1_contabilidad_general/EliminarPeriodo', {
                            CodigoMarca: datos_eliminar.CodigoPeriodo,
                            Opcion: this.Opcion,
                            SubOpcion: this.SubOpcion

                        })
                        .then(resp => {
                            this.$vs.loading.close();
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Periodo',
                                    text: resp.data.mensaje,
                                })

                            } else {
                                this.ListaPeriodos = [];
                                this.Consultar_Periodo();
                                this.$vs.notify({
                                    color: '#danger',
                                    title: 'Periodo',
                                    text: resp.data.mensaje,
                                });

                            }
                        })
                        .catch(() => {
                            this.$vs.loading.close();
                        })
                    this.$vs.loading.close();

                }
            })
        },
        Cancelar() {
            this.CodigoPeriodo = ''
            this.NombrePeriodo = ''
            this.FechaI = ''
            this.FechaF = ''
            this.BloquearComponentes = true
            this.BloquearCodigo = true
            this.Opcion = '' // Indica la variable que es un nuevo registro
            this.SubOpcion = ''
            this.BloquearCancel = true
            this.SigPartida = ''
        },
        NuevoPeriodo() {
            this.CodigoPeriodo = ''
            this.NombrePeriodo = ''
            this.FechaI = ''
            this.FechaF = ''
            this.BloquearComponentes = false
            this.BloquearCodigo = false
            this.Opcion = 'I' // Indica la variable que es un nuevo registro
            this.SubOpcion = '1'
            this.BloquearCancel = false
        },
        EditarPeriodo(datos_editar) {
            this.Opcion = 'U'; // Indica la variable que es una Edición de registro
            this.SubOpcion = '1'
            this.CodigoPeriodo = datos_editar.CodigoPeriodo;
            this.NombrePeriodo = datos_editar.Descripcion;
            this.FechaI = this.getDateValue(moment(datos_editar.FechaInicial, "DD/MM/YYYY").toDate());
            this.FechaF = this.getDateValue(moment(datos_editar.FechaFinal, "DD/MM/YYYY").toDate());
            this.SigPartida = datos_editar.SigPartida;
            this.BloquearComponentes = false;
            this.BloquearCodigo = true;
        },

        // Validacion_Campos(Tipo, Nombre, valor, obligatorio) {
        //     /**
        //      * @General
        //      * Tipo : N = Númerio, Nombre = Descripción del campo, Valor = A evaluar, Obligatorio= Verdadero o falso.
        //      * Devuelve un mensaje al usuario si las validaciones no se cumple y aborta, de lo contrario devuelve verdadero
        //      */
        //     if (Tipo == 'N') {
        //         if (valor <= 0) {
        //             this.$vs.notify({
        //                 color: '#B71C1C',
        //                 title: 'Periodo',
        //                 text: 'Campo \'' + Nombre + '\' es obligatorio.',
        //             });
        //             return false;
        //         } else {
        //             return true;
        //         }
        //     } else {
        //         if (valor == '' && obligatorio == true) {
        //             this.$vs.notify({
        //                 color: '#B71C1C',
        //                 title: 'Periodo',
        //                 text: 'Campo \'' + Nombre + '\' es obligatorio.',
        //             });
        //             return false;
        //         } else {
        //             return true;
        //         }
        //     }
        // },

    }
}
</script>

<style scoped>
.footer {
    height: 50px;
    margin-top: -65px;
    margin-left: 10px;
}

.label-size {
    font-size: 14px;
    font-weight: bold;
}
</style>
