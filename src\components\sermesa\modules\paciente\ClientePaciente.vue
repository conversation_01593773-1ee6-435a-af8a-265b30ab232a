<template>
<div class="clientes-pacientes-container">
    <form @submit="handleSubmit">
        <DxForm :visible="modo=='A'" ref="CamposBusqueda" :form-data.sync="consulta" label-location="left" label-mode="floating" validation-group="validacionBusqueda" :col-count="4">

            <DxFormSimpleItem data-field="PrimerNombre" :validation-rules="[StrRule]" editor-type="dxTextBox" css-class="uppercase" />
            <DxFormSimpleItem data-field="SegundoNombre" editor-type="dxTextBox" css-class="uppercase" />
            <DxFormSimpleItem data-field="PrimerApellido" :validation-rules="[StrRule]" editor-type="dxTextBox" css-class="uppercase" />
            <DxFormSimpleItem data-field="SegundoApellido" editor-type="dxTextBox" css-class="uppercase" />
            <DxFormGroupItem :col-span="4" :col-count="2">
                <DxFormButtonItem :visible="true" :button-options="{ text: 'Buscar',  type: 'default', stylingMode:'contained',  icon: 'search', useSubmitBehavior: true, }" horizontal-alignment="center" verical-alignment="buttom" />
                <DxFormButtonItem :visible="!this.datosCliente && !this.datosPaciente" :button-options="{ text: 'Limpiar', hint: 'Limpiar campos de búsqueda',  type: 'danger', stylingMode:'outlined',  icon: 'fas fa-broom', onClick: ()=>LimpiarBusqueda(), }" horizontal-alignment="center" verical-alignment="buttom" />
            </DxFormGroupItem>

        </DxForm>
    </form>

    <div class="flex flex-wrap">

        <div :class="tableClasses">
            <div class="dx-card m-1">
                <div class="grid-title">Clientes:</div>
                <dx-data-grid :ref="refGridClientes" v-bind="dataGridConf" :data-source="listaClientes" @selection-changed="onClienteSeleccionado" @row-prepared="rowPreparedCli">
                    <dx-data-grid-column data-field="IdCliente" :width="150" />
                    <dx-data-grid-column data-field="NombreCompleto" caption="Nombres" />
                    <dx-data-grid-column data-field="IdPaciente" :width="90" sort-order="desc" />
                    <dx-data-grid-column data-field="FechaNacimiento" data-type="date" />
                    <dx-data-grid-column data-field="Sexo" :width="45" />
                    <dx-data-grid-column data-field="DPI" caption="DPI" :width="120" />
                    <dx-data-grid-column data-field="Pasaporte" />

                    <dx-data-grid-column data-field="NumeroAdhesion" caption="Número Adhesión" />
                    <dx-data-grid-column data-field="IdPoliza" caption="Póliza" />
                    <dx-data-grid-column data-field="FechaInicioCobertura" data-type="date" format="dd/MM/yyyy" />
                </dx-data-grid>
            </div>
        </div>

        <div :class="tableClasses">
            <div class="dx-card m-1">
                <div class="grid-title">Pacientes:</div>
                <dx-data-grid :ref="refGridPacientes" v-bind="dataGridConf" :data-source="listaPacientes" @selection-changed="onPacienteSeleccionado" @row-prepared="rowPreparedPac">
                    <dx-data-grid-column data-field="Codigo" :width="90" sort-order="desc" />
                    <dx-data-grid-column data-field="NombrePaciente" caption="Nombres" />
                    <dx-data-grid-column data-field="IdCliente" :width="150" />
                    <dx-data-grid-column data-field="Nacimiento" caption="FechaNacimiento" data-type="date" />
                    <dx-data-grid-column data-field="Sexo" :width="45" />
                    <dx-data-grid-column data-field="DPI" caption="DPI" :width="120" />
                    <dx-data-grid-column data-field="Pasaporte" />

                    <dx-data-grid-column data-field="NumeroAdhesion" caption="Número Adhesión" />
                    <dx-data-grid-column data-field="IdPoliza" caption="Póliza" />
                    <dx-data-grid-column data-field="FechaInicioCobertura" data-type="date" format="dd/MM/yyyy" />
                    <dx-data-grid-column caption="%" data-type="number" format="#0.00" :visible="true" :fixed="true" sort-order="desc" :sort-index="0" :calculate-cell-value="calculateCellValue" />
                </dx-data-grid>
            </div>

        </div>

    </div>

    <div class="flex flex-wrap justify-center gap-2">
        <vs-button v-if="pacienteSeleccionado && !pacienteSeleccionado.IdCliente" class="w-80" @click="ConfirmCreaCliente">
            <font-awesome-icon :icon="['fas', 'user-plus']" /> Crear Cliente
        </vs-button>

        <vs-button v-if="relacionDirecta" class="w-80" color="danger" @click="AsociarClientePaciente('DesVincularClientePaciente')">
            <font-awesome-icon :icon="['fas', 'link-slash']" /> Desenlazar Cliente-Paciente
        </vs-button>

        <vs-button v-else-if="pacienteSeleccionado && clienteSeleccionado" class="w-80" color="success" @click="AsociarClientePaciente('VincularClientePaciente')">
            <font-awesome-icon :icon="['fas', 'link']" /> Enlazar Cliente-Paciente
        </vs-button>

        <vs-button v-if="clienteSeleccionado && !clienteSeleccionado.IdPaciente" class="w-80" @click="ConfirmCreaPaciente">
            <font-awesome-icon :icon="['fas', 'user-plus']" /> Crear Paciente
        </vs-button>
    </div>
    <details open class="w-full pb-3">
        <summary>
            <span>Detalle coincidencias:</span>
        </summary>
        <div class="flex flex-wrap justify-center gap-5">
            <div class="dx-card">
                <div class="grid-title">Pesos y forma de comparar</div>
                <div class="flex flex-row gap-2">

                    <vs-input readonly size="small" class="w-24" label="Nombre" :value="$formato_decimal(ponderados.nombre.porcentaje*100,2) + ' %'" />
                    <vs-switch class="self-center" v-model="ponderados.nombre.exacto">
                        <span slot="on">Exacto</span>
                        <span slot="off">Similar</span>
                    </vs-switch>

                    <vs-input readonly size="small" class="w-24" label="DPI" :value="$formato_decimal(ponderados.dpi.porcentaje*100,2) + ' %'" />
                    <vs-switch class="self-center" v-model="ponderados.dpi.exacto">
                        <span slot="on">Exacto</span>
                        <span slot="off">Similar</span>
                    </vs-switch>

                    <vs-input readonly size="small" class="w-24" label="Nacimiento" :value="$formato_decimal(ponderados.nacimiento.porcentaje*100,2) + ' %'" />
                    <vs-switch class="self-center" v-model="ponderados.nacimiento.exacto">
                        <span slot="on">Exacto</span>
                        <span slot="off">Similar</span>
                    </vs-switch>
                </div>
            </div>

            <div class="dx-card">
                <div class="grid-title">Cliente y Paciente seleccionados:</div>
                <div class="flex flex-row">
                    <vs-input size="small" class="w-24 p-2" label="Nombre" :value="$formato_decimal(Pond.Nombre,2) + ' %'" />
                    <vs-input size="small" class="w-24 p-2" label="DPI" :value="$formato_decimal(Pond.DPI,2) + ' %'" />
                    <vs-input size="small" class="w-24 p-2" label="Nacimiento" :value="$formato_decimal(Pond.Nacimiento,2) + ' %'" />
                    <vs-input size="small" class="w-24 p-2" label="Total" :value="$formato_decimal(Pond.Total,2) + ' %'" />
                </div>
            </div>
        </div>

    </details>
</div>
</template>

<script>
const refGridClientes = 'refGridClientes'
const refGridPacientes = 'refGridPacientes'
export default {
    name: 'ClientesPacientes',
    components: {

    },
    props: {
        datosCliente: {
            type: Object
        },
        datosPaciente: {
            type: Object
        },
        showCreateButton: {
            type: Boolean,
            default: true
        },
        includeAfiliacion: {
            type: Boolean,
            default: false
        },
        loadOnMount:{
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            listaPacientes: null,
            listaClientes: null,
            consulta: {
                Empresa: '',
                PrimerNombre: '',
                SegundoNombre: '',
                PrimerApellido: '',
                SegundoApellido: '',
                NumeroDocumento: '',
            },
            clienteSeleccionado: null,
            pacienteSeleccionado: null,
            dataGridConf: {
                visible: true,
                showRowLines: true,
                showColumnLines: true,
                showBorders: true,
                'load-panel': {
                    enabled: false
                },
                selection: {
                    mode: 'single'
                },
                focusedRowEnabled: false,
                rowAlternationEnabled: true,
                columnHidingEnabled: true,
                hoverStateEnabled: true,
                width: '100%',
                height: '100%',
                columnAutoWidth: true,
                allowColumnResizing: true,
                columnResizingMode: 'nextColumn',

                paging: {
                    enabled: false,
                },
                scrolling: {
                    showScrollbar: 'always',
                    useNative: false,
                },
            },

            StrRule: {
                type: 'stringLength',
                max: 30,
                min: 2,
                ignoreEmptyValue: false,
                message: 'Debe contener entre 2 y 30 caracteres'
            },
            refGridClientes,
            refGridPacientes,
            arr: [],
            ponderados: {
                nombre: {
                    exacto: false,
                    porcentaje: 0.5,
                },
                dpi: {
                    exacto: false,
                    porcentaje: 0.25,
                },
                nacimiento: {
                    exacto: true,
                    porcentaje: 0.25,
                },
            }
        }

    },
    mounted() {
        if(this.loadOnMount)
            this.Cargar()
    },
    watch: {
        'datosPaciente': {
            handler: function (e) {
                this.consulta = {
                    ...this.consulta,
                    ...e
                }

                if (this.datosPaciente && this.datosPaciente.NombrePaciente && this.datosPaciente.ApellidoPaciente && !this.datosCliente)
                    this.ConsultarCliente()

                if (this.datosPaciente && this.datosPaciente.Codigo)
                    this.CargarPaciente()
            },
            deep: true,
        },
        'datosCliente': {
            handler: function (e) {
                this.consulta = {
                    ...this.consulta,
                    ...e
                }

                if (this.datosCliente && this.datosCliente.PrimerNombre && this.datosCliente.PrimerApellido && !this.datosPaciente)
                    this.ConsultarPaciente()

                if (this.datosCliente && this.datosCliente.IdCliente)
                    this.CargarCliente()
            },
            deep: true,
        },
        'clienteSeleccionado'() {
            this.$nextTick(() => {
                this.dataGridPacientes.refresh()
                this.dataGridClientes.refresh()
            })
        },
        'pacienteSeleccionado'() {
            this.$nextTick(() => {
                this.dataGridPacientes.refresh()
                this.dataGridClientes.refresh()
            })
        },
    },
    computed: {
        relacionDirecta() {
            return this.clienteSeleccionado && this.pacienteSeleccionado && this.clienteSeleccionado.IdPaciente == this.pacienteSeleccionado.Codigo && this.pacienteSeleccionado.IdCliente == this.clienteSeleccionado.IdCliente
        },
        Pond() {
            return this.ponderacion(this.clienteSeleccionado, this.pacienteSeleccionado)
        },
        tableClasses() {
            return [
                'p-1',
                'w-full',
                !this.datosCliente && !this.datosPaciente ? 'md:w-1/2' : '',
            ]
        },
        /**
         * C: Cliente conocido
         * P: Paciente conocido
         * A: Busqueda de ambos
         */
        modo() {
            if (this.datosCliente)
                return 'C'
            if (this.datosPaciente)
                return 'P'
            return 'A'
        },

        dataGridClientes: function () {
            return this.$refs[refGridClientes].instance
        },

        dataGridPacientes: function () {
            return this.$refs[refGridPacientes].instance
        }

    },
    methods: {
        handleSubmit(e) {
            this.Consultar()
            e.preventDefault()
        },
        Cargar() {
            if (this.modo == 'A')
                this.Consultar()
            if (this.modo == 'C')
                this.CargarCliente()
            if (this.modo == 'P')
                this.CargarPaciente()
        },
        //boton de buscar por nombre
        Consultar() {
            if (this.consulta.PrimerNombre && this.consulta.PrimerApellido) {
                if (!this.datosCliente)
                    this.ConsultarCliente()

                if (!this.datosPaciente)
                    this.ConsultarPaciente()
            }
        },
        LimpiarBusqueda() {
            this.consulta.PrimerNombre = ''
            this.consulta.SegundoNombre = ''
            this.consulta.PrimerApellido = ''
            this.consulta.SegundoApellido =''
            this.consulta.NumeroDocumento = ''
            this.listaClientes = null
            this.listaPacientes = null
            this.pacienteSeleccionado = null
            this.clienteSeleccionado = null
        },
        /**Caraga el listado de pacientes cuando la busqueda es por nombres */
        ConsultarPaciente() {
            this.pacienteSeleccionado = null
            return this.axios.post('/app/v1_afiliados/ConsultaPacientePorNombre', {
                NombrePaciente: ''.concat(this.consulta.PrimerNombre, this.consulta.SegundoNombre ? (' '.concat(this.consulta.SegundoNombre)) : ''),
                ApellidoPaciente: ''.concat(this.consulta.PrimerApellido, this.consulta.SegundoApellido ? (' '.concat(this.consulta.SegundoApellido)) : ''),
                NumeroDocumento: '',

                PrimerNombre: this.consulta.PrimerNombre,
                SegundoNombre: this.consulta.SegundoNombre,
                PrimerApellido: this.consulta.PrimerApellido,
                SegundoApellido: this.consulta.SegundoApellido,
            }).then(resp => {
                this.arr.slice(0, 0)
                this.listaPacientes = resp.data.json.map(t=>{
                    t.IdCliente = t.IdCliente?.replace(/[\s]+/g, "")
                    return t
                })
            })
        },
        /**Carga el listado de clientes cuando la busqueda es por nombres 
         * TODO: refinar busqueda
         */
        ConsultarCliente() {
            this.clienteSeleccionado = null
            let postData = this.consulta
            if (this.modo == 'P') // provicional mientras se mejora la  busqueda de clientes ya que puede que el paciente no tenga el sergundo nombre y apellido
                postData = {
                    PrimerNombre: this.consulta.PrimerNombre,
                    PrimerApellido: this.consulta.PrimerApellido,
                }
            return this.axios.post('/app/v1_afiliados/BusqudaAfiliados', postData).then(resp => {
                this.listaClientes = resp.data.json
            })
        },
        AsociarClientePaciente(endpoint) {
            this.$vs.dialog({
                type: 'confirm',
                color: endpoint == 'VincularClientePaciente' ? 'success' : 'danger',
                acceptText: 'Sí',
                cancelText: 'No',
                title: 'Crear Paciente',
                text: `Esta opción ${endpoint=='VincularClientePaciente'?'enlaza': 'desenlaza'} todos los datos del cliente ${this.clienteSeleccionado.IdCliente} y el paciente ${this.pacienteSeleccionado.Codigo} ¿Desea continuar?`,

                accept: () => {
                    this.axios.post(`/app/v1_admision/${endpoint}`, {
                        CodigoPaciente: this.pacienteSeleccionado.Codigo,
                        IdCliente: this.clienteSeleccionado.IdCliente,
                    }).then(() => {
                        this.$emit('asociated', {
                            IdCliente: this.clienteSeleccionado.IdCliente,
                            IdPaciente: this.pacienteSeleccionado.Codigo
                        })
                        this.Cargar()
                    })
                }
            })

        },
        ConfirmCreaPaciente() {
            this.$vs.dialog({
                type: 'confirm',
                color: '#ED8C72',
                acceptText: 'Sí',
                cancelText: 'No',
                title: 'Crear Paciente',
                text: 'Esta opción crea un paciente a partir de los datos del cliente ¿Desea continuar?',

                accept: () => {
                    this.CrearPaciente()
                }
            })
        },
        CrearPaciente() {
            this.axios.post('/app/v1_admision/CrearPacienteDesdeCliente', {
                IdCliente: this.clienteSeleccionado.IdCliente
            }).then(res => {
                this.$emit('paciente-created', res)
                this.Cargar()
            })
        },
        ConfirmCreaCliente() {
            this.$vs.dialog({
                type: 'confirm',
                color: '#ED8C72',
                acceptText: 'Sí',
                cancelText: 'No',
                title: 'Crear Cliente',
                text: 'Esta opción crea un cliente a partir de los datos del paciente seleccionado ¿Desea continuar?',
                accept: () => {
                    this.CrearCliente()
                }
            })
        },
        CrearCliente() {
            //replica la funcionalidad de creacion de pacientes, pequeño truquillo ya que el backend al modificar o crear paciente valida si el cliente no existe y si no lo crea
            //ver AJE017 y '@/components/sermesa/modules/paciente/DatosGeneralesPaciente.vue'
            if (this.pacienteSeleccionado) {
                this.$store.dispatch('paciente/initPaciente')
                this.$store.dispatch('paciente/setPaciente', this.pacienteSeleccionado)
                this.$store.dispatch('paciente/crearPaciente')
                    .then((response) => {
                        this.$emit('cliente-created', response)
                        this.Cargar()
                    })
            }
        },
        /** Carga los datos de un cliente en especifico, util cuando se utilizaeste componente dentro de otro que ya tiene seleccionado un cliente */
        CargarCliente() {
            this.axios.post('/app/v1_afiliados/FichaAfiliado', {
                IdCliente: this.datosCliente.IdCliente,
                IdAfiliado: this.datosCliente.IdAfiliado,
            }).then(resp => {
                if (resp.data.json.length > 0) {
                    resp.data.json.map(t => {
                        t.PrimerNombre = t.Nombre1
                        t.SegundoNombre = t.Nombre2
                        t.PrimerApellido = t.Apellido1
                        t.SegundoApellido = t.Apellido2
                        t.NombreCompleto = ''.concat(
                            t.PrimerNombre, ' ',
                            t.SegundoNombre, ' ',
                            t.PrimerApellido, ' ',
                            t.SegundoApellido)
                    })

                    this.consulta = {
                        ...this.consulta,
                        ...resp.data.json[0]
                    }

                    this.clienteSeleccionado = resp.data.json[0]
                    this.listaClientes = resp.data.json.slice(0, 1)
                    this.ConsultarPaciente()
                } else {
                    this.clienteSeleccionado = null
                    this.listaClientes = null
                    this.listaPacientes = null
                }
            })
        },
        CargarPaciente() {
            this.axios.post('/app/v1_admision/ConsultaPacienteId', {
                Codigo: this.datosPaciente.Codigo
            }).then(resp => {
                if (resp.data.json.length > 0) {

                    this.consulta = {
                        ...this.consulta,
                        ...resp.data.json[0]
                    }
                    this.pacienteSeleccionado = resp.data.json[0]
                    this.listaPacientes = resp.data.json.slice(0, 1)
                    this.ConsultarCliente()
                } else {
                    this.pacienteSeleccionado = null
                    this.listaClientes = null
                }
            })

        },
        onPacienteSeleccionado(e) {
            this.pacienteSeleccionado = e.selectedRowsData[0]
        },
        onClienteSeleccionado(e) {
            this.clienteSeleccionado = e.selectedRowsData[0]
        },
        concatName(dat) {
            return ''.concat(
                dat.PrimerNombre ?? '', ' ',
                dat.SegundoNombre ?? '', ' ',
                dat.PrimerApellido ?? '', ' ',
                dat.SegundoApellido ?? '', ' ',
            )
        },
        rowPreparedCli: function (e) {

            if (e.data && this.pacienteSeleccionado) {
                if (this.pacienteSeleccionado.IdCliente == e.data.IdCliente && this.pacienteSeleccionado.Codigo == e.data.IdPaciente && this.pacienteSeleccionado.IdCliente) {
                    e.rowElement.classList ?.add('asociacion-directa')
                    this.dataGridClientes.navigateToRow(e.key)
                } else if (this.pacienteSeleccionado.IdCliente == e.data.IdCliente && this.pacienteSeleccionado.IdCliente || this.pacienteSeleccionado.Codigo == e.data.IdPaciente)
                    e.rowElement.classList ?.add('asociacion-parcial')
            }

        },
        rowPreparedPac: function (e) {

            if (e.data && this.clienteSeleccionado) {
                if (this.clienteSeleccionado.IdCliente == e.data.IdCliente && this.clienteSeleccionado.IdPaciente == e.data.Codigo) {
                    e.rowElement.classList ?.add('asociacion-directa')
                    this.dataGridPacientes.navigateToRow(e.key)
                } else if (this.clienteSeleccionado.IdCliente == e.data.IdCliente || this.clienteSeleccionado.IdPaciente == e.data.Codigo && this.clienteSeleccionado.IdPaciente)
                    e.rowElement.classList ?.add('asociacion-parcial')
            }

        },

        ponderacion(cli, pac) {
            if (!cli || !pac)
                return 0

            const pondNombre = this.ponderados.nombre.exacto ?
                (this.normalizar(cli.NombreCompleto).localeCompare(this.normalizar(pac.NombrePaciente)) == 0 ? 1 : 0) :
                (this.similitud(this.normalizar(cli.NombreCompleto), this.normalizar(pac.NombrePaciente)))

            const pondDPI = this.ponderados.dpi.exacto ?
                (this.normalizar(cli.DPI ?? '').localeCompare(this.normalizar(pac.DPI ?? '')) == 0 ? 1 : 0) :
                (this.similitud(this.normalizar(cli.DPI ?? ''), this.normalizar(pac.DPI ?? '')))

            const pondNacimiento = this.ponderados.nacimiento.exacto ?
                ((cli.FechaNacimiento ?? '').substring(0, 10).localeCompare((pac.Nacimiento ?? '').substring(0, 10)) == 0 ? 1 : 0) :
                (this.similitud((cli.FechaNacimiento ?? '').substring(0, 10), (pac.Nacimiento ?? '').substring(0, 10)))

            return {
                Nombre: pondNombre * 100,
                DPI: pondDPI * 100,
                Nacimiento: pondNacimiento * 100,
                Total: (pondDPI * this.ponderados.dpi.porcentaje +
                    pondNombre * this.ponderados.nombre.porcentaje +
                    pondNacimiento * this.ponderados.nacimiento.porcentaje) * 100
            }
        },

        normalizar(x) {
            
            if (!x)
                return ''
          
            return x.normalize('NFD').toLowerCase().replace(/[\W\s_]|\p{Diacritic}+/g, "")
           
        },

        similitud(s1, s2) {
            let longer = s1
            let shorter = s2
            if (s1.length < s2.length) {
                longer = s2
                shorter = s1
            }
            let longerLength = longer.length
            if (longerLength == 0) {
                return 1.0
            }
            return (longerLength - this.editDistance(longer, shorter)) / parseFloat(longerLength)
        },

        editDistance(s1, s2) {
            s1 = s1.toLowerCase()
            s2 = s2.toLowerCase()

            var costs = new Array()
            for (var i = 0; i <= s1.length; i++) {
                var lastValue = i
                for (var j = 0; j <= s2.length; j++) {
                    if (i == 0)
                        costs[j] = j
                    else {
                        if (j > 0) {
                            var newValue = costs[j - 1]
                            if (s1.charAt(i - 1) != s2.charAt(j - 1))
                                newValue = Math.min(Math.min(newValue, lastValue),
                                    costs[j]) + 1
                            costs[j - 1] = lastValue
                            lastValue = newValue
                        }
                    }
                }
                if (i > 0)
                    costs[s2.length] = lastValue
            }
            return costs[s2.length]
        },
        calculateCellValue(e) {
            const p = this.ponderacion(this.clienteSeleccionado, e)
            return p.Total
        }
    }
}
</script>

<style>
.clientes-pacientes-container .dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed)>td {
    color: black !important;
    font-weight: bold !important;
}

.clientes-pacientes-container .dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed) {
    border: 2px solid
}

.uppercase input {
    text-transform: uppercase;
}

.asociacion-directa td {
    background-color: #009539 !important;
}

.asociacion-parcial td {
    background-color: #EA5455 !important;
}

.clientes-pacientes-container .dx-selection tr {
    border: 1px solid;
}

.clientes-pacientes-container .grid-title {
    margin: 5px;
    font-weight: bold;
}

.clientes-pacientes-container .dx-card {
    max-height: 80vh;
    padding: 10px;
}

.clientes-pacientes-container .dx-datagrid {
    max-height: calc(80vh - 55px);
}
</style>
