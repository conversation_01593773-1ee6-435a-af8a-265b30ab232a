<template>
<div class="fechas-reclamos-archivo-container">
    <div>
        <import-excel v-if="reclamos.length==0" :onSuccess="OnSuccessExcel"></import-excel>

        <vs-row vs-justify="center" class="m-2">
            <vs-button color="primary" icon="help" @click="mostrarAyuda=true"> Ayuda </vs-button>
            <vs-button :disabled="reclamos.length==0" class="ml-2" color="success" icon="settings" @click="ProcesarArchivo"> Procesar Archivo </vs-button>
            <vs-button v-if="reclamos.length>0" class="ml-2" color="warning" icon="delete_forever" @click="reclamos.splice(0)">Limpiar Datos</vs-button>
        </vs-row>

        <DxDataGrid :visible="reclamos.length>0" ref="remisionesDataGrid" :data-source="reclamos" v-bind="DefaultDxGridConfiguration" @cell-prepared="onCellPrepared" @row-validating="ValidateRow" />
    </div>
    <vs-popup title="Ayuda" :active.sync="mostrarAyuda">

        <p>
            Cargue su archivo excel y actualice las fechas de remisión para el grupo de Datos
        </p>
        <strong>Columnas obligatorias:</strong>
        <p>
            Admisión (U-115713), Número Referencia (12649) y por lo menos una de las siguientes fechas para actualizar:
        </p>

        <li>Traslado del reclamo de la base a cobros [hora 1]</li>
        <li>Cobros Traslada a Aseguradoras [hora 2]</li>
        <li>Recibido Aseguradora (Sello) [hora 3]</li>
        <li>Aseguradora Devuelve por Ajustes [hora 4]</li>

        <li>Liquidaciones Devuelve el Reclamo a la Base [hora 5]</li>
        <li>Base Devuelve Nuevamente el Reclamo a Cobros [hora 6]</li>
        <li>Cobros/Base Traslada a Aseguradoras [hora 7]</li>
        <li>Recibido Aseguradora (Nuevo Sello) [hora 8] </li>
        <p>
            Cada columna debe tener formato de fecha y puede incluir la hora en la misma columna o en otra adicional [hora n], según corresponda.
        </p>
        <br>Si incluye la hora en otra columna debe estar en formato hora.

        <p>
            Puede realizar modificaciones sobre el excel cargado, luego presione el botón procesar archivo para grabar los datos.
        </p>

        <strong>NOTA: solo los registros sin error serán procesados.</strong>
        <strong>Solo se puede agregar y modificar fechas (no se pueden borrar)</strong>

    </vs-popup>
</div>
</template>

<script>
import ImportExcel from '/src/components/excel/ImportExcel.vue'

const myDateColumn = {
    dataType: 'datetime',
    format: "dd/MM/yyyy HH:mm:ss",
    editorOptions: {
        showClearButton: false,
        useMaskBehavior: true,
        placeholder: 'dd/MM/yyyy HH:mm:ss',
        displayFormat: 'dd/MM/yyyy HH:mm:ss',
        dateSerializationFormat: 'yyyy-MM-ddTHH:mm:ss'
    },
    width: 150,
}
export default {
    name: 'carga-fechas-reclamos',
    components: {
        ImportExcel,
    },
    data() {
        return {
            reclamos: [],
            mostrarAyuda: false,
            DefaultDxGridConfiguration: {
                columns: [{
                        dataField: 'SerieAdmision',
                        caption: 'Serie Admisión',
                        width: 80,
                        validationRules: [{
                            type: 'required'
                        }],
                    },
                    {
                        dataField: 'NumeroAdmision',
                        caption: 'Número Admisión',
                        dataType: 'number',
                        width: 80,
                        validationRules: [{
                            type: 'required'
                        }],
                    },
                    {
                        dataField: 'Referencia',
                        caption: 'Número Referencia',
                        dataType: 'number',
                        width: 80,
                        validationRules: [{
                            type: 'required'
                        }],
                    },
                    {
                        dataField: 'TrasladoBaseCobros',
                        caption: 'Traslado del reclamo de la base a cobros',
                        ...myDateColumn,
                    },
                    {
                        dataField: 'FechaEntrega',
                        caption: 'Cobros Traslada a Aseguradoras',
                        ...myDateColumn,
                    },
                    {
                        dataField: 'FechaRecibido',
                        caption: 'Recibido Aseguradora (Sello)',
                        ...myDateColumn,
                    },
                    {
                        dataField: 'DevuelveAsegLiq',
                        caption: 'Aseguradora Devuelve por Ajustes',
                        dataType: 'datetime',
                        ...myDateColumn,
                    },
                    {
                        dataField: 'DevuelveLiqSeg',
                        caption: 'Liquidaciones Devuelve el Reclamo a la Base',
                        ...myDateColumn,
                    },
                    {
                        dataField: 'NuevoTrasladoBaseCobros',
                        caption: 'Base Devuelve Nuevamente el Reclamo a Cobros',
                        ...myDateColumn,
                    },
                    {
                        dataField: 'NuevaEntrega',
                        caption: 'Cobros/Base Traslada a Aseguradoras',
                        dataType: 'datetime',
                        ...myDateColumn,
                    },

                    {
                        dataField: 'NuevoFechaRecibido',
                        caption: 'Recibido Aseguradora (Nuevo Sello)',
                        ...myDateColumn,
                    },
                    {
                        dataField: 'Errores',
                        allowEditing: false,
                        width: 200,
                        calculateCellValue: x => x && x.Errores && x.Errores.length > 0 ? x.Errores.join('; ') : 'NO'
                    }
                ],

                editing: {
                    allowUpdating: true,
                    allowAdding: true,
                    allowDeleting: true,
                    mode: 'row',
                    useIcons: true,
                    confirmDelete: false,
                    newRowPosition: 'last',
                },
                visible: true,
                showRowLines: true,
                showColumnLines: true,
                showBorders: true,
                'load-panel': {
                    enabled: false,
                },
                selection: {
                    mode: 'single'
                },
                searchPanel: {
                    visible: true
                },
                focusedRowEnabled: false,
                rowAlternationEnabled: true,
                columnHidingEnabled: true,
                columnMminWidth: 150,
                hoverStateEnabled: true,
                height: 'calc(100vh - 295px)',
                allowColumnReordering: true,
                allowColumnResizing: true,
                columnResizingMode: 'widget',
                headerFilter: {
                    visible: true,
                    allowSearch: true
                },
                wordWrapEnabled: true,
                paging: {
                    enabled: true,
                    pageSize: 50
                },
                showBborders: true,
                keyExpr: 'Correlativo',
            }
        }
    },
    methods: {
        ProcesarArchivo() {
            const postData = this.reclamos.filter(t => !t.Errores || t.Errores.length == 0)
            if (!postData.length) {
                this.$vs.notify({
                    title: 'Remisiones dice...',
                    text: 'Debe tener por lo menos un registro sin errores',
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'warning',
                    time: 4000,
                })
                return
            }

            this.axios.post("/app/v1_cobros/ActualizarFechasRemisiones", {
                Remisiones: postData
            }).then(resp => {
                const newRemisiones = []
                //dejar solo las remisiones o reclamos que no se pudieron actualizar
                resp.data.errores.forEach(err => {
                    newRemisiones.push(this.reclamos.find(x => {
                        if (x.SerieAdmision == err.SerieAdmision && x.NumeroAdmision == err.NumeroAdmision && x.Referencia == err.Referencia) {
                            x.Errores.push(err.descripcion)
                            return x
                        }
                        return null
                    }))
                })

                this.reclamos = newRemisiones

                if (this.reclamos.length)
                    this.$vs.notify({
                        title: 'Remisiones dice...',
                        text: 'Algunos registros no se han podido procesar, vea el detalle',
                        iconPack: 'feather',
                        icon: 'icon-alert-circle',
                        color: 'warning',
                        time: 4000,
                    })
            })
        },
        OnSuccessExcel(arg) {
            this.ValidarEstructuraArchivo(arg)
        },
        ValidarEstructuraArchivo(arg) {
            //buscar la hoja
            let datos = arg.find(sheet => sheet.meta.sheetName == 'Reclamos')
            if (!datos)
                datos = arg[0] //primer hoja
            if (!datos || !datos.results || datos.results.length == 0) {
                this.$vs.dialog({
                    type: 'alert',
                    color: 'danger',
                    title: 'Error en carga de archivo',
                    text: 'Valide que la hoja que exista por lo menos una hoja o una llamada Reclamos y no esté vacía',
                })
                return
            }

            function cleanText(str) {
                if (!str)
                    return ''
                return str.normalize('NFD').toLowerCase().replace(/[\W\s_]|\p{Diacritic}+/g, "");
            }

            function toMyISODate(value) {
                if (value && value.constructor && value.constructor.name == 'Date' && value.valueOf())
                    return value.toISOString().replace('Z', '')
                return value
            }
            //agregar el componente decimal Hora a la fecha si esta ya tiene hora la remueve
            function sumDateTime(date, time) {
                if (typeof date == 'number' && typeof time == 'number')
                    //return Number.parseFloat(date.toFixed(0)) + time
                    return Math.trunc(date) + time
                return date
            }
            //Unicando el nombre de las columnas que vienen en el excel
            let NAdmision = datos.header.find((x) => cleanText(x) == 'admision')
            let NReferencia = datos.header.find((x) => cleanText(x) == 'numeroreferencia')

            //Fase1
            let NTrasladoBaseCobros = datos.header.find((x) => cleanText(x) == 'trasladodelreclamodelabaseacobros')
            let NFechaEntrega = datos.header.find((x) => cleanText(x) == 'cobrostrasladaaseguradoras')
            let NFechaRecibido = datos.header.find((x) => cleanText(x).includes('recibidoaseguradorasello'))
            let NDevuelveAsegLiq = datos.header.find((x) => cleanText(x) == 'aseguradoradevuelveporajustes')

            //Fase2
            let NDevuelveLiqSeg = datos.header.find((x) => cleanText(x) == 'liquidacionesdevuelveelreclamoalabase')
            let NNuevoTrasladoBaseCobros = datos.header.find((x) => cleanText(x) == 'basedevuelvenuevamenteelreclamoacobros')
            let NNuevaEntrega = datos.header.find((x) => cleanText(x) == 'cobrosbasetrasladaaaseguradoras')
            let NNuevoFechaRecibido = datos.header.find((x) => cleanText(x) == 'recibidoaseguradoranuevosello')

            //las siguientes variables se usan cuando las fechas y horas estan separadas en dos columnas
            let NHora1 = datos.header.find((x) => cleanText(x) == 'hora1')
            let NHora2 = datos.header.find((x) => cleanText(x) == 'hora2')
            let NHora3 = datos.header.find((x) => cleanText(x) == 'hora3')
            let NHora4 = datos.header.find((x) => cleanText(x) == 'hora4')
            let NHora5 = datos.header.find((x) => cleanText(x) == 'hora5')
            let NHora6 = datos.header.find((x) => cleanText(x) == 'hora6')
            let NHora7 = datos.header.find((x) => cleanText(x) == 'hora7')
            let NHora8 = datos.header.find((x) => cleanText(x) == 'hora8')

            if (!NAdmision) {
                this.$vs.dialog({
                    type: 'alert',
                    color: 'danger',
                    title: 'Error en carga de archivo',
                    text: 'Se requiere columna con el nombre Admision, cuyos valores sea la serie y el número separados por un guión (A-1234)',
                })
                return
            }
            if (!NReferencia) {
                this.$vs.dialog({
                    type: 'alert',
                    color: 'danger',
                    title: 'Error en carga de archivo',
                    text: 'Se requiere columna con el nombre Numero Referencia, cuyos valores sea numerico',
                })
                return
            }

            if (!NTrasladoBaseCobros && !NFechaEntrega && !NFechaRecibido && !NDevuelveAsegLiq && !NDevuelveLiqSeg &&
                !NNuevoTrasladoBaseCobros && !NNuevaEntrega && !NNuevoFechaRecibido) {
                this.$vs.dialog({
                    type: 'alert',
                    color: 'danger',
                    title: 'Error en carga de archivo',
                    text: 'Se requiere por lo menos una colmna de actualización de fecha',
                })
                return
            }

            this.reclamos.splice(0)

            datos.results.forEach((element, index) => {

                let newRow = {
                    Correlativo: index,
                    SerieAdmision: null,
                    NumeroAdmision: null,
                    Referencia: null,

                    TrasladoBaseCobros: toMyISODate(this.$parse_date(sumDateTime(element[NTrasladoBaseCobros], element[NHora1]), true, 'excel')),
                    FechaEntrega: toMyISODate(this.$parse_date(sumDateTime(element[NFechaEntrega], element[NHora2]), true, 'excel')),
                    FechaRecibido: toMyISODate(this.$parse_date(sumDateTime(element[NFechaRecibido], element[NHora3]), true, 'excel')),
                    DevuelveAsegLiq: toMyISODate(this.$parse_date(sumDateTime(element[NDevuelveAsegLiq], element[NHora4]), true, 'excel')),
                    DevuelveLiqSeg: toMyISODate(this.$parse_date(sumDateTime(element[NDevuelveLiqSeg], element[NHora5]), true, 'excel')),
                    NuevaEntrega: toMyISODate(this.$parse_date(sumDateTime(element[NNuevaEntrega], element[NHora6]), true, 'excel')),
                    NuevoTrasladoBaseCobros: toMyISODate(this.$parse_date(sumDateTime(element[NNuevoTrasladoBaseCobros], element[NHora7]), true, 'excel')),
                    NuevoFechaRecibido: toMyISODate(this.$parse_date(sumDateTime(element[NNuevoFechaRecibido], element[NHora8]), true, 'excel')),
                    Errores: []
                }
                if (!/[a-z]{1,3}-[0-9]+/gi.test(element[NAdmision])) {
                    newRow.Errores.push('SerieAdmision')
                    newRow.Errores.push('NumeroAdmision')
                } else {
                    let adm = (element[NAdmision] ?? '').split('-')
                    newRow.SerieAdmision = adm[0]
                    newRow.NumeroAdmision = adm[1]
                }
                if (!/[0-9]+/gi.test(element[NReferencia])) {
                    newRow.Errores.push('Referencia')
                } else
                    newRow.Referencia = element[NReferencia]

                Object.entries(newRow).forEach(entrie => {
                    if (entrie[0] != 'Errores' && entrie[1] && typeof entrie[1] == 'object' && isNaN(entrie[1].valueOf())) {
                        newRow.Errores.push(entrie[0])
                        newRow[entrie[0]] = null
                    }
                })
                this.reclamos.push(newRow)

            })
        },
        onCellPrepared(e) {
            if (e.rowType === 'data' && e.data.Errores && e.data.Errores.length > 0 && (e.column.name == 'Errores' || e.data.Errores.find(x => x == e.column.name))) {
                e.cellElement.style.cssText = 'background-color: #EA5455;'
            }
        },
        /**Validar la edicion y adicion de filas */
        ValidateRow(e) {
            e.newData.Errores = []
            let obj = {
                ...e.oldData,
                ...e.newData
            }
            if (!obj.SerieAdmision || !/[a-z]/gi.test(obj.SerieAdmision))
                obj.Errores.push('SerieAdmision')
            if (!obj.NumeroAdmision || !/[0-9]+/gi.test(obj.NumeroAdmision))
                obj.Errores.push('NumeroAdmision')
            if (!obj.Referencia || !/[0-9]+/gi.test(obj.Referencia))
                obj.Errores.push('Referencia')
            if (
                !obj.TrasladoBaseCobros &&
                !obj.FechaEntrega &&
                !obj.FechaRecibido &&
                !obj.DevuelveAsegLiq &&
                !obj.DevuelveLiqSeg &&
                !obj.NuevoTrasladoBaseCobros &&
                !obj.NuevaEntrega &&
                !obj.NuevoFechaRecibido
            ) {
                e.newData.Errores.push('Se requiere por lo menos una fecha de actualización')
            }
        },
    },
    created() {

    },

}
</script>
