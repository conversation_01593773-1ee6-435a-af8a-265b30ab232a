<template>
  <div id="HistorialAplicaciones" class="val003-container">
    <div id="gdaplicacion">
      <P style="font-size: 15px; color: #0693e3">
        <strong>{{ "Paciente: " }}</strong
        >{{ this.Paciente }} <strong>{{ "    Admisión: " }}</strong>
        {{ this.SerieAdmision + "-" + CodigoAdmision }}
      </P>
      <DxDataGrid
        id="gridAplicacion"
        :show-borders="true"
        :dataSource="SeguimientoProductosDatasource"
        :focused-row-enabled="true"
        :repaint-changes-only="true"
        key-expr="Id"
        v-bind="DefaultDxGridConfiguration"
        :row-alternation-enabled="true"
      >
        <DxColumn
          data-field="FechaAsignada"
          caption="Fecha asignada"
          format="dd/MM/yyyy HH:mm"
          data-type="date"
          sort-order="desc"
          width="100"
        />
        <DxColumn
          data-field="Nombre"
          caption="Nombre medicamento"
          width="400"
        />
        <DxColumn data-field="Id" caption="Id" width="75" :visible="false" />
        <DxColumn
          data-field="FechaCero"
          caption="Fecha cero"
          width="125"
          format="dd/MM/yyyy HH:mm"
          data-type="date"
        />
        <DxColumn
          data-field="DescripcionStatus"
          caption="Descripción estado"
          width="125"
        />
        <DxColumn
          data-field="EstadoActual"
          caption="EstadoActual"
          width="100"
        />
        <DxColumn
          data-field="MotivoNoAplicacion"
          caption="motivo de no aplicación"
          width="200"
        />
        <DxColumn
          data-field="Colaborador"
          caption="Colaborador da seguimiento"
          width="250"
        />
        <DxColumn data-field="NoOrden" caption="No. orden" width="75" />

        <DxSearchPanel :visible="true" width="400px" />

        <DxSelection mode="single" />
        <DxFilterPanel :visible="true" />
        <DxToolbar>
          <DxItem name="groupPanel" />
          <DxItem name="searchPanel" />
          <DxItem location="after" template="opcionesTemplate" />
        </DxToolbar>
        <template #opcionesTemplate>
          <ExpedienteGridToolBar
            :StatusAdmision="StatusAdmision"
            :visible="true"
            :pdfExportItems="[{}]"
            @refresh="CargarHistorial"
            :showItems="['refresh']"
          />
        </template>
        <DxFilterRow :visible="true" />
        <DxGroupPanel :visible="true" />
        <DxGrouping :auto-expand-all="false" />
      </DxDataGrid>
    </div>
  </div>
</template>

<script>
import {
  DxDataGrid,
  DxColumn,
  DxSelection,
  DxSearchPanel,
  DxToolbar,
  DxItem,
  DxFilterRow,
  DxGroupPanel,
  DxGrouping,
  DxFilterPanel,
} from "devextreme-vue/data-grid";
import { DefaultDxGridConfiguration } from "../../EXPEDIENTE/data";
import CustomStore from "devextreme/data/custom_store";
import ExpedienteGridToolBar from "./../../EXPEDIENTE/ExpedienteGridToolBar.vue";
export default {
  components: {
    DxDataGrid,
    DxColumn,
    DxSelection,
    DxSearchPanel,
    DxToolbar,
    DxItem,
    ExpedienteGridToolBar,
    DxFilterRow,
    DxGroupPanel,
    DxGrouping,
    DxFilterPanel,
  },
  data() {
    return {
      Historial: [],
      SerieAdmision: null,
      CodigoAdmision: null,
      StatusAdmision: null,
      Paciente: null,
      DefaultDxGridConfiguration,

      SeguimientoProductosDatasource: new CustomStore({
        key: "Id",
        load: () => {
          return this.Historial;
        },
      }),
    };
  },
  methods: {
    CargarHistorial() {
      if (this.SerieAdmision === null) {
        this.showValidacion();
        return;
      } else {
        this.axios
          .post("/app/v1_ValeElectronico/HistorialAplicacionMedicamentos", {
            SerieAdmision: this.SerieAdmision,
            Admision: this.CodigoAdmision,
          })
          .then((resp) => {
            this.SeguimientoProductosDatasource = resp.data.json.map(
              (x, index) => {
                return {
                  id: index,
                  Id: x.Id,
                  FechaAsignada: x.FechaAsignada,
                  Nombre: x.Nombre,
                  FechaCero: x.FechaCero,
                  DescripcionStatus: x.DescripcionStatus,
                  EstadoActual: x.EstadoActual,
                  MotivoNoAplicacion: x.MotivoNoAplicacion,
                  Colaborador: x.Colaborador,
                  NoOrden: x.NoOrden,
                };
              }
            );
            this.mensajeActualizacion();
            // this.ValeDataSource.load().then(
            //     () => {
            //         this.refreshDataGrid()
            //     },
            //     (error) => {
            //         console.error('error loading data customstore' + JSON.stringify(error))
            //     }
            // )
          });
      }
    },
    mensajeActualizacion() {
      this.$vs.notify({
        time: 4000,
        title: "Información actualizada",
        text: "se actualizo la información",
        iconPack: "feather",
        icon: "icon-alert-circle",
        color: "success",
        position: "bottom-center",
      });
    },
    showValidacion() {
      this.$vs.notify({
        time: 4000,
        title: "No hay admisión seleccionada",
        text: "No ha ingresado una admisión valida",
        iconPack: "feather",
        icon: "icon-alert-circle",
        color: "warning",
        position: "top-center",
      });
    },
  },
};
</script>
