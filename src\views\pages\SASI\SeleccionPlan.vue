<template>
<div class="afiliado-informacion-plan">
    <DxLookup ref="mylookup" :dataSource="PlanesDataSource" :grouped="true" item-template="list-item" group-template="groupt" search-mode="contains" :search-expr="[ 'NombreContrato', 'NombrePlan', 'IdPlan', 'IdContrato']" :min-search-length="2" :show-data-before-search="true" :drop-down-options="{ showTitle:false, hideOnOutsideClick: true, shading:false, fullScreen:false, width: 400}" @value-changed="onValueChange" :value="IdPlan_" v-bind="{...defultEditorOptions,...editorOptions}" value-expr="IdPlan">
        <DxValidator v-if="validator" v-bind="validator" />
        <template #list-item="{ data: itemData }">
            <div>
                <p>{{ itemData.NombrePlan + (mostrarId?`  [${itemData.IdPlan}]`:'')  }}</p>
            </div>
        </template>
        <template #groupt="{ data: itemData }">
            <h6 color="red">{{ `${itemData.items[0].NombreContrato}` + (mostrarId? `[${itemData.items[0].IdContrato}]`:'')  }}</h6>
        </template>
    </DxLookup>
</div>
</template>

<script>
import DataSource from 'devextreme/data/data_source'
import ArrayStore from 'devextreme/data/array_store'
import DxValidator from 'devextreme-vue/validator'
const myplans = []
const dataSource = new DataSource({
    group: ['IdContrato'],
    store: new ArrayStore({
        data: myplans,
        key: 'IdPlan',
    }),
    key: 'IdPlan',
})

export default {
    name: 'SeleccionPlan',
    props: {
        PlanList: {
            type: Array,
            default: () => null
        },
        IdPlan: {
            type: String,
            default: null
        },
        /**Indica si se mustra el Id del plan y contraro en al combo */
        mostrarId: {
            type: Boolean,
            default: false
        },
        editorOptions: Object, //DxLookup devextreme options
        validator: Object,
    },
    components: {
        DxValidator,
        DxLookup: () => import('devextreme-vue/lookup'),
    },
    data() {
        return {
            PlanesDataSource: dataSource,
            IdPlan_: null,
            defultEditorOptions: {
                displayExpr: 'NombrePlan',
            }
        }
    },
    methods: {
        async Cargar() {
            if (this.PlanList)
                myplans.splice(0, myplans.length, ...this.PlanList)
            else
                return this.axios.post("/app/v1_afiliados/BusquedaPlanes", {
                        //vacio para obtener todo el catalogo
                    })
                    .then(resp => {
                        if (resp.data.codigo == 0) {
                            myplans.splice(0, myplans.length, ...resp.data.json)
                            this.$emit('catalog-loaded', myplans)
                        }
                    })
        },
        onValueChange(e) {
            this.IdPlan_ = e.value
            this.$emit('value-change', myplans.find((x) => x.IdPlan == e.value))
        }
    },
    watch: {
        'IdPlan'(value) {
            if (value != this.IdPlan_)
                this.IdPlan_ = value
        }
    },
    created() {
        this.Cargar()
    },
    mounted() {
        this.IdPlan_ = this.IdPlan
        let find = myplans.find((x) => x.IdPlan == this.IdPlan_)
        if(find)
            this.$emit('value-change', find)
    },
}
</script>
<style>
.afiliado-informacion-plan .dx-lookup-field {
    padding-top: 10px;
    padding-bottom: 10px;
}
</style>
