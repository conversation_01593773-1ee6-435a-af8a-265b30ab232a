<template>
    <div class="the-navbar__user-meta flex items-center">
        <div class="text-right leading-tight hidden sm:block">
            <div class="usuario" v-if="sesion.usuario">
                {{ sesion.usuario.toUpperCase() }}
            </div>
            <div class="panel" v-if="sesion.sesion_validar">
                <div v-if="global.instancia" class="panel-instancia"
                    :style="{ 'background-color': global.instancia.toUpperCase() == 'LOCAL' ? '#E74C3C' : global.instancia.toUpperCase() == 'QA' ? '#D35400' : '#27AE60' }">
                    {{ global.instancia }}
                </div>
                <div class="panel-sucursal" @click="cambiar_sucursal">
                    {{ (!sesion.sesion_sucursal_actualizacion) ? sesion.sesion_sucursal_nombre.toUpperCase() :
                        'EsperandoEmpresa...' }}
                </div>
            </div>
        </div>

        <div class="perfil">
            <VmProgress :percentage="((sesion.sesion_tiempo * 100 / sesion.sesion_tiempo_defecto))" type="circle"
                :width="50" status="info" stroke-width="4" class="ml-3">
                <vs-dropdown vs-custom-content vs-trigger-click class="cursor-pointer">

                    <div class="row">
                        <div v-if="imagen_src !== 0" class="con-img">
                            <img :src="'data:imagen_gordito/png;base64,' + info.src" alt="img" width="40" height="40"
                                class="rounded-full shadow-md cursor-pointer block" />
                            <div class="socket"
                                :style="{ 'background-color': (sesion.status_socket) ? '#4CAF50' : '#F44336' }" />
                        </div>
                        <div v-else style="padding:10px;width:30%;">
                            <img src="@/assets/images/others/usuario.png" alt="user-img" width="40" height="40" />
                        </div>
                    </div>
                    <vs-dropdown-menu class="vx-navbar-dropdown">
                        <ul style="min-width: 14rem">

                            <li class="flex py-2 px-4 cursor-pointer hover:bg-primary hover:text-white"
                                @click="cambiar_sucursal">
                                <feather-icon icon="RepeatIcon" svgClasses="w-4 h-4" />
                                <span class="ml-2">Cambiar Sucursal</span>
                            </li>

                            <vs-divider class="m-1" />
                            <li class="flex py-2 px-4 cursor-pointer hover:bg-primary hover:text-white"
                                @click="Visualizar_Perfil">
                                <i class="fas fa-user"></i>
                                <span class="ml-2">Visualizar perfil</span>
                            </li>

                            <vs-divider class="m-1" />
                            <li class="flex py-2 px-4 cursor-pointer hover:bg-primary hover:text-white"
                                @click="CambioClave">
                                <i class="fas fa-key"></i>
                                <span class="ml-2">Actualizar Contraseña</span>
                            </li>

                            <vs-divider class="m-1" />
                            <li class="flex py-2 px-4 cursor-pointer hover:bg-primary hover:text-white" @click="logout">
                                <feather-icon icon="LogOutIcon" svgClasses="w-4 h-4" />
                                <span class="ml-2">Salir </span>
                            </li>

                            <vs-divider class="m-1" />
                            <div disabled class="flex py-2 px-4 " style="font-size:12px;color:#ccc">
                                <i class="fas fa-server"></i>
                                <span class="ml-2">{{ global.instancia }}</span>
                            </div>

                        <div disabled class="flex py-2 px-4 " v-if="sesion.appPC_socket">
                            <feather-icon icon="CastIcon" svgClasses="w-4 h-4" />
                            <span class="ml-2">{{sesion.appPC_socket.version}}</span> <a href="#" v-on:click="refresh">(Actualizar)</a>
                        </div>
                        </ul>
                    </vs-dropdown-menu>
                </vs-dropdown>
            </VmProgress>
        </div>

        <!-- <vs-popup title="Selección de Empresa" :active.sync="seleccionEmpresa.mostrar" class="seleccionEmpresa">
                <div v-if="seleccionEmpresa.seleccion_index==-1">
                    <div class="seleccon_empresa" v-for="(l,index) in seleccionEmpresa.empresas" :key="index" v-on:click="((seleccionEmpresa.empresas[index].Sub.length>0)?seleccionEmpresa.seleccion_index = index:seleccion_sucursal(l.Empresa,1))">
                        [{{l.Empresa}}] {{l.Nombre}}
                    </div>
        
                </div>
                <div v-else>
                    <div>
                        <i class="fas fa-arrow-circle-left" style="margin-right:5px;font-size:20px;cursor:pointer" v-on:click="seleccionEmpresa.seleccion_index = -1"></i> {{seleccionEmpresa.empresas[seleccionEmpresa.seleccion_index].Nombre}}
                    </div>
                    <vs-divider />
        
                    <div class="seleccon_empresa" v-for="(l,index) in seleccionEmpresa.empresas[seleccionEmpresa.seleccion_index].Sub" :key="index" v-on:click="seleccion_sucursal(l.CodigoSuc,2)">
                        [{{l.CodigoSuc}}] {{l.NombreSuc}}
                    </div>
                </div>
            </vs-popup> -->

        <vs-popup title="Selección de Empresa" :active.sync="seleccionEmpresa.mostrar" class="seleccionEmpresa">
            <div class="flex">
                <div class="empresa w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-5">
                    <div class="seleccion_empresa" v-for="(l, index) in seleccionEmpresa.empresas" :key="index"
                        :class="[(seleccionEmpresa.seleccion_index == index) ? 'activo' : '']"
                        v-on:click="((seleccionEmpresa.empresas[index].Sub.length > 0) ? seleccionEmpresa.seleccion_index = index : seleccion_sucursal(l.Empresa, 1))">
                        [{{ l.Empresa }}] {{ l.Nombre }}
                    </div>
                </div>
                <div class="sucursal w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-5">
                    <div v-if="seleccionEmpresa.seleccion_index > -1">
                        <h5>{{ seleccionEmpresa.empresas[seleccionEmpresa.seleccion_index].Nombre }}</h5><br>
                        <div class="seleccion_empresa"
                            v-for="(l, index) in seleccionEmpresa.empresas[seleccionEmpresa.seleccion_index].Sub"
                            :key="index" v-on:click="seleccion_sucursal(l.CodigoSuc, 2)">
                            [{{ l.CodigoSuc }}] {{ l.NombreSuc }}
                        </div>
                    </div>
                    <div v-else style="text-align:center">
                        Seleccione la empresa a utilizar
                    </div>
                </div>
            </div>
        </vs-popup>

        <!--Visualizar Usuario-->

        <vs-popup title="Visualizar Perfil" :active.sync="editar">
            <vs-divider position="left">Información del Usuario</vs-divider>
            <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                <form method="post" @submit.prevent="handleSubmit(Guardar().Guardar_Ajenos)">
                    <div class="flex mb-5"
                        style="justify-content: space-between;border: 1px solid rgba(0, 0, 0, 0.1);padding: 5px;">
                        <div class="w-full md:w-1/2 lg:w-1/2 sm:w-1/2 p-1">

                            <div class="text-subtitle-2">Fotografia</div>
                            <div class="row" v-if="info.corporativo !== 0">
                                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-2 imagen" style="padding:10px;width:100%;text-align:center">
                                    <img class="Foto_Perfil" :src="'data:src/png;base64,' + info.src" alt="">
                                </div>
                            </div>
                            <div v-else style="padding:10px;width:30%;">
                                <img src="@/assets/images/others/notfound.png" alt=""
                                    style="max-width:100%;max-height:100%">
                            </div>
                            <ValidationProvider name="Codigo" rules="required|numero_entero|numero_min:1"
                                v-slot="{ errors }">
                                <vs-input label="Número Corporativo" class="w-full" v-model="info.Corporativo"
                                    :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null"
                                    disabled />
                            </ValidationProvider>
                        </div>
                        <div v-if="info.Corporativo" class="w-full md:w-1/2 lg:w-1/6 sm:w-1/2">
                            <br>
                            <SMImagen @Borrar_Imagen="actualizarArchivoEliminado" @metodo_emit="Consulta_Realizada"
                                :v-show="false" icono="fa-signature" texto="Adjuntar Imagen"
                                api='/app/externo/ArchivosPerfil_BD' :api_parametros="{ Codigo: info.Corporativo }"
                                :firma="true"></SMImagen>

                        </div>
                    </div>
                    <!-- NUEVO -->
                    <div v-if="(info.Corporativo || editar)">
                        <div class="flex flex-wrap">
                            <div class="w-full sm:w-1/4 p-1">
                                <ValidationProvider name="Nombres" rules="required" v-slot="{ errors }">
                                    <vs-input label="Nombres" class="w-full" v-model="info.Nombres" :disabled="true"
                                        :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null" />
                                </ValidationProvider>
                            </div>
                            <div class="w-full sm:w-1/4 p-1">
                                <ValidationProvider name="Apellidos" rules="required" v-slot="{ errors }">
                                    <vs-input label="Apellidos" class="w-full" v-model="info.Apellidos" :disabled="true"
                                        :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null" />
                                </ValidationProvider>
                            </div>
                            <div class="w-full sm:w-1/4 p-1">
                                <ValidationProvider name="Puesto" rules="" v-slot="{ errors }">
                                    <SM-Buscar v-model="info.IdPuesto" label="Puesto"
                                        api="app/administracion/BusquedaCorporativosPuestos" :api_campos="['Puesto']"
                                        :api_titulos="['Puest   o']" api_campo_respuesta="Id"
                                        api_campo_respuesta_mostrar="Puesto" :disabled_texto="true" :api_preload="true"
                                        :callback_cancelar="true" :disabled_busqueda="true" :danger="errors.length > 0"
                                        :dangertext="errors.length > 0 ? errors[0] : null" />
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4 p-1">
                                <SM-Buscar label="Estado" v-model="info.Status"
                                    :api="[{ Codigo: 'A', Nombre: 'Activo' }, { Codigo: 'I', Nombre: 'Inactivo' }]"
                                    :api_campos="['Nombre']" :api_titulos="['Nombre']" api_campo_respuesta="Codigo"
                                    api_campo_respuesta_mostrar="Nombre" :api_preload="true" :disabled_texto="true"
                                    :disabled_busqueda="true" />
                            </div>
                        </div>

                        <div v-if="editar">
                            <vs-divider position="left"></vs-divider>
                            <div class="flex flex-wrap">
                                <div class="w-full sm:w-1/4 p-1">
                                    <ValidationProvider name="Correo" rules="email" v-slot="{ errors }">
                                        <vs-input label="Correo Corporativo" class="w-full" v-model="info.Correo"
                                            :danger="errors.length > 0"
                                            :danger-text="errors.length > 0 ? errors[0] : null" />
                                    </ValidationProvider>

                                </div>
                                <div class="w-full sm:w-1/4 p-1">
                                    <vs-input type="password" name="ClaveCorreo" icon="icon icon-lock" icon-pack="feather"
                                        v-model="info.ClaveCorreo" class="w-full" label="Contraseña Correo Corporativo"
                                        :disabled="info.ClaveIngresada == 1" />
                                    <div>
                                        <p>Contraseña:
                                            <strong>{{ textoDesencriptado }}</strong>
                                        </p>
                                    </div>
                                    <br>
                                    <div class="button-container">
                                        <vs-button title="Actualizar Contraseña" color="#04B1E3"
                                            :disabled="info.ClaveIngresada == 0" class="button-item"
                                            @click="Cambiar_Estadpo()"><i class="fa fa-retweet"></i></vs-button>

                                        <vs-button title="Visualizar Contraseña" color="#F4D03F" class="button-item"
                                            :disabled="info.ClaveIngresada == 0"
                                            @click="visualizarContraseñaDesencriptada()"><i
                                                class="fa fa-lock"></i></vs-button>

                                    </div>
                                </div>
                                <div class="w-full sm:w-1/4 p-1">
                                    <ValidationProvider name="Correo" rules="email">
                                        <vs-input label="Correo Grupo" class="w-full" v-model="info.CorreoGrupo" />
                                    </ValidationProvider>
                                </div>
                                <div class="w-full sm:w-1/4 p-1">
                                    <ValidationProvider name="Telefono" rules="min:0|max:8">
                                        <vs-input label="Número teléfonico o Extensión" class="w-full"
                                            v-model="info.Telefono" />
                                    </ValidationProvider>
                                </div>
                            </div>
                        </div>
                        <vs-divider position="left"></vs-divider>
                        <div v-if="editar" class="flex buttom">
                            <vs-spacer></vs-spacer>
                            <vs-button color="#27AE60" :disabled="invalid"
                                @click.native="handleSubmit(Guardar().Corporativo());">
                                Actualizar datos
                            </vs-button>
                        </div>
                    </div>
                </form>
            </ValidationObserver>
        </vs-popup>

        <vs-popup title="Actualizar Contraseña" :active.sync="changepass"  class="popup-update-password">
            <ChangePassword ref="refChangePassword" class="w-full mb-2 justify-self-center" :corporativo="sesion.corporativo" @cancel="changepass=false" @password-changed="changepass=false" />
        </vs-popup>
    </div>
</template>
            
<script>

import ChangePassword from '../../../../views/pages/AUTH/ChangePassword.vue'
import SMImagen from '/src/components/sermesa/modules/archivos/SMImagen.vue'

export default {
    data() {
        return {


            seleccionEmpresa: {
                mostrar: false,
                empresas: [],
                seleccion_index: -1
            },

            archivos: [],
            imagen_src: '',
            textoEncriptado: 'ClaveCorreo',
            textoDesencriptado: null,

            permisos: {
                nuevo: false,
                editar: true,
                cambiar_imagen: true,
            },


            show: {
                Puesto: false
            },

            /**
             * Habilita los campos para crear un nuevo ajeno
             */
            nuevo: false,

            /**
             * Habilita los campos para editar
             */
            editar: false,

            /**
             * Editar información del corporativo
             */
            editarCorporativo: false,

            /**
             * Indica si se bloqueara el contenido para su edición
             */
            bloquear: false,

            /**
             * Modelo de datos
             */

            info: {
                Corporativo: null,
                Status: 'A',
                Nombres: null,
                Apellidos: null,
                Correo: null,
                IdPuesto: null,
                DPI: null,
                NIT: null,
                Ajeno: null,
                IdSlack: null,
                Indicio: null,
                CorreoGrupo: null,
                Telefono: null,
                src: null,
                ClaveCorreo: null,
                ClaveIngresada: null

            },

            ClaveIngresada: 0,
            ClaveCorreo: '',
            changepass: false,//popup
        }
    },

    components: {
        SMImagen,
        ChangePassword,
    },
    computed: {
        sesion() {
            return this.$store.state.sesion
        },
        global() {
            return this.$store.state.global
        }
    },

    methods: {
        CambioClave() {
            this.$vs.dialog({
                type:'confirm',
                title: `Cambio de clave`,
                text: 'Se enviará token de verificación para actualizar su clave.\n ¿Desea continuar?',
                accept: ()=> {
                    this.$refs.refChangePassword.EnviarToken().then(()=>{
                        this.changepass = true
                    })
                },
                acceptText: 'Si',
                cancelText: 'No'
            })
        },
        logout() {
            this.$store.dispatch('sesionEliminar')
            location.replace("/pages/login");
        },

        //visualizar Informacion 
        Visualizar_Perfil() {
            this.Consulta().ConsultaCorporativo();
            this.editar = true

        },

        //consulta de informacion Corporativo
        Consulta_Foto_login() {
            this.ConsultaCorporativo()
        },

        //Cambio de Sucursal
        cambiar_sucursal() {
            this.axios.post('/app/usuario/obtener_sucursal', {})
                .then(resp => {
                    let temp_emp = ''
                    this.seleccionEmpresa.empresas = []
                    this.seleccionEmpresa.mostrar = true;
                    resp.data.json.map(data => {
                        if (temp_emp != data.Codigo) this.seleccionEmpresa.empresas.push({
                            Empresa: data.Codigo,
                            Nombre: data.Nombre,
                            Sub: []
                        })
                        if (data.CodigoSuc != "") this.seleccionEmpresa.empresas[this.seleccionEmpresa.empresas.length - 1].Sub.push(data)
                        temp_emp = data.Codigo
                    })
                })
        },

        //Consulta Automatica de corporativo 
        Consulta() {
            return {
                ConsultaCorporativo: () => {

                    const corporativoUsuario = this.sesion.corporativo; // Asumo que tienes un objeto "sesion" con la información del usuario

                    if (corporativoUsuario) {
                        this.axios.post('/app/administracion/CorporativoConsulta', {
                            IdCorporativo: corporativoUsuario
                        })
                            .then(resp => {

                                if (resp.data.json) {

                                    this.$map({
                                        objeto: this.info,
                                        respuestaAxios: resp,
                                        omitir: ['corporativoUsuario']
                                    })

                                }

                            });
                    }
                },
            };
        },

        //Edición
        Editar() {
            return {
                Editar: () => {
                    //Validando tipo Casa y su subclasificación
                    if (this.info.Tipo == "M" && !this.info.TipoEnMedax) {
                        this.$vs.dialog({
                            color: "danger",
                            title: "Ajenos - Error",
                            text: "Falta definir SubClasificación en Honorarios",
                        });
                        return false;
                    }
                    this.Consulta().ConsultaCorporativo();
                },
            };
        },

        //Guarda y encripta contrseña correo
        Guardar() {
            return {
                Corporativo: () => {
                    this.axios.post('/app/administracion/CorporativoGuardar', {
                        ...this.info,

                        editar: this.editar
                    })
                    .then(resp => {
                                if (resp.data.codigo ==0 ){
                                    this.Consulta().ConsultaCorporativo()
                                }else{
                                    this.Consulta().ConsultaCorporativo()
                                }                            
                        })
                        .catch({});
                },
            };
        },

        //Actualizacion de Imagenes 
        Consulta_Realizada(imagen_gordito) {
            this.info.src = imagen_gordito.src

        },

        //Actualizacion de Eliminacion de Imagen
        actualizarArchivoEliminado() {
            this.Consulta().ConsultaCorporativo();
        },

        //Visualiza la contraseña ya desencriptada 
        visualizarContraseñaDesencriptada() {
            this.desencriptarTexto();
        },

        //Desencriptar Contraseña
        desencriptarTexto() {
            this.axios.post('/app/administracion/Desencriptardatos', { ClaveCorreo: this.info.ClaveCorreo })
                .then(response => {
                    const nuevoTexto = response.data;
                    if (this.textoDesencriptado == nuevoTexto) {
                        this.textoDesencriptado = ''; // Si es el mismo dato, lo borra
                    } else {
                        this.textoDesencriptado = nuevoTexto; // Si es diferente, coloca el nuevo valor
                    }
                })
                .catch(error => {
                    this.$vs.notify({
                    title: 'NOTIFICACION!',
                    text: 'Error al desencriptar ' + error,
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'warning'
                })
            });
        },

        //Cambia es estado del input    
        Cambiar_Estadpo() {
            if (this.info.ClaveIngresada == 1) {
                this.info.ClaveIngresada = this.info.ClaveCorreo
                this.info.ClaveCorreo = '';
                this.$vs.notify({
                    title: 'ADVERTENCIA!',
                    text: 'YA CUENTAS CON UNA CONTRASEÑA ESTABLECIDA',
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'danger'
                })
                return
            } else if (this.ClaveCorreo == 0) {
                this.Consulta().ConsultaCorporativo()
                this.$vs.notify({
                    title: 'NOTIFICACION!',
                    text: 'CONTRASEÑA NO FUE MODIFICADA!',
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'warning'
                })
                return
            } else {
                this.info.ClaveIngresada == 0
            }
        },

        // validando privilegios
        validar_privilegios() {
            this.axios.post('/app/usuario/obtener_privilegios', {})
                .then(resp => {
                    this.$store.dispatch('sesionValidar') //valida que se ha cargado la informacion
                    if (resp.data.tipo == "Empresa") {
                        let temp_emp = ''
                        this.seleccionEmpresa.mostrar = true;
                        resp.data.json.map(data => {
                            if (temp_emp != data.Codigo) this.seleccionEmpresa.empresas.push({
                                Empresa: data.Codigo,
                                Nombre: data.Nombre,
                                Sub: []
                            })

                            this.seleccionEmpresa.empresas[this.seleccionEmpresa.empresas.length - 1].Sub.push(data)
                            temp_emp = data.Codigo
                        })

                    } else {
                        this.$store.dispatch('funcionalidadListado', resp.data.json)
                        this.seleccionEmpresa.mostrar = false;
                    }
                })
        },

        // seleccionar Sucursal
        seleccion_sucursal(vsucursal, vtipo) {
            this.axios.post('/app/usuario/seleccion_sucursal', {
                Sucursal: vsucursal,
                Tipo: vtipo
            })
                .then(resp => {
                    this.validar_privilegios() // verificar nuevamente los privilegios
                    this.$store.dispatch('sesionGuardar', resp.data)

                })
        },

        Otros() {
            return {
                visible: (visible) => {
                    this.info.visible = visible
                },
                b64toBlob: (b64Data, contentType = '', sliceSize = 512) => {
                    const byteCharacters = atob(b64Data);
                    const byteArrays = [];

                    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
                        const slice = byteCharacters.slice(offset, offset + sliceSize);

                        const byteNumbers = new Array(slice.length);
                        for (let i = 0; i < slice.length; i++) {
                            byteNumbers[i] = slice.charCodeAt(i);
                        }

                        const byteArray = new Uint8Array(byteNumbers);
                        byteArrays.push(byteArray);
                    }

                    const blob = new Blob(byteArrays, {
                        type: contentType
                    });
                    return blob;
                }

            }
        },

        refresh() {
            //registrando al usuario en el socket
            this.$socket.emit('actualizar')
        },

        mounted() {
            this.permisos.editar = this.$validar_privilegio("EDITAR").status;
            this.permisos.cambiar_imagen = this.$validar_privilegio("CAMBIAR_IMAGEN").status;

        },
    }
}
</script>
            
<style scoped>
.lock {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999999;
}

.seleccon_empresa {
    padding: 15px;
    background-color: white;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
    cursor: pointer;
    border-radius: 5px;
    margin-bottom: 5px;
}

.seleccionEmpresa .vs-popup {
    width: 450px !important;
}

.usuario {
    height: 15px;
    font-weight: bold;
    padding-top: 5px;
    padding-right: 50px;

}

.socket {
    height: 15px;
    width: 15px;
    border-radius: 100%;
    background-color: red;
    position: absolute;
    top: 31px;
    right: 1px;
    border: 1px solid white
}

.panel {
    /* background-color: rgba(var(--vs-primary), 1); */
    color: white;
    /* padding: 5px 65px 5px 20px; */
    position: relative;
    width: auto;
    top: 15px;
    overflow: hidden;
    margin-right: -14px;
    font-size: 13px;
    /* right: 50px; */
    /* border-radius: 30px 0 0 0; */
    /* box-shadow: inset 0 2px 0px 1px rgba(0, 0, 0, 0.2); */
    /* cursor: pointer; */
}

.panel-sucursal {
    display: inline-block;
    background-color: rgba(var(--vs-primary), 1);
    color: white;
    padding: 5px 65px 5px 10px;
    position: relative;
    width: auto;
    /* top: 12px; */
    overflow: hidden;
    /* margin-right: -14px; */
    font-size: 13px;
    /* right: 50px; */
    /* border-radius: 30px 0 0 0; */
    /* box-shadow: inset 0 2px 0px 1px rgba(0, 0, 0, 0.2); */
    cursor: pointer;
}

.panel-instancia {
    display: inline-block;
    opacity: 0.7;
    /* position: absolute; */
    /* background-color: #2ECC71; */
    color: white;
    padding: 5px 10px 5px 10px;
    width: auto;
    /* top: 12px; */
    overflow: hidden;
    /* margin-right: -14px; */
    font-size: 13px;
    /* right: 50px; */
    border-radius: 10px 0 0 0;
    /* box-shadow: inset 0 2px 0px 1px rgba(0, 0, 0, 0.2); */
    /* cursor: pointer; */
}

.perfil {
    position: absolute;
    right: 5px;
    /* background: red; */
}

.perfil::before {
    content: '';
    height: 60px;
    width: 60px;
    background-color: white;
    position: absolute;
    /* z-index: -1;  */
    left: 5px;
    top: -5px;
    border-radius: 100%;
}

.Foto_Perfil {

    height: 140px;
    width: 140px;
    border-radius: 6.5REM;
    border-color: #839192;
    border-width: 2px;
    border-style: solid;
}

.button-container {
    display: flex;
    justify-content: space-between;
}

.button-item {
    flex: 1;
    margin-right: 10px;
    text-align: center;
}

</style>

<style>
.popup-update-password.con-vs-popup .vs-popup {
    width: 30rem !important;
}
</style>
