<template>
<div>
    <vx-card :title="`Punto de Venta de Clínicas - ${sesion.sesion_sucursal_nombre}`" >
            <ConfiguracionCaja
                    @CargarCaja="CargarCaja" 
                    :FacturaCambiaria=0  
                    AgrupacionCaja="CajaPuntoDeVenta" 
                    TipoCajaSeleccionada="TipoCajaFacturacion" 
                    CajaSeleccionada="CajaFacturacion" 
                    class="w-full">
            </ConfiguracionCaja>

        <!-- reportes -->
        <vs-popup id="contentreport" classContent="popup-generar" title="Informe" :active.sync="info.reporte" fullscreen style="z-index:99998;height:100%">
            <embed v-if="info.reporte_src!=''" type="application/pdf" :src="info.reporte_src" ref="pdfDocument" width="100%" height="90%" />
        </vs-popup>

        <vs-row style="margin-top: 5px;">
            <vs-col class="md:w-1/2 lg:w-1/2 xl:w-1/2 sm:w-1/2 pl-2">

                <!--generar lote de primas-->
                <h5 style="margin-top: -5px; height: 17px;">Genera lote de primas SaSi</h5>
                <div class="flex flex-wrap smtabla" style="height: 70px !important">
                    <vs-row class="w-full">
                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-2">
                            <vs-input class="w-full" type="date" 
                                v-model="info.fechaPrimas"
                                :disabled="!info.generaPrimas" />
                        </div>
                        <vs-button style="height: 40px; margin-top: 5px; margin-left: 10px"
                            :disabled="!GetDateValue(info.fechaPrimas)" 
                            @click="Consulta().primasRoble()">Generar lote</vs-button>
                        <vs-button style="height: 40px; margin-top: 5px; margin-left: 10px"
                            :disabled="!info.lotePrimas" 
                            @click="Facturar().Primas2XL()">Exportar</vs-button>
                        
                        <h5 v-show="info.lotePrimas" style="color: darkred;  padding-left: 12px; margin-top: 20px;">Procesando lote</h5>
                        <!-- <vs-checkbox v-model="info.lotePrimas">Procesando lote de primas</vs-checkbox> -->
                    </vs-row>
                </div>
                
                <h5 style="margin-top: -5px;">Datos de la factura</h5>
                <div class="flex flex-wrap smtabla" style="height: 250px !important">
                    <div class="flex flex-wrap">

                        <!--inicia bloque de Nit-->
                        <vs-row class="w-full">
                            <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12" style="height: 60px;">
                                <label class="typo__label label-shrink" style="white-space: nowrap; color:#626262; font-size:12px;  padding-bottom:2px; padding-top:0px;"> Tipo Documento </label>
                                <DxSelectBox style="padding: 2px;" 
                                    :items="documento.tiposDocumentoLista" 
                                    display-expr="Descripcion" 
                                    placeholder="Seleccione..." 
                                    v-model="documento.tipoDocumento" />
                            </vs-col>
                            <vs-col class="sm:w-full md:w-full lg:w-8/12 xl:w-4/12 pl-5 pr-2">
                                <vs-input label="No. documento" 
                                    placeholder="C/F" class="w-full" 
                                    v-model="documento.noDocumento" 
                                    v-on:change="documento.noDocumento = documento.noDocumento.toUppercase(); {{ documento.valido = null }}" 
                                    @dblclick="AsignaNit('C/F')" 
                                    @keydown.enter="ValidacionDocumentoTributario()" 
                                    @keydown.tab.prevent="ValidacionDocumentoTributario()"
                                />
                            </vs-col>
                            <vs-col class="sm:w-full md:w-full lg:w-full xl:w-6/12 pr-5">
                                <vs-input style="margin-left: 10px" label="Nombre" placeholder="Consumidor final" class="w-full" v-model="documento.nombre" />
                            </vs-col>
                        </vs-row>

                        <!--forma de pago-->
                        <vs-col class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-2">
                            <h6>Forma de pago</h6>
                            <vs-input label="Efectivo:" v-model="info.efectivo" 
                                placeholder="Monto en efectivo"
                                @keyup.alt.enter="Facturar().detalle()"
                                style="text-align: right;" class="w-full" />

                            <vs-input label="Tarjeta:" v-model="info.tarjeta" 
                                placeholder="Monto en tarjeta" id = "MoneyCard"
                                style="text-align:right" class="w-full" />

                            <td class="p-1" width="490px"  style="text-align:right;">
                                <h5>{{"Total pagado " + parseFloat(totalPagado).toLocaleString("es-GT", {style: "currency",currency: "GTQ" })}}</h5>
                            </td>
                        </vs-col>

                        <vs-col class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 mt-12" 
                            style="padding:10px;
                            height: 30px;
                            border-radius:5px">
                            <vs-row>
                                <vs-radio class="pl-1" v-model="info.tipoPos" vs-value="VIS">Visa</vs-radio>
                                <vs-radio class="pl-12" v-model="info.tipoPos" vs-value="CRE">Credomatic</vs-radio>
                            </vs-row>
                            <vs-input label="Autorización" placeholder="Autorización del POS" 
                                @keyup.alt.enter="Facturar().detalle()"
                                style="padding-top: 12px"
                                v-model="info.autoriza" class="w-full" />
                            <div data-v-5e12eaeb="" class="vs-spacer"></div>

                            <td class="p-1"  width="490px">
                                <h5 style="color:green;" 
                                v-show="this.vueltoCash >0">{{"Vuelto " + parseFloat(vueltoCash).toLocaleString("es-GT", {style: "currency",currency: "GTQ" })}}</h5>
                            </td>
                            <td class="p-1"  width="490px" style="text-align:right;">
                                <h5 style="color: darkred" v-show="this.saldoPendiente >0">{{"Saldo " + parseFloat(saldoPendiente).toLocaleString("es-GT", {style: "currency",currency: "GTQ" })}}</h5>
                            </td>
                        </vs-col>
                    </div>
                </div>

                <!--productos agregados-->
                <h5>Detalle factura</h5>
                <div>
                    <vs-table2 max-items="15" :data="detalleFac" style="height: 350px;">
                        <template slot="thead">
                            <th width="100px">Producto</th>
                            <th>Nombre</th>
                            <th width="150px">Precio U.</th>
                            <th width="90px">Cita</th>
                            <th width="90px">Cupón</th>
                            <th width="85px">Cant.</th>
                        </template>
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2>
                                    {{ tr.Producto }}
                                </vs-td2>
                                <vs-td2>
                                    <div class="col-md-8">
                                        <small>
                                            {{ tr.Nombre }}
                                        </small>
                                        <div style="margin-top:-3px">
                                            <small>{{tr.NombreCliente}}</small>
                                        </div>
                                    </div>
                                </vs-td2>

                                <vs-td2 style="text-align:right" v-if="tr.Precio == 0.00">
                                    <vs-input type="number" class="w-full" 
                                        v-model="info.precioLibre"
                                        @keyup.enter="AsignaPrecio()"
                                    />
                                </vs-td2>
                                <vs-td2 style="text-align:right" v-if="tr.Precio != 0.00">
                                        {{parseFloat(tr.Precio).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                </vs-td2>

                                <vs-td2 style="text-align:right">
                                    {{ tr.IdCita }}
                                </vs-td2>
                                <vs-td2 style="text-align:right">
                                    {{ tr.Cupon }}
                                </vs-td2>

                                <vs-td2>
                                    <div class="cantidad">
                                        <div>{{ tr.Cantidad }}</div>
                                        <button v-if="tr.Cantidad>1" type="filled" @click="tr.Cantidad--">-</button>
                                        <button v-else type="filled" :disabled="info.lotePrimas" @click="detalleFac.splice(indextr,1)">-</button>
                                    </div>
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
                </div>
            </vs-col>

            
            <vs-col class="md:w-1/2 lg:w-1/2 xl:w-1/2 sm:w-1/2 pl-2 mt-1">
                <div>
                    <!-- Listado citas y cupones -->
                    <div class="flex" style="height: 15px !important; margin-top: -8px;">
                            <h5>Citas y Cupones</h5>
                            <div data-v-5e12eaeb="" class="vs-spacer"></div>
                        <!--Botonera de actualización y reimpresión de factura/corte-->

                        <vx-tooltip text="actualiza listado de citas/cupones" style="margin-top: -6.75%; margin-left: .5%;">
                            <vs-button  style="height: 35px;" color="green" 
                            icon-pack="feather" icon="icon-refresh-cw"  
                            @click="Consulta().telemedicina()"></vs-button>
                        </vx-tooltip>

                        <vx-tooltip text="Reimprime última factura emitida" style="margin-top: -6.75%; margin-left: .5%;">
                                <vs-button  style="height: 35px;" color="green" 
                            icon-pack="feather" icon="icon-printer"
                            :disabled="totalFacturado >0"  
                            @click="Consulta().Reprintfactura()"></vs-button>
                        </vx-tooltip>
                        <vx-tooltip text="Reimprime último corte de caja" style="margin-top: -6.75%; margin-left: .5%;">
                            <vs-button  style="height: 35px;" color="green" 
                            icon-pack="feather" icon="icon-box"  
                            :disabled="totalFacturado >0"
                            @click="Consulta().ReprintCorte()"></vs-button>
                        </vx-tooltip>
                    </div>          
                    
                    <vs-table2 max-items="15" 
                        search pagination
                        :data="citasCoEx" height="200px">

                        <template slot="thead">
                            <th order="Producto" width="120px">Producto</th>
                            <th order="Nombre">Nombre</th>
                            <th order="Precio" width="120px">Precio</th>
                            <th order="Cita" width="100px">Cita</th>
                            <th order="Cupon" width="100px">Cupón</th>
                            <!-- <th order="Exist" width="50px">Existencia</th> -->
                        </template>
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data" @click="Interfaz().agregaCitaCupon(tr)">
                                <vs-td2>
                                    {{ tr.Producto }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.NombreCliente + tr.Afiliado}}
                                    <div style="margin-top:-3px">
                                        <small>{{tr.Nombre +" - Nit: "+tr.Nit}}</small>
                                    </div>
                                    <div style="font-style: italic">
                                        <small>{{tr.Medico + tr.Especialidad}}</small>
                                    </div>
                                </vs-td2>
                                <vs-td2 style="text-align:right">
                                    {{parseFloat(tr.Precio).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                </vs-td2>
                                <vs-td2 style="text-align:right">
                                    {{tr.IdCita }}
                                </vs-td2>
                                <vs-td2 style="text-align:right">
                                    {{ tr.Cupon }}
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>

                    <!-- Listado de productos y servicios -->
                    <h5>Productos y Servicios</h5>
                    <vs-table2 style="height: 350px;"
                        max-items="10" search pagination 
                        :data="productos" height="60px">
                        <template slot="thead">
                            <th order="Producto" width="120px">Producto</th>
                            <th order="Nombre">Nombre</th>
                            <th order="Precio" width="150px">Precio</th>
                        </template>
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data" 
                                @click=Interfaz().agregaProducto(tr)>
                                <vs-td2>
                                    {{ tr.Producto }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.Nombre }}
                                </vs-td2>
                                <vs-td2 style="text-align:right">
                                    {{parseFloat(tr.Precio).toLocaleString("es-GT", {style: "currency",currency: "GTQ" })}}
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>

                </div>
            </vs-col>
        </vs-row>

        <div data-v-5e12eaeb="" class="flex bottom">
            <!--total a facturar ********* -->
            <vs-row>
                <vs-button @click="Facturar().detalle()" 
                    :disabled="totalFacturado == 0 && configuracion.activarFacturacion">Facturar</vs-button>
                <vs-button color="danger" style="margin-left: 10px" 
                    @click="Otros().limpiar()">Cancelar</vs-button>
                <td class="p-1" style="margin-left: 8%">
                    <h4 style="text-align: right;" >{{"Total: " + parseFloat(totalFacturado).toLocaleString("es-GT", {style: "currency",currency: "GTQ" })}}</h4>
                </td>
                <div data-v-5e12eaeb="" class="vs-spacer"></div>

                <vs-button style="margin-left: end;" @click="CorteCaja().ValidaCorte()"
                    :disabled="totalFacturado >0">Corte Caja</vs-button>
                <vs-button 
                    style="margin-left: 10px;" 
                    @click="ImpresionArqueo()"
                    :disabled="totalFacturado >0">Arqueo</vs-button>
            </vs-row>
        </div>
    </vx-card>
</div>
</template>

<script>
import {ref} from 'vue'
import ConfiguracionCaja from "/src/components/sermesa/modules/caja/ConfiguracionCaja.vue";
import VxCard from '../../../components/vx-card/VxCard.vue';
import "devextreme/ui/lookup";
import moment from "moment";

export default {
    components: {
        ConfiguracionCaja,
        VxCard
    },

    data() {
        return {
            configuracion:{
                cajaSeleccionada: null,
                activarFacturacion: false,
                activarRecibos: false  
            },
            sndMsg: {
                errorMsg: 'danger',
                succesMsg: 'success',
                infoMsg: 'warning',
                delayTime: 4000
            },
            info: {
                reporte: false,
                reporte_src: null,
                tgtReporte : 'application/pdf',

                efectivo: null,
                tarjeta: null,
                pagado: null,
                vuelto: null,
                autoriza: null,
                tipoPos: 'VIS',
                montoUltimo:0,
                precioLibre:0,
                sinPrecio:0,

                serieFel:  null,
                facturaFel:null,
                tipoDocSat:null,
                nitCliente:null,
                NombreCli :null,
                
                numCaja: 112,
                numCorte: null,
                numBodega: 99,
                proceso: 1,
                lotePrimas: false,
                fechaPrimas: null,
                generaPrimas: false,
                tipoImpresion:'ImpresioFelTmu'

            },
            documento: {
                tiposDocumentoLista: [],
                tipoDocumento: null,
                noDocumento: null,
                nombre: null,
                correo: null,
                valido: null,
                direccion: null
            },

            productos: [],
            citasCoEx: [],
            detalleFac: []
        }
    },
    computed: {
        totalFacturado() {
            return Math.round(
            this.detalleFac.reduce((acumulador, detalle) => acumulador + parseFloat(detalle.Precio,2) * detalle.Cantidad, 0)
            * 100.00) / 100.00
        },
        totalPagado() {
            return this.ConvertirFloat(this.info.efectivo) + this.ConvertirFloat(this.info.tarjeta)
        },
        saldoPendiente() {
            if ((this.totalFacturado - this.totalPagado) >0 ) {
                return (this.totalFacturado - this.totalPagado)
            }else{
                return 0
            }
        },
        vueltoCash(){
            if (this.totalFacturado >0) {
                return this.ConvertirFloat(this.info.efectivo) - (this.totalFacturado - this.ConvertirFloat(this.info.tarjeta))
            }else{ 
                return 0
            }
        },
        ControlCupon() {
            return this.detalleFac.reduce((acumulador, detalle) => acumulador + parseInt(detalle.Cupon), 0)
        },
        sesion() {
            return this.$store.state.sesion
        }

    },
    methods: {
        AsignaPrecio() {
            if (this.info.precioLibre >0) {
                this.detalleFac[0].Precio = this.info.precioLibre;
                this.montoUltimo = this.info.precioLibre;
            }
            this.info.precioLibre = 0;
        },
        GetDateValue(value) {
            if (value === undefined) {
                return null
            } else if (value === null) {
                return null
            } else if (value == '') {
                return null
            }
            return moment(value).format('YYYYMMDD');
        },
        ChkSinPrecio(){
            this.info.sinPrecio = 0;
            for (let step = 0; step < this.detalleFac.length; step++) {
                if (this.detalleFac[step].Precio <= 0)
                { 
                    this.info.sinPrecio = 1;
                }
            }
        },
        CargarCaja(cajaSeleccionada){                
            this.configuracion.cajaSeleccionada   = cajaSeleccionada.Cajero
            this.configuracion.activarFacturacion = cajaSeleccionada.Factura
            this.configuracion.activarRecibos     = cajaSeleccionada.Recibo
        },
        ConvertirFloat(Valor, Decimales = 2) {
            let conversion = parseFloat(Valor ? Valor : 0.00)
            if (isNaN(conversion)) {
                return 0.00
            }
            return parseFloat(conversion.toFixed(Decimales))
        },
        ValidacionDocumentoTributario() {
            if (this.Consulta().EsVacia(this.documento.tipoDocumento)) {
                this.Consulta().MostrarMensaje('Seleccione un tipo de documento', this.sndMsg.errorMsg);
                return;
            }
            if (this.Consulta().EsVacia(this.documento.noDocumento)) {
                this.Consulta().MostrarMensaje('Ingrese el número de documento', this.sndMsg.errorMsg);
                return;
            }
            this.axios.post('/app/v1_caja/ValidacionDocumentoTributario', {
                    tipoReceptor: parseInt(this.documento.tipoDocumento.Valor),
                    documento: this.documento.noDocumento
                })
                .then(resp => {
                    if (resp.data.codigo == 0 && resp.data.json.length > 0 && resp.data.json[0].codigo == 0) {
                        this.documento.nombre = resp.data.json[0].NOMBRE
                        this.documento.correo = resp.data.json[0].EMAIL
                        this.documento.direccion = resp.data.json[0].DIRECCION
                        this.documento.valido = 1
                    } else if (resp.data.codigo == 0 && resp.data.json.length > 0 && resp.data.json[0].codigo != 0) {
                        this.Consulta().MostrarMensaje(resp.data.json[0].descripcion, this.sndMsg.errorMsg);
                        this.documento.nombre = null
                        this.documento.correo = null
                        this.documento.valido = null
                        this.documento.direccion = null
                    } else if (resp.data.json.length == 0) {
                        this.documento.nombre = null
                        this.documento.correo = null
                        this.documento.valido = null
                        this.documento.direccion = null
                        this.Consulta().MostrarMensaje('No se encontraron datos para el documento ingresado', this.sndMsg.errorMsg);
                    }
                })

        },
        AsignaNit(value) {
            this.documento.tipoDocumento = ref(this.documento.tiposDocumentoLista.find(doc => doc.Descripcion == 'NIT'))
            this.documento.noDocumento = value;
            this.ValidacionDocumentoTributario()
        },
        CargarTipoDocumento() {
            this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision', {
                    opcion: 'C',
                    subOpcion: 2,
                    agrupacion: 'TipoReceptor'
                })
                .then(resp => {
                    if (resp.data && resp.data.json.length > 0) {
                        this.documento.tiposDocumentoLista = resp.data.json;
                        this.documento.tipoDocumento = 4;
                    }
                })
        },
        CorteCaja(){
            return {
                ValidaCorte:() => {
                    if (this.totalFacturado > 0) {
                        this.Consulta().MostrarMensaje('Facturación en proceso.  Finalice o cancele.', this.sndMsg.errorMsg)
                        return;
                    }
                    if (!this.configuracion.cajaSeleccionada.CajaLocal || this.configuracion.cajaSeleccionada.CajaLocal <= 0){
                        this.Consulta().MostrarMensaje('Número de caja NO válido.', this.sndMsg.errorMsg)
                        return;
                    }
                    this.$vs.dialog({
                        type: 'confirm',
                        color: 'success',
                        title: 'Confirmar',
                        acceptText: 'SI',
                        cancelText: 'NO',
                        text: 'Desea realizar el corte de caja?',
                        accept: ()=>{
                            this.CorteCaja().HaceCorte();
                        }
                    })
                },
                HaceCorte:() =>{
                   
                    this.Consulta().MostrarMensaje('Corte caja: '     + 
                        this.configuracion.cajaSeleccionada.CajaLocal +
                        '.  Por favor espere...',this.sndMsg.succesMsg)

                    // if (!this.info.primasRoble) {return}

                    this.axios.post('/app/v1_api_PosFarmacia/CorteCajaCoEx',{
                        numeroCaja: this.configuracion.cajaSeleccionada.CajaLocal
                    }).then( resp => {
                        if (resp.data.json[0].codigo != 0) {
                            this.Consulta().MostrarMensaje(resp.data.json[0].descripcion, this.sndMsg.errorMsg)
                            return false
                        }else{
                            this.Consulta().MostrarMensaje('Imprimiendo corte: '+ resp.data.json[0].NumCorte,this.sndMsg.succesMsg)
                                this.info.numcorte = resp.data.json[0].NumCorte;
                                this.ImpresionCorteArqueo(this.info.numcorte);
                                // this.CorteCaja().ImprimeCorte();
                            }
                        })
                },
                ImprimeCorte:(NumeroCorte)=>{
                    return new Promise((resolve) =>{

                        this.axios.post("/app/reporte/ReporteGenerador", {
                        Nombre: "Impresion Corte CoEx",
                            Opciones: {
                                tiporeporte: this.info.tgtReporte,
                                nombrereporte: "ImprimeCorteCoEx",
                                caja : this.configuracion.cajaSeleccionada.CajaLocal,
                                corte: NumeroCorte,
                                nombreImpresora:'TMU'
                            }
                        }, {
                            responseType: 'arraybuffer'
                        })
                        .then(resp => {
                                this.$store.dispatch('reporte', {
                                    pdf: 'data:application/pdf;base64,' + Buffer.from(resp.data, 'binary').toString('base64'),
                                    abrirTab: true, 
                                    imprimir: false,
                                    titulo: 'Impresion Fel',
                                })
                            this.Consulta().MostrarMensaje('Impresión corte finalizada.',this.sndMsg.succesMsg);
                            resolve(true);
                        })
                    })
                },
                ArqueoCoEx:(numArqueo)=>{
                    return new Promise((resolve) =>{

                        this.axios.post("/app/reporte/ReporteGenerador", {
                        Nombre: "Impresion Arqueo CoEx",
                            Opciones: {
                                tiporeporte: this.info.tgtReporte,
                                nombrereporte: "ImprimeArqueo",
                                caja : this.configuracion.cajaSeleccionada.CajaLocal,
                                corte: numArqueo,
                                nombreImpresora:'TMU'
                            }
                        }, {
                            responseType: 'arraybuffer'
                        })
                        .then(resp => {
                                this.$store.dispatch('reporte', {
                                    pdf: 'data:application/pdf;base64,' + Buffer.from(resp.data, 'binary').toString('base64'),
                                    abrirTab: true, 
                                    imprimir: false,
                                    titulo: 'Impresion Arqueo CoEx',
                                })
                            this.Consulta().MostrarMensaje('Impresión corte finalizada.',this.sndMsg.succesMsg);
                            resolve(true);
                        })
                    })
                }
            }
        },
                 
        Consulta() {
            return {
                EsVacia: (variable) => {
                    return variable ? false : true;
                },
                MostrarMensaje:(mensaje, tipoMsg)=>{
                        this.$vs.notify({
                            time:this.sndMsg.delayTime,
                            title: 'Punto de Venta',
                            color: tipoMsg,
                            text:mensaje,
                            position: 'top-center'
                        })
                },

                init: () => {         
                    this.CargarTipoDocumento();
                    this.info.generaPrimas = this.$validar_privilegio('FACTURAR_PRIMAS').status

                    //citas y cupones
                    this.citasCoEx = []
                    this.info.proceso = 15
                    this.axios.post('/app/v1_api_PosFarmacia/ProductosCoEx', {
                        tipoProceso: this.info.proceso
                    }).then(resp => {
                        if (resp.data && resp.data.json.length > 0) this.citasCoEx = resp.data.json
                    })
                },
                //productos diversos
                cargaProductos: ()=>{
                    this.productos = []
                    this.info.proceso = 14
                    this.info.serieFel = this.configuracion.cajaSeleccionada.SerieFac
                    this.axios.post('/app/v1_api_PosFarmacia/ProductosCoEx', {
                        serieFactura: this.info.serieFel,
                        tipoProceso: this.info.proceso
                    }).then(resp => {
                        if (resp.data && resp.data.json.length > 0) this.productos = resp.data.json
                    })
                },
                //citas y cupones (refresh)
                telemedicina: () => {
                    this.citasCoEx = []
                    this.info.proceso = 15

                    this.axios.post('/app/v1_api_PosFarmacia/ProductosCoEx', {
                        tipoProceso: this.info.proceso
                    }).then(resp => {
                        if (resp.data && resp.data.json.length > 0) this.citasCoEx = resp.data.json
                    })
                },
                //obtiene ultimo corte realizado
                ReprintCorte: ()=>{
                    this.axios.post('/app/v1_api_PosFarmacia/ReprintCorteFel', {
                        tipoProceso : 10,
                        numeroCaja  : this.configuracion.cajaSeleccionada.CajaLocal
                    }).then(resp => {
                        if (resp.data && resp.data.json.length > 0) {
                            this.info.numcorte = resp.data.json[0].NumCorte;
                            this.ImpresionCorteArqueo(this.info.numcorte);
                            
                        }
                    })
                },
                //obtiene ultima factura emitida
                Reprintfactura: ()=>{
                    this.axios.post('/app/v1_api_PosFarmacia/ReprintCorteFel', {
                        tipoProceso : 9,
                        serieFactura: this.configuracion.cajaSeleccionada.SerieFac
                    }).then(resp => {
                        if (resp.data && resp.data.json.length > 0) {
                            this.info.serieFel = this.configuracion.cajaSeleccionada.SerieFac
                            this.info.facturaFel = resp.data.json[0].Factura;

                            this.ImpresionFactura(0);
                        }
                    })
                },                
                //consolidado de primas (detalle fac)
                primasRoble: () => {
                    this.detalleFac = []
                    this.info.proceso = 17
                    if (!this.info.fechaPrimas) {
                        this.Consulta().MostrarMensaje('Ingrese fecha de registro de primas El Roble', this.sndMsg.errorMsg);
                        return false
                    }
                    this.axios.post('/app/v1_api_PosFarmacia/ProductosCoEx', {
                        fechaPrimas: this.info.fechaPrimas,
                        tipoProceso: this.info.proceso
                    }).then(resp => {
                        if (resp.data && resp.data.json.length > 0) {
                            this.info.lotePrimas = true;
                            this.detalleFac = resp.data.json
                        }
                        this.info.tarjeta = 0
                        this.info.autoriza = ''
                        this.info.efectivo = this.totalFacturado;

                        this.documento.noDocumento = resp.data.json[0].Nit
                        this.documento.tipoDocumento = ref(this.documento.tiposDocumentoLista.find(doc => doc.Descripcion == 'NIT'))
                        this.ValidacionDocumentoTributario()
                    })
                }
            }
        },

        async ImpresionArqueo(){
            await this.CorteCaja().ArqueoCoEx(0)
            .then(()=>{
                this.Otros().limpiar();
            });
        },

        async ImpresionCorteArqueo(NumCorteArqueo) {
            await this.CorteCaja().ArqueoCoEx(NumCorteArqueo)
            .then(() => {
                     this.CorteCaja().ImprimeCorte(NumCorteArqueo);             
            });
        },

        async ImpresionFactura(ctrlCupon) {

            await this.Facturar().ImprimeFel()
            .then(()=>{
                this.Facturar().VoucherCoEx()
                .then(()=>{
                    if (ctrlCupon > 0) {
                        this.Facturar().ImprimeCupon()
                    }
                })
            })
            .then(()=>{
                this.Otros().limpiar();
            })
        },

        Facturar() {
            return {
                Primas2XL: () => {
                   this.axios.post("/app/reporte/ReporteGenerador", {
                    Nombre: "Impresion Fel",
                        Opciones: {
                            tiporeporte: "application/vnd.ms-excel",
                            nombrereporte: "PrimasSaSiDetalle",
                            SerieFactura : this.info.serieFel,
                            NumFactura: 1,
                            fechaPrimas: this.info.fechaPrimas
                        }
                    }, {
                        responseType: 'arraybuffer'
                    })
                    .then(resp => {
                            this.$store.dispatch('reporte', {
                                pdf: 'data:application/vnd.ms-excel;base64,' + Buffer.from(resp.data, 'binary').toString('base64'),
                                abrirTab: false,
                                imprimir: false,
                                titulo: 'PrimasSaSiDetalle'
                                
                            })
                    })
                },

                ImprimeFel: ()=>{
                    return new Promise((resolve) =>{

                        this.axios.post("/app/reporte/ReporteGenerador", {
                        Nombre: "Impresion Fel",
                            Opciones: {
                                tiporeporte: this.info.tgtReporte,
                                nombrereporte: this.configuracion.cajaSeleccionada.FormatoImpresion,
                                SerieFactura : this.info.serieFel,
                                NumFactura: this.info.facturaFel,
                                nombreImpresora:'TMU'
                            }
                        }, {
                            responseType: 'arraybuffer'
                        })
                        .then(resp => {
                                this.$store.dispatch('reporte', {
                                    pdf: 'data:application/pdf;base64,' + Buffer.from(resp.data, 'binary').toString('base64'),
                                    abrirTab: true,
                                    imprimir: false,
                                    titulo: 'Impresion Fel',
                                })
                            this.Consulta().MostrarMensaje('Impresión factura finalizada.',this.sndMsg.succesMsg);
                            resolve(true)
                        })
                    })
                },
                VoucherCoEx: ()=>{
                    return new Promise((resolve) =>{
                        this.axios.post("/app/reporte/ReporteGenerador", {
                        Nombre: "Impresion Fel",
                            Opciones: {
                                tiporeporte: this.info.tgtReporte,
                                nombrereporte: "ImprimeVoucherCoEx",
                                SerieFactura : this.info.serieFel,
                                NumFactura: this.info.facturaFel,
                                nombreImpresora:'TMU'
                            }
                        }, {
                            responseType: 'arraybuffer'
                        })
                        .then(resp => {
                                this.$store.dispatch('reporte', {
                                    pdf: 'data:application/pdf;base64,' + Buffer.from(resp.data, 'binary').toString('base64'),
                                    abrirTab: true,
                                    imprimir: false,
                                    titulo: 'Impresion Fel',
                                })
                            this.Consulta().MostrarMensaje('Impresión factura finalizada.',this.sndMsg.succesMsg);
                            resolve(true)
                        })
                    })
                },

                ImprimeCupon: ()=>{
                    return new Promise ((resolve) =>{
                        this.axios.post("/app/reporte/ReporteGenerador", {
                        Nombre: "Impresion Fel",
                            Opciones: {
                                tiporeporte: this.info.tgtReporte,
                                nombrereporte: "ImprimeCupon",
                                SerieFactura : this.info.serieFel,
                                NumFactura: this.info.facturaFel,
                                nombreImpresora:'TMU'
                            }
                        }, {
                            responseType: 'arraybuffer'
                        })
                        .then(resp => {
                                this.$store.dispatch('reporte', {
                                    pdf: 'data:application/pdf;base64,' + Buffer.from(resp.data, 'binary').toString('base64'),
                                    abrirTab: true, 
                                    imprimir: false,
                                    titulo: 'Impresion Fel',
                                })
                            this.Consulta().MostrarMensaje('Impresión cupón finalizada.',this.sndMsg.succesMsg);
                            resolve(true);
                        })
                    });
                },
                EmisionFel: async()=>{
                        await this.axios.post('/app/v1_FacturaElectronica/GeneraFel',{
                                    serieFactura: this.info.serieFel,
                                    numeroFactura: this.info.facturaFel
                                }).then(resp=>{
                                            if(resp.data.codigo == 0 && resp.data.json[0].codigo == 0){
                                                this.Consulta().MostrarMensaje(resp.data.json[0].descripcion,this.sndMsg.succesMsg);
                                                this.ImpresionFactura(this.ControlCupon);
                                            }else{
                                                this.Consulta().MostrarMensaje(resp.data.json[0].descripcion, this.sndMsg.errorMsg);
                                            }
                                    return resp;
                                })
                },
                //genera factura sistema
                detalle: () => {
                    //quita el foco de facturar
                    let input = document.getElementById('MoneyCard');
                    input.focus();

                    //validaciones
                    if (!this.documento.tipoDocumento.Valor) {
                        this.Consulta().MostrarMensaje('Seleccione tipo de receptor', this.sndMsg.errorMsg);
                        return false
                    }

                    if (!this.documento.nombre) {
                        this.Consulta().MostrarMensaje('Ingrese nombre del receptor', this.sndMsg.errorMsg);
                        return false
                    }

                    if (!this.documento.valido) {
                        this.Consulta().MostrarMensaje('NIT/Doc. No válido', this.sndMsg.errorMsg);
                        return false
                    }

                    this.info.efectivo= this.ConvertirFloat(this.info.efectivo)
                    this.info.tarjeta = this.ConvertirFloat(this.info.tarjeta)

                    if (this.info.tarjeta > 0 && !this.info.autoriza) {
                        this.Consulta().MostrarMensaje('Ingrese número de autorización', this.sndMsg.errorMsg);
                        return false
                    }

                    if (this.totalFacturado <= 0){
                        this.Consulta().MostrarMensaje('Factura sin detalle. Verifique.', this.sndMsg.errorMsg);
                        return false
                    }

                    this.info.pagado = parseFloat(this.info.efectivo) + parseFloat(this.info.tarjeta)

                    if (this.info.pagado <= 0) {
                        this.Consulta().MostrarMensaje('Ingrese el monto a pagar', this.sndMsg.errorMsg);
                        return false
                    }

                    if (this.info.pagado < this.totalFacturado) {
                        this.Consulta().MostrarMensaje('Monto pagado no cubre el total de la factura', this.sndMsg.errorMsg);
                        return false
                    }

                    // if (!this.info.lotePrimas) {return}
                    
                    //preparacion de parametros
                    this.info.proceso = 19           //facturacion primas por lote

                    if (this.info.lotePrimas != true) {
                        this.info.proceso = 16       //facturacion CoEx

                        this.ChkSinPrecio();

                        if (this.info.sinPrecio > 0){
                            this.Consulta().MostrarMensaje('Existen productos sin precio. Verifique.', this.sndMsg.errorMsg);
                            return;
                        }
                    }

                    this.Consulta().MostrarMensaje('Facturando', this.sndMsg.succesMsg);

                    const detalleFac = this.detalleFac.map(m => {
                        return {
                            producto: m.Producto,
                            cantidad: m.Cantidad,
                            precioU: m.Precio,
                            numCupon: m.Cupon,
                            numCita: m.IdCita
                        }
                    })

                    this.info.NombreCli  = this.documento.nombre
                    this.info.nitCliente = this.documento.noDocumento
                    this.info.tipoDocSat = this.documento.tipoDocumento.Valor

                    //facturacion
                    this.axios.post('/app/v1_api_posFarmacia/FacturaCoEx', {
                        // ...this.info,
                        serieFactura:   this.info.serieFel,
                        tipoDoc:        this.documento.tipoDocumento.Valor,
                        nitCliente:     this.documento.noDocumento,
                        nomCliente:     this.documento.nombre,
                        efectivo:       this.info.efectivo,
                        tarjeta:        this.info.tarjeta,
                        posTarjeta:     this.info.tipoPos,
                        autorizacion:   this.info.autoriza,
                        fechaPrimas:    this.info.fechaPrimas,
                        tipoProceso:    this.info.proceso,
                        detalleFac
                    }).then(resp => {
                        if (resp.data.json[0].codigo != 0) {
                            this.Consulta().MostrarMensaje(resp.data.json[0].descripcion, this.sndMsg.errorMsg)
                            return false
                        }else{
                            this.info.facturaFel = resp.data.json[0].Factura
                            this.Consulta().MostrarMensaje('Sincronizando factura: '+this.info.serieFel+'-'+this.info.facturaFel,this.sndMsg.succesMsg)
                            this.Facturar().EmisionFel();
                            this.Consulta().telemedicina();
                        }
                    })
                }
            }
        },
        Interfaz() {
            return {
                agregaCitaCupon: (citasCoEx) => {
                    this.info.precioLibre = 0;

                    if (this.info.lotePrimas == true) {
                        this.Consulta().MostrarMensaje('Acción denegada, lote de primas en proceso.', this.sndMsg.errorMsg);
                        return;
                    }

                    this.ChkSinPrecio();

                    if (this.info.sinPrecio > 0){
                        this.Consulta().MostrarMensaje('Ingrese precio unitario.', this.sndMsg.errorMsg);
                        return;
                    }

                    const buscar = this.detalleFac.filter(listado => (listado.Cupon + listado.IdCita) == (citasCoEx.Cupon + citasCoEx.IdCita))
                    if (buscar.length == 0) {
                        this.detalleFac.unshift({
                            ...citasCoEx,
                            Cantidad: 1
                        })

                        if (!this.citasCoEx.Nit && this.totalFacturado == 0) {
                            this.AsignaNit(citasCoEx.Nit);
                        }
                    }else{
                        this.Consulta().MostrarMensaje('Cita/Cupón ya fue ingresado.', this.sndMsg.errorMsg);
                    }
                },
                agregaProducto: (productos) => {
                    this.info.precioLibre = 0;
                    if (this.info.lotePrimas) {
                        this.Consulta().MostrarMensaje('Acción denegada, lote de primas en proceso.', this.sndMsg.errorMsg);
                        return;
                    }

                    this.ChkSinPrecio();

                    if (this.info.sinPrecio > 0){
                        this.Consulta().MostrarMensaje('Ingrese precio unitario.', this.sndMsg.errorMsg);
                        return;
                    }

                    this.detalleFac.unshift({
                        ...productos,
                        Cupon: 0,
                        IdCita: 0,
                        Cantidad: 1,
                        NombreCliente: ''
                    
                    })
                }
            }
        },
        Otros() {
            return {
                limpiar: () => {

                    this.Consulta().telemedicina();
                    this.info.lotePrimas = false
                    this.info.efectivo = null
                    this.info.tarjeta = null
                    this.info.tipoPos = 'VIS'
                    this.info.autoriza = null
                    this.info.fechaPrimas = null
                    this.info.precioLibre = 0;

                    this.AsignaNit('C/F');

                    this.detalleFac = []
                    this.pagosCoEx = []
                }
            }
        }
    },
    mounted() {
        this.Consulta().init();

    },
    watch:{

        'configuracion.cajaSeleccionada'(valueant , value){

            if(!valueant && !value){
                return
            }

            if(valueant && !value){                
                this.Consulta().cargaProductos();
            }else if(!valueant && value){                
                this.Consulta().cargaProductos();
            }else if(valueant.SerieFac != value.SerieFac){                
                this.Consulta().cargaProductos();
            }
        }
    }
}
</script>

<style scoped>
.smtabla {
    /* height: 355px; */
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    border-radius: 5px;
    padding: 10px;
    margin-top: 10px;
    margin-bottom: 10px;
    overflow: hidden;

    /* background-color:rgba(0,143,190,0.9) */
}

.cantidad button {
    height: 25px;
    width: 30px;
    border: 1px solid #ccc;
    border-radius: 5px;
}

.cantidad div {
    display: inline-block;
    height: 25px;
    width: 30px;
    text-align: center;
    stop-color: red;
}
.bottompanel {
    min-height:30px;
    display: flexbox;
    color: white;
    position: relative;
    font-size: 17px;
    text-align: center;
    outline: none;
    border-bottom-left-radius:  10px;
    border-bottom-right-radius: 10px;
    background-color: rgba(252, 254, 255, 0.7);
    box-shadow: 0 1px 3px rgba(39, 39, 42, 0.875);
    cursor: pointer;
}
</style>
