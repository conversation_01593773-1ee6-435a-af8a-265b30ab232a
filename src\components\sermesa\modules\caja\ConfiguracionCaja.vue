<template>
    <div class="flex">
        <vs-popup :active.sync="popUpMonitorContingencias" title="" id="popUpContingencia" @keydown.esc="popUpMonitorContingencias=false">            
                <MONITOR_CONTINGENCIAS :serieFactura="configuracion?.cajaSeleccionada?.SerieFac" 
                                       :formatoImpresion="configuracion?.cajaSeleccionada?.FormatoImpresion" 
                                        ref="monitorContingenciaRef" class="w-full" @keydown.esc="popUpMonitorContingencias=false"></MONITOR_CONTINGENCIAS>
        </vs-popup>
        <vs-popup title="Configuración Caja" :active.sync="configuracion.popUpConfiguracion" class="seleccionEmpresa">
            <vs-row>
                <vs-col class="empresa w-1/2 p-5">
                    <div class="seleccion_empresa" v-for="(l,index) in caja.tiposCajas" :key="index" 
                            :class="[(configuracion.tipoCajaMostrar?.Valor==l.Valor)?'activo':'']" 
                            v-on:click="cambiar_tipo_cajero(l)">
                        {{l.Descripcion}}
                    </div>
                </vs-col>
                <vs-col class="sucursal w-1/2 p-5">
                    <div v-if="configuracion.cajasFiltradasPorTipo.length>0">
                        <h5>Seleccione la caja a utilizar</h5><br>
                        <div class="seleccion_empresa" :class="[(configuracion.cajaSeleccionada?.SerieFac == l?.SerieFac)?'activo':'']" 
                             v-for="(l,index) in configuracion.cajasFiltradasPorTipo" :key="index" v-on:click="grabarCajaOpcion(l)">
                            {{' No. Caja: '+l?.CajaLocal+' Serie Factura: '+l?.SerieFac+' Serie Recibo: '+l?.SerieRecibo}}
                        </div>
                    </div>
                </vs-col>
            </vs-row>
        </vs-popup>  
        <details style="margin-top: -10px;" class="w-full pb-2" close>
            <summary :style="{'margin-bottom':'.5%','color':configuracion?.cajaSeleccionada?.CajaLocal?'#008FBE':'red'}">{{(MostrarMonitor?'Monitor de contingencias / Configuración de caja: ':'Configuración de caja: ')+(configuracion?.cajaSeleccionada?.CajaLocal? configuracion?.cajaSeleccionada?.CajaLocal:'') }}</summary>
            <vs-row class="w-full mb-4" vs-justify="center" id="filaConfiguracionCaja">
                <vs-col class="sm:w-full md:w-3/12 lg:w-3/12 xl:w-3/12 md:pl-2 sm:pl-2 xs:pl-2">
                    <vs-button color="primary"  v-if="MostrarMonitor"
                                @click="abrirMonitorContingencia" @keyup.esc="popUpMonitorContingencias=false">
                        Monitor Contingencias
                    </vs-button>
                </vs-col>
                <vs-col class="sm:w-full md:w-9/12 lg:w-9/12 xl:w-5/12 pl-2 " style="text-align:center">
                    <div class="w-full panel-cajero" @click="(configuracion.popUpConfiguracion = true)"                   
                        v-if="configuracion.tipoCajaSeleccionada && configuracion.cajaSeleccionada"
                        >
                        <div v-if="TipoDocumento !='N'">
                            {{configuracion.tipoCajaSeleccionada?.Descripcion?.toUpperCase()??''}}
                            {{' / No. Caja: '+configuracion.cajaSeleccionada.CajaLocal+' - Serie Factura: '+configuracion.cajaSeleccionada.SerieFac+' - Serie Recibo: '+configuracion.cajaSeleccionada.SerieRecibo}}                        
                        </div>
                        <div class="w-full panel-cajero" v-else>
                            {{configuracion.tipoCajaSeleccionada?.Descripcion?.toUpperCase()??''}}
                            {{' / Serie Nota: '+configuracion.cajaSeleccionada.SerieFac}}
                        </div>                        
                    </div>                    
                    <div class="w-full panel-cajero panel-cajero-elegir" @click="(configuracion.popUpConfiguracion = true)" 
                        v-if="!configuracion.tipoCajaSeleccionada || !configuracion.cajaSeleccionada"  
                        >
                        {{'Seleccione Tipo Caja ...'}}
                    </div>
                </vs-col>
                <vs-col class="sm:w-full md:w-3/12 lg:w-3/12 xl:w-4/12 md:pl-2 sm:pl-2 xs:pl-2">
                   <slot></slot>
                </vs-col>
            </vs-row>
        </details>
    </div>
</template>
<script>
import MONITOR_CONTINGENCIAS from "/src/views/pages/CAJERO/HSP011.vue"
export default {    
    components:{
        MONITOR_CONTINGENCIAS
    },
    name: 'configuracion-caja',
    props: {
      MostrarMonitor:{
          type: Boolean,
          default: true
      },
      //El nombre de la agrupacion para obtener los tipos de caja en la tabla de agrupaciones  
      AgrupacionCaja: {
        type: String,
        default: 'TipoCajaRecibo'
      },
      //Permite filtrar la lista de cajas obtenidas en la tabla agrupaciones
      FiltrarAgrupacionCaja: {
        type: Array,
        default(){
                return []
        } 
      },
      //Nombre de la variable local a grabar en el navegador para obtener el tipo de caja (CAJA CENTRAL, CAJA DIAGNOSTICO, ETC....)
      TipoCajaSeleccionada: {
        type: String,
        default: 'TipoCajaSeleccionada'
      },
      //Nombre de la variable local a guardar los datos de la caja (No. Serie, No. Caja, No. Recibo)
      CajaSeleccionada: {
        type: String,
        default: 'CajaSeleccionada'
      },
      TipoDocumento: {
        type: String,
        default: 'F'
      },
      FacturaCambiaria: {
        type: Number,
        default: 1
      }
    },
     data() {    
        return {   
            popUpMonitorContingencias:false,
            configuracion:{
                    popUpConfiguracion: false,

                    tiposCajas: [],
                    tipoCajaMostrar: null,
                    tipoCajaSeleccionada: null,
                    indexTipoCajaSeleccionada: null,

                    cajaSeleccionada: null,
                    cajasFiltradasPorTipo: [],
                    codigosPermisosCajas: []                        
                },
            caja: { serieFactura:[],
                    serieFacturaFiltradaPorHospital:[]
                },
        }
     },
     methods:{
        ValidarVariable(entrada){   
            if(typeof(entrada) == 'number'){
                return entrada ? true : false;
            }else if(typeof(entrada) == 'string'){
                return entrada && entrada.length > 0 ? true : false;
            }else if(typeof(entrada) == 'object'){
                return entrada ? true : false;
            }            
            return false;
        },
        abrirMonitorContingencia(){
            if(!this.configuracion.cajaSeleccionada){
                this.Consulta().MostrarError('Seleccione un caja valida.')
                return
            }
            
            this.popUpMonitorContingencias = true;
            this.$refs.monitorContingenciaRef.CargarFacturasEnContingencias()
        },
        async CargarAgrupaciones(agrupacion, subOpcion=2){
            await this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision', {
                                opcion:'C',
                                subOpcion:subOpcion,
                                agrupacion
                            })
                        .then(resp => {                            
                                if (resp.data && resp.data.json.length > 0){
                                    if(this.FiltrarAgrupacionCaja.length>0){
                                        this.caja.tiposCajas = resp.data.json.filter(tipoCaja => this.FiltrarAgrupacionCaja.includes(tipoCaja.Valor))
                                    }else{
                                        this.caja.tiposCajas = resp.data.json
                                    }
                                }
                            }
                        )
        },  
        cambiar_tipo_cajero(tipoCaja){                
            this.configuracion.tipoCajaMostrar = tipoCaja;     

            this.configuracion.cajasFiltradasPorTipo = 
                this.caja.serieFacturaFiltradaPorHospital.filter( caja => caja.TipoCaja == tipoCaja.Valor );

            if(this.configuracion.cajasFiltradasPorTipo.length == 0){
                let descripcion = this.configuracion.tipoCajaMostrar.Descripcion;
                this.Consulta().MostrarError('No cuenta con acceso a cajas de tipo '+descripcion,5000);
            }
        },
        grabarCajaOpcion(caja){            
            this.configuracion.tipoCajaMostrar = this.caja.tiposCajas.find(tipoCaja => tipoCaja.Valor==caja.TipoCaja)
            this.grabarTipoCajero();    
            this.grabarCaja(caja)
            this.configuracion.popUpConfiguracion = false;
        },
        grabarTipoCajero(){
            this.configuracion.tipoCajaSeleccionada = this.configuracion.tipoCajaMostrar
            localStorage.setItem(this.TipoCajaSeleccionada, JSON.stringify({...this.configuracion.tipoCajaSeleccionada}));            
        },   
        enviarCaja(caja){
            if(caja){
                this.configuracion.indexTipoCajaSeleccionada = caja.TipoCaja;  
                caja.FormatoImpresion = !caja.FormatoFel ? 'ImprimeFelLtr' : caja.FormatoFel == 0 ? 'ImprimeFelTmu'   
                                        : caja.FormatoFel == 1 ? 'ImprimeFelEFX'   : caja.FormatoFel == 2 ? 'ImprimeFelLtr'   
                                        : 'ImprimeFelLtr'
            }

            this.configuracion.cajaSeleccionada = caja;            

            let EstadoFactura = this.ValidarVariable(this.configuracion.cajaSeleccionada?.SerieFac)
            let EstadoRecibo = this.ValidarVariable(this.configuracion.cajaSeleccionada?.SerieRecibo)   

            this.$emit('CargarCaja',
                        {'Cajero' :this.configuracion.cajaSeleccionada,
                         'Factura':EstadoFactura,
                         'Recibo' :EstadoRecibo})            
        },      
        grabarCaja(caja){
            if(caja){
                this.configuracion.indexTipoCajaSeleccionada = caja.TipoCaja;  
                caja.FormatoImpresion = !caja.FormatoFel ? 'ImprimeFelLtr' : caja.FormatoFel == 0 ? 'ImprimeFelTmu'   
                                        : caja.FormatoFel == 1 ? 'ImprimeFelEFX'   : caja.FormatoFel == 2 ? 'ImprimeFelLtr'   
                                        : 'ImprimeFelLtr'
            }
            this.configuracion.cajaSeleccionada = caja;            
            localStorage.setItem(this.CajaSeleccionada, JSON.stringify(this.configuracion.cajaSeleccionada));

            let EstadoFactura = this.ValidarVariable(this.configuracion.cajaSeleccionada?.SerieFac)
            let EstadoRecibo = this.ValidarVariable(this.configuracion.cajaSeleccionada?.SerieRecibo)
            
            this.$emit('CargarCaja',
                        {'Cajero' :this.configuracion.cajaSeleccionada,
                         'Factura':EstadoFactura,
                         'Recibo' :EstadoRecibo})               
        },
        CargarPermisosCaja(){
            this.configuracion.codigosPermisosCajas = []

            for(let privilegio of this.$store.state.privilegios){
                if(privilegio.Privilegio.includes("CAJA_")){
                    let caja = privilegio.Privilegio.split('_')[1]
                    this.configuracion.codigosPermisosCajas.push(caja)                    
                }
            }     
        },
        async CargarPermisosCajaCentral(){
            let Funcionalidad = this.$store.state.funcionalidades.find(aplicativo => aplicativo.Funcionalidad == "Permisos Cajero")
            if(!Funcionalidad){
                this.CargarPermisosCaja()
                return
            }

            this.configuracion.codigosPermisosCajas = []

            await this.axios.post('/app/usuario/obtener_privilegios',
                                    {   
                                        Modulo:Funcionalidad.IdFuncionalidad
                                    })
                            .then(resp=>{
                                        for(let privilegio of resp.data.json){
                                            if(privilegio.Privilegio.includes("CAJA_")){
                                                let caja = privilegio.Privilegio.split('_')[1]
                                                this.configuracion.codigosPermisosCajas.push(caja)                    
                                            }
                                        }     
                                    })
           
        },
        async CargarSeriesFaturas(tipoDoc,cambiaria){   
            if(tipoDoc == "F" && this.configuracion.codigosPermisosCajas.length == 0){
                this.Consulta().MostrarError('Solicitar el permiso para utilizar una caja');
                return 0;
            }

            await this.axios.post('/app/v1_caja/SeriesFacturas', {
                                tipoDocFel : tipoDoc,
                                cambiaria: cambiaria
                            })
                            .then(resp => {                                  
                                if (resp.data && resp.data.json.length > 0){
                                    if(tipoDoc == "F" || tipoDoc == "L" || tipoDoc == "N"){
                                        this.caja.serieFactura = resp.data.json
                                        this.caja.serieFacturaFiltradaPorHospital = []
                                        this.caja.serieFacturaFiltradaPorHospital =
                                            this.caja.serieFactura.filter( cajero => this.configuracion.codigosPermisosCajas.includes(cajero.SerieFac) )  
                                        
                                        if( this.caja.serieFacturaFiltradaPorHospital.length <=0 ){
                                            this.Consulta().MostrarError('Solicitar el permiso para utilizar una caja');
                                            return 0
                                        }else{
                                            return 1
                                        }                 
                                    }else if(tipoDoc == "R"){
                                        this.caja.serieRecibo = resp.data.json
                                    }
                                }else{
                                    this.Consulta().MostrarError('No hay ninguna caja configurada para esta sucursal');
                                    return 0;
                                }
                            })
        },
       CargarCajaInicial(){
            this.configuracion.tipoCajaSeleccionada = JSON.parse(localStorage.getItem(this.TipoCajaSeleccionada));   
            this.configuracion.tipoCajaMostrar = this.configuracion.tipoCajaSeleccionada
            this.configuracion.cajaSeleccionada = JSON.parse(localStorage.getItem(this.CajaSeleccionada));  

            if(!this.ValidarVariable(this.configuracion.tipoCajaSeleccionada) || !this.ValidarVariable(this.caja.tiposCajas) || !this.ValidarVariable(this.configuracion.cajaSeleccionada)){
                this.Consulta().MostrarError('Seleccione una caja para iniciar la facturación');
                this.grabarCaja(null);
                return 
            }

            let indexTipoCaja = 
                this.caja.tiposCajas.findIndex( tipoCaja => tipoCaja.Valor == this.configuracion.tipoCajaSeleccionada.Valor)
            if(indexTipoCaja < 0){
                this.Consulta().MostrarError('Este modulo no puede trabajar con cajeros de tipo '+ this.configuracion.tipoCajaSeleccionada.Descripcion);
                this.enviarCaja(null);
                return 
            }

            if(this.configuracion.cajaSeleccionada.TipoCaja != this.configuracion.tipoCajaSeleccionada.Valor){
                this.Consulta().MostrarError('Seleccionar una caja para poder iniciar la facturación',5000);
                this.grabarCaja(null);
                return
            }

            this.cambiar_tipo_cajero(this.configuracion.tipoCajaSeleccionada)
            
            let indiceCaja = this.caja.serieFacturaFiltradaPorHospital.findIndex( cajero => cajero.SerieFac == this.configuracion.cajaSeleccionada.SerieFac );
            if(indiceCaja >= 0){                
                let cajero = this.caja.serieFacturaFiltradaPorHospital[indiceCaja];
                this.grabarTipoCajero();
                if(this.configuracion.cajaSeleccionada.CajaLocal == cajero.CajaLocal && this.configuracion.cajaSeleccionada.SerieRecibo == cajero.SerieRecibo){
                    this.grabarCaja(this.configuracion.cajaSeleccionada);                        
                }else{
                    this.configuracion.cajaSeleccionada = cajero
                    this.grabarCaja(this.configuracion.cajaSeleccionada);
                }
                return
            }
            
            let cajasTipoSeleccionadas = 
                this.caja.serieFacturaFiltradaPorHospital.filter( caja => caja.TipoCaja == this.configuracion.tipoCajaSeleccionada.Valor )                                                

            if(cajasTipoSeleccionadas.length == 0){                         
                this.Consulta().MostrarError('No cuenta con permisos en este hospital para utilizar '+this.configuracion.tipoCajaMostrar.Descripcion);                                        
                this.grabarCaja(null);
                return
            }  

            if(cajasTipoSeleccionadas.length == 1){                         
                this.configuracion.cajaSeleccionada = cajasTipoSeleccionadas[0]
                this.grabarTipoCajero();
                this.grabarCaja(this.configuracion.cajaSeleccionada);
                return
            } 
            
            if(cajasTipoSeleccionadas.length > 1){                                                                              
                this.grabarCaja(null);
                this.Consulta().MostrarError('Cuenta con mas de una '+this.configuracion.tipoCajaSeleccionada.Descripcion+' seleccione manualmente para iniciar.',5000);
                return
            } 
        },    
        Consulta(){
            return {
                init: async () => {
                    await this.CargarPermisosCajaCentral();
                    await this.CargarAgrupaciones(this.AgrupacionCaja);
                    let estado = await this.CargarSeriesFaturas(this.TipoDocumento,this.FacturaCambiaria);
                    if(estado == 0) return;
                    this.CargarCajaInicial();
                },
                MostrarError:(mensaje,tiempo=4000)=>{
                    this.$vs.notify({
                        time:tiempo,
                        title: 'Cajero',
                        color: 'danger',
                        text:mensaje,
                        position: 'top-center'
                    })
                },
                MostrarSatisfactorio:(mensaje)=>{
                    this.$vs.notify({
                        time:2000,
                        title: 'Cajero',
                        color: 'success',
                        text:mensaje,
                        position: 'top-center'
                    })
                }
            }
        }
     },
     mounted() {
        this.Consulta().init()   
     }
}
</script>
<style>
 #filaConfiguracionCaja{
    justify-content:left !important;
}

#popUpContingencia > .vs-popup { 
    width:100% !important;
    height:100% !important;
    max-width: 100 !important; 
}

.panel-cajero-elegir{
    background-color: rgba(var(--vs-danger), 1) !important; 
}

.panel-cajero {
    display:flex;
    vertical-align: middle;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    min-height:40px;
    background-color: rgba(var(--vs-primary), 1);
    color: white;    
    position: relative;
    width: auto;
    /* top: 12px; */
    overflow: hidden;
    /* margin-right: -14px; */
    font-size: 13px;
    /* right: 50px; */
    /* border-radius: 30px 0 0 0; */
    /* box-shadow: inset 0 2px 0px 1px rgba(0, 0, 0, 0.2); */
    cursor: pointer;
}

.seleccionTipoCaja .vs-popup {
    width: 250px !important;
}

.label-shrink{
    display: block;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    width: 100%;
}

.labelMuestraConfiguracion{
    min-height:50px;
}
</style>