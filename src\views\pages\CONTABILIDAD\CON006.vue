<template>
<vx-card title="Contabilidad General">
    <div class="content content-pagex">
        <form>
            <div class="flex flex-wrap mb-1">
                <div class="w-full p-1">
                    <vs-alert color="danger" v-for="(item, key) in CuentasNValidas" :key="key" style="text-align:center;height:40px">
                        {{ item.Nombre }} - {{ item.Cuenta }}
                    </vs-alert>
                </div>
            </div>
            <vs-divider class="label-size">MANTENIMIENTO DE CUENTAS</vs-divider>
            <div class="btn-group">
                <vs-button class="label-size" :type="(Opcion==1)?'filled':'line'" style="display:inline;height:50px" color="primary" icon-pack="feather" @click="Opcion=1" icon="icon-layers">Cuentas Bodegas</vs-button>
                <vs-button class="label-size" :type="(Opcion==2)?'filled':'line'" style="display:inline;height:50px" color="primary" icon-pack="feather" @click="Opcion=2" icon="icon-layers">Cuentas Departamento</vs-button>
                <vs-button class="label-size" :type="(Opcion==3)?'filled':'line'" style="display:inline;height:50px" color="primary" icon-pack="feather" @click="Opcion=3" icon="icon-layers">Cuentas Categoría Costos</vs-button>
                <vs-button class="label-size" :type="(Opcion==4)?'filled':'line'" style="display:inline;height:50px" color="primary" icon-pack="feather" @click="Opcion=4" icon="icon-layers">Cuentas Categoría Ventas</vs-button>
                <vs-button class="label-size" :type="(Opcion==5)?'filled':'line'" style="display:inline;height:50px" color="primary" icon-pack="feather" @click="Opcion=5" icon="icon-layers">Cuentas Anticipo</vs-button>
            </div>
            <!-------------------------CUENTAS----------------- -->
            <div v-show="Opcion==1">
                <div style="box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.2);padding:10px;border-radius:5px; border:1px solid #ccc;">
                    <div class="flex flex-wrap">
                        <div class="md:w-full xl:w-6/12 xl:w-6/12" style="float:left;padding:20px 50px">
                            <ValidationProvider name="Bodegas">
                                <label class="label-size"> Bodega</label>
                                <multiselect v-model="cbBodega" :options="ListaBodega" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="BodegaSeleccionada" placeholder="Búsqueda de Bodega" @input="onChangeBodega" :disabled="BloqueoCodigo==true">
                                    <span slot="noOptions">Lista no disponible.</span>
                                </multiselect>
                            </ValidationProvider>
                        </div>
                    </div>
                    <br>
                    <div class="flex flex-wrap">
                        <div class="w-full">
                            <div class="md:w-6/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:20px 50px">
                                <ValidationProvider name="CuentaLocal" rules="max:12" v-slot="{ errors }">
                                    <label class="label-size"> Cuenta Local </label>
                                    <vs-input type="text" class="w-full" v-model="cbBodega.CuentaLocal" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="BloqueoB==true" />
                                </ValidationProvider>
                            </div>
                            <div class="md:w-6/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:20px 50px">
                                <ValidationProvider name="CuentaEnTransito" rules="max:12" v-slot="{ errors }">
                                    <label class="label-size"> Cuenta En Tránsito </label>
                                    <vs-input type="text" class="w-full" v-model="cbBodega.CuentaEnTransito" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="BloqueoB==true" />
                                </ValidationProvider>
                            </div>
                            <div class="md:w-6/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:20px 50px">
                                <ValidationProvider name="CuentaDespachoPaciente" rules="max:12" v-slot="{ errors }">
                                    <label class="label-size"> Cuenta Vena Paciente </label>
                                    <vs-input type="text" class="w-full" v-model="cbBodega.CuentaDespachoPaciente" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="BloqueoB==true" />
                                </ValidationProvider>
                            </div>
                        </div>
                    </div>
                    <br>
                    <div class="flex flex-wrap">
                        <div class="w-full">
                            <div class="md:w-full xl:w-6/12 xl:w-6/12" style="float:left;padding:20px 50px">
                                <ValidationProvider name="Hospital">
                                    <label class="label-size"> Siglas Hospital</label>
                                    <multiselect v-model="cbHospital" :options="ListaHospital" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="HospitalSeleccionado" placeholder="Búsqueda Hospital" @input="onChangeHospital" :disabled="BloqueoB==true">
                                        <span slot="noOptions">Lista no disponible.</span>
                                    </multiselect>
                                </ValidationProvider>
                            </div>
                            <div class="md:w-full xl:w-6/12 xl:w-6/12" style="float:left;padding:20px 50px">
                                <ValidationProvider name="EmpresaRealx">
                                    <label class="label-size">Empresa Real</label>
                                    <multiselect v-model="cbBusquedaEmpresa" :options="ListaEmpresas" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="EmpresaSeleccionada" placeholder="Búsqueda empresa" @input="onChangeEmpresa" :disabled="BloqueoB==true">
                                        <span slot="noOptions">Lista no disponible.</span>
                                    </multiselect>
                                </ValidationProvider>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-wrap">
                        <div class="w-full">
                            <div style="float:right;padding:20px 50px">
                                <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" @click="Validar()" id="btn_guardar" :disabled="BloqueoBB"> Guardar</vs-button>
                                <vs-button color="success" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-edit-1" @click="EditarCuentas()" id="btn_editar"> Editar</vs-button>
                                <vs-button color="danger" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-x" @click="Cancelar()" id="btn_cancelar" :disabled="BloqueoB"> Cancelar</vs-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--------------------------CUENTAS DEPARTAMENTO-------------------->
            <div v-show="Opcion==2">
                <div style="box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.2);padding:10px;border-radius:5px; border:1px solid #ccc;">
                    <div class="flex flex-wrap">
                        <div class="md:w-full xl:w-6/12 xl:w-6/12" style="float:left;padding:20px 50px">
                            <ValidationProvider name="Departamento" v-slot="{ errors }">
                                <label class="label-size">Departamento</label>
                                <multiselect v-model="cbDepartamentos" :options="ListaDepartamentos" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="DepartamentoSeleccionado" placeholder="Seleccionar" @input="onChangedepartamento" :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null" :disabled="BloqueoCodigoD==true">
                                    <span slot="noOptions">Lista no disponible.</span>
                                </multiselect>
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class="flex flex-wrap">
                        <div class="w-full">
                            <div class="md:w-6/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:20px 50px">
                                <ValidationProvider name="CuentaEnTransito" rules="max:12" v-slot="{ errors }">
                                    <label class="label-size"> Cuenta En Tránsito </label>
                                    <vs-input type="text" class="w-full" v-model="cbDepartamentos.CuentaEnTransito" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="BloqueoD==true" />
                                </ValidationProvider>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-wrap">
                        <div class="w-full">
                            <div class="md:w-full xl:w-6/12 xl:w-6/12" style="float:left;padding:20px 50px">
                                <ValidationProvider name="Hospital">
                                    <label class="label-size"> Siglas Hospital</label>
                                    <multiselect v-model="cbHospitalD" :options="ListaHospital" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="HospitalSeleccionadoD" placeholder="Búsqueda Hospital" @input="onChangeHospitalD" :disabled="BloqueoD==true">
                                        <span slot="noOptions">Lista no disponible.</span>
                                    </multiselect>
                                </ValidationProvider>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-wrap">
                        <div class="w-full">
                            <div style="float:right;padding:20px 50px">
                                <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" @click="ValidarD()" id="btn_guardarD" :disabled="BloqueoBD"> Guardar</vs-button>
                                <vs-button color="success" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-edit-1" @click="EditarCuentasD()" id="btn_EditarD"> Editar</vs-button>
                                <vs-button color="danger" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-x" @click="CancelarD()" id="btn_cancelarD" :disabled="BloqueoD"> Cancelar</vs-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!---------------------------CUENTA CATEGORÍA COSTOS--------------------->
            <div v-show="Opcion==3">
                <div style="box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.2);padding:10px;border-radius:5px; border:1px solid #ccc;">
                    <div class="w-full">
                        <div class="flex flex-wrap">
                            <div class="w-12/12 lg:w-12/12 xl:w-6/12">
                                <vs-table2 max-items="10" tooltip pagination :data="ListaCategoriaC" search id="tb_catcostos">
                                    <template slot="thead">
                                        <th width="50">Hospital</th>
                                        <th width="30">Tipo Bodega</th>
                                        <th width="100">Bodega</th>
                                        <th width="30">Categoría</th>
                                        <th width="400">Descripción Categoría</th>
                                        <th width="20">Cuentas Costo</th>
                                        <th width="20">Diversos</th>
                                    </template>
                                    <template slot-scope="{data}">
                                        <tr :key="indextr" v-for="(tr, indextr) in data">
                                            <vs-td2 noTooltip :data="data[indextr].Hospital" align="center">
                                                {{data[indextr].Hospital}}
                                            </vs-td2>
                                            <vs-td2 noTooltip :data="data[indextr].TipoBodega">
                                                {{data[indextr].TipoBodega}}
                                            </vs-td2>
                                            <vs-td2 :data="data[indextr].Bodega">
                                                {{data[indextr].Bodega}}
                                            </vs-td2>
                                            <vs-td2 noTooltip :data="data[indextr].Categoria">
                                                {{data[indextr].Categoria}}
                                            </vs-td2>
                                            <vs-td2 :data="data[indextr].NombreCat">
                                                {{data[indextr].NombreCat}}
                                            </vs-td2>
                                            <vs-td2 noTooltip :data="data[indextr].CuentasCostoSuministro">
                                                {{data[indextr].CuentasCostoSuministro}}
                                            </vs-td2>
                                            <vs-td2 noTooltip :data="data[indextr].Diversos">
                                                {{data[indextr].Diversos}}
                                            </vs-td2>
                                            <vs-td2 noTooltip align="center">
                                                <vs-button color="success" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" @click="EditarCuentasCC(tr)"></vs-button>
                                            </vs-td2>
                                            <vs-td2 noTooltip align="center">
                                                <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="EliminarCuentasCC(tr)"></vs-button>
                                            </vs-td2>
                                        </tr>
                                    </template>
                                </vs-table2>
                            </div>
                            <br>
                            <div class="w-12/12 lg:w-12/12 xl:w-6/12" style="float:left;padding:100px 100px">
                                <div class="w-full">
                                    <vs-button color="primary" style="float:left;margin: 5px" type="filled" icon-pack="fas" icon="fa-plus" @click="NuevaCuentaCC()"> Nueva</vs-button>
                                    <br>
                                    <vs-divider class="label-size">Categoría Costos</vs-divider>
                                    <div class="flex flex-wrap">
                                        <div class="w-full">
                                            <div style="padding:10px 20px">
                                                <ValidationProvider name="Categoría Costos" v-show="Nueva==false">
                                                    <label class="label-size"> Categoría Costos</label>
                                                    <multiselect v-model="cbCategoriaC" :options="ListaCategoriaC" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="CategoriaSeleccionadaC" placeholder="Búsqueda de Categoría" @input="onChangeCategoriaC" :disabled="BloqueoCodigoCC==true">
                                                        <span slot="noOptions">Lista no disponible.</span>
                                                    </multiselect>
                                                </ValidationProvider>
                                            </div>
                                            <div style="padding:10px 20px" v-show="Nueva==true">
                                                <ValidationProvider name="Categorías">
                                                    <label class="label-size"> Categorías</label>
                                                    <multiselect v-model="cbCategoria" :options="ListaCategoria" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="CategoriaSeleccionada" placeholder="Búsqueda de Categoría" @input="onChangeCategoria">
                                                        <span slot="noOptions">Lista no disponible.</span>
                                                    </multiselect>
                                                </ValidationProvider>
                                            </div>
                                            <div style="padding:10px 20px">
                                                <ValidationProvider name="Hospital">
                                                    <label class="label-size"> Hospital</label>
                                                    <multiselect v-model="cbHospitalCC" :options="ListaHospital" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="HospitalSeleccionadoCC" placeholder="Búsqueda Hospital" @input="onChangeHospitalCC" :disabled="BloqueoHospital==true">
                                                        <span slot="noOptions">Lista no disponible.</span>
                                                    </multiselect>
                                                </ValidationProvider>
                                            </div>
                                            <div style="padding:20px 20px">
                                                <vs-divider class="label-size">Tipo Bodega</vs-divider>
                                                <div class=" md:w-5/5 lg:w-1/5 xl:w-1/5, label-size" style="float:left;margin: 20px" align="right">
                                                    <vs-radio v-model="cbCategoriaC.TipoBodega" vs-name="TipoBod" vs-value=0 :disabled="BloqueoCodigoCC==true"> No Aplica</vs-radio>
                                                </div>
                                                <div class=" md:w-5/5 lg:w-1/5 xl:w-1/5, label-size" style="float:left;margin: 20px" align="center">
                                                    <vs-radio v-model="cbCategoriaC.TipoBodega" vs-name="TipoBod" vs-value=1 :disabled="BloqueoCodigoCC==true"> Cocina</vs-radio>
                                                </div>
                                                <div class=" md:w-5/5 lg:w-1/5 xl:w-1/5, label-size" style="float:left;margin: 20px" align="left">
                                                    <vs-radio v-model="cbCategoriaC.TipoBodega" vs-name="TipoBod" vs-value=2 :disabled="BloqueoCodigoCC==true"> Cafetería</vs-radio>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex flex-wrap">
                                        <div class="w-full">
                                            <div class="md:5/12 lg:5/12 xl:w-5/12" style="float:left;padding:10px 20px">
                                                <ValidationProvider name="CuentaLocal" rules="max:12|required" v-slot="{ errors }">
                                                    <label class="label-size"> Cuenta Costo </label>
                                                    <vs-input type="text" class="w-full" v-model="cbCategoriaC.CuentasCostoSuministro" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="BloqueoCC==true" />
                                                </ValidationProvider>
                                            </div>
                                            <div class="md:5/12 lg:5/12 xl:w-5/12" style="float:left;padding:10px 20px">
                                                <ValidationProvider name="CuentaEnTransito" rules="max:12" v-slot="{ errors }">
                                                    <label class="label-size"> Diversos </label>
                                                    <vs-input type="text" class="w-full" v-model="cbCategoriaC.Diversos" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="BloqueoCC==true" />
                                                </ValidationProvider>
                                            </div>
                                        </div>
                                    </div>

                                    <div style="float:right;padding:20px 50px">
                                        <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" @click="ValidarCC()" id="btn_GuardarCC" :disabled="BloqueoBCC"> Guardar</vs-button>
                                        <vs-button color="danger" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-x" @click="CancelarCC()" id="btn_CancelarCC" :disabled="BloqueoCC"> Cancelar</vs-button>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- -----------------------CUENTAS CATEGORÍA VENTAS--------------------->
            <div v-show="Opcion==4">
                <div style="box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.2);padding:10px;border-radius:5px; border:1px solid #ccc;">
                    <div class="w-full">
                        <div class="flex flex-wrap">
                            <div class="w-12/12 lg:w-12/12 xl:w-6/12">
                                <vs-table2 max-items="10" tooltip pagination :data="ListaCategoriaCV" search id="tb_catcostos">
                                    <template slot="thead">
                                        <th width="50">Hospital</th>
                                        <th width="30">Categoría</th>
                                        <th width="400">Descripción Categoría</th>
                                        <th width="20">Cuentas Ventas</th>
                                        <th width="20">Cuenta Descuento</th>
                                        <th width="20">Empresa</th>
                                    </template>
                                    <template slot-scope="{data}">
                                        <tr :key="indextr" v-for="(tr, indextr) in data">
                                            <vs-td2 noTooltip :data="data[indextr].Hospital" align="center">
                                                {{data[indextr].Hospital}}
                                            </vs-td2>
                                            <vs-td2 noTooltip :data="data[indextr].Categoria">
                                                {{data[indextr].Categoria}}
                                            </vs-td2>
                                            <vs-td2 :data="data[indextr].NombreCat">
                                                {{data[indextr].NombreCat}}
                                            </vs-td2>
                                            <vs-td2 noTooltip :data="data[indextr].CuentaVentas">
                                                {{data[indextr].CuentaVentas}}
                                            </vs-td2>
                                            <vs-td2 noTooltip :data="data[indextr].CuentaDescuento">
                                                {{data[indextr].CuentaDescuento}}
                                            </vs-td2>
                                            <vs-td2 noTooltip :data="data[indextr].Empresa">
                                                {{data[indextr].Empresa}}
                                            </vs-td2>
                                            <vs-td2 noTooltip align="center">
                                                <vs-button color="success" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" @click="EditarCuentasCV(tr)"></vs-button>
                                            </vs-td2>
                                            <vs-td2 noTooltip align="center">
                                                <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="EliminarCuentasCV(tr)"></vs-button>
                                            </vs-td2>
                                        </tr>
                                    </template>
                                </vs-table2>
                            </div>
                            <br>
                            <div class="w-12/12 lg:w-12/12 xl:w-6/12" style="float:left;padding:100px 100px">
                                <div class="w-full">
                                    <vs-button color="primary" style="float:left;margin: 5px" type="filled" icon-pack="fas" icon="fa-plus" @click="NuevaCuentaCV()"> Nueva</vs-button>
                                    <br>
                                    <vs-divider class="label-size">Categoría Ventas</vs-divider>
                                    <div class="flex flex-wrap">
                                        <div class="w-full">
                                            <div style="padding:10px 20px">
                                                <ValidationProvider name="Categoría" v-show="Nueva==false">
                                                    <label class="label-size"> Categoría Ventas</label>
                                                    <multiselect v-model="cbCategoriaCV" :options="ListaCategoriaCV" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="CategoriaSeleccionadaCV" placeholder="Búsqueda de Categoría" @input="onChangeCategoriaCV" :disabled="BloqueoCodigoCV==true">
                                                        <span slot="noOptions">Lista no disponible.</span>
                                                    </multiselect>
                                                </ValidationProvider>
                                            </div>
                                            <div style="padding:10px 20px" v-show="Nueva==true">
                                                <ValidationProvider name="Categorías">
                                                    <label class="label-size"> Categorías</label>
                                                    <multiselect v-model="cbCategoria" :options="ListaCategoria" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="CategoriaSeleccionada" placeholder="Búsqueda de Categoría" @input="onChangeCategoria">
                                                        <span slot="noOptions">Lista no disponible.</span>
                                                    </multiselect>
                                                </ValidationProvider>
                                            </div>
                                            <div style="padding:10px 20px">
                                                <ValidationProvider name="Hospital">
                                                    <label class="label-size"> Hospital</label>
                                                    <multiselect v-model="cbHospitalCV" :options="ListaHospital" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="HospitalSeleccionadoCV" placeholder="Búsqueda Hospital" @input="onChangeHospitalCV" :disabled="BloqueoHospital==true">
                                                        <span slot="noOptions">Lista no disponible.</span>
                                                    </multiselect>
                                                </ValidationProvider>
                                            </div>
                                            <div class="flex flex-wrap">
                                                <div class="w-full">
                                                    <div class="md:5/12 lg:5/12 xl:w-5/12" style="float:left;padding:10px 20px">
                                                        <ValidationProvider name="CuentaVentas" rules="max:12" v-slot="{ errors }">
                                                            <label class="label-size"> Cuenta Ventas </label>
                                                            <vs-input type="text" class="w-full" v-model="cbCategoriaCV.CuentaVentas" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="BloqueoCV==true" />
                                                        </ValidationProvider>
                                                    </div>
                                                    <div class="md:5/12 lg:5/12 xl:w-5/12" style="float:left;padding:10px 20px">
                                                        <ValidationProvider name="CuentaDescuento" rules="max:12" v-slot="{ errors }">
                                                            <label class="label-size"> Cuenta Descuento </label>
                                                            <vs-input type="text" class="w-full" v-model="cbCategoriaCV.CuentaDescuento" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="BloqueoCV==true" />
                                                        </ValidationProvider>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="float:right;padding:20px 50px">
                                                <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" @click="ValidarCV()" id="btn_GuardarCV" :disabled="BloqueoBCV==true"> Guardar</vs-button>
                                                <vs-button color="danger" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-x" @click="CancelarCV()" id="btn_CancelarCV" :disabled="BloqueoCV==true"> Cancelar</vs-button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>

            </div>
            <!-- -----------------------CUENTAS ANTICIPO--------------------->
            <div v-show="Opcion==5">
                <div style="box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.2);padding:10px;border-radius:5px; border:1px solid #ccc;">
                    <div class="w-full">
                        <div class="flex flex-wrap">
                            <div class="w-12/12 lg:w-12/12 xl:w-6/12">
                                <vs-table2 tooltip max-items="10" pagination :data="ListaCuentasA" search id="tb_departamentos">
                                    <template slot="thead">
                                        <th width="50">Cuenta Contable</th>
                                        <th width="400">Descripción Contable</th>
                                        <th width="400">Descripción para el Cheque</th>
                                        <th width="30">Editar</th>
                                        <th width="30">Eliminar</th>
                                    </template>
                                    <template slot-scope="{data}">
                                        <tr :key="indextr" v-for="(tr, indextr) in data">
                                            <vs-td2 noTooltip :data="data[indextr].Cuenta" align="center">
                                                {{data[indextr].Cuenta}}
                                            </vs-td2>
                                            <vs-td2 :data="data[indextr].Nombre">
                                                {{data[indextr].Nombre}}
                                            </vs-td2>
                                            <vs-td2 :data="data[indextr].Descripcion">
                                                {{data[indextr].Descripcion}}
                                            </vs-td2>
                                            <vs-td2 noTooltip align="center">
                                                <vs-button color="success" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" @click="EditarCuentasCA(tr)"></vs-button>
                                            </vs-td2>
                                            <vs-td2 noTooltip align="center">
                                                <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="EliminarCuentasCA(tr)"></vs-button>
                                            </vs-td2>
                                        </tr>
                                    </template>
                                </vs-table2>
                            </div>
                            <br>
                            <div class="w-12/12 lg:w-12/12 xl:w-6/12" style="float:left;padding:100px 100px">
                                <div class="w-full">
                                    <vs-button color="primary" style="float:left;margin: 5px" type="filled" icon-pack="fas" icon="fa-plus" @click="NuevaCuentaAnt()"> Nueva</vs-button>
                                    <br>
                                    <br>
                                    <vs-divider class="label-size">Cuentas Anticipo</vs-divider>
                                    <div style="padding:10px 20px">
                                        <ValidationProvider name="CuentaContable">
                                            <label class="label-size"> Cuenta Contable</label>
                                            <multiselect v-model="cbCuentasContables" :options="ListaCuentasCont" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="CuentaSeleccionada" placeholder="Búsqueda Cuenta Contable" @input="onChangeCuenta" :disabled="BloqueoCodigoCA==true">
                                                <span slot="noOptions">Lista no disponible.</span>
                                            </multiselect>
                                        </ValidationProvider>
                                    </div>
                                    <div style="padding:10px 20px">
                                        <ValidationProvider name="Descripcion" rules="max:60|required" v-slot="{ errors }">
                                            <label class="label-size"> Descripción </label>
                                            <vs-input type="text" class="w-full" v-model="cbCuentasContables.Descripcion" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="BloqueoCA==true" />
                                        </ValidationProvider>
                                    </div>
                                    <div style="padding:10px 20px">
                                        <vs-button v-if="Actualiza == true" color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" @click="GuardarACuentasCA()" id="btn_GuardarA" :disabled="BloqueoCA"> Guardar</vs-button>
                                        <vs-button v-if="Nueva == true" color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" @click="GuardarNCuentasCA()" id="btn_GuardarNA" :disabled="BloqueoCA"> Guardar</vs-button>
                                        <vs-button color="danger" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-x" @click="CancelarCA()" id="btn_CancelarA" :disabled="BloqueoCA"> Cancelar</vs-button>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </form>
    </div>
</vx-card>
</template>

<script>
import Multiselect from 'vue-multiselect'
import bitacora from '@/components/sermesa/funciones/SMBitacora'
import "vue-multiselect/dist/vue-multiselect.min.css"

export default {
    components: {
        Multiselect,

    },
    data() {
        return {
            Opcion: 1,
            BloqueoB: true,
            BloqueoD: true,
            BloqueoCC: true,
            BloqueoCV: true,
            BloqueoCA: true,
            BloqueoCodigo: false,
            BloqueoCodigoD: false,
            BloqueoCodigoCC: true,
            BloqueoCodigoCV: false,
            BloqueoHospital: true,
            BloqueoCanc: false,
            //Boton guardar 
            BloqueoBB: true,
            BloqueoBD: true,
            BloqueoBCC: true,
            BloqueoBCV: true,
            BloqueoBCA: true,
            cbBodega: '',
            ListaBodega: [],
            //
            ListaHospital: [],
            cbHospital: '',
            Hospital: '',
            NombreHospital: '',
            ListaDepartamentos: [],
            cbDepartamentos: '',
            HospitalD: '',
            NombreHospitalD: '',
            ListaHospitalD: [],
            cbHospitalD: '',
            cbBusquedaEmpresa: [],
            //
            cbCategoriaC: [],
            cbCategoria: '',
            ListaCategoriaC: [],
            ListaCategoria: [],
            ListaCategoriaCV: [],
            CodCategoriaCC: '',
            NombreCateogiaCC: '',
            CodHospitalCC: '',
            cbHospitalCC: [],
            NombreHospitalCC: '',
            cbBusquedaEmpresaCC: [],
            CodEmpresaCC: [],
            NombreEmpresaCC: '',
            NuevaCat: {},
            EliminarCat: {},
            cbCategoriaCV: [],
            CodCategoriaCV: '',
            NombreCateogiaCV: '',
            CodHospitalCV: '',
            cbHospitalCV: [],
            NombreHospitalCV: '',
            //
            ListaCuentasA: {},
            cbCuentasContables: [],
            ListaCuentasCont: [],
            CodCuentaContable: '',
            Nombre: '',
            BloqueoCodigoCA: '',
            Actualiza: false,
            Nueva: false,
            CuentasNValidas: [],
            ListaEmpresas: []

        }
    },
    mounted() {

        this.ConsultaBodegas()
        this.ConsultaHospital()
        this.ConsultaEmpresas()
    },
    watch: {

        Opcion(value) {
            if (value == 1) {
                this.BloqueoD = true
                this.BloqueoCC = true
                this.BloqueoCV = true
                this.BloqueoCA = true
                this.BloqueoBD = true
                this.BloqueoBCC = true
                this.BloqueoBCV = true
                this.BloqueoBCA = true
                this.BloqueoCanc = false
            }
            if (value == 2) {
                this.BloqueoB = true
                this.BloqueoCC = true
                this.BloqueoCV = true
                this.BloqueoCA = true
                this.BloqueoBB = true
                this.BloqueoBCC = true
                this.BloqueoBCV = true
                this.BloqueoBCA = true
                this.BloqueoBCanc = false
                this.ConsultaDepartamento()
            }
            if (value == 3) {
                this.BloqueoB = true
                this.BloqueoD = true
                this.BloqueoCV = true
                this.BloqueoCA = true
                this.BloqueoBB = true
                this.BloqueoBD = true
                this.BloqueoBCV = true
                this.BloqueoBCA = true
                this.BloqueoCanc = false
                this.BloqueoHospital = true
                this.ConsultaCatCostos()
                this.ConsultaHospital()
                this.CancelarCC()

            }
            if (value == 4) {
                this.CancelarCV()
                this.BloqueoB = true
                this.BloqueoD = true
                this.BloqueoCC = true
                this.BloqueoCA = true
                this.BloqueoBB = true
                this.BloqueoBD = true
                this.BloqueoBCC = true
                this.BloqueoBCA = true
                this.BloqueoCanc = false
                this.ConsultaCatCostosV()
                this.ConsultaHospital()
            }
            if (value == 5) {
                this.BloqueoB = true
                this.BloqueoD = true
                this.BloqueoCV = true
                this.BloqueoCC = true
                this.BloqueoBB = true
                this.BloqueoBD = true
                this.BloqueoBCV = true
                this.BloqueoBCC = true
                this.BloqueoCanc = false
                this.ConsultaCuentaAnticipos()
                this.BloqueoCodigoCA = true
                this.BloqueoCA = true
                this.BloqueoBCA = true
            }
        }

    },
    methods: {
        //BODEGAS
        ConsultaBodegas() {
            this.axios.post('/app/v1_contabilidad_general/ConsultaCuentas', {
                    Opcion: 'C',
                    SubOpcion: '1',
                    CodigoBodega: this.BloqueoCanc == true ? this.cbBodega.CodigoBodega : ''
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.ListaBodega = [];
                    } else {

                        if (this.BloqueoCanc == false) {
                            this.ListaBodega = resp.data.json;
                        }
                        this.cbBodega = resp.data.json[0]

                        this.cbHospital = {
                            Hospital: this.cbBodega.Hospital,
                            NombreHospital: this.cbBodega.NombreHospital
                        };
                        this.cbBusquedaEmpresa = {
                            CodigoEmpresa: resp.data.json[0].EmpresaRealx,
                            NombreEmpresa: resp.data.json[0].NombreEmp
                        }
                        setTimeout(() => {
                            bitacora.registrar(this.cbBodega, {
                                Tipo: 'Modificación',
                                Codigo: this.cbBodega.CodigoBodega
                            })
                        }, 2000)

                    }
                })
                .catch(() => {})
        },
        ConsultaHospital() {
            this.axios.post('/app/v1_contabilidad_general/ConsultaCuentas', {
                    Opcion: 'C',
                    SubOpcion: '2'
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.ListaHospital = [];
                    } else {

                        this.ListaHospital = resp.data.json;
                    }
                })
                .catch(() => {})
        },
        ConsultaEmpresas() {
            this.axios.post('/app/v1_contabilidad_general/ConsultaCuentas', {
                    Opcion: 'C',
                    SubOpcion: '3'
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.ListaEmpresas = [];
                    } else {
                        this.ListaEmpresas = resp.data.json;
                    }
                })
                .catch(() => {})
        },
        BodegaSeleccionada({
            CodigoBodega,
            Nombre
        }) {
            return `${CodigoBodega} - ${Nombre} `
        },
        onChangeBodega(value) {
            //Si existe seleccionada un elemento
            if (value !== null && value.length !== 0) {
                this.cbBodega.CuentaLocal = value.CuentaLocal;
                this.cbBodega.CuentaEnTransito = value.CuentaEnTransito;
                this.cbBodega.CuentaDespachoPaciente = value.CuentaDespachoPaciente;

                this.cbHospital = {
                    Hospital: value.Hospital,
                    NombreHospital: value.NombreHospital
                };
                this.cbBusquedaEmpresa = {
                    CodigoEmpresa: value.EmpresaRealx,
                    NombreEmpresa: value.NombreEmp
                }
                setTimeout(() => {
                    bitacora.registrar(this.cbBodega, {
                        Tipo: 'Modificación',
                        Codigo: this.cbBodega.CodigoBodega
                    })
                }, 2000)

            }
        },
        HospitalSeleccionado({
            Hospital,
            NombreHospital
        }) {
            return `${Hospital} - ${NombreHospital} `
        },
        onChangeHospital(value) {
            //Si existe seleccionada un elemento
            if (value !== null && value.length !== 0) {
                this.Hospital = value.Hospital;
                this.NombreHospital = value.NombreHospital;
                this.cbBodega.Hospital = value.Hospital;
            } else {
                this.Hospital = '';
                this.NombreHospital = ''
            }

        },
        EmpresaSeleccionada({
            CodigoEmpresa,
            NombreEmpresa
        }) {
            return `${CodigoEmpresa} - ${NombreEmpresa} `
        },
        onChangeEmpresa(value) {
            //Si existe seleccionada un elemento
            if (value !== null && value.length !== 0) {
                this.CodEmpresa = value.CodigoEmpresa;
                this.NombreEmpresa = value.NombreEmpresa
            } else {
                this.CodEmpresa = ''
                this.NombreEmpresa = ''
            }
        },

        //DEPARTAMENTOS
        ConsultaDepartamento() {
            this.axios.post('/app/v1_contabilidad_general/ConsultaDepartamento', {
                    Opcion: 'C',
                    SubOpcion: '4',
                    CodigoDepto: this.BloqueoCanc == true ? this.cbDepartamentos.Codigo : ''
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.ListaDepartamentos = [];
                    } else {
                        if (this.BloqueoCanc == false) {
                            this.ListaDepartamentos = resp.data.json;
                        }
                        this.cbDepartamentos = resp.data.json[0]

                        this.cbHospitalD = {
                            Hospital: this.cbDepartamentos.Hospital,
                            NombreHospital: this.cbDepartamentos.NombreHospital
                        }
                        setTimeout(() => {
                            bitacora.registrar(this.cbDepartamentos, {
                                Tipo: 'Modificación',
                                Codigo: this.cbDepartamentos.CodigoBodega
                            })
                        }, 2000)
                    }

                })
                .catch(() => {})
        },
        HospitalSeleccionadoD({
            Hospital,
            NombreHospital
        }) {
            return `${Hospital} - ${NombreHospital} `
        },
        onChangeHospitalD(value) {
            //Si existe seleccionada un elemento
            if (value !== null && value.length !== 0) {
                this.HospitalD = value.Hospital;
                this.NombreHospitalD = value.NombreHospital;
                this.cbDepartamentos.Hospital = value.Hospital

            } else {
                this.HospitalD = '';
                this.NombreHospitalD = ''
            }

        },
        onChangedepartamento(value) {
            if (value !== null && value.length !== 0) {
                this.idDepartamento = value.Codigo;
                this.Departamento = value.NombreDepto;
                this.cbDepartamentos.CuentaEnTransito = value.CuentaEnTransito;
                this.cbHospitalD = {
                    Hospital: value.Hospital,
                    NombreHospital: value.NombreHospital
                };
                setTimeout(() => {
                    bitacora.registrar(this.cbDepartamentos, {
                        Tipo: 'Modificación',
                        Codigo: this.cbDepartamentos.Codigo
                    })
                }, 2000)

            } else {
                this.idDepartamento = '';
                this.Departamento = '';
                this.cbDepartamentos.CuentaEnTransito = '';
                this.cbDepartamentos.Hospital = ''
            }
        },
        DepartamentoSeleccionado({
            Codigo,
            NombreDepto
        }) {
            return `${Codigo} - ${NombreDepto} `
        },
        CategoriaSeleccionadaC({
            Categoria,
            NombreCat,
            Hospital
        }) {
            return `${Categoria} - ${NombreCat} - ${Hospital}`
        },
        CategoriaSeleccionada({
            Codigo,
            Nombre
        }) {
            return `${Codigo} - ${Nombre}`
        },
        onChangeCategoriaC(value) {
            //Si existe seleccionada un elemento
            if (value !== null && value.length !== 0) {
                this.CodCategoriaCC = value.Categoria;
                this.NombreCateogiaCC = value.NombreCat;
                this.CuentasCostoSuministro = value.CuentasCostoSuministro;
                this.Diversos = value.Diversos;
                this.cbHospitalCC = {
                    Hospital: value.Hospital,
                    NombreHospital: value.NombreHospital
                };
                this.cbBusquedaEmpresaCC = {
                        CodigoEmpresa: value.Empresa,
                        NombreEmpresa: value.NombreEmp
                    },
                    this.cbCategoriaC.TipoBodega = value.TipoBodega
            } else {
                this.CodCategoriaCC = '';
                this.NombreCateogiaCC = '';
                this.cbCategoriaC.CuentasCostoSuministro = '';
                this.cbCategoriaC.Diversos = '';
                this.cbHospitalCC = '';
                this.cbBusquedaEmpresaCC = '';
                this.cbCategoriaC.TipoBodega = '';
            }

        },
        onChangeCategoria(value) {
            //Si existe seleccionada un elemento
            if (value !== null && value.length !== 0) {
                this.Codigo = value.Codigo;
                this.Nombre = value.Nombre;
                this.NuevaCat.Codigo = value.Codigo
                this.NuevaCat.Empresa = value.Empresa
            } else {
                this.Codigo = '',
                    this.Nombre = ''
                this.NuevaCat.Codigo = ''
                this.NuevaCat.Empresa = ''
            }

        },
        HospitalSeleccionadoCC({
            Hospital,
            NombreHospital
        }) {
            return `${Hospital} - ${NombreHospital} `
        },
        onChangeHospitalCC(value) {
            //Si existe seleccionada un elemento
            if (value !== null && value.length !== 0) {
                this.CodHospitalCC = value.Hospital;
                this.NombreHospitalCC = value.NombreHospital;
                this.NuevaCat.Hospital = value.Hospital

            } else {
                this.CodHospitalCC = '';
                this.NombreHospitalCC = '',
                    this.NuevaCat.Hospital = ''
            }

        },
        CategoriaSeleccionadaCV({
            Categoria,
            NombreCat,
            Hospital
        }) {
            return `${Categoria} - ${NombreCat} - ${Hospital} `
        },
        onChangeCategoriaCV(value) {
            //Si existe seleccionada un elemento
            if (value !== null && value.length !== 0) {
                this.CodCategoriaCV = value.Categoria;
                this.NombreCateogiaCV = value.NombreCat;
                this.CuentaVentas = value.CuentaVentas;
                this.CuentaDescuento = value.CuentaDescuento;
                this.cbHospitalCV = {
                    Hospital: value.Hospital,
                    NombreHospital: value.NombreHospital
                };
                this.CodEmpresa = value.Empresa
                this.TipoBod = value.TipoBodega
            } else {
                this.CodCategoriaCV = '';
                this.NombreCateogiaCV = '';
                this.cbHospitalCV = '';
                this.cbBusquedaEmpresaCV = ''
                this.CuentaDescuento = ''
                this.CuentaVentas = ''
            }

        },
        HospitalSeleccionadoCV({
            Hospital,
            NombreHospital
        }) {
            return `${Hospital} - ${NombreHospital} `
        },
        onChangeHospitalCV(value) {
            //Si existe seleccionada un elemento
            if (value !== null && value.length !== 0) {
                this.CodHospitalCV = value.Hospital;
                this.NombreHospitalCV = value.NombreHospital;
                this.NuevaCat.Hospital = value.Hospital
            } else {
                this.CodHospitalCV = '';
                this.NombreHospitalCV = ''
                this.NuevaCat.Hospital = ''
            }

        },
        CuentaSeleccionada({
            Cuenta,
            Nombre
        }) {
            return `${Cuenta} - ${Nombre} `
        },
        onChangeCuenta(value) {
            //Si existe seleccionada un elemento
            if (value !== null && value.length !== 0) {
                this.Codigo = value.Cuenta;
                this.Nombre = value.Nombre;
                this.cbCuentasContables.Empresa = value.Empresa
                this.cbCuentasContables.Descripcion = value.Descripcion == null ? value.Nombre : value.Descripcion
            } else {
                this.Codigo = '';
                this.Nombre = ''
                this.cbCuentasContables.Descripcion = ''
                this.cbCuentasContables.Empresa = ''
            }

        },
        ConsultaCatCostos() {
            this.axios.post('/app/v1_contabilidad_general/ConsultaCategoriaC', {
                    Opcion: 'C',
                    SubOpcion: '5'
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaCategoriaC = resp.data.json;
                    }

                })
                .catch(() => {})
        },
        ConsultaCategorias() {
            this.axios.post('/app/v1_contabilidad_general/ConsultaCategoriaC', {
                    Opcion: 'C',
                    SubOpcion: '9'
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.ListaCategoria = [];
                    } else {
                        if (this.Nueva == true) {
                            this.ListaCategoria = resp.data.json;
                        }
                    }
                })
                .catch(() => {})
        },
        ConsultaMax() {
            this.MaxCodigo = ''
            this.axios.post('/app/v1_contabilidad_general/ConsultaCategoriaC', {
                    Opcion: 'C',
                    SubOpcion: '10'
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.MaxCodigo = resp.data.json[0].Codigo;
                    }
                })
                .catch(() => {})
        },
        ConsultaCatCostosV() {
            this.axios.post('/app/v1_contabilidad_general/ConsultaCategoriaC', {
                    Opcion: 'C',
                    SubOpcion: '8',
                    CodigoCategoria: this.BloqueoCanc == true ? this.cbCategoriaCV.Categoria : '',
                    HospitalCC: this.BloqueoCanc == true ? this.cbCategoriaCV.Hospital : '',
                })
                .then(resp => {
                    let arrayTemp = []
                    if (resp.data.codigo == 0) {
                        arrayTemp = resp.data.json;
                    }
                    this.ListaCategoriaCV = arrayTemp
                })
                .catch(() => {})
        },
        ConsultaCuentaAnticipos() {
            this.axios.post('/app/v1_contabilidad_general/ConsultaCuentasAnt', {
                    Opcion: 'C',
                    SubOpcion: '6'
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.ListaCuentasA = [];
                    } else {
                        this.ListaCuentasA = resp.data.json;
                    }
                })
                .catch(() => {})
        },
        ConsultaCuentaContable() {
            this.axios.post('/app/v1_contabilidad_general/ConsultaCuentasAnt', {
                    Opcion: 'C',
                    SubOpcion: '7'
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.ListaCuentasCont = [];
                    } else {
                        this.ListaCuentasCont = resp.data.json;
                    }
                })
                .catch(() => {})
        },
        GuardarCuentas() {
            if (this.CuentasNValidas.length == 0) {
                let valor = {
                    ...this.cbBodega
                }
                valor = bitacora.obtener()

                if (valor == null) {
                    this.BloqueoB = true
                    this.BloqueoBB = true
                    this.BloqueoCodigo = false
                    this.BloqueoCancM = false
                    this.ListaBodega = []
                    this.ConsultaBodegas()

                } else if (valor != null) {
                    this.axios.post('/app/v1_contabilidad_general/UpdateCuentasBodegas', {
                            ...this.cbBodega,
                            ...this.cbHospital,
                            ...this.cbBusquedaEmpresa,
                            Opcion: 'U',
                            SubOpcion: '1'
                        })
                        .then(resp => {
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Configuración',
                                    text: resp.data.mensaje,
                                })
                            } else {
                                this.axios.post('/app/v1_contabilidad_general/registro_bitacora', {
                                    info: valor,
                                    tabla: 'Bodegas',
                                    llave: {
                                        Codigo: this.cbBodega.CodigoBodega,
                                        Empresa: this.cbBodega.Empresa
                                    }
                                })
                                this.BloqueoB = true
                                this.BloqueoBB = true
                                this.BloqueoCodigo = false
                                this.BloqueoCancM = false
                                this.ListaBodega = []
                                this.ConsultaBodegas()
                            }
                        })
                }
            }
        },
        GuardarCuentasD() {
            if (this.CuentasNValidas.length == 0) {
                let valor1 = {
                    ...this.cbDepartamentos
                }
                valor1 = bitacora.obtener()

                if (valor1 == null) {
                    this.BloqueoD = true
                    this.BloqueoBD = true
                    this.BloqueoCodigoD = false
                    this.BloqueoCanc = false
                    this.ListaDepartamentos = []
                    this.ConsultaDepartamento()

                } else if (valor1 != null) {

                    this.axios.post('/app/v1_contabilidad_general/UpdateDepartamento', {
                            CodigoDepto: this.cbDepartamentos.Codigo,
                            CuentaEnTransitoD: this.cbDepartamentos.CuentaEnTransito,
                            HospitalD: this.cbHospitalD.Hospital,
                            Opcion: 'U',
                            SubOpcion: '2'
                        })
                        .then(resp => {
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Configuración',
                                    text: resp.data.mensaje,
                                })
                            } else {
                                this.axios.post('/app/v1_contabilidad_general/registro_bitacora', {
                                    info: valor1,
                                    tabla: 'DepartamentosInv',
                                    llave: {
                                        Codigo: this.cbDepartamentos.Codigo,
                                        Empresa: this.cbDepartamentos.Empresa
                                    }
                                })
                                this.BloqueoD = true
                                this.BloqueoBD = true
                                this.BloqueoCodigoD = false
                                this.BloqueoCanc = false
                                this.ListaDepartamentos = []
                                this.ConsultaDepartamento()
                            }
                        })
                }
            }

        },
        GuardarCuentasCC() {
            if (this.CuentasNValidas.length == 0) {
                let valor2 = {
                    ...this.cbCategoriaC
                }
                valor2 = bitacora.obtener()

                if (valor2 == null) {

                    this.CancelarCC()
                    this.ConsultaCatCostos()

                } else {
                    if (this.Nueva == false) {
                        this.axios.post('/app/v1_contabilidad_general/TranCategoriaCostos', {
                                Opcion: 'U',
                                SubOpcion: '3',
                                CodigoCategoria: this.cbCategoriaC.Categoria,
                                HospitalCC: this.cbHospitalCC.Hospital,
                                TipoBodega: this.cbCategoriaC.TipoBodega,
                                CuentasCostoSuministro: this.cbCategoriaC.CuentasCostoSuministro,
                                Diversos: this.cbCategoriaC.Diversos
                            })
                            .then(resp => {
                                if (resp.data.codigo == 0) {
                                    this.axios.post('/app/v1_contabilidad_general/registro_bitacora', {
                                        info: valor2,
                                        tabla: 'CategoriaCuentasCostos',
                                        llave: {
                                            Empresa: this.cbCategoriaC.Empresa,
                                            Hospital: this.cbHospitalCC.Hospital,
                                            Categoria: this.cbCategoriaC.Categoria,
                                            TipoBodega: this.cbCategoriaC.TipoBodega
                                        }
                                    })
                                    this.CancelarCC()
                                    this.ConsultaCatCostos()
                                } else {
                                    this.CancelarCC()
                                    this.ConsultaCatCostos()
                                }
                            })
                    } else {
                        if (this.cbCategoria.Codigo == '' || this.cbCategoria.Codigo == null || this.cbCategoria.Codigo == '  ') {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Cuentas Categoría Costos',
                                text: 'Seleccione la Categoría',
                            })
                            return;
                        } else if (this.cbHospitalCC.Hospital == '' || this.cbHospitalCC.Hospital == null || this.cbHospitalCC.Hospital == '  ') {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Cuentas Categoría Costos',
                                text: 'Seleccione el Hospital',
                            })
                            return;
                        } else if (this.cbCategoriaC.TipoBodega == '') {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Cuentas Categoría Costos',
                                text: 'Seleccione el Tipo de Bodega',
                            })
                            return;
                        } else if (this.cbCategoriaC.CuentasCostoSuministro == '') {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Cuentas Categoría Costos',
                                text: 'Agregue Cuenta Costos',
                            })
                            return;
                        } else {
                            this.NuevaCat.TipoBodega = this.cbCategoriaC.TipoBodega,
                                this.NuevaCat.CuentasCostoSuministro = this.cbCategoriaC.CuentasCostoSuministro,
                                this.NuevaCat.Diversos = this.cbCategoriaC.Diversos
                            this.NuevaCat.Empresa = this.cbCategoria.Empresa

                            this.axios.post('/app/v1_contabilidad_general/TranCategoriaCostos', {
                                    Opcion: 'I',
                                    SubOpcion: '2',
                                    CodigoCategoria: this.NuevaCat.Codigo,
                                    HospitalCC: this.NuevaCat.Hospital,
                                    TipoBodega: this.NuevaCat.TipoBodega,
                                    CuentasCostoSuministro: this.NuevaCat.CuentasCostoSuministro,
                                    Diversos: this.NuevaCat.Diversos
                                })
                                .then(resp => {
                                    if (resp.data.codigo == 0) {
                                        this.axios.post('/app/v1_contabilidad_general/registro_bitacora', {
                                            info: valor2,
                                            tabla: 'CategoriaCuentasCostos',
                                            llave: {
                                                Empresa: this.NuevaCat.Empresa,
                                                Hospital: this.NuevaCat.Hospital,
                                                Categoria: this.NuevaCat.Codigo,
                                                TipoBodega: this.NuevaCat.TipoBodega,
                                            }
                                        })
                                        this.CancelarCC()
                                        this.ConsultaCatCostos()
                                    } else {
                                        this.CancelarCC()
                                        this.ConsultaCatCostos()
                                    }
                                })

                        }
                    }
                }
            }
        },
        EliminarCuentasCC(value) {
            this.EliminarCat.Empresa = value.Empresa
            this.EliminarCat.Hospital = value.Hospital
            this.EliminarCat.TipoBodega = value.TipoBodega
            this.EliminarCat.Categoria = value.Categoria
            this.EliminarCat.CuentasCostoSuministro = value.CuentasCostoSuministro
            this.EliminarCat.Diversos = value.Diversos
            this.EliminarCat.CuentaDescuento = value.CuentaDescuento
            this.EliminarCat.CuentaVentas = value.CuentaVentas

            if (this.Nueva == false) {
                setTimeout(() => {
                    bitacora.registrar(this.EliminarCat, {
                        Tipo: 'Linea Eliminada',
                        Codigo: this.EliminarCat.Categoria,
                    })
                }, 2000)

                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',
                    text: '¿Desea eliminar el registro? ',
                    acceptText: 'Aceptar',
                    cancelText: 'Cancelar',
                    accept: () => {
                        let llave = {
                            Empresa: this.EliminarCat.Empresa,
                            Hospital: this.EliminarCat.Hospital,
                            Categoria: this.EliminarCat.Categoria,
                            TipoBodega: this.EliminarCat.TipoBodega,
                        }
                        this.EliminarCat.Empresa = ''
                        this.EliminarCat.Hospital = ''
                        this.EliminarCat.TipoBodega = ''
                        this.EliminarCat.Categoria = ''
                        this.EliminarCat.CuentasCostoSuministro = ''
                        this.EliminarCat.Diversos = ''
                        this.EliminarCat.CuentaDescuento = ''
                        this.EliminarCat.CuentaVentas = ''

                        let valor2 = {
                            ...this.EliminarCat
                        }
                        valor2 = bitacora.obtener()
                        this.axios.post('/app/v1_contabilidad_general/TranCategoriaCostos', {
                                Opcion: 'E',
                                SubOpcion: '2',
                                CodigoCategoria: value.Categoria,
                                HospitalCC: value.Hospital,
                                TipoBodega: value.TipoBodega
                            })
                            .then(resp => {
                                if (resp.data.codigo == 0) {
                                    this.axios.post('/app/v1_contabilidad_general/registro_bitacora', {
                                        info: valor2,
                                        tabla: 'CategoriaCuentasCostos',
                                        llave: llave
                                    })
                                    this.CancelarCC()
                                    this.ConsultaCatCostos()
                                } else {
                                    this.CancelarCC()
                                    this.ConsultaCatCostos()
                                }
                            })
                    }
                })
            }

        },
        EliminarCuentasCV(value) {
            this.EliminarCat.Empresa = value.Empresa
            this.EliminarCat.Hospital = value.Hospital
            this.EliminarCat.Categoria = value.Categoria
            this.EliminarCat.CuentaDescuento = value.CuentaDescuento
            this.EliminarCat.CuentaVentas = value.CuentaVentas

            if (this.Nueva == false) {
                setTimeout(() => {
                    bitacora.registrar(this.EliminarCat, {
                        Tipo: 'Linea Eliminada',
                        Codigo: this.EliminarCat.Categoria,
                    })
                }, 2000)

                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',
                    text: '¿Desea eliminar el registro? ',
                    acceptText: 'Aceptar',
                    cancelText: 'Cancelar',
                    accept: () => {

                        let llave = {
                            Empresa: this.EliminarCat.Empresa,
                            Hospital: this.EliminarCat.Hospital,
                            Categoria: this.EliminarCat.Categoria,
                            TipoBodega: this.EliminarCat.TipoBodega,
                        }

                        this.EliminarCat.Empresa = ''
                        this.EliminarCat.Hospital = ''
                        this.EliminarCat.Categoria = ''
                        this.EliminarCat.CuentaDescuento = ''
                        this.EliminarCat.CuentaVentas = ''

                        let valor3 = {
                            ...this.EliminarCat
                        }
                        valor3 = bitacora.obtener()
                        this.axios.post('/app/v1_contabilidad_general/TranCategoriaCostos', {
                                Opcion: 'E',
                                SubOpcion: '3',
                                CodigoCategoria: value.Categoria,
                                HospitalCC: value.Hospital,
                            })
                            .then(resp => {
                                if (resp.data.codigo == 0) {
                                    this.axios.post('/app/v1_contabilidad_general/registro_bitacora', {
                                        info: valor3,
                                        tabla: 'CategoriaCuentasVentas',
                                        llave: llave
                                    })
                                    this.CancelarCV()
                                    this.ConsultaCatCostosV()
                                } else {
                                    this.CancelarCV()
                                    this.ConsultaCatCostosV()
                                }
                            })
                    }
                })
            }
        },
        GuardarCuentasCV() {
            if (this.CuentasNValidas.length == 0) {
                let valor3 = {
                    ...this.cbCategoriaCV
                }
                valor3 = bitacora.obtener()

                if (valor3 == null) {
                    this.CancelarCV()
                    this.ConsultaCatCostosV()
                } else if (valor3 != null) {
                    if (this.Nueva == false) {
                        this.axios.post('/app/v1_contabilidad_general/TranCategoriaCostos', {
                                Opcion: 'U',
                                SubOpcion: '4',
                                CodigoCategoria: this.cbCategoriaCV.Categoria,
                                HospitalCC: this.cbHospitalCV.Hospital,
                                CuentaVentas: this.cbCategoriaCV.CuentaVentas,
                                CuentaDescuento: this.cbCategoriaCV.CuentaDescuento
                            })
                            .then(resp => {
                                if (resp.data.codigo == 0) {
                                    this.axios.post('/app/v1_contabilidad_general/registro_bitacora', {
                                        info: valor3,
                                        tabla: 'CategoriaCuentasVentas',
                                        llave: {
                                            Empresa: this.cbCategoriaCV.Empresa,
                                            Hospital: this.cbCategoriaCV.Hospital,
                                            Categoria: this.cbCategoriaCV.Categoria
                                        }
                                    })

                                    this.CancelarCV()
                                    this.ConsultaCatCostosV()
                                } else {
                                    this.CancelarCV()
                                    this.ConsultaCatCostosV()
                                }
                            })

                    } else {

                        if (this.cbCategoria.Codigo == '' || this.cbCategoria.Codigo == null || this.cbCategoria.Codigo == '  ') {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Cuentas Categoría Ventas',
                                text: 'Seleccione la Categoría',
                            })
                            return;
                        } else if (this.cbHospitalCV.Hospital == '' || this.cbHospitalCV.Hospital == null || this.cbHospitalCV.Hospital == '  ') {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Cuentas Categoría Ventas',
                                text: 'Seleccione el Hospital',
                            })
                            return;
                        } else {
                            this.NuevaCat.CuentaVentas = this.cbCategoriaCV.CuentaVentas,
                                this.NuevaCat.CuentaDescuento = this.cbCategoriaCV.CuentaDescuento
                            this.NuevaCat.Empresa = this.cbCategoria.Empresa

                            this.axios.post('/app/v1_contabilidad_general/TranCategoriaCostos', {
                                    Opcion: 'I',
                                    SubOpcion: '3',
                                    CodigoCategoria: this.cbCategoria.Codigo,
                                    HospitalCC: this.cbHospitalCV.Hospital,
                                    CuentaVentas: this.cbCategoriaCV.CuentaVentas,
                                    CuentaDescuento: this.cbCategoriaCV.CuentaDescuento
                                })
                                .then(resp => {
                                    if (resp.data.codigo == 0) {
                                        this.axios.post('/app/v1_contabilidad_general/registro_bitacora', {
                                            info: valor3,
                                            tabla: 'CategoriaCuentasVentas',
                                            llave: {
                                                Empresa: this.NuevaCat.Empresa,
                                                Hospital: this.NuevaCat.Hospital,
                                                Categoria: this.NuevaCat.Codigo,
                                            }
                                        })
                                        this.CancelarCV()
                                        this.ConsultaCatCostosV()
                                    } else {
                                        this.CancelarCV()
                                        this.ConsultaCatCostosV()
                                    }
                                })

                        }
                    }
                }
            }

        },
        GuardarACuentasCA() {
            if (this.cbCuentasContables.Codigo == '' || this.cbCuentasContables.Codigo == null || this.cbCuentasContables.Codigo == '  ') {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Cuentas Anticipos',
                    text: 'Seleccione la Cuenta Contable',
                })
                return;
            } else if (this.cbCuentasContables.Descripcion == '' || this.cbCuentasContables.Descripcion == null || this.cbCuentasContables.Descripcion == '  ') {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Cuentas Anticipos',
                    text: 'Ingrese Descripción',
                })
                return;
            } else {

                this.axios.post('/app/v1_contabilidad_general/UpdateCuentasAnt', {
                        Opcion: 'U',
                        SubOpcion: '5',
                        CuentaAnticipo: this.cbCuentasContables.Codigo,
                        CuentaContable: this.cbCuentasContables.Cuenta,
                        Descripcion: this.cbCuentasContables.Descripcion,

                    })
                    .then(resp => {
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Configuración',
                                text: resp.data.mensaje,
                            })
                            this.CancelarCA()
                            this.ConsultaCuentaAnticipos()
                        } else {
                            let valor = bitacora.obtener()
                            if (valor != null) {
                                this.axios.post('/app/v1_contabilidad_general/registro_bitacora', {
                                    info: valor,
                                    tabla: 'CuentasDeAnticipos',
                                    llave: {
                                        Empresa: this.cbCuentasContables.Empresa,
                                        Codigo: this.cbCuentasContables.Codigo
                                    }
                                })
                            }
                            this.BloqueoCodigoCA = false
                            this.ListaCuentasA = []
                            this.BloqueoCA = true
                            this.BloqueoCodigoCA = false
                            this.CancelarCA()
                            this.ConsultaCuentaAnticipos()
                        }
                    })
            }
        },
        GuardarNCuentasCA() {
            this.NuevaCat.Empresa = this.cbCuentasContables.Empresa
            this.NuevaCat.Codigo = this.MaxCodigo
            this.NuevaCat.Cuenta = this.cbCuentasContables.Cuenta
            this.NuevaCat.Descripicon = this.cbCuentasContables.Descripcion

            let valor = bitacora.obtener()
            let llave = {
                Empresa: this.cbCuentasContables.Empresa,
                Codigo: this.NuevaCat.Codigo
            }

            if (valor != null) {
                this.axios.post('/app/v1_contabilidad_general/InsertCuentasAnt', {
                        Opcion: 'I',
                        SubOpcion: '1',
                        CuentaContable: this.cbCuentasContables.Cuenta,
                        Descripcion: this.cbCuentasContables.Descripcion,
                    })
                    .then(resp => {
                        if (resp.data.codigo == 0) {
                            if (valor != null) {
                                this.axios.post('/app/v1_contabilidad_general/registro_bitacora', {
                                    info: valor,
                                    tabla: 'CuentasDeAnticipos',
                                    llave: llave
                                })

                            }
                            this.BloqueoCodigoCA = false
                            // this.ListaCuentasA = []
                            this.BloqueoCA = true
                            this.BloqueoCodigoCA = false

                            this.ConsultaCuentaAnticipos()
                            this.CancelarCA()
                        } else {
                            this.CancelarCA()
                        }
                    })
            }
        },
        EliminarCuentasCA(value) {
            this.EliminarCat.Empresa = value.Empresa
            this.EliminarCat.Codigo = value.Codigo
            this.EliminarCat.Cuenta = value.Cuenta
            this.EliminarCat.Descripcion = value.Descripcion

            if (this.Nueva == false) {
                setTimeout(() => {
                    bitacora.registrar(this.EliminarCat, {
                        Tipo: 'Linea Eliminada',
                        Codigo: this.EliminarCat.Codigo,
                    })
                }, 2000)

                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',
                    text: '¿Desea eliminar el registro? ',
                    acceptText: 'Aceptar',
                    cancelText: 'Cancelar',
                    accept: () => {
                        this.axios.post('/app/v1_contabilidad_general/DeleteCuentasAnt', {
                                Opcion: 'E',
                                SubOpcion: '1',
                                CuentaAnticipo: value.Codigo
                            })
                            .then(resp => {
                                if (resp.data.codigo == 0) {
                                    this.EliminarCat.Empresa = ''
                                    this.EliminarCat.Codigo = ''
                                    this.EliminarCat.Cuenta = ''
                                    this.EliminarCat.Descripcion = ''

                                    let llave = {
                                        Empresa: value.Empresa,
                                        Codigo: value.Codigo,
                                    }
                                    let valor = this.EliminarCat
                                    valor = bitacora.obtener()
                                    if (valor != null) {
                                        this.axios.post('/app/v1_contabilidad_general/registro_bitacora', {
                                            info: valor,
                                            tabla: 'CuentasDeAnticipos',
                                            llave: llave
                                        })

                                        this.CancelarCA()
                                        this.ConsultaCuentaAnticipos()
                                    } else {
                                        this.CancelarCA()
                                        this.ConsultaCuentaAnticipos()
                                    }
                                }
                            })
                    }
                })
            }

        },
        EditarCuentas() {
            this.BloqueoB = false
            this.BloqueoBB = false
            this.BloqueoCodigo = true
        },
        EditarCuentasD() {
            this.BloqueoD = false
            this.BloqueoBD = false
            this.BloqueoCodigoD = true
        },
        EditarCuentasCC(value) {
            this.CancelarCC()
            this.Nueva = false
            this.BloqueoCC = false
            this.BloqueoBCC = false
            this.BloqueoCodigoCC = true
            this.BloqueoHospital = true
            //
            this.cbCategoriaC = this.ListaCategoriaC
            this.cbCategoriaC = {
                Categoria: value.Categoria,
                NombreCat: value.NombreCat,
                Hospital: value.Hospital
            }
            this.cbCategoriaC.CuentasCostoSuministro = value.CuentasCostoSuministro
            this.cbCategoriaC.Diversos = value.Diversos
            this.cbCategoriaC.Empresa = value.Empresa
            this.cbHospitalCC = {
                Hospital: value.Hospital,
                NombreHospital: value.NombreHospital
            }
            this.cbBusquedaEmpresaCC = {
                CodigoEmpresa: value.Empresa,
                NombreEmpresa: value.NombreEmp
            }
            this.cbCategoriaC.TipoBodega = value.TipoBodega
            setTimeout(() => {
                bitacora.registrar(this.cbCategoriaC, {
                    Tipo: 'Modificación',
                    Codigo: this.cbCategoriaC.Categoria,
                })
            }, 2000)
        },
        EditarCuentasCV(value) {
            this.CancelarCV()
            this.Nueva = false
            this.BloqueoCV = false
            this.BloqueoBCV = false
            this.BloqueoCodigoCV = true
            this.BloqueoHospital = true
            //
            this.cbCategoriaCV = this.ListaCategoriaCV
            this.cbCategoriaCV = {
                Categoria: value.Categoria,
                NombreCat: value.NombreCat,
                Hospital: value.Hospital
            }
            this.cbCategoriaCV.Empresa = value.Empresa
            this.cbCategoriaCV.CuentaVentas = value.CuentaVentas
            this.cbCategoriaCV.CuentaDescuento = value.CuentaDescuento
            this.cbHospitalCV = {
                Hospital: value.Hospital,
                NombreHospital: value.NombreHospital
            }
            setTimeout(() => {
                bitacora.registrar(this.cbCategoriaCV, {
                    Tipo: 'Modificación',
                    Codigo: this.cbCategoriaCV.Categoria,
                })
            }, 2000)
        },
        EditarCuentasCA(value) {
            this.BloqueoCA = false
            this.BloqueoBCA = false
            this.Actualiza = true
            this.BloqueoCodigoCA = true
            //this.CuentaAnticipo = value.Codigo
            this.cbCuentasContables = {
                Empresa: value.Empresa,
                Codigo: value.Codigo,
                Descripcion: value.Descripcion,
                Cuenta: value.Cuenta,
                Nombre: value.Nombre
            };
            this.ConsultaCuentaAnticipos()
            setTimeout(() => {
                bitacora.registrar(this.cbCuentasContables, {
                    Tipo: 'Modificación',
                    Codigo: this.cbCuentasContables.Codigo,
                })
            }, 2000)
        },
        Cancelar() {
            this.BloqueoB = true
            this.BloqueoBB = true
            this.BloqueoCodigo = false
            this.BloqueoCanc = true
            this.ConsultaBodegas()
            this.CuentasNValidas = []
            this.Opcion = 1

        },
        CancelarD() {
            this.BloqueoD = true
            this.BloqueoBD = true
            this.BloqueoCodigoD = false
            this.BloqueoCanc = true
            this.ConsultaDepartamento()
            this.CuentasNValidas = []
            this.Opcion = 2
        },
        CancelarCC() {
            this.BloqueoCC = true
            this.BloqueoBCC = true
            this.BloqueoCodigoCC = true
            this.BloqueoCanc = true
            this.Nueva = false
            this.cbCategoriaC = []
            this.cbHospitalCC = []
            this.cbCategoria = []
            this.cbCategoriaC = []
            this.cbCategoriaC.CuentasCostoSuministro = ''
            this.cbCategoriaC.Diversos = ''
            this.cbCategoriaC.Empresa = ''
            this.cbBusquedaEmpresaCC = {
                CodigoEmpresa: '',
                NombreEmpresa: ''
            }
            this.cbCategoriaC.TipoBodega = '',
                this.CuentasNValidas = []
            this.NuevaCat = {}
            this.Opcion = 3
        },
        CancelarCV() {
            this.BloqueoCV = true
            this.BloqueoBCV = true
            this.BloqueoCodigoCV = true
            this.BloqueoCanc = true
            this.Nueva = false
            this.cbCategoriaCV = []
            this.cbHospitalCV = []
            this.cbCategoria = []
            this.cbCategoriaCV.CuentaVentas = ''
            this.cbCategoriaCV.CuentaDescuento = '',
                this.NombreHospitalCV = ''
            this.CuentasNValidas = []
            this.NuevaCat = {}
            this.Opcion = 4
        },
        CancelarCA() {
            this.BloqueoCA = true
            this.BloqueoBCA = true
            this.BloqueoCodigoCA = true
            //this.CuentaAnticipo = ''
            this.cbCuentasContables = []
            this.Descripcion = ''
            this.ConsultaCuentaAnticipos()
            this.Actualiza = ''
            this.Nueva = ''
            this.CuentasNValidas = []
            this.Opcion = 5
        },
        NuevaCuentaCC() {
            this.CancelarCC()
            setTimeout(() => {
                bitacora.registrar(this.NuevaCat, {
                    Tipo: 'Inserción'
                })
            }, 2000)
            this.BloqueoCodigoCC = false
            this.Nueva = true
            this.BloqueoCC = false
            this.BloqueoBCC = false
            this.BloqueoHospital = false
            this.ConsultaCategorias()
        },
        NuevaCuentaCV() {
            this.CancelarCV()
            setTimeout(() => {
                bitacora.registrar(this.NuevaCat, {
                    Tipo: 'Inserción'
                })
            }, 2000)
            this.Nueva = true
            this.BloqueoCV = false
            this.BloqueoBCV = false
            this.BloqueoHospital = false
            this.ConsultaCategorias()
        },
        NuevaCuentaAnt() {
            this.CancelarCA()
            setTimeout(() => {
                bitacora.registrar(this.NuevaCat, {
                    Tipo: 'Inserción'
                })
            }, 2000)
            this.Nueva = true
            this.BloqueoCA = false
            this.BloqueoBCA = false
            this.BloqueoCodigoCA = false
            this.ConsultaCuentaContable()
            this.ConsultaMax()
        },
        Validar() {
            this.CuentasNValidas = []
            this.axios.post('/app/v1_contabilidad_general/ValidacionCuentasConf', {
                    ...this.cbBodega,
                    Opcion: 'C',
                    SubOpcion: '11',
                })
                .then((resp) => {
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        resp.data.json.map(data => {
                            this.CuentasNValidas.push({
                                ...data
                            })
                            this.CuentasNValidas = this.CuentasNValidas.filter(ab => ab.Cuenta != '');
                        })
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'Validar Cuentas, o no existen o no es correcta.',
                        })
                    }else{
                        this.GuardarCuentas()
                    }
                })

        },
        ValidarD() {
            this.CuentasNValidas = []
            this.axios.post('/app/v1_contabilidad_general/ValidacionCuentasConf', {
                    ...this.cbDepartamentos,
                    Opcion: 'C',
                    SubOpcion: '12',
                })
                .then((resp) => {
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        resp.data.json.map(data => {
                            this.CuentasNValidas.push({
                                ...data
                            })
                            this.CuentasNValidas = this.CuentasNValidas.filter(ab => ab.Cuenta != '');
                        })
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'Validar Cuentas, o no existen o no es correcta.',
                        })
                    }else{
                        this.GuardarCuentasD()
                    }
                })

        },
        ValidarCC() {
            this.CuentasNValidas = []
            this.axios.post('/app/v1_contabilidad_general/ValidacionCuentasConf', {
                    ...this.cbCategoriaC,
                    Opcion: 'C',
                    SubOpcion: '13',
                })
                .then((resp) => {
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        resp.data.json.map(data => {
                            this.CuentasNValidas.push({
                                ...data
                            })
                            this.CuentasNValidas = this.CuentasNValidas.filter(ab => ab.Cuenta != '');
                        })
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'Validar Cuentas, o no existen o no es correcta.',
                        })
                    }
                    else{
                        this.GuardarCuentasCC()
                    }
                })

        },
        ValidarCV() {
            this.CuentasNValidas = []
            this.axios.post('/app/v1_contabilidad_general/ValidacionCuentasConf', {
                    ...this.cbCategoriaCV,
                    Opcion: 'C',
                    SubOpcion: '14',
                })
                .then((resp) => {
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        resp.data.json.map(data => {
                            this.CuentasNValidas.push({
                                ...data
                            })
                            this.CuentasNValidas = this.CuentasNValidas.filter(ab => ab.Cuenta != '');
                        })
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'Validar Cuentas, o no existen o no es correcta.',
                        })
                    }
                    {
                        this.GuardarCuentasCV()
                    }
                })
        },

    }
}
</script>   

<style scoped>
.footer {
    height: 50px;
    margin-top: -65px;
    margin-left: 10px;
}

.label-size {
    font-size: 14px;
    font-weight: bold;
}

.padre {
    position: relative;
}

.hijo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
</style>
