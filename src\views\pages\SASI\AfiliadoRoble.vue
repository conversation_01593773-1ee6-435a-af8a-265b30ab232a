<template>
  <div class="consultas-roble-container">
    
    
      <vx-card title="Detalle de clientes Roble">
        <div id="EstadoPoliza">
          <DxDataGrid
            id="gdEstadoPoliza"
            :data-source="dsEstadoPoliza"
            v-bind="DefaultDxGridConfiguration"
            :height="'auto'"
            :width="'auto'"
            @selection-changed="onSelectionChangedGrid"
            :on-row-prepared="onRowPrepared"
            @cell-hover-changed="onCellHoverChanged"
          >
            <DxDataGridDxSearchPanel :visible="false" />
            <DxDataGridColumn data-field="NoPoliza" Caption="No Póliza" :width="100" />

            <DxDataGridColumn caption="Datos en Roble" :alignment="'center'">
              <DxDataGridColumn data-field="NombreAseguradoRoble" caption="Nombre" width="300" />
              <DxDataGridColumn data-field="Codigo_Parentesco" caption="Parentesco" />
              <DxDataGridColumn data-field="Estado_Poliza" caption="Estado póliza" />
              <DxDataGridColumn
                data-field="Estado_Asegurado"
                caption="Estado asegurado"
              />
              <DxDataGridColumn data-field="NombrePlanRoble" caption="Plan" width="230"/>
              <DxDataGridColumn data-field="FechaIngreso" caption="Fecha ingreso" />
              <DxDataGridColumn
                data-field="FechaNacimiento"
                caption="Fecha nacimiento"
              />
              <DxDataGridColumn data-field="CODCLI" caption="Código asegurado" />
            </DxDataGridColumn>

            <DxDataGridColumn caption="Datos en Sermesa" :alignment="'center'">
              <DxDataGridColumn data-field="NombreAseguradoSermesa" Caption="Nombre" width="300"/>
              <DxDataGridColumn data-field="Parentesco" caption="Parentesco" />
              <DxDataGridColumn data-field="status" caption="Status" />
              <DxDataGridColumn data-field="Idplan" caption="Cod plan" width="80"/>
            </DxDataGridColumn>
            <DxDataGridColumn caption="Validaciones" :alignment="'center'">
              <DxDataGridColumn data-field="ValidarExistencia" caption="Existencia" />
              <DxDataGridColumn
                data-field="ValidarStatusAsegurado"
                caption="Status asegurado"
              />
              <DxDataGridColumn data-field="ValidarStatus" caption="Status póliza" />
              <DxDataGridColumn data-field="ValidarParentesco" caption="Parentesco" />
              <DxDataGridColumn data-field="ValidarNombre" caption="Nombre" />
              <DxDataGridColumn data-field="ValidarPlan" caption="Plan" />
              <DxDataGridColumn data-field="Enviado" caption="Enviado" />
            </DxDataGridColumn>
          </DxDataGrid>
        </div>
      </vx-card>
    
   
    <dx-context-menu
      v-if="MostrarMenu"
      :data-source="menuItems"
      target="#gdEstadoPoliza"
      @itemClick="onMenuItemClick"
      itemRender="menuItemRender"
    />

    <DxPopup
      :width="660"
      :height="300"
      :visible.sync="active"
      :show-close-button="true"
      :show-title="true"
      title="Actualización de nombres"
    >
      <div class="flex flex-wrap">
        <div class="w-full sm:w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
          <label for="nombre">Nombre asegurado en Sermesa:</label>
          <vs-input
            class="w-full"
            v-model="this.NombreAseguradoSermesa"
            label-font-size="32px"
            label-color="blue"
            disabled
            id="Nombre1"
          />
        </div>
        <div class="w-full sm:w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
          <label for="nombre2">Nombre asegurado en Roble</label>
          <vs-input
            class="w-full"
            v-model="this.NombreAseguradoRoble"
            label-font-size="42px"
            label-color="blue"
            disabled
            id="nombre2"
          />
        </div>
        <span class="font-weight-bold">¿Seguro de actualizar el nombre?</span>
        <vs-divider />
        <div
          class="btn-group pl-4 pb-4 w-full sm:w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2"
        >
          <vs-button
            color="success"
            icon="fa-exclamation-circle"
            icon-pack="fas"
            class="w-full"
            title="Aceptar"
            @click="ActualizaPlanNombreStatus('Nombres')"
            >Si</vs-button
          >
        </div>

        <div
          class="btn-group pl-4 pb-4 w-full sm:w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2"
        >
          <vs-button
            color="success"
            icon="fa-exclamation-circle"
            icon-pack="fas"
            class="w-full"
            title="Aceptar"
            @click="active = false"
            >No</vs-button
          >
        </div>
      </div>
    </DxPopup>
  </div>
</template>
<script>

import { DefaultDxGridConfiguration } from "../EXPEDIENTE/data";

export default {
  components: {
  },
  props:{
    NumeroAdhesion: {type: String, },
    OpcionConsulta: {type: String, },
    NombresBusqueda: {type: String, },
    Apellido1Busqueda: {type: String, },
    Apellido2Busqueda: {type: String, },
    FechaNacimientoBusqueda: {type: String, },
  },
  data() {
    return {
      contextMenuVisible: false,
      contextMenuTarget: null,
      menuItems: [
        { text: "Actualizar nombres", icon: "card", disabled: false },
        { text: "Actualizar status", icon: "user", disabled: false },
        { text: "Actualizar plan", icon: "edit", disabled: false },
        { text: "Ver status póliza", icon: "info", disabled: false },
        {
          text: "Vincular código asegurado",
          icon: "parentfolder",
        },
        {
          text: "Consultar pagos",
          icon: "money",
        },
      ],
      NombreAseguradoSermesa: "",
      NombreAseguradoRoble: "",
      selectedRow: null,
      RegistroSeleccionado: null,
      dsEstadoPoliza: [],

      DefaultDxGridConfiguration: {
        ...DefaultDxGridConfiguration, 
        rowAlternationEnabled:false,//si se quiere habilitar validar que se visualicen correctemente los colores segun estado, nombre y existencia ver onRowprepared
      },
      
      active: false,
      MostrarMenu: false,
    };
  },
  methods: {
    onContextMenu(e) {
      const rowElement = e.target.closest(".dx-data-row");
      if (rowElement) {
        e.preventDefault();
        const rowIndex = rowElement.getAttribute("aria-rowindex") - 1;
        this.selectedRow = this.dataSource[rowIndex];
        this.contextMenuTarget = e.target;
        this.contextMenuVisible = true;
      } else {
        this.contextMenuVisible = false;
      }
    },
    onSelectionChanged(e) {
      this.selectedRow = e.selectedRowsData[0] || null;
    },
    onMenuItemClick(e) {
      if (e.itemData.text === "Actualizar nombres") {
        e.itemData.enabled = true;
        this.active = true;
      }

      if (e.itemData.text.includes("Ver status póliza")) {
        e.itemData.enabled = true;
        this.EstadoPoliza();
        this.MostrarMenu = false;
      }

      if (e.itemData.text === "Actualizar status") {
       
        this.ActualizaPlanNombreStatus("status");
        e.itemData.enabled = true;
        this.EstadoPoliza();
        this.MostrarMenu = false;
      }

      if (e.itemData.text === "Actualizar plan") {
        e.itemData.enabled = true;
        this.ActualizaPlanNombreStatus("Plan");
        e.itemData.enabled = true;
        this.EstadoPoliza();
        this.MostrarMenu = false;
      }

      if (e.itemData.text === "Consultar pagos") {
        this.MostrarMenu = false;
        this.$emit('pagos', this.selectedRow)
      }

      if (e.itemData.text === "Vincular código asegurado") {
        this.VincularAsegurado()
      }

      this.contextMenuVisible = false;
    },
    menuItemRender(itemData, _, element) {
      const menuItemElement = document.createElement("div");
      menuItemElement.className = "dx-menu-item-content";
      menuItemElement.textContent = itemData.text;

      if (this.selectedMenuItem === itemData.text) {
        menuItemElement.classList.add("selected-menu-item");
      }
      element.appendChild(menuItemElement);
    },

    sinregistros() {
      this.$vs.notify({
        time: 4000,
        title: "No se encontro información",
        text: "No se encontro información con los datos proporcionados",
        iconPack: "feather",
        icon: "icon-alert-circle",
        color: "warning",
        position: "top-center",
      });
    },
    

    //Estado poliza
    EstadoPoliza(adhesion) {
      this.dsEstadoPoliza = [];
      let Adhesion = adhesion ?? this.RegistroSeleccionado?.NoPoliza
      if (!Adhesion) {
        return;
      } else {
        this.axios
          .post("/app/v1_afiliados/EstadoPoliza", {
            NumeroAdhesion: Adhesion
          })
          .then((resp) => {
            this.dsEstadoPoliza = resp.data.json.map((x, index) => {
              return {
                id: index,
                Respuesta: x.respuesta,
                TextoRespuesta: x.Textorespuesta,
                NoPoliza: x.NoPoliza,
                Producto: x.Producto,
                FechaIngreso: x.FechaIngreso,

                Estado_Poliza: x.Estado_Poliza,
                Estado_Asegurado: x.Estado_Asegurado,
                Nombres: x.Nombres,
                Primer_Apellido: x.Primer_Apellido,
                Segundo_Apellido: x.Segundo_Apellido,
                Apellido_Casada: x.Apellido_Casada,

                FechaNacimiento: x.FechaNacimiento,
                Codigo_Plan: x.Codigo_Plan,
                Revision_Plan: x.Revision_Plan,
                NombrePlanRoble: x.NombrePlanRoble,
                Plan_Sermesa: x.Plan_Sermesa,
                Codigo_Parentesco: x.Codigo_Parentesco,

                Enviado: x.Enviado,
                CodMotAnul: x.CodMotAnul,
                MotAnul: x.MotAnul,
                Idplan: x.Idplan,

                Parentesco: x.Parentesco,
                CodigoAsegurado: x.CodigoAsegurado,
                status: x.status,
                StatusPolizaRoble: x.StatusPolizaRoble,
                NombreAseguradoSermesa: x.NombreAseguradoSermesa,
                NombreAseguradoRoble: x.NombreAseguradoRoble,

                StatusAseguradoRoble: x.StatusAseguradoRoble,
                NombreStatus: x.NombreStatus,
                ValidarNombre: x.ValidarNombre,
                ValidarStatus: x.ValidarStatus,

                ValidarStatusAsegurado: x.ValidarStatusAsegurado,
                ValidarExistencia: x.ValidarExistencia,
                ValidarParentesco: x.ValidarParentesco,
                ValidarPlan: x.ValidarPlan,
                CODCLI: x.CODCLI,
              };
            });

            if (this.dsEstadoPoliza[0].Respuesta === "0") {
              this.sinregistros();
            }
            else {
              this.$emit('selected-poliza', this.dsEstadoPoliza)
            }
          });
      }
    },

    onRowPrepared(e) {

      if (e.rowType === "data" && e.data.ValidarParentesco === "FALSE") {
        e.rowElement.style.backgroundColor = "#fff0c9";
      }
      if (e.rowType === "data" && e.data.ValidarExistencia === "FALSE") {
        e.rowElement.style.backgroundColor = "#FF0000";
      }

      if (e.rowType === "data" && e.data.ValidarNombre === "FALSE") {
        e.rowElement.style.backgroundColor = "#00a33d";
      }
      if (e.rowType === "data" && e.data.ValidarPlan === "FALSE") {
        e.rowElement.style.backgroundColor = "#00a33d";
     
      }
      if (e.rowType === "data" && e.data.ValidarStatus === "FALSE") {
        e.rowElement.style.backgroundColor = "#00a33d";
  
      }
    },

    onSelectionChangedGrid(selectedItems) {
      this.MostrarMenu = false;
      if (selectedItems.selectedRowsData.length > 0) {
        this.MostrarMenu = true;
        const primerRegistro = selectedItems.selectedRowsData[0];
        this.NombreAseguradoSermesa = primerRegistro.NombreAseguradoSermesa;
        this.NombreAseguradoRoble = primerRegistro.NombreAseguradoRoble;
        if (primerRegistro.ValidarNombre === "FALSE") {
          this.menuItems[0].disabled = false;
        } else {
          this.menuItems[0].disabled = true;
        }
        this.$emit('selected-row', selectedItems.selectedRowsData)
      }
    },

    onCellHoverChanged(e) {
      // Lógica cuando el usuario pasa el cursor sobre una celda

      if (e.row && e.row.data) {
        this.MostrarMenu = false;
        const row = e.row.data;
        this.RegistroSeleccionado = row;
        this.NombreAseguradoSermesa = row.NombreAseguradoSermesa;
        this.NombreAseguradoRoble = row.NombreAseguradoRoble;
        this.MostrarMenu = true;
        if (row.ValidarNombre === "FALSE") {
          this.menuItems[0].disabled = false;
        } else {
          this.menuItems[0].disabled = true;
        }

        if (row.ValidarStatus === "FALSE") {
          this.menuItems[1].disabled = false;
        } else {
          this.menuItems[1].disabled = true;
        }

        if (row.ValidarPlan === "FALSE") {
          this.menuItems[2].disabled = false;
        } else {
          this.menuItems[2].disabled = true;
        }

        if (row.ValidarExistencia === "FALSE") {
          this.menuItems[4].disabled = false;
        } else {
          this.menuItems[4].disabled = true;
        }
        this.menuItems[3].text = `Ver status póliza (${row.NoPoliza})`
      }
    },
    ActualizaPlanNombreStatus(opcionAct) {
     
      this.axios
        .post("/app/v1_afiliados/ActualizarPlanNombreStatus", {
          Nombres: this.RegistroSeleccionado.Nombres,
          Apellido1: this.RegistroSeleccionado.Primer_Apellido,
          Apellido2: this.RegistroSeleccionado.Segundo_Apellido,
          ApellidoCasada: this.RegistroSeleccionado.Apellido_Casada,
          NoPoliza: this.RegistroSeleccionado.NoPoliza,
          CodigoAsegurado: this.RegistroSeleccionado.CodigoAsegurado,
          PlanActualiza: this.RegistroSeleccionado.Plan_Sermesa,
          StatusActualiza: this.RegistroSeleccionado.StatusPolizaRoble,
          Opcion: opcionAct,
        })
        .then((resp) => {
          if (resp.data.codigo == 0) {
            this.active = false;
            this.$vs.notify({
              time: 4000,
              title: "Actualización nombres",
              text: resp.data.json[0].Respuesta,
              iconPack: "feather",
              icon: "icon-alert-circle",
              color: "warning",
              position: "top-center",
            });
            this.EstadoPoliza();
          }
        });
    },

    VincularAsegurado() {
      this.axios
        .post("/app/v1_afiliados/VincularAsegurado", {
          NombreCompleto: this.RegistroSeleccionado.NombreAseguradoRoble,
          FechaNacimiento: this.RegistroSeleccionado.FechaNacimiento,
          NoPoliza: this.RegistroSeleccionado.NoPoliza,
          CodigoAsegurado: this.RegistroSeleccionado.CODCLI,
        })
        .then((resp) => {
          if (resp.data.codigo == 0) {
            this.active = false;
            this.$vs.notify({
              time: 4000,
              title: "Actualización registrada",
              text: resp.data.json[0].respuesta,
              iconPack: "feather",
              icon: "icon-alert-circle",
              color: "warning",
              position: "top-center",
            });
            this.EstadoPoliza();
          }
        });
    },

        //consulta por nombres
        ConsultarAfiliadosxNombres() {
          this.RegistroSeleccionado = null
      this.dsEstadoPoliza = [];
      if (!this.NombresBusqueda && !this.Apellido1) {
        return;
      } else {
        this.axios
          .post("/app/v1_Afiliados/ConsultaPolizaNombres", {
            NombreCompleto:this.NombresBusqueda,
            Apellido1:this.Apellido1Busqueda,
            Apellido2:this.Apellido2Busqueda,
            FechaNacimiento:this.FechaNacimientoBusqueda,
          })
          .then((resp) => {
            this.dsEstadoPoliza = resp.data.json.map((x, index) => {
              return {
                id: index,
                Respuesta: x.RESPUESTA,
                TextoRespuesta: x.TEXTORESPUESTA,
                NoPoliza: x.POLIZA,
                Producto: x.PRODUCTO,
                FechaIngreso: x.FECHA_INGRESO,

                Estado_Poliza: x.ESTADO_POLIZA,
                Estado_Asegurado: x.ESTADO_ASEGURADO,
                Nombres: x.NOMBRES,
                Primer_Apellido: x.PRIMER_APELLIDO,
                Segundo_Apellido: x.SEGUNDO_APELLIDO,
                Apellido_Casada: x.APELLIDO_CASADA,

                FechaNacimiento: x.FECHA_NACIMIENTO,
                Codigo_Plan: x.CODIGO_PLAN,
                Revision_Plan: x.REVISION_PLAN,
                NombrePlanRoble: x.DESCRIPCION_PLAN,
                Plan_Sermesa: x.PLAN_SERMESA,
                Codigo_Parentesco: x.CODIGO_PARENTESCO,

                Enviado: x.ENVIO_SERMESA,
                CodMotAnul: 'Consultar status',
                MotAnul: 'ConsultarStatus',
                Idplan: x.Idplan,

                Parentesco: x.Parentesco,
                CodigoAsegurado: x.CODCLI,
                status: x.status,
                StatusPolizaRoble: x.ESTADO_POLIZA,
                NombreAseguradoSermesa: x.NombreAseguradoSermesa,
                NombreAseguradoRoble: x.NombreAseguradoRoble,

                StatusAseguradoRoble: x.StatusRoble,
                NombreStatus: x.NombreStatus,
                ValidarNombre: x.ValidarNombre,
                ValidarStatus: x.ValidarStatus,

                ValidarStatusAsegurado: x.ValidarStatus,
                ValidarExistencia: x.ValidarExistencia,
                ValidarParentesco: x.ValidarParentesco,
                ValidarPlan: x.ValidarPlan,
                CODCLI: x.CODCLI,
              };
            });

            if (this.dsEstadoPoliza[0].Respuesta === "0") {
              this.sinregistros();
            }
          });
      }
    },
    Limpiar(){
      this.dsEstadoPoliza = []
    }
  },
  mounted() {
    // if (this.OpcionConsulta==="Poliza"){this.EstadoPoliza();}
    // if (this.OpcionConsulta==="Nombres"){this.ConsultarAfiliadosxNombres();}
    
  },
};
</script>
<style>
.dx-context-menu {
  z-index: 1000;
}
.dx-item-content .dx-menu-item-content {
  color: rgb(13, 107, 161) !important;
}
.dx-menu-item-content span {
  margin-right: 5px;
  color: rgb(10, 78, 124) !important;
}

.selected-menu-item {
  color: rgba(31, 185, 17, 0.623) !important;
}

.dx-menu-item {
  background-color: aliceblue;
  /* Cambia este color según tus preferencias */
}

.dx-menu-item.dx-state-focused {
  background-color: rgb(
    38,
    7,
    121
  ); /* Cambia este color según tus preferencias */
}

.dx-item-dx-menu-item-dx-menu-item-has-text-dx-menu-item-has-icon {
  background-color: rgb(38, 7, 121);
}
.dx-menu-base .dx-menu-items-container {
  background-color: rgb(92, 184, 92);
}

.dx-datagrid-rowsview
  .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed)
  > td {
  background-color: #00ccff !important;
  color: black !important;
}

.dx-datagrid-rowsview .dx-row.dx-data-row.dx-column-lines.dx-state-hover > td {
  background-color: #79e4ff !important;
  color: black !important;
  /* font-size: 16px; */
}

.dx-datagrid-headers {
  background-color: aliceblue !important;
  color: black !important;
  font-weight: bold;
}
</style>
