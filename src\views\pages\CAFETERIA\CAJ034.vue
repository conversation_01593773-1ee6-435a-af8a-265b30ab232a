<template>
    <div>
       <div class="flex flex-wrap">
       <vx-card title="Consulta de Cargos Devueltos">
            <vs-divider></vs-divider>
            <div class="box-custom">
                <vs-row vs-type="flex" vs-w="12">
                    <!-- <vs-col vs-w="2"> -->
                        <div class ="div-input flex-wrap-nowrap">
                            <label class="label-static">Número de Anulación</label>
                            <vs-input type="number" class="w-full input-correlativo" v-model="correlativo" ></vs-input>
                        </div>
                    <!-- </vs-col> -->
                </vs-row>
                <vs-row vs-type="flex" vs-w="12">
                    <div class="div-input flex-wrap-nowrap">
                        <label class="label-static">Tipo</label>
                        <vs-input class="w-full input-tipo" ref="tipo" v-model="tipoOrden" v-on:blur="BuscarTipoOrden()" ></vs-input>
                    </div>
                    <div class="button_search flex-wrap-nowrap">
                        <div class="button_search flex-wrap-nowrap">
                            <input class="w-full label-info" v-model="nombreTipo" disabled/>
                        </div>
                    </div>
                </vs-row>
                <vs-row vs-type="flex" vs-w="12">
                    <div class="div-input flex-wrap-nowrap">
                        <label class="label-static">Número</label>
                        <vs-input type="number" class="w-full input-numero" ref="tipo" v-model="numeroOrden"></vs-input>
                    </div>
                    <div class="button_search flex-wrap-nowrap">
                        <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="BuscarOrden()" icon="icon-search">Buscar</vs-button>
                    </div>
                </vs-row>
                <vs-row vs-justify="left" vs-w="12">
                    <div class="div-input flex-wrap-nowrap">
                        <label class="label-static">Admisión</label>
                        <input class="w-full label-info" v-model="admision" disabled/>
                    </div>
                </vs-row>
                <vs-row vs-justify="left" vs-w="12">
                    <div class="div-input flex-wrap-nowrap">
                        <label class="label-static">Fecha de Anulación</label>
                        <input class="w-full label-info" v-model="fecha" disabled/>
                    </div>
                </vs-row>
                <br>
                <vx-card title="Detalle">
                    <div class="flex flex-wrap">
                        <div class="w-full  p-2">
                            <vs-table2 max-items="10" search pagination :data="devoluciones" class="mt-0 mb-0">
                                <template slot="thead">
                                    <th order="Correlativo" width="50px">Correlativo</th>
                                    <th order="Fecha" width="50px">Fecha</th>
                                    <th order="TipoOrden" width="100px">Tipo Orden</th>
                                    <th order="Orden" width="50px">Orden</th>
                                    <th order="Producto" width="250px">Producto</th>
                                    <th order="Cantidad"  width="80px">Cantidad</th>
                                    <th order="Valor" width="150px">Venta Total</th>
                                    <th order="Costo" width="150px">Costo Unitario Bod</th>
                                    <th order="CostoTotalBod" width="150px">Costo Total Bod</th>
                                    <th order="Usuario" width="150px">Usuario</th>
                                </template>
                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td2>{{ tr.Correlativo }}</vs-td2>
                                        <vs-td2>{{tr.Fecha}}</vs-td2>
                                        <vs-td2>{{tr.TipoOrden}}</vs-td2>
                                        <vs-td2>{{tr.Orden}}</vs-td2>
                                        <vs-td2>{{tr.Producto}}</vs-td2>
                                        <vs-td2>{{tr.Cantidad}}</vs-td2>
                                        <vs-td2>{{tr.Valor}}</vs-td2>
                                        <vs-td2>{{tr.Costo}}</vs-td2>
                                        <vs-td2>{{tr.CostoTotalBod}}</vs-td2>
                                        <vs-td2>{{tr.Usuario}}</vs-td2>
                                    </tr>
                                </template>
                            </vs-table2>
                        </div>
                    </div> 
                </vx-card>
                <vx-card title="Poliza de Costo">
                    <div class="flex flex-wrap">
                        <h3>
                            Nota: Modulo con datos a partir de Mayo 2013
                        </h3>
                        <div class="w-full  p-2">
                            <vs-table2 max-items="10" search pagination :data="inventarios" class="mt-0 mb-0">
                                <template slot="thead">
                                    <th order="Empresa" width="50px">Empresa</th>
                                    <th order="Tipo" width="50px">Tipo</th>
                                    <th order="SerieDocumento" width="50px">Serie Documento</th>
                                    <th order="Documento" width="80px">Documento</th>
                                    <th order="LineaDevolución" width="10px">Linea Dev.</th>
                                    <th order="LineaInventario" width="10px">Linea Inv.</th>
                                    <th order="Categoria" width="50px">Categoría</th>
                                    <th order="Costo" width="100px">Costo</th>
                                    <th order="Debe" width="150px">Debe</th>
                                    <th order="Haber" width="150px">Haber</th>
                                    <th order="Fecha" width="300px">Fecha</th>
                                    <th order="PeriodoPartida" width="150px">Periodo Partida</th>
                                    <th order="Partida" width="50px">Partida</th>
                                    <th order="SerieFactura" width="50px">Serie Factura</th>
                                    <th order="Factura" width="150px">Factura</th>
                                </template>
                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td2>{{tr.Empresa}}</vs-td2>
                                        <vs-td2>{{tr.Tipo}}</vs-td2>
                                        <vs-td2>{{tr.SerieDocumento}}</vs-td2>
                                        <vs-td2>{{tr.Documento}}</vs-td2>
                                        <vs-td2>{{tr.LineaDevolución}}</vs-td2>
                                        <vs-td2>{{tr.LineaInventario}}</vs-td2>
                                        <vs-td2>{{tr.Categoria}}</vs-td2>
                                        <vs-td2>{{tr.Costo}}</vs-td2>
                                        <vs-td2>{{tr.Debe}}</vs-td2>
                                        <vs-td2>{{tr.Haber}}</vs-td2>
                                        <vs-td2>{{tr.Fecha}}</vs-td2>
                                        <vs-td2>{{tr.PeriodoPartida}}</vs-td2>
                                        <vs-td2>{{tr.Partida}}</vs-td2>
                                        <vs-td2>{{tr.SerieFactura}}</vs-td2>
                                        <vs-td2>{{tr.Factura}}</vs-td2>
                                    </tr>
                                </template>
                        </vs-table2>
                        </div>
                    </div>
                </vx-card>
        </div>
        </vx-card>
    </div>
</div>
</template>
<script>
export default{
    data(){
        return{
            correlativo: '', 
            tipoOrden: '',
            nombreTipo: '',
            numeroOrden: '',
            admision: '',
            fecha: '',
            devoluciones: [],
            inventarios: [],
            correlativoDev: '',
            tipoDev: '',
            numeroDev: ''
            
        }
    },
    methods:{
        BuscarTipoOrden(){
            if(this.tipoOrden != ''){
                this.axios.post('/app/v1_JefeCaja/CargarTipo',{
                   Tipo: this.tipoOrden
                })
                .then(resp => {
                    if (resp.data.codigo == 0 ){
                        this.nombreTipo = resp.data.NombreTipo;
                    }
                    else {
                        this.nombreTipo =''
                    }
                })
            }
        },
        BuscarOrden(){
            if(this.correlativo == '' && this.tipoOrden == '' && this.numeroOrden == ''){
                this.$vs.notify({
                        color: 'danger',
                        text: 'No coloco ningún dato para búsqueda',
                        position: 'bottom-center'
                })
            }
            else{
                this.axios.post('/app/v1_JefeCaja/CargarDevCargos',{
                    Correlativo: this.correlativo,
                    Tipo: this.tipoOrden,
                    Orden: this.numeroOrden
                })
                .then(resp => {
                    if(!this.isEmptyObject(resp.data.json)){
                        this.devoluciones = resp.data.json.map(m => {
                            return{
                                ...m,
                                Valor: parseFloat(m.Cantidad).toFixed(2),
                                Costo: parseFloat(m.Costo).toFixed(2),
                                CostoTotalBod: parseFloat(m.CostoTotalBod).toFixed(2),
                            }
                        })
                        this.admision = resp.data.json[0].SerieAdmision + ' ' + resp.data.json[0].Admision
                        this.fecha = resp.data.json[0].Fecha
                    
                        this.correlativoDev = resp.data.json[0].Correlativo
                        this.tipoDev = resp.data.json[0].TipoOrden
                        this.numeroDev = resp.data.json[0].Orden
                        this.BuscarInventario()
                    }
                    else{
                        this.devoluciones = ''
                    }
                })
            }
        },
        BuscarInventario (){
            this.axios.post('/app/v1_JefeCaja/CargarInventarios',{
                Correlativo: this.correlativo,
                Tipo: this.tipoDev,
                Orden: this.numeroDev
            })
                .then(resp => {
                    if(!this.isEmptyObject(resp.data.json)){
                        this.inventarios = resp.data.json.map(m => {
                            return{
                                ...m,
                                Costo: parseFloat(m.Costo).toFixed(2),
                                Debe: parseFloat(m.Debe).toFixed(2),
                                Haber: parseFloat(m.Haber).toFixed(2),
                            }
                        })
                    }
                    else{
                        this.inventarios = ''
                    }
                })
        },
        isEmptyObject(obj){
            return Object.keys(obj).length === 0;
        },
    }
}

</script>
<style lang="scss" scoped>
h3{
        flex-basis:100%;
        text-align: right;
        color:#E74C3C;
    }
    .box-custom{
        box-shadow: 0px 15px 40px -10px;
        padding: 15px;

        .button_search{
            padding-top: 35px;
        }
    }
    .div-input{
        padding: 10px;
        .input-serie{
            width: 75px;
        }
        .input-numero{
            width: 200px;
        }
        .textEstado{
            color: #fc0909;
            font-weight: bold;
            font-size: 25px;
        }

    }
    .label-info{
        margin-left: 10px;
        color:#E74C3C;
        opacity: 0.7;
        border: 0;
        font-size: 16px;
        background-color: white;
    }
    .label-info2{
        margin-left: 10px;
        padding-top: 25px;
        color:#E74C3C;
        opacity: 0.7;
        border: 0;
        font-size: 16px;
        background-color: white;
    }
    .label-static{
        font-weight: bold;
        font-size: 16px;
    }
    .label-static1{
        font-weight: bold;
        font-size: 24px;
        text-align: center;
    }
</style>