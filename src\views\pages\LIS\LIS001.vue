<template>
    <vx-card type = "2" title="Definición de Pruebas" class="justify-center ">

        <buscador ref="buscar_examen" buscador_titulo="Buscador / Exámenes" :api="'app/laboratorio/BuscarExamenLab'" 
        :campos="['codigoExamen','nombreExamen']" :titulos="['Código#','Nombre']" 
        :multiselect="false" :api_validar_campo="true" :api_preload="false" 
        />
        <!--Para editar o agreagar pruebas al examen-->
        <vs-popup id="datosPrueba" :title = this.tituloModalNewEdit  :active.sync="dialogPrueba" style="z-index:99998;">
        <ValidationObserver ref="formValidate" v-slot="{ invalid,handleSubmit }" mode="lazy">
            <form method="post" @submit.prevent="handleSubmit(savePrueba())">
                <div class="flex flex-wrap">
                    <vs-row vs-justify="center">
                        <div class="flex flex-wrap w-full md:w-full lg:w-1/2 xl:w-1/2">
                            <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                                <ValidationProvider rules="between:1,50|required|numero_entero" v-slot="{ errors }" class="required">
                                    <vs-input class="w-full" ref="uiLinea" label="Línea"  type="number" v-model="prueba.linea" @blur="validarLinea()" @focusin="mostarAyuda(0)" @focusout="mostarAyuda(-1)" 
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" required/>
                                </ValidationProvider>
                            </div>

                            <div class="w-full md:w-5/6 lg:w-5/6 xl:w-5/6 pr-1 p-1" style="min-width: 50% + 52px;">
                                <ValidationProvider rules="max:60|required" v-slot="{ errors }" class="required">
                                    <vs-input class="w-full" label="Prueba"  type="text" v-model="prueba.nombrePrueba" @focusin="mostarAyuda(3)" @focusout="mostarAyuda(-1)"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" required />
                                </ValidationProvider>
                            </div>
                        </div>   
                        
                        <div class="flex flex-wrap w-full md:w-full lg:w-1/2 xl:w-1/2 p-1">
                            <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                                <ValidationProvider rules="max:15" v-slot="{ errors }" class="required">
                                    <vs-input class="w-full" label="Unidad de Medida"  type="text" v-model="prueba.umedida" :on-change="verificarMedida()" @focusin="mostarAyuda(2)" @focusout="mostarAyuda(-1)"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" required/>
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5 pr-1 pl-1">
                                <vs-select class="w-full" filter  label = " " placeholder="Catálogo Unidad de medida" v-model="prueba.umedida" :required = "true">
                                    <vs-select-item :key="index" :value="item.abrev" :text="'(' + item.abrev + ') ' +item.unidad" v-for="(item,index) in unidadesMedida" />
                                </vs-select>
                            </div>
                        </div>
                    </vs-row>
                    <div class="w-full lg:w-1/3 md:w-1/2 sm:w-full p-1">
                        <ValidationProvider rules="max:15" v-slot="{ errors }">
                            <vs-input label="Resultado Típico" type="text" class="w-full" v-model="prueba.resTipico" @focusin="mostarAyuda(4)" @focusout="mostarAyuda(-1)"
                            :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full lg:w-1/3 md:w-1/2 sm:w-full p-1">
                        <ValidationProvider rules="max:40" v-slot="{ errors }">
                            <vs-input label="Valor Normal" class="w-full" type="text" v-model="prueba.vNormal" @focusin="mostarAyuda(5)" @focusout="mostarAyuda(-1)"
                            :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full lg:w-1/3 md:w-1/2 sm:w-full p-1">
                        <ValidationProvider rules="max:40" v-slot="{ errors }">
                            <vs-input label="Valor Normal Recién Nacido" class="w-full" type="text" v-model="prueba.vNormalRn" @focusin="mostarAyuda(6)" @focusout="mostarAyuda(-1)"
                            :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full lg:w-1/3 md:w-1/2 sm:w-full p-1">
                        <ValidationProvider rules="max:40" v-slot="{ errors }">
                            <vs-input label="Valor Normal Menor de Edad" class="w-full" type="text" v-model="prueba.vNormalMenor" @focusin="mostarAyuda(7)" @focusout="mostarAyuda(-1)"     
                            :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full lg:w-1/3 md:w-1/2 sm:w-full p-1">
                        <ValidationProvider rules="max:40" v-slot="{ errors }">
                            <vs-input label="Valor Normal Masculino" class="w-full" type="text" v-model="prueba.vNormalMasc" @focusin="mostarAyuda(9)" @focusout="mostarAyuda(-1)" 
                            :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full lg:w-1/3 md:w-1/2 sm:w-full p-1">
                        <ValidationProvider rules="max:40" v-slot="{ errors }">
                            <vs-input label="Valor Normal Femenino" class="w-full" type="text" v-model="prueba.vNormalFem" @focusin="mostarAyuda(8)" @focusout="mostarAyuda(-1)"
                            :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full lg:w-1/3 md:w-1/2 sm:w-full p-1">
                        <ValidationProvider rules="max:80" v-slot="{ errors }">
                            <vs-input label="Valor Normal No. 1" class="w-full" type="text" v-model="prueba.vNormal1" @focusin="mostarAyuda(10)" @focusout="mostarAyuda(-1)"
                            :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full lg:w-1/3 md:w-1/2 sm:w-full p-1">
                        <ValidationProvider rules="max:80" v-slot="{ errors }">
                            <vs-input label="Valor Normal No. 2" class="w-full" type="text" v-model="prueba.vNormal2" @focusin="mostarAyuda(10)" @focusout="mostarAyuda(-1)"
                            :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full lg:w-1/3 md:w-1/2 sm:w-full p-1">
                        <ValidationProvider rules="max:80" v-slot="{ errors }">
                            <vs-input label="Valor Normal No. 3" class="w-full" type="text" v-model="prueba.vNormal3" @focusin="mostarAyuda(10)" @focusout="mostarAyuda(-1)"
                            :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full lg:w-1/3 md:w-1/2 sm:w-full p-1">
                        <ValidationProvider rules="max:80" v-slot="{ errors }">
                            <vs-input label="Valor Normal No. 4" class="w-full" type="text" v-model="prueba.vNormal4" @focusin="mostarAyuda(10)" @focusout="mostarAyuda(-1)"
                            :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full lg:w-1/3 md:w-1/2 sm:w-full p-1">
                        <ValidationProvider rules="max:80" v-slot="{ errors }">
                            <vs-input label="Valor Normal No. 5" class="w-full" type="text" v-model="prueba.vNormal5" @focusin="mostarAyuda(10)" @focusout="mostarAyuda(-1)"
                            :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full lg:w-1/3 md:w-1/2 sm:w-full p-1">
                        <ValidationProvider rules="max:80" v-slot="{ errors }">
                            <vs-input label="Valor Normal No. 6" class="w-full" type="text" v-model="prueba.vNormal6" @focusin="mostarAyuda(10)" @focusout="mostarAyuda(-1)"
                            :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full lg:w-1/3 md:w-1/2 sm:w-full p-1">
                        <ValidationProvider rules="max:80" v-slot="{ errors }">
                            <vs-input label="Valor Normal No. 7" class="w-full" type="text" v-model="prueba.vNormal7" @focusin="mostarAyuda(10)" @focusout="mostarAyuda(-1)"
                            :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full lg:w-1/3 md:w-1/2 sm:w-full p-1">
                        <ValidationProvider rules="max:80" v-slot="{ errors }">
                            <vs-input label="Valor Normal No. 8" class="w-full" type="text" v-model="prueba.vNormal8" @focusin="mostarAyuda(10)" @focusout="mostarAyuda(-1)"
                            :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full lg:w-1/3 md:w-1/2 sm:w-full p-1">
                        <ValidationProvider rules="max:80" v-slot="{ errors }">
                            <vs-input label="Valor Normal No. 9" class="w-full" type="text" v-model="prueba.vNormal9" @focusin="mostarAyuda(10)" @focusout="mostarAyuda(-1)"
                            :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full lg:w-1/3 md:w-1/2 sm:w-full p-1">
                        <ValidationProvider rules="max:80" v-slot="{ errors }">
                            <vs-input label="Valor Normal No. 10" class="w-full" type="text" v-model="prueba.vNormal10" @focusin="mostarAyuda(10)" @focusout="mostarAyuda(-1)"
                            :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                        </ValidationProvider>
                    </div>

                    
                    <div class="w-full lg:w-1/3 md:w-1/2 sm:w-full p-1">
                        <ValidationProvider rules="max:50" v-slot="{ errors }">
                            <vs-input label="Mínimo Alerta" class="w-full" type="number" v-model="prueba.MinimoAlerta" @focusin="mostarAyuda(11)" @focusout="mostarAyuda(-1)"
                            :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full lg:w-1/3 md:w-1/2 sm:w-full p-1">
                        <ValidationProvider rules="max:50" v-slot="{ errors }">
                            <vs-input label="Máximo Alerta" class="w-full" type="number" v-model="prueba.MaximoAlerta" @focusin="mostarAyuda(11)" @focusout="mostarAyuda(-1)"
                            :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                        </ValidationProvider>
                    </div>

      
                    <div class="w-full p-1">
                        <label>Se Imprime Siempre</label>
                        <vs-switch color="success" v-model="prueba.imprimeSiempre" @focusin="mostarAyuda(1)" @focusout="mostarAyuda(-1)"> 
                            <span slot="on">SI</span>
                            <span slot="off">NO</span>
                        </vs-switch>
                    </div>
                </div>
                <vs-divider></vs-divider>
                <div tyle="text-align:right;margin:5px 0;padding:5px ">
                    <vs-button @click="savePrueba()" color="success" icon-pack="fa" icon="fa-save" style="float:right" :disabled="invalid">Guardar</vs-button>
                    <vs-button @click="dialogPrueba = false" color="danger" icon-pack="fa" icon="fa-ban" style="float:right" class="mr-2">Cancelar</vs-button>
                </div>
            </form>
        </ValidationObserver>
        </vs-popup>
        <!--Para editar o agreagar pruebas al examen-->
        <vs-divider>Búsqueda de Exámenes:</vs-divider>
            <div class="flex flex-wrap" style="max-width: 720px;"> 
                <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                    <vs-input label="Código" class="w-full" placeholder="exámen" v-on:change="cargarExamen()" v-model="examen.codigo" :disabled="bloqueoBusqueda"/>
                </div>
                <div class="w-full md:w-5/6 lg:w-5/6 xl:w-5/6 p-1">
                    <label class="vs-input--label">Descripción</label>
                    <vx-input-group class="">
                        <vs-input v-model="examen.nombre" placeholder="Ingrese una descripción" v-on:click="buscarExamen()" :disabled="bloqueoBusqueda"/>
                        <template slot="append">
                            <div class="append-text btn-addon">
                                <vs-button id="button-with-loading-1" color="primary" icon-pack="feather" @click="buscarExamen()" icon="icon-search" v-if="bloqueoBusqueda==false"></vs-button>
                                <vs-button id="button-with-loading-2" color="danger" icon-pack="feather" @click="limpiarDatos()" icon="icon-x" v-else></vs-button>
                            </div>
                        </template>
                    </vx-input-group>
                </div>
            </div>       
        <vs-textarea label="Observaciones" class="w-full" counter="500" v-model = "examen.observaciones" v-if = "examen.empresa != null" @change="actualizarTexto()"/>
        <div class="w-full" v-if = "examen.empresa != null">
            <label>Envio correo / SMS: </label>
            <vs-switch color="success" v-model = "examen.bloqueoCorreo"  @input ="actualizarBloqueoCorreo()"> 
                <span slot="on">SI</span>
                <span slot="off">NO</span>
            </vs-switch>
        </div>
        <vs-divider v-if = "examen.empresa != null">Definición de Pruebas:</vs-divider>
        <vs-button color="success" icon-pack="feather" @click="agregarPrueba()" icon="icon-plus-circle" v-if="examen.empresa != null" style="float:inline-block">Agregar Prueba</vs-button>
        <vs-table v-if = "examen.empresa != null" :data="this.examen.pruebas" noDataText="Este exámen no tiene pruebas registradas">
            <template slot="thead">
                    <vs-th>Línea</vs-th>
                    <vs-th>Nombre</vs-th>
                    <vs-th>U.Medida</vs-th>
                    <vs-th>Opciones</vs-th>
            </template>
            <template slot-scope="{data}">
                <vs-tr :key="indextr" v-for="(tr, indextr) in data">
                    <vs-td :data="tr.linea" width="5px">{{tr.linea}}</vs-td>
                    <vs-td :data="tr.nombrePrueba" width="250px">{{tr.nombrePrueba}}</vs-td>
                    <vs-td :data="tr.umedida" width="150px">{{tr.umedida}}</vs-td>

                    <vs-td width="100px">
                        <vs-row>
                            <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" @click="editarPrueba(tr)"></vs-button>
                            <vs-button color="danger" icon-pack="feather" icon="icon-trash-2" style="display:inline-block;margin-right:2px" @click="borrarPrueba(tr)"></vs-button>
                        </vs-row>
                    </vs-td>

                </vs-tr>
            </template>
        </vs-table>
    </vx-card>
</template>
<script>
    export default {
        data() {
            return {
                examen: {
                    empresa:null,
                    codigo:null,
                    nombre:null,
                    descripcion:null,
                    pruebas:[],
                    observaciones: null,
                    bloqueoCorreo: false
                },
                prueba: {
                    emp:null,
                    codigo:null,
                    linea:null,
                    nombre:null,
                    umedida:null,
                    resTipico:null,
                    vNormal:null,
                    vNormalRn:null,
                    vNormalMenor:null,
                    vNormalMasc:null,
                    vNormalFem:null,
                    vNormal1:null,
                    vNormal2:null,
                    vNormal3:null,
                    vNormal4:null,
                    vNormal5:null,
                    vNormal6:null,
                    vNormal7:null,
                    vNormal8:null,
                    vNormal9:null,
                    vNormal10:null,
                    imprimeSiempre:false,
                    lineaanterior: null,
                },
                bloqueoBusqueda: false,
                editMode: false,
                dialogPrueba: false,
                tituloModalNewEdit: null,
                unidadesMedida: [
                    {unidad: "Fentolitros", abrev:"fL"} ,
                    {unidad: "Gramos", abrev:"g"} ,
                    {unidad: "Gramos por decilitro", abrev:"g/dL"} ,
                    {unidad: "Gramos por litro", abrev:"g/L"} ,
                    {unidad: "Microgramos", abrev:"mcg"} ,
                    {unidad: "Microgramos por decilitro", abrev:"mcg/dL"} ,
                    {unidad: "Microgramos por litro", abrev:"mcg/L"} ,
                    {unidad: "Microkats por litro", abrev:"mckat/L"} ,
                    {unidad: "Microlitros", abrev:"mcL"} ,
                    {unidad: "Micromoles por litro", abrev:"mcmol/L"} ,
                    {unidad: "Miliequivalentes", abrev:"mEq"} ,
                    {unidad: "Miliequivalentes por litro", abrev:"mEq/L"} ,
                    {unidad: "Miligramos", abrev:"mg"} ,
                    {unidad: "Miligramos por decilitro", abrev:"mg/dL"} ,
                    {unidad: "Miligramos por litro", abrev:"mg/L"} ,
                    {unidad: "Mililitros", abrev:"mL"} ,
                    {unidad: "Milímetros", abrev:"mm"} ,
                    {unidad: "Milímetros de mercurio", abrev:"mm Hg"} ,
                    {unidad: "Milimoles", abrev:"mmol"} ,
                    {unidad: "Milimoles por litro", abrev:"mmol/L"} ,
                    {unidad: "Miliosmoles por kilogramo de agua", abrev:"mOsm/kg agua"} ,
                    {unidad: "Miliunidades internacionales por litro", abrev:"mUI/L"} ,
                    {unidad: "Miliunidades por gramo", abrev:"mU/g"} ,
                    {unidad: "Miliunidades por litro", abrev:"mU/L"} ,
                    {unidad: "Nanogramos por decilitro", abrev:"ng/dL"} ,
                    {unidad: "Nanogramos por litro", abrev:"ng/L"} ,
                    {unidad: "Nanogramos por mililitro", abrev:"ng/mL"} ,
                    {unidad: "Nanogramos por mililitro por hora", abrev:"ng/mL/h"} ,
                    {unidad: "Nanomoles", abrev:"nmol"} ,
                    {unidad: "Nanomoles por litro", abrev:"nmol/L"} ,
                    {unidad: "Picogramos", abrev:"pg"} ,
                    {unidad: "Picogramos por mililitro", abrev:"pg/mL"} ,
                    {unidad: "Picomoles por litro", abrev:"pmol/L"} ,
                    {unidad: "Títulos.", abrev:""} ,
                    {unidad: "Unidades internacionales por litro", abrev:"UI/L"} ,
                    {unidad: "Unidades internacionales por mililitro", abrev:"UI/mL"} ,
                    {unidad: "Unidades por litro", abrev:"U/L"} ,
                    {unidad: "Unidades por mililitro", abrev:"U/mL"}
                ],
                ayudaValores:[
                    'Orden de la prueba',
                    'Imprimir Siempre?, Si esta prueba se reporta aunque no tenga resultado( como un subtítulo), indique SI',
                    'Unidad de medida básica de la prueba, este dato puede ser modificado en cada resultado',
                    'Nombre de la prueba',
                    'Respuesta que se indica automáticamente',
                    'Valor normal para cualquier paciente',
                    'Valor normal para bebés de hasta de 1 mes de edad',
                    'Valor normal para niños de hasta 12 años de edad',
                    'Valor normal para mujeres mayores de 12 años',
                    'Valor normal para hombres mayores de 12 años',
                    'Estos valores normales serán impresos siempre que haya resultado',
                    'Valor mínimo y/o máximo para los resultados de la prueba, el cual si se excede será notificado',
                ]
            }
        },
        methods: {
            //Funcion que cargara los datos del examen seleccionado
            cargarExamen(){
                if(this.examen.codigo.trim().length > 0)
                    this.axios.post('/app/laboratorio/DefinicionExamen', {
                        codigoExamen: this.examen.codigo.trim()
                    })
                    .then(resp => {
                        if(resp.data.codigo === 0 && resp.data.json.length > 0){
                            this.examen.empresa = resp.data.json[0].empresa
                            this.examen.nombre = resp.data.json[0].nombreExamen
                            this.examen.observaciones = resp.data.json[0].observaciones.replace(/\\n/g, '\n').replace(/\\r/g, '\r')
                            this.examen.bloqueoCorreo = !resp.data.json[0].bloqueoCorreo 
                            if(resp.data.json.length == 1 && resp.data.json[0].linea == '')
                                this.examen.pruebas = []
                            else
                                this.examen.pruebas = resp.data.json.map(dat => {
                                    dat.lineaanterior = dat.linea
                                    dat.imprimeSiempre = dat.imprimeSiempre == "S" ? true:false
                                    return dat  
                                })
                            this.bloqueoBusqueda = true
                        }
                        else {
                            this.$vs.notify({
                                title: 'Definición de Pruebas',
                                text: 'No se encontraron exámenes con el código especificado',
                                iconPack: 'feather',
                                icon: 'icon-alert-circle',
                                color: 'warning'
                            })
                            this.limpiarDatos()
                        }
                    })
                    .catch( () => {
                        this.limpiarDatos()
                    })
            },
            buscarExamen(){
                this.$refs.buscar_examen.iniciar((data) => {
                    if (data != null) {
                        this.examen.codigo = data.codigoExamen
                        this.cargarExamen();
                    }
                })
            },
            limpiarDatos(){
                this.examen = {
                        empresa:null,
                        codigo:null,
                        nombre:null,
                        descripcion:null,
                        pruebas:[],
                        observaciones:null,
                        bloqueoCorreo:null
                    }
                this.bloqueoBusqueda = false
                this.editMode = false
            },
            borrarPrueba(prueba){
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',
                    acceptText: 'SI',
                    cancelText: 'NO',
                    text: '¿Desea eliminar la prueba seleccionada?',
                    accept: () => {

                        this.axios.post('/app/laboratorio/BorrarPrueba', {
                            empresa: this.examen.empresa,
                            codigoExamen: this.examen.codigo,
                            pruebas: [{linea:prueba.linea}]
                        })
                        .then( () => {
                            this.cargarExamen()
                        })

                    },
                    cancel: () => {}
                })
            },
            editarPrueba(trprueba){
                this.editMode = true
                this.dialogPrueba = true
                this.prueba = JSON.parse(JSON.stringify(trprueba))
                this.prueba.lineaanterior = this.prueba.linea
                this.tituloModalNewEdit = "Editar Prueba"
            },
            agregarPrueba() {
                this.editMode = false
                this.dialogPrueba = true
                this.tituloModalNewEdit = "Agregar Prueba"  
                this.prueba = {
                    empresa: this.examen.empresa,
                    codigoExamen: this.examen.codigo,
                    linea:this.getMaxLinea(),
                    nombre:null,
                    umedida:null,
                    resTipico:null,
                    vNormal:null,
                    vNormalRn:null,
                    vNormalMenor:null,
                    vNormalMasc:null,
                    vNormalFem:null,
                    vNormal1:null,
                    vNormal2:null,
                    vNormal3:null,
                    vNormal4:null,
                    vNormal5:null,
                    vNormal6:null,
                    vNormal7:null,
                    vNormal8:null,
                    vNormal9:null,
                    vNormal10:null,
                    imprimeSiempre:false,
                    lineaanterior: null
                }
            },
            savePrueba(){
                let postPrueba = {...this.prueba}
                postPrueba.imprimeSiempre = postPrueba.imprimeSiempre ? "S":"N"
                this.axios.post('/app/laboratorio/'+ (this.editMode?'ActualizarPrueba':'GuaradarPrueba'), {
                        empresa: this.examen.empresa,
                        codigoExamen: this.examen.codigo,
                        pruebas: [postPrueba]
                    })
                    .then(() => {
                        this.dialogPrueba = false
                        this.cargarExamen()
                    })
                    .catch( () => { 
                        this.dialogPrueba = false
                        this.cargarExamen()
                    }
                    )
                    
            },
            actualizarTexto(){
                this.axios.post('/app/laboratorio/ActualizaExamen',
                    {
                        empresa: this.examen.empresa,
                        codigoExamen: this.examen.codigo,
                        observaciones: this.examen.observaciones,
                    }
                )
                .then(
                    () =>{this.cargarExamen()}
                )
                .catch(
                    () => {this.cargarExamen()}
                )

            },
            actualizarBloqueoCorreo(){
                this.axios.post('/app/laboratorio/ActualizaExamenBloqCorreo',
                    {
                        empresa: this.examen.empresa,
                        codigoExamen: this.examen.codigo,
                        bloqueoCorreo: !this.examen.bloqueoCorreo
                    }
                )
                .then(
                    () =>{this.cargarExamen()}
                )
                .catch(
                    () => {this.cargarExamen()}
                )
            },
            validarLinea(){
                
                for(let pr of this.examen.pruebas)
                    if(pr.lineaanterior != this.prueba.lineaanterior && pr.linea == this.prueba.linea || this.prueba.linea < 1 || this.prueba.linea > 50){
                        this.prueba.linea = this.prueba.lineaanterior
                        this.$vs.notify({
                                title: 'Definición de Pruebas',
                                text: 'Existe otra prueba con el mismo número de línea',
                                iconPack: 'feather',
                                icon: 'icon-alert-circle',
                                color: 'warning'
                            })
                            return
                    }
            },
            getMaxLinea(){
                if(this.examen && this.examen.pruebas)
                    return this.examen.pruebas.reduce( (m,n) => Math.max(parseInt(n.linea),m),0) + 1
                return 1;
            },
            verificarMedida(){
                this.unidadesMedida.map(unidad => { 
                    if (this.prueba.umedida && this.prueba.umedida.toUpperCase().replaceAll(' ','') == unidad.abrev.toUpperCase().replaceAll(' ','') )
                        this.prueba.umedida = unidad.abrev
                })
            },
            mostarAyuda(num){
                if(Number.isInteger(num) && num < this.ayudaValores.length && num >=0){
                    this.tituloModalNewEdit = (this.editMode? "Editar Prueba":"Agregar Prueba") + ':  ' + this.ayudaValores[num]
                }
                else{
                    this.tituloModalNewEdit = (this.editMode? "Editar Prueba":"Agregar Prueba")
                }
                    
            }
        }
    }
</script>
<style>

</style>

