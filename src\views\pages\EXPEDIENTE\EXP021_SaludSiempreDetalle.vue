<template>
<DxDataGrid :ref="dataDetalleRefKey" v-bind="DefaultDxGridConfiguration" :headerFilter="{visible:false,allowSearch:false}" :paging="{ enabled:false }" :data-source="detalleDataSource" :height="'400px'" :on-row-removing="onRowRemoving" :on-row-removed="onRowRemoved" :no-data-text="noDataText">
    <DxSelection mode="single" />
    <DxToolbar>
        <DxItem name="groupPanel" />
        <DxItem name="searchPanel" />
        <DxItem location="after" template="opcionesTemplate" />
    </DxToolbar>

    <DxEditing :allow-updating="false" :allow-adding="true" :allow-deleting="deleting" :use-icons="true" mode="popup">
        <DxPopup v-bind="popupConf" :height="300" :width="600" title="Registro de autorización">
            <DxToolbarItem widget="dxButton" toolbar="bottom" :options="{
                                                text: 'Guardar',
                                                type: 'success',
                                                useSubmitBehavior: true,
                                                onClick: submit
                                            }" location="after">
            </DxToolbarItem>
            <DxToolbarItem widget="dxButton" toolbar="bottom" :options="{
                                                text: 'Cancelar',
                                                type: 'danger',
                                                useSubmitBehavior: true,
                                                onClick: ()=>{this.dataGrid.cancelEditData()}
                                            }" location="after">
            </DxToolbarItem>
        </DxPopup>

        <DxForm labelMode="floating" :ref="dataAddDetalleRefKey">
            <DxItem item-type="group" :col-span="2" :col-count="2">
                <DxItem data-field="Procedimiento" :label="{text: 'Procedimiento'}" editor-type="dxTextBox" :editor-options="{ maxLength: 100 }" :is-required="true" />
                <DxItem data-field="Codigo" caption="Código" editor-type="dxTextBox" :editor-options="{ maxLength: 20 }" :is-required="true" />
                <DxItem data-field="Cantidad" editor-type="dxNumberBox" :editor-options="{ min: 0, max: 99999999, format: '#0.0#', step: 0 }" :is-required="true" />
                <DxItem data-field="Valor" editor-type="dxNumberBox" :editor-options="reglasValor" :is-required="true" />
            </DxItem>
        </DxForm>
    </DxEditing>

    <DxColumn :width="400" data-field="Procedimiento" caption="Procedimiento autorizado médico/QX" alignment="center" />
    <DxColumn :width="100" data-field="Codigo" caption="Código" alignment="center" />
    <DxColumn :width="100" data-field="Cantidad" alignment="center" />
    <DxColumn :width="100" data-field="Valor" alignment="center" />
    <DxColumn :width="150" data-field="SubTotal" caption="SubTotal" alignment="center" format="number" />
    <DxColumn :width="150" data-field="FechaRegistro" caption="Fecha registro" format="dd/MM/yyyy HH:mm:ss" data-type="date" alignment="center" />
    <DxColumn :width="150" data-field="Usuario" alignment="center" />

    <DxSummary>
        <DxTotalItem column="Procedimiento" summary-type="count" />
        <DxTotalItem column="SubTotal" summary-type="sum" display-format="{0}" :value-format="{ type:'currency', precision: 2, currency: 'GTQ' }" />
    </DxSummary>

    <template #opcionesTemplate>
        <ExpedienteGridToolBar v-bind="$props" :visible="true" :pdfExportItems="[]" @add="addRow" @refresh="Cargar" :report-param="$props" :showItems="buttons" />
    </template>
</DxDataGrid>
</template>

<script>
import {
    DxDataGrid,
    DxColumn,
    DxSelection,
    DxPopup,
    DxEditing,
    DxToolbar,
    DxSummary,
    DxTotalItem,
    DxForm,
} from 'devextreme-vue/data-grid'
import CustomStore from 'devextreme/data/custom_store'
import 'devextreme-vue/popup';
import {
    DxToolbarItem
} from 'devextreme-vue/popup'
import {
    DefaultDxGridConfiguration,
    popupConf
} from './data'
import {
    DxItem
} from 'devextreme-vue/form'

const dataDetalleRefKey = 'detalle-form'
const dataAddDetalleRefKey = 'detalle-popup'

export default {
    name: 'SaludSiempreDetalle',
    components: {
        DxForm,
        DxDataGrid,
        DxColumn,
        DxItem,
        DxSelection,
        DxPopup,
        DxEditing,
        DxToolbar,
        DxToolbarItem,
        DxSummary,
        DxTotalItem,
        ExpedienteGridToolBar: () => import('./ExpedienteGridToolBar.vue'),
    },
    data() {
        return {
            DefaultDxGridConfiguration,
            dataDetalleRefKey,
            dataAddDetalleRefKey,
            /**Registro de antecedente que se selecciona el el grid */
            detalleDataSource: new CustomStore({
                key: 'id',
                load: () => {
                    return this.detalle
                },
                insert: (values) => {
                    this.grabarDetalle(values);
                },
                remove: () => {
                    return this.detalle
                }
            }),
            detalle: [],
            //Reglas para el campos numéricos
            reglasValor: {
                min: 0,
                max: 99999999.99,
                format: "#0.00",
                step: 0
            },
            noDataText: 'Sin datos',
            popupConf,
        }
    },
    props: {
        SerieAdmision: null,
        CodigoAdmision: null,
        NumeroExpediente: null,

        StatusAdmision: null,

        Formulario: null,
        EstadoFormulario: null
    },
    methods: {
        Cargar() {
            if (this.Formulario !== '' && this.Formulario !== null) {
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaDetFormHosp', {
                        NoFormulario: this.Formulario
                    })
                    .then(resp => {
                        this.detalle = resp.data.json.map((x, index) => {
                            return {
                                id: index,
                                NoFormulario: x.NoFormulario,
                                Empresa: x.Empresa,
                                CodDetalle: x.CodDetalle,
                                Procedimiento: x.NombreAutorizacion,
                                Codigo: x.Codigo,
                                Cantidad: x.Cantidad,
                                Valor: x.Valor,
                                SubTotal: x.SubTotal,
                                FechaRegistro: x.Fecharegistro,
                                Usuario: x.Usuario
                            }
                        })
                        this.detalleDataSource.load().then(
                            () => {
                                this.refreshDataGrid();
                            },
                            () => {
                              //  console.error('error loading data customstore' + JSON.stringify(error))
                            }
                        )
                    })
            } else {
                this.detalle = []
            }
        },
        grabarDetalle(values) {
            let postData = {
                SerieAdmision: this.SerieAdmision,
                Admision: this.CodigoAdmision,
                NoFormulario: this.Formulario,
                NombreAutorizacion: values.Procedimiento,
                Codigo: values.Codigo,
                Cantidad: values.Cantidad,
                Valor: values.Valor
            }
            this.axios.post("/app/v1_ExpedienteEvolucion/InsertarDetFormHosp", {
                ...postData
            }).then(() => {
                this.Cargar()
            })
        },
        refreshDataGrid() {
            this.dataGrid.refresh()
                .then(function () {

                })
                .catch(function () {
                  //  console.error('error refreshing: ' + error)
                });
            this.dataGrid.clearSelection();
            this.dataGrid.clearSorting();
            this.dataGrid.clearFilter();
            this.dataGrid.pageIndex(0);
        },
        submit() {
            this.dataGrid.saveEditData()
        },
        addRow() {
            this.dataGrid.addRow();
        },
        onRowRemoving(e) {
            e.cancel = new Promise((resolve, reject) => {
                this.axios.post('/app/v1_ExpedienteEvolucion/AnulaDetFormHosp', {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    NoFormulario: this.Formulario,
                    CodDetalle: e.data.CodDetalle,
                }).then((resp) => {
                    resp.data.codigo == 0 ? resolve(false) : resolve(true)
                }).catch((err) => {
                    reject(err.descripcion ? err.descripcion : err)
                })
            })
        },
        onRowRemoved() {
            this.Cargar();
        }
    },
    watch: {
        'Formulario'(newval) {
            if (newval !== '' && newval !== null) {
                this.noDataText = 'Sin datos'
                this.Cargar()
            } else {
                this.detalle = []
                this.noDataText = 'Debe guardar el formulario para poder agregar una autorización'
            }
        },
    },
    computed: {
        dataGrid: function () {
            return this.$refs[dataDetalleRefKey].instance;
        },
        buttons: function () {
            if (!this.EstadoFormulario && this.Formulario !== '' && this.Formulario !== null) {
                return ['add', 'refresh']
            } else {
                return [];
            }
        },
        deleting: function () {
            if (!this.EstadoFormulario && this.Formulario !== '' && this.Formulario !== null) {
                return true
            } else {
                return false;
            }
        }
    },
}
</script>
