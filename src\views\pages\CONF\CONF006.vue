<template>
<div>
    <vs-input placeholder="Buscar carpetas..." v-model="Searchword" @input="search" size="small" />
    <v-tree v-if="true" ref="menuTree" :data="TreeData" :tpl="tpl" :radio="true" :halfcheck='true' :allowGetParentNode="true" :draggable="true" @drag-node-end="(e) => DragEnd(e)" @node-select="(e)=>$emit('carpetaSeleccionada',e)"/>

    <vs-popup :title="CarpetaActual.IdSub? 'Editar Carpeta': 'Nueva Carpeta'" :active.sync="PopupNuevaEditarCarpeta">
        <vs-input label="Nombre" class="w-full" v-model="CarpetaActual.Nombre" />
        <vs-select label="Padre" class="w-full" :options="CarpetasSelect" v-model="CarpetaActual.IdSubPadre">
            <vs-select-item :key="key" :value="item.IdSub" :text="'['+item.IdSub + ']'+item.Nombre" v-for="(item,key) in CarpetasSelect" />
        </vs-select>

        <vs-divider />
        <div class="flex">
            <vs-spacer></vs-spacer>
            <vs-button color="success" @click="GuardarCarpeta">Guardar</vs-button>
        </div>
    </vs-popup>

</div>
</template>

<script>
//https://github.com/halower/vue-tree
import {
    VTree
} from 'vue-tree-halower'
import 'vue-tree-halower/dist/halower-tree.min.css'

export default {

    components: {
        VTree,
    },
    props: {
        titulo: {
            type: String,
            default: ''
        },
        idAplicativo: {
            type: Number,
            required: true
        },
        allowDelete: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            CarpetaActual: {}, //carpeta nueva o que se esta actualizando
            Searchword: '', //filtrado del arbol
            PopupNuevaEditarCarpeta: false, //para mostrar el popup de nuevo / editar
            CarpetasAplicativo: [], //todas las carpetas que pertenecen al aplicativo
            /**array para mostrar la información en el Tree */
            TreeData: [],
        }
    },
    methods: {
        search() {
            this.$refs.menuTree.searchNodes(this.Searchword)
            this.$refs.menuTree.searchNodes(this.Searchword ? (x => x.title.toUpperCase().includes(this.Searchword.toUpperCase())) : '')
        },
        /**Funcion ara crear el template, sintaxis JSX */
        tpl(...args) {
            let {
                0: node,
            //     2: parent,
            //     3: index
            } = args
            let titleClass = node.selected ? 'node-title node-selected' : 'node-title'
            if (node.searched) 
                titleClass += ' node-searched'
            const btnDelete = (node.id>0 && this.allowDelete?<vs-button size="small" color="danger" icon-pack="fas" icon="fa-trash" class="ml-1" type="border"  onClick = { () => this.EliminarCarpeta(node) }/>:null)
            const btnEdit = (node.id>0?<vs-button size="small" color="success" icon-pack="fas" icon="fa-edit" class="mr-1" type="border" onClick = { () => this.EditarCarpeta(node) }/>:null)
            const btnAdd = <vs-button size="small" color="primary" icon-pack="fas" icon="fa-plus" class="mr-1" type="border" onClick = { ()=> this.AgregarCarpeta(node)}/>
            return <span class="template-tree-node" >
                    
                        <span class = { titleClass } domPropsInnerHTML = { '['+node.id +'] '+node.title } onClick = { () => { this.$refs.menuTree.nodeSelected(node); node.expanded=!node.expanded } }>
                        </span> 
                        {btnAdd}{btnEdit}{btnDelete}
                    </span>
                    
        },
        GuardarCarpeta() {
            return this.axios.post('/app/administracion/guardar_subCarpeta', {
                    IdAplicativo: this.CarpetaActual.IdAplicativo,
                    SubCarpeta: this.CarpetaActual.Nombre,
                    IdSubPadre: this.CarpetaActual.IdSubPadre,
                    IdSub: this.CarpetaActual.IdSub==0?null:this.CarpetaActual.IdSub,
                })
                .then(() => {
                    this.PopupNuevaEditarCarpeta = false
                    this.CargarCarpetas()
                })
        },
        CargarCarpetas() {
            if(this.idAplicativo){
                this.axios.post('/app/administracion/obtener_subCarpeta', {
                    IdAplicativo: this.idAplicativo
                }).then(resp => {
                    this.CarpetasAplicativo = resp.data.json
                    this.$emit('loadedData', this.CarpetasAplicativo)
                })
                
            }
        },
        EliminarCarpeta(node) {
            return this.axios.post('/app/administracion/EliminarCarpeta', {
                    IdSub: node.IdSub,
                })
                .then(() => {
                    this.$emit('deletedData', node)
                    this.CargarCarpetas()
                })
        },
        DragEnd(e) {
            /**
             * llamar a la api de update 
             * siempre llamar al cargarCarpetas ya que el drag del componente no se puede cancelar
             * 
             */
            this.CarpetaActual = {
                IdAplicativo: this.idAplicativo,
                IdSub: e.dragNode.IdSub,
                IdSubPadre: e.targetNode.IdSub,
                Nombre: e.dragNode.Nombre,
            }
            this.GuardarCarpeta().catch(() => this.CargarCarpetas())
            return false
        },
        
        nodechecked(node, v) {
            alert('that a node-check envent ...' + node.title + v)
        },
        //Inicia el popup para gagregar nuevo registro
        AgregarCarpeta(parent) {

            this.PopupNuevaEditarCarpeta = true
            this.CarpetaActual = {
                IdAplicativo: this.idAplicativo,
                IdSub: null,
                IdSubPadre: parent.IdSub,
                Nombre: '',
            }
            parent.expanded = true

        },
        EditarCarpeta(carpeta) {
            this.PopupNuevaEditarCarpeta = true
            this.CarpetaActual = {
                IdAplicativo: this.idAplicativo,
                IdSub: carpeta.IdSub,
                IdSubPadre: carpeta.IdSubPadre,
                Nombre: carpeta.Nombre,
            }
        },
    },
    mounted() {
        this.CargarCarpetas()
    },
    watch: {
        'CarpetasAplicativo'() {
            this.TreeData = [{
                id: 0,
                title: this.titulo,
                expanded: true,
                children: this.$list_to_tree(this.CarpetasAplicativo, 'IdSub', 'IdSubPadre', 'children', false, (x) => {
                    x.id = x.IdSub
                    x.title = x.Nombre
                })
            }]
        },
        'idAplicativo'() {
            this.CargarCarpetas()
        },
    },
    computed: {
        /**Genera un listado de las carpetas a las que se puede asignar la actual */
        CarpetasSelect(){
            let arr = [{

                IdAplicativo: this.idAplicativo,
                IdSub: 0,
                IdSubPadre: null,
                Nombre: '- Ninguno -',
            
            }]
            
            arr = arr.concat(this.CarpetasAplicativo.filter(x=>x.IdSub != this.CarpetaActual.IdSub))
            //this.$emit('CarpetasChanged',arr)
            return arr
        }
    }
}
</script>

<style>

.tree-node-el {
    width: fit-content !important;
    height: fit-content !important;
    background: aliceblue !important;
    display: flex !important;
    flex-direction: row;
}

.template-tree-node {
    display: contents !important;
    flex-direction: row !important;
}
</style>
