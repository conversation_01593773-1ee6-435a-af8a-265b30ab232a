<template>
<vx-card title="Cotización">
    <div class="content content-pagex">
        <!----------------------- Encabezado cotización ---------->
        <div class="flex flex-wrap">
            <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">
                <div class="flex flex-wrap">
                    <div style="margin-left:10px;margin-right:20px" class="xs:w-full md:w-5/6 lg:w-5/6 xl:w-5/6">
                        <ValidationProvider name="Agencia">
                            <label class="typo__label">Departamentos</label>
                            <multiselect v-model="cbDepartamento" :options="ListaDepartamentos" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Departamento_seleccionada" placeholder="Seleccionar" @input="onChangeDepartamento">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>

                </div>

            </div>
            <div style="margin-right: 20px" class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <label class="typo__label">Estado</label>
                <multiselect v-model="cb_lista_operacion" :options="lista_estado" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Operacion_seleccionada" placeholder="Seleccionar" @input="onChangeEstado">
                    <span slot="noOptions">Lista no disponible.</span>
                </multiselect>
            </div>

            <div style="margin-left:10px;margin-right:20px" class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7">
                <label class="typo__label">Fecha Inicio:</label>
                <vs-input type="date" v-model="fecha_inicio" name="date1" />
            </div>

            <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7">
                <label class="typo__label">Fecha Final:</label>
                <vs-input type="date" v-model="fecha_final" name="date1" />
            </div>

            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <vs-button style="float:left;margin: 20px" color="primary" type="filled" icon-pack="fas" icon="fa-search" @click="Consultar_cotizacion('I', Id_estado_seleccionado)"> Búsqueda</vs-button>
            </div>
            <br>
        </div>
        <!-----------------------Finalización Encabezado cotización ---------->

        <!----------------------- Resultado busqueda producto ---------->
        <vs-popup classContent="popup-example" title="Resultado Búsqueda" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container" :active.sync="Estado_VentanaEmergente_Busqueda">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">

                <form>

                    <vs-divider></vs-divider>
                    <vs-table2 max-items="10" pagination :data="producto">

                        <template slot="thead">
                            <th width="100px">Codigo</th>
                            <th width="1000px">Descripcion</th>
                            <th width="300px">Marca</th>
                            <th width="300px">Presentación</th>
                            <th></th>
                        </template>

                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 :data="data[indextr].Codigo">
                                    {{data[indextr].Codigo}}
                                </vs-td2>
                                <vs-td2 :data="data[indextr].Nombre">
                                    {{data[indextr].Nombre}}
                                </vs-td2>

                                <vs-td2 :data="data[indextr].Marca">
                                    {{data[indextr].Marca}}
                                </vs-td2>
                                <vs-td2 :data="data[indextr].PresentacionNombre">
                                    {{data[indextr].PresentacionNombre}}
                                </vs-td2>
                                <vs-td2 align="right">
                                    <vs-button color="success" icon-pack="fas" icon="fa-check-circle" style="display:inline-block;margin-right:2px" @click="Seleccionar_Producto(data[indextr])"></vs-button>
                                </vs-td2>
                            </tr>correctamente
                        </template>
                    </vs-table2>
                </form>

            </div>
        </vs-popup>
        <!----------------------- Resultado busqueda producto ---------->

        <!----------------------- Inicio Cotizaciones Internas generadas---------->
        <div>

            <vs-divider></vs-divider>

            <vs-table2 max-items="10" pagination :data="Lista_cotizaciones_internas" search id="tb_departamentos">

                <template slot="thead">
                    <th width="100">Nº. Solicitud </th>
                    <th width="300">Departamento </th>
                    <th width="30">Tipo</th>
                    <th width="500">Observación de Solicitud</th>
                    <th width="100">Fecha Solicitud</th>
                    <th width="200">Usuario</th>
                    <th width="120">Estado</th>
                    <th width="20">Detalle</th>
                </template>

                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data" @click="SeleccionarCotizacionInterna(tr)">
                        <vs-td2 :data="data[indextr].IDSOLICITUDENCFK">
                            {{data[indextr].IDSOLICITUDENCFK}}
                        </vs-td2>

                        <vs-td2 :data="data[indextr].DESCRIPCION_DEPTO">
                            {{data[indextr].IDDEPARTAMENTOFK}}
                            -
                            {{data[indextr].DESCRIPCION_DEPTO}}
                        </vs-td2>

                        <vs-td2 :data="data[indextr].TIPO">
                            {{data[indextr].TIPO}}
                        </vs-td2>
                        <vs-td2 :data="data[indextr].OBSERVACIONES_S">
                            {{data[indextr].OBSERVACIONES_S}}
                        </vs-td2>
                        <vs-td2 :data="data[indextr].FECHA_CREACION_ORDEN">
                            {{data[indextr].FECHA_CREACION_ORDEN}}
                        </vs-td2>
                        <vs-td2 :data="data[indextr].USUARIO_S">
                            {{data[indextr].USUARIO_S}} - {{data[indextr].NOMBRES_S}}
                        </vs-td2>
                        <vs-td2 style="text-align: center;" :data="data[indextr].ESTADO">
                            <label v-if="data[indextr].ESTADO == 'P'" style="height: 30px;background-color:#B2BABB;width:150px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                            <label v-if="data[indextr].ESTADO == 'A'" style=" height: 30px;background-color:green;width:150px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                            <label v-if="data[indextr].ESTADO == 'R'" style="height: 30px;background-color:#C82B0A;width:150px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                        </vs-td2>

                        <vs-td2 align="right">
                            <vs-button color="success" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-eye" @click="Consultar_solicitud(data[indextr].IDSOLICITUDENCFK)"></vs-button>
                        </vs-td2>

                    </tr>
                </template>
            </vs-table2>

        </div>
        <!----------------------- Finalizadas Cotizaciones Internas generadas---------->

        <!----------------------- Encabezado cotización ---------->
        <vs-divider></vs-divider>
        <div class="flex flex-wrap">

            <div style="margin-right: 20px" class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <div style="font-family: arial;font-size: 18px;    padding: 27px 0;color: black;  margin-right: 30px;">Cotizaciones Asociadas</div>
            </div>
            <div style="margin-right: 20px" class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                <vs-button v-if=" CotInt_Estado== 'P'" color="primary" style="float:left;margin-top:20px;margin-right:5px" type="filled" icon-pack="fas" icon="fa-plus" @click="CotizacionProveedor()"> Nueva</vs-button>
                <vs-button v-if=" CotInt_Estado== 'P'" color="primary" style="float:left;margin-top:20px;margin-right:1px" type="filled" icon-pack="fas" icon="fa-search" @click="Buscar_cotizacionExistente()"> Existente</vs-button>
            </div>
        </div>

        <div class="flex flex-wrap">
            <div style="margin: 15px" class="flex flex-wrap">
                <div class="w-full md:w-1/9 lg:w-1/9 xl:w-1/9, label-sizem ">
                    <div style="border-radius:5px;padding:5px;font-size:15px;background-color:white;color:black">
                        Solicitud No.: {{IdSolicitud}}
                        <br>
                        Departamento Solicitante: {{DescripcionDepto}}
                    </div>
                </div>
            </div>

        </div>
        <!-----------------------Finalización Encabezado cotización ---------->

        <!----------------------- Inicio Cotizaciones Externas---------->
        <div>
            <br>
            <vs-divider></vs-divider>
            <vs-table2 max-items="10" pagination :data="Lista_cotizaciones_externas">

                <template slot="thead">
                    <th width="100">Nº. Cotización </th>
                    <th width="100">NIT</th>
                    <th width="500">Proveedor</th>
                    <th width="100">Monto Total</th>
                    <th width="500">Observación de Cotización</th>
                    <th width="200">Usuario</th>
                </template>

                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">
                        <vs-td2 :data="data[indextr].IDCOTIZACIONENC">
                            {{data[indextr].IDCOTIZACIONENC}}
                        </vs-td2>

                        <vs-td2 :data="data[indextr].PROV_NIT">
                            {{data[indextr].PROV_NIT}}
                        </vs-td2>

                        <vs-td2 :data="data[indextr].DESCRIPCION_PROVEEDOR">
                            {{data[indextr].DESCRIPCION_PROVEEDOR}}
                        </vs-td2>
                        <vs-td2 align="right" :data="data[indextr].TOTAL">
                            Q. {{data[indextr].TOTAL}}
                        </vs-td2>
                        <vs-td2 :data="data[indextr].OBSERVACIONES">
                            {{data[indextr].OBSERVACIONES}}
                        </vs-td2>
                        <vs-td2 :data="data[indextr].USUARIO">
                            {{data[indextr].USUARIO}} - {{data[indextr].NOMBRES}}
                        </vs-td2>
                    </tr>
                </template>
            </vs-table2>

        </div>
        <!----------------------- Finalizadas Cotizaciones Externas---------->

        <!----------------------- Inicio Cotizaciones Externas---------->

        <vs-popup classContent="popup-example" :title="Titulo_emergente" :active.sync="Estado_VentanaCotizacionExterna">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
                <!--------LISTA PROVEEDOR  / Monto Total / Dias Credito--------->
                <div style="margin: 15px" class="flex flex-wrap">

                    <div class="w-full">
                        <label class="typo__label"> Observaciones</label>
                        <vs-textarea class="w-full" v-model="observaciones" />
                        <br>
                    </div>

                    <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5" style="margin-right:10px;">
                        <ValidationProvider name="selectClase"  rules="required" class="required">
                            <label class="typo__label"> Proveedor</label>
                            <multiselect v-model="cb_proveedor" :options="Lista_proveedores" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="seleccion_proveedor" placeholder="Búsqueda" @input="onChangeProveedor" >
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-1/4 lg:w-1/6 xl:w-1/6" style="margin-right:10px;">
                        <ValidationProvider name="MontoTotal" rules="required|numero_min:0" class="required">
                            <label class="typo__label">Monto</label>
                            <vs-input type="number" class="w-full" v-model="granTotal" v-on:blur="onChangeGranTotal" />
                        </ValidationProvider>

                    </div>
                    <div class="w-full md:w-1/4 lg:w-1/6 xl:w-1/6" style="margin-right:10px;">
                        <ValidationProvider name="DiasCredito" rules="required|numero_min:0|numero_entero"  class="required">
                            <label class="typo__label"> Días Crédito</label>
                            <vs-input type="number" class="w-full" v-model="CotExt_DiasCredito" />
                        </ValidationProvider>
                        <br>
                    </div>
                    <div class="w-full md:w-1/4 lg:w-1/6 xl:w-1/6" style="margin-right:10px;">
                        <ValidationProvider name="pagos" rules="required|numero_min:0|numero_entero" class="required">
                            <label class="typo__label">Pagos</label>
                            <vs-input type="number" class="w-full" v-model="CotExt_CantPagos" />
                        </ValidationProvider>
                    </div>

                </div>

                <vs-divider> </vs-divider>
                <!-------- PRODUCTO  / Cantidad / Precio Unitario --------->
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5" style="margin-right:10px;">
                        <ValidationProvider name="Producto" rules="required|max:50" class="required">
                            <label class="typo__label"> Producto</label>
                            <vs-input type="text" class="w-full" placeholder="Buscar" v-model="producto_buscar" v-on:blur="cargar_producto" />
                        </ValidationProvider>
                    </div>

                    <div class="w-full md:w-1/4 lg:w-1/6 xl:w-1/6" style="margin-right:10px;">
                        <ValidationProvider name="Cantidad" rules="required|numero_min:0"  class="required">
                            <label class="typo__label"> Cantidad</label>
                            <vs-input id="cantidad_" type="number" class="w-full" v-model="Ingreso_Cantidad_proveedor" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-1/4 lg:w-1/6 xl:w-1/6" style="margin-right:10px;">
                        <ValidationProvider name="Total" rules="required|numero_min:0" class="required">
                            <label class="typo__label"> Total</label>
                            <vs-input id="precio_untario_" type="number" class="w-full" v-model="subtotal" @blur="precio_uni()" v-on:blur="onChangeSubTotal" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-1/4 lg:w-1/6 xl:w-1/6" style="margin-right:10px;">
                        <ValidationProvider name="PrecioUnitario" rules="required|numero_min:0"  class="required">
                            <label class="typo__label"> Precio Unitario</label>
                            <vs-input disabled id="precio_untario_" type="number" class="w-full" v-model="Ingreso_Precio_unitario" />
                        </ValidationProvider>
                    </div>
                </div>
                <!--------------INICIO DETALLE PRODUCTO SELECCIONADO ------------->
                <div style="margin: 15px" class="flex flex-wrap">
                    <div v-if="Producto_seleccionado.Codigo > 0 " class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                        <div style="border-radius:5px;padding:5px;font-size:15px;background-color:#09af00;color:white">
                            <table>
                                <tr>
                                    <td>{{Producto_seleccionado.Codigo}}</td>
                                </tr>
                                <tr>
                                    <td>{{Producto_seleccionado.marca_comercial}}</td>
                                </tr>
                                <tr>
                                <td> {{Producto_seleccionado.Presentacion}}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="w-full md:w-1/4 lg:w-1/6 xl:w-1/6" style="margin-left:10px;">
                        <ValidationProvider name="MontoTotal" rules="required|numero_min:0|numero_entero" class="required">
                            <label class="typo__label">Total Cotización</label>
                            <vs-input disabled type="number" class="w-full" v-model="CotExt_GranTotal" />
                        </ValidationProvider>
                    </div>
                    <div style="margin-right:5px;">
                        <div>
                            <vs-button color="primary" style="float:left;margin: 20px" type="filled" icon-pack="fas" icon="fa-plus" @click="Agregar_Detalle()"> Agregar</vs-button>

                            <vs-button color="success" style="float:left;margin: 20px" type="filled" icon-pack="feather" icon="icon-x" id="btn_limpieza" @click="Limpiar()"> Limpiar</vs-button>
                        </div>
                    </div>

                </div>
                <!----------- --FINALIZACION PRODUCTO SELECCIONADO------------->

                <vs-table2 max-items="10" pagination :data="CotExt_Detalle" id="tb_departamentos">

                    <template slot="thead">
                        <th width="100px">Código</th>
                        <th width="100px">Cant. Proveedor</th>
                        <th width="1000px">Descripción</th>
                        <th width="150px">Precio Unitario</th>
                        <th width="200px">SubTotal</th>
                        <th width="100px"></th>

                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2 :data="data[indextr].IdProducto">
                                {{data[indextr].IdProducto}}
                            </vs-td2>
                            <vs-td2 :data="data[indextr].Cantidad_proveedor">
                                {{data[indextr].Cantidad_proveedor}}
                            </vs-td2>

                            <vs-td2 :data="data[indextr].Descripcion">
                                {{data[indextr].Descripcion}}
                            </vs-td2>

                            <vs-td2 align="right" :data="data[indextr].Precio_unitario">
                                {{parseFloat(tr.Precio_unitario).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                            </vs-td2>

                            <vs-td2 align="right" :data="data[indextr].Subtotal">
                                {{parseFloat(tr.Subtotal).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                            </vs-td2>
                            <vs-td2 align="center">
                                <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="EliminarProducto(indextr, data[indextr].Subtotal)"></vs-button>
                            </vs-td2>

                        </tr>
                    </template>
                </vs-table2>

                <!----------- --DETALLE COTIZACION EXTERNA ------------->
                <vs-divider></vs-divider>
                <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" id="btn_confirmacion" @click="Confirmacion_Transaccion()"> Grabar</vs-button>
                <vs-divider></vs-divider>
            </div>
        </vs-popup>
        <!----------------------- FIN Cotizaciones Externas---------->

        <!---------------Fin Ventana Emergente _---------->

        <!--------------------- BUSCAR COTIZACIONES EXISTENTES--------------->
        <vs-popup classContent="popup-example" :title="Titulo_emergente" :active.sync="Estado_VentanaCotExistente">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">

                <vs-divider></vs-divider>
                <vs-table2 max-items="10" pagination :data="Lista_cotizaciones_externas_Anteriores">

                    <template slot="thead">
                        <th width="100">Nº. Cotización </th>
                        <th width="100">NIT </th>
                        <th width="500">Proveedor</th>
                        <th width="200">Monto Total</th>
                        <th width="500">Observación de Cotización</th>

                    </template>

                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2 :data="data[indextr].IDCOTIZACIONENC">
                                {{data[indextr].IDCOTIZACIONENC}}
                            </vs-td2>

                            <vs-td2 :data="data[indextr].PROV_NIT">
                                {{data[indextr].PROV_NIT}}
                            </vs-td2>

                            <vs-td2 :data="data[indextr].DESCRIPCION_PROVEEDOR">
                                {{data[indextr].DESCRIPCION_PROVEEDOR}}
                            </vs-td2>
                            <vs-td2 :data="data[indextr].TOTAL_REP">
                                {{data[indextr].TOTAL_REP}}
                            </vs-td2>
                            <vs-td2 :data="data[indextr].OBSERVACIONES">
                                {{data[indextr].OBSERVACIONES}}
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
        </vs-popup>
        <!---+++++++++++++++++++++++++++++++ MUESTRA LA VENTANA PARA MODIFICAR LA COTIZACION REGISTRADA ANTERIORENTE ***** ---->

        <vs-popup classContent="popup-example" :title="Titulo_emergente" :active.sync="Estado_VentanaCotizacionExternaReutilizada">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
                <!--------LISTA PROVEEDOR  / Monto Total / Dias Credito--------->
                <div style="margin: 15px" class="flex flex-wrap">
                    <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3" style="margin-right:10px;">
                        <ValidationProvider name="selectClase" rules="required" class="required">
                            <label style="font-size:12px">Proveedor</label>
                            <multiselect v-model="cb_proveedor" :options="Lista_proveedores" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="seleccion_proveedor" placeholder="Búsqueda" @input="onChangeProveedor">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5" style="margin-right:10px;">
                        <vs-input disabled label="Monto" type="text" class="w-full" v-model="granTotal" />
                    </div>
                    <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5" style="margin-right:10px;">
                        <ValidationProvider name="DiasCredito" rules="required|numero_min:0|numero_entero"  class="required">
                            <vs-input label="Dias Crédito" type="number" class="w-full" v-model="CotExt_DiasCredito" />
                        </ValidationProvider>
                    </div>

                </div>

                <vs-divider> </vs-divider>

                <!--------------INICIO DETALLE PRODUCTO SELECCIONADO ------------->
                <div class="flex flex-wrap">
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">

                        <div style="top:-8px;left:30px" class="vs-con-loading__container">
                            <div class="vx-card__body">
                                <div class="vx-row">
                                    <div id="account-info-col-1" class="vx-col flex-1">
                                        <table>
                                            <tr>
                                                <td class="font-semibold">{{Producto_seleccionado.Codigo}}</td>
                                            </tr>
                                            <tr>
                                                <td class="font-semibold">{{Producto_seleccionado.codigo_interno}}</td>
                                            </tr>
                                            <tr>
                                                <td class="font-semibold">{{Producto_seleccionado.nombre}}</td>
                                            </tr>
                                            <tr>
                                                <td class="font-semibold">{{Producto_seleccionado.marca_comercial}}</td>
                                            </tr>
                                            <tr>
                                                <td class="font-semibold">{{Producto_seleccionado.Principio_activo}}</td>
                                            </tr>
                                            <tr>
                                                <td class="font-semibold"> {{Producto_seleccionado.Presentacion}}</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                </div>
                <!----------- --FINALIZACION PRODUCTO SELECCIONADO------------->

                <vs-table2 max-items="10" pagination :data="CotExt_Detalle" id="tb_departamentos">

                    <template slot="thead">
                        <th>Código</th>
                        <th>Cant. Proveedor</th>
                        <th>Descripción</th>
                        <th>Precio Unitario</th>
                        <th>SubTotal</th>

                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2 width="50px" :data="data[indextr].IdProducto">
                                {{data[indextr].IdProducto}}
                            </vs-td2>
                            <vs-td2 width="50px" :data="data[indextr].Cantidad_proveedor">
                                {{data[indextr].Cantidad_proveedor}}
                            </vs-td2>

                            <vs-td2 width="1000px" :data="data[indextr].Descripcion">
                                {{data[indextr].Descripcion}}
                            </vs-td2>

                            <vs-td2 width="200px" :data="data[indextr].Precio_unitario">
                                {{data[indextr].Precio_unitario}}
                            </vs-td2>

                            <vs-td2 width="150px" :data="data[indextr].Subtotal">
                                {{data[indextr].Subtotal}}
                            </vs-td2>

                        </tr>
                    </template>
                </vs-table2>

                <!----------- --DETALLE COTIZACION EXTERNA ------------->
                <vs-divider></vs-divider>
                <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" id="btn_confirmacion" @click="Confirmacion_TransaccionReutilizar()"> Grabar</vs-button>
                <vs-divider></vs-divider>
            </div>
        </vs-popup>
        <!--------------- VISOR DE DETALLE -------------->
        <vs-popup classContent="popup-example" title="Verificación de Detalle Solicitud" :active.sync="Ventana_Detalle" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">

                <form>
                    <div class="flex flex-wrap">
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                            <label class="label-sizem ">Solicitud de Compra No. </label>
                            <label class="label-sizem">{{this.IdSolicitudSeleccionado}}</label>

                        </div>
                        <!---  DATOS SOLICITUD -->
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">

                        </div>
                    </div>

                    <vs-divider></vs-divider>
                    <vs-table2 max-items="10" pagination :data="Lista_detalle_solicitud" noDataText="Sin datos disponibles" search id="tb_departamentos">

                        <template slot="thead">
                            <th>Código </th>
                            <th>Producto</th>
                            <th>Cantidad</th>
                        </template>

                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 :data="data[indextr].id_producto">
                                    {{data[indextr].id_producto}}
                                </vs-td2>

                                <vs-td2 :data="data[indextr].nombre">
                                    {{data[indextr].nombre}}
                                </vs-td2>

                                <vs-td2 :data="data[indextr].cantidad">
                                    {{data[indextr].cantidad}}
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
                </form>
            </div>
        </vs-popup>

    </div>

</vx-card>
</template>

<script>
import Multiselect from 'vue-multiselect'

import "vue-multiselect/dist/vue-multiselect.min.css"

import moment from "moment";
export default {

    components: {
        Multiselect,
    },
    data() {
        return {
            Lista_detalle_solicitud: [],
            Titulo_emergente: '',
            fecha_inicio: this.getDateValue(new Date()),
            fecha_final: this.getDateValue(new Date()),
            cb_lista_operacion: '',
            lista_estado: [{
                    ID: 'P',
                    DESCRIPCION: 'Proceso'
                },
                {
                    ID: 'A',
                    DESCRIPCION: 'Autorizado'
                },
                {
                    ID: 'R',
                    DESCRIPCION: 'Rechazado'
                }
            ],
            Id_estado_seleccionado: 0,
            Lista_cotizaciones_internas: [],
            Cotizacion_interna_selec: [],
            Lista_cotizaciones_externas: [],
            Estado_VentanaCotizacionExterna: false,
            Estado_VentanaCotizacionExternaReutilizada: false,
            Estado_VentanaCotExistente: false,
            Lista_proveedores: [],
            cb_proveedor: '',
            proveedor: {
                codigo: '',
                nombre: '',
                nit: ''
            },
            producto_buscar: '',

            Producto_seleccionado: {
                nombre: '',
                marca_comercial: '',
                Principio_activo: '',
                // Concentracion: '',
                Presentacion: '',
                Codigo: '',
                codigo_interno: '',
                Activo: 'N'
            },
            Id_clase_seleccionado: '1',
            Id_Sub_clase_seleccionado: '1',
            Estado_VentanaEmergente_Busqueda: false,
            producto: [],

            CotExt_Id_Cotizacion: 0,
            CotExt_Id_Solicitud: 0,
            CotExt_GranTotal: '',
            CotExt_Cant_lineas: '',
            CotExt_Observaciones: '',
            CotExt_Id_Bodega: 0,
            CotExt_Id_Departamento: '',
            CotExt_Id_ProductoClase: '',
            CotExt_Id_ProductoSubClase: '',
            CotExt_Tipo: '',
            CotExt_CantPagos: 0,
            CotExt_DiasCredito: 0,
            CotExt_Id_Proveedor: '',
            CotExt_Operacion: '',
            CotExt_Detalle: [{
                Id_Cotizacion_enc: '',
                Id_Detalle_cotizacion: '',
                Descripcion: '',
                IdProducto: '',
                Cantidad_solicitada: '',
                Cantidad_proveedor: '',
                Precio_unitario: '',
                Subtotal: '',
                Operacion: '',
            }],
            CotInt_Estado: '',
            Ingreso_Precio_unitario: '',
            Ingreso_Cantidad_proveedor: '',
            Ingreso_Cantidad_solicitada: '',
            NuevaCotizacion: '',
            Lista_solicitud: [],

            Lista_cotizaciones_externas_Anteriores: [],
            DescripcionDepto: '',
            IdSolicitud: '',
            Lista_solicitud_enc: [],
            IdSolicitudSeleccionado: [],
            Ventana_Detalle: false,
            Tolerancia: 0,
            ListaDepartamentos: [],
            cbDepartamento: '',
            IdDepartamento: '',
            Departamento: '',
            observaciones: '',
            subtotal: '',
            granTotal: 0,

        };
    },
    mounted() {
        this.Id_estado_seleccionado = 'P';
        this.cb_lista_operacion = {
            ID: 'P',
            DESCRIPCION: 'Proceso'
        }
        this.Consultar_cotizacion('I', this.Id_estado_seleccionado, '0');
        this.ConsultarDepartamentos();

    },

    methods: {
        getDateValue(value) {
            if (value === undefined) {
                return '';
            } else if (value === null) {
                return '';
            } else if (value == '') {
                return '';
            }
            return moment(value).format('YYYY-MM-DD');
        },
        Operacion_seleccionada({
            DESCRIPCION
        }) {
            return `${DESCRIPCION} `
        },
        onChangeEstado(value) {
            if (value !== null && value.length !== 0) {
                this.Id_estado_seleccionado = value.ID;
            } else {
                this.Id_estado_seleccionado = '';
            }
        },
        ConsultarDepartamentos() {

            this.axios.post('/app/v1_OrdenCompra/consulta_departamento', {})
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.ListaDepartamentos = "";
                    } else {
                        this.ListaDepartamentos = resp.data.json;
                    }
                })
                .catch(() => {})

        },
        onChangeDepartamento(value) {

            if (value !== null && value.length !== 0) {
                this.IdDepartamento = value.CODIGO;
                this.Departamento = value.NOMBRE;

            } else {
                this.IdDepartamento = '';
                this.Departamento = '';
            }
        },
        Departamento_seleccionada({
            CODIGO,
            NOMBRE
        }) {
            return `${CODIGO} - ${NOMBRE} `
        },
        Consultar_cotizacion(Tipo, Estado, IdSolicitud) {

            const url = this.$store.state.global.url
            const sesion = this.$store.state.sesion
            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/consultar_cotizacion_enc', {
                    Empresa: sesion.sesion_empresa,
                    tipo: Tipo,
                    estado: Estado,
                    fecha_inicio: this.fecha_inicio,
                    fecha_fin: this.fecha_final,
                    Solicitud_enc: IdSolicitud,
                    Departamento: this.IdDepartamento

                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        if (Tipo === 'I') {
                            this.Lista_cotizaciones_internas = [];
                            this.CotInt_Estado = "";
                            this.Lista_cotizaciones_externas = [];
                        } else {
                            this.Lista_cotizaciones_externas = [];
                        }

                    } else {
                        if (Tipo === 'I') {
                            this.Lista_cotizaciones_internas = resp.data.json;
                            this.CotInt_Estado = "";
                            this.Lista_cotizaciones_externas = [];

                        } else {
                            this.Lista_cotizaciones_externas = resp.data.json.map(m => {
                                return {
                                    ...m,
                                    TOTAL: parseFloat(m.TOTAL).toFixed(2)
                                }
                            })

                        }

                    }
                })
               

        },
        Consultar_cotizacionTodos() {

            const url = this.$store.state.global.url

            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/consultar_cotizacion_enc', {
                    Empresa: 'AME',
                    tipo: 'T',
                    estado: '',
                    fecha_inicio: '',
                    fecha_fin: '',
                    Solicitud_enc: 0
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.Lista_cotizaciones_externas_Anteriores = [];

                    } else {

                        this.Lista_cotizaciones_externas_Anteriores = resp.data.json;

                    }
                })
        },
        SeleccionarCotizacionInterna(tr) {
            this.CotInt_Estado = tr.ESTADO;
            this.Cotizacion_interna_selec = tr;
            this.CotExt_Id_Cotizacion = 0;
            this.CotExt_Id_Solicitud = tr.IDSOLICITUDENCFK;
            this.CotExt_Total = '';
            this.CotExt_Cant_lineas = 0;
            this.CotExt_Observaciones = '';
            this.CotExt_Id_Bodega = tr.IDBODEGAFK;
            this.CotExt_Id_Departamento = tr.IDDEPARTAMENTOFK;
            this.CotExt_Id_ProductoClase = tr.IDPRODUCTOCLASEFK;
            this.CotExt_Id_ProductoSubClase = tr.IDPRODUCTOSUBFK;
            this.CotExt_Tipo = 'E';
            this.Consultar_cotizacion('E', 'P', this.CotExt_Id_Solicitud);
            this.DescripcionDepto = tr.DESCRIPCION_DEPTO
            this.IdSolicitud = tr.IDSOLICITUDENCFK

        },
        Buscar_cotizacionExistente() {
            this.Estado_VentanaCotExistente = true;
            this.Titulo_emergente = "Cotizaciones anteriores";
            this.Consultar_cotizacionTodos();
        },
        CotizacionProveedor() {

            if (this.Cotizacion_interna_selec.IDSOLICITUDENCFK > 0) {
                this.CotExt_Operacion = 'N';
                this.Estado_VentanaCotizacionExterna = true;
                this.Titulo_emergente = "Nueva cotización";
                this.NuevaCotizacion = "S";
                this.Id_clase_seleccionado = this.Cotizacion_interna_selec.IDPRODUCTOCLASEFK;

                if (this.Lista_proveedores == null || this.Lista_proveedores == '') {
                    this.Consultar_proveedor();
                }
                this.observaciones = ''
                this.proveedor = [];
                this.cb_proveedor = '';
                this.CotExt_GranTotal = '';
                this.CotExt_CantPagos = ''
                this.CotExt_DiasCredito = '';
                this.producto_buscar = '';
                this.Ingreso_Precio_unitario = '';
                this.Ingreso_Cantidad_solicitada = '';
                this.Ingreso_Cantidad_proveedor = '';
                this.CotExt_Detalle = [];
                this.Limpiar_Campos();

            }

        },
        Consultar_proveedor() {

            const url = this.$store.state.global.url
            const sesion = this.$store.state.sesion;
            this.axios.post(url + 'app/v1_OrdenCompra/consulta_proveedor', {
                    empresa: sesion.sesion_empresa
                })
                .then(resp => {

                    if (resp.data.codigo != 0) {

                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Proveedores',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.Lista_proveedores = "";
                    } else {
                        //Decodificación
                        this.Lista_proveedores = resp.data.json;

                    }
                })
                .catch(() => {})
        },
        seleccion_proveedor({
            NIT,
            NOMBRE,
        }) {
            return `${NIT} - ${NOMBRE} `
        },
        onChangeProveedor(value) {
            if (value !== null && value.length !== 0) {

                this.proveedor.codigo = value.CODIGO,
                    this.proveedor.nombre = value.NOMBRE.toUpperCase();
                this.proveedor.nit = value.NIT;

            } else {
                this.proveedor.codigo = '';
                this.proveedor.nombre = '';
                this.proveedor.nit = '';
            }
        },
        Limpiar_Campos() {
            this.Producto_seleccionado.Codigo = '';
            this.Producto_seleccionado.marca_comercial = '';
            this.Producto_seleccionado.Principio_activo = '';
            this.Producto_seleccionado.codigo_interno = '';
            this.Producto_seleccionado.Presentacion = '';
        },

        cargar_producto: function () {
            this.Limpiar_Campos();
            this.$vs.loading();
            this.axios.post('/app/inventario/invProductolist', {
                    Pagina: 1,
                    Busqueda: '',
                    Clase: this.CotExt_Id_ProductoSubClase,
                    SubClase: 0,
                    accion: 'A1',
                    Nombre: this.producto_buscar.trim(),
                    Departamento: this.CotExt_Id_Departamento,
                })
                .then(resp => {
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        this.producto = []
                        resp.data.json.map(data => {
                            this.producto.push({
                                ...data
                            })
                        })

                        this.Mostrar_resultado_producto(this.producto);

                    } else {
                        this.producto = []
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Producto',
                            text: (resp.data.codigo < 0) ? resp.data.descripcion : 'Sin Registros',
                        })
                    }
                })
        },
        precio_uni: function () {
            this.Ingreso_Precio_unitario = (parseFloat(this.subtotal / this.Ingreso_Cantidad_proveedor).toFixed(2))
        },
        Mostrar_resultado_producto(value) {

            if (value.length == 1) {
                this.Producto_seleccionado.Codigo = value.find(obj => obj.Codigo != '').Codigo;
                this.Producto_seleccionado.marca_comercial = value.find(obj => obj.Nombre != '').Nombre;
                this.Producto_seleccionado.Principio_activo = value.find(obj => obj.Principio_activo != '').Principio_activo;
                this.Producto_seleccionado.codigo_interno = value.find(obj => obj.codigo_interno != '').codigo_interno;
                this.Producto_seleccionado.Presentacion = value.find(obj => obj.PresentacionNombre != '').PresentacionNombre;
            } else if (value.length > 1) {
                this.Estado_VentanaEmergente_Busqueda = true;
            }

        },
        Seleccionar_Producto(value) {
            this.producto = [];
            this.Producto_seleccionado.Codigo = value.Codigo;
            this.Producto_seleccionado.marca_comercial = value.Nombre;
            this.Producto_seleccionado.Principio_activo = value.Principio_activo;
            this.Producto_seleccionado.codigo_interno = value.codigo_interno;
            this.Producto_seleccionado.Presentacion = value.PresentacionNombre;
            this.Estado_VentanaEmergente_Busqueda = false;
            

        },
        Agregar_Detalle() {

            if (this.Producto_seleccionado.Codigo === 0 || this.Producto_seleccionado.Codigo === '' || this.Producto_seleccionado.Codigo === null || this.Producto_seleccionado.Codigo === undefined) {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Cotización',
                    text: 'Seleccionar producto.',
                })
                return;
            }
            if (this.Ingreso_Precio_unitario === 0 || this.Ingreso_Precio_unitario === '') {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Cotización',
                    text: 'Ingresar Precio Unitario',
                })
                return;
            }
            if (this.Ingreso_Cantidad_proveedor === 0 || this.Ingreso_Cantidad_proveedor === '') {

                this.$vs.notify({
                    color: 'danger',
                    title: 'Cotización',
                    text: 'Ingresar Cantidad',
                })
                return;
            }
            this.ValidacionTolerancia()

        },
        ValidacionTolerancia() {

            this.axios.post('/app/inventario/MargenTolerancia', {
                    PrecioProducto: this.Ingreso_Precio_unitario,
                    Codigo: this.Producto_seleccionado.Codigo,
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.Tolerancia = 0

                        if (this.Tolerancia == 0) {
                            if ((!this.CotExt_Detalle.some(data => data.IdProducto === this.Producto_seleccionado.Codigo))) {
                                this.CotExt_Detalle.push({
                                    Id_Cotizacion_enc: 0,
                                    Id_Detalle_cotizacion: 0,
                                    IdProducto: this.Producto_seleccionado.Codigo,
                                    Descripcion: (this.Producto_seleccionado.marca_comercial + ' - ' + this.Producto_seleccionado.Presentacion),
                                    Cantidad_solicitada: 0,
                                    Precio_unitario: this.Ingreso_Precio_unitario,
                                    Cantidad_proveedor: this.Ingreso_Cantidad_proveedor,
                                    Subtotal: (this.Ingreso_Precio_unitario * this.Ingreso_Cantidad_proveedor),
                                    Operacion: 'N'
                                })
                                //}
                            } else {
                                //Actualización Producto
                                const index_ = this.CotExt_Detalle.findIndex(data => data.IdProducto === this.Producto_seleccionado.Codigo);
                                this.CotExt_Detalle[index_].Cantidad_proveedor = (Number(this.CotExt_Detalle[index_].Cantidad_proveedor) + Number(this.Ingreso_Cantidad_proveedor));
                                this.CotExt_Detalle[index_].Subtotal = (Number(this.Ingreso_Precio_unitario) * Number(this.CotExt_Detalle[index_].Cantidad_proveedor));
                                this.CotExt_Detalle[index_].Precio_unitario = this.Ingreso_Precio_unitario;
                                this.CotExt_Detalle[index_].Operacion = 'A';
                            }
                            var total = 0;
                            this.CotExt_Detalle.forEach(element => {
                                total += (Number(element.Subtotal));
                            });

                            this.CotExt_GranTotal = (parseFloat(total).toFixed(2));
                        } else {
                            this.Tolerancia = 1
                            return;
                        }
                    }
                    this.Producto_seleccionado = [];
                    this.Ingreso_Precio_unitario = '';
                    this.Ingreso_Cantidad_proveedor = '';
                    this.Ingreso_Cantidad_solicitada = '';
                    this.Limpiar_Campos();
                    this.producto_buscar = '';
                    this.subtotal = ''
                })
                .catch(() => {
                    this.Tolerancia = 1
                    this.Producto_seleccionado = [];
                    this.Ingreso_Precio_unitario = '';
                    this.Ingreso_Cantidad_proveedor = '';

                    this.Limpiar_Campos();
                    this.producto_buscar = '';
                })

        },
        EliminarProducto(index, datos) {
            /**
             * @General
             * Función eliminar registro;
             */
            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Confirmación',
                text: '¿Eliminar detalle? ',
                accept: () => {
                    this.CotExt_GranTotal = (this.CotExt_GranTotal - datos.Subtotal)
                    this.$delete(this.CotExt_Detalle, index)
                }
            })
        },
        Confirmacion_Transaccion() {

            if (!this.CotExt_Id_Departamento > 0) {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Cotización',
                    text: 'Seleccionar departamento',
                })
                return;
            }
            if (!this.proveedor.codigo > 0) {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Cotización',
                    text: 'Seleccionar proveedor',
                })
                return;
            }
            if (!this.CotExt_GranTotal > 0) {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Cotización',
                    text: 'Ingrese Monto',
                })
                return;
            }

            if (!this.CotExt_DiasCredito > 0) {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Cotización',
                    text: 'Ingrese Días de Crédito',
                })
                return;
            }
            if (!this.CotExt_CantPagos > 0) {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Cotización',
                    text: 'Ingrese Pagos',
                })
                return;
            }
            if (!this.granTotal > 0) {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Cotización',
                    text: 'Ingrese Monto',
                })
                return;
            }


            var total = 0;
            this.CotExt_Detalle.forEach(element => {
                total += (Number(element.Subtotal));
            });

            if (total <= 0) {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Cotización',
                    text: 'Ingresar detalle cotización',
                })
                return;
            }
            if (Number(this.granTotal) == Number(this.CotExt_GranTotal)) {
                this.CotExt_GranTotal = Number(total)
            } else {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Cotización',
                    text: 'No cuadra Monto ingresado con el Monto Cotización o Monto total del detalle',
                })
                return;
            }
            this.$vs.loading();
            if (this.CotExt_Id_Cotizacion === '' || this.CotExt_Id_Cotizacion === "") {
                this.CotExt_Id_Cotizacion = 0;
            }
            //Convierte el detalle de la cotización en json
            var JsonDetalle = JSON.stringify(this.CotExt_Detalle);
            const sesion = this.$store.state.sesion
            this.axios.post('/app/v1_OrdenCompra/cotizacion', {
                    IdCotizacionEnc: this.CotExt_Id_Cotizacion,
                    IdSolicitudEnc: this.Cotizacion_interna_selec.IDSOLICITUDENCFK,
                    Empresa: sesion.sesion_empresa,
                    total: total,
                    cant_lineas: this.CotExt_Detalle.length,
                    observaciones: this.observaciones,
                    Departamento: this.CotExt_Id_Departamento,
                    Bodega: this.CotExt_Id_Bodega,
                    ProductoClase: this.CotExt_Id_ProductoClase,
                    ProductoSubClase: this.CotExt_Id_ProductoSubClase,
                    corporativo: sesion.corporativo,
                    estado: 'P',
                    tipo: 'E',
                    cant_pagos_propuesta: this.CotExt_CantPagos,
                    dias_credito: this.CotExt_DiasCredito,
                    IdProveedor: this.proveedor.codigo,
                    operacion: this.CotExt_Operacion,
                    datos: JsonDetalle

                })
                .then(resp => {
                
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cotización',
                            text: resp.data.mensaje,
                        })
                       
                    } else {
                        this.Estado_VentanaCotizacionExterna = false;
                        this.Limpiar_Campos_Cotizacion();
                        this.Limpiar_Campos()
                        this.Consultar_cotizacion('E', 'P', this.Cotizacion_interna_selec.IDSOLICITUDENCFK);
                    }
                })
                
        },
        Confirmacion_TransaccionReutilizar() {
          
            var total = 0;
            this.CotExt_Detalle.forEach(element => {
                total += (Number(element.Subtotal));
            });
            if (total <= 0) {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Cotización',
                    text: 'Ingresar detalle cotización',
                })
                return;
            }

            if (Number(total) === Number(this.CotExt_GranTotal)) {
                this.CotExt_GranTotal = Number(this.CotExt_GranTotal);
            } else {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Cotización',
                    text: 'Monto total incorrecto',
                })
                return;
            }
            this.$vs.loading();
            if (this.CotExt_Id_Cotizacion === '' || this.CotExt_Id_Cotizacion === "") {
                this.CotExt_Id_Cotizacion = 0;
            }
            // Actualiza los campos del array detalle
            for (const obj of this.CotExt_Detalle) {
                obj.Id_Cotizacion_enc = 0;
                obj.Operacion = 'N';
            }
            //Convierte el detalle de la cotización en json
            var JsonDetalle = JSON.stringify(this.CotExt_Detalle);
            const sesion = this.$store.state.sesion
            this.axios.post('/app/v1_OrdenCompra/cotizacion', {
                    IdCotizacionEnc: 0,
                    IdSolicitudEnc: this.Cotizacion_interna_selec.IDSOLICITUDENCFK,
                    Empresa: sesion.sesion_empresa,
                    total: total,
                    cant_lineas: this.CotExt_Detalle.length,
                    observaciones: this.CotExt_Observaciones,
                    Departamento: this.CotExt_Id_Departamento,
                    Bodega: this.CotExt_Id_Bodega,
                    ProductoClase: this.CotExt_Id_ProductoClase,
                    ProductoSubClase: this.CotExt_Id_ProductoSubClase,
                    corporativo: sesion.corporativo,
                    estado: 'P',
                    tipo: 'E',
                    cant_pagos_propuesta: this.CotExt_CantPagos,
                    dias_credito: this.CotExt_DiasCredito,
                    IdProveedor: this.proveedor.codigo,
                    operacion: 'N',
                    datos: JsonDetalle

                })
                .then(resp => {
                 
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cotización',
                            text: resp.data.mensaje,
                        })

                    } else {
                        this.Estado_VentanaCotizacionExterna = false;
                        this.Estado_VentanaCotizacionExternaReutilizada = false;
                        this.Limpiar_Campos_Cotizacion();
                        this.Limpiar_Campos()
                        this.Consultar_cotizacion('E', 'P', this.Cotizacion_interna_selec.IDSOLICITUDENCFK);
                    }
                })
                

        },

        Limpiar_Campos_Cotizacion() {

            this.CotExt_Id_Cotizacion = '';
            this.CotExt_GranTotal = '';
            this.CotExt_CantPagos = '';
            this.CotExt_DiasCredito = '';
            this.CotExt_Cant_lineas = '';
            this.CotExt_Observaciones = '';
            this.CotExt_Tipo = 'E';
            this.proveedor = [];
            this.Producto_seleccionado = [];
            this.Ingreso_Precio_unitario = '';
            this.Ingreso_Cantidad_proveedor = '';
            this.Ingreso_Cantidad_solicitada = '';
        },
        Consultar_solicitud(IdSolicitud) {

            const url = this.$store.state.global.url
            const sesion = this.$store.state.sesion
            sesion.depto_seleccionado

            this.axios.post(url + 'app/v1_OrdenCompra/consultar_solicitud_compra', {
                    empresa: sesion.sesion_empresa,
                    id_departamento: '0',
                    id_bodega: '0',
                    tipo_consulta: 'E',
                    estado: '',
                    codigo: IdSolicitud
                })
                .then(resp => {
                  
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cotización',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.Lista_solicitud_enc = [];
                    } else {
                        this.Lista_solicitud_enc = resp.data.json;
                        this.Generar_detalle(this.Lista_solicitud_enc);

                    }
                })
                

        },
        async Generar_detalle(datos) {

            const url = this.$store.state.global.url
            this.Lista_detalle_solicitud = [];
            this.IdSolicitudSeleccionado = datos.find(obj => obj.CODIGO != '').CODIGO;

            this.axios.post(url + 'app/v1_OrdenCompra/consultar_solicitud_compra_det', {
                    id_empresa: datos.find(obj => obj.EMPRESA != '').EMPRESA,
                    id_solicitud_enc: datos.find(obj => obj.CODIGO != '').CODIGO,
                    tipo_transaccion: 'T'
                })
                .then(resp => {
                   
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cotización',
                            text: resp.data.mensaje,
                        })

                    } else {
                        this.Lista_detalle_solicitud = resp.data.json;
                        this.Ventana_Detalle = true
                    }
                })
                

        },

        Limpiar() {

            this.producto_buscar = '',
            this.Limpiar_Campos(),
            this.Ingreso_Cantidad_proveedor = '',
            this.subtotal = ''
            this.Ingreso_Precio_unitario = ''
            
        },
        GenerarCotizacion(datos) {

            /**CONSULTAR DETALLE COTIZACION SELECCIONADA */

            const url = this.$store.state.global.url
            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/consultar_cotizacion_det', {
                    IdCotizacionEnc: datos.IDCOTIZACIONENC
                })
                .then(resp => {
                 
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cotización',
                            text: resp.data.mensaje,
                        })

                    } else {

                        this.GenerarPDFCotizacion(resp.data.json, datos)

                    }
                })
               
        },
        onChangeGranTotal: function() {
            this.granTotal = this.granTotal != '' ? parseFloat(this.granTotal).toFixed(2) : ''
        },
        onChangeSubTotal: function() {
            this.subtotal = this.subtotal != '' ? parseFloat(this.subtotal).toFixed(2) : ''
        }
    }


}
</script>

<style scoped>
.label-sizem {
    font-size: 16px;
    font-weight: bold;
}
</style>
