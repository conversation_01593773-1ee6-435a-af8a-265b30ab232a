<template>
<div>
    <form @submit="handleSubmit">
        <DxForm :ref="formEliminacion" :form-data.sync="formulario" label-mode="floating" @field-data-changed="BuscarCheques">
            <DxFormGroupItem :col-count="2">
                <DxFormItem data-field="Cuenta" editor-type="dxSelectBox" :editor-options="{ width: 'auto', searchEnabled: true, items: cuentas,  displayExpr: 'Valor', valueExpr: 'Codigo' }" :validationRules="[{ type: 'required' }]" />
                <DxFormItem data-field="Periodo" editor-type="dxSelectBox" :editor-options="{ width: 'auto', searchEnabled: true, items: periodos,  displayExpr: 'Valor', valueExpr: 'Codigo', onSelectionChanged: AsignarPeriodo }" :validationRules="[{ type: 'required' }]" />
            </DxFormGroupItem>
            <DxFormButtonItem :button-options="buttonGenerar" :visible="formulario.Cuenta !== null && formulario.Periodo !== null" name="Eliminar" horizontal-alignment="center" verical-alignment="center" />
            <DxFormGroupItem>
                <DxFormItem template="cheques" />
            </DxFormGroupItem>

            <template #cheques="{}">
                <div class="pb-2">
                    <DxDataGrid :ref="gridCheques" v-bind="DefaultDxGridConfiguration" :data-source="cheques" :paging="{ enabled: false }" :searchPanel="{ visible: false} " :headerFilter="{ visible: false, allowSearch: false }" :width="'100%'" height="170">
                        <DxDataGridSelection mode="single" />
                        <DxDataGridScrolling mode="virtual" />

                        <DxDataGridColumn width="30%" data-field="Nombre" alignment="center" data-type="string">
                            <DxFormLabel test="Cuenta" />
                        </DxDataGridColumn>
                        <DxDataGridColumn width="10%" data-field="Numero" alignment="center" data-type="string" />
                        <DxDataGridColumn width="10%" data-field="Monto" alignment="center" data-type="string" :customize-text="NumeroComas" />
                        <DxDataGridColumn width="50%" data-field="NombreBeneficiario" alignment="center" data-type="string">
                            <DxFormLabel test="Beneficiario" />
                        </DxDataGridColumn>
                    </DxDataGrid>
                </div>
            </template>
        </DxForm>
    </form>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'
import 'devextreme-vue/lookup'

// import DxButton from 'devextreme-vue/button'

const formEliminacion = 'formEliminacion'
const gridCheques = 'gridCheques'

export default {
    name: 'Eliminacion',
    components: {},
    data() {
        return {
            DefaultDxGridConfiguration,
            formEliminacion,
            gridCheques,

            formulario: {
                Cuenta: null, // Codigo de la cuenta de banco de 2 dígitos
                Periodo: null,
                NumeroCuenta: null, // Número de la cuenta bancaria
                BancoContabilidad: null, // Cuenta contable de la cuenta bancaria seleccionada
                NombreCuentaBanco: null, // Nombre de la cuenta contable de la cuenta bancaria
            },
            cuentas: [],
            periodos: [],
            cheques: [],

            buttonGenerar: {
                width: 'auto',
                icon: 'fas fa-check',
                text: 'Generar',
                type: 'success',
                onClick: () => {
                    this.Generar()
                },
                useSubmitBehavior: false,
            },

            infoPeriodo: null,

            periodo: null,
            opciones: null,
            chequesLiberar: []
        }
    },
    props: {
        ModalidadPago: null,
        TipoCheque: null,
        ImpresionLotes: null,
        Corporativo: null
    },
    methods: {
        handleSubmit(e) {
            e.preventDefault()
        },

        CargarCuentas() {
            this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 1
                })
                .then(resp => {
                    this.cuentas = resp.data.json

                    this.formulario.Cuenta = this.cuentas[0].Codigo
                })
        },

        CargarPeriodos() {
            this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 3,
                })
                .then(resp => {
                    this.periodos = resp.data.json

                    this.formulario.Periodo = this.periodos[0].Codigo

                })
        },

        CargarOpciones() {
            this.axios.post('/app/v1_bancos/Opciones', {
                    Opcion: 1
                })
                .then(resp => {
                    this.opciones = resp.data.json[0]
                })
        },

        BuscarCheques() {
            if (this.formulario.Cuenta !== null && this.formulario.Periodo !== null && this.infoPeriodo !== null) {
                this.cheques = []
                this.axios.post('/app/v1_bancos/Vencimientos', {
                        Opcion: 1,
                        Cuenta: this.formulario.Cuenta,
                        Periodo: this.formulario.Periodo,
                        TipoCheque: this.TipoCheque,
                        Fecha: new Date(this.infoPeriodo.FechaFinal)
                    })
                    .then(resp => {
                        if (resp.data.json.length > 0) {
                            this.cheques = resp.data.json
                        }
                    })
            }
        },

        async BuscarChequesLiberar() {
            await this.axios.post('/app/v1_bancos/Vencimientos', {
                    Opcion: 2,
                    Cuenta: this.formulario.Cuenta,
                    Periodo: parseInt(this.formulario.Periodo - this.opciones.MesesVigencia),
                    TipoCheque: this.TipoCheque,
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.chequesLiberar = resp.data.json
                    }
                })
        },

        AsignarPeriodo(e) {
            this.infoPeriodo = e.selectedItem
        },

        NumeroComas(cellInfo) {
            let x = parseFloat(cellInfo.value).toFixed(2)
            return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        },

        async Generar() {
            let saldoFinal = await this.ConsultarSaldoFinal()

            if (!saldoFinal) {
                await this.BuscarChequesLiberar()

                for (let i = 0; i < this.chequesLiberar.length; i++) {
                    await this.LiberarDocumentos(this.chequesLiberar[i])
                }
                this.Vencimiento()
            }
        },

        async ConsultarSaldoFinal() {
            return await this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 2,
                    Cuenta: this.formulario.Cuenta,
                    Periodo: this.formulario.Periodo
                })
                .then(resp => {
                    if (resp.data.json.length === 0) {
                        return false
                    } else if (resp.data.json[0].SaldoFinal !== null) {
                        let text = 'El movimiento de la cuenta bancaria de ' + this.infoPeriodo.Descripcion + ' ya está cerrado, debe inicializarlo.'
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Período no activo',
                            acceptText: 'Aceptar',
                            text: text,
                            buttonCancel: 'border',
                            accept: () => {
                            },
                        })
                        return true
                    }
                })
        },

        async Vencimiento() {
            let vencimiento = {
                ActualizarBancosMovtos: {},
                BancosMovtos: {},
                AuxiliarBancos: {},
                Conciliado: {}
            }

            vencimiento.ActualizarBancosMovtos = {
                Opcion: 3,
                Fecha: this.infoPeriodo.FechaFinal,
                Usuario: this.Corporativo,
                Cuenta: this.formulario.Cuenta,
                Periodo: parseInt(this.formulario.Periodo - this.opciones.MesesVigencia)
            }

            vencimiento.BancosMovtos = {
                Opcion: 4,
                Cuenta: this.formulario.Cuenta,
                Periodo: this.formulario.Periodo,
                PeriodoCheques: parseInt(this.formulario.Periodo - this.opciones.MesesVigencia),
                Fecha: this.infoPeriodo.FechaFinal,
                Usuario: this.Corporativo,
            }

            vencimiento.AuxiliarBancos = {
                Opcion: 5,
                Cuenta: this.formulario.Cuenta,
                Periodo: this.formulario.Periodo,
                PeriodoCheques: parseInt(this.formulario.Periodo - this.opciones.MesesVigencia),
                Fecha: this.infoPeriodo.FechaFinal,
                Usuario: this.Corporativo,
            }

            vencimiento.Conciliado = {
                Opcion: 6,
                Cuenta: this.formulario.Cuenta,
                PeriodoCheques: parseInt(this.formulario.Periodo - this.opciones.MesesVigencia),
            }

            await this.axios.post('/app/v1_bancos/VencimientosAgrupados', {
                    ...vencimiento
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Vencimiento registrados',
                            acceptText: 'Aceptar',
                            text: 'Se han registrado correctamente los vencimientos.',
                            buttonCancel: 'border',
                            accept: () => {
                                return true
                            },
                        })
                        return
                    }
                })
        },

        async LiberarDocumentos(cheque) {
            let liberar = {
                Referencias: [],
                Cupones: [],
                Multiniveles: [],
                EmpAnticipo: {},
                Pago: {},
                SaldoFactura: {},
                ActualizarPagos: {},
                Radiologos: {},
                EliminarPagos: {},
                Ajenos: {},
                Saldos: {},
                ProntoPago: {},
                Bonificaciones: {},
                EliminarBonificaciones: {},
                Pagos: {}
            }

            let comprasPagos = await this.BuscarComprasPagos(cheque)

            if (comprasPagos !== null) {
                for (let i = 0; i < comprasPagos.length; i++) {
                    let nuevo = {
                        Cuenta: null,
                        Cheque: null,
                        ProveedorInicio: comprasPagos[i].Proveedor,
                        Documento: comprasPagos[i].Documento,
                    }
                    switch (comprasPagos[i].Tipo) {
                        case 'C':
                            nuevo.Opcion = 9
                            liberar.Referencias.push(nuevo)
                            break;

                        case 'U':
                            nuevo.Opcion = 10
                            liberar.Cupones.push(nuevo)
                            break;

                        case 'G':
                            nuevo.Opcion = 11
                            liberar.Multiniveles.push(nuevo)
                            break;
                    }
                }
            }

            if (cheque.TipoCheque == 'E') {

                // En Delphi la actualización de EmpAnticipos (Opcion 11) y EmpPagos (Opcion 13) están en la misma consulta, pero aquí se dejasn separados porque ya existían los método para otro proceso pero de manera individual
                // --------------------------------->
                liberar.EmpAnticipo = {
                    Opcion: 11,
                    EmpresaCheque: cheque.EmpesaEspecial == null ? cheque.Empresa : cheque.EmpresaEspecial,
                    Cuenta: cheque.Cuenta,
                    ChequeInicial: cheque.Numero
                }

                liberar.Pago = {
                    Opcion: 13,
                    EmpresaCheque: cheque.EmpesaEspecial == null ? cheque.Empresa : cheque.EmpresaEspecial,
                    Cuenta: cheque.Cuenta,
                    ChequeInicial: cheque.Numero
                }
                // --------------------------------->

                liberar.SaldoFactura = {
                    Opcion: 12,
                    Cuenta: cheque.Cuenta,
                    ChequeInicial: cheque.Numero
                }

                liberar.ActualizarPagos = {
                    Opcion: 1,
                    Cuenta: cheque.Cuenta,
                    TipoCheque: this.TipoCheque,
                    Cheque: cheque.Numero,
                    Tipo: 'P'
                }
            }

            if (cheque.TipoCheque == 'R' || this.chequeInterpretacion) {
                liberar.Radiologos = {
                    Opcion: 17,
                    Cuenta: cheque.Cuenta,
                    ChequeInicial: cheque.Numero,
                }

                liberar.EliminarPagos = {
                    Opcion: 2,
                    Cuenta: cheque.Cuenta,
                    Cheque: cheque.Numero,
                    TipoCheque: this.TipoCheque,
                    Tipo: 'P'
                }
            }

            if (cheque.TipoCheque == 'A' || cheque.TipoCheque == 'M') {
                liberar.Ajenos = {
                    Opcion: 21,
                    Cuenta: cheque.Cuenta,
                    ChequeInicial: cheque.Numero,
                }

                liberar.Saldos = {
                    Opcion: 15,
                    Cuenta: cheque.Cuenta,
                    ChequeInicial: cheque.Numero,
                    TipoCheque: this.TipoCheque
                }

                liberar.ProntoPago = {
                    Opcion: 7,
                    TipoCheque: this.TipoCheque,
                    Cuenta: cheque.Cuenta,
                    ChequeInicial: cheque.Numero,
                }

                liberar.ProntoPago = {
                    Opcion: 3,
                    Cuenta: cheque.Cuenta,
                    Cheque: cheque.Numero,
                }

                liberar.EliminarPagos = {
                    Opcion: 2,
                    Cuenta: cheque.Cuenta,
                    Cheque: cheque.Numero,
                    TipoCheque: this.TipoCheque,
                    Tipo: 'P'
                }
            }

            if (cheque.TipoCheque == 'B') {

                // Deja las bonificaciones sin cheque
                liberar.Bonificaciones = {
                    Opcion: 19,
                    TipoCheque: this.TipoCheque,
                    Cuenta: cheque.Cuenta,
                    ChequeInicial: cheque.Numero,
                }

                liberar.EliminarBonificaciones = {
                    Opcion: 20,
                    TipoCheque: this.TipoCheque,
                    Cuenta: cheque.Cuenta,
                    ChequeInicial: cheque.Numero,
                }

                liberar.EliminarPagos = {
                    Opcion: 2,
                    Cuenta: cheque.Cuenta,
                    Cheque: cheque.Numero,
                    TipoCheque: this.TipoCheque,
                    Tipo: 'P'
                }
            }

            if (cheque.TipoCheque == 'P') {
                liberar.Saldos = {
                    Opcion: 15,
                    Cuenta: cheque.Cuenta,
                    ChequeInicial: cheque.Numero,
                    TipoCheque: this.TipoCheque
                }
                liberar.Pagos = {
                    Opcion: 4,
                    TipoCheque: this.TipoCheque,
                    Cuenta: cheque.Cuenta,
                    Cheque: cheque.Numero,
                }

                liberar.EliminarPagos = {
                    Opcion: 2,
                    Cuenta: cheque.Cuenta,
                    Cheque: cheque.Numero,
                    TipoCheque: this.TipoCheque,
                    Tipo: 'P'
                }
            }

            await this.axios.post('/app/v1_bancos/LiberarDocumentos', {
                    ...liberar
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        return false
                    } else {
                        return true
                    }
                })
        },

        async BuscarComprasPagos(cheque) {
            return await this.axios.post('/app/v1_bancos/Eliminacion', {
                    Opcion: 2,
                    TipoCheque: cheque.Tipo,
                    Cuenta: cheque.Cuenta,
                    ChequeInicial: cheque.Numero,
                    ChequeFinal: cheque.Numero
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        return resp.data.json
                    }
                    return null
                })
        },

        LimpiarVariables() {
            this.formulario.ChequeInicial = null
            this.formulario.ChequeFinal = null
            this.formulario.Inicial = null
            this.formulario.Final = null
        },

        ConsultarPeriodo() {
            this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 1,
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.periodo = resp.data.json[0].Codigo
                    }
                })
        },

    },
    created() {},
    mounted() {
        this.BuscarCheques()
    },
    beforeMount() {
        this.CargarCuentas()
        this.CargarPeriodos()
        this.ConsultarPeriodo()
        this.CargarOpciones()
    },
    watch: {},
    computed: {
        formEliminacionInstance: function () {
            return this.$refs[formEliminacion].instance;
        },

        dataGridCheques: function () {
            return this.$refs[gridCheques].instance;
        },
    }
}
</script>

<style>
        </style>
