<template>
    <div>
        <!-- Buscador de corporativos -->
        <buscador ref="BuscarUsuarioCorporativo" 
            style="z-index:9999999"
            buscador_titulo="Buscador / Corporativo" :api="'app/Ajenos/Busqueda_Corporativo'" 
            :campos="['Corporativo', ['Nombres', 'Nombre'], ['Apellidos', 'Apellido']]"
            :titulos="['Corporattivo', 'Nombres', 'Apellidos']"
            :api_filtro="{ Corporativo:corporativoHuella}"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" :api_preload="true"  />

        <!-- Validacion de huella -->
        <DxPopup :visible.sync="popUpSensorHuellas" :width="'60%'" :height="'60%'" 
                :show-title="true" :full-screen="false" :hide-on-outside-click="false"        
                title="Validación de Huella" :showCloseButton="true">
            <ValidarHuella ref="validarHuellas"  tipoHuella="1" :corporativoBuscar="corporativoHuella" @getValidacionHuella="getValidacionHuella"/>
        </DxPopup>  

        <!-- Ingreso de corporativo para validar huella -->
        <vs-popup  :title="tituloHuella" :active.sync="popUpBusquedaCorporativo"  id="div-with-loading" class="vs-con-loading__container aje042" style="z-index: 50;">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px;" >              
                    <vs-row vs-align="center">
                            <vs-input label="Corporativo:" v-model="corporativoHuella" @keyup.enter="BuscarCorporativo(false)" @keydown.tab="BuscarCorporativo(false)" ></vs-input>
                            <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="BuscarCorporativo(false)" icon="fa-search" style="margin-top: 16px;"></vs-button>
                            <vs-button id="button-with-loading" color="danger" v-if="usuario" icon-pack="fa" @click="usuario=null; corporativoHuella=null;" icon="fa-times" style="margin-top: 16px;"></vs-button>
                            <div style="margin-left:20px;">
                                <h5>{{ usuario?.Nombres}} {{usuario?.Apellidos}} </h5>
                            </div>
                    </vs-row>
                    <vs-divider></vs-divider>

                    <vs-row class="w-full" vs-align="left">
                        <vs-button color="primary" style="float:right" type="filled" icon-pack="feather" icon="icon-save" :disabled="usuario==null" @click="ValidaHuellaActiva()">Validar</vs-button>
                        <vs-button color="warning"  style="float:right; margin-left:10px;" @click="popUpBusquedaCorporativo = false; corporativoHuella=null; usuario=null;">Cancelar</vs-button>                   
                    </vs-row>
            </div>
        </vs-popup>
    </div>    
</template>
<script>

    import ValidarHuella from '/src/components/validador-huella/ValidarHuella.vue'

    export default {
        components: {
            ValidarHuella
        },
        data() {
            return {
                popUpBusquedaCorporativo:false,
                popUpSensorHuellas:false,                
                corporativoHuella:null,
                huellaValidada:0,
                usuario:null
            }
        },
        props: {
            tituloHuella: {
                type: String,
                default: 'Corporativo Huella'
            }
        },
        methods: {
            BuscarCorporativo(){     
                if(this.corporativoHuella != '' && this.corporativoHuella != null && this.corporativoHuella != 0){           
                    this.axios.post('/app/administracion/CorporativoConsulta', {
                        IdCorporativo:  this.corporativoHuella
                    })
                    .then(resp => {
                        if(resp.data.codigo == 0){
                            if(resp.data.json.length == 1){
                                    this.corporativoHuella = resp.data.json[0].Corporativo
                                    this.usuario = resp.data.json[0]                                
                            }else{
                                this.$refs.BuscarUsuarioCorporativo.iniciar((data) => {
                                    if(data != null){
                                        this.axios.post('/app/administracion/CorporativoConsulta', {
                                            IdCorporativo: data.Corporativo
                                        })
                                        .then(resp => {
                                            this.corporativoHuella = resp.data.json[0].Corporativo
                                            this.usuario = resp.data.json[0]
                                        })
                                    }
                                })
                            }
                        }
                    })
                }else{
                    this.$refs.BuscarUsuarioCorporativo.iniciar((data) => {
                        if(data != null){
                            this.axios.post('/app/administracion/CorporativoConsulta', {
                                IdCorporativo: data.Corporativo
                            })
                            .then(resp => {
                                this.corporativoHuella = resp.data.json[0].Corporativo
                                this.usuario = resp.data.json[0]
                            })
                        }
                    })
                }
            },
            ValidarHuella(Corporativo){
                this.popUpSensorHuellas = true                
                this.$refs.validarHuellas.activarValidador(Corporativo)
            },
            ValidaHuellaActiva(){
                if(this.corporativoHuella == '' || this.corporativoHuella == null || this.corporativoHuella == 0){ 
                    this.$vs.notify({
                                            title:'Validacion Huella',
                                            text:'No se ingreso un corporativo valido',
                                            timer:6000,
                                            color:'danger',
                                            position: 'top-center'
                                        })
                    return;                                        
                }



                this.axios.post('/app/huella/ValidaHuellaActiva',{"Corporativo":this.corporativoHuella,"Opcion":"C","SubOpcion":13})
                    .then((resp)=>{
                            if(resp.data.estado==0){
                                if(resp.data.descripcion){
                                    this.popUpBusquedaCorporativo = false;
                                    this.ValidarHuella(this.corporativoHuella);
                                }else{
                                    this.$vs.notify({
                                            title:'Validacion Huella',
                                            text:'No cuenta con una huella registrada en el sistema',
                                            timer:6000,
                                            color:'danger',
                                            position: 'top-center'
                                        })
                                }
                            }
                        })  
                                
            },
            CargarHuellas(){
                this.limpiarRegistroHuella();
                this.popUpBusquedaCorporativo = true;
            },  
            getValidacionHuella(data){
                if(data.StatusPlantilla == 'VERIFICADO'){ 
                    this.$emit("getCorporativoHuellas", {usuario:this.usuario,corporativo:this.corporativoHuella,huellaValidada:true})    
                    this.popUpSensorHuellas = false;
                }
            },
            limpiarRegistroHuella(){
                this.popUpBusquedaCorporativo = false
                this.popUpSensorHuellas = false               
                this.corporativoHuella = null
                this.huellaValidada = 0
                this.usuario = null
            }
        }
    }
    
</script>
