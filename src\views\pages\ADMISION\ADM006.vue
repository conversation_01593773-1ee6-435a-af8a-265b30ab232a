<template>
<vx-card title="Admisión - Ficha Paciente">

    <div v-if="!nuevo" style="border:1px solid #ccc; border-radius:10px">
        <div class="flex flex-wrap" style="padding:10px 20px" >
            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">

                <label class="vs-input--label">Admisión</label>
                <vx-input-group class="">
                    <vs-input v-model="info.codigo" disabled />
                    <template slot="append">
                        <div class="append-text btn-addon">
                            <button type="submit" v-show="false" name="button"></button>
                            <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_radiologias()" icon="icon-search"></vs-button>
                        </div>
                    </template>
                </vx-input-group>
            </div>
        </div>
        <div class="flex flex-wrap" style="padding:10px 20px">
            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                <vs-input label="Cód. Paciente" v-model="info.codigo"  class="w-full" disabled />
            </div>
            <div class="w-full md:w-2/3 lg:w-2/3 xl:w-2/3">
                <vs-input label="Paciente" v-model="info.codigo"  class="w-full" disabled />
            </div>
            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                <vs-input label="Habitación:" v-model="info.codigo"  class="w-full" disabled />
            </div>
            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                <vs-input label="Afiliado:" v-model="info.codigo"  class="w-full" disabled />
            </div>
            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                <vs-input label="Acompañante:" v-model="info.codigo"  class="w-full" disabled />
            </div>
        </div>
    </div>
    <br>

    <vs-tabs position="left" color="success">
        <vs-tab label="Datos Generales" icon-pack="fas" icon="fa-info-circle">
            <h5 style="float:left">Datos Generales</h5><br>

            <div class="flex flex-wrap" style="padding:10px 20px">
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <vs-input label="Edad" class="w-full" v-on:change="info.nombre1=info.nombre1.toUpperCase()" v-model="info.nombre1" />
                </div>
                <div class="w-full">
                    <vs-input label="Notificar a:" class="w-full" v-on:change="info.nombres2=info.nombres2.toUpperCase()" v-model="info.nombres2" />
                </div>
                <div class="w-full md:w-4/5 lg:w-4/5 xl:w-4/5">
                    <vs-input label="Dirección:" class="w-full" v-on:change="info.apellido1=info.apellido1.toUpperCase()" v-model="info.apellido1" />
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <vs-input label="Teléfono:" class="w-full" v-on:change="info.apellido2=info.apellido2.toUpperCase()" v-model="info.apellido2" />
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <label class="vs-input--label">Médico:</label>
                    <vx-input-group class="">
                        <vs-input v-model="info.codigo" disabled />
                        <template slot="append">
                            <div class="append-text btn-addon">
                                <button type="submit" v-show="false" name="button"></button>
                                <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_radiologias()" icon="icon-search"></vs-button>
                                <vs-button color="success" icon-pack="feather" @click="info.paciente_mostrar=true" icon="icon-plus"></vs-button>
                            </div>
                        </template>
                    </vx-input-group>
                </div>
            </div>

            <div class="flex flex-wrap" style="padding:10px 20px">

                <div class="w-full">
                    <label class="vs-input--label">Razón:</label>
                    <ul class="centerx center">
                        <li>
                            <vs-radio v-model="info.sexo" vs-value="Medicina">Medicina</vs-radio>
                        </li>
                        <li>
                            <vs-radio v-model="info.sexo" vs-value="Cirugía">Cirugía</vs-radio>
                        </li>
                        <li>
                            <vs-radio v-model="info.sexo" vs-value="Obstetricia">Obstetricia</vs-radio>
                        </li>
                        <li>
                            <vs-radio v-model="info.sexo" vs-value="Pediatría">Pediatría</vs-radio>
                        </li>
                        <li>
                            <vs-radio v-model="info.sexo" vs-value="Ortopedía">Ortopedía</vs-radio>
                        </li>
                    </ul>
                </div>

                <div class="w-full">
                    <label class="vs-input--label">Dx (CIE-10)</label>
                    <vs-table :data="[]">

                        <template slot="thead">
                            <vs-th>Cod.</vs-th>
                            <vs-th>Descripción</vs-th>
                            <vs-th>Wrng</vs-th>
                            <vs-th>Espera</vs-th>
                            <vs-th>Inhab.</vs-th>
                        </template>

                        <template slot-scope="{data}">
                            <vs-tr :key="indextr" v-for="(tr, indextr) in data">

                                <vs-td :data="data[indextr].email">
                                    {{ data[indextr].email }}
                                </vs-td>

                                <vs-td :data="data[indextr].username">
                                    {{ data[indextr].name }}
                                </vs-td>

                                <vs-td :data="data[indextr].id">
                                    {{ data[indextr].website }}
                                </vs-td>

                                <vs-td :data="data[indextr].id">
                                    {{ data[indextr].id }}
                                </vs-td>
                            </vs-tr>
                        </template>

                    </vs-table>
                </div>
            </div>
        </vs-tab>
        <!--  -->

        <vs-tab label="Términos de Pago" icon-pack="fas" icon="fa-file-signature">
            <h5 style="float:left">Términos de Pago</h5><br>
            <div class="flex flex-wrap" style="padding:10px 20px">
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <vs-input label="Nivel" class="w-full" v-on:change="info.direccion=info.direccion.toUpperCase()" v-model="info.direccion" />
                </div>
            </div>
            <div class="flex flex-wrap" style="padding:10px 20px;">
                <div class="w-full">
                    <label class="vs-input--label">Categoría de Cuenta:</label>
                    <ul class="centerx center">
                        <li>
                            <vs-radio v-model="info.sexo" vs-value="Privado">Privado</vs-radio>
                        </li>
                        <li>
                            <vs-radio v-model="info.sexo" vs-value="Seguro">Seguro</vs-radio>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="flex flex-wrap" style="padding:10px 20px;margin-bottom:200px">
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <label class="vs-input--label">Médico:</label>
                    <vx-input-group class="">
                        <vs-input v-model="info.codigo" disabled />
                        <template slot="append">
                            <div class="append-text btn-addon">
                                <button type="submit" v-show="false" name="button"></button>
                                <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_radiologias()" icon="icon-search"></vs-button>
                                <vs-button color="success" icon-pack="feather" @click="info.paciente_mostrar=true" icon="icon-plus"></vs-button>
                            </div>
                        </template>
                    </vx-input-group>
                </div>
                <div class="w-full md:w-4/5 lg:w-4/5 xl:w-4/5">
                </div>
            </div>
        </vs-tab>

        <vs-tab label="Factura" icon-pack="fas" icon="fa-cash-register">
            <h5 style="float:left">Facturación</h5><br>
            <div class="flex flex-wrap" style="padding:10px 20px">

                <div class="w-full md:w-2/3 lg:w-2/3 xl:w-2/3">
                    <vs-input label="Nombre:" class="w-full" v-on:change="info.nombre_factura=info.nombre_factura.toUpperCase()" v-model="info.nombre_factura" />
                </div>
                <div class="w-full md:w-4/5 lg:w-4/5 xl:w-4/5">
                    <vs-input label="Dirección:" class="w-full" v-on:change="info.direccion_factura=info.direccion_factura.toUpperCase()" v-model="info.direccion_factura" />
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <vs-input label="NIT:" class="w-full" v-on:change="info.nit=info.nit.toUpperCase()" v-model="info.nit" />
                </div>

            </div>

        </vs-tab>

        <vs-tab label="Responsable Cuenta" icon-pack="fas" icon="fa-user-check">
            <h5 style="float:left">Responsable Cuenta</h5><br>
            <div class="flex flex-wrap" style="padding:10px 20px">
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                    <vs-input label="Nombre" class="w-full" v-model="info.profesion" />
                </div>
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                    <vs-input label="Apellido" class="w-full" v-on:change="info.carnet=info.carnet.toUpperCase()" v-model="info.carnet" />
                </div>
                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4">
                    <vs-input label="Edad" class="w-full" v-on:change="info.socio=info.socio.toUpperCase()" v-model="info.socio" />
                </div>
                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4">
                    <vs-input label="Estado Civil" class="w-full" v-on:change="info.pasaporte=info.pasaporte.toUpperCase()" v-model="info.pasaporte" />
                </div>
                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4">
                    <vs-input label="Telefonos" class="w-full" v-on:change="info.segurosocial=info.segurosocial.toUpperCase()" v-model="info.segurosocial" />
                </div>
                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4">
                    <vs-input label="Parentescos" class="w-full" v-model="info.profesion" />
                </div>
                <div class="w-full md:w-3/4 lg:w-3/4 xl:w-3/4">
                    <vs-input label="Dirección" class="w-full" v-on:change="info.carnet=info.carnet.toUpperCase()" v-model="info.carnet" />
                </div>
                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4">
                    <vs-input label="Departamento" class="w-full" v-on:change="info.segurosocial=info.segurosocial.toUpperCase()" v-model="info.segurosocial" />
                </div>
                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4">
                    <vs-input label="Nacionalidad" class="w-full" v-on:change="info.socio=info.socio.toUpperCase()" v-model="info.socio" />
                </div>
                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4">
                    <vs-input label="DPI" class="w-full" v-on:change="info.pasaporte=info.pasaporte.toUpperCase()" v-model="info.pasaporte" />
                </div>
                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4">
                    <vs-input label="Ext. Municipalidad" class="w-full" v-on:change="info.segurosocial=info.segurosocial.toUpperCase()" v-model="info.segurosocial" />
                </div>
                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4">
                    <vs-input label="Ext. Departamento" class="w-full" v-on:change="info.segurosocial=info.segurosocial.toUpperCase()" v-model="info.segurosocial" />
                </div>
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                    <vs-input label="Pasaporte" class="w-full" v-on:change="info.segurosocial=info.segurosocial.toUpperCase()" v-model="info.segurosocial" />
                </div>
                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4">
                    <vs-input label="Profesión" class="w-full" v-on:change="info.segurosocial=info.segurosocial.toUpperCase()" v-model="info.segurosocial" />
                </div>
                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4">
                    <vs-input label="Lugar de Trabajo" class="w-full" v-on:change="info.segurosocial=info.segurosocial.toUpperCase()" v-model="info.segurosocial" />
                </div>

            </div>

        </vs-tab>

        <vs-tab label="Observaciones Cobros" icon-pack="far" icon="fa-comment-dots">
            <h5 style="float:left">Observaciones Cobros</h5><br>
            <div class="flex flex-wrap" style="padding:10px 20px">

                <div class="w-full">
                    <label class="vs-input--label">Observaciones Internas:</label>
                    <vx-input-group class="">
                        <vs-input v-model="info.codigo" disabled />
                        <template slot="append">
                            <div class="append-text btn-addon">
                                <button type="submit" v-show="false" name="button"></button>
                                <vs-button color="success" icon-pack="feather" icon="icon-plus"></vs-button>
                            </div>
                        </template>
                    </vx-input-group>
                </div>

            </div>
        </vs-tab>

    </vs-tabs>

    <vs-divider></vs-divider>
    <vs-button style="float:right" @click="guardar_informe(true)"> Guardar</vs-button>
    <!-- <vs-button color="primary" style="float:right" type="border" @click="info.opcion.show=false" v-else> Salir</vs-button> -->
    <div style="clear:both"></div>

</vx-card>
</template>

<script>
// import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";

export default {
    data() {
        return {
            examenes: [],
            info: {
                invalido: false,
                nombre1: null,
                nombre2: null,
                apellido1: null,
                apellido2: null,
                casada: null,
                nacimiento: null,
                edad: null,
                dpi: null,
                telefono: null,
                sexo: null,
                estado_civil: null,
                religion: null,
                lugar_nacimiento: null,
                nacionalidad: null,
                telefonos: null,
                celular: null,
                email: null,

                profesion: null,

                direccion_factura: null,
                nombre_factura: null,
                nit: null,

                carnet: null,
                socio: null,
                pasaporte: null,
                segurosocial: null,

                direccion: null,
                ciudad: null,
                calle: null,
                avenida: null,
                casa: null,
                zona: null,
                colonia: null,
                edificio: null,
                departamento: null,
                municipio: null,

                direccion_trabajo: null,
                telefono_trabajo: null,
                ocupacion: null,
                CalleTrabajo: null,
                AvenidaTrabajo: null,
                NoCasaTrabajo: null,
                ZonaTrabajo: null,
                ColoniaTrabajo: null,
                EdificioTrabajo: null,
                OficinaTrabajo: null,
                DeptoTrabajo: null,
                MunicipioTrabajo: null,

                padre: null,
                madre: null,
                conyuge: null,
            },
            Tipos: [{
                    Id: 1,
                    Nombre: 'Interno'
                },
                {
                    Id: 2,
                    Nombre: 'Emergencia'
                },
                {
                    Id: 3,
                    Nombre: 'IGSS'
                },
                {
                    Id: 4,
                    Nombre: 'RN en Hospital'
                },
                {
                    Id: 5,
                    Nombre: 'Paquete'
                },
            ],
            EstadosCiviles: [{
                    Id: 'C',
                    Nombre: 'Casado(a)'
                },
                {
                    Id: 'D',
                    Nombre: 'Divorsiado(a)'
                },
                {
                    Id: 'S',
                    Nombre: 'Soltero(a)'
                },
                {
                    Id: 'U',
                    Nombre: 'Unido(a)'
                },
                {
                    Id: 'V',
                    Nombre: 'Viudo(a)'
                },
            ]
        }
    },
    props: {
        paciente: {
            default: null
        },
        nuevo: {
            default: false
        }
    },
    components: {
        // Multiselect
    },
    methods: {

        cargar_estudios() {
            this.axios.post('/app/admision/Medicamento', {
                    Corporativo: 0
                })
                .then(resp => {
                    
                    this.examenes = []
                    resp.data.json.map(data => {
                        this.examenes.push({
                            ControlMedicamento: data.ControlMedicamento,
                            Admision: data.Admision,
                            Codigo: data.Codigo,
                            FechaAsignada: data.FechaAsignada,
                            Frecuencia: data.Frecuencia,
                            DIAS: data.DIAS,
                            Serie: data.Serie,
                            Paciente: data.Paciente,
                            Tipo: data.tipo
                        })
                    })

                })
        }
    },
    created() {

    }
}
</script>

<style scoped>
.center li {
    display: inline !important;
    padding: 2px 10px;
}

.button {
    margin: 0px !important;
}

.btnx {
    margin-left: 10px !important;
    background: red;
    border-radius: 5px 0px 0px 5px;
}

.btn-drop {
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: red;

}

.menu-funcionalidad {
    /* padding-top:0 !important; */
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    padding: 15px;
    display: inline;
    float: right;
    border-radius: 10px;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 10px;
}

.menu-funcionalidad td {
    text-align: center;
    padding-right: 10px;
}

.vs-switch {
    height: 30px;
    width: 60px !important;
}

.vs-circle-switch {
    height: 25px;
    width: 25px;
}

.vs-switch--icon {
    font-size: 15px
}

.vs-switch.vs-switch-active .vs-switch--circle {
    margin-left: calc(100% - 30px);
}
</style>
