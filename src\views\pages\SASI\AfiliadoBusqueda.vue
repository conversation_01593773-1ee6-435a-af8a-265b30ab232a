<template>
<div class="afiliado-busqueda-container">

    <DxDataGrid v-bind="GridOptions" :data-source="Afiliados" @selection-changed="onSelectionChangedGrid" css-class="afiliado-busqueda-grid">
        <template #dropdowntemplate="{data}">
            <AfiliadoAcciones :data="data.data" @action-item-click="onItemClick"/>
        </template>
        <template #detalle-citas>
            <div>
                <span> Citas por año: {{ RestriccionCitas.CitasAnuales }} </span>
                <span> Citas acumuladas: {{ RestriccionCitas.CitasAcumuladas }} </span>
            </div>
        </template>
    </DxDataGrid>
</div>
</template>

<script>
import {
    DefaulGridOptions,
} from './data.js'
import AfiliadoAcciones from './AfiliadoAcciones.vue'
export default {
    name: 'AfiliadoBusqueda',
    components: {
        AfiliadoAcciones
    },
    props: {
        IdAfiliado: String,
        IdCliente: String,
        IdPaciente: Number,
        NumeroAdhesion: String,
        PrimerNombre: String,
        SegundoNombre: String,
        PrimerApellido: String,
        SegundoApellido: String,
        SoloAfiliados: Boolean, //indica si se cargan tambien clientes sin afiliación
        ShowMessage: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            Afiliados: null,
            RestriccionCitas: {

            },
            GridOptions: {
                ...DefaulGridOptions,
                headerFilter: null,
                groupPanel: null,
                toolbar: {
                    items: [{
                            template: 'detalle-citas',
                            location: 'before',
                            locateInMenu: 'never',
                        },
                        {
                            location: 'center',
                            locateInMenu: 'auto',
                            widget: 'dxButton',
                            options: {
                                icon: 'plus',
                                type: 'default',
                                stylingMode: 'contained',
                                text: 'Nuevo Afiliado',
                                hint: 'Nuevo Afiliado',
                                onClick: this.onNuevoAfiliado
                            }
                        },
                        {
                            name: 'searchPanel',
                            locateInMenu: 'auto',
                        },
                    ],
                },
                height: 'calc(100vh - 430px)',
                showColumnHeaders: true,
                columns: [
                    {
                        dataField: 'CodigoAseguradora',
                        caption: 'Código Aseguradora',
                        width: 70,
                    },
                    {
                        dataField: 'NombreAseguradora',
                        caption: 'Nombre Aseguradora',
                        width: 180,
                    },  
                    {
                        dataField: 'NumeroAdhesion',
                        caption: 'Número Adhesión',
                        width: 100,
                    },
                    {
                        dataField: 'Status',
                        width: 85,
                    },
                    {
                        dataField: 'FechaInicioCobertura',
                        format: 'dd/MM/yyyy',
                        dataType: 'datetime',
                        width: 95,
                    },

                    {
                        caption: 'Póliza',
                        dataField: 'Poliza',
                        width: 60,
                    },
                    {
                        dataField: 'PlanXContrato',
                        caption: 'Nombre Plan',
                        width: 180,
                    },
                    {
                        dataField: 'IdCliente',
                        width: 100,
                    },
                    {
                        dataField: 'IdAfiliado',
                        width: 100,
                    },
                    {
                        dataField: 'IdPaciente',
                        width: 75,
                    },
                    {
                        dataField: 'NombreCompleto',
                        caption: 'Nombre',
                    },
                    {
                        dataField: 'ListaNegra',
                        width: 65,
                        editorType: 'dxCheckBox',
                    },
                    {
                        caption: 'Acción',
                        type: 'buttons',
                        width: '65',
                        allowResizing: false,
                        fixed: true,
                        visible: true,
                        cellTemplate: 'dropdowntemplate',
                        cssClass: 'busqueda-afiliado-action-buttons'
                    },
                ],
            },
        }
    },
    methods: {
        Buscar() {
            return this.axios.post("/app/v1_afiliados/BusqudaAfiliados", {
                    ...this.$props
                })
                .then((resp) => {
                    this.Afiliados = resp.data.json
                    this.$emit('loaded-data', this.Afiliados)
                    if (this.ShowMessage && !resp.data.json.length)
                        this.$vs.notify({
                            time: 4000,
                            title: "No se encontro información",
                            text: "No se encontro información con los datos proporcionados",
                            iconPack: "feather",
                            icon: "icon-alert-circle",
                            color: "warning",
                            position: "top-center",
                        })
                })
        },
        CargaRestriccionCitas(IdPlan, NumeroAdhesion) {
            this.RestriccionCitas = {
                CitasAnuales: 0,
                Permitidas: 0,
                CitasAutorizadas: 0,
                CitasAcumuladas: 0,
                Restriccion: null,
                ValidaCitas: null,
                PermiteCita: null,
            }
            if (IdPlan && NumeroAdhesion)
                return this.axios.post("/app/v1_afiliados/RestriccionCitas", {
                        IdPlan: IdPlan,
                        NumeroAdhesion: NumeroAdhesion,
                        IdAfiliado: '000000000000'
                    })
                    .then((resp) => {
                        this.RestriccionCitas = resp.data.json[0]
                    })
        },

        onSelectionChangedGrid(e) {
            this.CargaRestriccionCitas(e.selectedRowsData[0] ?.IdPlan, e.selectedRowsData[0] ?.NumeroAdhesion)
            this.$emit('selected-row', e.selectedRowsData)
        },
        onItemClick(e) {
            this.$emit('action-item-click', e)
            this.$emit(e.itemData.id, e)
        },
        onNuevoAfiliado() {
            this.$emit('nuevo')
        },
        Limpiar(){
            this.Afiliados = null
        },
    },
}
</script>

<style>

.busqueda-afiliado-action-buttons .dx-button {
    border-radius: 50px !important;
    border-width: 2px;
    border-color: #2980B9 !important;
    height: 35px !important;
    width: 35px !important;

}

.busqueda-afiliado-action-buttons i {
    color: #2980B9 !important;
    font-weight: bolder;
}

.afiliado-busqueda-container span {
    font-size: larger;
    font-weight: bolder;
}

.i-size {
    font-size: 18px;
}
</style>
