<template>
<div id="Anestesia-Preoperatoria">
    <div class="p-2" v-if="(consulta && hojaAnestesia !== null && hojaAnestesia !== '') || (!consulta)">
        <form @submit="handleSubmit">
            <DxForm :form-data.sync="formulario" labelMode="floating" :ref="dataForm" :show-validation-summary="true" :read-only="consulta">

                <DxGroupItem item-type="group" :col-span="2" :col-count="2" v-if="consulta === true">
                    <DxSimpleItem v-for="(item) in info" v-bind:key="item.value" data-field="x">
                        <template #default>
                            <div id="detail">
                                <b>{{ item.name }}</b>
                                {{ formulario[item.value] }}
                            </div>
                        </template>
                    </DxSimpleItem>
                </DxGroupItem>

                <DxGroupItem item-type="group" :col-span="2" :col-count="2" caption=" ">
                    <DxItem data-field="Antecedentes" editor-type="dxTextArea" :editor-options="{ height: 100 }">
                        <DxLabel :text="'Antecedentes importantes'" />
                    </DxItem>
                    <DxItem data-field="Medicamentos" editor-type="dxTextArea" :editor-options="{ height: 100 }">
                        <DxLabel :text="'Medicamentos importantes'" />
                    </DxItem>
                    <DxItem data-field="Examen" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 6000 }">
                        <DxLabel :text="'Exámen físico (Datos positivos)'" />
                    </DxItem>
                    <DxItem data-field="Otros" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000 }" />
                </DxGroupItem>

                <DxGroupItem :col-count-by-screen="colCountByScreenSelect" item-type="group" caption=" ">
                    <DxItem data-field="Ayuno" editor-type="dxSelectBox" :editor-options="{ items: ayuno }" :is-required="true">
                        <DxLabel :text="'Horas de ayuno'" />
                    </DxItem>
                    <DxItem data-field="Anestesia" editor-type="dxSelectBox" :editor-options="{ items: anestesia }" :is-required="true">
                        <DxLabel :text="'Plan de anestesia'" />
                    </DxItem>
                    <DxItem data-field="Valuacion" :v-model:value="formulario.Valuacion" editor-type="dxSelectBox" :editor-options="{ items: valuacion }" :is-required="true">
                        <DxLabel :text="'Valuación de ASA'" />
                    </DxItem>
                </DxGroupItem>

                <DxGroupItem :col-count-by-screen="colCountByScreen" item-type="group" caption="Preguntas">
                    <DxItem v-for="(item, index) in preguntas" v-bind:key="index" :data-field="item.name" editor-type="dxCheckBox" css-class="check-box" :editor-options="{ disabled: consulta }">
                        <DxLabel :text="item.text" />
                    </DxItem>
                </DxGroupItem>

                <DxGroupItem :col-count-by-screen="colCountByScreen" item-type="group" caption="Enfermedades">
                    <DxItem v-for="(item) in enfermedades" v-bind:key="item.name" :data-field="item.name" editor-type="dxCheckBox" css-class="check-box" :editor-options="{ disabled: consulta }">
                        <DxLabel :text="item.text" />
                    </DxItem>
                </DxGroupItem>

                <DxGroupItem v-if="!consulta" item-type="group" :col-count="2" caption=" ">
                    <DxButtonItem :col-span="3" :button-options="submitButtonOptions" horizontal-alignment="center" verical-alignment="center" />
                    <DxButtonItem :col-span="3" :button-options="newButtonOptions" horizontal-alignment="center" verical-alignment="center" />
                </DxGroupItem>
            </DxForm>
        </form>
    </div>
    <vs-row id="texto" class="p-2" v-if="(hojaAnestesia === '' || hojaAnestesia === null) && consulta">
        <p>Seleccione una fecha para mostrar información</p>
    </vs-row>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'
import {
    DxForm,
    DxItem,
    DxLabel,
    DxGroupItem,
    DxButtonItem,
    DxSimpleItem
} from 'devextreme-vue/form'
import 'devextreme-vue/text-area'

const dataForm = 'anestesiaForm'

export default {
    name: 'AnestesiaPreoperatoria',
    components: {
        DxForm,
        DxItem,
        DxLabel,
        DxGroupItem,
        DxButtonItem,
        DxSimpleItem
    },
    data() {
        return {
            DefaultDxGridConfiguration,
            dataForm,
            ayuno: ['Sin ayuno', '1 hora', '2 horas', '3 horas', '4 horas', '5 horas', 'Más de 5 horas', ],
            anestesia: ['General', 'Epidural', 'Regional', 'Otra', ],
            valuacion: ['1', '2', '3', '4'],
            preguntas: [{
                    text: '¿Ha sido operado(a) anteriormente?',
                    name: 'OperadoAnteriormente'
                },
                {
                    text: '¿Usted o algún familiar ha tenido alguna complicación seria o inexplicable durante una cirugía o bajo anestesia?',
                    name: 'Complicacion'
                },
                {
                    text: '¿Es usted alérgico a algo?',
                    name: 'Alergico'
                },
                {
                    text: '¿Toma aspirina frecuentemente?',
                    name: 'Aspirina'
                },
                {
                    text: '¿Toma algún medicamento o droga?',
                    name: 'TomaMedicina'
                },
                {
                    text: '¿Usa gotas para los ojos o nariz?',
                    name: 'Gotas'
                },
                {
                    text: '¿Usa lentes de contacto?',
                    name: 'LentesContacto'
                },
                {
                    text: '¿Toma bebidas alcohólicas frecuentemente?',
                    name: 'Alcohol'
                },
                {
                    text: '¿Fuma o ha dejado de fumar?',
                    name: 'Fuma'
                },
                {
                    text: '¿Ha recibido transfusiones de sanguíneas?',
                    name: 'Transfusiones'
                },
                {
                    text: '¿Está embarazada actualmente o cree estarlo?',
                    name: 'Embarazo'
                },
                {
                    text: '¿Usa dentadura postiza o puentes?',
                    name: 'Dentadura'
                },
                {
                    text: '¿Tiene algún diente flojo o quebrado?',
                    name: 'Diente'
                },
                {
                    text: '¿Ha tenido tos, catarro o dolor de garganta recientemente?',
                    name: 'DolorGarganta'
                },
            ],
            enfermedades: [{
                    text: '¿Ulcera, gastrítis, indegestión?',
                    name: 'Ulcera'
                },
                {
                    text: '¿Problemas de sangrado?',
                    name: 'Sangrado'
                },
                {
                    text: '¿Bronquitis, asma, enfisema?',
                    name: 'Bronquitis'
                },
                {
                    text: '¿Flema, tos crónica?',
                    name: 'Flema'
                },
                {
                    text: '¿Presión sanguínea alta?',
                    name: 'Presion'
                },
                {
                    text: '¿Enfermedades renales?',
                    name: 'Renales'
                },
                {
                    text: '¿Enfermedades cardiacas?',
                    name: 'Cardiacas'
                },
                {
                    text: '¿Enfermedades venosas?',
                    name: 'Venosas'
                },
                {
                    text: '¿Mareos, AVC?',
                    name: 'Mareos'
                },
                {
                    text: '¿Convulsiones, ataques?',
                    name: 'Convulsiones'
                },
                {
                    text: '¿Glaucoma?',
                    name: 'Glaucoma'
                },
                {
                    text: '¿Problemas de espalda?',
                    name: 'Espalda'
                },
                {
                    text: '¿Debilidad al caminar?',
                    name: 'Caminar'
                },
                {
                    text: '¿Tratamiento con esteroides?',
                    name: 'Esteroides'
                },
                {
                    text: '¿Enfermedades del hígado?',
                    name: 'Higado'
                },
            ],
            formulario: {
                Antecedentes: '',
                Medicamentos: '',
                Examen: '',
                Otros: '',
                Ayuno: '',
                Anestesia: '',
                Valuacion: '',
                OperadoAnteriormente: false,
                Complicacion: false,
                Alergico: false,
                Aspirina: false,
                TomaMedicina: false,
                Gotas: false,
                LentesContacto: false,
                Alcohol: false,
                Fuma: false,
                Transfusiones: false,
                Embarazo: false,
                Dentadura: false,
                Diente: false,
                DolorGarganta: false,
                Ulcera: false,
                Sangrado: false,
                Bronquitis: false,
                Flema: false,
                Presion: false,
                Renales: false,
                Cardiacas: false,
                Venosas: false,
                Mareos: false,
                Convulsiones: false,
                Glaucoma: false,
                Espalda: false,
                Caminar: false,
                Esteroides: false,
                Higado: false,
                MotivoConsulta: '',
                NombreRegistra: '',
                PuestoRegistra: '',
                FechaFormulario: ''
            },
            submitButtonOptions: {
                text: 'Guardar formulario',
                type: 'success',
                icon: 'save',
                useSubmitBehavior: true,
                disabled: this.consulta
            },
            newButtonOptions: {
                text: 'Nuevo formulario',
                type: 'default',
                icon: 'file',
                useSubmitBehavior: false,
                onClick: ()=>{this.limpiarFormulario()},
                disabled: this.consulta
            },
            info: [{
                    name: 'Usuario:',
                    value: 'NombreRegistra'
                },
                {
                    name: 'Puesto:',
                    value: 'PuestoRegistra'
                }, {
                    name: 'Fecha registro:',
                    value: 'FechaFormulario'
                },
                {
                    name: 'Motivo consulta:',
                    value: 'MotivoConsulta'
                },
            ],
        }
    },
    props: {
        SerieAdmision: null,
        CodigoAdmision: null,
        NumeroExpediente: null,

        StatusAdmision: null,
        consulta: null, //Variable para indicar si el componente se usará para consulta -> 'true' o para ingreso -> 'false'
        hojaAnestesia: null, //Variable para obtener la hoja cuando se utilice para consulta
    },
    methods: {
        handleSubmit(e) {
            e.preventDefault()
            if (this.SerieAdmision && this.CodigoAdmision) {
                let postData = {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    ...this.formulario,
                }

                this.axios.post("/app/v1_ExpedienteEvolucion/InsertarPreoperatorio", {
                    ...postData
                }).then(() => {
                    this.limpiarFormulario();
                    this.$emit('ingreso', 1);
                })
            }
        },
        limpiarFormulario() {
            this.formulario = {
                Antecedentes: '',
                Medicamentos: '',
                Examen: '',
                Otros: '',
                Ayuno: '',
                Anestesia: '',
                Valuacion: '',
                OperadoAnteriormente: false,
                Complicacion: false,
                Alergico: false,
                Aspirina: false,
                TomaMedicina: false,
                Gotas: false,
                LentesContacto: false,
                Alcohol: false,
                Fuma: false,
                Transfusiones: false,
                Embarazo: false,
                Dentadura: false,
                Diente: false,
                DolorGarganta: false,
                Ulcera: false,
                Sangrado: false,
                Bronquitis: false,
                Flema: false,
                Presion: false,
                Renales: false,
                Cardiacas: false,
                Venosas: false,
                Mareos: false,
                Convulsiones: false,
                Glaucoma: false,
                Espalda: false,
                Caminar: false,
                Esteroides: false,
                Higado: false,
                MotivoConsulta: '',
                NombreRegistra: '',
                PuestoRegistra: '',
                FechaFormulario: ''
            }
        },
        getAnestesiaPre() {
            this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaHojaAnestesia', {
                    Id: this.hojaAnestesia
                })
                .then(resp => {
                    this.formulario = resp.data.json[0]
                    this.formulario.FechaFormulario = this.formatFecha(this.formulario.FechaFormulario)
                })
        },
        formatFecha(date) {
            date = new Date(date)
            var month = date.getMonth() + 1
            month = month < 10 ? '0' + month : month;
            var hours = date.getHours();
            var minutes = date.getMinutes();
            minutes = minutes < 10 ? '0' + minutes : minutes;
            var strTime = hours + ':' + minutes;

            return date.getDate() + "/" + month + "/" + date.getFullYear() + " " + strTime;
        },
    },
    mounted() {
        if (this.consulta !== null) {
            if (this.consulta === true && this.hojaAnestesia !== null && this.hojaAnestesia !== '') {
                this.getAnestesiaPre();
            }
        }
    },
    watch: {
        'hojaAnestesia'(newval) {
            if (newval !== undefined)
                this.getAnestesiaPre();
        },
    },
    computed: {
        colCountByScreen() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 2,
                    md: 3,
                    lg: 4,
                };
        },
        colCountByScreenSelect() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 3,
                    lg: 3,
                };
        },
        anestesiaDataForm: function () {
            return this.$refs[dataForm].instance;
        },
    },
}
</script>

<style>
#Anestesia-Preoperatoria .dx-layout-manager .dx-label-h-align .dx-field-item-label {
    white-space: pre-wrap;
    vertical-align: middle !important;
    word-break: break-word;
}

#Anestesia-Preoperatoria .dx-layout-manager .dx-label-h-align.dx-flex-layout:not(.dx-field-item-label-align) {
    align-items: center !important;
}

#Anestesia-Preoperatoria .dx-item-content .dx-box-item-content {
    place-content: center;
    padding: 5px !important;
    padding-right: 10px !important;
}

#Anestesia-Preoperatoria .dx-layout-manager .dx-label-h-align.dx-flex-layout .dx-field-item-label {
    min-width: 90%;
    max-width: 90%;
}

#Anestesia-Preoperatoria .dx-layout-manager .dx-label-h-align.dx-flex-layout .dx-field-item-content,
.dx-layout-manager .dx-label-h-align.dx-flex-layout .dx-field-item-content-wrapper {
    min-width: 10%;
    max-width: 10%;
}

#Anestesia-Preoperatoria .dx-field-item-label-content {
    width: 90%;
}

#Anestesia-Preoperatoria .check-box {
    border-color: #e3e3e3;
    border-style: dotted;
    border-width: 1px;
}

#Anestesia-Preoperatoria .dx-layout-manager .dx-label-h-align.dx-flex-layout {
    padding: 0px !important;
    padding-left: 5px !important;
    padding-right: 5px !important;
}

#texto {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    height: 100px;
}
</style>
