<template>
    <div>
        <vx-card title="Relación Recibo de Caja - Facturación de Conta">
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-justify="flex-end" vs-align="center" vs-w="1">
                    <label class="typo__label">Factura:&nbsp;</label>
                </vs-col>
                <vs-col vs-lg="1" vs-type="flex" vs-align="center" vs-w="2">
                    <vs-input ref="SerieFactura" v-model="SerieFactura"></vs-input>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="3">
                    <vs-input ref="NumeroFactura" v-model="NumeroFactura" @keyup.enter="RecuperaFactura()" @change="RecuperaFactura()"></vs-input>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-justify="flex-end" vs-align="center" vs-w="1">
                    <label class="typo__label">Referencia:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-input v-model="SerieReferencia" disabled></vs-input>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="3">
                    <vs-input v-model="NumeroReferencia" disabled></vs-input>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-justify="flex-end" vs-align="center" vs-w="1">
                    <label class="typo__label">A Nombre De:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="5">
                    <h5>{{ NombreFactura }}</h5>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-justify="flex-end" vs-align="center" vs-w="1">
                    <label class="typo__label">Del:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <h5>{{ FechaFactura }}</h5>
                </vs-col>
                <vs-col vs-type="flex" vs-justify="flex-end" vs-align="center" vs-w="1">
                    <label class="typo__label">Por:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <h5>{{ $formato_moneda(TotalFactura) }}</h5>
                </vs-col>
                <vs-col vs-type="flex" vs-justify="flex-end" vs-align="center" vs-w="1">
                    <label class="typo__label">Saldo:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <h5>{{ $formato_moneda(Saldo) }}</h5>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-justify="flex-end" vs-align="center" vs-w="1">
                    <label class="typo__label">Recibo:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-input ref="SerieFactura" v-model="SerieRecibo"></vs-input>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="3">
                    <vs-input ref="NumeroFactura" v-model="NumeroRecibo" @keyup.enter="RecuperaRecibo()" @change="RecuperaRecibo()"></vs-input>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-justify="flex-end" vs-align="center" vs-w="1">
                    <label class="typo__label">A Nombre De:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="5">
                    <h5>{{ NombreRecibo }}</h5>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-justify="flex-end" vs-align="center" vs-w="1">
                    <label class="typo__label">Del:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <h5>{{ FechaRecibo }}</h5>
                </vs-col>
                <vs-col vs-type="flex" vs-justify="flex-end" vs-align="center" vs-w="1">
                    <label class="typo__label">Total:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <h5>{{ $formato_moneda(TotalRecibo) }}</h5>
                </vs-col>
                <vs-col vs-type="flex" vs-justify="flex-end" vs-align="center" vs-w="1">
                    <label class="typo__label">Por Aplicar:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <h5>{{ $formato_moneda(SaldoRecibo) }}</h5>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-justify="flex-end" vs-align="center" vs-w="1">
                    <label class="typo__label">Monto a unir:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-input type="number" ref="SerieFactura" v-model="MontoUnir"></vs-input>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="6">
                    <h2>Inserta nueva relación Factura-Recibo</h2>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-button color="success" icon-pack="fas" icon="fa-save" @click="GeneraRelacionFacturaRecibo()">OK</vs-button>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-button color="primary" icon-pack="fas" icon="fa-broom" @click="Limpiar()">Limpiar</vs-button>
                </vs-col>
            </vs-row>
            <br>
            <vs-divider></vs-divider>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="12">
                    <h2>Listado de todas las relaciones con la factura y recibo colocados arriba</h2>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center" vs-justify="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="12">
                    <div class="w-full">
                        <vs-table2 max-items="10" tooltip pagination search :data="ListreciboFactura">
                            <template slot="thead">
                                <th>SerieFactura</th>
                                <th>Factura</th>
                                <th>SerieRecibo</th>
                                <th>Recibo</th>
                                <th>Monto</th>
                                <th>Modificar Relación</th>
                                <th>Eliminar Relación</th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2 :data="data[indextr].SF">
                                        {{ data[indextr].SF }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].FA">
                                        {{ data[indextr].FA }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].SerieRecibo">
                                        {{ data[indextr].SerieRecibo }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Recibo">
                                        {{ data[indextr].Recibo }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Monto">
                                        {{ $formato_moneda(data[indextr].Monto) }}
                                    </vs-td2>
                                    <vs-td align="center">
                                        <vs-button color="warning" icon-pack="fas" icon="fa-pencil-alt" @click="IsRelacionMonto=true;ModificarMontoRelacionado(data[indextr])" class="mr-1" style="display:inline-block">
                                        </vs-button>
                                    </vs-td>
                                    <vs-td align="center">
                                        <vs-button color="danger" icon-pack="fas" icon="fa-rectangle-xmark" @click="EliminarRelacionFacturaRecibo(data[indextr])" class="mr-1" style="display:inline-block">
                                        </vs-button>
                                    </vs-td>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                </vs-col>
            </vs-row>
        </vx-card>
        <vs-popup title="Eliminar Relación Monto" :active.sync="IsRelacionMonto" class="seleccionEmpresa">
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="12">
                    <label class="typo__label">Monto a restar de la relación:&nbsp;</label>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="12">
                    <vs-input autofocus type="number" v-model="MontoUnirMod"></vs-input>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="6">
                    <vs-button color="success"  @click="EliminarRelacionFacturaRecibo()">Confirmar</vs-button>
                </vs-col>
                <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="6">
                    <vs-button color="danger"  @click="IsRelacionMonto=false">Cancelar</vs-button>
                </vs-col>
            </vs-row>
        </vs-popup>
    </div>
</template>
<script>
    export default {
        data() {
            return{
                SerieFactura: '',
                NumeroFactura: '',
                SerieReferencia: '',
                NumeroReferencia: '',
                NombreFactura: '',
                FechaFactura: '',
                TotalFactura: '',
                Saldo: '',
                SerieAdmision: '',
                NumeroAdmision: '',
                SerieRecibo: '',
                NumeroRecibo: '',
                NombreRecibo: '',
                FechaRecibo: '',
                TotalRecibo: '',
                SaldoRecibo: '',
                MontoUnir: '',
                ListreciboFactura: [],
                IsRelacionMonto: false,
                MontoUnirMod: '',
                DataModificar:{
                    SerieRecibo: '',
                    NumeroRecibo: '',
                    SerieFacturaCorta: '',
                    NumeroFacturaCorta: '',
                    SerieFactura: '',
                    NumeroFactura: '',
                    ReciboTotal : '',
                    SerieAdmision: '',
                    NumeroAdmision: ''
                }
            }
        },
        methods: {
            isEmptyObject(obj){
                return Object.keys(obj).length === 0;
            },
            RecuperaFactura(){
                this.axios.post('/app/v1_contabilidad_hospital/RecuperaFactura', {
                    SerieFactura: this.SerieFactura,
                    NumeroFactura: this.NumeroFactura
                })
                .then(resp => {
                    if(this.isEmptyObject(resp.data.json)){
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Mensaje',
                            text: 'Esta Factura no está registrada en esta empresa',
                        })
                        return
                    }
                    const { Nombre, Status, Fecha, Total, Saldo, EsFacturaCambiaria, SerieAdmision, Admision, Serie, Codigo } = resp.data.json[0]

                    if(Status == 'A'){
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Mensaje',
                            text: 'La factura fue anulada',
                        })
                        return
                    }
                    if(EsFacturaCambiaria == 0){
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Mensaje',
                            text: 'La factura no es cambiaria verifique o cambie definición',
                        })
                        return
                    }

                    this.SerieReferencia = Serie
                    this.NumeroReferencia = Codigo
                    this.NombreFactura = Nombre
                    this.FechaFactura = Fecha
                    this.TotalFactura = Total
                    this.Saldo = Saldo
                    this.SerieAdmision = SerieAdmision
                    this.NumeroAdmision = Admision
                    this.RecuperaReciboFactura()
                })
            },
            RecuperaReciboFactura(){
                this.axios.post('/app/v1_contabilidad_hospital/RecuperaReciboFactura', {
                    SerieFactura: this.SerieReferencia,
                    NumeroFactura: this.NumeroReferencia,
                    SerieRecibo: this.SerieRecibo,
                    NumeroRecibo: this.NumeroRecibo
                })
                .then(resp => {
                    if(resp.data.codigo == 0){
                        this.ListreciboFactura = resp.data.json
                    }
                })
            },
            RecuperaRecibo(){
                this.axios.post('/app/v1_contabilidad_hospital/RecuperaRecibo', {
                    SerieRecibo: this.SerieRecibo,
                    NumeroRecibo: this.NumeroRecibo,
                    SerieAdmision: this.SerieAdmision,
                    NumeroAdmision: this.NumeroAdmision
                })
                .then(resp => {
                    if(this.isEmptyObject(resp.data.json)){
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Mensaje',
                            text: 'Este Recibo no está registrado en esta empresa o no es parte de la admision',
                        })
                        return
                    }
                    const { Fecha, Total, Status, Nombre, Saldo } = resp.data.json[0]
                    if(Status == 'A'){
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Mensaje',
                            text: 'El Recibo fue anulado',
                        })
                        return
                    }
                    this.NombreRecibo = Nombre
                    this.FechaRecibo = Fecha
                    this.TotalRecibo = Total
                    this.SaldoRecibo = Saldo
                    this.RecuperaReciboFactura()
                })
            },
            GeneraRelacionFacturaRecibo(){
                if(this.NombreFactura == ''){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Mensaje',
                        text: 'Debe de ingresar una factura valida',
                    })
                    return
                }
                if(this.NombreRecibo == ''){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Mensaje',
                        text: 'Debe de indicar el recibo a relacionar',
                    })
                    return
                }
                this.axios.post('/app/v1_contabilidad_hospital/GeneraRelacionFacturaRecibo', {
                    SerieRecibo: this.SerieRecibo,
                    NumeroRecibo: this.NumeroRecibo,
                    SerieFactura: this.SerieReferencia,
                    NumeroFactura: this.NumeroReferencia,
                    FacturaSaldo: this.Saldo,
                    ReciboTotal : this.TotalRecibo,
                    SerieAdmision: this.SerieAdmision,
                    NumeroAdmision: this.NumeroAdmision,
                    MontoRecibo: this.MontoUnir
                })
                .then(resp => {
                    if(resp.data.codigo == 0){
                        this.SerieRecibo = ''
                        this.NumeroRecibo = ''
                        this.NombreRecibo = ''
                        this.FechaRecibo = ''
                        this.TotalRecibo = ''
                        this.SaldoRecibo = ''
                        this.MontoUnir = ''
                        this.RecuperaReciboFactura()
                        this.RecuperaFactura()
                        // this.RecuperaRecibo()
                    }
                })
            },
            EliminarRelacionFacturaRecibo(data){
                if(parseFloat(this.DataModificar.ReciboTotal) >= parseFloat(this.MontoUnirMod)){
                    if(this.IsRelacionMonto){
                        this.$vs.dialog({
                            type:'confirm',
                            color: 'primary',
                            title: 'Mensaje',
                            acceptText: 'Aceptar',
                            cancelText: 'Cancelar',
                            text: `'A continuación se le regresa ${ parseFloat(this.MontoUnirMod).toFixed(2) } al saldo a la factura ${ this.DataModificar.SerieFactura } ${ this.DataModificar.NumeroFactura }`,
                            accept: () => {
                                this.axios.post('/app/v1_contabilidad_hospital/EliminarRelacionFacturaRecibo', {
                                    SerieRecibo: this.DataModificar.SerieRecibo,
                                    NumeroRecibo: this.DataModificar.NumeroRecibo,
                                    SerieFactura: this.DataModificar.SerieFacturaCorta,
                                    NumeroFactura: this.DataModificar.NumeroFacturaCorta,
                                    ReciboTotal: this.MontoUnirMod,
                                    SerieAdmision: this.DataModificar.SerieAdmision,
                                    NumeroAdmision: this.DataModificar.NumeroAdmision
                                })
                                .then(resp => {
                                    this.IsRelacionMonto = false
                                    if(resp.data.codigo == 0){
                                        this.RecuperaReciboFactura()
                                        this.RecuperaFactura()
                                        // this.RecuperaRecibo()
                                    }
                                })
                            }
                        })
                    }else{
                        this.$vs.dialog({
                            type:'confirm',
                            color: 'primary',
                            title: 'Mensaje',
                            acceptText: 'Aceptar',
                            cancelText: 'Cancelar',
                            text: `'A continuación se le regresa ${ parseFloat(data.Monto).toFixed(2) } al saldo a la factura ${ data.SerieFactura } ${ data.Factura }`,
                            accept: () => {
                                this.axios.post('/app/v1_contabilidad_hospital/EliminarRelacionFacturaRecibo', {
                                    SerieRecibo: data.SerieRecibo,
                                    NumeroRecibo: data.Recibo,
                                    SerieFactura: data.SF,
                                    NumeroFactura: data.FA,
                                    ReciboTotal : data.Monto,
                                    SerieAdmision: data.SerieAdmision,
                                    NumeroAdmision: data.Admision
                                })
                                .then(resp => {
                                    if(resp.data.codigo == 0){
                                        this.RecuperaReciboFactura()
                                        this.RecuperaFactura()
                                        // this.RecuperaRecibo()
                                    }
                                })
                            }
                        })
                    }
                }else{
                this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Relación Factura - Recibo',
                        text: 'La cantidad es mayor al monto que la relación, no es válida',
                    })
                    return
            }
                
            },
            ModificarMontoRelacionado(data){
                this.MontoUnirMod = parseFloat(data.Monto).toFixed(2)
                this.DataModificar.SerieRecibo = data.SerieRecibo
                this.DataModificar.NumeroRecibo = data.Recibo
                this.DataModificar.SerieFacturaCorta = data.SF
                this.DataModificar.NumeroFacturaCorta = data.FA
                this.DataModificar.SerieFactura = data.SerieFactura
                this.DataModificar.NumeroFactura = data.Factura
                this.DataModificar.ReciboTotal = data.Monto
                this.DataModificar.SerieAdmision = data.SerieAdmision
                this.DataModificar.NumeroAdmision = data.Admision
            },
            Limpiar(){
                this.SerieFactura =  '',
                this.NumeroFactura = '',
                this.SerieReferencia = '',
                this.NumeroReferencia = '',
                this.NombreFactura = '',
                this.FechaFactura = '',
                this.TotalFactura = '',
                this.Saldo = '',
                this.SerieAdmision = '',
                this.NumeroAdmision = '',
                this.SerieRecibo = '',
                this.NumeroRecibo = '',
                this.NombreRecibo = '',
                this.FechaRecibo = '',
                this.TotalRecibo = '',
                this.SaldoRecibo = '',
                this.MontoUnir = '',
                this.ListreciboFactura = []
            },
            LimpiarMod(){
                this.DataModificar = {
                    SerieRecibo: '',
                    NumeroRecibo: '',
                    SerieFacturaCorta: '',
                    NumeroFacturaCorta: '',
                    SerieFactura: '',
                    NumeroFactura: '',
                    ReciboTotal: '',
                    SerieAdmision: '',
                    NumeroAdmision: ''
                },
                this.MontoUnirMod = ''
            }
        }
    }
</script>