<template>
<div>
    <ValidationObserver ref="formValidate" v-slot="{ invalid,handleSubmit }" mode="lazy">
        <div class="flex">
            <!-- <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5 p-1">
                <ValidationProvider name="Categoria" rules="required" v-slot="{ errors }">
                    <SM-Buscar label="Categoría del Cargo" v-model="info.categoria" api="app/Ajenos/Busqueda_Ajenos" :api_campos="['Nombre']" :api_titulos="['Nombre']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="true" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                </ValidationProvider>
            </div> -->
            <div class="sm:w-full md:w-3/12 lg:w-2/12 xl:w-2/12 p-1">
                <vs-input label="Categoria:" type="text" class="w-full" v-model="categoria" :disabled="true" />
            </div>
            <div class="sm:w-full md:w-6/12 lg:w-4/12 xl:w-3/12 p-1">               
                <ValidationProvider name="Servicio" rules="required" v-slot="{ errors }">
                    <SM-Buscar label="Servicio" v-model="info.producto" api="app/Ajenos/Busqueda_Productos" :api_campos="['Categoria','CategoriaNombre','Codigo','Nombre']" :api_titulos="['Cód. Categoría','Nombre Categoría','Código Producto', 'Producto']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :api_preload="true" :api_filtro="{InformesRadiologia:1}" :disabled_busqueda="!!editarTabla" :disabled_texto="!!editarTabla" 
                               :callback_buscar="cargarCategoria" :mostrar_busqueda="true" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                </ValidationProvider>            
                <div class="append-text" style="background-color:#ecf0f1;" v-if="info.producto">
                    {{ `Código: ${info.producto}`}}                            
                </div>
            </div>
            <div class="sm:w-full md:w-4/12 lg:w-2/12 xl:w-2/12 p-1">
                <table width="100%">
                    <tr>
                        <td>
                            <ValidationProvider name="Producto" rules="required" v-slot="{ errors }">
                                <vs-input label="Valor" type="number" class="w-full" v-model="info.valor" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                            </ValidationProvider>
                        </td>
                        <td style="padding-left:15px">
                            <label for="">Porcentaje</label>
                            <vs-switch v-model="info.porcentual" style="height: 23px;" />
                        </td>
                    </tr>
                </table>
            </div>
            <div v-if="!!editarTabla" class="sm:w-full md:w-2/12 lg:w-1/12 xl:w-1/12 p-1 pt-4">
                <label for="">Estado</label>
                <vs-switch v-model="info.estado" style="height: 23px;" />
            </div>
        </div>
        <div class="flex ">
            <vs-spacer></vs-spacer>
            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1 pt-8">
                <vs-button  style="float:right"  @click="handleSubmit((!editarTabla)?Guardar().producto():Editar().producto())" :disabled="invalid">Guardar</vs-button>
            </div>
        </div>
    </ValidationObserver>
</div>
</template>

<script>
import bitacora from '@/components/sermesa/funciones/SMBitacora'
export default {
    data() {
        return {
            mostrarSeleccionados: false, //Inidica si se muestran solo los seleccionados
            poliza: {
                pagina: null,
                mostrar: false, //Muestra el listado de Polizas
                busqueda: null,
                filtrarSeleccionados: false, //Muestra solo las polizas seleccionadas
                listado: []
            },
            info: {
                categoria:null,
                categoriaNombre:null,
                producto: null,
                porcentual: false,
                valor: null,
                estado: true
            },
        }
    },
    props: {
        editarTabla: {
            default: null
        },
        callback: {
            default: null
        },
        nuevo: {
            default: false
        }
    },
    computed: {
        polizaSeleccionadas() {
            return this.poliza.listado.filter(f => f.Seleccionar).length
        },
        polizaListado() {
            return (!this.mostrarSeleccionados) ? this.poliza.listado : this.poliza.listado.filter(f => f.Seleccionar)
        },
        categoria(){
            return this.info.categoriaNombre!=null ? `(${this.info.categoria}) - ${this.info.categoriaNombre}` : '';
        }
    },
    watch: {
        'editarTabla.info'(value) {
            
            if (value) {
                const valor = (value.ValorPorcentual > 0) ? value.ValorPorcentual : value.ValorNominal
                const porcentual = (value.ValorPorcentual > 0) ? true : false

                this.info.categoria = value.Categoria
                this.info.categoriaNombre = value.CategoriaNombre
                this.info.producto = value.Producto
                this.info.porcentual = porcentual
                this.info.valor = valor
                this.info.estado = (value.Status == 1) ? true : false
                bitacora.registrar(this.info)
                // console.log(this.info)
            }
        },
        // 'poliza.mostrar'(value) {
        //     if (!value) {
        //         setTimeout(() => {
        //             this.$refs.tabla.busqueda = null
        //             this.mostrarSeleccionados = false
        //             this.poliza.filtrarSeleccionados = false
        //             this.$emit('update:editarTabla', null)
        //         }, 100)
        //     }
        // },
    },
    methods: {
        cargarCategoria(producto){
            this.info.categoria = producto.Categoria
            this.info.categoriaNombre = producto.CategoriaNombre
        },
        Consulta() {
            return {
                // polizas: (id) => {
                //     return this.axios.post('/app/ajenos/FiltrosCargosListadoPolizasSeleccionadas', {
                //         IdFiltro: id
                //     })
                // }
            }
        },
        Guardar() {
            return {
                producto: () => {
                    const info = this.info
                    info.porcentual = (info.porcentual) ? 1 : 0
                    //  info.Bitacora2 = bitacora.obtener()

                    //Si la admision es tipo 1 se eliminan las polizas
                    
                    this.axios.post('/app/ajenos/TablaInterpretacionGuardar', info)
                        .then(() => {
                            this.info.producto = null
                            this.info.valor = null
                            this.info.porcentaje = false
                            this.callback()
                            
                        })
                        .catch(() => {
                            
                        })
                }
            }
        },
        Editar() {
            return {
                producto: () => {
                    const info = this.info
                    //info.Bitacora2 = bitacora.obtener()
                    info.porcentual = (info.porcentual) ? 1 : 0
                    info.estado = (info.estado) ? 'S' : 'N'
                    

                    //Si la admision es tipo 1 se eliminan las polizas
                    
                    this.axios.post('/app/ajenos/TablaInterpretacionEditar', info)
                        .then(() => {
                            this.$emit('update:editarTabla', {
                                mostrar: false,
                                info: null
                            })
                            this.info.producto = null
                            this.info.valor = null
                            this.info.porcentaje = false
                            this.callback()
                            
                        })
                        .catch(() => {
                            
                        })
                }
            }
        },
    },
    mounted() {}
}
</script>
