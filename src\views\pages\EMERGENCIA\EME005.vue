<!-- Modificación de Ordenes por Producto -->
<template>
<vx-card type="2" title="Modificación de Ordenes por Producto" class="justify-center ">
    <ValidationObserver ref="formValidate" mode="lazy">
        
        <form>
            <div class="flex flex-wrap">                
                <div class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 m-2">
                    <label>Admision:</label> 
                    &nbsp;&nbsp;
                    <label style="color:black">{{info.admision}}</label>                                 
                </div>
                <div class="sm:w-full md:w-full lg:w-6/12 xl:w-4/12 m-2 lg:pr-6">
                    <label>Paciente:</label> 
                    &nbsp;&nbsp;
                    <label style="color:black">{{info.paciente}}</label>      
                </div>
                <div class="sm:w-full md:w-full lg:w-1/12 xl:w-5/12"></div>

                <vs-divider position="left">Orden</vs-divider>
                
                <div class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 m-2" tabindex="1">
                    <vs-input label="Tipo Orden" class="w-full" v-on:change="info.tipoOrden = info.tipoOrden.toUpperCase()" v-model="info.tipoOrden" :disabled="info.desactivarCamposOrden" />

                    <div v-if="info.descripcionOrden && info.descripcionOrden!=''" style="background-color:#ecf0f1;">
                        {{ info.descripcionOrden}}
                    </div>
                </div>
                <div class="sm:w-full md:w-full lg:w-6/12 xl:w-4/12 m-2 lg:pr-6" tabindex="2">
                    <ValidationProvider label="*No. Orden" rules="required" v-slot="{ errors }">
                        <SM-Buscar class="w-full" label="*No. Orden" 
                        v-model="info.orden" 
                        api="app/v1_JefeCaja/obtener_orden" 
                        :api_campos="['Tipo','orden','serie','noAdmision','nombres','apellidos']" 
                        :api_titulos="['Tipo#','orden#','Serie Admisión','Admisión','Nombres','Apellidos']" 
                        :api_filtro="{'iOpcion':'C','iSubOpcion':'G','tipoOrden':info.tipoOrden}" 
                        :callback_buscar="ConsultarOrden" 
                        :callback_cancelar="LimpiarCampos" 
                        :disabled_texto="info.desactivarCamposOrden" 
                        api_campo_respuesta="orden" 
                        :api_preload="false" 
                        :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                    </ValidationProvider>

                </div>

                <div class="sm:w-full md:w-full lg:w-full xl:w-full m-1" style="padding:10px;border:1px solid #ccc;border-radius:5px">
                    <label class="vs-input--label">Seleccione la nueva categoría</label><br />
                    <vs-radio class="pr-10" v-model="info.categoria" vs-value="01">Medicamentos</vs-radio>
                    <vs-radio class="pr-10" v-model="info.categoria" vs-value="02">Suministros</vs-radio>
                    <vs-radio v-model="info.categoria" vs-value="30">Extraordinarios</vs-radio>
                </div>
                <vs-textarea class="sm:w-full md:w-full lg:w-full xl:w-full m-1" label="Motivo del cambio" counter="200" v-model="info.observaciones" />
            </div>

        </form>
    </ValidationObserver>
    <br>
    <h5 class="justify-right">Detalle de la Orden</h5>
    <div class="flex flex-wrap">
        <div class="xl:w-full lg:w-full md:w-full sm:w-full p-2">
            <vs-table2 max-items="6" search pagination :data="productos" height="470px" class="mt-0 mb-0">
                <template slot="thead">
                    <th order="Producto" width="25px">Código</th>
                    <th order="Nombre" width="350px">Nombre</th>
                    <th order="Cantidad" width="25px">Cantidad</th>
                    <th order="Factura" width="50px">Factura</th>
                    <th order="Categoria" width="50px">Categoría</th>
                    <th order="Descripcion" width="200px">Descripción</th>
                </template>
                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">
                        <vs-td2>{{ tr.Producto }}</vs-td2>
                        <vs-td2>{{ tr.Nombre }}</vs-td2>
                        <vs-td2>{{ tr.Cantidad }}</vs-td2>
                        <vs-td2>{{ tr.Factura }}</vs-td2>
                        <vs-td2>{{ tr.Categoria }}</vs-td2>
                        <vs-td2>{{ tr.Descripcion }}</vs-td2>
                        <vs-td class="pr-5">
                            <vs-button color="primary" icon-pack="feather" icon="icon-zap" style="display:inline-block;margin-right:2px; float:right" :disabled="tr.Factura !='-'" @click="SetCategoria(tr)"></vs-button>
                        </vs-td>
                    </tr>
                    <div>

                    </div>

                </template>
            </vs-table2>
        </div>
    </div>
</vx-card>
</template>

<script>
export default {
    data() {
        return {
            info: {
                tipoOrden: null,
                orden: null,
                admision: null,
                paciente: null,
                categoria: null,
                observaciones: null
            },
            productos: []
        };
    },
    methods: {
        ConsultarOrden(datos) {
            this.info.orden = datos.orden
            this.info.tipoOrden = datos.Tipo
            this.info.paciente = datos.NombrePaciente
            this.info.admision = datos.serie +'-'+ datos.noAdmision
            this.DetalleOrden()

        },
        DetalleOrden() {
            this.axios.post(
                '/app/emergencia/DetalleOrdenConsolidado', {
                    tipoOrden: this.info.tipoOrden,
                    orden: this.info.orden
                }
            ).then(resp => {
                if (resp.data && resp.data.json.length > 0) this.productos = resp.data.json
            })
        },
        LimpiarCampos() {
            this.serieAdm = null
            this.noAdmision = null
            this.info.orden = null
            this.info.tipoOrden = null
            this.info.admision = null
            this.info.paciente = null
            this.info.categoria = null
            this.observaciones = null
            this.productos = []
        },
        SetCategoria(tr) {
            if (!this.info.orden || !this.info.tipoOrden) {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Alerta',
                    text: 'Debe de ingresar la orden',
                })
                return false
            }
            if (!this.info.observaciones) {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Alerta',
                    text: 'Debe de ingresar el motivo del cambio',
                })
                return false
            }
            if (!this.info.categoria) {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Alerta',
                    text: 'Seleccione la nueva categoría',
                })
                return false
            }
            this.axios.post(
                    '/app/emergencia/CambiaCatProducto', {
                        orden: this.info.orden,
                        tipoOrden: this.info.tipoOrden,
                        categoriaCargo: this.info.categoria,
                        observaciones: this.info.observaciones,
                        producto: tr.Producto
                    }
                )
                .then(
                    (respuesta) => {
                        const Respuesta = respuesta.data ? respuesta.data.json[0].descripcion : respuesta.json.descripcion
                        const CodigoError = respuesta.data ? respuesta.data.json[0].tipo_error : respuesta.json.tipo_error
                        if (CodigoError == 1) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: Respuesta,
                                time: 4000,
                                position: 'bottom-center'
                            })
                            return
                        } else {
                            this.DetalleOrden()
                            this.$vs.notify({
                                color: 'success',
                                title: 'Producto actualizado',
                                text: tr.Nombre
                            })

                        }
                    }
                )
        }
    }
}
</script>
