<template>
  <div>
      <div class="long-title">
          <h3>Reportes Facturación Sermesa Centro Americana</h3>
      </div>
      <br />
      <div style="display: flex">
          <vs-select class="w-full" label="Empresa" v-model="Reportes.Sucursal">
              <vs-select-item v-for="(item, index) in Sucursales" :key="index" :value="item.EmpresaReal" :text="item.Nombre" /> </vs-select>&nbsp;
          <vs-input class="w-1/2" type="date" label="Fecha Inicial" v-model="Reportes.FechaInicial"></vs-input>&nbsp;
          <vs-input class="w-1/2" type="date" label="Fecha Final" v-model="Reportes.FechaFinal"></vs-input>&nbsp;
          <div style="display: flex; margin-top: 20px">
              <DxButton id="1" :width="150" :height="40" text="Generar" type="default" @click="GenerarDatos(1)" />&nbsp;
              <DxButton :width="150" :height="40" text="Limpiar Datos" type="danger" @click="LimpiarDatos()" />&nbsp;
          </div>
      </div>

      <br />

      <div class="flex flex-wrap">
          <div class="w-1/6">
              <vx-card title="Reportes Facturación">
                  <form>
                      <vs-select class="w-full" label="Tipo Reporte" v-model="info.SubOpcion">
                          <vs-select-item v-for="(item, index) in SubOpciones" :key="index" :value="item.value" :text="item.text" />
                      </vs-select>
                      <vs-select class="w-full" label="Opciónes" v-model="info.Opcion">
                          <vs-select-item v-for="(item, index) in Opciones" :key="index" :value="item.value" :text="item.text" />
                      </vs-select>
                      <vs-select class="w-full" v-if="info.Opcion === 'E'" label="Opción" v-model="info.SubOpcionEmpleados">
                          <vs-select-item v-for="(item, index) in Opcion" :key="index" :value="item.value" :text="item.text" />
                      </vs-select>

                      <br />
                  </form>
                  <div style="display: flex">
                      <DxButton  text="Excel" type="success" @click="CargarExcel()" />
                      &nbsp; 
                      <DxButton  text="Empleados" type="default" @click="ConsultaEmpleadosFacturados().Empleados()" v-if="this.info.SubOpcion == 1" />
                  </div>
                  <br>
            
              </vx-card>
          </div>

          <div class="w-5/6">
              <vx-card>
                  <h6 style="margin: 5px">Requerimientos</h6>
                  <DxDataGrid :data-source="Requerimientos" :allow-column-reordering="true" :allow-column-resizing="true" :column-auto-width="true" :show-borders="true" data-table-id="requerimientos" @selection-changed="onSelectionChanged('requerimientos', $event)" ref="requerimientosGrid"
                  :height="230">
                      <DxDataGridHeaderFilter :visible="true" />
                      <DxDataGridSelection mode="single" />
                      <DxDataGridColumn data-field="EmpresaReal" caption="Empresa" />
                      <DxDataGridColumn data-field="Debe" caption="Debe" data-type="number" />
                      <DxDataGridColumn data-field="CuentaDebe" caption="Cuenta Debe" />
                      <DxDataGridColumn data-field="Haber" caption="Haber" data-type="number" />
                      <DxDataGridColumn data-field="CuentaHaber" caption="Cuenta Haber" />
                      <DxDataGridColumn data-field="CostoTotal" caption="Costo Total" data-type="number" :format="{
              formatter: (value) =>
                `Q ${value.toLocaleString('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}`,
            }" />
                      <DxDataGridColumn data-field="Facturar" caption="Factura" data-type="number" :format="{
              formatter: (value) =>
                `Q ${value.toLocaleString('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}`,
            }" />
                      <DxDataGridColumn data-field="Devolucion" caption="Devolución" data-type="number" :format="{
              formatter: (value) =>
                `Q ${value.toLocaleString('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}`,
            }" />
                      <DxDataGridColumn data-field="NotaCredito" caption="Nota Crédito" data-type="number" :format="{
              formatter: (value) =>
                `Q ${value.toLocaleString('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}`,
            }" />
                      <DxDataGridColumn data-field="Bodega" caption="Bodega" />
                      <DxDataGridColumn data-field="SerieFActura" caption="Serie Factura" />
                      <DxDataGridColumn data-field="Factura" caption="Factura" data-type="number" />
                      <DxDataGridColumn data-field="SerieNCredito" caption="Serie Nota Crédito" />
                      <DxDataGridColumn data-field="NCredito" caption="Nota Crédito" data-type="number" />

                      <DxDataGridScrolling mode="virtual" />
                  </DxDataGrid>
                  <br />

                  <h6 style="margin: 5px">Cargos</h6>

                  <DxDataGrid :data-source="Cargos" :allow-column-reordering="true" :allow-column-resizing="true" :column-auto-width="true" :show-borders="true" data-table-id="cargos" @selection-changed="onSelectionChanged('cargos', $event)" ref="cargosGrid" :height="230">
                      <DxDataGridHeaderFilter :visible="true" />
                      <DxDataGridSelection mode="single" />
                      <DxDataGridColumn data-field="EmpresaReal" caption="Empresa" />
                      <DxDataGridColumn data-field="Debe" caption="Debe" data-type="number" />
                      <DxDataGridColumn data-field="CuentaDebe" caption="Cuenta Debe" />
                      <DxDataGridColumn data-field="Haber" caption="Haber" data-type="number" />
                      <DxDataGridColumn data-field="CuentaHaber" caption="Cuenta Haber" />
                      <DxDataGridColumn data-field="CostoTotal" caption="Costo Total" data-type="number" :format="{
              formatter: (value) =>
                `Q ${value.toLocaleString('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}`,
            }" />
                      <DxDataGridColumn data-field="CostoTotalHospital" caption="Costo Total Hospital" data-type="number" :format="{
              formatter: (value) =>
                `Q ${value.toLocaleString('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}`,
            }" />
                      <DxDataGridColumn data-field="Facturar" caption="Factura" data-type="number" :format="{
              formatter: (value) =>
                `Q ${value.toLocaleString('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}`,
            }" />
                      <DxDataGridColumn data-field="Devolucion" caption="Devolución" data-type="number" :format="{
              formatter: (value) =>
                `Q ${value.toLocaleString('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}`,
            }" />
                      <DxDataGridColumn data-field="NotaCredito" caption="Nota Crédito" data-type="number" :format="{
              formatter: (value) =>
                `Q ${value.toLocaleString('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}`,
            }" />
                      <DxDataGridColumn data-field="Bodega" caption="Bodega" />
                      <DxDataGridColumn data-field="SerieFActura" caption="Serie Factura" />
                      <DxDataGridColumn data-field="Factura" caption="Factura" data-type="number" />
                      <DxDataGridColumn data-field="SerieNCredito" caption="Serie Nota Crédito" />
                      <DxDataGridColumn data-field="NCredito" caption="Nota Crédito" data-type="number" />
                      <DxDataGridScrolling mode="virtual" />
                  </DxDataGrid>

                  <br />
                  <h6 style="margin: 5px">Venta Directa</h6>
                  <DxDataGrid :data-source="VentaDirecta" :allow-column-reordering="true" :allow-column-resizing="true" :column-auto-width="true" :show-borders="true" data-table-id="venta-directa" @selection-changed="onSelectionChanged('venta-directa', $event)" ref="ventadirectaGrid"
                  :height="230">
                      <DxDataGridHeaderFilter :visible="true" />
                      <DxDataGridSelection mode="single" />
                      <DxDataGridColumn data-field="EmpresaReal" caption="Empresa" />
                      <DxDataGridColumn data-field="Debe" caption="Debe" data-type="number" />
                      <DxDataGridColumn data-field="CuentaDebe" caption="Cuenta Debe" />
                      <DxDataGridColumn data-field="Haber" caption="Haber" data-type="number" />
                      <DxDataGridColumn data-field="CuentaHaber" caption="Cuenta Haber" />
                      <DxDataGridColumn data-field="CostoTotal" caption="Costo Total" data-type="number" :format="{
              formatter: (value) =>
                `Q ${value.toLocaleString('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}`,
            }" />
                      <DxDataGridColumn data-field="CostoTotalHospital" caption="Costo Total Hospital" data-type="number" :format="{
              formatter: (value) =>
                `Q ${value.toLocaleString('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}`,
            }" />
                      <DxDataGridColumn data-field="Facturar" caption="Factura" data-type="number" :format="{
              formatter: (value) =>
                `Q ${value.toLocaleString('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}`,
            }" />
                      <DxDataGridColumn data-field="Devolucion" caption="Devolución" data-type="number" :format="{
              formatter: (value) =>
                `Q ${value.toLocaleString('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}`,
            }" />
                      <DxDataGridColumn data-field="NotaCredito" caption="Nota Crédito" data-type="number" :format="{
              formatter: (value) =>
                `Q ${value.toLocaleString('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}`,
            }" />
                      <DxDataGridColumn data-field="Bodega" caption="Bodega" />
                      <DxDataGridColumn data-field="SerieFActura" caption="Serie Factura" />
                      <DxDataGridColumn data-field="Factura" caption="Factura" data-type="number" />
                      <DxDataGridColumn data-field="SerieNCredito" caption="Serie Nota Crédito" />
                      <DxDataGridColumn data-field="NCredito" caption="Nota Crédito" data-type="number" />
                      <DxDataGridScrolling mode="virtual" />
                  </DxDataGrid>
                  <br>
                  <div v-if="EmpleadosFac && EmpleadosFac.length > 0">
                      <h6 style="margin: 5px">Facturación Empleados</h6>
                      <DxDataGrid :data-source="EmpleadosFac" :allow-column-reordering="true" :allow-column-resizing="true" :column-auto-width="true" :show-borders="true">
                          <DxDataGridHeaderFilter :visible="true" />
                          <DxDataGridColumn data-field="EmpresaReal" caption="Empresa" />
                          <DxDataGridColumn data-field="Debe" caption="Debe" data-type="number" />
                          <DxDataGridColumn data-field="CuentaDebe" caption="Cuenta Debe" />
                          <DxDataGridColumn data-field="Haber" caption="Haber" data-type="number" />
                          <DxDataGridColumn data-field="CuentaHaber" caption="Cuenta Haber" />
                          <DxDataGridColumn data-field="CostoTotal" caption="Costo Total" data-type="number" :format="{
              formatter: (value) =>
                `Q ${value.toLocaleString('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}`,
            }" />
                          <DxDataGridColumn data-field="CostoTotalHospital" caption="Costo Total Hospital" data-type="number" :format="{
              formatter: (value) =>
                `Q ${value.toLocaleString('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}`,
            }" />
                          <DxDataGridColumn data-field="Facturar" caption="Factura" data-type="number" :format="{
              formatter: (value) =>
                `Q ${value.toLocaleString('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}`,
            }" />
                          <DxDataGridColumn data-field="Devolucion" caption="Devolución" data-type="number" :format="{
              formatter: (value) =>
                `Q ${value.toLocaleString('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}`,
            }" />
                          <DxDataGridColumn data-field="NotaCredito" caption="Nota Crédito" data-type="number" :format="{
              formatter: (value) =>
                `Q ${value.toLocaleString('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}`,
            }" />
                          <DxDataGridColumn data-field="Bodega" caption="Bodega" />
                          <DxDataGridColumn data-field="SerieFActura" caption="Serie Factura" />
                          <DxDataGridColumn data-field="Factura" caption="Factura" data-type="number" />
                          <DxDataGridColumn data-field="SerieNCredito" caption="Serie Nota Crédito" />
                          <DxDataGridColumn data-field="NCredito" caption="Nota Crédito" data-type="number" />
                          <DxDataGridScrolling mode="virtual" />
                      </DxDataGrid>
                  </div>
              </vx-card>
          </div>
      </div>

      <DxPopup :visible="isPopupVisible" :close-on-outside-click="false" width="1000px" height="auto" @hiding="onPopupHiding">
          <div class="popup-content">
              <div>
                  <!--//===========================================================================================================================================//
      //                                          Funciones Reportes Facturación Sermesa Centroamericana                                               //
      //===============================================================================================================================================//-->

                  <form action="">
                      <h3 style="text-align: center">Facturas y Notas de Crédito</h3>
                      <vs-divider position="left"> </vs-divider>
                      <div style="display: flex; margin-top: 5px">
                          <vs-select class="w-full" v-model="info.OpcionesTable">
                              <vs-select-item v-for="(item, index) in OpcionesTable" :key="index" :value="item.value" :text="item.text" />
                          </vs-select>
                      </div>
                      <div style="text-align: right; margin-top: 15px"></div>
                      <div class="Espacio" v-if="info.OpcionesTable">
                          <div style="display: flex">
                              <vs-select class="w-full" label="Empresa" v-model="Reportes.Sucursal" v-if="info.OpcionesTable !== '0'">
                                  <vs-select-item v-for="(item, index) in Sucursales" :key="index" :value="item.EmpresaReal" :text="item.Nombre" />
                              </vs-select>

                              &nbsp;
                              <vs-select v-if="['V', 'VN', 'D'].includes(info.OpcionesTable)" class="w-full" label="Tipo Bodega" label-placeholder="Autocomplete" autocomplete v-model="info.TipoBodega">
                                  <vs-select-item :key="index" :value="item.Codigo" :text="item.NombreMasCodigo" v-for="(item, index) in Bodegas" />
                              </vs-select>
                          </div>

                          <div v-if="['V', 'VN', 'D'].includes(info.OpcionesTable)" style="margin-top: 10px">
                              <vs-select class="w-full" label="Tipo" v-model="info.SubOpcion" @change="updateTypes">
                                  <vs-select-item v-for="(item, index) in SubOpciones" :key="index" :value="item.value" :text="item.text" />
                              </vs-select>
                          </div>
                          <!--   <div v-if="['DF'].includes(info.OpcionesTable)" style="margin-top: 10px;">
                              <vs-checkbox :value="1" v-model="selectedCheckbox">Desvincular</vs-checkbox>
                          </div>-->
                          <div v-if="['V', 'DF', 'VN', 'D'].includes(info.OpcionesTable)" style="display: flex; margin-top: 10px">
                              <vs-input class="w-1/2" type="date" label="Fecha Inicial" v-model="Reportes.FechaInicial"></vs-input>&nbsp;
                              <vs-input class="w-1/2" type="date" label="Fecha Final" v-model="Reportes.FechaFinal"></vs-input>
                          </div>
                          <div style="display: flex; margin-top: 10px" v-if="['V', 'DF', 'F'].includes(info.OpcionesTable)">
                              <vs-input class="w-1/2" label="Serie Factura" v-model="info.Serie" type="text" />&nbsp;
                              <vs-input class="w-1/2" label="Número Serie" v-model="info.Numero" type="text" />&nbsp;
                              <vs-input class="w-1/2" v-if="['V'].includes(info.OpcionesTable)" label="Fecha Factura" v-model="info.Fecha" color="success" type="date" />&nbsp;
                          </div>
                          <div style="display: flex; margin-top: 10px" v-if="['DN'].includes(info.OpcionesTable)">
                              <vs-input class="w-1/2" label="Serie Nota de Crédito" v-model="info.SerieN" type="text" />&nbsp;
                              <vs-input class="w-1/2" label="Número Nota de Crédito" v-model="info.NumeroN" type="text" />&nbsp;
                              <vs-input class="w-1/2" v-if="['V'].includes(info.OpcionesTable)" label="Fecha Factura" v-model="info.Fecha" color="success" type="date" />&nbsp;
                          </div>
                          <div style="display: flex; margin-top: 10px" v-if="['VN', 'D'].includes(info.OpcionesTable)">
                              <vs-input class="w-1/2" v-model="info.SerieN" label="Serie Nota Crédito" type="text" />&nbsp;
                              <vs-input class="w-1/2" v-model="info.NumeroN" label="Número Serie Nota Crédito" type="text" />&nbsp;
                          </div>
                          <div v-if="['VN'].includes(info.OpcionesTable)">
                              <vs-input class="w-1/2" v-model="info.Fecha" label="Fecha Nota de Crédito" color="success" type="date" />&nbsp;
                          </div>
                          <div style="display: flex; margin-top: 10px">
                              <vs-tooltip text="Vincular Factura" position="top">
                                  <vs-button v-if="['V'].includes(info.OpcionesTable)" @click="Vincular().Vinculacion()" color="success" type="filled"><i class="fas fa-link"></i> Vincular Factura</vs-button>
                              </vs-tooltip>
                              <vs-tooltip text="Desvincular Factura" position="top">
                                  <vs-button v-if="['DF'].includes(info.OpcionesTable)" color="primary" @click="desVincular().desVinculacion()" type="filled"><i class="fas fa-unlink"></i> Desvincular Factura
                                  </vs-button>
                              </vs-tooltip>
                              <vs-tooltip text="Vincular Nota de Crédito">
                                  <vs-button v-if="['VN'].includes(info.OpcionesTable)" @click="Vincular().Vinculacion()" color="success" type="filled"><i class="fas fa-link"></i> Vincular Nota Crédito
                                  </vs-button>
                              </vs-tooltip>
                              <vs-tooltip text=" Desvincular Nota de Crédito">
                                  <vs-button v-if="['D'].includes(info.OpcionesTable)" color="primary" type="filled" @click="desVincular().desVinculacion()"><i class="fas fa-unlink"></i> Desvincular Nota de Crédito
                                  </vs-button>
                              </vs-tooltip>
                              <vs-tooltip text="Ver Factura" position="top">
                                  <vs-button v-if="info.OpcionesTable === 'F'" color="primary" type="filled" @click="CargarPDF()"><i class="fas fa-eye"></i> Ver Factura</vs-button>&nbsp;
                              </vs-tooltip>

                              <vs-tooltip text="Excel" position="top">
                                  <vs-button v-if="info.OpcionesTable === 'F'" color="success" type="filled" @click="CargarExcelFactura()">
                                      <i class="fas fa-file-excel"></i> Excel</vs-button>&nbsp;
                              </vs-tooltip>

                              <vs-tooltip text="Ver Nota de Crédito" position="top">
                                  <vs-button v-if="info.OpcionesTable === 'DN'" color="primary" type="filled" @click="CargarNotaDetalle()"><i class="fas fa-eye"></i> Ver Nota Crédito</vs-button>&nbsp;
                              </vs-tooltip>
                              <vs-tooltip text="Excel" position="top">
                                  <vs-button v-if="info.OpcionesTable === 'DN'" color="success" type="filled" @click="CargarExcelNotaCredito()">
                                      <i class="fas fa-file-excel"></i> Excel</vs-button>&nbsp;
                              </vs-tooltip>
                              <vs-tooltip text="Eliminar Autoconsumo" position="top">
                                  <vs-button v-if="['V', 'DF'].includes(info.OpcionesTable)" color="warning" type="filled" @click="eliminarAutoConsumo()" icon="close">Eliminar Autoconsumo</vs-button>&nbsp;
                              </vs-tooltip>
                          </div>
                      </div>
                  </form>
              </div>
          </div>
      </DxPopup>
  </div>
</template>

<script>
import Swal from "sweetalert2";


export default {
  data() {
    return {
      isPopupVisible: false, // Controla la visibilidad del popup
      selectedRowData: null, // Almacena los datos de la fila seleccionada

      info: {
        Sucursal: null,
        Opcion: null,
        SubOpcion: null,
        SubOpcionEmpleados: null,
        FechaInicial: null,
        FechaFinal: null,
        TipoBodega: null,
        OpcionesTable: "0",
        Tipos1: null,
        Tipos2: null,
        Serie: null,
        Numero: null,
        Fecha: null,
        SerieN: null,
        NumeroN: null,
      },
      Reportes: {
        Sucursal: null,
        SubOpcion: null,
        Opcion: null,
        FechaInicial: null,
        FechaFinal: null,
      },

      SubOpciones: [
        {
          value: "1",
          text: "Requerimientos",
        },
        {
          value: "2",
          text: "Cargos",
        },
        {
          value: "3",
          text: "Venta Directa",
        },
      ],

      Opcion: [
        {
          value: "1",
          text: "Detalle",
        },
        {
          value: "2",
          text: "Resumen",
        },
      ],

      OpcionesTable: [
        {
          value: "0",
          text: "Seleccionar Opción",
        },
        {
          value: "V",
          text: "Vincular Factura",
        },
        {
          value: "DF",
          text: "Desvincular Factura",
        },
        {
          value: "VN",
          text: "Vincular Nota de Crédito",
        },
        {
          value: "D",
          text: "Devincular Nota de Crédito",
        },
        {
          value: "F",
          text: "Detalle Factura",
        },
        {
          value: "DN",
          text: "Detalle Nota de Crédito",
        },
      ],

      //Opciones Reporte

      Opciones: [
        {
          value: "D",
          text: "Detalle",
        },
        {
          value: "C",
          text: "Resumen",
        },
        {
          value: "N",
          text: "Notas de Credito",
        },
        {
          value: "E",
          text: "Facturación Empleados",
        },
      ],

      Tipos1: [
        {
          value: "RL",
          text: "Requerimientos RL",
        },
        {
          value: "CT",
          text: "Cargos CT",
        },
        {
          value: "VL",
          text: "Venta Directa VL",
        },
      ],

      Tipos2: [
        {
          value: "DL",
          text: "Requerimientos DL",
        },
        {
          value: "OR",
          text: "Cargos OR",
        },
        {
          value: "VF",
          text: "Venta Directa VF",
        },
      ],

      Consultas: [],
      Sucursales: [],
      collapsedRows: [],
      Bodegas: [],
      Cargos: [],
      Requerimientos: [],
      VentaDirecta: [],
      EmpleadosFac:[],
      selectedCheckbox: "1",
      popupVisible: false,
      selectedTable: null,
      botonPresionado: null,
    };
  },

  computed: {
    showTipoBodega() {
      return ["V", "VN", "D"].includes(this.info.OpcionesTable);
    },
    showCheckboxes() {
      return ["V", "VN", "D"].includes(this.info.OpcionesTable);
    },
    showTiposSelect() {
      return ["V", "VN", "D"].includes(this.info.OpcionesTable);
    },
    showDateInputs() {
      return ["V", "DF", "VN", "D"].includes(this.info.OpcionesTable);
    },
    showFacturaInputs() {
      return ["V", "DF", "F", "DN"].includes(this.info.OpcionesTable);
    },
    showNotaCreditoInputs() {
      return ["VN", "D"].includes(this.info.OpcionesTable);
    },
  },
  methods: {
    //===========================================================================================================================================//
    //                                                             Cambios de pantallas                                                          //
    //===========================================================================================================================================//
    toggleCard() {
      this.showCard = !this.showCard;
      this.LimpiarCampos();
    },

    toggleCollapse(index) {
      const idx = this.collapsedRows.indexOf(index);
      if (idx > -1) {
        this.collapsedRows.splice(idx, 1);
      } else {
        this.collapsedRows.push(index);
      }
    },

    //===========================================================================================================================================//
    //                                                             Consultas de información                                                      //
    //===========================================================================================================================================//
    ConsultaEmpresa: function () {
      return {
        ListadoEmpresas: () => {
          this.axios
            .post("/app/v1_autorizaciones/ConsultaEmpresa", {
              Opcion: "0",
            })
            .then((resp) => {
              if (resp.data.codigo == "0") {
                this.Sucursales = resp.data.json;
              }
            });
        },
      };
    },

    Consulta: function () {
      return {
        Consultas: () => {
          this.axios
            .post("/app/v1_contabilidad_general/ConsultaDetalleResumen", {
              Opcion: "C",
              Hospital: this.info.Sucursal,
              SubOpcion: this.info.SubOpcion,
              Fecha: this.info.FechaInicial,
              FechaF: this.info.FechaFinal,
            })
            .then((resp) => {
              if (resp.data.codigo == "0") {
                this.Consultas = resp.data.json;
              }
            });
        },
      };
    },

    ConsultaBodega: function () {
      return {
        ConsultasB: () => {
          this.axios
            .post("/app/v1_contabilidad_general/ConsultaBodegas", {
              Tipo: "CU",
            })
            .then((resp) => {
              if (resp.data.codigo == "0") {
                this.Bodegas = resp.data.json;
              }
            });
        },
      };
    },

    GenerarDatos(botonId) {
      this.botonPresionado = botonId; // Establecemos que el botón "Todos" fue presionado
      this.ConsultaResumenRequerimientos().Requerimientos(); // Llamamos la primera consulta
    },
    ConsultaResumenRequerimientos: function () {
      return {
        Requerimientos: () => {
          this.axios
            .post("/app/v1_contabilidad_general/ConsultaResumen", {
              Hospital: this.Reportes.Sucursal,
              Opcion: "C",
              SubOpcion: 1,
              FechaI: this.Reportes.FechaInicial,
              FechaF: this.Reportes.FechaFinal,
            })
            .then((resp) => {
              if (resp.data.codigo == "0") {
                this.Requerimientos = resp.data.json;
              }
              if (this.botonPresionado === 1) {
                this.ConsultaResumenCargos().Cargos();
                this.ConsultaResumenVentaDirecta().VentaDirecta();
                
                this.botonPresionado = null;
              }
            })
            .catch(() => {
              Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Hubo Un problema con la carga de datos.",
                customClass: {
                  container: "FrentePopup",
                  popup: "FrentePopup",
                  content: "FrentePopup",
                },
              });
            });
        },
      };
    },

    ConsultaResumenCargos: function () {
      return {
        Cargos: () => {
          this.axios
            .post("/app/v1_contabilidad_general/ConsultaResumen", {
              Hospital: this.Reportes.Sucursal,
              Opcion: "C",
              SubOpcion: 2,
              FechaI: this.Reportes.FechaInicial,
              FechaF: this.Reportes.FechaFinal,
            })
            .then((resp) => {
              if (resp.data.codigo == "0") {
                this.Cargos = resp.data.json;
              }
            });
        },
      };
    },

    ConsultaResumenVentaDirecta: function () {
      return {
        VentaDirecta: () => {
          this.axios
            .post("/app/v1_contabilidad_general/ConsultaResumen", {
              Hospital: this.Reportes.Sucursal,
              Opcion: "C",
              SubOpcion: 3,
              FechaI: this.Reportes.FechaInicial,
              FechaF: this.Reportes.FechaFinal,
            })
            .then((resp) => {
              if (resp.data.codigo == "0") {
                this.VentaDirecta = resp.data.json;
              }
            });
        },
      };
    },

    ConsultaEmpleadosFacturados: function(){
      return{
        Empleados :() => {
          this.axios
            .post("/app/v1_contabilidad_general/ConsultaResumen", {
              Hospital: this.Reportes.Sucursal,
              Opcion: "E",
              SubOpcion: 1,
              SubOpcioneEmp: 2,
              FechaI: this.Reportes.FechaInicial,
              FechaF: this.Reportes.FechaFinal,
            })

            .then((resp) => {
              if (resp.data.codigo == "0") {
                this.EmpleadosFac = resp.data.json;
              }
            });
        }
      }
    },
    //===========================================================================================================================================//
    //                                                       Vinculación y Desvinculación                                                        //
    //===========================================================================================================================================//

    Vincular: function () {
      return {
        Vinculacion: async () => {
          // Convertimos la función a async
          // Llamar a updateTypes para actualizar los valores de Tipo1 y Tipo2
          this.updateTypes();
          const payload = {
            Opcion: this.info.OpcionesTable, // Corrección de uso de this
            Sucursal: this.Reportes.Sucursal,
            SubOpcion: this.info.SubOpcion,
            Fecha: this.Reportes.FechaInicial,
            FechaF: this.Reportes.FechaFinal,
            Tipo1: this.info.tipo1, // Actualización de Tipo1 y Tipo2 después de updateTypes
            Tipo2: this.info.tipo2,
            Bodega: this.info.TipoBodega,
          };
          // Agregar campos específicos dependiendo del valor de this.info.OpcionesTable
          if (this.info.OpcionesTable === "VN") {
            payload.SerieNote = this.info.SerieN;
            payload.NumeroNote = this.info.NumeroN;
            payload.FechaNota = this.info.Fecha;
          } else if (this.info.OpcionesTable === "V") {
            payload.SerieFac = this.info.Serie;
            payload.NumeroFac = this.info.Numero;
            payload.FechaFac = this.info.Fecha;
          }
          try {
            // Esperar a que la solicitud POST se complete
            await this.axios.post(
              "/app/v1_contabilidad_general/Vincular",
              payload
            );
            this.onPopupHiding(); // Esta parte se ejecuta después de la respuesta
            // Ahora ejecutas la lógica condicional
            if (this.info.SubOpcion == 1) {
              this.ConsultaResumenRequerimientos().Requerimientos();
            } else if (this.info.SubOpcion == 2) {
              this.ConsultaResumenCargos().Cargos();
            } else {
              this.ConsultaResumenVentaDirecta().VentaDirecta();
            }
          } catch (error) {
            Swal.fire({
              icon: "error",
              title: "Oops...",
              text: "Hubo Un problema con la carga de datos.",
              customClass: {
                container: "FrentePopup",
                popup: "FrentePopup",
                content: "FrentePopup",
              },
            });
          }
        },
      };
    },

   desVincular: function () {
      return {
        desVinculacion: async () => {
          // Convertimos la función a async
          this.updateTypes();
          const payload = {
            Opcion: this.info.OpcionesTable, // Corrección de uso de this
            Sucursal: this.Reportes.Sucursal,
            SubOpcion: this.info.SubOpcion,
            Fecha: this.Reportes.FechaInicial,
            FechaF: this.Reportes.FechaFinal,
            Tipo1: this.info.tipo1,
            Tipo2: this.info.tipo2,
            Bodega: this.info.TipoBodega,
          };
          // Agregar campos específicos dependiendo del valor de OpcionesTable
          if (this.info.OpcionesTable === "D") {
            payload.SerieNote = this.info.SerieN;
            payload.NumeroNote = this.info.NumeroN;
            payload.FechaNota = this.info.Fecha;
          } else if (this.info.OpcionesTable === "DF") {
            payload.SerieFac = this.info.Serie;
            payload.NumeroFac = this.info.Numero;
          }
          try {
            // Esperar a que la solicitud POST se complete
            await this.axios.post(
              "/app/v1_contabilidad_general/desVincular",
              payload
            );
            this.onPopupHiding(); // Esta parte se ejecuta después de la respuesta

           if (this.info.SubOpcion == 1) {
              this.ConsultaResumenRequerimientos().Requerimientos();
            } else if (this.info.SubOpcion == 2) {
              this.ConsultaResumenCargos().Cargos();
            } else {
              this.ConsultaResumenVentaDirecta().VentaDirecta();
            }

          } catch (error) {
            Swal.fire({
              icon: "error",
              title: "Oops...",
              text: "Hubo un problema con la carga de datos.",
              customClass: {
                container: "FrentePopup",
                popup: "FrentePopup",
                content: "FrentePopup",
              },
            });
          }
        },
      };
    }, 
	
    //===========================================================================================================================================//
    //                                                        Eliminación de Autoconsumo                                                         //
    //===========================================================================================================================================//
    eliminarAutoConsumo() {
      this.axios.post("/app/v1_contabilidad_general/eliminarAutoConsumo", {
        FechaI: this.Reportes.FechaInicial,
        FechaF: this.Reportes.FechaFinal,
      });
    },

    //===========================================================================================================================================//
    //                                                         Generación de PDF y EXCEL                                                         //
    //===========================================================================================================================================//

    /*Ganerar detalle factura PDF*/
    CargarPDF() {
      this.$reporte_modal({
        Nombre: "Detalle Facturación",
        Opciones: {
          Opcion: this.info.OpcionesTable,
          Hospital: this.Reportes.Sucursal,
          SubOpcion: "1",
          SerieFac: this.info.Serie,
          Factura: this.info.Numero,
        },
      })
        .then(() => {
          // Aquí puedes manejar la respuesta si es exitosa
          // Por ejemplo, mostrar una notificación de éxito o procesar los datos
        })
        .catch(() => {
          // Manejar otros errores si es necesario
          Swal.fire({
            icon: "error",
            title: "Oops...",
            text: "No se encontro información para mostrar.",
            customClass: {
              container: "FrentePopup",
              popup: "FrentePopup",
              content: "FrentePopup",
            },
          });
        });
    },

    /*Ganerar detalle Factura Excel*/
    CargarExcelFactura() {
      this.$reporte_modal({
        Nombre: "Detalle Facturación Excel",
        Formato: "EXCEL",
        Opciones: {
          Opcion: this.info.OpcionesTable,
          Hospital: this.Reportes.Sucursal,
          SubOpcion: "1",
          SerieFac: this.info.Serie,
          Factura: this.info.Numero,
        },
      })
        .then(() => {
          // Aquí puedes manejar la respuesta si es exitosa
          // Por ejemplo, mostrar una notificación de éxito o procesar los datos
        })
        .catch(() => {
          // Manejar otros errores si es necesario
          Swal.fire({
            icon: "error",
            title: "Oops...",
            text: "No se encontro información para mostrar.",
            customClass: {
              container: "FrentePopup",
              popup: "FrentePopup",
              content: "FrentePopup",
            },
          });
        });
    },

    /*Genera detalle de Nota de credito PDF */
    CargarNotaDetalle() {
      this.$reporte_modal({
        Nombre: "DetalleNotaCredito",
        Opciones: {
          Opcion: this.info.OpcionesTable,
          Hospital: this.Reportes.Sucursal,
          SubOpcion: "1",
          NotaCredito: this.info.NumeroN,
          SerieNotaCredito: this.info.SerieN,
        },
      })
        .then(() => {
          // Aquí puedes manejar la respuesta si es exitosa
          // Por ejemplo, mostrar una notificación de éxito o procesar los datos
        })
        .catch(() => {
          // Manejar otros errores si es necesario
          Swal.fire({
            icon: "error",
            title: "Oops...",
            text: "No se encontro información para mostrar.",
            customClass: {
              container: "FrentePopup",
              popup: "FrentePopup",
              content: "FrentePopup",
            },
          });
        });
    },

    CargarExcelNotaCredito() {
      this.$reporte_modal({
        Nombre: "DetalleNotaCreditoExcel",
        Formato: "EXCEL",
        Opciones: {
          Opcion: this.info.OpcionesTable,
          Hospital: this.Reportes.Sucursal,
          SubOpcion: "1",
          NotaCredito: this.info.NumeroN,
          SerieNotaCredito: this.info.SerieN,
        },
      })
        .then(() => {
          // Aquí puedes manejar la respuesta si es exitosa
          // Por ejemplo, mostrar una notificación de éxito o procesar los datos
        })
        .catch(() => {
          // Manejar otros errores si es necesario
          Swal.fire({
            icon: "error",
            title: "Oops...",
            text: "No se encontro información para mostrar.",
            customClass: {
              container: "FrentePopup",
              popup: "FrentePopup",
              content: "FrentePopup",
            },
          });
        });
    },

    /*Ganerar Reporte Excel Facturación*/
    CargarExcel() {
      this.$reporte_modal({
        Nombre: "Facturación CA Resumen y Detalle",
        Formato: "EXCEL",
        Opciones: {
          Hospital: this.Reportes.Sucursal,
          Opcion: this.info.Opcion,
          SubOpcion: this.info.SubOpcion,
          SubOpcionEmp: this.info.SubOpcionEmpleados,
          Fecha: this.Reportes.FechaInicial,
          FechaF: this.Reportes.FechaFinal,
        },
      })
        .then(() => {
          // Aquí puedes manejar la respuesta si es exitosa
          // Por ejemplo, mostrar una notificación de éxito o procesar los datos
        })
        .catch(() => {
          // Manejar otros errores si es necesario
          Swal.fire({
            icon: "error",
            title: "Oops...",
            text: "No se encontro información para mostrar.",
            customClass: {
              container: "FrentePopup",
              popup: "FrentePopup",
              content: "FrentePopup",
            },
          });
        });
    },

    //===========================================================================================================================================//
    //                                 Actualiza los select con los tipos de requerimientos, cargos y venta directa                              //
    //===========================================================================================================================================//

    onSelectionChanged(tableId, e) {
      const selectedRow = e.selectedRowsData[0]; // Obtiene la fila seleccionada
      if (!selectedRow) return;

      // Validaciones existentes
      if (selectedRow.Bodega) {
        this.info.TipoBodega = selectedRow.Bodega; // Asigna el valor de la bodega
        this.info.Serie = selectedRow.SerieFActura;
        this.info.Numero = selectedRow.Factura;
        this.info.SerieN = selectedRow.SerieNCredito;
        this.info.NumeroN = selectedRow.NCredito;
        this.isPopupVisible = true; // Muestra el popup
      }

      if (selectedRow.Factura && selectedRow.SerieFActura) {
        Swal.fire({
          icon: "question",
          title: "Alerta!",
          text: "Ya cuenta con Factura Vinculada",
          confirmButtonColor: "#27ae60",
          customClass: {
            container: "FrentePopup",
            popup: "FrentePopup",
            content: "FrentePopup",
          },
        });
      }

      if (selectedRow.SerieNCredito || selectedRow.NCredito) {
        Swal.fire({
          icon: "question",
          title: "Alerta!",
          text: "Ya cuenta con Nota de Crédito Vinculada ",
          confirmButtonColor: "#27ae60",
          customClass: {
            container: "FrentePopup",
            popup: "FrentePopup",
            content: "FrentePopup",
          },
        });
      }

      // Lógica para actualizar SubOpcion según la tabla seleccionada
      switch (tableId) {
        case "requerimientos":
          this.info.SubOpcion = "1";
          break;
        case "cargos":
          this.info.SubOpcion = "2";
          break;
        case "venta-directa":
          this.info.SubOpcion = "3";
          break;
        default:
          this.info.SubOpcion = null;
      }

      // Llamar a updateTypes para ajustar tipo1 y tipo2
      this.updateTypes();
    },

    updateTypes() {
      switch (this.info.SubOpcion) {
        case "1":
          this.info.tipo1 = "RL";
          this.info.tipo2 = "DL";
          break;
        case "2":
          this.info.tipo1 = "CT";
          this.info.tipo2 = "OR";
          break;
        case "3":
          this.info.tipo1 = "VL";
          this.info.tipo2 = "VF";
          break;
        default:
          this.info.tipo1 = "";
          this.info.tipo2 = "";
      }
    },

    onPopupHiding() {
      this.isPopupVisible = false; // Oculta el popup al cerrar
      this.info.OpcionesTable = "0";
      this.$refs.requerimientosGrid.instance.clearSelection();
      this.$refs.cargosGrid.instance.clearSelection();
      this.$refs.ventadirectaGrid.instance.clearSelection();
    },
    //===========================================================================================================================================//
    //                                                             Limpia los datos                                                              //
    //===========================================================================================================================================//

    LimpiarCampos() {
      (this.info.Sucursal = null),
        (this.info.Opcion = null),
        (this.info.SubOpcionEmpleados = null),
        (this.info.SubOpcion = null),
        (this.info.FechaInicial = null),
        (this.info.FechaFinal = null),
        (this.info.OpcionesTable = "0");
       this.Resumen = [];
    },

    Limpiar() {
      (this.info.TipoBodega = null),
        (this.info.Tipos1 = null),
        (this.info.Tipos2 = null),
        (this.info.SubOpcion = null),
        (this.info.FechaInicial = null),
        (this.info.FechaFinal = null),
        (this.info.TipoBodega = null),
        (this.info.Fecha = null),
        (this.info.Numero = null),
        (this.info.Serie = null),
        (this.info.Sucursal = null);
    },

    LimpiarDatos() {
      this.Reportes.Sucursal = null;
      this.Reportes.FechaFinal = null;
      this.Reportes.FechaInicial = null;
      this.Reportes.Opcion = null;
      this.Cargos = [];
      this.Requerimientos = [];
      this.VentaDirecta = [];
        this.EmpleadosFac = [];
      this.Limpiar();
      this.LimpiarCampos();

    },
  },

  mounted() {
    this.ConsultaEmpresa().ListadoEmpresas();
    this.ConsultaBodega().ConsultasB();
  },
};
</script>

<style scoped>
.date-inputs {
  display: flex;
  gap: 8px;
}

.permiso {
  padding: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  margin-bottom: 5px;
  cursor: pointer;
  font-size: 15px;
}

.permiso-sub {
  padding: 15px;
}

.permiso-sub table {
  border-spacing: 0px;
}

.permiso-sub table tr td {
  padding: 2px;
}

.permiso-sub table tr:hover {
  background-color: #ccc;
}

.derecha {
  display: flex;
  justify-content: flex-end;
}
</style>
<style>
.long-title h3 {
  font-size: 24px;
  text-align: center;
  line-height: 2em;
}

.vertical-tabs-container {
  display: flex;
}

.vertical-tabs {
  display: flex;
  flex-direction: column; /* Coloca los tabs en columna */
}

.vertical-tabs .vs-tabs__header {
  display: flex;
  flex-direction: column; /* Asegura que los tabs queden en forma vertical */
  min-width: 200px; /* Ancho ajustable para los tabs */
}

.vertical-tabs .vs-tabs__items {
  width: 100%;
  padding-left: 20px; /* Espacio entre los tabs y el contenido */
}

.vertical-tabs .vs-tab {
  display: flex;
  justify-content: start;
}

.FrentePopup {
  z-index: 52001 !important;
}

.dx-datagrid-rowsview
  .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed)
  > td {
  background-color: #d4efdf !important;
  color: black !important;
}

.dx-datagrid-rowsview .dx-row.dx-data-row.dx-column-lines.dx-state-hover > td {
  background-color: #d6eaf8 !important;
  color: #273746 !important;
  font-size: 16px;
}

.dx-datagrid-headers {
  background-color: #5dade2 !important;
  color: #273746 !important;
}
</style>
