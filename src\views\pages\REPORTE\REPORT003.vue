<template>
<vx-card title="Administración de Formularios">
    <buscador ref="buscar_aplicativo" buscador_titulo="Buscador / Aplicativo" :data="list_aplicativo" campo_llave="IdAplicativo" :campos="['Nombre']" :titulos="['Aplicativo']" :multiselect="false" :api_validar_campo="true"  />
    <buscador ref="buscar_funcionalidad" buscador_titulo="Buscador / Funcionalidad" :data="list_funcionalidad" campo_llave="IdFuncionalidad" :campos="['Nombre']" :titulos="['Funcionalidad']" :multiselect="false" :api_validar_campo="true"  />

    <vs-popup classContent="popup-example" :title="(info.IdReporte)?'Editar Formulario':'Nuevo Formulario'" :active.sync="reporte_nuevoEditar">
        <form v-on:submit.prevent="cargar_orden()">
            <div class="flex flex-wrap">
                <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                    <vs-input label="Nombre" class="w-full" v-model="info.nombre" />
                </div>
                <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
                    <vs-input label="URL" class="w-full" v-model="info.url" />
                </div>
            </div>
            <div class="flex flex-wrap">
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <label class="vs-input--label">Aplicación</label>
                    <vx-input-group class>
                        <vs-button id="button-with-loading" class="w-full" color="primary" v-on:click="buscar_aplicativos()">{{info.Aplicativo}}</vs-button>
                    </vx-input-group>
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <label class="vs-input--label">Funcionalidad</label>
                    <vx-input-group class>
                        <vs-button id="button-with-loading" class="w-full" color="primary" :disabled="info.idAplicativo==0" v-on:click="buscar_funcionalidad()">{{info.Funcionalidad}}</vs-button>
                    </vx-input-group>
                </div>
            </div>
            <vs-divider />
            <vue-editor v-model="info.formulario" :editorToolbar="customToolbar" :disabled="bloqueoCampos"></vue-editor>
            <vs-divider />
            <vs-button style="float:right"  @click="guardar_reporte()" v-if="!info.IdReporte">Guardar</vs-button>
            <vs-button color="primary" style="float:right" type="filled" icon-pack="feather" icon="icon-save" @click="editar_reporte()" v-else>Editar</vs-button>
            <div style="clear:both"></div>
        </form>
    </vs-popup>

    <div class="flex flex-wrap">
        <div class="w-full">
            <vs-button color="primary" style="float:right" v-on:click="nuevoEditar(true)">Nuevo Formulario</vs-button>
        </div>
    </div>
    <!-- {{listado_reportes}} -->

    <vs-divider>Formularios</vs-divider>
    <vs-table :data="listado_reportes">
        <template slot="thead">
            <vs-th>Aplicativo</vs-th>
            <vs-th>Nombre</vs-th>
            <vs-th>Url</vs-th>
            <vs-th width="180px">Acciones</vs-th>
        </template>
        <template slot-scope="{data}">
            <vs-tr :key="indextr" v-for="(tr, indextr) in data">
                <vs-td :data="tr.Aplicativo">
                    {{ tr.Aplicativo }}
                    <div>
                        <small>{{tr.Funcionalidad}}</small>
                    </div>
                </vs-td>
                <vs-td :data="tr.Nombre">{{tr.Nombre}}</vs-td>
                <vs-td :data="tr.UrlApi">{{tr.UrlApi}}</vs-td>

                <vs-td>
                    <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" @click="nuevoEditar(false,tr)"></vs-button>
                    <vs-button color="danger" style="margin-left:1px;display:inline-block;" v-on:click="eliminar_reporte(tr.IdReporte)" icon-pack="fas" icon="fa-trash"></vs-button>
                    <vs-button color="warning" style="margin-left:1px;display:inline-block;" v-on:click="info_reporte(tr)" icon-pack="fas" icon="fa-info"></vs-button>
                </vs-td>
            </vs-tr>
        </template>
    </vs-table>
</vx-card>
</template>

<script>
import {
    VueEditor
} from "vue2-editor";
export default {
    data() {
        return {
            reporte_nuevoEditar: false,
            reporte_editar: false,

            listado_aplicativo: [],
            listado_reportes: [],
            listado_tipologia: [
                'Texto',
                'Numero',
                'Fecha',
                'Fecha/Hora',
                'Checkbox',
                'Oculto'
            ],
            parametros: {
                Index: null,
                Etiqueta: null,
                Parametro: null,
                Tipo: null,
                Eval: null,
                MensajeError: null,
                Obligatorio: false,
                ValorDefecto: null
            },
            info: {
                IdReporte: null,
                nombre: null,
                url: null,
                idAplicativo: 0,
                Aplicativo: "--",
                idFuncionalidad: 0,
                Funcionalidad: "--",
                formulario: null
            },
            customToolbar: [
                [{
                    'header': [1, 2, 3, 4, 5, 6, false]
                }],
                [{
                    'font': []
                }],
                ['bold', 'italic', 'underline', 'strike'],
                [{
                    list: "ordered"
                }, {
                    list: "bullet"
                }],
                [{
                    'indent': '-1'
                }, {
                    'indent': '+1'
                }],
                [{
                    'color': []
                }, {
                    'background': []
                }],
            ],
        };
    },
    components: {
        VueEditor,
    },
    computed: {
        list_aplicativo() {
            let arr = [];
            this.listado_aplicativo.map(data => {
                arr.push({
                    IdAplicativo: data.IdAplicativo,
                    Nombre: data.Nombre
                });
            });
            return arr;
        },
        list_funcionalidad() {
            let arr = [];
            // console.log(this.info.idAplicativo)
            this.listado_aplicativo
                .filter(data => data.IdAplicativo == this.info.idAplicativo)
                .map(data => {
                    // console.log(data)
                    data._level.map(data => {
                        arr.push({
                            IdFuncionalidad: data.IdFuncionalidad,
                            Nombre: data.Nombre_Funcionalidad
                        });
                    });
                });
            return arr;
        }
    },
    methods: {
        lista_aplicativos() {
            this.axios
                .post("/app/administracion/obtener_aplicativos", {})
                .then(resp => {
                    this.listado_aplicativo = resp.data.json;
                });
        },

        listado_reporte() {
            this.axios.post("/app/reporte/ReporteListado", {}).then(resp => {
                this.listado_reportes = resp.data.json;
            });
        },

        buscar_aplicativos() {
            this.$refs.buscar_aplicativo.iniciar(data => {
                // console.log(data)
                if (data != null) {
                    this.info.idAplicativo = data.IdAplicativo;
                    this.info.Aplicativo = data.Nombre;
                    this.info.idFuncionalidad = 0;
                    this.info.Funcionalidad = "--";
                }
            });
        },

        buscar_funcionalidad() {
            this.$refs.buscar_funcionalidad.iniciar(data => {
                // console.log(data)
                if (data != null) {
                    this.info.idFuncionalidad = data.IdAplicativo;
                    this.info.Funcionalidad = data.Nombre;
                }
            });
        },

        nuevoEditar(nuevo, data) {
            this.reporte_nuevoEditar = true;
            this.reporte_editar = !nuevo;

            this.parametros.Index = null
            this.parametros.Etiqueta = null
            this.parametros.Parametro = null
            this.parametros.Tipo = null
            this.parametros.Eval = null
            this.parametros.MensajeError = null
            this.parametros.Obligatorio = false
            this.parametros.ValorDefecto = null

            if (nuevo) {
                this.info.IdReporte = null;
                this.info.nombre = "";
                this.info.url = "";
                this.info.idAplicativo = 0;
                this.info.Aplicativo = "--";
                this.info.idFuncionalidad = 0;
                this.info.Funcionalidad = "--";
                this.info.listado_parametros = [];
            } else {
                // console.log(data)
                this.info.IdReporte = data.IdReporte;
                this.info.nombre = data.Nombre;
                this.info.url = data.UrlApi;
                this.info.idAplicativo = data.IdAplicativo;
                this.info.Aplicativo = data.Aplicativo;
                this.info.idFuncionalidad = data.IdFuncionalidad;
                this.info.Funcionalidad = (data.Funcionalidad != "") ? data.Funcionalidad : "--";
                this.info.listado_parametros = [...data._level]

            }
            // console.log(data)
        },

        nuevoParametro() {

            if (this.parametros.Index == null) {
                this.info.listado_parametros.push({
                    ...this.parametros
                });
            } else {
                this.info.listado_parametros[this.parametros.Index].Etiqueta = this.parametros.Etiqueta
                this.info.listado_parametros[this.parametros.Index].Parametro = this.parametros.Parametro
                this.info.listado_parametros[this.parametros.Index].Tipo = this.parametros.Tipo
                this.info.listado_parametros[this.parametros.Index].Eval = this.parametros.Eval
                this.info.listado_parametros[this.parametros.Index].MensajeError = this.parametros.MensajeError
                this.info.listado_parametros[this.parametros.Index].Obligatorio = (this.parametros.Obligatorio) ? 1 : 0
                this.info.listado_parametros[this.parametros.Index].ValorDefecto = this.parametros.ValorDefecto
            }

            this.parametros.Index = null
            this.parametros.Etiqueta = null
            this.parametros.Parametro = null
            this.parametros.Tipo = null
            this.parametros.Eval = null
            this.parametros.MensajeError = null
            this.parametros.Obligatorio = false
            this.parametros.ValorDefecto = null
        },

        editar_parametro(index) {
            let a = this.info.listado_parametros[index]
            this.parametros.Index = index
            this.parametros.Etiqueta = a.Etiqueta
            this.parametros.Parametro = a.Parametro
            this.parametros.Tipo = a.Tipo
            this.parametros.Eval = a.Eval
            this.parametros.MensajeError = a.MensajeError
            this.parametros.Obligatorio = (a.Obligatorio == 1) ? true : false
            this.parametros.ValorDefecto = a.ValorDefecto
        },

        eliminar_parametro(index) {
            this.info.listado_parametros.splice(index, 1)
        },

        guardar_reporte() {
            this.axios
                .post("/app/reporte/ReporteNuevo", {
                    Nombre: this.info.nombre,
                    Url: this.info.url,
                    IdAplicativo: this.info.idAplicativo,
                    IdFuncionalidad: this.info.idFuncionalidad,
                    Parametros: this.info.listado_parametros.map(data => {
                        return {
                            Etiqueta: data.Etiqueta,
                            Parametro: data.Parametro,
                            Tipo: data.Tipo,
                            Eval: data.Eval,
                            MensajeError: data.MensajeError,
                            Obligatorio: (data.Obligatorio == true || data.Obligatorio == 1) ? 1 : 0,
                            Defecto: data.ValorDefecto,
                        }

                    })
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.$vs.notify({
                            color: "success",
                            title: "Reporte",
                            text: "Se ha creado el reporte exitosamente"
                        });
                        this.listado_reporte();
                        this.reporte_nuevoEditar = false;
                    } else {
                        this.$vs.notify({
                            color: "danger",
                            title: "Reporte",
                            text: resp.data.descripcion
                        });
                    }
                });
        },

        editar_reporte() {
            this.axios
                .post("/app/reporte/ReporteEditar", {
                    IdReporte: this.info.IdReporte,
                    Nombre: this.info.nombre,
                    Url: this.info.url,
                    IdAplicativo: this.info.idAplicativo,
                    IdFuncionalidad: this.info.idFuncionalidad,
                    Parametros: this.info.listado_parametros.map(data => {
                        return {
                            Etiqueta: data.Etiqueta,
                            Parametro: data.Parametro,
                            Tipo: data.Tipo,
                            Eval: data.Eval,
                            MensajeError: data.MensajeError,
                            Obligatorio: (data.Obligatorio == true || data.Obligatorio == 1) ? 1 : 0,
                            Defecto: data.ValorDefecto
                        }

                    })
                })
                .then(() => {
                    this.$vs.notify({
                        color: "success",
                        title: "Reporte",
                        text: "Se ha editado el reporte exitosamente"
                    });
                    this.listado_reporte();
                    this.reporte_nuevoEditar = false;
                });
        },

        eliminar_reporte(IdReporte) {
            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Reportes',
                text: '¿Desea eliminar el reporte?',
                accept: () => {
                    this.axios
                        .post("/app/reporte/ReporteEliminar", {
                            IdReporte: IdReporte
                        })
                        .then(() => {
                            this.$vs.notify({
                                color: "success",
                                title: "Reporte",
                                text: "Se ha eliminado el reporte exitosamente"
                            });
                            this.listado_reporte();
                        });
                }
            })
        },

        info_reporte(reporte) {

            this.axios.get(reporte.UrlApi+'/status')
                .then(resp => {
                    this.$vs.dialog({
                        color: 'success',
                        title: 'Reportes',
                        text: resp.data,
                    });
                })
                .catch(err => {
                    this.$vs.dialog({
                        color: 'danger',
                        title: 'Reportes',
                        text: err,
                    });
                })

        },
    },
    created() {
        this.lista_aplicativos();
        this.listado_reporte();
    }
};
</script>

<style>
.button {
    margin: 0px !important;
}

.btnx {
    margin-left: 10px !important;
    background: red;
    border-radius: 5px 0px 0px 5px;
}

.btn-drop {
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: red;
}

.popup-generar {
    height: 100%;
}
</style>
