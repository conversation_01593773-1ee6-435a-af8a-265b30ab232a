<template>
    <vx-card title="Consulta Admisiones Estado">

        <div class="admmisionesEstado">

            <div class="seleccionAdmision">

                <div class="radio-button-group">                    
                    <div class="col-lg-12">
                       <BaseRadioButtonGroup
                         class="radio-button-group"
                         :options="radioOptions"
                         v-model="selTipoAdmision"
                         :groupName="'admisionCompletar'" 
                         :selected-option="this.selTipoAdmision.Descripcion"/>
                    </div>
                    
                </div>

            </div>

            <div class="listaAdmisiones">
                <div style="overflow:scroll;height:100%;width: 100%;overflow:auto">


                    <vs-table2 max-items="10" search tooltip pagination :data="lista_admisiones">
                        <template slot="thead">
                            <th width="50px" filtro="TipoAdmision">Tipo Admisión</th>
                            <th width="10px" filtro="Serie">Serie Admisión</th>
                            <th width="30px" filtro="CodigoAdmision">Admisión</th>
                            <th width="200px" filtro="NombrePaciente">Nombre Paciente</th>
                            <th width="50px" filtro="IngresoPaciente">Fecha Ingreso</th>
                            <th width="20px" filtro="EstadoProceso">HoraIngreso</th>
                            <th width="200px" filtro="Tiempo Transcurrido desde Admisión">Tiempo Transcurrido desde Admisión</th>
                        </template>

                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data" :ref="`list_${indextr}`" v-on:click=submit(tr)>

                                <vs-td2>
                                    {{ tr.TipoAdmision }}
                                </vs-td2>

                                <vs-td2>
                                    {{ tr.SerieAdmision }}
                                </vs-td2>

                                <vs-td2>
                                    {{ tr.CodigoAdmision }}
                                </vs-td2>

                                <vs-td2>
                                    {{ tr.NombrePaciente }}
                                </vs-td2>

                                <vs-td2>
                                    {{  FormatDate(tr.IngresoPaciente) }}
                                </vs-td2>

                                <vs-td2>
                                    {{ ObtenerHora(tr.IngresoPaciente) }}
                                </vs-td2>

                                <vs-td2>
                                    {{ elapsedTime(tr.IngresoPaciente) }}
                                </vs-td2>

                            </tr>
                        </template>
                    </vs-table2>
                </div>

            </div>

        </div>


    </vx-card>
</template>



<script>

import BaseRadioButtonGroup from "@/components/sermesa/global/radioButtons/BaseRadioButtonGroup";
import { mapFields } from 'vuex-map-fields';


export default {
    name:'ConsultaAdmisionesEstado',
    props:{
        TipoAdmisionCompletar:{
            default:'Externa',
            type:String
        },
        opcionesAdmision:{
            Type: Array,
            default:[]
        }
    },
    components:{
        BaseRadioButtonGroup
    },
    data: () => ({
        lista_admisiones: '',
        selTipoAdmision: '',
        setTipoAdmision:'',
         radioOptions:[]
    }),
    mounted(){      
        this.radioOptions = Array.apply(null, this.opcionesAdmision)

        if (this.opcionesAdmision.length > 0) {
            if(this.TipoAdmisionCompletar){
                this.selTipoAdmision = this.TipoAdmisionCompletar
            }
               
            if (this.opcionesAdmision.length > 0) {
                    let valorPredefinido = this.radioOptions.find(option => option.Descripcion === this.selTipoAdmision)
                    if (valorPredefinido) {
                        this.selTipoAdmision = valorPredefinido;
                    }
                }
             
            if(this.selTipoAdmision)    
               this.ConsultarAdmisionesEstado().Consultar()

        }
}   ,
    computed: {
        ...mapFields('admision',["AdmisionData"]),
       
    },
    watch:{
        selTipoAdmision(){
            this.ConsultarAdmisionesEstado().Consultar()
        }

    },
    methods: {
        ConsultarAdmisionesEstado() {            
            return {                
                Consultar: async () => {
                    const resp = await this.axios.post('/app/v1_admision/ConsultarAdmisionesEstado', {
                        Empresa: '',
                        EmpresaReal: '',
                        Hospital: '',
                        TipoAdmision: this.selTipoAdmision.Descripcion
                    })                    
                    this.lista_admisiones = resp.data.json.map(m => {
                        return {
                            ...m,
                        }
                    })                    
                },
            }
        },
        submit(item) {

            this.AdmisionData.Codigo = item.CodigoAdmision
            this.AdmisionData.Serie  = item.SerieAdmision

            delete item.TipoAdmision
            delete item.Serie
            delete item.NombrePaciente 
            delete item.IngresoPaciente
            delete item.EstadoProceso
            delete item.CodigoAdmision

            this.$store.dispatch('paciente/setPaciente', item)
            this.$emit("ready", true);
        },
        FormatDate(date){
        
            // Crear el objeto Date con los componentes individuales (nota: los meses son 0-indexados en JavaScript)
            const fechaConHora = this.stringToDate(date);

            // Obtener solo la fecha sin la hora
            var fechaSinHora = new Date(fechaConHora.getFullYear(), fechaConHora.getMonth(), fechaConHora.getDate());

            var dia = fechaSinHora.getDate();
            var mes = fechaSinHora.getMonth() + 1; // Los meses comienzan desde 0
            var año = fechaSinHora.getFullYear();

            // Ajustar el formato DD/MM/YYYY
            var fechaFormateada = (dia < 10 ? '0' : '') + dia + '/' + (mes < 10 ? '0' : '') + mes + '/' + año;

            return fechaFormateada
        },
        ObtenerHora(fechaConHoraString) {

            // Convertir la cadena a un objeto Date
            var fechaConHora = this.stringToDate(fechaConHoraString);

            // Obtener los componentes de la hora
            var hora = fechaConHora.getHours();
            var minutos = fechaConHora.getMinutes();

            // Ajustar el formato HH:MM
            var horaFormateada = (hora < 10 ? '0' : '') + hora + ':' + (minutos < 10 ? '0' : '') + minutos;

            return horaFormateada;
        },
        elapsedTime(stringDate) {

            const fechaIngreso = this.stringToDate(stringDate);
            const dateNow= new Date()
            const startDate = new Date(fechaIngreso).getTime()
            const ahora = dateNow.getTime()

            
            // get total seconds between the times            
            var delta = Math.abs(ahora - startDate) / 1000;


            // calculate (and subtract) whole days
            var days = Math.floor(delta / 86400);
            delta -= days * 86400;

            // calculate (and subtract) whole hours
            var hours = Math.floor(delta / 3600) % 24;
            delta -= hours * 3600;

            // calculate (and subtract) whole minutes
            var minutes = Math.floor(delta / 60) % 60;
            delta -= minutes * 60;

            // what's left is seconds
            //var seconds = delta % 60;  // in theory the modulus is not required


           // Construir la cadena de tiempo transcurrido
            let elapsedTimeString = '';

            if (days > 0) {
                elapsedTimeString += days + ' Días, ';
            }
            if (hours > 0 || days > 0) {
                elapsedTimeString += hours + ' Horas, ';
            }
            if (minutes > 0 || hours > 0 || days > 0) {
                elapsedTimeString += minutes + ' Minutos';
            }
            else if(minutes === 0 && hours === 0 && days === 0)
            {
                elapsedTimeString = 'Ahora Mismo'
            }

            return elapsedTimeString
        },
        stringToDate(strDate){
            const [datePart, timePart] = strDate.split(' ');

            // Dividir la parte de la fecha y la hora en sus componentes
            const [day, month, year] = datePart.split('/');
            const [hours, minutes, seconds] = timePart.split(':');

            // Crear el objeto Date con los componentes individuales (nota: los meses son 0-indexados en JavaScript)
            const fechaConHora = new Date(year, month - 1, day, hours, minutes, seconds);
            return fechaConHora;
        }
    }
}   
    


</script>


<style scoped>
* {
    box-sizing: border-box;
    padding: 2px;
    margin: 0;
}

.admmisionesEstado {
    display: grid;
    grid-gap: 10px;
    width: 100%;
    height: 100%;
    grid-template-areas: "seleccionAdmision"
        "listaAdmisiones";
    grid-template-columns: 100%;
    grid-template-rows: 15% 85%;
}

.admmisionesEstado>div {
    border: 1px solid #888;

}

.seleccionAdmision {
    grid-area: seleccionAdmision;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    padding-left: 1rem;
    padding-right: 1rem;
    border-radius: 5px;
    margin: 5px;
}

.listaAdmisiones {
    grid-area: listaAdmisiones;
    border-radius: 5px;
}

label {
    margin-right: 40px;
    margin-left: 40px;
    vertical-align: middle;

}

input[type="radio"] {
    margin-right: 10px;
    margin-left: 5px;
}

.radio-button-group {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}


</style>


