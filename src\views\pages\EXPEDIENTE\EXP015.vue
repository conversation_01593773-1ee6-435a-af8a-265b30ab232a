<template>
    <Stepper id="pasos-egreso" v-if="true" :steps="steps" :onNext="nextClicked" :onBack="backClicked" @onSelectedStep="SelectedStep" :showActions="true" ref="pasosEgreso" previousStepLabel="Anterior" nextStepLabel="Siguiente">
        <div slot="PlanEgreso">
            <PlanEgreso v-bind="this.$props" @onSaved="Next"/>
        </div>
        <div slot="PreEgreso" >
            <Menu v-bind="this.$props" :menu="menuActual" @onSelectedItem="OpcionSeleccionada" style="padding-top: 50px;"/>
        </div>
        <div slot="EgresoEnfermaria">
            <BitacoraEnfermeria v-bind="this.$props" @onSaved=" (data) => {if (data.FechaConfirma !== '' || data.Minutos >= 15 ) Next()}"/>
        </div>
        <div slot="EgresoSistema">
            <EgresoSistema v-bind="this.$props" @onPrerequisiteNeed="()=> SelectedStep(2)" @onSaved="()=> $emit('onSaved','EgresoSistema')"/>
        </div>
    </Stepper>
</template>
<script>
import EXPBase from './EXPBase.vue'
import { confirm } from 'devextreme/ui/dialog'

export default {
    name: 'EgresoPaciente',
    extends: EXPBase,
    props: {
        FechaEgreso: null
    },
    components:{
        PlanEgreso: ()=> import("./EXP012.vue"),
        BitacoraEnfermeria: ()=> import("./EXP013.vue"),
        EgresoSistema: ()=> import("./EXP014.vue"),
        Stepper: () => import("../../../components/sermesa/global/VueGoodWizzard.vue"),
        Menu: ()=>import("./ExpedienteMenu.vue"),
    },
    methods:{
        ValidarEnfermeria(data){
            if(data.Minutos >= 15)
                this.HabilitarEgresoSistema = true
        },        
        SelectedStep(index){
            this.$refs.pasosEgreso.goTo(index)
        },
        Next(){
            setTimeout( ()=> this.$refs.pasosEgreso.goNext() , 1750)
        },
        nextClicked() {
            return true
        },
        backClicked() {
            return true //no hay restricción para regresar
        },
        OpcionSeleccionada(data){
            this.$nextTick(function() {
                if(data.id === 1){
                let result = confirm("<i>¿Desea registrar el pre egreso del paciente?</i> <p></p>", "Pre egreso médico");
                    result.then((dialogResult) => {
                        if(dialogResult){
                            this.RegistrarPreEgresoMedico()
                        } 
                    });
                }
                if(data.id === 2){
                    let result = confirm("<i>¿Desea registrar la cirugía?</i> <p></p>", "Registrar cirugía");
                        result.then((dialogResult) => {
                            if(dialogResult){
                                this.RegistrarCirugia()
                            } 
                        });
                }
            })
        },
        RegistrarCirugia(){
            this.axios.post("/app/v1_ExpedienteEvolucion/InsertarNotificacionCirugia", 
                {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision
                },
                {
                    headers: {'X-Requested-With': 'XMLHttpRequest'},
                }
            )
            .then(resp => {
                if (resp.data.codigo == 0 ) {
                    this.emit('onCirugiaSaved')
                }
            })    
        },
        RegistrarPreEgresoMedico(){
            this.axios.post("/app/v1_ExpedienteEvolucion/InsertarPreegresoMedico", 
                {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision
                }
            )
            .then(resp => {
                if (resp.data.codigo == 0 ) {
                    this.emit('onPreegresoSaved')
                }
            })    
        }
    },
    data() {
        return {
            steps: [
                {
                    label: 'Plan de egreso',
                    slot: 'PlanEgreso',
                },
                {
                    label: 'Pre-egreso médico',
                    slot: 'PreEgreso',
                },
                {
                    label: 'Checklist enfermería',
                    slot: 'EgresoEnfermaria',
                },
                {
                    label: 'Egreso sistema',
                    slot: 'EgresoSistema',
                },
            ],
            menuActual:[{
                id: 1,
                title: "Pre-egreso médico",
                text: "Pre-egreso médico",
                icon: "address-card",
                path: "",
                animation: ""
            },{
                id: 2,
                title: "Antecedentes personales",
                text: "Registrar cirugía",
                icon: "address-card",
                path: "",
                animation: ""
            }],
            HabilitarEgresoSistema:false,
        }
    }

} 
</script>
<style>
/**Para esta ocaciín no se quiere mostrar el botón de finalizar */
#pasos-egreso .wizard__body__actions a.final-step {
    display: none !important;
}
</style>