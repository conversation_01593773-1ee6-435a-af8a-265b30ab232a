<!-- =========================================================================================
  File Name: TheNavbar.vue
  Description: Navbar component
  Component Name: TheNavbar
  ----------------------------------------------------------------------------------------
  Item Name: Vuexy - Vuejs, HTML & Laravel Admin Dashboard Template
  Author: Pixinvent
  Author URL: http://www.themeforest.net/user/pixinvent
========================================================================================== -->

<template>
<div class="relative">
    <div class="search" v-if="ayuda">
        <div class="input mt-5">
            <!-- Buscador -->
            <vs-input ref="searchInput" style="width:550px" v-model.trim="search" @keydown="Movimiento"></vs-input>
        </div>
        <div class="menus">
            <div v-if="store.length>0" class="p-2">
                <small>Favoritos: {{store.length}} / 9</small>
                <div v-for="(s,key) in store" :key="key" class="favoritos">
                    <router-link :to="s.url" @click.native="ayuda = false">
                        <b>{{key + 1}}.</b> {{s.padre}} > {{s.nombre}}
                    </router-link>
                </div>
            </div>
            <div v-else style="text-align:center" class="p-5">
                Sin Favoritos
            </div>
            <hr>
            <div v-for="(item,key) in searchFilter" :key="key" class="mt-5">
                <fav-nav-search-item :item="item" :favoritos="store" @onItemClick="ayuda = false" @onGuardarLocal="(e)=> {ObtenerLocal(); focusInput()}" ref="menuitem"/>
            </div>
        </div>
    </div>

    <div class="vx-navbar-wrapper" style="height:65px;border-bottom:1px solid rgba(var(--vs-primary), 1)" :class="classObj">

        <vs-navbar class="vx-navbar navbar-custom navbar-skelton" :color="navbarColorLocal" :class="textColor">

            <!-- SM - OPEN SIDEBAR BUTTON -->
            <!-- <feather-icon class="sm:inline-flex xl:hidden cursor-pointer p-2" icon="MenuIcon" @click.stop="showSidebar" /> -->

            <!-- <bookmarks :navbarColor="navbarColor" v-if="windowWidth >= 992" /> -->

            <BarraSoporte />
            <Paqueteria />            
            <vs-spacer />

            <!-- <search-bar /> -->

            <!-- <notification-drop-down /> -->

            <!-- <privilege-drop-down /> -->
 
            <profile-drop-down ref="dropdown"/>
            

        </vs-navbar>

        <!-- <div class="tabs">
            <router-link v-for="(item,key) in tabs" :key="key" :to="item.path">
                <div class="tab" :class="[(rutaActual==item.path)?'activo':'']">
                    <div class="texto">
                        <div class="key">
                            {{key+1}}
                        </div>
                        {{item.name}}
                    </div>
                </div>
            </router-link>

        </div> -->
    </div>
</div>
</template>

<script>
import ProfileDropDown from "./components/ProfileDropDown.vue"
import FavNavSearchItem from "./FavNavSearchItem.vue"
export default {
    name: "the-navbar-vertical",
    data() {
        return {
            ayuda: false, //indica la visibilidad del modal de ayuda
            search: '',//texto de busqueda de funcionalidades u opciones de menú
            searchIndex: -1, //el elemento seleccionado al navegar por la lista 
            store: []//Los favoritos añadidos por el usuario
        }
    },
    props: {
        navbarColor: {
            type: String,
            default: "#fff",
        },
        menus: []//estructura de menu (jerarquica)
    },
    components: {
        // Bookmarks,
        // SearchBar,
        // NotificationDropDown,
        // PrivilegeDropDown,
        ProfileDropDown,
        BarraSoporte: () => import('@/views/pages/TI/TI001.vue'),
        Paqueteria: () => import('@/views/pages/AJENOS/AJE033.vue'),        
        FavNavSearchItem,
        
    },
    watch: {
        search(value) {
            if (this.searchIndex >= 0) {
                this.searchIndex = -1
            }

            if (value > 0 && this.store[value - 1]) {
                const s = this.store[value - 1]
                if(this.$router.currentRoute.path != s.url)
                    this.$router.push(s.url)
                this.search = ''
                this.ayuda = false
            }
        },
        ayuda(newval) {
            if (newval) {
                setTimeout(() => {
                    this.focusInput()
                }, 100)
            }
        }
    },
    computed: {
        sesion() {
            return this.$store.state.sesion
        },
        navbarColorLocal() {
            return this.$store.state.theme === "dark" && this.navbarColor === "#fff" ? "#10163a" : this.navbarColor
        },
        verticalNavMenuWidth() {
            return this.$store.state.verticalNavMenuWidth
        },
        textColor() {
            return {
                'text-white': (this.navbarColor != '#10163a' && this.$store.state.theme === 'dark') || (this.navbarColor != '#fff' && this.$store.state.theme !== 'dark')
            }
        },
        windowWidth() {
            return this.$store.state.windowWidth
        },

        tabs() {
            return this.$store.state.tabs
        },

        rutaActual() {
            return this.$store.state.tabPath
        },

        // NAVBAR STYLE
        classObj() {
            if (this.verticalNavMenuWidth == "default") return "navbar-default"
            else if (this.verticalNavMenuWidth == "reduced") return "navbar-reduced"
            else if (this.verticalNavMenuWidth) return "navbar-full"
            return false
        },

        searchFilter() {
            if (this.search != '') {
                const menuTree = 
                this.$filter_nodes({name:'root', submenu: this.menus}, 'submenu', (currentNode) => currentNode.name.toUpperCase().indexOf(this.search.toUpperCase())>=0)                
                
                if(menuTree)
                    return menuTree.submenu
                else
                    return []

            } else {
                return this.menus
            }
        }
    },
    methods: {
        focusInput() {
            this.$refs.searchInput.$el.children[0].children[0].focus()
        },

        showSidebar() {
            this.$store.commit('TOGGLE_IS_VERTICAL_NAV_MENU_ACTIVE', true);
        },

        ObtenerLocal() {
            let menuFav = localStorage.getItem('menuFav')
            if (menuFav) {
                menuFav = JSON.parse(menuFav)
                this.store = menuFav
            } else {
                return null
            }
        },

        Movimiento(ev) {
            if (ev.key == 'ArrowDown' || ev.key == 'ArrowUp') {
                let t = document.querySelectorAll('.selectorClass')//potente herramienta, un array de NodeList con los elementos que son la funcionalidades

                if (ev.key == 'ArrowDown' && this.searchIndex < t.length - 1) {
                    if(this.searchIndex>=0)
                        t[this.searchIndex].classList.remove('Seleccion')
                    this.searchIndex++
                }
                    
                if (ev.key == 'ArrowUp' && this.searchIndex > 0) {
                    if(this.searchIndex <= t.length - 1)
                        t[this.searchIndex].classList.remove('Seleccion')
                    this.searchIndex--
                }
                if(t.length!=0){
                    t[this.searchIndex].classList.add('Seleccion')
                    t[this.searchIndex].scrollIntoView({  block: "center", behavior: "auto" } )
                }
            }

            // Enter
            if(ev.key == 'Enter' && this.searchIndex>-1){ 
                const ruta = document.querySelectorAll('.selectorClass')
                const url = ruta[this.searchIndex].children[1].getAttribute('href')
                if(this.$router.currentRoute.path != url)
                    this.$router.push(url)
                this.ayuda = false
                this.search = ''
            }

        }
    },
    mounted() {
        this.ObtenerLocal()
        this.$refs.dropdown.Consulta().ConsultaCorporativo();    
        document.addEventListener("keydown", (e) => {
            if (e.key == 'Escape' && this.ayuda) this.ayuda = false
            if (e.key == 'F1') {
                if (this.sesion.sesion_validar) this.ayuda = !this.ayuda
                
                this.searchIndex = -1
                this.search = ''

                e.preventDefault();
            }
            if (e.key >= 1 && e.key < 9 && e.ctrlKey == true) {
                e.preventDefault();

                if (this.tabs[parseInt(e.key) - 1]) {
                    const path = this.tabs[parseInt(e.key) - 1].path
                    if (this.rutaActual != path) this.$router.replace(path)
                }
            }
        })
    },
}
</script>

<style scoped>
.search {
    background-color: rgba(0, 0, 0, 0.2);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1003;
    display: flex;
    justify-content: center;
    backdrop-filter: blur(5px);

}

.search .input {
    background-color: white;
    position: absolute;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 1;

}

.search .menus {
    background-color: white;
    position: absolute;
    width: 570px;
    top: 70px;
    padding: 15px;
    max-height: calc(100% - 150px);
    border-radius: 0 0 15px 15px;
    overflow: auto;
}


.search .favoritos {
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    padding: 1px;
}


.search .Seleccion {
    background-color: #ccc
}

.tabs {
    background-color: rgba(255, 255, 255, 0.1);
    width: 100%;
    height: 25px;
    z-index: 1;
    top: 65px;
    padding-left: 10px;
}

.tabs .tab {
    display: inline-block;
    max-width: 150px;
    margin-right: 4px;
    font-size: 12px;
    padding: 2px 14px;
    cursor: pointer;
    position: relative;
    background-color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    border-radius: 0 0 15px 15px;
}

.tab .texto {
    max-width: 150px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.tabs .tab.activo {
    font-weight: bold;
}

.tabs .tab .key {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.4);
    color: white;
    border-radius: 0 0 50% 50%;
    height: 16px;
    width: 16px;
    font-size: 9.2px;
    text-align: center;
    left: -1px;
    top: -1px;
    border: 1px solid white
}
</style>
