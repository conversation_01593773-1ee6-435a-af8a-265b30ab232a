<template>
    <div class="tempo-container">
        <div :class="'time-digits '+calcClass">
            {{ addLeft<PERSON>ero(choras) }}:{{ addLeft<PERSON>ero(cminutos)  }}:{{ addLeftZero(csegundos) }}
            <!-- <vs-button v-show="showMute" radius icon-pack="fas" :icon="mute?'fa-volume-up':'fa-volume-mute'"  :color="mute?'success':'danger'" type="flat" @click="mute=!mute"></vs-button> -->
        </div>
        <div class="tempo-actions">
            <i v-if="showMute && sonido" :style="mute?'':'color:#ea5455'" :class="mute?'fas fa-volume-up':'fas fa-volume-mute'" @click="mute=!mute"></i>
        </div>
    </div>
</template>
<script>

import beep from '../funciones/Beep.js'
export default {
    name:'temporizador',
    props: {
        horas: { type: Number, default: 0 },
        minutos: { type: Number, default: 0 },
        segundos: { type: Number, default: 0 },
        sonido: { type:Boolean, default:false },
        volumen: { type: Number, default: 50}
    },
    data() {
        return{
            totalTime: 0,
            intervalCountDownId: 0,
            intervalAllertId: 0,
            intervalAllertToogleId: 0,
            beep,
            allertBoolToogle:false,
            mute: false,
            showMute: false,
        }
    }, 
    computed: {
        choras() {
            return Math.trunc( this.totalTime / 3600)
        },
        cminutos(){
            return ( (this.totalTime - this.csegundos ) /60 ) % 60
        },
        csegundos(){
            return this.totalTime % 60
        },
        calcClass(){
            if(this.totalTime <= 0)
                return this.allertBoolToogle? 'change-allert end-time-1': 'change-allert end-time-2'
            else
                return 'running-time'
        },
    },
    mounted(){
        this.totalTime = this.horas * 3600 + this.minutos * 60 + this.segundos
        if (this.totalTime <= 0 ){
            this.totalTime = 0
            this.timeUp()
        }
        else  
            this.intervalCountDownId = setInterval(() => {
                this.totalTime --
                if(this.totalTime <= 0 ){
                    clearInterval(this.intervalCountDownId)
                    this.timeUp()
                }
            }, 1000)

    },
    methods:{
        start(){

        },
        stop(){

        },
        toogleAllert(){
            this.allertBoolToogle = !this.allertBoolToogle
            if(!this.mute && this.sonido){
                const freq = 825
                beep(50, freq, this.volumen)
                setTimeout(() => { beep(50, freq, this.volumen) }, 125)
                setTimeout(() => { beep(50, freq, this.volumen) }, 250)
                setTimeout(() => { beep(50, freq, this.volumen) }, 375) 
            }
            
        }, 
        addLeftZero(numberval){
            if(numberval < 10)
                return '0'+numberval
            return numberval
        },
        timeUp(){
            if(this.totalTime <= 0 ){
                this.showMute = true
                this.$emit('onTimeUp')
                this.intervalAllertId = setInterval(() => {
                    this.toogleAllert()
                }, 1000);
            }
        }
    },
    beforeDestroy() {
        clearInterval(this.intervalCountDownId)
        clearInterval(this.intervalAllertId)
        clearInterval(this.intervalAllertToogleId)
    },

}
</script>
<style>
.time-digits{
    height: 30px;
    width: 90px;
}
.running-time{
    font-size: 18px;
}
.end-time-1{
    color:red;
    font-size: 18px;
    font-weight: bold;
}
.end-time-2{
    color:rgb(255, 153, 0);
    font-size: 22px;
    font-weight: bolder;
}
.change-allert{
    /* transition-property: color font-size; */
    transition-timing-function: ease-in-out;
    transition-duration: 1s;
}
.tempo-container{
    display: flex;
    flex-direction: row;
}
.tempo-actions {
    padding-left: 10px;
    padding-top: 4px;
}
</style>