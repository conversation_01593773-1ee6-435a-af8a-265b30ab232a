/*USUARIO: USRHISSEMAFOROEGRESOS

Para crear los usuarios de login debe ir sin incriptación
DESA -> U$H1$287x2s3Gr$$
QA   -> U$H1$S3MEGR3A$()S
PROD -> U$R33MFREG0(S269.02

Para insertar en seguridadconexiones 
DESA -> VSRIMSQyODd4MnMzR3IkJA==
QA   -> VSRIMSRTM01FR1IzQSQoKVM=
PROD -> VSRSMzNNRlJFRzAoUzI2OS4wMg==
*/
--Eliminar usuarios
--DROP LOGIN USRHISSEMAFOROEGRESOS;  
--DROP USER  USRHISSEMAFOROEGRESOS;


USE MASTER;
IF (SELECT SUSER_ID('USRHISSEMAFOROEGRESOS')) IS NULL
--/*DESA -> */ CREATE LOGIN [USRHISSEMAFOROEGRESOS] WITH PASSWORD=N'U$H1$287x2s3Gr$$', DEFAULT_DATABASE=[HOSPITAL], DEFAULT_LANGUAGE=[us_english], CHECK_EXPIRATION=OFF, CHECK_POLICY=OFF
/*QA   -> */ CREATE LOGIN [USRHISSEMAFOROEGRESOS] WITH PASSWORD=N'U$H1$S3MEGR3A$()S', DEFAULT_DATABASE=[HOSPITAL], DEFAULT_LANGUAGE=[us_english], CHECK_EXPIRATION=OFF, CHECK_POLICY=OFF
--/*PROD -> */ CREATE LOGIN [USRHISSEMAFOROEGRESOS] WITH PASSWORD=N'U$R33MFREG0(S269.02', DEFAULT_DATABASE=[HOSPITAL], DEFAULT_LANGUAGE=[us_english], CHECK_EXPIRATION=OFF, CHECK_POLICY=OFF


-->> Insertar en la tabla seguridadconexiones

begin tran;

INSERT INTO CONTADB..seguridadconexiones (TagConexion,TipoConexion,Usuario,UserPassword,Servidor,Puerto,BaseDefault,FechaIngreso) VALUES
	-- ('USR_HIS_SEMAFORO_EGRESOS_DESA','SQLSERVER','USRHISSEMAFOROEGRESOS','VSRIMSQyODd4MnMzR3IkJA=='     ,'***************','1433','HOSPITAL','2023-01-12 00:00:49.923'),
	 ('USR_HIS_SEMAFORO_EGRESOS_QA'  ,'SQLSERVER','USRHISSEMAFOROEGRESOS','VSRIMSRTM01FR1IzQSQoKVM='   ,'***************','1433','HOSPITAL','2023-01-12 00:00:49.930'),
	-- ('USR_HIS_SEMAFORO_EGRESOS_PROD','SQLSERVER','USRHISSEMAFOROEGRESOS','VSRSMzNNRlJFRzAoUzI2OS4wMg=='	,'************','1433'   ,'HOSPITAL','2023-01-12 00:00:49.930');
	 

--	 rollback;
COMMIT;

USE [HOSPITAL]
GO

IF (SELECT USER_ID('USRHISSEMAFOROEGRESOS')) IS NULL
	CREATE USER [USRHISSEMAFOROEGRESOS] FOR LOGIN [USRHISSEMAFOROEGRESOS] WITH DEFAULT_SCHEMA=[dbo]
GO
-->>

USE [CONTADB]
GO

IF (SELECT USER_ID('USRHISSEMAFOROEGRESOS')) IS NULL
	CREATE USER [USRHISSEMAFOROEGRESOS] FOR LOGIN [USRHISSEMAFOROEGRESOS] WITH DEFAULT_SCHEMA=[dbo]
GO
-->>
USE [PLANESMEDICOS]
GO

IF (SELECT USER_ID('USRHISSEMAFOROEGRESOS')) IS NULL
	CREATE USER [USRHISSEMAFOROEGRESOS] FOR LOGIN [USRHISSEMAFOROEGRESOS] WITH DEFAULT_SCHEMA=[dbo]
GO
-->>
USE [NOMINADB]
GO

IF (SELECT USER_ID('USRHISSEMAFOROEGRESOS')) IS NULL
	CREATE USER [USRHISSEMAFOROEGRESOS] FOR LOGIN [USRHISSEMAFOROEGRESOS] WITH DEFAULT_SCHEMA=[dbo]
GO


-->>Alter table
Use Hospital GO;
ALTER TABLE DescuentoBeneficio
ADD Corporativo int
ALTER TABLE DescuentoBeneficio
ADD Equipo varchar(15)

ALTER TABLE descuentosaplicados
ADD Corporativo int
--> fin alter table

-->>Inicia la creación de procedimientos almacenados

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[spHisSemaforoEgresosCatalogos]    Script Date: 29/10/2024 10:18 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[spHisSemaforoEgresosCatalogos] 
 @iCatalago AS varchar(50) 
-- ======================================================================================================
-- Creado por:			Fabrizio Martínez
-- Fecha de creación:	06-08-2024
-- Producto:			RTC-00000  
-- Description:			Procedimiento para mostrar los diferentes catalogos utilizados en semaforo egresos--

-- ========================================================================================================
AS
BEGIN
  IF @iCatalago = 'Descuentos'
  BEGIN

  select idDescuento,Empresa,Descripcion,descuento,Fecha,Observacion 
		From hospital..Tipodescuento where activo='S'

  End
 IF @iCatalago='Areas'
 Begin
 SELECT 
    ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) AS IdServicio,
    Servicio
FROM (VALUES
    ('Emergencia'),
    ('Diagnostico'),
    ('Hospitalización-Emergencia'),
    ('Hospitalización')
    
) AS TempTable (Servicio);
 End



END


GO;

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[spSemaforoEgresosValidarCuentaCalculada]    Script Date: 29/10/2024 10:35 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE procedure [dbo].[spSemaforoEgresosValidarCuentaCalculada]
@iserieAdmision as varchar(2),
@iAdmision as int
/*-------------------------------------------------------------
CREADO POR: Fabrizio Martínez  Numero de Rational:						
FECHA CREACIÓN: 29/10/2024												
PRODUCTO:        						
----------------- DESCRIPCION---------------------------------
mostrar el estado de una admisión para ver si tiene beneficios y si esta ya fue calculada o no	 

*/
as Begin
SELECT isnull(ctacalculada,0)ctaCalculada,Isnull(IdTipoDescuento,-1) as idTipoDescuento,ROUND(BeneficioTerceros,2) as BeneficioTerceros,
TipoDescuento=(select Descripcion 
		From hospital..Tipodescuento where  idDescuento=A.IdTipoDescuento)
  FROM HOSPITAL.dbo.Admisiones A (nolock) 
  Where serie=@iserieAdmision and codigo=@iadmision

  End
  GO;

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[spHisSemaforoEgresosDescuentos]    Script Date: 29/10/2024 10:40 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE procedure [dbo].[spHisSemaforoEgresosDescuentos]
        @iempresaU          AS VARCHAR(3),--empresa unificada
        @iEmpresaSucursal   CHAR(3),--Empresa de la sucursal
        @iAdmision          INT ,--Numero de admisión
        @iSerieAdmision     VARCHAR(2),--serie de admision
        @iCopago            MONEY,--copago 
        @iCoaseguro         MONEY ,--coaseguro
        @iDeducible         MONEY,--Deducible
        @iGastosNoCubiertos MONEY,--gastos especiales no cubiertos
        @iAutorizacion      INT,--Numero de autorización texto libre
        @iCorporativo       AS INT,--Nueva variable
        @iequipo            VARCHAR(15),--dirección ip del equipo que procesa..
        @iIdDescuento       INT =NULL,--CODIGO del tipo de descuento 
        @iOpcion            AS VARCHAR(1)-- opción a procesar --I insertar --B Desactivar descuento 
/*

CREADO POR: Fabrizio Martínez        
Numero de Rational  RTC-0408218_migración_semaforo_egresos
FECHA CREACIÓN: 09/09/2024
/**************************   DESCRIPCIÓN    ******************************/ 
Calculo y eliminacion de descuentos en servicio de semaforo egresos

*/

As Begin

DECLARE @wIdTipoDescuento     INT ,--correlativo de la tabla descuento beneficio
        @wSumCopagoCoa        AS MONEY,
        @wTotalCuenta         AS MONEY,
        @wCuentaDescA         AS INT,
        @wrazon               AS VARCHAR(254),
        @wFacturaPaso2        AS INT,
        @wCodRespuesta        AS INT,
        @wRespuesta           AS VARCHAR(max),
        @wDescuento           AS MONEY,
        @wValidarDescuento    AS INT,
        @wPorcentajeDescuento INT,--porcentaje descuento
        @wTipoAdmision        AS VARCHAR(30) --tipo de admisión para calculo de porcentaje

--validar que existan cargos para continuar
SELECT @wTotalCuenta = Isnull(Sum(Isnull(valor, 0)), 0)
FROM   hospital..cargos AS CO (nolock)
WHERE  co.empresa = @iempresaU
       AND co.admision = @iAdmision
       AND co.serieadmision = @iSerieAdmision
            --Mostrar el monto del descuento
            SELECT @wdescuento = Round(beneficioterceros, 2)
            FROM   hospital..admisiones
            WHERE  seguro IS NOT NULL
                   AND serie = @iSerieAdmision
                   AND codigo = @iAdmision;

    
IF ( Isnull(@wTotalCuenta, 0) > 0)
  BEGIN
      --Validar que admisión no tenga factura paso 2 antes de realizar el descuento
      SET @wFacturaPaso2 = (SELECT Count(admision) AS Total
                            FROM   facturas
                            WHERE  serieadmision = @iSerieAdmision
                                   AND admision = @iAdmision
                                   AND empresa = @iEmpresaSucursal
                                   AND tipo = 2
                                   AND status = 'P')

      IF @wFacturaPaso2 = 0
        BEGIN
        --si opcion es insertar descuento
        SELECT @wValidarDescuento = Isnull(Count(admision), 0)
        FROM   hospital..descuentobeneficio
        WHERE  serieadmision = @iSerieAdmision
       AND admision = @iAdmision
       AND empresa = @iempresaU
       AND status = 'S'
        IF @iOpcion='I' And @wValidarDescuento=0
        Begin
            SELECT @wTipoAdmision = ( CASE
                                        WHEN adm.serie IN( Emp.internos )AND adm.interno = 'S' 
                                            THEN 'INTERNOS'
                                        WHEN adm.serie IN( emp.urgencias )AND adm.interno <> 'S' 
                                            THEN'URGENCIAS'
                                        WHEN adm.serie IN( emp.externos )AND adm.interno <> 'S' 
                                            THEN 'EXTERNOS'
                                        WHEN adm.serie IN(emp.igss, emp.igsssesiones, emp.igsshemodialisis)
                                         THEN 'IGSS'
                                        WHEN adm.interno = 'S' THEN 'INTERNOS'
                                      END 
                                      )
            FROM   hospital..admisiones AS adm
                   INNER JOIN nominadb..emphospital AS emp (nolock)
                           ON ( emp.empresa = adm.empresa AND emp.activo = 'S'AND emp.internos IS NOT NULL 
                           AND adm.serie IN(emp.internos, emp.urgencias, emp.externos, emp.igss,
                                            emp.igsssesiones, emp.igsshemodialisis 
                                            ) )
            WHERE  adm.serie = @iSerieAdmision AND adm.codigo = @iAdmision

            SELECT @wPorcentajeDescuento = CASE 
                                                WHEN @wTipoAdmision = 'INTERNOS'
                                                    THEN (SELECT descuentoin
                                                            FROM  hospital..tipodescuento
                                                                 WHERE activo ='S'AND empresa = @iEmpresaU
                                                                    AND iddescuento = @iIdDescuento
                                                 )
                                                 WHEN @wTipoAdmision = 'EXTERNOS'
                                                    THEN (SELECT descuento
                                                            FROM hospital..tipodescuento
                                                                 WHERE activo ='S'AND empresa = @iEmpresaU
                                                                        And iddescuento = @iIdDescuento
                                                           )
                                                 WHEN @wTipoAdmision = 'URGENCIAS'
                                                        THEN (SELECT descuentoem
                                                                FROM  hospital..tipodescuento
                                                               WHERE activo ='S'AND empresa = @iEmpresaU
                                                                        And iddescuento = @iIdDescuento
                                                           )
                                           END

            SET @wSumCopagoCoa = ( @iCopago + @iCoaseguro + @iDeducible+ @iGastosNoCubiertos)

            SELECT @wrazon = observacion
                FROM   hospital.dbo.tipodescuento
                    WHERE  activo = 'S' AND iddescuento = @iIdDescuento

            SELECT @wIdTipoDescuento = Isnull(Max(idtipodescuento), 0)
                    FROM   hospital..descuentobeneficio

            INSERT INTO hospital..descuentobeneficio
                        (idtipodescuento,
                         aplicdescuento,
                         empresa,
                         iddescuento,
                         serieadmision,
                         admision,
                         categoria,
                         autorizacion,
                         codigopaciente,
                         montoinicial,
                         peso,
                         sumacopcoa,
                         montopeso,
                         descuento,
                         corporativo,
                         fecha,
                         status,
                         equipo,
                         usuario)
            SELECT Row_number() OVER (ORDER BY nombre DESC)+ @wIdTipoDescuento AS IdTipoDescuento,
                   Ca.descuentobi AS AplicaDescuento,
                   CO.empresa,
                   @iIdDescuento AS IdDescuento,
                   CO.serieadmision,
                   CO.admision,
                   CO.categoria,
                   @iAutorizacion AS Autorizacion,
  Co.paciente AS CodigoPaciente,
                   Sum(CO.valor)AS MontoInicial,
                   Round((Sum(Cast(CO.valor AS FLOAT)) / @wTotalCuenta ) * 100,2, 0)AS Peso,
                   @wSumCopagoCoa AS SumCopagoCoa,
                   ( ( Sum(CO.valor) / @wTotalCuenta ) * @wSumCopagoCoa )AS MontoPeso,
                   Round(( ( Sum(Cast(CO.valor AS FLOAT)) / @wTotalCuenta ) * @wSumCopagoCoa) * @wPorcentajeDescuento / 100, 2)AS Descuento,
                   @iCorporativo AS corporativo,
                   Getdate()AS fecha,
                   'S' AS Status,
                   @iequipo ASequipo,
                   ''AS Usuario
            FROM   hospital..cargos AS CO (nolock)
                   INNER JOIN hospital..categorias AS CA (nolock) ON ( co.categoria = CA.codigo AND co.empresa = ca.empresa )
            WHERE  co.empresa = ca.empresa 
                   AND co.admision = @iAdmision
                   AND co.serieadmision = @iSerieAdmision
            GROUP  BY Ca.descuentobi,Co.paciente,CO.empresa,CO.serieadmision,CO.admision, CO.categoria,CA.nombre;

            --Actualizar admisiones 
            UPDATE hospital..admisiones
            SET    deducible = @iDeducible,
                   idtipodescuento = @iIdDescuento,
                   beneficioterceros = (SELECT Sum(descuento) AS Descuento
                                        FROM   hospital..descuentobeneficio
                                        WHERE  serieadmision = @iSerieAdmision
                                               AND admision = @iAdmision
                                               AND empresa = @iempresaU
                                               AND aplicdescuento = 'S'
                                               AND status = 'S'
                                        )
            WHERE  seguro IS NOT NULL
                   AND serie = @iSerieAdmision
                   AND codigo = @iAdmision;

            --Actualizar descuentos aplicados 
            SET @wCuentaDescA = (SELECT Count(admision)
                                 FROM   hospital..descuentosaplicados
                                 WHERE  serieadmision = @iSerieAdmision
                                        AND admision = @iAdmision
                                 )
            --Mostrar el monto del descuento
            SELECT @wdescuento = Round(beneficioterceros, 2)
            FROM   hospital..admisiones
            WHERE  seguro IS NOT NULL
                   AND serie = @iSerieAdmision
                   AND codigo = @iAdmision;


            IF @wCuentaDescA = 0
              BEGIN
                  INSERT INTO hospital..descuentosaplicados
                              (empresa,serieadmision,admision,usuario,porcentaje,valornominal,razon,tipo,corporativo)
                  VALUES      (@iempresaU,
                               @iSerieAdmision,
                               @iAdmision,
                               '',
                               0,
                               (SELECT Round(beneficioterceros, 2) AS TotalDescuento
                                FROM   hospital..admisiones
                                WHERE  seguro IS NOT NULL
                                       AND serie = @iSerieAdmision
                                       AND codigo = @iAdmision
                                       AND empresa = @iempresaU),
                               @wrazon,
                               'A',
                               @iCorporativo 
                               )
              END
            ELSE
              BEGIN
                  UPDATE hospital..descuentosaplicados
                  SET    valornominal = (SELECT Round(beneficioterceros, 2) AS
                                                TotalDescuento
                                         FROM   hospital..admisiones
                                         WHERE  seguro IS NOT NULL
                                                AND serie = @iSerieAdmision
                                                AND codigo = @iAdmision
                                                AND empresa = @iempresau
),
                         fecha = Getdate()
                  WHERE  serieadmision = @iSerieAdmision
                         AND admision = @iAdmision
                         AND empresa = @iEmpresaU
              END



            IF @@rowcount > 0
              BEGIN
                  SET @wCodRespuesta = 0
                  SET @wRespuesta = 'Descuento procesado'
              END
            End --fin opcion insertar registro
              Else
              Begin
               if @iOpcion='E' 
                  Begin
                    SELECT @wdescuento = Round(beneficioterceros, 2)
            FROM   hospital..admisiones
            WHERE  seguro IS NOT NULL
                   AND serie = @iSerieAdmision
                   AND codigo = @iAdmision;
               
                    --Actualizar status de descuento beneficio para que no sea tomado 
                     Update hospital..DescuentoBeneficio set Status = 'N' where Admision= @iAdmision and SerieAdmision= @iSerieAdmision and Empresa=@iEmpresaU;
                     --actualizar admisión
                     Update Hospital..Admisiones set IdTipoDescuento = NULL,BeneficioTerceros= 0.00 where seguro is not null and serie= @iSerieAdmision and codigo = @iAdmision and Empresa=@iEmpresaU;
                    --actualizar descuentos aplicados
                    Update hospital..DescuentosAplicados SET ValorNominal = 0.00 where serieAdmision=@iSerieAdmision and Admision=@iAdmision and Empresa=@iEmpresaU;
                    if @@rowcount>0
                    begin
                     SET @wCodRespuesta = 0
                     SET @wRespuesta ='Descuento eliminado exitosamente'
                    End
                 End
				 Else Begin
                 IF @iOpcion='I' And @wValidarDescuento=1 Begin
                 SET @wCodRespuesta = -1
                     SET @wRespuesta ='La admision ingresada ya tiene descuento'
                  End 
                  Else Begin
                  SET @wCodRespuesta = -1
                  SET @wRespuesta ='La opción ingresada no existe'
                  End
				  
				 End --fin opcion
              End
        END
      ELSE
        BEGIN
            SET @wCodRespuesta = -1
            SET @wRespuesta =
            'La admisión seleccionada ya tiene factura paso 2, no se puede continuar'
        END
  END --fin si hay cargos
ELSE
  BEGIN
      IF @wTotalCuenta = 0
        BEGIN
            SET @wCodRespuesta = -1
            SET @wRespuesta = 'la admisión ingresada no tiene cargos, no se puede continuar'
        END

      IF @wValidarDescuento > 0
        BEGIN
            SET @wCodRespuesta = -1
            SET @wRespuesta =
            'La admisión seleccionada ya tiene en proceso un descuento'
        END
  END

SELECT @wCodRespuesta AS codigo,
       @wRespuesta    AS descripcion,
       Isnull(@wdescuento,'')    AS descuento,
       0  AS tipo_error

End
GO;

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[spSemaforoEgresosporcentajedescuento]    Script Date: 29/10/2024 10:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
 CREATE procedure [dbo].[spSemaforoEgresosporcentajedescuento]
  @wTipoAdmision as varchar(20),
  @iIdDescuento as int
as 
begin
 /*-------------------------------------------------------------
	CREADO POR: Fabrizio Martínez	
	Numero de Rational  RTC-0408218_migración_semaforo_egresos
 	FECHA CREACIÓN: 12/09/2023												
	PRODUCTO: visualizar porcentaje descuento       						
	----------------- DESCRIPCION---------------------------------
 	Tiene como objetivo mostrar el porcentaje de descuento de acuerdo al 
    tipo de admision 		
    
  */ 

 select case when @wTipoAdmision='INTERNOS' 
				THEN(Select DescuentoIn 
						From Hospital..TipoDescuento 
							Where Activo='S' 
							And Empresa ='MED'    and idDescuento=@iIdDescuento) 
			when @wTipoAdmision='EXTERNOS' 
				THEN(Select Descuento 
						From Hospital..TipoDescuento 
							Where Activo='S' 
							And Empresa ='MED'    and idDescuento=@iIdDescuento)
			when @wTipoAdmision='URGENCIAS' 
				THEN(Select DescuentoEM 
						From Hospital..TipoDescuento 
							Where Activo='S' 
							And Empresa ='MED'    and idDescuento=@iIdDescuento)
							END/100 as PorcentajeDescuento
End
GO;

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[spHisSemaforoEgresoInsCalculo]    Script Date: 29/10/2024 10:45 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE procedure [dbo].[spHisSemaforoEgresoInsCalculo]
    @iCopago Decimal(10,2),
    @iCoaseguro Decimal(10,2),
    @iDeducible Decimal(10,2),
    @iGastosEspeciales Decimal(10,2),
    @iExtraOrdinarios Decimal(10,2),
    @iCtaAjena Decimal(10,2),
    @iFacturado Decimal(10,2),
    @iDescuento Decimal(10,2),
    @iTotal Decimal(10,2),
    @iUsuario as varchar(15),
    @iNombre as varchar(100),
    @iComentariosSemaforo varchar(250),
    @iSerieAdmision as varchar(2),
    @iAdmision as int,
    @iEmpresaU as varchar(3),
    @iNombreDescuento as varchar(50),
	@iRecalculo as bit=NULL --ingresar 1 si es recalculo
   
  As Begin

/*
CREADO POR: Fabrizio Martínez	
 Numero de Rational:RTC-0408218_migración_semaforo_egresos	
FECHA CREACIÓN: 07/10/2024												
PRODUCTO: Insertar monto a cobrar en semaforo egresos					
**************************** DESCRIPCION ***********************************
	Tiene como objetivo insertar el monto a pagar de una admisión.          	
																			
*/


DECLARE 

@wDescuento as varchar(100)='Descuento '+isnull(rtrim(@iNombreDescuento),''),
@wFecha as datetime=getdate(), 
@wCorrelativo AS int=0,
@wCodRespuesta AS int = 0,
@wRespuesta AS varchar(500),
@wvalidarCuenta as varchar(1),
@wBitacora varchar(max)
--select @wFecha

Set @wBitacora=Concat('-----------Desglose De Cobro--------- ',char(10),
				 'Copago_______________________________:Q',FORMAT(CAST(@iCopago AS decimal(10,2)), 'N', 'es-GT')  ,char(10),
				 'Coaseguro____________________________:Q',FORMAT(CAST(@iCoaseguro AS decimal(10,2)), 'N', 'es-GT') ,char(10),
				 'Deducible____________________________:Q',FORMAT(CAST(@iDeducible AS decimal(10,2)), 'N', 'es-GT') , char(10),
				 'Gastos especiales no cubiertos_______:Q',FORMAT(CAST(@iGastosEspeciales AS decimal(10,2)), 'N', 'es-GT') ,char(10),
				 'Extraordinarios______________________:Q',FORMAT(CAST(@iExtraOrdinarios AS decimal(10,2)), 'N', 'es-GT') ,char(10),
				 'Cuenta ajena no elegible_____________:Q',FORMAT(CAST(@iCtaAjena AS decimal(10,2)), 'N', 'es-GT') ,char(10),
				 'Facturado o cobrado en cuenta________:Q',FORMAT(CAST(@iFacturado AS decimal(10,2)), 'N', 'es-GT') ,char(10),
                 LEFT(@wDescuento + REPLICATE('_', 37), 37),':Q',FORMAT(CAST(@iDescuento AS decimal(10,2)), 'N', 'es-GT') ,char(10),
				 'Total a cobrar_______________________:Q',FORMAT(CAST(@iTotal AS decimal(10,2)), 'N', 'es-GT') ,char(10),   char(10),
				 'Usuario equipo : ',@iUsuario,char(10),
				 'Nombre Operador: ',@iNombre,char(10),
				 'Fecha: ',FORMAT (getdate(), 'dd/MM/yyyy hh:mm:ss tt'),char(10),char(13),@iComentariosSemaforo
                 
                 )
                


declare @wObservacion varchar(max)
select @wvalidarCuenta=CtaCalculada
from Hospital..Admisiones
Where serie=@iSerieAdmision and Codigo=@iAdmision

if Isnull(@wvalidarCuenta,0)=1  AND isnull(@iRecalculo,0)<>1
Begin
SET @wCodRespuesta = -1
 SET @wRespuesta = concat('La Admisión: ',(@iSerieAdmision),'-',@iAdmision,', Ya fue realizado su calculo previamente, debera recalcular')

End
if (@wvalidarCuenta is null or isnull(@iRecalculo,0)=1)
Begin
	Update Hospital..Admisiones Set CopagoAcobrar=@iCopago, CoaseguroACobrar=@iCoaseguro,Deducible=@iDeducible,
                              CtaCalculada=1,FechaCalculo=Getdate(), GastosEspNoCubiertos=@iGastosEspeciales, ComentariosSemaforoE=@iComentariosSemaforo
			 Where serie=@iSerieAdmision and Codigo=@iAdmision
IF @@ROWCOUNT>0 Begin
 SET @wCodRespuesta = 0
 SET @wRespuesta = concat('Datos guardados exitosamente',char(10),'Admisión: ',@iSerieAdmision,'-',@iAdmision)
 SELECT @wObservacion=Observacion
From Hospital..AdmisionesObservaciones
Where empresa=@iEmpresaU and Serie=@iSerieAdmision And CODIGO=@iAdmision

if @wObservacion is NULL
Begin
 Insert into HOSPITAL.dbo.AdmisionesObservaciones
                Values(@iEmpresaU,@iSerieAdmision,@iAdmision,@wBitacora)
End
if @wObservacion IS NOT NULL
Begin
 Update HOSPITAL.dbo.AdmisionesObservaciones
       Set Observacion=@wObservacion+@wBitacora
	Where empresa=@iEmpresaU and Serie=@iSerieAdmision And CODIGO=@iAdmision
End
Insert into HOSPITAL.dbo.AdmisionesObservacion(Empresa,Serie,Admision,Observacion,Usuario)
Values(@iEmpresaU,@iSerieAdmision,@iAdmision,@wBitacora,@iUsuario)



End else 
begin
 SET @wCodRespuesta = -1
      SET @wRespuesta = 'No se pudo registrar la información, causas posibles admisión incorrecta'
End

End--fin @wvalidarCuenta

 IF @wCodRespuesta = 0
    BEGIN
      SELECT
        0 AS codigo,
        @wRespuesta AS descripcion,
        0 AS tipo_error,
        @wCorrelativo AS correlativo;
    END

    IF @wCodRespuesta <> 0
    BEGIN
      SELECT
        @wCodRespuesta AS codigo,
        @wRespuesta AS descripcion,
        -1 AS tipo_error
    END
 

End
GO;

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[spHisSemaforoEgresosObservaciones]    Script Date: 29/10/2024 10:48 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Procedure [dbo].[spHisSemaforoEgresosObservaciones] 
@iEmpresaU as varchar(3),
@iSerieAdmision as varchar(2),
@iAdmision as int
/* 
Author:		Fabrizio Martínez
Fecha Creación: 09-10-2024
Numero de Rational:RTC-0408218_migración_semaforo_egresos	
 Proyecto :   RTC-00000 migración semaforo egresos
 PRODUCTO: obtener las diferentes observaciones ingresadas para la admision					
**************************** DESCRIPCION ***********************************
	Tiene como objetivo se pueda visualizar la bitacora de las observaciones
	de calculo de la admisión 

*/
as Begin
select Observacion,fecharegistro,usuario From Hospital..AdmisionesObservacion 
 Where empresa=@iEmpresaU and Serie=@iSerieAdmision And Admision=@iAdmision 
Order by fecharegistro desc
End
GO;

USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[spHisSemaforoEgresosBusquedaRecalculo]    Script Date: 29/10/2024 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE procedure [dbo].[spHisSemaforoEgresosBusquedaRecalculo]
 @IBusqueda as varchar(100),
 @iEmpresaU as varchar(3)
As Begin
/*
 ======================================================================================================
 Creado por:			Fabrizio Martínez
 Fecha de creación:	10-10-2024
 Producto:			RTC-00000  migración web semaforo
 Description:			Realiza busqueda de paciente o admisión según los criterios enviados

 ========================================================================================================
*/
WHILE CHARINDEX('  ', @Ibusqueda) > 0
BEGIN
    SET @Ibusqueda = REPLACE(@Ibusqueda, '  ', ' ');
END


SET @iBusqueda=REPLACE(REPLACE(REPLACE(LTRIM(RTRIM(@Ibusqueda)), '  ', ' '), ' ', ' '), '-', '')
select a.serie,a.codigo as Admision,a.status as statusadmision,a.fechaegreso,
	Paciente=(Select Rtrim(p.nombre)+' '+Rtrim(p.apellido) from Hospital..Pacientes as P where p.codigo=a.paciente and p.empresa=a.empresa)
,s2.nombre as NombreSeguro,
s1.nombre as NombreAseguradora
,pxc.nombre as Planxcontrato,
			DATEDIFF(dd, a.Entrada, ISNULL(a.FechaEgreso, GETDATE())) as Dias,
     TipoAdmision= CASE
                                        WHEN a.serie IN( Emp.internos )AND a.interno = 'S' 
                                            THEN 'INTERNOS'
                                        WHEN a.serie IN( emp.urgencias )AND a.interno <> 'S' 
                                            THEN'URGENCIAS'
                                        WHEN a.serie IN( emp.externos )AND a.interno <> 'S' 
                                            THEN 'EXTERNOS'
                                        WHEN a.serie IN(emp.igss, emp.igsssesiones, emp.igsshemodialisis)
                                            THEN 'IGSS'
                                        WHEN a.interno = 'S' THEN 'INTERNOS'
                                      END 
				From Hospital..Admisiones A (nolock)
					 Inner Join Hospital..Pacientes as P (nolock) ON(  p.empresa=a.empresa And p.codigo=a.paciente )
						INNER Join Hospital.dbo.seguros as s2 (nolock) ON (s2.Empresa = a.Empresa and s2.Codigo = a.Seguro)
						INNER Join Hospital..Aseguradoras as s1 (nolock) ON(s1.Empresa = s2.Empresa and s1.Asegura = s2.Asegura)
						Inner Join Nominadb.dbo.EmpHospital as emp (nolock)ON (a.empresa=emp.empresa and emp.activo='S' And internos is not null
					AND a.Serie IN (emp.Internos, emp.Urgencias, emp.Externos, emp.Igss, emp.Farmacia, emp.igssSesiones,emp.igsshemodialisis))
					Left Join PlanesMedicos.dbo.PlanesXContrato pxc (nolock) ON (pxc.IdEmpresa = a.Empresa  and a.Seguro=pxc.idpoliza)

			Where a.CtaCalculada=1 AND A.ENTRADA>=DATEADD(Year, -1, GETDATE())
			AND a.empresa =@iEmpresaU

AND concat( A.serie,A.codigo,' ',+RTRIM(P.Nombre),' ',RTRIM(P.apellido)) LIKE '%'+ Replace(replace(@Ibusqueda, ' ', '%'),'-','') +'%'


End

GO;

--> Permisos para tablas diferentes a Hospital

	  Use PlanesMedicos ;

	 GRANT Select ON PlanesXContrato TO USRHISSEMAFOROEGRESOS
     GRANT Select ON CopagosXContrato TO USRHISSEMAFOROEGRESOS
     

   	  Use Nominadb 
	GO
	 GRANT Select ON EmpHospital TO USRHISSEMAFOROEGRESOS 


-->>	<Inician los Permisos>
USE [HOSPITAL]

-->>Inician lospermisos para los procedimientos alamcenados
GRANT EXECUTE ON spHisSemaforoEgresosCatalogos TO USRHISSEMAFOROEGRESOS
GRANT EXECUTE ON Sp_SemaforoEgresos_Listado TO USRHISSEMAFOROEGRESOS
GRANT EXECUTE ON sp_semaforoegresosV2 TO USRHISSEMAFOROEGRESOS -- no se modifica ni se actualiza el procedimiento, se utiliza en delphi
GRANT EXECUTE ON spSemaforoEgresosValidarCuentaCalculada TO USRHISSEMAFOROEGRESOS
GRANT EXECUTE ON spSemaforoEgresosporcentajedescuento TO USRHISSEMAFOROEGRESOS

GRANT EXECUTE ON spHisSemaforoEgresoInsCalculo TO USRHISSEMAFOROEGRESOS
GRANT EXECUTE ON spHisSemaforoEgresosObservaciones TO USRHISSEMAFOROEGRESOS
GRANT EXECUTE ON spHisSemaforoEgresosBusquedaRecalculo TO USRHISSEMAFOROEGRESOS
GRANT EXECUTE ON sp_semaforoegresosRecalculo TO USRHISSEMAFOROEGRESOS -- no se modifica ni se actualiza el procedimiento se utiliza en delphi
GRANT EXECUTE ON [spHisSemaforoEgresosDescuentos] TO USRHISSEMAFOROEGRESO 




GO


--> Inicia modificacion de procedimientos
USE [HOSPITAL]
GO
/****** Object:  StoredProcedure [dbo].[Sp_SemaforoEgresos_Listado]    Script Date: 29/10/2024 10:25 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

ALTER Procedure [dbo].[Sp_SemaforoEgresos_Listado]
@Empresa as varchar(3),
@Hospital as varchar(100),
@Opcion as varchar(100)
As Begin
/*
Fecha modificacion:21-10-2024
Descripcion: se agrega el campo tipo admisión
Autor: Fabrizio Martínez
Proyecto: Migracion web semaforo egresos
*/

--SET @Empresa='MED'
--SET @Hospital='Centro Hospitalario La Paz Zona 14'
--SET @Opcion='Diagnostico'
  BEGIN TRY
Begin Transaction
  DECLARE @ErrRaise varchar(99) = '',
          @ErrRaiseNum int = 0
		  SET @Empresa='MED'

If @Opcion='Emergencia'
Begin

select a.serie,a.codigo as Admision,a.status as statusadmision,a.fechaegreso,
	Paciente=(Select Rtrim(p.nombre)+' '+Rtrim(p.apellido) from Hospital..Pacientes as P where p.codigo=a.paciente and p.empresa=a.empresa)
,s2.nombre as NombreSeguro,
s1.nombre as NombreAseguradora
,
				pxc.nombre as Planxcontrato,
			DATEDIFF(dd, a.Entrada, ISNULL(a.FechaEgreso, GETDATE())) as Dias,
            TipoAdmision= CASE
                                        WHEN a.serie IN( Emp.internos )AND a.interno = 'S' 
                                            THEN 'INTERNOS'
                                        WHEN a.serie IN( emp.urgencias )AND a.interno <> 'S' 
                                            THEN'URGENCIAS'
                                        WHEN a.serie IN( emp.externos )AND a.interno <> 'S' 
                                            THEN 'EXTERNOS'
                                        WHEN a.serie IN(emp.igss, emp.igsssesiones, emp.igsshemodialisis)
                                            THEN 'IGSS'
                                        WHEN a.interno = 'S' THEN 'INTERNOS'
                                      END 
				From Hospital..Admisiones A (nolock)
						INNER Join Hospital.dbo.seguros as s2 (nolock) ON (s2.Empresa = a.Empresa and s2.Codigo = a.Seguro)
						INNER Join Hospital..Aseguradoras as s1 (nolock) ON(s1.Empresa = s2.Empresa and s1.Asegura = s2.Asegura)
						Inner Join Nominadb.dbo.EmpHospital as emp (nolock)ON (a.empresa=emp.empresa and emp.activo='S' And internos is not null 
						AND a.Serie IN (emp.Internos, emp.Urgencias, emp.Externos, emp.Igss, emp.Farmacia, emp.igssSesiones,emp.igsshemodialisis))
						Left Join PlanesMedicos.dbo.PlanesXContrato pxc (nolock) ON (pxc.IdEmpresa = a.Empresa  and a.Seguro=pxc.idpoliza)
					Where a.empresa=@Empresa
					 And a.Fechaegreso is NOT null 
					 And a.CtaCalculada is null
				 And emp.nombre=@Hospital
				 And A.Serie in(emp.Urgencias) and a.interno<>'S'
				  AND EXISTS (SELECT TOP 1 serie From Hospital..Cargos as C where C.empresa=a.empresa And c.serieadmision=a.serie and c.admision=a.codigo)

End


If @Opcion='Diagnostico'
Begin

	Select a.serie,a.codigo as Admision,a.status as statusadmision,a.fechaegreso,
	Paciente=(Select Rtrim(p.nombre)+' '+Rtrim(p.apellido) from Hospital..Pacientes as P where p.codigo=a.paciente and p.empresa=a.empresa)
	,s2.nombre as NombreSeguro, s1.nombre as NombreAseguradora,
	pxc.nombre as Planxcontrato, DATEDIFF(dd, a.Entrada, ISNULL(a.FechaEgreso, GETDATE())) as Dias
	,TipoAdmision= CASE
                                        WHEN a.serie IN( Emp.internos )AND a.interno = 'S' 
                                            THEN 'INTERNOS'
                                        WHEN a.serie IN( emp.urgencias )AND a.interno <> 'S' 
                                            THEN'URGENCIAS'
                                        WHEN a.serie IN( emp.externos )AND a.interno <> 'S' 
                                            THEN 'EXTERNOS'
                                        WHEN a.serie IN(emp.igss, emp.igsssesiones, emp.igsshemodialisis)
                                            THEN 'IGSS'
                                        WHEN a.interno = 'S' THEN 'INTERNOS'
                                      END 
    From Hospital..Admisiones A (nolock)
	INNER Join Hospital.dbo.seguros as s2 (nolock) ON (s2.Empresa = a.Empresa and s2.Codigo = a.Seguro)
	INNER Join Hospital..Aseguradoras as s1 (nolock) ON(s1.Empresa = s2.Empresa and s1.Asegura = s2.Asegura)
	Inner Join Nominadb.dbo.EmpHospital as emp (nolock)ON (a.empresa=emp.empresa and emp.activo='S' And internos is not null 
	AND a.Serie IN (emp.Internos, emp.Urgencias, emp.Externos, emp.Igss, emp.Farmacia, emp.igssSesiones,emp.igsshemodialisis))
	Left Join PlanesMedicos.dbo.PlanesXContrato pxc (nolock) ON (pxc.IdEmpresa = a.Empresa  and a.Seguro=pxc.idpoliza)
	Where a.Empresa = @Empresa
	And A.Serie = emp.externos  and a.interno <>'S'
	And a.CtaCalculada IS NULL
	and emp.Nombre = @Hospital				 
	AND EXISTS (SELECT TOP 1 1 From Hospital..Cargos as C where C.empresa=a.empresa And c.serieadmision=a.serie and c.admision=a.codigo)

End

If @Opcion='Hospitalización-Emergencia'
Begin

	Select a.serie, a.codigo as Admision, a.status as Statusadmision, a.Fechaegreso,
	Paciente = (Select Rtrim(p.nombre)+' '+Rtrim(p.apellido) from Hospital..Pacientes as P (nolock) Where p.codigo=a.paciente and p.empresa=a.empresa),
	s2.nombre as NombreSeguro, s1.nombre as NombreAseguradora, pxc.nombre as Planxcontrato, DATEDIFF(dd, a.Entrada, ISNULL(a.FechaEgreso, GETDATE())) as Dias
	,TipoAdmision= CASE
                                        WHEN a.serie IN( Emp.internos )AND a.interno = 'S' 
                                            THEN 'INTERNOS'
                                        WHEN a.serie IN( emp.urgencias )AND a.interno <> 'S' 
                                            THEN'URGENCIAS'
                                        WHEN a.serie IN( emp.externos )AND a.interno <> 'S' 
                                            THEN 'EXTERNOS'
                                        WHEN a.serie IN(emp.igss, emp.igsssesiones, emp.igsshemodialisis)
                                            THEN 'IGSS'
                                        WHEN a.interno = 'S' THEN 'INTERNOS'
                                      END 
    From Hospital..Admisiones A (nolock)
		INNER Join Hospital.dbo.seguros as s2 (nolock) ON (s2.Empresa = a.Empresa and s2.Codigo = a.Seguro)
		INNER Join Hospital..Aseguradoras as s1 (nolock) ON (s1.Empresa = s2.Empresa and s1.Asegura = s2.Asegura)
		Inner Join Nominadb.dbo.EmpHospital as emp (nolock) ON (a.empresa=emp.empresa and emp.activo='S' And internos is not null 
		            AND a.Serie IN (emp.Internos, emp.Urgencias, emp.Externos, emp.Igss, emp.Farmacia, emp.igssSesiones,emp.igsshemodialisis))
		Left Join PlanesMedicos.dbo.PlanesXContrato pxc (nolock) ON (pxc.IdEmpresa = a.Empresa  and a.Seguro=pxc.idpoliza)
	Where a.Empresa = @Empresa
	and (a.Serie = emp.Urgencias  OR a.interno='S' )
	And a.FechaEgreso IS NOT NULL  and a.CtaCalculada IS NULL
	And emp.Nombre = @Hospital
	AND EXISTS (SELECT TOP 1 1 from Hospital..Cargos as C (nolock)
		Where C.Empresa = a.Empresa and c.Serieadmision = a.Serie and c.Admision = a.Codigo)


End

If @Opcion='Hospitalización'
Begin

	Select a.serie, a.Codigo as Admision, a.Status as Statusadmision, a.Fechaegreso, 
	Paciente=(Select Rtrim(p.nombre)+' '+Rtrim(p.apellido) from Hospital..Pacientes as P (nolock) Where p.codigo=a.paciente and p.empresa=a.empresa),
	s2.nombre as NombreSeguro, s1.nombre as NombreAseguradora, pxc.nombre as Planxcontrato, DATEDIFF(dd, a.Entrada, ISNULL(a.FechaEgreso, GETDATE())) as Dias
	,TipoAdmision= CASE
                                        WHEN a.serie IN( Emp.internos )AND a.interno = 'S' 
                                            THEN 'INTERNOS'
                                        WHEN a.serie IN( emp.urgencias )AND a.interno <> 'S' 
                                            THEN'URGENCIAS'
                                        WHEN a.serie IN( emp.externos )AND a.interno <> 'S' 
                                            THEN 'EXTERNOS'
                                        WHEN a.serie IN(emp.igss, emp.igsssesiones, emp.igsshemodialisis)
                                            THEN 'IGSS'
                                        WHEN a.interno = 'S' THEN 'INTERNOS'
                                      END 
    From Hospital..Admisiones A (nolock)
	INNER Join Hospital.dbo.seguros as s2 (nolock) ON (s2.Empresa = a.Empresa and s2.Codigo = a.Seguro)
	INNER Join Hospital..Aseguradoras as s1 (nolock) ON (s1.Empresa = s2.Empresa and s1.Asegura = s2.Asegura)
	Inner Join Nominadb.dbo.EmpHospital as emp (nolock) ON (a.empresa=emp.empresa and emp.activo ='S' And internos is not null 
			AND a.Serie IN (emp.Internos, emp.Urgencias, emp.Externos, emp.Igss, emp.Farmacia, emp.igssSesiones,emp.igsshemodialisis))
	Left Join PlanesMedicos.dbo.PlanesXContrato pxc (nolock) ON (pxc.IdEmpresa = a.Empresa  and a.Seguro=pxc.idpoliza)
	Where a.Empresa = @Empresa
	And a.interno = 'S'
	And a.Fechaegreso is NOT null  and a.CtaCalculada is null
	And emp.nombre = @Hospital 	
	AND EXISTS (SELECT TOP 1 1 From Hospital..Cargos as C (nolock) 
		Where C.empresa = a.Empresa And c.serieadmision=a.serie and c.admision=a.codigo)


End
Commit Transaction
 END TRY
  BEGIN CATCH
    ROLLBACK TRANSACTION
    SET @ErrRaise = ERROR_MESSAGE()
    SET @ErrRaiseNum = ERROR_NUMBER()
    --Select ERROR_NUMBER ( )  as NoError, ERROR_MESSAGE() as textoerror
    RAISERROR (@ErrRaise, 16, @ErrRaiseNum)
  END CATCH
End



---> Creación de procedimientos almacenados

GO;





--> inicia creación de indices
CREATE NONCLUSTERED INDEX [idxSemaforoEgresos] ON [dbo].[Admisiones]
(
	[Empresa] ASC,
	[CtaCalculada] ASC,
	[Interno] ASC,
	[Seguro] ASC,
	[Serie] ASC,
	[Paciente] ASC,
	[Codigo] ASC
)
INCLUDE([Entrada],[Status],[FechaEgreso]) WITH (SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF) ON [PRIMARY]

--> reduce spool de consulta 
Use Hospital

CREATE NONCLUSTERED INDEX [idxsemaforoEgresosEnPorInt] ON [dbo].[Admisiones]
(
	[Serie] ASC,
	[Codigo] ASC,
	[Empresa] ASC,
	[CtaCalculada] ASC,
	[Paciente] ASC,
	[Seguro] ASC,
	[FechaEgreso] ASC,
	[Descuento] ASC,
	[Status] ASC
)
INCLUDE([Entrada],[PorcentajeDescuento],[Interno]) WITH (SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF) ON [PRIMARY]



Use Hospital 
CREATE NONCLUSTERED INDEX [idxSemaforoEgresosSerAdEmp] ON [dbo].[Cargos]
(
	[SerieAdmision] ASC,
	[Admision] ASC,
	[Empresa] ASC
)
INCLUDE([Categoria],[Valor]) WITH (SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF) ON [PRIMARY]


Use planesMedicos

CREATE NONCLUSTERED INDEX [idxsemaforoegresosIdContrato] ON [dbo].[CopagosXContrato]
(
	[IdContrato] ASC
)
INCLUDE([IdPlan],[IdCopagoDescripcion],[IdTipoHospital],[DiaInicial],[DiaFinal],[Porcentaje],[Monto]) WITH (SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF) ON [PRIMARY]
