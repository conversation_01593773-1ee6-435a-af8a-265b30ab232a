<template>
    <vx-card :title="`Facturación SASI y Percapitados ${sesion.sesion_sucursal_nombre}`"> 
        <vs-popup :active.sync="popUpBusquedaFacturaXLote" title="Búsqueda de Facturas Por Lote">
            <DxDataGrid v-bind="DefaultDxGridConfiguration" 
                        :data-source="dataSource.lotesFacturasDataSource"      
                        :searchPanel="{visible:true}" :sorting="{mode: 'none'}" 
                        :height="'100%'" :width="'100%'" 
                        @row-dbl-click="SeleccionarFacturaLote">
                <DxDataGridSelection mode="single" />
                <DxDataGridScrolling  mode="virtual"
                              row-rendering-mode="virtual"
                />                
                <DxDataGridPaging :page-size="15"/>
                <DxDataGridColumn :width="100" data-field="SerieFactura" caption="Serie" alignment="center" />
                <DxDataGridColumn :width="100" data-field="Factura" caption="Documento" alignment="center" />
                <DxDataGridColumn :width="350" data-field="Nombre" caption="Nombre" alignment="center" />
                <DxDataGridColumn :width="180" data-field="TotalFactura" caption="Total Factura" format="Q #,##0.00" data-type="number" alignment="center" />
                <DxDataGridColumn :width="180" data-field="Saldo" caption="Saldo"  format="Q #,##0.00" data-type="number" alignment="center" />
                <DxDataGridColumn :width="150" data-field="FechaInicial" caption="Fecha Inicial" format="dd/MM/yyyy" data-type="date" alignment="center" />
                <DxDataGridColumn :width="150" data-field="FechaFinal" caption="Fecha Final" format="dd/MM/yyyy" data-type="date" alignment="center" />  
                <DxDataGridHeaderFilter :group-interval="15"/>
            </DxDataGrid>
        </vs-popup>
        <vs-popup title="Emisión de Recibos Por Lote" :active.sync="popUpRecibo">
            <vs-row>                
                <vs-row class="w-full">
                    <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-4/12 p-1"> 
                        <vs-input label="No. Cheque"
                                class="w-full"
                                type="number"
                                v-on:keydown.enter="GeneraRecibosLote(3)"
                                v-on:keydown.tab.prevent="GeneraRecibosLote(3)" 
                                v-model="recibo.noAutorizacion"/> 
                    </vs-col>
                    <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-4/12 p-1"> 
                        <vs-input label="No. Cuenta"
                                class="w-full"
                                disabled="true"
                                v-model="recibo.noCuenta"/> 
                    </vs-col>
                    <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1 pt-6"> 
                        <vs-button @click="GeneraRecibosLote(1)" color="primary" id="botonRecibo" icon-pack="feather" icon="icon-settings" class="w-full">
                            Aplicar Pago
                        </vs-button>
                    </vs-col>
                    <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-1/12 p-1 pt-6"> 
                        <vs-button @click="popUpRecibo = false" color="warning" class="w-full pl-5 pr-5">
                            Cancelar
                        </vs-button>
                    </vs-col>
                </vs-row>
                <vs-row class="w-full">
                    <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-4/12 p-1">     
                        <div>
                            <label class="typo__label"> Forma de Pago  </label>                                                                              
                            <multiselect 
                                     v-model="recibo.formaDePagoSeleccionada" :custom-label="(formaDePago)=>{return formaDePago.Descripcion}"
                                     :options="recibo.formasDePago"  :allow-empty="false" :showLabels="false"
                                     :disabled="true"
                                     placeholder="Forma de Pago"                                      
                                     label="Forma de Pago" >
                            </multiselect>      
                        </div> 
                    </vs-col>
                    <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-4/12 p-1">
                        <div class="w-full">
                            <label class="typo__label"> Banco </label>                                                                              
                            <multiselect 
                                     v-model="recibo.bancoSeleccionado" :custom-label="(banco)=>{return banco.Nombre}"
                                     :options="recibo.listaBanco"  :allow-empty="false" :showLabels="false"
                                     :disabled="true"
                                     placeholder="Banco"                                      
                                     label="Banco" >
                            </multiselect>     
                        </div>
                    </vs-col>
                    <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1" >
                        <label class="typo__label"> Monto </label>   
                        <DxNumberBox            
                                :min="0"
                                :show-spin-buttons="false"
                                :disabled="true"
                                v-model=recibo.saldoRecibo format="Q #,##0.00" style="padding: 2px;"/>
                    </vs-col>
                </vs-row>
            </vs-row>
            <vs-row style="height: 100px;"></vs-row>
        </vs-popup>

        <vs-row> 
            <ConfiguracionCaja  ref="ConfiguracionCajaRef"
                                @CargarCaja="CargarCaja" :FiltrarAgrupacionCaja=FiltrarAgrupacionCaja
                                TipoCajaSeleccionada="TipoCajaFacturacion" CajaSeleccionada="CajaFacturacion" 
                                class="w-full">
            </ConfiguracionCaja>
            <vs-row class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12 p-1" >
                <vs-row class="w-full">
                    <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-3/12 p-1">                                                      
                        <div >Tipo Facturacion</div>
                        <div>
                        <DxRadioGroup
                            :items="Facturaciones"
                            value-expr="Id"
                            display-expr="Nombre"
                            :value="TipoFacturacionRadio"
                            @valueChanged="TipoFacturacionRadioChange"
                            layout="horizontal"
                        />
                        </div>
                    </vs-col>
                    <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-3/12 p-1">                                  
                        <vs-input label="Fecha Inicial" type="date" class="w-full" v-model="consultaLotes.fechaInicial" />
                    </vs-col>
                    <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-3/12 p-1">                                  
                        <vs-input label="Fecha Final" type="date" class="w-full" v-model="consultaLotes.fechaFinal" />
                    </vs-col>
                    <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-3/12 p-1">                                                         
                        <label style="color: gray; font-size: smaller">Fecha Documento</label>
                        <flat-pickr class="w-full" v-model="consultaLotes.fechaDocumento" :config="configFromdateTimePicker"/>
                    </vs-col>                
                </vs-row>
                <vs-row class="w-full">
                    <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1">
                        <vs-input label="Nit" type="text" class="w-full input-class" v-model="numeroNit" :disabled="true" />
                    </vs-col>
                    <vs-col class="sm:w-full md:w-full lg:w-5/12 xl:w-4/12 p-1">
                        <vs-input label="Nombre" type="text" class="w-full" v-model="nombreNit"  :disabled="true"/>
                    </vs-col>
                    <vs-col class="sm:w-full md:w-full lg:w-5/12 xl:w-6/12 p-1">
                        <vs-input label="Dirección" type="text" class="w-full" v-model="direccionNit" :disabled="true"/>
                    </vs-col>
                </vs-row>
            </vs-row>
            <vs-row class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12 p-1" >
                <vs-row class="w-full" vs-justify="center">
                    <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1">
                        <vs-button @click="GeneracionLote();" color="primary" icon-pack="feather" icon="icon-settings" class="w-full ajustar-texto-botton">
                            Generar
                        </vs-button>
                    </vs-col>    
                    <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1">
                        <vs-button @click="BusquedaFacturaPorLote()" color="primary" icon-pack="feather" icon="icon-search" class="w-full ajustar-texto-botton">
                            Buscar
                        </vs-button>
                    </vs-col>    
                    <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-2/12 p-1">
                        <vs-button  @click="GeneraFacturaLotes(6)" id="botonFacturar" color="primary" icon-pack="feather" icon="icon-file-text" 
                                   :disabled="!habilitarFactura || !configuracion.activarFacturacion" 
                                   class="w-full ajustar-texto-botton">
                            Facturar
                        </vs-button>
                    </vs-col>    
                    <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-2/12 p-1">
                        <vs-button color="primary" @click="exportGrids()"
                                   :disabled="!habilitarExportar" 
                                   class="w-full ajustar-texto-botton">
                            <i class="far fa-file-excel"></i>
                            Exportar
                        </vs-button>
                    </vs-col>    
                    <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1">
                        <vs-button color="primary" icon-pack="feather" icon="icon-file-plus" 
                                    @click="AbrirRecibo"
                                   :disabled="!recibo.saldoRecibo || recibo.saldoRecibo <= 0 || !configuracion.activarRecibos"
                                   class="w-full ajustar-texto-botton">
                            Recibo
                        </vs-button>
                    </vs-col>   
                    <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1">
                        <vs-button color="primary" icon-pack="feather" icon="icon-trash" 
                                    @click="()=>{this.LimpiarPantalla(); this.Consulta().LimpiarTablas();}"                                   
                                   class="w-full ajustar-texto-botton">
                            Limpiar
                        </vs-button>
                    </vs-col>     
                </vs-row>
                <vs-row class="w-full" vs-justify="center">
                    <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1">
                        <label class="typo__label label-total centered-element pl-3">
                            <b>Total:</b>
                        </label>
                    </vs-col>
                    <vs-col class="sm:w-full md:w-full lg:w-10/12 xl:w-10/12 p-1">
                        <label class="typo__label label-total centered-element">
                            {{ $formato_moneda(montoTotalFacturacion) }}
                        </label>
                    </vs-col>                    
                </vs-row>
            </vs-row>
            <vs-row vs-justify="center" vs-align="center" class='w-full'>
                <DxTabPanel width="100%" :height="'100%'" :data-source="dataSource.tabs" :show-nav-buttons="true" :defer-rendering="false">
                    <template #title="{ data: tab }">
                        <span>{{ tab.name }}</span>
                    </template>
                    <template #item="{ data: tab }">
                        <div v-if="tab.value === 1" class="tabpanel-item">
                            <vx-card>
                                <vs-row vs-justify="center" vs-align="center" class="w-full">
                                    <DxDataGrid v-bind="DefaultDxGridConfiguration" :headerFilter="{visible:false,allowSearch:false}"                                                 
                                            id="CargosPorAdmisionGrid"
                                            name="CargosPorAdmisionGrid"                                                                                                
                                            :ref="CargosPorAdmisionRef"
                                            :data-source="dataSource.cargosAdmision" width="100%" height="'100%'" >
                                            <DxDataGridColumn :width="250" caption="Admisión" alignment="center">
                                                <DxDataGridColumn :width="100" data-field="SerieAdmision" caption="Serie" alignment="center" />
                                                <DxDataGridColumn :width="150" data-field="Admision" caption="Número" alignment="center" />
                                            </DxDataGridColumn>
                                            <DxDataGridColumn :width="400" caption="Factura Aseguradora" alignment="center">
                                                <DxDataGridColumn :width="100" data-field="EmpresaFacRec" caption="Empresa" alignment="center" />
                                                <DxDataGridColumn :width="150" data-field="SerieFacRec" caption="Serie" alignment="center" />
                                                <DxDataGridColumn :width="150" data-field="FacRec" caption="Número" alignment="center" />
                                            </DxDataGridColumn>
                                            <DxDataGridColumn :width="150" data-field="EmpresaReal" caption="Empresa Real" alignment="center" />
                                            <DxDataGridColumn :width="200" data-field="TotalCargos" caption="Total Cargos" format="Q #,##0.00" data-type="number" alignment="right" />
                                            <DxDataGridColumn :width="200" data-field="CopagoCoaseguro" caption="Copago/Coaseguro" format="Q #,##0.00" data-type="number" alignment="right" />
                                            <DxDataGridColumn :width="200" data-field="TotalAdmision" caption="Total Admisión" format="Q #,##0.00" data-type="number" alignment="right" />
                                            <DxDataGridColumn :group-index="0" data-field="Aseguradora" group-continued-message="" group-continues-message="" />
                                            <DxDataGridGrouping :auto-expand-all="expandir"/>
                                            <DxDataGridSummary>
                                                <DxDataGridTotalItem  column="TotalCargos" summary-type="sum" value-format="Q #,##0.00" data-type="number" display-format="{0}"/>
                                                <DxDataGridTotalItem  column="CopagoCoaseguro" summary-type="sum" value-format="Q #,##0.00" data-type="number" display-format="{0}"/>
                                                <DxDataGridTotalItem  column="TotalAdmision" summary-type="sum"  value-format="Q #,##0.00" data-type="number" display-format="{0}"/>
                                            </DxDataGridSummary> 
                                            <DxDataGridPaging :page-size="15"/>
                                            <DxDataGridPager  :show-page-size-selector="true"
                                                        :allowed-page-sizes="[15, 30, 45]"
                                            />
                                    </DxDataGrid>
                                </vs-row>                                    
                            </vx-card>
                        </div>
                        <div v-else-if="tab.value === 2" class="tabpanel-item w-full" >
                            <vs-col vs-justify="center" vs-align="center" class="w-full">
                                <vx-card>
                                    <vs-row vs-justify="center" vs-align="center" class="w-full">
                                        <DxDataGrid v-bind="DefaultDxGridConfiguration" :headerFilter="{visible:false,allowSearch:false}" 
                                                    id="CargosDetalleGrid"
                                                    name="CargosDetalleGrid"
                                                    :ref="CargosDetalleRef"
                                                    :data-source="dataSource.cargosDetalle" :height="'100%'">
                                                    <DxDataGridColumn caption="Admisión" alignment="center">
                                                        <DxDataGridColumn :width="100" data-field="SerieAdmision" caption="Serie" alignment="center" />
                                                        <DxDataGridColumn :width="100" data-field="Admision" caption="Número" alignment="center" />
                                                    </DxDataGridColumn>
                                                    <DxDataGridColumn caption="Orden" alignment="center">
                                                        <DxDataGridColumn :width="100" data-field="TipoOrden" caption="Tipo" alignment="center" />
                                                        <DxDataGridColumn :width="100" data-field="Orden" caption="Número" alignment="center" />
                                                    </DxDataGridColumn>
                                                    <DxDataGridColumn caption="Producto" alignment="center">
                                                        <DxDataGridColumn :width="75"  data-field="Producto" caption="Código" alignment="center" />
                                                        <DxDataGridColumn :width="250" data-field="NombreProducto" caption="Nombre" alignment="center" />
                                                        <DxDataGridColumn :width="150" data-field="Valor" caption="Monto" format="Q #,##0.00" data-type="number" alignment="right" />
                                                    </DxDataGridColumn>
                                                    <DxDataGridColumn :width="150" data-field="EmpresaFacRec" caption="E. Copago" alignment="center" />
                                                    <DxDataGridColumn :width="150" data-field="EmpresaReal" caption="Empresa Real" alignment="center" />
                                                    <DxDataGridPaging :page-size="15"/>
                                                    <DxDataGridPager  :show-page-size-selector="true"
                                                            :allowed-page-sizes="[15, 30, 45]"
                                                    />
                                        </DxDataGrid>
                                    </vs-row>
                                </vx-card>
                            </vs-col>
                        </div>
                        <div v-else-if="tab.value === 3" class="tabpanel-item" >
                            <vs-col vs-justify="center" vs-align="center" vs-w="12">
                                <vx-card>
                                    <vs-row vs-justify="center" vs-align="center" class="w-full">
                                        <DxDataGrid v-bind="DefaultDxGridConfiguration" :headerFilter="{visible:false,allowSearch:false}" 
                                                    id="SaldoPorAdmisionGrid"
                                                    :ref="SaldoPorAdmisionRef"
                                                    :data-source="dataSource.saldosAdmision" :height="'100%'" 
                                                    :summary="{
                                                        visible:true,
                                                        groupItems:[
                                                            {column: 'TotalAdmision'}
                                                        ]
                                                        }"
                                                    >
                                                    <DxDataGridColumn :width="100" data-field="SerieAdmision" caption="Serie" alignment="center" />
                                                    <DxDataGridColumn :width="100" data-field="Admision" caption="Admisión" alignment="center" />
                                                    <DxDataGridColumn :width="150" data-field="TotalCargos" caption="Total Cargos" format="Q #,##0.00" data-type="number" alignment="right" />
                                                    <DxDataGridColumn :width="150" data-field="CopagoMonto" caption="Copago Monto" format="Q #,##0.00" data-type="number" alignment="right" />
                                                    <DxDataGridColumn :width="150" data-field="TotalAdmision" caption="Total Admisión" format="Q #,##0.00" data-type="number" alignment="right" />
                                                    <DxDataGridColumn :width="120" data-field="SaldoAdmision" caption="Saldo Admisión" format="Q #,##0.00" data-type="number" alignment="right" />
                                                    <DxDataGridColumn :width="150" data-field="EmpresaRecibo" caption="Empresa" alignment="center" />
                                                    <DxDataGridColumn :width="150" data-field="SerieRecibo" caption="Serie Recibo" alignment="center" />
                                                    <DxDataGridColumn :width="150" data-field="Recibo" caption="Recibo" alignment="center" />
                                                    <DxDataGridColumn :width="150" data-field="MontoAplicado" caption="Monto Aplicado" format="Q #,##0.00" data-type="number" alignment="right" />
                                                    <DxDataGridColumn :width="150" data-field="Registro" caption="Registro" format="dd/MM/yyyy" data-type="date" alignment="center" />
                                                    <DxDataGridSummary>
                                                        <DxDataGridTotalItem  column="TotalAdmision" summary-type="sum" value-format="Q #,##0.00" data-type="number" display-format="{0}"/>
                                                        <DxDataGridTotalItem  column="SaldoAdmision" summary-type="sum" value-format="Q #,##0.00" data-type="number" display-format="{0}"/>
                                                    </DxDataGridSummary>  
                                                    <DxDataGridPaging :page-size="15"/>
                                                    <DxDataGridPager  :show-page-size-selector="true"
                                                            :allowed-page-sizes="[15, 30, 45]"
                                                    />
                                        </DxDataGrid>
                                    </vs-row>
                                </vx-card>
                            </vs-col>
                        </div>
                        <div v-else-if="tab.value === 4" class="tabpanel-item" >
                            <vs-col vs-justify="center" vs-align="center" vs-w="12">
                                <vx-card>
                                    <vs-row vs-justify="center" vs-align="center" class="w-full">
                                        <DxDataGrid v-bind="DefaultDxGridConfiguration" :headerFilter="{visible:false,allowSearch:false}" 
                                                    id="LoteAdmisionesGrid"
                                                    :ref="LoteAdmisionesRef"
                                                    :data-source="dataSource.loteAdmision" :height="'100%'" width ="100%">
                                                    <DxDataGridColumn :width="100" data-field="EmpresaFacRec" caption="Empresa" alignment="center" />
                                                    <DxDataGridColumn :width="80" data-field="SerieAdmision" caption="Serie Adm." alignment="center" />
                                                    <DxDataGridColumn :width="100" data-field="Admision" caption="Admisión" alignment="center" />
                                                    <DxDataGridColumn :width="100" data-field="Seguro" caption="Seguro" alignment="center" />
                                                    <DxDataGridColumn :width="300" data-field="Nombre" caption="Nombre Plan" alignment="center" />
                                                    <DxDataGridColumn :width="120" data-field="Entrada" caption="Entrada" format="dd/MM/yyyy" data-type="date" alignment="center" />
                                                    <DxDataGridColumn :width="120" data-field="Salida" caption="Salida" format="dd/MM/yyyy" data-type="date" alignment="center" />
                                                    <DxDataGridColumn :width="150" data-field="SerieFacRec" caption="Serie Factura" alignment="center" />
                                                    <DxDataGridColumn :width="150" data-field="FacRec" caption="Factura" alignment="center" />
                                                    <DxDataGridColumn :width="150" data-field="TotalCargos" caption="Total Cargos" format="Q #,##0.00" data-type="number" alignment="right" />
                                                    <DxDataGridColumn :width="150" data-field="CopagoCoaseguro" caption="Copago Monto" format="Q #,##0.00" data-type="number" alignment="right" />
                                                    <DxDataGridColumn :width="150" data-field="TotalAdmision" caption="Total Admisión" format="Q #,##0.00" data-type="number" alignment="right" />
                                                    <DxDataGridPaging :page-size="15"/>
                                                    <DxDataGridPager  :show-page-size-selector="true"
                                                            :allowed-page-sizes="[15, 30, 45]"
                                                    />
                                        </DxDataGrid>
                                    </vs-row>
                                </vx-card>
                            </vs-col>
                        </div>
                        <div v-else-if="tab.value === 5" class="tabpanel-item" >
                            <vs-col vs-justify="center" vs-align="center" vs-w="12">
                                <vx-card>
                                    <vs-row vs-justify="center" vs-align="center" class="w-full">
                                        <DxDataGrid v-bind="DefaultDxGridConfiguration" :headerFilter="{visible:false,allowSearch:false}" 
                                                    id="DetalleFacturaGrid"
                                                    :ref="DetalleFacturaRef"
                                                    :data-source="dataSource.detalleFactura" :height="'100%'">
                                                    <DxDataGridColumn :width="100" data-field="Categoria" caption="Categoria" alignment="center" />
                                                    <DxDataGridColumn :width="100" data-field="Cuenta" caption="Cuenta" alignment="center" />
                                                    <DxDataGridColumn :width="200" data-field="Nombre" caption="Nombre" alignment="center" />
                                                    <DxDataGridColumn :width="100" :calculate-cell-value="()=>{return '1'}" caption="Cantidad" data-type="number" alignment="center" />
                                                    <DxDataGridColumn :width="150" data-field="Cargos" caption="Cargos" format="Q #,##0.00" data-type="number" alignment="right" />
                                                    <DxDataGridColumn :width="150" data-field="Copago" caption="Copago" format="Q #,##0.00" data-type="number" alignment="right" />
                                                    <DxDataGridColumn :width="150" data-field="Total" caption="Total" format="Q #,##0.00" data-type="number" alignment="right" />
                                                    <DxDataGridSummary>
                                                        <DxDataGridTotalItem  column="Cargos" summary-type="sum" value-format="Q #,##0.00" data-type="number" display-format="{0}"/>
                                                        <DxDataGridTotalItem  column="Copago" summary-type="sum" value-format="Q #,##0.00" data-type="number" display-format="{0}"/>
                                                        <DxDataGridTotalItem  column="Total" summary-type="sum" value-format="Q #,##0.00" data-type="number" display-format="{0}"/>
                                                    </DxDataGridSummary> 
                                                    <DxDataGridPaging :page-size="15"/>
                                                    <DxDataGridPager  :show-page-size-selector="true"
                                                            :allowed-page-sizes="[15, 30, 45]"
                                                    /> 
                                        </DxDataGrid>
                                    </vs-row>
                                </vx-card>
                            </vs-col>
                        </div>
                    </template>
                </DxTabPanel>               
            </vs-row>
        </vs-row>
    </vx-card>
</template>
<script>
import {
    DefaultDxGridConfiguration
} from './data';
import { Workbook } from 'exceljs';
import flatPickr from 'vue-flatpickr-component';
import 'flatpickr/dist/flatpickr.css';
import { Spanish as SpanishLocale } from 'flatpickr/dist/l10n/es.js';
import moment from "moment"
import { ref } from 'vue'
import "vue-multiselect/dist/vue-multiselect.min.css"; 
import { saveAs } from 'file-saver-es';
import ConfiguracionCaja from "/src/components/sermesa/modules/caja/ConfiguracionCaja.vue";    
import { exportDataGrid } from 'devextreme/excel_exporter';   
const autoExpandAll = ref(true);
const CargosPorAdmisionRef = "grid1"
const CargosDetalleRef = "grid2";
const SaldoPorAdmisionRef = "grid3";
const LoteAdmisionesRef = "grid4";
const DetalleFacturaRef = "grid5";
export default{    
    name:'CAJERO_SASI',
    components:{
        flatPickr,      
        ConfiguracionCaja
    },
    data() {
        return{  
            permisos:{
                recibos:false,
                facturar:false
            },
            FiltrarAgrupacionCaja:['5'],   
            expandir:autoExpandAll,
            CargosPorAdmisionRef,
            CargosDetalleRef,
            SaldoPorAdmisionRef,
            LoteAdmisionesRef,
            DetalleFacturaRef,
            habilitarFactura: false,
            habilitarExportar: false,
            habilitarRecibo: false,
            configuracion:{
                cajaSeleccionada: null,
                activarFacturacion: false,
                activarRecibos: false  
            },
            recibo:{
                formasDePago:[],
                listaBanco:[],
                noAutorizacion:null,
                noCuenta:null,
                saldoRemision:null,
                saldoRecibo:null,
                saldoReciboInicial:null,
                valido:null,
                formaDePagoSeleccionada:{Descripcion:'Cheque',Valor:'B'},
                bancoSeleccionado:{Codigo:'IND',Nombre:'Banco Industrial, S. A.'},
            },
            TipoFacturacionRadio:null,   
            Facturaciones:[{Nombre:'Facturación Sasi',Id:1},{Nombre:'Facturación Percapitados', Id:2}],
            DefaultDxGridConfiguration:{
                ...DefaultDxGridConfiguration,
                groupContinuedMessage:'Siguiente pagina',
                groupContinuesMessage:'Siguiente pagina',                
                headerFilter:{
                    visible:false,
                    allowSearch:false
                },
            },
            popUpBusquedaFacturaXLote:false,
            popUpRecibo:false,
            numeroNit:null,
            nombreNit:null,
            direccionNit:null,
            montoTotalFacturacion:null,
            consultaLotes:{
                serieFel:null,
                proceso:null,
                tipoSasi:null,
                factura:null,
                fechaInicial:null,
                fechaFinal:null,
                fechaDocumento:null,
            },
            dataSource:{
                lotesFacturas:[],
                lotesFacturasDataSource:[],
                cargosAdmision:[],
                cargosDetalle:[],
                saldosAdmision:[],
                loteAdmision:[],
                detalleFactura:[],
                tabs: [
                        {
                            name: 'Cargos por Admisión',
                            value: 1
                        }, {
                            name: 'Cargos Detalle',
                            value: 2
                        }, {
                            name: 'Saldos por Admisión',
                            value: 3
                        }, {
                            name: 'Lote Admisiones',
                            value: 4
                        }, {
                            name: 'Detalle Factura',
                            value: 5
                        }
                        
                    ],
            },
            configFromdateTimePicker: {
                minDate: new Date().fp_incr(-4),
                maxDate: new Date(),
                locale: SpanishLocale,
                dateFormat: "d/m/Y",
            },
        }
    },
    mounted(){
        this.Init()                 
    },
    activated() {
        window.addEventListener('focus', this.actualizarCajero);   
    },
    deactivated() {
        window.removeEventListener('focus',this.actualizarCajero);
    },
    watch:{
        popUpRecibo(value){
            if(!value){                
                this.recibo.noAutorizacion = null
                this.recibo.noCuenta = null
                this.recibo.valido = null
            }
        }
    },
    methods:{   
        LimpiarPantalla(){
            this.consultaLotes.fechaInicial = null
            this.consultaLotes.fechaFinal = null
            this.consultaLotes.fechaDocumento = null
            this.consultaLotes.serieFel = null
            this.consultaLotes.proceso = null
            this.consultaLotes.tipoSasi = null
            this.consultaLotes.factura = null
            this.montoTotalFacturacion = null           
            this.habilitarExportar = false
            this.TipoFacturacionRadio = 1
            this.consultaLotes.tipoSasi = 1
        },
        AbrirRecibo(){
            if(!this.permisos.recibo){
                this.Consulta().MostrarError('No cuenta con permiso para realizar recibos');
                return
            }
            this.popUpRecibo = true;
        },
        CargarCaja(cajaSeleccionada){                
                this.configuracion.cajaSeleccionada   = cajaSeleccionada.Cajero
                this.configuracion.activarFacturacion = cajaSeleccionada.Factura
                this.configuracion.activarRecibos     = cajaSeleccionada.Recibo
            },
        async GenerarFactura(Serie,NumeroFactura) {
            let reporte = "Impresion FEL"

            let postData = {
                SerieFactura: Serie ,
                NumFactura: NumeroFactura,
                nombrereporte: this.configuracion.cajaSeleccionada.FormatoImpresion
            }


            this.$reporte_modal({
                Nombre: reporte,
                Opciones: {
                    ...postData
                }
            })                

        },   
        async GenerarRecibo(Serie,NumeroRecibo) {

            let reporte = "Recibo"

            let postData = {
                SerieRecibo:   Serie ,
                NumeroRecibo: NumeroRecibo
            }


            this.$reporte_modal({
                Nombre: reporte,
                Opciones: {
                    ...postData
                },
                Formato:this.FormatoReporte,
                NombreArchivoDescargar:Serie+'_'+NumeroRecibo,
                Descargar:true
            })                

        },  
        Fel(serieFactura,numeroFactura){
            this.axios.post('/app/v1_FacturaElectronica/GeneraFel',
                            {serieFactura: serieFactura,
                                numeroFactura: numeroFactura
                            })                                  
                        .then(resp=>{
                                if(resp.data.codigo == 0 && resp.data.json[0].codigo == 0){
                                    this.Consulta().MostrarSatisfactorio(resp.data.json[0].descripcion);
                                    this.GenerarFactura(serieFactura,numeroFactura);                                                                               
                                }else{
                                    this.Consulta().MostrarError(resp.data.json[0].descripcion);
                                }
                            })                                 
        },
        ValidarMontoRecibo(){
            let saldoIngresado = this.ConvertirFloat(this.recibo.saldoRecibo);
            let saldoInicial = this.ConvertirFloat(this.recibo.saldoReciboInicial);
            let saldoRemision = this.ConvertirFloat(this.recibo.saldoRemision);
            let valido = true
            if(saldoInicial <= 0.00){
                this.Consulta().MostrarError('No se puede realizar recibo ya que no cuenta con un monto de saldo')
                valido = false
            }
            if(saldoIngresado > saldoInicial){
                this.Consulta().MostrarError('No puede ingresar un monto mayor al del recibo')
                valido = false
            }
            if(saldoIngresado <= 0){
                this.Consulta().MostrarError('El monto del recibo debe ser mayor a cero')
                valido = false
            }
            if(saldoIngresado > saldoRemision){
                this.Consulta().MostrarError('El cheque no cuenta con saldo suficiente para realizar el recibo')
                valido = false
            }
            return valido
        }, 
        exportGrids() {
            const workbook = new Workbook();
            const CargosPorAdmision = workbook.addWorksheet('CargosPorAdmision');
            const CargosDetalle = workbook.addWorksheet('CargosDetalle');
            const SaldoPorAdmision = workbook.addWorksheet('SaldoPorAdmision');
            const LoteAdmisiones = workbook.addWorksheet('LoteAdmisiones');
            const DetalleFactura = workbook.addWorksheet('DetalleFactura');
            exportDataGrid({
                worksheet: CargosPorAdmision,
                component: this.$refs[CargosPorAdmisionRef].instance              
            }).then(() => exportDataGrid({
                worksheet: CargosDetalle,
                component: this.$refs[CargosDetalleRef].instance   
            })).then(() => exportDataGrid({
                worksheet: SaldoPorAdmision,
                component: this.$refs[SaldoPorAdmisionRef].instance   
            })).then(() => exportDataGrid({
                worksheet: LoteAdmisiones,
                component: this.$refs[LoteAdmisionesRef].instance   
            })).then(() => exportDataGrid({
                worksheet: DetalleFactura,
                component: this.$refs[DetalleFacturaRef].instance   
            })).then(() => {                
                workbook.xlsx.writeBuffer().then((buffer) => {
                saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'Lotes_'+this.GetDateTimeValue(new Date())+'.xlsx');
                });
            });
        },
        parseDate(datestr, format) {
            if(!datestr || datestr == '')
                return null

            return moment(datestr, format, true).toDate();
        },
        calcularTotalAdmision(cargos){
           this.montoTotalFacturacion  = cargos.reduce((total, cargo)=>total+Number(cargo.TotalAdmision),0)                                 
        },
        GetDateValue(value) {
                if (value === undefined) {
                    return null
                } else if (value === null) {
                    return null
                } else if (value == '') {
                    return null
                }
                return moment(value).format('YYYYMMDD');          
        },
        GetDateTimeValue(value) {
                if (value === undefined) {
                    return null
                } else if (value === null) {
                    return null
                } else if (value == '') {
                    return null
                }
                return moment(value).format('YYYYMMDD_HHmm');          
        },
        Init(){
            this.permisos.facturar = this.$validar_privilegio('FACTURACION').status
            this.permisos.recibo = this.$validar_privilegio('RECIBO').status

            this.TipoFacturacionRadio = ref(this.Facturaciones[0].Id)
            this.CargarAgrupaciones('NIT_FACTURACION_LOTES');
            this.CargarAgrupaciones('FormasDePago');
            this.CargarAgrupaciones('Bancos',6);                 
        },
        actualizarCajero(){  
            this.$refs.ConfiguracionCajaRef.CargarCajaInicial();
        },
        TipoFacturacionRadioChange(Tipo){
            this.TipoFacturacionRadio = Tipo.value;
            this.consultaLotes.tipoSasi = Tipo.value;
        },
        CargarAgrupaciones(agrupacion, subOpcion=2){
            this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision', {
                                opcion:'C',
                                subOpcion:subOpcion,
                                agrupacion
                            })
                        .then(resp => {                            
                                if (resp.data && resp.data.json.length > 0){
                                    if(agrupacion == 'NIT_FACTURACION_LOTES'){
                                        this.numeroNit = resp.data.json[0].Valor;
                                        this.CargarDatosNit()
                                    }
                                    if(agrupacion == 'FormasDePago'){
                                        this.recibo.formasDePago = resp.data.json;
                                    }
                                    if(agrupacion == 'Bancos'){
                                        this.recibo.listaBanco = resp.data.json;
                                    }
                                }
                            }
                            )
        },
        GeneraRecibosLote(proceso){
            if(proceso == 1){
                let valida = this.ValidarMontoRecibo()
                if(!valida) return;
                
                const botonRecibo = document.getElementById("botonRecibo");
                if(botonRecibo) botonRecibo.blur();
            }

            if(this.recibo.valido != 1 && proceso==1){
                this.Consulta().MostrarError('Ingrese un no. de autorización valido')
                return
            }     
            
            

            this.axios.post('/app/v1_caja/GeneraRecibosLote',{
                            proceso,                            
                            serieFel: this.consultaLotes.serieFel,
                            serieRecibo: this.configuracion.cajaSeleccionada.SerieRecibo,
                            factura: this.ConvertirEntero(this.consultaLotes.factura),
                            idBanco: this.recibo.bancoSeleccionado.Codigo,
                            formaPago: this.recibo.formaDePagoSeleccionada.Valor,
                            montoRecibo: this.ConvertirFloat(this.recibo.saldoRecibo),
                            numeroCheque: this.ConvertirEntero(this.recibo.noAutorizacion), 
                            numeroCaja: this.configuracion.cajaSeleccionada.CajaLocal,
                            docFiscal:0,
                        })
                    .then(resp=>{
                            if(proceso==1){
                                if(resp.data.codigo==0 || resp.data.codigo==200){
                                    this.GenerarRecibo(resp.data.Serie, resp.data.Numero)
                                    this.$vs.dialog({
                                                    type: 'confirm',
                                                    color: 'success',
                                                    title: 'Recibo',
                                                    acceptText: 'Aceptar',
                                                    cancelText: 'Cancelar',
                                                    text: resp.data.descripcion,                                               
                                                })
                                    this.ConsultaFacturaLotes(3);
                                    this.popUpRecibo = false
                                }
                            }
                            if(proceso==3){
                                if(resp.data.codigo == 0 && resp.data.json.length > 0){
                                    this.recibo.noCuenta      = resp.data.json[0].CuentaCheque
                                    this.recibo.saldoRemision = resp.data.json[0].Saldo                                    
                                    this.recibo.valido = 1
                                    this.ValidarMontoRecibo()
                                }else{                                    
                                    this.recibo.valido = null
                                    this.recibo.saldoRemision = null
                                    this.Consulta().MostrarError('No se encontro el no. de autorización.');
                                }
                            }
                        })
        },
        CargarDatosNit(){
            if(!this.numeroNit){
                this.Consulta().MostrarError('No se pudo obtener el nit, recargar la pagina');
                return
            }

            this.axios.post('/app/v1_caja/ValidacionDocumentoTributario',{
                                    tipoReceptor: 4,
                                    documento: this.numeroNit
                                })
                          .then(resp=>{
                                    if ( resp.data.codigo == 0 && resp.data.json.length > 0 && resp.data.json[0].codigo == 0){
                                        this.nombreNit = resp.data.json[0].NOMBRE
                                        this.direccionNit = resp.data.json[0].DIRECCION
                                    }else if ( resp.data.codigo == 0 && resp.data.json.length > 0 && resp.data.json[0].codigo != 0){
                                        this.Consulta().MostrarError(resp.data.json[0].descripcion);
                                        this.nombreNit = null
                                        this.direccionNit = null
                                    }
                                    else if(resp.data.json.length == 0){
                                        this.nombreNit = null
                                        this.direccionNit = null
                                        this.Consulta().MostrarError('No se encontraron datos para el documento ingresado');
                                    }
                                })
        },              
        CargaConsultaLote(){            
            this.Consulta().LimpiarTablas();
            this.ConsultaFacturaLotes(1);
            this.ConsultaFacturaLotes(2);
            this.ConsultaFacturaLotes(3);
            this.ConsultaFacturaLotes(4);
            this.ConsultaFacturaLotes(5);            
        },
        async GeneracionLote(){
            if(!this.permisos.facturar){
                this.Consulta().MostrarError('No cuenta con permiso para la generación de lotes.');
                return
            }

            this.Consulta().LimpiarTablas();
            let respuesta = await this.GeneraFacturaLotes(1);
            if(respuesta == 1){
                this.GeneraFacturaLotes(2);
                this.habilitarFactura = true;                
                this.habilitarExportar = true;
            }else{
                this.habilitarFactura = false;
                this.habilitarRecibo = false;
                this.habilitarExportar = false;
            }
        },
        async BusquedaFacturaPorLote(){
            this.dataSource.lotesFacturasDataSource = await this.ConsultaBusquedaFacturasGeneradas()
            this.popUpBusquedaFacturaXLote = true;
        },
        SeleccionarFacturaLote(lote){
            this.consultaLotes.serieFel = lote.data.SerieFactura,
            this.consultaLotes.factura  = lote.data.Factura,
            this.popUpBusquedaFacturaXLote = false
            this.CargaConsultaLote()
        },
        ConvertirEntero(Valor){
            let conversion = parseInt(Valor)
            if(isNaN(conversion)){
                return 0
            }
            return conversion;
        },
        ConvertirFloat(Valor,Decimales=2){
            let conversion = parseFloat(Valor?Valor:0.00)
            if(isNaN(conversion)){
                return 0.00
            }
            return parseFloat(conversion.toFixed(Decimales))
        },
        Consulta(){
            return {               
                MostrarError:(mensaje,tiempo=2000)=>{
                    this.$vs.notify({
                        time:tiempo,
                        title: 'Cajero',
                        color: 'danger',
                        text:mensaje,
                        position: 'top-center'
                    })
                },
                MostrarSatisfactorio:(mensaje)=>{
                    this.$vs.notify({
                        time:2000,
                        title: 'Cajero',
                        color: 'success',
                        text:mensaje,
                        position: 'top-center'
                    })
                },
                LimpiarTablas:() => {
                     this.montoTotalFacturacion = null
                     this.recibo.saldoRecibo = null
                     this.recibo.saldoReciboInicial = null
                     this.dataSource.lotesFacturas=[]
                     this.dataSource.cargosAdmision=[]
                     this.dataSource.cargosDetalle=[]
                     this.dataSource.saldosAdmision=[]
                     this.dataSource.loteAdmision=[]
                     this.dataSource.detalleFactura=[]
                   }
                }
        },
        ObtenerError(mensaje){
            let error = '';
            if(mensaje.json && mensaje.json.length > 0){
                error = mensaje.json[0].Message ??  
                        mensaje.json[0].descripcion.slice(0,mensaje.json[0].descripcion.length>400?400:mensaje.json[0].descripcion.length)
            }else{
                error = mensaje.descripcion.Message ??  
                        mensaje.descripcion.slice(0,mensaje.descripcion.length>400?400:mensaje.descripcion.length)
            }
            return error;
        },       
        async ConsultaBusquedaFacturasGeneradas(bloqueNum=0){
            let respuesta = await
            this.axios.post('/app/v1_caja/ListaDocsLote',
                                { 
                                  docFiscal:0, //0 es para documentos facturas
                                  bloqueNum,
                                  tipoSasi:this.consultaLotes.tipoSasi ?? 0,
                                  proceso: 2
                                }
                            )
                       .then(resp=>{
                            if(resp.data.codigo != 0){
                                this.Consulta().MostrarError(this.ObtenerError(resp.data),5000);
                                return [];
                            }
                            return resp.data.json;
                            //this.dataSource.lotesFacturas = resp.data.json;
                        });
            return respuesta;
        },
        ConsultaFacturaLotes(proceso){                       

            this.axios.post('/app/v1_caja/GeneraFacturaLotes',
                                { serieFel:this.consultaLotes.serieFel,
                                  factura: this.ConvertirEntero(this.consultaLotes.factura),
                                  tipoSasi:this.consultaLotes.tipoSasi ?? 0,
                                  proceso
                            })
                          .then(resp=>{
                            if(resp.data.codigo != 0){
                                this.Consulta().MostrarError(this.ObtenerError(resp.data),5000);
                                return;
                            }

                            if(proceso == 1){
                                this.dataSource.cargosAdmision = resp.data.json
                                this.calcularTotalAdmision(this.dataSource.cargosAdmision);

                                if(this.dataSource.cargosAdmision.length > 0){
                                    this.habilitarRecibo = true;
                                    this.habilitarExportar = true;
                                }else{
                                    this.habilitarRecibo = false;
                                    this.habilitarExportar = false;
                                }
                            }
                            else if(proceso == 2)
                                this.dataSource.cargosDetalle = resp.data.json
                            else if(proceso == 3){
                                this.dataSource.saldosAdmision = resp.data.json
                                this.recibo.saldoRecibo = this.dataSource.saldosAdmision.reduce((total, cargo)=>total+Number(cargo.SaldoAdmision),0) 
                                this.recibo.saldoReciboInicial = this.recibo.saldoRecibo
                            }
                            else if(proceso == 4)
                                this.dataSource.loteAdmision = resp.data.json
                            else if(proceso == 5)
                                this.dataSource.detalleFactura = resp.data.json

                          }).catch((ex)=>{                            
                            this.Consulta().MostrarError(this.ObtenerError(ex),5000);
                          })
        },
        async GeneraFacturaLotes(proceso){

            if(!this.permisos.facturar && proceso == 6){
                this.Consulta().MostrarError('No cuenta con permiso para facturar');
                return
            }
            
            if(!this.configuracion.cajaSeleccionada.SerieFac){
                this.Consulta().MostrarError('Configure un cajero para iniciar la facturación',5000);
                return
            }
            if(!this.consultaLotes.fechaInicial || !this.consultaLotes.fechaFinal){
                this.Consulta().MostrarError('Seleccione la Fecha Inicial y la  Fecha Final para la generación del lote',5000);
                return
            }

            const botonFacturar = document.getElementById("botonFacturar");
            if(botonFacturar) botonFacturar.blur();
            
            let respuesta =  this.axios.post('/app/v1_caja/GeneraFacturaLotes',
                                { serieFel:this.configuracion.cajaSeleccionada.SerieFac,
                                  fechaInicial: this.GetDateValue(this.consultaLotes.fechaInicial),
                                  fechaFinal: this.GetDateValue(this.consultaLotes.fechaFinal),
                                  fechaFactura: this.GetDateValue(this.parseDate(this.consultaLotes.fechaDocumento,'DD/MM/YYYY')),
                                  tipoSasi:this.consultaLotes.tipoSasi ?? 0,
                                  factura: this.ConvertirEntero(this.consultaLotes.factura),
                                  nit: this.numeroNit,
                                  proceso
                            })
                          .then(resp=>{
                            if(resp.data.codigo != 0){
                                this.Consulta().MostrarError(this.ObtenerError(resp.data),5000);
                                return;
                            }

                            if((proceso == 1 || proceso == 2) && resp.data.json.length <= 0){
                                this.Consulta().MostrarError('No se encontraron admisiones en el rango especificado',5000);
                                this.calcularTotalAdmision([]);
                                return;
                            }

                            if(proceso == 1){
                                this.dataSource.cargosAdmision = resp.data.json
                                this.calcularTotalAdmision(this.dataSource.cargosAdmision);
                                return 1;
                            }
                            else if(proceso == 2){
                                this.dataSource.cargosDetalle = resp.data.json
                                return 1;
                            }
                            else if(proceso == 6){
                                var datosFactura = resp.data.descripcion.split(':')[1].split('-');
                                this.consultaLotes.serieFel = datosFactura[0].trim();
                                this.consultaLotes.factura = datosFactura[1].trim();
                                this.Fel(this.consultaLotes.serieFel,this.consultaLotes.factura)
                                this.CargaConsultaLote();                                
                            }
                          }).catch((ex)=>{                            
                            this.Consulta().MostrarError(this.ObtenerError(ex),5000);     
                            return                       
                          })
            return respuesta;
        }
    },
    computed:{
        sesion() {
            return this.$store.state.sesion
        }
    }
}
</script>    
<style scoped>
    #detail .vs-col {
        padding: 5px;
        border-color: black;
        border-style: solid;
        border-width: 1px;
    }

    [dir=ltr] .dx-datagrid-group-closed::before,
    [dir=ltr] .dx-datagrid-group-opened::before {
        font-size: 40px !important;
        color: #337ab7;
    }
    .ajustar-texto-botton{
        margin: 5px 5px 5px 0px;        
        font-size: 12px;
        width:auto;
        height:45px;
        text-align:center;
    }
    .ajustar-texto-label{
        font-size: 12px;
        height:10px;
        width:auto;
        color:black;
        text-align:center;
    }
    .label-total
    {
        font-size: 25px;
        color:black;
        text-align: center;
    }

    .centered-element {
        margin: 0;
        top: 50%;
        transform: translateY(-50%);
    }

    .multiselect, .multiselect__input, .multiselect__single{
        height: 10px !important;        
        z-index: 500;
    }

    .main-expediente {
        display: grid;
        height: calc(100vh - 49px - 67px);
        grid-template-areas: "Encabezado Encabezado""Opciones Contenido";
        grid-template-columns: 0fr 1fr;
        grid-template-rows: 85px 1fr;
        margin: 0;
        padding: 0;
        right: 0;
        background-color: rgb(255, 255, 255);
        position: fixed;
        left: 80px;
        width: calc(100%-80px);
        min-width: 400px;
    }

    .Encabezado {
        grid-area: Encabezado;
        overflow-y: hidden;
        font-size: small;
        /* background-color: #d0e1f9; */
        background: rgb(208, 225, 249);
        background: linear-gradient(90deg, rgba(208, 225, 249, 1) 67%, rgba(119, 175, 255, 1) 94%, rgba(2, 0, 36, 1) 100%);
        color: #2f496e;
    }

    .Contenido {
        grid-area: Contenido;
        overflow-y: auto;
    }

    .Opciones {
        grid-area: Opciones;
        overflow-y: auto;
        overflow-x: hidden;
        width: max-content;
    }

    .Opciones>#toolbar {
        background-color: #f4eade;
        background-color: #2988bc;
    }

    #view {
        margin-left: 10px;
        margin-top: 10px;
    }
    </style><style>
    .dx-popup-title {
        background-color: #f4eade !important;
        color: #2f496e !important;
    }

    .dx-list-item>#itemsMenuDrawer {
        color: #2988bc !important;
        background-color: #f4eade !important;
    }

    .dx-list-item-selected>#itemsMenuDrawer {
        color: #f4eade !important;
        background-color: #ed8c72 !important;
    }

    .dx-scrollable-container {
        touch-action: auto !important;
    }

    /*Ancho mínmo de los grid*/
    #Contenido .dx-datagrid {
        min-width: 302px;
    }

    /**Modal actualizacion de peso y talla */
    #popupTallaPeso .vs-popup {
        width: 400px !important;
    }

    .dx-datagrid-headers td {
        vertical-align: middle !important;
    }

    .dx-resizable {
        display: inline-grid;
    }

    .dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed)>td {
        background-color: #00ccff !important;
        color: black !important;
    }

    .dx-datagrid-rowsview .dx-row.dx-data-row.dx-column-lines.dx-state-hover>td {
        background-color: #79e4ff !important;
        color: black !important;
        font-size: 16px;
    }

    .dx-datagrid-headers {
        background-color: linen !important;
        color: black !important;
        font-weight: bold;
    }

    td {
        vertical-align: middle !important;
    }

    .dx-viewport,
    .dx-device-phone,
    .dx-device-mobile,
    .dx-device-android,
    .dx-device-android-6,
    .dx-theme-generic,
    .dx-theme-generic-typography,
    .dx-color-scheme-light,
    .dx-overlay-wrapper {
        color: #2980B9 !important;
    }

    [dir] .dx-popup-title {
        height: 3em !important;
    }

    .dx-button.dx-button-warning {
        background-color: #e0d100;
    }

    .buttonTab {
        height: 50px;
        width: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 10px !important;
    }

    .buttonTab:hover {
        background-color: blue !important;
    }

    .buttonTabSeleccionada {
        height: 50px;
        width: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 10px !important;
    }

    .buttonTabSeleccionada:hover {
        background-color: transparent !important;
    }

    .sticky {
        position: -webkit-sticky !important;
        /* for browser compatibility */
        position: sticky !important;
        top: 0px;
        z-index: 500;
    }

    .dx-overlay-wrapper.dx-popup-wrapper.dx-dropdowneditor-overlay.dx-popover-wrapper.dx-popover-without-title.dx-lookup-popup-wrapper.dx-lookup-popup-search.dx-position-bottom {
        z-index: 999999 !important;
    }

    .buttons {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
    }

    .button {
        height: 40px;
        width: 200px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 10px !important;
    }

    .button:hover {
        background-color: blue !important;
    }

    .div-button {
        display: flex;
        justify-content: center;
    }

    .stickyIntegracion {
        position: -webkit-sticky !important;
        /* for browser compatibility */
        position: sticky !important;
        top: 0px;
        background-color: rgb(117, 177, 255);
        z-index: 500;
    }

</style>