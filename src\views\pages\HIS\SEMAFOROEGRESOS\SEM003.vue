<template>

  <div id="formRecalculo"  class="sem003-container w-full pr-2">
    <vx-card>
      <vs-row>
        <DxForm :form-data="formData" label-mode="static" width="100%">
          <DxFormGroupItem :col-span="3" :col-count="3">
            <DxFormGroupItem
              :col-span="1"
              :col-count="3"
              caption="Ingrese el criterio de busqueda"
            >
              <!-- <DxFormItem data-field="Hospital" editor-type="dxSelectBox"></DxFormItem> -->
              <DxFormItem
                item-type="dxTextArea"
                data-field="criterioBusqueda"
                :height="500"
                :editor-options="{
                  hint: 'Ingrese admisión o nombres y apellidos del paciente',
                }"
                :col-span="3"
              >
                <DxFormLabel text="Ingrese admisión ó nombres y apellidos" />
              </DxFormItem>
              <DxFormItem
                item-type="button"
                :button-options="{
                  ...buttonBusqueda,
                  hint: 'Asigna descuento a la admisión seleccionada',
                }"
                :col-span="1"
              ></DxFormItem>
            </DxFormGroupItem>
          </DxFormGroupItem>
        </DxForm>
      </vs-row>
    </vx-card>
    <vx-card>
      <DxDataGrid
        ref="dataGrid"
        id="gridlistadoadmisiones"
        :data-source="dsListadoAdmisiones"
        @selectionChanged="onSelectionChanged"
        :no-data-text="'Sin registros'"
        height="auto"
        width="auto"
        :headerFilter="{ visible: true, allowSearch: true }"
        :row-alternation-enabled="true"
        :hoverStateEnabled="true"
        :column-hiding-enabled="true"
      >
        <DxDataGridDxSearchPanel :visible="true" width="400px" />
        <DxDataGridGroupPanel :visible="true" expanded="true" />
        <DxDataGridSelection mode="single" />

        <DxDataGridToolbar>
          <DxFormItem name="groupPanel" locateInMenu="auto" />
          <DxFormItem name="searchPanel" locateInMenu="auto" />
        </DxDataGridToolbar>

        <DxDataGridPaging :page-size="10" />
        <DxDataGridPager :visible="true" />
        <DxDataGridColumn data-field="serie" caption="Serie" :width="80"></DxDataGridColumn>
        <DxDataGridColumn
          data-field="Admision"
          caption="Admision"
          :width="100"
        ></DxDataGridColumn>
        <DxDataGridColumn
          data-field="statusadmision"
          caption="Status Admision"
          :width="80"
        ></DxDataGridColumn>
        <DxDataGridColumn
          data-field="fechaegreso"
          caption="Fecha Egreso"
          :width="100"
        ></DxDataGridColumn>
        <DxDataGridColumn
          data-field="Paciente"
          caption="Paciente"
          :width="300"
        ></DxDataGridColumn>
        <DxDataGridColumn
          data-field="NombreSeguro"
          caption="Nombre Seguro"
          :width="250"
        ></DxDataGridColumn>
        <DxDataGridColumn
          data-field="NombreAseguradora"
          caption="Nombre Aseguradora"
          :width="250"
        ></DxDataGridColumn>
        <DxDataGridColumn
          data-field="Planxcontrato"
          caption="Plan x Contrato"
          :width="200"
        ></DxDataGridColumn>
      </DxDataGrid>
    </vx-card>
    <dx-popup
      :visible.sync="popupCalculoCobro"
      :show-title="true"
      title="Resumen para cobro"
      width="auto"
      max-width="100%"
      height="auto"
      max-height="100%"
      
    >
      <table style="width: 100%; table-layout: auto">
        <tr>
          <th class="titulo-th" colspan="2">
            Desglose de cobro admisión {{ this.formData.Admision }}
          </th>
        </tr>
        <tr>
          <td style="white-space: nowrap">Copago</td>
          <td>{{ formatCurrency(formData.copago) }}</td>
        </tr>
        <tr>
          <td style="white-space: nowrap">Coaseguro</td>
          <td>{{ formatCurrency(formData.coaseguro) }}</td>
        </tr>
        <tr>
          <td style="white-space: nowrap">Deducible</td>
          <td>{{ formatCurrency(formData.deducible) }}</td>
        </tr>
        <tr>
          <td style="white-space: nowrap">Gastos especiales no cubiertos</td>
          <td>{{ formatCurrency(formData.gastosEspeciales) }}</td>
        </tr>
        <tr>
          <td style="white-space: nowrap">Extraordinarios</td>
          <td>{{ formatCurrency(formData.extraordinarios) }}</td>
        </tr>
        <tr>
          <td style="white-space: nowrap">Cuenta ajena no elegible</td>
          <td>{{ formatCurrency(formData.ctaAjeno) }}</td>
        </tr>
        <tr>
          <td style="white-space: nowrap">Facturado o cobrado en cuenta</td>
          <td :class="{ rojo: isPositive(formData.Facturado) }">
            {{ formatCurrency(formData.Facturado) }}
          </td>
        </tr>
        <tr>
          <td style="white-space: nowrap">
            Descuento {{ this.formData.nombreDescuento }}
          </td>
          <td :class="{ rojo: isPositive(formData.totalDescuento) }">
            {{ formatCurrency(formData.totalDescuento) }}
          </td>
        </tr>
        <tr>
          <td style="white-space: nowrap">Total a cobrar</td>
          <td style="font-weight: bold">
            --{{ formatCurrency(formData.totalCobrar) }}--
          </td>
        </tr>
      </table>
      <p style="text-align: center">
        <strong>{{ "--" + this.formData.observaciones + "--" }}</strong>
      </p>
      <div style="margin-top: 20px; display: flex; justify-content: center">
        <div class="btn-group pl-4">
          <vs-button
            class="w-full"
            color="success"
            type="filled"
            icon-pack="fas"
            icon="fa-save"
            @click="InsertarCalculoCobro()"
            title="Graba el dato para cobro con los datos generados en pantalla"
          >
            Guardar
          </vs-button>
        </div>
        <div class="btn-group pl-4">
          <vs-button
            class="w-full"
            color="danger"
            type="filled"
            icon-pack="fas"
            icon="fa-times"
            @click="popupCalculoCobro = false"
            title="Cancelar la solicitud"
          >
            Cancelar
          </vs-button>
        </div>
      </div>
    </dx-popup>
    <dx-popup
      :visible.sync="popupCalculo"
      :show-title="true"
      title="Recalculo de cuentas"
      :width="'80%'"
      :height="'90%'"
     class="custom-popup"
     :resizeEnabled="true"
      
      :hide-on-outside-click="true"
        @hidden="OctultadoPopup"
    >
      <DxScrollView width="100%" height="100%">
        <CalculoForm ref="refCalculoForm" />
      </DxScrollView>
    </dx-popup>
  </div>
</template>

<script>
import CalculoForm from "./SEM004.vue";

import VxCard from "../../../../components/vx-card/VxCard.vue";

import { DefaultDxGridConfiguration } from "../../EXPEDIENTE/data.js";
import CustomStore from "devextreme/data/custom_store";
import esMessages from "devextreme/localization/messages/es.json";
import { locale, loadMessages } from "devextreme/localization";
import { CustomDevExtremeEsMessages } from "../../EXPEDIENTE/data.js";

export default {
  name: "SemaforoRecalculo",
  components: {   
    VxCard,
    CalculoForm,
    //componentes grid
  },
  data() {
    return {
      DefaultDxGridConfiguration,
      buttonObtenerDescuento: {
        text: "Obtener Descuento",
        type: "success",
        onClick: () => this.Descuentos("I"),
        icon: "add",
        //   disabled: this.obtenerDescuento
      },
      buttonBusqueda: {
        text: "Buscar",
        type: "success",
        onClick: () => this.ListadoAdmisiones(),
        icon: "refresh",
        //   disabled: this.obtenerDescuento
      },
      buttonElminardescuento: {
        width: "150",
        text: "Eliminar Descuento",
        type: "default",
        icon: "minus",

        onClick: () => this.Descuentos("E"),
        stylingMode: "contained",
        elementAttr: {
          style: "white-space: pre-wrap; text-align: center;",
        },
      },
      buttonCalcularDescuento: {
        text: "Calcular",
        type: "default",
        icon: "minus",
        onClick: this.handleButtonClick,
      },

      buttonGrabarCambios: {
        text: "Grabar",
        type: "default",
        icon: "floppy",

        onClick: this.GrabarCambios,
        hint: "Grabar cambios",
        width: "200",
      },
      buttonGrabarCalcular: {
        text: "Calcular",
        type: "default",
        icon: "money",
        onClick: this.CalculoCobro,
        hint: "Calcular cobro a paciente",
        width: "200",
      },
      buttonGrabarActualizar: {
        text: "Actualizar",
        type: "default",
        icon: "refresh",
        onClick: this.ListadoAdmisiones,
        width: "200",

        hint: "Actualizar información",
      },
      buttonGrabarReCalcular: {
        text: "Re-Calcular",
        type: "default",
        icon: "revert",
        onClick: this.handleButtonClick,
        hint: "Abrir ventana para recalcular cuenta",
      },
      Areas: {
        dataSource: [],
        displayExpr: "Area",
        valueExpr: "Id",
        searchEnabled: true,
      },
      notificationOptions: {
        time: 4000,
        title: "Semaforo egresos",
        iconPack: "feather",
        icon: "icon-alert-circle",
        position: "top-center",
      },

      formData: {
        deducible: 0.0,
        copago: 0.0,
        gastosEspeciales: 0,
        Facturado: 0.0,
        coaseguro: 0.0,
        facturado: 0.0,
        extraordinarios: 0.0,
        ctaAjeno: 0.0,
        totalCobrar: 0.0,
        Hospital: "",
        Servicio: "",
        Paciente: "",
        Seguro: "",
        Admision: "",
        SerieAdmision: "",
        noAdmision: 0,
        tipoDescuento: "",
        descuento: 0.0,
        totalDescuento: 0,
        noAutorizacion: 0,
        observaciones: "",
        tipoAdmision: "",
        nombreDescuento: "",
        criterioBusqueda: "",
      }, //dat
      Cobro: [
        {
          deducible: "",
          copago: "",
          gastosEspeciales: "",
          coaseguro: "",
          facturado: "",
          extraordinarios: "",
          ctaAjeno: "",
          totalCobrar: "",
          cargosHospital: "",
          descuento: "",
          montoPago: "",
          CtaAjenaNoElegible: "",
        },
      ],

      positionEditorOptions: {
        items: this.dsDescuentos,
        searchEnabled: true,
        value: "",
      },
      observacionesAdmision: "Notas para la admisión",
      ListaCuentasx: new CustomStore({
        key: "ID",
        load: () => {
          return this.ListaCuentas;
        },
      }),
      dsCobros: [
        {
          deducible: "",
          copago: "",
          gastosEspeciales: "",
          coaseguro: "",
          facturado: "",
          extraordinarios: "",
          ctaAjeno: "",
          totalCobrar: "",
          cargosHospital: "",
          descuento: "",
          montoPago: "",
          ultimaActualizacion: "",
        },
      ],
      dsDescuentos: [],
      selectedDescuento: null,
      dsAreas: [],
      dsListadoAdmisiones: [],
      obtenerDescuento: true,
      eliminarDescuento: true,
      popupCalculoCobro: false,
      popupCalculo: false,
      AdmisionesObservacion: [],
      opcionRecalculo:"0",

      currencyOptions: {
        format: "Q ###,##0.##",

        //   {
        //   type: "custom",
        //   formatter: function (value) {
        //     // Formatear el valor como moneda y reemplazar 'GTQ' por 'Q'
        //     let formattedValue = new Intl.NumberFormat('en-GT', {
        //       style: 'currency',
        //       currency: 'GTQ',
        //       currencyDisplay: 'symbol'
        //     }).format(value);

        //     // Reemplazar 'GTQ' por 'Q'
        //     return formattedValue.replace('GTQ', 'Q');
        //     //return  this.$formato_moneda(value)
        //   },
        // },
        showClearButton: true,
        onValueChanged: function (e) {
          if (e.value === null || e.value < 0) {
            e.component.option("value", 0);
          }
        },
        min: 0,
      },
      PorcentajeOptions: {
        format: "#0%",
        showClearButton: true,
        onValueChanged: this.handleValueChanged,
      },
      currentDate: new Date().toLocaleDateString(),

      currentTime: "",
    }; //return
  },

  methods: 
  {
    OctultadoPopup(){
      this.$refs["dataGrid"].instance.deselectAll();
   },
    formatCurrency(value) {
      if (!value) return "0";
      value = value.toString();
      return "Q" + value.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    isPositive(value) {
      return parseFloat(value) > 0;
    },

    ListadoAdmisiones() {
      this.resetFormData();
      this.Cobro = [{}];
      if (this.formData.criterioBusqueda === "") {
        this.$vs.notify({
          time: 4000,
          title: "Falta información",
          text: "Favor ingrese el criterio de busqueda",
          iconPack: "feather",
          icon: "icon-alert-circle",
          color: "warning",
          position: "top-center",
        });
        return;
      }
      this.axios
        .post(
          "/app/v1_SemaforoEgresos/BusquedaRecalculo",
          {
            CriterioBusqueda: this.formData.criterioBusqueda,
          },
          {
            headers: {
              "Content-Type": "application/json; charset=UTF-8",
            },
          }
        )
        .then((resp) => {
          this.dsListadoAdmisiones = resp.data;
     
          this.$refs["dataGrid"].instance.deselectAll();
          this.AdmisionesObservacion = [];
        });
    }, //lista admisiones

    resetFormData() {
      (this.formData.deducible = 0),
        (this.formData.copago = 0),
        (this.formData.gastosEspeciales = 0),
        (this.formData.Facturado = 0),
        (this.formData.coaseguro = 0),
        (this.formData.facturado = 0),
        (this.formData.extraordinarios = 0),
        (this.formData.ctaAjeno = 0),
        (this.formData.totalCobrar = 0),
        (this.formData.Paciente = ""),
        (this.formData.Seguro = ""),
        (this.formData.Admision = ""),
        (this.formData.SerieAdmision = ""),
        (this.formData.noAdmision = 0),
        (this.formData.tipoDescuento = ""),
        (this.formData.descuento = 0),
        (this.formData.totalDescuento = 0),
        (this.formData.noAutorizacion = 0),
        (this.formData.observaciones = ""),
        (this.formData.tipoAdmision = "");
      this.formData.nombreDescuento = "";
    }, //resetformdata



    handleButtonClick() {
      alert("Button clicked!");
    },
    calcular() {
      // Lógica para calcular
    },
    guardarCambios() {
      // Lógica para guardar cambios
    },
    actualizar() {
      // Lógica para actualizar
    },
    DisplayExpression(e) {
      return this.$saltos_tab_to_html(e.value);
    },
    observacionCellTemplate(cellElement, cellInfo) {
      const formattedValue = cellInfo.value
        .replace(/\t/g, "   ") // Reemplaza tabuladores por tres espacios
        .replace(/\n/g, "<br>"); // Reemplaza saltos de línea por <br>
      cellElement.innerHTML = `<span class="observacion-text">${formattedValue}</span>`;
    },

    onSelectionChanged(selectedItems) {
      const CalculoForm = this.$refs.refCalculoForm;
      CalculoForm.resetCalculoCobro();
      CalculoForm.resetFormData();

      if (selectedItems.selectedRowsData.length > 0) {
        this.popupCalculo = true;

        const Seleccion = selectedItems.selectedRowsData[0];
        CalculoForm.formData.Paciente = Seleccion.Paciente;
        CalculoForm.formData.Seguro = Seleccion.NombreSeguro;
        CalculoForm.formData.Admision =
          Seleccion.serie + "-" + Seleccion.Admision;
        CalculoForm.formData.noAdmision = Seleccion.Admision;
        CalculoForm.formData.SerieAdmision = Seleccion.serie;
        CalculoForm.formData.tipoAdmision = Seleccion.TipoAdmision;
        CalculoForm.MostrarobservacionesAdmision();
        CalculoForm.CalcularCuentaRecalculo(Seleccion.serie,Seleccion.Admision)
        CalculoForm.opcionRecalculo=1;
       this.ValidarCuenta(Seleccion.serie, Seleccion.Admision);
 
        }

      },
   

   
    ValidarCuenta(SerieAdmision, Admision) {
      this.axios
        .post("/app/v1_SemaforoEgresos/ValidarCuenta", {
          SerieAdmision: SerieAdmision,
          Admision: Admision,
        })
        .then((resp) => {
          const CalculoForm = this.$refs.refCalculoForm;
          
          this.formData.nombreDescuento = "";
          if (resp.data) {
            CalculoForm.formData.totalDescuento = resp.data[0].BeneficioTerceros;
       
            if (resp.data[0].idTipoDescuento === -1) {
           
              CalculoForm.eliminarDescuento = true;
              CalculoForm.obtenerDescuento = false;
            } else {
              
              CalculoForm.formData.nombreDescuento = resp.data[0].TipoDescuento;
              CalculoForm.eliminarDescuento = false;
              CalculoForm.obtenerDescuento = true;
            }

         
          }

          if (resp.data && resp.data[0].ctaCalculada === 1) {
            (this.Cobro = [
              {
                deducible: "",
                copago: "",
                gastosEspeciales: "",
                coaseguro: "",
                facturado: "",
                extraordinarios: "",
                ctaAjeno: "",
                totalCobrar: "",
                cargosHospital: "",
                descuento: "",
                montoPago: "",
                CtaAjenaNoElegible: "",
              },
            ]),
              this.$vs.notify({
                time: 4000,
                title:
                  "Admisión " +
                  SerieAdmision +
                  "-" +
                  Admision +
                  " Ya fue calculada",
                text: "La cuenta seleccionada ya fue calculada",
                iconPack: "feather",
                icon: "icon-alert-circle",
                color: "warning",
                position: "top-center",
              });
            this.dsListadoAdmisiones = this.dsListadoAdmisiones.filter(
              (record) =>
                record.serie !== SerieAdmision || record.Admision !== Admision
            );
          } else {
            CalculoForm.CalcularCuentaRecalculo(SerieAdmision, Admision);
          }
        })

    },

    CalcularCuenta(SerieAdmision, Admision) {
      this.axios
        .post("/app/v1_SemaforoEgresos/ConsultaCuentaRecalculo", {
          SerieAdmision: SerieAdmision,
          Admision: Admision,
        })
        .then((resp) => {
          if (resp.data.json && resp.data.json.length > 0) {
            this.Cobro = resp.data.json.map((x, index) => {
              return {
                id: index,
                cargosHospital: x.CargosHospital,
                copago: x.Copago,
                coaseguro: x.Coaseguro,
                Hora: x.Hora,
                Copago: x.Copago,
                montoPago: x.MontoPago,
                descuento: x.descuento,
                porcentajedescuento: x.porcentajedescuento,
                extraordinarios: x.ExtraOrdinarios,
              };
            });
            this.formData.extraordinarios = this.Cobro[0].extraordinarios;
            this.formData.ctaAjeno = this.Cobro[0].ctaAjeno;
            this.formData.totalCobrar = this.Cobro[0].montoPago;
          }

          this.updateTime();
        });
    },
    Descuentos(X) {
      if (X === "I") {
        if (this.formData.descuento <= 0) {
          this.$vs.notify({
            time: 4000,
            title: "Notificación descuentos",
            text: "El descuento seleccionado es '0%' no se puede continuar",
            iconPack: "feather",
            icon: "icon-alert-circle",
            color: "warning",
            position: "top-center",
          });
          return; // Salir de la función si el descuento es 0 o menor
        }
      }

      if (X === "E") {
        this.formData.tipoDescuento = 0;
      }
      this.axios
        .post("/app/v1_SemaforoEgresos/Descuentos", {
          admision: this.formData.noAdmision,
          serieAdmision: this.formData.SerieAdmision,
          copago: this.formData.copago,
          coaseguro: this.formData.coaseguro,
          deducible: this.formData.deducible,
          gastosnocubiertos: this.formData.gastosEspeciales,
          autorizacion: this.formData.noAutorizacion,
          idDescuento: this.formData.tipoDescuento,
          opcion: X,
        })
        .then((response) => {
          const data = response.data[0];
          const notificationOptions = {
            time: 4000,
            title: "Notificación descuentos",
            iconPack: "feather",
            icon: "icon-alert-circle",
            position: "top-center",
          };

          if (data.codigo === 0) {
            this.formData.descuento = null;
            this.$vs.notify({
              ...notificationOptions,
              text: `${data.descripcion} por un monto de: Q${data.descuento}`,
              color: "success",
            });
            //eliminar descuento
            if (X === "E") {
              this.obtenerDescuento = false;
              this.eliminarDescuento = true;
              this.formData.totalDescuento = 0;
            }

            // otener descuento
            if (X === "I") {
              this.obtenerDescuento = true;
              this.eliminarDescuento = false;
              this.formData.totalDescuento = data.descuento;
            }
          } else {
            this.$vs.notify({
              ...notificationOptions,
              text: data.descripcion,
              color: "warning",
            });
          }

          this.updateTime();
        })

    },

    porcentajedescuento() {
      if (
        (this.formData.tipoAdmision !== "") &
        (this.formData.tipoDescuento != "")
      ) {
        this.axios
          .post("/app/v1_SemaforoEgresos/PorcentajeDescuento", {
            tipoAdmision: this.formData.tipoAdmision,
            idDescuento: this.formData.tipoDescuento,
          })
          .then((resp) => {
            this.formData.descuento = resp.data[0].PorcentajeDescuento;
            
          });
      }
    },

  
    updateTime() {
      const now = new Date();
      const hours = String(now.getHours()).padStart(2, "0");
      const minutes = String(now.getMinutes()).padStart(2, "0");
      const seconds = String(now.getSeconds()).padStart(2, "0");
      this.currentTime = `${hours}:${minutes}:${seconds}`;
     
    },
   
    handleValueChanged(e) {
      if (e.value === null) {
        this.formData.tipoDescuento = "";
      }
    },
    CalculoCobro() {
      const notificationOptions = {
        time: 4000,
        title: "Notificación calculo admisión",
        iconPack: "feather",
        icon: "icon-alert-circle",
        position: "top-center",
      };

      if (this.formData.SerieAdmision === "") {
        this.$vs.notify({
          ...notificationOptions,
          text: "No ha seleccionado una admisión",
          color: "warning",
        });
        return;
      }
      const sumatoria =
        this.formData.coaseguro +
        this.formData.copago +
        this.formData.deducible +
        this.formData.gastosEspeciales +
        this.formData.extraordinarios +
        this.formData.ctaAjeno -
        (this.formData.Facturado + this.formData.totalDescuento);
      
      this.formData.totalCobrar = sumatoria;
      if (this.formData.totalCobrar > this.Cobro[0].cargosHospital) {
        this.$vs.notify({
          ...notificationOptions,
          text: "El calculo realizado, supera el valor total de cargos",
          color: "warning",
        });
        return;
      }
    },

    GrabarCambios() {
      const notificationOptions = {
        time: 4000,
        title: "Notificación calculo admisión",
        iconPack: "feather",
        icon: "icon-alert-circle",
        position: "top-center",
      };

      if (this.formData.SerieAdmision === "") {
        this.$vs.notify({
          ...notificationOptions,
          text: "No ha seleccionado una admisión",
          color: "warning",
        });
        return;
      }
      this.CalculoCobro();

      this.popupCalculoCobro = true;
    },
   
    InsertarCalculoCobro() {
      const notificationOptions = {
        time: 4000,
        title: "Notificación Calculo admisión",
        iconPack: "feather",
        icon: "icon-alert-circle",
        position: "top-center",
      };
      if (this.formData.SerieAdmision === "") {
        this.$vs.notify({
          ...notificationOptions,
          text: "No ha seleccionado una admisión",
          color: "warning",
        });
        return;
      }
      this.axios
        .post("/app/v1_SemaforoEgresos/InsertarCalculo", {
          Copago: this.formData.copago,
          Coaseguro: this.formData.coaseguro,
          Deducible: this.formData.deducible,
          Gastosnocubiertos: this.formData.gastosEspeciales,
          ExtraOrdinarios: this.formData.extraordinarios,
          CtaAjena: this.formData.ctaAjeno,
          Facturado: this.formData.facturado,
          Descuento: this.formData.totalDescuento,
          Total: this.formData.totalCobrar,
          Comentario: this.formData.observaciones,
          SerieAdmision: this.formData.SerieAdmision,
          Admision: this.formData.noAdmision,
          NombreDescuento: this.formData.nombreDescuento,
          Recalculo: 1,
        })
        .then((response) => {
          const data = response.data[0];

          if (data.codigo === 0) {
            this.$vs.notify({
              ...notificationOptions,
              text: `${data.descripcion}`,
              color: "success",
            });

            const index = this.dsListadoAdmisiones.findIndex(
              (item) =>
                item.Admision === this.formData.noAdmision &&
                item.serie === this.formData.SerieAdmision
            );

            if (index !== -1) {
              this.dsListadoAdmisiones.splice(index, 1);
              this.key++; // Para forzar la actualización del DataGrid
              this.popupCalculoCobro = false;
              this.AdmisionesObservacion = [];
            }
          } else {
            this.$vs.notify({
              ...notificationOptions,
              text: data.descripcion,
              color: "warning",
            });
          }
        })
   
    },
  
  cellTemplate(cellElement, cellInfo) {
    cellElement.innerHTML = cellInfo.value.replace(/\n/g, "<br>");
  },
},
  watch: {
    NumeroAdmision(newVal) {
      if (newVal != undefined) this.calcular();
    },
    "formData.tipoDescuento"(newVal) {
      if (newVal != undefined) this.porcentajedescuento();
    },

  },
  computed: {
    datosParaCobroCaption() {
      const { Paciente, Seguro, Admision } = this.formData;
      if (Paciente || Seguro || Admision) {
        return `Datos para cobro:\nPaciente: ${Paciente}\nSeguro: ${Seguro}\nAdmision: ${Admision}`;
      }
      return "Datos para cobro";
    },
    captionHtml() {
      const { Paciente, Seguro, Admision } = this.formData;
      if (Paciente || Seguro || Admision) {
        return `Datos para cobro:<br>Paciente: ${Paciente}<br>Seguro: ${Seguro}<br>Admision: <span style="color: blue;">${Admision}</span>`;
      }
      return "Datos para cobro";
    },
  },
  created() {
    loadMessages(esMessages);
    loadMessages({
      es: CustomDevExtremeEsMessages,
    });
    locale(navigator.language);
  },
};
</script>
<style>
.sem003-container .dx-field-item-content {
  padding: 0px;
  margin-bottom: 3px;
  border: 2px;
}

.sem003-container .dx-widget {
  line-height: 1.1;
}
[dir] .sem003-container .dx-form-group-with-caption > .dx-form-group-content {
  padding-top: 3px;
  margin-top: 6px;
  border-top: 1px solid #ddd;
  padding-bottom: 7px;
}
.sem003-container .dx-datagrid-rowsview
  .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed)
  > td {
  background-color: #00ccff !important;
  color: black !important;
}

.sem003-container .dx-datagrid-rowsview .dx-row.dx-data-row.dx-column-lines.dx-state-hover > td {
  background-color: #79e4ff !important;
  color: black !important;
  font-size: 16px;
}

.sem003-container .dx-datagrid-headers {
  background-color: #008fbe !important;
  color: rgb(252, 255, 254) !important;
  font-weight: bold;
}




.sem003-container .dx-popup.custom-popup  {
  background-color: #01836d !important;
  color: #f3f4f7 !important;
}


</style>
