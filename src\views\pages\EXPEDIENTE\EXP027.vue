<template>
<div>
    <!-- Panel para ingreso o consulta -->
    <DxTabPanel :height="'100%'" :data-source="tabs" @selection-changed="onSelectionChanged">
        <template #title="{ data: tab }">
            <span>{{ tab.name }}</span>
        </template>
        <template #item="{ data: tab }">

            <div v-if="tab.value === 1">
                <vs-col vs-w="12" vs-justify="center" vs-align="center" class="pl-2 pr-2 pt-2">

                    <!-- Panel de SOP -->
                    <vx-card>
                        <DxTabPanel id="tabIngresoID" ref="tabIngreso" :repaint-changes-only="true" :height="'100%'" :data-source="tabsSOP" @selection-changed="onSelectionChangedIngreso" :selected-index="indiceIngreso" :showNavButtons="true">
                            <template #title="{ data: tab }">
                                <span>
                                    <font-awesome-icon class="pr-1" :icon="['fas', tab.icon]" />
                                    {{ tab.name }}
                                </span>
                            </template>
                            <template #item="{ data: tab }">
                                <div>
                                    <div v-if="tab.value === 1">
                                        <AnestesiaPre v-bind="$props" :hojaAnestesia="''" :consulta="false" @ingreso="getMounted" />
                                    </div>
                                    <div v-if="tab.value === 2">
                                        <AnestesiaDT ref="DTConsulta" v-bind="$props" :hojaAnestesia="''" :consulta="false" @anestesiaEncabezado="setAnestesiaEncabezado" @ingreso="getMounted" />
                                    </div>

                                    <!-- Panel de registro -->
                                    <div v-if="tab.value === 3">
                                        <vx-card>
                                            <vs-row class="pb-2">
                                                <vs-col vs-type="flex" vs-justify="flex-end">
                                                    <DxButton id="botonBarra" text="Órdenes" icon="fas fa-file-prescription" hint="Agregar órdenes" @click="() => { modalOrdenes = true }" />
                                                </vs-col>
                                            </vs-row>

                                            <DxTabPanel ref="tabRegistro" :height="'100%'" :data-source="tabRegistro">
                                                <template #title="{ data: tab }">
                                                    <span>
                                                        <font-awesome-icon class="pr-1" :icon="['fas', tab.icon]" />
                                                        {{ tab.name }}
                                                    </span>
                                                </template>
                                                <template #item="{ data: tab }">
                                                    <div>
                                                        <div v-if="tab.value === 1">
                                                            <RegistroAnestesico v-bind="$props" :hojaAnestesia="''" :consulta="false" :registrosAnestesicos="listaRegistrosAnestesicos" :registrosTransoperatorios="registroTransoperatorioDatos" :ordenMedica="ordenMedica.Orden" />
                                                        </div>
                                                        <div v-if="tab.value === 2">
                                                            <RegistroTransoperatorio v-bind="$props" :hojaAnestesia="''" :consulta="false" :registrosTransoperatorios="listaRegistrosTransoperatorios" @registroTransoperatorio="registroTransoperatorio" />
                                                        </div>
                                                    </div>
                                                </template>
                                            </DxTabPanel>
                                        </vx-card>

                                    </div>

                                    <!-- Recuento de materiales -->
                                    <div v-if="tab.value === 4">
                                        <Recuento v-bind="$props" :hojaAnestesia="''" :consulta="false" :registrosRecuento="listaRegistrosRecuento" />
                                    </div>

                                    <!-- Test de aldrete -->
                                    <div v-if="tab.value === 5">
                                        <TestAldrete v-bind="$props" :hojaAnestesia="''" :consulta="false" />
                                    </div>

                                    <!-- Estadística -->
                                    <div v-if="tab.value === 6">
                                        <Estadistica v-bind="$props" ref="EstadisticaTab" :hojaAnestesia="''" :consulta="false" />
                                    </div>

                                    <!-- Record operatorio -->
                                    <div v-if="tab.value === 7">
                                        <RecordOperatorio v-bind="$props" key="Record" ref="RecordTab" :hojaAnestesia="''" :consulta="false" />
                                    </div>
                                </div>

                            </template>
                        </DxTabPanel>
                    </vx-card>
                </vs-col>
            </div>

            <div v-else-if="tab.value === 2" :width="'100%'">
                <vs-col vs-w="2" vs-justify="center" vs-align="center" class="pl-2 pt-2">
                    <vx-card title="Filtrar hoja">
                        <DxDataGrid :ref="dataGridFechaKey" v-bind="DefaultDxGridConfiguration" :headerFilter="{visible:true, allowSearch:true}" :data-source="fechas" :width="'100%'" :height="'100%'" @selection-changed="onSelectionChangedFecha">
                            <DxSelection mode="single" />
                            <DxColumn :width="120" data-field="Fecha" caption="Fecha" format="dd/MM/yyyy HH:mm" data-type="date" alignment="center" />
                            <DxColumn :width="110" data-field="IdHojaAnestesia" caption="Hoja" data-type="string" alignment="center" />
                        </DxDataGrid>
                    </vx-card>
                </vs-col>
                <vs-col vs-w="10" vs-justify="center" vs-align="center" class="pl-2 pr-2 pt-2">
                    <vx-card>
                        <DxTabPanel ref="tabConsulta" :height="'100%'" :data-source="tabsSOPConsulta" :showNavButtons="true" @selection-changed="onSelectionChangedConsulta">
                            <template #title="{ data: tab }">
                                <span>
                                    <font-awesome-icon class="pr-1" :icon="['fas', tab.icon]" />
                                    {{ tab.name }}
                                </span>
                            </template>
                            <template #item="{ data: tab }">
                                <div>
                                    <div v-if="tab.value === 1">
                                        <AnestesiaPre v-bind="$props" :hojaAnestesia="hojaAnestesia" :consulta="true" />
                                    </div>
                                    <div v-if="tab.value === 2">
                                        <AnestesiaDT v-bind="$props" :hojaAnestesia="hojaAnestesia" :consulta="true" />
                                    </div>
                                    <div v-if="tab.value === 3">
                                        <vs-col vs-w="12" vs-justify="center" vs-align="center" class="pl-2 pr-2 pt-2">
                                            <vx-card>
                                                <DxTabPanel ref="tabRegistro" :height="'100%'" :data-source="tabRegistro">
                                                    <template #title="{ data: tab }">
                                                        <span>
                                                            <font-awesome-icon class="pr-1" :icon="['fas', tab.icon]" />
                                                            {{ tab.name }}
                                                        </span>
                                                    </template>
                                                    <template #item="{ data: tab }">
                                                        <div>
                                                            <div v-if="tab.value === 1">
                                                                <RegistroAnestesico v-bind="$props" :hojaAnestesia="hojaAnestesia" :consulta="true" :registrosAnestesicos="listaRegistrosAnestesicos" />
                                                            </div>
                                                            <div v-if="tab.value === 2">
                                                                <RegistroTransoperatorio v-bind="$props" :hojaAnestesia="hojaAnestesia" :consulta="true" :registrosTransoperatorios="listaRegistrosTransoperatorios" />
                                                            </div>
                                                        </div>
                                                    </template>
                                                </DxTabPanel>
                                            </vx-card>
                                        </vs-col>
                                    </div>
                                    <div v-if="tab.value === 4">
                                        <Recuento v-bind="$props" :hojaAnestesia="hojaAnestesia" :consulta="true" :registrosRecuento="listaRegistrosRecuento" />
                                    </div>
                                    <div v-if="tab.value === 5">
                                        <TestAldrete v-bind="$props" :hojaAnestesia="hojaAnestesia" :consulta="true" />
                                    </div>

                                    <div v-if="tab.value === 6">
                                        <RecordOperatorio ref="RecordConsulta" v-bind="$props" :hojaAnestesia="hojaAnestesia" :consulta="true" />
                                    </div>
                                </div>

                            </template>
                        </DxTabPanel>
                    </vx-card>
                </vs-col>
            </div>
        </template>
    </DxTabPanel>
    <OrdenPopup :visible.sync="modalOrdenes" v-bind="popupConf" :width="'80%'" :height="'80%'" title="Agregar órdenes">
        <template #content>
            <div>
                <div id="detail" style="text-align: center">
                    <b>*Este formulario se guardará automáticamente cuando se ingrese el registro anestésico</b>
                </div>
                <div>
                    <DxForm :form-data.sync="ordenMedica" labelMode="floating">
                        <DxItem data-field="Orden" editor-type="dxTextArea" :editor-options="{ height: 500, }">
                            <DxLabel :text="'Descripción de órden'" />
                        </DxItem>
                        <DxGroupItem :col-count="2" item-type="group">
                            <DxButtonItem :col-span="3" :button-options="plantillaButtonOptions" horizontal-alignment="center" verical-alignment="center" />
                            <DxButtonItem :col-span="3" :button-options="limpiarButtonOptions" horizontal-alignment="center" verical-alignment="center" />
                        </DxGroupItem>
                    </DxForm>
                </div>
            </div>
        </template>
    </OrdenPopup>
</div>
</template>

<script>
import 'devextreme-vue/lookup'
import DxTabPanel from 'devextreme-vue/tab-panel';
import {
    DxDataGrid,
    DxColumn,
    DxSelection
} from 'devextreme-vue/data-grid'
import {
    DefaultDxGridConfiguration,
    popupConf
} from './data'
import {
    DxPopup as OrdenPopup
} from 'devextreme-vue/popup'
import DxButton from 'devextreme-vue/button'
import {
    DxForm,
    DxItem,
    DxLabel,
    DxGroupItem,
    DxButtonItem
} from 'devextreme-vue/form'
import {
    ObtenerPlantilla
} from './data'

const dataGridFechaKey = 'gridFiltro'
const tabPanelInsertar = 'tabIngreso'

export default {
    name: 'SalaOperaciones',
    components: {
        DxForm,
        DxItem,
        DxLabel,
        DxGroupItem,
        DxButtonItem,
        DxTabPanel,
        DxDataGrid,
        DxColumn,
        DxSelection,
        OrdenPopup,
        DxButton,
        AnestesiaPre: () => import("./EXP027_AnestesiaPre.vue"),
        AnestesiaDT: () => import("./EXP027_AnestesiaDT.vue"),
        RegistroAnestesico: () => import("./EXP027_RegistroAnestesico.vue"),
        RegistroTransoperatorio: () => import("./EXP027_RegistroTransoperatorio.vue"),
        Recuento: () => import("./EXP027_Recuento.vue"),
        TestAldrete: () => import("./EXP027_TestAldrete.vue"),
        Estadistica: () => import("./EXP027_Estadistica.vue"),
        RecordOperatorio: () => import("./EXP027_RecordOperatorio.vue"),
    },
    data() {
        return {
            fechas: [],
            indiceIngreso: 0,
            dataGridFechaKey,
            anestesiaEncabezado: '',
            consulta: false,
            tabs: [{
                name: 'Ingreso',
                value: 1,
            }, {
                name: 'Consulta',
                value: 2
            }, ],
            tabsSOP: [{
                    name: 'Anestesia preoperatoria',
                    value: 1,
                    disabled: false,
                    icon: "bed",
                },
                {
                    name: 'Anestesia datos generales',
                    value: 2,
                    disabled: false,
                    icon: "hospital-user",
                },
                {
                    name: 'Registro',
                    value: 3,
                    disabled: false,
                    icon: "house-medical-circle-check",
                },
                {
                    name: 'Recuento materiales',
                    value: 4,
                    disabled: false,
                    icon: "kit-medical",
                },
                {
                    name: 'Test aldrete',
                    value: 5,
                    disabled: false,
                    icon: "user-clock",
                },
                {
                    name: 'Estadística',
                    value: 6,
                    disabled: false,
                    icon: "arrow-trend-up",
                },
                {
                    name: 'Record operatorio',
                    value: 7,
                    disabled: false,
                    icon: "notes-medical",
                },
            ],
            tabsSOPConsulta: [{
                    name: 'Anestesia preoperatoria',
                    value: 1,
                    disabled: false,
                    icon: "bed",
                },
                {
                    name: 'Anestesia datos generales',
                    value: 2,
                    disabled: false,
                    icon: "hospital-user",
                },
                {
                    name: 'Registro',
                    value: 3,
                    disabled: false,
                    icon: "house-medical-circle-check",
                },
                {
                    name: 'Recuento materiales',
                    value: 4,
                    disabled: false,
                    icon: "kit-medical",
                },
                {
                    name: 'Test aldrete',
                    value: 5,
                    disabled: false,
                    icon: "user-clock",
                },
                {
                    name: 'Record operatorio',
                    value: 6,
                    disabled: false,
                    icon: "notes-medical",
                },
            ],
            tabRegistro: [{
                    name: 'Registro anestésico',
                    value: 1,
                    icon: "house-medical-circle-check",
                },
                {
                    name: 'Registro transoperatorio',
                    value: 2,
                    icon: "heartbeat",
                },
            ],
            DefaultDxGridConfiguration,
            hojaAnestesia: '', //Valor de la hoja seleccionada en la sección de consulta
            idDatosGenerales: '', //Valor que devuelve la pestaña de datos generales en la sección de ingreso al seleccionar el último formulario o si crea uno nuevo
            listaRegistrosAnestesicos: [],
            listaRegistrosTransoperatorios: [],
            listaRegistrosRecuento: [],
            registroTransoperatorioDatos: '',
            idPreoperatoria: '', //Valor obtenido para determinar si la admisión tiene anestesia preoperatoria en el ingreso
            idDT: '', //Valor obtenido para determinar si la admisión tiene datos generales en el ingreso
            modalOrdenes: false, //Variable para mostrar el modal de agregar orden
            plantillaButtonOptions: {
                hint: 'Carga la plantilla de orden de ingreso',
                text: 'Orden de ingreso',
                type: 'normal',
                icon: 'import',
                useSubmitBehavior: false,
                onClick: () => {
                    ObtenerPlantilla('2', om => this.ordenMedica.Orden = om)
                }
            },
            limpiarButtonOptions: {
                hint: 'Elimina todo el texto ingresado en la órden',
                text: 'Limpiar orden',
                type: 'normal',
                icon: 'fas fa-broom',
                useSubmitBehavior: false,
                onClick: () => {
                    this.ordenMedica.Orden = ''
                }
            },
            ordenMedica: {
                Orden: ''
            },
            seleccionoRecordOperatorio: false,
            popupConf,
        }
    },
    props: {
        SerieAdmision: null,
        CodigoAdmision: null,
        NumeroExpediente: null,

        StatusAdmision: null,
    },
    methods: {
        getFechas() {
            if (this.SerieAdmision && this.CodigoAdmision)
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaHojasAnestesicas', {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                })
                .then(resp => {
                    this.fechas = resp.data.json.map((x, index) => {
                        return {
                            id: index,
                            Fecha: x.Fecha,
                            IdHojaAnestesia: x.IdHojaAnestesia,
                            Admision: x.Admision,
                            CodigoPaciente: x.CodigoPaciente,
                            IdHistorial: x.IdHistorial
                        }
                    })
                })
            else {
                this.fechas = [];
                this.refreshFechaDataGrid();
            }
        },
        refreshFechaDataGrid() {
            this.dataGridFecha.pageIndex(0);
            this.dataGridFecha.clearSelection();
            this.dataGridFecha.clearSorting();
        },
        onSelectionChangedFecha(e) {
            if (e.selectedRowsData.length > 0) {
                this.hojaAnestesia = e.selectedRowsData[0].IdHojaAnestesia
                this.getRegistrosAnestesicos()
                this.getRegistrosTransoperatorios()
                this.getRegistrosRecuento()
            }
        },
        onSelectionChangedIngreso(e) { //Método para realizar acciones cuando se seleccionar una pestaña en el panel de Ingreso
            setTimeout(() => {
                this.indiceIngreso = e.addedItems[0].value - 1
                if (e.addedItems[0].value === 2) { //Anestesia datos generales
                    this.$refs.DTConsulta.getMounted();
                } else if (e.addedItems[0].value === 6) //Estadística
                {
                    this.$refs.EstadisticaTab.getEstadistica();
                }

                if (e.addedItems[0].value === 7) {
                    this.$refs.RecordTab.getRecord()
                }
            }, 100);
        },
        onSelectionChangedConsulta(e) { //Método para realizar acciones cuando se seleccionar una pestaña en el panel de Consulta
            setTimeout(() => {
                if (e.addedItems[0].value === 6) {
                    this.$refs.RecordConsulta.getRecord()
                }
            }, 100);
        },
        onSelectionChanged(e) { //Método que valida las acciones al seleccionar Ingreso o Consulta
            setTimeout(() => {
                if (e.addedItems[0].value === 1) {
                    this.consulta = false
                    this.getHojas()
                }
                if (e.addedItems[0].value === 2) {
                    this.getFechas()
                    this.consulta = true
                    for (let i of this.panelInsertar._dataSource._items) {
                        i.disabled = false
                    }
                }
            }, 100);
        },
        setAnestesiaEncabezado(e) {
            this.idDatosGenerales = e;
        },
        getRegistrosAnestesicos() {
            this.axios.post('/app/v1_ExpedienteEvolucion/BuscarIDsRegistroAnestesicoHoja', {
                    Id: this.hojaAnestesia
                })
                .then(resp => {
                    this.listaRegistrosAnestesicos = resp.data.json
                })
        },
        getRegistrosTransoperatorios() {
            this.axios.post('/app/v1_ExpedienteEvolucion/BuscarIDsRegistroTransOperatorioHoja', {
                    Id: this.hojaAnestesia
                })
                .then(resp => {
                    this.listaRegistrosTransoperatorios = resp.data.json
                })
        },
        getRegistrosRecuento() {
            this.axios.post('/app/v1_ExpedienteEvolucion/BuscarIDsRegistroCompresasHoja', {
                    Id: this.hojaAnestesia
                })
                .then(resp => {
                    this.listaRegistrosRecuento = resp.data.json
                })
        },
        registroTransoperatorio(e) {
            this.registroTransoperatorioDatos = e;
        },
        getHojas() {
            this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaHojasAnestesicas', {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.idPreoperatoria = resp.data.json[0].IdHojaAnestesia

                        this.axios.post('/app/v1_ExpedienteEvolucion/BuscarEncRegistrosAnestesicosActual', {
                                SerieAdmision: this.SerieAdmision,
                                Admision: this.CodigoAdmision
                            })
                            .then(resp => {
                                if (resp.data.json.length > 0) {
                                    this.idDT = resp.data.json[0].IdAnestesicoEn
                                } else {
                                    if (!this.consulta) {
                                        for (let i of this.panelInsertar._dataSource._items) {
                                            if (i.value !== 1 && i.value !== 2 && i.value !== 7) {
                                                i.disabled = true
                                            }
                                        }
                                    }
                                }
                            })
                    } else {
                        if (!this.consulta) {
                            for (let i of this.panelInsertar._dataSource._items) {
                                if (i.value !== 1 && i.value !== 7) {
                                    i.disabled = true
                                }
                            }
                        }
                    }
                })
        },
        getMounted(e) { //Método para poder habilitar las demás pestañas de Ingreso al realizar un Registro en Anestesia preoperatorio o Datos generales
            if (e === 1) {
                for (let i of this.tabsSOP) {
                    if (i.value !== 0 || i.value !== 7) {
                        i.disabled = true
                    }
                }
                this.tabsSOP[0].disabled = false
                this.tabsSOP[1].disabled = false
                this.tabsSOP[6].disabled = false
            } else if (e === 2) {
                for (let i of this.tabsSOP) {
                    i.disabled = false
                }
            }
        }
    },
    mounted() {
        this.getFechas();
        this.getHojas();
    },
    computed: {
        dataGridFecha: function () {
            return this.$refs[dataGridFechaKey].instance;
        },

        panelInsertar: function () {
            return this.$refs[tabPanelInsertar].instance;
        },
    },
}
</script>

<style>
#detail {
    padding: 5px;
    border: none;
    color: black;
}
</style>
