
/**************************************************************************************************************************************/
/*******************************************INICIO MODIFICACION DE PAQUETES****************************************************************/
/**************************************************************************************************************************************/
/**************************************************************************************************************************************/
/******************************************* SpHisOpcionesOrdenes ***********************************************************************/
/**************************************************************************************************************************************/
USE [HOSPITAL]
GO


SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/****************************************************************************/
/*	CREADO POR: Javier Castillo	Numero de Rational: Migración								*/
/*	FECHA CREACIÓN: 03/03/2023																							*/
/*	PRODUCTO: ANULACIÓN ORDENES																							*/
/**************************** DESCRIPCION ***********************************/
/*	Este Procedure tiene como objetivo consultar ordenes										*/
/*	segun sea necesario.																										*/
/****************************************************************************/
/*  Se agrego opcion para consulta y modificación de medicos en ordenes     */
-- EXEC SpHisOpcionesOrdenes 'C', 'A', 0, 'MED', '', 'OR1', 2488, 'P,I'
CREATE PROCEDURE [dbo].[SpHisOpcionesOrdenes](
	-- PARAMETROS DEFECTO 
	-- ========================================================== 
	-- C = Consulta 
	-- M = Modificar
	@IOpcion VARCHAR(1), 
	-- G Consulta general para obtener ordenes
	-- A Consulta para anulacion
	-- M Consulta para modificar medico a la orden / Modificar medico a la orden
	@ISubOpcion VARCHAR(2), 
	@IHospital INT = 0,
	@IEmpresa VARCHAR(3) = 'MED',
	@IEmpresaReal varchar(3)='',
	@ITipoOrden VARCHAR(3) = null,
	@ICodigoOrden int =  null,
	@IMedico int = null,
	@IStatus varchar(20)='',
	@ICorporativo int = null,
	@IRazon varchar(40) = null,
	@ITipoModificacion varchar(2) = 'LA',
	@INombrePaciente varchar(30) = NULL,
	@IApellidoPaciente varchar(30) = NULL,
	@ISerie char(1) = null,
	@IAdmision int = null
)
AS

BEGIN
	DECLARE
		@ErrRaise VARCHAR(99) = '',
		@ErrRaiseNum Int = 0,
		@Existe INT = 0,
		@ContadorHonorarios INT = 0;

	SET NOCOUNT ON

			IF @IOpcion = 'C'
				BEGIN
					IF @ISubOpcion = 'G'
					BEGIN
					  SELECT  top 100
										o.Tipo, 
										o.Codigo orden, 
										o.SerieAdmision serie, 
										o.Admision noAdmision, 
										p.Codigo Paciente,
										CONCAT(ltrim(rtrim(p.Nombre)),' ',ltrim(rtrim(p.Apellido)), CASE WHEN IsNull(p.ApellidoCasada,'')='' THEN '' ELSE ' De ' +ltrim(rtrim(p.ApellidoCasada)) END) NombrePaciente,
										p.Nombre nombres,
										p.Apellido apellidos
							FROM Ordenes o inner join Admisiones a 
											ON (o.Empresa = a.Empresa and o.SerieAdmision = a.Serie and o.Admision = a.Codigo)
							INNER JOIN Pacientes p
											ON (a.paciente = p.codigo and a.empresa = p.empresa)
							WHERE o.empresa = @IEmpresa 
								AND ((@ITipoOrden IS NULL OR @ICodigoOrden IS NULL) OR (o.Tipo = @ITipoOrden and  o.Codigo = @ICodigoOrden))
								AND (@INombrePaciente IS NULL OR p.Nombre like '%'+@INombrePaciente+'%')
								AND (@IApellidoPaciente IS NULL OR p.Apellido like '%'+@IApellidoPaciente+'%')
								AND (@ISerie IS NULL or  a.Serie = @ISerie)		
								AND (@IAdmision IS NULL or  a.Codigo = @IAdmision )
					END
					ELSE IF @ISubOpcion = 'A'
						BEGIN
							SELECT o.Tipo, 
										 o.Codigo, 
										 o.SerieAdmision, 
										 o.Admision, 
										 p.Codigo Paciente,
										 CONCAT(ltrim(rtrim(p.Nombre)),' ',ltrim(rtrim(p.Apellido)), CASE WHEN IsNull(p.ApellidoCasada,'')='' THEN '' ELSE ' De ' +ltrim(rtrim(p.ApellidoCasada)) END) NombrePaciente
						  	FROM Ordenes o inner join Admisiones a 
										   ON (o.SerieAdmision = a.Serie and o.Admision = a.Codigo)
								INNER JOIN Pacientes p
											 ON (a.paciente = p.codigo and a.empresa = p.empresa)
								WHERE o.empresa = @IEmpresa and o.Tipo = @ITipoOrden and  o.Codigo = @ICodigoOrden
									AND o.status IN (SELECT item FROM Contadb..SplitStringIndex(@IStatus,','))

						END
					ELSE IF @ISubOpcion = 'M'
						BEGIN
							IF @ITipoOrden = 'AJE'
								BEGIN								
									SELECT @ContadorHonorarios = COUNT(*) FROM His_Honorarios
									 WHERE EmpresaUnificadora = @IEmpresa 
									   and TipoOrden = @ITipoOrden 
									   and Orden = @ICodigoOrden
										 and estado_honorarios <> 'A'

									IF @ContadorHonorarios > 0 
										BEGIN
											SELECT 0 AS codigo, 'La orden cuenta con cargos de honorarios, no se puede modificar el médico' AS descripcion, -1 AS tipo_error											
											RETURN
										END
									
									SELECT @ContadorHonorarios = Count(*)
									  FROM Cargos c Inner Join LotesProntoPagoDetalle d
														ON (d.SerieAdmision = c.SerieAdmision and d.Admision = c.Admision and d.Medico = c.Ajeno)
									 where c.TipoOrden = @ITipoOrden and c.Orden = @ICodigoOrden and c.Empresa = @IEmpresa

									 IF @ContadorHonorarios > 0 
										BEGIN
											SELECT 0 AS codigo, 'Esta orden ya forma parte de un lote de facturas Pronto Pago.' AS descripcion, -1 AS tipo_error											
											RETURN
										END

								END

							SELECT C.TipoOrden Tipo,
										 C.Orden Codigo, 
										 C.Linea,
										 C.SerieAdmision, 
										 C.Admision, 
										 p.Codigo Paciente,
										 CONCAT(ltrim(rtrim(p.Nombre)),' ',ltrim(rtrim(p.Apellido)), CASE WHEN IsNull(p.ApellidoCasada,'')='' THEN '' ELSE ' De ' +ltrim(rtrim(p.ApellidoCasada)) END) NombrePaciente,						  	
										 C.FacRec,
										 O.Medico, 
										 CONCAT(ltrim(rtrim(A.Nombre)),' ',ltrim(rtrim(A.Apellido))) NombreMedico					  	
							  FROM Cargos C left join Pacientes P
											on ( C.Empresa = P.Empresa and C.Paciente = P.Codigo)
								left join ordenes O 
											on ( C.Empresa= O.Empresa and C.TipoOrden = O.Tipo and C.Orden = O.Codigo)
								left join Ajenos A 
											on ( C.Empresa = A.Empresa and O.Medico = A.Codigo)
							 WHERE C.Empresa = @IEmpresa and C.TipoOrden = @ITipoOrden  and C.Orden = @ICodigoOrden
							END
					ELSE
						BEGIN
							SELECT 0 AS codigo, 'No se ha encontrado la opcion' AS descripcion, -1 AS tipo_error	
						END
				END
			ELSE IF @IOpcion = 'M'
				BEGIN
					BEGIN TRAN
					BEGIN TRY
						IF @ISubOpcion = 'M'
							BEGIN
								UPDATE ordenes 
								   SET Medico = @IMedico
								 WHERE Empresa = @IEmpresa and Tipo = @ITipoOrden AND Codigo = @ICodigoOrden

								INSERT INTO Modificaciones(Empresa,Tipo,Serie,Documento,Fecha,Usuario,Observaciones)
								VALUES(@IEmpresa,@ITipoModificacion, @ITipoOrden,@ICodigoOrden,GetDate(),@ICorporativo ,@IRazon)
								COMMIT TRAN
							END
						ELSE
							BEGIN
								SELECT 0 AS codigo, 'No se ha encontrado la opcion' AS descripcion, -1 AS tipo_error;
								ROLLBACK TRAN			
							END
					END TRY
					BEGIN CATCH
						ROLLBACK TRAN
						SET @ErrRaise = ERROR_MESSAGE()
						SET @ErrRaiseNum = ERROR_NUMBER()
						RAISERROR(@ErrRaise, 16, @ErrRaiseNum)
					END CATCH
				END
			ELSE
				BEGIN
					SELECT 0 AS codigo, 'No se ha encontrado la opcion' AS descripcion, -1 AS tipo_error;					
				END
END
GO

/**************************************************************************************************************************************/
/*******************************************FIN MODIFICACION DE PAQUETES*******************************************************************/
/**************************************************************************************************************************************/
