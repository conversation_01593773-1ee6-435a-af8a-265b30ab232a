<template>
    <div class="principal">
        <buscador ref="BusquedaPacientesQx" buscador_titulo="Buscador / Pacientes" :api="'app/v1_caja/ListaPaquetesMed'" 
            :campos="['Id', 'NombrePaciente', 'Dpi','NombreHospital','Paquete']"
            :titulos="['Codigo', 'Nombre Paciente', 'Dpi', 'Hospital','Paquete']"  
            :api_filtro="{'NombrePaciente': paquete.nombreCompleto}"
            :api_filtro_buscar ="false"          
            :multiselect="false" :api_validar_campo="true" 
            :api_reload="true" :api_preload="true" 
            :api_cache="true"  :all_datos="true"/>

        <vx-card :title="`Anticipos Paquetes - ${sesion.sesion_sucursal_nombre}`">    
            <vs-row>
                    <ConfiguracionCaja  ref="ConfiguracionCajaRef"
                                       @CargarCaja="CargarCaja" :FiltrarAgrupacionCaja=FiltrarAgrupacionCaja
                                       TipoCajaSeleccionada="TipoCajaFacturacion" CajaSeleccionada="CajaFacturacion" 
                                       class="w-full">
                    </ConfiguracionCaja>
                    <vs-row class="sm:w-full md:w-full lg:w-full xl:w-full">
                        <vs-row class="w-full">                            
                            <vs-col class="sm:w-full md:w-full lg:w-full xl:w-full">    
                                <vs-col class="sm:w-full md:w-full lg:w-8/12 xl:w-6/12 p-1 flex">
                                        <vs-input label="Cod Paciente Qx" style="min-width:100px" id="numeroPaquete" type="number" v-model="paquete.codigo" :disabled="true"  />
                                        <vs-input label="Nombre" style="min-width:350px" id="nombrePaciente" v-model="paquete.nombreCompleto"  :disabled="true"
                                                v-on:keydown.enter="carga_paciente()"
                                                v-on:keydown.tab.prevent="carga_paciente()"/>
                                        <div class="append-text btn-addon pt-6">
                                            <button type="submit" v-show="false" name="button"></button>
                                            <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="carga_paciente()" icon="fa-search"></vs-button>
                                        </div>
                                </vs-col>
                            </vs-col>                                                
                        </vs-row>
                        <vs-row class="w-full">
                            <vs-col class="w-full"> 
                                <vs-col class="sm:w-full md:w-full lg:w-8/12 xl:w-5/12 p-1" >   
                                    <ValidationProvider  rules="max:100" v-slot="{ errors }">
                                        <vs-textarea type="text" label="Concepto" rows="6" v-model="paquete.concepto" counter="100"  :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"  />
                                    </ValidationProvider>
                                </vs-col>                                        
                            </vs-col>            
                        </vs-row>    
                    </vs-row>                   
                    
                    <vs-row class="pt-1" >
                        <DxDataGrid  ref="recibosFacturacionRef" :data-source="sourceRecibos.recibos" :visible="true"  
                                    v-bind="DefaultDxGridConfiguration"
                                    :hoverStateEnabled="false"
                                    :show-borders="true" :showColumnLines="true" :showBorders="true" 
                                    :rowAlternationEnabled="true" width="100%" height="400px" :column-hiding-enabled="true" 
                                    @editor-preparing="onCellPrepared"
                                    :word-wrap-enabled="false">

                                        <DxDataGridToolbar>                                                 
                                            <DxDataGridItem location="before"
                                                    name="addRowButton"
                                                    showText="always"
                                                    :options="{
                                                        type:'success',
                                                        text:'Nuevo'
                                                    }"
                                            />                                                                                
                                        </DxDataGridToolbar>

                                        <DxDataGridEditing :allow-updating="true" :allow-adding="true" :allow-deleting="true" mode="row" />
                                      
                                        <DxDataGridColumn :width="150" data-field="status" :visible="true" caption="Status" :allow-editing="true" :allow-filtering="false">
                                            <DxDataGridRequiredRule />
                                            <DxDataGridLookup :dataSource="sourceRecibos.status" value-expr="Valor" display-expr="Descripcion" />
                                        </DxDataGridColumn>  
                                        <DxDataGridColumn :width="150" data-field="formaDePago" :set-cell-value="actualizarFormaDePago" :visible="true" caption="Forma Pago" :allow-editing="true" :allow-filtering="false">
                                            <DxDataGridLookup :dataSource="sourceRecibos.formasPago" value-expr="Valor" display-expr="Descripcion" />
                                            <DxDataGridRequiredRule message="La forma de pago es requerida"/>
                                        </DxDataGridColumn>                                              
                                        <DxDataGridColumn :width="200" data-field="monto" caption="Monto"  :editor-options="{format:'Q ##,##0.00'}" format="Q ##,##0.00" data-type="number" alignment="center" />
                                        <DxDataGridColumn :width="300" data-field="banco" :visible="true" caption="Banco" :allow-editing="true" :allow-filtering="false">
                                            <DxCustomRule message="El banco es requerido para Tarjeta y Cheque" type="custom" :validation-callback="validarBanco" :reevaluate="true" />
                                            <DxDataGridLookup :dataSource="sourceRecibos.listaBancos" value-expr="Codigo" :display-expr="(data)=>data.Codigo.trim()+' - '+data.Nombre.trim()" />
                                        </DxDataGridColumn>
                                        <DxDataGridColumn :width="150" data-field="cheque" caption="Cheque" data-type="number" alignment="center" />
                                        <DxDataGridColumn :width="150" data-field="cuenta" caption="Cuenta/Num Tarjeta" alignment="center" />
                                        <DxDataGridColumn :width="150" data-field="autorizacion" caption="Autorización" alignment="center" />
                                        <DxDataGridColumn type="buttons" caption="Acción">
                                            <DxDataGridButton name="save" icon="save" text="Guardar" />
                                            <DxDataGridButton name="cancel" type="danger" icon="feather icon-x" text="Cancelar" />
                                            <DxDataGridButton name="edit" icon="edit" text="Editar"/>
                                            <DxDataGridButton name="delete" type="danger" icon="fas fa-trash" text="Borrar"/>
                                        </DxDataGridColumn>
                                        <DxDataGridSummary>
                                            <DxDataGridTotalItem  column="monto" summary-type="sum" value-format="Q ##,##0.00" data-type="number" display-format="Total: {0}"/>
                                        </DxDataGridSummary>                                      
                            </DxDataGrid>
                    </vs-row>                        
                    <vs-row class = 'w-full pt-10'>
                        <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 m-1">
                            <vs-button color="success" class="w-full" id="botonRecibo"
                                :type="reciboSeleccionado?'border':'filled'" 
                                :disabled="!configuracion.activarRecibos"
                                @keyup.tab="reciboSeleccionado=true"
                                v-on:blur="reciboSeleccionado=false"
                                @click="GrabarRecibosHospital()">
                                Recibo
                            </vs-button>
                        </vs-col>
                        <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 m-1">
                            <vs-button color="warning" class="w-full"
                                @click="LimpiarDatosCliente()">
                                Limpiar
                            </vs-button>
                        </vs-col>
                    </vs-row>        
                    <vs-divider></vs-divider>
                    
            </vs-row>            
        </vx-card>
    </div>
</template>
    
<script>
    import ConfiguracionCaja from "/src/components/sermesa/modules/caja/ConfiguracionCaja.vue";     
    import "vue-multiselect/dist/vue-multiselect.min.css"; 
    import {DefaultDxGridConfiguration } from './data';
    export default {
        components:{
            ConfiguracionCaja                   
        },     
        data() {    
            return {   
                reciboSeleccionado:false,
                FiltrarAgrupacionCaja:['1','5'],   
                DefaultDxGridConfiguration:{...DefaultDxGridConfiguration},   
                configuracion:{
                    cajaSeleccionada:null,
                    activarFacturacion: false,
                    activarRecibos: false                                            
                },       
                paquete:{
                    paciente:null,
                    codigo:null,
                    nombreCompleto:null,
                    concepto:null
                },              
                sourceRecibos:{
                    serieFactura:null,
                    numeroFactura:null,
                    tipoRecibo:null,
                    recibos:[],
                    status:[],
                    formasPago:[],
                    listaBancos:[]
                },	 
                limpiarAdmision: false,   
                GridToolBar: {
                    items: [{
                        name: 'addRowButton',
                        showText: 'always',
                        options: {
                            type: 'success',
                            text: 'Nuevo'
                        }
                    }],
                },
                EditorPopup: {
                    toolbarItems: [{
                            widget: 'dxButton',
                            location: 'after',
                            toolbar: 'bottom',
                            options: {
                                text: 'Guardar Cambios',
                                type: 'default',
                                useSubmitBehavior: true,
                                onClick: () => {
                                    this.recibosFacturacionDataGrid.saveEditData()
                                }
                            }
                        },
                        {
                            widget: 'dxButton',
                            location: 'after',
                            toolbar: 'bottom',
                            options: {
                                text: 'Cancelar',
                                type: 'danger',
                                useSubmitBehavior: true,
                                onClick: () => {
                                    this.recibosFacturacionDataGrid.cancelEditData()
                                }
                            }
                        },
                    ],
                },                     
                money: {
                    decimal: '.',
                    thousands: ',',
                    prefix: 'Q',
                    precision: 2,
                    masked: false
                }            
            }
        },
        computed:{ 
            recibosFacturacionDataGrid(){
                return this.$refs['recibosFacturacionRef'].instance;
            },
            sesion() {
                return this.$store.state.sesion
            }
        },
        watch:{
        },
        methods: {   
            actualizarCajero(){  
                this.$refs.ConfiguracionCajaRef.CargarCajaInicial();
            },
            actualizarFormaDePago(fila,value){
                fila.formaDePago = value
            },
            onCellPrepared(e) {

                if (e.dataField == 'banco' && e.row.data.formaDePago == 'T') {
                    e.editorOptions.dataSource = this.sourceRecibos.listaBancos.filter(banco=>banco.formaDePago=='T')
                }else if (e.dataField == 'banco' && e.row.data.formaDePago == 'B') {
                    e.editorOptions.dataSource = this.sourceRecibos.listaBancos.filter(banco=>banco.formaDePago=='B')
                }else if(e.dataField == 'banco'){
                    e.editorOptions.dataSource = []
                }
            },
            validarBanco(linea){
                let bancoValido = true;
                if( (linea.data.formaDePago == 'T' || linea.data.formaDePago == 'B')){
                    if(!linea.data.banco || linea.data.banco.trim() == ''){
                        bancoValido = false
                    }                    
                }  
                return bancoValido              
            },
            CargarCaja(cajaSeleccionada){                
                this.configuracion.cajaSeleccionada   = cajaSeleccionada.Cajero
                this.configuracion.activarFacturacion = cajaSeleccionada.Factura
                this.configuracion.activarRecibos     = cajaSeleccionada.Recibo
            },
            carga_paciente(){
                /* TODO: agregar llamanda a la busqueda filtrada por nombre */
                this.$refs.BusquedaPacientesQx.iniciar((cliente) => {
                                if(cliente){
                                    this.paquete.paciente = cliente.CodigoPaciente
                                    this.paquete.codigo = cliente.Id
                                    this.paquete.nombreCompleto = cliente.NombrePaciente
                                    this.paquete.concepto = cliente.Concepto
                                }else{
                                    this.paquete.paciente = null
                                    this.paquete.codigo = null
                                    this.paquete.nombreCompleto = null
                                    this.paquete.concepto = null
                                }
                            })

                /*this.axios.post('/app/v1_caja/ListaPaquetesMed',{})
                          .then(resp=>{this.resp.data.json})*/
            }, 
            async GenerarRecibo(Serie,NumeroRecibo) {

                let reporte = "Recibo"

                let postData = {
                    SerieRecibo:   Serie ,
                    NumeroRecibo: NumeroRecibo
                }


                this.$reporte_modal({
                    Nombre: reporte,
                    Opciones: {
                        ...postData
                    },
                    Formato:this.FormatoReporte,
                    NombreArchivoDescargar:Serie+'_'+NumeroRecibo,
                    Descargar:true
                })                

            },  
            GrabarRecibosHospital(){
                /*
                    serieReciboAnt:this.configuracion.cajaSeleccionada.SerieRecibo, 
                    cajaLocal:this.configuracion.cajaSeleccionada.CajaLocal,                                    
                */    
               if(Object.keys(this.configuracion.cajaSeleccionada).length === 0 && this.configuracion.cajaSeleccionada.constructor === Object){          
                    this.Consulta().MostrarError('Seleccione un caja para realizar el recibo');
                    return
               }  

               if(!this.configuracion.cajaSeleccionada.SerieRecibo || this.configuracion.cajaSeleccionada.SerieRecibo.trim().length <= 0){
                    this.Consulta().MostrarError('La caja no cuenta con una serie de recibo valida');
                    return
               }

               if(!this.configuracion.cajaSeleccionada.CajaLocal || this.configuracion.cajaSeleccionada.CajaLocal.trim().length <= 0){
                    this.Consulta().MostrarError('La caja no cuenta con un numero de caja valido');
                    return
               }

               const botonRecibo = document.getElementById("botonRecibo");
               if(botonRecibo) botonRecibo.blur();

                this.axios.post('/app/v1_caja/RecibosAnticipos',
                                {                                  
                                    serieReciboAnt:this.configuracion.cajaSeleccionada.SerieRecibo, 
                                    cajaLocal:this.configuracion.cajaSeleccionada.CajaLocal,
                                    motivoAnticipo:this.paquete.concepto,
                                    paciente:this.paquete.paciente,   
                                    idPaquete:this.paquete.codigo,                                                              
                                    listaRecibos:this.sourceRecibos.recibos                    
                                }).then(resp=>{
                                    if(resp.data.codigo == 0){
                                        this.$vs.dialog({
                                                    type: 'confirm',
                                                    color: 'success',
                                                    title: 'Recibo',
                                                    acceptText: 'Aceptar',
                                                    cancelText: 'Cancelar',
                                                    text: resp.data.descripcion,    
                                                    accept: ()=>{
                                                        this.LimpiarDatosCliente()
                                                    },
                                                    cancel:()=>{
                                                        this.LimpiarDatosCliente()
                                                    },                                           
                                                })
                                        this.GenerarRecibo(resp.data.Serie, resp.data.Recibo);
                                    }
                                });
            },           
            async CargarAgrupaciones(agrupacion, subOpcion=2, tipoPago ='B'){
                await this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision', {
                                    opcion:'C',
                                    subOpcion:subOpcion,
                                    agrupacion,
                                    tipoPago
                                })
                            .then(resp => {                            
                                    if (resp.data && resp.data.json.length > 0){
                                        if(agrupacion == 'StatusDePago'){
                                            this.sourceRecibos.status = resp.data.json;
                                        }
                                        if(agrupacion == 'FormasDePago'){
                                            this.sourceRecibos.formasPago = resp.data.json;
                                        }
                                        if(agrupacion == 'Bancos'){
                                            resp.data.json.map(banco=>banco.formaDePago=tipoPago)
                                            if(this.sourceRecibos.listaBancos && this.sourceRecibos.listaBancos.length>0){                                                
                                                this.sourceRecibos.listaBancos.push(...resp.data.json)                                                
                                            }else{
                                                this.sourceRecibos.listaBancos = resp.data.json;
                                            }
                                        }
                                    }
                                }
                            )
            },   
            LimpiarDatosCliente(){
                this.paquete.paciente = null
                this.paquete.codigo = null
                this.paquete.nombreCompleto = null
                this.paquete.concepto = null
                this.sourceRecibos.recibos = []               
            },
            Consulta() {
                return {                  
                    init: async () => {
                        this.LimpiarDatosCliente();
                        await this.CargarAgrupaciones('StatusDePago');
                        await this.CargarAgrupaciones('FormasDePago');
                        await this.CargarAgrupaciones('Bancos',4,'B');
                        await this.CargarAgrupaciones('Bancos',4,'T');
                    },
                    EsVacia:(variable)=>{
                        return variable?false:true;
                    },
                    MostrarError:(mensaje,tiempo=2000)=>{
                        this.$vs.notify({
                            time:tiempo,
                            title: 'Cajero',
                            color: 'danger',
                            text:mensaje,
                            position: 'top-center'
                        })
                    },
                    MostrarSatisfactorio:(mensaje)=>{
                        this.$vs.notify({
                            time:2000,
                            title: 'Cajero',
                            color: 'success',
                            text:mensaje,
                            position: 'top-center'
                        })
                    }
                }
            },
        },
        mounted() {
            this.Consulta().init()                     
        },
        activated() {
            window.addEventListener('focus', this.actualizarCajero);   
        },
        deactivated() {
            window.removeEventListener('focus',this.actualizarCajero);
        },
    }
    </script>
    <style>
        /* Estilo tablas dev express */
        .dx-popup-title {
            background-color: #f4eade !important;
            color: #2f496e !important;
        }

        .dx-list-item>#itemsMenuDrawer {
            color: #2988bc !important;
            background-color: #f4eade !important;
        }

        .dx-list-item-selected>#itemsMenuDrawer {
            color: #f4eade !important;
            background-color: #ed8c72 !important;
        }

        .dx-scrollable-container {
            touch-action: auto !important;
        }

        /*Ancho mínmo de los grid*/
        #Contenido .dx-datagrid {
            min-width: 302px;
        }

        /**Modal actualizacion de peso y talla */
        #popupTallaPeso .vs-popup {
            width: 400px !important;
        }

        .dx-datagrid-headers td {
            vertical-align: middle !important;
        }

        .dx-resizable {
            display: inline-grid;
        }

        .dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed)>td {
            background-color: #00ccff !important;
            color: black !important;
        }

        .dx-datagrid-rowsview .dx-row.dx-data-row.dx-column-lines.dx-state-hover>td {
            background-color: #79e4ff !important;
            color: black !important;
            font-size: 16px;
        }

        .dx-datagrid-headers {
            background-color: linen !important;
            color: black !important;
            font-weight: bold;
        }

        td {
            vertical-align: middle !important;
        }
        /* Fin Estilo tablas dev express */
    </style>
    <style scoped>
    .fuente-label {
        font-size: 10pt;
        white-space: nowrap;
        color: red;
    }
    .radio-button-group {
        display: flex;
        justify-content: left;
        align-items: center;
        height: 100%;
    }

    .cantidad button {
        height: 25px;
        width: 30px;
        border: 1px solid #ccc;
        border-radius: 5px;
    
    }
    
    .cantidad div {
        display: inline-block;
        height: 25px;
        width: 30px;
        text-align: center;
    }

    .quitar_espacio {
        padding: 0;
        margin: 0;        
    }

    .quitar_espacio_buscar > .label{
        padding: 0 !important;
    }

    .money-input{
        white-space: nowrap;
        color: #008FBE;
        border-radius: 5px;
        height: 36px;
    }

    .money-input:focus{
        border:1px solid #008FBE!important;
    }

    .panel-cajero-elegir{
        background-color: rgba(var(--vs-danger), 1) !important; 
    }

    .panel-cajero {
        display:inline-block;
        border-radius: 25px;
        background-color: rgba(var(--vs-primary), 1);
        color: white;
        padding: 5px 65px 5px 10px;
        position: relative;
        width: auto;
        /* top: 12px; */
        overflow: hidden;
        /* margin-right: -14px; */
        font-size: 13px;
        /* right: 50px; */
        /* border-radius: 30px 0 0 0; */
        /* box-shadow: inset 0 2px 0px 1px rgba(0, 0, 0, 0.2); */
        cursor: pointer;
    }

    .seleccionTipoCaja .vs-popup {
        width: 250px !important;
    }
    
    .label-shrink{
        display: block;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        width: 100%;
    }
    
    

    .dx-button.dx-button-warning {
        background-color: #e0d100;
    }

    .buttonTab {
        height: 50px;
        width: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 10px !important;
    }

    .buttonTab:hover {
        background-color: blue !important;
    }

    .buttonTabSeleccionada {
        height: 50px;
        width: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 10px !important;
    }

    .buttonTabSeleccionada:hover {
        background-color: transparent !important;
    }

    .sticky {
        position: -webkit-sticky !important;
        /* for browser compatibility */
        position: sticky !important;
        top: 0px;
        z-index: 500;
    }

    .dx-overlay-wrapper.dx-popup-wrapper.dx-dropdowneditor-overlay.dx-popover-wrapper.dx-popover-without-title.dx-lookup-popup-wrapper.dx-lookup-popup-search.dx-position-bottom {
        z-index: 999999 !important;
    }

    .buttons {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
    }

    .button {
        height: 40px;
        width: 200px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 10px !important;
    }

    .button:hover {
        background-color: blue !important;
    }

    .div-button {
        display: flex;
        justify-content: center;
    }

    .stickyIntegracion {
        position: -webkit-sticky !important;
        /* for browser compatibility */
        position: sticky !important;
        top: 0px;
        background-color: rgb(117, 177, 255);
        z-index: 500;
    }
    </style>    