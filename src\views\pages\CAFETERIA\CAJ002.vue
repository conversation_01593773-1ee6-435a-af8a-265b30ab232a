<template>
    <div>
        <vs-popup :title="tituloCorrelativoFactura" :active.sync="isCorrelativoFactura" style="z-index:99998;">
            <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                <div class="flex flex-wrap">
                    <div class="w-full">
                        <div class="flex flex-wrap">
                            <div class="default-input">
                                <ValidationProvider name="serie" rules="required|max:3" class="required" v-slot="{ errors }">
                                    <label class="typo_label">Serie</label>
                                    <vs-input class="d-inline-block" v-model="serie" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="isSerieFactura"/>
                                </ValidationProvider>
                            </div>
                            <div class="default-input siguiente-input">
                                <ValidationProvider name="siguiente" rules="required|max:5" class="required" v-slot="{ errors }">
                                    <label class="typo_label">Siguiente</label>
                                    <vs-input  class="d-inline-block" v-model="siguiente" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"/>
                                </ValidationProvider>
                            </div>
                            
                        </div>
                        <div class="flex flex-wrap">
                            <div class="multi-span">
                                <ValidationProvider name="hospital" rules="required" class="required" v-slot="{ errors }">
                                    
                                    <label class="typo_label">Hospital</label>
                                    <multiselect v-model="selectHospital" :options="listadoHospitalFacturas" placeholder="Seleccione Hospital" track-by="Codigo" :show-labels="false" :custom-label="DatosMostrarHospitalFacturas"></multiselect>
                                    <span style="position:relative;top:-2px" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                                </ValidationProvider>
                            </div>
                            <div class="multi-span">
                                <ValidationProvider name="facturaIgss" rules="required" class="required" v-slot="{ errors }">
                                    
                                    <label class="typo_label">Factura IGSS</label>
                                    <multiselect v-model="selectFacturaIgss" :options="listadoEsIgss" placeholder="Es Factura IGSS" :show-labels="false" track-by="Codigo" label="Nombre"></multiselect>
                                    <span style="position:relative;top:-2px" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                                </ValidationProvider>
                            </div>
                            <div class="multi-span">
                                <ValidationProvider name="facturaCambiaria" rules="required" class="required" v-slot="{ errors }">
                                    
                                    <label class="typo_label">Factura Cambiaria</label>
                                    <multiselect v-model="selectFacturaCambiaria" :options="listadoFacturaCambiaria" placeholder="Es Factura Cambiaria" :show-labels="false" track-by="Codigo" label="Nombre"></multiselect>
                                    <span style="position:relative;top:-2px" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                                </ValidationProvider>
                            </div>
                            <div class="multi-span">
                                <ValidationProvider name="serie" rules="required|max:15" class="required" v-slot="{ errors }">
                                    <label class="typo_label">Serie GFACE</label>
                                    <vs-input class="d-inline-block" v-model="serieGface" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"/>
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap">
                            <div>
                                <vs-button @click="handleSubmit(GuardarCorrelativoFactura)" :disabled="invalid" color="success" class="btn btn-primary mx-3 my-3" icon-pack="fas" icon="fa-save">
                                    Guardar
                                </vs-button>
                            </div>
                            <div>
                                <vs-row vs-justify="center">
                                    <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="6">
                                        <vs-card>
                                            <div slot="header">
                                                <h2 class="h2-leyenda">Nota</h2>
                                            </div>
                                            <div class="span-leyenda">
                                                <span>
                                                    1. EsFacturaIGSS: Si la serie Factura es para la facturación del IGSS NO Hemodialisis 
                                                    seleccione I. Si la facturación es para IGSS Hemodialisis es H de lo contrario 
                                                    seleccione N no deje en blanco la casilla. En el campo Hospital seleccione la sucursal.
                                                </span>
                                                <br>
                                                <span>
                                                    2. EsFacturaCambiaria: Si es Factura Cambiaria seleccione True, 
                                                    de lo contrario seleccione False
                                                </span>
                                                <br>
                                                <span>
                                                    3. SerieGFACE: Debes ingresar la serie copia de Factura Electronica 
                                                    en formato CFACE - segun Guatefacturas.
                                                </span>
                                            </div>
                                        </vs-card>
                                    </vs-col>
                                </vs-row>
                            </div>
                        </div>

                    </div>
                </div>

            </ValidationObserver>
        </vs-popup>
        <vs-popup :title="tituloCorrelativoRecibo" :active.sync="isCorrelativoRecibo" style="z-index:99998;">
            <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                <div class="flex flex-wrap">
                    <div class="w-full">
                        <div class="flex flex-wrap">
                            <div class="default-input">
                                <ValidationProvider name="serieRecibo" rules="required|max:3" class="required" v-slot="{ errors }">
                                    <label class="typo_label">Serie</label>
                                    <vs-input class="d-inline-block" v-model="serieRecibo" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="isSerieRecibo"/>
                                </ValidationProvider>
                            </div>
                            <div class="default-input">
                                <ValidationProvider name="siguienteRecibo" rules="required|max:5" class="required" v-slot="{ errors }">
                                    <label class="typo_label">Siguiente</label>
                                    <vs-input class="d-inline-block" v-model="siguienteRecibo" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"/>
                                </ValidationProvider>
                            </div>
                            <div class="multi-span">
                                <ValidationProvider name="selectHospitalRecibo" rules="required" class="required" v-slot="{ errors }">
                                    
                                    <label class="typo_label">Hospital</label>
                                    <multiselect v-model="selectHospitalRecibo" :options="listadoHospitalRecibos" placeholder="Seleccione Hospital" track-by="Codigo" :show-labels="false" :custom-label="DatosMostrarHospitalRecibos"></multiselect>
                                    <span style="position:relative;top:-2px" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap">
                            <div class="multi-span">
                                <ValidationProvider name="ReciboIgss" rules="required" class="required" v-slot="{ errors }">
                                    
                                    <label class="typo_label">Factura IGSS</label>
                                    <multiselect v-model="selectReciboIgss" :options="listadoEsIgss" placeholder="Es Factura IGSS" :show-labels="false" track-by="Codigo" label="Nombre"></multiselect>
                                    <span style="position:relative;top:-2px" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap mensaje">
                            <div>
                                <vs-button @click="handleSubmit(GuardarCorrelativoRecibo)" :disabled="invalid" color="success" class="btn btn-primary mx-3 my-3" icon-pack="fas" icon="fa-save">
                                    Guardar
                                </vs-button>
                            </div>
                            <div>
                                <vs-row vs-justify="center">
                                    <vs-col type="flex" vs-justify="center" vs-align="center" vs-w="6">
                                        <vs-card>
                                            <div slot="header">
                                                <h2 class="h2-leyenda">Nota</h2>
                                            </div>
                                            <div class="span-leyenda">
                                                <span>
                                                    Si la Serie de Recibo es para Admisiones del IGSS No Hemodialisis seleccione I.
                                                </span>
                                                <br>
                                                <span>
                                                    Si es para Admisiones Hemodialisis IGSS seleccione H de lo contrario coloque N no deje en blanco la casilla
                                                </span>
                                                <br>
                                                <span>
                                                    En el campo Hospital seleccione las siglas de la Sucursal
                                                </span>
                                            </div>
                                        </vs-card>
                                    </vs-col>
                                </vs-row>
                            </div>
                        </div>
                    </div>
                </div>

            </ValidationObserver>
        </vs-popup>
        <vs-popup :title="tituloCorrelativoNotasCredito" :active.sync="isCorrelativoNotasCredito" style="z-index:99998;">
            <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                <div class="flex flex-wrap">
                    <div class="w-full">
                        <div class="flex flex-wrap">
                            <div class="default-input">
                                <ValidationProvider name="serieNotasCredito" rules="required|max:3" class="required" v-slot="{ errors }">
                                    <label class="typo_label">Serie</label>
                                    <vs-input class="d-inline-block" v-model="serieNotasCredito" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="isSerieNotasCredito"/>
                                </ValidationProvider>
                            </div>
                            <div class="default-input">
                                <ValidationProvider name="siguienteNotasCredito" rules="required|max:5" class="required" v-slot="{ errors }">
                                    <label class="typo_label">Siguiente</label>
                                    <vs-input class="d-inline-block" v-model="siguienteNotasCredito" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"/>
                                </ValidationProvider>
                            </div>
                            <div class="default-input">
                                <ValidationProvider name="establecimientoSat" rules="required|max:1" class="required" v-slot="{ errors }">
                                    <label class="typo_label">Establecimiento SAT</label>
                                    <vs-input  class="d-inline-block" v-model="establecimientoSat" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" :disabled="isSerieNotasCredito"/>
                                </ValidationProvider>
                            </div>
                        </div>
                        <div class="flex flex-wrap">
                            <div class="default-input">
                                <ValidationProvider name="numeroCaja" rules="required|max:1" class="required" v-slot="{ errors }">
                                    <label class="typo_label">Numero Caja</label>
                                    <vs-input class="d-inline-block" v-model="numeroCaja" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"/>
                                </ValidationProvider>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex flex-wrap">
                    <vs-button @click="handleSubmit(GuardarCorrelativoNotasCredito)" :disabled="invalid" color="success" class="btn btn-primary mx-3 my-3" icon-pack="fas" icon="fa-save">
                        Guardar
                    </vs-button>
                </div>
            </ValidationObserver>
        </vs-popup>
        
        <vs-tabs>
            <vs-tab label="Facturas"  icon-pack="fas" icon="fa-file-invoice-dollar">
                <vx-card title="Facturas">
                    <div>
                        <vs-button @click="NuevoCorrelativoFactura" color="success" icon-pack="fas" icon="fa-plus-circle" >Nuevo Correlativo</vs-button>
                    </div>
                    <vs-divider border-style="solid" color="dark"></vs-divider>
                    <div class="con-tab-ejemplo">
                        <vs-table2 max-items="10" search filter pagination :data="tablaCorrelativoFacturas" noSelectText>
                            <template slot="thead">
                                <th width="100px" filtro="Serie">Serie</th>
                                <th width="100px">Siguiente</th>
                                <th width="150px" filtro="NombreCorto">Hospital</th>
                                <th width="100px" filtro="EsFacturaIGSS">EsFacturaIGSS</th>
                                <th width="100px" filtro="EsFacturaCambiaria">EsFacturaCambiaria</th>
                                <th width="100px">SerieGFACE</th>
                                <th width="100px">Editar</th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data" :data="tr">
                                    <vs-td2 width="100px">{{ tr.Serie }}</vs-td2>
                                    <vs-td2 width="100px">{{ tr.Siguiente }}</vs-td2>
                                    <vs-td2 width="150px">{{ tr.NombreCorto }}</vs-td2>
                                    <vs-td2 width="100px">{{ tr.EsFacturaIGSS }}</vs-td2>
                                    <vs-td2 width="100px">{{ tr.EsFacturaCambiaria }}</vs-td2>
                                    <vs-td2 width="100px">{{ tr.SerieGFACE }}</vs-td2>
                                    <vs-td2 class="th_editar">
                                        <vs-button @click="EditarCorrelativoFacturas(tr)" color="primary" size="small" icon-pack="fas" icon="fa-edit" class="btn_editar mr-1"></vs-button>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>

                </vx-card>
            </vs-tab>
            <vs-tab label="Recibos" icon-pack="fas" icon="fa-receipt">
                <vx-card title="Recibos">
                    <div>
                        <vs-button @click="NuevoCorrelativoRecibos" color="success" icon-pack="fas" icon="fa-plus-circle" >Nuevo Correlativo</vs-button>
                    </div>
                    <vs-divider border-style="solid" color="dark"></vs-divider>
                    <div class="con-tab-ejemplo">
                        <vs-table2 max-items="10" search filter pagination :data="tablaCorrelativoRecibos">
                            <template slot="thead">
                                <th width="100px">Serie</th>
                                <th width="100px">Siguiente</th>
                                <th width="100px" filtro="NombreCorto">Hospital</th>
                                <th width="100px" filtro="EsReciboIGSS">EsReciboIGSS</th>
                                <th width="100px">Editar</th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data" :data="tr">
                                    <vs-td2 width="100px">{{ tr.Serie }}</vs-td2>
                                    <vs-td2 width="100px">{{ tr.Siguiente }}</vs-td2>
                                    <vs-td2 width="100px">{{ tr.NombreCorto }}</vs-td2>
                                    <vs-td2 width="100px">{{ tr.EsReciboIGSS }}</vs-td2>
                                    <vs-td2 class="th_editar">
                                        <vs-button @click="EditarCorrelativoRecibo(tr)" color="primary" size="small" icon-pack="fas" icon="fa-edit" class="btn_editar mr-1"></vs-button>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
    
                </vx-card>
            </vs-tab>
            <vs-tab label="Notas Crédito" icon-pack="fas" icon="fa-file-invoice">
                <vx-card title="Notas Crédito">
                    <div>
                        <vs-button @click="NuevoCorrelativoNotasCredito" color="success" icon-pack="fas" icon="fa-plus-circle" >Nuevo Correlativo</vs-button>
                    </div>
                    <vs-divider border-style="solid" color="dark"></vs-divider>
                    <div class="con-tab-ejemplo">
                        <vs-table2 max-items="10" search filter pagination :data="tablaCorrelativoNotasCredito">
                            <template slot="thead">
                                <th width="100px">Serie</th>
                                <th width="100px">SiguienteNota</th>
                                <th width="100px" filtro="EstablecimientoSAT">EstablecimientoSAT</th>
                                <th width="100px">NumeroCaja</th>
                                <th width="100px">Editar</th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data" :data="tr">
                                    <vs-td2 width="100px">{{ tr.Serie }}</vs-td2>
                                    <vs-td2 width="100px">{{ tr.SiguienteNota }}</vs-td2>
                                    <vs-td2 width="100px">{{ tr.EstablecimientoSAT }}</vs-td2>
                                    <vs-td2 width="100px">{{ tr.NumeroCaja }}</vs-td2>
                                    <vs-td2 class="th_editar">
                                        <vs-button @click="EditarCorrelativoNotasCredito(tr)" color="primary" size="small" icon-pack="fas" icon="fa-edit" class="btn_editar mr-1"></vs-button>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                </vx-card>
            </vs-tab>
        </vs-tabs>
    </div>
</template>

<script>
    import Multiselect from "vue-multiselect"
    import "vue-multiselect/dist/vue-multiselect.min.css"

    export default {
        data() {
            return{
                tablaCorrelativoFacturas: [],
                isCorrelativoFactura: false,
                tituloCorrelativoFactura: '',
                isSerieFactura: false,
                serie: '',
                siguiente: '',
                listadoHospitalFacturas: [],
                selectHospital: '',
                listadoFacturaCambiaria: [
                    {
                        Codigo: '0',
                        Nombre: 'falso'
                    },
                    {
                        Codigo: '1',
                        Nombre: 'verdadero'
                    }
                ],
                selectFacturaCambiaria: '',
                listadoEsIgss: [
                {
                        Codigo: 'S',
                        Nombre: 'Si'
                    },
                    {
                        Codigo: 'N',
                        Nombre: 'No'
                    },
                    {
                        Codigo: 'I',
                        Nombre: 'No Hemodialisis'
                    },
                    {
                        Codigo: 'H',
                        Nombre: 'Hemodialisis'
                    }
                ],
                selectFacturaIgss: '',
                serieGface: '',
                /* Parte de correlativo recibos */
                tablaCorrelativoRecibos: [],
                isCorrelativoRecibo: false,
                tituloCorrelativoRecibo: '',
                isSerieRecibo: false,
                serieRecibo: '',
                siguienteRecibo: '',
                listadoHospitalRecibos: [],
                selectHospitalRecibo: '',
                selectReciboIgss: '',

                /* Parte de correlativo notas credito */
                tablaCorrelativoNotasCredito: [],
                isCorrelativoNotasCredito: false,
                tituloCorrelativoNotasCredito: '',
                isSerieNotasCredito: false,
                serieNotasCredito: '',
                siguienteNotasCredito: '',
                establecimientoSat: '',
                numeroCaja: ''
            }
        },
        components: {
            Multiselect
        },
        methods: {
            DatosMostrarHospitalFacturas({
                Codigo,
                NombreCorto
            }){
                return Codigo ? ` ${Codigo} - ${NombreCorto} ` : ''
            },
            ObtenerCorrelativoFaturas(){
                this.axios.post('/app/v1_JefeCaja/ObtenerCorrelativoFacturas', {
                    "Opcion": "C",
                    "SubOpcion": "1",
                    "Hospital": "0",
                    "empresa": this.$store.state.sesion.sesion_empresa
                })
                .then(resp => {
                    this.tablaCorrelativoFacturas = resp.data.json.map(m => {
                        return {
                            ...m
                        }
                    })
                })
            },
            ObtenerHospitalesFactuas(){
                this.axios.post('/app/v1_JefeCaja/ObtenerHospitalesFacturas', {
                    "Opcion": "C",
                    "SubOpcion": "2",
                    "Hospital": "0",
                    "empresa": this.$store.state.sesion.sesion_empresa
                })
                .then(resp => {
                    this.listadoHospitalFacturas = resp.data.json.map(m => {
                        return {
                            ...m
                        }
                    })
                })
            },
            NuevoCorrelativoFactura(){
                this.tituloCorrelativoFactura = 'Nuevo Correlativo Factura'
                this.isSerieFactura = false
                this.isCorrelativoFactura = true
                this.ObtenerHospitalesFactuas()
            },
            EditarCorrelativoFacturas(item){
                this.ObtenerHospitalesFactuas()
                // console.log(item)
                this.serie = item.Serie
                this.siguiente = item.Siguiente
                
                this.selectHospital = {
                    Codigo: item.Codigo,
                    NombreCorto: item.NombreCorto
                }
                this.selectFacturaCambiaria = {
                    Codigo: item.EsFacturaCambiaria ? '1' : '0',
                    Nombre: item.EsFacturaCambiaria ? 'true' : 'false'
                }
                this.selectFacturaIgss = {
                    Codigo: item.EsFacturaIGSS,
                    Nombre: item.EsFacturaIGSS == 'N' ? 'No' : item.EsFacturaIGSS == 'S' ? 'Si' : item.EsFacturaIGSS == 'I' ? 'No Hemodialisis' : 'Hemodialisis'
                }
                this.serieGface = item.SerieGFACE
                this.tituloCorrelativoFactura = 'Modificar Correlativo Factura'
                this.isSerieFactura = true
                this.isCorrelativoFactura = true

                
            },
            GuardarCorrelativoFactura(){
                if(this.isSerieFactura){
                    this.axios.post('/app/v1_JefeCaja/ActualizarCorrelativoFacturas', {
                        "opcion": "A",
                        "subOpcion": "1",
                        "hospital": "0",
                        "empresa": this.$store.state.sesion.sesion_empresa,
                        "serie": this.serie,
                        "siguiente": this.siguiente,
                        "hospitalCorrelativo": this.selectHospital.Codigo,
                        "es_factura_igss": this.selectFacturaIgss.Codigo,
                        "es_factura_cambiaria": this.selectFacturaCambiaria.Codigo,
                        "serie_gface": this.serieGface
                    })
                    .then( () => {
                        this.serie = null
                        this.siguiente = null
                        this.selectHospital = null
                        this.selectFacturaCambiaria = null
                        this.selectFacturaIgss = null
                        this.serieGface = null
                        this.isCorrelativoFactura = false
                        this.ObtenerCorrelativoFaturas()
                    })
                }else{
                    this.axios.post('/app/v1_JefeCaja/AlmacenarCorrelativoFacturas', {
                        "opcion": "I",
                        "subOpcion": "1",
                        "hospital": "0",
                        "empresa": this.$store.state.sesion.sesion_empresa,
                        "serie": this.serie,
                        "siguiente": this.siguiente,
                        "hospitalCorrelativo": this.selectHospital.Codigo,
                        "es_factura_igss": this.selectFacturaIgss.Codigo,
                        "es_factura_cambiaria": this.selectFacturaCambiaria.Codigo,
                        "serie_gface": this.serieGface
                    })
                    .then( resp => {
                        this.isCorrelativoFactura = false
                        if(resp.data.json[0].tipo_error == '1'){
                            this.$vs.dialog({
                                color: 'danger',
                                title: 'Correlativo Facturas',
                                text: resp.data.json[0].descripcion,
                                acceptText: 'Aceptar'
                            })
                            return
                        }else{
                            this.serie = null
                            this.siguiente = null
                            this.selectHospital = null
                            this.selectFacturaCambiaria = null
                            this.selectFacturaIgss = null
                            this.serieGface = null
                            this.ObtenerCorrelativoFaturas()
                        }   
                    })
                }
            },
            
            /* inicio metodos de correlativo recibos */
            DatosMostrarHospitalRecibos({
                Codigo,
                NombreCorto
            }){
                return Codigo ? ` ${Codigo} - ${NombreCorto} ` : ''
            },
            ObtenerCorrelativoRecibos(){
                this.axios.post('/app/v1_JefeCaja/ObtenerCorrelativoRecibos', {
                    "Opcion": "C",
                    "SubOpcion": "1",
                    "Hospital": "0",
                    "empresa": this.$store.state.sesion.sesion_empresa
                })
                .then(resp => {
                    this.tablaCorrelativoRecibos = resp.data.json.map(m => {
                        return {
                            ...m
                        }
                    })
                })
            },
            ObtenerHospitalesRecibos(){
                this.axios.post('/app/v1_JefeCaja/ObtenerHospitalesRecibos', {
                    "Opcion": "C",
                    "SubOpcion": "2",
                    "Hospital": "0",
                    "empresa": this.$store.state.sesion.sesion_empresa
                })
                .then(resp => {
                    this.listadoHospitalRecibos = resp.data.json.map(m => {
                        return {
                            ...m
                        }
                    })
                })
            },
            NuevoCorrelativoRecibos(){
                this.tituloCorrelativoRecibo = 'Nuevo Correlativo Recibo',
                this.isSerieRecibo = false,
                this.isCorrelativoRecibo = true,
                this.ObtenerHospitalesRecibos()
            },
            EditarCorrelativoRecibo(item){
                
                this.ObtenerHospitalesRecibos()
                this.serieRecibo = item.Serie
                this.siguienteRecibo = item.Siguiente
                this.selectHospitalRecibo = {
                    Codigo: item.Codigo,
                    NombreCorto: item.NombreCorto
                }
                this.selectReciboIgss = {
                    Codigo: item.EsReciboIGSS,
                    Nombre: item.EsReciboIGSS == 'N' ? 'No' : 'Si'
                }
                this.tituloCorrelativoRecibo = 'Modificar Correlativo Recibo'
                this.isSerieRecibo = true
                this.isCorrelativoRecibo = true
            },
            GuardarCorrelativoRecibo(){
                if(this.isSerieRecibo){
                    this.axios.post('/app/v1_JefeCaja/ActualizarCorrelativoRecibos', {
                        "opcion": "A",
                        "subOpcion": "1",
                        "hospital": "0",
                        "empresa": this.$store.state.sesion.sesion_empresa,
                        "serie": this.serieRecibo,
                        "siguiente": this.siguienteRecibo,
                        "hospitalCorrelativo": this.selectHospitalRecibo.Codigo,
                        "es_factura_igss": this.selectReciboIgss.Codigo
                    })
                    .then( () => {
                        this.serieRecibo = null
                        this.siguienteRecibo = null
                        this.selectHospitalRecibo = null
                        this.selectReciboIgss = null
                        this.isCorrelativoRecibo = false
                        this.ObtenerCorrelativoRecibos()
                    })
                }else{
                    this.axios.post('/app/v1_JefeCaja/AlmacenarCorrelativoRecibos', {
                        "opcion": "I",
                        "subOpcion": "1",
                        "hospital": "0",
                        "empresa": this.$store.state.sesion.sesion_empresa,
                        "serie": this.serieRecibo,
                        "siguiente": this.siguienteRecibo,
                        "hospitalCorrelativo": this.selectHospitalRecibo.Codigo,
                        "es_factura_igss": this.selectReciboIgss.Codigo
                    })
                    .then( resp => {
                        this.isCorrelativoRecibo = false
                        if(resp.data.json[0].tipo_error == '1'){
                            this.$vs.dialog({
                                color: 'danger',
                                title: 'Correlativo Recibos',
                                text: resp.data.json[0].descripcion,
                                acceptText: 'Aceptar'
                            })
                            return
                        }else{
                            this.serieRecibo = null
                            this.siguienteRecibo = null
                            this.selectHospitalRecibo = null
                            this.selectReciboIgss = null
                            this.ObtenerCorrelativoRecibos()
                        }   
                    })
                }
            },
            /* inicio metodos de correlativo recibos */
            
            ObtenerCorrelativoNotasCredito(){
                this.axios.post('/app/v1_JefeCaja/ObtenerCorrelativoNotasCredito', {
                    "Opcion": "C",
                    "SubOpcion": "1",
                    "Hospital": "0",
                    "empresa": this.$store.state.sesion.sesion_empresa
                })
                .then(resp => {
                    this.tablaCorrelativoNotasCredito = resp.data.json.map(m => {
                        return {
                            ...m
                        }
                    })
                })
            },
            NuevoCorrelativoNotasCredito(){
                this.tituloCorrelativoNotasCredito = 'Nuevo Correlativo Nota de Credito'
                this.isCorrelativoNotasCredito = true
                this.isSerieNotasCredito = false
            },
            EditarCorrelativoNotasCredito(item){
                
                this.serieNotasCredito = item.Serie
                this.siguienteNotasCredito = item.SiguienteNota
                this.tituloCorrelativoNotasCredito = 'Modificar Correlativo Notas Credito'
                this.isSerieNotasCredito = true
                this.isCorrelativoNotasCredito = true
            },
            GuardarCorrelativoNotasCredito(){
                if(this.isSerieNotasCredito){
                    this.axios.post('/app/v1_JefeCaja/ActualizarCorrelativoNotasCredito', {
                        "opcion": "A",
                        "subOpcion": "1",
                        "hospital": "0",
                        "empresa": this.$store.state.sesion.sesion_empresa,
                        "serie": this.serieNotasCredito,
                        "siguiente": this.siguienteNotasCredito,
                        "establecimientoSat": this.establecimientoSat,
                        "numeroCaja": this.numeroCaja
                    })
                    .then( () => {
                        this.serieNotasCredito = null
                        this.siguienteNotasCredito = null
                        this.isCorrelativoNotasCredito = false
                        this.ObtenerCorrelativoNotasCredito()
                    })
                }else{
                    this.axios.post('/app/v1_JefeCaja/AlmacenarCorrelativoNotasCredito', {
                        "opcion": "I",
                        "subOpcion": "1",
                        "hospital": "0",
                        "empresa": this.$store.state.sesion.sesion_empresa,
                        "serie": this.serieNotasCredito,
                        "siguiente": this.siguienteNotasCredito,
                        "establecimientoSat": this.establecimientoSat,
                        "numeroCaja": this.numeroCaja
                    })
                    .then( resp => {
                        this.isCorrelativoNotasCredito = false
                        if(resp.data.json[0].tipo_error == '1'){
                            this.$vs.dialog({
                                color: 'danger',
                                title: 'Correlativo Notas Credito',
                                text: resp.data.json[0].descripcion,
                                acceptText: 'Aceptar'
                            })
                            return
                        }else{
                            this.serieNotasCredito = null
                            this.siguienteNotasCredito = null
                            this.ObtenerCorrelativoNotasCredito()
                        }   
                    })
                }
            }
        },
        activated(){
            this.ObtenerCorrelativoFaturas()
            this.ObtenerCorrelativoRecibos()
            this.ObtenerCorrelativoNotasCredito()
            
        },
        watch: {
            isCorrelativoFactura(valor) {
                if(!valor) {
                    this.serie = null
                    this.siguiente = null
                    this.selectHospital = null
                    this.selectFacturaCambiaria = null
                    this.selectFacturaIgss = null
                    this.serieGface = null
                }
            },
            isCorrelativoRecibo(valor) {
                if(!valor){
                    this.serieRecibo = null
                    this.siguienteRecibo = null
                    this.selectHospitalRecibo = null
                    this.selectReciboIgss = null
                }
            },
            isCorrelativoNotasCredito(valor) {
                if(!valor){
                    this.serieNotasCredito = null
                    this.siguienteNotasCredito = null
                }
            }
        }
    }
</script>
<style lang="scss" scoped>

    .siguiente-input{
        padding-left: 80px;
    }
    .default-input {
        margin: 10px;
    }
    .multi-span {
        padding-top: 5px;
        padding-left: 10px;
        width: 40%;
    }
    .th_editar {
        text-align: center;
        .btn_editar{
            display: inline-block;
        }
    }
    .h2-leyenda {
        font-weight: bold;
        color: red;
    }
    .span-leyenda {
        font-weight: bold;
        font-size: 16px;
        color: red;
    }
    .mensaje {
        margin-top: 10px;
    }
</style>