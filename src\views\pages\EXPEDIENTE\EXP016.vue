<template>
<div>
    <DxDataGrid v-bind="DefaultDxGridConfiguration" :data-source="DetalleOrdenes" :height="'100%'" :sorting="{mode: 'none'}" :scrolling="{ showScrollbar: 'never' , useNative: false}" :searchPanel="{visible: false}" @editor-preparing="onCellPrepared" :editing="{
                allowUpdating: true,
                allowAdding: false,
                allowDeleting: false,
                mode:'cell',
            }" :visible="true">

        <DxColumn :width="50" cell-template="checkButton" header-cell-template="checkAllButton" alignment="center" />

        <DxColumn data-field="id" :visible="false" />
        <DxColumn data-field="idOrdenMedica" :visible="false" />
        <DxColumn :width="300" caption="Descripción orden médica" :allow-editing="false" :allowHeaderFiltering="false" :minWidth="100" data-field="OrdenMedica" />
        <DxColumn caption="Estado de orden" :minWidth="100" :allow-editing="false" data-field="Estado" />
        <DxColumn caption="Observaciones" :allowHeaderFiltering="false" :minWidth="100" data-field="Observaciones" />
        <DxColumn caption="Usuario" :minWidth="100" :allow-editing="false" data-field="Usuario" />

        <DxToolbar>
            <DxItem name="groupPanel" />
            <DxItem widget="dxButton" toolbar="bottom" :options="{
                                        text: 'Guardar',
                                        type: 'success',
                                        icon: 'save',
                                        useSubmitBehavior: true,
                                        onClick: submit,
                                        disabled: deshabilitarGuardar
                                    }" location="before">
            </DxItem>
            <DxItem widget="dxButton" toolbar="bottom" :options="{
                                        text: 'Cancelar',
                                        type: 'danger',
                                        icon: 'fas fa-ban',
                                        useSubmitBehavior: true,
                                        onClick: () =>{ this.$emit('cancelOrdenes', true) },
                                    }" location="before">
            </DxItem>
        </DxToolbar>

        <template #checkButton="{data: info}">
            <div style="display: flex; flex-wrap: wrap; justify-content: center;">
                <vs-tooltip v-if="info.data.IdEstado !== '6'" text="Cambiar estado" position="bottom" style="cursor: pointer;">
                    <font-awesome-icon v-if="info.data.IdEstado === '0'" :icon="['far', 'square']" style="font-size: 20px; color: #337ab7;" @click="check(info)" />
                    <font-awesome-icon v-if="info.data.IdEstado === '1'" :icon="['fas', 'square-check']" style="font-size: 20px; color: #20a10a;" @click="check(info)" />
                </vs-tooltip>
                <font-awesome-icon v-else :icon="['fas', 'square-check']" style="font-size: 20px; color: #20a10a;" />
            </div>
        </template>
        <template #checkAllButton>
            <div style="display: flex; flex-wrap: wrap; justify-content: center;">
                <vs-tooltip v-if="selectAll !== 3" :text="!selectAll?'Seleccionar todo':'Deseleccionar todo'" position="bottom" style="cursor: pointer;">
                    <font-awesome-icon v-if="selectAll === 0" :icon="['far', 'square']" style="font-size: 20px; color: #337ab7;" @click="checkAll()" />
                    <font-awesome-icon v-if="selectAll === 1" :icon="['fas', 'square-check']" style="font-size: 20px; color: #20a10a;" @click="checkAll()" />
                    <font-awesome-icon v-if="selectAll === 2" :icon="['fas', 'square-minus']" style="font-size: 20px; color: #337ab7;" @click="checkAll()" />
                </vs-tooltip>
                <vs-tooltip v-if="selectAll === 3" text="Completado" position="bottom" style="cursor: auto;">
                    <font-awesome-icon :icon="['fas', 'star']" style="font-size: 20px; color: #20a10a;" />
                </vs-tooltip>
            </div>
        </template>
    </DxDataGrid>
</div>
</template>

<script>
/**
Orden Médica- Es un documento escrito donde el médico prescribe servicios y/o tratamientos para el paciente. 
TODA orden médica está firmada por el facultativo que la prescribe y el personal de enfermería que toma la misma.
Tipos de Órdenes Médicas:
a) Órdenes “STAT” - Debe ser cumplidas inmediatamente y ejecutadas por una sola vez (“one time order”). Estas órdenes son tomadas y ejecutadas con la prioridad que ameritan.
b) Órdenes en formatos estandarizados , protocolos o pre impresas – establecen las alternativas de manejo para una condición o un área clínica en específico.
Facilitan la documentación de órdenes médicas y proveen guías de manejo y tratamiento.
c) Órdenes PRN (si es necesario) – son ordenes donde el medico establece los parámetros para su ejecutoria. Ejemplo: Acetaminophen 500mg orally Q 6 hrs PRN if temperature more than 38.3ºC. .
d) Órdenes rutinarias- son ordenes que se administran siguiendo el horario de ejecución y/o administración descrito. Se llevan a cabo por un número de días específicos o hasta que una nueva orden las cancele. Algunas órdenes rutinarias les aplica el procedimiento de paro automático.
e) Órdenes Verbales - instrucción dada por el médico vía telefónica o verbal para ofrecer tratamientos o servicios. Requieren la firma del médico no más tarde de 8 horas de ser generada. Requiere de testigos y repetición (“Read Back”) de la orden al tomarse la misma. 
*/
import EXPBase from './EXPBase.vue'
import {
    DefaultDxGridConfiguration
} from './data'
import {
    DxDataGrid,
    DxToolbar,
    DxItem,
    DxColumn,
} from 'devextreme-vue/data-grid'

export default {
    name: 'CumplimientoOrdenes',
    extends: EXPBase,
    props: {
        /**Encabezado de orden médica a la cual se agregará el detalle de cumplimiento */
        idOrdenMedica: {
            type: Number,
            default: -1,
        },
        /**Texto de la orden médica ese será subdivido en varias ordenes para agregar el detalle de cumplimineto si este no existe */
        txtOrdenMedica: {
            type: String,
            default: ''
        },
    },
    components: {
        DxDataGrid,
        DxToolbar,
        DxItem,
        DxColumn,
    },
    data() {
        return {
            DefaultDxGridConfiguration,
            DetalleOrdenes: [],
            contadorCumplimientos: 0, //Variable para llevar el control de cuantos cumplimientos se han seleccionado
            contadorCumplidas: 0, //Variable para saber cuantos cumplimientos ya existen, que no se pueden modificar
            cumplimientosMaximos: 0, //Variable para saber la cantidad de cumplimienos máximos que se pueden seleccionar
            deshabilitarGuardar: false
        }
    },
    methods: {
        submit() {
            let postData = {
                SerieAdmision: this.SerieAdmision,
                Admision: this.CodigoAdmision,
                IdOrden: this.idOrdenMedica,
                Cumplidas: this.DetalleOrdenes
            }
            this.axios.post("/app/v1_ExpedienteEvolucion/InsertarCumplimientoOrden", {
                ...postData
            }).then(() => {
                this.$emit('refreshOrdenes', true);
                this.Cargar()
            })
        },
        Cargar() {
            this.contadorCumplimientos = 0
            this.contadorCumplidas = 0
            this.cumplimientosMaximos = 0
            this.DetalleOrdenes = []
            /** Buscar si hay registro de cumplimiento, sino split txtOrdenMedica */
            if (this.idOrdenMedica !== -1) {

                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaOrdenesCumplidas', {
                        IdOrden: this.idOrdenMedica
                    })
                    .then(resp => {
                        this.DetalleOrdenes = resp.data.json.map((x, index) => {
                            if (x.IdEstado === '6') {
                                this.contadorCumplidas++;
                            }
                            return {
                                id: index,
                                OrdenMedica: x.OrdenMedica,
                                IdEstado: x.IdEstado,
                                Estado: x.Estado,
                                Observaciones: x.Observaciones,
                                IdOrdenMedica: x.IdOrdenMedica,
                                IdOrdenCumplida: x.IdOrdenCumplida,
                                Usuario: x.Usuario
                            }
                        })
                        if (this.DetalleOrdenes.length > 0) {
                            this.cumplimientosMaximos = this.DetalleOrdenes.length - this.contadorCumplidas
                        } else {
                            this.DetalleOrdenes = this.SplitOrden()
                            this.cumplimientosMaximos = this.DetalleOrdenes.length
                        }
                    })
            }
        },
        SplitOrden() {
            let ordenes = this.txtOrdenMedica.split('\n');

            return ordenes.reduce((res, item, index) => {
                if (item.trim() !== '') {
                    res.push({
                        id: index,
                        IdOrdenMedica: this.idOrdenMedica,
                        OrdenMedica: item.trim(),
                        IdOrdenCumplida: 0,
                        Estado: '',
                        Usuario: '',
                        Puesto: '',
                        IdEstado: '0'
                    })
                }
                return res
            }, [])
        },
        check(obj) {
            if (obj.data.IdEstado === '0') {
                obj.data.Estado = '** Cumplido **'
                obj.data.IdEstado = '1'

                this.contadorCumplimientos++;
            } else if (obj.data.IdEstado === '1') {
                obj.data.Estado = ''
                obj.data.IdEstado = '0'

                this.contadorCumplimientos--;
            }
        },
        checkAll() {
            if (this.selectAll === 0) {
                for (let i of this.DetalleOrdenes) {
                    if (i.IdEstado !== '6') {
                        i.Estado = '** Cumplido **'
                        i.IdEstado = '1'
                        this.contadorCumplimientos++
                    }
                }
                this.contadorCumplimientos = this.DetalleOrdenes.length - this.contadorCumplidas
            } else if (this.selectAll === 1 || this.selectAll === 2) {
                for (let i of this.DetalleOrdenes) {
                    if (i.IdEstado !== '6') {
                        i.Estado = ''
                        i.IdEstado = '0'
                    }
                }
                this.contadorCumplimientos = 0;
            }
        },
        onCellPrepared(e) {
            if (e.parentType === 'dataRow' && e.row.data.Estado === 'Cumplido') {
                e.editorOptions.disabled = true
            }
        }
    },
    watch: {
        'idOrdenMedica'() {
            this.Cargar()
        },
        'selectAll'(newval) {
            this.deshabilitarGuardar = false
            if (newval === 3) {
                this.deshabilitarGuardar = true
            }
        }
    },
    computed: {
        //Variable para manejar estado del checkbox para seleccionar todos
        selectAll() {
            if (this.contadorCumplimientos === 0 && this.cumplimientosMaximos > 0) {
                return 0
            } else if (this.contadorCumplimientos === this.cumplimientosMaximos && this.cumplimientosMaximos !== 0) {
                return 1
            } else if (this.contadorCumplidas > 0 && this.cumplimientosMaximos === 0) {
                return 3
            } else {
                return 2
            }
        }
    }
}
</script>
