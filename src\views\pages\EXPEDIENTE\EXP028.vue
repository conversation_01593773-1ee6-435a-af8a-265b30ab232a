<template>
<div>
    <form @submit="handleSubmit">
        <DxForm :visible="true" :form-data.sync="formulario" labelMode="floating" :ref="dataForm">
            <DxGroupItem item-type="group" :col-count="2">
                <DxGroupItem item-type="group">
                    <DxItem data-field="Fecha" :is-required="true" editor-type="dxDateBox" :editor-options="dateOptions">
                        <DxLabel :text="'Mes y año'" />
                    </DxItem>
                    <DxItem data-field="Medico" editor-type="dxLookup" :is-required="habilitarMedico" :editor-options="{ dataSource: medicos, displayExpr: 'Descripcion', showCancelButton: false, dropDownOptions: dropDownOptions, disabled: !habilitarMedico }">
                        <DxLabel :text="'Médico'" />
                    </DxItem>
                </DxGroupItem>
                <DxGroupItem item-type="group">
                    <DxItem data-field="UsarMedico" editor-type="dxCheckBox" :editor-options="checkBoxOptions">
                        <DxLabel :text="'Buscar por médico y mes'" />
                    </DxItem>
                    <DxButtonItem :button-options="submitButtonOptions" horizontal-alignment="center" verical-alignment="center" />
                </DxGroupItem>
            </DxGroupItem>
        </DxForm>
    </form>
</div>
</template>

<script>
import {
    DxForm,
    DxItem,
    DxLabel,
    DxGroupItem,
    DxButtonItem,
} from 'devextreme-vue/form'
import 'devextreme-vue/lookup';

const dataForm = 'anestesiaForm'

export default {
    name: 'EstadisticaSOP',
    components: {
        DxForm,
        DxItem,
        DxLabel,
        DxGroupItem,
        DxButtonItem,
    },
    data() {
        return {
            dataForm,
            formulario: {
                Opcion: 'Mes',
                Medico: '',
                Mes: '',
                Año: '',
                UsarMedico: false
            },
            dateOptions: {
                max: new Date(),
                type: "date",
                displayFormat: 'monthAndYear',
                calendarOptions: {
                    maxZoomLevel: 'year',
                    minZoomLevel: 'century',
                }
            },
            submitButtonOptions: {
                text: 'Generar',
                type: 'danger',
                icon: 'fas fa-file-pdf',
                width: 200,
                useSubmitBehavior: true,
                disabled: false
            },
            medicos: [],
            dropDownOptions: {
                showTitle: false,
                maxHeight: '300px',
                hideOnOutsideClick: true,
            },
            checkBoxOptions: {
                onValueChanged: this.checkBoxChanged
            },
            habilitarMedico: false,
        }
    },
    props: {
        SerieAdmision: null,
        CodigoAdmision: null,
        NumeroExpediente: null,

        StatusAdmision: null,
    },
    methods: {

        handleSubmit(e) {
            e.preventDefault()

            let postData = {
                ...this.formatFecha(this.formulario.Fecha),
                ...this.mapSolicitud(),
                Opcion: this.formulario.Opcion
            }

            this.$reporte_modal({
                Nombre: 'Estadistica Sala Operaciones',
                Opciones: {
                    ...postData
                }
            })
        },
        mapSolicitud() {
            let peticion = {}

            if (this.formulario.UsarMedico) {
                peticion = {
                    TipoMedico: this.formulario.Medico.Tipo,
                    CodigoMedico: this.formulario.Medico.Codigo,
                    EmpresaMedico: this.formulario.Medico.Empresa,
                    Corporativo: this.formulario.Medico.Corporativo,
                }
            }

            return peticion
        },
        formatFecha(date) {
            const year = new Date(date).toLocaleString('default', {
                year: 'numeric'
            });
            const month = new Date(date).toLocaleString('default', {
                month: '2-digit'
            });

            return {
                Año: year,
                Mes: month
            }
        },
        checkBoxChanged(e) {
            if (e.value) {
                this.habilitarMedico = true
                this.formulario.Opcion = 'Medico'
            } else {
                this.habilitarMedico = false
                this.formulario.Opcion = 'Mes'
            }
        },
        getMedicos() {
            this.axios.post('/app/v1_ExpedienteEvolucion/MedicosEstadistica', {
                    Busqueda: ''
                })
                .then(resp => {
                    this.medicos = resp.data.json.map(x => {
                        let desc = ''
                        if (x.Cod_Empleado.trim()) {
                            desc = x.Cod_Empleado + ' - ' + x.Empleado_registra
                        } else if (x.Corporativo.trim()) {
                            desc = x.Corporativo + ' - ' + x.Empleado_registra
                        } else {
                            desc = x.Empleado_registra
                        }
                        return {
                            Codigo: parseInt(x.Cod_Empleado),
                            Cargo: x.Puesto_registra,
                            Empresa: x.Empresa_Empleado,
                            Tipo: x.TipoMedico,
                            Descripcion: desc,
                            Corporativo: parseInt(x.Corporativo)
                        }
                    })
                })
        },
        getMounted() {
            this.getMedicos()
            this.formulario = {
                Opcion: 'Mes',
                Medico: '',
                Mes: '',
                Año: '',
                UsarMedico: false
            }
        }
    },
    mounted() {},
    watch: {

    },
    computed: {},
}
</script>

<style>
#aldrete .dx-item-content .dx-box-item-content {
    place-content: baseline !important;
}

#aldrete .dx-invalid .dx-radiobutton-icon::before {
    border-color: red !important
}
</style>
