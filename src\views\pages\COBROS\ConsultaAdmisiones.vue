<template>
<div class="consulta-admisiones-container">
    <!-- <vx-card class="p-2 "> -->
    <div class="Encabezado flex flex-wrap">
        <div class="flex-none w-fit pt-5 pl-1 pr-1 info-enc-height" v-if="!pacienteInfo.Nombre">
            <vs-tooltip text="Buscar Paciente">
                <vs-button color="primary" size="small" icon-pack="feather" icon="icon-search" @click="BuscarPaciente" />
            </vs-tooltip>
        </div>
        <vs-popup :active.sync="popupBusquedaPaciente" title="Búsqueda de pacientes">
            <BusquedaPacientes v-bind="$props" ref="busquedaPop" @admision="ingresarAdmisionPopup" />
        </vs-popup>
        <div class="flex-none w-fit">
            <div v-if="pacienteInfo.Nombre">
                <h5 style="font-weight: 700;">{{ Encabezado.Nombre + ' ' + Encabezado.Apellido}} </h5>
            </div>
            <vx-input-group class="w-auto">
                <vs-tooltip text="Ingrese la serie seguido del número de la admisión, o bien el número de habitacion">
                    <vs-input class="w-full" size="small" @keyup="No_Admision=No_Admision.toUpperCase()" v-model="No_Admision" name="Admision" label-placeholder="Número de admisión" @change="CargarAdmision()" :disabled="pacienteInfo.Nombre"  />
                </vs-tooltip>

                <template slot="append">
                    <div class="append-text btn-addon pt-5">
                        <vs-tooltip text="Buscar datos de la admisión" v-if="!pacienteInfo.Nombre">
                            <vs-button size="small" color="success" icon-pack="fas" @click="CargarAdmision()" icon="fa-share" />
                        </vs-tooltip>

                        <vs-tooltip v-if="pacienteInfo.Nombre" text="Limpiar datos" class="vs-button vs-button-danger vs-button-filled includeIcon includeIconOnly small">
                            <vs-button clientWidth="42" size="small" color="danger" icon-pack="feather" @click="LimpiarVariables(); No_Admision=''" icon="icon-x"></vs-button>
                        </vs-tooltip>

                        <vs-tooltip v-if="pacienteInfo.Nombre" text="Recargar datos de la admisión" class="vs-button vs-button-filled includeIcon includeIconOnly small">
                            <vs-button clientWidth="42" size="small" color="warning" icon-pack="feather" @click="CargarAdmision()" icon="icon-refresh-ccw"></vs-button>
                        </vs-tooltip>
                    </div>
                </template>
            </vx-input-group>
        </div>
        <transition name="slide-fade">
            <div v-if="pacienteInfo.Nombre" class="w-52 p-1 info-enc">
                <table>
                    <tr>
                        <td class="font-semibold">Edad:</td>
                        <td>{{ Encabezado.Edad }}</td>
                    </tr>
                    <tr>
                        <td class="font-semibold">Sexo:</td>
                        <td>{{ Encabezado.DescripcionSexo }}</td>
                    </tr>
                    <tr>
                        <td colspan="1" class="font-semibold">Nacimiento:</td>
                        <td colspan="1">{{ this.$formato_fecha(Encabezado.Nacimiento, 'ddMMyyyy') }}</td>
                    </tr>
                </table>
            </div>
        </transition>

        <transition name="slide-fade">
            <div v-if="pacienteInfo.Nombre" class="w-52 p-1 info-enc">
                <table>
                    <tr>
                        <td class="font-semibold">Peso:</td>
                        <td> <span style="color: #ed1b24" v-if="Encabezado.Peso==''">0</span> {{ Encabezado.Peso + ' kg'}} </td>

                        <td class="font-semibold">Estatura:</td>
                        <td> <span style="color: #ed1b24" v-if="Encabezado.Estatura==''">0</span> {{ Encabezado.Estatura + ' cm'}} </td>
                    </tr>

                    <tr>
                        <td colspan="1" class="font-semibold">IMC:</td>
                        <td> {{ this.$formato_decimal(Encabezado.IMC, 2) }} </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            {{ Encabezado.DescripcionIMC }}
                        </td>
                    </tr>
                </table>
            </div>
        </transition>
        <transition name="slide-fade">
            <div v-if="pacienteInfo.Nombre" class="w-56 p-1 info-enc">
                <table>
                    <tr>
                        <td class="font-semibold">Tipo Admisión:</td>
                        <td>{{ Encabezado.TipoAdmision}} </td>
                    </tr>
                    <tr>
                        <td class="font-semibold">Fecha Ingreso:</td>
                        <td>{{ this.$formato_fecha(Encabezado.Entrada) }}</td>
                    </tr>
                    <tr>
                        <td class="font-semibold">Fecha Egreso:</td>
                        <td>{{ this.$formato_fecha(Encabezado.FechaEgreso) }}</td>
                    </tr>
                </table>
            </div>
        </transition>
        <transition name="slide-fade">
            <div v-if="pacienteInfo.Nombre" class="w-52 p-1 info-enc">
                <table>
                    <tr>
                        <td class="font-semibold">Habitación:</td>
                        <td>{{ Encabezado.Habitacion}}</td>
                    </tr>
                    <tr>
                        <td class="font-semibold">Hospital Habitación:</td>
                        <td>{{ Encabezado.HospitalHabitacion}}</td>
                    </tr>
                    <tr>
                        <td class="font-semibold">Médico Tratante:</td>
                        <td>{{ Encabezado.Tratante }}</td>
                    </tr>
                </table>
            </div>
        </transition>
        <transition name="slide-fade">
            <div v-if="pacienteInfo.Nombre" :class="'w-auto p-1 info-enc '+ ColorSeguro">
                <table>
                    <tr>
                        <td class="font-semibold">Seguro:</td>
                        <td>{{ Encabezado.NombreSeguro}}</td>
                    </tr>
                    <tr>
                        <td class="font-semibold">Tipo Seguro:</td>
                        <td>{{ Encabezado.TipoSeguro }} </td>
                    </tr>
                    <tr>
                        <td class="font-semibold">Visitas previas:</td>
                        <td>{{ Encabezado.VisitasPrevias}}</td>
                    </tr>
                </table>
            </div>
        </transition>
    </div>
    <div class="pt-4">
        <vx-card>
            <DxForm :ref="formularioConsulta" :form-data.sync="formulario" labelMode="hidden">
                <DxFormGroupItem :col-count="2">
                    <DxFormGroupItem caption="Observaciones internas">
                        <DxFormItem data-field="ObservacionesInternas" editor-type="dxTextArea" :editor-options="{ readOnly: !pacienteInfo.Nombre, height: 300 }" />
                        <DxFormButtonItem :button-options="buttonObsInternas" name="GrabarInterna" horizontal-alignment="center" verical-alignment="center" />
                    </DxFormGroupItem>
                    <DxFormGroupItem caption="Observaciones paciente">
                        <DxFormItem data-field="ObservacionesPaciente" editor-type="dxTextArea" :editor-options="{ readOnly: !pacienteInfo.Nombre, height: 300 }" />
                        <DxFormGroupItem :col-count="2">
                            <DxFormButtonItem :button-options="buttonObsPaciente" name="GrabarPaciente" horizontal-alignment="right" verical-alignment="center" />
                            <DxFormButtonItem :button-options="buttonLimpiar" name="Limpiar" horizontal-alignment="left" verical-alignment="center" />
                        </DxFormGroupItem>
                    </DxFormGroupItem>
                </DxFormGroupItem>
            </DxForm>
            <DxTabPanel :ref="tabPanelConsulta" class="pt-4" :height="'100%'" :data-source="tabs" :selected-index="indiceTab" :showNavButtons="true">
                <template #title="{ data: tab }">
                    <span>
                        <font-awesome-icon class="pr-1" :icon="['fas', tab.icon]" style="font-size: 16px" />
                        {{ tab.name }}
                    </span>
                </template>
                <template #item="{ data: tab }">
                    <div>
                        <div v-if="tab.value === 1" class="m-4">
                            <DxDataGrid v-bind="DefaultDxGridConfiguration" :data-source="obsInteras" :filter-sync-enabled="true" :headerFilter="{ visible: true , allowSearch: true }" :height="'100%'" :width="'100%'">
                                <DxDataGridSelection mode="single" />

                                <DxDataGridColumn width="100%" css-class="wrap-text" data-field="Observacion" caption="Observación" :allowReordering="false" :allowHeaderFiltering="false" data-type="string" alignment="left" :customize-text="MostrarSaltoLinea" />
                                <DxDataGridColumn width="150px" data-field="FechaRegistro" caption="Fecha registro" :allowReordering="false" :allowHeaderFiltering="false" format="dd/MM/yyyy HH:mm" data-type="date" alignment="center" />
                                <DxDataGridColumn width="70px" data-field="Usuario" :allowReordering="false" :allowHeaderFiltering="false" data-type="string" alignment="center" />
                            </DxDataGrid>
                        </div>
                        <div v-if="tab.value === 2" class="m-4">
                            <DxForm :ref="formSeguimiento" :form-data.sync="formularioSeguimiento" labelMode="floating">
                                <DxFormGroupItem>
                                    <DxFormItem data-field="Seguimiento" editor-type="dxTextArea" :editor-options="{ readOnly: !pacienteInfo.Nombre, height: 100 }">
                                        <DxFormLabel text="Seguimiento" />
                                    </DxFormItem>
                                    <DxFormButtonItem :button-options="buttonSeguimiento" name="GrabarSeguimiento" horizontal-alignment="center" verical-alignment="center" />
                                </DxFormGroupItem>
                            </DxForm>
                            <div class="mt-4">
                                <DxDataGrid v-bind="DefaultDxGridConfiguration" :data-source="seguimiento" :filter-sync-enabled="true" :headerFilter="{ visible: true , allowSearch: true }" :height="'100%'" :width="'100%'">
                                    <DxDataGridSelection mode="single" />

                                    <DxDataGridColumn width="100%" css-class="wrap-text" data-field="Observacion" caption="Observación" :allowReordering="false" :allowHeaderFiltering="false" data-type="string" alignment="left" />
                                    <DxDataGridColumn width="150px" data-field="FechaRegistro" caption="Fecha registro" :allowReordering="false" :allowHeaderFiltering="false" format="dd/MM/yyyy HH:mm" data-type="date" alignment="center" />
                                    <DxDataGridColumn width="70px" data-field="Usuario" :allowReordering="false" :allowHeaderFiltering="false" data-type="string" alignment="center" />
                                </DxDataGrid>
                            </div>
                        </div>
                        <div v-if="tab.value === 3" class="m-4">
                            <DxForm :form-data.sync="pacienteInfo" labelMode="floating" :read-only="true">
                                <DxFormGroupItem caption="Paciente">
                                    <DxFormGroupItem :col-count-by-screen="colCountByScreen">
                                        <DxFormItem data-field="Nombre" editor-type="dxTextBox" />
                                        <DxFormItem data-field="Apellido" editor-type="dxTextBox" />
                                        <DxFormItem data-field="ApellidoCasada" editor-type="dxTextBox">
                                            <DxFormLabel text="Apellido casada" />
                                        </DxFormItem>
                                    </DxFormGroupItem>
                                    <DxFormItem data-field="Direccion" editor-type="dxTextBox">
                                        <DxFormLabel text="Dirección" />
                                    </DxFormItem>
                                    <DxFormGroupItem :col-count="2">
                                        <DxFormItem data-field="DPI" editor-type="dxTextBox" />
                                        <DxFormItem data-field="Pasaporte" editor-type="dxTextBox">
                                            <DxFormLabel text="Pasaporte" />
                                        </DxFormItem>
                                    </DxFormGroupItem>
                                    <DxFormGroupItem :col-count="2">
                                        <DxFormItem data-field="Telefono" editor-type="dxTextBox">
                                            <DxFormLabel text="Teléfono" />
                                        </DxFormItem>
                                        <DxFormItem data-field="Edad" editor-type="dxTextBox">
                                            <DxFormLabel text="Edad" />
                                        </DxFormItem>
                                    </DxFormGroupItem>
                                </DxFormGroupItem>
                                <DxFormGroupItem caption="Responsable">
                                    <DxFormGroupItem :col-count-by-screen="colCountByScreen">
                                        <DxFormItem data-field="NombreResponsable" editor-type="dxTextBox">
                                            <DxFormLabel text="Nombre" />
                                        </DxFormItem>
                                        <DxFormItem data-field="ApellidoResponsable" editor-type="dxTextBox">
                                            <DxFormLabel text="Apellido" />
                                        </DxFormItem>
                                        <DxFormItem data-field="ProfesionResponable" editor-type="dxTextBox">
                                            <DxFormLabel text="Profesión" />
                                        </DxFormItem>
                                    </DxFormGroupItem>
                                    <DxFormItem data-field="DireccionResponsable" editor-type="dxTextBox">
                                        <DxFormLabel text="Dirección" />
                                    </DxFormItem>
                                    <DxFormGroupItem :col-count="2">
                                        <DxFormItem data-field="DPIReponsable" editor-type="dxTextBox">
                                            <DxFormLabel text="DPI" />
                                        </DxFormItem>
                                        <DxFormItem data-field="PasaporteResponsable" editor-type="dxTextBox">
                                            <DxFormLabel text="Pasaporte" />
                                        </DxFormItem>
                                    </DxFormGroupItem>
                                    <DxFormGroupItem :col-count="2">
                                        <DxFormItem data-field="TelefonoResponsable" editor-type="dxTextBox">
                                            <DxFormLabel text="Teléfono" />
                                        </DxFormItem>
                                        <DxFormItem data-field="EdadReponsable" editor-type="dxTextBox">
                                            <DxFormLabel text="Edad" />
                                        </DxFormItem>
                                    </DxFormGroupItem>
                                </DxFormGroupItem>
                            </DxForm>
                        </div>
                    </div>
                </template>
            </DxTabPanel>
        </vx-card>
    </div>
    <!-- </vx-card> -->
</div>
</template>

<script>
import 'devextreme-vue/text-area'
import {
    DefaultDxGridConfiguration
} from './data'

const tabPanelConsulta = 'tabPanelConsulta'
const formularioConsulta = 'formularioConsulta'
const formSeguimiento = 'formSeguimiento'

export default {
    name: 'ConsultaAdmision',
    components: {
        BusquedaPacientes: () => import("../EXPEDIENTE/BusquedaPacientes.vue")
    },
    data() {
        return {
            DefaultDxGridConfiguration,
            indiceTab: 0,
            No_Admision: '',
            formulario: {},
            formularioSeguimiento: {},
            tabPanelConsulta,
            formularioConsulta,
            formSeguimiento,

            tabs: [{
                name: 'Observaciones internas',
                value: 1,
                disabled: true,
                icon: "clipboard-list",
            }, {
                name: 'Seguimiento',
                value: 2,
                disabled: true,
                icon: "clipboard-check",
            }, {
                name: 'Datos',
                value: 3,
                disabled: true,
                icon: "hospital-user",
            }, ],

            pacienteInfo: {},

            buttonObsInternas: {
                width: 'auto',
                icon: 'fas fa-save',
                text: 'Obs interna',
                type: 'success',
                onClick: () => {
                    this.GrabarObservacionInterna()
                },
                useSubmitBehavior: false,
            },

            buttonObsPaciente: {
                width: 'auto',
                icon: 'fas fa-save',
                text: 'Obs paciente',
                type: 'success',
                onClick: () => {
                    this.ActualizarObservacionPaciente()
                },
                useSubmitBehavior: false,
            },

            buttonSeguimiento: {
                width: 'auto',
                icon: 'fas fa-save',
                text: 'Agregar',
                type: 'success',
                onClick: () => {
                    this.GrabarSeguimiento()
                },
                useSubmitBehavior: false,
            },

            buttonLimpiar: {
                width: 'auto',
                icon: 'fas fa-broom',
                text: 'Limpiar',
                type: 'warning',
                onClick: () => {
                    this.LimpiarObservaciones()
                },
                useSubmitBehavior: false,
            },

            obsInteras: [],
            seguimiento: [],

            Encabezado: this.LimpiarEncabezado(),
            popupBusquedaPaciente: false,
        }
    },
    methods: {
        BuscarPaciente() {
            this.popupBusquedaPaciente = true;
        },
        ingresarAdmisionPopup(e) {
            this.popupBusquedaPaciente = false
            this.No_Admision = e

            this.CargarAdmision()
        },
        async CargarAdmision() {
            if (this.No_Admision) {
                await this.axios.post("/app/v1_cobros/ConsultaAdmisiones", {
                        Opcion: 1,
                        Serie: this.No_Admision.substring(0, 1),
                        Admision: this.No_Admision.substring(1, this.No_Admision.length)
                    })
                    .then(async resp => {
                        //Al momento de cargar una nueva admisión deshabilita todas las pestañas del módulo
                        this.panelConsulta.option("selectedIndex", 0)
                        this.LimpiarVariables()

                        if (resp.data.json.length > 0) {
                            this.pacienteInfo = resp.data.json[0]

                            this.pacienteInfo.Edad = this.pacienteInfo.Edad + ' años'

                            this.formularioConsultaInstance.getEditor('GrabarInterna').option('visible', true)
                            this.formularioConsultaInstance.getEditor('GrabarPaciente').option('visible', true)
                            this.formularioConsultaInstance.getEditor('Limpiar').option('visible', true)

                            await this.CargarEncabezado()
                            await this.ListarObservacionesInternas()
                            await this.ListarSeguimiento()
                            await this.CargarObservacionPaciente()

                            for (let i of this.panelConsulta._dataSource._items) {
                                i.disabled = false
                            }
                        } else {
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#ed8c72',
                                title: 'Admisión no registrada',
                                acceptText: 'Aceptar',
                                text: 'La admisión ingresada no está registrada en el sistema.',
                                buttonCancel: 'border',
                                accept: () => {
                                    return
                                },
                            })
                        }
                    })
            }
        },
        async CargarEncabezado() {
            this.axios.post("/app/v1_ExpedienteEvolucion/BusquedaEncabezadoExpediente", {
                    hideloading: true,
                    serie: this.No_Admision.substring(0, 1),
                    AdmisionX: this.No_Admision.substring(1, this.No_Admision.length),
                    Admision: 5
                })
                .then(resp => {

                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        this.Encabezado = resp.data.json[0]
                        //renombrando atributos
                        this.Encabezado.NumeroExpediente = this.Encabezado.NoExpediente
                        this.Encabezado.SerieAdmision = this.Encabezado.Serie
                        this.Encabezado.CodigoAdmision = this.Encabezado.CodAdmision
                        this.Encabezado.CodigoPaciente = this.Encabezado.CodPaciente
                        delete this.Encabezado.NoExpediente
                        delete this.Encabezado.Serie
                        delete this.Encabezado.CodAdmision
                        this.Encabezado.DescripcionSexo = this.Encabezado.Sexo
                        this.Encabezado.Sexo = this.Encabezado.Sexo.substring(0, 1)
                        this.No_Admision = this.Encabezado.SerieAdmision + this.Encabezado.CodigoAdmision
                    } else {
                        this.$vs.notify({
                            time: 4000,
                            title: 'Admisión no encontrada',
                            text: 'No se encontró la admisión "' + this.No_Admision + '" o esta no esta activa',
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'warning',
                            position: 'top-center'
                        })
                    }
                })
        },
        async ListarObservacionesInternas() {
            await this.axios.post("/app/v1_cobros/ConsultaAdmisiones", {
                    Opcion: 2,
                    Serie: this.No_Admision.substring(0, 1),
                    Admision: this.No_Admision.substring(1, this.No_Admision.length)
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.obsInteras = resp.data.json
                    }
                })
        },

        async ListarSeguimiento() {
            await this.axios.post("/app/v1_cobros/ConsultaAdmisiones", {
                    Opcion: 3,
                    Serie: this.No_Admision.substring(0, 1),
                    Admision: this.No_Admision.substring(1, this.No_Admision.length)
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.seguimiento = resp.data.json
                    }
                })
        },

        async CargarObservacionPaciente() {
            await this.axios.post("/app/v1_cobros/ConsultaAdmisiones", {
                    Opcion: 4,
                    Serie: this.No_Admision.substring(0, 1),
                    Admision: this.No_Admision.substring(1, this.No_Admision.length)
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.formulario.ObservacionesPaciente = resp.data.json[0].Observacion

                        this.formularioConsultaInstance.repaint()
                    }
                })
        },

        async GrabarObservacionInterna() {
            await this.axios.post("/app/v1_cobros/ConsultaAdmisiones", {
                    Opcion: 5,
                    Serie: this.No_Admision.substring(0, 1),
                    Admision: this.No_Admision.substring(1, this.No_Admision.length),
                    Observacion: this.formulario.ObservacionesInternas,
                    Usuario: this.corporativo
                })
                .then(resp => {
                    if (resp.data.error == 0) {
                        this.formulario.ObservacionesInternas = null
                        this.formularioConsultaInstance.repaint()

                        this.ListarObservacionesInternas()
                    }
                })
        },

        async GrabarSeguimiento() {
            await this.axios.post("/app/v1_cobros/ConsultaAdmisiones", {
                    Opcion: 6,
                    Serie: this.No_Admision.substring(0, 1),
                    Admision: this.No_Admision.substring(1, this.No_Admision.length),
                    Observacion: this.formularioSeguimiento.Seguimiento,
                    Usuario: this.corporativo
                })
                .then(resp => {
                    if (resp.data.error == 0) {
                        this.formularioSeguimiento.Seguimiento = null
                        this.formularioSeguimientoInstance.repaint()

                        this.ListarSeguimiento()
                    }
                })
        },

        async ActualizarObservacionPaciente() {
            await this.axios.post("/app/v1_cobros/ConsultaAdmisiones", {
                    Opcion: 7,
                    Serie: this.No_Admision.substring(0, 1),
                    Admision: this.No_Admision.substring(1, this.No_Admision.length),
                    Observacion: this.formulario.ObservacionesPaciente,
                })
                .then()
        },

        MostrarSaltoLinea(texto) {
            if (texto.value !== null) {
                return texto.value.replace(/\r\n/g, '\n').replace(/\r/g, '\n')
            }
            return null
        },

        LimpiarVariables() {
            //Limpia las variables de los formularios y oculta los botones
            this.pacienteInfo = {}
            this.obsInteras = []
            this.seguimiento = []
            this.formulario.ObservacionesInternas = null
            this.formulario.ObservacionesPaciente = null
            this.formularioConsultaInstance.repaint()
            this.formularioConsultaInstance.getEditor('GrabarInterna').option('visible', false)
            this.formularioConsultaInstance.getEditor('GrabarPaciente').option('visible', false)
            this.formularioConsultaInstance.getEditor('Limpiar').option('visible', false)

            //Al momento de cargar una nueva cita deshabilita todas las pestañas del módulo
            for (let i of this.panelConsulta._dataSource._items) {
                i.disabled = true
            }
        },

        LimpiarObservaciones() {
            this.$vs.dialog({
                type: 'confirm',
                color: '#ed8c72',
                title: 'Elminar observación paciente',
                acceptText: 'Sí',
                cancelText: 'No',
                text: `¿Está seguro de querer borrar las observaciones de paciente?`,
                buttonCancel: 'border',
                accept: () => {
                    this.formulario.ObservacionesPaciente = ''
                    this.ActualizarObservacionPaciente()
                    this.formularioConsultaInstance.repaint()
                },
                cancel: () => {

                }
            })
        },

        LimpiarEncabezado() {
            return {
                NumeroExpediente: "",
                Motivoconsulta: "",
                HistoriaEnfermedad: "",

                Empresa: "",
                SerieAdmision: "",
                CodigoAdmision: "",
                TipoAdmision: "",
                StatusAdmision: "",
                Entrada: "",
                FechaEgreso: "",

                Habitacion: "",
                HospitalHabitacion: "",

                NombreSeguro: "",
                TipoSeguro: "",
                Tratante: "",
                VisitasPrevias: "",

                CodigoPaciente: "",
                Nombre: "",
                Apellido: "",
                Sexo: "",
                Nacimiento: "",
                Edad: "",
                Peso: "",
                Estatura: "",
                IMC: "",
                DescripcionIMC: "",

                Celular: "",
                Ocupacion: "",

            }
        },
    },
    created() {},
    mounted() {
        this.formularioConsultaInstance.getEditor('GrabarInterna').option('visible', false)
        this.formularioConsultaInstance.getEditor('GrabarPaciente').option('visible', false)
        this.formularioConsultaInstance.getEditor('Limpiar').option('visible', false)
    },
    beforeMount() {},
    watch: {},
    computed: {
        colCountByScreen() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 2,
                    lg: 3
                };
        },

        panelConsulta: function () {
            return this.$refs[tabPanelConsulta].instance;
        },

        formularioConsultaInstance: function () {
            return this.$refs[formularioConsulta].instance;
        },

        formularioSeguimientoInstance: function () {
            return this.$refs[this.formSeguimiento].instance;
        },

        corporativo() {
            return this.$store.state.sesion.corporativo
        },

        ColorSeguro() {
            return this.Encabezado.TipoSeguro.replace(' ', '-').toLowerCase()
        }
    }
}
</script>

<style scoped>
.info-enc {
    border: 1px solid #ccc !important;
    border-radius: 5px;
    margin: 1px;
    background-color: #f8f8f8;
    height: 83px !important;
}

.info-enc-height {
    height: 83px !important;
}

.salud-siempre {
    background-color: #ffa500 !important;
}

.privado {
    background-color: #c0dcc0 !important;
}

.seguro {
    background-color: #ffff00 !important;
}

.vacio {
    background-color: #fffdd0 !important;
}

.Encabezado {
    grid-area: Encabezado;
    overflow-y: hidden;
    font-size: small;
    /* background-color: #d0e1f9; */
    background: rgb(208, 225, 249);
    background: linear-gradient(90deg, rgba(208, 225, 249, 1) 67%, rgba(119, 175, 255, 1) 94%, rgba(2, 0, 36, 1) 100%);
    color: #2f496e;
    height: 84px !important;
}
</style>
<style>
/*sobrescribiendo los estilos se SM-INFO*/
.info-expediente-container .info-contenido {
    width: 400px !important;
}

.info-expediente-container .info i {
    font-size: 14px;
}

/**transitions */
.fade-enter-active,
.fade-leave-active {
    transition: opacity .9s;
}

.fade-enter,
.fade-leave-to

/* .fade-leave-active below version 2.1.8 */
    {
    opacity: 0;
}

.slide-fade-enter-active {
    transition: all .3s ease;
}

.slide-fade-leave-active {
    transition: all .8s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.slide-fade-enter,
.slide-fade-leave-to

/* .slide-fade-leave-active below version 2.1.8 */
    {
    transform: translateX(10px);
    opacity: 0;
}

.bounce-enter-active {
    animation: bounce-in .5s;
}

.bounce-leave-active {
    animation: bounce-in .5s reverse;
}

@keyframes bounce-in {
    0% {
        transform: scale(0);
    }

    50% {
        transform: scale(1.5);
    }

    100% {
        transform: scale(1);
    }
}

.consulta-admisiones-container .dx-datagrid-rowsview .dx-row {
    height: auto !important;
    /* Alto automático para las filas */
}

.consulta-admisiones-container .dx-datagrid-rowsview .dx-row>td {
    white-space: pre-line !important;
    /* Permite el ajuste de texto */
    word-wrap: break-word;
    /* Rompe palabras largas */
}
</style>
