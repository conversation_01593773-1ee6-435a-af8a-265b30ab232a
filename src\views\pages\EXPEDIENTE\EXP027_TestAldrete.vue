<template>
<div>
    <div v-if="(consulta && hojaAnestesia !== null && hojaAnestesia !== '') || (!consulta)">
        <div class="p-2" id="aldrete">
            <form @submit="handleSubmit">
                <DxForm :form-data.sync="encabezado" labelMode="floating" :ref="dataEncabezadoRefKey" v-if="!consulta">
                    <DxGroupItem item-type="group" :col-count="2">
                        <DxGroupItem item-type="group" caption="Actividad motora">
                            <DxItem :is-required="true" :disabled="consulta" editor-type="dxRadioGroup" :editor-options="{ dataSource: actividad, layout:'vertical', displayExpr: 'Item', onValueChanged: this.valueChanged }" />
                        </DxGroupItem>

                        <DxGroupItem item-type="group" caption="Respiración">
                            <DxItem :is-required="true" :disabled="consulta" editor-type="dxRadioGroup" :editor-options="{ dataSource: respiracion, layout:'vertical', displayExpr: 'Item', onValueChanged: this.valueChanged }" />
                        </DxGroupItem>

                        <DxGroupItem item-type="group" caption="Circulación">
                            <DxItem :is-required="true" :disabled="consulta" editor-type="dxRadioGroup" :editor-options="{ dataSource: circulacion, layout:'vertical', displayExpr: 'Item', onValueChanged: this.valueChanged}" />
                        </DxGroupItem>

                        <DxGroupItem item-type="group" caption="Conciencia">
                            <DxItem :is-required="true" :disabled="consulta" editor-type="dxRadioGroup" :editor-options="{ dataSource: consciencia, layout:'vertical', displayExpr: 'Item', onValueChanged: this.valueChanged }" />
                        </DxGroupItem>

                        <DxGroupItem item-type="group" caption="Color">
                            <DxItem :is-required="true" :disabled="consulta" editor-type="dxRadioGroup" :editor-options="{ dataSource: color, layout:'vertical', displayExpr: 'Item', onValueChanged: this.valueChanged }" />
                        </DxGroupItem>
                        <DxSimpleItem>
                            <template #default>
                                <div style="font-size: 30px">
                                    <b>Total de puntos:</b>
                                    {{ puntos }}
                                </div>
                            </template>
                        </DxSimpleItem>
                    </DxGroupItem>
                    <DxGroupItem item-type="group" :col-count="2">
                        <DxGroupItem item-type="group" caption="Tipo">
                            <DxItem :is-required="true" :disabled="consulta" editor-type="dxRadioGroup" :editor-options="{ dataSource: tipo, layout:'vertical', displayExpr: 'name', onValueChanged: this.valueChangedTipo }" />
                        </DxGroupItem>
                        <DxGroupItem item-type="group">
                            <DxButtonItem :button-options="submitButtonOptions" horizontal-alignment="center" verical-alignment="center" />
                            <DxButtonItem :button-options="clearButtonOptions" horizontal-alignment="center" verical-alignment="center" @click="limpiarFormulario" />
                        </DxGroupItem>
                    </DxGroupItem>
                </DxForm>
                <DxDataGrid v-if="consulta && testAldrete.length > 0" :ref="dataGridRefKey" v-bind="DefaultDxGridConfiguration" :data-source="testAldrete" :headerFilter="{visible:false, allowSearch: false }" :searchPanel="{visible:false}" :sorting="{mode: 'none'}" :paging="{ enabled:true, pageSize:10 }" :width="'100%'" :height="'100%'">
                    <DxSelection mode="single" />

                    <DxColumn :width="150" data-field="Fecha" format="dd/MM/yyyy" data-type="date" alignment="center" />
                    <DxColumn :width="150" data-field="Puntuacion" caption="Puntación" alignment="center" />
                    <DxColumn :width="150" data-field="Usuario" alignment="center" />
                    <DxColumn :width="150" data-field="Puesto" alignment="center" />
                    <DxColumn :width="150" data-field="Tipo" caption="Tipo de test" alignment="center" />
                </DxDataGrid>
            </form>
        </div>
    </div>
    <vs-row id="texto" class="p-2" v-if="(hojaAnestesia === '' || hojaAnestesia === null) && consulta">
        <p>Seleccione una fecha para mostrar información</p>
    </vs-row>
    <vs-row id="texto" class="p-2" v-if="consulta && hojaAnestesia !== '' && hojaAnestesia !== null && testAldrete.length === 0">
        <p>La hoja seleccionada no cuenta con información para mostrar</p>
    </vs-row>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'
import {
    DxForm,
    DxItem,
    DxGroupItem,
    DxButtonItem,
    DxSimpleItem,
} from 'devextreme-vue/form'
import {
    DxDataGrid,
    DxColumn,
    DxSelection,
} from 'devextreme-vue/data-grid'
import 'devextreme-vue/radio-group';

const dataGridRefKey = 'grid'
const dataEncabezadoRefKey = 'encabezado-form'

export default {
    name: 'TestAldrete',
    components: {
        DxForm,
        DxItem,
        DxGroupItem,
        DxButtonItem,
        DxSimpleItem,
        DxDataGrid,
        DxColumn,
        DxSelection,
    },
    data() {
        return {
            DefaultDxGridConfiguration,
            dataEncabezadoRefKey,
            dataGridRefKey,
            encabezado: {
                ActividadMotora: {
                    Puntos: 0
                },
                Respiracion: {
                    Puntos: 0
                },
                Circulacion: {
                    Puntos: 0
                },
                Consciencia: {
                    Puntos: 0
                },
                Color: {
                    Puntos: 0
                }
            },
            submitButtonOptions: {
                text: 'Guardar datos',
                type: 'success',
                icon: 'save',
                width: 200,
                useSubmitBehavior: true,
                disabled: false
            },
            clearButtonOptions: {
                text: 'Limpiar registro',
                type: 'danger',
                icon: 'trash',
                width: 200,
                useSubmitBehavior: false,
                disabled: false,
                onClick: this.limpiarFormulario
            },
            actividadRadio: {},
            catalogo: [],
            actividad: [],
            respiracion: [],
            circulacion: [],
            consciencia: [],
            color: [],
            tipo: [{
                name: 'Egreso',
                value: 0
            }, {
                name: 'Ingreso',
                value: 1
            }],
            tipoValor: '',
            testAldrete: []
        }
    },
    props: {
        SerieAdmision: null,
        CodigoAdmision: null,
        NumeroExpediente: null,

        StatusAdmision: null,
        consulta: null, //Variable para indicar si el componente se usará para consulta -> 'true' o para ingreso -> 'false'
        hojaAnestesia: null, //Variable para obtener la hoja cuando se utilice para consulta
    },
    methods: {
        handleSubmit(e) {
            e.preventDefault()
            if (this.SerieAdmision && this.CodigoAdmision) {
                let postData = {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    TipoIngreso: this.tipoValor,
                    Items: this.mapTest(),
                }
                this.axios.post("/app/v1_ExpedienteEvolucion/InsertarTestAldrete", {
                    ...postData
                }).then(() => {
                    this.limpiarFormulario()
                })
            }
        },
        getCatalogo() {
            this.axios.post('/app/v1_ExpedienteEvolucion/CatalogosTestAldrete', {})
                .then(resp => {
                    this.catalogo = resp.data.json.map((x) => {
                        x.Item = x.PuntosItem + ' - ' + x.Item
                        return {
                            ...x
                        }
                    })
                    this.actividad = this.catalogo.filter((item) => {
                        return item.CodigoCategoria === 1
                    })
                    this.respiracion = this.catalogo.filter((item) => {
                        return item.CodigoCategoria === 2
                    })
                    this.circulacion = this.catalogo.filter((item) => {
                        return item.CodigoCategoria === 3
                    })
                    this.consciencia = this.catalogo.filter((item) => {
                        return item.CodigoCategoria === 4
                    })
                    this.color = this.catalogo.filter((item) => {
                        return item.CodigoCategoria === 5
                    })
                })
        },
        limpiarFormulario() {
            this.getTipo()
            this.encabezado = {
                ActividadMotora: {
                    Puntos: 0
                },
                Respiracion: {
                    Puntos: 0
                },
                Circulacion: {
                    Puntos: 0
                },
                Consciencia: {
                    Puntos: 0
                },
                Color: {
                    Puntos: 0
                }
            }
            this.getCatalogo()
        },
        getDetalleRegistro() {
            this.axios.post('/app/v1_ExpedienteEvolucion/ConsultaTestAldrete', {
                    Id: this.hojaAnestesia,
                })
                .then(resp => {
                    this.testAldrete = []
                    if (resp.data.json.length > 0) {
                        this.testAldrete = resp.data.json.map((x) => {
                            return {
                                Fecha: x.FechaRegistro,
                                Puntuacion: x.Puntuacion,
                                Usuario: x.Colaborador,
                                Puesto: x.PuestoRegistra,
                                Tipo: x.Clasificacion
                            }
                        })
                    }
                })
        },
        valueChanged(e) {
            if (e.value.CodigoCategoria === 1) {
                this.encabezado.ActividadMotora = {
                    Puntos: e.value.PuntosItem,
                    Codigo: e.value.CodigoItem
                }
            }
            if (e.value.CodigoCategoria === 2) {
                this.encabezado.Respiracion = {
                    Puntos: e.value.PuntosItem,
                    Codigo: e.value.CodigoItem
                }
            }
            if (e.value.CodigoCategoria === 3) {
                this.encabezado.Circulacion = {
                    Puntos: e.value.PuntosItem,
                    Codigo: e.value.CodigoItem
                }
            }
            if (e.value.CodigoCategoria === 4) {
                this.encabezado.Consciencia = {
                    Puntos: e.value.PuntosItem,
                    Codigo: e.value.CodigoItem
                }
            }
            if (e.value.CodigoCategoria === 5) {
                this.encabezado.Color = {
                    Puntos: e.value.PuntosItem,
                    Codigo: e.value.CodigoItem
                }
            }
        },
        valueChangedTipo(e) {
            this.tipoValor = e.value.value
        },
        getTipo() {
            this.tipo = [{
                name: 'Egreso',
                value: 0
            }, {
                name: 'Ingreso',
                value: 1
            }]
        },
        mapTest() {
            let i = [];

            i.push({
                CodigoItem: this.encabezado.ActividadMotora.Codigo
            })
            i.push({
                CodigoItem: this.encabezado.Respiracion.Codigo
            })
            i.push({
                CodigoItem: this.encabezado.Circulacion.Codigo
            })
            i.push({
                CodigoItem: this.encabezado.Consciencia.Codigo
            })
            i.push({
                CodigoItem: this.encabezado.Color.Codigo
            })

            return i
        }
    },
    mounted() {
        this.getCatalogo()
    },
    watch: {
        'hojaAnestesia'(newval) {
            if (newval !== undefined && newval !== '') {
                this.getDetalleRegistro()
            }
        },
    },
    computed: {
        dataGrid: function () {
            return this.$refs[dataGridRefKey].instance;
        },

        puntos: function () {
            return parseInt(this.encabezado.ActividadMotora.Puntos) + parseInt(this.encabezado.Respiracion.Puntos) + parseInt(this.encabezado.Circulacion.Puntos) + parseInt(this.encabezado.Consciencia.Puntos) + parseInt(this.encabezado.Color.Puntos)
        }
    },
}
</script>

<style>
#aldrete .dx-item-content .dx-box-item-content {
    place-content: baseline !important;
}

#aldrete .dx-invalid .dx-radiobutton-icon::before {
    border-color: red !important
}

#aldrete .dx-item-content .dx-box-item-content {
    place-content: center;
    padding: 5px !important;
    padding-right: 10px !important;
}
</style>
