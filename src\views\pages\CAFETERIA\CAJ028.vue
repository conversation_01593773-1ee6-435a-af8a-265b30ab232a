<template>
    <vx-card title="FaceContingencia">
        <div class="content content-pagex">
            <vs-divider></vs-divider>
            <div class="flex mb-5 ">
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                    <div class="div-input">
                        <ValidationProvider name="serie" rules="required|max:3" class="required" v-slot="{ errors }">
                            <label class="typo_label label-static">Serie</label>
                            <vs-input class="w-full" style="padding:0" v-model="info.SerieFactura" v-on:blur="HabilitarFact()" autofocus :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"></vs-input>
                        </ValidationProvider>
                    </div>   
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                    <div class="div-input">
                        <ValidationProvider name="numero" rules="required" class="required" v-slot="{ errors }">
                            <label class="typo_label label-static">No. de Factura</label>
                            <vs-input class="w-full" type="number" style="padding:0" :disabled="factura" v-on:blur="HabilitarBtn()" v-model="info.NumeroFactura" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"></vs-input>
                        </ValidationProvider>
                    </div>
                </div>
                    <vs-button color="primary" style="height:37px;margin-top:25px" :disabled="buscar" @click= "ConsultarBusquedaXML()">Buscar</vs-button>                    
            </div>
            <div class="flex mb-60 ">
                <vs-textarea rows="10" label="XMLEmisor:"  v-model="datosFac.XMLEmisor"></vs-textarea>                 
            </div>
            <vs-divider></vs-divider>
            <vs-button color="success" style="height:37px;margin-top:25px" @click="ActualizarInformacion()" :disabled="actualizar"  icon-pack="fas" icon="fa-check-circle">Actualizar</vs-button>
        </div>
    </vx-card>
</template>

<script>
export default {
    data() {
        return {
             //Factura en FaceContigencia

            info: {
                Empresa: null,
                NumeroFactura: null,
                SerieFactura: null
            },
            datosFac: {
                XMLEmisor: '',
                ID : ''
            },
            factura: true,
            buscar: true,
            actualizar: true           
        }
    },
    methods: {
        HabilitarFact(){
            this.factura = false
        },
        HabilitarBtn(){
            this.buscar = false
        },
        ConsultarBusquedaXML(){
            this.axios.post( '/app/v1_JefeCaja/XMLEmisorCont', {
                        NumeroFactura: this.info.NumeroFactura,
                        SerieFactura: this.info.SerieFactura 
                })
                .then(resp => {

                    if (resp.data.codigo == 0) {
                         //Mapeo de datos
                        resp.data.json.map(data => {  
                                    this.datosFac.XMLEmisor = data.XMLEmisor;    
                                    this.datosFac.ID  = data.ID;     
                                    this.actualizar = false; 
                                    this.buscar = true                                                       
                                })                        
                    }
                })
                .catch(() => {
                })
        },
        ActualizarInformacion(){
            this.axios.post('/app/v1_JefeCaja/XMLEmisorAct',{
                        Id:  this.datosFac.ID,
                        XML:  this.datosFac.XMLEmisor
                   })
                   .then(resp => {
                        //Limpieza de objetos 
                        if (resp.data.codigo == 0){
                            this.info.SerieFactura= ''; 
                            this.info.NumeroFactura= '', 
                            this.datosFac.XMLEmisor= '';
                            this.actualizar = true; 
                            this.factura = true;
                        }
                    }
        )}
    }
}
</script>