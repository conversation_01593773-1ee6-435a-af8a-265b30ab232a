<template>
<vx-card title="Tabla de Pagos Por Producto" :class="[(editar)?'edicion':'']">
    <vs-popup ref="rechazar" :title="`Editar Producto`" :active.sync="editar" style="z-index:52000" id="div-with-loading" class="vs-con-loading__container" width="200">
        <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
            <div class="flex mb-5 ">
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <ValidationProvider name="SubEspecialidad" rules="required" v-slot="{ errors }">
                        <SM-Buscar v-model.trim="editarInfo.Codigo" label="Código del Producto" api="app/Admision/BusquedaProductos" :api_campos="['Codigo','Nombre']" :api_titulos="['Codigo','Nombre']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :callback_buscar="Consulta().Consulta_Ajenos" :callback_cancelar="Otros().Limpiar_Datos" :api_preload="true" :danger="errors.length>0" :dangertext="(errors.length>0)?errors[0]:null" :disabled_texto="true" :disabled_editar="editar" />
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <ValidationProvider name="Descripcion" rules="required" v-slot="{ errors }">
                        <vs-input class="w-full" label="Descripción" v-model="editarInfo.Descripcion" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"> </vs-input>
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <table>
                        <tr>
                            <td width="250px">
                                <ValidationProvider name="Valor" rules="required|numero_min:1" v-slot="{ errors }">
                                    <vs-input class="w-full" label="Monto" v-model="editarInfo.Valor" type="number" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"> </vs-input>
                                </ValidationProvider>
                            </td>
                            <td width="100px pt-5" style="padding:0px 10px">
                                <vs-button color="success" class="mt-5" icon-pack="fas" :disabled="invalid" @click.native="handleSubmit(Editar().Producto())" icon="fa-save">Editar"</vs-button>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </ValidationObserver>
    </vs-popup>

    <!-- POR SUB ESPECIALIDAD -->
    <div class="card">
        <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
            <h5>Producto</h5>
            <vs-divider></vs-divider>
            <div class="flex mb-5 ">
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <ValidationProvider name="SubEspecialidad" rules="required" v-slot="{ errors }">
                        <SM-Buscar v-model.trim="info.Codigo" label="Código del Producto" api="app/Admision/BusquedaProductos" :api_campos="['Codigo','Nombre']" :api_titulos="['Codigo','Nombre']" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Nombre" :callback_buscar="Consulta().Consulta_Ajenos" :callback_cancelar="Otros().Limpiar_Datos" :api_preload="true" :danger="errors.length>0" :dangertext="(errors.length>0)?errors[0]:null" :disabled_texto="true" :disabled_editar="editar" />
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <ValidationProvider name="Descripcion" rules="required" v-slot="{ errors }">
                        <vs-input class="w-full" label="Descripción" v-model="info.Descripcion" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"> </vs-input>
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                    <table>
                        <tr>
                            <td width="250px">
                                <ValidationProvider name="Valor" rules="required|numero_min:1" v-slot="{ errors }">
                                    <vs-input class="w-full" label="Monto" v-model="info.Valor" type="number" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"> </vs-input>
                                </ValidationProvider>
                            </td>
                            <td width="100px pt-5" style="padding:0px 10px">
                                <vs-button color="success" class="mt-5" icon-pack="fas" :disabled="invalid" @click.native="handleSubmit(Guardar().Producto())" icon="fa-save">Guardar</vs-button>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </ValidationObserver>

        <!-- aranceles -->
        <vs-table2 max-items="10" pagination :data="tablaProductos" tooltip search>
            <template slot="thead">
                <th width="350px">Producto</th>
                <th>Descrición del Estudio</th>
                <th width="150px">Monto</th>
                <th width="100px">Acción</th>
                <!-- <th v-for="(item,key) in encabezado" :key="key" width="550px">{{item.Arancel}}</th> -->
            </template>

            <template slot-scope="{data}">
                <tr :key="indextr" v-for="(tr, indextr) in data">
                    <vs-td2>
                        ({{tr.Codigo}}) {{tr.Producto}}
                    </vs-td2>
                    <vs-td2>
                        {{tr.Descripcion}}
                    </vs-td2>
                    <vs-td2 style="text-align:right" noTooltip>
                        {{ tr.Valor}}
                    </vs-td2>
                    <vs-td2 noTooltip>
                        <vs-button @click="editar = true;editarInfo={...tr}" color="primary" size="small" style="display:inline" class="mr-2" icon-pack="feather" icon="icon-edit"></vs-button>
                    </vs-td2>
                </tr>
            </template>
        </vs-table2>
    </div>
</vx-card>
</template>

<script>
// import bitacora from '@/components/sermesa/funciones/SMBitacora'
export default {
    data() {
        return {
            /**
             * Documentación del módulo
             */
            doc_: [
                'Es utilizado para indicar los productos y los valores a pagar en el semáforo de diagnóstico'
            ],

            info: {
                Codigo: null,
                Descripcion: null,
                Valor: 0
            },

            editar: false,
            editarInfo: {
                Codigo: null,
                Descripcion: null,
                Valor: 0
            },

            /**
             * Tabla 
             */
            tablaProductos: [],

            /**
             * Permisos
             */
            permisos: {
                admin_arancel: false,
                admin_tipopago: false,
                editar: false
            },

        }
    },
    watch: {
        editar(value) {
            if (!value) {
                this.editarInfo.Codigo = null
                this.editarInfo.Descripcion = null
                this.editarInfo.Valor = 0
            }
        }
    },
    methods: {
        //=======================================================================================================
        // CONSULTA
        //=======================================================================================================
        Consulta: function() {
            return {
                /**
                 * Consulta de aranceles
                 * Permite realizar una consulta con todos los ajenos
                 */
                ConsultaProductos: () => {
                    return this.axios.post('/app/admision/TablaProductosListado', {})
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.tablaProductos = resp.data.json
                            }
                        })
                },
            }
        },

        //=======================================================================================================
        // NUEVO
        //=======================================================================================================
        Nuevo: function() {
            return {

            }
        },

        //=======================================================================================================
        // GUARDAR
        //=======================================================================================================
        Guardar: function() {
            return {
                Producto: () => {
                    return this.axios.post('/app/admision/TablaProductosGuardar', this.info)
                        .then(() => {
                            this.Consulta().ConsultaProductos()
                        })
                },

            }
        },

        //=======================================================================================================
        // EDITAR
        //=======================================================================================================
        Editar: function() {
            return {
                Producto: () => {
                    return this.axios.post('/app/admision/TablaProductosEditar', this.editarInfo)
                        .then(() => {
                            this.Consulta().ConsultaProductos()
                        })
                },
            }
        },

        //=======================================================================================================
        // ELIMINAR
        //=======================================================================================================
        Eliminar: function() {
            return {}
        },

        //=======================================================================================================
        // OTROS
        //=======================================================================================================
        Otros: function() {
            return {}
        }
    },
    async mounted() {
        this.permisos.admin_arancel = this.$validar_privilegio('ADMIN_ARANCEL').status
        this.permisos.admin_tipopago = this.$validar_privilegio('ADMIN_TIPOPAGO').status
        this.permisos.editar = this.$validar_privilegio('EDITAR_TABLA').status

        // ajenos/Consulta_TablaHonorarios
        this.Consulta().ConsultaProductos()

    }
}
</script>

<style scoped>
.editar {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 99999;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
}

.editar>div {
    background-color: white;
    padding: 10px;
    border-radius: 5px;
    width: 400px
}

.card {
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    border-radius: 5px;
    padding: 10px;
}
</style>
