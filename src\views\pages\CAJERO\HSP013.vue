<template>
<div>
    <vx-card :title="`Facturación Parcial ${sesion.sesion_sucursal_nombre}`">   
        <vs-row class="w-full">
            <ConfiguracionCaja ref="ConfiguracionCajaRef"
                            @CargarCaja="CargarCaja" :FiltrarAgrupacionCaja=FiltrarAgrupacionCaja
                            TipoCajaSeleccionada="TipoCajaFacturacion" CajaSeleccionada="CajaFacturacion" 
                            class="w-full">
            </ConfiguracionCaja>
        </vs-row>
        <vs-row class="w-full">
            <vs-col class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12">
                <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-4/12 pr-2">
                    <div><b>Tipo Admisión:</b> {{ tipoAdmision }}</div>
                </vs-col>        
                <vs-col class="sm:w-full md:w-full lg:w-8/12 xl:w-8/12">
                    <div v-if="info.seguro !== undefined && info.seguro?.trim()?.length > 0">
                        <p><b>Seguro: </b> {{ info.seguro + ' - ' + info.nombreSeguro }}</p>
                    </div>
                </vs-col>  
                <BusquedaAdmision  class="w-full"  ref="componenteAdmisionesF"   
                        @datos_admision="DatosAdmision"
                        @limpiar_datos_admision="LimpiarDatosClienteAdmision"
                        size = "media-fila"
                        :api_campos="['Serie','Codigo','Nombres','Apellidos','Habitacion']"
                        :api_titulos="['Serie#','Admision#','Nombres','Apellidos','Habitación']"
                        :api_filtro="{'Opcion':'CONSULTA','SubOpcion':'CAJERO','activo':0,'tieneHabitacion':0}"
                        >                
                </BusquedaAdmision>                    
                <vs-row class="w-full">
                    <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 p-1" style="height: 60px;">   
                        <label class="typo__label label-shrink" style="white-space: nowrap; color:#626262; font-size:12px;  padding-bottom:2px; padding-top:0px;"> Tipo Documento  </label>  
                        <DxSelectBox            
                            style="padding: 2px;"
                            :items="documento.tiposDocumentoLista"
                            display-expr="Descripcion"
                            placeholder="Seleccionar.."
                            v-model="documento.tipoDocumento"
                        />    
                    </vs-col>              
                    <vs-col class="sm:w-full md:w-full lg:w-8/12 xl:w-4/12 p-1">
                        <vs-input label="No. documento" 
                                v-model="documento.noDocumento" 
                                class="w-full"
                                @keydown.enter="ValidacionDocumentoTributarioTab()" @keydown.tab.prevent="ValidacionDocumentoTributarioTab()"
                                />      
                    </vs-col>  
                    <vs-col class="sm:w-full md:w-full lg:w-full xl:w-6/12 p-1">
                        <vs-input id="idNombre"
                                label="Nombre"  
                                class="w-full"
                                v-model="documento.nombre" />      
                    </vs-col>  
                </vs-row>    
                <vs-row class="w-full">
                    <vs-col class="sm:w-full md:w-full lg:w-full xl:w-2/12 p-1"></vs-col>
                    <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-4/12 p-1">
                        <vs-input label="E-Mail"   
                                class="w-full" 
                                v-model="documento.correo" />      
                    </vs-col>
                    <vs-col class="sm:w-full md:w-full lg:w-8/12 xl:w-6/12 p-1">
                        <vs-input label="Dirección"   
                                class="w-full" 
                                v-model="documento.direccion" />      
                    </vs-col>
                </vs-row>
                <vs-row class = 'w-full pt-5'>
                    <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 m-1">
                        <vs-button color="success" class="w-full"
                            id="botonFactura"
                            :disabled="!configuracion.activarFacturacion"   
                            :type="facturaSeleccionado?'border':'filled'"                         
                            @keyup.tab="facturaSeleccionado=true"
                            v-on:blur="facturaSeleccionado=false"
                            @click="FacturacionParcial()">
                            Facturar
                        </vs-button>
                    </vs-col>
                    <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 m-1">
                        <vs-button color="warning" class="w-full"
                            @click="LimpiarDatosCliente()">
                            Limpiar
                        </vs-button>
                    </vs-col>
                </vs-row> 
            </vs-col>
            <vs-col class="flex sm:w-full md:w-full lg:w-6/12 xl:w-6/12" >
                <vs-row class="w-full pt-4" style="justify-content: center;">
                    <DxDataGrid ref="cargosDataGridRef"
                                v-bind="DefaultDxGridConfiguration"
                                @selection-changed="onSelectionChanged"
                                :selected-row-keys="cargosSeleccionados"
                                :data-source="cargos" :visible="true"  
                                :showColumnLines="true" :showBorders="true" :searchPanel="{visible: false}" 
                                :rowAlternationEnabled="true" width="100%" 
                                :column-hiding-enabled="true"                                                         
                                :word-wrap-enabled="false">

                            <DxDataGridEditing :allow-updating="false" :allow-adding="false" :allow-deleting="false" mode="none" />
                            <DxDataGridSelection  select-all-mode="allPages" show-check-boxes-mode="always"   mode="multiple"  />
                            <DxDataGridColumn :width="225" caption="Orden" alignment="center">
                                <DxDataGridColumn :width="100" data-field="TipoOrden" :visible="true" caption="Tipo"  :allow-filtering="false" >                                             
                                </DxDataGridColumn>  
                                <DxDataGridColumn :width="125" data-field="Orden" :visible="true" caption="Número" :allow-filtering="false">                                                                                              
                                </DxDataGridColumn>  
                            </DxDataGridColumn>
                            <DxDataGridColumn :width="240" data-field="NombreCategoria" :visible="true" caption="Categoria" :allow-filtering="false">                                                                                              
                            </DxDataGridColumn>                                             
                            <DxDataGridColumn :width="225" data-field="SumofValor" caption="Monto"  :editor-options="{format:'Q #,##0.00'}" format="Q #,##0.00" data-type="number" alignment="right">
                            </DxDataGridColumn>
                            <DxDataGridSummary :calculate-custom-summary="SumarizarTotal">
                                <DxDataGridTotalItem  name="SumaCargosSeleccionados" show-in-column="SumofValor" summary-type="custom"  alignment="right" style="font-size: 10pt;" value-format="Q #,##0.00" data-type="number" display-format="Subtotal: {0}"/>
                                <DxDataGridTotalItem  column="SumofValor"  data-type="text"   alignment="right" style="font-size: 10pt;" :customize-text="()=>{return 'Descuento: ' + $formato_moneda(cargosSeleccionados.length == 0 ? 0.00 : cargosResumen.descuento)}" />                                                    
                                <DxDataGridTotalItem  column="SumofValor"  data-type="text"   alignment="right" style="font-size: 10pt;" :customize-text="()=>{return 'Total: ' + $formato_moneda(cargosSeleccionados.length == 0 ? 0.00 : cargosResumen.total)}" />                                                    
                            </DxDataGridSummary>   
                            <DxDataGridPaging :page-size="15"/>
                            <DxDataGridPager  :show-page-size-selector="true"
                                        :allowed-page-sizes="[15, 30, 45]"
                            />                                               
                        </DxDataGrid>
                </vs-row>    
            </vs-col>
                                
    
        </vs-row>
    </vx-card>
</div>
</template>
<script>
    import BusquedaAdmision from "/src/components/sermesa/modules/admisiones/BusquedaAdmision.vue";    
    import ConfiguracionCaja from "/src/components/sermesa/modules/caja/ConfiguracionCaja.vue"; 
    import {DefaultDxGridConfiguration} from './data';         
    import { ref } from 'vue'
    export default {
        components:{
            BusquedaAdmision,
            ConfiguracionCaja            
        },
        data(){
            return{
                facturaSeleccionado:false,
                FiltrarAgrupacionCaja:['1','2'],   
                DefaultDxGridConfiguration:{
                    ...DefaultDxGridConfiguration,
                    keyExpr:'Id'
                },   
                configuracion:{
                    cajaSeleccionada:null,
                    activarFacturacion: false,
                    activarRecibos: false  
                },
                info:{  serie:null,
                        numeroAdmision:null,
                        paciente: null,
                        habitacion: null,                         
                        tipoDescuento:null,
                        seguro:null,
                        nombreSeguro:null,
                        copago:null,
                        coaseguro:null,
                        dataAdmision:null
                },
                documento:{ tiposDocumentoLista:[],
                            tipoDocumento:null,
                            noDocumento:null,
                            nombre:null,
                            correo:null,
                            direccion:null,
                            validacion:null			
                },	
                cargos:[],
                cargosResumen:{
                    subTotal:null,
                    descuento:null,
                    total:null
                },
                cargosSeleccionados:[]
            }
        },
        methods:{
            async ValidacionDocumentoTributarioTab(){
                await this.ValidacionDocumentoTributario();
                if(this.documento.validacion == 1){
                    const campoNombre = document.getElementById("idNombre");
                    if(campoNombre) campoNombre.focus();
                }
            },
            actualizarCajero(){  
                this.$refs.ConfiguracionCajaRef.CargarCajaInicial();
            },                 
            Init(){
                this.CargarTipoDocumento()
            },            
            SumarizarTotal(options){
                if (options.name === 'SumaCargosSeleccionados') {
                    if (options.summaryProcess === 'start') {
                        options.totalValue = 0;
                    }

                    const isRowSelected = options.component.isRowSelected(options.value?.Id);

                    if (options.summaryProcess === 'calculate' && isRowSelected) {
                        options.totalValue += Number(options.value.SumofValor);
                    }
                }
            },
            onSelectionChanged(e){
                const allSelectedRowKeys = e.selectedRowKeys;
                if(allSelectedRowKeys && allSelectedRowKeys.length > 0){
                    this.cargosSeleccionados = allSelectedRowKeys
                    this.FiltrarCargosPorAdmision()
                }else{
                    this.cargosSeleccionados = []
                }
                e.component.refresh();
            },            
            FacturacionParcial(){
                if(!this.cargosSeleccionados || this.cargosSeleccionados.length == 0){
                    this.MostrarError('Seleccione al menos un cargo.',3000)
                    return
                }

                if(!this.ValidarVariable(this.configuracion.cajaSeleccionada.SerieFac)){
                    this.MostrarError('Configure un cajero para continuar.',3000)
                    return
                }

                if(!this.ValidarVariable(this.documento.validacion)){
                    this.MostrarError('Ingresar un documento valido para realizar la factura.',3000)
                    return
                }
                const botonFactura = document.getElementById("botonFactura");
                if(botonFactura) botonFactura.blur();

                let tipoReceptor = this.documento.tipoDocumento.Valor

                this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision',
                                {opcion:'I',
                                 subOpcion:4,
                                 serieAdmision:this.info.serie,
                                 numeroAdmision:this.info.numeroAdmision,
                                 serieFactura:this.configuracion.cajaSeleccionada.SerieFac,
                                 tipoReceptor:tipoReceptor,
                                 nombreFactura:this.documento.nombre,
                                 documento:this.documento.noDocumento,
                                 direccion:this.documento.direccion,
                                 correo:this.documento.correo,
                                 listaCargos:this.cargosSeleccionados.toString()
                                }
                            )
                        .then(resp=>{
                            if(resp.data && resp.data.Serie && resp.data.Factura){
                                this.Fel(resp.data.Serie,resp.data.Factura);
                            } 
                            this.$vs.dialog({
                                                type: 'confirm',
                                                color: 'success',
                                                title: 'Facturación Parcial',
                                                acceptText: 'Aceptar',
                                                cancelText: 'Cancelar',
                                                text: 'Factura Generada '+resp.data.Serie +'-'+resp.data.Factura,       
                                                accept:()=>{ this.LimpiarDatosCliente()},
                                                cancel:()=>{ this.LimpiarDatosCliente()}                                        
                                            })
                        })                
            },
            ObtenerCargosPorAdmision(){
                
                if(!this.ValidarVariable(this.info.serie) || !this.ValidarVariable(this.info.numeroAdmision)){
                    return
                }

                if(!this.ValidarVariable(this.configuracion.cajaSeleccionada.SerieFac)){
                    this.MostrarError('Configure un cajero para continuar.',3000)
                    return
                }
              

                this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision',
                                {opcion:'C',
                                 subOpcion:8,
                                 serieAdmision:this.info.serie,
                                 numeroAdmision:this.info.numeroAdmision,
                                 serieFactura:this.configuracion.cajaSeleccionada.SerieFac,
                                }
                            )
                        .then(resp=>{
                            
                            if(resp.data.json && resp.data.json.length > 0 && resp.data.json[0].TipoOrden){
                                this.cargos = resp.data.json                                
                                this.cargosResumen.descuento = resp.data.json[0].TotalDescuento
                                this.cargosResumen.total = resp.data.json[0].TotalFactura
                                
                                this.cargosSeleccionados = Array.from(this.cargos, (cargo)=>cargo.Id)
                            }else if(resp.data.json && resp.data.json.length > 0 && resp.data.json[0].descripcion){
                                this.MostrarError(resp.data.json[0].descripcion,3000)
                            }  
                        })                
            },
            FiltrarCargosPorAdmision(){
                
                if(!this.ValidarVariable(this.info.serie) || !this.ValidarVariable(this.info.numeroAdmision)){
                    return
                }

                if(!this.ValidarVariable(this.configuracion.cajaSeleccionada.SerieFac)){
                    this.MostrarError('Configure un cajero para continuar.')
                    return
                }
              

                this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision',
                                {opcion:'C',
                                 subOpcion:8,
                                 serieAdmision:this.info.serie,
                                 numeroAdmision:this.info.numeroAdmision,
                                 serieFactura:this.configuracion.cajaSeleccionada.SerieFac,
                                 listaCargos:this.cargosSeleccionados.toString()
                                }
                            )
                        .then(resp=>{
                            
                            if(resp.data.json && resp.data.json.length > 0 && resp.data.json[0].TipoOrden){                                                               
                                this.cargosResumen.descuento = resp.data.json[0].TotalDescuento
                                this.cargosResumen.total = resp.data.json[0].TotalFactura                                     
                                this.cargosDataGrid.refresh()                                                                 
                            }else if(resp.data.json && resp.data.json.length > 0 && resp.data.json[0].descripcion){
                                this.MostrarError(resp.data.json[0].descripcion,3000)
                            }  
                        })                
            },  
            Fel(serieFactura,numeroFactura){
                this.axios.post('/app/v1_FacturaElectronica/GeneraFel',
                                {serieFactura: serieFactura,
                                    numeroFactura: numeroFactura
                                })                                  
                            .then(resp=>{
                                    if(resp.data.codigo == 0 && resp.data.json[0].codigo == 0){
                                        this.MostrarSatisfactorio(resp.data.json[0].descripcion);
                                        this.GenerarFactura(serieFactura,numeroFactura);                                                                               
                                    }else{
                                        this.MostrarError(resp.data.json[0].descripcion,3000);
                                    }
                                })                                 
            },    
            async GenerarFactura(Serie,NumeroFactura) {
                let reporte = "Impresion FEL"

                let postData = {
                    SerieFactura: Serie ,
                    NumFactura: NumeroFactura,
                    nombrereporte: this.configuracion.cajaSeleccionada.FormatoImpresion
                }


                this.$reporte_modal({
                    Nombre: reporte,
                    Opciones: {
                        ...postData
                    }
                })                

            },                    
            CargarCaja(cajaSeleccionada){                
                this.configuracion.cajaSeleccionada   = cajaSeleccionada.Cajero
                this.configuracion.activarFacturacion = cajaSeleccionada.Factura
                this.configuracion.activarRecibos     = cajaSeleccionada.Recibo
            },
            DatosAdmision(datos){
                this.info.dataAdmision = datos
                this.info.serie = datos.Serie
                this.info.numeroAdmision = datos.Codigo
                this.info.paciente = datos.Paciente
                this.info.habitacion = datos.Habitacion 
                this.info.tipoDescuento = datos.TipoDescuento
                this.info.seguro = datos.Seguro
                this.info.nombreSeguro = datos.Nombre

                this.documento.tipoDocumento = ref(this.documento.tiposDocumentoLista.find(doc=>doc.Descripcion == 'NIT'))                
                this.documento.noDocumento = datos.Nit
                this.documento.nombre = datos.NombreFactura
                this.documento.direccion = datos.DireccionFactura

                this.ObtenerCargosPorAdmision()
                this.ValidacionDocumentoTributario()
            },
            async ValidacionDocumentoTributario(){
                if(this.EsVacia(this.documento.tipoDocumento)){
                    this.MostrarError('Seleccione un tipo de documento para el cliente',3000);
                    return;
                }
                if(this.EsVacia(this.documento.noDocumento)){
                    this.MostrarError('Ingrese el NIT/DPI del cliente',3000);
                    return;
                }
                await
                this.axios.post('/app/v1_caja/ValidacionDocumentoTributario',{
                                    tipoReceptor: parseInt(this.documento.tipoDocumento.Valor),
                                    documento: this.documento.noDocumento
                                })
                          .then(resp=>{
                                    if ( resp.data.codigo == 0 && resp.data.json.length > 0 && resp.data.json[0].codigo == 0){
                                        this.documento.nombre = resp.data.json[0].NOMBRE
                                        this.documento.correo = resp.data.json[0].EMAIL
                                        this.documento.direccion = resp.data.json[0].DIRECCION
                                        this.documento.validacion = 1
                                    }else if ( resp.data.codigo == 0 && resp.data.json.length > 0 && resp.data.json[0].codigo != 0){
                                        this.MostrarError(resp.data.json[0].descripcion,3000);
                                        this.documento.nombre = null
                                        this.documento.correo = null
                                        this.documento.direccion = null
                                        this.documento.validacion = null
                                    }
                                    else if(resp.data.json.length == 0){
                                        this.documento.nombre = null
                                        this.documento.correo = null
                                        this.documento.direccion = null
                                        this.documento.validacion = null
                                        this.MostrarError('No se encontraron datos para el documento ingresado',3000);
                                    }
                                })
                            
            },
            CargarTipoDocumento(){
                this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision', {
                                    opcion:'C',
                                    subOpcion:2,
                                    agrupacion:'TipoReceptor'
                                })
                          .then(resp => {                            
                                    if (resp.data && resp.data.json.length > 0){
                                        this.documento.tiposDocumentoLista = resp.data.json;
                                    }
                                }
                               )
            },  
            MostrarError(mensaje,tiempo=2000){
                this.$vs.notify({
                    time:tiempo,
                    title: 'Facturación Parcial',
                    color: 'danger',
                    text:mensaje,
                    position: 'top-center'
                })
            },
            MostrarSatisfactorio(mensaje){
                this.$vs.notify({
                    time:2000,
                    title: 'Facturación Parcial',
                    color: 'success',
                    text:mensaje,
                    position: 'top-center'
                })
            },
            EsVacia:(variable)=>{
                return variable?false:true;
            },
            ValidarVariable(entrada){   
                if(typeof(entrada) == 'number'){
                    return entrada ? true : false;
                }else if(typeof(entrada) == 'string'){
                    return entrada && entrada.length > 0 ? true : false;
                }else if(typeof(entrada) == 'object'){
                    return entrada ? true : false;
                }            
                return false;
            },
            LimpiarDatosCliente(){
                this.$refs.componenteAdmisionesF.limpiar_campos()
            },
            LimpiarDatosClienteAdmision(){
                this.info.serie=null
                this.info.numeroAdmision=null
                this.info.paciente= null
                this.info.habitacion= null                        
                this.info.tipoDescuento=null
                this.info.seguro=null
                this.info.nombreSeguro=null
                this.info.copago=null
                this.info.coaseguro=null
                this.info.dataAdmision=null
                
                this.documento.tipoDocumento=null
                this.documento.noDocumento=null
                this.documento.nombre=null
                this.documento.correo=null
                this.documento.direccion=null
                this.documento.validacion=null		                
                this.cargos=[]
                
                this.cargosResumen.subTotal=null
                this.cargosResumen.descuento=null
                this.cargosResumen.total=null                
                this.cargosSeleccionados=[]
            }
        },
        computed:{
            cargosDataGrid(){
                return this.$refs['cargosDataGridRef'].instance;
            },
            tipoAdmision(){
                return this.info.tipoDescuento == 'N' ? 'Privada' : this.info.tipoDescuento == 'R' ? 'Reingreso' : 
                       this.info.tipoDescuento == 'Q' ? 'Paquete' : this.info.tipoDescuento == 'S' ? 'Seguro' : 
                       this.info.tipoDescuento == 'E' ? 'Empleado' : ''
            },
            sesion() {
                return this.$store.state.sesion
            }
        },
        mounted(){
            this.Init()
        },
        activated() {
            window.addEventListener('focus', this.actualizarCajero);   
        },
        deactivated() {
            window.removeEventListener('focus',this.actualizarCajero);
        },
    }
</script>
<style>
.vs-row-especial{
    display: block !important;
}
/* Estilo tablas dev express */
.dx-popup-title {
    background-color: #f4eade !important;
    color: #2f496e !important;
}

.dx-list-item>#itemsMenuDrawer {
    color: #2988bc !important;
    background-color: #f4eade !important;
}

.dx-list-item-selected>#itemsMenuDrawer {
    color: #f4eade !important;
    background-color: #ed8c72 !important;
}

.dx-scrollable-container {
    touch-action: auto !important;
}

/*Ancho mínmo de los grid*/
#Contenido .dx-datagrid {
    min-width: 302px;
}

/**Modal actualizacion de peso y talla */
#popupTallaPeso .vs-popup {
    width: 400px !important;
}

.dx-datagrid-headers td {
    vertical-align: middle !important;
}

.dx-resizable {
    display: inline-grid;
}

.dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed)>td {
    background-color: #00ccff !important;
    color: black !important;
}

.dx-datagrid-rowsview .dx-row.dx-data-row.dx-column-lines.dx-state-hover>td {
    background-color: #79e4ff !important;
    color: black !important;
    font-size: 16px;
}

.dx-datagrid-headers {
    background-color: linen !important;
    color: black !important;
    font-weight: bold;
}

td {
    vertical-align: middle !important;
}
/* Fin Estilo tablas dev express */
</style>