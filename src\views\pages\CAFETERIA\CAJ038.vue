<template>
<vx-card type = "2" title="Modificar Ajeno A Un Cargo" class="justify-center ">    
    <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
        <form ref="formModificarOrden" method="post" @submit.prevent="handleSubmit(modificar_ajeno())">
            <div class="flex flex-wrap">
                <div class="w-full md:w-full lg:w-4/12 xl:w-2/12 m-2">
                    <label>Admisión:</label> 
                    &nbsp;&nbsp;
                    <label style="color:black">{{info.admision}}</label>                                 
                </div>
                <div class="w-full md:w-full lg:w-6/12 xl:w-4/12 m-2 lg:pr-6">
                    <label>Paciente:</label> 
                    &nbsp;&nbsp;
                    <label style="color:black">{{info.paciente}}</label>      
                </div>
                <div class="w-full md:w-full lg:w-1/12 xl:w-5/12"></div>
                <vs-divider position="left">Orden</vs-divider>
                <div class="w-full md:w-4/12 lg:w-1/12 xl:w-1/12 m-2" tabindex="1">
                    <ValidationProvider label="*Tipo" rules="required" v-slot="{ errors }">
                        <SM-Buscar class="w-full" label="*Tipo" v-model="info.tipoOrden" api="app/v1_JefeCaja/ObtenerTiposOrdenes" :api_campos="['Codigo','Nombre']" :api_titulos="['Tipo de Orden','Descripción']" :api_filtro="{'Activa':0,'opcion':'C','subOpcion':'1'}" 
                                   api_campo_respuesta="Codigo" :api_preload="true"  :callback_buscar="cargar().datos_tipo_orden" 
                                   :disabled_busqueda="info.desactivarCamposOrden"
                                   :disabled_texto="info.desactivarCamposOrden"
                                   :danger="errors.length > 0" 
                                   :danger-text="(errors.length > 0) ? errors[0] : null"/>                                
                    </ValidationProvider>                                
                    <div v-if="info.descripcionOrden && info.descripcionOrden!=''" style="background-color:#ecf0f1;">
                        {{ info.descripcionOrden }}
                    </div>
                </div>
                <div class="w-full md:w-4/12 lg:w-4/12 xl:w-2/12 m-2" tabindex="2">  
                    <ValidationProvider label="*Orden" rules="required" v-slot="{ errors }">
                        <vs-input class="w-full" type="text" @keydown.enter="cargar_paciente()" @keydown.tab="cargar_paciente();" v-model="info.orden" label="*Orden" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"/>                            
                    </ValidationProvider> 
                </div>
                <div class="w-full md:w-4/12 lg:w-1/12 xl:w-1/12 m-2" tabindex="3">
                    <ValidationProvider label="*Linea" rules="required" v-slot="{ errors }">
                        <vs-input id="linea" type="number" @keydown.enter="cargar_paciente()" @keydown.tab.prevent="cargar_paciente()" label="*Linea" class="w-full" v-model="info.lineaCargo" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null"/>
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-full lg:w-5/12 xl:w-7/12 m-2"></div>

                <div class="w-full" ><vs-divider position="left">Ajeno Actual</vs-divider></div>
                <div class="w-full md:w-full lg:w-4/12 xl:w-2/12 m-2" tabindex="4">                    
                    <vs-input label="Médico(Ajeno)" class="w-full" :value="info.ajeno" :disabled="true"/>                  
                </div>
                <div class="w-full md:w-full lg:w-6/12 xl:w-4/12 m-2 lg:pr-6">
                    <vs-input label="Nombre:" class="w-full" :value="info.nombreAjeno" :disabled="true"/>
                </div>
                <div class="w-full md:w-full lg:w-1/12 xl:w-5/12 "></div>

                <div class="w-full" ><vs-divider position="left">Ajeno Modificado</vs-divider></div>
                <div class="w-full md:w-full lg:w-4/12 xl:w-2/12 m-2" tabindex="4">     
                    <ValidationProvider rules="required" v-slot="{ errors }">
                        <SM-Buscar  class="w-full" v-model="info.ajenoModificado" label="Médico(Ajeno)" api="app/v1_JefeCaja/Busqueda_Ajenos" :api_campos="['Codigo','Nombre','Apellido','Tipo','Especialidad']" :api_titulos="['Codigo','Nombre','Apellido','Tipo','Especialidad']" api_campo_respuesta="Codigo" :api_preload="true"  :callback_buscar="cargar_ajeno" 
                                    :disabled_busqueda="info.desactivarBotonModificacion" :disabled_texto="info.desactivarBotonModificacion"                                    
                                   />  
                        <span style="position:relative;top:-2px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>                   
                    </ValidationProvider>                                    
                </div>
                <div class="w-full md:w-full lg:w-6/12 xl:w-4/12 m-2 lg:pr-6">
                    <vs-input label="Nombre:" class="w-full" :value="info.nombreAjenoModificado" disabled/>
                </div>
                <div class="w-full md:w-full lg:w-1/12 xl:w-5/12 "></div>

                <div class="w-full md:w-full lg:w-4/12 xl:w-2/12 m-2">
                    <vs-input label="Valor:" class="w-full" :value="$formato_moneda(info.valor)" disabled/>
                </div>
                <div class="w-full md:w-full lg:w-8/12 xl:w-10/12"></div>

                <div class="w-full md:w-full lg:w-10/12 xl:w-6/12 m-2" tabindex="5">
                    <ValidationProvider rules="max:40|required" v-slot="{ errors }">
                        <vs-input size="large" label="*Motivo" class="w-full"  v-model="info.razon" 
                                  :danger="errors.length > 0" 
                                  :danger-text="(errors.length > 0) ? errors[0] : null"/>
                    </ValidationProvider>
                </div>                  
                <div class="w-full m-2" tabindex="5">
                    <vs-button color="success" class="md:w-full lg:w-4/12 xl:w-1/12 m-2"
                        :disabled="info.desactivarBotonModificacion"
                        @click="handleSubmit(modificar_ajeno(invalid))">
                        Modificar Médico
                    </vs-button>

                    <vs-button class="md:w-full lg:w-4/12 xl:w-1/12 m-2" color="warning" @click="confirmar_limpia_campos()">
                        Limpiar Campos
                    </vs-button>
                </div>
            </div>
        </form>
    </ValidationObserver>        
</vx-card>
</template>

<script>

export default {
    data(){
        return {
            info:{
                tipoOrden: null,
                descripcionOrden: null,
                orden: null,
                admision: null,
                serie:null,
                noAdmision:null,
                paciente: null,
                razon: null,
                lineaCargo: null,
                valor:null,
                nombreAjeno: null,
                ajeno: null,
                nombreAjenoModificado: null,
                ajenoModificado: null,
                desactivarBotonModificacion: true,
                desactivarCamposOrden: false
            },
        };
    },computed:{

    },methods:{
        consultar_orden(datos){  
            this.info.orden = datos.orden                  
            this.cargar_paciente()
        },
        cargar_paciente(){            
            if (this.cargar().validar_campos()) {                                 
                this.cargar().datos_orden()
            }else{                               
                this.info.admision = null 
                this.info.paciente = null  
                this.info.desactivarBotonModificacion = true
            }
        },
        cargar_ajeno(data){
            if(data){                
                this.info.nombreAjenoModificado = (data.Nombre?data.Nombre.trim():'') +' '+(data.Apellido?data.Apellido.trim():'') 
            }else{                
                this.info.nombreAjenoModificado  = 'SIN REFERENCIA'
            }
           
        },
        cargar(){
            return {
                datos_tipo_orden: (datos)=>{
                    this.info.tipoOrden = datos.Codigo
                    this.info.descripcionOrden = datos.Nombre
                    this.cargar_paciente()
                },
                datos_orden: () => {
                    this.axios.post('/app/v1_JefeCaja/modificar_ajeno_cargo',{
                                            iOpcion: 'C',
                                            iSubOpcion: '1',                                            
                                            tipoOrden: this.info.tipoOrden,
                                            orden: this.info.orden,
                                            linea: this.info.lineaCargo
                                        })
                              .then((resp) => {
                                                                                                   
                                                    if(resp.data.json.length == 0){
                                                        this.$vs.notify({
                                                            position: 'top-center',
                                                            time: 4000,
                                                            color: '#B71C1C',
                                                            title: 'Alerta',
                                                            text: 'Cargo NO Existe ó ya fue provisionado para pago'
                                                        })
                                                        this.limpiar_campos_entrada()
                                                        this.info.desactivarBotonModificacion = true  
                                                        return 
                                                    }

                                                    if(!resp.data.json[0].Ajeno  || resp.data.json[0].Ajeno.trim() == '' ){
                                                        this.$vs.notify({
                                                            position: 'top-center',
                                                            time: 4000,
                                                            color: '#B71C1C',
                                                            title: 'Alerta',
                                                            text: 'Este Cargo NO Tiene Ajeno, Verifique # Orden'
                                                        })
                                                        this.limpiar_campos_entrada()
                                                        this.info.desactivarBotonModificacion = true  
                                                        return                                                         
                                                    }

                                                   
                                                    this.info.admision = resp.data.json[0].SerieAdmision + '-' + resp.data.json[0].Admision
                                                    this.info.serie = resp.data.json[0].SerieAdmision
                                                    this.info.noAdmision = resp.data.json[0].Admision
                                                    this.info.paciente = resp.data.json[0].Nombre + ' ' + resp.data.json[0].Apellido + ' ' + resp.data.json[0].ApellidoCasada   
                                                    this.info.ajeno = resp.data.json[0].Ajeno
                                                    this.info.nombreAjeno = resp.data.json[0].NombreMedico.trim() + ' ' + resp.data.json[0].ApellidoMedico.trim()
                                                    this.info.valor = resp.data.json[0].Valor 
                                                    this.info.desactivarBotonModificacion = false   
                                                    this.info.desactivarCamposOrden = false                     
                                                    
                                                    this.info.ajenoModificado = this.info.ajeno 
                                                    this.info.nombreAjenoModificado = this.info.nombreAjeno
                                                }
                                    )
                },
                validar_campos:()=>{
                    return this.info.tipoOrden && this.info.descripcionOrden && this.info.orden  && this.info.lineaCargo                      
                }
            }
        },
        modificar_ajeno(invalid){
            if(invalid){
                this.$vs.notify({
                            position: 'top-center',
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'Falta ingresar campos obligatorios',
                        })
                return
            }  
            
            this.axios.post('/app/v1_JefeCaja/modificar_ajeno_cargo',{
                                            iOpcion: 'M',
                                            iSubOpcion: '1',   
                                            serie: this.info.serie,
                                            noAdmision: this.info.noAdmision,                                         
                                            tipoOrden: this.info.tipoOrden,
                                            orden: this.info.orden,
                                            linea: this.info.lineaCargo,
                                            ajeno: this.info.ajenoModificado,
                                            razon: this.info.razon
                                        })
                              .then((resp) => {
                                                    let error 
                                                    let mensajeError                                                    
                                                    if(resp.data.json && resp.data.json.length != 0){
                                                        error = resp.data.json[0].tipo_error
                                                        mensajeError = resp.data.json[0].descripcion
                                                    }else{
                                                        error = resp.data.tipo_error
                                                        mensajeError = resp.data.descripcion
                                                    }

                                                    if(error && error != 0){
                                                        this.$vs.notify({
                                                            position: 'top-center',
                                                            time: 4000,
                                                            color: '#B71C1C',
                                                            title: 'Alerta',
                                                            text: mensajeError
                                                        })                                                                                      
                                                    }
                                                    else{ //TipoOrden,CodigoOrden                                                                                                                    

                                                        this.confirmar_limpia_campos();                                                                          
                                                    }     
                                                }
                                    )
            
        },
        limpiarOrden(){
            this.limpiar_campos()
            this.info.desactivarBotonModificacion = true
            this.info.desactivarCamposOrden = false
        }
        ,
        limpiar_campos_entrada(){
            this.info.admision = null
            this.info.serie = null
            this.info.noAdmision = null
            this.info.paciente = null
            this.info.ajeno = null
            this.info.ajenoModificado = null
            this.info.nombreAjeno = null
            this.info.nombreAjenoModificado = null
            this.info.valor = null
            this.info.razon = null  
        }
        ,
        limpiar_campos(){            
            this.info.tipoOrden = null
            this.info.descripcionOrden = null
            this.info.orden = null
            this.info.admision = null
            this.info.serie = null
            this.info.noAdmision = null
            this.info.paciente = null
            this.info.ajeno = null
            this.info.ajenoModificado = 
            this.info.nombreAjeno = null
            this.info.nombreAjenoModificado = null
            this.info.valor = null
            this.info.razon = null       
            this.info.lineaCargo = null     
        },
        limpiar_campos_exito(){            
            this.info.orden = null
            this.info.admision = null
            this.info.paciente = null
            this.info.razon = null            
        },
        confirmar_limpia_campos(){
            this.$vs.dialog({
                            type: 'confirm',
                            color: 'danger',
                            title: 'Confirmación',
                            acceptText: 'Limpiar',
                            cancelText: 'Cancelar',
                            text: `¿Desea limpiar los campos?`,
                            accept: () => {
                                this.limpiar_campos()
                                this.info.desactivarBotonModificacion = true
                                this.info.desactivarCamposOrden = false
                            }
                        })
        }
    },mounted(){    
    },watch:{
    }
}

</script>