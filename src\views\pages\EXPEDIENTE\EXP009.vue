<template>
    <DxForm v-bind="formConfig" id="FormPesoTalla" :form-data.sync="FormData"/>
</template>
<script>
import DxForm from 'devextreme-vue/form'

export default {
    name: 'DatosAdmision',
    emits: ['onSavedData','onCancel'],
    components: {
        DxForm,
    },
    data() {
        return {
            updating: false,
            formConfig: {
                labelMode: 'floating',
                colCount: this.modalMode? 2:3,
                width: '100%',
                minColWidth: 200,
                screenByWidth: (width)=> {
                    if (width < 540)  return "xs"
                    if (width < 992)  return "sm"
                    if (width < 1200) return "md"
                    return "lg";
                },
                items: [
                    {
                        editorType: 'dxNumberBox',
                        editorOptions: {
                            type: 'decimal', 
                            max: 1000, min: 0, 
                            format: '#.#### kg',
                            isValid: this.valido1
                        },
                        label: {text: 'Peso'},
                        width: 100,
                        dataField: 'Peso',
                        placeholder: 'Ingrese el peso en kilogramos',
                    },
                    {
                        editorType: 'dxNumberBox',
                        editorOptions: {
                            type: 'decimal', 
                            max: 300, 
                            min: 0, 
                            format: '#.## cm',
                        },
                        label: {text: 'Estatura'},
                        width: 100,
                        placeholder: 'Ingrese la estatura en centímetros',
                        dataField: 'Estatura',
                    },
                    // 

                    {   
                        itemType:'button',
                        buttonOptions:{
                            text: 'Grabar',
                            icon: "save",
                            type:'success',
                            onClick: this.Grabar,
                            useSubmitBehavior: true,
                            disabled: this.updating
                        },
                        width: 50,
                        verticalAlignment: 'bottom',
                    },        
                ],
            },    
        }       
    },
    props: {
        //no se hereda de EXPBase por si lo quieren usar en otro lado
        SerieAdmision: null,
        CodigoAdmision: null,
        Peso: null,
        Estatura: null,
        modalMode: {
                    type: Boolean,
                    default: false,
                },
        /**'none', 'button' ó blur */
        saveMode: {
                    type: String,
                    default: 'button',
                },
    },
    methods: {
        
        Grabar() {
            if(Boolean(this.SerieAdmision) && Boolean(this.CodigoAdmision) && (this.HasMasa || this.HasTalla ))
            {
//Si no se quiere actualizar un valor no se envia en el body
                let postData = {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                }
                if(this.HasMasa)
                    postData.Peso = this.FormData.Peso
                if(this.HasTalla)
                    postData.Estatura = this.FormData.Estatura

                this.axios.post("/app/v1_ExpedienteEvolucion/ActualizarPesoTalla", postData)
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        postData.SerieAdmision = this.SerieAdmision,
                        postData.CodigoAdmision = this.CodigoAdmision,
                        this.$emit('onSaved', {...postData, ...this.CalculaIMC(postData.Peso, postData.Estatura)})
                    }
                })
            }
        },
        CalculaIMC(peso, talla) {
            if (!(peso))
                peso=1
            if (!(talla))
                talla = 100
            let IMC = 0
            let DescripcionIMC = ''
            try {
                talla = talla / 100
                IMC = peso / (talla * talla)
                if (IMC < 18.5)
                    DescripcionIMC = 'BAJO PESO'
                else if (IMC >= 18.5 && IMC < 25)
                    DescripcionIMC = 'NORMAL'
                else if (IMC >= 25 && IMC < 29.9)
                    DescripcionIMC = 'SOBREPESO'
                else if (IMC >= 30 && IMC <= 35)
                    DescripcionIMC = 'OBESIDAD EN PRIMER GRADO'
                else if (IMC > 35 && IMC <= 40)
                    DescripcionIMC = 'OBESIDAD EN SEGUNDO GRADO'
                else if (IMC > 40)
                    DescripcionIMC = 'OBESIDAD EN TERCER GRADO'
                else
                    DescripcionIMC = 'Revise peso y estatura'
            } catch {
                IMC = 0
                DescripcionIMC = 'Revise peso y estatura'
            }
            return { IMC, DescripcionIMC }
        },
    },
    computed: {
        FormData() {
            return {
                Peso: this.Peso,
                Estatura: this.Estatura
            }
        },
        HasMasa(){
            return Boolean(this.FormData.Peso)
        },
        HasTalla(){
            return Boolean(this.FormData.Estatura)
        }
    },
}
</script>
<style>
#FormPesoTalla .dx-field-item{
    padding-right: 5px;
    padding-left: 5px;
}
#FormPesoTalla{
    max-width: 450px;
}
</style>