import Vue from 'vue'
import App from './App.vue'

// Vuesax Component Framework
import Vuesax from 'vuesax'
import 'material-icons/iconfont/material-icons.css' //Material Icons
import 'vuesax/dist/vuesax.css'; // Vuesax

//Font Awesome
import { library } from '@fortawesome/fontawesome-svg-core'

import { fas } from '@fortawesome/free-solid-svg-icons'
import { far } from '@fortawesome/free-regular-svg-icons'

import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'

library.add(fas);
library.add(far);

Vue.component('font-awesome-icon', FontAwesomeIcon);

Vue.use(Vuesax)

// Vuex Store
import store from './store/store'

import VueSocketIO from 'vue-socket.io'
Vue.use(new VueSocketIO({
    debug: false,
    connection: 'https://sighos-linea.hospitaleslapaz.com/',
    vuex: {
        store,
        actionPrefix: 'SOCKET_',
        mutationPrefix: 'SOCKET_'
    },
    // options: { path: "/my-app/" } //Optional options
}))


// axios
import axios from 'axios'
import { AxiosInit, AxiosResponse } from './axios.js';
AxiosInit(axios, store)
AxiosResponse(axios, store)


// Vue.prototype.$http = axios

//axios
import VueAxios from 'vue-axios'
Vue.use(VueAxios, axios)


import Progress from 'vue-multiple-progress'
Vue.component('VmProgress', Progress)

//MASK
import VueMask from 'v-mask'
Vue.use(VueMask);

import vBlur from 'v-blur'
Vue.use(vBlur)

//import money from 'v-money'
//Vue.use(money)

// almacenamiento
// import Storage from 'vue-web-storage';
// Vue.use(Storage, {
//   prefix: 'sermesa_', // default `app_`
//   //drivers: ['session','local'], // default 'local'
// });
document.addEventListener('contextmenu', event => event.preventDefault());


// Theme Configurations
import '../themeConfig.js'

// Globally Registered Components
import './globalComponents.js'

// Styles: SCSS
import './assets/scss/main.scss'

// Tailwind
import '@/assets/css/main.css'

// Vue Router
import router from './router'

// Prototipos
import './prototypes'

// Vuejs - Vue wrapper for hammerjs
import {
    VueHammer
} from 'vue2-hammer'
Vue.use(VueHammer)

// VeeValidate
import "./VeeValidate";
// import VeeValidate from 'vee-validate';
// Vue.use(VeeValidate);

// PrismJS
import 'prismjs'
import 'prismjs/themes/prism-tomorrow.css'

import VueExpandableImage from 'vue-expandable-image'
Vue.use(VueExpandableImage)

import VueTheMask from 'vue-the-mask'
Vue.use(VueTheMask)

import 'devextreme/dist/css/dx.light.css';

// Feather font icon
require('./assets/css/iconfont.css')

// FONT AWESOME
require('./assets/css/font-awesome.min.css')

// GLOBAL
require('./assets/css/global.css')



Vue.directive('clickoutside', {
    inserted: (el, binding, vnode) => {
  // assign event to the element
      el.clickOutsideEvent = function (event) {
        // here we check if the click event is outside the element and it's children
        if (!(el == event.target || el.contains(event.target))) {
          // if clicked outside, call the provided method
          vnode.context[binding.expression](event)
        }
      }
  // register click and touch events
      document.body.addEventListener('click', el.clickOutsideEvent)
      document.body.addEventListener('touchstart', el.clickOutsideEvent)
    },
    unbind: function (el) {
  // unregister click and touch events before the element is unmounted
      document.body.removeEventListener('click', el.clickOutsideEvent)
      document.body.removeEventListener('touchstart', el.clickOutsideEvent)
    },
    stopProp(event) {
      event.stopPropagation()
    },
})


// Vue select css
// Note: In latest version you have to add it separately
// import 'vue-select/dist/vue-select.css';
Vue.config.productionTip = false

new Vue({
    store,
    router,
    data() {
        return {
            socket_registado: false
        }
    },
    sockets: {
        connect: function () {
            // console.log('socket connected')
            this.$store.dispatch('sesionSocket', true)
            this.registrar_socket()
        },
        disconnect: function () {
            this.$store.dispatch('sesionSocket', false)
        },
        conexion_usuario: function () {
            //al momento de poseer ya usuario en el socket verifica si se cuenta con appPC
            this.$socket.emit('estado')
        },
        resultado_estado: function (data) {
            this.$store.dispatch('appPCSocket', data)
        },
        //conexion web despues apppc
        conexion_appPC: function (data) {
            this.$store.dispatch('appPCSocket', data)
        },
        desconexion_appPC: function () {
            this.$store.dispatch('appPCSocket', null)
        },

    },
    methods: {
        registrar_socket() {
            const internalIp = require('internal-ip');            

            var currentUrl = window.location.pathname;
            const sesion = this.$store.state.sesion
            // alert(sesion.corporativo)
            if (currentUrl != '/pages/login') {
                if (sesion.corporativo <= 0) {
                    location.replace("/pages/login");
                } else {
                    if (!this.socket_registado) {
                        (async () => {
                            const ip = await internalIp.v4()
                            this.$socket.emit('registrar_usuario', {
                                corporativo: sesion.corporativo,
                                usuario: sesion.usuario,
                                ip:ip
                            })
                            this.$router.beforeEach((resp, from, next) => {
                                this.$socket.emit('usuario_ubicacion', { ubicacion: resp.matched[resp.matched.length-1].path })
                                next()
                            })
                        })()                     
                    }
                }
            } else {
                if (sesion.corporativo > 0) location.replace("/");
            }
            this.socket_registado = true
        }
    },
    mounted() {
        // validando url y permisos
        this.registrar_socket()
    },
    created(){
        Vue.prototype.$vs = this.$vs;
    },
    render: h => h(App)
}).$mount('#app')
