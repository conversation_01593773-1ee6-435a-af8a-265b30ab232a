<template>
<vx-card title="Admisión Diagnostíco">
    <vs-alert active="true" color="success" class="mt-5" icon-pack="feather" icon="icon-star" v-if="$route.query.nuevo == undefined">
        Nueva Admisión Diagnostíco
    </vs-alert>

    <vs-popup id="contentreport" classContent="popup-generar" title="Paciente" :active.sync="info.paciente_mostrar" style="z-index:99999;height:100%">
        <Pacientes :paciente="info.paciente" :buscar="true" />
    </vs-popup>

    <vs-popup id="contentreport" classContent="popup-generar" title="Facturación" :active.sync="info.adicional_mostrar" style="z-index:99999;height:100%">
        <Facturacion :paciente="info.paciente" :nuevo="true" />
    </vs-popup>

    <div class="content content-pagex">

        <vs-divider position="left">Tipo Admisión</vs-divider>

        <vx-input-group class="">
            <ul class="centerx">
                <li v-for="(item,index) in Tipos" :key="index">
                    <vs-radio v-model="TipoAdmision" :vs-value="item.Id">{{item.Nombre}}</vs-radio>
                </li>
            </ul>
        </vx-input-group>

        <!-- {{TipoAdmision}} -->

        <vs-divider />

        <div v-if="TipoAdmision!=null">
            <div class="flex flex-wrap" style="padding:10px 20px" v-if="!$route.query.nuevo == undefined">
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <vs-input label="Serie" v-mask="'AA'" class="w-full" v-on:change="info.serie=info.serie.toUpperCase()" v-model="info.serie" disabled />
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <label class="vs-input--label">Código</label>
                    <vx-input-group class="">
                        <vs-input v-model="info.codigo" disabled />
                        <template slot="append">
                            <div class="append-text btn-addon">
                                <button type="submit" v-show="false" name="button"></button>
                                <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="info.paciente_mostrar=true" icon="icon-search" v-if="!nuevo"></vs-button>
                                <!-- <vs-button color="success" icon-pack="feather" @click="info.paciente_mostrar=true" icon="icon-plus"></vs-button> -->
                            </div>
                        </template>
                    </vx-input-group>

                </div>
            </div>

            <div class="flex flex-wrap" style="padding:10px 20px">
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <vs-button id="button-with-loading" color="primary" class="w-full" icon-pack="feather" @click="info.paciente_mostrar=true" icon="icon-search">Paciente</vs-button>
                </div>
                <div class="w-full md:w-4/5 lg:w-4/5 xl:w-4/5 ">
                    <div style="padding:10px 0 0 10px">
                        Juan Pedro Rivera
                        <!-- <div style="font-size:13px">RN</div> -->
                    </div>
                </div>
            </div>

            <div class="flex flex-wrap" style="padding:10px 20px">
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <label class="vs-input--label">Código Médico</label>
                    <vx-input-group class="">
                        <vs-input v-model="info.codigo" disabled />
                        <template slot="append">
                            <div class="append-text btn-addon">
                                <button type="submit" v-show="false" name="button"></button>
                                <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="info.paciente_mostrar=true" icon="icon-search"></vs-button>
                            </div>
                        </template>
                    </vx-input-group>
                </div>
                <div class="w-full md:w-4/5 lg:w-4/5 xl:w-4/5 ">
                    <div style="padding:30px 0 0 10px">
                        Dr. Juan Pedro Rivera
                        <!-- <div style="font-size:13px">RN</div> -->
                    </div>
                </div>
                <div class="w-full">
                    <label class="vs-input--label">Correo Médico</label>
                    <vx-input-group class="">
                        <vs-input v-model="info.paciente" disabled />
                        <template slot="append">
                            <div class="append-text btn-addon">
                                <vs-button>
                                    <vs-checkbox v-model="checkBox1">No Tiene Correo</vs-checkbox>
                                </vs-button>
                            </div>
                        </template>
                    </vx-input-group>
                </div>
            </div>

            <div>
                <div class="flex flex-wrap" style="">
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 px-2 py-2 ">

                        <div style="padding:10px;border:1px solid #ccc;min-height:250px">
                            <vs-divider position="left">Facturación</vs-divider>
                            <vs-input v-model="info.codigo" class="w-full" label="Nit:" />
                            <vs-input v-model="info.codigo" class="w-full" label="Nombre:" />
                            <vs-input v-model="info.codigo" class="w-full" label="Dirección:" />
                        </div>
                    </div>
                    <div class="w-full md:w-1/2  lg:w-1/2 xl:w-1/2 px-2 py-2">
                        <div style="padding:10px;border:1px solid #ccc;min-height:250px">
                            <vs-divider position="left">Información Paciente</vs-divider>
                            <label class="vs-input--label">Correo Paciente</label>
                            <vx-input-group class="">
                                <vs-input v-model="info.paciente" disabled />
                                <template slot="append">
                                    <div class="append-text btn-addon">
                                        <vs-button>
                                            <vs-checkbox v-model="checkBox1">No Tiene Correo</vs-checkbox>
                                        </vs-button>
                                    </div>
                                </template>
                            </vx-input-group>
                            <vs-input v-model="info.codigo" class="w-full" label="Teléfono:" />

                        </div>
                    </div>
                </div>

            </div>

            <div class="flex flex-wrap" style="">
                <div class="w-full md:w-4/5 lg:w-4/5 xl:w-4/5 px-2 py-2">
                    <div style="padding:10px;border:1px solid #ccc;">
                        <div class="flex flex-wrap">
                            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                                <label class="vs-input--label">Compañia</label>
                                <vx-input-group class="">
                                    <vs-input v-model="info.paciente" disabled />
                                    <template slot="append">
                                        <div class="append-text btn-addon">
                                            <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="info.paciente_mostrar=true" icon="icon-search"></vs-button>
                                        </div>
                                    </template>
                                </vx-input-group>
                            </div>
                            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 " v-show="false">
                                <label class="vs-input--label">Paquete</label>
                                <vx-input-group class="">
                                    <vs-input v-model="info.paciente" disabled />
                                    <template slot="append">
                                        <div class="append-text btn-addon">
                                            <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="info.paciente_mostrar=true" icon="icon-search"></vs-button>
                                        </div>
                                    </template>
                                </vx-input-group>
                            </div>
                            <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5" style="padding:30px 0 0 10px">
                                Seguro xx
                            </div>
                            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                                <vs-input v-model="info.codigo" class="w-full" label="Porcentaje:" />
                            </div>
                            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                                <vs-input v-model="info.codigo" class="w-full" label="Descuento:" />
                            </div>
                            
                        </div>
                    </div>
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 px-2 py-2">
                    <div style="padding:10px;border:1px solid #ccc;">
                        <vs-input label="NivelPrecios" class="w-full" v-on:change="info.serie=info.serie.toUpperCase()" v-model="info.serie" />
                    </div>
                </div>
            </div>

            <div class="flex flex-wrap" style="padding:10px 20px">
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <vs-input label="Usuario" class="w-full" v-model="info.usuario" disabled />
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <vs-input label="Fecha Ingreso" class="w-full" v-model="info.ingreso" disabled />
                </div>
            </div>
            <vs-divider></vs-divider>

            <vs-button style="float:right"  @click="guardar_informe(true)" v-if="permisos.crear"> Guardar</vs-button>
            <div style="clear:both"></div>
        </div>
        <!-- </vx-card> -->
    </div>
</vx-card>
</template>

<script>
// require styles
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'

// import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";

export default {
    data() {
        return {
            nuevo: true,
            permisos: {
                crear: false
            },
            examenes: [],
            info: {
                serie: null,
                codigo: null,
                paciente: "",
                paciente_mostrar: false,
                adicional_mostrar: false,
                sexo: null,
                dx: [{
                    codigo: null,
                    descripcion: null,
                    wrnq: null,
                    espera: null,
                    inhab: null,
                }],
                referencia: 1,
                paquete: null,
                usuario: null,
                ingreso: null
            },
            TipoAdmision: null,
            Tipos: [{
                    Id: 1,
                    Nombre: 'Privada'
                },
                {
                    Id: 2,
                    Nombre: 'Seguro'
                },
                {
                    Id: 3,
                    Nombre: 'Paquete Ra-La'
                }
            ],
            editorOption: {
                modules: {
                    toolbar: '#toolbar'
                },
                placeholder: 'Informe'
            },
        }
    },
    props: {
        IdFuncionalidad: {
            default: null
        }
    },
    components: {
        // Multiselect,
        Pacientes: () => import('@/views/pages/ADMISION/ADM005.vue'),
        Facturacion: () => import('@/views/pages/ADMISION/ADM006.vue')

    },
    methods: {

    },
    mounted() {
        // this.nuevo = (this.$route.query.nuevo == undefined) ? true : this.$route.query.nuevo
        // this.permisos.crear = this.$validar_privilegio('CREAR').status && this.nuevo
    }
}
</script>

<style scoped>
.centerx {
    display: flex;
}

.centerx li {
    margin-right: 10px
}

table {
    width: 100%;
}

th {
    border-bottom: 1px solid #ccc;
}

td {
    padding: 5px;
    height: 30px;
    border-bottom: 1px solid #ccc;
    border-right: 1px solid #ccc;
}

td:nth-child(1) {
    border-left: 1px solid #ccc;
}

.center li {
    display: inline !important;
    padding: 2px 10px;
}

.button {
    margin: 0px !important;
}

.btnx {
    margin-left: 10px !important;
    background: red;
    border-radius: 5px 0px 0px 5px;
}

.btn-drop {
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: red;

}

.menu-funcionalidad {
    /* padding-top:0 !important; */
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    padding: 15px;
    display: inline;
    float: right;
    border-radius: 10px;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 10px;
}

.menu-funcionalidad td {
    text-align: center;
    padding-right: 10px;
}

.vs-switch {
    height: 30px;
    width: 60px !important;
}

.vs-circle-switch {
    height: 25px;
    width: 25px;
}

.vs-switch--icon {
    font-size: 15px
}

.vs-switch.vs-switch-active .vs-switch--circle {
    margin-left: calc(100% - 30px);
}
</style>
