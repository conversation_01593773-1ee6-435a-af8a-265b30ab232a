<template>
<vx-card style="display:none">

    <buscador buscador_titulo="Buscador / Laboratorio" ref="buscar_laboratorio" :bloqueo="false" :api="'app/inventario/BusquedaLaboratorio'" :campos="['Codigo','Nombre']" :titulos="['Codigo','Nombre']" :multiselect="false" :api_reload="true" />
    <buscador buscador_titulo="Buscador / Proveedor" ref="buscar_proveedor" :bloqueo="false" :api="'app/inventario/BusquedaProveedor'" :campos="['Codigo','Nombre','Nit']" :titulos="['Codigo','Nombre','Nit']" :multiselect="false" :api_reload="true" api_disable_seleccion="Estado!='Activo'"/>
    <buscador buscador_titulo="Buscador / Departamento" ref="buscar_departamento" :bloqueo="false" :api="'app/inventario/BusquedaDepartamento'" :campos="['Codigo','Nombre']" :titulos="['Codigo','Nombre']" :multiselect="false" :api_reload="true" />
    <buscador buscador_titulo="Buscador / SubDepartamento" ref="buscar_subdepartamento" :bloqueo="false" :api="'app/inventario/BusquedaSubDepartamento'" :api_filtro="{iddepartamento:codedepartamento}" :campos="['Codigo','Nombre']" :titulos="['Codigo','Nombre']" :multiselect="false" :api_reload="true" />
    <buscador buscador_titulo="Buscador / Grupo" ref="buscar_grupo" :bloqueo="false" :api="'app/inventario/BusquedaGrupo'" :campos="['Codigo','Nombre']" :titulos="['Codigo','Nombre']" :multiselect="false" :api_reload="true" />
    <buscador buscador_titulo="Buscador / SubGrupo" ref="buscar_subgrupo" :bloqueo="false" :api="'app/inventario/BusquedaSubGrupo'" :api_filtro="{idgrupo:codegrupo}" :campos="['Codigo','Nombre']" :titulos="['Codigo','Nombre']" :multiselect="false" :api_reload="true" />
    <buscador buscador_titulo="Buscador / Producto" ref="buscar_producto" :bloqueo="false" :api="'app/inventario/BusquedaProducto'" :campos="['Codigo','Nombre']" :titulos="['Codigo','Nombre']" :multiselect="false" :api_reload="true" />
    <buscador buscador_titulo="Buscador / Genericos" ref="buscar_Genericos" :bloqueo="false" :api="'app/inventario/BusquedaGenericos'" :campos="['Codigo','Nombre']" :titulos="['Codigo','Nombre']" :multiselect="false" :api_reload="true" />

    <!-- ========================================================================================================================================================== -->
    <!-- POPUP INICIO -->
    <!-- ========================================================================================================================================================== -->
    <vs-popup classContent="popup-example" :title="'Productos'" style="z-index:99999;" :active.sync="show">
        <!-- data-vv-scope="step1" -->
        <!-- <ValidationObserver v-slot="{ invalid ,handleSubmit,errors}"> -->
        <ValidationObserver v-slot="{invalid,handleSubmit,errors}">
            <form>

                    <div style="padding:10px;border-radius:5px; border:1px solid #ccc;">

                        <div class="vx-card mb-base">
                            <div class="vx-card__header">
                                <div class="vx-card__title">
                                    <div class="flex items-end px-3">	
                                        <span class="mr-2 feather-icon select-none relative">
                                                <i class="feather icon-user-check"></i>
                                            </span>
                                        <span class="font-medium text-lg leading-none">Datos Generales</span>
                                    </div>
                        
                                </div>
                            </div>
                            <vs-divider ></vs-divider>
                            <div style="top:-8px;left:30px" class="vs-con-loading__container">
                                <div class="vx-card__body">
                                    <div class="vx-row">
                                        <div id="account-info-col-1" class="vx-col flex-1">
                                            <table>
                                                <tr>
                                                    <td class="font-semibold">Código:</td>
                                                    <td>{{usuario}}</td>
                                                </tr>
                                                <tr>
                                                    <td class="font-semibold">Usuario:</td>
                                                    <td>{{nombreusuario}}</td>
                                                </tr>
                                                <tr>
                                                    <td class="font-semibold">Fecha:</td>
                                                    <td>{{fechacreacion}}</td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    <vs-divider ></vs-divider>

                    <div class="flex flex-wrap">

                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <ValidationProvider name="ListadoClase" rules="required" v-slot="{ errors }">
                                <P class="font-bold mb-3">Clase:</P>
                                <multiselect v-model="selectClase" :options="ListadoClase" @input="onChangeClase" track-by="Codigo" label="Nombre" placeholder="Seleccione Clase"></multiselect>
                                <span style="position:relative;top:-2px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                            </ValidationProvider>
                        </div>

                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <ValidationProvider name="ListadoCategoria" rules="required" v-slot="{ errors }">
                                <P class="font-bold mb-3">Categoría:</P>
                                <multiselect v-model="selectCategoria" :options="ListadoCategoria" @input="onChangeCategoria" track-by="Codigo" label="Nombre" placeholder="Seleccione Categoria"></multiselect>
                                <span style="position:relative;top:-2px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                            </ValidationProvider>
                        </div>

                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <ValidationProvider name="codeProducto" ref="codeProducto" rules="required" v-slot="{ errors }">
                                <vs-input disabled v-model="codeProducto" label="Código Hospital" class="w-full font-bold mb-3" />
                                <span style="position:relative;top:-12px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                            </ValidationProvider>
                        </div>

                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <!-- v-validate="'required|numeric|min:0|max:4'" -->
                            <ValidationProvider name="CodigoFarmacia" rules="required|max:4" v-slot="{ errors }">
                                <vs-input v-model="Cod_Farmacia" placeholder="Codigo Farmacia" label="Código Farmacia" class="w-full font-bold mb-3" @focus="removeMask(Cod_Farmacia)" />
                                <span style="position:relative;top:-12px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                            </ValidationProvider>
                        </div>

                    </div>

                    <div class="flex flex-wrap">
                        <div class="w-full">
                            <ul class="demo-alignment">
                                <li>
                                    <P class="font-bold mb-3">Opciones:</P>
                                    <vs-checkbox style="float:left;padding:0px 0px" icon-pack="feather" icon="icon-check" color="success" v-for="(option, indextr) in ListadoProductoOpc" v-model="selectProductoOpc[option.Codigo]" :key="indextr">
                                        {{option.Nombre}}
                                    </vs-checkbox>

                                </li>
                            </ul>
                        </div>
                    </div>

                    <vs-divider></vs-divider>

                    <div class="flex flex-wrap">
                        <div class="w-full">
                            <ValidationProvider name="Marca-Comercial" rules="required|max:80" v-slot="{ errors }">
                                <vs-textarea counter="80"  :label="(codeClasificacion=='S'?'Nombre de Servicio':'Nombre (Marca Comercial)')"  :counter-danger.sync="counterDanger" v-model="Marca" />
                                <span style="position:relative;top:-18px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                            </ValidationProvider>
                        </div>
                    </div>

                    <div class="flex flex-wrap">
                        <div class="w-full">
                            <ValidationProvider name="Principio-Activo" rules="required|max:80" v-slot="{ errors }">
                                <vs-textarea counter="80"   :label="(codeClasificacion=='S'?'Descripción':'Principio Activo')" :counter-danger.sync="counterDanger" v-model="Principio_Activo" />
                                <span style="position:relative;top:-18px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                            </ValidationProvider>
                        </div>
                    </div>

                    <vs-divider></vs-divider>

                    <div class="flex flex-wrap">
                        <div class="w-full">
                            <P class="font-bold mb-3">Utilizado en:</P>
                            <ul class="centerx">
                                <li>
                                    <vs-radio style="float:left;padding:1px 5px" v-for="(fila, index) in ListadoUtilizadoOrigen" :key="index" :vs-name="'UtilizadoOrigen_'+index" v-model="selectUtilizadoOrigen" :vs-value="fila.Codigo">
                                        {{fila.Nombre}}
                                    </vs-radio>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="flex flex-wrap">

                        <div v-if="codeClasificacion=='P'" class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <label class="vs-input--label">Laboratorio:{{NombreLaboratorio}}</label>
                            <ValidationProvider name="Laboratorio" :rules="(codeProducto.substring(0,1)!='1'?null:'required')" v-slot="{ errors }">
                                <vx-input-group class="">
                                    <vs-input v-if="codeClasificacion=='P'" v-model="codeLaboratorio" disabled @change="buscar_laboratorio()" name="Laboratorio" />
                                    <template slot="append">
                                        <div class="append-text btn-addon">
                                            <button type="submit" v-show="false" name="button"></button>
                                            <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_laboratorio()" icon="icon-search"></vs-button>
                                        </div>
                                    </template>
                                </vx-input-group>
                                <span style="position:relative;top:-2px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                            </ValidationProvider>
                        </div>

                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <label class="vs-input--label">Sub-Categoría:{{Nombredepartamento}}</label>
                            <ValidationProvider name="Sub-Categoría" rules="required" v-slot="{ errors }">
                                <vx-input-group class="">
                                    <vs-input v-model="codedepartamento" disabled @change="buscar_departamento()" name="SubCategoría" />
                                    <template slot="append">
                                        <div class="append-text btn-addon">
                                            <button type="submit" v-show="false" name="button"></button>
                                            <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_departamento()" icon="icon-search"></vs-button>
                                        </div>
                                    </template>
                                </vx-input-group>
                                <span style="position:relative;top:-2px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                            </ValidationProvider>
                        </div>

                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <label class="vs-input--label">Proveedor:{{NombreProveedor}}</label>
                            <ValidationProvider name="Proveedor" rules="required" v-slot="{ errors }">
                                <vx-input-group class="">
                                    <vs-input v-model="codeProveedor" disabled @change="buscar_proveedor()" name="Proveedor" />
                                    <template slot="append">
                                        <div class="append-text btn-addon">
                                            <button type="submit" v-show="false" name="button"></button>
                                            <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_proveedor()" icon="icon-search"></vs-button>
                                        </div>
                                    </template>
                                </vx-input-group>
                                <span style="position:relative;top:-2px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                            </ValidationProvider>
                        </div>

                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <label class="vs-input--label">Sub-SubCategoría:{{Nombresubdepartamento}}</label>
                            <vx-input-group class="">
                                <vs-input v-model="codesubdepartamento" disabled @change="buscar_subdepartamento()" />
                                <template slot="append">
                                    <div class="append-text btn-addon">
                                        <button type="submit" v-show="false" name="button"></button>
                                        <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_subdepartamento()" icon="icon-search"></vs-button>
                                    </div>
                                </template>
                            </vx-input-group>
                        </div>

                        <div v-show="codeClasificacion=='P' && codeProducto.substring(0,1)!='7'" class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <P>Familia:</P>
                            <!-- <P class="font-bold mb-3">Familia:</P> -->
                            <multiselect v-model="selectFamilia" :options="ListadoFamilia" track-by="Codigo" label="Nombre" name="ListadoFamilia" placeholder="Seleccione Familia"></multiselect>
                        </div>

                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <P>Presentación:</P>
                            <ValidationProvider name="Presentacion-Producto" rules="required" v-slot="{ errors }">
                                <multiselect v-model="selectPresentacion" :options="ListadoPresentacion" track-by="Nombre" label="Descripcion" name="PresentacionProducto" placeholder="Seleccione Presentación"></multiselect>
                                <span style="position:relative;top:-2px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                            </ValidationProvider>
                        </div>

                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <label class="vs-input--label">Grupo:{{Nombregrupo}}</label>
                            <vx-input-group class="">
                                <vs-input v-model="codegrupo" disabled />
                                <template slot="append">
                                    <div class="append-text btn-addon">
                                        <button type="submit" v-show="false" name="button"></button>
                                        <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_grupo()" icon="icon-search"></vs-button>
                                    </div>
                                </template>
                            </vx-input-group>
                        </div>

                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <label class="vs-input--label">Sub-Grupo:{{Nombresubgrupo}}</label>
                            <vx-input-group class="">
                                <vs-input v-model="codesubgrupo" disabled />
                                <template slot="append">
                                    <div class="append-text btn-addon">
                                        <button type="submit" v-show="false" name="button"></button>
                                        <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_subgrupo()" icon="icon-search"></vs-button>
                                    </div>
                                </template>
                            </vx-input-group>
                        </div>

                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <label class="vs-input--label">Unidad Genérico:{{prod.GenericosNombre}}</label>
                            <vx-input-group class="">
                                <vs-input v-model="prod.GenericosCodigo" disabled />
                                <template slot="append">
                                    <div class="append-text btn-addon">
                                        <button type="submit" v-show="false" name="button"></button>
                                        <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="buscar_Genericos()" icon="icon-search"></vs-button>
                                    </div>
                                </template>
                            </vx-input-group>
                        </div>

                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <ValidationProvider name="Costo_IVA:" rules="required:max:11" v-slot="{ errors }">
                                <vs-input  id="monto-solicitado" v-model="Costo_IVA" @change="validacion_costo"  @keydown="numbersOnlyWithDecimal(Costo_IVA,$event)" @blur="validacion_costo"  @focus="removeMoneyMask(Costo_IVA)" label="Costo con IVA" class="w-full font-bold mb-3"  />
                                <span style="position:relative;top:-12px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                            </ValidationProvider>
                        </div>

                        <div v-if="codeClasificacion=='P'" class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <vs-input v-model="Costo_Competencia" label="Costo Competencia:" class="w-full" type="number" @focus="removeMask1(Costo_Competencia)"  />
                        </div>

                        <div v-if="codeClasificacion=='P'" class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <vs-input v-model="ExistenciaMin" @change="Validacion_ExistenciaMin" label="Existencia Minima:" class="w-full" type="number"   @keydown="numbersOnly(ExistenciaMin,$event)" @blur="Validacion_ExistenciaMin"  @focus="removeMask2(ExistenciaMin)"  />
                        </div>

                        <div v-if="codeClasificacion=='P'" class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <vs-input v-model="ExistenciaMax" @change="Validacion_ExistenciaMax" label="Existencia Maxima:" class="w-full" type="number"   @keydown="numbersOnly(ExistenciaMax,$event)" @blur="Validacion_ExistenciaMax" @focus="removeMask3(ExistenciaMax)"  />
                        </div>

                        <!-- Campos Nuevos -->

                        <div v-if="codeClasificacion=='P'" class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <vs-input v-model="VentaHospital" label="Unidad de venta en hospital:" class="w-full" />
                        </div>

                        <div v-if="codeClasificacion=='P'" class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <vs-input v-model="UnidadCompra" label="Unidad de compra(Presentación del producto):" class="w-full" />
                        </div>

                        <div v-if="codeClasificacion=='P'" class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <vs-input v-model="RegistroSanitario" label="Registro Sanitario:" class="w-full" />
                        </div>

                        <!-- COMENTADO NO SE USA EN LA CREACION. -->
                        <!-- <div  v-if="codeClasificacion=='P'" class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <vs-input v-model="atc" label="Código ATC" class="w-full" />
                        </div> -->

                    </div>

                    <vs-divider></vs-divider>

                    <div class="flex flex-wrap">
                        <div class="w-full">
                            <P class="font-bold mb-3">Tipo Bodega:</P>
                            <ValidationProvider name="Tipo-Bodega" rules="required" v-slot="{ errors }">
                                <ul class="centerx">
                                    <li>
                                        <vs-radio style="float:left;padding:1px 5px" v-for="(fila, index) in ListadoTipoBodega" :key="index" v-model="selectTipoBodega" name="TipoBodega" :vs-name="'TipoBodega_'+index" :vs-value="fila.Codigo" @input="onChangeTipoBodega">
                                            {{fila.Nombre}}
                                        </vs-radio>
                                    </li>
                                </ul>
                                <span style="position:relative;top:-2px;left:0px;display:inline-block" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                            </ValidationProvider>
                        </div>
                    </div>

                    <div class="vx-col md:w-1/1 w-full mt-5">
                        <P class="font-bold mb-3">Categoría para Facturación:</P>
                        <ValidationProvider name="Listado-Categoria-Facturacion" rules="required" v-slot="{ errors }">
                            <multiselect v-model="selectCategoriasFact" :options="ListadoCatFacturacion" track-by="Codigo" label="Nombre" name="ListadoCatFacturacion" placeholder="Seleccione Tipo Bodega"></multiselect>
                            <span style="position:relative;top:-2px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                        </ValidationProvider>
                    </div>

                    <div class="flex flex-wrap">
                        <div class="w-full">
                            <P class="font-bold mb-3" v-show="showTiposede">Tipo Sede Cocina:</P>
                            <ul class="centerx">
                                <li>
                                    <vs-radio style="float:left;padding:1px 5px" v-for="(fila, index) in ListadoTipoSedeCocina" :key="index" v-model="selectTipoSedeCocina" :vs-name="'TipoSedeCocina_'+index" :vs-value="fila.Codigo">
                                        {{fila.Nombre}}
                                    </vs-radio>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div v-if="bloqueoBusqueda" class="flex flex-wrap">
                        <div v-if="codeClasificacion=='P'" class="w-full">
                            <ul class="demo-alignment">
                                <li>
                                    <P  class="font-bold mb-3">Características del Producto:</P>
                                    <vs-checkbox style="float:left;padding:2px 2px" icon-pack="feather" icon="icon-check" color="success" v-for="(option, indextr) in ListadoCaractProcucto" v-model="selectCaractProducto[option.Codigo]" :key="indextr">
                                        {{option.Nombre}}
                                    </vs-checkbox>

                                </li>
                            </ul>
                        </div>
                    </div>

                    <div v-if="codeClasificacion=='P'" class="flex flex-wrap">
                        <vs-divider></vs-divider>
                        <div class="w-full">
                            <ValidationProvider name="Descripcion-Producto" rules="required|max:160" v-slot="{ errors }">
                                <vs-textarea counter="160" label="Descripcion Producto" :counter-danger.sync="counterDanger" name="DescripcionProducto" v-model="Descripcion_Producto" />
                                <span style="position:relative;top:-18px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>
                            </ValidationProvider>
                        </div>
                    </div>

                    <div v-if="bloqueoBusqueda">
                        <vs-divider class="font-bold mb-3">Archivos </vs-divider>

                        <div class="flex flex-wrap archivos">
                            <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5" v-for="(i,index) in list" :key="index" v-show="list.length !=0" >
                                <expandable-image class="image" :src="'data:image/png;base64, ' +i.src" v-if="i.archivo_nombre">
                                </expandable-image>

                                <vs-button color="danger" :id="'scanner_'+index" class="icon-eliminar" :disabled="i.archivo_nombre==null" v-on:click="remove_scanner(index)" icon-pack="fas" icon="fa-trash" v-if="i.archivo_nombre"></vs-button>
                            
                                <div v-else style="width:100%">
                                    <div v-if="!image">
                                        <div style="width:100%">
                                            <img src="@/assets/images/others/notfound.png" alt="" style="max-width:100%;max-height:100%">
                                        </div>
                                        <input accept=".png, .jpg, .jpeg" id="fileInput" type="file" style="display:none" @change="onFileChange($event);"  />
                                        <vs-button class="icon-camara" onclick="document.getElementById('fileInput').click()" icon-pack="fas" icon="fa-camera-retro"></vs-button>
                                    </div>
                                    <div v-else style="display:flex">
                                         <div style="width:100%">
                                            <img :src="image" class="image" style="max-width:100%;max-height:100%;" />
                                        </div>
                                        <div style="background-color:rgba(0,0,0,0.5);padding:10px;position:absolute;width:100%;text-align:center;top:45%">
                                            <vs-button color="danger" :disabled="bloqueoScanner" style="position:relative;top:0;left:0;margin-right:5px;display:inline-block" class="icon-eliminar" v-on:click="removeImage" icon-pack="fas" icon="fa-trash"></vs-button>
                                            
                                            <vs-button color="primary" style="position:relative;top:0;left:0;display:inline-block" type="filled" @click="guardarScanner" icon-pack="fas" icon="fa-save"></vs-button>
                                        </div>
                                    </div>
                                </div>
                                
                            </div>

                            <div  v-show="list.length == 0" class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                                <div v-if="!image">
                                    <div style="width:100%">
                                        <img src="@/assets/images/others/notfound.png" alt="" style="max-width:100%;max-height:100%">
                                    </div>
                                    <input accept=".png, .jpg, .jpeg" id="fileInput" type="file" style="display:none" @change="onFileChange($event)" />
                                    <vs-button class="icon-camara" onclick="document.getElementById('fileInput').click()" icon-pack="fas" icon="fa-camera-retro"></vs-button>
                                </div>
                                <div v-else style="display:flex">
                                    <img :src="image" class="image" style="max-width:100%;max-height:100%;" />
                                    <div style="background-color:rgba(0,0,0,0.5);padding:10px;position:absolute;width:100%;text-align:center;top:45%">
                                        <vs-button color="danger" :disabled="bloqueoScanner" style="position:relative;top:0;left:0;margin-right:5px;display:inline-block" class="icon-eliminar" v-on:click="removeImage" icon-pack="fas" icon="fa-trash"></vs-button>
                                        <vs-button color="primary" style="position:relative;top:0;left:0;display:inline-block" type="filled" @click="guardarScanner" icon-pack="fas" icon="fa-save"></vs-button>
                                    </div>
                                </div>
                            </div>
                            
                        </div>
                    </div>

                    <div v-if="bloqueoBusqueda">

                        <vs-divider>Codigo de Barra</vs-divider>

                        <div class="flex flex-wrap" >
                            <barcode :value="codeProducto.trim()" :options="{ displayValue: true , format:'CODE128',fonyt:'Arial',textAlign:'center', fontOptions: 'bold'}"></barcode>
                        </div>

                    </div>

                    <vs-divider></vs-divider>
                    <!-- :disabled="invalid" -->

            
                    <div   v-if="bloqueoBusqueda" class="con-vs-alert con-vs-alert-danger con-icon">
                        <div v-for="(error,index) in errors" :key="index">
                            <span style="font-size:18px" v-if="error.length!=0" class="feather-icon select-none relative">
                                <i class="feather icon-alert-circle"></i>
                            </span>
                            <span  style="font-size:14px;margin-left:5px" v-if="error.length!=0"><b>{{index+':'}}</b>{{error}}</span>
                        </div>   
                    </div> 


                    <vs-button  style="float:right"  @click="handleSubmit(guardar_producto(invalid))"> Grabar</vs-button>
                    <vs-button color="danger" style="float:right" type="border" icon-pack="feather" icon="icon-x" @click="show=false"> Cancelar</vs-button>
                    <div style="clear:both"></div>

                </div>

            </form>

        </ValidationObserver>
    </vs-popup>

    <vs-popup classContent="popup-example" :button-close-hidden="false" :title="'Producto - Precios'" :active.sync="showprecio" style="z-index:999999">
        <div style="padding:10px;border-radius:5px; border:1px solid #ccc; font: 14px Arial;">

            <div class="flex flex-wrap">

                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                    <vs-input v-model="PNivel" :disabled="bloqueoPrecioCampo" label="Nivel" class="w-full" type="number" name="Codigo_v2" />
                </div>

                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                    <vs-input v-model="PSubNivel" :disabled="bloqueoPrecioCampo" label="Sub Nivel" class="w-full" type="number" name="Codigo_v2" />
                </div>

                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                    <P>Unidad Medida:</P>
                    <multiselect v-model="PUnidadM" :disabled="bloqueoPrecioCampo" :options="ListadoPresentacion" name="Presentacion-Producto-Mant" track-by="Nombre" label="Descripcion" placeholder="Seleccione Presentación"></multiselect>
                </div>

                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                    <vs-input v-model="Ppor" :disabled="bloqueoPrecioCampo" @change="precio_porcentaje" label="% Porcentaje" class="w-full" type="number" name="Codigo_v2" />
                </div>

                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                    <vs-input v-model="PFactor" :disabled="bloqueoPrecioCampo" @change="precio_factor" label="Factor" class="w-full" type="number" name="Codigo_v2" />
                </div>

                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                    <vs-input v-model="PPrecio" :disabled="bloqueoPrecioCampo" @keydown="numbersOnlyWithDecimal(PPrecio,$event)" @change="precio_costo" label="Precio" class="w-full" type="number" name="Codigo_v2" />
                </div>

            </div>

            <vs-divider></vs-divider>
            <vs-button style="float:right" :disabled="bloqueoPrecioBtn" @click="guardar_precios"> Grabar</vs-button>
            <vs-button color="primary" style="float:right" type="border" icon-pack="feather" icon="icon-save" :disabled="bloqueoGPrecioBtn" @click="guardar_prodprecio_niv1"> Generar Precios</vs-button>
           
            <div style="clear:both"></div>

        </div>
    </vs-popup>
    <!-- ========================================================================================================================================================== -->
    <!-- POPUP FIN -->
    <!-- ========================================================================================================================================================== -->

</vx-card>
</template>

<script>

// For custom error message
import {
    ValidationObserver,
    ValidationProvider,

} from "vee-validate";

import Multiselect from 'vue-multiselect'
import "vue-multiselect/dist/vue-multiselect.min.css"

export default {
    name: 'componenteProductos',
    data() {
        return {
            show: true, //mostrar las opciones del producto
            showTiposede: false, //Opcion Vista Tipo Sede Cocina
            showprecio: false, //Opcion Precio del Producto
            bloqueoBusqueda: false,
            bloqueoPrecioCampo: false,
            bloqueoPrecioBtn: true,
            bloqueoGPrecioBtn: false,
            codeClase: '',
            codeClasificacion: '',
            ListadoClase: [],
            ListadoCategoria: [],
            ListadoClaseGeneral: [],
            ListadoCategoriaGeneral: [],
            ListadoFamilia: [],
            ListadoPresentacion: [],
            ListadoCategoriasFact: [],
            ListadoCatFacturacion: [],
            ListadoUtilizadoOrigen: [],
            ListadoProductoOpc: [],
            ListadoTipoBodega: [],
            ListadoTipoSedeCocina: [],
            ListadoCaractProcucto: [],
            selectBodega: null,
            selectClase: null,
            selectCategoria: null,
            selectFamilia: null,
            selectPresentacion: null,
            selectCategoriasFact: null,
            codeCategoria: '',
            selectProductoOpc: {},
            selectTipoBodega: '',
            selectUtilizadoOrigen: 'H',
            selectTipoSedeCocina: '',
            selectCaractProducto: {},
            codeCategoriaFact: '',
            codeFamilia: '',
            codePresentacion: 'UNIDAD',
            //Campos Productos Nuevos
            codeProducto: '',
            codeLaboratorio: '',
            NombreLaboratorio: '',
            codeProveedor: '',
            NombreProveedor: '',
            codedepartamento: '',
            Nombredepartamento: '',
            codesubdepartamento: '',
            Nombresubdepartamento: '',
            codegrupo: '',
            Nombregrupo: '',
            codesubgrupo: '',
            Nombresubgrupo: '',
            Cod_Farmacia: '0',
            Costo_IVA: '0',
            ValorCosto: '',
            //ValorCosto: 0,
            CostoInicial: '0',
            Costo_Competencia: '0',
            ExistenciaMin: 0,
            ExistenciaMax: 0,
            counterDanger: false,
            Marca: '',
            Principio_Activo: '',
            Descripcion_Producto: '',
            prod: {
                GenericosCodigo: '',
                GenericosNombre: '',
                codeBodega: '',
            },
            list: [],
            image: '',
            PNivel: '',
            PSubNivel: '',
            PUnidadM: null,
            codePUnidadM: '',
            Ppor: '',
            PFactor: '',
            PPrecio: '',
            //Campos Nuevos
            VentaHospital: '',
            UnidadCompra: '',
            RegistroSanitario: '',
            Contraindicaciones: '',
            usuario: '',
            nombreusuario: '',
            fechacreacion: '',
            atc: '',
            //erroresprod: [],
        }
    },
    props: {
        callback: {
            default: null
        },
        cerrar:{
            default:null
        }
    },
    components: {
        Multiselect,
        ValidationProvider,
        ValidationObserver,
    },
    watch: {
        selectCategoriasFact(value) {
            this.codeCategoriaFact = value.Codigo
        },
        selectFamilia(value) {
            this.codeFamilia = value.Codigo
        },
        selectPresentacion(value) {
            this.codePresentacion = value.Nombre
        },
        PUnidadM(value) {
            this.codePUnidadM = value.Nombre
        },
        selectBodega(value) {
            this.prod.codeBodega = value.Codigo
        },
        show(){
            this.cerrar()
        }
    },
    methods: {
        onFileChange(e) {
            var files = e.target.files || e.dataTransfer.files;
            if (!files.length)
                return;
            this.createImage(files[0]);
        },
        createImage(file) {
            //var image = new Image();
            var reader = new FileReader();
            var vm = this;
            reader.onload = (e) => {
                vm.image = e.target.result;
                this.guardarScanner();
            };
            reader.readAsDataURL(file);  
        },
        removeImage() {
            this.image = '';
        },
        onChangeClase(value) {
            this.selectCategoria = null
            this.codeProducto = ''
            this.ListadoCategoria = []
            this.codeClase = value.Codigo
            this.codeClasificacion = value.Clasificacion
            this.cargar_categoria(value.Codigo)
            this.cargar_ProductoOpciones()
            this.cargar_CaracteristicasProducto()
        },
        onChangeCategoria(value) {
            if (value !== null && value.length !== 0) {
                this.codeCategoria = value.Codigo
                this.Generar_codigo_prod(this.codeClase, this.codeCategoria)
            } else {
                this.codeProducto = ''
            }
        },
        onChangeTipoBodega() {
            this.cargar_CatFacturacion()
            if (this.selectTipoBodega == 'C') {
                this.showTiposede = true;
            } else {
                this.showTiposede = false;
            }
            this.cargar_TipoSedeCocina()
        },
        Generar_codigo_prod(codigoclase, codigocategoria) {
            
            this.axios.post('/app/inventario/invGenerarCodigoProd', {
                    //Empresa: 'MED',
                    claseid: codigoclase,
                    categoriaid: codigocategoria
                })
                .then(resp => {
                    
                    this.bloqueoBusqueda = true
                    this.list = []
                    resp.data.json.map(data => {
                        this.codeProducto = data.Codigo
                    })
                    this.cargar_scanner()
                    this.cargar_TipoBodega()
                })
                .catch(err => {
                    
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Producto',
                        text: 'Error al Generar Codigo del Producto.',
                    })
                    
                })
        }, //Fin de metodo Categoria
        guardar_producto(validar) {
        
            if(validar){
                //false todo limpio.
                //true todo ok.
                this.$vs.notify({
                    color: 'danger',
                    title: 'Producto',
                    text: 'Estimado Usuario, Tiene campos obligatorios por Completar.',
                 })
    

            }else if( parseFloat(this.ValorCosto)<=0  ||  parseFloat(this.Costo_IVA)<=0  ||  parseFloat(this.CostoInicial)<=0 ){
                
                this.$vs.notify({   
                    color: 'danger',
                    title: 'Producto',
                    text: 'Estimado Usuario,El Costo No Puede ir valor 0 (Cero).',
                 })

            }
            else{

                
                this.axios.post('/app/inventario/invProductoins', {
                        CodigoNuevo: this.codeProducto,
                        Nombre: this.Marca.trim(),
                        Generico: this.Principio_Activo.trim(),
                        Descripcion: this.Descripcion_Producto.trim(),
                        Categoria: this.codeCategoriaFact,
                        Costo: this.ValorCosto,
                        CodigoHospIFar: (this.Cod_Farmacia=='' || this.Cod_Farmacia==null ? '0':this.Cod_Farmacia),
                        CostoReferencia: (this.Costo_Competencia=='' || this.Costo_Competencia==null ? '0':this.Costo_Competencia),
                        ExistenciaMIN: (this.ExistenciaMin=='' || this.ExistenciaMin==null ? '0':this.ExistenciaMin),
                        ExistenciaMAX: (this.ExistenciaMax=='' || this.ExistenciaMax==null ? '0':this.ExistenciaMax),
                        FamiliasCodigo: this.codeFamilia,
                        Trasladable: this.selectProductoOpc.T,
                        ExcentoIva: this.selectProductoOpc.E,
                        Warning: this.selectProductoOpc.W,
                        EsActivoFijo: this.selectProductoOpc.A,
                        Consignacion: this.selectProductoOpc.C,
                        TipodeUso: this.selectUtilizadoOrigen,
                        Departamento: this.codedepartamento,
                        SubDepartamento: this.codesubdepartamento,
                        Marca: this.codeLaboratorio,
                        Proveedor: this.codeProveedor,
                        Clase: this.codeClase,
                        SubClase: this.codeCategoria,
                        unidadesmedida: this.codePresentacion,
                        TipoBodega: this.selectTipoBodega,
                        Ensamblado: this.selectTipoSedeCocina,
                        IdGrupo: this.codegrupo,
                        IdSubGrupo: this.codesubgrupo,
                        IdGenerico: this.prod.GenericosCodigo,
                        Tipo: this.codeClasificacion,
                        // Campos Nuevos 25/09/2020 Descas Ficha Del Producto.
                        VentaHospital: this.VentaHospital,
                        UnidadCompra: this.UnidadCompra,
                        RegistroSanitario: this.RegistroSanitario,
                        CadenaFrio: this.selectCaractProducto.CF,
                        LuzDirecta: this.selectCaractProducto.LD,
                        Controlado: this.selectCaractProducto.MC,
                        Stock: this.selectCaractProducto.PS,
                        BajoPedido: this.selectCaractProducto.BP,
                        Sasi: this.selectCaractProducto.SS,
                        CatalogoGeneral: this.selectCaractProducto.CG,
                        CompraLocal: this.selectCaractProducto.CL,
                        Importado: this.selectCaractProducto.PI,
                        bitacora: [{
                            llave: this.codeProducto + "-" + this.Marca + "-" + this.Principio_Activo,
                            tipo: "Producto",
                            registros: [
                                'Mantenimiento Producto-Guardar'
                            ]
                        }]
                    })
                    .then(resp => {

                        
                        if (resp.data.codigo == 0 && resp.data.json.length > 0) {

                            if (resp.data.json[0].codigo == -1) {
                                this.$vs.notify({
                                    color: 'danger',
                                    title: 'Producto',
                                    text: resp.data.json[0].error,
                                })
                            } else if (resp.data.json[0].codigo == -2) {
                                //this.codeProducto=codeProducto+1;
                                //this.cargar_producto()
                                //this.callback()
                                this.$vs.notify({
                                    color: 'danger',
                                    title: 'Producto',
                                    text: resp.data.json[0].error,
                                })
                            } else {
                                //this.callback()
                                this.$vs.notify({
                                    color: 'success',
                                    title: 'Producto',
                                    text: resp.data.json[0].descripcion,
                                })
                                //this.show = false;
                                this.showprecio = true;
                                //Procesos Correcto Guardar Producto
                                if (this.codeClasificacion == 'P') {
                                    this.PNivel = 1;
                                    this.PSubNivel = 1;
                                    this.codePUnidadM = 'Unidad';
                                    this.Ppor = 0;
                                    this.PFactor = 1;
                                    this.PPrecio = this.ValorCosto;

                                    
                                    this.guardar_prodprecio_niv1()
                                    .then(()=>{
                                        this.guardar_precios()
                                    })
                                    
                                    
                                }

                            }
                        }
                    })
                    .catch(() => {
                        
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Producto',
                            text: 'Error al Guardar la información',
                        })
                    })

            }


        },
        guardar_prodprecio_niv1() {
            return new Promise((resolve, reject)=>{
                
                this.axios.post('/app/inventario/invProductoPrecioNivel1', {
                        CodigoNuevo: this.codeProducto,
                        Nivel: this.PNivel,
                        SubNivel: this.PSubNivel,
                        factor: this.PFactor,
                        //unidadesmedida: this.codePUnidadM,
                        unidadesmedida: this.codePresentacion,
                        porcentaje: this.Ppor,
                        precio: this.PPrecio,
                        Tipo: this.codeClasificacion,
                        bitacora: [{
                            llave: this.CodigoNuevo + "-" + this.Nivel + "-" + this.SubNivel,
                            tipo: "Producto",
                            registros: [
                                'Mantenimiento Producto Precio Nivel 1-Guardar'
                            ]
                        }]
                    })
                    .then(resp => {
                        
                        if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                            this.$vs.notify({
                                color: 'success',
                                title: 'Producto',
                                text: resp.data.json[0].descripcion,
                            })
                            this.bloqueoPrecioBtn = false;
                            this.bloqueoGPrecioBtn = true;
                            this.bloqueoPrecioCampo = true;         
                            resolve(resp)   
                        }
                    })
                    .catch(() => {
                        
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Producto',
                            text: 'Error al Guardar la información',
                        })
                        reject()
                    })
            })
            //this.bloqueoPrecioBtn=false;
            //this.bloqueoGPrecioBtn=true;
        },
        guardar_precios() {
            
            this.axios.post('/app/inventario/invProductoPrecio', {
                    CodigoNuevo: this.codeProducto,
                    Categoria: this.codeCategoriaFact,
                    Tipo: this.codeClasificacion,
                    bitacora: [{
                        llave: this.CodigoNuevo + "-" + this.Categoria,
                        tipo: "Producto",
                        registros: [
                            'Mantenimiento Producto Precio Guardar - General'
                        ]
                    }]
                })
                .then(resp => {
                    
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        //this.cargar_producto()
                        this.callback()
                         //SE QUITO EL 15/10/2020 COLOCAR OTRA VEZ
                        this.$vs.notify({
                            color: 'success',
                            title: 'Producto',
                            text: resp.data.json[0].descripcion,
                        })
                        //this.limpiar()
                    }
                })
                .catch(() => {
                    
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Producto',
                        text: 'Error al Guardar la información',
                    })
                })
        },
        cargar_clase() {
            this.axios.post('/app/inventario/invClaselist', {})
                .then(resp => {
                    this.ListadoClase = resp.data.json
                })
                .catch(() => {})
        },
        cargar_categoria(codigoclase) {
            this.axios.post('/app/inventario/invCategorialist', {
                    claseid: codigoclase
                })
                .then(resp => {
                    this.ListadoCategoria = resp.data.json
                })
                .catch(() => {})
        },
        cargar_ProductoOpciones() {
            this.axios.post('/app/inventario/invProdopcioneslist', {
                    Tipo: this.codeClasificacion
                })
                .then(resp => {
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        this.ListadoProductoOpc = resp.data.json
                    }
                })
                .catch(() => {})
        },
        cargar_UtilizadoOrigen() {
            this.axios.post('/app/inventario/invutilorigenlist', {})
                .then(resp => {
                    this.ListadoUtilizadoOrigen = resp.data.json
                })
                .catch(() => {})
        }, //Fin de metodo Utilizado Origen
        cargar_familia() {
            this.axios.post('/app/inventario/invFamiliaslist', {})
                .then(resp => {
                    this.ListadoFamilia = resp.data.json
                })
                .catch(() => {})
        }, //Fin de metodo Familia
        cargar_presentacion() {
            this.axios.post('/app/inventario/invPresentacionlist', {})
                .then(resp => {
                    this.ListadoPresentacion = resp.data.json
                })
                .catch(() => {})
        }, //Fin de metodo Presentación
        cargar_TipoBodega() {
            this.axios.post('/app/inventario/invCatTipoBodegalist', {
                Tipo: this.codeClasificacion
            })
                .then(resp => {
                    this.ListadoTipoBodega = resp.data.json
                })
                .catch(() => {})
        }, //Fin de metodo Tipo Bodega
        cargar_CatFacturacion() {
            this.ListadoCatFacturacion = []
            this.selectCategoriasFact = null
            this.axios.post('/app/inventario/invCatFacturacionlist', {
                    TipoBodega: this.selectTipoBodega
                })
                .then(resp => {
                    this.ListadoCatFacturacion = resp.data.json
                })
                .catch(() => {})
        }, //Fin de metodo Categoria Facturacion
        cargar_TipoSedeCocina() {
            this.axios.post('/app/inventario/invCatTipoSedeCocinalist', {
                    TipoBodega: this.selectTipoBodega
                })
                .then(resp => {
                    this.ListadoTipoSedeCocina = resp.data.json
                })
                .catch(() => {})
        }, //Fin de metodo Tipo Sede Cocina
        cargar_CaracteristicasProducto() {
            this.axios.post('/app/inventario/invCaractProducto', {
                    Tipo: this.codeClasificacion
                })
                .then(resp => {
                    this.ListadoCaractProcucto = resp.data.json
                })
                .catch(() => {})
        }, //Fin de metodo Tipo Bodega
        buscar_laboratorio() {
            this.$refs.buscar_laboratorio.iniciar((data) => {
                if (data != null) {
                    this.codeLaboratorio = data.Codigo
                    this.NombreLaboratorio = data.Nombre
                }
            })
        },
        buscar_proveedor() {
            this.$refs.buscar_proveedor.iniciar((data) => {

                if (data != null) {
                    this.codeProveedor = data.Codigo
                    this.NombreProveedor = data.Nombre
                }
            })
        },
        buscar_departamento() {
            this.$refs.buscar_departamento.iniciar((data) => {
                if (data != null) {
                    this.codedepartamento = data.Codigo
                    this.Nombredepartamento = data.Nombre
                    this.codesubdepartamento = ''
                    this.Nombresubdepartamento = ''
                }
            })
        },
        buscar_subdepartamento() {
            this.$refs.buscar_subdepartamento.iniciar((data) => {
                if (data != null) {
                    this.codesubdepartamento = data.Codigo
                    this.Nombresubdepartamento = data.Nombre
                }
            })
        },
        buscar_grupo() {
            this.$refs.buscar_grupo.iniciar((data) => {
                if (data != null) {
                    this.codegrupo = data.Codigo
                    this.Nombregrupo = data.Nombre
                    this.codesubgrupo = ''
                    this.Nombresubgrupo = ''
                }
            })
        },
        buscar_subgrupo() {
            this.$refs.buscar_subgrupo.iniciar((data) => {
                if (data != null) {
                    this.codesubgrupo = data.Codigo
                    this.Nombresubgrupo = data.Nombre
                }
            })
        },
        buscar_producto() {
            this.$refs.buscar_producto.iniciar((data) => {
                if (data != null) {
                    //this.codeProducto = data.Codigo
                    //this.NombreProducto = data.Nombre
                    this.prod.Codigo = data.Codigo
                    this.prod.Nombre = data.Nombre
                }
            })
        },
        buscar_Genericos() {
            this.$refs.buscar_Genericos.iniciar((data) => {
                if (data != null) {
                    this.prod.GenericosCodigo = data.Codigo
                    this.prod.GenericosNombre = data.Nombre
                }
            })
        },
        cargar_scanner() {
            
            this.axios.post('/app/inventario/InvObtenerImagen', {
                    Categoria: 'PROD',
                    subfolder: 'PROD' + '-' + this.codeProducto,
                    // orden: this.info.orden_numero
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.list = []
                        resp.data.listado.map(data => {
                            this.list.push({
                                src: data.src,
                                archivo_nombre: data.nombreArchivo
                            })
                        })
                        this.list.push({
                            src: null,
                            archivo_nombre: null
                        })
                    }
                   
                    this.$vs.loading.close("#button-with-loading > .con-vs-loading")
                })
                
        },
        guardarScanner() {
            if(this.image=="" || this.image == null ||  this.codeProducto=="" ||  this.codeProducto=="0" ||  this.codeProducto==null){
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Producto',
                        text: 'Estimado Usuario,Cargue una Imagen.',
                    })
            }else{

                
                this.axios.post('/app/inventario/InvGuardarImagen', {
                        base64String: this.image.replace('data:image/jpeg;base64,', '').replace('data:image/png;base64,', '').replace('data:image/jpg;base64,', ''),
                        Categoria: 'PROD',
                        subfolder: 'PROD' + '-' + this.codeProducto,
                        nombreArchivo: 'PRODUCTO'
                    })
                    .then(() => {
                        
                        this.removeImage()
                        this.cargar_scanner()
                    })
            }
 
        },
        remove_scanner(index) {
            
            this.axios.post('/app/inventario/InvEliminarImagen', {
                    Categoria: 'PROD',
                    subfolder: 'PROD' + '-' + this.codeProducto,
                    nombreArchivo: this.list[index].archivo_nombre
                })
                .then(resp => {
                    
                    if (resp.data.codigo == 0) {
                        this.list.splice(index, 1)
                    }

                })
        },
        precio_porcentaje() {
            if (this.Costo_IVA > 0) {

                this.axios.post('/app/inventario/invGenerarPrecioPorcentaje', {
                        factor: ((this.PFactor == '' || this.PFactor == 0) ? 1 : this.PFactor),
                        Costo: this.ValorCosto,
                        porcentaje: this.Ppor,
                    })
                    .then(resp => {
                        resp.data.json.map(data => {
                            this.PPrecio = data.Precio
                        })
                    })
                    .catch(() => {})
            }
        },
        precio_factor() {
            if (this.PFactor > 0) {
                this.axios.post('/app/inventario/invGenerarPrecioFactor', {
                        factor: ((this.PFactor == '' || this.PFactor == 0) ? 1 : this.PFactor),
                        Costo: this.ValorCosto,
                        porcentaje: this.Ppor,
                    })
                    .then(resp => {
                        resp.data.json.map(data => {
                            this.PPrecio = data.Precio
                        })
                    })
                    .catch(() => {})
            }
        },
        precio_costo() {
            if (this.PPrecio > 0 && this.PPrecio != '') {

                this.axios.post('/app/inventario/invGenerarPrecioCosto', {
                        factor: ((this.PFactor == '' || this.PFactor == 0) ? 1 : this.PFactor),
                        Costo: this.ValorCosto,
                        porcentaje: this.Ppor,
                        precio: this.PPrecio,
                    })
                    .then(resp => {
                        resp.data.json.map(data => {
                            this.Ppor = data.Porcentaje
                        })
                    })
                    .catch(() => {})
            }
        },
        Validacion_ExistenciaMax() {
            if (parseInt(this.ExistenciaMin) > 0) {
                if (parseInt(this.ExistenciaMin) > parseInt(this.ExistenciaMax)) {
                    const TemporalMin = this.ExistenciaMin
                    this.ExistenciaMin = this.ExistenciaMax
                    this.ExistenciaMax = TemporalMin
                }
            }
        },
        Validacion_ExistenciaMin() {
            if (parseInt(this.ExistenciaMax) > 0) {
                if (parseInt(this.ExistenciaMin) > parseInt(this.ExistenciaMax)) {
                    const TemporalMax = this.ExistenciaMin
                    this.ExistenciaMin = this.ExistenciaMax
                    this.ExistenciaMax = TemporalMax
                }
            }
        },
        validacion_costo() {
            this.ValorCosto = '0'
            this.CostoInicial='0'
            if (this.Costo_IVA > 0) {
                this.CostoInicial =this.Costo_IVA;
                this.axios.post('/app/inventario/invGenerarCotoSinIVA', {
                        Costo: this.Costo_IVA
                    })
                    .then(resp => {
                        resp.data.json.map(data => {
                            this.ValorCosto = data.costo                         
                            //this.Costo_IVA=parseFloat(data.costo).formatMoney(2,currencySymbol, ',', '.');
                            //this.Costo_IVA =data.costo.$formatMoneyQ(2,currencySymbol, ',', '.');
                            // this.applyMoneyPattern(data.costo, 'Q');
                            this.applyMoneyPattern(data.costo, 'Q');
                    
                        })
                    })
                    .catch(() => {})
            }
        },    
        removeMoneyMask(htmlObject) {
            // this.CostoInicial.replace(/([a-z]|[A-Z]|[<>%\$])/g, '');
            // this.CostoInicial = this.CostoInicial.replace(/,/g, '');
            htmlObject.replace(/,/g, '');
            this.Costo_IVA=(this.Costo_IVA=='0'  || this.Costo_IVA==0 || this.Costo_IVA=='' || this.Costo_IVA==null ? '':this.CostoInicial);
            //alert(this.Costo_IVA)

        },
        removeMask(valor) {
            valor=(valor=='0'  || valor==0 || valor=='' || valor==null ? '':valor);
            this.Cod_Farmacia=(valor=='0'  || valor==0 || valor=='' || valor==null ? '':this.Cod_Farmacia);
            //alor.replace(/,/g, '');
           // this.Cod_Farmacia.replace(/([a-z]|[A-Z]|[<>%\$])/g, '');
            //this.Cod_Farmacia.replace(/[\s\/]/g, '');
            //this.Cod_Farmacia.replace(/(^\s+|\s+$)/gm, '');
           // this.Cod_Farmacia.replace('0', '');
            //this.Cod_Farmacia='';
            //this.Cod_Farmacia.replace(/[\s\/]/g, '');
            //alert('removeMask');
        },
        removeMask1(valor) {
            valor=(valor=='0'  || valor==0 || valor=='' || valor==null ? '':valor);
            this.Costo_Competencia=(valor=='0'  || valor==0 || valor=='' || valor==null ? '':this.Costo_Competencia);
        },
        removeMask2(valor) {
           // alert('removeMask2')
            valor=(valor=='0'  || valor==0 || valor=='' || valor==null ? '':valor);
            this.ExistenciaMin=(valor=='0'  || valor==0 || valor=='' || valor==null ? '':this.ExistenciaMin);

        
        },
        removeMask3(valor) {
            valor=(valor=='0'  || valor==0 || valor=='' || valor==null ? '':valor);
            this.ExistenciaMax=(valor=='0'  || valor==0 || valor=='' || valor==null ? '':this.ExistenciaMax);


        },
        applyMoneyPattern(htmlObject, currencySymbol) {
           
           //alert(this.Costo_IVA);
           //this.validacion_costo();

            var txt = htmlObject;
            //var txt =  this.ValorCosto;
            if (txt !== "") {
                // txt = txt.replace(/([a-z]|[A-Z]|[<>%\$])/g, '');
                txt = txt.replace(/([a-z]|[A-Z]|[<>%])/g, '');
                txt = txt.replace(/,/g, '');
                txt = txt.substring(0, parseInt(htmlObject.length));
                if (this.isValidMount(txt)) {
                    //var value = parseFloat(txt); NO SE USA
                    //this.Costo_IVA=this.numberToCurrency(parseFloat(this.Costo_IVA.trim()),'.',',',2);
                    //this.Costo_IVA=parseFloat(this.Costo_IVA.trim()).formatMoney('2', 'R$', '.', ',');
                    // this.Costo_IVA=parseFloat(this.Costo_IVA.trim()).formatMoney(2,currencySymbol, ',', '.');
                      this.Costo_IVA=parseFloat(htmlObject.trim()).formatMoney(2,currencySymbol, ',', '.');

                } else {
                    this.Costo_IVA ='';
                }
            }else {
                 this.Costo_IVA ='';
            }
        },
        numbersOnlyWithDecimal(objectHtml, event) {

            //var charCode = (event.which) ? event.which : event.keyCode;

            // Allow special chars + arrows
            if (event.keyCode == 46 || event.keyCode == 190 || event.keyCode == 110
                    || event.keyCode == 8 || event.keyCode == 9 || event.keyCode == 27
                    || event.keyCode == 13
                    || (event.keyCode == 65 && event.ctrlKey === true)
                    || (event.keyCode >= 35 && event.keyCode <= 39)) {
                return;
            } else {
                // If it's not a number stop the keypress
                if (event.shiftKey || (event.keyCode < 48 || event.keyCode > 57)
                        && (event.keyCode < 96 || event.keyCode > 105)) {
                    event.preventDefault();
                }// else {

                // }
            }
            
        },
        numbersOnly(objectHtml, event) {

            if (event.keyCode == 46 || event.keyCode == 190 
                    || event.keyCode == 8 || event.keyCode == 9 || event.keyCode == 27
                    || event.keyCode == 13
                    || (event.keyCode == 65 && event.ctrlKey === true)
                    || (event.keyCode >= 35 && event.keyCode <= 39)) {
                return;
            } else {
                // If it's not a number stop the keypress
                if (event.shiftKey || (event.keyCode < 48 || event.keyCode > 57)
                        && (event.keyCode < 96 || event.keyCode > 105)) {
                    event.preventDefault();
                }// else {

                // }
            }
            
        },
        isValidMount(val) {
            var regexDouble = /(^(\d*[0-9]\.\d*)\d+$|^(\d*[0-9]+$))/;
            var str = parseFloat(val);
            if (regexDouble.test(str)) {
                return true;
            } else {
                return false;
            }
        },   
    },
    mounted() {
        const sesion = this.$store.state.sesion;
        this.bloqueoScanner = false,
            this.cargar_clase(),
            this.cargar_presentacion(),
            this.cargar_familia(),
            //this.cargar_TipoBodega(),
            this.cargar_UtilizadoOrigen(),
            this.selectProductoOpc = {
                T: true
            },
            // this.$validator.localize('en', dict);

            this.usuario = sesion.corporativo
            this.nombreusuario = sesion.usuario

            var f = new Date();
            //this.fechacreacion='04/09/2020'
            this.fechacreacion = (f.getDate() + "/" + (f.getMonth() + 1) + "/" + f.getFullYear())
    },
    created() {
        this.list.push({
            src: null,
            archivo_nombre: null
        })
    }
}

//Formato Moneda.
Number.prototype.formatMoney = function(places, symbol, thousand, decimal) {
	places = !isNaN(places = Math.abs(places)) ? places : 2;
	symbol = symbol !== undefined ? symbol : "Q";
	/*if (symbol === "GTQ") {
		symbol = "Q";
	} else {
		symbol = "$";
	}*/
	thousand = thousand || ",";
	decimal = decimal || ".";
	var number = this, negative = number < 0 ? "-" : "", i = parseInt(
			number = Math.abs(+number || 0).toFixed(places), 10)
			+ "", j = (j = i.length) > 3 ? j % 3 : 0;
	return symbol
			+ negative
			+ (j ? i.substr(0, j) + thousand : "")
			+ i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + thousand)
			+ (places ? decimal + Math.abs(number - i).toFixed(places).slice(2)
					: "");
};

</script>

<style>
.footer {
    height: 50px;
    margin-top: -65px;
    margin-left: 10px;
}
</style>

