<template>
<details open class="vx-card mb-2 p-2">
    <summary>
        Información del Plan
    </summary>

    <div class="flex flex-wrap">
        <div class="w-full flex flex-wrap">
            <vs-input class="w-full sm:w-1/12 p-1" label="Póliza" :value="DatosPlan.IdPoliza" readonly size="small" />
            <vs-input class="w-full sm:w-5/12 p-1" label="Contrato" :value="DatosPlan.NombreContrato" readonly size="small" />
            <vs-input class="w-full sm:w-6/12 p-1" label="Plan" :value="DatosPlan.NombrePlan" readonly size="small" />
        </div>

        <div class="w-full flex flex-wrap">
            <vs-input class="w-full md:w-1/6 sm:w-1/3 p-1" label="Status" :value="Afiliado.Status" readonly size="small" />
            <vs-input class="w-full md:w-1/6 sm:w-1/3 p-1" label="Número Adhesión" :value="Afiliado.NumeroAdhesion" readonly size="small" />
            <vs-input class="w-full md:w-1/6 sm:w-1/3 p-1" label="ID Afiliado" :value="Afiliado.IdAfiliado" readonly size="small" />
            <vs-input class="w-full md:w-1/6 sm:w-1/3 p-1" label="ID Cliente" :value="Afiliado.IdCliente" readonly size="small" />
            <vs-input class="w-full md:w-1/6 sm:w-1/3 p-1" label="ID Paciente" :value="Afiliado.IdPaciente" readonly size="small" />
            <vs-input class="w-full md:w-1/6 sm:w-1/3 p-1" label="Fecha Cobertura" :value=" $formato_fecha(Afiliado.FechaInicioCobertura)?.substring(0,10) " readonly size="small" />
        </div>

        <div class="w-full flex flex-wrap">
            <vs-input class="w-full md:w-1/4 sm:w-1/4 p-1" label="Meses Activo" :value="Math.round((new Date() - new Date(Afiliado.FechaInicioCobertura))/31557600000*12)" readonly size="small" />
            <vs-input class="w-full md:w-1/4 sm:w-1/4 p-1" label="Monto Vitalicio" :value="$formato_moneda(DatosPlan.MaximoVitalicio)" readonly size="small" />
            <vs-input class="w-full md:w-1/4 sm:w-1/4 p-1" label="Monto Anual" :value="$formato_moneda(DatosPlan.MaximoAnual)" readonly size="small" />
            <vs-input class="w-full md:w-1/4 sm:w-1/4 p-1" label="Monto Evento" :value="$formato_moneda(DatosPlan.MaximoEvento)" readonly size="small" />
        </div>
    </div>
</details>
</template>

<script>

export default {
    name: 'AfiliadoInfoPlanEncabezado',
    /**Componente que muestra datos de encabezado del seguroy afiliado */
    props: {
        Afiliado: {
            type: Object,
            default: ()=> {return {} }
        },
        DatosPlan: {
            type: Object,
            default: ()=> {return {} },
        }
    },
    components: {
       
    },
    data() {
        return {
           
        }
    },
    methods: {
        
    },
    watch: {
       
    },
    beforeMount() {
        
    },
    mounted() {
       
    },
}
</script>
<styles>

</styles>
