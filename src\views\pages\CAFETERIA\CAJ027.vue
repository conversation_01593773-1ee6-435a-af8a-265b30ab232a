<template>
    <vx-card type = "2" title="Eliminación de Ajenos Facturados " class="justify-center ">    
        <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
            <form ref="formAnulacion" method="post" @submit.prevent="handleSubmit(AnularOrden())">
                <div class="flex flex-wrap" v-if ="info.lbOrdenCorrecta">
                        <div class="md:w-full lg:w-4/12 xl:w-2/12 m-2" >
                            <label>Admision: </label> 
                            <label style="color:black">{{info.Admision}}</label> 
                        </div>                                
                        <div class="md:w-full lg:w-6/12 xl:w-4/16 m-2 lg:pr-6">
                            <label>Paciente: </label>                   
                            <label style="color:black">{{info.NombreCompleto}}</label>      
                        </div>
                    <div class="md:w-full lg:w-1/12 xl:w-5/12"></div>
                </div>
                <div class="flex flex-wrap">
                    <div class="md:w-full lg:w-4/12 xl:w-2/12 m-2">
                        <ValidationProvider rules="required" v-slot="{ errors }">
                            <SM-Buscar class="w-full" 
                                label="*Tipo de Orden" v-model="info.Tipo" 
                                api="app/v1_JefeCaja/ObtenerTiposOrdenesRol" 
                                :api_campos="['Codigo','Nombre']" 
                                :api_titulos="['Tipo de Orden','Descripción']" 
                                :api_filtro="{'Activa':0,'opcion':'C','subOpcion':'3',
                                'FiltroTipoOrdenHospital':info.FiltTipoOrden.toString(),
                                'FiltroTipoOrdenGeneral':'AJE'}" 
                                api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Codigo" :api_preload="true" 
                                :disabled_texto="true" :mostrar_busqueda="true" :callback_buscar="Cargar() .ValidarTipo" 
                                :danger="errors.length > 0"
                                :danger-text="(errors.length > 0) ? errors[0] : null"/>        
                        </ValidationProvider>
                    </div>
                    <div class="md:w-full lg:w-6/12 xl:w-4/12 m-2 lg:pr-6">
                        <vs-input label="Descripción" class="w-full" :value="info.DescripcionOrden" disabled/>
                    </div> 
                    <div class="md:w-full lg:w-10/12 xl:w-6/12 m-2">
                        <ValidationProvider rules="required" v-slot="{ errors }">
                            <vs-input label="*No. Orden" type="number"  class="w-full"  v-model="info.Orden" 
                                :danger="errors.length > 0"
                                :danger-text="(errors.length > 0) ? errors[0] : null" v-on:change="ConsultaOrden"/>
                        </ValidationProvider>
                    </div> 
                    <div class="md:w-full lg:w-10/12 xl:w-6/12 m-2">
                        <ValidationProvider rules="max:40|required" v-slot="{ errors }">
                            <vs-input label="*Razón"   class="w-full"  v-model="info.Razon" 
                                :danger="errors.length > 0"
                                :danger-text="(errors.length > 0) ? errors[0] : null" />
                        </ValidationProvider>
                        <br>
                    </div>  
                    <div class="w-full m-2">
                        <vs-button color="success" class="md:w-full lg:w-4/12 xl:w-1/12 m-2"
                            :disabled="info.ActivarBtnAnular"
                            @click="handleSubmit(AnularOrden(invalid))">
                            Anular Orden
                        </vs-button> 

                        <vs-button class="md:w-full lg:w-4/12 xl:w-1/12 m-2" :disabled = "info.ActivarBtnLimpiar" color="warning" @click="ConfirmarLimpiarCampos()">
                            Limpiar Campos
                        </vs-button>
                    </div>
                </div>               
           </form>
        </ValidationObserver>
    </vx-card>
  </template>
  
  <script>
  export default {
    data(){
        return{
            info:{
                Tipo: null,
                DescripcionOrden: null,
                Orden: null,
                Admision: null,
                NombreCompleto: null, 
                Razon: null,
                ActivarBtnAnular: true,
                ActivarBtnLimpiar: true,
                lbOrdenCorrecta: false,
                FiltTipoOrden: [],
                FiltTipoOrdenGeneral: [] 
            },
            permisos:{
                aje_anula: false
            }
        }
    },
    methods:{
        Cargar(){
            return {
                ValidarTipo: (datos)=>{
                    this.info.Tipo = datos.Codigo;
                    this.info.DescripcionOrden = datos.Nombre;
                    this.info.ActivarBtnLimpiar = false;
                    this.DatosPaciente()
                },
                ValidarOrden: () =>{
                    this.axios.post('/app/v1_JefeCaja/ValidarOrden',{
                    "Tipo": this.info.Tipo,
                    "Orden": this.info.Orden
                })
                .then(resp => {
                    let error 
                    let mensajeError
                    // console.log(resp)
                    if(resp.data.json && resp.data.json.length != 0){
                        error = resp.data.json[0].tipo_error;
                        mensajeError = resp.data.json[0].descripcion
                    }else{
                        error = resp.data.tipo_error;
                        mensajeError = resp.data.descripcion
                    }
                    
                    if(error && error != 0){               
                        this.info.Admision = null;
                        this.info.NombreCompleto = null;
                        this.info.ActivarBtnAnular = true  
                    }                
                    else{  
                        this.info.lbOrdenCorrecta = true                              
                        this.info.Admision = resp.data.SerieAdmision + '-' + resp.data.Admision;
                        this.info.NombreCompleto = resp.data.NombreCompleto;
                        this.info.ActivarBtnAnular = false;
                        this.info.ActivarBtnLimpiar = false;
                           
                                          
                    }     
                })
                },
                ValidarCampos:()=>{
                    return this.info.Tipo && this.info.DescripcionOrden && this.info.Orden                        
                }
            }
        },
        ConsultaOrden(){
            this.DatosPaciente()
        },
        DatosPaciente(){
            if (this.Cargar().ValidarCampos()) {                
                    this.Cargar().ValidarOrden()
                }else{                
                    this.info.Admision = null,
                    this.info.NombreCompleto = null,
                    this.info.ActivarBtnAnular = true                    
                }
        },
        AnularOrden(invalid){
            if(invalid){
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Alerta',
                    text: 'Falta ingresar campos obligatorios',
                })
                return
            }
                this.axios.post('/app/v1_JefeCaja/AnularOrden',{
                    "Tipo": this.info.Tipo,
                    "Orden": this.info.Orden,
                    "Razon": this.info.Razon
                })
                .then((resp) => {
                    let error 
                    let mensajeError
                    if(resp.data.json && resp.data.json.length != 0){
                        error = resp.data.json[0].tipo_error
                        mensajeError = resp.data.json[0].descripcion
                    }else{
                        error = resp.data.tipo_error
                        mensajeError = resp.data.descripcion
                    }
                    
                        if(error && error != 0){
                            this.info.ActivarBtnAnular = true 
                        }else{
                            this.LimpiarCampos()                                
                        }     
                })
        },   
        LimpiarCampos(){  
            this.info.Admision = null,
            this.info.NombreCompleto = null,          
            this.info.Tipo = null,
            this.info.DescripcionOrden = null,
            this.info.Orden = null,
            this.info.Razon = null,
            this.info.ActivarBtnAnular = true,
            this.info.ActivarBtnLimpiar = true,
            this.info.lbOrdenCorrecta = false
        },
        ConfirmarLimpiarCampos(){
            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Confirmación',
                acceptText: 'Limpiar',
                cancelText: 'Cancelar',
                text: `¿Desea limpiar los campos?`,
                accept: () => {
                    this.LimpiarCampos()
                }
            })
        }
    },
    mounted(){
        this.info.FiltTipoOrden = [],
        this.info.FiltTipoOrdenGeneral = [],
        this.permisos.aje_anula = this.$validar_privilegio('AJE_ANULA').status
    },
    watch:{
        'permisos.aje_anula'(){    
                if(this.permisos.aje_anula){
                    this.info.FiltTipoOrdenGeneral.push('AJE')
                }                    
       }
    }
}
  </script>