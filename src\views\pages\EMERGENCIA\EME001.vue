<template>
    <div>
        <vx-card :title="`Órdenes Emergencia ${sesion.sesion_sucursal_nombre}`">    
                        
            <buscador ref="BuscarMedicoAdmision" buscador_titulo="Buscador / Médico" :api="'app/emergencia/Busqueda_Ajenos'" 
                        :campos="['Codigo', 'Nombre', 'Apellido', 'Tipo', 'Especialidad']"
                        :titulos="['Codigo', 'Nombre', 'Apellido', 'Tipo', '#Especialidad']"
                        :api_filtro="{'Opcion':'C','SubOpcion':'2'}"
                        :multiselect="false" :api_validar_campo="true" :api_reload="true" :api_preload="true"  />


            <!--------------- Resultado busqueda Producto-------------->            
            <vs-popup classContent="popup-example" title="Resultado Búsqueda" :active.sync="Estado_VentanaEmergente_Busqueda" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
                <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
                    <form>
    
                        <vs-divider></vs-divider>
                        <div class="flex flex-wrap">
                            <vs-input type="number" label="Codigo" class="w-2/12" v-model="busquedaAjeno.Codigo"/>
                            <vs-input label="Nombre" class="w-2/14" v-model="busquedaAjeno.Nombre"/>
                            <vs-input label="Apellido" class="w-2/14" v-model="busquedaAjeno.Apellido"/>
                            <vs-input label="Tipo" class="w-2/14" v-model="busquedaAjeno.Tipo"/>
                            <vs-input label="Especialidad" class="w-2/14" v-model="busquedaAjeno.Especialidad"/>
                            <div class="w-1/14">
                                <br>
                                <vs-button id="button-with-loading"   color="primary" icon-pack="fa" @click="filtrarAjeno()" icon="fa-search"></vs-button>
                            </div>
                            
                            
                        </div>
                        <vs-table2 max-items="10" pagination :data="medicos" id="tb_medicos">                            
                            <template slot="thead">
                                <th>Codigo</th>
                                <th>Nombre</th>
                                <th>Apellido</th>
                                <th>Tipo</th>
                                <th>Especialidad</th>
                                <th></th>
                            </template>
    
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2 :data="data[indextr].Codigo">
                                        {{data[indextr].Codigo}}
                                    </vs-td2>
    
                                    <vs-td2 :data="data[indextr].Nombre">
                                        <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">
                                            {{data[indextr].Nombre}}
                                        </div>
                                    </vs-td2>
    
                                    <vs-td2 :data="data[indextr].Apellido">
                                        <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">
                                            {{data[indextr].Apellido}}
                                        </div>
                                    </vs-td2>                                    

                                    <vs-td2 :data="data[indextr].Tipo">
                                        {{data[indextr].Tipo}}
                                    </vs-td2>

                                    <vs-td2 :data="data[indextr].Especialidad">
                                        {{data[indextr].Especialidad}}
                                    </vs-td2>
    
                                    <vs-td2 align="right">
                                        <vs-button color="primary" icon-pack="fas" icon="fa-check-circle" style="display:inline-block;margin-right:2px" @click="seleccionarAjeno(data[indextr])"></vs-button>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </form>
    
                </div>
            </vs-popup>

            <vs-alert active="true" style="text-align:center" v-if="mostrarOrden">
                <i class="fas fa-exclamation-triangle"></i> Orden Generada: {{ info.tipoOrdenGrabada }} - {{ info.correlativoOrden }}
            </vs-alert>      
            <div class="flex flex-wrap">
                <div class="sm:w-full md:w-full lg:w-1/2 xl:w-1/2 p-0"><p><b>Paciente:</b> {{ info.paciente }}</p></div>
                <div class="sm:w-full md:w-full lg:w-1/2 xl:w-1/2"></div>
                <div class="sm:w-full md:w-full lg:w-4/12 xl:w-1/12 p-0"><b>Habitación:</b> {{ info.habitacion }}</div>                
                <div class="sm:w-full md:w-full lg:w-4/12 xl:w-1/12"><b>Nivel Precios:</b> {{ info.nivelprecio }}</div>
            </div>
            <div class="md:w-full lg:w-1/12 xl:w-1/12 m-1">
                    <ValidationProvider>
                        <!-- :disabled="bloqueoBusqueda" -->
                        <vs-input label="Habitación:" class="w-full"  v-model="info.habitacion" v-on:change="BusquedaAdmisionHabitacion" />
                    </ValidationProvider>
            </div>

            <BusquedaAdmision   ref="componenteAdmisiones"  
                                @datos_admision="DatosAdmision"
                                @limpiar_datos_admision="LimpiarPantallaCancelar"
                                :api_campos="['Serie','Codigo','Nombres','Apellidos','Habitacion']"
                                :api_titulos="['Serie#','Admision#','Nombres','Apellidos','Habitación']"
                                >                
            </BusquedaAdmision>     
            <div class="flex flex-wrap" >
                <BusquedaTipoOrdenPorPermiso ref="componenteTipoOrden" 
                                            @datos_tipo_orden="DatosTipoOrden"                                              
                                           :filtroTipoOrden="this.info.filtroTipoOrden"
                                           :filtroTipoOrdenGeneral="this.info.filtroTipoOrdenGeneral"
                                >                
                </BusquedaTipoOrdenPorPermiso>
                <div class="w-full md:w-full lg:w-2/12 xl:w-2/12 m-1">
                    <vs-input label="Bodega"  
                            class="w-full" 
                            :value="info.bodega" disabled />      
                </div>
                <div class="w-full md:w-full lg:w-7/12 xl:w-3/12 m-1">
                    <ValidationProvider name="Fecha" rules="required" class="required">
                        <label style="font-size:12px; color:gray;">Fecha a Realizar Estudio</label>                     
                        <flat-pickr  class="w-full" v-model="info.fechaEstudio" :config="configFromdateTimePicker"  />
                    </ValidationProvider>
                </div>                    
            </div>
            <div class="flex flex-wrap">
                <div class="sm:w-full md:w-full lg:w-2/12 xl:w-1/12 m-1">                    
                    <label class="typo__label" style="color: rgba(0,0,0,.7); font-size: 10pt;">Médico/Terapista:&nbsp;</label>
                    <vs-col vs-type="flex" vs-align="center">
                        <vs-input  v-model="info.medico" @keyup.enter="BusquedaMedicoAdmision()" @keydown.tab="BusquedaMedicoAdmision()" ></vs-input>
                        <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="BusquedaMedicoAdmision()" icon="fa-search"></vs-button>
                    </vs-col>
                </div>
                <div class="w-full md:w-full lg:w-9/12 xl:w-5/12 m-1">
                    <ValidationProvider rules="required" v-slot="{ errors }">
                        <vs-input label="Nombre del Médico/Terapista:"  
                                    class="w-full" 
                                    :value="info.nombreMedico" disabled 
                                    :danger="errors.length > 0"
                                    :danger-text="(errors.length > 0) ? errors[0] : null" />                                
                    </ValidationProvider>
                </div>
            </div>
            <div class="w-full md:w-full lg:w-11/12 xl:w-6/12 m-2">
                <ValidationProvider rules="max:100|required" v-slot="{ errors }">
                    <vs-textarea class="w-full" label="Observaciones" counter="200" 
                    v-model="info.observaciones" 
                                :danger="errors.length > 0" 
                                :danger-text="(errors.length > 0) ? errors[0] : null"/>
                </ValidationProvider>
            </div> 
            
    <!-- {{TempProductoSelec}}
    {{ObservacionesRegion}} -->
            <!-- CARGOS AGREGADOS -->
            <div class="flex flex-wrap mt-4" >                
                <div class="sm:w-full md:w-full lg:w-full xl:w-7/12  p-0">
                    <div class="w-full p-0">
                        <h3 style="color: #3498db;"><b>Productos a Cargar</b></h3>
                    </div>
                    <vs-table2 ref="tablacargos" max-items="8" search pagination :data="cargos" height="470px">
                        <template slot="thead">
                            <th order="Código" width="10%">Código</th>
                            <th order="Nombre" width="37%" >Nombre</th>
                            <th order="Nombre" width="10%" >U. Medida</th>
                            <th  order="Ajeno" width="16%" >Ajeno</th>
                            <th order="Precio" width="12%" > Precio.U</th>
                            <th order="Cantidad" width="10%">Cant.</th>
                            <th order="Accion" width="5%"></th>
                            <!-- <th  width="50px">Acción</th> -->
                            <!-- <th order="Exist" width="50px">Existencia</th> -->
                        </template>
                        <template slot-scope="{data}">
                            <tr :key="tr.Codigo+indextr" v-for="(tr, indextr) in data">
                                <vs-td2 class = "w-1/12">
                                    {{ tr.Codigo }}
                                </vs-td2>
                                <vs-td2 class = "w-3/12">
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">
                                        <small>
                                            {{ tr.Nombre }}
                                        </small>
                                    </div>
                                </vs-td2>
                                <vs-td2 class = "w-1/12" style="word-wrap: break-word;white-space: wrap;">
                                    {{ tr.UnidadMedida }}
                                </vs-td2>
                                <vs-td2  class = "w-2/12" style="padding: 0 !important;">  
                                    
                                     
                                   <vx-input-group class="" v-if="info.tipoOrden=='AJE'">
                                        <vs-input  v-model="tr.Ajeno" v-on:blur="cargarAjeno(tr.Ajeno,tr)"
                                                   @keyup.enter="cargarAjeno(tr.Ajeno,tr)"  @keyup.tab="cargarAjeno(tr.Ajeno,tr)"  />
                                        <template slot="append">
                                            <div class="append-text btn-addon">
                                                <button type="submit" v-show="false" name="button"></button>
                                                <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="cargarAjeno(tr.Ajeno,tr)" icon="fa-search"></vs-button>
                                            </div>
                                        </template>
                                    </vx-input-group>    
                                   
                                        <div class="w-full" style="word-wrap: break-word; white-space: wrap; background-color:#ecf0f1;">
                                            <small>{{ tr.NombreAjeno}}</small>
                                        </div>                                                                                                        
                                </vs-td2>
                                <vs-td2 v-if="tr.Precio==0.00" class="w-1/12"> 
                                    <vs-input type="number" class="w-full" v-model="tr.PrecioLibre"/>                      
                                </vs-td2>
                                <vs-td2 v-if="tr.Precio!=0.00" class="w-1/12">
                                    {{parseFloat(tr.Precio).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                </vs-td2>
    
                                <vs-td2 class="w-3/12">
                                    <ValidationProvider rules="required|numero_min:0" v-slot="{ errors }" class="required">                                        
                                        <vs-input class="w-full" type="number" v-model="tr.Cantidad"  
                                                  v-on:blur="validarMaxMin(tr)" @keyup.enter="validarMaxMin(tr)"  @keyup.tab="validarMaxMin(tr)"
                                                  :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                    </ValidationProvider>                                    
                                </vs-td2>
                                <vs-td2 width='5%'>
                                    <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="EliminarProducto(tr)"></vs-button>
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
                    <!-- {{cargos}} -->
    
                </div>
    
                <!-- LISTADO DE PRODUCTOS -->
                <div class="sm:w-full md:w-full lg:w-full xl:w-5/12  pl-2">
                    <div class="w-full pl-2">
                        <h3 style="color: #3498db;"><b>Lista de Productos</b></h3>
                    </div>
                    <div>
                        <vs-table2 max-items="10" search pagination :data="productos" height="470px">
                            <template slot="thead">
                                <th order="Código" width="10%">Código</th>
                                <th order="Nombre" width="70%">Nombre</th>
                                <th order="Precio" width="20%">Precio</th>
                                <!-- <th order="Precio" width="20%">Categoría</th> -->
                                <!-- <th order="Exist" width="50px">Existencia</th> -->
                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data" @click="TempProductoSelec = [];ObservacionesRegion='';(tr.Existencia>0 || tr.Tipo == 'S')? Interfaz().agregaProducto(tr) :null; " :style="{'cursor':tr.Existencia<=0?'default':'pointer','opacity':tr.Existencia<=0?0.5:1}">
                                    <vs-td2>
                                        {{ tr.Codigo }}
                                    </vs-td2>
                                    <vs-td2>
                                        <div style="word-wrap: break-word;white-space: wrap;">
                                            {{ tr.Nombre }}
                                        </div>
                                    </vs-td2>
                                    <vs-td2 style="min-width: 120px;">
                                        {{parseFloat(tr.Precio).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                    </vs-td2>
                                    <!-- <vs-td2>
                                        <div style="word-wrap: break-word;white-space: wrap;">
                                            {{ tr.Categoria }}
                                        </div>
                                    </vs-td2>                                     -->
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                </div>
            </div>
            <vs-divider></vs-divider>
            <div class="flex bottom">
                <vs-button color="danger" class="mr-5" @click="LimpiarOrden">Limpiar Orden</vs-button>
                <vs-spacer></vs-spacer>
                <div style="width:200px">
                    <tr>
                        <td class="p-3">
                            <h4>TOTAL</h4>
                        </td>
                        <td class="p-3" width="200px" style="text-align:right">
                            <h4>{{totalCargos}}</h4>
                        </td>
                    </tr>
                </div>
                <vs-spacer></vs-spacer>
                <!-- <vs-button color="success" @click="Guardar().reporte()">Reporte</vs-button> -->
                <vs-button @click="Guardar().pedido()">Cargar Orden</vs-button>
            </div>
        </vx-card>
        <!-- {{  cargos }} -->
        <vs-popup class="popNuevo" title="Región, Indicaciones u Observaciones" :active.sync="VentanaObservacionesRegion" style="z-index:99999" id="4">
        <div  class="content-wrapper">

            <div class="mb-4">
                <div class=" bg-grid-color-secondary h-12 mt-5">
                    <vs-col><b><small> Código: </small></b> {{ TempProductoSelec.Codigo }}</vs-col>
                    <vs-col><b><small> Producto: </small></b> {{ TempProductoSelec.Nombre }} </vs-col>
                    <vs-col><b><small> Descripción: </small></b> {{ TempProductoSelec.Descripcion }}</vs-col>
                    <vs-col><b><small> Categoría: </small></b> {{ TempProductoSelec.Categoria }}</vs-col>
                    <vs-col><b><small> Unidad Médida: </small></b> {{ TempProductoSelec.UnidadMedida }}</vs-col>
                </div>
            </div>
           

           <div>
            <vs-row>
                <vs-textarea class="w-full" label="Región, Indicaciones u Observaciones (Texto máximo  400 caracteres)" counter="400" v-model="ObservacionesRegion" />
            </vs-row>
                <div v-if="MostrarMensajeRegion"  style="color:white; border-radius:5px;padding:5px;font-size:15px;background-color:#F9A825">
                    Favor ingresar Región, indicaciones u observación.
                </div>
                <br>
            </div>
                
          
          
            <vs-button color="primary" style="float: right" type="filled" icon-pack="feather" icon="icon-save" @click="(ObservacionesRegion.length>0)?Interfaz().agregaProducto(TempProductoSelec):MostrarMensajeRegion= true;">
                Grabar</vs-button>
            <vs-button color="danger" style="float: right" type="border" icon-pack="feather" icon="icon-x" @click="ObservacionesRegion ='';
                        TempProductoSelec = []; ObservacionesRegion='';VentanaObservacionesRegion=false; MostrarMensajeRegion = false;">
                Cancelar</vs-button>
          
        </div>
        </vs-popup>        
    </div>
    </template>
    
    <script>
    import BusquedaAdmision from "/src/components/sermesa/modules/admisiones/BusquedaAdmision.vue"
    import BusquedaTipoOrdenPorPermiso from "/src/components/sermesa/modules/admisiones/BusquedaTipoOrdenPorPermiso.vue"
    import flatPickr from 'vue-flatpickr-component';
    import 'flatpickr/dist/flatpickr.css';
    import { Spanish as SpanishLocale } from 'flatpickr/dist/l10n/es.js';
    import moment from 'moment';    

    export default {
        components:{
            BusquedaAdmision,
            BusquedaTipoOrdenPorPermiso,            
            flatPickr            
        },
        data() {    
            return {
                
                listado_reportes:[],
                MostrarMensajeRegion: false,
                quitar_espacio_buscar : {
                    padding: '0 !important;'
                },
                
                busquedaAjeno : {
                    Codigo:undefined,
                    Nombre:null,
                    Apellido:null,
                    Celular:null,
                    Tipo:null,
                    Especialidad:null
                },
                info: {
                    serie:null,
                    numeroAdmision:null,  
                    verificacionSeguro:null,                  
                    nivelprecio: null,
                    bodega: null,
                    tipoOrden: null,
                    filtroTipoOrden: [],
                    filtroTipoOrdenGeneral: [],
                    medico:null,
                    nombreMedico:'SIN REFERENCIA',
                    observaciones:null,
                    fechaEstudio:null,
                    habitacion: null,
                    paciente: null
                },
                cargos: [],
                productos: [],  
                medicos:[],
                cargoSeleccionado: null,
                Estado_VentanaEmergente_Busqueda: false,
                categoriasAjenos:[],   
                serviciosMedico:null,
                validarCandadoAdmision:null,
                categoriaMedicoExtraoridnario:330621,
                mostrarOrden:false,  
                limpiarAdmision: false,        
                configFromdateTimePicker: {
                    minDate: new Date(),
                    maxDate: this.Otros().sumarDias(new Date(), 360),
                    locale: SpanishLocale,
                    dateFormat: "d/m/Y H:i",                    
                    enableTime: true,                                   
                    time_24hr: true
                },
                money: {
                    decimal: ',',
                    thousands: ',',
                    prefix: 'Q',
                    precision: 2,
                    masked: false
                },
                CategoriaObservaciones: [],
                VentanaObservacionesRegion: false,
                ObservacionesRegion: '',
                TempProductoSelec: []
            }
        },
        computed: {
            totalCargos() {
                return this.cargos.reduce((acumulador, cargo) => acumulador + (cargo.PrecioLibre ? parseFloat(cargo.PrecioLibre) : parseFloat(cargo.Precio))  * cargo.Cantidad, 0).toLocaleString("es-GT", {
                    style: "currency",
                    currency: "GTQ"
                })
    
            },
            montoTotal() {
                return this.cargos.reduce((acumulador, cargo) => acumulador + (cargo.PrecioLibre ? parseFloat(cargo.PrecioLibre) : parseFloat(cargo.Precio))  * cargo.Cantidad, 0)
            },
            sesion() {
                return this.$store.state.sesion
            }
        },
        methods: {     
            validarMaxMin(producto){
                if(producto.TieneMaxMin == 1){                    
                    if(parseFloat(producto.Cantidad) > parseFloat(producto.MaxAdmision) ){
                        producto.Cantidad = producto.MaxAdmision
                        this.$vs.notify({
                                    time:4000,
                                    title: 'Maximos y Minimos',
                                    color: 'danger',
                                    text:`La cantida maxima permitida para el producto es de  ${producto.Cantidad}`,
                                    position: 'top-center'
                                })
                       
                    }
                    /* validacion de minimos
                    if(parseFloat(producto.Cantidad) < parseFloat(producto.MinAdmision)){
                        producto.Cantidad = producto.MinAdmision
                        this.$vs.notify({
                                    time:4000,
                                    title: 'Maximos y Minimos',
                                    color: 'danger',
                                    text:`La cantida minima permitida para el producto es de  ${producto.Cantidad}`,
                                    position: 'top-center'
                                })
                    }
                    */
                }
            },    
            EliminarProducto(producto){
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',                    
                    acceptText: 'Aceptar',
                    cancelText: 'Cancelar',
                    text: '¿Dar eliminar el producto  \'' + producto.Nombre + '\' con cantidad ' + producto.Cantidad + ' ? ',
                    accept: () => {
                        let indice = this.cargos.indexOf(producto)
                        if(indice > -1){
                            this.cargos.splice(indice,1)  
                        }                        
                    }
                })
                
            },
            BusquedaAdmisionHabitacion() {
            this.axios.post('/app/v1_JefeCaja/consulta_admision_recalculo', {
                    Habitacion: this.info.habitacion,
                    Opcion: 'CONSULTA',
                    Serie: this.info.serie,
                    Codigo: this.info.numeroAdmision,
                    SubOpcion: 'ADMISION',
                    activo: 1,
                    pagina: 1,
                    tieneHabitacion: 0
                })
                .then(resp => {
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {


                        resp.data.json.map(datos => {
                            this.info.serie = datos.Serie
                            this.info.numeroAdmision = datos.Codigo
                            this.info.verificacionSeguro = datos.VerificacionSeguro
                            this.info.paciente = datos.Paciente
                            this.info.habitacion = datos.Habitacion
                            this.info.nivelprecio = datos.NivelPrecios
                            this.info.medico = datos.CodigoMedico
                            this.info.nombreMedico = datos.Medico;
                            this.info.fechaEstudio = new Date()
                            this.DatosProductos()
                            this.$nextTick(()=>{
                                if(this.info){
                                this.$refs.componenteAdmisiones.recargarComponente(datos)
                                }
                            })
                            //this.info.Asegura = datos.Asegura
                            //this.info.TipoDescuento = datos.TipoDescuento;
                            //this.info.TipoPaciente = datos.TipoPaciente;
                            //this.bloqueoBusqueda = true;
                            //this.ValidacionTipoDescuento(datos.TipoDescuento);
                            //this.PermisosTipoOrdenes();

                            // setTimeout(() => {
                            //     this.CargarMedicamentos();
                            // }, 1500)

                        })
                    }
                })
                .catch()
            //}

            },
            BusquedaMedicoAdmision(){
                if(this.info.medico != '' && this.info.medico != null && this.info.medico != 0){
                    this.axios.post('/app/emergencia/Busqueda_Ajenos', {
                        Codigo: this.info.medico,
                        Opcion:"C",
                        SubOpcion:"2"
                    })
                    .then(resp => {
                        if(resp.data.codigo == 0){
                            if(resp.data.json.length == 1){
                                this.info.nombreMedico = resp.data.json[0].Nombre?.trim()+' '+resp.data.json[0].Apellido?.trim()
                            }else{
                                this.$refs.BuscarMedicoAdmision.iniciar((data) => {
                                    if(data != null){
                                        this.info.medico = data.Codigo
                                        this.info.nombreMedico = data.Nombre?.trim()+' '+data.Apellido?.trim()
                                    }
                                })
                            }
                        }else{
                            this.info.medico = null
                            this.info.nombreMedico = "SIN REFERENCIA"
                        }
                    })
                }else{
                    this.$refs.BuscarMedicoAdmision.iniciar((data) => {
                        if(data != null){
                            this.info.medico = data.Codigo
                            this.info.nombreMedico = data.Nombre?.trim()+' '+data.Apellido?.trim()
                        }else{
                            this.info.medico = null
                            this.info.nombreMedico = "SIN REFERENCIA"
                        }
                    })
                }
            },
            seleccionarAjeno(ajeno){
                this.DatosAjeno(ajeno,this.cargoSeleccionado,true)    
            },
            Mostrar_resultado_ajeno(value,codigo,cargo) {      
                this.cargoSeleccionado = cargo          
                if (value.length === 1) {
                    this.DatosAjeno(value[0],cargo,false)
                 
                } else if (value.length > 1) {
                    this.busquedaAjeno.Codigo = codigo
                    cargo.NombreAjeno = null
                    this.Estado_VentanaEmergente_Busqueda = true;
                }    
            },
            filtrarAjeno(){
                this.axios.post('/app/emergencia/Busqueda_Ajenos', {
                        Opcion:'C',
                        SubOpcion:'2',
                        Codigo: this.busquedaAjeno.Codigo && this.busquedaAjeno.Codigo.length > 0 ? this.busquedaAjeno.Codigo : undefined,
                        Nombre: this.busquedaAjeno.Nombre ? this.busquedaAjeno.Nombre : null ,
                        Apellido: this.busquedaAjeno.Apellido,
                        Tipo: this.busquedaAjeno.Tipo,
                        Especialidad: this.busquedaAjeno.Especialidad
                    })
                    .then(resp => {
                        
                        if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                            this.medicos = []
                            if(this.busquedaAjeno.Especialidad && this.busquedaAjeno.Especialidad?.trim()?.length > 0){
                                this.medicos = resp.data.json.filter( medico => medico.Especialidad.toLowerCase().includes(this.busquedaAjeno.Especialidad.trim().toLowerCase()) )
                            }else{
                                resp.data.json.map(data => {
                                    this.medicos.push({
                                        ...data
                                    })
                                }) 
                            }                                                       
                        } else {
                            this.medicos = []
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Ajenos',
                                text: (resp.data.codigo < 0) ? resp.data.descripcion : 'Sin Registros',
                            })
    
                        }
                    })
            },
            cargarAjeno(codigo,cargo){
                this.axios.post('/app/emergencia/Busqueda_Ajenos', {                    
                        Opcion:'C',
                        SubOpcion:'2',
                        Codigo: codigo && codigo.trim().length > 0 ? codigo: undefined
                    })
                    .then(resp => {
    
                        if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                            this.medicos = []
                            resp.data.json.map(data => {
                                this.medicos.push({
                                    ...data
                                })
                            })
    
                            this.Mostrar_resultado_ajeno(this.medicos,codigo,cargo);
    
                        } else {
    
                            this.medicos = []
                            cargo.Ajeno = ''
                            cargo.NombreAjeno = null
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Ajenos',
                                text: (resp.data.codigo < 0) ? resp.data.descripcion : 'Sin Registros Codigo Ajeno',
                            })
    
                        }
    
                    })
                    .catch(() => {
                        this.$vs.loading.close();
    
                    })
            },
            DatosTipoOrden(datos){
                this.info.tipoOrden = datos.Codigo
                this.info.bodega = datos.BodegaDefault
                this.cargos = []
                this.DatosProductos()
            },  
            CargarCategoriasAjenos(){
                this.axios.post('/app/emergencia/ConsultaCategoriasAjenos', {
                                })
                          .then(resp => {
                                    const categoriasAjenos = resp.data.json[0].descripcion
                                    if(resp.data.json && resp.data.json.length > 0 && resp.data.json[0].codigo == 0){
                                        this.categoriasAjenos = categoriasAjenos.split(',')
                                        this.serviciosMedico = resp.data.json[0].ServiciosMedicos
                                        this.validarCandadoAdmision = resp.data.json[0].ValidarCandadoAdmision
                                    }else{
                                        this.categoriasAjenos = ['99','98','97']
                                        this.serviciosMedico = '97'
                                        this.validarCandadoAdmision = 'S'
                                    }                                    
                                })
            },
            DatosAjeno(datos,cargo,opcionBusqueda){    

                this.axios.post('/app/emergencia/ValidarCargoAjeno', {
                                    serie:this.info.serie,
                                    codigoAdmision:this.info.numeroAdmision,
                                    medicoAjeno: datos.Codigo,
                                    categoriaCargo: cargo.Categoria
                                })
                          .then(function (resp) {
                                    const ajenoValido = resp.data?.json[0]?.descripcion ?? 0
                                    if(ajenoValido == '0'){
                                        cargo.Ajeno = undefined;                                        
                                        cargo.NombreAjeno = null;
                    
                                        this.$vs.notify({
                                            color: '#B71C1C',
                                            title: 'Alerta',
                                            text: 'No es posible asignar el cargo al médico seleccionado. Comuníquese con su jefatura',
                                        })                                                                                   
                                    }else{   
                                        cargo.Ajeno = datos.Codigo
                                        cargo.NombreAjeno = 'Dr. '+datos.Nombre?.trim()+' '+datos.Apellido?.trim();                                           
                                        if(opcionBusqueda){
                                            this.busquedaAjeno.Codigo = undefined
                                            this.busquedaAjeno.Nombre = null
                                            this.busquedaAjeno.Apellido = null
                                            this.busquedaAjeno.Especialidad = null
                                            this.busquedaAjeno.Tipo = null
                                            this.Estado_VentanaEmergente_Busqueda = false;
                                        }  
                                        this.$refs.tablacargos.actualizacionEncabezado()
                                    }
                                   
                                }.bind(this))
                                
                                
            },
            DatosAdmision(datos){
                this.info.serie = datos.Serie
                this.info.numeroAdmision = datos.Codigo
                this.info.verificacionSeguro = datos.VerificacionSeguro
                this.info.paciente = datos.Paciente
                this.info.habitacion = datos.Habitacion
                this.info.nivelprecio = datos.NivelPrecios     
                this.info.medico = datos.CodigoMedico   
                this.info.nombreMedico = datos.Medico        
                this.info.fechaEstudio = new Date()     
                this.DatosProductos()
            },  
            ObservacionesObligatorias(){
                this.axios.post('/app/emergencia/CategoriasObservaciones', {})
                                .then(resp => {                                  
                                    if (resp.data && resp.data.json.length > 0) {
                                        this.CategoriaObservaciones = resp.data.json
                                    }
                                })
            },               
            DatosProductos(){

                if(!this.info.tipoOrden || !this.info.nivelprecio){
                    this.productos = []
                    return
                }
                this.ObservacionesObligatorias();
                this.axios.post('/app/emergencia/BusquedaProductos', {
                                    MostrarServicios : 'S',
                                    NivelPrecio: this.info.nivelprecio,
                                    TipoOrden: this.info.tipoOrden,
                                    Bodega: null
                                })
                                .then(resp => {                                  
                                    if (resp.data && resp.data.json.length > 0) this.productos = resp.data.json
                                })
            },            
            LimpiarAdmisiones(){
                this.$refs.componenteAdmisiones.limpiar_campos()
                this.$refs.componenteTipoOrden.limpiarTipoOrden()
            },  
            LimpiarOrden(){
                this.mostrarOrden = false
                this.tipoOrdenGrabada = null
                this.correlativoOrden = null
                this.LimpiarPantalla()
            },
            LimpiarPantallaCancelar(){
                this.info.serie = null
                this.info.numeroAdmision = null
                this.info.verificacionSeguro = null
                this.info.paciente = null
                this.info.habitacion = null
                this.info.nivelprecio = null  
                this.info.bodega = null
                this.info.tipoOrdenGrabada = null
                this.info.tipoOrden = null
                this.info.medico = null
                this.info.correlativoOrden = null
                this.info.nombreMedico = 'SIN REFERENCIA'
                this.info.observaciones = null
                this.info.fechaEstudio = new Date()
                this.cargos = []   
                this.productos = []                
                this.$refs.componenteTipoOrden.limpiarTipoOrden()
                this.mostrarOrden = false
                this.TempProductoSelec = [];
                this.VentanaObservacionesRegion=false;
                this.MostrarMensajeRegion = false;
                this.ObservacionesRegion = '';
            },
            LimpiarPantalla(){
                this.info.serie = null
                this.info.numeroAdmision = null
                this.info.verificacionSeguro = null
                this.info.paciente = null
                this.info.habitacion = null
                this.info.nivelprecio = null  
                this.info.bodega = null
                this.info.tipoOrdenGrabada = null
                this.info.tipoOrden = null
                this.info.medico = null
                this.info.correlativoOrden = null
                this.info.nombreMedico = 'SIN REFERENCIA'
                this.info.observaciones = null
                this.info.fechaEstudio = new Date()
                this.cargos = []   
                this.productos = []                
                this.$refs.componenteAdmisiones.limpiar_campos()
                this.$refs.componenteTipoOrden.limpiarTipoOrden()
                this.mostrarOrden = false
            },
            Consulta() {
                return {
                    habitacion: (datos) => {
                        this.info.habitacion = datos.Habitacion
                        this.info.nivelprecio = datos.NivelPrecios
                        this.info.paciente = datos.Paciente
    
                    },
                    init: () => {
                        this.LimpiarPantalla()
                    }
                }
            },
            Guardar() {
                return {
                    pedido: () => {
                        if (!this.info.numeroAdmision) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Alerta',
                                text: 'Debe de ingresar la admisión',
                            })
                            return false
                        }
                        
                        

                        if(this.info.tipoOrden == 'AJE'){
                            let vacio = this.cargos.some( cargo => !cargo.Ajeno || cargo.Ajeno.trim().length == 0 )
                                                                                    
                            if(vacio){
                                this.$vs.notify({
                                                        color: '#B71C1C',
                                                        title: 'Alerta',
                                                        text: 'Debe llenar el campo Ajeno para todos los cargos',
                                                        time: 4000,
                                                        position: 'top-center'
                                                    })
                                return
                            }
                        }

                        let vacio = this.cargos.some( cargo => ( (parseFloat(cargo.Precio) == 0.00) && ( parseFloat( ( (cargo?.PrecioLibre?.trim()??'') =='') ?0.00:cargo.PrecioLibre.trim()) <= 0.00)) )                         
                        if(vacio){
                                this.$vs.notify({
                                                        color: '#B71C1C',
                                                        title: 'Alerta',
                                                        text: 'Debe ingresar el Precio.U para los cargos con precio Q0.00 en la lista de productos',
                                                        time: 4000,
                                                        position: 'top-center'
                                                    })
                                return
                            }

                        let indiceCargoNegativo = this.cargos.findIndex( cargo => parseFloat(cargo.Cantidad) <= 0.00 )                         
                        if(indiceCargoNegativo != -1){
                                let productoNegativo = this.cargos[indiceCargoNegativo]
                                this.$vs.notify({
                                                        color: '#B71C1C',
                                                        title: 'Alerta',
                                                        text: `El producto ${productoNegativo.Nombre} cuenta con una cantidad negativa de ${productoNegativo.Cantidad}`,
                                                        time: 5000,
                                                        position: 'top-center'
                                                    })
                                return
                            }

                        if(this.cargos.length == 0){
                            return
                        }
                        const cargos = this.cargos.map(m => {
                            return {
                                Codigo: m.Codigo,
                                Cantidad: m.Cantidad,
                                Ajeno: m?.Ajeno??'',
                                PrecioLibre: m?.PrecioLibre??'',
                                RegionObservaciones: m.ObservacionesRegion.split('@').join('')
                            }
                        })
                        this.axios.post('/app/emergencia/CargarOrdenEmergencia', {
                            serie:this.info.serie,
                            codigoAdmision:this.info.numeroAdmision,
                            nivelPrecio:this.info.nivelprecio,
                            tipoOrden:this.info.tipoOrden,
                            medico: this.info.medico && this.info.medico != '' ? this.info.medico : '1',
                            observaciones:this.info.observaciones,                            
                            fechaEstudio:this.info.fechaEstudio && this.info.fechaEstudio != '' ? moment(this.info.fechaEstudio, 'DD/MM/YYYY HH:mm').format('DD/MM/YYYY HH:mm'):null,
                            montoTotal:this.montoTotal,                            
                            cargos,
                            idTipoCargo:1                            
                        }).then((respuesta) => {
                                const Respuesta = respuesta.data?respuesta.data.json[0].descripcion:respuesta.json.descripcion
                                const CodigoError = respuesta.data?respuesta.data.json[0].tipo_error:respuesta.json.tipo_error
                                if(CodigoError==1){
                                    this.$vs.notify({
                                                        color: '#B71C1C',
                                                        title: 'Alerta',
                                                        text: Respuesta,
                                                        time: 6000,
                                                        position: 'top-center'
                                                    })
                                    return
                                }

                                this.$vs.dialog({
                                        type: 'confirm',
                                        color: 'success',
                                        acceptText: 'Aceptar',
                                        cancelText: 'Cancelar',
                                        title: 'Orden Grabada',
                                        text: Respuesta,
                                        clientWidth: 100,
                                    });        

                                const arreglo = Respuesta.split('-')
                                this.info.tipoOrdenGrabada = this.info.tipoOrden
                                this.info.correlativoOrden  = arreglo[1]                                        
                                this.mostrarOrden = true                  
                                                                
                                this.$genera_reporte({
                                        Nombre: "Cargos por Orden",
                                        Data_source: {TipoOrden:this.info.tipoOrdenGrabada,
                                                      CodigoOrden:Number(this.info.correlativoOrden)},
                                        Data_report: this.listado_reportes
                                    }).catch(() => {
                                })
                                /*
                                this.$reporte_modal({
                                    Nombre: "Cargos por Orden",
                                    Opciones: {TipoOrden:this.info.tipoOrdenGrabada,
                                               CodigoOrden:Number(this.info.correlativoOrden)}
                                }).catch(() => {
                                }) 
                                */
                                this.LimpiarPantalla()
                            }
                        )
                    }
                }
            },
            Otros(){
                return {
                    sumarDias: (fecha, dias) => {
                        fecha.setDate(fecha.getDate() + dias);                        
                        return fecha;
                    }
                }                
            },
            Interfaz() {
                return {
                    agregaProducto: (producto) => {
                        const servicioMedicoExtraordinario = 330621;
                        this.VentanaObservacionesRegion=false;
                        let Cantidad = 1
                        if( (this.categoriasAjenos.includes(producto.Categoria)    &&  this.info.tipoOrden!='AJE') ||
                            (this.categoriaMedicoExtraoridnario == producto.Codigo &&  this.info.tipoOrden!='AJE') ){
                            this.$vs.notify({
                                                color: '#B71C1C',
                                                title: 'Alerta',
                                                text: 'Este producto es para un tipo de orden aje de honorarios',
                                            })   
                            return
                        }else if( this.validarCandadoAdmision == 'S' && this.info.verificacionSeguro == 'S' &&
                                 (servicioMedicoExtraordinario == producto.Codigo || this.serviciosMedico == producto.Categoria) 
                                ){
                            this.$vs.notify({
                                                color: '#B71C1C',
                                                title: 'Alerta',
                                                text: 'No se puede cargar Honorarios en una admisión ya validada por Seguros...',
                                            })   
                            return
                        }
                        
                        //Se valida si solicita Observaciones según la categoría del producto.
                 
                        //if (this.CategoriaObservaciones.categoria.includes(producto.Categoria) ){
                        if (this.CategoriaObservaciones.filter( f => f.categoria == producto.Categoria).length > 0 &&  this.ObservacionesRegion.length === 0){
                            this.VentanaObservacionesRegion = true;
                            this.MostrarMensajeRegion = false;
                            this.TempProductoSelec =producto; 
                        }else{
                            if(producto.TieneMaxMin == 1){
                                /*
                                if(parseFloat(producto.MaxAdmision) < parseFloat(producto.MinAdmision)){
                                    this.$vs.notify({
                                                color: '#B71C1C',
                                                title: 'Alerta',
                                                text: 'La cantidad minima para la admisión es mayor a la cantidad maxima, por favor corregir antes de cargar',
                                                time:5000
                                            })   
                                    this.ObservacionesRegion ='';
                                    this.TempProductoSelec = [];
                                    return
                                }
                                */                                
                                this.$vs.notify({
                                    time:4000,
                                    title: 'Maximos y Minimos',
                                    color: 'success',
                                    text:`Producto con cantidad maxima por admisión de ${producto.MaxAdmision}`,
                                    position: 'top-center'
                                })
                            }

                            this.cargos.unshift({
                                ...producto,
                                Cantidad: Cantidad,
                                ObservacionesRegion: this.ObservacionesRegion
                            })
                            this.ObservacionesRegion ='';
                            this.TempProductoSelec = [];
                        }

                    }
                }
            },
        },
        async beforeCreate(){
            this.listado_reportes = await this.$recupera_parametros_reporte('Cargos por Orden')        
        },
        mounted() {
            this.Consulta().init()
            this.CargarCategoriasAjenos()
            for(let privilegio of this.$store.state.privilegios){
               if(privilegio.Privilegio.includes("TIPO_HOSPITAL")){
                   let tipo_orden = privilegio.Privilegio.substring(privilegio.Privilegio.length-2,privilegio.Privilegio.length)                    
                   this.info.filtroTipoOrden.push(tipo_orden)                    
                }else if(privilegio.Privilegio.includes("TIPO_GENERAL")){
                    let tipo_orden_general = privilegio.Privilegio.substring(privilegio.Privilegio.length-3,privilegio.Privilegio.length)                    
                    this.info.filtroTipoOrdenGeneral.push(tipo_orden_general)                    
                }
            }
        },

    }
    </script>
    
    <style scoped>
    .popNuevo .vs-popup {
       
        width: 990px !important;
        
        
        
        
    }
    .content-wrapper {
        overflow-y: auto; /* Permitir desplazamiento vertical si el contenido es grande */
        width: 100%;
        height: 100%;
    }
    .cantidad button {
        height: 25px;
        width: 30px;
        border: 1px solid #ccc;
        border-radius: 5px;
    
    }
    
    .cantidad div {
        display: inline-block;
        height: 25px;
        width: 30px;
        text-align: center;
    }

    .quitar_espacio {
        padding: 0;
        margin: 0;        
    }

    .quitar_espacio_buscar > .label{
        padding: 0 !important;
    }
    </style>    