<template>
<vx-card type = "2" title="Modificar Médico a la Orden" class="justify-center ">    
    <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
        <form ref="formModificarOrden" method="post" @submit.prevent="handleSubmit(modificar_ajeno())">
            <div class="flex flex-wrap">
                <div class="w-full md:w-full lg:w-4/12 xl:w-2/12 m-2">
                    <label>Admision:</label> 
                    &nbsp;&nbsp;
                    <label style="color:black">{{info.admision}}</label>                                 
                </div>
                <div class="w-full md:w-full lg:w-6/12 xl:w-4/12 m-2 lg:pr-6">
                    <label>Paciente:</label> 
                    &nbsp;&nbsp;
                    <label style="color:black">{{info.paciente}}</label>      
                </div>
                <div class="w-full md:w-full lg:w-1/12 xl:w-5/12"></div>
                <vs-divider position="left">Orden</vs-divider>
                <div class="w-full md:w-4/12 lg:w-1/12 xl:w-1/12 m-2" tabindex="1">
                    <ValidationProvider label="*Tipo" rules="required" v-slot="{ errors }">
                        <SM-Buscar class="w-full" label="*Tipo" v-model="info.tipoOrden" api="app/v1_JefeCaja/ObtenerTiposOrdenes" :api_campos="['Codigo','Nombre']" :api_titulos="['Tipo de Orden','Descripción']" :api_filtro="{'Activa':0,'opcion':'C','subOpcion':'1'}" 
                                   api_campo_respuesta="Codigo" :api_preload="true"  :callback_buscar="cargar().datos_tipo_orden" 
                                   :disabled_busqueda="info.desactivarCamposOrden"
                                   :disabled_texto="info.desactivarCamposOrden"
                                   :danger="errors.length > 0" 
                                   :danger-text="(errors.length > 0) ? errors[0] : null"/>                                
                    </ValidationProvider>                                
                    <div v-if="info.descripcionOrden && info.descripcionOrden!=''" style="background-color:#ecf0f1;">
                        {{ info.descripcionOrden }}
                    </div>
                </div>
                <div class="w-full md:w-4/12 lg:w-4/12 xl:w-2/12 m-2" tabindex="2">  
                    <ValidationProvider label="*Número" rules="required" v-slot="{ errors }">
                        <SM-Buscar class="w-full" label="*Número" v-model="info.orden" api="app/v1_JefeCaja/obtener_orden" :api_campos="['Tipo','orden','serie','noAdmision','nombres','apellidos']" :api_titulos="['Tipo#','orden#','Serie de la Admisión','Admisión','Nombres','Apellidos']" :api_filtro="{'iOpcion':'C','iSubOpcion':'G','tipoOrden':info.tipoOrden}" 
                                   :callback_buscar="consultar_orden"
                                   :callback_cancelar="limpiarOrden"
                                   :disabled_texto="info.desactivarCamposOrden"
                                    api_campo_respuesta="orden" :api_preload="false"
                                   :danger="errors.length > 0" 
                                   :danger-text="(errors.length > 0) ? errors[0] : null"/>                                
                    </ValidationProvider> 
                </div>
                <div class="w-full md:w-full lg:w-6/12 xl:w-8/12 m-2"></div>

                <div class="w-full md:w-full lg:w-4/12 xl:w-2/12 m-2" tabindex="3">     
                    <ValidationProvider rules="required" v-slot="{ errors }">
                        <SM-Buscar  class="w-full" v-model="info.ajeno" label="Médico(Ajeno)" api="app/v1_JefeCaja/Busqueda_Ajenos" :api_campos="['Codigo','Nombre','Apellido','Tipo','Especialidad']" :api_titulos="['Codigo','Nombre','Apellido','Tipo','Especialidad']" api_campo_respuesta="Codigo" :api_preload="true"  :callback_buscar="cargar_ajeno" 
                                    :disabled_busqueda="info.desactivarBotonModificacion" :disabled_texto="info.desactivarBotonModificacion"                                    
                                   />  
                        <span style="position:relative;top:-2px" class="text-danger text-sm" v-show="errors.length>0" v-html="errors.join('<br>')"></span>                   
                    </ValidationProvider>                                    
                </div>
                <div class="w-full md:w-full lg:w-6/12 xl:w-4/12 m-2 lg:pr-6">
                    <vs-input label="Nombre:" class="w-full" :value="info.nombreAjeno" disabled/>
                </div>
                <div class="w-full md:w-full lg:w-10/12 xl:w-6/12 m-2" tabindex="4">
                    <ValidationProvider rules="max:40|required" v-slot="{ errors }">
                        <vs-input size="large" label="*Motivo" class="w-full"  v-model="info.razon" 
                                  :danger="errors.length > 0" 
                                  :danger-text="(errors.length > 0) ? errors[0] : null"/>
                    </ValidationProvider>
                </div>                  
                <div class="w-full m-2" tabindex="5">
                    <vs-button color="success" class="md:w-full lg:w-4/12 xl:w-1/12 m-2"
                        :disabled="info.desactivarBotonModificacion"
                        @click="handleSubmit(modificar_ajeno(invalid))">
                        Modificar Médico
                    </vs-button>

                    <vs-button class="md:w-full lg:w-4/12 xl:w-1/12 m-2" color="warning" @click="confirmar_limpia_campos()">
                        Limpiar Campos
                    </vs-button>
                </div>
            </div>
        </form>
    </ValidationObserver>        
</vx-card>
</template>

<script>

export default {
    data(){
        return {
            info:{
                tipoOrden: null,
                descripcionOrden: null,
                orden: null,
                admision: null,
                paciente: null,
                razon: null,
                ajeno: null,
                nombreAjeno: null,
                desactivarBotonModificacion: true,
                desactivarCamposOrden: false,
                filtroTipoOrden: [],
                filtroTipoOrdenGeneral: [] 
            },
        };
    },computed:{

    },methods:{
        consultar_orden(datos){  
            this.info.tipoOrden = datos.Tipo 
            this.info.orden = datos.orden                  
            this.cargar_paciente()
        },
        cargar_paciente(){            
            if (this.cargar().validar_campos()) {                                 
                this.cargar().datos_orden()
            }else{                               
                this.info.admision = null
                this.info.paciente = null  
                this.info.desactivarBotonModificacion = true
            }
        },
        cargar_ajeno(data){
            if(data){                
                this.info.nombreAjeno = (data.Nombre?data.Nombre.trim():'') +' '+(data.Apellido?data.Apellido.trim():'') 
            }else{                
                this.info.nombreAjeno  = 'SIN REFERENCIA'
            }
           
        },
        cargar(){
            return {
                datos_tipo_orden: (datos)=>{
                    this.info.tipoOrden = datos.Codigo
                    this.info.descripcionOrden = datos.Nombre
                    this.cargar_paciente()
                },
                datos_orden: () => {
                    this.axios.post('/app/v1_JefeCaja/obtener_orden',{
                                            iOpcion: 'C',
                                            iSubOpcion: 'M',                                            
                                            tipoOrden: this.info.tipoOrden,
                                            orden: this.info.orden,
                                            status: 'P,I'
                                        })
                              .then((resp) => {
                                                    let error 
                                                    let mensajeError                                                    
                                                    if(resp.data.json && resp.data.json.length != 0){
                                                        error = resp.data.json[0].tipo_error
                                                        mensajeError = resp.data.json[0].descripcion
                                                    }else{
                                                        error = resp.data.tipo_error
                                                        mensajeError = resp.data.descripcion
                                                    }

                                                    if(error && error != 0){
                                                        this.$vs.notify({
                                                            time: 4000,
                                                            color: '#B71C1C',
                                                            title: 'Alerta',
                                                            text: mensajeError
                                                        })

                                                        this.info.admision = null
                                                        this.info.paciente = null  
                                                        this.info.desactivarBotonModificacion = true  
                                                    }else if(resp.data.json.length==0){
                                                        this.$vs.notify({
                                                            time: 4000,
                                                            color: '#B71C1C',
                                                            title: 'Alerta',
                                                            text: 'No existe la orden'
                                                        })
                                                        this.info.admision = null
                                                        this.info.paciente = null  
                                                        this.info.desactivarBotonModificacion = true   
                                                    }
                                                    else{ //TipoOrden,CodigoOrden   
                                                        this.info.admision = resp.data.json[0].SerieAdmision + '-' + resp.data.json[0].Admision
                                                        this.info.paciente = resp.data.json[0].NombrePaciente   
                                                        this.info.ajeno = resp.data.json[0].Medico
                                                        this.info.nombreAjeno = resp.data.json[0].NombreMedico
                                                        this.info.desactivarBotonModificacion = false   
                                                        this.info.desactivarCamposOrden = true                     
                                                    }     
                                                }
                                    )
                },
                validar_campos:()=>{
                    return this.info.tipoOrden && this.info.descripcionOrden && this.info.orden                        
                }
            }
        },
        modificar_ajeno(invalid){
            if(invalid){
                this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'Falta ingresar campos obligatorios',
                        })
                return
            }  
            
            this.axios.post('/app/v1_JefeCaja/modificar_ajeno_orden',{
                                            iOpcion: 'M',
                                            iSubOpcion: 'M',                                            
                                            tipoOrden: this.info.tipoOrden,
                                            orden: this.info.orden,
                                            status: 'P,I',
                                            ajeno: this.info.ajeno,
                                            razon: this.info.razon
                                        })
                              .then((resp) => {
                                                    let error 
                                                    let mensajeError                                                    
                                                    if(resp.data.json && resp.data.json.length != 0){
                                                        error = resp.data.json[0].tipo_error
                                                        mensajeError = resp.data.json[0].descripcion
                                                    }else{
                                                        error = resp.data.tipo_error
                                                        mensajeError = resp.data.descripcion
                                                    }

                                                    if(error && error != 0){
                                                        this.$vs.notify({
                                                            time: 4000,
                                                            color: '#B71C1C',
                                                            title: 'Alerta',
                                                            text: mensajeError
                                                        })
                                                        
                                                        this.info.desactivarBotonModificacion = true                                                          
                                                    }
                                                    else{ //TipoOrden,CodigoOrden     
                                                       
                                                        this.$vs.notify({   
                                                            time: 5000,                                         
                                                            title:'Exito',
                                                            text: 'Se actualizo el médico de la orden',
                                                            color:'success'
                                                        })
                                                        
                                                        this.limpiar_campos()
                                                        this.info.desactivarBotonModificacion = false   
                                                        this.info.desactivarCamposOrden = false                     
                                                    }     
                                                }
                                    )
            
        },
        limpiar_datos_ajeno(){
            this.info.ajeno = null
            this.info.nombreAjeno  = 'SIN REFERENCIA'
        },
        limpiarOrden(){
            this.limpiar_campos()
            this.info.desactivarBotonModificacion = true
            this.info.desactivarCamposOrden = false
        }
        ,
        limpiar_campos(){            
            this.info.tipoOrden = null
            this.info.descripcionOrden = null
            this.info.orden = null
            this.info.admision = null
            this.info.paciente = null
            this.info.ajeno = null
            this.info.nombreAjeno = null
            this.info.razon = null            
        },
        limpiar_campos_exito(){            
            this.info.orden = null
            this.info.admision = null
            this.info.paciente = null
            this.info.razon = null            
        },
        confirmar_limpia_campos(){
            this.$vs.dialog({
                            type: 'confirm',
                            color: 'danger',
                            title: 'Confirmación',
                            acceptText: 'Limpiar',
                            cancelText: 'Cancelar',
                            text: `¿Desea limpiar los campos?`,
                            accept: () => {
                                this.limpiar_campos()
                                this.info.desactivarBotonModificacion = true
                            }
                        })
        }
    },mounted(){    
    },watch:{
        'info.tipoOrden'(value){
            if(!value || value == ''){
                this.info.descripcionOrden = null
            }
        }
    }
}

</script>