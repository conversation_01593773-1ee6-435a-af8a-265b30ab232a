<template>
    <div>



        <ValidationObserver ref="billData" v-slot="{ invalid, handleSubmit }" mode="lazy">
            <form @submit.prevent="handleSubmit(submitForm(submit))">

                <div class="tab-text">
                    <div class="container_div ">

                        <div class="container pl-4 pt-2 mx-auto">


                            <vs-row>
                                <vs-col vs-type="flex" vs-w="10">
                                    <div class="w-full  p-4 ml-2">
                                        <h6>Datos de facturación</h6>
                                        <hr
                                            style="height: 8px; border: 0; box-shadow: inset 0 12px 12px -12px rgba(0, 0, 0, 0.5)">
                                    </div>
                                </vs-col>
                            </vs-row>

                            <vs-row>
                                <vs-col vs-type="flex" vs-w="6">
                                    <div class="w-2/3  ">
                                        <validationProvider name="Nombre en Factura" class="required" rules="required|min:3"
                                            v-slot="{ errors }">
                                            <vs-input label="Nombre en Factura" class="w-full "
                                                v-model="PacienteData.NombreFactura" :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null" :maxlength="60" />
                                        </validationProvider>
                                    </div>
                                </vs-col>

                                <vs-col vs-type="flex" vs-w="6">
                                    <div class="w-2/3  ">
                                        <validationProvider name="Dirección en factura" class="required"
                                            rules="required|min:3" v-slot="{ errors }">
                                            <vs-input label="Dirección en factura" class="w-full "
                                                v-model="PacienteData.DireccionFactura" 
                                                :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null" :maxlength="20" />
                                        </validationProvider>
                                    </div>
                                </vs-col>
                            </vs-row>

                            <vs-row>
                                <vs-col vs-type="flex" vs-w="6">
                                    <div class="w-2/3  ">
                                        <validationProvider name="Dirección en factura" class="required"
                                            rules="required|min:3" v-slot="{ errors }">
                                            <vs-input label="NIT" class="w-full "
                                             v-model="PacienteData.Nit" 
                                            :danger="errors.length > 0"
                                            :danger-text="(errors.length > 0) ? errors[0] : null" :maxlength="15"/>
                                        </validationProvider>
                                    </div>
                                </vs-col>
                            </vs-row>

                        </div>
                    </div>

                    <div v-show="showButtons">
                        <vs-button style="margin-right:1px" v-on:click="handleSubmit(submit())" :disabled="invalid" 
                        :clientWidth="20">
                            Guardar
                        </vs-button>
                    </div>

                </div>
            </form>
        </ValidationObserver>
        <!-- </vx-card> -->
    </div>
</template>


<script>

//import DatosPaciente from "/src/components/sermesa/modules/paciente/DatosPaciente.vue"

import { mapFields } from "vuex-map-fields";
export default {


    name: 'DatosFacturacion',
    data: () => ({
        showButtons: false
    }),
    components: {
        //DatosPaciente
    },
    mounted(){
        
        this.PacienteData.NombreFactura = this.PacienteData.PrimerNombre + ' ' + this.PacienteData.SegundoNombre + ' ' +
                                          this.PacienteData.PrimerApellido + ' ' + this.PacienteData.SegundoApellido
            this.PacienteData.DireccionFactura = "CIUDAD"

    },
    activated(){
    },
    computed: {
        ...mapFields('paciente', ["PacienteData"])
    },
    methods: {
        getionarDatos() {
            return new Promise((resolve) => {
                // Lógica de guardado del cliente aquí
                // Resuelve la promesa si el guardado es exitoso
                return resolve(true);
                // Rechaza la promesa si hay un error en el guardado
                // reject('Mensaje de error aquí');
            });
        },
        validacionFormulario() {
            return new Promise((resolve, reject) => {
                this.$refs.billData.validate()
                    .then((isValid) => {
                        if (isValid) {
                            
                            resolve(true);
                        } else {
                            
                            reject('Revisar campos requeridos');
                        }
                    })
                    .catch((error) => {
                        
                        reject(error);
                    });
            });
        },
        gestionarPaciente() {
            return new Promise((resolve, reject) => {
                this.$store.dispatch('paciente/crearPaciente')
                    .then((response) => {

                        resolve(response);
                    })
                    .catch((error) => {
                        reject(error);
                    });
            });
        },
        async submit() {

            

            this.getionarDatos()
                .then(() => {
                    return this.validacionFormulario();
                })
                .then(() => {                                        
                    return this.gestionarPaciente();
                })
                .then(() => {
                    
                    this.$emit("ready", true);
                })
                .catch((error) => {
                    this.$vs.dialog({
                        type: 'confirm',
                        color: '#ED8C72',
                        acceptText: 'Continuar',
                        title: 'Error en gestión de Paciente',
                        text: error,
                        clientWidth: 100,
                        accept: () => {
                            
                            this.$emit("ready", false);
                            
                        }
                    });
                })
        }
    }

}

</script>



<style scoped>
* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}


.container_div {
    display: grid;
    grid-gap: 10px;
    width: 100%;
    height: 100%;
    overflow: hidden;
}



.container_div>div {
    border: 1px solid #888;
    padding-top: 10px;
    padding-bottom: 10px;
}



.container_b {
    display: grid;
    grid-gap: 10px;
    width: 100%;
    height: 10%;
}

.container {
    max-width: 100%;
}

.radio-group.radio-group--inline div[data-radio-button] {
    width: auto;
}

.radio-group.radio-group--inline div[data-radio-button]:not(:first-of-type) {
    padding-left: var(--space-base);
}

.field {
    margin-bottom: 10px;
}

.form {
    margin-top: 20px;
}

span {
    display: block;
    margin-top: 3px;
}

.vs-input {
    padding-top: 4px;
}
</style>
  