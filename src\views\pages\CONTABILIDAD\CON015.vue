<template>
    <div>
        <buscador ref="BuscarFacturasNota" buscador_titulo="Buscador / Factura" :api="'app/v1_contabilidad_hospital/GetFacturas'" 
            :campos="['SerieFactura', 'NumeroFactura', 'Fecha', 'Periodo', 'Status']"
            :titulos="['Serie Factura', 'Numero Factura', '#Fecha', '#Periodo', '#Status']"
            :multiselect="false" :api_validar_campo="true" :api_reload="true" />
    

        <!-- ingreso nota parcial -->
        <vs-popup title="Categorías de la Factura" :active.sync="IsFacturaParcial" class="seleccionEmpresa">
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="1">
                    <label class="typo__label">Factura:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="5">
                    <h3 class="labelPadding">{{ FacturaParcial.FacturaFel }}</h3>
                </vs-col>
            </vs-row>
            <br>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <label class="typo__label">Total Cargado:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <label class="typo__label">Descuento:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <label class="typo__label">Total Facturado:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <label class="typo__label">Notas Anteriores:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <label class="typo__label">Saldo Factura:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <label class="typo__label">Disponible:&nbsp;</label>
                </vs-col>
            </vs-row>
            <vs-row vs-align="center" vs-justify="center">
                <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="2">
                    <!-- <vs-input v-model="FacturaParcial.Total" disabled></vs-input> -->
                    <h5>{{ $formato_moneda(FacturaParcial.Total) }}</h5>
                </vs-col>
                <vs-col style="border-color: blue; border: 1px;" vs-type="flex" vs-justify="center" vs-align="center" vs-w="2">
                    <!-- <vs-input v-model="FacturaParcial.Descuento" disabled></vs-input> -->
                    <h5>{{ $formato_moneda(FacturaParcial.Descuento) }}</h5>
                </vs-col>
                <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="2">
                    <!-- <vs-input v-model="FacturaParcial.Facturado" disabled></vs-input> -->
                    <h5>{{ $formato_moneda(FacturaParcial.Facturado) }}</h5>
                </vs-col>
                <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="2">
                    <!-- <vs-input v-model="FacturaParcial.NotasAnt" disabled></vs-input> -->
                    <h5>{{ $formato_moneda(FacturaParcial.NotasAnt) }}</h5>
                </vs-col>
                <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="2">
                    <!-- <vs-input v-model="FacturaParcial.SaldoFactura" disabled></vs-input> -->
                    <h5>{{ $formato_moneda(FacturaParcial.SaldoFactura) }}</h5>
                </vs-col>
                <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="2">
                    <!-- <vs-input v-model="FacturaParcial.FacturaMenosNotas" disabled></vs-input> -->
                    <h5>{{ $formato_moneda(FacturaParcial.FacturaMenosNotas) }}</h5>
                </vs-col>
            </vs-row>
            
            <vs-row vs-align="center" vs-justify="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="12">
                    <div class="w-full">
                        <vs-table2 max-items="10" tooltip pagination :data="ListCuentasAuxiliarParcial">
                            <template slot="thead">
                                <th>Categoría</th>
                                <th>Valor Nota Crédito</th>
                                <th>Valor Factura</th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2 :data="data[indextr].Nombre">
                                        {{ data[indextr].Nombre }}
                                    </vs-td2>
                                    <vs-td2 noTooltip>
                                        <vs-input ref="ValorParcial" type="number" v-model.number="tr.Debe" class="w-full" @change="OnChangeValorNota(tr)" />
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].ValorInicial">
                                        {{ $formato_moneda(data[indextr].ValorInicial) }}
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                </vs-col>
            </vs-row>
            <vs-row vs-align="center" vs-justify="flex-end">
                <vs-col vs-type="flex" vs-align="center" vs-w="3">
                    <h4>{{ $formato_moneda(TotalValorNota) }}</h4>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <h4>{{ $formato_moneda(TotalValorFactura) }}</h4>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="1"></vs-col>
            </vs-row>
            <br>

            <vs-row vs-align="center" vs-justify="flex-end">
                <vs-col vs-type="flex" vs-align="center" vs-w="3">
                    <vs-button color="primary" icon-pack="fas" icon="fa-save" 
                        @click="AceptarValoresParciales()" 
                        :disabled="TotalValorNota ==0 || !TotalValorNota"
                        class="mr-1" style="display:inline-block">Guardar</vs-button>
                </vs-col>
            </vs-row>
        </vs-popup>

        <vx-card title="Notas de Crédito">
            <vs-row class="w-full mb-4" vs-justify="center">
                <ConfiguracionCaja
                    @CargarCaja="CargarCaja" 
                    :FacturaCambiaria=1
                    TipoDocumento = "N"  
                    TipoCajaSeleccionada = "TipoCajaNotaCredito"
                    CajaSeleccionada = "CajaNotaCredito"
                    AgrupacionCaja="NotaCreditoContaHospital"
                    class="w-full">
                </ConfiguracionCaja>
            </vs-row>

            <vs-prompt title="Anulación de la Nota de Crédito"
            color="danger"
            @cancel="MotivoAnulacion=''"
            @accept="AnularNotaCredito"
            :is-valid="ValidObservacion"
            acceptText="Aceptar"
            cancelText="Cancelar"
            :active.sync="activePrompt">
                <div class="con-exemple-prompt">
                    <label class="typo__label">Razón:&nbsp;</label>
                    <vs-input class="vs-input-motivo" v-model="MotivoAnulacion"/>
                    <vs-alert :active="!ValidObservacion" color="danger" icon="new_releases" >
                    Debe de Ingresar un motivo valido
                    </vs-alert>
                </div>
            </vs-prompt>

            <vs-divider></vs-divider>
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="12">
                    <vs-button color="success" icon-pack="fas" icon="fa-plus" 
                        :disabled="!SerieNotaAsignada"
                        @click="NuevaNota=1;IsNotaCredito=true;Limpiar()" 
                        style="display:inline-block">Nota de Crédito</vs-button>
                    <div data-v-5e12eaeb="" class="vs-spacer"></div>
                
                    <label class="typo__label">Serie Nota:&nbsp;</label>
                    <vs-input v-model="SerieNotaAsignada" disabled></vs-input>

                    <label style="margin-left: 1%;" class="typo__label">Número de Nota:&nbsp;</label>
                    <vs-input v-model="NumeroNota"></vs-input>

                    <vs-button color="primary" icon-pack="fas" icon="fa-search" 
                        @click="RecuperaNotasdeCredito()" 
                        style="display:inline-block; margin-left: .5%;">Buscar</vs-button>
                    <vs-button color="primary" icon-pack="feather" icon="icon-refresh-cw"  
                        @click="NumeroNota = ''; RecuperaNotasdeCredito()" 
                        style="margin-left: .5%;"></vs-button>
                </vs-col>
            </vs-row>
            
            <!-- listado principal de notas recientes -->
            <vs-row vs-align="center" vs-justify="center">
                <vs-col vs-type="flex" >
                    <div class="w-full">
                        <vs-table2 max-items="10" tooltip pagination search :data="ListNotasCredito">
                            <template slot="thead">
                                <th width="100px">Serie Nota</th>
                                <th width="100px">Numero Nota</th>
                                <th width="100px">Factura</th>
                                <th width="100px">Total Nota</th>
                                <th width="100px">Estado</th>
                                <th width="100px">Ver Nota</th>
                                <th width="100px">Bitácora</th>
                                <th width="100px">Imprime Nota</th>
                                <th width="100px">Anular Nota</th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2 :data="data[indextr].SerieNota">
                                        {{ data[indextr].SerieNota }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Codigo">
                                        {{ data[indextr].Codigo }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Face">
                                        {{ data[indextr].Face }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Total">
                                        {{ $formato_moneda(data[indextr].Total) }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Estado">
                                        {{ data[indextr].Estado == 'A' || data[indextr].Estado == 'X' ? 'ANULADA' : 'VIGENTE' }}
                                    </vs-td2>
                                    <vs-td2 noTooltip align="center">
                                        <vs-button color="primary" icon-pack="feather" icon="icon-eye" @click="NuevaNota=0;IsNotaCredito=true;VerNota(data[indextr])" class="mr-1" style="display:inline-block">
                                        </vs-button>
                                    </vs-td2>
                                    <vs-td2 noTooltip align="center">
                                        <vs-button color="primary" icon-pack="feather" icon="icon-file-text" @click="BitacoraImpresion(data[indextr])" class="mr-1" style="display:inline-block">
                                        </vs-button>
                                    </vs-td2>
                                    <vs-td2 noTooltip align="center">
                                        <vs-button color="primary" icon-pack="feather" icon="icon-printer" @click="NotaCreditoImpresion(data[indextr])" class="mr-1" style="display:inline-block">
                                        </vs-button>
                                    </vs-td2>
                                    <vs-td2 noTooltip align="center">
                                        <vs-button color="danger" icon-pack="fas" icon="fa-rectangle-xmark" @click="activePrompt=true;SelectAnulacion=data[indextr]" class="mr-1" style="display:inline-block" :disabled="data[indextr].Estado == 'A' || data[indextr].Estado == 'X'">
                                        </vs-button>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                </vs-col>
            </vs-row>
        </vx-card>

        <!-- formulario ingreso de nota  -->
        <vs-popup classContent="popup-example" title="Notas de Crédito" :active.sync="IsNotaCredito">
            <!-- informacion de la nota -->
        
            <vs-row>
                <vs-col vs-type="flex" vs-justify="flex-end" vs-align="center" vs-w="2">
                    <label class="labelPadding typo__label">Nota de Crédito:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2.5">
                    <vs-input label="No. de documento" v-model="SelecNotaCredito.NotaFel" disabled></vs-input>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-input label="No. interno" v-model="SelecNotaCredito.NotaInterno" disabled></vs-input>
                </vs-col>

                <vs-col v-if="NuevaNota==1" vs-type="flex" vs-align="center" vs-w="2">
                    <vs-input label="fecha documento" type="date" name="date1" v-model="SelecNotaCredito.Fecha" disabled></vs-input>
                </vs-col>
                <vs-col v-else vs-type="flex" vs-align="center" vs-w="2">
                    <vs-input label="fecha documento" name="date1" v-model="SelecNotaCredito.Fecha" :disabled="NuevaNota==0"></vs-input>
                </vs-col>
                <div data-v-5e12eaeb="" class="vs-spacer"></div>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <label class="labelPadding typo__label">Total:&nbsp;</label>
                    <h4>{{ $formato_moneda(SelecNotaCredito.TotalNota) }}</h4>
                </vs-col>
            </vs-row>
            <br>

            <!-- informacion de la factura -->
            <vs-row vs-align="center">
                <vs-col vs-justify="flex-end" vs-type="flex" vs-align="center" vs-w="2">
                    <label class="typo__label">Factura:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="3">
                    <vs-input v-model="SelecNotaCredito.FacturaFel" disabled></vs-input>
                    <vs-button color="primary" icon-pack="fa" icon="fa-search" @click="Limpiar();GetFacturas()" :disabled="NuevaNota==0"></vs-button>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="3">
                    <h5>{{ `${ SelecNotaCredito.SerieFactura } - ${ SelecNotaCredito.Factura }` }}</h5>
                </vs-col>
                <div data-v-5e12eaeb="" class="vs-spacer"></div>

                <vs-col v-if="NuevaNota==1"
                    vs-type="flex" vs-align="center" vs-w="2" style="margin-right: 6%;">
                    <label class="labelPadding typo__label">Disponible:&nbsp;</label>
                    <h4>{{ $formato_moneda(SelecNotaCredito.Disponible) }}</h4>
                </vs-col>

            </vs-row>

            <!-- nit y nombre de factura -->
            <vs-row vs-align="center">
                <vs-col vs-justify="flex-end" vs-type="flex" vs-align="center" vs-w="2">
                    <label class="typo__label">Nit:&nbsp;</label>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2.5">
                    <vs-input v-model="SelecNotaCredito.Nit" disabled></vs-input>
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="7.5">
                    <vs-input class="w-full" v-model="SelecNotaCredito.Nombre" disabled></vs-input>
                </vs-col>
            </vs-row>

            <!-- concepto/descripcion de la nota -->
            <vs-row vs-align="center">
                <label class="typo__label">Concepto&nbsp;</label>
                <vs-col vs-type="flex">
                    <vs-textarea counter="180" v-model="SelecNotaCredito.Concepto" 
                    :disabled="NuevaNota==0" @change="OnChangeConcepto"></vs-textarea>
                </vs-col>
            </vs-row>


            <!-- tipo de nota: total/parcial -->
            <vs-col vs-type="flex" vs-align="center" vs-w="2">
                <label class="typo__label">Tipo de Nota&nbsp;</label>
            </vs-col>
            <vs-col style="margin-top: -1%;" vs-w ="6.5">
                <div class="flex flex-wrap smtabla" style="height: 50px !important">
                    <vs-radio class="pl-1" vs-name="radios1" vs-value="0"
                        v-model="SelecNotaCredito.RadioValorFactura" 
                        :disabled="IsNotaNueva" @change="TipoNotaChange()">Valor total de la factura</vs-radio>

                    <vs-radio class="pl-12" vs-name="radios1" vs-value="1"
                        v-model="SelecNotaCredito.RadioValorFactura" 
                        :disabled="IsNotaNueva" @change="TipoNotaChange()">Valor parcial de la factura</vs-radio>
                </div>
            </vs-col>

            <!-- botones especiales -->
            <vs-col style="margin-left: 10px;" vs-type="flex" vs-align="center" vs-w="3">
            </vs-col>
            
            <br>

            <vs-row vs-justify="flex-end" vs-align="center">
                <vs-col vs-justify="flex-end" vs-type="flex" vs-align="center" vs-w="1">
                    <label class="typo__label">Servicios:&nbsp;</label>
                </vs-col>
                <!-- <vs-col v-if="NuevaNota==1" vs-type="flex" vs-align="center" vs-w="4">
                    <vs-input type="number" v-model="SelecNotaCredito.Servicios" disabled></vs-input>
                </vs-col> -->
                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                    <h4>{{ $formato_moneda(SelecNotaCredito.Servicios) }}</h4>
                </vs-col>
                <vs-col vs-justify="flex-end" vs-type="flex" vs-align="center" vs-w="1">
                    <label class="typo__label">Ventas:&nbsp;</label>
                </vs-col>
                <!-- <vs-col v-if="NuevaNota==1" vs-type="flex" vs-align="center" vs-w="4">
                    <vs-input type="number" v-model="SelecNotaCredito.Ventas"></vs-input>
                </vs-col> -->
                <vs-col vs-type="flex" vs-align="center" vs-w="4">
                    <h4>{{ $formato_moneda(SelecNotaCredito.Ventas) }}</h4>
                </vs-col>
            </vs-row>
            
            <vs-row vs-align="center">
                <vs-col vs-type="flex" vs-align="center" vs-w="12">
                    <div class="w-full">
                        <vs-table2 max-items="12" tooltip pagination :data="ListAuxiliarClientes">
                            <template slot="thead">
                                <th width="50px">Documento</th>
                                <th width="130px">Cuenta</th>
                                <th>Nombre</th>
                                <th width="020px">Tipo</th>
                                <th width="025px">Cat.</th>
                                <th width="150px">Debe</th>
                                <th width="150px">Haber</th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2 :data="data[indextr].Documento">
                                        {{ data[indextr].Documento }}
                                    </vs-td2>
                                    <vs-td2 noTooltip v-if="NuevaNota==1" :data="data[indextr].Cuenta">
                                        <vs-input v-model="data[indextr].Cuenta" class="w-full" 
                                        @change="GetCuentaConta(tr)"></vs-input>
                                    </vs-td2>
                                    <vs-td2 v-else :data="data[indextr].Cuenta">
                                        {{ data[indextr].Cuenta }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Nombre">
                                        {{ data[indextr].Nombre }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].TipoVenta">
                                        {{ data[indextr].TipoVenta }}
                                    </vs-td2>
                                    <vs-td2 :data="data[indextr].Categoria">
                                        {{ data[indextr].Categoria }}
                                    </vs-td2>

                                    <vs-td2 noTooltip v-if="NuevaNota==1" :data="data[indextr].Debe">
                                        <vs-input v-model="data[indextr].Debe" class="w-full" 
                                        @change="SumaDH(data)"></vs-input>
                                    </vs-td2>
                                    <vs-td2 v-else style="text-align:right" :data="data[indextr].Debe">
                                        {{ $formato_moneda(data[indextr].Debe) }}
                                    </vs-td2>

                                    <vs-td2 noTooltip v-if="NuevaNota==1" :data="data[indextr].Haber">
                                        <vs-input v-model="data[indextr].Haber" class="w-full"
                                        @change="SumaDH(data)"></vs-input>
                                    </vs-td2>
                                    <vs-td2 v-else style="text-align:right" :data="data[indextr].Haber">
                                        {{ $formato_moneda(data[indextr].Haber) }}
                                    </vs-td2>

                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                </vs-col>
            </vs-row>

            <!-- Totales del detalle NC -->
            <vs-col vs-type="flex">
                <vs-button color="success" icon-pack="fas" icon="fa-save" 
                    @click="GetBitacora()" class="mr-1" 
                    style="display:inline-block; width: 150px; height: 50;" 
                    :disabled="IsNotaNueva">Grabar</vs-button>
            
            <div class="vs-spacer"></div>
                <vs-col vs-type="flex" vs-w="2">
                    <h4 style="text-align:right; margin-left: -40%">{{ $formato_moneda(TotalDebe) }}</h4>
                    <div data-v-5e12eaeb="" class="vs-spacer"></div>
                    <h4 style="text-align:right; margin-right: 10%;">{{ $formato_moneda(TotalHaber) }}</h4>
                </vs-col>
            </vs-col>
    </vs-popup>
    </div>
</template>
<script>

import ConfiguracionCaja from '/src/components/sermesa/modules/caja/ConfiguracionCaja.vue'

    export default {
        components: {
            ConfiguracionCaja
        },
        data() {
            return{
                SerieNotaAsignada:'',
                SerieNota: '',
                NumeroNota: '',
                IsNotaCredito: false,
                NuevaNota: '0',
                ListNotasCredito: [],
                IsNotaNueva: true,
                SelecNotaCredito: {
                    Fecha: '',
                    Hospital: '',
                    NotaFel: '',
                    NotaInterno: '',
                    Status: '',
                    TotalNota: '',
                    FacturaFel: '',
                    SerieFactura:'',
                    Factura: '',
                    Nit: '',
                    CuiPasaporte: '',
                    Nombre: '',
                    Concepto: '',
                    RadioValorFactura: '',
                    Servicios: '',
                    Ventas: '',
                    Disponible:0,
                    TipoReceptor: '',
                    TotalNotasEmitidas:0
                },

                totalMontoDebe:0,


                ListAuxiliarClientes: [],
                TotalDebe: '',
                TotalHaber: '',
                MotivoAnulacion: '',
                activePrompt: false,
                SelectAnulacion:{},
                SelectFactura: {},
                IsFacturaParcial:false,
                FacturaParcial: {
                    FacturaFel: '',
                    Total: '',
                    Descuento: '',
                    Facturado: '',
                    NotasAnt: '',
                    SaldoFactura: '',
                    FacturaMenosNotas: ''
                },
                ListCuentasAuxiliarParcial: [],
                tempCuentasAuxiliarParcial: [],

                AjusteItem:0,
                DiferenciaAux:'',
                TotalValorNota: '',
                TotalValorFactura: '',
                CantidadModificados: '',
                configuracion:{
                    popUpConfiguracion: false,
                    activarFacturacion: false,

                    tiposCajas: [],
                    tipoCajaSeleccionada: null,
                    indexTipoCajaSeleccionada: null,

                    cajaSeleccionada: null,
                    activarRecibos: false  


                },
         
                sndMsg: {
                errorMsg: 'danger',
                succesMsg: 'success',
                infoMsg: 'warning',
                delayTime: 4000
                },
                caja: {
                    SerieNota:[],
                    SerieNotaFiltrada:[],
                                        
                    tiposFacturacion: [],
                    tipoFacturacionSeleccionada:{}
                }
            }
        },
        computed:{
            ValidObservacion(){
                return (this.MotivoAnulacion.length > 10 && this.MotivoAnulacion.length <= 40)
            }
        },

        methods: {
            SumaDH(listData){
                this.TotalDebe = 0;
                this.TotalHaber = 0;

                listData.forEach(items =>{

                    this.TotalDebe += parseFloat(items.Debe);
                    this.TotalHaber += parseFloat(items.Haber);
                });
            },


            isEmptyObject(obj){
                return Object.keys(obj).length === 0;
            },
            RecuperaNotasdeCredito(){
                if(!this.configuracion.cajaSeleccionada && !this.configuracion.cajaSeleccionada.SerieFac){
                    return;
                }

                this.axios.post('/app/v1_contabilidad_hospital/RecuperaNotasdeCredito', {
                    SerieNota: this.configuracion.cajaSeleccionada.SerieFac,
                    NumeroNota: this.NumeroNota
                })
                .then(resp => {

                    if(resp.data.codigo == 0){
                        this.ListNotasCredito = resp.data.json
                    }
                })
            },
            VerNota(data){

                this.axios.post('/app/v1_contabilidad_hospital/GetNotaCreditoIngresada', {
                    SerieNota: data.SerieNotaCorta,
                    NumeroNota: data.CodigoNotaCorta
                })
                .then(resp => {
                    if(resp.data.codigo == '0'){
                        this.SelecNotaCredito = resp.data.json[0]
                        this.SelecNotaCredito.Status = data.Estado == 'A' || data.Estado == 'X' ? '***ANULADA***' : ''
                        this.SelecNotaCredito.NotaFel = `${ data.SerieNota } - ${ data.Codigo }`
                        this.SelecNotaCredito.TotalNota = data.Total
                        this.SelecNotaCredito.Disponible = 0
                        this.SelecNotaCredito.TotalNotasEmitidas = 0
                        this.SelecNotaCredito.NotaInterno = `${ data.SerieNotaCorta } - ${ data.CodigoNotaCorta }`
                        this.SelecNotaCredito.FacturaFel = data.Face

                        this.GetAuxiliarCliente(data)
                    }
                   
                })
            },
            GetAuxiliarCliente(data){
                this.axios.post('/app/v1_contabilidad_hospital/GetAuxiliarClientes', {
                    SerieNotaCorta: data.SerieNotaCorta,
                    NumeroNotaCorta: data.CodigoNotaCorta
                })
                .then(resp => {
                    let SumDebe = 0
                    let SumHaber = 0
                    if(resp.data.codigo == 0){
                        this.ListAuxiliarClientes = resp.data.json
                        this.TotalDebe = this.ListAuxiliarClientes.forEach(function(data){
                            SumDebe += parseFloat(data.Debe)
                            SumHaber += parseFloat(data.Haber)
                        })
                        this.TotalDebe = SumDebe.toFixed(2)
                        this.TotalHaber = SumHaber.toFixed(2)
                    }
                })
            },
            GetCuentaConta(data){
                this.axios.post('/app/v1_contabilidad_hospital/GetCuentaConta', {
                    NoCuentaConta: data.Cuenta
                })
                .then(resp => {
                    if(resp.data.codigo == 0){
                        data.Nombre = resp.data.json[0].Nombre
                    }
                })
            },

            makeNotaCredito(rebajaSaldo){
                this.Consulta().MostrarMensaje('Registro en el sistema', this.sndMsg.succesMsg);
                this.axios.post('/app/v1_contabilidad_hospital/IngresoNotaCredito', {
                    SerieFacturaCorta: this.SelecNotaCredito.SerieFactura,
                    NumeroFacturaCorta: this.SelecNotaCredito.Factura,
                    SerieNotaCorta: this.SerieNotaAsignada,
                    // NumeroNotaCorta: this.NumeroNotaCorta,
                    TotalVenSer: this.TotalHaber,
                    ModificaSaldo: rebajaSaldo,
                    FechaRegistro: this.SelecNotaCredito.Fecha,
                    TotalVenta: this.SelecNotaCredito.Ventas,
                    TotalServicio: this.SelecNotaCredito.Servicios,
                    Concepto: this.SelecNotaCredito.Concepto,
                    NombreFactura: this.SelecNotaCredito.Nombre,
                    Status: 'V',
                    NitFactura: this.SelecNotaCredito.Nit,
                    CuiPasaporte: this.SelecNotaCredito.CuiPasaporte,
                    TipoReceptor: this.SelecNotaCredito.TipoReceptor,
                    RebajaSaldo: '0',
                    TipoCaja: this.configuracion.cajaSeleccionada.TipoCaja,
                    Datos: JSON.stringify(this.ListAuxiliarClientes)
                })
                .then(resp => {
                    this.IsNotaNueva = true;

                    if(resp.data.codigo == '0'){
                        this.NumeroNotaCorta = parseInt(resp.data.numeroNota);

                        this.Consulta().MostrarMensaje('Sincronizando nota: '+ this.SerieNotaAsignada +'-'+ this.NumeroNotaCorta,this.sndMsg.succesMsg);

                        this.axios.post('/app/v1_FacturaElectronica/GeneraFel',{
                                serieFactura: this.SerieNotaAsignada,
                                numeroFactura: this.NumeroNotaCorta,
                                tipoDocumento: 2
                            }).then(resp=>{
                                if(resp.data.codigo == 0 && resp.data.json[0].codigo == 0){
                                    // this.BitacoraImpresion(this.data);
                                    this.Consulta().MostrarMensaje(resp.data.json[0].descripcion, this.sndMsg.succesMsg);
                                    this.Limpiar();
                                    this.IsNotaCredito = false;
                                    this.RecuperaNotasdeCredito();

                                }else{
                                    this.Consulta().MostrarMensaje(resp.data.json[0].descripcion,this.sndMsg.errorMsg);
                                }
                            })
                    }
                })

            },

            GetBitacora(){
                if(this.SelecNotaCredito.Nombre == ''){
                    this.Consulta().MostrarMensaje('Factura ingresada no tiene nombre', this.sndMsg.errorMsg);
                    return
                }
                if(this.SelecNotaCredito.Servicios == '' && this.SelecNotaCredito.Ventas == ''){
                    this.Consulta().MostrarMensaje('Nota no tine valor definido', this.sndMsg.errorMsg);
                    return
                }
                if (parseFloat(this.TotalDebe) <= 0 || parseFloat(this.TotalDebe)> parseFloat(this.SelectFactura.TotalFactura)){
                    this.Consulta().MostrarMensaje('Monto de la nota de crédito es mayor que la factura', this.sndMsg.errorMsg);
                    return
                }

                if (this.ListAuxiliarClientes.filter(cuenta => cuenta.Cuenta === '').length >0){
                    this.Consulta().MostrarMensaje('Verifique rubro sin cuenta contable',this.sndMsg.errorMsg);
                    return;
                }

                if (this.ListAuxiliarClientes.filter(nombre => nombre.Nombre === '').length >0){
                    this.Consulta().MostrarMensaje('Verifique rubro sin cuenta contable',this.sndMsg.errorMsg);
                    return;
                }

                if (!this.TotalDebe || !this.TotalHaber){
                    this.Consulta().MostrarMensaje('Partida sin datos.  Verifique',this.sndMsg.errorMsg);
                    return;

                }

                if (this.TotalDebe != this.TotalHaber){
                    this.Consulta().MostrarMensaje('Partida no cuadra.  Verifique',this.sndMsg.errorMsg);
                    return;

                }

                this.Consulta().MostrarMensaje('Validaciones completadas', this.sndMsg.succesMsg);

                if (this.SelectFactura.Tipo == '2'){
                    this.$vs.dialog({
                        type: 'confirm',
                        color: 'success',
                        title: 'Confirmar',
                        acceptText: 'SI',
                        cancelText: 'NO',
                        text: 'Factura de Copago. Desea rebajar saldo?',
                        accept: ()=>{
                            this.makeNotaCredito('S');
                        },
                        cancel: ()=>{
                            this.makeNotaCredito('N');
                        }
                    })
                }else{
                    this.makeNotaCredito('N');
                }
            },
       

            BitacoraImpresion(data){
                if(this.SelecNotaCredito.NotaInterno != ''){
                    let SerieNotaCorta = this.SerieNotaAsignada;
                    let NumeroNotaCorta= data.CodigoNotaCorta;

                    this.$reporte_modal({
                        Nombre: "Bitacora Nota Credito Contabilidad Hospital",
                        Opciones: {
                            NotaCredito: NumeroNotaCorta,
                            SerieNotaCredito: SerieNotaCorta
                        },
                        Formato: "PDF"
                    })
                }
                else{
                    this.$reporte_modal({
                        Nombre: "Bitacora Nota Credito Contabilidad Hospital",
                        Opciones: {
                            NotaCredito: data.CodigoNotaCorta,
                            SerieNotaCredito: data.SerieNotaCorta
                        },
                        Formato: "PDF"
                    })
                }
            },
            NotaCreditoImpresion(data){
                if(this.SelecNotaCredito.NotaInterno != ''){
                    let SerieNotaCorta = this.SerieNotaAsignada;
                    let NumeroNotaCorta= data.CodigoNotaCorta;

                    this.$reporte_modal({
                        Nombre: "Nota de Credito Contabilidad Hospital",
                        Opciones: {
                            NotaCredito: NumeroNotaCorta,
                            SerieNotaCredito: SerieNotaCorta,
                            RebajaIva: data.RebajaIva
                        },
                        Formato: "PDF"
                    })
                }
                else{
                    this.$reporte_modal({
                        Nombre: "Nota de Credito Contabilidad Hospital",
                        Opciones: {
                            NotaCredito: data.CodigoNotaCorta,
                            SerieNotaCredito: data.SerieNotaCorta,
                            RebajaIva: data.RebajaIva
                        },
                        Formato: "PDF"
                    })
                }
            },
            GetFacturas(){
                
                this.$refs.BuscarFacturasNota.iniciar((data) => {
                    if(data != null){
                        if(data.Status == 'A'){
                            this.Consulta().MostrarMensaje('Factura está anulada',this.sndMsg.errorMsg);
                            return
                        }
                        
                        this.IsNotaNueva = true
                        this.SelecNotaCredito.Concepto = ''
                        this.SelecNotaCredito.RadioValorFactura = ''
                        this.SelecNotaCredito.FacturaFel = data.SerieFactura + ' ' + data.NumeroFactura
                        this.SelecNotaCredito.SerieFactura = data.SerieReferencia
                        this.SelecNotaCredito.Factura = data.Referencia
                        this.SelecNotaCredito.Nit = data.Nit
                        this.SelecNotaCredito.Nombre = data.Nombre
                        this.SelecNotaCredito.TipoReceptor = data.TipoReceptor
                        this.SelecNotaCredito.CuiPasaporte = data.CuiPasaporte
                        this.SelectFactura = data
                        this.axios.post


                        if (this.SelecNotaCredito.FacturaFel.split(' ',1)[0].length <6){
                            this.Consulta().MostrarMensaje('Factura no tiene certificado electrónico',this.sndMsg.errorMsg);
                            return;
                        }

                        this.axios.post('/app/v1_contabilidad_hospital/DisponibleFactura', {

                            SerieFacturaCorta:this.SelecNotaCredito.SerieFactura,
                            NumeroFacturaCorta:this.SelecNotaCredito.Factura
                        })
                        .then(resp => {
                            this.SelecNotaCredito.Disponible = resp.data.json[0].Disponible;
                            this.SelecNotaCredito.TotalNotasEmitidas = resp.data.json[0].TotalNotas;

                            this.OnChangeConcepto();
                        })
                    }
                })
            },

            AnularNotaCredito(){
                this.axios.post('/app/v1_contabilidad_hospital/AnulaNotaCredito', {
                    SerieNotaCorta: this.SelectAnulacion.SerieNotaCorta,
                    NumeroNotaCorta: this.SelectAnulacion.CodigoNotaCorta,
                    SerieFacturaCorta: this.SelectAnulacion.SerieFactura,
                    NumeroFacturaCorta: this.SelectAnulacion.Factura,
                    Razon: this.MotivoAnulacion
                })
                .then(resp => {
                     if(resp.data.codigo == '0'){

                    this.NumeroNotaCorta = parseInt(resp.data.numeroNota);

                        this.Consulta().MostrarMensaje('Sincronizando anulación: '+ this.SerieNotaAsignada +'-'+ this.NumeroNotaCorta,this.sndMsg.succesMsg);

                        this.axios.post('/app/v1_FacturaElectronica/AnulaDte',{
                                serieFactura: this.SerieNotaAsignada,
                                numeroFactura: this.NumeroNotaCorta,
                                tipoDocumento: 2
                            }).then(resp=>{
                                        if(resp.data.codigo == 0 && resp.data.json[0].codigo == 0){
                                            // this.BitacoraImpresion(this.data);
                                            this.Consulta().MostrarMensaje(resp.data.json[0].descripcion, this.sndMsg.succesMsg);
                                            this.Limpiar();
                                            this.IsNotaCredito = false;
                                            this.RecuperaNotasdeCredito();

                                        }else{
                                            this.Consulta().MostrarMensaje(resp.data.json[0].descripcion,this.sndMsg.errorMsg);
                                        }
                            })

                     }else{
                        this.Consulta().MostrarMensaje(resp.data.descripcion,this.sndMsg.errorMsg);
                     }
                })
            },
            Limpiar(){
                this.MotivoAnulacion = ''
                this.TotalDebe = ''
                this.TotalHaber = ''
                this.SelecNotaCredito.Disponible = 0,
                this.SelecNotaCredito.Fecha= new Date().toISOString().slice(0, 10)
                this.SelecNotaCredito.Hospital = this.$store.state.sesion.sesion_sucursal
                this.SelecNotaCredito.NotaFel = ''
                this.SelecNotaCredito.NotaInterno = ''
                this.SelecNotaCredito.Status = ''
                this.SelecNotaCredito.TotalNota = ''
                this.SelecNotaCredito.FacturaFel = ''
                this.SelecNotaCredito.SerieFactura = ''
                this.SelecNotaCredito.Factura = ''
                this.SelecNotaCredito.Nit = ''
                this.SelecNotaCredito.Nombre = ''
                this.SelecNotaCredito.Concepto = ''
                this.SelecNotaCredito.RadioValorFactura = ''
                this.SelecNotaCredito.Servicios = ''
                this.SelecNotaCredito.Ventas = ''
                this.ListAuxiliarClientes = []
                this.SelectAnulacion = {}
            },
            OnChangeConcepto(){
                this.ListAuxiliarClientes = [];
                this.SelecNotaCredito.Ventas = 0;
                this.SelecNotaCredito.Servicios = 0
                if(this.SelecNotaCredito.Concepto != '' && !this.isEmptyObject(this.SelectFactura))
                {
                   if ( this.SelecNotaCredito.Disponible > 0 ) 
                   {
                        this.IsNotaNueva = false
                    }else{
                        this.IsNotaNueva = true
                        this.Consulta().MostrarMensaje('Factura no tiene disponible para notas',this.sndMsg.errorMsg);
                    }
                }
            },
            TipoNotaChange(){
                this.ListAuxiliarClientes = [];
                if(this.SelecNotaCredito.RadioValorFactura == 0){

                    this.axios.post('/app/v1_contabilidad_hospital/GetAuxiliarClientesReveso', {
                        TipoNota: this.SelecNotaCredito.RadioValorFactura,
                        FechaRegistro: this.SelecNotaCredito.Fecha,
                        SerieFacturaCorta: this.SelecNotaCredito.SerieFactura,
                        NumeroFacturaCorta: this.SelecNotaCredito.Factura
                    })
                    .then(resp => {
                        let SumDebe = 0
                        let SumHaber = 0

                        if(resp.data.json[0].codigo != 0){
                            this.Consulta().MostrarMensaje(resp.data.json[0].descripcion,this.sndMsg.errorMsg);
                            this.IsNotaNueva = true
                            this.SelecNotaCredito.Concepto = ''
                            this.SelecNotaCredito.RadioValorFactura = ''
                            return
                        }else{
                            if(!this.isEmptyObject(resp.data.json)){
                                // if(resp.data.codigo == '-1'){
                                //     this.Consulta().MostrarMensaje(resp.data.json[0].descripcion + ' aquí',this.sndMsg.errorMsg);
                                //     this.IsNotaNueva = true
                                //     this.SelecNotaCredito.Concepto = ''
                                //     this.SelecNotaCredito.RadioValorFactura = ''
                                //     return
                                // }
                                this.ListAuxiliarClientes = resp.data.json.map(m => {
                                    return{
                                        ...m,
                                        Status: 'P'
                                    }
                                })
                                this.SelecNotaCredito.Servicios = resp.data.json[0].TotalServicio
                                this.SelecNotaCredito.Ventas = resp.data.json[0].TotalVenta
                                this.ListAuxiliarClientes.forEach(function(data){
                                    SumDebe += parseFloat(data.Debe)
                                    SumHaber += parseFloat(data.Haber)
                                })
                                this.TotalDebe = SumDebe.toFixed(2)
                                this.TotalHaber = SumHaber.toFixed(2)


                                this.SelecNotaCredito.TotalNota = parseFloat(this.TotalHaber);


                            }
                        }
                    })
                }else{
                    this.axios.post('/app/v1_contabilidad_hospital/GetParcialFactura', {
                        SerieFacturaCorta: this.SelecNotaCredito.SerieFactura,
                        NumeroFacturaCorta: this.SelecNotaCredito.Factura
                    })
                    .then(resp => {
                        if(resp.data.codigo == 0){
                            this.FacturaParcial = resp.data.json[0]
                            this.FacturaParcial.FacturaFel = this.SelecNotaCredito.FacturaFel
                            // this.FacturaParcial = resp.data.json.map(m =>{
                            //     return {
                            //         ...m,
                            //         Total:parseFloat(m.Total).toFixed(2)
                            //     }
                            // });
                           
                        }
                    })
                    this.axios.post('/app/v1_contabilidad_hospital/GetParcialAuxiliarClientes', {
                        SerieFacturaCorta: this.SelecNotaCredito.SerieFactura,
                        NumeroFacturaCorta: this.SelecNotaCredito.Factura
                    })
                    .then(resp => {
                        let SumTotalInicial = 0
                        if(resp.data.codigo == 0){
                            this.ListCuentasAuxiliarParcial = resp.data.json.map(m => {
                                return{
                                    ...m,
                                    Status: 'P'
                                }
                            })
                            this.ListCuentasAuxiliarParcial.forEach(function(data){
                                SumTotalInicial += parseFloat(data.ValorInicial)
                            })

                            this.TotalValorFactura = SumTotalInicial
                        }
                    })
                    this.TotalValorNota = 0
                    this.IsFacturaParcial = true
                }
            },
            OnChangeValorNota(data){
                let SumTotalNota = 0
                if(parseFloat(data.ValorInicial) < parseFloat(data.Debe)){
                    this.Consulta().MostrarMensaje('Monto NO válido, verifique total de este rubro',this.sndMsg.errorMsg);
                    data.Debe = 0
                    return
                }
                this.ListCuentasAuxiliarParcial.forEach(function(data){
                    SumTotalNota += parseFloat(data.Debe)
                })
                this.TotalValorNota = SumTotalNota
            },

            AceptarValoresParciales(){
                let SumaServicios = 0
                let SumaBienes = 0
                this.ListAuxiliarClientes = []
                this.tempCuentasAuxiliarParcial = []

                this.tempCuentasAuxiliarParcial = this.ListCuentasAuxiliarParcial.filter( d => (parseFloat(d.Debe)>0));

                this.ListCuentasAuxiliarParcial = [];
                this.ListCuentasAuxiliarParcial = this.tempCuentasAuxiliarParcial;

                this.ListCuentasAuxiliarParcial.forEach(function(data){
                    if(data.TipoVenta =='S'){
                        SumaServicios += parseFloat(data.Debe)
                    }else{
                        SumaBienes += parseFloat(data.Debe)
                    }
                })

                if(SumaServicios == 0){
                    this.SelecNotaCredito.Servicios = 0
                }else{
                    this.SelecNotaCredito.Servicios = SumaServicios
                }
                if(SumaBienes == 0){
                    this.SelecNotaCredito.Ventas = 0
                }else{
                    this.SelecNotaCredito.Ventas = SumaBienes
                }
                this.SelecNotaCredito.TotalNota = SumaServicios + SumaBienes
                if((SumaServicios + SumaBienes + parseFloat(this.FacturaParcial.NotasAnt)) > parseFloat(this.FacturaParcial.Facturado)){
                    this.Consulta().MostrarMensaje('Valor de notas exceden el valor de esta factura',this.sndMsg.errorMsg);
                    this.SelecNotaCredito.Ventas = 0
                    this.SelecNotaCredito.Servicios = 0
                    return
                }

                this.axios.post('/app/v1_contabilidad_hospital/CargaInformacionNotaParcial', {
                    SerieFacturaCorta: this.SelecNotaCredito.SerieFactura,
                    NumeroFacturaCorta: this.SelecNotaCredito.Factura,
                    TotalNotaParcial: this.TotalValorNota
                })
                .then(resp => {
                    if(resp.data.codigo == 0){
                        if(resp.data.json.filter(cuenta => cuenta.Cuenta === 'NoCtaEnFac' ).length >0){
                            this.Consulta().MostrarMensaje('Verifique las cuentas contables',this.sndMsg.errorMsg);
                        }
                        let SumDebe = 0
                        let SumHaber = 0
                        

                        let ListadoJson = resp.data.json.map(m => {
                            return{
                                ...m,
                                Status: 'P'
                            }
                        })

                        this.ListAuxiliarClientes.push(ListadoJson[0])
                        
                        let listadoCuentas = this.ListCuentasAuxiliarParcial.map(m => {
                            return{
                                ...m,
                                Debe: (parseFloat(m.Debe) - 
                                       (parseFloat(m.Debe) - (100 * (parseFloat(m.Debe)/(1 + (parseFloat(m.PorcentajeIva)/100))))/100)).toFixed(2),
                                Status: 'P'
                                
                            }
                        })

                        for(let i = 0; this,this.ListCuentasAuxiliarParcial.length > i; i++){
                            this.ListAuxiliarClientes.push(listadoCuentas[i])

                        }
                        
                        this.ListAuxiliarClientes.push(ListadoJson[1])

                        this.ListAuxiliarClientes.forEach(function(data){
                            SumDebe += parseFloat(data.Debe)
                            SumHaber += parseFloat(data.Haber)
                        })

                        this.TotalDebe = SumDebe.toFixed(2)
                        this.TotalHaber = SumHaber.toFixed(2)
                        this.AjusteItem = this.ListAuxiliarClientes.length-2;
                                               

                        this.DiferenciaAux = (this.TotalHaber - this.TotalDebe).toFixed(2);
                        this.ListAuxiliarClientes[this.AjusteItem].Debe = 
                            parseFloat(this.ListAuxiliarClientes[this.AjusteItem].Debe) + 
                            parseFloat(this.DiferenciaAux);

                        this.TotalDebe = parseFloat(this.TotalDebe) + parseFloat(this.DiferenciaAux);

                        this.IsFacturaParcial = false
                    }
                })
            },

            CargarCaja(cajaSeleccionada){                
                this.configuracion.cajaSeleccionada   = cajaSeleccionada.Cajero
                this.configuracion.activarFacturacion = cajaSeleccionada.Factura
                this.SerieNota = cajaSeleccionada.Cajero.SerieFac;
                this.SerieNotaAsignada = this.SerieNota;
                this.RecuperaNotasdeCredito();
            },

            Consulta(){
                return{
                    init: async () => {

                    },

                    MostrarMensaje:(mensaje, tipoMsg)=>{
                        this.$vs.notify({
                            time:this.sndMsg.delayTime,
                            title: 'Notas de crédito',
                            color: tipoMsg,
                            text:mensaje,
                            position: 'top-center'
                        })
                    },
                }
            }
        },

        mounted(){
            this.SelecNotaCredito.Fecha= new Date().toISOString().slice(0, 10)
            this.SelecNotaCredito.Hospital = this.$store.state.sesion.sesion_sucursal
            this.Consulta().init()
            
        },
        watch: {

            IsNotaCredito(value){
                if(value){
                    if(this.NuevaNota == 1){
                        this.SelecNotaCredito.NotaInterno = this.SerieNotaAsignada;
                        this.SelecNotaCredito.RadioValorFactura = ''
                    }
                    
                }
            }
        }
    }
</script>
<style scoped>
    .showvalues{
        border: red;
    }
    .labelPadding{
        padding-left: 5px;
    }
    h3{
        color: #E74C3C;
    }
    h5{
        padding-left: 15px;
    }
    h4{
        color:blue;
    }
    .almacenador{
        border: 1px solid #888;

    }
    .div-container{
        padding: 5px;
        margin: 10px;
    }
    .inputTotal{
        outline: none;
        background-color: #dfe;
        border: 0;
    }
    .con-exemple-prompt{
        padding: 10px;
        padding-bottom: 0px;
    }
    .vs-input-motivo{
        width: 100%;
        margin-top: 10px;
    }
    .almacenador{
        border: 1px solid #888;

    }
    .div-container{
        padding: 5px;
        margin: 10px;
    }
    .panel-cajero-elegir{
        background-color: rgba(var(--vs-danger), 1) !important; 
    }
    .panel-cajero {
        display:inline-block;
        border-radius: 25px;
        background-color: rgba(var(--vs-primary), 1);
        color: white;
        padding: 5px 65px 5px 10px;
        position: relative;
        width: auto;
        /* top: 12px; */
        overflow: hidden;
        /* margin-right: -14px; */
        font-size: 13px;
        cursor: pointer;
    }
    
</style>