<template>
<div class="layout--main" :class="[layoutTypeClass, navbarClasses, footerClasses, {'no-scroll': isAppPage}]">
    <v-nav-menu-simple v-if="menuDesign == 0" :navMenuItems="funcionalidades" title="Hospital la Paz"/>
    <v-nav-menu v-if="menuDesign == 1" :navMenuItems="funcionalidades" title="Hospital la Paz" 
        :openGroupHover="false"
        :reduce-not-rebound="false"
    />
    <div id="content-area" :class="['content-area-lg', {'show-overlay': bodyOverlay}]">
        <div id="content-overlay" />

        <!-- Navbar -->
        <template>
            <the-navbar-vertical  :menus="funcionalidades" :navbarColor="navbarColor" :class="[{'text-white' : isNavbarDark  && !isThemeDark},{'text-base'  : !isNavbarDark && isThemeDark}]" />
        </template>
        <!-- /Navbar -->
        <div class="alerta_empresa" v-if="sesion.sesion_empresa=='' && seleccionEmpresa.empresas.length>0 && sesion.sesion_validar">
            <vs-alert active="true" color="danger" style="text-align:center;height:80px">
                No se ha seleccionado la empresa
                <br>
                <vs-button @click="seleccionEmpresa.mostrar=true" color="#3498db">Seleccionar Empresa</vs-button>
            </vs-alert>
        </div>
        <!-- Si no existen empresas a mostrar -->

        <ValidarCredenciales/>

        <div class="alerta_empresa" v-if="!sesion.sesion_sucursal_nombre && seleccionEmpresa.empresas.length==0 && sesion.sesion_validar">
            <vs-alert active="true" style="text-align:center">
                <i class="fas fa-exclamation-triangle"></i> No cuenta con un rol definido
            </vs-alert>
        </div>

        <vs-popup title="Selección de Empresa" :active.sync="seleccionEmpresa.mostrar" class="seleccionEmpresa">
            <div class="flex">
                <div class="empresa w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-5">
                    <div class="seleccion_empresa" v-for="(l,index) in seleccionEmpresa.empresas" :key="index" :class="[(seleccionEmpresa.seleccion_index==index)?'activo':'']" v-on:click="((seleccionEmpresa.empresas[index].Sub.length>0)?seleccionEmpresa.seleccion_index = index:seleccion_sucursal(l.Empresa,1))">
                        [{{l.Empresa}}] {{l.Nombre}}
                    </div>
                </div>
                <div class="sucursal w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-5">
                    <div v-if="seleccionEmpresa.seleccion_index>-1">
                        <h5>{{seleccionEmpresa.empresas[seleccionEmpresa.seleccion_index].Nombre}}</h5><br>
                        <div class="seleccion_empresa" v-for="(l,index) in seleccionEmpresa.empresas[seleccionEmpresa.seleccion_index].Sub" :key="index" v-on:click="seleccion_sucursal(l.CodigoSuc,2)">
                            [{{l.CodigoSuc}}] {{l.NombreSuc}}
                        </div>
                    </div>
                    <div v-else style="text-align:center">
                        Seleccione la empresa a utilizar
                    </div>
                </div>
            </div>
        </vs-popup>

        <div class="content-wrapper" v-blur="!login.bloqueo_temporal && sesion.sesion_validar==false">
            <br>
            <div class="router-view" v-if="sesion.sesion_sucursal_nombre || (seleccionEmpresa.empresasCargadas && seleccionEmpresa.empresas.length == 0)">
                <div class="router-content">
                    <transition :name="routerTransition">
                        <div v-if="$route.meta.breadcrumb || $route.meta.pageTitle" class="router-header flex flex-wrap items-center mb-6">
                            <div class="content-area__heading" :class="{'pr-4 border-0 md:border-r border-solid border-grey-light' : $route.meta.breadcrumb}">
                                <h2 class="mb-1">{{ routeTitle }}</h2>
                            </div>

                            <!-- BREADCRUMB -->
                            <vx-breadcrumb class="ml-4 md:block hidden" v-if="$route.meta.breadcrumb" :route="$route" :isRTL="$vs.rtl" />

                            <!-- DROPDOWN -->
                            <vs-dropdown vs-trigger-click class="ml-auto md:block hidden cursor-pointer">
                                <vs-button radius icon="icon-settings" icon-pack="feather" />

                                <vs-dropdown-menu class="w-32">
                                    <vs-dropdown-item>
                                        <div @click="$router.push('/pages/profile').catch(() => {})" class="flex items-center">
                                            <feather-icon icon="UserIcon" class="inline-block mr-2" svgClasses="w-4 h-4" />
                                            <span>Profile</span>
                                        </div>
                                    </vs-dropdown-item>
                                    <vs-dropdown-item>
                                        <div @click="$router.push('/apps/todo').catch(() => {})" class="flex items-center">
                                            <feather-icon icon="CheckSquareIcon" class="inline-block mr-2" svgClasses="w-4 h-4" />
                                            <span>Tasks</span>
                                        </div>
                                    </vs-dropdown-item>
                                    <vs-dropdown-item>
                                        <div @click="$router.push('/apps/email').catch(() => {})" class="flex items-center">
                                            <feather-icon icon="MailIcon" class="inline-block mr-2" svgClasses="w-4 h-4" />
                                            <span>Inbox</span>
                                        </div>
                                    </vs-dropdown-item>
                                </vs-dropdown-menu>

                            </vs-dropdown>
                        </div>
                    </transition>

                    <div class="content-area__content">

                        <back-to-top bottom="5%" :right="$vs.rtl ? 'calc(100% - 2.2rem - 38px)' : '30px'" visibleoffset="500" v-if="!hideScrollToTop">
                            <vs-button icon-pack="feather" icon="icon-arrow-up" class="shadow-lg btn-back-to-top" />
                        </back-to-top>
                        <h1 v-show="false">{{extra}}</h1>

                        <transition :name="routerTransition" mode="out-in">
                            <keep-alive>
                                <router-view @changeRouteTitle="changeRouteTitle" @setappclasses="(classesStr) => $emit('setappclasses', classesStr)" />
                            </keep-alive>
                        </transition>
                    </div>
                </div>
            </div>
        </div>
        <the-footer />
    </div>

    <Info />

    <PantallaLogin v-if="!login.bloqueo_temporal && sesion.corporativo >0 && sesion.sesion_validar==false" reanudar="true" />

</div>
</template>

<script>
import BackToTop from 'vue-backtotop'
import TheNavbarVertical from '@/layouts/components/navbar/TheNavbarVertical.vue'
import TheFooter from '@/layouts/components/TheFooter.vue'
import themeConfig from '@/../themeConfig.js'
import VNavMenuSimple from '@/layouts/components/vertical-nav-menu/VerticalNavMenuSimple.vue'
import VNavMenu from '@/layouts/components/vertical-nav-menu/VerticalNavMenu.vue'
//Carga de mensajes de devextreme en español para no cargar en cada componente
import {CustomDevExtremeEsMessages} from '@/views/pages/EXPEDIENTE/data.js'
import esMessages from "devextreme/localization/messages/es.json"
import {
    locale,
    loadMessages
} from "devextreme/localization"

export default {
    components: {
        BackToTop,
        TheFooter,
        TheNavbarVertical,
        VNavMenuSimple,
        VNavMenu,
        // Notas: () => import( /* webpackPrefetch: true */ /* webpackChunkName: "componentes" */ '@/components/sermesa/documentacion_notas.vue'),
        PantallaLogin: () => import('../../views/pages/Login.vue'),

        Info: () => import('../../components/sermesa/global/SMInfo.vue'),

        ValidarCredenciales: () => import('./components/ValidarCredenciales.vue')
    },
    data() {
        return {
            footerType: themeConfig.footerType || 'static',
            hideScrollToTop: themeConfig.hideScrollToTop,
            isNavbarDark: false,
            navbarColor: themeConfig.navbarColor || '#fff',
            navbarType: themeConfig.navbarType || 'floating',
            // navMenuItems: navMenuItems,
            routerTransition: themeConfig.routerTransition || 'none',
            routeTitle: this.$route.meta.pageTitle,
            //popup
            seleccionEmpresa: {
                mostrar: false,
                seleccion: null,
                empresas: [],
                empresasCargadas: false, //Indica si ya se realizo la carga
                seleccion_index: -1
            },

            login: {
                password: null,
                bloqueo_temporal: true
            },
            menuDesign: 1,//diseño con el que se dibujará el menu ;)
        }
    },
    watch: {
        "$route"() {
            this.routeTitle = this.$route.meta.pageTitle
        },
        isThemeDark(val) {
            const color = this.navbarColor == "#fff" && val ? "#10163a" : "#fff"
            this.updateNavbarColor(color)
        },
        "$store.state.mainLayoutType"(val) {
            this.setNavMenuVisibility(val)
        }
    },
    computed: {
        sesion() {
            return this.$store.state.sesion
        },
        global() {
            return this.$store.state.global
        },
        extra() {
            const e = this.$store.state.extra
            if (e.loading) {
                this.$vs.loading()
            } else {
                this.$vs.loading.close()
            }
            if (e.notificacion) {
                const a = {
                    ...e.notificacion
                }
                this.$vs.notify(a)
                this.$store.dispatch('notify', null)
            }
            return e
        },
        ///Carga la estructura de funcionalidades del store y lo adecúa a la estructura de Menú
        funcionalidades() {
            return this.$build_menu (this.$store.state.funcionalidades)
        },
        bodyOverlay() {
            return this.$store.state.bodyOverlay
        },
        contentAreaClass() {
            if (this.mainLayoutType === "vertical") {
                if (this.verticalNavMenuWidth == "default") return "content-area-reduced"
                else if (this.verticalNavMenuWidth == "reduced") return "content-area-lg"
                else return "content-area-full"
            }
            // else if(this.mainLayoutType === "boxed") return "content-area-reduced"
            else return "content-area-full"
        },
        footerClasses() {
            return {
                'footer-hidden': this.footerType == 'hidden',
                'footer-sticky': this.footerType == 'sticky',
                'footer-static': this.footerType == 'static',
            }
        },
        isAppPage() {
            return this.$route.meta.no_scroll
        },
        isThemeDark() {
            return this.$store.state.theme == 'dark'
        },
        layoutTypeClass() {
            return `main-${this.mainLayoutType}`
        },
        mainLayoutType() {
            return this.$store.state.mainLayoutType
        },
        navbarClasses() {
            return {
                'navbar-hidden': this.navbarType == 'hidden',
                'navbar-sticky': this.navbarType == 'sticky',
                'navbar-static': this.navbarType == 'static',
                'navbar-floating': this.navbarType == 'floating',
            }
        },
        verticalNavMenuWidth() {
            return this.$store.state.verticalNavMenuWidth
        },
        windowWidth() {
            return this.$store.state.windowWidth
        }
    },
    methods: {
        changeRouteTitle(title) {
            this.routeTitle = title
        },
        updateNavbarColor(val) {
            this.navbarColor = val
            if (val == "#fff") this.isNavbarDark = false
            else this.isNavbarDark = true
        },
        setNavMenuVisibility(layoutType) {
            if ((layoutType === 'horizontal' && this.windowWidth >= 1200) || (layoutType === "vertical" && this.windowWidth < 1200)) {
                this.$store.commit('TOGGLE_IS_VERTICAL_NAV_MENU_ACTIVE', false)
                this.$store.dispatch('updateVerticalNavMenuWidth', 'no-nav-menu')
            } else {
                this.$store.commit('TOGGLE_IS_VERTICAL_NAV_MENU_ACTIVE', true)
            }
        },

        // ordenar el menu
        sort_menu(a, b) {
            if (a.name == 'Herramientas') return 1
            if (a.name == 'Administrador') return 1

            if (a.name < b.name) {
                return -1;
            }
            if (a.name > b.name) {
                return 1;
            }

            return 0;
        },

        // validando funcionalidades
        validar_funcionalidad() {
            this.axios.post('/app/usuario/obtener_privilegios', {})
                .then(resp => {
                    // console.log(resp.data)
                    this.$store.dispatch('sesionValidar') //valida que se ha cargado la informacion

                    // si es necesario seleccionar empresa m cargara toda la informacion de las empresas y sus sucursales para ser cargadas
                    if (resp.data.tipo == "Empresa") {
                        let temp_emp = ''

                        resp.data.json.map(data => {
                            if (temp_emp != data.Codigo) {
                                this.seleccionEmpresa.empresas.push({
                                    Empresa: data.Codigo,
                                    Nombre: data.Nombre,
                                    Sub: []
                                })
                            }
                            if (data.CodigoSuc != "") this.seleccionEmpresa.empresas[this.seleccionEmpresa.empresas.length - 1].Sub.push(data)
                            temp_emp = data.Codigo
                        })

                        // Indica que ya se realizo la carga
                        this.seleccionEmpresa.empresasCargadas = true

                        // Si existen empresas para su selección
                        if (this.seleccionEmpresa.empresas.length > 0) this.seleccionEmpresa.mostrar = true;
                    } else {
                        this.$store.dispatch('funcionalidadListado', resp.data.json)
                        this.seleccionEmpresa.mostrar = false;
                    }
                })
        },

        seleccion_empresa(index) {
            this.seleccion_empresa.seleccion_index = index
        },

        // seleccionar Sucursal
        seleccion_sucursal(vsucursal, tipo) {
            this.axios.post('/app/usuario/seleccion_sucursal', {
                    Sucursal: vsucursal,
                    Tipo: tipo
                })
                .then(resp => {
                    //guardando la informacion en el localstorage del cliente
                    this.$store.dispatch('sesionGuardar', resp.data)

                    location.reload();

                    //this.validar_funcionalidad()            // verificar nuevamente los privilegios
                })
        },

        reanudar_sesion() {
            const url = this.$store.state.global.url
            const token = this.$store.state.global.tokenDefault
            const config = {
                headers: {
                    Authorization: 'Bearer ' + token
                }
            };
            this.axios.post(url + 'app/usuario/verificar', { //DEBE SER GET EN LUGAR DE POST

                    Corporativo: this.sesion.corporativo,
                    Password: this.login.password
                }, config)
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Sesión - Error',
                            text: 'Usuario o contraseña incorrecta',
                        })
                        this.password = null
                    } else {
                        this.$store.dispatch('sesionGuardar', resp.data)
                        // window.location.href = "/";
                    }
                })
                .catch(() => {})
        },

        cerrar_sesion() {
            this.login.bloqueo_temporal = true
            this.$store.dispatch('sesionEliminar')
            location.replace("/pages/login");
        }
    },
    created() {
        // this.login.bloqueo_temporal = (localStorage.getItem("sermesa_sesion")!=null)
        // console.log(this.login.bloqueo_temporal)

        //liberación del bloqueo temporal
        setTimeout(() => {
            this.login.bloqueo_temporal = false
        }, 1000)

        const sesion = this.$store.state.sesion
        if (sesion.bienvenida == true) {
            this.$store.dispatch('sesionBienvenida', false)
            this.$vs.notify({
                title: 'Bienvenido(a)',
                text: sesion.usuario,
                color: 'success'
            })
        }

        this.validar_funcionalidad()

        const color = this.navbarColor == "#fff" && this.isThemeDark ? "#10163a" : this.navbarColor
        this.updateNavbarColor(color)
        this.setNavMenuVisibility(this.$store.state.mainLayoutType)

        window.onclick = () => {
            this.$store.dispatch('sessionReiniciarTiempo')
        }

        window.onkeydown = () => {
            this.$store.dispatch('sessionReiniciarTiempo')
        }

        // ANTES DE SALIR
        // window.onbeforeunload = preguntarAntesDeSalir;
        // function preguntarAntesDeSalir () {
        //   var respuesta;
        //   var currentUrl = window.location.pathname;
        //   if (currentUrl !='/' && currentUrl != '/pages/login'){
        //     respuesta = confirm ( '¿Seguro que quieres salir?' );
        //     if ( respuesta ) {
        //       window.onunload = function () {
        //         return true;
        //       }
        //     } else {
        //       return false;
        //     }
        //   }
        // }

        //Para la carga de mensajes de devextreme en español para no cargar en cada componente
        loadMessages(esMessages)
        loadMessages({
            es: CustomDevExtremeEsMessages
        })
        locale('es-GT')
    }
}
</script>

<style scoped>
.alerta_empresa {
    top: 60px;
    position: relative;
    padding: 0 50px;
    z-index: 999;
}
</style>
<style>
.dx-viewport,
.dx-device-phone,
.dx-device-mobile,
.dx-device-android,
.dx-device-android-6,
.dx-theme-generic,
.dx-theme-generic-typography,
.dx-color-scheme-light

{
    color: #2980B9 !important;
}
/** Habilitar scroll en moviles */
.dx-scrollable-container {
    touch-action: auto !important;
}

/**warning css for devextreme button 
#f7b334
#ffc107
*/
/* Contained */

.dx-button-mode-contained.dx-button-warning.dx-state-focused
,.dx-button-mode-contained.dx-button-warning.dx-state-hover {
    background-color: #e6a225 !important;
}
.dx-button-mode-contained.dx-button-warning {
    background-color: #f7b334;
    border-color: transparent;
}

.dx-button-mode-contained.dx-button-warning .dx-icon
,.dx-button-mode-contained.dx-button-warning
{
    color: #fff;
}
/* Contained */

/* outlined */
.dx-button-mode-outlined.dx-button-warning.dx-state-focused
,.dx-button-mode-outlined.dx-button-warning.dx-state-hover {
    background-color: rgba(247, 179, 52, 0.1);
}
.dx-button-mode-outlined.dx-button-warning {
    background-color: transparent;
    border-color: #f7b334;
}
.dx-button-mode-outlined.dx-button-warning .dx-icon
,.dx-button-mode-outlined.dx-button-warning 
{
    color: #f7b334;
}
/* outlined */

/* text */
.dx-button-mode-text.dx-button-warning.dx-state-focused
,.dx-button-mode-text.dx-button-warning.dx-state-hover {
    background-color: rgba(247, 179, 52, 0.1);
}

.dx-button-mode-text.dx-button-warning
,.dx-button-mode-text.dx-button-warning .dx-icon
{
    color: #f7b334;
    background-color: transparent;
    border-color: transparent
}
/**warning css for devextreme button */

.dx-overlay-wrapper.dx-popup-wrapper
,.dx-overlay-content.dx-popup-normal
{
    z-index: 53000 !important;
}
</style>
