<template>
    <div>
        <vx-card>
            <label class="w-full titulo">Pantalla Informativa - {{sesion.sesion_sucursal_nombre}} -
                {{info.dateToday}}</label>
            <table class="w-full bordestabla">
                <th class="bordesth">Hora</th>
                <th class="bordesth">Qx</th>
                <th class="bordesth">Procedimiento</th>
                <th class="bordesth">Cirujanos</th>
                <th class="bordesth">Anestesiólogo</th>
                <th class="bordesth">Paciente</th>
                <tr :key="indextr" v-for="(tr, indextr) in cirugias" class="intercalado">
                    <td class="bordestd"> {{ tr.HoraInicio }}</td>
                    <td class="bordestd"> {{ tr.Quirofano }}</td>
                    <td class="bordestd"> {{ tr.<PERSON>ced<PERSON> }}</td>
                    <td class="bordestd"> {{ tr.Cirujan<PERSON> }}</td>
                    <td class="bordestd"> {{ tr.Anestesiologos }}</td>
                    <td class="bordestd"> {{ tr.Paciente }}</td>
                </tr>
            </table>
            <!--<vs-table2 max-items="15" :data="cirugias" tooltip>
                <template slot="thead">
                    <th>Hora Inicio</th>
                    <th>Quirófano</th>
                    <th>Procedimiento</th>
                    <th>Cirujanos</th>
                    <th>Anestesiologos</th>
                    <th>Paciente</th>
                </template>
                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">
                        <vs-td2 width='5%'>
                            {{ tr.HoraInicio }}
                        </vs-td2>
                        <vs-td2 width='8%'>
                            {{ tr.Quirofano }}
                        </vs-td2>
                        <vs-td2 width='15%'>
                            {{ tr.Procedimiento }}
                        </vs-td2>
                        <vs-td2>
                            {{ tr.Cirujanos }}
                        </vs-td2>
                        <vs-td2>
                            {{ tr.Anestesiologos }}
                        </vs-td2>
                        <vs-td2>
                            {{ tr.Paciente }}
                        </vs-td2>
                    </tr>
                </template>
            </vs-table2>-->
        </vx-card>
    </div>
</template>

<script>
export default {
    data() {
        return {
            /**
             * Documentación del módulo
             */
            doc_: [
                'Pantalla de administración de cirugías',
            ],
            info: {
                dateToday: new Date().toLocaleDateString()
            },
            cirugias: [],
            number: 0
        }
    },
    computed: {
        sesion() {
            return this.$store.state.sesion
        }
    },
    methods: {
        Consulta() {
            return {
                init: () => {
                    
                    this.axios.post('/app/salaoperaciones/ListarPantallaInformativa', {})
                        .then(resp => {
                            this.cirugias = resp.data.json;
                        })
                }
            }
        },
        Otros() {
            return {
                ActualizarPantalla: () => {
                    let scope = this;

                    setTimeout(function () {
                        location.reload()
                    }, 600000);

                    setInterval(function () {
                        
                        scope.axios.post('/app/salaoperaciones/ListarPantallaInformativa', {})
                            .then(resp => {
                                scope.cirugias = resp.data.json;
                                
                            })
                    }, 60000);


                }
            }
        }
    },
    mounted() {
        
        this.Consulta().init()
        this.Otros().ActualizarPantalla()
    }
}
</script>
<style>
/*@media only screen and (max-width: 1000) {
    body {
        background-color: red
    }

    .intercalado:nth-child(even) {
        background-color: rgba(113, 212, 212, 0.4);
    }

    .bordesth {
        border: 1px solid;
        font-size: 150%;
        background-color: rgba(113, 212, 212, 0.4);
        border: 1px solid;
    }

    .bordestabla {
        font-size: 250%;
        border: 1px solid;
    }

    .bordestd {
        border: 1px solid;
    }

    .titulo {
        font-size: 500%;
    }
}*/
@media only screen and (max-width: 1919px) {
    .intercalado:nth-child(even) {
        background-color: rgba(113, 212, 212, 0.4);
    }

    .bordesth {
        border: 1px solid;
        font-size: 125%;
        background-color: rgba(113, 212, 212, 0.4);
        border: 1px solid;
    }

    .bordestabla {
        font-size: 125%;
        border: 1px solid;
    }

    .bordestd {
        border: 1px solid;
    }

    .titulo {
        font-size: 175%;
    }

}

@media only screen and (min-width: 1920px) and (max-width: 2559px) {
    .intercalado:nth-child(even) {
        background-color: rgba(113, 212, 212, 0.4);
    }

    .bordesth {
        border: 1px solid;
        font-size: 150%;
        background-color: rgba(113, 212, 212, 0.4);
        border: 1px solid;
    }

    .bordestabla {
        font-size: 150%;
        border: 1px solid;
    }

    .bordestd {
        border: 1px solid;
    }

    .titulo {
        font-size: 200%;
    }

}

@media only screen and (min-width: 2560px) {
    .intercalado:nth-child(even) {
        background-color: rgba(113, 212, 212, 0.4);
    }

    .bordesth {
        border: 1px solid;
        font-size: 200%;
        background-color: rgba(113, 212, 212, 0.4);
        border: 1px solid;
    }

    .bordestabla {
        font-size: 200%;
        border: 1px solid;
    }

    .bordestd {
        border: 1px solid;
    }

    .titulo {
        font-size: 300%;
    }

}
</style>