{"name": "serm<PERSON>a", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve -p 8081", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "socket": "nodemon socket.js"}, "dependencies": {"@chenfengyuan/vue-barcode": "^1.0.1", "@chenfengyuan/vue-countdown": "^1.1.5", "@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-brands-svg-icons": "^6.4.0", "@fortawesome/free-regular-svg-icons": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/vue-fontawesome": "^2.0.10", "ag-grid-community": "^24.1.0", "ag-grid-vue": "^24.1.1", "apexcharts": "^3.22.2", "auth0-js": "^9.14.0", "axios": "^0.21.0", "babel-eslint": "^10.1.0", "chart.js": "^2.9.4", "core-js": "^3.21.1", "cors": "^2.8.5", "devextreme": "^22.1.3", "devextreme-vue": "^22.1.3", "dotenv": "^8.2.0", "echarts": "^4.9.0", "env-var": "^7.0.0", "eslint-plugin-vue": "^7.1.0", "exceljs": "^4.4.0", "express": "^4.17.1", "file-saver": "2.0.2", "file-saver-es": "^2.0.5", "fs-jetpack": "^5.1.0", "instantsearch.css": "^7.4.5", "internal-ip": "^6.2.0", "jsonwebtoken": "^8.5.1", "jspdf": "^3.0.1", "ls": "^0.2.1", "material-icons": "^0.3.1", "perfect-scrollbar": "^1.5.0", "postcss-rtl": "^1.7.3", "print-js": "^1.6.0", "prismjs": "^1.22.0", "save": "^2.5.0", "signature_pad": "^3.0.0-beta.4", "socket.io": "^2.3.0", "sweetalert2": "^11.15.10", "uniqid": "^5.2.0", "v-blur": "^1.0.4", "v-mask": "^2.2.3", "vee-validate": "^3.4.4", "vue": "^2.6.12", "vue-acl": "4.1.10", "vue-apexcharts": "^1.6.0", "vue-awesome-swiper": "^4.1.1", "vue-axios": "^3.1.3", "vue-backtotop": "^1.6.1", "vue-chartjs": "^3.5.1", "vue-clipboard2": "^0.3.1", "vue-codemirror": "^4.0.6", "vue-context": "6.0.0", "vue-echarts": "^4.1.0", "vue-expandable-image": "^0.1.0", "vue-feather-icons": "^5.1.0", "vue-flatpickr-component": "^8.1.6", "vue-form-wizard": "^0.8.4", "vue-fullcalendar": "^1.0.9", "vue-i18n": "^8.22.1", "vue-instantsearch": "^3.4.2", "vue-materialize-datatable": "^1.0.4", "vue-multiple-progress": "^1.6.0", "vue-multiselect": "^2.1.6", "vue-perfect-scrollbar": "^0.2.1", "vue-prism-component": "^1.2.0", "vue-property-decorator": "^9.1.2", "vue-quill-editor": "^3.0.6", "vue-router": "^3.4.9", "vue-select": "^3.16.0", "vue-simple-calendar": "^5.0.0", "vue-simple-suggest": "^1.10.3", "vue-socket.io": "^3.0.10", "vue-star-rating": "^1.7.0", "vue-sweetalert2": "^5.0.2", "vue-the-mask": "^0.11.1", "vue-tour": "^1.5.0", "vue-tree-halower": "^1.8.3", "vue-video-player": "^5.0.2", "vue2-daterange-picker": "^0.6.1", "vue2-editor": "^2.10.2", "vue2-google-maps": "^0.10.7", "vue2-hammer": "^2.1.2", "vuecode.js": "0.0.27", "vuedraggable": "^2.24.3", "vuejs-datepicker": "^1.6.2", "vuesax": "^3.12.2", "vuex": "^3.5.1", "vuex-map-fields": "^1.4.1", "xlsx": "^0.16.8"}, "devDependencies": {"@fullhuman/postcss-purgecss": "^3.0.0", "@vue/cli-plugin-babel": "^4.5.8", "@vue/cli-plugin-eslint": "^4.5.8", "@vue/cli-service": "^4.5.8", "axios-mock-adapter": "^1.19.0", "eslint": "^7.15.0", "node-sass": "^5.0.0", "purgecss": "^3.0.0", "sass-loader": "^10.0.5", "script-loader": "0.7.2", "tailwindcss": "^1.9.6", "vue-template-compiler": "^2.6.12"}}