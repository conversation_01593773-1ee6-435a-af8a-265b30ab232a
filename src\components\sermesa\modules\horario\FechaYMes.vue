<template>
  <div class="fecha-y-mes">
    <p class="mes"> {{ mes }}</p>
    <p class="fecha">{{fecha}} </p>
  </div>

</template>

<script>
export default {
  data() {
    return {
      fecha: '',
      mes: ''
    };
  },
  async mounted() {
    this.obtenerFechaYMes();
  },
  methods: {
    async obtenerFechaYMes() {
      this.$dbdate() 
      const fechaActual = new Date();
      const opcionesFecha = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
      const opcionesMes = { month: 'long' };

      this.fecha = (await this.$dbdate()).toLocaleDateString('es-ES', opcionesFecha); //fechaActual.toLocaleDateString('es-ES', opcionesFecha);
      this.mes = fechaActual.toLocaleDateString('es-ES', opcionesMes);
    }
  }
};

</script>

<style scoped>
.fecha-y-mes {
  background-color: #f5f5f5;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.fecha-y-mes p {
  margin: 0;
  padding: 5px 0;
}

.fecha {
  font-size: 20px;
}

.mes {
  font-size: 50px;
  text-transform:UPPERCASE;
  COLOR:#3498DB


  }
</style>