<template>
    <div>
        <div>
            <ConsultaPacientes ref="ConsultaPacientesComponent">
            </ConsultaPacientes>
            <DatosPaciente ref="DatosPacienteComponent">
            </DatosPaciente>
            <PacienteNuevo ref="PacienteNuevoComponent">
            </PacienteNuevo>
        </div>
    </div>
</template>


<script>
import ConsultaPacientes from '@/components/sermesa/modules/paciente/ConsultaPacientes.vue'
import PacienteNuevo from '@/components/sermesa/modules/paciente/DatosGeneralesPaciente.vue'
import DatosPaciente from "/src/components/sermesa/modules/paciente/DatosPaciente.vue"

import { ref } from 'vue'

const childConsultaPacientes = ref()
const childPaciente = ref()

export default {
    data() {
        return {
            patientProcess: [
                { step: 1, showButtons: true, visible: true, title: 'ConsultaPacientes', component: ConsultaPacientes, comp: childConsultaPacientes, render: true },
                { step: 2, showButtons: true, visible: false, title: 'CreacionPaciente', component: PacienteNuevo, comp: childPaciente, render: false }
            ]
        }
    },
    mounted() {
    },
    computed: {
    },
    created() {

    },
    components: {
        DatosPaciente,
        PacienteNuevo,
        ConsultaPacientes
    },
    methods: {

    }
}


</script>

<style>
.animation-in {
    transform-origin: left;
    animation: in .6s ease-in-out;
}

.animation-out {
    transform-origin: bottom left;
    animation: out .6s ease-in-out;
}
</style>