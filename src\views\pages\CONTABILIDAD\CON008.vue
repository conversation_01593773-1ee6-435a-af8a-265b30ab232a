<template>
<vx-card title="Partidas" class="Partidas-Container">
    <DxForm :ref="formConsulta" :form-data.sync="formulario" label-mode="floating">
        <DxFormGroupItem :col-count="2">
            <DxFormItem data-field="Periodo" editor-type="dxSelectBox" :editor-options="{ width: 'auto', searchEnabled: true, items: ListaPeriodos,  displayExpr: 'Valor', valueExpr: 'CodigoPeriodo', onSelectionChanged: AsignarPeriodo, readOnly: bloquearNavegacion }" :validationRules="[{ type: 'required' }]" />
            <DxFormItem template="textoEstado" />
        </DxFormGroupItem>
        <DxFormGroupItem :visible="true" caption=" ">
            <DxFormItem template="infoPartida" />
        </DxFormGroupItem>

        <template #textoEstado="{}">
            <div>
                <span style="color: green; font-size: 14px; font-weight: bold;" v-if="DescripcionM === 'M'">Ya se mayorizó en este período</span>
                <br>
                <span style="color: red; font-size: 14px; font-weight: bold;" v-if="DescripcionNoTodas == true">Pero existen partidas sin mayorizar</span>
            </div>
        </template>

        <template #infoPartida="{}">
            <div>
                <DxForm :ref="formEnc" :form-data.sync="formEncabezado" label-mode="floating" :show-validation-summary="true">
                    <DxFormItem template="navegacion" />
                    <DxFormGroupItem :col-count="2">
                        <DxFormGroupItem :col-count="3">
                            <DxFormItem data-field="CodPartida" editor-type="dxTextBox" :editor-options="{ readOnly: true }">
                                <DxFormLabel text="Número" />
                            </DxFormItem>
                            <DxFormItem data-field="Fecha" editor-type="dxDateBox" :is-required="true" :editor-options="{ readOnly: !editarEncabezado && !crearPartida, dataType: 'date', displayFormat: 'dd/MM/yyyy', min: inicioMes, max: finMes }" />
                            <DxFormItem data-field="Referencia" editor-type="dxTextBox" :editor-options="{ readOnly: !editarEncabezado && !crearPartida, maxLenght: 10 }" />

                            <DxFormItem data-field="Descripcion" editor-type="dxTextArea" :is-required="true" :editor-options="{ height: '150px', readOnly: !editarEncabezado && !crearPartida, onFocusOut : handleDescripcionBlur }" :col-span="3">
                                <DxFormLabel text="Descripción" />
                            </DxFormItem>
                        </DxFormGroupItem>
                        <DxFormGroupItem>
                            <DxFormGroupItem :col-count="2">
                                <DxFormItem template="botonesMayorizar" />
                                <DxFormItem template="textoMayorizada" />
                            </DxFormGroupItem>
                            <DxFormGroupItem :col-count="2">
                                <DxFormItem data-field="Generada" editor-type="dxCheckBox" :editor-options="{ readOnly: true }">
                                    <DxFormLabel text="Generada por el Auxiliar" />
                                </DxFormItem>
                                <DxFormItem template="textoAuxiliar" />
                            </DxFormGroupItem>

                            <DxFormGroupItem :col-count="2">
                                <DxFormItem data-field="Tipo" editor-type="dxRadioGroup" :editor-options="{ items: tipoOpciones, displayExpr:'Tipo', valueExpr: 'Valor', layout:'horizontal', readOnly: !editarEncabezado && !crearPartida }">
                                    <DxFormLabel :visible="false" />
                                </DxFormItem>
                                <DxFormItem data-field="Cuenta" editor-type="dxSelectBox" :editor-options="{ width: 'auto', searchEnabled: true, items: cuentasAjuste,  displayExpr: 'Valor', valueExpr: 'CodigoAj', readOnly: !editarEncabezado && !crearPartida }" :visible="formEncabezado.Tipo == 'J'" />
                            </DxFormGroupItem>
                            <DxFormGroupItem :col-count="2" :visible="formEncabezado.Tipo == 'J' && valoresTabla.length == 0 && !editarEncabezado && !crearPartida">
                                <DxFormButtonItem :button-options="buttonLiquidar" name="LiquidarAdmisiones" horizontal-alignment="center" verical-alignment="center" />
                                <DxFormButtonItem :button-options="buttonDevolucion" name="Devolucion" horizontal-alignment="center" verical-alignment="center" />
                            </DxFormGroupItem>
                        </DxFormGroupItem>
                        <!-- <DxFormGroupItem :col-count="12">
                            <DxFormItem :col-span="8" data-field="PagarNombre" editor-type="dxTextBox" :editor-options="{ readOnly: !((Boolean(this.formulario.NumeroOrden) && this.formulario.Funcion == 'CH') || ((this.formulario.Funcion == 'ND' || this.formulario.Funcion == 'NA') && this.formulario.Proveedor !== null && this.formulario.Proveedor !== '')) }">
                                <DxFormLabel text="Pagar a la orden de" />
                            </DxFormItem>
                            <DxFormItem :col-span="4" data-field="Monto" editor-type="dxNumberBox" :editor-options="{ format: '#0.00', step: 0.01 }" />
                        </DxFormGroupItem> -->
                    </DxFormGroupItem>
                    <DxFormItem template="infoTabla" />

                    <template #textoAuxiliar="{}">
                        <div>
                            <span style="font-size: 14px; font-weight: bold; display: flex; align-items: middle;" v-if="formEncabezado.Generada">{{ formEncabezado.Auxiliar }}</span>
                        </div>
                    </template>

                    <template #textoMayorizada="{}">
                        <div>
                            <span style="color: green; font-size: 14px; font-weight: bold; display: flex; align-items: middle;" v-if="formEncabezado.Status && formEncabezado.Status !== 'M'">PENDIENTE</span>
                            <span style="color: red; font-size: 14px; font-weight: bold;" v-if="formEncabezado.Status && formEncabezado.Status === 'M'">MAYORIZADA</span>
                        </div>
                    </template>

                    <template #botonesMayorizar="{}">
                        <div>
                            <DxButton :visible="formEncabezado.Status !== 'M'" :disabled="!formEncabezado.Status || crearPartida" width="auto" height="36px" type="default" class="p-1 ml-1" styling-mode="contained" @click="Mayorizar">
                                <font-awesome-icon class="mr-2" :icon="['fas', 'file-circle-check']" style="font-size: 18px; vertical-align: middle" />
                                <span>Mayorizar</span>
                            </DxButton>
                            <DxButton :visible="formEncabezado.Status === 'M'" :disabled="!formEncabezado.Status || crearPartida" width="auto" height="36px" type="danger" class="p-1 ml-1" styling-mode="contained" @click="Mayorizar">
                                <font-awesome-icon class="mr-2" :icon="['fas', 'file-circle-xmark']" style="font-size: 18px; vertical-align: middle" />
                                <span>Des-Mayorizar</span>
                            </DxButton>
                        </div>
                    </template>

                    <template #navegacion="{}">
                        <div class="navigation-buttons flex flex-row">
                            <vs-button class="ml-1 text-lg" type="filled" color="rgb(110, 110, 110)" size="small" @click="ConsultaPartidas(0, 1)" :disabled="!(formEncabezado.CodPartida > 0)">
                                <vs-tooltip text="Primero">
                                    <font-awesome-icon :icon="['fas', 'backward-step']" />
                                </vs-tooltip>
                            </vs-button>
                            <vs-button class="ml-1 text-xl" type="filled" color="rgb(110, 110, 110)" size="small" @click="ConsultaPartidas(formEncabezado.CodPartida, -1)" :disabled="!(formEncabezado.CodPartida > 0)">
                                <vs-tooltip text="Anterior">
                                    <font-awesome-icon :icon="['fas', 'caret-left']" />
                                </vs-tooltip>
                            </vs-button>
                            <vs-button class="ml-1 text-xl" type="filled" color="rgb(110, 110, 110)" size="small" @click="ConsultaPartidas(formEncabezado.CodPartida, 1)" :disabled="!(formEncabezado.CodPartida > 0)">
                                <vs-tooltip text="Siguiente">
                                    <font-awesome-icon :icon="['fas', 'caret-right']" />
                                </vs-tooltip>
                            </vs-button>
                            <vs-button class="ml-1 text-lg" type="filled" color="rgb(110, 110, 110)" size="small" @click="ConsultaPartidas(999999999, -1)" :disabled="!(formEncabezado.CodPartida > 0)">
                                <vs-tooltip text="Último">
                                    <font-awesome-icon :icon="['fas', 'forward-step']" />
                                </vs-tooltip>
                            </vs-button>

                            <div v-if="editarEncabezado == false && crearPartida == false">
                                <DxButton v-if="formEncabezado.Status !== 'M'" width="auto" height="36px" type="danger" class="p-1 ml-1" styling-mode="contained" @click="EliminarPartida" :disabled="!(formEncabezado.CodPartida > 0)">
                                    <font-awesome-icon class="mr-2" :icon="['fas', 'trash']" style="font-size: 18px; vertical-align: middle" />
                                    <span>Eliminar</span>
                                </DxButton>

                                <DxButton width="auto" height="36px" type="default" class="p-1 ml-1" styling-mode="contained" @click="EditarEncabezado" :disabled="!(formEncabezado.CodPartida > 0)">
                                    <font-awesome-icon class="mr-2" :icon="['fas', 'edit']" style="font-size: 18px; vertical-align: middle" />
                                    <span>Modificar</span>
                                </DxButton>

                                <DxButton width="auto" height="36px" type="success" class="p-1 ml-1" styling-mode="contained" @click="NuevaPartida" :disabled="bloquearNavegacion">
                                    <font-awesome-icon class="mr-2" :icon="['fas', 'plus']" style="font-size: 18px; vertical-align: middle" />
                                    <span>Nueva partida</span>
                                </DxButton>
                            </div>

                            <div v-if="editarEncabezado == true">
                                <DxButton width="auto" height="36px" type="success" class="p-1 ml-1" styling-mode="contained" @click="ActualizarEncabezado">
                                    <font-awesome-icon class="mr-2" :icon="['fas', 'save']" style="font-size: 18px; vertical-align: middle" />
                                    <span>Aceptar</span>
                                </DxButton>
                                <DxButton width="auto" height="36px" type="danger" class="p-1 ml-1" styling-mode="contained" @click="CancelarModificacion">
                                    <font-awesome-icon class="mr-2" :icon="['fas', 'ban']" style="font-size: 18px; vertical-align: middle" />
                                    <span>Cancelar</span>
                                </DxButton>
                            </div>

                            <div v-if="crearPartida == true">
                                <DxButton width="auto" height="36px" type="success" class="p-1 ml-1" styling-mode="contained" @click="GuardarPartida">
                                    <font-awesome-icon class="mr-2" :icon="['fas', 'save']" style="font-size: 18px; vertical-align: middle" />
                                    <span>Guardar partida</span>
                                </DxButton>
                                <DxButton width="auto" height="36px" type="danger" class="p-1 ml-1" styling-mode="contained" @click="CancelarCreacion">
                                    <font-awesome-icon class="mr-2" :icon="['fas', 'ban']" style="font-size: 18px; vertical-align: middle" />
                                    <span>Cancelar</span>
                                </DxButton>
                            </div>
                        </div>
                    </template>

                    <template #infoTabla="{}">
                        <div class="pt-4">
                            <DxDataGrid :ref="gridDetalle" v-bind="DefaultDxGridConfiguration" :data-source="valoresTabla" :headerFilter="{ visible: false, allowSearch: false }" :searchPanel="{ visible: false }" :width="'100%'" height="auto" @editor-preparing="editorDatagrid" :on-saving="GuardaCambiosTabla" @init-new-row="NuevaLinea" @editing-start="onEditingStart" :on-row-removing="onRowRemoving" :on-cell-prepared="onCellPrepared">
                                <DxDataGridDxSorting mode="none" />
                                <DxDataGridToolbar>
                                    <!-- <DxFormItem location="after" template="vistoBueno" /> -->
                                    <DxFormItem name="addRowButton" />
                                    <DxFormItem name="saveButton" />
                                    <DxFormItem name="revertButton" />
                                </DxDataGridToolbar>
                                <DxDataGridSelection mode="single" />
                                <DxDataGridEditing :allow-updating="validarHabilitarTablaDetalle()" :allow-adding="validarHabilitarTablaDetalle()" :allow-deleting="validarHabilitarTablaDetalle()" mode="batch" :use-icons="true" new-row-position="last" />

                                <DxDataGridColumn width="70px" data-field="Linea" caption="Número" alignment="center" data-type="number" :allow-editing="false" />
                                <DxDataGridColumn width="100px" data-field="Cuenta" alignment="left" data-type="number" />
                                <DxDataGridColumn width="100%" data-field="NombreCuenta" caption="Nombre" alignment="left" data-type="string" :allow-editing="false" />
                                <DxDataGridColumn width="100%" data-field="Debe" alignment="right" data-type="number" :customize-text="customizeTextValores" :editor-options="editorMontos" :value-format="{ type: 'fixedPoint', precision: 2 }" css-class="textCell" />
                                <DxDataGridColumn width="100%" data-field="Haber" alignment="right" data-type="number" :customize-text="customizeTextValores" :editor-options="editorMontos" :value-format="{ type: 'fixedPoint', precision: 2 }" css-class="textCell" />
                                <DxDataGridColumn width="100px" data-field="Sucursal" alignment="center" data-type="string" :editor-options="{ maxLenght: 3 }" :value-format="{ type: 'fixedPoint', precision: 2 }" :visible="formEncabezado.Tipo != 'J'" />
                                <DxDataGridColumn width="120px" data-field="TipoDocumento" caption="Tipo" alignment="center" data-type="string" :visible="formEncabezado.Tipo == 'J'">
                                    <DxDataGridLookup :dataSource="tipoDocumento" display-expr="Valor" value-expr="Valor" :show-cancel-button="false" :show-clear-button="false" :search-enabled="false" drop-down-options="dropDownOptions" />
                                </DxDataGridColumn>
                                <DxDataGridColumn width="100px" data-field="Proveedor" alignment="center" data-type="string" :visible="formEncabezado.Tipo == 'J'" />
                                <DxDataGridColumn width="100px" data-field="Documento" alignment="center" data-type="string" :visible="formEncabezado.Tipo == 'J'" />
                                <DxDataGridColumn width="150px" data-field="FechaAplicaDocumento" caption="Fecha aplicación" alignment="center" data-type="date" format="dd/MM/yyyy" :visible="formEncabezado.Tipo == 'J'" />
                                <DxDataGridSummary :recalculate-while-editing="true">
                                    <DxDataGridTotalItem column="Debe" summary-type="sum" :customize-text="customizeTextValores" />
                                    <DxDataGridTotalItem column="Haber" summary-type="sum" :customize-text="customizeTextValores" />
                                </DxDataGridSummary>
                            </DxDataGrid>
                        </div>
                    </template>
                </DxForm>
            </div>
        </template>
    </DxForm>

    <DxPopup :visible.sync="ListaDocumentosV" :width="'30%'" height="auto" :show-title="true" :full-screen="false" :hide-on-outside-click="false" title="Documentos que no existen" :showCloseButton="true">
        <DxDataGrid :ref="gridDetalleD" v-bind="DefaultDxGridConfiguration" :data-source="ListaDocumentos" :headerFilter="{ visible: false, allowSearch: false }" :search-panel="{ visible: false }" :width="'100%'" height="auto" :allow-adding="false">
            <!-- Habilitar la edición de celdas -->
            <DxDataGridEditing mode="cell" :use-icons="true" />
            <!-- Selección de fila única -->
            <DxDataGridSelection mode="single" />
            <!-- Definir columnas editables -->
            <DxDataGridColumn width="auto" data-field="Linea" alignment="center" data-type="number" :allow-editing="false" />
            <DxDataGridColumn width="auto" data-field="TipoDocumento" caption="Tipo documento" alignment="center" data-type="string" />
            <DxDataGridColumn width="auto" data-field="Documento" alignment="center" data-type="string" :allow-editing="false" />
        </DxDataGrid>
    </DxPopup>

    <!-- Liquidar dos admisiones -->
    <DxPopup :visible.sync="liquidarAdmisiones" :width="'80%'" height="auto" :show-title="true" :full-screen="false" :hide-on-outside-click="false" title="Liquidar admisiones" :showCloseButton="true">
        <DxForm :ref="formLiquidacion" :form-data.sync="formularioLiquidacion" label-mode="floating">
            <DxFormGroupItem :col-count="2">
                <DxFormGroupItem>
                    <DxFormItem data-field="PacienteOrigen" editor-type="dxTextBox" :editor-options="{ readOnly: true }">
                        <DxFormLabel text="Paciente origen" />
                    </DxFormItem>
                    <DxFormItem data-field="PacienteDestino" editor-type="dxTextBox" :editor-options="{ readOnly: true }">
                        <DxFormLabel text="Paciente destino" />
                    </DxFormItem>
                </DxFormGroupItem>
                <DxFormGroupItem :col-count="2">
                    <DxFormItem data-field="Debe" editor-type="dxTextBox" :editor-options="{ readOnly: true }" />
                    <DxFormItem data-field="Haber" editor-type="dxTextBox" :editor-options="{ readOnly: true }" />
                    <!-- <DxFormItem :col-span="2" data-field="Remanente" editor-type="dxTextBox" :editor-options="{ readOnly: true }">
                        <DxFormLabel text="Total remanente" />
                    </DxFormItem> -->
                </DxFormGroupItem>
            </DxFormGroupItem>
        </DxForm>

        <DxDataGrid class="mt-4 my-cell-invalid" :ref="gridLiquidacion" :data-source="admisionesLiquidar" v-bind="DefaultDxGridConfiguration" :searchPanel="{visible: false}" :headerFilter="{visible:false, allowSearch:false}" :width="'100%'" height="auto" @editor-preparing="editorDatagridLiquidacion" :on-saving="Liquidar" @init-new-row="nuevaFilaLiquidacion">
            <DxDataGridDxSorting mode="none" />
            <DxDataGridSelection mode="single" />

            <DxDataGridToolbar>
                <DxFormItem name="addRowButton" />
                <DxFormItem name="saveButton" template="liquidar" />
                <DxFormItem name="revertButton" />
            </DxDataGridToolbar>

            <DxDataGridEditing :allow-updating="false" :allow-adding="true" :allow-deleting="true" mode="batch" :use-icons="true" new-row-position="last" />
            <DxDataGridColumn caption="Admisión origen" alignment="center">
                <DxDataGridColumn css-class="codigoUpper" width="150px" data-field="SerieOrigen" caption="Serie" alignment="center" data-type="string" :validation-rules="[{ type:'required', message: 'Ingrese la serie de la admisión' }]" />
                <DxDataGridColumn width="150px" data-field="AdmisionOrigen" caption="Admisión" alignment="center" data-type="string" :validation-rules="[{ type:'required', message: 'Ingrese el número de admisión' }]" />
                <DxDataGridColumn :visible="false" width="150px" data-field="NombreOrigen" caption="Admisión" alignment="center" data-type="string" />
            </DxDataGridColumn>
            <DxDataGridColumn caption="Admisión destino" alignment="center">
                <DxDataGridColumn css-class="codigoUpper" width="150px" data-field="SerieDestino" caption="Serie" alignment="center" data-type="string" :validation-rules="[{ type:'required', message: 'Ingrese la serie de la admisión' }]" />
                <DxDataGridColumn width="150px" data-field="AdmisionDestino" caption="Admisión" alignment="center" data-type="string" :validation-rules="[{ type:'required', message: 'Ingrese el número de admisión' }]" />
                <DxDataGridColumn :visible="false" width="150px" data-field="NombreDestino" caption="Admisión" alignment="center" data-type="string" />
            </DxDataGridColumn>
            <DxDataGridColumn caption="" alignment="center">
                <DxDataGridColumn width="100%" data-field="Remanente" caption="Remanente recibos" alignment="center" data-type="string" :editor-options="{ readOnly: true, tabIndex:-1 }" />
                <DxDataGridColumn width="100%" data-field="Saldo" caption="Saldo facturas" alignment="center" data-type="string" :editor-options="{ readOnly: true, tabIndex:-1 }" />
            </DxDataGridColumn>

            <template #liquidar>
                <div>
                    <DxButton :visible="true" :disabled="!habilitarLiquidar" width="100px" height="36px" type="success" styling-mode="contained" @click="ejecutarLiquidar">
                        <font-awesome-icon :icon="['fas', 'money-bill-transfer']" style="font-size: 18px; vertical-align: middle" />
                        <span class="ml-2" style="font-size: 14px !important;">Liquidar</span>
                    </DxButton>
                </div>
            </template>

            <DxDataGridSummary :recalculate-while-editing="true">
                <DxDataGridTotalItem column="Remanente" summary-type="sum" :customize-text="customizeTextValores" />
            </DxDataGridSummary>
        </DxDataGrid>
    </DxPopup>

    <!-- Devolución no gestionada -->
    <DxPopup :visible.sync="devolucionNoGenerada" :width="'80%'" height="auto" :show-title="true" :full-screen="false" :hide-on-outside-click="false" title="Devoluciones no gestionadas a pacientes" :showCloseButton="true">
        <DxForm :ref="formDevolucion" :form-data.sync="formularioDevolucion" label-mode="floating">
            <DxFormGroupItem :col-count="2">
                <DxFormItem data-field="Debe" editor-type="dxTextBox" :editor-options="{ readOnly: true }" />
                <DxFormItem data-field="Haber" editor-type="dxTextBox" :editor-options="{ readOnly: true }" />
            </DxFormGroupItem>
        </DxForm>

        <DxDataGrid class="mt-4 my-cell-invalid" :ref="gridDevolucion" :data-source="admisionesDevolucion" v-bind="DefaultDxGridConfiguration" :searchPanel="{visible: false}" :headerFilter="{visible:false, allowSearch:false}" :width="'100%'" height="auto" @editor-preparing="editorDatagridDevolucion" :on-saving="Devolucion">
            <DxDataGridDxSorting mode="none" />
            <DxDataGridSelection mode="single" />

            <DxDataGridToolbar>
                <DxFormItem name="addRowButton" />
                <DxFormItem name="saveButton" template="devolver" />
                <DxFormItem name="revertButton" />
            </DxDataGridToolbar>

            <DxDataGridEditing :allow-updating="false" :allow-adding="true" :allow-deleting="true" mode="batch" :use-icons="true" new-row-position="last" />

            <DxDataGridColumn css-class="codigoUpper" width="150px" data-field="SerieOrigen" alignment="center" data-type="string" :validation-rules="[{ type:'required', message: 'Ingrese la serie de la admisión' }]">
                <DxFormLabel text="Serie" />
            </DxDataGridColumn>
            <DxDataGridColumn width="150px" data-field="AdmisionOrigen" alignment="center" data-type="string" :validation-rules="[{ type:'required', message: 'Ingrese el número de admisión' }]">
                <DxFormLabel text="Admisión" />
            </DxDataGridColumn>
            <DxDataGridColumn width="100%" data-field="Nombre" alignment="center" data-type="string" :editor-options="{ readOnly: true }" />
            <DxDataGridColumn width="200px" data-field="Remanente" caption="Remanentes" alignment="center" data-type="string" :editor-options="{ readOnly: true }" />

            <template #devolver>
                <div>
                    <DxButton :disabled="!habilitarDevolucion" :visible="true" width="100px" height="36px" type="success" styling-mode="contained" @click="ejecutarDevolucion">
                        <font-awesome-icon :icon="['fas', 'scale-unbalanced']" style="font-size: 18px; vertical-align: middle" />
                        <span class="ml-2" style="font-size: 14px !important;">Devolver</span>
                    </DxButton>
                </div>
            </template>

            <DxDataGridSummary :recalculate-while-editing="true">
                <DxDataGridTotalItem column="Remanente" summary-type="sum" :customize-text="customizeTextValores" />
            </DxDataGridSummary>
        </DxDataGrid>
    </DxPopup>
</vx-card>
</template>

<script>
// import { readonly } from 'vue'
import 'devextreme-vue/text-area'

import {
    DefaultDxGridConfiguration
} from "./data"

// import Multiselect from 'vue-multiselect'

// import "vue-multiselect/dist/vue-multiselect.min.css"
const formConsulta = 'formConsulta'
const formEnc = 'formEncabezado'
const gridDetalle = 'gridDetalle'
const gridLiquidacion = 'gridLiquidacion'
const gridDetalleD = 'gridDetalleD'
const formLiquidacion = 'formLiquidacion'
const gridDevolucion = 'gridDevolucion'
const formDevolucion = 'formDevolucion'

export default {
    components: {
        // Multiselect,

    },
    data() {
        return {
            DefaultDxGridConfiguration,
            formConsulta,
            gridDetalle,
            gridLiquidacion,
            gridDetalleD,
            formLiquidacion,
            gridDevolucion,
            formDevolucion,
            formEnc,

            formulario: {
                Periodo: null
            },
            periodoActual: null,
            formEncabezado: {
                CodPartida: null,
                Generada: false,
                Cuenta: null,
                Tipo: null,
            },

            tipoOpciones: [{
                Valor: 'A',
                Tipo: 'Apertura'
            }, {
                Valor: 'N',
                Tipo: 'Normal'
            }, {
                Valor: 'J',
                Tipo: 'Ajuste'
            }, {
                Valor: 'C',
                Tipo: 'Cierre'
            }, {
                Valor: 'P',
                Tipo: 'Provisión'
            }],

            valoresTabla: [],
            editorMontos: {
                min: 0,
                format: '#0.##',
                step: 0.01
            },
            editarEncabezado: false,
            encabezadoPartida: {}, //Esto guardará la información del encabezado por si se cancela la edición del encabezado
            tempEncabezado: {}, //Esto sirve para guardar la información del encabezado cuando se crea una nueva partida
            Corporativo: null,

            cuentasAjuste: [],

            buttonLiquidar: {
                width: 'auto',
                icon: 'fas fa-file-invoice-dollar',
                text: 'Liquidar dos admisiones',
                type: 'success',
                onClick: () => {
                    this.CuentasLiq(1)
                    this.formularioLiquidacion = {}
                    this.admisionesLiquidar = []
                    this.gridLiquidacionInstance.cancelEditData()
                    this.liquidarAdmisiones = true
                },
                useSubmitBehavior: false,
            },

            buttonDevolucion: {
                width: 'auto',
                icon: 'fas fa-arrow-left',
                text: 'Devolución NO Gestionada',
                type: 'success',
                onClick: () => {
                    this.CuentasLiq(0)
                    this.formularioDevolucion = {}
                    this.admisionesDevolucion = []
                    this.gridDevolucionInstance.cancelEditData()
                    this.devolucionNoGenerada = true
                },
                useSubmitBehavior: false,
            },

            permisoMayorizar: false,

            liquidarAdmisiones: false,
            devolucionNoGenerada: false,

            admisionesLiquidar: [],
            admisionesDevolucion: [],

            inicioMes: null,
            finMes: null,

            TipoBitacora: '-',

            crearPartida: false,

            ListaDocumentos: [],
            ListaDocumentosV: false,

            tipoDocumento: [{
                    Valor: 'F'
                },
                {
                    Valor: 'R'
                },
                {
                    Valor: 'FC'
                }
            ],

            dropDownOptions: {
                showTitle: false,
                hideOnOutsideClick: true,
            },

            formularioLiquidacion: {},
            formularioDevolucion: {},

            otroPeriodo: false, //Variable para indicar si se está generando una partida nueva mientras estaba viendo otro periodo y manejar las fechas del datebox

            habilitarEdicionTabla: false,

            habilitarLiquidar: false,
            habilitarDevolucion: false,

            bloquearNavegacion: false,

            contieneLDA_DNG: false,

            //------------------------------

            cbPeriodos: '',
            ListaPeriodos: [],
            CodigoPeriodo: '',
            DescPeriodo: '',

            ListaPartidas: [],
            CodigoPartida: '',

            ListaAjustes: [],
            DetalleAjuste: false,
            CuentaIntegracion: '',

            Fecha: '',
            PartidasLineas: [],
            CountLineas: '',

            Status: '',
            DescripcionM: '',
            DescripcionNoTodas: false,
            Liquidacion: false,
            revisar_siguientes: 'F',
            NuevaPartidaContable: false
        }
    },
    beforeMount() {
        this.ConsultaPeridoActual()
        this.ConsultaPeriodo()
        this.CuentasAjuste()
        this.Corporativo = this.sesion.corporativo

        this.$validar_funcionalidad('/CONTABILIDAD/CON008', 'MAYORIZAR', (d) => {
            this.permisoMayorizar = d.status
        })
    },
    watch: {
        Tipo(value) {
            if (value === 'J') {
                this.CuentasAjuste()
                this.DetalleAjuste = true
            } else {
                this.DetalleAjuste = false
            }
        },
    },
    methods: {
        validarHabilitarTablaDetalle() {
            const noGenerada = this.formEncabezado.Generada !== 'S';
            const noEnMantenimiento = this.formEncabezado.Status !== 'M';
            const tipoNoEsJ = this.formEncabezado.Tipo !== 'J';
            const tipoJsindatosLDA_GND = this.formEncabezado.Tipo === 'J' && !this.contieneLDA_DNG;

            return (
                !this.crearPartida &&
                !this.editarEncabezado &&
                this.formEncabezado.CodPartida > 0 &&
                noGenerada &&
                noEnMantenimiento &&
                (tipoNoEsJ || tipoJsindatosLDA_GND)
            );
        },
        handleDescripcionBlur() {
            if (this.crearPartida) {
                this.GuardarPartida()
            }
        },
        //PERIODOS
        async ConsultaPeriodo() {
            const url = this.$store.state.global.url
            await this.axios.post(url + 'app/v1_contabilidad_general/ConsultaPeriodo', {
                    Opcion: "C",
                    SubOpcion: "6",
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaPeriodos = resp.data.json;
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido cargar los periodos, favor validar.',
                        })
                    }
                })
                .catch(() => {})
        },
        async ConsultaPeridoActual() {
            const url = this.$store.state.global.url
            await this.axios.post(url + 'app/v1_contabilidad_general/ConsultaPeriodoActual', {
                    Opcion: 'C',
                    SubOpcion: '2',
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.periodoActual = resp.data.json[0]
                        this.formulario.Periodo = this.periodoActual.CodigoPeriodo
                        this.formulario.SigPartida = this.periodoActual.SigPartida
                        this.inicioMes = new Date(this.periodoActual.FechaInicial + 'T00:00:00')
                        this.finMes = new Date(this.periodoActual.FechaFinal + 'T23:59:59')
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Auxiliar Bancos',
                            text: 'No se ha podido cargar los periodos, favor validar.',
                        })
                    }
                })
        },

        AsignarPeriodo(e) {
            if (e.selectedItem && !this.otroPeriodo) {
                this.otroPeriodo = false
                this.formulario.Periodo = e.selectedItem.CodigoPeriodo
                this.formulario.SigPartida = e.selectedItem.CorrelativoPartidas
                this.inicioMes = new Date(e.selectedItem.FechaInicial + 'T00:00:00')
                this.finMes = new Date(e.selectedItem.FechaFinal + 'T23:59:59')
                this.editarEncabezado = false
                this.ConsultaPartidas(0, 1, 1)
                this.Mayorizacion()
                // if (this.crearPartida) {
                //     this.crearPartida = false
                // }
            } else {
                this.formulario.Periodo = this.periodoActual.CodigoPeriodo
                this.formulario.SigPartida = this.periodoActual.SigPartida

                this.inicioMes = new Date(e.selectedItem.FechaInicial + 'T00:00:00')
                this.finMes = new Date(e.selectedItem.FechaFinal + 'T23:59:59')
                this.editarEncabezado = false
                this.otroPeriodo = false
                // if (this.crearPartida) {
                //     this.crearPartida = false
                // }
            }
        },

        SeleccionarCuentaAjuste(e) {
            this.formEncabezado.Cuenta = e.selectedItem.CodigoAj
        },

        PeriodoSeleccionado({
            CodigoPeriodo,
            DescPeriodo
        }) {
            return `${CodigoPeriodo} - ${DescPeriodo} `
        },
        //Verifica si el periodo tiene partidas sin mayorizar
        Mayorizacion() {
            this.DescripcionM = ''
            this.DescripcionNoTodas = false
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/Mayorizacion_SM', {
                    Opcion: 'C',
                    SubOpcion: '1',
                    Periodo: this.formulario.Periodo
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.DescripcionM = resp.data.json[0].DescripcionM
                        this.PartidasSinMayorizar()
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido Mayorizar, favor validar.',
                        })
                    }
                })
                .catch(() => {})
        },
        //PARTIDAS
        // accion -> 1 = eliminar / 2 -> nueva partida
        async ConsultaPartidas(partida = 0, direccion = 1, seleccionPeriodo = 0) { // seleccionPeriodo sirve para manejar cuando sí se debe limpiar la información del formulario
            const url = this.$store.state.global.url

            // Este if hace que la navegación se mantenga en la misma partida si está descuadrada
            if (this.bloquearNavegacion) {
                partida = this.formEncabezado.CodPartida
                direccion = 0
            }

            await this.axios.post(url + 'app/v1_contabilidad_general/Partidas', {
                    Opcion: 'C',
                    SubOpcion: '2',
                    Periodo: this.formulario.Periodo,
                    Partida: partida,
                    Direccion: direccion
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        // this.LimpiarPartidas()
                        this.DescripcionNoTodas = false

                        this.formEncabezado = resp.data.json[0]
                        this.formEncabezado.Generada = this.formEncabezado.Generada === 'S' ? true : false
                        this.ConsultaPartidasLineas()
                        this.dataGridDetalle.repaint()
                    }

                    if (resp.data.json.length == 0 && seleccionPeriodo == 1) {
                        this.LimpiarPartidas()
                    }
                })
                .catch(() => {})

        },

        LimpiarPartidas() {
            this.formEncabezado = {
                CodigoPartida: null,
                Generada: false,
                Cuenta: null,
            }
            this.valoresTabla = []
            this.dataGridDetalle.repaint()

            this.Fecha = ''
            this.Status = ''
            this.DescripcionM = ''
            this.DescripcionNoTodas = false
        },

        BuscarPartida() {

        },
        //LÍNES GRID
        ConsultaPartidasLineas() {
            this.valoresTabla = []
            this.axios.post('/app/v1_contabilidad_general/Lineas', {
                    Opcion: 'C',
                    SubOpcion: '4',
                    Periodo: this.formulario.Periodo,
                    Partida: this.formEncabezado.CodPartida
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.valoresTabla = resp.data.json.map((x) => {
                            x.Debe = parseFloat(x.Debe).toFixed(2)
                            x.Haber = parseFloat(x.Haber).toFixed(2)
                            return x
                        })
                        if (this.formEncabezado.Status === 'P') {
                            this.VerificaCuadreEncQuery()
                        }
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Partidas',
                            text: 'No se ha podido cargar los PartidasLineas, favor validar.',
                        })
                    }
                })
                .catch(() => {})
        },
        // Bitacora() {
        /* Borrar(){
             const url = this.$store.state.global.url
             this.axios.post(url + 'app/v1_contabilidad_general/Mayorizacion_Partidas_SM', {
                     Opcion: "C",
                     SubOpcion: "1",
                 })
                 .then(resp => {
                     if (resp.data.codigo == 0) {
                         this.ListaAjustes = resp.data.json;
                     } else {
                         this.$vs.notify({
                             color: '#B71C1C',
                             title: 'Cuentas',
                             text: 'No se ha podido Mayorizar, favor validar.',
                         })
                     }
                 })
                 .catch(() => {})
         },*/
        // SiguienteLinea() {

        // },

        onCellPrepared(e) {
            if (e.rowType === "header") {
                e.cellElement.style.textAlign = "center";
            }
        },

        VerificaCuadreEncQuery() {
            this.axios.post('/app/v1_contabilidad_general/Lineas', {
                    Opcion: 'C',
                    SubOpcion: '10',
                    Periodo: this.formulario.Periodo,
                    Partida: this.formEncabezado.CodPartida
                })
                .then(resp => {
                    if (resp.data.json.length > 0) { //Si trae información es porque existe un descuadre
                        this.bloquearNavegacion = true
                        this.$vs.dialog({
                            type: 'confirm',
                            color: 'danger',
                            title: 'Partida descuadrada',
                            text: 'Su partida está descuadrada, ¿desea eliminarla?',
                            acceptText: 'Aceptar',
                            cancelText: 'Cancelar',
                            accept: () => {
                                this.TipoBitacora = 'D'
                                this.BorrarPartidas()
                            }
                        })
                    } else {
                        //VerificarCuadre PartidasLineas
                        const totalDebe = parseFloat(this.dataGridDetalle.getTotalSummaryValue("Debe")).toFixed(2);
                        const totalHaber = parseFloat(this.dataGridDetalle.getTotalSummaryValue("Haber")).toFixed(2);

                        if (Math.abs(totalDebe - totalHaber) !== 0) {
                            this.bloquearNavegacion = true
                            this.$vs.dialog({
                                type: 'confirm',
                                color: 'danger',
                                title: 'Partida descuadrada',
                                text: 'El detalle de su partida está descuadrada, desea eliminarla?',
                                acceptText: 'Aceptar',
                                cancelText: 'Cancelar',
                                accept: () => {
                                    //Eliminada por descuadre por --Indicar por descudre
                                    this.TipoBitacora = 'D'
                                    this.BorrarPartidas()
                                }
                            })
                        } else {
                            this.bloquearNavegacion = false
                            if (this.formEncabezado.Tipo == 'J') {
                                //Verifica que las partidas de ajuste que tengan cta afectada en el encabezado
                                //Todas las lineas que utilizan esa cuenta tengan documento
                                if ((this.formEncabezado.Cuenta != '' || this.formEncabezado.Cuenta != null) && this.valoresTabla.length > 0) {
                                    this.VerificaAjustes()
                                }

                            }
                        }
                    }
                })
                .catch(() => {})
        },

        VerificaAjustes() {
            // VerificaAjustesSinDocumento            
            this.axios.post('/app/v1_contabilidad_general/Lineas', {
                    Opcion: 'C',
                    SubOpcion: '11',
                    Periodo: this.formulario.Periodo,
                    Partida: this.formEncabezado.CodPartida,
                    CuentaInt: this.formEncabezado.Cuenta == '' || this.formEncabezado.Cuenta == null ? null : this.formEncabezado.Cuenta
                })
                .then((resp) => {
                    if (resp.data.json.length > 0) {
                        if (resp.data.json[0].Cuenta == 1) {
                            this.$vs.dialog({
                                type: 'alert',
                                color: 'danger',
                                title: 'Partida descuadrada',
                                text: 'La línea ' + resp.data.json[0].Linea + ' no tiene número de documento',
                                acceptText: 'Aceptar',
                                accept: () => {}
                            })
                            return
                        } else if (resp.data.json[0].Cuenta > 1) {
                            this.$vs.dialog({
                                type: 'alert',
                                color: 'danger',
                                title: 'Partida descuadrada',
                                text: 'Hay ' + resp.data.json[0].Cuenta + ' líneas que no tienen documento, empezando por la línea ' + resp.data.json[0].Linea,
                                acceptText: 'Aceptar',
                                accept: () => {}
                            })
                            return
                        }
                    } else {
                        //qVerificaSiEsAjusteLDA_DNG  
                        this.VerificaAjusteLDA_DNG()
                    }
                })
        },

        PartidasSinMayorizar() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/Mayorizacion_SM', {
                    Opcion: 'C',
                    SubOpcion: '7',
                    Periodo: this.formulario.Periodo
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.DescripcionNoTodas = true
                    }
                })
                .catch(() => {})
        },
        //AJUSTE
        CuentasAjuste() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/CuentasAjuste', {
                    Opcion: "C",
                    SubOpcion: "8"
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.cuentasAjuste = resp.data.json;
                    }
                })
                .catch(() => {})
        },
        onChangeAjustes(value) {
            if (value !== null && value.length !== 0) {
                this.CodigoAjuste = value.CodigoAj;
                this.DescAjuste = value.NombreAj

            } else {
                this.CodigoAjuste = '';
                this.DescAjuste = ''
            }
        },
        AjusteSeleccionado({
            CodigoAj,
            NombreAj
        }) {
            return `${CodigoAj} - ${NombreAj} `
        },
        //EliminaPartidaEnInventarioCargos
        async BorrarPartidas() {
            await this.axios.post('/app/v1_contabilidad_general/TransPartidas', {
                    Opcion: 'E',
                    SubOpcion: '1',
                    Periodo: this.formulario.Periodo,
                    Partida: this.formEncabezado.CodPartida,
                    TipoBitacora: this.TipoBitacora
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        if (this.formEncabezado.CodPartida == parseInt(this.formulario.SigPartida - 1)) {
                            this.ActPeriodo(true)
                        }
                        this.NuevaPartidaContable = false
                        this.bloquearNavegacion = false
                        this.ConsultaPartidas(this.formEncabezado.CodPartida, -1, 1)
                    }
                })
        },

        // async EliminarPartidaInventariosCargos() {
        //     await this.axios.post('/app/v1_contabilidad_general/TransPartidas', {
        //             Opcion: 'U',
        //             SubOpcion: '1',
        //             Periodo: this.formulario.Periodo,
        //             Partida: this.formEncabezado.CodPartida,
        //             _NOTIFICACIONES: false
        //         })
        //         .then(resp => {
        //             if (resp.data.codigo == 0) {
        //                 this.UpdateRecibosFacturas()
        //                 this.ActBitacora()
        //             }
        //         })
        // },

        // async UpdateRecibosFacturas() {
        //     await this.axios.post('/app/v1_contabilidad_general/TransPartidas', {
        //             Opcion: 'U',
        //             SubOpcion: '2',
        //             Periodo: this.formulario.Periodo,
        //             Partida: this.formEncabezado.CodPartida,
        //             _NOTIFICACIONES: false
        //         })
        //         .then(resp => {
        //             if (resp.data.codigo == 0) {
        //                 this.BorrarPartidas()
        //             }
        //         })
        //         .catch(() => {})
        // },
        NuevaLinea(e) {
            const allRows = e.component.getVisibleRows();
            const currentLines = allRows
                .map(r => r.data.Linea)
                .filter(l => l != null)
                .sort((a, b) => b - a);

            const maxLinea = currentLines.length > 0 ? parseInt(currentLines[0]) : 0;
            const siguienteLinea = maxLinea + 1;

            // Asignar el siguiente número correlativo
            e.data.Linea = siguienteLinea;

            e.data.TipoDocumento = 'F'
        },
        CheckActividad() {
            this.axios.post('/app/v1_contabilidad_general/CheckAct_SaldoInicial', {
                    Opcion: 'C',
                    SubOpcion: '9',
                    Periodo: this.formulario.Periodo
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        let Cuantos = resp.data.json[0].Cuantos;

                        if (Cuantos > 0) {
                            this.$vs.dialog({
                                type: 'confirm',
                                color: '#ed8c72',
                                title: 'Periodo cerrado',
                                text: 'La partida pertenece a un periodo cerrado. Si continúa se modificaran los saldos posteriores.',
                                buttonCancel: 'border',
                                acceptText: 'Sí',
                                cancelText: 'No',
                                accept: () => {
                                    this.revisar_siguientes = 'T'

                                    if (this.formEncabezado.Status == 'P') {
                                        this.ProcesoLineasEdicionP()
                                    } else {
                                        this.ProcesoLineasEdicionM()
                                    }
                                },
                                cancel: () => {
                                    return
                                }
                            })
                            return
                        } else {
                            if (this.formEncabezado.Status == 'P') {
                                this.ProcesoLineasEdicionP()
                            } else {
                                this.ProcesoLineasEdicionM()
                            }
                        }
                    }
                })
                .catch(() => {})
        },
        ProcesoLineasEdicionP() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/LineasEdicion', {
                    Opcion: 'U',
                    SubOpcion: '3',
                    Periodo: this.formulario.Periodo,
                    Partida: this.formEncabezado.CodPartida,
                    TipoPartida: this.formEncabezado.Tipo
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        //Actuliza Partidas
                        this.ConsultaPartidas(this.formEncabezado.CodPartida, 0)
                        this.Mayorizacion()
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Partidas',
                            text: 'No se ha podido Mayorizar, favor validar.',
                        })
                    }
                })
                .catch(() => {})
        },
        ProcesoLineasEdicionM() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/LineasEdicion', {
                    Opcion: 'U',
                    SubOpcion: '4',
                    Revisar: this.revisar_siguientes,
                    Periodo: this.formulario.Periodo,
                    Partida: this.formEncabezado.CodPartida,
                    TipoPartida: this.formEncabezado.Tipo
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        //Actuliza Partidas
                        this.ConsultaPartidas(this.formEncabezado.CodPartida, 0)
                        this.Mayorizacion()
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Partidas',
                            text: 'No se ha podido Des-Mayorizar, favor validar.',
                        })
                    }
                })
                .catch(() => {})
        },

        async ActualizarEncabezado() {
            if (this.formEncabezadoInstance) {
                const result = this.formEncabezadoInstance.validate(); // Dispara validación

                if (result.isValid) {
                    await this.axios.post('/app/v1_contabilidad_general/TransPartidas', {
                            Opcion: 'U',
                            SubOpcion: '5',
                            Periodo: this.formEncabezado.Periodo,
                            Partida: this.formEncabezado.CodPartida,
                            Fecha: this.formEncabezado.Fecha,
                            Referencia: this.formEncabezado.Referencia,
                            Descripcion: this.formEncabezado.Descripcion,
                            TipoPartida: this.formEncabezado.Tipo,
                            CuentaInt: this.formEncabezado.CodigoAj,
                        })
                        .then((resp) => {
                            if (resp.data.codigo == 0) {
                                this.TipoBitacora = 'M'
                                this.ActBitacora()
                                this.ConsultaPartidas(this.formEncabezado.CodPartida, 0)
                                this.editarEncabezado = false
                            }
                        })
                } else {
                    this.$vs.dialog({
                        type: 'alert',
                        color: 'danger',
                        title: 'Información incompleta',
                        text: 'Faltan información para la partida',
                        acceptText: 'Aceptar',
                        accept: () => {}
                    })
                    return
                }
            }
        },
        //LIQUIDA ADMISIONES
        /*LiquidaAdmisiones() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/Mayorizacion_Partidas_SM', {
                    Opcion: "C",
                    SubOpcion: "1",
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaAjustes = resp.data.json;
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido Mayorizar, favor validar.',
                        })
                    }
                })
                .catch(() => {})
        },*/

        //DEVOLUCIÓN NO GESTIONADA
        //qAjusteLDA_SigLinea
        CuentaAjusteLDA() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/qAjusteLDA_SigLinea', {
                    Opcion: 'C',
                    SubOpcion: '8',
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaAjustes = resp.data.json;
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido cargar Las cuentas de integración, favor validar.',
                        })
                    }
                })
                .catch(() => {})
        },

        editorDatagrid(e) {
            if (e.parentType === 'dataRow' && e.dataField === 'Cuenta') {
                const defaultValueChangeHandler = e.editorOptions.onValueChanged;

                e.editorOptions.onValueChanged = async (args) => {
                    const cuenta = args.value; // Captura el valor ingresado de la cuenta
                    await this.axios.post('/app/v1_contabilidad_general/ConsultasCuentas', {
                            Opcion: 'C',
                            SubOpcion: '11',
                            CodigoCuenta: cuenta,
                        })
                        .then(async (resp) => {
                            if (resp.data.json.length > 0) {
                                const updatedNombreCuenta = resp.data.json[0].NombreCuenta;
                                const rowIndex = e.row.rowIndex;

                                if (updatedNombreCuenta != '') {
                                    // Actualizamos la celda de "NombreCuenta"
                                    await this.$refs.gridDetalle.instance.cellValue(
                                        rowIndex,
                                        "NombreCuenta",
                                        updatedNombreCuenta
                                    );
                                }
                            } else {
                                this.$vs.dialog({
                                    type: 'alert',
                                    color: '#ed8c72',
                                    title: 'Cuenta no encontrada',
                                    acceptText: 'Aceptar',
                                    text: 'El número de cuenta ingresado no se encontró.',
                                    buttonCancel: 'border',
                                    accept: () => {

                                    },
                                })
                                const rowIndex = e.row.rowIndex;

                                this.$refs.gridDetalle.instance.cellValue(
                                    rowIndex,
                                    "NombreCuenta",
                                    null
                                );

                                this.$refs.gridDetalle.instance.cellValue(
                                    rowIndex,
                                    "Cuenta",
                                    null
                                );
                            }
                            // Actualizamos la fila para reflejar los cambios
                            // setTimeout(() => {
                            //     this.$refs.gridDetalle.instance.refresh();
                            // }, 100);
                            defaultValueChangeHandler(args);
                        })
                }
            }

            if (e.parentType === 'dataRow' && (e.dataField === 'NombreCuenta' || e.dataField === 'NombreCuenta')) {
                e.editorOptions.disabled = true
            }
        },

        editorDatagridLiquidacion(e) {
            if (e.parentType === 'dataRow') {

                this.formularioLiquidacion.PacienteOrigen = e.row.data.NombreOrigen
                this.formularioLiquidacion.PacienteDestino = e.row.data.NombreDestino

                this.formLiquidacionInstance.repaint()

                if (e.dataField === 'SerieOrigen' || e.dataField === 'SerieDestino') {
                    e.editorOptions.maxLength = 1;
                    e.editorOptions.onKeyDown = this.onlyLeters
                    // e.editorElement.classList.add('codigoUpper')
                    const defaultValueChangeHandler = e.editorOptions.onValueChanged;
                    e.editorOptions.onValueChanged = async (args) => {
                        args.value = args.value.toUpperCase();
                        defaultValueChangeHandler(args);
                    }
                }

                if (e.dataField === 'AdmisionOrigen') {
                    const defaultValueChangeHandler = e.editorOptions.onValueChanged;

                    e.editorOptions.onValueChanged = async (args) => {
                        const Admision = args.value
                        defaultValueChangeHandler(args);

                        const Remanente = await this.Buscar(e.row.data.SerieOrigen, Admision, '3', 1)
                        const rowIndex = e.row.rowIndex

                        if (Remanente > 0) {
                            //Actualiza la celda Remanente
                            this.$refs.gridLiquidacion.instance.cellValue(
                                rowIndex,
                                "Remanente",
                                Remanente
                            );
                            let nombre = await this.BuscarPaciente(e.row.data.SerieOrigen, Admision, '3')

                            this.$refs.gridLiquidacion.instance.cellValue(
                                rowIndex,
                                "NombreOrigen",
                                nombre
                            );
                        } else {
                            this.$refs.gridLiquidacion.instance.cellValue(
                                rowIndex,
                                "Remanente",
                                null
                            );
                            this.$refs.gridLiquidacion.instance.cellValue(
                                rowIndex,
                                "AdmisionOrigen",
                                null
                            );
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#ed8c72',
                                title: 'Admisión no válida',
                                acceptText: 'Aceptar',
                                text: 'El remanente de recibos debe ser mayor a cero.',
                                buttonCancel: 'border',
                                accept: () => {},
                            })
                            this.habilitarLiquidar = false
                        }
                        // this.$refs.gridLiquidacion.instance.refresh();
                    }
                }

                if (e.dataField === 'AdmisionDestino') {
                    const defaultValueChangeHandler = e.editorOptions.onValueChanged;

                    e.editorOptions.onValueChanged = async (args) => {
                        const Admision = args.value
                        defaultValueChangeHandler(args);

                        const Saldo = await this.Buscar(e.row.data.SerieDestino, Admision, '4')
                        const rowIndex = e.row.rowIndex

                        if ((e.row.data.SerieDestino == e.row.data.SerieOrigen) && (Admision == e.row.data.AdmisionOrigen)) {
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#ed8c72',
                                title: 'Admisión no válida',
                                acceptText: 'Aceptar',
                                text: 'Las admisiones deben ser distintas.',
                                buttonCancel: 'border',
                                accept: () => {},
                            })
                            this.habilitarLiquidar = false
                            return
                        }

                        if (Saldo > 0) {
                            //Actualiza la celda Remanente
                            this.$refs.gridLiquidacion.instance.cellValue(
                                rowIndex,
                                "Saldo",
                                Saldo
                            );
                            let nombre = await this.BuscarPaciente(e.row.data.SerieDestino, Admision, '4')

                            this.$refs.gridLiquidacion.instance.cellValue(
                                rowIndex,
                                "NombreDestino",
                                nombre
                            );

                            if (parseFloat(e.row.data.Remanente) > parseFloat(Saldo)) {
                                this.$vs.dialog({
                                    type: 'alert',
                                    color: '#ed8c72',
                                    title: 'Admisión no válida',
                                    acceptText: 'Aceptar',
                                    text: 'El saldo de facturas es menor que remanentes.',
                                    buttonCancel: 'border',
                                    accept: () => {},
                                })
                                this.habilitarLiquidar = false
                                return
                            }

                            this.formularioLiquidacion.Remanente = parseFloat(this.gridLiquidacionInstance.getTotalSummaryValue("Remanente")).toFixed(2);

                            this.habilitarLiquidar = true
                        } else {
                            this.$refs.gridLiquidacion.instance.cellValue(
                                rowIndex,
                                "Saldo",
                                null
                            );

                            this.$refs.gridLiquidacion.instance.cellValue(
                                rowIndex,
                                "AdmisionDestino",
                                null
                            );
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#ed8c72',
                                title: 'Admisión no válida',
                                acceptText: 'Aceptar',
                                text: 'El saldo de las facturas debe ser mayor a cero.',
                                buttonCancel: 'border',
                                accept: () => {},
                            })
                            this.habilitarLiquidar = false
                        }
                        this.$refs.gridLiquidacion.instance.refresh();
                    }
                }
            }
        },

        editorDatagridDevolucion(e) {
            if (e.parentType === 'dataRow') {

                if (e.dataField === 'SerieOrigen') {
                    e.editorOptions.maxLength = 1;
                    e.editorOptions.onKeyDown = this.onlyLeters
                    // e.editorElement.classList.add('codigoUpper')

                    const defaultValueChangeHandler = e.editorOptions.onValueChanged;
                    e.editorOptions.onValueChanged = async (args) => {
                        args.value = args.value.toUpperCase();
                        defaultValueChangeHandler(args);
                    }
                }

                if (e.dataField === 'AdmisionOrigen') {
                    const defaultValueChangeHandler = e.editorOptions.onValueChanged;

                    e.editorOptions.onValueChanged = async (args) => {
                        const Admision = args.value
                        defaultValueChangeHandler(args);

                        const Remanente = await this.Buscar(e.row.data.SerieOrigen, Admision, '3')
                        const rowIndex = e.row.rowIndex

                        if (Remanente > 0) {
                            //Actualiza la celda Remanente
                            this.$refs.gridDevolucion.instance.cellValue(
                                rowIndex,
                                "Remanente",
                                Remanente
                            );
                            let nombre = await this.BuscarPaciente(e.row.data.SerieOrigen, Admision, '3')

                            this.$refs.gridDevolucion.instance.cellValue(
                                rowIndex,
                                "Nombre",
                                nombre
                            );

                            this.habilitarDevolucion = true
                        } else {
                            this.$refs.gridDevolucion.instance.cellValue(
                                rowIndex,
                                "Remanente",
                                null
                            );
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#ed8c72',
                                title: 'Admisión no válida',
                                acceptText: 'Aceptar',
                                text: 'El remanente de recibos debe ser mayor a cero.',
                                buttonCancel: 'border',
                                accept: () => {},
                            })
                            this.habilitarDevolucion = false
                        }
                        // this.$refs.gridLiquidacion.instance.refresh();
                    }
                }
            }
        },

        async Buscar(Serie, Admision, SubOpcion) {
            return await this.axios.post('/app/v1_contabilidad_general/LiquidaAdmisionesC', {
                    Opcion: 'C',
                    SubOpcion: SubOpcion,
                    SerieA: Serie,
                    Admision: Admision
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        if (SubOpcion == '3') {

                            return parseFloat(resp.data.json.reduce((x, y) => {
                                return x + parseFloat(y.Remanente)
                            }, 0)).toFixed(2)
                        } else {
                            return parseFloat(resp.data.json.reduce((x, y) => {
                                return x + parseFloat(y.Saldo)
                            }, 0)).toFixed(2)
                        }
                    } else {
                        return 0
                    }
                })
        },

        async BuscarPaciente(Serie, Admision, Adm) {
            return await this.axios.post('/app/v1_contabilidad_general/LiquidaAdmisionesC', {
                    Opcion: 'C',
                    SubOpcion: '2',
                    SerieA: Serie,
                    Admision: Admision
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        if (Adm == '3') {
                            this.formularioLiquidacion.PacienteOrigen = resp.data.json[0].NombrePac
                        } else {
                            this.formularioLiquidacion.PacienteDestino = resp.data.json[0].NombrePac
                        }
                        this.formLiquidacionInstance.repaint()
                        return resp.data.json[0].NombrePac

                    } else {
                        return 0
                    }
                })
        },

        async GuardaCambiosTabla(e) {
            let lineas = {
                Agregados: [],
                Eliminados: [],
                Modificados: [],
                Partida: {
                    Opcion: 'U',
                    Subopcion: '7',
                    Partida: this.formEncabezado.CodPartida,
                    Periodo: this.formulario.Periodo,
                    Debe: this.dataGridDetalle.getTotalSummaryValue("Debe"),
                    Haber: this.dataGridDetalle.getTotalSummaryValue("Haber")
                }
            }

            let valid = true;

            if (e.changes.length > 0) {
                for (let i = 0; i < e.changes.length; i++) {
                    const change = e.changes[i];
                    let row = {}
                    if (change.type === 'insert') {
                        row = {
                            ...(change.data || {})
                        };
                        row.FechaAplicaDocumento = row.FechaAplicaDocumento == null ? null : new Date(row.FechaAplicaDocumento)
                    } else if (change.type === 'update') {
                        row = {
                            ...(change.key || {}),
                            ...(change.data || {})
                        };
                    } else if (change.type === 'remove') {
                        row = {
                            ...(change.key || {})
                        };
                    }

                    const debe = Number(row.Debe) || 0;
                    const haber = Number(row.Haber) || 0;

                    if (debe === 0 && haber === 0 && change.type !== 'remove') {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Valores incorrectos',
                            acceptText: 'Aceptar',
                            text: 'No puede grabar si alguna línea cuenta con Debe y Haber nulos o cero.',
                            buttonCancel: 'border',
                            accept: () => {},
                        })

                        e.cancel = true;
                        valid = false;
                        break;
                    } else if ((debe < 0 || haber < 0) && change.type !== 'remove') {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Valores incorrectos',
                            acceptText: 'Aceptar',
                            text: 'No puede grabar si alguna línea cuenta con monto menor a 0 en Debe o Haber.',
                            buttonCancel: 'border',
                            accept: () => {},
                        })

                        e.cancel = true;
                        valid = false;
                        break;
                    } else if (debe > 0 && haber > 0 && change.type !== 'remove') {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Valores incorrectos',
                            acceptText: 'Aceptar',
                            text: 'Solo uno de los campos Debe o Haber debe tener valor mayor a 0. No puede llenar ambos.',
                            buttonCancel: 'border',
                            accept: () => {},
                        });

                        e.cancel = true;
                        valid = false;
                        break;
                    } else if (row.Cuenta == '' || row.Cuenta == null || row.NombreCuenta == '' || row.NombreCuenta == null) {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Cuenta no válida',
                            acceptText: 'Aceptar',
                            text: 'Debe ingresar una cuenta válida.',
                            buttonCancel: 'border',
                            accept: () => {},
                        })

                        e.cancel = true;
                        valid = false;
                        break;
                    } else if (this.formEncabezado.Tipo == 'J' && this.formEncabezado.Cuenta == row.Cuenta && (change.type === 'insert' || change.type === 'update')) {
                        if (row.FechaAplicaDocumento == null) {
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#ed8c72',
                                title: 'Fecha aplicación',
                                acceptText: 'Aceptar',
                                text: 'No puede continuar sin seleccionar una fecha de aplicación.',
                                buttonCancel: 'border',
                                accept: () => {},

                            })

                            e.cancel = true;
                            valid = false;
                            break;
                        }

                        if (row.Documento == null) {
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#ed8c72',
                                title: 'Documento',
                                acceptText: 'Aceptar',
                                text: 'No puede continuar sin ingresar el documento.',
                                buttonCancel: 'border',
                                accept: () => {},

                            })

                            e.cancel = true;
                            valid = false;
                            break;
                        }

                        if (row.TipoDocumento == 'FC' && (row.Proveedor == null || row.Proveedor == '')) {
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#ed8c72',
                                title: 'Proveedor',
                                acceptText: 'Aceptar',
                                text: 'No puede continuar sin código de proveedor.',
                                buttonCancel: 'border',
                                accept: () => {},

                            })

                            e.cancel = true;
                            valid = false;
                            break;
                        }

                        if (row.TipoDocumento != 'FC' && (row.Proveedor != null && row.Proveedor != '')) {
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#ed8c72',
                                title: 'Proveedor',
                                acceptText: 'Aceptar',
                                text: 'No debe agregar código de proveedor si el documento no es \'FC\'.',
                                buttonCancel: 'border',
                                accept: () => {},

                            })

                            e.cancel = true;
                            valid = false;
                            break;
                        }
                    } else {
                        // Aquí se almacena cada una de las acciones que se realizarán en la tabla de auxiliares
                        let insertar = {
                            Periodo: this.formulario.Periodo,
                            Partida: this.formEncabezado.CodPartida,
                        };
                        if (e.changes[i].type == 'insert') {
                            insertar = {
                                ...row,
                                Opcion: 'I',
                                Subopcion: '2',
                                ...insertar,
                            };
                            insertar.TipoPartida = this.formEncabezado.Tipo
                            lineas.Agregados.push(insertar);
                        } else if (e.changes[i].type == 'update') {
                            insertar = {
                                ...row,
                                Opcion: 'U',
                                Subopcion: '6',
                                ...insertar
                            };

                            // Recorrer las columnas del 'data' y actualizar el 'key' con los nuevos valores
                            for (const field in row) {
                                if (Object.prototype.hasOwnProperty.call(row, field)) {
                                    insertar[field] = row[field]; // Actualiza el campo en 'key' con el valor de 'data'
                                }
                            }

                            insertar.TipoPartida = this.formEncabezado.Tipo

                            lineas.Modificados.push(insertar);
                        } else if (e.changes[i].type == 'remove') {
                            insertar = {
                                ...row,
                                Opcion: 'E',
                                Subopcion: '2',
                                ...insertar
                            };
                            lineas.Eliminados.push(insertar);
                        }

                        valid = true;
                    }
                }
            }

            if (valid) {
                if (parseFloat(this.dataGridDetalle.getTotalSummaryValue("Debe")).toFixed(2) === parseFloat(this.dataGridDetalle.getTotalSummaryValue("Haber")).toFixed(2)) { //Ya que debe y haber son iguales, se compara cualquiera de los dos con el monto del encabezado del cheque
                    await this.axios.post('/app/v1_contabilidad_general/LineasAgrupado', {
                            ...lineas
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.TipoBitacora = 'M'
                                this.ActBitacora()
                                this.ConsultaPartidas(this.formEncabezado.CodPartida, 0)

                                this.$vs.dialog({
                                    type: 'alert',
                                    color: '#ed8c72',
                                    title: 'Actulización correcta',
                                    acceptText: 'Aceptar',
                                    text: 'Las líneas de la partida han sido actualizadas',
                                    buttonCancel: 'border',
                                    accept: () => {},
                                })

                            }
                        })
                } else {
                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'Montos erroneos',
                        acceptText: 'Aceptar',
                        text: 'Los montos de Debe y Haber no coinciden.',
                        buttonCancel: 'border',
                        accept: () => {},
                    })
                    e.cancel = true
                    return
                }
            }

        },

        onEditingStart(e) {
            const fila = e.data;

            //Evita que se puedan editar las filas cuando sean generados por cualquiera de los otros 2 popups
            if (fila.TipoPartida === 'LDA' || fila.TipoPartida === 'DNG') {
                e.cancel = true;
            }
        },

        onRowRemoving(e) {
            const fila = e.data;
            if (fila.TipoPartida === 'LDA' || fila.TipoPartida === 'DNG') {
                e.cancel = true; // Evita que se elimine
                this.$vs.dialog({
                    title: 'No permitido',
                    text: 'Las líneas de partidas LDA o DNG no pueden ser eliminadas.',
                    color: 'warning',
                    acceptText: 'Aceptar'
                });
            }
        },

        EditarEncabezado() {
            this.editarEncabezado = true

            this.encabezadoPartida = {
                ...this.formEncabezado
            }
        },

        EliminarPartida() {
            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Boorar partida',
                text: '¿Está seguro de eliminar esta partida?',
                acceptText: 'Aceptar',
                cancelText: 'Cancelar',
                accept: () => {
                    this.TipoBitacora = 'E'
                    this.BorrarPartidas()
                }
            })
        },

        async AceptarModificacion() {
            await this.axios.post('/app/v1_bancos/Revision', {
                    Opcion: 5,
                    Observaciones: this.formularioCheque.Observaciones,
                    NoNegociable: this.formularioCheque.NoNegociable == true ? 'S' : 'N',
                    CuentaBanco: this.formularioCheque.Cuenta,
                    Tipo: this.formularioCheque.Tipo,
                    Referencia: this.formularioCheque.Numero,
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.editarEncabezado = false
                    }
                })
        },

        CancelarModificacion() {
            this.editarEncabezado = false

            this.formEncabezado = {
                ...this.encabezadoPartida
            }
        },

        customizeTextValores(cellInfo) {
            if (cellInfo.value !== null && typeof cellInfo.value === 'number') {
                let x = parseFloat(cellInfo.value).toFixed(2)
                return 'Q. ' + x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            }
            return null
        },

        async ActBitacora() {
            await this.axios.post('/app/v1_contabilidad_general/TransPartidas', {
                    Opcion: "I",
                    SubOpcion: "1",
                    Periodo: this.formulario.Periodo,
                    Partida: this.NuevaPartidaContable == true ? parseInt(this.periodoActual.SigPartida) : this.formEncabezado.CodPartida,
                    TipoBitacora: this.TipoBitacora,
                    _NOTIFICACIONES: false
                })
                .then()
        },

        NuevaPartida() {
            this.crearPartida = true
            this.tempEncabezado = {
                ...this.formEncabezado,
            }

            if (this.formulario.Periodo != this.periodoActual.CodigoPeriodo) {
                this.otroPeriodo = true
                this.formulario.Periodo = this.periodoActual.CodigoPeriodo
            }

            this.LimpiarPartidas() // Se limpia el encabezado antes de asignarle lo nuevo valores

            this.formEncabezado = {
                Tipo: 'N',
                Generada: false,
                Debe: 0,
                Haber: 0,
                Status: 'P',
                Cuenta: '',
                Fecha: new Date()
            }
            this.tempValoresTabla = this.valoresTabla
            this.valoresTabla = []

            this.NuevaPartidaContable = true

        },

        CancelarCreacion() {
            this.crearPartida = false

            if (this.tempEncabezado.Periodo && this.formulario.Periodo != this.tempEncabezado.Periodo) {
                this.otroPeriodo = true
                this.formulario.Periodo = this.tempEncabezado.Periodo
            } else {
                this.formEncabezado = {
                    CodigoPartida: null,
                    Generada: false,
                    Cuenta: null,
                    Tipo: null,
                    Fecha: null
                }
            }

            this.ConsultaPartidas(this.tempEncabezado.CodPartida, 0)

            // this.formEncabezado = {
            //     ...this.tempEncabezado
            // }

            // this.valoresTabla = this.tempValoresTabla
            this.tempEncabezado = {}
        },

        Mayorizar() {
            if (!this.permisoMayorizar) {
                this.$vs.dialog({
                    type: 'alert',
                    color: 'danger',
                    title: 'Sin permiso',
                    text: 'No cuenta con permiso para mayorizar.',
                    acceptText: 'Aceptar',
                    cancelText: 'Cancelar',
                    accept: () => {
                        return
                    }
                })
                return
            } else {
                if (this.formEncabezado.Status == 'P') {
                    if (this.valoresTabla.length == 0) {
                        this.$vs.dialog({
                            type: 'alert',
                            color: 'danger',
                            title: 'Sin datos',
                            text: 'No hay datos en las líneas.',
                            acceptText: 'Aceptar',
                            cancelText: 'Cancelar',
                            accept: () => {
                                return
                            }
                        })
                        return
                    }

                    if (Math.abs(parseFloat(this.dataGridDetalle.getTotalSummaryValue("Debe")).toFixed(2) - parseFloat(this.dataGridDetalle.getTotalSummaryValue("Haber")).toFixed(2)) > 0.01) {
                        this.$vs.dialog({
                            type: 'alert',
                            color: 'danger',
                            title: 'Descuadre',
                            text: 'El balance de la partida debe ser igual a 0.',
                            acceptText: 'Aceptar',
                            cancelText: 'Cancelar',
                            accept: () => {
                                return
                            }
                        })
                        return
                    }

                    if (this.formEncabezado.Tipo == 'C') {
                        this.$vs.dialog({
                            type: 'confirm',
                            color: 'danger',
                            title: 'Descuadre',
                            text: 'Ya ejecutó el procedimiento .',
                            acceptText: 'Sí',
                            cancelText: 'No',
                            accept: () => {
                                this.CheckActividad()
                            },
                            cancel: () => {
                                return
                            }
                        })
                        return
                    }
                    this.CheckActividad()
                } else {
                    this.CheckActividad()
                }
            }
        },

        GuardarPartida() {
            if (this.formEncabezadoInstance) {
                const result = this.formEncabezadoInstance.validate(); // Dispara validación

                if (result.isValid) {
                    this.TipoBitacora = '-'
                    this.axios.post('/app/v1_contabilidad_general/TranAuxiliaresB', {
                            Opcion: 'I',
                            SubOpcion: '3',
                            Periodo: this.periodoActual.CodigoPeriodo,
                            Partida: parseInt(this.periodoActual.SigPartida) == 0 || this.periodoActual.SigPartida == '' ? 1 : parseInt(this.periodoActual.SigPartida),
                            Fecha: this.formEncabezado.Fecha,
                            Referencia: this.formEncabezado.Referencia,
                            Descripcion: this.formEncabezado.Descripcion,
                            TotalDebe: this.formEncabezado.TotalDebe,
                            TotalHaber: this.formEncabezado.TotalHaber,
                            TipoPartida: this.formEncabezado.Tipo,
                            CuentaInt: this.formEncabezado.Cuenta == '' || this.formEncabezado.Cuenta == null ? null : this.formEncabezado.Cuenta,
                            Generada: 'N'
                        })
                        .then((resp) => {
                            if (resp.data.codigo == 0) {
                                this.tempEncabezado = {}
                                this.TipoBitacora = 'I'

                                this.ActPeriodo()
                                this.crearPartida = false
                                this.ConsultaPartidas(999999999, -1)
                            }
                        })
                } else {
                    this.$vs.dialog({
                        type: 'alert',
                        color: 'danger',
                        title: 'Información incompleta',
                        text: 'Faltan información para la partida',
                        acceptText: 'Aceptar',
                        accept: () => {}
                    })
                    return
                }
            }
            //Nueva partida

        },

        async ActPeriodo(regresarSigPartida = false) { // regresarSigPartida sirve para regresar el correlativo de la siguiente partida cuando se está eliminando la última partida
            let partida = 0
            if (regresarSigPartida) {
                partida = parseInt(this.formulario.SigPartida - 2) // Se le resta 2 ya que en el SP se le suma 1
            } else {
                partida = parseInt(this.formulario.SigPartida)
            }
            await this.axios.post('/app/v1_contabilidad_general/TranAuxiliaresB', {
                    Opcion: 'U',
                    SubOpcion: '2',
                    Periodo: this.formulario.Periodo,
                    Partida: parseInt(partida) == 0 || partida == '' ? 1 : partida,
                    _NOTIFICACIONES: false
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        if (!regresarSigPartida) {
                            this.TipoBitacora = 'M'
                            this.ActBitacora()
                            this.ConsultaPeridoActual()
                        }
                    }
                })
        },
        //Verificar que existan las facturas digitadas
        //VerificaDocumentosDeAjustes
        async VerificaSiEsAjusteLDA_DNG() {
            this.ListaDocumentos = []

            await this.axios.post('/app/v1_contabilidad_general/CuentaAjusteLDA', {
                    Opcion: 'C',
                    SubOpcion: '12',
                    Periodo: this.formulario.Periodo,
                    Partida: this.formEncabezado.CodPartida,
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.ListaDocumentos = resp.data.json;
                        this.ListaDocumentosV = true
                        this.bloquearNavegacion = true
                    } else {
                        this.ListaDocumentos = []
                        this.ListaDocumentosV = false
                        this.bloquearNavegacion = false
                    }
                })
                .catch(() => {})
        },

        async VerificaAjusteLDA_DNG() { // Esta función solo devuelve si las líneas son LDA o DNG, para determinar si se pueden editar las líneas o no
            this.ListaDocumentos = []

            await this.axios.post('/app/v1_contabilidad_general/CuentaAjusteLDA', {
                    Opcion: 'C',
                    SubOpcion: '13',
                    Periodo: this.formulario.Periodo,
                    Partida: this.formEncabezado.CodPartida,
                })
                .then(resp => {
                    if (resp.data.json[0].Cuantos > 0) {
                        this.contieneLDA_DNG = true
                    } else {
                        this.contieneLDA_DNG = false
                        this.VerificaSiEsAjusteLDA_DNG()
                    }
                })
                .catch(() => {})
        },

        async CuentasLiq(liquida = 1) {
            await this.axios.post('/app/v1_contabilidad_general/LiquidaAdmisionesC', {
                    Opcion: 'C',
                    SubOpcion: '1',
                    Liquida: liquida
                })
                .then((resp) => {
                    if (resp.data.codigo == 0) {
                        if (liquida == 1) {
                            this.formularioLiquidacion = resp.data.json[0]
                        } else {
                            this.formularioDevolucion = resp.data.json[0]
                        }
                    }
                })
                .catch(() => {});
        },

        async Liquidar(e) {
            let lineas = []
            const claves = [];
            for (let i = 0; i < e.changes.length; i++) {
                const change = e.changes[i];
                if (change.type === 'insert') {
                    lineas.push({
                        ...(change.data),
                    })
                }
            }

            lineas.forEach((linea, index) => {
                const origen = `${linea.SerieOrigen}-${linea.AdmisionOrigen}`;
                const destino = `${linea.SerieDestino}-${linea.AdmisionDestino}`;

                claves.push({
                    valor: origen,
                    fila: index
                });
                claves.push({
                    valor: destino,
                    fila: index
                });
            });

            for (let i = 0; i < claves.length; i++) {
                for (let j = i + 1; j < claves.length; j++) {
                    if (claves[i].valor === claves[j].valor) {
                        this.$vs.dialog({
                            type: 'alert',
                            color: 'danger',
                            title: 'Admisión duplicada',
                            text: 'No debe haber admisiones duplicadas. ' + claves[i].valor,
                            cancelText: 'Cancelar',
                            accept: () => {}
                        })
                        e.cancel = true
                        return
                    }
                }
            }

            await this.axios.post('/app/v1_contabilidad_general/Liquidar', {
                    Lineas: lineas,
                    FechaPartida: this.formEncabezado.Fecha,
                    Periodo: this.formulario.Periodo,
                    Partida: this.formEncabezado.CodPartida,
                    TipoPartida: 'LDA',
                    CuentaDebe: this.formularioLiquidacion.Debe,
                    CuentaHaber: this.formularioLiquidacion.Haber,
                })
                .then((resp) => {
                    if (resp.data.codigo == 0) {
                        this.ConsultaPartidas(this.formEncabezado.CodPartida, 0)
                        this.liquidarAdmisiones = false
                    }
                })
                .catch(() => {});
        },

        async Devolucion(e) {
            let lineas = []
            for (let i = 0; i < e.changes.length; i++) {
                const change = e.changes[i];
                if (change.type === 'insert') {
                    lineas.push({
                        ...(change.data),
                    })
                }
            }

            await this.axios.post('/app/v1_contabilidad_general/Devolucion', {
                    Lineas: lineas,
                    FechaPartida: this.formEncabezado.Fecha,
                    Periodo: this.formulario.Periodo,
                    Partida: this.formEncabezado.CodPartida,
                    TipoPartida: 'DNG',
                    CuentaDebe: this.formularioDevolucion.Debe,
                    CuentaHaber: this.formularioDevolucion.Haber,
                })
                .then((resp) => {
                    if (resp.data.codigo == 0) {
                        this.ConsultaPartidas(this.formEncabezado.CodPartida, 0)
                        this.devolucionNoGenerada = false
                    }
                })
                .catch(() => {});
        },

        onlyLeters(e) {
            /*
                8 = backspace
                9 = tab
                13 = enter
                60 - 90 = letras
                37 = flecha izquierda
                39 = flecha derecha
            */
            if ((e.event.keyCode < 65 || e.event.keyCode > 90) && e.event.keyCode !== 192 && e.event.keyCode !== 8 && e.event.keyCode !== 9 && e.event.keyCode !== 13 && e.event.keyCode !== 37 && e.event.keyCode !== 39) {
                e.event.preventDefault()
            }
        },

        ejecutarLiquidar() {
            this.$refs.gridLiquidacion.instance.saveEditData();
        },

        ejecutarDevolucion() {
            this.$refs.gridDevolucion.instance.saveEditData();
        },

        validarDuplicados() {
            const filas = this.admisionesLiquidar;
            const claves = [];

            // Generamos un arreglo con todas las combinaciones Serie|Admision y la fila donde están
            filas.forEach((fila, index) => {
                const origen = `${fila.SerieOrigen}|${fila.AdmisionOrigen}`;
                const destino = `${fila.SerieDestino}|${fila.AdmisionDestino}`;

                claves.push({
                    valor: origen,
                    fila: index
                });
                claves.push({
                    valor: destino,
                    fila: index
                });
            });

            // Ahora comparamos cada clave con las demás
            for (let i = 0; i < claves.length; i++) {
                for (let j = i + 1; j < claves.length; j++) {
                    if (claves[i].valor === claves[j].valor) {
                        return true; // Hay al menos una coincidencia
                    }
                }
            }

            return false; // No se encontraron duplicados
        },

        nuevaFilaLiquidacion() {
            this.habilitarLiquidar = false
            this.formularioLiquidacion.PacienteOrigen = null
            this.formularioLiquidacion.PacienteDestino = null

            this.formLiquidacionInstance.repaint()
        }
    },
    computed: {
        formConsultaInstance: function () {
            return this.$refs[formConsulta].instance;
        },

        formEncabezadoInstance: function () {
            return this.$refs[formEnc].instance;
        },

        dataGridDetalle: function () {
            return this.$refs[gridDetalle].instance;
        },

        sesion() {
            return this.$store.state.sesion
        },

        gridLiquidacionInstance: function () {
            return this.$refs[gridLiquidacion].instance;
        },

        formLiquidacionInstance: function () {
            return this.$refs[formLiquidacion].instance;
        },

        gridDevolucionInstance: function () {
            return this.$refs[gridDevolucion].instance;
        },

        formDevolucionInstance: function () {
            return this.$refs[formDevolucion].instance;
        },
    }
}
</script>   

<style>
.codigoUpper input {
    text-transform: uppercase;
}

.textCell .dx-data-row td {
    text-align: left !important;
}

.my-cell-invalid .dx-datagrid-rowsview .dx-data-row .dx-validator.dx-datagrid-invalid.dx-cell-modified::after,
.my-cell-invalid .dx-datagrid-rowsview .dx-data-row .dx-validator.dx-datagrid-invalid.dx-datagrid-invalid::after {
    border-width: 2px !important;
    border-color: #D9534F;
}

.Partidas-Container .dx-datagrid {
    max-height: 620px;
}
</style><style scoped>
.footer {
    height: 70px;
    margin-top: -65px;
    margin-left: 10px;
}

.label-size {
    font-size: 14px;
    font-weight: bold;
}

.label-StatusP {
    font-size: 15px;
    font-weight: bold;
    color: darkgreen;
}

.label-StatusM {
    font-size: 15px;
    font-weight: bold;
    color: darkred;
}

.flex-container {
    display: flex;
    flex-direction: row;
}

.navigation-buttons button:focus {
    background-color: blue !important;

}
</style>
