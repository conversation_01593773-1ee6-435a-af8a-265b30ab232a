<template>
<div>
    <DxTabPanel ref="tabRegistro" :height="'100%'" :data-source="menu">
        <template #title="{ data: tab }">
            <span>
                <font-awesome-icon class="pr-1" :icon="['fas', tab.icon]" />
                {{ tab.title }}
            </span>
        </template>
        <template #item="{ data: tab }">
            <div>
                <div v-if="tab.id === 1" class="p-2">
                    <Proveedores :TipoDoc="1" ref="Proveedores" :Corporativo="Corporativo" />
                </div>
                <div v-if="tab.id === 2" class="p-2">
                    <GeneracionChequesLotes :TipoDoc="1" :Corporativo="Corporativo" ref="GeneracionChequesLotes" />
                </div>
                <div v-if="tab.id === 3" class="p-2">
                    <ConsultaAnulacion ref="ConsultaAnulacion" :TipoCheque="'CH'" :ModalidadPago="'C'" :ImpresionLotes="'N'" :Corporativo="Corporativo" />
                </div>
                <div v-if="tab.id === 4" class="p-2">
                    <Revision ref="Revision" :TipoCheque="'CH'" :Corporativo="Corporativo" />
                </div>
                <div v-if="tab.id === 5" class="p-2">
                    <Impresion ref="Impresion" :TipoCheque="'CH'" :ModalidadPago="'C'" :ImpresionLotes="'N'" :Corporativo="Corporativo" />
                </div>
                <div v-if="tab.id === 6" class="p-2">
                    <Eliminacion ref="Eliminacion" :TipoCheque="'CH'" :ModalidadPago="'C'" :Corporativo="Corporativo" />
                </div>
                <div v-if="tab.id === 7" class="p-2">
                    <Vencimientos ref="Vencimientos" :Corporativo="Corporativo" :TipoCheque="'CH'" />
                </div>
                <div v-if="tab.id === 8" class="p-2">
                    <AnulacionExtemporanea ref="Entrega" :Corporativo="Corporativo" :TipoCheque="'CH'" />
                </div>
                <div v-if="tab.id === 9" class="p-2">
                    <Entrega ref="Entrega" :Corporativo="Corporativo" :TipoCheque="'CH'" />
                </div>
            </div>
        </template>
    </DxTabPanel>
</div>
</template>

<script>
import Proveedores from './Proveedores.vue'
import GeneracionChequesLotes from './GeneracionChequesLotes.vue';
import Impresion from './Impresion.vue';
import ConsultaAnulacion from './ConsultaAnulacion.vue';
import Eliminacion from './Eliminacion.vue';
import Vencimientos from './Vencimientos.vue';
import AnulacionExtemporanea from './AnulacionExtemporanea.vue';
import Entrega from './Entrega.vue';
import Revision from './Revision.vue';

export default {
    name: 'Cheques',
    components: {
        Proveedores,
        GeneracionChequesLotes,
        Impresion,
        ConsultaAnulacion,
        Eliminacion,
        Vencimientos,
        AnulacionExtemporanea,
        Entrega,
        Revision
    },
    data() {
        return {

            SelectedOption: 1,

            menu: [{
                    id: 1,
                    title: "Proveedores",
                    icon: "people-carry-box",
                },
                {
                    id: 2,
                    title: "Generación cheques lotes",
                    icon: "money-bills",
                },
                {
                    id: 3,
                    title: "Consulta y anulación",
                    icon: "magnifying-glass-minus",
                },
                {
                    id: 4,
                    title: "Revisión",
                    icon: "check-to-slot",
                },
                {
                    id: 5,
                    title: "Impresión",
                    icon: "print",
                },
                {
                    id: 6,
                    title: "Eliminación",
                    icon: "trash",
                },
                {
                    id: 7,
                    title: "Vencimientos",
                    icon: "calendar-xmark",
                },
                {
                    id: 8,
                    title: "Anulación extemporánea",
                    icon: "calendar-minus",
                },
                {
                    id: 9,
                    title: "Entrega",
                    icon: "circle-dollar-to-slot",
                },
            ],

        }
    },
    props: {
        TipoDoc: null, // Indica desde qué módulo se invoca el componente  1 = Cheques / 2 = Transferencias
        Corporativo: null
    },
    methods: {},
    created() {},
    mounted() {},
    watch: {},
    computed: {}
}
</script>

<style>

</style>
