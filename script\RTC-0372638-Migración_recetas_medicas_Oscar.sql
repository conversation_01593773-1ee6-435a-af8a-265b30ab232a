--> <CREACIÓN DE USUARIOS EN MASTER>
USE [master]
GO
IF (SELECT SUSER_ID('USRCOEXHISTORIALCLINICO')) IS NULL
/*QA   -> */ CREATE LOGIN [USRCOEXHISTORIALCLINICO] WITH PASSWORD=N'$Hi!StoRi@lCl!nco', DEFAULT_DATABASE=[PLANESMEDICOS], DEFAULT_LANGUAGE=[us_english], CHECK_EXPIRATION=OFF, CHECK_POLICY=OFF

USE [master]
GO
IF (SELECT SUSER_ID('USRREPORTEHISTORIALCLINICO')) IS NULL
/*QA   -> */ CREATE LOGIN [USRREPORTEHISTORIALCLINICO] WITH PASSWORD=N'R3pH1I$Tor!alCln', DEFAULT_DATABASE=[PLANESMEDICOS], DEFAULT_LANGUAGE=[us_english], CHECK_EXPIRATION=OFF, CHECK_POLICY=OFF

--> <FINALIZACIÓN CREACIÓN DE USUARIOS EN MASTER>

--> <Creación de usuario en bases de datos a utilizar>
USE [FARMACIAEXTERNA]
GO
IF (SELECT USER_ID('USRCOEXHISTORIALCLINICO')) IS NULL
	CREATE USER [USRCOEXHISTORIALCLINICO] FOR LOGIN [USRCOEXHISTORIALCLINICO] WITH DEFAULT_SCHEMA=[dbo]
GO

USE [PLANESMEDICOS]
GO
IF (SELECT USER_ID('USRCOEXHISTORIALCLINICO')) IS NULL
	CREATE USER [USRCOEXHISTORIALCLINICO] FOR LOGIN [USRCOEXHISTORIALCLINICO] WITH DEFAULT_SCHEMA=[dbo]
GO

USE [HOSPITAL]
GO
IF (SELECT USER_ID('USRCOEXHISTORIALCLINICO')) IS NULL
	CREATE USER [USRCOEXHISTORIALCLINICO] FOR LOGIN [USRCOEXHISTORIALCLINICO] WITH DEFAULT_SCHEMA=[dbo]
GO

USE [NOMINADB]
GO
IF (SELECT USER_ID('USRCOEXHISTORIALCLINICO')) IS NULL
	CREATE USER [USRCOEXHISTORIALCLINICO] FOR LOGIN [USRCOEXHISTORIALCLINICO] WITH DEFAULT_SCHEMA=[dbo]
GO

--> <Finalización de creación de usuario en bases de datos a utilizar>


--> <Creación de usuario de reporte en bases de datos a utilizar>
USE [NOMINADB]
GO
IF (SELECT USER_ID('USRREPORTEHISTORIALCLINICO')) IS NULL
	CREATE USER [USRREPORTEHISTORIALCLINICO] FOR LOGIN [USRREPORTEHISTORIALCLINICO] WITH DEFAULT_SCHEMA=[dbo]
GO

USE [FARMACIAEXTERNA]
GO
IF (SELECT USER_ID('USRREPORTEHISTORIALCLINICO')) IS NULL
	CREATE USER [USRREPORTEHISTORIALCLINICO] FOR LOGIN [USRREPORTEHISTORIALCLINICO] WITH DEFAULT_SCHEMA=[dbo]
GO

USE [HOSPITAL]
GO
IF (SELECT USER_ID('USRREPORTEHISTORIALCLINICO')) IS NULL
	CREATE USER [USRREPORTEHISTORIALCLINICO] FOR LOGIN [USRREPORTEHISTORIALCLINICO] WITH DEFAULT_SCHEMA=[dbo]
GO

USE [PLANESMEDICOS]
GO
IF (SELECT USER_ID('USRREPORTEHISTORIALCLINICO')) IS NULL
	CREATE USER [USRREPORTEHISTORIALCLINICO] FOR LOGIN [USRREPORTEHISTORIALCLINICO] WITH DEFAULT_SCHEMA=[dbo]
GO

--> <Finalización de creación de reporte de usuario en bases de datos a utilizar>


--****************************************
--************ ALTER TABLE ***************
--****************************************

--> <ALTER TABLE RecetasCl>
USE [FARMACIAEXTERNA];
GO
ALTER TABLE dbo.RecetasCl ADD
	Corporativo INT NULL
GO
EXECUTE sp_addextendedproperty N'MS_Description', N'Corporativo del usuario que registra información en la tabla', N'SCHEMA', N'dbo', N'TABLE', N'RecetasCl', N'COLUMN', N'Corporativo'
GO
--> <Finaliza ALTER TABLE RecetasCl>

--> <ALTER TABLE COEXRecetaPrescripcionEncabezado>
USE [PLANESMEDICOS];
GO
ALTER TABLE dbo.COEXRecetaPrescripcionEncabezado ADD
	Corporativo INT NULL
GO
EXECUTE sp_addextendedproperty N'MS_Description', N'Corporativo del usuario que registra información en la tabla', N'SCHEMA', N'dbo', N'TABLE', N'COEXRecetaPrescripcionEncabezado', N'COLUMN', N'Corporativo'
GO
--> <Finaliza ALTER TABLE COEXRecetaPrescripcionEncabezado>

--****************************************
--********** STORE PROCEDURE *************
--****************************************

------> Modificados

--> <ALTER PROCEDURE sp_CopagoReceta>   ***** Revisar si tiene cambios *****

USE [FARMACIAEXTERNA]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

ALTER procedure [dbo].[sp_CopagoReceta]
        @IdContrato as varchar(15),
        @Idplan as varchar(15),
        @IdTipoHospital as varchar(1),
        @IdCliente as varchar(15),
        @NoAdhesion as varchar(25)
As 
Begin
Begin Try
Begin transaction
	Declare @Respuesta as varchar(100),
	@CodRespuesta as int


	Select *, EventosDisponibles=(Isnull(MaximoEventos,0)-eventos),
					AplicaBeneficio= Case When NoPagos>=PeriodoEsperaMeses then 'SI' ELSE 'NO' END from (
	select B.IDBENEFICIO,c.idtipoHospital,c.idcopago,cd.Descripcion,c.monto as CopagoFijo,b.LimiteMonto as MontoBeneficio,
			LimiteVeces as maximoEventos,PeriodoEsperaMeses,
			Eventos=Isnull((Select count(*)
									From FarmaciaExterna..RecetasCl as r
									Inner Join FarmaciaExterna..Facturasrecetas as f on (r.codigo=f.idreceta) 
									Inner Join PlanesMedicos..Citas as C ON(C.idcita=r.IdCita )
									Where c.idcliente=@IdCliente
									AND DATEPART(year,FechaHoraCita)=datepart(year,getdate())
	And f.Beneficio>0.1),0),
	NoPagos=(Select count(*) as NoPagos
			From planesmedicos..afiliadosxcontrato as axc
					Inner Join PlanesMedicos..AfiliadospagodeCuotas as apd 
					On(axc.NumeroAdhesion=apd.NumeroAdhesion and axc.idempresa=apd.Empresa)
							Where axc.numeroadhesion=@NoAdhesion and axc.parentesco='00'
	)
	From  PlanesMedicos..BeneficiosXContrato as b
			Inner Join planesmedicos..contratos as ct ON(ct.idcontrato=B.IdContrato and ct.idempresa=B.idempresa and ct.status='A') 
			Inner Join PlanesMedicos..CopagosxContrato as c ON(b.idempresa=c.idempresa and b.idplan=c.idplan and b.idcontrato=c.idcontrato aND idCopagoDescripcion=13) 
			Inner Join PlanesMedicos..CopagoDescripcion as cd on(cd.IdcopagoDescripcion=c.IdcopagoDescripcion )
	Where B.status='A' aND b.GrupoDeBeneficio=18
	And b.idContrato=@IdContrato        
	And b.idplan=@Idplan
	And IdTipoHospital=@IdTipoHospital
	)pv

commit transaction
End Try
Begin Catch
         Rollback Transaction
                Set @Respuesta='Error no controlado al realizar la transacción '+ ERROR_MESSAGE()+' No. error: '+Cast(ERROR_NUMBER() as varchar(10)) 
                Set @CodRespuesta='-1'
                Select @Respuesta as Respuesta,@CodRespuesta as CodRespuesta

End catch
End

--> <Finaliza ALTER PROCEDURE sp_CopagoReceta>


--> <ALTER PROCEDURE sp_CargaProductosInv> ***** Revisar si tiene cambios *****

USE [FARMACIAEXTERNA]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*
******************************************************
Autor: Vinicio Cahuex C.
Descripcion:  Obtiene el catálogo de productos en base
        al listado de códigos que envió Guido en anexo I.
        incluye listado de productos para Pacientes con 
        beneficio SaSi (cubiertoa)
Fecha: 17/03/2021
******************************************************

*/
--ALTER
ALTER PROCEDURE [dbo].[sp_CargaProductosInv]
        @i_IdSucursal varchar (5)        --Codigo de sucursal Meykos
AS
BEGIN

        SELECT 
			ISNULL(e.Existencia,0) AS Existencia, 
			s.Producto, 
			s.CubreSaSi, 
			Descripcion, 
			PVentaA, 
            Indicaciones, 
			Descrip_Marca AS DescripMarca, 
			Estado, 
			Principio_Activo AS PrincipioActivo, 
            Patologia, 
			ContraIndicaciones, 
			Presentacion
        FROM 
			ProductosSaSi s (NOLOCK)
        INNER JOIN ProductosFarmaciaExt p (nolock) 
			ON (p.producto = s.producto)
        INNER JOIN Existencias e (NOLOCK) 
			ON (e.producto = p.producto 
				AND e.sucursal = @i_IdSucursal)
        ORDER BY p.Descripcion
END;

--> <Finaliza ALTER PROCEDURE sp_CargaProductosInv>



------> Nuevos

--> <CREATE PROCEDURE SpCoexPlanBusquedaInformacionSeguro>

USE [PLANESMEDICOS]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Creado por:			Oscar Rodríguez
-- Fecha de creación:	09/02/2024
-- Producto:			RTC-0372638
-- Descripción:			Obtiene la información de seguro del paciente
--=============================================
CREATE PROCEDURE [dbo].[SpCoexPlanBusquedaInformacionSeguro]
	@iEmpresaU AS VARCHAR(3)
	, @iIdCita AS INT
AS
BEGIN
	SELECT 
		p.IdCita, 
		RTRIM(p.IdCliente) IdCliente, 
		p.NumeroAdhesion, 
		p.IdAfiliado, 
		IdMedico,
		NombreMedico = RTRIM(a.Nombre)+ ' ' + RTRIM(a.Apellido), 
		IdBase, 
		CONCAT(Nombre1,' ', Nombre2,' ', Apellido1,' ', Apellido2) as Nombre, 
		c.Nit, 
		REPLACE(ISNULL(c.TelefonoDomicilio, c.TelefonoCelular),'-','') as Telefono, 
		h.CodigoFarmaciaExt, 
		RTRIM(h.Nombre) Hospital, 
		RTRIM(IdPlan) IdPlan,  
		TipoDeHospital, 
		RTRIM(r.IdContrato) IdContrato,
		ISNULL(s.nombre,'Paciente sin seguro (Privado)') AS NombrePlan,
		a.Codigo as Colegiado, 
		a.ClaveStandard, 
		a.Titulo, 
		b.FirmaBitmap Firma
	FROM 
		PagoCitas p (NOLOCK)
		INNER JOIN Citas r (NOLOCK) 
			ON r.IdCita = p.IdCita
		INNER JOIN Clientes c (NOLOCK) 
			ON c.IdCliente = p.IdCliente
		INNER JOIN NOMINADB..EmpHospital h (NOLOCK) 
			ON h.codigobase = r.IdBase 
			AND h.empresa = @iEmpresaU
		LEFT JOIN PlanesXcontrato s (NOLOCK) 
			ON s.IdPlanXcontrato = r.IdPlan
		INNER JOIN HOSPITAL..Ajenos a (NOLOCK) 
			ON a.Empresa = r.IdEmpresa 
			AND a.Activo = 'S' 
			AND a.Especialidad IS NOT NULL
			AND a.Codigo = r.IdMedico 
		LEFT JOIN HOSPITAL..AjenosFirmaDigital b (NOLOCK) 
			ON a.Codigo = b.Ajeno
	WHERE 
		p.IdCita  = @iIdCita
END

--> <Finaliza CREATE PROCEDURE SpCoexPlanBusquedaInformacionSeguro>


--> <CREATE PROCEDURE SpCoexPlanInsertarRecetaEncabezado>

USE [PLANESMEDICOS]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Creado por:			Oscar Rodríguez
-- Fecha de creación:	15/02/2024
-- Producto:			RTC-0372638
-- Descripción:			Crea el encabezado de la receta
--=============================================
CREATE PROCEDURE [dbo].[SpCoexPlanInsertarRecetaEncabezado]
	@iIdMedico AS INT
	, @iIdCita AS INT
	, @iEmpresaU AS VARCHAR(3)
	, @iIdCliente AS VARCHAR(15)
	, @iIdAfiliado AS VARCHAR(17) = ''
	, @iNumeroAdhesion AS VARCHAR(25) = 0
	, @iCorporativo AS INT
AS
BEGIN
	DECLARE @wReceta AS INT = 
		(
			SELECT
				ISNULL(MAX(NoReceta),0) + 1
			FROM
				PLANESMEDICOS..COEXRecetaPrescripcionEncabezado
		)

	INSERT
		PLANESMEDICOS..COEXRecetaPrescripcionEncabezado 
			(
				Medico, 
				IdCita,
				NoReceta,
				IdEmpresa,
				IdCliente,
				IdAfiliado,
				NumeroAdhesion,
				FechaReceta,
				Corporativo
			)
	VALUES
		(
			@iIdMedico,
			@iIdCita,
			@wReceta,
			@iEmpresaU,
			@iIdCliente,
			@iIdAfiliado,
			@iNumeroAdhesion,
			CONVERT(DATE, GETDATE()),
			@iCorporativo
		)

	SELECT
		0 AS codigo,
		'Registro agregado con éxito' AS descripcion,
		0 AS tipo_error,
		@wReceta AS NoReceta
	
END

--> <Finaliza CREATE PROCEDURE SpCoexPlanInsertarRecetaEncabezado>


--> <CREATE PROCEDURE SpCoexPlanInsertarRecetaDetalle>

USE [PLANESMEDICOS]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Creado por:			Oscar Rodríguez
-- Fecha de creación:	16/02/2024
-- Producto:			RTC-0372638
-- Descripción:			Registra el detalle de la receta
--=============================================
CREATE PROCEDURE [dbo].[SpCoexPlanInsertarRecetaDetalle]
	@iEmpresaU AS VARCHAR(3)
	, @iNoReceta AS INT
	, @iLinea AS INT
	, @iDescripcion AS VARCHAR(MAX)
AS
BEGIN
	DECLARE @wReceta AS INT = 
		(
			SELECT
				ISNULL(MAX(NoReceta),0) + 1
			FROM
				PLANESMEDICOS..COEXRecetaPrescripcionEncabezado
		)

	INSERT
		PLANESMEDICOS..COEXRecetaPrescripcionTratamiento
			(
				IdEmpresa, 
				NoReceta,
				Linea,
				Descripcion
			)
	VALUES
		(
			@iEmpresaU,
			@iNoReceta,
			@iLinea,
			@iDescripcion
		)

	SELECT
		0 AS codigo,
		'Registro agregado con éxito' AS descripcion,
		0 AS tipo_error
	
END

--> <Finaliza CREATE PROCEDURE SpCoexPlanInsertarRecetaDetalle>


--> <CREATE PROCEDURE SpCoexFarmInsertarRecetaEncabezado>

USE [FARMACIAEXTERNA]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Creado por:			Oscar Rodríguez
-- Fecha de creación:	15/02/2024
-- Producto:			RTC-0372638
-- Descripción:			Crea el encabezado de la receta
--=============================================
CREATE PROCEDURE [dbo].[SpCoexFarmInsertarRecetaEncabezado]
	@iIdCita AS INT
	, @iTotal AS FLOAT
	, @iCopago AS FLOAT
	, @iBeneficio AS FLOAT
	, @iXpNoReceta AS INT
	, @iSucursal AS INT
	, @iObservaciones AS VARCHAR(1000)
	, @iTieneBeneficio AS INT = NULL
	, @iIdPlan AS VARCHAR(15) = NULL
	, @iIdCopago AS VARCHAR(15) = NULL
	, @iIdBeneficio AS VARCHAR(15) = NULL
	, @iCorporativo AS INT
AS
BEGIN
	DECLARE @wReceta AS INT = 
		(
			SELECT
				ISNULL(MAX(Codigo),0) + 1
			FROM
				FARMACIAEXTERNA..RecetasCl
		)

	IF (@iTieneBeneficio IS NOT NULL)
	BEGIN
		INSERT
			FARMACIAEXTERNA..RecetasCl 
				(
					Fuente, 
					Origen, 
					IdCita, 
					Total, 
					Copago, 
					Codigo, 
					Beneficio, 
					XpNoReceta, 
					Sucursal, 
					Observaciones, 
					Fecha, 
					Registro,
					IdPlan,
					IdCopago,
					IdBeneficio,
					Corporativo
				)
		VALUES
			(
				1,
				2,
				@iIdCita,
				@iTotal,
				@iCopago,
				@wReceta,
				@iBeneficio,
				@iXpNoReceta,
				@iSucursal,
				@iObservaciones,
				CONVERT(DATE, GETDATE()),
				GETDATE(),
				@iIdPlan,
				@iIdCopago,
				@iIdBeneficio,
				@iCorporativo
			)

		SELECT
			0 AS codigo,
			'Registro agregado con éxito' AS descripcion,
			0 AS tipo_error,
			@wReceta AS NoReceta
	END
	ELSE
	BEGIN
		INSERT
				RecetasCl 
					(
						Fuente, 
						Origen, 
						IdCita, 
						Total, 
						Copago, 
						Codigo, 
						Beneficio, 
						XpNoReceta, 
						Sucursal, 
						Observaciones, 
						Fecha, 
						Registro,
						Usuario
					)
		VALUES
			(
				1,
				2,
				@iIdCita,
				@iTotal,
				@iCopago,
				@wReceta,
				@iBeneficio,
				@iXpNoReceta,
				@iSucursal,
				@iObservaciones,
				CONVERT(DATE, GETDATE()),
				GETDATE(),
				@iCorporativo
			)

		SELECT
			0 AS codigo,
			'Registro agregado con éxito' AS descripcion,
			0 AS tipo_error,
			@wReceta AS NoReceta
	END
END

--> <Finaliza CREATE PROCEDURE SpCoexFarmInsertarRecetaEncabezado>


--> <CREATE PROCEDURE SpCoexFarmInsertarRecetaDetalle>

USE [FARMACIAEXTERNA]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Creado por:			Oscar Rodríguez
-- Fecha de creación:	16/02/2024
-- Producto:			RTC-0372638
-- Descripción:			Registra el detalle de la receta
--=============================================
CREATE PROCEDURE [dbo].[SpCoexFarmInsertarRecetaDetalle]
	@iIdReceta AS INT
	, @iTotal AS FLOAT
	, @iPrecioU AS FLOAT
	, @iCantidad AS INT
	, @iCoaseguro AS FLOAT
	, @iProducto AS VARCHAR(10)
	, @iPrescripcion AS VARCHAR(175)
AS
BEGIN
	INSERT
		FARMACIAEXTERNA..RecetasDetalle
			(
				IdReceta, 
				Total, 
				PrecioU, 
				Cantidad, 
				Coaseguro, 
				Producto, 
				Prescripcion
			)
	VALUES
		(
			@iIdReceta,
			@iTotal,
			@iPrecioU,
			@iCantidad,
			@iCoaseguro,
			@iProducto,
			@iPrescripcion
		)

	SELECT
		0 AS codigo,
		'Registro agregado con éxito' AS descripcion,
		0 AS tipo_error
END

--> <Finaliza CREATE PROCEDURE SpCoexFarmInsertarRecetaDetalle>


--> <CREATE PROCEDURE SpCoexFarmBusquedaRecetaPDF>

USE [FARMACIAEXTERNA]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Creado por:			Oscar Rodríguez
-- Fecha de creación:	20/02/2024
-- Producto:			RTC-0372638
-- Descripción:			Devuelve la información de la receta a imprimir
--=============================================
CREATE PROCEDURE [dbo].[SpCoexFarmBusquedaRecetaPDF]
	@iIdReceta AS INT
	, @iIdCita AS INT
AS
BEGIN
	SELECT 
		re.Codigo IdReceta,
		re.IdCita,
		re.Total,
		re.Copago,
		re.Beneficio,
		re.Observaciones,
		re.Fecha
	FROM 
		FARMACIAEXTERNA..RecetasCl re (NOLOCK)
	WHERE
		re.Codigo = @iIdReceta
		AND re.IdCita = @iIdCita
	
END

--> <Finaliza CREATE PROCEDURE SpCoexFarmBusquedaRecetaPDF>


--> <CREATE PROCEDURE SpCoexFarmBusquedaDetalleRecetaPDF>

USE [FARMACIAEXTERNA]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Creado por:			Oscar Rodríguez
-- Fecha de creación:	20/02/2024
-- Producto:			RTC-0372638
-- Descripción:			Obtiene el detalle de la receta a imprimir
--=============================================
CREATE PROCEDURE [dbo].[SpCoexFarmBusquedaDetalleRecetaPDF]
	@iIdReceta AS INT
	, @iIdCita AS INT
AS
BEGIN
	SELECT 
		rd.Cantidad,
		rd.Prescripcion,
		pfe.Descripcion,
		pfe.Presentacion
	FROM 
		FARMACIAEXTERNA..RecetasCl re (NOLOCK)
		INNER JOIN FARMACIAEXTERNA..RecetasDetalle rd (NOLOCK)
			ON re.Codigo = rd.IdReceta
		INNER JOIN FARMACIAEXTERNA..ProductosFarmaciaExt pfe (NOLOCK)
			ON rd.Producto = pfe.Producto
	WHERE
		re.Codigo = @iIdReceta
		AND re.IdCita = @iIdCita
	
END

--> <Finaliza CREATE PROCEDURE SpCoexFarmBusquedaDetalleRecetaPDF>


--> <CREATE PROCEDURE SpCoexFarmBusquedaDetalleRecetaPDF>

USE [FARMACIAEXTERNA]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Creado por:			Oscar Rodríguez
-- Fecha de creación:	16/02/2024
-- Producto:			RTC-0372638
-- Descripción:			Busca la configuración para el envío de receta
--=============================================
CREATE PROCEDURE [dbo].[SpCoexPlanBusquedaConfiguracionReceta]
AS
BEGIN
	SELECT
		*
	FROM 
		RecetasConfig


END

--> <Finaliza CREATE PROCEDURE SpCoexFarmBusquedaRecetaPDF>


--******************************************
--********** PERMISOS DATABASE *************
--******************************************

-- HOSPITAL
USE [HOSPITAL]
GO

---- Select
GRANT SELECT ON [HOSPITAL].[dbo].[AjenosEspecialidades] TO [USRCOEXHISTORIALCLINICO];

-- PLANESMEDICOS
USE [PLANESMEDICOS]
GO

---- Select
GRANT SELECT ON [PLANESMEDICOS].[dbo].[Citas] TO [USRCOEXHISTORIALCLINICO];
GRANT SELECT ON [PLANESMEDICOS].[dbo].[AfiliadosXContrato] TO [USRCOEXHISTORIALCLINICO];
GRANT SELECT ON [PLANESMEDICOS].[dbo].[AfiliadosPagoDeCuotas] TO [USRCOEXHISTORIALCLINICO];
GRANT SELECT ON [PLANESMEDICOS].[dbo].[Contratos] TO [USRCOEXHISTORIALCLINICO];
GRANT SELECT ON [PLANESMEDICOS].[dbo].[CopagoDescripcion] TO [USRCOEXHISTORIALCLINICO];
GRANT SELECT ON [PLANESMEDICOS].[dbo].[CopagosxContrato] TO [USRCOEXHISTORIALCLINICO];
GRANT SELECT ON [PLANESMEDICOS].[dbo].[BeneficiosXContrato] TO [USRCOEXHISTORIALCLINICO];

---- Búsqueda
GRANT EXECUTE ON [PLANESMEDICOS].[dbo].[SpCoexPlanBusquedaInformacionSeguro] TO [USRCOEXHISTORIALCLINICO];
GRANT EXECUTE ON [PLANESMEDICOS].[dbo].[SpCoexPlanBusquedaPacientes] TO [USRCOEXHISTORIALCLINICO];

---- Insertar
GRANT EXECUTE ON [PLANESMEDICOS].[dbo].[SpCoexPlanInsertarRecetaEncabezado] TO [USRCOEXHISTORIALCLINICO];
GRANT EXECUTE ON [PLANESMEDICOS].[dbo].[SpCoexPlanInsertarRecetaDetalle] TO [USRCOEXHISTORIALCLINICO];

-- FARMACIAEXTERNA
USE [FARMACIAEXTERNA]
GO

---- Búsqueda
GRANT EXECUTE ON [FARMACIAEXTERNA].[dbo].[sp_CopagoReceta] TO [USRCOEXHISTORIALCLINICO];
GRANT EXECUTE ON [FARMACIAEXTERNA].[dbo].[SpCoexPlanBusquedaConfiguracionReceta] TO [USRCOEXHISTORIALCLINICO];

---- Insertar
GRANT EXECUTE ON [FARMACIAEXTERNA].[dbo].[SpCoexFarmInsertarRecetaEncabezado] TO [USRCOEXHISTORIALCLINICO];
GRANT EXECUTE ON [FARMACIAEXTERNA].[dbo].[SpCoexFarmInsertarRecetaDetalle] TO [USRCOEXHISTORIALCLINICO];


--**************************************************
--********** PERMISOS DATABASE REPORTE *************
--**************************************************

-- HOSPITAL
USE [HOSPITAL]
GO

---- Select
GRANT SELECT ON [HOSPITAL].[dbo].[AjenosFirmaDigital] TO [USRREPORTEHISTORIALCLINICO];

-- NOMINADB
USE [NOMINADB]
GO

---- Select
GRANT SELECT ON [NOMINADB].[dbo].[EmpHospital] TO [USRREPORTEHISTORIALCLINICO];

-- PLANESMEDICOS
USE [PLANESMEDICOS]
GO

GRANT EXECUTE ON [PLANESMEDICOS].[dbo].[SpCoexPlanBusquedaInformacionSeguro] TO [USRREPORTEHISTORIALCLINICO];

-- FARMACIAEXTERNA
USE [FARMACIAEXTERNA]
GO

GRANT EXECUTE ON [FARMACIAEXTERNA].[dbo].[SpCoexFarmBusquedaDetalleRecetaPDF] TO [USRREPORTEHISTORIALCLINICO];
GRANT EXECUTE ON [FARMACIAEXTERNA].[dbo].[SpCoexFarmBusquedaRecetaPDF] TO [USRREPORTEHISTORIALCLINICO];
