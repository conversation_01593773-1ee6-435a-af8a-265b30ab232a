<template>
<div class="p-2">
    <vx-card>
        <div class="buttons" v-show="graficaSeleccionada === 0">
            <div clientWidth="150" class="p-2 w-full sm:w-1 md:w-1/2 lg:w-1/2 xl:w-1/4 div-button" v-for="(item, index) in MenuGraficas" v-bind:key="index">
                <vs-button class="buttonGrafica" color="primary" type="filled" @click="OpcionSeleccionada(item)">
                    <div>
                        <font-awesome-icon :icon="['fas', item.icon]" class="i-size" />
                    </div>
                    <span>{{item.text}}</span>
                </vs-button>
            </div>
        </div>
        <div v-show="graficaSeleccionada !== 0">
            <vs-button color="primary" type="filled" @click="() => {this.LimpiarVariables()}">
                <font-awesome-icon :icon="['fas', 'arrow-left']" />
                <span> Regresar</span>
            </vs-button>
            <DxChart id="chart" :data-source="series" :title="tituloGrafica" :tooltip="{ enabled: true }" @point-hover-changed="onPointHoverChanged">
                <DxChartCommonSeriesSettings :type="'spline'" :argument-field="graficaSeleccionada === 3 ? 'Estatura' : 'Mes'" />
                <DxChartCommonAxisSettings>
                    <DxChartGrid :visible="true" />
                </DxChartCommonAxisSettings>
                <DxChartSeries v-for="architecture in graficasLineas" :key="architecture.value" :value-field="architecture.value" :name="architecture.name" :point="{ visible: architecture.point }" />
                <DxChartMargin :bottom="20" />
                <DxChartArgumentAxis :allow-decimals="true" :title="ejeX" :visual-range-update-mode="'keep'" :visual-range="{ startValue:(rangoMinimo - 3) >= 0 ? (rangoMinimo - 3) : (graficaSeleccionada === 3 ? 45 : 0), endValue: rangoMaximo + 3}">
                    <DxChartLabel>
                        <DxChartFormat type="decimal" />
                    </DxChartLabel>
                </DxChartArgumentAxis>
                <DxChartValueAxis :title="ejeY" />
                <DxChartLegend vertical-alignment="top" horizontal-alignment="right" />
                <DxChartExport :enabled="true" />
                <DxChartTooltip :enabled="true" :zIndex="9999" contentTemplate="tooltipTemplate" />
                <DxChartZoomAndPan :allow-mouse-wheel="true" value-axis="both" argument-axis="both" />
                <DxChartCrosshair :enabled="true">
                    <DxChartLabel :visible="true" />
                </DxChartCrosshair>

                <template #tooltipTemplate="{ data : info }">
                    <div>
                        <p v-if="graficaSeleccionada === 1">
                            <b>Talla: </b>{{ info.value }}
                        </p>
                        <p v-if="graficaSeleccionada === 2 || graficaSeleccionada === 3">
                            <b>Peso: </b>{{ info.value }}
                        </p>
                        <p v-if="graficaSeleccionada === 4">
                            <b>Circunferencia: </b>{{ info.value }}
                        </p>

                        <p v-if="graficaSeleccionada !== 3">
                            <b>Mes: </b> {{ info.argument }}
                        </p>
                        <p v-if="graficaSeleccionada === 3">
                            <b>Talla: </b> {{ info.argument }}
                        </p>
                    </div>
                </template>
            </DxChart>
        </div>
    </vx-card>
</div>
</template>

<script>
import {
    graficasAltura,
    graficasPeso,
    graficasPesoAltura,
    graficasCircunferencia,
    sharingStatisticsInfo,
    AlturaNiños,
    AlturaNiñas,
    PesoNiños,
    PesoNiñas,
    PesoAlturaNiños,
    PesoAlturaNiñas,
    CircunferenciaNiños,
    CircunferenciaNiñas,
    MenuGraficas
} from './data';

export default {
    name: 'Graficas',
    components: {},
    data() {
        return {
            graficasAltura,
            graficasPeso,
            graficasPesoAltura,
            graficasCircunferencia,
            sharingStatisticsInfo,
            AlturaNiños,
            AlturaNiñas,
            PesoNiños,
            PesoNiñas,
            PesoAlturaNiños,
            PesoAlturaNiñas,
            CircunferenciaNiños,
            CircunferenciaNiñas,
            MenuGraficas,
            series: [],
            paciente: [],
            rangoMinimo: 144,
            rangoMaximo: 0,
            graficaSeleccionada: 0,
            graficasLineas: null,
            tituloGrafica: '',
            ejeY: '',
            ejeX: '',
            rango: []
        }
    },
    props: {
        Sexo: null,
        IdCita: null,
        IdCliente: null,
        IdPaciente: null,
        IdConsulta: null,

        FechaNacimiento: null,
        Edad: null
    },
    methods: {
        BusquedaSignosVitales() {
            if (this.IdCliente !== null && this.IdCliente !== '' && this.IdCliente !== undefined) {
                this.axios.post('/app/v1_Historial_Clinico/BusquedaSignosVitales', {
                        IdCliente: this.IdCliente,
                    })
                    .then(resp => {
                        if (this.graficaSeleccionada === 1) {
                            if (this.Sexo === 'M') {
                                this.series.push(...this.AlturaNiños)
                            }
                            if (this.Sexo === 'F') {
                                this.series.push(...this.AlturaNiñas)
                            }
                            this.graficasLineas = graficasAltura
                        } else if (this.graficaSeleccionada === 2) {
                            if (this.Sexo === 'M') {
                                this.series.push(...this.PesoNiños)
                            }
                            if (this.Sexo === 'F') {
                                this.series.push(...this.PesoNiñas)
                            }
                            this.graficasLineas = graficasPeso
                        } else if (this.graficaSeleccionada === 3) {
                            if (this.Sexo === 'M') {
                                this.series.push(...this.PesoAlturaNiños)
                            }
                            if (this.Sexo === 'F') {
                                this.series.push(...this.PesoAlturaNiñas)
                            }
                            this.graficasLineas = graficasPesoAltura
                        } else if (this.graficaSeleccionada === 4) {
                            if (this.Sexo === 'M') {
                                this.series.push(...this.CircunferenciaNiños)
                            }
                            if (this.Sexo === 'F') {
                                this.series.push(...this.CircunferenciaNiñas)
                            }
                            this.graficasLineas = graficasCircunferencia
                        }
                        let cont = 0

                        resp.data.json.map((x) => {
                            let valor = {}

                            if (this.graficaSeleccionada !== 4) {
                                if (parseFloat(x.Peso) > 0 && parseFloat(x.Estatura) > 1) {

                                    //Se convierten los valores a números
                                    // valor.Metabolismo = parseInt(x.Metabolismo)
                                    // valor.Sistolica = parseInt(x.Sistolica)
                                    // valor.Diastolica = parseInt(x.Diastolica)
                                    // valor.CircunferenciaCefalica = parseInt(x.CircunferenciaCefalica)
                                    // valor.ComplexionFisica = parseInt(x.ComplexionFisica)
                                    // valor.FrecuenciaRespiratoria = parseInt(x.FrecuenciaRespiratoria)
                                    // valor.EdadMetabolica = parseInt(x.EdadMetabolica)
                                    // valor.Pulso = parseInt(x.Pulso)

                                    valor.Peso = this.calcularKilos(parseFloat(x.Peso))
                                    // valor.Temperatura = parseFloat(x.Temperatura)
                                    valor.Estatura = parseFloat(x.Estatura)
                                    // valor.MasaOsea = parseFloat(x.MasaOsea)
                                    // valor.Musculo = parseFloat(x.Musculo)
                                    // valor.Grasa = parseFloat(x.Grasa)
                                    // valor.Agua = parseFloat(x.Agua)
                                    // valor.GrasaVisceral = parseFloat(x.GrasaVisceral)
                                    valor.Fecha = x.Fecha

                                    valor.Mes = this.obtenerEdad(this.FechaNacimiento, new Date(x.Fecha))

                                    this.series.push(valor)
                                    if (this.graficaSeleccionada !== 3) {
                                        if (parseInt(valor.Mes) <= 144) {
                                            if (parseInt(valor.Mes) > this.rangoMaximo) {
                                                this.rangoMaximo = valor.Mes
                                            }
                                        } else {
                                            this.rangoMaximo = 144
                                        }

                                        if (parseInt(valor.Mes) < this.rangoMinimo) {
                                            this.rangoMinimo = valor.Mes
                                        }
                                    } else {
                                        if (parseFloat(valor.Estatura) > this.rangoMaximo) {
                                            this.rangoMaximo = valor.Estatura
                                        }

                                        if (parseFloat(valor.Estatura) < this.rangoMinimo) {
                                            this.rangoMinimo = valor.Estatura
                                        }
                                    }
                                    cont++;
                                }
                            } else if (this.graficaSeleccionada === 4) {
                                if (parseFloat(x.CircunferenciaCefalica) > 30 && this.obtenerEdad(this.FechaNacimiento, new Date(x.Fecha)) <= 36) {
                                    valor.Circunferencia = parseInt(x.CircunferenciaCefalica)
                                    valor.Fecha = x.Fecha

                                    valor.Mes = this.obtenerEdad(this.FechaNacimiento, new Date(x.Fecha))

                                    this.series.push(valor)
                                    cont++;
                                }
                                this.rangoMinimo = 0
                                this.rangoMaximo = 36
                            }
                        })

                        if (cont == 0) {
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#ed8c72',
                                title: 'No hay datos válidos',
                                acceptText: 'Aceptar',
                                text: 'No se han ingresado datos válidos para poder graficar',
                                buttonCancel: 'border',
                                clientWidth: 100,
                                accept: () => {},
                            })
                        }

                        this.rango = [this.rangoMinimo, this.rangoMaximo]
                    })

            }
        },
        OpcionSeleccionada(item) {
            this.LimpiarVariables()

            this.graficaSeleccionada = item.id
            this.ejeX = item.ejeX
            this.ejeY = item.ejeY
            this.tituloGrafica = item.title

            this.BusquedaSignosVitales()
        },
        calcularKilos(libras) {
            return (libras * 0.45359237).toFixed(2)
        },
        diasEnElMesAnterior(fecha) {
            fecha = new Date(fecha)
            let clon = new Date(fecha);
            clon.setMonth(fecha.getMonth(), 0)
            return clon.getDate();
        },

        diasEnElMes(fecha) {
            fecha = new Date(fecha)
            let clon = new Date(fecha);
            clon.setMonth(fecha.getMonth() + 1, 0)
            return clon.getDate();
        },
        obtenerCumpleañosDeEsteAño(fechaDeNacimientoComoCadena, fecha) {
            fecha = new Date(fecha)
            let cumpleañosEsteAño = new Date(fechaDeNacimientoComoCadena);
            cumpleañosEsteAño.setFullYear(fecha.getFullYear());
            return cumpleañosEsteAño;
        },
        obtenerAños(nacimiento, ahora, cumpleañosEsteAño) {
            nacimiento = new Date(nacimiento)
            ahora = new Date(ahora)
            cumpleañosEsteAño = new Date(cumpleañosEsteAño)
            let años = ahora.getFullYear() - nacimiento.getFullYear();
            if (ahora.getTime() < cumpleañosEsteAño.getTime()) {
                años--;
            }
            return años;
        },
        obtenerMeses(ahora, cumpleañosEsteAño) {
            let meses = 0;
            ahora = new Date(ahora)
            cumpleañosEsteAño = new Date(cumpleañosEsteAño)
            if (ahora.getTime() < cumpleañosEsteAño.getTime()) {
                meses = ahora.getMonth() + 12 - cumpleañosEsteAño.getMonth();
            } else {
                meses = ahora.getMonth() - cumpleañosEsteAño.getMonth();
            }
            if (ahora.getDate() < cumpleañosEsteAño.getDate()) {
                meses--;
            }
            return meses;
        },
        obtenerDias(ahora, cumpleañosEsteAño) {
            ahora = new Date(ahora)
            cumpleañosEsteAño = new Date(cumpleañosEsteAño)
            if (ahora.getTime() < cumpleañosEsteAño.getTime()) {
                const conteoDeDiasDelMesAnterior = this.diasEnElMesAnterior(cumpleañosEsteAño);
                const diaDeNacimiento = cumpleañosEsteAño.getDate();
                const diaDelMesActual = ahora.getDate();
                let diasQueVivioEnElMesAnterior = conteoDeDiasDelMesAnterior - diaDeNacimiento;
                if (diasQueVivioEnElMesAnterior < 0) {
                    diasQueVivioEnElMesAnterior = 0;
                }
                return diasQueVivioEnElMesAnterior + diaDelMesActual;
            } else {
                if (cumpleañosEsteAño.getDate() <= ahora.getDate()) {
                    return ahora.getDate() - cumpleañosEsteAño.getDate();
                } else {
                    let diasQueVivioEnElMesAnterior = this.diasEnElMesAnterior(ahora) - cumpleañosEsteAño.getDate();
                    if (diasQueVivioEnElMesAnterior < 0) {
                        diasQueVivioEnElMesAnterior = 0;
                    }
                    return diasQueVivioEnElMesAnterior + ahora.getDate();
                }
            }
        },
        obtenerEdad(fechaDeNacimientoComoCadena, fechaActual) {
            fechaDeNacimientoComoCadena = new Date(fechaDeNacimientoComoCadena)
            fechaActual = new Date(fechaActual)
            const ahora = fechaActual.getTime();
            const cumpleañosEsteAño = this.obtenerCumpleañosDeEsteAño(fechaDeNacimientoComoCadena, ahora);
            const años = this.obtenerAños(fechaDeNacimientoComoCadena, ahora, cumpleañosEsteAño);
            const meses = this.obtenerMeses(ahora, cumpleañosEsteAño);
            // const dias = this.obtenerDias(ahora, cumpleañosEsteAño);

            const mesesFecha = meses + (años * 12)
            return mesesFecha
        },
        LimpiarVariables() {
            this.graficaSeleccionada = 0
            this.series = []
            this.graficasLineas = []

            this.rangoMinimo = 144
            this.rangoMaximo = 0
        },

        onPointHoverChanged(e) {
            const point = e.target;

            if (!point.isHovered()) {
                point.hideTooltip();
            }
        }
    },
    mounted() {
        // this.BusquedaSignosVitales()
    },
    watch: {
        'IdCliente'(newval) {
            if (newval !== '' && newval !== null && newval !== undefined) {
                // this.BusquedaSignosVitales()
            }
        },
    },
    computed: {

    },
}
</script>

<style scoped>
.buttonGrafica {
    height: 150px;
    width: 150px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 20px !important;
}

.buttonGrafica:hover {
    background-color: blue !important;
}

.i-size {
    font-size: 40px;
    padding-bottom: 5px;
}

.options {
    padding: 20px;
    background-color: rgba(191, 191, 191, 0.15);
    margin-top: 20px;
}

.option {
    margin-top: 10px;
}

.caption {
    font-size: 18px;
    font-weight: 500;
}

.option>span {
    margin-right: 14px;
}

.option>.dx-widget {
    display: inline-block;
    vertical-align: middle;
}
</style>
