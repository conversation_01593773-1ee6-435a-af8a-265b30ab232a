<template>
    <div>
        <vx-card :title="`Cirugías - ${sesion.sesion_sucursal_nombre}`">
            <div v-if="this.permisos.agregar || this.permisos.editar">
                <vs-divider position="left">{{ texto }}</vs-divider>
                <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                    <form method="post" @submit.prevent="handleSubmit(Guardar().Nuevo())">
                        <div class="flex flex-wrap mb-1">
                            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1 ">
                                <ValidationProvider name="Cirugía" rules="alpha_spaces|required|max:60"
                                    v-slot="{ errors }" class="required">
                                    <vs-input label="Nombre Cirugía" type="text" class="w-full"
                                        v-model="info.cirugia.Nombre" :disabled="bloquear" :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" required />
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1 ">
                                <ValidationProvider name="Tiempo" rules="required|between:0.01,99.99|max:4"
                                    v-slot="{ errors }" class="required">
                                    <vs-input label="Tiempo aproximado (horas)" type="number" class="w-full"
                                        v-model="info.cirugia.TiempoPromedio" :disabled="bloquear"
                                        :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" required />
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1 ">
                                <vs-button color="success" class="mr-1 mt-5"
                                    @click="texto == 'Agregar' ? handleSubmit(Guardar().Nuevo(invalid)) : handleSubmit(Guardar().Actualizar(invalid))">
                                    Guardar
                                </vs-button>
                                <vs-button class="mr-1 mt-5" color="warning" @click="Limpiar()">
                                    Limpiar
                                </vs-button>
                            </div>
                        </div>
                    </form>
                </ValidationObserver>
            </div>
            <vs-table2 max-items="10" tooltip search pagination :data="cirugias" v-if="permisos.visualizar">
                <template slot="thead">
                    <th order="Nombre">Nombre Cirugia</th>
                    <th order="Tiempo">Tiempo aproximado (horas)</th>
                    <th order="Estado" v-if="permisos.editar">Estado</th>
                    <th v-if="permisos.editar"></th>
                </template>
                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">
                        <vs-td2>
                            {{ tr.Nombre }}
                        </vs-td2>
                        <vs-td2>
                            {{ tr.TiempoPromedio }}
                        </vs-td2>
                        <vs-td2 width="50px" v-if="permisos.editar">
                            <vs-switch title="Cambiar Estado" v-model="tr.Estado" @click="CambioEstado(tr)" />
                        </vs-td2>
                        <vs-td2 width="50px" v-if="permisos.editar">
                            <vs-button title="Editar" color="primary" size="small" icon-pack="fas" icon="fa-edit"
                                class="mr-1" style="display:inline-block" @click="Actualizar(tr)"></vs-button>
                        </vs-td2>

                    </tr>
                </template>
            </vs-table2>
        </vx-card>
    </div>
</template>

<script>
export default {
    data() {
        return {
            /**
             * Documentación del módulo
             */
            doc_: [
                'Pantalla de administración de cirugías',
            ],
            info: {
                cirugia: {
                    Opcion: "",
                    SubOpcion: "",
                    IdCirugia: 0,
                    Nombre: "",
                    TiempoPromedio: 1,
                    Empresa: this.$store.state.sesion.sesion_empresa,
                    Estado: true
                }
            },
            permisos: {
                agregar: false,
                editar: false,
                visualizar: false
            },
            cirugias: [],
            texto: 'Agregar',
            bloquear: false
        }
    },
    computed: {
        sesion() {
            return this.$store.state.sesion
        }
    },
    methods: {
        Consulta() {
            return {
                init: () => {
                    this.info.cirugia.Opcion = 'C';
                    this.info.cirugia.SubOpcion = '1';

                    this.axios.post('/app/salaoperaciones/ListarCirugias', this.info.cirugia)
                        .then(resp => {
                            this.cirugias = resp.data.json;
                        })
                }
            }
        },
        Guardar() {
            return {
                Nuevo: (formaIsInvalid) => {
                    this.info.cirugia.Opcion = 'I'
                    this.info.cirugia.SubOpcion = '1'

                    if (!formaIsInvalid) {
                        this.axios.post('/app/salaoperaciones/IngresarCirugia', this.info.cirugia)
                            .then(resp => {
                                if (resp.data.tipo_error == 0) {
                                    this.Limpiar();
                                    this.Consulta().init();
                                }
                            })
                    } else {
                        this.$vs.notify({
                            title: this.texto,
                            text: 'Formulario contiene errores',
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                    }
                },
                Actualizar: (formaIsInvalid) => {
                    if (!formaIsInvalid) {
                        this.axios.post('/app/salaoperaciones/ActualizarCirugia', this.info.cirugia)
                            .then(resp => {
                                if (resp.data.tipo_error == 0) {
                                    this.Limpiar();
                                    this.Consulta().init();
                                }
                            })
                    } else {
                        this.$vs.notify({
                            title: this.texto,
                            text: 'Formulario contiene errores',
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'danger',
                            position: 'bottom-center'
                        })
                    }
                },
                CambioEstado: () => {
                    this.axios.post('/app/salaoperaciones/DarBajaCirugia', this.info.cirugia)
                        .then(resp => {
                            if (resp.data.tipo_error == -1) {
                                this.Consulta().init();
                            }
                        })
                }
            }
        },
        Actualizar(data) {
            if (data.Estado) {
                this.info.cirugia.Opcion = 'A';
                this.info.cirugia.SubOpcion = '1';
                this.info.cirugia.IdCirugia = data.IdCirugia;
                this.info.cirugia.Nombre = data.Nombre;
                this.info.cirugia.TiempoPromedio = data.TiempoPromedio;
                this.texto = 'Actualizar';
            } else {
                this.$vs.notify({
                    time: 8000,
                    title: 'Edición',
                    text: 'El registro seleccionado esta inactivo',
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'danger',
                    position: 'bottom-center'
                })
            }
        },
        CambioEstado(x) {
            this.info.cirugia.Opcion = "E";
            this.info.cirugia.SubOpcion = "1";
            this.info.cirugia.IdCirugia = x.IdCirugia;
            this.info.cirugia.Estado = !x.Estado;

            this.Guardar().CambioEstado();
        },
        Limpiar() {
            if (this.texto == "Actualizar") { this.texto = "Agregar" }

            this.info.cirugia.IdCirugia = 0;
            this.info.cirugia.Nombre = "";
            this.info.cirugia.TiempoPromedio = 1;
        },
        Otros: function () {
            return {
                MensajeError: (valido) => {
                    if (valido)
                        this.$vs.dialog({
                            color: 'danger',
                            title: 'Cirugías - Validación',
                            text: "Existen campos obligatorios"
                        })
                }
            }
        }
    },
    mounted() {
        this.permisos.agregar = this.$validar_privilegio('AGREGAR').status
        this.permisos.editar = this.$validar_privilegio('EDITAR').status
        this.permisos.visualizar = this.$validar_privilegio('VISUALIZAR').status
        this.Consulta().init()
    }
}
</script>