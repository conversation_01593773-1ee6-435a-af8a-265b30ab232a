<template>
    <vx-card title="Proveedores">
        <div class="content content-pagex">
            <!-- <div class="terms">
                <vs-button color="primary" type="filled" icon-pack="fas" icon="fa-plus"
                    @click="Abrir_Ventana_Emergente_Nuevo()"> Nuevo</vs-button>
            </div>
            <div>
                <vs-divider />
                <div class="flex flex-wrap">
                    <div class="w-full md:w-full lg:w-full xl:w-full">
                        <vs-table2 max-items="40" pagination :data="Lista_proveedores"
                            noDataText="Sin datos disponibles" search id="tb_proveedores">
                            <template slot="thead">
                                <th>Código</th>
                                <th>Nit</th>
                                <th>Nombre</th>
                                <th>Telefono</th>
                                <th>Correo E.</th>
                                <th style="text-align: center;">Estado</th>
                                <th></th>
                            </template>
<template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2 width="50px" :data="data[indextr].CODIGO">
                                        {{ data[indextr].CODIGO }}
                                    </vs-td2>
                                    <vs-td2 width="120px" :data="data[indextr].NIT">
                                        {{ data[indextr].NIT }}
                                    </vs-td2>
                                    <vs-td2 width="300px" :data="data[indextr].NOMBRE">
                                        {{ data[indextr].NOMBRE }}
                                    </vs-td2>
                                    <vs-td2 width="120px" :data="data[indextr].TELEFONOS">
                                        {{ data[indextr].TELEFONOS }}
                                    </vs-td2>
                                    <vs-td2 width="200px" :data="data[indextr].EMAIL">
                                        {{ data[indextr].EMAIL }}
                                    </vs-td2>
                                    <vs-td2 width="200px" :data="data[indextr].EMAIL">
                                        {{ (data[indextr].ACTIVO == 'S' ? 'Activo' : 'No Activo') }}
                                    </vs-td2>
                                    <vs-td2 width="200px">
                                        <vs-button color="primary" icon-pack="feather" icon="icon-edit-1"
                                            style="display:inline-block;margin-right:2px"
                                            @click="Abrir_Ventana_Emergente_Editar(data[indextr])"></vs-button>
                                        <vs-button color="warning" icon-pack="fas" icon="fa-info"
                                            style="display:inline-block;margin-right:2px"
                                            @click="Informacion_Registro(data[indextr])"></vs-button>
                                    </vs-td2>
                                </tr>
                            </template>
</vs-table2>
</div>
</div>
</div>
</div> -->
            <!--------------- Nuevo / Editar / Información   (Ventana Emergente)-------------->
            <!-- <vs-popup classContent="popup-example" :title="Titulo_emergente" :active.sync="Estado_VentanaEmergente"> -->
            <div class="flex flex-wrap mb-1">
                <div class="w-full p-1">
                    <vs-alert color="danger" :active.sync="nitInvalido" style="text-align:center;height:40px">
                        {{ TituloError }}
                    </vs-alert>
                    <vs-alert color="success" :active.sync="proveedorIngresado" style="text-align:center;height:40px">
                        {{ TituloAviso }}
                    </vs-alert>
                </div>
            </div>
            <div>
                <!-- style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px" -->
                <form>
                    <vs-divider> Datos </vs-divider>
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <!-- <ValidationProvider name="Código" rules="required|max:12" v-slot="{ errors }"
                                class="required">
                                <vs-input label="Código" class="w-full" count="12" v-model="codigo"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    :disabled="Deshabilitar_campo_codigo" id="txt_codigo" />
                            </ValidationProvider>
                            <ValidationProvider name="BuscarProveedor" rules="required" v-slot="{ errors }"
                                class="required">
                                <SM-Buscar v-model="proveedor.CodigoProveedor" label="Proveedor"
                                    api="app/v2_api_compras/BuscarProveedor" :api_campos="['CODIGO', 'NOMBRE', 'NIT']"
                                    :api_titulos="['Código', 'Nombre', 'NIT']" api_campo_respuesta="CODIGO"
                                    :danger="errors.length > 0" :callback_buscar="Consulta().ListarCompras"
                                    :dangertext="(errors.length > 0) ? errors[0] : null" :disabled_editar="true" />
                            </ValidationProvider> -->
                            <ValidationProvider name="Codigo" rules="max:6" v-slot="{ errors }"
                                class="required">
                                <SM-Buscar v-model="codigo" label="Código del proveedor"
                                    api="app/v2_api_compras/BuscarProveedor" :api_campos="['NIT', 'CODIGO', 'NOMBRE']"
                                    :api_titulos="['NIT', 'Código', 'Nombre']" api_campo_respuesta="CODIGO"
                                    :callback_buscar="Informacion_Registro" :callback_cancelar="LimpiarCampos"
                                    :disabled_texto="Deshabilitar_campo_multiempresa"
                                    :callback_nuevo="Abrir_Ventana_Emergente_Nuevo"
                                    :disabled_search_input="!Deshabilitar_campo_multiempresa"
                                    :callback_editar="Abrir_Ventana_Emergente_Editar" :danger="errors.length > 0"
                                    :dangertext="(errors.length > 0) ? errors[0] : null" />
                                <!-- :callback_nuevo="(permisos.crear) ? Nuevo().Ajeno_Nuevo : null"
                                        :callback_editar="(e) => { editar = true }"
                                        :callback_cancelar="Otros().Limpiar_Datos" :disabled_texto="!nuevo"
                                        :danger="errors.length > 0" :dangertext="(errors.length > 0) ? errors[0] : null"
                                        :disabled_editar="editar" :disabled_search_input="nuevo" -->
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <ValidationProvider name="Nit" rules="required|max:10" v-slot="{ errors }" class="required">
                                <vs-input label="Nit" class="w-full" count="10" v-model="nit"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    :disabled="Deshabilitar_campo_multiempresa" @blur="ValidarNit()" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <ValidationProvider name="Nombre" rules="max:80" v-slot="{ errors }" class="required">
                                <vs-input label="Nombre" class="w-full" count="80" v-model="nombre"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    :disabled="Deshabilitar_campo_multiempresa" />
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <ValidationProvider name="Fax" rules="max:8" v-slot="{ errors }">
                                <vs-input label="Fax" class="w-full" count="8" v-model="fax" :danger="errors.length > 0"
                                    :danger-text="(errors.length > 0) ? errors[0] : null"
                                    :disabled="Deshabilitar_campo_multiempresa" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <ValidationProvider name="Teléfono" rules="max:25" v-slot="{ errors }">
                                <vs-input label="Teléfono" class="w-full" count="25" v-model="telefono"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    :disabled="Deshabilitar_campo_multiempresa" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <ValidationProvider name="Correo_electronico" rules="max:30" v-slot="{ errors }">
                                <vs-input type="email" label="Correo Electrónico" class="w-full" count="30"
                                    v-model="email" :danger="errors.length > 0"
                                    :danger-text="(errors.length > 0) ? errors[0] : null"
                                    :disabled="Deshabilitar_campo_multiempresa" />
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <ValidationProvider name="Linea" rules="max:40" v-slot="{ errors }">
                                <vs-input label="Productos" class="w-full" count="40" v-model="linea"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    :disabled="Deshabilitar_campo_multiempresa" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <ValidationProvider name="Contacto" rules="max:60" v-slot="{ errors }">
                                <vs-input label="Contacto" class="w-full" count="60" v-model="contacto"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    :disabled="Deshabilitar_campo_multiempresa" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <ValidationProvider name="Contacto_trato" rules="max:6" v-slot="{ errors }">
                                <vs-input label="Número Contacto" class="w-full" count="6" v-model="contacto_trato"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    :disabled="Deshabilitar_campo_multiempresa" />
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <ValidationProvider name="Cuentabanco" rules="max:15" v-slot="{ errors }">
                                <vs-input label="Cuenta Banco" class="w-full" count="15" v-model="cuenta_banco"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    :disabled="Deshabilitar_campo_multiempresa" />
                            </ValidationProvider>
                        </div>
                        <!-- <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <ValidationProvider name="CodigoHBA" rules="max:80" v-slot="{ errors }">
                                <vs-input label="A Nombre de:" class="w-full" count="80" v-model="codigo_hba"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    :disabled="Deshabilitar_campo_multiempresa" />
                            </ValidationProvider>
                        </div> -->
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <ValidationProvider name="Ciudad" rules="max:30" v-slot="{ errors }">
                                <vs-input label="Ciudad" class="w-full" count="30" v-model="ciudad"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    :disabled="Deshabilitar_campo_multiempresa" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <ValidationProvider name="Direccion" rules="max:60" v-slot="{ errors }">
                                <vs-input label="Dirección" class="w-full" count="60" v-model="direccion"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    :disabled="Deshabilitar_campo_multiempresa" />
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <ValidationProvider name="Cheque_A" rules="max:80" v-slot="{ errors }">
                                <vs-input label="Cheque a Nombre de" class="w-full" count="80" v-model="nombre_cheque"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    :disabled="Deshabilitar_campo_multiempresa" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <ValidationProvider name="cb_tipo_proveedor" rules="required" v-slot="{ errors }"
                                class="required">
                                <label style="font-size:12px">Tipo Proveedor</label>
                                <multiselect v-model="cb_tipo_proveedor" :options="Lista_tipo_proveedor"
                                    :searchable="true" :close-on-select="true" :show-labels="false"
                                    :custom-label="TipoProveedor_seleccionado" placeholder="Búsqueda tipo proveedor"
                                    @input="onChangeTipoProveedor" :disabled="Deshabilitar_campo_multiempresa"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null">
                                    <span slot="noOptions">Lista no disponible.</span>
                                </multiselect>
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3  p-1">
                            <ValidationProvider name="DiasCredito" rules="max:80" v-slot="{ errors }">
                                <vs-input label="Días de Crédito" class="w-full" count="80" v-model="DiasCredito"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    :disabled="Deshabilitar_campo_multiempresa" />
                            </ValidationProvider>
                        </div>
                    </div>
                    <!----- INICIO SWITCH --->
                    <div style="margin: 15px" class="flex flex-wrap">
                        <div class="w-full sm:w-1/4" style="margin-bottom:15px">
                            <label>Proveedor de Servicios</label>
                            <vs-switch v-model="es_proveedor_servicio" :disabled="Deshabilitar_campo_multiempresa">
                            </vs-switch>
                        </div>
                        <div class="w-full sm:w-1/4" style="margin-bottom:15px">
                            <label>Agente Retenedor</label>
                            <vs-switch v-model="es_agente_retenedor" :disabled="Deshabilitar_campo_multiempresa">
                            </vs-switch>
                        </div>
                        <div class="w-full sm:w-1/4" style="margin-bottom:15px">
                            <label>Proveedor del exterior</label>
                            <vs-switch v-model="es_proveedor_exterior" :disabled="Deshabilitar_campo_multiempresa"
                                @input="ValidarNit()">
                            </vs-switch>
                        </div>
                        <div class="w-full sm:w-1/4" style="margin-bottom:15px">
                            <label>Activo</label>
                            <vs-switch v-model="activo" :disabled="Deshabilitar_campo_multiempresa">
                            </vs-switch>
                        </div>
                        <!-- <div class="w-full sm:w-1/4" style="margin-bottom:15px">
                            <label>Autorizado</label>
                            <vs-switch v-model="es_proveedor_aprobado" :disabled="Deshabilitar_campo_multiempresa">
                            </vs-switch>
                        </div> -->
                    </div>

                    <!---- FIN SWITCH --->

                    <!---<vs-divider v-if="!Deshabilitar_campo_uniempresa" >Otros datos</vs-divider> -->
                    <vs-divider>Otros Datos</vs-divider>
                    <!---style="visibility: hidden;"--->
                    <!--- <SM-Tabs v-if="!Deshabilitar_campo_uniempresa">-->
                    <SM-Tabs>

                        <div label="Crédito">
                            <div class="tab-text card-border p-2">
                                <vs-card>
                                    <vs-divider>Datos Crédito</vs-divider>
                                    <!---ACEPTA CREDITO -->
                                    <div class="flex flex-wrap">
                                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                            <div class="w-full sm:w-1/2" style="margin-bottom:15px">
                                                <label>Crédito</label>
                                                <vs-switch v-model="credito" :disabled="Deshabilitar_campo_multiempresa"
                                                    @change="onChangeCredito">
                                                </vs-switch>
                                            </div>
                                        </div>
                                    </div>

                                    <!---DESCUENTO / LIMITE DESCUENTO -->
                                    <div class="flex flex-wrap">
                                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                            <ValidationProvider name="Descuento" rules="required|numero_min:0"
                                                v-slot="{ errors }" class="required">
                                                <vs-input label="Descuento %" class="w-full" type="number"
                                                    v-model="descuento" :danger="errors.length > 0"
                                                    :danger-text="(errors.length > 0) ? errors[0] : null"
                                                    :disabled="Deshabilitar_campo_credito" />
                                            </ValidationProvider>
                                        </div>
                                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                            <ValidationProvider name="limite_descuento" rules="required|max:40"
                                                v-slot="{ errors }" class="required">
                                                <vs-input label="Límite Descuento" class="w-full" type="number"
                                                    v-model="limite_descuento" :danger="errors.length > 0"
                                                    :danger-text="(errors.length > 0) ? errors[0] : null"
                                                    :disabled="Deshabilitar_campo_credito" />
                                            </ValidationProvider>
                                        </div>
                                    </div>

                                    <!---LIMITE CREDITO / VENCE CREDITO-->
                                    <div class="flex flex-wrap">
                                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                            <ValidationProvider name="LimiteCredito" rules="required|numero_min:0"
                                                v-slot="{ errors }" class="required">
                                                <vs-input label="Límite Crédito" class="w-full" type="number"
                                                    v-model="limite_credito" :danger="errors.length > 0"
                                                    :danger-text="(errors.length > 0) ? errors[0] : null"
                                                    :disabled="Deshabilitar_campo_credito" />
                                            </ValidationProvider>
                                        </div>
                                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                            <ValidationProvider name="vence_credito" rules="required|numero_min:0"
                                                v-slot="{ errors }" class="required">
                                                <vs-input label="Vence Crédito" class="w-full" type="number"
                                                    v-model="vence_credito" :danger="errors.length > 0"
                                                    :danger-text="(errors.length > 0) ? errors[0] : null"
                                                    :disabled="Deshabilitar_campo_credito" />
                                            </ValidationProvider>
                                        </div>
                                    </div>
                                </vs-card>
                            </div>
                        </div>

                        <div label="Compras">

                            <!---*****************Contabilidad proveedores ******************-->
                            <div class="tab-text card-border p-2">
                                <vs-card>
                                    <vs-divider>Contabilidad Proveedores</vs-divider>

                                    <!---COMPRAS / CREDITO -->
                                    <div class="flex flex-wrap">
                                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                            <ValidationProvider name="Conta_compras" rules="max:12" v-slot="{ errors }">
                                                <vs-input label="Cuenta Compras" class="w-full" count="12"
                                                    v-model="conta_compra" :danger="errors.length > 0"
                                                    :danger-text="(errors.length > 0) ? errors[0] : null"
                                                    :disabled="Deshabilitar_campo_multiempresa" />
                                            </ValidationProvider>
                                        </div>
                                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                            <ValidationProvider name="CrEdito" rules="max:12" v-slot="{ errors }">
                                                <vs-input label="Cuenta Crédito" class="w-full" count="12"
                                                    v-model="conta_credito" :danger="errors.length > 0"
                                                    :danger-text="(errors.length > 0) ? errors[0] : null"
                                                    :disabled="Deshabilitar_campo_multiempresa" />
                                            </ValidationProvider>
                                        </div>
                                    </div>

                                    <!---ANTICIPO -->
                                    <div class="flex flex-wrap">
                                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                            <ValidationProvider name="Anticipo" rules="max:12" v-slot="{ errors }">
                                                <vs-input label="Cuenta Anticipo" class="w-full" count="12"
                                                    v-model="conta_anticipo" :danger="errors.length > 0"
                                                    :danger-text="(errors.length > 0) ? errors[0] : null"
                                                    :disabled="Deshabilitar_campo_multiempresa" />
                                            </ValidationProvider>
                                        </div>
                                    </div>
                                </vs-card>
                            </div>

                            <!---*****************Contabilidad Honorarios Medicos ******************-->
                            <div class="tab-text card-border p-2">
                                <vs-card>
                                    <vs-divider>Contabilidad Honorarios Médicos</vs-divider>

                                    <!---COMPRAS / CREDITO -->
                                    <div class="flex flex-wrap">
                                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                            <ValidationProvider name="Conta_compras_medico" rules="max:12"
                                                v-slot="{ errors }">
                                                <vs-input label="Cuenta Compras" class="w-full" count="12"
                                                    v-model="conta_compra_medicos" :danger="errors.length > 0"
                                                    :danger-text="(errors.length > 0) ? errors[0] : null"
                                                    :disabled="Deshabilitar_campo_multiempresa" />
                                            </ValidationProvider>
                                        </div>
                                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                            <ValidationProvider name="conta_credito_medicos" rules="max:12"
                                                v-slot="{ errors }">
                                                <vs-input label="Cuenta Crédito" class="w-full" count="12"
                                                    v-model="conta_credito_medicos" :danger="errors.length > 0"
                                                    :danger-text="(errors.length > 0) ? errors[0] : null"
                                                    :disabled="Deshabilitar_campo_multiempresa" />
                                            </ValidationProvider>
                                        </div>
                                    </div>

                                </vs-card>
                            </div>
                        </div>

                        <div label="Retención">
                            <!---*****************Contabilidad Datos de retencion ******************-->
                            <div class="tab-text card-border p-2">
                                <vs-card>
                                    <vs-divider>Datos Retención</vs-divider>

                                    <!---TIPO RETENCION / PORCENTAJE -->
                                    <div class="flex flex-wrap">
                                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                            <ValidationProvider name="cb_tipo_retencion" rules="required"
                                                v-slot="{ errors }">
                                                <label style="font-size:12px">Tipo Retención</label>
                                                <multiselect v-model="cb_tipo_retencion" :options="Lista_tipo_retencion"
                                                    :searchable="false" :close-on-select="true" :show-labels="false"
                                                    :custom-label="TipoRetencion_seleccionado"
                                                    placeholder="Búsqueda tipo retención" @input="onChangeTipoRetencion"
                                                    :disabled="Deshabilitar_campo_multiempresa"
                                                    :danger="errors.length > 0"
                                                    :danger-text="(errors.length > 0) ? errors[0] : null">
                                                    <span slot="noOptions">Lista no disponible.</span>
                                                </multiselect>
                                            </ValidationProvider>
                                        </div>
                                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                            <ValidationProvider name="Porcentaje" v-slot="{ errors }">
                                                <vs-input label="Porcentaje" class="w-full" v-model="retencion"
                                                    :danger="errors.length > 0"
                                                    :danger-text="(errors.length > 0) ? errors[0] : null"
                                                    :disabled="true" />
                                            </ValidationProvider>
                                        </div>
                                    </div>

                                    <!---CONTABILIDAD / NIT -->
                                    <div class="flex flex-wrap">
                                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                            <ValidationProvider name="Contabilidad" rules="max:12" v-slot="{ errors }">
                                                <vs-input label="Cuenta Retención" class="w-full" count="12"
                                                    v-model="cuenta_retencion" :danger="errors.length > 0"
                                                    :danger-text="(errors.length > 0) ? errors[0] : null"
                                                    :disabled="Deshabilitar_campo_multiempresa" />
                                            </ValidationProvider>
                                        </div>
                                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                            <ValidationProvider name="NIT_Retencion" rules="max:10" v-slot="{ errors }">
                                                <vs-input label="Nit Retencíon" class="w-full" count="10"
                                                    v-model="nit_retencion" :danger="errors.length > 0"
                                                    :danger-text="(errors.length > 0) ? errors[0] : null"
                                                    :disabled="Deshabilitar_campo_multiempresa" />
                                            </ValidationProvider>
                                        </div>
                                    </div>
                                </vs-card>
                            </div>

                            <!---*****************Dirección de retención ******************-->
                            <div class="tab-text card-border p-2">
                                <vs-divider>Dirección</vs-divider>

                                <!---Departamento /  Municipio -->
                                <div class="flex flex-wrap">
                                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">

                                        <ValidationProvider name="cb_departamento_geografico" v-slot="{ errors }">
                                            <label style="font-size:12px">Departamento</label>
                                            <multiselect v-model="cb_departamento_geografico"
                                                :options="Lista_departamento_geografico" :searchable="true"
                                                :close-on-select="true" :show-labels="false"
                                                :custom-label="DepartamentoGeografico_seleccionado"
                                                placeholder="Búsqueda departamento"
                                                @input="onChangeDepartamentoGeografico"
                                                :disabled="Deshabilitar_campo_multiempresa" :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null">
                                                <span slot="noOptions">Lista no disponible.</span>
                                            </multiselect>
                                        </ValidationProvider>
                                    </div>
                                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                        <ValidationProvider name="Municipio" rules="max:50" v-slot="{ errors }">
                                            <vs-input label="Municipio" class="w-full" count="50"
                                                v-model="direccion_municipio" :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null"
                                                :disabled="Deshabilitar_campo_multiempresa" />
                                        </ValidationProvider>
                                    </div>
                                </div>

                                <!---Calle /  Número -->
                                <div class="flex flex-wrap">
                                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                        <ValidationProvider name="Calle" rules="max:50" v-slot="{ errors }">
                                            <vs-input label="Calle" class="w-full" count="50" v-model="direccion_calle"
                                                :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null"
                                                :disabled="Deshabilitar_campo_multiempresa" />
                                        </ValidationProvider>
                                    </div>

                                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                        <ValidationProvider name="Numero" rules="max:10" v-slot="{ errors }">
                                            <vs-input label="Número" class="w-full" count="10"
                                                v-model="direccion_numero" :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null"
                                                :disabled="Deshabilitar_campo_multiempresa" />
                                        </ValidationProvider>
                                    </div>
                                </div>

                                <!---Apartamento /  Zona -->
                                <div class="flex flex-wrap">
                                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                        <ValidationProvider name="Apartamento" rules="max:10" v-slot="{ errors }">
                                            <vs-input label="Apartamento" class="w-full" count="10"
                                                v-model="direccion_apto" :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null"
                                                :disabled="Deshabilitar_campo_multiempresa" />
                                        </ValidationProvider>
                                    </div>
                                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                        <ValidationProvider name="Zona" rules="required|numero_min:0"
                                            v-slot="{ errors }">
                                            <vs-input label="Zona" class="w-full" type="number" v-model="direccion_zona"
                                                :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null"
                                                :disabled="Deshabilitar_campo_multiempresa" />
                                        </ValidationProvider>
                                    </div>
                                </div>

                                <!---Colonia -->
                                <div class="flex flex-wrap">
                                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                                        <ValidationProvider name="Colonia" rules="max:50" v-slot="{ errors }">
                                            <vs-input label="Colonia" class="w-full" count="50"
                                                v-model="direccion_colonia" :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null"
                                                :disabled="Deshabilitar_campo_multiempresa" />
                                        </ValidationProvider>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </SM-Tabs>

                    <vs-divider></vs-divider>
                    <vs-button v-if="Operacion == 'N' || Operacion == 'A'" color="primary"
                        style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save"
                        id="btn_confirmacion" @click="Confirmacion_Transaccion()" :disabled="proveedorIngresado">
                        Grabar</vs-button>
                    <vs-divider></vs-divider>
                </form>
            </div>
        </div>
        <!--  </vs-popup> -->
        <!---------------Fin Ventana Emergente _---------->
        <!-----------------LLamanda de componenetes ----------------------->


    </vx-card>
</template>

<script>
/**
 * @General
 * Registro de proveedores, volcado de información por medio de archivo de excel e historial de compras
 */
import Multiselect from 'vue-multiselect'
import "vue-multiselect/dist/vue-multiselect.min.css"

export default {
    name: 'componente_Actualizar_Lista_Proveedores',

    created() {
        this.$root.$refs.A = this;
    },
    components: {
        Multiselect
    },
    data() {
        return {
            Lista_proveedores: [],

            Titulo_emergente: '',
            Estado_VentanaEmergente: false,

            //Campos para la bodega
            codigo: '',
            nit: '',
            nombre: '',
            nombre_cheque: '',
            activo: true,
            direccion: '',
            ciudad: '',
            telefono: '',
            fax: '',
            email: '',
            contacto_trato: '',
            contacto: '',
            linea: '',
            conta_compra: '',
            descuento: 0,
            limite_descuento: 0,
            credito: false,
            limite_credito: 0,
            vence_credito: 0,
            conta_anticipo: '',
            conta_credito: '',
            retencion: '',
            cuenta_retencion: '',
            nit_retencion: '',
            direccion_calle: '',
            direccion_numero: '',
            direccion_apto: '',
            direccion_zona: 0,
            direccion_colonia: '',
            direccion_municipio: '',
            conta_compra_medicos: '',
            conta_credito_medicos: '',
            codigo_hba: '',
            es_proveedor_servicio: false,
            es_agente_retenedor: false,
            es_proveedor_aprobado: false,
            tipo_contribuyente: '',
            es_proveedor_exterior: false,
            cuenta_banco: '',
            bloqueado: '',
            Estado_bloqueado: '',
            DiasCredito: '',
            proveedor_activo: '',
            //Listas 
            Lista_tipo_proveedor: [],
            cb_tipo_proveedor: '',
            id_tipo_proveedor_seleccionado: '',

            Lista_tipo_retencion: [],
            cb_tipo_retencion: '',
            id_tipo_retencion_seleccionado: '',

            Lista_departamento_geografico: [],
            cb_departamento_geografico: '',
            id_departamento_geografico_seleccionado: '',

            //Campos para Habilitar o deshabilitar todos los campos de la ventana emergente.
            Deshabilitar_campo_codigo: false,
            Deshabilitar_campo_multiempresa: true,
            Deshabilitar_campo_uniempresa: false,
            Deshabilitar_campo_credito: true,
            Operacion: '', // N = Nuevo Registro / A = Actualización registor

            Parametro_UniEmpresa: '',
            Parametro_Codigo_empresa_unica: '',
            Parametro_Descripcion_empresa_unica: '',

            nitInvalido: false,
            TituloError: null,
            proveedorIngresado: false,
            TituloAviso: null
        };
    },
    mounted() {
        //this.Consultar_proveedor(); //Carga  la tabla inicial
        this.Consultar_TipoProveedor();
        this.Consultar_TipoRetencion();
        this.Consultar_DepartamentoGeografico();
    },
    methods: {
        onChangeCredito() {
            //Si existe seleccionada un elemento
            if (this.credito) {
                this.Deshabilitar_campo_credito = false;
            } else {
                this.Deshabilitar_campo_credito = true;
                //Colocar valores por defecto
                this.descuento = 0;
                this.limite_descuento = 0;
                this.limite_credito = 0;
                this.vence_credito = 0;
            }
        },
        onChangeTipoProveedor(value) {
            //Si existe seleccionada un elemento
            if (value !== null && value.length !== 0) {
                this.id_tipo_proveedor_seleccionado = value.CODIGO;
            } else {
                this.id_tipo_proveedor_seleccionado = '';
            }
        },
        onChangeTipoRetencion(value) {
            //Si existe seleccionada un elemento
            if (value !== null && value.length !== 0) {
                this.id_tipo_retencion_seleccionado = value.CODIGO;
                this.retencion = value.PORCENTAJE;
            } else {
                this.id_tipo_retencion_seleccionado = '';
            }

        },
        onChangeDepartamentoGeografico(value) {
            //Si existe seleccionada un elemento
            if (value !== null && value.length !== 0) {
                this.id_departamento_geografico_seleccionado = value.IdDepartamento;
            } else {
                this.id_departamento_geografico_seleccionado = '';
            }
        },
        TipoProveedor_seleccionado({
            DESCRIPCION
        }) {
            return `${DESCRIPCION} `
        },
        TipoRetencion_seleccionado({
            DESCRIPCION
        }) {
            return `${DESCRIPCION}`
        },
        DepartamentoGeografico_seleccionado({
            Departamento
        }) {
            return `${Departamento} `
        },
        Empresa_seleccionada({
            codigo,
            nombre
        }) {
            return `${codigo} - ${nombre} `
        },
        /* Consultar_proveedor() {
            this.axios.post('/app/v2_api_compras/ListarProveedores', {})
                .then(resp => {

                    if (resp.data.codigo != 0) {

                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Proveedores',
                            text: resp.data.mensaje,
                        });
                        //Limpia la tabla si no existen registros
                        this.Lista_proveedores = "";
                    } else {
                        //Decodificación

                        this.Lista_proveedores = resp.data.json;

                    }
                })
                .catch(() => {

                })

        }, */
        Consultar_TipoProveedor() {
            const sesion = this.$store.state.sesion;

            this.axios.post('/app/v2_api_compras/ListaTipoProveedor', {
                empresa: sesion.sesion_empresa
            })
                .then(resp => {

                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Tipo Proveedor',
                            text: resp.data.mensaje,
                        });
                        //Limpia la tabla si no existen registros
                        this.Lista_tipo_proveedor = "";
                    } else {
                        //Decodificación

                        this.Lista_tipo_proveedor = resp.data.json;

                    }
                })
                .catch(() => {

                })


        },
        Consultar_TipoRetencion() {

            this.axios.post('/app/v2_api_compras/ListaTiposRetencion', {})
                .then(resp => {

                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Tipo Proveedor',
                            text: resp.data.mensaje,
                        });
                        //Limpia la tabla si no existen registros
                        this.Lista_tipo_retencion = "";
                    } else {
                        //Decodificación

                        this.Lista_tipo_retencion = resp.data.json;

                    }
                })
                .catch(() => {

                })


        },
        Consultar_DepartamentoGeografico() {

            this.axios.post('/app/v2_api_compras/ListaDepartamentosGeograficos', {})
                .then(resp => {

                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Tipo Proveedor',
                            text: resp.data.mensaje,
                        });
                        //Limpia la tabla si no existen registros
                        this.Lista_departamento_geografico = "";
                    } else {
                        //Decodificación

                        this.Lista_departamento_geografico = resp.data.json;

                    }
                })
                .catch(() => {

                })


        },
        Abrir_Ventana_Emergente_Nuevo() {
            this.proveedorIngresado = false
            this.TituloAviso = 'Proveedor ingresado correctamente'
            this.nitInvalido = false

            this.Titulo_emergente = 'Nuevo Proveedor';

            this.Estado_VentanaEmergente = true;

            //this.LimpiarCampos(true);
            this.Consultar_TipoProveedor();
            this.Consultar_TipoRetencion();
            this.Consultar_DepartamentoGeografico();

            //Estado de los campos de la ventana emergente
            this.Deshabilitar_campo_codigo = false;
            this.Deshabilitar_campo_multiempresa = false;
            this.Deshabilitar_campo_uniempresa = false;

            this.Operacion = 'N'; // Indica la variable que es un nuevo registro
            //this.Consultar_Parametros("S")
            this.Deshabilitar_campo_credito = true;
        },
        Abrir_Ventana_Emergente_Editar() {
            this.nitInvalido = false
            this.proveedorIngresado = false
            this.TituloAviso = 'Proveedor modificado correctamente'
            //this.Titulo_emergente = 'Editar Proveedor';
            this.Operacion = 'A'; // Indica la variable que es una actualización
            //this.Estado_VentanaEmergente = true;
            //Campos abiertos
            /*this.codigo = datos_editar.CODIGO;
            this.nit = datos_editar.NIT;
            this.nombre = datos_editar.NOMBRE;
            this.nombre_cheque = datos_editar.NOMBRECHEQUE;
            this.direccion = datos_editar.DIRECCION;
            this.ciudad = datos_editar.CIUDAD;
            this.telefono = datos_editar.TELEFONOS;
            this.fax = datos_editar.FAX;
            this.email = datos_editar.EMAIL;
            this.contacto_trato = datos_editar.CONTACTOTRATO;
            this.contacto = datos_editar.CONTACTO;
            this.linea = datos_editar.LINEA;
            this.conta_compra = datos_editar.CONTACOMPRA;
            this.descuento = datos_editar.DESCUENTO;
            this.limite_descuento = datos_editar.LIMITEDESCUENTO;*/

            /*if (datos_editar.CREDITO === 'S') {
                this.credito = true;
                this.Deshabilitar_campo_credito = false;
            } else {
                this.credito = false;
                this.Deshabilitar_campo_credito = true;
            }

            if (datos_editar.ACTIVO === 'S') {
                this.activo = true;
            } else {
                this.activo = false;
            }*/

            /* this.limite_credito = datos_editar.LIMITECREDITO;
             this.vence_credito = datos_editar.VENCECREDITO;
             this.conta_anticipo = datos_editar.CONTAANTICIPO;
             this.conta_credito = datos_editar.CONTACREDITO;
             this.retencion = datos_editar.RETENCION;
             this.cuenta_retencion = datos_editar.CUENTARETENCION;
             this.nit_retencion = datos_editar.NITRETENCION;
             this.direccion_calle = datos_editar.DIRECCIONCALLE;
             this.direccion_numero = datos_editar.DIRECCIONNUMERO;
             this.direccion_apto = datos_editar.DIRECCIONAPTO;
             this.direccion_zona = datos_editar.DIRECCIONZONA;
             this.direccion_colonia = datos_editar.DIRECCIONCOLONIA;
             this.direccion_municipio = datos_editar.DIRECCIONMUNI;
             this.conta_compra_medicos = datos_editar.CONTACOMPRAMEDICOS;
             this.conta_credito_medicos = datos_editar.CONTACREDITOMEDICOS;
             this.codigo_hba = datos_editar.CODIGOHBA;
             this.Estado_bloqueado = datos_editar.ESTADO_BLOQUEADO;
             this.DiasCredito = datos_editar.DIASCREDITO;*/

            /*if (datos_editar.TIPODEPROVEEDOR === '0' || datos_editar.TIPODEPROVEEDOR == '')
                this.es_proveedor_servicio = false;
            else
                this.es_proveedor_servicio = true;

            if (datos_editar.ESAGENTERETENEDOR === "N" || datos_editar.ESAGENTERETENEDOR === "0" || datos_editar.ESAGENTERETENEDOR == '')
                this.es_agente_retenedor = false;
            else
                this.es_agente_retenedor = true;

            if (datos_editar.APROBADO === 'N')
                this.es_proveedor_aprobado = false;
            else
                this.es_proveedor_aprobado = true;

            if (datos_editar.PROVEEDORDELEXTERIOR === '0' || datos_editar.PROVEEDORDELEXTERIOR == '')
                this.es_proveedor_exterior = false;
            else
                this.es_proveedor_exterior = true;

            this.cuenta_banco = datos_editar.CUENTABANCO;*/

            //Lista
            this.Consultar_TipoProveedor();
            this.Consultar_TipoRetencion();
            this.Consultar_DepartamentoGeografico();

            /*this.id_tipo_proveedor_seleccionado = datos_editar.TIPO;
            this.id_tipo_retencion_seleccionado = datos_editar.TIPORETENCION;
            this.id_departamento_geografico_seleccionado = datos_editar.DIRECCIONDEPTO;

            //Setear Multilistas
            this.cb_tipo_proveedor = {
                CODIGO: datos_editar.TIPO,
                DESCRIPCION: datos_editar.DESCRIPCION_TIPO_PROVEEDOR
            }

            if (datos_editar.DIRECCIONDEPTO !== '0')
                this.cb_departamento_geografico = {
                    Departamento: datos_editar.DEPARTAMENTO_GEOGRAFICO
                }

            if (datos_editar.TIPORETENCION !== '0')
                this.cb_tipo_retencion = {
                    DESCRIPCION: datos_editar.DESCRIPCION_TIPO_RETENCION
                }*/

            //Estado de los campos de la ventana emergente
            this.Deshabilitar_campo_codigo = true;
            this.Deshabilitar_campo_multiempresa = false;
            this.Deshabilitar_campo_uniempresa = false;
            this.Operacion = 'A'; // Indica la variable que es un nuevo registro
        },
        Informacion_Registro(datos_editar) {
            this.nitInvalido = false
            this.Titulo_emergente = 'Información Proveedor';
            this.proveedorIngresado = false
            this.Operacion = 'A'; // Indica la variable que es una actualización
            this.Estado_VentanaEmergente = true;
            //Campos abiertos
            this.codigo = datos_editar.CODIGO;
            this.nit = datos_editar.NIT;
            this.nombre = datos_editar.NOMBRE;
            this.nombre_cheque = datos_editar.NOMBRECHEQUE;

            if (datos_editar.ACTIVO === 'S') {
                this.activo = true;
            } else {
                this.activo = false;
            }

            this.direccion = datos_editar.DIRECCION;
            this.ciudad = datos_editar.CIUDAD;
            this.telefono = datos_editar.TELEFONOS;
            this.fax = datos_editar.FAX;
            this.email = datos_editar.EMAIL;
            this.contacto_trato = datos_editar.CONTACTOTRATO;
            this.contacto = datos_editar.CONTACTO;
            this.linea = datos_editar.LINEA;
            this.conta_compra = datos_editar.CONTACOMPRA;
            this.descuento = datos_editar.DESCUENTO;
            this.limite_descuento = datos_editar.LIMITEDESCUENTO;
            if (datos_editar.CREDITO === 'S') {
                this.credito = true;
            } else {
                this.credito = false;
            }

            this.limite_credito = datos_editar.LIMITECREDITO;
            this.vence_credito = datos_editar.VENCECREDITO;
            this.conta_anticipo = datos_editar.CONTAANTICIPO;
            this.conta_credito = datos_editar.CONTACREDITO;
            this.retencion = datos_editar.RETENCION;
            this.cuenta_retencion = datos_editar.CUENTARETENCION;
            this.nit_retencion = datos_editar.NITRETENCION;
            this.direccion_calle = datos_editar.DIRECCIONCALLE;
            this.direccion_numero = datos_editar.DIRECCIONNUMERO;
            this.direccion_apto = datos_editar.DIRECCIONAPTO;
            this.direccion_zona = datos_editar.DIRECCIONZONA;
            this.direccion_colonia = datos_editar.DIRECCIONCOLONIA;
            this.direccion_municipio = datos_editar.DIRECCIONMUNI;
            this.conta_compra_medicos = datos_editar.CONTACOMPRAMEDICOS;
            this.conta_credito_medicos = datos_editar.CONTACREDITOMEDICOS;
            this.codigo_hba = datos_editar.CODIGOHBA;
            this.Estado_bloqueado = datos_editar.ESTADO_BLOQUEADO;
            this.DiasCredito = datos_editar.DIASCREDITO;

            if (datos_editar.TIPODEPROVEEDOR === '0' || datos_editar.TIPODEPROVEEDOR == '')
                this.es_proveedor_servicio = false;
            else
                this.es_proveedor_servicio = true;

            if (datos_editar.ESAGENTERETENEDOR === "N" || datos_editar.ESAGENTERETENEDOR === "0" || datos_editar.ESAGENTERETENEDOR == '')
                this.es_agente_retenedor = false;
            else
                this.es_agente_retenedor = true;

            if (datos_editar.APROBADO === 'N')
                this.es_proveedor_aprobado = false;
            else
                this.es_proveedor_aprobado = true;

            if (datos_editar.PROVEEDORDELEXTERIOR === '0' || datos_editar.PROVEEDORDELEXTERIOR == '')
                this.es_proveedor_exterior = false;
            else
                this.es_proveedor_exterior = true;

            this.cuenta_banco = datos_editar.CUENTABANCO;

            //Setear Multilistas
            this.id_tipo_proveedor_seleccionado = datos_editar.TIPO
            this.cb_tipo_proveedor = {
                CODIGO: datos_editar.TIPO,
                DESCRIPCION: datos_editar.DESCRIPCION_TIPO_PROVEEDOR
            }

            this.id_departamento_geografico_seleccionado = datos_editar.DIRECCIONDEPTO;
            if (datos_editar.DIRECCIONDEPTO !== '0')
                this.cb_departamento_geografico = {
                    Departamento: datos_editar.DEPARTAMENTO_GEOGRAFICO
                }

            this.id_tipo_retencion_seleccionado = datos_editar.TIPORETENCION
            //if (datos_editar.TIPORETENCION !== '0')
            this.cb_tipo_retencion = {
                CODIGO: datos_editar.TIPORETENCION,
                DESCRIPCION: datos_editar.DESCRIPCION_TIPO_RETENCION
            }

            //Estado de los campos de la ventana emergente
            this.Deshabilitar_campo_codigo = true;
            this.Deshabilitar_campo_multiempresa = true;
            this.Deshabilitar_campo_uniempresa = true;
            this.Deshabilitar_campo_credito = true;
            this.Operacion = 'I'; // Indica la variable que es un nuevo registro
        },
        Confirmacion_Transaccion() {
            /**
             * @General
             * Función Permite  actualizar y  almacenar un nuevo registro;
             */
            const sesion = this.$store.state.sesion;
            this.Corporativo_Sesion = sesion.corporativo

            this.res_variables = this.Validacion_Campos('A', 'Código', this.codigo, true, 6);
            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('A', 'Nit', this.nit, true);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('A', 'Nombre', this.nombre, true);

            if (this.res_variables)
                this.res_variables = this.Validacion_Campos('N', 'Tipo proveedor', this.id_tipo_proveedor_seleccionado, true);

            //Validar el porcentaje de descuento
            if ((this.res_variables === true) && ((this.descuento < 0) || (this.descuento > 99))) {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Alerta',
                    text: 'Campo Descuento %: Debe menor o igual a 99',
                });
                this.res_variables = false;
            }

            if (this.nitInvalido) {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Alerta',
                    text: 'NIT no es valido, favor validar de nuevo',
                });
                this.res_variables = false;
            }

            if (this.res_variables) {
                //Validacion para tipo de retención seleccionado
                if (this.id_tipo_retencion_seleccionado === '' || this.id_tipo_retencion_seleccionado === null)
                    this.id_tipo_retencion_seleccionado = 0;

                //Validacion para dirección zona
                if (this.direccion_zona === '' || this.direccion_zona === null)
                    this.direccion_zona = 0;

                //Validacion para dirección departamento
                if (this.id_departamento_geografico_seleccionado === '' || this.id_departamento_geografico_seleccionado === null)
                    this.id_departamento_geografico_seleccionado = 0;

                //Porcentaje retencion %
                if (this.retencion === '' || this.retencion === null)
                    this.retencion = 0;

                //Validación de check's
                //Validacion de check Proveedor de servicio
                if (this.es_proveedor_servicio)
                    this.es_proveedor_servicio = 1;
                else
                    this.es_proveedor_servicio = 0;

                //Validacion de check es agente retenedor
                if (this.es_agente_retenedor)
                    this.es_agente_retenedor = 'S';
                else
                    this.es_agente_retenedor = 'N';

                //Validacion de check proveedor del exterior
                if (this.es_proveedor_exterior)
                    this.es_proveedor_exterior = 1;
                else
                    this.es_proveedor_exterior = 0;

                //Validacion de check acepta credito
                if (this.credito)
                    this.credito = 1;
                else
                    this.credito = 0;

                //Validacion de check acepta credito
                if (this.es_proveedor_aprobado)
                    this.es_proveedor_aprobado = 1;
                else
                    this.es_proveedor_aprobado = 0;

                if (this.activo)
                    this.proveedor_activo = 'S';
                else
                    this.proveedor_activo = 'N';

                const sesion = this.$store.state.sesion;

                this.axios.post('/app/v2_api_compras/proveedor', {
                    codigo: this.codigo,
                    nombre: this.nombre,
                    nombre_cheque: this.nombre_cheque,
                    activo: this.proveedor_activo,
                    tipo: this.id_tipo_proveedor_seleccionado,
                    direccion: this.direccion,
                    ciudad: this.ciudad,
                    telefonos: this.telefono,
                    fax: this.fax,
                    email: this.email,
                    contactotrato: this.contacto_trato,
                    contacto: this.contacto,
                    linea: this.linea,
                    conta_compra: this.conta_compra,
                    descuento: this.descuento,
                    limite_descuento: this.limite_descuento,
                    credito: this.credito,
                    limite_credito: this.limite_credito,
                    vence_credito: this.vence_credito,
                    nit: this.nit,
                    conta_anticipo: this.conta_anticipo,
                    conta_credito: this.conta_credito,
                    retencion: this.retencion,
                    tipo_retencion: this.id_tipo_retencion_seleccionado,
                    cuenta_retencion: this.cuenta_retencion,
                    nit_retencion: this.nit_retencion,
                    direccion_calle: this.direccion_calle,
                    direccion_numero: this.direccion_numero,
                    direccion_apto: this.direccion_apto,
                    direccion_zona: this.direccion_zona,
                    direccion_colonia: this.direccion_colonia,
                    direccion_depto: this.id_departamento_geografico_seleccionado,
                    direccion_muni: this.direccion_municipio,
                    conta_compra_medicos: this.conta_compra_medicos,
                    conta_credito_medicos: this.conta_credito_medicos,
                    codigo_hba: this.codigo_hba,
                    tipo_proveedor: this.es_proveedor_servicio,
                    es_agente_retenedor: this.es_agente_retenedor,
                    tipo_contribuyente: '',
                    proveedor_del_exterior: this.es_proveedor_exterior,
                    cuenta_banco: this.cuenta_banco,
                    bloqueado: 'N',
                    operacion: this.Operacion,
                    corporativo: sesion.corporativo,
                    aprobado: this.es_proveedor_aprobado,
                    dias_credito: this.DiasCredito
                }).then(resp => {
                    if (resp.data.tipo_error == 0) {
                        this.nitInvalido = false
                        if (resp.data.error > 0) {
                            this.nitInvalido = true
                            this.TituloError = resp.data.descripcion
                            this.$vs.notify({
                                title: 'Proveedor',
                                text: resp.data.descripcion,
                                color: 'danger',
                                position: 'bottom-center'
                            });
                        } else {
                            //this.Consultar_proveedor(); //Carga  la tabla inicial

                            this.proveedorIngresado = true
                            this.Deshabilitar_campo_codigo = true;
                            this.Deshabilitar_campo_multiempresa = true;
                            this.Deshabilitar_campo_uniempresa = true;
                            this.Deshabilitar_campo_credito = true;


                            this.$vs.notify({
                                title: 'Proveedor',
                                text: resp.data.descripcion,
                                color: 'success',
                                position: 'bottom-center'
                            });
                        }

                    }
                })
                    .catch(() => {

                    })


            }
        },
        LimpiarCampos() {
            this.Deshabilitar_campo_multiempresa = true
            this.codigo = "",
                this.nit = "",
                this.nombre = "",
                this.nombre_cheque = "",
                this.activo = true,
                this.direccion = "",
                this.ciudad = "",
                this.telefono = "",
                this.fax = "",
                this.email = "",
                this.contacto_trato = "",
                this.contacto = "",
                this.linea = "",
                this.conta_compra = "",
                this.descuento = 0,
                this.limite_descuento = 0,
                this.credito = false,
                this.limite_credito = 0,
                this.vence_credito = 0,
                this.conta_anticipo = "",
                this.conta_credito = "",
                this.retencion = "",
                this.cuenta_retencion = "",
                this.nit_retencion = "",
                this.direccion_calle = "",
                this.direccion_numero = "",
                this.direccion_apto = "",
                this.direccion_zona = 0,
                this.direccion_colonia = "",
                this.direccion_municipio = "",
                this.conta_compra_medicos = "",
                this.conta_credito_medicos = "",
                this.codigo_hba = "",
                this.es_proveedor_servicio = false,
                this.es_agente_retenedor = false,
                this.es_proveedor_aprobado = false,
                this.tipo_contribuyente = "",
                this.es_proveedor_exterior = "",
                this.cuenta_banco = "",
                this.bloqueado = "";
            this.DiasCredito = ""

            this.cb_tipo_proveedor = null; //Limpiar los valores seleccionados multiselec
            this.cb_tipo_retencion = null; //Limpiar los valores seleccionados multiselec
            this.cb_departamento_geografico = null; //Limpiar los valores seleccionados multiselec
            this.proveedorIngresado = false
            this.nitInvalido = false
            /*  this.Lista_tipo_proveedor = [];
             this.Lista_tipo_retencion = [];
             this.Lista_departamento_geografico = []; */
        },
        Validacion_Campos(Tipo, Nombre, valor, obligatorio, tamanio = 0) {
            /**
             * @General
             * Tipo : N = Númerio, Nombre = Descripción del campo, Valor = A evaluar, Obligatorio= Verdadero o falso.
             * Devuelve un mensaje al usuario si las validaciones no se cumple y aborta, de lo contrario devuelve verdadero
             */
            if (Tipo == 'N') {
                if (valor <= 0) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Alerta',
                        text: 'Campo \'' + Nombre + '\' es obligatorio.',
                    });
                    return false;
                } else {
                    return true;
                }
            } else {
                if ((valor === '' || valor === null) && obligatorio == true) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Alerta',
                        text: 'Campo \'' + Nombre + '\' es obligatorio.',
                    });
                    return false;
                } else if (valor.length > tamanio && tamanio != 0) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Alerta',
                        text: 'Campo \'' + Nombre + '\' debe contener ' + tamanio + ' o menos caracteres.',
                    });
                    return false;
                } else {
                    return true;
                }
            }
        },
        ValidarNit: function () {
            if (this.nit != '') {
                if (this.nit.indexOf("-") < 0 && !this.es_proveedor_exterior) {
                    this.nitInvalido = true
                    this.TituloError = 'NIT no valido'
                } else {
                    this.axios.post('/app/v2_api_compras/ValidarNit', { nit: this.nit, codigo: this.codigo })
                        .then(resp => {
                            this.nitInvalido = false

                            if ((resp.data.json[0].NitValido == 'N' || resp.data.json[0].Existentes != '0') && !this.es_proveedor_exterior) {
                                this.nitInvalido = true
                                this.TituloError = 'NIT no valido o ya existente para otro proveedor'
                            } else if (resp.data.json[0].Existentes != '0' && this.es_proveedor_exterior) {
                                this.nitInvalido = true
                                this.TituloError = 'NIT ya existe para otro proveedor'
                            }
                        })
                }
            }
        }
    }
}
</script>

<!-- <style scoped>
.img-container {
    text-align: center;
}

/*SOLO PARA DISEÑO BOTON */
.tooltip {
    position: relative;
    display: inline-block;
    border-bottom: 0px dotted black;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: black;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px 0;

    /* Position the tooltip */
    position: absolute;
    z-index: 1;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
}
</style> -->