<template>
    <div id="app">
        <div class="tabla-polizas contenedor-tabla">
            <vs-table2 max-items="4" tooltip :data="ListaPolizas">
                <template slot="thead">
                    <th width="150px">Codigo Afiliado</th>
                    <th width="50px">Id Poliza</th>
                    <th width="300px">Nombre Plan</th>
                    <th width="100px">Codigo Plan</th>
                    <th width="75px">Estado</th>
                </template>
                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data" :ref="`list_${indextr}`" v-on:click=submit(tr,indextr)
                        :class="indextr === filaSeleccionada ? 'resaltar-columna': ''">
                        <vs-td2>
                            {{ tr.IdAfiliado }}
                        </vs-td2>
                        <vs-td2>
                            {{ tr.IdPoliza }}
                        </vs-td2>

                        <vs-td2>
                            {{ tr.<PERSON>mbre }}
                        </vs-td2>

                        <vs-td2>
                            {{ tr.Id<PERSON>lan }}
                        </vs-td2>
                        <vs-td2>
                            {{ tr.Estado }}
                        </vs-td2>
                    </tr>
                </template>

            </vs-table2>
        </div>

    </div>
</template>
<script>

export default {
    name: "ClientesPlanes",
    props: {
        IdCliente: {
            required: true,
            default:'',
            type: String
        },
        Opcion: {
            required: true,
            type: Number 
        }
    },
    components: {
    },
    data() {
        return {
            selPolizaSaludSiempre: '',
            ListaPolizas: [],
            filaSeleccionada: null
        };
    },
    watch: {
        IdCliente: async function(value){
            const resp = await this.axios.post('/app/emergencia/BuscaCliente', {
                IdCliente: value,
                TipoBusca: this.Opcion
            })
            this.ListaPolizas = resp.data.json            
        },immediate: true
    },
    activated() {
        
    },
    mounted() {
        this.ConsultaPolizasPaciente()
    },
    methods: {
        async ConsultaPolizasPaciente() {
            const resp = await this.axios.post('/app/emergencia/BuscaCliente', {
                TipoBusca: this.Opcion,
                IdCliente: this.IdCliente
            })
            this.ListaPolizas = resp.data.json
            this.$emit("lista-vacia", this.ListaPolizas.length);
        },
        submit(item,indextr) {
            this.filaSeleccionada = indextr
            this.$emit("poliza-seleccionada", item);
        }
    }
};
</script>

<style scoped>

</style>

<style >
.tabla-polizas .contenedor-tabla {
    min-height: auto;
}

.resaltar-columna {
    background-color:  rgba(0, 143, 190, 0.2) !important
}
</style>
