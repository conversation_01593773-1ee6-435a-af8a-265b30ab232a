<template>
<div>
    <div class="p-2" v-if="(consulta === true && hojaAnestesia !== null && hojaAnestesia !== '' && formulario.IdHojaAnestesia !== '') || (!consulta)">
        <form @submit="handleSubmit">
            <DxForm :form-data.sync="formulario" labelMode="floating" :read-only="consulta" :ref="dataEncabezadoRefKey" :show-validation-summary="true">

                <DxGroupItem item-type="group" :col-span="2" :col-count="2" v-if="consulta === true">
                    <DxSimpleItem v-for="(item, index) in info" v-bind:key="index" data-field="x">
                        <template #default>
                            <div id="detail">
                                <b>{{ item.name }}</b>
                                {{ formulario[item.value] }}
                            </div>
                        </template>
                    </DxSimpleItem>
                </DxGroupItem>

                <DxGroupItem item-type="group" :col-span="2" :col-count="2" caption="Diagnóstico">
                    <DxItem data-field="DiagnosticoPre" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000 }">
                        <DxLabel :text="'Diagnóstico preoperatorio'" />
                    </DxItem>
                    <DxItem data-field="DiagnosticoPos" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000 }">
                        <DxLabel :text="'Diagnóstico postoperatorio'" />
                    </DxItem>
                </DxGroupItem>

                <DxGroupItem :col-count-by-screen="colCountByScreen" item-type="group" caption=" ">
                    <DxItem data-field="Asa" editor-type="dxSelectBox" :editor-options="{ items: valuacion }" :is-required="true">
                        <DxLabel :text="'Valuación de ASA'" />
                    </DxItem>
                    <DxItem data-field="Sala" editor-type="dxSelectBox" :editor-options="{ items: quirofano }">
                        <DxLabel :text="'Quirófano'" />
                    </DxItem>
                    <DxItem data-field="TiempoAyuno" editor-type="dxSelectBox" :editor-options="{ items: ayuno }">
                        <DxLabel :text="'Horas de ayuno'" />
                    </DxItem>
                </DxGroupItem>

                <DxGroupItem :col-count-by-screen="colCountByScreenSelect" item-type="group" caption="Vía aérea">
                    <DxItem data-field="ViaAerea" editor-type="dxSelectBox" :editor-options="{ items: aerea }" :is-required="true">
                        <DxLabel :text="'Vía aérea'" />
                    </DxItem>
                    <DxItem data-field="Numero" editor-type="dxTextBox" :editor-options="{min: 0, maxLength: 50, format: '#' }" :is-required="true">
                        <DxLabel :text="'Número'" />
                    </DxItem>
                    <DxItem data-field="Otro" editor-type="dxTextBox" :editor-options="{ maxLength: 4000 }">
                        <DxLabel :text="'Otros'" />
                    </DxItem>
                    <DxItem data-field="CCAire" editor-type="dxNumberBox" :editor-options="{min: 0, max: 50000, format: '#0.00', step: 0  }">
                        <DxLabel :text="'CC. aire'" />
                    </DxItem>
                </DxGroupItem>

                <DxGroupItem item-type="group" caption="Detalles" :col-count="3">
                    <DxItem :col-span="3" data-field="TipoAnestesia" editor-type="dxTextBox" :editor-options="{ maxLength: 200 }">
                        <DxLabel :text="'Tipo de anestesia'" />
                    </DxItem>
                    <DxItem data-field="Induccion" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000 }">
                        <DxLabel :text="'Inducción'" />
                    </DxItem>
                    <DxItem data-field="OperacionEfectuada" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000 }">
                        <DxLabel :text="'Operación efectuada'" />
                    </DxItem>
                    <DxItem data-field="Comentarios" caption="Observaciones" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000 }" />
                </DxGroupItem>

                <DxGroupItem v-if="!consulta" item-type="group" :col-count="2" caption=" ">
                    <DxButtonItem :col-span="3" :button-options="submitButtonOptions" horizontal-alignment="center" verical-alignment="center" />
                    <DxButtonItem :col-span="3" :button-options="newButtonOptions" horizontal-alignment="center" verical-alignment="center" />
                </DxGroupItem>
            </DxForm>
        </form>
    </div>
    <vs-row id="texto" class="p-2" v-if="(hojaAnestesia === '' || hojaAnestesia === null) && consulta">
        <p>Seleccione una fecha para mostrar información</p>
    </vs-row>
    <vs-row id="texto" class="p-2" v-if="formulario.IdHojaAnestesia === '' && hojaAnestesia !== ''">
        <p>La hoja seleccionada no cuenta con información para mostrar</p>
    </vs-row>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'
import {
    DxForm,
    DxItem,
    DxLabel,
    DxGroupItem,
    DxButtonItem,
    DxSimpleItem
} from 'devextreme-vue/form'

const dataEncabezadoRefKey = 'encabezado-form'

export default {
    name: 'AnestesiaDatosGenerales',
    components: {
        DxForm,
        DxItem,
        DxLabel,
        DxGroupItem,
        DxButtonItem,
        DxSimpleItem
    },
    data() {
        return {
            DefaultDxGridConfiguration,
            dataEncabezadoRefKey,
            formularioSeleccionado: true,
            numeroFormulario: '',
            ayuno: ['Sin ayuno', '1 hora', '2 horas', '3 horas', '4 horas', '5 horas', 'Más de 5 horas', ],
            quirofano: [],
            valuacion: ['1', '2', '3', '4'],
            aerea: ['Intub. OT', 'Intub. NT', 'Mascarilla', 'M. Laríngea', 'Binasal'],
            formulario: {
                IdHojaAnestesia: '',
                IdAnestesicoEn: '',
                DiagnosticoPre: '',
                DiagnosticoPos: '',
                Induccion: '',
                OperacionEfectuada: '',
                Intubot: '',
                IntubNt: '',
                Mascarilla: '',
                MLaringea: '',
                Binasal: '',
                ViaAerea: '',
                Numero: '',
                Otro: '',
                CCAire: '',
                Sala: '',
                Asa: '',
                Comentarios: '',
                TipoAnestesia: '',
                TiempoAyuno: '',
                Usuario: '',
                Puesto: '',
                Fecha: ''
            },
            idEncabezado: '',
            submitButtonOptions: {
                text: 'Guardar formulario',
                type: 'success',
                icon: 'save',
                useSubmitBehavior: true,
                disabled: this.consulta
            },
            newButtonOptions: {
                text: 'Nuevo formulario',
                type: 'default',
                icon: 'file',
                useSubmitBehavior: false,
                onClick: () => {
                    this.limpiarFormulario()
                },
                disabled: this.consulta
            },
            info: [{
                    name: 'Usuario:',
                    value: 'Usuario'
                },
                {
                    name: 'Puesto:',
                    value: 'Puesto'
                }, {
                    name: 'Fecha registro:',
                    value: 'Fecha'
                },
            ],
        }
    },
    props: {
        SerieAdmision: null,
        CodigoAdmision: null,
        NumeroExpediente: null,

        StatusAdmision: null,
        consulta: null, //Variable para indicar si el componente se usará para consulta -> 'true' o para ingreso -> 'false'
        hojaAnestesia: null, //Variable para obtener la hoja cuando se utilice para consulta
    },
    methods: {
        handleSubmit(e) {
            e.preventDefault()
            if (this.SerieAdmision && this.CodigoAdmision) {

                this.$vs.dialog({
                    type: 'confirm',
                    color: '#ed8c72',
                    title: 'Registro de datos generales',
                    acceptText: 'Ingresar',
                    cancelText: 'Cancelar ingreso',
                    text: `Se ingresarán datos generales de anestesia correspondientes a la admisión: '` + this.SerieAdmision + this.CodigoAdmision + `'`,
                    buttonCancel: 'border',
                    clientWidth: 100,
                    accept: () => {
                        this.formulario = this.$reemplazar_tabulares_objeto(this.formulario, /\t/g, '   ')
                        // this.formulario.OperacionEfectuada = this.$reemplazar_tabulares(this.formulario.OperacionEfectuada)
                        let postData = {
                            SerieAdmision: this.SerieAdmision,
                            Admision: this.CodigoAdmision,
                            ...this.formulario,
                        }

                        this.axios.post("/app/v1_ExpedienteEvolucion/InsertarAnestesiaDatosGenerales", {
                            ...postData
                        }).then(() => {
                            this.$emit('ingreso', 2);
                        })
                    },
                    cancel: () => {

                    }
                })
            }
        },
        getQuirofanos() {
            this.axios.post('/app/salaoperaciones/ListarQuirofanos', {
                    Opcion: "C",
                    SubOpcion: "1",
                    Hospital: this.$store.state.sesion.sesion_sucursal,
                })
                .then(resp => {
                    this.quirofano = resp.data.json.map((x) => {
                        return x.text
                    })
                })
        },
        getAnestesiaDT() {
            this.axios.post('/app/v1_ExpedienteEvolucion/BuscarEncRegistroAnestesicoIdHoja', {
                    Id: this.hojaAnestesia
                })
                .then(resp => {
                    this.idEncabezado = ''
                    this.limpiarFormulario();
                    if (resp.data.json.length > 0) {
                        this.formulario = resp.data.json[0]
                        this.formulario.Fecha = this.formatFecha(this.formulario.Fecha)
                        this.idEncabezado = this.formulario.IdAnestesicoEn
                    }
                })
        },
        limpiarFormulario() {
            this.formulario = {
                IdHojaAnestesia: '',
                IdAnestesicoEn: '',
                DiagnosticoPre: '',
                DiagnosticoPos: '',
                Induccion: '',
                OperacionEfectuada: '',
                Intubot: '',
                IntubNt: '',
                Mascarilla: '',
                MLaringea: '',
                Binasal: '',
                ViaAerea: '',
                Numero: '',
                Otro: '',
                CCAire: '',
                Sala: '',
                Asa: '',
                Comentarios: '',
                TipoAnestesia: '',
                TiempoAyuno: '',
                Usuario: '',
                Puesto: '',
                Fecha: ''
            }
        },
        formatFecha(date) {
            date = new Date(date)
            var month = date.getMonth() + 1
            month = month < 10 ? '0' + month : month;
            var hours = date.getHours();
            var minutes = date.getMinutes();
            minutes = minutes < 10 ? '0' + minutes : minutes;
            var strTime = hours + ':' + minutes;

            return date.getDate() + "/" + month + "/" + date.getFullYear() + " " + strTime;
        },
        getAnestesiaDTHistorial() {
            this.axios.post('/app/v1_ExpedienteEvolucion/BuscarEncRegistrosAnestesicosActual', {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.formulario = resp.data.json[0]
                        this.idEncabezado = this.formulario.IdAnestesicoEn
                        
                        // if (!this.formularioSeleccionado) {
                        //     this.$vs.dialog({
                        //         type: 'confirm',
                        //         color: '#ed8c72',
                        //         title: 'Datos generales de anestesia existentes',
                        //         acceptText: 'Trabajar con el formulario existente',
                        //         cancelText: 'Crear un nuevo formulario',
                        //         text: `¿Desea trabajar con el formulario existente o desea crear uno nuevo?`,
                        //         buttonCancel: 'border',
                        //         clientWidth: 100,
                        //         accept: () => {
                        //             this.formularioSeleccionado = true
                        //             this.formulario = resp.data.json[0]

                        //             this.idEncabezado = this.formulario.IdAnestesicoEn
                        //         },
                        //         cancel: () => {
                        //             this.formularioSeleccionado = true
                        //         }
                        //     })
                        // }
                    }
                })
        },
        getMounted() {
            this.limpiarFormulario();
            this.getQuirofanos();
            this.formularioSeleccionado = false;

            if (this.consulta !== null) {
                if (this.consulta === true && this.hojaAnestesia !== null && this.hojaAnestesia !== '') {
                    this.getAnestesiaDT();
                } else if (this.consulta === false) {
                    this.getAnestesiaDTHistorial();
                }
            }
        },
        sendEmitter() {
            this.$emit('anestesiaEncabezado', this.idEncabezado);
        }
    },
    mounted() {
        if (this.consulta && this.hojaAnestesia !== null && this.hojaAnestesia !== '') {
            this.getAnestesiaDT();
        }
    },
    watch: {
        'hojaAnestesia'(newval) {
            if (newval !== undefined && newval !== '') {
                this.getAnestesiaDT();
            }
        },
        'idEncabezado'(newval) {
            if (newval !== undefined && newval !== '') {
                this.sendEmitter()
            }
        },
    },
    computed: {
        colCountByScreen() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 3,
                    lg: 3
                };
        },
        colCountByScreenSelect() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 4,
                    lg: 4,
                    xl: 4
                };
        },
    },
}
</script>

<style>
#texto {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    height: 100px;
}
</style>
