<template>
<vs-row class="p-2 row w-full">
    <vs-col vs-justify="center" vs-align="center" vs-w="10">
        <vx-card>
            <DxDataGrid :ref="dataGridRefKey" v-bind="DefaultDxGridConfiguration" :headerFilter="{visible:false,allowSearch:false}" :data-source="ingestaDataSource" :height="'100%'" :no-data-text="'Seleccione una fecha para cargar información'" @editor-preparing="editorHorarioValue">
                <DxSelection mode="single" />
                <DxToolbar>
                    <DxItem name="groupPanel" />
                    <DxItem name="searchPanel" />
                    <DxItem location="after" template="opcionesIngesta" />
                </DxToolbar>

                <DxGroupPanel :visible="true" />
                <DxGrouping :auto-expand-all="false" />

                <DxEditing :allow-updating="false" :allow-adding="true" :allow-deleting="false" mode="popup">
                    <DxPopup v-bind="popupConf" :height="500" title="Registro de ingesta">
                        <DxToolbarItem widget="dxButton" toolbar="bottom" :options="{
                                        text: 'Guardar',
                                        type: 'success',
                                        useSubmitBehavior: true,
                                        onClick: submit
                                    }" location="after">
                        </DxToolbarItem>
                        <DxToolbarItem widget="dxButton" toolbar="bottom" :options="{
                                        text: 'Cancelar',
                                        type: 'danger',
                                        useSubmitBehavior: true,
                                        onClick: ()=>{
                                            this.dataGrid.cancelEditData();
                                        }
                                    }" location="after">
                        </DxToolbarItem>
                    </DxPopup>

                    <DxForm labelMode="floating" :ref="dataIngestaFormRefKey">
                        <DxItem item-type="group" :col-span="2" :col-count="2">
                            <DxItem item-type="group" :col-span="1" :col-count="1">
                                <DxItem data-field="Horario" editor-type="dxSelectBox" :col-span="1" :editor-options="horarios" :is-required="true">
                                    <DxLabel :text="'Horario'" />
                                </DxItem>
                                <DxItem data-field="FechaIngesta" editor-type="dxDateBox" :col-span="1" :is-required="true" :editor-options="dateBoxOptions">
                                    <DxLabel :text="'Fecha de ingesta'" />
                                </DxItem>
                                <DxItem data-field="IngestaOral" editor-type="dxNumberBox" :editor-options="{min: 0, max: 5000, format: '#', step: 0  }" />
                                <DxItem data-field="Otros" editor-type="dxNumberBox" :editor-options="{min: 0, max: 5000, format: '#', step: 0  }" />
                                <DxItem data-field="Comentarios" editor-type="dxTextArea" :editor-options="{ height: 100 }" :col-span="1">
                                </DxItem>
                            </DxItem>
                            <DxItem item-type="group" :col-span="1" :col-count="1">
                                <DxItem template="intravenosaGrid" :col-span="1"></DxItem>
                            </DxItem>
                        </DxItem>
                    </DxForm>
                </DxEditing>

                <DxColumn :width="100" data-field="Horario" alignment="center" />
                <DxColumn :width="110" data-field="FechaIngesta" caption="Fecha ingesta" format="dd/MM/yyyy" data-type="date" alignment="center" />
                <DxColumn :width="100" data-field="IngestaOral" caption="Ingesta oral" alignment="center" />
                <DxColumn v-for="(item, index) in solucionesIngesta" v-bind:key="index" :width="150" :data-field="item.Solucion" alignment="center" />
                <DxColumn :width="100" data-field="Otros" alignment="center" />
                <DxColumn :width="300" data-field="Comentarios" alignment="center" />
                <DxColumn :width="300" data-field="Usuario" alignment="center" />
                <DxColumn :width="150" data-field="Puesto" alignment="center" />
                <DxColumn :width="150" data-field="FechaRegistro" caption="Fecha registro" format="dd/MM/yyyy HH:mm:ss" data-type="date" alignment="center" />

                <template #opcionesIngesta>
                    <ExpedienteGridToolBar v-bind="$props" :visible="true" :pdfExportItems="[{text:'Ingesta', reportName: 'Ingesta'}]" @add="addRow" @refresh="Cargar(true)" :report-param="$props" />
                </template>
                <template #intravenosaGrid>
                    <div id="intravenosa">
                        <DxDataGrid :ref="dataGridSoluciones" :data-source="soluciones" v-bind="DefaultDxGridConfiguration" :headerFilter="{visible:false,allowSearch:false}" :searchPanel="{visible: false}" :height="'100%'" :paging="{pageSize:5}" @editor-preparing="editorSoluciones" @key-down="noBlankSpace">
                            <DxColumn :width="'50%'" data-field="Solucion" caption="Solución" alignment="center" data-type="string" />
                            <DxColumn :width="'50%'" data-field="Cantidad" alignment="center" data-type="number" :editor-options="{min: 0, max: 5000, format: '#'}" />

                            <DxEditing :allow-updating="true" :allow-adding="true" :allow-deleting="allowDeleting" :use-icons="true" :confirmDelete="false" mode="cell">
                            </DxEditing>
                        </DxDataGrid>
                    </div>
                </template>
            </DxDataGrid>
        </vx-card>
    </vs-col>
    <vs-col vs-w="2" vs-justify="center" vs-align="center" class="pl-2">
        <vx-card title="Filtrar fecha">
            <!-- Se dejó toda la configuración, ya que si se usa la configuración global, se reinicia el pageIndex cada vez que se selecciona una fecha -->
            <DxDataGrid :ref="dataGridFechaKey" :headerFilter="{visible:true, allowSearch:true}" :showRowLines="true" :showColumnLine="true" :hoverStateEnabled="true" :data-source="fechas" key-expr="id" :width="'100%'" :height="'100%'" :show-borders="true" :focused-row-enabled="false" :row-alternation-enabled="true" :column-hiding-enabled="false" :load-panel="{enabled:false}" :paging="{pageSize:10}" column-resizing-mode="nextColumn" :column-min-width="50" :column-auto-width="false" @selection-changed="onSelectionChanged">
                <DxSelection mode="single" />
                <DxColumn :width="100" data-field="fecha" caption="Fecha" format="dd/MM/yyyy" data-type="date" alignment="center" />
            </DxDataGrid>
        </vx-card>
    </vs-col>
</vs-row>
</template>

<script>
import {
    DxDataGrid,
    DxColumn,
    DxEditing,
    DxForm,
    DxToolbar,
    DxLabel,
    DxPopup,
    DxSelection,
    DxGrouping,
    DxGroupPanel,
} from 'devextreme-vue/data-grid'
import CustomStore from 'devextreme/data/custom_store'
import ExpedienteGridToolBar from './ExpedienteGridToolBar.vue'
import {
    DxItem
} from 'devextreme-vue/form'
import 'devextreme-vue/lookup'
import {
    DxToolbarItem
} from 'devextreme-vue/popup'
import {
    DefaultDxGridConfiguration,
    popupConf
} from './data'
import 'devextreme-vue/text-area'

const dataGridRefKey = 'gridDiagnosticos'
const dataGridFechaKey = 'gridFechaKey'
const dataIngestaFormRefKey = 'ingesta-form'
const dataGridSoluciones = 'gridSoluciones'

export default {
    name: 'Ingesta',
    components: {
        DxDataGrid,
        DxColumn,
        ExpedienteGridToolBar,
        DxEditing,
        DxForm,
        DxItem,
        DxToolbar,
        DxPopup,
        DxLabel,
        DxToolbarItem,
        DxSelection,
        DxGrouping,
        DxGroupPanel,
    },
    data() {
        return {
            //Listado de diagnosticos asociados a la admisión
            ingesta: [],
            ingestaDataSource: new CustomStore({
                key: "id",
                load: () => {
                    return this.ingesta;
                },
                insert: (values) => {
                    //Se agregó un timeout para que el rid pueda guardar la información ingresada
                    setTimeout(() => {
                        this.GrabarIngesta(values);
                    }, 500);
                },
            }),
            fechas: [], //Listado de fechas de ingesta
            soluciones: [], //Variable para guardar las soluciones que el usuario ingrese
            solucionesIngesta: [], //Variable para almacenar las soluciones que retorne la carga de datos
            dataGridRefKey,
            dataGridFechaKey,
            dataIngestaFormRefKey,
            DefaultDxGridConfiguration,
            dataGridSoluciones,
            popupConf,
        }
    },
    props: {
        SerieAdmision: null,
        CodigoAdmision: null,
        NumeroExpediente: null,

        StatusAdmision: null,
        horarios: null,
        dateBoxOptions: null,
    },
    methods: {
        submit() {
            this.dataGrid.saveEditData()
            this.getFechasIngesta()
        },
        Cargar(update, dia = 0, mes = 0, año = 0) {
            this.cargarSoluciones();
            if (this.SerieAdmision && this.CodigoAdmision)
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaIngestaGrid', {
                    Serie: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    dia: dia,
                    mes: mes,
                    año: año
                })
                .then(resp => {
                    this.ingesta = resp.data.json.map((x, index) => {
                        let y = {};
                        for (let i of this.solucionesIngesta) {
                            y[i.Solucion] = x[i.Solucion]
                        }
                        y.id = index
                        y.Horario = x.Horario
                        y.FechaIngesta = x.Fechaingesta
                        y.IngestaOral = x.Inoral
                        y.Otros = x.Otros
                        y.Comentarios = x.Comentarios
                        y.FechaRegistro = x.Fecha
                        y.Usuario = x.Empleado
                        y.Puesto = x.Puesto
                        return y;
                    })
                    this.ingestaDataSource.load().then(
                        () => {
                            if (update) {
                                this.getFechasIngesta();
                                this.refreshFechaDataGrid();
                                this.refreshDataGrid();
                            } else {
                                this.refreshDataGrid();
                            }
                        },
                        () => {
                            //console.error('error loading data customstore' + JSON.stringify(error))
                        }
                    )
                })
            else {
                this.ingesta = [];
                this.refreshDataGrid();
                this.refreshFechaDataGrid();
            }
        },
        getFechasIngesta() {
            if (this.SerieAdmision && this.CodigoAdmision)
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaFechaIngSolExc', {
                    Serie: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    TipoBusqueda: "Ingesta"
                })
                .then(resp => {
                    this.fechas = resp.data.json.map((x, index) => {
                        return {
                            id: index,
                            fecha: this.formatFechaFiltro(x.Fecha)
                        }
                    })
                })
            else {
                this.fechas = [];
                this.refreshFechaDataGrid();
            }
        },
        cargarSoluciones() {
            if (this.SerieAdmision && this.CodigoAdmision)
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaIngestaSoluciones', {
                    Serie: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                })
                .then(resp => {
                    this.solucionesIngesta = resp.data.json.map((x, index) => {
                        return {
                            id: index,
                            Solucion: x.SOLUCION,
                            Cantidad: '',
                            NoEditable: true,
                        }
                    })
                    this.soluciones = this.solucionesIngesta.slice(0)
                })
            else {
                this.solucionesIngesta = []
                this.soluciones = []
            }
        },
        GrabarIngesta(values) {
            let postData = {
                SerieAdmision: this.SerieAdmision,
                Admision: this.CodigoAdmision,
                IdHorario: values.Horario,
                Oral: values.IngestaOral,
                Otros: values.Otros,
                Comentarios: this.$reemplazar_tabulares(values.Comentarios),
                FechaIngesta: this.formatFecha(values.FechaIngesta),
                xml: this.soluciones.map(x => {
                        return {
                            Solucion: x.Solucion,
                            cantidad: x.Cantidad,
                        }
                    }).filter(x => typeof x.Solucion !== 'undefined')
                    .filter(x => typeof x.cantidad !== 'undefined')
                    .filter(x => x.cantidad !== '')
                    .filter(x => x.Solucion !== '')
            }
            this.axios.post("/app/v1_ExpedienteEvolucion/InsertarIngesta", {
                ...postData
            }).then(() => {
                this.Cargar(true)
                this.$emit('onSaved', )
                this.soluciones = [];
            })
        },
        refreshDataGrid() {
            this.dataGrid.refresh()
                .then(function () {

                })
                .catch(function () {
                    //console.error('error refreshing: ' + error)
                });
            this.dataGrid.clearSelection();
            this.dataGrid.clearSorting();
            this.dataGrid.pageIndex(0);
            this.dataGrid.clearGrouping()
        },
        refreshFechaDataGrid() {
            this.dataGridFecha.pageIndex(0);
            this.dataGridFecha.clearSelection();
            this.dataGridFecha.clearSorting();
            this.dataGridFecha.columnOption('Fecha', 'sortOrder', 'desc');
        },
        addRow() {
            if (this.StatusAdmision != 'A') {
                this.$vs.notify({
                    time: 4000,
                    title: 'Antecedentes',
                    text: 'La admisión no está activa, no se puede agregar nuevo registro',
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'warning',
                    position: 'bottom-center'
                })
            } else {
                this.soluciones = this.solucionesIngesta.slice(0);
                this.dataGrid.addRow();
            }
        },
        editorHorarioValue(e) {
            if (e.parentType === 'dataRow' && e.dataField === 'Horario') {
                e.setValue(new Date().getHours());
                e.editorOptions.value = new Date().getHours();
            }
            if (e.parentType === 'dataRow' && e.dataField === 'FechaIngesta') {
                e.setValue(new Date());
                e.editorOptions.value = new Date();
            }
        },
        formatFecha(date) {
            const year = new Date(date).toLocaleString('default', {
                year: 'numeric'
            });
            const month = new Date(date).toLocaleString('default', {
                month: '2-digit'
            });
            const day = new Date(date).toLocaleString('default', {
                day: '2-digit'
            });

            return [year, month, day].join('');
        },
        formatFechaFiltro(fecha) {
            fecha = fecha.split('/');
            const year = fecha[2];
            const month = fecha[1];
            const day = fecha[0];

            return [month, day, year].join('/');
        },
        filtrarFecha(fecha) {
            fecha = fecha.split('/');
            const year = fecha[2];
            const month = fecha[0];
            const day = fecha[1];

            this.Cargar(false, day, month, year);

        },
        //Método para detectar cuando se selecciona una fila de la tabla de filtrado de fecha
        onSelectionChanged(e) {
            if (e.selectedRowsData.length > 0) {
                const data = e.selectedRowsData[0].fecha;
                this.filtrarFecha(data);
            }
        },
        editorSoluciones(e) {
            if (e.parentType === 'dataRow' && e.dataField === 'Solucion' && e.row.data.NoEditable === true) {
                e.editorOptions.disabled = true
            }
        },
        allowDeleting(e) {
            if (e.row.data.NoEditable === true) {
                return false
            }
            return true
        },
        noBlankSpace(e) {
            if (e.event.keyCode === 32) {
                e.event.preventDefault()
            }

        }
    },
    mounted() {
        this.Cargar(true);
    },
    watch: {
        'NumeroExpediente'(newval) {
            if (newval != undefined)
                this.Cargar(true)
        },
    },
    computed: {
        ShouldRender() {
            return Boolean(this.SerieAdmision) && Boolean(this.CodigoAdmision) && Boolean(this.NumeroExpediente)
        },

        dataGrid: function () {
            return this.$refs[dataGridRefKey].instance;
        },
        dataGridFecha: function () {
            return this.$refs[dataGridFechaKey].instance;
        }
    },
}
</script>

<style>
#intravenosa .dx-item-content .dx-box-item-content {
    display: contents !important;
}
</style>
