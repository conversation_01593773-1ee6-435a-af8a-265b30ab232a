<template>
<div class="afiliado-siniestro-container vx-card p-2">
    <div>
        <DxDataGrid v-for="(item,key) in OpcionSinietro" :key="key" :ref="RefDataGridSinistro" :data-source="SiniestroDataSource" v-bind="VitalicioOptions" :columns="item.columns" :visible="key == OpcionSinietroSelec" style="max-height: calc(100vh - 310px)" />
    </div>
</div>
</template>

<script>

const dataGridSinistro = 'dataGridSinistro'
const _OpcionSinietro = [

    {
        key: '<PERSON><PERSON><PERSON>',
        name: '<PERSON><PERSON><PERSON>',
        columns: [{
                caption: 'Máximo Vitalicio',
                dataField: 'MaximoVitalicio',
                ..._confMoneda,
            },
            {
                caption: 'Total Acumulado',
                dataField: 'TotalAcumulado',
                ..._confMoneda,
            },
            {
                caption: 'Siniestro Acumulado',
                dataField: 'SiniestroAcumulado',
                dataType: 'number',
                ..._confMoneda,
            },
            {
                caption: 'Participación',
                dataField: 'Participacion',
                dataType: 'number',
                ..._confMoneda,
            },
            {
                caption: 'Total Disponible',
                dataField: 'TotalDisponible',
                ..._confMoneda,
            },
        ]
    },
    {
        key: 'Anual',
        name: 'Anual',
        columns: [

            {
                caption: 'Año',
                dataField: 'Anio',
                dataType: 'number',
                sortOrder: 'desc',
            },
            {
                caption: 'Máximo Anual',
                dataField: 'MaximoAnual',
                ..._confMoneda,
            },
            {
                caption: 'Participación',
                dataField: 'Participacion',
                ..._confMoneda,
            },
            {
                caption: 'Total Acumulado',
                dataField: 'TotalAcumulado',
                ..._confMoneda,
            },
            {
                caption: 'Total Disponible',
                dataField: 'TotalDisponible',
                ..._confMoneda,
            },
        ]
    },
    {
        key: 'Evento',
        name: 'Evento',
        columns: [

            {
                caption: 'Serie',
                dataField: 'SerieAdmision',
                sortOrder: 'asc',
            },
            {
                caption: 'Admisión',
                dataField: 'Admision',
                sortOrder: 'asc',
            },
            {
                caption: 'Máximo Evento',
                dataField: 'MaximoXEvento',
                ..._confMoneda,
            },
            {
                caption: 'Total Evento',
                dataField: 'TotalEvento',
                ..._confMoneda,
            },
            {
                caption: 'Total Disponible',
                dataField: 'TotalDisponible',
                ..._confMoneda,
            },

        ]
    }, {
        key: 'SinCierre',
        name: 'Sin Cierre',
        columns: [

            {
                caption: 'Año',
                dataField: 'Anio',
            },
            {
                caption: 'Total Acumulado',
                dataField: 'TotalAcumulado',
                ..._confMoneda,
            },
            {
                caption: 'Total Encamamiento',
                dataField: 'TotalEncamamiento',
                ..._confMoneda,
            },

        ]
    }, {
        key: 'Historico',
        name: 'Histórico',
        columns: [

            {
                dataField: 'Anio',
                caption: 'Año',
                sortOrder: 'asc',
            },
            {
                dataField: 'Mes',
                sortOrder: 'asc',
            },
            {
                dataField: 'Serie',
                sortOrder: 'asc',
            },
            {
                dataField: 'Admision',
                caption: 'Admisión',
                sortOrder: 'asc',
            },
            {
                dataField: 'Categoria',
                caption: 'Categoría',
                sortOrder: 'asc',
            },
            {
                dataField: 'Valor',
                ..._confMoneda
            },
            {
                dataField: 'Participacion',
                caption: 'Partición',
                ..._confMoneda,
            },
            {
                dataField: 'CuantasOrdenes',
                caption: 'Cantidad',
            },

        ],
    }
]
const _ToolBarButtonItem = {
    widget: "dxButton",
    locateInMenu: 'auto',
    location: 'center',
}
import {
    DefaulGridOptions,
    _confMoneda
} from './data.js'
export default {
    name: 'AfiliadosSiniestro',
    components: {
        
    },
    data() {
        return {

            OpcionSinietroSelec: 0, 
            OpcionSinietro: _OpcionSinietro,
            RefDataGridSinistro: dataGridSinistro,
            SiniestroDataSource: [], //los datos de siniestralidad a mostrar
            Columnas: [],
            Pagos: null,
            Recibos: null,
            GridToolBar: {
                visible: true,
                items: [
                    'searchPanel',

                ]
            },
            VitalicioOptions: {
                ...DefaulGridOptions,
                export: {
                    enabled: true,
                    formats: ['xlsx'],
                    fileName: 'Siniestro',
                },
                width: '100%',
                height: 'auto',

                toolbar: {
                    items: [
                        'exportButton',
                        ..._OpcionSinietro.map((x, index) => {
                            return {
                                ..._ToolBarButtonItem,
                                options: {
                                    type: index == this.OpcionSinietroSelec?'success': 'default',
                                    onClick: () => this.Cargar(index),
                                    hint: x.name,
                                    text: x.name,
                                },
                            }
                        })
                    ],

                },
            },
        }
    },
    props: {
        NumeroAdhesion: String,
        IdAfiliado: String,
    },
    methods: {

        Cargar(optSinistro) {
            this.SiniestroDataSource = []
            this.DataGridSinistro.clearSorting()
            if (Number.isSafeInteger(optSinistro))
                this.OpcionSinietroSelec = optSinistro

            this.Columnas = this.OpcionSinietro[this.OpcionSinietroSelec].columns
            //reiniciar ordenamientos
            this.OpcionSinietro[this.OpcionSinietroSelec].columns.forEach(x => {
                if (x.sortOrder)
                    this.DataGridSinistro.columnOption(x.dataField, 'sortOrder', x.sortOrder)
            })

            this.axios.post("/app/v1_afiliados/Siniestro" + this.OpcionSinietro[this.OpcionSinietroSelec].key, {
                    NumeroAdhesion: this.NumeroAdhesion,
                    IdAfiliado: this.IdAfiliado,
                })
                .then((resp) => {
                    this.SiniestroDataSource = resp.data
                })
        },
    },
    mounted() {
        this.Cargar()
    },
    watch: {
        'NumeroAdhesion'() {
            this.Cargar()
        }
    },
    computed: {
        DataGridSinistro: function () {
            return this.$refs[this.RefDataGridSinistro][this.OpcionSinietroSelec].instance;
        },
    },

}
</script>

<style>

</style>
