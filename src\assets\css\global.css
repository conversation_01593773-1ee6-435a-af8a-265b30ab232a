@font-face {
    font-family: "Orbitron"; 
    src: url('../fonts/Orbitron-Regular.ttf');
}
@font-face {
    font-family: "Montserrat";
    src: url('../fonts/Montserrat-Regular.ttf');
}

.con-vs-popup .vs-popup {
    width: 65% !important;
    min-width:350px;
    /* height: auto !important; */
 }

 .v-nav-menu .scroll-area-v-nav-menu {
    height: calc(var(--vh, 1vh) * 100 - 91px) !important;
 }

 .vs-input--text-validation-span{
    position: absolute;
    white-space: nowrap;
 }

 .card-border{
     border:1px solid #ccc;
     box-shadow: none;
 }

 tr.tr-values:hover{
     background-color:#03a8f43d;
     /* color:white; */
 }

.vs-input--input.normal{
    border:1px solid rgba(0, 0, 0, 0.4) !important;
}

input[type=date].vs-input--input.normal{
    padding:0.5rem !important;
}
/* padding:2px !important; */

.span-text-validation{
    background-color:#c0392b;
    color:white;
    border-radius:0 0 10px 5px;
    padding:0px 10px;
    font-size:8px;
    margin-left:2px;
}

.vs-inputx:required{
    position: relative;
}
span.required .vs-component{
    position:relative;
}
span.required  label::before,span.required  .vs-input--label::before  {
    /* background-color:green; */
    content: "* ";
    position: relative;
    font-weight: bold;
    /* padding:10px; */
    color:#e74c3c;
    top:0;
    right: 0;
    font-size:16px;
    line-height:0.1;
    height:10px;
    /* float:left; */
}

.popup-generar {
    height: 100%
}

/**
 * Muestra editar en un card
 */
.vx-card.modal{
    height: 100%;
    
}
.vx-card.modal .vx-card__collapsible-content{
    height: calc(100% - 45px);
    position: relative;
}
.vx-card.modal .vx-card__collapsible-content .vx-card__body{
    /* background-color: green; */
    height: calc(100% - 47px);
    overflow: auto;   
}

.vx-card.modal .vx-card__collapsible-content .bottom-card{
    /* background-color: blue; */
    position: absolute;
    bottom:0;
    left: 0;
    padding: 5px;
    width: 100%;
}

.vx-card.edicion{
    border:5px solid rgba(var(--vs-primary),1);
    /* display: flex; */
}
.vx-card.edicion::before{
    content:"Edición";
    position: absolute;
    text-align: center;
    border-radius: 0 0 5px 5px;
    color:white;
    width:100px;
    background-color: rgba(var(--vs-primary),1);
    left:calc(50% - 50px);
}

.includeIconOnly.small{
    width: 32px !important;
}

 
.vs-popup-md .vs-popup {
    width: 990px !important;
}

.sm .vs-popup {
    width: 660px !important;
}

.vs-sidebar-group .vs-sidebar-group-items .vs-sidebar-group .group-header{
    /* background-color: red; */
    font-size: 13px;
    padding-left: 10px;
}

.vs-sidebar-group.vs-sidebar-group-active > .group-header{
    background-color: rgba(var(--vs-primary),1) !important;
    color:white;
    /* font-weight: bold; */
}

.vs-sidebar-group .vs-sidebar-group.vs-sidebar-group-active > .group-header{
    background-color: rgba(var(--vs-primary),0.8) !important;
    color:white;
    /* font-weight: bold; */
}
.vs-sidebar-group .group-header{
    margin-bottom: 0 !important;
}

.vs-sidebar-group.vs-sidebar-group-open ul{
    border-left:1px solid rgba(0, 0, 0, 0.3);
    margin-top: 0;
    /* box-shadow: inset 2px 0  5px rgba(0, 0, 0, 0.4); */
    /* font-weight: bold; */
}

.vs-sidebar-group .vs-sidebar-group.vs-sidebar-group-open ul{
    
    border-left:2px solid  rgba(var(--vs-primary),0.6);
}

.vs-sidebar--item a{
    overflow: hidden;  
}

.vs-sidebar--item.vs-sidebar-item-active a{
    background: rgba(var(--vs-primary),0.6) !important;
    box-shadow: none !important;
    overflow: hidden;  
}
.vs-sidebar--item.vs-sidebar-item-active a span{
    /* color:#626262 !important; */
    /* font-weight: ; */
}

.vs-sidebar-group .vs-sidebar-group-items .vs-sidebar-group .vs-sidebar-group-items a{
    /* background-color: red; */
    /* font-size: 12px; */
    padding-left: 7px;
}

input:disabled {color:black}
body{
    color:#2980B9;
}

.seleccionEmpresa .vs-popup{
    /* background-color: red !important; */
    width: 80% !important;
    max-width: 800px;
}

.seleccionEmpresa .vs-popup .seleccion_empresa.activo{
    background-color: rgba(var(--vs-primary),1);
    color:white
}
.seleccionEmpresa .vs-popup .empresa{
    border-right: 1px solid #ccc;
}

.seleccion_empresa {
    padding: 15px;
    background-color: white;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
    cursor: pointer;
    border-radius: 5px;
    margin-bottom: 5px;
}


div.bottom{
    position: fixed;
    bottom: 0px;
    right: 0;
    width:calc(100% - 82px);
    padding: 10px;
    background-color: white;
    border-top: 2px solid rgba(var(--vs-primary),0.6);
    /* box-shadow: 0 -5px  5px rgba(0, 0, 0, 0.3); */

}

@media print { 
    /* All your print styles go here */
    .app-contenedor { display: none !important; } 
    .pdfDocument {
        display: inline;
    }
   }