<template>
    <vx-card title="Auto Despacho Sub-Bodega Laboratorio">
        <ValidarPermisoHuella ref="componenteValidarPermiso" :permisoHuella="PermisoRecepcionDepartamento" @getPermisosHuellas="PeticionRecepcion"></ValidarPermisoHuella>
        <div class="content content-pagex">
            <vs-divider></vs-divider>
            <br>
            <div class="flex flex-wrap">                
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">                    
                    <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                        <label class="typo__label">Destino:</label>       
                        <strong>{{ cod_departamentoDestino.Codigo }}</strong>                           
                        <multiselect
                        v-model="cod_departamentoDestino"
                        :options="Lista_Departamentos_destino_consulta"
                        :searchable="true"
                        :close-on-select="true"
                        :show-labels="false"
                        :allow-empty="false"
                        :custom-label="Departamentos_seleccionado"
                        placeholder="Seleccionar departamento"
                        @input="onChangeDepartamentoDestino"
                        :danger="errors.length > 0"
                        :danger-text="errors.length > 0 ? errors[0] : null"
                        >
                        <span
                        slot="noOptions">Lista no disponible.</span>
                        </multiselect>
                    </ValidationProvider>                                                                  
                </div>
                <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2 pt-2 pl-10">
                    <label v-if="this.permiso_bodega_vencido == 'S'" class="typo__label">Envio Bodega Vencido:</label> 
                    <vs-switch v-if="this.permiso_bodega_vencido == 'S'" v-model="enviar_bodega_vencido" />      
                </div>
            </div>

            <div class="flex flex-wrap">
    
                <div style="margin-right: 20px" class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                    <label class="typo__label">Estado</label>
                    <multiselect
                      v-model="cb_lista_operacion"
                      :options="lista_estado"
                      :searchable="true"
                      :close-on-select="true"
                      :show-labels="false"
                      :allow-empty="false"
                      :custom-label="Operacion_seleccionada"
                      placeholder="Seleccionar"
                      @input="onChangeEstado">
                      <span slot="noOptions">Lista no disponible.</span>
                    </multiselect>
                </div>
    
                <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7 m-1">
                    <label class="typo__label">Fecha Inicio:</label>
                    <vs-input type="date" v-model="fecha_inicio" name="date1" />
                </div>
    
                <div class="w-1/7 md:w-1/7 lg:w-1/7 xl:w-1/7 m-1">
                    <label class="typo__label">Fecha Final:</label>
                    <vs-input type="date" v-model="fecha_final" name="date1" />
                </div>
    
                <vs-button style="float:left;margin: 20px" color="primary" type="filled" icon-pack="fas" icon="fa-search" @click="Consultar_OrdenEnc()"> Búsqueda</vs-button>
    
                <vs-button color="primary" style="float:left;margin: 20px" type="filled" icon-pack="fas" icon="fa-plus" @click="Emergente_Detalle('','N')"> Nuevo</vs-button>
                
            </div>
            <vs-divider></vs-divider>
    
            <vs-table2 max-items="10" pagination :data="Lista_vista" search id="tb_lista_solicitud">
    
                <template slot="thead">
                    <th width="130px">Nº. Requerimiento</th>
                    <th>Fuente</th>
                    <th>Fecha(s)</th>
                    <th>Observaciones</th>
                    <th></th>
    
                </template>
    
                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">
    
                        <vs-td2 width='5%'>
                            {{tr.IDCODIGOREQUERIMIENTO}}
                        </vs-td2>
    
                        <vs-td2 width='20%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                {{tr.IdBodegaFuentefk +' - '+tr.NOMBRE_BODEGA_FUENTE}}
                            </div>
                        </vs-td2>
    
                        <vs-td2 width='20%'>    
                            <div style="word-wrap: break-word;white-space: wrap;">
                                <label>Solicitado: {{tr.FECHASOLICITUD}}</label>
                                <br v-if="tr.FECHASOLICITUD">
                                <label v-if="tr.FECHAENTREGADA">Despachado: {{tr.FECHAENTREGADA}}</label>
                                <br v-if="tr.FECHAENTREGADA">
                                <label v-if="Id_estado_seleccionado  === 'F'">Aceptado: {{tr.FECHARECIBIDA}}</label>                                
                            </div>
                        </vs-td2>

                        <vs-td2  width='30%'>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                {{ tr.OBSERVACIONES }}
                            </div>
                        </vs-td2>    
    
                        <vs-td2 v-if="Id_estado_seleccionado  == 'E'" width='10%' align="right" style="white-space:nowrap;min-width: 200px;">
                            <vx-tooltip text="Consultar" style="display:inline-block;margin-right:30px">
                                <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" @click="Emergente_Detalle(data[indextr], 'E')"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip text="Anular" style="display:inline-block;margin-right:2px">
                                <vs-button color="red" icon-pack="fas" icon="fa-times"   @click="Anular_Movimiento(data[indextr])"></vs-button>
                            </vx-tooltip>
                        </vs-td2>
                        <vs-td2 v-else-if="Id_estado_seleccionado  == 'P'" width='10%' align="right" style="white-space:nowrap;min-width: 200px;">
                            <vx-tooltip text="Consultar" style="display:inline-block;margin-right:30px">
                                <vs-button color="primary" icon-pack="fas" icon="fa-eye" style="display:inline-block;margin-right:2px" @click="Emergente_Detalle_Pedido(data[indextr])"></vs-button>    
                            </vx-tooltip>
                            <vx-tooltip text="Imprimir" style="display:inline-block;margin-right:30px">
                                <vs-button color="primary" icon-pack="feather" icon="icon-printer" style="display:inline-block;margin-right:2px" @click="Consultar_Movimiento(data[indextr])"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip text="Anular" style="display:inline-block;margin-right:2px">
                                <vs-button color="red" icon-pack="fas" icon="fa-times"   @click="Anular_Movimiento(data[indextr])"></vs-button>
                            </vx-tooltip>
                        </vs-td2>
                        <vs-td2 v-else-if="Id_estado_seleccionado  == 'T,N'" width='10%' align="right" style="white-space:nowrap;min-width: 200px;">
                            <vx-tooltip text="Consultar" style="display:inline-block;margin-right:30px">
                                <vs-button color="primary" icon-pack="fas" icon="fa-arrow-left" style="display:inline-block;margin-right:2px" @click="Emergente_Detalle_Recepcion(data[indextr])"></vs-button>
                            </vx-tooltip>
                            <vx-tooltip text="Imprimir" style="display:inline-block;margin-right:30px">
                                <vs-button color="primary" icon-pack="feather" icon="icon-printer" style="display:inline-block;margin-right:2px" @click="Consultar_Movimiento(data[indextr])"></vs-button>
                            </vx-tooltip>
                        </vs-td2> 
    
                    </tr>
                </template>
            </vs-table2>
    
            <!----------------------- NUEVA ASOCIACIÓN ---------->
            <vs-popup classContent="popup-example" :title="Descripcion_Emergente" :active.sync="Estado_Emergente">
                <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
                    <div style="border:2px solid #1B5E20;border-radius:5px;padding:5px;font-size:12px;background-color: #C8E6C9">
                        Sucursal Origen: <b style="font-size:1vw">{{sesion.sesion_sucursal_nombre}}</b>
                    </div>
                    <br>
                    <div>
                        <vs-alert color="danger" active="true" v-if="MensajeAdvertenciaMontoLimite && MensajeAdvertenciaMontoLimite.trim().length > 0">
                            {{ MensajeAdvertenciaMontoLimite }}
                        </vs-alert>
                    </div>
                    <br>
                    <b> <small>No. Requerimiento </small></b>
                    <b style="font-size:2vw">{{IdCodigoRequerimiento}}</b>
    
                    <!--- Ubicación Destino -->
                    <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <br>
                        <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                            <label class="typo__label">Bodega Fuente:</label>
                            <strong>{{ cod_bodegaFuente.CODIGO }}</strong>
                            <multiselect
                              v-model="cod_bodegaFuente"
                              :options="Lista_bodegas_fuente"
                              :searchable="true"
                              :close-on-select="true"
                              :show-labels="false"
                              :custom-label="Bodegas_seleccionado"
                              placeholder="Seleccionar bodega"
                              @input="onChangeBodegaFuente"
                              :danger="errors.length > 0"
                              :danger-text="errors.length > 0 ? errors[0] : null"
                              :disabled="IdCodigoRequerimiento>0"
                              >
                              <span
                              slot="noOptions">Lista no disponible.</span>
                              </multiselect>
                        </ValidationProvider>
                    </div>
                    <!--- Ubicación Destino -->
                    <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <br>
                        <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                            <label class="typo__label">Destino:</label>       
                            <strong>{{ cod_departamentoDestino.CODIGO }}</strong>                                                 
                            <multiselect
                              v-model="cod_departamentoDestino"
                              :options="Lista_Departamentos_destino_consulta"
                              :searchable="true"
                              :close-on-select="true"
                              :show-labels="false"
                              :custom-label="Departamentos_seleccionado"
                              placeholder="Seleccionar Departamento"
                              @input="onChangeDepartamentoDestino"
                              :danger="errors.length > 0"
                              :danger-text="errors.length > 0 ? errors[0] : null"
                              :disabled="Deshabilitar_campos">
                              <span
                              slot="noOptions">Lista no disponible.</span>
                              </multiselect>
                        </ValidationProvider>                                                
                    </div>

                    <br>
                    <!--- Producto -->
                    <vs-divider>Detalle</vs-divider>
    
                    <div class="flex flex-wrap">
    
                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                            <ValidationProvider name="producto" rules="required" v-slot="{ errors }" class="required">
                                <label class="typo__label">Producto</label>
                                <vx-input-group class="">
                                    <vs-input id="busquedaProducto" v-model="producto_buscar" @keydown.enter="cargar_producto()" @keydown.tab.prevent="cargar_producto()" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                    <template slot="append">
                                        <div class="append-text btn-addon">
                                            <button type="submit" v-show="false" name="button"></button>
                                            <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="cargar_producto()" icon="fa-search"></vs-button>
                                        </div>
                                    </template>
                                </vx-input-group>
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                            <ValidationProvider name="cant_solicitada" rules="required|numero_min:0" v-slot="{ errors }" class="required">
                                <label class="typo__label">Cant. Trasladar</label>
                                <vs-input id="cantidad_sol" class="w-full" type="number" count="100" v-model="Cantida_solicitar"  :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                            </ValidationProvider>
                            <div style="word-wrap: break-word;white-space: wrap;">
                                <label v-if="Redondeo(parseFloat(Producto_seleccionado.Existencias)) > 0" style="width:100px; border-radius: 3px;  background-color:#69F0AE;text-align: center;color:black;font-size:15px; "> Disponible: {{Producto_seleccionado.Existencias}} </label>
                                <label v-else-if="Redondeo(parseFloat(Producto_seleccionado.Existencias)) <= 0" style="width:100px; border-radius: 3px;  background-color:#FF5252;text-align: center;color:white;font-size:15px; "> Disponible: {{Producto_seleccionado.Existencias}} </label>                              
                            </div>
                        </div>
                        <!------ BOTONES DE ACCION ---->
                        <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6">
                            <vs-button color="primary" style="float:left;margin: 20px" type="filled" icon-pack="fas" icon="fa-plus" @keydown.tab.prevent="Confirmacion_Transaccion()" @click="Confirmacion_Transaccion()">Agregar</vs-button>
                        </div>
                    </div>
    
                    <!----- Detalle producto seleccionado--->
                    <div v-if="Producto_seleccionado.Codigo>0" class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <!---<div style="border:2px solid #1B5E20;border-radius:5px;padding:5px;font-size:12px;background-color:#A5D6A7">--->
                        <div style="border-radius:5px;padding:5px;font-size:12px;background-color:#A5D6A7">
                            Código: {{Producto_seleccionado.Codigo}}
                            <br>
                            Producto: {{Producto_seleccionado.marca_comercial}}
                            <br>
                            Presentación: {{Producto_seleccionado.Presentacion}}
                            <br>
    
                        </div>
                    </div>
                    <!--- Mostrar detalle de la orden--->
                    <vs-table2 max-items="10" pagination :data="Lista_detalle" id="tb_lista_solicitud">
    
                        <template slot="thead">
                            <th>Linea</th>
                            <th>Código</th>
                            <th>Producto</th>
                            <th>Unidad Medida</th>
                            <th>Cant. Solicitada</th>
                            <th></th>
                        </template>
    
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 width='5%'>                                                                            
                                        {{tr.LINEA}}                                    
                                </vs-td2>  
                                <vs-td2 width='5%'>                                                                            
                                        {{tr.IDPRODUCTOFK}}                                    
                                </vs-td2>                                        
                                <vs-td2 width='65%'>
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                                        
                                        {{tr.DESCRIPCION_PRODUCTO}}                                        
                                    </div>
                                </vs-td2>
                                <vs-td2 width='15%'>
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">
                                        {{tr.DESCRIPCION_UNIDAD}}
                                    </div>                                    
                                </vs-td2>
                                <vs-td2 width='5%'>
                                    {{tr.CANTIDAD_PEDIDA}}
                                </vs-td2>
                                <vs-td2 width='5%'>
                                    <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="Eliminar_Registro(data[indextr])"></vs-button>
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
    
                    <vs-divider></vs-divider>
                    <div class="flex flex-wrap">
                        <div class="w-full md:w-full lg:w-full xl:w-full">
                            <vs-textarea label="Observaciones" type="text" class="w-full" v-model="Observaciones" />
                        </div>
                    </div>
                    <vs-divider></vs-divider> 
                    <vs-button
                      color="primary"
                      style="float:right;margin: 5px"
                      type="filled"
                      icon-pack="feather"
                      icon="icon-save"
                      id="btn_confirmacion"
                      @click="ValidarMontoLimite(1)"> Finalizar Requerimiento</vs-button>
                    <vs-divider></vs-divider>                    
                </div>
            </vs-popup>
    
            <!--------------- Resultado busqueda Producto-------------->
            <vs-popup classContent="popup-example" title="Resultado Búsqueda" :active.sync="Estado_VentanaEmergente_Busqueda" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
                <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
    
                    <form>
    
                        <vs-divider></vs-divider>
                        <vs-table2 max-items="10" pagination :data="producto" id="tb_departamentos">
    
                            <template slot="thead">
                                <th>Codigo</th>
                                <th>Descripcion</th>
                                <th>Concentración</th>
                                <th>Presentación</th>
                                <th>Existencias</th>
                                <th></th>
                            </template>
    
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2 :data="data[indextr].Codigo">
                                        {{data[indextr].Codigo}}
                                    </vs-td2>
    
                                    <vs-td2 :data="data[indextr].Descripcion">
                                        <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">
                                            {{data[indextr].Descripcion}}
                                        </div>
                                    </vs-td2>
    
                                    <vs-td2 :data="data[indextr].Concentracion">
                                        {{data[indextr].Concentracion}}
                                    </vs-td2>
    
                                    <vs-td2 :data="data[indextr].PresentacionNombre">
                                        {{data[indextr].PresentacionNombre}}
                                    </vs-td2>

                                    <vs-td2 style="text-align:right" :data="data[indextr].Existencia" width='15%'>
                                        {{data[indextr].Existencia}}
                                    </vs-td2>
    
                                    <vs-td2 align="right">
                                        <vs-button color="primary" icon-pack="fas" icon="fa-check-circle" style="display:inline-block;margin-right:2px" @click="Seleccionar_producto(data[indextr])"></vs-button>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </form>
    
                </div>
            </vs-popup>
    
            <!----------------------- RECEPCION DE PRODUCTO ---------->
            <vs-popup classContent="popup-example" :title="Descripcion_Emergente" :active.sync="Estado_Emergente_Recepcion">
                <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">  
                    <div style="border:2px solid #1B5E20;border-radius:5px;padding:5px;font-size:12px;background-color: #C8E6C9">
                        Sucursal Origen: <b style="font-size:1vw">{{sesion.sesion_sucursal_nombre}}</b>
                    </div>
                    <br>          
                    <b> <small>No. Requerimiento: </small></b>
                    <b style="font-size:2vw">{{IdCodigoRequerimiento}}</b>
                    <br>
                    <br>    
                    <div class="w-1/2 md:w-1/2 lg:w-6/12 xl:w-6/12">
                         <b> <small>Fecha Solictud: </small></b>
                        <b>{{Requerimiento_seleccionado.FECHASOLICITUD}}</b>
                    </div>
                    <!--- Ubicación Destino -->
                    <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <br>
                        <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                            <label class="typo__label">Bodega Fuente:</label>
                            <strong>{{id_bodega_fuente}}</strong>
                            <vs-input  type="text" v-model="nombre_bodega_fuente" :disabled="Deshabilitar_campos" class="w-full" 
                                       :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null"/>
                        </ValidationProvider>
                    </div>  
                    <br>                      
                    <div class="w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/2">
                        <br>
                        <ValidationProvider name="Bodegas" rules="required" v-slot="{ errors }" class="required">
                            <label class="typo__label">Destino:</label>
                            <strong>{{id_departamento_destino}}</strong>
                            <vs-input  type="text" v-model="nombre_departamento_destino" :disabled="Deshabilitar_campos" class="w-full" 
                                       :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null"/>
                        </ValidationProvider>
                    </div>
                    <br>
                    <div v-if="Consulta_Aceptados" class="w-full md:w-full lg:w-full xl:w-full">
                        Usuario Recepción:
                        <b>{{Requerimiento_seleccionado.CorporativoDespacho+"   "+Requerimiento_seleccionado.NombreDespacho}}</b>                      
                    </div>
                    <vs-divider>Detalle</vs-divider>
    
                    <!--- Mostrar detalle de la orden--->
                    <vs-table2 max-items="10" pagination :data="Lista_detalle" id="tb_lista_solicitud">
    
                        <template slot="thead">
                            <th>Linea</th>
                            <th>Código</th>
                            <th>Producto</th>
                            <th>Unidad Medida</th>
                            <th>Cant. Solicitada</th>
                            <th v-if="!Consulta_Pedido">Cant. Despachada</th>
                        </template>
    
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 width='5%'>                                                                            
                                        {{tr.LINEA}}                                    
                                </vs-td2> 
                                <vs-td2 width='5%'>                                                                            
                                        {{tr.IDPRODUCTOFK}}                                    
                                </vs-td2>  
                                <vs-td2 width='65%'>
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                                        
                                        {{tr.DESCRIPCION_PRODUCTO}}                                        
                                    </div>
                                </vs-td2>
                                <vs-td2 width='15%'>
                                    <div class="col-md-8" style="word-wrap: break-word;white-space: wrap;">                                        
                                        {{tr.DESCRIPCION_UNIDAD}}
                                    </div>
                                </vs-td2>
                                <vs-td2 width='5%'>
                                    {{tr.CANTIDAD_PEDIDA}}
                                </vs-td2>
                                <vs-td2 v-if="!Consulta_Pedido" width='5%'>
                                    {{tr.CANTIDAD_ENTREGA}}
                                </vs-td2>                                
                            </tr>
                        </template>
                    </vs-table2>
                    <vs-divider></vs-divider>
                    <div class="flex flex-wrap">
                        <div class="w-full md:w-full lg:w-full xl:w-full">
                            <vs-textarea disabled label="Observaciones" type="text" class="w-full" v-model="Observaciones" />
                        </div>                        
                    </div>
                    <vs-divider></vs-divider>
                        <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-printer" id="btn_movimiento" @click="Consultar_Movimiento(Requerimiento_seleccionado)"> Imprimir </vs-button>                
                    <vs-divider></vs-divider>
                </div>
            </vs-popup>
        </div>
    </vx-card>
    </template>
    
    <script>
    import Multiselect from 'vue-multiselect'
    import moment from "moment"
    import "vue-multiselect/dist/vue-multiselect.min.css"  
    import ValidarPermisoHuella from "/src/components/validador-huella/ValidarPermisoHuella.vue"      
    export default {
        components: {
            Multiselect,
            ValidarPermisoHuella
        },
        watch:{
            enviar_bodega_vencido(bodegaVencido){
                if(!this.cod_departamentoDestino && this.cod_departamentoDestino.length == 0)
                    return
                
                if(bodegaVencido && this.id_bodega_fuente==5)
                    return

                for(const bodega of this.Lista_bodegas_fuente){          
                    if(bodegaVencido){
                        if(bodega.CODIGO == 5){
                            this.id_bodega_fuente = bodega.CODIGO
                            this.cod_bodegaFuente = bodega
                            break
                        }
                        
                    }                                                                                 
                }                    

            },
            Estado_Emergente(value){
                if(value==false){
                    this.InsertoEncabezado = false;
                    this.MensajeAdvertenciaMontoLimite = '';
                }                
            }
        },
        data() {
            return {
                Estado_VentanaEmergente_Busqueda: false,
                PermisoRecepcionDepartamento:'',
                cb_lista_operacion: '',
                Descripcion_Emergente: '',
                MensajeAdvertenciaMontoLimite: '',
                MontoDisponibleValido: false,
                Consulta_Pedido:false,
                Consulta_Aceptados:false,
                producto_buscar: '',                
                lista_estado: [{
                        ID: 'E',
                        DESCRIPCION: 'Edición'
                    },                   
                    {
                        ID: 'T,N',
                        DESCRIPCION: 'Auto Despachos/Anulados'
                    }
                ],
                Id_estado_seleccionado: 0,
                fecha_inicio: '',
                fecha_final: '',
                Lista_vista: [],
                Estado_Emergente: false,
                Estado_Emergente_Recepcion: false,
                enviar_bodega_vencido: false,
                permisos_tipos_requerimientos: [],
                permiso_bodega_vencido:'',
                Lista_bodegas_fuente: [],
                Lista_Departamentos_destino_consulta: [],
                id_bodega_fuente: '',
                nombre_bodega_fuente: '',
                id_departamento_destino: '',
                nombre_departamento_destino: '',
                cod_bodegaFuente: '',
                cod_departamentoDestino: '',    
                Producto_seleccionado: {
                    nombre: '',
                    marca_comercial: '',
                    Principio_activo: '',
                    Concentracion: '',
                    Presentacion: '',
                    Codigo: '',
                    codigo_interno: '',
                    Activo: 'N',
                    Existencias:''
                },
                Requerimiento_seleccionado:{},
                IdCodigoRequerimiento: 0,
                InsertoEncabezado: false,
                Cantida_solicitar: '',    
                Lista_detalle: [],
                Deshabilitar_campos: false,
                producto: [],    
                Observaciones: '',
                listado_reportes: [],
                listado_source: {
                    CODIGO_SOLICITUD: '',
                    CODIGO_MOVIMIENTO: '',
                    CODIGO_REQUERIMIENTO: '',
                }                   
            }
        },
        mounted() {
    
            this.cb_lista_operacion = {
                ID: 'E',
                DESCRIPCION: 'Edición'
            }
            this.Id_estado_seleccionado = 'E';                     
            for(let privilegio of this.$store.state.privilegios){
               if(privilegio.Privilegio.includes("AUTO_REQUERIMIENTO")){                      
                   let tipobodega = privilegio.Privilegio.split('_')[2];
                   if(tipobodega == 'T'){
                     this.permiso_bodega_vencido = 'S'
                   }else{
                     this.permisos_tipos_requerimientos.push(privilegio.Privilegio.split('_')[2])                    
                   }                                                       
                }
            }      
            this.Consultar_Departamentos(this.permisos_tipos_requerimientos.join(","));      
            this.ConsultarSubBodega('G',this.permisos_tipos_requerimientos.join(","));             
        },
        computed: {
            sesion() {
                
                return this.$store.state.sesion
            }
        },
        async beforeCreate(){
            this.listado_reportes = await this.$recupera_parametros_reporte('Movimiento Requerimiento')                        
        },
        methods: {
            Redondeo(monto){
                return Math.round(((monto) + Number.EPSILON) * 100) / 100
            },
            async Consultar_Movimiento(datos) {                
                this.listado_source.CODIGO_REQUERIMIENTO = datos.IDCODIGOREQUERIMIENTO
                this.$genera_reporte({
                    Nombre: "Movimiento Requerimiento",
                    Data_source: this.listado_source,
                    Data_report: this.listado_reportes
                }).catch(() => {
                })
            },
            GetDateValue(value) {
                if (value === undefined) {
                    return '';
                } else if (value === null) {
                    return '';
                } else if (value == '') {
                    return '';
                }
                return moment(value).format('DD/MM/YYYY');          
            },
            MontoDisponible(){
                this.axios.post('/app/v1_OrdenCompra/FuncionesMontoLimiteBodega', {
                                    opcion: 'MONTO_DISPONIBLE',       
                                    codigoBodega: this.id_bodega_fuente,
                                    codigoDepartamento: '0'
                          })
                          .then(resp=>{                            
                                if(resp.data.codigo != 0 || resp.data.json.length != 1){
                                    this.MensajeAdvertenciaMontoLimite = ''
                                    return;
                                }

                                if(resp.data.json[0].Estado == 'N' || resp.data.json[0].Estado == 'C'){
                                    this.MensajeAdvertenciaMontoLimite =  'No cuenta con disponibilidad para realizar requerimiento'                
                                }else if(resp.data.json[0].Estado == 'S'){                                
                                    this.MensajeAdvertenciaMontoLimite = 'Monto Disponible ' +
                                                parseFloat(resp.data.json[0].MontoDisponible).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})                                                
                                }else{
                                    this.MensajeAdvertenciaMontoLimite = ''
                                }
                                
                                
                           })
                           .catch(()=>{this.MensajeAdvertenciaMontoLimite = ''})
            },
            ValidarMontoLimite(finalizarRequerimiento){
                var montoSuperado = 0.0;
                this.axios.post('/app/v1_OrdenCompra/FuncionesMontoLimiteBodega', {
                                    opcion: 'CANTIDAD_SUPERADA',       
                                    codigoRequerimiento: this.IdCodigoRequerimiento,
                          })
                          .then(resp=>{                            
                            montoSuperado = resp.data;
                            if(montoSuperado > 0){
                                var montoFormato = parseFloat(montoSuperado*-1).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})
                                this.$vs.dialog({
                                                type: 'confirm',
                                                color: 'danger',
                                                title: 'Monto Límite Bodega',
                                                acceptText: 'Aceptar',
                                                cancelText: 'Cancelar',
                                                text: 'Excedío su límite por '+montoFormato+', favor de revisar su requerimiento'                                                                                            
                                                })
                                return
                            }
                            if(finalizarRequerimiento == 1){
                                this.Finalizar_Requerimiento()
                            }                            
                          })
                          .catch(()=>{})
            },
            Operacion_seleccionada({
                DESCRIPCION
            }) {
                return `${DESCRIPCION} `
            },
            onChangeEstado(value) {
                if (value !== null && value.length !== 0) {
                    if (value.ID == 'S')
                        this.Id_estado_seleccionado = 'P';
                    else
                        this.Id_estado_seleccionado = value.ID;
                    this.Consultar_OrdenEnc();
                } else {
                    this.Id_estado_seleccionado = '';
                }
            },
            Bodegas_seleccionado({
                CODIGO,NOMBRE
            }) {
                return `${CODIGO} - ${NOMBRE} `;
            },
            Departamentos_seleccionado({
                CODIGO,NOMBRE
            }) {
                return `${CODIGO} - ${NOMBRE} `;
            },
            onChangeBodega(value) {
                if (value !== null && value.length !== 0) {
                    this.id_bodega_fuente = value.CODIGO;
    
                } else {
                    this.id_bodega_fuente = '';
                }
            },
            onChangeBodegaFuente(value) {
                if (value !== null && value.length !== 0) {
                    this.id_bodega_fuente = value.CODIGO;
                    this.MontoDisponible()
                } else {
                    this.id_bodega_fuente = '';
                }                
            },
            onChangeDepartamentoDestino(value) {
                if(!value){
                    this.cod_departamentoDestino = ''
                    this.id_departamento_destino = ''
                    return
                }
                if (value !== null && value.length !== 0) {
                    this.id_departamento_destino = value.CODIGO;
                    this.Consultar_OrdenEnc();
                    if(this.enviar_bodega_vencido && this.id_bodega_fuente==5)
                        return

                    for(const bodega of this.Lista_bodegas_fuente){          
                        if(this.enviar_bodega_vencido){
                            if(bodega.CODIGO == 5){
                                this.id_bodega_fuente = bodega.CODIGO
                                this.cod_bodegaFuente = bodega
                                break
                            }
                            
                        }                                      
                        else if( bodega.TipoBodega == value.TipoBodega){                                                
                            this.id_bodega_fuente = bodega.CODIGO
                            this.cod_bodegaFuente = bodega
                            break
                        }                            
                    }                    
                } else {
                    this.cod_bodegaFuente = '';
                    this.id_bodega_fuente = '';
                }
            },
            Emergente_Detalle(Datos, Operacion) {
                this.Estado_Emergente = true;
                this.Limpiar_Campos();
                this.producto_buscar = '';
                this.Cantida_solicitar = '';
                if (Operacion == 'N') {                                        
                    this.IdCodigoRequerimiento = 0;
                    this.Deshabilitar_campos = true;
                    this.Descripcion_Emergente = 'Ingreso Solicitud Requerimiento';
                    this.Lista_detalle = [];
                    this.id_bodega_fuente = '';
                    this.cod_bodegaFuente = '';
                    this.Observaciones = '';
                } else {
                    this.IdCodigoRequerimiento = Datos.IDCODIGOREQUERIMIENTO;
                    this.id_bodega_fuente = Datos.IdBodegaFuentefk;
                    this.id_departamento_destino = Datos.IDDEPARTAMENTO;
                    setTimeout(() => {
                        this.cod_bodegaFuente = {
                            NOMBRE: Datos.NOMBRE_BODEGA_FUENTE,
                            CODIGO: Datos.IdBodegaFuentefk
                        }
                        this.cod_departamentoDestino = {
                            NOMBRE: Datos.NOMBREDEPARTAMENTO,
                            CODIGO: Datos.IDDEPARTAMENTO
                        }
                    }, 500);
                    this.Deshabilitar_campos = true;
                    this.Descripcion_Emergente = 'Editar Solicitud Requerimiento';
                    this.PermisoRecepcionDepartamento = 'RECEPCION_DEPARTAMENTO_'+Datos.IDDEPARTAMENTO;
                    this.Consultar_OrdenDet()
                    this.MontoDisponible()
                }
            },
            Emergente_Detalle_Pedido(Datos) {

                this.Estado_Emergente_Recepcion = true;
                this.Consulta_Pedido = true,
                this.Consulta_Aceptados = false,
                this.producto_buscar = '';
                this.Cantida_solicitar = '';
                this.Requerimiento_seleccionado = Datos;
                this.Observaciones = Datos.OBSERVACIONES;
                this.IdCodigoRequerimiento = Datos.IDCODIGOREQUERIMIENTO;
                this.id_bodega_fuente = Datos.IdBodegaFuentefk;
                this.id_departamento_destino = Datos.IDDEPARTAMENTO;
                this.nombre_bodega_fuente = Datos.NOMBRE_BODEGA_FUENTE;
                this.nombre_departamento_destino = Datos.NOMBREDEPARTAMENTO;
    
                this.Deshabilitar_campos = true;
                this.Descripcion_Emergente = 'Consulta Detalle Requerimiento Pedido';
                this.Consultar_OrdenDet()
    
            },
            Emergente_Detalle_Recepcion(Datos) {
               // 
               // 
                this.Estado_Emergente_Recepcion = true;
                this.Consulta_Pedido = false,
                this.Consulta_Aceptados = true,
                this.producto_buscar = '';
                this.Cantida_solicitar = '';
                this.Requerimiento_seleccionado = Datos;
                this.Observaciones = Datos.OBSERVACIONES;
                this.IdCodigoRequerimiento = Datos.IDCODIGOREQUERIMIENTO;
                this.id_bodega_fuente = Datos.IdBodegaFuentefk;
                this.id_departamento_destino = Datos.IDDEPARTAMENTO;
                this.nombre_bodega_fuente = Datos.NOMBRE_BODEGA_FUENTE;
                this.nombre_departamento_destino = Datos.NOMBREDEPARTAMENTO;

                this.Deshabilitar_campos = true;
                this.Descripcion_Emergente = 'Consulta Requerimiento Despachado';
                this.Consultar_OrdenDet()
    
            },
            Limpiar_Campos() {
                this.Producto_seleccionado.Codigo = '';
                this.Producto_seleccionado.marca_comercial = '';
                this.Producto_seleccionado.Principio_activo = '';
                this.Producto_seleccionado.Concentracion = '';
                this.Producto_seleccionado.codigo_interno = '';
                this.Producto_seleccionado.Presentacion = '';
                this.Producto_seleccionado.Existencias = '';
            },
            Seleccionar_producto(obj) {
    
                this.Producto_seleccionado.Codigo = obj.Codigo;
                this.Producto_seleccionado.marca_comercial = obj.Descripcion;
                this.Producto_seleccionado.Concentracion = obj.Concentracion;
                this.Producto_seleccionado.codigo_interno = obj.codigo_interno;
                this.Producto_seleccionado.Presentacion = obj.PresentacionNombre;
                this.Producto_seleccionado.Existencias = obj.Existencia;
                document.getElementById("cantidad_sol").focus();
                this.Estado_VentanaEmergente_Busqueda = false;
    
            },
            Mostrar_resultado_producto(value,codigo_busqueda) {
                if (value.length === 1) {

                    if(codigo_busqueda != value[0].Codigo){
                        this.$vs.notify({
                            timer:6000,
                            position:'top-center',
                            color: 'warning',
                            title: 'Inventario',
                            text: `El código encontrado (${value[0].Codigo}) no corresponde exactamente al buscado.`,
                        })
                    }
                    
                    value.map(obj => {
                        this.Producto_seleccionado.Codigo = obj.Codigo;
                        this.Producto_seleccionado.marca_comercial = obj.Descripcion;
                        this.Producto_seleccionado.Concentracion = obj.Concentracion;
                        this.Producto_seleccionado.codigo_interno = obj.codigo_interno;
                        this.Producto_seleccionado.Presentacion = obj.PresentacionNombre;
                        this.Producto_seleccionado.Existencias = obj.Existencia;
                        document.getElementById("cantidad_sol").focus();
    
                    })
    
                } else if (value.length > 1) {
                    var productoEncontrado = value.find(obj =>obj.Codigo == codigo_busqueda)
                    if(productoEncontrado){                        
                        this.Producto_seleccionado.Codigo = productoEncontrado.Codigo;
                        this.Producto_seleccionado.marca_comercial = productoEncontrado.Descripcion;
                        this.Producto_seleccionado.Concentracion = productoEncontrado.Concentracion;
                        this.Producto_seleccionado.codigo_interno = productoEncontrado.codigo_interno;
                        this.Producto_seleccionado.Presentacion = productoEncontrado.PresentacionNombre;
                        this.Producto_seleccionado.Existencias = productoEncontrado.Existencia;
                        document.getElementById("cantidad_sol").focus();                             
                        return
                    }
                    this.Estado_VentanaEmergente_Busqueda = true;
                }
    
            },
            Validacion_Campos(Tipo, Nombre, valor, obligatorio, cant_maxima) {
                /**
                 * @General
                 * Tipo : N = Númerio, Nombre = Descripción del campo, Valor = A evaluar, Obligatorio= Verdadero o falso,cant_maxima  = Aplica unicamente para un string y define la cantidad de caracteres 
                 * Devuelve un mensaje al usuario si las validaciones no se cumple y aborta, de lo contrario devuelve verdadero
                 */
                if (Tipo == 'ID') {
                    if (valor <= 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                            position: 'top-center'
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else if (Tipo == 'N') {
    
                    if (valor <= 0 || valor === "") {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                            position: 'top-center'
                        });
                        return false;
                    } else {
                        return true;
                    }
                } else {
                    if (valor == '' && obligatorio == true) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Campo \'' + Nombre + '\' es obligatorio.',
                            position: 'top-center'
                        });
                        return false;
                    } else {
    
                        if (cant_maxima < valor.length) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: 'Campo ' + Nombre + ' no debe superar los ' + cant_maxima + ' carácteres.',
                                position: 'top-center'
                            });
                            return false;
                        } else {
                            return true;
                        }
    
                    }
                }
            },
            /*****************************CONSULTAR INFORMACION*************/
            Consultar_OrdenEnc() {

                
                
                if(!this.Id_estado_seleccionado || !this.id_departamento_destino){
                    this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: 'Seleccione un destino y un estado para realizar la búsqueda',
                                position: 'top-center'
                            })
                    this.Lista_vista = [];
                    return
                }

                this.axios.post('/app/v1_OrdenCompra/ConsultasRequerimientos', {                        
                        Estado: this.Id_estado_seleccionado,
                        FechaInicio: this.GetDateValue(this.fecha_inicio),
                        FechaFin: this.GetDateValue(this.fecha_final),
                        Opcion: 'C',
                        SubOpcion: 'E',
                        DepartamentoDestino: this.id_departamento_destino
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: resp.data.mensaje,
                                position: 'top-center'
                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_vista = [];
                        } else {
                            this.$vs.notify({
                                                position:'top-center',
                                                color: 'success',
                                                title: 'Inventario',
                                                text: 'Se actualizaron los requerimientos exitosamente',
                                            })
                            this.Lista_vista =
                                resp.data.json.filter(req => req.CodigoAgrupacion == '10' ? true:false)
                               
                        }
                    })
                    .catch(() => {})
            },
            Consultar_OrdenDet() {
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/ConsultasRequerimientos', {
                        Opcion: 'C',
                        SubOpcion: 'D',
                        IdRequerimiento: this.IdCodigoRequerimiento
                    })
                    .then(resp => {
    
                        if (resp.data.codigo != 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Inventario',
                                text: resp.data.mensaje,
                                position: 'top-center'
                            })
                            //Limpia la tabla si no existen registros
                            this.Lista_detalle = [];
                        } else {
                            this.Lista_detalle = resp.data.json;
                            
    
                        }
                    })
                    .catch(() => {})
            },
            Consultar_Departamentos(ListaTipoBodegas) {
                const url = this.$store.state.global.url
                this.axios.post(url+'app/v1_OrdenCompra/ConsultasRequerimientos', {
                                                                        Opcion: 'D',
                                                                        SubOpcion: 'P',
                                                                        ListaTipoBodegas: ListaTipoBodegas
                                                                     })
                           .then(resp => {
            
                                if (resp.data.codigo != 0) {
                                    this.$vs.notify({
                                        color: '#B71C1C',
                                        title: 'Departamentos',
                                        text: resp.data.mensaje,
                                        position: 'top-center'
                                    })
                                } else {
                                    this.Lista_Departamentos_destino_consulta = resp.data.json;                         
                                }
                            })
                           .catch(() => { })
        
            },
            Consultar_Bodega(Operacion,Departamento) {
                const url = this.$store.state.global.url
                this.axios.post(url+'app/v1_OrdenCompra/consulta_bodega', {
                                                                        operacion: Operacion,
                                                                        id_departamento: Departamento
                                                                     })
                           .then(resp => {
                                //resp.data.json[0].descripcion
                                if (resp.data.codigo != 0) {
                                    this.$vs.notify({
                                        color: '#B71C1C',
                                        title: 'Bodegas',
                                        text: resp.data.mensaje,
                                        position: 'top-center'
                                    })
                                } else {
                                    this.Lista_bodegas_fuente = resp.data.json;                                                
                                }
                            })
                           .catch(() => { })
        
            },
            ConsultarSubBodega(Operacion,TipoBodegas) {
                if(!TipoBodegas || TipoBodegas.length == 0 ){return}

                const url = this.$store.state.global.url
                this.axios.post(url+'app/v1_OrdenCompra/consulta_bodega', {
                                                                        operacion: Operacion,
                                                                        lista_tipo_bodegas: TipoBodegas
                                                                        })
                            .then(resp => {
            
                                if (resp.data.codigo != 0) {
                                    this.$vs.notify({
                                        position:'top-center',
                                        color: '#B71C1C',
                                        title: 'Bodegas',
                                        text: resp.data.mensaje,
                                    })
                                } else {
                                    this.Lista_bodegas_fuente =
                                        this.Lista_bodegas_fuente.concat(resp.data.json.filter(bodega=>bodega.BodegaFuente == 'N'))                  
                                
                                    this.Lista_bodegas_fuente.sort((a,b)=>Number(a.CODIGO)>=Number(b.CODIGO)?1:-1)
                                }
                            })
                            .catch(() => { })
        
            },  
            defecto(){},
            cargar_producto() {
    
                this.Limpiar_Campos();
                
                if(!this.cod_bodegaFuente.CODIGO || !this.cod_departamentoDestino.CODIGO){
                    this.$vs.notify({
                                color: 'danger',
                                title: 'Producto',
                                text: 'Debe seleccionar una bodega fuente para cargar productos',
                                position: 'top-center'                                                                
                            })
                    return
                }

                this.axios.post('/app/v1_OrdenCompra/ConsultaProductos', {
                        operacion:'A',
                        accion:'A6',
                        Producto: this.producto_buscar.trim(),
                        bodega_fuente: this.cod_bodegaFuente?.CODIGO ?? '',
                        departamento_destino: this.cod_departamentoDestino.CODIGO,                        
                    })
                    .then(resp => {
    
                        if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                            this.producto = []

                            if(resp.data.json.length==1 && resp.data.json[0].tipo_error == "-1"){
                                this.$vs.notify({
                                    time: 5000,
                                    position:'top-center',
                                    color: 'danger',
                                    title: 'Producto',
                                    text: resp.data.json[0].descripcion,
                                })
                                return
                            }

                            resp.data.json.map(data => {
                                this.producto.push({
                                    ...data
                                })
                            })
    
                            this.Mostrar_resultado_producto(this.producto,this.producto_buscar.trim());
    
                        } else {
    
                            this.producto = []
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Producto',
                                text: (resp.data.codigo < 0) ? resp.data.descripcion : 'Código no existe, es un servicio ó no esta activo',
                                position: 'top-center'
                            })
    
                        }
    
                    })
                    .catch(() => {
                        this.$vs.loading.close();
    
                    })
    
            },
            /*************************** ACTUALIZAR, ELIMINAR NUEVO REGISTRO */
            Registrar_NuevaOrdenEnc() {
                var res_variables = false;
                res_variables = this.Validacion_Campos('ID', 'Departamento Destino', this.id_departamento_destino, true, 0);
    
                if (res_variables)
                    res_variables = this.Validacion_Campos('ID', 'Bodega Fuente', this.id_bodega_fuente, true, 0);
    
                if (res_variables) {
                    
                    this.axios.post('/app/v1_OrdenCompra/ManejoRequerimientos', {
                            Descripcion: '',
                            Departamento: this.cod_departamentoDestino.CODIGO,
                            BodOrigen: this.cod_bodegaFuente.CODIGO,
                            TipoUpdate: 10
                        })
                        .then(resp => {
                            
                            if (resp.data.json[0].codigo == 0) {
                                this.InsertoEncabezado = true;
                                this.IdCodigoRequerimiento = resp.data.json[0].descripcion;
                                this.Deshabilitar_campos = true;
                                this.Confirmacion_Transaccion();    
                            }else{
                                this.$vs.notify({
                                    color: 'danger',
                                    title: 'Requerimiento Inventario',
                                    text: resp.data.json[0].descripcion,
                                    time: 6000,
                                    position: 'top-center'
                                })
                            }
                            
                        })
                }else{
                    this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Verifique el producto y la cantidad a solicitar',                            
                            position: 'top-center'
                        })
                    document.getElementById("busquedaProducto").focus();
                }
            },
            Registrar_NuevaOrdenDet() {               
                
                let codigoProducto = this.Producto_seleccionado.Codigo
                let idProducto = this.Lista_detalle.findIndex( (producto) => producto.IDPRODUCTOFK.trim()===codigoProducto.trim())
                                                                           
                if(idProducto >= 0){
                    this.$vs.dialog({
                        type: 'confirm',
                        color: 'danger',
                        title: 'Confirmación',                        
                        acceptText: 'Aceptar',
                        cancelText: 'Cancelar',
                        text: `¿Desea actualizar la cantidad solicitada del producto  ${this.Producto_seleccionado.Codigo} de ${this.Lista_detalle[idProducto].CANTIDAD_PEDIDA} a  ${this.Cantida_solicitar}?`,
                        accept: () => {       
                            this.IngresarDetalle()     
                        }
                    })
                }else{
                    this.IngresarDetalle()   
                }

                
            },
            IngresarDetalle() {

                
                
                


                var res_variables = false;
                res_variables = this.Validacion_Campos('ID', 'No. Requerimiento', this.IdCodigoRequerimiento, true, 0);
    
                if (res_variables)
                    res_variables = this.Validacion_Campos('ID', 'Producto', this.Producto_seleccionado.Codigo, true, 0);
    
                if (res_variables)
                    res_variables = this.Validacion_Campos('N', 'Cantidad solicitar', this.Cantida_solicitar, true, 0);
    
                if (res_variables) {

                    this.axios.post('/app/v1_OrdenCompra/ManejoRequerimientos', {
                            NoRequerimiento: this.IdCodigoRequerimiento,
                            CodProducto: this.Producto_seleccionado.Codigo,
                            CantPedida: this.Cantida_solicitar,
                            TipoUpdate: 20
                        })
                        .then(resp => {
                            
                            if (resp.data.json[0].codigo == 0) {
                                this.InsertoEncabezado = false;
                                this.$vs.notify({
                                    color:'success',
                                    title:'Requerimiento',
                                    text:'Se agrego el detalle exitosamente',                                    
                                    position: 'top-center'
                                })
                                this.Consultar_OrdenDet();
                                this.producto_buscar = '';
                                this.Cantida_solicitar = '';
                                this.Limpiar_Campos();
                                document.getElementById("busquedaProducto").focus();
                                this.ValidarMontoLimite(0);
                            }else{                                
                                this.$vs.notify({
                                    color:'danger',
                                    title:'Requerimiento',
                                    text: resp.data.json[0].descripcion,
                                    position: 'top-center'
                                })
                            }
                        })
                }else{
                    this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Verifique el producto y la cantidad a solicitar',
                            position: 'top-center'
                        })
                    document.getElementById("busquedaProducto").focus();
                }
            },
            Registrar_RecibidoDet(datos, operacion) {
                
                
               // 
    
                /* VALIDACION DE ARRAY */
    
                this.axios.post('/app/v1_OrdenCompra/Requisicion_Det', {
                        Requisicion_det: datos.IDREQUISICIONDET,
                        Requisicion_enc: datos.IDCODIGOREQUERIMIENTO,
                        Producto: datos.IDPRODUCTOFK,
                        cant_pedida: 0,
                        cant_entregada: 0,
                        cant_recibida: datos.CANTIDAD_RECIBIDA,
                        Operacion: operacion,
                    })
                    .then(resp => {
    
                        if (resp.data.codigo == 0) {
                            this.Consultar_OrdenDet();
                        }
                    })
    
            },
            Confirmacion_Transaccion() {       
                if(parseFloat(this.Producto_seleccionado.Existencias) < parseFloat(this.Cantida_solicitar)){
                    this.$vs.notify({
                                    color: 'danger',
                                    title: 'Error',
                                    text: 'No se cuenta con sufientes existencias, ajuste la cant. a solicitar',
                                    time: 3000,
                                    position: 'top-center'
                                })   
                    return
                }     

                if (this.IdCodigoRequerimiento > 0) {
                    this.Registrar_NuevaOrdenDet();
                }else if(!this.InsertoEncabezado) {
                    this.Registrar_NuevaOrdenEnc();
                }else{
                    this.$vs.notify({
                                    color: 'danger',
                                    title: 'Error',
                                    text: 'Cierre esta ventana y busque el primer requerimiento en estado Edición para continuar.',
                                    time: 3000,
                                    position: 'top-center'
                                })   
                }
    
            },
            Anular_Movimiento(Datos){
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación Anulación',
                    acceptText: 'Anular',
                    cancelText: 'Cancelar',
                    text: `¿Desea anular el requerimiento No. ${Datos.IDCODIGOREQUERIMIENTO}?`,
                    accept: () => {
                        return this.axios.post('/app/v1_OrdenCompra/ManejoRequerimientos', {
                            NoRequerimiento: Datos.IDCODIGOREQUERIMIENTO,
                            TipoUpdate: 14
                        }).then(resp =>{
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Alerta',
                                    text: resp.data.mensaje,
                                    position: 'top-center'
                                })
                            } else {
                                this.$vs.notify({
                                    color: 'sucess',
                                    title: 'Anulación',
                                    text: resp.data.mensaje,
                                    position: 'top-center'
                                })    
                                this.Consultar_OrdenEnc();
                            }
                        })
                    }
                })
            },
            Eliminar_Registro(value) {
                /**
                 * @General
                 * Función eliminar registro;
                 */
                
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',                    
                    acceptText: 'Aceptar',
                    cancelText: 'Cancelar',
                    text: '¿Dar de baja Pedido  \'' + value.DESCRIPCION_PRODUCTO + '\'   \'' + value.DESCRIPCION_UNIDAD + '\' ? ',
                    accept: () => {
                        this.axios.post('/app/v1_OrdenCompra/ManejoRequerimientos', {
                                CodProducto: value.IDPRODUCTOFK,
                                NoRequerimiento: this.IdCodigoRequerimiento,
                                TipoUpdate: 21
                            })
                            .then(resp => {
    
                                if (resp.data.tipo_error >= 0) {
                                    this.Consultar_OrdenDet();
                                    document.getElementById("busquedaProducto").focus();
                                }
                            })
                        
                    }
                })
    
            },
            PeticionRecepcion(respuestaHuella){
                
                if(respuestaHuella.statusValidacion == 'NA'){
                    this.$vs.notify({
                        title:'Alerta',
                        color: 'danger',
                        position:'top-center',
                        time:6000,
                        text:'No se registro una huella valida'
                    })
                    return;
                }

                if(respuestaHuella.statusValidacion == 'N'){
                    this.$vs.notify({
                        title:'Alerta',
                        color: 'danger',
                        position:'top-center',
                        time:6000,
                        text:'No cuenta con permiso para recibir pedidos de este departamento'
                    })
                    return;
                }


                this.axios.post('/app/v1_OrdenCompra/AutoDespachoRequerimiento', {
                            NoRequerimiento: this.IdCodigoRequerimiento,
                            ListaProductos: this.Lista_detalle.map((producto)=>producto.IDPRODUCTOFK).toString(),
                            CantidadesDespacho: this.Lista_detalle.map((producto)=>producto.CANTIDAD_PEDIDA).toString(),
                            TipoUpdate: 11,
                            Descripcion: this.Observaciones,
                            CorporativoRecepcion: respuestaHuella.Corporativo
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.Estado_Emergente = false;
                                var mensajeMontoLimite = resp.data.descripcion.split(';')[1]
                                if(mensajeMontoLimite.slice(0,1) == "1"){
                                    this.$vs.notify({
                                        position:'top-center',
                                        color: 'warning',
                                        time: 6000,
                                        title: 'Monto Limite Bodega',                                    
                                        text: mensajeMontoLimite.slice(1)
                                    })
                                }else{
                                    this.$vs.dialog({
                                                type: 'confirm',
                                                color: 'success',
                                                title: 'Monto Limite Bodega',
                                                acceptText: 'Aceptar',
                                                cancelText: 'Cancelar',
                                                text: resp.data.descripcion.split(';')[1],                                               
                                                })
                                }
                                this.Consultar_OrdenEnc();    
                            }                         
                        }).catch(() => { })                                                  
            },
            Finalizar_Requerimiento() {

                var res_variables = false;
                res_variables = this.Validacion_Campos('ID', 'No. Requerimiento', this.IdCodigoRequerimiento, true, 0);
    
                if (res_variables) {
                    if (this.Lista_detalle.length <= 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: 'Debe tener al menos un producto requerido',
                            position: 'top-center'
                        })
                        return;
                    }
    
                }
    
                if (res_variables) {
                    this.$refs.componenteValidarPermiso.popUpPermisosHuella = true        
                }
            }
        }
    
    }
    </script>
    