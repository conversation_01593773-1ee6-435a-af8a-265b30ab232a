<template>
    <div>
        <vx-card title="Consulta de Despachos Pendientes de Aceptar">
            <vs-popup classContent="popup-example" title="Consulta de Despachos Pendientes de Aceptar" :active.sync="isPopup" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
                <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
                    <vs-row class="row_margin" vs-align="flex-start" vs-type="flex" vs-justify="center" vs-w="12">
                        <vs-col vs-w="12" vs-type="flex" vs-justify="center" vs-align="center" >
                            <vs-button type="filled" icon-pack="fas" icon="fa-file-csv" color="primary" @click="GenerarTexto()">Generar Texto</vs-button>
                            &nbsp;
                            &nbsp;
                            <vs-button type="filled" icon-pack="fas" icon="fa-file-excel" color="primary" @click="GenerarExcel()">Generar Excel</vs-button>
                        </vs-col>
                    </vs-row>
                </div>
            </vs-popup>
            <vs-button type="filled" icon-pack="fas" icon="fa-check-circle" color="success" @click="ActivarVentana()">Generar</vs-button>
        </vx-card>
    </div>
</template>
<script>
export default {
    data(){
        return{
            isPopup: false,
            listado_reportes_texto: [],
            listado_reportes_excel: [] 
        }
    },
    methods: {
        GenerarExcel(){
            this.$reporte_modal({
                Nombre: "Pendiente Aceptar Excel",
                Opciones: {},
                Formato: "EXCEL"
            })
        },
        GenerarTexto(){
            this.$reporte_modal({
                Nombre: "Pendiente Aceptar Texto",
                Opciones: {},
                Formato: "text/csv"
            })
        },
        ActivarVentana(){
            this.isPopup = true
        }
    },
    mounted() {
        this.ActivarVentana()
    },
    async beforeCreate() {
        this.listado_reportes_texto = await this.$recupera_parametros_reporte('Pendiente Aceptar Texto')
        this.listado_reportes_excel = await this.$recupera_parametros_reporte('Pendiente Aceptar Excel')
        
    }
}
</script>