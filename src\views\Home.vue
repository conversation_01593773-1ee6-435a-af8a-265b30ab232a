<template>
<div>
    <!-- <Snow /> -->
    <div class="flex  pl-5 pr-5" style="justify-content: space-between;">
        <!-- <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
            <vx-card slot="no-body" class="" style="overflow:hidden;background:linear-gradient(135deg, #13f1fc 0%,#0470dc 100%);height:130px;border:2px solid white;box-shadow:none">
                <h1 class="mb-6 text-white">Bienvenido(a) {{sesion_usuario}}</h1>
            </vx-card>

        </div> -->
        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1 blur">
            <Clock />
        </div>
        <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1 blur">
            <StickyNotes />
        </div>
    </div>
    <!-- <div class="wallpaper" :style="{'background-image':'url('+require('@/assets/images/others/wallpaper.png')+')'}">
        1
    </div> -->

    <!-- <div class="wallpaper">
        <img :src="'data:image/png;base64,' + imagen_src" alt="Saludo" height="100%" style="position:absolute;top:40px;right:0;opacity:0.2">
    </div> -->
    <div class="wallpaper" :style="{'background-image':'url('+ 'data:image/png;base64,' + imagen_src +')'}">
        
    </div>
    <!-- <img class="wallpaper" src="@/assets/images/others/pattern.png" alt=""> -->
    <!-- <img class="wallpaper" src="@/assets/images/others/wallpaper.jpg" alt=""> -->

    <Noticias />

    <!-- <a href="https://www.freepik.es/vectores/fondo">Vector de Fondo creado por rawpixel.com - www.freepik.es</a> -->
</div>
</template>

<script>
// import VueApexCharts from 'vue-apexcharts'
// import VxTimeline from "@/components/timeline/VxTimeline"
// import ChangeTimeDurationDropdown from '@/components/ChangeTimeDurationDropdown.vue'
export default {
    data() {
        return {
            imagen_src: null,
            nombre: '',
            texto: '',
            list: [],
        }
    },
    computed: {
        sesion_usuario() {
            return (this.$store.state.sesion.usuario) ? this.$store.state.sesion.usuario.split(",")[1].trim().split(" ")[0] : null
        },
        funcionalidades() {
            return this.$store.state.funcionalidades
        },
        global() {
            return this.$store.state.global
        }
    },
    methods: {
        radiologia() {
            this.$socket.emit('radiologia', "")
        },
        ConsultaHorarios() {
            this.axios.post('/app/externo/ArchivosVariables_DB', {
                    idControl: 'WEB_FONDO_SIGHOS',
                    tipo: 'CONSULTA',

                })
                .then(resp => {
                    this.archivos = resp.data.json
                    resp.data.json.map(data => {
                        if (data.src != '') {
                            this.imagen_src = data.src;

                        } else {
                            this.imagen_src = '';
                        }
                    });

                })
        },
    },
    components: {
        Noticias: () => import('@/components/sermesa/home/<USER>'),
        // RestaurarPassword: () => import('@/components/sermesa/home/<USER>'),
        // Slack: () => import('@/components/sermesa/home/<USER>'),
        // Snow: () => import('@/components/sermesa/home/<USER>'),
        Clock: () => import('@/components/sermesa/home/<USER>'),
        StickyNotes: () => import('@/components/sermesa/home/<USER>'),
        // DocumentacionApis: () => import('@/components/sermesa/home/<USER>'),
        // MCI: () => import('@/components/sermesa/home/<USER>'),

    },
    mounted() {
        // this.$store.dispatch('updateTheme', 'dark')
        this.ConsultaHorarios();
    }

}
</script>

<style>
.wallpaper {
    position: absolute;
    top: 67PX;
    height: -webkit-fill-available;
    z-index: -1!important;
    background-position-x: center;
    background: no-repeat;
    /*object-fit: cover;*/
    width: -webkit-fill-available;
    background-size: cover;
}


.message {
    /* float: left; */
    /* box-shadow: 0 0 5px rgba(0, 0, 0, 0.5); */
    padding: 10px;
    border-radius: 10px;
    position: relative;
    display: block;
    max-width: 500px;
}

.message .title {
    position: relative;
    top: 0;
    left: 0;
}

.blur {
    backdrop-filter: blur(10px);
    border-radius: 10px;
}
</style>
