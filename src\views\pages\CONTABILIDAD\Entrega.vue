<template>
<div>
    <form @submit="handleSubmit">
        <DxForm :ref="formEntrega" :form-data.sync="formulario" label-mode="floating">
            <DxFormGroupItem>
                <DxFormItem data-field="Cuenta" editor-type="dxSelectBox" :editor-options="{ width: 'auto', searchEnabled: true, items: cuentas,  displayExpr: 'Valor', valueExpr: 'Codigo' }" :validationRules="[{ type: 'required' }]" />
                <DxFormGroupItem :col-count="2">
                    <DxFormGroupItem>
                        <DxFormItem data-field="Cheque" editor-type="dxNumberBox" :editor-options="{ min: 0, format: '#0', step: 1 }" />
                        <DxFormItem data-field="ChequeInfo" :disabled="false" editor-type="dxTextBox" :editor-options="{ readOnly: true, tabIndex: -1 }">
                            <DxFormLabel text=" " />
                        </DxFormItem>
                    </DxFormGroupItem>
                    <DxFormGroupItem>
                        <DxFormItem data-field="Recibo" editor-type="dxNumberBox" :editor-options="{ min: 0, format: '#0', step: 1 }" />
                        <DxFormItem template="textoEstado" alignment="center" />
                    </DxFormGroupItem>
                </DxFormGroupItem>
                <DxFormButtonItem :button-options="buttonEntregar" :visible="infoCheque !== null && infoCheque.Status !== 'A'" name="Eliminar" horizontal-alignment="center" verical-alignment="center" />
            </DxFormGroupItem>

            <template #textoEstado="{}">
                <div class="mt-4" style="display: flex; justify-content: center !important">
                    <span style="color: blue; font-size: 14px; font-weight: bold;" v-if="infoCheque !== null && fechaEntregado != '' && infoCheque.Impresion !== null">Este cheque ya fue entregado el {{ $formato_fecha(fechaEntregado, 'dd/MM/yyyy hh:mm:ss') }}</span>
                    <span style="color: blue; font-size: 14px; font-weight: bold;" v-else-if="infoCheque !== null && infoCheque.Impresion == null">Este cheque no ha sido impreso</span>
                </div>
            </template>
        </DxForm>
    </form>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'
import 'devextreme-vue/lookup'

// import DxButton from 'devextreme-vue/button'

const formEntrega = 'formEntrega'

export default {
    name: 'Entrega',
    components: {},
    data() {
        return {
            DefaultDxGridConfiguration,
            formEntrega,

            formulario: {
                Cuenta: null, // Codigo de la cuenta de banco de 2 dígitos
                Cheque: null, // Cheque para entregar
                NombreCuentaBanco: null, // Nombre de la cuenta contable de la cuenta bancaria
                ChequeInfo: null,
            },
            cuentas: [],

            buttonEntregar: {
                width: 'auto',
                icon: 'fas fa-circle-dollar-to-slot',
                text: 'Entregar',
                type: 'success',
                onClick: () => {
                    this.Entregar()
                },
                useSubmitBehavior: false,
            },

            infoCheque: null,
            fechaEntregado: '',
        }
    },
    props: {
        ModalidadPago: null,
        TipoCheque: null,
        Corporativo: null
    },
    methods: {
        handleSubmit(e) {
            e.preventDefault()
        },

        CargarCuentas() {
            this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 1
                })
                .then(resp => {
                    this.cuentas = resp.data.json

                    this.formulario.Cuenta = this.cuentas[0].Codigo
                })
        },

        async BuscarCheque(cheque) {
            await this.axios.post('/app/v1_bancos/Eliminacion', {
                    Opcion: 1,
                    Cuenta: parseInt(this.formulario.Cuenta),
                    TipoCheque: this.TipoCheque,
                    ChequeInicial: cheque
                })
                .then(resp => {
                    this.LimpiarVariables()

                    if (resp.data.json.length > 0) {
                        this.infoCheque = resp.data.json[0]

                        if (this.infoCheque.Status == 'A') {
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#ed8c72',
                                title: 'Anulado',
                                acceptText: 'Aceptar',
                                text: 'El cheque se encuentra anulado',
                                buttonCancel: 'border',
                                accept: () => {
                                    this.formulario.Cheque = null
                                },
                            })
                            this.infoCheque = null
                            return
                        } else {
                            this.fechaEntregado = this.infoCheque.Entregado !== null ? this.infoCheque.Entregado : ''
                            this.formulario.ChequeInfo = this.infoCheque.NombreBeneficiario + ' - Q. ' + this.NumeroComas(this.infoCheque.Monto)
                        }
                    } else {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'No existe',
                            acceptText: 'Aceptar',
                            text: 'El cheque ingresado no existe',
                            buttonCancel: 'border',
                            accept: () => {
                                this.formulario.Cheque = null
                            },
                        })
                        this.infoCheque = null
                        return
                    }
                })
        },

        DetectarCambios(viejoValor, nuevoValor) {
            if (viejoValor !== nuevoValor) {
                if (this.formulario.Cuenta !== null && this.formulario.Cheque !== null && this.formulario.Cheque > 0 && this.formulario.Cheque !== '') {
                    this.BuscarCheque(this.formulario.Cheque);
                }
            }
        },

        NumeroComas(x) {
            x = parseFloat(x).toFixed(2)
            return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        },

        Entregar() {
            if (this.formulario.Recibo == null || this.formulario.Recibo == 0) {
                this.$vs.dialog({
                    type: 'confirm',
                    color: '#ed8c72',
                    title: 'No ingresó recibo',
                    acceptText: 'Aceptar',
                    text: '¿Desea registrar la entrega sin número de recibo?',
                    buttonCancel: 'border',
                    accept: () => {
                        this.ConfirmarEntrega()
                    },
                    cancelText: 'Cancelar',
                    cancel: () => {}
                })
                return
            } else {
                this.ConfirmarEntrega()
            }
        },

        async ConfirmarEntrega() {
            await this.axios.post('/app/v1_bancos/Entrega', {
                    Opcion: 1,
                    Cuenta: this.formulario.Cuenta,
                    TipoCheque: this.TipoCheque,
                    Cheque: parseInt(this.formulario.Cheque),
                    Recibo: this.formulario.Recibo == null ? 0 : parseInt(this.formulario.Recibo)
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.LimpiarVariables()
                        this.formulario.Cheque = null
                    }
                })
        },

        LimpiarVariables() {
            this.formulario.ChequeInfo = null
            this.infoCheque = null
            this.fechaEntregado = ''
        },

    },
    created() {},
    mounted() {},
    beforeMount() {
        this.CargarCuentas()
    },
    watch: {

        'formulario.Cuenta'(nuevoValor, viejoValor) {
            this.DetectarCambios(viejoValor, nuevoValor);
        },
        'formulario.Cheque'(nuevoValor, viejoValor) {
            this.DetectarCambios(viejoValor, nuevoValor);
        },
    },
    computed: {
        formEntregaInstance: function () {
            return this.$refs[formEntrega].instance;
        },
    }
}
</script>

<style>
        </style>
