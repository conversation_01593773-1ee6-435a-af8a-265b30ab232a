<script>
import { popupConf } from './data'

export default {
    data() {
        return {
            popupConf
        }
    },
    props: {
        SerieAdmision: null,
        CodigoAdmision: null,
        NumeroExpediente: null,
        StatusAdmision: null,
        FechaEgreso: null,
        ValidarIngreso:null,
    },
    computed: {
        /**Evalua si el expediente  y o admisión esta cargada en el componete padre y fue asignado a este como propiedad */
        ExpedienteCargado() {
            return Boolean(this.SerieAdmision) && Boolean(this.CodigoAdmision) && Boolean(this.NumeroExpediente)
        },   
    },
    mounted(){
        this.Cargar()
    },
    methods: {
        Cargar(){
            
        },
        ValidarRegistroDatos(elementName='Expediente Evolución', succesCallback = ()=>{}, failCallback = ()=>{}, arg = null){
            if (this.StatusAdmision != 'A'){
                this.$vs.notify({
                    time: 4000,
                    title: elementName??'Expediente Rvolución',
                    text: 'La admisión no está activa, no se puede agregar ' + String.prototype.toLowerCase.call(elementName??''),
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'warning',
                    position: 'bottom-center'
                })
                if(typeof failCallback == 'function')
                    failCallback(arg)
                return
            }
            if (this.FechaEgreso && this.ValidarIngreso ==='S'){
                this.$vs.notify({
                    time: 4000,
                    title: elementName??'Expediente Rvolución',
                    text: 'La admision tiene registrado egreso médico, no podrá ingresar nuevo registro de ' + String.prototype.toLowerCase.call(elementName??'') + ' sin previa autorización y reactivación por parte de coordinación.',
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'warning',
                    position: 'bottom-center'
                })
                if(typeof failCallback == 'function')
                    failCallback(arg)
                return
            }
            succesCallback(arg)
        }
    }
}
</script>