
<!-- ADM025.vue -->
<template>
    <ValidationObserver ref="admisionNeoNatal" v-slot="{ invalid, handleSubmit }" mode="lazy">
        <form @submit.prevent="handleSubmit(submitForm(submit))">
    <div class="admision-interna">

        <DatosPaciente ref="DatosPacienteComponent" v-show="false">
        </DatosPaciente>

        <SerieCodigoAdmision class="admision-interna" ref="serieCodigoAdmisionComponent"
            @selected-option-changed="onSelectedOptionChanged" 
            v-model="AdmisionData.TipoAdmision" :show-options="false"
            :location-data="'upper'">
        </SerieCodigoAdmision>

        <DiagnosticoCovid class="admision-interna" ref="diagnosticoCovidComponent"
            @checked-options-changed="onCheckedOptionChanged" @vacunas-seleccionadas="obtenerListaVacunas"
            :id-paciente="parseInt(PacienteData.Codigo)">
        </DiagnosticoCovid>

        
        <ConsultaHabitaciones class="admision-interna"
            ref="consultaHabitacionesComponent"
            v-on:codigo-habitacion="obtenerCodigoHabitacion">
        </ConsultaHabitaciones>
            


        <TiposDescuento
            class="admision-interna"
            ref="tipoDescuentoComponent"
            @selected-tipodes-changed="onSelectedTipoChanged"
            v-model="AdmisionData.TipoDescuento"
            :show-options="false"
            :tipo-descuento="'Recien Nacido'"
            :tipo-admision="parseInt(AdmisionData.TipoAdmision)">
        </TiposDescuento>

        <ConsultaPaqueteNeoNatal class="admision-interna"
            ref="paqueteNeoNatalComponent"
            @paquete-rn-sel="obtenerPaqueteNeoNatal"
            :serie-admision-raiz="AdmisionData.SerieAdmisionRaiz"
            :admision-raiz="parseInt(AdmisionData.AdmisionRaiz)">
        </ConsultaPaqueteNeoNatal>

        <!-- <ConsultaDiagnosticos class="admision-interna" ref="consultaDiagnosticosComponent"
            @lista-diagnosticos="onObtenerListaDiagnosticos">
        </ConsultaDiagnosticos> -->

        <div class="admision-interna datosnotificarA">

                    <div class="w-full md:w-full lg:w-3/5 xl:w-3/5 m-1">
                        <validationProvider name="Primer Nombre Paciente" class="required" rules="required|min:3"
                            v-slot="{ errors }">
                            <vs-input class="w-full" label="Notificar a" v-model="AdmisionData.NotificarA"
                                :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null">
                            </vs-input>
                        </validationProvider>
                    </div>
                    <div class="w-full md:w-full lg:w-3/5 xl:w-3/5 m-1">
                        <validationProvider name="Primer Nombre Paciente" class="required" rules="required|min:3"
                            v-slot="{ errors }">
                            <vs-input class="w-full" label="Direccion" v-model="AdmisionData.DireccionNotificarA"
                                :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null">
                            </vs-input>
                        </validationProvider>
                    </div>

                    <div class="w-full md:w-full lg:w-3/5 xl:w-3/5 m-1">
                        <validationProvider name="Primer Nombre Paciente" class="required" rules="required|min:3"
                            v-slot="{ errors }">
                            <vs-input class="w-full" label="Telefono" v-model="AdmisionData.TelefonoNotificarA"
                                :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null">
                            </vs-input>
                        </validationProvider>
                    </div>
        </div>

        <ConsultarMedicos class="admision-interna"
                    ref="ConsultarMedicosComponent" 
                    v-on:codigo-medico="obtenerCodigoMedico"
                    v-on:no-mail-checked="validaPoseeCorreo"
                    v-on:mail-changed="obtieneCorreoMedico"  >
        </ConsultarMedicos>

        
        <div v-show="showButtons">
                    <vs-button style="margin-right:1px" v-on:click="handleSubmit(submit())" :disabled="invalid"
                    :clientWidth="20"> Crear Admisión Interna
                    </vs-button>
                </div>
    </div>
        </form>
    </ValidationObserver>
</template>
  
<script>

import { mapFields } from "vuex-map-fields";


import DatosPaciente from "/src/components/sermesa/modules/paciente/DatosPaciente.vue"
import DiagnosticoCovid from "/src/components/sermesa/modules/admisiones/DiagnosticoCovid.vue";
import ConsultaHabitaciones from "/src/components/sermesa/modules/admisiones/ConsultaHabitaciones.vue"
import TiposDescuento from "/src/components/sermesa/modules/admisiones/TiposDescuento.vue";
import ConsultaPaqueteNeoNatal from "/src/components/sermesa/modules/admisiones/ConsultaPaqueteNeoNatal.vue"
import SerieCodigoAdmision from "/src/components/sermesa/modules/admisiones/SerieCodigoAdmision.vue";
import ConsultarMedicos from "/src/components/sermesa/modules/admisiones/ConsultarMedicos.vue"
//import ConsultaDiagnosticos from "/src/components/sermesa/modules/admisiones/ConsultaDiagnosticos.vue"


export default {
    name: "AdmisionRecienNacidos",
    components: {
        DatosPaciente,
        DiagnosticoCovid,
        ConsultaHabitaciones,
        TiposDescuento,
        SerieCodigoAdmision,
        ConsultarMedicos,
        //ConsultaDiagnosticos,
        ConsultaPaqueteNeoNatal
    },
    data() {
        return {
            IdPaciente: 0,
            PaqueteQx: '',
            initialSerie: '',
            selectedTipo: '',
            NombreMedico: '',
            showButtons: false,
            TipoAdmisionBusqueda: 13,
        }
    },
    computed: {
        ...mapFields('admision', ["AdmisionData"]),
        ...mapFields('paciente', ["PacienteData"])

    },
    beforeCreated() {
    },
    created() {
        this.AdmisionData.TipoAdmision = 4
        this.IdPaciente = this.PacienteData.Codigo
    },
    updated(){
       
    } ,
    beforeMount() {
    },
    mounted() {
        
        this.AdmisionData.TipoAdmision = 4
    },
    methods: {
        getionarDatos() {
            return new Promise((resolve) => {
                // Lógica de guardado del cliente aquí
                // Resuelve la promesa si el guardado es exitoso
                return resolve(true);
                // Rechaza la promesa si hay un error en el guardado
                // reject('Mensaje de error aquí');
            });
        },
        validacionFormulario() {
            return new Promise((resolve, reject) => {
                this.$refs.admisionNeoNatal.validate()
                    .then((isValid) => {
                        if (isValid) {
                            resolve(true);
                        } else {

                            reject('Revisar campos requeridos');
                        }
                    })
                    .catch((error) => {
                        reject(error);
                    });
            });
        },
        gestionarAdmision() {
            return new Promise((resolve, reject) => {
                this.$store.dispatch('admision/crearAdmision')
                    .then((response) => {                        
                        this.$emit("mensaje-respuesta", response);
                        resolve(response);
                    })
                    .catch((error) => {                                                
                        reject(error.descripcion[0].DescError);
                    });
            });
        },
        async submit() {
            
            this.AdmisionData.Paciente = this.PacienteData.Codigo
            this.AdmisionData.NombreFactura = this.PacienteData.NombreFactura
            this.AdmisionData.DireccionFactura = this.PacienteData.DireccionFactura
            this.AdmisionData.IdCliente = this.PacienteData.IdCliente
            this.AdmisionData.DPI       = this.PacienteData.DPI 
            this.AdmisionData.Nit = this.PacienteData.Nit       
            this.AdmisionData.FechaNacimiento = this.PacienteData.Nacimiento
    

            this.getionarDatos()
                .then(() => {
                    return this.validacionFormulario();
                })
                .then(() => {
                    return this.gestionarAdmision();
                })
                .then((resp) => {
                    this.$vs.dialog({
                        type: 'confirm',
                        color: '#ED8C72',
                        acceptText: 'Continuar',
                        title: 'Se creo la Admisión No.',
                        text:  this.AdmisionData.Serie + " " + this.AdmisionData.Codigo + ' ' + resp.data.json[0]?.CoberturaRN||'',
                        clientWidth: 100,
                        accept: () => {
                            this.$emit("ready", true);
                        }
                    })
                })
                .catch((error) => {
                    this.$vs.dialog({
                        type: 'confirm',
                        color: '#ED8C72',
                        acceptText: 'Continuar',
                        title: 'Error en gestión de Paciente',
                        text: error,
                        clientWidth: 100,
                        accept: () => {

                            this.$emit("ready", false);

                        }
                    });
                })
            
            // this.$store.dispatch('admision/crearAdmision')
            //     .then(() => {
            
            //         this.$vs.dialog({
            //             type: 'confirm',
            //             color: '#ED8C72',
            //             acceptText: 'Continuar',
            //             title: 'Se creo la Admisión No.',
            //             text: this.AdmisionData.Serie + ' ' + this.AdmisionData.Codigo,
            //             clientWidth: 100,
            //             accept: () => {
            //                 this.$emit("ready", true);
            //             }
            //         })

            //     })
            //     .catch(() => {                    
            //         this.$emit("ready", false);
            //     })

            // console.log(this.$store.state)
        },
        onCheckedOptionChanged(diagnostico) {

            this.DisgnosticoCovid = diagnostico
            this.AdmisionData.DiagnosticoCOVID = (diagnostico.length === 0) ? 0 : 1
        },
        obtenerListaVacunas(vacunas) {
            this.AdmisionData.PrimeraVacunaCOVID = vacunas[0]?.Codigo || 0
            this.AdmisionData.SegundaVacunaCOVID = vacunas[1]?.Codigo || 0
            this.AdmisionData.TerceraVacunaCOVID = vacunas[2]?.Codigo || 0
        },
        onSelectedOptionChanged(admision) {                                    
            this.AdmisionData.TipoAdmision = admision.TipoAdmision
            this.AdmisionData.Serie = admision.Serie
            this.AdmisionData.NivelPrecios = admision.NivelDePrecios
        },
        onSelectedTipoChanged() {    
        },
        obtenerCodigoMedico(datosMedico) {
            this.AdmisionData.Medico = datosMedico.CodigoMedico
            this.AdmisionData.CorreoElectronicoMedico = datosMedico.CorreoMedico
        },
        validaPoseeCorreo(poseeCorreo){
            this.AdmisionData.Medico = poseeCorreo.CodigoMedico
            this.AdmisionData.CorreoElectronicoMedico = poseeCorreo.CorreoMedico
            this.AdmisionData.MedicoNoPoseeCorreo = poseeCorreo.NoCorreo
        },
        obtieneCorreoMedico(correoMedico){
            this.AdmisionData.Medico = correoMedico.CodigoMedico
            this.AdmisionData.CorreoElectronicoMedico = correoMedico.CorreoMedico
            this.AdmisionData.MedicoNoPoseeCorreo = correoMedico.NoCorreo
        },
        obtenerCodigoHabitacion(codigo) {
            this.AdmisionData.Habitacion = codigo
        },
        onObtenerListaDiagnosticos(diagnosticos) {
            var codigos = []
            diagnosticos.forEach(function (item) {
                codigos.push(item.CodigoDiagnostico.trim());
            });

            var codigosSeparados = codigos.join(" | ");
            this.AdmisionData.Otros = codigosSeparados
        },
        obtenerPaqueteNeoNatal(paquete){
            this.AdmisionData.CodigoPaqueteBebe =  paquete.Codigo
            this.AdmisionData.PrecioPaquete     =  parseFloat(paquete.Precio).toFixed(2)
            this.AdmisionData.PlanMedax         =  paquete.Medax
            this.AdmisionData.CodigoBaseHospital =  paquete.CodigoBase
        }



    },
};
</script>


<style scoped>
* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

.admision-interna {
    margin: 10px;
}

.medico-habitacion {
    display: grid;
    width: 100%;
    height: 100%;
    grid-template-areas: "codigo-medico codigo-habitacion";
    grid-template-columns: 50% 50%;
    grid-template-rows: 50% 50%;
}

.medico-habitacion>div {
    grid-gap: 1px;
    border-radius: 5px;
    margin: 2px;
}

.codigo-medico {
    grid-area: codigo-medico;
    border-radius: 5px;
}

.codigo-habitacion {
    grid-area: codigo-habitacion;
    border-radius: 5px;
}

.datosnotificarA {
    display: flex;
    grid-gap: 30px;
    align-items: center;
    padding-top: 0.5rem;
    padding-bottom: 1rem;
    padding-left: 1rem;
    padding-right: 1rem;
    border: 1px solid #888;
    border-radius: 5px;
    margin: 2px;
}

</style>

