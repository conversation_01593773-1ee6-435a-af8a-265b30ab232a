<template>
    <div>
        <vx-card title="Ingreso a Bodegas">
            <vs-divider></vs-divider>
            <vs-row vs-type="flex" vs-w="12">
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <label class="typo__label">Del:&nbsp;</label>
                    <vs-input type="date" v-model="FechaInicio" />
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <label class="typo__label">Al:&nbsp;</label>
                    <vs-input type="date" v-model="FechaFin" />
                </vs-col>
                <vs-col vs-type="flex" vs-align="center" vs-w="2">
                    <vs-button id="button-with-loading" color="primary" icon-pack="fa" @click="ConsultaPedidosConsignacion()" icon="fa-search">Buscar</vs-button>
                </vs-col>
            </vs-row>
            <div class="card">
                <vs-table2 max-items="10" pagination :data="ListaPedidos" search>
                    <template slot="thead">
                        <th>Pedido</th>
                        <th>Fecha</th>
                        <th>Proveedor</th>
                        <th>Nombre Proveedor</th>
                        <th>Fecha Entrega</th>
                        <th>Destino</th>
                        <th>Estado</th>
                        <th>Solicitante</th>
                        <th>Descripcion</th>
                        <th with="30px">Ver Pedido</th>
                        <th with="30px">Ingreso</th>
                        <th with="30px">Desactivar Pedido</th>
                    </template>
    
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2>
                                {{tr.Codigo}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.Fecha}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.Proveedor}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.Nombre}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.FechaEntrega}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.BodegaDestino}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.Estado}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.Usuario}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.Descripcion}}
                            </vs-td2>
                            <vs-td2 align="center">
                                <vs-button color="warning" icon-pack="fas" icon="fa-file-invoice" @click="ConsultaReportePedido(tr)" class="mr-1" style="display:inline-block">
                                </vs-button>
                            </vs-td2>
                            <vs-td2 align="center">
                                <vs-button v-if="tr.Tipo != 'O'" color="success" icon-pack="fas" icon="fa-arrow-right" @click="IsIngreso=true;ConsultarPedidoConsignacionDet(data[indextr])" class="mr-1" style="display:inline-block">
                                </vs-button>
                                <vs-button v-else color="#B2BABB" icon-pack="fas" icon="fa-arrow-right" class="mr-1" style="display:inline-block">
                                </vs-button>
                            </vs-td2>
                            <vs-td2 align="center" noTooltip>
                                <vs-button v-if="PermisoDesactivar && tr.Tipo != 'O'" color="danger" icon-pack="fas" icon="fa-rectangle-xmark" @click="DesactivarPedido(tr)" class="mr-1" style="display:inline-block">
                                </vs-button>
                                <vs-button v-else color="#B2BABB" icon-pack="fas" icon="fa-rectangle-xmark" class="mr-1" style="display:inline-block">
                                </vs-button>
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
            <div class="card">
                <!----------------------- RECEPCIONAR DETALLE ---------->
                <vs-popup classContent="popup-example" title="Ingreso Bodega" :active.sync="IsIngreso">
                    <div style="padding:15px; border-radius:5px; box-shadow:0px 15px 60px -10px;margin-bottom:20px">
                        <div>
                            <!-- <label class="tituloLabel">Pedido</label>
                            <vs-divider></vs-divider> -->
                            <div class="div-container">
                                <vs-row vs-type="flex" vs-w="12">
                                    <vs-col vs-type="flex" vs-align="center" vs-w="1">
                                        <label class="typo__label">Pedido:&nbsp;</label>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="2">
                                        <h6>{{ Info.Pedido }}</h6>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="1">
                                        <label class="typo__label">Estado:&nbsp;</label>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                        <h6>{{ Info.Estado }}</h6>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="1">
                                        <label class="typo__label">Autoriza:&nbsp;</label>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="3">
                                        <h6>{{ Info.Autoriza }}</h6>
                                    </vs-col>
                                </vs-row>
                                <br>
                                <vs-row vs-type="flex" vs-w="12">
                                    <vs-col vs-type="flex" vs-align="center" vs-w="1">
                                        <label class="typo__label">Proveedor:&nbsp;</label>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="2">
                                        <h6>{{ Info.Proveedor }}</h6>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="5">
                                        <h6>{{ Info.NombreProveedor }}</h6>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-justify="flex-end" vs-w="2">
                                        <label class="typo__label">Entregar a más tardar el día:&nbsp;</label>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="2">
                                        <h6>{{ Info.FechaEntrega }}</h6>
                                    </vs-col>
                                </vs-row>
                                <br>
                                <vs-row vs-type="flex" vs-w="12">
                                    <vs-col vs-type="flex" vs-align="center" vs-w="1">
                                        <label class="typo__label">Fecha:&nbsp;</label>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="2">
                                        <h6>{{ Info.Fecha }}</h6>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="1">
                                        <label class="typo__label">Lugar Entrega:&nbsp;</label>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                        <h6>{{ Info.LugarEntrega }}</h6>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="2">
                                        <label class="typo__label">Fecha Ingreso:&nbsp;</label>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="2">
                                        <h6>{{ Info.FechaIngreso }}</h6>
                                    </vs-col>
                                </vs-row>
                                <br>
                                <vs-row vs-type="flex" vs-w="12">
                                    <vs-col vs-type="flex" vs-align="center" vs-w="1">
                                        <label class="typo__label">Motivo:&nbsp;</label>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="11">
                                        <h6>{{ Info.Motivo }}</h6>
                                    </vs-col>
                                </vs-row>
                                <br>
                                <vs-row vs-type="flex" vs-w="12">
                                    <vs-col vs-type="flex" vs-align="center" vs-w="8">
                                        <label>Observación&nbsp;</label>
                                        <vs-input class="w-full" v-model="Observacion"></vs-input>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                        &nbsp;Contacto:&nbsp;&nbsp;<h6>{{ Info.Contacto }}</h6>
                                    </vs-col>
                                </vs-row>
                                <br>
                                <vs-row vs-type="flex" vs-w="12" vs-justify="flex-end">
                                    <vs-col vs-type="flex" vs-align="center" vs-w="1">
                                        <label class="typo__label">Tel:&nbsp;</label>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="3">
                                        <h6>{{ Info.TelContacto }}</h6>
                                    </vs-col>
                                </vs-row>
                                <vs-divider></vs-divider>
                                <vs-row vs-type="flex" vs-w="12">
                                    <vs-col vs-type="flex" vs-align="center" vs-w="1">
                                        <label class="typo__label">Envío:&nbsp;</label>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="3">
                                        <vs-input v-model="NoEnvio"></vs-input>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="1">
                                        <label class="typo__label">Bodega:&nbsp;</label>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="2">
                                        <vs-input v-model="Info.CodigoBodega" disabled></vs-input>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="1"></vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                        <h6>&nbsp;&nbsp;{{ Info.LugarEntrega }}</h6>
                                    </vs-col>
                                </vs-row>
                                <br>
                                <vs-row vs-type="flex" vs-w="12">
                                    <vs-col vs-type="flex" vs-align="center" vs-w="1">
                                        <label class="typo__label">Fecha Envío:&nbsp;</label>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="3">
                                        <vs-input type="date" v-model="FechaEnvio" @blur="onChangeFecha"/>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="1"></vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="3">
                                        <vs-input v-model="TotalEnvio" disabled/>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="1">
                                        <label class="typo__label">Fecha&nbsp;</label>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="3">
                                        <vs-input type="date" v-model="FechaIngresoBodega" disabled/>
                                    </vs-col>
                                </vs-row>
                                <br>
                                <div class="cont div-margin">
                                    <label>&nbsp;&nbsp;Impuesto</label>
                                    <vs-divider></vs-divider>
                                    <div class="div-container">
                                        <vs-row vs-type="flex" vs-w="12">
                                            <vs-col vs-type="flex" vs-align="center" vs-w="6">
                                                <vs-radio v-model="Impuesto" vs-name="radios1" vs-value="0">Afecto</vs-radio>
                                            </vs-col>
                                            <vs-col vs-type="flex" vs-align="center" vs-w="6">
                                                <vs-radio v-model="Impuesto" vs-name="radios1" vs-value="1">No Afecto</vs-radio>
                                            </vs-col>
                                        </vs-row>
                                    </div>
                                </div>
                                <br>
                                <vs-row vs-type="flex" vs-w="12">
                                    <vs-col vs-type="flex" vs-align="center" vs-w="1">
                                        <label class="typo__label">Exento:&nbsp;</label>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="3">
                                        <vs-input v-model="Exento" disabled/>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="1">
                                        <label class="typo__label">Afecto:&nbsp;</label>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="3">
                                        <vs-input v-model="Afecto" disabled/>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="1">
                                        <label class="typo__label">Iva:&nbsp;</label>
                                    </vs-col>
                                    <vs-col vs-type="flex" vs-align="center" vs-w="3">
                                        <vs-input v-model="Iva" disabled/>
                                    </vs-col>
                                </vs-row>
                                <vs-divider></vs-divider>
                                <vs-row>
                                    <vs-col vs-type="flex" vs-align="center" vs-justify="center" vs-w="12">
                                        <vs-table2 class="w-full" max-items="10" tooltip pagination :data="DetallePedidoConsignacion">
                                            <template slot="thead">
                                                <th order="Producto">Producto</th>
                                                <th order="Cantidad">Cantidad</th>
                                                <th order="Nombre">Nombre</th>
                                                <th order="UnidadMedida">Unidad Medida</th>
                                                <th order="CostoUnitario">Valor Unitario</th>
                                                <th order="CantidadRecibida">Despachado</th>
                                                <th order="SubTotal">Total</th>
                                                <th order="Convenio">Convenio</th>
                                                <th order="ValorUnitarioDetalle">Costo Convenio</th>
                                            </template>
                                            <template slot-scope="{data}">
                                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                                    <vs-td2 noTooltip>
                                                        {{ tr.Producto }}
                                                    </vs-td2>
                                                    <vs-td2 noTooltip>
                                                        {{ tr.Cantidad }}
                                                    </vs-td2>
                                                    <vs-td2>
                                                        {{ tr.Nombre }}
                                                    </vs-td2>
                                                    <vs-td2 noTooltip>
                                                        {{ tr.UnidadMedida }}
                                                    </vs-td2>
                                                    <vs-td2 noTooltip>
                                                        {{ $formato_moneda(tr.CostoUnitario) }}
                                                    </vs-td2>
                                                    <vs-td2 noTooltip>
                                                        <vs-input type="number" v-model.number="tr.CantidadRecibida" class="w-full" @change="onChangeCantidadDespachado(tr)"/>
                                                    </vs-td2>
                                                    <vs-td2 noTooltip>
                                                        {{ $formato_moneda(tr.CantidadRecibida * tr.CostoUnitario) }}
                                                    </vs-td2>
                                                    <vs-td2 noTooltip>
                                                        {{ tr.Convenio }}
                                                    </vs-td2>
                                                    <vs-td2 noTooltip>
                                                        {{ $formato_moneda(tr.ValorUnitarioDetalle) }}
                                                    </vs-td2>
                                                </tr>
                                            </template>
                                        </vs-table2>

                                    </vs-col>
                                </vs-row>
                                <vs-row vs-type="flex" vs-w="12">
                                    <vs-col vs-type="flex" vs-justify="flex-end" vs-align="center" vs-w="10">
                                        <h2 class="labelTotal">Total:&nbsp;&nbsp;</h2><h3 class="labelTotal">{{ $formato_moneda(GranTotal) }}</h3>
                                    </vs-col>
                                </vs-row>
                            </div>
                        </div>
                    </div>
                    <vs-row vs-type="flex" vs-w="12">
                        <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="12">
                            <vs-button color="success" icon-pack="fa" icon="fa-save" @click="GrabarEnvio()">Imprimir y Guardar</vs-button>
                        </vs-col>
                    </vs-row>
                </vs-popup>
            </div>
        </vx-card>
    </div>
</template>
<script>
    import moment from 'moment'
    export default {
        data() {
            return{
                FechaInicio: '',
                FechaFin: '',
                PermisoDesactivar: false,
                ListaPedidos: [],
                IsIngreso: false,
                Info: {
                    Pedido: '',
                    Estado: '',
                    Autoriza: '',
                    Proveedor: '',
                    NombreProveedor: '',
                    FechaEntrega: '',
                    Fecha: '',
                    LugarEntrega: '',
                    CodigoBodega: '',
                    CodigoAgrupacion: '',
                    FechaIngreso: '',
                    Motivo: '',
                    Contacto: '',
                    TelContacto: ''
                },
                Observacion: '',
                NoEnvio: '',
                FechaEnvio: '',
                FechaIngresoBodega: '',
                Impuesto: '0',
                DetallePedidoConsignacion: [{
                    Producto: '',
                    Cantidad: '',
                    Nombre: '',
                    UnidadMedida: '',
                    CostoUnitario: '',
                    Convenio: '',
                    ValorUnitarioDetalle: '',
                    CantidadRecibida: '',
                    SubTotal: ''
                }],
                GranTotal: ''
            }
        },
        mounted() {
            this.PermisoDesactivar = this.$validar_privilegio('DESACTIVAR').status
            this.ObtenerFechas()
        },
        methods: {
            ObtenerFechas(){
                this.FechaInicio =  moment(new Date(Date.now())).format('YYYY-MM-DD')
                this.FechaFin = moment(new Date(Date.now())).format('YYYY-MM-DD')
                this.FechaEnvio = moment(new Date(Date.now())).format('YYYY-MM-DD')
                this.FechaIngresoBodega = moment(new Date(Date.now())).format('YYYY-MM-DD')

                this.ConsultaPedidosConsignacion()
            },
            ConsultaPedidosConsignacion(){
                this.axios.post('/app/v1_OrdenCompra/ConsultaPedidosConsignacion', {
                    FechaInicio: this.FechaInicio,
                    FechaFin: this.FechaFin
                })
                .then(resp => {
                    if(resp.data.codigo == 0){
                        this.ListaPedidos = resp.data.json
                    }else{
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: resp.data.descripcion,
                        })
                        //Limpia la tabla si no existen registros
                        this.ListaPedidos = [];

                    }
                })
            },
            ConsultarPedidoConsignacionDet(data){
                this.LimpiarCampos()
                this.Info.Pedido = data.Codigo
                this.Info.Estado = data.Estado
                this.Info.Autoriza = data.Autorizado
                this.Info.Proveedor = data.Proveedor
                this.Info.NombreProveedor = data.Nombre
                this.Info.FechaEntrega = data.FechaEntrega
                this.Info.Fecha = data.Fecha
                this.Info.CodigoBodega =  data.BodegaDestino
                this.Info.CodigoAgrupacion = data.CodigoAgrupacion
                this.Info.LugarEntrega = data.LugarEntrega
                this.Info.FechaIngreso = data.FechaIngreso
                this.Info.Motivo = data.Descripcion
                this.Info.Contacto = data.ContactoProveedor
                this.Info.TelContacto = data.TelContacto

                this.axios.post('/app/v1_OrdenCompra/ConsultaPedidosConsignacionDetalle', {
                    NoPedido: data.Codigo
                })
                .then(resp => {
                    if(resp.data.codigo == 0){
                        this.DetallePedidoConsignacion = resp.data.json.map(m => {
                            return{
                                Producto: m.Producto,
                                Cantidad: m.Cantidad,
                                Nombre: m.Nombre,
                                UnidadMedida: m.UnidadMedida,
                                CostoUnitario: m.CostoUnitario,
                                Convenio: m.Convenio,
                                ValorUnitarioDetalle: m.ValorUnitarioDetalle,
                                CantidadRecibida: '',
                                SubTotal: 0
                            }
                        })
                    }else{
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Inventario',
                            text: resp.data.descripcion,
                        })
                        this.DetallePedidoConsignacion = []
                    }
                })
            },
            ConsultaReportePedido(data){
                this.$reporte_modal({
                    Nombre: "Pedido Consignacion",
                    Opciones: {
                        Pedido: data.Codigo
                    },
                    Formato: "PDF"
                })
            },
            GrabarEnvio(){
                if(this.Info.Estado == 'D'){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'El pedido ya fue despachado..!!!'
                    })
                    return
                }
                if(this.Info.Estado == 'A'){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'El pedido fue anulado..!!!'
                    })
                    return
                }
                if(this.NoEnvio == '' || this.NoEnvio == null || this.NoEnvio == 0){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'No hay un envío para respaldar este ingreso'
                    })
                    return
                }
                if(this.Info.CodigoBodega == '' || this.Info.CodigoBodega == null || this.Info.CodigoBodega == 0){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'A ingresado una bodega inválida!!!'
                    })
                    return
                }
                if(this.GranTotal == '' || this.GranTotal == null || this.GranTotal == 0){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'Debe de despachar algún producto...'
                    })
                    return
                }

                this.axios.post('/app/v1_OrdenCompra/IngresoConsignacion', {
                    Proveedor: this.Info.Proveedor,
                    NoDocumento: this.NoEnvio,
                    FechaIngreso: this.FechaIngresoBodega,
                    CodigoBodega: this.Info.CodigoBodega,
                    NoPedido: this.Info.Pedido,
                    FechaEnvio: this.FechaEnvio,
                    Motivo: this.Info.Motivo,
                    Observacion: this.Observacion,
                    Impuesto: this.Impuesto,
                    CodigoAgrupacion: this.Info.CodigoAgrupacion,
                    GranTotal: this.GranTotal,
                    Datos: JSON.stringify(this.DetallePedidoConsignacion)
                })
                .then(resp => {
                    if(resp.data.codigo == 0){
                        let resultado = resp.data.resultado.split(',')
                        this.$reporte_modal({
                            Nombre: "Envio Consignacion",
                            Opciones: {
                                Envio: resultado[1]
                            },
                            Formato: "PDF"
                        })

                        this.LimpiarCampos()
                        this.ConsultaPedidosConsignacion()
                        this.IsIngreso = false
                    }
                })

            },
            DesactivarPedido(data){
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmacion',
                    text: `¿Desea desactivar el pedido: ${ data.Codigo }?`,
                    acceptText: 'Aceptar',
                    cancelText: 'Cancelar',
                    accept: () => {
                        this.axios.post('/app/v1_OrdenCompra/DesactivaPedidosConsignacion', {
                            NoPedido: data.Codigo
                        })
                        .then(resp => {
                            this.$vs.notify({
                                color: 'success',
                                title: 'Consignación',
                                text: resp.data.descripcion
                            })
                            this.ConsultaPedidosConsignacion()
                        })
                    }
                })
            },
            onChangeFecha(){
                if(this.FechaEnvio > moment(this.Info.FechaEntrega, "DD/MM/YYYY").format('YYYY-MM-DD')){
                    this.FechaEnvio = moment(new Date(Date.now())).format('YYYY-MM-DD')
                }
            },
            onChangeCantidadDespachado(dato){
                if(dato.Cantidad < parseInt(dato.CantidadRecibida)){
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Inventario',
                        text: 'No puede ingresar más de lo solicitado',
                    })
                    dato.CantidadRecibida = ''
                }else{
                    dato.CantidadRecibida = parseInt(dato.CantidadRecibida)
                    dato.SubTotal = parseFloat(dato.CostoUnitario) * parseFloat(dato.CantidadRecibida)
                }
            },
            ChangeTotalNevio(){
                let total = 0;
                this.DetallePedidoConsignacion.forEach(element => {
                    total += (Number(element.SubTotal))
                });
                this.GranTotal = parseFloat(total)
                return this.$formato_moneda(parseFloat(total))
            },
            LimpiarCampos(){
                this.DetallePedidoConsignacion = []
                this.Info = []
                this.Observacion = ''
                this.NoEnvio = ''
                this.FechaEnvio = moment(new Date(Date.now())).format('YYYY-MM-DD')
                this.FechaIngresoBodega = moment(new Date(Date.now())).format('YYYY-MM-DD')
                this.Impuesto = '0'
                this.GranTotal = ''
            }
        },
        computed: {
            TotalEnvio(){
                // let total = 0;
                // this.DetallePedidoConsignacion.forEach(element => {
                //     total += (Number(element.SubTotal))
                // });
                // this.GranTotal = parseFloat(total)
                // return this.$formato_moneda(parseFloat(total))
                return this.ChangeTotalNevio()
            },
            Afecto(){
                if(this.Impuesto == 0){
                    let total = 0;
                    this.DetallePedidoConsignacion.forEach(element => {
                        total += (Number(element.SubTotal))
                    });

                    return this.$formato_moneda(parseFloat(total) / 1.12)
                }else{
                    return 0
                }
            },
            Exento(){
                if(this.Impuesto == 1){
                    let total = 0;
                    this.DetallePedidoConsignacion.forEach(element => {
                        total += (Number(element.SubTotal))
                    });

                    return this.$formato_moneda(parseFloat(total))
                }else{
                    return 0
                }  
            },
            Iva(){
                if(this.Impuesto == 0){
                    let total = 0;
                    this.DetallePedidoConsignacion.forEach(element => {
                        total += (Number(element.SubTotal))
                    });

                    return this.$formato_moneda(parseFloat(total) - (parseFloat(total) / 1.12))
                }else{
                    return 0
                }
            }
        }
    }
</script>
<style lang="scss" scoped>
    .div-container{
        padding: 5px;
        margin: 10px;
    }
    .box-custom1{
        box-shadow: 0px 15px 40px -10px;
        padding: 15px;
        width: 25%;
    }
    .cont{
        border: 1px solid #888;
        width: 25%;
        
    }
    .tituloLabel{
        font-size: 16px;
    }
    .labelTotal{
        font-weight: bold;
    }
    .div-margin{
        margin-left: 75px;
    }
</style>