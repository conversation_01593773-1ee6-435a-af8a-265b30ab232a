<template>
    <vx-card :title="`Consulta de Facturas ${sesion.sesion_sucursal_nombre}`"> 
        <vs-row>
            <vs-row class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12 p-1" >
                <vs-row class="w-full">
                    <vs-row class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12" >
                        <vs-col class="w-full p-1">                                  
                            <vs-input label="Serie Factura" class="w-full" v-model="factura.serie" :disabled="consultaFactura"/>
                        </vs-col>
                        <vs-col class="w-full p-1">                                  
                            <vs-input label="Número Factura" type="number" class="w-full" v-model="factura.numero" :disabled="consultaFactura"/>
                        </vs-col>
                    </vs-row>
                    <vs-row class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12 " >
                        <vs-col class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12 p-1" style="height: 60px;">   
                            <label class="typo__label label-shrink" style="white-space: nowrap; color:#626262; font-size:12px;  padding-bottom:2px; padding-top:0px;"> Tipo Documento  </label>  
                            <DxSelectBox            
                                style="padding: 2px;"
                                :items="listas.tiposDocumentos"
                                display-expr="Descripcion"
                                placeholder=""
                                v-model="documento.tipoDocumento"
                                :disabled="true"
                            />    
                        </vs-col>              
                        <vs-col class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12 p-1">
                            <vs-input label="No. documento" 
                                    v-model="documento.noDocumento" 
                                    class="w-full"     
                                    :disabled="true"                              
                                    />      
                        </vs-col>  
                        <vs-col class="w-full p-1">
                            <vs-input label="Nombre"  
                                    class="w-full"
                                    v-model="documento.nombre" 
                                    :disabled="true"
                                    />      
                        </vs-col>  
                    </vs-row>                   
                </vs-row>
            </vs-row>
            <vs-row class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12 p-1" >                
                <vs-row class="w-full" vs-justify="center">
                    <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1">
                        <vs-button @click="ConsultaFactura(1)" :disabled="consultaFactura" color="primary" icon-pack="feather" icon="icon-search" class="w-full ajustar-texto-botton">
                            Buscar
                        </vs-button>
                    </vs-col>    
                    <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1">
                        <vs-button @click="exportDataTables()" :disabled="!facturaCargada" color="primary" class="w-full ajustar-texto-botton">
                            <i class="far fa-file-excel"></i>
                            Exportar
                        </vs-button>
                    </vs-col>    
                    <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-2/12 p-1">
                        <vs-button :disabled="!facturaCargada" @click="Consulta().GenerarFactura('ImprimeFelTmu')" color="primary" icon-pack="feather" icon="icon-file-text" 
                                   class="w-full ajustar-texto-botton">
                            TMU
                        </vs-button>
                    </vs-col>    
                    <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-2/12 p-1">
                        <vs-button :disabled="!facturaCargada" color="primary" @click="Consulta().GenerarFactura('ImprimeFelEFX')" icon-pack="feather" icon="icon-file-text" 
                                   class="w-full ajustar-texto-botton">
                            FX
                        </vs-button>
                    </vs-col>    
                    <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1">
                        <vs-button :disabled="!facturaCargada" @click="Consulta().GenerarFactura('ImprimeFelLtr')" color="primary" icon-pack="feather" icon="icon-file-text" 
                                   class="w-full ajustar-texto-botton">
                            Láser
                        </vs-button>
                    </vs-col>  
                    <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1">
                        <vs-button :disabled="!facturaCargada && !consultaFactura" @click="Consulta().LimpiarDatos()" color="primary" icon-pack="feather" icon="icon-trash" 
                                   class="w-full ajustar-texto-botton">
                            Limpiar
                        </vs-button>
                    </vs-col> 
                      
                </vs-row>
                <vs-row class="w-full" vs-justify="center" >
                    <vs-col class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12 p-1 flex"  style="align-items:center; justify-content:center;">
                        <label class="typo__label label-total pl-3" >
                            {{factura.estado}}
                        </label>
                    </vs-col>
                    <vs-col class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12 p-1" >
                        <vs-row class="w-full" vs-justify="center">                            
                            <vs-col class="w-full">
                                <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1">
                                    <label class="typo__label pl-3">
                                        <b>Total:</b>
                                    </label>
                                </vs-col>
                                <vs-col class="sm:w-full md:w-full lg:w-10/12 xl:w-10/12 p-1">
                                    <label class="typo__label label-total ">
                                        {{ $formato_moneda(factura.total) }}
                                    </label>
                                </vs-col>                    
                            </vs-col>
                        </vs-row>
                        <vs-row class="w-full" vs-justify="center">
                            <vs-col class="w-full">
                                <vs-col class="sm:w-full md:w-full lg:w-2/12 xl:w-2/12 p-1">
                                    <label Saldo="typo__label  pl-3">
                                        <b>Saldo:</b>
                                    </label>
                                </vs-col>
                                <vs-col class="sm:w-full md:w-full lg:w-10/12 xl:w-10/12 p-1">
                                    <label class="typo__label label-total">
                                        {{ $formato_moneda(factura.saldo) }}
                                    </label>
                                </vs-col>                    
                            </vs-col>                            
                        </vs-row>
                    </vs-col>
                </vs-row>                                                
            </vs-row>
            <vs-row class="sm:w-full md:w-full lg:w-full xl:w-full p-1">
                <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-3/12 p-1">
                    <vs-row class="p-1">
                        <label class="labelTitulo" style="width:100px; text-align:right">Número FEL:&nbsp;</label><label class="labelInfoImportante">{{factura.numeroFel}}</label>
                    </vs-row>
                </vs-col>
                <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-3/12 p-1">
                    <vs-row class="p-1">
                        <label class="labelTitulo" style="width:80px; text-align:right">Admisión:&nbsp;</label><label class="labelInfoImportante ">{{admision}}</label>
                    </vs-row>
                </vs-col>
                <vs-col class="sm:w-full md:w-full lg:w-6/12 xl:w-6/12 p-1">
                    <vs-row class="p-1">
                        <label class="labelTitulo" style="width:125px; text-align:right">Tipo:&nbsp;</label><label class="labelInfoImportante ">{{factura.tipo}}</label>
                    </vs-row>
                </vs-col>
            </vs-row>
            <vs-row class="sm:w-full md:w-full lg:w-full xl:w-full p-1">
                <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-3/12 p-1">
                    <vs-row class="p-1">
                        <label class="labelTitulo" style="width:100px; text-align:right">Fecha registro:&nbsp;</label><label class="labelInfo">{{factura.fechaRegistro}}</label>
                    </vs-row>
                    <vs-row class="p-1">
                        <label class="labelTitulo" style="width:100px; text-align:right">Fecha emisión:&nbsp;</label><label class="labelInfo">{{factura.fechaEmision}}</label>
                    </vs-row>
                    <vs-row class="p-1">
                        <label class="labelTitulo" style="width:100px; text-align:right">Cajero:&nbsp;</label><label class="labelInfo">{{factura.cajero}}</label>
                    </vs-row>
                </vs-col>
                <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-3/12 p-1">
                    <vs-row class="p-1">
                        <label class="labelTitulo" style="width:80px; text-align:right">Subtotal:&nbsp;</label><label class="labelInfo">{{$formato_moneda(factura.subtotal)}}</label>
                    </vs-row>
                    <vs-row class="p-1">
                        <label class="labelTitulo" style="width:80px; text-align:right">Descuento:&nbsp;</label><label class="labelInfo">{{$formato_moneda(factura.descuento)}}</label>
                    </vs-row>
                    <vs-row class="p-1">
                        <label class="labelTitulo" style="width:80px; text-align:right">Total:&nbsp;</label><label class="labelInfo">{{$formato_moneda(factura.total)}}</label>
                    </vs-row>
                </vs-col>
                <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-3/12 p-1">
                    <vs-row class="p-1">
                        <label class="labelTitulo" style="width:125px; text-align:right">Fecha anulación:&nbsp;</label><label class="labelInfo">{{factura.fechaAnulacion}}</label>
                    </vs-row>
                    <vs-row class="p-1">
                        <label class="labelTitulo" style="width:125px; text-align:right">Usuario que anula:&nbsp;</label><label class="labelInfo">{{factura.usuarioAnulacion}}</label>
                    </vs-row>
                    <vs-row class="p-1">
                        <label class="labelTitulo" style="width:125px; text-align:right">Motivo:&nbsp;</label><label class="labelInfo">{{factura.motivoAnulacion}}</label>
                    </vs-row>
                </vs-col>
                <vs-col class="sm:w-full md:w-full lg:w-3/12 xl:w-3/12 p-1">
                    <vs-row class="p-1">
                        <label class="labelTitulo" style="width:125px; text-align:right">Código empleado:&nbsp;</label><label class="labelInfo">{{factura.codigoEmpleado}}</label>
                    </vs-row>
                    <vs-row class="p-1">
                        <label class="labelTitulo" style="width:125px; text-align:right">Refacturada:&nbsp;</label><vs-switch style="text-align:center; align-self: center;" v-model="factura.refacturada" :disabled="true"/>                  
                    </vs-row>
                    <vs-row class="p-1">
                        <label class="labelTitulo" style="width:125px; text-align:right">Factura-Lote:&nbsp;</label><vs-switch style="text-align:center; align-self: center;" v-model="factura.facturaLote" :disabled="true"/>                  
                    </vs-row>
                </vs-col>
            </vs-row>
            <vs-row vs-justify="center" vs-align="center" class='w-full'>
                <DxTabPanel width="100%" :height="'100%'" :data-source="tabs" :show-nav-buttons="true" :defer-rendering="false">
                    <template #title="{ data: tab }">
                        <span>{{ tab.name }}</span>
                    </template>
                    <template #item="{ data: tab }">
                        <div v-if="tab.value === 1" class="tabpanel-item">
                            <vx-card>
                                <vs-row vs-justify="center" vs-align="center" class="w-full">   
                                    <DxDataGrid v-bind="DefaultDxGridConfiguration"                                               
                                            id="detalleFacturaGrid"
                                            name="detalleFacturaGrid"                                                                                                
                                            :ref="detalleFacturaRef"
                                            :data-source="listas.detalleFactura" width="100%" height="'100%'" >
                                            <DxDataGridColumn :width="100" data-field="Categoria" caption="Categoria" alignment="center" />
                                            <DxDataGridColumn :width="150" data-field="Nombre" caption="Nombre" alignment="center" />
                                            <DxDataGridColumn :width="100" data-field="Subtotal" caption="SubTotal" format="Q #,##0.00" data-type="number"  alignment="right" />
                                            <DxDataGridColumn :width="150" data-field="Cantidad" caption="Cantidad" format="#,##0.00" data-type="number"  alignment="center" />
                                            <DxDataGridColumn :width="150" data-field="Descuento" caption="Descuento" format="Q #,##0.00" data-type="number" alignment="right" />
                                            <DxDataGridColumn :width="150" data-field="PorDescuento" caption="Porc Descuento" format="##0.00 %" :calculate-display-value="(rowData)=>(rowData.PorDescuento)?(rowData.PorDescuento/100):0/100"data-type="number"  alignment="center" />
                                            <DxDataGridColumn :width="200" data-field="Cuenta" caption="Cuenta" alignment="center" />
                                            <DxDataGridColumn :width="200" data-field="CuentaDescuento" caption="Cuenta Descuento" alignment="center" />
                                            <DxDataGridColumn :width="200" data-field="Total" caption="Total" format="Q #,##0.00" data-type="number" alignment="right" />
                                            <DxDataGridSummary>
                                                <DxDataGridTotalItem  column="Subtotal" summary-type="sum" value-format="Q #,##0.00" data-type="number" display-format="{0}"/>
                                                <DxDataGridTotalItem  column="Total" summary-type="sum" value-format="Q #,##0.00" data-type="number" display-format="{0}"/>
                                            </DxDataGridSummary> 
                                            <DxDataGridPaging :page-size="15"/>
                                            <DxDataGridPager  :show-page-size-selector="true"
                                                        :allowed-page-sizes="[15, 30, 45]"
                                            />
                                    </DxDataGrid>         
                                </vs-row>                                    
                            </vx-card>
                        </div>
                        <div v-else-if="tab.value === 2" class="tabpanel-item w-full" >
                            <vs-col vs-justify="center" vs-align="center" class="w-full">
                                <vx-card>
                                    <vs-row vs-justify="center" vs-align="center" class="w-full">
                                        <DxDataGrid v-bind="DefaultDxGridConfiguration"                                               
                                                id="detalleCargosGrid"
                                                name="detalleCargosGrid"                                                                                                
                                                :ref="detalleCargosRef"
                                                :data-source="listas.detalleCargos" width="100%" height="'100%'" >
                                                <DxDataGridColumn :width="100" data-field="EmpresaReal" caption="Empresa" alignment="center" />
                                                <DxDataGridColumn :width="250" caption="Admisión" alignment="center">
                                                    <DxDataGridColumn :width="100" data-field="SerieAdmision" caption="Serie" alignment="center" />
                                                    <DxDataGridColumn :width="150" data-field="Admision" caption="Número" alignment="center" />
                                                </DxDataGridColumn>
                                                <DxDataGridColumn :width="100" data-field="Fecha" caption="Fecha"   alignment="center" />
                                                <DxDataGridColumn :width="100" data-field="Producto" caption="Producto" alignment="center" />
                                                <DxDataGridColumn :width="200" data-field="Nombre" caption="Nombre"  alignment="center" />
                                                <DxDataGridColumn :width="70" data-field="UnidadMedida" caption="Unidad Medida" alignment="center" />
                                                <DxDataGridColumn :width="100" data-field="Categoria" caption="Categoria" alignment="center" />
                                                <DxDataGridColumn :width="200" data-field="PrecioUnitario" caption="Precio Unitario" format="Q #,##0.00" data-type="number" alignment="right" />
                                                <DxDataGridColumn :width="100" data-field="Cantidad" caption="Cantidad" format="#,##0.00" data-type="number" alignment="center" />
                                                <DxDataGridColumn :width="200" data-field="SubTotal" caption="SubTotal" format="Q #,##0.00" data-type="number" alignment="right" />
                                                <DxDataGridSummary>
                                                    <DxDataGridTotalItem  column="Subtotal" summary-type="sum" value-format="Q #,##0.00" data-type="number" display-format="{0}"/>
                                                </DxDataGridSummary> 
                                                <DxDataGridPaging :page-size="15"/>
                                                <DxDataGridPager  :show-page-size-selector="true"
                                                            :allowed-page-sizes="[15, 30, 45]"
                                                />
                                        </DxDataGrid>         
                                    </vs-row>
                                </vx-card>
                            </vs-col>
                        </div>
                        <div v-else-if="tab.value === 3" class="tabpanel-item" >
                            <vs-col vs-justify="center" vs-align="center" vs-w="12">
                                <vx-card>
                                    <vs-row vs-justify="center" vs-align="center" class="w-full">
                                        <DxDataGrid v-bind="DefaultDxGridConfiguration"                                             
                                                id="recibosGrid"
                                                name="recibosGrid"                                                                                                
                                                :ref="recibosRef"
                                                :data-source="listas.recibos" width="100%" height="'100%'" >
                                                <DxDataGridColumn :width="200" caption="Recibo" alignment="center">
                                                    <DxDataGridColumn :width="70" data-field="SerieRecibo" caption="Serie" alignment="center" />
                                                    <DxDataGridColumn :width="100" data-field="Recibo" caption="Número" alignment="center" />
                                                </DxDataGridColumn>
                                                <DxDataGridColumn :width="150" data-field="Registro" caption="Fecha"   alignment="center" />
                                                <DxDataGridColumn :width="150" data-field="Tipo" caption="Tipo"  alignment="center" />
                                                <DxDataGridColumn :width="70" data-field="Periodo" caption="Periodo" alignment="center" />
                                                <DxDataGridColumn :width="100" data-field="Paciente" caption="Paciente" alignment="center" />
                                                <DxDataGridColumn :width="250" data-field="Nombre" caption="Nombre" alignment="center" />
                                                <DxDataGridColumn :width="70" data-field="Caja" caption="Caja" alignment="center" />
                                                <DxDataGridColumn :width="100" data-field="Usuario" caption="Usuario" alignment="center" />
                                                <DxDataGridColumn :width="250" data-field="Monto" caption="Monto" format="Q #,##0.00" data-type="number"  alignment="right" />
                                                <DxDataGridSummary>
                                                    <DxDataGridTotalItem  column="Monto" summary-type="sum" value-format="Q #,##0.00" data-type="number" display-format="{0}"/>
                                                </DxDataGridSummary>
                                                <DxDataGridPaging :page-size="15"/>
                                                <DxDataGridPager  :show-page-size-selector="true"
                                                            :allowed-page-sizes="[15, 30, 45]"
                                                />
                                        </DxDataGrid>   
                                    </vs-row>
                                </vx-card>
                            </vs-col>
                        </div>
                        <div v-else-if="tab.value === 4" class="tabpanel-item" >
                            <vs-col vs-justify="center" vs-align="center" vs-w="12">
                                <vx-card>
                                    <vs-row vs-justify="center" vs-align="center" class="w-full">
                                        <DxDataGrid v-bind="DefaultDxGridConfiguration"                                                 
                                                id="notasCreditoGrid"
                                                name="notasCreditoGrid"                                                                                                
                                                :ref="notasCreditoRef"
                                                :data-source="listas.notasCredito" width="100%" height="'100%'" >
                                                <DxDataGridColumn :width="250" caption="Nota de Crédito" alignment="center">
                                                    <DxDataGridColumn :width="70" data-field="SerieNota" caption="Serie" alignment="center" />
                                                    <DxDataGridColumn :width="100" data-field="Numero" caption="Número" alignment="center" />
                                                </DxDataGridColumn>
                                                <DxDataGridColumn :width="100" data-field="Fecha" caption="Fecha"   alignment="center" />
                                                <DxDataGridColumn :width="100" data-field="Abono" caption="Abono" format="Q #,##0.00" data-type="number"  alignment="right" />
                                                <DxDataGridColumn :width="200" data-field="Nombre" caption="Nombre"  alignment="center" />
                                                <DxDataGridColumn :width="70" data-field="Periodo" caption="Periodo" alignment="center" />
                                                <DxDataGridColumn :width="100" data-field="Estado" caption="Estado" alignment="center" />
                                                <DxDataGridColumn :width="450" data-field="Concepto" caption="Concepto" alignment="center" />
                                                <DxDataGridColumn :width="200" data-field="NumeroFEL" caption="Número Fel" alignment="center" />
                                                <DxDataGridPaging :page-size="15"/>
                                                <DxDataGridPager  :show-page-size-selector="true"
                                                            :allowed-page-sizes="[15, 30, 45]"
                                                />
                                        </DxDataGrid>     
                                    </vs-row>
                                </vx-card>
                            </vs-col>
                        </div>
                        <div v-else-if="tab.value === 5" class="tabpanel-item" >
                            <vs-col vs-justify="center" vs-align="center" vs-w="12">
                                <vx-card>
                                    <vs-row vs-justify="center" vs-align="center" class="w-full">
                                        <DxDataGrid v-bind="DefaultDxGridConfiguration"                                                
                                                id="costoDetalleGrid"
                                                name="costoDetalleGrid"                                                                                                
                                                :ref="costoDetalleRef"
                                                :data-source="listas.costoDetalle" width="100%" height="'100%'" >
                                                <DxDataGridColumn :width="70" data-field="Empresa" caption="Empresa" alignment="center" />
                                                <DxDataGridColumn :width="50" data-field="Tipo" caption="Tipo" alignment="center" />
                                                <DxDataGridColumn :width="50" data-field="Linea" caption="Linea" alignment="center" />
                                                <DxDataGridColumn :width="70" data-field="Categoria" caption="Categoria"   alignment="center" />
                                                <DxDataGridColumn :width="100" data-field="Producto" caption="Producto" alignment="center" />
                                                <DxDataGridColumn :width="200" data-field="Nombre" caption="Nombre"  alignment="center" />
                                                <DxDataGridColumn :width="150"  data-field="CostoUnitario" caption="Costo Unitario" format="Q #,##0.00" data-type="number" alignment="right" />
                                                <DxDataGridColumn :width="100" data-field="Cantidad" caption="Cantidad" format="#,##0.00" data-type="number" alignment="center" />
                                                <DxDataGridColumn :width="150" data-field="CostoTotal" caption="Costo Total" format="Q #,##0.00" data-type="number" alignment="right" />
                                                <DxDataGridColumn :width="100" data-field="Debe" caption="Debe"  alignment="center" />
                                                <DxDataGridColumn :width="100" data-field="Haber" caption="Haber" alignment="center" />
                                                <DxDataGridColumn :width="100" data-field="PeriodoPartida" caption="Periodo Partida"  alignment="center" />
                                                <DxDataGridColumn :width="70"  data-field="Partida" caption="Partida" alignment="center" />
                                                <DxDataGridColumn :width="250" caption="Factura" alignment="center">
                                                    <DxDataGridColumn :width="100" data-field="SerieFactura" caption="Serie" alignment="center" />
                                                    <DxDataGridColumn :width="200" data-field="Factura" caption="Número"  alignment="center" />
                                                </DxDataGridColumn>
                                                <DxDataGridColumn :width="100" data-field="Registro" caption="Registro" alignment="center" />
                                                                                              
                                                <DxDataGridPaging :page-size="15"/>
                                                <DxDataGridPager  :show-page-size-selector="true"
                                                            :allowed-page-sizes="[15, 30, 45]"
                                                />
                                        </DxDataGrid>         
                                    </vs-row>
                                </vx-card>
                            </vs-col>
                        </div>
                        <div v-else-if="tab.value === 6" class="tabpanel-item" >
                            <vs-col vs-justify="center" vs-align="center" vs-w="12">
                                <vx-card>
                                    <vs-row vs-justify="center" vs-align="center" class="w-full">
                                        <DxDataGrid v-bind="DefaultDxGridConfiguration"                                               
                                                id="ventaDirectaDetalleGrid"
                                                name="ventaDirectaDetalleGrid"                                                                                                
                                                :ref="ventaDirectaDetalleRef"
                                                :data-source="listas.ventaDirectaDetalle" width="100%" height="'100%'" >                                                
                                                <DxDataGridColumn :width="100" data-field="Producto" caption="Producto" alignment="center" />
                                                <DxDataGridColumn :width="200" data-field="Nombre" caption="Nombre"  alignment="center" />
                                                <DxDataGridColumn :width="200" data-field="PrecioUnitario" caption="Precio Unitario" format="Q #,##0.00" data-type="number" alignment="right" />
                                                <DxDataGridColumn :width="100" data-field="Cantidad" caption="Cantidad" format="#,##0.00" data-type="number" alignment="center" />
                                                <DxDataGridColumn :width="200" data-field="Subtotal" caption="SubTotal" format="Q #,##0.00" data-type="number" alignment="right" />
                                                <DxDataGridSummary>
                                                    <DxDataGridTotalItem  column="Subtotal" summary-type="sum" value-format="Q #,##0.00" data-type="number" display-format="{0}"/>
                                                </DxDataGridSummary> 
                                                <DxDataGridPaging :page-size="15"/>
                                                <DxDataGridPager  :show-page-size-selector="true"
                                                            :allowed-page-sizes="[15, 30, 45]"
                                                />
                                        </DxDataGrid>         
                                    </vs-row>
                                </vx-card>
                            </vs-col>
                        </div>
                    </template>
                </DxTabPanel>               
            </vs-row>
        </vs-row>
    </vx-card>
</template>
<script> 
import { ref } from 'vue'
import {DefaultDxGridConfiguration} from './data';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver-es';
import moment from "moment";
import { exportDataGrid } from 'devextreme/excel_exporter';   

const detalleFacturaRef = "grid1"
const detalleCargosRef  = "grid2"
const recibosRef        = "grid3"
const notasCreditoRef   = "grid4"
const costoDetalleRef   = "grid5"
const ventaDirectaDetalleRef = "grid6"
export default{
    name:'CONSULTA_FACTURAS',    
    components:{
    },
    props:{
        consultaFactura: {
                            type: Boolean,
                            default: false
                         },
    },
    data()
    {
        return{       
            facturaCargada:false,     
            detalleFacturaRef,
            detalleCargosRef,
            recibosRef,
            notasCreditoRef,
            costoDetalleRef,
            ventaDirectaDetalleRef,
            admision:null,
            factura:{
                tipo:null,
                serie:null,
                numero:null,
                numeroFel:null,
                estado:null,
                fechaRegistro:null,
                fechaEmision:null,
                cajero:null,
                fechaAnulacion:null,
                usuarioAnulacion:null,
                motivoAnulacion:null,
                subtotal:null,
                descuento:null,
                total:null,
                saldo:null,
                codigoEmpleado:null,
                refacturada:null,
                facturaLote:null
            },
            documento:{
                tipoDocumento:null,
                noDocumento:null,
                nombre:null
            },
            listas:{
                tiposDocumentos:[],
                detalleFactura:[],
                detalleCargos:[],
                recibos:[],
                notasCredito:[],
                costoDetalle:[],
                ventaDirectaDetalle:[]
            },
            DefaultDxGridConfiguration:{...DefaultDxGridConfiguration,
                                                headerFilter:{
                                                    visible:false,
                                                    allowSearch:false
                                                }
            },
            tabs: [
                        {
                            name: 'Detalle factura',
                            value: 1
                        }, {
                            name: 'Detalle de Cargos',
                            value: 2
                        }, {
                            name: 'Recibos',
                            value: 3
                        }, {
                            name: 'Notas de crédito',
                            value: 4
                        }, {
                            name: 'Costo detalle',
                            value: 5
                        },  {
                            name: 'Venta directa detalle',
                            value: 6
                        }
                        
                    ],  
        }
    },
    methods:{
        init(){
            this.Consulta().CargarTipoDocumento()
        },
        async exportDataTables() {
            const workbook = new Workbook();
            const DetalleFactura = workbook.addWorksheet('Detalle Factura');
            const DetalleCargos = workbook.addWorksheet('Detalle de Cargos');
            const Recibo = workbook.addWorksheet('Recibo');
            const NotasCredito = workbook.addWorksheet('Notas de Crédito');
            const CostoDetalle = workbook.addWorksheet('Costo Detalle');
            const VentaDirectaDetalle = workbook.addWorksheet('Venta Directa Detalle');
            const fechaGeneracion = await this.$dbdate()
            exportDataGrid({
                worksheet: DetalleFactura,
                component: this.$refs[detalleFacturaRef].instance              
            }).then(() => exportDataGrid({
                worksheet: DetalleCargos,
                component: this.$refs[detalleCargosRef].instance   
            })).then(() => exportDataGrid({
                worksheet: Recibo,
                component: this.$refs[recibosRef].instance   
            })).then(() => exportDataGrid({
                worksheet: NotasCredito,
                component: this.$refs[notasCreditoRef].instance   
            })).then(() => exportDataGrid({
                worksheet: CostoDetalle,
                component: this.$refs[costoDetalleRef].instance   
            })).then(() => exportDataGrid({
                worksheet: VentaDirectaDetalle,
                component: this.$refs[ventaDirectaDetalleRef].instance   
            })).then(() => {                
                workbook.xlsx.writeBuffer().then((buffer) => {
                saveAs(new Blob([buffer], { type: 'application/octet-stream' }), ''.concat('Factura',this.factura.serie,'-',this.factura.numero,'_',
                                                                                            this.Consulta().GetDateTimeValue(fechaGeneracion),'.xlsx'));
                });
            });
        },
        ConsultaFactura(tipoProceso){
            this.facturaCargada = false
            if(!this.ValidarDatosFactura(tipoProceso)){ return }

            this.axios.post('/app/v1_caja/ConsultaFactura',
                            {
                                serieFactura:this.factura.serie,
                                numeroFactura:this.factura.numero,
                                tipoProceso
                            })
                      .then(resp=>
                        {
                            if(resp.data.codigo!=0){ return }
                        
                            if(tipoProceso == 1)
                            {
                                if(resp.data.json.length == 0){
                                    this.Consulta().MostrarError('No se encontro la factura con los datos ingresados.')
                                    this.Consulta().LimpiarDatosBusqueda()
                                    return
                                }

                                if(resp.data.json[0].codigo == 0){
                                    this.CargarCabecera(resp.data.json[0])
                                    this.CargarConsultaFacturas()
                                    this.facturaCargada = true
                                }else{
                                    this.Consulta().MostrarError(resp.data.json[0].descripcion)
                                }
                            }
                            
                            if(tipoProceso == 2){
                                this.listas.detalleFactura = resp.data.json                                
                            }else if(tipoProceso == 3){
                                this.listas.detalleCargos = resp.data.json                                
                            }else if(tipoProceso == 4){
                                this.listas.recibos = resp.data.json                             
                            }else if(tipoProceso == 5){
                                this.listas.notasCredito = resp.data.json                             
                            }else if(tipoProceso == 6){
                                this.listas.costoDetalle = resp.data.json                               
                            }else if(tipoProceso == 7){
                                this.listas.ventaDirectaDetalle = resp.data.json                              
                            }
                            
                        }
                      )                        
        },
        CargarConsultaFacturas(){
            this.ConsultaFactura(2)
            this.ConsultaFactura(3)
            this.ConsultaFactura(4)
            this.ConsultaFactura(5)
            this.ConsultaFactura(6)
            this.ConsultaFactura(7)
        },
        CargarCabecera(DatosCabecera){
            this.admision                   = DatosCabecera.Admision

            this.factura.serie              = DatosCabecera.SerieFactura
            this.factura.numero             = DatosCabecera.Factura
            this.factura.numeroFel          = DatosCabecera.NumeroFEL
            this.factura.estado             = DatosCabecera.EstadoFac
            this.factura.fechaRegistro      = DatosCabecera.FechaRegistro
            this.factura.fechaEmision       = DatosCabecera.Fecha
            this.factura.cajero             = DatosCabecera.Cajero
            this.factura.fechaAnulacion     = DatosCabecera.FechaAnulacion
            this.factura.usuarioAnulacion   = DatosCabecera.UsuarioAnulacion
            this.factura.motivoAnulacion    = DatosCabecera.Razon
            this.factura.subtotal           = DatosCabecera.Subtotal
            this.factura.descuento          = DatosCabecera.Descuento
            this.factura.total              = DatosCabecera.Total
            this.factura.codigoEmpleado     = DatosCabecera.Empleado
            this.factura.refacturada        = DatosCabecera.YaRefacturada
            this.factura.facturaLote        = DatosCabecera.FacturaLote
            this.factura.tipo               = DatosCabecera.TipoFactura

            this.documento.tipoDocumento    = ref(this.listas.tiposDocumentos.find(doc=>doc.Valor == DatosCabecera.TipoReceptor))                            
            this.documento.noDocumento      = DatosCabecera.Nit
            this.documento.nombre           = DatosCabecera.Nombre

        },
        ValidarDatosFactura(tipoProceso){
            let validacion = true
            if(!this.ValidarVariable(this.factura.serie) || !this.ValidarVariable(this.factura.numero)){
                this.Consulta().MostrarError('Ingrese una serie y número de factura')
                validacion = false
            }else if(!this.ValidarVariable(tipoProceso)){
                this.Consulta().MostrarError('Ingrese un opción para consultar')
                validacion = false
            }
            return validacion
        },
        ValidarVariable(entrada){   
            if(typeof(entrada) == 'number'){
                return entrada ? true : false;
            }else if(typeof(entrada) == 'string'){
                return entrada && entrada.length > 0 ? true : false;
            }else if(typeof(entrada) == 'object'){
                return entrada ? true : false;
            }            
            return false;
        },
        Consulta(){
            return{
                GenerarFactura: async (nombrereporte)=>{
                    let reporte = "Impresion FEL"
                    let postData = {
                        SerieFactura: this.factura.serie ,
                        NumFactura:   this.factura.numero,
                        nombrereporte
                    }


                    this.$reporte_modal({
                        Nombre: reporte,
                        Opciones: {
                            ...postData
                        }
                    })                

                },   
                MostrarError:(mensaje,tiempo=2000)=>{
                    this.$vs.notify({
                        time:tiempo,
                        title: 'Cajero',
                        color: 'danger',
                        text:mensaje,
                        position: 'top-center'
                    })
                },
                MostrarSatisfactorio:(mensaje)=>{
                    this.$vs.notify({
                        time:2000,
                        title: 'Cajero',
                        color: 'success',
                        text:mensaje,
                        position: 'top-center'
                    })
                },
                CargarTipoDocumento:()=>{
                    this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision', {
                                        opcion:'C',
                                        subOpcion:2,
                                        agrupacion:'TipoReceptor'
                                    })
                            .then(resp => {                            
                                        if (resp.data && resp.data.json.length > 0){
                                            this.listas.tiposDocumentos = resp.data.json;
                                        }
                                    }
                                )
                },  
                LimpiarDatos:()=>{
                    this.admision                   = null

                    this.factura.serie              = null
                    this.factura.numero             = null
                    this.factura.numeroFel          = null
                    this.factura.estado             = null
                    this.factura.fechaRegistro      = null
                    this.factura.fechaEmision       = null
                    this.factura.cajero             = null
                    this.factura.fechaAnulacion     = null
                    this.factura.usuarioAnulacion   = null
                    this.factura.motivoAnulacion    = null
                    this.factura.subtotal           = null
                    this.factura.descuento          = null
                    this.factura.total              = null
                    this.factura.codigoEmpleado     = null
                    this.factura.refacturada        = null
                    this.factura.facturaLote        = null
                    this.factura.tipo               = null

                    this.documento.tipoDocumento    = null
                    this.documento.noDocumento      = null
                    this.documento.nombre           = null

                    this.listas.detalleFactura = []
                    this.listas.detalleCargos  = []
                    this.listas.recibos        = []
                    this.listas.notasCredito   = []
                    this.listas.costoDetalle   = []
                    this.listas.ventaDirectaDetalle = []

                    this.facturaCargada = false
                },
                LimpiarDatosBusqueda:()=>{
                    this.admision                   = null

                    this.factura.numeroFel          = null
                    this.factura.estado             = null
                    this.factura.fechaRegistro      = null
                    this.factura.fechaEmision       = null
                    this.factura.cajero             = null
                    this.factura.fechaAnulacion     = null
                    this.factura.usuarioAnulacion   = null
                    this.factura.motivoAnulacion    = null
                    this.factura.subtotal           = null
                    this.factura.descuento          = null
                    this.factura.total              = null
                    this.factura.codigoEmpleado     = null
                    this.factura.refacturada        = null
                    this.factura.facturaLote        = null
                    this.factura.tipo               = null

                    this.documento.tipoDocumento    = null
                    this.documento.noDocumento      = null
                    this.documento.nombre           = null

                    this.listas.detalleFactura = []
                    this.listas.detalleCargos  = []
                    this.listas.recibos        = []
                    this.listas.notasCredito   = []
                    this.listas.costoDetalle   = []
                    this.listas.ventaDirectaDetalle = []

                    this.facturaCargada = false
                },
                GetDateTimeValue(value) {
                    if (value === undefined) {
                        return null
                    } else if (value === null) {
                        return null
                    } else if (value == '') {
                        return null
                    }
                    return moment(value).format('DD.MM.YYYY_HH.mm');          
                },
            }
        }
    },
    computed:{
        sesion() {
            return this.$store.state.sesion
        }
    },
    mounted(){
        this.init()
    }
}
</script>
<style>

    .vs-row-especial{
        display: block !important;
    }
    /* Estilo tablas dev express */
    .dx-popup-title {
        background-color: #f4eade !important;
        color: #2f496e !important;
    }

    .dx-list-item>#itemsMenuDrawer {
        color: #2988bc !important;
        background-color: #f4eade !important;
    }

    .dx-list-item-selected>#itemsMenuDrawer {
        color: #f4eade !important;
        background-color: #ed8c72 !important;
    }

    .dx-scrollable-container {
        touch-action: auto !important;
    }

    /*Ancho mínmo de los grid*/
    #Contenido .dx-datagrid {
        min-width: 302px;
    }

    /**Modal actualizacion de peso y talla */
    #popupTallaPeso .vs-popup {
        width: 400px !important;
    }

    .dx-datagrid-headers td {
        vertical-align: middle !important;
    }

    .dx-resizable {
        display: inline-grid;
    }

    .dx-datagrid-rowsview .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed)>td {
        background-color: #00ccff !important;
        color: black !important;
    }

    .dx-datagrid-rowsview .dx-row.dx-data-row.dx-column-lines.dx-state-hover>td {
        background-color: #79e4ff !important;
        color: black !important;
        font-size: 16px;
    }

    .dx-datagrid-headers {
        background-color: linen !important;
        color: black !important;
        font-weight: bold;
    }

    td {
        vertical-align: middle !important;
    }
    /* Fin Estilo tablas dev express */

    .labelTitulo{
        font-size:10pt;
        font-weight: bold;        
        color:black;
    }
    .labelInfo{
        font-size:10pt;
        font-weight: bold;
        color: "primary";
    }
    .labelInfoImportante{
        vertical-align: center;        
        font-size:14pt;
        font-weight: bold;
        color: "primary";
    }
    .label-total
    {
        font-size: 25px;
        color:black;
        font-weight: bold;
        text-align: center;
    }
    .centered-element {        
        top: -20%;
        transform: translateY(-20%);
    }
</style>