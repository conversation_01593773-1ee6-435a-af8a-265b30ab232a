import Vue from 'vue'
import { ListoToTree, TravelTree, FilterNodes, TreeToList } from './components/sermesa/funciones/Tree'


/**
 * Validar corporativo de un usuario
 */
Vue.prototype.$validar_usuario = function ({ corporativo = null, notify = true }={}) {
    return new Promise((resolve, reject) => {
        // console.log(1)
        const result = (e)=>{
            resolve(e)
        }
        const rej = (e)=>{
            reject(e)
        }
        this.$store.dispatch('solicitarCredencial', {
            corporativo,
            result,
            reject: rej,
            notify,
        })
    })
}

// validación de privilegios sicronico
Vue.prototype.$validar_privilegio = function (permiso) {
    let arr = this.$store.state.privilegios.filter(data => data.Privilegio == permiso)
    return {
        status: (arr.length > 0) ? true : false,
        data: arr
    }
}

/**
 * Validación de privilegio especifico con un corporativo, de utilidad para validar permisos de usuarios diferentes al que inició sesión
 * @param {*} corporativo user id o caroporativo
 * @param {*} permiso Nombre del permiso a evaluar sobre la funcionalidad acutal
 * @returns promesa que al resolverse devuelve el objeto
 * { 
 *      estado = 0, 
 *      descripcion = [boolean] true si tiene permiso, de lo contario false
 * }
 */
Vue.prototype.$validar_permiso_corporativo = function(corporativo, permiso){
    return this.axios.post('/app/usuario/validar_permiso_corporativo',{"Corporativo":corporativo,"NombrePermiso":permiso})
}

/**validar_func_usuario_permiso
 * Verifica si el usuario de sessión tiene el privilegio de la funcionalidad actual (router)
 * Si no tiene el privilegio, se solicita autenticación de otro usuario y se valida que este otro usuario tenga el privilefio
 * @param {string} pathPermiso ruta de la funcionalidad a validar: @example '/COBROS/COB009'
 * @param {string} privilegio nombre del privilegio a validar: @example 'CHEQUE_MODIFICA_MONTO'
 * @returns {Promise} que al resolverse con permisos del usuario actual lleva argumento 
 * {
 *      status: true, 
 *      data: [array], //lista de permisos
 *      privilegio: [array] //objetos del privilegios que coinciden 
 * }
 * 
 * Si resuelve solicitando autenticación de otro usuario devuelve argumento 
 * {
 *      corporativo: [string], //número de cormporativo que se autentico
 *      descripcion: true, //indica que el permiso existe
 *      estado: 0,
 * }
 */
Vue.prototype.$validar_func_usuario_permiso = function(pathPermiso, privilegio, notify = true) {
    return new Promise( (resolve, reject)=> {
        this.$validar_funcionalidad(pathPermiso, privilegio, (r) => {
            
            if(r.status) {
                resolve(r)
            }
            else{
                this.$validar_usuario({notify:false}).then((corporativo)=> {
                    this.$validar_permiso_corporativo(corporativo, privilegio).then(resp=>{
                        if (resp.data.descripcion)
                            resolve({...resp.data, corporativo})
                        else {
                            if (notify)
                                this.$vs.notify({
                                    title: 'Validar permiso',
                                    text: 'El usuario no tiene el permiso necesario',
                                    color: 'danger',
                                    iconPack: 'feather',
                                    icon: 'icon-alert-circle',
                                    time: 5000,
                                })
                            reject(false)
                        }
                    })
                })
            }
            
        })
    })
},

//Permite la funcionalidad de manera generia 
//de generar un reporte sobre una pantalla 
//realiza la recuperacion de los valores de paramtria de un reporte configurado
//para permitir que una pantalla genere un reporte
//-----------------------------------------------------------------------------
Vue.prototype.$recupera_parametros_reporte = async function (nombreReporte) {

    const resp = await this.axios.post('/app/reporte/ReporteListadoPrivilegio',
        {
            "Nombre": nombreReporte

        })

    return resp.data.json.map(data => {
        return {
            Aplicativo: data.Aplicativo,
            IdFuncionalidad: data.IdFuncionalidad,
            Visible: false,
            _level: data._level.map(d => {
                let l = []
                d._level.map(data => {
                    if (data._level.filter(d => d.Tipo != 'Oculto').length > 0) l.push(data)
                    return true
                })
                return {
                    Funcionalidad: d.Funcionalidad,
                    IdFuncionalidad: d.IdFuncionalidad,
                    _level: l.map(m => {
                        return {
                            ...m,
                            _level: m._level.map(m => {
                                //const extraer = extraerBuscador(m.Eval)
                                return {
                                    ...m,
                                    Buscador: '',//((extraer) ? extraer[0] : null),
                                    Buscador_Campo: ''// ((extraer) ? extraer[1] : null)
                                }
                            })
                        }
                    })
                }
            })

        }
    })

    // function extraerBuscador(evaluador) {
    //     try {
    //         const texto = validarBuscador(evaluador)
    //         if (!texto) return null
    //         const conversion = JSON.parse(texto[0])
    //         return [conversion, texto[1]]
    //     } catch (error) {
    //         return null
    //     }
    // }

    // function validarBuscador(evaluador) {
    //     if (evaluador === '') return false
    //     const separacion = evaluador.split('***')
    //     if (separacion.length !== 2) return false
    //     let texto = (separacion[0]) ? separacion[0] : ''
    //     return [((texto) ? `[${texto.replace(/(\r\n|\n|\\n|\r)/gm, '')}]` : null), separacion[1]]
    // }

}
//Se crea para permitir la funcionalidad generica de generacion de reporte
//desde una pantalla.
//Una vez se configure la misma en el modulo de reportes
//este metodo mapea los valores del modelo dentro de las
//opciiones del reporte, permitiendo la generacion de una archivo PDF
//------------------------------------------------------------------------------
Vue.prototype.$prepara_valores_reporte = function ({
    Nombre,
    Data_source,
    Data_report
} = {}) {
    this.$vs.loading({
        scale: 0.6
    })
    // const Imprimir =''
    // const  AbrirTab ='

    const reporte = {
        generarPDF: true,
        generarExcel: true,
        generarCSV: true,
        generar: false,
        popup: false,
        buscar: '',
        titulo: Nombre,
        url: '',
        url_pdf: '',
        opciones: [],
        pdf: '',
        buscador: null
    }

    const arr_datos = Data_report
    const arr_source = Data_source


    let _opciones = {}, arrayObjetos = []

    const getNestedObject = (nestedObj, pathArr) => {
        return pathArr.reduce((obj, key) =>
            (obj && obj[key] !== 'undefined') ? obj[key] : undefined, nestedObj);
    }


    reporte.url = getNestedObject(arr_datos, [0, '_level', 0, '_level', 0, 'UrlApi'])
    reporte.opciones = arr_datos.map(data => {
        return {
            ...data,
            value: null
        }
    })

    let parametros_reporte = getNestedObject(reporte.opciones, [0, '_level', 0, '_level', 0, '_level']).map(data => {
        return {
            ...data,
            value: null
        }
    })


    Object.keys(parametros_reporte).forEach(function (key, i) {
        let parametro = getNestedObject(parametros_reporte, [i, 'Parametro'])

        Object.keys(arr_source).forEach(key => {
            if (parametro === key) {
                parametros_reporte[i].value = arr_source[key]
            }

        });
    });


    parametros_reporte.map(data => {
        _opciones[data.Parametro] = ((data.Parametro.indexOf("=") < 0) ? ((data.Tipo == 'Checkbox') ? (data.value == true ? 1 : 0) : (data.value != null && data.value != "") ? data.value : data.ValorDefecto) : data.Parametro.split('=')[1])


        if (Array.isArray(_opciones[data.Parametro])) _opciones[data.Parametro] = _opciones[data.Parametro].join(',')

        // Transformación de fecha
        if ((data.Tipo == 'Fecha' || data.Tipo == 'Fecha/Hora') && data.value ) {
            const a = data.value.split(' ')
            const f = a[0].split('-')
            if (f[0] > 1900) {
                _opciones[data.Parametro] = `${f[2]}/${f[1]}/${f[0]}`
            } else {
                _opciones[data.Parametro] = `${f[0]}/${f[1]}/${f[2]}`
            }

            if (a.length > 1) {
                _opciones[data.Parametro] = _opciones[data.Parametro] + ' ' + a[1]
            } else if (data.Tipo == 'Fecha/Hora') {
                _opciones[data.Parametro] = _opciones[data.Parametro] + ' 00:00:00'
            }
        }
        arrayObjetos.push({ "Etiqueta": data.Etiqueta, "Parametro": data.Parametro, "value": (data.Parametro.indexOf("=") < 0) ? ((data.Tipo == 'Checkbox') ? (data.value == true ? 1 : 0) : (data.value != null && data.value != "") ? data.value : data.ValorDefecto) : data.Parametro.split('=')[1] })
    })

    _opciones.json = JSON.stringify(arrayObjetos)

    return _opciones;


}
//Permite la funcionalidad de generar y enviar un correo desde una
//pantalla configurada para emisision de reporte
//-----------------------------------------------------------------------------
Vue.prototype.$genera_reporte_envio = function ({
    Nombre,
    Data_source,
    Data_report
} = {}) {
    this.$vs.loading({
        scale: 0.6
    })

  
    let _opciones = {}//, arrayObjetos = []
     _opciones = this.$prepara_valores_reporte({
                            Nombre: Nombre,
                            Data_source: Data_source,
                            Data_report: Data_report
                        })

    const Formato = "PDF"


    return this.axios.post("/app/reporte/EnvioReporte", {
        Nombre,
        Opciones: {
            tiporeporte: (Formato == 'PDF') ? "application/pdf" : (Formato == 'EXCEL') ? 'application/vnd.ms-excel' : "text/csv",
            ..._opciones
        }
    }, {
        responseType: 'arraybuffer'
    })



}
//Permite la funciondalidad de generacion de reporte 
// de menera generica desde una pantalla configurada
//en el modulo de reportes 
//-----------------------------------------------------------------
Vue.prototype.$genera_reporte = function ({
    Nombre,
    Data_source,
    Data_report
} = {}) {
    this.$vs.loading({
        scale: 0.6
    })


    let _opciones = {}//, arrayObjetos = []
     _opciones = this.$prepara_valores_reporte({
                            Nombre: Nombre,
                            Data_source: Data_source,
                            Data_report: Data_report
                        })
    
    const resp =  this.$reporte_modal({
        Nombre: Nombre,
        Opciones: _opciones,
        Formato: 'PDF'
    }).then(resp => {
        if (resp == 0) {
            throw new Error('Uh-oh!');
        }
    })

    return resp

}
Vue.prototype.$validar_funcionalidad = function (path, permiso, variable) {
    let arr = []
    Vue.prototype.$find_in_tree(
        {children: this.$store.state.funcionalidades}//dummy object
        , x => x.children 
        , (x) =>{
            if(path == x.Url)
                arr.push(x)
            }
        )
    if (arr.length > 0) {
        if (variable == null) {
            return {
                status: (arr.length > 0) ? true : false,
                data: arr
            }
        } else {
            this.axios.post('/app/usuario/obtener_privilegios', {
                Modulo: arr[0].IdFuncionalidad
            })
                .then(resp => {
                    let r = resp.data.json.filter(data => data.Privilegio == permiso)

                    variable({
                        status: (arr.length > 0 && r.length > 0) ? true : false,
                        data: arr,
                        privilegio: r
                    })
                })
        }
    }
}

/**
 * Walter Ronquillo
 * Envia mensajes a Slack
 * @param {int} usuario 
 * @param {string} notificacion 
 * @returns 
 */
Vue.prototype.$mensaje_Slack = function (usuario, notificacion) {
    let bodyFormData = new FormData();
    bodyFormData.append('SlackUsuario', usuario);
    bodyFormData.append('SlackNotificacion', notificacion);
    return this.axios.post("/externo/EnviarNotificacionesSlack", bodyFormData)
}

/* Walter
    Mensaje cuando axios retorna una respuesta correcta
*/
Vue.prototype.$mensaje_ok = function (title, arr) {
    if (arr.data.codigo == 0 && arr.data.json.length > 0) {
        this.$vs.notify({
            time: 4000,
            title: title,
            text: arr.data.json[0].descripcion,
            iconPack: 'feather',
            icon: 'icon-alert-circle',
            color: 'success',
            position: 'bottom-right'
        })
    }
}

/* Walter
    09/07/2020 - Utilizado para validar y mostrar los reportes en funcionalidades independientes
*/
Vue.prototype.$reporte_modal = function ({
    Nombre,
    Opciones,
    Formato = 'PDF', // PDF, EXCEL
    AbrirTab = false,
    Imprimir = false,
    Descargar = false,
    NombreArchivoDescargar = ''
} = {}) {
    this.$vs.loading({
        scale: 0.6
    })

    return this.axios.post("/app/reporte/ReporteGenerador", {
        Nombre,
        Opciones: {
            tiporeporte: (Formato == 'PDF') ? "application/pdf" : (Formato == 'EXCEL') ? 'application/vnd.ms-excel' : "text/csv",
            ...Opciones
        }
    }, {
        responseType: 'arraybuffer'
    })
        .then(resp => {
            if (Formato == 'PDF') {
                this.$store.dispatch('reporte', {
                    pdf: 'data:application/pdf;base64,' + Buffer.from(resp.data, 'binary').toString('base64'),
                    abrirTab: AbrirTab, // Abrir nueva pestaña
                    imprimir: Imprimir,
                    titulo: Nombre,
                    mail: Opciones.ConfigCorreo,
                })
                if (Descargar){
                    if ((NombreArchivoDescargar =='') &&  (Nombre != '')){
                        NombreArchivoDescargar  = Nombre;
                    }else if (NombreArchivoDescargar ==''){
                        NombreArchivoDescargar ='Archivo';
                    }
                    const link = document.createElement('a');
                    link.href = `data:application/pdf;base64,${Buffer.from(resp.data, 'binary').toString('base64')}`;
                    link.download = NombreArchivoDescargar +'.pdf'; // Replace with desired filename
                    link.click();
                }
            }
            else if (Formato == 'EXCEL') {
                const url = window.URL.createObjectURL(new Blob([resp.data]))
                const link = document.createElement('a')
                link.href = url
                if (Nombre == ""){
                    Nombre = 'Reporte';
                }
                
                
                link.setAttribute('download', Nombre + '.xlsx') //or any other extension
                document.body.appendChild(link)
                link.click()
            }
            else {
                const url = window.URL.createObjectURL(new Blob([resp.data]))
                const link = document.createElement('a')
                link.href = url

                if (Nombre == ""){
                    Nombre = 'Reporte';
                }
                
                link.setAttribute('download', Nombre + '.csv') //or any other extension
                document.body.appendChild(link)
                link.click()
            }
            // resolve(true)
        })
}

Vue.prototype.$reporte_unificador = function (Reportes = []) {
    this.$vs.loading({
        scale: 0.6
    })

    return this.axios.post("/app/reporte/ReporteUnificador", {
        Reportes
    }, {
        responseType: 'arraybuffer'
    })
        .then(async resp => {
            // console.log(Buffer.from(resp.data, 'binary').toString('base64'))
            
            await this.$store.dispatch('reporte', {
                pdf: 'data:application/pdf;base64,' + Buffer.from(resp.data, 'binary').toString('base64'),
                abrirTab: false, // Abrir nueva pestaña
                imprimir: true,
                titulo: 'Reporte auditoria'
            })
        })
}

/**
 * Permite mapear los datos obtenidos del axios a un objeto
 * @param {*} param0 
 */
Vue.prototype.$map = function ({ objeto, respuestaAxios, omitir = [] }) {
    if (respuestaAxios && respuestaAxios.data && respuestaAxios.data.json) {
        const dataResp = respuestaAxios.data.json[0]
        Object.entries(objeto).forEach(item => {
            let data = Object.entries(dataResp).filter(f => f[0].toUpperCase() == item[0].toUpperCase())
            if (data.length > 0 && omitir.indexOf(item[0]) == -1) objeto[item[0]] = data[0][1]
        })
    }
}

Vue.prototype.$global_lpad = function (num, longitud) {
    let add = ""
    if (num.toString().length < longitud) {
        for (let i = 0; i < longitud - num.toString().length; i++) {
            add += "0"
        }
    }
    return add + '' + num;
}

Vue.prototype.$global_rpad = function (num, longitud) {
    let add = ""
    const decimal = num.toString().indexOf('.')
    if (decimal == -1) {
        if (num.toString().length < longitud) {
            for (let i = 0; i < longitud - num.toString().length; i++) {
                add += "0"
            }
        }
    } else {
        if (num.toString().length - decimal - 1 < longitud) {
            for (let i = 0; i < longitud - (num.toString().length - decimal - 1); i++) {
                add += "0"
            }
        }
    }
    return num + '' + add;
}

Vue.prototype.$excluir_objeto = function (obj, keys) {
    var target = {};
    for (var i in obj) {
        if (keys.indexOf(i) >= 0) continue;
        if (!Object.prototype.hasOwnProperty.call(obj, i)) continue;
        target[i] = obj[i];
    }
    return target;
}

Vue.prototype.$ahora = async function () {
    let datos = await this.axios.get('/ahora')
    let sep = datos.data.split(" ")
    let fecha = sep[0].split("/");
    let hora = sep[1].split(':')
    return new Date(parseInt(fecha[2]), parseInt(fecha[1] - 1), +fecha[0], +hora[0], +hora[1], +hora[2]);
}

Vue.prototype.$dbdate = async function () {
    let datos = await this.axios.get('/dbdate')
    return new Date(datos.data);
}

Vue.prototype.$formato_moneda = (valor) => {
    if (!valor) valor = 0
    const v = parseFloat(valor.toString().replace(/,/g, '')).toLocaleString("es-GT", {
        style: "currency",
        currency: "GTQ"
    })
    return v.toString().replace(v.toString().substring(1, 2), '')
}

Vue.prototype.$formato_decimal = (valor, decimal = 4) => {
    if (!valor) valor = 0
    const v = parseFloat(valor.toString().replace(/,/g, '')).toLocaleString("es-GT", {
        style: "decimal",
        maximumFractionDigits: decimal,
        minimumFractionDigits: decimal
    })
    return v
}
/**
 * Funcion que trasnforma cadenas representativas de fecha en formato MM/dd/yyyy, MM/dd/yyyy [hora] y formatos ISO 8601 al formato de fecha comunmente utilizado por el grupo 
 * hospitalario dd/MM/yyyy HH24:mm:ss, para fines de visualización
 *
 * @param {*} fecha_ cadena representativa de una fecha y hora
 * @param {*} formato fecha, fechahora, hora, vervos
 * d - dias 
 * M|MM - mes del año en dos digitos 03
 * MMMM - mes abreviado Mar
 * MMMMM - nombre del mes Marzo
 * y - año 2024
 * h - hora del día
 * m - minuto
 * s - segundo
 * W|WW - letra inicial del dia de la semana M
 * WWW - dia abreviado de la semana mar
 * WWWWW - nombre completo del día martes
 * @param {*} AmPm indica si sequiere fromato am pm o 24 horas, cuando se requiera la hora
 * 20/03/2024 13:45:01
 * 
 * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date/toLocaleDateString
 * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/DateTimeFormat#timestyle
 * M: en mayuscula indica el fromato de mes 
 * 
 * ddMMMMMMyyyy HH:mm:ss xm
 * @returns cadena de la fecha en base al formato solicitado
 * TODO, esta implementación es suceptible a problemas de rendimiento al tener una cantidad masiva de fechas por formatear (crear intancias de Formmaters puede mejorar el rendimiento)
 */

Vue.prototype.$formato_fecha = function (fecha_, formato = 'ddMMyyyy hh:mm:ss', AmPm = false) {
    if (!fecha_) return null

    let formatOptions = {
        hour12: AmPm,
        hour: formato.includes('h')?'2-digit':undefined,
        minute: formato.includes('m')?'2-digit': undefined,
        second: formato.includes('s')?'2-digit': undefined,
        weekday: formato.includes('WWWW')?'long': (formato.includes('WWW')? 'short': formato.includes('W')?'narrow':undefined),
        year: formato.includes('y')?'numeric':undefined,
        month: formato.includes('MMMM')?'long': (formato.includes('MMM')? 'short': formato.includes('MM')?'2-digit': formato.includes('M')?'narrow':undefined),
        day: formato.includes('d')? '2-digit': undefined,
    }
   
    /*eslint-disable */
    let mydate = new Date(
        fecha_  .replace(/a[\.\s]*m[\.\s]*/gi,' am')
                .replace(/p[\.\s]*m[\.\s]*/gi,' pm')
                .replace(/T/gi,' ') 
                + ' '
    /*eslint-enable */
    )// para tener algo asi 2024/03/29 10:44:54 am, 2024-03-29 10:44:54 am, 03/29/2024 01:49 pm
    /*el replace T por ' ' es para formatos ISO 8601 de fecha sin componente de hora de forma que el parse de date pueda '2024-03-29 ' con el espacio al final genera el date con timezone 
    Ejemplo new Date('2024-05-29') es diferente a new Date('2024-05-29 ') 
    ver https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date#date_time_string_format "date-only forms are interpreted as a UTC time and date-time forms are interpreted as local time"
    */
    if (isNaN(mydate.getDay())) {
        return fecha_
    }
    return mydate.toLocaleString('es-GT', formatOptions).replace(',','')
}

/**
 * Permite convertir una lista a una estructura de arbol, los objetos deben tener un identificador de cada elemento, y una propiedad para identificar al padre al que pertenece
 * 
 * @param collection arreglo con los elementos a manipular con objetos de la forma 
 * {
 *      id: ..., 
 *      idPadre:...,
 * } 
 * @param idName        {String} que indica el nombre de la propiedad que es el identificador de cada elemento del array, en algunos casos sea 'ID', 'identificado', 'index', ...
 * @param parentIdName  {String} nombre de la propiedad que hace referencia la padre al que pertenece el objeto para formar el árbol (como el caso anterior)
 * @param childsName    {String} indica el nombre de la propiedad del array de hijos de cada nodo del arbol, 'children', 'elementos', 'hijos', 'ChidNodes', 'Submenu', ...
 * @param clearChilds   {Boolean} @default false indica si limpian los hijos del nodo evaluado en base a @see childsName, por ejemplo si el nodo tiene la propiedad  llamada hijos = [ {... hijos, ...}, ... {...} ]
 * Si es verdadero limpiará el arreglo previo a generar el arbol de lo contrario se hará el anidamiento con lo elementos de @see collection y los 'hijos' de cada nodo si existen
 * @return  {Array} Con la estructura del árbol Objeto resultante con el atributo adicional llamado @see childsName agrupando a los descendientes 
 * {
 *      id: 1,
 *      parent: null,
 *      ...
 *      otras propiedades de cada elmento del array original
 *      ...
 *      chidren: [
 *          {
 *              id: 12,
 *              parent: 1,
 *              ...
 *              otras propiedades de cada elmento del array original
 *              ...
 *              chidren: [...]
 *          }, 
 *          ...
 *      ]
 * }
 */
Vue.prototype.$list_to_tree = ListoToTree


/**
 * Funcion de recorrido del árbol, se peuede utilizar para búsqueda o ejecutar acciones con cada elemento del arbol
 * Si es para búsqueda retornar algo diferente de null en la función @see compFun, el elemento encontrado por ejemplo ;)
 * @param {*} node nodo actual que se esta recorriendo, para utilizarlo por primera vez, eviar la Raíz.
 * @param {*} childsName nombre de la propiedad de tipo arreglo con los hijos del elemento actual
 * @param {*} compFun función de evalucaión para busquedas, recibe el elemento actual y allí se pueden hace las comparaciones necesarias. si e solo para recorrido retornar null o no retornar valor así recorrerá todo el árbol
 * @param {Function} preFunc función de evalucaión para busquedas se ejecuta en preorden, recibe el elemento actual y allí se pueden hace las comparaciones necesarias. si e solo para recorrido retornar null o no retornar valor así recorrerá todo el árbol
 * @param {Function} postFunc función de evalucaión para busquedas seejecuta en postorden, recibe el elemento actual y allí se pueden hace las comparaciones necesarias. si e solo para recorrido retornar null o no retornar valor así recorrerá todo el árbol
 * @returns retorna null si el elemento no se encontróde lo contrario retorna el elemento encontrado o bien lo que retorne @see compFun
 */
Vue.prototype.$find_in_tree = TravelTree


/**
 * 
 * @param {Array<Funcionalidades>} funcionalidades arreglo de objetos con los atributos mínimos:
 * @property {String} IdAplicativo Identificador del aplicativo
 * @property {String} Aplicativo Nombre del aplicativo o primer nivel de la raíz
 * @property {String} icon icono que se dibuja junto al nombre del aplicativo
 * @property {String} IdFuncionalidad idetificador de la funcionalidad asociada, es nula cuan es un directorio o carpeta
 * @property {String} Funcionalidad nombre de la funcionalidad
 * @property {String} Url indica la ubicación del directorio del archivo vue a dibujar y además la ruta asociada en el navegador
 * @property {Boolean} oculto 
 * @property {String} SubCarpeta
 * @returns 
 */
Vue.prototype.$build_menu = function(funcionalidades){

        /* 
        Si el aplicativo no existe se crea en el diccionario,
        si la carpeta no existe se crea en el diccionario
        si la funcionalidad tiene idCarpeta se asigna al submenu de carpeta <diccionario> sino se asigna al submenu del aplicativo <diccionario>
        a partir del diccinario de carpetas anidarlas entre si y luego asignarlas a su aplicativo
        */ 
        let temp = [] //resultado de aplicativos
        let diccionarioCarpetas = Object.create(null)
        let diccionarioAplicativos = Object.create(null)
        funcionalidades.map((func) => {
            if(!func.oculto && !func.IdFuncionalidadPadre){
                //Objeto base de funcionalidad
                let currFunc = {
                    url: func.path || func.Url,
                    name: func.Funcionalidad,
                    slug: func.Funcionalidad,
                    oculto: func.oculto,
                    props: {
                        IdFuncionalidad: func.IdFuncionalidad
                    },
                    icon: func.IconoFuncionalidad? func.IconoFuncionalidad: '',
                    children: func.children,
                    path: func.path,
                    orden: func.OrdenFuncionalidad
                }
                //Agregando aplicativos
                if(!diccionarioAplicativos[func.IdAplicativo])
                    diccionarioAplicativos[func.IdAplicativo] = 
                        {
                            IdAplicativo: func.IdAplicativo,
                            name: func.Aplicativo,
                            slug: func.Aplicativo,
                            icon: func.Icono,
                            submenu: [],
                            orden: func.OrdenAplicativo,
                        }

                if(func.IdCarpeta && !diccionarioCarpetas[func.IdCarpeta])
                    diccionarioCarpetas[func.IdCarpeta] = 
                        {
                            IdAplicativo: func.IdAplicativo,
                            IdCarpeta: func.IdCarpeta,
                            IdCarpetaPadre: func.IdCarpetaPadre,
                            name: func.SubCarpeta,
                            slug: func.SubCarpeta,
                            icon: null,
                            submenu: [],
                            orden: "-1"
                        }
                ///ahora vemos si la funcionalidad se agrega a la carpeta o al aplicativo
                if(func.IdFuncionalidad)//es falso en los elementos que representan carpetas sin funcionalidad asociada
                {
                    if(func.IdCarpeta)//Se agrega a la carpeta
                    {
                        if(!diccionarioCarpetas[func.IdCarpeta].submenu.find(t => !t.submenu && t.props.IdFuncionalidad == currFunc.props.IdFuncionalidad))//Se agrega si no existe en el menú por otro rol
                            diccionarioCarpetas[func.IdCarpeta].submenu.push(currFunc)
                    } 
                    else //Se agrega al aplicativo
                    {
                        if(!diccionarioAplicativos[func.IdAplicativo].submenu.find(t => !t.submenu && t.props.IdFuncionalidad == currFunc.props.IdFuncionalidad))//Se agrega si no existe en el menú por otro rol
                            diccionarioAplicativos[func.IdAplicativo].submenu.push(currFunc)
                    }
                }
                /////yeiiii 
            }
        })
        let carpetasAnidadas = Vue.prototype.$list_to_tree(Object.values(diccionarioCarpetas), 'IdCarpeta', 'IdCarpetaPadre', 'submenu')
        carpetasAnidadas.map(e => diccionarioAplicativos[e.IdAplicativo].submenu.push(e))
        
        function eliminarCarpetasSinFuncionalidades(data) {
            let nuevasCarpetas = [];
            if(data.props && data.props.IdFuncionalidad)
                return data
            // Llamar recursivamente la función para las carpetas restantes
            for (const carpeta of data.submenu || []) {
                let child = eliminarCarpetasSinFuncionalidades(carpeta);
                if (child)
                    nuevasCarpetas.push(carpeta);
            }
            // Actualizar la lista de carpetas en el objeto original
            if(data.submenu)
                data.submenu = nuevasCarpetas;

            if (nuevasCarpetas.length == 0)
                return null
            else
                return data
        }

        temp = Object.values(diccionarioAplicativos)
        eliminarCarpetasSinFuncionalidades({submenu:temp});
        
        //TODO ver el ordenamiento, en el SP esta g.orden, g.Aplicativo, sas.Nombre, f.orden, f.nombre
        temp = temp.sort( (a,b) => {
            return String.prototype.concat(a.orden, a.name).toUpperCase().localeCompare(String.prototype.concat(b.orden, b.name.toUpperCase()))
        })// ver los nuevos objetos en los diccionarios
        return temp
}

Vue.prototype.$filter_nodes = FilterNodes

Vue.prototype.$tree_to_list = TreeToList
/**Reemplaza las etiquetas de una cadena en formato HTML, */
Vue.prototype.$limpiar_html_tags = (strin) => {
    let regexTags = /(<([^>]+)>)/ig
    let regexSaltos = /<p><br><\/p>|<br>|<br\/>/ig
    let result = strin.replace(regexSaltos,'\n\r')
    result = result.replace(regexTags,'')
    result = result.replace(/<li>/gi, '\n-')
    return result
}
Vue.prototype.$limpiar_saltos_tabulares = (str) => {
    return str.replace(/\\n/g, '\n').replace(/\\\\n/g, '\n')
                .replace(/\\r/g,'\r').replace(/\\\\r/g,'\r')
                .replace(/\\t/g,'\t').replace(/\\\\t/g,'\t')
}
/**Reemplaza los saltos de línea escritos en \n por <br> y tabulaciones por '   '*/
Vue.prototype.$saltos_tab_to_html = (str) => {
    
    return str != undefined?
         str.replace(/\n/g, '<br>')
                .replace(/\r/g,'')
                .replace(/\t/g,'   ')
                :''
}

//Reemplaza los tabulares por 3 espacios en blanco
Vue.prototype.$reemplazar_tabulares = (str) => {
    return str !== undefined? str.replace(/\t/g, '   ') : ''
}

//Reemplaza los tabulares por 3 espacios en blanco
Vue.prototype.$reemplazar_tabulares_objeto = (str, busqueda, reemplazo) => {
    if(str !== undefined)
    {
        Object.entries(str).forEach((t) => {
            if (typeof t[1] === 'string') {
                str[t[0]] = t[1].replace(busqueda, reemplazo)
            }
        })
    }
    return str !== undefined? str : ''
}

/**
 * Filtra los elementos del arreglo en base a las palabras contenidas en la cadena de entrada, omitiendo los acentos y es case insensitive
 *
 * @param entrada		Es la cadena de entrada que se utilizará para filtrar
 * @param arreglo       Es el arreglo en le que se realizará el filtrado
 * @param descripcion   Es la propiedad de los elementos del arreglo sobre la cual se aplicará el criterio de búsqueda
 * @param inicio        Es la posición inicial del arreglo para deliminar el resultado del arreglo, de no haber coincidencias
 * @param fin           Es la posición final del arreglo para deliminar el resultado del arreglo, de no haber coincidencias
 *
 * @return {Array}      Retorna un arreglo con los elementos que coinciden con el filtro
 */
Vue.prototype.$filtrado_avanzado_palabras = (entrada, arreglo, descripcion, inicio, fin) => {
    let filtro = []
	if (entrada && entrada.length >= 3) {
		var searchValues = entrada.trim().split(/[\s|%]/)
        var values = '';
		for (let x of searchValues) {
            /* eslint-disable */
			values += x.normalize('NFD')
				.replace(/([^n\u0300-\u036f]|n(?!\u0303(?![\u0300-\u036f])))[\u0300-\u036f]+/gi, "$1")
				.normalize() + '(\\w|\\W)*'
            /* eslint-enable */
		}

		let patron = RegExp(values, 'i')
		for (let i of arreglo) {
            /* eslint-disable */
			if (patron.test(i[descripcion].normalize('NFD')
					.replace(/([^n\u0300-\u036f]|n(?!\u0303(?![\u0300-\u036f])))[\u0300-\u036f]+/gi, "$1")
					.normalize())) {
				filtro.push(i)
			}
            /* eslint-enable */
		}
		return filtro.slice(inicio, fin)
    }
    else if(entrada && entrada.length === 2)
    {
		// let patron = RegExp('j1' + '(\\w|\\W)*', 'i')
		// for (let i of arreglo) {
        //     /* eslint-disable */
		// 	if (patron.test(i[descripcion].normalize('NFD')
		// 			.replace(/([^n\u0300-\u036f]|n(?!\u0303(?![\u0300-\u036f])))[\u0300-\u036f]+/gi, "$1")
		// 			.normalize())) {
		// 		filtro.push(i)
		// 	}
        //     /* eslint-enable */
		// }
		// return filtro
    }else {
		return arreglo.slice(inicio, fin)
	}
}

/**
 * Realiza la acción para levantar el aplicativo de Visorfm
 */

import io from 'socket.io-client'

Vue.prototype.$socke_up = function (comando)  {
    return new Promise((resolve, reject) => {
        try{
            let ip = localStorage.getItem('IpLocal');
            if(ip === null || ip === '')
            {
                this.$vs.notify({
                    time: 4000,
                    title: 'Error IP',
                    text: 'Su IP no está configurada correctamente. Contacte con soporte técnico para solucionarlo.',
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'danger',
                    position: 'bottom-center'
                })
            }
            else
            {
                let socket = io('https://sighos-linea.hospitaleslapaz.com/');

                socket.emit('message',"WEB;"+ ip + ";" + comando);

                socket.on("disconnect", () => {
                });
            }
            resolve();
        }catch(error){
            reject(error);
        }
    })
}
/**
 * 
 * @param {D} myDate 
 * @param {*} removeTimeZone 
 * @param {*} dateBase 
 * @returns 
 */
Vue.prototype.$parse_date = function (myDate, removeTimeZone= false, dateBase = 'js')  {
    if(!myDate) 
        return null
    const tipo = typeof myDate
    try{
        if (tipo == 'string') {
            let strdate = myDate
            if(myDate.includes('/')) {//formato dd/mm/yyyy hh:mm:ss o hh:mm:ss am|pm
                myDate = myDate.replace(/\s+|\.|t/gi,'')//removiendo espacios, puntos y si tuviera separador T para que quede algo asi '01/02/202312:13:00pm'
                let fecha = myDate.slice(0,10)
                let parts = fecha.split('/')
                let hora = myDate.slice(10,20)//no deberia exceder los 20 caracteres
                if(!hora) hora = '00:00:00'
                hora = hora.replace(/am|pm/gi,' $&') + (removeTimeZone?' -00:00':'') //le agregamos un espacio si hay am pm para que parsee bien 
                strdate = String.prototype.concat(
                    parseInt(parts[2], 10),'-',
                    parseInt(parts[1], 10),'-',
                    parseInt(parts[0], 10),' ',
                    hora,
                )     //ver estandar ISO 8601 para el fromato que se usa
            }
            return new Date(strdate)
        }
        else if (tipo == 'number' && dateBase == 'excel') {
            return new Date(Math.round((myDate - 25569)*86400*1000))
        }
        return new Date(myDate)    
    }
    catch {
        return null
    }
    

}
/**
 * 
 * @param {*} nacimiento fecha de nacimeinto
 * @param {*} fecha fecha a la cual se quiere calcular la edad, regularmente es hoy
 * @returns 
 */
Vue.prototype.$calcular_edad = function (nacimiento, fecha){    
    //funcion de parseo para que admita multiples expresiones de fecha
    let _hoy = Vue.prototype.$parse_date(fecha)
    let _nacimiento = Vue.prototype.$parse_date(nacimiento)
    
    if (!_hoy || !_nacimiento)
        return null 

    let edad = _hoy.getFullYear() - _nacimiento.getFullYear() - 1; //-1 porque no se si ha cumplido años ya este año

    //si resto los meses y me da menor que 0 entonces no ha cumplido años. Si da mayor si ha cumplido
    if (_hoy.getMonth() - _nacimiento.getMonth() < 0) 
       return edad
    if (_hoy.getMonth() - _nacimiento.getMonth() > 0)
       return edad + 1

    //entonces es que eran iguales. miro los dias
    //si resto los dias y me da menor que 0 entonces no ha cumplido años. Si da mayor o igual si ha cumplido
    if (_hoy.getUTCDate() - _nacimiento.getDate() >= 0)
       return edad + 1

    return edad

}