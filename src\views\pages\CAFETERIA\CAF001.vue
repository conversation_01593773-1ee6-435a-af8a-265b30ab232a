<template>
<div>
    <vx-card :title="`Cafetería ${sesion.sesion_sucursal_nombre}`">
        <!-- reportes -->
        <vs-popup id="contentreport" classContent="popup-generar" title="Informe" :active.sync="info.reporte" fullscreen style="z-index:99998;height:100%">
            <embed v-if="info.reporte_src!=''" type="application/pdf" :src="info.reporte_src" ref="pdfDocument" width="100%" height="98%" />
        </vs-popup>

        <div class="flex flex-wrap">
            <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                <SM-Buscar label="Admisión" v-model="info.admision" api="app/cafeteria/Busqueda_Admisiones" :api_campos="['Admision','Paciente','Habitacion']" :api_titulos="['Admision','Paciente','Habitación']" :api_filtro="{'Activa':0}" api_campo_respuesta="Admision" api_campo_respuesta_mostrar="Admision" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="true" :callback_buscar="Consulta().habitacion" :callback_cancelar="Otros().limpiar" />
                <br>
                <p>Paciente: {{ info.paciente }}</p>
                <b>Habitacion: {{ info.habitacion }}</b>
            </div>
            <vs-spacer></vs-spacer>
            <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                <div class="flex flex-wrap p-1 mt-4" style="background-color:#eee;border:1px solid #ccc">
                    <!-- <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-4">
                    <vs-input label="Habitación:" class="w-full" :value="info.habitacion" disabled />
                </div> -->
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-4">
                        <vs-input label="Orden:" class="w-full" :value="info.tipoOrden" disabled />
                    </div>
                    <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-4">
                        <vs-input label="Bodega:" class="w-full" :value="info.bodega" disabled />
                    </div>
                </div>
            </div>
        </div>

        <!-- CARGOS AGREGADOS -->
        <div class="flex flex-wrap mt-4">
            <div class="md:w-1/2 lg:w-1/2 xl:w-1/2 sm:w-1/2 p-0">
                <vs-table2 max-items="10" search pagination :data="cargos" height="470px">
                    <template slot="thead">
                        <th order="Codigo" width="50px">Código</th>
                        <th order="Nombre">Nombre</th>
                        <th order="Precio" width="60px"> Precio.U</th>
                        <th order="Cantidad" width="140px">Cant.</th>
                        <!-- <th  width="50px">Acción</th> -->
                        <!-- <th order="Exist" width="50px">Existencia</th> -->
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2>
                                {{ tr.Codigo }}
                            </vs-td2>
                            <vs-td2>
                                <div class="col-md-8">
                                    <small>
                                        {{ tr.Nombre }}
                                    </small>
                                </div>

                            </vs-td2>
                            <vs-td2>
                                {{parseFloat(tr.Precio).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                            </vs-td2>

                            <vs-td2>
                                <div class="cantidad">
                                    <button type="filled" @click="tr.Cantidad ++" :disabled="tr.Existencia<=tr.Cantidad && tr.Tipo != 'S'">+</button>
                                    <div>{{ tr.Cantidad }}</div>
                                    <button v-if="tr.Cantidad>1" type="filled" @click="tr.Cantidad--">-</button>
                                    <button v-else type="filled" @click="cargos.splice(indextr,1)">-</button>
                                </div>
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>
                <!-- {{cargos}} -->

            </div>

            <!-- LISTADO DE PRODUCTOS -->
            <div class="md:w-1/2 lg:w-1/2 xl:w-1/2 sm:w-1/2 pl-2">
                <div>
                    <vs-table2 max-items="7" search pagination :data="productos" height="470px">
                        <template slot="thead">
                            <th order="Codigo" width="50px">Código</th>
                            <th order="Nombre">Nombre</th>
                            <th order="Precio" width="60px">Precio</th>
                            <!-- <th order="Exist" width="50px">Existencia</th> -->
                        </template>
                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data" @click="(tr.Existencia>0 || tr.Tipo == 'S')? Interfaz().agregaProducto(tr) :null" :style="{'cursor':tr.Existencia<=0?'default':'pointer','opacity':tr.Existencia<=0?0.5:1}">
                                <vs-td2>
                                    {{ tr.Codigo }}
                                </vs-td2>
                                <vs-td2>
                                    {{ tr.Nombre }}
                                    <div style="margin-top:-3px">
                                        <small>{{tr.Descripcion}}</small>
                                    </div>
                                </vs-td2>
                                <vs-td2>
                                    {{parseFloat(tr.Precio).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
                </div>
            </div>
        </div>
        <vs-divider></vs-divider>
        <div class="flex bottom">
            <vs-button color="danger" class="mr-5" @click="cargos = []">Limpiar Pedido</vs-button>
            <vs-spacer></vs-spacer>
            <div style="width:200px">
                <tr>
                    <td class="p-3">
                        <h4>TOTAL</h4>
                    </td>
                    <td class="p-3" width="200px" style="text-align:right">
                        <h4>{{totalCargos}}</h4>
                    </td>
                </tr>
            </div>
            <vs-spacer></vs-spacer>
            <!-- <vs-button color="success" @click="Guardar().reporte()">Reporte</vs-button> -->
            <vs-button @click="Guardar().pedido()">Guardar Pedido</vs-button>
        </div>
    </vx-card>
</div>
</template>

<script>
export default {
    data() {

        return {
            info: {
                admision: null,
                nivelprecio: null,
                bodega: null,
                tipoOrden: null,
                habitacion: null,
                paciente: null,
                reporte_src: null,
                reporte: false
            },
            cargos: [],
            productos: []
        }
    },
    computed: {
        totalCargos() {
            return this.cargos.reduce((acumulador, cargo) => acumulador + parseFloat(cargo.Precio) * cargo.Cantidad, 0).toLocaleString("es-GT", {
                style: "currency",
                currency: "GTQ"
            })

        },
        sesion() {
            return this.$store.state.sesion
        }
    },
    methods: {
        Consulta() {
            return {
                habitacion: (datos) => {
                    // console.log(datos)
                    this.info.habitacion = datos.Habitacion
                    this.info.nivelprecio = datos.NivelPrecios
                    this.info.paciente = datos.Paciente

                },
                init: () => {
                    this.axios.post('/app/cafeteria/obtiene_parametros', {})
                        //  Obtener información
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.info.bodega = resp.data.json[0].NumeroBodega
                                this.info.tipoOrden = resp.data.json[0].TipoOrden
                                return this.info.bodega
                            } else {
                                return null
                            }
                        })
                        // Obtener bodega
                        .then(bodega => {
                            if (!bodega) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Alerta',
                                    text: 'No existe información de bodega para esta base',
                                })
                                return false
                            }
                            this.productos = []
                            this.axios.post('/app/cafeteria/productos_cafe', {
                                    bodega
                                })
                                .then(resp => {
                                    if (resp.data && resp.data.json.length > 0) this.productos = resp.data.json
                                })
                        })
                }
            }
        },
        Guardar() {
            return {
                pedido: () => {
                    if (!this.info.admision) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Alerta',
                            text: 'Debe de ingresar la habitación',
                        })
                        return false
                    }

                    const cargos = this.cargos.map(m => {
                        return {
                            Codigo: m.Codigo,
                            Cantidad: m.Cantidad
                        }
                    })
                    this.axios.post('/app/cafeteria/inserta_cargos', {
                        ...this.info,
                        cargos
                    }).then((Tipo) => {
                            //TODO impresion del cargo
                            // console.log(Tipo)
                            const Respuesta = Tipo.data.descripcion.split(':')[1]
                            const [TipoOrden, Orden] = Respuesta.split('-')
                            // console.log(TipoOrden, Orden)

                
                            this.$reporte_modal({
                                Nombre: "Comanda",
                                Opciones: {
                                    TipoOrden: TipoOrden.trim(),
                                    orden: Orden.trim()
                                },
                                Imprimir: true
                            })

                            this.cargos = []
                            this.Otros().limpiar()
                        }
                    )
                }
            }
        },
        Interfaz() {
            return {
                agregaProducto: (producto) => {
                    const buscar = this.cargos.filter(listado => listado.Codigo == producto.Codigo)
                    if (buscar.length == 0) {
                        this.cargos.unshift({
                            ...producto,
                            Cantidad: 1
                        })
                    } else {
                        if (buscar[0].Cantidad < buscar[0].Existencia || buscar[0].Tipo == 'S') buscar[0].Cantidad++
                    }

                    // console.log(producto)
                }
            }
        },
        Otros() {
            return {
                limpiar: () => {
                    this.info.admision = null
                    this.info.habitacion = null
                    this.info.paciente = null
                }
            }
        }
    },
    mounted() {
        this.Consulta().init()
    }
}
</script>

<style scoped>
.cantidad button {
    height: 25px;
    width: 30px;
    border: 1px solid #ccc;
    border-radius: 5px;

}

.cantidad div {
    display: inline-block;
    height: 25px;
    width: 30px;
    text-align: center;
}
</style>
