<template>
<vx-card title="Contabilidad General">
    <div class="content content-pagex">
        <vs-divider class="label-size"> INTEGRACIÓN / ANTIGÜEDAD CLIENTES </vs-divider>
        <br>
        <br>
        <div class="flex-container">
            <div class="flex flex-wrap">
                <vs-col class="md:w-full lg:w-full xl:w-4/12 p-2">
                    <vx-card class="w-full" title="Reportes" style="padding:10px 10px">

                        <vs-row title="Reportes" style="padding:10px 10px">
                            <div class="flex flex-wrap">
                                <div class="w-full" style="padding:15px 10px">
                                    <label class="label-size" style="padding:10px 20px"> 1.</label>
                                    <vs-radio class="label-size" v-model="IdReporte" vs-name="Reporte" vs-value=1> Proveedores </vs-radio>
                                </div>
                                <div class="w-full" style="padding:15px 10px">
                                    <label class="label-size" style="padding:10px 20px"> 2.</label>
                                    <vs-radio class="label-size" v-model="IdReporte" vs-name="Reporte" vs-value=2> Anticipos a Proveedores </vs-radio>
                                </div>
                                <div class="w-full" style="padding:15px 10px">
                                    <label class="label-size" style="padding:10px 20px"> 3.</label>
                                    <vs-radio class="label-size" v-model="IdReporte" vs-name="Reporte" vs-value=3> Clientes </vs-radio>
                                </div>
                                <div class="w-full" style="padding:15px 10px">
                                    <label class="label-size" style="padding:10px 20px"> 4.</label>
                                    <vs-radio class="label-size" v-model="IdReporte" vs-name="Reporte" vs-value=4> Anticipos de Clientes </vs-radio>
                                </div>
                                <div class="w-full" style="padding:15px 10px">
                                    <label class="label-size" style="padding:10px 20px"> 5.</label>
                                    <vs-radio class="label-size" v-model="IdReporte" vs-name="Reporte" vs-value=5> Tarjetas de Crédito </vs-radio>
                                </div>
                                <div class="w-full" style="padding:15px 10px">
                                    <label class="label-size" style="padding:10px 20px"> 6.</label>
                                    <vs-radio class="label-size" v-model="IdReporte" vs-name="Reporte" vs-value=6> Seguros </vs-radio>
                                </div>
                                <div class="w-full" style="padding:15px 10px">
                                    <label class="label-size" style="padding:10px 20px"> 7.</label>
                                    <vs-radio class="label-size" v-model="IdReporte" vs-name="Reporte" vs-value=7> Cheques Rechazados</vs-radio>
                                </div>
                            </div>
                        </vs-row>
                    </vx-card>
                </vs-col>
                <vs-col class="md:w-full lg:w-full xl:w-8/12 p-2">
                    <vx-card class="w-full" style="padding:20px 20px">
                        <vs-row class="w-full">

                            <div class="w-full">
                                <div class="w-full" align="rigth">
                                    <vs-row class="md:w-full lg:w-full xl:w-w-full" style="padding:10px 50px">

                                        <div class="w-full" style="padding:10px 10px" v-show="DocPagadosV">
                                            <vs-checkbox class="label-size" v-model="DocPagados">Documentos Pagados Dentro del Mes</vs-checkbox>
                                            <br>
                                        </div>
                                        <div class="flex flex-wrap">
                                            <div class="w-full" style="padding:10px 10px">
                                                <ValidationProvider name="PeriodosF">
                                                    <label class="label-size">Del Período:</label>
                                                    <multiselect v-model="cbPeriodos" :options="ListaPeriodos" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="PeriodoSeleccionado" placeholder="Seleccionar Período" @input="onChangePeriodos">
                                                        <span slot="noOptions">Lista no disponible.</span>
                                                    </multiselect>
                                                </ValidationProvider>
                                            </div>
                                            <div class="w-full" style="padding:10px 10px">
                                                <ValidationProvider name="PeriodosI">
                                                    <label class="label-size">No tomar datos anteriores a:</label>
                                                    <multiselect v-model="cbPeriodosI" :options="ListaPeriodosI" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="PeriodoSeleccionadoI" placeholder="Seleccionar Período" @input="onChangePeriodosI">
                                                        <span slot="noOptions">Lista no disponible.</span>
                                                    </multiselect>
                                                </ValidationProvider>
                                            </div>
                                            <br>
                                            <br>
                                        </div>

                                        <div class="w-full" style="padding:10px 10px" v-show="CuentaV">
                                            <ValidationProvider name="Cuentas">
                                                <label class="label-size">Cuenta</label>
                                                <multiselect v-model="cbCuentas" :options="ListaCuentas" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="CuentaSeleccionado" placeholder="Seleccionar Cuenta" @input="onChangeCuenta">
                                                    <span slot="noOptions">Lista no disponible.</span>
                                                </multiselect>
                                            </ValidationProvider>
                                        </div>
                                    </vs-row>
                                    <vs-row class="md:w-full lg:w-full xl:w-8/12" style="padding:10px 50px">
                                        <div class="w-full" style="padding:10px 10px" v-show="ResumenV">
                                            <vs-checkbox class="label-size" v-model="Resumen">Resumen</vs-checkbox>
                                        </div>
                                        <br>
                                    </vs-row>
                                    <vs-row class="md:w-full lg:w-full xl:w-9/12" style="padding:20px 50px">
                                        <div class="flex flex-wrap">
                                            <vs-button color="primary" style="float:left;margin: 10px" type="filled" class="label-size" icon-pack="fas" icon="fa-file-pdf" @click="GenerarReporte('application/pdf','PDF')"> Generar Reporte</vs-button>
                                            <vs-button color="success" style="float:left;margin: 10px" type="filled" class="label-size" icon-pack="fas" icon="fa-file-excel" @click="GenerarReporte('application/vnd.ms-excel','EXCEL')" :disabled="Exportar">Exportar</vs-button>
                                        </div>
                                    </vs-row>

                                </div>
                            </div>

                        </vs-row>
                    </vx-card>
                </vs-col>

            </div>
        </div>
    </div>

</vx-card>
</template>

<script>
import Multiselect from 'vue-multiselect'
import "vue-multiselect/dist/vue-multiselect.min.css"

export default {
    components: {
        Multiselect,

    },
    data() {
        return {
            DocPagadosV: true,
            DocPagados: '',
            cbPeriodos: '',
            ListaPeriodos: [],
            cbPeriodosI: '',
            ListaPeriodosI: [],
            CodigoPeriodo: '',
            DescPeriodo: '',
            CodigoPeriodoI: 0,
            DescPeriodoI: '',
            Cuenta: '',
            CuentaV: true,
            Periodo: '',
            cbCuentas: '',
            ListaCuentas: [],
            CodCuenta: '',
            NombreCuenta: '',
            IdReporte: 0,
            ListadoReportes: [],
            ResumenV: true,
            Resumen: false,
            FechaF: '',
            FechaI: ''
        }
    },
    mounted() {
        this.IdReporte = 1
        this.ConsultaPeriodo()
        this.ConsultaPeriodoI()
        this.ConsultaPeridoActual()
        this.ConsultasCuentas()
        this.ListadoReportes = this.$recupera
        this.cbPeriodosI = {
            CodigoPeriodo: 81,
            DescPeriodo: 'Septiembre, 2014'
        }
    },
    watch: {
        'IdReporte'() {
            if (this.IdReporte == '1') {
                this.DocPagadosV = true
                this.cbCuentas = {
                    CodigoCuenta: '31103',
                    Nombre: 'PROVEEDORES'
                }
                this.ResumenV = true
            } else if (this.IdReporte == '2') {
                this.DocPagadosV = false
                this.ResumenV = false
                this.CuentaV = false
            } else if (this.IdReporte == '3') {
                this.DocPagadosV = false
                this.ResumenV = false
                this.CuentaV = true
                this.cbCuentas = {
                    CodigoCuenta: '11201',
                    Nombre: 'CUENTAS POR COBRAR SEGUROS'
                }
            } else if (this.IdReporte == '4') {
                this.DocPagadosV = false
                this.ResumenV = false
                this.CuentaV = true
                this.cbCuentas = {
                    CodigoCuenta: '31106',
                    Nombre: 'INGRESOS POR APLICAR'
                }
            } else if (this.IdReporte == '5') {
                this.DocPagadosV = true
                this.ResumenV = false
                this.CuentaV = false
                this.cbCuentas = ''
            } else if (this.IdReporte == '6') {
                this.DocPagadosV = false
                this.ResumenV = false
                this.CuentaV = true
                this.cbCuentas = {
                    CodigoCuenta: '11201',
                    Nombre: 'CUENTAS POR COBRAR SEGUROS'
                }
            } else if (this.IdReporte == '7') {
                this.DocPagadosV = false
                this.ResumenV = false
                this.CuentaV = true
                this.CuentaV = false
                this.cbCuentas = ''
            }
        },
    },

    methods: {
        //PERIODOS
        ConsultaPeriodo() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/ConsultaPeriodo', {
                    Opcion: 'C',
                    SubOpcion: '6',
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaPeriodos = resp.data.json
                    } else {
                        this.ListaPeriodos = []
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Auxiliar Compras',
                            text: 'No se ha podido cargar los periodos, favor validar.',
                        })
                    }
                })

        },
        ConsultaPeriodoI() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/ConsultaPeriodo', {
                    Opcion: 'C',
                    SubOpcion: '6',
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaPeriodosI = resp.data.json

                    } else {
                        this.ListaPeriodosI = []
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Auxiliar Compras',
                            text: 'No se ha podido cargar los periodos, favor validar.',
                        })
                    }
                })

        },
        ConsultaPeridoActual() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/ConsultaPeriodoActual', {
                    Opcion: 'C',
                    SubOpcion: '2',
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        //this.FechaF = resp.data.json[0].FechaF
                        this.cbPeriodos = {
                            CodigoPeriodo: resp.data.json[0].CodigoPeriodo,
                            DescPeriodo: resp.data.json[0].Descripcion
                        }
                        this.FechaF = resp.data.json[0].FechaFinal
                        this.FechaI = resp.data.json[0].FechaInicial
                    } else {
                        //this.FechaF = ''
                        this.cbPeriodos = []
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Auxiliar Compras',
                            text: 'No se ha podido cargar los periodos, favor validar.',
                        })
                    }
                })
        },
        onChangePeriodos(value) {
            if (value !== null && value.length !== 0) {
                this.CodigoPeriodo = value.CodigoPeriodo
                this.DescPeriodo = value.DescPeriodo
                this.FechaF = value.FechaFinal
                this.FechaI = value.FechaInicial
            } else {
                this.CodigoPeriodo = ''
                this.DescPeriodo = ''
                this.FechaF = ''
                this.FechaI = ''
            }
        },
        onChangePeriodosI(value) {
            if (value !== null && value.length !== 0) {
                this.CodigoPeriodoI = value.CodigoPeriodo
                this.DescPeriodoI = value.DescPeriodo
            } else {
                this.CodigoPeriodoI = ''
                this.DescPeriodoI = ''
            }
        },
        PeriodoSeleccionado({
            CodigoPeriodo,
            DescPeriodo
        }) {
            return `${CodigoPeriodo} - ${DescPeriodo}`
        },
        PeriodoSeleccionadoI({
            CodigoPeriodo,
            DescPeriodo
        }) {
            return `${CodigoPeriodo} - ${DescPeriodo}`
        },
        //Cuentas
        ConsultasCuentas() {
            const url = this.$store.state.global.url
            this.axios.post(url + 'app/v1_contabilidad_general/ConsultasCuentas', {
                    Opcion: 'C',
                    SubOpcion: '1',
                    Activa: 'S'
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.ListaCuentas = resp.data.json
                    } else {
                        this.ListaCuentas = []
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Auxiliar Compras',
                            text: 'No se ha podido cargar las cuentas, favor validar.',
                        })
                    }
                })

        },
        onChangeCuenta(value) {
            if (value !== null && value.length !== 0) {
                this.CodigoCuenta = value.CodigoCuenta
                this.Nombre = value.Nombre
            } else {
                this.CodigoCuenta = ''
                this.Nombre = ''
            }
        },
        CuentaSeleccionado({
            CodigoCuenta,
            Nombre
        }) {
            return `${CodigoCuenta} - ${Nombre} `
        },

        //Reporte 1 
        async GenerarReporte(formatoInterno, formato) {
            let nombreReporte = '';
            let OpcionInterna = this.IdReporte

            if (this.IdReporte == 1) {
                nombreReporte = 'ReporteIntProveedores'
                OpcionInterna = this.IdReporte
            } else if (this.IdReporte == 2) {
                nombreReporte = 'ReporteIntAnticipoProv'
                OpcionInterna = this.IdReporte
            } else if (this.IdReporte == 3) {
                nombreReporte = 'ReporteIntClientes'
                OpcionInterna = this.IdReporte
            } else if (this.IdReporte == 4) {
                nombreReporte = 'ReporteIntAntClientes'
                OpcionInterna = this.IdReporte
            } else if (this.IdReporte == 5) {
                nombreReporte = 'ReporteIntTarjetasCred'
                OpcionInterna = this.IdReporte
            } else if (this.IdReporte == 6) {
                nombreReporte = 'ReporteIntSeguros'
                OpcionInterna = this.IdReporte
            } else if (this.IdReporte == 7) {
                nombreReporte = 'ReporteIntChequesR'
                OpcionInterna = this.IdReporte
            }

            let postData = {
                nombrereporte: nombreReporte,
                tiporeporte: formatoInterno,
                Opcion: 'C',
                SubOpcion: OpcionInterna,
                PeriodoI: this.cbPeriodosI.CodigoPeriodo,
                Periodo: this.cbPeriodos.CodigoPeriodo,
                Cuenta: this.cbCuentas.CodigoCuenta,
                DocPagados: this.DocPagados == true ? 'S' : 'N',
                Resumen: this.Resumen == true ? 'S' : 'N',
                FechaFinal: this.FechaF,
                FechaInicial: this.FechaI

            }
            this.$reporte_modal({
                Nombre: 'ReportesIntegracion/AntigüedadClientes',
                Opciones: {
                    ...postData
                },
                Formato: formato
            })
        },

    }
}
</script>   

<style scoped>
.footer {
    height: 70px;
    margin-top: -65px;
    margin-left: 10px;
}

.label-size {
    font-size: 14px;
    font-weight: bold;
}

.label-StatusM {
    font-size: 14px;
    font-weight: bold;
    color: darkred;
}

.label-i {
    font-size: 18px;
    font-weight: bold;
    color: rgb(240, 158, 6);
}

.cuadro-i {
    background: #faf2f1;
}

.flex-container {
    display: flex;
    flex-direction: row;
}

.padre {
    position: relative;
}

.hijo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
</style>
