<template>
<div class="flex-container">
    <DxDrawer template="listMenu" :close-on-outside-click="false" :opened="true">
        <template #listMenu>
            <div style="width: 200px">
                <DxList :data-source="menu" @item-click="clickOnItem">
                    <template #item={data:item}>
                        <div :class="item.id === SelectedOption ? ['itemSelected', 'dx-template-wrapper', 'dx-item-content', 'dx-list-item-content'] : ['itemNoSelected', 'dx-template-wrapper', 'dx-item-content', 'dx-list-item-content']">
                            <div style="display: flex !important; place-items: center !important">
                                <font-awesome-icon :icon="['fas', item.icon]" style="font-size: 20px !important;" />
                                <span class="ml-2" style="font-size: 14px !important;">{{item.title}}</span>
                            </div>
                        </div>
                    </template>
                </DxList>
            </div>
        </template>
        <div id="content" class="ml-2 borderDiv" width="100%">
            <div v-if="SelectedOption === 1" class="p-2">
                <CuentaAjena ref="CuentaAjena" :TipoDoc="TipoDoc" :TipoCobro="1" :Corporativo="Corporativo" :key="NaN" /> <!-- Se agregó el atributo key para que pueda ser reutilizado el componente de CuenaAjena -->
            </div>
            <div v-if="SelectedOption === 2" class="p-2">
                <CuentaAjena ref="Honorarios" :TipoDoc="TipoDoc" :TipoCobro="2" :Corporativo="Corporativo" :key="NaN" /> <!-- Se agregó el atributo key para que pueda ser reutilizado el componente de CuenaAjena -->
            </div>
            <div v-if="SelectedOption === 3 && TipoDoc == 1" class="p-2">
                <Impresion ref="Impresion" :TipoCheque="'CH'" :ModalidadPago="'C'" :ImpresionLotes="'S'" :Corporativo="Corporativo" />
            </div>
            <div v-if="SelectedOption === 3 && TipoDoc == 2" class="p-2">
                <AcreditamientoLotes ref="AcreditamientoLotes" :TipoCheque="'NH'" :Corporativo="Corporativo" />
            </div>
            <div v-if="SelectedOption === 4" class="p-2">
                <ConsultaAnulacion ref="ConsultaAnulacion" :TipoCheque="TipoDoc == 2 ? 'NH' : 'CH'" :ModalidadPago="'C'" :ImpresionLotes="'S'" :Corporativo="Corporativo" />
            </div>
        </div>
    </DxDrawer>
</div>
</template>

<script>
import CuentaAjena from './CuentaAjena.vue';
import Impresion from './Impresion.vue';
import ConsultaAnulacion from './ConsultaAnulacion.vue';
import AcreditamientoLotes from './AcreditamientoLotes.vue';

export default {
    name: 'GeneracionChequesLotes',
    components: {
        CuentaAjena,
        Impresion,
        ConsultaAnulacion,
        AcreditamientoLotes
    },
    data() {
        return {
            SelectedOption: 1,
            Title: 'Proveedores',

            menu: [{
                    id: 1,
                    title: "Cuenta ajena",
                    icon: "people-arrows",
                },
                {
                    id: 2,
                    title: "Honorarios",
                    icon: "hand-holding-dollar",
                },
                {
                    id: 3,
                    title: "Impresión de cheques",
                    icon: "print",
                },
                {
                    id: 4,
                    title: "Consulta y anulación",
                    icon: "magnifying-glass-minus",
                },
            ],

            tabsAttributes: {
                class: 'tabMenu'
            }
        }
    },
    methods: {
        clickOnItem(e) {
            this.SelectedOption = e.itemData.id
        }
    },
    created() {},
    beforeMount() {
        if (this.TipoDoc == 2) {
            this.menu[2] = {
                id: 3,
                title: "Acreditamiento",
                icon: "money-bill-trend-up",
            }
        }
    },
    mounted() {

    },
    props: {
        TipoDoc: null, // Indica desde qué módulo se invoca el componente  1 = Cheques / 2 = Transferencias 
        Corporativo: null
    },
    watch: {}
}
</script>

<style>
.button {
    height: 40px;
    width: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px !important;
}

.button:hover {
    background-color: blue !important;
}

.div-button {
    display: flex;
    justify-content: center;
}

/* 
    Drawer
*/

.flex-container {
    display: flex;
    flex-direction: column;
}

.itemSelected {
    color: white;
    background-color: #337ab7;
}

.itemNoSelected {
    color: black;
    background-color: transparent;
}

.borderDiv {
    border-style: solid;
    border-color: blueviolet;
}
</style>
