<template>
    <div>
        <vx-card title="Consulta y Anulación de Ingresos de Consignación">
            <vs-row vs-type="flex" vs-w="12">
                <vs-col vs-type="flex" vs-justify="center" vs-align="center" vs-w="12">
                    <vs-input v-model="Parametro" @keyup.enter="CargarIngresos()" @keydown.tab="CargarIngresos()" />
                    <vs-button color="primary" icon-pack="fa" @click="CargarIngresos()" icon="fa-search"></vs-button>
                </vs-col>
            </vs-row>
            <div class="card">
                <vs-table2 max-items="10" tooltip pagination :data="ListIngresos" search>
                    <template slot="thead">
                        <th>Codigo</th>
                        <th>Proveedor</th>
                        <th>Nombre</th>
                        <th>Nit</th>
                        <th>Envio</th>
                        <th>Fecha</th>
                        <th>Bodega</th>
                        <th>Nombre Bodega</th>
                        <th>Validacion</th>
                        <th>Excento</th>
                        <th>Estado</th>
                        <th with="30px">Ver Envio</th>
                        <th with="30px">Ver Detalle</th>
                        <th with="30px">Anular Ingreso</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2>
                                {{tr.Codigo}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.CodigoProveedor}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.Nombre}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.Nit}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.Documento}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.Fecha}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.BodegaDestino}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.NombreBodega}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.Validacion}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.Excento}}
                            </vs-td2>
                            <vs-td2>
                                {{tr.Estado}}
                            </vs-td2>
                            <vs-td2 align="center" noTooltip>
                                <vs-button color="warning" icon-pack="fas" icon="fa-file-invoice" @click="ConsultarReporteIngreso(tr)" class="mr-1" style="display:inline-block">
                                </vs-button>
                            </vs-td2>
                            <vs-td2 align="center" noTooltip>
                                <vs-button color="success" icon-pack="fas" icon="fa-info-circle" @click="ConsultaDetalleIngreso(tr)" class="mr-1" style="display:inline-block">
                                </vs-button>
                            </vs-td2>
                            <vs-td2 align="center" noTooltip>
                                <vs-button v-if="PermisoDevolucion && tr.CodigoEstado!='A' && tr.Tipo != 'O'" color="danger" icon-pack="fas" icon="fa-rectangle-xmark" @click="InformacionConsignacion(tr);activePrompt2=true" class="mr-1" style="display:inline-block">
                                </vs-button>
                                <vs-button v-else color="#B2BABB" icon-pack="fas" icon="fa-rectangle-xmark" class="mr-1" style="display:inline-block">
                                </vs-button>
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
            <div class="card">
                <vs-popup classContent="popup-example" title="Detalle Envío" :active.sync="IsDetalle">
                    <div style="padding:15px; border-radius:5px; box-shadow:0px 15px 60px -10px;margin-bottom:20px">
                        <vs-row vs-type="flex" vs-w="12">
                            <vs-col vs-type="flex" vs-align="center" vs-w="1">
                                <label class="typo__label">Fecha:&nbsp;</label>
                            </vs-col>
                            <vs-col vs-type="flex" vs-align="center" vs-w="7">
                                <vs-input v-model="Info.Fecha" disabled />
                            </vs-col>
                            <vs-col vs-type="flex" vs-align="center" vs-w="2">
                                <label class="typo__label">Pedido Origen:&nbsp;</label>
                            </vs-col>
                            <vs-col vs-type="flex" vs-align="center" vs-w="2">
                                <h6>{{ Info.Pedido }}</h6>
                            </vs-col>
                        </vs-row>
                        <br>
                        <vs-row vs-type="flex" vs-w="12">
                            <vs-col vs-type="flex" vs-align="center" vs-w="1">
                                <label class="typo__label">Tipo:&nbsp;</label>
                            </vs-col>
                            <vs-col vs-type="flex" vs-align="center" vs-w="5">
                                <vs-input v-model="Info.Tipo" disabled />
                                <h6>&nbsp;Salida Consignacion Proveedor</h6>
                            </vs-col>
                            <vs-col vs-type="flex" vs-align="center" vs-w="6">
                                <h6>&nbsp;{{  Info.Descripcion }}</h6>
                            </vs-col>
                        </vs-row>
                        <br>
                        <vs-row vs-type="flex" vs-w="12">
                            <vs-col vs-type="flex" vs-align="center" vs-w="1">
                                <label class="typo__label">Bodega:&nbsp;</label>
                            </vs-col>
                            <vs-col vs-type="flex" vs-align="center" vs-w="7">
                                <vs-input v-model="Info.CodigoBodega" disabled />
                                <h6>&nbsp;{{  Info.NombreBodega }}</h6>
                            </vs-col>
                            <vs-col vs-type="flex" vs-align="center" vs-w="2">
                                Impuesto:&nbsp;<h6>{{ Info.Excento }}</h6>
                            </vs-col>
                            <vs-col vs-type="flex" vs-align="center" vs-w="2">
                                Estado:&nbsp;<h6>{{ Info.Estado }}</h6>
                            </vs-col>
                        </vs-row>
                        <br>
                        <vs-row vs-type="flex" vs-w="12">
                            <vs-col vs-type="flex" vs-align="center" vs-w="1">
                                <label class="typo__label">Proveedor:&nbsp;</label>
                            </vs-col>
                            <vs-col vs-type="flex" vs-align="center" vs-w="7">
                                <vs-input v-model="Info.CodigoProveedor" disabled />
                                <h6>&nbsp;{{  Info.NombreProveedor }}</h6>
                            </vs-col>
                            <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                Nit:&nbsp;<h6>{{ Info.Nit }}</h6>
                            </vs-col>
                        </vs-row>
                        <br>
                        <vs-row vs-type="flex" vs-w="12">
                            <vs-col vs-type="flex" vs-align="center" vs-w="1">
                                <label class="typo__label">Envío:&nbsp;</label>
                            </vs-col>
                            <vs-col vs-type="flex" vs-align="center" vs-w="3">
                                <vs-input v-model="Info.Envio" disabled />
                            </vs-col>
                            <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                Validación:&nbsp;<h6>{{ Info.Validacion }}</h6>
                            </vs-col>
                            <vs-col vs-type="flex" vs-align="center" vs-w="4">
                                Correlativo:&nbsp;<h6>{{ Info.Correlativo }}</h6>
                            </vs-col>
                        </vs-row>
                        <br>
                        <vs-row vs-type="flex" vs-align="center" vs-w="12">
                            <vs-col vs-type="flex" vs-align="center" vs-w="1">
                                <label class="typo__label">Fecha de Envío:&nbsp;</label>
                            </vs-col>
                            <vs-col vs-type="flex" vs-align="center" vs-w="3">
                                <h6>{{ Info.FechaEnvio }}</h6>
                            </vs-col>
                            <vs-col vs-type="flex" vs-align="center" vs-w="6">
                                Fecha de Ingreso:&nbsp;<h5>{{ Info.Fecha }}</h5>
                            </vs-col>
                        </vs-row>
                        <br>
                        <vs-row>
                            <vs-col vs-type="flex" vs-align="center" vs-justify="center" vs-w="12">
                                <vs-table2 class="w-full" max-items="10" tooltip pagination :data="DetalleIngreso">
                                    <template slot="thead">
                                        <th order="Linea">Linea</th>
                                        <th order="Producto">Producto</th>
                                        <th order="Nombre">Nombre</th>
                                        <th order="UnidadMedida">Unidad Medida</th>
                                        <th order="CantidadPedida">Cantidad Pedida</th>
                                        <th order="Cantidad">Cantidad</th>
                                        <th order="CantidadDevuelta">Cantidad Devuelta</th>
                                        <th order="ValorUnitarioDetalle">Valor Unitario Detalle</th>
                                        <th order="Total">Total</th>
                                    </template>
                                    <template slot-scope="{data}">
                                        <tr :key="indextr" v-for="(tr, indextr) in data">
                                            <vs-td2 noTooltip>
                                                {{ tr.Linea }}
                                            </vs-td2>
                                            <vs-td2 noTooltip>
                                                {{ tr.Producto }}
                                            </vs-td2>
                                            <vs-td2 >
                                                {{ tr.Nombre }}
                                            </vs-td2>
                                            <vs-td2>
                                                {{ tr.UnidadMedida }}
                                            </vs-td2>
                                            <vs-td2 noTooltip>
                                                {{ tr.CantidadPedida }}
                                            </vs-td2>
                                            <vs-td2 noTooltip>
                                                {{ tr.Cantidad }}
                                            </vs-td2>
                                            <vs-td2 noTooltip>
                                                {{ tr.CantidadDevuelta }}
                                            </vs-td2>
                                            <vs-td2 >
                                                {{ $formato_moneda(tr.ValorUnitarioDetalle) }}
                                            </vs-td2>
                                            <vs-td2 >
                                                {{ $formato_moneda(tr.Total) }}
                                            </vs-td2>
                                        </tr>
                                    </template>
                                </vs-table2>

                            </vs-col>
                        </vs-row>
                    </div>
                </vs-popup>
            </div>
        </vx-card>
        <div div class="con-content">
            <vs-prompt
                title = "Anulación Consignación"
                color="danger"
                cancel-text="Cancelar" accept-text="Grabar" button-cancel="border"
                @cancel="ObservacionAnulacion=''"
                @accept="acceptAlert"
                @close="close"
                :is-valid="validName"
                :active.sync="activePrompt2">
                <div class="con-exemple-prompt w-flex">
                    <label class="typo__label">Observación:&nbsp;</label>
                    <vs-textarea v-model="ObservacionAnulacion" rows="5" counter="240"/>
                    <vs-alert :active="!validName" color="danger" icon="new_releases" >
                        El campo no puede ir vacío, ingreso un motivo valido.
                    </vs-alert>
                </div>
            </vs-prompt>
        </div>
    </div>
</template>
<script>
    export default {
        data(){
            return{
                Parametro: '',
                ListIngresos: [],
                Info: {
                    Fecha: '',
                    Pedido: '',
                    Tipo: '20',
                    Descripcion: '',
                    CodigoBodega: '',
                    NombreBodega: '',
                    Excento: '',
                    Estado: '',
                    CodigoProveedor: '',
                    NombreProveedor: '',
                    Nit: '',
                    Envio: '',
                    Validacion: '',
                    Correlativo: '',
                    FechaEnvio: '',
                    CodigoAgrupacion: ''
                },
                DetalleIngreso:[],
                IsDetalle: false,
                PermisoDevolucion: false,
                activePrompt2:false,
                ObservacionAnulacion: ''
            }
        },
        mounted(){
            this.PermisoDevolucion = this.$validar_privilegio('DEVOLUCION').status
            this.CargarIngresos()
        },
        computed:{
            validName(){
                return (this.ObservacionAnulacion.length > 0)
            }
        },
        methods: {
            CargarIngresos(){
                this.axios.post('/app/v1_OrdenCompra/ConsultaIngresosConsignacion', {
                    Parametro: this.Parametro,
                })
                .then(resp => {
                    if(resp.data.codigo == 0){
                        this.ListIngresos = resp.data.json
                    }
                })
            },
            ConsultarReporteIngreso(data){
                this.$reporte_modal({
                    Nombre: "Envio Consignacion",
                    Opciones: {
                        Envio: data.Codigo
                    },
                    Formato: "PDF"
                })
            },
            ConsultaDetalleIngreso(data){
                this.Info.Fecha = data.Fecha
                this.Info.Pedido = data.Pedido
                this.Info.Descripcion = data.Descripcion
                this.Info.CodigoBodega = data.BodegaDestino
                this.Info.NombreBodega = data.NombreBodega
                this.Info.Excento = data.Excento
                this.Info.Estado = data.Estado
                this.Info.CodigoProveedor = data.CodigoProveedor
                this.Info.NombreProveedor = data.Nombre
                this.Info.Nit = data.Nit
                this.Info.Envio = data.Documento
                this.Info.Validacion = data.Validacion
                this.Info.Correlativo = data.Codigo
                this.Info.FechaEnvio = data.FechaEnvio,
                this.Info.Tipo = 20

                this.axios.post('/app/v1_OrdenCompra/ConsultaDetalleIngresosConsignacion', {
                    NoEnvio: data.Codigo,
                })
                .then(resp => {
                    if(resp.data.codigo == 0){
                        this.DetalleIngreso = resp.data.json
                        this.IsDetalle = true
                    }
                })
            },
            InformacionConsignacion(data){
                this.Info.Fecha = data.Fecha
                this.Info.Pedido = data.Pedido
                this.Info.Descripcion = data.Descripcion
                this.Info.CodigoBodega = data.BodegaDestino
                this.Info.NombreBodega = data.NombreBodega
                this.Info.Excento = data.Excento
                this.Info.Estado = data.Estado
                this.Info.CodigoProveedor = data.CodigoProveedor
                this.Info.NombreProveedor = data.Nombre
                this.Info.Nit = data.Nit
                this.Info.Envio = data.Documento
                this.Info.Validacion = data.Validacion
                this.Info.Correlativo = data.Codigo
                this.Info.FechaEnvio = data.FechaEnvio
                this.Info.CodigoAgrupacion = data.CodigoAgrupacion,
                this.Info.Tipo = 20
            },
            acceptAlert(){
                this.axios.post('/app/v1_OrdenCompra/DevolucionIngresosConsignacion', {
                    CodigoBodega: this.Info.CodigoBodega,
                    Proveedor: this.Info.CodigoProveedor,
                    Observacion: this.ObservacionAnulacion,
                    CodigoAgrupacion: this.Info.CodigoAgrupacion,
                    NoEnvio: this.Info.Correlativo,
                    NoPedido: this.Info.Pedido,
                    NoMovimientos: this.Info.Validacion,
                    NoDocumento: this.Info.Envio
                })
                .then(resp => {
                    if(resp.data.codigo == 0){
                        this.ObservacionAnulacion = '',
                        this.Limpiar()
                        this.CargarIngresos()
                    }
                })
                
            },
            close(){
                this.$vs.notify({
                    color:'danger',
                    title:'Closed',
                    text:'You close a dialog!'
                })
            },
            Limpiar(){
                this.ObservacionAnulacion = '',
                this.Info = {}
                this.Info.Tipo = 20
            }
        }
    }
</script>
<style lang="scss" scoped>
    .con-exemple-prompt{
        padding: 10px;
        padding-bottom: 0px;
    }
</style>