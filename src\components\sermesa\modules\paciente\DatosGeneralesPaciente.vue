<template>
    <ValidationObserver ref="personalData" v-slot="{ invalid, handleSubmit }" mode="lazy">
        <form @submit.prevent="handleSubmit(submitForm(submit))">

            <!-- <div class="tab-text"> -->
            <div class="container pl-4">

                <div class="datos_personales">

                    <vs-row>

                        <vs-col vs-type="flex" vs-w="6">
                            <div class="w-2/3  ">
                                <validationProvider name="Primer Nombre Paciente" class="required"
                                    rules="required|min:1|max:15" v-slot="{ errors }">
                                    <vs-input label="Primer Nombre Paciente" class="w-full  code"
                                        v-model.Trim="PacienteData.PrimerNombre" :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" :disabled="!editable" />
                                </validationProvider>

                            </div>
                        </vs-col>

                        <vs-col vs-type="flex" vs-w="6">
                            <div class="w-2/3  ">
                                <vs-input label="Segundo Nombre Paciente" class="w-full "
                                    v-model="PacienteData.SegundoNombre" :disabled="!editable" />
                            </div>
                        </vs-col>
                    </vs-row>

                    <vs-row>
                        <vs-col vs-type="flex" vs-w="6">
                            <div class="w-2/3  ">
                                <validationProvider name="Primer Apellido Paciente" class="required"
                                    rules="required|min:1|max:15" v-slot="{ errors }">
                                    <vs-input label="Primer Apellido Paciente" class="w-full  code"
                                        v-model="PacienteData.PrimerApellido" :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" :disabled="!editable" />
                                </validationProvider>
                            </div>
                        </vs-col>

                        <vs-col vs-type="flex" vs-w="6">
                            <div class="w-2/3  ">
                                <vs-input label="Segundo Apellido Paciente" class="w-full "
                                    v-model="PacienteData.SegundoApellido" :disabled="!editable" />
                            </div>
                        </vs-col>
                    </vs-row>
                    <vs-row>
                        <vs-col vs-type="flex" vs-w="6">
                            <div class="w-2/3  ">
                                <validationProvider name="Apellido Casada">
                                    <vs-input label="Apellido Casada" class="w-full "
                                        v-model="PacienteData.ApellidoCasada" />
                                </validationProvider>
                            </div>
                        </vs-col>

                        <vs-col vs-type="flex" vs-w="6">
                            <div class="w-2/3  ">
                                <validationProvider name="Fecha de Nacimiento" class="required" rules="required|min:10"
                                    v-slot="{ errors }">
                                    <vs-input label="Fecha de Nacimiento" class="w-full " type="date"
                                        v-model="PacienteData.Nacimiento" :danger="errors.length > 0"
                                        :danger-text="(errors.length > 0) ? errors[0] : null" :maxlength="20" />
                                </validationProvider>
                            </div>
                        </vs-col>

                    </vs-row>



                    <vs-row>
                        <vs-col vs-type="flex" vs-w="6">
                            <div class="w-2/3  ">
                                <validationProvider name="Sexo" class="required" rules="required" v-slot="{ errors }">
                                    <label class="vs-input--label">Sexo </label>
                                    <multiselect v-model="SelSexo" :options="Sexos" :disabled="false" :allow-empty="false"
                                        :taggable="true" placeholder="Selección de Sexo" track-by="Nombre" label="Nombre">
                                    </multiselect>
                                    <span class="error-msg">{{ errors[0] }}</span>
                                </validationProvider>

                            </div>
                        </vs-col>

                        <vs-col vs-type="flex" vs-w="6">
                            <div class="w-2/3  ">
                                <validationProvider name="SelEstadoCivil" class="required" rules="required"
                                    v-slot="{ errors }">
                                    <label class="vs-input--label">Estado Civil </label>
                                    <multiselect v-model="SelEstadoCivil" :options="EstadosCiviles" :disabled="false"
                                        :allow-empty="false" :taggable="true" placeholder="Selección de Estado Civil"
                                        track-by="Nombre" label="Nombre">
                                    </multiselect>
                                    <span class="error-msg">{{ errors[0] }}</span>
                                </validationProvider>
                            </div>
                        </vs-col>
                    </vs-row>

                    <vs-row>
                        <vs-col vs-type="flex" vs-w="6">
                            <div class="w-2/3  ">
                                <validationProvider name="Nacionalidad">
                                    <vs-input label="Nacionalidad" class="w-full "
                                        v-model.trim="PacienteData.Nacionalidad" />
                                </validationProvider>
                            </div>
                        </vs-col>

                        <vs-col vs-type="flex" vs-w="6">
                            <div class="w-2/3  ">
                                <vs-input label="Profesión/Oficio" class="w-full " v-model.trim="PacienteData.Profesion" />

                            </div>
                        </vs-col>
                    </vs-row>

                </div>


                <div class="documentos">

                    <vs-row>
                        <vs-col vs-type="flex" vs-w="10">
                            <div class="w-full  ml-2">
                                <h6>Documentos de Identificación</h6>
                                <hr style="height: 2px; border: 0; box-shadow: inset 0 12px 12px -12px rgba(0, 0, 0, 0.5)">
                            </div>
                        </vs-col>
                    </vs-row>

                    <vs-row>
                        <vs-col vs-type="flex" class="w-full md:w-full lg:w-2/12 xl:w-2/12 m-1 mr-6">
                            <validationProvider name="DPI">
                                <vs-input label="DPI" class="w-full " v-model="PacienteData.DPI" />
                            </validationProvider>
                        </vs-col>

                        <vs-col vs-type="flex" class="w-full md:w-full lg:w-2/12 xl:w-2/12 m-1 mr-6">

                            <validationProvider name="NIT">
                                <vs-input label="NIT" class="w-full " v-model="PacienteData.Nit" />
                            </validationProvider>

                        </vs-col>

                        <vs-col vs-type="flex" class="w-full md:w-full lg:w-2/12 xl:w-2/12 m-1 mr-6">
                            <validationProvider name="Pasaporte">
                                <vs-input label="Pasaporte" class="w-full " v-model="PacienteData.Pasaporte" />
                            </validationProvider>

                        </vs-col>
                    </vs-row>

                </div>

                <div class="contacto">
                    <vs-row>
                        <vs-col vs-type="flex" vs-w="8">
                            <div class="w-full pt-2  ml-2">
                                <h6>Datos de contacto</h6>
                                <hr style="height: 2px; border: 0; box-shadow: inset 0 12px 12px -12px rgba(0, 0, 0, 0.5)">
                            </div>
                        </vs-col>
                    </vs-row>

                    <vs-row>

                        <vs-col vs-type="flex" vs-w="4">
                            <div class="w-1/2  ">
                                <validationProvider name="celular">
                                    <vs-input label="Teléfono móvil" class="w-full " v-model="PacienteData.Celular" />
                                </validationProvider>
                            </div>
                        </vs-col>

                        <vs-col vs-type="flex" vs-w="4">
                            <div class="w-2/3  ">
                                <validationProvider name="eMail" rules="email" v-slot="{ errors }">
                                    <vs-input
                                      v-bind:readonly="noCorreoElectronico"
                                      label="Correo Electrónico"
                                      class="w-full "
                                      v-model="PacienteData.Email"
                                      :danger="errors.length>0"
                                      :danger-text="(errors.length>0)?errors[0]:null" 
                                    />
                                </validationProvider>
                            </div>

                            <div class="w-1/3 mt-6 pl-4">
                                <BaseCheckboxGroup :options="checkboxOptions" v-model="optionChecked"
                                    :groupName="'correoMedico'">
                                </BaseCheckboxGroup>
                            </div>
                        </vs-col>

                    </vs-row>



                </div>


                <!----------------------------------------------------------------------------------------------------------------------------------->
                <!----------------------------------------------PACIENTE MENOR DE EDAD      -------------------------------------------------------->

                <div class="menor_edad">

                    <vs-row>
                        <vs-col vs-type="flex" vs-w="10">
                            <div class="w-full  pt-2 ml-2">
                                <h6>Datos Persona Responsable (Menor de edad)</h6>
                                <hr style="height: 2px; border: 0; box-shadow: inset 0 12px 12px -12px rgba(0, 0, 0, 0.5)">
                            </div>
                        </vs-col>
                    </vs-row>

                    <vs-row class="pt-2">
                        <vs-col vs-type="flex" vs-w="5">
                            <div class="w-2/3  ">
                                <validationProvider name="Nombre Padre">
                                    <vs-input label="Nombre del Padre" class="w-full " v-model="PacienteData.Padre" />
                                </validationProvider>
                            </div>
                        </vs-col>

                        <vs-col vs-type="flex" vs-w="3">
                            <div class="w-2/3  ">
                                <validationProvider name="DPI">
                                    <vs-input label="DPI del Padre" class="w-full "
                                        v-model="PacienteData.DPIPadreResponsable" />
                                </validationProvider>
                            </div>
                        </vs-col>

                        <vs-col vs-type="flex" vs-w="2">
                            <div class="w-2/3  ">
                                <validationProvider name="Telefono">
                                    <vs-input label="Tel. Padre" class="w-full "
                                        v-model="PacienteData.TelefonoPadreResponsable" />
                                </validationProvider>
                            </div>
                        </vs-col>
                    </vs-row>

                    <vs-row>
                        <vs-col vs-type="flex" vs-w="5">
                            <div class="w-2/3  ">
                                <validationProvider name="Nombre Madre">
                                    <vs-input label="Nombre de la Madre" class="w-full " v-model="PacienteData.Madre" />
                                </validationProvider>
                            </div>
                        </vs-col>

                        <vs-col vs-type="flex" vs-w="3">
                            <div class="w-2/3  ">
                                <validationProvider name="DPI">
                                    <vs-input label="DPI de la Madre" class="w-full "
                                        v-model="PacienteData.DPIMadreResponsable" />
                                </validationProvider>
                            </div>
                        </vs-col>

                        <vs-col vs-type="flex" vs-w="2">
                            <div class="w-2/3  ">
                                <validationProvider name="Telefono">
                                    <vs-input label="Tel. Madre" class="w-full "
                                        v-model="PacienteData.TelefonoMadreResponsable" />
                                </validationProvider>
                            </div>
                        </vs-col>
                    </vs-row>



                </div>

            </div>
            <div v-show="showButtons">
                <vs-button style="margin-right:1px" v-on:click="handleSubmit(submit())" :disabled="invalid"
                    :clientWidth="20">
                    Guardar
                </vs-button>
            </div>


        </form>
    </ValidationObserver>
    <!-- </vx-card> -->
</template>
  
<script>


import BaseCheckboxGroup from "@/components/sermesa/global/checkbox/BaseCheckboxGroup";
import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";
import { mapFields } from "vuex-map-fields";

import moment from "moment";
import validador_dpi_nit from '@/components/sermesa/funciones/SMVFormatsDocs'


export default {
    props: ['renderComp'],
    name: "DatosGeneralesPaciente",
    data: () => ({
        activeStep: 0,
        NumeroDocumento: null,
        SelSexo: '',
        SelEstadoCivil: '',
        SelTipoDocumento: '',
        NombrePaciente: '',
        showButtons: false,
        checkboxOptions: [],
        optionChecked: [],
        noCorreoElectronico: false,
        editable: Boolean,
        Sexos: [
            {
                Codigo: 'M',
                Nombre: 'MASCULINO'
            },
            {
                Codigo: 'F',
                Nombre: 'FEMENINO'
            }
        ],
        EstadosCiviles: [
            {
                Codigo: 'C',
                Nombre: 'CASADO'
            },
            {
                Codigo: 'S',
                Nombre: 'SOLTERO'
            }
        ],
        TiposDocumento: [
            {
                Codigo: 'D',
                Nombre: 'DPI'
            },
            {
                Codigo: 'P',
                Nombre: 'PASAPORTE'
            }
        ]
    }),
    components: {
        BaseCheckboxGroup,
        Multiselect
    },
    mounted() {
        this.CargarOpcionCorreo()
        const sexoPaciente = this.Sexos.find(option => option.Codigo === this.PacienteData.Sexo)
        const estadoCivilPac = this.EstadosCiviles.find(option => option.Codigo === this.PacienteData.EstadoCivil)
        this.SelSexo = sexoPaciente === undefined ? '' : sexoPaciente
        this.SelEstadoCivil = estadoCivilPac === undefined ? '' : estadoCivilPac
        // eslint-disable-next-line no-constant-condition
        this.PacienteData.Nacionalidad = isNaN(this.PacienteData.Nacionalidad) ? this.PacienteData.Nacionalidad.toUpperCase() : "GUATEMALTECO(A)"
        this.PacienteData.Profesion = isNaN(this.PacienteData.Profesion) ? this.PacienteData.Profesion.toUpperCase() : ""
        this.editable = this.isDisabled

    },
    computed: {
        ...mapFields('paciente', ["PacienteData"]),
        isDisabled() {
            return (this.$store.getters['workflow/getTipoPaso'] === 'CREATE' || this.$store.getters['workflow/getTipoPaso'] === '' ? true : false);
        }

    },
    created() {


        this.PacienteData.Direccion = 'CIUDAD'
        this.PacienteData.DireccionFactura = 'CIUDAD'
        this.PacienteData.Telefonos = '502'

    },
    activated() {

        this.PacienteData.Nacimiento = this.getDateValue(this.PacienteData.Nacimiento)
        this.PacienteData.FechaPrimeraVisita = this.getDateValue(this.PacienteData.FechaPrimeraVisita)
        this.PacienteData.FechaUltimaVisita = this.getDateValue(this.PacienteData.FechaUltimaVisita)
        this.PacienteData.FechaPrimeraConsulta = this.getDateValue(this.PacienteData.FechaPrimeraConsulta)
        this.PacienteData.UltimaAdmision = this.getDateValue(this.PacienteData.UltimaAdmision)
        this.PacienteData.FechaPrimeraConsulta = this.getDateValue(this.PacienteData.FechaPrimeraConsulta)

    },
    watch: {
        'optionChecked': function (val) {
            this.noCorreoElectronico = val.length > 0 ? true : false
            this.PacienteData.Email = ''
        },
        isDisabled(newValue) {
            this.editable = newValue
        },
        'PacienteData': function (val) {
            this.SelSexo = this.Sexos.find(option => option.Codigo == val.Sexo) ?? ''
            this.SelEstadoCivil = this.EstadosCiviles.find(option => option.Codigo == val.EstadoCivil) ?? ''
        }

    },
    methods: {
        CargarOpcionCorreo() {
            this.checkboxOptions = [
                {
                    label: 'No posee correo',
                    value: '1',
                }
            ]
        },
        getionarDatos() {

            return new Promise((resolve) => {
                // Lógica de guardado del cliente aquí
                // Resuelve la promesa si el guardado es exitoso
                return resolve(true);
                // Rechaza la promesa si hay un error en el guardado
                // reject('Mensaje de error aquí');
            });
        },
        validacionFormulario() {
            return new Promise((resolve, reject) => {
                this.$refs.personalData.validate()
                    .then((isValid) => {
                        if (isValid) {
                            resolve(true);
                        } else {

                            reject('Revisar campos requeridos');
                        }
                    })
                    .catch((error) => {
                        reject(error);
                    });
            });
        },
        gestionarPaciente() {
            return new Promise((resolve, reject) => {
                this.$store.dispatch('paciente/crearPaciente')
                    .then((response) => {
                        this.$emit("mensaje-respuesta", response);
                        resolve(response);
                    })
                    .catch((error) => {
                        reject(error);
                    });
            });
        },

        async submit() {

            this.PacienteData.Sexo = this.SelSexo.Codigo
            this.PacienteData.EstadoCivil = this.SelEstadoCivil.Codigo

            this.PacienteData.NombreFactura = this.PacienteData.PrimerNombre + ' ' + this.PacienteData.SegundoNombre + ' ' +
                                              this.PacienteData.PrimerApellido + ' ' + this.PacienteData.SegundoApellido
            this.PacienteData.DireccionFactura = "CIUDAD"


            if (this.PacienteData.DPI)
                if (!validador_dpi_nit.cuiValido(this.PacienteData.DPI)) {
                    this.$vs.dialog({
                        type: 'confirm',
                        color: '#ED8C72',
                        acceptText: 'Continuar',
                        title: 'Error en gestión de Paciente',
                        text: 'Formato DPI inválido',
                        clientWidth: 100,
                        accept: () => {
                        }
                    });
                    return
                }

            this.getionarDatos()
                .then(() => {
                    return this.validacionFormulario();
                })
                .then(() => {
                    return this.gestionarPaciente();
                })
                .then(() => {
                    this.$emit("ready", true);
                })
                .catch((error) => {
                    this.$vs.dialog({
                        type: 'confirm',
                        color: '#ED8C72',
                        acceptText: 'Continuar',
                        title: 'Error en gestión de Paciente',
                        text: error,
                        clientWidth: 100,
                        accept: () => {

                            this.$emit("ready", false);

                        }
                    });
                })

        },
        getDateValue(value) {
            if (value === undefined) {
                return '';
            } else if (value === null) {
                return '';
            } else if (value == '') {
                return '';
            }
            return moment(value).format('YYYY-MM-DD');
        },
        nextStep() {
            if (this.isLastStep) {
                return this.submit();
            }

            this.activeStep++;
        }
    }
};


</script>
  
  
<style scoped>
* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

.container {
    display: grid;
    width: 100%;
    height: 100%;
    border: 1px solid #6a6767;
    grid-template-areas: "datos_personales datos_personales"
        "documentos contacto"
        "menor_edad menor_edad";
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 350px 100px 180px;
}

.container>div {}

.datos_personales {

    grid-area: datos_personales;
}

.documentos {
    grid-area: documentos;
}

.contacto {
    grid-area: contacto;
}

.menor_edad {
    grid-area: menor_edad;
}

.form {
    margin-top: 20px;
}

span {
    display: block;
    margin-top: 1px;
}


span {
    display: block;
    margin-top: 3px;
}

.container {
    max-width: 100%;
}

span.error-msg {
    color: red;
    margin-top: 5px;
}
</style>
  