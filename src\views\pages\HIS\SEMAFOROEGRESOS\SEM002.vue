<template>
  <div class="sem002-container w-full pr-2">
    <vx-card>
      <vs-row>
        <DxForm :form-data="formData" label-mode="static" width="100%">
          <DxFormGroupItem :col-span="2" :col-count="8">
            <DxFormGroupItem :col-span="2" :col-count="3" caption="Área">
              <!-- <DxFormItem data-field="Hospital" editor-type="dxSelectBox"></DxFormItem> -->
              <DxFormItem
               :col-span="2"
                :editor-options="{
                  items: dsAreas,
                  displayExpr: 'Servicio',
                  valueExpr: 'Servicio',
                  searchEnabled: 'true',
                }"
                data-field="Servicio"
                editor-type="dxSelectBox"
              >
              </DxFormItem>
              <DxFormItem
      data-field="Ultima Actualización"
      editor-type="dxTextBox"
      :editor-options="{ value: currentTime }"
    >
    </DxFormItem>
            </DxFormGroupItem>
          </DxFormGroupItem>

          <DxFormGroupItem :col-count="10">
            <DxFormItem
              item-type="button"
              :button-options="buttonGrabarActualizar"
            ></DxFormItem>
            <DxFormItem
              item-type="button"
              :button-options="buttonGrabarReCalcular"
            ></DxFormItem>
          </DxFormGroupItem>
        </DxForm>
      </vs-row>
    </vx-card>
    <vx-card>
      <DxDataGrid
        ref="dataGrid"
        id="gridlistadoadmisiones"
        :data-source="dsListadoAdmisiones"
        @selectionChanged="onSelectionChanged"
        :no-data-text="'Sin registros'"
        height="auto"
        width="auto"
        :headerFilter="{ visible: true, allowSearch: true }"
        :row-alternation-enabled="true"
        :hoverStateEnabled="true"
        
        
        :cellHintEnabled="true"
      >
        <DxDataGridDxSearchPanel :visible="true" width="400px" />
        <DxDataGridGroupPanel :visible="true" expanded="true" />
        <DxDataGridSelection mode="single" />

        <DxDataGridToolbar>
          <DxFormItem name="groupPanel" locateInMenu="auto" />
          <DxFormItem name="searchPanel" locateInMenu="auto" />
        </DxDataGridToolbar>

        <DxDataGridPaging :page-size="10" />
        <DxDataGridPager :visible="true" />
        <DxDataGridColumn data-field="serie" caption="Serie" :width="80"></DxDataGridColumn>
        <DxDataGridColumn
          data-field="Admision"
          caption="Admisión"
          width="auto"
          dataType="string"
        ></DxDataGridColumn>
        <DxDataGridColumn
          data-field="statusadmision"
          caption="Status Admisión"
          width="auto"
        ></DxDataGridColumn>
        <DxDataGridColumn
          data-field="fechaegreso"
          caption="Fecha Egreso"
          width="auto"
           format="dd/MM/yyyy HH:mm"
           data-type="date"
        ></DxDataGridColumn>
        <DxDataGridColumn
          data-field="Paciente"
          caption="Paciente"
          width="auto"
        ></DxDataGridColumn>
        <DxDataGridColumn
          data-field="NombreSeguro"
          caption="Nombre Seguro"
          width="auto"
        ></DxDataGridColumn>
        <DxDataGridColumn
          data-field="NombreAseguradora"
          caption="Nombre Aseguradora"
          width="auto"
        ></DxDataGridColumn>
        <DxDataGridColumn
          data-field="Planxcontrato"
          caption="Plan x Contrato"
          width="auto"
        ></DxDataGridColumn>
      </DxDataGrid>

    </vx-card>
    <dx-popup
      :visible.sync="popupCalculoCobro"
      :show-title="true"
      title="Resumen para cobro"
      width="auto"
      max-width="100%"
      height="auto"
      max-height="100%"
      :wrapperAttr="{ class: 'fm-super-popup' }"
    >
      <table style="width: 100%; table-layout: auto">
        <tr>
          <th class="titulo-th" colspan="2">
            Desglose de cobro admisión {{ this.formData.Admision }}
          </th>
        </tr>
        <tr>
          <td style="white-space: nowrap">Copago</td>
          <td>{{ formatCurrency(formData.copago) }}</td>
        </tr>
        <tr>
          <td style="white-space: nowrap">Coaseguro</td>
          <td>{{ formatCurrency(formData.coaseguro) }}</td>
        </tr>
        <tr>
          <td style="white-space: nowrap">Deducible</td>
          <td>{{ formatCurrency(formData.deducible) }}</td>
        </tr>
        <tr>
          <td style="white-space: nowrap">Gastos especiales no cubiertos</td>
          <td>{{ formatCurrency(formData.gastosEspeciales) }}</td>
        </tr>
        <tr>
          <td style="white-space: nowrap">Extraordinarios</td>
          <td>{{ formatCurrency(formData.extraordinarios) }}</td>
        </tr>
        <tr>
          <td style="white-space: nowrap">Cuenta ajena no elegible</td>
          <td>{{ formatCurrency(formData.ctaAjeno) }}</td>
        </tr>
        <tr>
          <td style="white-space: nowrap">Facturado o cobrado en cuenta</td>
          <td :class="{ rojo: isPositive(formData.Facturado) }">
            {{ formatCurrency(formData.Facturado) }}
          </td>
        </tr>
        <tr>
          <td style="white-space: nowrap">
            Descuento {{ this.formData.nombreDescuento }}
          </td>
          <td :class="{ rojo: isPositive(formData.totalDescuento) }">
            {{ formatCurrency(formData.totalDescuento) }}
          </td>
        </tr>
        <tr>
          <td style="white-space: nowrap">Total a cobrar</td>
          <td style="font-weight: bold">
            --{{ formatCurrency(formData.totalCobrar) }}--
          </td>
        </tr>
      </table>
      <p style="text-align: center">
        <strong>{{ "--" + this.formData.observaciones + "--" }}</strong>
      </p>
      <div style="margin-top: 20px; display: flex; justify-content: center">
        <div class="btn-group pl-4">
          <vs-button
            class="w-full"
            color="success"
            type="filled"
            icon-pack="fas"
            icon="fa-save"
            @click="InsertarCalculoCobro()"
            title="Graba el dato para cobro con los datos generados en pantalla"
          >
            Guardar
          </vs-button>
        </div>
      </div>
    </dx-popup>
    <div class="flex flex-wrap">
      <dx-popup
        :visible.sync="popupRecalculo"
        :show-title="true"
        title="Recálculo de cuentas"
        :width="'90%'"
        :height="'90%'"
        class="custom-popup"
        :hide-on-outside-click="true"
        @shown="EventoPopupShown"
        :resizeEnabled="true"
        :wrapperAttr="{ class: 'fm-super-popup' }"
      >
        <DxScrollView width="100%" height="100%">
          <RecalculoCuenta ref="refRecalculo" />
        </DxScrollView>
      </dx-popup>
    </div>

    <div class="flex-container">
      <!-- w-full md:w-1/2 lg:w-1/2 xl:w-1/2 -->
      <div class="pl-4 pb-4 w-full sm:w-1/2 md:w-1/2 lg:w-19/50 xl:w-19/50">
        <dx-popup
          :visible.sync="popupCalculo"
          :show-title="true"
          title="Cálculo de cuentas"
          :width="'80%'"
          :height="'90%'"
          class="custom-popup"
          :hide-on-outside-click="true"
          @hidden="OctultadoPopup"
          :resizeEnabled="true"
           :wrapperAttr="{ class: 'fm-super-popup' }"
        >
          <DxScrollView width="100%" height="100%">
            <CalculoForm
              ref="refCalculoForm"
              @calculo-cobro-insertado="eliminarRegistroGrid"
            />
          </DxScrollView>
        </dx-popup>
      </div>
    </div>

    <SM-Validar-Pass
      ref="refValidarPass"
      in_titulo="Validación recálculo de cuentas"
    />
  </div>
</template>

<script>
import RecalculoCuenta from "./SEM003.vue";
import CalculoForm from "./SEM004.vue";


import VxCard from "../../../../components/vx-card/VxCard.vue";
import { DefaultDxGridConfiguration } from "../../EXPEDIENTE/data.js";
import CustomStore from "devextreme/data/custom_store";
import esMessages from "devextreme/localization/messages/es.json";
import { CustomDevExtremeEsMessages } from "../../EXPEDIENTE/data.js";

export default {
  name: "PrincipalForm",
  components: {
    VxCard,
    RecalculoCuenta,
    CalculoForm,

  },
  data() {
    return {
      DefaultDxGridConfiguration,
      buttonObtenerDescuento: {
        text: "Obtener Descuento",
        type: "success",
        onClick: () => this.Descuentos("I"),
        icon: "add",
        //   disabled: this.obtenerDescuento
      },
      buttonElminardescuento: {
        width: "150",
        text: "Eliminar Descuento",
        type: "default",
        icon: "minus",

        onClick: () => this.Descuentos("E"),
        stylingMode: "contained",
        elementAttr: {
          style: "white-space: pre-wrap; text-align: center;",
        },
      },
      buttonCalcularDescuento: {
        text: "Calcular",
        type: "default",
        icon: "minus",
        onClick: this.handleButtonClick,
      },

      buttonGrabarCambios: {
        text: "Grabar",
        type: "default",
        icon: "floppy",

        onClick: this.GrabarCambios,
        hint: "Grabar cambios",
        width: "200",
      },
      buttonGrabarCalcular: {
        text: "Calcular",
        type: "default",
        icon: "money",
        onClick: this.CalculoCobro,
        hint: "Calcular cobro a paciente",
        width: "200",
      },
      buttonGrabarActualizar: {
        text: "Actualizar",
        type: "default",
        icon: "refresh",
        onClick: this.ListadoAdmisiones,
        width: "200",

        hint: "Actualizar información",
      },

      buttonGrabarReCalcular: {
        text: "Re-Calcular",
        type: "default",
        icon: "revert",
        onClick: () => {
          this.ValidarPermisos(); //this.popupRecalculo = true;
        },
        hint: "Abrir ventana para recalcular cuenta",
      },
      Areas: {
        dataSource: [],
        displayExpr: "Area",
        valueExpr: "Id",
        searchEnabled: true,
      },
      notificationOptions: {
        time: 4000,
        title: "Semaforo egresos",
        iconPack: "feather",
        icon: "icon-alert-circle",
        position: "top-center",
      },

      formData: {
        deducible: 0.0,
        copago: 0.0,
        gastosEspeciales: 0,
        Facturado: 0.0,
        coaseguro: 0.0,
        facturado: 0.0,
        extraordinarios: 0.0,
        ctaAjeno: 0.0,
        totalCobrar: 0.0,
        Hospital: "",
        Servicio: "",
        Paciente: "",
        Seguro: "",
        Admision: "",
        SerieAdmision: "",
        noAdmision: 0,
        tipoDescuento: "",
        descuento: 0.0,
        totalDescuento: 0,
        noAutorizacion: 0,
        observaciones: "",
        tipoAdmision: "",
        nombreDescuento: "",
      }, //dat
      Cobro: [
        {
          deducible: "",
          copago: "",
          gastosEspeciales: "",
          coaseguro: "",
          facturado: "",
          extraordinarios: "",
          ctaAjeno: "",
          totalCobrar: "",
          cargosHospital: "",
          descuento: "",
          montoPago: "",
          CtaAjenaNoElegible: "",
        },
      ],

      positionEditorOptions: {
        items: this.dsDescuentos,
        searchEnabled: true,
        value: "",
      },
      observacionesAdmision: "Notas para la admisión",
      ListaCuentasx: new CustomStore({
        key: "ID",
        load: () => {
          return this.ListaCuentas;
        },
      }),
      dsCobros: [
        {
          deducible: "",
          copago: "",
          gastosEspeciales: "",
          coaseguro: "",
          facturado: "",
          extraordinarios: "",
          ctaAjeno: "",
          totalCobrar: "",
          cargosHospital: "",
          descuento: "",
          montoPago: "",
          ultimaActualizacion: "",
        },
      ],
      dsDescuentos: [],
      selectedDescuento: null,
      dsAreas: [],
      dsListadoAdmisiones: [],
      obtenerDescuento: true,
      eliminarDescuento: true,
      popupCalculoCobro: false,
      popupRecalculo: false,
      popupCalculo: false,
      AdmisionesObservacion: [],
      PermisoRecalculo: false,
      ListaPermisos: "X",
       currentTime: '',
     

      currencyOptions: {
        format: "Q ###,##0.##",

        //   {
        //   type: "custom",
        //   formatter: function (value) {
        //     // Formatear el valor como moneda y reemplazar 'GTQ' por 'Q'
        //     let formattedValue = new Intl.NumberFormat('en-GT', {
        //       style: 'currency',
        //       currency: 'GTQ',
        //       currencyDisplay: 'symbol'
        //     }).format(value);

        //     // Reemplazar 'GTQ' por 'Q'
        //     return formattedValue.replace('GTQ', 'Q');
        //     //return  this.$formato_moneda(value)
        //   },
        // },
        showClearButton: true,
        onValueChanged: function (e) {
          if (e.value === null || e.value < 0) {
            e.component.option("value", 0);
          }
        },
        min: 0,
      },
      PorcentajeOptions: {
        format: "#0%",
        showClearButton: true,
        onValueChanged: this.handleValueChanged,
      },
      currentDate: new Date().toLocaleDateString(),

    
    }; //return
  },

  methods: {
    OctultadoPopup() {
      this.$refs["dataGrid"].instance.deselectAll();
      const CalculoForm = this.$refs.refCalculoForm;
      //si el registro se proceso eliminarlo de la lista
      if (CalculoForm.CobroProcesado === true) {
        this.eliminarRegistroGrid();
        CalculoForm.CobroProcesado = false;
      }
    },

    eliminarRegistroGrid() {
      const index = this.dsListadoAdmisiones.findIndex(
        (item) =>
          item.Admision === this.formData.noAdmision &&
          item.serie === this.formData.SerieAdmision
      );

      if (index !== -1) {
        this.dsListadoAdmisiones.splice(index, 1);
        this.key++; // Para forzar la actualización del DataGrid
        this.popupCalculoCobro = false;
        this.AdmisionesObservacion = [];
      }
    },
    EventoPopupShown() {
      const Recalculo = this.$refs.refRecalculo;
      Recalculo.$refs["dataGrid"].instance.deselectAll();
    },

    DisplayExpression(e) {
      return this.$saltos_tab_to_html(e.value);
    },
    handleButtonClick() {
      alert("Button clicked!");
    },
    calcular() {
      // Lógica para calcular
    },
    guardarCambios() {
      // Lógica para guardar cambios
    },
    actualizar() {
      // Lógica para actualizar
    },

    ValidarPermisos() {
      if (this.PermisoRecalculo === true) {
        this.popupRecalculo = true;
      } else {
        this.$vs.notify({
          time: 4000,
          title: "!No tiene permisos para recálculo de cuentas!",
          text: "Solicitar permiso para recálculo cuentas, Semaforo egresos ",
          iconPack: "feather",
          icon: "icon-alert-circle",
          color: "warning",
          position: "top-center",
        });

        return;
      }
    },
    observacionCellTemplate(cellElement, cellInfo) {
      const formattedValue = cellInfo.value
        .replace(/\t/g, "   ") // Reemplaza tabuladores por tres espacios
        .replace(/\n/g, "<br>"); // Reemplaza saltos de línea por <br>
      cellElement.innerHTML = `<span class="observacion-text">${formattedValue}</span>`;
    },
    onSelectionChanged(selectedItems) {
      const CalculoForm = this.$refs.refCalculoForm;
      CalculoForm.resetCalculoCobro();
      CalculoForm.resetFormData();
      if (selectedItems.selectedRowsData.length > 0) {
        const Seleccion = selectedItems.selectedRowsData[0];
        CalculoForm.formData.Paciente = Seleccion.Paciente;
        //this.formData.Paciente = Seleccion.Paciente;
        CalculoForm.formData.Seguro = Seleccion.NombreSeguro;
        CalculoForm.formData.Admision =
          Seleccion.serie + "-" + Seleccion.Admision;
        this.formData.noAdmision = Seleccion.Admision;
        this.formData.SerieAdmision = Seleccion.serie;
        CalculoForm.formData.noAdmision = Seleccion.Admision;
        CalculoForm.formData.SerieAdmision = Seleccion.serie;
        CalculoForm.formData.tipoAdmision = Seleccion.TipoAdmision;
     
        this.ValidarCuenta(Seleccion.serie, Seleccion.Admision);
      }
    },

    ListadoAdmisiones() {
      this.dsListadoAdmisiones = [];
      this.resetFormData();
      this.resetCalculoCobro();
      if (this.formData.Servicio === "") {
        this.$vs.notify({
          time: 4000,
          title: "Falta información",
          text: "Favor ingrese el servicio a consultar",
          iconPack: "feather",
          icon: "icon-alert-circle",
          color: "warning",
          position: "top-center",
        });
        return;
      }
      this.axios
        .post(
          "/app/v1_SemaforoEgresos/ListaAdmisiones",
          {
            Hospital: this.$store.state.sesion.sesion_sucursal_nombre,
            Catalogo: this.formData.Servicio,
          },
          {
            headers: {
              "Content-Type": "application/json; charset=UTF-8",
            },
          }
        )
        .then((resp) => {
          this.dsListadoAdmisiones = resp.data;
          this.$refs["dataGrid"].instance.deselectAll();
          this.AdmisionesObservacion = [];
          this.updateTime();
        });
    },

    ValidarCuenta(SerieAdmision, Admision) {
      this.axios
        .post("/app/v1_SemaforoEgresos/ValidarCuenta", {
          SerieAdmision: SerieAdmision,
          Admision: Admision,
        })
        .then((resp) => {
          const CalculoForm = this.$refs.refCalculoForm;

          this.formData.nombreDescuento = "";
          if (resp.data) {
            CalculoForm.formData.totalDescuento =
              resp.data[0].BeneficioTerceros;

            if (resp.data[0].idTipoDescuento === -1) {
              CalculoForm.eliminarDescuento = true;
              CalculoForm.obtenerDescuento = false;
            } else {
              CalculoForm.formData.nombreDescuento = resp.data[0].TipoDescuento;
              CalculoForm.eliminarDescuento = false;
              CalculoForm.obtenerDescuento = true;
            }
          }

          if (resp.data[0].ctaCalculada === true) {
            (CalculoForm.Cobro = [
              {
                deducible: "",
                copago: "",
                gastosEspeciales: "",
                coaseguro: 0.0,
                facturado: "",
                extraordinarios: "",
                ctaAjeno: "",
                totalCobrar: "",
                cargosHospital: "",
                descuento: "",
                montoPago: "",
                CtaAjenaNoElegible: "",
              },
            ]),
              this.$vs.notify({
                time: 4000,
                title:
                  "Admisión " +
                  SerieAdmision +
                  "-" +
                  Admision +
                  " Ya fue calculada",
                text: "La cuenta seleccionada ya fue calculada",
                iconPack: "feather",
                icon: "icon-alert-circle",
                color: "warning",
                position: "top-center",
              });
            this.dsListadoAdmisiones = this.dsListadoAdmisiones.filter(
              (record) =>
                record.serie !== SerieAdmision || record.Admision !== Admision
            );
          } else {
            const CalculoForm = this.$refs.refCalculoForm;
            this.popupCalculo = true;
            CalculoForm.CalcularCuenta(SerieAdmision, Admision);
            CalculoForm.MostrarobservacionesAdmision();
          }
        });
    },

    CalcularCuenta(SerieAdmision, Admision) {
      this.axios
        .post("/app/v1_SemaforoEgresos/ConsultaCuenta", {
          SerieAdmision: SerieAdmision,
          Admision: Admision,
        })
        .then((resp) => {
          if (resp.data.json && resp.data.json.length > 0) {
            this.Cobro = resp.data.json.map((x, index) => {
              return {
                id: index,
                cargosHospital: x.CargosHospital,
                copago: x.Copago,
                coaseguro: x.Coaseguro,
                Hora: x.Hora,
                Copago: x.Copago,
                montoPago: x.MontoPago,
                descuento: x.descuento,
                porcentajedescuento: x.porcentajedescuento,
                extraordinarios: x.ExtraOrdinarios,
              };
            });
          }

          this.updateTime();
        });
    },
    Descuentos(X) {
      if (X === "I") {
        if (this.formData.descuento <= 0) {
          this.$vs.notify({
            time: 4000,
            title: "Notificación descuentos",
            text: "El descuento seleccionado es '0%' no se puede continuar",
            iconPack: "feather",
            icon: "icon-alert-circle",
            color: "warning",
            position: "top-center",
          });
          return; // Salir de la función si el descuento es 0 o menor
        }
      }

      if (X === "E") {
        this.formData.tipoDescuento = 0;
      }
      this.axios
        .post("/app/v1_SemaforoEgresos/Descuentos", {
          admision: this.formData.noAdmision,
          serieAdmision: this.formData.SerieAdmision,
          copago: this.formData.copago,
          coaseguro: this.formData.coaseguro,
          deducible: this.formData.deducible,
          gastosnocubiertos: this.formData.gastosEspeciales,
          autorizacion: this.formData.noAutorizacion,
          idDescuento: this.formData.tipoDescuento,
          opcion: X,
        })
        .then((response) => {
          const data = response.data[0];
          const notificationOptions = {
            time: 4000,
            title: "Notificación descuentos",
            iconPack: "feather",
            icon: "icon-alert-circle",
            position: "top-center",
          };

          if (data.codigo === 0) {
            this.formData.descuento = null;
            this.$vs.notify({
              ...notificationOptions,
              text: `${data.descripcion} por un monto de: Q${data.descuento}`,
              color: "success",
            });
            //eliminar descuento
            if (X === "E") {
              this.obtenerDescuento = false;
              this.eliminarDescuento = true;
              this.formData.totalDescuento = 0;
            }

            // otener descuento
            if (X === "I") {
              this.obtenerDescuento = true;
              this.eliminarDescuento = false;
              this.formData.totalDescuento = data.descuento;
            }
          } else {
            this.$vs.notify({
              ...notificationOptions,
              text: data.descripcion,
              color: "warning",
            });
          }

          this.updateTime();
        });
    },

    porcentajedescuento() {
      if (
        (this.formData.tipoAdmision !== "") &
        (this.formData.tipoDescuento != "")
      ) {
        this.axios
          .post("/app/v1_SemaforoEgresos/PorcentajeDescuento", {
            tipoAdmision: this.formData.tipoAdmision,
            idDescuento: this.formData.tipoDescuento,
          })
          .then((resp) => {
            this.formData.descuento = resp.data[0].PorcentajeDescuento;
          });
      }
    },

    formatCurrency(value) {
      if (!value) return "0";
      value = value.toString();
      return "Q" + value.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    updateTime() {
      const now = new Date();
      const hours = String(now.getHours()).padStart(2, "0");
      const minutes = String(now.getMinutes()).padStart(2, "0");
      const seconds = String(now.getSeconds()).padStart(2, "0");
      this.currentTime = `${hours}:${minutes}:${seconds}`;
    },
    resetFormData() {
      (this.formData.deducible = 0),
        (this.formData.copago = 0),
        (this.formData.gastosEspeciales = 0),
        (this.formData.Facturado = 0),
        (this.formData.coaseguro = 0.0),
        (this.formData.facturado = 0),
        (this.formData.extraordinarios = 0),
        (this.formData.ctaAjeno = 0),
        (this.formData.totalCobrar = 0),
        (this.formData.Paciente = ""),
        (this.formData.Seguro = ""),
        (this.formData.Admision = ""),
        (this.formData.SerieAdmision = ""),
        (this.formData.noAdmision = 0),
        (this.formData.tipoDescuento = ""),
        (this.formData.descuento = 0),
        (this.formData.totalDescuento = 0),
        (this.formData.noAutorizacion = 0),
        (this.formData.observaciones = ""),
        (this.formData.tipoAdmision = "");
      this.formData.nombreDescuento = "";
    },
    resetCalculoCobro() {
      this.Cobro = [
        {
          deducible: "",
          copago: "",
          gastosEspeciales: "",
          coaseguro: "0.0",
          facturado: "",
          extraordinarios: "",
          ctaAjeno: "",
          totalCobrar: "",
          cargosHospital: "",
          descuento: "",
          montoPago: "",
          CtaAjenaNoElegible: "",
        },
      ];
    },

    handleValueChanged(e) {
      if (e.value === null) {
        this.formData.tipoDescuento = "";
      }
    },
    CalculoCobro() {
      const notificationOptions = {
        time: 4000,
        title: "Notificación calculo admisión",
        iconPack: "feather",
        icon: "icon-alert-circle",
        position: "top-center",
      };

      if (this.formData.SerieAdmision === "") {
        this.$vs.notify({
          ...notificationOptions,
          text: "No ha seleccionado una admisión",
          color: "warning",
        });
        return;
      }
      const sumatoria = (
        this.formData.coaseguro +
        this.formData.copago +
        this.formData.deducible +
        this.formData.gastosEspeciales +
        this.formData.extraordinarios +
        this.formData.ctaAjeno -
        (this.formData.Facturado + this.formData.totalDescuento)
      ).toFixed(2);

      this.formData.totalCobrar = parseFloat(sumatoria);
      if (this.formData.totalCobrar > this.Cobro[0].cargosHospital) {
        this.$vs.notify({
          ...notificationOptions,
          text: "El calculo realizado, supera el valor total de cargos",
          color: "warning",
        });
        return;
      }
    },

    GrabarCambios() {
      const notificationOptions = {
        time: 4000,
        title: "Notificación calculo admisión",
        iconPack: "feather",
        icon: "icon-alert-circle",
        position: "top-center",
      };

      if (this.formData.SerieAdmision === "") {
        this.$vs.notify({
          ...notificationOptions,
          text: "No ha seleccionado una admisión",
          color: "warning",
        });
        return;
      }
      this.CalculoCobro();

      this.popupCalculoCobro = true;
    },
    isPositive(value) {
      return parseFloat(value) > 0;
    },

    InsertarCalculoCobro() {
      const notificationOptions = {
        time: 4000,
        title: "Notificación Calculo admisión",
        iconPack: "feather",
        icon: "icon-alert-circle",
        position: "top-center",
      };
      if (this.formData.SerieAdmision === "") {
        this.$vs.notify({
          ...notificationOptions,
          text: "No ha seleccionado una admisión",
          color: "warning",
        });
        return;
      }
      this.axios
        .post("/app/v1_SemaforoEgresos/InsertarCalculo", {
          Copago: this.formData.copago,
          Coaseguro: this.formData.coaseguro,
          Deducible: this.formData.deducible,
          Gastosnocubiertos: this.formData.gastosEspeciales,
          ExtraOrdinarios: this.formData.extraordinarios,
          CtaAjena: this.formData.ctaAjeno,
          Facturado: this.formData.facturado,
          Descuento: this.formData.totalDescuento,
          Total: this.formData.totalCobrar,
          Comentario: this.formData.observaciones,
          SerieAdmision: this.formData.SerieAdmision,
          Admision: this.formData.noAdmision,
          NombreDescuento: this.formData.nombreDescuento,
        })
        .then((response) => {
          const data = response.data[0];

          if (data.codigo === 0) {
            this.$vs.notify({
              ...notificationOptions,
              text: `${data.descripcion}`,
              color: "success",
            });

            const index = this.dsListadoAdmisiones.findIndex(
              (item) =>
                item.Admision === this.formData.noAdmision &&
                item.serie === this.formData.SerieAdmision
            );

            if (index !== -1) {
              this.dsListadoAdmisiones.splice(index, 1);
              this.key++; // Para forzar la actualización del DataGrid
              this.popupCalculoCobro = false;
              this.AdmisionesObservacion = [];
            }
          } else {
            this.$vs.notify({
              ...notificationOptions,
              text: data.descripcion,
              color: "warning",
            });
          }
        });
    },

    formatObservacion(observacion) {
      return observacion.replace(/\r\n/g, "<br/>");
    },

  },
  watch: {
    NumeroAdmision(newVal) {
      if (newVal != undefined) this.calcular();
    },
    "formData.tipoDescuento"(newVal) {
      if (newVal != undefined) this.porcentajedescuento();
    },
  },
  computed: {
    datosParaCobroCaption() {
      const { Paciente, Seguro, Admision } = this.formData;
      if (Paciente || Seguro || Admision) {
        return `Datos para cobro:\nPaciente: ${Paciente}\nSeguro: ${Seguro}\nAdmision: ${Admision}`;
      }
      return "Datos para cobro";
    },
    captionHtml() {
      const { Paciente, Seguro, Admision } = this.formData;
      if (Paciente || Seguro || Admision) {
        return `Datos para cobro:<br>Paciente: ${Paciente}<br>Seguro: ${Seguro}<br>Admision: <span style="color: blue;">${Admision}</span>`;
      }
      return "Datos para cobro";
    },
  },
  created() {
    loadMessages(esMessages);
    loadMessages({
      es: CustomDevExtremeEsMessages,
    });
    locale(navigator.language);
  },
  mounted() {
    this.PermisoRecalculo = this.$validar_privilegio("RECALCULOCUENTAS").status;

    this.axios
      .post("/app/v1_SemaforoEgresos/CatalogoSemaforo", {
        Catalogo: "Areas",
      })
      .then((resp) => {
        this.dsAreas = resp.data;
      });
  },
};
</script>
<style>
.sem002-container .dx-field-item-content {
  padding: 0px;
  margin-bottom: 3px;
  border: 2px;
}

.sem002-container .dx-widget {
  line-height: 1.1;
}
[dir] .dx-form-group-with-caption > .dx-form-group-content {
  padding-top: 3px;
  margin-top: 6px;
  border-top: 1px solid #ddd;
  padding-bottom: 7px;
}
.sem002-container .dx-datagrid-rowsview
  .dx-selection.dx-row:not(.dx-row-focused):not(.dx-row-removed)
  > td {
  background-color: #00ccff !important;
  color: black !important;
}

.sem002-container .dx-datagrid-rowsview .dx-row.dx-data-row.dx-column-lines.dx-state-hover > td {
  background-color: #79e4ff !important;
  color: black !important;
  font-size: 16px;
}

.sem002-container .dx-datagrid-headers {
  background-color: #008fbe !important;
  color: rgb(252, 255, 254) !important;
  font-weight: bold;
}

.sem002-container .dx-layout-manager .dx-field-item:not(.dx-first-row) {
  padding-top: 2px;
}

.sem002-container .dx-layout-manager .dx-field-item:not(.dx-first-col) {
  padding-left: 5px;
  padding-right: 5px;
}

[dir="ltr"] .sem002-container .dx-layout-manager .dx-field-item:not(.dx-last-col) {
  padding-right: 5px;
}
/*
table {
  width: 100%;
  border-collapse: collapse;
}

th,
td {
  border: 1px solid black;
  padding: 2px;
  text-align: left;
}

th {
  background-color: #f2f2f2;
}
*/
.sem002-container .responsive-table {
  width: 100%;
  border-collapse: collapse;
  padding: 2px;
}

.sem002-container .responsive-table th,
.sem002-container .responsive-table td {
  padding: 2px;
  text-align: left;
  border: 1px solid #ddd;
}

/* Añadir altura mínima a las filas */
.sem002-container .dx-datagrid-rowsview .dx-row {
  min-height: 30px; /* Ajusta este valor según sea necesario */
}

/* Evitar desbordamiento de contenido */
.sem002-container .dx-datagrid-rowsview .dx-row td {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Estilos generales para la tabla */
.sem002-container .responsive-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: auto; /* Permite que las filas se ajusten automáticamente al contenido */
}

.sem002-container .responsive-table td {
  border: 1px solid #ddd;
  padding: 4px;
  text-align: right;
  vertical-align: top; /* Asegura que el contenido se alinee en la parte superior */
}

.sem002-container .responsive-table th {
  background-color: #f2f2f2;
  color: #008fbe;
  text-align: center;
}

/* Estilos responsivos */
@media screen and (max-width: 600px) {
  .sem002-container .responsive-table thead {
    display: none;
  }

  .sem002-container .responsive-table tr {
    display: block;
    margin-bottom: 10px;
  }

  .sem002-container .responsive-table td {
    display: block;
    text-align: right;
    padding-left: 50%;
    position: relative;
    white-space: normal; /* Evitar que el texto se desborde */
    min-width: 200px; /* Ancho mínimo para celdas en dispositivos móviles */
    line-height: 1.5;
  }

  .sem002-container .responsive-table td::before {
    content: attr(data-label);
    position: absolute;
    left: 0;
    width: 50%;
    padding-left: 5px;
    font-weight: bold;
    text-align: left;
    white-space: nowrap;
  }
  .sem002-container .responsive-table td:last-child {
    margin-bottom: 10px; /* Añadir espacio al final de cada fila */
  }
}
.rojo {
  color: red;
}
.titulo-th {
  background-color: #008fbe !important;
  color: rgb(252, 255, 254) !important;
  font-weight: bold;
  font-size: 20px; /* Puedes ajustar el tamaño según tus necesidades */
}

/* .fm-super-popup .dx-popup-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-width: auto;
  text-align: center;
} */
.fm-super-popup .dx-scrollable .dx-scrollview .dx-visibility-change-handler .dx-scrollable-vertical .dx-scrollable-simulated
{
  width: 100%;
}
.sem002-container .centered-text {
  font-weight: bold;
}
.fm-super-popup .dx-popup-title {
  background-color: #008fbe !important;
  color: #f3f4f7 !important;
}

.sem002-container td {
  vertical-align: middle !important;
}
.sem002-container .observacion-text {
  font-family: "Consolas", sans-serif;
  font-size: 16px;
}
</style>