<template>
    <div class="container">

        <div class="DatosPaciente sticky" v-show="formPosition > 0">
            <DatosPaciente ref="DatosPacienteComponent">
            </DatosPaciente>
        </div>

        <div class="pasosProceso">
            <section :class="animation">
                <div>
                    <div v-for="(tab, index) in patientProcess" :key="'component' + index">
                        <component v-if="patientProcess[index].render" v-show="patientProcess[index].visible"
                            v-bind:is="tab.component" ref="comp" @ready="ready" :renderComp="render">
                        </component>
                    </div>
                </div>

                <vs-button v-if="(formPosition < patientProcess.length && formPosition > 0)" @click.native="prevStep">
                    <i class="fas fa-chevron-left"></i>
                    Regresar
                </vs-button>


                <vs-button id="botonRecibo" v-show="formPosition > 0" v-if="(formPosition <= patientProcess.length - 1)"
                    @click.native="nextStep" class="ml-4">
                    Guardar
                    <i class="fas fa-chevron-right"></i>
                </vs-button>

            </section>
        </div>
    </div>
</template>


<script>



import ConsultaPacientes from '@/components/sermesa/modules/paciente/ConsultaPacientesPolizas.vue'
import PacienteNuevo from '@/components/sermesa/modules/paciente/DatosGeneralesPaciente.vue'
import AdmisionesInternas from "/src/views/pages/AJENOS/AJE022.vue"
import DatosPaciente from "/src/components/sermesa/modules/paciente/DatosPaciente.vue"


import { ref } from 'vue'


const childConsultaPacientes = ref()
const childPaciente = ref()
const cildAdmisionesExternas = ref()


export default {
    data() {
        return {
            formPosition: 0,
            animation: 'animate-in',
            datosPaciente: [],
            render: '',

            patientProcess: [
                { step: 1, showButtons: true, visible: true, title: 'ConsultaPacientes', component: ConsultaPacientes, comp: childConsultaPacientes, render: true },
                { step: 2, showButtons: true, visible: false, title: 'CreacionPaciente', component: PacienteNuevo, comp: childPaciente, render: false },
                { step: 3, showButtons: true, visible: false, title: 'AdmisionesInternas', component: AdmisionesInternas, comp: cildAdmisionesExternas, render: false },
            ]
        }
    },
    mounted() {
    },
    beforeCreate() {

    },
    deactivated() {
        this.$destroy()
        this.$el.parentNode.removeChild(this.$el)
    },
    computed: {

        getComponentConf(pos) {
            return this.patientProcess[pos].step
        },
    },
    created() {

    },
    components: {
        DatosPaciente

    },
    methods: {
        ready(value) {

            if (!value)
                return

            this.patientProcess[this.formPosition].visible = false
            this.animation = 'animate-in';
            this.formPosition += 1;

            if (this.formPosition === this.patientProcess.length) {
                this.formPosition = 0
                this.patientProcess[this.formPosition].visible = true
                this.$store.dispatch('admision/initAdmision')
                this.$store.dispatch('paciente/initPaciente')
                
                this.$router.push('/')
                return
            }

            this.patientProcess[this.formPosition].render = true
            this.patientProcess[this.formPosition].visible = true
        },
        nextStep() {
            const botonRecibo = document.getElementById("botonRecibo");
            if(botonRecibo) botonRecibo.blur()
            this.animation = 'animate-out';
            this.$refs.comp[this.formPosition].submit()
            

        },
        prevStep() {

            this.animation = 'animate-out';


            this.patientProcess[this.formPosition].visible = false
            this.animation = 'animate-in';
            this.formPosition -= 1;
            this.patientProcess[this.formPosition].visible = true
        }
    }
}


</script>



<style scoped>

* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}


.container {
    display: grid;
    grid-gap: 5px;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    right: 0;
    left: 80px;
    min-width: 400px;
    grid-template-areas: "DatosPaciente" "pasosProceso";
    grid-template-columns: 1fr;
    grid-template-rows: auto auto;
}

.container>div {
    border: 1px none #888;
}


.DatosPaciente{
    grid-area: DatosPaciente;
    overflow-y: hidden;
    font-size: small;
    background: rgb(208, 225, 249);
    color: #2f496e;
}

.sticky {
    position: -webkit-sticky !important;
    position: sticky !important;
    top: 66px;
    background-color: rgb(229, 248, 254);
    z-index: 9999 !important;
}

.pasosProceso {
    grid-area: pasosProceso;
}

.container {
    max-width: 100%;
}



.animation-in {
    transform-origin: left;
    animation: in .6s ease-in-out;
}

.animation-out {
    transform-origin: bottom left;
    animation: out .6s ease-in-out;
}
</style>